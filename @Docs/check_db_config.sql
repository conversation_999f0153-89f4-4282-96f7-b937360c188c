-- ============================================
-- 数据库连接配置检查脚本
-- 用于评估迁移任务对数据库的影响
-- ============================================

-- 1. 检查最大连接数配置
SHOW VARIABLES LIKE 'max_connections';

-- 2. 当前连接统计
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Threads_running';
SHOW STATUS LIKE 'Max_used_connections';

-- 3. 计算连接使用率
SELECT 
    VARIABLE_VALUE as current_connections,
    @@max_connections as max_connections,
    ROUND((VARIABLE_VALUE / @@max_connections) * 100, 2) as usage_percent,
    @@max_connections - VARIABLE_VALUE as available_connections,
    CASE 
        WHEN (VARIABLE_VALUE / @@max_connections) > 0.8 THEN '🔴 危险：建议增加max_connections或减少现有连接'
        WHEN (VARIABLE_VALUE / @@max_connections) > 0.6 THEN '🟡 警告：连接数偏高，需谨慎启动迁移'
        ELSE '✅ 正常：有足够空间启动迁移'
    END as status
FROM information_schema.global_status 
WHERE VARIABLE_NAME = 'Threads_connected';

-- 4. 查看各应用的连接数分布
SELECT 
    SUBSTRING_INDEX(host, ':', 1) as client_host,
    db as database_name,
    user,
    COUNT(*) as connection_count,
    SUM(IF(command != 'Sleep', 1, 0)) as active_connections,
    SUM(IF(command = 'Sleep', 1, 0)) as idle_connections
FROM information_schema.processlist
GROUP BY client_host, database_name, user
ORDER BY connection_count DESC;

-- 5. 检查是否有长时间的空闲连接（可以清理）
SELECT 
    id,
    user,
    host,
    db,
    command,
    time as idle_seconds,
    state
FROM information_schema.processlist
WHERE command = 'Sleep' 
  AND time > 300  -- 空闲超过5分钟
ORDER BY time DESC
LIMIT 20;

-- 6. 检查慢查询日志设置
SHOW VARIABLES LIKE 'slow_query_log%';
SHOW VARIABLES LIKE 'long_query_time';

-- 7. 当前正在执行的查询
SELECT 
    id,
    user,
    host,
    db,
    time,
    state,
    LEFT(info, 100) as query_preview
FROM information_schema.processlist
WHERE command != 'Sleep'
  AND time > 1  -- 执行超过1秒
ORDER BY time DESC
LIMIT 10;


