CREATE TABLE `admin_cycle_operation_detail` (
             `id` varchar(50) NOT NULL COMMENT '主键ID',
             `cycle_operation_id` varchar(50) DEFAULT NULL COMMENT '周期操作记录ID',
             `emp_id` varchar(255) DEFAULT NULL COMMENT '被考核人ID',
             `emp_name` varchar(255) DEFAULT NULL COMMENT '被考核人姓名',
             `emp_avatar` varchar(255) DEFAULT NULL COMMENT '被考核人头像',
             `org_name` varchar(255) DEFAULT NULL COMMENT '被考核人所属部门',
             `before` longtext COMMENT '修改前值',
             `after` longtext COMMENT '修改后值',
             `task_name` varchar(255) DEFAULT NULL COMMENT '考核任务名称',
             `was_updated` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否变更【0-否，1-是】',
             `type` int(11) DEFAULT NULL COMMENT '操作记录类型【1-应用分布】',
             `company_id` varchar(50) DEFAULT NULL COMMENT '公司ID',
             `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
             `created_time` datetime DEFAULT NULL COMMENT '创建时间',
             `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
             `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
             `version` int(11) NOT NULL DEFAULT '0' COMMENT '版本号',
             `is_deleted` varchar(10) DEFAULT NULL COMMENT '是否删除',
             PRIMARY KEY (`id`),
             KEY `idx_companyId` (`company_id`) USING BTREE,
             KEY `idx_cycleOperationId` (`cycle_operation_id`) USING BTREE
) COMMENT='周期等级规则修改操作记录详情';

INSERT INTO `sequence`(`name`, `current_value`, `increment` )
VALUES ('admin_cycle_operation_detail', 10000, 100);