ALTER TABLE score_range MODIFY COLUMN perf_coefficient text COMMENT '等级对应绩效系数/绩效系数公式';
ALTER TABLE perf_evaluate_task_score_result MODIFY COLUMN perf_coefficient VARCHAR(10) COMMENT '绩效系数';
ALTER TABLE perf_evaluate_task_user MODIFY COLUMN perf_coefficient VARCHAR(10) COMMENT '绩效系数';
ALTER TABLE perf_evaluate_task_user MODIFY COLUMN original_perf_coefficient VARCHAR(10) COMMENT '原始绩效系数';


alter table score_rule add column coeff_type int(1) comment '系数类型：1=固定系数值，2=自定义公式计算系数';
alter table score_rule add column place int(1) comment '系数保留小数位';
alter table score_rule add column perf_coefficient text comment '等级对应绩效系数/绩效系数公式';
alter table score_rule add column field_json text comment '公式字符串,页面回填使用';


alter table score_range add column coeff_type int(1) comment '系数类型：1=固定系数值，2=自定义公式计算系数';
alter table score_range add column place int(1) comment '系数保留小数位';
alter table score_range add column field_json text comment '公式字符串,页面回填使用';
