/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = admin_cycle_operation   */
/******************************************/

 drop table if exists `admin_cycle_operation`;
 CREATE TABLE `admin_cycle_operation`  (
  `id` varchar(50) NOT NULL COMMENT '操作记录id',
  `cycle_id` bigint(20) NOT NULL COMMENT '周期id',
  `company_id` varchar(50) NOT NULL COMMENT '公司id',
  `admin_type` varchar(20) NOT NULL COMMENT '管理员角色 主管理员=main 子管理员=child',
  `change_type` int(11) NOT NULL COMMENT '操作类型',
  `base_info` text COMMENT '基本信息修改',
  `audit_result` text COMMENT '结果校准',
  `confirm_result` text COMMENT '结果确认',
  `publish_result` text COMMENT '结果公示',
  `appeal_conf` text COMMENT '结果申诉',
  `score_conf` text COMMENT '评分设置',
  `score_view_conf` text COMMENT '评分人查看权限',
  `admin_diff` text COMMENT '管理员权限变更',
  `operation_time` bigint(20) NOT NULL COMMENT '操作时间毫秒ms',
  `operator_id` varchar(50) NOT NULL COMMENT '操作人emp_id',
  `operator_name` varchar(50) NOT NULL COMMENT '操作人名',
  `operator_avatar` varchar(200) NOT NULL COMMENT '操作人头像',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_cycle_id` (`cycle_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='周期操作日志'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = admin_manage_org   */
/******************************************/
 drop table if exists `admin_manage_org`;
 CREATE TABLE `admin_manage_org`  (
  `company_id` varchar(50) NOT NULL COMMENT '企业id',
  `org_id` varchar(50) DEFAULT '' COMMENT '部门id',
  `admin_emp_id` varchar(50) DEFAULT '' COMMENT '管理员id',
  `created_user` varchar(50) DEFAULT '',
  `updated_user` varchar(50) DEFAULT '',
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  UNIQUE KEY `uk_admin_org` (`admin_emp_id`,`org_id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_org_id` (`org_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员管理的部门表'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = admin_of_cycle   */
/******************************************/
 drop table if exists `admin_of_cycle`;
 CREATE TABLE `admin_of_cycle`  (
  `cycle_id` bigint(20) NOT NULL COMMENT '周期id',
  `company_id` varchar(50) NOT NULL COMMENT '公司id',
  `emp_id` varchar(50) NOT NULL COMMENT '员工id',
  `name` varchar(50) NOT NULL COMMENT '员工名称',
  `main` int(11) DEFAULT '1' COMMENT '是否是周期主管',
  `priv` int(11) DEFAULT '0' COMMENT '周期权限 无=0，管理=1，查看=2',
  `is_deleted` varchar(5) DEFAULT 'false' COMMENT '是否删除',
  `created_user` varchar(50) DEFAULT NULL,
  `updated_user` varchar(50) DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  KEY `k_cycle_id` (`cycle_id`),
  KEY `k_company_id` (`company_id`),
  KEY `k_emp_id` (`emp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='周期权限配置'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = admin_of_task   */
/******************************************/
 drop table if exists `admin_of_task`;
 CREATE TABLE `admin_of_task`  (
  `company_id` varchar(50) NOT NULL COMMENT '企业id',
  `task_id` varchar(50) DEFAULT NULL COMMENT '管理的任务id',
  `admin_emp_id` varchar(50) NOT NULL COMMENT '管理员工id',
  `main` int(11) DEFAULT '1' COMMENT '是否是任务主管',
  `priv` int(11) DEFAULT '1' COMMENT '管理员的权限,二进位表示00=无,01=读,10=写,11=读写',
  `created_user` varchar(50) DEFAULT '',
  `created_time` datetime DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  UNIQUE KEY `uk_admin_task` (`admin_emp_id`,`task_id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_admin_emp_id` (`admin_emp_id`),
  KEY `idx_task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理任务包含的管理员'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = admin_task_operation   */
/******************************************/
 drop table if exists `admin_task_operation`;
 CREATE TABLE `admin_task_operation`  (
  `id` varchar(50) NOT NULL COMMENT '操作记录id',
  `task_id` varchar(50) NOT NULL COMMENT '外键task_base.id, 管理任务id',
  `company_id` varchar(50) NOT NULL COMMENT '公司id',
  `admin_type` varchar(20) NOT NULL COMMENT '管理员角色 main child',
  `change_type` int(11) NOT NULL COMMENT '操作类型:修改基本信息=01=1,修改考核规则=10=2,修改考核任务管理权限=100=4,增加考核人员=1000=8,删除考核人员=10000=16,终止考核任务=100000=32',
  `base_info` text COMMENT '基本信息修改',
  `priv_info` text COMMENT '管理员的权限变更内容',
  `confirm_task` text COMMENT '确认任务 对应模板 templAffirmJson:PerfTemplEvaluateAffirm',
  `edit_exe_indi` text COMMENT '执行中修改指标日志记录  对应模板 templExecuteJson:PerfTemplEvaluateExecute',
  `score_conf` text COMMENT 'enter_score,s3_self_rater,s3_mutual_rater,s3_super_rater ,score_veiw',
  `score_sort_conf` text COMMENT '2.0.0新版:新加字段 评分环节顺序配置',
  `comment_conf` text COMMENT '评语与总结配置',
  `score_view` varchar(512) DEFAULT NULL COMMENT '可见范围配置 json',
  `audit_result` text COMMENT '结果校准,从templEvaluateJson:PerfTemplEvaluate 模板评价配置json 单独出来的',
  `confirm_result` text COMMENT '结果确认,多个字段合并',
  `publish_result` text COMMENT '结果公示,多个字段合并',
  `appeal_conf` varchar(512) DEFAULT NULL COMMENT '结果申诉,多个字段合并',
  `add_emps` text COMMENT '增加的员工',
  `remove_emps` text COMMENT '删除的员工',
  `operation_time` bigint(20) NOT NULL COMMENT '操作时间毫秒ms',
  `operator_id` varchar(50) NOT NULL COMMENT '操作人emp_id',
  `operator_name` varchar(225) NOT NULL COMMENT '操作人名',
  `operator_avatar` varchar(200) NOT NULL COMMENT '操作人头像',
  `created_time` datetime DEFAULT NULL COMMENT '记录创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_operation_time` (`operation_time`),
  KEY `idx_task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理任务的操作日志'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = announcement   */
/******************************************/
 drop table if exists `announcement`;
 CREATE TABLE `announcement`  (
  `id` varchar(50) NOT NULL,
  `content` text,
  `created_time` varchar(50) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = app_version   */
/******************************************/
 drop table if exists `app_version`;
 CREATE TABLE `app_version`  (
  `ver` bigint(20) NOT NULL COMMENT '1.9.1= 1*1000*1000 + 9*1000 + 1=1009001，用于计算的版本号，也是主键',
  `label` varchar(174) DEFAULT NULL COMMENT '1.9.1 用于显示版本号',
  `desc` varchar(1000) DEFAULT NULL COMMENT '版本描述',
  `method` varchar(1000) DEFAULT NULL COMMENT '升级当前版本需要执行的方法',
  `pre_version` bigint(20) DEFAULT NULL COMMENT '上一个版本,仅做参考，现在没有用',
  `open` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否开放使用',
  PRIMARY KEY (`ver`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用的版本信息'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = app_version_script   */
/******************************************/
 drop table if exists `app_version_script`;
 CREATE TABLE `app_version_script`  (
  `id` bigint(20) NOT NULL COMMENT 'id',
  `app_version` bigint(20) NOT NULL COMMENT '数字型版本号',
  `order` int(11) DEFAULT '0' COMMENT '排序字段，非必填',
  `script` text COMMENT '执行脚本',
  `desc` varchar(1000) DEFAULT NULL COMMENT '脚本描述',
  `script_type` int(11) DEFAULT '0' COMMENT '默认0=升级脚本；1=版本全量迁移脚本',
  PRIMARY KEY (`id`),
  KEY `idx_app_version` (`app_version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用升级对应的版本'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = company   */
/******************************************/
 drop table if exists `company`;
 CREATE TABLE `company`  (
  `id` varchar(40) NOT NULL DEFAULT '0' COMMENT '自增id',
  `code` varchar(10) DEFAULT NULL COMMENT '公司code',
  `name` varchar(512) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '公司名称',
  `abbr_name` varchar(20) DEFAULT NULL COMMENT '公司简称',
  `business` varchar(127) DEFAULT NULL COMMENT '公司业务方向',
  `legal_person` varchar(45) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '公司负责人',
  `legal_person_mobile` varchar(200) DEFAULT NULL COMMENT '公司联系人电话',
  `legal_person_email` varchar(45) DEFAULT NULL COMMENT '公司联系人邮箱',
  `business_license` varchar(256) DEFAULT NULL COMMENT '工商执照',
  `credit_code` varchar(50) DEFAULT NULL COMMENT '社会统一信用代码',
  `logo` varchar(256) DEFAULT NULL COMMENT '公司LOGO',
  `address` varchar(512) DEFAULT NULL COMMENT '公司地址',
  `scale` varchar(32) DEFAULT NULL COMMENT '企业规模',
  `city` varchar(255) DEFAULT NULL COMMENT '公司所在城市',
  `license` varchar(50) DEFAULT NULL COMMENT '开户许可证',
  `license_img` varchar(255) DEFAULT NULL COMMENT '开户许可证',
  `head_name` varchar(50) DEFAULT NULL COMMENT '法人名称',
  `head_id_num` varchar(50) DEFAULT NULL COMMENT '法人证件号码',
  `head_id_type` varchar(50) DEFAULT NULL COMMENT '法人证件类型',
  `head_img1` varchar(255) DEFAULT NULL COMMENT '法人证件正面',
  `head_img2` varchar(255) DEFAULT NULL COMMENT '法人证件反面',
  `agent_img1` varchar(255) DEFAULT NULL COMMENT '经办人身份证正面',
  `agent_img2` varchar(255) DEFAULT NULL COMMENT '经办人身份证反面',
  `approval_status` varchar(10) DEFAULT NULL COMMENT '审核状态（review:审核中，pass:通过，unpass:不通过）',
  `approval_memo` varchar(200) DEFAULT NULL COMMENT '审核备注',
  `app_perm` text COMMENT 'app权限json格式',
  `open_push` varchar(10) DEFAULT NULL COMMENT '开启推送（true|false）',
  `sync_type` varchar(10) DEFAULT NULL COMMENT '同步类型（auto:自动,manu:手动）',
  `ding_corp_id` varchar(200) DEFAULT NULL COMMENT '钉钉企业id',
  `ding_agent_id` varchar(200) DEFAULT NULL COMMENT '钉钉授权方应用id',
  `ding_space_id` varchar(20) DEFAULT NULL COMMENT '企业钉盘id',
  `we_corp_id` varchar(200) DEFAULT NULL COMMENT 'welink企业id',
  `status` varchar(10) DEFAULT 'using' COMMENT '状态using：正常使用中，close：删除',
  `company_type` varchar(10) DEFAULT NULL COMMENT '公司类型（review:待审核-默认不需要审核,simple-极简版，enterprise-企业版）',
  `emp_limit` int(11) DEFAULT NULL COMMENT '人数上限',
  `perm_start_time` date DEFAULT NULL COMMENT '权限开始日期',
  `perm_end_time` date DEFAULT NULL COMMENT '权限结束日期',
  `target_num` int(11) DEFAULT NULL COMMENT '目标数量',
  `action_num` int(11) DEFAULT NULL COMMENT '成果数量',
  `created_time` datetime DEFAULT NULL COMMENT '记录创建时间',
  `updated_time` datetime DEFAULT NULL COMMENT '记录更新时间',
  `indicator_num` int(11) DEFAULT NULL COMMENT '指标数量',
  `enter_on_scoring` int(11) DEFAULT '0' COMMENT '评分阶段可以录入完成值 0=关, 1=打开',
  `result_input_send_msg` varchar(10) DEFAULT 'true' COMMENT '是否发送录入完成值代办',
  `sup_is_can_auto_score_item` int(11) DEFAULT '0' COMMENT '上级是否能看自动评分指标明细',
  `can_res_submit_input_finish` int(11) DEFAULT '0' COMMENT '自动评分指标能多次录入完成值',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `order_from` int(11) DEFAULT '0' COMMENT '订单来源 0=默认, 1 = hrm,2 =mark_place, 3=',
  `eval_emp_on_log_auth` int(1) DEFAULT '1' COMMENT '被考核人打开日志权限',
  `enter_on_ding_msg` int(1) DEFAULT '1' COMMENT '钉钉代办开关',
  `log_auth_set` varchar(100) DEFAULT 'taskEmp,participant,createdUser' COMMENT '日志权限设置，taskEmp=被考核人,participant=参与人,createdUser=创建人',
  `ignore_vacancy_manager` int(11) DEFAULT '0' COMMENT '忽略空缺主管',
  `emp_entry_auto_open` int(1) DEFAULT '0' COMMENT '员工自动开启开关 默认0=关闭，1=开启',
  `app_version` varchar(50) DEFAULT '2000000',
  `last_version` varchar(50) DEFAULT '2000000',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_c_ding_corp_id` (`ding_corp_id`) USING BTREE,
  KEY `idx_update_time` (`updated_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='公司'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = company_auto_free   */
/******************************************/
 drop table if exists `company_auto_free`;
 CREATE TABLE `company_auto_free`  (
  `id` varchar(40) NOT NULL DEFAULT '0' COMMENT 'id',
  `code` varchar(10) DEFAULT NULL COMMENT '公司code',
  `name` varchar(512) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '公司名称',
  `perm_start_time` date DEFAULT NULL COMMENT '权限开始日期',
  `perm_end_time` date DEFAULT NULL COMMENT '权限结束日期',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='试用自动转免费的公司信息备份表'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = company_cache_info   */
/******************************************/
 drop table if exists `company_cache_info`;
 CREATE TABLE `company_cache_info`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `company_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `link_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '业务关联id',
  `business_scene` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '业务场景',
  `cache_key` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '缓存的key',
  `value` longtext COLLATE utf8mb4_bin COMMENT '缓存的value',
  `is_deleted` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = company_category   */
/******************************************/
 drop table if exists `company_category`;
 CREATE TABLE `company_category`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `company_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `category_name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '类别名称',
  `type` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '所属模块',
  `is_deleted` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = company_dic   */
/******************************************/
 drop table if exists `company_dic`;
 CREATE TABLE `company_dic`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `sys_dic_type_id` varchar(50) DEFAULT NULL COMMENT '字典类型id',
  `sys_dic_type` varchar(50) DEFAULT NULL COMMENT '字典类型',
  `dic_value` varchar(200) DEFAULT NULL COMMENT '字典值',
  `is_temporary` varchar(10) DEFAULT NULL COMMENT '是否临时数据',
  `is_default` varchar(10) DEFAULT NULL COMMENT '是否默认值',
  `is_deleted` varchar(10) DEFAULT NULL COMMENT '是否删除',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='公司字典'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = company_formula_field   */
/******************************************/
 drop table if exists `company_formula_field`;
 CREATE TABLE `company_formula_field`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `company_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `formula_field_name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公式里的字段名称',
  `module` varchar(30) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '字段所属模块',
  `is_system_field` varchar(10) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '是否系统设置里的字段',
  `is_temporary` varchar(10) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '是否临时字段',
  `is_deleted` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = company_init_industry   */
/******************************************/
 drop table if exists `company_init_industry`;
 CREATE TABLE `company_init_industry`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `company_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司id',
  `sys_item_industry` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '已导入的系统指标行业',
  `created_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = company_item_decompose   */
/******************************************/
 drop table if exists `company_item_decompose`;
 CREATE TABLE `company_item_decompose`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL,
  `kpi_item_id` varchar(50) DEFAULT NULL COMMENT '指标id',
  `year` int(11) DEFAULT NULL COMMENT '年份',
  `emp_id` varchar(50) DEFAULT NULL COMMENT '员工id',
  `month1` decimal(18,2) DEFAULT NULL COMMENT '一月分解值',
  `month2` decimal(18,2) DEFAULT NULL COMMENT '2月分解值',
  `month3` decimal(18,2) DEFAULT NULL COMMENT '3月分解值',
  `month4` decimal(18,2) DEFAULT NULL COMMENT '4月分解值',
  `month5` decimal(18,2) DEFAULT NULL COMMENT '5月分解值',
  `month6` decimal(18,2) DEFAULT NULL COMMENT '6月分解值',
  `month7` decimal(18,2) DEFAULT NULL COMMENT '7月分解值',
  `month8` decimal(18,2) DEFAULT NULL COMMENT '8月分解值',
  `month9` decimal(18,2) DEFAULT NULL COMMENT '9月分解值',
  `month10` decimal(18,2) DEFAULT NULL COMMENT '10月分解值',
  `month11` decimal(18,2) DEFAULT NULL COMMENT '11月分解值',
  `month12` decimal(18,2) DEFAULT NULL COMMENT '12月分解值',
  `total_value` decimal(18,2) DEFAULT NULL COMMENT '合计',
  `status` varchar(50) DEFAULT NULL COMMENT '状态',
  `is_deleted` varchar(10) DEFAULT NULL,
  `created_user` varchar(50) DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `company_id` (`company_id`) USING BTREE,
  KEY `item_emp` (`company_id`,`is_deleted`,`kpi_item_id`,`emp_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = company_item_rel_org   */
/******************************************/
 drop table if exists `company_item_rel_org`;
 CREATE TABLE `company_item_rel_org`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `company_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `company_item_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司指标id',
  `org_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '指标关联的部门id',
  `is_deleted` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = company_item_rel_tag   */
/******************************************/
 drop table if exists `company_item_rel_tag`;
 CREATE TABLE `company_item_rel_tag`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `company_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `company_item_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司指标id',
  `tag_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '指标关联的部门id',
  `is_deleted` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`),
  KEY `idx_cirt_itemid` (`company_item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = company_kpi_item   */
/******************************************/
 drop table if exists `company_kpi_item`;
 CREATE TABLE `company_kpi_item`  (
  `id` varchar(50) NOT NULL COMMENT 'id',
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `sys_item_id` varchar(50) DEFAULT NULL COMMENT '系统指标库id',
  `item_path` text COMMENT '指标id链，“|”隔开',
  `item_name` text COMMENT '指标项名称',
  `item_type` varchar(50) DEFAULT NULL COMMENT '指标项类型（量化/非量化）',
  `item_value` decimal(18,2) DEFAULT NULL COMMENT '指标项目标值',
  `item_plan_value` decimal(18,2) DEFAULT NULL COMMENT '指标计划值',
  `item_unit` varchar(50) DEFAULT NULL COMMENT '目标值单位',
  `parent_kpi_item_id` varchar(50) DEFAULT NULL COMMENT '上级指标项',
  `item_rule` text COMMENT '考核规则',
  `scoring_rule` text COMMENT '计分规则',
  `result_input_type` varchar(50) DEFAULT NULL COMMENT '指标结果录入类型（被考核人/指定员工/无需录入）',
  `result_input_user_id` text COMMENT '指定结果录入人id',
  `scorer_type` varchar(50) DEFAULT NULL COMMENT '指标评分人类型（按评分流程/指定员工/指定主管）',
  `scorer_user_id` text COMMENT '指定评分人id',
  `multiple_reviewers_type` varchar(30) DEFAULT NULL COMMENT '审核为多人时是或签还是会签',
  `item_weight` decimal(10,2) DEFAULT NULL COMMENT '指标项权重',
  `category_id` varchar(100) DEFAULT NULL COMMENT '指标级别id',
  `is_board` varchar(10) DEFAULT NULL COMMENT '是否加入指标看板',
  `is_deleted` varchar(10) DEFAULT NULL COMMENT '是否删除',
  `is_temporary` varchar(10) DEFAULT NULL COMMENT '是否临时指标',
  `threshold_json` text COMMENT '量化指标阈值设置json',
  `show_target_value` varchar(10) DEFAULT NULL COMMENT '是否展示目标值',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `version` int(11) DEFAULT '0',
  `item_custom_field_json` text COMMENT '指标自定义字段json',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`),
  KEY `idx_cki_pid` (`parent_kpi_item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='公司指标库'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = company_kpi_item_custom_field   */
/******************************************/
 drop table if exists `company_kpi_item_custom_field`;
 CREATE TABLE `company_kpi_item_custom_field`  (
  `id` varchar(50) NOT NULL COMMENT 'id',
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `field_name` varchar(50) DEFAULT NULL COMMENT '字段名称',
  `is_req` int(11) DEFAULT '1' COMMENT '是否必填（0：否  1：是）',
  `field_status` varchar(50) DEFAULT NULL COMMENT '指标字段状态（valid：有效，invalid：无效）',
  `field_order` int(11) DEFAULT NULL COMMENT '指标字段排序值',
  `is_deleted` varchar(10) DEFAULT NULL COMMENT '是否删除',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `version` int(11) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='公司自定义指标字段'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = company_msg_center   */
/******************************************/
 drop table if exists `company_msg_center`;
 CREATE TABLE `company_msg_center`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL,
  `business_scene` varchar(50) DEFAULT NULL COMMENT '业务场景',
  `emp_id` varchar(50) DEFAULT NULL COMMENT '当前处理人id',
  `link_id` varchar(50) DEFAULT NULL COMMENT '关联业务id',
  `att_content` text COMMENT '附加内容',
  `url` varchar(500) DEFAULT NULL COMMENT '跳转url',
  `third_msg_id` varchar(100) DEFAULT NULL COMMENT '第三方消息id',
  `handler_status` varchar(50) DEFAULT NULL COMMENT '处理状态',
  `is_urgent` varchar(10) DEFAULT NULL COMMENT '是否紧急：true/fase',
  `params` text COMMENT '其他参数',
  `third_msg_flag` varchar(10) DEFAULT NULL COMMENT '是否需要给第三方发待办',
  `created_user` varchar(50) DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `is_ignore` varchar(10) DEFAULT NULL COMMENT '是否忽略提醒，true/false',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`),
  KEY `k_link_id` (`link_id`),
  KEY `k_emp_id` (`emp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='消息中心'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = company_msg_rel_score_result   */
/******************************************/
 drop table if exists `company_msg_rel_score_result`;
 CREATE TABLE `company_msg_rel_score_result`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `company_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司id',
  `score_result_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '考核结果id',
  `emp_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '当前责任人id',
  `task_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '考核任务id',
  `temp_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '模板id',
  `msg_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '待办消息id',
  `scorer_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '原责任人id',
  `handle_status` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '处理结果：true/false',
  `to_emp_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '转交接收人id',
  `task_emp_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '被考核员工id',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `k_task_id` (`task_id`),
  KEY `k_task_emp_id` (`task_emp_id`),
  KEY `k_score_result_id` (`score_result_id`),
  KEY `k_company_id` (`company_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = company_notice   */
/******************************************/
 drop table if exists `company_notice`;
 CREATE TABLE `company_notice`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `company_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `business_scene` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '业务场景',
  `msg_type` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '消息类型',
  `emp_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '接收人id',
  `link_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '关联业务id',
  `att_content` text COLLATE utf8mb4_bin COMMENT '附加内容',
  `url` varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '跳转url',
  `third_msg_id` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '第三方消息id',
  `params` text COLLATE utf8mb4_bin COMMENT '附加参数',
  `created_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `show_flag` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否需要展示在通知我的页面',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`),
  KEY `created_user` (`created_user`),
  KEY `link_id` (`link_id`),
  KEY `emp_id` (`emp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = company_perf_level_header   */
/******************************************/
 drop table if exists `company_perf_level_header`;
 CREATE TABLE `company_perf_level_header`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL,
  `level_seq` int(11) DEFAULT NULL COMMENT '等级序号',
  `level_name` varchar(100) DEFAULT NULL COMMENT '等级名称',
  `is_deleted` varchar(10) DEFAULT NULL,
  `created_user` varchar(50) DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `step_id` varchar(50) DEFAULT NULL COMMENT '等级分值步长id',
  PRIMARY KEY (`id`),
  KEY `idx_companyid` (`company_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='绩效等级表头配置'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = company_perf_level_quota   */
/******************************************/
 drop table if exists `company_perf_level_quota`;
 CREATE TABLE `company_perf_level_quota`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `org_id` varchar(50) DEFAULT NULL COMMENT '部门id',
  `level_name` varchar(200) DEFAULT NULL COMMENT '等级名称',
  `level_quota` bigint(20) DEFAULT NULL COMMENT '等级配额',
  `is_deleted` varchar(10) DEFAULT NULL,
  `created_user` varchar(50) DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_company_id` (`company_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='绩效等级配额'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = company_score_group   */
/******************************************/
 drop table if exists `company_score_group`;
 CREATE TABLE `company_score_group`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `group_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '分组名称',
  `company_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司id',
  `is_deleted` varchar(10) COLLATE utf8mb4_bin DEFAULT 'false',
  `created_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = company_score_group_copy   */
/******************************************/
 drop table if exists `company_score_group_copy`;
 CREATE TABLE `company_score_group_copy`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `group_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '分组名称',
  `company_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司id',
  `is_deleted` varchar(10) COLLATE utf8mb4_bin DEFAULT 'false',
  `created_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = company_score_group_member   */
/******************************************/
 drop table if exists `company_score_group_member`;
 CREATE TABLE `company_score_group_member`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `company_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `group_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '考核评分组id',
  `weight` decimal(10,2) DEFAULT NULL COMMENT '权重，百分比',
  `emp_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '评分成员id',
  `is_deleted` varchar(10) COLLATE utf8mb4_bin DEFAULT 'false',
  `created_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = company_sys_setting   */
/******************************************/
 drop table if exists `company_sys_setting`;
 CREATE TABLE `company_sys_setting`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `setting_type` varchar(100) DEFAULT NULL COMMENT '设置类型',
  `value` varchar(50) DEFAULT NULL COMMENT '设置值',
  `is_deleted` varchar(10) DEFAULT NULL COMMENT '是否删除',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='公司系统设置'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = company_tag   */
/******************************************/
 drop table if exists `company_tag`;
 CREATE TABLE `company_tag`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `company_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `tag_name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '标签名称',
  `type` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '所属模块',
  `is_deleted` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = cycle_eval_rule   */
/******************************************/
 drop table if exists `cycle_eval_rule`;
 CREATE TABLE `cycle_eval_rule`  (
  `cycle_id` bigint(20) NOT NULL COMMENT '周期id',
  `company_id` varchar(50) NOT NULL COMMENT '公司id',
  `audit_result` text COMMENT '结果校准流程配置',
  `confirm_result` text COMMENT '结果确认流程配置',
  `publish_result` text COMMENT '结果公示流程配置',
  `appeal_conf` text COMMENT '结果申诉流程配置',
  `score_conf` text COMMENT '评分设置',
  `score_view` text COMMENT '评分人查看权限',
  `created_user` varchar(50) DEFAULT NULL,
  `updated_user` varchar(50) DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  KEY `k_cycle_id` (`cycle_id`),
  KEY `k_company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='周期规则配置'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = del_result   */
/******************************************/
 drop table if exists `del_result`;
 CREATE TABLE `del_result`  (
  `company_id` varchar(200) DEFAULT NULL,
  `id` varchar(200) DEFAULT NULL,
  `task_id` varchar(500) DEFAULT NULL,
  `emp_id` varchar(500) DEFAULT NULL,
  `kpi_type_id` varchar(500) DEFAULT NULL,
  `kpi_item_id` varchar(500) DEFAULT NULL,
  `scorer_type` varchar(500) DEFAULT NULL,
  `scorer_id` varchar(500) DEFAULT NULL,
  `score` int(1) DEFAULT NULL,
  `del` int(1) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = dept_leader_syn   */
/******************************************/
 drop table if exists `dept_leader_syn`;
 CREATE TABLE `dept_leader_syn`  (
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司ID',
  `org_id` varchar(50) NOT NULL COMMENT '部门ID',
  `ding_user_id` varchar(50) DEFAULT NULL COMMENT '钉钉用户id',
  `push_id` bigint(20) NOT NULL COMMENT 'open_sync_biz_data.id 钉钉事件id',
  UNIQUE KEY `uk_orgId_userId` (`company_id`,`ding_user_id`,`org_id`,`push_id`),
  KEY `idx_userId` (`ding_user_id`),
  KEY `idx_pushId` (`push_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='员工同步临时表,部门主管'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = dept_ref_rule   */
/******************************************/
 drop table if exists `dept_ref_rule`;
 CREATE TABLE `dept_ref_rule`  (
  `company_id` varchar(60) NOT NULL COMMENT '公司id',
  `rule_id` varchar(50) NOT NULL COMMENT '分值id',
  `org_id` varchar(60) NOT NULL,
  `is_deleted` varchar(10) DEFAULT 'false',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `created_time` varchar(50) DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '更新人',
  `updated_time` varchar(50) DEFAULT NULL COMMENT '更新时间',
  `version` int(11) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = employee_base_info   */
/******************************************/
 drop table if exists `employee_base_info`;
 CREATE TABLE `employee_base_info`  (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `employee_id` varchar(50) NOT NULL COMMENT '主键员工ID',
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司ID',
  `org_id` varchar(50) DEFAULT NULL COMMENT '部门ID',
  `account_id` varchar(50) DEFAULT NULL COMMENT '账号ID',
  `name` varchar(128) DEFAULT NULL COMMENT '姓名',
  `nickname` varchar(128) CHARACTER SET utf8 DEFAULT NULL COMMENT '花名',
  `sex` varchar(10) CHARACTER SET utf8 DEFAULT NULL COMMENT '性别',
  `mobile` varchar(200) CHARACTER SET utf8 DEFAULT NULL,
  `id_type` varchar(50) DEFAULT NULL COMMENT '证件类型',
  `id_num` varchar(50) DEFAULT NULL COMMENT '证件号码',
  `type` varchar(50) DEFAULT NULL COMMENT '员工类型（正式-regular；试用-probation；实习-practice；兼职-parttimer）',
  `status` varchar(50) DEFAULT NULL COMMENT '员工状态（在职-on_the_job；待离职-pending_leave；已离职-leave）',
  `entry_date` date DEFAULT NULL COMMENT '入职日期',
  `avatar` varchar(256) CHARACTER SET utf8 DEFAULT NULL COMMENT '头像链接地址',
  `birthday` date DEFAULT NULL COMMENT '公历出生日期',
  `create_id` varchar(50) DEFAULT NULL COMMENT '创建人ID',
  `created_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `name_chinese` varchar(512) DEFAULT NULL COMMENT '查询关键字',
  `hierarchy` varchar(128) CHARACTER SET utf8 DEFAULT NULL COMMENT '职级',
  `nation` varchar(50) CHARACTER SET utf8 DEFAULT NULL COMMENT '民族',
  `native_place` varchar(128) CHARACTER SET utf8 DEFAULT NULL COMMENT '籍贯',
  `political_party` varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '政治面貌',
  `house_reg_type` varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '户籍性质',
  `house_reg_addr` varchar(300) CHARACTER SET utf8 DEFAULT NULL COMMENT '户籍地址',
  `living_addr` varchar(300) CHARACTER SET utf8 DEFAULT NULL COMMENT '现住地址',
  `office_addr` varchar(300) CHARACTER SET utf8 DEFAULT NULL COMMENT '办公地址',
  `highest_edu` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '最高学历',
  `marital_status` varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '婚姻状态',
  `child_status` varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '子女状态',
  `linkman_name` varchar(100) CHARACTER SET utf8 DEFAULT NULL COMMENT '联系人姓名',
  `linkman_type` varchar(50) CHARACTER SET utf8 DEFAULT NULL COMMENT '联系人关系',
  `linkman_tel` varchar(100) DEFAULT NULL COMMENT '联系人电话',
  `is_delete` varchar(10) DEFAULT '' COMMENT '删除标识；否，是',
  `source` varchar(30) DEFAULT NULL COMMENT '多语言字段',
  `photo` varchar(255) DEFAULT NULL COMMENT '照片',
  `memo` varchar(255) DEFAULT NULL COMMENT '备注',
  `qy_user_id` varchar(50) DEFAULT NULL COMMENT '企业微信用户id',
  `ding_user_id` varchar(50) DEFAULT NULL COMMENT '钉钉用户id',
  `we_user_id` varchar(50) DEFAULT NULL COMMENT 'welink用户id',
  `open_id` varchar(255) DEFAULT NULL COMMENT '第三方（钉钉，企微等）平台的open id',
  `union_id` varchar(255) DEFAULT NULL COMMENT '第三方（钉钉，企微等）平台的union id',
  `state_code` varchar(10) DEFAULT NULL COMMENT '手机号对应的国家号',
  `mobile_num` varchar(20) DEFAULT NULL COMMENT '用户的手机号,需要在开发者后台申请',
  `email` varchar(20) DEFAULT NULL COMMENT '用户的个人邮箱',
  `locale` varchar(30) DEFAULT NULL COMMENT '多语言的语言代码，用中划线',
  `ding_role_name` varchar(50) DEFAULT NULL COMMENT '钉钉角色名称',
  `ding_manager_id` varchar(255) DEFAULT NULL COMMENT '钉钉直属主管id',
  `version` int(10) DEFAULT NULL COMMENT '新版 1=旧版，2=新版',
  `last_login_time` datetime DEFAULT NULL,
  `jobnumber` varchar(50) DEFAULT '' COMMENT '工号',
  `post` varchar(300) DEFAULT '' COMMENT '岗位',
  PRIMARY KEY (`employee_id`) USING BTREE,
  KEY `k_id` (`id`),
  KEY `k_company_id` (`company_id`) USING BTREE,
  KEY `k_ding_id` (`ding_user_id`) USING BTREE,
  KEY `k_account_id` (`account_id`),
  KEY `k_name` (`name`),
  KEY `index_status_key` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='员工'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = employee_syn   */
/******************************************/
 drop table if exists `employee_syn`;
 CREATE TABLE `employee_syn`  (
  `id` varchar(50) DEFAULT NULL,
  `employee_id` varchar(50) DEFAULT NULL COMMENT '主键员工ID',
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司ID',
  `org_id` varchar(50) NOT NULL COMMENT '部门ID',
  `account_id` varchar(50) DEFAULT NULL COMMENT '账号ID',
  `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '姓名',
  `nickname` varchar(128) CHARACTER SET utf8 DEFAULT NULL COMMENT '花名',
  `sex` varchar(10) CHARACTER SET utf8 DEFAULT NULL COMMENT '性别',
  `mobile` varchar(200) CHARACTER SET utf8 DEFAULT NULL,
  `type` varchar(20) DEFAULT NULL COMMENT '员工类型（正式-regular；试用-probation；实习-practice；兼职-parttimer）',
  `status` varchar(50) DEFAULT NULL COMMENT '员工状态（在职-on_the_job；待离职-pending_leave；已离职-leave）',
  `entry_date` date DEFAULT NULL COMMENT '入职日期',
  `avatar` varchar(256) CHARACTER SET utf8 DEFAULT NULL COMMENT '头像链接地址',
  `birthday` date DEFAULT NULL COMMENT '公历出生日期',
  `created_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `name_chinese` varchar(512) DEFAULT NULL COMMENT '查询关键字',
  `is_delete` varchar(10) DEFAULT 'false' COMMENT '删除标识；否，是',
  `ding_user_id` varchar(50) DEFAULT NULL COMMENT '钉钉用户id',
  `we_user_id` varchar(50) DEFAULT NULL COMMENT 'welink用户id',
  `open_id` varchar(255) DEFAULT NULL COMMENT '第三方（钉钉，企微等）平台的open id',
  `union_id` varchar(255) DEFAULT NULL COMMENT '第三方（钉钉，企微等）平台的union id',
  `state_code` varchar(10) DEFAULT NULL COMMENT '手机号对应的国家号',
  `mobile_num` varchar(20) DEFAULT NULL COMMENT '用户的手机号,需要在开发者后台申请',
  `email` varchar(50) DEFAULT NULL COMMENT '用户的个人邮箱',
  `ding_role_name` varchar(50) DEFAULT NULL COMMENT '钉钉角色名称',
  `ding_manager_id` varchar(255) DEFAULT NULL COMMENT '钉钉直属主管id',
  `jobnumber` varchar(50) DEFAULT NULL COMMENT '工号',
  `post` varchar(256) DEFAULT NULL COMMENT '岗位',
  `version` bigint(20) DEFAULT NULL,
  `last_login_time` datetime DEFAULT NULL,
  `push_id` bigint(20) NOT NULL COMMENT 'open_sync_biz_data.id 钉钉事件id',
  UNIQUE KEY `uk_orgId_userId` (`company_id`,`ding_user_id`,`org_id`,`push_id`),
  KEY `idx_userId` (`ding_user_id`),
  KEY `idx_pushId` (`push_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='员工同步临时表,部门+员工'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = emp_apply_use   */
/******************************************/
 drop table if exists `emp_apply_use`;
 CREATE TABLE `emp_apply_use`  (
  `id` varchar(50) NOT NULL COMMENT 'id',
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `emp_id` varchar(50) DEFAULT NULL COMMENT '员工id',
  `status` varchar(20) DEFAULT NULL COMMENT '状态（review:审核中,pass：同意，unpass：不同意）',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `created_user` varchar(50) DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '操作人',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`),
  KEY `k_company_status` (`company_id`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='员工申请使用表'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = emp_eval_kpi_type   */
/******************************************/
 drop table if exists `emp_eval_kpi_type`;
 CREATE TABLE `emp_eval_kpi_type`  (
  `task_user_id` varchar(50) NOT NULL COMMENT 'task user 表的id',
  `company_id` varchar(50) NOT NULL COMMENT '公司id',
  `kpi_type_id` varchar(50) DEFAULT NULL COMMENT '指标类id',
  `kpi_type_name` varchar(200) DEFAULT NULL COMMENT '指标类名称',
  `kpi_type_weight` decimal(10,2) DEFAULT NULL COMMENT '指标类权重',
  `open_okr_score` int(11) DEFAULT '0' COMMENT '指标评分使用的是okr的分数',
  `is_okr` varchar(10) DEFAULT NULL COMMENT '是否OKR类别，true/false',
  `type_order` int(10) DEFAULT NULL COMMENT '类别排序',
  `reserve_okr_weight` decimal(10,2) DEFAULT NULL COMMENT '预留OKR权重',
  `kpi_type_classify` varchar(30) DEFAULT NULL COMMENT '指标类别，为空表示已有类别，custom自定义/plus加分项/subtract减分项',
  `max_extra_score` decimal(10,2) DEFAULT NULL COMMENT '本类别最大加减分上限',
  `import_okr_flag` varchar(10) DEFAULT NULL COMMENT '是否已导入OKR',
  `locked_items` varchar(100) DEFAULT NULL COMMENT 'is_type_locked 类别锁定类型  ["typeWeight","addIndex","modifyIndex","deleteIndex"]',
  `item_limit_cnt` varchar(128) DEFAULT NULL COMMENT '类别指标数量最大最小限制',
  `is_deleted` varchar(10) DEFAULT 'false' COMMENT '是否删除',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `plus_sub_interval` varchar(50) DEFAULT NULL COMMENT '考核规则类别加减分上限',
  KEY `idx_companyId_taskUserId_typeId` (`company_id`,`task_user_id`,`kpi_type_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='考核任务-指标类别'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = emp_eval_operation   */
/******************************************/
 drop table if exists `emp_eval_operation`;
 CREATE TABLE `emp_eval_operation`  (
  `id` varchar(50) NOT NULL COMMENT '操作记录id',
  `emp_eval_id` varchar(50) NOT NULL COMMENT '外键task_user.id, 员工任务id',
  `company_id` varchar(50) NOT NULL COMMENT '公司id',
  `at_status` varchar(50) NOT NULL COMMENT '修改时所在阶段',
  `base_info` text COMMENT '基本信息',
  `change_type` int(11) NOT NULL COMMENT '01=1=指标, 10=2=流程,11=3=指标+流程',
  `indicator` mediumtext COMMENT '指标类修改,type_weight_conf,score_value_conf, _kpi表,_kpi_type表',
  `confirm_task` text COMMENT '确认任务 对应模板 templAffirmJson:PerfTemplEvaluateAffirm',
  `edit_exe_indi` text COMMENT '执行中修改指标日志记录  对应模板 templExecuteJson:PerfTemplEvaluateExecute',
  `score_conf` text COMMENT 'enter_score,s3_self_rater,s3_mutual_rater,s3_super_rater ,score_veiw',
  `audit_result` text COMMENT '结果校准,从templEvaluateJson:PerfTemplEvaluate 模板评价配置json 单独出来的',
  `confirm_result` varchar(512) DEFAULT NULL COMMENT '结果确认,多个字段合并',
  `publish_result` text COMMENT '结果公示,多个字段合并',
  `appeal_conf` varchar(512) DEFAULT NULL COMMENT '结果申诉,多个字段合并',
  `operation_time` bigint(20) NOT NULL COMMENT '操作时间毫秒ms',
  `operator_id` varchar(50) NOT NULL COMMENT '操作人emp_id',
  `operator_name` varchar(50) NOT NULL COMMENT '操作人名',
  `operator_avatar` varchar(225) NOT NULL COMMENT '操作人头像',
  `created_time` datetime DEFAULT NULL COMMENT '记录创建时间',
  `admin_type` varchar(25) DEFAULT NULL COMMENT '操作人管理员类型/admin/child',
  PRIMARY KEY (`id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_operation_time` (`operation_time`),
  KEY `idx_emp_eval_id` (`emp_eval_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='员工任务的操作日志'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = emp_eval_rule   */
/******************************************/
 drop table if exists `emp_eval_rule`;
 CREATE TABLE `emp_eval_rule`  (
  `emp_eval_id` varchar(50) NOT NULL COMMENT '外键task_user.id, 新加',
  `company_id` varchar(50) NOT NULL COMMENT '公司id',
  `rule_name` varchar(250) DEFAULT '' COMMENT '考核规则名字,可以去掉此字段,名字是动态生成的,不需要搜索',
  `evaluate_type` varchar(10) NOT NULL COMMENT '评分流程类型 ： simple-简易流程，360-360°考核流程，custom-自定义流程',
  `type_weight_conf` varchar(128) DEFAULT '' COMMENT '指标类别权重 json',
  `score_value_conf` varchar(128) DEFAULT '' COMMENT '分值的设定 json',
  `confirm_task` text COMMENT '确认任务 对应模板 templAffirmJson:PerfTemplEvaluateAffirm',
  `edit_exe_indi` text COMMENT '执行中修改指标  对应模板 templExecuteJson:PerfTemplEvaluateExecute',
  `enter_score` varchar(128) DEFAULT '' COMMENT '启动评分配置 ,多字段合并',
  `s3_self_rater` varchar(128) DEFAULT '' COMMENT '360或简易 自评人',
  `s3_peer_rater` text COMMENT '同级互评人 json',
  `s3_sub_rater` text COMMENT '同级互评人 json',
  `s3_super_rater` text COMMENT '360或简易 上级人 json',
  `score_view` text COMMENT '可见范围配置 json',
  `audit_result` text COMMENT '结果校准,从templEvaluateJson:PerfTemplEvaluate 模板评价配置json 单独出来的',
  `confirm_result` text COMMENT '结果确认,多个字段合并',
  `publish_result` text COMMENT '结果公示,多个字段合并',
  `appeal_conf` text COMMENT '结果申诉,多个字段合并',
  `level_group_id` varchar(50) DEFAULT '' COMMENT '关联哪个等级组/perf_evaluation_level 的level_group_id',
  `index_raters` text COMMENT '评分人索引,用于员工列表的显示,编辑时更新索引',
  `indicator_cnt` int(11) DEFAULT '0' COMMENT '指标数统计',
  `score_conf` varchar(225) DEFAULT NULL COMMENT '评分设置',
  `comment_conf` varchar(225) DEFAULT NULL COMMENT '评语与总结配置',
  `is_deleted` varchar(5) DEFAULT 'false' COMMENT '是否删除',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `edit_status` int(11) DEFAULT '0' COMMENT '配置变更情况',
  `initiator` varchar(50) DEFAULT NULL COMMENT '考核表发起人',
  `s3_appoint_rater` text COMMENT '指定评分人json',
  index `idx_companyId_evalId` (`company_id`,`emp_eval_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 AVG_ROW_LENGTH=5012 COMMENT='考核规则'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = emp_eval_table   */
/******************************************/
 drop table if exists `emp_eval_table`;
 CREATE TABLE `emp_eval_table`  (
  `company_id` varchar(50) NOT NULL COMMENT '公司id',
  `id` varchar(50) NOT NULL COMMENT '主键:考核表id',
  `emp_id` varchar(50) DEFAULT NULL COMMENT '外键员工id:emloyee_info.employee_id',
  `is_default` int(11) DEFAULT '0' COMMENT '是否员工的默认考核表',
  `rule_name` varchar(250) DEFAULT '' COMMENT '考核表名字',
  `evaluate_type` varchar(10) NOT NULL COMMENT '评分流程类型 ： simple-简易流程，360-360°考核流程，custom-自定义流程',
  `type_weight_conf` varchar(128) DEFAULT '' COMMENT '指标类别权重 json',
  `score_value_conf` varchar(128) DEFAULT '' COMMENT '分值的设定 json',
  `s3_self_rater` varchar(128) DEFAULT '' COMMENT '360或简易 自评人',
  `s3_peer_rater` text COMMENT '同级互评人 json',
  `s3_sub_rater` text COMMENT '同级互评人 json',
  `s3_super_rater` text COMMENT '360或简易 上级人 json',
  `score_view` text COMMENT '可见范围配置 json',
  `index_raters` text COMMENT '评分人索引,用于员工列表的显示,编辑时更新索引',
  `indicator_cnt` int(11) DEFAULT '0' COMMENT '指标数统计',
  `comment_conf` varchar(225) DEFAULT NULL COMMENT '评语与总结配置',
  `is_deleted` varchar(5) DEFAULT 'false' COMMENT '是否删除',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `eval_org_id` varchar(50) DEFAULT NULL COMMENT '被考核组织Id',
  `performance_type` int(11) DEFAULT '1' COMMENT '绩效类型 1=个人，2=组织',
  `s3_appoint_rater` text COMMENT '指定评分人json',
  PRIMARY KEY (`id`),
  KEY `idx_companyId_empId` (`company_id`,`emp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='员工的考核表,一个员工可以有多个考核表'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = emp_organization   */
/******************************************/
 drop table if exists `emp_organization`;
 CREATE TABLE `emp_organization`  (
  `id` varchar(50) DEFAULT NULL,
  `org_id` varchar(50) NOT NULL COMMENT '部门ID',
  `org_code` varchar(1000) DEFAULT NULL COMMENT '部门代码路径，所有父节点加上自身节点的节点ID，采用|号拼接生成',
  `company_id` varchar(50) NOT NULL COMMENT '公司ID',
  `org_name` varchar(128) DEFAULT NULL COMMENT '部门名称',
  `parent_org_id` varchar(50) DEFAULT NULL COMMENT '父级部门ID',
  `manager_id` varchar(50) DEFAULT NULL COMMENT '负责人ID',
  `executor_id` varchar(50) DEFAULT NULL COMMENT '执行人id',
  `status` varchar(50) DEFAULT NULL COMMENT '部门状态:有效；无效',
  `type` varchar(50) DEFAULT NULL COMMENT '部门类型:顶级节点部门；普通部门',
  `name_chinese` varchar(512) DEFAULT NULL COMMENT '搜索关键字',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  `sort_num` bigint(11) DEFAULT NULL COMMENT '排序',
  `top_report_id` varchar(50) DEFAULT NULL COMMENT '最高汇报人ID',
  `staff_num` int(20) DEFAULT NULL COMMENT '人员编制',
  `org_num` varchar(50) DEFAULT NULL COMMENT '部门编码',
  `build_time` datetime DEFAULT NULL COMMENT '成立日期',
  `memo` varchar(255) DEFAULT NULL COMMENT '备注',
  `qy_org_id` varchar(50) DEFAULT NULL COMMENT '企业微信对应id',
  `ding_org_id` varchar(50) DEFAULT NULL COMMENT '钉钉对应id',
  `we_org_id` varchar(50) DEFAULT NULL COMMENT 'welink对应id',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`org_id`) USING BTREE,
  KEY `idx_companyid_orgname` (`company_id`,`org_name`),
  KEY `idx_dingOrgId` (`ding_org_id`),
  KEY `idx_eo_cid` (`company_id`,`org_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='部门'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = emp_ref_announcement   */
/******************************************/
 drop table if exists `emp_ref_announcement`;
 CREATE TABLE `emp_ref_announcement`  (
  `emp_id` varchar(50) DEFAULT NULL,
  `announcement_id` varchar(50) DEFAULT NULL,
  `created_time` varchar(50) DEFAULT NULL,
  KEY `emp_id` (`emp_id`),
  KEY `announcement_id` (`announcement_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = emp_ref_org   */
/******************************************/
 drop table if exists `emp_ref_org`;
 CREATE TABLE `emp_ref_org`  (
  `id` varchar(50) NOT NULL COMMENT 'id',
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `emp_id` varchar(50) DEFAULT NULL COMMENT '员工id',
  `org_id` varchar(50) DEFAULT NULL COMMENT '部门id',
  `ref_type` varchar(10) DEFAULT NULL COMMENT '关联类型(org:员工关联部门,manager:部门负责人)',
  `is_main_org` varchar(10) DEFAULT NULL COMMENT '是否为主部门：true/false',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_ero_cid` (`company_id`) USING BTREE,
  KEY `idx_ero_oid` (`org_id`) USING BTREE,
  KEY `k_emp_id` (`emp_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='员工关联部门表'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = emp_ref_score_rule   */
/******************************************/
 drop table if exists `emp_ref_score_rule`;
 CREATE TABLE `emp_ref_score_rule`  (
  `rule_id` varchar(50) DEFAULT NULL COMMENT '等级分值规则id',
  `emp_id` varchar(50) DEFAULT NULL COMMENT '员工id',
  `emp_org_id` varchar(50) DEFAULT NULL COMMENT '员工所在部门id',
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `created_time` datetime DEFAULT NULL,
  `created_user` varchar(50) DEFAULT '',
  `updated_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) DEFAULT '',
  `is_deleted` varchar(5) DEFAULT 'false',
  `version` int(11) DEFAULT '0',
  `is_add_emp` varchar(10) DEFAULT NULL COMMENT '单独添加进来的人',
  `is_appoint_emp` int(11) DEFAULT '0' COMMENT '创建等级规则时，指定的人',
  KEY `idx_company_id` (`company_id`),
  KEY `idx_emp_id` (`emp_id`),
  KEY `idx_rule_id` (`rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = emp_table_item_rule   */
/******************************************/
 drop table if exists `emp_table_item_rule`;
 CREATE TABLE `emp_table_item_rule`  (
  `table_id` varchar(50) NOT NULL COMMENT '考核表的id,emp_eval_table.id',
  `kpi_item_id` varchar(50) DEFAULT NULL COMMENT '指标项id  emp_table_kpi_item.id',
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `kpi_type_id` varchar(50) DEFAULT NULL COMMENT '指标类id',
  `mutual_user_type` varchar(30) DEFAULT NULL COMMENT '设置互评人类型，all:为所有被考核人设置相同的互评人; user: 为每位被考核人分别设置互评人; exam：由被考核人自行指定',
  `mutual_user_value` text COMMENT '自定义互评设置人',
  `self_rater` text COMMENT ' 自评人',
  `mutual_rater` text COMMENT '互评人 json',
  `super_rater` text COMMENT '上级人 json',
  `appoint_rater` text COMMENT '指定评分人json',
  `peer_rater` text COMMENT '自定义同级互评人',
  `sub_rater` text COMMENT '自定义下级互评人',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `is_deleted` varchar(10) DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  KEY `idx_companyId_tableId` (`company_id`,`table_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='员工考核表-指标流程'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = emp_table_kpi_item   */
/******************************************/
 drop table if exists `emp_table_kpi_item`;
 CREATE TABLE `emp_table_kpi_item`  (
  `table_id` varchar(50) NOT NULL COMMENT '考核表的id,emp_eval_table.id',
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `kpi_type_id` varchar(50) DEFAULT NULL COMMENT '指标类id',
  `kpi_item_id` varchar(50) DEFAULT NULL COMMENT '指标项id',
  `kpi_type_name` varchar(200) DEFAULT NULL COMMENT '指标类名称',
  `kpi_type_weight` decimal(10,2) DEFAULT NULL COMMENT '指标类权重',
  `kpi_item_name` text COMMENT '指标项名称',
  `item_target_value` decimal(18,2) DEFAULT NULL COMMENT '指标项目标值',
  `item_finish_value` decimal(18,2) DEFAULT NULL COMMENT '指标项完成值',
  `item_unit` varchar(20) DEFAULT NULL COMMENT '指标项单位',
  `item_weight` decimal(10,2) DEFAULT NULL COMMENT '指标项权重',
  `result_input_type` varchar(50) DEFAULT NULL COMMENT '结果录入类型',
  `input_emps` text COMMENT '结果录入人id',
  `examine_oper_type` varchar(30) DEFAULT NULL COMMENT '被考核人确认指标时操作类型：增删改',
  `item_rule` text COMMENT '考核规则',
  `scoring_rule` text COMMENT '计分规则',
  `scorer_type` varchar(50) DEFAULT NULL COMMENT '指标评分人类型（按评分流程/指定员工/指定主管）',
  `scorer_obj_id` text COMMENT '指定评分人json串',
  `item_type` varchar(50) DEFAULT NULL COMMENT '指标项类型（量化/非量化）',
  `multiple_reviewers_type` varchar(50) DEFAULT NULL COMMENT '多人审核时，and会签，or或签',
  `kpi_type_classify` varchar(50) DEFAULT NULL COMMENT '指标类别，为空表示已有类别，custom自定义/plus加分项/subtract减分项',
  `plus_limit` decimal(10,2) DEFAULT NULL COMMENT '加分上限',
  `subtract_limit` decimal(10,2) DEFAULT NULL COMMENT '减分上限',
  `max_extra_score` decimal(10,2) DEFAULT NULL COMMENT '本类别最大加减分上限',
  `order` int(11) DEFAULT NULL COMMENT '排序，数字小的排前面',
  `item_formula` text COMMENT '指标计算公式',
  `threshold_json` text COMMENT '量化指标阈值设置json',
  `formula_condition` text COMMENT '公式条件',
  `item_field_json` text COMMENT '指标关联的阈值字段',
  `is_type_locked` varchar(100) DEFAULT NULL COMMENT '类别锁定类型',
  `is_okr` varchar(10) DEFAULT NULL COMMENT '是否OKR类别，true/false',
  `type_order` int(10) DEFAULT NULL COMMENT '类别排序',
  `reserve_okr_weight` decimal(10,2) DEFAULT NULL COMMENT '预留OKR权重',
  `item_score_value` varchar(500) DEFAULT NULL COMMENT '指标评分分值',
  `input_format` varchar(50) DEFAULT NULL COMMENT '录入格式',
  `work_item_finish_value` varchar(2000) DEFAULT NULL COMMENT '工作事项完成情况说明',
  `item_plan_flag` varchar(10) DEFAULT NULL COMMENT '是否同步了年度指标计划',
  `show_target_value` varchar(10) DEFAULT NULL COMMENT '是否展示目标值',
  `must_result_input` tinyint(1) DEFAULT '0' COMMENT '指标完成值是否必需录入',
  `show_finish_bar` int(11) DEFAULT '1' COMMENT '完成度进度条 默认开启 1=显示,0=不显示',
  `manager_level` varchar(10) DEFAULT NULL COMMENT '录入主管等级',
  `item_full_score_cfg` varchar(10) DEFAULT 'false' COMMENT '自动计算指标得分满分值',
  `item_limit_cnt` varchar(255) DEFAULT NULL COMMENT '类别指标数量最大最小限制',
  `item_finish_value_text` varchar(2000) DEFAULT NULL COMMENT '非量化指标完成值文本录入',
  `open_okr_score` int(11) DEFAULT '0' COMMENT '指标评分使用的是okr的分数',
  `okr_score` decimal(10,3) DEFAULT NULL COMMENT 'okr的原始分数',
  `formula_fields` text COMMENT '公式字段列表',
  `is_deleted` varchar(10) DEFAULT NULL COMMENT '是否删除',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `plus_sub_interval` varchar(50) DEFAULT NULL COMMENT '考核表指标加减分上限',
  `item_custom_field_json` text COMMENT '指标自定义字段json',
  KEY `idx_companyId_tableId` (`company_id`,`table_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='员工考核表--关联指标'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = emp_table_kpi_type   */
/******************************************/
 drop table if exists `emp_table_kpi_type`;
 CREATE TABLE `emp_table_kpi_type`  (
  `table_id` varchar(50) NOT NULL COMMENT '考核表的id,emp_eval_table.id',
  `company_id` varchar(50) NOT NULL COMMENT '公司id',
  `kpi_type_id` varchar(50) DEFAULT NULL COMMENT '指标类id',
  `kpi_type_name` varchar(200) DEFAULT NULL COMMENT '指标类名称',
  `kpi_type_weight` decimal(10,2) DEFAULT NULL COMMENT '指标类权重',
  `is_okr` varchar(10) DEFAULT NULL COMMENT '是否OKR类别，true/false',
  `type_order` int(10) DEFAULT NULL COMMENT '类别排序',
  `reserve_okr_weight` decimal(10,2) DEFAULT NULL COMMENT '预留OKR权重',
  `kpi_type_classify` varchar(30) DEFAULT NULL COMMENT '指标类别，为空表示已有类别，custom自定义/plus加分项/subtract减分项',
  `max_extra_score` decimal(10,2) DEFAULT NULL COMMENT '本类别最大加减分上限',
  `import_okr_flag` varchar(10) DEFAULT NULL COMMENT '是否已导入OKR',
  `locked_items` varchar(100) DEFAULT NULL COMMENT 'is_type_locked 类别锁定类型  ["typeWeight","addIndex","modifyIndex","deleteIndex"]',
  `item_limit_cnt` varchar(50) DEFAULT NULL COMMENT '类别指标数量最大最小限制',
  `is_deleted` varchar(10) DEFAULT 'false' COMMENT '是否删除',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `plus_sub_interval` varchar(50) DEFAULT NULL COMMENT '考核表类别加减分上限',
  KEY `idx_companyId_tableId` (`company_id`,`table_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='员工的考核表-指标类别'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = emp_table_operation   */
/******************************************/
 drop table if exists `emp_table_operation`;
 CREATE TABLE `emp_table_operation`  (
  `id` varchar(50) NOT NULL COMMENT '操作记录id',
  `table_id` varchar(50) NOT NULL COMMENT '外键table_id',
  `company_id` varchar(50) NOT NULL COMMENT '公司id',
  `at_status` varchar(50) NOT NULL COMMENT '修改时所在阶段',
  `change_type` int(11) NOT NULL COMMENT '01=1=指标, 10=2=流程,11=3=指标+流程',
  `indicator` mediumtext COMMENT '指标类修改,type_weight_conf,score_value_conf, _kpi表,_kpi_type表',
  `confirm_task` text COMMENT '确认任务 对应模板 templAffirmJson:PerfTemplEvaluateAffirm',
  `edit_exe_indi` text COMMENT '执行中修改指标日志记录  对应模板 templExecuteJson:PerfTemplEvaluateExecute',
  `score_conf` text COMMENT 'enter_score,s3_self_rater,s3_mutual_rater,s3_super_rater ,score_veiw',
  `audit_result` text COMMENT '结果校准,从templEvaluateJson:PerfTemplEvaluate 模板评价配置json 单独出来的',
  `confirm_result` varchar(512) DEFAULT NULL COMMENT '结果确认,多个字段合并',
  `publish_result` text COMMENT '结果公示,多个字段合并',
  `appeal_conf` varchar(512) DEFAULT NULL COMMENT '结果申诉,多个字段合并',
  `operation_time` bigint(20) NOT NULL COMMENT '操作时间毫秒ms',
  `operator_id` varchar(50) NOT NULL COMMENT '操作人emp_id',
  `operator_name` varchar(50) NOT NULL COMMENT '操作人名',
  `operator_avatar` varchar(225) NOT NULL COMMENT '操作人头像',
  `created_time` datetime DEFAULT NULL COMMENT '记录创建时间',
  `admin_type` varchar(25) DEFAULT NULL COMMENT '操作人管理员类型/admin/child',
  `base_info` text COMMENT '基本信息',
  PRIMARY KEY (`id`),
  KEY `idx_companyId_tableId` (`company_id`,`table_id`),
  KEY `idx_operation_time` (`operation_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='员工考核表修改记录'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = enter_on_msg_log   */
/******************************************/
 drop table if exists `enter_on_msg_log`;
 CREATE TABLE `enter_on_msg_log`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL,
  `created_time` varchar(50) DEFAULT NULL,
  `update_user_id` varchar(50) DEFAULT NULL,
  `before_change_status` int(1) DEFAULT NULL COMMENT '改变前的状态',
  `after_change_status` int(1) DEFAULT NULL COMMENT '改变后的状态',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = eval_kpi_input_bak   */
/******************************************/
 drop table if exists `eval_kpi_input_bak`;
 CREATE TABLE `eval_kpi_input_bak`  (
  `id` int(11) NOT NULL,
  `company_id` varchar(50) NOT NULL COMMENT '公司id',
  `task_user_id` varchar(50) NOT NULL COMMENT 'task user 表的id',
  `kpi_item_id` varchar(50) DEFAULT NULL,
  `result_input_type` varchar(50) DEFAULT NULL,
  `result_input_emp_id` text,
  `is_deleted` varchar(10) DEFAULT 'false' COMMENT '是否删除',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`),
  KEY `task_user_id` (`task_user_id`),
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='备份录入人转交前的录入人和录入类型，用来重置回复录入人'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = global_sys_param   */
/******************************************/
 drop table if exists `global_sys_param`;
 CREATE TABLE `global_sys_param`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT '0' COMMENT '企业id,固定的id=0',
  `param_name` varchar(50) DEFAULT NULL COMMENT '参数名称',
  `param_value` varchar(500) DEFAULT NULL COMMENT '参数值',
  `expires_in` int(11) DEFAULT NULL COMMENT '有效时间，单位：秒',
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_param_name` (`param_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统参数表'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = grade_step   */
/******************************************/
 drop table if exists `grade_step`;
 CREATE TABLE `grade_step`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `company_id` varchar(50) NOT NULL,
  `name` varchar(50) NOT NULL COMMENT '等阶名字,多个等阶组成套等级规则',
  `sort` int(11) DEFAULT NULL,
  `created_time` date DEFAULT NULL,
  `created_user` varchar(50) DEFAULT '',
  `updated_time` date DEFAULT NULL,
  `updated_user` varchar(50) DEFAULT '',
  `is_deleted` varchar(5) DEFAULT 'false',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`),
  KEY `idx_company_id` (`company_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3688465 DEFAULT CHARSET=utf8mb4
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = hrm_solution_order   */
/******************************************/
 drop table if exists `hrm_solution_order`;
 CREATE TABLE `hrm_solution_order`  (
  `id` varchar(50) NOT NULL COMMENT 'id',
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司ID',
  `solution_type` varchar(50) NOT NULL DEFAULT 'onboarding' COMMENT '解决方案类型:onboarding=新人流程',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '解决方案的名字：新人入职解决方案',
  `max_cnt` int(11) DEFAULT NULL COMMENT '最大可用数',
  `used_cnt` int(11) DEFAULT NULL COMMENT '最大可用数',
  `is_open` int(11) DEFAULT '0' COMMENT '菜单是否打开 0=不打开, 1=打开',
  `push_id` bigint(20) NOT NULL COMMENT '创建此单据的事件id',
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) NOT NULL DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`),
  KEY `idx_companyId_type` (`company_id`,`solution_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业购的买人事解决方案'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = kpi_nemp_order   */
/******************************************/
 drop table if exists `kpi_nemp_order`;
 CREATE TABLE `kpi_nemp_order`  (
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司ID',
  `corp_id` varchar(50) DEFAULT NULL COMMENT '三方平台id',
  `order_id` varchar(50) DEFAULT NULL COMMENT '订单号',
  `present_rel_main_order_id` varchar(50) DEFAULT NULL COMMENT '满赠订单关联的付费主订单ID',
  `goods_code` varchar(50) DEFAULT NULL COMMENT '下单组织购买的官方应用商品码',
  `goods_name` varchar(50) DEFAULT NULL COMMENT '下单组织购买的官方应用商品名称',
  `item_code` varchar(50) DEFAULT NULL COMMENT '下单组织购买的官方应用规格码',
  `item_name` varchar(50) DEFAULT NULL COMMENT '下单组织购买的官方应用规格名称',
  `order_type` varchar(50) DEFAULT NULL COMMENT 'BUY=新购,RENEW=续费,UPGRADE=升级,RENEW_UPGRADE=续费升配,RENEW_DEGRADE=续费降配',
  `order_status` int(11) DEFAULT NULL COMMENT '订单状态 1=新购,2=续购,4=送,16=退',
  `service_start_time` bigint(20) NOT NULL COMMENT '订单有效-开始时间',
  `service_stop_time` bigint(20) NOT NULL COMMENT '订单过期时间',
  `paidtime` bigint(20) NOT NULL COMMENT '支付时间',
  `order_label` int(11) DEFAULT NULL COMMENT '订单标记, 0=普通订单, 1=满赠订单',
  `cnt` int(11) DEFAULT NULL COMMENT '购的新人可用次数',
  `used_cnt` int(11) DEFAULT NULL COMMENT '已用次数',
  `push_id` bigint(20) NOT NULL COMMENT '创建此单据的事件id',
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) NOT NULL DEFAULT '0' COMMENT '版本号',
  KEY `idx_companyId` (`company_id`),
  KEY `idx_extCorpId` (`corp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业的直通车额度订单'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = kpi_nemp_used_log   */
/******************************************/
 drop table if exists `kpi_nemp_used_log`;
 CREATE TABLE `kpi_nemp_used_log`  (
  `id` varchar(50) DEFAULT NULL COMMENT '记录id',
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司ID',
  `spend_cnt` int(11) DEFAULT NULL COMMENT '购的新人可用次数',
  `order_id` varchar(50) DEFAULT NULL COMMENT '订单号',
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) NOT NULL DEFAULT '0' COMMENT '版本号',
  `use_emp_id` varchar(50) DEFAULT NULL,
  KEY `idx_companyId` (`company_id`),
  KEY `use_emp_id` (`use_emp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直通车额度消耗日志'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = last_cycle_task_conf   */
/******************************************/
 drop table if exists `last_cycle_task_conf`;
 CREATE TABLE `last_cycle_task_conf`  (
  `tenant_id` varchar(50) NOT NULL COMMENT '企业id',
  `temp_id` varchar(50) NOT NULL COMMENT '模板id',
  `enable` int(11) DEFAULT '1' COMMENT '1=可用,0=失效不可用',
  `target_value` longtext COMMENT '目标值配置',
  `finish_value_input_user` mediumtext COMMENT '完成值录入人配置',
  `task_score_rule` longtext COMMENT '评分人配置',
  `created_user` varchar(50) DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `customer_item_score_rule` longtext COMMENT '自定义指定评分人',
  KEY `k_temp_id` (`temp_id`),
  KEY `k_created_user` (`created_user`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='最近一次自定义的模板发起时的配置'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = media   */
/******************************************/
 drop table if exists `media`;
 CREATE TABLE `media`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `media_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '媒体文件id',
  `media_type` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '媒体文件类型',
  `scene` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '使用场景',
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='媒体文件'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = my_id   */
/******************************************/
 drop table if exists `my_id`;
 CREATE TABLE `my_id`  (
  `id` varchar(50) NOT NULL,
  `task_id` varchar(50) DEFAULT NULL,
  `company_id` varchar(50) DEFAULT NULL,
  `emp_id` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = new_emp   */
/******************************************/
 drop table if exists `new_emp`;
 CREATE TABLE `new_emp`  (
  `company_id` varchar(50) NOT NULL COMMENT '企业id',
  `emp_id` varchar(50) DEFAULT '' COMMENT '员工id',
  `evaled` int(11) DEFAULT '0' COMMENT '是否已考核0',
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `ext_user_id` varchar(50) NOT NULL COMMENT '钉钉员工id',
  `ext_instance_id` varchar(50) NOT NULL COMMENT '钉钉流程实例id',
  KEY `k_tenantId_empId` (`company_id`,`emp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='新入职员工'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = onboard_timer_conf   */
/******************************************/
 drop table if exists `onboard_timer_conf`;
 CREATE TABLE `onboard_timer_conf`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `company_id` varchar(50) NOT NULL COMMENT '公司id',
  `confirm_timer` text COMMENT '确认提醒配置 {"open":1,conf:["timerType": 2, "id": "10000", "modCode": 1000001, "eachType": 1,"conf":{"beforDays":0,"afterDays":46,"type":1,"runTime":"11:00"}]}',
  `scoring_timer` text COMMENT '评分提醒配置 {"open":1,[{"timerType": 2, "id": "10000", "modCode": 1000002, "eachType": 1, "conf": {"beforDays": 0, "afterDays": 0, "type": 1, "runTime": "11:00"}}]}',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_user` varchar(50) DEFAULT '',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_user` varchar(50) DEFAULT '',
  `is_deleted` varchar(5) DEFAULT 'false',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`),
  KEY `otc_idx_companyId` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='新人考核的定时提醒配置'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = onboard_timer_item   */
/******************************************/
 drop table if exists `onboard_timer_item`;
 CREATE TABLE `onboard_timer_item`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `company_id` varchar(50) NOT NULL COMMENT '公司id',
  `onboard_timer_conf_id` bigint(20) NOT NULL COMMENT 'onboard_timer_conf.id',
  `mod_code` bigint(20) NOT NULL COMMENT '子模块编号:[1000001=新人任务确认],[1000002=新人评分提醒管理员],[1000003=新人评分提醒评分人]',
  `conf` text COMMENT 'json配置对象',
  `timer_type` int(11) DEFAULT '1' COMMENT '执行分类: forEach执行=2, 单次执行=1',
  `each_type` int(11) DEFAULT '1' COMMENT 'forEach分类: 每天=1, 每小时=2',
  `exe_cond_sql` text COMMENT '检测执行条件的sql,参数中要的company_id',
  `biz_method` text COMMENT '执行业务操作方法',
  `last_run_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `lock_id` bigint(20) DEFAULT '0' COMMENT '并发执行的标记锁id,0=未锁定, 大于0表示执行的锁id',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_user` varchar(50) DEFAULT '',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_user` varchar(50) DEFAULT '',
  `is_deleted` varchar(5) DEFAULT 'false',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`),
  KEY `oti_idx_companyId` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='新人考核的定时提醒明细项'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = onboard_timer_log   */
/******************************************/
 drop table if exists `onboard_timer_log`;
 CREATE TABLE `onboard_timer_log`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `company_id` varchar(50) NOT NULL COMMENT '公司id',
  `timer_item_id` bigint(20) NOT NULL COMMENT 'onboard_timer_item.id,已执行过的timerItemId',
  `task_user_id` varchar(50) NOT NULL COMMENT '单次提醒过的记录id',
  `lock_id` bigint(20) DEFAULT '0' COMMENT '并发执行的标记锁id,0=未锁定, 大于0表示执行的锁id',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_user` varchar(50) DEFAULT '',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_user` varchar(50) DEFAULT '',
  `is_deleted` varchar(5) DEFAULT 'false',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`),
  KEY `otl_idx_companyId_itemId` (`company_id`,`timer_item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='新人考核的定时明细执行记录'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = open_access_cache   */
/******************************************/
 drop table if exists `open_access_cache`;
 CREATE TABLE `open_access_cache`  (
  `key` varchar(191) NOT NULL COMMENT 'key',
  `val` varchar(255) DEFAULT NULL COMMENT 'value',
  `expire_in` bigint(20) DEFAULT NULL COMMENT '毫秒',
  `db_timestamp` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '当前时间戳',
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提供开放接口的access token缓存持久化'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = open_access_limit   */
/******************************************/
 drop table if exists `open_access_limit`;
 CREATE TABLE `open_access_limit`  (
  `key` varchar(191) NOT NULL COMMENT '格式化的key',
  `max_count` bigint(20) DEFAULT '1000' COMMENT '每小时最大访问量',
  `access_count` bigint(20) DEFAULT '0' COMMENT '当前时间段已持久化的访问量',
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开放接口每小时访问量持久化表'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = open_auth_info   */
/******************************************/
 drop table if exists `open_auth_info`;
 CREATE TABLE `open_auth_info`  (
  `corp_id` varchar(191) NOT NULL COMMENT 'corp id',
  `company_id` varchar(191) NOT NULL COMMENT 'company id',
  `company_name` varchar(255) DEFAULT NULL COMMENT 'company name',
  `contact` varchar(255) DEFAULT NULL COMMENT '联系人，可以是联系我们开通的人，仅参考',
  `access_key` varchar(255) DEFAULT NULL COMMENT '访问key',
  `access_secret` varchar(255) DEFAULT NULL COMMENT '访问secret',
  `access_token` varchar(255) DEFAULT NULL,
  `perm_start_time` varchar(255) DEFAULT NULL COMMENT '服务开启时间',
  `perm_end_time` varchar(255) DEFAULT NULL COMMENT '服务结束时间',
  `max_access_limit` bigint(20) DEFAULT NULL COMMENT '总访问上限，-1不限制',
  `access_count` bigint(20) DEFAULT NULL COMMENT '当前访问量',
  `apis` text COMMENT '授权的接口',
  `ips` text COMMENT '授权的ip',
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`company_id`),
  KEY `k_corp_id` (`corp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提供开放接口的注册公司信息'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = operation_log   */
/******************************************/
 drop table if exists `operation_log`;
 CREATE TABLE `operation_log`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `ref_id` varchar(50) DEFAULT NULL COMMENT '关联的id',
  `business_scene` varchar(50) DEFAULT NULL COMMENT '业务场景',
  `field_name` varchar(50) DEFAULT NULL COMMENT '字段名称',
  `before_value` text COMMENT '修改前的值',
  `after_value` text COMMENT '修改后的值',
  `operation_type` varchar(20) DEFAULT NULL COMMENT '操作类型(增删改)',
  `description` text COMMENT '描述',
  `created_user` varchar(50) DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `kpi_item_id` varchar(50) DEFAULT NULL COMMENT '关联的指标id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`),
  KEY `idx_refid_businessscene` (`ref_id`,`business_scene`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='操作日志'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = org_eval_owner   */
/******************************************/
 drop table if exists `org_eval_owner`;
 CREATE TABLE `org_eval_owner`  (
  `org_id` varchar(50) NOT NULL COMMENT '部门id',
  `company_id` varchar(50) NOT NULL COMMENT '公司id',
  `org_owner_id` varchar(50) DEFAULT '' COMMENT '组织负责人',
  `org_owner_org_name` varchar(50) DEFAULT '' COMMENT '组织负责人所在部门',
  `org_owner_org_id` varchar(50) DEFAULT '' COMMENT '组织负责人所在部门id',
  `index_tables` varchar(500) DEFAULT '[]' COMMENT '组织负责人',
  `table_cnt` int(11) DEFAULT '0' COMMENT '组织负责人',
  `is_deleted` varchar(5) DEFAULT 'false' COMMENT '是否删除',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`org_id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_org_id` (`org_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='组织任务负责人'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_evaluate_cycle   */
/******************************************/
 drop table if exists `perf_evaluate_cycle`;
 CREATE TABLE `perf_evaluate_cycle`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '周期id',
  `company_id` varchar(50) NOT NULL COMMENT '企业id',
  `name` varchar(100) DEFAULT NULL,
  `year` int(11) DEFAULT '0' COMMENT '周期年份',
  `type` varchar(15) DEFAULT 'month' COMMENT '"month", "cross_month", "quarter", "half_year", "year"',
  `value` int(11) DEFAULT '0' COMMENT '1-12月,1-4季,1-12双月,1-2半年',
  `cycle_start` varchar(100) DEFAULT NULL COMMENT '周期开始时间点',
  `cycle_end` varchar(100) DEFAULT NULL COMMENT '周期结束时间点',
  `eval_cnt` int(11) DEFAULT '0' COMMENT '周期中考核人次',
  `from_task_id` varchar(50) DEFAULT '0' COMMENT '旧数据的任务id,0=表示新数据',
  `created_user` varchar(50) DEFAULT '',
  `updated_user` varchar(50) DEFAULT '',
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `cycle_status` varchar(10) DEFAULT 'normal' COMMENT '周期状态',
  `is_new_cycle` int(11) DEFAULT '0' COMMENT '是否新周期',
  PRIMARY KEY (`id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_created_user` (`created_user`),
  KEY `idx_type` (`type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1910422 DEFAULT CHARSET=utf8mb4 COMMENT='考核周期'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_evaluate_item_notice   */
/******************************************/
 drop table if exists `perf_evaluate_item_notice`;
 CREATE TABLE `perf_evaluate_item_notice`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL,
  `task_id` varchar(50) DEFAULT NULL,
  `emp_id` varchar(50) DEFAULT NULL,
  `kpi_type_id` varchar(50) DEFAULT NULL,
  `kpi_item_id` varchar(50) DEFAULT NULL,
  `send_week_day` varchar(50) DEFAULT NULL COMMENT '每周发送日期',
  `send_time` varchar(255) DEFAULT NULL COMMENT '发送时间',
  `close_type` varchar(50) DEFAULT NULL COMMENT '关闭提醒的条件，date指定日期，finish任务完成',
  `close_date` date DEFAULT NULL COMMENT '关闭提醒的日期',
  `update_notice_user` text COMMENT '更新后抄送进度的人',
  `status` varchar(30) DEFAULT NULL COMMENT '状态 normal正常，close已关闭',
  `is_deleted` varchar(10) DEFAULT NULL,
  `created_user` varchar(50) DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `task_user_id` varchar(50) DEFAULT NULL COMMENT 'task user 表的id',
  PRIMARY KEY (`id`),
  KEY `company_id` (`company_id`),
  KEY `idx_task_user_id` (`task_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_evaluate_score_summary   */
/******************************************/
 drop table if exists `perf_evaluate_score_summary`;
 CREATE TABLE `perf_evaluate_score_summary`  (
  `company_id` varchar(50) NOT NULL,
  `task_user_id` varchar(50) NOT NULL,
  `score_type` varchar(255) NOT NULL,
  `summary` text,
  `created_time` datetime DEFAULT NULL,
  `created_user` varchar(50) DEFAULT '',
  `updated_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) DEFAULT '',
  `is_deleted` varchar(5) DEFAULT 'false',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  KEY `company_id` (`company_id`) USING BTREE,
  KEY `task_user_id` (`task_user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评分总结'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_evaluate_task_action   */
/******************************************/
 drop table if exists `perf_evaluate_task_action`;
 CREATE TABLE `perf_evaluate_task_action`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `task_id` varchar(50) DEFAULT NULL COMMENT '考核任务id',
  `task_kpi_id` varchar(50) DEFAULT NULL COMMENT '考核任务指标id',
  `name` varchar(100) DEFAULT NULL COMMENT '行动名称',
  `start_time` datetime DEFAULT NULL COMMENT '计划完成日期开始',
  `end_time` datetime DEFAULT NULL COMMENT '计划完成日期截止',
  `priority` int(11) DEFAULT NULL COMMENT '优先级(0-普通,1-中,2-高)',
  `progress` decimal(10,2) DEFAULT NULL COMMENT '当前进度（百分比）',
  `responsible_emp_id` varchar(50) DEFAULT NULL COMMENT '指定责任人id',
  `remark` varchar(200) DEFAULT NULL COMMENT '描述',
  `is_deleted` varchar(10) DEFAULT NULL COMMENT '是否删除',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='考核任务-关键行动'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_evaluate_task_appeal_batch   */
/******************************************/
 drop table if exists `perf_evaluate_task_appeal_batch`;
 CREATE TABLE `perf_evaluate_task_appeal_batch`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL,
  `task_id` varchar(50) DEFAULT NULL,
  `emp_id` varchar(50) DEFAULT NULL,
  `appeal_date` date DEFAULT NULL COMMENT '申诉日期',
  `receiver_name` varchar(100) DEFAULT NULL COMMENT '申诉受理人姓名',
  `receiver_id` varchar(50) DEFAULT NULL COMMENT '申诉受理人id',
  `appeal_status` varchar(20) DEFAULT NULL COMMENT '申诉状态（待受理：wait/已受理：handled/已撤回：canceled）',
  `is_deleted` varchar(10) DEFAULT NULL,
  `created_user` varchar(50) DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `is_read` tinyint(1) DEFAULT NULL COMMENT '审核记录是否已读',
  `task_user_id` varchar(50) DEFAULT NULL COMMENT 'task user 表的id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='考核任务申诉批次'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_evaluate_task_appeal_info   */
/******************************************/
 drop table if exists `perf_evaluate_task_appeal_info`;
 CREATE TABLE `perf_evaluate_task_appeal_info`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL,
  `task_id` varchar(50) DEFAULT NULL,
  `emp_id` varchar(50) DEFAULT NULL,
  `appeal_batch_id` varchar(50) DEFAULT NULL COMMENT '申诉批次id',
  `items` text COMMENT '申诉项目',
  `reason` text COMMENT '申诉理由',
  `remark` text COMMENT '申诉备注',
  `appeal_result` varchar(20) DEFAULT NULL COMMENT '申诉结果（通过：pass/驳回：reject）',
  `adjust_score` decimal(8,2) DEFAULT NULL COMMENT '调整分数',
  `is_deleted` varchar(10) DEFAULT NULL,
  `created_user` varchar(50) DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `step_id` int(11) DEFAULT '0' COMMENT '调整的等级Id',
  `step_name` varchar(20) DEFAULT '' COMMENT '调整的等级',
  `appeal_files` text,
  `appeal_pictures` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='考核任务申诉明细'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_evaluate_task_audit   */
/******************************************/
 drop table if exists `perf_evaluate_task_audit`;
 CREATE TABLE `perf_evaluate_task_audit`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `task_id` varchar(50) DEFAULT NULL COMMENT '考核任务id',
  `emp_id` varchar(50) DEFAULT NULL COMMENT '被考核人id',
  `org_id` varchar(50) DEFAULT NULL COMMENT '被考核人部门id',
  `kpi_item_id` varchar(50) DEFAULT NULL COMMENT '指标id',
  `scene` varchar(50) DEFAULT NULL COMMENT '考核场景（确认任务/上级评分/结果审批）',
  `approval_order` int(11) DEFAULT NULL COMMENT '审批顺序（从1开始递增）',
  `approver_type` varchar(30) DEFAULT NULL COMMENT '审批人类型（指定级别主管/指定人员）',
  `approver_info` text COMMENT '指定对象id（级别）',
  `weight` decimal(10,2) DEFAULT NULL COMMENT '权重',
  `multiple_reviewers_type` varchar(30) DEFAULT NULL COMMENT '多个审核人时会签还是或签',
  `transfer_flag` varchar(10) DEFAULT NULL COMMENT '是否可转交',
  `vacancy_approver_type` varchar(30) DEFAULT NULL COMMENT '审批人空缺时指定人类型（管理员/指定人员）',
  `vacancy_approver_info` text COMMENT '审批人空缺时指定人id',
  `status` varchar(50) DEFAULT NULL,
  `is_deleted` varchar(10) DEFAULT NULL,
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `kpi_type_id` varchar(50) DEFAULT NULL COMMENT '指标类id',
  `modify_flag` varchar(10) DEFAULT NULL COMMENT '是否可以修改',
  `last_audit_id` varchar(50) DEFAULT NULL COMMENT '上个审核节点id',
  `audit_open` int(11) DEFAULT '1' COMMENT '执行阶段变更指标是否打开审核流程,默认打开 1=开,0=关 ',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `merge_weight_del` int(1) DEFAULT '0' COMMENT '合并权重是标记删除的audit，重置时候需要恢复',
  `task_user_id` varchar(50) DEFAULT NULL COMMENT '员工考核id,task_user.id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`),
  KEY `k_task_id` (`task_id`) USING BTREE,
  KEY `group_key` (`company_id`,`task_id`,`scene`,`is_deleted`) USING BTREE,
  KEY `k_task_user_id` (`task_user_id`,`is_deleted`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='考核任务-审批执行'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_evaluate_task_audit_init   */
/******************************************/
 drop table if exists `perf_evaluate_task_audit_init`;
 CREATE TABLE `perf_evaluate_task_audit_init`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `temp_base_id` varchar(50) DEFAULT NULL COMMENT '模板基础id',
  `task_id` varchar(50) DEFAULT NULL COMMENT '考核任务id',
  `emp_id` varchar(50) DEFAULT NULL COMMENT '被考核员工id',
  `kpi_item_id` varchar(50) DEFAULT NULL COMMENT '指标id',
  `scene` varchar(30) DEFAULT NULL COMMENT '考核场景（确认任务/上级评分/结果审批）',
  `approval_order` int(11) DEFAULT NULL COMMENT '审批顺序（从1开始递增）',
  `approver_type` varchar(30) DEFAULT NULL COMMENT '审批人类型（指定级别主管/指定人员）',
  `approver_info` varchar(200) DEFAULT NULL COMMENT '指定对象id（级别）',
  `superior_score_weight` decimal(10,2) DEFAULT NULL COMMENT '上级评分权重',
  `multiple_reviewers_type` varchar(30) DEFAULT NULL COMMENT '多个责任人或签还是会签',
  `transfer_flag` varchar(10) DEFAULT NULL COMMENT '是否可转交',
  `vacancy_approver_type` varchar(30) DEFAULT NULL COMMENT '审批人空缺时指定人类型（管理员/指定人员）',
  `vacancy_approver_info` varchar(200) DEFAULT NULL COMMENT '审批人空缺时指定人id',
  `score_rule` varchar(50) DEFAULT NULL COMMENT '评分规则，total对考核任务打总分，item对每个指标打分',
  `status` varchar(30) DEFAULT NULL COMMENT '当前节点状态',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `is_deleted` varchar(10) DEFAULT NULL,
  `is_default` varchar(10) DEFAULT NULL COMMENT '是否默认配置，true/false',
  `kpi_type_id` varchar(50) DEFAULT NULL COMMENT '指标类id',
  `modify_flag` varchar(10) DEFAULT NULL COMMENT '是否可以修改',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`),
  KEY `company_id` (`company_id`),
  KEY `k_task_id` (`task_id`) USING BTREE,
  KEY `k_temp_base_id` (`temp_base_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_evaluate_task_base   */
/******************************************/
 drop table if exists `perf_evaluate_task_base`;
 CREATE TABLE `perf_evaluate_task_base`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `task_name` varchar(250) DEFAULT NULL COMMENT '考核任务名称',
  `templ_base_id` varchar(50) DEFAULT NULL COMMENT '考核模板id',
  `templ_name` varchar(200) DEFAULT NULL COMMENT '考核模板名称',
  `cycle_start_date` varchar(20) DEFAULT NULL COMMENT '考核周期起始',
  `cycle_end_date` varchar(20) DEFAULT NULL COMMENT '考核周期截止',
  `task_desc` text COMMENT '考核任务描述',
  `evaluation_staff` text COMMENT '考核员工JSON对象；[{"obj_type":"对象类型(部门/角色/岗位/指定员工)",“objItems”:[{"objId":"对象id","objName":"对象名称"}]}]',
  `exclude_staff` text COMMENT '排除员工JSON对象；[{"empId":"员工id","empName":"员工姓名"}]',
  `create_task_type` varchar(20) DEFAULT NULL COMMENT '发送考核方式（自动/手动）',
  `create_task_date_type` varchar(20) DEFAULT NULL COMMENT '发起日期类型（开始前/开始后）',
  `day` int(11) DEFAULT NULL COMMENT '发起日期值',
  `visible_type` varchar(30) DEFAULT NULL COMMENT '可见范围类型(公司/部门/自己和上级)',
  `task_status` varchar(30) DEFAULT NULL COMMENT '考核任务状态',
  `is_deleted` varchar(10) DEFAULT NULL COMMENT '是否删除',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `cycle_type` varchar(50) DEFAULT NULL COMMENT '考核周期类型',
  `templ_desc` text COMMENT '模板描述',
  `templ_item_json` longtext COMMENT '模板关联的指标json',
  `templ_initiate_json` text COMMENT '模板发起考核任务配置json',
  `templ_affirm_json` text COMMENT '模板确认指标配置json',
  `templ_evaluate_json` longtext COMMENT '模板评价配置json',
  `score_start_rule_type` varchar(50) DEFAULT NULL COMMENT '评分开始时间规则类型（周期结束前/后）',
  `score_start_rule_day` int(11) DEFAULT NULL COMMENT '评分开始时间规则值',
  `enter_score_method` varchar(30) DEFAULT NULL COMMENT '进入评分方式，手动manual，自动auto',
  `public_type` varchar(50) DEFAULT NULL COMMENT '考核结果公示类型：auto:实时自动公示，afterFinished: 考核任务完成后自动公示，manual:考核任务完成后，由发起人手动公示',
  `templ_base_json` text COMMENT '模板基础信息json',
  `evaluate_type` varchar(20) DEFAULT NULL COMMENT '评分流程类型 ： simple-简易流程，360-360°考核流程，custom-自定义流程',
  `public_emp_json` text COMMENT '手动公示人信息',
  `templ_execute_json` text COMMENT '模板执行阶段配置',
  `result_affirm` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '结果是否需（被考核人）确认；true/false',
  `affirm_signature` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '确认是否需（被考核人）签名；true/false',
  `templ_points_json` longtext,
  `can_appeal` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '是否可申诉；true/false',
  `appeal_receiver` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '申述受理人；格式：[{"obj_type":"user","objItems":[{"objId":"员工id","objName":"姓名"}]}]',
  `custom_full_score` decimal(10,2) DEFAULT NULL COMMENT '自定义满分分值',
  `enter_score_emp_type` int(11) DEFAULT NULL COMMENT ' 发起评分的人员type=1管理员,type=2考核员工',
  `public_dimension` int(11) DEFAULT '3' COMMENT '公示维度',
  `public_to_emp` text COMMENT '公示范围',
  `auto_result_affirm` varchar(10) DEFAULT 'false' COMMENT '自动确认结果/true/false',
  `auto_result_affirm_day` int(11) DEFAULT '0' COMMENT '自动确认结果限制的时间',
  `level_group_id` varchar(50) DEFAULT '' COMMENT '关联哪个等级组/perf_evaluation_level 的level_group_id',
  `cycle_id` bigint(20) DEFAULT '0' COMMENT '周期id',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `is_new_emp` int(11) DEFAULT '0' COMMENT '是否新人任务',
  `total_cnt` int(11) DEFAULT '0' COMMENT '参与人数',
  `draw_up_cnt` int(11) DEFAULT '0' COMMENT '已制定人数',
  `start_cnt` int(11) DEFAULT '0' COMMENT '已发起人数',
  `finish_cnt` int(11) DEFAULT '0' COMMENT '已完成人数',
  `confirm_task` text COMMENT '确认任务 对应模板 templAffirmJson:PerfTemplEvaluateAffirm',
  `edit_exe_indi` text COMMENT '执行中修改指标  对应模板 templExecuteJson:PerfTemplEvaluateExecute',
  `enter_score` varchar(128) DEFAULT '' COMMENT '启动评分配置 ,多字段合并',
  `score_sort_conf` text COMMENT '2.0.0新版:新加字段 评分环节顺序配置',
  `score_view` text COMMENT '可见范围配置 json',
  `audit_result` text COMMENT '结果校准,从templEvaluateJson:PerfTemplEvaluate 模板评价配置json 单独出来的',
  `confirm_result` text COMMENT '结果确认,多个字段合并',
  `publish_result` text COMMENT '结果公示,多个字段合并',
  `appeal_conf` varchar(512) DEFAULT NULL COMMENT '结果申诉,多个字段合并',
  `comment_conf` text COMMENT '评语与总结配置',
  `performance_type` int(11) DEFAULT '1' COMMENT '绩效类型 1=个人绩效，2=组织绩效',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`),
  KEY `k_start_date` (`cycle_start_date`),
  KEY `k_end_date` (`cycle_end_date`),
  KEY `idx_task_name` (`task_name`(100)),
  KEY `idx_cycle_id` (`cycle_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 AVG_ROW_LENGTH=170412 ROW_FORMAT=DYNAMIC COMMENT='考核任务基础信息'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_evaluate_task_coach   */
/******************************************/
 drop table if exists `perf_evaluate_task_coach`;
 CREATE TABLE `perf_evaluate_task_coach`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `company_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `task_user_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '任务用户id',
  `summary` text COLLATE utf8mb4_bin COMMENT '总结',
  `suggestion` varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '改进建议',
  `files` text COLLATE utf8mb4_bin COMMENT '附件url，多个逗号分隔',
  `emp_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '辅导员工id',
  `emp_name` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '辅导员工姓名',
  `emp_avatar` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '辅导员工头像',
  `is_deleted` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否删除',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `coach_type` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '辅导模型;  RAP模型：rap，标准模型：standard，自定义：custom',
  `coach_content` text COLLATE utf8mb4_bin COMMENT '辅导内容',
  `company_msg_id` varchar(100) COLLATE utf8mb4_bin DEFAULT '' COMMENT '工作台代办消息id',
  `is_read` varchar(10) COLLATE utf8mb4_bin DEFAULT 'false' COMMENT '已读标识',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='绩效任务面谈辅导'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_evaluate_task_discuss   */
/******************************************/
 drop table if exists `perf_evaluate_task_discuss`;
 CREATE TABLE `perf_evaluate_task_discuss`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `company_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `task_user_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `type` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '评论类型（主评论/回复评论）',
  `main_discuss_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '主评论id',
  `emp_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '评论员工id',
  `emp_name` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '评论员工姓名',
  `emp_avatar` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '评论员工头像',
  `content` varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '评论内容',
  `content_att` text COLLATE utf8mb4_bin COMMENT '评论附件',
  `seq` int(3) DEFAULT NULL COMMENT '评论顺序',
  `kpi_item_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `is_deleted` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否删除',
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='绩效任务评论'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_evaluate_task_file   */
/******************************************/
 drop table if exists `perf_evaluate_task_file`;
 CREATE TABLE `perf_evaluate_task_file`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL,
  `task_user_id` varchar(50) DEFAULT NULL,
  `type` varchar(20) DEFAULT NULL COMMENT '业务类型',
  `content` varchar(2000) DEFAULT NULL COMMENT '评论内容',
  `file_url` text COMMENT '评论附件',
  `kpi_item_id` varchar(50) DEFAULT NULL,
  `is_deleted` varchar(10) DEFAULT NULL COMMENT '是否删除',
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `file_id` varchar(50) DEFAULT NULL,
  `file_name` varchar(255) DEFAULT NULL,
  `file_size` int(11) DEFAULT NULL,
  `file_type` varchar(50) DEFAULT NULL,
  `space_id` varchar(50) DEFAULT NULL,
  `created_user` varchar(50) DEFAULT NULL,
  `updated_user` varchar(50) DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`),
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_evaluate_task_formula_field   */
/******************************************/
 drop table if exists `perf_evaluate_task_formula_field`;
 CREATE TABLE `perf_evaluate_task_formula_field`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `company_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `task_user_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'task user id',
  `task_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '考核任务id',
  `kpi_item_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '指标id',
  `company_field_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司公式字段id',
  `formula_field_name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公式里的字段名称',
  `formula_field_value` decimal(18,2) DEFAULT NULL COMMENT '公式里的字段值',
  `is_deleted` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`),
  KEY `idx_task_user_id` (`task_id`,`task_user_id`) USING BTREE,
  KEY `task_user_id` (`task_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_evaluate_task_item_score_rule   */
/******************************************/
 drop table if exists `perf_evaluate_task_item_score_rule`;
 CREATE TABLE `perf_evaluate_task_item_score_rule`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `company_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司id',
  `task_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '任务id',
  `task_user_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `kpi_item_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '指标项id',
  `self_score_flag` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否自评',
  `self_score_view_rule` varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '被考核人查看评分规则JSON',
  `self_score_weight` decimal(10,2) DEFAULT NULL COMMENT '自评权重',
  `mutual_score_flag` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否互评',
  `mutual_score_attend_rule` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '互评参与规则',
  `mutual_score_anonymous` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '互评人姓名是否匿名',
  `mutual_score_vacancy` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '互评人空缺时规则',
  `mutual_score_view_rule` varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '互评人查看评分规则JSON',
  `peer_score_weight` decimal(10,2) DEFAULT NULL COMMENT '同级互评权重',
  `sub_score_weight` decimal(10,2) DEFAULT NULL COMMENT '下级互评权重',
  `superior_score_flag` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否上级评',
  `superior_score_view_rule` varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '上级评人查看评分规则JSON',
  `superior_score_weight` decimal(10,2) DEFAULT NULL COMMENT '上级评权重',
  `superior_score_vacancy` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '上级评分人空缺时规则',
  `appoint_score_flag` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否指定评分',
  `appoint_score_weight` decimal(10,2) DEFAULT NULL COMMENT '指定评分权重',
  `created_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `is_deleted` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL,
  `kpi_type_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '指标类id',
  `mutual_user_type` varchar(30) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '设置互评人类型，all:为所有被考核人设置相同的互评人; user: 为每位被考核人分别设置互评人; exam：由被考核人自行指定',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `mutual_user_value` varchar(2000) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '自定义互评设置人',
  `self_rater` varchar(128) COLLATE utf8mb4_bin DEFAULT '' COMMENT ' 自评人',
  `mutual_rater` text COLLATE utf8mb4_bin COMMENT '互评人 json',
  `super_rater` text COLLATE utf8mb4_bin COMMENT '上级人 json',
  `appoint_rater` text COLLATE utf8mb4_bin COMMENT '指定评分人json',
  `peer_rater` text COLLATE utf8mb4_bin COMMENT '自定义同级互评人',
  `sub_rater` text COLLATE utf8mb4_bin COMMENT '自定义下级互评人',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`),
  KEY `k_task_user_id` (`task_user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_evaluate_task_kpi   */
/******************************************/
 drop table if exists `perf_evaluate_task_kpi`;
 CREATE TABLE `perf_evaluate_task_kpi`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `org_id` varchar(50) DEFAULT NULL COMMENT '部门id',
  `task_id` varchar(50) DEFAULT NULL COMMENT '考核任务id',
  `emp_id` varchar(50) DEFAULT NULL COMMENT '被考核人id',
  `task_user_id` varchar(50) DEFAULT NULL COMMENT 'task user 表的id',
  `reviewer` text COMMENT '审核人信息',
  `kpi_type_id` varchar(50) DEFAULT NULL COMMENT '指标类id',
  `kpi_type_name` varchar(200) DEFAULT NULL COMMENT '指标类名称',
  `kpi_type_weight` decimal(10,2) DEFAULT NULL COMMENT '指标类权重',
  `kpi_item_id` varchar(50) DEFAULT NULL COMMENT '指标项id',
  `kpi_item_name` text COMMENT '指标项名称',
  `item_target_value` decimal(18,2) DEFAULT NULL COMMENT '指标项目标值',
  `item_finish_value` decimal(18,2) DEFAULT NULL COMMENT '指标项完成值',
  `item_unit` varchar(20) DEFAULT NULL COMMENT '指标项单位',
  `item_weight` decimal(10,2) DEFAULT NULL COMMENT '指标项权重',
  `result_input_type` varchar(50) DEFAULT NULL COMMENT '结果录入类型',
  `result_input_emp_id` text COMMENT '结果录入人id',
  `backup` varchar(500) DEFAULT NULL COMMENT '备份，用于指标变更审核时的恢复操作',
  `examine_oper_type` varchar(30) DEFAULT NULL COMMENT '被考核人确认指标时操作类型：增删改',
  `is_deleted` varchar(10) DEFAULT NULL COMMENT '是否删除',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `item_rule` text COMMENT '考核规则',
  `scoring_rule` text COMMENT '计分规则',
  `scorer_type` varchar(50) DEFAULT NULL COMMENT '指标评分人类型（按评分流程/指定员工/指定主管）',
  `scorer_obj_id` text COMMENT '指定评分人json串',
  `item_type` varchar(50) DEFAULT NULL COMMENT '指标项类型（量化/非量化）',
  `multiple_reviewers_type` varchar(50) DEFAULT NULL COMMENT '多人审核时，and会签，or或签',
  `kpi_type_classify` varchar(50) DEFAULT NULL COMMENT '指标类别，为空表示已有类别，custom自定义/plus加分项/subtract减分项',
  `plus_limit` decimal(10,2) DEFAULT NULL COMMENT '加分上限',
  `subtract_limit` decimal(10,2) DEFAULT NULL COMMENT '减分上限',
  `max_extra_score` decimal(10,2) DEFAULT NULL COMMENT '本类别最大加减分上限',
  `order` int(11) DEFAULT NULL COMMENT '排序，数字小的排前面',
  `item_formula` text COMMENT '指标计算公式',
  `item_auto_score` decimal(10,3) DEFAULT NULL COMMENT '指标自动计算后的分数',
  `threshold_json` text COMMENT '量化指标阈值设置json',
  `urging_flag` varchar(10) DEFAULT NULL COMMENT '完成值录入催办标识',
  `item_actual_formula` text COMMENT '代入真实值后的计算公式',
  `auto_score_ex_flag` varchar(10) DEFAULT NULL COMMENT '自动算分异常标识',
  `formula_condition` text COMMENT '公式条件',
  `item_field_json` text COMMENT '指标关联的阈值字段',
  `is_type_locked` varchar(100) DEFAULT NULL COMMENT '类别锁定类型',
  `is_okr` varchar(10) DEFAULT NULL COMMENT '是否OKR类别，true/false',
  `type_order` int(10) DEFAULT NULL COMMENT '类别排序',
  `okr_ref_flag` varchar(20) DEFAULT NULL COMMENT '指标被OKR关联的标记',
  `reserve_okr_weight` decimal(10,2) DEFAULT NULL COMMENT '预留OKR权重',
  `points_rule` varchar(30) DEFAULT NULL COMMENT '积分规则',
  `points_num` decimal(10,2) DEFAULT NULL COMMENT '指标积分',
  `item_score_value` varchar(500) DEFAULT NULL COMMENT '指标评分分值',
  `input_format` varchar(50) DEFAULT NULL COMMENT '录入格式',
  `work_item_finish_value` varchar(2000) DEFAULT NULL COMMENT '工作事项完成情况说明',
  `item_plan_flag` varchar(10) DEFAULT NULL COMMENT '是否同步了年度指标计划',
  `show_target_value` varchar(10) DEFAULT NULL COMMENT '是否展示目标值',
  `must_result_input` tinyint(1) DEFAULT '0' COMMENT '指标完成值是否必需录入',
  `show_finish_bar` int(11) DEFAULT '1' COMMENT '完成度进度条 默认开启 1=显示,0=不显示',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `is_new_emp` int(11) DEFAULT '0' COMMENT '是否新人培训指标',
  `manager_level` varchar(10) DEFAULT NULL COMMENT '录入主管等级',
  `item_full_score_cfg` varchar(10) DEFAULT 'false' COMMENT '自动计算指标得分满分值',
  `final_submit_finish_value` int(1) DEFAULT '0' COMMENT '最终提交完成值标识',
  `item_limit_cnt` varchar(50) DEFAULT NULL COMMENT '类别指标数量最大最小限制',
  `item_finish_value_text` varchar(2000) DEFAULT NULL COMMENT '非量化指标完成值文本录入',
  `open_okr_score` int(11) DEFAULT '0' COMMENT '指标评分使用的是okr的分数',
  `okr_score` decimal(10,3) DEFAULT NULL COMMENT 'okr的原始分数',
  `plus_sub_interval` varchar(50) DEFAULT NULL COMMENT '考核规则指标加减分上限',
  `item_custom_field_json` text COMMENT '指标自定义字段json',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_empid` (`emp_id`),
  KEY `k_task_id` (`task_id`) USING BTREE,
  KEY `k_task_user_id` (`task_user_id`) USING BTREE,
  KEY `k_item_id` (`kpi_item_id`),
  KEY `k_companyId_taskUserId_itemId` (`company_id`,`task_user_id`,`kpi_item_id`),
  KEY `idx_companyId_taskUserId_typeId` (`company_id`,`task_user_id`,`kpi_type_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 AVG_ROW_LENGTH=1724 ROW_FORMAT=DYNAMIC COMMENT='考核任务--关联指标'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_evaluate_task_okr_type   */
/******************************************/
 drop table if exists `perf_evaluate_task_okr_type`;
 CREATE TABLE `perf_evaluate_task_okr_type`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `company_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `task_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `emp_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `task_user_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `kpi_type_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '指标类id',
  `kpi_type_name` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '指标类名称',
  `kpi_type_weight` decimal(10,2) DEFAULT NULL COMMENT '指标类权重',
  `kpi_type_classify` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '指标类别，为空表示已有类别，custom自定义/plus加分项/subtract减分项',
  `max_extra_score` decimal(10,2) DEFAULT NULL COMMENT '本类别最大加减分上限',
  `type_order` int(11) DEFAULT NULL COMMENT '排序，数字小的排前面',
  `is_type_locked` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '类别锁定类型',
  `is_okr` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否OKR类别，true/false',
  `import_okr_flag` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否已导入OKR',
  `reserve_okr_weight` decimal(10,2) DEFAULT NULL COMMENT '预留OKR权重',
  `is_deleted` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `open_okr_score` int(11) DEFAULT '0' COMMENT '指标评分使用的是okr的分数',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`),
  KEY `k_taskId` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_evaluate_task_ref_okr   */
/******************************************/
 drop table if exists `perf_evaluate_task_ref_okr`;
 CREATE TABLE `perf_evaluate_task_ref_okr`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `company_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `task_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `emp_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `task_kpi_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `action_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '成果id',
  `action_name` varchar(1000) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '成果名称',
  `target_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '目标id',
  `target_name` varchar(1000) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'OKR目标名称',
  `okr_task_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'OKR任务id',
  `okr_task_name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'OKR任务名',
  `cycle` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'OKR周期',
  `evaluate_start_date` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '开始时间',
  `evaluate_end_date` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '结束时间',
  `unit` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '单位',
  `dept_name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '部门名称',
  `user_name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '员工名称',
  `is_deleted` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_user` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `task_user_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'task user 表的id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`),
  KEY `k_task_kpi_id` (`task_kpi_id`),
  KEY `idx_task_user_id` (`task_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_evaluate_task_score_result   */
/******************************************/
 drop table if exists `perf_evaluate_task_score_result`;
 CREATE TABLE `perf_evaluate_task_score_result`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `task_id` varchar(50) DEFAULT NULL COMMENT '考核任务id',
  `org_id` varchar(50) DEFAULT NULL COMMENT '部门id',
  `emp_id` varchar(50) DEFAULT NULL COMMENT '被考核人id',
  `kpi_type_id` varchar(50) DEFAULT NULL COMMENT '指标类id',
  `kpi_item_id` varchar(50) DEFAULT NULL COMMENT '指标项id',
  `scorer_type` varchar(50) DEFAULT NULL COMMENT '评分人类型（自评/同级互评/下级互评/上级评/指定评分人）',
  `scorer_id` varchar(50) DEFAULT NULL COMMENT '评分人id',
  `score` decimal(10,2) DEFAULT NULL COMMENT '评分分数',
  `score_weight` decimal(10,2) DEFAULT NULL COMMENT '评分权重',
  `score_comment` text COMMENT '评分评语',
  `score_att_url` text COMMENT '评分附件url',
  `reviewers_type` varchar(30) DEFAULT NULL COMMENT '会签还是或签',
  `audit_status` varchar(30) DEFAULT NULL COMMENT '审核状态',
  `final_score` decimal(10,3) DEFAULT NULL COMMENT '没算阶段权重的分数',
  `transfer_id` varchar(1000) DEFAULT NULL COMMENT '转交来源id',
  `approval_order` int(11) DEFAULT NULL COMMENT '审批顺序',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `plus_score` decimal(10,2) DEFAULT NULL COMMENT '加分',
  `subtract_score` decimal(10,2) DEFAULT NULL COMMENT '减分',
  `final_plus_score` decimal(10,2) DEFAULT NULL COMMENT '加分加权计算得分',
  `final_subtract_score` decimal(10,2) DEFAULT NULL COMMENT '减分加权计算后得分',
  `final_weight_score` decimal(10,3) DEFAULT NULL COMMENT '最终加权后的分数',
  `final_weight_plus_score` decimal(10,2) DEFAULT NULL COMMENT '最终加权后的加分总分',
  `final_weight_subtract_score` decimal(10,2) DEFAULT NULL COMMENT '最终加权后的减分总分',
  `score_level` varchar(255) DEFAULT NULL COMMENT '打分人提交的绩效等级',
  `no_item_score` decimal(10,3) DEFAULT NULL COMMENT '没算指标权重的分数',
  `emp_score` decimal(10,3) DEFAULT NULL COMMENT '评分人加权后的分数',
  `is_deleted` varchar(10) CHARACTER SET utf8 DEFAULT NULL,
  `task_audit_id` varchar(50) CHARACTER SET utf8 DEFAULT NULL COMMENT '审核节点id',
  `modify_flag` varchar(10) CHARACTER SET utf8 DEFAULT NULL COMMENT '是否可以修改',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `merge_rs_infos` varchar(500) DEFAULT NULL COMMENT '相同评分人合并权重后记录被合并的rs信息',
  `task_user_id` varchar(50) DEFAULT NULL COMMENT '员工考核id,task_user.id',
  `calibration_type` int(11) DEFAULT NULL COMMENT '校准类型（1：按人员校准 2：按分组校准 3：按指标评分校准）',
  `operate_reason` longtext COMMENT '校准内容',
  `index_calibration` text COMMENT '指标校准时，用于存放json字符串',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `k_scorer_id` (`scorer_id`) USING BTREE,
  KEY `idx_task_id` (`task_id`),
  KEY `idx_emp_id` (`emp_id`),
  KEY `company_id` (`company_id`) USING BTREE,
  KEY `item_id` (`kpi_item_id`) USING BTREE,
  KEY `scorer_type_key` (`scorer_type`),
  KEY `k_task_user_id` (`task_user_id`,`is_deleted`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='考核任务-评分结果'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_evaluate_task_score_result_tmp   */
/******************************************/
 drop table if exists `perf_evaluate_task_score_result_tmp`;
 CREATE TABLE `perf_evaluate_task_score_result_tmp`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `task_id` varchar(50) DEFAULT NULL COMMENT '考核任务id',
  `org_id` varchar(50) DEFAULT NULL COMMENT '部门id',
  `emp_id` varchar(50) DEFAULT NULL COMMENT '被考核人id',
  `kpi_type_id` varchar(50) DEFAULT NULL COMMENT '指标类id',
  `kpi_item_id` varchar(50) DEFAULT NULL COMMENT '指标项id',
  `scorer_type` varchar(50) DEFAULT NULL COMMENT '评分人类型（自评/同级互评/下级互评/上级评/指定评分人）',
  `scorer_id` varchar(50) DEFAULT NULL COMMENT '评分人id',
  `score` decimal(10,2) DEFAULT NULL COMMENT '评分分数',
  `score_weight` decimal(10,2) DEFAULT NULL COMMENT '评分权重',
  `score_comment` text COMMENT '评分评语',
  `score_att_url` text COMMENT '评分附件url',
  `reviewers_type` varchar(30) DEFAULT NULL COMMENT '会签还是或签',
  `audit_status` varchar(30) DEFAULT NULL COMMENT '审核状态',
  `final_score` decimal(10,3) DEFAULT NULL COMMENT '没算阶段权重的分数',
  `transfer_id` varchar(1000) DEFAULT NULL COMMENT '转交来源id',
  `approval_order` int(11) DEFAULT NULL COMMENT '审批顺序',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `plus_score` decimal(10,2) DEFAULT NULL COMMENT '加分',
  `subtract_score` decimal(10,2) DEFAULT NULL COMMENT '减分',
  `final_plus_score` decimal(10,2) DEFAULT NULL COMMENT '加分加权计算得分',
  `final_subtract_score` decimal(10,2) DEFAULT NULL COMMENT '减分加权计算后得分',
  `final_weight_score` decimal(10,3) DEFAULT NULL COMMENT '最终加权后的分数',
  `final_weight_plus_score` decimal(10,2) DEFAULT NULL COMMENT '最终加权后的加分总分',
  `final_weight_subtract_score` decimal(10,2) DEFAULT NULL COMMENT '最终加权后的减分总分',
  `score_level` varchar(255) DEFAULT NULL COMMENT '打分人提交的绩效等级',
  `no_item_score` decimal(10,3) DEFAULT NULL COMMENT '没算指标权重的分数',
  `emp_score` decimal(10,3) DEFAULT NULL COMMENT '评分人加权后的分数',
  `is_deleted` varchar(10) DEFAULT NULL,
  `task_audit_id` varchar(50) DEFAULT NULL COMMENT '审核节点id',
  `modify_flag` varchar(10) DEFAULT NULL COMMENT '是否可以修改',
  PRIMARY KEY (`id`),
  KEY `company_id` (`company_id`),
  KEY `item_id` (`kpi_item_id`),
  KEY `task_and_emp` (`task_id`,`emp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考核任务-评分结果'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_evaluate_task_score_rule   */
/******************************************/
 drop table if exists `perf_evaluate_task_score_rule`;
 CREATE TABLE `perf_evaluate_task_score_rule`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `task_id` varchar(50) DEFAULT NULL COMMENT '考核任务id',
  `emp_id` varchar(50) DEFAULT NULL COMMENT '被考核人id',
  `org_id` varchar(50) DEFAULT NULL,
  `task_user_id` varchar(50) DEFAULT NULL,
  `need_self_score` varchar(10) DEFAULT NULL COMMENT '是否自评',
  `peer_score_json` longtext COMMENT '同级互评人列表',
  `sub_score_json` longtext COMMENT '下级互评人列表',
  `superior_score_json` text COMMENT '上级评分人列表',
  `result_audit_json` text COMMENT '考核结果审批人列表',
  `is_deleted` varchar(10) DEFAULT NULL,
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='考核任务-指标评分规则'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_evaluate_task_user   */
/******************************************/
 drop table if exists `perf_evaluate_task_user`;
 CREATE TABLE `perf_evaluate_task_user`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `task_id` varchar(50) DEFAULT NULL COMMENT '考核任务id',
  `emp_id` varchar(50) DEFAULT NULL COMMENT '被考核用户id',
  `org_id` varchar(50) DEFAULT NULL,
  `task_status` varchar(50) DEFAULT NULL COMMENT '任务状态',
  `final_score` decimal(11,2) DEFAULT NULL COMMENT '考核最终评分',
  `original_final_score` decimal(11,2) DEFAULT NULL COMMENT '原考核分数',
  `evaluation_level` varchar(20) DEFAULT NULL COMMENT '考评等级',
  `reviewers_json` text COMMENT '流程责任人列表',
  `self_score_flag` varchar(10) DEFAULT NULL COMMENT '自评完成标识，true标识已完成',
  `manual_score_flag` varchar(10) DEFAULT NULL COMMENT '互评完成标识，true表示已完成',
  `superior_score_flag` varchar(10) DEFAULT NULL COMMENT '上级评分完成标识',
  `item_score_flag` varchar(10) DEFAULT NULL COMMENT '指标评分完成标识',
  `result_audit_flag` varchar(10) DEFAULT NULL COMMENT '最终结果审核完成标识',
  `final_self_score` decimal(10,3) DEFAULT NULL COMMENT '自评最终得分',
  `final_peer_score` decimal(10,3) DEFAULT NULL COMMENT '同级互评最终得分',
  `final_sub_score` decimal(10,3) DEFAULT NULL COMMENT '下级互评最终得分',
  `final_superior_score` decimal(10,3) DEFAULT NULL COMMENT '上级评分最终得分',
  `final_item_score` decimal(10,3) DEFAULT NULL COMMENT '指定指标评分人最终得分',
  `last_score_comment` varchar(255) DEFAULT NULL COMMENT '最近一条评分审核修改理由',
  `task_confirm_time` datetime DEFAULT NULL COMMENT '任务确认时间',
  `task_score_start_time` datetime DEFAULT NULL COMMENT '进入评分时间',
  `is_deleted` varchar(10) DEFAULT NULL COMMENT '是否删除',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `final_plus_score` decimal(10,3) DEFAULT NULL COMMENT '最终加分',
  `final_subtract_score` decimal(10,3) DEFAULT NULL COMMENT '最终减分',
  `public_flag` varchar(10) DEFAULT NULL COMMENT '是否已公示',
  `final_self_plus_score` decimal(10,3) DEFAULT NULL COMMENT '最终自评加分',
  `final_peer_plus_score` decimal(10,3) DEFAULT NULL COMMENT '最终同级互评加分',
  `final_sub_plus_score` decimal(10,3) DEFAULT NULL COMMENT '最终下级互评加分',
  `final_superior_plus_score` decimal(10,3) DEFAULT NULL COMMENT '最终上级加分',
  `final_self_subtract_score` decimal(10,3) DEFAULT NULL COMMENT '最终自评加减分',
  `final_peer_subtract_score` decimal(10,3) DEFAULT NULL COMMENT '最终同级互评减分',
  `final_sub_subtract_score` decimal(10,3) DEFAULT NULL COMMENT '最终下级互评减分',
  `final_superior_subtract_score` decimal(10,3) DEFAULT NULL COMMENT '最终上级减分',
  `final_item_plus_score` decimal(10,3) DEFAULT NULL COMMENT '指定评分人最终加分',
  `final_item_subtract_score` decimal(10,3) DEFAULT NULL COMMENT '指定评分人最终减分',
  `final_item_auto_score` decimal(10,3) DEFAULT NULL COMMENT '自动评分指标最终分数',
  `enter_score_flag` varchar(10) DEFAULT NULL COMMENT '已发起评分操作标识',
  `original_evaluation_level` varchar(20) DEFAULT NULL COMMENT '原始考评等级',
  `distribution_flag` varchar(10) DEFAULT NULL COMMENT '是否经过正态分布',
  `adjust_reason` varchar(255) DEFAULT NULL COMMENT '调整绩效结果理由',
  `total_points_num` decimal(10,2) DEFAULT NULL COMMENT '任务总积分',
  `signature_pic` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '签名图片',
  `item_change_user` varchar(50) DEFAULT NULL COMMENT '指标变更人',
  `has_appeal` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否有申诉；true/false',
  `appeal_receiver_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '申诉受理人id',
  `cc_emp_ids` text COMMENT '评分抄送人、公示抄送人取值地方',
  `in_result_affirm_time` date DEFAULT NULL COMMENT '进入结果确认的时间',
  `is_publish` varchar(10) DEFAULT 'true' COMMENT '某个任务下的某个被考核人是否可公示',
  `all_scored` varchar(10) DEFAULT NULL COMMENT '所有评分人都评分完成',
  `step_id` varchar(50) DEFAULT NULL COMMENT '等阶id',
  `score_ranges` text COMMENT '每个人都带着等级分值组走',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `is_new_emp` int(11) DEFAULT '0' COMMENT '是否新人任务',
  `emp_name` varchar(50) DEFAULT '' COMMENT '员工名字',
  `emp_org_name` longtext,
  `emp_org_id` longtext,
  `appeal_dead_line` varchar(50) DEFAULT NULL COMMENT '结果申诉截止时间',
  `org_name_list` longtext,
  `org_changed` int(11) DEFAULT '0' COMMENT '标记考核人员部门已变更 1=已变更, 0=无变更, 3(11)=有变更已处理',
  `input_finish_status` int(1) DEFAULT NULL COMMENT '完成值录入情况：0=不需要录入、1=未录入、2=部分录入、3=全部录入',
  `confirm_dead_line` varchar(50) DEFAULT NULL COMMENT '指标确认截止时间',
  `distribution_before_step_id` int(11) DEFAULT NULL COMMENT '正态分布之前的stepId',
  `cycle_id` bigint(20) DEFAULT '0' COMMENT '周期id',
  `temp_task` int(11) DEFAULT '1' COMMENT '是否从模板创建任务,旧数据标识',
  `rule_conf_status` int(11) DEFAULT '0' COMMENT '考核表配置进度, 0=未配置, 100 = 进行中, 101=进行中有异常  200=完成ok ',
  `avatar` varchar(256) DEFAULT '' COMMENT '考核员工头像',
  `score_end_time` datetime DEFAULT NULL COMMENT '评分阶段结束时间',
  `eval_org_name` varchar(200) DEFAULT NULL COMMENT '被考核组织名',
  `eval_org_id` varchar(100) DEFAULT NULL COMMENT '被考核组织id',
  `weight_of_ref` decimal(10,3) DEFAULT NULL COMMENT '在关联绩效的权重',
  `score_of_ref` decimal(10,3) DEFAULT NULL COMMENT '关联绩效后的得分',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`),
  KEY `company_emp` (`company_id`,`emp_id`,`is_deleted`),
  KEY `company_task_id` (`company_id`,`task_id`,`is_deleted`) USING BTREE,
  KEY `task_id` (`task_id`),
  KEY `task_status` (`task_status`) USING BTREE,
  KEY `eval_org_id` (`eval_org_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 AVG_ROW_LENGTH=1340 ROW_FORMAT=DYNAMIC COMMENT='考核任务-用户信息'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_evaluation_level   */
/******************************************/
 drop table if exists `perf_evaluation_level`;
 CREATE TABLE `perf_evaluation_level`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `grade_name` varchar(100) DEFAULT NULL COMMENT '等级名称',
  `grade_lowest` decimal(10,2) DEFAULT NULL COMMENT '等级最底分',
  `grade_highest` decimal(10,2) DEFAULT NULL COMMENT '等级最高分',
  `ratio` decimal(10,2) DEFAULT NULL COMMENT '等级占比',
  `is_system` varchar(10) DEFAULT NULL COMMENT '是否是系统设置的等级，是true',
  `level_rule` varchar(30) DEFAULT NULL COMMENT '等级分布规则，score按分数评定等级，rank按排名强制占比',
  `level_group_id` varchar(50) DEFAULT NULL COMMENT '绩效分组id',
  `is_deleted` varchar(10) DEFAULT NULL COMMENT '是否删除',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `rank` int(11) DEFAULT NULL COMMENT '排名',
  `name` varchar(50) DEFAULT NULL COMMENT '等级组名称',
  `step_id` varchar(50) DEFAULT NULL COMMENT '等阶id',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `min_append_equal` int(11) DEFAULT '0' COMMENT '0拼在max上，1拼在min上',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='绩效等级设置'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_evaluation_level_group   */
/******************************************/
 drop table if exists `perf_evaluation_level_group`;
 CREATE TABLE `perf_evaluation_level_group`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `company_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司id',
  `group_name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '分组名称',
  `level_rule` varchar(30) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '等级分布规则，score按分数评定等级，rank按排名强制占比',
  `is_deleted` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否删除',
  `created_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_item_ref_point_detail   */
/******************************************/
 drop table if exists `perf_item_ref_point_detail`;
 CREATE TABLE `perf_item_ref_point_detail`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL,
  `templ_base_id` varchar(50) DEFAULT NULL,
  `task_user_id` varchar(50) DEFAULT NULL,
  `ref_id` varchar(50) DEFAULT NULL COMMENT '关联指标数据id',
  `grade_lowest` decimal(10,2) DEFAULT NULL,
  `grade_highest` decimal(10,2) DEFAULT NULL,
  `points` decimal(10,2) DEFAULT NULL COMMENT '积分分值',
  `is_deleted` varchar(10) DEFAULT NULL,
  `created_user` varchar(50) DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`),
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_level_group_rel_temp   */
/******************************************/
 drop table if exists `perf_level_group_rel_temp`;
 CREATE TABLE `perf_level_group_rel_temp`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `company_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司id',
  `level_group_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '绩效分组id',
  `temp_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '模板id',
  `is_deleted` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否删除',
  `created_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_modification_record   */
/******************************************/
 drop table if exists `perf_modification_record`;
 CREATE TABLE `perf_modification_record`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL,
  `link_id` varchar(50) DEFAULT NULL COMMENT '业务关联id',
  `business_scene` varchar(50) DEFAULT NULL COMMENT '业务场景',
  `status` varchar(50) DEFAULT NULL COMMENT '数据状态',
  `value` text COMMENT '缓存的value',
  `is_deleted` varchar(10) DEFAULT NULL,
  `created_user` varchar(50) DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`),
  KEY `company_id` (`company_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_templ_base   */
/******************************************/
 drop table if exists `perf_templ_base`;
 CREATE TABLE `perf_templ_base`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `name` varchar(200) DEFAULT NULL COMMENT '模板名称',
  `cycle_type` varchar(50) DEFAULT NULL COMMENT '考核周期类型',
  `templ_desc` text COMMENT '模板描述',
  `status` varchar(30) DEFAULT NULL COMMENT '模板的状态，draft草稿，published已发布',
  `public_type` varchar(50) DEFAULT NULL,
  `public_emp_json` text COMMENT '手动公示人信息',
  `type_weight_switch` varchar(10) DEFAULT NULL COMMENT '指标类别权重开关，open/close',
  `type_weight_limit_flag` varchar(10) DEFAULT NULL COMMENT '限制类别权重之和为100%开关',
  `score_range_type` varchar(30) DEFAULT NULL COMMENT '得分范围类型：fullScore满分、weightScore指标加权后的分数',
  `is_deleted` varchar(10) DEFAULT NULL COMMENT '是否删除 true/false',
  `created_user` varchar(50) DEFAULT NULL COMMENT '考核结果公示类型：auto:实时自动公示，afterFinished: 考核任务完成后自动公示，manual:考核任务完成后，由发起人手动公示',
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `evaluate_type` varchar(20) DEFAULT NULL COMMENT '评分流程类型 ： simple-简易流程，360-360°考核流程，custom-自定义流程',
  `points_rule` varchar(20) DEFAULT NULL COMMENT '积分规则，open/close',
  `public_to_emp` text COMMENT '公示给哪些人看',
  `can_appeal` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '是否可申诉；true/false',
  `appeal_receiver` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '申述受理人；格式：[{"obj_type":"user","objItems":[{"objId":"员工id","objName":"姓名"}]}]',
  `check_item_weight_flag` varchar(10) DEFAULT NULL COMMENT '是否校验指标权重之和',
  `check_item_weight` decimal(10,0) DEFAULT NULL COMMENT '校验指标权重之和',
  `result_affirm` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '结果是否需（被考核人）确认；true/false',
  `affirm_signature` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '确认是否需（被考核人）签名；true/false',
  `base_score` decimal(10,2) DEFAULT NULL COMMENT '基准分',
  `custom_full_score` decimal(10,2) DEFAULT NULL COMMENT '自定义满分分值',
  `public_dimension` int(11) DEFAULT '3' COMMENT '公示维度',
  `auto_result_affirm` varchar(10) DEFAULT 'false' COMMENT '自动确认结果/true/false',
  `auto_result_affirm_day` int(11) DEFAULT '0' COMMENT '自动确认结果限制的时间',
  `level_group_id` varchar(50) DEFAULT '' COMMENT '关联哪个等级组/perf_evaluation_level 的level_group_id',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `is_new_emp` int(11) DEFAULT '0' COMMENT '是否新人模板',
  `post` varchar(50) DEFAULT '' COMMENT '岗位',
  `new_org_ids` text COMMENT '新人模板的部门ids',
  `result_appeal_node` int(11) DEFAULT '70' COMMENT '哪个节点的结果审核 70=考核任务结束后的结果申诉 50=结果确认中的结果申诉',
  `can_appeal_day` int(11) DEFAULT '10' COMMENT '考核任务结束后的结果申诉  考核任务结束后几天之内可以申诉',
  `exceed_full_score` varchar(10) DEFAULT 'false' COMMENT '评分是否可以超过满分分值',
  `result_confirm_type` int(11) DEFAULT '50' COMMENT '结果确认类型  10=等级 ，20=分数 ，默认50=等级/分数',
  `is_example` int(11) DEFAULT '0' COMMENT '是否示例模板 默认0=非示例模板、1=示例模板',
  `onboard_auto_start` int(11) DEFAULT '0' COMMENT '员工新入职后以该任务为员工自动发起考核 1=开启自动, 0=关闭自动',
  `onboard_cycle_cnt` int(11) DEFAULT '0' COMMENT '入职后{onboardCycleCnt}{onboardCycleUnit}内为新人考核时间段',
  `onboard_cycle_unit` int(11) DEFAULT '0' COMMENT '入职后{onboardCycleCnt}{onboardCycleUnit}内为新人考核时间段',
  `match_child_org` int(11) DEFAULT '0' COMMENT '是否匹配子部门人员:1=是,0=否',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='绩效模板基础信息'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_templ_evaluate   */
/******************************************/
 drop table if exists `perf_templ_evaluate`;
 CREATE TABLE `perf_templ_evaluate`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `templ_base_id` varchar(50) DEFAULT NULL COMMENT '模板基础id',
  `score_start_rule_type` varchar(50) DEFAULT NULL COMMENT '评分开始时间规则类型（周期结束前/后）',
  `score_start_rule_day` int(11) DEFAULT NULL COMMENT '评分开始时间规则值',
  `self_score_flag` varchar(10) DEFAULT NULL COMMENT '是否自评',
  `self_score_rule` varchar(50) DEFAULT NULL COMMENT '自评评分规则（打总分/指标打分）',
  `self_score_view_rule` varchar(500) DEFAULT NULL COMMENT '被考核人查看评分规则JSON',
  `self_score_weight` decimal(10,2) DEFAULT NULL COMMENT '自评权重',
  `mutual_score_flag` varchar(10) DEFAULT NULL COMMENT '是否互评',
  `mutual_score_rule` varchar(50) DEFAULT NULL COMMENT '互评规则（打总分/指标打分）',
  `mutual_score_attend_rule` varchar(50) DEFAULT NULL COMMENT '互评参与规则',
  `mutual_score_anonymous` varchar(10) DEFAULT NULL COMMENT '互评人姓名是否匿名',
  `mutual_score_vacancy` varchar(50) DEFAULT NULL COMMENT '互评人空缺时规则',
  `mutual_score_view_rule` varchar(500) DEFAULT NULL COMMENT '互评人查看评分规则JSON',
  `peer_score_weight` decimal(10,2) DEFAULT NULL COMMENT '同级互评权重',
  `sub_score_weight` decimal(10,2) DEFAULT NULL COMMENT '下级互评权重',
  `superior_score_flag` varchar(10) DEFAULT NULL COMMENT '是否上级评',
  `superior_score_order` varchar(50) DEFAULT NULL COMMENT '上级评顺序类型（同时/依次）',
  `superior_score_view_rule` varchar(500) DEFAULT NULL COMMENT '上级评人查看评分规则JSON',
  `superior_score_weight` decimal(10,2) DEFAULT NULL COMMENT '上级评权重',
  `superior_score_rule` varchar(50) DEFAULT NULL COMMENT '上级评分规则（打总分/指标打分）',
  `superior_score_vacancy` varchar(50) DEFAULT NULL COMMENT '上级评分人空缺时规则',
  `audit_flag` varchar(10) DEFAULT NULL COMMENT '结果是否审批',
  `comment_flag` varchar(30) DEFAULT NULL COMMENT '评语填写类型（选填/必填/按评分值）',
  `comment_required_value` int(11) DEFAULT NULL COMMENT '评语按评分值规则值, 低于某个比例',
  `comment_required_high_value` int(11) DEFAULT NULL COMMENT '评语按评分值规则值, 高于某个比例',
  `enter_score_method` varchar(30) DEFAULT NULL COMMENT '进入评分方式，手动manual，自动auto',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `is_deleted` varchar(10) DEFAULT NULL,
  `evaluate_type` varchar(20) DEFAULT NULL COMMENT '评分流程类型 ： simple-简易流程，360-360°考核流程，custom-自定义流程',
  `appoint_score_view_rule` varchar(500) DEFAULT NULL COMMENT '指定评分查看规则',
  `multiple_reviewers_type` varchar(30) DEFAULT NULL COMMENT '多个责任人或签还是会签',
  `duplicate_type` varchar(10) DEFAULT NULL COMMENT '当责任人担任多个评分人时,true表示去重，false表示不去重',
  `role_vacancy_type` varchar(30) DEFAULT NULL COMMENT '指定角色空缺时,skip跳过，taskAdmin转交给考核任务发起人',
  `submit_level_flag` varchar(10) DEFAULT NULL COMMENT '评分时是否提交绩效等级，true/false',
  `mutual_user_type` varchar(30) DEFAULT NULL COMMENT '设置互评人类型，all:为所有被考核人设置相同的互评人; user: 为每位被考核人分别设置互评人',
  `enter_score_emp_type` int(11) DEFAULT NULL COMMENT ' 发起评分的人员type=1管理员,type=2考核员工',
  `is_add_audit_comment` tinyint(1) DEFAULT '0' COMMENT '是否需要校准备注',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `score_summary_switch` int(11) DEFAULT '-1' COMMENT '评分总结开关 0非必填/1必填/-1关闭 默认=-1',
  `plus_or_sub_comment` int(11) DEFAULT '0' COMMENT '加减分评语填设置/1(必填)/0(不必填)/-1关闭',
  `sub_score_flag` varchar(10) DEFAULT NULL COMMENT '下级互评开关',
  `sub_score_rule` varchar(10) DEFAULT NULL COMMENT '下级互评规则',
  `sub_user_type` varchar(50) DEFAULT NULL COMMENT '下级指定评分人类型',
  `sub_user_value` varchar(50) DEFAULT NULL COMMENT '下级指定人类型id',
  `peer_user_type` varchar(50) DEFAULT NULL COMMENT '下级指定评分人类型',
  `peer_user_value` varchar(50) DEFAULT NULL COMMENT '下级指定人类型id',
  `peer_user_name` varchar(50) DEFAULT NULL COMMENT '同级评指定人姓名',
  `sub_user_name` varchar(50) DEFAULT NULL COMMENT '下级评指定人姓名',
  `peer_score_flag` varchar(50) DEFAULT NULL COMMENT '同级互评开关',
  `peer_score_rule` varchar(50) DEFAULT NULL COMMENT '同级互评规则',
  `appoint_score_flag` varchar(10) DEFAULT NULL COMMENT '是否开启指定评分',
  `appoint_score_weight` decimal(10,2) DEFAULT NULL COMMENT '指定评分权重',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`),
  KEY `k_templ_base_id` (`templ_base_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='绩效模板流程-考核评价'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_templ_evaluate_affirm   */
/******************************************/
 drop table if exists `perf_templ_evaluate_affirm`;
 CREATE TABLE `perf_templ_evaluate_affirm`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `templ_base_id` varchar(50) DEFAULT NULL COMMENT '模板基础id',
  `confirm_flag` varchar(10) DEFAULT NULL COMMENT '是否需要被考核人确认',
  `modify_flag` varchar(10) DEFAULT NULL COMMENT '是否允许被考核人修改',
  `modify_audit_flag` varchar(10) DEFAULT NULL COMMENT '修改后是否需要审核',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `is_deleted` varchar(10) DEFAULT NULL,
  `new_flag` varchar(10) DEFAULT NULL COMMENT '是否改版后的数据',
  `no_change_skip_flag` varchar(10) DEFAULT NULL COMMENT '第一位责任人未修改考核任务，则跳过后续的确认流程',
  `vacancy_approver_type` varchar(50) DEFAULT NULL COMMENT '责任人空缺时，指派给更上级superior，跳过skip',
  `multiple_reviewers_type` varchar(30) DEFAULT NULL COMMENT '多个责任人或签还是会签',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `open_confirm_l_t` int(11) DEFAULT '0' COMMENT '指标确认限时的开关',
  `confirm_l_t_day` int(11) DEFAULT '0' COMMENT '指标确认限时天数',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`),
  KEY `k_templ_base_id` (`templ_base_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='绩效模板流程-确认考核任务'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_templ_evaluate_audit   */
/******************************************/
 drop table if exists `perf_templ_evaluate_audit`;
 CREATE TABLE `perf_templ_evaluate_audit`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `temp_base_id` varchar(50) DEFAULT NULL COMMENT '模板基础id',
  `emp_id` varchar(50) DEFAULT NULL COMMENT '被考核员工id',
  `kpi_item_id` varchar(50) DEFAULT NULL COMMENT '指标id',
  `scene` varchar(30) DEFAULT NULL COMMENT '考核场景（确认任务/上级评分/结果审批）',
  `approval_order` int(11) DEFAULT NULL COMMENT '审批顺序（从1开始递增）',
  `approver_type` varchar(30) DEFAULT NULL COMMENT '审批人类型（指定级别主管/指定人员）',
  `approver_info` varchar(200) DEFAULT NULL COMMENT '指定对象id（级别）',
  `superior_score_weight` decimal(10,2) DEFAULT NULL COMMENT '评分人权重',
  `multiple_reviewers_type` varchar(30) DEFAULT NULL COMMENT '多个责任人或签还是会签',
  `transfer_flag` varchar(10) DEFAULT NULL COMMENT '是否可转交',
  `vacancy_approver_type` varchar(30) DEFAULT NULL COMMENT '审批人空缺时指定人类型（管理员/指定人员）',
  `vacancy_approver_info` varchar(200) DEFAULT NULL COMMENT '审批人空缺时指定人id',
  `score_rule` varchar(50) DEFAULT NULL COMMENT '评分规则，total对考核任务打总分，item对每个指标打分',
  `status` varchar(30) DEFAULT NULL COMMENT '当前节点状态',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `is_deleted` varchar(10) DEFAULT NULL,
  `is_default` varchar(10) DEFAULT NULL COMMENT '是否默认配置，true/false',
  `kpi_type_id` varchar(50) DEFAULT NULL COMMENT '指标类id',
  `modify_flag` varchar(10) DEFAULT NULL COMMENT '是否可以修改',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `approver_emp_name` varchar(50) DEFAULT NULL COMMENT '责任人名称（包含主管、角色）',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`),
  KEY `k_kpi_item_id` (`kpi_item_id`),
  KEY `k_temp_base_id` (`temp_base_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='绩效模板流程-审批设置'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_templ_evaluate_execute   */
/******************************************/
 drop table if exists `perf_templ_evaluate_execute`;
 CREATE TABLE `perf_templ_evaluate_execute`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `templ_base_id` varchar(50) DEFAULT NULL COMMENT '模板基础id',
  `change_flag` varchar(10) DEFAULT NULL COMMENT '是否允许变更',
  `change_user` varchar(50) DEFAULT NULL COMMENT '变更责任人：被考核人taskEmp, 上级主管superior，发起人和主管理员admin',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `is_deleted` varchar(10) DEFAULT NULL,
  `vacancy_approver_type` varchar(50) DEFAULT NULL COMMENT '责任人空缺时，指派给更上级superior，跳过skip',
  `multiple_reviewers_type` varchar(30) DEFAULT NULL COMMENT '多个责任人或签还是会签',
  `audit_open` int(11) DEFAULT '1' COMMENT '执行阶段变更指标是否打开审核流程,默认打开 1=开,0=关 ',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`),
  KEY `company_id` (`company_id`) USING BTREE,
  KEY `k_templ_base_id` (`templ_base_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_templ_evaluate_initiate   */
/******************************************/
 drop table if exists `perf_templ_evaluate_initiate`;
 CREATE TABLE `perf_templ_evaluate_initiate`  (
  `id` varchar(50) NOT NULL COMMENT 'id',
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `templ_base_id` varchar(50) DEFAULT NULL COMMENT '模板基础id',
  `initiator_type` varchar(50) DEFAULT NULL COMMENT '发起人类型',
  `initiator_list` text COMMENT '发起人用户id数组',
  `examine_obj_data` text COMMENT '被考核员工来源JSON对象；[{"obj_type":"对象类型(部门/角色/岗位/指定员工)",“objItems”:[{"objId":"对象id","objName":"对象名称"}]}]',
  `exclude_emp_data` text COMMENT '排查员工JSON对象；[{"empId":"员工id","empName":"员工姓名"}]',
  `enable_add_reduce` varchar(10) DEFAULT NULL COMMENT '发起任务时是否可以增减指标',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `is_deleted` varchar(10) DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`),
  KEY `k_temp_base_id` (`templ_base_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='绩效模板考核流程-发起考核'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_templ_formula_field   */
/******************************************/
 drop table if exists `perf_templ_formula_field`;
 CREATE TABLE `perf_templ_formula_field`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `company_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `templ_base_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '模板id',
  `kpi_item_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '指标id',
  `company_field_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司公式字段id',
  `formula_field_name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公式里的字段名称',
  `formula_field_value` decimal(18,2) DEFAULT NULL COMMENT '公式里的字段值',
  `is_deleted` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_templ_item_evaluate   */
/******************************************/
 drop table if exists `perf_templ_item_evaluate`;
 CREATE TABLE `perf_templ_item_evaluate`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `company_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司id',
  `templ_base_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '模板基础id',
  `kpi_item_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '指标项id',
  `self_score_flag` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否自评',
  `self_score_view_rule` varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '被考核人查看评分规则JSON',
  `self_score_weight` decimal(10,2) DEFAULT NULL COMMENT '自评权重',
  `mutual_score_flag` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否互评',
  `mutual_score_attend_rule` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '互评参与规则',
  `mutual_score_anonymous` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '互评人姓名是否匿名',
  `mutual_score_vacancy` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '互评人空缺时规则',
  `mutual_score_view_rule` varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '互评人查看评分规则JSON',
  `peer_score_weight` decimal(10,2) DEFAULT NULL COMMENT '同级互评权重',
  `sub_score_weight` decimal(10,2) DEFAULT NULL COMMENT '下级互评权重',
  `superior_score_flag` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否上级评',
  `superior_score_view_rule` varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '上级评人查看评分规则JSON',
  `superior_score_weight` decimal(10,2) DEFAULT NULL COMMENT '上级评权重',
  `superior_score_vacancy` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '上级评分人空缺时规则',
  `appoint_score_flag` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否指定评分',
  `appoint_score_weight` decimal(10,2) DEFAULT NULL COMMENT '指定评分权重',
  `created_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `is_deleted` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL,
  `mutual_user_type` varchar(30) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '设置互评人类型，all:为所有被考核人设置相同的互评人; user: 为每位被考核人分别设置互评人',
  `kpi_type_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '指标类id',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `mutual_user_value` varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '自定义互评设置人',
  `sub_score_flag` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '自定义下级互评开关',
  `sub_user_type` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '下级指定评分人类型',
  `sub_user_value` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '下级指定人类型id',
  `peer_user_type` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '下级指定评分人类型',
  `peer_user_value` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '下级指定人类型id',
  `peer_user_name` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '同级评指定人姓名',
  `sub_user_name` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '下级评指定人姓名',
  `peer_score_flag` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '同级互评开关',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`),
  KEY `k_template_base_id` (`templ_base_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_templ_item_point_rule   */
/******************************************/
 drop table if exists `perf_templ_item_point_rule`;
 CREATE TABLE `perf_templ_item_point_rule`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL,
  `templ_base_id` varchar(50) DEFAULT NULL,
  `kpi_type_id` varchar(50) DEFAULT NULL,
  `kpi_item_id` varchar(50) DEFAULT NULL,
  `rule` varchar(30) DEFAULT NULL COMMENT '积分规则',
  `is_deleted` varchar(10) DEFAULT NULL,
  `created_user` varchar(50) DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`),
  KEY `company_id` (`company_id`),
  KEY `k_templ_base_id` (`templ_base_id`) USING BTREE,
  KEY `k_item_id` (`kpi_item_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_templ_kpi_item   */
/******************************************/
 drop table if exists `perf_templ_kpi_item`;
 CREATE TABLE `perf_templ_kpi_item`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `templ_base_id` varchar(50) DEFAULT NULL COMMENT '模板基础id',
  `kpi_type_id` varchar(50) DEFAULT NULL COMMENT '指标类id',
  `kpi_item_id` varchar(50) DEFAULT NULL,
  `item_type` varchar(20) DEFAULT NULL COMMENT '指标项类型（量化/非量化）',
  `item_value` decimal(18,2) DEFAULT NULL COMMENT '指标项目标值',
  `item_unit` varchar(20) DEFAULT NULL COMMENT '目标值单位',
  `parent_kpi_type_id` varchar(50) DEFAULT NULL COMMENT '上级指标项',
  `item_rule` text COMMENT '考核规则',
  `scoring_rule` text COMMENT '计分规则',
  `result_input_type` varchar(50) DEFAULT NULL COMMENT '指标结果录入类型（被考核人/指定员工/无需录入）',
  `result_input_user_id` text COMMENT '指定结果录入人id',
  `scorer_type` varchar(50) DEFAULT NULL COMMENT '指标评分人类型（按评分流程/指定员工/指定主管）',
  `scorer_obj_id` text COMMENT '指定评分人json串',
  `item_weight` decimal(10,2) DEFAULT NULL COMMENT '指标项权重',
  `is_deleted` varchar(10) DEFAULT NULL COMMENT '是否删除',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `multiple_reviewers_type` varchar(50) DEFAULT NULL COMMENT '多人审核时，and会签，or或签',
  `plus_limit` decimal(10,2) DEFAULT NULL COMMENT '加分上限',
  `subtract_limit` decimal(10,2) DEFAULT NULL COMMENT '减分上限',
  `order` int(11) DEFAULT NULL COMMENT '排序，数字小的排前面',
  `item_formula` text COMMENT '指标计算公式',
  `threshold_json` text COMMENT '量化指标阈值设置json',
  `formula_condition` text COMMENT '公式条件',
  `item_field_json` text COMMENT '指标关联的阈值字段',
  `kpi_item_name` text COMMENT '指标项名称',
  `item_score_value` varchar(500) DEFAULT NULL COMMENT '指标评分分值',
  `input_format` varchar(50) DEFAULT NULL COMMENT '录入格式',
  `show_target_value` varchar(10) DEFAULT NULL COMMENT '是否展示目标值',
  `must_result_input` tinyint(1) DEFAULT '0' COMMENT '指标完成值是否必需录入',
  `show_finish_bar` int(11) DEFAULT '1' COMMENT '完成度进度条 默认开启 1=显示,0=不显示',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `is_new_emp` int(11) DEFAULT '0' COMMENT '是否新人培训指标',
  `manager_level` varchar(10) DEFAULT NULL COMMENT '录入主管等级',
  `item_full_score_cfg` varchar(10) DEFAULT 'false' COMMENT '自动计算指标得分满分值',
  `result_input_emp_name` varchar(128) DEFAULT NULL COMMENT '录入人姓名',
  `plus_sub_interval` varchar(50) DEFAULT NULL COMMENT '模板指标加减分上限',
  `item_custom_field_json` text COMMENT '指标自定义字段json',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`),
  KEY `k_templ_base_id` (`templ_base_id`) USING BTREE,
  KEY `k_kpi_item_id` (`kpi_item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='绩效模板指标项'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_templ_kpi_type   */
/******************************************/
 drop table if exists `perf_templ_kpi_type`;
 CREATE TABLE `perf_templ_kpi_type`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `templ_base_id` varchar(50) DEFAULT NULL COMMENT '模板基础id',
  `type_id` varchar(50) DEFAULT NULL COMMENT '指标类id',
  `type_name` varchar(200) DEFAULT NULL COMMENT '指标类名称',
  `type_weight` decimal(10,2) DEFAULT NULL COMMENT '指标类权重',
  `is_deleted` varchar(10) DEFAULT NULL COMMENT '是否删除',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `classify` varchar(50) DEFAULT NULL COMMENT '指标类别，为空表示已有类别，custom自定义/plus加分项/subtract减分项',
  `max_extra_score` decimal(10,2) DEFAULT NULL COMMENT '加减分上限',
  `order` int(11) DEFAULT NULL COMMENT '排序，数字小的排前面',
  `is_type_locked` varchar(100) DEFAULT NULL COMMENT '类别锁定类型',
  `is_okr` varchar(10) DEFAULT NULL COMMENT '是否OKR类别，true/false',
  `reserve_okr_weight` decimal(10,2) DEFAULT NULL COMMENT '预留OKR权重',
  `is_empty_type` varchar(10) DEFAULT NULL COMMENT '是否空类别',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `item_limit_cnt` varchar(50) DEFAULT NULL COMMENT '类别指标数量最大最小限制',
  `open_okr_score` int(11) DEFAULT '0' COMMENT '指标评分使用的是okr的分数',
  `plus_sub_interval` varchar(50) DEFAULT NULL COMMENT '模板类别加减分上限',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`),
  KEY `k_templ_base_id` (`templ_base_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='绩效模板指标类'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = perf_templ_org   */
/******************************************/
 drop table if exists `perf_templ_org`;
 CREATE TABLE `perf_templ_org`  (
  `id` varchar(50) NOT NULL,
  `company_id` varchar(50) DEFAULT NULL,
  `templ_base_id` varchar(50) DEFAULT NULL COMMENT '模板id',
  `org_id` varchar(50) DEFAULT NULL COMMENT '部门id',
  `is_deleted` varchar(10) DEFAULT NULL COMMENT '是否删除',
  `created_user` varchar(50) DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = ref_eval   */
/******************************************/
 drop table if exists `ref_eval`;
 CREATE TABLE `ref_eval`  (
  `company_id` varchar(50) NOT NULL COMMENT '公司id',
  `main_eval_id` varchar(50) NOT NULL COMMENT '主绩效id=task_user.task_user_id',
  `ref_eval_id` varchar(50) NOT NULL COMMENT '关联的任务id=task_user.task_user_id',
  `weight` decimal(10,3) NOT NULL COMMENT '权重',
  `score` decimal(10,3) DEFAULT NULL COMMENT '被关联的任务分数',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  KEY `ref_eval_idx_companyId_mainEvalId` (`company_id`,`main_eval_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='关联的绩效任务'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = report_weight_setting   */
/******************************************/
 drop table if exists `report_weight_setting`;
 CREATE TABLE `report_weight_setting`  (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `company_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `year` int(11) DEFAULT NULL COMMENT '年份',
  `cycle_type` varchar(50) DEFAULT NULL COMMENT '考核周期类型',
  `month` int(11) DEFAULT NULL COMMENT '月份',
  `weight` decimal(10,2) DEFAULT NULL COMMENT '权重',
  `is_deleted` varchar(10) DEFAULT NULL,
  `created_user` varchar(50) DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `quarter` int(11) DEFAULT NULL COMMENT '季度',
  `version` int(11) DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = role   */
/******************************************/
 drop table if exists `role`;
 CREATE TABLE `role`  (
  `id` varchar(50) NOT NULL DEFAULT '' COMMENT '主键ID',
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司ID',
  `role_name` varchar(255) DEFAULT NULL COMMENT '角色名称',
  `role_desc` varchar(255) DEFAULT NULL COMMENT '角色描述',
  `role_type` varchar(50) DEFAULT 'custom' COMMENT '角色类型: ding-钉钉，custom-自定义',
  `ding_role_id` varchar(50) DEFAULT NULL,
  `group_id` varchar(50) DEFAULT NULL COMMENT '所属角色组id',
  `is_group` varchar(10) DEFAULT NULL COMMENT '是否为角色组：true/false',
  `is_deleted` varchar(10) DEFAULT NULL COMMENT '是否有效: false-否,true-是',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改者',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `perf_evaluation_level` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`),
  KEY `idx_ding_role_id` (`ding_role_id`),
  KEY `idx_companyId_roleId` (`company_id`,`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = role_bak1228   */
/******************************************/
 drop table if exists `role_bak1228`;
 CREATE TABLE `role_bak1228`  (
  `id` varchar(50) NOT NULL DEFAULT '' COMMENT '主键ID',
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司ID',
  `role_name` varchar(255) DEFAULT NULL COMMENT '角色名称',
  `role_desc` varchar(255) DEFAULT NULL COMMENT '角色描述',
  `role_type` varchar(50) DEFAULT 'custom' COMMENT '角色类型: ding-钉钉，custom-自定义',
  `ding_role_id` varchar(50) DEFAULT NULL,
  `group_id` varchar(50) DEFAULT NULL COMMENT '所属角色组id',
  `is_group` varchar(10) DEFAULT NULL COMMENT '是否为角色组：true/false',
  `is_deleted` varchar(10) DEFAULT NULL COMMENT '是否有效: false-否,true-是',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改者',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_company_id_active` (`is_deleted`,`company_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = role_ref_emp   */
/******************************************/
 drop table if exists `role_ref_emp`;
 CREATE TABLE `role_ref_emp`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `company_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `role_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '角色id',
  `emp_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '员工id',
  `status` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '状态：valid-启用，invalid-禁用',
  `is_deleted` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否删除：true/false',
  `created_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_company_id` (`company_id`),
  KEY `emp_role` (`emp_id`) USING BTREE,
  KEY `k_company_id_role_id` (`role_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='角色与员工关联关系'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = schedule_temp   */
/******************************************/
 drop table if exists `schedule_temp`;
 CREATE TABLE `schedule_temp`  (
  `task_key` varchar(150) NOT NULL,
  `company_id` varchar(255) NOT NULL,
  `batch_no` bigint(20) NOT NULL COMMENT '批次号,删除用',
  `type` varchar(255) NOT NULL,
  `created_time` datetime DEFAULT NULL COMMENT '记录创建时间',
  PRIMARY KEY (`task_key`),
  KEY `batch_no_key` (`batch_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='定时任务并发处理'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = score_range   */
/******************************************/
 drop table if exists `score_range`;
 CREATE TABLE `score_range`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `company_id` varchar(50) NOT NULL,
  `score_rule_id` int(11) NOT NULL,
  `step_id` int(11) NOT NULL,
  `min` decimal(10,2) NOT NULL COMMENT '最小分值含有',
  `max` decimal(10,2) NOT NULL COMMENT '最大分值',
  `created_time` date DEFAULT NULL,
  `created_user` varchar(50) DEFAULT NULL,
  `updated_time` date DEFAULT NULL,
  `updated_user` varchar(50) DEFAULT NULL,
  `is_deleted` varchar(6) DEFAULT 'false',
  `version` int(11) DEFAULT '0',
  `min_append_equal` int(11) DEFAULT '0' COMMENT '0拼在max上，1拼在min上',
  PRIMARY KEY (`id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_score_rule_id` (`score_rule_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3688471 DEFAULT CHARSET=utf8mb4
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = score_rule   */
/******************************************/
 drop table if exists `score_rule`;
 CREATE TABLE `score_rule`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) DEFAULT NULL COMMENT '分值规则名',
  `system` tinyint(1) DEFAULT '0' COMMENT '是否系统规则,旧数据生成',
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `eg_orgs` longtext COMMENT '示例部门用于显示,最多三个名字',
  `created_time` datetime DEFAULT NULL,
  `created_user` varchar(50) DEFAULT '',
  `updated_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) DEFAULT '',
  `is_deleted` varchar(6) DEFAULT 'false',
  `version` int(11) DEFAULT '0',
  `exclude_emps` text COMMENT '创建等级规则时，排除的人',
  PRIMARY KEY (`id`),
  KEY `idx_company_id` (`company_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3688466 DEFAULT CHARSET=utf8mb4
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = sequence   */
/******************************************/
 drop table if exists `sequence`;
 CREATE TABLE `sequence`  (
  `name` varchar(50) NOT NULL,
  `current_value` int(11) NOT NULL,
  `increment` int(11) NOT NULL DEFAULT '100',
  PRIMARY KEY (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = sv_in_service   */
/******************************************/
 drop table if exists `sv_in_service`;
 CREATE TABLE `sv_in_service`  (
  `id` varchar(100) DEFAULT NULL,
  `service_id` varchar(100) DEFAULT NULL COMMENT '服务id',
  `account_id` varchar(100) DEFAULT NULL COMMENT '账号id',
  `company_id` varchar(100) DEFAULT NULL COMMENT '公司id',
  `service_name` varchar(255) DEFAULT NULL COMMENT '服务名',
  `end_time` datetime DEFAULT NULL COMMENT '服务失效时间点',
  `start_time` datetime DEFAULT NULL COMMENT '服务生效时间点',
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `run_status` varchar(10) DEFAULT NULL COMMENT '暂停-pause,启动-start',
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='公司相关服务'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = system_admin_set   */
/******************************************/
 drop table if exists `system_admin_set`;
 CREATE TABLE `system_admin_set`  (
  `id` varchar(50) NOT NULL COMMENT 'id',
  `company_id` varchar(50) DEFAULT NULL COMMENT '公司id',
  `emp_id` varchar(50) DEFAULT NULL COMMENT '员工id',
  `status` varchar(10) DEFAULT NULL COMMENT 'valid：有效，invalid：无效',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  `menu_purview` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '菜单权限',
  `manage_purview` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '管理权限',
  `template_purview` text,
  `admin_type` varchar(5) DEFAULT NULL COMMENT '主：main;子：child',
  `created_user` varchar(50) DEFAULT NULL,
  `updated_user` varchar(50) DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本字段',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `perm_sas_eid` (`emp_id`),
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='系统管理员设置'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = system_iteration   */
/******************************************/
 drop table if exists `system_iteration`;
 CREATE TABLE `system_iteration`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `version` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '版本',
  `content` varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '迭代内容',
  `iterate_date` date DEFAULT NULL COMMENT '迭代日期',
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='系统迭代信息'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = sys_dic_info   */
/******************************************/
 drop table if exists `sys_dic_info`;
 CREATE TABLE `sys_dic_info`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '字典id',
  `type_id` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '字典类型',
  `code` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '字典编码',
  `desc` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '字典描述',
  `is_deleted` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否删除：true/false',
  `is_base` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否为基础数据(true/false)，基础数据会在创建公司时作为公司初始数据',
  `created_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='系统字典'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = sys_dic_type   */
/******************************************/
 drop table if exists `sys_dic_type`;
 CREATE TABLE `sys_dic_type`  (
  `id` varchar(50) DEFAULT NULL COMMENT 'id',
  `type` varchar(50) DEFAULT NULL COMMENT '类型编码',
  `name` varchar(100) DEFAULT NULL COMMENT '类型名称',
  `is_deleted` varchar(10) DEFAULT NULL COMMENT '是否删除',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='系统字典类型'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = sys_init_industry   */
/******************************************/
 drop table if exists `sys_init_industry`;
 CREATE TABLE `sys_init_industry`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `industry` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '行业名称',
  `type` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '所属分类',
  `bg_url` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '背景图片',
  `is_deleted` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = sys_init_temp   */
/******************************************/
 drop table if exists `sys_init_temp`;
 CREATE TABLE `sys_init_temp`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `industry_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '行业id',
  `temp_name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '模板名称',
  `is_deleted` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = sys_init_temp_rel_item   */
/******************************************/
 drop table if exists `sys_init_temp_rel_item`;
 CREATE TABLE `sys_init_temp_rel_item`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `temp_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '模板id',
  `item_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '指标id',
  `item_weight` decimal(10,2) DEFAULT NULL COMMENT '模板里面的指标权重',
  `is_deleted` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = sys_kpi_item   */
/******************************************/
 drop table if exists `sys_kpi_item`;
 CREATE TABLE `sys_kpi_item`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `item_name` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '指标项名称',
  `scoring_rule` text COLLATE utf8mb4_bin COMMENT '计分规则',
  `item_rule` text COLLATE utf8mb4_bin COMMENT '考核规则',
  `is_measurable` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否可量化',
  `item_unit` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '目标值单位',
  `industry` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '行业',
  `position` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '岗位',
  `init_flag` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否是初始化指标',
  `init_weight` decimal(10,2) DEFAULT NULL COMMENT '初始化指标权重',
  `init_temp_flag` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否初始化到模板',
  `is_deleted` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_user` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_user` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='系统指标库'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = sys_params   */
/******************************************/
 drop table if exists `sys_params`;
 CREATE TABLE `sys_params`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `param_name` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '参数名称',
  `param_value` varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '参数值',
  `expires_in` int(11) DEFAULT NULL COMMENT '有效时间，单位：秒',
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = task_fix_finish_status   */
/******************************************/
 drop table if exists `task_fix_finish_status`;
 CREATE TABLE `task_fix_finish_status`  (
  `task_user_id` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='perf_evaluate_task_user完成值录入情况字段维护临时表'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = temp_company   */
/******************************************/
 drop table if exists `temp_company`;
 CREATE TABLE `temp_company`  (
  `id` varchar(40) NOT NULL DEFAULT '0' COMMENT '公司id',
  `name` varchar(512) DEFAULT NULL COMMENT '公司名称',
  `company_type` varchar(10) DEFAULT NULL COMMENT '公司类型（review:待审核-默认不需要审核,simple-极简版，enterprise-企业版）',
  `app_version` varchar(174) DEFAULT '1009002' COMMENT '当前企业的应用版本',
  `open_emp_cnt` int(11) DEFAULT '0' COMMENT '启用人数',
  `emp_limit` int(11) DEFAULT '0' COMMENT '人数上限',
  `login_cnt` int(11) DEFAULT '0' COMMENT '近1个季度登录人数（去重）',
  `task_user_cnt` int(11) DEFAULT '0' COMMENT '近1个季度考核人次',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `perm_end_time` datetime DEFAULT NULL COMMENT '到期时间',
  UNIQUE KEY `uk_id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='付费用户临时表,用后删除'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = tem_okr_score   */
/******************************************/
 drop table if exists `tem_okr_score`;
 CREATE TABLE `tem_okr_score`  (
  `action_id` varchar(100) DEFAULT NULL,
  `okr_score` decimal(10,3) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = tem_role_company_msg_center   */
/******************************************/
 drop table if exists `tem_role_company_msg_center`;
 CREATE TABLE `tem_role_company_msg_center`  (
  `task_audit_id` varchar(50) NOT NULL COMMENT 'task user 表的id',
  `task_id` varchar(50) NOT NULL COMMENT 'task user 表的id',
  `emp_id` varchar(50) NOT NULL COMMENT 'task user 表的id',
  `scorer_id` varchar(50) NOT NULL COMMENT 'task user 表的id',
  `role_emp_id` varchar(50) NOT NULL COMMENT 'task user 表的id',
  `company_id` varchar(50) NOT NULL COMMENT 'sc.task_audit_id',
  `task_user_id` varchar(50) DEFAULT NULL COMMENT 'sc.task_audit_id',
  `score_result_id` varchar(50) DEFAULT NULL COMMENT '指标类名称',
  KEY `idx_task_user_id` (`task_user_id`),
  KEY `idx_company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='临时表,用完删除'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = tenant_sys_conf   */
/******************************************/
 drop table if exists `tenant_sys_conf`;
 CREATE TABLE `tenant_sys_conf`  (
  `company_id` varchar(50) NOT NULL COMMENT '企业id',
  `open` int(11) DEFAULT '1' COMMENT '是否开放1= 开放, 0 = 关闭',
  `conf_code` varchar(50) NOT NULL COMMENT '配置编号,用于在场景中查询配置,确定后不可变',
  `conf_content` varchar(256) NOT NULL COMMENT '配置内容',
  `conf_desc` varchar(50) DEFAULT '' COMMENT '配置简述',
  `created_user` varchar(50) DEFAULT '' COMMENT '创建人',
  `updated_user` varchar(50) DEFAULT '' COMMENT '变更人',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime DEFAULT NULL COMMENT '变更时间',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  UNIQUE KEY `tenantSysConf_companyId_conCode` (`company_id`,`conf_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户级的应用配置,可运管后台控制,用于有些企业定制功能时使用'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = test   */
/******************************************/
 drop table if exists `test`;
 CREATE TABLE `test`  (
  `name` varchar(50) NOT NULL,
  PRIMARY KEY (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = tg_tmp   */
/******************************************/
 drop table if exists `tg_tmp`;
 CREATE TABLE `tg_tmp`  (
  `company_id` varchar(200) DEFAULT NULL,
  `task_id` varchar(500) DEFAULT NULL,
  `emp_id` varchar(500) DEFAULT NULL,
  `kpi_type_id` varchar(500) DEFAULT NULL,
  `kpi_item_id` varchar(500) DEFAULT NULL,
  `scorer_type` varchar(500) DEFAULT NULL,
  `scorer_id` varchar(500) DEFAULT NULL,
  `score` int(1) DEFAULT NULL,
  `del_id` varchar(200) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = third_api_info   */
/******************************************/
 drop table if exists `third_api_info`;
 CREATE TABLE `third_api_info`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `access_key` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '密钥id',
  `secret` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '密钥secret',
  `api_list` text COLLATE utf8mb4_bin COMMENT '访问方法',
  `corp_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司id',
  `withe_ips` text COLLATE utf8mb4_bin COMMENT 'ip白名单，null/""：无',
  `start_time` datetime DEFAULT NULL COMMENT '有效开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '有效结束时间',
  `status` tinyint(1) DEFAULT NULL COMMENT '1/0',
  `remark` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '方法备注',
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = third_api_invoke_log   */
/******************************************/
 drop table if exists `third_api_invoke_log`;
 CREATE TABLE `third_api_invoke_log`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `api_key` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '密钥id',
  `ip` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '访问ip',
  `api_name` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '接口名称',
  `api_path` varchar(300) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '接口路径',
  `api_exception` varchar(300) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '访问异常',
  `times` int(10) DEFAULT NULL COMMENT '访问时长',
  `status` tinyint(1) DEFAULT NULL COMMENT '调用结果',
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = third_platform_setting   */
/******************************************/
 drop table if exists `third_platform_setting`;
 CREATE TABLE `third_platform_setting`  (
  `id` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `company_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `third_name` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '第三方名称: smart_work-智能人事',
  `status` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '状态：open-开启对接，close-关闭对接',
  `third_doc_url` varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '第三方对接文档l链接',
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='第三方平台设置'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = tip   */
/******************************************/
 drop table if exists `tip`;
 CREATE TABLE `tip`  (
  `id` int(11) NOT NULL COMMENT '格式化的key',
  `company_id` varchar(50) DEFAULT '0' COMMENT '公司id=0表示系统的,其它是企业的',
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统引导提示'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = tip_read   */
/******************************************/
 drop table if exists `tip_read`;
 CREATE TABLE `tip_read`  (
  `tip_id` bigint(20) NOT NULL COMMENT 'tipId',
  `company_id` varchar(50) DEFAULT '0' COMMENT '公司id=0表示系统的,其它是企业的',
  `emp_id` varchar(50) DEFAULT '0' COMMENT '员工id',
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `is_deleted` varchar(50) DEFAULT 'false' COMMENT 'is_deleted',
  `updated_user` varchar(50) DEFAULT 'false' COMMENT 'is_deleted',
  `created_user` varchar(50) DEFAULT 'false' COMMENT 'is_deleted',
  `version` int(11) DEFAULT '0' COMMENT '版本号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='已读引导'
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = tmp_help_index   */
/******************************************/
 drop table if exists `tmp_help_index`;
 CREATE TABLE `tmp_help_index`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `o_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=32 DEFAULT CHARSET=utf8mb4
;

/******************************************/
/*   DatabaseName = company_perf   */
/*   TableName = train_appointment   */
/******************************************/
 drop table if exists `train_appointment`;
 CREATE TABLE `train_appointment`  (
  `id` varchar(50) NOT NULL COMMENT '预约id',
  `company_id` varchar(50) NOT NULL COMMENT '公司id',
  `company` varchar(256) NOT NULL COMMENT '公司名',
  `emp_name` varchar(50) NOT NULL COMMENT '外键员工:emloyee_info.name',
  `emp_id` varchar(50) NOT NULL COMMENT '外键员工id:emloyee_info.employee_id',
  `phone` varchar(50) NOT NULL COMMENT '手机号',
  `is_deleted` varchar(5) DEFAULT 'false' COMMENT '是否删除',
  `created_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  KEY `idx_companyId_empId` (`company_id`,`emp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预约演示活动'
;
