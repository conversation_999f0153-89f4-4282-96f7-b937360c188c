insert into sequence(name, current_value, increment) VALUE ('emp_eval_table', 1000000, 100);
# 员工的考核表,一个员工可以有多个考核表
drop table if exists emp_eval_table;
create table emp_eval_table
(
    company_id       varchar(50)            not null comment '公司id',
    id               varchar(50)            not null comment '主键:考核表id' primary key,
    emp_id           varchar(50)            null comment '外键员工id:emloyee_info.employee_id',
    is_default       int          default 0 comment '是否员工的默认考核表',
    rule_name        varchar(250) default '' comment '考核表名字',
    evaluate_type    varchar(10)            not null comment '评分流程类型 ： simple-简易流程，360-360°考核流程，custom-自定义流程',
    type_weight_conf varchar(128) default '' comment '指标类别权重 json',
    score_value_conf varchar(128) default '' comment '分值的设定 json',
#     confirm_task     text comment '确认任务 对应模板 templAffirmJson:PerfTemplEvaluateAffirm',
#     edit_exe_indi    text comment '执行中修改指标  对应模板 templExecuteJson:PerfTemplEvaluateExecute',
#     enter_score      varchar(128) default '' comment '启动评分配置 ,多字段合并',
    s3_self_rater    varchar(128) default '' comment '360或简易 自评人',
    s3_peer_rater    text comment '同级互评人 json',
    s3_sub_rater     text comment '同级互评人 json',
    s3_super_rater   text comment '360或简易 上级人 json',
    score_view       text                   null comment '可见范围配置 json',
#     audit_result     text comment '结果校准,从templEvaluateJson:PerfTemplEvaluate 模板评价配置json 单独出来的',
#     confirm_result   varchar(128) comment '结果确认,多个字段合并',
#     publish_result   text comment '结果公示,多个字段合并',
#     appeal_conf      varchar(128) comment '结果申诉,多个字段合并',
#     level_group_id   varchar(50)  default '' comment '关联哪个等级组/perf_evaluation_level 的level_group_id',
    index_raters     text comment '评分人索引,用于员工列表的显示,编辑时更新索引',
    indicator_cnt    int          default 0 null comment '指标数统计',
#     score_conf       varchar(225)           null comment '评分设置',
    comment_conf     varchar(225)           null comment '评语与总结配置',
    is_deleted       varchar(5)   default 'false' comment '是否删除',
    created_user     varchar(50)            null comment '创建用户',
    created_time     datetime               null comment '创建时间',
    updated_user     varchar(50)            null comment '修改用户',
    updated_time     datetime               null comment '修改时间',
    version          int          default 0 null comment '版本号',
    key idx_companyId_empId (company_id, emp_id)
) comment '员工的考核表,一个员工可以有多个考核表' charset = utf8mb4;

-- 员工的考核表-指标类别
drop table if exists emp_table_kpi_type;
create table emp_table_kpi_type
(
    table_id           varchar(50)           not null comment '考核表的id,emp_eval_table.id',
    company_id         varchar(50)           not null comment '公司id',
    kpi_type_id        varchar(50) comment '指标类id',
    kpi_type_name      varchar(200)          null comment '指标类名称',
    kpi_type_weight    decimal(10, 2)        null comment '指标类权重',

    is_okr             varchar(10)           null comment '是否OKR类别，true/false',
    type_order         int(10)               null comment '类别排序',
    reserve_okr_weight decimal(10, 2)        null comment '预留OKR权重',
    kpi_type_classify  varchar(30)           null comment '指标类别，为空表示已有类别，custom自定义/plus加分项/subtract减分项',
    max_extra_score    decimal(10, 2)        null comment '本类别最大加减分上限',
    import_okr_flag    varchar(10)           null comment '是否已导入OKR',
    locked_items       varchar(100)          null comment 'is_type_locked 类别锁定类型  ["typeWeight","addIndex","modifyIndex","deleteIndex"]',
    item_limit_cnt     VARCHAR(50) COMMENT '类别指标数量最大最小限制',
    is_deleted         varchar(10) default 'false' comment '是否删除',
    created_user       varchar(50)           null comment '创建用户',
    created_time       datetime              null comment '创建时间',
    updated_user       varchar(50)           null comment '修改用户',
    updated_time       datetime              null comment '修改时间',
    version            int         default 0 null comment '版本号',
    key idx_companyId_tableId (company_id, table_id)
) comment '员工的考核表-指标类别' charset = utf8mb4;


-- 员工的考核表-指标
drop table if exists emp_table_kpi_item;
create table emp_table_kpi_item
(
    table_id                varchar(50)                 not null comment '考核表的id,emp_eval_table.id',
    company_id              varchar(50)                 null comment '公司id',
#     reviewer                  text                        null comment '审核人信息',
    kpi_type_id             varchar(50)                 null comment '指标类id',
    kpi_item_id             varchar(50)                 null comment '指标项id',
    kpi_type_name           varchar(200)                null comment '指标类名称',
    kpi_type_weight         decimal(10, 2)              null comment '指标类权重',
    kpi_item_name           text                        null comment '指标项名称',
    item_target_value       decimal(18, 2)              null comment '指标项目标值',
    item_finish_value       decimal(18, 2)              null comment '指标项完成值',
    item_unit               varchar(20)                 null comment '指标项单位',
    item_weight             decimal(10, 2)              null comment '指标项权重',
    result_input_type       varchar(50)                 null comment '结果录入类型',
    input_emps              text                        null comment '结果录入人id',
    examine_oper_type       varchar(30)                 null comment '被考核人确认指标时操作类型：增删改',

    item_rule               text                        null comment '考核规则',
    scoring_rule            text                        null comment '计分规则',
    scorer_type             varchar(50)                 null comment '指标评分人类型（按评分流程/指定员工/指定主管）',
    scorer_obj_id           text                        null comment '指定评分人json串',
    item_type               varchar(50)                 null comment '指标项类型（量化/非量化）',
    multiple_reviewers_type varchar(50)                 null comment '多人审核时，and会签，or或签',
    kpi_type_classify       varchar(50)                 null comment '指标类别，为空表示已有类别，custom自定义/plus加分项/subtract减分项',
    plus_limit              decimal(10, 2)              null comment '加分上限',
    subtract_limit          decimal(10, 2)              null comment '减分上限',
    max_extra_score         decimal(10, 2)              null comment '本类别最大加减分上限',
    `order`                 int                         null comment '排序，数字小的排前面',
    item_formula            text                        null comment '指标计算公式',
#     item_auto_score           decimal(10, 3)              null comment '指标自动计算后的分数',
    threshold_json          text                        null comment '量化指标阈值设置json',
#     urging_flag               varchar(10)                 null comment '完成值录入催办标识',
#     item_actual_formula       text                        null comment '代入真实值后的计算公式',
#     auto_score_ex_flag        varchar(10)                 null comment '自动算分异常标识',
    formula_condition       text                        null comment '公式条件',
    item_field_json         text                        null comment '指标关联的阈值字段',
    is_type_locked          varchar(100)                null comment '类别锁定类型',
    is_okr                  varchar(10)                 null comment '是否OKR类别，true/false',
    type_order              int(10)                     null comment '类别排序',
#     okr_ref_flag              varchar(20)                 null comment '指标被OKR关联的标记',
    reserve_okr_weight      decimal(10, 2)              null comment '预留OKR权重',
#     points_rule               varchar(30)                 null comment '积分规则',
#     points_num                decimal(10, 2)              null comment '指标积分',
    item_score_value        varchar(500)                null comment '指标评分分值',
    input_format            varchar(50)                 null comment '录入格式',
    work_item_finish_value  varchar(2000)               null comment '工作事项完成情况说明',
    item_plan_flag          varchar(10)                 null comment '是否同步了年度指标计划',
    show_target_value       varchar(10)                 null comment '是否展示目标值',
    must_result_input       tinyint(1)  default 0       null comment '指标完成值是否必需录入',
    show_finish_bar         int         default 1       null comment '完成度进度条 默认开启 1=显示,0=不显示',
    manager_level           varchar(10)                 null comment '录入主管等级',
    item_full_score_cfg     varchar(10) default 'false' null comment '自动计算指标得分满分值',
    item_limit_cnt          varchar(255)                null comment '类别指标数量最大最小限制',
    item_finish_value_text  varchar(2000)               null comment '非量化指标完成值文本录入',
    open_okr_score          int         default 0       null comment '指标评分使用的是okr的分数',
    okr_score               decimal(10, 3)              null comment 'okr的原始分数',
    formula_fields          text                        null comment '公式字段列表',
    is_deleted              varchar(10)                 null comment '是否删除',
    version                 int         default 0       null comment '版本号',
    created_user            varchar(50)                 null comment '创建用户',
    created_time            datetime                    null comment '创建时间',
    updated_user            varchar(50)                 null comment '修改用户',
    updated_time            datetime                    null comment '修改时间',
    key idx_companyId_tableId (company_id, table_id)
) comment '员工考核表--关联指标' charset = utf8mb4;


-- 员工考核表-指标流程
drop table if exists emp_table_item_rule;
create table emp_table_item_rule
(
#     task_id                  varchar(50)    null comment '任务id',
#     task_user_id             varchar(50)    null,
#     self_score_flag          varchar(10)    null comment '是否自评',
#     self_score_view_rule     varchar(500)   null comment '被考核人查看评分规则JSON',
#     self_score_weight        decimal(10, 2) null comment '自评权重',
#     mutual_score_flag        varchar(10)    null comment '是否互评',
#     mutual_score_attend_rule varchar(50)    null comment '互评参与规则',
#     mutual_score_anonymous   varchar(10)    null comment '互评人姓名是否匿名',
#     mutual_score_vacancy     varchar(50)    null comment '互评人空缺时规则',
#     mutual_score_view_rule   varchar(500)   null comment '互评人查看评分规则JSON',
#     peer_score_weight        decimal(10, 2) null comment '同级互评权重',
#     sub_score_weight         decimal(10, 2) null comment '下级互评权重',
#     superior_score_flag      varchar(10)    null comment '是否上级评',
#     superior_score_view_rule varchar(500)   null comment '上级评人查看评分规则JSON',
#     superior_score_weight    decimal(10, 2) null comment '上级评权重',
#     superior_score_vacancy   varchar(50)    null comment '上级评分人空缺时规则',
#     appoint_score_flag       varchar(10)    null comment '是否指定评分',
#     appoint_score_weight     decimal(10, 2) null comment '指定评分权重',

    table_id          varchar(50)   not null comment '考核表的id,emp_eval_table.id',
    kpi_item_id       varchar(50)   null comment '指标项id  emp_table_kpi_item.id',
    company_id        varchar(50)   null comment '公司id',
    kpi_type_id       varchar(50)   null comment '指标类id',
    mutual_user_type  varchar(30)   null comment '设置互评人类型，all:为所有被考核人设置相同的互评人; user: 为每位被考核人分别设置互评人; exam：由被考核人自行指定',
    mutual_user_value text          null comment '自定义互评设置人',
    self_rater        text          null comment ' 自评人',
    mutual_rater      text          null comment '互评人 json',
    super_rater       text          null comment '上级人 json',
    appoint_rater     text          null comment '指定评分人json',
    peer_rater        text          null comment '自定义同级互评人',
    sub_rater         text          null comment '自定义下级互评人',
    created_user      varchar(50)   null comment '创建用户',
    created_time      datetime      null comment '创建时间',
    updated_user      varchar(50)   null comment '修改用户',
    updated_time      datetime      null comment '修改时间',
    is_deleted        varchar(10)   null,
    version           int default 0 null comment '版本号',
    key idx_companyId_tableId (company_id, table_id)
) comment '员工考核表-指标流程' charset = utf8mb4;


# 员工考核表修改记录 新加表
-- auto-generated definition
drop table if exists emp_table_operation;
create table emp_table_operation
(
    id              varchar(50)  not null comment '操作记录id'
        primary key,
    table_id        varchar(50)  not null comment '外键table_id',
    company_id      varchar(50)  not null comment '公司id',
    at_status       varchar(50)  not null comment '修改时所在阶段',
    change_type     int          not null comment '01=1=指标, 10=2=流程,11=3=指标+流程',
    indicator       mediumtext   null comment '指标类修改,type_weight_conf,score_value_conf, _kpi表,_kpi_type表',
    confirm_task    text         null comment '确认任务 对应模板 templAffirmJson:PerfTemplEvaluateAffirm',
    edit_exe_indi   text         null comment '执行中修改指标日志记录  对应模板 templExecuteJson:PerfTemplEvaluateExecute',
    score_conf      text         null comment 'enter_score,s3_self_rater,s3_mutual_rater,s3_super_rater ,score_veiw',
    audit_result    text         null comment '结果校准,从templEvaluateJson:PerfTemplEvaluate 模板评价配置json 单独出来的',
    confirm_result  varchar(512) null comment '结果确认,多个字段合并',
    publish_result  text         null comment '结果公示,多个字段合并',
    appeal_conf     varchar(512) null comment '结果申诉,多个字段合并',
    operation_time  bigint       not null comment '操作时间毫秒ms',
    operator_id     varchar(50)  not null comment '操作人emp_id',
    operator_name   varchar(50)  not null comment '操作人名',
    operator_avatar varchar(225) not null comment '操作人头像',
    created_time    datetime     null comment '记录创建时间',
    admin_type      varchar(25)  null comment '操作人管理员类型/admin/child',
    base_info       text         null comment '基本信息',
    index idx_companyId_tableId (company_id, table_id),
    index idx_operation_time (operation_time)
) comment '员工考核表修改记录' charset = utf8mb4;

