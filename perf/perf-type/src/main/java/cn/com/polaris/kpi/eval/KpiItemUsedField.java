package cn.com.polaris.kpi.eval;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;


/**
 * 考核指标自定义字段使用表
 */
@Setter
@Getter
public class KpiItemUsedField {

    private String fieldId;     //company_kpi_item_custom_field.id
    private String companyId;//公司id
    private String kpiItemId;      //kpi_item_id
    private String taskUserId;      //perf_evaluate_task_user.id
    private String name;            //字段名称【复制于company_kpi_item_custom_field】
    private String value;           //输入值
    private Integer type;       //字段类型（1：输入框  2：开关 3：下拉选 4：复选 5：img）【复制于company_kpi_item_custom_field】
    private Integer req;        //是否必填（0：否  1：是）【复制于company_kpi_item_custom_field】
    private String status;      //指标字段状态（valid：有效，invalid：无效）【复制于company_kpi_item_custom_field】
    private Integer show;       //是否显示（0：否  1：是）
    private Integer sort;       //指标字段排序值
    private Integer adminType;  //1:系统默认字段  0：自定义字段
    private String isDeleted;
    private String createdUser;//创建用户
    private Date createdTime;//创建时间
    private String updatedUser;//修改用户
    private Date updatedTime;//修改时间
    private String fieldUnit;       //字段单位

    public boolean isNum() {
        return type == 2;
    }
}

