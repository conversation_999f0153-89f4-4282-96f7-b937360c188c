package com.perf.www.manager.task;

import cn.hutool.core.collection.CollUtil;
import com.perf.www.common.constant.BusinessConstant;
import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import com.perf.www.common.utils.string.StringTool;
import com.perf.www.domain.entity.company.EmployeeBaseInfoModel;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskScoreResultDo;
import com.polaris.kpi.org.infr.company.dao.CompanyLevelDao;
import com.perf.www.model.perftmp.PerfTemplBaseModel;
import com.perf.www.model.task.*;
import com.perf.www.vo.common.emp.EmployeeSimpleInfoVO;
import com.perf.www.vo.task.PerfEvaluateSaveScoreVO;
import com.perf.www.vo.task.PerfEvaluateTaskScoreResultVO;
import com.perf.www.vo.task.query.EvaluateTaskAuditQueryVO;
import com.perf.www.vo.task.score.EvaLevelGroup;
import com.perf.www.vo.task.score.PerfEvaluateTaskFinalAuditVO;
import com.polaris.sdk.type.TenantId;
import com.quick.common.util.date.DateTimeUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lufei.ibatis.builder.*;
import org.lufei.ibatis.dao.AutoBaseDao;
import org.lufei.ibatis.mapper.PagedList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <h3>此处添加注释</h3>
 * created by Bruce.R on 2020/9/20
 */
@Component
public class PerfEvaluateTaskScoreResultManager {
    @Resource
    private AutoBaseDao autoBaseDao;
    @Autowired
    private CompanyLevelDao levelDao;

    public String insert(PerfEvaluateTaskScoreResultModel resultModel) {
        resultModel.setCreatedTime(null);
        resultModel.setUpdatedTime(null);
        resultModel.setIsDeleted("false");
        autoBaseDao.save(resultModel);
        return resultModel.getId();
    }

    public int batchInsert(List<PerfEvaluateTaskScoreResultModel> resultList) {
        StringBuilder sql = new StringBuilder("INSERT INTO perf_evaluate_task_score_result(id,company_id,task_id,emp_id,org_id,kpi_type_id,kpi_item_id,scorer_type,scorer_id,score,score_weight,score_comment,score_att_url,created_user,created_time,approval_order,transfer_id,reviewers_type," +
                "is_deleted,task_audit_id,modify_flag) VALUES ");
        for (PerfEvaluateTaskScoreResultModel taskScoreResultModel : resultList) {
            sql.append(String.format("('%s','%s','%s','%s','%s','%s','%s','%s','%s',%s,%s,'%s','%s','%s','%s',%s,'%s','%s','false','%s','%s')",
                    taskScoreResultModel.getId(), taskScoreResultModel.getCompanyId(), taskScoreResultModel.getTaskId(), taskScoreResultModel.getEmpId(), StringTool.handleNullToEmpty(taskScoreResultModel.getOrgId()),
                    StringTool.handleNullToEmpty(taskScoreResultModel.getKpiTypeId()), StringTool.handleNullToEmpty(taskScoreResultModel.getKpiItemId()), taskScoreResultModel.getScorerType(), taskScoreResultModel.getScorerId(),
                    taskScoreResultModel.getScore(), taskScoreResultModel.getScoreWeight(), StringTool.handleNullToEmpty(taskScoreResultModel.getScoreComment()), StringTool.handleNullToEmpty(taskScoreResultModel.getScoreAttUrl()),
                    taskScoreResultModel.getScorerId(), DateTimeUtils.now2StrDateTime(), taskScoreResultModel.getApprovalOrder(), StringTool.handleNullToEmpty(taskScoreResultModel.getTransferId()),
                    StringTool.handleNullToEmpty(taskScoreResultModel.getReviewersType()), StringTool.handleNullToEmpty(taskScoreResultModel.getTaskAuditId()), StringTool.handleNullToEmpty(taskScoreResultModel.getModifyFlag()))).append(",");
        }
        String sqlStr = StringUtils.removeEnd(sql.toString(), ",");
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskScoreRuleModel.class).setSql(sqlStr);
        return autoBaseDao.nativeExecute(sqlBuilder);
    }

    public List<PerfEvaluateTaskScoreResultModel> queryNotAuditResult(String companyId, String taskId, String scorerType, String empId, String orgId) {
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskScoreResultModel.class)
                .whereEq("company_id", companyId)
                .whereEq("task_id", taskId)
                .whereEq("scorer_type", scorerType)
                .whereIsNull("audit_status")
                .whereEq("emp_id", empId)
                .whereEq("org_id", orgId)
                .whereEq("is_deleted", "false");
        return autoBaseDao.listAll(queryBuilder);
    }

    public List<PerfEvaluateTaskScoreResultModel> queryNotAuditResultByScoreId(String companyId, String taskId, List<String> scorerTypes, String empId, String scoreId) {
        ComQB queryBuilder = ComQB.buildDiff(PerfEvaluateTaskScoreResultModel.class, "v_task_score_result", "v")
                .clearSelect().select("*")
                .appendWhere(String.format("company_id='%s'", companyId))
                .appendWhere(String.format("task_id='%s'", taskId))
                .appendWhere(String.format("emp_id='%s'", empId))
                .appendWhere(String.format("scorer_id='%s'", scoreId))
                .appendWhere("audit_status = 'wait' ")
                .appendWhere("is_deleted='false'");
        if(CollectionUtils.isNotEmpty(scorerTypes)) {
            queryBuilder.whereIn("scorer_type", scorerTypes);
        }
        final List<PerfEvaluateTaskScoreResultModel> rs = autoBaseDao.listAll(queryBuilder);
        return rs;
    }


    public void updateAuditStatus(String id, String auditStatus, String updatedUser) {
        if(StringUtils.isEmpty(id)) {
            return;
        }
        UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_score_result").set("audit_status", auditStatus)
                .set("updated_user", updatedUser).whereEq("id", id);
        autoBaseDao.update(updateBuilder);
    }

    public void updateAuditStatusByIds(List<String> ids, String auditStatus, String updatedUser) {
        if(CollectionUtils.isEmpty(ids)) {
            return;
        }
        UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_score_result")
                .set("audit_status", auditStatus)
                .set("updated_user", updatedUser)
                .whereIn("id", ids);
        autoBaseDao.update(updateBuilder);
    }

    public void updateEmpAuditStatus(String companyId, String taskId, String scorerType, String empId, String orgId, String auditStatus, String updatedUser) {
        if(StringUtils.isAnyBlank(taskId, scorerType, empId)) {
            return;
        }
        UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_score_result").set("audit_status", auditStatus)
                .set("updated_user", updatedUser).whereEq("company_id", companyId).whereEq("task_id", taskId)
                .whereEq("org_id", orgId).whereEq("emp_id", empId).whereEq("scorer_type", scorerType);
        autoBaseDao.update(updateBuilder);
    }

    public void updateFinalAuditScore(String companyId, String taskId, String scorerType, String empId, String orgId, BigDecimal score, String scoreLevel,
                                      String updatedUser, String scoreComment) {
        if(StringUtils.isAnyBlank(taskId, empId, updatedUser)) {
            return;
        }
        UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_score_result").set("score", score).set("score_level", scoreLevel).set("score_comment", scoreComment).set("audit_status", "pass")
                .set("updated_user", updatedUser).whereEq("company_id", companyId).whereEq("task_id", taskId)
                .whereEq("org_id", orgId).whereEq("emp_id", empId).whereEq("scorer_type", scorerType)
                .whereEq("scorer_id", updatedUser).appendWhere("(audit_status = '' or audit_status is null)").whereEq("is_deleted", "false");
        autoBaseDao.update(updateBuilder);
    }

    public List<PerfEvaluateTaskScoreResultModel> queryNoAuditRecord(String companyId, String taskId, String scorerType, List<String> scorerTypeList, String empId, String orgId) {
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId).whereEq("emp_id", empId).whereEq("org_id", orgId)
                .whereEq("task_id", taskId).whereEq("scorer_type", scorerType).whereIsNull("audit_status").whereNotNull("kpi_item_id").whereEq("is_deleted", "false");
        if(CollectionUtils.isNotEmpty(scorerTypeList)) {
            queryBuilder.whereIn("scorer_type", scorerTypeList);
        }
        return autoBaseDao.listAll(queryBuilder);
    }

    public List<PerfEvaluateTaskScoreResultModel> queryItemNoAuditRecord(String companyId, String taskId, String scorerType, List<String> scorerTypeList, String empId, String kpiItemId) {
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId).whereEq("emp_id", empId).whereEq("kpi_item_id", kpiItemId)
                .whereEq("task_id", taskId).whereEq("scorer_type", scorerType).whereIsNull("audit_status").whereEq("is_deleted", "false");
        if(CollectionUtils.isNotEmpty(scorerTypeList)) {
            queryBuilder.whereIn("scorer_type", scorerTypeList);
        }
        return autoBaseDao.listAll(queryBuilder);
    }

    public List<PerfEvaluateTaskScoreResultModel> queryNoAuditRecordWithOutItem(String companyId, String taskId, String scorerType, List<String> scorerTypeList, String empId, String orgId) {
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId).whereEq("emp_id", empId).whereEq("org_id", orgId)
                .whereEq("task_id", taskId).whereEq("scorer_type", scorerType).whereIsNull("audit_status").whereEq("is_deleted", "false");
        if(CollectionUtils.isNotEmpty(scorerTypeList)) {
            queryBuilder.whereIn("scorer_type", scorerTypeList);
        }
        return autoBaseDao.listAll(queryBuilder);
    }

    public List<PerfEvaluateTaskScoreResultModel> queryNoAuditRecordByItem(String companyId, String taskId, String scorerType, String itemId, String empId, String orgId) {
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId).whereEq("emp_id", empId).whereEq("org_id", orgId)
                .whereEq("task_id", taskId).whereEq("scorer_type", scorerType).whereIsNull("audit_status").whereEq("kpi_item_id", itemId).whereEq("is_deleted", "false");
        return autoBaseDao.listAll(queryBuilder);
    }

    public List<PerfEvaluateTaskScoreResultModel> queryNoAuditRecordNotInItems(String companyId, String taskId, String scorerType, List<String> notInItemIds, String empId, String orgId) {
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId).whereEq("emp_id", empId).whereEq("org_id", orgId)
                .whereEq("task_id", taskId).whereEq("scorer_type", scorerType).whereIsNull("audit_status").whereNotIn("kpi_item_id", notInItemIds).whereEq("is_deleted", "false");
        return autoBaseDao.listAll(queryBuilder);
    }

    public List<PerfEvaluateTaskScoreResultModel> queryResultByType(String companyId, String taskId, String scorerType, String empId, String orgId) {
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId).whereEq("emp_id", empId).whereEq("org_id", orgId)
                .whereEq("task_id", taskId).whereEq("scorer_type", scorerType).whereEq("is_deleted", "false");
        return autoBaseDao.listAll(queryBuilder);
    }

    public List<PerfEvaluateTaskScoreResultVO> listTaskScoreResults(String companyId, String taskId, List<String> orgIds, List<String> empIds) {
        return listTaskScoreResults(companyId, taskId, orgIds, empIds, null, null);
    }

    public List<PerfEvaluateTaskScoreResultVO> listTaskScoreResults(String companyId, String taskId, String empId, String scorerType, List<String> scorerIds) {
        return listTaskScoreResults(companyId, taskId, null, Collections.singletonList(empId), scorerType, scorerIds);
    }

    public List<PerfEvaluateTaskScoreResultVO> listTaskScoreResults(String companyId, String scorerType, List<String> scorerIds) {
        return listTaskScoreResults(companyId, null, null, scorerType, scorerIds);
    }

    public List<PerfEvaluateTaskScoreResultVO> listTaskScoreResults(String companyId, String taskId, List<String> orgIds, List<String> empIds, String scorerType, List<String> scorerIds) {
        if(StringUtils.isBlank(taskId) || CollectionUtils.isEmpty(empIds)) {
            return new ArrayList<>();
        }

        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskScoreResultVO.class).setSql("SELECT r.*, e. NAME scorer, e.avatar, k.* " +
                "FROM perf_evaluate_task_score_result AS r " +
                "LEFT OUTER JOIN employee_base_info AS e ON r.scorer_id = e.employee_id " +
                "LEFT OUTER JOIN ( " +
                " SELECT DISTINCT emp_id,kpi_item_id,kpi_type_classify,kpi_type_name,kpi_item_name,item_rule,scoring_rule,max_extra_score,plus_limit,subtract_limit, " +
                "  is_okr,type_order,`order`,item_weight,item_score_value,item_target_value,item_finish_value,item_unit,result_input_type,scorer_type AS itemScorerType, " +
                "  threshold_json,formula_condition,item_field_json,item_auto_score,kpi_type_weight,work_item_finish_value,k.input_format,k.item_finish_value_text " +
                " FROM perf_evaluate_task_kpi AS k WHERE " +
                "  (k.company_id = #{companyId} AND task_id = #{taskId} AND is_deleted = 'false' AND k.emp_id IN " + StringTool.getInStr(empIds) + ") " +
                ") AS k ON r.kpi_item_id = k.kpi_item_id AND r.emp_id = k.emp_id " +
                "WHERE " +
                " ( " +
                "  r.company_id = #{companyId} AND r.is_deleted = 'false' " +
                "  AND r.task_id = #{taskId} " +
                "  AND r.emp_id IN " + StringTool.getInStr(empIds) +
                "  AND ( " +
                "   r.audit_status IS NULL " +
                "   OR r.audit_status != 'transferred' " +
                "  ) " +
                " ) " +
                "ORDER BY " +
                " k.type_order, " +
                " k.`order`");

        sqlBuilder.setValue("companyId", companyId);
        sqlBuilder.setValue("taskId", taskId);
        return autoBaseDao.listAll(sqlBuilder);
    }


    public List<PerfEvaluateTaskScoreResultVO> listTaskScoreResults(String companyId, String taskUserId) {
        if(StringUtils.isBlank(taskUserId)) {
            return new ArrayList<>();
        }

        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskScoreResultVO.class).setSql("SELECT r.*, e. NAME scorer, e.avatar, k.* " +
                "FROM perf_evaluate_task_score_result AS r " +
                "LEFT OUTER JOIN employee_base_info AS e ON r.scorer_id = e.employee_id " +
                "LEFT OUTER JOIN ( " +
                " SELECT DISTINCT emp_id,kpi_item_id,kpi_type_classify,kpi_type_name,kpi_item_name,item_rule,scoring_rule,max_extra_score,plus_limit,subtract_limit, " +
                "  is_okr,type_order,`order`,item_weight,item_score_value,item_target_value,item_finish_value,item_unit,result_input_type,scorer_type AS itemScorerType, " +
                "  threshold_json,formula_condition,item_field_json,item_auto_score,kpi_type_weight,work_item_finish_value " +
                " FROM perf_evaluate_task_kpi AS k WHERE " +
                "  (k.company_id = #{companyId} AND task_user_id = #{taskUserId}  AND is_deleted = 'false' " +
                ")) AS k ON r.kpi_item_id = k.kpi_item_id AND r.emp_id = k.emp_id " +
                "WHERE " +
                " ( " +
                "  r.company_id = #{companyId} AND r.is_deleted = 'false' " +
                "  AND r.task_user_id = #{taskUserId} " +
                "  AND ( " +
                "   r.audit_status IS NULL " +
                "   OR r.audit_status != 'transferred' " +
                "  ) " +
                " ) " +
                "ORDER BY " +
                " k.type_order, " +
                " k.`order`");

        sqlBuilder.setValue("companyId", companyId);
        sqlBuilder.setValue("taskUserId", taskUserId);
        return autoBaseDao.listAll(sqlBuilder);
    }

    public List<PerfEvaluateTaskScoreResultVO> listAllTaskScoreResults(String companyId, String taskId, String empId, String scorerType, List<String> scorerIds) {
        if(StringUtils.isNotBlank(taskId) && StringUtils.isNotEmpty(empId)) {
            ComQB<PerfEvaluateTaskKpiModel> kpiModelComQB = ComQB.build(PerfEvaluateTaskKpiModel.class, "k").clearSelect().select("DISTINCT kpi_item_id,kpi_type_classify,kpi_type_name,kpi_item_name ")
                    .whereEq("task_id", taskId).whereEq("is_deleted", "false");

            ComQB<PerfEvaluateTaskScoreResultVO> comQB = ComQB.build(PerfEvaluateTaskScoreResultModel.class, "r")
                    .leftJoin(EmployeeBaseInfoModel.class, "e").on("r.scorer_id", "e.employee_id")
                    .leftJoinQ(kpiModelComQB, "k").on("r.kpi_item_id", "k.kpi_item_id")
                    .clearSelect().select("r.*,e.name scorer,e.avatar,k.kpi_type_classify,k.kpi_type_name,k.kpi_item_name itemName ").setRsType(PerfEvaluateTaskScoreResultVO.class)
                    .whereEq("r.company_id", companyId).whereEq("r.scorer_type", scorerType)
                    .whereEq("r.task_id", taskId).whereEq("r.is_deleted", "false")
                    .whereEq("r.emp_id", empId).whereIn("r.scorer_id", scorerIds);
            return autoBaseDao.listAll(comQB);
        }
        return null;
    }

    public List<PerfEvaluateTaskScoreResultVO> findTaskScoreResults(String companyId, String taskId, String empId, String scorerType, Integer approvalOrder) {
        if(StringUtils.isNotBlank(taskId) && StringUtils.isNotEmpty(empId)) {
            ComQB<PerfEvaluateTaskKpiModel> kpiModelComQB = ComQB.build(PerfEvaluateTaskKpiModel.class, "k").clearSelect().select("DISTINCT kpi_item_id,kpi_type_classify,kpi_type_name,kpi_item_name ")
                    .whereEq("task_id", taskId).whereEq("is_deleted", "false");

            ComQB<PerfEvaluateTaskScoreResultVO> comQB = ComQB.build(PerfEvaluateTaskScoreResultModel.class, "r")
                    .leftJoin(EmployeeBaseInfoModel.class, "e").on("r.scorer_id", "e.employee_id")
                    .leftJoinQ(kpiModelComQB, "k").on("r.kpi_item_id", "k.kpi_item_id")
                    .clearSelect().select("r.*,e.name scorer,e.avatar,k.kpi_type_classify,k.kpi_type_name,k.kpi_item_name itemName ").setRsType(PerfEvaluateTaskScoreResultVO.class)
                    .whereEq("r.company_id", companyId).whereEq("r.scorer_type", scorerType)
                    .whereEq("r.task_id", taskId).whereEq("r.is_deleted", "false")
                    .whereEq("r.emp_id", empId).whereEq("r.approval_order", approvalOrder);
            return autoBaseDao.listAll(comQB);
        }
        return null;
    }

    public List<PerfEvaluateTaskScoreResultVO> queryNotAuditResultByScorerId(String companyId, String taskId, String empId, String orgId, String scorerId) {
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskScoreResultVO.class).setSql("SELECT t.*,k.kpi_item_name as item_name,k.kpi_type_name as kpiTypeName,k.item_weight,k.is_okr,k.okr_ref_flag,k.kpi_type_classify,k.work_item_finish_value  " +
                "FROM perf_evaluate_task_score_result t  " +
                "LEFT JOIN (SELECT DISTINCT kpi_type_id, kpi_type_name,kpi_item_id,item_weight,is_okr,okr_ref_flag,kpi_item_name,`order`,type_order,kpi_type_classify,work_item_finish_value FROM perf_evaluate_task_kpi WHERE task_id = #{taskId} and emp_id = #{empId} and is_deleted = 'false' GROUP BY kpi_item_id) k ON k.kpi_item_id = t.kpi_item_id " +
                "WHERE t.company_id = #{companyId} AND t.task_id = #{taskId} AND (t.audit_status IS NULL or t.audit_status = '') AND t.emp_id = #{empId}  " +
                "AND t.scorer_id = #{scorerId} AND t.is_deleted = 'false' ");
        sqlBuilder.setValue("companyId", companyId);
        sqlBuilder.setValue("taskId", taskId);
        sqlBuilder.setValue("empId", empId);
        sqlBuilder.setValue("scorerId", scorerId);
        sqlBuilder.append("order by k.type_order ASC, k.`order` asc");
        return autoBaseDao.listAll(sqlBuilder);
    }

    public List<PerfEvaluateTaskScoreResultVO> queryNotAuditResultByScorerIds(String companyId, List<String> scorerIds) {
        ComQB<PerfEvaluateTaskScoreResultVO> comQB = ComQB.build(PerfEvaluateTaskScoreResultModel.class, "r")
                .join(PerfEvaluateTaskBaseModel.class, "t").on("r.task_id", "t.id")
                .join(PerfTemplBaseModel.class, "tp").on("tp.id", "t.templ_base_id")
                .leftJoin(EmployeeBaseInfoModel.class, "se").on("se.employee_id", "r.scorer_id")
                .clearSelect().setRsType(PerfEvaluateTaskScoreResultVO.class)
                .select("r.*,r.emp_id taskEmpId,r.id scoreResultId,tp.id tempId,tp.name tempName,t.task_name,se.name scorer,se.avatar scorerAvatar,t.created_user taskInitiator")
                .whereEq("r.company_id", companyId).whereEq("r.is_deleted", "false")
                .whereIn("r.scorer_id", scorerIds)
                .whereIsNull("r.audit_status");
        return autoBaseDao.listAll(comQB);
    }

    public List<PerfEvaluateTaskScoreResultModel> queryNotAuditResultByScorerTypes(String companyId, String taskId, List<String> scorerTypeList, String empId, String orgId, String scorerId) {
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId)
                .whereEq("task_id", taskId).whereIn("scorer_type", scorerTypeList).whereIsNull("audit_status").whereEq("emp_id", empId)
                .whereEq("org_id", orgId).whereEq("scorer_id", scorerId).whereEq("is_deleted", "false");
        //只查普通指标
        String sql = String.format("kpi_item_id in (select DISTINCT kpi_item_id from perf_evaluate_task_kpi WHERE (kpi_type_classify not in ('plus','subtract') or kpi_type_classify is null)  AND task_id = '%s' and is_deleted = 'false')", taskId);
        queryBuilder.appendWhere(sql);
        return autoBaseDao.listAll(queryBuilder);
    }

    public List<PerfEvaluateTaskScoreResultModel> queryAuditedResultByScorerTypes(String companyId, String taskId, String scorerType, String empId, String orgId, String scorerId) {
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId)
                .whereEq("task_id", taskId).whereEq("scorer_type", scorerType).whereEq("audit_status", "pass").whereEq("emp_id", empId)
                .whereEq("org_id", orgId).whereEq("scorer_id", scorerId).whereEq("is_deleted", "false");
        //只查普通指标
        String sql = String.format("kpi_item_id in (select DISTINCT kpi_item_id from perf_evaluate_task_kpi WHERE (kpi_type_classify not in ('plus','subtract') or kpi_type_classify is null)  AND task_id = '%s' and is_deleted = 'false')", taskId);
        queryBuilder.appendWhere(sql);
        return autoBaseDao.listAll(queryBuilder);
    }

    public List<PerfEvaluateTaskScoreResultModel> queryAuditedResultByTask(String companyId, String taskId) {
        if(StringUtils.isAnyBlank(companyId, taskId)) {
            return null;
        }
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId)
                .whereEq("task_id", taskId).whereEq("audit_status", "pass").whereEq("is_deleted", "false");
        return autoBaseDao.listAll(queryBuilder);
    }

    public List<PerfEvaluateTaskScoreResultModel> queryAuditedResultByTaskIds(TenantId tenantId, List<String> taskIds) {
        if (CollectionUtils.isEmpty(taskIds)) {
            return new ArrayList<>();
        }
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskScoreResultModel.class).
                whereEq("company_id", tenantId.getId())
                .whereIn("task_id", taskIds)
                .whereEq("audit_status", "pass")
                .whereEq("is_deleted", "false");
        return autoBaseDao.listAll(queryBuilder);
    }

    public List<PerfEvaluateTaskScoreResultModel> queryTotalAuditedResultByScorerTypes(String companyId, String taskId, String scorerType, String empId, String orgId, String scorerId) {
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId)
                .whereEq("task_id", taskId).whereEq("scorer_type", scorerType).whereEq("audit_status", "pass").whereEq("emp_id", empId)
                .whereEq("org_id", orgId).whereEq("scorer_id", scorerId).whereNotNull("score").whereEq("is_deleted", "false");
        return autoBaseDao.listAll(queryBuilder);
    }

    public List<PerfEvaluateTaskScoreResultModel> queryScoreResultByEmp(String companyId, String taskId, String empId, String orgId, String scorerId, String scorerType) {
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId).whereEq("is_deleted", "false")
                .whereEq("task_id", taskId).whereEq("emp_id", empId).whereEq("org_id", orgId).whereEq("scorer_id", scorerId)
                .whereEq("scorer_type", scorerType);
        return autoBaseDao.listAll(queryBuilder);
    }

    public void updateTaskScoreResult(PerfEvaluateTaskScoreResultModel model) {
        model.setCreatedTime(null);
        model.setUpdatedTime(null);
        UpdateBuilder updateBuilder = UpdateBuilder.build(PerfEvaluateTaskScoreResultModel.class).setBean(model).whereEq("id", model.getId());
        autoBaseDao.update(updateBuilder);
    }

    public List<PerfEvaluateTaskScoreResultModel> queryItemScoreResult(String companyId, String taskId, String scorerType, String empId, String orgId, String scorerId, String kpiTypeId, String kpiItemId) {
        ComQB queryBuilder = ComQB.buildDiff(PerfEvaluateTaskScoreResultModel.class, "perf_evaluate_task_score_result", "t")
                .whereEq("company_id", companyId)
                .whereEq("task_id", taskId).whereEq("scorer_type", scorerType)
                .whereIsNull("audit_status").whereEq("emp_id", empId).whereEq("is_deleted", "false")
                .whereEq("org_id", orgId).whereEq("scorer_id", scorerId).whereEq("kpi_type_id", kpiTypeId).whereEq("kpi_item_id", kpiItemId);
        return autoBaseDao.listAll(queryBuilder);
    }

    public List<PerfEvaluateTaskScoreResultModel> queryItemAuditedScoreResult(String companyId, String taskId, String scorerType, String empId, String orgId, String scorerId, String kpiTypeId, String kpiItemId) {
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId).whereEq("is_deleted", "false")
                .whereEq("task_id", taskId).whereEq("scorer_type", scorerType).whereEq("audit_status", "pass").whereEq("emp_id", empId)
                .whereEq("org_id", orgId).whereEq("scorer_id", scorerId).whereEq("kpi_type_id", kpiTypeId).whereEq("kpi_item_id", kpiItemId);
        return autoBaseDao.listAll(queryBuilder);
    }

    public PerfEvaluateTaskScoreResultModel queryItemScoreResultByTypes(String companyId, String taskId, List<String> scorerTypes, String empId, String orgId, String scorerId, String kpiTypeId, String kpiItemId) {
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId).whereEq("is_deleted", "false")
                .whereEq("task_id", taskId).whereIn("scorer_type", scorerTypes).whereIsNull("audit_status").whereEq("emp_id", empId)
                .whereEq("org_id", orgId).whereEq("scorer_id", scorerId).whereEq("kpi_type_id", kpiTypeId).whereEq("kpi_item_id", kpiItemId);
        List<PerfEvaluateTaskScoreResultModel> list = autoBaseDao.listAll(queryBuilder);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    public PerfEvaluateTaskScoreResultModel queryTotalScoreResult(String companyId, String taskId, String scorerType, String empId, String orgId, String scorerId) {
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId).whereEq("is_deleted", "false")
                .whereEq("task_id", taskId).whereEq("scorer_type", scorerType).whereIsNull("audit_status").whereEq("emp_id", empId)
                .whereEq("org_id", orgId).whereEq("scorer_id", scorerId).whereIsNull("kpi_item_id");
        List<PerfEvaluateTaskScoreResultModel> list = autoBaseDao.listAll(queryBuilder);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    public BigDecimal queryItemFinalScoreSum(String companyId, String taskId, String scorerType, String empId, String orgId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId).whereEq("task_id", taskId)
                .whereEq("scorer_type", scorerType).whereEq("audit_status", BusinessConstant.PASS).whereEq("emp_id", empId)
                .whereEq("org_id", orgId).whereNotNull("kpi_item_id").whereEq("is_deleted", "false");
        comQB.clearSelect().select("SUM(final_score)").setRsType(BigDecimal.class);
        return (BigDecimal) autoBaseDao.findOne(comQB);
    }

    public BigDecimal queryItemFinalWeightScoreSum(String companyId, String taskId, String empId, String orgId, String scorerType) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId).whereEq("task_id", taskId)
                .whereEq("scorer_type", scorerType).whereEq("audit_status", BusinessConstant.PASS).whereEq("emp_id", empId)
                .whereEq("org_id", orgId).whereNotNull("kpi_item_id").whereEq("is_deleted", "false");
        comQB.clearSelect().select("SUM(final_weight_score)").setRsType(BigDecimal.class);
        return (BigDecimal) autoBaseDao.findOne(comQB);
    }

    public BigDecimal queryPlusFinalScoreSum(String companyId, String taskId, String scorerType, String empId, String orgId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId).whereEq("task_id", taskId)
                .whereEq("scorer_type", scorerType).whereEq("audit_status", BusinessConstant.PASS).whereEq("emp_id", empId)
                .whereEq("org_id", orgId).whereNotNull("kpi_item_id").whereEq("is_deleted", "false");
        comQB.clearSelect().select("SUM(final_plus_score)").setRsType(BigDecimal.class);
        return (BigDecimal) autoBaseDao.findOne(comQB);
    }

    public BigDecimal queryPlusFinalWeightScoreSum(String companyId, String taskId, String scorerType, String empId, String orgId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId).whereEq("task_id", taskId)
                .whereEq("scorer_type", scorerType).whereEq("audit_status", BusinessConstant.PASS).whereEq("emp_id", empId)
                .whereEq("org_id", orgId).whereNotNull("kpi_item_id").whereEq("is_deleted", "false");
        comQB.clearSelect().select("SUM(final_weight_plus_score)").setRsType(BigDecimal.class);
        return (BigDecimal) autoBaseDao.findOne(comQB);
    }

    public BigDecimal queryPlusFinalWeightScoreByKpiType(String companyId, String taskId, String kpiTypeId, String empId, String orgId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId).whereEq("task_id", taskId)
                .whereEq("kpi_type_id", kpiTypeId).whereEq("audit_status", BusinessConstant.PASS).whereEq("emp_id", empId)
                .whereEq("org_id", orgId).whereNotNull("kpi_item_id").whereEq("is_deleted", "false");
        comQB.clearSelect().select("SUM(final_weight_plus_score)").setRsType(BigDecimal.class);
        return (BigDecimal) autoBaseDao.findOne(comQB);
    }

    public BigDecimal querySubtractFinalScoreSum(String companyId, String taskId, String scorerType, String empId, String orgId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId).whereEq("task_id", taskId)
                .whereEq("scorer_type", scorerType).whereEq("audit_status", BusinessConstant.PASS).whereEq("emp_id", empId)
                .whereEq("org_id", orgId).whereNotNull("kpi_item_id").whereEq("is_deleted", "false");
        comQB.clearSelect().select("SUM(final_subtract_score)").setRsType(BigDecimal.class);
        return (BigDecimal) autoBaseDao.findOne(comQB);
    }

    public BigDecimal querySubtractFinalWeightScoreSum(String companyId, String taskId, String scorerType, String empId, String orgId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId).whereEq("task_id", taskId)
                .whereEq("scorer_type", scorerType).whereEq("audit_status", BusinessConstant.PASS).whereEq("emp_id", empId)
                .whereEq("org_id", orgId).whereNotNull("kpi_item_id").whereEq("is_deleted", "false");
        comQB.clearSelect().select("SUM(final_weight_subtract_score)").setRsType(BigDecimal.class);
        return (BigDecimal) autoBaseDao.findOne(comQB);
    }

    public BigDecimal querySubtractFinalWeightScoreByKpiType(String companyId, String taskId, String kpiTypeId, String empId, String orgId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId).whereEq("task_id", taskId)
                .whereEq("kpi_type_id", kpiTypeId).whereEq("audit_status", BusinessConstant.PASS).whereEq("emp_id", empId)
                .whereEq("org_id", orgId).whereNotNull("kpi_item_id").whereEq("is_deleted", "false");
        comQB.clearSelect().select("SUM(final_weight_subtract_score)").setRsType(BigDecimal.class);
        return (BigDecimal) autoBaseDao.findOne(comQB);
    }

    public BigDecimal queryItemFinalScoreSumByTypes(String companyId, String taskId, List<String> scorerTypes, String empId, String orgId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId).whereEq("task_id", taskId)
                .whereIn("scorer_type", scorerTypes).whereEq("audit_status", BusinessConstant.PASS).whereEq("emp_id", empId)
                .whereEq("org_id", orgId).whereNotNull("kpi_item_id").whereEq("is_deleted", "false");
        comQB.clearSelect().select("SUM(final_score)").setRsType(BigDecimal.class);
        return (BigDecimal) autoBaseDao.findOne(comQB);
    }

    public List<PerfEvaluateTaskScoreResultVO> queryAuditResultByType(String companyId, String taskId, String scorerType, String empId, String orgId) {
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskScoreResultVO.class).setSql("select r.*,e.name as scorer,e.avatar, " +
                "(select GROUP_CONCAT(o.org_name) FROM emp_organization o WHERE o.company_id =#{companyId} and o.org_id in (select org_id from emp_ref_org where emp_id = e.employee_id and ref_type = 'org')) as orgName " +
                "from perf_evaluate_task_score_result r  " +
                "LEFT JOIN employee_base_info e ON r.scorer_id = e.employee_id " +
                "WHERE r.company_id = #{companyId} and r.emp_id = #{empId} AND r.task_id = #{taskId}  AND r.audit_status = 'pass' AND r.is_deleted = 'false' and r.score is not null ");
        sqlBuilder.setValue("companyId", companyId);
        sqlBuilder.setValue("empId", empId);
        sqlBuilder.setValue("taskId", taskId);
        sqlBuilder.appendIfOpt("AND r.scorer_type = #{scorerType}", "scorerType", scorerType);
        sqlBuilder.append("ORDER BY r.updated_time DESC");
        return autoBaseDao.listAll(sqlBuilder);
    }
    //线上移动端查询加减分项指标时score为空的没有返回，导致移动端不显示加分项的分数，这里暂时处理下，后面要梳理逻辑后重构掉
    public List<PerfEvaluateTaskScoreResultVO> queryScoreByType(String companyId, String taskId, String scorerType, String empId, String orgId) {
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskScoreResultVO.class).setSql("select r.*,e.name as scorer,e.avatar, " +
                "(select GROUP_CONCAT(o.org_name) FROM emp_organization o WHERE o.company_id =#{companyId} and o.org_id in (select org_id from emp_ref_org where emp_id = e.employee_id and ref_type = 'org')) as orgName " +
                "from perf_evaluate_task_score_result r  " +
                "LEFT JOIN employee_base_info e ON r.scorer_id = e.employee_id " +
                "WHERE r.company_id = #{companyId} and r.emp_id = #{empId} AND r.task_id = #{taskId}  AND r.audit_status = 'pass' AND r.is_deleted = 'false' ");
        sqlBuilder.setValue("companyId", companyId);
        sqlBuilder.setValue("empId", empId);
        sqlBuilder.setValue("taskId", taskId);
        sqlBuilder.appendIfOpt("AND r.scorer_type = #{scorerType}", "scorerType", scorerType);
        sqlBuilder.append("ORDER BY r.updated_time DESC");
        return autoBaseDao.listAll(sqlBuilder);
    }

    public List<PerfEvaluateTaskScoreResultVO> queryAuditResult(PerfEvaluateTaskScoreResultModel model) {
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskScoreResultVO.class).setSql("select r.*,e.name as scorer from perf_evaluate_task_score_result r  " +
                "LEFT JOIN employee_base_info e ON r.scorer_id = e.employee_id " +
                "WHERE r.company_id = #{companyId} and r.emp_id = #{empId}  AND r.task_id = #{taskId}  AND r.audit_status = 'pass' AND r.is_deleted = 'false' ");
        sqlBuilder.setValue("companyId", model.getCompanyId());
        sqlBuilder.setValue("empId", model.getEmpId());
        sqlBuilder.setValue("taskId", model.getTaskId());
        sqlBuilder.appendIfOpt("AND r.scorer_type = #{scorerType}", "scorerType", model.getScorerType());
        sqlBuilder.appendIfOpt("AND r.kpi_item_id = #{itemId}", "itemId", model.getKpiItemId());
        sqlBuilder.append("ORDER BY r.updated_time DESC");
        return autoBaseDao.listAll(sqlBuilder);
    }

    public List<PerfEvaluateTaskScoreResultModel> queryResultByTaskIds(String companyId, List<String> taskIds) {
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId)
                .whereIn("task_id", taskIds).whereEq("is_deleted", "false");
        return autoBaseDao.listAll(queryBuilder);
    }

    public List<Map> queryNotAuditScoreIds(String taskId, String empId, String companyId) {
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(Map.class).setSql("select r.scorer_id, e.`name` as scoreName,GROUP_CONCAT(DISTINCT r.scorer_type) as scorerType from perf_evaluate_task_score_result r  " +
                "LEFT JOIN employee_base_info e on r.scorer_id = e.employee_id " +
                "WHERE r.task_id = #{taskId} and r.emp_id = #{empId} and audit_status is NULL AND r.company_id = #{companyId} AND r.is_deleted = 'false' and r.scorer_type != 'self_score' GROUP BY r.scorer_id,e.`name`");
        sqlBuilder.setValue("taskId", taskId);
        sqlBuilder.setValue("empId", empId);
        sqlBuilder.setValue("companyId", companyId);
        return autoBaseDao.listAll(sqlBuilder);
    }

    public List<PerfEvaluateTaskScoreResultModel> queryNotAuditByScoreType(String taskId, String empId, String companyId, String scoreType, String scorerId) {
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskScoreResultModel.class).setSql("select r.* from perf_evaluate_task_score_result r  " +
                "WHERE r.task_id = #{taskId} and r.emp_id = #{empId} and audit_status is NULL AND r.company_id = #{companyId} and r.scorer_type = #{scoreType} and (r.is_deleted = 'false' or r.is_deleted is null ) ");
        sqlBuilder.setValue("taskId", taskId);
        sqlBuilder.setValue("empId", empId);
        sqlBuilder.setValue("companyId", companyId);
        sqlBuilder.setValue("scoreType", scoreType);
        sqlBuilder.appendIfOpt("and r.scorer_id = #{scorerId}", "scorerId", scorerId);
        return autoBaseDao.listAll(sqlBuilder);
    }

    public int batchInsertMsgRel(List<CompanyMsgRelScoreResultModel> resultList, String companyId, String empId, String msgId) {
        StringBuilder sql = new StringBuilder("INSERT INTO company_msg_rel_score_result(id,company_id,emp_id,temp_id,task_id,score_result_id,msg_id,scorer_id,handle_status,created_time,updated_time) VALUES ");
        for (CompanyMsgRelScoreResultModel taskScoreResultModel : resultList) {
            sql.append(String.format("('%s','%s','%s','%s','%s','%s','%s','%s','false',now(),now()),",
                    UUID.randomUUID().toString(), companyId, empId, taskScoreResultModel.getTempId(), taskScoreResultModel.getTaskId(),
                    taskScoreResultModel.getScoreResultId(), msgId, taskScoreResultModel.getScorerId()));
        }
        String sqlStr = StringUtils.removeEnd(sql.toString(), ",");
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskScoreRuleModel.class).setSql(sqlStr);
        return autoBaseDao.nativeExecute(sqlBuilder);
    }

    public List<PerfEvaluateTaskScoreResultVO> queryResultByOrder(String companyId, String taskId, String empId, String scene, Integer approvalOrder) {
        ComQB queryBuilder = ComQB.build(PerfEvaluateTaskScoreResultModel.class, "r")
                .leftJoin(EmployeeBaseInfoModel.class, "e").on("e.employee_id", "r.scorer_id")
                .whereEq("r.company_id", companyId).whereEq("r.is_deleted", "false")
                .whereEq("r.task_id", taskId).whereEq("r.emp_id", empId).whereEq("r.scorer_type", scene).whereEq("r.approval_order", approvalOrder).appendWhere("(r.audit_status not in('transferred','reject') or r.audit_status is NULL)")
                .clearSelect().select("r.*,e.name as scorer,e.avatar,e.status scorerStatus").setRsType(PerfEvaluateTaskScoreResultVO.class);
        return autoBaseDao.listAll(queryBuilder);
    }

    public List<PerfEvaluateTaskScoreResultModel> queryNotHandlerResultByIds(String companyId, List<String> ids) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultModel.class, "r")
                .leftJoin(PerfEvaluateTaskBaseModel.class, "b").on("r.task_id", "b.id")
                .whereEq("r.company_id", companyId).whereEq("r.is_deleted", "false")
                .whereIn("r.id", ids).whereIsNull("r.audit_status").whereNotEq("r.scorer_type", "self_score")
                .whereEq("b.is_deleted", "false").clearSelect().select("r.*").setRsType(PerfEvaluateTaskScoreResultModel.class);
        return autoBaseDao.listAll(comQB);
    }

    public List<PerfEvaluateTaskScoreResultVO> queryResultVOByIds(String companyId, List<String> ids) {
        if(CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskScoreResultVO.class).setSql("SELECT r.*,b.task_name,u.id as taskUserId,b.evaluate_type FROM perf_evaluate_task_score_result r   " +
                "LEFT JOIN perf_evaluate_task_base b ON r.task_id = b.id AND b.is_deleted = 'false'  " +
                "LEFT JOIN perf_evaluate_task_user u ON r.task_id = u.task_id AND r.emp_id = u.emp_id   " +
                "where r.company_id = #{companyId} AND r.is_deleted = 'false' AND u.is_deleted = 'false' AND b.is_deleted = 'false' and r.id in ").append(StringTool.getInStr(ids).toString());
        sqlBuilder.setValue("companyId", companyId);
        return autoBaseDao.listAll(sqlBuilder);
    }

    public List<EmployeeSimpleInfoVO> queryScoreEmp(String companyId, String taskId, String empId) {
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(EmployeeSimpleInfoVO.class).setSql("SELECT DISTINCT i.employee_id as empId, i.name as empName, i.avatar FROM perf_evaluate_task_score_result r LEFT JOIN employee_base_info i on r.scorer_id = i.employee_id " +
                "WHERE r.task_id = #{taskId} AND r.emp_id = #{empId} and r.company_id = #{companyId} AND r.is_deleted = 'false' " +
                "AND r.audit_status is null ");
        sqlBuilder.setValue("taskId", taskId);
        sqlBuilder.setValue("empId", empId);
        sqlBuilder.setValue("companyId", companyId);
        return autoBaseDao.listAll(sqlBuilder);
    }

    public List<EmployeeSimpleInfoVO> queryScoreEmpWithoutMutualScore(String companyId, String taskId, String empId) {
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(EmployeeSimpleInfoVO.class).setSql("SELECT DISTINCT i.employee_id as empId, i.name as empName, i.avatar FROM perf_evaluate_task_score_result r LEFT JOIN employee_base_info i on r.scorer_id = i.employee_id " +
                "WHERE r.task_id = #{taskId} AND r.emp_id = #{empId} and r.company_id = #{companyId} AND r.is_deleted = 'false' " +
                "AND r.audit_status is null AND r.scorer_type not in ('peer_score', 'sub_score', 'total_peer_score', 'total_sub_score') ");
        sqlBuilder.setValue("taskId", taskId);
        sqlBuilder.setValue("empId", empId);
        sqlBuilder.setValue("companyId", companyId);
        return autoBaseDao.listAll(sqlBuilder);
    }

    public List<PerfEvaluateTaskScoreResultModel> queryScorerNoAudit(String companyId, String taskId, String noScorerType, String empId, String kpiItemId, String scorerId) {
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId).whereEq("emp_id", empId).whereEq("scorer_id", scorerId).whereEq("is_deleted", "false")
                .whereEq("task_id", taskId).whereIsNull("audit_status").appendWhere("((kpi_item_id = '" + kpiItemId + "' and scorer_type != '" + noScorerType + "') or (kpi_item_id != '" + kpiItemId + "'))");
        return autoBaseDao.listAll(queryBuilder);
    }

    public void deleteScoreResultByIds(String companyId, String taskId, List<String> ids) {
//        DeleteBuilder deleteBuilder = DeleteBuilder.build("perf_evaluate_task_score_result").whereEq("company_id", companyId).whereEq("task_id", taskId)
//                .whereIn("id", ids);
//        autoBaseDao.delete(deleteBuilder);
        if(StringUtils.isAnyBlank(companyId, taskId)) {
            return;
        }
        UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_score_result").set("is_deleted", "true").whereEq("company_id", companyId).whereEq("task_id", taskId).whereEq("is_deleted", "false").whereIn("id", ids);
        autoBaseDao.update(updateBuilder);
    }

    public void deleteScoreResultByScenes(String companyId, String taskId, String empId, List<String> scenes) {
        if(StringUtils.isAnyBlank(taskId, empId)) {
            return;
        }
        UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_score_result").set("is_deleted", "true").whereEq("company_id", companyId).whereEq("task_id", taskId)
                .whereEq("emp_id", empId).whereEq("is_deleted", "false");
        if(CollectionUtils.isNotEmpty(scenes)) {
            updateBuilder.whereIn("scorer_type", scenes);
        }
        autoBaseDao.update(updateBuilder);
    }

    public boolean checkHasScoreType(String companyId, String taskId, String empId, String scorerType) {
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId).whereEq("emp_id", empId).whereEq("scorer_type", scorerType)
                .whereEq("task_id", taskId).whereEq("is_deleted", "false");
        return CollectionUtils.isNotEmpty(autoBaseDao.listAll(queryBuilder));
    }

    public List<String> queryMyFinishedScoreType(String companyId, String taskId, String empId, String scorerId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId).whereEq("task_id", taskId).whereEq("is_deleted", "false")
                .whereEq("emp_id", empId).whereEq("audit_status", "pass").whereEq("scorer_id", scorerId).appendWhere("scorer_type NOT LIKE 'total_%' and (score is not null or plus_score is not null or subtract_score is not null) AND scorer_type not in ('final_result_audit','modify_item_audit')");
        comQB.clearSelect().setRsType(String.class).select("DISTINCT scorer_type");
        return autoBaseDao.listAll(comQB);
    }

    public PerfEvaluateSaveScoreVO queryAuditedTotalScore(String companyId, String taskId, String empId, String scorerId, String scoreType) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId)
                .whereEq("task_id", taskId).whereEq("is_deleted", "false")
                .whereEq("emp_id", empId)
                .whereEq("audit_status", "pass")
                .whereEq("scorer_id", scorerId)
                .whereEq("scorer_type", scoreType)
                .appendWhere("score IS NOT NULL");
        comQB.clearSelect().setRsType(PerfEvaluateSaveScoreVO.class)
                .select("score AS totalScore, score_comment as totalComment, task_id, emp_id, org_id");
        List<PerfEvaluateSaveScoreVO> list = autoBaseDao.listAll(comQB);
        if(CollectionUtils.isEmpty(list)) {
            PerfEvaluateSaveScoreVO saveScoreVO = new PerfEvaluateSaveScoreVO();
            saveScoreVO.setTaskId(taskId);
            saveScoreVO.setEmpId(empId);
            return saveScoreVO;
        }
        return list.get(0);
    }

    public List<PerfEvaluateTaskScoreResultVO> queryAuditedResultByScorerId(String companyId, String taskId, String empId, String scorerId, String scorerType) {
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskScoreResultVO.class).setSql("SELECT t.*,k.kpi_item_name item_name,k.kpi_type_name,k.item_weight,k.kpi_type_classify,k.plus_limit,k.subtract_limit,k.max_extra_score,k.item_type,k.work_item_finish_value  " +
                "FROM perf_evaluate_task_score_result t  " +
                "LEFT JOIN (SELECT DISTINCT kpi_type_id, kpi_type_name,kpi_item_id,kpi_item_name,item_type,item_weight,kpi_type_classify,plus_limit,subtract_limit,max_extra_score,work_item_finish_value FROM perf_evaluate_task_kpi WHERE company_id = #{companyId} AND task_id = #{taskId} and emp_id = #{empId} and is_deleted = 'false' GROUP BY kpi_item_id) k ON k.kpi_item_id = t.kpi_item_id " +
                "WHERE t.company_id = #{companyId} AND t.task_id = #{taskId} AND t.audit_status = 'pass' AND t.emp_id = #{empId} AND t.is_deleted = 'false'  " +
                "AND t.scorer_id = #{scorerId} AND t.scorer_type = #{scorerType}");
        sqlBuilder.setValue("companyId", companyId);
        sqlBuilder.setValue("taskId", taskId);
        sqlBuilder.setValue("empId", empId);
        sqlBuilder.setValue("scorerId", scorerId);
        sqlBuilder.setValue("scorerType", scorerType);
        return autoBaseDao.listAll(sqlBuilder);
    }

    public void updateScoreById(String id, BigDecimal score, String updatedUser, String scoreComment) {
        if(StringUtils.isEmpty(id)) {
            return;
        }
        UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_score_result").set("score", score).set("score_comment", scoreComment)
                .set("updated_user", updatedUser).whereEq("id", id);
        autoBaseDao.update(updateBuilder);
    }

    public Boolean checkHasScoreAudit(String taskId, String empId) {
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskScoreResultModel.class)
                .whereEq("task_id", taskId)
                .whereEq("emp_id", empId)
                .whereEq("is_deleted", "false")
                .whereEq("audit_status", "pass")
                .whereNotIn("scorer_type", Arrays.asList(EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT.getScene(), EvaluateAuditSceneEnum.CHANGE_ITEM_AUDIT.getScene()));
        return CollectionUtils.isNotEmpty(autoBaseDao.listAll(queryBuilder));
    }

    public Boolean checkFinalResultAudit(String taskId, String empId, String scorerId) {
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskScoreResultModel.class).whereEq("task_id", taskId).whereEq("emp_id", empId).whereEq("is_deleted", "false")
                .whereEq("scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene()).whereEq("scorer_id", scorerId).appendWhere("(audit_status is null or audit_status = 'pass')");
        return CollectionUtils.isNotEmpty(autoBaseDao.listAll(queryBuilder));
    }

    public PagedList<PerfEvaluateTaskFinalAuditVO> queryBatchFinalAuditList(EvaluateTaskAuditQueryVO queryVO) {
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskFinalAuditVO.class).setSql("SELECT DISTINCT r.id,r.task_id,r.emp_id,b.cycle_start_date,b.cycle_end_date,e.`name` AS taskEmpName,b.level_group_id,b.task_name,b.templ_evaluate_json,u.final_score,u.evaluation_level,u.id as taskUserId,u.task_status,  " +
                "(select GROUP_CONCAT(o.org_name) FROM emp_organization o WHERE o.company_id = #{companyId} and o.org_id in (select org_id from emp_ref_org where emp_id = r.emp_id and ref_type = 'org')) as orgName   " +
                "FROM perf_evaluate_task_score_result r   " +
                "LEFT JOIN perf_evaluate_task_user u on r.task_id = u.task_id AND r.emp_id = u.emp_id AND u.is_deleted = 'false' AND u.company_id = #{companyId} " +
                "LEFT JOIN perf_evaluate_task_base b ON r.task_id = b.id AND b.is_deleted = 'false' AND b.company_id = #{companyId} " +
                "LEFT JOIN employee_base_info e ON r.emp_id = e.employee_id AND r.company_id = #{companyId}  " +
                "LEFT JOIN emp_ref_org ro ON ro.ref_type = 'org' AND ro.emp_id = r.emp_id AND ro.company_id = #{companyId}  " +
                "WHERE r.company_id = #{companyId} AND r.is_deleted = 'false' and u.task_status='resultsAuditing' AND scorer_type = 'final_result_audit' AND scorer_id = #{createdUser} AND (r.audit_status is null or r.audit_status = '')  " +
                "AND b.is_deleted = 'false' AND u.is_deleted = 'false'");
        sqlBuilder.setValue("companyId", queryVO.getCompanyId());
        sqlBuilder.setValue("createdUser", queryVO.getCreatedUser());

        if(StringUtils.isNotBlank(queryVO.getEmpId())) {
            sqlBuilder.append("AND r.emp_id in ").append(StringTool.getInStr(queryVO.getEmpId()).toString());
        }
        if(StringUtils.isNotBlank(queryVO.getOrgId())) {
            sqlBuilder.append("AND ro.org_id in ").append(StringTool.getInStr(queryVO.getOrgId()).toString());
        }
        if(StringUtils.isNotBlank(queryVO.getTaskId())) {
            sqlBuilder.append("AND r.task_id in ").append(StringTool.getInStr(queryVO.getTaskId()).toString());
        }

        sqlBuilder.appendIfOpt("AND b.cycle_start_date = #{cycleStartDate}", "cycleStartDate", queryVO.getCycleStartDate());
        sqlBuilder.appendIfOpt("AND b.cycle_end_date = #{cycleEndDate}", "cycleEndDate", queryVO.getCycleEndDate());

        if(StringUtils.isNotBlank(queryVO.getScoreOrderBy())) {
            sqlBuilder.append("ORDER BY u.final_score ").append(queryVO.getScoreOrderBy());
        } else {
            sqlBuilder.append("ORDER BY r.created_time DESC");
        }

        sqlBuilder.setPage(queryVO.getPageNo(), queryVO.getPageSize());
        PagedList<PerfEvaluateTaskFinalAuditVO> dataList = autoBaseDao.listPage(sqlBuilder);

        if(CollectionUtils.isEmpty(dataList.getData())){
            return dataList;
        }
        List<PerfEvaluateTaskFinalAuditVO> customLevels = dataList.getData().stream().filter(a -> StringUtils.
                isNotBlank(a.getLevelGroupId())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(customLevels)) {
            return dataList;
        }
        List<String> ids = customLevels.stream().map(level -> level.getLevelGroupId()).collect(Collectors.toList());
        List<EvaLevelGroup> levels = levelDao.levelGroups(queryVO.getCompanyId(), ids);
        if(CollectionUtils.isEmpty(levels)){
            return dataList;
        }
        Map<String, EvaLevelGroup> map = levels.stream().collect(Collectors.toMap(EvaLevelGroup::getLevelGroupId, v -> v));
        dataList.getData().forEach(dat->{
            dat.setLevelGroup(map.get(dat.getLevelGroupId()));
        });
        return dataList;
    }

    public List<PerfEvaluateTaskScoreResultModel> queryRejectItemAudit(String companyId, String taskId, String empId, String scene, String taskAuditId, Integer order) {
        if(StringUtils.isAnyBlank(companyId, taskId, empId, scene)) {
            return null;
        }
        if(StringUtils.isEmpty(taskAuditId) && Objects.isNull(order)) {
            return null;
        }
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskScoreResultModel.class)
                .whereIn("audit_status", Arrays.asList("pass","skip"))
                .whereEq("company_id", companyId)
                .whereEq("task_id", taskId).whereEq("emp_id", empId).whereEq("scorer_type", scene).whereEq("is_deleted", "false")
                .whereEq("task_audit_id", taskAuditId).whereEq("approval_order", order);
        return autoBaseDao.listAll(queryBuilder);
    }

    public List<PerfEvaluateTaskScoreResultModel> queryNotAuditResultByScorerTypes(String companyId, String taskId, List<String> scorerType, String empId, String orgId) {
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskScoreResultModel.class)
                .whereEq("company_id", companyId)
                .whereEq("task_id", taskId)
                .whereIn("scorer_type", scorerType)
                .whereIsNull("audit_status")
                .whereEq("emp_id", empId)
                .whereEq("org_id", orgId)
                .whereEq("is_deleted", "false");
        return autoBaseDao.listAll(queryBuilder);
    }

    /**
     * 查询评分人信息
     *
     * @param companyId
     * @param taskId
     * @param createdUser
     * @return
     */
    public List<PerfEvaluateTaskScoreResultModel> queryScorerByScorerUser(String companyId, String taskId, String createdUser) {
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskScoreResultModel.class).whereEq("company_id", companyId)
                .whereEq("task_id", taskId).whereEq("scorer_id", createdUser).whereEq("is_deleted", "false");
        return autoBaseDao.listAll(queryBuilder);
    }

    public List<PerfEvaluateTaskScoreResultModel> listResult(String toEmpId, String companyId, String taskId, String empId) {
        ComQB<PerfEvaluateTaskScoreResultModel> com = ComQB.build(PerfEvaluateTaskScoreResultModel.class)
                .whereEq("is_deleted", "false")
                .whereEq("task_id", taskId)
                .whereEq("company_id", companyId)
                .whereEq("emp_id", empId)
                .whereEq("scorer_id", toEmpId)
                .appendWhere(" (audit_status !='transferred' or audit_status is null)");
        return autoBaseDao.listAll(com);
    }

    public List<String> listScoreEmp(String taskId, String employeeId) {
        ComQB<String> comQB = ComQB.build(PerfEvaluateTaskScoreResultModel.class)
                .clearSelect()
                .select(" scorer_id")
                .setRsType(String.class)
                .whereEq("task_id", taskId)
                .whereEq("emp_id", employeeId)
                .whereNotEq("audit_status", "transferred")
                .appendWhere(" scorer_type like '%score' ")
                .whereEq("is_deleted", "false");
        return autoBaseDao.listAll(comQB);
    }

    public Integer hasSelfScore(String companyId, String taskBaseId, String evalEmpId) {
        ComQB<Integer> comQB = ComQB.buildDiff(PerfEvaluateTaskScoreResultModel.class,"v_task_score_result","v")
                .clearSelect()
                .select(" count(1) cnt")
                .setRsType(Integer.class)
                .whereEq("companyId", companyId)
                .whereEq("empId", evalEmpId)
                .whereEq("task_id", taskBaseId)
                .appendWhere("scorer_type='self_score'")
                .appendWhere("audit_status='wait'")
                .whereEq("is_deleted", "false");
        final Integer cnt = autoBaseDao.findOne(comQB);
        return cnt > 0 ? 1 : 0;
    }

    public PerfEvaluateSaveScoreVO findSelfEvaluateScore(String taskId, String evalEmpId) {

        ComQB<PerfEvaluateTaskScoreResultModel> comQB = ComQB.buildDiff(PerfEvaluateTaskScoreResultModel.class,"v_task_score_result","v")
                .clearSelect().select(" kpi_type_id, kpi_item_id ")
                .whereEq("empId", evalEmpId)
                .whereEq("task_id", taskId)
                .appendWhere("scorer_type='self_score'")
                .whereEq("is_deleted", "false");
        List<PerfEvaluateTaskScoreResultModel> itemS = autoBaseDao.listAll(comQB);
        PerfEvaluateSaveScoreVO scores = new PerfEvaluateSaveScoreVO(itemS,"跳过自评",taskId,evalEmpId);
        return scores;
    }

    public List<EmployeeBaseInfoModel> excludeTransferredReceiver(PerfEvaluateTaskAuditModel auditModel) {
        ComQB result = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .whereEqReq("company_id", auditModel.getCompanyId())
                .whereEqReq("task_id", auditModel.getTaskId())
                .whereEqReq("emp_id", auditModel.getEmpId())
                .whereEqReq("task_audit_id", auditModel.getId())
                .whereEqReq("scorer_type", auditModel.getScene())
                .whereEq("is_deleted", Boolean.FALSE.toString());
        List<PerfEvaluateTaskScoreResultDo> resultDos = autoBaseDao.listAll(result);
        if (CollUtil.isEmpty(resultDos)) {
            return new ArrayList<>();
        }
        List<String> scoreIds = new ArrayList<>();
        Map<String, List<PerfEvaluateTaskScoreResultDo>> scoreMap = resultDos.stream().filter(r -> StringUtils.isNotBlank(r.getScorerId())).collect(Collectors.groupingBy(r -> r.getScorerId()));
        for (String k : scoreMap.keySet()) {
            List<PerfEvaluateTaskScoreResultDo> resultDoList = scoreMap.get(k);
            List<PerfEvaluateTaskScoreResultDo> transferred = resultDoList.stream().filter(r -> BusinessConstant.TRANSFERRED.equals(r.getAuditStatus())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(transferred)) {
                continue;
            }
            List<String> empIds = resultDoList.stream().map(re -> re.getScorerId()).collect(Collectors.toList());
            scoreIds.addAll(empIds);
        }
        if (CollUtil.isEmpty(scoreIds)) {
            return new ArrayList<>();
        }
        ComQB empComQb = ComQB.build(EmployeeBaseInfoModel.class)
                .whereInReq("employee_id", scoreIds);
        return autoBaseDao.listAll(empComQb);

        //ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "a")
       //        .join(EmployeeBaseInfoDo.class, "e")
       //        .appendOn("a.company_id=e.company_id and a.scorer_id=e.employee_id")
       //        .clearSelect().select("e.*").setRsType(EmployeeBaseInfoModel.class)
       //        .whereEqReq("a.company_id", auditModel.getCompanyId())
       //        .whereEqReq("a.task_id", auditModel.getTaskId())
       //        .whereEqReq("a.emp_id", auditModel.getEmpId())
       //        .whereEqReq("a.task_audit_id", auditModel.getId())
       //        .whereEq("a.is_deleted", Boolean.FALSE.toString())
       //        .whereEq("e.is_delete", Boolean.FALSE.toString())
       //        .whereEqReq("a.scorer_type", auditModel.getScene())
       //        .whereEq("a.audit_status", "reject")
       //        .groupBy("a.scorer_id");
        //return autoBaseDao.listAll(comQB);
    }

    public List<PerfEvaluateTaskScoreResultModel> listResultByScene(String companyId, String taskId, String empId, String scene) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultModel.class)
                .whereEq("company_id", companyId)
                .whereEq("task_id", taskId)
                .whereEq("emp_id", empId)
                .whereEq("scorer_type", scene)
                .appendWhere("audit_status is null")
                .whereEq("is_deleted", Boolean.FALSE.toString());
        return autoBaseDao.listAll(comQB);
    }
}
