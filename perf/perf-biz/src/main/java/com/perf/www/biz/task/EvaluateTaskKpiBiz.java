package com.perf.www.biz.task;

import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import com.perf.www.api.task.EvaluateTaskKpiApi;
import com.perf.www.common.constant.BusinessConstant;
import com.perf.www.common.em.EvaluateTypeEnum;
import com.perf.www.manager.task.PerfEvaluateTaskAuditManager;
import com.perf.www.manager.task.PerfEvaluateTaskKpiManager;
import com.perf.www.vo.task.audit.PerfEvaluateTaskAuditVO;
import com.perf.www.vo.task.item.PerfEvaluateTaskKpiItemDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional
public class EvaluateTaskKpiBiz implements EvaluateTaskKpiApi {
    @Resource
    private PerfEvaluateTaskKpiManager taskKpiManager;
    @Autowired
    private PerfEvaluateTaskAuditManager taskAuditManager;

    @Override
    public PerfEvaluateTaskKpiItemDetailVO queryKpiItemDetail(String taskId, String empId, String kpiItemId, String companyId) {
        PerfEvaluateTaskKpiItemDetailVO kpiItemDetail = taskKpiManager.queryKpiItemDetail(taskId, empId, kpiItemId);
        if (Objects.isNull(kpiItemDetail)) {
            return null;
        }
        //兼容指定评分人历史数据
        if (BusinessConstant.APPROVER_TYPE_USER.equals(kpiItemDetail.getScorerType())) {
            if (!kpiItemDetail.getScorerObjId().contains("{")) {
                String scorerObjId = "[{\"obj_type\":\"role\",\"objItems\":[]},{\"obj_type\":\"user\",\"objItems\":[{\"objId\":\"" + kpiItemDetail.getScorerObjId() + "\",\"objName\":\"" + kpiItemDetail.getItemScorerName() + "\"}]}]";
                kpiItemDetail.setScorerObjId(scorerObjId);
            }
        }

        if (EvaluateTypeEnum.CUSTOM.getType().equals(kpiItemDetail.getEvaluateType())) {
            setCustomerAudit(taskId, empId, kpiItemId, companyId, kpiItemDetail);
        }
        return kpiItemDetail;
    }

    private void setCustomerAudit(String taskId, String empId, String kpiItemId, String companyId, PerfEvaluateTaskKpiItemDetailVO kpiItemDetail) {
        List<PerfEvaluateTaskAuditVO> allAuditList = taskAuditManager.queryAllItemAuditList(companyId, taskId, empId, kpiItemId, null);
        if (CollectionUtils.isEmpty(allAuditList)) {
            return;
        }
        kpiItemDetail.setPeerScoreList(getByScene(allAuditList, EvaluateAuditSceneEnum.PEER_SCORE.getScene()));
        kpiItemDetail.setSubScoreList(getByScene(allAuditList, EvaluateAuditSceneEnum.SUB_SCORE.getScene()));
        kpiItemDetail.setSuperiorScoreList(getByScene(allAuditList, EvaluateAuditSceneEnum.SUPERIOR_SCORE.getScene()));
        kpiItemDetail.setAppointScoreList(getByScene(allAuditList, EvaluateAuditSceneEnum.APPOINT_SCORE.getScene()));
    }

    @NotNull
    private List<PerfEvaluateTaskAuditVO> getByScene(List<PerfEvaluateTaskAuditVO> allAuditList, String scene) {
        return allAuditList.stream().filter(l -> scene.equals(l.getScene())).collect(Collectors.toList());
    }


}
