package com.perf.www.biz.report;

import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.perf.www.api.report.ReportApi;
import com.perf.www.common.constant.BusinessConstant;
import com.perf.www.common.em.EvaluateTaskStatusEnum;
import com.perf.www.common.utils.bean.Convert;
import com.perf.www.common.utils.page.ModelPagedList;
import com.perf.www.cons.AdminType;
import com.perf.www.dao.company.AdminSetDao;
import com.perf.www.dao.company.EmpOrgDao;
import com.perf.www.domain.dto.company.EmpOrgSimpleDTO;
import com.perf.www.domain.entity.company.CompanyModel;
import com.perf.www.domain.entity.company.SystemAdminSetModel;
import com.perf.www.dto.EvaluateScoreSettingDTO;
import com.perf.www.dto.PerfEvaluateTaskUserDTO;
import com.perf.www.manager.dic.CompanyPerfLevelManager;
import com.perf.www.manager.dic.PerfEvaluationLevelManager;
import com.polaris.kpi.ind.infr.dao.CompanyKpiItemDao;
import com.perf.www.vo.report.year.KpiTypePo;
import com.polaris.kpi.eval.infr.task.dao.EvalKpiDao;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.EmpEvalTaskScore2Po;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.EmpDetailKpiTypePo;
import com.perf.www.manager.kpi.CompanyManager;
import com.perf.www.manager.perftmp.PerfTmplBaseManager;
import com.perf.www.manager.task.*;
import com.perf.www.model.perftmp.PerfTemplBaseModel;
import com.perf.www.model.task.*;
import com.perf.www.utils.ReportSeriesDataCalculator;
import com.perf.www.vo.dic.CompanyPerfLevelQuotaOrgVO;
import com.perf.www.vo.report.*;
import com.perf.www.vo.report.item.ReportItemScoreRankVO;
import com.perf.www.vo.report.query.ReportTaskQueryVO;
import com.perf.www.vo.report.year.ReportYearCountVO;
import com.perf.www.vo.report.year.ReportYearEmpTaskDetailVO;
import com.perf.www.vo.task.*;
import com.perf.www.vo.task.query.EvaluationTaskUserQueryVO;
import com.polaris.kpi.eval.infr.task.dao.EvaluateTaskDao;
import com.polaris.kpi.org.infr.dept.dao.KpiOrgDao;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.lufei.ibatis.mapper.PagedList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <h3>此处添加注释</h3>
 * created by Bruce.R on 2020/9/23
 */
@Slf4j
@Service
public class ReportBiz implements ReportApi {
    @Resource
    private PerfEvaluationLevelManager levelManager;
    @Resource
    private CompanyKpiItemDao kpiItemManager;
    @Resource
    private PerfTmplBaseManager tmplBaseManager;
    @Resource
    private PerfEvaluateTaskBaseManager taskBaseManager;
    @Resource
    private PerfEvaluateTaskUserManager taskUserManager;
    @Resource
    private PerfEvaluateTaskKpiManager taskKpiManager;
    @Resource
    private PerfEvaluateTaskActionManager taskActionManager;
    @Resource
    private AdminSetDao adminSetDao;
    @Autowired
    private EmpOrgDao empOrgDao;
    @Autowired
    private CompanyPerfLevelManager perfLevelManager;
    @Autowired
    private CompanyManager companyManager;
    @Autowired
    private KpiOrgDao kpiOrgDao;
    @Autowired
    private EvalKpiDao kpiDao;

    @Override
    public List<ReportTopCountOptionVO> listYearCountOption(ReportTaskQueryVO queryVO) {
        List<ReportTopCountOptionVO> optionList = new ArrayList<>();
        //考核任务数
        BigDecimal baseTaskCount = taskBaseManager.countBaseTask(queryVO.getCompanyId(), queryVO.getYear());
        //考核人次
        BigDecimal taskUserCount = taskUserManager.countEvaluateTaskUser(queryVO.getCompanyId(), queryVO.getYear());
        //考核指标
        BigDecimal taskKpiCount = kpiItemManager.countCompanyKpiItem(queryVO.getCompanyId(), queryVO.getYear());
        //绩效模板
        BigDecimal tmplBaseCount = tmplBaseManager.countUsedTmplBase(queryVO.getCompanyId(), queryVO.getYear());
        optionList.add(ReportTopCountOptionVO.builder().enName("Evaluate Tasks").chName("考核任务").count(baseTaskCount).build());
        optionList.add(ReportTopCountOptionVO.builder().enName("Evaluate People-Times").chName("考核人次").count(taskUserCount).build());
        optionList.add(ReportTopCountOptionVO.builder().enName("Evaluate Indicators").chName("考核指标").count(taskKpiCount).build());
        optionList.add(ReportTopCountOptionVO.builder().enName("KPI Templates").chName("绩效模板").count(tmplBaseCount).build());
        return optionList;
    }

    public void getAllChildOrgIds(ReportTaskQueryVO queryVO) {
        if (StringUtils.isBlank(queryVO.getOrgId())) {
            return;
        }
        List<String> orgIds = kpiOrgDao.listAllChildOrgIds(new TenantId(queryVO.getCompanyId()),
                Arrays.asList(queryVO.getOrgId().split(",")));
        if (CollUtil.isEmpty(orgIds)) {
            return;
        }
        queryVO.setOrgId(String.join(",", orgIds));
    }


    public ModelPagedList<ReportTaskAverageCountVO> listPagedYearTaskCount(ReportTaskQueryVO reportTaskQueryVO, String adminEmpId) {
        getAllChildOrgIds(reportTaskQueryVO);
        getOrgIds(reportTaskQueryVO);
        if (reportTaskQueryVO.getNoOrgPerm()) {
            return ModelPagedList.createEmptyData(reportTaskQueryVO.getPageNo(), reportTaskQueryVO.getPageSize());
        }
        return ModelPagedList.convertToModelPage(taskUserManager.listPagedYearTaskCount(reportTaskQueryVO));
    }

    private void getOrgIds(ReportTaskQueryVO queryVO) {
        //只能查看有权限的数据
        queryVO.setNoOrgPerm(false);
        //是否是主管理员或应用管理员
        SystemAdminSetModel admin = adminSetDao.findByEmpId(queryVO.getCompanyId(), queryVO.getCreatedUser());
        if (Objects.isNull(admin)) {
            //非管理员，没有查看权限
            queryVO.setNoOrgPerm(true);
            return;
        }

        //主管理员或者应用管理员
        if (Arrays.asList(AdminType.MAIN, AdminType.APP).contains(admin.getAdminType())
                || SystemAdminSetModel.ManagePurview.COMPANY.equals(admin.getManagePurview().getManageScope())) {
            return;
        }

        List<String> orgIdList = adminSetDao.extractManageOrgId(admin);
        //没有传部门参数，默认可以查自己有权限的部门
        if (SystemAdminSetModel.ManagePurview.CURRENT_ORG.equals(admin.getManagePurview().getManageScope())) {
            if (StringUtils.isBlank(queryVO.getOrgId())) {
                queryVO.setOrgId(StringUtils.join(orgIdList, ","));
                if (StringUtils.isBlank(queryVO.getOrgId())) {
                    queryVO.setNoOrgPerm(true);
                }
                return;
            }
            List<String> queryOrgIds = Arrays.asList(queryVO.getOrgId().split(","));
            queryOrgIds = queryOrgIds.stream().filter(o -> orgIdList.contains(o)).collect(Collectors.toList());
            if (CollUtil.isEmpty(queryOrgIds)) {
                queryVO.setNoOrgPerm(true);
                return;
            }
            queryVO.setOrgId(StringUtils.join(queryOrgIds, ","));
            return;
        }
        List<String> orgIds = kpiOrgDao.listAllChildOrgIds(new TenantId(queryVO.getCompanyId()), orgIdList);
        if (StringUtils.isNotBlank(queryVO.getOrgId())) {
            List<String> queryOrgIds = Arrays.asList(queryVO.getOrgId().split(","));
            queryOrgIds = queryOrgIds.stream().filter(o -> orgIds.contains(o)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(queryOrgIds)) {
                queryVO.setNoOrgPerm(true);
                return;
            }
            queryVO.setOrgId(StringUtils.join(queryOrgIds, ","));
            return;
        }
        queryVO.setOrgId(StringUtils.join(orgIds, ","));
        if (StringUtils.isBlank(queryVO.getOrgId())) {
            queryVO.setNoOrgPerm(true);
            return;
        }
    }


    public ModelPagedList<PerfEvaluateTaskUserDTO> listPagedUserTask(ReportTaskQueryVO queryVO) {
        return ModelPagedList.convertToModelPage(taskUserManager.listPagedEvaluateTaskUser(queryVO));
    }

    @Override
    public ReportUserTaskCountVO countUserTask(String companyId, String empId, String orgId, String year) {
        return taskUserManager.countUserTask(companyId, empId, orgId, year);
    }

    @Override
    public List<ReportSeriesDataVO> listTaskUserSeries(String companyId, String taskId) {
        List<ReportSeriesDataVO> list = new ArrayList<>();
        Map<String, ReportSeriesDataVO> map = new HashMap<>(EvaluateTaskStatusEnum.values().length * 2);
        EvaluateTaskStatusEnum.getStatusList().forEach(s -> {
            ReportSeriesDataVO data = ReportSeriesDataVO.builder().name(s).value(BigDecimal.ZERO).build();
            list.add(data);
            map.put(s, data);
        });
        List<PerfEvaluateTaskUserDTO> taskUsers = taskUserManager.listEvaluateTaskUser(companyId, null, null, taskId, null);
        if (CollectionUtils.isNotEmpty(taskUsers)) {
            taskUsers.stream()
                    .collect(Collectors.groupingBy(PerfEvaluateTaskUserModel::getTaskStatus))
                    .forEach((taskStatus, users) -> {
                        ReportSeriesDataVO data = map.get(taskStatus);
                        if (data != null) {
                            data.setValue(BigDecimal.valueOf(users.size()));
                        }
                    });
        }
        return ReportSeriesDataCalculator.create().addAll(list).calculatePercentage().getDataList();
    }

    @Override
    public List<ReportSeriesDataVO> listMeasurableItemProgressSeries(String companyId, String taskId) {
        Map<String, Object> result = initProgressSeries();
        List<ReportSeriesDataVO> list = (List<ReportSeriesDataVO>) result.get("list");
        Map<String, ReportSeriesDataVO> map = (Map<String, ReportSeriesDataVO>) result.get("map");
        List<PerfEvaluateTaskKpiModel> kpiList = taskKpiManager.listMeasurableItemProgress(companyId, taskId);
        if (CollectionUtils.isNotEmpty(kpiList)) {
            kpiList.stream().collect(Collectors.groupingBy(kpi -> {
                if (kpi.getItemFinishValue() == null) {
                    kpi.setItemFinishValue(BigDecimal.ZERO);
                }
                //完成度0~100
                BigDecimal progress;
                if (Objects.isNull(kpi.getItemTargetValue()) || kpi.getItemTargetValue().compareTo(BigDecimal.ZERO) == 0) {
                    progress = new BigDecimal("100");
                } else {
                    progress = kpi.getItemFinishValue().divide(kpi.getItemTargetValue(), 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                }
                return arrangeProgress(progress);
            })).forEach((progress, kpis) -> {
                ReportSeriesDataVO seriesDataVO = map.get(progress);
                if (seriesDataVO != null) {
                    seriesDataVO.setValue(BigDecimal.valueOf(kpis.size()));
                }
            });
        }

        return list;
    }

    @Override
    public List<ReportSeriesDataVO> listMeasurableItemProgressSeriesForMobile(String companyId, String taskId) {
        Map<String, Object> result = initProgressSeriesForMobile();
        List<ReportSeriesDataVO> list = (List<ReportSeriesDataVO>) result.get("list");
        Map<String, ReportSeriesDataVO> map = (Map<String, ReportSeriesDataVO>) result.get("map");
        List<PerfEvaluateTaskKpiModel> kpiList = taskKpiManager.listMeasurableItemProgress(companyId, taskId);
        if (CollectionUtils.isNotEmpty(kpiList)) {
            kpiList.stream().collect(Collectors.groupingBy(kpi -> {
                if (kpi.getItemFinishValue() == null) {
                    kpi.setItemFinishValue(BigDecimal.ZERO);
                }
                //完成度0~100
                BigDecimal progress;
                if (Objects.isNull(kpi.getItemTargetValue()) || kpi.getItemTargetValue().compareTo(BigDecimal.ZERO) == 0) {
                    progress = new BigDecimal("100");
                } else {
                    progress = kpi.getItemFinishValue().divide(kpi.getItemTargetValue(), 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                }
                return arrangeProgressForMobile(progress);
            })).forEach((progress, kpis) -> {
                ReportSeriesDataVO seriesDataVO = map.get(progress);
                if (seriesDataVO != null) {
                    seriesDataVO.setValue(BigDecimal.valueOf(kpis.size()));
                }
            });
        }

        return list;
    }

    @Override
    public List<ReportSeriesDataVO> listTaskActionProgressSeries(String companyId, String taskId) {
        Map<String, Object> result = initProgressSeries();
        List<ReportSeriesDataVO> list = (List<ReportSeriesDataVO>) result.get("list");
        Map<String, ReportSeriesDataVO> map = (Map<String, ReportSeriesDataVO>) result.get("map");
        List<PerfEvaluateTaskActionModel> actionList = taskActionManager.listTaskAction(companyId, taskId);
        if (CollectionUtils.isNotEmpty(actionList)) {
            actionList.stream().collect(Collectors.groupingBy(a -> {
                a.setProgress(a.getProgress() == null ? BigDecimal.ZERO : a.getProgress());
                return arrangeProgress(a.getProgress());
            })).forEach((progress, actions) -> {
                ReportSeriesDataVO seriesDataVO = map.get(progress);
                if (seriesDataVO != null) {
                    seriesDataVO.setValue(BigDecimal.valueOf(actions.size()));
                }
            });
        }
        return list;
    }

    private Map<String, Object> initProgressSeries() {
        Map<String, Object> result = new HashMap<>(4);
        List<ReportSeriesDataVO> list = new ArrayList<>(8);
        Map<String, ReportSeriesDataVO> map = new HashMap<>(8);
        result.put("list", list);
        result.put("map", map);
        ReportSeriesDataVO data1 = ReportSeriesDataVO.builder().name("0% ~ 50%").value(BigDecimal.ZERO).build();
        list.add(data1);
        map.put("0% ~ 50%", data1);
        ReportSeriesDataVO data2 = ReportSeriesDataVO.builder().name("50% ~ 70%").value(BigDecimal.ZERO).build();
        list.add(data2);
        map.put("50% ~ 70%", data2);
        ReportSeriesDataVO data3 = ReportSeriesDataVO.builder().name("70% ~ 90%").value(BigDecimal.ZERO).build();
        list.add(data3);
        map.put("70% ~ 90%", data3);
        ReportSeriesDataVO data4 = ReportSeriesDataVO.builder().name("90% ~ 100%").value(BigDecimal.ZERO).build();
        list.add(data4);
        map.put("90% ~ 100%", data4);
        return result;
    }

    private Map<String, Object> initProgressSeriesForMobile() {
        Map<String, Object> result = new HashMap<>(4);
        List<ReportSeriesDataVO> list = new ArrayList<>(8);
        Map<String, ReportSeriesDataVO> map = new HashMap<>(8);
        result.put("list", list);
        result.put("map", map);
        ReportSeriesDataVO data1 = ReportSeriesDataVO.builder().name("<50%").value(BigDecimal.ZERO).build();
        list.add(data1);
        map.put(data1.getName(), data1);
        ReportSeriesDataVO data2 = ReportSeriesDataVO.builder().name(">=50%").value(BigDecimal.ZERO).build();
        list.add(data2);
        map.put(data2.getName(), data2);
        ReportSeriesDataVO data3 = ReportSeriesDataVO.builder().name(">=80%").value(BigDecimal.ZERO).build();
        list.add(data3);
        map.put(data3.getName(), data3);
        return result;
    }

    @NotNull
    private String arrangeProgress(BigDecimal progress) {
        String groupKey;
        if (progress.compareTo(BigDecimal.ZERO) >= 0 && progress.compareTo(BigDecimal.valueOf(50)) < 0) {
            groupKey = "0% ~ 50%";
        } else if (progress.compareTo(BigDecimal.valueOf(50)) >= 0 && progress.compareTo(BigDecimal.valueOf(70)) < 0) {
            groupKey = "50% ~ 70%";
        } else if (progress.compareTo(BigDecimal.valueOf(70)) >= 0 && progress.compareTo(BigDecimal.valueOf(90)) < 0) {
            groupKey = "70% ~ 90%";
        } else {
            groupKey = "90% ~ 100%";
        }
        return groupKey;
    }

    @NotNull
    private String arrangeProgressForMobile(BigDecimal progress) {
        String groupKey;
        if (progress.compareTo(BigDecimal.ZERO) >= 0 && progress.compareTo(BigDecimal.valueOf(50)) < 0) {
            groupKey = "<50%";
        } else if (progress.compareTo(BigDecimal.valueOf(50)) >= 0 && progress.compareTo(BigDecimal.valueOf(80)) < 0) {
            groupKey = ">=50%";
        } else {
            groupKey = ">=80%";
        }
        return groupKey;
    }

    @Override
    public List<ReportSeriesDataVO> listEmpDistribution(String companyId, String taskId, String type) {
        //TODO: 暂无岗位职位的处理
        final String ORG = "org", ROLE = "role", POST = "post";
        List<ReportSeriesDataVO> list = new ArrayList<>();
        List<PerfEvaluateTaskUserDTO> userList = taskUserManager.listEvaluateTaskUser(companyId, null, null, taskId, null);
        if (CollectionUtils.isNotEmpty(userList)) {
            Map<String, ReportSeriesDataVO> orgMap = new HashMap<>();
            userList.stream().filter(dto -> StringUtils.isNotEmpty(dto.getOrgName())).collect(Collectors.groupingBy(PerfEvaluateTaskUserDTO::getOrgName))
                    .forEach((orgNames, empList) -> {
                        for (String orgName : orgNames.split(",")) {
                            ReportSeriesDataVO seriesDataVO = orgMap.get(orgName);
                            if (seriesDataVO == null) {
                                seriesDataVO = ReportSeriesDataVO.builder().name(orgName).value(BigDecimal.ZERO).build();
                                list.add(seriesDataVO);
                                orgMap.put(orgName, seriesDataVO);
                            }
                            seriesDataVO.setValue(seriesDataVO.getValue().add(BigDecimal.valueOf(empList.size())));
                        }
                    });
        }
        return list;
    }

    //@Override
    //public List<ReportCountResultVO> listTaskUserLevelDistribution(ReportTaskQueryVO queryVO) {
    //    List<ReportCountResultVO> list = new ArrayList<>();
    //    String companyId = queryVO.getCompanyId();
    //    //权限
    //    getOrgIds(queryVO);
    //    if (queryVO.getNoOrgPerm()) {
    //        return null;
    //    }
    //    //查询公司所有等级 等级排序规则，系统按分数-系统按排名
    //    LinkedHashSet<String> levelList = new LinkedHashSet<>();
    //    List<String> systemLevel = levelManager.querySystemLevelName(companyId);
    //    if (CollectionUtils.isNotEmpty(systemLevel)) {
    //        levelList.addAll(systemLevel);
    //    }
    //    List<String> distributionLevel = levelManager.queryDistributionLevelName(companyId);
    //    if (CollectionUtils.isNotEmpty(distributionLevel)) {
    //        levelList.addAll(distributionLevel);
    //    }
    //    if (CollectionUtils.isEmpty(levelList)) {
    //        return list;
    //    }
    //
    //    Map<String, Integer> levelMap = new HashMap<>();
    //    int order = 0;
    //    for (String level : levelList) {
    //        levelMap.put(level, order++);
    //    }
    //
    //    list = taskUserManager.queryLevelCount(queryVO);
    //    if (CollectionUtils.isEmpty(list)) {
    //        return list;
    //    }
    //
    //    for (ReportCountResultVO reportSeriesDataVO : list) {
    //        reportSeriesDataVO.setOrder(levelMap.get(reportSeriesDataVO.getName()));
    //        if (Objects.isNull(reportSeriesDataVO.getOrder())) {
    //            levelMap.put(reportSeriesDataVO.getName(), order++);
    //            reportSeriesDataVO.setOrder(levelMap.get(reportSeriesDataVO.getName()));
    //        }
    //    }
    //
    //    //排序
    //    list = list.stream().sorted(Comparator.comparing(ReportCountResultVO::getOrder)).collect(Collectors.toList());
    //    //stepId 替换为gradeName
    //    List<String> stepIds = list.stream().map(li -> li.getStepId()).collect(Collectors.toList());
    //    List<GradeStepDo> gradeStepDos = gradeDao.listGradeStepByIds(stepIds);
    //    Map<String, List<GradeStepDo>> gradeStepMap = gradeStepDos.stream().collect(Collectors.groupingBy(grade -> grade.getId()));
    //    for (ReportCountResultVO li : list) {
    //        if (StringUtils.isEmpty(li.getStepId())) {
    //            continue;
    //        }
    //        List<GradeStepDo> gradeStepDoList = gradeStepMap.get(li.getName());
    //        if (CollectionUtils.isNotEmpty(gradeStepDoList)) {
    //            li.setName(gradeStepDoList.get(0).getName());
    //        }
    //    }
    //    return list;
    //}

    /**
     * 钉钉工作台
     *
     * @param corpId
     * @return
     */
    @Override
    public WorkbenchVo ddWorkbench(String corpId) {

        CompanyModel company = this.companyManager.queryCompanyByCorpId(corpId);
        if (company == null) {
            return null;
        }

        String companyId = company.getId();

        PerfEvaluateTaskBaseModel taskBaseModel = this.taskBaseManager.latestTask(companyId);
        if (taskBaseModel == null) {
            return null;
        }

        List<WorkbenchVo.Level> list = new ArrayList<>();
        LinkedHashSet<String> levelList = new LinkedHashSet<>();
        List<String> systemLevel = levelManager.querySystemLevelName(companyId);
        if (CollectionUtils.isNotEmpty(systemLevel)) {
            levelList.addAll(systemLevel);
        }
        List<String> distributionLevel = levelManager.queryDistributionLevelName(companyId);
        if (CollectionUtils.isNotEmpty(distributionLevel)) {
            levelList.addAll(distributionLevel);
        }

        if (!CollectionUtils.isEmpty(levelList)) {
            ReportTaskQueryVO queryVO = new ReportTaskQueryVO();
            queryVO.setCompanyId(companyId);

            List<ReportCountResultVO> resultVOList = taskUserManager.queryLevelCount(queryVO);
            if (!CollectionUtils.isEmpty(resultVOList)) {
                int sum = resultVOList.stream().mapToInt(ReportCountResultVO::getValue).sum();
                resultVOList.forEach(item -> {
                    WorkbenchVo.Level level = new WorkbenchVo.Level(item.getValue(), item.getName());
                    level.calculateRate(sum);
                    list.add(level);
                });
            }
        }

        PerfEvaluateTaskCoachUserQueryVo queryVO = new PerfEvaluateTaskCoachUserQueryVo();
        queryVO.setCompanyId(companyId);
        queryVO.setTaskId(taskBaseModel.getId());
        queryVO.setPageSize(100);
        queryVO.setPageNo(1);
        List<PerfEvaluateTaskCoachUserVo> coachUserVos = this.taskUserManager.pageCoachUserList(queryVO);

        int zeroCount = 0;
        int oneCount = 0;
        int twoCount = 0;
        int threeCount = 0;
        int total = coachUserVos.size();

        for (PerfEvaluateTaskCoachUserVo coachUserVo : coachUserVos) {
            if (coachUserVo.getCoachNum() == null || 0 == coachUserVo.getCoachNum()) {
                zeroCount++;
            } else if (1 == coachUserVo.getCoachNum()) {
                oneCount++;
            } else if (2 == coachUserVo.getCoachNum()) {
                twoCount++;
            } else {
                threeCount++;
            }
        }

        List<WorkbenchVo.Counseling> counselings = new ArrayList<>();
        WorkbenchVo.Counseling counseling = new WorkbenchVo.Counseling(0, zeroCount);
        counseling.calculateRate(total);
        counselings.add(counseling);

        counseling = new WorkbenchVo.Counseling(1, oneCount);
        counseling.calculateRate(total);
        counselings.add(counseling);

        counseling = new WorkbenchVo.Counseling(2, oneCount);
        counseling.calculateRate(total);
        counselings.add(counseling);


        counseling = new WorkbenchVo.Counseling(3, oneCount);
        counseling.calculateRate(total);
        counselings.add(counseling);

        WorkbenchVo workbenchVo = new WorkbenchVo(taskBaseModel.getTaskName(), taskBaseModel.getId(), counselings, list);

        return workbenchVo;
    }

    @Override
    public List<ReportSeriesDataVO> listTaskUserScoreDistribution(String companyId, String taskId) {
        List<ReportSeriesDataVO> list = new ArrayList<>();
        List<PerfEvaluateTaskUserDTO> userList = taskUserManager.listEvaluateTaskUser(companyId, null, null, taskId, null);
        if (CollectionUtils.isNotEmpty(userList)) {
            userList.stream().filter(u -> EvaluateTaskStatusEnum.FINISHED.getStatus().equals(u.getTaskStatus()))
                    .collect(Collectors.groupingBy(PerfEvaluateTaskUserDTO::getFinalScore))
                    .forEach((score, empList) -> {
                        list.add(ReportSeriesDataVO.builder().name(score.toString()).value(BigDecimal.valueOf(empList.size())).build());
                    });
        }
        return list;
    }


    public ModelPagedList<ReportEmpScoreRankVO> listPagedEmpTaskRank(ReportTaskQueryVO queryVO) {
        getAllChildOrgIds(queryVO);
        getOrgIds(queryVO);
        if (queryVO.getNoOrgPerm()) {
            return ModelPagedList.createEmptyData(queryVO.getPageNo(), queryVO.getPageSize());
        }
        return ModelPagedList.convertToModelPage(taskUserManager.queryEmpRank(queryVO));
    }

    @Override
    public List<String> queryEmpRankLevel(ReportTaskQueryVO queryVO) {
        getAllChildOrgIds(queryVO);
        return taskUserManager.queryEmpRankLevel(queryVO);
    }

//    @Override
//    public ReportTaskScoreTableVO listTaskUserScoreResult(String companyId, String taskId, String taskEmpId, String keyword, Integer pageNo, Integer pageSize, String createdUser) {
//        /*
//        1、查询考核员工列表
//        2、查询考核得分结果列表(按指标项打分，各评分阶段给考核任务打总分）
//        3、按员工、指标类整理得分结果
//         */
//        ReportTaskScoreTableVO tableVO = new ReportTaskScoreTableVO();
//
//        PerfEvaluateTaskBaseModel taskBaseModel = taskBaseManager.findById(taskId);
//        //类别权重开关
//        String tempBaseJson = taskBaseModel.getTemplBaseJson();
//        if (StringUtils.isNotBlank(tempBaseJson) && !"{}".equals(tempBaseJson)) {
//            PerfTemplBaseModel tempBaseModel = JSONObject.parseObject(tempBaseJson, PerfTemplBaseModel.class);
//            if (Objects.nonNull(taskBaseModel)) {
//                tableVO.setTypeWeightSwitch(tempBaseModel.getTypeWeightSwitch());
//            }
//        }
//
//        PerfTemplEvaluateVO perfTemplEvaluateVO = taskBaseManager.getEvaluate(companyId, "");
//        //PerfTemplEvaluateVO perfTemplEvaluateVO=taskBaseModel.parseTemplEvaluateVO();
//        //查询自定义指标流程
//        List<PerfEvaluateTaskItemScoreRuleModel> ruleModels = itemScoreRuleManager.listItemScoreRule(taskBaseModel.getCompanyId(), taskId);
//        perfTemplEvaluateVO.setItemEvaluateList(Convert.convertListOnlyMatch(ruleModels, PerfTemplItemEvaluateVO.class));
//        //查询是否有互评流程
//        List<PerfEvaluateTaskAuditModel> peerScoreAuditList = taskAuditManager.queryAuditListByItem(companyId, taskId, taskEmpId, null, EvaluateAuditSceneEnum.PEER_SCORE.getScene());
//        if (CollectionUtils.isNotEmpty(peerScoreAuditList)) {
//            perfTemplEvaluateVO.setPeerScoreFlag(BusinessConstant.TRUE);
//        }
//        List<PerfEvaluateTaskAuditModel> subScoreAuditList = taskAuditManager.queryAuditListByItem(companyId, taskId, taskEmpId, null, EvaluateAuditSceneEnum.SUB_SCORE.getScene());
//        if (CollectionUtils.isNotEmpty(subScoreAuditList)) {
//            perfTemplEvaluateVO.setSubScoreFlag(BusinessConstant.TRUE);
//        }
//
//        tableVO.setTemplEvaluateInfo(perfTemplEvaluateVO);
//
//        //查询当前操作人是否模板管理员
//        boolean tempAdmin = adminSetDao.checkTempAdmin(companyId, taskBaseModel.getTemplBaseId(), createdUser);
//
//        //查当前满分值
//        String fullScoreValue = companySysSettingManager.querySettingValue(companyId, CompanySysSettingModel.SETTING_TYPE_FULL_SCORE);
//        BigDecimal fullScore = StringUtils.isNoneBlank(fullScoreValue) ? new BigDecimal(fullScoreValue) : null;
//        String scoreRangeType = null;
//        if (StringUtils.isNotEmpty(taskBaseModel.getTemplBaseJson()) && !"{}".equals(taskBaseModel.getTemplBaseJson())) {
//            PerfTemplBaseInfoVO templBaseInfoVO = JSONObject.parseObject(taskBaseModel.getTemplBaseJson(), PerfTemplBaseInfoVO.class);
//            scoreRangeType = templBaseInfoVO.getScoreRangeType();
//        }
//
//        //查是否有指定评分
//        if (taskKpiManager.checkHasItemScore(companyId, taskId) || taskAuditManager.checkHasAppointScore(companyId, taskId)) {
//            tableVO.setItemScoreFlag(BusinessConstant.TRUE);
//        }
//
//        PagedList<PerfEvaluateTaskUserDTO> taskUserList = taskUserManager.listPagedEvaluateTaskUser(companyId, taskEmpId, taskId, null, keyword, pageNo, pageSize);
//        if (CollectionUtils.isNotEmpty(taskUserList)) {
//            List<String> orgIds = new ArrayList<>();
//            List<String> empIds = new ArrayList<>();
//            Map<String, ReportTaskUserVO> userMap = taskUserList.getData().stream()
//                    .map(u -> {
//                        orgIds.add(u.getOrgId());
//                        empIds.add(u.getEmpId());
//                        return JSONObject.parseObject(JSONObject.toJSONString(u), ReportTaskUserVO.class);
//                    }).collect(Collectors.toMap(ReportTaskUserVO::getEmpId, o -> o));
//            //2、考核得分结果列表
//            List<PerfEvaluateTaskScoreResultVO> resultList = taskScoreResultManager.listTaskScoreResults(companyId, taskId, null, empIds);
//            //2.1、构建自动打分结果列表
//            List<PerfEvaluateTaskKpiVO> taskKpiInfoList = taskKpiManager.listEvaluateTaskKpi(companyId, taskId, null);
//            if (CollectionUtils.isNotEmpty(taskKpiInfoList)) {
//                List<PerfEvaluateTaskScoreResultVO> autoScoreResultList = taskKpiInfoList.stream().filter(
//                        kpi -> empIds.contains(kpi.getEmpId()) && (ScoreTypeEnum.AUTO.getType().equals(kpi.getScorerType()) || kpi.getOpenOkrScore() == 1)
//                ).map(kpi -> {
//                    PerfEvaluateTaskScoreResultVO resultVO = Convert.convertOnlyMatch(kpi, PerfEvaluateTaskScoreResultVO.class);
//                    resultVO.setScorerType(EvaluateAuditSceneEnum.TOTAL_AUTO_SCORE.getScene());
//                    resultVO.setFinalScore(kpi.getItemAutoScore());
//                    resultVO.setItemScorerType(BusinessConstant.SCORER_TYPE_AUTO);
//                    return resultVO;
//                }).collect(Collectors.toList());
//                if (CollectionUtils.isNotEmpty(autoScoreResultList)) {
//                    resultList.addAll(autoScoreResultList);
//                }
//            }
//            if (CollectionUtils.isNotEmpty(resultList)) {
//                List<PerfEvaluateTaskKpiVO> taskKpiList = taskKpiManager.queryTaskApiItemByTaskId(taskId);
//                final OkrItemList okrItems = new OkrItemList(taskKpiList);
//                // fix 11983
//                setOKRInfo(companyId, taskKpiList);
//                //3、按员工、打分类型+指标类分组，这里主要是区分是打总分还是按指标打分
//                Map<String, LinkedHashMap<String, List<PerfEvaluateTaskScoreResultVO>>> map = resultList.stream().collect(
//                        Collectors.groupingBy(PerfEvaluateTaskScoreResultModel::getEmpId,
//                                Collectors.groupingBy(r -> getScoreTypeKey(r.getScorerType(), r.getKpiTypeId()), LinkedHashMap::new, Collectors.toList())));
//                for (String empId : map.keySet()) {
//                    LinkedHashMap<String, List<PerfEvaluateTaskScoreResultVO>> typeScoreMap = map.get(empId);
//                    ReportTaskUserVO taskUserVO = userMap.get(empId);
//                    //3.1、考核总分详情
//                    List<ReportTaskUserTotalScoreVO> totalScoreList = new ArrayList<>(8);
//
//                    //3.1.1、自评总分详情
//                    totalScoreList.add(processTotalScoreDetail(taskBaseModel.getTemplBaseJson(), typeScoreMap, EvaluateAuditSceneEnum.TOTAL_SELF_SCORE.getScene(), EvaluateAuditSceneEnum.SELF_SCORE.getScene(), perfTemplEvaluateVO.getSelfScoreRule()));
//                    //3.1.2、互评总分详情
//                    totalScoreList.add(processTotalScoreDetail(taskBaseModel.getTemplBaseJson(), typeScoreMap, EvaluateAuditSceneEnum.TOTAL_PEER_SCORE.getScene(), EvaluateAuditSceneEnum.PEER_SCORE.getScene(), perfTemplEvaluateVO.getMutualScoreRule()));
//                    totalScoreList.add(processTotalScoreDetail(taskBaseModel.getTemplBaseJson(), typeScoreMap, EvaluateAuditSceneEnum.TOTAL_SUB_SCORE.getScene(), EvaluateAuditSceneEnum.SUB_SCORE.getScene(), null));
//                    //3.1.3、上级评分详情
//                    totalScoreList.add(processTotalScoreDetail(taskBaseModel.getTemplBaseJson(), typeScoreMap, EvaluateAuditSceneEnum.TOTAL_SUPERIOR_SCORE.getScene(), EvaluateAuditSceneEnum.SUPERIOR_SCORE.getScene(), perfTemplEvaluateVO.getSuperiorScoreRule()));
//                    //3.1.4、指定评分详情
//                    totalScoreList.add(processTotalScoreDetail(taskBaseModel.getTemplBaseJson(), typeScoreMap, EvaluateAuditSceneEnum.ITEM_SCORE.getScene(), EvaluateAuditSceneEnum.ITEM_SCORE.getScene(), null));
//                    //自定义流程指定评分
//                    totalScoreList.add(processTotalScoreDetail(taskBaseModel.getTemplBaseJson(), typeScoreMap, EvaluateAuditSceneEnum.APPOINT_SCORE.getScene(), EvaluateAuditSceneEnum.APPOINT_SCORE.getScene(), null));
//                    //加分项总分
//                    List<PerfEvaluateTaskKpiModel> empTaskKpiList = taskKpiManager.queryTaskKpiBaseInfoByTaskId(taskId, empId, null);
//                    if (checkHasPlusAndSubtract(empTaskKpiList, empId, BusinessConstant.KPI_TYPE_CLASSIFY_PLUS)) {
//                        totalScoreList.add(processTotalScoreDetail(taskBaseModel.getTemplBaseJson(), typeScoreMap, EvaluateAuditSceneEnum.PLUS_SCORE.getScene(), null, null));
//                    }
//                    //减分项总分
//                    if (checkHasPlusAndSubtract(empTaskKpiList, empId, BusinessConstant.KPI_TYPE_CLASSIFY_SUBTRACT)) {
//                        totalScoreList.add(processTotalScoreDetail(taskBaseModel.getTemplBaseJson(), typeScoreMap, EvaluateAuditSceneEnum.SUBTRACT_SCORE.getScene(), null, null));
//                    }
//                    //自动评分总分
//                    ReportTaskUserTotalScoreVO autoScoreDetail = new ReportTaskUserTotalScoreVO();
//                    autoScoreDetail.setScorerType(EvaluateAuditSceneEnum.TOTAL_AUTO_SCORE.getScene());
//                    autoScoreDetail.setFinalScore(taskUserVO.getFinalItemAutoScore());
//                    autoScoreDetail.setTotalScoreResultList(null);
//                    totalScoreList.add(autoScoreDetail);
//                    taskUserVO.setTotalScoreDetailList(totalScoreList);
//                    //3.2、按指标类细分
//                    List<ReportTaskUserKpiTypeScoreVO> kpiTypeScoreDetailList = new ArrayList<>();
//                    Set<String> scoreTypeSet = new HashSet<>();
//                    for (String typeKey : typeScoreMap.keySet()) {
//                        List<PerfEvaluateTaskScoreResultVO> scoreList = typeScoreMap.get(typeKey);
//                        /*
//                        3.2.1、按指标分组
//                        3.2.2、按打分类型分组：指定评分人与自评-互评-上级评分
//                         */
//                        String[] arr = typeKey.split("&");
//                        String scorerType = arr[0];
//                        String kpiTypeId = arr.length == 2 ? arr[1] : null;
//                        String kpiTypeClassify = CollectionUtils.isEmpty(scoreList) ? null : scoreList.get(0).getKpiTypeClassify();
//                        BigDecimal maxExtraScore = CollectionUtils.isEmpty(scoreList) ? null : scoreList.get(0).getMaxExtraScore();
//                        String isOkr = CollectionUtils.isEmpty(scoreList) ? null : scoreList.get(0).getIsOkr();
//                        //排除总评
//                        if (EvaluateScoreSettingDTO.ScoreRule.ITEM.equals(scorerType) && kpiTypeId != null) {
//                            ReportTaskUserKpiTypeScoreVO kpiTypeScore = new ReportTaskUserKpiTypeScoreVO();
//                            kpiTypeScore.setTaskId(taskId);
//                            kpiTypeScore.setKpiTypeId(kpiTypeId);
//                            kpiTypeScore.setKpiTypeName(okrItems.kpiTypeName(kpiTypeId));
//                            kpiTypeScore.setKpiItemScoreDetailList(new ArrayList<>());
//                            kpiTypeScore.setKpiTypeClassify(kpiTypeClassify);
//                            kpiTypeScore.setMaxExtraScore(maxExtraScore);
//                            kpiTypeScore.setIsOkr(isOkr);
//                            kpiTypeScore.setTypeOrder(CollectionUtils.isEmpty(scoreList) ? null : scoreList.get(0).getTypeOrder());
//                            if (!BusinessConstant.TYPE_WEIGHT_SWITCH_CLOSE.equals(tableVO.getTypeWeightSwitch())) {
//                                kpiTypeScore.setKpiTypeWeight(CollectionUtils.isEmpty(scoreList) ? null : scoreList.get(0).getKpiTypeWeight());
//                            }
//                            //保存指标类打分详情 完成一个分类
//                            kpiTypeScoreDetailList.add(kpiTypeScore);
//
//                            //3.2.1、按指标分组
//                            Map<String, ReportTaskUserKpiItemScoreVO> kpiItemScoreMap = new HashMap<>(16);
//                            //3.2.2、按打分类型分组
//                            Map<String, Map<String, ReportTaskUserScorerTypeScoreVO>> itemScorerTypeScoreMap = new HashMap<>(16);
//                            for (PerfEvaluateTaskScoreResultVO r : scoreList) {
//                                ReportTaskUserKpiItemScoreVO kpiItemScoreVO = kpiItemScoreMap.get(r.getKpiItemId());
//                                if (kpiItemScoreVO == null) {
//                                    kpiItemScoreVO = new ReportTaskUserKpiItemScoreVO();
//                                    kpiItemScoreVO.setKpiItemId(r.getKpiItemId());
//                                    kpiItemScoreVO.setKpiItemName(r.getKpiItemName());
//                                    kpiItemScoreVO.setItemRule(r.getItemRule());
//                                    kpiItemScoreVO.setScoringRule(r.getScoringRule());
//                                    kpiItemScoreVO.setPlusLimit(r.getPlusLimit());
//                                    kpiItemScoreVO.setSubtractLimit(r.getSubtractLimit());
//                                    kpiItemScoreVO.setScorerTypeScoreDetailList(new ArrayList<>());
//                                    kpiItemScoreVO.setOrder(r.getOrder());
//                                    kpiItemScoreVO.setItemWeight(r.getItemWeight());
//                                    kpiItemScoreVO.setItemScoreValue(r.getItemScoreValue());
//                                    kpiItemScoreVO.setItemUnit(r.getItemUnit());
//                                    kpiItemScoreVO.setItemTargetValue(r.getItemTargetValue());
//                                    if (kpiTypeScore.isOkrType()) {
//                                        kpiItemScoreVO.setItemFinishValue(okrItems.getItemFinishValue(r.getKpiItemId()));
//                                    } else {
//                                        kpiItemScoreVO.setItemFinishValue(r.getItemFinishValue());
//                                    }
//                                    kpiItemScoreVO.setOpenOkrScore(r.getOpenOkrScore());
//                                    kpiItemScoreVO.setResultInputType(r.getResultInputType());
//                                    kpiItemScoreVO.setItemFormula(r.getItemFormula());
//                                    kpiItemScoreVO.setThresholdJson(r.getThresholdJson());
//                                    kpiItemScoreVO.setItemFieldJson(r.getItemFieldJson());
//                                    kpiItemScoreVO.setScorerType(r.getItemScorerType());
//                                    kpiItemScoreVO.setAutoScore(r.getItemAutoScore());
//                                    kpiItemScoreVO.setWorkItemFinishValue(r.getWorkItemFinishValue());
//                                    kpiItemScoreVO.setItemTags(companyTagDao.listTagName(companyId, r.getKpiItemId()));
//                                    kpiItemScoreVO.setInputChangeRecord(logManager.queryItemDynamic(taskUserVO.getId(), r.getKpiItemId()));
//                                    kpiItemScoreVO.setItemFullScoreCfg(r.getItemFullScoreCfg());
//                                    kpiItemScoreVO.setInputFormat(r.getInputFormat());
//                                    kpiItemScoreVO.setItemFinishValueText(r.getItemFinishValueText());
//
//                                    //设置打分范围
//                                    if (Objects.nonNull(fullScore)) {
//                                        if (PerfTemplBaseModel.SCORE_RANGE_TYPE_WEIGHT_SCORE.equals(scoreRangeType)) {
//                                            //满分值 * 指标权重
//                                            if (Objects.nonNull(r.getItemWeight())) {
//                                                kpiItemScoreVO.setItemFullScore(new BigDecimal(fullScoreValue).multiply(r.getItemWeight().divide(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP)));
//                                            }
//                                        } else {
//                                            kpiItemScoreVO.setItemFullScore(fullScore);
//                                        }
//                                    }
//
//                                    kpiItemScoreMap.put(r.getKpiItemId(), kpiItemScoreVO);
//                                    //保存指标打分详情
//                                    kpiTypeScore.getKpiItemScoreDetailList().add(kpiItemScoreVO);
//
//                                    Map<String, ReportTaskUserScorerTypeScoreVO> scorerTypeScoreMap = new HashMap<>(16);
//                                    itemScorerTypeScoreMap.put(r.getKpiItemId(), scorerTypeScoreMap);
//                                }
//                                Map<String, ReportTaskUserScorerTypeScoreVO> scorerTypeScoreMap = itemScorerTypeScoreMap.get(r.getKpiItemId());
//                                ReportTaskUserScorerTypeScoreVO scorerTypeScoreVO = scorerTypeScoreMap.get(r.getScorerType());
//                                if (scorerTypeScoreVO == null) {
//                                    scorerTypeScoreVO = new ReportTaskUserScorerTypeScoreVO();
//                                    scorerTypeScoreVO.setScorerType(r.getScorerType());
//                                    scorerTypeScoreVO.setScoreResultList(new ArrayList<>());
//                                    scorerTypeScoreMap.put(scorerTypeScoreVO.getScorerType(), scorerTypeScoreVO);
//                                    //保存打分类型详情
//                                    kpiItemScoreVO.getScorerTypeScoreDetailList().add(scorerTypeScoreVO);
//                                }
////                                if (!scorerTypeScoreVO.getScoreResultList().contains(r)) {
//                                scorerTypeScoreVO.getScoreResultList().add(r);
////                                }
//                                if (createdUser.equals(r.getScorerId())) {
//                                    scoreTypeSet.add(r.getScorerType());
//                                }
//                            }
//
//                            //处理各阶段分数
//                            setItemScore(kpiTypeScore);
//                        }
//                    }
//                    taskUserVO.setKpiTypeScoreDetailList(kpiTypeScoreDetailList);
//                    // 查询当前操作人为哪些审核人角色
//                    if (createdUser.equals(taskBaseModel.getCreatedUser())) {
//                        //当前操作人是否为任务发起人
//                        scoreTypeSet.add("task_admin");
//                    }
//                    if (tempAdmin) {
//                        //模板管理员
//                        scoreTypeSet.add("temp_admin");
//                    }
//                    //查询当前操作人是否为校准人
//                    if (taskScoreResultManager.checkFinalResultAudit(taskId, empId, createdUser)) {
//                        scoreTypeSet.add(EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene());
//                    }
//                    if (CollectionUtils.isNotEmpty(scoreTypeSet)) {
//                        taskUserVO.setScoreTypes(scoreTypeSet);
//                    }
//                }
//            }
//            PagedList<ReportTaskUserVO> list = Convert.convertPagedListOnlyMatch(taskUserList, ReportTaskUserVO.class);
//            ModelPagedList<ReportTaskUserVO> pagedList = ModelPagedList.convertToModelPage(list);
//            pagedList.setData(new ArrayList<>(userMap.values()));
//            tableVO.setScoreResults(pagedList);
//        }
//        return tableVO;
//    }


    //private void setOKRInfo(String companyId, List<PerfEvaluateTaskKpiVO> empTaskApiList) {
    //    CompanyModel companyModel = companyManager.queryCompany(companyId);
    //    String corpId = Objects.nonNull(companyModel) ? companyModel.getDingCorpId() : null;
    //    okrTaskManager.setOKRInfo(corpId, empTaskApiList);
    //}

    private void setItemScore(ReportTaskUserKpiTypeScoreVO kpiTypeScore) {
        List<ReportTaskUserKpiItemScoreVO> kpiItemScoreDetailList = kpiTypeScore.getKpiItemScoreDetailList();
        if (CollectionUtils.isEmpty(kpiItemScoreDetailList)) {
            return;
        }
        for (ReportTaskUserKpiItemScoreVO itemScoreVO : kpiItemScoreDetailList) {
            //先看是不是自动打分
            if (BusinessConstant.SCORER_TYPE_AUTO.equals(itemScoreVO.getScorerType())
                    || itemScoreVO.getOpenOkrScore() == 1) {
                itemScoreVO.setItemFinalScore(itemScoreVO.getAutoScore());
                continue;
            }
            List<ReportTaskUserScorerTypeScoreVO> scoreDetailList = itemScoreVO.getScorerTypeScoreDetailList();
            if (CollectionUtils.isEmpty(scoreDetailList)) {
                continue;
            }
            List<PerfEvaluateTaskScoreResultVO> allScoreResultList = itemScoreVO.getScorerTypeScoreDetailList().stream().map(ReportTaskUserScorerTypeScoreVO::getScoreResultList).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(allScoreResultList)) {
                continue;
            }

            if (BusinessConstant.KPI_TYPE_CLASSIFY_PLUS.equals(kpiTypeScore.getKpiTypeClassify())) {
                Optional<BigDecimal> itemFinalScore = allScoreResultList.stream().map(PerfEvaluateTaskScoreResultVO::getFinalWeightPlusScore).filter(Objects::nonNull).reduce(BigDecimal::add);
                if (itemFinalScore.isPresent()) {
                    itemScoreVO.setItemFinalScore(itemFinalScore.get());
                }
                itemScoreVO.setSelfScore(setPlusScore(allScoreResultList, EvaluateAuditSceneEnum.SELF_SCORE.getScene()));
                itemScoreVO.setPeerScore(setPlusScore(allScoreResultList, EvaluateAuditSceneEnum.PEER_SCORE.getScene()));
                itemScoreVO.setSubScore(setPlusScore(allScoreResultList, EvaluateAuditSceneEnum.SUB_SCORE.getScene()));
                itemScoreVO.setSupScore(setPlusScore(allScoreResultList, EvaluateAuditSceneEnum.SUPERIOR_SCORE.getScene()));
                itemScoreVO.setAppointScore(setPlusScore(allScoreResultList, EvaluateAuditSceneEnum.APPOINT_SCORE.getScene()));
                itemScoreVO.setItemScore(setPlusScore(allScoreResultList, EvaluateAuditSceneEnum.ITEM_SCORE.getScene()));
            } else if (BusinessConstant.KPI_TYPE_CLASSIFY_SUBTRACT.equals(kpiTypeScore.getKpiTypeClassify())) {
                Optional<BigDecimal> itemFinalScore = allScoreResultList.stream().map(PerfEvaluateTaskScoreResultVO::getFinalWeightSubtractScore).filter(Objects::nonNull).reduce(BigDecimal::add);
                if (itemFinalScore.isPresent()) {
                    itemScoreVO.setItemFinalScore(itemFinalScore.get());
                }
                itemScoreVO.setSelfScore(setSubtractScore(allScoreResultList, EvaluateAuditSceneEnum.SELF_SCORE.getScene()));
                itemScoreVO.setPeerScore(setSubtractScore(allScoreResultList, EvaluateAuditSceneEnum.PEER_SCORE.getScene()));
                itemScoreVO.setSubScore(setSubtractScore(allScoreResultList, EvaluateAuditSceneEnum.SUB_SCORE.getScene()));
                itemScoreVO.setSupScore(setSubtractScore(allScoreResultList, EvaluateAuditSceneEnum.SUPERIOR_SCORE.getScene()));
                itemScoreVO.setAppointScore(setSubtractScore(allScoreResultList, EvaluateAuditSceneEnum.APPOINT_SCORE.getScene()));
                itemScoreVO.setItemScore(setSubtractScore(allScoreResultList, EvaluateAuditSceneEnum.ITEM_SCORE.getScene()));
            } else {
                Optional<BigDecimal> itemFinalScore = allScoreResultList.stream().map(PerfEvaluateTaskScoreResultVO::getFinalWeightScore).filter(Objects::nonNull).reduce(BigDecimal::add);
                if (itemFinalScore.isPresent()) {
                    itemScoreVO.setItemFinalScore(itemFinalScore.get());
                }
                itemScoreVO.setSelfScore(setScore(scoreDetailList, EvaluateAuditSceneEnum.SELF_SCORE.getScene()));
                itemScoreVO.setPeerScore(setScore(scoreDetailList, EvaluateAuditSceneEnum.PEER_SCORE.getScene()));
                itemScoreVO.setSubScore(setScore(scoreDetailList, EvaluateAuditSceneEnum.SUB_SCORE.getScene()));
                itemScoreVO.setSupScore(setScore(scoreDetailList, EvaluateAuditSceneEnum.SUPERIOR_SCORE.getScene()));
                itemScoreVO.setAppointScore(setScore(scoreDetailList, EvaluateAuditSceneEnum.APPOINT_SCORE.getScene()));
                itemScoreVO.setItemScore(setScore(scoreDetailList, EvaluateAuditSceneEnum.ITEM_SCORE.getScene()));
            }

        }
    }

    private BigDecimal setPlusScore(List<PerfEvaluateTaskScoreResultVO> allScoreResultList, String scoreType) {
        if (CollectionUtils.isEmpty(allScoreResultList)) {
            return null;
        }
        List<PerfEvaluateTaskScoreResultVO> list = allScoreResultList.stream().filter(l -> scoreType.equals(l.getScorerType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        Optional<BigDecimal> plusScore = list.stream().map(PerfEvaluateTaskScoreResultVO::getFinalPlusScore).filter(Objects::nonNull).reduce(BigDecimal::add);
        if (plusScore.isPresent()) {
            return plusScore.get();
        }
        return null;
    }

    private BigDecimal setSubtractScore(List<PerfEvaluateTaskScoreResultVO> allScoreResultList, String scoreType) {
        if (CollectionUtils.isEmpty(allScoreResultList)) {
            return null;
        }
        List<PerfEvaluateTaskScoreResultVO> list = allScoreResultList.stream().filter(l -> scoreType.equals(l.getScorerType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        Optional<BigDecimal> plusScore = list.stream().map(PerfEvaluateTaskScoreResultVO::getFinalSubtractScore).filter(Objects::nonNull).reduce(BigDecimal::add);
        if (plusScore.isPresent()) {
            return plusScore.get();
        }
        return null;
    }

    private BigDecimal setScore(List<ReportTaskUserScorerTypeScoreVO> scoreDetailList, String scene) {
        if (CollectionUtils.isEmpty(scoreDetailList)) {
            return null;
        }
        Optional<ReportTaskUserScorerTypeScoreVO> optional = scoreDetailList.stream().filter(l -> l.getScorerType().equals(scene)).findFirst();
        if (!optional.isPresent()) {
            return null;
        }

        ReportTaskUserScorerTypeScoreVO scorerTypeScoreVO = optional.get();
        List<PerfEvaluateTaskScoreResultVO> scoreResultList = scorerTypeScoreVO.getScoreResultList();
        if (CollectionUtils.isEmpty(scoreResultList)) {
            return null;
        }

        BigDecimal score = BigDecimal.ZERO;
        for (PerfEvaluateTaskScoreResultVO scoreResultVO : scoreResultList) {
            score = score.add(getScore(scoreResultVO));
        }
        return score.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    private BigDecimal getScore(PerfEvaluateTaskScoreResultVO scoreResultVO) {
        if (Objects.isNull(scoreResultVO.getScore())) {
            return BigDecimal.ZERO;
        }
        if (Objects.isNull(scoreResultVO.getScoreWeight())) {
            return scoreResultVO.getScore();
        }
        return scoreResultVO.getScore().multiply(scoreResultVO.getScoreWeight()).divide(new BigDecimal("100"));
    }


    public ModelPagedList<ReportTaskUserVO> listPagedEmpTask(String companyId, String taskId, String taskStatus, Integer pageNo, Integer pageSize) {
        PagedList<PerfEvaluateTaskUserDTO> taskUserList = taskUserManager.listPagedEvaluateTaskUser(companyId, null, taskId, taskStatus, null, pageNo, pageSize);
        PagedList<ReportTaskUserVO> pagedList = Convert.convertPagedListOnlyMatch(taskUserList, ReportTaskUserVO.class);
        ModelPagedList<ReportTaskUserVO> result = ModelPagedList.convertToModelPage(pagedList);
        if (CollectionUtils.isNotEmpty(taskUserList)) {
            List<String> taskUserIds = taskUserList.stream().map(PerfEvaluateTaskUserDTO::getId).collect(Collectors.toList());
            List<PerfEvaluateTaskKpiModel> kpiList = taskKpiManager.listTaskKpiByUsers(taskUserIds);
            if (CollectionUtils.isNotEmpty(kpiList)) {
                Map<String, List<PerfEvaluateTaskKpiModel>> userKpiCountMap = kpiList.stream().collect(Collectors.groupingBy(PerfEvaluateTaskKpiModel::getTaskUserId));
                result.getData().forEach(reportTaskUserVO -> {
                    List<PerfEvaluateTaskKpiModel> list = userKpiCountMap.get(reportTaskUserVO.getId());
                    reportTaskUserVO.setItemCount(0);
                    reportTaskUserVO.setActCount(0);
                    if (CollectionUtils.isNotEmpty(list)) {
                        reportTaskUserVO.setItemCount(list.size());
                    }
                });
            }
        }
        return result;
    }

    private boolean checkHasPlusAndSubtract(List<PerfEvaluateTaskKpiModel> taskKpiList, String empId, String kpiTypeClassify) {
        if (CollectionUtils.isEmpty(taskKpiList)) {
            return false;
        }
        if (StringUtils.isEmpty(empId)) {
            return false;
        }
        return taskKpiList.stream().filter(s -> kpiTypeClassify.equals(s.getKpiTypeClassify())).findAny().isPresent();
    }


    /**
     * scorerType与kpiTypeId组成的键，若kpiTypeId不存在，则scorerType即为键
     *
     * @param scorerType
     * @param kpiTypeId
     * @return
     */
    private String getScoreTypeKey(String scorerType, String kpiTypeId) {
        //这里主要是区分是打总分还是按指标打分
        List<String> totalSceneList = Arrays.asList(EvaluateAuditSceneEnum.TOTAL_SELF_SCORE.getScene(), EvaluateAuditSceneEnum.TOTAL_PEER_SCORE.getScene(), EvaluateAuditSceneEnum.TOTAL_SUB_SCORE.getScene(), EvaluateAuditSceneEnum.TOTAL_SUPERIOR_SCORE.getScene());
        if (totalSceneList.contains(scorerType)) {
            return scorerType;
        } else {
            return EvaluateScoreSettingDTO.ScoreRule.ITEM + (StringUtils.isBlank(kpiTypeId) ? "" : "&" + kpiTypeId);
        }

    }

    private ReportTaskUserTotalScoreVO processTotalScoreDetail(String templBaseJson, Map<String, List<PerfEvaluateTaskScoreResultVO>> typeScoreMap,
                                                               String scene, String itemScore, String scoreRule) {
        List<PerfEvaluateTaskScoreResultVO> totalScoreList = typeScoreMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(itemScore)) {
            totalScoreList = EvaluateScoreSettingDTO.ScoreRule.TOTAL.equals(scoreRule) ?
                    totalScoreList.stream().filter(l -> l.getScorerType().equals(scene)).collect(Collectors.toList()) :
                    totalScoreList.stream().filter(l -> l.getScorerType().equals(itemScore)).collect(Collectors.toList());
        }
        PerfTemplBaseModel templBaseModel = JSONObject.parseObject(templBaseJson, PerfTemplBaseModel.class);
        ReportTaskUserTotalScoreVO totalScoreDetail = new ReportTaskUserTotalScoreVO(scene, BigDecimal.ZERO, totalScoreList);
        //计算未加阶段权重的分数
        totalScoreDetail.builderTotalScore(templBaseModel.getScoreRangeType());
        return totalScoreDetail;
    }

    private ReportTaskUserTotalScoreVO processMergeTotalScoreDetail(String scoreRangeType, Map<String, List<PerfEvaluateTaskScoreResultVO>> typeScoreMap,
                                                                    String scene, String itemScore, String scoreRule) {
        List<PerfEvaluateTaskScoreResultVO> totalScoreList = typeScoreMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(itemScore)) {
            totalScoreList = EvaluateScoreSettingDTO.ScoreRule.TOTAL.equals(scoreRule) ?
                    totalScoreList.stream().filter(l -> l.getScorerType().equals(scene)).collect(Collectors.toList()) :
                    totalScoreList.stream().filter(l -> l.getScorerType().equals(itemScore)).collect(Collectors.toList());
        }
        ReportTaskUserTotalScoreVO totalScoreDetail = new ReportTaskUserTotalScoreVO(scene, BigDecimal.ZERO, totalScoreList);
        //计算未加阶段权重的分数
        totalScoreDetail.builderTotalScore(scoreRangeType);
        return totalScoreDetail;
    }

    //
    //public List<ReportUserTaskExcelVO> listYearTaskDetail(ReportTaskQueryVO vo) throws MalformedURLException {
    //    return listYearTaskDetail(vo.getCompanyId(), vo.getLoginEmpId(), vo);
    //}

    private boolean hasSignature(URL url) {
        HttpURLConnection conn = null;
        try {
            conn = (HttpURLConnection) url.openConnection();
            conn.setConnectTimeout(1000);
            conn.setReadTimeout(20_000);
            conn.setRequestMethod("HEAD");
            long start = Calendar.getInstance().getTimeInMillis();
            Map<String, List<String>> headerMap = conn.getHeaderFields();
            log.info("head_spend: {}", Calendar.getInstance().getTimeInMillis() - start);
            String contentType = JSONUtil.toJsonStr(headerMap.get("Content-Type"));
            return headerMap != null &&
                    headerMap.size() > 0 &&
                    headerMap.containsKey("Content-Length") &&
                    (Long.parseLong(headerMap.get("Content-Length").get(0)) > 0) &&
                    (!contentType.contains("xml"));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

//    @Override
//    public List<ReportUserTaskExcelVO> listYearTaskDetail(String companyId, String adminEmpId, ReportTaskQueryVO reportTaskQueryVO) throws MalformedURLException {
//        if (!BusinessConstant.TRUE.equals(reportTaskQueryVO.getTaskQueryFlag())) {
//            getOrgIds(reportTaskQueryVO);
//            if (reportTaskQueryVO.getNoOrgPerm()) {
//                return new ArrayList<>();
//            }
//        }
//        List<ReportUserTaskExcelVO> list = taskUserManager.listYearTaskDetail(reportTaskQueryVO);
//        if (CollectionUtils.isEmpty(list)) {
//            return list;
//        }
//
//        List<String> taskIds = list.stream().map(ReportUserTaskExcelVO::getTaskId).collect(Collectors.toList());
//        List<PerfEvaluateTaskScoreResultModel> scoreResultModels = taskScoreResultManager.queryResultByTaskIds(companyId, taskIds);
//        if (scoreResultModels == null) {
//            scoreResultModels = new ArrayList<>();
//        }
//        // 获取所有员工信息
//        List<EmployeeBaseInfoModel> allEmpList = employeeDao.findAllEmpByCompanyId(companyId);
//        Map<String, String> allEmpMap = new HashMap<>();
//        allEmpList.stream().forEach(e -> allEmpMap.put(e.getEmployeeId(), e.getName()));
//
//        Map<String, List<PerfEvaluateTaskScoreResultModel>> scoreResultMap = scoreResultModels.stream().collect(Collectors.groupingBy(s -> s.getTaskId() + "_" + s.getEmpId() + "_" + s.getKpiItemId() + "_" + s.getScorerType()));
//
//        Set<String> userIds = new HashSet<>();
//        for (ReportUserTaskExcelVO taskExcelVO : list) {
//            if (StringUtils.isNotEmpty(taskExcelVO.getSignaturePic()) && (!userIds.contains(taskExcelVO.getTaskUserId()))) {
//                URL url = new URL(taskExcelVO.getSignaturePic());
//                String path = url.getPath();
//                String downRrlSrt = downUrl + path;
//                taskExcelVO.setUrl(new URL(downRrlSrt));
//                userIds.add(taskExcelVO.getTaskUserId());
//                log.info("图片下载地址：{}", JSON.toJSONString(taskExcelVO.getUrl()));
//                if (!hasSignature(taskExcelVO.getUrl())) {
//                    log.info("OSS中不存在图片：{}", JSONUtil.toJsonStr(url));
//                    taskExcelVO.setUrl(null);
//                }
//            }
//            //考核时段
//            if (StringUtils.isNotBlank(taskExcelVO.getCycleStartDate()) && StringUtils.isNotBlank(taskExcelVO.getCycleEndDate())) {
//                taskExcelVO.setTaskDate(DateTimeUtils.date2StrDate(DateTimeUtils.strDate2Date(taskExcelVO.getCycleStartDate()), DateTimeUtils.FORMAT_yyyy_nian_MM_yue_mm_ri) + "至"
//                        + DateTimeUtils.date2StrDate(DateTimeUtils.strDate2Date(taskExcelVO.getCycleEndDate()), DateTimeUtils.FORMAT_yyyy_nian_MM_yue_mm_ri));
//            }
//            String unit = ObjectUtils.defaultIfNull(taskExcelVO.getItemUnit(), "");
//
//            //完成度，单位是%的不需要算完成度
//            if (StringUtils.isNotBlank(taskExcelVO.getItemTargetValue()) && StringUtils.isNotBlank(taskExcelVO.getItemFinishValue())
//                    && !"%".equals(unit)) {
//                BigDecimal targetValue = new BigDecimal(taskExcelVO.getItemTargetValue());
//                BigDecimal finishValue = new BigDecimal(taskExcelVO.getItemFinishValue());
//                String progress;
//                if (targetValue.compareTo(BigDecimal.ZERO) == 0) {
//                    progress = "-";
//                } else if (finishValue.compareTo(targetValue) >= 0) {
//                    progress = "100";
//                } else {
//                    progress = finishValue.divide(targetValue, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(1, BigDecimal.ROUND_HALF_UP).toString();
//                }
//                taskExcelVO.setProgress(progress + "%");
//            } else {
//                taskExcelVO.setProgress("-");
//            }
//            //目标值,非量化指标目标值是100%
//            if (CompanyItemTypeEnum.NON_MEASURABLE.getType().equals(taskExcelVO.getItemType())) {
//                taskExcelVO.setItemTargetValue("100.00%");
//            } else {
//                taskExcelVO.setItemTargetValue(StringUtils.isNotEmpty(taskExcelVO.getItemTargetValue()) ? taskExcelVO.getItemTargetValue() + unit : "-");
//            }
//            //完成值
//            taskExcelVO.setItemFinishValue(StringUtils.isNotEmpty(taskExcelVO.getItemFinishValue()) ? taskExcelVO.getItemFinishValue() + unit : "-");
//            if (BusinessConstant.KPI_TYPE_CLASSIFY_WORK_ITEM.equals(taskExcelVO.getKpiTypeClassify())) {
//                taskExcelVO.setItemFinishValue(StringUtils.isNotEmpty(taskExcelVO.getWorkItemFinishValue()) ?
//                        taskExcelVO.getWorkItemFinishValue() : "-");
//            }
//            //完成值录入人
////            if (BusinessConstant.EXAM.equals(taskExcelVO.getResultInputType())){
////                //由被考核人自己录入
////                taskExcelVO.setFinishValueInputEmp(taskExcelVO.getEmpName());
////            }
//
//            // 完成值更新记录  内容由0%变更为47%
////            2021-3-6  12:00
////            张三更新由0%变更为47%。
////            更新说明：这是一个小目标
//            StringBuffer updateLogStr = new StringBuffer("");
//         /*   if (StringUtils.isNotEmpty(taskExcelVO.getTaskUserId()) && StringUtils.isNotEmpty(taskExcelVO.getKpiItemId())) {
//                List<PerfEvaluateTaskItemDynamicVO> itemDynamicVOs = logManager.queryItemDynamic(taskExcelVO.getTaskUserId(), taskExcelVO.getKpiItemId());
//                if (CollectionUtils.isNotEmpty(itemDynamicVOs)) {
//                    for (PerfEvaluateTaskItemDynamicVO itemDynamic : itemDynamicVOs) {
//                        if (OperationLogSceneEnum.INPUT_FINISH_VALUE.getScene().equals(itemDynamic.getBusinessScene())) {
//                            // {\"kpiItemName\":\"订货差错率\",\"unit\":\"%\",\"files\":\"[]\",\"comment\":\"更新说明内容\"}
//                            JSONObject contentObj = JSON.parseObject(itemDynamic.getContent());
//                            updateLogStr.append(itemDynamic.getCreatedTime()).append("\n").append(itemDynamic.getEmpName() + "：");
//                            if (BusinessConstant.KPI_TYPE_CLASSIFY_WORK_ITEM.equals(taskExcelVO.getKpiTypeClassify())) {
//                                updateLogStr.append(StringUtils.isEmpty(taskExcelVO.getWorkItemFinishValue())?"-":taskExcelVO.getWorkItemFinishValue());
//                                break;
//                            } else {
//                                updateLogStr
//                                        .append("由").append(StringUtils.isBlank(itemDynamic.getBeforeValue()) ? "0" :
//                                        itemDynamic.getBeforeValue()).append(contentObj.get("unit"))
//                                        .append("变更为").append(itemDynamic.getAfterValue()).append(contentObj.get("unit")).append(".\n");
//                            }
//                            if (Objects.nonNull(contentObj.get("comment"))) {
//                                updateLogStr.append("更新说明：").append(contentObj.get("comment")).append(".\n");
//                            }
//                        }
//                    }
//                } else {
//                    updateLogStr.append("-");
//                }
//            }*/
//            updateLogStr.append("-");
//            taskExcelVO.setFinishValueUpdateLog(updateLogStr.toString());
//            //自评得分
//            taskExcelVO.setSelfScore(getScoreStr(scoreResultMap, taskExcelVO, EvaluateAuditSceneEnum.SELF_SCORE.getScene()));
//            // 自评详情
//            taskExcelVO.setSelfScoreInfo(getScoreInfoStr(scoreResultMap, allEmpMap, taskExcelVO, EvaluateAuditSceneEnum.SELF_SCORE.getScene()));
//
//            //同级互评得分
//            taskExcelVO.setPeerScore(getScoreStr(scoreResultMap, taskExcelVO, EvaluateAuditSceneEnum.PEER_SCORE.getScene()));
//            // 同级评分详情
//            taskExcelVO.setPeerScoreInfo(getScoreInfoStr(scoreResultMap, allEmpMap, taskExcelVO, EvaluateAuditSceneEnum.PEER_SCORE.getScene()));
//
//            //下级互评得分
//            taskExcelVO.setSubScore(getScoreStr(scoreResultMap, taskExcelVO, EvaluateAuditSceneEnum.SUB_SCORE.getScene()));
//            // 下级评分详情
//            taskExcelVO.setSubScoreInfo(getScoreInfoStr(scoreResultMap, allEmpMap, taskExcelVO, EvaluateAuditSceneEnum.SUB_SCORE.getScene()));
//
//            //上级评得分
//            taskExcelVO.setSuperiorScore(getScoreStr(scoreResultMap, taskExcelVO, EvaluateAuditSceneEnum.SUPERIOR_SCORE.getScene()));
//            // 上级评分详情
//            taskExcelVO.setSuperiorScoreInfo(getScoreInfoStr(scoreResultMap, allEmpMap, taskExcelVO, EvaluateAuditSceneEnum.SUPERIOR_SCORE.getScene()));
//
//            //指标指定评分人得分
//            taskExcelVO.setItemScore(getScoreStr(scoreResultMap, taskExcelVO, EvaluateAuditSceneEnum.ITEM_SCORE.getScene()));
//            // 指定评分详情
//            taskExcelVO.setItemScoreInfo(getScoreInfoStr(scoreResultMap, allEmpMap, taskExcelVO, EvaluateAuditSceneEnum.ITEM_SCORE.getScene()));
//
//
//            if ("-".equals(taskExcelVO.getItemScore())) {
//                //自定义评分流程中的指定评分
//                taskExcelVO.setItemScore(getScoreStr(scoreResultMap, taskExcelVO, EvaluateAuditSceneEnum.APPOINT_SCORE.getScene()));
//                taskExcelVO.setItemScoreInfo(getScoreInfoStr(scoreResultMap, allEmpMap, taskExcelVO, EvaluateAuditSceneEnum.APPOINT_SCORE.getScene()));
//
//            }
//            if ("a5415be0-ad88-472d-a63e-7aa6ff4fe24d".equals(companyId) ||
//                    "acd50632-e6f0-4ee3-9f41-1f402fb1ac74".equals(companyId)) {//圆心科技
//                //if ("5a031297-1b38-48ae-bc82-375849835203".equals(companyId)) {//绩效测试公司
//                if (EvaluateTaskStatusEnum.RESULTS_AUDITING.getStatus().equals(taskExcelVO.getTaskStatus())) {
//                    taskExcelVO.setFinalScore("");
//                    taskExcelVO.setEvaluationLevel("");
//                }
//            } else {
//                //已完成的任务或者待审核的任务才有核定总分和考核等级
//                if (!EvaluateTaskStatusEnum.FINISHED.getStatus().equals(taskExcelVO.getTaskStatus())
//                        && !EvaluateTaskStatusEnum.WAIT_PUBLISHED.getStatus().equals(taskExcelVO.getTaskStatus())
//                        && !EvaluateTaskStatusEnum.RESULTS_AUDITING.getStatus().equals(taskExcelVO.getTaskStatus())) {
//                    taskExcelVO.setFinalScore("");
//                    taskExcelVO.setEvaluationLevel("");
//                }
//            }
//            //当前状态
//            taskExcelVO.setTaskStatus(EvaluateTaskStatusEnum.getDescByStatus(taskExcelVO.getTaskStatus()));
//            //加减分指标不展示权重
//            if (BusinessConstant.KPI_TYPE_CLASSIFY_PLUS.equals(taskExcelVO.getKpiTypeClassify()) || BusinessConstant.KPI_TYPE_CLASSIFY_SUBTRACT.equals(taskExcelVO.getKpiTypeClassify())) {
//                taskExcelVO.setItemWeight("-");
//            }
//        }
//        userIds.clear();
//        return list;
//    }

    //private String getScoreStr(Map<String, List<PerfEvaluateTaskScoreResultModel>> scoreResultMap, ReportUserTaskExcelVO taskExcelVO, String scoreType) {
    //    BigDecimal score = getScore(scoreResultMap, taskExcelVO, scoreType);
    //    if (score == null) {
    //        return "-";
    //    }
    //    return score.toString();
    //}

    /**
     * 评分详情；格式： 姓名:评分（90）,评语（111）
     *
     * @param scoreResultMap
     * @param taskExcelVO
     * @param scoreType
     * @return
     */
    //private String getScoreInfoStr(Map<String, List<PerfEvaluateTaskScoreResultModel>> scoreResultMap, Map<String, String> allEmpMap, ReportUserTaskExcelVO taskExcelVO, String scoreType) {
    //    StringBuffer scoreInfoStr = new StringBuffer("");
    //    List<PerfEvaluateTaskScoreResultModel> scoreResultList = scoreResultMap.get(taskExcelVO.getTaskId() + "_" + taskExcelVO.getEmpId() + "_" + taskExcelVO.getKpiItemId() + "_" + scoreType);
    //    if (CollectionUtils.isNotEmpty(scoreResultList)) {
    //        for (PerfEvaluateTaskScoreResultModel scoreResult : scoreResultList) {
    //            BigDecimal score = Objects.nonNull(scoreResult.getScore()) ? scoreResult.getScore() : Objects.nonNull(scoreResult.getPlusScore()) ? scoreResult.getPlusScore() : scoreResult.getSubtractScore();
    //            scoreInfoStr.append(allEmpMap.get(scoreResult.getScorerId())).append("：").append("评分(").append(score).append("),评语(").append(StringTool.handleNullToEmpty(scoreResult.getScoreComment())).append(")").append("\n");
    //        }
    //    } else {
    //        scoreInfoStr.append("-");
    //    }
    //    return scoreInfoStr.toString();
    //}

    //private BigDecimal getScore(Map<String, List<PerfEvaluateTaskScoreResultModel>> scoreResultMap, ReportUserTaskExcelVO taskExcelVO, String scoreType) {
    //    List<PerfEvaluateTaskScoreResultModel> scoreResultList = scoreResultMap.get(taskExcelVO.getTaskId() + "_" + taskExcelVO.getEmpId() + "_" + taskExcelVO.getKpiItemId() + "_" + scoreType);
    //    if (CollectionUtils.isEmpty(scoreResultList)) {
    //        return null;
    //    }
    //
    //    String kpiTypeClassify = taskExcelVO.getKpiTypeClassify();
    //    Optional<BigDecimal> optional;
    //    //加分项
    //    if (BusinessConstant.KPI_TYPE_CLASSIFY_PLUS.equals(kpiTypeClassify)) {
    //        optional = scoreResultList.stream().map(PerfEvaluateTaskScoreResultModel::getFinalPlusScore).filter(s -> s != null).reduce(BigDecimal::add);
    //    }
    //    //减分分项
    //    else if (BusinessConstant.KPI_TYPE_CLASSIFY_SUBTRACT.equals(kpiTypeClassify)) {
    //        optional = scoreResultList.stream().map(PerfEvaluateTaskScoreResultModel::getFinalSubtractScore).filter(s -> s != null).reduce(BigDecimal::add);
    //    } else {
    //        //普通指标
    //        optional = scoreResultList.stream().map(PerfEvaluateTaskScoreResultModel::getFinalWeightScore).filter(s -> s != null).reduce(BigDecimal::add);
    //
    //    }
    //    if (optional.isPresent()) {
    //        return optional.get();
    //    }
    //    return null;
    //}

    @Override
    public List<ReportCountResultVO> queryScoreCount(ReportTaskQueryVO queryVO) {
        getAllChildOrgIds(queryVO);
        getOrgIds(queryVO);
        if (queryVO.getNoOrgPerm()) {
            return null;
        }
        return taskUserManager.queryScoreCount(queryVO);
    }

    @Override
    public List<ReportCountResultVO> queryItemScoreCount(ReportTaskQueryVO queryVO) {
        getAllChildOrgIds(queryVO);
        getOrgIds(queryVO);
        if (queryVO.getNoOrgPerm()) {
            return null;
        }
        return taskUserManager.queryItemScoreCount(queryVO);
    }

    @Override
    public List<ReportCountResultVO> queryItemFinishValueCount(ReportTaskQueryVO queryVO) {
        getAllChildOrgIds(queryVO);
        getOrgIds(queryVO);
        if (queryVO.getNoOrgPerm()) {
            return null;
        }
        return taskUserManager.queryItemFinishValueCount(queryVO);
    }


    public ModelPagedList<ReportItemScoreRankVO> queryItemScoreRank(ReportTaskQueryVO queryVO) {
        getAllChildOrgIds(queryVO);
        getOrgIds(queryVO);
        if (queryVO.getNoOrgPerm()) {
            return ModelPagedList.createEmptyData(queryVO.getPageNo(), queryVO.getPageSize());
        }
        PagedList<ReportItemScoreRankVO> pagedList = taskUserManager.queryItemScoreRank(queryVO);
        if (CollectionUtils.isNotEmpty(pagedList.getData())) {
            //计算完成度
            for (ReportItemScoreRankVO data : pagedList.getData()) {
                if (Objects.isNull(data.getItemTargetValue())) {
                    continue;
                }
                data.setProgress(data.getPercent());
            }
        }
        return ModelPagedList.convertToModelPage(pagedList);
    }

    ///***
    // * 查询指标分析导出数据
    // * @param queryVO
    // * @return
    // */
    //public PagedList<ReportItemAnalyseExcelPo> queryItemScoreExport(ReportTaskQueryVO queryVO) {
    //    getAllChildOrgIds(queryVO);
    //    getOrgIds(queryVO);
    //    if (queryVO.getNoOrgPerm()) {
    //        LoggerFactory.getLogger(getClass()).info("没有权限查看报表");
    //        PagedList<ReportItemAnalyseExcelPo> reportPos = new PagedList<>();
    //        reportPos.setPageSize(10);
    //        reportPos.setPageNo(1);
    //        reportPos.setTotalPage(0);
    //        reportPos.setTotalRow(0);
    //        return reportPos;
    //    }
    //    PagedList<ReportItemAnalyseExcelPo> pagedList = taskUserManager.PageItemScoreExcel(queryVO);
    //    if (CollectionUtils.isNotEmpty(pagedList.getData())) {
    //        //计算完成度
    //        for (ReportItemAnalyseExcelPo data : pagedList.getData()) {
    //            data.builderProgress();
    //        }
    //    }
    //    return pagedList;
    //}

    @Override
    public PagedList<PerfEvaluateTaskBaseModel> pagedItemReportTask(EvaluationTaskUserQueryVO queryVO) {
        return taskKpiManager.pagedItemReportTask(queryVO);
    }

    //@Override
    //public List<ReportYearCountVO> queryYearReport(ReportTaskQueryVO queryVO) {
    //    getAllChildOrgIds(queryVO);
    //    getOrgIds(queryVO);
    //    if (queryVO.getNoOrgPerm()) {
    //        return null;
    //    }
    //    List<ReportYearCountVO> list = taskUserManager.queryYearReport(queryVO);
    //    if (CollectionUtils.isEmpty(list)) {
    //        return list;
    //    }
    //    if (Arrays.asList("a5415be0-ad88-472d-a63e-7aa6ff4fe24d", "acd50632-e6f0-4ee3-9f41-1f402fb1ac74", "5a031297-1b38-48ae-bc82-375849835203").contains(queryVO.getCompanyId())) {
    //        for (ReportYearCountVO reportYearCountVO : list) {
    //            List<TaskUserOrg> taskUserOrgList = reportAppSvc.listTaskUserOrg(reportYearCountVO.getEmpId(), queryVO.getCompanyId());
    //            StringBuilder orgNames = new StringBuilder();
    //            for (TaskUserOrg taskUserOrg : taskUserOrgList) {
    //                if (taskUserOrg.getType().equals("root")) {
    //                    orgNames.append(taskUserOrg.getOrgNames() + ";");
    //                } else {
    //                    orgNames.append(taskUserOrg.getOrgNames().substring(taskUserOrg.getOrgNames().indexOf("/") + 1) + ";");
    //                }
    //            }
    //            reportYearCountVO.setOrgName(orgNames.toString());
    //        }
    //    }
    //    List<ReportWeightSetting> weightSettingModels = reportWeightSettingManager.queryReportWeightSetting(queryVO);
    //    if (CollectionUtils.isEmpty(weightSettingModels)) {
    //        //初始化权重数据
    //        weightSettingModels = JSONArray.parseArray("[{\"month\":1,\"weight\":8.33},{\"month\":2,\"weight\":8.33},{\"month\":3,\"weight\":8.33},{\"month\":4,\"weight\":8.33},{\"month\":5,\"weight\":8.33},{\"month\":6,\"weight\":8.33},{\"month\":7,\"weight\":8.33},{\"month\":8,\"weight\":8.33},{\"month\":9,\"weight\":8.33},{\"month\":10,\"weight\":8.33},{\"month\":11,\"weight\":8.33},{\"month\":12,\"weight\":8.37}]", ReportWeightSetting.class);
    //        weightSettingModels.forEach(l -> {
    //            l.setYear(Integer.parseInt(queryVO.getYear()));
    //            l.setCycleType(queryVO.getCycleType());
    //            l.setCompanyId(queryVO.getCompanyId());
    //            l.setCreatedUser(queryVO.getCreatedUser());
    //        });
    //        reportWeightSettingManager.batchSaveReportWeightSetting(weightSettingModels);
    //    }
    //    Map<Integer, BigDecimal> weightMap = weightSettingModels.stream().collect(Collectors.toMap(ReportWeightSetting::getMonth, ReportWeightSetting::getWeight, (a, b) -> b));
    //    for (ReportYearCountVO reportYearCountVO : list) {
    //        setFinalScore(reportYearCountVO.getMonth1(), weightMap.get(1), reportYearCountVO);
    //        setFinalScore(reportYearCountVO.getMonth2(), weightMap.get(2), reportYearCountVO);
    //        setFinalScore(reportYearCountVO.getMonth3(), weightMap.get(3), reportYearCountVO);
    //        setFinalScore(reportYearCountVO.getMonth4(), weightMap.get(4), reportYearCountVO);
    //        setFinalScore(reportYearCountVO.getMonth5(), weightMap.get(5), reportYearCountVO);
    //        setFinalScore(reportYearCountVO.getMonth6(), weightMap.get(6), reportYearCountVO);
    //        setFinalScore(reportYearCountVO.getMonth7(), weightMap.get(7), reportYearCountVO);
    //        setFinalScore(reportYearCountVO.getMonth8(), weightMap.get(8), reportYearCountVO);
    //        setFinalScore(reportYearCountVO.getMonth9(), weightMap.get(9), reportYearCountVO);
    //        setFinalScore(reportYearCountVO.getMonth10(), weightMap.get(10), reportYearCountVO);
    //        setFinalScore(reportYearCountVO.getMonth11(), weightMap.get(11), reportYearCountVO);
    //        setFinalScore(reportYearCountVO.getMonth12(), weightMap.get(12), reportYearCountVO);
    //        reportYearCountVO.setMonth1(setScale(reportYearCountVO.getMonth1()));
    //        reportYearCountVO.setMonth2(setScale(reportYearCountVO.getMonth2()));
    //        reportYearCountVO.setMonth3(setScale(reportYearCountVO.getMonth3()));
    //        reportYearCountVO.setMonth4(setScale(reportYearCountVO.getMonth4()));
    //        reportYearCountVO.setMonth5(setScale(reportYearCountVO.getMonth5()));
    //        reportYearCountVO.setMonth6(setScale(reportYearCountVO.getMonth6()));
    //        reportYearCountVO.setMonth7(setScale(reportYearCountVO.getMonth7()));
    //        reportYearCountVO.setMonth8(setScale(reportYearCountVO.getMonth8()));
    //        reportYearCountVO.setMonth9(setScale(reportYearCountVO.getMonth9()));
    //        reportYearCountVO.setMonth10(setScale(reportYearCountVO.getMonth10()));
    //        reportYearCountVO.setMonth11(setScale(reportYearCountVO.getMonth11()));
    //        reportYearCountVO.setMonth12(setScale(reportYearCountVO.getMonth12()));
    //        reportYearCountVO.setFinalScore(setScale(reportYearCountVO.getFinalScore()));
    //    }
    //
    //    if ("finalScore".equals(queryVO.getOrderField())) {
    //        if ("ASC".equalsIgnoreCase(queryVO.getOrderType())) {
    //            return list.stream().sorted(Comparator.comparing(ReportYearCountVO::getFinalScore, Comparator.nullsFirst(BigDecimal::compareTo))).collect(Collectors.toList());
    //        } else {
    //            return list.stream().sorted(Comparator.comparing(ReportYearCountVO::getFinalScore, Comparator.nullsFirst(BigDecimal::compareTo)).reversed()).collect(Collectors.toList());
    //        }
    //    }
    //    return list;
    //}

    private BigDecimal setScale(BigDecimal score) {
        if (Objects.isNull(score)) {
            return null;
        }
        return score.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    private void setFinalScore(BigDecimal weight, BigDecimal monthScore, ReportYearCountVO reportYearCountVO) {
        if (Objects.isNull(weight)) {
            return;
        }
        if (Objects.isNull(monthScore)) {
            return;
        }
        BigDecimal finalScore = ObjectUtils.defaultIfNull(reportYearCountVO.getFinalScore(), BigDecimal.ZERO);
        finalScore = finalScore.add(monthScore.multiply(weight).divide(new BigDecimal("100"), 3, BigDecimal.ROUND_HALF_UP));
        reportYearCountVO.setFinalScore(finalScore);
    }

    @Override
    public Map queryOrgPerm(ReportTaskQueryVO queryVO) {
        Map map = new HashMap(2);
        //是否是主管理员或应用管理员
        SystemAdminSetModel admin = adminSetDao.findByEmpId(queryVO.getCompanyId(), queryVO.getCreatedUser());
        if (Objects.isNull(admin)) {
            //非管理员，没有查看权限
            map.put("perm", "no");
            return map;
        }

        //主管理员或者应用管理员
        if (Arrays.asList(AdminType.MAIN, AdminType.APP).contains(admin.getAdminType())
                || SystemAdminSetModel.ManagePurview.COMPANY.equals(admin.getManagePurview().getManageScope())) {
            map.put("perm", "all");
            return map;
        }

        List<String> orgIdList = adminSetDao.extractManageOrgId(admin);
        List<EmpOrgSimpleDTO> orgList = empOrgDao.queryOrgByOrgIds(queryVO.getCompanyId(), orgIdList);

        map.put("perm", "subAdmin");
        map.put("orgList", orgList);
        return map;
    }


    public ModelPagedList<ReportOrgPerfLevelQuotaVO> queryReportOrgLevelQuotaList(ReportTaskQueryVO queryVO) {
        getOrgIds(queryVO);
        PagedList<ReportOrgPerfLevelQuotaVO> pagedList = perfLevelManager.queryReportOrgLevelQuotaList(queryVO);
        if (CollectionUtils.isNotEmpty(pagedList.getData())) {
            for (ReportOrgPerfLevelQuotaVO quotaVO : pagedList.getData()) {
                queryVO.setOrgId(quotaVO.getOrgId());
                List<CompanyPerfLevelQuotaOrgVO> quotaOrgVOList = perfLevelManager.queryPerfLevelQuotaOrgList(queryVO);
                quotaVO.setQuotaOrgVOList(quotaOrgVOList);
            }
        }
        return ModelPagedList.convertToModelPage(pagedList);
    }

    @Override
    public List<PerfEvaluateTaskUserVO> querylevelQuotaRatioTaskInfo(ReportTaskQueryVO queryVO) {
        return perfLevelManager.querylevelQuotaRatioTaskInfo(queryVO);
    }

    @Override
    public Map queryYearReportTaskDetail(ReportTaskQueryVO queryVO) {
        Map map = new HashMap(2);
        List<ReportYearEmpTaskDetailVO> list = taskUserManager.queryYearReportTaskDetail(queryVO);
        if (CollUtil.isNotEmpty(list)){
            List<String> taskUserIds = list.stream().map(s -> s.getTaskUserId()).collect(Collectors.toList());
            List<KpiTypePo> kpiTypePos = kpiDao.listKpiType(queryVO.getCompanyId(), taskUserIds);
            if (CollUtil.isNotEmpty(kpiTypePos)){
                Map<String, List<KpiTypePo>> collect = kpiTypePos.stream().collect(Collectors.groupingBy(KpiTypePo::getTaskUserId));
                for (ReportYearEmpTaskDetailVO vo : list){
                    vo.setKpiTypeList(collect.get(vo.getTaskUserId()));
                }
            }
        }
        map.put("list", list);

        if (CollectionUtils.isEmpty(list)) {
            return map;
        }
        //任务数大于2，取平均值
        if (list.size() > 1) {
            Optional<BigDecimal> totalScoreOption = list.stream().map(ReportYearEmpTaskDetailVO::getFinalScore).filter(Objects::nonNull).reduce(BigDecimal::add);
            if (totalScoreOption.isPresent()) {
                BigDecimal totalScore = totalScoreOption.get();
                if (queryVO.isAvg()) {
                    BigDecimal avgScore = totalScore.divide(new BigDecimal(list.size()), 2, BigDecimal.ROUND_HALF_UP);
                    map.put("avgScore", avgScore);
                }else {
                    map.put("sumScore", totalScore);
                }
            }
        }
        return map;
    }

    @Override
    public List<PerfEvaluateTaskRefOkrModel> listTargetName(List<String> ids) {
        return taskKpiManager.listTargetName(ids);
    }

    @Autowired
    private EvaluateTaskDao taskDao;


    //评分详情接口 替换 listTaskUserScoreResult
//    public EmpEvalTaskScorePo getMergeScoreResult(String companyId, String taskUserId, String createdUser) {
//        /*
//        1、查询考核员工列表
//        2、查询考核得分结果列表(按指标项打分，各评分阶段给考核任务打总分）
//        3、按员工、指标类整理得分结果
//         */
//        EmpEvalTaskScorePo taskScorePo = new EmpEvalTaskScorePo();
//        CycleEval taskBaseModel = taskDao.getMergeTaskBase(new TenantId(companyId), taskUserId);
//        PerfTemplEvaluate perfEval = taskBaseModel.getTemplEvaluateJson();
//
//        //taskDao.listMergeTaskBaseAsMap()
//        //PerfEvaluateTaskBaseModel taskBaseModel = taskBaseManager.findById(taskId);
//        //类别权重开关
//        ExamGroup tmpBase = taskBaseModel.getTemplBaseJson();
//        taskScorePo.setTypeWeightSwitch(tmpBase.getTypeWeightSwitch());
//
//        String rangeType = tmpBase.getScoreRangeType();
//        EmpEvalUserDetailOfScorePo taskUser = taskUserManager.getMergeTaskUser(companyId, taskUserId);
//        String taskId = taskUser.getTaskId();
//        String taskEmpId = taskUser.getEmpId();
//
//        //查询自定义指标流程
//        //List<PerfEvaluateTaskItemScoreRuleModel> ruleModels = itemScoreRuleManager.listItemScoreRule(companyId, taskId);
//        ListWrap<EvalItemScoreRule> itemRules = kpiDao.listEmpEvalItemRule(new TenantId(companyId), taskUserId);
//        perfEval.setItemEvaluateList(Convert.convertListOnlyMatch(itemRules.getDatas(), PerfTemplItemEvaluate.class));
//        //查询是否有互评流程
//        List<PerfEvaluateTaskAuditModel> peerScoreAuditList = taskAuditManager.queryAuditListByItem(companyId, taskId, taskEmpId, null, EvaluateAuditSceneEnum.PEER_SCORE.getScene());
//        if (CollectionUtils.isNotEmpty(peerScoreAuditList)) {
//            perfEval.setPeerScoreFlag(BusinessConstant.TRUE);
//        }
//        List<PerfEvaluateTaskAuditModel> subScoreAuditList = taskAuditManager.queryAuditListByItem(companyId, taskId, taskEmpId, null, EvaluateAuditSceneEnum.SUB_SCORE.getScene());
//        if (CollectionUtils.isNotEmpty(subScoreAuditList)) {
//            perfEval.setSubScoreFlag(BusinessConstant.TRUE);
//        }
//
//        taskScorePo.setTemplEvaluateInfo(perfEval);
//        //查询当前操作人是否模板管理员
//        boolean tempAdmin = adminSetDao.checkTempAdmin(companyId, taskBaseModel.getTemplBaseId(), createdUser);
//        //查当前满分值
//        String fullScoreValue = companySysSettingManager.querySettingValue(companyId, CompanySysSettingModel.SETTING_TYPE_FULL_SCORE);
//        BigDecimal fullScore = StringUtils.isNoneBlank(fullScoreValue) ? new BigDecimal(fullScoreValue) : null;
//        String scoreRangeType = taskBaseModel.getScoreRangeType();
//        //查是否有指定评分
//        if (taskKpiManager.checkHasItemScore(companyId, taskId) || taskAuditManager.checkHasAppointScore(companyId, taskId)) {
//            taskScorePo.setItemScoreFlag(BusinessConstant.TRUE);
//        }
//
//        //修改，实时返回考核结果
//        //1、考核员工列表
//        {
//            //2、考核得分结果列表
//            List<PerfEvaluateTaskScoreResultVO> resultList = taskScoreResultManager.listTaskScoreResults(companyId, taskUserId);
//            //2.1、构建自动打分结果列表
//            List<PerfEvaluateTaskKpiVO> kpis = taskKpiManager.listEvaluateTaskKpi(companyId, taskUserId);
//            if (CollectionUtils.isNotEmpty(kpis)) {
//                List<PerfEvaluateTaskScoreResultVO> autoScoreResultList = kpis.stream()
//                        .filter(kpi -> ScoreTypeEnum.AUTO.getType().equals(kpi.getScorerType()))
//                        .map(kpi -> {
//                            PerfEvaluateTaskScoreResultVO resultVO = Convert.convertOnlyMatch(kpi, PerfEvaluateTaskScoreResultVO.class);
//                            resultVO.setScorerType(EvaluateAuditSceneEnum.TOTAL_AUTO_SCORE.getScene());
//                            resultVO.setFinalScore(kpi.getItemAutoScore());
//                            resultVO.setItemScorerType(BusinessConstant.SCORER_TYPE_AUTO);
//                            return resultVO;
//                        }).collect(Collectors.toList());
//                if (CollectionUtils.isNotEmpty(autoScoreResultList)) {
//                    resultList.addAll(autoScoreResultList);
//                }
//            }
//            if (CollectionUtils.isNotEmpty(resultList)) {
//                List<PerfEvaluateTaskKpiVO> taskKpiList = taskKpiManager.listTaskApiItemByTaskId(companyId, taskUserId);
//                final OkrItemList okrItems = new OkrItemList(taskKpiList);
//                // fix 11983
//                setOKRInfo(companyId, taskKpiList);
//                //3、按员工、打分类型+指标类分组，这里主要是区分是打总分还是按指标打分
//                LinkedHashMap<String, List<PerfEvaluateTaskScoreResultVO>> typeScoreMap = resultList.stream()
//                        .collect(Collectors.groupingBy(r -> getScoreTypeKey(r.getScorerType(), r.getKpiTypeId()),
//                                LinkedHashMap::new, Collectors.toList()));
//                {
//                    String empId = taskUser.getEmpId();
//                    //3.1、考核总分详情
//                    List<ReportTaskUserTotalScoreVO> totalScoreList = new ArrayList<>(8);
//                    //3.1.1、自评总分详情
//                    totalScoreList.add(processMergeTotalScoreDetail(rangeType, typeScoreMap, EvaluateAuditSceneEnum.TOTAL_SELF_SCORE.getScene(), EvaluateAuditSceneEnum.SELF_SCORE.getScene(), perfEval.getSelfScoreRule()));
//                    //3.1.2、互评总分详情
//                    totalScoreList.add(processMergeTotalScoreDetail(rangeType, typeScoreMap, EvaluateAuditSceneEnum.TOTAL_PEER_SCORE.getScene(), EvaluateAuditSceneEnum.PEER_SCORE.getScene(), perfEval.getMutualScoreRule()));
//                    totalScoreList.add(processMergeTotalScoreDetail(rangeType, typeScoreMap, EvaluateAuditSceneEnum.TOTAL_SUB_SCORE.getScene(), EvaluateAuditSceneEnum.SUB_SCORE.getScene(), null));
//                    //3.1.3、上级评分详情
//                    totalScoreList.add(processMergeTotalScoreDetail(rangeType, typeScoreMap, EvaluateAuditSceneEnum.TOTAL_SUPERIOR_SCORE.getScene(), EvaluateAuditSceneEnum.SUPERIOR_SCORE.getScene(), perfEval.getSuperiorScoreRule()));
//                    //3.1.4、指定评分详情
//                    totalScoreList.add(processMergeTotalScoreDetail(rangeType, typeScoreMap, EvaluateAuditSceneEnum.ITEM_SCORE.getScene(), EvaluateAuditSceneEnum.ITEM_SCORE.getScene(), null));
//                    //自定义流程指定评分
//                    totalScoreList.add(processMergeTotalScoreDetail(rangeType, typeScoreMap, EvaluateAuditSceneEnum.APPOINT_SCORE.getScene(), EvaluateAuditSceneEnum.APPOINT_SCORE.getScene(), null));
//                    //加分项总分
//                    List<PerfEvaluateTaskKpiModel> empTaskKpiList = taskKpiManager.queryTaskKpiBaseInfoByTaskId(taskId, empId, null);
//
//                    if (checkHasPlusAndSubtract(empTaskKpiList, empId, BusinessConstant.KPI_TYPE_CLASSIFY_PLUS)) {
//                        totalScoreList.add(processMergeTotalScoreDetail(rangeType, typeScoreMap, EvaluateAuditSceneEnum.PLUS_SCORE.getScene(), null, null));
//                    }
//                    //减分项总分
//                    if (checkHasPlusAndSubtract(empTaskKpiList, empId, BusinessConstant.KPI_TYPE_CLASSIFY_SUBTRACT)) {
//                        totalScoreList.add(processMergeTotalScoreDetail(rangeType, typeScoreMap, EvaluateAuditSceneEnum.SUBTRACT_SCORE.getScene(), null, null));
//                    }
//                    //自动评分总分
//                    ReportTaskUserTotalScoreVO autoScoreDetail = new ReportTaskUserTotalScoreVO();
//                    autoScoreDetail.setScorerType(EvaluateAuditSceneEnum.TOTAL_AUTO_SCORE.getScene());
//                    autoScoreDetail.setFinalScore(taskUser.getFinalItemAutoScore());
//                    autoScoreDetail.setTotalScoreResultList(null);
//                    totalScoreList.add(autoScoreDetail);
//                    taskUser.setTotalScoreDetailList(totalScoreList);
//                    //3.2、按指标类细分
//                    List<ReportTaskUserKpiTypeScoreVO> kpiTypeScoreDetailList = new ArrayList<>();
//                    Set<String> scoreTypeSet = new HashSet<>();
//                    for (String typeKey : typeScoreMap.keySet()) {
//                        List<PerfEvaluateTaskScoreResultVO> scoreList = typeScoreMap.get(typeKey);
//                        /*
//                        3.2.1、按指标分组
//                        3.2.2、按打分类型分组：指定评分人与自评-互评-上级评分
//                         */
//                        String[] arr = typeKey.split("&");
//                        String scorerType = arr[0];
//                        String kpiTypeId = arr.length == 2 ? arr[1] : null;
//                        String kpiTypeClassify = CollectionUtils.isEmpty(scoreList) ? null : scoreList.get(0).getKpiTypeClassify();
//                        BigDecimal maxExtraScore = CollectionUtils.isEmpty(scoreList) ? null : scoreList.get(0).getMaxExtraScore();
//                        String isOkr = CollectionUtils.isEmpty(scoreList) ? null : scoreList.get(0).getIsOkr();
//                        //排除总评
//                        if (EvaluateScoreSettingDTO.ScoreRule.ITEM.equals(scorerType) && kpiTypeId != null) {
//                            ReportTaskUserKpiTypeScoreVO kpiTypeScore = new ReportTaskUserKpiTypeScoreVO();
//                            kpiTypeScore.setTaskId(taskId);
//                            kpiTypeScore.setKpiTypeId(kpiTypeId);
//                            kpiTypeScore.setKpiTypeName(okrItems.kpiTypeName(kpiTypeId));
//                            kpiTypeScore.setKpiItemScoreDetailList(new ArrayList<>());
//                            kpiTypeScore.setKpiTypeClassify(kpiTypeClassify);
//                            kpiTypeScore.setMaxExtraScore(maxExtraScore);
//                            kpiTypeScore.setIsOkr(isOkr);
//                            kpiTypeScore.setTypeOrder(CollectionUtils.isEmpty(scoreList) ? null : scoreList.get(0).getTypeOrder());
//                            if (!BusinessConstant.TYPE_WEIGHT_SWITCH_CLOSE.equals(taskScorePo.getTypeWeightSwitch())) {
//                                kpiTypeScore.setKpiTypeWeight(CollectionUtils.isEmpty(scoreList) ? null : scoreList.get(0).getKpiTypeWeight());
//                            }
//                            //保存指标类打分详情 完成一个分类
//                            kpiTypeScoreDetailList.add(kpiTypeScore);
//
//                            //3.2.1、按指标分组
//                            Map<String, ReportTaskUserKpiItemScoreVO> kpiItemScoreMap = new HashMap<>(16);
//                            //3.2.2、按打分类型分组
//                            Map<String, Map<String, ReportTaskUserScorerTypeScoreVO>> itemScorerTypeScoreMap = new HashMap<>(16);
//                            for (PerfEvaluateTaskScoreResultVO r : scoreList) {
//                                ReportTaskUserKpiItemScoreVO kpiItemScoreVO = kpiItemScoreMap.get(r.getKpiItemId());
//                                if (kpiItemScoreVO == null) {
//                                    kpiItemScoreVO = new ReportTaskUserKpiItemScoreVO();
//                                    kpiItemScoreVO.setKpiItemId(r.getKpiItemId());
//                                    kpiItemScoreVO.setKpiItemName(r.getKpiItemName());
//                                    kpiItemScoreVO.setItemRule(r.getItemRule());
//                                    kpiItemScoreVO.setScoringRule(r.getScoringRule());
//                                    kpiItemScoreVO.setPlusLimit(r.getPlusLimit());
//                                    kpiItemScoreVO.setSubtractLimit(r.getSubtractLimit());
//                                    kpiItemScoreVO.setScorerTypeScoreDetailList(new ArrayList<>());
//                                    kpiItemScoreVO.setOrder(r.getOrder());
//                                    kpiItemScoreVO.setItemWeight(r.getItemWeight());
//                                    kpiItemScoreVO.setItemScoreValue(r.getItemScoreValue());
//                                    kpiItemScoreVO.setItemUnit(r.getItemUnit());
//                                    kpiItemScoreVO.setItemTargetValue(r.getItemTargetValue());
//                                    if (kpiTypeScore.isOkrType()) {
//                                        kpiItemScoreVO.setItemFinishValue(okrItems.getItemFinishValue(r.getKpiItemId()));
//                                    } else {
//                                        kpiItemScoreVO.setItemFinishValue(r.getItemFinishValue());
//                                    }
//                                    kpiItemScoreVO.setResultInputType(r.getResultInputType());
//                                    kpiItemScoreVO.setItemFormula(r.getItemFormula());
//                                    kpiItemScoreVO.setThresholdJson(r.getThresholdJson());
//                                    kpiItemScoreVO.setItemFieldJson(r.getItemFieldJson());
//                                    kpiItemScoreVO.setScorerType(r.getItemScorerType());
//                                    kpiItemScoreVO.setAutoScore(r.getItemAutoScore());
//                                    kpiItemScoreVO.setWorkItemFinishValue(r.getWorkItemFinishValue());
//                                    kpiItemScoreVO.setItemTags(companyTagDao.listTagName(companyId, r.getKpiItemId()));
//                                    kpiItemScoreVO.setInputChangeRecord(logManager.queryItemDynamic(taskUser.getId(), r.getKpiItemId()));
//                                    kpiItemScoreVO.setItemFullScoreCfg(r.getItemFullScoreCfg());
//
//                                    //设置打分范围
//                                    if (Objects.nonNull(fullScore)) {
//                                        if (PerfTemplBaseModel.SCORE_RANGE_TYPE_WEIGHT_SCORE.equals(scoreRangeType)) {
//                                            //满分值 * 指标权重
//                                            if (Objects.nonNull(r.getItemWeight())) {
//                                                kpiItemScoreVO.setItemFullScore(new BigDecimal(fullScoreValue).multiply(r.getItemWeight().divide(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP)));
//                                            }
//                                        } else {
//                                            kpiItemScoreVO.setItemFullScore(fullScore);
//                                        }
//                                    }
//
//                                    kpiItemScoreMap.put(r.getKpiItemId(), kpiItemScoreVO);
//                                    //保存指标打分详情
//                                    kpiTypeScore.getKpiItemScoreDetailList().add(kpiItemScoreVO);
//
//                                    Map<String, ReportTaskUserScorerTypeScoreVO> scorerTypeScoreMap = new HashMap<>(16);
//                                    itemScorerTypeScoreMap.put(r.getKpiItemId(), scorerTypeScoreMap);
//                                }
//                                Map<String, ReportTaskUserScorerTypeScoreVO> scorerTypeScoreMap = itemScorerTypeScoreMap.get(r.getKpiItemId());
//                                ReportTaskUserScorerTypeScoreVO scorerTypeScoreVO = scorerTypeScoreMap.get(r.getScorerType());
//                                if (scorerTypeScoreVO == null) {
//                                    scorerTypeScoreVO = new ReportTaskUserScorerTypeScoreVO();
//                                    scorerTypeScoreVO.setScorerType(r.getScorerType());
//                                    scorerTypeScoreVO.setScoreResultList(new ArrayList<>());
//                                    scorerTypeScoreMap.put(scorerTypeScoreVO.getScorerType(), scorerTypeScoreVO);
//                                    //保存打分类型详情
//                                    kpiItemScoreVO.getScorerTypeScoreDetailList().add(scorerTypeScoreVO);
//                                }
////                                if (!scorerTypeScoreVO.getScoreResultList().contains(r)) {
//                                scorerTypeScoreVO.getScoreResultList().add(r);
////                                }
//                                if (createdUser.equals(r.getScorerId())) {
//                                    scoreTypeSet.add(r.getScorerType());
//                                }
//                            }
//
//                            //处理各阶段分数
//                            setItemScore(kpiTypeScore);
//                            //计算完得分后将自动得分归为指标得分子类，便于前端处理
//                        }
//                    }
//                    taskUser.setKpiTypeScoreDetailList(kpiTypeScoreDetailList);
//                    // 查询当前操作人为哪些审核人角色
//                    if (createdUser.equals(taskBaseModel.getCreatedUser())) {
//                        //当前操作人是否为任务发起人
//                        scoreTypeSet.add("task_admin");
//                    }
//                    if (tempAdmin) {
//                        //模板管理员
//                        scoreTypeSet.add("temp_admin");
//                    }
//                    //查询当前操作人是否为校准人
//                    if (taskScoreResultManager.checkFinalResultAudit(taskId, empId, createdUser)) {
//                        scoreTypeSet.add(EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene());
//                    }
//                    if (CollectionUtils.isNotEmpty(scoreTypeSet)) {
//                        taskUser.setScoreTypes(scoreTypeSet);
//                    }
//                }
//            }
//            taskScorePo.setScoreResult(taskUser);
//        }
//        return taskScorePo;
//    }
}
