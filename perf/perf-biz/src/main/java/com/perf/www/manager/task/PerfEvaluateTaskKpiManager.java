package com.perf.www.manager.task;

import cn.com.polaris.kpi.eval.StaffItemType;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.perf.www.common.constant.BusinessConstant;
import com.perf.www.common.em.EvaluateTaskStatusEnum;
import com.perf.www.common.utils.string.StringTool;
import com.perf.www.domain.entity.company.EmpOrganizationModel;
import com.perf.www.domain.entity.company.EmployeeBaseInfoModel;
import com.perf.www.model.kpi.CompanyKpiItemModel;
import com.perf.www.model.perftmp.PerfTemplBaseModel;
import com.perf.www.model.task.PerfEvaluateTaskBaseModel;
import com.perf.www.model.task.PerfEvaluateTaskKpiModel;
import com.perf.www.model.task.PerfEvaluateTaskRefOkrModel;
import com.perf.www.model.task.PerfEvaluateTaskUserModel;
import com.perf.www.vo.task.EvaluationStaffVO;
import com.perf.www.vo.task.PerfEvaluateTaskKpiVO;
import com.perf.www.vo.task.item.PerfEvaluateTaskKpiItemDetailVO;
import com.perf.www.vo.task.query.EvaluationTaskUserQueryVO;
import com.perf.www.vo.task.taskuser.PerfEvaluateSimpleTaskUserVO;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.org.infr.emp.pojo.EmployeeBaseInfoDo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.NativeSQLBuilder;
import org.lufei.ibatis.builder.QueryBuilder;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.dao.AutoBaseDao;
import org.lufei.ibatis.mapper.PagedList;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class PerfEvaluateTaskKpiManager {
    @Resource
    private AutoBaseDao autoBaseDao;


//    public PerfEvaluateTaskKpiModel findById(String id) {
//        return autoBaseDao.findById(PerfEvaluateTaskKpiModel.class, id);
//    }

//    public void deleteTaskKpi(String taskBaseId, String createdUser) {
//        if(StringUtils.isEmpty(taskBaseId)) {
//            return;
//        }
//        UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_kpi").set("is_deleted", "true").set("updated_user", createdUser)
//                .whereEq("task_id", taskBaseId).whereEq("is_deleted", "false");
//        autoBaseDao.update(updateBuilder);
//    }

//    public void deleteTaskKpiByEmp(String taskBaseId, List<String> empIds, String createdUser) {
//        if(StringUtils.isEmpty(taskBaseId) || CollectionUtils.isEmpty(empIds)) {
//            return;
//        }
//        UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_kpi").set("is_deleted", "true").set("updated_user", createdUser)
//                .whereEq("task_id", taskBaseId).whereEq("is_deleted", "false").whereIn("emp_id", empIds);
//        autoBaseDao.update(updateBuilder);
//    }

//    public void deleteTaskKpiById(String id, String updateUser) {
//        if(StringUtils.isEmpty(id)) {
//            return;
//        }
//        UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_kpi").set("is_deleted", "true").set("updated_user", updateUser)
//                .whereEq("id", id).whereEq("is_deleted", "false");
//        autoBaseDao.update(updateBuilder);
//    }

//    public void addKpis(List<PerfEvaluateTaskKpiModel> kpis) {
//        if (CollectionUtils.isEmpty(kpis)) {
//            return;
//        }
//        kpis.forEach(kpi -> {
//            if (Objects.isNull(kpi.getId())) {
//                kpi.setId(UUID.randomUUID().toString());
//            }
//        });
//        autoBaseDao.saveBatch(kpis);
//    }

//    public void batchInsertTaskKpi(List<PerfEvaluateTaskKpiModel> list) {
//        if(CollectionUtils.isEmpty(list)) {
//            return;
//        }
//        StringBuilder sql = new StringBuilder("insert into perf_evaluate_task_kpi (id,company_id,org_id,task_id,emp_id,reviewer," +
//                "kpi_type_id,kpi_type_name,kpi_type_weight,kpi_item_id,kpi_item_name,item_target_value,item_finish_value,item_unit," +
//                "item_weight,result_input_type,result_input_emp_id,is_deleted,created_user,created_time,task_user_id,item_rule," +
//                "scoring_rule,scorer_type,scorer_obj_id,item_type,examine_oper_type,multiple_reviewers_type,kpi_type_classify," +
//                "plus_limit,subtract_limit,max_extra_score,`order`,item_formula,threshold_json,formula_condition,item_field_json," +
//                "is_type_locked,is_okr,type_order,reserve_okr_weight,points_rule,item_score_value,input_format,item_plan_flag," +
//                "show_target_value,must_result_input,show_finish_bar,manager_level,item_full_score_cfg,item_limit_cnt) values ");
//        for (PerfEvaluateTaskKpiModel model : list) {
//            sql.append(String.format("('%s','%s','%s','%s','%s','%s','%s','%s',%s,'%s','%s',%s,%s,'%s',%s,'%s','%s','false','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s',%s,%s,%s,%s,'%s','%s','%s','%s','%s','%s',%s,%s,'%s','%s','%s','%s','%s',%s,%s,'%s','%s','%s')",
//                    StringUtils.isEmpty(model.getId()) ? UUID.randomUUID().toString() : model.getId(), model.getCompanyId(), StringTool.handleNullToEmpty(model.getOrgId()), model.getTaskId(), model.getEmpId(), StringTool.handleNullToEmpty(model.getReviewer()),
//                    model.getKpiTypeId(), StringTool.handleNullToEmpty(model.getKpiTypeName()), model.getKpiTypeWeight(), model.getKpiItemId(), StringTool.handleNullToEmpty(model.getKpiItemName()),
//                    model.getItemTargetValue(), model.getItemFinishValue(), StringTool.handleNullToEmpty(model.getItemUnit()), model.getItemWeight(),
//                    StringTool.handleNullToEmpty(model.getResultInputType()), StringTool.handleNullToEmpty(model.getResultInputEmpId()), model.getCreatedUser(),
//                    DateTimeUtils.now2StrDateTime(), model.getTaskUserId(), StringTool.handleNullToEmpty(model.getItemRule()), StringTool.handleNullToEmpty(model.getScoringRule()), model.getScorerType(), StringTool.handleNullToEmpty(model.getScorerObjId()), StringTool.handleNullToEmpty(model.getItemType()), StringTool.handleNullToEmpty(model.getExamineOperType()),
//                    StringTool.handleNullToEmpty(model.getMultipleReviewersType()), StringTool.handleNullToEmpty(model.getKpiTypeClassify()), model.getPlusLimit(), model.getSubtractLimit(), model.getMaxExtraScore(), model.getOrder(), StringTool.handleNullToEmpty(model.getItemFormula()),
//                    StringTool.handleNullToEmpty(model.getThresholdJson()), StringTool.handleNullToEmpty(model.getFormulaCondition()), StringTool.handleNullToEmpty(model.getItemFieldJson()), StringTool.handleNullToEmpty(model.getIsTypeLocked()), StringTool.handleNullToEmpty(model.getIsOkr()),
//                    model.getTypeOrder(), model.getReserveOkrWeight(), StringTool.handleNullToEmpty(model.getPointsRule()), StringTool.handleNullToEmpty(model.getItemScoreValue()), StringTool.handleNullToEmpty(model.getInputFormat()), StringTool.handleNullToEmpty(model.getItemPlanFlag()),
//                    StringTool.handleNullToEmpty(model.getShowTargetValue()), model.getMustResultInput(),model.getShowFinishBar(),model.getManagerLevel(),model.getItemFullScoreCfg(),model.getItemLimitCnt())).append(",");
//        }
//        String sqlStr = StringUtils.removeEnd(sql.toString(), ",");
//        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskKpiModel.class).setSql(sqlStr);
//        autoBaseDao.nativeExecute(sqlBuilder);
//    }

//    public void updateTaskKpi(PerfEvaluateTaskKpiModel model) {
//        if(StringUtils.isEmpty(model.getId())) {
//            return;
//        }
//        UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_kpi").setBean(model).whereEq("id", model.getId());
//        autoBaseDao.update(updateBuilder);
//    }

//    public void updateScorerObjId(String id, String scorerObjId) {
//        if(StringUtils.isEmpty(id)) {
//            return;
//        }
//        UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_kpi").set("scorer_obj_id", scorerObjId).whereEq("id", id);
//        autoBaseDao.update(updateBuilder);
//    }

//    public void saveTaskKpi(PerfEvaluateTaskKpiModel model) {
//        model.setIsDeleted("false");
//        model.setId(UUID.randomUUID().toString());
//        autoBaseDao.save(model);
//    }


    public List<PerfEvaluateTaskKpiVO> queryTaskKpiByTaskId(String taskId, String companyId) {
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskKpiVO.class)
                .setSql("select DISTINCT p.*, e.`name` AS empName, re.name resultInputEmpName,re.avatar resultInputEmpAvatar,re.status resultInputEmpStatus,re.ding_user_id resultInputExtUserId, " +
                "(select GROUP_CONCAT(o.org_name) FROM emp_organization o WHERE o.company_id = #{companyId} and o.org_id in (select org_id from emp_ref_org where emp_id = p.emp_id and ref_type = 'org')) as orgName  " +
                "from perf_evaluate_task_kpi p  " +
                "LEFT JOIN employee_base_info e on p.emp_id = e.employee_id " +
                "left join employee_base_info re on re.employee_id = p.result_input_emp_id " +
                "WHERE p.task_id = #{taskId} and p.is_deleted = 'false' ");
        sqlBuilder.setValue("taskId", taskId);
        sqlBuilder.setValue("companyId", companyId);
        List<PerfEvaluateTaskKpiVO> items = autoBaseDao.listAll(sqlBuilder);
        if(items.isEmpty()){
            return items;
        }
        for (PerfEvaluateTaskKpiVO item : items) {
            List<String> inputEmpIds = item.resultInputEmpIds();
            ComQB empQb = ComQB.build(EmployeeBaseInfoDo.class)
                    .clearSelect().select("employee_id as emp_id, name ,ding_user_id,avatar,status").setRsType(PerfEvaluateTaskKpiVO.InputEmp.class)
                    .whereEqReq("company_id", companyId)
                    .whereIn("employeeId", inputEmpIds);
            if(CollUtil.isNotEmpty(inputEmpIds)){
                List<PerfEvaluateTaskKpiVO.InputEmp> inputers = autoBaseDao.listAll(empQb);
                if(CollUtil.isNotEmpty(inputers)){
                    item.setInputEmps(inputers);
                }
            }
        }
        return items;
    }

    public List<PerfEvaluateTaskKpiVO> queryTaskApiItemByTaskId(String taskId) {
        // left join perf_evaluate_task_ref_okr o on k.id = o.task_kpi_id and o.is_deleted = 'false'
        final ComQB qb = ComQB.buildDiff(PerfEvaluateTaskKpiVO.class, "perf_evaluate_task_kpi", "p")
                .leftJoin(PerfEvaluateTaskRefOkrModel.class,"o").appendOn(" p.id = o.task_kpi_id and o.is_deleted = 'false' and p.company_id = o.company_id ")
                .leftJoin(CompanyKpiItemModel.class, "i").appendOn(" i.id = p.kpi_item_id and i.company_id = p.company_id ")
                .clearSelect().select("DISTINCT p.kpi_type_id, p.kpi_type_name, p.kpi_item_id, p.kpi_item_name, p.item_unit, i.item_type,p.kpi_type_classify")
                .select("o.action_id")
                .whereEq("p.task_id", taskId)
                .appendWhere("p.is_deleted = 'false'")
                .orderBy("p.kpi_type_id ASC, p.`order` asc ");

        // NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskKpiVO.class).setSql("SELECT DISTINCT p.kpi_type_id, p.kpi_type_name, p.kpi_item_id, p.kpi_item_name, p.item_unit, i.item_type,p.kpi_type_classify FROM perf_evaluate_task_kpi p left join " +
        //         " company_kpi_item i on i.id = p.kpi_item_id " +
        //         " WHERE p.task_id = #{taskId} and p.is_deleted = 'false' order by p.kpi_type_id ASC, p.`order` asc  ");
        // sqlBuilder.setValue("taskId", taskId);
        return autoBaseDao.listAll(qb);
    }


//    public List<PerfEvaluateTaskKpiVO> listTaskApiItemByTaskId(String companyId,String taskUserId) {
//        // left join perf_evaluate_task_ref_okr o on k.id = o.task_kpi_id and o.is_deleted = 'false'
//        final ComQB qb = ComQB.buildDiff(PerfEvaluateTaskKpiVO.class, "perf_evaluate_task_kpi", "p")
//                .leftJoin(PerfEvaluateTaskRefOkrModel.class,"o").appendOn(" p.id = o.task_kpi_id and o.is_deleted = 'false' and p.company_id = o.company_id ")
//                .leftJoin(CompanyKpiItemModel.class, "i").appendOn(" i.id = p.kpi_item_id and i.company_id = p.company_id ")
//                .clearSelect().select("DISTINCT p.kpi_type_id, p.kpi_type_name, p.kpi_item_id, p.kpi_item_name, p.item_unit, i.item_type,p.kpi_type_classify")
//                .select("o.action_id")
//                .whereEqReq("p.companyId", companyId)
//                .whereEqReq("p.task_user_id", taskUserId)
//                .appendWhere("p.is_deleted = 'false'")
//                .orderBy("p.kpi_type_id ASC, p.`order` asc ");
//
//        // NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskKpiVO.class).setSql("SELECT DISTINCT p.kpi_type_id, p.kpi_type_name, p.kpi_item_id, p.kpi_item_name, p.item_unit, i.item_type,p.kpi_type_classify FROM perf_evaluate_task_kpi p left join " +
//        //         " company_kpi_item i on i.id = p.kpi_item_id " +
//        //         " WHERE p.task_id = #{taskId} and p.is_deleted = 'false' order by p.kpi_type_id ASC, p.`order` asc  ");
//        // sqlBuilder.setValue("taskId", taskId);
//        return autoBaseDao.listAll(qb);
//    }

    public List<PerfEvaluateTaskKpiVO> queryTaskApiByEmp(String companyId, String taskId, String empId, String orgId) {
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskKpiVO.class).setSql(
                "SELECT k.*,e.name empName,e.avatar,e.status empStatus,e.ding_user_id empExtUserId, ie.name itemScorer,ie.avatar itemScorerAvatar,ie.status itemScorerStatus,ie.ding_user_id itemScorerExtUserId" +
                        ", re.name resultInputEmpName,re.avatar resultInputEmpAvatar,re.status resultInputEmpStatus,re.ding_user_id resultInputExtUserId,o.action_id,o.target_id,o.target_name," +
                        "o.okr_task_id,o.okr_task_name,o.dept_name,o.evaluate_start_date,d.is_temporary as kpiTypeIsTemporary,i.item_plan_value " +
                        "FROM perf_evaluate_task_kpi k " +
                        "left join employee_base_info e on e.employee_id = k.emp_Id " +
                        "left join employee_base_info ie on ie.employee_id = k.scorer_obj_id " +
                        "left join employee_base_info re on re.employee_id = k.result_input_emp_id " +
                        "left join perf_evaluate_task_ref_okr o on k.id = o.task_kpi_id and o.is_deleted = 'false' " +
                        "left join company_dic d on d.id = k.kpi_type_id and d.is_deleted = 'false'" +
                        "left join company_kpi_item i on k.company_id = i.company_id and k.kpi_item_id = i.id " +
                        "where k.is_deleted = 'false' and k.task_id = #{taskId} and k.emp_id = #{empId} and k.company_id = #{companyId}");
        sqlBuilder.setValue("companyId", companyId);
        sqlBuilder.setValue("taskId", taskId);
        sqlBuilder.setValue("empId", empId);
        sqlBuilder.append("order by k.kpi_type_id ASC, k.`order` asc ");
        List<PerfEvaluateTaskKpiVO> kpis = autoBaseDao.listAll(sqlBuilder);
        if (CollUtil.isEmpty(kpis)) {
            return kpis;
        }
        Map<String, List<String>> scorerMap = new HashMap<>();
        Map<String, List<String>> inputMap = new HashMap<>();
        List<String> itemScorerIds = new ArrayList<>();
        List<String> inputEmpIds = new ArrayList<>();
        for (PerfEvaluateTaskKpiVO kpi : kpis) {
            List<EvaluationStaffVO> userStaffs = getScorerObj(kpi.getScorerObjId());
            if (CollUtil.isEmpty(userStaffs)) {
                continue;
            }
            userStaffs.forEach(user -> {
                List<String> scoreIds = user.getObjItems().stream().map(u -> u.getObjId()).collect(Collectors.toList());
                scorerMap.put(kpi.getId(), scoreIds);
                itemScorerIds.addAll(scoreIds);
            });
            if (StrUtil.isNotBlank(kpi.getResultInputEmpId())) {
                List<String> inputEmps = Arrays.asList(kpi.getResultInputEmpId().split(","));
                inputEmpIds.addAll(inputEmps);
                inputMap.put(kpi.getId(), inputEmps);
            }
        }
        if (CollUtil.isNotEmpty(itemScorerIds)) {
            List<PerfEvaluateTaskKpiVO.InputEmp> itemScorers = getItemScorers(itemScorerIds);
            Map<String, PerfEvaluateTaskKpiVO.InputEmp> DbScoreMap = CollUtil.toMap(itemScorers, new HashMap<>(), i -> i.getEmpId());
            for (PerfEvaluateTaskKpiVO kpi : kpis) {
                if (Objects.isNull(kpi.getItemScorers())) {
                    kpi.setItemScorers(new ArrayList<>());
                }
                List<String> scoreIds = scorerMap.get(kpi.getId());
                if (CollUtil.isEmpty(scoreIds)) {
                    continue;
                }
                scoreIds.forEach(id -> {
                    PerfEvaluateTaskKpiVO.InputEmp scoreList = DbScoreMap.get(id);
                    if(Objects.nonNull(scoreList)){
                        kpi.getItemScorers().add(scoreList);
                    }
                });
            }
        }
        if (CollUtil.isNotEmpty(inputEmpIds)) {
            List<PerfEvaluateTaskKpiVO.InputEmp> inputEmps = getItemScorers(inputEmpIds);
            Map<String, PerfEvaluateTaskKpiVO.InputEmp> DbScoreMap = CollUtil.toMap(inputEmps, new HashMap<>(), i -> i.getEmpId());
            for (PerfEvaluateTaskKpiVO kpi : kpis) {
                if (Objects.isNull(kpi.getInputEmps())) {
                    kpi.setInputEmps(new ArrayList<>());
                }
                List<String> inputIds = inputMap.get(kpi.getId());
                if (CollUtil.isEmpty(inputIds)) {
                    continue;
                }
                inputIds.forEach(id -> {
                    PerfEvaluateTaskKpiVO.InputEmp inputEmp = DbScoreMap.get(id);
                    if(Objects.nonNull(inputEmp)){
                        kpi.getInputEmps().add(inputEmp);
                    }
                });
            }
        }
        return kpis;
    }

    private List<EvaluationStaffVO> getScorerObj(String scorerObjId) {
        List<EvaluationStaffVO> evaluationStaffVOS = JSON.parseArray(scorerObjId, EvaluationStaffVO.class);
        if (CollUtil.isEmpty(evaluationStaffVOS)) {
            return new ArrayList<>();
        }
        return evaluationStaffVOS.stream().filter(staffVO ->
                StaffItemType.user.name().equals(staffVO.getObj_type())).collect(Collectors.toList());
    }

    public List<PerfEvaluateTaskKpiVO.InputEmp> getItemScorers(List<String> empIds){
        if(CollUtil.isEmpty(empIds)){
            return new ArrayList<>();
        }
        ComQB comQB=ComQB.build(EmployeeBaseInfoDo.class)
                .clearSelect().select("employee_id as emp_id ,name ,ding_user_id").setRsType(PerfEvaluateTaskKpiVO.InputEmp.class)
                .whereIn("employee_id",empIds);
        return autoBaseDao.listAll(comQB);
    }

//    public List<PerfEvaluateTaskKpiModel> queryTaskApiByIds(List<String> kpiIds, String companyId) {
//        if(CollectionUtils.isNotEmpty(kpiIds) && StringUtils.isNotBlank(companyId)) {
//            ComQB<PerfEvaluateTaskKpiModel> comQB = ComQB.build(PerfEvaluateTaskKpiModel.class).whereEq("is_deleted", "false")
//                    .whereIn("id", kpiIds);
//            return autoBaseDao.listAll(comQB);
//        }
//        return null;
//    }

//    public List<PerfEvaluateTaskKpiVO> queryTaskKpiByResultInputEmpId(String companyId, String inputEmpId, String taskId, String kpiItemId, String empId, Boolean isInput) {
//        ComQB<PerfEvaluateTaskKpiVO> comQB = ComQB.build(PerfEvaluateTaskKpiModel.class, "kpi")
//                .join(EmployeeBaseInfoModel.class, "e").on("e.employee_id", "kpi.emp_id")
//                .join(EmpOrganizationModel.class, "o").on("o.org_id", "kpi.org_id")
//                .join(PerfEvaluateTaskBaseModel.class, "t").on("t.id", "kpi.task_id")
//                .clearSelect().select("kpi.*,e.name empName,o.org_name,t.task_name,t.task_desc").setRsType(PerfEvaluateTaskKpiVO.class)
//                .whereEq("kpi.company_id", companyId)
//                .whereEq("kpi.result_input_emp_id", inputEmpId)
//                .whereEq("kpi.task_id", taskId).whereEq("kpi.kpi_item_id", kpiItemId).whereEq("kpi.emp_id", empId);
//        if(isInput != null) {
//            if(isInput) {
//                comQB.whereNotNull("kpi.item_finish_value");
//            } else {
//                comQB.whereIsNull("kpi.item_finish_value");
//            }
//        }
//        return autoBaseDao.listAll(comQB);
//    }

//    public List<PerfEvaluateTaskKpiScoreVO> queryItemByTask(String taskId, String empId, String orgId) {
//        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskKpiScoreVO.class).setSql("SELECT distinct k.kpi_item_id as id,k.scorer_type,k.scorer_obj_id as scorerUserId,k.item_formula," +
//                "k.kpi_type_id,k.item_weight,k.item_target_value,k.item_finish_value,k.kpi_type_weight,k.id as taskKpiId, k.item_full_score_cfg " +
//                "FROM perf_evaluate_task_kpi k   " +
//                "WHERE k.is_deleted = 'false'  " +
//                "AND k.task_id =#{taskId} and k.emp_id = #{empId} ");
//        sqlBuilder.setValue("taskId", taskId);
//        sqlBuilder.setValue("empId", empId);
//        return autoBaseDao.listAll(sqlBuilder);
//    }

//    public List<PerfEvaluateTaskKpiScoreVO> queryItemByIds(Set<String> items) {
//        if (CollectionUtils.isEmpty(items)) {
//            return new ArrayList<>();
//        }
//        ComQB<PerfEvaluateTaskKpiScoreVO> comQB = ComQB.build(PerfEvaluateTaskKpiModel.class)
//                .clearSelect()
//                .select(" kpi_item_id as id,scorer_type,scorer_obj_id as scorerUserId,item_formula," +
//                        " kpi_type_id,item_weight,item_target_value,item_finish_value,kpi_type_weight,id as taskKpiId,item_full_score_cfg ")
//                .setRsType(PerfEvaluateTaskKpiScoreVO.class)
//                .whereIn("id", items);
//        return autoBaseDao.listAll(comQB);
//    }

//    public PerfEvaluateTaskKpiModel queryTaskKpiByUser(String taskId, String empId, String orgId, String kpiTypeId, String kpiItemId) {
//        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskKpiModel.class).whereEq("task_id", taskId).whereEq("org_id", orgId)
//                .whereEq("emp_id", empId).whereEq("kpi_type_id", kpiTypeId).whereEq("kpi_item_id", kpiItemId).whereEq("is_deleted", "false");
//        List<PerfEvaluateTaskKpiModel> list = autoBaseDao.listAll(queryBuilder);
//        return CollectionUtils.isEmpty(list) ? null : list.get(0);
//    }

//    public BigDecimal listTaskKpiByUser(String taskId, String empId) {
//        ComQB<BigDecimal> comQB = ComQB.build(PerfEvaluateTaskKpiModel.class)
//                .clearSelect()
//                .select(" sum(item_weight)")
//                .setRsType(BigDecimal.class)
//                .whereEq("task_id", taskId)
//                .whereEq("emp_id", empId)
//                .whereEq("is_deleted", "false");
//        BigDecimal totalItemWeight = autoBaseDao.findOne(comQB);
//        return totalItemWeight;
//    }

//    public PerfEvaluateTaskKpiModel queryItemByTaskUser(String taskUserId, String kpiItemId) {
//        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskKpiModel.class).whereEq("task_user_id", taskUserId).whereEq("kpi_item_id", kpiItemId).whereEq("is_deleted", "false");
//        List<PerfEvaluateTaskKpiModel> list = autoBaseDao.listAll(queryBuilder);
//        return CollectionUtils.isEmpty(list) ? null : list.get(0);
//    }

    public PerfEvaluateTaskKpiVO queryTaskKpiDetailByUser(String taskId, String empId, String kpiTypeId, String kpiItemId) {
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskKpiVO.class).setSql("SELECT k.*,r.action_id FROM perf_evaluate_task_kpi k  " +
                "LEFT JOIN perf_evaluate_task_ref_okr r ON k.id = r.task_kpi_id AND r.is_deleted = 'false' " +
                "where k.task_id = #{taskId} " +
                "AND k.emp_id =#{empId} " +
                "AND k.kpi_item_id = #{kpiItemId} " +
                "AND k.is_deleted = 'false'");
        sqlBuilder.setValue("taskId", taskId);
        sqlBuilder.setValue("empId", empId);
        sqlBuilder.appendIfOpt("AND k.kpi_type_id = #{kpiTypeId} ", "kpiTypeId", kpiTypeId);
        sqlBuilder.setValue("kpiItemId", kpiItemId);
        List<PerfEvaluateTaskKpiVO> list = autoBaseDao.listAll(sqlBuilder);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        PerfEvaluateTaskKpiVO kpi = list.get(0);
        if (StrUtil.isBlank(kpi.getResultInputEmpId())) {
            return kpi;
        }
        ComQB comQB = ComQB.build(EmployeeBaseInfoDo.class)
                .clearSelect().select("employee_id as emp_id,name,ding_user_id").setRsType(PerfEvaluateTaskKpiVO.InputEmp.class)
                .whereIn("employee_id", Arrays.asList(kpi.getResultInputEmpId().split(",")))
                .whereEq("company_id", kpi.getCompanyId());
        kpi.setInputEmps(autoBaseDao.listAll(comQB));
        return kpi;
    }

    public List<PerfEvaluateTaskKpiModel> listTaskKpiByUsers(List<String> taskUserIds) {
        if(CollectionUtils.isNotEmpty(taskUserIds)) {
            ComQB<PerfEvaluateTaskKpiModel> comQB = ComQB.build(PerfEvaluateTaskKpiModel.class).whereEq("is_deleted", "false").whereIn("task_user_id", taskUserIds);
            return autoBaseDao.listAll(comQB);
        }
        return null;
    }

    public List<PerfEvaluateTaskKpiModel> listMeasurableItemProgress(String companyId, String taskId) {
        ComQB<PerfEvaluateTaskKpiModel> comQB = ComQB.build(PerfEvaluateTaskKpiModel.class, "kpi")
                .join(CompanyKpiItemModel.class, "i").on("i.id", "kpi.kpi_item_id")
                .clearSelect().select("kpi.*")
                .whereEq("i.item_type", "measurable")
                .whereEq("kpi.company_id", companyId).whereEq("kpi.task_id", taskId)
                .whereEq("kpi.is_deleted", "false");
        return autoBaseDao.listAll(comQB);
    }

    public List<PerfEvaluateTaskKpiVO> listEvaluateTaskKpi(String companyId, List<String> kpiItemIds, String startDate, String endDate, String year) {
        return listEvaluateTaskKpi(companyId, null, kpiItemIds, startDate, endDate, year, true,false);
    }

//    public List<PerfEvaluateTaskKpiVO> listEvaluateTaskKpi(String companyId, String taskId, List<String> kpiItemIds) {
//        return listEvaluateTaskKpi(companyId, taskId, kpiItemIds, null, null, null, true,true);
//    }

    public List<PerfEvaluateTaskKpiVO> listEvaluateTaskKpi(String companyId, String taskId, List<String> kpiItemIds, String startDate,
                                                           String endDate, String year, boolean showNotFinished,boolean openOkrScore) {
        List<String> statusList = new ArrayList<>();
        statusList.add(EvaluateTaskStatusEnum.FINISHED.getStatus());
        statusList.add(EvaluateTaskStatusEnum.TERMINATED.getStatus());
        if(showNotFinished) {
            statusList.add(TalentStatus.CONFIRMED.getStatus());
            statusList.add(TalentStatus.FINISH_VALUE_AUDIT.getStatus());
            statusList.add(EvaluateTaskStatusEnum.SCORING.getStatus());
            statusList.add(EvaluateTaskStatusEnum.RESULTS_AUDITING.getStatus());
            statusList.add(EvaluateTaskStatusEnum.RESULTS_AFFIRMING.getStatus());
            statusList.add(EvaluateTaskStatusEnum.WAIT_PUBLISHED.getStatus());
            statusList.add(TalentStatus.RESULTS_INTERVIEW.getStatus()); //增加面谈中
        }
        ComQB<PerfEvaluateTaskKpiVO> comQB = ComQB.build(PerfEvaluateTaskKpiModel.class, "kpi")
                .join(PerfEvaluateTaskBaseModel.class, "t").on("t.id", "kpi.task_id")
                .leftJoin(EmployeeBaseInfoModel.class, "e").on("e.employee_id", "kpi.emp_id")
                .leftJoin(PerfEvaluateTaskUserModel.class, "tu").on("kpi.task_user_id", "tu.id")
                .clearSelect().select("kpi.*,e.name empName,e.avatar,t.cycle_end_date,t.task_name," +
                        "(select GROUP_CONCAT(o.org_name) FROM emp_organization o WHERE o.company_id ='" + companyId + "' and o.org_id in (select org_id from emp_ref_org where emp_id = e.employee_id and ref_type = 'org')) as orgName")
                .setRsType(PerfEvaluateTaskKpiVO.class)
                .whereEq("t.id", taskId)
                .whereEq("t.company_id", companyId).whereIn("kpi.kpi_item_id", kpiItemIds)
                //指标完成度皆以考核周期结束日期为统计时间点
                .whereBigEq("t.cycle_end_date", startDate).whereLowEq("t.cycle_end_date", endDate)
                .whereEq("kpi.is_deleted", "false").whereEq("t.is_deleted", "false")
                .whereEq("e.is_delete","false").whereEq("e.status","on_the_job")
                .whereEq("tu.is_deleted","false") .whereIn("tu.task_status", statusList);
        if(openOkrScore){
            comQB.appendWhere(" (kpi.item_finish_value is not null or (kpi.open_okr_score=1))");
        }else {
            comQB.whereNotNull("kpi.item_finish_value");
        }
        if(Objects.nonNull(year)) {
            comQB.appendWhere("DATE_FORMAT(t.cycle_end_date,'%Y') = " + year);
        }
        comQB.orderBy("kpi.type_order, kpi.order");
        return autoBaseDao.listAll(comQB);
    }


//    public List<PerfEvaluateTaskKpiVO> listEvaluateTaskKpi(String companyId, String taskUserId) {
//        List<String> statusList = new ArrayList<>();
//        statusList.add(EvaluateTaskStatusEnum.FINISHED.getStatus());
//        statusList.add(EvaluateTaskStatusEnum.TERMINATED.getStatus());
//        statusList.add(EvaluateTaskStatusEnum.SCORING.getStatus());
//        statusList.add(EvaluateTaskStatusEnum.RESULTS_AUDITING.getStatus());
//        statusList.add(EvaluateTaskStatusEnum.RESULTS_AFFIRMING.getStatus());
//        statusList.add(EvaluateTaskStatusEnum.WAIT_PUBLISHED.getStatus());
//        ComQB<PerfEvaluateTaskKpiVO> comQB = ComQB.build(PerfEvaluateTaskKpiDo.class, "kpi")
//                .join(PerfEvaluateTaskBaseDo.class, "t").on("t.id", "kpi.task_id")
//                .leftJoin(PerfEvaluateTaskUserDo.class, "tu").on("kpi.task_user_id", "tu.id")
//                .clearSelect().select("kpi.*,tu.emp_name,tu.avatar,t.cycle_end_date,t.task_name,tu.emp_org_name org_name")
//                .setRsType(PerfEvaluateTaskKpiVO.class)
//                .whereEq("tu.id", taskUserId)
//                .whereEq("t.company_id", companyId)
//                //指标完成度皆以考核周期结束日期为统计时间点
//                .whereEq("kpi.is_deleted", "false").whereEq("t.is_deleted", "false")
//                .whereIn("tu.task_status", statusList)
//                .whereNotNull("kpi.item_finish_value");
//        comQB.orderBy("kpi.type_order, kpi.order");
//        return autoBaseDao.listAll(comQB);
//    }

    public PagedList<PerfEvaluateTaskKpiVO> listPagedEvaluateTaskKpi(String companyId, String startDate, String endDate, int pageNo, int pageSize) {
        ComQB<PerfEvaluateTaskKpiVO> comQB = ComQB.build(PerfEvaluateTaskKpiModel.class, "kpi")
                .join(PerfEvaluateTaskBaseModel.class, "t").on("t.id", "kpi.task_id")
                .leftJoin(PerfTemplBaseModel.class, "tp").on("tp.id", "t.templ_base_id")
                .leftJoin(EmployeeBaseInfoModel.class, "e").on("e.employee_id", "kpi.emp_id")
                .leftJoin(EmpOrganizationModel.class, "o").on("o.org_id", "kpi.org_id")
                .clearSelect().select("kpi.*,e.name empName,o.org_name,tp.name tmplName,t.task_name").setRsType(PerfEvaluateTaskKpiVO.class)
                .whereEq("t.company_id", companyId)
                .whereBigEq("t.cycle_start_date", startDate).whereLowEq("t.cycle_start_date", endDate)
                .whereEq("kpi.is_deleted", "false").whereEq("t.is_deleted", "false");
        comQB.setPage(pageNo, pageSize);
        return autoBaseDao.listPage(comQB);
    }

//    public List<PerfEvaluateTaskKpiModel> queryTaskKpiBaseInfoByTaskId(String taskId, String empId, String orgId) {
//        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskKpiModel.class).whereEq("is_deleted", "false")
//                .whereEq("task_id", taskId).whereEq("emp_id", empId).whereEq("org_id", orgId);
//        return autoBaseDao.listAll(queryBuilder);
//    }


//    public List<PerfEvaluateTaskKpiVO> queryFinishValueList(EvaluateTaskKpiQueryVO queryVO, CompanyModel company) {
//        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskKpiVO.class).setSql("select u.eval_org_id,u.eval_org_name,b.task_name,e.name as empName,e.ding_user_id, " +
//                "if(c.item_finish_value is not null,c.item_finish_value,k.item_finish_value) AS item_finish_value, " +
//                "if(c.work_item_finish_value is not null,c.work_item_finish_value,k.work_item_finish_value) AS work_item_finish_value, " +
//                "if(c.item_finish_value_text is not null,c.item_finish_value_text,k.item_finish_value_text) AS item_finish_value_text,k.*, " +
//                "(select GROUP_CONCAT(o.org_name) FROM emp_organization o WHERE o.company_id =#{companyId} and o.org_id in (select org_id from emp_ref_org where emp_id = e.employee_id and ref_type = 'org')) as orgName " +
//                "from perf_evaluate_task_kpi k  " +
//                "LEFT JOIN input_finish_val_cache c on k.company_id = c.company_id and k.task_user_id = c.task_user_id and c.operate_emp_id = #{createdUser} and k.kpi_item_id = c.kpi_item_id AND c.is_deleted = 'false' " +
//                "LEFT JOIN perf_evaluate_task_base b on k.task_id = b.id and k.company_id = b.company_id " +
//                "LEFT JOIN employee_base_info e on k.emp_id = e.employee_id and k.company_id = e.company_id " +
//                "LEFT JOIN perf_evaluate_task_user u on u.id = k.task_user_id and u.company_id = k.company_id ");
//        sqlBuilder.append("where k.company_id = #{companyId} and k.result_input_type in ('exam','user','manager','role') and FIND_IN_SET(#{createdUser}, IF(k.result_input_type = 'exam',k.emp_id,k.result_input_emp_id)) " +
//                "AND k.is_deleted = 'false' and b.is_deleted = 'false' and b.task_status = 'published' and u.task_status in ('confirmed', 'scoring') and u.is_deleted = 'false' ");
//        sqlBuilder.setValue("companyId", queryVO.getCompanyId());
//        sqlBuilder.setValue("createdUser", queryVO.getCreatedUser());
//        sqlBuilder.appendIfOpt("and b.performance_type = #{performanceType}", "performanceType", queryVO.getPerformanceType());
//        sqlBuilder.appendIfOpt("and k.task_id=#{taskId}", "taskId", queryVO.getTaskId());
//        sqlBuilder.appendIfOpt("and k.emp_id=#{empId}", "empId", queryVO.getEmpId());
//        sqlBuilder.appendIfOpt("and u.eval_org_id=#{evalOrgId}", "evalOrgId", queryVO.getEvalOrgId());
//        sqlBuilder.appendIfOpt("and k.final_submit_finish_value=#{finalSubmitFinishValue}", "finalSubmitFinishValue", 0);
//        sqlBuilder.appendIfOpt("and k.kpi_item_id=#{kpiItemId}", "kpiItemId", queryVO.getKpiItemId());
//        //没有人提交评分的任务还可以录完成值
//        if (!company.canResSubmitInputFinish()) {
//            sqlBuilder.append("AND NOT EXISTS (SELECT id FROM perf_evaluate_task_score_result WHERE task_id = k.task_id AND emp_id = k.emp_id and task_user_id = u.id AND audit_status = 'pass' AND scorer_type NOT IN ('modify_item_audit','change_item_audit')) ");
//        }
//        if ("emp".equals(queryVO.getOrderBy())) {
//            sqlBuilder.append("ORDER BY k.emp_id ");
//        } else {
//            sqlBuilder.append("ORDER BY k.kpi_item_name");
//        }
//        return autoBaseDao.listAll(sqlBuilder);
//    }

//    public String queryFinishInputUserByTask(String taskUserId, String companyId) {
//        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(String.class).setSql("SELECT GROUP_CONCAT(CONCAT_WS('&',name,status)) FROM employee_base_info WHERE FIND_IN_SET(employee_id,   " +
//                "                (SELECT GROUP_CONCAT( IF(k.result_input_type = 'exam',k.emp_id,k.result_input_emp_id)) FROM perf_evaluate_task_kpi k WHERE k.company_id = #{companyId} AND k.task_user_id = #{taskUserId}   " +
//                "                AND k.is_deleted = 'false' AND k.result_input_type in ('exam','user','manager') GROUP BY k.task_user_id)) AND company_id = #{companyId}  ");
//        sqlBuilder.setValue("taskUserId", taskUserId);
//        sqlBuilder.setValue("companyId", companyId);
//        return (String) autoBaseDao.findOne(sqlBuilder);
//    }

//    public List<EmployeeSimpleInfoVO> queryFinishInputUserInfoByTask(String taskUserId, String taskId, String empId) {
//        // NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(EmployeeSimpleInfoVO.class).setSql("SELECT DISTINCT employee_id as empId, name as empName, avatar FROM employee_base_info WHERE FIND_IN_SET(employee_id,   " +
//        //         "                (SELECT GROUP_CONCAT(IF(k.result_input_type = 'exam',k.emp_id,k.result_input_emp_id)) FROM perf_evaluate_task_kpi k WHERE k.task_user_id = #{taskUserId}    " +
//        //         "                AND k.is_deleted = 'false' AND k.result_input_type in ('exam','user') GROUP BY k.task_user_id))");
//        // sqlBuilder.setValue("taskUserId", taskUserId);
//        //
//        // return autoBaseDao.listAll(sqlBuilder);
//
//        PerfEvaluateTaskUserModel taskUser = autoBaseDao.findById(PerfEvaluateTaskUserModel.class, taskUserId);
//        //result表不能省，例如有指标变更审核，当前责任人
//        final ComQB taskResult = ComQB.build(PerfEvaluateTaskScoreResultModel.class)
//                .clearSelect().select("scorer_id").setRsType(String.class)
//                .whereEq("company_id", taskUser.getCompanyId())
//                .whereEq("task_id", taskUser.getTaskId())
//                .whereEq("emp_id", taskUser.getEmpId())
//                .whereEq("is_deleted", Boolean.FALSE.toString())
//                .appendWhere(" audit_status is null");
//        List<String> scorerIds = autoBaseDao.listAll(taskResult);
//        if (CollectionUtils.isEmpty(scorerIds)) {
//            final ComQB inq = ComQB.build(PerfEvaluateTaskKpiModel.class, "k")
//                    .clearSelect().select("IF(k.result_input_type= 'exam', k.emp_id, k.result_input_emp_id) emp_id ")
//                    .setRsType(String.class)
////                    .whereEq("task_user_id", taskUserId)
//                    .whereEqReq("task_id", taskId)
//                    .whereEqReq("emp_id", empId)
//                    .whereEq("k.is_deleted", "false")
//                    .appendWhere(" k.result_input_type in('exam', 'user') ");
//            List<String> resultInputEmps = autoBaseDao.listAll(inq);
//            if (CollectionUtils.isNotEmpty(resultInputEmps)) {
//                for (String resultInputEmp : resultInputEmps) {
//                    if (StringUtils.isBlank(resultInputEmp)) {
//                        continue;
//                    }
//                    scorerIds.addAll(Arrays.asList(resultInputEmp.split(",")));
//                }
//            }
//        }
//        if (CollectionUtils.isEmpty(scorerIds)) {
//            scorerIds.add(taskUser.getEmpId());
//        }
//        final ComQB qb = ComQB.build(EmployeeBaseInfoModel.class, "e")
//                .clearSelect().select("DISTINCT employee_id AS employeeId, name, avatar")
//                .whereIn("employee_id", scorerIds);
//        return autoBaseDao.listAll(qb);
//    }

//    public List<PerfEvaluateTaskKpiModel> queryTaskKpiByClassify(String taskId, String empId, String orgId, String kpiTypeClassify) {
//        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskKpiModel.class).whereEq("task_id", taskId).whereEq("org_id", orgId)
//                .whereEq("emp_id", empId).whereEq("kpi_type_classify", kpiTypeClassify).whereEq("is_deleted", "false");
//        List<PerfEvaluateTaskKpiModel> list = autoBaseDao.listAll(queryBuilder);
//        return list;
//    }

//    public void updateItemAutoScore(String id, BigDecimal itemAutoScore, String itemActualFormula, String autoScoreExFlag) {
//        if(StringUtils.isEmpty(id)) {
//            return;
//        }
//        UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_kpi").set("item_auto_score", itemAutoScore).set("item_actual_formula", itemActualFormula)
//                .set("auto_score_ex_flag", autoScoreExFlag).whereEq("id", id);
//        autoBaseDao.update(updateBuilder);
//    }

//    public BigDecimal queryItemAutoScoreSum(String companyId, String taskUserId) {
//        ComQB comQB = ComQB.build(PerfEvaluateTaskKpiModel.class).whereEq("company_id", companyId).whereEq("task_user_id", taskUserId)
//                .whereEq("scorer_type", "auto").whereEq("is_deleted", "false");
//        comQB.clearSelect().select("SUM(item_auto_score)").setRsType(BigDecimal.class);
//        return (BigDecimal) autoBaseDao.findOne(comQB);
//    }

//    public List<PerfEvaluateTaskKpiModel> queryFinishValueNullKpi(String companyId, String taskUserId, String id) {
//        if (StringUtils.isBlank(taskUserId) && StringUtils.isBlank(id)) {
//            return null;
//        }
//        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskKpiModel.class).setSql("SELECT k.* FROM  " +
//                " perf_evaluate_task_kpi k  " +
//                "LEFT JOIN perf_evaluate_task_item_score_rule s on s.task_user_id = k.task_user_id AND s.kpi_item_id = k.kpi_item_id AND s.is_deleted = 'false'  " +
//                "WHERE k.company_id = #{companyId} AND k.is_deleted = 'false' AND k.scorer_type = 'auto' AND k.item_finish_value IS NULL AND s.id is NULL");
//        sqlBuilder.setValue("companyId", companyId);
//        sqlBuilder.appendIfOpt("and k.task_user_id = #{taskUserId}", "taskUserId", taskUserId);
//        sqlBuilder.appendIfOpt("and k.id = #{id}", "id", id);
//        List list = autoBaseDao.listAll(sqlBuilder);
//
//        ComQB<PerfEvaluateTaskKpiModel> comQB = ComQB.build(PerfEvaluateTaskKpiModel.class)
//                .whereEq("company_id", companyId)
//                .whereEq("must_result_input", true)
//                .whereEq("is_deleted", "false")
//                .whereNotEq("scorer_type", "auto")
//                .whereEq("task_user_id", taskUserId)
//                .whereEq("id", id)
//                .appendWhere(" if(kpi_type_classify = 'workItem', \n" +
//                        "(work_item_finish_value = '' OR work_item_finish_value IS NULL),\n" +
//                        "item_finish_value IS NULL)");
//        list.addAll(autoBaseDao.listAll(comQB));
//        return list;
//    }

//    public String queryItemNameByItemIds(String companyId, String taskId, String empId, List<String> itemIds) {
//        ComQB comQB = ComQB.build(PerfEvaluateTaskKpiModel.class).whereEq("company_id", companyId).whereEq("task_id", taskId).whereEq("emp_id", empId)
//                .whereIn("kpi_item_id", itemIds).clearSelect().select("GROUP_CONCAT(kpi_item_name)").setRsType(String.class);
//        return (String) autoBaseDao.findOne(comQB);
//    }




    public List<PerfEvaluateTaskKpiVO> queryNoFinishValueTask(String companyId, List<String> taskUserIds) {
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskKpiVO.class).setSql("SELECT t.*, (SELECT GROUP_CONCAT(name) Fid)ROM employee_base_info WHERE company_id = #{companyId} and FIND_IN_SET(employee_id, t.result_input_emp_) as resultInputEmpName from " +
                "(SELECT k.id, k.task_id, k.emp_id, k.task_user_id, k.kpi_item_id, k.kpi_item_name,k.urging_flag, e1.`name` as empName,e1.avatar AS avatar,IF(k.result_input_type = 'exam',k.emp_id, k.result_input_emp_id) as result_input_emp_id FROM `perf_evaluate_task_kpi` k " +
                "LEFT JOIN employee_base_info e1 on e1.employee_id = k.emp_id " +
                "LEFT JOIN perf_evaluate_task_item_score_rule s on s.task_user_id = k.task_user_id AND s.kpi_item_id = k.kpi_item_id AND s.is_deleted = 'false' " +
                "WHERE k.company_id = #{companyId} and k.task_user_id in " + StringTool.getInStr(taskUserIds) + " and k.scorer_type = 'auto' AND k.is_deleted = 'false' and k.item_finish_value is null AND s.id is NULL " +
                ")t ");
        sqlBuilder.setValue("companyId", companyId);
        List list = autoBaseDao.listAll(sqlBuilder);

        NativeSQLBuilder sqlBuilder2 = NativeSQLBuilder.build(PerfEvaluateTaskKpiVO.class).setSql("SELECT\n" +
                "  t.*,\n" +
                "  (SELECT GROUP_CONCAT(name)\n" +
                "   FROM employee_base_info\n" +
                "   WHERE company_id = #{companyId} and FIND_IN_SET(employee_id, t.result_input_emp_id)) AS resultInputEmpName,\n" +
                "  (SELECT GROUP_CONCAT(ding_user_id)\n" +
                "   FROM employee_base_info\n" +
                "   WHERE company_id = #{companyId} and FIND_IN_SET(employee_id, t.result_input_emp_id)) AS resultInputDingId\n" +
                "FROM (SELECT\n" +
                "        k.id,\n" +
                "        k.task_id,\n" +
                "        k.kpi_type_classify,\n" +
                "        k.emp_id,\n" +
                "        k.task_user_id,\n" +
                "        k.kpi_item_id,\n" +
                "        k.kpi_item_name,\n" +
                "        k.urging_flag,\n" +
                "        e1.`name`  AS empName,\n" +
                "        e1.avatar  AS avatar,\n" +
                "        IF(k.result_input_type = 'exam', k.emp_id, k.result_input_emp_id) AS result_input_emp_id\n" +
                "      FROM `perf_evaluate_task_kpi` k LEFT JOIN employee_base_info e1 ON e1.employee_id = k.emp_id\n" +
                "      WHERE k.company_id = #{companyId} AND\n" +
                "            k.task_user_id IN   " + StringTool.getInStr(taskUserIds) + "  AND  k.must_result_input= TRUE " +
                " AND k.is_deleted = 'false' and k.scorer_type != 'auto' AND if(kpi_type_classify='workItem',(work_item_finish_value='' or work_item_finish_value is null),k.item_finish_value IS NULL) " +
                " and if (k.item_type = 'non-measurable' and k.input_format = 'text', (item_finish_value_text = '' OR item_finish_value_text IS NULL ), k.item_finish_value IS NULL) ) t");
        sqlBuilder2.setValue("companyId", companyId);
        list.addAll(autoBaseDao.listAll(sqlBuilder2));
        return list;
    }

    public void batchUpdateUrgingFlag(List<String> ids, String companyId) {
        if(CollectionUtils.isEmpty(ids)) {
            return;
        }
        UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_kpi").set("urging_flag", "true").whereEq("company_id", companyId)
                .whereIn("id", ids);
        autoBaseDao.update(updateBuilder);
    }

    public List<PerfEvaluateTaskKpiModel> queryNoUrgingFinishValueKpi(String companyId, String taskUserId, String id) {
        if (StringUtils.isBlank(taskUserId) && StringUtils.isBlank(id)) {
            return null;
        }
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskKpiModel.class)
                .whereEq("company_id", companyId)
                .whereEq("is_deleted", "false")
                .whereEq("task_user_id", taskUserId)
                //.whereEq("scorer_type", "auto")
                //.whereIsNull("item_finish_value")
                .whereEq("id", id)
                .appendWhere(" (scorer_type = 'auto' or `must_result_input` =true) and if(kpi_type_classify = 'workItem',\n" +
                        "          (work_item_finish_value = '' OR work_item_finish_value IS NULL),\n" +
                        "          item_finish_value IS NULL)")
                .whereIsNull("urging_flag");
        return autoBaseDao.listAll(queryBuilder);
    }

//    public void updateOkrRefFlag(String companyId, String createdUser, List<String> idList, String okrRefFlag) {
//        if(CollectionUtils.isEmpty(idList)) {
//            return;
//        }
//        UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_kpi").set("okr_ref_flag", okrRefFlag);
//        if(StringUtils.isNotEmpty(createdUser)) {
//            updateBuilder.set("updated_user", createdUser);
//        }
//
//        updateBuilder.whereEq("company_id", companyId).whereIn("id", idList);
//        autoBaseDao.update(updateBuilder);
//    }
//
//    public void updateFinishValue(String companyId, String createdUser, String id, BigDecimal finishValue) {
//        if(StringUtils.isEmpty(id)) {
//            return;
//        }
//        UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_kpi").set("item_finish_value", finishValue);
//        if(StringUtils.isNotEmpty(createdUser)) {
//            updateBuilder.set("updated_user", createdUser);
//        }
//
//        updateBuilder.whereEq("company_id", companyId).whereEq("id", id);
//        autoBaseDao.update(updateBuilder);
//    }

//    public void updateTargetValue(String companyId, String createdUser, String id, BigDecimal targetValue) {
//        if(StringUtils.isEmpty(id)) {
//            return;
//        }
//        UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_kpi").set("item_target_value", targetValue);
//        if(StringUtils.isNotEmpty(createdUser)) {
//            updateBuilder.set("updated_user", createdUser);
//        }
//
//        updateBuilder.whereEq("company_id", companyId).whereEq("id", id);
//        autoBaseDao.update(updateBuilder);
//    }

//    public List<OKRGetItemInfoReturnVO> queryOkrRefItemInfo(List<String> taskKpiId) {
//        if(CollectionUtils.isEmpty(taskKpiId)) {
//            return null;
//        }
//        ComQB comQB = ComQB.build(PerfEvaluateTaskKpiModel.class, "k").leftJoin(PerfEvaluateTaskBaseModel.class, "b")
//                .on("k.task_id", "b.id")
//                .whereIn("k.id", taskKpiId);
//        comQB.clearSelect().select("k.id as refKpiItemId,k.kpi_item_name,k.item_target_value,k.item_weight,b.task_name,k.item_rule,k.scoring_rule,k.item_unit,k.is_deleted,k.result_input_type,k.item_finish_value,k.task_id,b.cycle_start_date,b.cycle_end_date,b.cycle_type,k.emp_id ")
//                .setRsType(OKRGetItemInfoReturnVO.class);
//        return autoBaseDao.listAll(comQB);
//    }

//    public List<OKRItemDetailVO> getTaskList(OKRTaskQueryVO queryVO) {
//        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(OKRItemDetailVO.class).setSql("SELECT b.id as taskId, b.task_name, b.cycle_start_date, b.cycle_end_date, b.cycle_type, " +
//                "k.id as refKpiItemId, k.kpi_item_name, k.item_target_value, k.item_weight, k.item_rule, k.scoring_rule,k.item_unit,u.task_status " +
//                "FROM perf_evaluate_task_kpi k " +
//                "LEFT JOIN perf_evaluate_task_base b ON k.task_id = b.id " +
//                "LEFT JOIN perf_evaluate_task_user u ON k.task_user_id = u.id " +
//                "WHERE k.is_deleted = 'false' AND b.is_deleted = 'false' AND b.task_status = 'published' AND (k.is_okr = 'false' or k.is_okr is null OR k.is_okr = '')   " +
//                "AND k.company_id = #{companyId}");
//        sqlBuilder.setValue("companyId", queryVO.getCompanyId());
//        sqlBuilder.appendIfOpt("AND k.emp_id = #{empId}", "empId", queryVO.getEmpId());
//        sqlBuilder.appendIfOpt("AND b.cycle_start_date >= #{startTime}", "startTime", queryVO.getStartTime());
//        sqlBuilder.appendIfOpt("AND b.cycle_start_date <= #{endTime}", "endTime", queryVO.getEndTime());
//        sqlBuilder.appendIfOpt("AND u.task_status = #{taskStatus}", "taskStatus", queryVO.getTaskStatus());
//        sqlBuilder.appendIfOpt("AND (b.task_name LIKE CONCAT('%',#{keyword},'%') OR k.kpi_item_name LIKE CONCAT('%',#{keyword},'%'))", "keyword", queryVO.getKeyword());
//        sqlBuilder.append("ORDER BY b.created_time DESC");
//        return autoBaseDao.listAll(sqlBuilder);
//    }

//    public List<OKRGetItemInfoReturnVO> queryTaskItems(OKRTaskQueryVO queryVO) {
//        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(OKRItemDetailVO.class).setSql("SELECT b.id as taskId, b.task_name, b.cycle_start_date, b.cycle_end_date, b.cycle_type, " +
//                "k.id as refKpiItemId, k.kpi_item_name, k.item_target_value, k.item_weight, k.item_rule, k.scoring_rule,k.item_unit " +
//                "FROM perf_evaluate_task_kpi k " +
//                "LEFT JOIN perf_evaluate_task_user u ON k.task_user_id = u.id " +
//                "LEFT JOIN perf_evaluate_task_base b ON u.task_id = b.id " +
//                "WHERE k.is_deleted = 'false' AND (k.is_okr = 'false' or k.is_okr is null OR k.is_okr = '') AND b.is_deleted = 'false' AND b.task_status = 'published' AND u.is_deleted = 'false'  " +
//                "AND k.company_id = #{companyId}  AND k.emp_id = #{empId}  ");
//        sqlBuilder.setValue("companyId", queryVO.getCompanyId());
//        sqlBuilder.setValue("empId", queryVO.getEmpId());
//        sqlBuilder.appendIfOpt("AND k.task_id = #{taskId}", "taskId", queryVO.getTaskId());
//        sqlBuilder.appendIfOpt("AND k.kpi_item_name LIKE CONCAT('%',#{keyword},'%')", "keyword", queryVO.getKeyword());
//        sqlBuilder.append("ORDER BY k.type_order, k.order");
//        return autoBaseDao.listAll(sqlBuilder);
//    }
//

    public PerfEvaluateTaskKpiItemDetailVO queryKpiItemDetail(String taskId, String empId, String kpiItemId) {
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskKpiItemDetailVO.class).setSql("SELECT k.*,e1.`name` as itemScorerName,e2.`name` AS taskEmpName,b.evaluate_type,s.self_score_flag,s.mutual_score_flag,s.superior_score_flag,s.appoint_score_flag    " +
                "FROM perf_evaluate_task_kpi k   " +
                "LEFT JOIN employee_base_info e1 on e1.employee_id = k.scorer_obj_id  " +
                "LEFT JOIN employee_base_info e2 ON e2.employee_id = k.emp_id  " +
                "LEFT JOIN perf_evaluate_task_base b ON k.task_id = b.id AND b.is_deleted = 'false' " +
                "LEFT JOIN perf_evaluate_task_item_score_rule s ON k.kpi_item_id = s.kpi_item_id AND k.task_user_id = s.task_user_id AND s.is_deleted = 'false' " +
                "WHERE k.task_id = #{taskId} AND k.emp_id = #{empId} AND k.id =#{kpiItemId} AND k.is_deleted = 'false'");
        sqlBuilder.setValue("taskId", taskId);
        sqlBuilder.setValue("empId", empId);
        sqlBuilder.setValue("kpiItemId", kpiItemId);
        final PerfEvaluateTaskKpiItemDetailVO exist = (PerfEvaluateTaskKpiItemDetailVO) autoBaseDao.findOne(sqlBuilder);
        if(exist != null) {
            exist.setInputEmpName(getInputEmpName(exist));
            return exist;
        }
        // 以下为旧兼容代码 .
        sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskKpiItemDetailVO.class).setSql("SELECT k.*,e1.`name` as itemScorerName,e2.`name` AS taskEmpName,b.evaluate_type,s.self_score_flag,s.mutual_score_flag,s.superior_score_flag,s.appoint_score_flag    " +
                "FROM perf_evaluate_task_kpi k   " +
                "LEFT JOIN employee_base_info e1 on e1.employee_id = k.scorer_obj_id  " +
                "LEFT JOIN employee_base_info e2 ON e2.employee_id = k.emp_id  " +
                "LEFT JOIN perf_evaluate_task_base b ON k.task_id = b.id AND b.is_deleted = 'false' " +
                "LEFT JOIN perf_evaluate_task_item_score_rule s ON k.kpi_item_id = s.kpi_item_id AND k.task_user_id = s.task_user_id AND s.is_deleted = 'false' " +
                "WHERE k.task_id = #{taskId} AND k.emp_id = #{empId} AND k.kpi_item_id =#{kpiItemId} AND k.is_deleted = 'false'");
        sqlBuilder.setValue("taskId", taskId);
        sqlBuilder.setValue("empId", empId);
        sqlBuilder.setValue("kpiItemId", kpiItemId);
        PerfEvaluateTaskKpiItemDetailVO item = (PerfEvaluateTaskKpiItemDetailVO) autoBaseDao.findOne(sqlBuilder);
        item.setInputEmpName(getInputEmpName(item));
        return item;
    }

    private String getInputEmpName(PerfEvaluateTaskKpiItemDetailVO item) {
        if(Objects.isNull(item) || StringUtils.isEmpty(item.getResultInputEmpId())){
            return "";
        }
        ComQB resultEmpNameQb =ComQB.build(EmployeeBaseInfoDo.class)
                .clearSelect().select("name").setRsType(String.class)
                .whereEqReq("employee_id",item.getResultInputEmpId());
        String inputEmpName = autoBaseDao.findOne(resultEmpNameQb);
        return inputEmpName;
    }

    public Boolean checkExamKpi(String taskId, String empId) {
        if(StringUtils.isBlank(taskId)) {
            return false;
        }
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskKpiModel.class).whereEq("task_id", taskId).whereEq("emp_id", empId).whereEq("is_deleted", "false")
                .whereEq("scorer_type", BusinessConstant.EXAM);
        return CollectionUtils.isNotEmpty(autoBaseDao.listAll(queryBuilder));
    }

//    public Boolean checkNotAllAutoScore(String companyId, String taskId) {
//        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskKpiModel.class).whereEq("task_id", taskId).whereEq("company_id", companyId).whereEq("is_deleted", "false")
//                .whereNotEq("scorer_type", BusinessConstant.SCORER_TYPE_AUTO);
//        return CollectionUtils.isNotEmpty(autoBaseDao.listAll(queryBuilder));
//    }

//    public Boolean checkHasItemScore(String companyId, String taskId) {
//        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskKpiModel.class).whereEq("task_id", taskId).whereEq("company_id", companyId).whereEq("is_deleted", "false")
//                .whereEq("scorer_type", BusinessConstant.APPROVER_TYPE_USER);
//        return CollectionUtils.isNotEmpty(autoBaseDao.listAll(queryBuilder));
//    }

//    public PagedList<PerfEvaluateTaskKpiSimpleVO> pagedFinishedItemSimpleInfo(EvaluationTaskUserQueryVO queryVO) {
//        ComQB comQB = ComQB.build(PerfEvaluateTaskKpiDo.class, "k")
//                .join(PerfEvaluateTaskBaseDo.class, "b")
//                .appendOn("b.company_id = k.company_id AND k.task_id = b.id")
//                .join(PerfEvaluateTaskUserModel.class, "u")
//                .appendOn("k.company_id = u.company_id AND k.task_id = u.task_id AND k.emp_id = u.emp_id")
//                .clearSelect().select("DISTINCT k.kpi_item_name, k.kpi_item_id, k.item_unit, k.kpi_type_classify")
//                .setRsType(PerfEvaluateTaskKpiSimpleVO.class)
//                .whereEq("k.company_id", queryVO.getCompanyId())
//                .whereEq("k.is_deleted", Boolean.FALSE.toString())
//                .whereEq("b.task_status", "published")
//                .whereEq("b.is_deleted", Boolean.FALSE.toString())
//                .whereEq("u.is_deleted", Boolean.FALSE.toString())
//                .whereEq("u.task_status", "finished")
//                .whereLike("k.kpi_item_name", queryVO.getKeyword())
//                .whereEq("b.cycle_id", queryVO.getCycleId())
//                .whereEq("b.cycle_type", queryVO.getCycleType());
//        if (StringUtils.isNotBlank(queryVO.getCycleStartDate()) && StringUtils.isNotBlank(queryVO.getCycleEndDate())) {
//            comQB.appendWhere("(b.cycle_start_date BETWEEN '" + queryVO.getCycleStartDate() + "' " +
//                    "AND '" + queryVO.getCycleEndDate() + "' OR b.cycle_end_date BETWEEN '"
//                    + queryVO.getCycleStartDate() + "' AND '" + queryVO.getCycleEndDate() + "') ");
//        }
//        comQB.groupBy("k.kpi_item_name,k.kpi_item_id,k.item_unit")
//                .orderByDesc("k.kpi_item_name")
//                .setPage(queryVO.getPageNo(), queryVO.getPageSize());
//        return autoBaseDao.listPage(comQB);
//    }

    public PagedList<PerfEvaluateTaskBaseModel> pagedItemReportTask(EvaluationTaskUserQueryVO queryVO) {
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskBaseModel.class).setSql("SELECT DISTINCT b.id,b.task_name FROM perf_evaluate_task_kpi k   " +
                "JOIN perf_evaluate_task_base b ON b.company_id = #{companyId} AND k.task_id = b.id  " +
                "JOIN perf_evaluate_cycle ec on  b.cycle_id=ec.id " +
                "JOIN perf_evaluate_task_user u ON u.company_id = #{companyId} AND k.task_user_id = u.id " +
                "WHERE k.company_id = #{companyId} AND k.is_deleted = 'false' AND b.task_status = 'published' AND b.is_deleted = 'false' AND u.is_deleted = 'false' AND u.task_status = 'finished' ");
        sqlBuilder.setValue("companyId", queryVO.getCompanyId());
        sqlBuilder.appendIfOpt("AND b.cycle_type = #{cycleType} ", "cycleType", queryVO.getCycleType());
        sqlBuilder.appendIfOpt("AND k.kpi_item_id = #{kpiItemId}", "kpiItemId", queryVO.getItemId());
        if(StringUtils.isNotBlank(queryVO.getCycleStartDate()) && StringUtils.isNotBlank(queryVO.getCycleEndDate())) {
            sqlBuilder.append("AND (ec.cycle_start BETWEEN #{cycleStartDate} AND #{cycleEndDate} OR ec.cycle_end BETWEEN #{cycleStartDate} AND #{cycleEndDate}) ");
            sqlBuilder.setValue("cycleStartDate", queryVO.getCycleStartDate());
            sqlBuilder.setValue("cycleEndDate", queryVO.getCycleEndDate());
        }
        if (StringUtils.isNotBlank(queryVO.getCycleId())) {
            sqlBuilder.append(" and b.cycle_Id=#{cycleId} ");
            sqlBuilder.setValue("cycleId", queryVO.getCycleId());
        }
        sqlBuilder.append("ORDER BY b.created_time DESC");
        sqlBuilder.setPage(queryVO.getPageNo(), queryVO.getPageSize());
        return autoBaseDao.listPage(sqlBuilder);
    }

    public List<PerfEvaluateTaskKpiModel> queryTaskKpi(String companyId, String taskId, String empId) {
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskKpiModel.class).whereEq("company_id", companyId).whereEq("task_id", taskId)
                .whereEq("emp_id", empId).whereEq("is_deleted", "false");
        List<PerfEvaluateTaskKpiModel> list = autoBaseDao.listAll(queryBuilder);
        return list;
    }

//    public List<PerfEvaluateTaskKpiModel> queryTaskKpiByEmpIds(String companyId, String taskId, List<String> empIds) {
//        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskKpiModel.class).whereEq("company_id", companyId).whereEq("task_id", taskId)
//                .whereIn("emp_id", empIds).whereEq("is_deleted", "false");
//        List<PerfEvaluateTaskKpiModel> list = autoBaseDao.listAll(queryBuilder);
//        return list;
//    }

    public List<PerfEvaluateSimpleTaskUserVO> queryNoWorkItemEmp(String companyId, String taskId, List<String> taskUserIds) {
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateSimpleTaskUserVO.class).setSql("SELECT k.task_id, k.task_user_id, k.emp_id, e1.`name` AS empName,k.result_input_type,k.result_input_emp_id,  " +
                "IF(k.result_input_type = 'user',(SELECT GROUP_CONCAT(`name`) FROM employee_base_info WHERE company_id = k.company_id AND FIND_IN_SET(employee_id,k.result_input_emp_id)), e1.`name`) AS finishValueInputEmp  " +
                "FROM perf_evaluate_task_kpi k   " +
                "LEFT JOIN employee_base_info e1 ON k.company_id = e1.company_id AND k.emp_id = e1.employee_id  " +
                "WHERE k.company_id = #{companyId} AND k.is_deleted='false' AND k.task_id = #{taskId}  " +
                "AND k.result_input_type != 'no' AND k.work_item_finish_value is NULL AND k.kpi_type_classify = 'workItem' ");
        sqlBuilder.setValue("companyId", companyId);
        sqlBuilder.setValue("taskId", taskId);
        if(CollectionUtils.isNotEmpty(taskUserIds)) {
            sqlBuilder.append("AND k.task_user_id in " + StringTool.getInStr(taskUserIds));
        }
        return autoBaseDao.listAll(sqlBuilder);
    }

//    public void updateEmptyType(List<Map> isDelEmptyTypes, String taskBaseId, String emp) {
//        for (Map del : isDelEmptyTypes) {
//            if(StringUtils.isEmpty(taskBaseId) || StringUtils.isEmpty(emp) || StringUtils.isEmpty(String.valueOf(del.get("kpiTypeId")))) {
//                continue;
//            }
//            UpdateBuilder updateBuilder = UpdateBuilder.build(PerfEvaluateTaskOkrTypeModel.class)
//                    .set("is_deleted", "true")
//                    .whereEq("task_id", taskBaseId)
//                    .whereEq("emp_id", emp)
//                    .whereEq("kpi_type_id", del.get("kpiTypeId"));
//            autoBaseDao.update(updateBuilder);
//        }
//    }

    public List<PerfEvaluateTaskRefOkrModel> listTargetName(List<String> ids) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskRefOkrModel.class)
                .clearSelect()
                .select(" target_name,task_kpi_id")
                .whereIn("task_kpi_id", ids);
        return autoBaseDao.listAll(comQB);
    }

//    public Boolean existNoInputValue(String taskId, String empId) {
//        ComQB<PerfEvaluateTaskKpiModel> comQB = ComQB.build(PerfEvaluateTaskKpiModel.class)
//                .whereEq("task_id", taskId)
//                .whereEq("emp_id", empId)
//                .whereEq("scorerType", ScoreTypeEnum.AUTO.getType())
//                .whereEq("is_deleted", Boolean.FALSE.toString())
//                .appendWhere(" (item_finish_value is null or item_auto_score is null)");
//        List<PerfEvaluateTaskKpiModel> kpis = autoBaseDao.listAll(comQB);
//        if (CollectionUtils.isEmpty(kpis)) {
//            return false;
//        }
//        return true;
//    }

//    public void itemAutoScoreIsNull(String taskId, String empId) {
//        if (StringUtils.isEmpty(taskId) || StringUtils.isEmpty(empId)) {
//            return;
//        }
//        UpdateBuilder update = UpdateBuilder.build(PerfEvaluateTaskKpiModel.class)
//                .set("itemAutoScore", null)
//                .whereEq("is_deleted", Boolean.FALSE.toString())
//                .whereEq("task_id", taskId)
//                .whereEq("emp_id", empId);
//        autoBaseDao.update(update);
//    }
}
