package com.polaris.kpi.eval.app.task.dto.sumit;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.MDC;

import java.util.List;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.app.task.dto.sumit
 * @Author: lufei
 * @CreateTime: 2023-12-11  09:52
 * @Description: 批量批分
 * @Version: 1.0
 */
@Getter
@Setter
public class BatchSubmitScoreCmd {
    private String tid;
    private String companyId;
    private String opEmpId;
    private List<CmdWrap> scoreCmdWraps;
    private boolean isV1;


    public BatchSubmitScoreCmd(String companyId, String opEmpId, List<CmdWrap> scoreCmdWraps) {
        this.companyId = companyId;
        this.opEmpId = opEmpId;
        this.scoreCmdWraps = scoreCmdWraps;
    }

    public void trace() {
        MDC.put("tid", tid);
    }

    public boolean isV1() {
        return isV1;
    }

    @Getter
    @Setter
    public static class CmdWrap {
        private String taskUserId;
        private List<SubmitScoreCmd> nodeScores;
        private TotalLevelCmd totalLevelRs;

        public void checkAndBuild(String companyId, String empId) {

            //if (CollUtil.isEmpty(nodeScores)) {
            //    throw new IllegalArgumentException("nodeScores不能为空 ");
            //}

            if (StrUtil.isBlank(taskUserId)) {
                this.taskUserId = nodeScores.get(0).getTaskUserId();
            }
            if(CollUtil.isNotEmpty(nodeScores)){
                for (SubmitScoreCmd nodeScore : nodeScores) {
                    nodeScore.submitScoreLog(new TenantId(companyId), empId);
                    nodeScore.checkParamNullAndBuild();
                    nodeScore.setEmpId(empId);
                }
            }

            if (totalLevelRs != null) {
                totalLevelRs.setTaskUserId(this.getTaskUserId());
                totalLevelRs.submitScoreLog(new TenantId(companyId), empId);
                totalLevelRs.checkParamNullAndBuild();
            }
        }
    }
}
