package com.polaris.kpi.eval.app.task.appsvc;

import cn.com.polaris.kpi.EmpStaff;
import cn.com.polaris.kpi.ItemDecompose;
import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.com.polaris.kpi.company.MsgSceneEnum;
import cn.com.polaris.kpi.eval.OperationTypeEnum;
import cn.com.polaris.kpi.eval.RaterNode;
import cn.com.polaris.kpi.eval.ScoreEmp;
import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.perf.www.common.em.CompanyMsgActionEnum;
import com.perf.www.common.em.OperationLogSceneEnum;
import com.polaris.acl.kpi.eval.domain.ExtBatchEvalResult;
import com.polaris.acl.kpi.eval.domain.ExtTalentEvalResult;
import com.polaris.acl.kpi.eval.face.ExtDataPushAcl;
import com.polaris.acl.msg.domain.FinishWorkReq;
import com.polaris.acl.msg.face.MsgAcl;
import com.polaris.kpi.ask.domain.acl.AskEvalAcl;
import com.polaris.kpi.common.event.ExceptionEvent;
import com.polaris.kpi.eval.app.TransactionWrap;
import com.polaris.kpi.eval.app.task.dto.RejectScoreCmd;
import com.polaris.kpi.eval.app.task.dto.ResetScoreEmpCmd;
import com.polaris.kpi.eval.domain.task.acl.CyclePushAcl;
import com.polaris.kpi.eval.domain.task.acl.ScoreMsgAcl;
import com.polaris.kpi.eval.domain.task.acl.TalentEvalResult;
import com.polaris.kpi.eval.domain.task.dmsvc.*;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.empeval.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.chain.ChainNode;
import com.polaris.kpi.eval.domain.task.entity.flow.FlowRater;
import com.polaris.kpi.eval.domain.task.entity.flow.LevelAuditFlow;
import com.polaris.kpi.eval.domain.task.entity.grade.ScoreRule;
import com.polaris.kpi.eval.domain.task.entity.interview.EvalTaskInterviewConf;
import com.polaris.kpi.eval.domain.task.entity.msg.MsgTodoAggregate;
import com.polaris.kpi.eval.domain.task.event.FinishedValueAuditEvent;
import com.polaris.kpi.eval.domain.task.event.KpiItemUpdateFinishedValueEvent;
import com.polaris.kpi.eval.domain.task.event.ThisStageEnded;
import com.polaris.kpi.eval.domain.task.event.admineval.EmpCntOfAdminTaskChanged;
import com.polaris.kpi.eval.domain.task.event.admineval.EvalInputNotifySend;
import com.polaris.kpi.eval.domain.task.event.msg.CancelTodoEvent;
import com.polaris.kpi.eval.domain.task.event.talent.*;
import com.polaris.kpi.eval.domain.task.event.talent.scoring.ChainNodeEnd;
import com.polaris.kpi.eval.domain.task.event.talent.scoring.ItemNodeStart;
import com.polaris.kpi.eval.domain.task.event.talent.scoring.ScoreNodeEnd;
import com.polaris.kpi.eval.domain.task.repo.*;
import com.polaris.kpi.eval.domain.task.type.ScoringNodeEnum;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.eval.infr.acl.ding.imp.ZnxcEvalResultPusher;
import com.polaris.kpi.eval.infr.cycle.dao.CycleEvalDao;
import com.polaris.kpi.eval.infr.task.dao.*;
import com.polaris.kpi.eval.infr.task.ppojo.OperationLogDo;
import com.polaris.kpi.eval.infr.task.repimpl.TaskKpiRepoImpl;
import com.polaris.kpi.org.domain.common.BaseEvent;
import com.polaris.kpi.org.domain.dept.entity.CompanyConf;
import com.polaris.kpi.org.domain.dept.entity.CompanyMsgCenter;
import com.polaris.kpi.org.domain.dept.entity.Tenant;
import com.polaris.kpi.org.domain.dept.repo.MsgCenterRepo;
import com.polaris.kpi.org.domain.dept.type.TaskId;
import com.polaris.kpi.org.domain.emp.entity.LeaderManger;
import com.polaris.kpi.org.domain.emp.type.Emp;
import com.polaris.kpi.org.infr.company.dao.CompanyDaoImpl;
import com.polaris.kpi.org.infr.company.dao.CompanyMsgCenterDao;
import com.polaris.kpi.org.infr.dept.dao.KpiOrgDao;
import com.polaris.kpi.org.infr.emp.dao.KpiEmpDao;
import com.polaris.kpi.out.domain.IOutputConnector;
import com.polaris.kpi.out.domain.OutputConnectorRepo;
import com.polaris.kpi.setting.domain.entity.ResultAuditFlow;
import com.polaris.kpi.setting.domain.entity.ScorerTodoSummary;
import com.polaris.kpi.setting.domain.entity.TaskUserScorer;
import com.polaris.kpi.setting.domain.repo.ResultAuditFlowRepo;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.lufei.ibatis.common.data.ToDataBuilder;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/***
 * 考核流程状态阶段变化处理应用服务
 */
@Slf4j
@Service
public class EvalStageAppSvc {
    @Autowired
    private TaskUserRepo userRepo;
    @Autowired
    private CycleEvalDmSvc evalDmSvc;
    @Autowired
    private CompanyDaoImpl companyDao;
    @Autowired
    private KpiEmpDao kpiEmpDao;
    @Autowired
    private CyclePushAcl cyclePushAcl;
    @Autowired
    private ExtDataPushAcl dataPushAcl;
    @Autowired
    private OutputConnectorRepo connectorRepo;
    @Autowired
    private GradeDao gradeDao;
    @Autowired
    private KpiOrgDao kpiOrgDao;
    @Autowired
    private TaskKpiRepoImpl kpiRepo;
    @Autowired
    private EmpEvalRuleRepo ruleRepo;
    @Autowired
    private CycleDao cycleDao;
    @Autowired
    private EvaluateTaskRepo taskRepo;
    @Autowired
    private EmpEvalRuleRepo empRuleRepo;
    @Autowired
    private OpLogDao opLogDao;
    @Autowired
    private MsgCenterRepo msgCenterRepo;
    @Resource
    private EvalScorerNodeRepo evalScorerNodeRepo;
    @Autowired
    private TaskUserDao userDao;
    @Autowired
    private EmpEvalDao empEvalDao;
    @Autowired
    private AdminTaskRepo adminTaskRepo;
    @Resource
    private CycleEvalDao cycleEvalDao;
    @Autowired
    private ResultAuditFlowRepo resultAuditFlowRepo;
    @Autowired
    private ResultAuditFlowRepo auditFlowRepo;
    @Autowired
    private AdminTaskDao adminTaskDao;
    @Autowired
    private EvalTaskInterviewRepo interviewRepo;
    @Autowired
    private AskEvalAcl askEvalAcl;

    @Autowired
    private TransactionWrap tx;
    @Autowired
    private TransactionWrap tranWrap;
    @Autowired
    private TaskAppealRepo taskAppealRepo;
    @Autowired
    private CompanyMsgCenterDao centerDao;
    @Autowired
    private ScorerSummaryTodoRepo scorerSummaryTodoRepo;
    @Autowired
    private MsgAcl msgAcl;
    @Autowired
    private ZnxcEvalResultPusher znxcEvalResultPusher;

    @Autowired
    private EmpEvalScorerRepo empEvalScorerRepo;
    @Autowired
    private OnScoreEvalRepo onScoreEvalRepo;
    @Autowired
    private MsgCenterRepo centerRepo;
    @Autowired
    private ScoreMsgAcl scoreMsgAcl;

    //仅新人考核发起使用
    @Transactional
    public void startTalentEval(boolean isOpenPointsRule, CycleEval cycleEval, EmpStaff empStaff, Map<String, ItemDecompose> decomposeMap) {

        EvalUser taskUser = evalDmSvc.createEvalUser(isOpenPointsRule, cycleEval, empStaff, decomposeMap);
        ScoreRule empScoreRule = gradeDao.getEmpScoreRule(taskUser.getCompanyId(), taskUser.getEmpId(), taskUser.getOrgId());
        taskUser.setIsNewEmp(cycleEval.getIsNewEmp());
        taskUser.scoreRanges(empScoreRule.getRanges());
        taskUser.updateInputFinishStatus();
        //设置具体完成值录入人
        //empManagerAppSvc.setKpiInputManager(taskUser.getKpis());
        userRepo.addTaskUser(taskUser);
        //考核进入创建阶段
        new TalentEvalCreated(cycleEval, taskUser, TalentStatus.PUBLISHED).publish();
        new ThisStageEnded(cycleEval, taskUser, TalentStatus.PUBLISHED).publish();
        //  处理 publishTask 1277行 网络io操作入低先级队列
    }

    //考核开始阶段启动
    @Transactional
    public void handCreatedStageStart(CreatedStageStart event) {
        final AsAdminTaskRule cycleTask = event.getCycleEval();
        final EvalUser taskUser = event.getTaskUser();
        if (!MsgSceneEnum.noExtMsgTodoCompanys.contains(taskUser.getCompanyId().getId())) {
            // 给考核人发送通知与消息
            new TaskMsg().createTaskMsg(cycleTask.companyId().getId(), cycleTask.taskName().getName(),
                    taskUser.getTaskId(), new Emp(taskUser.getEmpId())).publish();
        }
        userRepo.updateStage(taskUser.getCompanyId(), taskUser.getId(), TalentStatus.CREATED);
        new ThisStageEnded(cycleTask, taskUser, TalentStatus.CREATED).publish();
    }

    public int updateStage(TenantId companyId, String taskUserId, TalentStatus stageStatus) {
        EvalUser taskUser = userRepo.getBaseTaskUser(companyId, taskUserId);
        taskUser.setTaskStatus(stageStatus.getStatus());
        taskUser.setSubStatus(stageStatus.getSubStatus());
        tx.runTran(() -> userRepo.updateStageSubStaus(taskUser));
        taskUser.setVersion(taskUser.getVersion() + 1);
        return taskUser.getVersion() + 1;
    }


    public void batchUpdateStageSubStaus(TenantId companyId, List<EvalUser> taskUsers, TalentStatus stageStatus) {
        for (EvalUser taskUser : taskUsers) {
            taskUser.setTaskStatus(stageStatus.getStatus());
            taskUser.setSubStatus(stageStatus.getSubStatus());
        }
        tx.runTran(() -> userRepo.batchUpdateStageSubStaus(companyId, taskUsers));
    }

    //考核执行阶段启动
    @Transactional
    public void handConfirmedStageStart(ConfirmedStageStart event) {
        event.start();
        // EvaluateTaskBiz.finishConfirmed 2454
        final AsAdminTaskRule cycleTask = event.getCycleEval();
        final EvalUser taskUser = event.getTaskUser();
        EmpEvalMerge evalMerge = ruleRepo.getEmpEvalMerge(taskUser.getCompanyId(), taskUser.getId(), EmpEvalMerge.all);
        evalMerge.initIndexRaters();//更新一下评价关系缓存
        ruleRepo.updateIndexRaters(evalMerge);
        taskUser.curStatus(TalentStatus.CONFIRMED, Collections.emptyList());
        taskUser.updateInputFinishStatus();

        /**1.0升级2.0的任务需使用兼容后的EmpEvalRule*/
        if (taskUser.wasTempTask()) {
            EmpEvalRule rule = new EmpEvalRule();
            BeanUtil.copyProperties(evalMerge, rule, true);
            taskUser.setEmpEvalRule(rule);
        }
        String cycleEnd = null;
        if (taskUser.isNewEmp()) {
            cycleEnd = adminTaskDao.getAdminTaskBase(taskUser.getCompanyId(), new TaskId(taskUser.getTaskId())).getCycleEndDate();
        } else {
            cycleEnd = cycleEvalDao.findCycle(taskUser.getCompanyId(), taskUser.getCycleId()).getCycleEnd();
        }
        if (taskUser.confirmedAutoEnterScore(cycleEnd, event.isReset(), evalMerge)) {
            userRepo.updateTaskUser(taskUser);//记录当前考核状态
            new KpiItemUpdateFinishedValueEvent(taskUser.getCompanyId(), taskUser, new EmpId(StrUtil.isBlank(event.getOpEmpId()) ? taskUser.getCreatedUser() : event.getOpEmpId()), true).publish();
            return;
        }

        List<EvalKpi> kpis = CollUtil.isEmpty(taskUser.getKpis()) ? userRepo.listTaskKpis(taskUser.getCompanyId(), taskUser.getId()) : taskUser.getKpis();
        if (CollUtil.isNotEmpty(kpis)) {
            taskUser.setKpis(kpis);
        // taskUser.forAllKpiAuto(kpis);
            taskUser.updateInputFinishStatus();
            final CompanyConf conf = companyDao.findCompanyConf(taskUser.getCompanyId());
            LeaderManger leaderManger = new LeaderManger((companyId, empId) -> kpiOrgDao.listEmpsManager(companyId, empId));
            Set<String> inputEmpIds = taskUser.confirmedInputEmp(conf.openInputOnScoring(), leaderManger);
            if (CollUtil.isNotEmpty(inputEmpIds)) {
                // 更新负责人信息
                List<KpiEmp> inputEmps = kpiEmpDao.listByEmp(taskUser.getCompanyId(), inputEmpIds);
                taskUser.reviewers(inputEmps);
                kpiRepo.inputItems(taskUser.getKpis());
                // 给指标值录入人生成作台待办 StringUtils.join(kpiItemIdList, ",")
                new EvalInputNotifySend(evalMerge.getInputNotify(), taskUser,
                        cycleTask.taskName(), inputEmpIds).publish();
            } else {
                log.info("无指标需要录入完成值,taskUserId:{}", taskUser.getId());
            }
        }
        userRepo.updateTaskUser(taskUser);
        //给被指定人、主管、角色设置互评人发送待办通知
        new TalentEvalConfirmedEvent(new Tenant(taskUser.getCompanyId().getId()), cycleTask.taskName().getName(),
                taskUser.getTaskId(), taskUser.getEmpId(), taskUser.getId())
                .publish();
        //推钉钉人事主数据 需要独立运行,不然会使用当前事务延长提交, 导致绩效的状态错误
        //doPushExtData(cycleTask, taskUser);
    }

    // 指标确认阶段启动
    @Transactional
    public void handAffirmStageStart(AffirmStageStart event) {
        event.start();
        final TalentStatus curStatus = TalentStatus.CONFIRMING;
        final AsAdminTaskRule cycleTask = event.getCycleEval();
        final EvalUser taskUser = event.getTaskUser();
        if (!cycleTask.needAffirm(taskUser) || taskUser.modifyItemAuditIsEmpty()) {//不需要确认或没有审核人跳过此阶段
            new ThisStageEnded(cycleTask, taskUser, curStatus, event.getOpEmpId()).publish();
            return;
        }
        //确认人
        LevelAuditFlow flow = userRepo.loadAuditFlow(taskUser.getCompanyId(), taskUser.getId(), EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT);
        flow.dispatchFirst();
        log.debug("handAffirmStageStart.flow:{}", JSONUtil.toJsonStr(flow));
        //userRepo.updateLevelFlow(flow.curAudits(), null, flow.curLevelRs());
        //没有确认人,自动跳过
        if (CollUtil.isEmpty(flow.curLevelRs())) {
            new ThisStageEnded(cycleTask, taskUser, curStatus, event.getOpEmpId()).publish();
            return;
        }
        SkipAuditNodeDmSvc nodeDmSvc = new SkipAuditNodeDmSvc(taskUser.getCompanyId().getId(), taskUser.getId(), flow, EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT.getScene());
        nodeDmSvc.setRepo(userRepo);
        nodeDmSvc.startSkip(1);
        opLogDao.batchSaveLogDomain(nodeDmSvc.getLogs());
        //当前阶段结束
        if (nodeDmSvc.isEnd()) {
            //opLogDao.saveOperationLog(cmd.getTenantId().getId(), cmd.getTaskUserId(),
            //        cmd.getConfirmEmpId().getId(), OperationLogSceneEnum.ITEM_AUDIT_FINISHED.getScene(), null);
            CycleEval cycleEval = taskRepo.getMergeCycleEval(taskUser.getCompanyId(), taskUser.getId());
            new ThisStageEnded(cycleEval, taskUser, TalentStatus.CONFIRMING, event.getOpEmpId()).publish();
            return;
        }
        //需要确认
        List<KpiEmp> kpiEmps = kpiEmpDao.listByEmp(taskUser.getCompanyId(), nodeDmSvc.getRecEmpId());
        taskUser.curStatus(curStatus, kpiEmps);
        taskUser.initConfirmDeadLine(cycleTask.limitConfirmDays());
        userRepo.updateTaskUser(taskUser);
        MsgSceneEnum msgScene = CollUtil.isEmpty(flow.nextAudits()) ? MsgSceneEnum.TASK_CONFIRM : MsgSceneEnum.TASK_ENACT;
        // 给确认者发通知与消息
        new MsgTodoAggregate(taskUser.getCompanyId(),
                taskUser.getTaskId(), cycleTask.taskName(), taskUser.getEmpId(), taskUser.getId())
                .useScene(msgScene, CompanyMsgActionEnum.CONFIRM)
                .addExtTempValue("evalEmpName",taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                .addExtTempValue("deadLineDate", taskUser.joinDeadLineStr(TalentStatus.CONFIRMING.getStatus()))
                .addTodoItem("msg.task.emp", taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                .sendExtMsg().addCenterMsg().sendExtTodo().addRecEmpId(nodeDmSvc.getRecEmpId()).publish();
    }

    //考核评分阶段启动 1.0
    @Transactional
    public void handScoringStageStart(ScoringStageStart event) {
        final EvalUser taskUser = event.getTaskUser();
        CycleEval cycleTask = taskRepo.getMergeCycleEval(taskUser.getCompanyId(), taskUser.getId());
        //CycleEval task = taskRepo.getMergeCycleEval(companyId, evalUser.getId());
        CompanyConf conf = companyDao.findCompanyConf(taskUser.getCompanyId());
        //有配置模板满分分值优先取模板满分值，否则取系统满分值
        BigDecimal fullScore = cycleTask.getCustomFullScore() == null ? companyDao.findFullScoreValue(cycleTask.getCompanyId()) : cycleTask.getCustomFullScore();
        if (cycleTask.getTemplBaseJson().isOpenPointsRule()) {
            // TODO 计算积分转到成功进入评分后处理 taskBiz.3343 线上没有公司在使用..只有测试公司在使用
        }
        String todoScene = MsgSceneEnum.TASK_SUBMIT_PROGRESS.getType();
        //不按owner,把所有录入完成值的待办清一次 taskBiz.3384 TaskDingDingCancelTodoMsgSubscribe 估计是个兼容代码,录入完成时应该完成待办才对
        new CancelTodoEvent(taskUser.getCompanyId(), taskUser.getId(), todoScene).publish();
        String setMutualAudit = MsgSceneEnum.SET_MUTUAL_AUDIT.getType();
        new CancelTodoEvent(taskUser.getCompanyId(), taskUser.getId(), setMutualAudit).publish();

        //  二次通知 控制发送录入通知 因为评分阶段中第一位提交评分前还可以录入/更新 taskBiz.3386
        if (conf.openInputOnScoring()) {
            new MsgTodoAggregate(taskUser.getCompanyId(), taskUser.getTaskId(), cycleTask.getTaskName(),
                    taskUser.getEmpId(), taskUser.getId())
                    .useScene(MsgSceneEnum.TASK_SUBMIT_AUTO_ITEM_PROGRESS, CompanyMsgActionEnum.SUBMIT_PROGRESS)
                    .addExtTempValue("empName",taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                    .addTodoItem("msg.task.emp", taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                    .sendExtTodo().sendExtMsg().addCenterMsg()
                    .addRecEmpId(taskUser.scoringInputEmp()).publish();
        }

        //  计算自动指标分
        log.info("开始自动评分 taskUserId:{}", taskUser.getId());
        boolean computed = conf.openInputOnScoring() ? false : taskUser.tryComputeAutoScore(cycleTask.isTypeWeightOpen(),
                fullScore, cycleTask.isFullScoreRange(), conf.openItemAutoScoreMultiplWeight());
        userRepo.updateAutoScore(taskUser);
        if (!cycleTask.isCustom()) {
            new ItemNodeStart(cycleTask, taskUser).publish();
        }
        new ScoreNodeEnd(cycleTask, taskUser, ScoringNodeEnum.SCORING_AUTO).publish();
        //推钉钉人事主数据 需要独立运行,不然会使用当前事务延长提交, 导致绩效的状态错误
        //doPushExtData(cycleTask, taskUser);
    }

    //考核评分阶段启动 2.0
    public void handScoringStageStart2(ScoringStageStart event) {
        event.startProcess();
        final EvalUser taskUser = event.getTaskUser();
        taskUser.enterScoring();
        tx.runTran(() -> userRepo.updateStageSubStaus(taskUser));
        taskUser.setVersion(taskUser.getVersion() + 1);

        EmpEvalMerge evalMerge = ruleRepo.getEmpEvalMerge(taskUser.getCompanyId(), taskUser.getId(), EmpEvalMerge.all);
        TenantId companyId = evalMerge.getCompanyId();
        //有配置模板满分分值优先取模板满分值，否则取系统满分值
        BigDecimal fullScore = evalMerge.getScoreValueConf().getCustomFullScore();
        String setMutualAudit = MsgSceneEnum.SET_MUTUAL_AUDIT.getType();
        new CancelTodoEvent(companyId, taskUser.getId(), setMutualAudit).publish();
        //  二次通知 控制发送录入通知 因为评分阶段中第一位提交评分前还可以录入/更新 taskBiz.3386
        //改为第一人评分后在发起，因为没有人评分前，普通指标的代办还没有关闭
        //if (evalMerge.inputOnScoring()) {
        //    new MsgTodoAggregate(companyId, taskUser.getTaskId(), new Name(evalMerge.getTaskName()), taskUser.getEmpId(), taskUser.getId())
        //            .useScene(MsgSceneEnum.TASK_SUBMIT_AUTO_ITEM_PROGRESS, CompanyMsgActionEnum.SUBMIT_PROGRESS)
        //            .sendExtTodo().sendExtMsg().addCenterMsg().addRecEmpId(taskUser.scoringInputEmp()).publish();
        //}

        //  计算自动指标分
        log.info("开始自动评分 taskUserId:{}", taskUser.getId());
        boolean isTypeWeightOpen = evalMerge.isTypeWeightOpen();
        boolean computed = !evalMerge.inputOnScoring() && taskUser.tryComputeAutoScore(isTypeWeightOpen, fullScore
                , evalMerge.getScoreValueConf().isFullScoreRange(), evalMerge.openItemAutoScoreMultiplWeight());
        if (computed) {
            tx.runTran(() -> userRepo.updateAutoScore(taskUser));
            taskUser.accCollecResult(1);
        }
        //if (!event.getCycleEval().isCustom()) {
        //    new ItemNodeStart(evalMerge, taskUser).publish();
        //}
        taskUser.setSendMsg(event.isSendInputMsg());
        ChainNode current = evalMerge.current(SubScoreNodeEnum.AUTO, 1);
        //分发360问卷
        askEvalAcl.dispatched(taskUser.getCompanyId().getId(), taskUser.getId(), taskUser.getUpdatedUser());
        new ChainNodeEnd(evalMerge, taskUser, current).publish();
    }

    // 结果校准阶段启动 taskBiz.step4[4915]
    public void handResultAuditingStageStart(ResultsAuditingStageStart event) {
        event.start();
        final TalentStatus curStatus = TalentStatus.RESULTS_AUDITING;
        final AsAdminTaskRule cycleTask = event.getCycleEval();
        final EvalUser taskUser = event.getTaskUser();
        if (!cycleTask.isResultAuditingOpen()) {//不需要结果审核(校准)跳过此阶段
            new ThisStageEnded(cycleTask, taskUser, curStatus).publish();
            return;
        }
        //先清除原有的数据，防止最后一个评分人重新评分后又重新生成
        tranWrap.runTran(() -> userRepo.delResultAudit(taskUser.getCompanyId().getId(), taskUser.getId()));
        ResultAuditFlowDmSvc flowDmSvc = new ResultAuditFlowDmSvc(taskUser.getCompanyId().getId(), taskUser.getTaskId(), taskUser.getId());
        flowDmSvc.setResultAuditFlowRepo(resultAuditFlowRepo);
        flowDmSvc.setRepo(userRepo);
        tranWrap.runTran(() -> flowDmSvc.start());
        if (flowDmSvc.isEnd()) {
            new ThisStageEnded(cycleTask, taskUser, curStatus).publish();
            return;
        }
        List<KpiEmp> reviewers = kpiEmpDao.listByEmp(taskUser.getCompanyId(), flowDmSvc.getRecEmpId());
        taskUser.curStatus(curStatus, reviewers);//负责人信息
        userRepo.updateTaskUser(taskUser);
        opLogDao.batchSaveLogDomain(flowDmSvc.getLogs());
        if (!taskUser.isAuditResultCollectSend()) {
            new MsgTodoAggregate(taskUser.getCompanyId(), taskUser.getTaskId(), cycleTask.taskName(),
                    taskUser.getEmpId(), taskUser.getId())
                    .useScene(MsgSceneEnum.TASK_RESULT_AUDIT, CompanyMsgActionEnum.RESULT_AUDIT)
                    .addExtTempValue("evalEmpName",taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                    .addExtTempValue("deadLineDate", taskUser.joinDeadLineStr(TalentStatus.RESULTS_AUDITING.getStatus()))
                    .addTodoItem("msg.task.emp", taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                    .sendExtTodo().sendExtMsg().addCenterMsg().addRecEmpId(flowDmSvc.getRecEmpId()).fire();
            return;
        }
        //统一校准
        ResultAuditDmSvc resultAuditDmSvc = new ResultAuditDmSvc(taskUser, flowDmSvc.getCurLevel(), taskUser.getCompanyId().getId(), taskUser.getTaskId());
        resultAuditDmSvc.setRepo(resultAuditFlowRepo);
        log.debug("handResultAuditingStageStart.flwDomSvc.getRecEmpId():{}", JSONUtil.toJsonStr(flowDmSvc.getRecEmpId()));
//        tranWrap.runTran(() -> auditFlowRepo.refreshSummary(taskUser.getCompanyId().getId(), taskUser.getTaskId()));
        for (String adminEmpId : flowDmSvc.getRecEmpId()) {
            tranWrap.runTran(() -> resultAuditDmSvc.executeStart(adminEmpId));
            resultAuditDmSvc.refreshSummaryFinishCnt(adminEmpId, flowDmSvc.getCurLevel());
        }
        if (resultAuditDmSvc.needSend()) {
            AdminTask adminTask = adminTaskRepo.getAdminTask(taskUser.getCompanyId(), taskUser.getTaskId());
            new ResultCollectMsgTodoEvent(taskUser.getCompanyId().getId(), resultAuditDmSvc.getSendRater(), adminTask).publish();
        }
    }

    // 结果面谈阶段启动 taskBiz.step4[4916]
    @Transactional
    public void handInterviewStageStart(InterviewStageStart event) {
        log.info(" ------面谈阶段开启-----，event：{}", JSONUtil.toJsonStr(event));
        event.start();
        final TalentStatus curStatus = TalentStatus.RESULTS_INTERVIEW;
        final AsAdminTaskRule cycleTask = event.getCycleEval();
        final EvalUser taskUser = event.getTaskUser();
        if (!cycleTask.isInterviewOpen()) {//不需要结果面谈跳过此阶段
            log.info(" ------面谈阶段未开启面谈-----");
            new ThisStageEnded(cycleTask, taskUser, curStatus).publish();
            return;
        }
        //解析被考核人是否符合面谈条件
        EvalTaskInterviewConf interview = taskUser.initEvalTaskInterview();
        if (!interview.isInterview(taskUser.getEvaluationLevel(), taskUser.getFinalScore())) {
            //不需要进行面谈的，此阶段直接结束
            log.info(" ------面谈阶段不符合面谈条件无需面谈-----，interview：{}", JSONUtil.toJsonStr(interview));
            new ThisStageEnded(cycleTask, taskUser, curStatus).publish();
            return;
        }
        //进入面谈阶段,解析面谈执行人员
        List<String> excuteors = interview.getInterviewExcuteEmpIds();
        //没有面谈人,自动跳过
        if (CollUtil.isEmpty(excuteors)) {
            log.info(" ------面谈阶段面谈人执行人，自动跳过-----");
            new ThisStageEnded(cycleTask, taskUser, curStatus).publish();
            return;
        }
        //被考核人面谈的面谈责任人信息
        List<KpiEmp> kpiEmps = kpiEmpDao.listByEmp(taskUser.getCompanyId(), excuteors);
        taskUser.curStatus(curStatus, kpiEmps);
        userRepo.updateTaskUser(taskUser);

        // 给执行人发通知与消息
        log.info(" ------面谈阶段给执行人发送待办通知-----taskUser.getEmpName()：{}，reason:{}", taskUser.getEmpName(), interview.getReason());
        new MsgTodoAggregate(taskUser.getCompanyId(), taskUser.getTaskId(), cycleTask.taskName(),
                taskUser.getEmpId(), taskUser.getId())
                .useScene(MsgSceneEnum.TASK_INTERVIEW_EXCUTE, CompanyMsgActionEnum.RESULT_INTERVIEW)
                .addExtTempValue("evalEmpName", taskUser.getEmpName())
                .addExtTempValue("reason", interview.getReason()) //修改全员面谈的文本展示
                .addExtTempValue("deadLineDate", taskUser.joinTitleDeadLineStr(TalentStatus.RESULTS_INTERVIEW.getStatus()))
                .addCenterMsg().sendExtTodo().sendExtMsg().addRecEmpId(excuteors).publish();
    }

    // 考核结果确认启动 taskBiz.step4[4902],[3465]
    @Transactional
    public void handResultsAffirmingStageStart(ResultsAffirmingStageStart event) {
        event.start();
        final TalentStatus curStatus = TalentStatus.RESULTS_AFFIRMING;
        final AsAdminTaskRule cycleTask = event.getCycleEval();
        final EvalUser taskUser = event.getTaskUser();
        if (!cycleTask.isResultAffirmOpen()) {//不需要结果确认跳过此阶段
            new ThisStageEnded(cycleTask, taskUser, curStatus).publish();
            return;
        }
        //被考核人确认
        List<KpiEmp> kpiEmps = kpiEmpDao.listByEmp(taskUser.getCompanyId(), Arrays.asList(taskUser.getEmpId()));
        //309和311行重复代码？
        taskUser.curStatus(curStatus, kpiEmps);//解析负责人taskBiz[3465]
        cycleTask.startAppealOn(curStatus, taskUser);
        taskUser.resultAffirmStarting(kpiEmps);
        userRepo.updateTaskUser(taskUser);
        // 给确认者发通知与消息
        // 发通知给被考核人
        new MsgTodoAggregate(cycleTask.companyId(), taskUser.getTaskId(), cycleTask.taskName(), taskUser.getEmpId(), taskUser.getId())
                .useScene(MsgSceneEnum.TASK_RESULT_AFFIRM, null)
                .addExtTempValue("evalEmpName", taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                .addExtTempValue("deadLineDate", taskUser.joinDeadLineStr(TalentStatus.RESULTS_AFFIRMING.getStatus()))
                .addTodoItem("msg.task.emp", taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                .sendExtTodo().sendExtMsg().addCenterMsg().addRecEmpId(taskUser.getEmpId()).publish();
        //doPushExtData(cycleTask, taskUser);
    }

    // 任务状态为待公示 taskBiz.4978
    @Transactional
    public void handWaitPublishedStageStart(WaitPublishedStageStart event) {
        final TalentStatus curStatus = TalentStatus.WAIT_PUBLISHED;
        final AsAdminTaskRule cycleTask = event.getCycleEval();
        final EvalUser taskUser = event.getTaskUser();

        //任务上关闭了结果确认后，会直接进入到下一阶段，需要关闭任务里面进行中的申诉
        log.info("任务上关闭了结果确认后，会直接进入到下一阶段，需要关闭任务里面进行中的申诉");
        taskAppealRepo.cancelAppealByTaskUserId(taskUser.getId());

        if (!cycleTask.isPublishResultOpen()) {
            new ThisStageEnded(cycleTask, taskUser, curStatus).publish();
            return;
        }
        if (!cycleTask.isManualPublicOpen()) {//手动公示才进入待公示阶段
            new PublishToEmpEvent(taskUser).publish();
            //new ThisStageEnded(cycleTask, taskUser, curStatus).publish();
            return;
        }
        List<String> pubEmpIds = cycleTask.parsePublicEmp();
        List<KpiEmp> pubEmps = kpiEmpDao.listByEmp(taskUser.getCompanyId(), pubEmpIds);
        taskUser.curStatus(curStatus, pubEmps);//解析负责人taskBiz[3465]
        userRepo.updateTaskUser(taskUser);
        //给公示人发内部待办,钉钉待办,钉钉通知
        new MsgTodoAggregate(taskUser.getCompanyId(), taskUser.getTaskId(), cycleTask.taskName(),
                taskUser.getEmpId(), taskUser.getId())
                .useScene(MsgSceneEnum.TASK_WAIT_PUBLIC, CompanyMsgActionEnum.TASK_WAIT_PUBLIC)
                .addExtTempValue("empName",taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                .addTodoItem("msg.task.emp", taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                .addCenterMsg().sendExtTodo().sendExtMsg().addRecEmpId(pubEmpIds).publish();
    }

    // 任务状态为已完成 taskBiz.4987
    @Transactional
    public BaseEvent handTalentEvalEnded(TalentEvalEnded event) {
        event.handleStart();
        final TalentStatus curStatus = TalentStatus.FINISHED;
        final AsAdminTaskRule cycleTask = event.getCycleEval();
        final EvalUser taskUser = event.getTaskUser();
//        List<KpiEmp> self = kpiEmpDao.listByEmp(taskUser.getCompanyId(), Arrays.asList(taskUser.getEmpId()));
        taskUser.curStatus(curStatus, new ArrayList<>());
        cycleTask.startAppealOn(curStatus, taskUser);
        userRepo.updateTaskUser(taskUser);
        //发送任务完成通知
//        new MsgTodoAggregate(taskUser.getCompanyId(), taskUser.getTaskId(), cycleTask.taskName(), taskUser.getEmpId(), taskUser.getId())
//                .useScene(MsgSceneEnum.TASK_FINISHED, CompanyMsgActionEnum.BLANK)
//                .sendExtMsg().addRecEmpId(taskUser.getEmpId()).publish();

        EmpEvalMerge merge = empEvalDao.getBaseEvalMerge(taskUser.getCompanyId(), taskUser.getId());
        //需处理面谈待办考核已完成还需处理的。 autoSkipHandlerType=1-继续进行流程（待办保留，内部流程继续）
        if (Objects.nonNull(merge)
                && Objects.nonNull(merge.getDeadLineConf())
                && Objects.nonNull(merge.getDeadLineConf().getTaskResultInterview())
                && merge.getDeadLineConf().getTaskResultInterview().getAutoSkip() == 1
                && merge.getDeadLineConf().getTaskResultInterview().getAutoSkipHandlerType() == 1) {
            List<CompanyMsgCenter> msgCenters = centerDao.listNotHandleMsgExcloudInterview(taskUser.getCompanyId(), taskUser.getId());
            if (CollUtil.isEmpty(msgCenters)) {
                return null;
            }
            List<String> ownerEmpIds = msgCenters.stream().map(CompanyMsgCenter::getEmpId).collect(Collectors.toList());
            List<String> todoScenes = msgCenters.stream().map(CompanyMsgCenter::getBusinessScene).collect(Collectors.toList());
            log.info("统一处理代办未关闭的问题 排除面谈");
            return new CancelTodoEvent(taskUser.getCompanyId(), taskUser.getId(), ownerEmpIds, todoScenes);
        }
        //统一处理代办未关闭的问题
        log.info("统一处理代办未关闭的问题");
        tx.runAsyn( MDC.get("tid"), () ->{
            znxcEvalResultPusher.evalResultPush(taskUser);
        });
        return new CancelTodoEvent(taskUser.getCompanyId(), new ArrayList<>(), taskUser.getId(), null);
    }

    public void doPushExtData(AsAdminTaskRule cycleTask, EvalUser taskUser) {
        CycleEval taskBase = taskUser.wasTempTask() ? taskRepo.getCycleEval(taskUser.getCompanyId(), new TaskId(taskUser.getTaskId())) : null;
        TalentEvalResult evalResult;
        Cycle cycle = taskUser.isNewEmp() ? taskRepo.adminTaskAsCycle(taskUser.getCompanyId(), taskUser.getTaskId())
                : cycleDao.find(taskUser.getCompanyId().getId(), taskUser.getCycleId());
        evalResult = new TalentEvalResult(
                taskUser.getCompanyId(),
                cycleTask.taskName().getName(),
                taskBase == null ? null : taskBase.getTemplBaseId(),
                cycle.getCycleStart(),
                cycle.getCycleEnd(),
                taskUser);
        try {
            cyclePushAcl.pushEvalResult(evalResult);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public void fixPushEvalResult(String companyId, String taskUserId) {
        List<String> ids = StrUtil.splitTrim(taskUserId, ",");
        for (String id : ids) {
            final TenantId tenantId = new TenantId(companyId);
            EvalUser user1 = userRepo.getBaseTaskUser(tenantId, id);
            CycleEval taskBase = taskRepo.getCycleEval(tenantId, new TaskId(user1.getTaskId()));
            cyclePushAcl.pushEvalResult(new TalentEvalResult(tenantId, taskBase.getTaskName().getName(),
                    taskBase.getTemplBaseId(), taskBase.getCycleStartDate(), taskBase.getCycleEndDate(), user1));
        }

    }

    //变更绩效结果会调用
    public void outputEvalResult(TenantId companyId, List<String> taskUserIds) {
        for (String taskUserId : taskUserIds) {
            CycleEval taskBase = taskRepo.getMergeCycleEval(companyId, taskUserId);
            EvalUser user = userRepo.getBaseTaskUser(companyId, taskUserId);
            tx.runAsyn( MDC.get("tid"), () ->{
                znxcEvalResultPusher.evalResultPush(user);
            });
            this.outputEvalResult(taskBase, user);
        }
    }

    public void outputEvalResult(CycleEval taskBase, EvalUser taskUser) {
        taskBase = taskBase == null ? taskRepo.getMergeCycleEval(taskUser.getCompanyId(), taskUser.getId()) : taskBase;
        log.info("通过连接器推送绩效到外部系统");
        //通过连接器推送绩效到外部系统
        IOutputConnector connector = connectorRepo.getConnector(taskUser.getCompanyId().getId());
        if (connector == null) {
            return;
        }
        try {
            Cycle cycle = taskUser.isNewEmp() ? taskRepo.adminTaskAsCycle(taskUser.getCompanyId(), taskUser.getTaskId())
                    : cycleDao.find(taskUser.getCompanyId().getId(), taskUser.getCycleId());
            String existExtId = connectorRepo.getOutExtEvalId(taskUser.getCompanyId().getId(), taskUser.getId());
            List<KpiEmp> self = kpiEmpDao.listByEmp(taskUser.getCompanyId(), Arrays.asList(taskUser.getEmpId()));
            if (StrUtil.isNotBlank(existExtId)) {
                connector.updateOutEvalResult(taskBase, self.get(0), taskUser, existExtId, cycle);
            } else {
                connector.createOutEvalResult(taskBase, self.get(0), taskUser, cycle);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            new ExceptionEvent(e).publish();
        }
    }

    public EvalUser handTalentEvalDeleted(TenantId companyId, String taskUserId) {
        ExtBatchEvalResult batchEval = new ExtBatchEvalResult(companyId);
        ExtTalentEvalResult result = new ExtTalentEvalResult();
        EvalUser user = userDao.getDelBaseEvalUser(companyId, taskUserId);
        EmpId evalEmpId = new EmpId(user.getEmpId());
        result.setEvalEmpId(evalEmpId);
        result.setId(taskUserId);
        batchEval.addEval(result);
        boolean evalResult = dataPushAcl.deleteEvalResult(batchEval);
        log.info("删除推送钉钉 {}", evalResult);
        return user;
    }


    @Transactional
    @Deprecated
    public void resetScoreEmp(ResetScoreEmpCmd cmd) {
        TenantId tenantId = new TenantId(cmd.getCompanyId());
        EvalUser taskUser = userRepo.getTaskUser(tenantId, cmd.getTaskUserId());
        EmpEvalMerge evalMerge = ruleRepo.getEmpEvalMerge(tenantId, cmd.getTaskUserId(), EmpEvalMerge.all);
        Set<String> clearScoreRsScenes = new HashSet<>();

        for (ScoreEmp scoreEmp : cmd.getScoreEmps()) {
            EvalScorerNode evalScorerNode = new EvalScorerNode();
            evalScorerNode.buildEvalScorerNode(tenantId, cmd.getOpEmpId(), cmd.getTaskUserId(), scoreEmp.getEmpId(), scoreEmp.getEmpName(),
                    scoreEmp.getScorerType(), scoreEmp.getApprovalOrder(), 0);
            evalScorerNodeRepo.saveEvalScorerNode(evalScorerNode);
        }

        //将result状态置位null
        ChainDispatchRs resetScoreRs = evalMerge.resetScoreEmp(cmd.getScoreEmps());
        if (resetScoreRs.hasNoneRs()) {
            return;
        }
        userRepo.updateAuditStatus(tenantId, cmd.getTaskUserId(), resetScoreRs);
        empEvalScorerRepo.saveDispatchNode(resetScoreRs.getEmpEvalScorers());//save Dispatch scorers 及环节
        //重新打开总评
        List<BaseScoreResult> totalLevelRs = empEvalDao.listAllTotalLevelRs(tenantId.getId(), cmd.getTaskUserId());
        userRepo.clearTotalLevelScoreResult(tenantId, cmd.getTaskUserId());
        List<String> totalLevelScorerIds = totalLevelRs.stream().map(baseScoreResult -> baseScoreResult.getScorerId()).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(totalLevelScorerIds)) {
            clearScoreRsScenes.add(CompanyMsgActionEnum.CUSTOM_SCORE.getMsgType());
        }
        List<String> scorerIds = cmd.getScoreEmps().stream().map(ScoreEmp::getEmpId).collect(Collectors.toList());
        // 更新负责人信息
        List<KpiEmp> raters = kpiEmpDao.listByEmp(taskUser.getCompanyId(), scorerIds);
        //如果是处于评分阶段重置，则把重置的人员列表合并加入当前责任人中
        if (taskUser.isScoreStage()) {
            List<KpiEmp> reviews = taskUser.getReviewersJson();
            List<KpiEmp> mergeReviews = CollectionUtils.union(reviews, raters).stream().distinct().collect(Collectors.toList());
            taskUser.reviewers(mergeReviews);
        } else {
            //不是评分阶段重置，责任人直接替换为重置的人员
            taskUser.reviewers(raters);
        }
        //若是从校准环节重置，清除校准环节的待办.
        List<String> statusList = Arrays.asList(TalentStatus.RESULTS_AUDITING.getStatus(), TalentStatus.RESULTS_INTERVIEW.getStatus(), TalentStatus.RESULTS_AFFIRMING.getStatus(), TalentStatus.RESULTS_APPEAL.getStatus());
        if (statusList.contains(taskUser.getTaskStatus())) {
            new CancelTodoEvent(tenantId, cmd.getTaskUserId(),
                    Arrays.asList(MsgSceneEnum.TASK_RESULT_AUDIT.getType(),
                            MsgSceneEnum.TASK_INTERVIEW_EXCUTE.getType(),
                            MsgSceneEnum.TASK_INTERVIEW_INPUT.getType(),
                            MsgSceneEnum.TASK_INTERVIEW_TRANSFER_INPUT.getType(),
                            MsgSceneEnum.TASK_INTERVIEW_CONFIRM.getType(),
                            MsgSceneEnum.TASK_RESULT_APPEAL.getType(),
                            MsgSceneEnum.TASK_RESULT_AFFIRM.getType())).publish();
        }
        //将校准审核汇总发送通知状态回滚
        if (taskUser.isAuditResultCollectSend()) {
            ResultAuditFlow flow = new ResultAuditFlow(tenantId.getId(), cmd.getOpEmpId());
            auditFlowRepo.saveAuditFlow(flow, taskUser);
        }
        //是否开启面谈环节
        if (Objects.nonNull(evalMerge.getInterviewConf()) && evalMerge.getInterviewConf().isOpen()) {
            interviewRepo.clearInterview(tenantId, cmd.getTaskUserId());//清除面谈环节数据
        }
        //如果当前节点是finished，重置需要更新任务数量
        if (TalentStatus.FINISHED.getStatus().equals(taskUser.getTaskStatus())) {
            new EmpCntOfAdminTaskChanged(tenantId.getId(), taskUser.getTaskId()).publish();
        }
        //将任务状态重置为评分中
        taskUser.enterScoring();
        taskUser.setHasAppeal(Boolean.FALSE.toString());
        //清除申述记录
        taskAppealRepo.clearAppeal(tenantId.getId(), cmd.getTaskUserId());
        //清除申诉次数
        evalMerge.getAppealConf().cleanAppealTimes();
        ruleRepo.updateEmpEvalRule(evalMerge);

        userRepo.updateTaskUser(taskUser);

        String businessScene = cmd.isRejectToScore() ? OperationLogSceneEnum.REJECT_TO_SCORE_STAGE.getScene() : OperationLogSceneEnum.RESET_SCORE_EMP.getScene();
        OperationLogDo logDo = new OperationLogDo(tenantId.getId(), cmd.getTaskUserId(), businessScene, cmd.getOpEmpId(), new Date());
        JSONObject logDesc = new JSONObject();
        logDesc.put("resetReason", cmd.getResetReason());
        logDesc.put("scoreEmps", JSONObject.toJSONString(cmd.getScoreEmps()));
        logDo.setDescription(logDesc.toJSONString());
        opLogDao.addLog(logDo);
        //没有需要清除的待办场景，则不执行清除待办事件
        if (CollUtil.isNotEmpty(clearScoreRsScenes)) {
            new CancelTodoEvent(tenantId, cmd.getTaskUserId(), clearScoreRsScenes).publish();
        }

        taskUser.setTaskName(evalMerge.getTaskName());
        ScorerTodoDmSvc todoDmSvc = new ScorerTodoDmSvc(evalMerge, taskUser);
        //汇总评分判断
        if (!todoDmSvc.support()) {
            //重新开启对应待办
            cmd.getScoreEmps().stream().forEach(scoreEmp -> {
                MsgSceneEnum toScene = SubScoreNodeEnum.todoScene(evalMerge.isCustom() ? SubScoreNodeEnum.CUSTOM.getScene() : scoreEmp.getScorerType());
                msgCenterRepo.reOpenTodoMsg(tenantId, cmd.getTaskUserId(), scoreEmp.getEmpId(), toScene.getType());
                //发送通知
                new MsgTodoAggregate(tenantId, taskUser.getTaskId(), evalMerge.taskName(), taskUser.getEmpId(), taskUser.getId())
                        .useScene(cmd.isRejectToScore() ? MsgSceneEnum.REJECT_TO_SCORE_STAGE : toScene)
                        .addExtTempValue("empName",taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                        .addExtTempValue("reason", cmd.getResetReason())
                        .sendExtMsg().addRecEmpId(scoreEmp.getEmpId()).publish();
            });
        } else {
            //所有被重置的评分人，需要重置评价状态为未评分，但是还在就绪状态,同时重新对涉及到的评分人进行汇总
            Set<String> scorerIdsSet = new HashSet<>(scorerIds);
            ScoreEmp  selfScore = cmd.selfScore();
            if (Objects.nonNull(selfScore)) {
                resetScoreReSendMsg(tenantId,taskUser,evalMerge,cmd,selfScore);
            }
            scorerSummaryTodoRepo.resetScorerScoreStatus(taskUser.getCompanyId().getId(), taskUser.getId(), scorerIdsSet);
            todoDmSvc.setRepo(scorerSummaryTodoRepo);
            todoDmSvc.refreshSummary(scorerIdsSet);
            List<ScorerTodoSummary> updateSummaries = todoDmSvc.getUpdateSummaries();
            List<ScorerTodoSummary> addSummaries = todoDmSvc.getAddSummaries();
            scorerSummaryTodoRepo.batchAddSummaries(addSummaries);
            scorerSummaryTodoRepo.batchUpdateSummaries(updateSummaries);
            if (!updateSummaries.isEmpty()) {
                for (ScorerTodoSummary updateSummary : updateSummaries) {
                    if (updateSummary.readyToSend()) {
                        List<TaskUserScorer> scorers = scorerSummaryTodoRepo.queryNeedSendSystemTodoScorers(updateSummary.getCompanyId(), updateSummary.getTaskId(), updateSummary.getScorerId());
                        new ScoreSummaryMsgTodoEvent(updateSummary.getCompanyId(), scorers, updateSummary).publish();
                    }
                }
            }
            //转交或者重置的可能存在新的汇总对象需要立马发一遍待办的情况
            if (!addSummaries.isEmpty()) {
                for (ScorerTodoSummary addSummary : addSummaries) {
                    if (addSummary.readyToSend()) {
                        List<TaskUserScorer> scorers = scorerSummaryTodoRepo.queryNeedSendSystemTodoScorers(addSummary.getCompanyId(), addSummary.getTaskId(), addSummary.getScorerId());
                        new ScoreSummaryMsgTodoEvent(addSummary.getCompanyId(), scorers, addSummary).publish();
                    }
                }
            }
        }
    }

    public void resetScoreReSendMsg(TenantId tenantId,EvalUser taskUser,EmpEvalMerge evalMerge,ResetScoreEmpCmd cmd,ScoreEmp scoreEmp) {
        MsgSceneEnum toScene = SubScoreNodeEnum.todoScene(evalMerge.isCustom() ? SubScoreNodeEnum.CUSTOM.getScene() : scoreEmp.getScorerType());
        msgCenterRepo.reOpenTodoMsg(tenantId, taskUser.getId(), scoreEmp.getEmpId(), toScene.getType());
        //发送通知
        new MsgTodoAggregate(tenantId, taskUser.getTaskId(), evalMerge.taskName(), taskUser.getEmpId(), taskUser.getId())
                .useScene(cmd.isRejectToScore() ? MsgSceneEnum.REJECT_TO_SCORE_STAGE : toScene)
                .addExtTempValue("empName",taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                .addExtTempValue("reason", cmd.getResetReason())
                .sendExtMsg().addRecEmpId(scoreEmp.getEmpId()).publish();
    }

    public void resetScoreEmpV3(ResetScoreEmpCmd cmd) {
        //不支持1.0版本的评分重置提交
        TenantId tenantId = new TenantId(cmd.getCompanyId());
        EvalResetScoreEmp evalResetScoreEmp = onScoreEvalRepo.getResetScoreEmpEval(tenantId, cmd.getTaskUserId(), cmd.getScoreEmps());
        if (Objects.isNull(evalResetScoreEmp)) {
            log.info("考核任务不存在或没有重置的人！！！");
            return;
        }
        ResetScoreEmpDmSvc dmSvc = new ResetScoreEmpDmSvc(tenantId, cmd.getOpEmpId(), evalResetScoreEmp, cmd.getScoreEmps(), cmd.getResetReason(), cmd.isRejectToScore());
        dmSvc.handResetEmp();//重置评分人
        if (dmSvc.getChainDispatchRs().hasNoneScorerRs()) {
            return;
        }
        //重置的核心数据
        tx.runTran(() -> {
            this.onScoreEvalRepo.saveResetScoreEmp(dmSvc.getTaskUser(), cmd.getOpEmpId(), dmSvc.getResetScoreEmp());//save 重置数据
            if (Objects.nonNull(dmSvc.getResetScoreEmp().getAuditFlow())) {  //校准
                resultAuditFlowRepo.saveAuditFlow(dmSvc.getResetScoreEmp().getAuditFlow(), dmSvc.getTaskUser());
            }
        });
        //1.异步执行 1.清除待办 2.异步执行事件//统计任务数量 //3.发送重置待办通知
        String tid = MDC.get("tid");
        tx.runAsyn(tid, () -> {
            if (CollUtil.isNotEmpty(dmSvc.getResetScoreEmp().getClearScoreRsScenes())) {
                new ClearTodoDmsSvc(centerRepo).clear(tenantId, cmd.getTaskUserId(), dmSvc.getResetScoreEmp().getClearScoreRsScenes()); //1.清除待办
            }
            dmSvc.publishEvent();//2.异步执行事件//统计任务数量
            ScorerTodoDmSvc todoDmSvc = new ScorerTodoDmSvc(dmSvc.getEmpEval(), dmSvc.getTaskUser());//3.发送重置待办通知
            todoDmSvc.sendScoreMsg(tenantId, cmd.getScoreEmps(), cmd.getResetReason(), cmd.isRejectToScore(), centerRepo, scorerSummaryTodoRepo, scoreMsgAcl);
        });
    }


    // 任务重置结点
    public void handResetStage(TenantId companyId, String taskUserId, String opEmpId, String resetNode, String operationType, String resetReason) {
        EvalUser taskUser = userRepo.getEmpEval(companyId, taskUserId);

        //检查当前考核任务重置的环节是否存在
        if (taskUser.notExistResetNode(resetNode)
                && OperationTypeEnum.RESET_NODE.getValue().equals(operationType)) {
            return;
        }
        log.debug("tranWrap:{}", this.tranWrap);
        ResetTaskDmSvc taskDmSvc = new ResetTaskDmSvc(companyId, opEmpId, resetNode, operationType, resetReason, taskUser);
        log.debug("taskDmSvc:{}", JSONUtil.toJsonStr(taskDmSvc));
        EmpEvalMerge evalMerge = empRuleRepo.getEmpEvalMerge(companyId, taskUserId, EmpEvalMerge.all);
        tranWrap.runTran(() ->{
            taskDmSvc.handResetStage(kpiRepo, userRepo, askEvalAcl, interviewRepo, empEvalScorerRepo, evalMerge);
        });  //执行重置
        //重置操作日志
        opLogDao.addLog(new ToDataBuilder<>(taskDmSvc.getLog(), OperationLogDo.class).data());
        new ClearTodoDmsSvc(msgCenterRepo).clear(companyId, taskUserId, taskDmSvc.getClearScoreRsScenes());
//        new CancelTodoEvent(companyId, taskUserId, taskDmSvc.getClearScoreRsScenes()).fire();
        TalentStatus current = TalentStatus.statusOf(taskUser.getTaskStatus());
        TalentStatus toStage = TalentStatus.statusOf(resetNode);
        //如果当前节点是finished，重置需要更新任务数量
        if (TalentStatus.FINISHED.getStatus().equals(taskUser.getTaskStatus())) {
            tranWrap.runTran(() -> adminTaskRepo.upFinishCnt(companyId.getId(), taskUser.getTaskId()));//修改完成数量
        }
        if (toStage.getOrder() == TalentStatus.FINISH_VALUE_AUDIT.getOrder() && TalentStatus.FINISH_VALUE_AUDIT.beforeEq(current)) {
            new FinishedValueAuditEvent(companyId, taskUserId, taskUser, new EmpId(opEmpId), true).fire();
            return;
        }
        AsAdminTaskRule cycleEval = taskUser.wasTempTask() ? taskRepo.getMergeCycleEval(companyId, taskUserId)
                : empRuleRepo.getEmpEvalMerge(companyId, taskUserId, EmpEvalMerge.beforeScore);

        taskUser.setTaskName(evalMerge.getTaskName());
        ScorerTodoDmSvc todoDmSvc = new ScorerTodoDmSvc(evalMerge, taskUser);
        if (todoDmSvc.support()) {
            //如果重置到评分环节，所有评价状态改为待评分
            if (TalentStatus.SCORING.getStatus().equals(toStage.getStatus())) {
                scorerSummaryTodoRepo.resetScorerScoreStatusByTaskUser(taskUser);
            }
            //如果重置到执行中，所有评价状态改为待评分，就绪状态改为未就绪，等待重新派发
            if (toStage.before(TalentStatus.SCORING)) {
                scorerSummaryTodoRepo.resetScorerStatusByTaskUser(taskUser);
            }
            List<String> scorerIds = scorerSummaryTodoRepo.queryScorerIds(taskUser);
            todoDmSvc.setRepo(scorerSummaryTodoRepo);
            todoDmSvc.refreshSummary(scorerIds);
            List<ScorerTodoSummary> updateSummaries = todoDmSvc.getUpdateSummaries();
            scorerSummaryTodoRepo.batchUpdateSummaries(updateSummaries);
            List<ScorerTodoSummary> addSummaries = todoDmSvc.getAddSummaries();
            List<ScorerTodoSummary> batchUpdate = new ArrayList<>();
            if (!updateSummaries.isEmpty()) {
                List<ScorerTodoSummary> tempUpdateList = new ArrayList<>();
                for (ScorerTodoSummary updateSummary : updateSummaries) {
                    if (updateSummary.readyToSend()) {
                        List<TaskUserScorer> scorers = scorerSummaryTodoRepo.queryNeedSendSystemTodoScorers(updateSummary.getCompanyId(), updateSummary.getTaskId(), updateSummary.getScorerId());
                        new ScoreSummaryMsgTodoEvent(updateSummary.getCompanyId(), scorers, updateSummary).fire();
                    }
                    if (updateSummary.readyToClose()) {
                        FinishWorkReq req = new FinishWorkReq(taskUser.getCompanyId(), new EmpId(updateSummary.getScorerId()), updateSummary.getThirdMsgId());
                        boolean isSuccess = false;
                        try {
                            msgAcl.finishTodoWork(req);
                            isSuccess = true;
                        } catch (Exception e) {
                            log.error("完成待办失败:" + e.getMessage(), e);
                        }
                        if (isSuccess) {
                            updateSummary.setTodoStatus(2);
                            updateSummary.setUpdatedTime(new Date());
                            tempUpdateList.add(updateSummary);
                        }
                    }
                }
                batchUpdate.addAll(tempUpdateList);
            }
            scorerSummaryTodoRepo.batchResetSummaries(batchUpdate);
            //转交或者重置的可能存在新的汇总对象需要立马发一遍待办的情况
            if (!addSummaries.isEmpty()) {
                scorerSummaryTodoRepo.batchAddSummaries(addSummaries);
                for (ScorerTodoSummary addSummary : addSummaries) {
                    if (addSummary.readyToSend()) {
                        List<TaskUserScorer> scorers = scorerSummaryTodoRepo.queryNeedSendSystemTodoScorers(addSummary.getCompanyId(), addSummary.getTaskId(), addSummary.getScorerId());
                        new ScoreSummaryMsgTodoEvent(addSummary.getCompanyId(), scorers, addSummary).fire();
                    }
                }
            }
        }

        ThisStageEnded thisStageEnded = new ThisStageEnded(cycleEval, taskUser, toStage.preStage(), opEmpId);
        thisStageEnded.setReset(true);
        thisStageEnded.fire();
    }

    public void rejectScore(RejectScoreCmd cmd) {

        TenantId companyId = new TenantId(cmd.getCompanyId());
        EvalUser evalUser = userRepo.getTaskUser(companyId, cmd.getTaskUserId());
        EmpEvalMerge evalMerge = empRuleRepo.getEmpEvalMerge(companyId, cmd.getTaskUserId(), EmpEvalMerge.all);
        // 更新负责人信息
        ScoreEmp scoreEmp = new ScoreEmp(evalUser.getEmpId(), evalUser.getEmpName(), "self_score", 1);
        List<String> scorerIds = Collections.singletonList(scoreEmp.getEmpId());
        List<KpiEmp> raters = kpiEmpDao.listByEmp(companyId, scorerIds);
        List<ScoreEmp> scoreEmps = Collections.singletonList(scoreEmp);

        //领域服务执行领域属性变更
        RejectScoreDmSvc rejectScoreDmSvc = new RejectScoreDmSvc(evalUser, evalMerge, raters, scoreEmps ,cmd.getOpEmpId(),cmd.getFromScoreType());
        rejectScoreDmSvc.rejectScore();
        //汇总评分？ 依次评才清待办，而现有的汇总评分只支持同时评，同时评不会因为驳回变更待办状态所以不影响汇总

        //准备驳回记录
        ScoreReject scoreReject = new ScoreReject(cmd.getCompanyId(),evalUser.getCycleId(),evalUser.getId(),evalUser.getTaskId(),
                scoreEmp.getScorerType(),scoreEmp.getEmpId(),scoreEmp.getApprovalOrder(),cmd.getRejectReason(),cmd.getOpEmpId());

        //准备操作记录
        String businessScene = OperationLogSceneEnum.REJECT_TO_SCORE_STAGE.getScene();
        OperationLogDo logDo = new OperationLogDo(cmd.getCompanyId(), cmd.getTaskUserId(), businessScene, cmd.getOpEmpId(), new Date());
        JSONObject logDesc = new JSONObject();
        logDesc.put("resetReason", cmd.getRejectReason());
        logDesc.put("scoreEmps", JSONObject.toJSONString(scoreEmps));
        logDesc.put("scoreType", cmd.getFromScoreType());
        logDo.setDescription(logDesc.toJSONString());

        //统一操作事务
        tx.runTran(() -> {
            //更新scoreResult //驳回状态
            userRepo.updateAuditStatusToRejected(companyId, cmd.getTaskUserId(), rejectScoreDmSvc.getResetScoreRs());
            //更新taskUser和驳回人的派发状态
            userRepo.updateRejectScoreRs(rejectScoreDmSvc.getEvalUser() ,scoreReject);
            //清驳回人的待办
            if (rejectScoreDmSvc.getClearTodo()){
                //更新scoreResult //上级评scoreResult到待派发
                userRepo.updateAuditStatusToWait(companyId, cmd.getTaskUserId(), rejectScoreDmSvc.getWaitScoreRs());
                MsgSceneEnum toScene = SubScoreNodeEnum.todoScene(evalMerge.isCustom() ? SubScoreNodeEnum.CUSTOM.getScene() : cmd.getFromScoreType());
                new ClearTodoDmsSvc(msgCenterRepo).clear(companyId, cmd.getTaskUserId(), CollUtil.toList(toScene.getType()), cmd.getOpEmpId());
            }
            //记录操作日志
            opLogDao.addLog(logDo);
        });
        //通过事件重新发送被驳回自评的待办
        new ScoreRejectedEvent(cmd.getCompanyId(),evalUser,evalMerge,scoreEmp.getScorerType(),scoreEmp.getEmpId() ,cmd.getRejectReason()).fire();
    }

    public void rejectScoreV3(RejectScoreCmd cmd) {
        TenantId companyId = new TenantId(cmd.getCompanyId());
        EvalUser evalUser = userRepo.getTaskUser(companyId, cmd.getTaskUserId());
        EmpEvalMerge evalMerge = empRuleRepo.getEmpEvalMerge(companyId, cmd.getTaskUserId(), EmpEvalMerge.all);
        // 更新负责人信息
        ScoreEmp scoreEmp = new ScoreEmp(evalUser.getEmpId(), evalUser.getEmpName(), "self_score", 1);
        List<String> scorerIds = Collections.singletonList(scoreEmp.getEmpId());
        List<KpiEmp> raters = kpiEmpDao.listByEmp(companyId, scorerIds);
        List<ScoreEmp> scoreEmps = Collections.singletonList(scoreEmp);

        //领域服务执行领域属性变更
        RejectScoreDmSvc rejectScoreDmSvc = new RejectScoreDmSvc(evalUser, evalMerge, raters, scoreEmps ,cmd.getOpEmpId(),cmd.getFromScoreType());
        rejectScoreDmSvc.rejectScoreV3(cmd.getRejectReason());
        if (rejectScoreDmSvc.getResetScoreRs().hasNoneRsV3()){
            log.warn(" rejectScoreV3 ,无驳回 的评分人");
            return;
        }
        //汇总评分？ 依次评才清待办，而现有的汇总评分只支持同时评，同时评不会因为驳回变更待办状态所以不影响汇总
        //准备驳回记录
        ScoreReject scoreReject = new ScoreReject(cmd.getCompanyId(),evalUser.getCycleId(),evalUser.getId(),evalUser.getTaskId(),
                scoreEmp.getScorerType(),scoreEmp.getEmpId(),scoreEmp.getApprovalOrder(),cmd.getRejectReason(),cmd.getOpEmpId());
        //统一操作事务
        tx.runTran(() -> {
            //更新scoreResult //驳回状态 + 更新taskUser和驳回人的派发状态
            onScoreEvalRepo.saveRejectScoreEmp(rejectScoreDmSvc.getEvalUser(),scoreReject,rejectScoreDmSvc.needUpScorers(),rejectScoreDmSvc.getOpLog());
            //清驳回人的待办
            if (rejectScoreDmSvc.getClearTodo()){
                //更新scoreResult //上级评scoreResult到待派发
                MsgSceneEnum toScene = SubScoreNodeEnum.todoScene(evalMerge.isCustom() ? SubScoreNodeEnum.CUSTOM.getScene() : cmd.getFromScoreType());
                new ClearTodoDmsSvc(msgCenterRepo).clear(companyId, cmd.getTaskUserId(), CollUtil.toList(toScene.getType()), cmd.getOpEmpId());
            }
        });
        //通过事件重新发送被驳回自评的待办
        new ScoreRejectedEvent(cmd.getCompanyId(),evalUser,evalMerge,scoreEmp.getScorerType(),scoreEmp.getEmpId() ,cmd.getRejectReason()).fire();
    }


    //记录评分阶段完成时间
    @Transactional
    public void recordScoringEnd(TenantId companyId, String taskUserId) {
        userRepo.recordScoringEnd(companyId, taskUserId);
    }


    public List<RaterNode> getIndexRaters(TenantId companyId, String taskUserId) {
        EmpEvalMerge empEvalMerge = empRuleRepo.getEmpEvalMerge(companyId, taskUserId, EmpEvalMerge.all);
        empEvalMerge.initIndexRaters();
        return empEvalMerge.getIndexRaters();
    }

    @Transactional
    public void initIndexRaters(TenantId companyId, List<String> taskUserIds) {
        for (String taskUserId : taskUserIds) {
            EmpEvalMerge empEvalMerge = empRuleRepo.getEmpEvalMerge(companyId, taskUserId, EmpEvalMerge.all);
            empEvalMerge.initIndexRaters();
            empRuleRepo.updateIndexRaters(empEvalMerge);
        }
    }

    @Transactional
    public void fixResultAuditReviewers(TenantId companyId, String taskUserId, List<FlowRater> ingNodes) {
        EvalUser eval = userRepo.getBaseTaskUser(companyId, taskUserId);
        List<String> empIds = CollUtil.map(ingNodes, flowRater -> flowRater.getEmpId(), true);
        List<KpiEmp> reviewers = kpiEmpDao.listByEmp(companyId, empIds);
        eval.reviewers(reviewers);
        userRepo.updateTaskUser(eval);
    }

    public void fixSendTodoToRater(String companyId, String taskUserIds) {
        List<String> userIds = StrUtil.splitTrim(taskUserIds, ",");

        for (String userId : userIds) {
            List<PerfEvalTypeResult> typeResults = new ArrayList<>();
            ListWrap<EvalScoreResult> itemResults = new ListWrap<>();

            EvalUser eval = userRepo.getBaseTaskUser(new TenantId(companyId), userId);
            EmpEvalMerge merge = empRuleRepo.getEmpEvalMerge(new TenantId(companyId), userId, EmpEvalMerge.all);
            ChainDispatchRs dispatchRs = new ChainDispatchRs(typeResults, itemResults);
            for (EmpEvalKpiType type : merge.getKpiTypes().getDatas()) {
                typeResults.addAll(type.getWaitScoresOld());
                for (EvalKpi item : type.getItems()) {
                    itemResults.addAll(item.getWaitScoresOld());
                }
            }

            //  发送通知与待办
            dispatchRs.distinctMsger().forEach((scoreType, reveiverEmpIds) -> {
                if (CollUtil.isNotEmpty(reveiverEmpIds)) {
                    MsgSceneEnum toScene = SubScoreNodeEnum.todoScene(merge.isCustom() ? SubScoreNodeEnum.CUSTOM.getScene() : scoreType);
                    new MsgTodoAggregate(eval.getCompanyId(), eval.getTaskId(), merge.taskName(), eval.getEmpId(), eval.getId())
                            .useScene(toScene).addRecEmpId(reveiverEmpIds).sendExtMsg()
                            .sendExtTodo().fire();
                }
            });
        }

    }

    public void handleScoreRejectedEvent(ScoreRejectedEvent event) {

        EmpEvalMerge evalMerge = event.getEvalMerge();
        EvalUser taskUser = event.getEvalUser();

        MsgSceneEnum toScene = SubScoreNodeEnum.todoScene(evalMerge.isCustom() ? SubScoreNodeEnum.CUSTOM.getScene() : event.getScoreType());
        CompanyMsgCenter center = msgCenterRepo.reOpenTodoMsgAndAddAttContent(new TenantId(event.getTenantId()), taskUser.getId(), event.getScoreEmpId(), toScene.getType() , "rejectReason", event.getRejectReason());
        //发送通知
        new MsgTodoAggregate(new TenantId(event.getTenantId()), taskUser.getTaskId(), evalMerge.taskName(), taskUser.getEmpId(), taskUser.getId(), center)
                .useScene(MsgSceneEnum.REJECT_TO_SCORE_STAGE, CompanyMsgActionEnum.SELF_SCORE)
                .addExtTempValue("empName",taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                .addExtTempValue("reason", event.getRejectReason().length() > 40 ? event.getRejectReason().substring(0, 40) + "..." : event.getRejectReason())
                .addTodoItem("msg.task.emp", taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                .sendExtTodo().sendExtMsg().addRecEmpId(event.getScoreEmpId()).publish();
    }
}
