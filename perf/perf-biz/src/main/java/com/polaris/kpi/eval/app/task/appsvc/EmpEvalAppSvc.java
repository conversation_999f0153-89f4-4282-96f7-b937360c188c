package com.polaris.kpi.eval.app.task.appsvc;

import cn.com.polaris.kpi.*;
import cn.com.polaris.kpi.company.*;
import cn.com.polaris.kpi.eval.*;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.perf.www.common.constant.BusinessConstant;
import com.perf.www.common.em.CompanyMsgActionEnum;
import com.perf.www.common.em.ExcelTemplateEnum;
import com.perf.www.common.em.OperationLogSceneEnum;
import com.perf.www.common.em.ScoreTypeEnum;
import com.perf.www.common.excel.ExcelUtil;
import com.perf.www.common.utils.date.DateTimeUtils;
import com.perf.www.common.utils.file.FileUtil;
import com.perf.www.common.utils.string.StringTool;
import com.perf.www.cons.AdminType;
import com.perf.www.cons.TaskProgressStatus;
import com.perf.www.dto.PerfEvaluateTaskUserDTO;
import com.perf.www.vo.okr.OKRGetItemInfoReturnVO;
import com.perf.www.vo.okr.OKRGetTaskReturnVO;
import com.perf.www.vo.okr.OKRItemDetailVO;
import com.perf.www.vo.okr.OKRTaskQueryVO;
import com.perf.www.vo.report.ReportSeriesDataVO;
import com.polaris.acl.dept.domain.org.EmpOrganization;
import com.polaris.acl.dept.pojo.CompanyDo;
import com.polaris.acl.dept.pojo.CompanyPo;
import com.polaris.acl.dept.repository.DeptEmpDao;
import com.polaris.acl.kpi.eval.domain.EvalEmp;
import com.polaris.acl.msg.domain.FinishWorkReq;
import com.polaris.acl.msg.face.MsgAcl;
import com.polaris.kpi.ExecutorEnum;
import com.polaris.kpi.ask.domain.acl.AskEvalAcl;
import com.polaris.kpi.cache.domain.repo.CompanyCacheRepo;
import com.polaris.kpi.common.KpiI18NException;
import com.polaris.kpi.eval.app.TransactionWrap;
import com.polaris.kpi.eval.app.task.dto.*;
import com.polaris.kpi.eval.app.task.dto.admin.SkipRaterCmd;
import com.polaris.kpi.eval.app.task.dto.calibrated.CalibratedCmd;
import com.polaris.kpi.eval.app.task.dto.eval.*;
import com.polaris.kpi.eval.app.task.dto.evaltable.ModifyOrgTableOwnerCmd;
import com.polaris.kpi.eval.app.task.dto.sumit.SubmitScoreCmd;
import com.polaris.kpi.eval.app.task.dto.sumit.SubmitScoreV3Cmd;
import com.polaris.kpi.eval.domain.TaskEvalContext;
import com.polaris.kpi.eval.domain.confirm.dmsvc.DirectConfirmItemDmSvc;
import com.polaris.kpi.eval.domain.confirm.entity.ConfirmNode;
import com.polaris.kpi.eval.domain.confirm.entity.ConfirmTaskFlow;
import com.polaris.kpi.eval.domain.confirm.repo.ConfirmFlowRepo;
import com.polaris.kpi.eval.domain.cycle.dmsvc.ScoreRuleSnapMatchDmSvc;
import com.polaris.kpi.eval.domain.stage.dmsvc.EvalRuleStatusCheckDmsvc;
import com.polaris.kpi.eval.domain.task.acl.*;
import com.polaris.kpi.eval.domain.task.dmsvc.*;
import com.polaris.kpi.eval.domain.task.dmsvc.score.ExplainBatchEvalScorerDmSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.score.SkipScorerDmSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.score.TransferScorerV3DmSvc;
import com.polaris.kpi.eval.domain.task.entity.EvalItem;
import com.polaris.kpi.eval.domain.task.entity.EvalItemField;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.admineval.*;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTaskDiff;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTaskStatusCnt;
import com.polaris.kpi.eval.domain.task.entity.admineval.IndicatorLogCompare;
import com.polaris.kpi.eval.domain.task.entity.calibrated.ResultAudit;
import com.polaris.kpi.eval.domain.task.entity.empeval.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.chain.ScoreSceneWrap;
import com.polaris.kpi.eval.domain.task.entity.empeval.invite.InviteItem;
import com.polaris.kpi.eval.domain.task.entity.flow.*;
import com.polaris.kpi.eval.domain.task.entity.grade.IndLevelGroup;
import com.polaris.kpi.eval.domain.task.entity.interview.EvalTaskInterviewConf;
import com.polaris.kpi.eval.domain.task.entity.interview.EvalTaskInterviewConfirm;
import com.polaris.kpi.eval.domain.task.entity.js.JSElComputer;
import com.polaris.kpi.eval.domain.task.entity.msg.MsgTodoAggregate;
import com.polaris.kpi.eval.domain.task.entity.msg.UrgingSendMsgCommon;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrAction;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrActionUpdate;
import com.polaris.kpi.eval.domain.task.event.FinishedValueAuditEvent;
import com.polaris.kpi.eval.domain.task.event.KpiItemUpdateFinishedValueEvent;
import com.polaris.kpi.eval.domain.task.event.SendTodoForInputOnScoreEvent;
import com.polaris.kpi.eval.domain.task.event.ThisStageEnded;
import com.polaris.kpi.eval.domain.task.event.admineval.*;
import com.polaris.kpi.eval.domain.task.event.msg.CancelRemoteTodoEvent;
import com.polaris.kpi.eval.domain.task.event.msg.CancelTodoEvent;
import com.polaris.kpi.eval.domain.task.event.talent.ResultCollectMsgTodoEvent;
import com.polaris.kpi.eval.domain.task.event.talent.ScoreSummaryMsgTodoEvent;
import com.polaris.kpi.eval.domain.task.event.talent.TalentEvalConfirmedEvent;
import com.polaris.kpi.eval.domain.task.event.talent.scoring.EvalScoreChanged;
import com.polaris.kpi.eval.domain.task.event.talent.scoring.ExtDataLocked;
import com.polaris.kpi.eval.domain.task.event.talent.scoring.ScorerSkiped;
import com.polaris.kpi.eval.domain.task.event.talent.scoring.ScorerTransfered;
import com.polaris.kpi.eval.domain.task.ext.IOkrAclSvc;
import com.polaris.kpi.eval.domain.task.repo.*;
import com.polaris.kpi.eval.domain.task.type.*;
import com.polaris.kpi.eval.domain.temp.entity.std.StdTemp;
import com.polaris.kpi.eval.infr.cycle.dao.CycleEvalDao;
import com.polaris.kpi.eval.infr.extData.dao.ExtDataItemFieldCorrDao;
import com.polaris.kpi.eval.infr.extData.dao.ExtDataSyncDao;
import com.polaris.kpi.eval.infr.extData.pojo.po.ExtDataSyncPo;
import com.polaris.kpi.eval.infr.group.pojo.EvalGroupOfTaskResult;
import com.polaris.kpi.eval.infr.task.ImportEvalEmpPo;
import com.polaris.kpi.eval.infr.task.builder.EmpDetailBuilder;
import com.polaris.kpi.eval.infr.task.dao.*;
import com.polaris.kpi.eval.infr.task.ppojo.*;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.EvalResultPo;
import com.polaris.kpi.eval.infr.task.ppojo.calibrated.ResultAuditTaskUserPo;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.*;
import com.polaris.kpi.eval.infr.task.ppojo.open.OpenMyTeamEvalPo;
import com.polaris.kpi.eval.infr.task.query.*;
import com.polaris.kpi.eval.infr.task.query.calibrated.CalibrateTaskUserQry;
import com.polaris.kpi.eval.infr.task.query.empeval.*;
import com.polaris.kpi.eval.infr.task.repimpl.EmpEvalRuleRepoImpl;
import com.polaris.kpi.eval.infr.task.repimpl.RuleSnapOnEmpRepoImpl;
import com.polaris.kpi.eval.infr.task.repimpl.RuleSnapOnTaskRepoImpl;
import com.polaris.kpi.extData.domain.entity.ExtDataItemFieldCorr;
import com.polaris.kpi.org.domain.company.repo.TenantSysConfRepo;
import com.polaris.kpi.org.domain.dept.dmsvc.EmpParser;
import com.polaris.kpi.org.domain.dept.entity.*;
import com.polaris.kpi.org.domain.dept.repo.EmpFinder;
import com.polaris.kpi.org.domain.dept.repo.MsgCenterRepo;
import com.polaris.kpi.org.domain.dept.type.TaskId;
import com.polaris.kpi.org.domain.emp.entity.KpiEmployee;
import com.polaris.kpi.org.domain.emp.repo.KpiEmpRepo;
import com.polaris.kpi.org.domain.emp.type.Emp;
import com.polaris.kpi.org.infr.company.dao.CompanyDaoImpl;
import com.polaris.kpi.org.infr.company.dao.CompanyMsgCenterDao;
import com.polaris.kpi.org.infr.company.dao.TenantSysConfDao;
import com.polaris.kpi.org.infr.company.query.CompanyMsgQuery;
import com.polaris.kpi.org.infr.company.repimpl.CompanyMsgCenterRepo;
import com.polaris.kpi.org.infr.dept.dao.KpiOrgDao;
import com.polaris.kpi.org.infr.emp.dao.KpiEmpDao;
import com.polaris.kpi.setting.dao.ManagerPrivDao;
import com.polaris.kpi.setting.domain.entity.*;
import com.polaris.kpi.setting.domain.repo.ResultAuditFlowRepo;
import com.polaris.kpi.setting.ppojo.ManagerPrivPo;
import com.polaris.sdk.type.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.lufei.ibatis.common.data.ToDataBuilder;
import org.lufei.ibatis.mapper.PagedList;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge.*;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.app.task.appsvc
 * @Author: lufei
 * @CreateTime: 2022-08-24  15:25
 * @Description: 员工考核
 * @Version: 1.0
 */
@Service
@Slf4j
public class EmpEvalAppSvc {
    @Autowired
    private TaskAuditRepo auditRepo;
    @Autowired
    private EvaluateTaskRepo taskRepo;
    @Autowired
    private EvaluateTaskDao evalTaskDao;

    @Autowired
    private TaskUserRepo userRepo;
    @Autowired
    private BatchUpdateBaseEvalUser batchUpdateBaseEvalUser;
    @Autowired
    private EmpEvalRuleRepoImpl empRuleRepo;
    @Autowired
    private EmpEvalDao empEvalDao;
    @Autowired
    private TaskUserDao taskUserDao;
    @Autowired
    private EmpEvalScorerDao empEvalScorerDao;
    @Autowired
    private TaskCoachDao taskCoachDao;
    @Autowired
    private ApplyLastDmSvc useLastDmSvc;
    @Autowired
    private KpiEmpDao kpiEmpDao;
    @Autowired
    private EvalKpiDao kpiDao;
    @Autowired
    private IOkrAclSvc evalOkrAcl;
    @Autowired
    private IObItemPlanAcl objectAcl;
    @Autowired
    private DeptEmpDao deptEmpDao;
    @Autowired
    private OpLogDao opLogDao;
    @Autowired
    private CycleEvalDao cycleEvalDao;
    @Autowired
    private CycleDao cycleDao;
    @Autowired
    private AdminTaskRepo adminTaskRepo;
    @Autowired
    private AdminTaskDao adminTaskDao;
    @Autowired
    private EmpEvalDmSvc evalDmSvc;
    @Autowired
    private CompanyDaoImpl companyDao;
    @Autowired
    private TenantSysConfDao confDao;
    @Autowired
    private TaskItemScoreRuleDao taskItemScoreRuleDao;
    @Autowired
    private CompanyMsgCenterDao companyMsgCenterDao;
    @Autowired
    private KpiOrgDao kpiOrgDao;
    @Autowired
    private TaskAuditDao taskAuditDao;
    @Autowired
    private TaskKpiItemDao taskKpiItemDao;
    @Autowired
    private ItemFileRepo itemFileRepo;
    @Autowired
    private ItemNoticeRepo itemNoticeRepo;
    @Resource
    private TaskAppealDao taskAppealDao;
    @Resource
    private TaskAppealRepo taskAppealRepo;
    @Resource
    private TaskKpiRepo taskKpiRepo;
    @Autowired
    private KpiEmpDao empDao;
    @Autowired
    private EvaluateTaskDao taskDao;
    @Autowired
    private EvalTaskAppSvc evalTaskAppSvc;
    @Autowired
    private GradeDao gradeDao;
    @Autowired
    private CalibratedDao calibratedDao;
    @Autowired
    private IOkrAclSvc okrAclSvc;
    @Autowired
    private MsgCenterRepo centerRepo;
    @Autowired
    private KpiEmpRepo kpiEmpRepo;
    @Autowired
    private ManagerPrivDao managerPrivDao;
    @Autowired
    private CycleEvalDmSvc cycleEvalDmSvc;
    @Autowired
    private ResultAuditFlowRepo auditFlowRepo;
    @Autowired
    private CalibratedMessageSender calibratedMsgSender;
    @Autowired
    private AdminTaskOperationRepo adminTaskOperationRepo;
    @Autowired
    private AskEvalAcl askEvalAcl;
    @Autowired
    private CompanyCacheRepo cacheRepo;
    @Autowired
    private CompanyMsgCenterRepo companyMsgCenterRepo;
    @Autowired
    private AdminTaskAppSvc taskAppSvc;
    @Autowired
    private RuleSnapOnEmpRepoImpl onEmpRepo;
    @Autowired
    private RuleSnapOnTaskRepoImpl onTaskRepo;
    @Autowired
    private FinishValueMessageSender finishValueMessageSender;
    @Autowired
    private TransactionWrap tx;
    @Autowired
    private EmpEvalDmSvc empEvalDmSvc;
    @Autowired
    private TenantSysConfRepo tenantSysConfRepo;
    @Autowired
    @Lazy
    private EmpEvalAppSvc self;
    @Autowired
    private ScoreStageAppSvc scoreStageAppSvc;
    @Autowired
    private ScorerSummaryTodoRepo scorerSummaryTodoRepo;
    @Autowired
    private MsgAcl msgAcl;
    @Autowired
    private ScoreMessageSender scoreMessageSender;
    @Autowired
    private EmpEvalScorerNodeDao scorerNodeDao;
    @Autowired
    private EmpEvalScorerRepo empEvalScorerRepo;
    @Autowired
    private EvalScorerNodeKpiItemDao evalScorerNodeKpiItemDao;
    @Autowired
    private EmpEvalScorerNodeDao empEvalScorerNodeDao;
    @Autowired
    private ExtDataItemFieldCorrDao itemFieldCorrDao;
    @Autowired
    private ExtDataSyncDao syncDao;
    @Autowired
    private OnScoreEvalRepo onScoreEvalRepo;
    @Autowired
    private ScoreMsgAcl scoreMsgAcl;
    @Autowired
    private IUrgingMsgSender urgingMsgSender;
    @Autowired
    private BatchScoreEvalMergeDao batchScoreEvalMergeDao;
    @Autowired
    private EvalTaskInterviewConfirmDao confirmDao;
    @Autowired
    private TenantSysConfDao tenantSysConfDao;

    public ScoreSceneWrap buildNodes(TenantId tenantId, String taskUserId) {
        EmpEvalMerge empEvalMerge = empRuleRepo.getEmpEvalMerge(tenantId, taskUserId, item | itemRule);
        ScoreSceneWrap scoreSceneWrap = empEvalMerge.getKpiTypes().buildNodes();
        return scoreSceneWrap;
    }


    //可共用的简单流程
    public DisplayEvalFlow getFlow(TenantId tenantId, String taskUserId, TalentStatus qryStatus, String scene) {
        EvalUser user = userRepo.getTaskUser(tenantId, taskUserId);
        DisplayEvalFlow flow = auditRepo.getFlow(user, scene);
        EmpEvalMerge empEvalMerge = empRuleRepo.getEmpEvalMerge(tenantId, taskUserId, type);
        AffirmTaskConf confirmTask = empEvalMerge.getConfirmTask();
        if (flow == null) {
            return confirmTask.asDisplayEvalFlow();
        }
        confirmTask.mergeIndFlow(flow);
        boolean before = TalentStatus.statusOf(user.getTaskStatus()).before(qryStatus);
        flow.status(before);
        flow.matchDeadLineEndDate(user.matchDeadLineEndDate(qryStatus.getStatus()));
        return flow;
    }

    //结果确认流程
    public DisplayEvalFlow getResultAffirmingFlow(TenantId tenantId, String taskUserId) {
        EvalUser user = userRepo.getTaskUser(tenantId, taskUserId);
        DisplayEvalFlow auditFlow = new DisplayEvalFlow(AuditEnum.PERF_RESULTS_AFFIRM.getScene());
        LevelNode node = new LevelNode(1, "or");
        FlowRater flowRater = new FlowRater(user.getAvatar(), user.getEmpId(), user.getEmpName(), "on_the_job");
        OperationLogDo log = opLogDao.findLog(tenantId, taskUserId, OperationLogSceneEnum.RESULT_AFFIRM.getScene());
        flowRater.setFinishTime(log == null ? null : log.getCreatedTime());
        node.setRaters(Arrays.asList(flowRater));
        auditFlow.addLevel(node);
        auditFlow.matchDeadLineEndDate(user.matchDeadLineEndDate(TalentStatus.RESULTS_AFFIRMING.getStatus()));
        if (TalentStatus.statusOf(user.getTaskStatus()) == TalentStatus.FINISHED) {
            auditFlow.setStatus(TaskProgressStatus.FINISHED);
        } else {
            auditFlow.setStatus(TaskProgressStatus.WAIT);
        }
        return auditFlow;
    }

    //查看评分流程
    public List<DisplayEvalFlow> getScoreFlow(TenantId tenantId, String taskUserId) {
        EvalUser user = userRepo.getTaskUser(tenantId, taskUserId);
        EmpEvalMerge empEvalMerge = empRuleRepo.getEmpEvalMerge(tenantId, taskUserId, item | itemRule);
        List<DisplayEvalFlow> auditFlows = auditRepo.listScoringFlow(user, empEvalMerge);
        return auditFlows;
    }

    //查看评价关系
    public List<DisplayEvalFlow> getScoreAuditFlows(EmpEvalRule empEvalRule, String taskId, String empId, String empName) {
        List<DisplayEvalFlow> auditFlows = auditRepo.listScoreFlow(empId, empName, taskId, empEvalRule);
        return auditFlows;
    }

    //查看评分流程
    public List<DisplayEvalFlow> getScoreFlowV2(TenantId tenantId, String taskUserId) {
        EvalUser user = userRepo.getTaskUser(tenantId, taskUserId);

        if (!user.wasTempTask()) {
            //  EmpEvalMerge empEvalMerge = empRuleRepo.getEmpEvalMerge(tenantId, taskUserId, EmpEvalMerge.item | EmpEvalMerge.itemRule);
            //AsDisplayFlowRs asDisplayFlow = empEvalMerge.asDisplayFlowV3(user.getEmpId());
            // Set<String> raterEmpIds = asDisplayFlow.mapRaterEmpIds();
            //  Map<String, KpiEmp> ratersMap = empDao.listByEmpAsMap(tenantId, raterEmpIds);
            //  asDisplayFlow.appendRaterName(ratersMap);
            //asDisplayFlow.matchDeadLineEndDate(user);
            AdminTask adminTask = adminTaskRepo.getAdminTask(tenantId, user.getTaskId());
            EmpEvalRule rule = empEvalDao.getBaseEvalRule(tenantId, taskUserId);
            ListWrap<EmpEvalKpiType> kpiTypes = kpiDao.listEmpEvalKpiTypeV3(tenantId, taskUserId);
            rule.setKpiTypes(new KpiListWrap(kpiTypes.getDatas()));
            if (Objects.isNull(rule.getScoreSortConf())) {
                rule.setScoreSortConf(adminTask.getScoreSortConf());
            }
            ListWrap<FlowRater> flowRatersWrap = empEvalScorerNodeDao.listScorerNodeFlow(tenantId.getId(), taskUserId);
            Set<String> raterEmpIds = flowRatersWrap.getDatas().stream().map(FlowRater::getEmpId).collect(Collectors.toSet());
            Map<String, KpiEmp> ratersMap = empDao.listByEmpAsMap(tenantId, raterEmpIds);
            String endDate = user.matchDeadLineEndDate(TalentStatus.SCORING.getStatus());
            AsDisplayFlowRsV3 asDisplayFlow = new AsDisplayFlowRsV3(rule, flowRatersWrap, ratersMap,endDate);
            asDisplayFlow.covertDisplayEvalFlow();
            return asDisplayFlow.getFlows();
        } else {
            EmpEvalMerge empEvalMerge = empRuleRepo.getEmpEvalMerge(tenantId, taskUserId, item | itemRule);
            List<LevelManager> managers = kpiEmpRepo.listEmpsManager(tenantId, new EmpId(user.getEmpId()));
            Map<String, List<LevelManager>> groups = new ListWrap<>(managers).groupBy(manager -> manager.getLevel() + "").getGroups();
            user.setLevelManagers(groups);
            return auditRepo.listScoringFlow(user, empEvalMerge);
        }
    }

    //执行中有录入流程及变更流程
    public List<DisplayEvalFlow> getConfirmedFlow(TenantId tenantId, String taskUserId) {
        EvalUser taskUser = userRepo.getTaskUser(tenantId, taskUserId);
        EmpEvalMerge empEvalMerge = empRuleRepo.getEmpEvalMerge(tenantId, taskUserId, item | itemRule);
        boolean hasInputWaitNotify = false;
        if(taskUser.isConfirmed()) {
             hasInputWaitNotify = adminTaskDao.existsInputNotifyTaskUserId(tenantId.getId(), taskUserId); //执行环节才查询
        }
        List<DisplayEvalFlow> inputAndChangeds = auditRepo.getConfirmedFlow(taskUser, empEvalMerge,hasInputWaitNotify);
        return inputAndChangeds;
    }

    //面谈中有执行流程及确认流程
    public List<DisplayEvalFlow> getInterviewResultFlow(TenantId tenantId, String taskUserId) {
        EvalUser taskUser = userRepo.getTaskUser(tenantId, taskUserId);
        return auditRepo.getInterviewResultFlow(taskUser);
    }

    public List<EvalEmp> listExistAtCycle(EmpEvalAtCycleQuery query) {
        return empEvalDao.listExistAtCycle(query);
    }

    //    @Transactional
    public void applyTemp(ApplyTempCmd cmd) {
        log.info("applyTemp处理开始, 线程: {}", Thread.currentThread().getName());
        boolean planAuthOpen = tenantSysConfRepo.isOpen(cmd.getTenantId().getId(), TenantSysConfEnum.BUSINESS_PLAN_ITEM_AUTH.getType());
        List<EvalUser> baseEvals = taskUserDao.listBaseEvalUser(cmd.getTenantId(), cmd.getTaskUserIds());
        CompanyConf companyConf = confDao.findCompanyConf(cmd.getTenantId());
        List<IndLevelGroup> levelGroups = gradeDao.listAllIndLevelGroup(cmd.getTenantId().getId());
        Set<String> taskIds = baseEvals.stream().map(evalUser -> evalUser.getTaskId()).collect(Collectors.toSet());
        ListWrap<AdminTask> taskMap = adminTaskDao.listAdminTaskMerge(cmd.getTenantId(), taskIds);
        EvalRuleConfed evalRuleConfed = new EvalRuleConfed(cmd.getTenantId().getId(), cmd.getOpEmpId(), new ArrayList<>());
        KpiEmp opEmp = empDao.findKpiEmp(cmd.getTenantId(), cmd.getOpEmpId());
        List<EmpEvalOperation> logs = new ArrayList<>();
        List<List<EvalUser>> groups = CollUtil.split(baseEvals, 100);
        for (List<EvalUser> evalUsers : groups) {
            List<String> userIdGroup = evalUsers.stream().map(evalUser -> evalUser.getId()).collect(Collectors.toList());
            ListWrap<EmpEvalRule> ruleMerges = batchScoreEvalMergeDao.listEmpEvalRuleMerge(cmd.getTenantId(), userIdGroup);
            for (EvalUser user : evalUsers) {
                ApplyTempDmSvc empEvalDmSvc = new ApplyTempDmSvc(user, companyConf, cmd.getTemp(), cmd.getOpEmpId(), cmd.getOpAdminType());
//            EvalUser user = userRepo.getBaseTaskUser(cmd.getTenantId(), curUserId);
                user.checkExistEvalEmp();
                if (!TalentStatus.DRAW_UP_ING.getStatus().equals(user.getTaskStatus())) {
                    throw new KpiI18NException("40010", "被考核人考核状态已发生改变，请刷新页面重新操作！！");
                }
//            EmpEvalRule empEvalRule = empEvalDao.getEmpEvalRule(cmd.getTenantId(), user.getId());
                EmpEvalRule empEvalRule = ruleMerges.mapGet(user.getId());
                AdminTask adminTask = taskMap.mapGet(user.getTaskId());
//            AdminTask adminTask = adminTaskRepo.getAdminTask(cmd.getTenantId(), user.getTaskId());
                List<BusinessPlanItem> planItemList = planAuthOpen ? this.listBusinessPlanItem(cmd.getTenantId().getId(), user.getId(), cmd.getPlanItemIds()) : null;  //经营计划指标目标值;
                log.info("经营计划指标目标值，taskUserIds={}", JSONObject.toJSONString(planItemList));
                if (Objects.isNull(empEvalRule)) {
                    empEvalRule = new EmpEvalRule(cmd.getTenantId(), cmd.getCurUserId(), new AuditResultConf());
                    empEvalDmSvc.init(empEvalRule, adminTask, planItemList);
                    user.setEmpEvalRule(empEvalRule);
                    empEvalRule = empEvalDmSvc.applyTemp2(cmd.getTemp(), user, new EmpId(cmd.getOpEmpId()), planItemList);
                } else {
                    empEvalDmSvc.init(empEvalRule, adminTask, planItemList);
                    user.setEmpEvalRule(empEvalRule);
                    //清除考核发起人。
                    empEvalRule.setInitiator(null);
                    empEvalRule = empEvalDmSvc.applyTemp2(cmd.getTemp(), user, new EmpId(cmd.getOpEmpId()), planItemList);

                }
                if (Objects.nonNull(cmd.getAuditResultAuditFlowConf())) {
                    empEvalRule.applyConfFromEvalGroup(cmd.getAuditResultAuditFlowConf());
                    user.setCustomAuditResultFlow(true);
                }

                //生成360问卷考核实例
                user.setTaskName(adminTask.getTaskName());
                if (cmd.getTemp().conf360Ask()) {//开发360问卷才执行
                    askEvalAcl.createdAsk360Eval(user, cmd.getTenantId().getId(), cmd.getOpEmpId());
                }
                evalDmSvc.mergeConf(adminTask.clone(), user, cmd.getOpEmpId());

                //复制指标等级组
                empEvalRule.copyLevelGroup(levelGroups);
                EmpEvalOperation operation = new EmpEvalOperation(user.getId(), cmd.getTenantId().getId(), cmd.getOpEmpId(), cmd.getOpAdminType());
                operation.empEvalCreated(empEvalRule.getRuleName());
                operation.operate(opEmp);
                logs.add(operation);
                if (Objects.isNull(user.getResultSendNotify())) {
                    user.setResultSendNotify(adminTask.getResultSendNotify());
                }
                user.setEvalGroupId(cmd.getEvalGroupId());
                user.setEvalGroupName(cmd.getEvalGroupName());
                evalRuleConfed.addEval(user); // evalRuleConfed 替换 new EvalTaskErrorEvent(cmd.getTenantId().getId(), user).fire();
            }
            tx.runTran(() -> {
                empRuleRepo.applyTempBatch(cmd.getTenantId(), evalUsers, logs);
                batchUpdateBaseEvalUser.updateBaseEvalUser(cmd.getTenantId(), evalUsers);
            });
        }
        evalRuleConfed.fire();
        new EmpCntOfAdminTaskChanged(cmd.getTenantId().getId(), taskIds).fire();
    }


    //发起考核:考核表-新接口,启动执行考核
    @Transactional
    public StartExecuteEvalCreated startExecuteEval(StartEmpEvalCmd cmd) {
        int ok200Cnt = userRepo.updateStage(cmd.getTenantId(), cmd.getTaskUserIds(), TalentStatus.CREATED);
        empRuleRepo.initiator(cmd.getTenantId(), cmd.getTaskUserIds(), cmd.getOpEmpId());
        List<OperationLogDo> opLogs = cmd.startLogs(OperationLogSceneEnum.CREATED_TASK);
        opLogDao.batchAddLogs(opLogs);
        return new StartExecuteEvalCreated(cmd.getTenantId().getId(), cmd.getOpEmpId(), cmd.getTaskUserIds(), ok200Cnt, cmd.getBatchId());
    }

    @Transactional
    public void refreshBatchStartCnt(String companyId, String bacthId, List<String> taskUserIds) {
        adminTaskRepo.refreshBatchStartCnt(companyId, bacthId, taskUserIds);
    }

    @Async(value = "high")
    public void batchStartExecuteEval(BatchStartEmpEvalCmd cmd) {
        TenantId companyId = new TenantId(cmd.getCompanyId());
        for (String taskId : cmd.getTaskIds()) {
            /**查询待发起任务*/
            List<String> userIds = taskUserDao.listDrawUpUserIds(companyId.getId(), taskId, cmd.getAdminOrgIds(), cmd.getPerformanceType());
            if (userIds.isEmpty()) {
                continue;
            }
            int ok200Cnt = userRepo.updateStage(companyId, userIds, TalentStatus.CREATED);
            empRuleRepo.initiator(companyId, userIds, cmd.getOpEmpId());
            List<OperationLogDo> opLogs = cmd.startLogs(userIds, OperationLogSceneEnum.CREATED_TASK);
            opLogDao.batchAddLogs(opLogs);
        }
        AdminTaskBatchStartEval adminTaskBatchStartEval = new AdminTaskBatchStartEval(companyId.getId(), cmd.getOpEmpId(), cmd.getBatchId());
        adminTaskBatchStartEval.copyCmd(cmd.getTaskIds(), cmd.getPerformanceType(), cmd.getAdminOrgIds());
        adminTaskBatchStartEval.fire();
    }

    public PagedList<PerfEvaluateTaskUserDTO> pagedEvalError(EmpEvalErrorQuery query) {
        query.setAdminOrgIds(kpiOrgDao.listAdminScopePrivOrgIds(query.getCompanyId(), query.getOpEmpId(), null));
        return taskUserDao.pagedEvalError(query);
    }

    public ScoreStageRule getScoreStageRule(TenantId tenantId, String taskUserId) {
        EvalUser evalUser = userRepo.getBaseTaskUser(tenantId, taskUserId);
        if (evalUser.hasEmpEvalRule()) {//新数据
            return userRepo.getScoreStageRule(tenantId, taskUserId, evalUser);
        }
        if (evalUser.wasTempTask()) {//旧数据
            AdminTask adminTask = adminTaskRepo.getAdminTask(tenantId, evalUser.getTaskId());
            return userRepo.buildScoreStageRule(tenantId, evalUser.getId(), adminTask);
        }
        return null;
    }


    //新加考核规则
    @Transactional
    public void addScoreStageRule(ScoreStageRuleCmd cmd) {
        ScoreStageRule scoreStageRule = cmd.getScoreStageRule();
        EvalUser evalUser = userRepo.getBaseTaskUser(cmd.getTenantId(), scoreStageRule.getEmpEvalId());
        evalUser.checkExistEvalEmp();
        AdminTask task = adminTaskRepo.getAdminTask(cmd.getTenantId(), evalUser.getTaskId());
        EmpEvalRule empEvalRule = new EmpEvalRule(cmd.getTenantId(), cmd.getOpEmpId(), evalUser.getTaskId(), evalUser.getEmpId(), scoreStageRule);
        empEvalRule.setScoreSortConf(task.getScoreSortConf());
        initAllowSelfAsPeer(cmd.getTenantId(), empEvalRule);
        evalUser.setEmpEvalRule(empEvalRule);
        evalUser.setTaskName(task.getTaskName());

        evalDmSvc.mergeConf(task, evalUser, cmd.getOpEmpId());
        EmpEvalOperation operation = new EmpEvalOperation(evalUser.getId(), cmd.getTenantId().getId(), cmd.getOpEmpId(), cmd.getOpAdminType());
        operation.empEvalCreated(cmd.getRuleName());
        List<IndLevelGroup> levelGroups = gradeDao.listAllIndLevelGroup(cmd.getTenantId().getId());
        //复制指标等级组
        empEvalRule.copyLevelGroup(levelGroups);
        //经营计划指标目标值只有一份时,默认选中
        List<BusinessPlanItem> planItemList = this.listBusinessPlanItem(cmd.getTenantId().getId(), evalUser.getId(), empEvalRule.getPlanItemIds());
        log.info("经营计划指标目标值，taskUserIds={}", JSONObject.toJSONString(planItemList));
        empEvalRule.initOkrGoalId(planItemList);
        //生成360问卷考核实例
        askEvalAcl.createdAsk360Eval(evalUser, cmd.getTenantId().getId(), cmd.getOpEmpId());
        empRuleRepo.addEmpEvalRule(evalUser, empEvalRule, operation, true);
        if (Objects.isNull(evalUser.getResultSendNotify())) {
            evalUser.setResultSendNotify(task.getResultSendNotify());
        }
        evalUser.setRuleConfStatus(scoreStageRule.checkRaters());
        userRepo.updateTaskUser(evalUser);
        EvalRuleConfed evalRuleConfed = new EvalRuleConfed(cmd.getTenantId().getId(), cmd.getOpEmpId(), new ArrayList<>());
        evalRuleConfed.addEval(evalUser);
        evalRuleConfed.fire();
        new EmpCntOfAdminTaskChanged(empEvalRule.getCompanyId().getId(), evalUser.getTaskId()).publish();
        /**处理异常*/
        new EvalTaskErrorEvent(cmd.getTenantId().getId(), evalUser).publish();
        //考核规则变更事件
        new EvalRuleChanged(cmd.getTenantId(),cmd.getOpEmpId(), evalUser,empEvalRule).fire();
    }

    private void initAllowSelfAsPeer(TenantId tenantId, EmpEvalRule empEvalRule) {
        TenantSysConf sysInfo = tenantSysConfRepo.getCompanySysInfo(tenantId.getId(), TenantSysConf.self_as_peer_20240113);
        log.info("是否允许自评为互评人：{}", sysInfo.isOpen());
        empEvalRule.initAllowSelfAsPeer(sysInfo.isOpen());
    }

    /**
     * 批量刷新待发起的任务异常
     */
    public List<EvalUser> listRefreshEvalErrorStatus(TenantId tenantId) {
        List<EvalUser> evalUserList = taskUserDao.listDrawUpIng(tenantId);
        return evalUserList;
    }

    public void checkErro(TenantId companyId, List<EvalUser> evalUsers) {
        EmpFinder empFinder = (tenantId, empIds) -> new ArrayList<>(empDao.listByEmpWithOutDeleted(tenantId, empIds).values());
        EmpParser empParser = new EmpParser(empFinder, companyId);
        EvalRuleStatusCheckDmsvc checkDmsvc = new EvalRuleStatusCheckDmsvc(companyId, empParser);
        checkDmsvc.checkEvalRuleErrStatus(objectAcl, evalUsers);
        tx.runTran(() -> checkDmsvc.saveErrStatus(empRuleRepo));
    }

    /**
     * 添加评分人
     */
    public void explainEmpEvalScorerBatchForUsers(TenantId tenantId, List<EvalUser> evalUsers) {
        if (Objects.isNull(tenantId) || CollUtil.isEmpty(evalUsers)) {
            return;
        }
        ExplainBatchEvalScorerDmSvc dmSvc = new ExplainBatchEvalScorerDmSvc(evalUsers, tenantId, null);
        dmSvc.explainBatchEvalScorer();
        if (CollUtil.isEmpty(dmSvc.getScorers())) {
            log.info(" 解析评分人为空");
            return;
        }
        tx.runTran(() -> {
            dmSvc.saveBatchEmpEvalScorerForUsers(empEvalScorerRepo);
            dmSvc.updateSuperiorScoreOrderBatch(empRuleRepo);
        });
    }

    /**
     * 添加评分人
     */
    public void explainEmpEvalScorer(TenantId tenantId, String opEmpId, EvalUser user, EmpEvalRule rule) {
        if (Objects.isNull(tenantId) || Objects.isNull(user) || Objects.isNull(rule)) {
            return;
        }

        ExplainEvalScorerDmSvc dmSvc = new ExplainEvalScorerDmSvc(user, rule, tenantId, opEmpId);
        dmSvc.explainEvalScorer();
        if (CollUtil.isEmpty(dmSvc.getScorers().getDatas())){
            log.info(" 解析评分人为空,无需评价人评价");
            tx.runTran(() -> {
                empEvalScorerRepo.delEmpEvalScorer(tenantId.getId(),user.getId());
            });
            return;
        }
        tx.runTran(() -> {
            empEvalScorerRepo.saveBatchEmpEvalScorer(tenantId.getId(),dmSvc.getRule().getCreatedUser(), user.getId(), dmSvc.getScorers().getDatas());
            empRuleRepo.updateSuperiorScoreOrder(tenantId, dmSvc.getRule().getEmpEvalId(),dmSvc.getRule().getSuperiorScoreOrder());
        });
    }


    /**
     * 添加评分人
     */
    public void fixExplainEvalScorer(TenantId tenantId,String  taskUserId) {
        if (Objects.isNull(tenantId) || Objects.isNull(taskUserId)) {
            return;
        }
        EvalUser taskUser = userRepo.getBaseTaskUser(tenantId, taskUserId);
        EvalOnScoreStage onScoreStage = onScoreEvalRepo.getOnScoreEval(tenantId, taskUserId);
        EmpEvalRule rule = new EmpEvalRule();
        BeanUtil.copyProperties(onScoreStage.getEmpEval(),rule);
        String opEmpId = rule.getCreatedUser();
        ExplainEvalScorerDmSvc dmSvc = new ExplainEvalScorerDmSvc(taskUser, rule, tenantId, opEmpId);
        dmSvc.explainEvalScorer();
        tx.runTran(() -> {
            empEvalScorerRepo.saveBatchEmpEvalScorer(tenantId.getId(),opEmpId, taskUserId, dmSvc.getScorers().getDatas());
            empRuleRepo.updateSuperiorScoreOrder(tenantId, dmSvc.getRule().getEmpEvalId(),dmSvc.getRule().getSuperiorScoreOrder());
        });
    }


    /**
     * 处理任务异常 checkErro
     */
    @Transactional
    public void anewEvalRuleStatus(String companyId, EvalUser evalUser) {
        evalUser.setRuleConfStatus(200);
        evalUser.setRuleConfError(null);
        EmpEvalMerge empEvalRule = new ToDataBuilder<>(evalUser.getEmpEvalRule(), EmpEvalMerge.class).data();
        if (empEvalRule == null) {
            return;
        }
        log.debug("anewEvalRuleStatus.empEvalRule:{}", JSONUtil.toJsonStr(empEvalRule));
//        if ("drawUpIng".equals(evalUser.getTaskStatus())) {
//            return;
//        }
        TalentStatus current = TalentStatus.statusOf(evalUser.getTaskStatus());
        //任务确认阶段配置
        AffirmTaskConf confirmTask = empEvalRule.getConfirmTask();
        if (confirmTask.isOpen() && current.beforeEq(TalentStatus.CONFIRMING)) {
            List<ConfirmNode> auditNodes = confirmTask.getAuditNodes();
            parseCheckFlowError(evalUser, auditNodes, "modify_item_audit", confirmTask.getNodeEmpVacancy());
        }
        //任务执行阶段配置
        EditExeIndiConf editExeIndi = empEvalRule.getEditExeIndi();
        if (editExeIndi.auditIsOpen() && current.beforeEq(TalentStatus.CONFIRMED)) {
            parseCheckFlowError(evalUser, editExeIndi.getAuditNodes(), "change_item_audit", editExeIndi.getNodeEmpVacancy());
        }
        //完成值审核阶段配置
        FinishValueAuditConf finishValueAudit = empEvalRule.getFinishValueAudit();
        if (finishValueAudit.isOpen() && current.beforeEq(TalentStatus.FINISH_VALUE_AUDIT)) {
            parseCheckFlowError(evalUser, finishValueAudit.getAuditNodes(), "finish_value_audit", 2);
        }
        //校准
        AuditResultConf auditResult = empEvalRule.getAuditResult();
        if (auditResult.isOpen() && current.beforeEq(TalentStatus.RESULTS_AUDITING)) {
            parseCheckFlowError(evalUser, auditResult.getAuditNodes(), "final_result_audit", auditResult.getNodeEmpVacancy());
        }
        //面谈
        InterviewConf interviewConf = empEvalRule.getInterviewConf();
        if (Objects.nonNull(interviewConf) && interviewConf.isOpen()
                && current.beforeEq(TalentStatus.RESULTS_INTERVIEW)) {
            EvalTaskInterviewConf evalTaskInterview = new EvalTaskInterviewConf(interviewConf, new TenantId(companyId), new EmpId(evalUser.getEmpId()), new EmpId(empEvalRule.getCreatedUser()));
            //执行人或者确认人为空需要提示
            if (Objects.nonNull(interviewConf.getInterviewExcutorInfo())) {
                // evalUser.confEvalPublicErro("108"); // 面谈执行人异常
                parseCheckFlowError(evalUser, Collections.singletonList(interviewConf.getInterviewExcutorInfo()), "final_result_interview_excute", interviewConf.getNodeEmpVacancy());
            }
            if (evalTaskInterview.getIsOpenConfirm()
                    && CollUtil.isNotEmpty(interviewConf.getInterviewConfirmConf().getInterviewConfirmInfo())) {
                // 面谈确认人异常
                parseCheckFlowError(evalUser, interviewConf.getInterviewConfirmConf().getInterviewConfirmInfo(), "final_result_interview_confirm", interviewConf.getNodeEmpVacancy());
                // evalUser.confEvalPublicErro("109"); // 面谈确认人异常
            }
        }
        //阶段截止时间
        DeadLineConf deadLineConf = empEvalRule.getDeadLineConf();
        if (Objects.nonNull(deadLineConf) && deadLineConf.isOpen() && Objects.equals(current.getStatus(), "drawUpIng")) {
            if (deadLineConf.timeOuted(confirmTask.isOpen(), finishValueAudit.isOpen(), auditResult.isOpen(), Objects.nonNull(empEvalRule.getInterviewConf()) ? empEvalRule.getInterviewConf().isOpen() : false,
                    empEvalRule.getConfirmResult().isOpen(), LocalDate.now(), current)) {
                evalUser.deadLineTimeError(); // 截止时间异常
            }
        }

        /**公示异常*/
        /*PublishResultConf publishResult = empEvalRule.getPublishResult();
        if (publishResult.isOpen()) {
            if (!"afterFinished".equals(empEvalRule.getPublishResult().getType())) {
                if (isLackEmp(new TenantId(companyId), empEvalRule.parsePublicEmp())) {
                    evalUser.confEvalPublishErro("502"); // 公示异常
                }
            }
        }*/
        /**申述异常*/
        AppealConf appealConf = empEvalRule.getAppealConf();
        if (appealConf.isOpen()) {
//            if (isLackEmp(new TenantId(companyId), empEvalRule.parseAppealEmp(), null, 0)) {
//                evalUser.confEvalPublicErro("601"); // 申述异常
//            }
            if (Objects.nonNull(appealConf.getAppealFlowConf()) && !appealConf.getAppealFlowConf().getAuditNodes().isEmpty()) {
                parseCheckFlowError(evalUser, appealConf.getAppealFlowConf().getAuditNodes(), "result_appeal", appealConf.getNodeEmpVacancy());
            }


        }
        /**评分人异常*/
        if (current.beforeEq(TalentStatus.SCORING)) {
            evalUser.confEvalScoreRuleErro(hasNodeLeavedRater(new TenantId(companyId), empEvalRule.initRaters(), evalUser.getEmpId()));
        }
        if (current.beforeEq(TalentStatus.CONFIRMED)) {
            /**邀请评分人异常*/
            evalUser.confEvalInviteMutualErro(hasNodeLeavedRater(new TenantId(companyId), empEvalRule.getInviteMutualAuditRater(), evalUser.getEmpId()));
            /**录入人异常*/
            if (!empEvalRule.parseEntryClerkNoInput()) {
                if (empEvalRule.parseEntryClerkIsNull()) {
                    evalUser.confEvalPublicErro("701");
                } else {
                    if (isLackEmp(new TenantId(companyId), empEvalRule.parseEntryClerk(), 2, 0)) {
                        evalUser.confEvalPublicErro("702"); // 录入人异常
                    }
                }
            }
        }

        if (current.beforeEq(TalentStatus.FINISH_VALUE_AUDIT)) {
            //完成值审核异常
            if (!empEvalRule.kpiItemAllIsEmpt() && isLackEmp(new TenantId(companyId), empEvalRule.parseFinishedValueAudit(), 2, 0)) {
                evalUser.confEvalPublicErro("105"); //
            }
        }
        //经营计划指标异常
        List<BusinessPlanItem> planItemList = listBusinessPlanItem(companyId, evalUser.getId(), null);
        empEvalRule.parseBusinessPlanItem(planItemList, evalUser);

        if (!evalUser.evalRuleIsErr()) {
            evalUser.confEvalRuleOk();//初始值ok
        }
        if (!evalUser.evalRuleConfIsErr()) {
            evalUser.confEvalRuleErrorOk();
        }
        updateEvalRuleStatus(companyId, evalUser.getId(), evalUser.getRuleConfStatus(), evalUser.getRuleConfError());
    }

    public boolean isLackEmp(TenantId companyId, List<String> raterEmpIds, Integer nodeEmpVacancy, Integer auditNodeSize) {
        Map<String, KpiEmp> kpiEmps = kpiEmpDao.listByEmpWithOutDeleted(companyId, raterEmpIds);
        for (String raterEmpId : raterEmpIds) {
            KpiEmp kpiEmp = kpiEmps.get(raterEmpId);
            if (kpiEmp == null) {
                if (Objects.equals(auditNodeSize, 1) && Objects.equals(nodeEmpVacancy, 1)) {
                    return true;
                }
                if (Objects.equals(nodeEmpVacancy, 2) || Objects.equals(nodeEmpVacancy, 3)) {
                    return true;
                }
            }
        }
        return false;
    }

    public List<String> hasNodeLeavedRater(TenantId companyId, List<RaterNode> raterNodes, String opEmpId) {
        List<String> scoreErrorNode = new ArrayList<>();
        for (RaterNode raterNode : raterNodes) {
            List<String> raterEmpIds = new ArrayList<>();
            if ("self".equals(raterNode.getNode())) {
                raterEmpIds.add(opEmpId);
            } else if (("peer".equals(raterNode.getNode()) || "sub".equals(raterNode.getNode()))) {
                for (Rater rater : raterNode.getRaters()) {
                    raterEmpIds.add(Objects.equals(rater.getType(), 5) ? opEmpId : rater.getEmpId());
                    /*if ("empId".equals(rater.getEmpId())) {
                        raterEmpIds.add(opEmpId);
                    } else {
                        raterEmpIds.add(rater.getEmpId());
                    }*/
                }
            } else {
                if (raterNode.getRaters().isEmpty()) {
                    scoreErrorNode.add(raterNode.getNode());
                    continue;
                }
                raterEmpIds = raterNode.getRaters().stream().map(Rater::getEmpId).collect(Collectors.toList());
            }
            /*if (raterEmpIds.size() > CollUtil.distinct(raterEmpIds).size()) {
                scoreErrorNode.add(raterNode.getNode());
                continue;
            }*/
            Map<String, KpiEmp> kpiEmps = kpiEmpDao.listByEmpWithOutDeleted(companyId, raterEmpIds);
            if (CollUtil.isEmpty(kpiEmps)) {
                scoreErrorNode.add(raterNode.getNode());
                continue;
            }
            for (String raterEmpId : raterEmpIds) {
                KpiEmp kpiEmp = kpiEmps.get(raterEmpId);
                if (kpiEmp == null) {
                    scoreErrorNode.add(raterNode.getNode());
                }
            }
        }
        return scoreErrorNode.stream().distinct().collect(Collectors.toList());
    }

    public <T extends BaseAuditNode> void parseCheckFlowError(EvalUser evalUser, List<T> auditNodes, String node, Integer nodeEmpVacancy) {
        if (auditNodes.stream().flatMap(a -> a.getRaters().stream().filter(r -> Objects.equals(r.getEmpId(), "-1"))).collect(Collectors.toList()).size() == auditNodes.size()) {
            evalUser.confEvalRuleErro(node);
            return;
        }
        for (BaseAuditNode auditNode : auditNodes) {
            if (auditNode.getRaters().isEmpty()) {
                if (Objects.equals(nodeEmpVacancy, 2) || Objects.equals(nodeEmpVacancy, 3)) {//节点人员空缺 1:系统自动跳过  2:提示异常  3:转交给考核任务发起人
                    evalUser.confEvalRuleErro(node);
                    break;
                }
            }
            if (isLackEmp(evalUser.getCompanyId(), auditNode.getRaters().stream().map(Rater::getEmpId).collect(Collectors.toList()), nodeEmpVacancy, auditNodes.size())) {
                evalUser.confEvalRuleErro(node);
                break;
            }
        }
    }

    @Async
    @Transactional
    public void batchSaveEmpEvalOperation(EvalChangeDmSvc changeDmSvc) {
        if (CollUtil.isEmpty(changeDmSvc.getChangedEvalUsers())) {
            return;
        }
        List<EmpEvalOperation> operations = new ArrayList<>();
        for (EvalUser evalUser : changeDmSvc.getChangedEvalUsers()) {
            IndicatorLogCompare logCompare = new IndicatorLogCompare(evalUser.getBeforeEmpEvalRule(), evalUser.getEmpEvalRule());
            EmpEvalOperation operation = logCompare.doCompareDiff(evalUser.getTaskStatus());

            if (!operation.changed()) {
                return;
            }

            Map<String, KpiEmp> map = kpiEmpDao.listByEmpAsMap(new TenantId(changeDmSvc.getCompanyId()), Arrays.asList(changeDmSvc.getOpEmpId()));
            KpiEmp opEmp = map.get(changeDmSvc.getOpEmpId());
            operation.operate(opEmp);
            operation.setAdminType(changeDmSvc.getOpAdminType());
            operations.add(operation);
        }

        empRuleRepo.batchSaveDiffLog(operations);
    }

    @Transactional
    public void saveEmpEvalOperation(String opEmpId, String taskStatus, EmpEvalRule before, EmpEvalRule after, String adminType) {

        IndicatorLogCompare logCompare = new IndicatorLogCompare(before, after);
        EmpEvalOperation operation = logCompare.doCompareDiff(taskStatus);

        if (!operation.changed()) {
            return;
        }

        Map<String, KpiEmp> map = kpiEmpDao.listByEmpAsMap(before.getCompanyId(), Arrays.asList(opEmpId));
        KpiEmp opEmp = map.get(opEmpId);
        operation.operate(opEmp);
        operation.setAdminType(adminType);
        empRuleRepo.saveDiffLog(operation);
    }

    public PagedList<EmpEvalOperation> pagedEmpEvalOperation(EmpEvaOpLogQuery query) {
        return empEvalDao.pagedEmpEvalOperation(query);
    }

    //可复用上次任务的员工
    public List<EvalEmp> canUseLastEmpEval(HasLastEmpEvalQuery query) {
        return empEvalDao.hasLastEmpEval(query);
    }

    @Transactional
    public void evalRuleFromLast(UseLastEvalCmd cmd) {
        String curEvalId = cmd.getCurUserId();
        AdminTask curAdminTask = cmd.getCurAdminTask();

        EvalUser curEmpEval = userRepo.getEmpEval(cmd.getTenantId(), curEvalId);
        EvalUser lastEval = userRepo.getLastEmpEval(cmd.getTenantId(), curEmpEval.getEmpId(), cmd.getEvaluateType(), curEvalId, curEmpEval.getEvalOrgId());
        if (lastEval == null || lastEval.getEmpEvalRule() == null) {//不支持1.0任务转为考核规则
            return;
        }
        EmpEvalOperation operation = new EmpEvalOperation(curEvalId, cmd.getTenantId().getId(), cmd.getOpEmpId(), cmd.getOpAdminType());
        EmpEvalRule empEvalRule = useLastDmSvc.applyLastScoreConf(curEmpEval, lastEval, cmd.getOpEmpId());
        empEvalRule.setScoreSortConf(curAdminTask.getScoreSortConf());
        empEvalRule.setScoreConf(curAdminTask.getScoreConf());
        //开启汇总评分将上级评改为同时评
        if (Objects.nonNull(curAdminTask.getScoreConf().getSummarySendTodo())
                && curAdminTask.getScoreConf().getSummarySendTodo() == 2) {
            if (Objects.nonNull(empEvalRule.getS3SuperRater())) {
                empEvalRule.getS3SuperRater().setSuperiorScoreOrder("sameTime");
            }
        }
        if (Objects.isNull(curEmpEval.getEmpEvalRule())) {
            curEmpEval.setEmpEvalRule(empEvalRule);
            evalDmSvc.mergeConf(curAdminTask, curEmpEval, cmd.getOpEmpId());
        } else {
            curEmpEval.setEmpEvalRule(empEvalRule);
        }
        List<IndLevelGroup> levelGroups = gradeDao.listAllIndLevelGroup(cmd.getTenantId().getId());
        //复制指标等级组
        empEvalRule.copyLevelGroup(levelGroups);
        operation.copiedLatest(empEvalRule.getRuleName());

        empRuleRepo.addEmpEvalRule(curEmpEval, empEvalRule, operation, true);
        // List<String> raterNameIds = curEmpEval.getResetRaterNameIds();
        //2024-08-04 by xiaoqiu去掉这行代码，里面内容与addEmpEvalRule 大部分重合了。
        //empRuleRepo.editScoreStageConf(empEvalRule, curEmpEval.getTaskId(), curEmpEval.getEmpId(), raterNameIds);
        if (Objects.isNull(curEmpEval.getResultSendNotify())) {
            curEmpEval.setResultSendNotify(curAdminTask.getResultSendNotify());
        }
        //生成校准审批流程实例
        if (curEmpEval.isAuditResultCollectSend()) {
            ResultAuditFlow flow = new ResultAuditFlow(cmd.getTenantId().getId(), cmd.getOpEmpId());
            auditFlowRepo.saveAuditFlow(flow, curEmpEval);
            userRepo.updateTaskUser(curEmpEval);
        }
        new EmpCntOfAdminTaskChanged(cmd.getTenantId().getId(), curAdminTask.getId()).publish();
        /**处理异常*/
        new EvalTaskErrorEvent(cmd.getTenantId().getId(), curEmpEval).publish();
        //考核规则变更事件
        new EvalRuleChanged(cmd.getTenantId(),cmd.getOpEmpId(), curEmpEval,empEvalRule).fire();
    }


    @Transactional
    public void fixScoreStageConf(String companyId, String taskUserId) {

        TenantId tenantId = new TenantId(companyId);
        EvalUser curEmpEval = userRepo.getBaseTaskUser(tenantId, taskUserId);
        curEmpEval.checkExistEvalEmp();
        EmpEvalRule before = empRuleRepo.getEmpEvalRule(curEmpEval.getCompanyId(), curEmpEval.getId());
        curEmpEval.setEmpEvalRule(before);


        //ScoreStageRule empEvalRule = this.getScoreStageRule(tenantId, taskUserId);
        AdminTask curAdminTask = this.getAdminTask(tenantId, curEmpEval.getTaskId());
        //使用本次的周期配置,和任务配置
        evalDmSvc.mergeConf(curAdminTask, curEmpEval, curEmpEval.getCreatedUser());

        //curEmpEval.getEmpEvalRule().reSetConf(empEvalRule, curEmpEval.getTaskId(), curEmpEval.getEmpId(), curEmpEval.getCreatedUser());
        //curEmpEval.setRuleConfStatus(empEvalRule.checkRaters());
        empRuleRepo.editConfirmConf(curEmpEval.getEmpEvalRule(), curEmpEval.getTaskId(), curEmpEval.getEmpId());
        empRuleRepo.editExeIndiConf(curEmpEval.getEmpEvalRule(), curEmpEval.getTaskId(), curEmpEval.getEmpId());
        empRuleRepo.editScoreStageConf(curEmpEval.getEmpEvalRule(), curEmpEval.getTaskId(), curEmpEval.getEmpId(), null,curEmpEval);
        empRuleRepo.editAuditResultConf(curEmpEval.getEmpEvalRule(), curEmpEval.getTaskId(), curEmpEval.getEmpId());
        //this.editScoreStageConf(cmd);
        //userRepo.updateTaskUser(user);

    }

    //public EmpEvalForScorePo empEvalForScore(TenantId tenantId, String taskUserId, String opEmpId) {
    //    EmpEvalMerge evalRule = empRuleRepo.getScoreEmpEvalDetail(tenantId, taskUserId, opEmpId);
    //    EvalUser taskUser = userRepo.getBaseTaskUser(tenantId, taskUserId);
    //    EmpEvalForScorePo eval = empEvalDao.getEmpEvalForScore(evalRule, taskUser, opEmpId);
    //    Map<String, KpiEmp> empMap = kpiEmpDao.listByEmpAsMap(tenantId, evalRule.getKpiTypes().scoreIds());
    //    ListWrap<EvalScoreResult> wrap = evalRule.getKpiTypes().scoreRs();
    //    eval.buildScoreResult(wrap, empMap);
    //    evalOkrAcl.loadOkrIf(tenantId, eval.okrItems());
    //    FinalWeightSumScore weightSumScore = evalRule.computeFinalScore(taskUser.getFinalItemAutoScore());
    //    eval.buildFinalNodeScore(weightSumScore);
    //    //手机端会直接进入评分页面,需要显示周期时间
    //    Cycle cycle = taskUser.isNewEmp() ? taskRepo.adminTaskAsCycle(tenantId, taskUser.getTaskId())
    //            : cycleDao.findCycle(tenantId, taskUser.getCycleId());
    //    eval.setCycleStart(cycle.getCycleStart());
    //    eval.setCycleEnd(cycle.getCycleEnd());
    //    eval.copyOkrValueForSubmits();
    //    eval.setIsReCommit(empEvalDao.listEvalScoreNode(tenantId.getId(), taskUserId, opEmpId) ? 1 : 0);
    //    return eval;
    //}


    public EmpEvalForScoreV3Po empEvalForScoreV2(TenantId tenantId, String taskUserId, String opEmpId, String reScoreNode) {
        TenantSysConf selfIsCanAutoScoreItem = confDao.findTenantConf(tenantId.getId(), GlobalConfEnum.SELF_IS_CAN_AUTO_SCORE_ITEM.getConfCode(), 0);
        EvalUser eval = userRepo.getBaseTaskUser(tenantId, taskUserId);
        EmpEvalMerge rule = empRuleRepo.getEmpEvalMerge(tenantId, taskUserId, opEmpId, all, 1);
       // Map<String, KpiEmp> empMap = kpiEmpDao.listByEmpAsMap(tenantId, rule.getKpiTypes().scoreIds());
        ListWrap<ScoreReject> rejectGroups = batchScoreEvalMergeDao.listScoreReject(tenantId, Collections.singletonList(taskUserId));
        List<ScoreReject> scoreRejects = rejectGroups.groupGet(taskUserId);
        ScoreReject scoreReject = scoreRejects.isEmpty() ? null : scoreRejects.get(0);
        List<EvalKpi> okItems = rule.okrItems();
        evalOkrAcl.loadOkrIf(tenantId, okItems, eval.getEmpId());
        if (CollUtil.isNotEmpty(okItems)) {
            List<OkrKRPo> okrKRPos = kpiDao.listOkrKRs(tenantId, taskUserId, null);
            ListWrap<OkrKRPo> itemOkrMap = new ListWrap<>(okrKRPos).asMap(OkrKRPo::getKpiItemId);
            for (EvalKpi okItem : okItems) {

                OkrKRPo okrKRPo = itemOkrMap.mapGet(okItem.getKpiItemId());
                if (okrKRPo != null) {
                    okItem.setTarget(okrKRPo.getTargetId(), okrKRPo.getTargetName());
                }
            }
        }

        EmpEvalForScoreV3Po empEval = new EmpEvalForScoreV3Po(rule, eval, opEmpId, reScoreNode,scoreReject,selfIsCanAutoScoreItem);
        //处理暂存的数据回填
        empEvalDao.appendCacheScore(empEval, opEmpId);
        empEval.setIsReCommit(empEvalDao.listEvalScoreNode(tenantId.getId(), taskUserId, opEmpId) ? 1 : 0);

        //手机端会直接进入评分页面,需要显示周期时间
        Cycle cycle = eval.isNewEmp() ? taskRepo.adminTaskAsCycle(tenantId, eval.getTaskId())
                : cycleEvalDao.findCycle(tenantId, eval.getCycleId());

        ScoreRuleSnapMatchDmSvc snapMatch = new ScoreRuleSnapMatchDmSvc(cycle, Arrays.asList(taskUserId));
        snapMatch.onMod(onTaskRepo, onEmpRepo);
        snapMatch.match(Arrays.asList(empEval));

        empEval.setCycleStart(cycle.getCycleStart());
        empEval.setCycleEnd(cycle.getCycleEnd());

//        //是否展示驳回按钮，要求任务上开启设置，并且当前是上级评，并且存在已经评分的自评
//        if (ObjectUtil.isNotNull(rule.getScoreConf()) && rule.getScoreConf().superRejectSelfScoreEnabled() && empEval.getMyTaskScoreTypes().contains("superior_score")) {
//            //自评的条件在里面一层进行判断
//            //只要有一个EmpEvalKpiType，获取alreadyScores,包含node为selfScore的对象 或者 EmpEvalKpiType的items里面存在alreadyNodes包含node为selfScore的对象
//            AtomicBoolean selfScoreFlag = new AtomicBoolean(false);
//            empEval.getWaitScoreTypes().stream()
//                    .flatMap(type -> Stream.concat(
//                            type.getAlreadyScores().stream().map(score -> score.getNode()),
//                            type.getItems().stream()
//                                    .flatMap(item -> item.getAlreadyNodes().stream())
//                                    .map(node -> node.getNode())
//                    ))
//                    .filter(node -> "self_score".equals(node))
//                    .findFirst()
//                    .ifPresent(node -> selfScoreFlag.set(true));
//            empEval.setShowRejectScoreButton(String.valueOf(selfScoreFlag.get()));
//        }
//
//        //当前是否是被驳回的自评，是否展示驳回详情
//        AtomicBoolean showRejectReason = new AtomicBoolean(false);

//        empEval.getWaitScoreTypes().stream()
//                .flatMap(type -> Stream.concat(
//                        type.getWaitScores().stream(),
//                        type.getItems().stream()
//                                .flatMap(item -> item.getWaitScores().stream())
//                ))
//                .filter(BaseTypeScoreResult::isSelfScoreRejected)
//                .findFirst()
//                .ifPresent(result -> showRejectReason.set(true));

//        if (showRejectReason.get()) {
//            if (rejectGroups != null) {
//                ScoreReject scoreReject = rejectGroups.groupGet(empEval.getTaskUserId(), 0);
//                if (scoreReject != null) {
//                    empEval.setRejectReason(scoreReject.getRejectReason());
//                }
//            }
//        }

        return empEval;

    }

    public EmpEvalFoConfirmPo empEvalForConfirm(TenantId tenantId, String taskUserId, String opEmpId) {
        EvalUser taskUser = userRepo.getBaseTaskUser(tenantId, taskUserId);
        //AdminTask adminTask = adminTaskRepo.getAdminTask(tenantId, taskUser.getTaskId());
        EmpEvalMerge empEvalMerge = empRuleRepo.getEmpEvalMerge(tenantId, taskUserId, beforeScore);
        boolean indexCofMdfEmp = tenantSysConfRepo.isOpen(tenantId.getId(), GlobalConfEnum.INDEX_COF_MDF_EMP.getConfCode());
        EmpEvalFoConfirmPo eval = empEvalDao.empEvalForConfirm(empEvalMerge, taskUser, opEmpId, indexCofMdfEmp);
        this.buildConfirmExtData(eval, taskUser, tenantId);
        //处理缓存中的问题数据,IndLevelGroup提交丢失问题 ,  1个月后可去掉
        List<IndLevelGroup> levelGroups = gradeDao.listAllIndLevelGroup(tenantId.getId());
        //复制指标等级组 1个月后可去掉
        eval.copyLevelGroup(levelGroups);
        loadOkrIf(tenantId, eval.okrItems(), eval.refKrItems());
        log.debug("empEvalForConfirm.result:{}", JSONUtil.toJsonStr(eval));
        return eval;
    }

    public Integer getConfirmFlowNodeOrder(TenantId tenantId, String taskUserId, String opEmpId) {
        ListScoreResultWrap confirmRs = empEvalDao.curScoreRss(tenantId.getId(), opEmpId, taskUserId, AuditEnum.CONFIRM_TASK.getScene());
        log.debug("empEvalForConfirm.curOrder:{}", confirmRs.curOrder());
        return confirmRs.curOrder();
    }

    public EmpEvalDetailPo empEvalWithOp(TenantId tenantId, String taskUserId, String taskId, String empId,
                                         String opEmpId, String adminType) {
        EvalUser taskUser = taskUserDao.getBothBaseUser(tenantId.getId(), taskId, empId, taskUserId);
        EmpDetailBuilder detailBuilder = new EmpDetailBuilder(taskUser);
        if (Objects.isNull(taskUser)) {
            throw new KpiI18NException("taskUser is not exist ", "考核任务不存在！taskUserId=" + taskUserId);
        }
        PerfEvaluateTaskBaseDo taskBase = adminTaskDao.findTaskBase(tenantId, taskUser.getTaskId());
        detailBuilder.convertTaskBase(taskBase);
        taskUserId = taskUser.getId();
        List<CompanyMsgCenter> msgs = companyMsgCenterDao.listCompanyMsg(new CompanyMsgQuery(tenantId, null, taskUserId, Arrays.asList()));
        detailBuilder.convertMsgs(msgs);
        detailBuilder.addEmpConvertFunc(p -> detailBuilder.loadV1Reviewers()); //处理一下当前负责人/升级数据有bug
        if (detailBuilder.needCycle()) {
            detailBuilder.convertCycle(cycleEvalDao.findCycle(tenantId, taskUser.getCycleId()));
        }
//        Cycle cycle = taskUser.isNewEmp() ? taskRepo.adminTaskAsCycle(tenantId, taskUser.getTaskId())
//                : cycleDao.findCycle(tenantId, taskUser.getCycleId());
        //查询评分人评分环节节点
        List<EmpEvalScorerNode> scorerNodes = scorerNodeDao.listEmpEvalScorerNodeByScorerId(tenantId.getId(), taskUserId);
        detailBuilder.convertScorerNodes(scorerNodes);
        EmpEvalMerge evalMerge = empRuleRepo.getEvalDetailBuilder(detailBuilder, tenantId, taskUser.getId(), null, type, 1);

        // 组装OKR数据
        detailBuilder.convertGoals(kpiDao.listOkrGoals(tenantId.getId(), taskUserId));
        BindEvalResult bindEvalResult = taskDao.getBindEvalResult(tenantId.getId(), taskUserId);
        List<EmpDetailKpiTypePo> kpiTypePos = kpiDao.loadKpiTypeR1(detailBuilder, evalMerge);
        EmpEvalDetailPo eval = empEvalDao.loadAsEvalDetail(detailBuilder, evalMerge, opEmpId, adminType, kpiTypePos);
        // 查询外部数据
        this.buildExtData(eval, tenantId);

        List<PerfEvaluateTaskAppealBatchDo> appealBatchDos = taskAppealDao.listAppealRs(taskUser.getId(), tenantId);
        detailBuilder.convertAppealBatch(appealBatchDos, evalMerge.getAppealConf(), opEmpId);
//        eval.setHasWaitReadAppeal(taskAppealDao.exitWaitReadAppeal(taskUser.getId(), tenantId));

        try {
            boolean hasInputWaitNotify = adminTaskDao.existsInputNotifyTaskUserId(tenantId.getId(), taskUserId);
            eval.setHasInputWaitNotify(hasInputWaitNotify);
            List<EvalBind> otherEvalBinds = taskDao.getOtherEvalBinds(tenantId.getId(), taskUserId);
            detailBuilder.convertEvalBind(otherEvalBinds);
//        eval.setBindEvalResult(bindEvalResult);
            eval.setArchiveStatus(bindEvalResult.getArchiveStatus());
            detailBuilder.convertHasSubmitedScoreV3();
//        eval.setHasScoreFlag(taskUserDao.hasSubmitedScore(tenantId, taskUserId));
            eval.setDeadLineConf(evalMerge.getDeadLineConf());
            eval.setWasTempTask(taskUser.getTempTask());
            eval.setFinishedValueAuditTransferFlag("允许".equals(evalMerge.getFinishValueAudit().transferFlagName()) ? true : false);
            eval.formatPerfCoefficient();
            eval.appendDeadLineWarn(evalMerge.getDeadLineConf(), evalMerge.getAppealConf());
            eval.appendAppealWarn(evalMerge.getPublishResult().isOpen(), taskUser.getAppealDeadLine(), evalMerge.getAppealConf());
            eval.setDiscussUnreadNum(taskCoachDao.getDiscussUnreadNum(tenantId.getId(), taskUserId, taskUser.getEmpId()));
            eval.setCoachUnreadNum(taskCoachDao.getCoachUnreadNum(tenantId.getId(), taskUserId, taskUser.getEmpId()));
            eval.setConfirmAuditSign(evalMerge.getConfirmTask().confirmAuditSign(opEmpId, eval.getConfirmApproveOrder()));
            //所有emp相关查询后置处理,进行回调.
            List<Employee> employees = deptEmpDao.listByEmpIds(tenantId.getId(), detailBuilder.allEmpIds(), null);
            detailBuilder.convertEmpInfo(employees);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("异常信息:" + e.getMessage());
        }
//        eval.initAppendAppeal(evalMerge.getAppealConf(), taskUser.getCreatedUser());
//        Employee creater = deptEmpDao.findEmployee(tenantId, StrUtil.isEmpty(evalMerge.getInitiator()) ? taskUser.getCreatedUser() : evalMerge.getInitiator());
//        eval.acceptCreater(Objects.nonNull(creater) ? creater.getAvatar() : null, Objects.nonNull(creater) ? creater.getName() : null, taskUser.getCreatedTime());
        OperationLogDo logDo = opLogDao.findLastLog(tenantId, taskUserId, null);
        eval.setLogDo(logDo != null ? ("item_audit_reject".equals(logDo.getBusinessScene()) ? logDo : null) : null);
        loadOkrIf(tenantId, eval.okrItems(), eval.refKrItems());
        // 查询驳回且未最终提交的指标及驳回理由
        loadRejectKpi(eval.getRejectTaskKpiPos(), tenantId.getId(), taskUserId);
        return eval;
    }
    private void loadRejectKpi(List<RejectTaskKpiPo> rejectTaskKpiPos, String tenantId, String taskUserId) {
        List<EvalKpi> evalKpis = kpiDao.listRejectKpiItem(tenantId, taskUserId, 0);
        if (CollUtil.isNotEmpty(evalKpis)) {
            Map<String, List<String>> groupedByAuditReason = evalKpis.stream().collect(Collectors.groupingBy(EvalKpi::getFinishValueAuditReason,
                    Collectors.mapping(
                            EvalKpi::getKpiItemName,
                            Collectors.toList()
                    )));
            groupedByAuditReason.forEach((auditReason, rejectList) -> {
                RejectTaskKpiPo rejectTaskKpiPo = new RejectTaskKpiPo();
                rejectTaskKpiPo.setAuditReason(auditReason);
                rejectTaskKpiPo.setRejectItemList(rejectList);
                rejectTaskKpiPos.add(rejectTaskKpiPo);
            });
        }
    }

    private void loadOkrIf(TenantId tenantId, List<EmpEvalDetailKpiItemPo> okrItems, List<EmpEvalDetailKpiItemPo> refKrItems) {
        CompanyPo company = deptEmpDao.getTenant(tenantId);
        if (CollUtil.isNotEmpty(okrItems)) {
            List<String> actionIds = okrItems.stream().map(EmpEvalDetailKpiItemPo::getActionId).collect(Collectors.toList());
            List<OkrAction> okrActions = evalOkrAcl.loadActionByIds(company.getDingCorpId(), actionIds);
            EmpEvalDetailKpiItemPo kpiItemPo = okrItems.get(0);
            ListWrap<OkrActionUpdate> updateListWrap = evalOkrAcl.listActionUpdateInfo(tenantId, kpiItemPo.getEmpId(), actionIds);
            /**查询成果最新一条更新记录*/
            Map<String, OkrActionUpdate> actionUpdateMap = evalOkrAcl.getActionLastUpdateInfo(company.getDingCorpId(), actionIds);
            Map<String, OkrAction> actionMap = okrActions.stream().collect(Collectors.toMap(OkrAction::getId, Function.identity()));
            for (EmpEvalDetailKpiItemPo okrItem : okrItems) {
                //设置OKR信息
                if (!actionMap.containsKey(okrItem.getActionId())) {
                    continue;
                }
                OkrAction action = actionMap.get(okrItem.getActionId());
                action.splitTargetTag();
                okrItem.setItemFinishValue(action.getFinishValue());
                okrItem.setConfidenceIndex(action.getConfidenceIndex());
                okrItem.acceptOkrValue(action);
                if (actionUpdateMap != null && actionUpdateMap.size() > 0) {
                    if (actionUpdateMap.containsKey(okrItem.getActionId())) {
                        okrItem.setOkrActionUpdate(JSON.parseObject(String.valueOf(actionUpdateMap.get(okrItem.getActionId())), OkrActionUpdate.class));
                    }
                }
                List<OkrActionUpdate> okrActionUpdates = updateListWrap.groupGet(okrItem.getActionId());
                okrItem.setOkrActionUpdates(okrActionUpdates);
            }
        }
        if (CollUtil.isEmpty(refKrItems)) {
            return;
        }
        //在循环外进行批量查询，需要okr那边在OkrAction中增加refId的返回
//        List<String> refIds = refKrItems.stream().map(EmpEvalDetailKpiItemPo::getId).collect(Collectors.toList());
//        List<OkrAction> okrActions = evalOkrAcl.loadActionByItemIds(company.getDingCorpId(), refIds);
//        ListWrap<OkrAction> okrActionMap = new ListWrap<>(okrActions).groupGet(OkrAction::getRefId);

        for (EmpEvalDetailKpiItemPo refKrItem : refKrItems) {
            List<OkrAction> okrActions2 = evalOkrAcl.loadActionByItemId(company.getDingCorpId(), refKrItem.getId());
            if (CollUtil.isEmpty(okrActions2)) {
                continue;
            }
            Map<String, OkrActionUpdate> actionUpdateMap = evalOkrAcl.getActionLastUpdateInfo(company.getDingCorpId(), okrActions2.stream().map(OkrAction::getId).collect(Collectors.toList()));
            for (OkrAction okrAction : okrActions2) {
                okrAction.accpKrScore();
                if (actionUpdateMap != null && actionUpdateMap.size() > 0) {
                    if (actionUpdateMap.containsKey(okrAction.getId())) {
                        okrAction.setOkrActionUpdate(JSON.parseObject(String.valueOf(actionUpdateMap.get(okrAction.getId())), OkrActionUpdate.class));
                    }
                }
            }
            refKrItem.setItemRefKRList(okrActions2);
        }
    }

    public List<OkrActionUpdate> listActionUpdateInfos(OkrQuery query) {
        TenantId tenantId = new TenantId(query.getCompanyId());
//        CompanyDo company = companyDao.getCompany(tenantId);
//        query.setCorpId(company.getDingCorpId());
//        if (StringUtils.isNotBlank(query.getEmpId())) {
//            KpiEmployee emp = empDao.findEmployee(tenantId, query.getEmpId());
//            query.setDingUserId(emp.getDingUserId());
//        }
//        return evalOkrAcl.listActionUpdateInfos(query.getCorpId(), query.getActionId(), query.getDingUserId());
        ListWrap<OkrActionUpdate> updateListWrap = evalOkrAcl.listActionUpdateInfo(tenantId, query.getEmpId(), StrUtil.splitTrim(query.getActionId(), ","));
        return updateListWrap.getDatas();
    }

    //考核任务
    public PagedList<MyEvalTaskPo> pageMyEmpEval(MyEmpEvalQuery query) {
        PagedList<MyEvalTaskPo> pagedList = empEvalDao.pagedMyEmpEval(query);
        if (pagedList.getData() != null && pagedList.getData().size() > 0) {
            pagedList.getData().forEach(task -> {
                publishMask(task, query);
            });
        }
        return pagedList;
    }

    private void publishMask(MyEvalTaskPo task, MyEmpEvalQuery query) {
        task.buildPublish();
        task.setBindEvalResult(taskDao.getBindEvalResult(query.getCompanyId().getId(), task.getTaskUserId()));
    }

    public PagedList<RuleEmpEvalPo> pagedEmpEval(EmpEvalAtTaskQuery3 query) {
        return empEvalDao.pagedEmpEval(query);
    }

    public PagedList<RuleEmpEvalDraftPo> pagedEmpEvalDraft(EmpEvalAtTaskQuery query) {
        query.setAdminOrgIds(kpiOrgDao.listAdminScopePrivOrgIds(query.getTenantId().getId(), query.getOpEmpId(), null));
        if (CollUtil.isNotEmpty(query.getOrgIds())) {
            query.setOrgIds(kpiOrgDao.listAllChildOrgIds(query.getTenantId(), query.getOrgIds()));
        }
        PagedList<RuleEmpEvalDraftPo> ruleEmpEvalDraftPos = empEvalDao.pagedEmpEvalDraft(query);
        List<String> curOrgIds = new ArrayList<>();
        for (RuleEmpEvalDraftPo po : ruleEmpEvalDraftPos) {
            if (StrUtil.isNotBlank(po.matchOrId())) {
                curOrgIds.add(po.matchOrId());
            }
        }
        //显示上级
        if (CollUtil.isNotEmpty(curOrgIds)) {
            List<KpiOrgSupNames> supNames = kpiOrgDao.listOrgSupNames(query.getTenantId(), curOrgIds);
            ruleEmpEvalDraftPos.forEach(r -> r.matchSupNames(supNames));
        }
        return ruleEmpEvalDraftPos;
    }

    public PagedList<SimpleEmpEvalPo> pagedSimpleEmpEval(EmpEvalAtTaskQuery query) {
        return empEvalDao.pagedSimpleEmpEval(query);
    }

    public PagedList<EmpEvalByStatusPo> pagedEmpEvalByStatus(EmpEvalAtTaskQuery2 query) {
        TenantId tenantId = new TenantId(query.getCompanyId());
        query.setAdminOrgIds(kpiOrgDao.listAdminScopePrivOrgIds(query.getCompanyId(), query.getOpEmpId(), null));  //获取管理权限范围
        if (CollUtil.isNotEmpty(query.getOrgIds())) {
            query.setOrgIds(kpiOrgDao.listAllChildOrgIds(new TenantId(query.getCompanyId()), query.getOrgIds()));
        }
        //考核任务权限
        if (query.isPriv()) {
            query.setTaskIds(cycleEvalDao.listTaskPrivByOpEmpId(query.getCompanyId(), query.getCycleId(), query.getOpEmpId()));
        }
        PagedList<EmpEvalByStatusPo> statusPos = empEvalDao.pagedEmpEvalByStatus(query);
        Set<String> empIds = statusPos.stream().map(EmpEvalByStatusPo::getEmpId).collect(Collectors.toSet());
        Map<String, KpiEmp> leavedEmps = kpiEmpDao.listLeavedEmp(tenantId, empIds);
        List<String> taskUserIds = CollUtil.map(statusPos, po -> po.getId(), true);
        ListWrap<ResultAuditReviewers> reviewerMap = auditFlowRepo.reviewersListWrap(tenantId, taskUserIds);
        ListWrap<SummaryScoreReviewer> summaryScoreReviewerMap = scorerSummaryTodoRepo.reviewersListWrap(tenantId, taskUserIds);
        ListWrap<EvalReviewers> notContainResultReviewerMap = empEvalDao.listWrapLoadReviewers(tenantId, taskUserIds, true);
        Map<String, BindEvalResult> bindEvalResultMap = taskDao.getBindEvalResultAsMap(tenantId.getId(), taskUserIds);
        Map<String, TaskAppealPo> poMap = taskAppealDao.listAppealRsByTaskId(taskUserIds, new TenantId(query.getCompanyId()));
        List<String> curOrgIds = new ArrayList<>();
        for (EmpEvalByStatusPo po : statusPos) {
            po.setLeaved(leavedEmps.containsKey(po.getEmpId()));
            po.builderReviewersResult(reviewerMap.groupGet(po.getId()));
            //不包含校準的責任人處理
            po.builderReviewers(new ArrayList<>(notContainResultReviewerMap.groupGet(po.getId())));
            //处理汇总评分下的责任人
            po.builderReviewersWithSummaryScore(summaryScoreReviewerMap.groupGet(po.getId()));
            TaskAppealPo appealPo = poMap.get(po.getTaskUserId());
            if (Objects.nonNull(appealPo)) {
                po.setAppealStatus(appealPo.getAppealStatus());
            }
//            List<String> reviewerIds = empEvalDao.loadReviewers(tenantId, po.getTaskUserId());
//            List<KpiEmp> reviewers = kpiEmpDao.listByEmp(tenantId, reviewerIds);
//            po.setReviewersEmp(reviewers);
            //po.setBindEvalResult(taskDao.getBindEvalResult(query.getCompanyId(), po.getTaskUserId()));
            if (CollUtil.isNotEmpty(bindEvalResultMap)) {
                po.setBindEvalResult(bindEvalResultMap.get(po.getId()));
            }
            //po.setAuditFlows(getScoreAuditFlows(tenantId, po.getTaskUserId(), po.getTaskId(), po.getEmpId(), po.getEmpName()));

            po.scoreNodeSort();

            if (StrUtil.isNotBlank(po.matchOrId())) {
                curOrgIds.add(po.matchOrId());
            }
        }

        //显示上级
        if (CollUtil.isNotEmpty(curOrgIds)) {
            List<KpiOrgSupNames> supNames = kpiOrgDao.listOrgSupNames(new TenantId(query.getCompanyId()), curOrgIds);
            statusPos.forEach(r -> r.matchSupNames(supNames));
        }
        return statusPos;
    }

    public List<String> listRuleNames(RuleNameAtTaskQuery query) {
        query.setAdminOrgIds(kpiOrgDao.listAdminScopePrivOrgIds(query.getCompanyId(), query.getOpEmpId(), null));  //获取管理权限范围
        return empEvalDao.listRuleNames(query);
    }

    public List<EvalGroupOfTaskResult> listEvalGroupNames(EvalGroupNameAtTaskQuery query) {
        query.setAdminOrgIds(kpiOrgDao.listAdminScopePrivOrgIds(query.getCompanyId(), query.getOpEmpId(), null));  //获取管理权限范围
        return empEvalDao.listEvalGroupNames(query);
    }

    private void buildItemExtData(EvalKpiPo itemDetail, TenantId tenantId) {
        PerfEvaluateTaskBaseDo taskBase = adminTaskDao.findTaskBase(tenantId, itemDetail.getTaskId());
        List<ExtDataItemFieldCorr> itemFieldCorr = itemDetail.getItemFieldCorr();
        if (ObjectUtil.isNull(itemDetail.getItemFinishValue()) && ObjectUtil.isNull(itemDetail.getItemFinishValueText())) {
            if (CollUtil.isNotEmpty(itemFieldCorr)) {
                for (ExtDataItemFieldCorr corr : itemFieldCorr) {
                    ExtDataSyncPo extData = syncDao.getExtData(tenantId.getId(), corr.getExtDataFieldId(), itemDetail.getEmpId(),
                            taskBase.getCycleStartDate(), taskBase.getCycleEndDate());
                    if (ObjectUtil.isNotNull(extData)) {
                        JSONObject jsonObject = JSONObject.parseObject(extData.getExtData(), JSONObject.class);
                        jsonObject.getString(corr.getExtDataFieldParamEnName());
                        if (StrUtil.equals("finishValue", corr.getKpiItemFieldId())) {
                            String value = jsonObject.getString(corr.getExtDataFieldParamEnName());
                            if (ObjectUtil.isNotNull(value) && StringTool.isNumber(value)) {
                                itemDetail.setItemFinishValue(new BigDecimal(value));
                            } else {
                                itemDetail.setItemFinishValueText(value);
                            }
                        }
                    }
                }
            }
        }
    }

    private void buildExtData(EmpEvalDetailPo eval, TenantId tenantId) {
        String taskId = eval.getTaskBaseId();
        PerfEvaluateTaskBaseDo taskBase = adminTaskDao.findTaskBase(tenantId, taskId);
        List<EmpEvalDetailKpiItemPo> evalKpis = eval.allKpiItems();
        for (EmpEvalDetailKpiItemPo item : evalKpis) {
            // 考核指标完成值为空查询外部数据
            if (ObjectUtil.isNull(item.getItemFinishValue()) && ObjectUtil.isNull(item.getItemFinishValueText())) {
                List<ExtDataItemFieldCorr> itemFieldCorr = item.getItemFieldCorr();
                if (CollUtil.isNotEmpty(itemFieldCorr)) {
                    for (ExtDataItemFieldCorr corr : itemFieldCorr) {
                        ExtDataSyncPo extData = syncDao.getExtData(tenantId.getId(), corr.getExtDataFieldId(), eval.getEmpId(),
                                taskBase.getCycleStartDate(), taskBase.getCycleEndDate());
                        if (ObjectUtil.isNotNull(extData)) {
                            JSONObject jsonObject = JSONObject.parseObject(extData.getExtData(), JSONObject.class);
                            jsonObject.getString(corr.getExtDataFieldParamEnName());
                            if (StrUtil.equals("finishValue", corr.getKpiItemFieldId())) {
                                String value = jsonObject.getString(corr.getExtDataFieldParamEnName());
                                if (ObjectUtil.isNotNull(value) && StringTool.isNumber(value)) {
                                    item.setItemFinishValue(new BigDecimal(value));
                                } else {
                                    item.setItemFinishValueText(value);
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    private void buildConfirmExtData(EmpEvalFoConfirmPo eval, EvalUser taskUser, TenantId tenantId) {
        PerfEvaluateTaskBaseDo taskBase = adminTaskDao.findTaskBase(tenantId, taskUser.getTaskId());
        List<EmpEvalDetailKpiItemPo> evalKpis = eval.allKpiItems();
        for (EmpEvalDetailKpiItemPo item : evalKpis) {
            // 考核指标完成值为空查询外部数据
            if (ObjectUtil.isNull(item.getItemFinishValue()) && ObjectUtil.isNull(item.getItemFinishValueText())) {
                List<ExtDataItemFieldCorr> itemFieldCorr = item.getItemFieldCorr();
                if (CollUtil.isNotEmpty(itemFieldCorr)) {
                    for (ExtDataItemFieldCorr corr : itemFieldCorr) {
                        ExtDataSyncPo extData = syncDao.getExtData(tenantId.getId(), corr.getExtDataFieldId(), taskUser.getEmpId(),
                                taskBase.getCycleStartDate(), taskBase.getCycleEndDate());
                        if (ObjectUtil.isNotNull(extData)) {
                            JSONObject jsonObject = JSONObject.parseObject(extData.getExtData(), JSONObject.class);
                            jsonObject.getString(corr.getExtDataFieldParamEnName());
                            if (StrUtil.equals("finishValue", corr.getKpiItemFieldId())) {
                                String value = jsonObject.getString(corr.getExtDataFieldParamEnName());
                                if (ObjectUtil.isNotNull(value) && StringTool.isNumber(value)) {
                                    item.setItemFinishValue(new BigDecimal(value));
                                } else {
                                    item.setItemFinishValueText(value);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    public Cycle getINewestCycle(MyManagerEmpEvalQuery query) {
        query = managerPrivDao.builderManagerPriv(query);
        return empEvalDao.getINewestCycle(query);
    }

    public PagedList<MyManagerEmpEval> pagedIManageEmpEval(MyManagerEmpEvalQuery query) {
        TenantId tenantId = new TenantId(query.getCompanyId());
        if (CollUtil.isNotEmpty(query.getOrgIds())) {
            query.setOrgIds(kpiOrgDao.listAllChildOrgIds(new TenantId(query.getCompanyId()), query.getOrgIds()));
        }
        query = managerPrivDao.builderManagerPriv(query);
        PagedList<MyManagerEmpEval> empEvals = empEvalDao.pagedIManageEmpEval(query);
        ListWrap<EvalReviewers> reviewerMap = empEvalDao.listWrapLoadReviewers(tenantId, CollUtil.map(empEvals, po -> po.getId(), true));
        for (MyManagerEmpEval po : empEvals) {
            po.setReviewersEmp(new ArrayList<>(reviewerMap.groupGet(po.getId())));
            //考核状态在校准之前的都不显示分数，等级，系数
            if (TalentStatus.isBefore(po.getTaskStatus(), TalentStatus.RESULTS_AUDITING.getStatus())) {
                po.setFinalScore(null);
                po.setEvaluationLevel(null);
                po.setPerfCoefficient(null);
            }
        }
        return empEvals;
    }

    public List<OpenMyTeamEvalPo> listNewestMyTeamEmpEval(MyManagerEmpEvalQuery query) {
        ManagerPrivPo priv = managerPrivDao.findByEmpId(query.getCompanyId(), query.getOpEmpId());
        if (priv == null || !priv.wasOpen()) {
            return new ArrayList<>();
        }
        query = managerPrivDao.builderManagerPriv(query);
        List<OpenMyTeamEvalPo> openManagEvalPos = empEvalDao.listNewestIManageEval(query);
        return openManagEvalPos;
    }

    public AdminTaskStatusCnt getIManageEmpEvalStatusCnt(MyManagerEmpEvalQuery query) {
        if (CollUtil.isNotEmpty(query.getOrgIds())) {
            query.setOrgIds(kpiOrgDao.listAllChildOrgIds(new TenantId(query.getCompanyId()), query.getOrgIds()));
        }
        ManagerPrivPo priv = managerPrivDao.findByEmpId(query.getCompanyId(), query.getOpEmpId());
        if (priv == null || !priv.wasOpen()) {
            return new AdminTaskStatusCnt();
        }
        query = managerPrivDao.builderManagerPriv(query);
        return empEvalDao.getIManageEmpEvalStatusCnt(query);
    }


    public List<ResultAuditRecordPo> listResultAuditRecord(TenantId tenantId, String taskUserId) {
        return evalTaskDao.listResultAuditRecord(tenantId, taskUserId);
    }

    //public String getHistoryScore(TenantId tenantId, String taskUserId) {
    //    return evalTaskDao.getHistoryScore(tenantId, taskUserId);
    //}

    public EvalNodeFlagPo scoreFlags(TenantId companyId, String taskUserId) {
        //CycleEval task = evalTaskDao.getMergeTaskBase(companyId, taskUserId);
        //EvalUser taskUser = userRepo.getTaskUser(companyId, taskUserId);
        //List<EvalKpi> kpiList = taskUser.getKpis();
        EmpEvalMerge empEvalMerge = empRuleRepo.getEmpEvalMerge(companyId, taskUserId, item | itemRule);
        KpiListWrap kpiTypes = empEvalMerge.getKpiTypes();
        EvalNodeFlagPo nodeFlag = new EvalNodeFlagPo();
        if (CollectionUtils.isEmpty(kpiTypes.getDatas())) {
            return nodeFlag;
        }
        kpiTypes.getDatas().stream().flatMap(type -> type.getItems().stream()).forEach(evalKpi -> {
            if (BusinessConstant.KPI_TYPE_CLASSIFY_PLUS.equals(evalKpi.getKpiTypeClassify())) { //是否有加分
                nodeFlag.setPlusScoreFlag(Boolean.TRUE.toString());
            }
            if (BusinessConstant.KPI_TYPE_CLASSIFY_SUBTRACT.equals(evalKpi.getKpiTypeClassify())) {//是否有减分
                nodeFlag.setSubtractScoreFlag(Boolean.TRUE.toString());
            }
            if (BusinessConstant.APPROVER_TYPE_USER.equals(evalKpi.getScorerType())) {
                nodeFlag.setItemScoreFlag(Boolean.TRUE.toString());
            }
            if (BusinessConstant.SCORER_TYPE_AUTO.equals(evalKpi.getScorerType())) {
                nodeFlag.setAutoScoreFlag(Boolean.TRUE.toString());
            }
        });
        EvalScorersWrap evalScorersWrap = empEvalMerge.getEvalScorersWrap();
        ListWrap<EmpEvalScorerNode> nodeWrap = evalScorersWrap.listSNExcludeTotalLevel();
        nodeFlag.setSelfScoreFlag(evalScorersWrap.openScorerNode(SubScoreNodeEnum.SELF_SCORE.getScene(), nodeWrap) + "");
        nodeFlag.setPeerScoreFlag(evalScorersWrap.openScorerNode(SubScoreNodeEnum.PEER_SCORE.getScene(), nodeWrap) + "");
        nodeFlag.setSubScoreFlag(evalScorersWrap.openScorerNode(SubScoreNodeEnum.SUB_SCORE.getScene(), nodeWrap) + "");
        nodeFlag.setSuperiorScoreFlag(evalScorersWrap.openScorerNode(SubScoreNodeEnum.SUPERIOR_SCORE.getScene(), nodeWrap) + "");
        nodeFlag.setAppointScoreFlag(evalScorersWrap.openScorerNode(SubScoreNodeEnum.APPOINT_SCORE.getScene(), nodeWrap) + "");
        nodeFlag.setItemScoreFlag(evalScorersWrap.openScorerNode(SubScoreNodeEnum.ITEM_SCORE.getScene(), nodeWrap) + "");//指定评分
//        ScoreSceneWrap scoreSceneWrap = kpiTypes.buildNodes();
//        nodeFlag.setSelfScoreFlag(scoreSceneWrap.onOpenNode(SubScoreNodeEnum.SELF_SCORE) + "");
//        nodeFlag.setPeerScoreFlag(scoreSceneWrap.onOpenNode(SubScoreNodeEnum.PEER_SCORE) + "");
//        nodeFlag.setSubScoreFlag(scoreSceneWrap.onOpenNode(SubScoreNodeEnum.SUB_SCORE) + "");
//        nodeFlag.setSuperiorScoreFlag(scoreSceneWrap.onOpenNode(SubScoreNodeEnum.SUPERIOR_SCORE) + "");
//        nodeFlag.setAppointScoreFlag(scoreSceneWrap.onOpenNode(SubScoreNodeEnum.APPOINT_SCORE) + "");
        //是否有指定评分
        return nodeFlag;
    }

    public EmpEvalRuleFlow getEmpEvalRuleFlow(TenantId tenantId, String taskUserId) {
        EvalUser evalUser = userRepo.getTaskUser(tenantId, taskUserId);
        if (evalUser.hasEmpEvalRule()) {//新数据
            return userRepo.getEmpEvalRuleFlow(tenantId, taskUserId, evalUser);
        }
        AdminTask adminTask = adminTaskRepo.getAdminTask(tenantId, evalUser.getTaskId());
        EmpEvalRuleFlow ruleFlow = new EmpEvalRuleFlow();
        if (evalUser.wasTempTask()) {
            //历史任务 展示考核流程
            ruleFlow = Convert.convert(EmpEvalRuleFlow.class, adminTask);
            List<String> raterIds = evalDmSvc.parseRaters(ruleFlow, tenantId, new EmpId(evalUser.getEmpId()), new EmpId(evalUser.getCreatedUser()));
            List<KpiEmp> kpiEmps = kpiEmpDao.listByEmp(tenantId, raterIds);
            ruleFlow.matchRaterBase(kpiEmps);
            ruleFlow.getDeadLineConf().initTaskResultInterview();
            return ruleFlow;
        }
        List<String> raterIds = evalDmSvc.mergeEvalFlowConf(adminTask, evalUser, ruleFlow);
        List<KpiEmp> kpiEmps = kpiEmpDao.listByEmp(tenantId, raterIds);
        ruleFlow.matchRaterBase(kpiEmps);
        ruleFlow.getDeadLineConf().initTaskResultInterview();
        return ruleFlow;
    }

    //历史考核任务无法编辑只能查看
    @Transactional
    public TalentEvalConfirmedEvent editScoreStageConf(ScoreStageRuleCmd cmd) {
        ScoreStageRule scoreStageRule = cmd.getScoreStageRule();
        EvalUser evalUser = userRepo.getBaseTaskUser(cmd.getTenantId(), cmd.getScoreStageRule().getEmpEvalId());
        if (evalUser.afterStage(TalentStatus.SCORING) || evalUser.eqStage(TalentStatus.SCORING)) {
            throw new KpiI18NException("10003", "员工任务该阶段无法修改配置");//员工任务该阶段无法修改配置
        }
        CompanyConf conf = companyDao.findCompanyConf(cmd.getTenantId());
        AdminTask task = adminTaskRepo.getAdminTask(cmd.getTenantId(), evalUser.getTaskId());
        evalUser.checkExistEvalEmp();
        EmpEvalRule before = empRuleRepo.getEmpEvalRule(evalUser.getCompanyId(), evalUser.getId());
        if(itemFinishValueChanged(scoreStageRule.getKpiTypes(),before.getKpiTypes().getDatas())){
            throw new KpiI18NException("item_finish_value_changed", "编辑期间指标完成值已被更新,请重新加载");
        }
        EmpEvalRule after = new EmpEvalRule();
        BeanUtils.copyProperties(before, after);
        after.setScoreSortConf(task.getScoreSortConf());
        after.reSetConf(scoreStageRule, cmd.getTaskId(), evalUser.getEmpId(), cmd.getOpEmpId());
        initAllowSelfAsPeer(cmd.getTenantId(), after);//初始化公司配置 是否允许被考核人作为互评人
        if (cmd.getOpType() == 1) {
            evalUser.setEmpEvalRule(after);
            //重新应用
            after.setInitiator(null);
            evalDmSvc.mergeConf(task, evalUser, cmd.getOpEmpId());
        }
        evalUser.setRuleConfStatus(scoreStageRule.checkRaters());
        List<IndLevelGroup> levelGroups = gradeDao.listAllIndLevelGroup(cmd.getTenantId().getId());
        //复制指标等级组
        after.copyLevelGroup(levelGroups);
        //经营计划指标目标值只有一份时,默认选中
        List<BusinessPlanItem> planItemList = this.listBusinessPlanItem(cmd.getTenantId().getId(), evalUser.getId(), after.getPlanItemIds());
        log.info("经营计划指标目标值，taskUserIds={}", JSONObject.toJSONString(planItemList));
        after.initOkrGoalId(planItemList);
        if (cmd.isParseAudit()) {
            evalDmSvc.parseScoreRaters(evalUser, after, new EmpId(cmd.getOpEmpId()));
        }
        empRuleRepo.editScoreStageConf(after, evalUser.getTaskId(), evalUser.getEmpId(), null , cmd.getOpType() == 1 ? true : false, evalUser);
        //生成360问卷考核实例
        EvalUser user = new ToDataBuilder<>(evalUser, EvalUser.class).data();
        user.setTaskName(task.getTaskName());
        user.setEmpEvalRule(after);
        askEvalAcl.createdAsk360Eval(user, cmd.getTenantId().getId(), cmd.getOpEmpId());
        taskKpiRepo.changedAskTypeEvalId(user.getEmpEvalRule().listAsk360Types(), cmd.getTenantId().getId(), cmd.getOpEmpId());
        if (Objects.isNull(evalUser.getResultSendNotify())) {
            evalUser.setResultSendNotify(task.getResultSendNotify());
        }
        // 如果当前是执行环节，判断是否有变更录入人。发送通知待办
        if (Objects.equals(evalUser.getTaskStatus(), TalentStatus.CONFIRMED.getStatus())) {
            List<String> curInputEmpIds = after.getResultInputEmpPackage().getCurInputEmpIds();
            List<String> oldInputEmpIds = after.getResultInputEmpPackage().getOldInputEmpIds();
            List<String> resultInputEmpIds = after.getResultInputEmpPackage().getResultInputEmpIds();
            log.info("完成值录入人变化,curInputEmpIds:{},oldInputEmpIds:{},resultInputEmpIds:{}", curInputEmpIds, oldInputEmpIds, resultInputEmpIds);
            List<CompanyMsgCenter> msgCenters = companyMsgCenterDao.listNotHandleMsg(cmd.getTenantId(), null, user.getId(), MsgSceneEnum.TASK_SUBMIT_PROGRESS.getType());
            List<String> sendMsgNotHandlerInputEmpIds = after.extractEmpIds(msgCenters);
            log.info("待办完成值录入人,sendMsgNotHandlerInputEmpIds:{}", sendMsgNotHandlerInputEmpIds);
            List<String> differenceInputEmpIds = after.findDifference(sendMsgNotHandlerInputEmpIds, resultInputEmpIds);
            if (CollUtil.isNotEmpty(differenceInputEmpIds)) {
                oldInputEmpIds.addAll(differenceInputEmpIds);
                log.info("待办完成值录入人,differenceInputEmpIds:{},oldInputEmpIds:{}", differenceInputEmpIds, oldInputEmpIds);
            }
            // 更新负责人信息
            List<KpiEmp> reviewers = kpiEmpDao.listByEmp(cmd.getTenantId(), resultInputEmpIds);
            evalUser.setReviewersJson(reviewers);
            if (oldInputEmpIds.size() > 0) {
                //清除待办
                new CancelTodoEvent(cmd.getTenantId(), oldInputEmpIds,
                        evalUser.getId(), MsgSceneEnum.TASK_SUBMIT_PROGRESS.getType()).publish();
            }
            if (curInputEmpIds.size() > 0) {
                List<String> differenceCurInputEmpIds = after.findDifference(curInputEmpIds, sendMsgNotHandlerInputEmpIds);
                log.info("当前完成值录入人，需发送待办,differenceCurInputEmpIds:{}", differenceCurInputEmpIds);
                evalUser.setInputFinishStatus(InputFinishStatusEnum.NO_INPUT.getStatus());//未录入
                //发送待办
                if (differenceCurInputEmpIds.size() > 0) {
                    new EvalInputNotifySend(task.getInputNotifyConf(), evalUser, new Name(task.getTaskName()), new HashSet<>(differenceCurInputEmpIds)).publish();
                }
            }
            if (CollUtil.isEmpty(resultInputEmpIds)) {
                evalUser.setInputFinishStatus(InputFinishStatusEnum.UN_NEED_INPUT.getStatus());//如果没有录入人，则不需要录入
            }
            //清除设置同级评待办
            new CancelTodoEvent(cmd.getTenantId(), evalUser.getId(), MsgSceneEnum.SET_MUTUAL_AUDIT.getType()).publish();
        }
        if (evalUser.afterStage(TalentStatus.PUBLISHED)) {//待发起的考核不进行自动计算
            //预防更新了考核表的自动计算指标，这里重新计算下，自动计算的最终分值。
            evalUser.tryComputeAutoScore(after.getTypeWeightConf().isOpen(), after.getScoreValueConf().getCustomFullScore()
                    , after.getScoreValueConf().isFullScoreRange(), conf.openItemAutoScoreMultiplWeight());
            log.info("===========finalItemAutoScore:{}", evalUser.getFinalItemAutoScore());
            if (null == evalUser.getFinalItemAutoScore()) {//如果为空需要设置个值，不然update不会拼接更新值
                evalUser.setFinalItemAutoScore(BigDecimal.ZERO);
            }
        }

        Integer inputFinishStatus = after.computeInputFinishStatus(evalUser.getTaskStatus());
        evalUser.updateInputFinishStatus(inputFinishStatus);

        userRepo.updateTaskUser(evalUser);
        new LogDiffAdminTask(cmd.getOpEmpId(), evalUser, before, after, cmd.getOpAdminType()).publish();
        /**处理异常*/
        evalUser.setEmpEvalRule(after);

        if (evalUser.afterStage(TalentStatus.PUBLISHED)) {
            //环节同时评，并且上级同时评时，并且开启了汇总发送待办，处于考核发起后阶段编辑才预派发
            EmpEvalMerge evalMerge = empRuleRepo.getEmpEvalMerge(user.getCompanyId(), user.getId(), all);
            ScorerTodoDmSvc todoDmSvc = new ScorerTodoDmSvc(evalMerge, user);
            //先移除，再添加，对受影响的评分人重新汇总
            Set<String> scorerIds = scorerSummaryTodoRepo.removeScorerByTaskUser(evalUser.getCompanyId().getId(), evalUser.getId());
            if (todoDmSvc.support()) {
                ChainDispatchRs chainDispatchRs = todoDmSvc.preDispatch(user);
                scorerIds.addAll(scorerSummaryTodoRepo.saveScorerInstance(chainDispatchRs, user, evalMerge));
                todoDmSvc.setRepo(scorerSummaryTodoRepo);
                todoDmSvc.refreshSummary(scorerIds);
                List<ScorerTodoSummary> addSummaries = todoDmSvc.getAddSummaries();
                List<ScorerTodoSummary> updateSummaries = todoDmSvc.getUpdateSummaries();
                if (!addSummaries.isEmpty()) {
                    scorerSummaryTodoRepo.batchAddSummaries(addSummaries);
                    for (ScorerTodoSummary addSummary : addSummaries) {
                        if (addSummary.readyToSend()) {
                            List<TaskUserScorer> scorers = scorerSummaryTodoRepo.queryNeedSendSystemTodoScorers(addSummary.getCompanyId(), addSummary.getTaskId(), addSummary.getScorerId());
                            new ScoreSummaryMsgTodoEvent(addSummary.getCompanyId(), scorers, addSummary).publish();
                        }
                    }
                }
                if (!updateSummaries.isEmpty()) {
                    scorerSummaryTodoRepo.batchUpdateSummaries(updateSummaries);
                    for (ScorerTodoSummary updateSummary : updateSummaries) {
                        if (updateSummary.readyToSend()) {
                            List<TaskUserScorer> scorers = scorerSummaryTodoRepo.queryNeedSendSystemTodoScorers(updateSummary.getCompanyId(), updateSummary.getTaskId(), updateSummary.getScorerId());
                            new ScoreSummaryMsgTodoEvent(updateSummary.getCompanyId(), scorers, updateSummary).publish();
                        }
                    }
                }


            }
        }

        new EvalTaskErrorEvent(cmd.getTenantId().getId(), evalUser).publish();
        //考核规则变更事件
        new EvalRuleChanged(cmd.getTenantId(),cmd.getOpEmpId(), evalUser,after).fire();
        //给被指定人、主管、角色设置互评人重新发送待办通知
        return new TalentEvalConfirmedEvent(new Tenant(evalUser.getCompanyId().getId()), task.getTaskName(),
                evalUser.getTaskId(), evalUser.getEmpId(), evalUser.getId(), after);
    }

    /**
     *
     * @param edit 编辑的指标
     * @param old  旧的指标
     * @return 是否完成值被修改
     */
    private boolean itemFinishValueChanged(List<EmpEvalKpiType> edit, List<EmpEvalKpiType> old) {

        // 从旧指标中找出已经存在完成值的指标
        List<EvalKpi> oldItemsWithFinishValue = old.stream()
                .flatMap(kpiType -> kpiType.getItems().stream())
                .filter(item -> !item.isNotInputVal())
                .collect(Collectors.toList());

        // 如果没有旧的完成值指标，直接返回false
        if (CollUtil.isEmpty(oldItemsWithFinishValue)) {
            return false;
        }

        // 创建一个映射，以便快速查找旧指标项
        Map<String, EvalKpi> oldItemMap = oldItemsWithFinishValue.stream()
                .collect(Collectors.toMap(
                        item -> item.getKpiItemId(),
                        item -> item,
                        (existing, replacement) -> existing
                ));

        // 检查编辑的指标中是否有完成值发生变化
        for (EmpEvalKpiType kpiType : edit) {
            for (EvalKpi item : kpiType.getItems()) {
                String key = item.getKpiItemId();
                EvalKpi oldItem = oldItemMap.get(key);

                // 如果找到了对应的旧指标项，检查完成值是否发生变化
                if (oldItem != null && item.isInputFinishChanged(createFinishValue(oldItem))) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 创建FinishValue对象用于比较
     */
    private FinishValue createFinishValue(EvalKpi item) {
        FinishValue finishValue = new FinishValue();
        finishValue.setItemFinishValue(item.getItemFinishValue());
        finishValue.setItemFinishValueText(item.getItemFinishValueText());
        finishValue.setWorkItemFinishValue(item.getWorkItemFinishValue());
        return finishValue;
    }

    @Transactional
    public void editConfirmConf(AffirmTaskConfCmd cmd) {
        EvalUser evalUser = userRepo.getBaseTaskUser(cmd.getTenantId(), cmd.getEmpEvalId());
        if (evalUser.wasTempTask()) {
            throw new KpiI18NException("error.empEval.editConf.10001");//历史任务不支持修改
        }
        if (evalUser.afterStage(TalentStatus.CONFIRMED)) {
            throw new KpiI18NException("error.empEval.editConf.10003");//员工任务该阶段无法修改配置
        }
        EmpEvalRule rule = empRuleRepo.getEmpEvalRule(cmd.getTenantId(), cmd.getEmpEvalId());
        AffirmTaskConf before = rule.getConfirmTask();
        AffirmTaskConf after = cmd.getAffirmTaskConf();
        if (cmd.isParseAudit()) {
            evalDmSvc.parseConfirmTask(after, evalUser, cmd.getOpEmpId());
        }
        rule.setConfirmTask(after);
        rule.setEditStatus(rule.getEditStatus() + 1);
        rule.builderRaterSkipType(AuditEnum.CONFIRM_TASK.getScene());
        Integer confStatus = rule.reChekConfStatus();
        //重新检查配置
        evalUser.setRuleConfStatus(confStatus);
        empRuleRepo.editConfirmConf(rule, evalUser.getTaskId(), evalUser.getEmpId());
        userRepo.updateTaskUser(evalUser);
        new LogDiffAdminTask(cmd.getOpEmpId(), evalUser, before, after, cmd.getOpAdminType()).publish();
        /**处理异常*/
        evalUser.setEmpEvalRule(rule);
        new EvalTaskErrorEvent(cmd.getTenantId().getId(), evalUser).publish();
    }

    @Transactional
    public void editExeIndiConf(EditExeIndiConfCmd cmd) {
        EvalUser evalUser = userRepo.getBaseTaskUser(cmd.getTenantId(), cmd.getEmpEvalId());
        if (evalUser.wasTempTask()) {
            throw new KpiI18NException("error.empEval.editConf.10001");//历史任务不支持修改
        }
//        if (evalUser.afterStage(TalentStatus.CONFIRMED)) {
//            throw new KpiI18NException("error.empEval.editConf.10003");//员工任务该阶段无法修改配置
//        }
        EmpEvalRule rule = empRuleRepo.getEmpEvalRule(cmd.getTenantId(), cmd.getEmpEvalId());
        EditExeIndiConf before = rule.getEditExeIndi();
        EditExeIndiConf after = cmd.getEditExeIndiConf();
        if (cmd.isParseAudit()) {
            evalDmSvc.parseEditExeIndi(after, evalUser, cmd.getOpEmpId());
        }
        rule.setEditExeIndi(after);
        rule.setEditStatus(rule.getEditStatus() + 2);
        rule.builderRaterSkipType(AuditEnum.EDIT_EXE_INDI.getScene());
        Integer confStatus = rule.reChekConfStatus();
        evalUser.setRuleConfStatus(confStatus);

        empRuleRepo.editExeIndiConf(rule, evalUser.getTaskId(), evalUser.getEmpId());
        userRepo.updateTaskUser(evalUser);
        new LogDiffAdminTask(cmd.getOpEmpId(), evalUser, before, after, cmd.getOpAdminType()).publish();
        /**处理异常*/
        evalUser.setEmpEvalRule(rule);
        new EvalTaskErrorEvent(cmd.getTenantId().getId(), evalUser).publish();
    }

    @Transactional
    public void editFinishedValueAuditConf(FinishedValueAuditConfCmd cmd) {
        EvalUser evalUser = userRepo.getBaseTaskUser(cmd.getTenantId(), cmd.getEmpEvalId());
        if (evalUser.wasTempTask()) {
            throw new KpiI18NException("error.empEval.editConf.10001");//历史任务不支持修改
        }
        if (evalUser.afterStage(TalentStatus.FINISH_VALUE_AUDIT)) {
            throw new KpiI18NException("error.empEval.editConf.10003");//员工任务该阶段无法修改配置
        }
        AdminTask adminTask = adminTaskRepo.getAdminTask(cmd.getTenantId(), evalUser.getTaskId());
        EmpEvalRule rule = empRuleRepo.getEmpEvalRule(cmd.getTenantId(), cmd.getEmpEvalId());
        rule.setScoreSortConf(adminTask.getScoreSortConf());
        FinishValueAuditConf before = rule.getFinishValueAudit();
        FinishValueAuditConf after = cmd.getFinishValueAudit();
        if (cmd.isParseAudit()) {
            evalDmSvc.parseFinishValueAudit(after, evalUser, cmd.getOpEmpId(), cmd.getItemFinishValueAudits());
        }
        rule.setFinishValueAudit(after);
        rule.setEditStatus(rule.getEditStatus() + 1);
        empRuleRepo.editFinishedValueAuditConf(rule, evalUser.getTaskId(), evalUser.getEmpId(), cmd.getItemFinishValueAudits());
        userRepo.updateTaskUser(evalUser);
        if (CollUtil.isNotEmpty(cmd.getItemFinishValueAudits())) {
            taskKpiRepo.inputItems(cmd.getItemFinishValueAudits());
        }
        new LogDiffAdminTask(cmd.getOpEmpId(), evalUser, before, after, cmd.getOpAdminType()).publish();
        /**处理异常*/
        evalUser.setEmpEvalRule(rule);
        new EvalTaskErrorEvent(cmd.getTenantId().getId(), evalUser).publish();
    }

    @Transactional
    public void editAuditResultConf(AuditResultConfCmd cmd) {
        EvalUser evalUser = userRepo.getBaseTaskUser(cmd.getTenantId(), cmd.getEmpEvalId());
        if (evalUser.wasTempTask()) {
            throw new KpiI18NException("error.empEval.editConf.10001");//历史任务不支持修改
        }
        if (evalUser.afterStage(TalentStatus.RESULTS_AUDITING)) {
            throw new KpiI18NException("error.empEval.editConf.10003");//员工任务该阶段无法修改配置
        }
        EmpEvalRule rule = empRuleRepo.getEmpEvalRule(cmd.getTenantId(), cmd.getEmpEvalId());
        AuditResultConf before = rule.getAuditResult();
        AuditResultConf after = cmd.getAuditResultConf();
        if (cmd.isParseAudit()) {
            evalDmSvc.parseAuditResult(after, evalUser, cmd.getOpEmpId());
        }
        rule.setAuditResult(after);
        rule.setEditStatus(rule.getEditStatus() + 4);
        rule.builderRaterSkipType(AuditEnum.FINAL_RESULT_AUDIT.getScene());
        Integer confStatus = rule.reChekConfStatus();
        evalUser.setRuleConfStatus(confStatus);

        empRuleRepo.editAuditResultConf(rule, evalUser.getTaskId(), evalUser.getEmpId());
        userRepo.updateTaskUser(evalUser);
        //生成校准审批流程实例
        if (Objects.equals(cmd.getAuditResultConf().getCollectSendNotify(), 2)) {
            ResultAuditFlow flow = new ResultAuditFlow(cmd.getTenantId().getId(), cmd.getOpEmpId());
            evalUser.setEmpEvalRule(rule);
            auditFlowRepo.saveAuditFlow(flow, evalUser);
            ResultAuditDmSvc dmSvc = new ResultAuditDmSvc(evalUser, null, cmd.getTenantId().getId(), cmd.getOpEmpId());
            dmSvc.setRepo(auditFlowRepo);
            dmSvc.refreshSummary(evalUser.getTaskId());
            if (dmSvc.needSend()) {
                AdminTask adminTask = adminTaskRepo.getAdminTask(cmd.getTenantId(), evalUser.getTaskId());
                new ResultCollectMsgTodoEvent(cmd.getTenantId().getId(), dmSvc.getSendRater(), adminTask).publish();
            }
        }
        new LogDiffAdminTask(cmd.getOpEmpId(), evalUser, before, after, cmd.getOpAdminType()).publish();
        /**处理异常*/
        evalUser.setEmpEvalRule(rule);
        new EvalTaskErrorEvent(cmd.getTenantId().getId(), evalUser).publish();
    }

    @Transactional
    public void editInterviewConf(InterviewConfCmd cmd) {
        EvalUser evalUser = userRepo.getBaseTaskUser(cmd.getTenantId(), cmd.getEmpEvalId());
        if (evalUser.wasTempTask()) {
            throw new KpiI18NException("error.empEval.editConf.10001");//历史任务不支持修改
        }
        if (evalUser.afterStage(TalentStatus.RESULTS_INTERVIEW)) {
            throw new KpiI18NException("error.empEval.editConf.10003");//员工任务该阶段无法修改配置
        }
        EmpEvalRule rule = empRuleRepo.getEmpEvalRule(cmd.getTenantId(), cmd.getEmpEvalId());
        InterviewConf before = rule.getInterviewConf();
        InterviewConf after = cmd.getInterviewConf();
        if (cmd.isParseAudit()) {
            evalDmSvc.parseInterviewConf(after, evalUser, cmd.getOpEmpId());
        }
        rule.setInterviewConf(after);
        rule.setEditStatus(rule.getEditStatus() + 5);
        Integer confStatus = rule.reChekConfStatus();
        evalUser.setRuleConfStatus(confStatus);

        empRuleRepo.updateEmpEvalRule(rule);
        userRepo.updateTaskUser(evalUser);
        new LogDiffAdminTask(cmd.getOpEmpId(), evalUser, before, after, cmd.getOpAdminType()).publish();
        /**处理异常*/
        evalUser.setEmpEvalRule(rule);
        new EvalTaskErrorEvent(cmd.getTenantId().getId(), evalUser).publish();
    }

    @Transactional
    public void editPubResultConf(PublishResultConfCmd cmd) {
        EvalUser evalUser = userRepo.getBaseTaskUser(cmd.getTenantId(), cmd.getEmpEvalId());
        if (evalUser.wasTempTask()) {
            throw new KpiI18NException("error.empEval.editConf.10001");//历史任务不支持修改
        }
        if (evalUser.afterStage(TalentStatus.WAIT_PUBLISHED)) {
            throw new KpiI18NException("error.empEval.editConf.10003");//员工任务该阶段无法修改配置
        }
        EmpEvalRule rule = empRuleRepo.getEmpEvalRule(cmd.getTenantId(), cmd.getEmpEvalId());
        PublishResultConf before = rule.getPublishResult();
        PublishResultConf after = cmd.getPublishResultConf();
        rule.setPublishResult(after);
        rule.setEditStatus(rule.getEditStatus() + 8);
        Integer confStatus = rule.reChekConfStatus();
        evalUser.setRuleConfStatus(confStatus);

        empRuleRepo.updateEmpEvalRule(rule);
        userRepo.updateTaskUser(evalUser);
        new LogDiffAdminTask(cmd.getOpEmpId(), evalUser, before, after, cmd.getOpAdminType()).publish();
        /**处理异常*/
        evalUser.setEmpEvalRule(rule);
        new EvalTaskErrorEvent(cmd.getTenantId().getId(), evalUser).publish();
    }

    @Transactional
    public void editAppealConf(AppealConfCmd cmd) {
        EvalUser evalUser = userRepo.getBaseTaskUser(cmd.getTenantId(), cmd.getEmpEvalId());
        if (evalUser.wasTempTask()) {
            //历史任务不支持修改
            return;
        }
        EmpEvalRule rule = empRuleRepo.getEmpEvalRule(cmd.getTenantId(), cmd.getEmpEvalId());
        AppealConf before = rule.getAppealConf();
        AppealConf after = cmd.getAppealConf();
        after.extendsFinishedNumber(before);
        after.parseTaskEmpRater(this.getRaterWithTaskAdmin(cmd.getTenantId().getId(), cmd.getEmpEvalId()));
        rule.setAppealConf(after);
        rule.setEditStatus(rule.getEditStatus() + 16);
        Integer confStatus = rule.reChekConfStatus();
        evalUser.setRuleConfStatus(confStatus);

        empRuleRepo.updateEmpEvalRule(rule);
        userRepo.updateTaskUser(evalUser);
        new LogDiffAdminTask(cmd.getOpEmpId(), evalUser, before, after, cmd.getOpAdminType()).publish();
        /**处理异常*/
        evalUser.setEmpEvalRule(rule);
        new EvalTaskErrorEvent(cmd.getTenantId().getId(), evalUser).publish();
    }

    @Transactional
    public void editDeadLineConf(DeadLineLineCmd cmd) {
        EvalUser evalUser = userRepo.getBaseTaskUser(cmd.getTenantId(), cmd.getEmpEvalId());
        if (evalUser.wasTempTask()) {
            //历史任务不支持修改
            return;
        }
        EmpEvalRule rule = empRuleRepo.getEmpEvalRule(cmd.getTenantId(), cmd.getEmpEvalId());
        DeadLineConf before = rule.getDeadLineConf();
        DeadLineConf after = cmd.getDeadLineConf();
        rule.setDeadLineConf(after);
        rule.setEditStatus(rule.getEditStatus() + 32);
        Integer confStatus = rule.reChekConfStatus();
        evalUser.setRuleConfStatus(confStatus);
        evalUser.checkDeadLineError(rule.deadLineTimeOuted());
        empRuleRepo.updateEmpEvalRule(rule);
        empRuleRepo.updateDeadLine(evalUser.getTaskId(), rule);
        userRepo.updateTaskUserRuleConfStatus(cmd.getOpEmpId(), evalUser);
        new LogDiffAdminTask(cmd.getOpEmpId(), evalUser, before, after, cmd.getOpAdminType()).publish();
        /**处理异常*/
        evalUser.setEmpEvalRule(rule);
        new EvalTaskErrorEvent(cmd.getTenantId().getId(), evalUser).publish();
    }

    public boolean existRule(String empEvalId) {
        return empEvalDao.existRule(empEvalId);
    }

    public boolean onDrawUpIng(TenantId companyId, String taskUserId) {
        EvalUser taskUser = taskUserDao.getBaseEvalUser(companyId, taskUserId);
        return TalentStatus.DRAW_UP_ING.getStatus().equals(taskUser.getTaskStatus());
    }


    //手工确认考核结果
    @Transactional
    public void doAffirmResult(TenantId companyId, String opUser, String taskUserId, boolean isSkip, String signPicUrl) {
        EvalUser taskUser = userRepo.getBaseTaskUser(companyId, taskUserId);
        EmpEvalMerge empEvalMerge = empRuleRepo.getEmpEvalMerge(companyId, taskUserId, all);
        if (!isSkip && taskUserDao.hasWaitAppeal(companyId, taskUserId)) {
            log.info("存在结果申诉未处理，不能结果确认taskUserId:{}", taskUser.getId());
            return;
        }
        //保存签名图片
        if (StrUtil.isNotBlank(signPicUrl) || isSkip) {
            taskUser.setSignaturePic(signPicUrl);
            taskUser.setHasAppeal(Boolean.FALSE.toString());
            userRepo.updateTaskUser(taskUser);
        }
        List<String> clearScoreRsScenes = new ArrayList<>();
        clearScoreRsScenes.add(MsgSceneEnum.TASK_RESULT_AFFIRM.getType());
        if (isSkip) {
            clearScoreRsScenes.add(MsgSceneEnum.TASK_RESULT_APPEAL.getType());
            //清除申述记录，Todo  后续需 过滤掉已处理过申诉的，才清除申诉记录
            // taskAppealRepo.clearAppeal(companyId.getId(),taskUserId);
        }
        new CancelTodoEvent(companyId, taskUser.getId(), clearScoreRsScenes).publish();
        //记录日志
        String scene = OperationLogSceneEnum.RESULT_AFFIRM.getScene();
        OperationLogDo operationLogDo = new OperationLogDo(companyId.getId(), taskUser.getId(), scene, opUser, new Date());
        opLogDao.addLog(operationLogDo);
        new ThisStageEnded(empEvalMerge, taskUser, TalentStatus.RESULTS_AFFIRMING).publish();
    }

    //执行公示,进入下一阶段
    @Transactional
    public void markPublish(MarkPublishCmd cmd) {
        TenantId companyId = new TenantId(cmd.getCompanyId());
        Company company = companyDao.getById(companyId);
        for (String userId : cmd.getTaskUserIds()) {
            EvalUser taskUser = userRepo.getBaseTaskUser(companyId, userId);
            EmpEvalMerge empEvalMerge = empRuleRepo.getEmpEvalMerge(companyId, userId, all);
            //申诉时间启动
            empEvalMerge.startAppealOn(TalentStatus.PUBLISHED, taskUser);
            taskUser.setPublicFlag(Boolean.TRUE.toString());
            userRepo.updateTaskUser(taskUser);

            OperationLogDo logDo = new OperationLogDo(cmd.getCompanyId(), OperationLogSceneEnum.PUBLICATION_RESULT.getScene(), userId,
                    cmd.getOpEmpId());
            opLogDao.addLog(logDo);

            List<String> toEmpIds = empEvalDao.loadPubEmpIds(taskUser, companyId, userId, taskUser.getEmpId(), empEvalMerge.getPublishResult().getToEmps(), true);
            new CancelTodoEvent(companyId, new EmpId(cmd.getOpEmpId()), taskUser.getId(), MsgSceneEnum.TASK_WAIT_PUBLIC.getType()).publish();
            new ThisStageEnded(empEvalMerge, taskUser, TalentStatus.WAIT_PUBLISHED).publish();

            //如果是京东方，不发公示待办【考核任务已公示】
            if (StrUtil.equals("ding52e9b5335305552324f2f5cc6abecb85", company.getDingCorpId())) {
                continue;
            }
            new MsgTodoAggregate(companyId, empEvalMerge.getTaskId(), new Name(empEvalMerge.getTaskName()),
                    taskUser.getEmpId(), taskUser.getId())
                    .useScene(MsgSceneEnum.TASK_HAS_PUBLICAN)
                    .addExtTempValue("empName", taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                    .addExtTempValue("value", empEvalMerge.buildPublishResult(taskUser.getFinalScore(), taskUser.getEvaluationLevel(), taskUser.getPerfCoefficient()))
                    .sendExtMsg().addRecEmpId(toEmpIds).publish();
        }
    }

    //标记不公示,进入下一阶段
    @Transactional
    public void markNonePublish(MarkPublishCmd cmd) {
        TenantId tenantId = new TenantId(cmd.getCompanyId());
        userRepo.markNonePublish(cmd.getCompanyId(), cmd.getTaskUserIds());
        for (String userId : cmd.getTaskUserIds()) {
            EvalUser taskUser = userRepo.getBaseTaskUser(tenantId, userId);
            EmpEvalMerge empEvalMerge = empRuleRepo.getEmpEvalMerge(tenantId, userId, all);
            new ThisStageEnded(empEvalMerge, taskUser, TalentStatus.WAIT_PUBLISHED).publish();
            new CancelTodoEvent(new TenantId(cmd.getCompanyId()), new ArrayList<>(), userId, null).publish();
        }
    }

    public void directConfirmItem(ConfirmItemCmd cmd) {
        LevelAuditFlow flow = userRepo.loadAuditFlow(cmd.getTenantId(), cmd.getTaskUserId(), EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT);
        DirectConfirmItemDmSvc directDmSvc = new DirectConfirmItemDmSvc(cmd.getTenantId(), cmd.getTaskUserId(), flow, EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT);
        directDmSvc.initTask(taskRepo, userRepo);
        List<EvalScoreResult> curRs = directDmSvc.submitPass(cmd.isOnlySkipNode(), cmd.getConfirmEmpId(),
                cmd.getApproveOrder(), cmd.getScorerName(), cmd.getConfirmAuditSignUrl());
        tx.runTran(() -> {
            userRepo.batchUpdateScoreResult(curRs);
            userRepo.updateTaskUser(directDmSvc.getEmpEval());
            opLogDao.batchSaveLogDomain(directDmSvc.getLogs());
        });
        String opEmpId = cmd.getConfirmEmpId().getId();
        if (!directDmSvc.endCurLevelIfOpt(opEmpId)) {//结束当前层结点并清理待办
            return;
        }
        if (directDmSvc.endStageWithNoChangeSkip(opEmpId)) {//结束整个阶段
            return;
        }
        if (tx.runTran(() -> directDmSvc.dispatchNextLevel(userRepo))) {//派发下个层级结点
            // 更新负责人信息
            List<KpiEmp> reviewers = kpiEmpDao.listByEmp(cmd.getTenantId(), directDmSvc.getRecEmpId());
            tx.runTran(() -> directDmSvc.reviewers(userRepo, reviewers));
            directDmSvc.sendMsgTodoAggregate();
        }
    }

    @Transactional
    public void confirmItem(ConfirmItemCmd cmd) {
        EvalUser empEval = userRepo.getEmpEval(cmd.getTenantId(), cmd.getTaskUserId());
        empEval.submitItems(cmd.isOnlySkipNode() ? empEval.getKpiTypes() : cmd.buildKpiTypes());
        AdminTask adminTask = adminTaskRepo.getAdminTask(cmd.getTenantId(), empEval.getTaskId());
        LevelAuditFlow flow = userRepo.loadAuditFlow(cmd.getTenantId(), cmd.getTaskUserId(), EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT);
        log.debug("*****first-flow*****{}", JSONUtil.toJsonStr(flow));
        boolean indexCofMdfEmp = tenantSysConfRepo.isOpen(cmd.getTenantId().getId(), GlobalConfEnum.INDEX_COF_MDF_EMP.getConfCode());
        flow.submitPassAndDispatch(cmd.isOnlySkipNode(), cmd.getConfirmEmpId(), cmd.getApproveOrder());

        EmpEvalRule empEvalRule = empEval.getEmpEvalRule();
        if (Objects.nonNull(empEvalRule)) {
            empEvalRule.setKpiTypes(new KpiListWrap(empEval.getKpiTypes()));
        }
        if (Objects.nonNull(empEvalRule) && empEvalRule.isCustom()) {
            empRuleRepo.customItemFlow(empEvalRule, empEval.getTaskId(), empEval.getEmpId(), indexCofMdfEmp);
        }
        /**处理okr指标*/
        if (cmd.okrNotNull()) {
            updateOKR(cmd, empEval);
        }
        if (Objects.nonNull(empEvalRule) && indexCofMdfEmp && !cmd.isOnlySkipNode()) {
            empEvalRule.accRater(cmd.getS3SelfRater(), cmd.getS3PeerRater(), cmd.getS3SubRater(), cmd.getS3SuperRater(), cmd.getS3AppointRater());
            empEvalRule.initIndexRaters();
            empRuleRepo.updateEmpEvalRule(empEvalRule);
            //考核规则变更事件
            new EvalRuleChanged(cmd.getTenantId(),cmd.getConfirmEmpId().getId(), empEval,empEvalRule).fire();
        }
        empEval.setEmpEvalRule(empEvalRule);
        userRepo.updateKpiItem(empEval, cmd.getRecord(), cmd.getConfirmEmpId());

        empEval.clearAutoScore();

        auditRepo.updateFinishedValueAudit(empEval.getCompanyId().getId(), empEval.getId());
        //删除问卷考核
        if (CollUtil.isNotEmpty(cmd.delAsk360EvalIds())) {
            askEvalAcl.delAsk360Eval(cmd.getTenantId().getId(), cmd.getTaskUserId(), cmd.delAsk360EvalIds(), cmd.getOpEmpId().getId());
        }
        OperationLogDo logDo = new OperationLogDo(cmd.getTenantId().getId(), cmd.getTaskUserId(),
                OperationLogSceneEnum.AUDIT_ITEM.getScene(), cmd.getOpEmpId().getId(), new Date());
        if (cmd.isOnlySkipNode()) {
            JSONObject logDesc = new JSONObject();
            logDesc.put("scorerId", cmd.getConfirmEmpId().getId());
            logDesc.put("scorerType", AuditEnum.CONFIRM_TASK.getScene());
            logDesc.put("scorerName", cmd.getScorerName());
            logDo.setDescription(logDesc.toJSONString());
            logDo.setBusinessScene(OperationLogSceneEnum.SKIP_REVIEWER.getScene());
        }
        if (StrUtil.isNotBlank(cmd.getConfirmAuditSignUrl())) {
            logDo.setDescription(cmd.getConfirmAuditSignUrl());
        }
        opLogDao.addLog(logDo);
        empEval.removeItemDeleted();
        List<String> todoScenes = MsgSceneEnum.confirmScene;
        //跳过流程到下阶段
        if (cmd.skipToNextStage(adminTask.getConfirmTask().noChangeSkip())) {
            CycleEval cycleEval = taskRepo.getMergeCycleEval(cmd.getTenantId(), empEval.getId());
            new ThisStageEnded(cycleEval, empEval, TalentStatus.CONFIRMING).publish();
            //前面一人未更改 ，则跳过后续的审核人，但是对于审核人的节点都需要派发并且处理掉，否则getFlow任务流程审核人的状态有问题
            userRepo.updateLevelFlow(flow.nextAllAudits(), flow.curLevelRs(), flow.nextAllRs(flow.nextAllAudits()));
            //如果任务设置的是第一个审批人未修改则跳过,将其他审核人的待办也清除
//            new CancelTodoEvent(cmd.getTenantId(), empEval.getId(), todoScenes).publish();
            new ClearTodoDmsSvc(centerRepo).clear(cmd.getTenantId(), empEval.getId(), todoScenes);
            cycleEvalDmSvc.batchSendAffirmTask(adminTask, empEval, empEval.listAffirmTaskNotify(cmd.getOpEmpId().getId()));
            return;
        }
        if (!flow.isLevelEnd()) { //仅结束当前人
            empEval.removeReviewers(Arrays.asList(cmd.getConfirmEmpId().getId()));
            userRepo.updateTaskUser(empEval);
            List<EvalScoreResult> curRs = flow.curLevelRs().stream().filter(scoreResult -> scoreResult.getScorerId().equals(cmd.getConfirmEmpId().getId())).collect(Collectors.toList());
            userRepo.batchUpdateScoreResult(curRs);
            //userRepo.updateLevelFlow(flow.nextAudits(), curRs, null);
            //先清理本地的待办,再异步清远端的.
            new ClearTodoDmsSvc(centerRepo).clear(cmd.getTenantId(), empEval.getId(), todoScenes, cmd.getConfirmEmpId().getId());
            return;
        }
        log.debug("*****flow*****{}", JSONUtil.toJsonStr(flow));
        log.debug("*****flow.curLevelEmpIds*****{}", JSONUtil.toJsonStr(flow.curLevelEmpIds()));
        new ClearTodoDmsSvc(centerRepo).clear(cmd.getTenantId(), empEval.getId(), todoScenes, flow.curLevelEmpIds());
        SkipAuditNodeDmSvc nodeDmSvc = new SkipAuditNodeDmSvc(cmd.getTenantId().getId(), cmd.getTaskUserId(), flow, EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT.getScene());
        nodeDmSvc.setRepo(userRepo);
        nodeDmSvc.skip(flow.currentLevel() + 1);
        opLogDao.batchSaveLogDomain(nodeDmSvc.getLogs());
        //当前阶段结束
        if (nodeDmSvc.isEnd()) {
            CycleEval cycleEval = taskRepo.getMergeCycleEval(cmd.getTenantId(), empEval.getId());
            new ThisStageEnded(cycleEval, empEval, TalentStatus.CONFIRMING, cmd.getOpEmpId().getId()).publish();
            cycleEvalDmSvc.batchSendAffirmTask(adminTask, empEval, empEval.listAffirmTaskNotify(cmd.getOpEmpId().getId()));
            return;
        }
        // 更新负责人信息
        List<KpiEmp> reviewers = kpiEmpDao.listByEmp(empEval.getCompanyId(), nodeDmSvc.getRecEmpId());
        empEval.reviewers(reviewers);
        userRepo.updateTaskUser(empEval);


        new MsgTodoAggregate(empEval.getCompanyId(), empEval.getTaskId(), new Name(adminTask.getTaskName()), empEval.getEmpId(), empEval.getId())
                .useScene(MsgSceneEnum.TASK_CONFIRM_AUDIT, null)
                .addExtTempValue("evalEmpName", empEval.getEvalOrgName() == null ? empEval.getEmpName() : empEval.getEvalOrgName())
                .addExtTempValue("deadLineDate", empEval.joinDeadLineStr(TalentStatus.CONFIRMING.getStatus()))
                .addTodoItem("msg.task.emp", empEval.getEvalOrgName() == null ? empEval.getEmpName() : empEval.getEvalOrgName())
                .sendExtMsg().addCenterMsg().sendExtTodo()
                .addRecEmpId(nodeDmSvc.getRecEmpId()).publish();
    }

    public void updateOKR(ConfirmItemCmd cmd, EvalUser empEval) {
        /**保存okr指标*/
        AddOKRCmd addOKRCmd = cmd.getAddOKRCmd();
        addOKRCmd.setOperator(cmd.getTenantId().getId(), cmd.getOpEmpId().getId(), null);
        CycleEval mergeTask = taskDao.getMergeTaskBase(addOKRCmd.getTenantId(), addOKRCmd.getTaskUserId());
        //EvalUser user = taskUserDao.getBothBaseUser(cmd.getTenantId().getId(), mergeTask.getId(), addOKRCmd.getOpEmpId(), cmd.getTaskUserId());
        KpiListWrap okrTypes = kpiDao.listAndBuildOkrType(empEval.getCompanyId(), empEval.getId());
        okrTypes.asMap(EmpEvalKpiType::getKpiTypeId);
        addOKRCmd.buildOkrKpis(empEval.getTaskId(), empEval.getId(), empEval.getEmpId(), okrTypes);
        EmpEvalRule empEvalRule = empEvalDao.getEmpEvalRule(cmd.getTenantId(), empEval.getId());
        ImportOkrDmSvc okrDmSvc = new ImportOkrDmSvc(mergeTask, empEval, okrTypes);
        if (!addOKRCmd.getAdds().isEmpty() || !addOKRCmd.getAddKpis().isEmpty() || !addOKRCmd.getDels().isEmpty() || !addOKRCmd.getEdits().isEmpty()) {
            //构建 itemRule
            okrDmSvc.doImportOkr(addOKRCmd.getAddKpis(), addOKRCmd.getAdds());
            //更新  item refAction
            tx.runTran(() -> taskKpiRepo.updateNewOkrKpi(new EmpId(addOKRCmd.getOpEmpId()), okrTypes, addOKRCmd.getAddKpis(), addOKRCmd.getAdds(),
                    addOKRCmd.dels, addOKRCmd.edits, empEvalRule, empEval.isOpenAvgWeightCompute()));
            // 通知 okr系统
            if (addOKRCmd.getAddKpis().isEmpty()) {
                return;
            }
            CompanyPo company = deptEmpDao.getTenant(cmd.getTenantId());
            for (EvalKpi add : addOKRCmd.getAddKpis()) {
                evalOkrAcl.addRefAction(company.getDingCorpId(), add.getRefOkr().getActionId(), add.getKpiTypeId());
            }
        }
    }

    public List<EmpChangeItemStagePo> listAuditCachedKpi(TenantId tenantId, String
            taskUserId, List<String> scenes) {
        List<EmpChangeItemStagePo> items = kpiDao.listCacheKpi(tenantId, taskUserId, scenes);
        if (CollUtil.isEmpty(items)) {
            return null;
        }
        List<String> inputEmpIds = items.stream().flatMap(it -> it.getItems().stream()).flatMap(o -> Arrays.stream(StrUtil.split(o.getResultInputEmpId(), ",")))
                .collect(Collectors.toList());
        Map<String, KpiEmp> empMap = kpiEmpDao.listByEmpAsMap(tenantId, inputEmpIds);
        for (EmpChangeItemStagePo item : items) {
            item.buildOkr();
            for (EmpChangeItemStagePo.CacheKpiItem itemItem : item.getItems()) {
                itemItem.initInputEmps(empMap);
            }
        }
        return items;
    }

    public void buildOkr(TenantId
                                 tenantId, List<EmpEvalDetailKpiItemPo> okrItems, List<EmpEvalDetailKpiItemPo> refKrItems) {
        loadOkrIf(tenantId, okrItems, refKrItems);
    }

    @Transactional
    public void submitChangeItem(ChangeItemCmd cmd) {
        EvalUser empEval = userRepo.getEmpEval(cmd.getTenantId(), cmd.getTaskUserId());
//        AdminTask adminTask = adminTaskRepo.getAdminTask(cmd.getTenantId(), empEval.getTaskId());
        EmpEvalMerge empEvalMerge = empRuleRepo.getEmpEvalMerge(cmd.getTenantId(), cmd.getTaskUserId(), beforeScore);
        EditExeIndiConf editExeIndi = empEvalMerge.getEditExeIndi();
        userRepo.saveAuditOpLog(cmd.getTenantId(), cmd.getTaskUserId(), cmd.getOpEmpId(), cmd.getRecord().getValue(), cmd.getChangeReason());
        userRepo.rmCacheOfOpEmpId(cmd.getTenantId(), cmd.getOpEmpId(), cmd.getTaskUserId(), AuditEnum.EDIT_EXE_INDI.getScene());
        if (!editExeIndi.auditIsOpen()) {
            List<EmpEvalKpiType> kpiTypes = cmd.listKpiTypes(empEval.getEmpId());

            EmpEvalRule empEvalRule = empEval.getEmpEvalRule();
            if (Objects.nonNull(empEvalRule)) {
                empEvalRule.setKpiTypes(new KpiListWrap(kpiTypes));
            }
            if (Objects.nonNull(empEvalRule) && empEvalRule.isCustom()) {
                empRuleRepo.customItemFlow(empEvalRule, empEval.getTaskId(), empEval.getEmpId());
            }


            /**处理okr指标*/
            ConfirmItemCmd itemCmd = new ConfirmItemCmd(cmd.getTenantId(), cmd.getOpEmpId(), cmd.getTaskUserId());
            itemCmd.buildOkrRef(kpiTypes);
            if (itemCmd.okrNotNull()) {
                updateOKR(itemCmd, empEval);
            }
            empEval.setEmpEvalRule(empEvalRule);
            userRepo.changeItem(empEval, cmd.getOpEmpId(), cmd.getChangeReason());
            //删除问卷
            if (CollUtil.isNotEmpty(cmd.delAsk360EvalIds())) {
                askEvalAcl.delAsk360Eval(cmd.getTenantId().getId(), cmd.getTaskUserId(), cmd.delAsk360EvalIds(), cmd.getOpEmpId().getId());
            }
            // 更新录入人为负责人
            List<KpiEmp> reviewers = kpiEmpDao.listByEmp(empEval.getCompanyId(), cmd.inputEmpIds());
            empEval.reviewers(reviewers);
            userRepo.updateTaskUser(empEval);

            //取消旧的录入完成值代办
            new CancelTodoEvent(cmd.getTenantId(), empEval.getId(), MsgSceneEnum.TASK_SUBMIT_PROGRESS.getType()).publish();
            //指标更变后 录入人可能更新，重新给这批录入人生成代办
            new EvalInputNotifySend(empEvalMerge.getInputNotify(), empEval,
                    empEvalMerge.taskName(), cmd.inputEmpIds().stream().collect(Collectors.toSet()), 5).publish();
            return;
        }
        empEval.setTaskStatus(TalentStatus.CHANGING.getStatus());
        //保存申请的变更信息,启动审核流程
        userRepo.saveApplyInfo(cmd.getTenantId(), cmd.getTaskUserId(), cmd.listKpiTypes(empEval.getEmpId()), cmd.getRecord(), cmd.getChangeReason());
        //需要审核
        LevelAuditFlow flow = userRepo.loadAuditFlow(cmd.getTenantId(), cmd.getTaskUserId(), EvaluateAuditSceneEnum.CHANGE_ITEM_AUDIT);
        SkipAuditNodeDmSvc nodeDmSvc = new SkipAuditNodeDmSvc(cmd.getTenantId().getId(), empEval.getId(), flow, EvaluateAuditSceneEnum.CHANGE_ITEM_AUDIT.getScene());
        nodeDmSvc.setRepo(userRepo);
        nodeDmSvc.startSkip(1);
        opLogDao.batchSaveLogDomain(nodeDmSvc.getLogs());
        // 更新负责人信息
        List<KpiEmp> reviewers = kpiEmpDao.listByEmp(empEval.getCompanyId(), nodeDmSvc.getRecEmpId());
        empEval.reviewers(reviewers);
        userRepo.updateTaskUser(empEval);
        //取消旧的录入完成值代办,变更审核通过会进行重新发送录入完成值待办
        new CancelTodoEvent(cmd.getTenantId(), empEval.getId(), MsgSceneEnum.TASK_SUBMIT_PROGRESS.getType()).publish();
        //已发送的Audit更新dispatch
        new MsgTodoAggregate(cmd.getTenantId(), empEval.getTaskId(), new Name(empEvalMerge.getTaskName()), empEval.getEmpId(), empEval.getId())
                .useScene(MsgSceneEnum.CHANGE_ITEM_AUDIT, null).sendExtMsg().addCenterMsg().sendExtTodo()
                .addRecEmpId(nodeDmSvc.getRecEmpId()).publish();
    }


    @Transactional
    public void finishedValueAuditStart(String companyId, String taskUserId, EvalUser empEval, EmpId opEmpId) {
        EmpEvalMerge empEvalMerge = empRuleRepo.getEmpEvalMerge(new TenantId(companyId), taskUserId, beforeScore);
//        if (TalentStatus.FINISH_VALUE_AUDIT.getStatus().equals(empEval.getTaskStatus())) {
//            log.info("====已经进入完成值审核阶段了，不再执行一遍===========");
//            return;
//        }

        empEval.setTaskStatus(TalentStatus.FINISH_VALUE_AUDIT.getStatus());
        //需要审核
        LevelAuditFlow flow = userRepo.loadAuditFlow(new TenantId(companyId), taskUserId, EvaluateAuditSceneEnum.FINISH_VALUE_AUDIT);
        flow.dispatchFirst();
        userRepo.updateLevelFlow(flow.curAudits(), null, flow.curLevelRs());
        // 更新负责人信息
        List<KpiEmp> reviewers = kpiEmpDao.listByEmp(empEval.getCompanyId(), flow.curLevelEmpIds());
        empEval.reviewers(reviewers);
        userRepo.updateTaskUser(empEval);
        //通知okr锁定
        new EvalEmpOkrLockEdited(new TenantId(companyId), Arrays.asList(taskUserId), opEmpId, "updateLock").publish();
        // 外部数据锁定
        new ExtDataLocked(empEval, companyId).fire();
        //已发送的Audit更新dispatch
        new MsgTodoAggregate(new TenantId(companyId), empEval.getTaskId(), new Name(empEvalMerge.getTaskName()), empEval.getEmpId(), empEval.getId())
                .useScene(MsgSceneEnum.FINISH_VALUE_AUDIT, null)
                .addExtTempValue("evalEmpName", empEval.getEvalOrgName() == null ? empEval.getEmpName() : empEval.getEvalOrgName())
                .addExtTempValue("deadLineDate", empEval.joinDeadLineStr(TalentStatus.FINISH_VALUE_AUDIT.getStatus()))
                .addTodoItem("msg.task.emp", empEval.getEvalOrgName() == null ? empEval.getEmpName() : empEval.getEvalOrgName())
                .sendExtMsg().addCenterMsg().sendExtTodo()
                .addRecEmpId(flow.curLevelEmpIds()).publish();
    }

    //    @Transactional
    public void finishedValueAudit(FinishedValueAuditCmd cmd) {
        EvalUser empEval = userRepo.getEmpEval(cmd.getTenantId(), cmd.getTaskUserId());
        //审核环节
        AdminTask adminTask = adminTaskRepo.getAdminTask(cmd.getTenantId(), empEval.getTaskId());
        LevelAuditFlow flow = userRepo.loadAuditFlow(cmd.getTenantId(), cmd.getTaskUserId(), EvaluateAuditSceneEnum.FINISH_VALUE_AUDIT);
        flow.submitFinishedValueAuditPass(cmd.isOnlySkipNode(), cmd.getFinishedValueAuditEmpId(), cmd.getApproveOrder());

        EmpEvalRule empEvalRule = empEval.getEmpEvalRule();
        if (Objects.nonNull(empEvalRule) && empEvalRule.isCustom()) {
            empEvalRule.setKpiTypes(new KpiListWrap(empEval.getKpiTypes()));
        }

        List<String> todoScenes = Collections.singletonList(MsgSceneEnum.FINISH_VALUE_AUDIT.getType());
        //跳过流程到下阶段
        if (cmd.skipToNextStage(adminTask.getFinishValueAudit().noChangeSkip())) {
            log.info("跳过流程到下阶段：taskUserId:{},", cmd.getTaskUserId());
            CycleEval cycleEval = taskRepo.getMergeCycleEval(cmd.getTenantId(), empEval.getId());
            new ThisStageEnded(cycleEval, empEval, TalentStatus.CONFIRMED).publish();
            //前面一人未更改 ，则跳过后续的审核人，但是对于审核人的节点都需要派发并且处理掉，否则getFlow任务流程审核人的状态有问题
            tx.runTran(() -> userRepo.updateLevelFlow(flow.nextAllAudits(), flow.curLevelRs(), flow.nextAllRs(flow.nextAllAudits())));
            new CancelTodoEvent(cmd.getTenantId(), cmd.getFinishedValueAuditEmpId(), empEval.getId(), MsgSceneEnum.TASK_CONFIRM.getType()).publish();
            return;
        }

        //更新录入完成值与说明
        tx.runTran(() -> taskKpiRepo.batchUpdateFinishValue(cmd.getTenantId(), cmd.getTaskUserId(), cmd.getOpEmpId(), cmd.getFinishValues(), empEval.getKpis(), OperationLogSceneEnum.FINISH_VALUE_AUDIT.getScene()));

        //同步更新okr
        Map<String, FinishValue> finishValueMap = cmd.getFinishValues().stream().collect(Collectors.toMap(f -> f.getId(), Function.identity()));
        if (CollUtil.isNotEmpty(finishValueMap)) {
            List<EvalKpi> okrItems = empEval.getKpis().stream().filter(kpi -> (kpi.isOkrType() || kpi.refOkr()) && finishValueMap.get(kpi.getId()) != null).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(okrItems)) {
                CompanyPo company = deptEmpDao.getTenant(cmd.getTenantId());
                KpiEmployee employee = empDao.findEmployee(cmd.getTenantId(), cmd.getOpEmpId().getId());
                for (EvalKpi okrItem : okrItems) {
                    String actionId = null;
                    String refId = null;
                    if (okrItem.isOkrType()) {
                        actionId = okrItem.getActionId();
                    }
                    if (okrItem.refOkr()) {
                        refId = okrItem.getId();
                    }
                    okrAclSvc.updateAction(company.getDingCorpId(), actionId, ObjectUtil.isNotNull(okrItem.getItemFinishValue()) ?
                            okrItem.getItemFinishValue().toString() : "", cmd.getFinishValueComment(okrItem.getId()), employee.getDingUserId(), refId);
                }
            }
        }
        if (!flow.isLevelEnd()) { //仅结束当前人
            log.info("仅结束当前人：taskUserId:{},", cmd.getTaskUserId());
            List<String> finishedValueAuditEmpIds = flow.passAuditEmpId();
            empEval.removeReviewers(finishedValueAuditEmpIds);
            List<EvalScoreResult> curRs = flow.curLevelRs().stream().filter(scoreResult -> finishedValueAuditEmpIds.contains(scoreResult.getScorerId())).collect(Collectors.toList());
            tx.runTran(() -> {
                userRepo.updateTaskUser(empEval);
                userRepo.batchUpdateScoreResult(curRs);
            });
            //userRepo.updateLevelFlow(flow.nextAudits(), curRs, null);
            // new CancelTodoEvent(cmd.getTenantId(), finishedValueAuditEmpIds, empEval.getId(), MsgSceneEnum.FINISH_VALUE_AUDIT.getType()).publish();
            //仅结束当前人  //先清理本地的待办,再异步清远端的.
            new ClearTodoDmsSvc(centerRepo).clear(cmd.getTenantId(), empEval.getId(), todoScenes, finishedValueAuditEmpIds);
            return;
        }
        //当前层级结束了
        tx.runTran(() -> userRepo.updateLevelFlow(flow.nextAudits(), flow.curLevelRs(), flow.nextLevelRs()));
        new ClearTodoDmsSvc(centerRepo).clear(cmd.getTenantId(), empEval.getId(), todoScenes, flow.curLevelEmpIds());
        // new CancelTodoEvent(cmd.getTenantId(), flow.curLevelEmpIds(), empEval.getId(), MsgSceneEnum.FINISH_VALUE_AUDIT.getType()).publish();
        //当前阶段结束,进入评分阶段
        if (flow.isEnd()) {
            empEval.setFinishValueAuditStatus(FinishValueAuditStatusEnum.PASS.getType());
            //审核环节结束后，通知okr删除锁定
            //new EvalEmpOkrLockEdited(cmd.getTenantId(), Arrays.asList(cmd.getTaskUserId()), cmd.getOpEmpId(), "del").publish();
            log.info("当前阶段结束,进入评分阶段：taskUserId:{},", cmd.getTaskUserId());
            log.info("录入完成值审核完成触发进入评分阶段：taskUserId:{},", cmd.getTaskUserId());
            CompanyConf conf = companyDao.findCompanyConf(cmd.getTenantId());
            //录入完成值审核进入评分阶段，进入类型是3
            EnterScoreDmSvc dmSvc = new EnterScoreDmSvc(conf, empEval, cmd.getOpEmpId(), true, EnterScoreTypeEnum.FINISHED_VALUE_AUDIT_ENTER_SCORE.getType());
            tx.runTran(() -> {
                userRepo.updateTaskUser(empEval);//更新完成值审核通过状态
                evalTaskAppSvc.enterScoring(dmSvc);
            });
            log.info("录入完成值审核完成值触发进入评分阶段end---");
            return;
        }

        // 更新负责人信息
        List<KpiEmp> reviewers = kpiEmpDao.listByEmp(empEval.getCompanyId(), flow.nextLevelEmpIds());
        empEval.reviewers(reviewers);
        tx.runTran(() -> userRepo.updateTaskUser(empEval));

        new MsgTodoAggregate(empEval.getCompanyId(), empEval.getTaskId(), new Name(adminTask.getTaskName()), empEval.getEmpId(), empEval.getId())
                .useScene(MsgSceneEnum.FINISH_VALUE_AUDIT, null)
                .addExtTempValue("evalEmpName", empEval.getEvalOrgName() == null ? empEval.getEmpName() : empEval.getEvalOrgName())
                .addExtTempValue("deadLineDate", empEval.joinDeadLineStr(TalentStatus.FINISH_VALUE_AUDIT.getStatus()))
                .addTodoItem("msg.task.emp", empEval.getEvalOrgName() == null ? empEval.getEmpName() : empEval.getEvalOrgName())
                .sendExtMsg().addCenterMsg().sendExtTodo()
                .addRecEmpId(flow.nextLevelEmpIds()).publish();
    }

    //编辑okr的锁定标识
    @Transactional
    public void okrLockEdited(EvalEmpOkrLockEdited event) {
        event.start();
        TenantId companyId = event.getCompanyId();
        List<String> taskUserIds = event.getTaskUserIds();
        if (CollUtil.isEmpty(taskUserIds)) {
            return;
        }
        CompanyPo company = deptEmpDao.getTenant(companyId);
        KpiEmployee employee = empDao.findEmployee(companyId, event.getOpEmpId().getId());
        for (String taskUserId : taskUserIds) {
            List<EvalKpi> kpi = kpiDao.listEmpEvalKpiItem(companyId, taskUserId).getDatas();
            kpi = kpi.stream().filter(k -> k.isOkrType() || k.refOkr()).collect(Collectors.toList());
            for (EvalKpi evalKpi : kpi) {
                String actionId = null;
                String refId = null;
                if (evalKpi.isOkrType()) {
                    actionId = evalKpi.getActionId();
                }
                if (evalKpi.refOkr()) {
                    refId = evalKpi.getId();
                }
                okrAclSvc.addRefAction(company.getDingCorpId(), actionId, refId, event.getOptType(), employee.getDingUserId());
            }

        }

    }

    @Transactional
    public void passChangeItem(String companyId, EmpId opEmpId, String taskUserId, String reason) {
        TenantId tenantId = new TenantId(companyId);
        EvalUser empEval = userRepo.getEmpEval(tenantId, taskUserId);
        //兼容1.0的错误枚举 ； kpiTypes 存在为空的场景，待办未清，重复提交
        List<EmpEvalKpiType> kpiTypes = userRepo.listCacheKpi(tenantId, taskUserId, empEval.getEmpId(),
                Arrays.asList(AuditEnum.EDIT_EXE_INDI.getScene(), OperationLogSceneEnum.CHANGE_ITEM.getScene()));
        if (CollUtil.isEmpty(kpiTypes)) {
            log.warn("考核维度为空，禁止提交！！");//待办还需要清，避免再次点  //结束当前操作人待办
            new CancelTodoEvent(tenantId, opEmpId, empEval.getId(), MsgSceneEnum.CHANGE_ITEM_AUDIT.getType()).publish();
            return;
        }
        AdminTask adminTask = adminTaskRepo.getAdminTask(tenantId, empEval.getTaskId());
        LevelAuditFlow flow = userRepo.loadAuditFlow(tenantId, taskUserId, EvaluateAuditSceneEnum.CHANGE_ITEM_AUDIT);
        log.debug("passChangeItem.flow:{}", JSONUtil.toJsonStr(flow));
        int order = flow.matchOpEmpOrder(opEmpId);
        flow.submitPassAndDispatch(false, opEmpId, order);
        //记录审核日志
        opLogDao.addLog(new OperationLogDo(companyId, empEval.getId(), OperationLogSceneEnum.CHANGE_ITEM_AUDIT_PASS.getScene(), opEmpId.getId(), reason));
        //变更操作日志
        userRepo.saveModification(new TenantId(companyId), opEmpId, taskUserId, OperationLogSceneEnum.CHANGE_ITEM.getScene(), BusinessConstant.PASS, reason);

        if (!flow.isLevelEnd()) { //仅结束当前人
            empEval.removeReviewers(Arrays.asList(opEmpId.getId()));
            List<EvalScoreResult> results = flow.curLevelRs().stream().filter(scoreResult -> scoreResult.getScorerId().equals(opEmpId.getId())).collect(Collectors.toList());
            userRepo.batchUpdateScoreResult(results);
            userRepo.updateTaskUser(empEval);
            new CancelTodoEvent(tenantId, opEmpId, empEval.getId(), MsgSceneEnum.CHANGE_ITEM_AUDIT.getType()).publish();
            return;
        }
        //结束当前层级的
        new CancelTodoEvent(tenantId, flow.curLevelEmpIds(), empEval.getId(), MsgSceneEnum.CHANGE_ITEM_AUDIT.getType()).publish();
        //保存下一层级的
        SkipAuditNodeDmSvc nodeDmSvc = new SkipAuditNodeDmSvc(companyId, taskUserId, flow, EvaluateAuditSceneEnum.CHANGE_ITEM_AUDIT.getScene());
        nodeDmSvc.setRepo(userRepo);
        nodeDmSvc.skip(flow.currentLevel() + 1);
        opLogDao.batchSaveLogDomain(nodeDmSvc.getLogs());
        //userRepo.updateLevelFlow(flow.nextAudits(), flow.curLevelRs(), flow.nextLevelRs());
        if (nodeDmSvc.isEnd()) {  //当前阶段结束
            CycleEval cycleEval = taskRepo.getMergeCycleEval(tenantId, empEval.getId());
            /**处理okr指标*/
            List<EmpChangeItemStagePo> stagePos = evalTaskDao.listCacheOkrKpi(tenantId, taskUserId, empEval.getEmpId(),
                    Arrays.asList(AuditEnum.EDIT_EXE_INDI.getScene(), OperationLogSceneEnum.CHANGE_ITEM.getScene())).stream().filter(obj -> obj.isOkr()).collect(Collectors.toList());
            if (stagePos != null && stagePos.size() > 0) {
                ConfirmItemCmd itemCmd = new ConfirmItemCmd(tenantId, opEmpId, taskUserId);
                itemCmd.buildOkrPassChangeRef(itemCmd, stagePos);
                updateOKR(itemCmd, empEval);
            }
            //若有自定义指标，新增指标后应初始化默认考核流程
            EmpEvalRule empEvalRule = empEval.getEmpEvalRule();
            if (Objects.nonNull(empEvalRule)) {
                empEvalRule.setKpiTypes(new KpiListWrap(kpiTypes));
            }
            if (Objects.nonNull(empEvalRule) && empEvalRule.isCustom()) {
                empRuleRepo.customItemFlow(empEvalRule, empEval.getTaskId(), empEval.getEmpId());
            }
            empEval.setEmpEvalRule(empEvalRule);
            empEval.setKpiTypes(kpiTypes);//使用更新之后的维度和指标,用于后面保存
            userRepo.changeItem(empEval, opEmpId, reason);
            //删除问卷
            List<String> delAskEvalIds = CollUtil.filterNew(kpiTypes, k -> k.isAskType()).stream()
                    .filter(ek -> Objects.equals(ek.getIsDeleted(), Boolean.TRUE.toString()))
                    .map(EmpEvalKpiType::getAsk360EvalId)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(delAskEvalIds)) {
                askEvalAcl.delAsk360Eval(tenantId.getId(), taskUserId, delAskEvalIds, opEmpId.getId());
            }
            //userRepo.updateLevelFlow(flow.curAudits(), flow.curLevelRs(), null);
            // 更新录入人为负责人
            List<String> inputEmpIds = kpiTypes.stream().flatMap(it -> it.getItems().stream())
                    .filter(i -> !"no".equals(i.getResultInputType()) && Objects.equals(i.getIsDeleted(), Boolean.FALSE.toString()))
                    .flatMap(o -> o.inputEmp(empEval.getEmpId()).stream())
                    .collect(Collectors.toList());
            List<KpiEmp> reviewers = kpiEmpDao.listByEmp(empEval.getCompanyId(), inputEmpIds);
            empEval.curStatus(TalentStatus.CONFIRMED, reviewers);
            userRepo.updateTaskUser(empEval);

//            EmpEvalRule empEvalRule = empEval.getEmpEvalRule();
//            if (empEvalRule.isCustom()) {
//                empEvalRule.acceptConfirmItem(kpiTypes);
//                empRuleRepo.applyDefRule(empEvalRule, empEval.getTaskId(), empEval.getEmpId());
//            }
            Date date = new Date();
            //取消旧的录入完成值代办
            CancelTodoEvent cancelTodoEvent = new CancelTodoEvent(tenantId, empEval.getId(), MsgSceneEnum.TASK_SUBMIT_PROGRESS.getType());
            cancelTodoEvent.setCancelBeforeDate(date);
            cancelTodoEvent.publish();
            //指标更变后 录入人可能更新，重新给这批录入人生成代办,by20240918,sendType5改成null[全部都发]
            new EvalInputNotifySend(adminTask.getInputNotifyConf(), empEval,
                    new Name(adminTask.getTaskName()), inputEmpIds.stream().collect(Collectors.toSet())).publish();
            new ThisStageEnded(cycleEval, empEval, TalentStatus.CONFIRMED).publish();
            return;
        }
        // 更新负责人信息
        List<KpiEmp> reviewers = kpiEmpDao.listByEmp(empEval.getCompanyId(), nodeDmSvc.getRecEmpId());
        empEval.reviewers(reviewers);
        userRepo.updateTaskUser(empEval);

        new MsgTodoAggregate(empEval.getCompanyId(), empEval.getTaskId(), new Name(adminTask.getTaskName()), empEval.getEmpId(), empEval.getId())
                .useScene(MsgSceneEnum.CHANGE_ITEM_AUDIT, null).sendExtMsg().addCenterMsg().sendExtTodo()
                .addRecEmpId(nodeDmSvc.getRecEmpId()).publish();
    }

    public void doResultAudit(ResultAuditCmd cmd) {
        TenantId tenantId = cmd.getCompanyId();
        EmpId opEmpId = cmd.getOpEmpId();
        String taskUserId = cmd.getId();
        //执行流程
        LevelAuditFlow flow = userRepo.loadAuditFlow(tenantId, taskUserId, EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT);
        log.debug("doResultAudit.flow流程------:{}", JSONUtil.toJsonStr(flow));
        EvalUser empEval = userRepo.getTaskUser(tenantId, taskUserId);
        if (!empEval.isResultsAuditing()) {//批量的时候用户重复提交忽略掉,防止阻止后面的成员校准
            return;
        }
        AdminTask adminTask = adminTaskRepo.getAdminTask(tenantId, empEval.getTaskId());
        CycleEval cycleEval = taskRepo.getMergeCycleEval(tenantId, empEval.getId());
        ResultAudit resultAudit = new ToDataBuilder<>(cmd, ResultAudit.class).data();
        PassAuditResultDmSvc resultDmSvc = new PassAuditResultDmSvc(empEval, flow, cycleEval, adminTask, resultAudit);
        resultDmSvc.accRepo(auditFlowRepo, userRepo, centerRepo);
        tx.runTran(() -> resultDmSvc.pass());
        //保存日志
        opLogDao.addLog(new ToDataBuilder<>(resultDmSvc.getLog(), OperationLogDo.class).data());
        //删除暂存数据
        calibratedDao.delResultAuditCache(cmd.getCompanyId().getId(), Arrays.asList(taskUserId), cmd.getOpEmpId().getId());
        if (resultDmSvc.isEnd()) {
            log.debug("校准环节结束:{}", JSONUtil.toJsonStr(empEval));
            return;
        }
        //跳过
        SkipResultAuditDmSvc skipDmSvc = new SkipResultAuditDmSvc(tenantId.getId(), adminTask.getId(), empEval.getId(), opEmpId.getId(), EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene(), flow);
        skipDmSvc.accRepo(userRepo, auditFlowRepo);
        tx.runTran(() -> {
            skipDmSvc.startSkip(flow.currentLevel() + 1);
            opLogDao.batchSaveLogDomain(skipDmSvc.getLogs());
        });
        if (skipDmSvc.isEnd()) {
            new ThisStageEnded(cycleEval, empEval, TalentStatus.RESULTS_AUDITING).fire();
            return;
        }
        // 更新负责人信息
        List<KpiEmp> reviewers = kpiEmpDao.listByEmp(empEval.getCompanyId(), skipDmSvc.getRecEmpId());
        empEval.reviewers(reviewers);
        tx.runTran(() -> userRepo.updateTaskUser(empEval));
        if (skipDmSvc.isSingle()) {
            log.debug(" =============taskUserId():{}", JSONUtil.toJsonStr(taskUserId));
            log.debug(" =============flow.nextLevelEmpIds():{}", JSONUtil.toJsonStr(flow.nextLevelEmpIds()));
            new MsgTodoAggregate(empEval.getCompanyId(), empEval.getTaskId(), new Name(adminTask.getTaskName()), empEval.getEmpId(), empEval.getId())
                    .useScene(MsgSceneEnum.TASK_RESULT_AUDIT, CompanyMsgActionEnum.RESULT_AUDIT).sendExtMsg().addCenterMsg().sendExtTodo()
                    .addExtTempValue("evalEmpName", empEval.getEvalOrgName() == null ? empEval.getEmpName() : empEval.getEvalOrgName())
                    .addExtTempValue("deadLineDate", empEval.joinDeadLineStr(TalentStatus.RESULTS_AUDITING.getStatus()))
                    .addTodoItem("msg.task.emp", empEval.getEvalOrgName() == null ? empEval.getEmpName() : empEval.getEvalOrgName())
                    .addRecEmpId(flow.nextLevelEmpIds()).fire();
            return;
        }
        //统一校准
        ResultAuditDmSvc resultAuditDmSvc = new ResultAuditDmSvc(empEval, skipDmSvc.getCurLevel(), empEval.getCompanyId().getId(), empEval.getTaskId());
        resultAuditDmSvc.setRepo(auditFlowRepo);
        for (String adminEmpId : skipDmSvc.getRecEmpId()) {
            tx.runTran(() -> resultAuditDmSvc.executeStart(adminEmpId));
            resultAuditDmSvc.refreshSummaryFinishCnt(adminEmpId, skipDmSvc.getCurLevel());
        }
        if (resultAuditDmSvc.needSend()) {
            new ResultCollectMsgTodoEvent(empEval.getCompanyId().getId(), resultAuditDmSvc.getSendRater(), adminTask).fire();
        }
    }

    /**
     * 开始发送绩效校准审核汇总通知待办
     *
     * @param raterSend
     * @param companyId
     * @param adminTask
     */
    public void startSendResultAuditCollectMsgTodo(List<ResultAuditFlowNodeRater> raterSend, String companyId, AdminTask adminTask) {
        if (CollUtil.isNotEmpty(raterSend)) {
            raterSend = raterSend.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                    new TreeSet<>(Comparator.comparing(ResultAuditFlowNodeRater::getAuditEmpId))), ArrayList::new));
            log.debug("startSendResultAuditCollectMsgTodo.raterSend----------:{}", JSONUtil.toJsonStr(raterSend));
            //系统待办还是分开，钉钉通知与钉钉待办按审核人合并一条发送
            for (ResultAuditFlowNodeRater rater : raterSend) {
                rater.getFlowUsers().forEach(user -> {
                    Boolean isProcessed = empEvalDao.isProcessedResultAudit(new TenantId(companyId), user.getTaskUserId(), rater.getAuditEmpId(), user.getCurLevel());
                    if (isProcessed) {
                        user.setIsSendUser(false);
                    }
                });
                if (rater.getFlowUsers().stream().allMatch(user -> user.notSend())) {
                    continue;
                }
                log.debug(" =============rater:{}", JSONUtil.toJsonStr(rater));
                // 给校准人发通知与消息
                String dingId = calibratedMsgSender.collectNotifyResultAudit(rater, adminTask.getTaskName(), adminTask.getId());
                //发送系统待办(过滤已发送过待办的数据)
                log.debug(" =============rater.getFlowUsers():{}", JSONUtil.toJsonStr(rater.getFlowUsers()));
                List<CompanyMsgCenter> todos = new ArrayList<>();
                for (ResultAuditFlowUser flowUser : rater.getFlowUsers()) {
                    if (flowUser.notSend()) {
                        continue;
                    }
                    MsgTodoAggregate todoAgg = new MsgTodoAggregate(new TenantId(companyId), adminTask.getId(),
                            new Name(adminTask.getTaskName()), "", flowUser.getTaskUserId())
                            .useScene(MsgSceneEnum.TASK_RESULT_AUDIT, CompanyMsgActionEnum.RESULT_AUDIT).addCenterMsg()
                            .addRecEmpId(Arrays.asList(rater.getAuditEmpId()));
                    todoAgg.build();
                    CompanyMsgCenter cloneTodo = todoAgg.getCenterMsg().clone();
                    cloneTodo.setEmpId(rater.getAuditEmpId());
                    cloneTodo.appExtTodoId(dingId);
                    todos.add(cloneTodo);
                }
                companyMsgCenterRepo.saveBatch(todos);
            }
        }
    }


    public PagedList<EvalInterviewPo> pagedEvalInterview(EvalInterviewQuery query) {
        query.setAdminOrgIds(kpiOrgDao.listAdminScopePrivOrgIds(query.getCompanyId(), query.getOpEmpId(), null));
        return taskUserDao.pagedEvalInterview(query);
    }

    public void sendAutoItemInputFinishValueMsg(TenantId companyId, String taskUserId) {
        CycleEval cycleEval = taskRepo.getMergeCycleEval(companyId, taskUserId);
        EvalUser taskUser = userRepo.getTaskUser(companyId, taskUserId);
        List<EvalKpi> kpis = taskUser.getKpis().stream().filter(k -> !BusinessConstant.NO.equals(k.getResultInputType())).collect(Collectors.toList());
        List<EvalKpi> autoItems = kpis.stream().filter(kpi -> ScoreTypeEnum.AUTO.getType().equals(kpi.getScorerType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(autoItems)) {
            return;
        }
        CompanyConf companyConf = companyDao.findCompanyConf(companyId);
        if (!companyConf.canResSubmitInputFinish()) {
            return;
        }
        Map<String, List<String>> inputEmpMap = new HashMap<>();
        for (EvalKpi evalKpi : autoItems) {
            setResultInputItem(inputEmpMap, evalKpi);
        }
        inputEmpMap.forEach((receiveId, kpiItemIdList) -> {
            MsgTodoAggregate msgSend = new MsgTodoAggregate(taskUser.getCompanyId(), taskUser.getTaskId(), cycleEval.getTaskName(), taskUser.getEmpId(), taskUser.getId())
                    .addExtTempValue("empName", taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                    .addTodoItem("msg.task.emp", taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                    .useScene(MsgSceneEnum.TASK_SUBMIT_AUTO_ITEM_PROGRESS, CompanyMsgActionEnum.SUBMIT_PROGRESS);
            if (companyConf.isEnableResultInputSendMsg()) {
                msgSend.addCenterMsg().sendExtMsg().sendExtTodo().addRecEmpId(receiveId).publish();//全部发送
            } else {
                msgSend.addCenterMsg().publish();//只发内部待办
            }
        });
    }

    /**
     * 重新结束当前流程阶段
     */
    public void anewStageEnded(String taskUserId, String curStatus) {
        String companyId = taskUserDao.getCompanyId(taskUserId);
        EvalUser empEval = userRepo.getEmpEval(new TenantId(companyId), taskUserId);
        if (empEval.wasTempTask()) {
            CycleEval cycleEval = taskRepo.getMergeCycleEval(new TenantId(companyId), empEval.getId());
            new ThisStageEnded(cycleEval, empEval, builderStatus(curStatus)).publish();
        } else {
            EmpEvalMerge evalMerge = empRuleRepo.getEmpEvalMerge(new TenantId(companyId), taskUserId, all);
            //CycleEval cycleEval = taskRepo.getMergeCycleEval(new TenantId(companyId), empEval.getId());
            new ThisStageEnded(evalMerge, empEval, builderStatus(curStatus), empEval.getCreatedUser()).publish();
        }

        return;
    }

    public TalentStatus builderStatus(String curStatus) {
        if (!"".equals(curStatus) && curStatus != null) {
            if ("created".equals(curStatus)) {
                return TalentStatus.CREATED;
            }
            if ("confirming".equals(curStatus)) {
                return TalentStatus.CONFIRMING;
            }
            if ("confirmed".equals(curStatus)) {
                return TalentStatus.CONFIRMED;
            }
            if ("scoring".equals(curStatus)) {
                return TalentStatus.SCORING;
            }
            if ("resultsAuditing".equals(curStatus)) {
                return TalentStatus.RESULTS_AUDITING;
            }
            if ("resultsInterview".equals(curStatus)) {
                return TalentStatus.RESULTS_INTERVIEW;
            }
            if ("resultsAffirming".equals(curStatus)) {
                return TalentStatus.RESULTS_AFFIRMING;
            }
            if ("waitPublish".equals(curStatus)) {
                return TalentStatus.WAIT_PUBLISHED;
            }
            if ("resultsAppeal".equals(curStatus)) {
                return TalentStatus.RESULTS_APPEAL;
            }
        }
        return null;
    }


    @Transactional
    public void recoverTerminated(List<String> taskUserIds, String companyId, String taskId) {
        if (CollUtil.isEmpty(taskUserIds)) {
            taskUserIds = taskUserDao.listTaskUserIdByTask(companyId, taskId);
        }
        List<EvalUser> evalUsers = taskUserDao.listTerminatedEvalUser(new TenantId(companyId), taskUserIds);
        if (CollUtil.isEmpty(evalUsers)) {
            return;
        }
        centerRepo.recoverTerminated(new TenantId(companyId), evalUsers);
        for (EvalUser evalUser : evalUsers) {
            userRepo.updateTaskUser(evalUser);
            for (MsgTodoInfo info : evalUser.getInfos()) {
                cycleEvalDmSvc.batchMsgTodoTask(info);
            }
        }

    }


    private void setResultInputItem(Map<String, List<String>> inputEmpMap, EvalKpi evalKpi) {
        String resultInputIds = evalKpi.getResultInputEmpId();
        if (BusinessConstant.EXAM.equals(evalKpi.getResultInputType())) {
            //被考核人自己录入完成值
            resultInputIds = evalKpi.getEmpId();
        }

        if (StrUtil.isNotEmpty(resultInputIds)) {
            Arrays.asList(resultInputIds.split(",")).forEach(scoreId -> {
                if (inputEmpMap.get(scoreId) == null) {
                    inputEmpMap.put(scoreId, new ArrayList<>());
                }
                inputEmpMap.get(scoreId).add(evalKpi.getKpiItemId());
            });
        }
    }

    //我协同的任务=我参与的
    public List<PerfEvaluateTaskUserDo> listMyCotaskingEval(String companyId, String taskId, String opEmpId) {
        return taskUserDao.listMyCotaskingEval(companyId, taskId, opEmpId);
    }

    public Set<String> listReScoringType(String companyId, String taskUserId, String opEmpId) {
        EvalUser user = userRepo.getBaseTaskUser(new TenantId(companyId), taskUserId);
        //控制改分权限 评分阶段可以改分，没有人审核可以改分
        Set<String> types = taskUserDao.listFinishedScoreType(companyId, taskUserId, opEmpId);
        if (user.onScoring()) {
            return types;
        }
        String scene = EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene();
        //如果当前在校准环节，且没有人进行校准
        if (user.onReScoring() && !taskUserDao.hasAuditedResults(companyId, taskUserId, scene, opEmpId)) {
            return types;
        }
        return Collections.emptySet();
    }

    public Set<String> listReScoringTypeV3(String companyId, String taskUserId, String opEmpId) {
        EvalUser user = userRepo.getBaseTaskUser(new TenantId(companyId), taskUserId);
        //控制改分权限 评分阶段可以改分，没有人审核可以改分
        EmpEvalScorer scorer = empEvalScorerDao.getEmpEvalScorerByUserId(companyId, taskUserId, opEmpId);
        if (Objects.isNull(scorer)) {
            return Collections.emptySet();
        }
        Set<String> types = scorer.existsFinishedNScorerType();
        log.info("listReScoringTypeV3,types:{}",types);
        if (user.onScoring()) {
            return types;
        }
        String scene = EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene();
        //如果当前在校准环节，且没有人进行校准
        if (user.onReScoring() && !taskUserDao.hasAuditedResults(companyId, taskUserId, scene, opEmpId)) {
            return types;
        }
        return Collections.emptySet();
    }

    // region 员工考核任务的面谈，评论，操作记录
    public String addTaskCoach(TenantId tenantId, PerfEvaluateTaskCoach coach, String opEmpId, boolean isSendNotice) {

        if (coach.isNew()) {
            KpiEmployee opEmp = kpiEmpDao.findEmployee(tenantId, opEmpId);
            coach.operatedBy(opEmp);
        }

        String id = this.taskRepo.saveTaskCoach(coach);

        if (!isSendNotice) {
            //不用发通知的情况
            return id;
        }

        //准备发通知
        EvalUser empEval = userRepo.getBaseTaskUser(tenantId, coach.getTaskUserId());
        if (empEval == null || StrUtil.isEmpty(empEval.getEmpId())) {
            return StrUtil.EMPTY;
        }

        if (StrUtil.equals(opEmpId, empEval.getEmpId())) {
            //不用给自己发通知
            return id;
        }

        PerfEvaluateTaskBaseDo task = taskDao.getTaskById(new TaskId(empEval.getTaskId()));
        MsgTodoAggregate aggregate = new MsgTodoAggregate(tenantId, task.getId(),
                new Name(task.getTaskName()), empEval.getEmpId(), coach.getTaskUserId())
                .useScene(MsgSceneEnum.TASK_COACH_CREATED, null).addCenterMsg()
                .addExtTempValue("coachId", id)
                .addExtTempValue("orgId", "")
                .addRecEmpId(empEval.getEmpId());
        aggregate.sendExtMsg();
        aggregate.sendExtTodo();
        aggregate.publish();
        return id;
    }

    public void readTaskCoach(TenantId tenantId, String coachId, String opEmpId) {
        PerfEvaluateTaskCoach coach = this.taskRepo.getTaskCoach(tenantId, coachId);
        EvalUser empEval = userRepo.getBaseTaskUser(tenantId, coach.getTaskUserId());
        if (!empEval.isSelf(opEmpId)) {
            return;
        }
        this.taskRepo.readTaskCoach(coachId);

        //取消代办
        new CancelTodoEvent(tenantId, new EmpId(opEmpId), empEval.getId(),
                MsgSceneEnum.TASK_COACH_CREATED.getType()).publish();
    }

    public Integer readTaskDiscuss(String companyId, String taskUserId, String kpiItemId) {
        return taskRepo.readTaskDiscuss(companyId, taskUserId, kpiItemId);
    }

    public List<PerfEvaluateTaskCoach> listTaskCoach(EmpEvalCoachQuery query) {
        return this.taskCoachDao.listTaskCoach(query);
    }

    public void taskCoachNotice(TenantId tenantId, String taskUserId, String coachId) {
        EvalUser empEval = userRepo.getBaseTaskUser(tenantId, taskUserId);
        if (empEval == null || StrUtil.isEmpty(empEval.getEmpId())) {
            return;
        }

        CycleEval task = evalTaskDao.getTaskBase(tenantId, empEval.getTaskId());
        MsgTodoAggregate aggregate = new MsgTodoAggregate(tenantId, task.getId(),
                task.getTaskName(), empEval.getEmpId(), taskUserId)
                .useScene(MsgSceneEnum.TASK_COACH_CREATED, null).addCenterMsg()
                .addExtTempValue("coachId", coachId)
                .addExtTempValue("org", "")
                .addRecEmpId(empEval.getEmpId());
        aggregate.sendExtMsg();
        aggregate.publish();
    }

    public TotalCoachCntPo coachCnt(EmpEvalCoachQuery query) {
        query.setAdminOrgIds(kpiOrgDao.listAdminScopePrivOrgIds(query.getCompanyId(), query.getCreatedUser(), null));
        return this.taskCoachDao.coachCnt(query);
    }

    public String saveTaskDiscuss(TenantId tenantId, String opEmpId, PerfEvaluateTaskDiscuss discuss) {
        KpiEmployee opEmp = kpiEmpDao.findEmployee(tenantId, opEmpId);
        discuss.operatedBy(opEmp);
        String id = this.taskRepo.saveTaskDiscuss(discuss);
        EvalUser taskUser = userRepo.getBaseTaskUser(tenantId, discuss.getTaskUserId());
        if (Objects.nonNull(taskUser)) {
            PerfEvaluateTaskBaseDo task = taskDao.getTaskById(new TaskId(taskUser.getTaskId()));
            if (Objects.nonNull(task)) {
                //发送通知  如果是被考核人自己，不发送通知
                if (!taskUser.getEmpId().equals(opEmpId)) {
                    new MsgTodoAggregate(tenantId, task.getId(), new Name(task.getTaskName()),
                            taskUser.getEmpId(), taskUser.getId())
                            .useScene(MsgSceneEnum.EVAL_REVIEW_INFORM, CompanyMsgActionEnum.EVAL_REVIEW_INFORM, id)
                            .addExtTempValue("reviewEmp", opEmp.getName())
                            .addExtTempValue("reviewContent", discuss.getReviewContent())
                            .addExtTempValue("reviewId", id)
                            .sendExtMsg().addRecEmpId(taskUser.getEmpId()).publish();
                }
            }
        }
        return id;
    }

    public List<TaskDiscussPo> listTaskDiscuss(EmpEvalDiscussQuery query) {
        //查主评论
        query.asMainType();
        ListWrap<PerfEvaluateTaskDiscuss> mainDiscuss = this.taskCoachDao.listTaskDiscuss(query);

        //查所有主评论的回复
        query.asReplyType();
        ListWrap<PerfEvaluateTaskDiscuss> replyDiscuss = this.taskCoachDao.listTaskDiscuss(query);
        //回复根据主评论 id 分组
        replyDiscuss.groupBy(PerfEvaluateTaskDiscuss::getMainDiscussId);

        List<TaskDiscussPo> discuss = new ArrayList<>();
        for (PerfEvaluateTaskDiscuss main : mainDiscuss.getDatas()) {
            TaskDiscussPo po = new TaskDiscussPo(main);
            po.appendReplies(replyDiscuss.groupGet(main.getId()));
            discuss.add(po);
        }

        return discuss;
    }

    public List<OperationLogPo> listTaskLog(TenantId companyId, String taskUserId, String opAdminType) {
        List<OperationLogPo> logs = this.taskCoachDao.queryTaskLog(companyId.getId(), taskUserId);
        if (CollUtil.isEmpty(logs)) {
            return logs;
        }

        EvalUser empEval = userRepo.getBaseTaskUser(companyId, taskUserId);
        if (empEval == null) {
            return logs;
        }
        //新人没有周期 id =0 或null
        if (empEval.getCycleId() == null) {
            return logs;
        }
        //管理员和应用管理员无需匿名
        if (Arrays.asList(AdminType.MAIN, AdminType.CHILD, AdminType.APP).contains(opAdminType)) {
            return logs;
        }
        //设置互评人匿名
        AdminTask adminTask = adminTaskRepo.getAdminTask(companyId, empEval.getTaskId());
        if (adminTask == null || adminTask.getScoreView() == null) {
            return logs;
        }

        ScoreViewConf conf = adminTask.getScoreView();
        for (OperationLogPo logPo : logs) {
            if (conf.isAppointAnon()) {
                logPo.appointAnonymous();
            }
            if (conf.isSuperiorScoreAnon()) {
                logPo.supAnonymous();
            }
            if (conf.isMutualScoreAnon(opAdminType)) {
                logPo.anonymous();
            }
        }

        return logs;
    }

    public void terminateEmpEval(String companyId, String opEmpId, String opAdminType, List<String> taskUserIds) {
        List<EvalUserOfTask> userOfTasks = taskUserDao.listEvalUserOfTask(companyId, taskUserIds);
        tx.runTran(() -> {
            userRepo.terminateEmpEval(companyId, opEmpId, opAdminType, userOfTasks.get(0).getTaskId(), taskUserIds);
            //终止关联的问卷
            askEvalAcl.terminatedAskEval(companyId, taskUserIds, opEmpId);
            // 更新任务人数
            taskAppSvc.refreshTaskCnt(companyId, userOfTasks.get(0).getTaskId());
        });

        for (EvalUserOfTask userOfTask : userOfTasks) {
            List<CompanyMsgCenter> progressTodos = centerRepo.finishByQuery(new TenantId(companyId), userOfTask.getTaskUserId());
            new CancelRemoteTodoEvent(new TenantId(userOfTask.getCompanyId()), progressTodos).publish();
            new MsgTodoAggregate(new TenantId(companyId), userOfTask.getTaskId(), new Name(userOfTask.getTaskName()),
                    userOfTask.getEmpId(), userOfTask.getTaskUserId())
                    .useScene(MsgSceneEnum.TASK_STOPPED, CompanyMsgActionEnum.RESULT_APPEAL)
                    .sendExtMsg().addRecEmpId(userOfTask.getEmpId()).publish();
            new TerminateEmpEvalEvnet(companyId, userOfTask.getTaskUserId(), userOfTasks.get(0).getTaskId(), opEmpId).publish();
        }
    }

    public void handerTerminateEmpEval(TerminateEmpEvalEvnet empEvalEvnet) {
        empEvalEvnet.handleStart();
        AdminTask adminTask = adminTaskRepo.getAdminTask(new TenantId(empEvalEvnet.getCompanyId()), empEvalEvnet.getTaskId());
        if (adminTask.isResultCollecSendNotify()) {
            //清除校准审核流程中人员。
            EvalUser evalUser = userRepo.getBaseTaskUser(new TenantId(empEvalEvnet.getCompanyId()), empEvalEvnet.getTaskUserId());
            ResultAuditDmSvc dmSvc = new ResultAuditDmSvc(evalUser, null, empEvalEvnet.getCompanyId(), empEvalEvnet.getOpEmpId());
            dmSvc.setRepo(auditFlowRepo);

            tx.runTran(() -> dmSvc.delResultAuditFlow());
            if (dmSvc.needSend()) {
                new ResultCollectMsgTodoEvent(empEvalEvnet.getCompanyId(), dmSvc.getSendRater(), adminTask).publish();
            }
        }

        EmpEvalMerge evalMerge = empRuleRepo.getEmpEvalMerge(new TenantId(empEvalEvnet.getCompanyId()), empEvalEvnet.getTaskUserId(), all);
        EvalUser taskUser = userRepo.getTaskUser(new TenantId(empEvalEvnet.getCompanyId()), empEvalEvnet.getTaskUserId());
        //处理终止人员考核，移除这个人的评分人实例，刷新汇总对象，判断是否可以关闭待办或者发送汇总待办
        ScorerTodoDmSvc todoDmSvc = new ScorerTodoDmSvc(evalMerge, taskUser);
        if (todoDmSvc.support()) {
            //移除考核涉及的所有评分人实例
            Set<String> scorerIds = scorerSummaryTodoRepo.removeScorerByTaskUser(empEvalEvnet.getCompanyId(), empEvalEvnet.getTaskUserId());
            todoDmSvc.setRepo(scorerSummaryTodoRepo);
            todoDmSvc.refreshSummary(scorerIds);
            List<ScorerTodoSummary> updateSummaries = todoDmSvc.getUpdateSummaries();
            List<ScorerTodoSummary> batchUpdate = todoDmSvc.getUpdateSummaries();
            //进行待办处理
            for (ScorerTodoSummary updateSummary : updateSummaries) {
                if (updateSummary.readyToClose()) { //关闭钉钉待办
                    FinishWorkReq req = new FinishWorkReq(new TenantId(empEvalEvnet.getCompanyId()), new EmpId(updateSummary.getScorerId()), updateSummary.getThirdMsgId());
                    boolean isSuccess = false;
                    try {
                        msgAcl.finishTodoWork(req);
                        isSuccess = true;
                    } catch (Exception e) {
                        log.error("完成待办失败:" + e.getMessage(), e);
                    }
                    if (isSuccess) {
                        updateSummary.setTodoStatus(2);
                    }
                    updateSummary.setUpdatedTime(new Date());
                    batchUpdate.add(updateSummary);
                }

                if (updateSummary.readyToSend()) { //发送钉钉待办，通知，系统待办
                    List<TaskUserScorer> scorers = scorerSummaryTodoRepo.queryNeedSendSystemTodoScorers(updateSummary.getCompanyId(), updateSummary.getTaskId(), updateSummary.getScorerId());
                    new ScoreSummaryMsgTodoEvent(updateSummary.getCompanyId(), scorers, updateSummary).publish();
                }
            }
            scorerSummaryTodoRepo.batchUpdateSummaries(batchUpdate);
        }

    }

    public List<String> listResetNodes(String companyId, String taskId, String taskUserId) {
        AdminTask adminTask = adminTaskRepo.getAdminTask(new TenantId(companyId), taskId);
        EvalUser evalUser = StringUtils.isNotBlank(taskUserId) ? userRepo.getTaskUser(new TenantId(companyId), taskUserId) : null;
        boolean isHavNoPassFinishValueAudit = false;
        if (Objects.nonNull(evalUser)) {
            evalUser.setEmpEvalRule(empEvalDao.getEmpEvalRule(new TenantId(companyId), taskUserId));
            isHavNoPassFinishValueAudit = CollUtil.isNotEmpty(taskUserDao.listResultByScoreTypes(new TenantId(companyId), evalUser.getId(), Arrays.asList("finish_value_audit"), false));
        }
        return adminTask.openNodes(evalUser, isHavNoPassFinishValueAudit);
    }

    public Set<EmpResetPo> listResetEmp(String companyId, String taskUserId) {
     //   return taskUserDao.listResetEmp(companyId, taskUserId);
        return taskUserDao.listResetEmpV3(companyId, taskUserId);
    }

    public List<EvalNoFinishValuePo> listNoFinishValueEval(String companyId, List<String> taskUserIds) {
        List<PerfEvaluateTaskKpiPo> list = taskUserDao.listNoFinishValueEval(companyId, taskUserIds);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        List<EvalNoFinishValuePo> res = new ArrayList<>();
        Map<String, List<PerfEvaluateTaskKpiPo>> taskUserMap = list.stream().collect(Collectors.groupingBy(PerfEvaluateTaskKpiPo::getTaskUserId));
        taskUserMap.forEach((k, v) -> {
            EvalNoFinishValuePo po = new EvalNoFinishValuePo(k, v.get(0).getEmpName(), v);
            res.add(po);
        });
        return res;
    }

    public EvalKpi getKpi(String companyId, String taskUserId, String kpiItemId) {
        return kpiDao.getEvalKpi(companyId, taskUserId, kpiItemId);
    }

    public List<ItemScorePo> getItemScoreDetail(KpiItemQuery query) {
        EvalUser taskUser = userRepo.getBaseTaskUser(new TenantId(query.getCompanyId()), query.getTaskUserId());
        EmpEvalRule evalRule = empEvalDao.getBaseEvalRule(new TenantId(query.getCompanyId()), query.getTaskUserId());
        List<NodeWeight> nodeWeights = kpiDao.listNodeWeightOfItem(evalRule, query.getCompanyId(), query.getTaskUserId(), query.getKpiItemId());
        //List<NodeWeight> nodeWeight = userRepo.listEmpEvalNodeWeight(query.getCompanyId(), query.getTaskUserId(), query.getKpiItemId());
        return kpiDao.getItemScoreDetail(query, nodeWeights, taskUser.isOpenAvgWeightCompute());
    }

    @Autowired
    protected EvalContextRepo contextRepo;
    @Autowired
    protected ConfirmFlowRepo confirmFlowRepo;


    public List<ItemScorePo> getItemScoreDetailV3(EvalKpi kpi,KpiItemQuery query) {
        EmpEvalKpiType kpiType = kpiDao.getEmpEvalKpiType(new TenantId(query.getCompanyId()), query.getTaskUserId(),kpi.getKpiTypeId());
        if (kpiType == null || !kpiType.isEvalScore()){//非指标评分不显示指标评价
            return new ArrayList<>();
        }
        EvalUser taskUser = userRepo.getBaseTaskUser(new TenantId(query.getCompanyId()), query.getTaskUserId());
        EmpEvalRule evalRule = empEvalDao.getBaseEvalRule(new TenantId(query.getCompanyId()), query.getTaskUserId());
        List<NodeWeight> nodeWeights = kpiDao.listNodeWeightOfItem(evalRule, query.getCompanyId(), query.getTaskUserId(), query.getKpiItemId());
        return kpiDao.getItemScoreDetailV3(kpi,query, nodeWeights, taskUser.isOpenAvgWeightCompute());
    }

    @Transactional
    public void rejectConfirmItem(String companyId, String opEmpId, String taskUserId, String rejectReason) {
        TenantId tenantId = new TenantId(companyId);
        EvalUser taskUser = userRepo.getBaseTaskUser(tenantId, taskUserId);
        TaskEvalContext context = contextRepo.listStartEvalContext(tenantId, Arrays.asList(taskUserId), 0).get(0);
        ConfirmTaskFlow confirmFlow = confirmFlowRepo.getConfirmFlow(context.getEvals().get(0).getRule().getConfirmTask(), tenantId, taskUserId);
        ConfirmNode fromNode = confirmFlow.getRejectNoe(opEmpId);

        List<String> curEmpIds = fromNode.listScorerId();
        curEmpIds.add(opEmpId);//兼容下转交后的人，需要清除待办
        //先清理本地的待办,再异步清远端的.
        new ClearTodoDmsSvc(centerRepo).clear(tenantId, taskUserId, MsgSceneEnum.confirmScene, curEmpIds);

        ConfirmNode confirmNode = confirmFlow.reject2Up(opEmpId);
        if (null == confirmNode){
            throw new KpiI18NException("10002", "上一级指标确认人空缺，无法驳回");
        }
        userRepo.rejectConfirmItem(companyId, opEmpId, taskUserId, rejectReason, confirmNode.getApprovalOrder());

        List<EvalUserOfTask> evalUserOfTasks = taskUserDao.listEvalUserOfTask(companyId, Arrays.asList(taskUserId));
        EvalUserOfTask evalUserOfTask = evalUserOfTasks.get(0);
        List<String> upScorerIds = confirmNode.listScorerId();//上一层级的审批人
        List<KpiEmp> reviewers = empDao.listByEmp(new TenantId(companyId), upScorerIds);
//        List<KpiEmp> reviewers = taskUserDao.listRatersByOrderLevel(taskUser.getCompanyId(), taskUser.getId(), EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT.getScene(), confirmNode.getApprovalOrder());
        taskUser.curStatus(TalentStatus.CONFIRMING, reviewers);//负责人信息
        userRepo.updateTaskUser(taskUser);

        //驳回通知与待办
        List<String> receiverIds = reviewers.stream().map(kpiEmp -> kpiEmp.getEmpId()).collect(Collectors.toList());
        new MsgTodoAggregate(tenantId, evalUserOfTask.getTaskId(), new Name(evalUserOfTask.getTaskName()), evalUserOfTask.getEmpId(), evalUserOfTask.getTaskUserId())
                .useScene(MsgSceneEnum.TASK_CONFIRM_REJECT, CompanyMsgActionEnum.CONFIRM)
                .sendExtMsg().addCenterMsg().sendExtTodo().addRecEmpId(receiverIds)
                .addExtTempValue("evalEmpName", taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                .addExtTempValue("rejectEmpName", empDao.findEmp(tenantId, opEmpId).getName())
                .addExtTempValue("rejectReason", rejectReason)
                .addTodoItem("msg.task.emp", taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                .fire();
    }

    public void batchResultAudit(List<ResultAuditCmd> cmds) {
        for (ResultAuditCmd cmd : cmds) {
            doResultAudit(cmd);
        }
    }

    // 高级校准按照等级组提交，需要查询当前等级组下面所有的考核人
    public void advancedAudit(CalibratedCmd calibratedCmd) {
        CalibrateTaskUserQry qry = new CalibrateTaskUserQry();
        qry.setCycleId(calibratedCmd.getCycleId());
        qry.setDistributionId(calibratedCmd.getDistributionId());
        qry.setTaskId(Arrays.asList(calibratedCmd.getTaskId()));
        qry.setPageSize(Integer.MAX_VALUE);
        qry.accOp(calibratedCmd.getCompanyId(), calibratedCmd.getEmpId());
        Cycle cycle = cycleDao.find(qry.getCompanyId(), qry.getCycleId());
        PagedList<ResultAuditTaskUserPo> res = null;
        if (cycle.wasOnEmpMod()) {
            res = calibratedDao.pagedCalibrateTaskUserOnEmp(qry);
        } else if (cycle.wasOnTaskMod()) {
            res = calibratedDao.pagedCalibrateTaskUserOnTask(qry);
        }
        List<String> userIds = CollUtil.map(res, u -> u.getTaskUserId(), true);
        ListWrap<RefEvalPo> refEvalPos = taskUserDao.listAllRefEvalPo(qry.getCompanyId(), userIds);
        for (ResultAuditTaskUserPo data : res.getData()) {
            data.setRefEvalScores(refEvalPos.groupGet(data.getTaskUserId()));
        }
        if (CollUtil.isNotEmpty(res)) {
            List<ResultAuditCmd> cmds = calibratedCmd.getCmds();
            List<String> ids = new ArrayList<>();
            if (CollUtil.isNotEmpty(cmds)) {
                ids = cmds.stream().map(s -> s.getId()).collect(Collectors.toList());
            }
            if (CollUtil.isNotEmpty(res)) {
                ResultAuditCmd cmd;
                for (ResultAuditTaskUserPo po : res) {
                    log.debug("res.po:{}", JSONUtil.toJsonStr(po));
                    if (ids.contains(po.getTaskUserId())) {
                        continue;
                    }
                    cmd = new ResultAuditCmd(po.getScoreComment(), po.getStepId(), po.getEvaluationLevel(),
                            po.getTaskUserId(), po.getFinalScore(), po.getTypes(), po.getPerfCoefficient());
                    log.debug("cmds.cmd:{}", JSONUtil.toJsonStr(cmd));
                    cmds.add(cmd);
                }
            }
            log.debug("cmds:{}", JSONUtil.toJsonStr(cmds));
            for (ResultAuditCmd cmd : cmds) {
                cmd.accOp(new TenantId(calibratedCmd.getCompanyId()), new EmpId(calibratedCmd.getEmpId()));
                doResultAudit(cmd);
            }
        }
    }

    @Transactional
    public void rejectChangeItem(String companyId, String opEmpId, String taskUserId, String reason) {
        EvalUser user = userRepo.getBaseTaskUser(new TenantId(companyId), taskUserId);
        EmpEvalMerge merge = empRuleRepo.getEmpEvalMerge(new TenantId(companyId), taskUserId, all);
        userRepo.rejectChangeItem(companyId, opEmpId, taskUserId, reason);
        List<EvalUserOfTask> evalUserOfTasks = taskUserDao.listEvalUserOfTask(companyId, Arrays.asList(taskUserId));

        new CancelTodoEvent(new TenantId(companyId), taskUserId, MsgSceneEnum.CHANGE_ITEM_AUDIT.getType()).publish();
        EvalUserOfTask evalUserOfTask = evalUserOfTasks.get(0);
        //驳回通知
        //驳回通知发送给变更发起人
        OperationLogDo logDo = opLogDao.findLastLog(new TenantId(companyId), taskUserId, "change_item");
        if (Objects.nonNull(logDo)) {
            new MsgTodoAggregate(new TenantId(companyId), evalUserOfTask.getTaskId(), new Name(evalUserOfTask.getTaskName()), evalUserOfTask.getEmpId(), evalUserOfTask.getTaskUserId())
                    .useScene(MsgSceneEnum.CHANGE_ITEM_REJECT)
                    .addExtTempValue("evalEmpName", user.getEmpName())
                    .addExtTempValue("rejectEmpName", empDao.findEmp(new TenantId(companyId), opEmpId).getName())
                    .addExtTempValue("rejectReason", reason)
                    .sendExtMsg().addRecEmpId(logDo.getCreatedUser()).publish();
        }

        Set<String> inputEmpIds = new HashSet<>(merge.inputEmpIds());
        log.info(" === 变更审核驳回 ===给录入完成值发送待办 inputEmpIds：{} ", JSONUtil.toJsonStr(inputEmpIds));
        //更新列表责任人
        List<KpiEmp> reviewers = kpiEmpDao.listByEmp(new TenantId(companyId), inputEmpIds);
        user.curStatus(TalentStatus.CONFIRMED, reviewers);//负责人信息
        userRepo.updateTaskUser(user);
        if (CollUtil.isEmpty(inputEmpIds)) {
            return;
        }
        //取消旧的录入完成值代办
        new CancelTodoEvent(new TenantId(companyId), taskUserId, MsgSceneEnum.TASK_SUBMIT_PROGRESS.getType()).publish();
        //指标更变后 录入人可能更新，重新给这批录入人生成代办
        new EvalInputNotifySend(merge.getInputNotify(), user, merge.taskName(), inputEmpIds).publish();
    }

    public List<BatchFinishValuePo> listFinishValue(FinishValueQuery query) {
        CompanyConf companyConf = companyDao.findCompanyConf(new TenantId(query.getCompanyId()));
        return kpiDao.queryFinishValueList(query, companyConf);
    }

    public void downLoadInputFinishTemp(HttpServletRequest request, HttpServletResponse response, FinishValueQuery
            query) {
        try {
            ClassPathResource resource = new ClassPathResource(FileUtil.getTemplatePath(ExcelTemplateEnum.INPUT_FINISH_VALUE.getTemplateName()));
            log.info("下载批量录入完成值模板templatePath={}", resource.getPath());
            String fileName = ExcelTemplateEnum.INPUT_FINISH_VALUE.getFileName() + DateTimeUtils.date2StrDate(new Date(), DateTimeUtils.FORMAT_yyyyMMddHHmmss) + BusinessConstant.XSSF_PREFIX;
            List<BatchFinishValuePo> data = listFinishValue(query);
            ExcelUtil.formatResponse(request, response, fileName);
            EasyExcel.write(response.getOutputStream()).withTemplate(resource.getInputStream()).sheet().doFill(data);
            log.info("下载批量录入完成值模板完成");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List<EvaluateTaskAppealItemPo> getTaskAppealItemList(String companyId, String taskUserId) {
        List<EvaluateTaskAppealItemPo> itemPos = new ArrayList<>();
        List<EvalKpi> kpis = userRepo.listTaskKpis(new TenantId(companyId), taskUserId);
        // 总分项目
        EvaluateTaskAppealItemPo itemPo = new EvaluateTaskAppealItemPo();
        itemPo.setItemType("totalScore");
        itemPo.setItemName("考核总分");
        itemPos.add(itemPo);
        // 指标类别列表
        Set<EvaluateTaskAppealItemPo> kpiClassItemList = new HashSet<>();
        // 指标项列表
        Set<EvaluateTaskAppealItemPo> kpiItemList = new HashSet<>();
        for (EvalKpi kpi : kpis) {
            EvaluateTaskAppealItemPo classItem = new EvaluateTaskAppealItemPo();
            classItem.setItemType("kpiClass");
            classItem.setItemName(kpi.getKpiTypeName());
            EvaluateTaskAppealItemPo kpiItem = new EvaluateTaskAppealItemPo();
            kpiItem.setItemType("kpiItem");
            kpiItem.setItemName(kpi.getKpiItemName());
            kpiClassItemList.add(classItem);
            kpiItemList.add(kpiItem);
        }
        itemPos.addAll(kpiClassItemList);
        itemPos.addAll(kpiItemList);
        return itemPos;
    }

    public List<InviteItem> listNeedSetMutualItem(TaskMutualItemQuery query) {
        return taskItemScoreRuleDao.listMutualItem(query);
    }

    public PagedList<EvalKpi> pageTaskKpiItem(TaskKpiItemQuery query) {
        return kpiDao.pageTaskKpiItem(query);
    }

    public List<MutualEmpPo> listMutualAuditEmp(MutualEmpQuery query) {
        return companyMsgCenterDao.listMutualAuditEmp(query.getCompanyId(), query.getTaskUserId(),
                MsgSceneEnum.SET_MUTUAL_AUDIT.getType());
    }

    public PagedList<WorkWaitMsgPo> pagedWaitResultAudit(WorkWaitMsgQuery query) {
        if (StringUtils.isNotBlank(query.getOrgId())) {
            query.setOrgIds(kpiOrgDao.listAllChildOrgIds(new TenantId(query.getCompanyId()), Arrays.asList(query.getOrgId().split(","))));
        }
        //ps:双data数据格式，前端影响范围大暂时不改
        return taskAuditDao.pagedWaitResultAudit(query);
    }


    public List<TaskKpiItemPo> listWaitInputAutoItem(TaskKpiItemQuery query) {
        CompanyConf companyConf = companyDao.findCompanyConf(new TenantId(query.getCompanyId()));
        if (!companyConf.canResSubmitInputFinish()) {
//            return ResponseInfo.error(" 未配置可持续更新自动打分指标配置 ");
            return null;
        }
        return taskKpiItemDao.listWaitInputAutoItem(query);
    }

    public List<ItemDynamicPo> listItemDynamic(String taskUserId, String kpiItemId) {
        return opLogDao.queryItemDynamic(taskUserId, kpiItemId);
    }

    public List<PerfEvaluateTaskFile> listItemFile(String taskUserId, String kpiItemId) {
        return taskKpiItemDao.listItemFile(taskUserId, kpiItemId);
    }

    public List<ItemScorePo> getItemRaterScoreDetail(KpiItemQuery query) {
        NodeWeight nodeWeight = new NodeWeight("item_score", new BigDecimal(100));
        return kpiDao.getItemScoreDetail(query, Arrays.asList(nodeWeight), true);
    }


    public List<ItemScorePo> getItemRaterScoreDetailV3(EvalKpi kpi,KpiItemQuery query) {
        NodeWeight nodeWeight = new NodeWeight("item_score", new BigDecimal(100));
        return kpiDao.getItemScoreDetailV3(kpi,query, Collections.singletonList(nodeWeight), true);
    }
    public void saveTaskFile(String companyId, String createdUser, String taskUserId, String kpiItemId, String
            fileList) {
        List<PerfEvaluateTaskFile> files = JSONArray.parseArray(fileList, PerfEvaluateTaskFile.class);
        if (CollectionUtils.isEmpty(files)) {
            return;
        }
        EvalKpi evalKpi = kpiDao.getEvalKpi(companyId, taskUserId, kpiItemId);
        JSONObject object = new JSONObject();
        object.put("kpiItemName", evalKpi.getKpiItemName());
        for (PerfEvaluateTaskFile file : files) {
            file.setCompanyId(companyId);
            file.setCreatedUser(createdUser);
            file.setTaskUserId(taskUserId);
            file.setKpiItemId(kpiItemId);
            itemFileRepo.saveFile(file);
            object.put("fileName", file.getFileName());
//            logManager.saveOperationLog(companyId, OperationLogSceneEnum.ADD_ITEM_FILE.getScene(),
//                    taskUserId, createdUser, JSONObject.toJSONString(object));
        }
    }

    public void deleteTaskFile(String companyId, String opEmpId, String fileId) {
        PerfEvaluateTaskFile file = itemFileRepo.getTaskFile(companyId, fileId);
        if (Objects.isNull(file)) {
            return;
        }
        itemFileRepo.deleteFile(companyId, fileId, opEmpId);
        EvalKpi evalKpi = kpiDao.getEvalKpi(companyId, file.getTaskUserId(), file.getKpiItemId());
        if (Objects.isNull(evalKpi)) {
            return;
        }
        JSONObject object = new JSONObject();
        object.put("kpiItemName", evalKpi.getKpiItemName());
        object.put("fileName", file.getFileName());
//        logManager.saveOperationLog(file.getCompanyId(), OperationLogSceneEnum.DELETE_ITEM_FILE.getScene(), file.getTaskUserId(), opEmpId, JSONObject.toJSONString(object));
    }

    public void saveItemNotice(PerfEvaluateItemNotice notice) {
        if (StrUtil.isEmpty(notice.getId())) {
            notice.setCreatedUser(notice.getEmpId());
            itemNoticeRepo.add(notice);
        } else {
            notice.setUpdatedUser(notice.getEmpId());
            itemNoticeRepo.update(notice);
        }
    }

    public PerfEvaluateItemNotice getItemNotice(ItemNoticeQuery query) {
        return taskKpiItemDao.getItemNotice(query);
    }

    public PagedList<ItemSimplePo> pagedItemSimpleInfo(ItemSimpleQuery query) {
        if (StringUtils.isNotBlank(query.getOrgId())) {
            List<String> orgIds = kpiOrgDao.listAllChildOrgIds(new TenantId(query.getCompanyId()), Arrays.asList(query.getOrgId().split(",")));
            if (CollUtil.isEmpty(orgIds)) {
                query.setOrgId(String.join(",", orgIds));
            }
        }
        return taskKpiItemDao.pagedFinishedItemSimpleInfo(query);
    }

    //测试接口
    public EmpEvalMerge getEmpEval(TenantId companyId, String taskUserId) {
        return empRuleRepo.getEmpEvalMerge(companyId, taskUserId, all);
    }

    public EvalUser getEvalUser(TenantId companyId, String taskUserId) {
        return userRepo.getEmpEval(companyId, taskUserId);
    }

    public EvalUser getBaseTaskUser(TenantId tenantId, String taskUserId) {
        return userRepo.getBaseTaskUser(tenantId, taskUserId);
    }

    public EvalUser getBaseTaskUserV3(TenantId tenantId, String taskUserId) {
        //使用这个需要判空
        return taskUserDao.getBaseEvalUser(tenantId, taskUserId);
    }

    public AdminTask getAdminTask(TenantId tenantId, String taskId) {
        return adminTaskRepo.getAdminTask(tenantId, taskId);
    }

    public EvalKpiPo getItemDetail(KpiItemQuery query) {
        TenantId tenantId = new TenantId(query.getCompanyId());
        EvalKpiPo itemDetail = kpiDao.getItemDetail(query);
        if (itemDetail == null) {
            return null;
        }
        itemDetail.initFinishValueSource();
        List<PerfEvaluateTaskScoreResultDo> resultDos = empEvalDao.listScoreRs(query.getCompanyId(), query.getTaskUserId(), null, query.getOpEmpId());
        Set<String> scoreTypes = resultDos.stream().map(s -> s.getScorerType()).collect(Collectors.toSet());
        itemDetail.setOpScoreTypes(scoreTypes);
        EmpEvalMerge empEvalMerge = empRuleRepo.getEmpEvalMerge(tenantId, query.getTaskUserId(), type);

        //AdminTask adminTask = adminTaskDao.getAdminTaskMerge(tenantId, new TaskId(query.getTaskId()));
        //ScoreRuleValue scoreRuleValue = adminTask.getScoreRuleValue();
        itemDetail.setScoreView(empEvalMerge.getScoreView());
        itemDetail.setScoreRangeType(empEvalMerge.getScoreValueConf().getScoreRangeType());
        List<PerfEvaluateTaskItemDynamicPo> dynamicPos = opLogDao.listItemDynamic(tenantId.getId(), query.getTaskUserId(), itemDetail.getKpiItemId());
        itemDetail.setInputChangeRecord(dynamicPos);

        EvalUser evalUser = getBaseTaskUser(tenantId, query.getTaskUserId());
        TalentStatus current = TalentStatus.statusOf(evalUser.getTaskStatus());
        this.buildItemExtData(itemDetail, tenantId);
        if (current.beforeEq(TalentStatus.RESULTS_AUDITING)) {
            return itemDetail;
        }
        if (Objects.equals(current.getStatus(), TalentStatus.RESULTS_AFFIRMING.getStatus())) {
            itemDetail.setScoreDetailPriv(empEvalMerge.getConfirmResult().getScoreDetailPriv());
        } else {
            itemDetail.setScoreDetailPriv(empEvalMerge.getPublishResult().getScoreDetailPriv());
        }
        return itemDetail;
    }


    public Cycle findCycle(TenantId tenantId, String cycleId) {
        return cycleEvalDao.findCycle(tenantId, cycleId);
    }


    //执行阶段只能转交录入人
    //public boolean isTransferInputEmp(TaskTransferCmd cmd) {
    //    EvalUser user = userRepo.getBaseTaskUser(cmd.getTenantId(), cmd.getEmpEvalId());
    //    return user.isAppointStage(TalentStatus.CONFIRMED.getStatus());
    //}

    //@Transactional
    //public void transferInputEmp(TaskTransferCmd cmd) {
    //    EvalUser user = userRepo.getBaseTaskUser(cmd.getTenantId(), cmd.getEmpEvalId());
    //    CycleEval taskBase = this.evalTaskDao.getTaskBase(cmd.getTenantId(), user.getTaskId());
    //    ListWrap<EvalKpi> items = kpiDao.listEmpEvalKpiItem(cmd.getTenantId(), cmd.getEmpEvalId());
    //    List<EvalKpi> fromInputItems = items.getDatas().stream().filter(item -> item.inputEmp(user.getEmpId()).contains(cmd.getFromEmpId())).collect(Collectors.toList());
    //    if (CollUtil.isEmpty(fromInputItems)) {
    //        throw new KpiI18NException("task.unableTransfer", "没有可以转交的记录");
    //    }
    //    for (EvalKpi fromInputItem : fromInputItems) {
    //        fromInputItem.replInputEmpId(cmd.getFromEmpId(), cmd.getToEmpId());
    //    }
    //    userRepo.updateKpiItem(fromInputItems);
    //    //更新user责任人
    //    List<KpiEmp> reviewers = kpiEmpDao.listByEmp(cmd.getTenantId(), Arrays.asList(cmd.getToEmpId()));
    //    user.replReviewers(reviewers, cmd.getFromEmpId());
    //    userRepo.updateTaskUser(user);
    //    //formEmp取消代办
    //    new CancelTodoEvent(cmd.getTenantId(), new EmpId(cmd.getFromEmpId()), user.getId(), MsgSceneEnum.TASK_SUBMIT_PROGRESS.getType()).publish();
    //    //toEmp发送代办
    //    new MsgTodoAggregate(cmd.getTenantId(), taskBase.getId(), taskBase.getTaskName(),
    //            user.getEmpId(), user.getId())
    //            .useScene(MsgSceneEnum.TASK_SUBMIT_PROGRESS, CompanyMsgActionEnum.SUBMIT_PROGRESS)
    //            .sendExtTodo().sendExtMsg().addCenterMsg().addRecEmpId(cmd.getToEmpId()).publish();
    //    //日志
    //    String empName = this.kpiEmpDao.getEmpName(cmd.getTenantId(), cmd.getToEmpId());
    //    JSONObject jsonObject = new JSONObject();
    //    jsonObject.put("empName", empName);
    //    jsonObject.put("scorerType", Arrays.asList("input_finish_value"));
    //    OperationLogDo logDo = new OperationLogDo(cmd.getTenantId().getId(), user.getId(), "transfer_task",
    //            cmd.getOpEmpId(), JSONObject.toJSONString(jsonObject));
    //    this.opLogDao.addLog(logDo);
    //}

    @Transactional
    public void skipRater(TaskSkipCmd cmd) {
        // 跳过检验
        List<EmpEvalSkipRatersPo> skipRatersPos = this.querySkipRaters(cmd.getTaskUserId(), cmd.getTenantId().getId());
        if (CollUtil.isEmpty(skipRatersPos)) {
            throw new KpiI18NException("task.unableSkip", "没有可以跳过的记录");
        }
        if (skipRatersPos.size() == 1) {
            throw new KpiI18NException("task.unableSkip", "仅剩一个评价人，无法跳过评分环节，您可转交评价人");
        }

        Map<String, List<EmpEvalSkipRatersPo>> scoreTypeRatersMap = skipRatersPos
                .stream().collect(Collectors.groupingBy(EmpEvalSkipRatersPo::getScoreType));

        scoreTypeRatersMap.keySet().stream().forEach(s -> {
            if (scoreTypeRatersMap.get(s).size() <= 1 && scoreTypeRatersMap.get(s).get(0).getSkipUserId().equals(cmd.getSkipUserId())) {
                throw new KpiI18NException("task.unableSkip", "仅剩一个评价人，无法跳过评分环节，您可转交评价人");
            }
        });

        EmpEvalSkipRatersPo empEvalSkipRatersPo = skipRatersPos.stream()
                .filter(s -> s.getSkipUserId().equals(cmd.getSkipUserId())).collect(Collectors.toList()).get(0);

        if (ObjectUtils.isNotEmpty(empEvalSkipRatersPo.getKpiItemName())) {
            throw new KpiI18NException("task.unableSkip", "'" + empEvalSkipRatersPo.getKpiItemName() + "' 仅剩一个评价人，无法跳过评分环节，您可转交评价人");
        }

        EvalUser user = userRepo.getEmpEval(cmd.getTenantId(), cmd.getTaskUserId());
        List<EvalScoreResult> skipScoreResults = this.empEvalDao.listNotAuditResult(cmd.getTenantId(),
                cmd.getTaskUserId(), cmd.getSkipUserId(), Arrays.asList(cmd.getScoreType()));

        if (CollUtil.isEmpty(skipScoreResults)) {
            throw new KpiI18NException("task.unableSkip", "没有可以跳过的记录");
        }

        EmpEvalMerge empEvalMerge = empRuleRepo.getEmpEvalMerge(cmd.getTenantId(), cmd.getTaskUserId(), item | itemRule);
        TransferOrSkipRaters transferOrSkipRaters = TransferOrSkipRaters.builder()
                .skipUserId(cmd.getSkipUserId())
                .tenantId(cmd.getTenantId())
                .taskUserId(cmd.getTaskUserId())
                .build();

        SkipRaterDmSvc skipRaterDmSvc = new SkipRaterDmSvc(empEvalMerge, transferOrSkipRaters, skipScoreResults, userRepo);
        skipRaterDmSvc.skipRater();
        empEvalMerge.initIndexRaters();
        empRuleRepo.updateTransferScoringIf(empEvalMerge);

        // 重新算分
        if (ObjectUtils.isNotEmpty(transferOrSkipRaters.getNeedReCompute())) {
            scoreStageAppSvc.reCompute(transferOrSkipRaters.getTenantId(), cmd.getTaskUserId(), true);
        }

        MsgSceneConvertor convertor = new MsgSceneConvertor(empEvalMerge.getEvaluateType(),
                skipScoreResults.stream()
                        .map(s -> s.getScorerType())
                        .collect(Collectors.toSet()));
        Set<String> msgScenes = convertor.getScene();

        if (msgScenes.isEmpty()) {
            throw new KpiI18NException("task.unableSkip", "没有可以跳转的记录");
        }

        // 更新负责人
        user.removeReviewers(Arrays.asList(cmd.getSkipUserId()));
        userRepo.updateTaskUser(user);

        // 删除待办
        Set<String> msgScenesArr = new HashSet<>();
        msgScenesArr.addAll(msgScenes);
        msgScenesArr.addAll(MsgSceneEnum.confirmScene);
        List<CompanyMsgCenter> msgCenters = this.companyMsgCenterDao.listNotHandleMsg(cmd.getTenantId(), cmd.getSkipUserId(),
                user.getId(), msgScenesArr);
        if (CollUtil.isNotEmpty(msgScenesArr)) {
            for (CompanyMsgCenter msgCenter : msgCenters) {
                new CancelTodoEvent(cmd.getTenantId(), new EmpId(msgCenter.getEmpId()), msgCenter.getLinkId(), msgCenter.getBusinessScene()).publish();
            }
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("empName", this.kpiEmpDao.getEmpName(cmd.getTenantId(), cmd.getSkipUserId()));
        jsonObject.put("scorerType", skipScoreResults.stream().map(s -> s.getScorerType()).collect(Collectors.toSet()));
        OperationLogDo logDo = new OperationLogDo(cmd.getTenantId().getId(), user.getId(), "skip_task",
                cmd.getOpEmpId(), JSONObject.toJSONString(jsonObject));
        this.opLogDao.addLog(logDo);


        //处理汇总待办的评分人跳过逻辑，移除这个人的评分人实例，刷新汇总对象
        user.setTaskName(user.getTaskName());
        ScorerTodoDmSvc todoDmSvc = new ScorerTodoDmSvc(empEvalMerge, user);
        if (todoDmSvc.support()) {
            Set<String> scorerIds = new HashSet<>();
            scorerIds.add(cmd.getSkipUserId());
            //移除
            scorerSummaryTodoRepo.removeScorer(cmd.getTaskUserId(), cmd.getSkipUserId());
            todoDmSvc.setRepo(scorerSummaryTodoRepo);
            todoDmSvc.refreshSummary(scorerIds);
            List<ScorerTodoSummary> updateSummaries = todoDmSvc.getUpdateSummaries();
            //进行待办处理
            for (ScorerTodoSummary updateSummary : updateSummaries) {
                if (updateSummary.readyToClose()) { //关闭钉钉待办
                    FinishWorkReq req = new FinishWorkReq(user.getCompanyId(), new EmpId(updateSummary.getScorerId()), updateSummary.getThirdMsgId());
                    boolean isSuccess = false;
                    try {
                        msgAcl.finishTodoWork(req);
                        isSuccess = true;
                    } catch (Exception e) {
                        log.error("完成待办失败:" + e.getMessage(), e);
                    }
                    if (isSuccess) {
                        updateSummary.setTodoStatus(2);
                        updateSummary.setUpdatedTime(new Date());
                    }
                }
            }
            scorerSummaryTodoRepo.batchUpdateSummaries(updateSummaries);
        }

    }

    public void skipRaterV3(TaskSkipCmd cmd) {
        //查跳过评分人的评价关系
        SkipScorerDmSvc dmSvc = onScoreEvalRepo.getSkipScorerEmpEval(cmd.getTenantId(),  cmd.getTaskUserId(), cmd.getSkipUserId(),
                cmd.getScoreType(), cmd.getOpEmpId());
        dmSvc.checkAllowSkip(cmd.getSkipUserId(),cmd.getScoreType());   // 跳过检验
        MsgSceneConvertor convertor = new MsgSceneConvertor(dmSvc.getEvalRule().getEvaluateType(), Collections.singletonList(cmd.getScoreType()));
        Set<String> msgScenes = convertor.getScene();
        dmSvc.acceptMsgScenes(msgScenes);
        dmSvc.skipRater(cmd.getSkipUserId(),cmd.getScoreType());//执行跳过
        //保存跳过后的数据
        tx.runTran(() -> {
            onScoreEvalRepo.saveSkipEval(dmSvc.getEvalUser(), dmSvc.getMsgCenters(),dmSvc.getOperationLog()); //保存跳过后任务及日志信息
            empEvalScorerRepo.batchUpAndDelEmpEvalScorer(cmd.getTenantId().getId(), cmd.getOpEmpId(), dmSvc.needSaveUpEmpEvalScorers()); //记录被转交评分人数据
        });
        //存在需要提交的数据(系统提交作为异步执行处理)使用事件处理提交评分
        if (CollUtil.isNotEmpty(dmSvc.getNeedSubmits())) {
            new ScorerSkiped(cmd.getTenantId(), cmd.getOpEmpId(),dmSvc.getEvalUser(),dmSvc.getEvalRule(), cmd.getTaskUserId(),
                    cmd.getSkipUserId(),cmd.getScoreType(), dmSvc.getNeedSubmits(),dmSvc.getMsgCentersWrap(),msgScenes).fire();//发送已跳过事件  // 重新算分 todo 异步事件执行 NeedReCompute
        }
        //存在待提交的节点，不允许清除待办
        if (dmSvc.existsWaitSubmit()){
            return;
        }
        //异步处理
        String tid = MDC.get("tid");
        ScorerTodoDmSvc todoDmSvc = new ScorerTodoDmSvc(dmSvc.getEvalRule(), dmSvc.getEvalUser());
        tx.runAsyn(tid, () -> {
            //清除跳过评分人的待办
            todoDmSvc.clearScorerSkipTodo(cmd.getSkipUserId(),dmSvc.getFromCenterMsgs(),scorerSummaryTodoRepo,scoreMsgAcl);
        });
    }


    /**
     * 转交的逻辑：
     * 1. 当前考核任务 empEvalId （原来是empId）的全部 (scoreResult) 的 fromEmpId 转交给 toEmpId
     * 2. 取消当前 empEval 处在的阶段的 fromEmpId 系统待办和平台待办
     * 3. 发送当前 empEval 处在阶段的的系统待办和平台待办给  toEmpId
     *
     * @param cmd
     */
    @Transactional
    public void transferTask(TaskTransferCmd cmd) {
        //如果没有转出的责任人，说明是责任人自己在操作，取当前操作人
        cmd.verifyEmp();

        //查询待转交的任务s
        EvalUser user = userRepo.getEmpEval(cmd.getTenantId(), cmd.getEmpEvalId());
        if (user == null || user.isCreatedStage()) {
            throw new KpiI18NException("task.unableTransfer", "没有可以转交的记录");
        }

        //查询fromEmpId的评分信息
        List<EvalScoreResult> fromScoreResults = this.empEvalDao.listNotAuditResult(cmd.getTenantId(),
                cmd.getEmpEvalId(), cmd.getFromEmpId(), user.auditScoreTypeConvertor());
        if (CollUtil.isEmpty(fromScoreResults)) {
            throw new KpiI18NException("task.unableTransfer", "没有可以转交的记录");
        }

        //自评不能转交
        HashSet<String> fromEmpScoreTypes = new HashSet<>();
        for (EvalScoreResult result : fromScoreResults) {
            if (result.isSelfNode()) {
                throw new KpiI18NException("task.selfScoreUnableTransfer", "自评不能转交");
            }
            fromEmpScoreTypes.add(result.getScorerType());
        }

        //该人员已存在当前流程中
        List<EvalScoreResult> toEmpResults = this.empEvalDao.listNotTransferredResult(cmd.getTenantId(), cmd.getToEmpId(),
                user.getTaskId(), cmd.getEmpEvalId());
        if (CollUtil.isNotEmpty(toEmpResults) && !user.isScoreStage()) {
            for (EvalScoreResult result : toEmpResults) {
                if (fromEmpScoreTypes.contains(result.getScorerType())) {
                    throw new KpiI18NException("task.raterExist", "该人员已存在当前流程中，不支持转交");
                }
            }
        }
        EmpEvalMerge empEvalMerge = empRuleRepo.getEmpEvalMerge(cmd.getTenantId(), cmd.getEmpEvalId(), item | itemRule);
        //保存转交后的信息
        for (EvalScoreResult fromScoreResult : fromScoreResults) {
            fromScoreResult.transfer(fromScoreResult.getId(), cmd.getToEmpId());
        }

        //清除暂存记录[如果是指标变更审核，暂存记录不删除]
        HashSet<String> temCacheScoreTypes = new HashSet<>(fromEmpScoreTypes);
        temCacheScoreTypes.remove(MsgSceneEnum.CHANGE_ITEM_AUDIT.getType());
        if (CollUtil.isNotEmpty(temCacheScoreTypes)) {
            cacheRepo.deleteCache(cmd.getTenantId().getId(), cmd.getEmpEvalId(), StrUtil.join(",", temCacheScoreTypes), cmd.getOpEmpId());
        }

        //取消旧的待办
        MsgSceneConvertor convertor = new MsgSceneConvertor(empEvalMerge.getEvaluateType(), fromEmpScoreTypes);
        Set<String> msgScenes = convertor.getScene();

        if (msgScenes.isEmpty()) {
            throw new KpiI18NException("task.unableTransfer", "没有可以转交的记录");
        }

        // 更新负责人信息
//        List<KpiEmp> reviewers = kpiEmpDao.listByEmp(user.getCompanyId(), Arrays.asList(cmd.getToEmpId()));
        KpiEmp kpiEmp = kpiEmpDao.findKpiEmp(user.getCompanyId(), cmd.getToEmpId());
        user.replReviewers(Arrays.asList(kpiEmp), cmd.getFromEmpId());
        userRepo.updateTaskUser(user);
        if (user.isResultsAuditing()) {
            Integer order = fromScoreResults.get(0).getApprovalOrder();
            empEvalMerge.modifyAuditRater(kpiEmp, cmd.getFromEmpId(), order);
            auditRepo.updateApproverInfo(cmd.getTenantId().getId(), cmd.getEmpEvalId(), cmd.getFromEmpId(),
                    cmd.getToEmpId(), order, EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene());
        }
        if (user.isConfirming()) {
            Integer order = fromScoreResults.get(0).getApprovalOrder();
            empEvalMerge.modifyConfirmingRater(kpiEmp, cmd.getFromEmpId(), order);
            auditRepo.updateApproverInfo(cmd.getTenantId().getId(), cmd.getEmpEvalId(), cmd.getFromEmpId(),
                    cmd.getToEmpId(), order, EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT.getScene());
        }
        empRuleRepo.updateEmpEvalRule(empEvalMerge);

        Set<String> msgScenesArr = new HashSet<>();
        msgScenesArr.addAll(msgScenes);
        msgScenesArr.addAll(MsgSceneEnum.confirmScene);
        List<CompanyMsgCenter> msgCenters = this.companyMsgCenterDao.listNotHandleMsg(cmd.getTenantId(), cmd.getFromEmpId(),
                user.getId(), msgScenesArr);
        if (CollUtil.isNotEmpty(msgScenesArr)) {
            for (CompanyMsgCenter msgCenter : msgCenters) {
                new CancelTodoEvent(cmd.getTenantId(), new EmpId(msgCenter.getEmpId()), msgCenter.getLinkId(), msgCenter.getBusinessScene()).publish();
            }
        }

        log.debug("*****fromScoreResults*****{}", JSONUtil.toJsonStr(fromScoreResults));
        log.debug("*****toEmpResults*****{}", JSONUtil.toJsonStr(toEmpResults));
        Boolean sendMsgIf = true;
        if (user.isScoreStage()) {
            Map<String, List<EvalScoreResult>> collect = fromScoreResults.stream()
                    .collect(Collectors.groupingBy(EvalScoreResult::getScorerType));

            for (String fromScorerType : collect.keySet()) {
                List<EvalScoreResult> scoreResults = collect.get(fromScorerType);
                TransferOrSkipRaters transferOrSkipRaters = TransferOrSkipRaters.builder()
                        .toEmpId(cmd.getToEmpId())
                        .fromEmpId(cmd.getFromEmpId())
                        .tenantId(cmd.getTenantId())
                        .taskUserId(cmd.getEmpEvalId())
                        .fromScoreResults(scoreResults)
                        .toEmpResults(toEmpResults)
                        .fromEmpScoreTypes(scoreResults.stream().map(EvalScoreResult::getScorerType).collect(Collectors.toSet()))
                        .build();

                TransferRaterDmSvc transferRaterDmSvc = new TransferRaterDmSvc(userRepo, transferOrSkipRaters, empEvalMerge, kpiEmp);
                transferRaterDmSvc.transferScoring();
                empEvalMerge.initIndexRaters();
                empRuleRepo.updateTransferScoringIf(empEvalMerge);
                if (ObjectUtils.isNotEmpty(transferOrSkipRaters.getSubmitRaterScore())) {
                    SubmitRaterScore submitRaterScore = transferOrSkipRaters.getSubmitRaterScore();
                    SubmitScoreCmd submitScoreCmd = new SubmitScoreCmd();
                    BeanUtils.copyProperties(submitRaterScore, submitScoreCmd);
                    submitScoreCmd.setWasAddLog(false);
                    scoreStageAppSvc.submitAnyWrap(submitScoreCmd);
                }
                sendMsgIf = transferRaterDmSvc.getSendMsgIf();
            }
        }

        if (!user.isScoreStage()) {
            userRepo.batchUpdateScoreResult(fromScoreResults);
        }
        //发送新待办、通知、日志

        //获取待办url-新流程不需要
        //所有指标id,存到待办记录里-之前处理过了不需要重复
        //将score type 转为msg的type-之前处理过了不需要重复
        //自定义评分流程只有一种消息类型，如果有评分流程需要转-之前处理过了不需要重复
        // 查 kpiItemId - 发送的消息中并不需要处理这个
        // List<EvalKpi> kpis = this.empEvalDao.listNotAuditKpi(cmd.getTenantId(), fromScoreResults);
        //按评分类型发待办
        if (sendMsgIf) {
            for (String msgScene : msgScenes) {
                //指标指定评分人评分的指标id需要重新过滤-现在发代办不依赖这个指标ID
                //不存在相同待办才发
                List<CompanyMsgCenter> existList = this.companyMsgCenterDao.listNotHandleMsg(cmd.getTenantId(), cmd.getToEmpId(), user.getId(), msgScene);
                if (CollUtil.isNotEmpty(existList)) {
                    //存在了相同地代办，跳过
                    continue;
                }

                new MsgTodoAggregate(cmd.getTenantId(), user.getTaskId(), new Name(empEvalMerge.getTaskName()), user.getEmpId(), user.getId())
                        .useScene(MsgSceneEnum.queryType(msgScene), CompanyMsgActionEnum.actionByType(msgScene))
                        .addExtTempValue("evalEmpName", user.getEmpName())
                        .addExtTempValue("deadLineDate", user.joinDeadLineStr(user.getTaskStatus()))
                        .sendExtTodo()
                        .sendExtMsg()
                        .addCenterMsg()
                        .addRecEmpId(cmd.getToEmpId())
                        .publish();
            }

            //发送收到转交的通知
            new TaskMsg().transferMsg(cmd.getTenantId().getId(), empEvalMerge.getTaskName(), empEvalMerge.getTaskId(),
                    new Emp(user.getEmpId()), new Emp(cmd.getToEmpId())).publish();
        }

        String empName = this.kpiEmpDao.getEmpName(cmd.getTenantId(), cmd.getToEmpId());
        //记录日志
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("empName", empName);
        jsonObject.put("scorerType", fromEmpScoreTypes);
        OperationLogDo logDo = new OperationLogDo(cmd.getTenantId().getId(), user.getId(), "transfer_task",
                cmd.getOpEmpId(), JSONObject.toJSONString(jsonObject));
        this.opLogDao.addLog(logDo);

        //插入管理任务日志
        adminTaskOperationRepo.saveTransferOperation(cmd.getEmpEvalId(), cmd.getTenantId().getId(), cmd.getOpEmpId(), cmd.getOpAdminType(), cmd.getFromEmpId(), cmd.getToEmpId());

        //处理汇总待办的评分人转交逻辑，转出的人需要移除，转入的人需要增加，刷新汇总对象
        //TODO 如果转交的人,是评过分的,那无须再评价
        user.setTaskName(empEvalMerge.getTaskName());
        ScorerTodoDmSvc todoDmSvc = new ScorerTodoDmSvc(empEvalMerge, user);
        if (user.isScoreStage() && todoDmSvc.support()) {
            Set<String> scorerIds = new HashSet<>();
            scorerIds.add(cmd.getFromEmpId());
            scorerIds.add(cmd.getToEmpId());
            //移除
            scorerSummaryTodoRepo.removeScorer(cmd.getEmpEvalId(), cmd.getFromEmpId());
            //转给新的评分人,需要判断是否存在，如果已经存在，无须增加？ TODO
            TaskUserScorer scorer = new TaskUserScorer();
            scorer.populateTaskUserInfo(user);
            scorer.populateScorerInfo(fromScoreResults.get(0).getScorerType(), fromScoreResults.get(0).getApprovalOrder(), cmd.getToEmpId(), empEvalMerge);
            scorer.initTransferStatus();
            scorerSummaryTodoRepo.addScorer(scorer);
            todoDmSvc.setRepo(scorerSummaryTodoRepo);
            todoDmSvc.refreshSummary(scorerIds);
            List<ScorerTodoSummary> updateSummaries = todoDmSvc.getUpdateSummaries();
            List<ScorerTodoSummary> addSummaries = todoDmSvc.getAddSummaries();
            scorerSummaryTodoRepo.batchAddSummaries(addSummaries);
            for (ScorerTodoSummary updateSummary : updateSummaries) {
                if (updateSummary.readyToClose()) { //关闭钉钉待办
                    FinishWorkReq req = new FinishWorkReq(user.getCompanyId(), new EmpId(updateSummary.getScorerId()), updateSummary.getThirdMsgId());
                    boolean isSuccess = false;
                    try {
                        msgAcl.finishTodoWork(req);
                        isSuccess = true;
                    } catch (Exception e) {
                        log.error("完成待办失败:" + e.getMessage(), e);
                    }
                    if (isSuccess) {
                        updateSummary.setTodoStatus(2);
                    }
                    updateSummary.setUpdatedTime(new Date());
                }
                if (updateSummary.readyToSend()) { //发送钉钉待办，系统待办，通知
                    List<TaskUserScorer> scorers = scorerSummaryTodoRepo.queryNeedSendSystemTodoScorers(updateSummary.getCompanyId(), updateSummary.getTaskId(), updateSummary.getScorerId());
                    new ScoreSummaryMsgTodoEvent(updateSummary.getCompanyId(), scorers, updateSummary).publish();
                }
            }
            scorerSummaryTodoRepo.batchUpdateSummaries(updateSummaries);

            if (!addSummaries.isEmpty()) {
                for (ScorerTodoSummary addSummary : addSummaries) {
                    if (addSummary.readyToSend()) { //发送钉钉待办，系统待办，通知
                        List<TaskUserScorer> scorers = scorerSummaryTodoRepo.queryNeedSendSystemTodoScorers(addSummary.getCompanyId(), addSummary.getTaskId(), addSummary.getScorerId());
                        new ScoreSummaryMsgTodoEvent(addSummary.getCompanyId(), scorers, addSummary).publish();
                    }
                }
            }
        }

    }


    /**
     * [新]评分转交的逻辑：
     * 1. 当前考核任务 empEvalId （原来是empId）fromEmpId的全部 (scoreResult)  转交给 toEmpId
     * 2. 取消当前 empEval 处在的阶段的 fromEmpId 系统待办和平台待办
     * 3. 发送当前 empEval 处在阶段的的系统待办和平台待办给  toEmpId
     *
     * @param cmd
     */
    public void transferScoreTaskV3(TaskTransferCmd cmd) {
        //如果没有转出的责任人，说明是责任人自己在操作，取当前操作人
        cmd.verifyEmp();
        EvalUser user = taskUserDao.getBaseEvalUser(cmd.getTenantId(), cmd.getEmpEvalId());
        if (user == null || user.isCreatedStage() || !user.isScoreStage()) {
            throw new KpiI18NException("task.unableTransfer"," 没有可以转交的记录");
        }
        //查询转交的评价关系
        TransferScorerV3DmSvc dmSvc = onScoreEvalRepo.getTransferScorerEmpEval(cmd.getTenantId(),  user, cmd.getFromEmpId(), cmd.getToEmpId(), cmd.getOpEmpId());
        if (Objects.isNull(dmSvc) || dmSvc.isNullWaitSubmit()) {
            throw new KpiI18NException("task.unableTransfer"," 没有可以转交的记录");
        }
        if (dmSvc.isSelfNode()) {
            throw new KpiI18NException("task.selfScoreUnableTransfer"," 自评不能转交");
        }

        MsgSceneConvertor convertor = new MsgSceneConvertor(dmSvc.getEvalRule().getEvaluateType(), dmSvc.getFromWaitScoreTypes());
        Set<String> msgScenes = convertor.getScene();//转换消息场景，用于取消旧的待办
        dmSvc.acceptMsgScenes(msgScenes);
        dmSvc.doTransferScoreV3(cmd.getFromEmpId(),cmd.getToEmpId(),cmd.getOpAdminType());//执行转交

        //保存转交后的数据
        tx.runTran(() -> {
            empRuleRepo.updateTransferScoringIfV3(dmSvc.getEvalRule());
            onScoreEvalRepo.saveTransferEval(user, dmSvc.getFromCenterMsgs(),dmSvc.getOperationLog(),  dmSvc.getAdminTaskOperation()); //保存转交后任务及日志信息
            empEvalScorerRepo.batchUpAndDelEmpEvalScorer(cmd.getTenantId().getId(), cmd.getOpEmpId(), dmSvc.listNeedSaveEmpEvalScorer()); //记录被转交评分人数据
        });
        //存在需要提交的数据(系统提交作为异步执行处理)使用事件处理提交评分
        if (CollUtil.isNotEmpty(dmSvc.getNeedSubmits())) {
            new ScorerTransfered(cmd.getTenantId(), cmd.getOpEmpId(),dmSvc.getEvalUser(), dmSvc.getEvalRule(),
                    cmd.getFromEmpId(),cmd.getToEmpId(), dmSvc.getNeedSubmits(), dmSvc.getToCenterMsgs(), dmSvc.getFromScoreNodeOne(), msgScenes).fire();//发送已转交事件
        }
        //异步处理
        String tid = MDC.get("tid");
        tx.runAsyn(tid, () -> {
            //清除转交待办
            scoreMsgAcl.clearTsScoreMsgTodo(cmd.getTenantId(), dmSvc.getFromCenterMsgs());
        });
    }
    //public List<EvalUser> listTaskUser(String companyId, String taskId, String taskUserIds) {
    //    return taskUserDao.listEvalUser(companyId, taskId, taskUserIds);
    //}

    @Transactional
    public void batchTransferTask(TaskTransferCmd cmd) {
        TaskTransferCmd single = new TaskTransferCmd();
        BeanUtils.copyProperties(cmd, single);
        for (String fromEmpId : cmd.getFromEmpIds()) {
            single.setFromEmpId(fromEmpId);
            single.setTenantId(cmd.getTenantId());
            this.transferTask(single);
        }
    }

    public PagedList<TransferPo> pageTransfer(TransferQuery query) {
        return this.empEvalDao.pageBatchTransfer(query.getTenantId(), query.getEmpName(), query.getPageNo(), query.getPageSize());
    }

    public List<TransferPo> listTransfer(TenantId tenantId, String scorerId) {
        return this.empEvalDao.listTransfer(tenantId, scorerId);
    }

    @Async
    public void urgingTask(UrgingTaskCmd cmd, String tid) {
//        MDC.put("tid", tid);

        List<PerfEvaluateTaskUserDo> users = this.empEvalDao.listTaskUser(cmd.getTenantId(), cmd.getCycleId(), cmd.getTaskId(), cmd.getTaskUserIds());
        if (CollUtil.isEmpty(users)) {
            log.info("taskIds is empty");
            return;
        }
        List<String> taskIds = users.stream().map(PerfEvaluateTaskUserDo::getTaskId).collect(Collectors.toList());
        ListWrap<PerfEvaluateTaskBaseDo> tasks = this.evalTaskDao.listTaskByIds(taskIds, cmd.getTenantId().getId());
        if (tasks.isEmpty()) {
            return;
        }

        for (PerfEvaluateTaskUserDo user : users) {
            PerfEvaluateTaskBaseDo task = tasks.groupGet(user.getTaskId(), 0);
            if (task == null) {
                continue;
            }
            if (Arrays.asList("drawUpIng", "terminated", "finished").contains(user.getTaskStatus())) {
                continue;
            }
            List<CompanyMsgCenter> msgCenters = this.companyMsgCenterDao.listNotHandleMsg(cmd.getTenantId(), user.getId());
            if (CollUtil.isEmpty(msgCenters)) {
                continue;
            }
            msgCenters.stream().map(CompanyMsgCenter::getEmpId).collect(Collectors.toSet()).forEach(empId -> {
                new TaskMsg().urgingTask(cmd.getTenantId().getId(), task.getTaskName(), task.getId(),
                        new Emp(user.getEmpId()), new Emp(empId), user.getId()).publish();
            });
            //for (CompanyMsgCenter msgCenter : msgCenters) {
            //    new TaskMsg().urgingTask(cmd.getTenantId().getId(), task.getTaskName(), task.getId(),
            //            new Emp(user.getEmpId()), new Emp(msgCenter.getEmpId())).publish();
            //}
        }
    }

    public void urgingTaskV2(UrgingTaskCmd cmd, String tid) {
        MDC.put("tid", tid);

        // 获取管理权限范围
        List<String> orgIdList = null;
        if (!cmd.isMainAdmin()) {
            orgIdList = kpiOrgDao.listAdminScopePrivOrgIds(cmd.getTenantId().getId(), cmd.getOpEmpId(), null);
        }
        List<PerfEvaluateTaskUserDo> users = this.empEvalDao.listTaskUser(cmd.getTenantId(), cmd.getCycleId(), cmd.getTaskId(), cmd.getTaskUserIds(), orgIdList);
        if (CollUtil.isEmpty(users)) {
            log.info("users is empty");
            return;
        }

        // 获取任务信息
        List<String> taskIds = users.stream().map(PerfEvaluateTaskUserDo::getTaskId).distinct().collect(Collectors.toList());
        ListWrap<PerfEvaluateTaskBaseDo> tasks = this.evalTaskDao.listTaskByIds(taskIds, cmd.getTenantId().getId());
        if (tasks.isEmpty()) {
            log.info("tasks is empty");
            return;
        }


        // 批量查询所有用户的未处理消息（避免循环中查数据库）
        Set<String> userIds = users.stream().map(PerfEvaluateTaskUserDo::getId).collect(Collectors.toSet());
        List<CompanyMsgCenter> msgCenters = companyMsgCenterDao.listNotHandleMsg(cmd.getTenantId(), null, userIds);
        if (CollUtil.isEmpty(msgCenters)) {
            log.info("msgCenters is empty");
            return;
        }

        // 构建 userId -> user
        Map<String, PerfEvaluateTaskUserDo> userMap = users.stream()
                .collect(Collectors.toMap(PerfEvaluateTaskUserDo::getId, Function.identity()));

        // 构建 taskId -> task
        Map<String, PerfEvaluateTaskBaseDo> taskMap = tasks.getDatas().stream()
                .collect(Collectors.toMap(PerfEvaluateTaskBaseDo::getId, Function.identity()));

        // 构建 empId -> List<被催办人>
        Map<String, List<PerfEvaluateTaskUserDo>> empToUrgedUsers = new HashMap<>();
        for (CompanyMsgCenter msg : msgCenters) {
            // 被考核用户
            String userId = msg.getLinkId();
            // 要推送的人
            String empId = msg.getEmpId();
            PerfEvaluateTaskUserDo user = userMap.get(userId);
            if (user != null) {
                empToUrgedUsers.computeIfAbsent(empId, k -> new ArrayList<>()).add(user);
            }
        }

        // 推送，每个执行人只发一次消息
        for (Map.Entry<String, List<PerfEvaluateTaskUserDo>> entry : empToUrgedUsers.entrySet()) {
            String empId = entry.getKey();
            List<PerfEvaluateTaskUserDo> urgedUsers = entry.getValue();
            if (CollUtil.isEmpty(urgedUsers)) {
                continue;
            }

            boolean isMany = urgedUsers.size() > 1;

            String urgedNameStr = buildUrgedNameStr(urgedUsers, 4);

            String taskNameStr = buildTaskNameStr(urgedUsers, taskMap, 1);
            PerfEvaluateTaskUserDo anyUser = urgedUsers.get(0);
            PerfEvaluateTaskBaseDo anyTask = taskMap.get(anyUser.getTaskId());
            if (anyTask == null) {
                continue;
            }

            List<EmpId> recEmpIds = new ArrayList<>();
            recEmpIds.add(new EmpId(empId));
            UrgingSendMsgCommon urgingSendMsgCommon = new UrgingSendMsgCommon(
                    cmd.getTenantId(),
                    recEmpIds,
                    MsgSceneEnum.URGING,
                    new EmpId(anyUser.getEmpId()),
                    new Name(taskNameStr),
                    new TaskId(anyTask.getId()),
                    anyUser.getId(),
                    isMany);
            urgingSendMsgCommon.addExtTempValue("empName", urgedNameStr);
            log.info("urgingSendMsgCommon={}, urgedNameStr={}, isMany={}", urgingSendMsgCommon, urgedNameStr, isMany);

            urgingMsgSender.sendUrgingMsg(urgingSendMsgCommon);
        }

        // 以下代码处理催办操作日志
        List<String> allExecutorEmpIds = msgCenters.stream()
                .map(CompanyMsgCenter::getEmpId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        List<OpEmpEval> opEmpEvalList = kpiOrgDao.listOpEmpEval(cmd.getTenantId(), allExecutorEmpIds);
        Map<String, OpEmpEval> empMap = opEmpEvalList.stream()
                .collect(Collectors.toMap(OpEmpEval::getEmpId, Function.identity(), (a, b) -> a));

        // 批量保存操作日志
        saveUrgingLogByTasks(users, taskMap, msgCenters, empMap, cmd.getTenantId(), cmd.getOpEmpId(), cmd.getOpAdminType(), cmd.isAll());
    }

    private void saveUrgingLogByTasks(
            List<PerfEvaluateTaskUserDo> users,
            Map<String, PerfEvaluateTaskBaseDo> taskMap,
            List<CompanyMsgCenter> msgCenters,
            Map<String, OpEmpEval> empMap,
            TenantId tenantId,
            String operatorEmpId,
            String operatorAdminType,
            boolean isAll
    ) {
        List<UrgingLogContext> logList = buildUrgingLogContextList(users, taskMap, msgCenters, empMap, tenantId,
                operatorEmpId, operatorAdminType, isAll);
        adminTaskRepo.batchSaveUrgingLog(logList);
    }

    private List<UrgingLogContext> buildUrgingLogContextList(
            List<PerfEvaluateTaskUserDo> users,
            Map<String, PerfEvaluateTaskBaseDo> taskMap,
            List<CompanyMsgCenter> msgCenters,
            Map<String, OpEmpEval> empMap,
            TenantId tenantId,
            String operatorEmpId,
            String operatorAdminType,
            boolean isAll
    ) {
        Map<String, List<PerfEvaluateTaskUserDo>> taskIdToUrgedUsers = users.stream()
                .collect(Collectors.groupingBy(PerfEvaluateTaskUserDo::getTaskId));

        List<UrgingLogContext> result = new ArrayList<>();
        for (Map.Entry<String, List<PerfEvaluateTaskUserDo>> entry : taskIdToUrgedUsers.entrySet()) {
            String taskId = entry.getKey();
            List<PerfEvaluateTaskUserDo> urgedUsers = entry.getValue();
            PerfEvaluateTaskBaseDo task = taskMap.get(taskId);
            if (task == null) continue;

            List<OpEmpEval> empEvalList = urgedUsers.stream()
                    .map(user -> {
                        OpEmpEval eval = new OpEmpEval();
                        eval.setEmpId(user.getEmpId());
                        eval.setEmpName(user.getEmpName());
                        eval.setAvatar(user.getAvatar());
                        eval.setOrgName(user.getEmpOrgName());
                        eval.setEvalOrgName(user.getEvalOrgName());
                        eval.setTaskUserId(user.getId());
                        return eval;
                    }).collect(Collectors.toList());

            Set<String> executorEmpIds = msgCenters.stream()
                    .filter(msg -> urgedUsers.stream().anyMatch(u -> u.getId().equals(msg.getLinkId())))
                    .map(CompanyMsgCenter::getEmpId)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());

            List<OpEmpEval> executorList = executorEmpIds.stream()
                    .map(empMap::get)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            result.add(new UrgingLogContext(
                    taskId,
                    task.getTaskName(),
                    operatorEmpId,
                    operatorAdminType,
                    urgedUsers.size(),
                    tenantId,
                    empEvalList,
                    executorList,
                    isAll
            ));
        }
        return result;
    }

    /**
     * 拼接最多 max 个名称，重复去重，超过加 "...”
     */
    private String joinDistinctNames(List<String> names, int max) {
        List<String> distinct = names.stream()
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        String result = distinct.stream().limit(max).collect(Collectors.joining("、"));
        if (distinct.size() > max) {
            result += " ...";
        }
        return result;
    }

    /**
     * 拼接被催办人名称
     */
    private String buildUrgedNameStr(List<PerfEvaluateTaskUserDo> urgedUsers, int max) {
        List<String> names = urgedUsers.stream()
                // 获取被考核人或者被考核组织名称
                .map(u -> StringUtils.defaultIfBlank(u.getEvalOrgName(), u.getEmpName()))
                .collect(Collectors.toList());
        return joinDistinctNames(names, max);
    }

    /**
     * 拼接任务名称
     */
    private String buildTaskNameStr(List<PerfEvaluateTaskUserDo> urgedUsers, Map<String, PerfEvaluateTaskBaseDo> taskMap, int max) {
        List<String> taskNames = urgedUsers.stream()
                .map(u -> {
                    PerfEvaluateTaskBaseDo task = taskMap.get(u.getTaskId());
                    return task != null ? task.getTaskName() : "";
                })
                .collect(Collectors.toList());
        return joinDistinctNames(taskNames, max);
    }


    public void urgingFinishValue(UrgingFinishValueCmd cmd) {
        if (cmd.isEmpty()) {
            return;
        }
        List<String> taskUserIds = cmd.getTaskUserIds();
        for (String taskUserId : taskUserIds) {
            ListWrap<EvalKpi> evalKpis = this.kpiDao.listNotUrgingFinishValue(cmd.getTenantId(), taskUserId, cmd.getId());
            if (evalKpis.isEmpty()) {
                continue;
            }

            Set<String> finishInputEmpIds = new HashSet<>();
            InnerFields<String> kpiIds = new InnerFields<>();
            for (EvalKpi kpi : evalKpis.getDatas()) {
                kpiIds.add(kpi.getId());
                if (kpi.ifSelfInput()) {
                    finishInputEmpIds.add(kpi.getEmpId());
                    continue;
                }

                if (kpi.isSpecifyUser() || kpi.isSpecifyManager() || kpi.isSpecifyRole()) {
                    if (StrUtil.isEmpty(kpi.getResultInputEmpId())) {
                        continue;
                    }
                    InnerFields<String> inputEmpIds = new InnerFields<>();
                    inputEmpIds.appendStr(kpi.getResultInputEmpId(), ",");
                    finishInputEmpIds.addAll(inputEmpIds.getFields());
                }
            }

            CycleEval cycleTask = taskRepo.getMergeCycleEval(cmd.getTenantId(), taskUserId);
            String empId = evalKpis.getDatas().get(0).getEmpId();

            for (String finishInputEmpId : finishInputEmpIds) {
                new TaskMsg().urgingTask(cmd.getTenantId().getId(), cycleTask.getTaskName().getName(), "提交完成值", cycleTask.getId(),
                        new Emp(empId), new Emp(finishInputEmpId)).publish();
            }

            //更新催办状态
            this.kpiDao.batchUpdateUrgingFlag(cmd.getTenantId(), kpiIds.unique());

        }
    }

    public void sendInputWorkItemUrging(TenantId tenantId, String taskId, String taskUserId, String createdUser) {
        log.info("催办工作事项,taskId={},taskUserId={},createdUser={}", taskId, taskUserId, createdUser);
        List<String> taskUserIds = new ArrayList<>();
        taskUserIds.add(taskUserId);
        ListWrap<EvalKpi> evalKpis = this.kpiDao.listNotUrgingWorkItem(tenantId, taskId, taskUserIds);
        if (evalKpis.isEmpty()) {
            return;
        }
        Set<String> finishInputEmpIds = new HashSet<>();
        for (EvalKpi kpi : evalKpis.getDatas()) {
            if (kpi.ifSelfInput()) {
                finishInputEmpIds.add(kpi.getEmpId());
                continue;
            }

            if (kpi.isSpecifyUser() || kpi.isSpecifyManager() || kpi.isSpecifyRole()) {
                if (StrUtil.isEmpty(kpi.getResultInputEmpId())) {
                    continue;
                }
                InnerFields<String> inputEmpIds = new InnerFields<>();
                inputEmpIds.appendStr(kpi.getResultInputEmpId(), ",");
                finishInputEmpIds.addAll(inputEmpIds.getFields());
            }
        }
        String empId = evalKpis.getDatas().get(0).getEmpId();
        CycleEval taskBase = this.evalTaskDao.getTaskBase(tenantId, taskId);
        for (String finishInputEmpId : finishInputEmpIds) {
            //改成发普通的通知
            new TaskMsg().inputValueMsg(tenantId.getId(), taskBase.getTaskName().getName(), taskBase.getId(), new Emp(empId),
                    new Emp(finishInputEmpId)).publish();
        }
    }

    @Async
    public void urgingSetMutualScoreEmp(TenantId tenantId, List<EvalUser> evalUsers, String tid) {
        MDC.put("tid", tid);
        ListWrap<EvalUser> group = new ListWrap<>(evalUsers).groupBy(EvalUser::getTaskId);
        if (group.isEmpty()) {
            return;
        }
        for (String taskId : group.groupKeySet()) {
            String userIds = evalUsers.stream().map(EvalUser::getId).collect(Collectors.joining(","));
            List<EvalUser> users = taskUserDao.listEvalUser(tenantId.getId(), taskId, userIds);
            if (CollUtil.isEmpty(users)) {
                continue;
            }
            CycleEval taskBase = this.evalTaskDao.getTaskBase(tenantId, taskId);
            for (EvalUser user : users) {
                List<MutualEmpPo> emps = companyMsgCenterDao.listMutualAuditEmp(tenantId.getId(), Arrays.asList(user.getId()),
                        CompanyMsgActionEnum.SET_MUTUAL_RATER.getMsgType());
                if (CollUtil.isEmpty(emps)) {
                    log.info("没有未处理的待办");
                    continue;
                }
                for (MutualEmpPo emp : emps) {
                    new TaskMsg().urgingTask(tenantId.getId(), taskBase.getTaskName().getName(), "设置互评人",
                            taskBase.getId(), new Emp(user.getEmpId()), new Emp(emp.getEmpId())).publish();
                }
            }
        }
    }


    public List<NoWorkItemEmpPo> noWorkItemEmp(TenantId tenantId, List<WorkItemQuery> query) {
        return taskKpiItemDao.noWorkItemEmp(tenantId, query);
    }

    public List<ModificationRecordPo> listModificationRecord(String companyId, String taskUserId, String scene) {
        return taskKpiItemDao.listModificationRecord(companyId, taskUserId, scene);
    }

    public List<EmpEvalIfvPo> listNotFinishTask(TenantId tenantId, List<String> empEvalIds) {
        ListWrap<EmpEvalKpiIfvEmpPo> list = this.kpiDao.listNotFinishedValueTask(tenantId, empEvalIds);
        if (list.isEmpty()) {
            return new ArrayList<>();
        }
        List<EmpEvalIfvPo> rs = new ArrayList<>();
        for (String empEvalId : list.groupKeySet()) {
            EmpEvalIfvPo po = new EmpEvalIfvPo(empEvalId, list.groupGet(empEvalId));
            rs.add(po);
        }
        return rs;
    }

    @Transactional
    public EvalScoreChanged adjustTaskUserResult(ResultAuditCmd cmd) {
        EvalUser before = userRepo.getBaseTaskUser(cmd.getTenantId(), cmd.getId());
        EvalUser after = new EvalUser();
        BeanUtils.copyProperties(before, after);
        after.auditResult(cmd.getScore(), cmd.getEvaluationLevel(), cmd.getAdjustReason(), cmd.getStepId(), cmd.getPerfCoefficient());
        this.userRepo.updateTaskUser(after);
        this.userRepo.updateTypeLevel(cmd.getTenantId(), cmd.getId(), cmd.getTypes());
        //taskDao.renewalRefEval(cmd.getTenantId().getId(), cmd.getId());
        OperationLogDo logDo = new OperationLogDo();
        logDo.adjustTaskUserResult(cmd.getTenantId(), cmd.getOpEmpId().getId(), before, after);
        this.opLogDao.addLog(logDo);
        return new EvalScoreChanged(cmd.getTenantId().getId(), cmd.getId());
    }


    public void submitResultAppeal(SubmitResultAppealCmd cmd) {

        PerfEvaluateTaskAppealBatch appealBatch = taskAppealDao.getTaskAppealBatch(cmd.getCompanyId(), cmd.getTaskUserId());
        if (Objects.nonNull(appealBatch)) {
            throw new KpiI18NException("error.taskAppeal.10001");//该任务正在申诉中，请勿重复操作
        }
        CycleEval cycleEval = taskRepo.getMergeCycleEval(cmd.getCompanyId(), cmd.getTaskUserId());
        EvalUser evalUser = userRepo.getTaskUser(cmd.getCompanyId(), cmd.getTaskUserId());
        if (TalentStatus.FINISHED.getStatus().equals(evalUser.getTaskStatus()) && evalUser.timeOutAppealDeadLine()) {
            throw new KpiI18NException("error.taskAppeal.10002");
        }
        EmpEvalMerge empEvalMerge = empRuleRepo.getEmpEvalMerge(cmd.getCompanyId(), evalUser.getId(), all);
        //超过次数，不允许提交
        if (empEvalMerge.getAppealConf().appealNumberOver()) {
            throw new KpiI18NException("error.taskAppeal.10003");
        }
        //附件必填判断
        if (empEvalMerge.getAppealConf().attachMust() && cmd.notAllInfoWithAttachment()) {
            throw new KpiI18NException("error.taskAppeal.10004");
        }

        //领域服务
        TaskAppealDmSvc appealDmSvc = new TaskAppealDmSvc(evalUser, empEvalMerge.getAppealConf(), kpiEmpRepo, cmd.getOpEmpId());
        //发起申诉
        appealDmSvc.submitAppeal();
        //拿出聚合根
        PerfEvaluateTaskAppealBatch batch = appealDmSvc.getAppealBatch();
        List<PerfEvaluateTaskAppealInfo> appealInfoModelList = cmd.getAppealInfoStr();
        batch.setAppealInfos(appealInfoModelList);

        tx.runTran(() -> {
            taskAppealRepo.saveSubmitAppealRs(batch);
            //组装日志记录
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("operationType", "submit");
            JSONArray objects = new JSONArray();
            appealInfoModelList.forEach(appealInfoModel -> {
                JSONObject detail = new JSONObject();
                detail.put("items", appealInfoModel.getItems());
                detail.put("appealFiles", appealInfoModel.getAppealFiles());
                detail.put("appealPictures", appealInfoModel.getAppealPictures());
                detail.put("reason", appealInfoModel.getReason());
                objects.add(detail);
            });
            jsonObject.put("details", objects);
            OperationLogDo createdLog = new OperationLogDo(cmd.getCompanyId().getId(), evalUser.getId(),
                    OperationLogSceneEnum.RESULT_APPEAL.getScene(), batch.getCreatedUser(), jsonObject.toJSONString());
            opLogDao.addLog(createdLog);
            userRepo.updateTaskUser(evalUser);
        });

        //3.给受理人发送申诉待办,通知
        new MsgTodoAggregate(cmd.getCompanyId(), cycleEval.getId(), cycleEval.getTaskName(),
                evalUser.getEmpId(), evalUser.getId())
                .useScene(MsgSceneEnum.TASK_RESULT_APPEAL, CompanyMsgActionEnum.RESULT_APPEAL)
                .addExtTempValue("empName", evalUser.getEvalOrgName() == null ? evalUser.getEmpName() : evalUser.getEvalOrgName())
                .addTodoItem("msg.task.emp", evalUser.getEvalOrgName() == null ? evalUser.getEmpName() : evalUser.getEvalOrgName())
                .sendExtTodo().sendExtMsg().addCenterMsg().addRecEmpId(batch.getNotifyEmpIds()).publish();
    }


    public EvalScoreChanged auditResultAppeal(AuditResultAppealCmd cmd) {

        CycleEval cycleEval = taskRepo.getMergeCycleEval(cmd.getCompanyId(), cmd.getTaskUserId());
        EmpEvalMerge empEvalMerge = empRuleRepo.getEmpEvalMerge(cmd.getCompanyId(), cmd.getTaskUserId(), EmpEvalMerge.all);
        PerfEvaluateTaskAppealBatch appealBatch = taskAppealDao.getTaskAppealBatchById(cmd.getCompanyId(), cmd.getAppealBatchId());
        //校验
        if (Objects.isNull(appealBatch)) {
            throw new KpiI18NException("error.taskAppeal.10005");
        }
        if (appealBatch.isEnd()) {
            throw new KpiI18NException("error.taskAppeal.10006");
        }
        //校验当前操作人是否存在待提交节点
        boolean opEmpValid = appealBatch.getOngoingAuditNode().opEmpValid(cmd.getOpEmpId());
        if (!opEmpValid) {
            throw new KpiI18NException("error.taskAppeal.10007");
        }

        EvalUser evalUser = userRepo.getTaskUser(cmd.getCompanyId(), appealBatch.getTaskUserId());
        AppealAudit appealAudit = new AppealAudit();
        BeanUtils.copyProperties(cmd, appealAudit);
        TaskAppealDmSvc appealDmSvc = new TaskAppealDmSvc(evalUser, appealBatch, kpiEmpRepo, cmd.getOpEmpId());
        appealDmSvc.auditAppeal(appealAudit);

        //如果当前是结果确认阶段，补偿找回结果确认的责任人
        if (TalentStatus.RESULTS_AFFIRMING.getStatus().equals(evalUser.getTaskStatus())) {
            evalUser.addReviewers(kpiEmpRepo.listByEmp(cmd.getCompanyId(), Collections.singletonList(evalUser.getEmpId())));
        }

        //组装日志记录
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("operationType", "audit");
        jsonObject.put("auditResult", cmd.getAppealResult());
        jsonObject.put("auditReason", cmd.getRemark());
        jsonObject.put("auditDetail", cmd.getAppealResult() == 1 ? cmd.getModifiedScore() + "|" + cmd.getModifiedLevel() + "|" + cmd.getModifiedPerfCoefficient() : "");

        tx.runTran(() -> {
            taskAppealRepo.saveAuditAppealRs(appealBatch);
            userRepo.updateTaskUser(evalUser);
            if (appealBatch.isEnd()) {
                empEvalMerge.getAppealConf().appealNumberInc();
                empRuleRepo.updateEmpEvalRule(empEvalMerge);
                jsonObject.put("endFlag", "true");
            }

            new ClearTodoDmsSvc(centerRepo).clear(cmd.getCompanyId(), evalUser.getId(), Collections.singletonList(MsgSceneEnum.TASK_RESULT_APPEAL.getType()), appealBatch.getCancelTodoEmpIds());

            OperationLogDo auditLog = new OperationLogDo(cmd.getCompanyId().getId(), evalUser.getId(),
                    OperationLogSceneEnum.RESULT_APPEAL.getScene(), cmd.getOpEmpId(), jsonObject.toJSONString());
            opLogDao.addLog(auditLog);
        });

        // 通知被考核人申诉已处理完毕
        if (appealBatch.isEnd() && empEvalMerge.getAppealConf().getNotifyConf().isOpen()) {
            new MsgTodoAggregate(cmd.getCompanyId(), cycleEval.getId(), cycleEval.getTaskName(),
                    evalUser.getEmpId(), evalUser.getId())
                    .useScene(MsgSceneEnum.TASK_APPEAL_RESULT, CompanyMsgActionEnum.RESULT_APPEAL)
                    .sendExtMsg().addRecEmpId(appealBatch.getAppealResultReceivers())
                    .addExtTempValue("taskName", empEvalMerge.getTaskName())
                    .addExtTempValue("auditResult", cmd.getAppealResult() == 1 ? "同意了结果申诉" : "拒绝了结果申诉")
                    .addExtTempValue("auditReason", cmd.getRemark())
                    .publish();
        }
        if (CollUtil.isNotEmpty(appealBatch.getNotifyEmpIds())) {
            //3.给受理人发送申诉待办,通知
            new MsgTodoAggregate(cmd.getCompanyId(), cycleEval.getId(), cycleEval.getTaskName(),
                    evalUser.getEmpId(), evalUser.getId())
                    .useScene(MsgSceneEnum.TASK_RESULT_APPEAL, CompanyMsgActionEnum.RESULT_APPEAL)
                    .addExtTempValue("empName", evalUser.getEvalOrgName() == null ? evalUser.getEmpName() : evalUser.getEvalOrgName())
                    .addTodoItem("msg.task.emp", evalUser.getEvalOrgName() == null ? evalUser.getEmpName() : evalUser.getEvalOrgName())
                    .sendExtTodo().sendExtMsg().addCenterMsg().addRecEmpId(appealBatch.getNotifyEmpIds()).publish();
        }

        return new EvalScoreChanged(cmd.getCompanyId().getId(), cmd.getTaskUserId());
    }

    //@Transactional
    //public EvalScoreChanged auditResultAppealR(AuditResultAppealCmd cmd) {
    //    PerfEvaluateTaskAppealBatch appealBatch = taskAppealDao.getTaskAppealBatchById(cmd.getCompanyId(), cmd.getAppealBatchId());
    //    CycleEval cycleEval = taskRepo.getMergeCycleEval(cmd.getCompanyId(), appealBatch.getTaskUserId());
    //    EvalUser evalUser = userRepo.getTaskUser(cmd.getCompanyId(), appealBatch.getTaskUserId());
    //    appealBatch.releationAudit(evalUser, cycleEval, cmd.getAppealInfoStr());
    //    appealBatch.passAudit();
    //
    //}

    public void cancelResultAppeal(String companyId, String opEmpId, String appealBatchId) {

        PerfEvaluateTaskAppealBatch appealBatch = taskAppealDao.getTaskAppealBatchById(new TenantId(companyId), appealBatchId);
        //校验
        if (Objects.isNull(appealBatch)) {
            throw new KpiI18NException("error.taskAppeal.10005");
        }
        if (appealBatch.isEnd()) {
            throw new KpiI18NException("error.taskAppeal.10006");
        }

        EvalUser evalUser = userRepo.getTaskUser(new TenantId(companyId), appealBatch.getTaskUserId());

        TaskAppealDmSvc appealDmSvc = new TaskAppealDmSvc(evalUser, appealBatch, kpiEmpRepo, opEmpId);
        appealDmSvc.cancelResultAppeal();

        //如果当前是结果确认阶段，补偿找回结果确认的责任人
        if (TalentStatus.RESULTS_AFFIRMING.getStatus().equals(evalUser.getTaskStatus())) {
            evalUser.addReviewers(kpiEmpRepo.listByEmp(new TenantId(companyId), Collections.singletonList(evalUser.getEmpId())));
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("operationType", "cancel");

        tx.runTran(() -> {
            userRepo.updateTaskUser(evalUser);
            taskAppealRepo.updateCancelAppealRs(appealBatch);
            //取消待办
            new ClearTodoDmsSvc(centerRepo).clear(new TenantId(companyId), evalUser.getId(), Collections.singletonList(MsgSceneEnum.TASK_RESULT_APPEAL.getType()), appealBatch.getCancelTodoEmpIds());
            // 记录日志
            OperationLogDo cancelLog = new OperationLogDo(companyId, evalUser.getId(),
                    OperationLogSceneEnum.RESULT_APPEAL.getScene(), opEmpId, jsonObject.toJSONString());
            opLogDao.addLog(cancelLog);
        });
    }

    public void transferAppeal(TransferAppealCmd cmd) {

        PerfEvaluateTaskAppealBatch appealBatch = taskAppealDao.getTaskAppealBatchById(cmd.getCompanyId(), cmd.getAppealBatchId());

        if (Objects.isNull(appealBatch)) {
            throw new KpiI18NException("error.taskAppeal.10005");
        }

//        if (!appealBatch.transferOpen()){
//            throw new KpiI18NException("当前申诉批次未开启转交功能");
//        }

        //校验转出人是否存在待提交节点
        boolean opEmpValid = appealBatch.getOngoingAuditNode().opEmpValid(cmd.getFromEmpId());
        if (!opEmpValid) {
            throw new KpiI18NException("error.taskAppeal.10008");
        }

        CycleEval cycleEval = taskRepo.getMergeCycleEval(cmd.getCompanyId(), appealBatch.getTaskUserId());
        EvalUser evalUser = userRepo.getTaskUser(cmd.getCompanyId(), appealBatch.getTaskUserId());

        TaskAppealDmSvc appealDmSvc = new TaskAppealDmSvc(evalUser, appealBatch, kpiEmpRepo, cmd.getOpEmpId());
        appealDmSvc.transferAppeal(cmd.getFromEmpId(), cmd.getToEmpId(), cmd.getToEmpName());

        //如果当前是结果确认阶段，补偿找回结果确认的责任人
        if (TalentStatus.RESULTS_AFFIRMING.getStatus().equals(evalUser.getTaskStatus())) {
            evalUser.addReviewers(kpiEmpRepo.listByEmp(cmd.getCompanyId(), Collections.singletonList(evalUser.getEmpId())));
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("operationType", "transfer");
        jsonObject.put("transferTo", cmd.getToEmpName());
        jsonObject.put("transferFrom", cmd.getFromEmpName());

        tx.runTran(() -> {
            userRepo.updateTaskUser(evalUser);
            taskAppealRepo.updateTransferAppealRs(appealBatch);
            //清空待办
            new ClearTodoDmsSvc(centerRepo).clear(cmd.getCompanyId(), evalUser.getId(), Collections.singletonList(MsgSceneEnum.TASK_RESULT_APPEAL.getType()), appealBatch.getCancelTodoEmpIds());
            // 4.记录日志
            OperationLogDo cancelLog = new OperationLogDo(cmd.getCompanyId().getId(), evalUser.getId(),
                    OperationLogSceneEnum.RESULT_APPEAL.getScene(), cmd.getFromEmpId(), jsonObject.toJSONString());
            opLogDao.addLog(cancelLog);

        });

        //给新受理人发送新的待办及通知
        new MsgTodoAggregate(cmd.getCompanyId(), cycleEval.getId(), cycleEval.getTaskName(),
                evalUser.getEmpId(), evalUser.getId())
                .useScene(MsgSceneEnum.TASK_RESULT_APPEAL, CompanyMsgActionEnum.RESULT_APPEAL)
                .addExtTempValue("empName", evalUser.getEvalOrgName() == null ? evalUser.getEmpName() : evalUser.getEvalOrgName())
                .addTodoItem("msg.task.emp", evalUser.getEvalOrgName() == null ? evalUser.getEmpName() : evalUser.getEvalOrgName())
                .sendExtTodo().sendExtMsg().addCenterMsg().addRecEmpId(appealBatch.getNotifyEmpIds()).publish();
    }

    public void resetTaskAppealFlow(AdminTask task, String taskUserId, String companyId, String opEmpId) {

        log.info("申诉流程被重置，保留申诉内容，流程重新进行，taskUserId: {} " + taskUserId);
        PerfEvaluateTaskAppealBatch oldOne = taskAppealDao.getTaskAppealBatch(new TenantId(companyId), taskUserId);
        List<String> cancelTodoEmpIds = taskAppealDao.listWaitAuditEmpIds(companyId, oldOne.getId());
        List<PerfEvaluateTaskAppealInfo> appealInfoModelList = taskAppealDao.listAppealInfo(oldOne.getId());

        EmpEvalMerge empEvalMerge = empRuleRepo.getEmpEvalMerge(new TenantId(companyId), taskUserId, EmpEvalMerge.all);
        EvalUser evalUser = userRepo.getTaskUser(new TenantId(companyId), taskUserId);
        evalUser.removeReviewers(cancelTodoEmpIds);

        //领域服务
        TaskAppealDmSvc appealDmSvc = new TaskAppealDmSvc(evalUser, empEvalMerge.getAppealConf(), kpiEmpRepo, opEmpId);
        //发起申诉
        appealDmSvc.submitAppeal();
        //拿出聚合根
        PerfEvaluateTaskAppealBatch batch = appealDmSvc.getAppealBatch();
        batch.setAppealInfos(appealInfoModelList);

        //如果当前是结果确认阶段，补偿找回结果确认的责任人
        if (TalentStatus.RESULTS_AFFIRMING.getStatus().equals(evalUser.getTaskStatus())) {
            evalUser.addReviewers(kpiEmpRepo.listByEmp(new TenantId(companyId), Collections.singletonList(evalUser.getEmpId())));
        }

        tx.runTran(() -> {
            taskAppealRepo.saveSubmitAppealRs(batch);
            taskAppealRepo.cancelAppealBatch(oldOne.getId());
            //清空待办
            new ClearTodoDmsSvc(centerRepo).clear(new TenantId(companyId), evalUser.getId(), Collections.singletonList(MsgSceneEnum.TASK_RESULT_APPEAL.getType()), cancelTodoEmpIds);
            //组装日志记录
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("operationType", "reset");
            OperationLogDo createdLog = new OperationLogDo(companyId, evalUser.getId(),
                    OperationLogSceneEnum.RESULT_APPEAL.getScene(), opEmpId, jsonObject.toJSONString());
            opLogDao.addLog(createdLog);
            userRepo.updateTaskUser(evalUser);
        });

        //3.给受理人发送申诉待办,通知
        new MsgTodoAggregate(new TenantId(companyId), task.getId(), new Name(empEvalMerge.getTaskName()),
                evalUser.getEmpId(), evalUser.getId())
                .useScene(MsgSceneEnum.TASK_RESULT_APPEAL, CompanyMsgActionEnum.RESULT_APPEAL)
                .addExtTempValue("empName", evalUser.getEvalOrgName() == null ? evalUser.getEmpName() : evalUser.getEvalOrgName())
                .addTodoItem("msg.task.emp", evalUser.getEvalOrgName() == null ? evalUser.getEmpName() : evalUser.getEvalOrgName())
                .sendExtTodo().sendExtMsg().addCenterMsg().addRecEmpId(batch.getNotifyEmpIds()).publish();
    }

//    public List<TaskAppealPo> listTaskAppealRecord(TaskAppealRecordQuery query) {
//        return taskAppealDao.listTaskAppealRecord(query);
//    }

    public List<TaskAppealRecordPo> listTaskAppealRecord(TaskAppealRecordQuery query) {
        return taskAppealDao.listTaskAppealRecordV2(query);
    }

    public void readAppeal(String companyId, String taskUserId) {
        taskAppealRepo.readAppeal(companyId, taskUserId);
    }

    //任务修改配置同步到考核表上
    public void syncAdminTaskConf(AdminTaskConfEdited event, AdminTaskDiff diff) {
        List<EvalUser> users;
        int pageNo = 1;
        List<String> adminOrgIds = kpiOrgDao.listAdminScopePrivOrgIds(event.getTenantId(), event.getOpEmpId(), null);
        List<EvalUser> flowUsers = new ArrayList<>();
        List<EvalUser> scoreSummaryUsers = new ArrayList<>();
        TenantId companyId = new TenantId(event.getTenantId());
        AdminTask adminTask = adminTaskRepo.getAdminTask(companyId, event.getTaskId());
        while ((CollUtil.isNotEmpty(users = taskUserDao.listEvalUserByTask(event.getTenantId(), event.getTaskId(),
                pageNo++, adminOrgIds, event.getBefore().getPerformanceType())))) {
            List<String> taskUserIds = users.stream().map(EvalUser::getId).collect(Collectors.toList());
            //评分查看权限、结果公示、结果申诉配置变更后实时同步考核规则
            if (diff.hasChange()) {
                taskUserDao.realTimeSynEvalRule(companyId, taskUserIds, adminTask, diff);
            }

            //过滤需要同步任务配置变更的员工任务列表
            List<EvalUser> synChangedUsers = users.stream().filter(user -> event.getTaskUserIds().contains(user.getId())
                    || TalentStatus.isBefore(user.getTaskStatus(), event.getChangedStage()) || TalentStatus.RESULTS_APPEAL.getStatus().equals(event.getChangedStage())).collect(Collectors.toList());

            synChangedUsers.stream().filter(changedUser -> Objects.nonNull(changedUser.getEmpEvalRule())).forEach(changedUser -> {
                //如果当前任务状态刚好处于执行中变更，并且已经提交了变更但是还没有审核的时候，编辑了指标变更审核为关闭，需要清除审核变更人的待办，并清除缓存
                if (TalentStatus.CHANGING.getStatus().equals(changedUser.getTaskStatus()) && !adminTask.getEditExeIndi().auditIsOpen()) {
                    new CancelTodoEvent(companyId, changedUser.getId(), MsgSceneEnum.CHANGE_ITEM_AUDIT.getType()).publish();
                    taskUserDao.clearCacheIno(companyId, changedUser.getId());
                }
                //如果本来是打开了手动公示，并且当前任务状态刚好处于待公示阶段，用户修改了周期公示环节配置为关闭时，需要将任务的状态更新成完成阶段。
                if (!adminTask.getPublishResult().isOpen()
                        && TalentStatus.WAIT_PUBLISHED.getStatus().equals(changedUser.getTaskStatus())) {
                    EmpEvalMerge empEvalMerge = empRuleRepo.getEmpEvalMerge(changedUser.getCompanyId(), changedUser.getId(), all);
                    new ThisStageEnded(empEvalMerge, changedUser, TalentStatus.WAIT_PUBLISHED).publish();
                }
                evalDmSvc.syncAdminTaskConf(adminTask, diff.getChangeScene(), changedUser);
                empRuleRepo.editEmpEvalRule(changedUser, TalentStatus.statusOf(event.getChangedStage()));
                if (Objects.isNull(changedUser.getResultSendNotify())) {
                    changedUser.setResultSendNotify(adminTask.getResultSendNotify());
                }
                log.debug("taskStatus:{}", JSONUtil.toJsonStr(changedUser.getTaskStatus()));
                if (changedUser.needChangedResultAudit(event.getChangedStage()) && !event.changedDeadLine()) {
                    if (changedUser.isAuditResultCollectSend()) {
                        flowUsers.add(changedUser);
                    }
                }
                //进行评分汇总的相关人员统计，如果任务设置从串行到并行
                if (event.changedDeadLine()) {
                    empRuleRepo.updateDeadLine(changedUser.getTaskId(), changedUser.getEmpEvalRule());
                }
                /**处理异常*/
                // this.anewEvalRuleStatus(event.getTenantId(), changedUser);
            });
            /**处理异常*/
            List<EvalUser> collect = synChangedUsers.stream().filter(changedUser -> Objects.nonNull(changedUser.getEmpEvalRule())).collect(Collectors.toList());
            this.checkErro(new TenantId(event.getTenantId()), collect);
        }
        //生成校准审批流程实例
        if (CollUtil.isNotEmpty(flowUsers)) {
            auditFlowRepo.refreshAuditFlow(event.getTenantId(), event.getOpEmpId(), flowUsers);
            auditFlowRepo.refreshSummary(event.getTenantId(), event.getTaskId());
        }

        //评分汇总进行重新解析,任务里面所有考核 TODO


    }

    //跳过校准
    @Transactional
    public void skipFinalResultAudit(SkipRaterCmd cmd) {
        EvalAudit audit = taskUserDao.getAudit(cmd.getTenantId(), cmd.getTaskUserId(), cmd.getScorerType(), cmd.getScorerId(), cmd.getApproveOrder());
        ResultAuditCmd resultAuditCmd = new ResultAuditCmd();
        //这里当前操作人cmd.getScorerId()和跳过校准责任人cmd.getOpEmpId()两个id搞反了
        resultAuditCmd = resultAuditCmd.asSkipped(cmd.getTenantId(), new EmpId(cmd.getScorerId()), cmd.getTaskUserId(), audit.getApprovalOrder(), cmd.getOpEmpId(), cmd.getScorerName());
        this.doResultAudit(resultAuditCmd);
    }

    @Transactional
    public KpiItemUpdateFinishedValueEvent inviteMutualEmp(InviteMutualCmd cmd) {
        TenantId tenantId = new TenantId(cmd.getCompanyId());
        EmpEvalMerge empEvalRule = empRuleRepo.getEmpEvalMerge(tenantId, cmd.getTaskUserId(), type | item | itemRule);
        EvalUser user = taskUserDao.getBaseEvalUser(tenantId, cmd.getTaskUserId());
        //邀请的评分人
        List<String> mutualEmpIds = cmd.mutualEmpIds();
        Map<String, SimpleEmp> raterEmpMap = deptEmpDao.listByEmpAsMap(tenantId, mutualEmpIds);
        //先清除任务下互评指标数据
        List<String> kpiItemIds = cmd.getMutualItemAudits().stream().map(MutualItemAudit::getKpiItemId).filter(StrUtil::isNotBlank).collect(Collectors.toList());
        Map<String, KpiEmp> empMap = kpiEmpDao.listByEmpAsMap(tenantId, cmd.mutualEmpIds());
        Map<String, Set<String>> inviteMutualAuditMap = empEvalRule.inviteMutualAuditMap(user.getEmpId(), kpiItemIds);
        if (CollUtil.isNotEmpty(cmd.getMutualTypeAudits())) {
            Map<String, List<Rater>> mutuRaters = cmd.mutuTypeRaters(empMap, user.isOpenAvgWeightCompute());
            List<EmpEvalKpiType> kpiTypes = empEvalRule.inviteMutualTypeEmp(cmd.getScene(), mutuRaters);
            taskKpiRepo.updateEmpEvalKpiType(kpiTypes, cmd.getCompanyId(), cmd.getOpEmpId());
            if (!empEvalRule.isCustom()) {
                empEvalRule.refreshIndexRaters(cmd.getSceneConvert());
                if (StrUtil.equals(SubScoreNodeEnum.PEER_SCORE.getScene(), cmd.getScene())) {
                    empEvalRule.getS3PeerRater().setRaters(mutuRaters.get(kpiTypes.get(0).getKpiTypeId()));
                }
                if (StrUtil.equals(SubScoreNodeEnum.SUB_SCORE.getScene(), cmd.getScene())) {
                    empEvalRule.getS3SubRater().setRaters(mutuRaters.get(kpiTypes.get(0).getKpiTypeId()));
                }
                empRuleRepo.updateEmpEvalRule(empEvalRule);
            }
        }
        if (CollUtil.isNotEmpty(cmd.getMutualItemAudits())) {
            if (empEvalRule.isCustom()) {
                Map<String, List<Rater>> mutuRaters = cmd.mutuRaters(empMap, user.isOpenAvgWeightCompute());
                List<EvalItemScoreRule> changedRules = empEvalRule.inviteMutualEmp(cmd.getScene(), mutuRaters);
                empRuleRepo.updateMutualRule(changedRules);
                empEvalRule.refreshIndexRaters(cmd.getSceneConvert());
                empRuleRepo.updateEmpEvalRule(empEvalRule);
                if (CollUtil.isEmpty(inviteMutualAuditMap)) {
                    for (EmpEvalKpiType kpiType : empEvalRule.getKpiTypes().getDatas()) {
                        if (kpiType.isAskType()) {
                            if (CollUtil.isEmpty(mutuRaters.get(kpiType.getKpiTypeId()))) {
                                continue;
                            }
                            if (cmd.getScene().equals("peer_score")) {
                                empEvalRule.setS3PeerRater(kpiType.getPeerRater());
                                empEvalRule.getS3PeerRater().setRaters(mutuRaters.get(kpiType.getKpiTypeId()));
                            } else {
                                empEvalRule.setS3SubRater(kpiType.getSubRater());
                                empEvalRule.getS3SubRater().setRaters(mutuRaters.get(kpiType.getKpiTypeId()));
                            }
                            askEvalAcl.editAsk360MutualEvalRule(new ToDataBuilder<>(empEvalRule, EmpEvalRule.class).data(), cmd.getCompanyId(), kpiType.getAsk360EvalId(), cmd.getOpEmpId());
                        }
                    }
                    taskKpiRepo.updateEmpEvalKpiType(empEvalRule.listAsk360Types(), cmd.getCompanyId(), cmd.getOpEmpId());
                }
            } else {
                List<Rater> mutualRates = cmd.s3mutualRatesSimple(empMap, user.isOpenAvgWeightCompute());
                List<EvalItemScoreRule> changedRules = empEvalRule.inviteMutualEmp(cmd.getScene(), mutualRates);
                if (cmd.getScene().equals("peer_score")) {
                    empEvalRule.getS3PeerRater().setRaters(mutualRates);
                    empEvalRule.refreshIndexRaters("peer");
                } else {
                    empEvalRule.getS3SubRater().setRaters(mutualRates);
                    empEvalRule.refreshIndexRaters("sub");
                }
                empRuleRepo.updateMutualRule(changedRules);
                empRuleRepo.updateEmpEvalRule(empEvalRule);
                if (CollUtil.isEmpty(inviteMutualAuditMap)) {
                    for (String id : empEvalRule.listAsk360EvalId()) {
                        askEvalAcl.editAsk360MutualEvalRule(new ToDataBuilder<>(empEvalRule, EmpEvalRule.class).data(), cmd.getCompanyId(), id, cmd.getOpEmpId());
                    }
                }
            }
        }

        auditRepo.batchDelete(cmd.getCompanyId(), cmd.getTaskUserId(), kpiItemIds, cmd.getOpEmpId(), cmd.getScene());
        List<EvalAudit> auditList = cmd.audits(empEvalRule.getScoreConf());
        auditRepo.batchInsert(auditList);

        OperationLogDo logDo = new OperationLogDo(cmd.getCompanyId(), cmd.getTaskUserId(), OperationLogSceneEnum.ADD_MUTUAL_AUDIT.getScene(),
                cmd.getOpEmpId(), JSONUtil.toJsonStr(cmd.mutuRaterMap(raterEmpMap, "add")));
        opLogDao.addLog(logDo);

//        List<String> empIds = new ArrayList<>();
//        if (empEvalRule.isCustom()) {
//            for (EmpEvalKpiType kpiType : empEvalRule.getKpiTypes().getDatas()) {
//                customerAppointer(empIds, kpiType);
//            }
//        } else {
//            if (null != empEvalRule.getS3PeerRater().getAppointer()) {
//                WaitAppoint waitAppoint = empEvalRule.getS3PeerRater().getAppointer();
//                if (waitAppoint.getType().equals("emp")) {
//                    if(cmd.getEmpId().equals(cmd.getOpEmpId())){
//                        empIds.add(cmd.getEmpId());
//                    }
//                }
//                if (waitAppoint.getRaters().stream().anyMatch(rater -> rater.getEmpId().equals(cmd.getEmpId()))) {
//                    for (Rater rater : waitAppoint.getRaters()) {
//                        empIds.add(rater.getEmpId());
//                    }
//                }
//            }
//            if (null != empEvalRule.getS3SubRater().getAppointer()) {
//                WaitAppoint waitAppoint = empEvalRule.getS3SubRater().getAppointer();
//                if (waitAppoint.getType().equals("emp")) {
//                    empIds.add(cmd.getEmpId());
//                }
//                for (Rater rater : waitAppoint.getRaters()) {
//                    empIds.add(rater.getEmpId());
//                }
//            }
//        }

//        List<String> mutualEmpIds = new ArrayList<>();
//        //根据指标id去重
//        auditList = auditList.stream().filter(distinctByKpiItemId(EvalAudit::getKpiItemId)).collect(Collectors.toList());
//        for (EvalAudit evalAudit : auditList) {
//            //查询当前指标需要设置互评人的所有人，其中一个人设置了互评人，消除当前指标所有人的待办
//            List<MutualUser> mutualUserValues = userRepo.listMutualUserValue(evalAudit.getCompanyId(),
//                    new TaskId(evalAudit.getTaskId()), cmd.getTaskUserId(), evalAudit.getKpiItemId());
//            mutualUserValues.stream().forEach(mutualUser -> {
//                mutualEmpIds.addAll(Arrays.asList(mutualUser.getMutualUserValue().split(",")));
//            });
//        }
        //取消代办
//        new CancelTodoEvent(tenantId, empIds, cmd.getTaskUserId(), MsgSceneEnum.SET_MUTUAL_AUDIT.getType()).publish();

        InviteMutualEmpClearTodoDmSvc clearTodoDmSvc = new InviteMutualEmpClearTodoDmSvc(companyMsgCenterRepo, empEvalRule);
        clearTodoDmSvc.computeClearTodo(cmd.getOpEmpId(), user.getEmpId());
        clearTodoDmSvc.clear(new TenantId(cmd.getCompanyId()));
        EvalUser taskUser = userRepo.getTaskUser(tenantId, cmd.getTaskUserId());
        //是否需要审核
        if (CollUtil.isNotEmpty(inviteMutualAuditMap)) {
            Set<String> inveteAuditEmpIds = inviteMutualAuditMap.get(cmd.getScene());
            if (CollUtil.isEmpty(inveteAuditEmpIds)) {
                return null;
            }
            StringBuilder remark = new StringBuilder();
            remark.append(deptEmpDao.findEmployee(tenantId, cmd.getOpEmpId()).getName()).append("邀请了");
            remark.append(StrUtil.join(",", raterEmpMap.values().stream().collect(Collectors.toList()).stream().filter(e -> !Objects.equals(e.getEmpId(), cmd.getOpEmpId())).map(SimpleEmp::getEmpName).collect(Collectors.toList())));
            remark.append(",请审核");
            MsgSceneEnum msgScene = Objects.equals(cmd.getScene(), "peer_score") ? MsgSceneEnum.INVITE_PEER_AUDIT : MsgSceneEnum.INVITE_SUB_AUDIT;
            new MsgTodoAggregate(tenantId, taskUser.getTaskId(), new Name(empEvalRule.getTaskName()),
                    taskUser.getEmpId(), taskUser.getId())
                    .useScene(msgScene, CompanyMsgActionEnum.SET_MUTUAL_RATER)
                    .sendExtTodo().sendExtMsg().addCenterMsg()
                    .addExtTempValue("evalEmpName", taskUser.getEmpName())
                    .addExtTempValue("remark", remark.toString())
                    .addRecEmpId(inveteAuditEmpIds)
                    .publish();
            return null;
        }
        String tid = MDC.get("tid");
        EmpEvalRule rule = new EmpEvalRule();
        BeanUtils.copyProperties(empEvalRule,rule);
        tx.runAsyn(tid, () -> {
            explainEmpEvalScorer(tenantId, cmd.getOpEmpId(), taskUser,  rule);//解析评价关系
        });

        return new KpiItemUpdateFinishedValueEvent(tenantId, taskUser, new EmpId(cmd.getOpEmpId()));
    }

    @Transactional
    public KpiItemUpdateFinishedValueEvent inviteMutualEmpAudit(InviteMutualCmd cmd) {
        EmpEvalMerge empEvalRule = empRuleRepo.getEmpEvalMerge(new TenantId(cmd.getCompanyId()), cmd.getTaskUserId(), type | item | itemRule);
        EvalUser user = taskUserDao.getBaseEvalUser(new TenantId(cmd.getCompanyId()), cmd.getTaskUserId());

        //邀请的评分人
        List<String> mutualEmpIds = cmd.mutualEmpIds();
        Map<String, SimpleEmp> raterEmpMap = deptEmpDao.listByEmpAsMap(new TenantId(cmd.getCompanyId()), mutualEmpIds);

        List<String> kpiItemIds = new ArrayList<>();
        //先清除任务下互评指标数据
        for (MutualItemAudit mutualItemAudit : cmd.getMutualItemAudits()) {
            if (StrUtil.isNotBlank(mutualItemAudit.getKpiItemId())) {
                kpiItemIds.add(mutualItemAudit.getKpiItemId());
            }
        }
        Map<String, KpiEmp> empMap = kpiEmpDao.listByEmpAsMap(new TenantId(cmd.getCompanyId()), cmd.mutualEmpIds());
        if (CollUtil.isNotEmpty(cmd.getMutualTypeAudits())) {
            Map<String, List<Rater>> mutuRaters = cmd.mutuTypeRaters(empMap, user.isOpenAvgWeightCompute());
            List<EmpEvalKpiType> kpiTypes = empEvalRule.inviteMutualTypeEmp(cmd.getScene(), mutuRaters);
            taskKpiRepo.updateEmpEvalKpiType(kpiTypes, cmd.getCompanyId(), cmd.getOpEmpId());
        }
        if (CollUtil.isNotEmpty(cmd.getMutualItemAudits())) {
            if (empEvalRule.isCustom()) {
                Map<String, List<Rater>> mutuRaters = cmd.mutuRaters(empMap, user.isOpenAvgWeightCompute());
                List<EvalItemScoreRule> changedRules = empEvalRule.inviteMutualEmp(cmd.getScene(), mutuRaters);
                empRuleRepo.updateMutualRule(changedRules);
                empEvalRule.refreshIndexRaters(cmd.getSceneConvert());
                empRuleRepo.updateEmpEvalRule(empEvalRule);
                for (EmpEvalKpiType kpiType : empEvalRule.getKpiTypes().getDatas()) {
                    if (kpiType.isAskType()) {
                        if (CollUtil.isEmpty(mutuRaters.get(kpiType.getKpiTypeId()))) {
                            continue;
                        }
                        if (cmd.getScene().equals("peer_score")) {
                            empEvalRule.setS3PeerRater(kpiType.getPeerRater());
                            empEvalRule.getS3PeerRater().setRaters(mutuRaters.get(kpiType.getKpiTypeId()));
                        } else {
                            empEvalRule.setS3SubRater(kpiType.getSubRater());
                            empEvalRule.getS3SubRater().setRaters(mutuRaters.get(kpiType.getKpiTypeId()));
                        }
                        askEvalAcl.editAsk360MutualEvalRule(new ToDataBuilder<>(empEvalRule, EmpEvalRule.class).data(), cmd.getCompanyId(), kpiType.getAsk360EvalId(), cmd.getOpEmpId());
                    }
                }
                taskKpiRepo.updateEmpEvalKpiType(empEvalRule.listAsk360Types(), cmd.getCompanyId(), cmd.getOpEmpId());
            } else {
                List<Rater> mutualRates = cmd.s3mutualRates(empMap, user.isOpenAvgWeightCompute());
                List<EvalItemScoreRule> changedRules = empEvalRule.inviteMutualEmp(cmd.getScene(), mutualRates);
                if (cmd.getScene().equals("peer_score")) {
                    empEvalRule.getS3PeerRater().setRaters(mutualRates);
                    empEvalRule.refreshIndexRaters("peer");
                } else {
                    empEvalRule.getS3SubRater().setRaters(mutualRates);
                    empEvalRule.refreshIndexRaters("sub");
                }
                empRuleRepo.updateMutualRule(changedRules);
                empRuleRepo.updateEmpEvalRule(empEvalRule);
                for (String id : empEvalRule.listAsk360EvalId()) {
                    askEvalAcl.editAsk360MutualEvalRule(new ToDataBuilder<>(empEvalRule, EmpEvalRule.class).data(), cmd.getCompanyId(), id, cmd.getOpEmpId());
                }
            }
        }

        //取消代办
        Set<String> inveteAuditEmpIds = empEvalRule.inviteMutualAuditMap(user.getEmpId(), kpiItemIds).get(cmd.getScene());
        String sence = Objects.equals(cmd.getScene(), "peer_score") ? MsgSceneEnum.INVITE_PEER_AUDIT.getType() : MsgSceneEnum.INVITE_SUB_AUDIT.getType();
        List<CompanyMsgCenter> todos = centerRepo.finishByQuery(new TenantId(cmd.getCompanyId()), cmd.getTaskUserId(), Arrays.asList(sence), new ArrayList<>(inveteAuditEmpIds));
        CancelRemoteTodoEvent cancelRemoteTodoEvent = new CancelRemoteTodoEvent(new TenantId(cmd.getCompanyId()), todos);
        cancelRemoteTodoEvent.fire();

        auditRepo.batchDelete(cmd.getCompanyId(), cmd.getTaskUserId(), kpiItemIds, cmd.getOpEmpId(), cmd.getScene());
        List<EvalAudit> auditList = cmd.audits(empEvalRule.getScoreConf());
        auditRepo.batchInsert(auditList);

        OperationLogDo logDo = new OperationLogDo(cmd.getCompanyId(), cmd.getTaskUserId(), "audit_invite_mutual",
                cmd.getOpEmpId(), JSONUtil.toJsonStr(cmd.mutuRaterMap(raterEmpMap, "audit")));
        opLogDao.addLog(logDo);
        EvalUser taskUser = userRepo.getTaskUser(new TenantId(cmd.getCompanyId()), cmd.getTaskUserId());
        String tid = MDC.get("tid");
        EmpEvalRule rule = new EmpEvalRule();
        BeanUtils.copyProperties(empEvalRule,rule);
        tx.runAsyn(tid, () -> {
            explainEmpEvalScorer(new TenantId(cmd.getCompanyId()), cmd.getOpEmpId(), taskUser,  rule);//解析评价关系
        });

        return new KpiItemUpdateFinishedValueEvent(new TenantId(cmd.getCompanyId()), taskUser, new EmpId(cmd.getOpEmpId()));
    }

    private void customerAppointer(List<String> empIds, EmpEvalKpiType kpiType) {
        for (EvalKpi item : kpiType.getItems()) {
            if (item.isAutoItem()) {
                continue;
            }
            EvalItemScoreRule scoreRule = item.getItemScoreRule();
            if (Objects.isNull(scoreRule)) {
                if (null != kpiType.getPeerRater() && kpiType.getPeerRater().isOpen()) {
                    WaitAppoint waitAppoint = kpiType.getPeerRater().getAppointer();
                    if (Objects.isNull(waitAppoint)) {
                        continue;
                    }
                    if (waitAppoint.getType().equals("emp")) {
                        empIds.add(item.getEmpId());
                    }
                    for (Rater rater : waitAppoint.getRaters()) {
                        empIds.add(rater.getEmpId());
                    }
                }
                if (null != kpiType.getSubRater() && kpiType.getSubRater().isOpen()) {
                    WaitAppoint waitAppoint = kpiType.getSubRater().getAppointer();
                    if (Objects.isNull(waitAppoint)) {
                        continue;
                    }
                    if (waitAppoint.getType().equals("emp")) {
                        empIds.add(item.getEmpId());
                    }
                    for (Rater rater : waitAppoint.getRaters()) {
                        empIds.add(rater.getEmpId());
                    }
                }
                continue;
            }
            if (null != scoreRule.getPeerRater() && scoreRule.getPeerRater().isOpen()) {
                WaitAppoint waitAppoint = scoreRule.getPeerRater().getAppointer();
                if (Objects.isNull(waitAppoint)) {
                    continue;
                }
                if (waitAppoint.getType().equals("emp")) {
                    empIds.add(item.getEmpId());
                }
                for (Rater rater : waitAppoint.getRaters()) {
                    empIds.add(rater.getEmpId());
                }
            }
            if (null != scoreRule.getSubRater() && scoreRule.getSubRater().isOpen()) {
                WaitAppoint waitAppoint = scoreRule.getSubRater().getAppointer();
                if (Objects.isNull(waitAppoint)) {
                    continue;
                }
                if (waitAppoint.getType().equals("emp")) {
                    empIds.add(item.getEmpId());
                }
                for (Rater rater : waitAppoint.getRaters()) {
                    empIds.add(rater.getEmpId());
                }
            }
        }
    }

    @Transactional
    public void updateEvalRuleStatus(String companyId, String taskUserId, Integer ruleConfStatus, String
            ruleConfError) {
        empRuleRepo.updateEvalRuleStatus(companyId, taskUserId, ruleConfStatus, ruleConfError);
    }

    public List<TransferRater> getTransferReviewers(String companyId, String userId) {
        List<TransferRater> transferReviewers = taskUserDao.getTransferReviewers(companyId, userId);
        if (CollUtil.isNotEmpty(transferReviewers) && "task_interview_confirm".equals(transferReviewers.get(0).getScene())) {
            transferReviewers.forEach(transferRater -> {
                EvalTaskInterviewConfirm confirm = confirmDao.getInterviewConfirm(companyId, userId, transferRater.getEmpId());
                transferRater.setFlowNodeId(confirm.getFlowNodeId());
            });
        }
        return transferReviewers;
    }

    @Transactional
    public void sendInputOnScoreIfNeed(SendTodoForInputOnScoreEvent event) {
        String userId = event.getUserId();
        Boolean exist = companyMsgCenterDao.existAutoItemMsg(new TenantId(event.getTenantId()), userId);
        if (exist) {
            return;
        }
        EvalUser taskUser = userRepo.getTaskUser(new TenantId(event.getTenantId()), userId);
        List<EvalKpi> autoItems = taskUser.getAutoItem();
        if (CollUtil.isEmpty(autoItems)) {
            return;
        }
        //给自动评分指标的录入人发送消息
        Map<String, List<String>> inputEmpMap = new HashMap<>();
        for (EvalKpi evalKpi : autoItems) {
            setResultInputItem(inputEmpMap, evalKpi);
        }
        inputEmpMap.forEach((receiveId, kpiItemIdList) -> {
            MsgTodoAggregate msgSend = new MsgTodoAggregate(taskUser.getCompanyId(), taskUser.getTaskId(), event.getTaskName(), taskUser.getEmpId(), taskUser.getId())
                    .addExtTempValue("empName", taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                    .addTodoItem("msg.task.emp", taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                    .useScene(MsgSceneEnum.TASK_SUBMIT_AUTO_ITEM_PROGRESS, CompanyMsgActionEnum.SUBMIT_PROGRESS);
            if (event.getSendInputMsg()) {
                msgSend.addCenterMsg().sendExtMsg().sendExtTodo().addRecEmpId(receiveId).publish();//全部发送
            } else {
                msgSend.addCenterMsg().addRecEmpId(receiveId).publish();//只发内部待办
            }
        });
    }

    public List<ShowSupOrgPo> showSupOrg(TenantId tenantId, List<ShowSupOrg> supOrgs) {
        return kpiOrgDao.showSupOrg(tenantId, supOrgs);
    }

    public List<String> listExistEvalOrg(TenantId tenantId, TaskId taskId, List<String> evalOrgIds) {
        return empEvalDao.listExistEvalOrg(tenantId, taskId, evalOrgIds);
    }

    @Transactional
    public void editOrgEvalOwner(ModifyOrgTableOwnerCmd cmd) {
        EvalUser user = userRepo.getBaseTaskUser(cmd.getTenantId(), cmd.getTaskUserId());
        userRepo.loadKpis(user);
        //评分之前可以跟换负责人
        if (!TalentStatus.DRAW_UP_ING.getStatus().equals(user.getTaskStatus())) {
            throw new KpiI18NException("40009", "发起考核后无法更换负责人");
        }
        EmpEvalRule evalRule = empEvalDao.getBaseEvalRule(cmd.getTenantId(), cmd.getTaskUserId());
        user.addEvalEmp(cmd.getNewOrgOwnerId(), cmd.getNewOrgOwnerOrgId(), cmd.getNewOrgOwnerOrgName(),
                cmd.getNewOrgOwnerAvatar(), cmd.getNewOrgOwnerName());
        userRepo.updateTaskUser(user);
        if (evalRule == null) {
            return;
        }
        evalRule = empEvalDao.getEmpEvalRule(cmd.getTenantId(), cmd.getTaskUserId());
        evalRule.replaceKpiOwner(cmd.getNewOrgOwnerId());
        user.setEmpEvalRule(evalRule);
        //删除以前生成的数据
        empRuleRepo.clearRuleConf(cmd.getTenantId(), user.getId());
        //重新应用一下周期，任务的规则，重新解析一下抽象的负责人
        AdminTask task = adminTaskRepo.getAdminTask(cmd.getTenantId(), user.getTaskId());
        evalDmSvc.mergeConf(task, user, cmd.getOpEmpId().getId());
        empRuleRepo.addEmpEvalRule(user, evalRule, null, false);
    }

    public List<ResultAuditRecordPo> getFinalAuditResult(TenantId tenantId, String taskUserId) {
        return evalTaskDao.getFinalAuditResult(tenantId, taskUserId);
    }

    public PagedList<EvalResultPo> pagedEvalResult(EvalUserQuery query) {
        query.setAdminOrgIds(kpiOrgDao.listAdminScopePrivOrgIds(query.getCompanyId(), query.getOpEmpId(), null));
        if (query.isPriv()) {
            query.setTaskIdList(cycleEvalDao.listTaskPrivByOpEmpId(query.getCompanyId(), query.getCycleId(), query.getOpEmpId()));
        }
        if (StrUtil.isNotBlank(query.getOrgId())) {
            query.setOrgIds(kpiOrgDao.listAllChildOrgIds(new TenantId(query.getCompanyId()), StrUtil.splitTrim(query.getOrgId(), ",")));
        }
        PagedList<EvalResultPo> resultPos = taskUserDao.pagedEvalResult(query);
        List<EvalResultPo> rss = resultPos.getData();
        if (CollUtil.isEmpty(rss)) {
            return resultPos;
        }
        List<String> taskUserIds = resultPos.getData().stream().map(po -> po.getId()).collect(Collectors.toList());

        ListWrap<AuditResultEvalType> typeLevelGroups = calibratedDao.listTypeLevelResultPo(query.getCompanyId(), taskUserIds);
        resultPos.forEach(po -> po.setTypes(typeLevelGroups.groupGet(po.getId())));

        //显示上级
        //List<String> curOrgIds = rss.stream().filter(r -> StrUtil.isNotBlank(r.matchOrId())).map(r -> r.matchOrId()).collect(Collectors.toList());
        //List<KpiOrgSupNames> supNames = kpiOrgDao.listOrgSupNames(new TenantId(query.getCompanyId()), curOrgIds);
        rss.forEach(r -> r.repaceOrgNamePath());
        return resultPos;
    }

    public List<BindEvalResult> testBindMatch(TestBindEvalQuery qry) {
        List<BindEvalResult> rs = new ArrayList<>();
        //针对个人任务做异常检查
        Map<String, List<EvalBind>> persons = evalTaskDao.testPersonBindMactch(qry);
        ////关联其它任务只需求查询关联出绩效结果
        List<EvalBind> others = evalTaskDao.listOtherBinds(qry);
        for (MainEval mainEval : qry.getMainEvals()) {
            BindEvalResult bindRs = new BindEvalResult(mainEval.getTaskUserId(), qry.getMainWeight());
            bindRs.addAllPersonBind(persons.get(mainEval.getTaskUserId()));
            bindRs.addAllOtherBind(others);
            rs.add(bindRs);
        }
        return rs;
    }

    public void saveRefEval(String companyId, List<BindEvalResult> batchRs) {
        List<String> mainUserIds = CollUtil.map(batchRs, BindEvalResult::getMainTaskUserId, true);

        List<EvalUser> evalUsers = taskUserDao.listEvalUser(companyId, null, mainUserIds);
        Cycle cycle = cycleDao.find(companyId, evalUsers.get(0).getCycleId());
        ScoreRuleSnapMatchDmSvc snapMatch = new ScoreRuleSnapMatchDmSvc(cycle, mainUserIds);
        snapMatch.onMod(onTaskRepo, onEmpRepo);
        snapMatch.match(batchRs);
        tx.runTran(() -> evalTaskDao.saveRefEval(companyId, batchRs));
    }


    @Transactional
    public void publicRefEval(String companyId, List<String> taskUserIds) {
        for (String userId : taskUserIds) {
            EvalUser taskUser = userRepo.getBaseTaskUser(new TenantId(companyId), userId);
            EmpEvalMerge empEvalMerge = empRuleRepo.getEmpEvalMerge(new TenantId(companyId), userId, all);
            new MsgTodoAggregate(new TenantId(companyId), empEvalMerge.getTaskId(), new Name(empEvalMerge.getTaskName()),
                    taskUser.getEmpId(), taskUser.getId())
                    .useScene(MsgSceneEnum.EVAL_REF_RESULT_PUBLIC)
                    .addExtTempValue("resultStr", String.format("%s分 | %s", taskUser.getScoreOfRef().setScale(2), taskUser.getEvaluationLevel()))
                    .addExtTempValue("score", taskUser.getFinalScore() == null ? new BigDecimal(0.00).toString() : taskUser.getFinalScore().toString())
                    .addExtTempValue("evalEmpName", taskUser.getEmpName())
                    .addExtTempValue("value", "")
                    .sendExtMsg().addRecEmpId(taskUser.getEmpId()).publish();
        }
    }

    @Transactional
    public void removeRefEval(String companyId, List<String> taksUserIds) {
        evalTaskDao.removeRefEval(companyId, taksUserIds);
    }

    //用于接收分数变化事件
    @Transactional
    public List<String> computeScoreAfterChange(String companyId, String changedUserId) {
        boolean closeCompute = tenantSysConfRepo.isOpen(companyId, "close_compute_score_of_ref_20250624");//关闭关联计算
        List<String> mainUserIds = evalTaskDao.listNeedComputeMainUserId(companyId, changedUserId);
        if (mainUserIds.isEmpty()) {
            return Collections.singletonList(changedUserId);
        }
        List<EvalUser> evalUsers = taskUserDao.listEvalUser(companyId, null, mainUserIds);
        if (evalUsers.isEmpty()) {
            return Collections.singletonList(changedUserId);
        }
        List<String> cycleIds = CollUtil.map(evalUsers, evalUser -> evalUser.getCycleId(), true);
        Map<String, Cycle> cycleMap = cycleDao.listCycleAsMap(companyId, cycleIds);

        //自己也有可能是关联的主任务
        mainUserIds.add(changedUserId);
        for (EvalUser evalUser : evalUsers) {
            ScoreRuleSnapMatchDmSvc snapMatch = new ScoreRuleSnapMatchDmSvc(cycleMap.get(evalUser.getCycleId()), Arrays.asList(evalUser.getId()));
            snapMatch.onMod(onTaskRepo, onEmpRepo);
            snapMatch.match(Arrays.asList(evalUser));
            evalTaskDao.computeScoreOfRef(companyId, evalUser, closeCompute);
        }
        return mainUserIds;
    }

    public BindEvalResult getBindEvalResult(String companyId, String mainUserId) {
        return evalTaskDao.getBindEvalResult(companyId, mainUserId);
    }

    public PagedList<ExportTargetValue> pagedImportTarget(TaskTargetQuery query) {

        query.setAdminOrgIds(kpiOrgDao.listAdminScopePrivOrgIds(query.getCompanyId(), query.getOpEmpId(), null));  //获取管理权限范围
        //1、查分页
//        PagedList<ExportTargetValue> taskUsers = evalTaskDao.pagedImportTarget(query);
        PagedList<ExportTargetValue> taskUsers = evalTaskDao.pagedImportTargetOptimized(query);
        if (CollUtil.isEmpty(taskUsers.getData())) {
            return taskUsers;
        }
        List<ExportTargetValue> taskUsersData = taskUsers.getData();
        //收集taskUserIds
        List<String> taskUserIds = taskUsersData.stream().map(ExportTargetValue::getTaskUserId).collect(Collectors.toList());
        //2、查分页记录的指标
        List<EvalItem> items = evalTaskDao.listEvalItem(query.getCompanyId(), taskUserIds, query.getKpiItemIds());
        //收集itemId
        List<String> itemIds = items.stream().map(EvalItem::getKpiItemId).collect(Collectors.toList());
        //3、查分页记录指标对应的字段
        List<EvalItemField> fields = evalTaskDao.listEvalField(query.getCompanyId(), taskUserIds, itemIds);
        Map<String, List<EvalItemField>> fieldMap = fields.stream()
                .peek(EvalItemField::removeZero)
                .collect(Collectors.groupingBy(field -> field.getKpiItemId() + ":" + field.getTaskUserId()));
        for (EvalItem itemPo : items) {
            itemPo.setFields(fieldMap.get(itemPo.getKpiItemId() + ":" + itemPo.getTaskUserId()));
        }
        //可能存在没有需要录入的字段的指标，需要过滤
        items.removeIf(itemPo -> itemPo.getFields() == null || itemPo.getFields().isEmpty());
        Map<String, List<EvalItem>> itemMap = items.stream().collect(Collectors.groupingBy(EvalItem::getTaskUserId));
        for (ExportTargetValue datum : taskUsersData) {
            datum.setItems(itemMap.get(datum.getTaskUserId()));
        }
        return taskUsers;
    }

//    public List<TaskEvalItemPo> listImportTarget(TaskTargetQuery query) {
//        return evalTaskDao.listImportTarget(query);
//    }

    public PagedList<EvalKpiItemPo> pagedEvalItem(EvalItemSelectQuery query) {

        return evalTaskDao.pagedEvalItem(query);
    }

    public PagedList<EmpStaff> pagedEvalEmpOrg(EvalEmpSelectQuery query) {
        if (query.getPerformanceType() == 2) {
            return evalTaskDao.pagedEvalOrg(query);
        } else {
            return evalTaskDao.pagedEvalEmp(query);
        }
    }

    @Transactional
    public void batchUpdateTarget(BatchUpdateTargetCmd cmd) {
        List<EvalUser> users = taskUserDao.listBaseEvalUser(new TenantId(cmd.getCompanyId()), cmd.getTaskUserIds());
        ListWrap<EvalUser> wrap = new ListWrap<>(users).asMap(u -> u.getId());
        AtomicInteger successCnt = new AtomicInteger();
        List<EvalKpi> totalKpis = new ArrayList<>();
        List<KpiItemUsedField> totalFields = new ArrayList<>();

        //先拿到指标
        cmd.getEvalUserItems().stream().forEach(po -> {
            EvalUser evalUser = wrap.getMap().get(po.getTaskUserId());
            if (Objects.isNull(evalUser)) {
                return;
            }
            if (!Objects.equals(evalUser.getTaskStatus(), TalentStatus.DRAW_UP_ING.getStatus())) {
                return;
            }

            //优化成批量更新
            List<cn.com.polaris.kpi.eval.EvalItem> items = po.getItemTargets();
            items.stream().forEach(item -> {
                List<cn.com.polaris.kpi.eval.EvalItemField> fields = item.getFields();
                List<EvalKpi> targetValueList = fields.stream()
                        .filter(field -> "targetValue".equals(field.getFieldId()))
                        .map(field -> {
                            EvalKpi kpiDo = new EvalKpi();
                            kpiDo.setItemTargetValue(new BigDecimal(field.getFieldValue()));
                            kpiDo.setKpiItemId(item.getKpiItemId());
                            kpiDo.setCompanyId(new TenantId(cmd.getCompanyId()));
                            kpiDo.setTaskUserId(po.getTaskUserId());
                            kpiDo.setUpdatedTime(new Date());
                            kpiDo.setUpdatedUser(cmd.getOpEmpId());
                            return kpiDo;
                        })
                        .collect(Collectors.toList());
                totalKpis.addAll(targetValueList);

                List<KpiItemUsedField> customFieldList = fields.stream()
                        .filter(field -> !"targetValue".equals(field.getFieldId()))
                        .map(field -> {
                            KpiItemUsedField usedField = new KpiItemUsedField();
                            usedField.setCompanyId(cmd.getCompanyId());
                            usedField.setKpiItemId(item.getKpiItemId());
                            usedField.setTaskUserId(po.getTaskUserId());
                            usedField.setFieldId(field.getFieldId());
                            usedField.setValue(field.getFieldValue());
                            usedField.setUpdatedTime(new Date());
                            usedField.setUpdatedUser(cmd.getOpEmpId());
                            return usedField;
                        })
                        .collect(Collectors.toList());

                totalFields.addAll(customFieldList);
            });

//            po.getItemTargetPos().stream().forEach(item -> {
//                    successCnt.addAndGet(taskKpiRepo.batchUpdateItemTarget(cmd.getCompanyId(), cmd.getOpEmpId(), po.getTaskUserId(), item.getKpiItemId(), item.getKpiItemName(), item.getTargetValue()));
//            });
        });
        successCnt.addAndGet(taskKpiRepo.batchUpdateTargetValue(cmd.getCompanyId(), cmd.getTaskUserIds(), totalKpis));
        successCnt.addAndGet(taskKpiRepo.batchUpdateCustomFieldValue(cmd.getCompanyId(), cmd.getTaskUserIds(), totalFields));

        cmd.setSuccessCnt(successCnt.get());
    }

    public void batchUpdateTarget(String companyId, String taskId, List<String> taskUserIds, List<EvalKpi> totalKpis, List<KpiItemUsedField> totalFields) {

        if (CollUtil.isEmpty(taskUserIds)) {
            throw new KpiI18NException("invalid file", "无效的导入文件");
        }

        List<EvalUser> users = taskUserDao.listBaseEvalUser(new TenantId(companyId), taskUserIds);
        if (CollUtil.isNotEmpty(users)) {
            if (!users.get(0).getTaskId().equals(taskId)) {
                throw new KpiI18NException("file not match task", "导入文件与任务不匹配");
            }
        } else {
            throw new KpiI18NException("invalid file", "无效的导入文件");
        }

        //排除掉不是DRAW_UP_ING的考核
        List<String> finalTaskUserIds = users.stream().filter(user -> Objects.equals(user.getTaskStatus(), TalentStatus.DRAW_UP_ING.getStatus())).map(EvalUser::getId).collect(Collectors.toList());

        //按照taskUserId进行分批处理
        int batchSize = 50;
        List<List<String>> taskUserIdBatches = Lists.partition(finalTaskUserIds, batchSize);
        tx.runTran(() -> {
            for (List<String> taskUserIdBatch : taskUserIdBatches) {
                List<EvalKpi> evalKpiBatch = totalKpis.stream().filter(kpi -> taskUserIdBatch.contains(kpi.getTaskUserId())).collect(Collectors.toList());
                List<KpiItemUsedField> kpiItemUsedFieldBatch = totalFields.stream().filter(kpi -> taskUserIdBatch.contains(kpi.getTaskUserId())).collect(Collectors.toList());
                taskKpiRepo.batchUpdateTargetValue(companyId, taskUserIdBatch, evalKpiBatch);
                taskKpiRepo.batchUpdateCustomFieldValue(companyId, taskUserIdBatch, kpiItemUsedFieldBatch);
            }
        });
    }


    public List<EvalItemPo> listItemHead(TaskTargetQuery query) {
        return evalTaskDao.listItemHead(query);
    }

    public ImportEvalEmpPo parseEvalEmp(MultipartFile file, AddEvalEmpCmd cmd) {
        ImportEvalEmpPo po = new ImportEvalEmpPo();
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            final int headRow = 0;
            final Excel2MapListener listener = new Excel2MapListener(headRow);
            EasyExcel.read(inputStream, listener)
                    .sheet().headRowNumber(headRow + 1).doRead();
            //解析Ecxel为Map对象
            BaseExcelData importData = listener.getImportData();
            log.info("上传文件解析={}", JSONObject.toJSONString(importData));
            List<Map<String, String>> datas = importData.getDatas();
            if (CollUtil.isEmpty(datas)) {
                log.info("解析文件内容为空！");
                return po;
            }
            ImportEvalEmpQuery query = new ImportEvalEmpQuery(cmd.getTenantId(), datas);
            List<EvalEmp> evalEmps = empEvalDao.listImportEvalEmp(query);
            cmd.setEmps(evalEmps);
            //导入成功人数
            po.setSucceedCnt(evalEmps.size());
            //统计导入失败数量  上传文件中人数-查询出的人数s
            po.setFailedCnt(datas.size() - evalEmps.size());
            //统计导入失败人员
            List failList = new ArrayList();
            List<String> exUserIds = evalEmps.stream().map(e -> e.getExUserId()).collect(Collectors.toList());
            datas.stream().forEach(d -> {
                if (!exUserIds.contains(d.get(0))) {
                    failList.add(d.get(1));
                }
            });
            po.setFailNames(failList);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return po;
    }

    public ImportEvalEmpPo parseEvalOrg(MultipartFile file, AddEvalEmpCmd cmd) {
        ImportEvalEmpPo po = new ImportEvalEmpPo();
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            final int headRow = 0;
            final Excel2MapListener listener = new Excel2MapListener(headRow);
            EasyExcel.read(inputStream, listener)
                    .sheet().headRowNumber(headRow + 1).doRead();
            //解析Ecxel为Map对象
            BaseExcelData importData = listener.getImportData();
            log.info("上传文件解析={}", JSONObject.toJSONString(importData));
            List<Map<String, String>> datas = importData.getDatas();
            if (CollUtil.isEmpty(datas)) {
                log.info("解析文件内容为空！");
                return po;
            }
            ImportEvalEmpQuery query = new ImportEvalEmpQuery(cmd.getTenantId(), datas);
            List<EvalOrg> evalOrgs = empEvalDao.listImportEvalOrg(query);
            cmd.setEvalOrgs(evalOrgs);
            //统计导入失败数量  上传文件中行数-查询出的记录
            po.setSucceedCnt(evalOrgs.size());
            po.setFailedCnt(datas.size() - evalOrgs.size());
            //统计导入失败部门
            List failList = new ArrayList();
            List<String> dingOrgIds = evalOrgs.stream().map(e -> e.getDingOrgId()).collect(Collectors.toList());
            datas.stream().forEach(d -> {
                if (!dingOrgIds.contains(d.get(0))) {
                    failList.add(d.get(1));
                }
            });
            po.setFailNames(failList);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return po;
    }

    public String checkFormula(FormulaVerifyCmd formula) {
        try {
            Object eval = new JSElComputer(formula.getFormula()).eval(formula.getMap());
            return eval.toString();
        } catch (ArithmeticException exception) {
            return "0";
        } catch (Exception e) {
            throw new KpiI18NException("formula verify fail", e.getMessage());
        }
    }

    public PagedList<ReportSeriesDataVO> pageEvaluateSeries(TaskUserQuery query) {
        query.setAdminOrgIds(kpiOrgDao.listAdminScopePrivOrgIds(query.getCompanyId(), query.getOpEmpId(), null));
        return taskUserDao.pageEvaluateTaskUser(query);
    }

    public PagedList<ReportSeriesDataVO> pageMeasurableItemProgress(TaskKpiItemQuery query) {
        query.setAdminOrgIds(kpiOrgDao.listAdminScopePrivOrgIds(query.getCompanyId(), query.getOpEmpId(), null));
        return taskKpiItemDao.pageMeasurableItemProgress(query);
    }

    public PagedList<EvalUserOfDeptPo> pagedEmpOfDept(ReportSeriesQuery query) {
        query.setAdminOrgIds(kpiOrgDao.listAdminScopePrivOrgIds(query.getTenantId().getId(), query.getOpEmpId(), null));
        return taskUserDao.pagedEmpOfDept(query);
    }

    public PagedList<EvalUserOfDeptPo> pageEvalUserOfDept(ReportSeriesQuery query) {
        query.setAdminOrgIds(kpiOrgDao.listAdminScopePrivOrgIds(query.getTenantId().getId(), query.getOpEmpId(), null));
        return taskUserDao.pageEvalUserOfDept(query);
    }

    //更新关联kr标识
    public void updateKpiOKRRefFlag(String companyId, String createdUser, String ids, String okrRefFlag) {
        List<String> idList = StrUtil.splitTrim(ids, ",");
        taskKpiRepo.updateOkrRefFlag(companyId, createdUser, idList, okrRefFlag);
    }

    //okr更新完成值目标值
    public void updateFinishOrTargetValue(String companyId, String createdUser, String id, String finishValue, String targetValue) {
        PerfEvaluateTaskKpiDo kpi = taskKpiItemDao.findById(id);
        if (Objects.isNull(kpi)) {
            return;
        }
        EvalUser bothTaskUser = taskUserDao.getBothBaseUser(companyId, null, null, kpi.getTaskUserId());
        if (TalentStatus.statusOf(bothTaskUser.getTaskStatus()).afterEq(TalentStatus.FINISH_VALUE_AUDIT)) {
            return;
        }
        if (StrUtil.isNotBlank(finishValue)) {
            taskKpiRepo.updateFinishValue(companyId, createdUser, id, new BigDecimal(finishValue));
            String opEmpId = StrUtil.isNotBlank(createdUser) ? createdUser : kpi.getCreatedUser();//操作人记录优化：如果传过来的不为空，则使用传过来的记录人，如果为空就还使用原来逻辑的记录人
            //记录日志
            OperationLogDo logModel = new OperationLogDo(companyId, bothTaskUser.getId(), OperationLogSceneEnum.INPUT_FINISH_VALUE.getScene(), opEmpId,
                    kpi.getKpiItemId(), kpi.getKpiItemName(), kpi.getItemUnit(),
                    StringTool.getBigDecimalToStringWithoutZero(kpi.getItemFinishValue()),
                    ObjectUtils.defaultIfNull(finishValue, ""));
            opLogDao.addLog(logModel);
        }
        if (StrUtil.isNotBlank(targetValue)) {
            taskKpiRepo.updateTargetValue(companyId, createdUser, id, new BigDecimal(targetValue));
            log.info("目标值变更成功");
            //变更操作日志
            List list = new ArrayList();
            Map<String, Object> eventMap = new HashMap<>();
            eventMap.put("changeType", "okrTargetValChange");
            eventMap.put("currentVal", targetValue);
            eventMap.put("originVal", kpi.getItemTargetValue());
            list.add(eventMap);
            ModificationValue modificationValue = new ModificationValue("modifyIndex", kpi.getKpiItemName(), kpi.getItemUnit(), list);
            JSONArray jsonArray = new JSONArray();
            jsonArray.add(JSONObject.toJSON(modificationValue));
            log.info("目标值变更日志jsonArray={}", jsonArray.toString());
            String businessScene = bothTaskUser.getTaskStatus().equals(TalentStatus.CONFIRMING) ? "confirm_item" : OperationLogSceneEnum.CHANGE_ITEM.getScene();
            userRepo.saveModification(new TenantId(companyId), new EmpId(kpi.getEmpId()), bothTaskUser.getId(), businessScene, BusinessConstant.PASS, jsonArray.toString());
        }
    }

    /**
     * 查询OKR关联的指标详情
     *
     * @param taskKpiId
     * @return
     */
    public List<OKRGetItemInfoReturnVO> queryOkrRefItemInfo(String taskKpiId) {
        List<OKRGetItemInfoReturnVO> list = taskKpiItemDao.queryOkrRefItemInfo(StrUtil.splitTrim(taskKpiId, ","));
        log.info("查询OKR关联的指标详情 list:{}", JSONUtil.toJsonStr(list));
        return list;
    }

    /**
     * OKR查询任务
     * ：
     *
     * @param queryVO
     * @return
     */
    public List<OKRGetTaskReturnVO> getTaskList(OKRTaskQueryVO queryVO) {
        setCompanyAndUserInfo(queryVO);
        if (StringUtils.isEmpty(queryVO.getCompanyId())) {
            return null;
        }

        List<OKRItemDetailVO> list = taskKpiItemDao.getTaskList(queryVO);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        Map<String, List<OKRItemDetailVO>> taskMap = list.stream().collect(Collectors.groupingBy(OKRItemDetailVO::getTaskId));
        List<OKRGetTaskReturnVO> res = new ArrayList<>();
        taskMap.forEach((k, v) -> {
            OKRGetTaskReturnVO taskReturnVO = new OKRGetTaskReturnVO();
            taskReturnVO.setTaskId(k);
            taskReturnVO.setTaskName(v.get(0).getTaskName());
            taskReturnVO.setCycleStartDate(v.get(0).getCycleStartDate());
            taskReturnVO.setCycleEndDate(v.get(0).getCycleEndDate());
            taskReturnVO.setCycleType(v.get(0).getCycleType());
            taskReturnVO.setTaskStatus(v.get(0).getTaskStatus());
            taskReturnVO.setItems(com.perf.www.common.utils.bean.Convert.convertListOnlyMatch(v, OKRGetItemInfoReturnVO.class));
            res.add(taskReturnVO);
        });
        return res;
    }

    private boolean setCompanyAndUserInfo(OKRTaskQueryVO queryVO) {

        CompanyDo companyDo = companyDao.findByDingCorpId(queryVO.getCorpId());
        if (Objects.nonNull(companyDo)) {
            queryVO.setCompanyId(companyDo.getId());
        } else {
            return true;
        }

        if (StringUtils.isNotEmpty(queryVO.getDingUserId())) {
            KpiEmployee employee = empDao.findEmpByDingUserId(new TenantId(companyDo.getId()), queryVO.getDingUserId());
            if (Objects.nonNull(employee)) {
                queryVO.setEmpId(employee.getEmployeeId());
            } else {
                queryVO.setEmpId(queryVO.getDingUserId());
            }
        }
        return false;
    }

    public List<OKRGetTaskReturnVO> queryOKRTaskList(OKRTaskQueryVO queryVO) {
        setCompanyAndUserInfo(queryVO);
        if (StringUtils.isEmpty(queryVO.getCompanyId())) {
            return null;
        }
        return evalTaskDao.queryOKRTaskList(queryVO);
    }

    public List<OKRGetItemInfoReturnVO> queryTaskItems(OKRTaskQueryVO queryVO) {
        setCompanyAndUserInfo(queryVO);
        if (StringUtils.isEmpty(queryVO.getCompanyId())) {
            return null;
        }
        log.info("执行新sql");
        return taskKpiItemDao.queryTaskItems(queryVO);
    }

    @DmEventPublish
    public void enterFinishedValueAudit(List<String> taskUserIds, String companyId, String taskId) {
        if (CollUtil.isEmpty(taskUserIds)) {
            taskUserIds = taskUserDao.listTaskUserIdByTask(companyId, taskId);
        }
        for (String taskUserId : taskUserIds) {
            EvalUser evalUser = userRepo.getTaskUser(new TenantId(companyId), taskUserId);
            evalUser.setCompanyId(new TenantId(companyId));
            new FinishedValueAuditEvent(new TenantId(companyId), taskUserId, evalUser, new EmpId("-1")).publish();
        }
    }


    public void fixMsgTodoConfirmItem(TenantId tenantId, String taskUserId, String confirmEmpId) {
        EvalUser empEval = userRepo.getEmpEval(tenantId, taskUserId);
        AdminTask adminTask = adminTaskRepo.getAdminTask(tenantId, empEval.getTaskId());
        new MsgTodoAggregate(empEval.getCompanyId(), empEval.getTaskId(), new Name(adminTask.getTaskName()), empEval.getEmpId(), empEval.getId())
                .useScene(MsgSceneEnum.TASK_CONFIRM_AUDIT, null)
                .addExtTempValue("evalEmpName", empEval.getEvalOrgName() == null ? empEval.getEmpName() : empEval.getEvalOrgName())
                .addTodoItem("msg.task.emp", empEval.getEvalOrgName() == null ? empEval.getEmpName() : empEval.getEvalOrgName())
                .sendExtMsg().addCenterMsg().sendExtTodo()
                .addRecEmpId(StrUtil.splitTrim(confirmEmpId, ",")).publish();
    }


    /**
     * 获取经营计划指标详情
     */
    public List<BusinessPlanItem> listBusinessPlanItem(String companyId, String taskUserId, String kpiItemIds) {
        return objectAcl.listBusinessPlanItem(companyId, taskUserId, kpiItemIds);
    }


    /**
     * 编辑经营指标完成值
     *
     * @param companyId
     * @param opEmpId
     * @param okrGoalId
     * @param finishValue
     */
    public void updatePlanItemFinishValue(String companyId, String opEmpId, String okrGoalId, String finishValue, String updatedMemo) {
        if (StrUtil.isNotBlank(finishValue) && !StringTool.isNumber(finishValue)) {
            throw new RuntimeException("Wrong format of parameter finishValue");
        }
        if (StrUtil.isBlank(finishValue)) {
            throw new RuntimeException("The required parameter is empty[finishValue]");
        }
        List<EvalKpi> items = taskKpiItemDao.listNoInputPlanItemByGoalId(companyId, okrGoalId);
        if (CollUtil.isEmpty(items)) {
            return;
        }
        if (StrUtil.isNotBlank(finishValue)) {
            taskKpiRepo.updatePlanItemFinishValue(CollUtil.map(items, kpi -> kpi.getId(), true), opEmpId, new BigDecimal(finishValue));
            //记录日志
            List<OperationLogDo> logDoList = new ArrayList<>();
            items.stream().collect(Collectors.groupingBy(i -> i.getTaskUserId())).forEach((k, v) -> {
                StringBuilder sb = new StringBuilder();
                for (EvalKpi kpi : v) {
                    sb.append("●   ").append(kpi.getKpiItemName()).append("    ")
                            .append(finishValue).append(kpi.getItemUnit()).append("|")
                            .append(kpi.getPercent(new BigDecimal(finishValue)) + "%").append(System.lineSeparator());

                    OperationLogDo logModel = new OperationLogDo(companyId, k, OperationLogSceneEnum.INPUT_PLAN_FINISH_VALUE.getScene(), opEmpId,
                            kpi.getKpiItemId(), kpi.getKpiItemName(), kpi.getItemUnit(),
                            StringTool.getBigDecimalToStringWithoutZero(kpi.getItemFinishValue()),
                            ObjectUtils.defaultIfNull(finishValue, "").toString());
                    logDoList.add(logModel);
                }
                EvalUser updatedEvalUser = userRepo.getTaskUser(new TenantId(companyId), k);
                updatedEvalUser.setInputFinishChanged(sb.toString().replaceAll("(?<!\n)\n$", ""));
                updatedInputFinishStatus(new TenantId(companyId), opEmpId, updatedEvalUser);
            });
            opLogDao.batchAddLogs(logDoList);
        }
    }

    @NotNull
    private Integer updatedInputFinishStatus(TenantId companyId, String opEmpId, EvalUser updatedEvalUser) {
        //需要重新查询更新完成之后的指标信息统计更新完成值录入状态
        updatedEvalUser.updateInputFinishStatus();
        userRepo.updateInputFinishStatus(updatedEvalUser.getInputFinishStatus(),
                updatedEvalUser.getId(), updatedEvalUser.getCompanyId());

        //发送变更通知
        AdminTask adminTask = adminTaskDao.getAdminTaskBase(companyId, new TaskId(updatedEvalUser.getTaskId()));
        if (adminTask.isOpenSendFinishValueChangedMsg()) {
            Emp emp = kpiEmpDao.findEmp(companyId, opEmpId);
            empEvalDmSvc.parseInputNotify(adminTask.getInputNotifyConf(), updatedEvalUser, opEmpId);
            cycleEvalDmSvc.batchSendFinishValueChanged(new Name(adminTask.getTaskName()), updatedEvalUser, adminTask.inputChangedRecEmpIds(), emp.getName(), opEmpId);
        }
        //如果在执行环节，判断是否进入下一阶段
        if (Objects.equals(updatedEvalUser.getTaskStatus(), TalentStatus.CONFIRMED.getStatus())) {
            new KpiItemUpdateFinishedValueEvent(companyId, updatedEvalUser, new EmpId(opEmpId), 0).publish();
        }
        return updatedEvalUser.getInputFinishStatus();
    }

    /**
     * 编辑经营指标目标值
     *
     * @param companyId
     * @param okrGoalId
     * @param targetValue
     */
    public void updatePlanItemTargetValue(String companyId, String opEmpId, String okrGoalId, String targetValue) {
        if (StrUtil.isNotBlank(targetValue) && !StringTool.isNumber(targetValue)) {
            throw new RuntimeException("Wrong format of parameter targetValue");
        }
        if (StrUtil.isBlank(targetValue)) {
            throw new RuntimeException("The required parameter is empty[targetValue]");
        }
        List<EvalKpi> items = taskKpiItemDao.listScoringPlanItemByGoalId(companyId, okrGoalId);

        if (CollUtil.isEmpty(items)) {
            return;
        }
        List<PerfModificationRecord> recordList = new ArrayList<>();
        if (StrUtil.isNotBlank(targetValue)) {
            taskKpiRepo.updatePlanItemTargetValue(CollUtil.map(items, kpi -> kpi.getId(), true), opEmpId, new BigDecimal(targetValue));
            log.info("经营计划指标目标值变更成功");
            //变更操作日志
            for (EvalKpi kpi : items) {
                List list = new ArrayList();
                Map<String, Object> eventMap = new HashMap<>();
                eventMap.put("changeType", "okrPlanTargetValChange");
                eventMap.put("currentVal", targetValue);
                eventMap.put("originVal", kpi.getItemTargetValue());
                list.add(eventMap);
                ModificationValue modificationValue = new ModificationValue("modifyIndex", kpi.getKpiItemName(), kpi.getItemUnit(), list);
                JSONArray jsonArray = new JSONArray();
                jsonArray.add(JSONObject.toJSON(modificationValue));
                log.info("经营计划指标目标值变更日志jsonArray={}", jsonArray.toString());
                String businessScene = kpi.getTaskStatus().equals(TalentStatus.CONFIRMING) ? "confirm_item" : OperationLogSceneEnum.CHANGE_ITEM.getScene();
                PerfModificationRecord record = new PerfModificationRecord();
                record.accOp(new TenantId(companyId), new EmpId(opEmpId), kpi.getTaskUserId(), businessScene, BusinessConstant.PASS, jsonArray.toString());
                recordList.add(record);
            }
            if (CollUtil.isNotEmpty(recordList)) {
                userRepo.batchSaveModification(recordList);
            }
        }

    }

    public void fefreshFormulaField(String companyId, String taskUserId, StdTemp stdTemp) {
        EmpEvalMerge empEvalMerge = empRuleRepo.getEmpEvalMerge(new TenantId(companyId), taskUserId, null, all, 1);
        empEvalMerge.convertFormulaField(stdTemp);
        empRuleRepo.addFormulaFields(new ToDataBuilder<>(empEvalMerge, EmpEvalRule.class).data());
    }


    public List<EvalKpi> listRejectKpiItem(String tenantId, String empEvalId, Integer finalSubmitFinishValue) {
        return kpiDao.listRejectKpiItem(tenantId, empEvalId, finalSubmitFinishValue);
    }

    public void rejectFinishedValueReAuditStart(String companyId, EmpId opEmpId, EvalUser empEval, List<EvalKpi> rejectEvalKpis) {
        EmpEvalMerge empEvalMerge = empRuleRepo.getEmpEvalMerge(new TenantId(companyId), empEval.getId(), beforeScore);

        empEval.setTaskStatus(TalentStatus.FINISH_VALUE_AUDIT.getStatus());
        // 查找驳回指标一级审核人，并发送待办
        List<String> kpiItemIds = rejectEvalKpis.stream().map(EvalKpi::getKpiItemId).collect(Collectors.toList());

        //加载审核流
        LevelAuditFlow flow = userRepo.reloadKpiItemsAuditFlow(new TenantId(companyId), empEval.getId(), EvaluateAuditSceneEnum.FINISH_VALUE_AUDIT, kpiItemIds);
        flow.dispatchFirst();
        userRepo.updateLevelFlow(flow.curAudits(), null, flow.curLevelRs());
        // 更新负责人信息
        List<KpiEmp> reviewers = kpiEmpDao.listByEmp(empEval.getCompanyId(), flow.curLevelEmpIds());
        empEval.reviewers(reviewers);
        userRepo.updateTaskUser(empEval);
        // 外部数据锁定
        new ExtDataLocked(empEval, companyId).fire();
        //通知okr锁定
        new EvalEmpOkrLockEdited(new TenantId(companyId), Collections.singletonList(empEval.getId()), opEmpId, "updateLock").publish();
        //已发送的Audit更新dispatch
        new MsgTodoAggregate(new TenantId(companyId), empEval.getTaskId(), new Name(empEvalMerge.getTaskName()), empEval.getEmpId(), empEval.getId())
                .useScene(MsgSceneEnum.FINISH_VALUE_AUDIT, null)
                .addExtTempValue("evalEmpName", empEval.getEvalOrgName() == null ? empEval.getEmpName() : empEval.getEvalOrgName())
                .addExtTempValue("deadLineDate", empEval.joinDeadLineStr(TalentStatus.FINISH_VALUE_AUDIT.getStatus()))
                .addTodoItem("msg.task.emp", empEval.getEvalOrgName() == null ? empEval.getEmpName() : empEval.getEvalOrgName())
                .sendExtMsg().addCenterMsg().sendExtTodo()
                .addRecEmpId(flow.curLevelEmpIds()).publish();
    }

    /**
     * 驳回完成值
     *
     * @param cmd
     */
    @Transactional
    public void rejectFinishedValue(RejectFinishedValueCmd cmd) {
        if (CollUtil.isEmpty(cmd.getAuditItemIds())) {
            return;
        }
        EvalUser taskUser = userRepo.getBaseTaskUser(cmd.getTenantId(), cmd.getTaskUserId());
        PerfEvaluateTaskBaseDo taskBase = adminTaskDao.getByTaskId(cmd.getTenantId().getId(), taskUser.getTaskId());
        List<EvalKpi> evalKpis = kpiDao.listKpiItemByIds(cmd.getAuditItemIds());
        RejectFinishedValueDmSvc dmSvc = new RejectFinishedValueDmSvc(cmd.getTenantId(), cmd.getOpEmpId(), taskBase.getTaskName(),
                taskBase.getId(), cmd.getAuditItemIds(), taskUser, evalKpis, cmd.getAuditReason());
        dmSvc.setRepo(auditRepo, taskKpiRepo);
        dmSvc.doReject();//执行驳回
        // 查询所有驳回指标，且最终提交状态为0的指标
        List<EvalKpi> rejectEvalKpis = this.listRejectKpiItem(cmd.getTenantId().getId(), cmd.getTaskUserId(), 0);
        dmSvc.buildEmpRejectItems(rejectEvalKpis);
        // 更新任务状态为执行中 + 更新负责人信息
        List<KpiEmp> reviewers = kpiEmpDao.listByEmp(cmd.getTenantId(), dmSvc.getInputEmps());
        taskUser.reviewers(reviewers);//完成值驳回，考核环节还是完成值审核中
        taskUser.setFinishValueAuditStatus(FinishValueAuditStatusEnum.REJECT.getType());
        userRepo.updateTaskUser(taskUser);
        // 查考核任务名称
        dmSvc.setTaskName(taskBase.getTaskName());
        dmSvc.getOwnerEmpIds().addAll(dmSvc.getCurrentInputEmps());
        dmSvc.getOwnerEmpIds().add(cmd.getOpEmpId().getId());//本人的完成值审核待办需要取消
        dmSvc.setScenes(Arrays.asList(MsgSceneEnum.FINISH_VALUE_AUDIT.getType()));
        finishValueMessageSender.notifyRejectFinishValueAudit(dmSvc);//发送驳回完成值审核通知
        finishValueMessageSender.cancelFinishValueAuditTodo(dmSvc);//撤销完成值审核待办
    }

    public PagedList<EmpEvalByStatusPo> pagedEvalPreview(EmpEvalAtTaskQuery2 query) {
        TenantId tenantId = new TenantId(query.getCompanyId());
        query.setAdminOrgIds(kpiOrgDao.listAdminScopePrivOrgIds(query.getCompanyId(), query.getOpEmpId(), null));  //获取管理权限范围
        if (CollUtil.isNotEmpty(query.getOrgIds())) {
            query.setOrgIds(kpiOrgDao.listAllChildOrgIds(new TenantId(query.getCompanyId()), query.getOrgIds()));
        }
        //考核任务权限
        if (query.isPriv()) {
            query.setTaskIds(cycleEvalDao.listTaskPrivByOpEmpId(query.getCompanyId(), query.getCycleId(), query.getOpEmpId()));
        }
        PagedList<EmpEvalByStatusPo> statusPos = empEvalDao.pagedEvalPreview(query);
        if (CollUtil.isNotEmpty(statusPos)) {
            log.info("11.getEditExeIndi = {}", JSONUtil.toJsonStr(statusPos.get(0).getEditExeIndi()));
        }
        List<String> taskUserIds = CollUtil.map(statusPos, po -> po.getId(), true);
        Set<String> empIds = statusPos.stream().map(EmpEvalByStatusPo::getEmpId).collect(Collectors.toSet());
        Set<String> inputEmpIds = statusPos.stream().flatMap(po -> po.getInputFinishValueEmpIds().stream()).collect(Collectors.toSet());
        Map<String, KpiEmp> leavedEmps = kpiEmpDao.listLeavedEmp(tenantId, empIds);
        ListWrap<KpiEmp> inputEmps = kpiDao.listByEmp(tenantId, inputEmpIds);
        Map<String, TaskAppealPo> poMap = taskAppealDao.listAppealRsByTaskId(taskUserIds, new TenantId(query.getCompanyId()));
        List<String> curOrgIds = new ArrayList<>();
        for (EmpEvalByStatusPo po : statusPos) {
            po.setLeaved(leavedEmps.containsKey(po.getEmpId()));
            if (CollUtil.isNotEmpty(inputEmpIds)) {
                po.getOpStages().add("inputFinishValue");
            }
            TaskAppealPo appealPo = poMap.get(po.getTaskUserId());
            if (Objects.nonNull(appealPo)) {
                po.setAppealStatus(appealPo.getAppealStatus());
            }
            po.scoreNodeSort();
            po.matchInputEmpName(inputEmps);
            if (StrUtil.isNotBlank(po.matchOrId())) {
                curOrgIds.add(po.matchOrId());
            }
        }
        if (CollUtil.isNotEmpty(statusPos)) {
            log.info("22.getEditExeIndi = {}", JSONUtil.toJsonStr(statusPos.get(0).getEditExeIndi()));
        }
        //显示上级
        if (CollUtil.isNotEmpty(curOrgIds)) {
            List<KpiOrgSupNames> supNames = kpiOrgDao.listOrgSupNames(new TenantId(query.getCompanyId()), curOrgIds);
            statusPos.forEach(r -> r.matchSupNames(supNames));
        }
        return statusPos;
    }

    public PagedList<EmpEvalChangeResult> pagedEvalChange(EmpEvalAtTaskQuery2 query) {
        TenantId tenantId = new TenantId(query.getCompanyId());
        query.setAdminOrgIds(kpiOrgDao.listAdminScopePrivOrgIds(query.getCompanyId(), query.getOpEmpId(), null));  //获取管理权限范围
        if (CollUtil.isNotEmpty(query.getOrgIds())) {
            query.setOrgIds(kpiOrgDao.listAllChildOrgIds(new TenantId(query.getCompanyId()), query.getOrgIds()));
        }
        //考核任务权限
        if (query.isPriv()) {
            query.setTaskIds(cycleEvalDao.listTaskPrivByOpEmpId(query.getCompanyId(), query.getCycleId(), query.getOpEmpId()));
        }
        PagedList<EmpEvalChangeResult> statusPos = empEvalDao.pagedEvalChange(query);
        Set<String> inputEmpIds = statusPos.stream().flatMap(po -> po.getInputFinishValueEmpIds().stream()).collect(Collectors.toSet());
        ListWrap<KpiEmp> inputEmps = kpiDao.listByEmp(tenantId, inputEmpIds);
        List<String> curOrgIds = new ArrayList<>();
        for (EmpEvalChangeResult po : statusPos) {
            po.matchInputEmpName(inputEmps);
            if (StrUtil.isNotBlank(po.matchOrId())) {
                curOrgIds.add(po.matchOrId());
            }
        }
        //显示上级
        if (CollUtil.isNotEmpty(curOrgIds)) {
            List<KpiOrgSupNames> supNames = kpiOrgDao.listOrgSupNames(new TenantId(query.getCompanyId()), curOrgIds);
            statusPos.forEach(r -> r.matchSupNames(supNames));
        }
        return statusPos;
    }

    @Transactional
    public String createEvalChange(String companyId, String opEmpId, String taskId) {
        return userRepo.saveChangeBatch(companyId, opEmpId, taskId);
    }

    @Async(ExecutorEnum.low)
    public void createEvalChangeResult(String logId, EmpEvalAtTaskQuery2 query, String batchId) {
        long methodStartTime = System.currentTimeMillis();
        MDC.put("tid", logId + "-createEvalChangeResult");
        log.info("[PERF-MONITOR] 开始执行createEvalChangeResult方法，batchId={}, companyId={}", batchId, query.getCompanyId());

        query.setPageNo(1);
        query.setPageSize(9999);
        List<EmpEvalByStatusPo> rs = this.pagedEvalPreview(query).getData();
        if (CollUtil.isEmpty(rs)) {
            return;
        }
        List<EmpEvalChangeResult> rsPos = rs.stream().map(statusPo -> {
            EmpEvalChangeResult rsPo = Convert.convert(EmpEvalChangeResult.class, statusPo);
            return rsPo;
        }).collect(Collectors.toList());
        //已完成的数据不显示直接排除，不显示在该列表
        rsPos = CollUtil.filterNew(rsPos, po -> !po.isFilter());
        if (CollUtil.isEmpty(rsPos)) {
            userRepo.upChangeBatchStatus(query.getCompanyId(), batchId, "wait");
            MDC.clear();
            return;
        }
        ListWrap<EmpRefOrg> refOrgWrap = kpiOrgDao.listAtOrgWrap(new TenantId(query.getCompanyId()), CollUtil.map(rsPos, po -> po.getEmpId(), true).stream().distinct().collect(Collectors.toList()));
        List<Employee> emps = kpiEmpDao.listEmps(query.getCompanyId(), "on_the_job");
        EvalChangeDmSvc changeDmSvc = new EvalChangeDmSvc(query.getCompanyId(), query.getOpEmpId(), refOrgWrap, emps, rsPos, batchId);
        changeDmSvc.bdEmps();
        changeDmSvc.load();
        //过滤部门有异动的数据
        List<String> changeOrgEmpIds = changeDmSvc.listChangeOrgEmpIds();
        ListWrap<EvalUser> userListWrap = taskUserDao.listByEmpEvalUser(new TenantId(query.getCompanyId()), changeOrgEmpIds);
        changeDmSvc.lockOrg(userListWrap);
        if (CollUtil.isNotEmpty(changeDmSvc.getChangeResults())) {
            userRepo.batchAddChangeResult(query.getCompanyId(), query.getOpEmpId(), changeDmSvc.getChangeResults());
        }
        userRepo.upChangeBatchStatus(query.getCompanyId(), batchId, "wait", changeDmSvc.getChangeResults().size());

        long totalTime = System.currentTimeMillis() - methodStartTime;
        log.info("[PERF-MONITOR] createEvalChangeResult方法执行完成，总耗时={}ms, batchId={}", totalTime, batchId);

        MDC.clear();

    }

    @Async(ExecutorEnum.low)
    public void createEvalChangeResultV2(String logId, EmpEvalAtTaskQuery2 query,String batchId) {
        long methodStartTime = System.currentTimeMillis();

        MDC.put("tid", logId + "-createEvalChangeResultV2");
        log.info("[PERF-MONITOR] 开始执行createEvalChangeResultV2方法，batchId={}, companyId={}", batchId, query.getCompanyId());

        Map<String, List<Rater>> raterCacheMap = new HashMap<>();
        Map<String, SimpleEmpInfo> empMap = new HashMap<>();
        Map<String, Rater> dingManagerMap = new HashMap<>();

        List<EmpEvalChangeResult> totalRs = new ArrayList<>();
        List<EmpEvalChangeResult> rs;
        int pageNo = 1;
        int totalProcessedCount = 0;

        long dataProcessStartTime = System.currentTimeMillis();

        while (!(rs = this.listEmpEvalChangeResultBaseInfo(query, pageNo)).isEmpty()) {
            long pageStartTime = System.currentTimeMillis();
            pageNo++;
            totalProcessedCount += rs.size();

            log.debug("[PERF-MONITOR] 处理第{}页数据，当前页记录数={}, 累计处理记录数={}", pageNo - 1, rs.size(), totalProcessedCount);

            long relationBuildStartTime = System.currentTimeMillis();
            List<String> empIds = rs.stream().map(EmpEvalChangeResult::getEmpId).distinct().collect(Collectors.toList());
            ListWrap<EmpRefOrg> refOrgWrap = kpiOrgDao.listAtOrgWrap(new TenantId(query.getCompanyId()), empIds);
            long relationBuildTime = System.currentTimeMillis() - relationBuildStartTime;

            log.debug("[PERF-MONITOR] 构建员工关系映射耗时={}ms", relationBuildTime);

            long changeProcessStartTime = System.currentTimeMillis();
            EvalChangeDmSvcV2 changeDmSvcV2 = new EvalChangeDmSvcV2(query.getCompanyId(), query.getOpEmpId(), query.getOpAdminType(), batchId, rs, refOrgWrap, raterCacheMap, empMap, dingManagerMap, kpiEmpRepo);
            changeDmSvcV2.loadChange();
            long changeProcessTime = System.currentTimeMillis() - changeProcessStartTime;

            log.debug("[PERF-MONITOR] 变更处理耗时={}ms", changeProcessTime);

            //部门异动锁定
            long lockStartTime = System.currentTimeMillis();
            List<String> changeOrgEmpIds = changeDmSvcV2.listChangeOrgEmpIds();
            ListWrap<EvalUser> userListWrap = taskUserDao.listByEmpEvalUser(new TenantId(query.getCompanyId()), changeOrgEmpIds);
            changeDmSvcV2.lockOrg(userListWrap);
            long lockTime = System.currentTimeMillis() - lockStartTime;

            log.debug("[PERF-MONITOR] 部门异动锁定耗时={}ms", lockTime);

            totalRs.addAll(changeDmSvcV2.getChangeResults());

            long pageTime = System.currentTimeMillis() - pageStartTime;
            log.debug("[PERF-MONITOR] 第{}页处理完成，耗时={}ms", pageNo - 1, pageTime);
        }
        long dataProcessTime = System.currentTimeMillis() - dataProcessStartTime;
        log.info("[PERF-MONITOR] 数据处理阶段完成，总处理记录数={}, 耗时={}ms", totalProcessedCount, dataProcessTime);

        long saveStartTime = System.currentTimeMillis();
        if (CollUtil.isNotEmpty(totalRs)) {
            userRepo.batchAddChangeResult(query.getCompanyId(), query.getOpEmpId(), totalRs);
            log.info("[PERF-MONITOR] 批量保存变更结果完成，保存记录数={}, 耗时={}ms", totalRs.size(), System.currentTimeMillis() - saveStartTime);
        } else {
            log.info("[PERF-MONITOR] 无变更结果需要保存");
        }

        long statusUpdateStartTime = System.currentTimeMillis();
        userRepo.upChangeBatchStatus(query.getCompanyId(),batchId,"wait");
        long statusUpdateTime = System.currentTimeMillis() - statusUpdateStartTime;
        log.debug("[PERF-MONITOR] 更新批次状态耗时={}ms", statusUpdateTime);

        long totalTime = System.currentTimeMillis() - methodStartTime;
        log.info("[PERF-MONITOR] createEvalChangeResultV2方法执行完成，总耗时={}ms, batchId={}", totalTime, batchId);

        MDC.clear();
    }

    private List<EmpEvalChangeResult> listEmpEvalChangeResultBaseInfo(EmpEvalAtTaskQuery2 query, int pageNo) {

        TenantId tenantId = new TenantId(query.getCompanyId());
        query.setAdminOrgIds(kpiOrgDao.listAdminScopePrivOrgIds(query.getCompanyId(), query.getOpEmpId(), null));  //获取管理权限范围
        if (CollUtil.isNotEmpty(query.getOrgIds())) {
            query.setOrgIds(kpiOrgDao.listAllChildOrgIds(new TenantId(query.getCompanyId()), query.getOrgIds()));
        }
        //考核任务权限
        if (query.isPriv()) {
            query.setTaskIds(cycleEvalDao.listTaskPrivByOpEmpId(query.getCompanyId(), query.getCycleId(), query.getOpEmpId()));
        }
        List<EmpEvalByStatusPo> statusPos = empEvalDao.listEvalPreview(query,pageNo);
        List<String> taskUserIds = CollUtil.map(statusPos, po -> po.getId(), true);
        Set<String> empIds = statusPos.stream().map(EmpEvalByStatusPo::getEmpId).collect(Collectors.toSet());
        Set<String> inputEmpIds = statusPos.stream().flatMap(po -> po.getInputFinishValueEmpIds().stream()).collect(Collectors.toSet());
        Map<String, KpiEmp> leavedEmps = kpiEmpDao.listLeavedEmp(tenantId, empIds);
        ListWrap<KpiEmp> inputEmps = kpiDao.listByEmp(tenantId, inputEmpIds);
        Map<String, TaskAppealPo> poMap = taskAppealDao.listAppealRsByTaskId(taskUserIds, new TenantId(query.getCompanyId()));
        List<String> curOrgIds = new ArrayList<>();
        for (EmpEvalByStatusPo po : statusPos) {
            po.setLeaved(leavedEmps.containsKey(po.getEmpId()));
            if (CollUtil.isNotEmpty(inputEmpIds)) {
                po.getOpStages().add("inputFinishValue");
            }
            TaskAppealPo appealPo = poMap.get(po.getTaskUserId());
            if (Objects.nonNull(appealPo)) {
                po.setAppealStatus(appealPo.getAppealStatus());
            }
            po.scoreNodeSort();
            po.matchInputEmpName(inputEmps);
            if (StrUtil.isNotBlank(po.matchOrId())) {
                curOrgIds.add(po.matchOrId());
            }
        }
        //显示上级
        if (CollUtil.isNotEmpty(curOrgIds)) {
            List<KpiOrgSupNames> supNames = kpiOrgDao.listOrgSupNames(new TenantId(query.getCompanyId()), curOrgIds);
            statusPos.forEach(r -> r.matchSupNames(supNames));
        }

        List<EmpEvalChangeResult> rsPos = statusPos.stream().filter(po -> !po.isFilter()).map(statusPo -> {
            EmpEvalChangeResult rsPo = Convert.convert(EmpEvalChangeResult.class, statusPo);
            return rsPo;
        }).collect(Collectors.toList());

        return rsPos;
    }


    public void orgChangeRefreshResult(EmpEvalChangeResultCmd cmd) {
        if (Objects.isNull(cmd)) {
            return;
        }
        List<EmpEvalChangeResult> rsPos = new ArrayList<>();
        rsPos.add(new ToDataBuilder<>(cmd, EmpEvalChangeResult.class).data());
        List<Employee> emps = kpiEmpDao.listEmps(cmd.getCompanyId(), "on_the_job");
        EvalChangeDmSvc changeDmSvc = new EvalChangeDmSvc(cmd.getCompanyId(), cmd.getOpEmpId(), cmd.getOpAdminType(), rsPos, emps);
        changeDmSvc.bdEmps();
        changeDmSvc.refresh();
        userRepo.updateChangeResult(changeDmSvc.getChangeResults().get(0));
    }

    public void orgChangeRefreshResultV2(EmpEvalChangeResultCmd cmd) {

        if (Objects.isNull(cmd)) {
            return;
        }

        Map<String, List<Rater>> raterCacheMap = new HashMap<>();
        Map<String, SimpleEmpInfo> empMap = new HashMap<>();
        Map<String, Rater> dingManagerMap = new HashMap<>();

        List<EmpEvalChangeResult> rsPos = new ArrayList<>();
        rsPos.add(new ToDataBuilder<>(cmd, EmpEvalChangeResult.class).data());
        for (EmpEvalChangeResult result : rsPos) {
            result.clearChange();
            result.closeTypeRater();
        }
        EvalChangeDmSvcV2 changeDmSvcV2 = new EvalChangeDmSvcV2(cmd.getCompanyId(), cmd.getOpEmpId(), cmd.getOpAdminType(), cmd.getBatchId(), rsPos, null, raterCacheMap, empMap, dingManagerMap, kpiEmpRepo);
        changeDmSvcV2.loadChange();
        userRepo.updateChangeResult(changeDmSvcV2.getChangeResults().get(0));
    }

    //异动考核人员规则信息
    public void changeEmpEval(ChangeEvalCmd cmd) {
        if (Objects.isNull(cmd)) {
            return;
        }
        List<EmpEvalChangeResult> changeResults = empEvalDao.listEvalChange(cmd);
        if (CollUtil.isEmpty(changeResults)) {
            return;
        }
        tx.runTran(() -> userRepo.upChangeBatchStatus(cmd.getCompanyId(), cmd.getBatchId(), "changed"));  //保存变更结果
        List<String> taskUserIds = CollUtil.map(changeResults, change -> change.getTaskUserId(), true);
        List<String> atOrgIds = CollUtil.map(changeResults, change -> change.getOrgId(), true);
        List<EmpEvalRule> empEvalRules = empEvalDao.listBaseEvalRule(cmd.getCompanyId(), taskUserIds);
        ListWrap<EmpEvalRule> empEvalMap = new ListWrap<>(empEvalRules).asMap(empEvalRule -> empEvalRule.getEmpEvalId());
        List<EvalUser> users = taskUserDao.listBaseEvalUser(new TenantId(cmd.getCompanyId()), taskUserIds);
        ListWrap<EmpEvalKpiType> kpiTypeListWrap = empEvalDao.loadKpiTypesWrap(cmd.getCompanyId(), taskUserIds);
        List<EmpOrganization> paths = deptEmpDao.listDeptWithNamePath(new TenantId(cmd.getCompanyId()), atOrgIds);
        ListWrap<EmpOrganization> pathMap = new ListWrap<>(paths).asMap(path -> path.getOrgId());
        EvalChangeDmSvc changeDmSvc = new EvalChangeDmSvc(cmd.getCompanyId(), cmd.getOpEmpId(), cmd.getOpAdminType(), changeResults);
        changeDmSvc.changed(users, empEvalMap, cmd.getChangeOrgFlag(), cmd.getChangeStage(), kpiTypeListWrap, pathMap);

        String tid = MDC.get("tid");
        CompletableFuture.runAsync(() -> {
            MDC.put("tid", tid + "-changeEmpEval(异动规则设置)");
            tx.runTran(tid, () -> self.saveChangeEmpEvalResult(changeDmSvc, cmd.getBatchId()));
            MDC.clear();
        }).thenRunAsync(() -> {
            MDC.put("tid", tid + "-changeEmpEval(刷新校准汇总)");
            tx.runTran(tid, () -> self.batchRefreshResultAuditFlow(changeDmSvc));
            MDC.clear();
        });
    }


    //保存异动后的结果。
    public void saveChangeEmpEvalResult(EvalChangeDmSvc changeDmSvc, String batchId) {
        empRuleRepo.applyChangeEval(changeDmSvc.getChangedEvalUsers());
        Integer totalCnt = calcCount(changeDmSvc.getChangedEvalUsers(),changeDmSvc.getVacancyErrors());
        userRepo.upChangeBatch(changeDmSvc.getCompanyId(), batchId, "finish", changeDmSvc.getChangedEvalUsers().size(), changeDmSvc.getVacancyErrors(), totalCnt);
        self.batchSaveEmpEvalOperation(changeDmSvc);
        //变更部门后
        new EvalOrgChangedEvent(changeDmSvc.getCompanyId(), changeDmSvc.getChangeOrgEvalUsers()).publish();
    }

    private Integer calcCount(List<EvalUser> changedEvalUsers, List<VacancyError> vacancyErrors) {
        Set<String> taskUserIds = new HashSet<>();
        for (EvalUser evalUser : changedEvalUsers) {
            taskUserIds.add(evalUser.getId());
        }
        for (VacancyError vacancyError : vacancyErrors) {
            taskUserIds.add(vacancyError.getTaskUserId());
        }
        return taskUserIds.size();
    }

    //刷新汇总
    public void batchRefreshResultAuditFlow(EvalChangeDmSvc changeDmSvc) {
        String companyId = changeDmSvc.getCompanyId();
        String opEmpId = changeDmSvc.getOpEmpId();
        List<EvalUser> evalUsers = changeDmSvc.getChangedEvalUsers();
        if (CollUtil.isEmpty(evalUsers)) {
            return;
        }
        List<EvalUser> filterUsers = new ArrayList<>();
        for (EvalUser evalUser : evalUsers) {
            if (!evalUser.isAuditResultCollectSend() || TalentStatus.statusOf(evalUser.getTaskStatus()).afterEq(TalentStatus.RESULTS_AUDITING)) {
                continue;
            }
            filterUsers.add(evalUser);
        }
        if (CollUtil.isEmpty(filterUsers)) {
            return;
        }
        auditFlowRepo.refreshAuditFlow(companyId, opEmpId, filterUsers);
        ResultAuditDmSvc dmSvc = new ResultAuditDmSvc(null, null, companyId, null);
        dmSvc.setRepo(auditFlowRepo);
        for (String taskId : CollUtil.map(evalUsers, eval -> eval.getTaskId(), true)) {
            dmSvc.refreshSummary(taskId);
            if (dmSvc.needSend()) {
                AdminTask adminTask = adminTaskRepo.getAdminTask(new TenantId(companyId), taskId);
                new ResultCollectMsgTodoEvent(companyId, dmSvc.getSendRater(), adminTask).publish();
            }
        }
    }

    public EvalTaskChangeBatch findEvalTaskChangeBatchInfo(String companyId, String taskId) {
        return empEvalDao.findEvalTaskChangeBatchInfo(companyId, taskId);
    }

    public void upChangeBatchStatus(String companyId, String batchId, String status) {
        userRepo.upChangeBatchStatus(companyId, batchId, status);
    }

    public List<EmpEvalSkipRatersPo> querySkipRaters(String taskUserId, String companyId) {
        List<PerfEvaluateTaskScoreResultDo> res = this.empEvalDao.listSkipRaters(taskUserId, companyId);
        List<PerfEvaluateTaskScoreResultDo> ratersGroupItem = this.empEvalDao.listSkipRatersGroupItem(taskUserId, companyId);
        List<PerfEvaluateTaskKpiPo> kpiItems = empEvalDao.listKpiItems(taskUserId, companyId);
        Map<String, String> kpiItemMap = new HashMap<>();
        if (CollUtil.isNotEmpty(kpiItems)) {
            kpiItemMap = kpiItems.stream().collect(Collectors.toMap(PerfEvaluateTaskKpiPo::getKpiItemId, PerfEvaluateTaskKpiPo::getKpiItemName));
        }
        Map<String, List<String>> ratersGroupItemMap = new HashMap<>();
        if (CollUtil.isNotEmpty(ratersGroupItem)) {
            for (PerfEvaluateTaskScoreResultDo d : ratersGroupItem) {
                ratersGroupItemMap.put(d.getKpiItemId(), Arrays.asList(d.getScorerId().split(",")));
            }
        }
        List<EmpEvalSkipRatersPo> skipRatersPos = new ArrayList<>();
        EmpEvalSkipRatersPo po;
        if (CollUtil.isNotEmpty(res)) {
            List<String> scoreIds = res.stream().map(s -> s.getScorerId()).collect(Collectors.toList());
            List<Employee> employees = empDao.listAllByEmpIds(companyId, scoreIds, null);
            Map<String, Employee> employeesMap = employees.stream().collect(Collectors.toMap(Employee::getEmployeeId, o -> o, (k1, k2) -> k1));
            for (PerfEvaluateTaskScoreResultDo o : res) {
                Employee employee = employeesMap.get(o.getScorerId());
                if (ObjectUtils.isNotEmpty(employee)) {
                    String kpiItemId = o.getKpiItemId();
                    po = new EmpEvalSkipRatersPo();
                    po.setRaterName(employeesMap.get(o.getScorerId()).getName());
                    po.setScoreType(o.getScorerType());
                    po.setAvatar(employeesMap.get(o.getScorerId()).getAvatar());
                    po.setSkipUserId(o.getScorerId());
                    List<String> scoreIdList = ratersGroupItemMap.get(kpiItemId);
                    if (CollUtil.isNotEmpty(scoreIdList) && scoreIdList.contains(o.getScorerId()) && scoreIdList.size() == 1) {
                        po.setKpiItemName(kpiItemMap.get(o.getKpiItemId()));
                    }
                    skipRatersPos.add(po);
                }
            }
        }
        return skipRatersPos;
    }


    public void lockExtData(ExtDataLocked event) {
        EvalUser taskUser = event.getTaskUser();
        String companyId = event.getCompanyId();
        String taskId = taskUser.getTaskId();
        PerfEvaluateTaskBaseDo taskBase = adminTaskDao.findTaskBase(new TenantId(companyId), taskId);
        List<EvalKpi> updateKpis = new ArrayList<>();
        for (EvalKpi kpi : taskUser.getKpis()) {
            List<ExtDataItemFieldCorr> itemFieldCorr = kpi.getItemFieldCorr();
            if (CollUtil.isNotEmpty(itemFieldCorr)) {
                for (ExtDataItemFieldCorr corr : itemFieldCorr) {
                    ExtDataSyncPo extData = syncDao.getExtData(companyId, corr.getExtDataFieldId(), taskUser.getEmpId(),
                            taskBase.getCycleStartDate(), taskBase.getCycleEndDate());
                    if (ObjectUtil.isNotNull(extData)) {
                        JSONObject jsonObject = JSONObject.parseObject(extData.getExtData(), JSONObject.class);
                        jsonObject.getString(corr.getExtDataFieldParamEnName());
                        if (StrUtil.equals("finishValue", corr.getKpiItemFieldId())) {
                            String value = jsonObject.getString(corr.getExtDataFieldParamEnName());
                            if (StringTool.isNumber(value)) {
                                kpi.setItemFinishValue(new BigDecimal(value));
                            } else {
                                kpi.setItemFinishValueText(value);
                            }
                        }
                    }
                }
                updateKpis.add(kpi);
            }
        }
        if (CollUtil.isNotEmpty(updateKpis)) {
            taskKpiRepo.extDataLock(updateKpis, companyId, taskId);
        }
    }

    public List<EmpEvalSkipRatersV3Po> queryScoreSkipRatersV3(String taskUserId, String companyId) {
        return this.evalScorerNodeKpiItemDao.listScorerNodeSkipRaters(taskUserId, companyId);
    }

    @Transactional
    public void startSendScoreSummaryMsgTodo(ScoreSummaryMsgTodoEvent event) {

        String tenantId = event.getTenantId();
        List<TaskUserScorer> scorers = event.getScorers();
        ScorerTodoSummary summary = event.getSummary();

        //根据汇总内容发钉钉待办和通知
        if (summary.getTodoStatus() == 0) {
            //进行锁定
            Boolean lock = scorerSummaryTodoRepo.lockSummary(summary);
            if (!lock) {
                return;
            }
            //成功则发待办
            String thirdMsgId = scoreMessageSender.scoreSummarySendTodo(summary, scorers);
            summary.setThirdMsgId(thirdMsgId);
            summary.setTodoStatus(1);
            scorerSummaryTodoRepo.addOrUpdateAfterSendRemoteTodo(summary);
        }

        //给所有没有系统待办的人发系统待办
        List<CompanyMsgCenter> msgCenters = scorers.stream().map(scorer -> {
            MsgSceneEnum toScene = SubScoreNodeEnum.todoScene(scorer.getScoreScene());
            CompanyMsgCenter center = new CompanyMsgCenter(new TenantId(tenantId), toScene,
                    scorer.getTaskUserId(), scorer.getScorerId());
            //获取URL
            MsgTodoAggregate msgTodoAggregate = new MsgTodoAggregate(new TenantId(scorer.getCompanyId()), scorer.getTaskId(), new Name(scorer.getTaskName()),
                    scorer.getEmpId(), scorer.getTaskUserId())
                    .useScene(toScene);
            msgTodoAggregate.build();
            String url = msgTodoAggregate.redirectUrl();
            center.attTask(url, scorer.getTaskId(), scorer.getTaskName(), null);
            return center;
        }).collect(Collectors.toList());
        companyMsgCenterRepo.saveBatch(msgCenters);
        //更新这部分发了系统待办的实例状态
        scorerSummaryTodoRepo.sendScorerCompanyMsg(scorers, summary);
    }

    public void handlerDownloadTargetValueExcel(TaskTargetQuery query, ByteArrayOutputStream outputStream) {

        query.setPageNo(1);
        query.setPageSize(4999);

        PagedList<ExportTargetValue> taskUsers = this.pagedImportTarget(query);
        List<ExportTargetValue> taskUsersData = taskUsers.getData();

        Map<String, Set<String>> itemAppendMap = new LinkedHashMap<>(); // 指标 -> 字段集合
        //表头指标单位取第一个，用一个map来保存
        Map<String, String> itemUnitMap = new HashMap<>();

        for (ExportTargetValue taskUser : taskUsersData) {
            for (EvalItem item : taskUser.getItems()) {
                String key = item.getKpiItemName() + System.lineSeparator() + "(指标ID)" + item.getKpiItemId();
                String itemAppendName;
                if (itemUnitMap.containsKey(key)) {
                    itemAppendName = itemUnitMap.get(key);
                } else {
                    itemAppendName = item.getKpiItemName() + "(" + item.getItemUnit() + ")" + System.lineSeparator() + "(指标ID)" + item.getKpiItemId();
                    itemUnitMap.put(key, itemAppendName);
                }
                Set<String> fieldAppends = itemAppendMap.computeIfAbsent(itemAppendName, k -> new LinkedHashSet<>());
                for (EvalItemField field : item.getFields()) {
                    fieldAppends.add(field.getFieldName() + System.lineSeparator() + "(字段ID)" + field.getFieldId());
                }
            }
        }

        ExportTargetValueDmSvc dmSvc = new ExportTargetValueDmSvc();
        dmSvc.accOp(query.getPerformanceType(), query.exportFileName(), taskUsersData, itemAppendMap);
        dmSvc.exportExcel(outputStream);
    }

    public OngoingAppealBatchPo getOngoingAppealBatch(String companyId, String taskUserId) {

        return taskAppealDao.getOngoingAppealBatch(companyId, taskUserId);
    }

    public AttachmentConf getAppealReqFieldConf(String companyId, String taskUserId) {

        EmpEvalRule empEvalRule = empRuleRepo.getEmpEvalRule(new TenantId(companyId), taskUserId);
        if (Objects.nonNull(empEvalRule)) {
            return empEvalRule.getAppealConf().getAttachmentConf();
        }
        return null;
    }

    public Rater getRaterWithTaskAdmin(String companyId, String taskUserId) {
        EvalUser evalUser = empEvalDao.getBaseUser(companyId, taskUserId);
        if (Objects.nonNull(evalUser)) {
            Rater rater = new Rater();
            rater.setEmpId(evalUser.getCreatedUser());
            KpiEmp emp = kpiEmpDao.findKpiEmp(new TenantId(companyId), evalUser.getCreatedUser());
            if (Objects.nonNull(emp)) {
                rater.setEmpName(emp.getEmpName());
                rater.setAvatar(emp.getAvatar());
                return rater;
            }
        }
        return null;
    }

    public OperationLogDo getLatestChangeItemLog(String companyId, String taskUserId) {
        return opLogDao.findLastLog(new TenantId(companyId), taskUserId, OperationLogSceneEnum.CHANGE_ITEM.getScene());
    }

    /**
     public void fixAppealHistoryData(String taskId, String beginDate, String endDate) {

     List<String> taskIds;
     if (StringUtils.isNotBlank(taskId)) {
     fixTaskBaseAppealConf(Collections.singletonList(taskId));
     } else {
     //分批查询开启了申诉的任务
     Integer pageNo = 1;
     Integer pageSize = 500;
     while (!(taskIds = taskDao.listTaskIdWithAppealOpen(pageNo, pageSize, beginDate, endDate)).isEmpty()) {
     pageNo++;
     fixTaskBaseAppealConf(taskIds);
     }
     }

     }

     private void fixOngoingAppealHistoryData(String taskId) {

     List<PerfEvaluateTaskAppealBatch> batches;

     //查询进行中的申诉
     Integer pageNo = 1;
     Integer pageSize = 200;
     while (!(batches = taskAppealDao.listHistoryOngoingAppealBatch(pageNo, pageSize, taskId)).isEmpty()) {
     List<String> batchIds = batches.stream().map(PerfEvaluateTaskAppealBatch::getId).collect(Collectors.toList());
     ListWrap<PerfEvaluateTaskAppealInfo> infos = taskAppealDao.listAppealInfoByBatchIds(batchIds);
     pageNo++;
     //遍历
     for (PerfEvaluateTaskAppealBatch batch : batches) {
     batch.historyInit();
     batch.setAppealInfos(infos.groupGet(batch.getId()));
     PerfEvaluateTaskAppealFlowNode node = new PerfEvaluateTaskAppealFlowNode();
     node.accHistoryBatch(batch);
     PerfEvaluateTaskAppealAuditor appealAuditor = new PerfEvaluateTaskAppealAuditor();
     appealAuditor.accHistoryBatch(batch);
     node.setOngoingAuditor(appealAuditor);
     batch.setOngoingAuditNode(node);
     }
     //批量更新
     taskAppealRepo.fixHistoryBatch(batches);
     }
     }

     public void fixTaskBaseAppealConf(List<String> taskIds) {

     for (String taskId : taskIds) {
     log.info("开始处理taskId:{}", taskId);
     AdminTask adminTask = adminTaskDao.getAdminTaskBaseWithTaskId(new TaskId(taskId));
     if (adminTask.isTmpTask()) {
     continue;
     }
     if (Objects.isNull(adminTask.getAppealConf())){
     continue;
     }
     if (Objects.nonNull(adminTask.getAppealConf().getNotifyConf()) && adminTask.getAppealConf().getMaxAppealNumber() == null){
     log.info("taskId:{}申诉配置已存在申诉通知配置，跳过", taskId);
     continue;
     }

     log.info("获取taskBase原申诉配置:{}", JSONUtil.toJsonStr(adminTask.getAppealConf()));
     AppealConf appealConf = taskBaseAppealConfTransfer(adminTask.getAppealConf());
     log.info("处理后的taskBase申诉配置:{}", JSONUtil.toJsonStr(appealConf));
     adminTask.setAppealConf(appealConf);
     adminTaskRepo.editAdminAppealConf(adminTask.getCompanyId(), taskId, appealConf);
     List<String> taskUserIds = taskUserDao.listTaskUserIdByTaskId(taskId);
     fixEvalRuleAppealConf(taskUserIds, adminTask.getCompanyId(), appealConf);
     }
     }

     public void fixEvalRuleAppealConf(List<String> taskUserIds, TenantId tenantId, AppealConf taskBaseAppealConf) {

     for (String taskUserId : taskUserIds) {
     log.info("开始处理taskUserId:{}", taskUserId);
     EvalUser evalUser = empEvalDao.getBaseUser(tenantId.getId(), taskUserId);
     if (evalUser.wasTempTask()) {
     //历史任务不支持修改
     continue;
     }
     EmpEvalRule rule = empEvalDao.getBaseEvalRule(tenantId, taskUserId);

     if (Objects.isNull(rule)){
     continue;
     }

     if (Objects.isNull(rule.getAppealConf())){
     continue;
     }

     if (!rule.getAppealConf().isOpen()){
     continue;
     }

     log.info("获取evalRule原申诉配置:{}", JSONUtil.toJsonStr(rule.getAppealConf()));
     if (Objects.nonNull(rule.getAppealConf().getAttachmentConf())){
     log.info("申诉配置已存在附件配置，请勿重复处理，跳过");
     continue;
     }

     if (CollUtil.isNotEmpty(rule.getAppealConf().getAppealReceiver())){
     taskBaseAppealConf.setAppealReceiver(rule.getAppealConf().getAppealReceiver());
     AppealConf appealConf = evalRuleAppealConfTransfer(taskBaseAppealConf, evalUser);
     log.info("处理后的evalRule申诉配置:{}", JSONUtil.toJsonStr(appealConf));
     rule.setAppealConf(appealConf);
     rule.setEditStatus(rule.getEditStatus() + 16);
     Integer confStatus = rule.reChekConfStatus();
     evalUser.setRuleConfStatus(confStatus);
     empRuleRepo.updateEmpEvalRule(rule);
     userRepo.updateTaskUser(evalUser);
     }
     }
     }


     private AppealConf taskBaseAppealConfTransfer(AppealConf appealConf) {

     //通知配置
     BaseAuditNode baseAuditNode = new BaseAuditNode();
     baseAuditNode.setApproverName("被考核人");
     baseAuditNode.setApproverInfo("");
     baseAuditNode.setApproverType("taskEmp");
     baseAuditNode.setRaters(new ArrayList<>());

     AppealResultNotifyConf notifyConf = new AppealResultNotifyConf();
     notifyConf.setOpen(1);
     notifyConf.setNotifyNodes(Collections.singletonList(baseAuditNode));
     appealConf.setNotifyConf(notifyConf);

     //申诉流程配置
     List<StaffConfItem> staffConfItems = appealConf.getAppealReceiver();
     if (CollUtil.isNotEmpty(staffConfItems)) {
     BaseAuditNode baseAuditNode1 = new BaseAuditNode();
     if (staffConfItems.get(0).isTaskAdminType()) {
     baseAuditNode1.setApprovalOrder(1);
     baseAuditNode1.setApproverName("考核任务发起人");
     baseAuditNode1.setApproverInfo("taskAdmin");
     baseAuditNode1.setApproverType("taskAdmin");
     baseAuditNode1.setRaters(new ArrayList<>());
     } else if (staffConfItems.get(0).isFixEmpType()) {
     baseAuditNode1.setApprovalOrder(1);
     baseAuditNode1.setApproverName(staffConfItems.get(0).getObjItems().get(0).getObjName());
     baseAuditNode1.setApproverInfo(staffConfItems.get(0).getObjItems().get(0).getObjId());
     baseAuditNode1.setApproverType("user");
     baseAuditNode1.setRaters(new ArrayList<>());
     }

     AppealFlowConf appealFlowConf = new AppealFlowConf();
     appealFlowConf.setAnyRejectThenExit(0);
     appealFlowConf.setAuditNodes(Collections.singletonList(baseAuditNode1));
     appealConf.setAppealFlowConf(appealFlowConf);
     }

     //附件必填配置
     AttachmentConf attachmentConf = new AttachmentConf();
     appealConf.setAttachmentConf(attachmentConf);

     //最大次数设置为null
     appealConf.setMaxAppealNumber(null);

     //完成转换
     return appealConf;
     }

     private AppealConf evalRuleAppealConfTransfer(AppealConf appealConf, EvalUser evalUser) {

     List<StaffConfItem> staffConfItems = appealConf.getAppealReceiver();
     BaseAuditNode baseAuditNode1 = new BaseAuditNode();
     if (CollUtil.isNotEmpty(staffConfItems)){
     if (staffConfItems.get(0).isTaskAdminType()) {
     baseAuditNode1.setApprovalOrder(1);
     baseAuditNode1.setApproverName("考核任务发起人");
     baseAuditNode1.setApproverInfo("taskAdmin");
     baseAuditNode1.setApproverType("taskAdmin");

     Rater rater = new Rater();
     rater.setEmpId(evalUser.getCreatedUser());
     rater.setEmpName(kpiEmpDao.getEmpName(evalUser.getCompanyId(), evalUser.getCreatedUser()));
     rater.setStatus("wait");
     rater.setType(0);
     baseAuditNode1.setRaters(Collections.singletonList(rater));
     } else if (staffConfItems.get(0).isFixEmpType()) {
     baseAuditNode1.setApprovalOrder(1);
     baseAuditNode1.setApproverName(staffConfItems.get(0).getObjItems().get(0).getObjName());
     baseAuditNode1.setApproverInfo(staffConfItems.get(0).getObjItems().get(0).getObjId());
     baseAuditNode1.setApproverType("user");

     Rater rater = new Rater();
     rater.setEmpId(staffConfItems.get(0).getObjItems().get(0).getObjId());
     rater.setEmpName(staffConfItems.get(0).getObjItems().get(0).getObjName());
     rater.setStatus("wait");
     rater.setType(0);
     baseAuditNode1.setRaters(Collections.singletonList(rater));
     }

     AppealFlowConf appealFlowConf = new AppealFlowConf();
     appealFlowConf.setAnyRejectThenExit(0);
     appealFlowConf.setAuditNodes(Collections.singletonList(baseAuditNode1));
     appealConf.setAppealFlowConf(appealFlowConf);
     }

     List<BaseAuditNode> notifyNodes = appealConf.getNotifyConf().getNotifyNodes();
     BaseAuditNode baseAuditNode = notifyNodes.get(0);
     Rater rater = new Rater();
     rater.setEmpId(evalUser.getEmpId());
     rater.setEmpName(evalUser.getEmpName());
     rater.setStatus("wait");
     rater.setType(0);
     baseAuditNode.setRaters(Collections.singletonList(rater));
     return appealConf;
     }

     public void fixAppealHistoryDataWithUpdateTime(String beginDate, String endDate) {

     List<String> taskIds;
     //分批查询开启了申诉的任务
     Integer pageNo = 1;
     Integer pageSize = 500;
     while (!(taskIds = taskDao.listTaskIdWithAppealOpenWithUpdateTime(pageNo, pageSize, beginDate, endDate)).isEmpty()) {
     pageNo++;
     fixTaskBaseAppealConfWithUpdateTime(taskIds,beginDate,endDate);
     }

     //处理已经发起的申诉
     fixOngoingAppealHistoryData(null);
     }

     private void fixTaskBaseAppealConfWithUpdateTime(List<String> taskIds, String beginDate, String endDate) {

     for (String taskId : taskIds) {
     log.info("开始处理taskId:{}", taskId);
     AdminTask adminTask = adminTaskDao.getAdminTaskBaseWithTaskId(new TaskId(taskId));
     if (adminTask.isTmpTask()) {
     continue;
     }
     if (Objects.isNull(adminTask.getAppealConf())){
     continue;
     }
     //不再跳过
     log.info("获取taskBase原申诉配置:{}", JSONUtil.toJsonStr(adminTask.getAppealConf()));
     AppealConf appealConf = taskBaseAppealConfTransfer(adminTask.getAppealConf());
     log.info("处理后的taskBase申诉配置:{}", JSONUtil.toJsonStr(appealConf));
     adminTask.setAppealConf(appealConf);
     adminTaskRepo.editAdminAppealConf(adminTask.getCompanyId(), taskId, appealConf);

     List<String> taskUserIds = taskUserDao.listTaskUserIdByTaskIdWithTime(taskId, beginDate, endDate);
     fixEvalRuleAppealConf(taskUserIds, adminTask.getCompanyId(), appealConf);
     }
     }

     public void fixAppealOngoingAppealBatch() {

     List<PerfEvaluateTaskAppealBatch> batches;

     //查询进行中的申诉
     Integer pageNo = 1;
     Integer pageSize = 200;
     while (!(batches = taskAppealDao.listHistoryOngoingAppealBatchLeft(pageNo, pageSize)).isEmpty()) {
     List<String> batchIds = batches.stream().map(PerfEvaluateTaskAppealBatch::getId).collect(Collectors.toList());
     ListWrap<PerfEvaluateTaskAppealInfo> infos = taskAppealDao.listAppealInfoByBatchIds(batchIds);
     pageNo++;
     //遍历
     for (PerfEvaluateTaskAppealBatch batch : batches) {
     batch.historyInit();
     batch.setAppealInfos(infos.groupGet(batch.getId()));
     PerfEvaluateTaskAppealFlowNode node = new PerfEvaluateTaskAppealFlowNode();
     node.accHistoryBatch(batch);
     PerfEvaluateTaskAppealAuditor appealAuditor = new PerfEvaluateTaskAppealAuditor();
     appealAuditor.accHistoryBatch(batch);
     node.setOngoingAuditor(appealAuditor);
     batch.setOngoingAuditNode(node);
     }
     //批量更新
     taskAppealRepo.fixHistoryBatch(batches);
     }
     }
     **/

    public EditEmpEvalOrgInfoRs editBatchEmpEvalOrgInfo(EditEmpEvalOrgInfoCmd cmd) {

        AdminTask adminTask = getAdminTask(new TenantId(cmd.getCompanyId()), cmd.getTaskId());
        if (Objects.isNull(adminTask)) {
            throw new KpiI18NException("task_not_exist", "任务不存在");
        }
        if (adminTask.archived()) {
            throw new KpiI18NException("task_archived", "任务已归档，不支持修改");
        }

        List<EvalUser> evalUsers = taskUserDao.listBaseEvalUser(new TenantId(cmd.getCompanyId()), cmd.getTaskUserIds());
        Integer all = evalUsers.size();
        Integer sucCnt = 0;
        Set<String> empIds = new HashSet<>();
        //按照empId分组,如果存在数量大于1的,排除这部分
        Map<String, List<EvalUser>> collect = evalUsers.stream().collect(Collectors.groupingBy(EvalUser::getEmpId));
        evalUsers = collect.values().stream().filter(list -> list.size() == 1).flatMap(List::stream).collect(Collectors.toList());

        List<String> leftEmpIds = evalUsers.stream().map(EvalUser::getEmpId).collect(Collectors.toList());

        if (CollUtil.isEmpty(leftEmpIds)){
            return new EditEmpEvalOrgInfoRs(all, sucCnt);
        }

        List<EvalUser> others = taskUserDao.listBaseEvalUserByEmpIdsInTask(new TenantId(cmd.getCompanyId()), cmd.getTaskId(), leftEmpIds);
        Map<String, List<EvalUser>> othersMap = others.stream().collect(Collectors.groupingBy(EvalUser::getEmpId));

        List<EmpOrganization> paths = deptEmpDao.listDeptWithNamePath(new TenantId(cmd.getCompanyId()), Collections.singletonList(cmd.getOrgId()));
        ListWrap<EmpOrganization> pathMap = new ListWrap<>(paths).asMap(EmpOrganization::getOrgId);
        EmpOrganization path = pathMap.mapGet(cmd.getOrgId());

        KpiEmployee opEmp = kpiEmpDao.findEmployee(adminTask.getCompanyId(), cmd.getOpEmpId());

        if (Objects.isNull(opEmp)) {
            return new EditEmpEvalOrgInfoRs(all, sucCnt);
        }

        AdminTaskOperation adminTaskOperation
                = AdminTaskOperation.buildBase(cmd.getCompanyId(), cmd.getTaskId(), cmd.getOpAdminType(), cmd.getOpEmpId());
        adminTaskOperation.operate(opEmp, cmd.getOpAdminType());

        List<OperationLogDo> logs = new ArrayList<>();

        for (EvalUser evalUser : evalUsers) {
            if (TalentStatus.endStatus().contains(evalUser.getTaskStatus())) {
                //排除不能修改的状态
                continue;
            }
            if (CollUtil.isNotEmpty(othersMap) &&
                    othersMap.get(evalUser.getEmpId()).stream()
                            .anyMatch(user -> user.getOrgId().equals(cmd.getOrgId()))) {
                //排除已经有处于目标部门的evalUser
                continue;
            }
            sucCnt++;
            empIds.add(evalUser.getEmpId());
            OperationLog log = new OperationLog(cmd.getCompanyId(), evalUser.getId(),
                    "edit_emp_eval_org_info", cmd.getOpEmpId(), new Date());
            log.setBeforeValue(evalUser.getEmpOrgName());
            log.setAfterValue(path.getOrgName());
            evalUser.changeEmpEvalOrgBaseInfo(cmd.getOrgId(), path.getOrgName());
            evalUser.acceptOrgPath(path.getOrgCode(), path.getNamePath(), path.getPathHight());
            evalUser.setUpdatedUser(cmd.getOpEmpId());
            logs.add(new ToDataBuilder<>(log, OperationLogDo.class).data());
        }

        List<KpiEmp> orgInfoChangedEmps = kpiEmpRepo.listByEmp(new TenantId(cmd.getCompanyId()), new ArrayList<>(empIds));
        adminTaskOperation.changeOrgInfo(opEmp, cmd.getOpAdminType(), new AdminTaskOperation.EmpOrgInfo(null, path.getOrgName()), orgInfoChangedEmps);
        List<EvalUser> finalEvalUsers = evalUsers;
        tx.runTran(() -> {
            userRepo.batchUpdateEvalUserOrgInfo(cmd.getCompanyId(), finalEvalUsers);
            opLogDao.batchAddLogs(logs);
            if (CollUtil.isNotEmpty(orgInfoChangedEmps)){
                adminTaskOperationRepo.save(adminTaskOperation);
            }
        });
        return new EditEmpEvalOrgInfoRs(all, sucCnt);
    }
}
