package com.polaris.kpi.eval.app.task.appsvc;

import cn.com.polaris.kpi.company.MsgSceneEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.perf.www.common.em.CompanyMsgActionEnum;
import com.polaris.kpi.eval.app.TransactionWrap;
import com.polaris.kpi.eval.app.task.dto.SubmitFinishValueCmd;
import com.polaris.kpi.eval.app.task.result.BatchSubmitFinishValueResult;
import com.polaris.kpi.eval.domain.TaskEvalContext;
import com.polaris.kpi.eval.domain.task.acl.IObItemPlanAcl;
import com.polaris.kpi.eval.domain.task.dmsvc.*;
import com.polaris.kpi.eval.domain.task.dmsvc.ScorerTodoDmSvc;
import com.polaris.kpi.eval.domain.task.entity.CycleEval;
import com.polaris.kpi.eval.domain.task.entity.EvalOnExeStage;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.empeval.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.ExtOkrConf;
import com.polaris.kpi.eval.domain.task.entity.grade.ScoreRule;
import com.polaris.kpi.eval.domain.task.entity.msg.MsgTodoAggregate;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrGoal;
import com.polaris.kpi.eval.domain.task.event.BatchStageEndedV2;
import com.polaris.kpi.eval.domain.task.event.ThisStageEnded;
import com.polaris.kpi.eval.domain.task.event.admineval.AdminTaskBatchStartEval;
import com.polaris.kpi.eval.domain.task.event.admineval.StartExecuteEvalCreated;
import com.polaris.kpi.eval.domain.task.event.msg.BatchMsgTodoAggregateEvent;
import com.polaris.kpi.eval.domain.task.repo.*;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.eval.infr.task.dao.*;
import com.polaris.kpi.eval.infr.task.ppojo.FinishValueKpiItemSelectPo;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskUserDo;
import com.polaris.kpi.eval.infr.task.repimpl.EvalContextRepoImpl;
import com.polaris.kpi.okr.domain.acl.IOkrAcl;
import com.polaris.kpi.org.domain.dept.entity.CompanyConf;
import com.polaris.kpi.org.domain.emp.type.Emp;
import com.polaris.kpi.org.infr.company.dao.CompanyDaoImpl;
import com.polaris.kpi.org.infr.emp.dao.KpiEmpDao;
import com.polaris.kpi.org.type.TraceKey;
import com.polaris.kpi.setting.domain.entity.ScorerTodoSummary;
import com.polaris.kpi.setting.domain.repo.ResultAuditFlowRepo;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.lufei.ibatis.mapper.PagedList;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 执行阶段应用层
 */
@Service
@Slf4j
public class ExecuteStageAppSvc {
    @Autowired
    private TaskUserRepo userRepo;
    @Autowired
    private EmpEvalRuleRepo empRuleRepo;
    @Autowired
    private EvalContextRepoImpl contextRepo;
    @Autowired
    private GradeDao gradeDao;
    @Autowired
    private TaskUserDao taskUserDao;
    @Autowired
    private EvaluateTaskDao taskDao;
    @Autowired
    private EvalKpiDao kpiDao;
    @Autowired
    private EmpEvalDao empEvalDao;
    @Autowired
    private TaskKpiRepo taskKpiRepo;
    @Autowired
    private IOkrAcl iOkrAcl;
    @Autowired
    private ResultAuditFlowRepo resultAuditFlowRepo;
    @Autowired
    private ScorerSummaryTodoRepo scorerSummaryTodoRepo;
    @Autowired
    private CompanyDaoImpl companyDao;
    @Autowired
    private KpiEmpDao kpiEmpDao;
    @Autowired
    private OnExeEvalRepo onExeEvalRepo;
    @Autowired
    private IObItemPlanAcl itemPlanAcl;
    @Autowired
    private TransactionWrap tx;

    @Autowired
    private OnScoreEvalRepo onScoreEvalRepo;

    public List<OkrGoal> listOkrRef(String corpId, String dingUserId, ExtOkrConf extOkrCon) {
        List<OkrGoal> evalRefOkrs = iOkrAcl.listOkrs(corpId, extOkrCon, dingUserId);
        return evalRefOkrs;
    }

    //生成校准审批流程实例
    @Transactional
    public void batchCreateResultAuditFlow(String companyId, String opEmpId, List<EvalUser> evalUsers) {
        List<EvalUser> filterUsers = new ArrayList<>();
        for (EvalUser evalUser : evalUsers) {
            if (!evalUser.isAuditResultCollectSend()) {
                continue;
            }
            filterUsers.add(evalUser);
        }
        if (CollUtil.isEmpty(filterUsers)) {
            return;
        }
        resultAuditFlowRepo.refreshAuditFlow(companyId, opEmpId, filterUsers);
        List<String> taskIds = evalUsers.stream().map(EvalUser::getTaskId).distinct().collect(Collectors.toList());
        for (String taskId : taskIds) {
            resultAuditFlowRepo.refreshSummary(companyId, taskId);
        }
    }


    //fix data.xiaoqiu
    @Transactional
    public void fixBatchInitScoreRanges(String companyId, List<EvalUser> finisedEvals) {
        //保存等级规则组快照
        for (EvalUser taskUser : finisedEvals) {
            ScoreRule empScoreRule = gradeDao.getEmpScoreRule(new TenantId(companyId), taskUser.getEmpId(), taskUser.getOrgId());
            taskUser.scoreRanges(empScoreRule.getRanges());
        }
        userRepo.batchInitScoreRanges(finisedEvals);
    }


    public EndStartStageRs enterNextStageV1(String tenantId1, EmpId opEmpId, List<EvalUser> v1Users) {
        TenantId tenantId = new TenantId(tenantId1);
        EndStartStageRs executeFinisedRs = new EndStartStageRs(tenantId1);
        BatchMsgTodoAggregateEvent batchMsgs = new BatchMsgTodoAggregateEvent(tenantId, opEmpId);

        for (EvalUser taskUser : v1Users) {
            // EvalUser taskUser = userRepo.getEmpEval(tenantId, curEvalId);
            executeFinisedRs.addEval(taskUser);
            log.info("考核任务是否存在异常 taskUserId:{},hasError:{},ruleConfError:{}", taskUser.getId(), taskUser.hasError(), taskUser.getRuleConfError());
            if (taskUser.hasError()) {
                continue;
            }
            log.info("开始发起考核任务 taskUserId:{}", taskUser.getId());
            executeFinisedRs.addOkUser(taskUser);
            AsAdminTaskRule cycleTask = executeFinisedRs.get(taskUser.getTaskId());
            cycleTask = cycleTask == null ? empRuleRepo.getEmpEvalMerge(tenantId, taskUser.getId(), EmpEvalMerge.beforeScore) : cycleTask;
            executeFinisedRs.addAdminTask(taskUser.getTaskId(), cycleTask);
            // 给考核人发送通知与消息
            if (!MsgSceneEnum.noExtMsgTodoCompanys.contains(taskUser.getCompanyId().getId())) {
                MsgTodoAggregate msgTodoAggregate = new MsgTodoAggregate(tenantId, taskUser.getTaskId(), cycleTask.taskName(), taskUser.getEmpId(), taskUser.getId()).useScene(MsgSceneEnum.CREATE_TASK, CompanyMsgActionEnum.CONFIRM).addCenterMsg().addExtTempValue("org", "");
                batchMsgs.addEvent(msgTodoAggregate);
            }
        }
        batchMsgs.publish();
        for (EvalUser eval : executeFinisedRs.getFinisedEvals()) {
            new ThisStageEnded(executeFinisedRs.get(eval.getTaskId()), eval, TalentStatus.CREATED, opEmpId.getId()).fire();
            List<OkrGoal> okrGoals = iOkrAcl.synOkrs(tenantId1, eval.getTaskId(), eval.getEmpId());
            if (CollUtil.isEmpty(okrGoals)) {
                continue;
            }
            tx.runTran(() -> {
                this.importOkrItem(eval.getCompanyId(), eval, okrGoals, opEmpId);//单独一个事务
            });

        }
        return executeFinisedRs;
    }

    //真正的v2版本
    public void batchEnterNextStage(EndStartStageRs endStartStageRs, TenantId tenantId, List<EvalUser> v2Users, EmpId opEmpId) {
        List<String> v2UserIds = v2Users.stream().map(evalUser -> evalUser.getId()).collect(Collectors.toList());
        List<TaskEvalContext> v2Contexts = contextRepo.listStartEvalContext(tenantId, v2UserIds, 0);
        List<EvalUser> oks = new ArrayList<>();
        for (TaskEvalContext v2Context : v2Contexts) {
            BatchMsgTodoAggregateEvent batchMsgs = new BatchMsgTodoAggregateEvent(tenantId, opEmpId);
            v2Context.getEvals().removeIf(eval -> {
                EmpEvalMerge empEvalMerge = eval.getRule();
                EvalUser taskUser = eval.getEval();
                endStartStageRs.addAdminTask(taskUser.getTaskId(), empEvalMerge);
                if (taskUser.hasError()) {
                    endStartStageRs.addEval(taskUser);
                    log.info("考核任务是否存在异常 taskUserId:{},hasError:{},ruleConfError:{}", taskUser.getId(), taskUser.hasError(), taskUser.getRuleConfError());
                    return true;
                }
                endStartStageRs.addOkUser(taskUser);
                log.info("开始发起考核任务  v2.0  taskUserId:{}", taskUser.getId());
                // 给考核人发送通知与消息
                if (!MsgSceneEnum.noExtMsgTodoCompanys.contains(taskUser.getCompanyId().getId())) {
                    MsgTodoAggregate msgTodoAggregate = new MsgTodoAggregate(tenantId, taskUser.getTaskId(), empEvalMerge.taskName(), taskUser.getEmpId(), taskUser.getId()).useScene(MsgSceneEnum.CREATE_TASK, CompanyMsgActionEnum.CONFIRM).addCenterMsg().addExtTempValue("org", "");
                    batchMsgs.addEvent(msgTodoAggregate);
                }
                return false;
            });
            batchMsgs.publish();
            new BatchStageEndedV2(tenantId, opEmpId, Arrays.asList(v2Context), TalentStatus.CREATED).publish();
            for (EvalUser eval : oks) {
                List<OkrGoal> okrGoals = iOkrAcl.synOkrs(tenantId.getId(), eval.getTaskId(), eval.getEmpId());
                if (CollUtil.isEmpty(okrGoals)) {
                    continue;
                }
                tx.runTran(() -> {
                    this.importOkrItem(eval.getCompanyId(), eval, okrGoals, opEmpId);//单独一个事务
                });
            }
        }
    }


    @Transactional
    public void importOkrItem(TenantId tenantId, EvalUser user, List<OkrGoal> okrGoals, EmpId opEmpId) {
        CycleEval mergeTask = taskDao.getMergeTaskBase(tenantId, user.getId());
        KpiListWrap okrTypes = kpiDao.listAndBuildOkrType(user.getCompanyId(), user.getId());
        if (CollUtil.isEmpty(okrTypes.getDatas())) {
            return;
        }
        EmpEvalRule empEvalRule = empEvalDao.getEmpEvalRule(tenantId, user.getId());
        AutoImportOkrDmSvc okrDmSvc = new AutoImportOkrDmSvc(mergeTask, user, okrTypes);
        okrDmSvc.autoImportOKR(opEmpId.getId(), okrGoals);
        taskKpiRepo.saveNewOkr(opEmpId, okrTypes, empEvalRule, user.isOpenAvgWeightCompute());
    }

    public void fixImportOkr(String tenantId, String taskUserId, String opEmpId) {
        EvalUser eval = taskUserDao.getBothBaseUser(tenantId, null, null, taskUserId);
        List<OkrGoal> okrGoals = iOkrAcl.synOkrs(tenantId, eval.getTaskId(), eval.getEmpId());

        tx.runTran(() -> {
            this.importOkrItem(eval.getCompanyId(), eval, okrGoals, new EmpId(opEmpId));//单独一个事务
        });
    }


    //批量提交完成值
    public BatchSubmitFinishValueResult batchPageSubmitFinishValue(String tid, TenantId tenantId, EmpId opEmpId, List<SubmitFinishValueCmd> cmds) {
        MDC.put(TraceKey.TID, tid);
        BatchSubmitFinishValueResult batchResult = new BatchSubmitFinishValueResult();
        CompanyConf conf = companyDao.findCompanyConf(tenantId);
        BigDecimal fullScoreValue = companyDao.findFullScoreValue(tenantId);
        Emp opEmp = kpiEmpDao.findEmp(tenantId, opEmpId.getId());
        List<String> taskUserIds = CollUtil.map(cmds, cmd -> cmd.getTaskUserId(), true);
        ListWrap<EvalOnExeStage> onExeEvals = onExeEvalRepo.listOnExeEval(tenantId, taskUserIds);
        BatchSubmitFinishValueDmSvc valueDmSvc = new BatchSubmitFinishValueDmSvc(opEmpId, conf, fullScoreValue, onExeEvals);

        for (SubmitFinishValueCmd cmd : cmds) {
            EvalOnExeStage onExeEval = onExeEvals.mapGet(cmd.getTaskUserId());
            valueDmSvc.submitFinishValueOne(cmd.getTaskUserId(), cmd.getFinishValues());
            if (onExeEval.isOpenSendFinishValueChangedMsg()) {//完成值变化发送通知 sendInputNotify
                AdminTask adminTask = onExeEval.getAdminTask();
                //此处并不是最终版本.但为了不扩散修改的代码面积. 先放这
                new EmpEvalDmSvc().parseInputNotify(adminTask.getInputNotifyConf(), onExeEval.getEvalUser(), opEmpId.getId());
//                new CycleEvalDmSvc().batchSendFinishValueChanged(new Name(adminTask.getTaskName()), onExeEval.getEvalUser(), adminTask.inputChangedRecEmpIds(), opEmp.getName(), opEmpId.getId());
                valueDmSvc.batchSendFinishValueChanged(onExeEval, opEmp);
            }
        }
        tx.runTran(() -> {
            onExeEvalRepo.batchSaveSubmitedFinishValue(valueDmSvc.getOks(), opEmpId.getId());//保存更新后的业务状态.
            onExeEvalRepo.updateInputNotifyJobStatus(tenantId.getId(), valueDmSvc.getBeEndStages());//修改通知状态。
            onExeEvalRepo.saveKpiItemAutoScore(valueDmSvc.listReComputedKpiScoreInConfirmed());//修改通知状态。 执行环节仅修改指标的分值
            //评分中的，提交 完成值，需重新计算环节分值，然后更新
            this.onScoreEvalRepo.saveKpiItemAutoScore(opEmpId.getId(), valueDmSvc.listReComputedKpiScoreInScoring()); //updateAutoScore
        });
        itemPlanAcl.updateObObjects(tenantId.getId(), opEmpId.getId(), valueDmSvc.getPlanItemValues());
        valueDmSvc.publishEevent();
        log.info(" 批量提交存在未录入的results：{}", JSONUtil.toJsonStr(batchResult));
        return batchResult;
    }

    //完成值批量录入的帅选器-下拉指标列表
    public PagedList<FinishValueKpiItemSelectPo> pagedItemForSelectorFinishValue(TenantId companyId, String opEmpId, String itemName, Integer pageNo, Integer pageSize) {
        return kpiDao.pagedItemForSelectorFinishValue(companyId, opEmpId, itemName, pageNo, pageSize);
    }

}
