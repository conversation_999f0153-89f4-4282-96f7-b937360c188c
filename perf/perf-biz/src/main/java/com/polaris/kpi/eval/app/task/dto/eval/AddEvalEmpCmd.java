package com.polaris.kpi.eval.app.task.dto.eval;

import cn.com.polaris.kpi.EvalOrg;
import cn.com.polaris.kpi.temp.CycleId;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.polaris.acl.dept.pojo.org.EmpPositionDo;
import com.polaris.acl.dept.pojo.org.EmpRankDo;
import com.polaris.acl.kpi.eval.domain.EvalEmp;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.joda.time.DateTime;
import org.joda.time.Days;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.app.task.dto.eval
 * @Author: lufei
 * @CreateTime: 2022-08-29  09:19
 * @Description: TODO
 * @Version: 1.0
 */
@NoArgsConstructor
@Setter
@Getter
public class AddEvalEmpCmd {
    private TenantId tenantId;
    private EmpId opEmpId;
    private String opAdminType;
    private String cycleId;
    private String taskId;
    private List<EvalEmp> emps = new ArrayList<>();
    private List<String> orgs = new ArrayList<>();
    private List<String> roles = new ArrayList<>();  //角色ids
    private List<String> roleEmps = new ArrayList<>();  //角色员工ids
    private List<String> evalGroupIds = new ArrayList<>(); //考核关系组ids

    private List<EvalOrg> evalOrgs = new ArrayList<>();
    private String resMsg;
    private Date permEndTime;
    private boolean isExceed = false;
    private Integer taskUserCount = 0;
    private Integer onTheJobStatus; // 员工在职状态(实习:2 正式:3)
    private String empStatus; // 已离职-leave
    private String type;
    private String entryDateStart;      //入职开始日期
    private String entryDateEnd;        //入职结束日期

    public List<EvalEmp> deduplicateEmps() {
        // 去重：empId + orgId 相同的只保留一条，并更新字段 emps
        this.emps = new ArrayList<>(emps.stream()
                .collect(Collectors.toMap(
                        e -> e.getEmpId() + "_" + e.getAtOrgId(), // 唯一key
                        e -> e,
                        (existing, replacement) -> existing // 冲突保留第一个
                ))
                .values());
        return this.emps;
    }

    public List<EvalUser> buildEvalUsers() {
        List<EvalUser> users = emps.stream().map(emp -> {
            EvalUser user = new EvalUser(tenantId, opEmpId, new CycleId(cycleId));
            user.setTaskId(taskId);
            user.setRuleConfStatus(0);
            user.copyAtOrg(emp.getAtOrgId(), emp.getAtOrgName(), emp.getEmpId(), emp.getEmpName(),
                    emp.getAvatar(), emp.getEmpRank(), emp.getEmpPosition());
            return user;
        }).collect(Collectors.toList());
        return users;
    }

    public List<EvalUser> buildEvalOrgUsers() {
        List<EvalUser> users = evalOrgs.stream().map(org -> {
            EvalUser user = new EvalUser(tenantId, opEmpId, new CycleId(cycleId));
            user.setTaskId(taskId);
            user.setRuleConfStatus(0);
            user.setEvalOrgId(org.getEvalOrgId());
            user.setEvalOrgName(org.getEvalOrgName());
            user.evalEmpOrg(org.getOrgOwnerOrgId(), org.getOrgOwnerOrgName(), org.getOrgOwnerId(), org.getOrgOwnerName(), org.getOrgOwnerAvatar());
            return user;
        }).collect(Collectors.toList());
        return users;
    }


    public void accOp(TenantId tenantId, String opEmpId, String adminType) {
        this.tenantId = tenantId;
        this.opEmpId = new EmpId(opEmpId);
        this.opAdminType = adminType;
    }

    public List<String> evalOrgIds() {
        return evalOrgs.stream().map(e -> e.getEvalOrgId()).collect(Collectors.toList());
    }

    public int rmDuplicateEvalOrgId(List<String> existEvalOrgIds) {
        List<String> evalOrgIds = evalOrgIds();
        int addTotalCnt = evalOrgIds.size();
        for (String existEvalOrgId : existEvalOrgIds) {
            evalOrgIds.removeIf(o -> o.contains(existEvalOrgId));
        }
        int addCnt = evalOrgIds.size();

        if (CollUtil.isEmpty(existEvalOrgIds)) {
            this.resMsg = "您已成功添加" + addTotalCnt + "个组织考核";
            return addTotalCnt;
        }
        if (addCnt == 0) {
            this.resMsg = "您已成功添加" + addCnt + "个组织考核，其中" + addTotalCnt + "个组织考核已在任务中，无需重复添加";
            return addCnt;
        }
        this.evalOrgs = evalOrgs.stream().filter(s -> evalOrgIds.contains(s.getEvalOrgId())).collect(Collectors.toList());
        this.resMsg = "您已成功添加" + addCnt + "个组织考核，其中" + (addTotalCnt - addCnt) + "个组织考核已在任务中，无需重复添加";
        return evalOrgIds.size();
    }

    public void addEvalEmps(List<EvalEmp> evalEmps) {
        if (CollUtil.isNotEmpty(evalEmps)) {
            this.emps.addAll(evalEmps);
            this.deduplicateEmps();
        }
    }

    public void filterExistsEmps(List<EvalEmp> existed) {
        List<EvalEmp> emps = this.emps.stream().filter(a -> !existed.stream().map(b -> b.getEmpId() + "&" + b.getAtOrgId()).collect(Collectors.toList())
                .contains(a.getEmpId() + "&" + a.getAtOrgId())).collect(Collectors.toList());
        this.emps = emps;
    }

    public Boolean hasOrgs() {
        return CollUtil.isNotEmpty(this.orgs);
    }

    public Boolean hasRoles() {
        return CollUtil.isNotEmpty(this.roles);
    }

    public Boolean hasEmps() {
        return CollUtil.isNotEmpty(this.emps);
    }

    public Boolean hasRoleEmps() {
        return CollUtil.isNotEmpty(this.roleEmps);
    }

    public void distinctEmps() {
        this.emps = this.emps.stream().distinct().collect(Collectors.toList());
    }

    public void handleFreeCompanyData() {
        Integer limit = builderFreeLimit();
        if (CollUtil.isNotEmpty(this.emps) && this.emps.size() > limit) {
            this.emps = this.emps.stream().skip(0).limit(limit).collect(Collectors.toList());
            this.isExceed = true;
        }
    }

    public Boolean hasFreeCompany() {
        int days = Days.daysBetween(DateTime.now(), new DateTime(this.permEndTime)).getDays();
        return days + 1 > 18250;
    }

    public Integer builderFreeLimit() {
        if (this.taskUserCount == 0) {
            return 6;
        }
        return 6 - taskUserCount;
    }

    public List<String> atOrgIds() {
        if(CollUtil.isNotEmpty(evalOrgs)){
            return CollUtil.map(evalOrgs, org -> org.getEvalOrgId(), true);
        }
        return CollUtil.map(emps, evalEmp -> evalEmp.getAtOrgId(), true);
    }

    public void buildRankPosition(List<EvalUser> evalUsers, List<EmpRankDo> empRankDos, List<EmpPositionDo> empPositionDos) {
        Map<String, String> rankMap = Collections.emptyMap();
        if (CollUtil.isNotEmpty(empRankDos)){
            rankMap = empRankDos.stream()
                    .collect(Collectors.toMap(EmpRankDo::getRankName, EmpRankDo::getId, (v1, v2) -> v1));
        }
        Map<String, String> positionMap = Collections.emptyMap();
        if (CollUtil.isNotEmpty(empPositionDos)){
            positionMap = empPositionDos.stream()
                    .collect(Collectors.toMap(EmpPositionDo::getPostName, EmpPositionDo::getPostId, (v1, v2) -> v1));
        }

        for (EvalUser evalUser : evalUsers){
            if (StrUtil.isNotEmpty(evalUser.getEmpRank())){
                evalUser.setEmpRankId(rankMap.get(evalUser.getEmpRank()));
            }
            if (StrUtil.isNotEmpty(evalUser.getEmpPosition())){
                evalUser.setEmpPositionId(positionMap.get(evalUser.getEmpPosition()));
            }
        }
    }
}
