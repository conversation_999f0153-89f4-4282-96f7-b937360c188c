package com.polaris.kpi.eval.app.task.appsvc;

import cn.com.polaris.kpi.company.TenantSysConf;
import cn.com.polaris.kpi.eval.ExportEvalUser;
import cn.com.polaris.kpi.eval.IndexCalibration;
import cn.com.polaris.kpi.eval.KpiItemUsedField;
import cn.com.polaris.kpi.eval.KpiTypeUsedField;
import cn.com.seendio.polaris.excel.SimpleExcelExport;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.polaris.acl.dept.face.AppAcl;
import com.polaris.acl.dept.repository.DeptEmpDao;
import com.polaris.kpi.eval.app.cycle.appsvc.CycleEvalAppSvc;
import com.polaris.kpi.eval.app.pdf.appsvc.PdfAppSvc;
import com.polaris.kpi.eval.domain.task.entity.Cycle;
import com.polaris.kpi.eval.domain.task.entity.EmpEvalScorerNode;
import com.polaris.kpi.eval.domain.task.entity.EvalKpi;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalKpiType;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.entity.empeval.KpiListWrap;
import com.polaris.kpi.eval.domain.task.entity.log.ItemDynamicLog;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrGoal;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrKeyResult;
import com.polaris.kpi.eval.domain.task.repo.AdminTaskOperationRepo;
import com.polaris.kpi.eval.infr.cycle.dao.CycleEvalDao;
import com.polaris.kpi.eval.infr.task.dao.*;
import com.polaris.kpi.eval.infr.task.ppojo.*;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.ExportEvalLogPolicy;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.ExportEvalTablePolicy;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.ExportData;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.ExportEvalPo;
import com.polaris.kpi.eval.infr.task.ppojo.okr.ExportGoalEvalKrPo;
import com.polaris.kpi.eval.infr.task.ppojo.okr.OkrGoalDo;
import com.polaris.kpi.eval.infr.task.query.admin.ExportTaskOpQuery;
import com.polaris.kpi.eval.infr.task.query.empeval.EmpEvalAtTaskQuery2;
import com.polaris.kpi.org.domain.dept.entity.Company;
import com.polaris.kpi.org.infr.company.dao.CompanyDaoImpl;
import com.polaris.kpi.org.infr.company.dao.TenantSysConfDao;
import com.polaris.kpi.org.infr.dept.dao.KpiOrgDao;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import freemarker.cache.ClassTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.TemplateExceptionHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringEscapeUtils;
import org.lufei.ibatis.common.data.ToDataBuilder;
import org.lufei.ibatis.mapper.PagedList;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.app.task.appsvc
 * @Author: lufei
 * @CreateTime: 2024-01-22  14:15
 * @Version: 1.0
 */
@Slf4j
@Service
public class ExportEvalAppSvc {
    @Autowired
    private EmpEvalDao empEvalDao;
    @Autowired
    private CycleDao cycleDao;
    @Autowired
    private CycleEvalDao cycleEvalDao;
    @Autowired
    private EmpEvalWordExportDao exportDao;
    @Autowired
    private BatchScoreEvalMergeDao evalMergeDao;
    @Autowired
    private AppAcl appAcl;
    @Autowired
    private KpiOrgDao kpiOrgDao;
    @Autowired
    private DeptEmpDao deptEmpDao;
    @Autowired
    private TenantSysConfDao confDao;
    @Autowired
    private CompanyDaoImpl companyDao;
    @Autowired
    private AdminTaskOperationRepo adminTaskOperationRepo;
    @Autowired
    private CycleEvalAppSvc cycleEvalAppSvc;
    @Autowired
    private EvalKpiDao evalKpiDao;
    @Autowired
    private PdfAppSvc pdfAppSvc;
    @Autowired
    private EvaluateTaskDao evaluateTaskDao;
    @Autowired
    private EmpEvalScorerNodeDao empEvalScorerNodeDao;

    @Deprecated
    public PagedList<ExportEvalPo> listExportEmpEval(EmpEvalAtTaskQuery2 qry) {

        TenantId companyId = new TenantId(qry.getCompanyId());
        String lookScorerCode = qry.isMainAdmin() ? "mainAdminSeeAnonymous_20240202" : "childAdminSeeAnonymous_20240110";
        TenantSysConf tenantConf = this.confDao.findTenantConf(qry.getCompanyId(), lookScorerCode, 1);
        BigDecimal fullScoreValue = companyDao.findFullScoreValue(companyId);
        qry.setPageSize(100);
        qry.setAdminOrgIds(kpiOrgDao.listAdminScopePrivOrgIds(qry.getCompanyId(), qry.getOpEmpId(), null));  //获取管理权限范围
        //根据传入部门找到下面子部门
        if(CollUtil.isNotEmpty(qry.getOrgIds())){
            qry.setOrgIds(kpiOrgDao.listAllChildOrgIds(companyId, qry.getOrgIds(), null));
        }
        //考核任务权限
        if (qry.isPriv()) {
            qry.setTaskIds(cycleEvalDao.listTaskPrivByOpEmpId(qry.getCompanyId(), qry.getCycleId(), qry.getOpEmpId()));
        }
        PagedList<ExportEvalPo> evalPos = empEvalDao.pagedEmpEvalForExport(qry);
        List<String> taskUserIds = CollUtil.map(evalPos, po -> po.getTaskUserId(), true);
        if (CollUtil.isEmpty(taskUserIds)) {
            return evalPos;
        }
        List<String> taskIds = CollUtil.map(evalPos, po -> po.getTaskId(), true);
        ListWrap<EmpEvalMerge> evalRuleMap = evalMergeDao.mapBaseEvalMerge(companyId, taskUserIds);
        ListWrap<EmpEvalKpiType> typeGroups = evalMergeDao.listEvalKpiType(companyId, taskUserIds);
        ListWrap<EvalKpi> kpiItemGroups = evalMergeDao.listEmpEvalKpiItem(companyId, taskUserIds);
        ListWrap<ItemDynamicLog> dynamicLogGroups = evalKpiDao.listItemDynamicWrap(companyId.getId(), taskUserIds);
        ListWrap<EvalScoreResult> scoreRs = evalMergeDao.listItemScoreRs(companyId, taskUserIds);
        ListWrap<KpiTypeUsedField> typeFieldGroups = evalMergeDao.listTypeUsedField(companyId, taskUserIds);
        ListWrap<KpiItemUsedField> itemFieldGroups = evalMergeDao.listItemUsedField(companyId, taskUserIds);
        ListWrap<ExportEvalPo.ExportRefEval> refEvalsGroup = exportDao.listRefEvalPo(companyId, taskUserIds);
        ListWrap<EvalUserSummaryPo> summaryGroup = exportDao.listEvalUserSummary(companyId, taskUserIds);
        ListWrap<EvalRefOkrDo> refOkrs = exportDao.listEvalRefOkr(companyId, taskUserIds);
        ListWrap<OkrGoal> okrGoalGroups = evalKpiDao.listOkrGoal(companyId.getId(), taskUserIds, refOkrs);
        ListWrap<AdminTask> adminTaskMap = evalMergeDao.mapAdminTaskMerge(companyId, taskIds);
        ListWrap<OperationLogDo> logWrap = exportDao.mapOplog(companyId, taskUserIds);
        ListWrap<OperationLogPo> logAllWrap = exportDao.listOplogWarp(companyId, taskUserIds);
        Set<ExportEvalPo> collect = evalPos.stream().filter(exportEvalPo -> evalRuleMap.contains(exportEvalPo.getTaskUserId())).collect(Collectors.toSet());
        ListWrap<IndexCalibration> indexWrap = evaluateTaskDao.indexCalibrationListWrap(companyId.getId(),taskUserIds);
        for (ExportEvalPo user : collect) {
            user.setDingCorpId(qry.getDingCorpId());//用于后续导出特殊处理，评分优化上线后，就无需此处理
            ListWrap<KpiTypeUsedField> typeFields = new ListWrap<>(typeFieldGroups.groupGet(user.getTaskUserId())).groupBy(KpiTypeUsedField::getKpiTypeId);
            ListWrap<KpiItemUsedField> itemFields = new ListWrap<>(itemFieldGroups.groupGet(user.getTaskUserId())).groupBy(KpiItemUsedField::getKpiItemId);
            user.setTenantConf(tenantConf);
            EmpEvalMerge evalRule = evalRuleMap.mapGet(user.getTaskUserId());
            if (Objects.isNull(evalRule)) {//V1的数据
                AdminTask task = adminTaskMap.mapGet(user.getTaskId());
                evalRule = task.buildAsEvalRule(user.getTaskUserId());//转主阶段的配置
                evalRuleMap.mapData().put(user.getTaskUserId(), evalRule);
            }
            //自定义满分分值 如果为null 则使用系统提供分值
            if (evalRule.getScoreValueConf().getCustomFullScore() == null) {
                evalRule.getScoreValueConf().setCustomFullScore(fullScoreValue);
            }
            Cycle cycle = cycleDao.find(companyId.getId(), user.getCycleId());
            ListWrap<EvalKpi> itemWrap = new ListWrap<>(kpiItemGroups.groupGet(user.getTaskUserId())).groupBy(EvalKpi::getKpiTypeId);
            ListWrap<ItemDynamicLog> dynamicLogWrap = new ListWrap<>(dynamicLogGroups.groupGet(user.getTaskUserId())).groupBy(ItemDynamicLog::getKpiItemId);
            List<EmpEvalKpiType> kpiTypes = typeGroups.groupGet(user.getTaskUserId());
            OperationLogDo log = logWrap.mapGet(user.getTaskUserId());
            user.setEvalRule(evalRule);
            evalRule.setKpiTypes(new KpiListWrap(kpiTypes, EmpEvalMerge.type | EmpEvalMerge.item));
            List<OkrGoal> goals = okrGoalGroups.groupGet(user.getTaskUserId());
            List<OkrKeyResult> krs = goals.stream().flatMap(okrGoal -> okrGoal.getKeyResults().stream()).collect(Collectors.toList());
            ListWrap<OkrKeyResult> krMap = new ListWrap<>(krs).asMap(OkrKeyResult::getKpiItemId);
            for (EmpEvalKpiType type : kpiTypes) {
                type.setKpiTypeUsedFields(typeFields.groupGet(type.getKpiTypeId()));
                List<EvalKpi> items = itemWrap.groupGet(type.getKpiTypeId());
                for (EvalKpi item : items) {
                    List<ItemDynamicLog> dynamicLogs = dynamicLogWrap.groupGet(item.getKpiItemId());
                    if (CollUtil.isNotEmpty(dynamicLogs)) {
                        item.setFinishValueComment(dynamicLogs.get(0).getComment());
                    }
                    OkrKeyResult keyResult = krMap.mapGet(item.getKpiItemId());
                    if (keyResult != null) {
                        item.setItemFinishValue(keyResult.getProgress());
                    }
                    item.initKpiItemUsedFields(itemFields.groupGet(item.getKpiItemId()));
                }
                type.setItems(itemWrap.groupGet(type.getKpiTypeId()));
            }
            user.setSignPicDate(log == null ? null : DateUtil.formatDate(log.getCreatedTime()));
            user.setCycle(cycle);
            user.setRefEvals(refEvalsGroup.groupGet(user.getTaskUserId()));
            List<OperationLogPo> logPos = logAllWrap.groupGet(user.getTaskUserId());
            for (OperationLogPo logPo : logPos) {
                user.addTaskLog(DateUtil.format(logPo.getCreatedTime(), "yyyy年MM月dd日 HH:mm"), logPo.getBusinessScene(), logPo.getCreatedUserName(), logPo.getSignature(), user.getSignImg());
            }
            user.buildWord(kpiTypes, scoreRs.groupGet(user.getTaskUserId()), summaryGroup.groupGet(user.getTaskUserId()),
                    refOkrs.groupGet(user.getTaskUserId()),indexWrap);
        }
        //log.info("=====evalPos:{}",JSONUtil.toJsonStr(evalPos));
        return evalPos;
    }

    public PagedList<ExportEvalPo> listExportEmpEvalV3(EmpEvalAtTaskQuery2 qry) {
        log.info("====开始进行导出考核表操作： listExportEmpEvalV3!!!!");

        try {
            long startTime = System.currentTimeMillis();
            TenantId companyId = new TenantId(qry.getCompanyId());
            String lookScorerCode = qry.isAdminType() ? "mainAdminSeeAnonymous_20240202" : "childAdminSeeAnonymous_20240110";

            // 查询配置和满分值
            TenantSysConf tenantConf = this.confDao.findTenantConf(qry.getCompanyId(), lookScorerCode, 1);
            if (tenantConf == null) {
                log.warn("未能找到租户配置，companyId: {}", qry.getCompanyId());
                return new PagedList<>();
            }

            BigDecimal fullScoreValue = companyDao.findFullScoreValue(companyId);
            if (fullScoreValue == null) {
                log.warn("未能找到满分值,赋予默认值0，companyId: {}", qry.getCompanyId());
                fullScoreValue = BigDecimal.ZERO; // 默认值
            }
            // 设置分页和权限范围
            qry.setPageSize(100);
            qry.setAdminOrgIds(kpiOrgDao.listAdminScopePrivOrgIds(qry.getCompanyId(), qry.getOpEmpId(), null));  //获取管理权限范围
            //考核任务权限
            if (qry.isPriv()) {
                qry.setTaskIds(cycleEvalDao.listTaskPrivByOpEmpId(qry.getCompanyId(), qry.getCycleId(), qry.getOpEmpId()));
            }
            PagedList<ExportEvalPo> evalPos = empEvalDao.pagedEmpEvalForExport(qry);
            if (evalPos == null || evalPos.isEmpty()) {
                log.info("未查询到考核数据");
                return evalPos;
            }
            List<String> taskUserIds = CollUtil.map(evalPos, ExportEvalPo::getTaskUserId, true);
            if (CollUtil.isEmpty(taskUserIds)) {
                return evalPos;
            }
            List<String> taskIds = CollUtil.map(evalPos, ExportEvalPo::getTaskId, true);

            // 查询导出考核表所需数据
            ListWrap<EmpEvalMerge> evalRuleMap = evalMergeDao.mapBaseEvalMerge(companyId, taskUserIds);
            ListWrap<EmpEvalKpiType> typeGroups = evalMergeDao.listEvalKpiType(companyId, taskUserIds);
            ListWrap<EvalKpi> kpiItemGroups = evalMergeDao.listEmpEvalKpiItem(companyId, taskUserIds);
            ListWrap<ItemDynamicLog> dynamicLogGroups = evalKpiDao.listItemDynamicWrap(companyId.getId(), taskUserIds);
            ListWrap<KpiTypeUsedField> typeFieldGroups = evalMergeDao.listTypeUsedField(companyId, taskUserIds);
            ListWrap<KpiItemUsedField> itemFieldGroups = evalMergeDao.listItemUsedField(companyId, taskUserIds);
            ListWrap<ExportEvalPo.ExportRefEval> refEvalsGroup = exportDao.listRefEvalPo(companyId, taskUserIds);
            ListWrap<EmpEvalScorerNode> scorerNodeGroups = empEvalScorerNodeDao.listWpEmpEvalScorerNode(companyId.getId(), taskUserIds);//评分环节
            ListWrap<EvalUserSummaryPo> summaryGroup = exportDao.listEvalUserSummary(companyId, taskUserIds);
            ListWrap<EvalRefOkrDo> refOkrs = exportDao.listEvalRefOkr(companyId, taskUserIds);
            ListWrap<OkrGoal> okrGoalGroups = evalKpiDao.listOkrGoal(companyId.getId(), taskUserIds, refOkrs);
            ListWrap<AdminTask> adminTaskMap = evalMergeDao.mapAdminTaskMerge(companyId, taskIds);
            ListWrap<OperationLogDo> logWrap = exportDao.mapOplog(companyId, taskUserIds);
            ListWrap<OperationLogPo> logAllWrap = exportDao.listOplogWarp(companyId, taskUserIds);
            ListWrap<IndexCalibration> indexWrap = evaluateTaskDao.indexCalibrationListWrap(companyId.getId(), taskUserIds);

            Set<ExportEvalPo> collect = evalPos.stream().filter(exportEvalPo -> evalRuleMap.contains(exportEvalPo.getTaskUserId())).collect(Collectors.toSet());
            for (ExportEvalPo user : collect) {
                processExportEvalPo(user, tenantConf, fullScoreValue, evalRuleMap, typeGroups, kpiItemGroups,
                        dynamicLogGroups, typeFieldGroups, itemFieldGroups, adminTaskMap, cycleDao, okrGoalGroups,
                        refEvalsGroup, scorerNodeGroups, summaryGroup, refOkrs, logWrap, logAllWrap, indexWrap);
            }

            log.info("===== 本批次导出完成 ！！！，耗时：{}", System.currentTimeMillis() - startTime);
            return evalPos;
        } catch (Exception e) {
            log.error("导出考核表操作失败", e);
            throw new RuntimeException("导出考核表操作失败", e);
        }
    }
    private void processExportEvalPo(ExportEvalPo user, TenantSysConf tenantConf, BigDecimal fullScoreValue,
                                     ListWrap<EmpEvalMerge> evalRuleMap, ListWrap<EmpEvalKpiType> typeGroups,
                                     ListWrap<EvalKpi> kpiItemGroups, ListWrap<ItemDynamicLog> dynamicLogGroups,
                                     ListWrap<KpiTypeUsedField> typeFieldGroups, ListWrap<KpiItemUsedField> itemFieldGroups,
                                     ListWrap<AdminTask> adminTaskMap, CycleDao cycleDao, ListWrap<OkrGoal> okrGoalGroups,
                                     ListWrap<ExportEvalPo.ExportRefEval> refEvalsGroup, ListWrap<EmpEvalScorerNode> scorerNodeGroups,
                                     ListWrap<EvalUserSummaryPo> summaryGroup, ListWrap<EvalRefOkrDo> refOkrs,
                                     ListWrap<OperationLogDo> logWrap, ListWrap<OperationLogPo> logAllWrap,
                                     ListWrap<IndexCalibration> indexWrap) {
        // 初始化用户数据
        user.setTenantConf(tenantConf);

        EmpEvalMerge evalRule = evalRuleMap.mapGet(user.getTaskUserId());
        if (Objects.isNull(evalRule)) { // V1 数据
            AdminTask task = adminTaskMap.mapGet(user.getTaskId());
            evalRule = task.buildAsEvalRule(user.getTaskUserId());
            evalRuleMap.mapData().put(user.getTaskUserId(), evalRule);
        }

        // 自定义满分分值
        if (evalRule != null && evalRule.getScoreValueConf().getCustomFullScore() == null) {
            evalRule.getScoreValueConf().setCustomFullScore(fullScoreValue);
        }

        // 设置考核周期
        Cycle cycle = cycleDao.find(user.getTenantConf().getCompanyId(), user.getCycleId());
        user.setCycle(cycle);

        // 处理 KPI 类型和项目
        ListWrap<KpiTypeUsedField> typeFields = new ListWrap<>(typeFieldGroups.groupGet(user.getTaskUserId())).groupBy(KpiTypeUsedField::getKpiTypeId);
        ListWrap<KpiItemUsedField> itemFields = new ListWrap<>(itemFieldGroups.groupGet(user.getTaskUserId())).groupBy(KpiItemUsedField::getKpiItemId);
        ListWrap<EvalKpi> itemWrap = new ListWrap<>(kpiItemGroups.groupGet(user.getTaskUserId())).groupBy(EvalKpi::getKpiTypeId);
        ListWrap<ItemDynamicLog> dynamicLogWrap = new ListWrap<>(dynamicLogGroups.groupGet(user.getTaskUserId())).groupBy(ItemDynamicLog::getKpiItemId);
        List<EmpEvalKpiType> kpiTypes = typeGroups.groupGet(user.getTaskUserId());

        OperationLogDo log = logWrap.mapGet(user.getTaskUserId());
        user.setSignPicDate(log == null ? null : DateUtil.formatDate(log.getCreatedTime()));

        List<OkrGoal> goals = okrGoalGroups.groupGet(user.getTaskUserId());
        List<OkrKeyResult> krs = goals.stream().flatMap(okrGoal -> okrGoal.getKeyResults().stream()).collect(Collectors.toList());
        ListWrap<OkrKeyResult> krMap = new ListWrap<>(krs).asMap(OkrKeyResult::getKpiItemId);

        for (EmpEvalKpiType type : kpiTypes) {
            type.setKpiTypeUsedFields(typeFields.groupGet(type.getKpiTypeId()));
            List<EvalKpi> items = itemWrap.groupGet(type.getKpiTypeId());
            if (CollUtil.isEmpty(items)){
                continue;
            }

            for (EvalKpi item : items) {
                List<ItemDynamicLog> dynamicLogs = dynamicLogWrap.groupGet(item.getKpiItemId());
                if (CollUtil.isNotEmpty(dynamicLogs)) {
                    item.setFinishValueComment(dynamicLogs.get(0).getComment());
                }
                OkrKeyResult keyResult = krMap.mapGet(item.getKpiItemId());
                if (keyResult != null) {
                    item.setItemFinishValue(keyResult.getProgress());
                }
                item.initKpiItemUsedFields(itemFields.groupGet(item.getKpiItemId()));
            }
            type.setItems(itemWrap.groupGet(type.getKpiTypeId()));
        }

        user.setEvalRule(evalRule);
        evalRule.setKpiTypes(new KpiListWrap(kpiTypes, EmpEvalMerge.type | EmpEvalMerge.item));
        user.setRefEvals(refEvalsGroup.groupGet(user.getTaskUserId()));

        List<OperationLogPo> logPos = logAllWrap.groupGet(user.getTaskUserId());
        for (OperationLogPo logPo : logPos) {
            user.addTaskLog(DateUtil.format(logPo.getCreatedTime(), "yyyy年MM月dd日 HH:mm"), logPo.getBusinessScene(), logPo.getCreatedUserName(), logPo.getSignature(), user.getSignImg());
        }

        user.buildWordV3(kpiTypes, scorerNodeGroups.groupGet(user.getTaskUserId()), summaryGroup.groupGet(user.getTaskUserId()),
                refOkrs.groupGet(user.getTaskUserId()), indexWrap);
    }

    //zip文件发送
    @Async
    public void startExportEmpEval(String tid, EmpEvalAtTaskQuery2 qry) throws IOException {
        MDC.put("tid", tid);
        PagedList<ExportEvalPo> exportEvalPos;
        Company company = deptEmpDao.getCompany(new TenantId(qry.getCompanyId()));
        qry.setDingCorpId(company.getDingCorpId());//用于后续导出特殊处理，评分优化上线后，就无需此处理
        List<ExportEvalUser> exportEvalUsers = new ArrayList<>();
        while (!(exportEvalPos = listExportEmpEvalV3(qry)).isEmpty()) {
            qry.setPageNo(qry.getPageNo() + 1);
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                ClassPathResource resource = new ClassPathResource("templates/模板-无评分详情.docx");
                String[] files = new String[exportEvalPos.size()];
                InputStream[] ins = new InputStream[exportEvalPos.size()];
                for (int i = 0; i < exportEvalPos.size(); i++) {
                    ExportEvalPo user = exportEvalPos.get(i);
                    exportEvalUsers.add(new ToDataBuilder<>(user, ExportEvalUser.class).data());
                    user.setCompanyName(company.getName());
                    if (user.getEvalRule() == null) {
                        continue;
                    }
                    Configure config = Configure.builder().bind("typeWrap", new ExportEvalTablePolicy())
                            .bind("taskLogs", new ExportEvalLogPolicy())
                            .build();
                    XWPFTemplate template = XWPFTemplate.compile(resource.getInputStream(), config).render(user);
                    String file = String.format("考核表%s_%s_%s.docx", i, user.getEmpName(), user.getEmpOrgName());
                    ByteArrayOutputStream oneOut = new ByteArrayOutputStream();
                    template.write(oneOut);
                    ins[i] = new ByteArrayInputStream(oneOut.toByteArray());
                    files[i] = file;
                }
                //zip(ZipOutputStream zipOutputStream, String[] paths, InputStream[] ins)
                ZipUtil.zip(outputStream, files, ins);
                appAcl.sendFile(new TenantId(qry.getCompanyId()), qry.getOpEmpId(), "考核表.zip", outputStream.toByteArray());
            } catch (Throwable e) {
                log.error("export report exception:" + e.getMessage(), e);
            }
        }
        adminTaskOperationRepo.saveExportOperation(exportEvalUsers, qry.getCompanyId(), qry.getOpEmpId(), qry.getOpAdminType(), 1);
        if (StrUtil.isNotBlank(qry.getCycleId())) {
            cycleEvalAppSvc.exportCycleTask(qry.getCompanyId(), qry.getOpEmpId(), qry.getOpAdminType(), 1, exportEvalUsers.size(), qry.getCycleId());
        }
    }

    //zip文件发送
    @Async
    public void startExportEmpEvalPdf(String tid, EmpEvalAtTaskQuery2 qry,HttpServletResponse response) {
        MDC.put("tid", tid);
        PagedList<ExportEvalPo> exportEvalPos;
        qry.setExcelType("pdf");
        Company company = deptEmpDao.getCompany(new TenantId(qry.getCompanyId()));
        qry.setDingCorpId(company.getDingCorpId());//用于后续导出特殊处理，评分优化上线后，就无需此处理
        while (!(exportEvalPos = listExportEmpEvalV3(qry)).isEmpty()) {
            qry.setPageNo(qry.getPageNo() + 1);
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                String[] files = new String[exportEvalPos.size()];
                InputStream[] ins = new InputStream[exportEvalPos.size()];
                for (int i = 0; i < exportEvalPos.size(); i++) {
                    ExportEvalPo user = exportEvalPos.get(i);
                    user.setCompanyName(company.getName());
                    if (user.getEvalRule() == null) {
                        continue;
                    }
                    String str = JSONObject.toJSONString(exportEvalPos.get(i), SerializerFeature.WriteNullListAsEmpty,
                            SerializerFeature.WriteNullBooleanAsFalse,
                            SerializerFeature.WriteMapNullValue,
                            SerializerFeature.WriteNullStringAsEmpty);
//                    log.info("=====考核表===== {}",str);
                    JSONObject map = (JSONObject) JSONObject.parse(str, Feature.InitStringFieldAsEmpty);
                    JSONObject filtered = map.entrySet().stream()
                            .filter(entry -> entry.getValue() != null && !"".equals(entry.getValue()))
                            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, JSONObject::new));
                    String html = FreeMarkerTemplateUtils.processTemplateIntoString(freemarkerConfiguration.getTemplate("taskEval.ftl"), filtered);
                    html = sanitizeHtmlForPdf(html);  // 加这一行,先处理实体，再过滤非法字符 【隐藏看不见的】
                    ByteArrayOutputStream oneOut = new ByteArrayOutputStream();
                    pdfAppSvc.htmlText2PDF("", html, oneOut);
                    String file = String.format("考核表%s_%s_%s.pdf", i, user.getEmpName(), user.getEmpOrgName());
                    ins[i] = new ByteArrayInputStream(oneOut.toByteArray());
                    files[i] = file;
                }
                ZipUtil.zip(outputStream, files, ins);
                appAcl.sendFile(new TenantId(qry.getCompanyId()), qry.getOpEmpId(), "考核表.zip", outputStream.toByteArray());
            }catch (Throwable e) {
                log.error("export report exception:" + e.getMessage(), e);
            }
        }
    }

    public static String sanitizeHtmlForPdf(String html) {
        if (html == null) return null;

        // 1. 替换 HTML 实体
        html = html
                .replace("&nbsp;", " ")
                .replace("&quot;", "\"")
                .replace("&amp;", "&")
                .replace("&lt;", "<")
                .replace("&gt;", ">")
                .replace("&apos;", "'");

        // 2. 过滤非法 XML 字符
        return stripNonValidXMLCharacters(html);
    }
    public static String stripNonValidXMLCharacters(String input) {
        if (input == null || input.isEmpty()) return "";
        StringBuilder out = new StringBuilder();
        char current;
        for (int i = 0; i < input.length(); i++) {
            current = input.charAt(i);
            // 允许的XML字符范围
            if ((current == 0x9) ||
                    (current == 0xA) ||
                    (current == 0xD) ||
                    ((current >= 0x20) && (current <= 0xD7FF)) ||
                    ((current >= 0xE000) && (current <= 0xFFFD)) ||
                    ((current >= 0x10000) && (current <= 0x10FFFF)))
                out.append(current);
        }
        return out.toString();
    }
        private static final Configuration freemarkerConfiguration = new Configuration(Configuration.VERSION_2_3_30);

        static {
            //这里比较重要，用来指定加载模板所在的路径
            freemarkerConfiguration.setTemplateLoader(new ClassTemplateLoader(PdfAppSvc.class, "/tpl"));
            freemarkerConfiguration.setDefaultEncoding("UTF-8");
            freemarkerConfiguration.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
//        CONFIGURATION.setCacheStorage(NullCacheStorage.INSTANCE);
        }
        //多人同一个文件
        @Async
        public void forEachExportEmpEval (String tid, EmpEvalAtTaskQuery2 qry) throws IOException {
            Company company = deptEmpDao.getCompany(new TenantId(qry.getCompanyId()));
            qry.setDingCorpId(company.getDingCorpId());//用于后续导出特殊处理，评分优化上线后，就无需此处理
            MDC.put("tid", tid);
            ClassPathResource resource = new ClassPathResource("templates/模板-考核表each.docx");
            PagedList<ExportEvalPo> exportEvalPos;
            while (!(exportEvalPos = listExportEmpEvalV3(qry)).isEmpty()) {
                Configure config = Configure.builder().bind("typeWrap", new ExportEvalTablePolicy())
                        .bind("taskLogs", new ExportEvalLogPolicy())
                        .build();
                qry.setPageNo(qry.getPageNo() + 1);
                ExportData exportData = new ExportData(company.getName(), exportEvalPos);
                XWPFTemplate template = XWPFTemplate.compile(resource.getInputStream(), config).render(exportData);
                String file = String.format("考核表页_%s.docx", exportEvalPos.getPageNo());
                try (ByteArrayOutputStream oneOut = new ByteArrayOutputStream()) {
                    template.write(oneOut);
                    oneOut.flush();
                    appAcl.sendFile(new TenantId(qry.getCompanyId()), qry.getOpEmpId(), file, oneOut.toByteArray());
                } catch (IOException e) {
                    log.error("export report exception:" + e.getMessage(), e);
                }
            }
        }

        @Async
        public void startExportGoalEval (String tid, String companyId, String sendToCompanyId, String sendToEmpId){
            TenantId tenantId = new TenantId(companyId);
            MDC.put("tid", tid);
            ExportTaskOpQuery qry = new ExportTaskOpQuery();
            qry.setTenantId(tenantId);
            qry.setPageNo(1);
            qry.setPageSize(500);

            Supplier<PagedList> pagedDataFun = () -> this.listExportGoalEval(qry);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ExcelWriterBuilder builder = EasyExcel.write(outputStream);
            new SimpleExcelExport(builder).writeSheet("KR表", pagedDataFun, qry, ExportGoalEvalKrPo.class).finish();
            this.appAcl.sendFile(new TenantId(sendToCompanyId), sendToEmpId, "KR表.xlsx", outputStream.toByteArray());
        }

        private PagedList listExportGoalEval (ExportTaskOpQuery qry){
            PagedList<String> page = exportDao.pagedTaskUser(qry);
            List<String> evalIds = page.getData();
            ListWrap<EvalScoreResult> scoreRs = evalMergeDao.listItemScoreRs(qry.getTenantId(), evalIds);
            ListWrap<OkrGoalDo> okrGoalWrap = exportDao.listEvaGoal(qry.getTenantId(), evalIds);
            ListWrap<EvalRefOkrDo> refOkrWrap = exportDao.listEvalRefOkr(qry.getTenantId(), evalIds);
            List<ExportGoalEvalKrPo> collect = refOkrWrap.getDatas().stream().map(ref -> {
                ExportGoalEvalKrPo evalKrPo = Convert.convert(ExportGoalEvalKrPo.class, ref);
                List<OkrGoalDo> okrGoalDos = okrGoalWrap.groupGet(ref.getTaskUserId());
                ListWrap<OkrGoalDo> oMap = new ListWrap<>(okrGoalDos).asMap(okrGoalDo -> okrGoalDo.getObjectiveId());
                OkrGoalDo okrGoalDo = oMap.mapGet(evalKrPo.getTargetId());
                OkrKeyResult kr = okrGoalDo.mapGetKeyResult(evalKrPo.getActionId());
                if (kr != null) {
                    evalKrPo.setKrWeight(kr.getWeight());//KR权重
                    evalKrPo.setProcess(kr.getProgress());//KR进度
                    evalKrPo.krKeyAcion(kr == null ? new ArrayList<>() : kr.getKeyActions());//关键举措
                }
                List<EvalScoreResult> results = scoreRs.groupGet(ref.getTaskUserId());
                ListWrap<EvalScoreResult> scoresByItem = new ListWrap<>(results).groupBy(r -> r.getKpiItemId());
                List<EvalScoreResult> itemScores = scoresByItem.groupGet(ref.getTaskKpiId());
                evalKrPo.scores(itemScores);  //上级评分	自评分
                return evalKrPo;
            }).collect(Collectors.toList());
            page.convert(collect);
            return page;
        }
    }
