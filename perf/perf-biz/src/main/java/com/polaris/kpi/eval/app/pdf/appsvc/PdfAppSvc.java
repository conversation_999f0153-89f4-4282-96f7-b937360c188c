package com.polaris.kpi.eval.app.pdf.appsvc;

import cn.hutool.json.JSONUtil;
import com.itextpdf.text.pdf.BaseFont;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.stereotype.Service;
import org.xhtmlrenderer.pdf.ITextFontResolver;
import org.xhtmlrenderer.pdf.ITextRenderer;

import java.io.*;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PdfAppSvc {
    public static String classPath = URLDecoder.decode(PdfAppSvc.class.getClassLoader().getResource(".").getPath());
    private static final String imageFolder2007 = "imageFolder2007/";
    private static final String imageFolder2003 = "imageFolder2003/";
    private Logger logger = LoggerFactory.getLogger(getClass());

    public PdfAppSvc() {
        init();
    }

    /**
     * 静态html页面转化为pdf格式
     *
     * @param fontPath 字体目录
     * @param htmlPath 静态页面路径
     * @param pdfPath  生成pdf的路径
     * @return
     * @throws Exception
     */
    public Boolean htmlFile2PDF(String fontPath, String htmlPath, String pdfPath) throws Exception {
        BufferedReader bufferedReader = new BufferedReader(new FileReader(htmlPath));
        String content = bufferedReader.lines().collect(Collectors.joining(""));
        return htmlText2PDF(fontPath, content, pdfPath);
    }

    public Boolean htmlText2PDF(String fontPath, String content, String pdfPath) throws Exception {
        OutputStream os = new FileOutputStream(pdfPath);
        return htmlText2PDF(fontPath, content, os);
    }

    /**
     * @param fontPath 字体目录
     * @param content  静态页面文本
     * @param os       输出流
     * @return
     * @throws Exception
     */
    public Boolean htmlText2PDF(String fontPath, String content, OutputStream os) throws Exception {
//        logger.debug("入参html:\n" + content);
        ITextRenderer renderer = new ITextRenderer();
        //去掉不支持的字体
        content = content.replaceAll("font-family:[^;]*;", "").trim();
        org.jsoup.nodes.Document normalise = Jsoup.parse(content).normalise();
        //增加字体
        normalise.body().attr("style", "font-family: SimSun;");
        //normalise.outputSettings().syntax(org.jsoup.nodes.Document.OutputSettings.Syntax.xml); // 强制输出 XML 语法
       // normalise.outputSettings().escapeMode(org.jsoup.nodes.Entities.EscapeMode.xhtml); // 确保正确的实体转义
        String html = normalise.html().toString();
        //闭合标签
        html = replaceHtmlTag(html, "br|img|meta");
        html = processHtml(html);
        html = html.replaceAll("<div style=\\\"width:595.0pt;.*", "<div style=\"position: relative; width: 100%; max-width: 600px; padding: 20px; font-family: 'SimSun'; font-size: 14px; letter-spacing: 1px; color: #666666; border: 1px solid #E1E1E1; word-break: break-all\">");

        // 解码 HTML 实体
        html = decodeHtmlEntities(html);

        logger.debug("处理掉闭合标签后:\n" + html);
        renderer.setDocumentFromString(html);
        // 解决中文支持问题
        ITextFontResolver fontResolver = renderer.getFontResolver();
        fontResolver.addFont(fontPath(fontPath)+",0", BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
        renderer.layout();
        try {
            renderer.createPDF(os);
        }catch (Exception e) {
            e.getMessage();
        }
        os.flush();
        os.close();
        return true;
    }

    private static String decodeHtmlEntities(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        input = input
                .replace("&nbsp;", " ");

        // 第一步：将所有非法的 & 符号转义为 &amp;
        // 匹配所有不是标准 HTML 实体的 & 符号
        return input.replaceAll("&(?!\\w+;)", "&amp;");
    }

    /**
     * 基本功能：替换指定的标签
     * <p>
     *
     * @param str
     * @param tag 要替换的标签
     * @return String
     * @如：替换img标签的src属性值为[img]属性值[/img]
     */
    public static String replaceHtmlTag(String str, String tag) {
        String regxpForTag = String.format("<((%s)[^>]*)>", tag);
//        String regxpForTagAttrib = tagAttrib + "=\"([^\"]+)\"";
        Pattern patternForTag = Pattern.compile(regxpForTag);
//        Pattern patternForAttrib = Pattern.compile(regxpForTagAttrib);
        Matcher matcherForTag = patternForTag.matcher(str);
        StringBuffer sb = new StringBuffer();
        boolean result = matcherForTag.find();
        while (result) {
            matcherForTag.appendReplacement(sb, String.format("<$1></$2>"));
            result = matcherForTag.find();
        }
        matcherForTag.appendTail(sb);
        return sb.toString();
    }

    public static boolean isReplaceHtmlTag(String str, String tag) {
        String regxpForTag = String.format("<((%s)[^>]*)>", tag);
        Pattern patternForTag = Pattern.compile(regxpForTag);
        Matcher matcherForTag = patternForTag.matcher(str);
        return matcherForTag.find();
    }

    public static String processHtml(String input) {
        // 创建模式和匹配器来查找HTML标签之间的文本
        Pattern pattern = Pattern.compile("<[^>]+>|[^<]+"); // 匹配HTML标签或非标签文本
        Matcher matcher = pattern.matcher(input);

        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            String match = matcher.group();
            if (containsChinese(match) && !isReplaceHtmlTag(match,"br|img|meta")) {  //包含中文，表示不是html标签
                match = escapeHtml(match); // 转义文本中的特殊字符
            }
            matcher.appendReplacement(result, Matcher.quoteReplacement(match)); // 添加到结果中
        }
        matcher.appendTail(result); // 添加剩余部分

        return result.toString();
    }

    public static boolean containsChinese(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }
        // 使用两个正则表达式检查
        boolean hasChinese = str.matches(".*[\\u4e00-\\u9fa5].*");
        return hasChinese ;
    }

    public static boolean containsChineseOrNumber(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }
        // 使用两个正则表达式检查
        boolean hasChinese = str.matches(".*[\\u4e00-\\u9fa5].*");
        boolean hasNumber = str.matches(".*[0-9].*");
        return hasChinese || hasNumber;
    }

    public static String escapeHtml(String text) {
        if (text == null) return null;
        return text
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace("\"", "&quot;")
                .replace("'", "&#x27;");
    }

    private String fontPath(String defaltFile) {

        if (StringUtils.isBlank(defaltFile)) {
            //String classPath = PdfAppSvc.class.getClassLoader().getResource(".").getPath();
            String createPath = classPath + "font_simsun.ttc"; //这个文件是专门处理中文的
            //log.info("===当前文件位置:{}",createPath);
            return createPath;
        }
        return defaltFile;
    }






    /**
     * 从jar包中复制到web服务器上
     */
    public void init() {
        PathMatchingResourcePatternResolver patternResolver = new PathMatchingResourcePatternResolver();
        try {
            Resource[] resources = patternResolver.getResources("classpath:fonts/simsun.ttc");
            //log.info("====中文字体文件数组:{}", JSONUtil.toJsonStr(resources));
            //读取文件
            InputStream in = new BufferedInputStream(resources[0].getInputStream());
            BufferedOutputStream out = new BufferedOutputStream(new FileOutputStream(fontPath(null)));
            //写文件
            int b;
            while ((b = in.read()) != -1) {
                out.write(b);
            }
            in.close();
            out.close();
            //new File(classPath + save).mkdirs();
            new File(classPath + imageFolder2007).mkdirs();
            new File(classPath + imageFolder2003).mkdirs();

        } catch (IOException e) {
            logger.error(e.getMessage(), e);
        }
    }

    public static void main(String[] args) {
        // 原始文本包含 &nbsp;
        String html = "逻辑开发&nbsp;";
        // 还原为 Unicode 字符
        String unescaped = StringEscapeUtils.unescapeHtml4(html);
        System.out.println(unescaped); // 输出：逻辑开发\u00A0（可见一个空格，实际为非断行空格）
    }
    public static String getDecodedFilePath(String encodedPath) {
        try {
            // 解码路径
            String decodedPath = URLDecoder.decode(encodedPath, String.valueOf(StandardCharsets.UTF_8));
            return decodedPath.replace("%23", "#");
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
