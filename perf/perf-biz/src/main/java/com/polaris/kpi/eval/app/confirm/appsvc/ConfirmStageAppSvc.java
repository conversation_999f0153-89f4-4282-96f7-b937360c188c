package com.polaris.kpi.eval.app.confirm.appsvc;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.com.polaris.kpi.company.GlobalConfEnum;
import cn.com.polaris.kpi.company.MsgSceneEnum;
import cn.com.polaris.kpi.company.TenantSysConf;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONUtil;
import com.perf.www.common.em.CompanyMsgActionEnum;
import com.polaris.acl.dept.pojo.CompanyPo;
import com.polaris.acl.dept.repository.DeptEmpDao;
import com.polaris.acl.msg.face.MsgAcl;
import com.polaris.kpi.ExecutorEnum;
import com.polaris.kpi.ask.domain.acl.AskEvalAcl;
import com.polaris.kpi.eval.app.confirm.dto.ConfirmEditItemCmd;
import com.polaris.kpi.eval.app.confirm.dto.OnlyConfirmtemCmd;
import com.polaris.kpi.eval.app.stage.appsvc.StageFinders;
import com.polaris.kpi.eval.domain.TaskEvalContext;
import com.polaris.kpi.eval.domain.confirm.dmsvc.*;
import com.polaris.kpi.eval.domain.confirm.entity.AcceptSumbitRs;
import com.polaris.kpi.eval.domain.confirm.entity.ConfirmEmpEval;
import com.polaris.kpi.eval.domain.confirm.entity.ConfirmTaskFlow;
import com.polaris.kpi.eval.domain.confirm.event.AdminConfirmConfEdited;
import com.polaris.kpi.eval.domain.confirm.repo.BatchLoadConfirmFlowRepo;
import com.polaris.kpi.eval.domain.confirm.repo.ConfirmConfRepo;
import com.polaris.kpi.eval.domain.confirm.repo.ConfirmEvalRuleRepo;
import com.polaris.kpi.eval.domain.confirm.repo.ExtDataSyncRepo;
import com.polaris.kpi.eval.domain.score.dmsvc.ClearScorerSummaryToDoDmSvc;
import com.polaris.kpi.eval.domain.stage.dmsvc.BaseAuditNodeChecker;
import com.polaris.kpi.eval.domain.stage.dmsvc.EmpEvalRuleDmSvc;
import com.polaris.kpi.eval.domain.stage.dmsvc.StageConfDiffLogDmSvc;
import com.polaris.kpi.eval.domain.stage.event.BatchAffirmStageStart;
import com.polaris.kpi.eval.domain.stage.repo.AutoValidEmpNameFinder;
import com.polaris.kpi.eval.domain.stage.repo.ResetEmpNameFinder;
import com.polaris.kpi.eval.domain.task.dmsvc.ClearTodoDmsSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.ExplainEvalScorerDmSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.SkipAuditNodeDmSvc;
import com.polaris.kpi.eval.domain.task.entity.EvalKpi;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.OperationLog;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTaskDiff;
import com.polaris.kpi.eval.domain.task.entity.empeval.*;
import com.polaris.kpi.eval.domain.task.entity.flow.LevelAuditFlow;
import com.polaris.kpi.eval.domain.task.entity.msg.MsgTodoAggregate;
import com.polaris.kpi.eval.domain.task.event.BatchStageEndedV2;
import com.polaris.kpi.eval.domain.task.event.msg.BatchMsgTodoAggregateEvent;
import com.polaris.kpi.eval.domain.task.ext.IOkrAclSvc;
import com.polaris.kpi.eval.domain.task.repo.*;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.eval.infr.confirm.dao.ConfirmContextDao;
import com.polaris.kpi.eval.infr.confirm.ppojo.ConfirmEmpEvalPo;
import com.polaris.kpi.eval.infr.task.dao.EmpEvalDao;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.ListScoreResultWrap;
import com.polaris.kpi.org.domain.company.repo.TenantSysConfRepo;
import com.polaris.kpi.org.domain.dept.dmsvc.EmpParser;
import com.polaris.kpi.org.domain.dept.dmsvc.ExtEmpParser;
import com.polaris.kpi.org.domain.dept.dmsvc.LevelLeaderParser;
import com.polaris.kpi.org.domain.dept.dmsvc.RoleParser;
import com.polaris.kpi.org.domain.dept.entity.Company;
import com.polaris.kpi.org.infr.company.dao.TenantSysConfDao;
import com.polaris.kpi.org.infr.dept.dao.KpiOrgDao;
import com.polaris.sdk.type.AuditEnum;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.MapWrap;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/***
 * 考核流程状态阶段变化处理应用服务
 */
@Slf4j
@Service
public class ConfirmStageAppSvc extends BaseConfirmStageAppSvc {

    @Autowired
    private TaskUserRepo userRepo;
    @Autowired
    private KpiOrgDao kpiOrgDao;
    @Autowired
    private ConfirmEvalRuleRepo confirmEvalRuleRepo;
    @Autowired
    private BatchLoadConfirmFlowRepo repo;
    @Autowired
    private BatchUpdateBaseEvalUser evalUserUpdater;
    @Autowired
    private ConfirmContextDao confirmContextDao;
    @Autowired
    private ConfirmConfRepo confirmConfRepo;
    @Autowired
    private AutoValidEmpNameFinder autoFinder;
    @Autowired
    private ResetEmpNameFinder resetFinder;
    @Autowired
    private AdminTaskRepo adminTaskRepo;
    @Autowired
    private BatchUpdateEvalErrorStatus errorStatus;
    @Autowired
    private ScorerSummaryTodoRepo summaryTodoRepo;
    @Autowired
    private MsgAcl msgAcl;
    @Autowired
    private AskEvalAcl askEvalAcl;
    @Autowired
    private DeptEmpDao deptEmpDao;
    @Autowired
    private IOkrAclSvc evalOkrAcl;
    @Autowired
    private TenantSysConfRepo tenantSysConfRepo;
    @Autowired
    private EmpEvalDao empEvalDao;
    @Autowired
    private ExtDataSyncRepo extDataSyncRepo;
    @Autowired
    private TenantSysConfDao confDao;
    @Autowired
    private EmpEvalScorerRepo empEvalScorerRepo;

    @EventListener
    @Async(ExecutorEnum.high)
    public void syncAdminConfirmConf(AdminConfirmConfEdited event) {
        event.start();
        TenantId tenantId = new TenantId(event.getTenantId());
        RoleParser roleParser = new RoleParser(tenantId, StageFinders.leaderFinder);
        LevelLeaderParser leaderParser = new LevelLeaderParser(tenantId, StageFinders.deptLeaderFinder, kpiOrgDao.openIgnoreVacancyManager(event.getTenantId()));
        ExtEmpParser extEmpParser = new ExtEmpParser(tenantId, StageFinders.empFinder, StageFinders.extEmpFinder);
        EmpParser empParser = new EmpParser(empFinder, tenantId);
        //比较变更结果生成日志记录

        StageConfDiffLogDmSvc logDm = new StageConfDiffLogDmSvc(empParser, autoFinder, resetFinder);
        AdminTaskDiff diff = logDm.diffAdminTaskOperation(event.getTenantId(), event.getTaskId(), event.getNeedResetUserIds(),
                event.getChangedStage(), event.getBeforeTask(), event.getAfterTask(), event.getOpEmpId(), event.getOpAdminType());
        if (diff == null) {
            return;
        }
        txw.runTran(() -> logDm.saveDiffLog(adminTaskRepo));
        //1 处理用户指定重置的成员,重置到确认中
        this.resetMember2Confirm(event, tenantId, roleParser, leaderParser, extEmpParser, empParser);
        //自动生效的,只需要同步考核表配置
        this.onlySynConfirmConf(event, tenantId, roleParser, leaderParser, extEmpParser, empParser);
    }

    private void onlySynConfirmConf(AdminConfirmConfEdited event, TenantId tenantId, RoleParser roleParser, LevelLeaderParser leaderParser, ExtEmpParser extEmpParser, EmpParser empParser) {
        List<EmpEval> empEvals;
        int pageNo = 1;
        while (CollUtil.isNotEmpty(empEvals = confirmContextDao.pagedBeforeConfirm(tenantId, event.getOpEmpId(), event.getTaskId(), pageNo++, event.getBeforeTask().getPerformanceType()))) {
            //1.2 同步考核表的确认配置及流程
            SynConfirmConfDmSvc confDmSvc = new SynConfirmConfDmSvc(tenantId, event.getOpEmpId(), empEvals)
                    .initRepo(roleParser, leaderParser, extEmpParser, StageFinders.deptFinder);
            confDmSvc.synConfirmConf(event.getAfterTask().getConfirmTask());
            List<EmpEval> finalEmpEvals1 = empEvals;
            txw.runTran(() -> confirmConfRepo.updateConfirmConf(tenantId, event.getOpEmpId(), finalEmpEvals1));
            //检测异常信息
            this.checkError(tenantId, empParser, empEvals);
        }
    }

    private void resetMember2Confirm(AdminConfirmConfEdited event, TenantId tenantId, RoleParser roleParser, LevelLeaderParser leaderParser, ExtEmpParser extEmpParser, EmpParser empParser) {
        List<EmpEval> empEvals;
        Integer pageNo = 1;
        while (!event.resetNone() && CollUtil.isNotEmpty(empEvals = confirmContextDao.pagedAfterConfirm(tenantId, event.getOpEmpId(), event.getTaskId(),
                event.getNeedResetUserIds(), pageNo++, event.getBeforeTask().getPerformanceType()))) {  // 1.1 分页处理while { 分页加载重置考核成员
            ResetToConfirmDmSvc resetSvc = new ResetToConfirmDmSvc(tenantId, event.getOpEmpId(), empEvals);
            resetSvc.resetStage();

            //1.2 同步考核表的确认配置及流程
            SynConfirmConfDmSvc confDmSvc = new SynConfirmConfDmSvc(tenantId, event.getOpEmpId(), empEvals)
                    .initRepo(roleParser, leaderParser, extEmpParser, StageFinders.deptFinder);
            confDmSvc.synConfirmConf(event.getAfterTask().getConfirmTask());
            List<EmpEval> finalEmpEvals = empEvals;
            txw.runTran(() -> {
                resetSvc.saveReset2ConfirmStage(contextRepo);// 1.2 清空对应已完成阶段:确认及后确认后阶段数据.及待办saveReset2ConfirmStage
                new ClearTodoDmsSvc(centerRepo).clear(tenantId, resetSvc.getResetedIds(), resetSvc.getClearScoreRsScenes());
                new ClearScorerSummaryToDoDmSvc(summaryTodoRepo, msgAcl).clear(resetSvc.getResetedIds(), tenantId.getId(), event.getTaskId());
                opLogDao.batchSaveLogDomain(resetSvc.getLogs());//1.3 记录操作日志
                confirmConfRepo.updateConfirmConf(tenantId, event.getOpEmpId(), finalEmpEvals);//保存确认配置
            });
            resetSvc.startConfirmStage(contextRepo);  // 1.3 启动确认阶段
            this.checkError(tenantId, empParser, empEvals);
        }
    }

    private void checkError(TenantId tenantId, EmpParser empParser, List<EmpEval> empEvals) {
        ConfirmTaskCheckDmSvc checker = new ConfirmTaskCheckDmSvc(new BaseAuditNodeChecker(tenantId, empParser));
        List<EvalUser> evalUsers = empEvals.stream().map(eval -> eval.getEval()).collect(Collectors.toList());
        checker.checkRuleErr(evalUsers);
        txw.runTran(() -> errorStatus.batchUpdateEvalErrorStatus(tenantId, evalUsers));
    }


    @EventListener
    @Async(ExecutorEnum.high)
    // 指标确认阶段启动
    public void handAffirmStageStart(BatchAffirmStageStart event) {
        event.start();
        final TalentStatus curStatus = TalentStatus.CONFIRMING;
        TenantId tenantId = new TenantId(event.getTenantId());
        TaskEvalContext evalContext = event.getEvalContext();
        Collection<String> taskUserIds = evalContext.evalIds();

        MapWrap<String, LevelAuditFlow> flowMap = repo.listConfirmAuditFlowOld(tenantId, taskUserIds);
        TaskEvalContext endCtx = evalContext.clone();
        List<OperationLog> logs = new ArrayList<>();
        BatchMsgTodoAggregateEvent batchMsg = new BatchMsgTodoAggregateEvent(tenantId, event.getOpEmpId());
        EmpParser empParser = new EmpParser(empFinder, tenantId);
        List<EvalUser> upUsers = new ArrayList<>();
        for (EmpEval data : evalContext.getEvals()) {
            final AsAdminTaskRule cycleTask = data.getRule();
            final EvalUser taskUser = data.getEval();
            if (!cycleTask.needAffirm(taskUser) || taskUser.modifyItemAuditIsEmpty()) {//不需要确认或没有审核人跳过此阶段
                endCtx.addMember(data);
                continue;
            }
            //确认人
            LevelAuditFlow flow = flowMap.get(taskUser.getId());
            flow.dispatchFirst();
            log.debug("handAffirmStageStart.flow:{}", JSONUtil.toJsonStr(flow));
            //没有确认人,自动跳过
            if (CollUtil.isEmpty(flow.curLevelRs())) {
                endCtx.addMember(data);
                continue;
            }
            SkipAuditNodeDmSvc nodeDmSvc = new SkipAuditNodeDmSvc(taskUser.getCompanyId().getId(), taskUser.getId(), flow, EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT.getScene());
            nodeDmSvc.setRepo(userRepo);
            txw.runTran(() -> nodeDmSvc.startSkip(1)); //后续还要改造成批量处理, 本次周迭代间上来不及
            logs.addAll(nodeDmSvc.getLogs());
            //当前阶段结束
            if (nodeDmSvc.isEnd()) {
                endCtx.addMember(data);
                continue;
            }
            //需要确认
            List<KpiEmp> kpiEmps = empParser.getEmps(nodeDmSvc.filterRepeatRecEmpIds());
            taskUser.curStatus(curStatus, kpiEmps);
            taskUser.initConfirmDeadLine(cycleTask.limitConfirmDays());
            upUsers.add(taskUser);//改成批量提交
            MsgSceneEnum msgScene = CollUtil.isEmpty(flow.nextAudits()) ? MsgSceneEnum.TASK_CONFIRM : MsgSceneEnum.TASK_ENACT;
            // 给确认者发通知与消息
            MsgTodoAggregate msgTodoAggregate = new MsgTodoAggregate(taskUser.getCompanyId(), taskUser.getTaskId(), cycleTask.taskName(), taskUser.getEmpId(), taskUser.getId()).useScene(msgScene, CompanyMsgActionEnum.CONFIRM).addExtTempValue("evalEmpName", taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName()).addExtTempValue("deadLineDate", taskUser.joinDeadLineStr(TalentStatus.CONFIRMING.getStatus())).addTodoItem("msg.task.emp", taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName()).sendExtMsg().addCenterMsg().sendExtTodo().addRecEmpId(nodeDmSvc.getRecEmpId());
            batchMsg.addEvent(msgTodoAggregate);
        }
        txw.runTran(() -> {
            evalUserUpdater.updateBaseEvalUser(tenantId, upUsers);
            opLogDao.batchSaveLogDomain(logs);
        });
        batchMsg.publish();
        if (CollUtil.isNotEmpty(endCtx.getEvals())) {
            new BatchStageEndedV2(tenantId, event.getOpEmpId(), Arrays.asList(endCtx), curStatus).publish();
        }
    }

    public void reject(TenantId companyId, String opEmpId, String taskUserId, String rejectReason) {
        TaskEvalContext context = contextRepo.listStartEvalContext(companyId, Arrays.asList(taskUserId), 0).get(0);
        ConfirmTaskFlow confirmFlow = confirmFlowRepo.getConfirmFlow(context.getEvals().get(0).getRule().getConfirmTask(), companyId, taskUserId);
        confirmFlow.reject2Up(opEmpId);


    }

    public void skipConfirmItem(OnlyConfirmtemCmd cmd) {
        TaskEvalContext context = contextRepo.listStartEvalContext(cmd.getTenantId(), Arrays.asList(cmd.getTaskUserId()), 0).get(0);
        ConfirmTaskFlow confirmFlow = confirmFlowRepo.getConfirmFlow(context.getEvals().get(0).getRule().getConfirmTask(), cmd.getTenantId(), cmd.getTaskUserId());
        //兼容逻辑,多级人员重复时,可能有问题
        Integer approveOrder = cmd.getApproveOrder() == null ? confirmFlow.getApproveOrder(cmd.getConfirmEmpId()) : cmd.getApproveOrder();
        cmd.setApproveOrder(approveOrder);
        AcceptSumbitRs acceptSumbitRs = super.acceptPass(cmd, confirmFlow);
        if (!acceptSumbitRs.getNodeFinished()) {//当前环节未全部完成，不进行分发,修改提交的责任人
            super.onlyUpReviewers(cmd.getTenantId(), acceptSumbitRs, context.getEval(cmd.getTaskUserId()).getEval());
            return;
        }
        //流程组件:派发下级, 发送组件:发送下级的新待办
        super.dispatchNext(cmd, confirmFlow, context, acceptSumbitRs.getNode().getApprovalOrder());
    }

    public void onlyConfirmItem(OnlyConfirmtemCmd cmd) {
        TaskEvalContext context = contextRepo.listStartEvalContext(cmd.getTenantId(), Arrays.asList(cmd.getTaskUserId()), 0).get(0);
        ConfirmTaskFlow confirmFlow = confirmFlowRepo.getConfirmFlow(context.getEvals().get(0).getRule().getConfirmTask(), cmd.getTenantId(), cmd.getTaskUserId());
        AcceptSumbitRs acceptSumbitRs = super.acceptPass(cmd, confirmFlow);
        if (!acceptSumbitRs.getNodeFinished()) {//当前环节未全部完成，不进行分发
            super.onlyUpReviewers(cmd.getTenantId(), acceptSumbitRs, context.getEval(cmd.getTaskUserId()).getEval());
            return;
        }
        //流程组件:派发下级, 发送组件:发送下级的新待办
        super.dispatchNext(cmd, confirmFlow, context, acceptSumbitRs.getNode().getApprovalOrder());
    }

    public void confirmEditItem(ConfirmEditItemCmd cmd) {
        TenantSysConf openConfirmOnInd = confDao.findTenantConf(cmd.getTenantId().getId(), GlobalConfEnum.CONFIRM_ON_INDICATOR.getConfCode(), 0);
        TaskEvalContext context = contextRepo.listStartEvalContext(cmd.getTenantId(), Arrays.asList(cmd.getTaskUserId()), 0).get(0);
        EmpEvalMerge evalRule = context.getEvals().get(0).getRule();
        ConfirmTaskFlow confirmFlow = confirmFlowRepo.getConfirmFlow(evalRule.getConfirmTask(), cmd.getTenantId(), cmd.getTaskUserId());
        AcceptSumbitRs acceptSumbitRs = super.acceptPass(cmd, confirmFlow);//流程组件:受理提交,清理组件:清理当前级待办.
        boolean indexCofMdfEmp = tenantSysConfRepo.isOpen(cmd.getTenantId().getId(), GlobalConfEnum.INDEX_COF_MDF_EMP.getConfCode());
        //考核表组件:更新考核表,导入okr ,处理360,生成评分规则,生成,TODO 深度拆分 评分流程应给到评分阶段订阅收领域事件处理. 这次先不改
        EmpEvalRuleDmSvc ruleDmSvc = new EmpEvalRuleDmSvc(indexCofMdfEmp, evalRule, context.getEvals().get(0).getEval(), cmd.getKpiTypes(), cmd.bindOkrTypes());
        ruleDmSvc.processEvalRule(openConfirmOnInd, cmd.getApproveOrder(), cmd.getOpEmpId().getId(), cmd.delAsk360EvalIds());
        ruleDmSvc.processItems(cmd.getOpEmpId(), cmd.getS3SelfRater(), cmd.getS3PeerRater(), cmd.getS3SubRater(), cmd.getS3SuperRater(), cmd.getS3AppointRater());
        askEvalAcl.delAsk360EvalOpt(cmd.getTenantId().getId(), cmd.getTaskUserId(), cmd.delAsk360EvalIds(), cmd.getOpEmpId().getId());
        txw.runTran(() -> ruleDmSvc.saveEvalRule(cmd.getOpEmpId().getId(), confirmEvalRuleRepo));
        ExplainEvalScorerDmSvc dmSvc = new ExplainEvalScorerDmSvc(ruleDmSvc.getUser(), ruleDmSvc.getEvalRule(), cmd.getTenantId(), cmd.getOpEmpId().getId());
        dmSvc.explainEvalScorer();//异步生成评价关系
        txw.runAsyn( MDC.get("tid"), () ->{
            empEvalScorerRepo.saveBatchEmpEvalScorer(cmd.getTenantId().getId(), cmd.getOpEmpId().getId(), cmd.getTaskUserId(), dmSvc.getScorers().getDatas());
        });
        confirmFlow.refreshAuditIndFlow(ruleDmSvc.getOnIndAudits());
        CompanyPo company = deptEmpDao.getTenant(cmd.getTenantId());
        for (EvalKpi okrItem : ruleDmSvc.getOkrItems()) {
            evalOkrAcl.addRefAction(company.getDingCorpId(), okrItem.getRefOkr().getActionId(), okrItem.getKpiTypeId());
        }
        if (!acceptSumbitRs.getNodeFinished()) {//当前环节未全部完成，不进行分发
            super.onlyUpReviewers(cmd.getTenantId(), acceptSumbitRs, context.getEval(cmd.getTaskUserId()).getEval());
            return;
        }
        //流程组件:派发下级, 发送组件:发送下级的新待办
        super.dispatchNext(cmd, confirmFlow, context, acceptSumbitRs.getNode().getApprovalOrder());
    }

    /**
     * @param tenantId
     * @param taskUserId
     * @param opEmpId
     * @return
     * @see com.polaris.kpi.eval.app.task.appsvc.EmpEvalAppSvc#empEvalForConfirm(TenantId, String, String)
     */
    public ConfirmEmpEvalPo empEvalForConfirm(TenantId tenantId, String taskUserId, String opEmpId) {
        TaskEvalContext context = contextRepo.listStartEvalContext(tenantId, Arrays.asList(taskUserId), 0).get(0);
        EmpEval empEval = context.getEvals().get(0);
        ConfirmEmpEval rule = Convert.convert(ConfirmEmpEval.class, empEval.getRule());
        rule.setKpiTypes(empEval.getRule().getKpiTypes());
        EvalUser taskUser = empEval.getEval();
        boolean indexCofMdfEmp = tenantSysConfRepo.isOpen(tenantId.getId(), GlobalConfEnum.INDEX_COF_MDF_EMP.getConfCode());
        String scene = Arrays.asList(TalentStatus.CONFIRMED.getStatus(), TalentStatus.CHANGING.getStatus()).contains(taskUser.getTaskStatus()) ?
                AuditEnum.EDIT_EXE_INDI.getScene() :
                AuditEnum.CONFIRM_TASK.getScene();
        List<EmpEvalKpiType> types = confirmContextDao.getConfirmCache(rule, tenantId, scene, taskUserId, taskUser.getEmpId(), indexCofMdfEmp);
        int typeFromCache = 0; //是不是缓存里面的指标
        if (CollUtil.isNotEmpty(types)) {
            rule.setKpiTypes(new KpiListWrap(types));
            typeFromCache = 1;
        }
        ListScoreResultWrap confirmRs = empEvalDao.curScoreRss(tenantId.getId(), opEmpId, taskUserId, AuditEnum.CONFIRM_TASK.getScene());
        rule.matchCurOrder(confirmRs.curOrder(), opEmpId, confirmRs.getConfirmApproveIndIds());

        Company company = deptEmpDao.getCompany(tenantId);
        new OkrLoadDmSvc(evalOkrAcl, company).loadOkrIf(tenantId, rule);
        new ConfirmExtDataDmSvc(rule, taskUser, context.getTask()).buildConfirmExtData(extDataSyncRepo);

        ConfirmEmpEvalPo convert = Convert.convert(ConfirmEmpEvalPo.class, rule);
        convert.setKpiTypes(rule.getKpiTypes().getDatas());
        convert.setTaskUserId(rule.getEmpEvalId());
        convert.setTaskStatus(taskUser.getTaskStatus());
        convert.setTypeFromCache(typeFromCache);
        return convert;
    }


    public void fixEvalRule(TenantId tenantId, String opEmpId, List<String> taskUserIds) {
        TenantSysConf openConfirmOnInd = confDao.findTenantConf(tenantId.getId(), GlobalConfEnum.CONFIRM_ON_INDICATOR.getConfCode(), 0);
        TaskEvalContext context = contextRepo.listStartEvalContext(tenantId, taskUserIds, 0).get(0);
        boolean indexCofMdfEmp = tenantSysConfRepo.isOpen(tenantId.getId(), GlobalConfEnum.INDEX_COF_MDF_EMP.getConfCode());
        for (EmpEval eval : context.getEvals()) {
            EmpEvalMerge evalRule = eval.getRule();
            if (evalRule.isCustom()) {
                for (EmpEvalKpiType data : evalRule.getKpiTypes().getDatas()) {
                    data.closeRater();
                }
            }
            EmpEvalRuleDmSvc ruleDmSvc = new EmpEvalRuleDmSvc(indexCofMdfEmp, evalRule, eval.getEval(), evalRule.getKpiTypes().getDatas(), null);
            ruleDmSvc.processEvalRule(openConfirmOnInd, null, opEmpId, null);
            ruleDmSvc.processItems(new EmpId(opEmpId), evalRule.getS3SelfRater(), evalRule.getS3PeerRater(), evalRule.getS3SubRater(), evalRule.getS3SuperRater(), evalRule.getS3AppointRater());
            txw.runTran(() -> ruleDmSvc.saveEvalRule(opEmpId, confirmEvalRuleRepo));
            CompanyPo company = deptEmpDao.getTenant(tenantId);
            for (EvalKpi okrItem : ruleDmSvc.getOkrItems()) {
                evalOkrAcl.addRefAction(company.getDingCorpId(), okrItem.getRefOkr().getActionId(), okrItem.getKpiTypeId());
            }
        }
    }
}
