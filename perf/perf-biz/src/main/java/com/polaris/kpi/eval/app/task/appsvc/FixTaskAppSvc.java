package com.polaris.kpi.eval.app.task.appsvc;


import cn.com.polaris.kpi.EmpStaff;
import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.com.polaris.kpi.company.MsgSceneEnum;
import cn.com.polaris.kpi.dept.DeptLeader;
import cn.com.polaris.kpi.dept.Leader;
import cn.com.polaris.kpi.dept.LevelLeader;
import cn.com.polaris.kpi.eval.ItemCustomFieldValue;
import cn.com.polaris.kpi.eval.KpiItemUsedField;
import cn.com.polaris.kpi.eval.Name;
import cn.com.polaris.kpi.eval.Rater;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.perf.www.common.em.CompanyMsgActionEnum;
import com.polaris.acl.dept.domain.org.EmpOrganization;
import com.polaris.acl.dept.repository.DeptEmpDao;
import com.polaris.acl.msg.domain.FinishWorkReq;
import com.polaris.acl.msg.face.MsgAcl;
import com.polaris.kpi.cache.domain.entity.CompanyCacheInfo;
import com.polaris.kpi.cache.domain.entity.ConfirmTypeCache;
import com.polaris.kpi.cache.domain.repo.CompanyCacheRepo;
import com.polaris.kpi.cache.infr.dao.CompanyCacheDao;
import com.polaris.kpi.eval.app.TransactionWrap;
import com.polaris.kpi.eval.domain.task.dmsvc.EmpEvalDmSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.ResultAuditDmSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.ResultAuditFlowDmSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.SkipResultAuditDmSvc;
import com.polaris.kpi.eval.domain.task.entity.CycleEval;
import com.polaris.kpi.eval.domain.task.entity.EvalAudit;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalRule;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.AuditResultConf;
import com.polaris.kpi.eval.domain.task.entity.flow.LevelAuditFlow;
import com.polaris.kpi.eval.domain.task.entity.msg.MsgTodoAggregate;
import com.polaris.kpi.eval.domain.task.event.ThisStageEnded;
import com.polaris.kpi.eval.domain.task.event.msg.CancelTodoEvent;
import com.polaris.kpi.eval.domain.task.event.talent.ResultCollectMsgTodoEvent;
import com.polaris.kpi.eval.domain.task.event.talent.scoring.ChainNodeEnd;
import com.polaris.kpi.eval.domain.task.repo.*;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.eval.infr.task.dao.*;
import com.polaris.kpi.eval.infr.task.ppojo.CancelTodoPo;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskBaseDo;
import com.polaris.kpi.org.domain.dept.dmsvc.ExtEmpParser;
import com.polaris.kpi.org.domain.dept.dmsvc.LevelLeaderParser;
import com.polaris.kpi.org.domain.dept.entity.CompanyMsgCenter;
import com.polaris.kpi.org.domain.dept.repo.*;
import com.polaris.kpi.org.domain.emp.repo.KpiEmpRepo;
import com.polaris.kpi.org.infr.company.dao.CompanyMsgCenterDao;
import com.polaris.kpi.org.infr.emp.dao.KpiEmpDao;
import com.polaris.kpi.setting.domain.entity.ResultAuditFlow;
import com.polaris.kpi.setting.domain.entity.ResultAuditFlowNodeRater;
import com.polaris.kpi.setting.domain.entity.ResultAuditFlowUser;
import com.polaris.kpi.setting.domain.entity.TaskResultAuditSummary;
import com.polaris.kpi.setting.domain.repo.ResultAuditFlowRepo;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import com.polaris.sdk.type.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge.*;

@Service
@Slf4j
public class FixTaskAppSvc {
    @Autowired
    private TaskAuditDao taskAuditDao;
    @Autowired
    private TaskUserRepo taskUserRepo;
    @Autowired
    private TaskAuditRepo taskAuditRepo;
    @Autowired
    private EmpEvalRuleRepo empRuleRepo;
    @Autowired
    private EmpEvalDao empEvalDao;
    @Autowired
    private ResultAuditFlowRepo resultAuditFlowRepo;
    @Autowired
    private AdminTaskDao adminTaskDao;
    @Autowired
    private AdminTaskRepo adminTaskRepo;
    @Autowired
    private EvaluateTaskRepo taskRepo;
    @Autowired
    private KpiEmpDao kpiEmpDao;
    @Autowired
    private OpLogDao opLogDao;
    @Autowired
    private DeptEmpDao deptEmpDao;
    @Autowired
    private TaskUserDao taskUserDao;
    @Autowired
    private ResultAuditFlowRepo auditFlowRepo;
    @Autowired
    private WorkTodoDao workTodoDao;
    @Autowired
    private MsgAcl msgAcl;
    @Autowired
    private CompanyMsgCenterDao centerDao;
    @Autowired
    private TaskKpiItemDao kpiItemDao;
    @Autowired
    private TransactionWrap tx;
    @Autowired
    private CompanyCacheDao cacheDao;
    @Autowired
    private CompanyCacheRepo companyCacheRepo;
    @Autowired
    private DeptLeaderFinder leaderFinder;
    @Autowired
    private RoleEmpFinder roleFinder;
    @Autowired
    private EmpFinder empFinder;
    @Autowired
    private ExtEmpFinder extEmpFinder;
    @Autowired
    private DeptFinder deptFinder;

    @Autowired
    private KpiEmpRepo empRepo;


    public List<EmpStaff> fixListEmpByRoleId(TenantId tenantId, String roleId, String orgId, String empId) {
        List<EmpStaff> empStaffs = empRepo.listEmpByRoleId(tenantId, roleId, orgId, empId);
        return empStaffs;
    }

    public LevelLeader getTestLeader(TenantId tenantId, String empId, String orgId) {
        LevelLeaderParser leaderParser = new LevelLeaderParser(tenantId, leaderFinder,false);
        ExtEmpParser empParser = new ExtEmpParser(tenantId, empFinder, extEmpFinder);

        Rater directLeader = empParser.getDirectLeader(empId);
        LevelLeader levelLeader = leaderParser.getLevelLeader(tenantId, orgId);
        Map<Integer, DeptLeader> levelLeaders = levelLeader.getLevelLeaders();
        Leader dir = new Leader(directLeader.getEmpName(), directLeader.getEmpId());

        if (levelLeaders.get(1) != null && CollUtil.isNotEmpty(levelLeaders.get(1).getLeaders())) {
            levelLeaders.get(1).getLeaders().add(dir);
        } else {
            levelLeaders.put(1, new DeptLeader(null, Arrays.asList(dir)));
        }
        return levelLeader;
    }

    public void fixFinalResultAuditOrder(String companyId, String taskId, String taskUserId, Integer moveUpNum) {
        if (StrUtil.isBlank(taskId) && StrUtil.isBlank(taskUserId)) {
            return;
        }

        List<EvalAudit> evalAudits = taskAuditDao.listResultAll(companyId, taskId, taskUserId);
        if (CollUtil.isEmpty(evalAudits)) {
            return;
        }
        Map<String, List<EvalAudit>> listMap = evalAudits.stream().collect(Collectors.groupingBy(EvalAudit::getTaskUserId));
        listMap.forEach((k, v) -> {
            for (EvalAudit evalAudit : v.stream().sorted(Comparator.comparing(EvalAudit::getApprovalOrder))
                    .collect(Collectors.toList())) {
                if (evalAudit.getApprovalOrder() - moveUpNum <= 0 || evalAudit.getApprovalOrder() - moveUpNum == 1) {
                    continue;
                }
                evalAudit.setApprovalOrder(evalAudit.getApprovalOrder() - moveUpNum);
                taskAuditRepo.updateFinalResultAuditOrder(companyId, evalAudit.getId(), evalAudit.getApprovalOrder());
                taskUserRepo.upFinalResultAuditOrder(companyId, evalAudit.getTaskUserId(), evalAudit.getId(), evalAudit.getApprovalOrder());
            }
        });
    }


    public void fixIndexRaters(String companyId, String taskId, String taskUserIds, String node) {
        if (StrUtil.isBlank(taskId) && StrUtil.isBlank(taskUserIds)) {
            return;
        }
        List<String> idList = StrUtil.splitTrim(taskUserIds, ",");
        if (CollUtil.isEmpty(idList)) {
            idList = empEvalDao.listEmpEvalId(companyId, taskId);
        }
        for (String id : idList) {
            EmpEvalMerge empEvalRule = empRuleRepo.getEmpEvalMerge(new TenantId(companyId), id, type | item | itemRule);
            if (Objects.isNull(empEvalRule)) {
                continue;
            }
            empEvalRule.refreshIndexRaters(node);
            empRuleRepo.updateEmpEvalRule(empEvalRule);
        }
    }


    @Transactional
    public void initializeFlowSummary(String companyId, String taskIds) {
        List<String> taskIdList = StrUtil.splitTrim(taskIds, ",");
        if (CollUtil.isEmpty(taskIdList)) {
            taskIdList = adminTaskDao.listOpenUnifyResultAuditTask(companyId);
        }
        if (CollUtil.isEmpty(taskIdList)) {
            return;
        }
        for (String taskId : taskIdList) {
            List<ResultAuditFlowUser> flowUsers = resultAuditFlowRepo.listFlowUserByTaskId(companyId, taskId);
            if (CollUtil.isEmpty(flowUsers)) {
                continue;
            }
            Map<String, List<ResultAuditFlowUser>> userGroupMap = flowUsers.stream().collect(Collectors.groupingBy(u -> u.getTaskUserId()));
            userGroupMap.forEach((k, v) -> {
                EvalUser evalUser = taskUserRepo.getBaseTaskUserAndRule(new TenantId(companyId), k);
                resultAuditFlowRepo.saveAuditFlowV2(evalUser, v);
            });
            ResultAuditFlowDmSvc flowDmSvc = new ResultAuditFlowDmSvc(companyId, taskId, null);
            flowDmSvc.setResultAuditFlowRepo(resultAuditFlowRepo);
            flowDmSvc.loadResultAuditFlow(resultAuditFlowRepo);
            flowDmSvc.initNewResultAuditFlow(flowUsers);
            resultAuditFlowRepo.saveTaskResultAuditSummary(flowDmSvc.getTaskResultAuditSummaries());
        }
    }


    public void nextDispatchUnifyResultAudit(TenantId tenantId, String taskUserId, Integer level) {
        EvalUser empEval = taskUserRepo.getTaskUser(tenantId, taskUserId);
        AdminTask adminTask = adminTaskRepo.getAdminTask(tenantId, empEval.getTaskId());
        SkipResultAuditDmSvc skipDmSvc = new SkipResultAuditDmSvc(tenantId.getId(), adminTask.getId(), empEval.getId(), null, EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene(), null);
        skipDmSvc.accRepo(taskUserRepo, resultAuditFlowRepo);
        skipDmSvc.startSkip(level);
        if (skipDmSvc.isEnd()) {
            /**计算关联得分*/
            //taskDao.renewalRefEval(tenantId.getId(), empEval.getId());
            CycleEval cycleEval = taskRepo.getMergeCycleEval(tenantId, empEval.getId());
            new ThisStageEnded(cycleEval, empEval, TalentStatus.RESULTS_AUDITING).publish();
            return;
        }
        // 更新负责人信息
        List<KpiEmp> reviewers = kpiEmpDao.listByEmp(empEval.getCompanyId(), skipDmSvc.getRecEmpId());
        empEval.reviewers(reviewers);
        taskUserRepo.updateTaskUser(empEval);
        opLogDao.batchSaveLogDomain(skipDmSvc.getLogs());
        //统一校准
        ResultAuditDmSvc resultAuditDmSvc = new ResultAuditDmSvc(empEval, skipDmSvc.getCurLevel(), empEval.getCompanyId().getId(), empEval.getTaskId());
        resultAuditDmSvc.setRepo(resultAuditFlowRepo);
        for (String adminEmpId : skipDmSvc.getRecEmpId()) {
            resultAuditDmSvc.executeStart(adminEmpId);
            resultAuditDmSvc.refreshSummaryFinishCnt(adminEmpId, skipDmSvc.getCurLevel());
        }
        if (resultAuditDmSvc.needSend()) {
            new ResultCollectMsgTodoEvent(empEval.getCompanyId().getId(), resultAuditDmSvc.getSendRater(), adminTask).publish();
        }
    }


    public void sendUnifyResultAuditMsg(TenantId tenantId, String taskId, String adminEmpId, Integer level) {
        AdminTask adminTask = adminTaskRepo.getAdminTask(tenantId, taskId);
        TaskResultAuditSummary auditSummary = resultAuditFlowRepo.getSummary(tenantId.getId(), taskId, adminEmpId, level);
        if (Objects.isNull(auditSummary)) {
            return;
        }
        List<ResultAuditFlowNodeRater> sendRater = new ArrayList<>();
        ResultAuditDmSvc resultAuditDmSvc = new ResultAuditDmSvc();
        sendRater.addAll(resultAuditDmSvc.builderSendRater(auditSummary.getRaters()));
        if (CollUtil.isNotEmpty(sendRater)) {
            log.info("====发送汇总通知:{}", JSONUtil.toJsonStr(sendRater));
            new ResultCollectMsgTodoEvent(tenantId.getId(), sendRater, adminTask).publish();
        }
    }

    public void nextDispatchSingleResultAudit(TenantId tenantId, String taskUserId, Integer level, EmpId opEmpId) {
        EvalUser empEval = taskUserRepo.getTaskUser(tenantId, taskUserId);
        //执行流程
        LevelAuditFlow flow = taskUserRepo.loadAuditFlow(tenantId, taskUserId, EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT);
        flow.submitPassAndDispatch(false, opEmpId, level);
        //删除暂存数据
        AdminTask adminTask = adminTaskRepo.getAdminTask(tenantId, empEval.getTaskId());
        //结束当前层级的
        new CancelTodoEvent(tenantId, flow.curLevelEmpIds(), taskUserId, MsgSceneEnum.TASK_RESULT_AUDIT.getType()).fire();
        if (flow.isEnd()) {  //当前阶段结束
            /**计算关联得分*/
            //taskDao.renewalRefEval(tenantId.getId(), empEval.getId());
            taskUserRepo.updateLevelFlow(flow.nextAudits(), flow.curLevelRs(), flow.nextLevelRs());
            CycleEval cycleEval = taskRepo.getMergeCycleEval(tenantId, empEval.getId());
            new ThisStageEnded(cycleEval, empEval, TalentStatus.RESULTS_AUDITING).publish();
            return;
        }

        //跳过
        SkipResultAuditDmSvc skipDmSvc = new SkipResultAuditDmSvc(tenantId.getId(), adminTask.getId(), empEval.getId(), null, EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene(), flow);
        skipDmSvc.accRepo(taskUserRepo, resultAuditFlowRepo);
        skipDmSvc.startSkip(flow.currentLevel() + 1);
        if (skipDmSvc.isEnd()) {
            /**计算关联得分*/
            //taskDao.renewalRefEval(tenantId.getId(), empEval.getId());
            CycleEval cycleEval = taskRepo.getMergeCycleEval(tenantId, empEval.getId());
            new ThisStageEnded(cycleEval, empEval, TalentStatus.RESULTS_AUDITING).publish();
            opLogDao.batchSaveLogDomain(skipDmSvc.getLogs());
            return;
        }
        // 更新负责人信息
        List<KpiEmp> reviewers = kpiEmpDao.listByEmp(empEval.getCompanyId(), skipDmSvc.getRecEmpId());
        empEval.reviewers(reviewers);
        taskUserRepo.updateTaskUser(empEval);
        opLogDao.batchSaveLogDomain(skipDmSvc.getLogs());
        if (skipDmSvc.isSingle()) {
            log.debug(" =============taskUserId():{}", JSONUtil.toJsonStr(taskUserId));
            log.debug(" =============flow.nextLevelEmpIds():{}", JSONUtil.toJsonStr(flow.nextLevelEmpIds()));
            new MsgTodoAggregate(empEval.getCompanyId(), empEval.getTaskId(), new Name(adminTask.getTaskName()), empEval.getEmpId(), empEval.getId())
                    .useScene(MsgSceneEnum.TASK_RESULT_AUDIT, CompanyMsgActionEnum.RESULT_AUDIT).sendExtMsg().addCenterMsg().sendExtTodo()
                    .addExtTempValue("evalEmpName", empEval.getEvalOrgName() == null ? empEval.getEmpName() : empEval.getEvalOrgName())
                    .addExtTempValue("deadLineDate", empEval.joinDeadLineStr(TalentStatus.RESULTS_AUDITING.getStatus()))
                    .addTodoItem("msg.task.emp", empEval.getEvalOrgName() == null ? empEval.getEmpName() : empEval.getEvalOrgName())
                    .addRecEmpId(flow.nextLevelEmpIds()).publish();
            return;
        }
        //统一校准
        ResultAuditDmSvc resultAuditDmSvc = new ResultAuditDmSvc(empEval, skipDmSvc.getCurLevel(), empEval.getCompanyId().getId(), empEval.getTaskId());
        resultAuditDmSvc.setRepo(resultAuditFlowRepo);
        for (String adminEmpId : skipDmSvc.getRecEmpId()) {
            resultAuditDmSvc.executeStart(adminEmpId);
        }
        if (resultAuditDmSvc.needSend()) {
            new ResultCollectMsgTodoEvent(empEval.getCompanyId().getId(), resultAuditDmSvc.getSendRater(), adminTask).publish();
        }
    }

    public void sendTaskResultAuditMsg(String companyId, String taskUserIds, String adminEmpId) {
        if (StrUtil.isBlank(taskUserIds)) {
            return;
        }
        for (String taskUserId : StrUtil.splitTrim(taskUserIds, ",")) {
            EvalUser empEval = taskUserRepo.getTaskUser(new TenantId(companyId), taskUserId);
            AdminTask adminTask = adminTaskRepo.getAdminTask(new TenantId(companyId), empEval.getTaskId());
            //执行流程
            new MsgTodoAggregate(empEval.getCompanyId(), empEval.getTaskId(), new Name(adminTask.getTaskName()), empEval.getEmpId(), empEval.getId())
                    .useScene(MsgSceneEnum.TASK_RESULT_AUDIT, CompanyMsgActionEnum.RESULT_AUDIT).sendExtMsg().addCenterMsg().sendExtTodo()
                    .addExtTempValue("evalEmpName", empEval.getEvalOrgName() == null ? empEval.getEmpName() : empEval.getEvalOrgName())
                    .addExtTempValue("deadLineDate", empEval.joinDeadLineStr(TalentStatus.RESULTS_AUDITING.getStatus()))
                    .addTodoItem("msg.task.emp", empEval.getEvalOrgName() == null ? empEval.getEmpName() : empEval.getEvalOrgName())
                    .addRecEmpId(Arrays.asList(adminEmpId)).publish();
        }
    }

    public void initAtOrgNamePath(String companyId, String taskId) {
        List<EvalUser> evalUsers = taskUserDao.listEvalUser(companyId, taskId, new ArrayList<>());
        if (CollUtil.isEmpty(evalUsers)) {
            return;
        }
        List<String> orgIds = evalUsers.stream().map(e -> StrUtil.isBlank(e.getOrgId()) ? e.getEvalOrgId() : e.getOrgId()).distinct().collect(Collectors.toList());
        List<EmpOrganization> paths = deptEmpDao.listDeptWithNamePath(new TenantId(companyId), orgIds);
        ListWrap<EmpOrganization> pathMap = new ListWrap<>(paths).asMap(path -> path.getOrgId());
        for (EvalUser evalUser : evalUsers) {
            EmpOrganization path = pathMap.mapGet(evalUser.getOrgId());
            if (Objects.isNull(path)) {
                log.debug("未查询到部门:{}", evalUser.getOrgId());
                continue;
            }
            evalUser.acceptOrgPath(path.getOrgCode(), path.getNamePath(), path.getPathHight());
            taskUserRepo.updateTaskUser(evalUser);
        }

    }

    @Transactional
    public void fixDispatchResultChainNode(TenantId tenantId, String taskUserIds, Integer level, EmpId opEmpId) {
        if (StrUtil.isBlank(taskUserIds)) {
            return;
        }
        for (String taskUserId : StrUtil.splitTrim(taskUserIds, ",")) {
            EvalUser empEval = taskUserRepo.getTaskUser(tenantId, taskUserId);
            EmpEvalRule empEvalRule = empEvalDao.getBaseEvalRule(tenantId, taskUserId);
            //重新解析
            EmpEvalDmSvc dmSvc = new EmpEvalDmSvc();
            AuditResultConf auditResult = empEvalRule.getAuditResult();
            if (auditResult.isOpen()) {
                auditResult.getAuditNodes().forEach(node -> {
                    if (StringUtils.isBlank(node.getNode())) {
                        node.setNode("final_result_audit");
                        node.setMultiType(auditResult.getMultiType());
                    }
                    if (node.getApprovalOrder() == level)
                        dmSvc.parseAuditRaters(empEval, auditResult.getAuditNodes(), opEmpId.getId(), auditResult.getAuditSetting());
                });
            }
            List<EvalAudit> auditList = empRuleRepo.saveReusltAudit(empEvalRule, empEval.getId(), empEval.getEmpId(), empEval.getTaskId(), level);
            if (CollUtil.isEmpty(auditList)) {
                continue;
            }
            for (EvalAudit evalAudit : auditList) {
                resultAuditFlowRepo.addResultSummary(tenantId.getId(), evalAudit);
            }
        }
    }


    @Transactional
    public void refreshSummary(TenantId tenantId, String taskId) {
        if (StrUtil.isBlank(taskId)) {
            return;
        }
        ResultAuditDmSvc dmSvc = new ResultAuditDmSvc(null, null, tenantId.getId(), null);
        dmSvc.setRepo(resultAuditFlowRepo);
        dmSvc.refreshSummary(taskId);
        if (dmSvc.needSend()) {
            AdminTask adminTask = adminTaskRepo.getAdminTask(tenantId, taskId);
            new ResultCollectMsgTodoEvent(tenantId.getId(), dmSvc.getSendRater(), adminTask).publish();
        }

    }

    @Transactional
    public void updateShowResultType(TenantId tenantId, String taskUserIds, Integer showResultType) {
        if (Objects.isNull(tenantId) || StrUtil.isBlank(taskUserIds) || Objects.isNull(showResultType)) {
            return;
        }
        if (CollUtil.isEmpty(StrUtil.splitTrim(taskUserIds, ","))) {
            return;
        }
        empRuleRepo.updateShowResultType(tenantId, StrUtil.splitTrim(taskUserIds, ","), showResultType);

    }

    @Transactional
    public void replaceResultAuditEmp(String companyId, String taskUserIds,
                                      String originalAuditEmpId, String newAuditEmpId, Integer level) {
        if (StrUtil.isBlank(companyId) || StrUtil.isBlank(taskUserIds)) {
            return;
        }
        KpiEmp originalEmp = null;
        if (StrUtil.isNotBlank(originalAuditEmpId)) {
            originalEmp = kpiEmpDao.findKpiEmp(new TenantId(companyId), originalAuditEmpId);
        }
        KpiEmp newEmp = null;
        if (StrUtil.isNotBlank(newAuditEmpId)) {
            newEmp = kpiEmpDao.findKpiEmp(new TenantId(companyId), newAuditEmpId);
        }
        List<String> userIds = StrUtil.splitTrim(taskUserIds, ",");
        List<EmpEvalRule> evalRules = empEvalDao.listBaseEvalRule(companyId, userIds);
        if (CollUtil.isNotEmpty(evalRules)) {
            for (EmpEvalRule evalRule : evalRules) {
                evalRule.replaceResultAuditEmp(originalEmp, newEmp, level);
            }
        }
        for (String userId : userIds) {
            EvalUser evalUser = taskUserRepo.getTaskUser(new TenantId(companyId), userId);
            EmpEvalRule rule = evalUser.getEmpEvalRule();
            rule.replaceResultAuditEmp(originalEmp, newEmp, level);
            AuditResultConf after = rule.getAuditResult();
            rule.setEditStatus(rule.getEditStatus() + 4);
            rule.builderRaterSkipType(AuditEnum.FINAL_RESULT_AUDIT.getScene());

            empRuleRepo.editAuditResultConf(rule, evalUser.getTaskId(), evalUser.getEmpId());
            //生成校准审批流程实例
            if (Objects.equals(after.getCollectSendNotify(), 2)) {
                ResultAuditFlow flow = new ResultAuditFlow(companyId, evalUser.getCreatedUser());
                evalUser.setEmpEvalRule(rule);
                auditFlowRepo.saveAuditFlow(flow, evalUser);
                ResultAuditDmSvc dmSvc = new ResultAuditDmSvc(evalUser, null, companyId, evalUser.getCreatedUser());
                dmSvc.setRepo(auditFlowRepo);
                dmSvc.refreshSummary(evalUser.getTaskId(), evalUser.getCreatedUser());
                if (dmSvc.needSend()) {
                    AdminTask adminTask = adminTaskRepo.getAdminTask(new TenantId(companyId), evalUser.getTaskId());
                    new ResultCollectMsgTodoEvent(companyId, dmSvc.getSendRater(), adminTask).publish();
                }
            }
        }
    }

    //重新生成校准审批流程实例
    public void fixResultAuditFlow(String companyId, List<String> taskUserIds) {
        for (String userId : taskUserIds) {
            EvalUser evalUser = taskUserRepo.getTaskUser(new TenantId(companyId), userId);
            EmpEvalRule rule = evalUser.getEmpEvalRule();
            if (Objects.equals(rule.getAuditResult().getCollectSendNotify(), 2)) {
                ResultAuditFlow flow = new ResultAuditFlow(companyId, evalUser.getCreatedUser());
                auditFlowRepo.saveAuditFlow(flow, evalUser);
                ResultAuditDmSvc dmSvc = new ResultAuditDmSvc(evalUser, null, companyId, evalUser.getCreatedUser());
                dmSvc.setRepo(auditFlowRepo);
                dmSvc.refreshSummary(evalUser.getTaskId(), evalUser.getCreatedUser());
                if (dmSvc.needSend()) {
                    AdminTask adminTask = adminTaskRepo.getAdminTask(new TenantId(companyId), evalUser.getTaskId());
                    new ResultCollectMsgTodoEvent(companyId, dmSvc.getSendRater(), adminTask).fire();
                }
            }
        }
    }

    public void fixResultAuditFlowWithTaskIds(String companyId, String taskIds ,String taskUserId){
        if (StrUtil.isBlank(taskIds)) {
            return;
        }
        List<String> taskIdList = StrUtil.splitTrim(taskIds, ",");

        for (String taskId : taskIdList) {
            fixResultAuditFlowWithTaskId(companyId, taskId, taskUserId);
        }
    }


    //重新生成校准审批流程实例
    public void fixResultAuditFlowWithTaskId(String companyId, String taskId ,String taskUserId) {

        if (StrUtil.isNotBlank(taskUserId)) {
            fixEvalUserAuditFlow(companyId, taskUserId);
        }else {
            if (StrUtil.isBlank(taskId)) {
                return;
            }

            PerfEvaluateTaskBaseDo taskByTaskId = adminTaskDao.getByTaskId(companyId, taskId);
            if (StrUtil.isBlank(companyId)){
                companyId =  taskByTaskId.getCompanyId();
            }

            List<String> taskUserIds;
            List<String> beforeEqResultsStage = TalentStatus.beforeEqResultsStage();
            int i = 1;
            while (CollUtil.isNotEmpty(taskUserIds = taskUserDao.listTaskUserIdsByTaskIdAndStatus(companyId, taskId, beforeEqResultsStage, i))) {
                for (String userId : taskUserIds) {
                    fixEvalUserAuditFlow(companyId, userId);
                }
                i++;
            }
        }

        ResultAuditDmSvc dmSvc = new ResultAuditDmSvc(null, null, companyId, null);
        dmSvc.setRepo(resultAuditFlowRepo);
        dmSvc.refreshSummary(taskId);
        if (dmSvc.needSend()) {
            AdminTask adminTask = adminTaskRepo.getAdminTask(new TenantId(companyId), taskId);
            new ResultCollectMsgTodoEvent(companyId, dmSvc.getSendRater(), adminTask).publish();
        }

    }

    private void fixEvalUserAuditFlow(String companyId,String taskUserId){
        EvalUser evalUser = taskUserRepo.getTaskUser(new TenantId(companyId), taskUserId);
        EmpEvalRule rule = evalUser.getEmpEvalRule();
        if (Objects.isNull(rule)) {
            return;
        }
        if (Objects.isNull(rule.getAuditResult())){
            return;
        }
        if (Objects.equals(rule.getAuditResult().getCollectSendNotify(), 2)) {
            ResultAuditFlow flow = new ResultAuditFlow(companyId, evalUser.getCreatedUser());
            auditFlowRepo.fixSaveAuditFlow(flow, evalUser);
        }
    }

    //发送汇总通知,不管是否达到汇总数量条件
    public void sendResultCollectMsgTodo(String companyId, String taskId) {
        ResultAuditDmSvc dmSvc = new ResultAuditDmSvc(null, null, companyId, null);
        dmSvc.setRepo(resultAuditFlowRepo);
        dmSvc.refreshSummary(taskId);//按任务刷新
//        if (dmSvc.needSend()) {不管是否达到汇总数量条件
        AdminTask adminTask = adminTaskRepo.getAdminTask(new TenantId(companyId), taskId);
        new ResultCollectMsgTodoEvent(companyId, dmSvc.getSendRater(), adminTask).fire();
//        }
    }

    @Async
    public void fixCancelDingMsg20250211(String companyId) {
        if (StrUtil.isBlank(companyId)) {
            return;
        }
        int pageNo = 1;
        while (true) {
            List<CancelTodoPo> cancelTodoPos = workTodoDao.fixlistMsgTodoInfo(companyId, pageNo);
            if (CollUtil.isEmpty(cancelTodoPos)) {
                return;
            }
            pageNo++;
            for (CancelTodoPo todoPo : cancelTodoPos) {
                FinishWorkReq req = new FinishWorkReq(new TenantId(companyId), new EmpId(todoPo.getEmpId()), todoPo.getThirdMsgId());
                try {
                    msgAcl.finishTodoWork(req);
                } catch (Exception e) {
                    log.error("完成待办失败:" + e.getMessage(), e);
                }
            }
        }
    }

    @Transactional
    public void fixSuperiorAuditRepeat(String companyId, String taskUserIds, String scoreType) {
        if (StrUtil.isBlank(companyId) || StrUtil.isBlank(taskUserIds) || StrUtil.isBlank(scoreType)) {
            return;
        }
        List<String> taskUserIdList = StrUtil.splitTrim(taskUserIds, ",");
        if (CollUtil.isEmpty(taskUserIdList)) {
            return;
        }

        for (String id : taskUserIdList) {
            EvalUser taskUser = taskUserRepo.getTaskUser(new TenantId(companyId), id);
            if (Objects.isNull(taskUser) || !StrUtil.equals(TalentStatus.SCORING.getStatus(), taskUser.getTaskStatus())) {
                continue;
            }
            List<EvalAudit> audits = taskUserRepo.listEvalAuditsByScene(new TenantId(companyId), id, scoreType);
            //如果已经分发过的，这里不再做去重了，只修复去重scoreResult
            if (CollUtil.isEmpty(audits) || StrUtil.equals("dispatched", audits.get(0).getStatus())) {
                continue;
            }
            boolean isUpAduit = false;
            //audits去重，根据 approverInfo 相同，保留approvalOrder 最小的
            ListWrap<EvalAudit> listWrap = new ListWrap<>(audits).groupBy(EvalAudit::getApproverInfo);
            for (Map.Entry<String, List<EvalAudit>> entry : listWrap.getGroups().entrySet()) {
                List<EvalAudit> groupAudit = entry.getValue();
                if (CollUtil.isEmpty(groupAudit) || groupAudit.size() == 1) {
                    continue;
                }
                log.info(" ======开始给 groupAudit去重 保留等级小的  ====== ");
                //groupAudit 根据 order 排序
                groupAudit.sort(Comparator.comparingInt(EvalAudit::getApprovalOrder));
//                if (!StrUtil.equals("dispatched",groupAudit.get(0).getStatus())) {//如果第一个不是已分发则退出，此订正接口仅支持上级同时
//                    continue;
//                }
                //如果大于第一个顺序的则标记删除，此订正接口仅支持上级同时
                groupAudit.stream().filter(audit -> audit.getApprovalOrder() > groupAudit.get(0).getApprovalOrder()).forEach(audit -> audit.setIsDeleted(Boolean.TRUE.toString()));
                isUpAduit = true;
            }
            //如果不需要修改就回退，证明无重复的
            if (!isUpAduit) {
                continue;
            }
            //重新给 listWrap.getData 的对象 ApprovalOrder 按从 0 开始编排
            log.info(" ======重新给 listWrap.getData 的对象 ApprovalOrder 按从 0 开始编排 ====== ");
            List<EvalAudit> newAudits = listWrap.getDatas();
            newAudits.sort(Comparator.comparingInt(EvalAudit::getApprovalOrder));
            int index = 0;
            for (EvalAudit newAudit : newAudits) {
                if (StrUtil.equals(Boolean.TRUE.toString(), newAudit.getIsDeleted())) {
                    continue;
                }
                index++;
                newAudit.setApprovalOrder(index);
            }
            log.info(" ======重新给 listWrap.getData 的对象 ApprovalOrder 按从 0 开始编排 ====== newAudits:{}", JSONUtil.toJsonStr(newAudits));
            taskUserRepo.batchUpdate(newAudits);
        }
    }

    @Transactional
    public void fixSuperiorResultRepeat(String companyId, String taskUserIds, String scoreType) {
        if (StrUtil.isBlank(companyId) || StrUtil.isBlank(taskUserIds) || StrUtil.isBlank(scoreType)) {
            return;
        }
        List<String> taskUserIdList = StrUtil.splitTrim(taskUserIds, ",");
        if (CollUtil.isEmpty(taskUserIdList)) {
            return;
        }

        for (String id : taskUserIdList) {
            EvalUser taskUser = taskUserRepo.getTaskUser(new TenantId(companyId), id);
            if (Objects.isNull(taskUser) || !StrUtil.equals(TalentStatus.SCORING.getStatus(), taskUser.getTaskStatus())) {
                continue;
            }
            List<EvalScoreResult> results = taskUserDao.fixListResultByScoreType(new TenantId(companyId), id, scoreType);
            if (CollUtil.isEmpty(results)) {
                continue;
            }
            //如果已经全部评分通过，则不处理
            if (isRsPass(results)) {
                return;
            }
            boolean isUpResult = false;
            List<CompanyMsgCenter> msgCenters = centerDao.listNotHandleMsg(new TenantId(companyId), id);
            List<String> scoreIds = new ArrayList<>();
            if (CollUtil.isNotEmpty(msgCenters)) {
                scoreIds = msgCenters.stream().map(CompanyMsgCenter::getEmpId).collect(Collectors.toList());
            }
            //results，根据 scoreId 相同，approvalOrder 最小的auditStatus = 'pass'，则另外一个approvalOrder 也 为 pass，
            // 且计算当前审批人是否全部pass,如果通过，
            ListWrap<EvalScoreResult> listWrap = new ListWrap<>(results).groupBy(EvalScoreResult::getScorerId);
            for (Map.Entry<String, List<EvalScoreResult>> entry : listWrap.getGroups().entrySet()) {
                List<EvalScoreResult> groupResults = entry.getValue();
                //存在待办也不处理,全部paSs 也不处理
                if (CollUtil.isEmpty(groupResults) || scoreIds.contains(entry.getKey()) || isRsPass(groupResults)) {
                    continue;
                }
                groupResults.sort(Comparator.comparingInt(EvalScoreResult::getApprovalOrder));//排序
                ListWrap<EvalScoreResult> listOderWrap = new ListWrap<>(groupResults).groupBy(evalScoreResult -> String.valueOf(evalScoreResult.getApprovalOrder()));
                //如果此评分人只有一个评分节点，则跳过，不处理。
                if (listOderWrap.getGroups().entrySet().size() <= 1) {
                    continue;
                }
                //往下 就是重复
                boolean isScoreed = false;
                for (Map.Entry<String, List<EvalScoreResult>> entryOder : listOderWrap.getGroups().entrySet()) {
                    List<EvalScoreResult> groupResult = entryOder.getValue();
                    if (CollUtil.isEmpty(groupResult)) {
                        continue;
                    }
                    //如果评分已经通过，则直接赋值通过
                    if (isScoreed) {
                        for (EvalScoreResult result : groupResult) {
                            result.setAuditStatus("pass");
                        }
                        log.info(" ======赋予通过 ======taskUserID:{} ", id);
                        isUpResult = true;
                        continue;
                    }
                    //如果通过，则赋值当前评分人其他节点也评分通过
                    if (StrUtil.equals("pass", groupResult.get(0).getAuditStatus())) {
                        isScoreed = true;
                    }
                }
                log.info(" ======开始计算是否已全部评分通过 ====== ");
            }
            //如果不需要修改就回退，证明无重复的  如果一个人多节点 的且都pass 不处理 ***
            if (!isUpResult) {
                continue;
            }
            taskUserRepo.fixBatchUpdateScoreResult(listWrap.getDatas());
            //没有评分人有待办，且全部pass，则当前层级结束重新计算分数，且重算等级
            if (CollUtil.isEmpty(scoreIds) && isRsPass(listWrap.getDatas())) {
                EmpEvalMerge evalMerge = empRuleRepo.getEmpEvalMerge(new TenantId(companyId), id, EmpEvalMerge.all);
                //重新计算分数，且重算等级
                log.info("评分层级结束" + "上级评");
                //下一个主层级
                ChainNodeEnd scoreNodeEnd = new ChainNodeEnd(evalMerge, taskUser, null);
                scoreNodeEnd.fire();
            }
        }
    }


    public void fixCustomUserFieldRepeatBatch() {
        //1. 查询 需要去重的数据 第一层是 去重后的taskUserid
        List<String> fields = kpiItemDao.listItemUsedFieldRepeat();
        if (CollUtil.isEmpty(fields)) {
            return;
        }

        for (String useId : fields) {
            log.info("开始去重 taskUserId:{}", useId);
            fixCustomUserFieldRepeat(useId);
            log.info("完成去重 taskUserId:{}", useId);
        }
    }

//    public void fixKpiItemRepeatBatch(List<String> companyIds) {
//        for (String companyId : companyIds) {
//            //1. 查询 需要去重的数据 第一层是 去重后的taskUserid
//            List<EvalKpi> kpis = kpiItemDao.listRepeatKpi(companyId);
//            if (CollUtil.isEmpty(kpis)) {
//                continue;
//            }
//            for (EvalKpi kpi : kpis) {
//
//            }
//        }
//
//
//        for (String useId : fields) {
//            log.info("开始去重 taskUserId:{}", useId);
//            fixCustomUserFieldRepeat(useId);
//            log.info("完成去重 taskUserId:{}", useId);
//        }
//    }

    @Async
    public void fixCustomUserFieldRepeat(String taskUserId) {
        EvalUser user = taskUserDao.getBaseEvalUserById(taskUserId);
        if (Objects.isNull(user)) {
            return;
        }
        //2.根据第1步的数据,循环处理单个task_user_id 数据
        List<KpiItemUsedField> usedFields = kpiItemDao.listItemUsedField(user.getCompanyId().getId(), taskUserId);
        if (Objects.isNull(usedFields)) {
            return;
        }
        //去重usedFields 根据指标id,字段id 去重取（修改时间取最新的）
        Map<String, KpiItemUsedField> resultMap = new HashMap<>();
        for (KpiItemUsedField field : usedFields) {
            String key = field.getKpiItemId() + "_" + field.getFieldId(); // 使用 kpiItemId 和 fieldId 组合作为键
            KpiItemUsedField existingField = resultMap.get(key);
            if (existingField == null || existingField.getUpdatedTime().before(field.getUpdatedTime())) {
                resultMap.put(key, field); // 更新为较新的记录
            }
        }
        List<KpiItemUsedField> uniqueFields = new ArrayList<>(resultMap.values());
        if (CollUtil.isEmpty(uniqueFields)) {
            return;
        }
        log.info("开始fixCustomUserFieldRepeat去重 companyId:{},taskUserId:{}", user.getCompanyId().getId(), taskUserId);
        CompanyCacheInfo companyCacheInfo = cacheDao.queryCacheInfo(user.getCompanyId().getId(), "modify_item_audit", taskUserId);
        if (companyCacheInfo != null) {
            initUseField(companyCacheInfo, uniqueFields);
        }
        //保存至数据库
        tx.runTran(() -> {
            empRuleRepo.updateItemUserdField(user.getCompanyId().getId(), taskUserId, uniqueFields);
            if (companyCacheInfo != null) {
                companyCacheRepo.updateCompanyCacheValue(companyCacheInfo);
                log.info("暂存内容fixCustomUserFieldRepeat去重 companyId:{},taskUserId:{}", user.getCompanyId().getId(), taskUserId);
            }
        });
        log.info("结束fixCustomUserFieldRepeat去重 companyId:{},taskUserId:{}", user.getCompanyId().getId(), taskUserId);
    }

    private void initUseField(CompanyCacheInfo companyCacheInfo, List<KpiItemUsedField> uniqueFields) {
        Map<String, List<KpiItemUsedField>> groupMap = uniqueFields.stream()
                .collect(Collectors.groupingBy(KpiItemUsedField::getKpiItemId));
        List<ConfirmTypeCache> confirmTypeCaches = JSONUtil.parseArray(companyCacheInfo.getValue()).toList(ConfirmTypeCache.class);
        confirmTypeCaches.forEach(confirmTypeCache -> {
            if (CollUtil.isEmpty(confirmTypeCache.getIndexList())) {
                return;
            }
            //循环confirmTypeCache.getIndexList()
            for (ConfirmTypeCache.CacheItem cacheItem : confirmTypeCache.getIndexList()) {
                if (groupMap.get(cacheItem.getId()) == null) {
                    continue;
                }
                List<ItemCustomFieldValue> fieldValueList = new ArrayList<>();
                List<KpiItemUsedField> kpiItemUsedFields = groupMap.get(cacheItem.getId());
                if (kpiItemUsedFields != null && kpiItemUsedFields.size() > 0) {
                    for (KpiItemUsedField itemUsedField : kpiItemUsedFields) {
                        ItemCustomFieldValue fieldValue = new ItemCustomFieldValue();
                        BeanUtils.copyProperties(itemUsedField, fieldValue);
                        fieldValue.setFieldName(itemUsedField.getName());
                        fieldValue.setFieldValue(itemUsedField.getValue());
                        fieldValue.setFieldStatus(itemUsedField.getStatus());
                        fieldValue.setId(itemUsedField.getFieldId());
                        fieldValue.setIsReq(itemUsedField.getReq());
                        fieldValueList.add(fieldValue);
                    }
                    cacheItem.setFieldValueList(fieldValueList);
                }
            }
        });

        companyCacheInfo.setValue(JSONUtil.toJsonStr(confirmTypeCaches));
    }

    private boolean isRsPass(List<EvalScoreResult> listRs) {
        if (CollUtil.isEmpty(listRs)) {
            return true;
        }
        List<String> ids = new ArrayList<>();
        for (EvalScoreResult rs : listRs) {
            if (StrUtil.equals("pass", rs.getAuditStatus())) {
                ids.add(rs.getId());
            }
        }
        return ids.size() == listRs.size();
    }

    @DmEventPublish
    public void cancelMsg(String companyId, String taskUserIds, String msgScene) {
        if (StrUtil.isBlank(companyId) || StrUtil.isBlank(taskUserIds) || StrUtil.isBlank(msgScene)) {
            return;
        }
        for (String taskUserId : StrUtil.splitTrim(taskUserIds, ",")) {
            new CancelTodoEvent(new TenantId(companyId), taskUserId, msgScene).fire();
        }
    }
}
