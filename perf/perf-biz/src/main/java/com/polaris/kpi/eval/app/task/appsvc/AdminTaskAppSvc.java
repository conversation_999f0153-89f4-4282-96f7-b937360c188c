package com.polaris.kpi.eval.app.task.appsvc;

import cn.com.polaris.kpi.EvalOrg;
import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.KpiOrgSupNames;
import cn.com.polaris.kpi.company.GlobalConfEnum;
import cn.com.polaris.kpi.company.MsgSceneEnum;
import cn.com.polaris.kpi.company.TenantSysConf;
import cn.com.polaris.kpi.eval.EvalUserOfTask;
import cn.com.polaris.kpi.eval.Name;
import cn.com.seendio.polaris.excel.AutoColumnWidthStyleStrategy;
import cn.com.seendio.polaris.excel.DCClsExport;
import cn.com.seendio.polaris.excel.DataClassExcelExport;
import cn.com.seendio.polaris.excel.DataClassMergeStrategy;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.perf.www.common.constant.BusinessConstant;
import com.perf.www.common.em.CompanyMsgActionEnum;
import com.perf.www.common.excel.ExcelUtil;
import com.perf.www.common.utils.date.DateTimeUtils;
import com.perf.www.manager.dic.CompanySysSettingManager;
import com.perf.www.model.dic.CompanySysSettingModel;
import com.perf.www.vo.task.taskuser.PerfEvaluateTaskUserLevelCountVO;
import com.perf.www.vo.task.taskuser.PerfEvaluateTaskUserLevelVO;
import com.polaris.acl.dept.domain.org.EmpOrganization;
import com.polaris.acl.dept.face.AppAcl;
import com.polaris.acl.dept.pojo.CompanyDo;
import com.polaris.acl.dept.pojo.org.EmpPositionDo;
import com.polaris.acl.dept.pojo.org.EmpRankDo;
import com.polaris.acl.dept.repository.DeptEmpDao;
import com.polaris.acl.kpi.eval.domain.EvalEmp;
import com.polaris.acl.msg.domain.FinishWorkReq;
import com.polaris.acl.msg.face.MsgAcl;
import com.polaris.kpi.ask.domain.acl.AskEvalAcl;
import com.polaris.kpi.common.KpiI18NException;
import com.polaris.kpi.eval.app.TransactionWrap;
import com.polaris.kpi.eval.app.task.dto.MutualApprover;
import com.polaris.kpi.eval.app.task.dto.MutualItemAudit;
import com.polaris.kpi.eval.app.task.dto.admin.*;
import com.polaris.kpi.eval.app.task.dto.eval.AddEvalEmpCmd;
import com.polaris.kpi.eval.app.task.dto.eval.BatchStartEmpEvalCmd;
import com.polaris.kpi.eval.app.task.dto.eval.InviteMutualCmd;
import com.polaris.kpi.eval.app.task.dto.eval.RemoveEvalEmpCmd;
import com.polaris.kpi.eval.domain.confirm.event.AdminConfirmConfEdited;
import com.polaris.kpi.eval.domain.cycle.repo.CycleRepo;
import com.polaris.kpi.eval.domain.cycle.type.CycleOperationEnum;
import com.polaris.kpi.eval.domain.stage.dmsvc.StageConfDiffLogDmSvc;
import com.polaris.kpi.eval.domain.stage.repo.AutoValidEmpNameFinder;
import com.polaris.kpi.eval.domain.stage.repo.ResetEmpNameFinder;
import com.polaris.kpi.eval.domain.task.dmsvc.CycleEvalDmSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.ResultAuditDmSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.ScorerTodoDmSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.ScorerTotalDmSvc;
import com.polaris.kpi.eval.domain.task.entity.Cycle;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.PerfTaskExecuteBatch;
import com.polaris.kpi.eval.domain.task.entity.ScorerItem;
import com.polaris.kpi.eval.domain.task.entity.admineval.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.AuditResultEvalType;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalRule;
import com.polaris.kpi.eval.domain.task.entity.empeval.TaskExecuteBatch;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.invite.InviteItem;
import com.polaris.kpi.eval.domain.task.entity.grade.GradeStep;
import com.polaris.kpi.eval.domain.task.entity.grade.ScoreRule;
import com.polaris.kpi.eval.domain.task.entity.msg.MsgTodoAggregate;
import com.polaris.kpi.eval.domain.task.event.KpiItemUpdateFinishedValueEvent;
import com.polaris.kpi.eval.domain.task.event.admineval.*;
import com.polaris.kpi.eval.domain.task.event.msg.CancelRemoteTodoEvent;
import com.polaris.kpi.eval.domain.task.event.msg.CancelTodoEvent;
import com.polaris.kpi.eval.domain.task.event.talent.ResultCollectMsgTodoEvent;
import com.polaris.kpi.eval.domain.task.event.talent.ScoreSummaryMsgTodoEvent;
import com.polaris.kpi.eval.domain.task.event.talent.TalentEvalDeleted;
import com.polaris.kpi.eval.domain.task.event.talent.TaskDeletedEvent;
import com.polaris.kpi.eval.domain.task.event.talent.scoring.EvalScoreChanged;
import com.polaris.kpi.eval.domain.task.repo.AdminTaskRepo;
import com.polaris.kpi.eval.domain.task.repo.EmpEvalRuleRepo;
import com.polaris.kpi.eval.domain.task.repo.ScorerSummaryTodoRepo;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.eval.domain.task.type.TaskEval;
import com.polaris.kpi.eval.domain.task.type.commentNodeConf.CommentNodeSet;
import com.polaris.kpi.eval.domain.task.type.commentNodeConf.CommentReqLevel;
import com.polaris.kpi.eval.domain.task.type.commentNodeConf.CommentRule;
import com.polaris.kpi.eval.infr.acl.ding.imp.ZnxcEvalResultPusher;
import com.polaris.kpi.eval.infr.cycle.dao.CycleEvalDao;
import com.polaris.kpi.eval.infr.task.dao.*;
import com.polaris.kpi.eval.infr.task.ppojo.FixPerfEvaluateTaskUserDo;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskBaseDo;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskUserDo;
import com.polaris.kpi.eval.infr.task.ppojo.ResultAuditRecordPo;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.*;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.EmpEvalRuleDo;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.EvalOfEmpPo;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.ScoreSortConfPo;
import com.polaris.kpi.eval.infr.task.query.EmpEvalTaskStatusQuery;
import com.polaris.kpi.eval.infr.task.query.EvalUserQuery;
import com.polaris.kpi.eval.infr.task.query.TaskGradeCountQuery;
import com.polaris.kpi.eval.infr.task.query.TaskMutualItemQuery;
import com.polaris.kpi.eval.infr.task.query.admin.*;
import com.polaris.kpi.eval.infr.task.query.empeval.EmpEvalAtCycleQuery;
import com.polaris.kpi.eval.infr.task.query.empeval.EvalOfEmpQuery;
import com.polaris.kpi.eval.infr.task.query.empeval.ParseAddEmpEvalQuery;
import com.polaris.kpi.eval.infr.task.repimpl.TaskUserRepoImpl;
import com.polaris.kpi.org.domain.company.repo.TenantSysConfRepo;
import com.polaris.kpi.org.domain.dept.dmsvc.EmpParser;
import com.polaris.kpi.org.domain.dept.entity.Company;
import com.polaris.kpi.org.domain.dept.entity.CompanyMsgCenter;
import com.polaris.kpi.org.domain.dept.entity.Employee;
import com.polaris.kpi.org.domain.dept.repo.EmpFinder;
import com.polaris.kpi.org.domain.dept.repo.MsgCenterRepo;
import com.polaris.kpi.org.domain.dept.type.TaskId;
import com.polaris.kpi.org.domain.emp.entity.KpiEmployee;
import com.polaris.kpi.org.infr.company.dao.CompanyDaoImpl;
import com.polaris.kpi.org.infr.dept.dao.KpiOrgDao;
import com.polaris.kpi.org.infr.emp.dao.KpiEmpDao;
import com.polaris.kpi.org.infr.emp.pojo.EmpRefOrgDo;
import com.polaris.kpi.setting.domain.entity.ScorerTodoSummary;
import com.polaris.kpi.setting.domain.entity.TaskUserScorer;
import com.polaris.kpi.setting.domain.repo.ResultAuditFlowRepo;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.lufei.ibatis.common.data.ToDataBuilder;
import org.lufei.ibatis.mapper.PagedList;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.app.task.appsvc
 * @Author: lufei
 * @CreateTime: 2022-08-27  11:27
 * @Description: 管理任务应用层
 * @Version: 1.0
 */
@Slf4j
@Service
public class AdminTaskAppSvc {
    @Autowired
    private AdminTaskDao adminTaskDao;
    @Autowired
    private DeptEmpDao deptEmpDao;
    @Autowired
    private AdminTaskRepo adminTaskRepo;
    @Autowired
    private GradeDao gradeDao;
    @Autowired
    private KpiEmpDao kpiEmpDao;
    @Autowired
    private CycleEvalDao cycleDao;
    @Resource
    private CycleRepo cycleRepo;
    @Autowired
    private TaskUserRepoImpl userRepo;
    @Autowired
    private TaskUserDao taskUserDao;
    @Autowired
    private EmpEvalDao empEvalDao;
    @Autowired
    private EmpEvalRuleRepo evalRuleRepo;
    @Autowired
    private MsgCenterRepo centerRepo;
    @Autowired
    private CompanyDaoImpl companyDao;
    @Autowired
    @Lazy
    private AdminTaskAppSvc self;
    @Autowired
    private KpiOrgDao kpiOrgDao;
    @Autowired
    private CalibratedDao calibratedDao;
    @Autowired
    private AppAcl appAcl;
    @Autowired
    private CycleEvalDmSvc evalDmSvc;
    @Autowired
    private CompanySysSettingManager companySysSettingManager;
    @Autowired
    private ResultAuditFlowRepo auditFlowRepo;
    @Autowired
    private AskEvalAcl askEvalAcl;
    @Autowired
    private RankRuleSnapDao rankRuleSnapDao;
    @Autowired
    private TransactionWrap trans;
    @Autowired
    private TenantSysConfRepo tenantSysConfRepo;
    @Autowired
    @Lazy
    private EmpEvalAppSvc empEvalAppSvc;
    @Autowired
    private ScorerSummaryTodoRepo scorerSummaryTodoRepo;
    @Autowired
    private MsgAcl msgAcl;

    @Autowired
    private AutoValidEmpNameFinder autoFinder;
    @Autowired
    private ResetEmpNameFinder resetFinder;
    @Autowired
    private EmpFinder empFinder;
    @Autowired
    private ZnxcEvalResultPusher znxcEvalResultPusher;

    public void setTaskDao(AdminTaskDao taskDao) {
        adminTaskDao = taskDao;
    }

    public boolean existedEvalTaskName(TenantId companyId, String taskName) {
        return adminTaskDao.existedEvalTaskName(companyId, taskName);
    }

    @Transactional
    public boolean editEvalTaskName(TenantId tenantId, String taskId, String name) {
        return adminTaskDao.editEvalTaskName(tenantId, taskId, name);
    }

    @Transactional
    public String editEvalTaskName(TenantId tenantId, String taskId, String name, EmpId opEmpId, String opAdminType) {
        KpiEmployee opEmp = kpiEmpDao.findEmployee(tenantId, opEmpId.getId());
        String logId = adminTaskDao.editEvalTaskName(tenantId, taskId, name, opEmp, opAdminType);
        return logId;
    }

    public PagedList<EvalOfEmpPo> pagedTaskOfEmpReport(EvalOfEmpQuery query) {
        return taskUserDao.pagedTaskOfEmpReport(query);
    }

    //创建管理任务
    @Transactional
    public CreateEvalTaskRs createEvalTask(AddAdminTaskCmd cmd) {
        CreateEvalTaskRs rs = new CreateEvalTaskRs(cmd.getCycleId(), cmd.getRuleId(), cmd.getScoreRuleSnapId(), cmd.getCompanyId().getId());
        if (cmd.needSnapId()) {//等级任务模式,绑定快照id
            cmd.newScoreRuleSnapId(rankRuleSnapDao.newSnapId());
            rs.markNeedCreateSnap(cmd.getScoreRuleSnapId());
        }
        cmd.totalCnt(0);
        EmpId opEmpId = new EmpId(cmd.getCreatedUser());
        Cycle cycle = cycleDao.findCycle(cmd.getCompanyId(), cmd.getCycleId());
        cmd.startDate(cycle.getCycleStart(), cycle.getCycleEnd());
        if (cmd.resultSendNotifyIsNull()) {
            //配置通知发送设置
            CompanySysSettingModel settingModel = companySysSettingManager.querySysSetting(cmd.getCompanyId().getId(), "notifySend");
            if (Objects.nonNull(settingModel) && settingModel.isResultCollectSend()) {
                cmd.setResultSendNotify(2);
            }
        }
        adminTaskRepo.addAdminTask(cmd, cmd.getAdmins(), cmd.getReaders(), opEmpId, cmd.getOpAdminType());
        rs.setTaskId(cmd.getId());
        return rs;
    }


    //删除管理任务
    public void deleteEvalTask(TenantId tenantId, String taskId, EmpId opEmpId, String opAdminType) {
        AdminTask adminTask = adminTaskRepo.getAdminTask(tenantId, taskId);
        if (Objects.isNull(adminTask)) {
            return;
        }

        List<EvalUser> evalUsers = taskUserDao.listTaskUsersByTaskId(tenantId, taskId);
        trans.runTran(() -> znxcEvalResultPusher.deleteEvalResult(tenantId, evalUsers, adminTask));

        List<String> userIds = trans.runTran(() -> adminTaskRepo.deleteAdminTask(tenantId, taskId));
        new TalentEvalDeleted(tenantId.getId(), userIds).fire();
        userIds.stream().forEach(userId -> {
            new CancelTodoEvent(tenantId, userId, new ArrayList<>()).fire();
//            new TalentEvalDeleted(tenantId.getId(), userId).publish();
            //taskDao.renewalRefEval(tenantId.getId(),userId);
            /**重新计算分数*/
            new EvalScoreChanged(tenantId.getId(), userId);
        });
        new TaskDeletedEvent(tenantId.getId(), taskId).fire();
        // 操作日志
        KpiEmployee opEmp = kpiEmpDao.findEmployee(adminTask.getCompanyId(), opEmpId.getId());
        //添加周期日志
        AdminCycleOperation cycleOperation = new AdminCycleOperation();
        cycleOperation.initTaskOperation(adminTask, opEmp, opAdminType, CycleOperationEnum.DELETE_TASK);
        trans.runAsyn(MDC.get("tid"), () -> {
            cycleRepo.saveDiffLog(cycleOperation);
            cycleRepo.refreshCycleEmpCnt(tenantId, adminTask.getCycleId());
        });

    }

    public CreateEvalTaskRs copyAdminTask(CopyAdminTaskCmd cmd) {
        CreateEvalTaskRs rs = new CreateEvalTaskRs(cmd.getCycleId(), cmd.getRuleId(), cmd.getScoreRuleSnapId(), cmd.getCompanyId().getId());
        if (cmd.needSnapId()) {//等级任务模式,绑定快照id
            cmd.newScoreRuleSnapId(rankRuleSnapDao.newSnapId());
            rs.markNeedCreateSnap(cmd.getScoreRuleSnapId());
        }
        Cycle cycle = cycleDao.findCycle(cmd.getCompanyId(), cmd.getCycleId());
        cmd.startDate(cycle.getCycleStart(), cycle.getCycleEnd());
        //复制管理任务与考核员工
        //配置通知发送设置
        if (cmd.getAuditResult().isOpen()) {
            CompanySysSettingModel settingModel = companySysSettingManager.querySysSetting(cmd.getCompanyId().getId(), "notifySend");
            if (Objects.nonNull(settingModel)) {
                cmd.setResultSendNotify(settingModel.getResultSendType());
            }
        }
        //取系统级评分汇总配置
        boolean scoreSummaryOpen = tenantSysConfRepo.isOpen(cmd.getCompanyId().getId(), GlobalConfEnum.SCORE_SUMMARY_NOTICE.getConfCode());
        if (scoreSummaryOpen) {
            ScoreConf scoreConf = cmd.getScoreConf();
            scoreConf.setSummarySendTodo(2);
            cmd.setScoreConf(scoreConf);
        } else {
            ScoreConf scoreConf = cmd.getScoreConf();
            scoreConf.setSummarySendTodo(1);
            cmd.setScoreConf(scoreConf);
        }

        TaskEval taskEval = self.copyTaskAndEvalUser(cmd);
        //复制考核规则
        copyEvalRule(cmd, taskEval);
        rs.setTaskId(taskEval.getTaskId());
        return rs;
    }

    @Transactional
    public TaskEval copyTaskAndEvalUser(CopyAdminTaskCmd cmd) {
        //复制管理任务
        String newTaskId = adminTaskRepo.copyAdminTask(cmd, cmd.getFromTaskId(), new EmpId(cmd.getCreatedUser()), cmd.getOpAdminType());
        TaskEval taskEval = new TaskEval();
        if (cmd.copyEvalUser()) {
            log.info("开启复制员工任务");
            List<String> adminOrgIds = kpiOrgDao.listAdminScopePrivOrgIds(cmd.getCompanyId().getId(), cmd.getCreatedUser(), null);
            AdminTaskCopyConf copyConf = new AdminTaskCopyConf(cmd.getCopyEvalUser(), cmd.getCopyEvalRule());
            taskEval = adminTaskRepo.copyEvalUsers(cmd, cmd.getFromTaskId(),
                    new EmpId(cmd.getCreatedUser()), cmd.getOpAdminType(), copyConf, adminOrgIds);
            taskEval.setTaskId(newTaskId);
            return taskEval;
        }
        taskEval.setTaskId(newTaskId);
        return taskEval;
    }

    public void copyEvalRule(CopyAdminTaskCmd cmd, TaskEval taskEval) {
        if (cmd.copyEvalUser()) {
            if (cmd.copyEvalRule()) {
                log.info("开启同步复制原任务的考核规则");
                CopyEvalRule copyEvalRule = new CopyEvalRule(cmd.getCompanyId(), cmd.getCycleId(), taskEval.getTaskId(), taskEval.getOpEmpEvals());
                copyEvalRule.setScoreSortConf(cmd.getScoreSortConf());
                copyEvalRule.accOp(cmd.getCreatedUser(), cmd.getOpAdminType(), cmd.isEdit());
                copyEvalRule.publish();
            } else {
                log.info("使用默认考核规则");
                AddEmpToTask addEmpToTask = new AddEmpToTask(cmd.getCompanyId(), cmd.getCycleId(), taskEval.getTaskId(), taskEval.getOpEmpEvals());
                addEmpToTask.accOp(cmd.getCreatedUser(), cmd.getOpAdminType());
                addEmpToTask.publish();
            }
        }
    }

    public AdminTaskCopyRecordDo getCopyRecordByTaskId(String companyId, String taskId) {
        return adminTaskDao.getCopyRecordByTaskId(companyId, taskId);
    }

    public PagedList<AdminTaskCopyRecordDetailsPo> pagedTaskCopyRecordDetails(AdminTaskCopyQuery query) {
        PagedList<AdminTaskCopyRecordDetailsPo> pagedList = adminTaskDao.pagedTaskCopyRecordDetails(query);
        //显示上级
        List<String> curOrgIds = pagedList.getData().stream().filter(r -> StrUtil.isNotBlank(r.getOrgId())).map(r -> r.getOrgId()).collect(Collectors.toList());
        List<KpiOrgSupNames> supNames = kpiOrgDao.listOrgSupNames(new TenantId(query.getCompanyId()), curOrgIds);
        pagedList.getData().forEach(r -> r.matchSupNames(supNames));
        return pagedList;
    }

    public void readTaskCopyRecord(String recordId) {
        adminTaskRepo.readTaskCopyRecord(recordId);
    }

    public PrivAdminTask getPrivTaskBase(TenantId tenantId, EmpId adminId, String taskId) {
        return adminTaskDao.getPrivTaskBase(tenantId, adminId, taskId);
    }

    public AdminTaskStatusCnt getTaskStatusCnt(EmpEvalTaskStatusQuery query) {
        query.setAdminOrgIds(kpiOrgDao.listAdminScopePrivOrgIds(query.getCompanyId(), query.getOpEmpId(), null));
        if (query.isPriv()) {
            query.setTaskIds(cycleDao.listTaskPrivByOpEmpId(query.getCompanyId(), query.getCycleId(), query.getOpEmpId()));
        }
        return adminTaskDao.getTaskStatusCnt(query);
    }

    @Transactional
    public void addEvalEmp(AddEvalEmpCmd cmd) {
        List<EmpOrganization> paths = deptEmpDao.listDeptWithNamePath(cmd.getTenantId(), cmd.atOrgIds());
        ListWrap<EmpOrganization> pathMap = new ListWrap<>(paths).asMap(path -> path.getOrgId());
        if (cmd.hasFreeCompany()) {
            cmd.setTaskUserCount(empEvalDao.getTaskUserCount(cmd.getTenantId().getId(), cmd.getTaskId()));
            cmd.handleFreeCompanyData();
        }
        List<EvalUser> evalUsers = cmd.buildEvalUsers();
        // 处理职位、职级
        Set<String> empRank = new HashSet<>();
        Set<String> empPosition = new HashSet<>();
        for (EvalEmp emp : cmd.getEmps()){
            if (StrUtil.isNotEmpty(emp.getEmpRank())){
                empRank.add(emp.getEmpRank());
            }
            if (StrUtil.isNotEmpty(emp.getEmpPosition())){
                empPosition.add(emp.getEmpPosition());
            }
        }
        List<EmpRankDo> empRankDos = Collections.emptyList();
        if (CollUtil.isNotEmpty(empRank)){
            empRankDos = kpiOrgDao.listRankByNames(cmd.getTenantId(), empRank);
        }
        List<EmpPositionDo> empPositionDos = Collections.emptyList();
        if (CollUtil.isNotEmpty(empPosition)){
            empPositionDos = kpiOrgDao.listPositionByNames(cmd.getTenantId(), empPosition);
        }
        cmd.buildRankPosition(evalUsers, empRankDos, empPositionDos);

        for (EvalUser evalUser : evalUsers) {
//            ScoreRule empScoreRule = gradeDao.getEmpScoreRule(cmd.getTenantId(), evalUser.getEmpId(),evalUser.getOrgId());
//            evalUser.scoreRanges(empScoreRule.getRanges());
            evalUser.setRuleConfStatus(100);
            //找不到需要报警
            EmpOrganization path = pathMap.mapGet(evalUser.getOrgId());
            evalUser.acceptOrgPath(path.getOrgCode(), path.getNamePath(), path.getPathHight());
        }
        List<OpEmpEval> opEmpEvals = adminTaskRepo.batchAddEmpEval(evalUsers, cmd.getOpEmpId(), cmd.getOpAdminType());
        AddEmpToTask addEmpToTask = new AddEmpToTask(cmd.getTenantId(), cmd.getCycleId(), cmd.getTaskId(), opEmpEvals, cmd.getEvalGroupIds());
        addEmpToTask.accOp(cmd.getOpEmpId().getId(), cmd.getOpAdminType());
        addEmpToTask.publish();
    }

    @Transactional
    public void addEvalOrg(AddEvalEmpCmd cmd) {
        List<EmpOrganization> paths = deptEmpDao.listDeptWithNamePath(cmd.getTenantId(), cmd.atOrgIds());
        ListWrap<EmpOrganization> pathMap = new ListWrap<>(paths).asMap(path -> path.getOrgId());
        List<EvalOrg> evalOrgs = userRepo.loadEvalOrgOwner(cmd.getTenantId(), cmd.evalOrgIds());
        cmd.setEvalOrgs(evalOrgs);
        List<EvalUser> evalUsers = cmd.buildEvalOrgUsers();
        for (EvalUser evalUser : evalUsers) {
            //获取被被考核组织使用的等级规则
            ScoreRule empScoreRule = gradeDao.getEvalOrgScoreRule(cmd.getTenantId(), evalUser.getEvalOrgId());
            evalUser.scoreRanges(empScoreRule.getRanges());
            evalUser.setRuleConfStatus(100);
            //找不到需要报警
            EmpOrganization path = pathMap.mapGet(evalUser.atOrgId());
            evalUser.acceptOrgPath(path.getOrgCode(), path.getNamePath(), path.getPathHight());
        }
        List<OpEmpEval> opEmpEvals = adminTaskRepo.batchAddEmpEval(evalUsers, cmd.getOpEmpId(), cmd.getOpAdminType());
        AddEmpToTask addEmpToTask = new AddEmpToTask(cmd.getTenantId(), cmd.getCycleId(), cmd.getTaskId(), opEmpEvals);
        addEmpToTask.accOp(cmd.getOpEmpId().getId(), cmd.getOpAdminType());
        addEmpToTask.publish();
    }

    public void removeEvalEmp(RemoveEvalEmpCmd cmd) {
        List<String> adminTaskIds = adminTaskDao.listAdminTaskIds(cmd.getTenantId(), cmd.getTaskUserIds());
        List<String> taskUserIds = cmd.getTaskUsers().stream().map(opEmpEval -> opEmpEval.getTaskUserId()).distinct().collect(Collectors.toList());
        List<EvalUser> evalUsers = taskUserDao.listBaseEvalUser(cmd.getTenantId(), taskUserIds);

        trans.runTran(() -> znxcEvalResultPusher.deleteEvalResult(cmd.getTenantId(), evalUsers, null));

        trans.runTran(() -> adminTaskRepo.removeEmpEval(cmd.getTenantId(), cmd.getOpAdminType(), cmd.getOpEmpId(), adminTaskIds, cmd.getTaskUsers()));
        new TalentEvalDeleted(cmd.getTenantId().getId(), taskUserIds).fire();
        trans.runTran(() -> {
            for (OpEmpEval taskUser : cmd.getTaskUsers()) {
                //取消待办
                new CancelTodoEvent(cmd.getTenantId(), new ArrayList<>(), taskUser.getTaskUserId()).publish();
                //处理okr的锁定标识
                new EvalEmpOkrLockEdited(cmd.getTenantId(), cmd.getTaskUserIds(), new EmpId(cmd.getOpEmpId()), "del").publish();
                //清除校准审核流程中人员。
                EvalUser user = userRepo.getTaskUser(cmd.getTenantId(), taskUser.getTaskUserId());
                ResultAuditDmSvc dmSvc = new ResultAuditDmSvc(user, null, cmd.getTenantId().getId(), user.getUpdatedUser());
                dmSvc.setRepo(auditFlowRepo);
                dmSvc.delResultAuditFlow();
                if (dmSvc.needSend()) {
                    AdminTask adminTask = adminTaskRepo.getAdminTask(cmd.getTenantId(), user.getTaskId());
                    new ResultCollectMsgTodoEvent(cmd.getTenantId().getId(), dmSvc.getSendRater(), adminTask).publish();
                }

                //执行汇总评分的移除人员逻辑
//            EmpEvalMerge evalMerge = evalRuleRepo.getEmpEvalMerge(cmd.getTenantId(), taskUser.getTaskUserId(), EmpEvalMerge.itemRule);
                ScorerTodoDmSvc todoDmSvc = new ScorerTodoDmSvc(user);
                //查询是否存在评分人实例
                List<String> strings = scorerSummaryTodoRepo.queryScorerIds(user);
                if (CollUtil.isNotEmpty(strings)) {
                    Set<String> scorerIds = scorerSummaryTodoRepo.removeScorerByTaskUser(user.getCompanyId().getId(), user.getId());
                    todoDmSvc.setRepo(scorerSummaryTodoRepo);
                    todoDmSvc.refreshSummary(scorerIds);
                    List<ScorerTodoSummary> updateSummaries = todoDmSvc.getUpdateSummaries();
                    for (ScorerTodoSummary updateSummary : updateSummaries) {
                        if (updateSummary.readyToClose()) { //关闭钉钉待办
                            FinishWorkReq req = new FinishWorkReq(user.getCompanyId(), new EmpId(updateSummary.getScorerId()), updateSummary.getThirdMsgId());
                            boolean isSuccess = false;
                            try {
                                msgAcl.finishTodoWork(req);
                                isSuccess = true;
                            } catch (Exception e) {
                                log.error("完成待办失败:" + e.getMessage(), e);
                            }
                            if (isSuccess) {
                                updateSummary.setTodoStatus(2);
                            }
                            updateSummary.setUpdatedTime(new Date());
                        }
                        if (updateSummary.readyToSend()) { //发送钉钉待办，系统待办，通知
                            List<TaskUserScorer> scorers = scorerSummaryTodoRepo.queryNeedSendSystemTodoScorers(updateSummary.getCompanyId(), updateSummary.getTaskId(), updateSummary.getScorerId());
                            new ScoreSummaryMsgTodoEvent(updateSummary.getCompanyId(), scorers, updateSummary).publish();
                        }
                    }
                    scorerSummaryTodoRepo.batchUpdateSummaries(updateSummaries);
                }
            }
        });
        //移除问卷关系
        askEvalAcl.removePerfRefAskEval(cmd.getTenantId().getId(), cmd.getTaskUserIds(), cmd.getOpEmpId());
        new EvalEmpRemoved(cmd.getTenantId(), cmd.getCycleId(), adminTaskIds).fire();
    }

    public PagedList<AdminTaskPo> pagedMyCreateTask(AdminTaskQuery query) {
        return adminTaskDao.pagedManageEvalTask(query);
    }

    public PagedList<PagedSimpleTaskBasePo> pagedEvaluateTaskBase(SimpleTaskBaseQuery query) {
        return adminTaskDao.pagedEvaluateTaskBase(query);
    }

    public PagedList<AdminTaskPo> pagedManageEvalTask(AdminTaskQuery query) {
        //cycleDao.findAdminOfCycle(query.getTenantId(),query.getCycleIds(),query.getOpEmpId())
        //主管理,直接查所有任务
        if (query.isMainAdminTypeOp()) {
            return adminTaskDao.pagedAdminTask(query);
        }
        //则只查任务管理范围内的
        return adminTaskDao.pagedManageEvalTask(query);
    }

    @Transactional
    public void addAdminOfTask(AdminOfTaskCmd cmd) {
        List<AdminOfTask> admins = cmd.batchAdmins();
        List<AdminOfTask> adds = new ArrayList<>();
        List<AdminOfTask> updates = new ArrayList<>();
        List<AdminOfTask> deleteds = new ArrayList<>();
        Map<String, AdminOfTask> exists = adminTaskDao.listAdminOfTaskAsMainMap(cmd.getCompanyId(), cmd.getTaskId());
        for (AdminOfTask admin : admins) {
            if (exists.get(admin.getAdminEmpId() + "-" + admin.getMain()) == null) {
                adds.add(admin);
            } else {
                updates.add(admin);
            }
            exists.remove(admin.getAdminEmpId() + "-" + admin.getMain());
        }
        exists.forEach((k, v) -> {
            deleteds.add(v);
        });
        //新增or编辑or移除
        adminTaskRepo.addAdminOfTask(adds, updates, deleteds, cmd.getOpAdminType());
    }

    @Transactional
    public void removeAdminOfTask(TenantId tenantId, String taskId, String adminEmpId, String adminType, String opEmpId) {
        adminTaskRepo.removeAdminOfTask(tenantId, taskId, adminEmpId, adminType, opEmpId);
    }

    public AdminTask getEditEvalTask(TenantId tenantId, String taskId) {
        return adminTaskRepo.getAdminTask(tenantId, taskId);
    }

    @Transactional
    public void refreshTaskCnt(String companyId, String taskId) {
        log.info("刷新任务完成度:{}", taskId);
        adminTaskRepo.refreshCntOfTask(companyId, taskId);
    }

    @Transactional
    public void refreshBatchStartCnt(String companyId, String batchId, List<String> taskUserIds) {
        //如果是批量发布
        adminTaskRepo.refreshBatchStartCnt(companyId, batchId, taskUserIds);
    }


    @Transactional
    public void refreshTaskExecuteBatch(String companyId, List<PerfTaskExecuteBatch> batchList) {
        if (batchList.isEmpty()) {
            return;
        }
        List<String> idList = new ArrayList<>();
        batchList.forEach(batch -> {
            if (batch.getStartCount() + batch.getErrorCount() == batch.getDrawUpCount()) {
                batch.setStatus("finish");
                adminTaskRepo.updateExecuteBatchStasuts(batch.getId());
            }
            if (batch.getErrorCount() - batch.getPassCount() == 0) {
                idList.add(batch.getId());
            }
        });
        if (!idList.isEmpty()) {
            adminTaskRepo.updateExecuteBatchRead(idList);
        }
    }

    public TaskExecuteBatch saveExecuteBatch(BatchStartEmpEvalCmd cmd) {
        List<String> adminOrgIds = kpiOrgDao.listAdminScopePrivOrgIds(cmd.getCompanyId(), cmd.getOpEmpId().getId(), null);
        cmd.setAdminOrgIds(adminOrgIds);
        TaskExecuteBatch batch = new TaskExecuteBatch(cmd.getCompanyId(), cmd.getTaskIds(), cmd.getOpEmpId().getId());
        trans.runTran(() -> adminTaskRepo.saveExecuteBatch(batch, adminOrgIds, cmd.getPerformanceType()));
        return batch;
    }

    @Transactional
    public String terminatedAdminTask(TenantId tenantId, String taskId, EmpId opEmpId, String opAdminType) {
        adminTaskRepo.terminatedAdminTask(tenantId, taskId, opEmpId, opAdminType);
        //TODO 发送领域管理任务被终止事件,用于清理待办
        List<EvalUserOfTask> userOfTasks = taskUserDao.listEvalUserOfTaskByTaskId(tenantId.getId(), taskId);
        for (EvalUserOfTask userOfTask : userOfTasks) {
            List<CompanyMsgCenter> progressTodos = centerRepo.finishByQuery(tenantId, userOfTask.getTaskUserId());
            new CancelRemoteTodoEvent(new TenantId(userOfTask.getCompanyId()), progressTodos).publish();
            new MsgTodoAggregate(tenantId, userOfTask.getTaskId(), new Name(userOfTask.getTaskName()),
                    userOfTask.getEmpId(), userOfTask.getTaskUserId())
                    .useScene(MsgSceneEnum.TASK_STOPPED, CompanyMsgActionEnum.RESULT_APPEAL)
                    .sendExtMsg().addRecEmpId(userOfTask.getEmpId()).publish();
        }
        return taskId;
    }

    @Transactional
    public String terminatedCycleAdminTask(TenantId tenantId, String cycleId, EmpId opEmpId) {
        adminTaskRepo.terminatedCycleAdminTask(tenantId, cycleId, opEmpId);
        //TODO 发送领域管理任务被终止事件,用于清理待办
        return cycleId;
    }

    @Transactional
    public void editAdminConfirmConf(AdminAffirmConfCmd cmd) {
        AdminTask dbTask = adminTaskRepo.getAdminTask(cmd.getTenantId(), cmd.getTaskId());
        adminTaskRepo.editAdminTaskConfirmConf(cmd.getTenantId(), cmd.getTaskId(), cmd);
        AdminTask before = new AdminTask(dbTask.getId(), dbTask.getCompanyId());
        AffirmTaskConf beforeConf = dbTask.getConfirmTask();
        before.setConfirmTask(beforeConf);
        AdminTask after = new AdminTask(dbTask.getId(), dbTask.getCompanyId());
        after.setConfirmTask(cmd);
        // new AdminTaskConfEdited(cmd.getAdminType(), cmd.getOpEmpId(), dbTask, after, cmd.getTaskUserIds(), TalentStatus.CONFIRMING.getStatus()).publish();
        new AdminConfirmConfEdited(cmd.getTenantId().getId(), cmd.getTaskId(),
                cmd.getAdminType(), cmd.getOpEmpId(),
                before, after, cmd.selectAll(), cmd.getTaskUserIds()).publish();
    }

    @Transactional
    public void editAdminFinishValueAuditConf(AdminFinishValueAuditConfCmd cmd) {
        AdminTask dbTask = adminTaskRepo.getAdminTask(cmd.getTenantId(), cmd.getTaskId());
        adminTaskRepo.editAdminFinishValueAuditConf(cmd.getTenantId(), cmd.getTaskId(), cmd);
        AdminTask before = new AdminTask(dbTask.getId(), dbTask.getCompanyId());
        before.setFinishValueAudit(dbTask.getFinishValueAudit());
        AdminTask after = new AdminTask(dbTask.getId(), dbTask.getCompanyId());
        after.setFinishValueAudit(cmd);
        new AdminTaskConfEdited(cmd.getAdminType(), cmd.getOpEmpId(), before, after, cmd.getTaskUserIds(), TalentStatus.FINISH_VALUE_AUDIT.getStatus()).publish();
    }

    @Transactional
    public void editAdminEditExeIndi(AdminExeIndiConfCmd cmd) {
        AdminTask dbTask = adminTaskRepo.getAdminTask(cmd.getTenantId(), cmd.getTaskId());
        adminTaskRepo.editAdminExeIndiConf(cmd.getTenantId(), cmd.getTaskId(), cmd);
        AdminTask before = new AdminTask(dbTask.getId(), dbTask.getCompanyId());
        before.setEditExeIndi(dbTask.getEditExeIndi());
        AdminTask after = new AdminTask(dbTask.getId(), dbTask.getCompanyId());
        after.setEditExeIndi(cmd);
        new AdminTaskConfEdited(cmd.getAdminType(), cmd.getOpEmpId(), before, after, cmd.getTaskUserIds(), TalentStatus.CONFIRMED.getStatus()).publish();
    }

    @Transactional
    public void editAdminInputNotify(AdminInputNotifyConfCmd cmd) {
        AdminTask dbTask = adminTaskRepo.getAdminTask(cmd.getTenantId(), cmd.getTaskId());
        adminTaskRepo.editAdminInputNotify(cmd.getTenantId(), cmd.getTaskId(), cmd);
        AdminTask before = new AdminTask(dbTask.getId(), dbTask.getCompanyId());
        before.setInputNotifyConf(dbTask.getInputNotifyConf());
        AdminTask after = new AdminTask(dbTask.getId(), dbTask.getCompanyId());
        after.setInputNotifyConf(cmd);
        new AdminTaskConfEdited(cmd.getAdminType(), cmd.getOpEmpId(), before, after, cmd.getTaskUserIds(), TalentStatus.INPUT_NOTIFY.getStatus()).publish();
    }

    @Async
    public void syncInputNotifyJobConf(String companyId, String taskId, boolean isTimelyInput, InputNotifyConf conf) {
        if (isTimelyInput) {
            List<EvalUser> evalUsers = adminTaskDao.listEvalUserInputNotify(companyId, taskId, null);
            if (CollUtil.isEmpty(evalUsers)) {
                return;
            }
            for (EvalUser evalUser : evalUsers) {
                if (CollUtil.isNotEmpty(evalUser.getResultInputEmpIds())) {
                    evalDmSvc.batchCreateTodoTask(new Name(evalUser.getTaskName()), evalUser, evalUser.getResultInputEmpIds(), 7);
                }
            }
            //变更录入通知发送状态
            adminTaskRepo.updateInputNotifyJobStatus(companyId, CollUtil.map(evalUsers, EvalUser::getId, true));
            return;
        }
        adminTaskRepo.updateInputNotifyJobConf(companyId, taskId, conf);
    }

    @Transactional
    public void editAdminScoreConf(AdminScoreConfCmd cmd) {
        AdminTask before = adminTaskRepo.getAdminTask(cmd.getTenantId(), cmd.getTaskId());
        adminTaskRepo.editAdminScoreConf(cmd.getTenantId(), cmd.getTaskId(),
                cmd.getEnterScore(), cmd.getCommentConf(), cmd.getScoreSortConf(),
                cmd.getScoreConf(), cmd.getScoreView(), cmd.getCommentReqConf());

        AdminTask after = new AdminTask(before.getId(), before.getCompanyId());
        after.setEnterScore(cmd.getEnterScore());
        after.setCommentConf(cmd.getCommentConf());
        after.setCommentReqConf(cmd.getCommentReqConf());
        after.setScoreSortConf(cmd.getScoreSortConf());
        after.setScoreConf(cmd.getScoreConf());
        after.setScoreView(cmd.getScoreView());
        new AdminTaskConfEdited(cmd.getAdminType(), cmd.getOpEmpId(), before, after, cmd.getTaskUserIds(), TalentStatus.SCORING.getStatus()).publish();
    }

    @Transactional
    public void editAdminAuditResult(AdminAuditResultCmd cmd) {
        AdminTask dbTask = adminTaskRepo.getAdminTask(cmd.getTenantId(), cmd.getTaskId());
        if (!dbTask.getAuditResult().isOpen() && cmd.isOpen()) {
            //配置通知发送设置
            if (dbTask.resultSendNotifyIsNull()) {
                CompanySysSettingModel settingModel = companySysSettingManager.querySysSetting(cmd.getTenantId().getId(), "notifySend");
                if (Objects.nonNull(settingModel) && settingModel.isResultCollectSend()) {
                    cmd.setCollectSendNotify(2);
                }
            }
        }
        adminTaskRepo.editAdminAuditResult(cmd.getTenantId(), cmd.getTaskId(), cmd);
        AdminTask before = new AdminTask(dbTask.getId(), dbTask.getCompanyId());
        before.setAuditResult(dbTask.getAuditResult());
        AdminTask after = new AdminTask(dbTask.getId(), dbTask.getCompanyId());
        after.setAuditResult(cmd);
        new AdminTaskConfEdited(cmd.getAdminType(), cmd.getOpEmpId(), before, after, cmd.getTaskUserIds(), TalentStatus.RESULTS_AUDITING.getStatus()).publish();
//        new AdminAuditResultEdited(cmd.getAdminType(), cmd.getOpEmpId(), before, after, cmd.getTaskUserIds()).publish();
    }

    @Transactional
    public void editAdminInterviewConf(AdminInterviewConfCmd cmd) {
        AdminTask dbTask = adminTaskRepo.getAdminTask(cmd.getTenantId(), cmd.getTaskId());
        adminTaskRepo.editAdminInterviewConf(cmd.getTenantId(), cmd.getTaskId(), cmd);
        AdminTask before = new AdminTask(dbTask.getId(), dbTask.getCompanyId());
        before.setInterviewConf(dbTask.getInterviewConf());
        AdminTask after = new AdminTask(dbTask.getId(), dbTask.getCompanyId());
        after.setInterviewConf(cmd);
        new AdminTaskConfEdited(cmd.getAdminType(), cmd.getOpEmpId(), before, after, cmd.getTaskUserIds(), TalentStatus.RESULTS_INTERVIEW.getStatus()).publish();
    }

    @Transactional
    public void editAdminConfirmResultConf(AdminResultConfirmCmd cmd) {
        AdminTask dbTask = adminTaskRepo.getAdminTask(cmd.getCompanyId(), cmd.getTaskId());
        adminTaskRepo.editAdminConfirmResult(cmd.getCompanyId(), cmd.getTaskId(), cmd);
        AdminTask before = new AdminTask(dbTask.getId(), dbTask.getCompanyId());
        before.setConfirmResult(dbTask.getConfirmResult());
        AdminTask after = new AdminTask(dbTask.getId(), dbTask.getCompanyId());
        after.setConfirmResult(cmd);
        new AdminTaskConfEdited(cmd.getAdminType(), cmd.getOpEmpId(), before, after, cmd.getTaskUserIds(), TalentStatus.RESULTS_AFFIRMING.getStatus()).publish();
    }

    @Transactional
    public void editAdminPubResultConf(AdminPublishResultCmd cmd) {
        AdminTask dbTask = adminTaskRepo.getAdminTask(cmd.getTenantId(), cmd.getTaskId());
        adminTaskRepo.editAdminPubResultConf(cmd.getTenantId(), cmd.getTaskId(), cmd);
        AdminTask before = new AdminTask(dbTask.getId(), dbTask.getCompanyId());
        before.setPublishResult(dbTask.getPublishResult());
        AdminTask after = new AdminTask(dbTask.getId(), dbTask.getCompanyId());
        after.setPublishResult(cmd);
        new AdminTaskConfEdited(cmd.getAdminType(), cmd.getOpEmpId(), before, after, cmd.getTaskUserIds(), TalentStatus.WAIT_PUBLISHED.getStatus()).publish();
    }

    @Transactional
    public void editAdminAppealConf(AdminAppealConfCmd cmd) {
        AdminTask dbTask = adminTaskRepo.getAdminTask(cmd.getTenantId(), cmd.getTaskId());
        adminTaskRepo.editAdminAppealConf(cmd.getTenantId(), cmd.getTaskId(), cmd);
        AdminTask before = new AdminTask(dbTask.getId(), dbTask.getCompanyId());
        before.setAppealConf(dbTask.getAppealConf());

        AdminTask after = new AdminTask(dbTask.getId(), dbTask.getCompanyId());
        after.setAppealConf(cmd);
        new AdminTaskConfEdited(cmd.getAdminType(), cmd.getOpEmpId(), before, after, cmd.getTaskUserIds(), TalentStatus.RESULTS_APPEAL.getStatus()).publish();
    }

    @Transactional
    public void editAdminDeadLineConf(AdminDeadLineConfCmd cmd) {
        AdminTask dbTask = adminTaskRepo.getAdminTask(cmd.getTenantId(), cmd.getTaskId());
        adminTaskRepo.editAdminDeadLineConf(cmd.getTenantId(), cmd.getTaskId(), cmd);
        AdminTask before = new AdminTask(dbTask.getId(), dbTask.getCompanyId());
        before.setDeadLineConf(dbTask.getDeadLineConf());

        AdminTask after = new AdminTask(dbTask.getId(), dbTask.getCompanyId());
        after.setDeadLineConf(cmd);
        new AdminTaskConfEdited(cmd.getAdminType(), cmd.getOpEmpId(), before, after, cmd.getTaskUserIds(), TalentStatus.DEAD_LINE.getStatus()).publish();
    }

    public AdminTaskDiff saveDiffAdminTaskOperation(AdminTaskConfEdited event) {
        StageConfDiffLogDmSvc logDm = new StageConfDiffLogDmSvc(new EmpParser(empFinder, new TenantId(event.getTenantId())), autoFinder, resetFinder);
        AdminTaskDiff adminTaskDiff = logDm.diffAdminTaskOperation(event.getTenantId(), event.getTaskId(), event.getTaskUserIds(),
                event.getChangedStage(), event.getBefore(), event.getAfter(), event.getOpEmpId(), event.getOpAdminType());
        trans.runTran(() -> logDm.saveDiffLog(adminTaskRepo));
        return adminTaskDiff;
    }

    public PagedList<AdminTaskOperationDo> pagedAdminTaskOp(AdminTaskOpQuery qry) {
        PagedList<AdminTaskOperationDo> ops = adminTaskDao.pagedAdminTaskOp(qry);
        return ops;
    }

    public PagedList<ExportTaskOperationPo> pagedExportAdminTaskOp(ExportTaskOpQuery qry) {
        PagedList<ExportTaskOperationPo> exportPage = new PagedList<>();
        PagedList<AdminTaskOperationDo> ops = pagedAdminTaskOp(new ToDataBuilder<>(qry, AdminTaskOpQuery.class).data());
        exportPage.setPageNo(qry.getPageNo());
        exportPage.setPageSize(qry.getPageSize());
        exportPage.setTotalRow(ops.getTotalRow());
        exportPage.setTotalPage(ops.getTotalPage());
        if (CollUtil.isEmpty(ops.getData())) {
            return exportPage;
        }
        List<ExportTaskOperationPo> operationPos = new ArrayList<>();
        for (AdminTaskOperationDo op : ops) {
            ExportTaskOperationPo po = new ToDataBuilder<>(op, ExportTaskOperationPo.class).data();
            po.setOperatorDetails(op.joinDetails());
            operationPos.add(po);
        }
        exportPage.addAll(operationPos);
        return exportPage;
    }

    public List<ResultAuditRecordPo> listResultAuditRecord(String companyId, String taskUserId) {
        return adminTaskDao.listResultAuditRecord(companyId, taskUserId);
    }

    public AdminTaskParseAddEmpPo parseAddEvalRepeatEmp(AddEvalEmpCmd cmd) {
        AdminTaskParseAddEmpPo adminTaskParseAddEmpPo = new AdminTaskParseAddEmpPo();
        CompanyDo companyDo = companyDao.getCompany(cmd.getTenantId());
        cmd.setPermEndTime(companyDo.getPermEndTime());
        List<String> adminScopeOrgIds = kpiOrgDao.listAdminScopePrivOrgIds(cmd.getTenantId().getId(), cmd.getOpEmpId().getId(), null);
        //解析用户选择的具体被考核人
        ParseAddEmpEvalQuery empEvalQuery = new ParseAddEmpEvalQuery(cmd.getTenantId(), cmd.getEmps(), cmd.getRoles(), cmd.getRoleEmps()
                , adminScopeOrgIds, cmd.getOrgs(), cmd.hasEmps(), cmd.hasRoleEmps(), cmd.hasRoles(), cmd.hasOrgs(),
                cmd.getOnTheJobStatus(), cmd.getEntryDateStart(), cmd.getEntryDateEnd(), cmd.getEmpStatus(), cmd.getEvalGroupIds());
        List<EvalEmp> allEmps = empEvalDao.listParseAddEmpEmps(empEvalQuery);
        //解析出来是空的，不处理
        if (CollUtil.isEmpty(allEmps)) {
            return new AdminTaskParseAddEmpPo();
        }

        EmpEvalAtCycleQuery query = new EmpEvalAtCycleQuery(cmd.getTenantId(), cmd.getTaskId(), allEmps);
        List<EvalUser> existed = empEvalDao.listExistEmpAtCycle(query);
        adminTaskParseAddEmpPo.buildRepeatEmps(allEmps, existed);
        //显示上级 full supOrgName
        List<String> curOrgIds = adminTaskParseAddEmpPo.getRepeatOrgIds();
        if (CollUtil.isNotEmpty(curOrgIds)) {
            List<KpiOrgSupNames> supNames = kpiOrgDao.listOrgSupNames(query.getTenantId(), curOrgIds);
            adminTaskParseAddEmpPo.setRepeatSupOrgNames(supNames);
        }
        return adminTaskParseAddEmpPo;
    }

    public boolean addEvalEmpsNew(AddEvalEmpCmd cmd) {
        if (CollUtil.isEmpty(cmd.getEmps()) && CollUtil.isEmpty(cmd.getEvalGroupIds())) {
            log.warn("新增被考核人参数为空！！！");
            return false;
        }
        log.info("新增被考核人开始：" + JSONObject.toJSONString(cmd));
        addEval(cmd);
        log.info("新增被考核人结束");
        return cmd.isExceed();
    }

//    public boolean addEvalEmps(AddEvalEmpCmd cmd) {
//        CompanyDo companyDo = companyDao.getCompany(cmd.getTenantId());
//        cmd.setPermEndTime(companyDo.getPermEndTime());
//        List<String> adminScopeOrgIds = kpiOrgDao.listAdminScopePrivOrgIds(cmd.getTenantId().getId(), cmd.getOpEmpId().getId(), null);
//        //先添加用户选择的具体被考核人
//        if (cmd.hasEmps() || cmd.hasRoleEmps()) {
//            //将角色员工添加入emps中
//            if (CollUtil.isNotEmpty(cmd.getRoleEmps())) {
//                List<EvalEmp> roleEmps = empEvalDao.listRoleEmpOrg(cmd.getTenantId(),
//                        cmd.getRoleEmps(), adminScopeOrgIds,cmd.getOnTheJobStatus(),cmd.getEntryDateStart(),cmd.getEntryDateEnd());
//                cmd.getEmps().addAll(roleEmps);
//                cmd.distinctEmps();
//            }
//            addEval(cmd);
//        }
//        if (cmd.isExceed()) {
//            return cmd.isExceed();
//        }
//        //再添加用户选择的部门下的人
//        Integer pageNo = 0;
//        Integer pageSize = 100;
//        while (true) {
//            List<EvalEmp> evalEmps = new ArrayList<>();
//            if (cmd.hasOrgs()) {
//                evalEmps.addAll(empEvalDao.listParseOrgEmp(cmd.getTenantId(), cmd.getOrgs(), pageNo, pageSize,
//                        cmd.getOnTheJobStatus(),cmd.getEntryDateStart(),cmd.getEntryDateEnd()));
//            }
//            if (cmd.hasRoles()) {
//                evalEmps.addAll(empEvalDao.listParseRoleEmp(cmd.getTenantId(), cmd.getRoles(), adminScopeOrgIds, pageNo, pageSize,
//                        cmd.getOnTheJobStatus(),cmd.getEntryDateStart(),cmd.getEntryDateEnd()));
//            }
//            if (CollUtil.isEmpty(evalEmps)) {
//                break;
//            }
//            evalEmps = evalEmps.stream().distinct().collect(Collectors.toList());
//            log.info("部门或角色下解析出的人员：" + JSONObject.toJSONString(evalEmps));
//            cmd.setEmps(evalEmps);
//            addEval(cmd);
//            pageNo = pageNo + pageSize;
//        }
//        return cmd.isExceed();
//    }

    private void addEval(AddEvalEmpCmd cmd) {
        if (CollUtil.isEmpty(cmd.getEmps()) && CollUtil.isEmpty(cmd.getEvalGroupIds())) {
            return;
        }
        if (CollUtil.isNotEmpty(cmd.getEvalGroupIds())) {
            List<String> adminScopeOrgIds = kpiOrgDao.listAdminScopePrivOrgIds(cmd.getTenantId().getId(), cmd.getOpEmpId().getId(), null);
            // 解析用户选择的具体被考核人
            ParseAddEmpEvalQuery empEvalQuery = new ParseAddEmpEvalQuery(cmd.getTenantId(), cmd.getEmps(), cmd.getRoles(), cmd.getRoleEmps()
                    , adminScopeOrgIds, cmd.getOrgs(), cmd.hasEmps(), cmd.hasRoleEmps(), cmd.hasRoles(), cmd.hasOrgs(),
                    cmd.getOnTheJobStatus(), cmd.getEntryDateStart(), cmd.getEntryDateEnd(), cmd.getEmpStatus(), cmd.getEvalGroupIds());
            List<EvalEmp> evalEmps = empEvalDao.listParseEvalGroupEmp(empEvalQuery, 0, Integer.MAX_VALUE);
            cmd.addEvalEmps(evalEmps);
        }
        EmpEvalAtCycleQuery query = new EmpEvalAtCycleQuery(cmd.getTenantId(), cmd.getTaskId(), cmd.getEmps());
        List<EvalEmp> existed = empEvalDao.listExistAtCycle(query);
        cmd.filterExistsEmps(existed);
        try {
            if (!cmd.isExceed()) {
                self.addEvalEmp(cmd);
            }
        } catch (Exception e) {
            log.error("添加被考核人异常:" + e.getMessage(), e);
        }
    }


    public List<PerfTaskExecuteBatch> listExecuteBatchDetail(String companyId, String batchId, String opEmpId, Integer performanceType) {
        List<String> adminOrgIds = kpiOrgDao.listAdminScopePrivOrgIds(companyId, opEmpId, null);
        List<PerfTaskExecuteBatch> batchList = adminTaskDao.listExecuteBatchDetail(companyId, batchId, adminOrgIds, performanceType);
        if (batchList.isEmpty()) {
            return batchList;
        }
        // new AdminTaskExecuteBatchChanged(companyId, batchList).publish();
        return batchList.stream().filter(obj -> !(obj.getErrorCount() > 0 && (obj.getErrorCount() - obj.getPassCount()) == 0) && obj.getErrorCount() != 0).collect(Collectors.toList());
    }

    public PerfTaskExecuteBatch getExecuteBatchDetail(String companyId, String batchId) {
        List<PerfTaskExecuteBatch> batchList = adminTaskDao.listExecuteBatchDetail(companyId, batchId, null, null);
        if (batchList.isEmpty()) {
            return null;
        }
        return batchList.get(0);
    }

    public void updateExecuteBatchRead(List<String> batchIdList) {
        adminTaskRepo.updateExecuteBatchRead(batchIdList);
    }

    public static final List<String> companyIds = Arrays.asList("ece4e403-43aa-47f2-bb19-a0dd18b8e98d", "1046531");

    @Async
    public void doExportScore(EvalUserQuery query) {
        MDC.put("tid", query.getTraceId());
        Company company = this.deptEmpDao.getCompany(new TenantId(query.getCompanyId()));
        String sheetName = "";
        if (StrUtil.isNotBlank(query.getTaskId())) {
            AdminTask task = adminTaskDao.getAdminTaskMerge(new TenantId(query.getCompanyId()), new TaskId(query.getTaskId()));
            Cycle cycle = cycleDao.findCycle(new TenantId(query.getCompanyId()), task.getCycleId());
            sheetName = String.format("%s(%s)", task.getTaskName(), cycle.getCycleValue().tagName());
        }
        if (StrUtil.isNotBlank(query.getCycleId())) {
            Cycle cycle = cycleDao.findCycle(new TenantId(query.getCompanyId()), query.getCycleId());
            sheetName = String.format("%s(%s)", cycle.getCycleName(), cycle.getCycleValue().format("至"));
        }
        query.setSeeAnonymity(tenantSysConfRepo.isOpen(query.getCompanyId(), query.getSeeAnonymityCode()));
        log.info("==== 匿名权限:{}", query.isSeeAnonymity());

        //如果开启维度评等级
        TenantSysConf openIndLevelGroup = tenantSysConfRepo.getCompanySysInfo(query.getCompanyId(), "ind_level_group_202306027");
        if (openIndLevelGroup.isOpen()){
            query.initExportTitleNamesWithTypeResult();
        }else {
            query.initExportTitleNames();
        }
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            Supplier<PagedList> pagedDataFun = () -> {
                PagedList<? extends EvalResultPo> resultPos = this.pagedEvalResult(query);
                List<BaseResultExcelPo> collect = resultPos.stream().map(soureData -> {
                    soureData.exportScoreWeight(query.isOpenWeight());
                    soureData.setKpiTypeResult();
                    if (query.isPersonPerformance()) {
                        EvalResultExcelPo target = new EvalResultExcelPo();
                        BeanUtils.copyProperties(soureData, target);
                        return target;
                    } else {
                        OrgResultExcelPo target = new OrgResultExcelPo();
                        BeanUtils.copyProperties(soureData, target);
                        return target;
                    }
                }).collect(Collectors.toList());
                PagedList others = resultPos;
                others.clear();
                others.addAll(collect);

                Set<String> taskUserIds = new HashSet<>();
                for (BaseResultExcelPo datum : resultPos.getData()) {
                    taskUserIds.add(datum.getId());
                }
                if (CollUtil.isEmpty(taskUserIds)) {
                    return others;
                }
                //添加评分人
                if (companyIds.contains(query.getCompanyId())) {
                    return others;
                }
                ScorerTotalDmSvc scorerTotalDmSvc = bdScorerTotalDmSvc(query.getCompanyId(), new ArrayList<>(taskUserIds), query.isSeeAnonymity());
                for (BaseResultExcelPo datum : resultPos.getData()) {
                    datum.initClsColumn(query.getCusColumnFiledIds(), scorerTotalDmSvc.bdRaterAsMap());
                }
                query.addRater(scorerTotalDmSvc);
                return others;
            };
            final ExcelWriterBuilder builder = EasyExcel.write(outputStream).head(query.getExportTitleHeads()).registerWriteHandler(AutoColumnWidthStyleStrategy.createAutoWidthStyleStrategy(18));
            final DCClsExport excelExport = new DCClsExport(builder, query.getExportTitleHeads(), query.isPersonPerformance() ? EvalResultExcelPo.class : OrgResultExcelPo.class, pagedDataFun, query, "-");
            excelExport.excelWrite(sanitizeSheetName(sheetName));
            String fileName = "考核结果" + DateUtil.format(new DateTime(), DatePattern.PURE_DATETIME_FORMAT) + ".xlsx";
            this.appAcl.exportFile(company.getDingCorpId(), query.getOpEmpId(), fileName, outputStream.toByteArray());
        } catch (Exception e) {
            log.error("考核结果导出exception: " + e.getMessage(), e);
        }
        MDC.clear();
    }

    private static String sanitizeSheetName(String rawName) {
        // 替换所有非法字符为下划线
        String sanitized = rawName.replaceAll("[\\[\\]:?*\\\\/]", "_");

        // 截断到31个字符（Excel限制）
        return sanitized.length() > 31 ? sanitized.substring(0, 31) : sanitized;
    }

    public void testExport(EvalUserQuery query) throws IOException {
        Company company = this.deptEmpDao.getCompany(new TenantId(query.getCompanyId()));
        // 创建一个临时文件路径来存储生成的Excel文件
        Path tempFile = Files.createTempFile("temp", ".xlsx");
        Path fileName = tempFile.getFileName();

        // 使用EasyExcel生成Excel文件
        ArrayList<String> head = new ArrayList<>();
        head.add("one");
        head.add("two");
        ArrayList<List<String>> headList = new ArrayList<>();
        headList.add(head);

        List<String> data = new ArrayList<>();
        data.add("1");
        data.add("2");
        EasyExcel.write(tempFile.toFile())
                .head(headList)
                .sheet("Sheet1")
                .doWrite(data);
        // 读取文件并写入到byte数组
        byte[] bytes = Files.readAllBytes(tempFile);
        this.appAcl.exportFile(company.getDingCorpId(), query.getOpEmpId(), "temp", bytes);
    }


    public PagedList<? extends EvalResultPo> pagedEvalResult(EvalUserQuery query) {
        query.setAdminOrgIds(kpiOrgDao.listAdminScopePrivOrgIds(query.getCompanyId(), query.getOpEmpId(), null));
        if (StrUtil.isNotBlank(query.getOrgId())) {
            query.setOrgIds(kpiOrgDao.listAllChildOrgIds(new TenantId(query.getCompanyId()), StrUtil.splitTrim(query.getOrgId(), ",")));
        }
        PagedList<EvalResultPo> resultPos = taskUserDao.pagedEvalResult(query);
        List<EvalResultPo> rss = resultPos.getData();
        if (CollUtil.isEmpty(rss)) {
            return resultPos;
        }
        List<String> taskUserIds = resultPos.getData().stream().map(po -> po.getId()).collect(Collectors.toList());

        ListWrap<AuditResultEvalType> typeLevelGroups = calibratedDao.listTypeLevelResultPo(query.getCompanyId(), taskUserIds);
        resultPos.forEach(po -> po.setTypes(typeLevelGroups.groupGet(po.getId())));

        //显示上级
        //List<String> curOrgIds = rss.stream().filter(r -> StrUtil.isNotBlank(r.matchOrId())).map(r -> r.matchOrId()).collect(Collectors.toList());
        //List<KpiOrgSupNames> supNames = kpiOrgDao.listOrgSupNames(new TenantId(query.getCompanyId()), curOrgIds);
        //添加评分人
        rss.forEach(r -> {
            r.repaceOrgNamePath();
        });

        return resultPos;
    }

    public ScorerTotalDmSvc bdScorerTotalDmSvc(String companyId, List<String> taskUserIds, boolean isSeeAnonymity) {
        List<ScorerItem> scorerItems = taskUserDao.listScorerTotal(companyId, taskUserIds);
        if (CollUtil.isEmpty(scorerItems)) {
            return new ScorerTotalDmSvc();
        }
        Map<String, EmpEvalRule> ruleMap = empEvalDao.listBaseEvalRuleMap(companyId, taskUserIds);
        List<KpiEmp> empList = kpiEmpDao.listByEmp(new TenantId(companyId), CollUtil.map(scorerItems, s -> s.getScorerId(), true).stream().distinct().collect(Collectors.toList()));
        ScorerTotalDmSvc totalDmSvc = new ScorerTotalDmSvc(scorerItems, empList, isSeeAnonymity, ruleMap);
        totalDmSvc.initScorer();
        totalDmSvc.computeScore();
        return totalDmSvc;
    }

    //需要删除的
    public ScorerTotalDmSvc listScorerTotal(String companyId, String taskId) {
        List<ScorerItem> scorerItems = taskUserDao.listScorerTotal(companyId, Arrays.asList(taskId));
        if (CollUtil.isEmpty(scorerItems)) {
            return null;
        }
        Map<String, KpiEmp> empMap = kpiEmpDao.listByEmpAsMap(new TenantId(companyId), CollUtil.map(scorerItems, s -> s.getScorerId(), true).stream().distinct().collect(Collectors.toList()));
        ScorerTotalDmSvc totalDmSvc = new ScorerTotalDmSvc(scorerItems, empMap, true);
        totalDmSvc.initScorer();
        totalDmSvc.computeScore();
        return totalDmSvc;
    }

    @Async
    public void fixEmpEvalPath(String tid, String companyId) {
        MDC.put("tid", tid);
        HashMap<String, EmpOrganization> cached = new HashMap<>();
        TenantId tenantId = new TenantId(companyId);
        List<FixPerfEvaluateTaskUserDo> userDos;
        int last = 0;
        int pageNo = 1;
        while (!(userDos = taskUserDao.listV1NoneOrgTaskUser(companyId, pageNo)).isEmpty()) {
            pageNo++;
            last = userDos.size();
            for (FixPerfEvaluateTaskUserDo evalUser : userDos) {
                String atOrgId = evalUser.getOrgId();
                if (StrUtil.isBlank(evalUser.getOrgId()) && StrUtil.isBlank(evalUser.getEvalOrgId())) {//完全没有数据
                    List<String> names = StrUtil.splitTrim(evalUser.getEmpOrgName(), ";");//有名字则匹配一下
                    List<String> orgNames = new ArrayList<>();
                    for (String name : names) {
                        List<String> strings = StrUtil.splitTrim(name, "/");//有名字则匹配一下
                        orgNames.add(strings.get(strings.size() - 1));
                    }
                    List<EmpRefOrgDo> empRefOrgDos = kpiOrgDao.listFixAtOrgIds(tenantId, new EmpId(evalUser.getEmpId()), orgNames);
                    if (CollUtil.isEmpty(empRefOrgDos)) {
                        log.error("找不到所在部门 taskUserId:{}", evalUser.getId());
                        continue;
                    }
                    EmpRefOrgDo empRefOrgDo = empRefOrgDos.get(0);
                    atOrgId = empRefOrgDo.getOrgId();
                } else {
                    atOrgId = StrUtil.isNotBlank(evalUser.getEvalOrgId()) ? evalUser.getEvalOrgId() : evalUser.getOrgId();
                }
                EmpOrganization path;
                if (cached.containsKey(atOrgId)) {
                    path = cached.get(atOrgId);
                } else {
                    List<EmpOrganization> paths = deptEmpDao.listDeptWithNamePath(tenantId, Arrays.asList(atOrgId));
                    path = CollUtil.isEmpty(paths) ? null : paths.get(0);
                }
                if (path == null) {
                    log.error("找不到所在部门 taskUserId:{},orgId:{}", evalUser.getId(), atOrgId);
                    continue;
                }
                cached.put(atOrgId, path);
                evalUser.acceptOrgPath(path.getOrgCode(), path.getNamePath(), path.getPathHight());
                evalUser.setOrgId(atOrgId);
                self.updateOrgPath(evalUser);
            }

        }
    }

    @Async
    public void fixDeadLineConfBatch(String tid) {
        MDC.put("tid", tid);
        //查询所有有效公司列表
        List<Company> companies = companyDao.allRunningCompany(2000000);
        if (CollectionUtils.isEmpty(companies)) {
            log.info("没有使用中的公司");
            return;
        }
        for (Company company : companies) {
            TenantId companyId = new TenantId(company.getId());
            List<PerfEvaluateTaskBaseDo> bases = adminTaskDao.listFixInterviewDeadLineConf(companyId, null);
            log.info(" 首次开启查询任务 bases.size:{}", bases.size());
            log.info(" 开始 订正DeadLineConf base ");
            bases.forEach(baseDo -> {
                if (Objects.isNull(baseDo.getDeadLineConf()) || Objects.isNull(baseDo.getDeadLineConf().getTaskResultInterview())) {
                    return;
                }
                baseDo.getDeadLineConf().getTaskResultInterview().setAutoSkipHandlerType(2);
                adminTaskRepo.editAdminDeadLineConf(companyId, baseDo.getId(), baseDo.getDeadLineConf());
                log.info(" 考核任务订正完成！ companyId：{}， base.getId：{}", companyId, baseDo.getId());
                //rule规则
                List<EmpEvalRule> rules = empEvalDao.listFixBaseEvalRuleDeadLineConf(companyId.getId(), baseDo.getId(), null);
                log.info(" 开始检索考核任务规则！ rules.size：{}", rules.size());
                log.info(" 开始 订正考核任务规则 DeadLineConf rule rules.size:{} ", rules.size());
                rules.forEach(rule -> {
                    if (Objects.isNull(rule.getDeadLineConf()) || Objects.isNull(rule.getDeadLineConf().getTaskResultInterview())) {
                        return;
                    }
                    rule.setCompanyId(companyId);
                    rule.getDeadLineConf().getTaskResultInterview().setAutoSkipHandlerType(2);
                    evalRuleRepo.editDeadLineConf(companyId, rule);
                });
                log.info(" 订正考核任务规则完成！DeadLineConf  rule ");
            });
            log.info(" 订正 任务 DeadLineConf base 完成 ");
        }
        log.info(" 订正 任务 InterviewConfirmConf 结束");
        //  MDC.clear();
    }

    @Transactional
    public void fixDeadLineConf(String companyId, String taskId) {
        List<PerfEvaluateTaskBaseDo> pagedBase = adminTaskDao.listFixInterviewDeadLineConf(new TenantId(companyId), taskId);
        if (CollUtil.isEmpty(pagedBase)) {
            return;
        }
        PerfEvaluateTaskBaseDo base = pagedBase.get(0);
        base.getDeadLineConf().getTaskResultInterview().setAutoSkipHandlerType(2);
        adminTaskRepo.editAdminDeadLineConf(new TenantId(companyId), base.getId(), base.getDeadLineConf());
        List<EmpEvalRule> rules = empEvalDao.listFixBaseEvalRuleDeadLineConf(companyId, base.getId(), null);
        if (CollUtil.isEmpty(rules)) {
            return;
        }

        log.info(" 开始 订正考核任务规则fixDeadLineConf.DeadLineConf rule rulePageNo:1, rulePageds.size:500 ");
        for (EmpEvalRule rule : rules) {
            if (Objects.isNull(rule.getDeadLineConf()) || Objects.isNull(rule.getDeadLineConf().getTaskResultInterview())) {
                continue;
            }
            rule.setCompanyId(new TenantId(companyId));
            rule.getDeadLineConf().getTaskResultInterview().setAutoSkipHandlerType(2);
            evalRuleRepo.editDeadLineConf(new TenantId(companyId), rule);
        }
        log.info(" 订正考核任务规则完成！fixDeadLineConf.DeadLineConf  rule rulePagedso:1, rulePageds.size:500 ");
    }

    @Transactional
    public void updateOrgPath(FixPerfEvaluateTaskUserDo evalUser) {
        taskUserDao.updateOrgPath(evalUser);
    }

    public List<String> listV1NoneOrgTaskUserCompany() {
        return taskUserDao.listV1NoneOrgTaskUserCompany(100);
    }

    public PerfEvaluateTaskUserLevelCountVO queryTaskUserLevelCount(TaskGradeCountQuery query) {
        query.setAdminOrgIds(kpiOrgDao.listAdminScopePrivOrgIds(query.getCompanyId(), query.getOpEmpId(), null));
        if (query.isPriv()) {
            query.setTaskIds(cycleDao.listTaskPrivByOpEmpId(query.getCompanyId(), query.getCycleId(), query.getOpEmpId()));
        }
        PerfEvaluateTaskUserLevelCountVO taskUserLevelCountVO = new PerfEvaluateTaskUserLevelCountVO();
        taskUserLevelCountVO.setTotalEmpNum(adminTaskDao.countTaskUserNum(query));
        List<PerfEvaluateTaskUserLevelVO> levelList = adminTaskDao.countLevelNum(query);
        if (CollectionUtils.isEmpty(levelList)) {
            return taskUserLevelCountVO;
        }
        //查询等级设置
        Map<String, GradeStep> stepMap = gradeDao.gradeStepByIdAsMap(new TenantId(query.getCompanyId()), CollUtil.map(levelList, l -> l.getStepId(), true));
        for (PerfEvaluateTaskUserLevelVO levelVO : levelList) {
            if (Objects.isNull(levelVO.getEmpNum())) {
                continue;
            }
            levelVO.setRatio(levelVO.getEmpNum().divide(taskUserLevelCountVO.getTotalEmpNum(), 2, BigDecimal.ROUND_HALF_UP));
            levelVO.setStepSort(stepMap.get(levelVO.getStepId()).getSort());
        }
        levelList.sort(Comparator.comparing(PerfEvaluateTaskUserLevelVO::getStepSort));
        taskUserLevelCountVO.setLevelList(levelList);
        return taskUserLevelCountVO;
    }

    public List<ScoreSortConfPo> getScoreSortConf(String companyId, List<String> taskIds) {
        return adminTaskDao.getScoreSortConf(companyId, taskIds);
    }

    @Transactional
    public void aVoid() {
        new CancelTodoEvent(new TenantId("1"), new ArrayList<>(), "", MsgSceneEnum.FINISH_VALUE_AUDIT.getType()).publish();
    }

    @Async
    public void downloadAdminTaskOp(ExportTaskOpQuery qry) {
        MDC.put("tid", qry.getTid());
        String fileName = "考核任务操作日志" + DateTimeUtils.date2StrDate(new Date(), DateTimeUtils.FORMAT_yyyyMMddHHmmss)
                + BusinessConstant.XSSF_PREFIX;
        String receiverId = qry.getOpEmpId();
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            DataClassMergeStrategy mergeStrategy = new DataClassMergeStrategy(ExportTaskOperationPo.class, qry.getPageSize());
            Supplier<PagedList> pagedDataFun = () -> this.pagedExportAdminTaskOp(qry);
            ExcelWriterBuilder builder = EasyExcel.write(outputStream, ExportTaskOperationPo.class);
            new DataClassExcelExport(builder, mergeStrategy, pagedDataFun, qry).excelWrite();
            this.appAcl.sendFile(qry.getTenantId(), receiverId, fileName, outputStream.toByteArray());
        } catch (IOException e) {
            log.error("export report exception:" + e.getMessage(), e);
        }
    }

    /**
     * 更新任务归档状态
     *
     * @param companyId
     * @param taskId
     * @param opEmpId
     * @param archiveStatus 任务归档状态：0 未归档 1已归档
     * @param opAdminType
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer updateArchiveStatus(String companyId, String taskId, String opEmpId, Integer archiveStatus, String opAdminType) {
        PerfEvaluateTaskBaseDo taskInfo = adminTaskDao.getByTaskId(companyId, taskId);
        if (taskInfo == null) {
            throw new KpiI18NException("考核任务不存在！");
        }

        log.info(" terminatedCnt:{}，finishCnt：{}，totalCnt：{}", taskInfo.getTerminatedCnt(), taskInfo.getFinishCnt(), taskInfo.getTotalCnt());
        int terminatedCnt = Objects.isNull(taskInfo.getTerminatedCnt()) ? 0 : taskInfo.getTerminatedCnt();
        int finishCnt = Objects.isNull(taskInfo.getFinishCnt()) ? 0 : taskInfo.getFinishCnt();
        int totalCnt = Objects.isNull(taskInfo.getTotalCnt()) ? 0 : taskInfo.getTotalCnt();

        TenantId tenantId = new TenantId(companyId);
        AdminTask adminTask = new AdminTask();
        adminTask.setId(taskId);
        adminTask.setArchiveStatus(taskInfo.getArchiveStatus());
        adminTask.setUpdatedUser(opEmpId);
        adminTask.setCompanyId(tenantId);
        adminTask.setTotalCnt(totalCnt);
        // 对任务进行归档时，需校验是不是所有人都已经完成了考核
        if (archiveStatus == 1) {
            // 有申诉中（考核任务状态为已完成）的考核任务不允许归档,
            Integer hasAppealUser = adminTaskDao.countHasAppealUser(companyId, taskId);

            int permitArchivedCount = finishCnt + terminatedCnt - hasAppealUser;
            // 已参与人数 = 已完成人数+已终止人数,说明所有人都完成了考核，存在考核未完成/未发起的需管理员手动移除/终止才能进行归档
            if (totalCnt == permitArchivedCount) {
                adminTaskRepo.updateArchiveStatus(adminTask, archiveStatus, opAdminType);
                // 清除钉钉待办以及系统待办
                List<CompanyMsgCenter> todos = centerRepo.finishBatchByQuery(tenantId, Collections.singletonList(taskInfo.getId()), null);
                CancelRemoteTodoEvent cancelRemoteTodoEvent = new CancelRemoteTodoEvent(tenantId, todos);
                cancelRemoteTodoEvent.addTodos(todos);
                cancelRemoteTodoEvent.publish();
                return null;
            } else {
                log.info("任务 {} 未能归档，还有 {} 人未完成考核", taskId, totalCnt - permitArchivedCount);
                // 需返回还有多少人未完成考核
                return totalCnt - permitArchivedCount;
            }
        } else {
            // 保存取消归档的操作日志
            adminTaskRepo.updateArchiveStatus(adminTask, taskInfo.getArchiveStatus(), opAdminType);
            return null;
        }
    }

    public Integer countUnfinishedUser(String companyId, String taskId) {
        PerfEvaluateTaskBaseDo taskInfo = adminTaskDao.getByTaskId(companyId, taskId);
        if (Objects.isNull(taskInfo)) {
            throw new KpiI18NException("考核任务不存在！");
        }
        // 有申诉中（考核任务状态为已完成）的考核任务不允许归档,
        Integer hasAppealUser = adminTaskDao.countHasAppealUser(companyId, taskId);
        log.info(" terminatedCnt:{}，finishCnt：{}，totalCnt：{}", taskInfo.getTerminatedCnt(), taskInfo.getFinishCnt(), taskInfo.getTotalCnt());
        int terminatedCnt = Objects.isNull(taskInfo.getTerminatedCnt()) ? 0 : taskInfo.getTerminatedCnt();
        int finishCnt = Objects.isNull(taskInfo.getFinishCnt()) ? 0 : taskInfo.getFinishCnt();
        int totalCnt = Objects.isNull(taskInfo.getTotalCnt()) ? 0 : taskInfo.getTotalCnt();
        int unfinishedCount = totalCnt - (finishCnt + terminatedCnt);
        return hasAppealUser > 0 ? unfinishedCount + hasAppealUser : unfinishedCount;
    }

    public SingleResponse importPeerRaters(MultipartFile file, String taskId, String companyId, String empId) throws Exception {
        if (StrUtil.isEmpty(taskId)) {
            return SingleResponse.of("考核任务id不能为空");
        }
        ExcelUtil<PeerRatersImportCmd> util = new ExcelUtil<>(PeerRatersImportCmd.class);
        List<PeerRatersImportCmd> importCmds = util.importExcel(file.getInputStream());

        // 未查到的考核人
        List<String> notFoundTaskUsers = new ArrayList<>();
        List<String> notFoundPeerRaters = new ArrayList<>();
        List<String> repeatNames = new ArrayList<>();
        if (CollUtil.isNotEmpty(importCmds)) {
            importCmds = importCmds.stream().filter(s -> StrUtil.isNotBlank(s.getTaskUser())).collect(Collectors.toList());
            importCmds = importCmds.stream().filter(s -> StrUtil.isNotBlank(s.getPeerRaters())).collect(Collectors.toList());
            // 检验表格数据
            List<PeerRatersImportCmd> checkTaskUsers = importCmds.stream()
                    .filter(s -> ObjectUtil.isNull(s.getTaskUser())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(checkTaskUsers)) {
                throw new RuntimeException("表格考核人数据有问题");
            }
            List<PeerRatersImportCmd> checkPeerRaters = importCmds.stream()
                    .filter(s -> ObjectUtil.isNull(s.getPeerRaters())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(checkPeerRaters)) {
                throw new RuntimeException("表格评分人数据有问题");
            }

            importCmds.stream().forEach(s -> s.setPeerRatersList(Arrays.asList(s.getPeerRaters().trim().split("[,，；; ]"))));
            log.info("环评导入表数据={}", JSONUtil.toJsonStr(importCmds));

            List<String> peerRaterNames = importCmds.stream()
                    .flatMap(s -> Arrays.stream(s.getPeerRaters().trim().split("[,，；; ]")))
                    .distinct().collect(Collectors.toList());
            // 同级评分人
            List<Employee> peerRaters = kpiEmpDao.listByEmpNames(companyId, peerRaterNames);
            if (CollUtil.isEmpty(peerRaters)) {
                notFoundPeerRaters = peerRaterNames;
            } else if (peerRaterNames.size() != peerRaters.size()) {
                peerRaterNames.removeAll(peerRaters.stream().map(s -> s.getName()).collect(Collectors.toList()));
                notFoundPeerRaters = peerRaterNames;
            }

            // 处理评分人 重名问题
            Map<String, List<Employee>> repeatPeerRatersMap = peerRaters.stream().collect(Collectors.groupingBy(Employee::getName));
            repeatNames = repeatPeerRatersMap.keySet()
                    .stream().filter(s -> repeatPeerRatersMap.get(s).size() > 1).collect(Collectors.toList());
            List<String> finalRepeatNames = repeatNames;
            peerRaters = peerRaters.stream().filter(s -> !finalRepeatNames.contains(s.getName())).collect(Collectors.toList());

            log.info("环评导入评分人信息={}", JSONUtil.toJsonStr(peerRaters));
            Map<String, String> peerRatersMap = peerRaters.stream().collect(Collectors.toMap(Employee::getName, Employee::getEmployeeId));

            importCmds.forEach(s -> {
                List<String> peerRaterIds = s.getPeerRatersList().stream().map(r -> peerRatersMap.get(r)).
                        filter(o -> ObjectUtil.isNotNull(o)).collect(Collectors.toList());
                s.setPeerRaterIds(peerRaterIds);
            });

            Map<String, List<String>> taskUserPeerRaterIdMap = importCmds.stream().collect(Collectors.toMap(PeerRatersImportCmd::getTaskUser, PeerRatersImportCmd::getPeerRaterIds));

            List<String> taskUserNames = importCmds.stream().map(s -> s.getTaskUser()).collect(Collectors.toList());

            // 被考核人
            List<Employee> taskUsers = kpiEmpDao.listByEmpNames(companyId, taskUserNames);
            if (CollUtil.isEmpty(taskUsers)) {
                notFoundTaskUsers = taskUserNames;
            } else if (taskUserNames.size() != taskUsers.size()) {
                taskUserNames.removeAll(taskUsers.stream().map(s -> s.getName()).collect(Collectors.toList()));
                notFoundTaskUsers = taskUserNames;
            }

            List<String> taskUserEmpIds = taskUsers.stream().map(s -> s.getEmployeeId()).collect(Collectors.toList());
            List<PerfEvaluateTaskUserDo> perfEvaluateTaskUserDos = taskUserDao.listTaskUserByEmpIds(companyId, taskId, taskUserEmpIds);

            // 只有在完成值审核和确认状态才能进行导出环评人
            perfEvaluateTaskUserDos = perfEvaluateTaskUserDos.stream().
                    filter(s -> s.getTaskStatus().equals("confirmed") || s.getTaskStatus().equals("finishValueAudit")).collect(Collectors.toList());
            TaskMutualItemQuery query = new TaskMutualItemQuery();
            InviteMutualCmd inviteMutualCmd;
            for (PerfEvaluateTaskUserDo userDo : perfEvaluateTaskUserDos) {
                query.setEmpId(userDo.getEmpId());
                query.setQueryType("add");
                query.setScene("peer_score");
                query.setTaskId(taskId);
                query.setTaskUserId(userDo.getId());
                query.setCompanyId(companyId);
                query.setOpEmpId(empId);
                List<InviteItem> inviteItems = empEvalAppSvc.listNeedSetMutualItem(query);

                inviteMutualCmd = new InviteMutualCmd();
                inviteMutualCmd.setTaskId(taskId);
                inviteMutualCmd.setTaskUserId(userDo.getId());
                inviteMutualCmd.setScene("peer_score");
                inviteMutualCmd.setEmpId(userDo.getEmpId());

                List<String> peerRaterIds = taskUserPeerRaterIdMap.get(userDo.getEmpName());
                List<MutualApprover> mutualApprovers = new ArrayList<>();
                MutualApprover mutualApprover;
                for (String id : peerRaterIds) {
                    mutualApprover = new MutualApprover();
                    mutualApprover.setApproverType("user");
                    mutualApprover.setApproverInfo(id);
                    mutualApprover.setWeight(new BigDecimal(100));
                    mutualApprovers.add(mutualApprover);
                }
                List<MutualItemAudit> mutualItemAudits = new ArrayList<>();
                MutualItemAudit mutualItemAudit;
                for (InviteItem item : inviteItems) {
                    mutualItemAudit = new MutualItemAudit();
                    mutualItemAudit.setKpiItemId(item.getKpiItemId());
                    mutualItemAudit.setMutualApprovers(mutualApprovers);
                    mutualItemAudits.add(mutualItemAudit);
                }
                inviteMutualCmd.setMutualItemAudits(mutualItemAudits);
                inviteMutualCmd.buildOp(companyId, empId);
                KpiItemUpdateFinishedValueEvent event = empEvalAppSvc.inviteMutualEmp(inviteMutualCmd);
                if (Objects.nonNull(event)) {
                    event.publish();
                }
            }
        }
        if (CollUtil.isNotEmpty(notFoundTaskUsers)) {
            return SingleResponse.of(notFoundTaskUsers.stream().collect(Collectors.joining(",")) + " 未找到被考核人");
        }
        if (CollUtil.isNotEmpty(notFoundPeerRaters)) {
            return SingleResponse.of(notFoundPeerRaters.stream().collect(Collectors.joining(",")) + " 未找到同级评分人");
        }
        if (CollUtil.isNotEmpty(repeatNames)) {
            return SingleResponse.of(repeatNames.stream().collect(Collectors.joining(",")) + " 重名的同级评分人");
        }
        return SingleResponse.buildSuccess();
    }

    public void addCommentReqConf(String companyId, String taskId, String createTime) {
        List<String> taskIds = new ArrayList<>();
        if (StrUtil.isNotBlank(taskId)) {
            taskIds = Arrays.asList(taskId.split(","));
        }

        List<String> companyIds = new ArrayList<>();
        if (StrUtil.isNotBlank(companyId)) {
            companyIds.add(companyId);
        } else {
            List<PerfEvaluateTaskBaseDo> res = adminTaskDao.listTaskCompanyIds();
            companyIds = res.stream().map(s -> s.getCompanyId()).collect(Collectors.toList());
        }

        if (CollUtil.isNotEmpty(companyIds)) {
            int loopCount = (int) Math.ceil(companyIds.size() / 100.0);
            List<List<String>> companyIdLists = Lists.partition(companyIds, loopCount);
            for (List<String> companyIdList : companyIdLists) {
                List<PerfEvaluateTaskBaseDo> tasks = adminTaskDao.listTaskByCompanyIds(companyIdList, taskIds, createTime);
                CommentRule commentRule1;
                CommentRule commentRule2;
                List<CommentRule> commentRules;
                CommentReqLevel plusOrSubItem;
                CommentReqLevel commonItem;
                CommentReqLevel scoreSummary;
                CommentNodeSet commentNodeSet;
                List<CommentNodeSet> commentNodeSets;
                CommentReqConf commentReqConf;
                for (PerfEvaluateTaskBaseDo task : tasks) {
                    commentReqConf = new CommentReqConf();
                    commentNodeSet = new CommentNodeSet();
                    commentNodeSets = new ArrayList<>();
                    commonItem = new CommentReqLevel();
                    plusOrSubItem = new CommentReqLevel();
                    scoreSummary = new CommentReqLevel();
                    ScoreCommentConf commentConf = task.getCommentConf();
                    int plusOrSubComment = commentConf.getPlusOrSubComment();
                    int scoreSummarySwitch = commentConf.getScoreSummarySwitch();
                    if (plusOrSubComment == 1) {
                        plusOrSubItem.setReqLevel(1);
                    } else if (plusOrSubComment == 0) {
                        plusOrSubItem.setReqLevel(2);
                    } else if (plusOrSubComment == -1) {
                        plusOrSubItem.setReqLevel(3);
                    }
                    if (scoreSummarySwitch == 1) {
                        scoreSummary.setReqLevel(1);
                    } else if (scoreSummarySwitch == 0) {
                        scoreSummary.setReqLevel(2);
                    } else if (scoreSummarySwitch == -1) {
                        scoreSummary.setReqLevel(3);
                    }

                    String commentFlag = commentConf.getCommentFlag();
                    if (StrUtil.equals(commentFlag, "notRequired")) {
                        commonItem.setReqLevel(2);
                    }
                    if (StrUtil.equals(commentFlag, "required")) {
                        commonItem.setReqLevel(1);
                    }
                    if (StrUtil.equals(commentFlag, "close")) {
                        commonItem.setReqLevel(3);
                    }
                    if (StrUtil.equals(commentFlag, "condition")) {
                        commonItem.setReqLevel(4);
                        commentRules = new ArrayList<>();
                        Integer commentRequiredValue = commentConf.getCommentRequiredValue();
                        Integer commentRequiredHighValue = commentConf.getCommentRequiredHighValue();
                        if (ObjectUtil.isNotNull(commentRequiredValue)) {
                            commentRule1 = new CommentRule();
                            commentRule1.setDimension(1);
                            commentRule1.setOp("<=");
                            commentRule1.setValue(commentRequiredValue.toString());
                            commentRules.add(commentRule1);
                        }
                        if (ObjectUtil.isNotNull(commentRequiredHighValue)) {
                            commentRule2 = new CommentRule();
                            commentRule2.setDimension(1);
                            commentRule2.setOp(">=");
                            commentRule2.setValue(commentRequiredHighValue.toString());
                            commentRules.add(commentRule2);
                        }
                        if (CollUtil.isNotEmpty(commentRules)) {
                            commonItem.setRules(commentRules);
                        }
                    }

                    commentNodeSet.setIndex(1);
                    commentNodeSet.setCommonItem(commonItem);
                    commentNodeSet.setPlusOrSubItem(plusOrSubItem);
                    commentNodeSet.setScoreSummary(scoreSummary);
                    commentNodeSets.add(commentNodeSet);
                    commentReqConf.setCommentNodeSet(commentNodeSets);
                    task.setCommentReqConf(commentReqConf);
                    adminTaskDao.updateTaskCommentReqConf(task);
                }
                log.info("********更新 perf_evaluate_task_base 表 comment_req_conf********");
            }
        }
    }

    public void addEvalRuleCommentReqConf(String companyId, String empEvalId, String createTime) {
        List<String> empEvalIds = new ArrayList<>();
        if (StrUtil.isNotBlank(empEvalId)) {
            empEvalIds = Arrays.asList(empEvalId.split(","));
        }

        List<String> companyIds = new ArrayList<>();
        if (StrUtil.isNotBlank(companyId)) {
            companyIds.add(companyId);
        } else {
            List<PerfEvaluateTaskBaseDo> res = adminTaskDao.listTaskCompanyIds();
            companyIds = res.stream().map(s -> s.getCompanyId()).collect(Collectors.toList());
        }

        if (CollUtil.isNotEmpty(companyIds)) {
            int loopCount = (int) Math.ceil(companyIds.size() / 100.0);
            List<List<String>> companyIdLists = Lists.partition(companyIds, loopCount);
            // 根据公司ID去查询 emp_eval_rule
            for (List<String> companyIdList : companyIdLists) {
                List<EmpEvalRuleDo> rules = adminTaskDao.listEmpEvalRuleByCompanyIds(companyIdList, empEvalIds, createTime);
                CommentRule commentRule1;
                CommentRule commentRule2;
                List<CommentRule> commentRules;
                CommentReqLevel plusOrSubItem;
                CommentReqLevel commonItem;
                CommentReqLevel scoreSummary;
                CommentNodeSet commentNodeSet;
                List<CommentNodeSet> commentNodeSets;
                CommentReqConf commentReqConf;
                for (EmpEvalRuleDo rule : rules) {
                    commentReqConf = new CommentReqConf();
                    commentNodeSet = new CommentNodeSet();
                    commentNodeSets = new ArrayList<>();
                    commonItem = new CommentReqLevel();
                    plusOrSubItem = new CommentReqLevel();
                    scoreSummary = new CommentReqLevel();
                    ScoreCommentConf commentConf = rule.getCommentConf();
                    int plusOrSubComment = commentConf.getPlusOrSubComment();
                    int scoreSummarySwitch = commentConf.getScoreSummarySwitch();
                    if (plusOrSubComment == 1) {
                        plusOrSubItem.setReqLevel(1);
                    } else if (plusOrSubComment == 0) {
                        plusOrSubItem.setReqLevel(2);
                    } else if (plusOrSubComment == -1) {
                        plusOrSubItem.setReqLevel(3);
                    }
                    if (scoreSummarySwitch == 1) {
                        scoreSummary.setReqLevel(1);
                    } else if (scoreSummarySwitch == 0) {
                        scoreSummary.setReqLevel(2);
                    } else if (scoreSummarySwitch == -1) {
                        scoreSummary.setReqLevel(3);
                    }

                    String commentFlag = commentConf.getCommentFlag();
                    if (StrUtil.equals(commentFlag, "notRequired")) {
                        commonItem.setReqLevel(2);
                    }
                    if (StrUtil.equals(commentFlag, "required")) {
                        commonItem.setReqLevel(1);
                    }
                    if (StrUtil.equals(commentFlag, "close")) {
                        commonItem.setReqLevel(3);
                    }
                    if (StrUtil.equals(commentFlag, "condition")) {
                        commonItem.setReqLevel(4);
                        commentRules = new ArrayList<>();
                        Integer commentRequiredValue = commentConf.getCommentRequiredValue();
                        Integer commentRequiredHighValue = commentConf.getCommentRequiredHighValue();
                        if (ObjectUtil.isNotNull(commentRequiredValue)) {
                            commentRule1 = new CommentRule();
                            commentRule1.setDimension(1);
                            commentRule1.setOp("<=");
                            commentRule1.setValue(commentRequiredValue.toString());
                            commentRules.add(commentRule1);
                        }
                        if (ObjectUtil.isNotNull(commentRequiredHighValue)) {
                            commentRule2 = new CommentRule();
                            commentRule2.setDimension(1);
                            commentRule2.setOp(">=");
                            commentRule2.setValue(commentRequiredHighValue.toString());
                            commentRules.add(commentRule2);
                        }
                        if (CollUtil.isNotEmpty(commentRules)) {
                            commonItem.setRules(commentRules);
                        }
                    }

                    commentNodeSet.setIndex(1);
                    commentNodeSet.setCommonItem(commonItem);
                    commentNodeSet.setPlusOrSubItem(plusOrSubItem);
                    commentNodeSet.setScoreSummary(scoreSummary);
                    commentNodeSets.add(commentNodeSet);
                    commentReqConf.setCommentNodeSet(commentNodeSets);
                    rule.setCommentReqConf(commentReqConf);
                    adminTaskDao.updateEmpEvalRuleCommentReqConf(rule);
                }
                log.info("********更新 emp_eval_rule 表 comment_req_conf********");
            }
        }
    }

    public void finishedStartBatch(String companyId, String batchId) {
        trans.runTran(() -> adminTaskRepo.updateExecuteBatchStasuts(batchId));
    }
}
