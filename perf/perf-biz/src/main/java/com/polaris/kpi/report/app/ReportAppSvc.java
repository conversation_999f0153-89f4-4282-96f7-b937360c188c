package com.polaris.kpi.report.app;

import cn.com.polaris.kpi.KpiOrgSupNames;
import cn.com.polaris.kpi.company.TenantSysConf;
import cn.com.polaris.kpi.eval.ExportEvalUser;
import cn.com.seendio.polaris.excel.*;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.fastjson.JSONObject;
import com.perf.www.common.constant.BusinessConstant;
import com.perf.www.common.utils.date.DateTimeUtils;
import com.perf.www.cons.AdminType;
import com.perf.www.dao.company.impl.AdminSetDaoImpl;
import com.perf.www.domain.entity.company.SystemAdminSetModel;
import com.perf.www.dto.TaskUserOrg;
import com.polaris.acl.dept.face.AppAcl;
import com.polaris.acl.dept.repository.DeptEmpDao;
import com.polaris.kpi.eval.app.TransactionWrap;
import com.polaris.kpi.eval.app.cycle.appsvc.CycleEvalAppSvc;
import com.polaris.kpi.eval.domain.task.entity.grade.EmpRefScoreRule;
import com.polaris.kpi.eval.domain.task.entity.grade.ScoreRange;
import com.polaris.kpi.eval.domain.task.entity.grade.ScoreRule;
import com.polaris.kpi.eval.domain.task.repo.AdminTaskOperationRepo;
import com.polaris.kpi.eval.infr.task.dao.EvaluateTaskDao;
import com.polaris.kpi.eval.infr.task.dao.GradeDao;
import com.polaris.kpi.report.domain.dmsvc.EmpYearReportDmSvc;
import com.polaris.kpi.report.domain.entity.EmpYearReport;
import com.polaris.kpi.report.domain.entity.EmpYearReportItem;
import com.polaris.kpi.report.domain.entity.ReportWeightConf;
import com.polaris.kpi.report.infr.dao.ReportDao;
import com.polaris.kpi.eval.infr.task.dao.ReportWeightSettingDao;
import com.polaris.kpi.eval.infr.task.ppojo.*;
import com.polaris.kpi.eval.infr.task.ppojo.report.*;
import com.polaris.kpi.eval.infr.task.query.report.ReportQuery;
import com.polaris.kpi.eval.infr.task.query.report.TaskReportQry;
import com.polaris.kpi.eval.infr.task.query.report.TaskUserOrgListWrap;
import com.polaris.kpi.org.domain.dept.entity.Company;
import com.polaris.kpi.org.infr.company.dao.TenantSysConfDao;
import com.polaris.kpi.org.infr.dept.dao.KpiOrgDao;
import com.polaris.kpi.org.infr.emp.pojo.EmpRefOrgDo;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lufei.ibatis.common.data.ToDataBuilder;
import org.lufei.ibatis.mapper.PagedList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lufei
 * @date 2022/2/9 10:51 上午
 */
@Service
public class ReportAppSvc {
    @Autowired
    private EvaluateTaskDao evaluateTaskDao;
    @Resource
    private KpiOrgDao orgDao;
    @Autowired
    private AdminSetDaoImpl adminSetDao;
    @Autowired
    private ReportDao reportDao;
    @Autowired
    private KpiOrgDao kpiOrgDao;
    @Autowired
    private ReportWeightSettingDao reportWeightSettingDao;
    @Autowired
    private DeptEmpDao companyDao;
    @Autowired
    private AppAcl appAcl;
    private final Logger log = LoggerFactory.getLogger(getClass());
    @Autowired
    @Lazy
    private ReportAppSvc self;
    @Autowired
    private TenantSysConfDao confDao;
    @Autowired
    private AdminTaskOperationRepo adminTaskOperationRepo;
    @Autowired
    private CycleEvalAppSvc cycleEvalAppSvc;
    @Autowired
    private GradeDao gradeDao;
    @Autowired
    private TransactionWrap tx;

    public PagedList<ReportEvalUserPo> pagedReportTaskEvalUser(TenantSysConf conf, TaskReportQry query) {
        if (StrUtil.isNotBlank(query.getOrgId())) {
            List<String> orgIds = kpiOrgDao.listAllChildOrgIds(new TenantId(query.getCompanyId()), Arrays.asList(query.getOrgId().split(",")));
            if (CollUtil.isNotEmpty(orgIds)) {
                query.setOrgId(String.join(",", orgIds));
            }
        }
        getOrgIds(query);
        LoggerFactory.getLogger(getClass()).info("请求参数11：{}", JSONObject.toJSON(query));
        if (query.getNoOrgPerm()) {
            LoggerFactory.getLogger(getClass()).info("没有权限查看报表");
            PagedList<ReportEvalUserPo> reportPos = new PagedList<>();
            reportPos.setPageSize(10);
            reportPos.setPageNo(1);
            reportPos.setTotalPage(0);
            reportPos.setTotalRow(0);
            return reportPos;
        }
        PagedList<ReportEvalUserPo> pagedList = evaluateTaskDao.pagedReportTaskEvalUser(conf, query);
        clearOrgId(query); //分页循环之后,管理范围内的invalid状态的orgId会被过滤掉,这里清理,使每次都能取管理范围内的orgId
        return pagedList;
    }

    public PagedList<ReportUserTaskExcelPo> pagedTaskDetail(TenantSysConf conf, TaskReportQry query) {
        query.setOrgId(query.getFilterOrgId()); //filterOrgId用来保存传过来的部门筛选条件
        if (StringUtils.isNotBlank(query.getOrgId())) {
            List<String> orgIds = kpiOrgDao.listAllChildOrgIds(new TenantId(query.getCompanyId()), Arrays.asList(query.getOrgId().split(",")));
            if (CollUtil.isNotEmpty(orgIds)) {
                query.setOrgId(String.join(",", orgIds));
            }
        }
        getOrgIds(query);
        LoggerFactory.getLogger(getClass()).info("请求参数11：{}", JSONObject.toJSON(query));
        if (query.getNoOrgPerm()) {
            LoggerFactory.getLogger(getClass()).info("没有权限查看报表");
            PagedList<ReportUserTaskExcelPo> reportPos = new PagedList<>();
            reportPos.setPageSize(10);
            reportPos.setPageNo(1);
            reportPos.setTotalPage(0);
            reportPos.setTotalRow(0);
            return reportPos;
        }
        PagedList<ReportUserTaskExcelPo> pagedList = evaluateTaskDao.pagedTaskDetailV3(conf, query);
//        clearOrgId(query); //分页循环之后,管理范围内的invalid状态的orgId会被过滤掉,这里清理,使每次都能取管理范围内的orgId
        // 正确的处理应该是每次对orgIds重新赋值,而不是清理orgId
        return pagedList;
    }
    private void clearOrgId(TaskReportQry query) {
        query.setOrgId(null);
    }

    public void updateOrgNames(PagedList<ReportUserTaskExcelPo> reportUserTaskExcelPos, String companyId) {
        for (ReportUserTaskExcelPo excelVO : reportUserTaskExcelPos) {
            if (excelVO.orgIdIsBland()) {
                return;
            }
            excelVO.repaceOrgNamePath();
//            List<KpiDeptCode> kpiDeptCodes = kpiOrgDao.listOrgCode(new TenantId(companyId), Arrays.asList(excelVO.orgId()));
//            List<String> curOrgIds = kpiDeptCodes.stream().map(d -> d.getOrgId()).collect(Collectors.toList());
//            List<KpiOrgSupNames> supNames = kpiOrgDao.listOrgSupNames(new TenantId(companyId), curOrgIds);
//            if (CollUtil.isEmpty(supNames)) {
//                continue;
//            }
//            excelVO.setOrgName(supNames.get(0).getOrgSupName() + ";");
//            excelVO.repaceOrgName(supNames.get(0).getOrgSupName());
//            excelVO.removeSuffix();
        }
    }

    public TaskUserOrgListWrap listTaskUserOrgGroup(List<String> empIds, String companyId) {
        TenantId tenantId = new TenantId(companyId);
        List<EmpRefOrgDo> atOrgs = kpiOrgDao.listAtOrgIds(tenantId, empIds);
        List<String> curOrgIds = CollUtil.map(atOrgs, ref -> ref.getOrgId(), true);
        List<KpiOrgSupNames> supNames = kpiOrgDao.listOrgSupNames(tenantId, curOrgIds);
        return new TaskUserOrgListWrap(atOrgs, supNames);
    }

    public List<TaskUserOrg> listTaskUserOrg(String empId, String companyId) {
        List<TaskUserOrg> resultList = new ArrayList<>();
        List<String> orgCodes = orgDao.listOrgCodes(empId, companyId);
        if (CollUtil.isEmpty(orgCodes)) {
            return resultList;
        }
        for (String orgCode : orgCodes) {
            if (StringUtils.isEmpty(orgCode)) {
                log.info("orgCode =" + orgCode + "empId = " + empId + "companyId =" + companyId);
                continue;
            }
            String[] code = orgCode.split("\\|");
            TaskUserOrg taskUserOrg = orgDao.getTaskUserOrg(code, companyId);
            if (Objects.nonNull(taskUserOrg)) {
                resultList.add(taskUserOrg);
            }
        }
        return resultList;
    }

    public void getAllChildOrgIds(ReportQuery query) {
        if (CollUtil.isEmpty(query.getOrgIds())) {
            return;
        }
        List<String> orgIds = kpiOrgDao.listAllChildOrgIds(new TenantId(query.getCompanyId()), query.getOrgIds());
        if (CollUtil.isEmpty(orgIds)) {
            return;
        }
        query.setOrgIds(orgIds);
    }

    public PagedList<QuarterReportPo> pagedQuarterReport(ReportQuery query) {
        PagedList<EmpYearReportItemPo> reportPos = this.pagedReport(query);
        List<QuarterReportPo> rsPos = reportPos.stream().map(itemPo -> {
            QuarterReportPo rsPo = Convert.convert(QuarterReportPo.class, itemPo);
            Map<String, String> showStrMap = itemPo.buildShowStr(query.getShowType());
            rsPo.setFinalScore(itemPo.getWeightYearAtQuarter().asShowStr(query.getShowType()));//权重年度分
            rsPo.matchShowStr(showStrMap);
            return rsPo;
        }).collect(Collectors.toList());
        PagedList<QuarterReportPo> pagedList = reportPos.convert(rsPos);
        return pagedList;
    }

    public Map<String, String> getCreateYearReportStatus(String companyId, String year) {
        Map<String, String> map = new HashMap<>();
        map.put("status", reportDao.getCreateYearReportStatus(companyId, year));
        return map;
    }

    public PagedList<YearReportPo> pagedYearReport(ReportQuery query) {
        PagedList<EmpYearReportItemPo> reportPos = pagedReport(query);
        List<YearReportPo> rsPos = reportPos.stream().map(itemPo -> {
            YearReportPo rsPo = Convert.convert(YearReportPo.class, itemPo);
            Map<String, String> showStrMap = itemPo.buildShowStr(query.getShowType());
            rsPo.matchShowStr(showStrMap);
            return rsPo;
        }).collect(Collectors.toList());
        PagedList<YearReportPo> rs = reportPos.convert(rsPos);
        List<String> empIds = CollUtil.map(rs, r -> r.getEmpId(), true);
        if (CollUtil.isEmpty(empIds)) {
            return rs;
        }
        TaskUserOrgListWrap wrap = listTaskUserOrgGroup(empIds, query.getCompanyId());
        wrap.replaceOrgName(rs);
        return rs;
    }

    public PagedList<EmpYearReportItemPo> pagedReport(ReportQuery query) {
        getAllChildOrgIds(query);
        reportDao.getReportAuth(query);
        if (CollUtil.isNotEmpty(query.getOrgIds())) {
            List<String> orgIds = kpiOrgDao.listAllChildOrgIds(new TenantId(query.getCompanyId()), query.getOrgIds());
            log.info("orgIds== {}", JSONObject.toJSONString(orgIds));
            query.setOrgIds(orgIds);
        }
        if (query.getNoOrgPerm()) {
            log.info("没有权限查看报表");
            return new PagedList<>(1, 10, 0);
        }
        log.info("param==== {}", JSONObject.toJSONString(query));
        PagedList<EmpYearReportItemPo> reportPos = reportDao.pagedYearReport(query);
        return reportPos;
    }

    public PagedList<MonthlyReportPo> pageMonthlyReport(ReportQuery query) {
        PagedList<EmpYearReportItemPo> reportPos = this.pagedReport(query);
        List<MonthlyReportPo> rsPos = reportPos.stream().map(itemPo -> {
            MonthlyReportPo rsPo = Convert.convert(MonthlyReportPo.class, itemPo);
            Map<String, String> showStrMap = itemPo.buildShowStr(query.getShowType());
            rsPo.setFinalScore(itemPo.getWeightYearAtMonth().asShowStr(query.getShowType()));//权重年度分
            rsPo.matchShowStr(showStrMap);
            return rsPo;
        }).collect(Collectors.toList());
        PagedList<MonthlyReportPo> pagedList = reportPos.convert(rsPos);
        List<String> empIds = CollUtil.map(pagedList, r -> r.getEmpId(), true);
        if (CollUtil.isEmpty(empIds)) {
            return pagedList;
        }
        TaskUserOrgListWrap wrap = listTaskUserOrgGroup(empIds, query.getCompanyId());
        wrap.replaceOrgName(pagedList);
        return pagedList;
    }

    private void getOrgIds(TaskReportQry queryVO) {
        //只能查看有权限的数据
        queryVO.setNoOrgPerm(false);
        //是否是主管理员或应用管理员
        SystemAdminSetModel admin = adminSetDao.findByEmpId(queryVO.getCompanyId(), queryVO.getCreatedUser());
        if (Objects.isNull(admin)) {
            //非管理员，没有查看权限
            queryVO.setNoOrgPerm(true);
            return;
        }

        //主管理员或者应用管理员
        if (Arrays.asList(AdminType.MAIN, AdminType.APP).contains(admin.getAdminType())
                || SystemAdminSetModel.ManagePurview.COMPANY.equals(admin.getManagePurview().getManageScope())) {
            return;
        }
        List<String> adminOrgIds = kpiOrgDao.listAdminScopePrivOrgIdsAll(queryVO.getCompanyId(), queryVO.getCreatedUser(), null);
        boolean haveContainRoot = kpiOrgDao.haveContainRoot(adminOrgIds, queryVO.getCompanyId());
        if (haveContainRoot){//包含主部门和主管理一样权限
            return;
        }
        if (CollUtil.isEmpty(adminOrgIds)) { //表示没有管理范围，不能查看数据
            //非管理员，没有查看权限
            queryVO.setNoOrgPerm(true);
            return;
        }
        if (StringUtils.isBlank(queryVO.getOrgId())) {  // 如果没有选择部门条件，则使用管理范围内的部门
            queryVO.setOrgId(StringUtils.join(adminOrgIds, ","));
        } else {
            //过滤出只存在于管理范围内的部门
            List<String> orgIds = Arrays.asList(queryVO.getOrgId());
            orgIds = orgIds.stream().filter(o -> adminOrgIds.contains(o)).collect(Collectors.toList());
            if (CollUtil.isEmpty(orgIds)) {
                //选择的部门不在管理范围内，没有查看权限
                return;
            }
            queryVO.setOrgId(StringUtils.join(orgIds, ","));
        }
    }

    public List<GradeDistributionPo> listGradeDistribution(ReportQuery query) {
        getAllChildOrgIds(query);
        if (query.checkAuth()) {
            reportDao.getReportAuth(query);
            query.listOrgIdsString();
            if (query.getNoOrgPerm()) {
                return new ArrayList<>();
            }
        }
        if (StringUtils.isNotEmpty(query.getTaskId())) {
            query.setAdminOrgIds(kpiOrgDao.listAdminScopePrivOrgIds(query.getCompanyId(), query.getOpEmpId(), null));
        }
        return reportDao.listGradeDistribution(query);
    }

    @Async
    public void createYearReport(String tid, TenantId companyId, EmpId loginEmpId,
                                 String yearStr, Boolean isAvg, Integer performanceType) {
        MDC.put("tid", tid);
        Integer year = Integer.valueOf(yearStr);
        EmpYearReport yearReport = new EmpYearReport(companyId, year, 0, loginEmpId.getId());
        tx.runTran(() -> {
            reportDao.clearEmpReportItems(yearReport);
        });
        ReportQuery query = new ReportQuery(companyId, loginEmpId, yearStr, 1, 500);
        List<String> empIds = null;
//        query.setEmpIds(Arrays.asList("1100018"));
        List<ScoreRange> scoreRanges = gradeDao.listScoreRange(companyId.getId(), null);
        ScoreRule defaultRule = gradeDao.getDefaultEmpScoreRule(companyId);
        ReportWeightConf weightConf = reportWeightSettingDao.getWeightConf(query.getYear(), query.getCompanyId());
        //处理一页员工列表
        while ((empIds = reportDao.pagedNeedReportEmpIds(query)).size() > 0) {
            query.setPageNo(query.getPageNo() + 1);
            //汇总员工结果
            List<EmpYearReportItem> empReportItems = reportDao.listSummaryYearReport(companyId, yearStr, empIds, isAvg, performanceType);
            List<EmpRefScoreRule> empRefScoreRules = gradeDao.listEmpScoreRuleWithOutDefualt(companyId, empIds);
            //使用结果匹配等级及系数, 优先匹配人员规则,没有测匹配默认规则
            EmpYearReportDmSvc dmSvc = new EmpYearReportDmSvc(empReportItems, empRefScoreRules, scoreRanges);
            dmSvc.computeWeightYearScore(weightConf);//计算加权年度分
            dmSvc.matchLevelByScore(empReportItems, defaultRule);//使用结果匹配等级及系
            tx.runTran(() -> reportDao.saveEmpReportItems(yearReport, empReportItems)); //保存结果
        }
        yearReport.finished();
        reportDao.relaceSave(yearReport);
    }

    //按员工任务导出
    @Async
    public void downloadYearDetailWithMerge(TaskReportQry qry) {
        MDC.put("tid", qry.getTid());
        qry.initExportTitleNames();
        String fileName = qry.getFileName("考核任务汇总");
        Company company = this.companyDao.getCompany(new TenantId(qry.getCompanyId()));
        String lookScorerCode = qry.isAdminType() ? "mainAdminSeeAnonymous_20240202" : "childAdminSeeAnonymous_20240110";
        TenantSysConf conf = this.confDao.findTenantConf(qry.getCompanyId(), lookScorerCode, 1);
        qry.setOpenRefPerformance(company.getAppPerm().contains("openRefPerformance"));
        List<ExportEvalUser> exportEvalUsers = new ArrayList<>();
        Supplier<PagedList> pagedDataFun = () -> {
            PagedList<ReportEvalUserPo> reportUserTaskExcelPos = this.pagedReportTaskEvalUser(conf, qry);
            for (ReportEvalUserPo datum : reportUserTaskExcelPos.getData()) {
                exportEvalUsers.add(new ToDataBuilder<>(datum, ExportEvalUser.class).data());
            }
            return reportUserTaskExcelPos;
        };
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelWriterBuilder builder = EasyExcel.write(outputStream).includeColumnFiledNames(qry.getIncludeColumnFiledNames());
        new SimpleExcelExport(builder).writeSheet("考核任务汇总", pagedDataFun, qry, qry.dataClass()).finish();
        this.appAcl.exportFile(company.getDingCorpId(), qry.getLoginEmpId(), fileName, outputStream.toByteArray());
        adminTaskOperationRepo.saveExportOperation(exportEvalUsers, qry.getCompanyId(), qry.getLoginEmpId(), qry.getOpEmpType(), 2);
        if (StrUtil.isNotBlank(qry.getCycleId())) {
            List<ExportEvalUser> treeSet = exportEvalUsers.stream()
                    .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getTaskUserId() + ";" + o.getEmpId()))), ArrayList::new));
            cycleEvalAppSvc.exportCycleTask(qry.getCompanyId(), qry.getLoginEmpId(), qry.getOpEmpType(), 2, treeSet.size(), qry.getCycleId());
        }
    }

    @Async
    public void downloadYearDetailWithItem(TaskReportQry reportQry) {
        MDC.put("tid", reportQry.getTid());
        String fileName = reportQry.getFileName("考核任务汇总");
        reportQry.initExportTitleNames();
        Company company = this.companyDao.getCompany(new TenantId(reportQry.getCompanyId()));
        String lookScorerCode = reportQry.isAdminType() ? "mainAdminSeeAnonymous_20240202" : "childAdminSeeAnonymous_20240110";
        TenantSysConf tenantConf = this.confDao.findTenantConf(reportQry.getCompanyId(), lookScorerCode, 1);
        reportQry.setOpenRefPerformance(company.getAppPerm().contains("openRefPerformance"));
        List<ExportEvalUser> exportEvalUsers = new ArrayList<>();
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            Supplier<PagedList> pagedDataFun = () -> {
                PagedList<ReportUserTaskExcelPo> reportUserTaskExcelPos = this.pagedTaskDetail(tenantConf, reportQry);
               // this.updateOrgNames(reportUserTaskExcelPos, reportQry.getCompanyId());
                for (ReportUserTaskExcelPo datum : reportUserTaskExcelPos.getData()) {
                    exportEvalUsers.add(new ToDataBuilder<>(datum, ExportEvalUser.class).data());
                }
                return reportUserTaskExcelPos;
            };
            final ExcelWriterBuilder builder = EasyExcel.write(outputStream).head(reportQry.getExportTitleHeads()).registerWriteHandler(AutoColumnWidthStyleStrategy.createAutoWidthStyleStrategy(18));
            final DCClsExport excelExport = new DCClsExport(builder, reportQry.getExportTitleHeads(), ReportUserTaskExcelPo.class, pagedDataFun, reportQry, "-");
            excelExport.excelWrite();
            this.appAcl.exportFile(company.getDingCorpId(), reportQry.getLoginEmpId(), fileName, outputStream.toByteArray());
            adminTaskOperationRepo.saveExportOperation(exportEvalUsers, reportQry.getCompanyId(), reportQry.getLoginEmpId(), reportQry.getOpEmpType(), 2);
            if (StrUtil.isNotBlank(reportQry.getCycleId())) {
                List<ExportEvalUser> treeSet = exportEvalUsers.stream()
                        .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getTaskUserId() + ";" + o.getEmpId()))), ArrayList::new));
                cycleEvalAppSvc.exportCycleTask(reportQry.getCompanyId(), reportQry.getLoginEmpId(), reportQry.getOpEmpType(), 2, treeSet.size(), reportQry.getCycleId());
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("upload_report_file_to_dingpan:" + e.getMessage(), e);
        }
    }


    private PagedList<ReportEvalUserPo> toReportEvalUserPos(PagedList<ReportUserTaskExcelPo> taskPos, Set<String> userId) {
//        PagedList<ReportEvalUserPo> pos = new PagedList<>(taskPos.getPageNo(), taskPos.getPageSize(), taskPos.getTotalRow());
        PagedList<ReportEvalUserPo> pos = new PagedList<>();
        pos.setPageNo(taskPos.getPageNo());
        pos.setPageSize(taskPos.getPageSize());
        pos.setTotalRow(taskPos.getTotalRow());
        for (ReportUserTaskExcelPo reportUserTaskExcelPo : taskPos) {
            if (userId.contains(reportUserTaskExcelPo.getTaskUserId())) {
                continue;
            }
            ReportEvalUserPo reportEvalUserPo = new ReportEvalUserPo();
            BeanUtils.copyProperties(reportUserTaskExcelPo, reportEvalUserPo);
            pos.getData().add(reportEvalUserPo);
            userId.add(reportUserTaskExcelPo.getTaskUserId());
        }
        return pos;
    }

    //导出指标分析报表
    @Async
    public void downloadItemScoreReport(TaskReportQry reportQry) {
        MDC.put("tid", reportQry.getTid());
        String fileName = "指标分析报表" + DateTimeUtils.date2StrDate(new Date(), DateTimeUtils.FORMAT_yyyyMMddHHmmss)
                + BusinessConstant.XSSF_PREFIX;
        String receiverId = reportQry.getLoginEmpId();
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            DataClassMergeStrategy mergeStrategy = new DataClassMergeStrategy(ReportItemAnalyseExcelPo.class, reportQry.getPageSize());
            Supplier<PagedList> pagedDataFun = () -> this.queryItemScoreExport(reportQry);
            ExcelWriterBuilder builder = EasyExcel.write(outputStream, ReportItemAnalyseExcelPo.class);
            new DataClassExcelExport(builder, mergeStrategy, pagedDataFun, reportQry).excelWrite();
            this.appAcl.sendFile(new TenantId(reportQry.getCompanyId()), receiverId, fileName, outputStream.toByteArray());
        } catch (IOException e) {
            log.error("export report exception:" + e.getMessage(), e);
        }
    }


    //导出部门等级分布表
    public void downloadOrgTaskLevelAnalysis(ReportQuery qry) {
        MDC.put("tid", qry.getTid());
        String fileName = qry.getCycleName() + "部门等级分布情况" + BusinessConstant.XSSF_PREFIX;
        String receiverId = qry.getLoginEmpId();

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            //等级 动态表头
            List<OrgAnaylsisStepPo> stepPos = listOrgAnaylsisLevels(qry);
            qry.appendHeadOrgTaskLevelAnalysis(stepPos);
            Supplier<PagedList> pagedDataFun = () -> pagedExporOrgLevelDistribution(qry, stepPos);
            final ExcelWriterBuilder builder = EasyExcel.write(outputStream).head(qry.getExportTitleHeads()).registerWriteHandler(AutoColumnWidthStyleStrategy.createAutoWidthStyleStrategy(18));
            final DCClsExport excelExport = new DCClsExport(builder, qry.getExportTitleHeads(), ReportUserTaskExcelPo.class, pagedDataFun, qry, "-");
            excelExport.excelWrite();
            this.appAcl.sendFile(new TenantId(qry.getCompanyId()), receiverId, fileName, outputStream.toByteArray());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("upload_report_file_to_dingpan:" + e.getMessage(), e);
        }
    }

    /***
     * 查询指标分析导出数据
     * @param query
     * @return
     */
    public PagedList<ReportItemAnalyseExcelPo> queryItemScoreExport(TaskReportQry query) {
        MDC.put("tid", query.getTid());
        query.setOrgId(query.getFilterOrgId());
        List<String> orgIds = kpiOrgDao.listAllChildOrgIds(new TenantId(query.getCompanyId()), query.getOrgIds());
        if (CollUtil.isNotEmpty(orgIds)) {
            query.setOrgId(String.join(",", orgIds));
        }
        getOrgIds(query);
        if (query.getNoOrgPerm()) {
            LoggerFactory.getLogger(getClass()).info("没有权限查看报表");
            PagedList<ReportItemAnalyseExcelPo> reportPos = new PagedList<>();
            reportPos.setPageSize(10);
            reportPos.setPageNo(1);
            reportPos.setTotalPage(0);
            reportPos.setTotalRow(0);
            return reportPos;
        }
        PagedList<ReportItemAnalyseExcelPo> pagedList = reportDao.PageItemScoreExcel(query);
        if (CollectionUtils.isNotEmpty(pagedList.getData())) {
            //计算完成度
            for (ReportItemAnalyseExcelPo data : pagedList.getData()) {
                data.builderProgress();
            }
        }
        return pagedList;
    }

    @Async
    public void downloadYearReport(ReportQuery qry) {
        MDC.put("tid", qry.getTid());
        String fileName = "年度报表" + DateTimeUtils.date2StrDate(new Date(), DateTimeUtils.FORMAT_yyyyMMddHHmmss)
                + BusinessConstant.XSSF_PREFIX;
        String receiverId = qry.getLoginEmpId();
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            DataClassMergeStrategy mergeStrategy = new DataClassMergeStrategy(YearReportPo.class, qry.getPageSize());
            Supplier<PagedList> pagedDataFun = () -> this.pagedYearReport(qry);
            new DataClassExcelExport(EasyExcel.write(outputStream, YearReportPo.class), mergeStrategy, pagedDataFun, qry).excelWrite();
            appAcl.sendFile(new TenantId(qry.getCompanyId()), receiverId, fileName, outputStream.toByteArray());
        } catch (IOException e) {
            log.error("export report exception: {}", e.getMessage());
        }
    }


    @Async
    public void downloadMothReportR(ReportQuery qry) {
        MDC.put("tid", qry.getTid());
        String fileName = "月度报表" + DateTimeUtils.date2StrDate(new Date(), DateTimeUtils.FORMAT_yyyyMMddHHmmss)
                + BusinessConstant.XSSF_PREFIX;
        String receiverId = qry.getLoginEmpId();
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            DataClassMergeStrategy mergeStrategy = new DataClassMergeStrategy(MonthlyReportPo.class, qry.getPageSize());
            Supplier<PagedList> pagedDataFun = () -> this.pageMonthlyReport(qry);
            new DataClassExcelExport(EasyExcel.write(outputStream, MonthlyReportPo.class), mergeStrategy, pagedDataFun, qry).excelWrite();
            appAcl.sendFile(new TenantId(qry.getCompanyId()), receiverId, fileName, outputStream.toByteArray());
        } catch (IOException e) {
            e.printStackTrace();
            log.error("export report exception: {}", e.getMessage());
        }
    }

    @Async
    public void downloadMothReport(ReportQuery query) {
        String fileName = query.getYear() + "月度报表" +
                DateTimeUtils.date2StrDate(new Date(), DateTimeUtils.FORMAT_yyyyMMddHHmmss) + BusinessConstant.XSSF_PREFIX;

        PagedList<MonthlyReportPo> list = this.pageMonthlyReport(query);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        if (CollectionUtils.isEmpty(list.getData())) {
            EasyExcel.write(outputStream, MonthlyReportPo.class)
                    .registerWriteHandler(new GenericExportMergeStrategy<>(list, null))
                    .sheet("月度报表").doWrite(list);
            this.appAcl.sendFile(new TenantId(query.getCompanyId()), query.getLoginEmpId(), fileName, outputStream.toByteArray());
            return;
        }
        ExcelWriterSheetBuilder builder = null;
        PagedList<MonthlyReportPo> reports = new PagedList<>();
        for (Integer i = 1; i <= list.getTotalPage(); i++) {
            query.setPageNo(i);
            PagedList<MonthlyReportPo> reportPos = this.pageMonthlyReport(query);
            reports.addAll(reportPos);
            builder = EasyExcel.write(outputStream, MonthlyReportPo.class)
                    .registerWriteHandler(new GenericExportMergeStrategy<>(reports, null))
                    .sheet("月度报表");
        }
        builder.doWrite(reports);
        this.appAcl.sendFile(new TenantId(query.getCompanyId()), query.getLoginEmpId(), fileName, outputStream.toByteArray());
    }

    //导出季度报表
    @Async
    public void downloadQuarterReport(ReportQuery query) {
        MDC.put("tid", query.getTid());
        ReportQuery tempQuery = query.clone();
        List<String> companys = new ArrayList<>();
        companys.add("a5415be0-ad88-472d-a63e-7aa6ff4fe24d");
        companys.add("acd50632-e6f0-4ee3-9f41-1f402fb1ac74");
        companys.add("5a031297-1b38-48ae-bc82-375849835203");
        boolean appointCompany = companys.contains(query.getCompanyId());
        String fileName = query.getYear() + "季度报表" +
                DateTimeUtils.date2StrDate(new Date(), DateTimeUtils.FORMAT_yyyyMMddHHmmss) + BusinessConstant.XSSF_PREFIX;
        String receiverId = query.getLoginEmpId();
        PagedList<QuarterReportPo> reportPos = this.pagedQuarterReport(query);
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            if (CollectionUtils.isEmpty(reportPos.getData())) {
                EasyExcel.write(outputStream, QuarterReportPo.class)
                        .registerWriteHandler(new GenericExportMergeStrategy<>(reportPos, null))
                        .sheet("季度报表").doWrite(reportPos);
                byte[] bytes = outputStream.toByteArray();
                this.appAcl.sendFile(new TenantId(query.getCompanyId()), receiverId, fileName, bytes);
                return;
            }
            ExcelWriterSheetBuilder builder = null;
            PagedList<QuarterReportPo> reports = new PagedList<>();
            for (Integer i = 1; i <= reportPos.getTotalPage(); i++) {
                ReportQuery forTempQuery = tempQuery.clone();
                forTempQuery.setPageNo(i);
                PagedList<QuarterReportPo> reportPoList = this.pagedQuarterReport(forTempQuery);
                if (reportPoList.isEmpty()) {
                    continue;
                }
                List<String> empIds = CollUtil.map(reportPoList, r -> r.getEmpId(), true);
                if (CollUtil.isEmpty(empIds)) {
                    continue;
                }
                TaskUserOrgListWrap wrap = listTaskUserOrgGroup(empIds, forTempQuery.getCompanyId());
                wrap.replaceOrgName(reportPoList);
                reports.addAll(reportPoList);
                builder = EasyExcel.write(outputStream, QuarterReportPo.class)
                        .registerWriteHandler(new GenericExportMergeStrategy<>(reports, null))
                        .sheet("季度报表");
            }
            builder.doWrite(reports);
            byte[] bytes = outputStream.toByteArray();
            this.appAcl.sendFile(new TenantId(query.getCompanyId()), receiverId, fileName, bytes);
        } catch (ExcelDataConvertException e) {
            CellData data = e.getCellData();
            String errorMsg = String.format("Export_excel_error:excel表格:第%s行,第%s列:%s==>%s",
                    e.getRowIndex() + 1, e.getColumnIndex(), e.getMessage(), JSONUtil.toJsonStr(data));
            log.error(errorMsg);
        } catch (IOException e) {
            e.printStackTrace();
            log.error("export report exception: {}", e.getMessage());
        }
    }

    public List<OrgAnaylsisStepPo> listOrgAnaylsisLevels(ReportQuery query) {
        List<OrgAnaylsisStepPo> targetList = new ArrayList<>();
        getAllChildOrgIds(query);
        if (query.checkAuth()) {
            reportDao.getReportAuth(query);
            query.listOrgIdsString();
            if (query.getNoOrgPerm()) {
                return targetList;
            }
        }
        if (StringUtils.isNotEmpty(query.getTaskId())) {
            query.setAdminOrgIds(kpiOrgDao.listAdminScopePrivOrgIds(query.getCompanyId(), query.getOpEmpId(), null));
        }
        return reportDao.listOrgAnaylsisLevels(query);
    }

    public PagedList<OrgAnaylsisStepExcelPo> pagedExporOrgLevelDistribution(ReportQuery query, List<OrgAnaylsisStepPo> stepPos) {
        getAllChildOrgIds(query);
        if (query.checkAuth()) {
            reportDao.getReportAuth(query);
            query.listOrgIdsString();
            if (query.getNoOrgPerm()) {
                return new PagedList<>();
            }
        }
        if (StringUtils.isNotEmpty(query.getTaskId())) {
            query.setAdminOrgIds(kpiOrgDao.listAdminScopePrivOrgIds(query.getCompanyId(), query.getOpEmpId(), null));
        }
        //查询需要显示的层高组织，比如一级组织
        return reportDao.pagedExporOrgLevelDistribution(query, stepPos);
    }

    public PagedList<OrgGradeDistributionPo> pageOrgLevelDistribution(ReportQuery query) {
        PagedList<OrgGradeDistributionPo> pagedList = new PagedList<>();
        getAllChildOrgIds(query);
        if (query.checkAuth()) {
            reportDao.getReportAuth(query);
            query.listOrgIdsString();
            if (query.getNoOrgPerm()) {
                return pagedList;
            }
        }
        if (StringUtils.isNotEmpty(query.getTaskId())) {
            query.setAdminOrgIds(kpiOrgDao.listAdminScopePrivOrgIds(query.getCompanyId(), query.getOpEmpId(), null));
        }
        //查询需要显示的层高组织，比如一级组织
        return reportDao.pageOrgPathHigh(query);
    }
}
