package com.polaris.kpi.eval.app.task.dto.eval;

import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.eval.domain.confirm.entity.BindOkrType;
import com.polaris.kpi.eval.domain.task.entity.EvalKpi;
import com.polaris.kpi.eval.domain.task.entity.EvalRefOkr;
import com.polaris.kpi.eval.domain.task.entity.PerfModificationRecord;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalKpiType;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.MutualNodeConf;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.RaterNodeConf;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.S3RaterBaseConf;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.S3SuperRaterConf;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.EmpChangeItemStagePo;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
@Setter
@NoArgsConstructor
public class ConfirmItemCmd {
    private TenantId tenantId;
    private EmpId confirmEmpId;
    private String scorerName;
    private EmpId opEmpId;
    private Integer approveOrder;
    private String taskUserId;
    private List<EmpEvalKpiType> kpiTypes;
    private PerfModificationRecord record;//变更记录
    private AdminTask adminTask;
    //private EvalUser evalUser;
    private boolean onlySkipNode = false;
    private AddOKRCmd addOKRCmd;        //导入okr指标对象
    private Boolean isAffirm;       //true:指标确认(affirm)   false:确认审核通过(pass)
    private String confirmAuditSignUrl;  //确认手写签名
    protected RaterNodeConf s3SelfRater;              //360或简易 自评人
    //protected MutualRaterConf s3MutualRater;          //360或简易 互评人
    protected MutualNodeConf s3PeerRater;                   //360或简易 同级互评人
    protected MutualNodeConf s3SubRater;                    //360下级
    protected S3SuperRaterConf s3SuperRater;          //360或简易 上级人
    protected S3RaterBaseConf s3AppointRater;          //默认流程指定评分人

    public void accOp(TenantId tenantId, EmpId opEmpId) {
        this.tenantId = tenantId;
        this.confirmEmpId = opEmpId;
        this.opEmpId = opEmpId;
        if (CollUtil.isNotEmpty(kpiTypes)) {
            kpiTypes.forEach(type -> {
                type.initBaseInfo(tenantId, opEmpId, taskUserId);
            });
        }
        if (Objects.nonNull(record)) {
            record.accOp(tenantId, opEmpId, taskUserId, EvaluateAuditSceneEnum.CONFIRM_ITEM.getScene(), Objects.nonNull(isAffirm) && isAffirm ? "affirm" : "pass");
        }
    }

    public ConfirmItemCmd(TenantId tenantId, EmpId opEmpId, String taskUserId) {
        this.tenantId = tenantId;
        this.opEmpId = opEmpId;
        this.taskUserId = taskUserId;
    }

    //是不是么有修改
    public boolean notEdit() {
        return CollUtil.isEmpty(kpiTypes);
    }

    //能跳过指标确认阶段
    public boolean skipToNextStage(Boolean noChangeSkipFlag) {
        if (onlySkipNode) {//公跳过单个审核结点,不跳过到下阶段,让流程执行完后面的审核结点自动进入下阶段
            return false;
        }
        return notEdit() && noChangeSkipFlag;
    }

    @JSONField(serialize = false)
    public List<EvalKpi> getItems() {
        List<EvalKpi> kpis = new ArrayList<>();
        for (EmpEvalKpiType kpiType : kpiTypes) {
            if (CollUtil.isEmpty(kpiType.getItems())) {
                continue;
            }
            kpis.addAll(kpiType.getItems());
        }
        return kpis;
    }

    public void asSkipped(TenantId tenantId, EmpId opEmpId, String taskUserId, String scoreId, String scorerName) {
        this.tenantId = tenantId;
        this.confirmEmpId = new EmpId(scoreId);
        this.scorerName = scorerName;
        this.opEmpId = opEmpId;
        this.taskUserId = taskUserId;
        this.onlySkipNode = true;
    }

    public List<EmpEvalKpiType> buildKpiTypes() {
        for (EmpEvalKpiType kpiType : kpiTypes) {
            kpiType.distinctKpiTypeUsedFields();
            if (CollUtil.isEmpty(kpiType.getItems())) {//空类别不删除
                continue;
            }
            int delCnt = 0;
            for (EvalKpi item : kpiType.getItems()) {
                //删
                if (StrUtil.equals("delete", item.getExamineOperType())) {
                    item.setIsDeleted(Boolean.TRUE.toString());
                    delCnt++;
                }
            }
            if (delCnt != 0 && delCnt == kpiType.getItems().size()) {
                kpiType.setIsDeleted(Boolean.TRUE.toString());
            }
//            kpiType.getItems().removeIf(evalKpi -> Boolean.valueOf(evalKpi.getIsDeleted()));
        }
//        kpiTypes.removeIf(type -> StrUtil.isNotBlank(type.getIsDeleted()) & Boolean.valueOf(type.getIsDeleted()));
        return kpiTypes;
    }

    public boolean okrNotNull() {
        return this.getAddOKRCmd() != null && this.getAddOKRCmd().getOkrTypes() != null && this.getAddOKRCmd().getOkrTypes().size() > 0;
    }

    public void buildOkrRef(List<EmpEvalKpiType> kpiTypes) {
        List<BindOkrType> okrTypes = new ArrayList<>();
        List<EmpChangeItemStagePo> stagePos = new ArrayList<>();
        kpiTypes.stream().filter(obj -> obj.isOkr()).collect(Collectors.toList()).forEach(item -> {
            EmpChangeItemStagePo stagePo = new EmpChangeItemStagePo();
            BeanUtil.copyProperties(item, stagePo, true);
            stagePo.buildOkr();
            stagePos.add(stagePo);
        });
        if (stagePos != null && stagePos.size() > 0) {
            for (EmpChangeItemStagePo stagePo : stagePos) {
                BindOkrType okrType = new BindOkrType();
                BeanUtil.copyProperties(stagePo, okrType, true);
                List<EvalRefOkr> krList = new ArrayList<>();
                if (stagePo.getItems() != null && stagePo.getItems().size() > 0) {
                    for (EmpChangeItemStagePo.CacheKpiItem item : stagePo.getItems()) {
                        EvalRefOkr evalRefOkr = new EvalRefOkr();
                        BeanUtil.copyProperties(item, evalRefOkr, true);
                        krList.add(evalRefOkr);
                    }
                }
                okrType.setKrList(krList);
                okrTypes.add(okrType);
            }
            AddOKRCmd addOKRCmd = new AddOKRCmd(null, null, this.getTaskUserId(), null, JSONObject.toJSONString(okrTypes));
            this.setAddOKRCmd(addOKRCmd);
        }
    }


    public void buildOkrPassChangeRef(ConfirmItemCmd cmd, List<EmpChangeItemStagePo> stagePos) {
        List<BindOkrType> okrTypes = new ArrayList<>();
        if (stagePos != null && stagePos.size() > 0) {
            for (EmpChangeItemStagePo stagePo : stagePos) {
                BindOkrType okrType = new BindOkrType();
                BeanUtil.copyProperties(stagePo, okrType, true);
                List<EvalRefOkr> krList = new ArrayList<>();
                if (stagePo.getItems() != null && stagePo.getItems().size() > 0) {
                    for (EmpChangeItemStagePo.CacheKpiItem item : stagePo.getItems()) {
                        EvalRefOkr evalRefOkr = new EvalRefOkr();
                        BeanUtil.copyProperties(item, evalRefOkr, true);
                        krList.add(evalRefOkr);
                    }
                }
                okrType.setKrList(krList);
                okrTypes.add(okrType);
            }
            AddOKRCmd addOKRCmd = new AddOKRCmd(null, null, cmd.getTaskUserId(), null, JSONObject.toJSONString(okrTypes));
            cmd.setAddOKRCmd(addOKRCmd);
        }
    }

    @JSONField(serialize = false)
    public List<String> delAsk360EvalIds() {
        if (CollUtil.isEmpty(this.kpiTypes)) {
            return null;
        }
        return CollUtil.filterNew(this.kpiTypes, k -> k.isAskType()).stream()
                .filter(ek -> Objects.equals(ek.getIsDeleted(), Boolean.TRUE.toString()))
                .map(EmpEvalKpiType::getAsk360EvalId)
                .collect(Collectors.toList());
    }

    public List<BindOkrType> bindOkrTypes() {
        return addOKRCmd.getOkrTypes();
    }
}
