package com.polaris.kpi.eval.app.task.appsvc;

import cn.com.polaris.kpi.EmpStaff;
import cn.com.polaris.kpi.company.MsgSceneEnum;
import cn.com.polaris.kpi.eval.CycleValue;
import cn.com.polaris.kpi.eval.EvaluationStaff;
import cn.com.polaris.kpi.eval.FinishValue;
import cn.com.polaris.kpi.eval.Name;
import cn.com.polaris.kpi.temp.CycleId;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.perf.www.common.em.OperationLogSceneEnum;
import com.perf.www.common.utils.page.ModelPagedList;
import com.perf.www.common.utils.string.StringTool;
import com.perf.www.vo.task.query.EvaluateTaskKpiQueryVO;
import com.polaris.acl.dept.domain.org.EmpOrganization;
import com.polaris.acl.dept.face.ExtAuthAcl;
import com.polaris.acl.dept.face.LoginAcl;
import com.polaris.acl.dept.pojo.AdminSetDo;
import com.polaris.acl.dept.pojo.CompanyDo;
import com.polaris.acl.dept.pojo.CompanyPo;
import com.polaris.acl.dept.repository.AclAdminSetDao;
import com.polaris.acl.dept.repository.DeptEmpDao;
import com.polaris.kpi.common.KpiI18NException;
import com.polaris.kpi.eval.app.TransactionWrap;
import com.polaris.kpi.eval.app.task.dto.*;
import com.polaris.kpi.eval.app.task.dto.eval.AddIndExtScoreItemCmd;
import com.polaris.kpi.eval.app.task.dto.eval.AddOKRCmd;
import com.polaris.kpi.eval.app.task.dto.eval.ImportFinishValueCmd;
import com.polaris.kpi.eval.app.task.result.BatchSubmitFinishValueResult;
import com.polaris.kpi.eval.app.task.result.EnterScoringResult;
import com.polaris.kpi.eval.app.task.result.SubmitFinishValueResult;
import com.polaris.kpi.eval.domain.task.acl.IObItemPlanAcl;
import com.polaris.kpi.eval.domain.task.dmsvc.*;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.empeval.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalKpiType;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalRule;
import com.polaris.kpi.eval.domain.task.entity.empeval.KpiListWrap;
import com.polaris.kpi.eval.domain.task.entity.msg.MsgTodoAggregate;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrTarget;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrTask;
import com.polaris.kpi.eval.domain.task.event.*;
import com.polaris.kpi.eval.domain.task.event.admineval.EvalRuleChanged;
import com.polaris.kpi.eval.domain.task.event.admineval.TerminateEmpEvalEvnet;
import com.polaris.kpi.eval.domain.task.event.msg.CancelTodoEvent;
import com.polaris.kpi.eval.domain.task.event.talent.ScoringStageStart;
import com.polaris.kpi.eval.domain.task.event.talent.scoring.BatchEnterScoringStage;
import com.polaris.kpi.eval.domain.task.event.talent.scoring.ExtDataLocked;
import com.polaris.kpi.eval.domain.task.ext.IOkrAclSvc;
import com.polaris.kpi.eval.domain.task.repo.*;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.eval.domain.temp.entity.ExamGroup;
import com.polaris.kpi.eval.domain.temp.repo.PerfTempRepo;
import com.polaris.kpi.eval.infr.extData.dao.ExtDataItemFieldCorrDao;
import com.polaris.kpi.eval.infr.extData.dao.ExtDataSyncDao;
import com.polaris.kpi.eval.infr.extData.pojo.po.ExtDataItemFieldCorrPo;
import com.polaris.kpi.eval.infr.extData.pojo.po.ExtDataSyncPo;
import com.polaris.kpi.eval.infr.task.dao.*;
import com.polaris.kpi.eval.infr.task.ppojo.*;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.MyTaskUserPo;
import com.polaris.kpi.eval.infr.task.ppojo.okr.OkrTargetPo;
import com.polaris.kpi.eval.infr.task.ppojo.okr.OkrTaskPo;
import com.polaris.kpi.eval.infr.task.query.*;
import com.polaris.kpi.eval.infr.task.query.empeval.CardDataReps;
import com.polaris.kpi.eval.infr.task.query.empeval.CardDataReq;
import com.polaris.kpi.eval.infr.task.query.empeval.MyHandleTaskQuery;
import com.polaris.kpi.eval.infr.temp.dao.PerfTempDao;
import com.polaris.kpi.eval.infr.temp.ppojo.AutoStartTempDo;
import com.polaris.kpi.extData.domain.entity.ExtDataItemFieldCorr;
import com.polaris.kpi.org.domain.dept.entity.Company;
import com.polaris.kpi.org.domain.dept.entity.CompanyConf;
import com.polaris.kpi.org.domain.dept.entity.CompanyMsgCenter;
import com.polaris.kpi.org.domain.dept.entity.Employee;
import com.polaris.kpi.org.domain.dept.repo.MsgCenterRepo;
import com.polaris.kpi.org.domain.dept.type.TaskId;
import com.polaris.kpi.org.domain.emp.entity.KpiEmployee;
import com.polaris.kpi.org.domain.emp.type.Emp;
import com.polaris.kpi.org.infr.company.dao.CompanyDaoImpl;
import com.polaris.kpi.org.infr.company.dao.TenantSysConfDao;
import com.polaris.kpi.org.infr.dept.dao.KpiOrgDao;
import com.polaris.kpi.org.infr.emp.dao.KpiEmpDao;
import com.polaris.kpi.org.infr.emp.pojo.DeptEmpPo;
import com.polaris.kpi.org.type.CorpId;
import com.polaris.kpi.org.type.TraceKey;
import com.polaris.sdk.type.DmEventPublish;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.joda.time.DateTime;
import org.lufei.ibatis.common.data.ToDataBuilder;
import org.lufei.ibatis.mapper.PagedList;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR> lufei
 * @date 2022/2/25 1:56 下午
 */
@Service
@Slf4j
public class EvalTaskAppSvc {

    @Autowired
    private PerfTempRepo tempRepo;
    @Autowired
    private PerfTempDao tempDao;
    @Autowired
    private CompanyDaoImpl companyDao;
    @Autowired
    private TenantSysConfDao confDao;
    @Autowired
    private EvaluateTaskRepo taskRepo;
    @Autowired
    private EvaluateTaskDao taskDao;
    @Autowired
    private TaskUserRepo userRepo;
    @Autowired
    private KpiOrgDao kpiOrgDao;
    @Autowired
    private CycleEvalDmSvc cycleEvalDmSvc;
    @Autowired
    @Lazy
    private EvalTaskAppSvc self;
    @Autowired
    private TaskUserDao taskUserDao;
    @Autowired
    private TaskAuditDao taskAuditDao;
    @Resource
    private EmpEvalRuleRepo empEvalRuleRepo;
    @Autowired
    private EmpEvalScorerRepo empEvalScorerRepo;
    @Autowired
    private EvalKpiDao kpiDao;
    @Autowired
    private TaskKpiRepo taskKpiRepo;
    @Autowired
    private IOkrAclSvc okrAclSvc;
    @Autowired
    private DeptEmpDao deptEmpDao;
    @Autowired
    private KpiEmpDao kpiEmpDao;
    @Autowired
    private EmpEvalDao empEvalDao;
    @Autowired
    private OpLogDao opLogDao;
    @Autowired
    private AdminTaskDao adminTaskDao;

    @Autowired
    private TaskAuditRepo auditRepo;
    @Resource
    private AclAdminSetDao adminSetDao;
    @Autowired
    private AdminTaskRepo adminTaskRepo;
    @Autowired
    private IObItemPlanAcl itemPlanAcl;
    @Autowired
    private TaskItemScoreRuleDao taskItemScoreRuleDao;
    @Autowired
    private EmpEvalDmSvc empEvalDmSvc;
    @Autowired
    private TransactionWrap tx;
    @Autowired
    private ExtDataItemFieldCorrDao itemFieldCorrDao;
    @Autowired
    private ExtDataSyncDao syncDao;
    @Autowired
    private MsgCenterRepo centerRepo;
    @Autowired
    private OnScoreEvalRepo onScoreEvalRepo;

    //新人考核调用
    public boolean autoStartNewEmp(TenantId tenantId, Employee emp, String postId) {
        List<DeptEmpPo> emtAtDepts = kpiEmpDao.listNewEmp(tenantId, emp.getEmployeeId());
        List<String> orgIds = CollUtil.map(emtAtDepts, deptEmpPo -> deptEmpPo.getOrgId(), true);
        Map<String, List<AutoStartTempDo>> examGroups = tempDao.matchPostExamGroup(tenantId, postId, orgIds);
        log.info("匹配的到模板:{}", JSONUtil.toJsonStr(examGroups));
        if (CollUtil.isEmpty(examGroups)) {
            return false;
        }
        examGroups.forEach((orgId, perfTemplBaseDos) -> {
            for (AutoStartTempDo examGroup : perfTemplBaseDos) {
                if (CollUtil.isEmpty(examGroup.getInitiatorList())) {
                    continue;
                }
                String startDay = new DateTime().toString("yyyy-MM-dd");
                String endDay = examGroup.createCycleEndDay();
                StartNewEmpEvalCmd cmd = new StartNewEmpEvalCmd(examGroup.getId(),
                        startDay, endDay,
                        emp.getEmployeeId(), emp.getName(), orgId);
                cmd.setCheckDupName(false);
                cmd.startorInfo(tenantId, new EmpId(examGroup.getInitiatorList().get(0).getEmpId()));//用户自动发起TODO
                cmd.setTaskName(emp.getName() + "试用期考核");
                cmd.setIsAuto(1);
                self.directStartEval(cmd);
            }
        });
        return true;
    }

    /**
     * 替换一键发起 saveEvaluateTaskBase
     * createUser=true  quickCreateTask=true  isReset=false
     *
     * @param cmd
     * @return 任务批次id
     */
    @Transactional
    public String directStartEval(DirectStartEvalCmd cmd) {
        if (cmd.isCheckDupName()) {
            boolean existed = taskDao.isExistedName(cmd.getCompanyId(), cmd.getTaskName());
            if (existed) {
                throw new RuntimeException("error.taskname.repeat");
            }
        }
        ExamGroup temp = tempRepo.getExamGroup(cmd.getCompanyId(), cmd.getTempId(), cmd.getIsNewEmp());
        EvaluationStaff staff = cmd.getEvaluationStaff() == null ?
                temp.getInitiate().asEvalStaff() :
                cmd.evaluationStaff(cmd.getIsNewEmp(), kpiOrgDao.listAllChildOrg(cmd.getCompanyId(), cmd.staffDeptIds()));
        final List<EmpStaff> collect = kpiOrgDao.listEmpByStaffConfItem(cmd.getCompanyId(), staff);
        List<EmpStaff> empStaffs = collect.stream().distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(empStaffs)) {
            log.info("被考核员工为空");
            throw new RuntimeException("error.taskuser.null");
        }
        List<String> orgIds = CollUtil.map(empStaffs, empStaff -> empStaff.getOrgId(), true);
        List<EmpOrganization> paths = deptEmpDao.listDeptWithNamePath(cmd.getCompanyId(), orgIds);
        ListWrap<EmpOrganization> pathMap = new ListWrap<>(paths).asMap(path -> path.getOrgId());
        empStaffs.forEach(empStaff -> {
            EmpOrganization path = pathMap.mapGet(empStaff.getOrgId());
            empStaff.acceptOrgPath(path.getOrgCode(), path.getNamePath(), path.getPathHight());
        });
        CycleEval cycleEval =
                cycleEvalDmSvc.createCycleEval(new Name(cmd.getTaskName()), cmd.getTaskDesc(), staff, temp, empStaffs);
        cycleEval.startTimeConf(cmd.getCreateTaskType(), cmd.getCreateTaskDateType(), cmd.getDay());
        cycleEval.cycleDate(cmd.getCycleStartDate(), cmd.getCycleEndDate());
        cycleEval.setCreatedUser(cmd.getStartEmpId());
        cycleEval.setCycleId(cmd.getCycleId());
        cycleEval.setIsAuto(cmd.getIsAuto());
        taskRepo.saveCycleTask(cycleEval);
        // initTaskData(model, createUser, quickCreateTask, taskBaseId, companyId, tempBaseId, empRefOrgList, templBaseModel.getPointsRule(), templEvaluateVO.getSelfScoreFlag(), evaluateType, isReset);
        new CycleEvalCreatedEvent(cycleEval, temp).publish();
        return cycleEval.getId();
    }

    //创建周期
    @Transactional
    public Cycle createCycleIfNeed(TenantId companyId, CycleValue cycleValue, String empId) {
        Cycle cycleIfNeed = taskRepo.createCycleIfNeed(companyId, cycleValue, empId);
        new CycleCreated(cycleIfNeed).publish();
        return cycleIfNeed;
    }

    public PagedList<CyclePo> pagedCycle(CycleQuery query) {
        query.setShowEmptyCycle(false);
        return taskDao.pagedCycle(query);
    }

    public PagedList<CyclePo> pagedMyStartCycle(CycleQuery query) {
        return taskDao.pagedMyStartCycle(query);
    }

    public PagedList<CycleTaskUserPo> pagedUserEvalOnCycle(OnCycleQuery query) {
        return taskDao.pagedUserEvalOnCycle(query);
    }

    public PagedList<TaskBasePo> pagedTaskOnCycle(OnCycleQuery query) {
        return taskDao.pagedTaskOnCycle(query);
    }

    public PagedList<NewEmpTaskPo> pagedNewEmpTaskPo(NewEmpTaskQuery query) {
        PagedList<NewEmpTaskPo> pos = taskDao.pagedNewEmpTaskPo(query);
        for (NewEmpTaskPo po : pos) {
            //执行阶段找指标录入人
            if (TalentStatus.CONFIRMED.getStatus().equals(po.getTaskStatus())) {
                po.setReviewersEmp(taskDao.findInputFinishReviewsEmp(po.getTaskUserId(), query.getTenantId().getId()));
            } else {
                //其他阶段找score_result中scorer_id责任人
                po.setReviewersEmp(taskDao.findReviewsEmp(po.getTaskId(), query.getTenantId().getId()));
            }
            //没有找到默认使用被考核人
            if (StrUtil.isEmpty(po.getReviewersEmp())) {
                po.setReviewersEmp(taskDao.findTaskEmp(po.getTaskId(), po.getEmpId(), query.getTenantId().getId()));
            }
        }
        return pos;
    }

//    @DmEventPublish
    public List<EnterScoringResult> batchEnterScoring(EnterScoringCmd cmd) {
        List<EnterScoringResult> rs = new ArrayList<>();
        if (CollUtil.isEmpty(cmd.getTaskUserIds())) {
            return rs;
        }
        new CancelTodoEvent(new TenantId("1"), new ArrayList<>(), "", MsgSceneEnum.FINISH_VALUE_AUDIT.getType()).fire();
        CompanyConf conf = companyDao.findCompanyConf(cmd.getCompanyId());
        List<EvalUser> evalUsers = taskDao.listConfirmedTaskUser(cmd.getCompanyId(), cmd.getTaskUserIds());

        if (evalUsers.isEmpty()) {
            return rs;
        }
        if (cmd.isAuto()) {
            //过滤出没有截止日期设置
            evalUsers = evalUsers.stream().filter(e -> Objects.isNull(e.getEmpEvalRule().getDeadLineConf())
                    || !e.getEmpEvalRule().getDeadLineConf().isOpen()).collect(Collectors.toList());
        }
        /*for (EvalUser evalUser : evalUsers) {
            List<EvalScoreResult> results = taskUserDao.listResultByScoreTypes(cmd.getCompanyId(), evalUser.getId(), Arrays.asList("finish_value_audit"), false);
            if (TalentStatus.FINISH_VALUE_AUDIT.equals(evalUser.getTaskStatus()) ? (evalUser.hasFinishValueAudit() && CollUtil.isNotEmpty(results)) : evalUser.hasFinishValueAudit()) {
                rs.add(new EnterScoringResult(false, evalUser.getEmpName(), "finishedValueAuditNotEnd"));
            }
        }
        if (CollUtil.isNotEmpty(rs)) {
            return rs;
        }*/
        BatchEnterScoringStage batchAsyEvent = new BatchEnterScoringStage(conf, evalUsers, cmd.getSendNotice());
        batchAsyEvent.opUser(cmd.getOpEmpId());
        batchAsyEvent.fire();
        return rs;
    }

    public List<EnterScoringResult> finishValueAuditIsFinish(String companyId, List<String> taskUserIds) {
        List<EvalUser> evalUsers = taskDao.listConfirmedTaskUser(new TenantId(companyId), taskUserIds);
        List<EnterScoringResult> scoringResults = new ArrayList<>();
        for (EvalUser evalUser : evalUsers) {
            boolean isHavNoPassFinishValueAudit = CollUtil.isNotEmpty(taskUserDao.listResultByScoreTypes(new TenantId(companyId), evalUser.getId(), Arrays.asList("finish_value_audit"), false));
            if (evalUser.hasFinishValueAudit(isHavNoPassFinishValueAudit, false) &&
                    TalentStatus.CONFIRMED.getStatus().equals(evalUser.getTaskStatus()) ? true : isHavNoPassFinishValueAudit) {
                scoringResults.add(new EnterScoringResult(false, evalUser.getId(), evalUser.getEmpName(), "finishedValueAuditNotEnd"));
            }
        }
        return scoringResults;
    }

    public EnterScoringResult enterScoringOne(@NotNull EnterScoringCmd cmd) {
        CompanyConf conf = companyDao.findCompanyConf(cmd.getCompanyId());
        List<EvalUser> evalUsers = taskDao.listConfirmedTaskUser(cmd.getCompanyId(), cmd.getTaskUserIds());
        if (evalUsers.isEmpty()) {
            return null;
        }
        EvalUser evalUser = evalUsers.get(0);
        // 检验指标字段是否有关联外部数据，控制事件是否发送
        Boolean extDataCorrFlag = this.checkExtDataItemFieldCorr(evalUser, cmd.getCompanyId().getId());
        if (extDataCorrFlag){
            Boolean syncFlag = this.checkExtDataSync(evalUser);
            if (!syncFlag){
                return new EnterScoringResult(false, evalUser.getEmpName(), "extDataNotSync");
            }
            ExtDataLocked extDataLocked = new ExtDataLocked(evalUser, cmd.getCompanyId().getId());
            extDataLocked.fire();
        }

        EnterScoringResult result = self.enterScoring(conf, evalUser, cmd.getOpEmpId(), cmd.getSendNotice());
        return result;
    }

    private Boolean checkExtDataItemFieldCorr(EvalUser evalUser, String companyId) {
        List<EvalKpi> kpis = evalUser.getKpis();
        if (CollUtil.isNotEmpty(kpis)){
            for (EvalKpi kpi : kpis) {
                List<ExtDataItemFieldCorr> itemFieldCorr = kpi.getItemFieldCorr();
                if (CollUtil.isNotEmpty(itemFieldCorr)) {
                    return true;
                }
            }
        }
        return false;
    }

    public Boolean checkExtDataSync(EvalUser evalUser){
        TenantId companyId = evalUser.getCompanyId();
        String taskId = evalUser.getTaskId();
        PerfEvaluateTaskBaseDo taskBase = adminTaskDao.findTaskBase(companyId, taskId);
        for (EvalKpi kpi : evalUser.getKpis()) {
            List<ExtDataItemFieldCorr> itemFieldCorr = kpi.getItemFieldCorr();
            if (CollUtil.isNotEmpty(itemFieldCorr)){
                for (ExtDataItemFieldCorr corr : itemFieldCorr){
                    ExtDataSyncPo extData = syncDao.getExtData(companyId.getId(), corr.getExtDataFieldId(), evalUser.getEmpId(),
                            taskBase.getCycleStartDate(), taskBase.getCycleEndDate());
                    if (ObjectUtil.isNotNull(extData)){
                        JSONObject jsonObject = JSONObject.parseObject(extData.getExtData(), JSONObject.class);
                        jsonObject.getString(corr.getExtDataFieldParamEnName());
                        if (StrUtil.equals("finishValue", corr.getKpiItemFieldId())){
                            String value = jsonObject.getString(corr.getExtDataFieldParamEnName());
                            if (ObjectUtil.isNotNull(value) && StrUtil.isNotBlank(value)){
                                return true;
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    public EnterScoringResult reEnterScoringOne(EnterScoringCmd cmd, List<EvalAudit> auditDos) {
        CompanyConf conf = companyDao.findCompanyConf(cmd.getCompanyId());
        EvalUser evalUser = userRepo.getTaskUser(cmd.getCompanyId(), cmd.getTaskUserIds().get(0));
        evalUser.setAllCustomAudits(auditDos);
        evalUser.setTaskStatus(TalentStatus.CONFIRMED.getStatus());
        EnterScoringResult result = self.enterScoring(conf, evalUser, cmd.getOpEmpId(), cmd.getSendNotice());
        return result;
    }

    public EnterScoringResult enterScoring(CompanyConf conf, EvalUser taskUser, EmpId opEmpId, Boolean sendNotice) {
        EnterScoreDmSvc dmSvc = new EnterScoreDmSvc(conf, taskUser, opEmpId, sendNotice, null);
        return enterScoring(dmSvc);
    }


    //进入评分阶段
    @Transactional
    public EnterScoringResult enterScoring(EnterScoreDmSvc dmSvc) {
        EvalUser evalUser = dmSvc.getTaskUser();
        Boolean extDataCorrFlag = this.checkExtDataItemFieldCorr(evalUser, evalUser.getCompanyId().getId());
        if (extDataCorrFlag){
            Boolean syncFlag = this.checkExtDataSync(evalUser);
            if (!syncFlag){
                return new EnterScoringResult(false, evalUser.getEmpName(), "对接外部数据尚未同步！");
            }
        }
        //public EnterScoringResult enterScoring(CompanyConf conf, EvalUser taskUser, EmpId opEmpId, Boolean sendNotice,Integer enterScoreType) {
        TenantId tenantId = new TenantId(dmSvc.getConf().getCompanyId());
        EmpEvalMerge evalMerge = empEvalRuleRepo.getEmpEvalMerge(tenantId, dmSvc.getTaskUser().getId(), EmpEvalMerge.all);
        AdminTask task = adminTaskDao.getAdminTaskBase(tenantId, new TaskId(dmSvc.getTaskUser().getTaskId()));
        dmSvc.acceptBaseDomain(tenantId, evalMerge, task);
        dmSvc.setRepo(userRepo);
        log.info("需要判断是否所有分类都已导入");
        List<CompanyMsgCenter> centers = empEvalDao.listMsgCenter(dmSvc.getConf().getCompanyId(), null, dmSvc.getTaskUser().getId(), Arrays.asList(MsgSceneEnum.INVITE_PEER_AUDIT.getType(), MsgSceneEnum.INVITE_SUB_AUDIT.getType()));
        dmSvc.setHavInvitePeerAudit(CollUtil.isNotEmpty(centers));
        List<EvalScoreResult> noPassFinishValueAudits = taskUserDao.listResultByScoreTypes(tenantId, dmSvc.getTaskUser().getId(), Arrays.asList("finish_value_audit"), false);
        dmSvc.setHavNoPassFinishValueAudit(CollUtil.isNotEmpty(noPassFinishValueAudits));
        String failReason = dmSvc.checkAllowEnterScore();
        if (StrUtil.isNotBlank(failReason)) {
            return new EnterScoringResult(false, dmSvc.getTaskUser().getEmpName(), failReason);
        }
        if (!dmSvc.getConf().openInputOnScoring() && CollUtil.isNotEmpty(dmSvc.getTaskUser().onlyInputAutoRaterIds())) {
            //如果没有开启在评分中录入，则关闭仅录入自动指标的待办, 如果同时录入两种指标的不关.
            new ClearTodoDmsSvc(centerRepo).clear(tenantId, dmSvc.getTaskUser().getId(), MsgSceneEnum.submitFinishValue(), dmSvc.getTaskUser().onlyInputAutoRaterIds());
        }
        if (dmSvc.getTaskUser().checkIsInputFinishValueEnterScoreType(dmSvc.getEnterScoreType())
                && Objects.nonNull(dmSvc.getOpEmpId())
                && !dmSvc.getTaskUser().opEmpExistsNotAutoItem(dmSvc.getOpEmpId().getId())
                && !dmSvc.getConf().canSubmitFinishValue()) {
            //兼容为2完成值录入待办不清除问题
            log.info("指标录入人包含当前操作人，且不存在非自动录入指标,清除待办；");
            new CancelTodoEvent(tenantId, dmSvc.getTaskUser().getId(), MsgSceneEnum.TASK_SUBMIT_PROGRESS.getType()).publish();
        }
        //经营计划指标目标值入库
        List<EvalKpi> planKpis = itemPlanAcl.getPlanItemTargetVal(evalMerge);
        int row = dmSvc.doEnterScore(planKpis);
        if (row == 0) {//重复操作
            return new EnterScoringResult(false, dmSvc.getTaskUser().getEmpName(), "repeatEnterScoring");
        }
        //进入评分重新进行下解析评价关系，避免中间评价人的指标变化
        EmpEvalRule rule = new EmpEvalRule();
        BeanUtil.copyProperties(evalMerge, rule);
        ExplainEvalScorerDmSvc explainEvalScorerDmSvc = new ExplainEvalScorerDmSvc(evalUser, rule, tenantId, rule.getCreatedUser());
        explainEvalScorerDmSvc.explainEvalScorer();
        empEvalScorerRepo.saveBatchEmpEvalScorer(tenantId.getId(),rule.getCreatedUser(), evalUser.getId(), explainEvalScorerDmSvc.getScorers().getDatas());
        //  taskBiz.3386 ,控制发送录入通知指定发通知给还未录入完成值人员->ScoringStageStart
        //  taskBiz.3234 进入事件循环中处理 ,指定了评分人,自动打分,自定义评分流程,更新任务状态信息->ScoringStageStart
        ScoringStageStart scoringStageStart = new ScoringStageStart(dmSvc.getTaskUser(), dmSvc.isSendNotice());
        scoringStageStart.opUser(dmSvc.getOpEmpId());
        scoringStageStart.publish();
        return new EnterScoringResult(true, dmSvc.getTaskUser().getEmpName(), "");
    }

    //进入评分阶段
//    @Transactional
//    public EnterScoringResult enterScoring(CompanyConf conf, EvalUser taskUser, EmpId opEmpId, Boolean sendNotice,Integer enterScoreType) {
//        TenantId tenantId = new TenantId(conf.getCompanyId());
//        EmpEvalMerge evalMerge = empEvalRuleRepo.getEmpEvalMerge(tenantId, taskUser.getId(), EmpEvalMerge.all);
//        AdminTask task = adminTaskDao.getAdminTaskBase(tenantId, new TaskId(taskUser.getTaskId()));
//        log.info("需要判断是否所有分类都已导入");
//        // 可考虑在确认或执行阶段校验是否完成指标导入 ,改到taskUser上
//        if (evalMerge.hasNotImportType()) {
//            return new EnterScoringResult(false, taskUser.getEmpName(), "emptyTypeNoItem");
//        }
//        if (taskUser.kpiItemIsEmpty()) {
//            log.error("查询指标列表为空");
//            return new EnterScoringResult(false, taskUser.getEmpName(), "itemIsNull");
//        }
//        if (!taskUser.wasTempTask() && evalMerge.notSetMutualScorer()) {
//            log.error("未设置互评人，不自动进入评分");
//            return new EnterScoringResult(false, taskUser.getEmpName(), "notSetMutualScorer");
//        }
//        List<CompanyMsgCenter> centers = empEvalDao.listMsgCenter(conf.getCompanyId(), null, taskUser.getId(), Arrays.asList(MsgSceneEnum.INVITE_PEER_AUDIT.getType(),MsgSceneEnum.INVITE_SUB_AUDIT.getType()));
//        if (CollUtil.isNotEmpty(centers)) {
//            log.error("未审核完成互评人，不自动进入评分");
//            return new EnterScoringResult(false, taskUser.getEmpName(), "notAuditMutualScorer");
//        }
//
////        boolean askMutualAll = taskItemScoreRuleDao.askMutualAll(tenantId.getId(),taskUser.getId(),null,CollUtil.map(evalMerge.listAsk360MutualTypes(),t -> t.getKpiTypeId(),true));
////        if (!taskUser.wasTempTask() && !askMutualAll) {
////            log.error("未设置问卷互评人，不自动进入评分");
////            return new EnterScoringResult(false, taskUser.getEmpName(), "notSetMutualScorer");
////        }
//        log.info("判断需要自动评分的指标完成值是否已录入");
//        if (!conf.openInputOnScoring() && taskUser.kpiItemIsNotInput()) {
//            log.error("有未录入完成值的指标，不进入评分");//更新进入评分标识????  taskUserManager.updateEnterScoreFlag(taskScoreVO.getTaskUserId());
//            return new EnterScoringResult(false, taskUser.getEmpName(), "finishValueEmpty");
//        }
//        log.info("需要判断录入完成值审核是否完成");
//        if (taskUser.hasFinishValueAudit() &&
//                TalentStatus.CONFIRMED.getStatus().equals(taskUser.getTaskStatus()) ? true
//                : (CollUtil.isNotEmpty(taskUserDao.listResultByScoreTypes(tenantId, taskUser.getId(), Arrays.asList("finish_value_audit"), false)))) {
//            log.info("录入完成值审核未完成,不进入评分");
//            return new EnterScoringResult(false, taskUser.getEmpName(), "finishedValueAuditNotEnd");
//        }
//        //2024-10-29进入评分增加限制，enterScoreType 为null代表手动发起。
//        if (Objects.nonNull(enterScoreType) && task.needManualEnterScore()) {
//            log.info("如果评分配置的是手动进入评分，【完成值录入或者完成值审核或者执行阶段超时】,都需手动发起评分：taskUserId:{},enterScoreType：{}，task：{}",
//                    taskUser.getId(), enterScoreType,
//                    JSONUtil.toJsonStr(task));
//            return new EnterScoringResult(false, taskUser.getEmpName(), "needManaulEnterScore");
//        }
//        if (!conf.openInputOnScoring()) {
//            //如果没有开启在评分中录入，则自动评分指标录入完成值的代办关闭
//            new CancelTodoEvent(tenantId, taskUser.getId(), MsgSceneEnum.TASK_SUBMIT_AUTO_ITEM_PROGRESS.getType()).publish();
//        }
//        if (taskUser.checkIsInputFinishValueEnterScoreType(enterScoreType)
//                && Objects.nonNull(opEmpId)
//                && !taskUser.opEmpExistsNotAutoItem(opEmpId.getId())
//                && !conf.canSubmitFinishValue()) {
//            //兼容为2完成值录入待办不清除问题
//            log.info("指标录入人包含当前操作人，且不存在非自动录入指标,清除待办；");
//            new CancelTodoEvent(tenantId, taskUser.getId(), MsgSceneEnum.TASK_SUBMIT_PROGRESS.getType()).publish();
//        }
//
//        taskUser.enterScoring();
//        taskUser.loadGradeScoreRule();
//        //经营计划指标目标值入库
//        List<EvalKpi> planKpis = itemPlanAcl.getPlanItemTargetVal(evalMerge);
//        int row = userRepo.enterScoreingStage(taskUser, opEmpId,planKpis,enterScoreType);// taskBiz.3188行,记录操做日志
//        if (row == 0) {//重复操作
//            return new EnterScoringResult(false, taskUser.getEmpName(), "repeatEnterScoring");
//        }
//        //  taskBiz.3386 ,控制发送录入通知指定发通知给还未录入完成值人员->ScoringStageStart
//        //  taskBiz.3234 进入事件循环中处理 ,指定了评分人,自动打分,自定义评分流程,更新任务状态信息->ScoringStageStart
//        ScoringStageStart scoringStageStart = new ScoringStageStart(taskUser, sendNotice);
//        scoringStageStart.opUser(opEmpId);
//        scoringStageStart.publish();
//
//        return new EnterScoringResult(true, taskUser.getEmpName(), "");
//    }


    public List<String> listCycleType(TenantId tenantId) {
        return taskDao.listCycleType(tenantId);
    }

    @Transactional
    public void delTaskUser(TaskUserQuery query) {
        if (StringUtils.isBlank(query.getTaskUserIds())) {
            log.info("taskUserIds 不能为空");
            return;
        }
        List<String> taskIds = userRepo.delTaskUser(query.getCompanyId(), query.getTaskUserIds(), query.getUpdatedEmp());
        String cycleId = taskRepo.delTaskIfNeed(new TenantId(query.getCompanyId()), taskIds, query.getTaskUserIds());
        for (String userId : Arrays.asList(query.getTaskUserIds().split(","))) {
            new CancelTodoEvent(new TenantId(query.getCompanyId()), userId, Collections.emptyList()).publish();
        }
        taskRepo.updateCycleEmpCnt(new TenantId(query.getCompanyId()), new CycleId(cycleId));
    }

    public PagedList<CyclePo> pageCycleManage(CycleQuery query) {
        query.setShowEmptyCycle(true);
        return taskDao.pagedCycle(query);
    }

    public PagedList<CyclePo> pageCycleManageOrderByCreatedTime(CycleQuery query) {
        query.setShowEmptyCycle(true);
        return taskDao.pagedCycleOrderByCreatedTime(query);
    }

    public void updateCycleEmpCnt(TenantId tenantId, CycleId cycleId) {
        taskRepo.updateCycleEmpCnt(tenantId, cycleId);
    }

    public ModelPagedList<WorkWaitMsgPo> pagedWaitResultAudit(WorkWaitMsgQuery query) {
        if (StringUtils.isNotBlank(query.getOrgId())) {
            query.setOrgIds(kpiOrgDao.listAllChildOrgIds(new TenantId(query.getCompanyId()), Arrays.asList(query.getOrgId().split(","))));
        }
        //ps:双data数据格式，前端影响范围大暂时不改
        return ModelPagedList.convertToModelPage(taskAuditDao.pagedWaitResultAudit(query));
    }

    @Transactional
    public SubmitFinishValueResult submitFinishValue(SubmitFinishValueCmd cmd) {
        SubmitFinishValueResult result = new SubmitFinishValueResult(true, "");
        CompanyConf conf = companyDao.findCompanyConf(cmd.getTenantId());
        EvalUser evalUser = userRepo.getTaskUser(cmd.getTenantId(), cmd.getTaskUserId());
        AdminTask adminTask = adminTaskDao.getAdminTaskBase(cmd.getTenantId(), new TaskId(evalUser.getTaskId()));
        Emp emp = kpiEmpDao.findEmp(cmd.getTenantId(), cmd.getOpEmpId().getId());
        SubmitFinishValueDmSvc dmSvc = new SubmitFinishValueDmSvc(conf, evalUser, cmd.getTenantId(), cmd.getOpEmpId(), adminTask, emp);
        dmSvc.accRepo(taskKpiRepo, userRepo);
        dmSvc.accDmSvc(empEvalDmSvc, cycleEvalDmSvc);
        // 校验维度和指标上的完成值必填
        if (dmSvc.checkFinishValueNoHavReq(cmd.getFinishValues())) {
            log.error("存在完成值必填项未录入！taskUserId为" + cmd.getTaskUserId());
            return new SubmitFinishValueResult(false, evalUser.getEmpName(), "itemExistsNotInput");
        }
        dmSvc.doSaveFinishValue(cmd.getFinishValues(), cmd.submitItems());
        taskKpiRepo.batchUpdateFinishValue(cmd.getTenantId(), evalUser.getId(), dmSvc.getOpEmpId(), cmd.getFinishValues(), evalUser.getKpis(), OperationLogSceneEnum.SUBMIT_FINISH_VALUE.getScene());
        evalUser.setInputFinishStatus(updatedInputFinishStatus(cmd.getTenantId(), cmd.getTaskUserId()));
        //批量更新经营计划指标
        if (CollUtil.isNotEmpty(cmd.listObItemPlan())) {
            itemPlanAcl.updateObObjects(cmd.getTenantId().getId(), cmd.getOpEmpId().getId(), cmd.listObItemPlan());
        }
        dmSvc.sendInputNotify();//完成值变化发送通知
        EmpId opEmpId = cmd.getOpEmpId();
        EmpEvalMerge empEvalRule = empEvalRuleRepo.getEmpEvalMerge(cmd.getTenantId(), cmd.getTaskUserId(), EmpEvalMerge.all);//要保持兼容
        dmSvc.accRule(empEvalRule);
        //校验该录入人指标是否已全部提交,如果未全部提交仅更新，不进行后续操作【需特殊处理同一個指标多个录入人，其他人需要撤销待办】
        if (dmSvc.checkIsCancelToDo()) {
            dmSvc.cancelOtherToDo(cmd.submitItems());//多人录入同一个指标，撤销其他人待办，仅提交部分
            return result;
        }
        boolean canComputeUp = dmSvc.doComputed();//指标自动计算，canComputeUp= true:计算完成可以更新值
        if (canComputeUp) {
            if (evalUser.isScoring()) {//评分中，提交 完成值，需重新计算环节分值，然后更新
                this.onScoreEvalRepo.saveAutoScoreEvalUserNodeScore(dmSvc.getEvalUser(), dmSvc.getEmpEvalRule().getKpiTypes(), cmd.getOpEmpId().getId(), dmSvc.getEmpEvalRule().getEvalScorersWrap());//保存计算后的环节分数
            } else {
                this.userRepo.updateAutoScore(evalUser);
            }
        }
        if (evalUser.isNextStage() && empEvalRule.allScorePassed()) {
            opEmpId = null;
            //环节结束事件
            new ThisStageEnded(empEvalRule, evalUser, TalentStatus.SCORING).publish();
            //修改通知状态。
            adminTaskRepo.updateInputNotifyJobStatus(cmd.getTenantId().getId(), StrUtil.splitTrim(cmd.getTaskUserId(), ","));
        }
        // 在这里查是否存在被驳回的指标，不为空，则只找对应的审批人发送待办
        List<EvalKpi> rejectEvalKpis = kpiDao.listRejectKpiItem(cmd.getTenantId().getId(), evalUser.getId(), null);
        dmSvc.asyncSubmitFinishValueEvent(rejectEvalKpis, opEmpId);//提交完成值异步执行订阅的事件
        /**可能存在多个录入人，清除待办的时候，统一清除*/
        dmSvc.appendCancelMsg(opEmpId);
        for (CancelTodoEvent cancelTodoEvent : dmSvc.getCancelTodoEvents()) {
            cancelTodoEvent.fire();
        }
        return result;
    }
//    @Transactional
//    public SubmitFinishValueResult submitFinishValue(SubmitFinishValueCmd cmd){
//        KpiListWrap kpiTypes = kpiDao.listAndBuildKpiType(cmd.getTenantId(),  cmd.getTaskUserId(), null,  EmpEvalMerge.all, 0);
//        // 校验维度和指标上的完成值必填
//        FinishValueReqValidationSvc finishValueReqValidationSvc = new FinishValueReqValidationSvc(kpiTypes.getDatas());
//        if (!finishValueReqValidationSvc.allItemsCheckPassed(cmd.getFinishValues())) {
//            EvalUser baseUser = userRepo.getBaseTaskUser(cmd.getTenantId(), cmd.getTaskUserId());
//            log.error("存在完成值必填项未录入！taskUserId为" + cmd.getTaskUserId());
//            return new SubmitFinishValueResult(false, baseUser.getEmpName(), "itemExistsNotInput");
//        }
//        modifyFinishValue(cmd);
//        EvalUser evalUser = cmd.getEvalUser();
//        EmpId opEmpId = cmd.getOpEmpId();
//        Integer opFinishValueType = 1;//提交
//        EmpEvalMerge empEvalRule = empEvalRuleRepo.getEmpEvalMerge(cmd.getTenantId(), cmd.getTaskUserId(), EmpEvalMerge.all);//要保持兼容
//        //校验该录入人指标是否已全部提交,如果未全部提交仅更新，不进行后续操作【需特殊处理同一個指标多个录入人，其他人需要撤销待办】
//        if (TalentStatus.CONFIRMED.getStatus().equals(evalUser.getTaskStatus()) && Objects.nonNull(opEmpId) && evalUser.getOpEmpExistsItemNotSubmitFinishValue()) {
//            /**可能存在多个录入人，如果其他录入人已提交需清理*/
//            List<String> resultInputEmpIds = empEvalRule.getCurItemAllSubmitInputEmpIds(cmd.getOpEmpId().getId(),cmd.submitItems());
//            log.info("录入人指标未全部提交仅更新,不进行后续操作【需特殊处理同一個指标多个录入人，其他人需要撤销待办log】,resultInputEmpIds:{}",resultInputEmpIds);
//            if (CollUtil.isNotEmpty(resultInputEmpIds)) {
//                //特殊场景取消代办
//                new CancelTodoEvent(cmd.getTenantId(), resultInputEmpIds, evalUser.getId(), MsgSceneEnum.TASK_SUBMIT_PROGRESS.getType()).publish();
//            }
//            return new SubmitFinishValueResult(true,"");
//        }
//
//        boolean computed = evalUser.tryComputeAutoScore(empEvalRule.getTypeWeightConf().isOpen(),
//                empEvalRule.getScoreValueConf().getCustomFullScore(),empEvalRule.getScoreValueConf().isFullScoreRange(),empEvalRule.openItemAutoScoreMultiplWeight());
//        if (computed) {
//            FinalWeightSumScore weightSumScore = empEvalRule.computeFinalScore(evalUser.getFinalItemAutoScore(),evalUser.isOpenAvgWeightCompute());
//            evalUser.computeLevel(weightSumScore, empEvalRule.getFinalScore(), empEvalRule.needComputeLevel());
//            userRepo.updateAutoScore(evalUser);
//        }
//        if (evalUser.isNextStage() && empEvalRule.allScorePassed()) {
//            opEmpId = null;
//            //环节结束事件
//            new ThisStageEnded(empEvalRule, evalUser, TalentStatus.SCORING).publish();
//            //修改通知状态。
//            adminTaskRepo.updateInputNotifyJobStatus(cmd.getTenantId().getId(), StrUtil.splitTrim(cmd.getTaskUserId(), ","));
//        }
//        //version 保存一致
//        new KpiItemUpdateFinishedValueEvent(cmd.getTenantId(), evalUser, cmd.getOpEmpId(), opFinishValueType).publish();
//
//        /**可能存在多个录入人，清除待办的时候，统一清除*/
//        List<String> resultInputEmpIds = empEvalRule.getResultInputEmpIds(cmd.getOpEmpId().getId());
//        //取消代办
//        new CancelTodoEvent(cmd.getTenantId(), resultInputEmpIds, evalUser.getId(), MsgSceneEnum.TASK_SUBMIT_PROGRESS.getType()).publish();
//        if (Objects.isNull(opEmpId)) {
//            new CancelTodoEvent(cmd.getTenantId(), cmd.getTaskUserId(), MsgSceneEnum.TASK_SUBMIT_AUTO_ITEM_PROGRESS.getType()).publish();
//            return new SubmitFinishValueResult(true, evalUser.getEmpName(), "");
//        }
//        new CancelTodoEvent(cmd.getTenantId(), resultInputEmpIds, cmd.getTaskUserId(), MsgSceneEnum.TASK_SUBMIT_AUTO_ITEM_PROGRESS.getType()).publish();
//        return new SubmitFinishValueResult(true, "");
//    }

    @Transactional
    public void modifyFinishValue(SubmitFinishValueCmd cmd) {
        CompanyConf conf = companyDao.findCompanyConf(cmd.getTenantId());
        EvalUser evalUser = userRepo.getTaskUser(cmd.getTenantId(), cmd.getTaskUserId());
        AdminTask adminTask = adminTaskDao.getAdminTaskBase(cmd.getTenantId(), new TaskId(evalUser.getTaskId()));
        Emp emp = kpiEmpDao.findEmp(cmd.getTenantId(), cmd.getOpEmpId().getId());
        SaveFinishValueDmSvc dmSvc = new SaveFinishValueDmSvc(conf, evalUser, cmd.getTenantId(), cmd.getOpEmpId(), adminTask, emp);
        dmSvc.accRepo(taskKpiRepo, userRepo);
        dmSvc.accDmSvc(empEvalDmSvc, cycleEvalDmSvc);
        dmSvc.doSaveFinishValue(cmd.getFinishValues(), cmd.submitItems());
        tx.runTran(() -> taskKpiRepo.batchUpdateFinishValue(cmd.getTenantId(), evalUser.getId(), dmSvc.getOpEmpId(), cmd.getFinishValues(), evalUser.getKpis(), null));
        tx.runTran(() -> evalUser.setInputFinishStatus(updatedInputFinishStatus(cmd.getTenantId(), cmd.getTaskUserId())));
        //批量更新经营计划指标
        if (CollUtil.isNotEmpty(cmd.listObItemPlan())) {
            itemPlanAcl.updateObObjects(cmd.getTenantId().getId(), cmd.getOpEmpId().getId(), cmd.listObItemPlan());
        }
        dmSvc.sendInputNotify();//完成值变化发送通知
        if (Objects.isNull(evalUser.getEmpEvalRule())) {
            evalUser.setEmpEvalRule(empEvalDao.getEmpEvalRule(cmd.getTenantId(), cmd.getTaskUserId()));
        }
        new KpiItemUpdateFinishedValueEvent(cmd.getTenantId(), evalUser, cmd.getOpEmpId(), 0).publish();
    }


//    @Transactional
//    public void modifyFinishValue(SubmitFinishValueCmd cmd) {
//        CompanyConf conf = companyDao.findCompanyConf(cmd.getTenantId());
//        EvalUser evalUser = userRepo.getTaskUser(cmd.getTenantId(), cmd.getTaskUserId());
//        evalUser.inputFinishChanged(cmd.getFinishValues());
//        //该地方不查了itemRule ，没有查公式， 后面判断如果有rule不再查 rule 和公式  考核这个接口是树干，则自己这边处理一下
//        evalUser.rmItemRule();
//        evalUser.canSubmitFinishValue(conf.isEnableEnterOnScoring(), conf.autoItemEnterOnScoring(), cmd.submitItems());
//        //校验该录入人指标是否已全部提交,如果未全部提交仅更新，不进行最终提交的后续操作，
//        evalUser.setOpEmpExistsItemNotSubmitFinishValue(evalUser.checkOpEmpExistsItemsNoSubmit(cmd.getOpEmpId().getId(), cmd.submitItems()));
//        log.info("====opEmpExistsItemNotSubmitFinishValue:{}",evalUser.getOpEmpExistsItemNotSubmitFinishValue());
//        taskKpiRepo.batchUpdateFinishValue(cmd.getTenantId(), cmd.getTaskUserId(), cmd.getOpEmpId(), cmd.getFinishValues(), evalUser.getKpis(), null);
//        evalUser.setInputFinishStatus(updatedInputFinishStatus(cmd.getTenantId(), cmd.getTaskUserId()));
//        if (Objects.isNull(evalUser.getEmpEvalRule()))
//            evalUser.setEmpEvalRule(empEvalDao.getEmpEvalRule(cmd.getTenantId(), cmd.getTaskUserId()));
//        cmd.setEvalUser(evalUser);
//
//        AdminTask adminTask = adminTaskDao.getAdminTaskBase(cmd.getTenantId(),new TaskId(evalUser.getTaskId()));
//        if (adminTask.isOpenSendFinishValueChangedMsg()) {
//            Emp emp = kpiEmpDao.findEmp(cmd.getTenantId(), cmd.getOpEmpId().getId());
//            empEvalDmSvc.parseInputNotify(adminTask.getInputNotifyConf(),evalUser,cmd.getOpEmpId().getId());
//            cycleEvalDmSvc.batchSendFinishValueChanged(new Name(adminTask.getTaskName()),evalUser,adminTask.inputChangedRecEmpIds(),emp.getName(),cmd.getOpEmpId().getId());
//        }
//        //完成值已经更新事件
//        if (cmd.getIsSubmit()) {
//            return;
//        }
//        new KpiItemUpdateFinishedValueEvent(cmd.getTenantId(), evalUser, cmd.getOpEmpId(),0).publish();
//    }

    @Transactional
    public void saveFinishValueCache(CacheFinishValueCmd cmd) {
        //先清除缓存
        taskKpiRepo.deletedCacheInfo(cmd.getTenantId(), cmd.getTaskUserId(), cmd.getOpEmpId());
        //插入缓存
        taskKpiRepo.saveInputFinishValCacheInfo(cmd.getTenantId(), cmd.getTaskUserId(), cmd.getOpEmpId(), cmd.getFinishValues());
    }

    public void batchSaveFinishValueCache(TenantId tenantId, EmpId empId, List<CacheFinishValueCmd> cmds) {
        List<InputFinishValCache> allCaches = new ArrayList<>();
        Set<String> taskUserIds = new HashSet<>();

        List<InputFinishValCache> submitCaches = new ArrayList<>();
        for (CacheFinishValueCmd cmd : cmds) {
            for (FinishValue finishValue : cmd.getFinishValues()) {
                InputFinishValCache cache = Convert.convert(InputFinishValCache.class, finishValue);
                cache.accept(tenantId.getId(), empId.getId(), cmd.getTaskUserId());
                submitCaches.add(cache);
                taskUserIds.add(cmd.getTaskUserId());
            }
        }

        ListWrap<InputFinishValCache> fileListWrap = new ListWrap<>(submitCaches).asMap(cache -> cache.getTaskUserId() + "-" + cache.getKpiItemId());
        List<String> userIds = new ArrayList<>(taskUserIds);
        ListWrap<InputFinishValCache> cachedWrap = kpiDao.listInputFinishValCacheByTaskUserIds(tenantId.getId(), userIds, empId.getId());
        if (!cachedWrap.isEmpty()){
            for (InputFinishValCache data : cachedWrap.getDatas()) {
                //如果提交的指标数据存在与暂存的一样，则使用提交的 数据
                if (fileListWrap.contains(data.getTaskUserId() + "-" + data.getKpiItemId())){
                    continue;
                }
                //添加已经暂存过的指标暂存数据【非本次提交的】
                allCaches.add(data);
            }
        }
        //添加本次提交的指标暂存数据【本次提交的】
        allCaches.addAll(submitCaches);
        taskKpiRepo.batchSaveFinishValueCache(tenantId, empId, taskUserIds, allCaches);
    }

    @Transactional
    public void addIndExtScoreItem(AddIndExtScoreItemCmd cmd) {
        TenantId tenantId = new TenantId(cmd.getCompanyId());
        EvalKpi kpi = kpiDao.getKpi(tenantId, cmd.getEvalId(), cmd.getIndId());
        if (StrUtil.isNotBlank(cmd.getUserId())) {
            Employee opEmp = deptEmpDao.getEmployee(tenantId, cmd.getUserId());
            cmd.setOpEmpId(opEmp != null ? opEmp.getEmployeeId() : cmd.getOpEmpId());
        }
        taskKpiRepo.addIndExtScoreItem(cmd.getBeforeScore(), cmd.getScoreValue(), cmd.getScoreComment(), cmd.getCreateTime(), cmd.getOpEmpId(), kpi);
        EvalUser taskUser = userRepo.getTaskUser(tenantId, cmd.getEvalId());
        AdminTask task = adminTaskDao.getAdminTaskBase(tenantId, new TaskId(taskUser.getTaskId()));
        MsgTodoAggregate msg = new MsgTodoAggregate(taskUser.getCompanyId(), taskUser.getTaskId(), new Name(task.getTaskName()), taskUser.getEmpId(), taskUser.getId())
                .addExtTempValue("scoreItem", cmd.getScoreComment())
                .addExtTempValue("talentEvalId", cmd.getEvalId())
                .sendExtMsg().addRecEmpId(taskUser.getEmpId());
        if (cmd.isEdit()) {
            msg.useScene(MsgSceneEnum.IND_SCORE_ITEM_CHANGED)
                    .addExtTempValue("beforeScore", cmd.getBeforeScore())
                    .addExtTempValue("afterScore", cmd.getScoreValue()).publish();
        } else {
            msg.useScene(MsgSceneEnum.IND_SCORE_ITEM_INPUT)
                    .addExtTempValue("score", cmd.getScoreValue()).publish();
        }

    }

    @NotNull
    private Integer updatedInputFinishStatus(TenantId companyId, String taskUserId) {
        //需要重新查询更新完成之后的指标信息统计更新完成值录入状态
        EvalUser updatedEvalUser = userRepo.getTaskUser(companyId, taskUserId);
        updatedEvalUser.updateInputFinishStatus();
        userRepo.updateInputFinishStatus(updatedEvalUser.getInputFinishStatus(),
                updatedEvalUser.getId(), updatedEvalUser.getCompanyId());
        return updatedEvalUser.getInputFinishStatus();
    }

    @Async
    public BatchSubmitFinishValueResult batchSubmitFinishValue(List<SubmitFinishValueCmd> cmds, String tid) {
        MDC.put(TraceKey.TID, tid);
        BatchSubmitFinishValueResult batchResult = new BatchSubmitFinishValueResult();
        List<SubmitFinishValueResult> results = new ArrayList<>();
        for (SubmitFinishValueCmd cmd : cmds) {
            try {
                SubmitFinishValueResult result = self.submitFinishValue(cmd);
                if (!result.getSuccess()) {
                    results.add(result);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        batchResult.setResults(results);
        log.info(" 批量提交存在未录入的results：{}", JSONUtil.toJsonStr(results));
        return batchResult;
    }

    @Async
    public void batchModifyFinishValue(List<SubmitFinishValueCmd> cmds, String tid) {
        MDC.put(TraceKey.TID, tid);
        for (SubmitFinishValueCmd cmd : cmds) {
            try {
                self.modifyFinishValue(cmd);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    public void delSchedule(Long batchNo) {
        taskRepo.delSchedule(batchNo);
    }

    public Map<String, List<EvalUserSummaryPo>> listEvalUserSummary(TenantId tenantId, String taskUserId) {
        //return taskUserDao.listEvalUserSummary(tenantId, taskUserId);
        return taskUserDao.listEvalUserSummaryV3(tenantId, taskUserId);
    }


    @Transactional
    public void autoStopNotConfirm(String companyId) {
        List<EvalUser> evalUsers = userRepo.listNotConfirmEvalUser(companyId);
        if (CollUtil.isEmpty(evalUsers)) {
            LoggerFactory.getLogger(getClass()).info("没有符合确认超时自动终止的员工任务");
            return;
        }
        for (EvalUser user : evalUsers) {
            log.info("taskUserId=" + user.getId());
            //记录日志
            OperationLogDo logDo = new OperationLogDo(user.getCompanyId().getId(), user.getId(), "", "-1", new Date());
            //1.0升级过来的数据没有EmpEvalRule，1.0只能按超时未确认终止处理
            EmpEvalRule empEvalRule = user.getEmpEvalRule();
            if (Objects.isNull(empEvalRule) || empEvalRule.getConfirmTask().confirmTimeOutAutoTerminate()) {
                logDo.setBusinessScene(OperationLogSceneEnum.CONFIRM_TIME_OUT_AUTO_TERMINATE.getScene());
                //user.setFinalScore(BigDecimal.ZERO);
                user.setTaskStatus(TalentStatus.TERMINATED.getStatus());
                //user.matchGrade();
                userRepo.updateTaskUser(user);
                // 任务终止事件
                new TerminateEmpEvalEvnet(companyId, user.getId(), user.getTaskId(), "-1").publish();
            } else {
                if (user.notExistItems()) {
                    logDo.setBusinessScene(OperationLogSceneEnum.ITEM_NOT_EXIST_NOT_AUTO_SKIP.getScene());
                    opLogDao.addLog(logDo);
                    continue;
                }
                logDo.setBusinessScene(OperationLogSceneEnum.CONFIRM_TIME_OUT_AUTO_SKIP.getScene());
                CycleEval cycleEval = taskRepo.getMergeCycleEval(user.getCompanyId(), user.getId());
                cycleEvalDmSvc.batchSendAffirmTask(cycleEval, user, user.listAffirmTaskNotify(null));
                new ThisStageEnded(cycleEval, user, TalentStatus.CONFIRMING, user.getCreatedUser()).publish();
            }
            opLogDao.addLog(logDo);
            //取消指标确认的所有待办
            new CancelTodoEvent(user.getCompanyId(), user.getId(), MsgSceneEnum.confirmScene).publish();
        }
    }

    @Transactional
    public EvalUser addOKRItem(AddOKRCmd cmd) {
        CycleEval mergeTask = taskDao.getMergeTaskBase(cmd.getTenantId(), cmd.getTaskUserId());
        EvalUser user = taskUserDao.getBothBaseUser(cmd.getTenantId().getId(), cmd.getTaskId(), cmd.getEmpId(), cmd.getTaskUserId());
        if (user.afterStage(TalentStatus.SCORING)){
            throw new KpiI18NException("error.changeOkrItemAfterScoring","进入评分后不允许编辑okr指标");
        }
        KpiListWrap okrTypes = kpiDao.listAndBuildOkrType(user.getCompanyId(), user.getId());
        cmd.buildKpis(user.getTaskId(), user.getId(), user.getEmpId(), okrTypes);
        EmpEvalRule empEvalRule = empEvalDao.getEmpEvalRule(cmd.getTenantId(), user.getId());
        ImportOkrDmSvc okrDmSvc = new ImportOkrDmSvc(mergeTask, user, okrTypes);
        //构建 itemRule
        okrDmSvc.doImportOkr(cmd.getAddKpis(), cmd.getAdds());
        //更新  item refAction
        taskKpiRepo.updateOkrKpi(new EmpId(cmd.getOpEmpId()), okrTypes, cmd.getAddKpis(), cmd.getAdds(), cmd.dels, cmd.edits,
                empEvalRule, user.isOpenAvgWeightCompute());
        auditRepo.updateFinishedValueAudit(cmd.getTenantId().getId(), cmd.getTaskUserId());
        // 通知 okr系统
        if (!cmd.getAddKpis().isEmpty()) {
            CompanyPo company = deptEmpDao.getTenant(cmd.getTenantId());
            for (EvalKpi add : cmd.getAddKpis()) {
                okrAclSvc.addRefAction(company.getDingCorpId(), add.getRefOkr().getActionId(), add.getKpiTypeId());
            }
        }
        //完成值审核
        user.setKpis(kpiDao.listEmpEvalKpiItem(cmd.getTenantId(), cmd.getTaskUserId()).getDatas());
        user.setEmpEvalRule(empEvalRule);
        return user;
    }

    public PagedList<MyTaskUserPo> pagedMyHandleTask(MyHandleTaskQuery taskQuery) {
        return taskUserDao.queryMyHandleTask(taskQuery);
    }

    public List<OkrTaskPo> listOkrKRs(TenantId tenantId, String userId, String kpiTypeId) {
        List<OkrKRPo> okrKRPos = kpiDao.listOkrKRs(tenantId, userId, kpiTypeId);
        okrAclSvc.loadOkrIf(tenantId, okrKRPos);
        return buildOkrTask(okrKRPos);
    }

    private List<OkrTaskPo> buildOkrTask(List<OkrKRPo> okrKRPos) {
        //从okrKRPo中构建OkrTargetPo
        List<OkrTargetPo> okrTargetPos = new ArrayList<>();
        Map<String, List<OkrKRPo>> krCollect = okrKRPos.stream().collect(Collectors.groupingBy(OkrKRPo::getTargetId));
        for (Map.Entry<String, List<OkrKRPo>> entry : krCollect.entrySet()) {
            OkrTargetPo okrTargetPo = new OkrTargetPo();
            okrTargetPo.accept(entry.getValue().get(0));
            okrTargetPo.setKrs(entry.getValue());
            okrTargetPos.add(okrTargetPo);
        }
        Map<String, List<OkrTargetPo>> targetCollect = okrTargetPos.stream().filter(po -> po.getOkrTaskId() != null).collect(Collectors.groupingBy(OkrTargetPo::getOkrTaskId));

        List<OkrTaskPo> okrTaskPos = new ArrayList<>();
        for (Map.Entry<String, List<OkrTargetPo>> entry : targetCollect.entrySet()) {
            OkrTaskPo okrTaskPo = new OkrTaskPo();
            okrTaskPo.accept(entry.getValue().get(0));
            okrTaskPo.setTargets(entry.getValue());
            okrTaskPos.add(okrTaskPo);
        }
        return okrTaskPos;
    }

    public List<OkrTask> listOkr(OkrQuery query) {
        TenantId tenantId = new TenantId(query.getCompanyId());
        CompanyDo company = companyDao.getCompany(tenantId);
        query.setCorpId(company.getDingCorpId());
        if (StringUtils.isNotBlank(query.getEmpId())) {
            KpiEmployee emp = kpiEmpDao.findEmployee(tenantId, query.getEmpId());
            query.setDingUserId(emp.getDingUserId());
        }
        Map<String, String> params = new HashMap<>();
        params.put("corpId", query.getCorpId());
        params.put("startTime", query.getStartTime());
        params.put("endTime", query.getEndTime());
        params.put("taskType", query.getTaskType());
        params.put("userName", query.getUserName());
        params.put("deptName", query.getDeptName());
        params.put("dingUserId", query.getDingUserId());
        params.put("userId", query.getDingUserId());
        params.put("returnTaskInfo", Boolean.FALSE.toString()); //说是传false优化返回速度
        params.put("openOkrScore", String.valueOf(query.getOpenOkrScore()));
        List<OkrTask> okrTasks = okrAclSvc.listOkrTask(params);
//        if (CollUtil.isEmpty(okrTasks)) {
//            return okrTasks;
//        }
//        for (OkrTask okrTask : okrTasks) {
//            okrTask.getTargetList().forEach(okrTarget -> {
//                okrTarget.initOpenOkrScore(query.getOpenOkrScore());
//            });
//        }
        return okrTasks;
    }

    @Autowired
    private LoginAcl loginAcl;
    @Autowired
    private ExtAuthAcl extAuthAcl;

    public CardDataReps cardData(CardDataReq query) {
        CardDataReps cardDataReps = new CardDataReps(query.parseFirstDynamicDataSourceId());
        Company company = loginAcl.getCompany(new CorpId(query.getCorpId()));
        if (company == null) {//未开通应用
            CardDataReps.ShowDatas data = new CardDataReps.ShowDatas();
            cardDataReps.addFirstData(data);
            return cardDataReps;
        }
        TenantId tenantId = new TenantId(company.getId());
        Employee targetEmp = null;
        String lookUserId = null;
        Employee lookEmp = null;
        String lastEvalUserId = null;
        EmpEvalMerge evalMerge = null;
        //无数据
        if ((targetEmp = deptEmpDao.getEmployee(tenantId, query.parseFirstTargetUserId())) == null
                || StrUtil.isBlank(lookUserId = extAuthAcl.getbyunionid(company.getDingCorpId(), query.getUserId()))
                || (lookEmp = deptEmpDao.getEmployee(tenantId, lookUserId)) == null
                || StrUtil.isBlank(lastEvalUserId = taskUserDao.getLastEvalUserId(tenantId, targetEmp.getEmployeeId()))
                || (evalMerge = empEvalRuleRepo.getEmpEvalMerge(tenantId, lastEvalUserId, EmpEvalMerge.type)) == null
                || evalMerge.getKpiTypes() == null
                || evalMerge.getKpiTypes().getDatas() == null) {
            log.info("parseFirstTargetUserId={}, lookUserId={}, eq={}", query.parseFirstTargetUserId(), lookUserId, StrUtil.equals(query.parseFirstTargetUserId(), lookUserId));
            CardDataReps.ShowDatas data = new CardDataReps.ShowDatas(Collections.emptyList());
            data.setMe(StrUtil.equals(query.parseFirstTargetUserId(), lookUserId));
            cardDataReps.addFirstData(data);
            return cardDataReps;
        }
        List<EmpEvalKpiType> types = evalMerge.getKpiTypes().getDatas();
        //查看人员,看是否有权限:自己看自己的||主管理员||配置有管理范围内
        AdminSetDo currentAdmin;
        if (StrUtil.equals(query.parseFirstTargetUserId(), lookUserId)
                || ((currentAdmin = adminSetDao.findAdminByEmpId(tenantId, lookEmp.getEmployeeId())) != null && currentAdmin.isMainAdmin())
                || adminSetDao.isAtAdminAuth(tenantId, lookEmp.getEmployeeId(), targetEmp.getEmployeeId())) {
            CardDataReps.ShowDatas data = new CardDataReps.ShowDatas(company.getDingCorpId(), types, evalMerge.isTypeWeightOpen());
            data.setMe(StrUtil.equals(query.parseFirstTargetUserId(), lookUserId));
            cardDataReps.addFirstData(data);
            return cardDataReps;
        }
        //无权限查看
        CardDataReps.ShowDatas data = new CardDataReps.ShowDatas(Collections.emptyList());
        cardDataReps.noAuthData(data);
        return cardDataReps;
    }

    public PagedList<ExportInputValue> pagedFinishValue(EvaluateTaskKpiQueryVO queryVO) {
        CompanyConf company = companyDao.findCompanyConf(new TenantId(queryVO.getCompanyId()));
        return kpiDao.pagedFinishValue(queryVO, company);
    }

    public List<ExportInputValue> listFinishValue(EvaluateTaskKpiQueryVO queryVO) {
        CompanyConf company = companyDao.findCompanyConf(new TenantId(queryVO.getCompanyId()));
        return kpiDao.listFinishValue(queryVO, company);
    }

    public void handlerDownFinishValueByItemExcel(EvaluateTaskKpiQueryVO query, ByteArrayOutputStream outputStream) {
        List<ExportInputValue> values = this.listFinishValue(query);
        if (CollUtil.isEmpty(values)) {
            return;
        }
        ExportInputFinishValueDmSvc dmSvc = new ExportInputFinishValueDmSvc();
        dmSvc.accOp(query.getPerformanceType(), "批量录入完成值-按指标", values);
        dmSvc.exportExcel(outputStream);
    }

    @Transactional
    @Async
    public void importFinishValue(CompanyConf conf, ImportFinishValueCmd cmd) {
        cmd.tracted();
        if (!cmd.hasValue()) {
            return;
        }
        conf = conf == null ? confDao.findCompanyConf(cmd.getTenantId()) : conf;
        EvalUser evalUser = userRepo.getTaskUser(cmd.getTenantId(), cmd.getTaskUserId());
        evalUser.inputFinishChanged(evalUser.convertFinishValues(cmd.getFinishValues()));
        ModifyFinishValueDmSvc valueDmSvc = new ModifyFinishValueDmSvc(conf, evalUser, cmd.getIsSubmit(), cmd.getOpEmpId());
        valueDmSvc.importFinishValue(cmd.getFinishValues());
        taskKpiRepo.saveFinishValue(valueDmSvc);
        AdminTask adminTask = adminTaskDao.getAdminTaskBase(cmd.getTenantId(), new TaskId(evalUser.getTaskId()));
        if (adminTask.isOpenSendFinishValueChangedMsg()) {
            Emp emp = kpiEmpDao.findEmp(cmd.getTenantId(), cmd.getOpEmpId().getId());
            empEvalDmSvc.parseInputNotify(adminTask.getInputNotifyConf(), evalUser, cmd.getOpEmpId().getId());
            cycleEvalDmSvc.batchSendFinishValueChanged(new Name(adminTask.getTaskName()), evalUser, adminTask.inputChangedRecEmpIds(), emp.getName(), cmd.getOpEmpId().getId());
        }
    }

    @Transactional
    public void handlerImportFinishValueExcel(String companyId, String opEmpId, List<ExcelFinishValue> excels, Integer importType) {
        if (CollUtil.isEmpty(excels)) {
            throw new KpiI18NException(" file data can not is empty", "文件内容不能为空！");
        }
        List<String> taskUserIds = CollUtil.distinct(CollUtil.map(excels, val -> val.getTaskUserId(), true));
        List<EvalKpi> items = kpiDao.listItemByTaskUserIds(companyId, taskUserIds, opEmpId);
        ImportInputFinishValueDmSvc dmSvc = new ImportInputFinishValueDmSvc(companyId, opEmpId, importType, excels, items);
        dmSvc.buildCache();
        taskKpiRepo.bacthAddImportFinishValCache(new TenantId(companyId), new EmpId(opEmpId), dmSvc.getValCaches());
    }

    public void bathcAddImportFinishValCache(TenantId tenantId, EmpId opEmpId, List<CacheFinishValueCmd> cmds) {
        List<ImportFinishValCache> all = new ArrayList<>();
        for (CacheFinishValueCmd cmd : cmds) {
            for (FinishValue value : cmd.getFinishValues()) {
                ImportFinishValCache importV = Convert.convert(ImportFinishValCache.class, value);
                importV.setTaskKpiId(value.getId());
                importV.setOperateEmpId(opEmpId.getId());
                 //importV.value(value.getItemFinishValue(), value.getWorkItemFinishValue(), value.getItemFinishValueText(), value.getFinishValueComment(), value.getFiles());
                all.add(importV);
            }
        }
        tx.runTran(() -> taskKpiRepo.updateImportFinishValCache(tenantId, opEmpId, all));
        List<ImportFinishValCache> imports = kpiDao.listImportFinishValCache(tenantId.getId(), opEmpId.getId());
        List<InputFinishValCache> caches = new ArrayList<>();
        Set<String> taskUserIds = new HashSet<>();
        for (ImportFinishValCache importCache : imports) {
            InputFinishValCache cache = Convert.convert(InputFinishValCache.class, importCache);
            cache.accept(tenantId.getId(), opEmpId.getId(), importCache.getTaskUserId());
            caches.add(cache);
            taskUserIds.add(importCache.getTaskUserId());
        }
        tx.runTran(() -> taskKpiRepo.batchSaveFinishValueCache(tenantId, opEmpId, taskUserIds, caches));
    }

    public void  updateImportFinishValCache(TenantId tenantId, EmpId opEmpId, List<ImportFinishValCache> all){
        taskKpiRepo.updateImportFinishValCache(tenantId, opEmpId, all);
    }




    public PagedList<ExportInputValue> pagedImportFinishValCache(String companyId, String opEmpId, Integer pageNo, Integer pageSize) {
        return kpiDao.pagedImportFinishValCache(companyId, opEmpId, pageNo, pageSize);
    }

    public List<SubmitFinishValueCmd> listImportFinishVal(String companyId, String opEmpId) {
        List<ImportFinishValCache> imports = kpiDao.listImportFinishValCache(companyId, opEmpId);
        if (CollUtil.isEmpty(imports)) {
            return new ArrayList<>();
        }
        List<SubmitFinishValueCmd> cmds = new ArrayList<>();
        ListWrap<ImportFinishValCache> wrap = new ListWrap<>(imports).groupBy(val -> val.getTaskUserId());
        wrap.getGroups().forEach((k, v) -> {
            SubmitFinishValueCmd cmd = new SubmitFinishValueCmd(new TenantId(companyId), new EmpId(opEmpId), k);
            List<FinishValue> values = new ArrayList<>();
            v.forEach(valCache -> {
                if (valCache.allValueFieldIsNull()) {
                    return;
                }
                FinishValue value = new ToDataBuilder<>(valCache, FinishValue.class).data();
                value.setId(valCache.getTaskKpiId());
                values.add(value);
            });
            cmd.setFinishValues(values);
            cmds.add(cmd);
        });
        return cmds;
    }
}
