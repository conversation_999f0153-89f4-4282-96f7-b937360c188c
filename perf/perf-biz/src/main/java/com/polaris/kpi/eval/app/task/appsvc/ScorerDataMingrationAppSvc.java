package com.polaris.kpi.eval.app.task.appsvc;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.eval.ConcurrentBitMap;
import cn.com.polaris.kpi.eval.RecordIndexCalculator;
import cn.hutool.core.collection.CollUtil;
import com.polaris.kpi.eval.app.TransactionWrap;
import com.polaris.kpi.eval.app.task.result.FailureRecordResult;
import com.polaris.kpi.eval.app.task.result.MigrationProgressResult;
import com.polaris.kpi.eval.app.task.result.MigrationResultConverter;
import com.polaris.kpi.eval.domain.task.dmsvc.migration.ScoreMigrationManagerDmSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.migration.ScorerDataMingrationDmSvc;
import com.polaris.kpi.eval.domain.task.entity.EmpEvalScorer;
import com.polaris.kpi.eval.domain.task.entity.MigrationConfig;
import com.polaris.kpi.eval.domain.task.entity.OptimizedBitmapProgress;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalRule;
import com.polaris.kpi.eval.domain.task.entity.empeval.migration.MigrationExecutor;
import com.polaris.kpi.eval.domain.task.entity.empeval.migration.MigrationTask;
import com.polaris.kpi.eval.domain.task.repo.*;
import com.polaris.kpi.eval.infr.task.dao.TaskUserDao;
import com.polaris.kpi.eval.infr.task.repimpl.EmpEvalRuleRepoImpl;
import com.polaris.kpi.org.domain.dept.entity.CompanyConf;
import com.polaris.kpi.org.infr.company.dao.CompanyDaoImpl;
import com.polaris.kpi.org.infr.emp.dao.KpiEmpDao;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.lufei.ibatis.mapper.PagedList;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.function.BiFunction;
import com.polaris.kpi.org.domain.dept.entity.Company;
import org.apache.commons.lang3.exception.ExceptionUtils;

/**
 * 评分人数据迁移应用服务
 * 负责业务编排，具体逻辑委托给领域对象和领域服务
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
@Service
@Slf4j
public class ScorerDataMingrationAppSvc {

    // ==================== 基础设施依赖 ====================
    @Autowired
    private EmpEvalScorerRepo empEvalScorerRepo;
    @Autowired
    private KpiEmpDao kpiEmpDao;
    @Autowired
    private OnScoreEvalRepo onScoreEvalRepo;
    @Autowired
    private TransactionWrap tx;
    @Autowired
    private EmpEvalRuleRepoImpl empRuleRepo;
    @Autowired
    private CompanyDaoImpl companyDao;
    @Autowired
    private TaskUserRepo userRepo;
    @Autowired
    private TaskUserDao taskUserDao;
    @Autowired
    private MigrationResultConverter migrationResultConverter;
    @Resource
    private MigrationConfig migrationConfig;

    // ==================== 位图管理组件 ====================
    @Autowired(required = false)
    private ConcurrentBitMap bitMap;
    @Autowired(required = false)
    private RecordIndexCalculator indexCalculator;

    // ==================== DDD领域对象 ====================
    private ScoreMigrationManagerDmSvc scoreMigrationDmSvc;


    // ==================== 配置参数 ====================
    private static final int DEFAULT_PAGE_SIZE = 1000;
    private static final int COMPANY_PAGE_SIZE = 50;
    private static final int VERSION = 2000000;
    private static final long PAGINATION_DELAY_MS = 50L;
    private static final long COMPANY_DELAY_MS = 100L;
    private static final long COMPANY_PAGINATION_DELAY_MS = 300L;


    // ==================== 任务管理 ====================
    private final Map<String, MigrationTask> activeTasks = new ConcurrentHashMap<>();
    private final Map<String, OptimizedBitmapProgress> progressMap = new ConcurrentHashMap<>();

    // ==================== 性能优化的暂停检查机制 ====================
    private volatile boolean paused = false;
    private volatile boolean stopped = false;
    private int checkCounter = 0;
    private int checkInterval = 50;
    
    // 缓存机制
    private volatile OptimizedBitmapProgress cachedProgress = null;
    private volatile long lastProgressUpdate = 0;
    private static final long PROGRESS_CACHE_TTL = 1000;
    
    // 保存优化
    private int saveCounter = 0;
    private int saveInterval = 10;
    
    /**
     * 高性能暂停检查
     */
    private boolean shouldCheckPause() {
        return (++checkCounter % checkInterval) == 0;
    }
    
    /**
     * 获取暂停状态（高性能）
     */
    private boolean isPausedOptimized() {
        return paused;
    }
    
    /**
     * 获取停止状态（高性能）
     */
    private boolean isStoppedOptimized() {
        return stopped;
    }
    
    /**
     * 重置状态
     */
    private void resetMigrationStatus() {
        paused = false;
        stopped = false;
        checkCounter = 0;
        saveCounter = 0;
    }
    
    /**
     * 优化的进度保存
     */
    private void saveProgressOptimized() {
        if (++saveCounter % saveInterval != 0) {
            return;
        }
        
        try {
            OptimizedBitmapProgress p = currentProgress();
            if (p == null) return;
            if (p.getStatus() != OptimizedBitmapProgress.MigrationStatus.RUNNING) {
                return;
            }
            p.saveMetadata();
        } catch (Throwable ignore) {}
    }

    @PostConstruct
    public void init() {
        log.info("Initializing ScorerDataMingrationAppSvc with DDD architecture");
        initializeDomainObjects();
        initializeOptimizedComponents();
    }

    /**
     * 初始化DDD领域对象
     */
    private void initializeDomainObjects() {
        try {
            // 初始化迁移领域服务
            scoreMigrationDmSvc = new ScoreMigrationManagerDmSvc(migrationConfig);
            log.info("DDD domain objects initialized successfully");
        } catch (Exception e) {
            log.error("Failed to initialize DDD domain objects", e);
            throw new RuntimeException("Failed to initialize DDD domain objects", e);
        }
    }

    /**
     * 初始化优化组件
     */
    private void initializeOptimizedComponents() {
        try {
            if (bitMap == null) {
                log.warn("BitmapManager is null, attempting manual initialization");
                bitMap = new cn.com.polaris.kpi.eval.ConcurrentBitMap();
                log.info("Successfully initialized ConcurrentBitMap manually");
            }

            if (indexCalculator == null) {
                log.warn("IndexCalculator is null, attempting manual initialization");
                indexCalculator = new cn.com.polaris.kpi.eval.RecordIndexCalculator();
                log.info("Successfully initialized RecordIndexCalculator manually");
            }

            log.info("Optimized migration components initialized successfully: bitmapManager={}, indexCalculator={}",
                    bitMap != null, indexCalculator != null);

        } catch (Exception e) {
            log.error("Failed to initialize optimized migration components", e);
            log.warn("Disabled bitmap optimization due to component initialization failure");
        }
    }

    // 获取或创建任务
    private MigrationTask ensureTask(String sessionId, String migrationType, String tid, String createdBy) {
        MigrationTask task = activeTasks.get(sessionId);
        if (task == null) {
            MigrationTask.MigrationType taskType = MigrationTask.MigrationType.valueOf(migrationType);
            task = scoreMigrationDmSvc.createMigrationTask(sessionId, taskType, tid, createdBy);
            activeTasks.put(sessionId, task);
            log.info("ensureTask: created new task, sessionId={}, type={}", sessionId, migrationType);
        } else {
            log.info("ensureTask: reuse existing task, sessionId={}", sessionId);
        }
        return task;
    }

    // 加载或初始化进度
    private void ensureProgress(String sessionId, String migrationType) {
        try {
            com.polaris.kpi.eval.domain.task.entity.OptimizedBitmapProgress persisted = com.polaris.kpi.eval.domain.task.entity.OptimizedBitmapProgress.loadMetadata(sessionId);
            if (persisted != null) {
                progressMap.put(sessionId, persisted);
                log.info("ensureProgress: loaded persisted metadata for sessionId={}", sessionId);
                return;
            }
        } catch (Throwable t) {
            log.warn("ensureProgress: load metadata failed, sessionId={}, msg={}", sessionId, t.getMessage());
        }
        com.polaris.kpi.eval.domain.task.entity.OptimizedBitmapProgress progress = new com.polaris.kpi.eval.domain.task.entity.OptimizedBitmapProgress(sessionId, migrationType);
        progress.start();
        progressMap.put(sessionId, progress);
        log.info("ensureProgress: initialized new progress for sessionId={}", sessionId);
    }

    // 辅助：当前会话ID
    private String currentSessionId() {
        return MDC.get("migrationSessionId");
    }

    // 辅助：当前任务类型（优先从 activeTasks，其次从 progressMap）
    private String currentTaskTypeName() {
        String sid = currentSessionId();
        if (sid != null) {
            MigrationTask t = activeTasks.get(sid);
            if (t != null && t.getMigrationType() != null) return t.getMigrationType().name();
            OptimizedBitmapProgress p = progressMap.get(sid);
            if (p != null && p.getMigrationType() != null) return p.getMigrationType();
        }
        return null;
    }

    // 并发会话检查：是否存在运行中的不同类型会话
    private void ensureNoConflictingSessions(String requestedType) {
        for (MigrationTask t : activeTasks.values()) {
            if (t == null) continue;
            if (t.isRunning()) {
                String runningType = (t.getMigrationType() == null ? null : t.getMigrationType().name());
                if (runningType != null && !runningType.equalsIgnoreCase(requestedType)) {
                    throw new RuntimeException("Another migration session is running with different type: " + runningType);
                }
            }
        }
    }
    /**
     * 单个公司迁移（已完成用户）
     */
    public String migrateCompanyFinished(String tid, String companyId) {
        log.info("Starting single company migration (finished): companyId={}", companyId);

        try {
            // 生成会话ID
            String sessionId = generateOptimizedSessionId();
            MigrationTask task = ensureTask(sessionId, "FINISHED", tid, null);
            // 设置公司ID
            task.setCompanyId(companyId);
            ensureProgress(sessionId, "FINISHED");

            // 设置MDC
            MDC.put("migrationSessionId", sessionId);
            MDC.put("migrationType", "FINISHED");

            // 创建迁移执行器
            MigrationExecutor executor = new MigrationExecutor(migrationConfig);
            
            // 执行单个公司迁移
            executeFinishedUsersMigration(task, executor);

            log.info("Single company migration (finished) completed: companyId={}, sessionId={}", companyId, sessionId);
            return sessionId;

        } catch (Exception e) {
            log.error("Failed to migrate single company (finished): companyId={}", companyId, e);
            throw new RuntimeException("Failed to migrate single company (finished): " + e.getMessage(), e);
        } finally {
            MDC.remove("migrationSessionId");
            MDC.remove("migrationType");
        }
    }

    /**
     * 单个公司迁移（未完成用户）
     */
    public String migrateCompanyNoFinished(String tid, String companyId) {
        log.info("Starting single company migration (no-finished): companyId={}", companyId);

        try {
            // 生成会话ID
            String sessionId = generateOptimizedSessionId();
            MigrationTask task = ensureTask(sessionId, "NO_FINISHED", tid, null);
            // 设置公司ID
            task.setCompanyId(companyId);
            ensureProgress(sessionId, "NO_FINISHED");

            // 设置MDC
            MDC.put("migrationSessionId", sessionId);
            MDC.put("migrationType", "NO_FINISHED");

            // 创建迁移执行器
            MigrationExecutor executor = new MigrationExecutor(migrationConfig);
            
            // 执行单个公司迁移
            executeNoFinishedUsersMigration(task, executor);

            log.info("Single company migration (no-finished) completed: companyId={}, sessionId={}", companyId, sessionId);
            return sessionId;

        } catch (Exception e) {
            log.error("Failed to migrate single company (no-finished): companyId={}", companyId, e);
            throw new RuntimeException("Failed to migrate single company (no-finished): " + e.getMessage(), e);
        } finally {
            MDC.remove("migrationSessionId");
            MDC.remove("migrationType");
        }
    }

    /**
     * 单个公司迁移（混合模式：串行执行）
     */
    public String migrateCompanyMixed(String tid, String companyId) {
        log.info("Starting single company migration (mixed): companyId={}", companyId);

        try {
            // 生成会话ID
            String sessionId = generateOptimizedSessionId();
            MigrationTask task = ensureTask(sessionId, "MIXED", tid, null);
            // 设置公司ID
            task.setCompanyId(companyId);
            ensureProgress(sessionId, "MIXED");

            // 设置MDC
            MDC.put("migrationSessionId", sessionId);
            MDC.put("migrationType", "MIXED");

            // 创建迁移执行器
            MigrationExecutor executor = new MigrationExecutor(migrationConfig);

            // 串行执行：先已完成，后未完成
            log.info("Step 1: Starting finished users migration for company: {}", companyId);
            executeFinishedUsersMigration(task, executor);

            log.info("Step 2: Starting no-finished users migration for company: {}", companyId);
            executeNoFinishedUsersMigration(task, executor);

            log.info("Single company migration (mixed) completed: companyId={}, sessionId={}", companyId, sessionId);
            return sessionId;

        } catch (Exception e) {
            log.error("Failed to migrate single company (mixed): companyId={}", companyId, e);
            throw new RuntimeException("Failed to migrate single company (mixed): " + e.getMessage(), e);
        } finally {
            MDC.remove("migrationSessionId");
            MDC.remove("migrationType");
        }
    }
    /**
     * 启动优化的大规模数据迁移（业务编排）
     */
    public String startOptimizedMigration(String tid, String migrationType, String operatorId) {
        log.info("Starting optimized migration: type={}, operator={}", migrationType, operatorId);

        try {
             // 重置本地状态
            resetMigrationStatus();

            ensureNoConflictingSessions(migrationType);

            // 新会话
            String taskId = generateOptimizedSessionId();
            MigrationTask task = ensureTask(taskId, migrationType, tid, operatorId);
            ensureProgress(taskId, migrationType);
            log.info("Optimized migration start : taskId={}, type={}", taskId, migrationType);
            // 启动并异步执行
            scoreMigrationDmSvc.startMigrationTask(task);
            executeMigrationAsync(tid, taskId);

            log.info("Optimized migration started successfully: taskId={}, type={}", taskId, migrationType);
            return taskId;

        } catch (Exception e) {
            log.error("Failed to start optimized migration: type={}", migrationType, e);
            throw new RuntimeException("Failed to start optimized migration: " + e.getMessage(), e);
        }
    }

    public String startOptimizedMigration(String tid, String migrationType, String operatorId,Integer shardIndex, Integer shardTotal) {
        log.info("Starting optimized migration: type={}, operator={}, shard={}/{}",
                migrationType, operatorId, shardIndex, shardTotal);

        try {
            resetMigrationStatus();
            ensureNoConflictingSessions(migrationType);

            String taskId = generateOptimizedSessionId();
            MigrationTask task = ensureTask(taskId, migrationType, tid, operatorId);

            // 设置分片信息到任务中
            task.setShardIndex(shardIndex);
            task.setShardTotal(shardTotal);

            ensureProgress(taskId, migrationType);
            scoreMigrationDmSvc.startMigrationTask(task);
            executeMigrationAsync(tid, taskId);

            return taskId;
        } catch (Exception e) {
            log.error("Failed to start optimized migration", e);
            throw new RuntimeException("Failed to start optimized migration: " + e.getMessage(), e);
        }
    }

    public String startOptimizedMigrationShard21(String tid, String migrationType, String operatorId,Integer shardIndex, Integer shardTotal) {
        log.info("Starting optimized migration Shard21: type={}, operator={}, shard={}/{}",
                migrationType, operatorId, shardIndex, shardTotal);

        try {
            resetMigrationStatus();
            ensureNoConflictingSessions(migrationType);

            String taskId = generateOptimizedSessionId();
            MigrationTask task = ensureTask(taskId, migrationType, tid, operatorId);

            // 设置分片信息到任务中
            task.setShardIndex(shardIndex);
            task.setShardTotal(shardTotal);
            task.setOpenMigration21(1);

            ensureProgress(taskId, migrationType);
            scoreMigrationDmSvc.startMigrationTask(task);
            executeMigrationAsync(tid, taskId);

            return taskId;
        } catch (Exception e) {
            log.error("Failed to start optimized migration", e);
            throw new RuntimeException("Failed to start optimized migration: " + e.getMessage(), e);
        }
    }

    /**
     * 恢复中断的优化迁移任务（业务编排）
     */
    public boolean resumeOptimizedMigration(String tid, String sessionId) {
        log.info("Attempting to resume optimized migration: {}", sessionId);
        
        try {
            // 重置本地状态
            resetMigrationStatus();

            // 1. 加载位图文件
            OptimizedBitmapProgress persisted = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (persisted == null) {
                log.error("Migration progress not found in bitmap file: {}", sessionId);
                return false;
            }
            
            // 2. 检查状态，如果是 RUNNING 则重置为可恢复状态
            if (OptimizedBitmapProgress.MigrationStatus.RUNNING.equals(persisted.getStatus())) {
                log.warn("Migration was RUNNING before restart, resetting to PAUSED for resume");
                persisted.setStatus(OptimizedBitmapProgress.MigrationStatus.PAUSED);
                persisted.saveMetadata(); // 保存状态变更
            }
            
            // 3. 检查是否可以恢复
            if (!persisted.canResume()) {
                log.warn("Migration cannot be resumed, current status: {}", persisted.getStatus());
                return false;
            }
            
            // 4. 继续恢复逻辑...
            
            // 5. 重新创建任务对象
            MigrationTask task = scoreMigrationDmSvc.createMigrationTask(sessionId, 
                MigrationTask.MigrationType.valueOf(persisted.getMigrationType()), tid, "RESUME");
            
            // 6. 恢复内存中的状态
            activeTasks.put(sessionId, task);
            progressMap.put(sessionId, persisted);
            
            log.info("Restored task and progress to memory: sessionId={}, type={}", 
                sessionId, persisted.getMigrationType());
            
            // 7. 重新启动迁移
            scoreMigrationDmSvc.startMigrationTask(task);
            executeMigrationAsync(tid, sessionId);
            
            log.info("Optimized migration resumed successfully: {}, type={}", sessionId, persisted.getMigrationType());
            return true;
            
        } catch (Exception e) {
            log.error("Failed to resume optimized migration: sessionId={}", sessionId, e);
            return false;
        }
    }

    /**
     * 暂停优化迁移任务（业务编排）
     */
    public boolean pauseOptimizedMigration(String sessionId) {
        log.info("Attempting to pause optimized migration: {}", sessionId);

        try {
            MigrationTask task = activeTasks.get(sessionId);
            if (task == null) {
                log.error("Migration task not found for session: {}", sessionId);
                return false;
            }

            scoreMigrationDmSvc.pauseMigrationTask(task);
            paused = true; // 设置本地状态
            
            log.info("Optimized migration paused successfully: {}", sessionId);
            return true;

        } catch (Exception e) {
            log.error("Failed to pause optimized migration: {}", sessionId, e);
            return false;
        }
    }

    /**
     * 获取优化迁移状态（业务编排）
     */
    public OptimizedBitmapProgress getOptimizedMigrationStatus(String sessionId) {
        try {
            return progressMap.get(sessionId);
        } catch (Exception e) {
            log.error("Failed to get optimized migration status: {}", sessionId, e);
            return null;
        }
    }

    /**
     * 获取迁移统计信息（业务编排）
     */
    public Map<String, Object> getMigrationStatistics(String sessionId) {
        try {
            MigrationTask task = activeTasks.get(sessionId);
            if (task == null) {
                return new HashMap<>();
            }

            Map<String, Object> stats = scoreMigrationDmSvc.getMigrationTaskStatistics(task);
            stats.putAll(scoreMigrationDmSvc.getMemoryStatistics());
            stats.putAll(scoreMigrationDmSvc.getExceptionStatistics());

            return stats;
        } catch (Exception e) {
            log.error("Failed to get migration statistics: {}", sessionId, e);
            return new HashMap<>();
        }
    }

    // ==================== 调试和工具方法 ====================

    /**
     * 单个已完成用户迁移（调试用）
     */
    public void migrateFinishedOne(String companyId, String taskUserId) {
        log.info("Starting single finished user migration: companyId={}, taskUserId={}", companyId, taskUserId);
        try {
            //   processFinishedUserWithDmSvc(companyId, taskUserId);
            processFinishedUserWithDmSvcBatch(companyId, Collections.singletonList(taskUserId));
            log.info("Single finished user migration completed: taskUserId={}", taskUserId);
        } catch (Exception e) {
            log.error("Single finished user migration failed: taskUserId={}", taskUserId, e);
            throw e;
        }
    }

    /**
     * 单个未完成用户迁移（调试用）
     */
    public void migrateNoFinishOne(String companyId, String taskUserId) {
        log.info("Starting single no-finished user migration: companyId={}, taskUserId={}", companyId, taskUserId);
        try {
            processNoFinishedUserWithDmSvcBatch(companyId, Collections.singletonList(taskUserId));
            //    processNoFinishedUserWithDmSvc(companyId, taskUserId);
            log.info("Single no-finished user migration completed: taskUserId={}", taskUserId);
        } catch (Exception e) {
            log.error("Single no-finished user migration failed: taskUserId={}", taskUserId, e);
            throw e;
        }
    }

    /**
     * 日志任务状态（调试用）
     */
    public void logTaskStatus() {
        log.info("Logging task status - active tasks count: {}", activeTasks.size());
        activeTasks.forEach((sessionId, task) -> {
            log.info("Task status - sessionId: {}, status: {}, progress: {}%",
                    sessionId, task.getStatus(), task.getProgressPercentage());
        });
    }

    // ==================== 转换方法（业务编排） ====================

    /**
     * 转换为迁移进度结果
     */
    public MigrationProgressResult convertToMigrationProgressResult(OptimizedBitmapProgress progress) {
        return migrationResultConverter.convertToMigrationProgressResult(progress);
    }


    /**
     * 转换为失败记录结果
     */
    public FailureRecordResult convertToFailureRecordResult(OptimizedBitmapProgress.FailureRecord failureRecord) {
        return migrationResultConverter.convertToFailureRecordResult(failureRecord);
    }

    // ==================== 私有辅助方法 ====================

    private boolean isDeadlockException(Throwable e) {
        Throwable cur = e;
        while (cur != null) {
            // Spring 统一的无法获取锁异常也视为可重试
            if (cur instanceof org.springframework.dao.CannotAcquireLockException) {
                return true;
            }
            String msg = cur.getMessage();
            if (msg != null) {
                String lower = msg.toLowerCase();
                if (lower.contains("deadlock found")
                        || lower.contains("lock wait timeout exceeded") // 1205
                        || lower.contains("sqlstate(40001)")
                        || lower.contains("1213") // deadlock code
                        || lower.contains("1205")) { // lock wait timeout code
                    return true;
                }
            }
            cur = cur.getCause();
        }
        return false;
    }

    private void sleepQuietly(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 生成优化的会话ID
     */
    private String generateOptimizedSessionId() {
        return "OPT_" + System.currentTimeMillis() + "_" + java.util.UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 保存单个用户的所有数据（规则、节点、评分人明细）
     */
    private boolean saveSingleUserData(String companyId, String taskUserId, 
                                 ScorerDataMingrationDmSvc dmSvc, TenantId tenantId) {
        final int maxRetry = 5;
        int attempt = 1;
        
        while (attempt <= maxRetry) {
            try {
                tx.runTranRC(() -> {
                    // 1. 保存规则/节点数据
                    empRuleRepo.updateSuperiorScoreOrder(tenantId, taskUserId, dmSvc.getSuperiorScoreOrder());
                    onScoreEvalRepo.saveEvalUserNodeScore(dmSvc.getTaskUser(), dmSvc.getEvalMerge().getKpiTypes(), dmSvc.getTaskUser().getCreatedUser());
                    
                    // 2. 同步版本到评分人数据
                    Integer curVer = dmSvc.getTaskUser().getVersion();
                    dmSvc.applyVersionToScorers(curVer);
                    
                    // 3. 保存评分人明细数据
                    if (CollUtil.isNotEmpty(dmSvc.getEmpEvalScorers())) {
                        empEvalScorerRepo.batchAddEmpEvalScorer(companyId, taskUserId, dmSvc.getEmpEvalScorers());
                    }
                });
                
                // 成功：更新进度
                OptimizedBitmapProgress p = currentProgress();
                if (p != null) {
                    p.removeFailureRecord(taskUserId);
                    p.incrementProcessedAndSuccess();
                }
                return true;
                
            } catch (Throwable ex) {
                if (isDeadlockException(ex) && attempt < maxRetry) {
                    long backoff = Math.min(100L * (1L << (attempt - 1)), 1000L);
                    log.warn("Deadlock on user save, companyId={}, taskUserId={}, attempt={}, backoff={}ms",
                            companyId, taskUserId, attempt, backoff);
                    sleepQuietly(backoff);
                    attempt++;
                } else {
                    // 失败：只记录失败信息，不标记为已处理
                    OptimizedBitmapProgress p = currentProgress();
                    if (p != null) {
                        p.recordFailure(taskUserId, companyId, p.getProcessedCount().get(), ex.getMessage(), "USER_SAVE");
                    }
                    log.error("User save failed, companyId={}, taskUserId={}, attempt={}",
                            companyId, taskUserId, attempt, ex);
                    return false;
                }
            }
        }
        return false;
    }

    /**
     * 保存用户数据（优化版：按用户串行 + 小事务 + 死锁重试）
     */
    private void saveUserDataBatch(String companyId, ListWrap<ScorerDataMingrationDmSvc> dmSvcWrap) {
        long startTime = System.currentTimeMillis();
        log.info("Starting batch data save for company: {}, dmSvcCount: {}",
                companyId, dmSvcWrap.getDatas().size());
        
        int companySuccessUsers = 0;
        int companyFailedUsers = 0;
        java.util.List<String> failedUserIds = new java.util.ArrayList<>();
        
        try {
            TenantId tenantId = new TenantId(companyId);
            
            // 收集全部评分人用于统一补充评分人信息
            ListWrap<EmpEvalScorer> empEvalScorers = new ListWrap<>();
            dmSvcWrap.getDatas().forEach(dmSvc -> empEvalScorers.addAll(dmSvc.getEmpEvalScorers()));
            
            List<String> scorerIds = empEvalScorers.getDatas().stream()
                    .map(EmpEvalScorer::getScorerId)
                    .collect(Collectors.toList());
            // 一次性获取所有评分人信息
            Map<String, KpiEmp> kpiMap = new HashMap<>();
            if (CollUtil.isNotEmpty(scorerIds)) {
                kpiEmpDao.listByEmpAsMap(tenantId, scorerIds);
            }
            // 按用户串行保存，每个用户一个事务
            for (ScorerDataMingrationDmSvc dmSvc : dmSvcWrap.getDatas()) {
                String taskUserId = dmSvc.getTaskUser().getId();
                
                if (CollUtil.isEmpty(dmSvc.getEmpEvalScorers())) {
                    log.info("No scorer info for company: {}，taskUserId: {}", companyId, taskUserId);
                    continue;
                }
                
                dmSvc.accScorerInfo(kpiMap);
                dmSvc.getEmpEvalScorers().sort(Comparator.comparing(EmpEvalScorer::getScorerId, Comparator.nullsLast(String::compareTo)));
                
                // 单个用户的所有数据在一个事务中保存
                boolean userSaved = saveSingleUserData(companyId, taskUserId, dmSvc, tenantId);
                
                if (userSaved) {
                    companySuccessUsers++;
                    log.info("User processed ok: companyId={}, taskUserId={}, scorers={}",
                            companyId, taskUserId, dmSvc.getEmpEvalScorers().size());
                } else {
                    companyFailedUsers++;
                    failedUserIds.add(taskUserId);
                }
            }
            
            // 记录统计信息
            log.info("Batch data save completed: companyId={}, success={}, failed={}, durationMs={}",
                    companyId, companySuccessUsers, companyFailedUsers, (System.currentTimeMillis() - startTime));
            
        } catch (Exception e) {
            log.error("Error saving user data: companyId={}, dmSvcCount={}",
                    companyId, dmSvcWrap.getDatas().size(), e);
            throw e;
        }
    }

    /**
     * 合并处理：一次性完成配置设置、数据收集和保存
     */
    private void processAndSaveUserDataBatch(String companyId, boolean isFinished, ListWrap<ScorerDataMingrationDmSvc> dmSvcWrap,
                                       TenantId tenantId, CompanyConf conf, BigDecimal fullScoreValue) {
        long startTime = System.currentTimeMillis();
        log.info("Starting optimized batch data save for company: {}, dmSvcCount: {}, isFinished: {}",
                companyId, dmSvcWrap.getDatas().size(), isFinished);
        
        int companySuccessUsers = 0;
        int companyFailedUsers = 0;
        List<String> failedUserIds = new ArrayList<>();
        
        // 预收集所有评分人信息，避免重复遍历
        ListWrap<EmpEvalScorer> empEvalScorers = new ListWrap<>();
        List<String> scorerIds = new java.util.ArrayList<>();
        List<ScorerDataMingrationDmSvc> validDmSvcs = new ArrayList<>();
        
        try {
            // 第一次遍历：预处理（每个用户独立处理）
            for (ScorerDataMingrationDmSvc dmSvc : dmSvcWrap.getDatas()) {
                String taskUserId = dmSvc.getTaskUser().getId();
                
                try {
                    // 1. 设置配置
                    dmSvc.getEvalMerge().setCompanyConf(conf);
                    if (dmSvc.getEvalMerge().getScoreValueConf() != null) {
                        dmSvc.getEvalMerge().getScoreValueConf().setCustomFullScore(fullScoreValue);
                    }
                    
                    // 2. 处理任务完成
                    if (isFinished) {
                        dmSvc.handlerTaskFinished();
                    } else {
                        dmSvc.handlerTaskNoFinished();
                    }
                    
                    // 3. 收集评分人信息
                    if (CollUtil.isNotEmpty(dmSvc.getEmpEvalScorers())) {
                        empEvalScorers.addAll(dmSvc.getEmpEvalScorers());
                        dmSvc.getEmpEvalScorers().forEach(scorer -> scorerIds.add(scorer.getScorerId()));
                    }
                    
                    validDmSvcs.add(dmSvc);
                    
                } catch (Exception userEx) {
                    // 预处理失败：记录失败信息，不标记为已处理
                    companyFailedUsers++;
                    failedUserIds.add(taskUserId);
                    OptimizedBitmapProgress p = currentProgress();
                    if (p != null) {
                        p.recordFailure(taskUserId, companyId, p.getProcessedCount().get(), userEx.getMessage(), "PREPROCESS_FAILED");
                    }
                    log.error("User preprocessing failed: companyId={}, isFinished: {}, taskUserId={}", 
                            companyId, isFinished, taskUserId, userEx);
                }
            }

            // 一次性获取所有评分人信息
            Map<String, KpiEmp> kpiMap = new HashMap<>();
            if (CollUtil.isNotEmpty(scorerIds)) {
                kpiEmpDao.listByEmpAsMap(tenantId, scorerIds);
            }
            // 第二次遍历：只处理预处理成功的用户
            for (ScorerDataMingrationDmSvc dmSvc : validDmSvcs) {
                String taskUserId = dmSvc.getTaskUser().getId();
                
                try {
                    if (CollUtil.isEmpty(dmSvc.getEmpEvalScorers()) && dmSvc.getTaskUser().finalAutoScoreIsNull()) {
                        log.info("No scorer info for company: {}, isFinished: {}, taskUserId: {}", 
                                companyId, isFinished, taskUserId);
                        continue;
                    }
                    
                    // 应用评分人信息
                    dmSvc.accScorerInfo(kpiMap);
                    if (CollUtil.isNotEmpty(dmSvc.getEmpEvalScorers())) {
                        dmSvc.getEmpEvalScorers().sort(Comparator.comparing(EmpEvalScorer::getScorerId, Comparator.nullsLast(String::compareTo)));
                    }

                    // 保存用户数据
                    boolean userSaved = saveSingleUserData(companyId, taskUserId, dmSvc, tenantId);
                    
                    if (userSaved) {
                        companySuccessUsers++;
                        log.info("User processed ok: companyId={}, isFinished: {}, taskUserId={}, scorers={}",
                                companyId, isFinished, taskUserId, dmSvc.getEmpEvalScorers().size());
                    } else {
                        companyFailedUsers++;
                        failedUserIds.add(taskUserId);
                    }
                    
                } catch (Exception userEx) {
                    // 保存异常：记录失败信息，不标记为已处理
                    companyFailedUsers++;
                    failedUserIds.add(taskUserId);
                    OptimizedBitmapProgress p = currentProgress();
                    if (p != null) {
                        p.recordFailure(taskUserId, companyId, p.getProcessedCount().get(), userEx.getMessage(), "SAVE_EXCEPTION");
                    }
                    log.error("User save exception: companyId={}, isFinished: {}, taskUserId={}", 
                            companyId, isFinished, taskUserId, userEx);
                }
            }
            
            // 记录统计信息
            log.info("Optimized batch data save completed: companyId={}, isFinished: {}, success={}, failed={}, durationMs={}, failedUserIds: {}",
                    companyId, isFinished, companySuccessUsers, companyFailedUsers, (System.currentTimeMillis() - startTime), failedUserIds);
            
        } catch (Exception e) {
            log.error("System error in optimized batch data save: companyId={}, isFinished: {}, dmSvcCount={}",
                    companyId, isFinished, dmSvcWrap.getDatas().size(), e);
            throw e;
        }
    }

    /**
     * 使用ScorerDataMingrationDmSvc处理已完成用户（批量/单条皆可）
     */
    private void processFinishedUserWithDmSvcBatch(String companyId, List<String> taskUserIds) {
        try {
            TenantId tenantId = new TenantId(companyId);

            // 分片降级函数
            java.util.function.Function<List<String>, ListWrap<ScorerDataMingrationDmSvc>> fetch = ids -> onScoreEvalRepo.listOnScoreEvalMingration(tenantId, ids);
            java.util.function.BiFunction<TenantId, String, ScorerDataMingrationDmSvc> fetchOne = (tid, uid) -> onScoreEvalRepo.getOnScoreEvalMingration(tid, uid);

            ListWrap<ScorerDataMingrationDmSvc> dmSvcWrap;
            try {
                dmSvcWrap = fetch.apply(taskUserIds);
            } catch (Throwable batchEx) {
                log.warn("Batch fetch failed, degrade to chunks (finished): sessionId={}, type={}, companyId={}, size={}, errClass={}, errMsg={}, root={}", 
                        currentSessionId(), currentTaskTypeName(), companyId, taskUserIds.size(), 
                        batchEx.getClass().getName(), String.valueOf(batchEx.getMessage()), 
                        ExceptionUtils.getRootCauseMessage(batchEx), batchEx);
                dmSvcWrap = new ListWrap<>();
                degradeFetch(companyId, tenantId, taskUserIds, fetch, fetchOne, dmSvcWrap);
            }

            if (dmSvcWrap.isEmpty()) {
                log.info("dmSvc is null: sessionId={}, type={}, companyId={}, taskUserIds={}", 
                        currentSessionId(), currentTaskTypeName(), companyId, taskUserIds);
                return;
            }
            
            // 移除不需要修改的数据
            dmSvcWrap.getDatas().removeIf(ScorerDataMingrationDmSvc::isNoNeedUp);
            if (dmSvcWrap.isEmpty()) {
                log.info("移除不需要修改的 dmSvc is null: sessionId={}, type={}, companyId={}, taskUserIds={}", 
                        currentSessionId(), currentTaskTypeName(), companyId, taskUserIds);
                return;
            }
            
            // 一次性获取公司配置
            BigDecimal fullScoreValue = companyDao.findFullScoreValue(tenantId);
            CompanyConf conf = companyDao.findCompanyConf(tenantId);
            
            // 合并循环：一次性完成所有处理
            processAndSaveUserDataBatch(companyId,true, dmSvcWrap, tenantId, conf, fullScoreValue);
            
        } catch (Exception e) {
            log.error("Error processing finished user with DmSvc: sessionId={}, type={}, companyId={}", 
                    currentSessionId(), currentTaskTypeName(), companyId, e);
            throw e;
        }
    }

    /**
     * 使用ScorerDataMingrationDmSvc处理未完成用户（批量/单条皆可）
     */
    private void processNoFinishedUserWithDmSvcBatch(String companyId, List<String> taskUserIds) {
        try {
            TenantId tenantId = new TenantId(companyId);

            // 分片降级函数：批量 -> 二分 -> 单条 fallback
            java.util.function.Function<List<String>, ListWrap<ScorerDataMingrationDmSvc>> fetch = ids -> onScoreEvalRepo.listOnScoreEvalMingration(tenantId, ids);
            java.util.function.BiFunction<TenantId, String, ScorerDataMingrationDmSvc> fetchOne = (tid, uid) -> onScoreEvalRepo.getOnScoreEvalMingration(tid, uid);

            ListWrap<ScorerDataMingrationDmSvc> dmSvcWrap;
            try {
                dmSvcWrap = fetch.apply(taskUserIds);
            } catch (Throwable batchEx) {
                log.warn("Batch fetch failed, degrade to chunks: sessionId={}, type={}, companyId={}, size={}, errClass={}, errMsg={}, root={}", currentSessionId(), currentTaskTypeName(), companyId, taskUserIds.size(), batchEx.getClass().getName(), String.valueOf(batchEx.getMessage()), ExceptionUtils.getRootCauseMessage(batchEx), batchEx);
                dmSvcWrap = new ListWrap<>();
                degradeFetch(companyId, tenantId, taskUserIds, fetch, fetchOne, dmSvcWrap);
            }

            if (dmSvcWrap.isEmpty()) {
                log.info(" processing no finished  dmSvc is null: sessionId={}, type={}, companyId={}, taskUserIds={}", currentSessionId(), currentTaskTypeName(), companyId, taskUserIds);
                return;
            }
            dmSvcWrap.getDatas().removeIf(ScorerDataMingrationDmSvc::isNoNeedUp);
            if (dmSvcWrap.isEmpty()) {
                log.info(" processing no finished 移除不需要修改的 dmSvc is null: sessionId={}, type={}, companyId={}, taskUserIds={}", currentSessionId(), currentTaskTypeName(), companyId, taskUserIds);
                return;
            }
            // 一次性获取公司配置
            BigDecimal fullScoreValue = companyDao.findFullScoreValue(tenantId);
            CompanyConf conf = companyDao.findCompanyConf(tenantId);

            // 合并循环：一次性完成所有处理
            processAndSaveUserDataBatch(companyId,false, dmSvcWrap, tenantId, conf, fullScoreValue);
        } catch (Exception e) {
            log.error("Error processing no finished user with DmSvc: sessionId={}, type={}, companyId={}", currentSessionId(), currentTaskTypeName(), companyId, e);
            throw e;
        }
    }

    // 二分降级：批量失败 -> 拆半重试 -> 单条 fallback
    private void degradeFetch(String companyId, TenantId tenantId, List<String> ids,
                              java.util.function.Function<List<String>, ListWrap<ScorerDataMingrationDmSvc>> fetch,
                              java.util.function.BiFunction<TenantId, String, ScorerDataMingrationDmSvc> fetchOne,
                              ListWrap<ScorerDataMingrationDmSvc> sink) {
        if (ids == null || ids.isEmpty()) return;
        if (ids.size() == 1) {
            String uid = ids.get(0);
            try {
                ScorerDataMingrationDmSvc one = fetchOne.apply(tenantId, uid);
                if (one != null) sink.add(one);
            } catch (Throwable ex) {
                log.warn("Single fetch failed, skip user: sessionId={}, type={}, companyId={}, userId={}, errClass={}, errMsg={}, root={}", currentSessionId(), currentTaskTypeName(), companyId, uid, ex.getClass().getName(), String.valueOf(ex.getMessage()), ExceptionUtils.getRootCauseMessage(ex), ex);
            }
            return;
        }
        int mid = ids.size() / 2;
        List<String> left = ids.subList(0, mid);
        List<String> right = ids.subList(mid, ids.size());
        try {
            ListWrap<ScorerDataMingrationDmSvc> lw = fetch.apply(left);
            if (lw != null && !lw.isEmpty()) sink.addAll(lw.getDatas());
        } catch (Throwable ex) {
            degradeFetch(companyId, tenantId, new java.util.ArrayList<>(left), fetch, fetchOne, sink);
        }
        try {
            ListWrap<ScorerDataMingrationDmSvc> rw = fetch.apply(right);
            if (rw != null && !rw.isEmpty()) sink.addAll(rw.getDatas());
        } catch (Throwable ex) {
            degradeFetch(companyId, tenantId, new java.util.ArrayList<>(right), fetch, fetchOne, sink);
        }
    }

    /**
     * 按公司分页处理用户迁移
     */
    private long[] processCompaniesWithPagination(boolean finished,
                                                  BiFunction<String, List<String>, Boolean> processor,
                                                  MigrationTask task) {
        int companyPageNo = 1;
        int companyPageSize = COMPANY_PAGE_SIZE;
        boolean hasMoreCompanies = true;
        long totalProcessed = 0;
        long totalSuccess = 0;
        long totalFailure = 0;
        int processedCompanies = 0;
        long roundStart = System.currentTimeMillis();

        Integer shardIndex = task.getShardIndex();
        Integer shardTotal = task.getShardTotal();
        int openMigration21 = task.getOpenMigration21();
        String shardInfo = (shardIndex != null && shardTotal != null)
                ? String.format("[Shard %d/%d]", shardIndex, shardTotal)
                : "";

        log.info("{}Starting company pagination processing: finished={}, companyPageSize={},openMigration21:{}",
                shardInfo, finished, companyPageSize,openMigration21);


        // 断点续跑：优先从持久化元数据恢复
        try {
            String sid = MDC.get("migrationSessionId");
            OptimizedBitmapProgress persisted = OptimizedBitmapProgress.loadMetadata(sid);
            if (persisted != null) {
                progressMap.put(sid, persisted);
                if (persisted.getCurrentCompanyPage() > 1) {
                    companyPageNo = persisted.getCurrentCompanyPage();
                    log.info("Resumed from metadata: companyPageNo={}, sessionId={}", companyPageNo, sid);
                }
            }
        } catch (Throwable ignore) {}

        // 断点续跑：从内存进度恢复
        OptimizedBitmapProgress prog0 = currentProgress();
        if (prog0 != null && prog0.getCurrentCompanyPage() > 1) {
            companyPageNo = prog0.getCurrentCompanyPage();
            log.info("Resuming company pagination from pageNo={}, sessionId={}", companyPageNo, MDC.get("migrationSessionId"));
        }

        while (hasMoreCompanies) {
            long pageStart = System.currentTimeMillis();
            if (shouldCheckPause() && (isStoppedOptimized() || isPausedOptimized())) {
                log.info("[PAUSE/STOP] Before fetch companies: sessionId={}, pageNo={}", MDC.get("migrationSessionId"), companyPageNo);
                saveProgressOptimized();
                return new long[]{processedCompanies, System.currentTimeMillis() - roundStart};
            }
            if (isPausedOptimized()) {
                log.info("[PAUSE] Before fetch companies: sessionId={}, pageNo={}", MDC.get("migrationSessionId"), companyPageNo);
                saveProgressOptimized();
                return new long[]{processedCompanies, System.currentTimeMillis() - roundStart};
            }
            // 获取当前页的公司列表（传递分片参数）
            List<String> companyIds = getCompanyIdsByPage(companyPageNo, companyPageSize,
                    shardIndex, shardTotal,openMigration21);
            long pageCost = System.currentTimeMillis() - pageStart;
            log.info("{}Company page fetched: pageNo={}, size={}, costMs={}",
                    shardInfo, companyPageNo, companyIds.size(), pageCost);

            if (companyIds.isEmpty()) {
                hasMoreCompanies = false;
                log.info("No more companies found, pagination completed at company page {}", companyPageNo);
                saveProgressOptimized();
                break;
            }

            // 保存公司页进度
            OptimizedBitmapProgress p = currentProgress();
            if (p != null) {
                p.setCurrentCompanyPage(companyPageNo);
                log.info("Progress updated: currentCompanyPage={}", companyPageNo);
            }

            log.info("Processing company page {} with {} companies", companyPageNo, companyIds.size());

            // 处理每个公司的用户
            for (String companyId : companyIds) {
                if (shouldCheckPause() && (isStoppedOptimized() || isPausedOptimized())) {
                    log.warn("[STOP] Before process company: sessionId={}, companyId={}", MDC.get("migrationSessionId"), companyId);
                    saveProgressOptimized();
                    return new long[]{processedCompanies, System.currentTimeMillis() - roundStart};
                }
                if (isPausedOptimized()) {
                    log.info("[PAUSE] Before process company: sessionId={}, companyId={}", MDC.get("migrationSessionId"), companyId);
                    saveProgressOptimized();
                    return new long[]{processedCompanies, System.currentTimeMillis() - roundStart};
                }
                // 保存当前公司ID与将从第1页用户开始（或按用户断点恢复）
                if (p != null) {
                    p.setCurrentCompanyId(companyId);
                    p.setCurrentUserPage(1);
                    log.info("Progress updated: currentCompanyId={}, reset currentUserPage=1", companyId);
                }

                long cstStart = System.currentTimeMillis();
                long before = totalProcessed;
                try {
                    log.info("Processing company: {}, finished: {}", companyId, finished);

                    // 按用户分页处理当前公司
                    long companyProcessed = processUsersForCompany(companyId, finished, processor);
                    totalProcessed += companyProcessed;
                    processedCompanies++;
                    int delta = (int) (totalProcessed - before);
                    long cst = System.currentTimeMillis() - cstStart;
                    log.info("Company done: companyId={}, processedUsers={}, costMs={}", companyId, delta, cst);

                    // 更新任务进度
                    if (task != null) {
                        task.getProcessedRecords().set(totalProcessed);
                        log.info("Task progress set: processedRecords={}", totalProcessed);
                    }

                    // 添加公司间延迟（可被中断）
                    if (shouldCheckPause() && (isStoppedOptimized() || isPausedOptimized())) {
                        log.info("[PAUSE/STOP] After company: sessionId={}, companyId={}", MDC.get("migrationSessionId"), companyId);
                        saveProgressOptimized();
                        return new long[]{processedCompanies, System.currentTimeMillis() - roundStart};
                    }
                    Thread.sleep(COMPANY_DELAY_MS);

                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.warn("Migration interrupted during company delay");
                    saveProgressOptimized();
                    return new long[]{processedCompanies, System.currentTimeMillis() - roundStart};
                } catch (Exception e) {
                    log.error("Failed to process company: {}", companyId, e);
                    totalFailure++;
                    // 继续处理下一个公司
                }
            }

            // 如果返回的公司数少于页面大小，说明已经是最后一页
            if (companyIds.size() < companyPageSize) {
                hasMoreCompanies = false;
            } else {
                companyPageNo++;
            }

            // 添加页面间延迟
            try {
                if (shouldCheckPause() && (isStoppedOptimized() || isPausedOptimized())) {
                    log.info("[PAUSE/STOP] After company page: sessionId={}, pageNo={}", MDC.get("migrationSessionId"), companyPageNo);
                    saveProgressOptimized();
                    return new long[]{processedCompanies, System.currentTimeMillis() - roundStart};
                }
                Thread.sleep(COMPANY_PAGINATION_DELAY_MS);
                saveProgressOptimized();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("Migration interrupted during company pagination delay");
                saveProgressOptimized();
                break;
            }
        }

        // 设置最终的总记录数
        if (task != null) {
            task.setTotalRecords(totalProcessed);
            log.info("Task totalRecords set: {}", totalProcessed);
        }
        saveProgressOptimized();

        log.info("Completed company pagination processing: finished={}, totalCompanies={}, " +
                        "totalProcessed={}, totalSuccess={}, totalFailure={}",
                finished, processedCompanies, totalProcessed, totalSuccess, totalFailure);
        return new long[]{processedCompanies, System.currentTimeMillis() - roundStart};
    }

    /**
     * 处理迁移失败
     */
    private void handleMigrationFailure(String sessionId, String errorMessage) {
        try {
            MigrationTask task = activeTasks.get(sessionId);
            if (task != null) {
                task.fail(errorMessage);
            }
        } catch (Exception e) {
            log.error("Failed to update migration failure status for session: {}", sessionId, e);
        }
    }

    @Async("migrationTaskExecutor")
    public void executeMigrationAsync(String tid, String sessionId) {
        MDC.put("tid", tid);
        MDC.put("migrationSessionId", sessionId);
        try {
            MigrationTask task = activeTasks.get(sessionId);
            if (task != null && task.getMigrationType() != null) {
                MDC.put("migrationType", task.getMigrationType().name());
            }
            log.info("Starting async migration execution for session: {}, type={}", sessionId, MDC.get("migrationType"));

            if (task == null) {
                log.error("Migration task not found for session: {}", sessionId);
                return;
            }

            // 委托给领域服务执行具体的迁移逻辑
            executeMigrationLogic(task);

            OptimizedBitmapProgress p = currentProgress();
            if (p != null) {
                java.util.Map<String,Object> m = p.getPerformanceMetrics();
                log.info("Migration progress: sessionId={}, processed={}, success={}, failure={}, speed={}/s, etaSec={}",
                        sessionId, m.get("processedCount"), m.get("successCount"), m.get("failureCount"), m.get("processingSpeed"), m.get("estimatedRemainingTimeSeconds"));
            }
            log.info("Migration completed successfully for session: {}", sessionId);

        } catch (Exception e) {
            log.error("Migration failed for session: {}", sessionId, e);
            handleMigrationFailure(sessionId, e.getMessage());
            OptimizedBitmapProgress p = currentProgress();
            if (p != null) {
                java.util.List<String> failed = p.getFailedUserIds();
                if (!failed.isEmpty()) {
                    log.warn("Top {} failed users: {}", Math.min(50, failed.size()), failed.stream().limit(50).collect(java.util.stream.Collectors.toList()));
                }
            }
        } finally {
            MDC.remove("migrationType");
            MDC.remove("migrationSessionId");
            MDC.remove("tid");
        }
    }

    /**
     * 执行迁移逻辑（业务编排）
     */
    private void executeMigrationLogic(MigrationTask task) {
        String sessionId = task.getTaskId();
        String migrationType = task.getMigrationType().name();
        String companyId = task.getCompanyId();

        log.info("Executing migration logic: taskId={}, type={}, companyId={}", sessionId, migrationType, companyId);

        try {
            MigrationExecutor executor = new MigrationExecutor(migrationConfig);

            if ("FINISHED".equals(migrationType)) {
                long[] result = processCompaniesWithPagination(true, (cid, userIds) ->
                        executor.executeFinishedUsersMigration(cid, userIds, this::processFinishedUserWithDmSvcBatch), task);
                log.info("Finished users migration completed. Processed companies: {}, duration: {}ms", result[0], result[1]);
            } else if ("NO_FINISHED".equals(migrationType)) {
                long[] result = processCompaniesWithPagination(false, (cid, userIds) ->
                        executor.executeNoFinishedUsersMigration(cid, userIds, this::processNoFinishedUserWithDmSvcBatch), task);
                log.info("No finished users migration completed. Processed companies: {}, duration: {}ms", result[0], result[1]);
            } else if ("MIXED".equals(migrationType)) {
                long[] result1 = processCompaniesWithPagination(true, (cid, userIds) ->
                        executor.executeFinishedUsersMigration(cid, userIds, this::processFinishedUserWithDmSvcBatch), task);
                log.info("Finished users migration completed. Processed companies: {}, duration: {}ms", result1[0], result1[1]);
                long[] result2 = processCompaniesWithPagination(false, (cid, userIds) ->
                        executor.executeNoFinishedUsersMigration(cid, userIds, this::processNoFinishedUserWithDmSvcBatch), task);
                log.info("No finished users migration completed. Processed companies: {}, duration: {}ms", result2[0], result2[1]);
                long companiesAll = result1[0] + result2[0];
                long durationAll = result1[1] + result2[1];
                log.info("Migration all rounds completed: sessionId={}, companiesFinished={}, companiesUnfinished={}, totalCompanies={}, durationFinishedMs={}, durationUnfinishedMs={}, totalDurationMs={}",
                        sessionId, result1[0], result2[0], companiesAll, result1[1], result2[1], durationAll);
            } else {
                log.error("Unknown migration type: {}", migrationType);
                task.fail("Unknown migration type: " + migrationType);
                return;
            }

            scoreMigrationDmSvc.completeMigrationTask(task);

        } catch (Exception e) {
            log.error("Migration execution failed: taskId={}", sessionId, e);
            task.fail(e.getMessage());
        }
    }

    /**
     * 优化后：大部分调用直接返回缓存
     */
    private OptimizedBitmapProgress currentProgress() {
        long now = System.currentTimeMillis();  // 系统调用：~100-1000纳秒
        if (cachedProgress != null && (now - lastProgressUpdate) < PROGRESS_CACHE_TTL) {
            return cachedProgress;  // 直接返回：~1纳秒
        }
        
        // 缓存失效时才执行原逻辑
        String sessionId = MDC.get("migrationSessionId");
        if (sessionId == null) return null;
        cachedProgress = progressMap.get(sessionId);
        lastProgressUpdate = now;
        return cachedProgress;
    }

    private void executeFinishedUsersMigration(MigrationTask task, MigrationExecutor executor) {
        String companyId = task.getCompanyId();

        // 失败优先合并
        java.util.Set<String> failed = new java.util.HashSet<>();
        OptimizedBitmapProgress p = currentProgress();
        if (p != null) {
            java.util.List<String> f = p.getFailedUserIds().stream()
                    .map(uid -> p.getFailureRecord(uid))
                    .filter(java.util.Objects::nonNull)
                    .filter(fr -> companyId.equals(fr.getCompanyId()))
                    .map(OptimizedBitmapProgress.FailureRecord::getUserId)
                    .collect(java.util.stream.Collectors.toList());
            failed.addAll(f);
        }

        // 候选用户（DAO 已完成“目标状态+版本”过滤）
        java.util.List<String> all = getUserIdsForMigration(companyId, true);
        task.setTotalRecords(all.size());

        java.util.LinkedHashSet<String> toProcessSet = new java.util.LinkedHashSet<>(all);
        toProcessSet.addAll(failed); // 失败用户强制重跑两步
        java.util.List<String> toProcess = new java.util.ArrayList<>(toProcessSet);

        log.info("FINISHED toProcess: companyId={}, allFiltered={}, failedAdd={}, final={}",
                companyId, all.size(), failed.size(), toProcess.size());
        if (toProcess.isEmpty()) return;

        boolean success = executor.executeFinishedUsersMigration(companyId, toProcess,
                this::processFinishedUserWithDmSvcBatch);
        if (!success) {
            throw new RuntimeException("Failed to execute finished users migration");
        }
    }

    private void executeNoFinishedUsersMigration(MigrationTask task, MigrationExecutor executor) {
        String companyId = task.getCompanyId();

        // 失败优先合并
        java.util.Set<String> failed = new java.util.HashSet<>();
        OptimizedBitmapProgress p = currentProgress();
        if (p != null) {
            java.util.List<String> f = p.getFailedUserIds().stream()
                    .map(uid -> p.getFailureRecord(uid))
                    .filter(java.util.Objects::nonNull)
                    .filter(fr -> companyId.equals(fr.getCompanyId()))
                    .map(OptimizedBitmapProgress.FailureRecord::getUserId)
                    .collect(java.util.stream.Collectors.toList());
            failed.addAll(f);
        }

        java.util.List<String> all = getUserIdsForMigration(companyId, false);
        task.setTotalRecords(all.size());

        java.util.LinkedHashSet<String> toProcessSet = new java.util.LinkedHashSet<>(all);
        toProcessSet.addAll(failed);
        java.util.List<String> toProcess = new java.util.ArrayList<>(toProcessSet);

        log.info("NO_FINISHED toProcess: companyId={}, allFiltered={}, failedAdd={}, final={}",
                companyId, all.size(), failed.size(), toProcess.size());
        if (toProcess.isEmpty()) return;

        boolean success = executor.executeNoFinishedUsersMigration(companyId, toProcess,
                this::processNoFinishedUserWithDmSvcBatch);
        if (!success) {
            throw new RuntimeException("Failed to execute no-finished users migration");
        }
    }

    private List<String> getCompanyIdsByPage(int page, int pageSize,
                                             Integer shardIndex, Integer shardTotal, int openMigration21) {
        try {
            List<String> companyIds = new ArrayList<>();
            PagedList<Company> companies;
            if (1 == openMigration21) {//开启迁移有效期是21年的
                companies = companyDao.pagedAllCompanyV2ForMigration21(
                        VERSION, page, pageSize, shardIndex, shardTotal);
            } else {
                companies = companyDao.pagedAllCompanyV2WithShard(
                        VERSION, page, pageSize, shardIndex, shardTotal);
            }
            for (Company company : companies) {
                companyIds.add(company.getId());
            }

            log.info("Retrieved company IDs (shard {}/{}): page={}, size={},openMigration21:{}",
                    shardIndex, shardTotal, page, companyIds.size(), openMigration21);
            return companyIds;
        } catch (Exception e) {
            log.error("Error getting company IDs", e);
            return new ArrayList<>();
        }
    }

    /**
     * 处理单个公司的用户迁移
     */
    private long processUsersForCompany(String companyId, boolean finished,
                                    BiFunction<String, List<String>, Boolean> processor) {
        int userPageNo = 1;
        int userPageSize = DEFAULT_PAGE_SIZE;
        boolean hasMoreUsers = true;
        long companyProcessed = 0;
        String lastId = null;

        
        try {
            log.info("Starting user processing for company: {}, finished: {}", companyId, finished);
            
            while (hasMoreUsers) {
                // 检查暂停/停止状态
                if (shouldCheckPause() && (isStoppedOptimized() || isPausedOptimized())) {
                    log.info("[PAUSE/STOP] During user processing: companyId={}, userPageNo={}", companyId, userPageNo);
                    saveProgressOptimized();
                    break;
                }
                
                // 获取当前页的用户ID列表
                List<String> userIds;
                // 自适应：浅页用 offset，深页/已定位用 keyset
                if (lastId == null && userPageNo <= 100) {
                    userIds = getUserIdsForMigration(companyId, finished, userPageNo, userPageSize);
                } else {
                    PagedList<String> page = taskUserDao.pagedFinishedTaskUserForMigrationByLastId(companyId, finished, lastId, userPageSize);
                    userIds = page.getData();
                    if (userIds == null) userIds = new ArrayList<>();
                }
                if (userIds.isEmpty()) {
                    log.info("No more users found for company: {}, userPageNo={}", companyId, userPageNo);
                    break;
                }
                
                log.info("Processing user page: companyId={}, userPageNo={}, userPageSize={}, actualSize={}", 
                        companyId, userPageNo, userPageSize, userIds.size());
                
                // 处理当前页的用户
                long handleStart = System.currentTimeMillis();
                boolean success = processor.apply(companyId, userIds);
                long handleCost = System.currentTimeMillis() - handleStart;
                
                if (success) {
                    companyProcessed += userIds.size();
                    log.info("User page processed successfully: companyId={}, userPageNo={}, size={}, costMs={}", 
                            companyId, userPageNo, userIds.size(), handleCost);
                } else {
                    log.warn("User page processing failed: companyId={}, userPageNo={}, size={}, costMs={}", 
                            companyId, userPageNo, userIds.size(), handleCost);
                }
                
                saveProgressOptimized();
                // 记录 lastId 以便 keyset 续页
                lastId = userIds.get(userIds.size() - 1);

                // 如果返回的记录数少于页面大小，说明已经是最后一页
                if (userIds.size() < userPageSize) {
                    hasMoreUsers = false;
                    log.info("Last user page reached for company: {}, userPageNo={}, actualSize={}", 
                            companyId, userPageNo, userIds.size());
                } else {
                    userPageNo++;
                }
                
                // 用户页面间延迟
                try {
                    Thread.sleep(PAGINATION_DELAY_MS);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("Migration interrupted during user pagination delay for company: {}", companyId);
                    saveProgressOptimized();
                    break;
                }
            }
            
            log.info("Completed user processing for company: {}, total processed: {}", companyId, companyProcessed);
            return companyProcessed;
            
        } catch (Exception e) {
            log.error("Error processing users for company: {}", companyId, e);
            return companyProcessed;
        }
    }
    /**
     * 获取迁移用户列表 - 支持分页查询
     */
    private List<String> getUserIdsForMigration(String companyId, boolean finished) {
        return getUserIdsForMigration(companyId, finished, 1, DEFAULT_PAGE_SIZE);
    }
    /**
     * 获取迁移用户列表 - 分页查询
     */
    private List<String> getUserIdsForMigration(String companyId, boolean finished, Integer pageNo, Integer pageSize) {
        try {
            long t0 = System.currentTimeMillis();
            log.info("Getting user IDs for migration: companyId={}, finished={}, pageNo={}, pageSize={}",
                    companyId, finished, pageNo, pageSize);

            PagedList<String> page = taskUserDao.pagedFinishedTaskUserForMigration(companyId, finished, pageNo, pageSize);
            List<String> userIds = page.getData();
            if (userIds == null) userIds = new ArrayList<>();

            long cost = System.currentTimeMillis() - t0;
            log.info("User IDs fetched: companyId={}, finished={}, pageNo={}, size={}, costMs={}",
                    companyId, finished, pageNo, userIds.size(), cost);
            return userIds;
        } catch (Exception e) {
            log.error("Error getting user IDs for migration: companyId={}, " +
                            "={}, pageNo={}",
                    companyId, finished, pageNo, e);
            return new ArrayList<>();
        }
    }
}
