create table active_app
(
    active_user_id bigint comment '引用外键:active_user.id',
    plat           int         default 10  null comment '平台wecom=10,钉钉=20',
    corp_id        varchar(50)             not null comment '外部企业id',
    ext_user_id    varchar(50)             not null comment '外部用户id',
    status         int                     null comment ' 0：close，1：open',
    app_code       varchar(20) default '0' null comment '应用编号 10001=okr,10002=kpi',
    gmt_create     bigint                  not null comment '创建时间',
    gmt_update     bigint                  not null comment '更新时间',
    version        int         default 0   null comment '版本号',
    constraint uk_app
        unique (corp_id, active_user_id, plat, app_code)
)
    comment '用户激活的应用明细' charset = utf8mb4;

create index idx_active_user_id
    on active_app (active_user_id);

alter table active_app
    modify active_user_id bigint auto_increment comment '引用外键:active_user.id';

create table active_corp
(
    plat       int default 10 null comment '平台wecom=10,钉钉=20',
    corp_id    varchar(50)    not null comment '公司ID',
    status     int default 0  null comment '0=试用,1=正式',
    lack_code  int default 0  null comment '公司是否缺码数量 1=是, 0=不缺',
    gmt_create bigint         not null comment '创建时间',
    gmt_update bigint         not null comment '更新时间',
    version    int default 0  null comment '版本号',
    constraint uk_corp
        unique (corp_id, plat)
)
    comment '授权激活公司' charset = utf8mb4;

create table active_user
(
    id             bigint auto_increment comment 'ID'
        primary key,
    plat           int         default 10  null comment '平台wecom=10,钉钉=20',
    corp_id        varchar(50)             not null comment '外部企业id',
    ext_user_id    varchar(50)             not null comment '外部用户id',
    actived_status int         default 0   null comment '状态 0=待激活, 10=激活中, 20=完成激活, 30=在职可转移 ,40=离职可转移',
    lack_code      int         default 0   null comment '缺码 1=是, 0=否',
    active_app_cnt int         default 0   null comment '激活应用数',
    tran_lock      int                     null comment '转移时抢占锁标记 1=已抢占,0=可抢占',
    active_time    bigint      default 0   null comment '激活时间',
    expire_time    bigint      default 0   null comment '过期时间',
    active_from    varchar(50) default '0' null comment '激活码时是:code值, 继承用户时是:userId',
    active_type    int         default 0   null comment '激活方式 1 = code, 2 = 继承',
    tran_time      bigint      default 0   null comment '最新转移时间',
    gmt_create     bigint                  not null comment '创建时间',
    gmt_update     bigint                  not null comment '更新时间',
    version        int         default 0   null comment '版本号',
    constraint uk_user_id
        unique (corp_id, ext_user_id, plat)
)
    comment '授权激活的用户' charset = utf8mb4;

create index idx_active_from
    on active_user (active_from);

create table admin_cycle_operation
(
    id              varchar(50)  not null comment '操作记录id'
        primary key,
    cycle_id        bigint       not null comment '周期id',
    company_id      varchar(50)  not null comment '公司id',
    admin_type      varchar(20)  not null comment '管理员角色 主管理员=main 子管理员=child',
    change_type     int          not null comment '操作类型',
    base_info       text         null comment '基本信息修改',
    audit_result    text         null comment '结果校准',
    confirm_result  text         null comment '结果确认',
    publish_result  text         null comment '结果公示',
    appeal_conf     text         null comment '结果申诉',
    score_conf      text         null comment '评分设置',
    score_view_conf text         null comment '评分人查看权限',
    admin_diff      text         null comment '管理员权限变更',
    operation_time  bigint       not null comment '操作时间毫秒ms',
    operator_id     varchar(50)  not null comment '操作人emp_id',
    operator_name   varchar(50)  not null comment '操作人名',
    operator_avatar varchar(200) not null comment '操作人头像',
    created_time    datetime     null comment '创建时间'
)
    comment '周期操作日志' charset = utf8mb4;

create index idx_company_id
    on admin_cycle_operation (company_id);

create index idx_cycle_id
    on admin_cycle_operation (cycle_id);

create table admin_manage_evaluate_task
(
    admin_emp_id varchar(50)        not null comment '管理员的员工id',
    company_id   varchar(50)        not null comment '公司id',
    task_id      varchar(50)        null comment '管理的任务id',
    is_owner     smallint default 0 null comment '是否owner 1=是, 0=不是',
    created_time datetime           null comment '创建时间',
    version      int      default 0 null comment '版本号',
    constraint uk_admin_org
        unique (admin_emp_id, task_id)
)
    comment '管理员管的任务,考核表新加' charset = utf8mb4;

create index idx_company_id
    on admin_manage_evaluate_task (company_id);

create table admin_manage_item_category
(
    category_id  varchar(50)   not null comment '类别ID',
    company_id   varchar(50)   null comment '公司id',
    admin_emp_id varchar(50)   null comment '管理员ID',
    is_deleted   varchar(10)   null comment '是否删除',
    created_user varchar(50)   null comment '创建用户',
    created_time datetime      null comment '创建时间',
    updated_user varchar(50)   null comment '修改用户',
    updated_time datetime      null comment '修改时间',
    version      int default 0 null
)
    comment '管理员管理的指标类别' charset = utf8mb4;

create index category_key
    on admin_manage_item_category (category_id);

create index company_key
    on admin_manage_item_category (company_id);

create index ref_key
    on admin_manage_item_category (admin_emp_id);

create table admin_manage_org
(
    company_id   varchar(50)            not null comment '企业id',
    org_id       varchar(50) default '' null comment '部门id',
    admin_emp_id varchar(50) default '' null comment '管理员id',
    created_user varchar(50) default '' null,
    updated_user varchar(50) default '' null,
    created_time datetime               null,
    updated_time datetime               null,
    version      int         default 0  null comment '版本号',
    constraint uk_admin_org
        unique (admin_emp_id, org_id)
)
    comment '管理员管理的部门表' charset = utf8mb4;

create index idx_company_id
    on admin_manage_org (company_id);

create index idx_org_id
    on admin_manage_org (org_id);

create table admin_of_cycle
(
    cycle_id     bigint                     not null comment '周期id',
    company_id   varchar(50)                not null comment '公司id',
    emp_id       varchar(50)                not null comment '员工id',
    name         varchar(50)                not null comment '员工名称',
    main         int        default 1       null comment '是否是周期主管',
    priv         int        default 0       null comment '周期权限 无=0，管理=1，查看=2',
    is_deleted   varchar(5) default 'false' null comment '是否删除',
    created_user varchar(50)                null,
    updated_user varchar(50)                null,
    created_time datetime                   null,
    updated_time datetime                   null,
    version      int        default 0       null comment '版本号'
)
    comment '周期权限配置' charset = utf8mb4;

create index k_company_id
    on admin_of_cycle (company_id);

create index k_cycle_id
    on admin_of_cycle (cycle_id);

create index k_emp_id
    on admin_of_cycle (emp_id);

create table admin_of_task
(
    company_id   varchar(50)            not null comment '企业id',
    task_id      varchar(50)            null comment '管理的任务id',
    admin_emp_id varchar(50)            not null comment '管理员工id',
    main         int         default 1  null comment '是否是任务主管',
    priv         int         default 1  null comment '管理员的权限,二进位表示00=无,01=读,10=写,11=读写',
    created_user varchar(50) default '' null,
    created_time datetime               null,
    version      int         default 0  null comment '版本号',
    constraint uk_admin_task
        unique (admin_emp_id, task_id)
)
    comment '管理任务包含的管理员' charset = utf8mb4;

create index idx_admin_emp_id
    on admin_of_task (admin_emp_id);

create index idx_company_id
    on admin_of_task (company_id);

create index idx_task_id
    on admin_of_task (task_id);

create table admin_scope_priv_group
(
    id           varchar(50)                     not null comment '主键'
        primary key,
    company_id   varchar(50)                     not null comment '企业id',
    name         varchar(255)                    not null comment '权限组名称',
    admin_emp_id varchar(50)                     null comment '管理员工id',
    is_deleted   varchar(10) collate utf8mb4_bin null,
    created_user varchar(50) default ''          null,
    created_time datetime                        null,
    updated_user varchar(50) collate utf8mb4_bin null,
    updated_time datetime                        null,
    version      int         default 0           null comment '版本号'
)
    comment '管理范围权限组' charset = utf8mb4;

create index idx_company_id
    on admin_scope_priv_group (company_id);

create table admin_scope_priv_group_of_emp
(
    id           varchar(50)                     not null comment '主键'
        primary key,
    company_id   varchar(50)                     not null comment '企业id',
    group_id     varchar(50)                     not null comment '权限组id',
    admin_emp_id varchar(50)                     not null comment '管理员工id',
    is_deleted   varchar(10) collate utf8mb4_bin null,
    created_user varchar(50) default ''          null,
    created_time datetime                        null,
    updated_user varchar(50) collate utf8mb4_bin null,
    updated_time datetime                        null,
    version      int         default 0           null comment '版本号'
)
    comment '管理范围权限组关联的管理员' charset = utf8mb4;

create index idx_company_id
    on admin_scope_priv_group_of_emp (company_id);

create index idx_emp_id
    on admin_scope_priv_group_of_emp (admin_emp_id);

create index idx_group_id
    on admin_scope_priv_group_of_emp (group_id);

create table admin_scope_priv_group_org
(
    id           varchar(50)                     not null comment '主键'
        primary key,
    company_id   varchar(50)                     not null comment '企业id',
    group_id     varchar(50)                     not null comment '权限组id',
    org_id       varchar(50)                     not null comment '部门ID',
    status       varchar(50)                     null comment '部门状态:有效；无效',
    is_deleted   varchar(10) collate utf8mb4_bin null,
    created_user varchar(50) default ''          null,
    created_time datetime                        null,
    updated_user varchar(50) collate utf8mb4_bin null,
    updated_time datetime                        null,
    version      int         default 0           null comment '版本号'
)
    comment '管理范围权限组部门' charset = utf8mb4;

create index idx_company_id
    on admin_scope_priv_group_org (company_id);

create index idx_group_id
    on admin_scope_priv_group_org (group_id);

create index idx_org_id
    on admin_scope_priv_group_org (org_id);

create table admin_task_copy_record
(
    id              varchar(50)              not null comment 'id'
        primary key,
    company_id      varchar(50)              null comment '公司id',
    task_id         varchar(50)              null comment '考核任务id',
    finish_cnt      int(10) default 0        null comment '完成数',
    quit_cnt        int(10) default 0        null comment '员工离职数',
    disable_cnt     int(10) default 0        null comment '员工禁用数',
    no_scope_cnt    int(10) default 0        null comment '管理范围外数量',
    change_dept_cnt int(10) default 0        null comment '变更部门异常数',
    copy_conf       text collate utf8mb4_bin null comment '考核任务复制配置',
    is_read         int(1)  default 0        null comment '是否已检查（0：未检查 1：已检查）',
    is_deleted      varchar(10)              null comment '是否删除',
    created_user    varchar(50)              null,
    created_time    datetime                 null comment '创建时间',
    updated_user    varchar(50)              null,
    updated_time    datetime                 null comment '更新时间',
    version         int     default 0        null comment '版本字段'
)
    comment '管理任务复制记录' charset = utf8mb4;

create index company_id
    on admin_task_copy_record (company_id);

create index idx_task
    on admin_task_copy_record (task_id);

create table admin_task_copy_record_details
(
    copy_record_id varchar(50)               null comment 'admin_task_copy_record.id',
    company_id     varchar(50)               null comment '公司id',
    type           int(10)                   null comment '记录详情类型(1：正常 2：管理范围外 3：员工离职 4:员工禁用 5：部门变更 )',
    emp_id         varchar(50)               null comment '员工ID',
    emp_name       varchar(50)               null comment '员工姓名',
    avatar         varchar(256) charset utf8 null comment '头像链接地址',
    org_id         varchar(50)               null comment '部门ID',
    org_name       varchar(50)               null comment '部门名称',
    eval_type      int(10)                   null comment '考核类型(1：个人考核  2：组织考核)',
    is_deleted     varchar(10)               null comment '是否删除',
    created_user   varchar(50)               null,
    created_time   datetime                  null comment '创建时间',
    updated_user   varchar(50)               null,
    updated_time   datetime                  null comment '更新时间',
    version        int default 0             null comment '版本字段'
)
    comment '管理任务复制记录详情' charset = utf8mb4;

create index company_id
    on admin_task_copy_record_details (company_id);

create index idx_task
    on admin_task_copy_record_details (copy_record_id);

create table admin_task_operation
(
    id                      varchar(50)  not null comment '操作记录id'
        primary key,
    task_id                 varchar(50)  not null comment '外键task_base.id, 管理任务id',
    company_id              varchar(50)  not null comment '公司id',
    admin_type              varchar(20)  not null comment '管理员角色 main child',
    change_type             int          not null comment '操作类型:修改基本信息=01=1,修改考核规则=10=2,修改考核任务管理权限=100=4,增加考核人员=1000=8,删除考核人员=10000=16,终止考核任务=100000=32',
    base_info               text         null comment '基本信息修改',
    priv_info               text         null comment '管理员的权限变更内容',
    confirm_task            text         null comment '确认任务 对应模板 templAffirmJson:PerfTemplEvaluateAffirm',
    edit_exe_indi           text         null comment '执行中修改指标日志记录  对应模板 templExecuteJson:PerfTemplEvaluateExecute',
    score_conf              text         null comment 'enter_score,s3_self_rater,s3_mutual_rater,s3_super_rater ,score_veiw',
    score_sort_conf         text         null comment '2.0.0新版:新加字段 评分环节顺序配置',
    comment_conf            text         null comment '评语与总结配置',
    score_view              varchar(512) null comment '可见范围配置 json',
    audit_result            text         null comment '结果校准,从templEvaluateJson:PerfTemplEvaluate 模板评价配置json 单独出来的',
    confirm_result          text         null comment '结果确认,多个字段合并',
    publish_result          text         null comment '结果公示,多个字段合并',
    appeal_conf             varchar(512) null comment '结果申诉,多个字段合并',
    add_emps                text         null comment '增加的员工',
    remove_emps             text         null comment '删除的员工',
    operation_time          bigint       not null comment '操作时间毫秒ms',
    operator_id             varchar(50)  not null comment '操作人emp_id',
    operator_name           varchar(50)  not null comment '操作人名',
    operator_avatar         text         null comment '操作人头像',
    created_time            datetime     null comment '记录创建时间',
    finish_value_audit_conf text         null comment '完成值审核人',
    dead_line_conf          text         null comment '阶段截止时间配置'
)
    comment '管理任务的操作日志' charset = utf8mb4;

create index idx_company_id
    on admin_task_operation (company_id);

create index idx_operation_time
    on admin_task_operation (operation_time);

create index idx_task_id
    on admin_task_operation (task_id);

create table announcement
(
    id           varchar(50) not null
        primary key,
    content      text        null,
    created_time varchar(50) not null
);

create table app_version
(
    ver         bigint        not null comment '1.9.1= 1*1000*1000 + 9*1000 + 1=1009001，用于计算的版本号，也是主键'
        primary key,
    label       varchar(174)  null comment '1.9.1 用于显示版本号',
    `desc`      varchar(1000) null comment '版本描述',
    method      varchar(1000) null comment '升级当前版本需要执行的方法',
    pre_version bigint        null comment '上一个版本,仅做参考，现在没有用',
    open        int           null comment '是否开放给用户升级可见'
)
    comment '应用的版本信息' charset = utf8mb4;

create table app_version_script
(
    id          bigint        not null comment 'id'
        primary key,
    app_version bigint        not null comment '数字型版本号',
    `order`     int default 0 null comment '排序字段，非必填',
    script      text          null comment '执行脚本',
    `desc`      varchar(1000) null comment '脚本描述',
    script_type int default 0 null comment '默认0=升级脚本；1=版本全量迁移脚本'
)
    comment '应用升级对应的版本' charset = utf8mb4;

create index idx_app_version
    on app_version_script (app_version);

create table company
(
    id                          varchar(40)  default '0'                               not null comment '自增id'
        primary key,
    code                        varchar(10)                                            null comment '公司code',
    name                        varchar(512) charset utf8mb4                           null comment '公司名称',
    abbr_name                   varchar(20)                                            null comment '公司简称',
    business                    varchar(127)                                           null comment '公司业务方向',
    legal_person                varchar(45)                                            null comment '公司负责人',
    legal_person_mobile         varchar(200)                                           null comment '公司联系人电话',
    legal_person_email          varchar(45)                                            null comment '公司联系人邮箱',
    business_license            varchar(256)                                           null comment '工商执照',
    credit_code                 varchar(50)                                            null comment '社会统一信用代码',
    logo                        varchar(256)                                           null comment '公司LOGO',
    address                     varchar(512)                                           null comment '公司地址',
    scale                       varchar(32)                                            null comment '企业规模',
    city                        varchar(255)                                           null comment '公司所在城市',
    license                     varchar(50)                                            null comment '开户许可证',
    license_img                 varchar(255)                                           null comment '开户许可证',
    head_name                   varchar(50)                                            null comment '法人名称',
    head_id_num                 varchar(50)                                            null comment '法人证件号码',
    head_id_type                varchar(50)                                            null comment '法人证件类型',
    head_img1                   varchar(255)                                           null comment '法人证件正面',
    head_img2                   varchar(255)                                           null comment '法人证件反面',
    agent_img1                  varchar(255)                                           null comment '经办人身份证正面',
    agent_img2                  varchar(255)                                           null comment '经办人身份证反面',
    approval_status             varchar(10)                                            null comment '审核状态（review:审核中，pass:通过，unpass:不通过）',
    approval_memo               varchar(200)                                           null comment '审核备注',
    app_perm                    text                                                   null comment 'app权限json格式',
    open_push                   varchar(10)                                            null comment '开启推送（true|false）',
    sync_type                   varchar(10)                                            null comment '同步类型（auto:自动,manu:手动）',
    ding_corp_id                varchar(200)                                           null comment '钉钉企业id',
    ding_agent_id               varchar(200)                                           null comment '钉钉授权方应用id',
    ding_space_id               varchar(20)                                            null comment '企业钉盘id',
    we_corp_id                  varchar(200)                                           null comment 'welink企业id',
    status                      varchar(10)  default 'using'                           null comment '状态using：正常使用中，close：删除',
    company_type                varchar(10)                                            null comment '公司类型（review:待审核-默认不需要审核,simple-极简版，enterprise-企业版）',
    emp_limit                   int                                                    null comment '人数上限',
    perm_start_time             date                                                   null comment '权限开始日期',
    perm_end_time               date                                                   null comment '权限结束日期',
    target_num                  int                                                    null comment '目标数量',
    action_num                  int                                                    null comment '成果数量',
    created_time                datetime                                               null comment '记录创建时间',
    updated_time                datetime                                               null comment '记录更新时间',
    indicator_num               int                                                    null comment '指标数量',
    enter_on_scoring            int          default 0                                 null comment '评分阶段可以录入完成值 0=关, 1=打开',
    result_input_send_msg       varchar(10)  default 'true'                            null comment '是否发送录入完成值代办',
    sup_is_can_auto_score_item  int          default 0                                 null comment '上级是否能看自动评分指标明细',
    can_res_submit_input_finish int          default 0                                 null comment '自动评分指标能多次录入完成值',
    version                     int          default 0                                 null comment '版本号',
    order_from                  int          default 0                                 null comment '订单来源 0=默认, 1 = hrm,2 =mark_place, 3=',
    eval_emp_on_log_auth        int(1)       default 1                                 null comment '被考核人打开日志权限',
    enter_on_ding_msg           int(1)       default 1                                 null comment '钉钉代办开关',
    log_auth_set                varchar(100) default 'taskEmp,participant,createdUser' null comment '日志权限设置，taskEmp=被考核人,participant=参与人,createdUser=创建人',
    ignore_vacancy_manager      int          default 0                                 null comment '忽略空缺主管',
    emp_entry_auto_open         int(1)       default 0                                 null comment '员工自动开启开关 默认0=关闭，1=开启',
    app_version                 varchar(174) default '1009002'                         null comment '当前企业的应用版本',
    last_version                varchar(174) default '1009002'                         null comment '企业可以升级的版本',
    open_org_performance        int          default 0                                 null comment '组织绩效开关 默认=0关闭、1=开启',
    open_ref_performance        int          default 0                                 null comment '关联绩效开关 默认=0关闭、1=开启',
    constraint idx_c_ding_corp_id
        unique (ding_corp_id)
)
    comment '公司' charset = utf8;

create table company_cache_info
(
    id             varchar(50)  not null
        primary key,
    company_id     varchar(50)  null,
    link_id        varchar(50)  null comment '业务关联id',
    business_scene varchar(50)  null comment '业务场景',
    cache_key      varchar(255) null comment '缓存的key',
    value          longtext     null comment '缓存的value',
    is_deleted     varchar(10)  null,
    created_user   varchar(50)  null,
    created_time   datetime     null,
    updated_user   varchar(50)  null,
    updated_time   datetime     null
)
    comment '业务数据缓存表';

create index company_id
    on company_cache_info (company_id);

create table company_category
(
    id                 varchar(50)      not null
        primary key,
    company_id         varchar(50)      null,
    category_name      varchar(255)     null comment '类别名称',
    type               varchar(50)      null comment '所属模块',
    is_deleted         varchar(10)      null,
    created_user       varchar(50)      null,
    created_time       datetime         null,
    updated_user       varchar(50)      null,
    updated_time       datetime         null,
    parent_category_id varchar(50)      null comment '上级分类id',
    top_category_id    varchar(50)      null comment '所属顶级分类id',
    category_level     int              null comment '分类等级',
    is_default         int(1) default 1 null comment '是否默认权限 默认=0否、1=是',
    category_num       int              null comment '类别序号',
    use_type           int(1) default 1 null comment '引用类型（1：全公司  2：指定部门 3：指定员工）',
    sub_org            int(1) default 1 null comment '是否包含子部门（默认=0否、1=是  引用权限使用字段）'
)
    comment '类别表';

create index company_id
    on company_category (company_id);

create index deleted_key
    on company_category (is_deleted);

create index top_key
    on company_category (top_category_id);

create table company_dic
(
    id              varchar(50)   not null
        primary key,
    company_id      varchar(50)   null comment '公司id',
    sys_dic_type_id varchar(50)   null comment '字典类型id',
    sys_dic_type    varchar(50)   null comment '字典类型',
    dic_value       varchar(200)  null comment '字典值',
    is_temporary    varchar(10)   null comment '是否临时数据',
    is_default      varchar(10)   null comment '是否默认值',
    is_deleted      varchar(10)   null comment '是否删除',
    created_user    varchar(50)   null comment '创建用户',
    created_time    datetime      null comment '创建时间',
    updated_user    varchar(50)   null comment '修改用户',
    updated_time    datetime      null comment '修改时间',
    version         int default 0 null comment '版本号'
)
    comment '公司字典' charset = utf8mb4;

create index company_id
    on company_dic (company_id);

create table company_formula_field
(
    id                 varchar(50)   not null
        primary key,
    company_id         varchar(50)   null,
    formula_field_name varchar(100)  null comment '公式里的字段名称',
    module             varchar(30)   null comment '字段所属模块',
    is_system_field    varchar(10)   null comment '是否系统设置里的字段',
    is_temporary       varchar(10)   null comment '是否临时字段',
    is_deleted         varchar(10)   null,
    created_user       varchar(50)   null,
    created_time       datetime      null,
    updated_user       varchar(50)   null,
    updated_time       datetime      null,
    version            int default 0 null comment '版本号'
)
    comment '公司计算公式字段表';

create index company_id
    on company_formula_field (company_id);

create table company_init_industry
(
    id                varchar(50)  not null
        primary key,
    company_id        varchar(50)  null comment '公司id',
    sys_item_industry varchar(255) null comment '已导入的系统指标行业',
    created_user      varchar(50)  null,
    created_time      datetime     null,
    updated_user      varchar(50)  null,
    updated_time      datetime     null
)
    comment '公司初始化的行业';

create index company_id
    on company_init_industry (company_id);

create table company_item_decompose
(
    id           varchar(50)    not null
        primary key,
    company_id   varchar(50)    null,
    kpi_item_id  varchar(50)    null comment '指标id',
    year         int            null comment '年份',
    emp_id       varchar(50)    null comment '员工id',
    month1       decimal(18, 2) null comment '一月分解值',
    month2       decimal(18, 2) null comment '2月分解值',
    month3       decimal(18, 2) null comment '3月分解值',
    month4       decimal(18, 2) null comment '4月分解值',
    month5       decimal(18, 2) null comment '5月分解值',
    month6       decimal(18, 2) null comment '6月分解值',
    month7       decimal(18, 2) null comment '7月分解值',
    month8       decimal(18, 2) null comment '8月分解值',
    month9       decimal(18, 2) null comment '9月分解值',
    month10      decimal(18, 2) null comment '10月分解值',
    month11      decimal(18, 2) null comment '11月分解值',
    month12      decimal(18, 2) null comment '12月分解值',
    total_value  decimal(18, 2) null comment '合计',
    status       varchar(50)    null comment '状态',
    is_deleted   varchar(10)    null,
    created_user varchar(50)    null,
    created_time datetime       null,
    updated_user varchar(50)    null,
    updated_time datetime       null
)
    comment '指标分解到员工';

create index company_id
    on company_item_decompose (company_id);

create index item_emp
    on company_item_decompose (company_id, is_deleted, kpi_item_id, emp_id);

create table company_item_rel_org
(
    id              varchar(50) not null
        primary key,
    company_id      varchar(50) null,
    company_item_id varchar(50) null comment '公司指标id',
    org_id          varchar(50) null comment '指标关联的部门id',
    is_deleted      varchar(10) null,
    created_user    varchar(50) null,
    created_time    datetime    null,
    updated_user    varchar(50) null,
    updated_time    datetime    null
)
    comment '指标关联的部门';

create index company_id
    on company_item_rel_org (company_id);

create table company_item_rel_tag
(
    id              varchar(50) not null
        primary key,
    company_id      varchar(50) null,
    company_item_id varchar(50) null comment '公司指标id',
    tag_id          varchar(50) null comment '指标关联的部门id',
    is_deleted      varchar(10) null,
    created_user    varchar(50) null,
    created_time    datetime    null,
    updated_user    varchar(50) null,
    updated_time    datetime    null
)
    comment '指标关联的标签';

create index company_id
    on company_item_rel_tag (company_id);

create table company_item_used_field
(
    field_id     varchar(50)      not null comment 'company_kpi_item_custom_field.id',
    company_id   varchar(50)      null comment '公司id',
    kpi_item_id  varchar(50)      null comment 'company_kpi_item.id',
    name         varchar(50)      null comment '字段名称【复制于company_kpi_item_custom_field】',
    value        varchar(512)     null comment '字段值【复制于company_kpi_item_custom_field】',
    type         int(2) default 1 null comment '字段类型（1：输入框  2：开关 3：下拉选 4：复选 5：img）【复制于company_kpi_item_custom_field】',
    req          int(1) default 1 null comment '是否必填（0：否  1：是）【复制于company_kpi_item_custom_field】',
    status       varchar(20)      null comment '指标字段状态（valid：有效，invalid：无效）【复制于company_kpi_item_custom_field】',
    `show`       int(1) default 1 null comment '是否显示（0：否  1：是）',
    sort         int(5)           null comment '指标字段排序值',
    admin_type   int(1)           null comment '1:系统默认字段  0：自定义字段',
    is_deleted   varchar(10)      null comment '是否删除',
    created_user varchar(50)      null comment '创建用户',
    created_time datetime         null comment '创建时间',
    updated_user varchar(50)      null comment '修改用户',
    updated_time datetime         null comment '修改时间',
    version      int    default 0 null
)
    comment '指标库自定义字段使用表' charset = utf8mb4;

create index company_key
    on company_item_used_field (company_id);

create index field_key
    on company_item_used_field (field_id);

create index item_key
    on company_item_used_field (kpi_item_id);

create table company_kpi_item
(
    id                      varchar(50)      not null comment 'id'
        primary key,
    company_id              varchar(50)      null comment '公司id',
    sys_item_id             varchar(50)      null comment '系统指标库id',
    item_path               text             null comment '指标id链，“|”隔开',
    item_name               text             null comment '指标项名称',
    item_type               varchar(50)      null comment '指标项类型（量化/非量化）',
    item_value              decimal(18, 2)   null comment '指标项目标值',
    item_plan_value         decimal(18, 2)   null comment '指标计划值',
    item_unit               varchar(50)      null comment '目标值单位',
    parent_kpi_item_id      varchar(50)      null comment '上级指标项',
    item_rule               text             null comment '考核规则',
    scoring_rule            text             null comment '计分规则',
    result_input_type       varchar(50)      null comment '指标结果录入类型（被考核人/指定员工/无需录入）',
    result_input_user_id    text             null comment '指定结果录入人id',
    scorer_type             varchar(50)      null comment '指标评分人类型（按评分流程/指定员工/指定主管）',
    scorer_user_id          text             null comment '指定评分人id',
    multiple_reviewers_type varchar(30)      null comment '审核为多人时是或签还是会签',
    item_weight             decimal(10, 2)   null comment '指标项权重',
    category_id             varchar(100)     null comment '指标级别id',
    is_board                varchar(10)      null comment '是否加入指标看板',
    is_deleted              varchar(10)      null comment '是否删除',
    is_temporary            varchar(10)      null comment '是否临时指标',
    threshold_json          text             null comment '量化指标阈值设置json',
    show_target_value       varchar(10)      null comment '是否展示目标值',
    created_user            varchar(50)      null comment '创建用户',
    created_time            datetime         null comment '创建时间',
    updated_user            varchar(50)      null comment '修改用户',
    updated_time            datetime         null comment '修改时间',
    version                 int    default 0 null,
    item_custom_field_json  text             null comment '指标自定义字段json',
    item_formula            text             null comment '指标计算公式',
    formula_type            int(1) default 1 null comment '公式类型：1=多场景公式、2=高级公式'
)
    comment '公司指标库' charset = utf8mb4;

create index category_key
    on company_kpi_item (category_id);

create index company_id
    on company_kpi_item (company_id);

create table company_kpi_item_custom_field
(
    id           varchar(50)   not null comment 'id'
        primary key,
    company_id   varchar(50)   null comment '公司id',
    field_name   varchar(50)   null comment '字段名称',
    is_req       int default 1 null comment '是否必填（0：否  1：是）',
    field_status varchar(50)   null comment '指标字段状态（valid：有效，invalid：无效）',
    field_order  int           null comment '指标字段排序值',
    is_deleted   varchar(10)   null comment '是否删除',
    created_user varchar(50)   null comment '创建用户',
    created_time datetime      null comment '创建时间',
    updated_user varchar(50)   null comment '修改用户',
    updated_time datetime      null comment '修改时间',
    version      int default 0 null
)
    comment '公司自定义指标字段' charset = utf8mb4;

create index company_id
    on company_kpi_item_custom_field (company_id);

create table company_msg_center
(
    id             varchar(50)  not null
        primary key,
    company_id     varchar(50)  null,
    business_scene varchar(50)  null comment '业务场景',
    emp_id         varchar(50)  null comment '当前处理人id',
    link_id        varchar(50)  null comment '关联业务id',
    att_content    text         null comment '附加内容',
    url            varchar(500) null comment '跳转url',
    third_msg_id   varchar(100) null comment '第三方消息id',
    handler_status varchar(50)  null comment '处理状态',
    is_urgent      varchar(10)  null comment '是否紧急：true/fase',
    params         text         null comment '其他参数',
    third_msg_flag varchar(10)  null comment '是否需要给第三方发待办',
    created_user   varchar(50)  null,
    created_time   datetime     null,
    updated_time   datetime     null,
    is_ignore      varchar(10)  null comment '是否忽略提醒，true/false'
)
    comment '消息中心' charset = utf8mb4;

create index company_id
    on company_msg_center (company_id);

create index k_emp_id
    on company_msg_center (emp_id);

create index k_link_id
    on company_msg_center (link_id);

create table company_msg_rel_score_result
(
    id              varchar(50) not null
        primary key,
    company_id      varchar(50) null comment '公司id',
    score_result_id varchar(50) null comment '考核结果id',
    emp_id          varchar(50) null comment '当前责任人id',
    task_id         varchar(50) null comment '考核任务id',
    temp_id         varchar(50) null comment '模板id',
    msg_id          varchar(50) null comment '待办消息id',
    scorer_id       varchar(50) null comment '原责任人id',
    handle_status   varchar(10) null comment '处理结果：true/false',
    to_emp_id       varchar(50) null comment '转交接收人id',
    task_emp_id     varchar(50) null comment '被考核员工id',
    created_time    datetime    null comment '创建时间',
    updated_time    datetime    null comment '更新时间'
)
    comment '待办对应考核结果';

create index handleIndex
    on company_msg_rel_score_result (company_id, handle_status);

create index index_result_id
    on company_msg_rel_score_result (score_result_id);

create index index_scorer_id
    on company_msg_rel_score_result (scorer_id);

create table company_notice
(
    id             varchar(50)  not null
        primary key,
    company_id     varchar(50)  null,
    business_scene varchar(50)  null comment '业务场景',
    msg_type       varchar(50)  null comment '消息类型',
    emp_id         varchar(50)  null comment '接收人id',
    link_id        varchar(50)  null comment '关联业务id',
    att_content    text         null comment '附加内容',
    url            varchar(500) null comment '跳转url',
    third_msg_id   varchar(100) null comment '第三方消息id',
    params         text         null,
    created_user   varchar(50)  null comment '附加参数',
    created_time   datetime     null,
    updated_time   datetime     null,
    show_flag      varchar(10)  null comment '是否需要展示在通知我的页面'
)
    comment '对外发送通知表' charset = utf8mb4;

create index company_id
    on company_notice (company_id);

create table company_perf_level_header
(
    id           varchar(50)  not null
        primary key,
    company_id   varchar(50)  null,
    level_seq    int          null comment '等级序号',
    level_name   varchar(100) null comment '等级名称',
    is_deleted   varchar(10)  null,
    created_user varchar(50)  null,
    created_time datetime     null,
    updated_user varchar(50)  null,
    updated_time datetime     null,
    step_id      varchar(50)  null comment '等级分值步长id'
)
    comment '绩效等级表头配置';

create index idx_companyid
    on company_perf_level_header (company_id);

create table company_perf_level_quota
(
    id           varchar(50)  not null
        primary key,
    company_id   varchar(50)  null comment '公司id',
    org_id       varchar(50)  null comment '部门id',
    level_name   varchar(200) null comment '等级名称',
    level_quota  bigint       null comment '等级配额',
    is_deleted   varchar(10)  null,
    created_user varchar(50)  null,
    created_time datetime     null,
    updated_user varchar(50)  null,
    updated_time datetime     null
)
    comment '绩效等级配额';

create index idx_companyid
    on company_perf_level_quota (company_id);

create table company_score_group
(
    id           varchar(50)                 not null
        primary key,
    group_name   varchar(50)                 not null comment '分组名称',
    company_id   varchar(50)                 null comment '公司id',
    is_deleted   varchar(10) default 'false' null,
    created_user varchar(50)                 null,
    created_time datetime                    null,
    updated_user varchar(50)                 null,
    updated_time datetime                    null
)
    comment '考核评分组';

create index company_id
    on company_score_group (company_id);

create table company_score_group_copy
(
    id           varchar(50)                 not null,
    group_name   varchar(50)                 not null comment '分组名称',
    company_id   varchar(50)                 null comment '公司id',
    is_deleted   varchar(10) default 'false' null,
    created_user varchar(50)                 null,
    created_time datetime                    null,
    updated_user varchar(50)                 null,
    updated_time datetime                    null
);

create index company_id
    on company_score_group_copy (company_id);

create table company_score_group_member
(
    id           varchar(50)                 not null
        primary key,
    company_id   varchar(50)                 null,
    group_id     varchar(50)                 null comment '考核评分组id',
    weight       decimal(10, 2)              null comment '权重，百分比',
    emp_id       varchar(50)                 null comment '评分成员id',
    is_deleted   varchar(10) default 'false' null,
    created_user varchar(50)                 null,
    created_time datetime                    null,
    updated_user varchar(50)                 null,
    updated_time datetime                    null
)
    comment '考核分组成员';

create index company_id
    on company_score_group_member (company_id);

create table company_sys_setting
(
    id           varchar(50)  not null
        primary key,
    company_id   varchar(50)  null comment '公司id',
    setting_type varchar(100) null comment '设置类型',
    value        varchar(50)  null comment '设置值',
    is_deleted   varchar(10)  null comment '是否删除',
    created_user varchar(50)  null comment '创建用户',
    created_time datetime     null comment '创建时间',
    updated_user varchar(50)  null comment '修改用户',
    updated_time datetime     null comment '修改时间'
)
    comment '公司系统设置' charset = utf8mb4;

create index company_id
    on company_sys_setting (company_id);

create table company_tag
(
    id           varchar(50)   not null
        primary key,
    company_id   varchar(50)   null,
    tag_name     varchar(255)  null comment '标签名称',
    type         varchar(50)   null comment '所属模块',
    is_deleted   varchar(10)   null,
    created_user varchar(50)   null,
    created_time datetime      null,
    updated_user varchar(50)   null,
    updated_time datetime      null,
    version      int default 0 null comment '版本号'
)
    comment '标签表';

create index company_id
    on company_tag (company_id);

create table company_test
(
    id              varchar(40) default '0'      not null comment '自增id'
        primary key,
    code            varchar(10)                  null comment '公司code',
    name            varchar(512) charset utf8mb4 null comment '公司名称',
    perm_start_time date                         null comment '权限开始日期',
    perm_end_time   date                         null comment '权限结束日期'
)
    comment '用于测试自动到期，测试完毕之后删除本表' charset = utf8;

create table cycle_eval_rule
(
    cycle_id       bigint        not null comment '周期id',
    company_id     varchar(50)   not null comment '公司id',
    audit_result   text          null comment '结果校准流程配置',
    confirm_result text          null comment '结果确认流程配置',
    publish_result text          null comment '结果公示流程配置',
    appeal_conf    text          null comment '结果申诉流程配置',
    score_conf     text          null comment '评分设置',
    score_view     text          null comment '评分人查看权限',
    created_user   varchar(50)   null,
    updated_user   varchar(50)   null,
    created_time   datetime      null,
    updated_time   datetime      null,
    version        int default 0 null comment '版本号'
)
    comment '周期规则配置' charset = utf8mb4;

create index k_company_id
    on cycle_eval_rule (company_id);

create index k_cycle_id
    on cycle_eval_rule (cycle_id);

create table dead_line_job
(
    company_id         varchar(50)                 not null comment '企业id',
    task_id            varchar(50)                 not null comment '管理任务id',
    task_user_id       varchar(50)                 not null comment '员工任务id',
    business_type      varchar(20)                 not null comment '状态类型：confirming=指标确认、confirmed=执行中',
    end_date           date                        null comment '截止时间',
    execute_status     int(1)      default 0       null comment '执行状态：1=执行完成 0=未执行',
    auto_skip          int(1)      default 0       null comment '是否自动跳过：1=跳过 0=不处理',
    auto_urging        int(1)      default 0       null comment '是否自动催办：1=开启 0=未开启',
    first_urging_time  datetime                    null comment '第一次催办时间',
    second_urging_time datetime                    null comment '第二次催办时间',
    is_deleted         varchar(5)  default 'false' null comment '是否删除',
    created_user       varchar(50) default ''      null,
    updated_user       varchar(50) default ''      null,
    created_time       datetime                    null,
    updated_time       datetime                    null,
    version            int         default 0       null comment '版本号'
)
    comment '阶段截止时间定时任务扫描数据表' charset = utf8mb4;

create index idx_company_id
    on dead_line_job (company_id);

create index idx_task_id
    on dead_line_job (task_id);

create index idx_task_user_id
    on dead_line_job (task_user_id);

create table dept_leader_syn
(
    company_id   varchar(50) null comment '公司ID',
    org_id       varchar(50) not null comment '部门ID',
    ding_user_id varchar(50) null comment '钉钉用户id',
    push_id      bigint      not null comment 'open_sync_biz_data.id 钉钉事件id',
    constraint uk_orgId_userId
        unique (company_id, ding_user_id, org_id, push_id)
)
    comment '员工同步临时表,部门主管' charset = utf8mb4;

create index idx_pushId
    on dept_leader_syn (push_id);

create index idx_userId
    on dept_leader_syn (ding_user_id);

create table dept_ref_rule
(
    company_id   varchar(60)                 not null comment '公司id',
    rule_id      varchar(50)                 not null comment '分值id',
    org_id       varchar(60)                 null,
    is_deleted   varchar(10) default 'false' null,
    created_user varchar(50)                 null comment '创建人',
    created_time varchar(50)                 null comment '创建时间',
    updated_user varchar(50)                 null comment '更新人',
    updated_time varchar(50)                 null comment '更新时间',
    version      int         default 0       null
);

create table emp_apply_use
(
    id           varchar(50) not null comment 'id'
        primary key,
    company_id   varchar(50) null comment '公司id',
    emp_id       varchar(50) null comment '员工id',
    status       varchar(20) null comment '状态（review:审核中,pass：同意，unpass：不同意）',
    created_time datetime    null comment '创建时间',
    created_user varchar(50) null,
    updated_time datetime    null comment '更新时间',
    updated_user varchar(50) null comment '操作人'
)
    comment '员工申请使用表' charset = utf8mb4;

create index company_id
    on emp_apply_use (company_id);

create table emp_eval_kpi_type
(
    task_user_id       varchar(50)                  not null comment 'task user 表的id',
    company_id         varchar(50)                  not null comment '公司id',
    kpi_type_id        varchar(50)                  not null comment '指标类id',
    kpi_type_name      varchar(200)                 null comment '指标类名称',
    kpi_type_weight    decimal(10, 2)               null comment '指标类权重',
    is_okr             varchar(10)                  null comment '是否OKR类别，true/false',
    type_order         int(10)                      null comment '类别排序',
    reserve_okr_weight decimal(10, 2)               null comment '预留OKR权重',
    kpi_type_classify  varchar(30)                  null comment '指标类别，为空表示已有类别，custom自定义/plus加分项/subtract减分项',
    max_extra_score    decimal(10, 2)               null comment '本类别最大加减分上限',
    import_okr_flag    varchar(10)                  null comment '是否已导入OKR',
    locked_items       varchar(100)                 null comment 'is_type_locked 类别锁定类型  ["typeWeight","addIndex","modifyIndex","deleteIndex"]',
    is_deleted         varchar(10)  default 'false' null comment '是否删除',
    created_user       varchar(50)                  null comment '创建用户',
    created_time       datetime                     null comment '创建时间',
    updated_user       varchar(50)                  null comment '修改用户',
    updated_time       datetime                     null comment '修改时间',
    version            int          default 0       null comment '版本号',
    item_limit_cnt     varchar(128)                 null comment '类别指标数量最大最小限制',
    open_okr_score     int          default 0       null comment '指标评分使用的是okr的分数',
    plus_sub_interval  varchar(50)                  null comment '考核规则类别加减分上限',
    self_rater         varchar(128) default ''      null comment '自评人',
    super_rater        text                         null comment '上级人 json',
    appoint_rater      text                         null comment '指定评分人json',
    peer_rater         text                         null comment '自定义同级互评人',
    sub_rater          text                         null comment '自定义下级互评人',
    ind_level_group    text                         null comment '指标等级组 ',
    ind_level_group_id bigint                       null comment '指标等级组id',
    type_level         varchar(50)                  null comment '最后评价等级',
    finish_value_audit text                         null comment '完成值审核人列表',
    ind_level          varchar(100)                 null comment '提交的等级',
    score_opt_type     int          default 2       null comment '# 0:等级[无等级组]indLevelId==null # 1:等级[设等级组] indLevelId=1000234  #2:评分[无选项组] indLevelId==null  # 4:评分[有选项组], indLevelId=1000235 ',
    des                varchar(512)                 null comment '维度描述 20230921增加'
)
    comment '考核任务-指标类别' charset = utf8mb4;

create index idx_company_id
    on emp_eval_kpi_type (company_id);

create index idx_task_user_id
    on emp_eval_kpi_type (task_user_id);

create table emp_eval_operation
(
    id              varchar(50)  not null comment '操作记录id'
        primary key,
    emp_eval_id     varchar(50)  not null comment '外键task_user.id, 员工任务id',
    company_id      varchar(50)  not null comment '公司id',
    at_status       varchar(50)  not null comment '修改时所在阶段',
    change_type     int          not null comment '01=1=指标, 10=2=流程,11=3=指标+流程',
    indicator       mediumtext   null comment '指标类修改,type_weight_conf,score_value_conf, _kpi表,_kpi_type表',
    confirm_task    text         null comment '确认任务 对应模板 templAffirmJson:PerfTemplEvaluateAffirm',
    edit_exe_indi   text         null comment '执行中修改指标日志记录  对应模板 templExecuteJson:PerfTemplEvaluateExecute',
    score_conf      text         null comment 'enter_score,s3_self_rater,s3_mutual_rater,s3_super_rater ,score_veiw',
    audit_result    text         null comment '结果校准,从templEvaluateJson:PerfTemplEvaluate 模板评价配置json 单独出来的',
    confirm_result  varchar(512) null comment '结果确认,多个字段合并',
    publish_result  text         null comment '结果公示,多个字段合并',
    appeal_conf     varchar(512) null comment '结果申诉,多个字段合并',
    operation_time  bigint       not null comment '操作时间毫秒ms',
    operator_id     varchar(50)  not null comment '操作人emp_id',
    operator_name   varchar(50)  not null comment '操作人名',
    operator_avatar varchar(225) not null comment '操作人头像',
    created_time    datetime     null comment '记录创建时间',
    admin_type      varchar(25)  null comment '操作人管理员类型/admin/child',
    base_info       text         null comment '基本信息',
    dead_line_conf  text         null comment '阶段截止时间配置'
)
    comment '员工任务的操作日志' charset = utf8mb4;

create index idx_company_id
    on emp_eval_operation (company_id);

create index idx_emp_eval_id
    on emp_eval_operation (emp_eval_id);

create index idx_operation_time
    on emp_eval_operation (operation_time);

create table emp_eval_rule
(
    emp_eval_id             varchar(50)                  not null comment '外键task_user.id, 新加',
    company_id              varchar(50)                  not null comment '公司id',
    rule_name               varchar(250) default ''      null comment '考核规则名字',
    evaluate_type           varchar(10)                  not null comment '评分流程类型 ： simple-简易流程，360-360°考核流程，custom-自定义流程',
    type_weight_conf        varchar(128) default ''      null comment '指标类别权重 json',
    score_value_conf        varchar(128) default ''      null comment '分值的设定 json',
    confirm_task            text                         null comment '确认任务 对应模板 templAffirmJson:PerfTemplEvaluateAffirm',
    edit_exe_indi           text                         null comment '执行中修改指标  对应模板 templExecuteJson:PerfTemplEvaluateExecute',
    enter_score             varchar(128) default ''      null comment '启动评分配置 ,多字段合并',
    s3_self_rater           varchar(128) default ''      null comment '360或简易 自评人',
    s3_mutual_rater         text                         null comment '360或简易 互评人 json',
    s3_super_rater          text                         null comment '360或简易 上级人 json',
    score_view              text                         null comment '可见范围配置 json',
    audit_result            text                         null comment '结果校准,从templEvaluateJson:PerfTemplEvaluate 模板评价配置json 单独出来的',
    confirm_result          text                         null comment '结果确认,多个字段合并',
    publish_result          text                         null comment '结果公示,多个字段合并',
    appeal_conf             text                         null comment '结果申诉,多个字段合并',
    level_group_id          varchar(50)  default ''      null comment '关联哪个等级组/perf_evaluation_level 的level_group_id',
    index_raters            text                         null comment '评分人索引,用于员工列表的显示,编辑时更新索引',
    indicator_cnt           int          default 0       null comment '指标数统计',
    is_deleted              varchar(5)   default 'false' null comment '是否删除',
    created_user            varchar(50)                  null comment '创建用户',
    created_time            datetime                     null comment '创建时间',
    updated_user            varchar(50)                  null comment '修改用户',
    updated_time            datetime                     null comment '修改时间',
    version                 int          default 0       null comment '版本号',
    score_conf              varchar(225)                 null comment '评分设置',
    comment_conf            varchar(225)                 null comment '评分设置',
    s3_peer_rater           text                         null comment '同级互评人 json',
    s3_sub_rater            text                         null comment '同级互评人 json',
    edit_status             int          default 0       null comment '配置变更情况',
    initiator               varchar(50)                  null comment '考核表发起人',
    s3_appoint_rater        text                         null comment '指定评分人json',
    show_result_type        int                          null comment '结果呈现:111=总分,总等级,维度等级',
    create_total_level_type int                          null comment '绩效总等级生成方式 1 = 自动, 2= 手动',
    total_level_raters      text                         null comment '绩效总等级评价人员',
    finish_value_audit      text                         null comment '完成值审核考核规则配置',
    dead_line_conf          text                         null comment '阶段截止时间配置'
)
    comment '考核规则' charset = utf8mb4;

create index idx_company_id
    on emp_eval_rule (company_id);

create table emp_eval_table
(
    company_id              varchar(50)                  not null comment '公司id',
    id                      varchar(50)                  not null comment '主键:考核表id'
        primary key,
    emp_id                  varchar(50)                  null comment '外键员工id:emloyee_info.employee_id',
    is_default              int          default 0       null comment '是否员工的默认考核表',
    rule_name               varchar(250) default ''      null comment '考核表名字',
    evaluate_type           varchar(10)                  not null comment '评分流程类型 ： simple-简易流程，360-360°考核流程，custom-自定义流程',
    type_weight_conf        varchar(128) default ''      null comment '指标类别权重 json',
    score_value_conf        varchar(128) default ''      null comment '分值的设定 json',
    s3_self_rater           varchar(128) default ''      null comment '360或简易 自评人',
    s3_peer_rater           text                         null comment '同级互评人 json',
    s3_sub_rater            text                         null comment '同级互评人 json',
    s3_super_rater          text                         null comment '360或简易 上级人 json',
    score_view              text                         null comment '可见范围配置 json',
    index_raters            text                         null comment '评分人索引,用于员工列表的显示,编辑时更新索引',
    indicator_cnt           int          default 0       null comment '指标数统计',
    comment_conf            varchar(225)                 null comment '评语与总结配置',
    is_deleted              varchar(5)   default 'false' null comment '是否删除',
    created_user            varchar(50)                  null comment '创建用户',
    created_time            datetime                     null comment '创建时间',
    updated_user            varchar(50)                  null comment '修改用户',
    updated_time            datetime                     null comment '修改时间',
    version                 int          default 0       null comment '版本号',
    eval_org_id             varchar(50)                  null comment '被考核组织Id',
    performance_type        int          default 1       null comment '绩效类型 1=个人，2=组织',
    s3_appoint_rater        text                         null comment '指定评分人json',
    show_result_type        int                          null comment '结果呈现:111=总分,总等级,维度等级',
    create_total_level_type int                          null comment '绩效总等级生成方式 1 = 自动, 2= 手动',
    total_level_raters      text                         null comment '绩效总等级评价人员'
)
    comment '员工的考核表,一个员工可以有多个考核表' charset = utf8mb4;

create index idx_companyId_empId
    on emp_eval_table (company_id, emp_id);

create table emp_eval_type_used_field
(
    field_id     varchar(50)      not null comment 'company_kpi_item_custom_field.id',
    company_id   varchar(50)      null comment '公司id',
    task_user_id varchar(50)      not null comment 'task user 表的id',
    kpi_type_id  varchar(50)      null comment 'emp_eval_kpi_type.kpi_type_id',
    name         varchar(50)      null comment '字段名称【复制于company_kpi_item_custom_field】',
    type         int(2) default 1 null comment '字段类型（1：输入框  2：开关 3：下拉选 4：复选 5：img）【复制于company_kpi_item_custom_field】',
    req          int(1) default 1 null comment '是否必填（0：否  1：是）【复制于company_kpi_item_custom_field】',
    status       varchar(20)      null comment '指标字段状态（valid：有效，invalid：无效）【复制于company_kpi_item_custom_field】',
    `show`       int(1) default 1 null comment '是否显示（0：否  1：是）',
    sort         int(5)           null comment '指标字段排序值',
    admin_type   int(1)           null comment '1:系统默认字段  0：自定义字段',
    is_deleted   varchar(10)      null comment '是否删除',
    created_user varchar(50)      null comment '创建用户',
    created_time datetime         null comment '创建时间',
    updated_user varchar(50)      null comment '修改用户',
    updated_time datetime         null comment '修改时间',
    version      int    default 0 null
)
    comment '考核任务维度自定义字段使用表' charset = utf8mb4;

create index company_key
    on emp_eval_type_used_field (company_id);

create index field_key
    on emp_eval_type_used_field (field_id);

create index type_key
    on emp_eval_type_used_field (kpi_type_id);

create table emp_of_post
(
    company_id   varchar(50)                           not null comment '公司id',
    emp_id       varchar(50)                           not null comment '员工id',
    post_id      varchar(50)                           not null comment '职级id',
    created_time datetime    default CURRENT_TIMESTAMP null,
    created_user varchar(50) default ''                null,
    updated_time datetime    default CURRENT_TIMESTAMP null,
    updated_user varchar(50) default ''                null,
    is_deleted   varchar(5)  default 'false'           null,
    version      int         default 0                 null comment '版本号'
)
    comment '员工所在职级-关联表' charset = utf8mb4;

create index oop_idx_empId
    on emp_of_post (company_id, emp_id);

create table emp_organization
(
    id                     varchar(50)   null,
    org_id                 varchar(50)   not null comment '部门ID'
        primary key,
    org_code               varchar(1000) null comment '部门代码路径，所有父节点加上自身节点的节点ID，采用|号拼接生成',
    company_id             varchar(50)   not null comment '公司ID',
    org_name               varchar(128)  null comment '部门名称',
    parent_org_id          varchar(50)   null comment '父级部门ID',
    manager_id             varchar(50)   null comment '负责人ID',
    executor_id            varchar(50)   null comment '执行人id',
    status                 varchar(50)   null comment '部门状态:有效；无效',
    type                   varchar(50)   null comment '部门类型:顶级节点部门；普通部门',
    name_chinese           varchar(512)  null comment '搜索关键字',
    created_time           datetime      null comment '创建时间',
    updated_time           datetime      null comment '更新时间',
    sort_num               bigint(11)    null comment '排序',
    top_report_id          varchar(50)   null comment '最高汇报人ID',
    staff_num              int(20)       null comment '人员编制',
    org_num                varchar(50)   null comment '部门编码',
    build_time             datetime      null comment '成立日期',
    memo                   varchar(255)  null comment '备注',
    qy_org_id              varchar(50)   null comment '企业微信对应id',
    ding_org_id            varchar(50)   null comment '钉钉对应id',
    we_org_id              varchar(50)   null comment 'welink对应id',
    version                int default 0 null comment '版本号',
    direct_emp_cnt         int default 0 null comment '直属员工数,应用授权时同会全量计算,部门变更时增量计算',
    child_emp_cnt          int default 0 null comment '子部门员工总数,应用授权时同会全量计算,部门变更时增量计算',
    direct_child_org_cnt   int default 0 null comment '直属子部门数,应用授权时同会全量计算,部门变更时增量计算',
    distinct_total_emp_cnt int default 0 null comment '直属员工+子部门员工去重后总数',
    last_parent_org_id     varchar(50)   null comment '前一次的上级部门id, 主要用于重复计算直属子部门数,支持幂等性'
)
    comment '部门' charset = utf8mb4;

create index idx_companyid_orgname
    on emp_organization (company_id, org_name);

create index idx_eo_cid
    on emp_organization (company_id);

create index idx_eo_sta
    on emp_organization (status);

create table emp_organization_backup
(
    id            varchar(50)   null,
    org_id        varchar(50)   not null comment '部门ID'
        primary key,
    org_code      varchar(1000) null comment '部门代码路径，所有父节点加上自身节点的节点ID，采用|号拼接生成',
    company_id    varchar(50)   not null comment '公司ID',
    org_name      varchar(128)  null comment '部门名称',
    parent_org_id varchar(50)   null comment '父级部门ID',
    manager_id    varchar(50)   null comment '负责人ID',
    executor_id   varchar(50)   null comment '执行人id',
    status        varchar(50)   null comment '部门状态:有效；无效',
    type          varchar(50)   null comment '部门类型:顶级节点部门；普通部门',
    name_chinese  varchar(512)  null comment '搜索关键字',
    created_time  datetime      null comment '创建时间',
    updated_time  datetime      null comment '更新时间',
    sort_num      bigint(11)    null comment '排序',
    top_report_id varchar(50)   null comment '最高汇报人ID',
    staff_num     int(20)       null comment '人员编制',
    org_num       varchar(50)   null comment '部门编码',
    build_time    datetime      null comment '成立日期',
    memo          varchar(255)  null comment '备注',
    qy_org_id     varchar(50)   null comment '企业微信对应id',
    ding_org_id   varchar(50)   null comment '钉钉对应id',
    we_org_id     varchar(50)   null comment 'welink对应id',
    version       int default 0 null comment '版本号'
)
    comment '部门' charset = utf8mb4;

create index idx_companyid_orgname
    on emp_organization_backup (company_id, org_name);

create index idx_eo_cid
    on emp_organization_backup (company_id);

create index idx_eo_sta
    on emp_organization_backup (status);

create table emp_ref_announcement
(
    emp_id          varchar(50) null,
    announcement_id varchar(50) null,
    created_time    varchar(50) null
);

create index announcement_id
    on emp_ref_announcement (announcement_id);

create index emp_id
    on emp_ref_announcement (emp_id);

create table emp_ref_org
(
    id           varchar(50) not null comment 'id'
        primary key,
    company_id   varchar(50) null comment '公司id',
    emp_id       varchar(50) null comment '员工id',
    org_id       varchar(50) null comment '部门id',
    ref_type     varchar(10) null comment '关联类型(org:员工关联部门,manager:部门负责人)',
    is_main_org  varchar(10) null comment '是否为主部门：true/false',
    created_time datetime    null comment '创建时间',
    updated_time datetime    null comment '更新时间'
)
    comment '员工关联部门表' charset = utf8mb4;

create index idx_empid_orgid
    on emp_ref_org (emp_id, org_id);

create index idx_ero_cid
    on emp_ref_org (company_id);

create index idx_ero_oid
    on emp_ref_org (org_id);

create index idx_orgid_reftype_empid
    on emp_ref_org (org_id, ref_type, emp_id);

create table emp_ref_score_rule
(
    rule_id        varchar(50)                 null comment '等级分值规则id',
    emp_id         varchar(50)                 null comment '员工id',
    emp_org_id     varchar(50)                 null comment '员工所在部门id',
    company_id     varchar(50)                 null comment '公司id',
    created_time   date                        null,
    created_user   varchar(50) default ''      null,
    updated_time   date                        null,
    updated_user   varchar(50) default ''      null,
    is_deleted     varchar(5)  default 'false' null,
    version        int         default 0       null,
    is_add_emp     varchar(10)                 null comment '单独添加进来的人',
    is_appoint_emp int         default 0       null comment '创建等级规则时，指定的人'
);

create index idx_company_id
    on emp_ref_score_rule (company_id);

create index idx_emp_id
    on emp_ref_score_rule (emp_id);

create index idx_rule_id
    on emp_ref_score_rule (rule_id);

create table emp_table_item_rule
(
    table_id          varchar(50)   not null comment '考核表的id,emp_eval_table.id',
    kpi_item_id       varchar(50)   null comment '指标项id  emp_table_kpi_item.id',
    company_id        varchar(50)   null comment '公司id',
    kpi_type_id       varchar(50)   null comment '指标类id',
    mutual_user_type  varchar(30)   null comment '设置互评人类型，all:为所有被考核人设置相同的互评人; user: 为每位被考核人分别设置互评人; exam：由被考核人自行指定',
    mutual_user_value text          null comment '自定义互评设置人',
    self_rater        text          null comment ' 自评人',
    mutual_rater      text          null comment '互评人 json',
    super_rater       text          null comment '上级人 json',
    appoint_rater     text          null comment '指定评分人json',
    peer_rater        text          null comment '自定义同级互评人',
    sub_rater         text          null comment '自定义下级互评人',
    created_user      varchar(50)   null comment '创建用户',
    created_time      datetime      null comment '创建时间',
    updated_user      varchar(50)   null comment '修改用户',
    updated_time      datetime      null comment '修改时间',
    is_deleted        varchar(10)   null,
    version           int default 0 null comment '版本号',
    default_eval      int default 0 null comment '是否默认评分流程'
)
    comment '员工考核表-指标流程' charset = utf8mb4;

create index idx_companyId_tableId
    on emp_table_item_rule (company_id, table_id);

create table emp_table_item_used_field
(
    field_id     varchar(50)      not null comment 'company_kpi_item_custom_field.id',
    company_id   varchar(50)      null comment '公司id',
    table_id     varchar(50)      not null comment '考核表的id,emp_eval_table.id',
    kpi_item_id  varchar(50)      null comment 'emp_table_kpi_item.kpi_item_id',
    name         varchar(50)      null comment '字段名称【复制于company_kpi_item_custom_field】',
    value        varchar(512)     null comment '字段值【复制于company_kpi_item_custom_field】',
    type         int(2) default 1 null comment '字段类型（1：输入框  2：开关 3：下拉选 4：复选 5：img）【复制于company_kpi_item_custom_field】',
    req          int(1) default 1 null comment '是否必填（0：否  1：是）【复制于company_kpi_item_custom_field】',
    status       varchar(20)      null comment '指标字段状态（valid：有效，invalid：无效）【复制于company_kpi_item_custom_field】',
    `show`       int(1) default 1 null comment '是否显示（0：否  1：是）',
    sort         int(5)           null comment '指标字段排序值',
    admin_type   int(1)           null comment '1:系统默认字段  0：自定义字段',
    is_deleted   varchar(10)      null comment '是否删除',
    created_user varchar(50)      null comment '创建用户',
    created_time datetime         null comment '创建时间',
    updated_user varchar(50)      null comment '修改用户',
    updated_time datetime         null comment '修改时间',
    version      int    default 0 null
)
    comment '考核表指标自定义字段使用表' charset = utf8mb4;

create index company_key
    on emp_table_item_used_field (company_id);

create index field_key
    on emp_table_item_used_field (field_id);

create index item_key
    on emp_table_item_used_field (kpi_item_id);

create index table_key
    on emp_table_item_used_field (table_id);

create table emp_table_kpi_item
(
    table_id                varchar(50)                 not null comment '考核表的id,emp_eval_table.id',
    company_id              varchar(50)                 null comment '公司id',
    kpi_type_id             varchar(50)                 null comment '指标类id',
    kpi_item_id             varchar(50)                 null comment '指标项id',
    kpi_type_name           varchar(200)                null comment '指标类名称',
    kpi_type_weight         decimal(10, 2)              null comment '指标类权重',
    kpi_item_name           text                        null comment '指标项名称',
    item_target_value       decimal(18, 2)              null comment '指标项目标值',
    item_finish_value       decimal(18, 2)              null comment '指标项完成值',
    item_unit               varchar(20)                 null comment '指标项单位',
    item_weight             decimal(10, 2)              null comment '指标项权重',
    result_input_type       varchar(50)                 null comment '结果录入类型',
    input_emps              text                        null comment '结果录入人id',
    examine_oper_type       varchar(30)                 null comment '被考核人确认指标时操作类型：增删改',
    item_rule               text                        null comment '考核规则',
    scoring_rule            text                        null comment '计分规则',
    scorer_type             varchar(50)                 null comment '指标评分人类型（按评分流程/指定员工/指定主管）',
    scorer_obj_id           text                        null comment '指定评分人json串',
    item_type               varchar(50)                 null comment '指标项类型（量化/非量化）',
    multiple_reviewers_type varchar(50)                 null comment '多人审核时，and会签，or或签',
    kpi_type_classify       varchar(50)                 null comment '指标类别，为空表示已有类别，custom自定义/plus加分项/subtract减分项',
    plus_limit              decimal(10, 2)              null comment '加分上限',
    subtract_limit          decimal(10, 2)              null comment '减分上限',
    max_extra_score         decimal(10, 2)              null comment '本类别最大加减分上限',
    `order`                 int                         null comment '排序，数字小的排前面',
    item_formula            text                        null comment '指标计算公式',
    threshold_json          text                        null comment '量化指标阈值设置json',
    formula_condition       text                        null comment '公式条件',
    item_field_json         text                        null comment '指标关联的阈值字段',
    is_type_locked          varchar(100)                null comment '类别锁定类型',
    is_okr                  varchar(10)                 null comment '是否OKR类别，true/false',
    type_order              int(10)                     null comment '类别排序',
    reserve_okr_weight      decimal(10, 2)              null comment '预留OKR权重',
    item_score_value        varchar(500)                null comment '指标评分分值',
    input_format            varchar(50)                 null comment '录入格式',
    work_item_finish_value  varchar(2000)               null comment '工作事项完成情况说明',
    item_plan_flag          varchar(10)                 null comment '是否同步了年度指标计划',
    show_target_value       varchar(10)                 null comment '是否展示目标值',
    must_result_input       tinyint(1)  default 0       null comment '指标完成值是否必需录入',
    show_finish_bar         int         default 1       null comment '完成度进度条 默认开启 1=显示,0=不显示',
    manager_level           varchar(10)                 null comment '录入主管等级',
    item_full_score_cfg     varchar(10) default 'false' null comment '自动计算指标得分满分值',
    item_limit_cnt          varchar(255)                null comment '类别指标数量最大最小限制',
    item_finish_value_text  varchar(2000)               null comment '非量化指标完成值文本录入',
    open_okr_score          int         default 0       null comment '指标评分使用的是okr的分数',
    okr_score               decimal(10, 3)              null comment 'okr的原始分数',
    formula_fields          text                        null comment '公式字段列表',
    is_deleted              varchar(10)                 null comment '是否删除',
    version                 int         default 0       null comment '版本号',
    created_user            varchar(50)                 null comment '创建用户',
    created_time            datetime                    null comment '创建时间',
    updated_user            varchar(50)                 null comment '修改用户',
    updated_time            datetime                    null comment '修改时间',
    plus_sub_interval       varchar(50)                 null comment '考核表指标加减分上限',
    item_custom_field_json  text                        null comment '指标自定义字段json',
    ind_level_group_id      bigint                      null comment '指标等级组id=开启等级组,  0=不开等级组',
    formula_type            int(1)      default 1       null comment '公式类型：1=多场景公式、2=高级公式',
    finish_value_audit      text                        null comment '完成值审核人列表',
    item_target_value_text  text                        null comment '指标项文本类型目标值'
)
    comment '员工考核表--关联指标' charset = utf8mb4;

create index idx_companyId_tableId
    on emp_table_kpi_item (company_id, table_id);

create table emp_table_kpi_type
(
    table_id           varchar(50)                  not null comment '考核表的id,emp_eval_table.id',
    company_id         varchar(50)                  not null comment '公司id',
    kpi_type_id        varchar(50)                  null comment '指标类id',
    kpi_type_name      varchar(200)                 null comment '指标类名称',
    kpi_type_weight    decimal(10, 2)               null comment '指标类权重',
    is_okr             varchar(10)                  null comment '是否OKR类别，true/false',
    type_order         int(10)                      null comment '类别排序',
    reserve_okr_weight decimal(10, 2)               null comment '预留OKR权重',
    kpi_type_classify  varchar(30)                  null comment '指标类别，为空表示已有类别，custom自定义/plus加分项/subtract减分项',
    max_extra_score    decimal(10, 2)               null comment '本类别最大加减分上限',
    import_okr_flag    varchar(10)                  null comment '是否已导入OKR',
    locked_items       varchar(100)                 null comment 'is_type_locked 类别锁定类型  ["typeWeight","addIndex","modifyIndex","deleteIndex"]',
    item_limit_cnt     varchar(50)                  null comment '类别指标数量最大最小限制',
    is_deleted         varchar(10)  default 'false' null comment '是否删除',
    created_user       varchar(50)                  null comment '创建用户',
    created_time       datetime                     null comment '创建时间',
    updated_user       varchar(50)                  null comment '修改用户',
    updated_time       datetime                     null comment '修改时间',
    version            int          default 0       null comment '版本号',
    plus_sub_interval  varchar(50)                  null comment '考核表类别加减分上限',
    open_okr_score     int                          null comment '指标评分使用的是okr的分数',
    self_rater         varchar(128) default ''      null comment '自评人',
    super_rater        text                         null comment '上级人 json',
    appoint_rater      text                         null comment '指定评分人json',
    peer_rater         text                         null comment '自定义同级互评人',
    sub_rater          text                         null comment '自定义下级互评人',
    ind_level_group_id bigint                       null comment '指标等级组id=开启等级组,  0=不开等级组',
    finish_value_audit text                         null comment '完成值审核人列表',
    score_opt_type     int          default 2       null comment '# 0:等级[无等级组]indLevelId==null # 1:等级[设等级组] indLevelId=1000234  #2:评分[无选项组] indLevelId==null  # 4:评分[有选项组], indLevelId=1000235 ',
    des                varchar(512)                 null comment '维度描述 20230921增加'
)
    comment '员工的考核表-指标类别' charset = utf8mb4;

create index idx_companyId_tableId
    on emp_table_kpi_type (company_id, table_id);

create table emp_table_operation
(
    id              varchar(50)  not null comment '操作记录id'
        primary key,
    table_id        varchar(50)  not null comment '外键table_id',
    company_id      varchar(50)  not null comment '公司id',
    at_status       varchar(50)  not null comment '修改时所在阶段',
    change_type     int          not null comment '01=1=指标, 10=2=流程,11=3=指标+流程',
    indicator       mediumtext   null comment '指标类修改,type_weight_conf,score_value_conf, _kpi表,_kpi_type表',
    confirm_task    text         null comment '确认任务 对应模板 templAffirmJson:PerfTemplEvaluateAffirm',
    edit_exe_indi   text         null comment '执行中修改指标日志记录  对应模板 templExecuteJson:PerfTemplEvaluateExecute',
    score_conf      text         null comment 'enter_score,s3_self_rater,s3_mutual_rater,s3_super_rater ,score_veiw',
    audit_result    text         null comment '结果校准,从templEvaluateJson:PerfTemplEvaluate 模板评价配置json 单独出来的',
    confirm_result  varchar(512) null comment '结果确认,多个字段合并',
    publish_result  text         null comment '结果公示,多个字段合并',
    appeal_conf     varchar(512) null comment '结果申诉,多个字段合并',
    operation_time  bigint       not null comment '操作时间毫秒ms',
    operator_id     varchar(50)  not null comment '操作人emp_id',
    operator_name   varchar(50)  not null comment '操作人名',
    operator_avatar varchar(225) not null comment '操作人头像',
    created_time    datetime     null comment '记录创建时间',
    admin_type      varchar(25)  null comment '操作人管理员类型/admin/child',
    base_info       text         null comment '基本信息'
)
    comment '员工考核表修改记录' charset = utf8mb4;

create index idx_companyId_tableId
    on emp_table_operation (company_id, table_id);

create index idx_operation_time
    on emp_table_operation (operation_time);

create table emp_table_type_eval_rule
(
    id            varchar(50)             not null
        primary key,
    company_id    varchar(50)             null comment '公司id',
    emp_id        varchar(50)             null comment '人员id',
    kpi_type_id   varchar(50)             null comment '指标类id',
    self_rater    varchar(128) default '' null comment '自评人',
    mutual_rater  text                    null comment '互评人 json',
    super_rater   text                    null comment '上级人 json',
    appoint_rater text                    null comment '指定评分人json',
    peer_rater    text                    null comment '自定义同级互评人',
    sub_rater     text                    null comment '自定义下级互评人',
    created_user  varchar(50)             null comment '创建用户',
    created_time  datetime                null comment '创建时间',
    updated_user  varchar(50)             null comment '修改用户',
    updated_time  datetime                null comment '修改时间',
    is_deleted    varchar(10)             null comment '是否删除',
    version       int          default 0  null comment '版本号'
)
    comment '考核表-考核维度自定义流程' charset = utf8mb4;

create index idx_companyId
    on emp_table_type_eval_rule (company_id);

create index idx_empId
    on emp_table_type_eval_rule (emp_id);

create table emp_table_type_used_field
(
    field_id     varchar(50)      not null comment 'company_kpi_item_custom_field.id',
    company_id   varchar(50)      null comment '公司id',
    table_id     varchar(50)      not null comment '考核表的id,emp_eval_table.id',
    kpi_type_id  varchar(50)      null comment 'emp_table_kpi_type.kpi_type_id',
    name         varchar(50)      null comment '字段名称【复制于company_kpi_item_custom_field】',
    type         int(2) default 1 null comment '字段类型（1：输入框  2：开关 3：下拉选 4：复选 5：img）【复制于company_kpi_item_custom_field】',
    req          int(1) default 1 null comment '是否必填（0：否  1：是）【复制于company_kpi_item_custom_field】',
    status       varchar(20)      null comment '指标字段状态（valid：有效，invalid：无效）【复制于company_kpi_item_custom_field】',
    `show`       int(1) default 1 null comment '是否显示（0：否  1：是）',
    sort         int(5)           null comment '指标字段排序值',
    admin_type   int(1)           null comment '1:系统默认字段  0：自定义字段',
    is_deleted   varchar(10)      null comment '是否删除',
    created_user varchar(50)      null comment '创建用户',
    created_time datetime         null comment '创建时间',
    updated_user varchar(50)      null comment '修改用户',
    updated_time datetime         null comment '修改时间',
    version      int    default 0 null
)
    comment '考核表维度自定义字段使用表' charset = utf8mb4;

create index company_key
    on emp_table_type_used_field (company_id);

create index field_key
    on emp_table_type_used_field (field_id);

create index table_key
    on emp_table_type_used_field (table_id);

create index type_key
    on emp_table_type_used_field (kpi_type_id);

create table emp_use_category_item
(
    category_id  varchar(50)   not null comment '类别ID',
    company_id   varchar(50)   null comment '公司id',
    emp_id       varchar(50)   null comment '为0表示所有',
    is_deleted   varchar(10)   null comment '是否删除',
    created_user varchar(50)   null comment '创建用户',
    created_time datetime      null comment '创建时间',
    updated_user varchar(50)   null comment '修改用户',
    updated_time datetime      null comment '修改时间',
    version      int default 0 null
)
    comment '员工引用的指标，通过类别权限控制' charset = utf8mb4;

create index category_key
    on emp_use_category_item (category_id);

create index company_key
    on emp_use_category_item (company_id);

create index ref_key
    on emp_use_category_item (emp_id);

create table employee_base_info
(
    id              varchar(50)                      null,
    employee_id     varchar(50)                      not null comment '主键员工ID'
        primary key,
    company_id      varchar(50)                      null comment '公司ID',
    org_id          varchar(50)                      null comment '部门ID',
    account_id      varchar(50)                      null comment '账号ID',
    name            varchar(128) collate utf8mb4_bin null comment '姓名',
    nickname        varchar(128) charset utf8        null comment '花名',
    sex             varchar(10) charset utf8         null comment '性别',
    mobile          varchar(200) charset utf8        null,
    id_type         varchar(50)                      null comment '证件类型',
    id_num          varchar(50)                      null comment '证件号码',
    type            varchar(50)                      null comment '员工类型（正式-regular；试用-probation；实习-practice；兼职-parttimer）',
    status          varchar(50)                      null comment '员工状态（在职-on_the_job；待离职-pending_leave；已离职-leave）',
    entry_date      date                             null comment '入职日期',
    avatar          varchar(256) charset utf8        null comment '头像链接地址',
    birthday        date                             null comment '公历出生日期',
    create_id       varchar(50)                      null comment '创建人ID',
    created_time    datetime(6)                      null comment '创建时间',
    updated_time    datetime(6)                      null comment '更新时间',
    name_chinese    varchar(256)                     null comment '查询关键字',
    hierarchy       varchar(128) charset utf8        null comment '职级',
    nation          varchar(50) charset utf8         null comment '民族',
    native_place    varchar(128) charset utf8        null comment '籍贯',
    political_party varchar(32) charset utf8         null comment '政治面貌',
    house_reg_type  varchar(32) charset utf8         null comment '户籍性质',
    house_reg_addr  varchar(300) charset utf8        null comment '户籍地址',
    living_addr     varchar(300) charset utf8        null comment '现住地址',
    office_addr     varchar(300) charset utf8        null comment '办公地址',
    highest_edu     varchar(64) charset utf8         null comment '最高学历',
    marital_status  varchar(32) charset utf8         null comment '婚姻状态',
    child_status    varchar(32) charset utf8         null comment '子女状态',
    linkman_name    varchar(100) charset utf8        null comment '联系人姓名',
    linkman_type    varchar(50) charset utf8         null comment '联系人关系',
    linkman_tel     varchar(100)                     null comment '联系人电话',
    is_delete       varchar(10) default ''           null comment '删除标识；否，是',
    source          varchar(30)                      null comment '多语言字段',
    photo           varchar(255)                     null comment '照片',
    memo            varchar(255)                     null comment '备注',
    qy_user_id      varchar(50)                      null comment '企业微信用户id',
    ding_user_id    varchar(50)                      null comment '钉钉用户id',
    we_user_id      varchar(50)                      null comment 'welink用户id',
    open_id         varchar(255)                     null comment '第三方（钉钉，企微等）平台的open id',
    union_id        varchar(255)                     null comment '第三方（钉钉，企微等）平台的union id',
    state_code      varchar(10)                      null comment '手机号对应的国家号',
    mobile_num      varchar(20)                      null comment '用户的手机号,需要在开发者后台申请',
    email           varchar(50)                      null comment '用户的个人邮箱',
    locale          varchar(30)                      null comment '多语言的语言代码，用中划线',
    ding_role_name  varchar(50)                      null comment '钉钉角色名称',
    ding_manager_id varchar(255)                     null comment '钉钉直属主管id',
    version         int         default 0            null comment '新版 1=旧版，2=新版',
    last_login_time datetime                         null,
    jobnumber       varchar(50) default ''           null comment '工号',
    post            varchar(50) default ''           null comment '岗位'
)
    comment '员工' charset = utf8mb4;

create index company_emp
    on employee_base_info (employee_id, company_id);

create index idx_company_status_delete
    on employee_base_info (company_id);

create index idx_companyid_isdelete
    on employee_base_info (company_id, is_delete);

create index idx_ebi_cid
    on employee_base_info (status);

create index idx_ebi_del
    on employee_base_info (is_delete);

create table employee_base_info_copy
(
    id              varchar(50)                      null,
    employee_id     varchar(50)                      not null comment '主键员工ID'
        primary key,
    company_id      varchar(50)                      null comment '公司ID',
    org_id          varchar(50)                      null comment '部门ID',
    account_id      varchar(50)                      null comment '账号ID',
    name            varchar(128) collate utf8mb4_bin null comment '姓名',
    nickname        varchar(128) charset utf8        null comment '花名',
    sex             varchar(10) charset utf8         null comment '性别',
    mobile          varchar(200) charset utf8        null,
    id_type         varchar(50)                      null comment '证件类型',
    id_num          varchar(50)                      null comment '证件号码',
    type            varchar(50)                      null comment '员工类型（正式-regular；试用-probation；实习-practice；兼职-parttimer）',
    status          varchar(50)                      null comment '员工状态（在职-on_the_job；待离职-pending_leave；已离职-leave）',
    entry_date      date                             null comment '入职日期',
    avatar          varchar(256) charset utf8        null comment '头像链接地址',
    birthday        date                             null comment '公历出生日期',
    create_id       varchar(50)                      null comment '创建人ID',
    created_time    datetime(6)                      null comment '创建时间',
    updated_time    datetime(6)                      null comment '更新时间',
    name_chinese    varchar(256)                     null comment '查询关键字',
    hierarchy       varchar(128) charset utf8        null comment '职级',
    nation          varchar(50) charset utf8         null comment '民族',
    native_place    varchar(128) charset utf8        null comment '籍贯',
    political_party varchar(32) charset utf8         null comment '政治面貌',
    house_reg_type  varchar(32) charset utf8         null comment '户籍性质',
    house_reg_addr  varchar(300) charset utf8        null comment '户籍地址',
    living_addr     varchar(300) charset utf8        null comment '现住地址',
    office_addr     varchar(300) charset utf8        null comment '办公地址',
    highest_edu     varchar(64) charset utf8         null comment '最高学历',
    marital_status  varchar(32) charset utf8         null comment '婚姻状态',
    child_status    varchar(32) charset utf8         null comment '子女状态',
    linkman_name    varchar(100) charset utf8        null comment '联系人姓名',
    linkman_type    varchar(50) charset utf8         null comment '联系人关系',
    linkman_tel     varchar(100)                     null comment '联系人电话',
    is_delete       varchar(10) default ''           null comment '删除标识；否，是',
    source          varchar(30)                      null comment '多语言字段',
    photo           varchar(255)                     null comment '照片',
    memo            varchar(255)                     null comment '备注',
    qy_user_id      varchar(50)                      null comment '企业微信用户id',
    ding_user_id    varchar(50)                      null comment '钉钉用户id',
    we_user_id      varchar(50)                      null comment 'welink用户id',
    locale          varchar(30)                      null comment '多语言的语言代码，用中划线',
    ding_role_name  varchar(50)                      null comment '钉钉角色名称',
    ding_manager_id varchar(255)                     null comment '钉钉直属主管id',
    constraint unique_ding_id
        unique (company_id, is_delete, ding_user_id)
)
    comment '员工' charset = utf8mb4;

create index company_emp
    on employee_base_info_copy (employee_id, company_id);

create index idx_company_status_delete
    on employee_base_info_copy (company_id);

create index idx_companyid_isdelete
    on employee_base_info_copy (company_id, is_delete);

create index idx_ebi_cid
    on employee_base_info_copy (status);

create index idx_ebi_del
    on employee_base_info_copy (is_delete);

create table employee_base_info_yuanxin
(
    id              varchar(50)                      null,
    employee_id     varchar(50)                      not null comment '主键员工ID'
        primary key,
    company_id      varchar(50)                      null comment '公司ID',
    org_id          varchar(50)                      null comment '部门ID',
    account_id      varchar(50)                      null comment '账号ID',
    name            varchar(128) collate utf8mb4_bin null comment '姓名',
    nickname        varchar(128) charset utf8        null comment '花名',
    sex             varchar(10) charset utf8         null comment '性别',
    mobile          varchar(200) charset utf8        null,
    id_type         varchar(50)                      null comment '证件类型',
    id_num          varchar(50)                      null comment '证件号码',
    type            varchar(50)                      null comment '员工类型（正式-regular；试用-probation；实习-practice；兼职-parttimer）',
    status          varchar(50)                      null comment '员工状态（在职-on_the_job；待离职-pending_leave；已离职-leave）',
    entry_date      date                             null comment '入职日期',
    avatar          varchar(256) charset utf8        null comment '头像链接地址',
    birthday        date                             null comment '公历出生日期',
    create_id       varchar(50)                      null comment '创建人ID',
    created_time    datetime(6)                      null comment '创建时间',
    updated_time    datetime(6)                      null comment '更新时间',
    name_chinese    varchar(256)                     null comment '查询关键字',
    hierarchy       varchar(128) charset utf8        null comment '职级',
    nation          varchar(50) charset utf8         null comment '民族',
    native_place    varchar(128) charset utf8        null comment '籍贯',
    political_party varchar(32) charset utf8         null comment '政治面貌',
    house_reg_type  varchar(32) charset utf8         null comment '户籍性质',
    house_reg_addr  varchar(300) charset utf8        null comment '户籍地址',
    living_addr     varchar(300) charset utf8        null comment '现住地址',
    office_addr     varchar(300) charset utf8        null comment '办公地址',
    highest_edu     varchar(64) charset utf8         null comment '最高学历',
    marital_status  varchar(32) charset utf8         null comment '婚姻状态',
    child_status    varchar(32) charset utf8         null comment '子女状态',
    linkman_name    varchar(100) charset utf8        null comment '联系人姓名',
    linkman_type    varchar(50) charset utf8         null comment '联系人关系',
    linkman_tel     varchar(100)                     null comment '联系人电话',
    is_delete       varchar(10) default ''           null comment '删除标识；否，是',
    source          varchar(30)                      null comment '多语言字段',
    photo           varchar(255)                     null comment '照片',
    memo            varchar(255)                     null comment '备注',
    qy_user_id      varchar(50)                      null comment '企业微信用户id',
    ding_user_id    varchar(50)                      null comment '钉钉用户id',
    we_user_id      varchar(50)                      null comment 'welink用户id',
    open_id         varchar(255)                     null comment '第三方（钉钉，企微等）平台的open id',
    union_id        varchar(255)                     null comment '第三方（钉钉，企微等）平台的union id',
    state_code      varchar(10)                      null comment '手机号对应的国家号',
    mobile_num      varchar(20)                      null comment '用户的手机号,需要在开发者后台申请',
    email           varchar(50)                      null comment '用户的个人邮箱',
    locale          varchar(30)                      null comment '多语言的语言代码，用中划线',
    ding_role_name  varchar(50)                      null comment '钉钉角色名称',
    ding_manager_id varchar(255)                     null comment '钉钉直属主管id',
    version         int         default 0            null comment '新版 1=旧版，2=新版',
    last_login_time datetime                         null,
    jobnumber       varchar(50) default ''           null comment '工号',
    post            varchar(50) default ''           null comment '岗位'
)
    comment '员工' charset = utf8mb4;

create index company_emp
    on employee_base_info_yuanxin (employee_id, company_id);

create index idx_company_status_delete
    on employee_base_info_yuanxin (company_id);

create index idx_companyid_isdelete
    on employee_base_info_yuanxin (company_id, is_delete);

create index idx_ebi_cid
    on employee_base_info_yuanxin (status);

create index idx_ebi_del
    on employee_base_info_yuanxin (is_delete);

create table employee_syn
(
    id              varchar(50)                      null,
    employee_id     varchar(50)                      null comment '主键员工ID',
    company_id      varchar(50)                      null comment '公司ID',
    org_id          varchar(50)                      not null comment '部门ID',
    account_id      varchar(50)                      null comment '账号ID',
    name            varchar(128) collate utf8mb4_bin null comment '姓名',
    nickname        varchar(128) charset utf8        null comment '花名',
    sex             varchar(10) charset utf8         null comment '性别',
    mobile          varchar(200) charset utf8        null,
    type            varchar(20)                      null comment '员工类型（正式-regular；试用-probation；实习-practice；兼职-parttimer）',
    status          varchar(50)                      null comment '员工状态（在职-on_the_job；待离职-pending_leave；已离职-leave）',
    entry_date      date                             null comment '入职日期',
    avatar          varchar(256) charset utf8        null comment '头像链接地址',
    birthday        date                             null comment '公历出生日期',
    created_time    datetime(6)                      null comment '创建时间',
    updated_time    datetime(6)                      null comment '更新时间',
    name_chinese    varchar(256)                     null comment '查询关键字',
    is_delete       varchar(10) default 'false'      null comment '删除标识；否，是',
    ding_user_id    varchar(50)                      null comment '钉钉用户id',
    we_user_id      varchar(50)                      null comment 'welink用户id',
    open_id         varchar(255)                     null comment '第三方（钉钉，企微等）平台的open id',
    union_id        varchar(255)                     null comment '第三方（钉钉，企微等）平台的union id',
    state_code      varchar(10)                      null comment '手机号对应的国家号',
    mobile_num      varchar(20)                      null comment '用户的手机号,需要在开发者后台申请',
    email           varchar(50)                      null comment '用户的个人邮箱',
    ding_role_name  varchar(50)                      null comment '钉钉角色名称',
    ding_manager_id varchar(255)                     null comment '钉钉直属主管id',
    jobnumber       varchar(50)                      null comment '工号',
    post            varchar(50)                      null comment '岗位',
    version         int         default 0            null comment '版本号0',
    last_login_time datetime                         null,
    push_id         bigint                           not null comment 'open_sync_biz_data.id 钉钉事件id',
    constraint uk_orgId_userId
        unique (company_id, ding_user_id, org_id, push_id)
)
    comment '员工同步临时表,部门+员工' charset = utf8mb4;

create index idx_pushId
    on employee_syn (push_id);

create index idx_userId
    on employee_syn (ding_user_id);

create table enter_on_msg_log
(
    id                   varchar(50) not null
        primary key,
    company_id           varchar(50) null,
    created_time         varchar(50) null,
    update_user_id       varchar(50) null,
    before_change_status int(1)      null comment '改变前的状态',
    after_change_status  int(1)      null comment '改变后的状态'
);

create table eval_kpi_input_bak
(
    id                  int                         not null
        primary key,
    company_id          varchar(50)                 not null comment '公司id',
    task_user_id        varchar(50)                 not null comment 'task user 表的id',
    kpi_item_id         varchar(50)                 null,
    result_input_type   varchar(50)                 null,
    result_input_emp_id text                        null,
    is_deleted          varchar(10) default 'false' null comment '是否删除',
    created_user        varchar(50)                 null comment '创建用户',
    created_time        datetime                    null comment '创建时间',
    updated_user        varchar(50)                 null comment '修改用户',
    updated_time        datetime                    null comment '修改时间',
    version             int         default 0       null comment '版本号'
)
    comment '备份录入人转交前的录入人和录入类型，用来重置回复录入人' charset = utf8mb4;

create index company_id
    on eval_kpi_input_bak (company_id);

create index task_user_id
    on eval_kpi_input_bak (task_user_id);

create table eval_scorer_node
(
    id             varchar(50)                 not null
        primary key,
    company_id     varchar(50)                 not null comment '公司id',
    task_user_id   varchar(50)                 not null comment '员工任务id',
    scorer_id      varchar(50)                 null comment '评分人id',
    scorer_name    varchar(50)                 null comment '评分人姓名',
    scorer_type    varchar(30)                 null comment '评分环节',
    approval_order int(2)                      null comment '节点',
    score_status   int(2)      default 0       null comment '是否已评分',
    is_deleted     varchar(10) default 'false' null comment '是否删除',
    created_user   varchar(50)                 null comment '创建用户',
    created_time   datetime                    null comment '创建时间',
    updated_user   varchar(50)                 null comment '修改用户',
    updated_time   datetime                    null comment '修改时间',
    version        int         default 0       null comment '版本号'
)
    comment '评分-按人员重置：当前仅用于按人员重置到评分环节' charset = utf8mb4;

create table global_sys_param
(
    id           varchar(50)             not null
        primary key,
    company_id   varchar(50) default '0' null comment '企业id,固定的id=0',
    param_name   varchar(50)             null comment '参数名称',
    param_value  varchar(500)            null comment '参数值',
    expires_in   int                     null comment '有效时间，单位：秒',
    created_time datetime                null,
    updated_time datetime                null,
    constraint uk_param_name
        unique (param_name)
)
    comment '系统参数表' charset = utf8mb4;

create table grade_step
(
    id           int auto_increment
        primary key,
    company_id   varchar(50)                 not null,
    name         varchar(50)                 not null comment '等阶名字,多个等阶组成套等级规则',
    sort         int                         null,
    created_time date                        null,
    created_user varchar(50) default ''      null,
    updated_time date                        null,
    updated_user varchar(50) default ''      null,
    is_deleted   varchar(5)  default 'false' null,
    version      int         default 0       null comment '版本号',
    level_id     varchar(50)                 null comment '企微测试添加，测试完之后会drop这个字段'
);

create index idx_company_id
    on grade_step (company_id);

create table hrm_solution_order
(
    id            varchar(20)                      null comment 'id',
    company_id    varchar(50)                      null comment '公司ID',
    solution_type varchar(50) default 'onboarding' not null comment '解决方案类型:onboarding=新人流程',
    name          varchar(50) default ''           not null comment '解决方案的名字：新人入职解决方案',
    max_cnt       int                              null comment '最大可用数',
    used_cnt      int                              null comment '最大可用数',
    is_open       int         default 0            null comment '菜单是否打开 0=不打开, 1=打开',
    push_id       bigint                           not null comment '创建此单据的事件id',
    created_time  datetime                         null,
    updated_time  datetime                         null,
    version       int         default 0            not null comment '版本号'
)
    comment '企业购的买人事解决方案' charset = utf8mb4;

create index idx_companyId_type
    on hrm_solution_order (company_id, solution_type);

create table ind_level_group
(
    id           bigint auto_increment
        primary key,
    name         varchar(50)   null comment '等级组名',
    des          varchar(256)  null comment '等级组描述',
    company_id   varchar(50)   null comment '公司id',
    levels       text          null comment '等级组列表',
    is_deleted   varchar(10)   null comment '是否删除',
    created_user varchar(50)   null comment '创建用户',
    created_time datetime      null comment '创建时间',
    updated_user varchar(50)   null comment '修改用户',
    updated_time datetime      null comment '修改时间',
    version      int default 0 null comment '数据据版本号',
    sys_def      int default 0 null comment '系统带的组=1, 用户创建的=0 ',
    type         int(1)        null comment '带分数类型选项式 = 1 ,不带分数的等级选项式=0或null'
)
    comment '指标等级组' charset = utf8mb4;

create index idx_companyId
    on ind_level_group (company_id);

create table ind_level_used_by
(
    id                 bigint auto_increment
        primary key,
    ind_level_group_id bigint        not null comment '指标等级组id',
    used_by_id         varchar(50)   null comment '使用者的id',
    used_by_name       varchar(500)  null comment '使用者的名字',
    used_by_type       varchar(50)   null comment '使用者的类型, table=考核表, temp=模板',
    company_id         varchar(50)   null comment '公司id',
    used_by_conent     text          null comment '使用具体的地方',
    is_deleted         varchar(10)   null comment '是否删除',
    created_user       varchar(50)   null comment '创建用户',
    created_time       datetime      null comment '创建时间',
    updated_user       varchar(50)   null comment '修改用户',
    updated_time       datetime      null comment '修改时间',
    version            int default 0 null comment '数据据版本号'
)
    comment '指标等级组使用情况条目,用于回显于设置页面' charset = utf8mb4;

create index idx_companyId_groupId
    on ind_level_used_by (company_id, ind_level_group_id);

create index idx_companyId_userById
    on ind_level_used_by (company_id, used_by_type, used_by_id);

create table kpi_nemp_order
(
    company_id                varchar(50)   null comment '公司ID',
    corp_id                   varchar(50)   null comment '三方平台id',
    order_id                  varchar(50)   null comment '订单号',
    present_rel_main_order_id varchar(50)   null comment '满赠订单关联的付费主订单ID',
    goods_code                varchar(50)   null comment '下单组织购买的官方应用商品码',
    goods_name                varchar(50)   null comment '下单组织购买的官方应用商品名称',
    item_code                 varchar(50)   null comment '下单组织购买的官方应用规格码',
    item_name                 varchar(50)   null comment '下单组织购买的官方应用规格名称',
    order_type                varchar(50)   null comment 'BUY=新购,RENEW=续费,UPGRADE=升级,RENEW_UPGRADE=续费升配,RENEW_DEGRADE=续费降配',
    order_status              int           null comment '订单状态 1=新购,2=续购,4=送,16=退',
    service_start_time        bigint        not null comment '订单有效-开始时间',
    service_stop_time         bigint        not null comment '订单过期时间',
    paidtime                  bigint        not null comment '支付时间',
    order_label               int           null comment '订单标记, 0=普通订单, 1=满赠订单',
    cnt                       int           null comment '购的新人可用次数',
    used_cnt                  int           null comment '已用次数',
    push_id                   bigint        not null comment '创建此单据的事件id',
    created_time              datetime      null,
    updated_time              datetime      null,
    version                   int default 0 not null comment '版本号'
)
    comment '企业的直通车额度订单' charset = utf8mb4;

create index idx_companyId
    on kpi_nemp_order (company_id);

create index idx_extCorpId
    on kpi_nemp_order (corp_id);

create table kpi_nemp_used_log
(
    id           varchar(50)   null comment '记录id',
    company_id   varchar(50)   null comment '公司ID',
    spend_cnt    int           null comment '购的新人可用次数',
    order_id     varchar(50)   null comment '订单号',
    created_time datetime      null,
    updated_time datetime      null,
    version      int default 0 not null comment '版本号',
    use_emp_id   varchar(50)   null comment '使用者id'
)
    comment '直通车额度消耗日志' charset = utf8mb4;

create index idx_companyId
    on kpi_nemp_used_log (company_id);

create index idx_use_emp_id
    on kpi_nemp_used_log (use_emp_id);

create table last_cycle_task_conf
(
    tenant_id                varchar(50)   not null comment '企业id',
    temp_id                  varchar(50)   not null comment '模板id',
    enable                   int default 1 null comment '1=可用,0=失效不可用',
    target_value             longtext      null comment '目标值配置',
    finish_value_input_user  mediumtext    null comment '完成值录入人配置',
    task_score_rule          mediumtext    null comment '评分人配置',
    customer_item_score_rule longtext      null comment '自定义指定评分人',
    created_user             varchar(50)   null,
    created_time             datetime      null,
    updated_time             datetime      null,
    version                  int default 0 null comment '版本号'
)
    comment '最近一次自定义的模板发起时的配置' charset = utf8mb4;

create index k_created_user
    on last_cycle_task_conf (created_user);

create index k_temp_id
    on last_cycle_task_conf (temp_id);

create table license_code
(
    plat        int         default 10 null comment '平台 wecom=10,钉钉=20',
    code        varchar(50)            null comment 'code',
    status      int         default 1  null comment '可用=1, 占用中=10,已激活=2, 已失效=3',
    corp_id     varchar(50)            not null comment '外部企业id',
    order_id    varchar(100)           not null comment '订单id，企微生成',
    ext_user_id varchar(50) default '' null comment '外部用户id',
    lock_id     varchar(50)            not null comment '锁定id',
    expire_time bigint                 null comment '过期时间',
    code_type   int                    not null comment '帐号类型：1:基础帐号，2:互通帐号(无),',
    months      int                    null comment '购买时长，按月计',
    gmt_create  bigint                 not null comment '创建时间',
    gmt_update  bigint                 not null comment '更新时间',
    version     int         default 0  null comment '版本号'
)
    comment '激活码' charset = utf8mb4;

create index idx_code
    on license_code (corp_id, code);

create index idx_ext_user_id
    on license_code (corp_id, ext_user_id);

create table license_code_temp
(
    plat        int         default 10 null comment '平台 wecom=10,钉钉=20',
    code        varchar(50)            null comment 'code',
    status      int         default 1  null comment '可用=1, 占用中=10,已激活=2, 已失效=3',
    corp_id     varchar(50)            not null comment '外部企业id',
    order_id    varchar(100)           not null comment '订单id，企微生成',
    ext_user_id varchar(50) default '' null comment '外部用户id',
    lock_id     varchar(50)            not null comment '锁定id',
    expire_time bigint                 null comment '过期时间',
    code_type   int                    not null comment '帐号类型：1:基础帐号，2:互通帐号(无),',
    months      int                    null comment '购买时长，按月计',
    gmt_create  bigint                 not null comment '创建时间',
    gmt_update  bigint                 not null comment '更新时间',
    version     int         default 0  null comment '版本号'
)
    comment '激活码' charset = utf8mb4;

create index idx_code
    on license_code_temp (corp_id, code);

create index idx_ext_user_id
    on license_code_temp (corp_id, ext_user_id);

create table license_order
(
    order_id               varchar(100)   not null comment '订单id，企微生成'
        primary key,
    plat                   int default 10 null comment '平台 wecom=10,钉钉=20',
    corp_id                varchar(50)    not null comment '外部企业id',
    order_type             int            null comment '订单类型，1：购买帐号，2：续期帐号 5:历史企业迁移订单',
    order_status           int            null comment '0：待支付，1：已支付，2：未支付，订单已关闭，3：未支付，订单已过期，4：申请退款中，5：退款成功，6：退款被拒绝',
    price                  bigint         null comment '订单金额，单位分',
    buyer_userid           varchar(50)    null,
    status                 int            null comment '处理状态 0：初始化，1：开始同步，2：同步完成，3：处理异常',
    months                 int            null comment '购买时长，按月计',
    pay_time               bigint         null comment '支付时间。迁移订单不返回该字段,企微返回的是秒',
    base_count             int            null comment '基础账号数量',
    external_contact_count int            null comment '互通账号数量',
    gmt_create             bigint         not null comment '创建时间',
    gmt_update             bigint         not null comment '更新时间',
    version                int default 0  null comment '版本号'
)
    comment '激活码订单' charset = utf8mb4;

create index idx_corp_id
    on license_order (corp_id);

create table media
(
    id           varchar(50)  not null
        primary key,
    media_id     varchar(255) not null comment '媒体文件id',
    media_type   varchar(50)  null comment '媒体文件类型',
    scene        varchar(20)  null comment '使用场景',
    created_time datetime     null,
    updated_time datetime     null
)
    comment '媒体文件';

create table member_admission_data
(
    id               bigint       null,
    section_name     varchar(255) null comment '款台名称',
    entry_date       date         null comment '入园日期',
    entry_time       time         null comment '入园时间',
    entry_type       varchar(50)  null comment '项目类型（入园类型）',
    member_card_type varchar(100) null comment '会员卡类型',
    taget_no         varchar(50)  null comment '会员卡编号（卡号）',
    admission_num    int          null comment '入园次数',
    member_name      varchar(255) null comment '会员姓名',
    id_card          varchar(255) null comment '身份证号',
    phone_num        varchar(20)  null comment '手机号码',
    create_user      bigint       null,
    created_time     datetime     null,
    update_user      bigint       null,
    updated_time     datetime     null,
    member_id        bigint       null,
    org_id           bigint       null,
    scenic_id        bigint       null,
    total            varchar(255) null comment '总计'
)
    collate = utf8_bin;

create table my_id
(
    id         varchar(50) not null
        primary key,
    task_id    varchar(50) null,
    company_id varchar(50) null,
    emp_id     varchar(50) null
);

create table new_emp
(
    emp_id          varchar(50)   null comment '员工id',
    created_time    datetime      null,
    updated_time    datetime      null,
    version         int default 0 null comment '版本号',
    evaled          int default 0 null comment '是否已考核0',
    company_id      varchar(50)   not null comment '企业id',
    ext_user_id     varchar(50)   not null comment '钉钉员工id',
    ext_instance_id varchar(50)   not null comment '钉钉流程实例id'
)
    comment '新入职员工' charset = utf8mb4;

create index k_tenantId_empId
    on new_emp (emp_id);

create table ob_audit_form
(
    id           varchar(50)   not null
        primary key,
    company_id   varchar(50)   null,
    audit_scene  int(5)        null comment '审批场景：1，目标新增；2，目标修改；3，目标删除；4，目标更新',
    audit_no     varchar(50)   null comment '审批单号',
    audit_status int(5)        null comment '审批状态：0，待处理；1，处理中；2，已处理',
    audit_level  int(5)        null comment '审批人在流程中层级',
    audit_emp_id varchar(50)   null comment '审批人',
    audit_result tinyint(1)    null comment '审批结果：1，通过；2，驳回',
    audit_memo   varchar(2000) null comment '审批意见',
    audit_order  int(5)        null comment '审批人排序',
    created_time datetime      null,
    updated_time datetime      null
)
    comment '审批记录';

create index idx_ano
    on ob_audit_form (audit_no);

create index idx_cid
    on ob_audit_form (company_id);

create table ob_audit_process
(
    id           varchar(50)               not null
        primary key,
    company_id   varchar(50)               null,
    audit_no     varchar(50)               null comment '审批单号',
    rule_name    varchar(100)              null comment '规则名称',
    audit_emp    varchar(2000) default '1' null comment '审批人',
    audit_mode   tinyint(1)    default 1   null comment '审核方式：1，或签；2，会签',
    audit_level  int(5)                    null comment '审批节点',
    audit_status tinyint(1)    default 0   null comment '0，待审批；1，审批中；2，已审批',
    created_time datetime                  null,
    updated_time datetime                  null
)
    comment '审批流程';

create index idx_ano
    on ob_audit_process (audit_no);

create index idx_cid
    on ob_audit_process (company_id);

create table ob_audit_rule
(
    id            varchar(50)          not null
        primary key,
    company_id    varchar(50)          null,
    plan_id       varchar(50)          null comment '计划id',
    rule_name     varchar(100)         null comment '规则名称',
    range_type    tinyint(1) default 1 null comment '应用范围类型（1，全公司；2，自定义）',
    range_value   varchar(3000)        null comment '应用范围值,类型为自定义时必传,值为逗号分隔的字符串',
    audit_process mediumtext           null comment '审核流程：[{"name":"","type":"head/manager/preLevel/custom","value":1/人员部门id字符串}',
    rule_status   tinyint(1) default 1 null comment '规则状态：0，已删除；1，有效',
    audit_mode    tinyint(1) default 1 null comment '审核方式：1，或签；2，会签',
    order_num     int(5)               null comment '排序',
    is_sys        tinyint(1) default 0 null comment '是否系统默认（0，否；1，是）',
    created_by    varchar(50)          null,
    created_time  datetime             null,
    updated_by    varchar(50)          null,
    updated_time  datetime             null
)
    comment '审批规则';

create index idx_cid
    on ob_audit_rule (company_id);

create index idx_pid
    on ob_audit_rule (plan_id);

create table ob_dimension
(
    id               varchar(50)   not null
        primary key,
    company_id       varchar(50)   null,
    dimension_code   varchar(50)   null comment '指标编码',
    dimension_name   varchar(200)  null comment '维度名称',
    dimension_level  int(5)        null comment '维度层级',
    dimension_path   varchar(3000) null comment '维度路径',
    parent_code      varchar(50)   null comment '父级维度code',
    had_child        tinyint(1)    null comment '是否存在下一级（0，否；1，是）',
    dimension_status tinyint(1)    null comment '维度状态：0，无效；1，有效',
    group_code       varchar(50)   null comment '维度组code',
    version          int(10)       null comment '版本',
    created_by       varchar(50)   null,
    created_time     datetime      null,
    updated_by       varchar(50)   null,
    updated_time     datetime      null
)
    comment '维度配置';

create index idx_cid
    on ob_dimension (company_id);

create index idx_dcode
    on ob_dimension (dimension_code);

create table ob_dimension_group
(
    id           varchar(50)      not null
        primary key,
    company_id   varchar(50)      null,
    group_type   int(5) default 1 null comment '维度组类型：1，自定义；2，组织结构',
    group_code   varchar(50)      null comment '维度组编码',
    group_name   varchar(200)     null comment '维度组名称',
    group_status tinyint(1)       null comment '维度组状态：0，删除；1，有效',
    version      int(10)          null comment '版本',
    created_by   varchar(50)      null,
    created_time datetime         null,
    updated_by   varchar(50)      null,
    updated_time datetime         null
)
    comment '维度组';

create index idx_cid
    on ob_dimension_group (company_id);

create index idx_dcode
    on ob_dimension_group (group_code);

create table ob_dimension_operate_log
(
    id             varchar(50)   not null
        primary key,
    company_id     varchar(50)   null,
    oper_type      int(5)        null comment '操作类型：1，新增；2，修改；3，删除',
    dimension_id   varchar(50)   null comment '维度id',
    dimension_code varchar(50)   null comment '维度code',
    oper_content   varchar(2000) null comment '操作内容',
    created_by     varchar(50)   null,
    created_time   datetime      null,
    updated_time   datetime      null
)
    comment '维度操作记录';

create index idx_cid
    on ob_dimension_operate_log (company_id);

create index idx_dcode
    on ob_dimension_operate_log (dimension_code);

create index idx_did
    on ob_dimension_operate_log (dimension_id);

create table ob_index
(
    id           varchar(50)          not null
        primary key,
    company_id   varchar(50)          null,
    index_code   varchar(50)          null comment '指标编码',
    index_name   varchar(50)          null comment '指标名称',
    index_unit   varchar(20)          null comment '指标单位',
    index_format int(5)               null comment '指标格式：1，整数；2，保留1位小数；3，保留2位小数；4，保留3位小数；5，千分位；6，千分位（小数点）；7，百分比；8，百分比（小数点）',
    parent_code  varchar(50)          null comment '上级指标',
    index_path   varchar(1000)        null comment '指标路径',
    index_level  int(5)               null comment '指标等级',
    index_remark varchar(200)         null comment '指标备注',
    group_code   varchar(50)          null comment '指标组code',
    index_type   tinyint(1)           null comment '指标类型（1，填入型；2，计算型）',
    index_status tinyint(1) default 1 null comment '指标状态（0，已删除；1，有效）',
    had_child    tinyint(1) default 0 null comment '是否存在下级（0，否；1，是）',
    had_formula  tinyint(1) default 0 null comment '是否配置公式（0，否；1，是）',
    version      int(10)              null comment '版本',
    created_by   varchar(50)          null comment '创建人',
    created_time datetime             null,
    updated_by   varchar(50)          null comment '修改人',
    updated_time datetime             null
)
    comment '指标信息';

create index idx_cid
    on ob_index (company_id);

create index idx_gcode
    on ob_index (group_code);

create index idx_icode
    on ob_index (index_code);

create index idx_pcode
    on ob_index (parent_code);

create table ob_index_formula
(
    id             varchar(50)   not null
        primary key,
    company_id     varchar(50)   null,
    index_id       varchar(50)   null comment '指标id',
    formula        text          null comment '公式',
    formula_status tinyint(1)    null comment '公式状态(0，无效；1，有效；2，公式配置错误）',
    check_result   varchar(3000) null comment '公式检验结果',
    created_by     varchar(50)   null,
    created_time   datetime      null,
    updated_by     varchar(50)   null,
    updated_time   datetime      null
)
    comment '指标公式配置';

create index idx_cid
    on ob_index_formula (company_id);

create index idx_iid
    on ob_index_formula (index_id);

create table ob_index_group
(
    id           varchar(50)          not null
        primary key,
    company_id   varchar(50)          null,
    group_code   varchar(50)          null comment '指标组编码',
    group_name   varchar(50)          null comment '指标组名称',
    group_status tinyint(1) default 1 null comment '指标组状态（0，无效；1，有效）',
    version      int(10)              null comment '版本',
    created_by   varchar(50)          null comment '创建人',
    created_time datetime             null,
    updated_by   varchar(50)          null,
    updated_time datetime             null
)
    comment '指标分组';

create index idx_cid
    on ob_index_group (company_id);

create index idx_gcode
    on ob_index_group (group_code);

create table ob_index_operate_log
(
    id           varchar(50)   not null
        primary key,
    company_id   varchar(50)   null,
    oper_type    int(5)        null comment '操作类型：1，新增；2，修改；3，删除',
    index_id     varchar(50)   null comment '指标id',
    index_code   varchar(50)   null comment '指标编码',
    oper_content varchar(2000) null comment '操作内容',
    created_by   varchar(50)   null,
    created_time datetime      null,
    updated_time datetime      null
)
    comment '指标操作记录';

create index idx_cid
    on ob_index_operate_log (company_id);

create index idx_icode
    on ob_index_operate_log (index_code);

create index idx_iid
    on ob_index_operate_log (index_id);

create table ob_index_tag
(
    id           varchar(50)  not null
        primary key,
    company_id   varchar(50)  null,
    tag_name     varchar(100) null,
    created_by   varchar(50)  null,
    created_time datetime     null,
    updated_by   varchar(50)  null,
    updated_time datetime     null
)
    comment '指标标签';

create index idx_cid
    on ob_index_tag (company_id);

create table ob_index_tag_ref
(
    id           varchar(50) not null
        primary key,
    company_id   varchar(50) null,
    index_id     varchar(50) null,
    tag_id       varchar(50) null,
    created_time datetime    null,
    updated_time datetime    null
)
    comment '指标标签关联';

create index idx_cid
    on ob_index_tag_ref (company_id);

create index idx_iid
    on ob_index_tag_ref (index_id);

create table ob_object
(
    id                  varchar(50)    not null
        primary key,
    company_id          varchar(50)    null,
    plan_id             varchar(50)    null comment '计划id',
    cycle_id            varchar(50)    null comment '周期id',
    dimension_id        varchar(50)    null comment '维度id',
    index_id            varchar(50)    null comment '指标id',
    dimension_name      varchar(200)   null comment '维度名称',
    dimension_level     int(5)         null comment '维度层级',
    index_name          varchar(200)   null comment '指标名称',
    index_level         int(5)         null comment '指标层级',
    index_format        int(5)         null comment '指标格式：1，整数；2，保留1位小数；3，保留2位小数；4，保留3位小数；5，千分位；6，千分位（小数点）；7，百分比；8，百分比（小数点）',
    emp_id              varchar(50)    null comment '目标负责人',
    object_name         varchar(200)   null comment '目标名称',
    object_no           varchar(50)    null comment '目标编号',
    target_value        decimal(10, 2) null comment '目标值',
    target_unit         varchar(20)    null comment '目标值单位',
    finish_value        decimal(10, 2) null comment '完成值',
    diff_value          decimal(10, 2) null comment '拆解差额',
    parent_id           varchar(50)    null comment '上级目标',
    parent_emp_id       varchar(50)    null comment '上级负责人',
    object_path         varchar(3000)  null comment '目标链路',
    object_status       tinyint(1)     null comment '目标状态：-1，已删除；0，草稿；1，审核中；2，审核通过（无需审核时也是此状态）',
    object_audit_status tinyint(1)     null comment '目标审核状态：1，新增审核；2，修改审核；3，删除审核；',
    audit_no            varchar(50)    null comment '审批单号',
    audit_submit_time   datetime       null comment '提交审核时间',
    created_by          varchar(50)    null,
    created_time        datetime       null,
    updated_by          varchar(50)    null,
    updated_time        datetime       null
)
    comment '目标信息';

create index idx_cid
    on ob_object (company_id);

create index idx_cycleid
    on ob_object (cycle_id);

create index idx_did
    on ob_object (dimension_id);

create index idx_eid
    on ob_object (emp_id);

create index idx_iid
    on ob_object (index_id);

create index idx_no
    on ob_object (audit_no);

create index idx_pid
    on ob_object (plan_id);

create table ob_object_discuss
(
    id                  varchar(50)   not null
        primary key,
    company_id          varchar(50)   null,
    plan_id             varchar(50)   null comment '计划id',
    object_id           varchar(50)   null comment '目标id',
    content             text          null comment '评论内容',
    content_type        tinyint(1)    null comment '内容类型:1，文本；2，图片；3，附件',
    discuss_type        tinyint(1)    null comment '评论类型：1，评论；2，回复；3，艾特',
    replayed_discuss_id varchar(50)   null comment '被回复的id',
    replayed_name       varchar(100)  null comment '回复人员姓名',
    replayed_emp_id     varchar(50)   null comment '回复人员id',
    at_emp_id           varchar(2000) null comment '艾特人员id，多个id逗号分隔',
    at_emp_name         varchar(2000) null comment '艾特人员姓名，多个人名逗号分隔',
    ref_id              varchar(50)   null comment '关联id，二级评论展示',
    created_by          varchar(50)   null,
    created_name        varchar(100)  null,
    created_time        datetime      null,
    updated_time        datetime      null
)
    comment '目标评论';

create index idx_cid
    on ob_object_discuss (company_id);

create index idx_oid
    on ob_object_discuss (object_id);

create index idx_pid
    on ob_object_discuss (plan_id);

create table ob_object_modify
(
    id             varchar(50) not null
        primary key,
    company_id     varchar(50) null,
    object_id      varchar(50) null,
    object_content mediumtext  null,
    audit_no       varchar(50) null comment '审批单号',
    created_by     varchar(50) null,
    created_time   datetime    null,
    updated_by     varchar(50) null,
    updated_time   datetime    null
)
    comment '目标修改信息';

create index idx_ano
    on ob_object_modify (audit_no);

create index idx_cid
    on ob_object_modify (company_id);

create index idx_oid
    on ob_object_modify (object_id);

create table ob_object_operate_log
(
    id              varchar(50) not null
        primary key,
    company_id      varchar(50) null,
    oper_type       int(5)      null comment '操作类型：1，新增；2，修改；3，删除；4，更新；5，修改完成值；6，派发',
    plan_id         varchar(50) null comment '计划id',
    object_id       varchar(50) null comment '目标id',
    operate_content text        null comment '修改内容',
    memo            text        null comment '内容备注',
    created_by      varchar(50) null,
    created_time    datetime    null,
    updated_time    datetime    null
)
    comment '目标操作记录';

create index idx_cid
    on ob_object_operate_log (company_id);

create index idx_oid
    on ob_object_operate_log (object_id);

create index idx_pid
    on ob_object_operate_log (plan_id);

create table ob_object_updated_log
(
    id                  varchar(50)    not null
        primary key,
    company_id          varchar(50)    null,
    update_no           varchar(50)    null comment '更新单号',
    plan_id             varchar(50)    null comment '计划id',
    object_id           varchar(50)    null comment '目标id',
    object_name         varchar(200)   null comment '更新来源目标名称',
    dimension_id        varchar(50)    null comment '维度id',
    dimension_name      varchar(100)   null comment '维度名称',
    dimension_level     int(5)         null comment '维度层级',
    index_id            varchar(50)    null comment '指标id',
    index_name          int(5)         null comment '指标名称',
    index_level         char(10)       null comment '指标层级',
    updated_object_id   varchar(50)    null comment '被更新目标id',
    updated_source_type tinyint(1)     null comment '更新来源类型：1，直接更新；2，下级更新；3，手动调整',
    updated_type        tinyint(1)     null comment '更新类型：1，新增；2，减少',
    updated_value       decimal(10, 2) null comment '更新数值',
    updated_unit        varchar(20)    null comment '更新数值单位',
    updated_files       text           null comment '更新附件：[{"name":"文件名","url":"文件地址","size":文件大小}]',
    updated_memo        varchar(2000)  null comment '更新备注',
    audit_status        tinyint(1)     null comment '审核状态：0，待审核；1，审核通过；2，审核驳回',
    audit_no            varchar(50)    null comment '审核单号',
    audit_submit_time   datetime       null comment '提交审核时间',
    created_by          varchar(50)    null,
    created_by_name     varchar(50)    null,
    created_by_avatar   varchar(50)    null,
    created_time        datetime       null,
    updated_time        datetime       null
);

create table ob_plan
(
    id                   varchar(50)          not null
        primary key,
    company_id           varchar(50)          null,
    plan_name            varchar(200)         null comment '计划名称',
    plan_no              varchar(50)          null comment '计划编号',
    group_id             varchar(50)          null comment '指标组id',
    group_code           varchar(50)          null comment '指标组编码',
    group_name           varchar(200)         null comment '指标名称',
    dimension_group_id   varchar(50)          null comment '维度组id',
    dimension_group_code varchar(50)          null comment '维度组编码',
    dimension_group_name varchar(200)         null comment '维度组名称',
    plan_cycle           varchar(100)         null comment '计划周期(year，年；half_year，半年；quarter，季度；bimonthly，双月；month，月度），多个周期逗号分隔',
    plan_start_date      date                 null comment '计划开始日期',
    plan_end_date        date                 null comment '计划截止日期',
    plan_status          tinyint(1) default 0 null comment '计划状态（-1，已删除；0，正常；1，已归档）',
    plan_manager         varchar(2000)        null comment '管理员：人员id字符串，逗号分隔',
    plan_viewer          varchar(3000)        null comment '可查看人：all,所有人；人员部门id字符串，逗号分隔',
    plan_usedby          varchar(3000)        null comment '使用的人：all,所有人；人员部门id字符串，逗号分隔',
    update_finish        tinyint(1)           null comment '是否允许修改目标完成值（0，否；1，是且需审核；2，是且无需审核）',
    auto_complete        tinyint(1) default 0 null comment '开启拆解自动补齐（0，否；1，是）',
    open_audit           tinyint(1) default 0 null comment '是否开审核（0，否；1，是）',
    open_set_remind      tinyint(1)           null comment '是否开启设置提醒（0，否；1，是）',
    created_by           varchar(50)          null,
    created_time         datetime             null,
    updated_by           varchar(50)          null,
    updated_time         datetime             null
)
    comment '目标计划';

create index idx_cid
    on ob_plan (company_id);

create index idx_did
    on ob_plan (dimension_group_id);

create index idx_gid
    on ob_plan (group_id);

create table ob_plan_cycle_detail
(
    id               varchar(50)          not null
        primary key,
    company_id       varchar(50)          null,
    plan_id          varchar(50)          null comment '计划id',
    plan_no          varchar(50)          null comment '计划编号',
    cycle_type       varchar(20)          null comment '周期类型(year，年；half_year，半年；quarter，季度；bimonthly，双月；month，月度）',
    cycle_name       varchar(100)         null comment '周期名称',
    cycle_start_date date                 null comment '周期开始时间',
    cycle_end_date   date                 null comment '周期截止日期',
    cycle_status     tinyint(1) default 1 null comment '周期状态：0，无效；1，有效',
    parent_id        varchar(50)          null comment '上级周期',
    cycle_path       varchar(3000)        null comment '周期路径',
    order_num        int(10)              null comment '排序',
    created_time     datetime             null,
    updated_time     datetime             null
)
    comment '计划周期详情';

create index idx_cid
    on ob_plan_cycle_detail (company_id);

create index idx_pid
    on ob_plan_cycle_detail (plan_id);

create index idx_pno
    on ob_plan_cycle_detail (plan_no);

create table ob_plan_dimension
(
    id               varchar(50)   not null
        primary key,
    company_id       varchar(50)   null,
    plan_id          varchar(50)   null comment '计划id',
    dimension_id     varchar(50)   null comment '维度id',
    dimension_code   varchar(50)   null comment '指标编码',
    dimension_name   varchar(200)  null comment '维度名称',
    dimension_level  int(5)        null comment '维度层级',
    dimension_path   varchar(3000) null comment '维度路径',
    parent_code      varchar(50)   null comment '父级维度code',
    had_child        tinyint(1)    null comment '是否存在下一级（0，否；1，是）',
    group_code       varchar(50)   null comment '维度组code ',
    dimension_status tinyint(1)    null comment '维度状态（0，无效；1，有效）',
    version          int(10)       null comment '版本',
    created_by       varchar(50)   null,
    created_time     datetime      null,
    updated_by       varchar(50)   null,
    updated_time     datetime      null
)
    comment '计划维度';

create index idx_cid
    on ob_plan_dimension (company_id);

create index idx_dcode
    on ob_plan_dimension (dimension_code);

create index idx_did
    on ob_plan_dimension (dimension_id);

create index idx_pid
    on ob_plan_dimension (plan_id);

create table ob_plan_dimension_group
(
    id           varchar(50)      not null
        primary key,
    company_id   varchar(50)      null,
    plan_id      varchar(50)      null comment '计划id',
    group_id     varchar(50)      null comment '维度组id',
    group_type   int(5) default 1 null comment '维度组类型：1，自定义；2，组织结构',
    group_code   varchar(50)      null comment '维度组编码',
    group_name   varchar(200)     null comment '维度组名称',
    group_status tinyint(1)       null comment '维度组状态：0，无效；1，有效',
    version      int(10)          null comment '版本',
    created_by   varchar(50)      null,
    created_time datetime         null,
    updated_by   varchar(50)      null,
    updated_time datetime         null
)
    comment '计划维度组';

create index idx_cid
    on ob_plan_dimension_group (company_id);

create index idx_gcode
    on ob_plan_dimension_group (group_code);

create index idx_gid
    on ob_plan_dimension_group (group_id);

create index idx_pid
    on ob_plan_dimension_group (plan_id);

create table ob_plan_index
(
    id            varchar(50)          not null
        primary key,
    company_id    varchar(50)          null,
    plan_id       varchar(50)          null comment '计划id',
    index_id      varchar(50)          null comment '指标id',
    index_code    varchar(50)          null comment '指标编码',
    index_name    varchar(50)          null comment '指标名称',
    index_unit    varchar(20)          null comment '指标单位',
    index_format  int(5)               null comment '指标格式：1，整数；2，保留1位小数；3，保留2位小数；4，保留3位小数；5，千分位；6，千分位（小数点）；7，百分比；8，百分比（小数点）',
    parent_code   varchar(50)          null comment '上级指标',
    index_path    varchar(1000)        null comment '指标路径',
    index_level   int(5)               null comment '指标等级',
    index_remark  varchar(200)         null comment '指标备注',
    group_code    varchar(50)          null comment '指标组编码',
    index_type    tinyint(1)           null comment '指标类型（1，填入型；2，计算型）',
    index_formula text                 null comment '指标公式',
    index_status  tinyint(1)           null comment '指标状态（0，已删除；1，有效）',
    index_tags    varchar(1000)        null comment '指标标签',
    index_tag_id  varchar(1000)        null comment '指标标签id',
    had_child     tinyint(1) default 0 null comment '是否存在下级：0，否；1，是',
    had_formula   tinyint(1) default 0 null comment '是否配置公式：0，否；1，是',
    version       int(10)              null comment '版本',
    created_by    varchar(50)          null comment '创建人',
    created_time  datetime             null,
    updated_by    varchar(50)          null comment '修改人',
    updated_time  datetime             null
)
    comment '计划指标信息';

create index idx_cid
    on ob_plan_index (company_id);

create index idx_icode
    on ob_plan_index (index_code);

create index idx_iid
    on ob_plan_index (index_id);

create index idx_pid
    on ob_plan_index (plan_id);

create table ob_plan_index_group
(
    id           varchar(50)          not null
        primary key,
    company_id   varchar(50)          null,
    plan_id      varchar(50)          null comment '计划id',
    group_id     varchar(50)          null comment '指标组id',
    group_code   varchar(50)          null comment '指标组编码',
    group_name   varchar(50)          null comment '指标组名称',
    group_status tinyint(1) default 1 null comment '指标组状态（0，已删除；1，有效）',
    version      int(10)              null comment '版本',
    created_by   varchar(50)          null comment '创建人',
    created_time datetime             null,
    updated_by   varchar(50)          null,
    updated_time datetime             null
)
    comment '计划指标分组';

create index idx_cid
    on ob_plan_index_group (company_id);

create index idx_gcode
    on ob_plan_index_group (group_code);

create index idx_gid
    on ob_plan_index_group (group_id);

create index idx_pid
    on ob_plan_index_group (plan_id);

create table ob_plan_manager
(
    id           varchar(50) not null
        primary key,
    company_id   varchar(50) null,
    plan_id      varchar(50) null comment '计划id',
    plan_no      varchar(50) null comment '计划编码',
    plan_name    varchar(50) null comment '计划名称',
    manager_id   varchar(50) null comment '管理员员工id',
    plan_status  tinyint(1)  null comment '计划状态（同计划信息中的状态）',
    created_time datetime    null,
    updated_time datetime    null
)
    comment '计划管理员';

create index ide_mid
    on ob_plan_manager (manager_id);

create index idx_cid
    on ob_plan_manager (company_id);

create index idx_pcode
    on ob_plan_manager (plan_no);

create table ob_plan_operate_log
(
    id           varchar(50)   not null
        primary key,
    company_id   varchar(50)   null,
    oper_type    int(5)        null comment '操作类型：1，新增；2，修改；3，删除；4，归档；5，重启',
    content_type char(10)      null comment '操作内容类型（1，基本信息；2，人员设置；3，审核设置；4，提醒设置；5，高级设置），用于修改计划操作',
    plan_id      varchar(50)   null comment '计划id',
    plan_no      varchar(50)   null comment '计划编码',
    oper_content varchar(2000) null comment '操作内容',
    created_by   varchar(50)   null,
    created_time datetime      null,
    updated_time datetime      null
)
    comment '计划操作记录';

create index idx_cid
    on ob_plan_operate_log (company_id);

create index idx_pid
    on ob_plan_operate_log (plan_id);

create index idx_pno
    on ob_plan_operate_log (plan_no);

create table ob_plan_remind
(
    id               varchar(50)          not null
        primary key,
    company_id       varchar(50)          null,
    plan_id          varchar(50)          null comment '计划id',
    remind_date_type tinyint(1) default 1 null comment '提醒时间类型：1，开始前',
    remind_date      int(10)              null comment '提醒时间：0，当天；1，前一天，后一天。。。。',
    range_type       tinyint(1)           null comment '提醒范围（1，全公司；2，自定义）',
    range_value      varchar(3000)        null comment '提醒范围值，自定义范围时必传',
    status           tinyint(1) default 1 null comment '状态（0，无效；1，有效）',
    created_by       varchar(50)          null,
    created_time     datetime             null,
    updated_by       varchar(50)          null,
    updated_time     datetime             null
)
    comment '目标计划提醒';

create index idx_cid
    on ob_plan_remind (company_id);

create index idx_pid
    on ob_plan_remind (plan_id);

create table ob_plan_usedby
(
    id           varchar(50) not null
        primary key,
    company_id   varchar(50) null,
    plan_id      varchar(50) null comment '计划id',
    plan_no      varchar(50) null comment '计划编码',
    plan_name    varchar(50) null comment '计划名称',
    usedby_id    varchar(50) null comment '参与使用人id，包含人员部门id',
    plan_status  tinyint(1)  null comment '计划状态，同计划信息中的状态',
    created_time datetime    null,
    updated_time datetime    null
)
    comment '可参与使用的计划的人';

create index ide_uid
    on ob_plan_usedby (usedby_id);

create index idx_cid
    on ob_plan_usedby (company_id);

create index idx_pcode
    on ob_plan_usedby (plan_no);

create table ob_plan_viewer
(
    id           varchar(50) not null
        primary key,
    company_id   varchar(50) null,
    plan_id      varchar(50) null comment '计划id',
    plan_no      varchar(50) null comment '计划编码',
    plan_name    varchar(50) null comment '计划名称',
    viewer_id    varchar(50) null comment '查看人id，包含部门id',
    plan_status  tinyint(1)  null comment '计划状态，同计划信息中的状态',
    created_time datetime    null,
    updated_time datetime    null
)
    comment '可查看计划的员工';

create index ide_vid
    on ob_plan_viewer (viewer_id);

create index idx_cid
    on ob_plan_viewer (company_id);

create index idx_pcode
    on ob_plan_viewer (plan_no);

create table onboard_timer_conf
(
    id            bigint                                not null comment '主键'
        primary key,
    company_id    varchar(50)                           not null comment '公司id',
    confirm_timer text                                  null comment '确认提醒配置 {"open":1,conf:["timerType": 2, "id": "10000", "modCode": 1000001, "eachType": 1,"conf":{"beforDays":0,"afterDays":46,"type":1,"runTime":"11:00"}]}',
    scoring_timer text                                  null comment '评分提醒配置 {"open":1,[{"timerType": 2, "id": "10000", "modCode": 1000002, "eachType": 1, "conf": {"beforDays": 0, "afterDays": 0, "type": 1, "runTime": "11:00"}}]}',
    created_time  datetime    default CURRENT_TIMESTAMP null,
    created_user  varchar(50) default ''                null,
    updated_time  datetime    default CURRENT_TIMESTAMP null,
    updated_user  varchar(50) default ''                null,
    is_deleted    varchar(5)  default 'false'           null,
    version       int         default 0                 null comment '版本号'
)
    comment '新人考核的定时提醒配置';

create index otc_idx_companyId
    on onboard_timer_conf (company_id);

create table onboard_timer_item
(
    id                    bigint                                not null comment '主键'
        primary key,
    company_id            varchar(50)                           not null comment '公司id',
    onboard_timer_conf_id bigint                                not null comment 'onboard_timer_conf.id',
    mod_code              bigint                                not null comment '子模块编号:[1000001=新人任务确认],[1000002=新人评分提醒管理员],[1000003=新人评分提醒评分人]',
    conf                  text                                  null comment 'json配置对象',
    timer_type            int         default 1                 null comment '执行分类: forEach执行=2, 单次执行=1',
    each_type             int         default 1                 null comment 'forEach分类: 每天=1, 每小时=2',
    exe_cond_sql          text                                  null comment '检测执行条件的sql,参数中要的company_id',
    biz_method            text                                  null comment '执行业务操作方法',
    last_run_time         datetime    default CURRENT_TIMESTAMP null,
    lock_id               bigint      default 0                 null comment '并发执行的标记锁id,0=未锁定, 大于0表示执行的锁id',
    created_time          datetime    default CURRENT_TIMESTAMP null,
    created_user          varchar(50) default ''                null,
    updated_time          datetime    default CURRENT_TIMESTAMP null,
    updated_user          varchar(50) default ''                null,
    is_deleted            varchar(5)  default 'false'           null,
    version               int         default 0                 null comment '版本号'
)
    comment '新人考核的定时提醒明细项' charset = utf8mb4;

create index oti_idx_companyId
    on onboard_timer_item (company_id);

create table onboard_timer_log
(
    id            bigint                                not null comment '主键'
        primary key,
    company_id    varchar(50)                           not null comment '公司id',
    timer_item_id bigint                                not null comment 'onboard_timer_item.id,已执行过的timerItemId',
    task_user_id  varchar(50)                           not null comment '单次提醒过的记录id',
    lock_id       bigint      default 0                 null comment '并发执行的标记锁id,0=未锁定, 大于0表示执行的锁id',
    created_time  datetime    default CURRENT_TIMESTAMP null,
    created_user  varchar(50) default ''                null,
    updated_time  datetime    default CURRENT_TIMESTAMP null,
    updated_user  varchar(50) default ''                null,
    is_deleted    varchar(5)  default 'false'           null,
    version       int         default 0                 null comment '版本号'
)
    comment '新人考核的定时明细执行记录' charset = utf8mb4;

create index otl_idx_companyId_itemId
    on onboard_timer_log (company_id, timer_item_id);

create table open_access_cache
(
    `key`        varchar(255)                       not null comment 'key'
        primary key,
    val          varchar(255)                       null comment 'value',
    expire_in    bigint                             null comment '毫秒',
    db_timestamp datetime default CURRENT_TIMESTAMP null comment '当前时间戳',
    created_time datetime                           null,
    updated_time datetime                           null,
    version      int      default 0                 null comment '版本号'
)
    comment '提供开放接口的access token缓存持久化' charset = utf8mb4;

create table open_access_limit
(
    `key`        varchar(255)        not null comment '格式化的key'
        primary key,
    max_count    bigint default 1000 null comment '每小时最大访问量',
    access_count bigint default 0    null comment '当前时间段已持久化的访问量',
    created_time datetime            null,
    updated_time datetime            null,
    version      int    default 0    null comment '版本号'
)
    comment '开放接口每小时访问量持久化表' charset = utf8mb4;

create table open_auth_info
(
    corp_id          varchar(255)  not null comment 'corp id',
    company_id       varchar(255)  not null comment 'company id'
        primary key,
    company_name     varchar(255)  null comment 'company name',
    contact          varchar(255)  null comment '联系人，可以是联系我们开通的人，仅参考',
    access_key       varchar(255)  null comment '访问key',
    access_secret    varchar(255)  null comment '访问secret',
    access_token     varchar(255)  null,
    perm_start_time  varchar(255)  null comment '服务开启时间',
    perm_end_time    varchar(255)  null comment '服务结束时间',
    max_access_limit bigint        null comment '总访问上限，-1不限制',
    access_count     bigint        null comment '当前访问量',
    apis             text          null comment '授权的接口',
    ips              text          null comment '授权的ip',
    created_time     datetime      null,
    updated_time     datetime      null,
    version          int default 0 null comment '版本号'
)
    comment '提供开放接口的注册公司信息' charset = utf8mb4;

create index k_corp_id
    on open_auth_info (corp_id);

create table operation_log
(
    id             varchar(50) not null
        primary key,
    company_id     varchar(50) null comment '公司id',
    ref_id         varchar(50) null comment '关联的id',
    business_scene varchar(50) null comment '业务场景',
    field_name     varchar(50) null comment '字段名称',
    before_value   text        null comment '修改前的值',
    after_value    text        null comment '修改后的值',
    operation_type varchar(20) null comment '操作类型(增删改)',
    description    text        null comment '描述',
    created_user   varchar(50) null,
    created_time   datetime    null,
    updated_time   datetime    null,
    kpi_item_id    varchar(50) null comment '关联的指标id'
)
    comment '操作日志' charset = utf8mb4;

create index company_id
    on operation_log (company_id);

create index idx_refid_businessscene
    on operation_log (ref_id, business_scene);

create table org_eval_owner
(
    org_id             varchar(50)                  not null comment '部门id'
        primary key,
    company_id         varchar(50)                  not null comment '公司id',
    org_owner_id       varchar(50)  default ''      null comment '组织负责人',
    org_owner_org_name varchar(50)  default ''      null comment '组织负责人所在部门',
    org_owner_org_id   varchar(50)  default ''      null comment '组织负责人所在部门id',
    index_tables       varchar(500) default '[]'    null comment '组织负责人',
    table_cnt          int          default 0       null comment '组织负责人',
    is_deleted         varchar(5)   default 'false' null comment '是否删除',
    created_user       varchar(50)                  null comment '创建用户',
    created_time       datetime                     null comment '创建时间',
    updated_user       varchar(50)                  null comment '修改用户',
    updated_time       datetime                     null comment '修改时间',
    version            int          default 0       null comment '版本号'
)
    comment '组织任务负责人' charset = utf8mb4;

create index idx_company_id
    on org_eval_owner (company_id);

create index idx_org_id
    on org_eval_owner (org_id);

create table org_use_category_item
(
    category_id  varchar(50)      not null comment '类别ID',
    company_id   varchar(50)      null comment '公司id',
    org_id       varchar(50)      null comment '为0表示所有',
    appoint_org  int(1) default 0 null comment '是否指定部门 默认=0否、1=是',
    is_deleted   varchar(10)      null comment '是否删除',
    created_user varchar(50)      null comment '创建用户',
    created_time datetime         null comment '创建时间',
    updated_user varchar(50)      null comment '修改用户',
    updated_time datetime         null comment '修改时间',
    version      int    default 0 null
)
    comment '部门引用的指标，通过类别权限控制' charset = utf8mb4;

create index category_key
    on org_use_category_item (category_id);

create index company_key
    on org_use_category_item (company_id);

create index ref_key
    on org_use_category_item (org_id);

create table output_connector
(
    company_id    varchar(50)                 not null comment '公司id',
    auth_type     int         default 1       null comment '1= {key, secret}',
    auth_conf     text                        not null comment '目标系统api授权配置 {key, secret}',
    token         text                        null comment '当前token值',
    token_status  int                         null comment '当前token状态',
    token_expire  mediumtext                  null comment '当前token过期时间s',
    token_version int         default 0       null comment '当前token 版本号',
    api_url       varchar(256)                not null comment '外部接口地址',
    is_deleted    varchar(10) default 'false' null comment '是否删除',
    created_user  varchar(50)                 null comment '创建用户',
    created_time  datetime                    null comment '创建时间',
    updated_user  varchar(50)                 null comment '修改用户',
    updated_time  datetime                    null comment '修改时间',
    version       int         default 0       null comment '版本号'
)
    comment '企业内部系统输出连接器' charset = utf8mb4;

create index idx_companyId
    on output_connector (company_id);

create table output_eval_ref
(
    company_id   varchar(50)                 not null comment '公司id',
    eval_id      varchar(50)                 not null comment '绩效结果id',
    ext_eval_id  varchar(50)                 not null comment '外部绩效id',
    ext_name     varchar(50)                 not null comment '外部系统名字',
    is_deleted   varchar(10) default 'false' null comment '是否删除',
    created_user varchar(50)                 null comment '创建用户',
    created_time datetime                    null comment '创建时间',
    updated_user varchar(50)                 null comment '修改用户',
    updated_time datetime                    null comment '修改时间',
    version      int         default 0       null comment '版本号'
)
    comment '企业内部系统输出连接器外部绩效id' charset = utf8mb4;

create index idx_companyId_evalId
    on output_eval_ref (company_id, eval_id);

create table perf_eval_total_level_result
(
    id             varchar(50)    not null
        primary key,
    company_id     varchar(50)    null comment '公司id',
    task_user_id   varchar(50)    null comment '员工考核id,task_user.id',
    audit_status   varchar(30)    null comment '审核状态',
    scorer_id      varchar(50)    null comment '评分人id',
    score_weight   decimal(10, 2) null comment '评分权重',
    score_comment  text           null comment '评分评语',
    score_att_url  text           null comment '评分附件url',
    score_level    varchar(255)   null comment '打分人提交的绩效等级',
    transfer_id    varchar(50)    null comment '转交来源id',
    approval_order int            null comment '审批顺序',
    created_user   varchar(50)    null comment '创建用户',
    created_time   datetime       null comment '创建时间',
    updated_user   varchar(50)    null comment '修改用户',
    updated_time   datetime       null comment '修改时间',
    is_deleted     varchar(10)    null,
    version        int default 0  null comment '版本号'
)
    comment '任务中考核维度总评分结果(等级),一个总评分需要多人评分' charset = utf8;

create index uk_companyId_scorerId
    on perf_eval_total_level_result (company_id, task_user_id, scorer_id);

create table perf_eval_type_result
(
    id             varchar(50)    not null
        primary key,
    company_id     varchar(50)    null comment '公司id',
    task_user_id   varchar(50)    null comment '员工考核id,task_user.id',
    audit_status   varchar(30)    null comment '审核状态',
    kpi_type_id    varchar(50)    null comment '指标类id',
    scorer_type    varchar(50)    null comment '评分人类型（自评/同级互评/下级互评/上级评/指定评分人）',
    scorer_id      varchar(50)    null comment '评分人id',
    score          decimal(10, 2) null comment '评分分数',
    score_weight   decimal(10, 2) null comment '评分权重',
    score_comment  text           null comment '评分评语',
    score_att_url  text           null comment '评分附件url',
    reviewers_type varchar(30)    null comment '会签还是或签',
    modify_flag    varchar(10)    null comment '是否可以修改',
    score_level    varchar(50)    null comment '打分人提交的绩效等级',
    final_score    decimal(10, 3) null comment '没算阶段权重的分数',
    transfer_id    varchar(50)    null comment '转交来源id',
    approval_order int            null comment '审批顺序',
    created_user   varchar(50)    null comment '创建用户',
    created_time   datetime       null comment '创建时间',
    updated_user   varchar(50)    null comment '修改用户',
    updated_time   datetime       null comment '修改时间',
    is_deleted     varchar(10)    null,
    version        int default 0  null comment '版本号'
)
    comment '任务中考核维度的评分结果,一个维度对应多次结果' charset = utf8;

create index k_scorerId
    on perf_eval_type_result (company_id, scorer_id);

create index uk_companyId_scorerId
    on perf_eval_type_result (company_id, task_user_id, kpi_type_id, scorer_type, scorer_id);

create table perf_evaluate_cycle
(
    id           bigint auto_increment comment '周期id'
        primary key,
    company_id   varchar(50)                  not null comment '企业id',
    name         varchar(100)                 null,
    year         int         default 0        null comment '周期年份',
    type         varchar(15) default 'month'  null comment '"month", "cross_month", "quarter", "half_year", "year"',
    value        int         default 0        null comment '1-12月,1-4季,1-12双月,1-2半年',
    cycle_start  varchar(100)                 null comment '周期开始时间点',
    cycle_end    varchar(100)                 null comment '周期结束时间点',
    eval_cnt     int         default 0        null comment '周期中考核人次',
    from_task_id varchar(50) default '0'      null comment '旧数据的任务id,0=表示新数据',
    created_user varchar(50) default ''       null,
    updated_user varchar(50) default ''       null,
    created_time datetime                     null,
    updated_time datetime                     null,
    version      int         default 0        null comment '版本号',
    cycle_status varchar(10) default 'normal' null comment '周期状态',
    is_new_cycle int         default 0        null comment '是否新周期'
)
    comment '考核周期' charset = utf8mb4;

create index idx_company_id
    on perf_evaluate_cycle (company_id);

create index idx_created_user
    on perf_evaluate_cycle (created_user);

create table perf_evaluate_item_notice
(
    id                 varchar(50)   null,
    company_id         varchar(50)   null,
    task_id            varchar(50)   null,
    emp_id             varchar(50)   null,
    kpi_type_id        varchar(50)   null,
    kpi_item_id        varchar(50)   null,
    send_week_day      varchar(50)   null comment '每周发送日期',
    send_time          varchar(255)  null comment '发送时间',
    close_type         varchar(50)   null comment '关闭提醒的条件，date指定日期，finish任务完成',
    close_date         date          null comment '关闭提醒的日期',
    update_notice_user text          null comment '更新后抄送进度的人',
    status             varchar(30)   null comment '状态 normal正常，close已关闭',
    is_deleted         varchar(10)   null,
    created_user       varchar(50)   null,
    created_time       datetime      null,
    updated_user       varchar(50)   null,
    updated_time       datetime      null,
    version            int default 0 null comment '版本号',
    task_user_id       varchar(50)   null comment 'task user 表的id'
)
    comment '指标的跟踪提醒配置表';

create index company_id
    on perf_evaluate_item_notice (company_id);

create index idx_task_user_id
    on perf_evaluate_item_notice (task_user_id);

create table perf_evaluate_item_used_field
(
    field_id     varchar(50)      not null comment 'company_kpi_item_custom_field.id',
    company_id   varchar(50)      null comment '公司id',
    task_user_id varchar(50)      null comment 'perf_evaluate_task_user.id',
    kpi_item_id  varchar(50)      null comment 'perf_evaluate_task_kpi.kpi_item_id',
    name         varchar(50)      null comment '字段名称【复制于company_kpi_item_custom_field】',
    value        varchar(512)     null comment '字段值【复制于company_kpi_item_custom_field】',
    type         int(2) default 1 null comment '字段类型（1：输入框  2：开关 3：下拉选 4：复选 5：img）【复制于company_kpi_item_custom_field】',
    req          int(1) default 1 null comment '是否必填（0：否  1：是）【复制于company_kpi_item_custom_field】',
    status       varchar(20)      null comment '指标字段状态（valid：有效，invalid：无效）【复制于company_kpi_item_custom_field】',
    `show`       int(1) default 1 null comment '是否显示（0：否  1：是）',
    sort         int(5)           null comment '指标字段排序值',
    admin_type   int(1)           null comment '1:系统默认字段  0：自定义字段',
    is_deleted   varchar(10)      null comment '是否删除',
    created_user varchar(50)      null comment '创建用户',
    created_time datetime         null comment '创建时间',
    updated_user varchar(50)      null comment '修改用户',
    updated_time datetime         null comment '修改时间',
    version      int    default 0 null
)
    comment '考核任务指标自定义字段使用表' charset = utf8mb4;

create index company_key
    on perf_evaluate_item_used_field (company_id);

create index field_key
    on perf_evaluate_item_used_field (field_id);

create index item_key
    on perf_evaluate_item_used_field (kpi_item_id);

create index task_user_key
    on perf_evaluate_item_used_field (task_user_id);

create table perf_evaluate_score_summary
(
    company_id   varchar(255)                not null,
    task_user_id varchar(255)                not null,
    score_type   varchar(255)                not null,
    summary      text                        null,
    created_time datetime                    null,
    created_user varchar(50) default ''      null,
    updated_time datetime                    null,
    updated_user varchar(50) default ''      null,
    is_deleted   varchar(5)  default 'false' null,
    version      int         default 0       null comment '版本号'
)
    comment '评分总结';

create index index_company_id
    on perf_evaluate_score_summary (company_id);

create index index_created_user
    on perf_evaluate_score_summary (created_user);

create index index_task_user_id
    on perf_evaluate_score_summary (task_user_id);

create table perf_evaluate_task_action
(
    id                 varchar(50)    not null
        primary key,
    company_id         varchar(50)    null comment '公司id',
    task_id            varchar(50)    null comment '考核任务id',
    task_kpi_id        varchar(50)    null comment '考核任务指标id',
    name               varchar(100)   null comment '行动名称',
    start_time         datetime       null comment '计划完成日期开始',
    end_time           datetime       null comment '计划完成日期截止',
    priority           int            null comment '优先级(0-普通,1-中,2-高)',
    progress           decimal(10, 2) null comment '当前进度（百分比）',
    responsible_emp_id varchar(50)    null comment '指定责任人id',
    remark             varchar(200)   null comment '描述',
    is_deleted         varchar(10)    null comment '是否删除',
    created_user       varchar(50)    null comment '创建用户',
    created_time       datetime       null comment '创建时间',
    updated_user       varchar(50)    null comment '修改用户',
    updated_time       datetime       null comment '修改时间',
    version            int default 0  null comment '版本号'
)
    comment '考核任务-关键行动' charset = utf8;

create index company_id
    on perf_evaluate_task_action (company_id);

create table perf_evaluate_task_appeal_batch
(
    id            varchar(50)   not null
        primary key,
    company_id    varchar(50)   null,
    task_id       varchar(50)   null,
    emp_id        varchar(50)   null,
    appeal_date   date          null comment '申诉日期',
    receiver_name varchar(100)  null comment '申诉受理人姓名',
    receiver_id   varchar(50)   null comment '申诉受理人id',
    appeal_status varchar(20)   null comment '申诉状态（待受理：wait/已受理：handled/已撤回：canceled）',
    is_deleted    varchar(10)   null,
    created_user  varchar(50)   null,
    created_time  datetime      null,
    updated_user  varchar(50)   null,
    updated_time  datetime      null,
    version       int default 0 null comment '版本号',
    is_read       tinyint(1)    null comment '审核记录是否已读',
    task_user_id  varchar(50)   null comment 'task user 表的id'
)
    comment '考核任务申诉批次';

create table perf_evaluate_task_appeal_info
(
    id              varchar(50)            not null
        primary key,
    company_id      varchar(50)            null,
    task_id         varchar(50)            null,
    emp_id          varchar(50)            null,
    appeal_batch_id varchar(50)            null comment '申诉批次id',
    items           text                   null comment '申诉项目',
    reason          text                   null comment '申诉理由',
    remark          text                   null comment '申诉备注',
    appeal_result   varchar(20)            null comment '申诉结果（通过：pass/驳回：reject）',
    adjust_score    decimal(8, 2)          null comment '调整分数',
    is_deleted      varchar(10)            null,
    created_user    varchar(50)            null,
    created_time    datetime               null,
    updated_user    varchar(50)            null,
    updated_time    datetime               null,
    version         int         default 0  null comment '版本号',
    step_id         int         default 0  null comment '调整的等级Id',
    step_name       varchar(20) default '' null comment '调整的等级',
    appeal_files    text                   null,
    appeal_pictures text                   null
)
    comment '考核任务申诉明细';

create table perf_evaluate_task_audit
(
    id                      varchar(50)      not null
        primary key,
    company_id              varchar(50)      null comment '公司id',
    task_id                 varchar(50)      null comment '考核任务id',
    emp_id                  varchar(50)      null comment '被考核人id',
    org_id                  varchar(50)      null comment '被考核人部门id',
    kpi_item_id             varchar(50)      null comment '指标id',
    scene                   varchar(50)      null comment '考核场景（确认任务/上级评分/结果审批）',
    approval_order          int              null comment '审批顺序（从1开始递增）',
    approver_type           varchar(30)      null comment '审批人类型（指定级别主管/指定人员）',
    approver_info           text             null comment '指定对象id（级别）',
    weight                  decimal(10, 2)   null comment '权重',
    multiple_reviewers_type varchar(30)      null comment '多个审核人时会签还是或签',
    transfer_flag           varchar(10)      null comment '是否可转交',
    vacancy_approver_type   varchar(30)      null comment '审批人空缺时指定人类型（管理员/指定人员）',
    vacancy_approver_info   text             null comment '审批人空缺时指定人id',
    status                  varchar(50)      null,
    is_deleted              varchar(10)      null,
    created_user            varchar(50)      null comment '创建用户',
    created_time            datetime         null comment '创建时间',
    updated_user            varchar(50)      null comment '修改用户',
    updated_time            datetime         null comment '修改时间',
    kpi_type_id             varchar(50)      null comment '指标类id',
    modify_flag             varchar(10)      null comment '是否可以修改',
    last_audit_id           varchar(50)      null comment '上个审核节点id',
    version                 int    default 0 null comment '版本号',
    audit_open              int    default 1 null comment '执行阶段变更指标是否打开审核流程,默认打开 1=开,0=关 ',
    task_user_id            varchar(50)      null comment '员工考核id,task_user.id',
    merge_weight_del        int(1) default 0 null comment '合并权重是标记删除的audit，重置时候需要恢复'
)
    comment '考核任务-审批执行' charset = utf8mb4;

create index company_id
    on perf_evaluate_task_audit (company_id);

create index group_key
    on perf_evaluate_task_audit (company_id, task_id, scene, is_deleted);

create index k_task_user_id
    on perf_evaluate_task_audit (task_user_id);

create table perf_evaluate_task_audit_init
(
    id                      varchar(50)    not null
        primary key,
    company_id              varchar(50)    null comment '公司id',
    temp_base_id            varchar(50)    null comment '模板基础id',
    task_id                 varchar(50)    null comment '考核任务id',
    emp_id                  varchar(50)    null comment '被考核员工id',
    kpi_item_id             varchar(50)    null comment '指标id',
    scene                   varchar(30)    null comment '考核场景（确认任务/上级评分/结果审批）',
    approval_order          int            null comment '审批顺序（从1开始递增）',
    approver_type           varchar(30)    null comment '审批人类型（指定级别主管/指定人员）',
    approver_info           varchar(200)   null comment '指定对象id（级别）',
    superior_score_weight   decimal(10, 2) null comment '上级评分权重',
    multiple_reviewers_type varchar(30)    null comment '多个责任人或签还是会签',
    transfer_flag           varchar(10)    null comment '是否可转交',
    vacancy_approver_type   varchar(30)    null comment '审批人空缺时指定人类型（管理员/指定人员）',
    vacancy_approver_info   varchar(200)   null comment '审批人空缺时指定人id',
    score_rule              varchar(50)    null comment '评分规则，total对考核任务打总分，item对每个指标打分',
    status                  varchar(30)    null comment '当前节点状态',
    created_user            varchar(50)    null comment '创建用户',
    created_time            datetime       null comment '创建时间',
    updated_user            varchar(50)    null comment '修改用户',
    updated_time            datetime       null comment '修改时间',
    is_deleted              varchar(10)    null,
    is_default              varchar(10)    null comment '是否默认配置，true/false',
    kpi_type_id             varchar(50)    null comment '指标类id',
    modify_flag             varchar(10)    null comment '是否可以修改',
    version                 int default 0  null comment '版本号'
)
    comment '新建任务时缓存的模板审核人' charset = utf8mb4;

create index company_id
    on perf_evaluate_task_audit_init (company_id);

create table perf_evaluate_task_audit_test
(
    id                      varchar(50)      not null
        primary key,
    company_id              varchar(50)      null comment '公司id',
    task_id                 varchar(50)      null comment '考核任务id',
    emp_id                  varchar(50)      null comment '被考核人id',
    org_id                  varchar(50)      null comment '被考核人部门id',
    kpi_item_id             varchar(50)      null comment '指标id',
    scene                   varchar(50)      null comment '考核场景（确认任务/上级评分/结果审批）',
    approval_order          int              null comment '审批顺序（从1开始递增）',
    approver_type           varchar(30)      null comment '审批人类型（指定级别主管/指定人员）',
    approver_info           text             null comment '指定对象id（级别）',
    weight                  decimal(10, 2)   null comment '权重',
    multiple_reviewers_type varchar(30)      null comment '多个审核人时会签还是或签',
    transfer_flag           varchar(10)      null comment '是否可转交',
    vacancy_approver_type   varchar(30)      null comment '审批人空缺时指定人类型（管理员/指定人员）',
    vacancy_approver_info   text             null comment '审批人空缺时指定人id',
    status                  varchar(50)      null,
    is_deleted              varchar(10)      null,
    created_user            varchar(50)      null comment '创建用户',
    created_time            datetime         null comment '创建时间',
    updated_user            varchar(50)      null comment '修改用户',
    updated_time            datetime         null comment '修改时间',
    kpi_type_id             varchar(50)      null comment '指标类id',
    modify_flag             varchar(10)      null comment '是否可以修改',
    last_audit_id           varchar(50)      null comment '上个审核节点id',
    version                 int    default 0 null comment '版本号',
    audit_open              int    default 1 null comment '执行阶段变更指标是否打开审核流程,默认打开 1=开,0=关 ',
    task_user_id            varchar(50)      null comment '员工考核id,task_user.id',
    merge_weight_del        int(1) default 0 null comment '合并权重是标记删除的audit，重置时候需要恢复'
)
    comment '考核任务-审批执行:测试用' charset = utf8mb4;

create index company_id
    on perf_evaluate_task_audit_test (company_id);

create index k_task_user_id
    on perf_evaluate_task_audit_test (task_user_id);

create table perf_evaluate_task_base
(
    id                     varchar(50)                                     not null
        primary key,
    company_id             varchar(50)                                     null comment '公司id',
    task_name              varchar(250)                                    null comment '考核任务名称',
    templ_base_id          varchar(50)                                     null comment '考核模板id',
    templ_name             varchar(200)                                    null comment '考核模板名称',
    cycle_start_date       varchar(20)                                     null comment '考核周期起始',
    cycle_end_date         varchar(20)                                     null comment '考核周期截止',
    task_desc              text                                            null comment '考核任务描述',
    evaluation_staff       text                                            null comment '考核员工JSON对象；[{"obj_type":"对象类型(部门/角色/岗位/指定员工)",“objItems”:[{"objId":"对象id","objName":"对象名称"}]}]',
    exclude_staff          text                                            null comment '排除员工JSON对象；[{"empId":"员工id","empName":"员工姓名"}]',
    create_task_type       varchar(20)                                     null comment '发送考核方式（自动/手动）',
    create_task_date_type  varchar(20)                                     null comment '发起日期类型（开始前/开始后）',
    day                    int                                             null comment '发起日期值',
    visible_type           varchar(30)                                     null comment '可见范围类型(公司/部门/自己和上级)',
    task_status            varchar(30)                                     null comment '考核任务状态',
    is_deleted             varchar(10)                                     null comment '是否删除',
    created_user           varchar(50)                                     null comment '创建用户',
    created_time           datetime                                        null comment '创建时间',
    updated_user           varchar(50)                                     null comment '修改用户',
    updated_time           datetime                                        null comment '修改时间',
    cycle_type             varchar(50)                                     null comment '考核周期类型',
    templ_desc             text                                            null comment '模板描述',
    templ_item_json        longtext                                        null comment '模板关联的指标json',
    templ_initiate_json    text                                            null comment '模板发起考核任务配置json',
    templ_affirm_json      text                                            null comment '模板确认指标配置json',
    templ_evaluate_json    text                                            null comment '模板评价配置json',
    score_start_rule_type  varchar(50)                                     null comment '评分开始时间规则类型（周期结束前/后）',
    score_start_rule_day   int                                             null comment '评分开始时间规则值',
    enter_score_method     varchar(30)                                     null comment '进入评分方式，手动manual，自动auto',
    public_type            varchar(50)                                     null comment '考核结果公示类型：auto:实时自动公示，afterFinished: 考核任务完成后自动公示，manual:考核任务完成后，由发起人手动公示',
    templ_base_json        text                                            null comment '模板基础信息json',
    evaluate_type          varchar(20)                                     null comment '评分流程类型 ： simple-简易流程，360-360°考核流程，custom-自定义流程',
    public_emp_json        text                                            null comment '手动公示人信息',
    templ_execute_json     text                                            null comment '模板执行阶段配置',
    result_affirm          varchar(10) collate utf8mb4_bin                 null comment '结果是否需（被考核人）确认；true/false',
    affirm_signature       varchar(10) collate utf8mb4_bin                 null comment '确认是否需（被考核人）签名；true/false',
    templ_points_json      longtext                                        null,
    can_appeal             varchar(10) collate utf8mb4_bin default ''      null comment '是否可申诉；true/false',
    appeal_receiver        text collate utf8mb4_bin                        null comment '申述受理人；格式：[{"obj_type":"user","objItems":[{"objId":"员工id","objName":"姓名"}]}]',
    custom_full_score      decimal(10, 2)                                  null comment '自定义满分分值',
    enter_score_emp_type   int                                             null comment ' 发起评分的人员type=1管理员,type=2考核员工',
    public_dimension       int                             default 3       null comment '公示维度',
    public_to_emp          text                                            null comment '公示范围',
    auto_result_affirm     varchar(10)                     default 'false' null comment '自动确认结果/true/false',
    auto_result_affirm_day int                             default 0       null comment '自动确认结果限制的时间',
    level_group_id         varchar(50)                     default ''      null comment '关联哪个等级组/perf_evaluation_level 的level_group_id',
    version                int                             default 0       null comment '版本号',
    cycle_id               bigint                          default 0       null comment '周期id',
    is_new_emp             int                             default 0       null comment '是否有新人任务',
    total_cnt              int                             default 0       null comment '参与人数',
    draw_up_cnt            int                             default 0       null comment '已制定人数',
    start_cnt              int                             default 0       null comment '已发起人数',
    finish_cnt             int                             default 0       null comment '已完成人数',
    confirm_task           text                                            null comment '确认任务 对应模板 templAffirmJson:PerfTemplEvaluateAffirm',
    edit_exe_indi          text                                            null comment '执行中修改指标  对应模板 templExecuteJson:PerfTemplEvaluateExecute',
    enter_score            varchar(128)                    default ''      null comment '启动评分配置 ,多字段合并',
    audit_result           text                                            null comment '结果校准,从templEvaluateJson:PerfTemplEvaluate 模板评价配置json 单独出来的',
    publish_result         text                                            null comment '结果公示,多个字段合并',
    comment_conf           text                                            null comment '评语与总结配置',
    score_sort_conf        text                                            null comment '2.0.0新版:新加字段 评分环节顺序配置',
    appeal_conf            varchar(512)                                    null comment '结果申诉,多个字段合并',
    score_view             text                                            null comment '可见范围配置 json',
    confirm_result         text                                            null comment '结果确认,多个字段合并',
    performance_type       int                             default 1       null comment '绩效类型 1=个人绩效，2=组织绩效',
    score_conf             text                                            null comment '评分设置: 会签/或签、是否允许转交',
    finish_value_audit     text                                            null comment '完成值审核规则配置',
    dead_line_conf         text                                            null comment '阶段截止时间配置'
)
    comment '考核任务基础信息' charset = utf8mb4;

create index company_id
    on perf_evaluate_task_base (company_id);

create index idx_cycle_id
    on perf_evaluate_task_base (cycle_id);

create table perf_evaluate_task_coach
(
    id             varchar(50)                  not null
        primary key,
    company_id     varchar(50)                  null,
    task_user_id   varchar(50)                  null comment '任务用户id',
    summary        text                         null comment '总结',
    suggestion     varchar(500)                 null comment '改进建议',
    files          text                         null comment '附件url，多个逗号分隔',
    emp_id         varchar(50)                  null comment '辅导员工id',
    emp_name       varchar(50)                  null comment '辅导员工姓名',
    emp_avatar     varchar(200)                 null comment '辅导员工头像',
    is_deleted     varchar(10)                  null comment '是否删除',
    created_time   datetime                     null comment '创建时间',
    updated_time   datetime                     null comment '修改时间',
    coach_type     varchar(20)                  null comment '辅导模型;  RAP模型：rap，标准模型：standard，自定义：custom',
    coach_content  text                         null comment '辅导内容',
    is_read        varchar(10)  default 'false' null comment '已读标识',
    company_msg_id varchar(100) default ''      null comment '工作台代办消息id',
    version        int          default 0       null comment '版本号'
)
    comment '绩效任务面谈辅导';

create index company_id
    on perf_evaluate_task_coach (company_id);

create table perf_evaluate_task_discuss
(
    id              varchar(50) collate utf8mb4_bin  not null
        primary key,
    company_id      varchar(50) collate utf8mb4_bin  null,
    task_user_id    varchar(50) collate utf8mb4_bin  null,
    type            varchar(20) collate utf8mb4_bin  null comment '评论类型（主评论/回复评论）',
    main_discuss_id varchar(50) collate utf8mb4_bin  null comment '主评论id',
    emp_id          varchar(50) collate utf8mb4_bin  null comment '评论员工id',
    emp_name        varchar(50) collate utf8mb4_bin  null comment '评论员工姓名',
    emp_avatar      varchar(200) collate utf8mb4_bin null comment '评论员工头像',
    content         varchar(500) collate utf8mb4_bin null comment '评论内容',
    content_att     text collate utf8mb4_bin         null comment '评论附件',
    seq             int(3)                           null comment '评论顺序',
    kpi_item_id     varchar(50) collate utf8mb4_bin  null,
    is_deleted      varchar(10) collate utf8mb4_bin  null comment '是否删除',
    created_time    datetime                         null,
    updated_time    datetime                         null,
    version         int default 0                    null comment '版本号'
)
    comment '绩效任务评论' charset = utf8mb4;

create index company_id
    on perf_evaluate_task_discuss (company_id);

create index kpi_item_id
    on perf_evaluate_task_discuss (kpi_item_id);

create index task_user_id
    on perf_evaluate_task_discuss (task_user_id);

create table perf_evaluate_task_file
(
    id           varchar(50)   not null
        primary key,
    company_id   varchar(50)   null,
    task_user_id varchar(50)   null,
    type         varchar(20)   null comment '业务类型',
    content      varchar(500)  null comment '评论内容',
    file_url     text          null comment '评论附件',
    kpi_item_id  varchar(50)   null,
    is_deleted   varchar(10)   null comment '是否删除',
    created_time datetime      null,
    updated_time datetime      null,
    file_id      varchar(255)  null,
    file_name    varchar(255)  null,
    file_size    int           null,
    file_type    varchar(50)   null,
    space_id     varchar(50)   null,
    created_user varchar(50)   null,
    updated_user varchar(50)   null,
    version      int default 0 null comment '版本号'
)
    comment '任务附件表';

create index company_id
    on perf_evaluate_task_file (company_id);

create table perf_evaluate_task_formula_field
(
    id                  varchar(50)    not null
        primary key,
    company_id          varchar(50)    null,
    task_user_id        varchar(50)    null comment 'task user id',
    task_id             varchar(50)    null comment '考核任务id',
    kpi_item_id         varchar(50)    null comment '指标id',
    company_field_id    varchar(50)    null comment '公司公式字段id',
    formula_field_name  varchar(100)   null comment '公式里的字段名称',
    formula_field_value decimal(18, 2) null comment '公式里的字段值',
    is_deleted          varchar(10)    null,
    created_user        varchar(50)    null,
    created_time        datetime       null,
    updated_user        varchar(50)    null,
    updated_time        datetime       null,
    version             int default 0  null comment '版本号'
)
    comment '任务指标公式字段表';

create index company_id
    on perf_evaluate_task_formula_field (company_id);

create index task_user_id
    on perf_evaluate_task_formula_field (task_user_id);

create table perf_evaluate_task_item_score_rule
(
    id                       varchar(50)    not null
        primary key,
    company_id               varchar(50)    null comment '公司id',
    task_id                  varchar(50)    null comment '任务id',
    task_user_id             varchar(50)    null,
    kpi_item_id              varchar(50)    null comment '指标项id',
    self_score_flag          varchar(10)    null comment '是否自评',
    self_score_view_rule     varchar(500)   null comment '被考核人查看评分规则JSON',
    self_score_weight        decimal(10, 2) null comment '自评权重',
    mutual_score_flag        varchar(10)    null comment '是否互评',
    mutual_score_attend_rule varchar(50)    null comment '互评参与规则',
    mutual_score_anonymous   varchar(10)    null comment '互评人姓名是否匿名',
    mutual_score_vacancy     varchar(50)    null comment '互评人空缺时规则',
    mutual_score_view_rule   varchar(500)   null comment '互评人查看评分规则JSON',
    peer_score_weight        decimal(10, 2) null comment '同级互评权重',
    sub_score_weight         decimal(10, 2) null comment '下级互评权重',
    superior_score_flag      varchar(10)    null comment '是否上级评',
    superior_score_view_rule varchar(500)   null comment '上级评人查看评分规则JSON',
    superior_score_weight    decimal(10, 2) null comment '上级评权重',
    superior_score_vacancy   varchar(50)    null comment '上级评分人空缺时规则',
    appoint_score_flag       varchar(10)    null comment '是否指定评分',
    appoint_score_weight     decimal(10, 2) null comment '指定评分权重',
    created_user             varchar(50)    null comment '创建用户',
    created_time             datetime       null comment '创建时间',
    updated_user             varchar(50)    null comment '修改用户',
    updated_time             datetime       null comment '修改时间',
    is_deleted               varchar(10)    null,
    kpi_type_id              varchar(50)    null comment '指标类id',
    mutual_user_type         varchar(30)    null comment '设置互评人类型，all:为所有被考核人设置相同的互评人; user: 为每位被考核人分别设置互评人; exam：由被考核人自行指定',
    version                  int default 0  null comment '版本号',
    mutual_user_value        text           null comment '自定义互评设置人',
    self_rater               text           null comment ' 自评人',
    mutual_rater             text           null comment '互评人 json',
    super_rater              text           null comment '上级人 json',
    appoint_rater            text           null comment '指定评分人json',
    peer_rater               text           null comment '自定义同级互评人',
    sub_rater                text           null comment '自定义下级互评人',
    default_eval             int default 0  null comment '是否默认评分流程'
)
    comment '指标自定义流程' charset = utf8mb4;

create index company_id
    on perf_evaluate_task_item_score_rule (company_id);

create table perf_evaluate_task_kpi
(
    id                        varchar(50)                 not null
        primary key,
    company_id                varchar(50)                 null comment '公司id',
    org_id                    varchar(50)                 null comment '部门id',
    task_id                   varchar(50)                 null comment '考核任务id',
    emp_id                    varchar(50)                 null comment '被考核人id',
    task_user_id              varchar(50)                 null comment 'task user 表的id',
    reviewer                  text                        null comment '审核人信息',
    kpi_type_id               varchar(50)                 null comment '指标类id',
    kpi_type_name             varchar(200)                null comment '指标类名称',
    kpi_type_weight           decimal(10, 2)              null comment '指标类权重',
    kpi_item_id               varchar(50)                 null comment '指标项id',
    kpi_item_name             text                        null comment '指标项名称',
    item_target_value         decimal(18, 2)              null comment '指标项目标值',
    item_finish_value         decimal(18, 2)              null comment '指标项完成值',
    item_unit                 varchar(20)                 null comment '指标项单位',
    item_weight               decimal(10, 2)              null comment '指标项权重',
    result_input_type         varchar(50)                 null comment '结果录入类型',
    result_input_emp_id       text                        null comment '结果录入人id',
    backup                    varchar(500)                null comment '备份，用于指标变更审核时的恢复操作',
    examine_oper_type         varchar(30)                 null comment '被考核人确认指标时操作类型：增删改',
    is_deleted                varchar(10)                 null comment '是否删除',
    created_user              varchar(50)                 null comment '创建用户',
    created_time              datetime                    null comment '创建时间',
    updated_user              varchar(50)                 null comment '修改用户',
    updated_time              datetime                    null comment '修改时间',
    item_rule                 text                        null comment '考核规则',
    scoring_rule              text                        null comment '计分规则',
    scorer_type               varchar(50)                 null comment '指标评分人类型（按评分流程/指定员工/指定主管）',
    scorer_obj_id             text                        null comment '指定评分人json串',
    item_type                 varchar(50)                 null comment '指标项类型（量化/非量化）',
    multiple_reviewers_type   varchar(50)                 null comment '多人审核时，and会签，or或签',
    kpi_type_classify         varchar(50)                 null comment '指标类别，为空表示已有类别，custom自定义/plus加分项/subtract减分项',
    plus_limit                decimal(10, 2)              null comment '加分上限',
    subtract_limit            decimal(10, 2)              null comment '减分上限',
    max_extra_score           decimal(10, 2)              null comment '本类别最大加减分上限',
    `order`                   int                         null comment '排序，数字小的排前面',
    item_formula              text                        null comment '指标计算公式',
    item_auto_score           decimal(10, 3)              null comment '指标自动计算后的分数',
    threshold_json            text                        null comment '量化指标阈值设置json',
    urging_flag               varchar(10)                 null comment '完成值录入催办标识',
    item_actual_formula       text                        null comment '代入真实值后的计算公式',
    auto_score_ex_flag        varchar(10)                 null comment '自动算分异常标识',
    formula_condition         text                        null comment '公式条件',
    item_field_json           text                        null comment '指标关联的阈值字段',
    is_type_locked            varchar(100)                null comment '类别锁定类型',
    is_okr                    varchar(10)                 null comment '是否OKR类别，true/false',
    type_order                int(10)                     null comment '类别排序',
    okr_ref_flag              varchar(20)                 null comment '指标被OKR关联的标记',
    reserve_okr_weight        decimal(10, 2)              null comment '预留OKR权重',
    points_rule               varchar(30)                 null comment '积分规则',
    points_num                decimal(10, 2)              null comment '指标积分',
    item_score_value          varchar(500)                null comment '指标评分分值',
    input_format              varchar(50)                 null comment '录入格式',
    work_item_finish_value    varchar(2000)               null comment '工作事项完成情况说明',
    item_plan_flag            varchar(10)                 null comment '是否同步了年度指标计划',
    show_target_value         varchar(10)                 null comment '是否展示目标值',
    must_result_input         tinyint(1)  default 0       null comment '指标完成值是否必需录入',
    show_finish_bar           int         default 1       null comment '完成度进度条 默认开启 1=显示,0=不显示',
    version                   int         default 0       null comment '版本号',
    is_new_emp                int         default 0       null comment '是否新人培训指标',
    manager_level             varchar(10)                 null comment '录入主管等级',
    item_full_score_cfg       varchar(10) default 'false' null comment '自动计算指标得分满分值',
    final_submit_finish_value int(1)      default 0       null comment '最终提交完成值标识',
    item_limit_cnt            varchar(255)                null comment '类别指标数量最大最小限制',
    item_finish_value_text    varchar(2000)               null comment '非量化指标完成值文本录入',
    open_okr_score            int         default 0       null comment '指标评分使用的是okr的分数',
    okr_score                 decimal(10, 3)              null comment 'okr的原始分数',
    plus_sub_interval         varchar(50)                 null comment '考核规则指标加减分上限',
    item_custom_field_json    text                        null comment '指标自定义字段json',
    formula_type              int(1)      default 1       null comment '公式类型：1=多场景公式、2=高级公式',
    ind_level_group           text                        null comment '指标等级组',
    ind_level                 varchar(100)                null comment '提交的等级',
    ind_level_group_id        bigint                      null comment '指标等级组id',
    finish_value_audit        text                        null comment '完成值审核人列表',
    item_target_value_text    text                        null comment '指标项文本类型目标值'
)
    comment '考核任务--关联指标' charset = utf8mb4;

create index company_id
    on perf_evaluate_task_kpi (company_id);

create index index_empId
    on perf_evaluate_task_kpi (emp_id);

create index item_id
    on perf_evaluate_task_kpi (company_id, is_deleted, kpi_item_id, kpi_type_id);

create index k_task_user_id
    on perf_evaluate_task_kpi (task_user_id);

create table perf_evaluate_task_okr_type
(
    id                 varchar(50)    not null
        primary key,
    company_id         varchar(50)    null,
    task_id            varchar(50)    null,
    emp_id             varchar(50)    null,
    task_user_id       varchar(50)    null,
    kpi_type_id        varchar(50)    null comment '指标类id',
    kpi_type_name      varchar(200)   null comment '指标类名称',
    kpi_type_weight    decimal(10, 2) null comment '指标类权重',
    kpi_type_classify  varchar(255)   null comment '指标类别，为空表示已有类别，custom自定义/plus加分项/subtract减分项',
    max_extra_score    decimal(10, 2) null comment '本类别最大加减分上限',
    type_order         int            null comment '排序，数字小的排前面',
    is_type_locked     varchar(100)   null comment '类别锁定类型',
    is_okr             varchar(10)    null comment '是否OKR类别，true/false',
    import_okr_flag    varchar(10)    null comment '是否已导入OKR',
    reserve_okr_weight decimal(10, 2) null comment '预留OKR权重',
    is_deleted         varchar(10)    null,
    created_user       varchar(50)    null,
    created_time       datetime       null,
    updated_user       varchar(50)    null,
    updated_time       datetime       null,
    version            int default 0  null comment '版本号',
    open_okr_score     int default 0  null comment '指标评分使用的是okr的分数'
)
    comment '任务关联的okr类型';

create index company_id
    on perf_evaluate_task_okr_type (company_id);

create table perf_evaluate_task_ref_okr
(
    id                  varchar(50)   not null
        primary key,
    company_id          varchar(50)   null,
    task_id             varchar(50)   null,
    emp_id              varchar(50)   null,
    task_kpi_id         varchar(50)   null,
    action_id           varchar(50)   null comment '成果id',
    action_name         varchar(255)  null comment '成果名称',
    target_id           varchar(50)   null comment '目标id',
    target_name         varchar(255)  null comment 'OKR目标名称',
    okr_task_id         varchar(50)   null comment 'OKR任务id',
    okr_task_name       varchar(255)  null comment 'OKR任务名',
    cycle               varchar(255)  null comment 'OKR周期',
    evaluate_start_date varchar(50)   null comment '开始时间',
    evaluate_end_date   varchar(50)   null comment '结束时间',
    unit                varchar(255)  null comment '单位',
    dept_name           varchar(255)  null comment '部门名称',
    user_name           varchar(255)  null comment '员工名称',
    is_deleted          varchar(10)   null,
    created_user        varchar(255)  null,
    created_time        datetime      null,
    updated_user        varchar(255)  null,
    updated_time        datetime      null,
    version             int default 0 null comment '版本号',
    task_user_id        varchar(50)   null comment 'task user 表的id'
)
    comment '任务导入的OKR';

create index company_id
    on perf_evaluate_task_ref_okr (company_id);

create index idx_task_user_id
    on perf_evaluate_task_ref_okr (task_user_id);

create table perf_evaluate_task_score_result
(
    id                          varchar(50)                  not null
        primary key,
    company_id                  varchar(50)                  null comment '公司id',
    task_id                     varchar(50)                  null comment '考核任务id',
    org_id                      varchar(50)                  null comment '部门id',
    emp_id                      varchar(50)                  null comment '被考核人id',
    kpi_type_id                 varchar(50)                  null comment '指标类id',
    kpi_item_id                 varchar(50)                  null comment '指标项id',
    scorer_type                 varchar(50)                  null comment '评分人类型（自评/同级互评/下级互评/上级评/指定评分人）',
    scorer_id                   varchar(50)                  null comment '评分人id',
    score                       decimal(10, 2)               null comment '评分分数',
    score_weight                decimal(10, 2)               null comment '评分权重',
    score_comment               text                         null comment '评分评语',
    score_att_url               text                         null comment '评分附件url',
    reviewers_type              varchar(30)                  null comment '会签还是或签',
    audit_status                varchar(30)                  null comment '审核状态',
    final_score                 decimal(10, 3)               null comment '没算阶段权重的分数',
    transfer_id                 varchar(1000)                null comment '转交来源id',
    approval_order              int                          null comment '审批顺序',
    created_user                varchar(50)                  null comment '创建用户',
    created_time                datetime                     null comment '创建时间',
    updated_user                varchar(50)                  null comment '修改用户',
    updated_time                datetime                     null comment '修改时间',
    plus_score                  decimal(10, 2)               null comment '加分',
    subtract_score              decimal(10, 2)               null comment '减分',
    final_plus_score            decimal(10, 2)               null comment '加分加权计算得分',
    final_subtract_score        decimal(10, 2)               null comment '减分加权计算后得分',
    final_weight_score          decimal(10, 3)               null comment '最终加权后的分数',
    final_weight_plus_score     decimal(10, 2)               null comment '最终加权后的加分总分',
    final_weight_subtract_score decimal(10, 2)               null comment '最终加权后的减分总分',
    score_level                 varchar(255)                 null comment '打分人提交的绩效等级',
    no_item_score               decimal(10, 3)               null comment '没算指标权重的分数',
    emp_score                   decimal(10, 3)               null comment '评分人加权后的分数',
    is_deleted                  varchar(10)                  null,
    task_audit_id               varchar(50)                  null comment '审核节点id',
    modify_flag                 varchar(10)                  null comment '是否可以修改',
    version                     int            default 0     null comment '版本号',
    task_user_id                varchar(50)                  null comment '员工考核id,task_user.id',
    merge_rs_infos              varchar(1024)                null comment '相同评分人合并权重后记录被合并的rs信息',
    calibration_type            int                          null comment '校准类型（1：按人员校准 2：按分组校准 3：按指标评分校准）',
    operate_reason              varchar(255)                 null comment '校准内容',
    index_calibration           text                         null comment '指标校准时，用于存放json字符串',
    perf_coefficient            varchar(10)                  null comment '绩效系数',
    merge_weight                decimal(10, 3) default 0.000 null comment '合并的权重',
    veto_flag                   varchar(10)                  null comment '否决标识(true为否决，false为不否决)'
)
    comment '考核任务-评分结果' charset = utf8;

create index idx_cid
    on perf_evaluate_task_score_result (company_id);

create index idx_emp_id
    on perf_evaluate_task_score_result (emp_id);

create index item_id
    on perf_evaluate_task_score_result (kpi_item_id);

create index k_cid
    on perf_evaluate_task_score_result (company_id);

create index k_scorer_id
    on perf_evaluate_task_score_result (scorer_id);

create index k_task_user_id
    on perf_evaluate_task_score_result (task_user_id);

create table perf_evaluate_task_score_result_tmp
(
    id                          varchar(50)    not null
        primary key,
    company_id                  varchar(50)    null comment '公司id',
    task_id                     varchar(50)    null comment '考核任务id',
    org_id                      varchar(50)    null comment '部门id',
    emp_id                      varchar(50)    null comment '被考核人id',
    kpi_type_id                 varchar(50)    null comment '指标类id',
    kpi_item_id                 varchar(50)    null comment '指标项id',
    scorer_type                 varchar(50)    null comment '评分人类型（自评/同级互评/下级互评/上级评/指定评分人）',
    scorer_id                   varchar(50)    null comment '评分人id',
    score                       decimal(10, 2) null comment '评分分数',
    score_weight                decimal(10, 2) null comment '评分权重',
    score_comment               text           null comment '评分评语',
    score_att_url               text           null comment '评分附件url',
    reviewers_type              varchar(30)    null comment '会签还是或签',
    audit_status                varchar(30)    null comment '审核状态',
    final_score                 decimal(10, 3) null comment '没算阶段权重的分数',
    transfer_id                 varchar(1000)  null comment '转交来源id',
    approval_order              int            null comment '审批顺序',
    created_user                varchar(50)    null comment '创建用户',
    created_time                datetime       null comment '创建时间',
    updated_user                varchar(50)    null comment '修改用户',
    updated_time                datetime       null comment '修改时间',
    plus_score                  decimal(10, 2) null comment '加分',
    subtract_score              decimal(10, 2) null comment '减分',
    final_plus_score            decimal(10, 2) null comment '加分加权计算得分',
    final_subtract_score        decimal(10, 2) null comment '减分加权计算后得分',
    final_weight_score          decimal(10, 3) null comment '最终加权后的分数',
    final_weight_plus_score     decimal(10, 2) null comment '最终加权后的加分总分',
    final_weight_subtract_score decimal(10, 2) null comment '最终加权后的减分总分',
    score_level                 varchar(255)   null comment '打分人提交的绩效等级',
    no_item_score               decimal(10, 3) null comment '没算指标权重的分数',
    emp_score                   decimal(10, 3) null comment '评分人加权后的分数',
    is_deleted                  varchar(10)    null,
    task_audit_id               varchar(50)    null comment '审核节点id',
    modify_flag                 varchar(10)    null comment '是否可以修改',
    version                     int default 0  null comment '版本号'
)
    comment '考核任务-评分结果' charset = utf8;

create index company_id
    on perf_evaluate_task_score_result_tmp (company_id);

create index item_id
    on perf_evaluate_task_score_result_tmp (kpi_item_id);

create index task_and_emp
    on perf_evaluate_task_score_result_tmp (task_id, emp_id);

create table perf_evaluate_task_score_rule
(
    id                  varchar(50)   not null
        primary key,
    company_id          varchar(50)   null comment '公司id',
    task_id             varchar(50)   null comment '考核任务id',
    emp_id              varchar(50)   null comment '被考核人id',
    org_id              varchar(50)   null,
    task_user_id        varchar(50)   null,
    need_self_score     varchar(10)   null comment '是否自评',
    peer_score_json     longtext      null comment '同级互评人列表',
    sub_score_json      longtext      null comment '下级互评人列表',
    superior_score_json text          null comment '上级评分人列表',
    result_audit_json   text          null comment '考核结果审批人列表',
    is_deleted          varchar(10)   null,
    created_user        varchar(50)   null comment '创建用户',
    created_time        datetime      null comment '创建时间',
    updated_user        varchar(50)   null comment '修改用户',
    updated_time        datetime      null comment '修改时间',
    version             int default 0 null comment '版本号'
)
    comment '考核任务-指标评分规则' charset = utf8mb4;

create index company_id
    on perf_evaluate_task_score_rule (company_id);

create table perf_evaluate_task_user
(
    id                            varchar(50)                      not null
        primary key,
    company_id                    varchar(50)                      null comment '公司id',
    task_id                       varchar(50)                      null comment '考核任务id',
    emp_id                        varchar(50)                      null comment '被考核用户id',
    org_id                        varchar(50)                      null,
    task_status                   varchar(50)                      null comment '任务状态',
    final_score                   decimal(11, 2)                   null comment '考核最终评分',
    original_final_score          decimal(11, 2)                   null comment '原考核分数',
    evaluation_level              varchar(20)                      null comment '考评等级',
    reviewers_json                text                             null comment '流程责任人列表',
    self_score_flag               varchar(10)                      null comment '自评完成标识，true标识已完成',
    manual_score_flag             varchar(10)                      null comment '互评完成标识，true表示已完成',
    superior_score_flag           varchar(10)                      null comment '上级评分完成标识',
    item_score_flag               varchar(10)                      null comment '指标评分完成标识',
    result_audit_flag             varchar(10)                      null comment '最终结果审核完成标识',
    final_self_score              decimal(10, 3)                   null comment '自评最终得分',
    final_peer_score              decimal(10, 3)                   null comment '同级互评最终得分',
    final_sub_score               decimal(10, 3)                   null comment '下级互评最终得分',
    final_superior_score          decimal(10, 3)                   null comment '上级评分最终得分',
    final_item_score              decimal(10, 3)                   null comment '指定指标评分人最终得分',
    last_score_comment            varchar(255)                     null comment '最近一条评分审核修改理由',
    task_confirm_time             datetime                         null comment '任务确认时间',
    task_score_start_time         datetime                         null comment '进入评分时间',
    is_deleted                    varchar(10)                      null comment '是否删除',
    created_user                  varchar(50)                      null comment '创建用户',
    created_time                  datetime                         null comment '创建时间',
    updated_user                  varchar(50)                      null comment '修改用户',
    updated_time                  datetime                         null comment '修改时间',
    final_plus_score              decimal(10, 2)                   null comment '最终加分',
    final_subtract_score          decimal(10, 2)                   null comment '最终减分',
    public_flag                   varchar(10)                      null comment '是否已公示',
    final_self_plus_score         decimal(10, 3)                   null comment '最终自评加分',
    final_peer_plus_score         decimal(10, 3)                   null comment '最终同级互评加分',
    final_sub_plus_score          decimal(10, 3)                   null comment '最终下级互评加分',
    final_superior_plus_score     decimal(10, 3)                   null comment '最终上级加分',
    final_self_subtract_score     decimal(10, 3)                   null comment '最终自评加减分',
    final_peer_subtract_score     decimal(10, 3)                   null comment '最终同级互评减分',
    final_sub_subtract_score      decimal(10, 3)                   null comment '最终下级互评减分',
    final_superior_subtract_score decimal(10, 3)                   null comment '最终上级减分',
    final_item_plus_score         decimal(10, 3)                   null comment '指定评分人最终加分',
    final_item_subtract_score     decimal(10, 3)                   null comment '指定评分人最终减分',
    final_item_auto_score         decimal(10, 3)                   null comment '自动评分指标最终分数',
    enter_score_flag              varchar(10)                      null comment '已发起评分操作标识',
    original_evaluation_level     varchar(20)                      null comment '原始考评等级',
    distribution_flag             varchar(10)                      null comment '是否经过正态分布',
    adjust_reason                 varchar(255)                     null comment '调整绩效结果理由',
    total_points_num              decimal(10, 2)                   null comment '任务总积分',
    signature_pic                 varchar(500) collate utf8mb4_bin null comment '签名图片',
    item_change_user              varchar(50)                      null comment '指标变更人',
    has_appeal                    varchar(10) collate utf8mb4_bin  null comment '是否有申诉；true/false',
    appeal_receiver_id            varchar(50) collate utf8mb4_bin  null comment '申诉受理人id',
    cc_emp_ids                    text                             null comment '评分抄送人、公示抄送人取值地方',
    in_result_affirm_time         date                             null comment '进入结果确认的时间',
    is_publish                    varchar(10) default 'true'       null comment '某个任务下的某个被考核人是否可公示',
    version                       int         default 0            null comment '版本号',
    all_scored                    varchar(10)                      null comment '所有评分人都评分完成',
    step_id                       int                              null comment '等阶id',
    score_ranges                  text                             null comment '每个人都带着等级分值组走',
    is_new_emp                    int         default 0            null comment '是否新人任务',
    emp_name                      varchar(50) default ''           null comment '员工名字',
    emp_org_name                  varchar(50) default ''           null comment '员工考核部门名',
    appeal_dead_line              varchar(50)                      null comment '结果申诉截止时间',
    eval_table_name               varchar(50)                      null comment '考核表名称',
    cycle_id                      bigint      default 0            null comment '周期id',
    org_name_list                 text                             null,
    emp_org_id                    text                             null,
    rule_conf_status              int         default 0            null comment '考核表配置进度, 0=未配置, 100 = 进行中, 101=进行中有异常  200=完成ok ',
    temp_task                     int         default 1            null comment '是否从模板创建任务,旧数据标识',
    org_changed                   int         default 0            null comment '标记考核人员部门已变更 1=已变更, 0=无变更, 3(11)=有变更已处理',
    input_finish_status           int(1)                           null comment '完成值录入情况：0=不需要录入、1=未录入、2=部分录入、3=全部录入',
    avatar                        varchar(256)                     null comment '头像链接地址',
    confirm_dead_line             varchar(50)                      null comment '指标确认截止时间',
    distribution_before_step_id   int                              null comment '正态分布之前的stepId',
    eval_org_name                 varchar(200)                     null comment '被考核组织名',
    eval_org_id                   varchar(200)                     null comment '被考核组织id',
    score_end_time                datetime                         null comment '评分结束时间',
    weight_of_ref                 decimal(10, 3)                   null comment '在关联绩效的权重',
    score_of_ref                  decimal(10, 3)                   null comment '关联绩效后的得分',
    perf_coefficient              varchar(10)                      null comment '绩效系数',
    original_perf_coefficient     varchar(10)                      null comment '原始绩效系数',
    rule_conf_error               text                             null comment '规则配置异常（401：自评 402：上级 403：下级  404：同级 405：指定 406：定向 501：公示范围异常  502：公示发起人异常   601：申述受理人异常）  ',
    at_org_code_path              text                             null comment '考核时员工所在部门codePath 以|连接',
    at_org_name_path              text                             null comment '考核时员工所在部门名字以|连接',
    at_org_path_hight             int         default 10000        null comment '考核时员工所在部门的高度'
)
    comment '考核任务-用户信息' charset = utf8mb4;

create index company_emp
    on perf_evaluate_task_user (company_id, emp_id, is_deleted);

create index company_id
    on perf_evaluate_task_user (company_id);

create index idx_cycle_id
    on perf_evaluate_task_user (cycle_id);

create index index_eval_org_id
    on perf_evaluate_task_user (eval_org_id);

create index task_id
    on perf_evaluate_task_user (task_id);

create table perf_evaluation_level
(
    id               varchar(50)    not null
        primary key,
    company_id       varchar(50)    null comment '公司id',
    grade_name       varchar(100)   null comment '等级名称',
    grade_lowest     decimal(10, 2) null comment '等级最底分',
    grade_highest    decimal(10, 2) null comment '等级最高分',
    ratio            decimal(10, 2) null comment '等级占比',
    is_system        varchar(10)    null comment '是否是系统设置的等级，是true',
    level_rule       varchar(30)    null comment '等级分布规则，score按分数评定等级，rank按排名强制占比',
    level_group_id   varchar(50)    null comment '绩效分组id',
    is_deleted       varchar(10)    null comment '是否删除',
    created_user     varchar(50)    null comment '创建用户',
    created_time     datetime       null comment '创建时间',
    updated_user     varchar(50)    null comment '修改用户',
    updated_time     datetime       null comment '修改时间',
    `rank`           int            null comment '排名',
    name             varchar(50)    null comment '等级组名称',
    version          int default 0  null comment '版本号',
    step_id          varchar(50)    null comment '等阶id',
    min_append_equal int default 0  null comment '0拼在max上，1拼在min上'
)
    comment '绩效等级设置' charset = utf8mb4;

create index company_id
    on perf_evaluation_level (company_id);

create table perf_evaluation_level_group
(
    id           varchar(50)   not null
        primary key,
    company_id   varchar(50)   null comment '公司id',
    group_name   varchar(100)  null comment '分组名称',
    level_rule   varchar(30)   null comment '等级分布规则，score按分数评定等级，rank按排名强制占比',
    is_deleted   varchar(10)   null comment '是否删除',
    created_user varchar(50)   null comment '创建用户',
    created_time datetime      null comment '创建时间',
    updated_user varchar(50)   null comment '修改用户',
    updated_time datetime      null comment '修改时间',
    version      int default 0 null comment '版本号'
)
    comment '绩效分组' charset = utf8mb4;

create index company_id
    on perf_evaluation_level_group (company_id);

create table perf_evaluation_task_level
(
    id             varchar(50)    not null
        primary key,
    task_id        varchar(100)   not null comment '考核任务id',
    company_id     varchar(50)    null comment '公司id',
    grade_name     varchar(100)   null comment '等级名称',
    grade_lowest   decimal(10, 2) null comment '等级最底分',
    grade_highest  decimal(10, 2) null comment '等级最高分',
    ratio          decimal(10, 2) null comment '等级占比',
    is_system      varchar(10)    null comment '是否是系统设置的等级，是true',
    level_rule     varchar(30)    null comment '等级分布规则，score按分数评定等级，rank按排名强制占比',
    level_group_id varchar(50)    null comment '绩效分组id',
    is_deleted     varchar(10)    null comment '是否删除',
    created_user   varchar(50)    null comment '创建用户',
    created_time   datetime       null comment '创建时间',
    updated_user   varchar(50)    null comment '修改用户',
    updated_time   datetime       null comment '修改时间',
    `rank`         int            null comment '排名',
    name           varchar(50)    null comment '等级组名称',
    version        int default 0  null comment '版本号'
)
    comment '考核任务绩效等级设置' charset = utf8mb4;

create index task_id
    on perf_evaluation_task_level (task_id);

create table perf_item_ref_point_detail
(
    id            varchar(50)    not null
        primary key,
    company_id    varchar(50)    null,
    templ_base_id varchar(50)    null,
    task_user_id  varchar(50)    null,
    ref_id        varchar(50)    null comment '关联指标数据id',
    grade_lowest  decimal(10, 2) null,
    grade_highest decimal(10, 2) null,
    points        decimal(10, 2) null comment '积分分值',
    is_deleted    varchar(10)    null,
    created_user  varchar(50)    null,
    created_time  datetime       null,
    updated_user  varchar(50)    null,
    updated_time  datetime       null,
    version       int default 0  null comment '版本号'
)
    comment '指标配置的积分详情';

create index company_id
    on perf_item_ref_point_detail (company_id);

create table perf_level_group_rel_temp
(
    id             varchar(50)   not null
        primary key,
    company_id     varchar(50)   null comment '公司id',
    level_group_id varchar(50)   null comment '绩效分组id',
    temp_id        varchar(50)   null comment '模板id',
    is_deleted     varchar(10)   null comment '是否删除',
    created_user   varchar(50)   null comment '创建用户',
    created_time   datetime      null comment '创建时间',
    updated_user   varchar(50)   null comment '修改用户',
    updated_time   datetime      null comment '修改时间',
    version        int default 0 null comment '版本号'
)
    comment '绩效分组关联的模板' charset = utf8mb4;

create index company_id
    on perf_level_group_rel_temp (company_id);

create table perf_modification_record
(
    id             varchar(50)   not null
        primary key,
    company_id     varchar(50)   null,
    link_id        varchar(50)   null comment '业务关联id',
    business_scene varchar(50)   null comment '业务场景',
    status         varchar(50)   null comment '数据状态',
    value          text          null comment '缓存的value',
    is_deleted     varchar(10)   null,
    created_user   varchar(50)   null,
    created_time   datetime      null,
    updated_user   varchar(50)   null,
    updated_time   datetime      null,
    version        int default 0 null comment '版本号'
)
    comment '业务数据变更记录表';

create index company_id
    on perf_modification_record (company_id);

create table perf_task_execute_batch
(
    id           varchar(50)   not null
        primary key,
    company_id   varchar(50)   null,
    status       varchar(20)   null comment 'publish：发布中  finish：发布已完成',
    is_deleted   varchar(10)   null,
    created_user varchar(50)   null,
    created_time datetime      null,
    updated_user varchar(50)   null,
    updated_time datetime      null,
    version      int default 0 null comment '版本号',
    is_read      tinyint(1)    null comment '发布状态是否已读'
)
    comment '考核任务批量发起考核批次' charset = utf8mb4;

create index company_key
    on perf_task_execute_batch (company_id);

create table perf_task_execute_batch_detail
(
    id               varchar(50)   not null
        primary key,
    execute_batch_id varchar(50)   null comment 'perf_task_execute_batch.id',
    company_id       varchar(50)   null,
    task_id          varchar(50)   null,
    draw_up_cnt      int default 0 null comment '待发起人数',
    start_cnt        int default 0 null comment '已发起人数',
    error_cnt        int default 0 null comment '异常人数',
    is_deleted       varchar(10)   null,
    created_user     varchar(50)   null,
    created_time     datetime      null,
    updated_user     varchar(50)   null,
    updated_time     datetime      null,
    version          int default 0 null comment '版本号'
)
    comment '考核任务批量发起考核明细' charset = utf8mb4;

create index batch_key
    on perf_task_execute_batch_detail (execute_batch_id);

create index company_key
    on perf_task_execute_batch_detail (company_id);

create index task_key
    on perf_task_execute_batch_detail (task_id);

create table perf_templ_base
(
    id                      varchar(50)                                        not null
        primary key,
    company_id              varchar(50)                                        null comment '公司id',
    name                    varchar(200)                                       null comment '模板名称',
    cycle_type              varchar(50)                                        null comment '考核周期类型',
    templ_desc              text                                               null comment '模板描述',
    status                  varchar(30)                                        null comment '模板的状态，draft草稿，published已发布',
    public_type             varchar(50)                                        null,
    public_emp_json         text                                               null comment '手动公示人信息',
    type_weight_switch      varchar(10)                                        null comment '指标类别权重开关，open/close',
    type_weight_limit_flag  varchar(10)                                        null comment '限制类别权重之和为100%开关',
    score_range_type        varchar(30)                                        null comment '得分范围类型：fullScore满分、weightScore指标加权后的分数',
    is_deleted              varchar(10)                                        null comment '是否删除 true/false',
    created_user            varchar(50)                                        null comment '考核结果公示类型：auto:实时自动公示，afterFinished: 考核任务完成后自动公示，manual:考核任务完成后，由发起人手动公示',
    created_time            datetime                                           null,
    updated_user            varchar(50)                                        null,
    updated_time            datetime                                           null,
    evaluate_type           varchar(20)                                        null comment '评分流程类型 ： simple-简易流程，360-360°考核流程，custom-自定义流程',
    points_rule             varchar(20)                                        null comment '积分规则，open/close',
    public_to_emp           text                                               null comment '公示给哪些人看',
    can_appeal              varchar(10) collate utf8mb4_bin default ''         null comment '是否可申诉；true/false',
    appeal_receiver         text collate utf8mb4_bin                           null comment '申述受理人；格式：[{"obj_type":"user","objItems":[{"objId":"员工id","objName":"姓名"}]}]',
    check_item_weight_flag  varchar(10)                                        null comment '是否校验指标权重之和',
    check_item_weight       decimal                                            null comment '校验指标权重之和',
    result_affirm           varchar(10) collate utf8mb4_bin                    null comment '结果是否需（被考核人）确认；true/false',
    affirm_signature        varchar(10) collate utf8mb4_bin                    null comment '确认是否需（被考核人）签名；true/false',
    base_score              decimal(10, 2)                                     null comment '基准分',
    custom_full_score       decimal(10, 2)                                     null comment '自定义满分分值',
    public_dimension        int                             default 3          null comment '公示维度',
    auto_result_affirm      varchar(10)                     default 'false'    null comment '自动确认结果/true/false',
    auto_result_affirm_day  int                             default 0          null comment '自动确认结果限制的时间',
    level_group_id          varchar(50)                     default ''         null comment '关联哪个等级组/perf_evaluation_level 的level_group_id',
    version                 int                             default 0          null comment '版本号',
    is_new_emp              int                             default 0          null comment '是否新人模板',
    post                    varchar(50)                     default ''         null comment '岗位',
    new_org_ids             text                                               null comment '新人模板的部门ids',
    appeal_node             varchar(50)                     default 'finished' null comment '那个节点审核',
    result_appeal_node      int                             default 70         null comment '哪个节点的结果审核 70=考核任务结束后的结果申诉 50=结果确认中的结果申诉',
    can_appeal_day          int                             default 10         null comment '考核任务结束后的结果申诉  考核任务结束后几天之内可以申诉',
    exceed_full_score       varchar(10)                     default 'false'    null comment '评分是否可以超过满分分值',
    result_confirm_type     int                             default 50         null comment '结果确认类型  10=等级 ，20=分数 ，默认50=等级/分数',
    is_example              int                             default 0          null comment '是否示例模板 默认0=非示例模板、1=示例模板',
    onboard_auto_start      int                             default 0          null comment '员工新入职后以该任务为员工自动发起考核 1=开启自动, 0=关闭自动',
    onboard_cycle_cnt       int                             default 0          null comment '入职后{onboardCycleCnt}{onboardCycleUnit}内为新人考核时间段',
    onboard_cycle_unit      int                             default 0          null comment '入职后{onboardCycleCnt}{onboardCycleUnit}内为新人考核时间段',
    match_child_org         int                             default 0          null comment '是否匹配子部门人员:1=是,0=否',
    show_result_type        int                                                null comment '结果呈现:111=总分,总等级,维度等级',
    create_total_level_type int                                                null comment '绩效总等级生成方式 1 = 自动, 2= 手动',
    total_level_raters      text                                               null comment '绩效总等级评价人员,数组只有一个元素'
)
    comment '绩效模板基础信息' charset = utf8mb4;

create index company_id
    on perf_templ_base (company_id);

create table perf_templ_evaluate
(
    id                          varchar(50)           not null
        primary key,
    company_id                  varchar(50)           null comment '公司id',
    templ_base_id               varchar(50)           null comment '模板基础id',
    score_start_rule_type       varchar(50)           null comment '评分开始时间规则类型（周期结束前/后）',
    score_start_rule_day        int                   null comment '评分开始时间规则值',
    self_score_flag             varchar(10)           null comment '是否自评',
    self_score_rule             varchar(50)           null comment '自评评分规则（打总分/指标打分）',
    self_score_view_rule        varchar(500)          null comment '被考核人查看评分规则JSON',
    self_score_weight           decimal(10, 2)        null comment '自评权重',
    mutual_score_flag           varchar(10)           null comment '是否互评',
    mutual_score_rule           varchar(50)           null comment '互评规则（打总分/指标打分）',
    mutual_score_attend_rule    varchar(50)           null comment '互评参与规则',
    mutual_score_anonymous      varchar(10)           null comment '互评人姓名是否匿名',
    mutual_score_vacancy        varchar(50)           null comment '互评人空缺时规则',
    mutual_score_view_rule      varchar(500)          null comment '互评人查看评分规则JSON',
    peer_score_weight           decimal(10, 2)        null comment '同级互评权重',
    sub_score_weight            decimal(10, 2)        null comment '下级互评权重',
    superior_score_flag         varchar(10)           null comment '是否上级评',
    superior_score_order        varchar(50)           null comment '上级评顺序类型（同时/依次）',
    superior_score_view_rule    varchar(500)          null comment '上级评人查看评分规则JSON',
    superior_score_weight       decimal(10, 2)        null comment '上级评权重',
    superior_score_rule         varchar(50)           null comment '上级评分规则（打总分/指标打分）',
    superior_score_vacancy      varchar(50)           null comment '上级评分人空缺时规则',
    audit_flag                  varchar(10)           null comment '结果是否审批',
    comment_flag                varchar(30)           null comment '评语填写类型（选填/必填/按评分值）',
    comment_required_value      int                   null comment '评语按评分值规则值, 低于某个比例',
    comment_required_high_value int                   null comment '评语按评分值规则值, 高于某个比例',
    enter_score_method          varchar(30)           null comment '进入评分方式，手动manual，自动auto',
    created_user                varchar(50)           null comment '创建用户',
    created_time                datetime              null comment '创建时间',
    updated_user                varchar(50)           null comment '修改用户',
    updated_time                datetime              null comment '修改时间',
    is_deleted                  varchar(10)           null,
    evaluate_type               varchar(20)           null comment '评分流程类型 ： simple-简易流程，360-360°考核流程，custom-自定义流程',
    appoint_score_view_rule     varchar(500)          null comment '指定评分查看规则',
    multiple_reviewers_type     varchar(30)           null comment '多个责任人或签还是会签',
    duplicate_type              varchar(10)           null comment '当责任人担任多个评分人时,true表示去重，false表示不去重',
    role_vacancy_type           varchar(30)           null comment '指定角色空缺时,skip跳过，taskAdmin转交给考核任务发起人',
    submit_level_flag           varchar(10)           null comment '评分时是否提交绩效等级，true/false',
    mutual_user_type            varchar(30)           null comment '设置互评人类型，all:为所有被考核人设置相同的互评人; user: 为每位被考核人分别设置互评人',
    enter_score_emp_type        int                   null comment ' 发起评分的人员type=1管理员,type=2考核员工,',
    is_add_audit_comment        tinyint(1) default 0  null comment '是否需要校准备注',
    version                     int        default 0  null comment '版本号',
    score_summary_switch        int        default -1 null comment '评分总结开关 0非必填/1必填/-1关闭 默认=-1',
    plus_or_sub_comment         int        default -1 null comment '加减分评语填设置/1(必填)/0(不必填)/-1关闭',
    sub_score_flag              varchar(10)           null comment '下级互评开关',
    sub_score_rule              varchar(10)           null comment '下级互评规则',
    approver_emp_name           varchar(50)           null comment '责任人名称（包含主管、角色）',
    sub_user_type               varchar(50)           null comment '下级指定评分人类型',
    sub_user_value              varchar(50)           null comment '下级指定人类型id',
    peer_user_type              varchar(50)           null comment '下级指定评分人类型',
    peer_user_value             varchar(50)           null comment '下级指定人类型id',
    peer_user_name              varchar(50)           null comment '同级评指定人姓名',
    sub_user_name               varchar(50)           null comment '下级评指定人姓名',
    peer_score_flag             varchar(50)           null comment '同级互评开关',
    peer_score_rule             varchar(50)           null comment '同级互评规则',
    appoint_score_flag          varchar(10)           null comment '是否开启指定评分',
    appoint_score_weight        decimal(10, 2)        null comment '指定评分权重'
)
    comment '绩效模板流程-考核评价' charset = utf8mb4;

create index company_id
    on perf_templ_evaluate (company_id);

create index idx_templ_base_id
    on perf_templ_evaluate (templ_base_id);

create table perf_templ_evaluate_affirm
(
    id                      varchar(50)   not null
        primary key,
    company_id              varchar(50)   null comment '公司id',
    templ_base_id           varchar(50)   null comment '模板基础id',
    confirm_flag            varchar(10)   null comment '是否需要被考核人确认',
    modify_flag             varchar(10)   null comment '是否允许被考核人修改',
    modify_audit_flag       varchar(10)   null comment '修改后是否需要审核',
    created_user            varchar(50)   null comment '创建用户',
    created_time            datetime      null comment '创建时间',
    updated_user            varchar(50)   null comment '修改用户',
    updated_time            datetime      null comment '修改时间',
    is_deleted              varchar(10)   null,
    new_flag                varchar(10)   null comment '是否改版后的数据',
    no_change_skip_flag     varchar(10)   null comment '第一位责任人未修改考核任务，则跳过后续的确认流程',
    vacancy_approver_type   varchar(50)   null comment '责任人空缺时，指派给更上级superior，跳过skip',
    multiple_reviewers_type varchar(30)   null comment '多个责任人或签还是会签',
    version                 int default 0 null comment '版本号',
    open_confirm_l_t        int default 0 null comment '指标确认限时的开关',
    confirm_l_t_day         int default 0 null comment '指标确认限时天数'
)
    comment '绩效模板流程-确认考核任务' charset = utf8mb4;

create index company_id
    on perf_templ_evaluate_affirm (company_id);

create table perf_templ_evaluate_audit
(
    id                      varchar(50)    not null
        primary key,
    company_id              varchar(50)    null comment '公司id',
    temp_base_id            varchar(50)    null comment '模板基础id',
    emp_id                  varchar(50)    null comment '被考核员工id',
    kpi_item_id             varchar(50)    null comment '指标id',
    scene                   varchar(30)    null comment '考核场景（确认任务/上级评分/结果审批）',
    approval_order          int            null comment '审批顺序（从1开始递增）',
    approver_type           varchar(30)    null comment '审批人类型（指定级别主管/指定人员）',
    approver_info           varchar(200)   null comment '指定对象id（级别）',
    superior_score_weight   decimal(10, 2) null comment '评分人权重',
    multiple_reviewers_type varchar(30)    null comment '多个责任人或签还是会签',
    transfer_flag           varchar(10)    null comment '是否可转交',
    vacancy_approver_type   varchar(30)    null comment '审批人空缺时指定人类型（管理员/指定人员）',
    vacancy_approver_info   varchar(200)   null comment '审批人空缺时指定人id',
    score_rule              varchar(50)    null comment '评分规则，total对考核任务打总分，item对每个指标打分',
    status                  varchar(30)    null comment '当前节点状态',
    created_user            varchar(50)    null comment '创建用户',
    created_time            datetime       null comment '创建时间',
    updated_user            varchar(50)    null comment '修改用户',
    updated_time            datetime       null comment '修改时间',
    is_deleted              varchar(10)    null,
    is_default              varchar(10)    null comment '是否默认配置，true/false',
    kpi_type_id             varchar(50)    null comment '指标类id',
    modify_flag             varchar(10)    null comment '是否可以修改',
    version                 int default 0  null comment '版本号',
    approver_emp_name       varchar(10)    null comment '责任人名称（包含主管、角色）'
)
    comment '绩效模板流程-审批设置' charset = utf8mb4;

create index company_id
    on perf_templ_evaluate_audit (company_id);

create index k_kpi_item_id
    on perf_templ_evaluate_audit (kpi_item_id);

create table perf_templ_evaluate_execute
(
    id                      varchar(50)   not null
        primary key,
    company_id              varchar(50)   null comment '公司id',
    templ_base_id           varchar(50)   null comment '模板基础id',
    change_flag             varchar(10)   null comment '是否允许变更',
    change_user             varchar(50)   null comment '变更责任人：被考核人taskEmp, 上级主管superior，发起人和主管理员admin',
    created_user            varchar(50)   null comment '创建用户',
    created_time            datetime      null comment '创建时间',
    updated_user            varchar(50)   null comment '修改用户',
    updated_time            datetime      null comment '修改时间',
    is_deleted              varchar(10)   null,
    vacancy_approver_type   varchar(50)   null comment '责任人空缺时，指派给更上级superior，跳过skip',
    multiple_reviewers_type varchar(30)   null comment '多个责任人或签还是会签',
    audit_open              int default 1 null comment '执行阶段变更指标是否打开审核流程,默认打开 1=开,0=关 ',
    version                 int default 0 null comment '版本号'
)
    comment '绩效模板流程-执行阶段变更' charset = utf8mb4;

create index company_id
    on perf_templ_evaluate_execute (company_id);

create table perf_templ_evaluate_initiate
(
    id                varchar(50)   not null comment 'id'
        primary key,
    company_id        varchar(50)   null comment '公司id',
    templ_base_id     varchar(50)   null comment '模板基础id',
    initiator_type    varchar(50)   null comment '发起人类型',
    initiator_list    text          null comment '发起人用户id数组',
    examine_obj_data  text          null comment '被考核员工来源JSON对象；[{"obj_type":"对象类型(部门/角色/岗位/指定员工)",“objItems”:[{"objId":"对象id","objName":"对象名称"}]}]',
    exclude_emp_data  text          null comment '排查员工JSON对象；[{"empId":"员工id","empName":"员工姓名"}]',
    enable_add_reduce varchar(10)   null comment '发起任务时是否可以增减指标',
    created_user      varchar(50)   null comment '创建用户',
    created_time      datetime      null comment '创建时间',
    updated_user      varchar(50)   null comment '修改用户',
    updated_time      datetime      null comment '修改时间',
    is_deleted        varchar(10)   null,
    version           int default 0 null comment '版本号'
)
    comment '绩效模板考核流程-发起考核' charset = utf8mb4;

create index company_id
    on perf_templ_evaluate_initiate (company_id);

create table perf_templ_formula_field
(
    id                  varchar(50)    not null
        primary key,
    company_id          varchar(50)    null,
    templ_base_id       varchar(50)    null comment '模板id',
    kpi_item_id         varchar(50)    null comment '指标id',
    company_field_id    varchar(50)    null comment '公司公式字段id',
    formula_field_name  varchar(100)   null comment '公式里的字段名称',
    formula_field_value decimal(18, 2) null comment '公式里的字段值',
    is_deleted          varchar(10)    null,
    created_user        varchar(50)    null,
    created_time        datetime       null,
    updated_user        varchar(50)    null,
    updated_time        datetime       null,
    version             int default 0  null comment '版本号'
)
    comment '模板指标公式字段表';

create index company_id
    on perf_templ_formula_field (company_id);

create table perf_templ_item_evaluate
(
    id                       varchar(50)    not null
        primary key,
    company_id               varchar(50)    null comment '公司id',
    templ_base_id            varchar(50)    null comment '模板基础id',
    kpi_item_id              varchar(50)    null comment '指标项id',
    self_score_flag          varchar(10)    null comment '是否自评',
    self_score_view_rule     varchar(500)   null comment '被考核人查看评分规则JSON',
    self_score_weight        decimal(10, 2) null comment '自评权重',
    mutual_score_flag        varchar(10)    null comment '是否互评',
    mutual_score_attend_rule varchar(50)    null comment '互评参与规则',
    mutual_score_anonymous   varchar(10)    null comment '互评人姓名是否匿名',
    mutual_score_vacancy     varchar(50)    null comment '互评人空缺时规则',
    mutual_score_view_rule   varchar(500)   null comment '互评人查看评分规则JSON',
    peer_score_weight        decimal(10, 2) null comment '同级互评权重',
    sub_score_weight         decimal(10, 2) null comment '下级互评权重',
    superior_score_flag      varchar(10)    null comment '是否上级评',
    superior_score_view_rule varchar(500)   null comment '上级评人查看评分规则JSON',
    superior_score_weight    decimal(10, 2) null comment '上级评权重',
    superior_score_vacancy   varchar(50)    null comment '上级评分人空缺时规则',
    appoint_score_flag       varchar(10)    null comment '是否指定评分',
    appoint_score_weight     decimal(10, 2) null comment '指定评分权重',
    created_user             varchar(50)    null comment '创建用户',
    created_time             datetime       null comment '创建时间',
    updated_user             varchar(50)    null comment '修改用户',
    updated_time             datetime       null comment '修改时间',
    is_deleted               varchar(10)    null,
    mutual_user_type         varchar(30)    null comment '设置互评人类型，all:为所有被考核人设置相同的互评人; user: 为每位被考核人分别设置互评人',
    kpi_type_id              varchar(50)    null comment '指标类id',
    version                  int default 0  null comment '版本号',
    mutual_user_value        varchar(500)   null comment '自定义互评设置人',
    sub_score_flag           varchar(10)    null comment '自定义下级互评开关',
    sub_user_type            varchar(50)    null comment '下级指定评分人类型',
    sub_user_value           varchar(50)    null comment '下级指定人类型id',
    peer_user_type           varchar(50)    null comment '下级指定评分人类型',
    peer_user_value          varchar(50)    null comment '下级指定人类型id',
    peer_user_name           varchar(50)    null comment '同级评指定人姓名',
    sub_user_name            varchar(50)    null comment '下级评指定人姓名',
    peer_score_flag          varchar(50)    null comment '同级互评开关'
)
    comment '绩效模板流程-考核评价-指标自定义流程' charset = utf8mb4;

create index company_id
    on perf_templ_item_evaluate (company_id);

create table perf_templ_item_point_rule
(
    id            varchar(50)   not null
        primary key,
    company_id    varchar(50)   null,
    templ_base_id varchar(50)   null,
    kpi_type_id   varchar(50)   null,
    kpi_item_id   varchar(50)   null,
    rule          varchar(30)   null comment '积分规则',
    is_deleted    varchar(10)   null,
    created_user  varchar(50)   null,
    created_time  datetime      null,
    updated_user  varchar(50)   null,
    updated_time  datetime      null,
    version       int default 0 null comment '版本号'
)
    comment '指标的积分规则';

create index company_id
    on perf_templ_item_point_rule (company_id);

create table perf_templ_item_used_field
(
    field_id      varchar(50)      not null comment 'company_kpi_item_custom_field.id',
    company_id    varchar(50)      null comment '公司id',
    templ_base_id varchar(50)      null comment '模板基础id  perf_templ_base.id',
    kpi_item_id   varchar(50)      null comment 'perf_templ_kpi_item.kpi_item_id',
    name          varchar(50)      null comment '字段名称【复制于company_kpi_item_custom_field】',
    value         varchar(512)     null comment '字段值【复制于company_kpi_item_custom_field】',
    type          int(2) default 1 null comment '字段类型（1：输入框  2：开关 3：下拉选 4：复选 5：img）【复制于company_kpi_item_custom_field】',
    req           int(1) default 1 null comment '是否必填（0：否  1：是）【复制于company_kpi_item_custom_field】',
    status        varchar(20)      null comment '指标字段状态（valid：有效，invalid：无效）【复制于company_kpi_item_custom_field】',
    `show`        int(1) default 1 null comment '是否显示（0：否  1：是）',
    sort          int(5)           null comment '指标字段排序值',
    admin_type    int(1)           null comment '1:系统默认字段  0：自定义字段',
    is_deleted    varchar(10)      null comment '是否删除',
    created_user  varchar(50)      null comment '创建用户',
    created_time  datetime         null comment '创建时间',
    updated_user  varchar(50)      null comment '修改用户',
    updated_time  datetime         null comment '修改时间',
    version       int    default 0 null
)
    comment '绩效模板指标自定义字段使用表' charset = utf8mb4;

create index company_key
    on perf_templ_item_used_field (company_id);

create index field_key
    on perf_templ_item_used_field (field_id);

create index item_key
    on perf_templ_item_used_field (kpi_item_id);

create index templ_base_key
    on perf_templ_item_used_field (templ_base_id);

create table perf_templ_kpi_item
(
    id                      varchar(50)                 not null
        primary key,
    company_id              varchar(50)                 null comment '公司id',
    templ_base_id           varchar(50)                 null comment '模板基础id',
    kpi_type_id             varchar(50)                 null comment '指标类id',
    kpi_item_id             varchar(50)                 null,
    item_type               varchar(20)                 null comment '指标项类型（量化/非量化）',
    item_value              decimal(18, 2)              null comment '指标项目标值',
    item_unit               varchar(20)                 null comment '目标值单位',
    parent_kpi_type_id      varchar(50)                 null comment '上级指标项',
    item_rule               text                        null comment '考核规则',
    scoring_rule            text                        null comment '计分规则',
    result_input_type       varchar(50)                 null comment '指标结果录入类型（被考核人/指定员工/无需录入）',
    result_input_user_id    text                        null comment '指定结果录入人id',
    scorer_type             varchar(50)                 null comment '指标评分人类型（按评分流程/指定员工/指定主管）',
    scorer_obj_id           text                        null comment '指定评分人json串',
    item_weight             decimal(10, 2)              null comment '指标项权重',
    is_deleted              varchar(10)                 null comment '是否删除',
    created_user            varchar(50)                 null comment '创建用户',
    created_time            datetime                    null comment '创建时间',
    updated_user            varchar(50)                 null comment '修改用户',
    updated_time            datetime                    null comment '修改时间',
    multiple_reviewers_type varchar(50)                 null comment '多人审核时，and会签，or或签',
    plus_limit              decimal(10, 2)              null comment '加分上限',
    subtract_limit          decimal(10, 2)              null comment '减分上限',
    `order`                 int                         null comment '排序，数字小的排前面',
    item_formula            text                        null comment '指标计算公式',
    threshold_json          text                        null comment '量化指标阈值设置json',
    formula_condition       text                        null comment '公式条件',
    item_field_json         text                        null comment '指标关联的阈值字段',
    kpi_item_name           text                        null comment '指标项名称',
    item_score_value        varchar(500)                null comment '指标评分分值',
    input_format            varchar(50)                 null comment '录入格式',
    show_target_value       varchar(10)                 null comment '是否展示目标值',
    must_result_input       tinyint(1)  default 0       null comment '指标完成值是否必需录入',
    show_finish_bar         int         default 1       null comment '完成度进度条 默认开启 1=显示,0=不显示',
    version                 int         default 0       null comment '版本号',
    is_new_emp              int         default 0       null comment '是否新人培训指标',
    manager_level           varchar(10)                 null comment '录入主管等级',
    item_full_score_cfg     varchar(10) default 'false' null comment '自动计算指标得分满分值',
    result_input_emp_name   varchar(10)                 null comment '录入人姓名',
    plus_sub_interval       varchar(50)                 null comment '模板指标加减分上限',
    item_custom_field_json  text                        null comment '指标自定义字段json',
    ind_level_group_id      bigint                      null comment '指标等级组id=开启等级组,  0=不开等级组',
    formula_type            int(1)      default 1       null comment '公式类型：1=多场景公式、2=高级公式',
    finish_value_audit      text                        null comment '完成值审核人列表',
    item_target_value_text  text                        null comment '指标项文本类型目标值'
)
    comment '绩效模板指标项' charset = utf8mb4;

create index company_id
    on perf_templ_kpi_item (company_id);

create table perf_templ_kpi_type
(
    id                 varchar(50)             not null
        primary key,
    company_id         varchar(50)             null comment '公司id',
    templ_base_id      varchar(50)             null comment '模板基础id',
    type_id            varchar(50)             null comment '指标类id',
    type_name          varchar(200)            null comment '指标类名称',
    type_weight        decimal(10, 2)          null comment '指标类权重',
    is_deleted         varchar(10)             null comment '是否删除',
    created_user       varchar(50)             null comment '创建用户',
    created_time       datetime                null comment '创建时间',
    updated_user       varchar(50)             null comment '修改用户',
    updated_time       datetime                null comment '修改时间',
    classify           varchar(50)             null comment '指标类别，为空表示已有类别，custom自定义/plus加分项/subtract减分项',
    max_extra_score    decimal(10, 2)          null comment '加减分上限',
    `order`            int                     null comment '排序，数字小的排前面',
    is_type_locked     varchar(100)            null comment '类别锁定类型',
    is_okr             varchar(10)             null comment '是否OKR类别，true/false',
    reserve_okr_weight decimal(10, 2)          null comment '预留OKR权重',
    is_empty_type      varchar(10)             null comment '是否空类别',
    version            int          default 0  null comment '版本号',
    item_limit_cnt     varchar(50)             null comment '类别指标数量最大最小限制',
    open_okr_score     int          default 0  null comment '指标评分使用的是okr的分数',
    plus_sub_interval  varchar(50)             null comment '模板类别加减分上限',
    self_rater         varchar(128) default '' null comment '自评人',
    super_rater        text                    null comment '上级人 json',
    appoint_rater      text                    null comment '指定评分人json',
    peer_rater         text                    null comment '自定义同级互评人',
    sub_rater          text                    null comment '自定义下级互评人',
    ind_level_group_id bigint                  null comment '指标等级组id=开启等级组,  0=不开等级组 ',
    finish_value_audit text                    null comment '完成值审核人列表',
    score_opt_type     int          default 2  null comment '# 0:等级[无等级组]indLevelId==null # 1:等级[设等级组] indLevelId=1000234  #2:评分[无选项组] indLevelId==null  # 4:评分[有选项组], indLevelId=1000235 ',
    des                varchar(512)            null comment '维度描述 20230921增加'
)
    comment '绩效模板指标类' charset = utf8mb4;

create index company_id
    on perf_templ_kpi_type (company_id);

create table perf_templ_org
(
    id            varchar(50)   not null
        primary key,
    company_id    varchar(50)   null,
    templ_base_id varchar(50)   null comment '模板id',
    org_id        varchar(50)   null comment '部门id',
    is_deleted    varchar(10)   null comment '是否删除',
    created_user  varchar(50)   null,
    created_time  datetime      null,
    updated_user  varchar(50)   null,
    updated_time  datetime      null,
    version       int default 0 null comment '版本号'
)
    comment '考核模板-部门关联';

create table perf_templ_type_eval_rule
(
    id            varchar(50)             not null
        primary key,
    company_id    varchar(50)             null comment '公司id',
    templ_base_id varchar(50)             null comment '模板id',
    kpi_type_id   varchar(50)             null comment '指标类id',
    self_rater    varchar(128) default '' null comment '自评人',
    mutual_rater  text                    null comment '互评人 json',
    super_rater   text                    null comment '上级人 json',
    appoint_rater text                    null comment '指定评分人json',
    peer_rater    text                    null comment '自定义同级互评人',
    sub_rater     text                    null comment '自定义下级互评人',
    created_user  varchar(50)             null comment '创建用户',
    created_time  datetime                null comment '创建时间',
    updated_user  varchar(50)             null comment '修改用户',
    updated_time  datetime                null comment '修改时间',
    is_deleted    varchar(10)             null comment '是否删除',
    version       int          default 0  null comment '版本号'
)
    comment '模板-考核维度自定义流程' charset = utf8mb4;

create index idx_companyId
    on perf_templ_type_eval_rule (company_id);

create index idx_tmpId
    on perf_templ_type_eval_rule (templ_base_id);

create table perf_templ_type_used_field
(
    field_id      varchar(50)      not null comment 'company_kpi_item_custom_field.id',
    company_id    varchar(50)      null comment '公司id',
    templ_base_id varchar(50)      null comment '模板基础id  perf_templ_base.id',
    kpi_type_id   varchar(50)      null comment 'perf_templ_kpi_type.type_id',
    name          varchar(50)      null comment '字段名称【复制于company_kpi_item_custom_field】',
    type          int(2) default 1 null comment '字段类型（1：输入框  2：开关 3：下拉选 4：复选 5：img）【复制于company_kpi_item_custom_field】',
    req           int(1) default 1 null comment '是否必填（0：否  1：是）【复制于company_kpi_item_custom_field】',
    status        varchar(20)      null comment '指标字段状态（valid：有效，invalid：无效）【复制于company_kpi_item_custom_field】',
    `show`        int(1) default 1 null comment '是否显示（0：否  1：是）',
    sort          int(5)           null comment '指标字段排序值',
    admin_type    int(1)           null comment '1:系统默认字段  0：自定义字段',
    is_deleted    varchar(10)      null comment '是否删除',
    created_user  varchar(50)      null comment '创建用户',
    created_time  datetime         null comment '创建时间',
    updated_user  varchar(50)      null comment '修改用户',
    updated_time  datetime         null comment '修改时间',
    version       int    default 0 null
)
    comment '绩效模板维度自定义字段使用表' charset = utf8mb4;

create index company_key
    on perf_templ_type_used_field (company_id);

create index field_key
    on perf_templ_type_used_field (field_id);

create index templ_base_key
    on perf_templ_type_used_field (templ_base_id);

create index type_key
    on perf_templ_type_used_field (kpi_type_id);

create table perf_year_report
(
    id               varchar(50) collate utf8mb4_bin not null
        primary key,
    company_id       varchar(50) collate utf8mb4_bin null,
    type             int(1) default 2                null comment '数据类型（1：主数据信息 2：用户数据类型）',
    emp_id           varchar(50)                     null comment '用户id',
    year             int                             null comment '年份',
    years            decimal(11, 2)                  null comment '年度',
    first_half_year  decimal(11, 2)                  null comment '上半年',
    second_half_year decimal(11, 2)                  null comment '下半年',
    quarter1         decimal(11, 2)                  null comment '第一季度',
    quarter2         decimal(11, 2)                  null comment '第二季度',
    quarter3         decimal(11, 2)                  null comment '第三季度',
    quarter4         decimal(11, 2)                  null comment '第四季度',
    month1           decimal(11, 2)                  null comment '1月',
    month2           decimal(11, 2)                  null comment '2月',
    month3           decimal(11, 2)                  null comment '3月',
    month4           decimal(11, 2)                  null comment '4月',
    month5           decimal(11, 2)                  null comment '5月',
    month6           decimal(11, 2)                  null comment '6月',
    month7           decimal(11, 2)                  null comment '7月',
    month8           decimal(11, 2)                  null comment '8月',
    month9           decimal(11, 2)                  null comment '9月',
    month10          decimal(11, 2)                  null comment '10月',
    month11          decimal(11, 2)                  null comment '11月',
    month12          decimal(11, 2)                  null comment '12月',
    status           varchar(255)                    null comment 'publish：执行中  finish：已完成',
    score_ranges     text                            null comment '等级分值组',
    is_deleted       varchar(10) collate utf8mb4_bin null,
    created_user     varchar(50) collate utf8mb4_bin null,
    created_time     datetime                        null,
    updated_user     varchar(50) collate utf8mb4_bin null,
    updated_time     datetime                        null,
    version          int    default 0                null
)
    comment '绩效报告每年份数据' charset = utf8mb4;

create index company_key
    on perf_year_report (company_id);

create index year_key
    on perf_year_report (year);

create table post
(
    company_id   varchar(50)                           not null comment '公司id',
    name         varchar(50)                           not null comment '职级名',
    post_id      varchar(50)                           not null comment '职级id',
    plat         int         default 1                 null comment '1 = 自建,2=钉钉,4=企微,8=,16',
    created_time datetime    default CURRENT_TIMESTAMP null,
    created_user varchar(50) default ''                null,
    updated_time datetime    default CURRENT_TIMESTAMP null,
    updated_user varchar(50) default ''                null,
    is_deleted   varchar(5)  default 'false'           null,
    version      int         default 0                 null comment '版本号'
)
    comment '职级表,同步钉钉的职级过来' charset = utf8mb4;

create index post_idx_empId
    on post (company_id, post_id);

create table ref_eval
(
    company_id   varchar(50)    not null comment '公司id',
    main_eval_id varchar(50)    not null comment '主绩效id=task_user.task_user_id',
    ref_eval_id  varchar(50)    not null comment '关联的任务id=task_user.task_user_id',
    weight       decimal(10, 3) not null comment '权重',
    score        decimal(10, 3) null comment '被关联的任务分数',
    created_user varchar(50)    null comment '创建用户',
    created_time datetime       null comment '创建时间'
)
    comment '关联的绩效任务' charset = utf8mb4;

create index ref_eval_idx_companyId_mainEvalId
    on ref_eval (company_id, main_eval_id);

create table renew_order
(
    id                    bigint auto_increment comment '记录id'
        primary key,
    order_type            varchar(20) collate utf8mb4_bin  null comment 'orderType=RENEW_UPGRADE时，订单为续费升级订单。orderType=RENEW_DEGRADE时，订单为续费降级订单。',
    distributor_corp_name varchar(200) collate utf8mb4_bin null comment '分销商企业名称',
    distributor_corp_id   varchar(50) collate utf8mb4_bin  null comment '分销商企业corpId',
    discount_fee          decimal(13, 2)                   null comment '折扣减免费用 （单位：分）现值为0',
    order_id              varchar(50) collate utf8mb4_bin  null comment '订单号',
    sync_action           varchar(50) collate utf8mb4_bin  null comment '该订单对应的用户操作，"syncAction": "market_order" 表示市场订单支付',
    max_of_people         int(10)                          null comment '最大人数',
    min_of_people         int(10)                          null comment '规格支持最小使用人数 0',
    item_code             varchar(100) collate utf8mb4_bin null comment 'DT_GOODS_881603867765821_830010,规格码',
    item_name             varchar(200) collate utf8mb4_bin null comment '221-300人规格名',
    goods_name            varchar(200) collate utf8mb4_bin null comment '商品名称:北极星绩效',
    goods_code            varchar(50) collate utf8mb4_bin  null comment '商品码',
    pay_fee               int(10)                          null comment '实际支付价格 （单位：分）',
    nominal_pay_fee       decimal(6, 2)                    null comment '名义票面费用 （单位：分） 现与payFee值相等',
    service_stop_time     bigint                           null comment '服务结束时间 （单位：毫秒',
    service_start_time    bigint                           null comment '服务开始时间 （单位：毫秒）',
    suite_key             varchar(100) collate utf8mb4_bin null comment '用户购买套件的suiteKey',
    order_creat_source    varchar(50) collate utf8mb4_bin  null comment '订单来源 默认订单来自应用中心；若值为 DRP，表示来自DRP分销平台；',
    suite_id              bigint                           null comment '用户购买套件的suiteId：应用的套件信息',
    corp_id               varchar(50) collate utf8mb4_bin  null comment '购买企业的corpId',
    sub_quantity          int(10)                          null comment '购买数量',
    close_type            int(10)                          null comment '1，升级关闭；2，到期关闭；3，退款关闭；4，其他关闭',
    paidtime              bigint                           null comment '支付时间 （单位：毫秒）',
    status                bigint                           null comment '0=等待处理, 1= 处理中, 2 = 已完成处理,其它数字=标号',
    gmt_create            bigint                           not null comment '创建时间',
    gmt_update            bigint                           not null comment '更新时间',
    version               int default 0                    null comment '版本号'
)
    comment '续费变更规格订单,bizType=17消息中的.bizData字段展开的记录' charset = utf8mb4;

create index idx_corp_id
    on renew_order (corp_id);

create table report_weight_setting
(
    id           varchar(255)   not null
        primary key,
    company_id   varchar(255)   null,
    year         int            null comment '年份',
    cycle_type   varchar(50)    null comment '考核周期类型',
    month        int            null comment '月份',
    weight       decimal(10, 2) null comment '权重',
    is_deleted   varchar(10)    null,
    created_user varchar(50)    null,
    created_time datetime       null,
    updated_user varchar(255)   null,
    updated_time datetime       null,
    version      int default 0  null
)
    comment '年度报表权重设置';

create table role
(
    id           varchar(50) default ''       not null comment '主键ID'
        primary key,
    company_id   varchar(50)                  null comment '公司ID',
    role_name    varchar(255)                 null comment '角色名称',
    role_desc    varchar(255)                 null comment '角色描述',
    role_type    varchar(50) default 'custom' null comment '角色类型: ding-钉钉，custom-自定义',
    ding_role_id varchar(50)                  null,
    group_id     varchar(50)                  null comment '所属角色组id',
    is_group     varchar(10)                  null comment '是否为角色组：true/false',
    is_deleted   varchar(10)                  null comment '是否有效: false-否,true-是',
    created_user varchar(50)                  null comment '创建者',
    created_time datetime                     null comment '创建时间',
    updated_user varchar(50)                  null comment '修改者',
    updated_time datetime                     null comment '修改时间',
    version      int         default 0        null comment '版本号'
)
    charset = utf8;

create index company_id
    on role (company_id);

create index idx_company_id_active
    on role (is_deleted, company_id);

create table role_ref_emp
(
    id            varchar(50)  not null
        primary key,
    company_id    varchar(50)  null,
    role_id       varchar(50)  null comment '角色id',
    emp_id        varchar(255) null,
    status        varchar(10)  null comment '状态：valid-启用，invalid-禁用',
    is_deleted    varchar(10)  null comment '是否删除：true/false',
    created_user  varchar(50)  null,
    created_time  datetime     null,
    updated_user  varchar(255) null,
    updated_time  datetime     null,
    scope_org_ids text         null comment '角色管理权限范围'
)
    comment '角色与员工关联关系';

create index company_id
    on role_ref_emp (company_id);

create index emp_id
    on role_ref_emp (emp_id);

create index role_id
    on role_ref_emp (role_id);

create table schedule_temp
(
    task_key     varchar(255) not null
        primary key,
    company_id   varchar(255) not null,
    batch_no     bigint       not null comment '批次号,删除用',
    type         varchar(255) not null,
    created_time datetime     null comment '记录创建时间'
)
    comment '定时任务并发处理' charset = utf8mb4;

create index batch_no_key
    on schedule_temp (batch_no);

create table score_range
(
    id               int auto_increment
        primary key,
    company_id       varchar(50)                not null,
    score_rule_id    int                        not null,
    step_id          int                        not null,
    min              decimal(10, 2)             not null comment '最小分值含有',
    max              decimal(10, 2)             not null comment '最大分值',
    rate decimal(10, 2) default -1 comment '排名评等级方式占比',
    created_time     date                       null,
    created_user     varchar(50)                null,
    updated_time     date                       null,
    updated_user     varchar(50)                null,
    is_deleted       varchar(6) default 'false' null,
    version          int        default 0       null,
    min_append_equal int        default 0       null comment '0拼在max上，1拼在min上',
    perf_coefficient text                       null comment '等级对应绩效系数/绩效系数公式',
    coeff_type       int(1)                     null comment '系数类型：1=固定系数值，2=自定义公式计算系数',
    place            int(1)                     null comment '系数保留小数位',
    field_json       text                       null comment '公式字符串,页面回填使用'
);

create index idx_company_id
    on score_range (company_id);

create index idx_score_rule_id
    on score_range (score_rule_id);

create table score_rule
(
    id               int auto_increment
        primary key,
    name             varchar(50)                 null comment '分值规则名',
    `system`         tinyint(1)  default 0       null comment '是否系统规则,旧数据生成',
    company_id       varchar(50)                 null comment '公司id',
    eg_orgs          varchar(300)                null comment '示例部门用于显示,最多三个名字',
    level_def_type   int         default 1       null comment '等级评定规则类型:1=按分数评定等级(旧数据), 2 = 按分数排名评等级',
    level_rate_conf  text                        null comment '强制正态分布规则:{rates:[{levels:[1,3],rate:20,op:"=",sortNo:1},{}],"startCnt":5,"open":1}',
    created_time     date                        null,
    created_user     varchar(50) default ''      null,
    updated_time     date                        null,
    updated_user     varchar(50) default ''      null,
    is_deleted       varchar(6)  default 'false' null,
    version          int         default 0       null,
    exclude_emps     text                        null comment '创建等级规则时，排除的人',
    coeff_type       int(1)                      null comment '系数类型：1=固定系数值，2=自定义公式计算系数',
    place            int(1)                      null comment '系数保留小数位',
    perf_coefficient text                        null comment '等级对应绩效系数/绩效系数公式',
    field_json       text                        null comment '公式字符串,页面回填使用'
);

create index idx_company_id
    on score_rule (company_id);

create table sequence
(
    name          varchar(50)     not null
        primary key,
    current_value int             not null,
    increment     int default 100 not null
);


create table sv_in_service
(
    id           varchar(100) null,
    service_id   varchar(100) null comment '服务id',
    account_id   varchar(100) null comment '账号id',
    company_id   varchar(100) null comment '公司id',
    service_name varchar(255) null comment '服务名',
    end_time     datetime     null comment '服务失效时间点',
    start_time   datetime     null comment '服务生效时间点',
    created_time datetime     null,
    updated_time datetime     null,
    run_status   varchar(10)  null comment '暂停-pause,启动-start'
)
    comment '公司相关服务' charset = utf8;

create index company_id
    on sv_in_service (company_id);

create table sys_dic_info
(
    id           varchar(50)  not null comment '字典id'
        primary key,
    type_id      varchar(20)  null comment '字典类型',
    code         varchar(50)  null comment '字典编码',
    `desc`       varchar(100) null comment '字典描述',
    is_deleted   varchar(10)  null comment '是否删除：true/false',
    is_base      varchar(10)  null comment '是否为基础数据(true/false)，基础数据会在创建公司时作为公司初始数据',
    created_user varchar(50)  null,
    created_time datetime     null,
    updated_user varchar(50)  null,
    updated_time datetime     null
)
    comment '系统字典';

create table sys_dic_type
(
    id           varchar(50)  null comment 'id',
    type         varchar(50)  null comment '类型编码',
    name         varchar(100) null comment '类型名称',
    is_deleted   varchar(10)  null comment '是否删除',
    created_user varchar(50)  null comment '创建用户',
    created_time datetime     null comment '创建时间',
    updated_user varchar(50)  null comment '修改用户',
    updated_time datetime     null comment '修改时间'
)
    comment '系统字典类型' charset = utf8mb4;

create table sys_init_industry
(
    id           varchar(50)  not null
        primary key,
    industry     varchar(255) null comment '行业名称',
    type         varchar(50)  null comment '所属分类',
    bg_url       varchar(255) null comment '背景图片',
    is_deleted   varchar(10)  null,
    created_user varchar(50)  null,
    created_time datetime     null,
    updated_user varchar(50)  null,
    updated_time datetime     null
)
    comment '系统初始化的行业';

create table sys_init_temp
(
    id           varchar(50)  not null
        primary key,
    industry_id  varchar(50)  null comment '行业id',
    temp_name    varchar(255) null comment '模板名称',
    is_deleted   varchar(10)  null,
    created_user varchar(50)  null,
    created_time datetime     null,
    updated_user varchar(50)  null,
    updated_time datetime     null
)
    comment '初始化模板';

create table sys_init_temp_rel_item
(
    id           varchar(50)    not null
        primary key,
    temp_id      varchar(50)    null comment '模板id',
    item_id      varchar(50)    null comment '指标id',
    item_weight  decimal(10, 2) null comment '模板里面的指标权重',
    is_deleted   varchar(10)    null,
    created_user varchar(50)    null,
    created_time datetime       null,
    updated_user varchar(50)    null,
    updated_time datetime       null
)
    comment '初始化模板';

create table sys_kpi_item
(
    id             varchar(50)    not null
        primary key,
    item_name      varchar(200)   null comment '指标项名称',
    scoring_rule   text           null comment '计分规则',
    item_rule      text           null comment '考核规则',
    is_measurable  varchar(10)    null comment '是否可量化',
    item_unit      varchar(50)    null comment '目标值单位',
    industry       varchar(50)    null comment '行业',
    position       varchar(50)    null comment '岗位',
    init_flag      varchar(10)    null comment '是否是初始化指标',
    init_weight    decimal(10, 2) null comment '初始化指标权重',
    init_temp_flag varchar(10)    null comment '是否初始化到模板',
    is_deleted     varchar(10)    null,
    created_user   varchar(255)   null,
    created_time   datetime       null,
    updated_user   datetime       null on update CURRENT_TIMESTAMP,
    updated_time   datetime       null
)
    comment '系统指标库';

create table system_admin_set
(
    id               varchar(50)              not null comment 'id'
        primary key,
    company_id       varchar(50)              null comment '公司id',
    emp_id           varchar(50)              null comment '员工id',
    status           varchar(10)              null comment 'valid：有效，invalid：无效',
    created_time     datetime                 null comment '创建时间',
    updated_time     datetime                 null comment '更新时间',
    menu_purview     text collate utf8mb4_bin null comment '菜单权限',
    manage_purview   text collate utf8mb4_bin null comment '管理权限',
    template_purview text                     null,
    operate_purview  text                     null comment '操作权限',
    admin_type       varchar(5)               null comment '主：main;子：child',
    created_user     varchar(50)              null,
    updated_user     varchar(50)              null,
    version          int default 0            null comment '版本字段'
)
    comment '系统管理员设置' charset = utf8mb4;

create index company_id
    on system_admin_set (company_id);

create table system_iteration
(
    id           varchar(50)  not null
        primary key,
    version      varchar(20)  null comment '版本',
    content      varchar(500) null comment '迭代内容',
    iterate_date date         null comment '迭代日期',
    created_time datetime     null,
    updated_time datetime     null
)
    comment '系统迭代信息';

create table task_fix_finish_status
(
    task_user_id varchar(50) null
)
    comment 'perf_evaluate_task_user完成值录入情况字段维护临时表' charset = utf8mb4;

create table tem_role_company_msg_center
(
    audit_status    varchar(50)  not null comment 'task user 表的id',
    task_id         varchar(50)  not null comment 'task user 表的id',
    emp_id          varchar(50)  not null comment 'task user 表的id',
    scorer_id       varchar(50)  not null comment 'task user 表的id',
    role_emp_id     varchar(50)  not null comment 'task user 表的id',
    company_id      varchar(50)  not null comment 'sc.task_audit_id',
    task_user_id    varchar(200) null comment 'sc.task_audit_id',
    score_result_id varchar(200) null comment '指标类名称'
)
    comment '临时表,用完删除' charset = utf8mb4;

create index idx_company_id
    on tem_role_company_msg_center (company_id);

create index idx_task_user_id
    on tem_role_company_msg_center (task_user_id);

create table temp_company
(
    id            varchar(40)  default '0'       not null comment '公司id',
    name          varchar(512)                   null comment '公司名称',
    company_type  varchar(10)                    null comment '公司类型（review:待审核-默认不需要审核,simple-极简版，enterprise-企业版）',
    app_version   varchar(174) default '1009002' null comment '当前企业的应用版本',
    open_emp_cnt  int          default 0         null comment '启用人数',
    emp_limit     int          default -1        null comment '人数上限',
    login_cnt     int          default -1        null comment '近1个季度登录人数（去重）',
    task_user_cnt int          default -1        null comment '近1个季度考核人次',
    created_time  datetime                       null comment '创建时间',
    perm_end_time datetime                       null comment '到期时间',
    constraint uk_id
        unique (id)
)
    comment '付费用户临时表,用后删除' charset = utf8mb4;

create table tenant
(
    corp_id      varchar(100)  not null comment '钉钉公司id',
    company_id   varchar(100)  not null comment 'kpi公司id',
    suite_secret varchar(100)  not null comment '票据',
    gmt_update   bigint        null,
    gmt_create   bigint        null,
    version      int default 0 null comment '版本号'
)
    comment '登录' charset = utf8mb4;

create table tenant_sys_conf
(
    company_id   varchar(50)            not null comment '企业id',
    open         int         default 1  null comment '是否开放1= 开放, 0 = 关闭',
    conf_code    varchar(50)            not null comment '配置编号,用于在场景中查询配置,确定后不可变',
    conf_content varchar(256)           not null comment '配置内容',
    conf_desc    varchar(50) default '' null comment '配置简述',
    created_user varchar(50) default '' null comment '创建人',
    updated_user varchar(50) default '' null comment '变更人',
    created_time datetime               null comment '创建时间',
    updated_time datetime               null comment '变更时间',
    version      int         default 0  null comment '版本号'
)
    comment '租户级的应用配置,可运管后台控制,用于有些企业定制功能时使用' charset = utf8mb4;

create index tenantSysConf_companyId_conCode
    on tenant_sys_conf (company_id, conf_code);

create table test
(
    name varchar(50) not null
        primary key
);

create table third_platform_setting
(
    id            varchar(50)  not null
        primary key,
    company_id    varchar(50)  null,
    third_name    varchar(20)  null comment '第三方名称: smart_work-智能人事',
    status        varchar(20)  null comment '状态：open-开启对接，close-关闭对接',
    third_doc_url varchar(500) null comment '第三方对接文档l链接',
    created_time  datetime     null,
    updated_time  datetime     null
);

create index company_id
    on third_platform_setting (company_id);

create table tip
(
    id           int                     not null comment '格式化的key'
        primary key,
    company_id   varchar(50) default '0' null comment '公司id=0表示系统的,其它是企业的',
    created_time datetime                null,
    updated_time datetime                null,
    version      int         default 0   null comment '版本号'
)
    comment '系统引导提示' charset = utf8mb4;

create table tip_read
(
    tip_id       bigint                      not null comment 'tipId',
    company_id   varchar(50) default '0'     null comment '公司id=0表示系统的,其它是企业的',
    emp_id       varchar(50) default '0'     null comment '员工id',
    created_time datetime                    null,
    updated_time datetime                    null,
    is_deleted   varchar(50) default 'false' null comment 'is_deleted',
    updated_user varchar(50) default 'false' null comment 'is_deleted',
    created_user varchar(50) default 'false' null comment 'is_deleted',
    version      int         default 0       null comment '版本号'
)
    comment '已读引导' charset = utf8mb4;

create table train_appointment
(
    id           varchar(50)                not null comment '预约id',
    company_id   varchar(50)                not null comment '公司id',
    company      varchar(256)               not null comment '公司名',
    emp_name     varchar(50)                not null comment '外键员工:emloyee_info.name',
    emp_id       varchar(50)                not null comment '外键员工id:emloyee_info.employee_id',
    phone        varchar(50)                not null comment '手机号',
    is_deleted   varchar(5) default 'false' null comment '是否删除',
    created_user varchar(50)                null comment '创建用户',
    created_time datetime                   null comment '创建时间',
    updated_user varchar(50)                null comment '修改用户',
    updated_time datetime                   null comment '修改时间',
    version      int        default 0       null comment '版本号'
)
    comment '预约演示活动' charset = utf8mb4;

create index idx_companyId_empId
    on train_appointment (company_id, emp_id);

create table use_item_log
(
    id                      varchar(50)    not null comment 'id',
    company_id              varchar(50)    null comment '公司id',
    sys_item_id             varchar(50)    null comment '系统指标库id',
    item_path               text           null comment '指标id链，“|”隔开',
    item_name               text           null comment '指标项名称',
    item_type               varchar(50)    null comment '指标项类型（量化/非量化）',
    item_value              decimal(18, 2) null comment '指标项目标值',
    item_plan_value         decimal(18, 2) null comment '指标计划值',
    item_unit               varchar(50)    null comment '目标值单位',
    parent_kpi_item_id      varchar(50)    null comment '上级指标项',
    item_rule               text           null comment '考核规则',
    scoring_rule            text           null comment '计分规则',
    result_input_type       varchar(50)    null comment '指标结果录入类型（被考核人/指定员工/无需录入）',
    result_input_user_id    text           null comment '指定结果录入人id',
    scorer_type             varchar(50)    null comment '指标评分人类型（按评分流程/指定员工/指定主管）',
    scorer_user_id          text           null comment '指定评分人id',
    multiple_reviewers_type varchar(30)    null comment '审核为多人时是或签还是会签',
    item_weight             decimal(10, 2) null comment '指标项权重',
    category_id             varchar(100)   null comment '指标级别id',
    is_board                varchar(10)    null comment '是否加入指标看板',
    is_deleted              varchar(10)    null comment '是否删除',
    is_temporary            varchar(10)    null comment '是否临时指标',
    threshold_json          text           null comment '量化指标阈值设置json',
    show_target_value       varchar(10)    null comment '是否展示目标值',
    created_user            varchar(50)    not null comment '创建用户',
    use_time                datetime       null comment '使用时间',
    primary key (id, created_user)
)
    comment '指标使用记录表';

drop table  if exists submit_score_cache;
create table submit_score_cache
(
    id             varchar(50)  not null primary key,
    company_id     varchar(50)  null,
    task_user_id   varchar(50)  null comment '员工任务id,原company_cache_info.link_id ',
    owner_emp_Id    varchar(50) null comment '缓存所属人,原company_cache_info.cache_key ',
    total_level_rs   longtext     null comment '打总等级缓存' ,
    item_scores   longtext     null comment '缓存的指标级的评分明细,原company_cache_info.value',
    prediction_score   decimal(10,3)     null comment '预估分数,新加字段',
    is_deleted     varchar(10)  null,
    created_user   varchar(50)  null,
    created_time   datetime     null,
    updated_user   varchar(50)  null,
    updated_time   datetime     null,
    key idx_cache_key(company_id,owner_emp_Id,task_user_id)
) comment '暂存提交评分的数据';


drop table if exists result_rank_instance_member;
create table result_rank_instance_member
(
    company_id      varchar(50) null comment '公司id',
    distribution_id varchar(50) null comment '等级分布实例id,result_rank_instace.id 规则id',
    task_user_id    varchar(50) null comment '员工任务task_user.id',
    emp_id          varchar(50) null comment '员工id',
    emp_org_id      varchar(50) null comment '员工所在部门id',
    rank_order      int         null comment '成员排名',
    rank_score      decimal(11, 2)          null comment '成员排名时的分数',
    rank_level      varchar(50) null comment '成员排名分布后等级',
    `created_time`  datetime    null comment '创建时间',
    `updated_time`  datetime    null comment '更新时间',
    `created_user`  varchar(50) default '' comment '创建人',
    `updated_user`  varchar(50) default '' comment '更新人',
    `is_deleted`    varchar(6)  default 'false' comment '是否删除',
    `version`       int         default 0,
    key distrId_taskUserId (task_user_id, distribution_id, company_id)
) comment '等级分布实例的成员';
/**分布组规则快照 = 实例**/
drop table if exists result_rank_instance;
create table result_rank_instance
(
    `cycle_id`         varchar(50)  null comment '周期id',
    `id`               varchar(50) primary key comment '等级分布实例id',
    `rule_id`          int comment '原规则id',
    `name`             varchar(50)  null comment '分值规则名',
    `system`           tinyint(1)  default 0 comment '是否系统规则,旧数据生成',
    `company_id`       varchar(50)  null comment '公司id',
    `eg_orgs`          varchar(300) null comment '示例部门用于显示,最多三个名字',
    `level_def_type`   int         default 1 comment '等级评定规则类型:1=按分数评定等级(旧数据), 2 = 按分数排名评等级',
    `level_rate_conf`  text comment '强制正态分布规则:{rates:[{levels:[1,3],rate:20,op:"=",sortNo:1},{}],"startCnt":5,"open":1}',
    `exclude_emps`     text         null comment '创建等级规则时，排除的人',
    `coeff_type`       int(1)       null comment '系数类型：1=固定系数值，2=自定义公式计算系数',
    `place`            int(1)       null comment '系数保留小数位',
    `perf_coefficient` text         null comment '等级对应绩效系数/绩效系数公式',
    `field_json`       text         null comment '公式字符串,页面回填使用',
    `created_time`     datetime     null comment '创建时间',
    `updated_time`     datetime     null comment '更新时间',
    `created_user`     varchar(50) default '' comment '创建人',
    `updated_user`     varchar(50) default '' comment '更新人',
    `is_deleted`       varchar(6)  default 'false' comment '是否删除',
    `version`          int         default 0,
    key idx_snapId (company_id, rule_id)
) comment '等级分布组规则主体信息快照,来源score_rule';

drop table if exists emp_of_rank_rule_snap;
create table emp_of_rank_rule_snap
(
    snap_id        varchar(50) comment '快照id',
    company_id     varchar(50) comment '公司id',
    rule_id        varchar(50) comment '等级分值规则id',
    emp_id         varchar(50) comment '员工id',
    emp_org_id     varchar(50) comment '员工所在部门id',
    `created_time` datetime null comment '创建时间',
    `updated_time` datetime null comment '更新时间',
    `created_user` varchar(50) default '' comment '创建人',
    `updated_user` varchar(50) default '' comment '更新人',
    `is_deleted`   varchar(6)  default 'false' comment '是否删除',
    `version`      int         default 0,
    key idx_snapId (company_id, snap_id, rule_id)
) comment '等级分布组规则人员应用范围快照,解析到组织员工,来源emp_ref_score_rule';

drop table if exists dept_of_rank_rule_snap;
create table dept_of_rank_rule_snap
(
    snap_id        varchar(50) comment '快照id',
    company_id     varchar(60) not null comment '公司id',
    org_id         varchar(60) not null comment '部门id',
    rule_id        varchar(50) not null comment '规则id',
    `created_time` datetime    null comment '创建时间',
    `updated_time` datetime    null comment '更新时间',
    `created_user` varchar(50) default '' comment '创建人',
    `updated_user` varchar(50) default '' comment '更新人',
    `is_deleted`   varchar(6)  default 'false' comment '是否删除',
    `version`      int         default 0,
    key idx_snapId (company_id, snap_id, rule_id)
) comment '等级分布组规则部门应用范围快照,解析到组织';


drop table if exists rank_rule_score_range_snap;;
create table rank_rule_score_range_snap
(
    snap_id          varchar(50) comment '快照id',
    company_id       varchar(50)          not null comment '公司id',
    score_rule_id    int                  not null comment '分布组id',
    step_id          int                  not null comment '等级id',
    step_name        varchar(50)          not null comment '等级名字',
    min              decimal(10, 2)       not null comment '最小分值含有',
    max              decimal(10, 2)       not null comment '最大分值',
    min_append_equal int        default 0 null comment '0拼在max上，1拼在min上',
    perf_coefficient text                 null comment '等级对应绩效系数/绩效系数公式',
    coeff_type       int(1)               null comment '系数类型：1=固定系数值，2=自定义公式计算系数',
    place            int(1)               null comment '系数保留小数位',
    field_json       text                 null comment '公式字符串,页面回填使用',
    rate             decimal(10, 2)       not null comment '排名占比',
    created_time     datetime,
    created_user     varchar(50),
    updated_time     datetime,
    updated_user     varchar(50),
    is_deleted       varchar(6) default 'false',
    version          int        default 0,
    index idx_snapId (company_id, snap_id)
) comment '按分数等级|排名方式生成规则配置表';




/**  校准驳回  **/
drop table if exists result_audit_batch;
create table result_audit_batch
(
    company_id     varchar(50) null comment '公司id',
    id             varchar(50) primary key comment '负责人角度的:提示批次id',
    cycle_id       varchar(50) comment '周期id',
    owner_emp_id   varchar(50) null comment '处理负责人:perf_evaluate_task_score_result.scorer_id',
    req_no         varchar(50) comment '驳回人提交批次号',
    reject_emp_id  varchar(50) comment '驳回人id',
    reject_reason  text comment '驳回原因',
    `status`       int(2)      default 1 comment '是否全部处理完成 1 = 未完成, 2 = 已完成',
    `cnt`       int     default 0 comment '驳回人数',
    `created_time` datetime        null comment '创建时间',
    `updated_time` datetime        null comment '更新时间',
    `created_user` varchar(50) default '' comment '创建人',
    `updated_user` varchar(50) default '' comment '更新人',
    `is_deleted`   varchar(6)  default 'false' comment '是否删除,处理完所有细项则变成 true',
    `version`      int         default 0,
    key idx_auditId (company_id, owner_emp_id, cycle_id)
) comment '负责人角度的校准驳回批次';


drop table if exists result_audit_batch_item;
create table result_audit_batch_item
(
    company_id          varchar(50) null comment '公司id',
    rank_audit_batch_id varchar(50) comment '外键批次id:rank_audit_batch.id',
    task_user_id        varchar(50) comment '员工任务id',
    emp_name       varchar(50) comment '员工名',
    avatar       varchar(256) comment '员工头像',
    org_name       varchar(50) comment '员工部门',
    task_id             varchar(50) null comment '管理任务id',
    `status`            int(2)      default 1 comment '是否处理完成 1 = 未完成, 2 = 已完成',
    `created_time`      datetime        null comment '创建时间',
    `updated_time`      datetime        null comment '更新时间',
    `created_user`      varchar(50) default '' comment '创建人',
    `updated_user`      varchar(50) default '' comment '更新人',
    `is_deleted`        varchar(6)  default 'false' comment '是否删除,处理完所有细项则变成 true',
    `version`           int         default 0,
    key idx_auditItemId (company_id, rank_audit_batch_id, task_user_id),
    key idx_auditTaskId (company_id, task_id)
) comment '负责人角度的校准驳回批次明细项';

/**  校准暂存  **/
drop table if exists result_audit_cache;
create table result_audit_cache
(
    company_id       varchar(50)    null comment '公司id',
    id               varchar(50)    null comment 'id',
    task_user_id     varchar(50) comment '员工任务id:task_user.id',
    final_score      decimal(11, 3) null comment '考核最终评分',
    evaluation_level varchar(20)    null comment '考评等级',
    step_id          varchar(50)    null comment '等阶id',
    perf_coefficient            varchar(10)                  null comment '绩效系数',
    score_comment    text comment '校准理由,对应perf_evaluate_task_score_result.score_comment ',
    item_scores      text comment '校准的指标分jsonAry:[{"kpiTypeId":"1001",kpiItemId:"1002",score:"30"},{}]',
    type_scores      text comment '校准的维度等级jsonAry:[{typeId:"1001",level:"优秀"},{}]',
    owner_emp_id     varchar(50)    null comment '暂存操作人',
    `created_time`   datetime           null comment '创建时间',
    `updated_time`   datetime           null comment '更新时间',
    `created_user`   varchar(50) default '' comment '创建人',
    `updated_user`   varchar(50) default '' comment '更新人',
    `is_deleted`     varchar(6)  default 'false' comment '是否删除',
    `version`        int         default 0,
    key idx_auditItemId (company_id, owner_emp_id, task_user_id)
) comment '校准暂存表';

select *from result_audit_cache;
select *from employee_base_info where employee_id='ac317717-2835-46ea-b693-60ac0b6e9302';