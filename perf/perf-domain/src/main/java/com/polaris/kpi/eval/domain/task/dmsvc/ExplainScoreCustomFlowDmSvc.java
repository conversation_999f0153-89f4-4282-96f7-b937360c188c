package com.polaris.kpi.eval.domain.task.dmsvc;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.polaris.kpi.eval.domain.task.entity.EvalItemScoreRule;
import com.polaris.kpi.eval.domain.task.entity.EvalKpi;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalKpiType;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalRule;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.lufei.ibatis.common.data.ToDataBuilder;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.domain.task.dmsvc
 * @Author: suxiaoqiu
 * @CreateTime: 2025-04-21  09:22
 * @Description: 解析评分人领域服务[自定义评分流程]
 * @Version: 2.0
 */
@Slf4j
@Getter
public class ExplainScoreCustomFlowDmSvc extends AbstractEvalScorerDmSvc {

    public ExplainScoreCustomFlowDmSvc(EvalUser taskUser, EmpEvalRule rule, TenantId companyId, String opEmpId) {
        super(taskUser,rule,companyId,opEmpId);
    }

    @Override
    public void explainEvalScorer() {
        //自定义评分流程 -- 指标流程为空，继承维度的
        for (EmpEvalKpiType kpiType : this.rule.getKpiTypes().getDatas()) {
            if (kpiType.isNoNeedScoreType()) {//维度无需评价
                continue;
            }
           customTypeRule(kpiType);//解析维度自定义评分人
            List<EvalKpi> items = kpiType.getItems();
            if (items == null || items.isEmpty()) {
                continue;
            }
            for (EvalKpi kpi : items) {
                //指标无需评价
                if (kpi.itemNoNeedScore(kpiType.getScoreOptType())) {
                    continue;
                }
                customItemRule(kpi); //自定义评分流程
            }
        }
    }


    //自定义的评分流程
    private void customTypeRule(EmpEvalKpiType kpiType) {
        if (Objects.isNull(kpiType) || Objects.isNull(kpiType.getTypeRule())) {
            return;
        }

        //普通指标，且无指标流程的
        List<EvalKpi> items = new ArrayList<>();
        for (EvalKpi item : kpiType.getItems()) {
            //指标无需评价
            if (item.itemNoNeedScore(kpiType.getScoreOptType())){
                continue;
            }
            if (item.isNormal() && Objects.isNull(item.getItemScoreRule())) {
                items.add(item);
            }
        }
        if (CollUtil.isEmpty(items)) {
            return;
        }
        //是否存在维度下指标没有流程 的，存在则继承维度的
        this.makeScorer(items,
                kpiType.getSelfRater(),
                kpiType.getPeerRater(),
                kpiType.getSubRater(),
                kpiType.getAppointRater(),
                kpiType.getSuperRater());//构建评分环节及环节评价指标
    }

    private void customItemRule(EvalKpi kpi) {
        if ((Objects.isNull(kpi)
                || Objects.isNull(kpi.getItemScoreRule()))) {
            return;
        }
        EvalItemScoreRule itemRule = kpi.getItemScoreRule();
        //todo
        this.makeScorer(Collections.singletonList(kpi),
                itemRule.getSelfRater(),
                itemRule.getPeerRater(),
                itemRule.getSubRater(),
                itemRule.getAppointRater(),
                itemRule.getSuperRater());//构建评分环节及环节评价指标
    }
}
