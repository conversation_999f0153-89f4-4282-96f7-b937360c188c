package com.polaris.kpi.extData.domain.type;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.polaris.kpi.extData.domain.entity.ExtDataField;
import com.polaris.kpi.extData.domain.type.enums.SymbolEnum;
import com.polaris.kpi.common.KpiI18NException;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: xuxw
 * @Date: 2025/03/06 11:53
 * @Description: Api组件参数解析
 */
public class ApiParamAnalysis extends ParamAnalysis {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final Pattern TIME_PATTERN = Pattern.compile("\\$\\{(now)([+-]\\d+[dmyDMY])?[，,](.*?)\\}");
    private static final List<DateTimeFormatter> FORMATTERS = Arrays.asList(
            DateTimeFormatter.ofPattern("yyyy-MM"),
            DateTimeFormatter.ofPattern("yyyy/MM"),
            DateTimeFormatter.ofPattern("yyyy.MM")
    );

    public Map<String, String> apiParamAnalysis(String input, Map<String, String> stepMap) {
        Map<String, String> resultMap = new HashMap<>();

        try {
            String[] pairs = input.split(SymbolEnum.AMPERSAND.getSymbol());
            for (String pair : pairs) {
                String[] keyValue = pair.split(SymbolEnum.EQUAL.getSymbol());
                if (keyValue.length != 2) {
                    throw new KpiI18NException("param.error", "外部数据系统动态参数配置异常");
                }

                String key = keyValue[0];
                String value = keyValue[1];

                if (value.startsWith(SymbolEnum.LEFT_CURLY_BRACE.getSymbol())
                        && value.endsWith(SymbolEnum.RIGHT_CURLY_BRACE.getSymbol())) {
                    if (ObjectUtil.isNull(stepMap)){
                        throw new KpiI18NException("param.error", "外部数据系统组件返回值异常");
                    }
                    value = resolveDynamicParam(value.substring(1, value.length() - 1), stepMap);
                }

                resultMap.put(key, value);
            }
        } catch (Exception e) {
            throw new KpiI18NException("param.error", "外部数据系统API参数解析异常");
        }
        return resultMap;
    }

    private static String resolveDynamicParam(String dynamicParam, Map<String, String> stepMap) {
        try {
            if (dynamicParam.startsWith(SymbolEnum.AT.getSymbol())) {
                return resolveStepValue(dynamicParam, stepMap);
            } else {
                throw new KpiI18NException("param.error", "外部数据系统参数配置异常");
            }
        } catch (Exception e) {
            throw new KpiI18NException("param.error", "外部数据系统参数配置异常");
        }
    }

    private static String resolveStepValue(String dynamicParam, Map<String, String> stepMap) {
        try {
            String stepKey = dynamicParam.substring(0, 2);

            if (!stepMap.containsKey(stepKey)) {
                throw new KpiI18NException("param.error", "外部数据系统组件返回值异常");
            }

            String stepValue = stepMap.get(stepKey);

            if (dynamicParam.length() == 2) {
                return stepValue;
            }

            String path = dynamicParam.substring(3);
            JsonNode node = objectMapper.readTree(stepValue).at(SymbolEnum.SLASH.getSymbol() +
                    path.replace(SymbolEnum.POINT.getSymbol(), SymbolEnum.SLASH.getSymbol()));
            if (node.isMissingNode()) {
                throw new KpiI18NException("param.error", "外部数据系统参数解析异常");
            }
            return node.asText();
        } catch (Exception e) {
            throw new KpiI18NException("param.error", "外部数据系统组件返回值异常");
        }
    }

    public void buildReqParams(ExtDataField fieldInfo) {
        String reqHeaders = fieldInfo.getReqHeaders();
        String queryParams = fieldInfo.getQueryParams();
        String body = fieldInfo.getQueryBody();
        Integer bodyType = fieldInfo.getQueryBodyType();
        if (StrUtil.isNotEmpty(reqHeaders) && reqHeaders.contains("now")){
            fieldInfo.setReqHeaders(replaceTemplates(reqHeaders));
        }
        if (StrUtil.isNotEmpty(queryParams) && queryParams.contains("now")){
            fieldInfo.setQueryParams(replaceTemplates(queryParams));
        }
        if (StrUtil.isNotEmpty(body) && body.contains("now")){
            if (bodyType == 1){
                fieldInfo.setQueryBody(replaceTemplates(queryParams));
            }else if (bodyType == 2){
                fieldInfo.setQueryBody(processJsonTemplates(body));
            }
        }
    }

    public static String processJsonTemplates(String jsonString) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(jsonString);
            JsonNode jsonNode = processNode(rootNode, mapper);
            return mapper.writeValueAsString(jsonNode);
        }catch (Exception e){
            throw new KpiI18NException("param.error", "外部数据系统参数解析异常");
        }

    }
    private static JsonNode processNode(JsonNode node, ObjectMapper mapper) {
        if (node.isObject()) {
            ObjectNode objectNode = mapper.createObjectNode();
            Iterator<Map.Entry<String, JsonNode>> fields = node.fields();
            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> entry = fields.next();
                objectNode.set(entry.getKey(), processNode(entry.getValue(), mapper));
            }
            return objectNode;
        } else if (node.isArray()) {
            JsonNode arrayNode = mapper.createArrayNode();
            for (JsonNode arrayElement : node) {
                ((com.fasterxml.jackson.databind.node.ArrayNode) arrayNode)
                        .add(processNode(arrayElement, mapper));
            }
            return arrayNode;
        } else if (node.isTextual()) {
            String textValue = node.asText();
            Matcher matcher = TIME_PATTERN.matcher(textValue.replaceAll(" ", ""));

            if (matcher.find()) {
                String offset = matcher.group(2);
                String format = matcher.group(3);
                String calculatedTime;
                if (offset != null) {
                    calculatedTime = calculateTime(offset, format);
                } else {
                    calculatedTime = LocalDateTime.now()
                            .format(DateTimeFormatter.ofPattern(format));
                }
                return mapper.getNodeFactory().textNode(calculatedTime);
            }
        }
        return node;
    }

    public static String replaceTemplates(String input) {
        Matcher matcher = TIME_PATTERN.matcher(input.replaceAll(" ",""));
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String offset = matcher.group(2);
            String format = matcher.group(3);
            String replacement;
            if (offset == null) {
                replacement = LocalDate.now().format(DateTimeFormatter.ofPattern(format));
            } else {
                replacement = calculateTime(offset, format);
            }
            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    private static String calculateTime(String timeExpr, String format) {
        Pattern exprPattern = Pattern.compile("([+-])(\\d+)([dmy])");
        Matcher matcher = exprPattern.matcher(timeExpr);

        if (!matcher.matches()) {
            throw new IllegalArgumentException("Invalid time expression: " + timeExpr);
        }

        String operator = matcher.group(1);
        int amount = Integer.parseInt(matcher.group(2));
        char unit = matcher.group(3).charAt(0);

        LocalDate date = LocalDate.now();

        switch (unit) {
            case 'd':
                date = applyOffset(date, operator, amount, ChronoUnit.DAYS);
                break;
            case 'm':
                date = applyOffset(date, operator, amount, ChronoUnit.MONTHS);
                break;
            case 'y':
                date = applyOffset(date, operator, amount, ChronoUnit.YEARS);
                break;
            default:
                throw new IllegalArgumentException("Unsupported time unit: " + unit);
        }
        return date.format(DateTimeFormatter.ofPattern(format));
    }

    private static LocalDate applyOffset(LocalDate date, String operator, int amount, ChronoUnit unit) {
        if ("+".equals(operator)) {
            return date.plus(amount, unit);
        } else if ("-".equals(operator)) {
            return date.minus(amount, unit);
        } else {
            throw new IllegalArgumentException("Unsupported operator: " + operator);
        }
    }

    public static String getDayOfMonth(String data) {
        // 遍历所有可能的格式，尝试解析
        for (DateTimeFormatter formatter : FORMATTERS) {
            try {
                YearMonth yearMonth = YearMonth.parse(data, formatter);
                return yearMonth.atDay(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } catch (DateTimeParseException ignored) {
                // 当前格式解析失败，继续尝试下一个
            }
        }
        return null;
    }
}
