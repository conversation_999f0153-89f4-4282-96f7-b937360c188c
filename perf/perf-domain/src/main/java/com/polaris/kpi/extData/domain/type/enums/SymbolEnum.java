package com.polaris.kpi.extData.domain.type.enums;

public enum SymbolEnum {

    AT("@", "@符号"),
    LEFT_CURLY_BRACE("{","左花括号"),
    RIGHT_CURLY_BRACE("}","右花括号"),
    SQUARE_BRACKETS("[]","中括号"),
    AMPERSAND("&","&符号"),
    POINT(".","点"),
    SLASH("/", "斜杠"),
    EQUAL("=", ""),
    QUESTION("?", "");
    private String symbol;
    private String desc;

    SymbolEnum(String symbol, String desc) {
        this.symbol = symbol;
        this.desc = desc;
    }

    public String getSymbol() {
        return symbol;
    }
    public String getDesc() {
        return desc;
    }
}
