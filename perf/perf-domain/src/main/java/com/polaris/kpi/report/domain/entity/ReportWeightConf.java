package com.polaris.kpi.report.domain.entity;

import cn.com.polaris.kpi.temp.CycleTypeEnum;
import cn.hutool.core.collection.CollUtil;
import com.polaris.sdk.type.ListWrap;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Getter
@Setter
public class ReportWeightConf {
    private List<ReportWeightSetting> monthSettings;
    private List<ReportWeightSetting> quarterSettings;

    public ReportWeightConf(String year, String companyId, List<ReportWeightSetting> settings) {
        if (CollUtil.isEmpty(settings)) {
            this.initDefualt(year, companyId, null);
            return;
        }
        ListWrap<ReportWeightSetting> settingGroups = new ListWrap<>(settings).groupBy(ReportWeightSetting::getCycleType);
        monthSettings = settingGroups.groupGet(CycleTypeEnum.MONTH.getType());
        quarterSettings = settingGroups.groupGet(CycleTypeEnum.QUARTER.getType());
        if (CollUtil.isEmpty(monthSettings)) {
            monthSettings = ReportWeightSetting.initDefualtMonth(year, CycleTypeEnum.MONTH.getType(), companyId, null);
        }
        if (CollUtil.isEmpty(quarterSettings)) {
            quarterSettings = ReportWeightSetting.initDefualtQuarter(year, CycleTypeEnum.QUARTER.getType(), companyId, null);
        }
    }

    public void initDefualt(String year, String companyId, String createdUser) {
        monthSettings = ReportWeightSetting.initDefualtMonth(year, CycleTypeEnum.MONTH.getType(), companyId, createdUser);
        //初始化季度权重数据
        quarterSettings = ReportWeightSetting.initDefualtQuarter(year, CycleTypeEnum.QUARTER.getType(), companyId, createdUser);
    }

    public Map<Integer, BigDecimal> monthWeightMap() {
        return monthSettings.stream().collect(Collectors.toMap(s -> s.getMonth(), o -> o.getWeight()));
    }

    public Map<Integer, BigDecimal> quarterWeightMap() {
        return quarterSettings.stream().collect(Collectors.toMap(s -> s.getMonth(), o -> o.getWeight()));
    }
}
