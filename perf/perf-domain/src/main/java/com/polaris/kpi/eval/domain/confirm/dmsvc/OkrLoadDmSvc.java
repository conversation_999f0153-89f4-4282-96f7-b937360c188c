package com.polaris.kpi.eval.domain.confirm.dmsvc;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.polaris.kpi.eval.domain.task.entity.EvalKpi;
import com.polaris.kpi.eval.domain.task.entity.OkrEvalKpi;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalKpiType;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrAction;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrActionUpdate;
import com.polaris.kpi.eval.domain.task.ext.IOkrAclSvc;
import com.polaris.kpi.org.domain.dept.entity.Company;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public class OkrLoadDmSvc {
    private IOkrAclSvc evalOkrAcl;
    private Company company;

    public OkrLoadDmSvc(IOkrAclSvc evalOkrAcl, Company company) {
        this.evalOkrAcl = evalOkrAcl;
        this.company = company;
    }

    public void loadOkrIf(TenantId tenantId, EmpEvalMerge rule) {
        loadActionForKpi(tenantId, rule);

        loadActionForOkr(rule);
    }

    private void loadActionForKpi(TenantId tenantId, EmpEvalMerge rule) {
        List<OkrEvalKpi> allOkrEvalKpis = new ArrayList<>();
        for (EmpEvalKpiType type : rule.getKpiTypes().getDatas()) {
            //从绩效关联okr的指标
            if (CollUtil.isEmpty(type.getItems())) {
                continue;
            }
            List<EvalKpi> okrEvalKpis = type.getItems().stream().map(item -> {
                OkrEvalKpi convert = Convert.convert(OkrEvalKpi.class, item);
                if (item.getRefOkr() == null) {
                    return convert;
                }
                convert.setOkrTaskId(item.getRefOkr().getOkrTaskId());
                convert.setOkrTaskName(item.getRefOkr().getOkrTaskName());
                convert.setEvaluateEndDate(item.getRefOkr().getEvaluateEndDate());
                convert.setEvaluateStartDate(item.getRefOkr().getEvaluateStartDate());
                if (StrUtil.isNotBlank(convert.getActionId())) {
                    allOkrEvalKpis.add(convert);
                }
                return convert;
            }).collect(Collectors.toList());
            type.setItems(okrEvalKpis);
            type.getItems().sort(Comparator.comparingInt(o -> ObjectUtil.defaultIfNull(o.getOrder(), 0)));
        }
        if (CollUtil.isNotEmpty(allOkrEvalKpis)) {
            List<String> actionIds = allOkrEvalKpis.stream().map(EvalKpi::getActionId).collect(Collectors.toList());
            List<OkrAction> okrActions = evalOkrAcl.loadActionByIds(company.getDingCorpId(), actionIds);
            EvalKpi kpiItemPo = allOkrEvalKpis.get(0);
            ListWrap<OkrActionUpdate> updateListWrap = evalOkrAcl.listActionUpdateInfo(tenantId, kpiItemPo.getEmpId(), actionIds);
            /**查询成果最新一条更新记录*/
            Map<String, OkrActionUpdate> actionUpdateMap = evalOkrAcl.getActionLastUpdateInfo(company.getDingCorpId(), actionIds);
            Map<String, OkrAction> actionMap = okrActions.stream().collect(Collectors.toMap(OkrAction::getId, Function.identity()));
            for (OkrEvalKpi okrItem : allOkrEvalKpis) {
                //设置OKR信息
                if (!actionMap.containsKey(okrItem.getActionId())) {
                    continue;
                }
                OkrAction action = actionMap.get(okrItem.getActionId());
                action.splitTargetTag();
                okrItem.setItemFinishValue(action.getFinishValue());
                okrItem.setConfidenceIndex(action.getConfidenceIndex());
                okrItem.acceptOkrValue(action);
                if (actionUpdateMap != null && actionUpdateMap.size() > 0) {
                    if (actionUpdateMap.containsKey(okrItem.getActionId())) {
                        okrItem.setOkrActionUpdate(JSON.parseObject(String.valueOf(actionUpdateMap.get(okrItem.getActionId())), OkrActionUpdate.class));
                    }
                }
                List<OkrActionUpdate> okrActionUpdates = updateListWrap.groupGet(okrItem.getActionId());
                okrItem.setOkrActionUpdates(okrActionUpdates);
            }
        }
    }

    private void loadActionForOkr(EmpEvalMerge rule) {
        List<OkrEvalKpi> refKrItems = new ArrayList<>();
        //从okr端关联绩效的指标
        for (EmpEvalKpiType type : rule.getKpiTypes().getDatas()) {
            if (CollUtil.isEmpty(type.getItems())) {
                continue;
            }
            List<EvalKpi> baseKpis = type.getItems().stream().filter(k -> Boolean.valueOf(k.getOkrRefFlag())).collect(Collectors.toList());
            List<OkrEvalKpi> okrEvalKpis = Convert.toList(OkrEvalKpi.class, baseKpis);
            refKrItems.addAll(okrEvalKpis);
        }
        if (CollUtil.isEmpty(refKrItems)) {
            return;
        }
        //在循环外进行批量查询，需要okr那边在OkrAction中增加refId的返回
//        List<String> refIds = refKrItems.stream().map(EmpEvalDetailKpiItemPo::getId).collect(Collectors.toList());
//        List<OkrAction> okrActions = evalOkrAcl.loadActionByItemIds(company.getDingCorpId(), refIds);
//        ListWrap<OkrAction> okrActionMap = new ListWrap<>(okrActions).groupGet(OkrAction::getRefId);

        for (OkrEvalKpi refKrItem : refKrItems) {
            List<OkrAction> okrActions2 = evalOkrAcl.loadActionByItemId(company.getDingCorpId(), refKrItem.getId());
            if (CollUtil.isEmpty(okrActions2)) {
                continue;
            }
            Map<String, OkrActionUpdate> actionUpdateMap = evalOkrAcl.getActionLastUpdateInfo(company.getDingCorpId(), okrActions2.stream().map(OkrAction::getId).collect(Collectors.toList()));
            for (OkrAction okrAction : okrActions2) {
                okrAction.accpKrScore();
                if (actionUpdateMap != null && actionUpdateMap.size() > 0) {
                    if (actionUpdateMap.containsKey(okrAction.getId())) {
                        okrAction.setOkrActionUpdate(JSON.parseObject(String.valueOf(actionUpdateMap.get(okrAction.getId())), OkrActionUpdate.class));
                    }
                }
            }
            refKrItem.setItemRefKRList(okrActions2);
        }
    }

}
