package com.polaris.kpi.eval.domain.task.entity.empeval.ecfg;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.eval.Pecent;
import cn.com.polaris.kpi.eval.Rater;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.perf.www.common.constant.BusinessConstant;
import com.polaris.kpi.eval.domain.task.entity.EvalAudit;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.domain.task.type.InnerFields;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @Author: lufei
 * @CreateTime: 2022-08-31  15:29
 * @Description: *_task_audit 表基类
 * @Version: 1.0
 */
@NoArgsConstructor
@Getter
@Setter
public class BaseAuditNode {
    public static String MULTI_OR = "or";
    public static String MULTI_AND = "and";

    protected String id;//
    protected String taskUserId;//
    protected String node;                  //结点名 "sub_score=下级互评" ,"peer_score=同级互评"
    protected String nodeTitle;             //审批节点标题  标题
    protected String nodeType;              //审批节点Type
    protected Integer approvalOrder;        //审批顺序（从1开始递增）
    protected String approverType;          //审批人类型（指定级别主管=manage|指定人员=user|考核人=taskEmp|role=角色|taskAdmin=考核任务发起人|ind=按指标上配置)
    protected String approverInfo;          //指定对象id (指定级别主管=1|指定人员=员工id |考核人=员工id ） taskEmp
    protected String approverName;          //指定角色名
    private BigDecimal weight;              //子结点权重,评分流程才有权重,评分环节1->N子结点1->N人员
    protected String multiType;             //多个审核人时会签还是或签 and |or  旧:multipleReviewersType
    private String vacancyApproverType;     //审批人空缺时指定人类型（管理员/指定人员）
    private String vacancyApproverInfo;     //审批人空缺时指定人id
    protected String status;                //分发状态 dispatched
    protected String transferFlag;          //是否可转交
    protected String modifyFlag;            //是否可以修改
    private String modifyItemDimension;//修改指标维度  默认可以修改指标全部内容 "all","target", 后面可能扩展 "target,itemName,.."
    //    private String modifyItemDimension;//修改指标维度  默认可以修改指标全部内容 "all","target", 后面可能扩展 "target,itemName,.."
//    private String confirmAuditSign;        //手写签名开关
    protected List<Rater> raters = new ArrayList<>();           //此环节审核人员多人

    public BaseAuditNode(Integer approvalOrder, String multiType) {
        this.approvalOrder = approvalOrder;
        this.multiType = multiType;
    }

    //单人处理
    public BaseAuditNode(Integer approvalOrder, String multiType, Rater rater) {
        this.approvalOrder = approvalOrder;
        this.multiType = multiType;
        addRater(rater);
    }

    public BaseAuditNode(Integer approveOrder, String approveType, String approveInfo, String approverName) {
        this.approvalOrder = approveOrder;
        this.approverType = approveType;
        this.approverInfo = approveInfo;
        this.approverName = approverName;
    }


    public BaseAuditNode(String node, Integer approveOrder, String approveInfo, String approveType, BigDecimal weight) {
        this.node = node;
        this.approvalOrder = approveOrder;
        this.approverType = approveType;
        this.approverInfo = approveInfo;
        this.weight = weight;
    }

    public BaseAuditNode(String approveType, List<Rater> raters) {
        this.approverType = approveType;
        this.raters = raters;
    }

    public void rater(String raterId, String raterName) {
        raters.add(new Rater(raterId, raterName, new BigDecimal(100)));
    }

    public void addRater(Rater rater) {
        raters.add(rater);
    }

    public void addAllRater(List<Rater> toAdds) {
        this.raters.addAll(toAdds);
    }

    public void allowOperation(String transferFlag, String modifyFlag) {
        this.transferFlag = transferFlag;
        this.modifyFlag = modifyFlag;
    }

    public void extendMutiConf(ScoreConf scoreConf) {
        this.multiType = scoreConf.getMultiType();
        this.vacancyApproverType = scoreConf.getVacancyApproveType();
        this.vacancyApproverInfo = scoreConf.getVacancyApproveInfo();
        this.transferFlag = scoreConf.getTransferFlag();
    }

    public void checkRequest() {
        //todo xzl 管理任务上匹配评分顺序
        //Assert.notNull(approvalOrder, "approvalOrder 不能空");
        //Assert.notNull(multiType, "multiType:多个审核人时会签还是或签 and|or 不能空");
        //Assert.notNull(transferFlag, "transferFlag:是否可转交true,false 不能空");
        //Assert.notNull(modifyFlag, "modifyFlag:是否可以修改 :true,false 不能空");
        //Assert.notEmpty(raters, "auditNode.raters 子结点中的人员不能空 ");
    }

    @JSONField(serialize = false)
    public boolean isAndMode() {
        return MULTI_AND.equalsIgnoreCase(multiType);
    }

    public boolean hasEmptyRater() {
        return CollUtil.isEmpty(raters);
    }
    public void replaceRater(String fromEmpId, KpiEmp toEmp, Boolean raterExist) {
        if (raterExist) {
            raters = raters.stream()
                    .filter(s -> !StrUtil.equals(s.getEmpId(), fromEmpId))
                    .collect(Collectors.toList());
        }else {
            this.replaceRater(fromEmpId, toEmp);
        }
    }
    private void replaceRater(String fromEmpId, KpiEmp toEmp) {
        raters.stream().filter(rater -> StrUtil.equals(fromEmpId, rater.getEmpId())).forEachOrdered(rater -> {
            rater.setEmpId(toEmp.getEmpId());
            rater.setEmpName(toEmp.getEmpName());
            rater.setAvatar(toEmp.getAvatar());
        });
    }
    //计算人员权重
    public void avgRaterWeight() {
        List<Rater> raters = getRaters();
        if (CollUtil.isEmpty(raters)) {
            return;
        }
        if (isAndMode()) {
            Pecent avgPect = new Pecent(weight, raters.size());
            raters.forEach(rater -> rater.setWeight(avgPect.getAvgWeight()));
            //权重除不尽的差值加到第一位上
            raters.get(0).setWeight(avgPect.getAvgAddDiff());
        } else {
            raters.forEach(rater -> rater.setWeight(weight));
        }
    }

    //[第2位|张三,李四|允许修改|允许转交|或签]=>[第3位|张三,李四|允许修改|允许转交|或签]
    public String auditName() {
        StringBuilder sb = new StringBuilder();
        if(isOnIndAudit()){
            sb.append("[指标审批人|");
        }else {
            sb.append("[第").append(approvalOrder).append("位|");
        }
        //指定级别主管=manage|指定人员=user|考核人=taskEmp）
        if ("taskEmp".equals(approverType)) {
            sb.append("考核人|");
        }
        if ("manager".equals(approverType)) {
            sb.append(approverInfo).append("级主管理|");
        }
        if ("user".equals(approverType)) {
            sb.append(approverInfo).append("指定人员|");
        }
        for (Rater rater : raters) {
            sb.append(rater.raterName()).append(",");
        }
        sb.append(names());
        sb.append(Boolean.valueOf(modifyFlag) ? "允许修改|" : "不允许修改|");
        sb.append(Boolean.valueOf(transferFlag) ? "允许转交|" : "不允许转交|");
        sb.append(isAndMode() ? "会签|" : "或签|");
        sb.append("]");
        return sb.toString();
    }

    private String indAuditName() {
        if (isOnIndAudit()) {
            return "指标审批人";
        }
        return "";
    }

    public StringBuilder auditFlowName() {
        StringBuilder sb = new StringBuilder();
        if(isOnIndAudit()){
            sb.append("[指标审批人|");
        }else {
            sb.append("[第").append(approvalOrder).append("位|");
        }
        if ("taskEmp".equals(approverType)) {
            sb.append("考核人|");
        }
        if ("manager".equals(approverType)) {
            sb.append("指定主管|");
        }
        if ("user".equals(approverType)) {
            sb.append("指定人员|");
        }
        return sb;
    }

    @NotNull
    public String modifyFlagName() {
        if (modifyFlag == null) {
            return "";
        }
        StringBuilder sb = auditFlowName();
        sb.append(Boolean.valueOf(modifyFlag) ? "允许修改]" : "不允许修改]");
        return sb.toString();
    }

    @NotNull
    public String multiTypeName() {
        if (multiType == null) {
            return "";
        }
        return isAndMode() ? "会签" : "或签";
    }

    @NotNull
    public String transferFlagName() {
        if (transferFlag == null) {
            return "";
        }
        return Boolean.valueOf(transferFlag) ? "允许" : "不允许";
    }

    //抽象流程
    public String abstractFlowName() {
        if (CollUtil.isNotEmpty(this.raters)) {
            //有了具体的人，应该显示具体的而不是抽象的信息
            return flowNames();
        }

        if ("taskEmp".equals(approverType)) {
            return "被考核人";
        }
        if ("manager".equals(approverType)) {
            if (approverInfo.equals("1")) {
                return "直属主管";
            }
            return approverInfo + "级主管理";
        }
        if ("user".equals(approverType)) {
            return "指定人员" + (StrUtil.isNotBlank(approverName) ? approverName : "");
        }
        if ("role".equals(approverType)) {
            return approverName;
        }
        return "";
    }

    //抽象流程
    public String abstractFlowNameAppendModifyFlag() {
        if (CollUtil.isNotEmpty(this.raters)) {
            //有了具体的人，应该显示具体的而不是抽象的信息
            return flowNames() + modifyName();
        }

        if ("taskEmp".equals(approverType)) {
            return "被考核人" + modifyName();
        }
        if ("manager".equals(approverType)) {
            if (approverInfo.equals("1")) {
                return "直属主管" + modifyName();
            }
            return approverName + modifyName();
        }
        if ("user".equals(approverType)) {
            return "指定人员：" + (StrUtil.isNotBlank(approverName) ? approverName : "") + modifyName();
        }
        if ("role".equals(approverType)) {
            return approverName + modifyName();
        }
        return "";
    }

    private String modifyName() {
        return Objects.nonNull(modifyFlag) && StrUtil.equals(modifyFlag, "true") ? "（允许修改）" : "（不允许修改）";
    }

    //具体的人
    public String flowNames() {
        if ("taskEmp".equals(approverType)) {
            return "被考核人";
        }
        if ("manager".equals(approverType) || "user".equals(approverType) || "role".equals(approverType)) {
            InnerFields<String> names = new InnerFields<>();
            for (Rater rater : raters) {
                if ("role".equals(approverType) && StrUtil.isBlank(rater.getEmpName())) {
                    names.add(rater.getRoleName());
                } else {
                    names.add(rater.getEmpName());
                }
            }
            return names.join(",");
        }
        return "";
    }


    @NotNull
    private String names() {
        return CollUtil.join(raters.stream().map(rater -> rater.raterName())
                .collect(Collectors.toList()), ",");
    }

    @JSONField(serialize = false, deserialize = false)
    public boolean isTaskEmp() {
        return "taskEmp".equals(approverType);
    }

    @JSONField(serialize = false, deserialize = false)
    public boolean isTaskAdmin() {
        return "taskAdmin".equals(approverType);
    }

    @JSONField(serialize = false, deserialize = false)
    public boolean isAppointUser() {
        return "user".equals(approverType);
    }

    @JSONField(serialize = false, deserialize = false)
    public boolean isManager() {
        return "manager".equals(approverType);
    }

    public boolean ratersIsEmpty() {
        return CollUtil.isEmpty(raters);
    }

    public Collection<? extends Rater> initRaterOrder(int i) {
        for (Rater rater : raters) {
            if (this.approvalOrder == 1) {//兼容处理 给1 的重新编号
                this.approvalOrder = i;
            }
            rater.setApprovalOrder(this.approvalOrder);
        }
        return raters;
    }

    public BigDecimal getScoreWeight() {
        return weight;
    }

    public void setScoreWeight(BigDecimal weight) {
        this.weight = weight;
    }

    //public List<Rater> getData() {
    //    return raters;
    //}
    //兼容旧的数据
    public void setData(List<Rater> raters) {
        this.raters = raters;
    }

    public void markRepeat(List<Rater> nextRaters) {
        if (CollUtil.isEmpty(this.raters)) {
            return;
        }
        if (CollUtil.isEmpty(nextRaters)) {
            return;
        }
        List<String> empIds = CollUtil.map(nextRaters, r -> r.getEmpId(), true);
        for (Rater rater : this.raters) {
            if (!rater.isSkip() && empIds.contains(rater.getEmpId())) {
                rater.setSkipType(2);
            }
        }
    }

    protected void explainRaters(String empId, TenantId companyId, EmpId opEmpId, String evalOrgId, String orgId) {
        List<Rater> raterList = new ArrayList<>();
        if (isRoleAudit() || isOrgAudit()) {
            for (Rater rater : getRaters()) {
                EvalAudit tmpAudit = new EvalAudit(approverType, isRoleAudit() ? rater.getRoleId() : rater.getOrgId(),
                        companyId, new EmpId(empId), opEmpId, evalOrgId, orgId);
                raterList.addAll(tmpAudit.genAuditFixRaters());
            }
        } else {
            EvalAudit tmpAudit = new EvalAudit(approverType, approverInfo, companyId, new EmpId(empId), opEmpId, evalOrgId, orgId);
            raterList.addAll(tmpAudit.genAuditFixRaters());
        }
        this.raters = raterList;
    }

    public List<Rater> parseRaters(String empId, TenantId companyId, EmpId opEmpId, String evalOrgId, String orgId) {
        List<Rater> raterList = new ArrayList<>();
        if (isRoleAudit() || isOrgAudit()) {
            for (Rater rater : getRaters()) {
                EvalAudit tmpAudit = new EvalAudit(approverType, isRoleAudit() ? rater.getRoleId() : rater.getOrgId(),
                        companyId, new EmpId(empId), opEmpId, evalOrgId, orgId);
                raterList.addAll(tmpAudit.genAuditFixRaters());
            }
        } else {
            EvalAudit tmpAudit = new EvalAudit(approverType, approverInfo, companyId, new EmpId(empId), opEmpId, evalOrgId, orgId);
            raterList.addAll(tmpAudit.genAuditFixRaters());
        }
        return raterList;
    }

    @JSONField(serialize = false)
    public boolean isOrgAudit() {
        return BusinessConstant.APPROVER_TYPE_CUSTOM_ORG.equals(this.getApproverType())
                || BusinessConstant.APPROVER_TYPE_LOCATION_ORG.equals(this.getApproverType());
    }
    @JSONField(serialize = false)
    public boolean isOnIndAudit() {
        return StrUtil.equals("ind", getApproverType());
    }

    public boolean isRoleAudit() {
        return BusinessConstant.APPROVER_TYPE_ROLE.equals(this.getApproverType());
    }

    @Override
    public BaseAuditNode clone() {
        return JSONUtil.toBean(JSONUtil.toJsonStr(this), BaseAuditNode.class);
    }


    public void addWeight(BigDecimal weight) {
        this.weight = this.weight.add(weight);
        Pecent pecent = new Pecent(this.weight, this.raters.size());
        this.raters.forEach(s -> s.setWeight(pecent.getAvgWeight()));
        this.raters.get(0).setWeight(pecent.getAvgAddDiff());
    }

    public boolean isUserAudit() {
        return BusinessConstant.APPROVER_TYPE_USER.equals(this.getApproverType());
    }

    public void markRater(List<EvalScoreResult> scoreResults) {
        if (CollUtil.isEmpty(scoreResults)) {
            return;
        }
        ListWrap<EvalScoreResult> resultListWrap = new ListWrap<>(scoreResults).groupBy(sr -> sr.getScorerId());
        List<Rater> newRaters = new ArrayList<>();
        resultListWrap.getGroups().forEach((k, v) -> {
            Rater rater = new Rater(k, v.get(0).getScorerName());
            if (v.stream().allMatch(rs -> StrUtil.isBlank(rs.getAuditStatus()))) {
                rater.doingStatus();
            }
            //已完成
            if (v.stream().allMatch(rs -> "pass".equals(rs.getAuditStatus()))) {
                rater.passStatus();
            }
            newRaters.add(rater);
        });
        this.raters = newRaters;
    }

    public void markAll() {
        this.raters.forEach(r -> {
            r.passStatus();
        });
    }

    public void markAll(String status) {
        this.raters.forEach(r -> {
            r.setStatus(status);
        });
    }

    public void replaceRater(KpiEmp originalEmp, KpiEmp newEmp, Integer level) {
        if (Objects.nonNull(level) && !Objects.equals(this.approvalOrder, level)) {
            return;
        }
        if (CollUtil.isEmpty(this.raters)) {
            return;
        }
        if (Objects.nonNull(originalEmp)) {
            this.raters.forEach(rater -> {
                if (Objects.equals(rater.getEmpId(), originalEmp.getEmpId())) {
                    rater.setEmpId(newEmp.getEmpId());
                    rater.setEmpName(newEmp.getEmpName());
                    rater.setAvatar(newEmp.getAvatar());
                    rater.setDingUserId(newEmp.getExUserId());
                }
            });
            return;
        }
        List<Rater> newRaters = new ArrayList<>();
        newRaters.add(new Rater(newEmp.getEmpId(), newEmp.getEmpName(), newEmp.getAvatar(), newEmp.getExUserId(), 0));
        this.raters = newRaters;
    }

    public void modifyRater(KpiEmp kpiEmp, String fromEmpId) {
        for (Rater rater : this.raters) {
            if (rater.getEmpId().equals(fromEmpId)) {
                rater.setEmpId(kpiEmp.getEmpId());
                rater.setEmpName(kpiEmp.getEmpName());
                rater.setAvatar(kpiEmp.getAvatar());
                rater.setDingUserId(kpiEmp.getExUserId());
            }
        }
    }
}
