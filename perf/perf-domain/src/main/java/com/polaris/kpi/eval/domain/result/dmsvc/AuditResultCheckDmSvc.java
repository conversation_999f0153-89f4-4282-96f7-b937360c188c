package com.polaris.kpi.eval.domain.result.dmsvc;

import com.polaris.kpi.eval.domain.stage.dmsvc.BaseAuditNodeChecker;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalRule;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.AuditResultConf;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;

import java.util.List;

public class AuditResultCheckDmSvc {

    private BaseAuditNodeChecker nodeChecker;

    public AuditResultCheckDmSvc(BaseAuditNodeChecker nodeChecker) {
        this.nodeChecker = nodeChecker;
    }

    public void checkEvalRuleErrStatus(List<EvalUser> evalUsers) {
        for (EvalUser evalUser : evalUsers) {
            EmpEvalRule empEvalRule = evalUser.getEmpEvalRule();
            AuditResultConf auditResult = empEvalRule.getAuditResult();
            TalentStatus current = TalentStatus.statusOf(evalUser.getTaskStatus());
            if (auditResult.isOpen() && current.beforeEq(TalentStatus.RESULTS_AUDITING)) {
                nodeChecker.parseCheckFlowError(evalUser, auditResult.getAuditNodes(), "final_result_audit", auditResult.getNodeEmpVacancy());
            } else if (!auditResult.isOpen()) {
                evalUser.removeEvalRuleError("103");
            }
            if (!evalUser.evalRuleIsErr()) {
                evalUser.confEvalRuleOk();//初始值ok
            }
            if (!evalUser.evalRuleConfIsErr()) {
                evalUser.confEvalRuleErrorOk();
            }
        }
    }
}
