package com.polaris.kpi.extData.domain.type.encAndDec;

import com.polaris.kpi.common.KpiI18NException;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * @Author: xuxw
 * @Date: 2025/03/06 09:55
 * @Description:
 */
public class Sha256 extends EncDecAdapter{

    private static final String SHA256 = "SHA-256";

    @Override
    public String enc(String plaintext){
        MessageDigest md = null;
        try {
            md = MessageDigest.getInstance(SHA256);
        } catch (NoSuchAlgorithmException e) {
            throw new KpiI18NException("enc.error", e.toString());
        }
        byte[] hashBytes = md.digest(plaintext.getBytes(StandardCharsets.UTF_8));
        return super.bytesToHex(hashBytes);
    }
}
