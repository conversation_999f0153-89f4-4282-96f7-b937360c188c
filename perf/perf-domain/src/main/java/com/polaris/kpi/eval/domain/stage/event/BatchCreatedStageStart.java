package com.polaris.kpi.eval.domain.stage.event;

import com.polaris.kpi.eval.domain.TaskEvalContext;
import com.polaris.kpi.org.domain.common.BaseEvent;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.Setter;

/**
 * batch考核进入启动阶段
 *
 * <AUTHOR> luf<PERSON>
 * @date 2025/7/23 4:55 下午
 */
@Getter
public class BatchCreatedStageStart extends BaseEvent {
    @Getter
    private TaskEvalContext evalContext;
    @Setter
    private EmpId opEmpId;

    public BatchCreatedStageStart(TenantId tenantId, EmpId opEmpId, TaskEvalContext evalContext) {
        super(tenantId.getId());
        this.evalContext = evalContext;
        this.opEmpId = opEmpId;
    }

    public void start() {
    }
}
