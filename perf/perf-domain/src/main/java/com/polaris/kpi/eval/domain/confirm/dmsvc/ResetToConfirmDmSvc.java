package com.polaris.kpi.eval.domain.confirm.dmsvc;

import cn.com.polaris.kpi.company.MsgSceneEnum;
import cn.com.polaris.kpi.eval.OperationTypeEnum;
import com.alibaba.fastjson.JSONObject;
import com.perf.www.common.em.OperationLogSceneEnum;
import com.polaris.kpi.eval.domain.TaskEvalContext;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.OperationLog;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEval;
import com.polaris.kpi.eval.domain.task.event.BatchStageEndedV2;
import com.polaris.kpi.eval.domain.task.repo.EvalContextRepo;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.sdk.type.AuditEnum;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;

public class ResetToConfirmDmSvc {
    private TenantId companyId;
    private List<String> resetedUserIds = new ArrayList<>();
    private String opEmpId;
    private String resetNode;
    private String operationType;
    private String resetReason;
    private List<EmpEval> evals;

    @Getter
    private Set<String> clearScoreRsScenes = new HashSet<>();
    @Getter
    private List<OperationLog> logs = new ArrayList<>();

    public ResetToConfirmDmSvc(TenantId companyId, String opEmpId, List<EmpEval> evals) {
        this.companyId = companyId;
        this.opEmpId = opEmpId;
        this.resetNode = TalentStatus.CONFIRMING.getStatus();
        this.operationType = OperationTypeEnum.CHANGED_CONFIG.getValue();
        this.resetReason = "编辑任务配置触发重置";
        this.evals = evals;
    }

    public List<String> resetStage() {
        ArrayList<String> resetedUserIds = new ArrayList<>();
        if (Objects.isNull(this.evals)) {
            return resetedUserIds;
        }
        //清掉所有待办
        clearScoreRsScenes.add(MsgSceneEnum.TASK_WAIT_PUBLIC.getType());
        clearScoreRsScenes.add(MsgSceneEnum.TASK_RESULT_APPEAL.getType());  //考核申述
        clearScoreRsScenes.add(AuditEnum.FINISH_VALUE_AUDIT.getScene());
        clearScoreRsScenes.addAll(MsgSceneEnum.scoreScene);
        clearScoreRsScenes.addAll(AuditEnum.allScore());
        clearScoreRsScenes.addAll(Arrays.asList(MsgSceneEnum.TASK_RESULT_AUDIT.getType(), MsgSceneEnum.TASK_RESULT_AFFIRM.getType()));
        clearScoreRsScenes.add(AuditEnum.FINAL_RESULT_AUDIT.getScene());
        clearScoreRsScenes.add(AuditEnum.FINISH_VALUE_AUDIT.getScene());
        clearScoreRsScenes.addAll(MsgSceneEnum.onConfirming());
        clearScoreRsScenes.add(AuditEnum.EDIT_EXE_INDI.getScene());
        clearScoreRsScenes.add(MsgSceneEnum.INVITE_PEER_AUDIT.getType());
        clearScoreRsScenes.add(MsgSceneEnum.INVITE_SUB_AUDIT.getType());
        clearScoreRsScenes.add(MsgSceneEnum.SET_MUTUAL_AUDIT.getType());
        clearScoreRsScenes.add(MsgSceneEnum.REJECT_FINISH_VALUE.getType());
        clearScoreRsScenes.add(MsgSceneEnum.TASK_SUBMIT_AUTO_ITEM_PROGRESS.getType());
        for (EmpEval eval : evals) {
            EvalUser user = eval.getEval();
            resetedUserIds.add(user.getId());
            user.setHasAppeal(Boolean.FALSE.toString());
            JSONObject logDesc = new JSONObject();
            logDesc.put("resetReason", resetReason);
            logDesc.put("resetNode", resetNode);
            //重置操作日志
            OperationLog operationLog = new OperationLog(companyId.getId(), user.getId(),
                    OperationLogSceneEnum.RESET_TASK_TO_NODE.getScene(), opEmpId, logDesc.toJSONString());
            operationLog.setCreatedTime(new Date());
            operationLog.setOperationType(operationType);
            logs.add(operationLog);
            this.resetedUserIds.add(user.getId());
        }

//        userRepo.updateTaskUser(evals);
        //userRepo.resetAuditStatus(companyId, taskUserId, clearScoreRsScenes);
        return resetedUserIds;
    }

    public static void main(String[] args) {
        TalentStatus toStage = TalentStatus.CONFIRMING;
        TalentStatus current = TalentStatus.SCORING;
        System.out.println(toStage.beforeEq(TalentStatus.FINISH_VALUE_AUDIT) && TalentStatus.FINISH_VALUE_AUDIT.beforeEq(current));
    }

    public List<String> getResetedIds() {
        return resetedUserIds;
    }

    public void saveReset2ConfirmStage(EvalContextRepo userRepo) {
        userRepo.resetAuditFlowStatus(companyId, resetedUserIds,clearScoreRsScenes);
    }

    public void startConfirmStage(EvalContextRepo contextRepo) {
        List<String> userIds = evals.stream().map(eval -> eval.getEval().getId()).collect(Collectors.toList());
        List<TaskEvalContext> resetContxs = contextRepo.listStartEvalContext(companyId, userIds, 0);
        new BatchStageEndedV2(companyId, new EmpId(opEmpId), resetContxs, TalentStatus.CONFIRMING.preStage()).publish();
    }
}
