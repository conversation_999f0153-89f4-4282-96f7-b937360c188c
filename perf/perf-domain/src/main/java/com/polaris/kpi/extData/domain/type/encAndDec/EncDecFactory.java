package com.polaris.kpi.extData.domain.type.encAndDec;

import cn.hutool.core.util.StrUtil;

/**
 * @Author: xuxw
 * @Date: 2025/03/10 10:19
 * @Description:
 */
public class EncDecFactory {
    private static final String AES = "Aes";
    private static final String RSA = "Rsa";
    private static final String SHA256 = "Sha256";
    private static final String MD5 = "Md5";
    private static final String HMACSHA256 = "HmacSha256";
    public static EncDec init(String type){
        if (StrUtil.equals(AES, type, true)){
            return new Aes();
        }
        if (StrUtil.equals(RSA, type, true)){
            return new Rsa();
        }
        if (StrUtil.equals(SHA256, type, true)){
            return new Sha256();
        }
        if (StrUtil.equals(MD5, type, true)){
            return new Md5();
        }
        if (StrUtil.equals(HMACSHA256, type, true)){
            return new HmacSha256();
        }
        return null;
    }
}
