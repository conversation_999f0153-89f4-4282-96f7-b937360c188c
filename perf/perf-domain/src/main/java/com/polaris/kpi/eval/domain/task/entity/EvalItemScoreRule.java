package com.polaris.kpi.eval.domain.task.entity;

import cn.com.polaris.kpi.ObjItem;
import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.com.polaris.kpi.eval.*;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.perf.www.common.constant.BusinessConstant;
import com.polaris.kpi.eval.domain.task.entity.admineval.EvalRuleLogField;
import com.polaris.kpi.eval.domain.task.entity.empeval.BaseEvalFlow;
import com.polaris.kpi.eval.domain.task.entity.empeval.IItemScoreRule;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.*;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrAction;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrActionUpdate;
import com.polaris.kpi.eval.domain.task.repo.TaskUserRepo;
import com.polaris.kpi.eval.domain.task.type.InnerFields;
import com.polaris.kpi.eval.domain.task.type.NodeWeight;
import com.polaris.sdk.type.AuditEnum;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.Setter;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * {
 * "id":"",
 * "companyId":"公司id",
 * "taskId":"任务id",
 * "taskUserId":"",
 * "kpiItemId":"指标项id",
 * "selfScoreFlag":"是否自评",
 * "selfScoreViewRule":"被考核人查看评分规则JSON",
 * "selfScoreWeight":"自评权重",
 * "mutualScoreFlag":"是否互评",
 * "mutualScoreAttendRule":"互评参与规则",
 * "mutualScoreAnonymous":"互评人姓名是否匿名",
 * "mutualScoreVacancy":"互评人空缺时规则",
 * "mutualScoreViewRule":"互评人查看评分规则JSON",
 * "peerScoreWeight":"同级互评权重",
 * "subScoreWeight":"下级互评权重",
 * "superiorScoreFlag":"是否上级评",
 * "superiorScoreViewRule":"上级评人查看评分规则JSON",
 * "superiorScoreWeight":"上级评权重",
 * "superiorScoreVacancy":"上级评分人空缺时规则",
 * "appointScoreFlag":"是否指定评分",
 * "appointScoreWeight":"指定评分权重",
 * "createdUser":"创建用户",
 * "createdTime":"创建时间",
 * "updatedUser":"修改用户",
 * "updatedTime":"修改时间",
 * "isDeleted":"",
 * "kpiTypeId":"指标类id",
 * "mutualUserType":"设置互评人类型，all:为所有被考核人设置相同的互评人; user: 为每位被考核人分别设置互评人; exam：由被考核人自行指定",
 * "version":"版本号",
 * }
 ***/
@Getter
@Setter
@Component
public class EvalItemScoreRule extends BaseEvalFlow implements IGetNodeWeight, IItemScoreRule, Cloneable {

//    private static TaskUserRepo repo;

//    @Autowired
//    public void setRepo(TaskUserRepo repo) {
//        this.repo = repo;
//    }

    private String id;//
    @JSONField(serialize = false)
    private TenantId companyId;//公司id
    private String taskId;//任务id
    private String taskUserId;//
    private String kpiItemId;//指标项id
    private String selfScoreFlag;//是否自评
    private String selfScoreViewRule;//被考核人查看评分规则JSON
    private BigDecimal selfScoreWeight;//自评权重
    private String mutualScoreFlag;//是否互评
    private String mutualScoreAttendRule;//互评参与规则
    private String mutualScoreAnonymous;//互评人姓名是否匿名
    private String mutualScoreVacancy;//互评人空缺时规则
    private String mutualScoreViewRule;//互评人查看评分规则JSON
    private BigDecimal peerScoreWeight;//同级互评权重
    private BigDecimal subScoreWeight;//下级互评权重
    private String superiorScoreFlag;//是否上级评
    private String superiorScoreViewRule;//上级评人查看评分规则JSON
    private BigDecimal superiorScoreWeight;//上级评权重
    private String superiorScoreVacancy;//上级评分人空缺时规则
    private String appointScoreFlag;//是否指定评分
    private BigDecimal appointScoreWeight;//指定评分权重
    private String kpiTypeId;//指标类id
    private String mutualUserType;//设置互评人类型，all:为所有被考核人设置相同的互评人; user: 为每位被考核人分别设置互评人; exam：由被考核人自行指定
    private String mutualUserValue;//自定义模板由指定人、主管、角色、设置互评人
    //
    //private RaterNodeConf selfRater;              //自评人
    ////private MutualRaterConf mutualRater;        //互评人        //todo 管理任务上匹配评分顺序
    //private MutualNodeConf peerRater;              //360或简易 同级互评人
    //private MutualNodeConf subRater;               //360下级
    //private S3SuperRaterConf superRater;          //上级人        //todo 管理任务上匹配评分顺序
    //private S3RaterBaseConf appointRater;         //指定评分人

    public EvalItemScoreRule() {
    }

    //兼容v1任务转为考核表，考核任务
    private String peerScoreFlag;
    private String peerUserName;
    private String peerUserType;
    private String peerUserValue;
    private String subScoreFlag;
    private String subUserType;
    private String subUserValue;
    private String subUserName;
    //角色指定同级互评
    @JSONField(serialize = false, deserialize = true)
    private List<Rater> roleAppointerPeers;
    //主管指定同级互评
    @JSONField(serialize = false, deserialize = true)
    private List<Rater> managerAppointerPeers;
    //角色指定下级互评
    @JSONField(serialize = false, deserialize = true)
    private List<Rater> roleAppointerSubs;
    //主管指定下级互评
    @JSONField(serialize = false, deserialize = true)
    private List<Rater> managerAppointerSubs;

    @JSONField(serialize = false, deserialize = true)
    private List<EvalAudit> peerScoreList;//同级互评人
    @JSONField(serialize = false, deserialize = true)
    private List<EvalAudit> subScoreList;//下级互评人
    @JSONField(serialize = false, deserialize = true)
    private List<EvalAudit> appointScoreList;//指定评分人
    @JSONField(serialize = false, deserialize = true)
    private List<EvalAudit> superiorScoreList;//上级评分人
    private int extend;//是否继承而来,继承的不入数据库只用来计算 0=不是继承,1=继续维度,2=继承统一流程

    public EvalItemScoreRule(TenantId companyId, String taskUserId) {
        this.companyId = companyId;
        this.taskUserId = taskUserId;
    }

    //s3格式的配置成以指标格式使用
    public void s3AsRaterConf(RaterNodeConf selfRater, MutualNodeConf peerRater,
                              MutualNodeConf subRater, S3SuperRaterConf superRater) {
        asRaterConf(selfRater, peerRater, subRater, superRater, S3RaterBaseConf.closeConf());
    }

    public void customAsRaterConf() {
        /*if (selfRater == null) {
            return;
        }*/
        asRaterConf(selfRater, peerRater, subRater, superRater, appointRater);
    }

    //旧字段使用
    public void asRaterConf(RaterNodeConf selfRater, MutualNodeConf peerRater,
                            MutualNodeConf subRater, S3SuperRaterConf superRater,
                            S3RaterBaseConf appointRater) {
        this.selfRater = this.selfRater == null ? new RaterNodeConf() : this.selfRater;
        if (selfRater != null) {
            this.selfRater = selfRater;
            this.selfScoreFlag = String.valueOf(selfRater.isOpen());
            this.selfScoreWeight = selfRater.getNodeWeight();
        }

        this.peerRater = this.peerRater == null ? new MutualNodeConf() : this.peerRater;
        if (peerRater != null) {
            this.mutualScoreFlag = peerRater.isOpen() ? String.valueOf(peerRater.isOpen()) : mutualScoreFlag;
            this.peerRater = peerRater;
            this.peerScoreWeight = peerRater.getNodeWeight();
        }

        this.subRater = this.subRater == null ? new MutualNodeConf() : this.subRater;
        if (subRater != null) {
            this.mutualScoreFlag = subRater.isOpen() ? String.valueOf(subRater.isOpen()) : mutualScoreFlag;
            this.subRater = subRater;
            this.subScoreWeight = subRater.getNodeWeight();
        }

        this.superRater = this.superRater == null ? new S3SuperRaterConf() : this.superRater;
        if (superRater != null) {
            this.superRater = superRater;
            this.superiorScoreWeight = superRater.getNodeWeight();
            this.superiorScoreFlag = String.valueOf(superRater.isOpen());
        }

        this.appointRater = this.appointRater == null ? new S3RaterBaseConf() : this.appointRater;
        if (appointRater != null) {
            this.appointRater = appointRater;
            this.appointScoreWeight = appointRater.getNodeWeight();
            this.appointScoreFlag = String.valueOf(appointRater.isOpen());
        }
    }

    public boolean selfNodeIsOpened() {
        return Boolean.TRUE.toString().equals(selfScoreFlag) || needSelfScore();
    }

    public boolean openNodeByScene(SubScoreNodeEnum node) {
        switch (node) {
            case SELF_SCORE:
                return isOpenSelfRater();
            case PEER_SCORE:
                return isOpenPeerRater();
            case SUB_SCORE:
                return isOpenSubRater();
            case SUPERIOR_SCORE:
                return isOpenSuperiorRater();
            case APPOINT_SCORE:
                return isOpenAppointRater();
            default:
                return false;
        }
    }
    public BigDecimal nodePecWeight(SubScoreNodeEnum node) {
        switch (node) {
            case SELF_SCORE:
                return scoreWeight(selfRater, selfScoreWeight);
            case PEER_SCORE:
                return scoreWeight(peerRater, peerScoreWeight);
            case SUB_SCORE:
                return scoreWeight(subRater, subScoreWeight);
            case SUPERIOR_SCORE:
                return scoreWeight(superRater, superiorScoreWeight);
            case APPOINT_SCORE:
                return scoreWeight(appointRater, appointScoreWeight);
            case ITEM_SCORE:
                return Pecent.ONE;
            default:
                return BigDecimal.ZERO;
        }
    }



    public BigDecimal nodeOrderWeight(SubScoreNodeEnum node,Integer order) {
        switch (node) {
            case SELF_SCORE:
            case PEER_SCORE:
            case SUB_SCORE:
            case ITEM_SCORE:
                return Pecent.ONE;
            case SUPERIOR_SCORE:
                return scoreOrderWeight(superRater, superiorScoreWeight, order);
            case APPOINT_SCORE:
                return scoreOrderWeight(appointRater, appointScoreWeight, order);
            default:
                return BigDecimal.ZERO;
        }
    }

    private BigDecimal scoreOrderWeight(S3RaterBaseConf conf, BigDecimal oldWgt,Integer order) {
        boolean isOpen = conf != null && conf.isOpen();
        BigDecimal nodeOrderWeight = Pecent.ONE;
        if(isOpen){
            order = order == null ? 1 : order;
            for (BaseAuditNode auditNode : conf.auditNodes()) {
                Integer nodeOrder = auditNode.getApprovalOrder() ;
                nodeOrder = nodeOrder == null ? 1 : nodeOrder;
                if (order.compareTo(nodeOrder) == 0) {
                    nodeOrderWeight = auditNode.getWeight();
                    break;
                }
            }
        }

        //BigDecimal bigDecimal = conf != null && conf.isOpen() ? conf.getNodeWeight() : oldWgt;
        BigDecimal bigDecimal = conf != null && conf.isOpen() ? nodeOrderWeight : oldWgt;
        return defaultIfNull(bigDecimal);
    }
    private BigDecimal scoreWeight(RaterNodeConf conf, BigDecimal oldWgt) {
        BigDecimal bigDecimal = conf != null && conf.isOpen() ? conf.getNodeWeight() : oldWgt;
        return defaultIfNull(bigDecimal);
    }

    @NotNull
    private BigDecimal defaultIfNull(BigDecimal weight) {
        return ObjectUtil.defaultIfNull(weight, BigDecimal.ZERO).divide(Pecent.ONE_HUNDRED);
    }

    //从模板来的audit,应用模板及模板任务转考核表时使用
    public List<EvalAudit> allTempAudit() {
        List<EvalAudit> raterAudits = new ArrayList<>();
        raterAudits.addAll(peerScoreList);
        raterAudits.addAll(subScoreList);
        raterAudits.addAll(appointScoreList);
        raterAudits.addAll(superiorScoreList);
        return raterAudits;
    }

    public boolean needSetMutual() {
        if (Objects.isNull(this.mutualUserType)) {
            return false;
        }
        return this.mutualUserType.equals("manager") || this.mutualUserType.equals("role") || this.mutualUserType.equals("exam");
    }

    public EvaluationStaff mutualConf(String empId, String createUser) {
        EvaluationStaff staff = new EvaluationStaff();
        if (StrUtil.isEmpty(this.mutualUserType)) {
            return staff;
        }
        if (this.mutualUserType.equals("manager")) {
            LevelManagerItem managerItem = new LevelManagerItem();
            managerItem.setEmpId(empId);
            managerItem.setLevel(Integer.parseInt(this.getMutualUserValue()));
            managerItem.setAdminId(createUser);
            staff.setLevelManager(managerItem);
            return staff;
        }
        if (this.mutualUserType.equals("role")) {
            List<String> roleIds = Arrays.asList(this.getMutualUserValue().split(","));
            List<StaffConfItem> staffConfItems = new ArrayList<>();
            List<ObjItem> objItems = roleIds.stream().map(roId -> new ObjItem(roId, "")).collect(Collectors.toList());
            StaffConfItem staffConfItem = new StaffConfItem("role", objItems);
            staffConfItems.add(staffConfItem);
            staff.setRoleItems(staffConfItems);
            return staff;
        }
        if (this.mutualUserType.equals("exam")) {
            List<StaffConfItem> staffConfItems = new ArrayList<>();
            List<ObjItem> objItems = new ArrayList<>();
            ObjItem objItem = new ObjItem();
            objItem.setObjId(empId);
            objItems.add(objItem);
            StaffConfItem staffConfItem = new StaffConfItem("user", objItems);
            staffConfItems.add(staffConfItem);
            staff.setFixEmps(staffConfItems);
        }
        return staff;
    }

    public List<EvalRuleLogField> compare(EvalItemScoreRule after) {
        InnerFields<EvalRuleLogField> mdType = new InnerFields<>();
        mdType.add(EvalRuleLogField.createIf("指标评分流程", nodeWeightName(), after.nodeWeightName()));
        mdType.add(EvalRuleLogField.createIf("上级评分人", superNodeName(), after.superNodeName()));
        mdType.add(EvalRuleLogField.createIf("互评人设置", mutualRaterName(), after.mutualRaterName()));
        mdType.add(EvalRuleLogField.createIf("指定评分人", appointRaterName(), after.appointRaterName()));
        mdType.add(EvalRuleLogField.createIf("评分环节权重", nodeWeightName(), after.nodeWeightName()));
        return mdType.getFields();
    }

    public Collection<String> allScoreType() {
        List<String> rs = new ArrayList<>();
        if (needSelfScore()) {
            rs.add(SubScoreNodeEnum.SELF_SCORE.getScene());
        }
        if (needSupScore()) {
            rs.add(SubScoreNodeEnum.SUPERIOR_SCORE.getScene());
        }
        if (needSubScore()) {
            rs.add(SubScoreNodeEnum.SUB_SCORE.getScene());
        }
        if (needPeerScore()) {
            rs.add(SubScoreNodeEnum.PEER_SCORE.getScene());
        }
        if (needAppointScore()) {
            rs.add(SubScoreNodeEnum.APPOINT_SCORE.getScene());
        }
        if (needAppointScore()) {
            rs.add(SubScoreNodeEnum.ITEM_SCORE.getScene());
        }
        return rs;
    }

    //是否需要指定互评人
    public boolean needSetPeerRater(String evalEmpId, String opEmpId, String queryType) {
        return this.needSetRater(peerRater, evalEmpId, opEmpId, queryType);
    }

    //是否需要指定互评人
    public boolean needSetSubRater(String evalEmpId, String opEmpId, String queryType) {
        return this.needSetRater(subRater, evalEmpId, opEmpId, queryType);
    }

    //是否需要指定互评人
    private boolean needSetRater(MutualNodeConf mutualNodeConf, String evalEmpId, String opEmpId, String queryType) {
        if (Objects.isNull(mutualNodeConf) || !mutualNodeConf.isOpen()) {
            return false;
        }
        WaitAppoint appointer = mutualNodeConf.getAppointer();
        if (Objects.isNull(appointer)) {
            return false;
        }
        if (appointer.isOpenInviteMutualAudit() && StrUtil.isBlank(queryType)) {
            if (appointer != null && appointer.isAppointer(opEmpId, evalEmpId, queryType) && CollUtil.isEmpty(mutualNodeConf.getRaters())) {//新的配置
                return true;
            }
            return false;
        }
        if (appointer != null && appointer.isAppointer(opEmpId, evalEmpId, queryType)) {//新的配置
            return true;
        }
        if ("exam".equals(mutualUserType) && StrUtil.equals(evalEmpId, opEmpId)) {//考核人
            return true;
        }
        if (StrUtil.isEmpty(mutualUserValue)) {
            return false;
        }
        return Arrays.asList(mutualUserValue.split(",")).contains(opEmpId);
    }

    //是否需要指定互评人
    public Set<String> appointEmpIds(String evalEmpId) {
        HashSet<String> appointEmpIds = new HashSet<>();
        Set<String> appointPeerRaterEmpIds = this.appointEmpIds(this.peerRater, evalEmpId);
        Set<String> appointSubRaterEmpIds = this.appointEmpIds(this.subRater, evalEmpId);
        appointEmpIds.addAll(appointPeerRaterEmpIds);
        appointEmpIds.addAll(appointSubRaterEmpIds);
        return appointEmpIds;
    }

    //是否需要指定互评人
    private Set<String> appointEmpIds(MutualNodeConf mutualNodeConf, String evalEmpId) {
        HashSet<String> appointEmpIds = new HashSet<>();
        if (mutualNodeConf != null && mutualNodeConf.isOpen() && mutualNodeConf.getAppointer() != null) {//新的配置
            return mutualNodeConf.getAppointer().appointEmpIds(evalEmpId);
        }
        if ("exam".equals(mutualUserType)) {//考核人
            appointEmpIds.add(evalEmpId);
            return appointEmpIds;
        }
        if (StrUtil.isEmpty(mutualUserValue)) {
            appointEmpIds.addAll(StrUtil.splitTrim(mutualUserValue, ","));
            return appointEmpIds;
        }
        return appointEmpIds;
    }


    //互评配置的由谁指定
    @JSONField(serialize = false, deserialize = false)
    public Boolean peerRaterWaitAppoint() {
        return peerUserType != null && peerUserType.contains("Invite");
    }

    @JSONField(serialize = false, deserialize = false)
    public Boolean subRaterWaitAppoint() {
        return subUserType != null && subUserType.contains("Invite");
    }

    public boolean peerScoreFlag() {
        if (peerScoreFlag != null) {
            return Boolean.TRUE.toString().equals(peerScoreFlag);
        }
        return Boolean.TRUE.toString().equals(mutualScoreFlag) && CollUtil.isNotEmpty(peerScoreList);
    }

    public boolean subScoreFlag() {
        if (subScoreFlag != null) {
            return Boolean.TRUE.toString().equals(subScoreFlag);
        }
        return Boolean.TRUE.toString().equals(mutualScoreFlag) && CollUtil.isNotEmpty(subScoreList);
    }

    public String matchPeerUserType() {
        if (StrUtil.isBlank(peerUserType)) {
            return mutualUserType;
        }
        return peerUserType;
    }

    public String matchSubUserType() {
        if (StrUtil.isBlank(subUserType)) {
            return mutualUserType;
        }
        return subUserType;
    }

    public boolean notSetMutualScorer() {
        if (Objects.isNull(peerRater) || Objects.isNull(subRater)) {
            return false;
        }
        if (this.peerRater.isOpen() && this.subRater.isOpen()) {
            return CollUtil.isEmpty(this.peerRater.getRaters()) || CollUtil.isEmpty(this.subRater.getRaters());
        }
        if (this.peerRater.isOpen()) {
            return CollUtil.isEmpty(this.peerRater.getRaters());
        }
        if (this.subRater.isOpen()) {
            return CollUtil.isEmpty(this.subRater.getRaters());
        }
        return false;
    }

    public void buildTypeRule(String scene, BigDecimal weight, String vacancyApproverType) {
        if (SubScoreNodeEnum.isSelfScore(scene)) {
            this.selfScoreFlag = "true";
            this.selfScoreWeight = weight;
        }
        if (SubScoreNodeEnum.isSuperioNode(scene)) {
            this.superiorScoreFlag = "true";
            this.superiorScoreWeight = weight;
            this.superiorScoreVacancy = vacancyApproverType;
        }
        if (SubScoreNodeEnum.isPeerNode(scene)) {
            this.mutualScoreFlag = "true";
            this.peerScoreWeight = weight;
        }
        if (SubScoreNodeEnum.isSubNode(scene)) {
            this.mutualScoreFlag = "true";
            this.subScoreWeight = weight;
        }
    }

    private static class MutualRater {
        private MutualNodeConf peerRater;
        private MutualNodeConf subRater;

        public MutualRater(MutualNodeConf peerRater, MutualNodeConf subRater) {
            this.peerRater = peerRater;
            this.subRater = subRater;
        }

        public BigDecimal mergeWeight() {
            if (this.peerRater != null && this.subRater != null) {
                if (this.peerRater.isOpen() && this.subRater.isOpen()) {
                    BigDecimal weight = new BigDecimal(this.subRater.getNodeWeight().doubleValue());//解引用
                    return weight.add(peerRater.getNodeWeight());
                }
            }
            if (this.peerRater != null && this.peerRater.isOpen()) {
                return this.peerRater.getNodeWeight();
            }
            if (this.subRater != null && this.subRater.isOpen()) {
                return this.subRater.getNodeWeight();
            }

            return null;
        }

        public String mergeRaterName() {
            if (subRater != null && peerRater != null) {
                return StrUtil.format("{},{}", subRater.nodeName(), peerRater.nodeName());
            }
            if (subRater != null) {
                return subRater.nodeName();
            }
            if (peerRater != null) {
                return peerRater.nodeName();
            }
            return "";
        }
    }

    private String nodeWeightName() {
        List<String> sb = new ArrayList<>();
        if (selfRater != null && selfRater.isOpen() && selfRater.getNodeWeight() != null) {
            sb.add(String.format("自评（%s％）", selfRater.getNodeWeight()));
        }
        MutualRater mutualRater = new MutualRater(this.peerRater, this.subRater);
        BigDecimal weight = mutualRater.mergeWeight();
        if (weight != null) {
            sb.add(StrUtil.format("互评({}％)", weight.setScale(2, BigDecimal.ROUND_HALF_UP)));
        }
        if (superRater != null && superRater.isOpen() && superRater.getNodeWeight() != null) {
            sb.add(String.format("上级评（%s％）", superRater.getNodeWeight()));
        }
        if (appointRater != null && appointRater.isOpen() && appointRater.getNodeWeight() != null) {
            sb.add(String.format("指定评（%s％）", appointRater.getNodeWeight()));
        }
        return CollUtil.join(sb, "+");
    }

    private String superNodeName() {
        if (superRater == null) {
            return "";
        }
        return superRater.superNodeName();
    }

    private String mutualRaterName() {
        MutualRater mutualRater = new MutualRater(this.peerRater, this.subRater);
        return mutualRater.mergeRaterName();
    }

    private String appointRaterName() {
        if (appointRater == null) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (Rater rater : appointRater.allRater()) {
            if (rater.getWeight() == null) {
                sb.append(StrUtil.format("{} ", rater.getEmpName()));
                continue;
            }
            sb.append(String.format("%s（%s％）", rater.getEmpName(), rater.getWeight()));
        }
        return sb.toString();
    }

    public boolean hasScoreRule() {
        return needSelfScore() || needPeerScore() || needSubScore() || needSupScore() || needAppointScore();
    }

    public boolean needSelfScore() {
        return Boolean.valueOf(selfScoreFlag) || (selfRater != null && selfRater.isOpen());
    }

    public boolean needPeerScore() {
        return Boolean.valueOf(mutualScoreFlag) || (peerRater != null && peerRater.isOpen());
    }

    public boolean needSubScore() {
        return Boolean.valueOf(mutualScoreFlag) || (subRater != null && subRater.isOpen());
    }

    public boolean needSupScore() {
        return Boolean.valueOf(superiorScoreFlag) || (superRater != null && superRater.isOpen());
    }

    public boolean needAppointScore() {
        return Boolean.valueOf(appointScoreFlag) || (appointRater != null && appointRater.isOpen());
    }

    public void initBaseInfo(TenantId tenantId, EmpId opEmpId, String empEvalId, String kpiTypeId, String kpiItemId,
                             String taskId) {
        this.companyId = tenantId;
        this.taskId = taskId;
        this.updatedUser = opEmpId.getId();
        this.createdUser = opEmpId.getId();
        this.createdTime = new Date();
        this.updatedTime = new Date();
        this.taskUserId = empEvalId;
        this.kpiTypeId = kpiTypeId;
        this.kpiItemId = kpiItemId;
    }

    public boolean belongItem(SubScoreNodeEnum node, String belongEmpId) {
        if (node == SubScoreNodeEnum.SELF_SCORE && selfRater.isOpen()) {
            return true;
        }
        if (node == SubScoreNodeEnum.PEER_SCORE && peerRater.isOpen()) {
            return peerRater.belongItem(belongEmpId);
        }
        if (node == SubScoreNodeEnum.SUB_SCORE && subRater.isOpen()) {
            return subRater.belongItem(belongEmpId);
        }
        return false;
    }

    @JSONField(serialize = false)
    public boolean isTypeRule() {
        return kpiItemId == null || "0".equals(kpiItemId);
    }


    public List<NodeWeight> nodesWeight() {
        NodeWeight sup = new NodeWeight(AuditEnum.SUPERIOR_SCORE.getScene(), this.superRater == null ? this.getSuperiorScoreWeight() : this.superRater.getNodeWeight());
        NodeWeight self = new NodeWeight(AuditEnum.SELF_SCORE.getScene(), this.selfRater == null ? this.getSelfScoreWeight() : this.selfRater.getNodeWeight());
        NodeWeight peer = new NodeWeight(AuditEnum.PEER_SCORE.getScene(), this.peerRater == null ? this.getPeerScoreWeight() : this.peerRater.getNodeWeight());
        NodeWeight subPeer = new NodeWeight(AuditEnum.SUB_SCORE.getScene(), this.subRater == null ? this.getSubScoreWeight() : this.subRater.getNodeWeight());
        NodeWeight appoint = new NodeWeight(AuditEnum.APPOINT_SCORE.getScene(), this.appointRater == null ? this.getAppointScoreWeight() : this.appointRater.getNodeWeight());
        return Arrays.asList(sup, self, peer, subPeer, appoint);
    }

    public void matchItemRuleAudit(List<EvalAudit> itemAudits) {
        if (CollUtil.isEmpty(itemAudits)) {
            return;
        }
        Map<String, List<EvalAudit>> itemRuleAuditGroup = itemAudits.stream().collect(Collectors.groupingBy(EvalAudit::getScene));
        this.peerScoreList = itemRuleAuditGroup.get(EvaluateAuditSceneEnum.PEER_SCORE.getScene());
        this.subScoreList = itemRuleAuditGroup.get(EvaluateAuditSceneEnum.SUB_SCORE.getScene());
        this.appointScoreList = itemRuleAuditGroup.get(EvaluateAuditSceneEnum.APPOINT_SCORE.getScene());
        this.superiorScoreList = itemRuleAuditGroup.get(EvaluateAuditSceneEnum.SUPERIOR_SCORE.getScene());
    }

    public void mutualScoreV1() {
        if ("true".equals(this.mutualScoreFlag)) {
            if ((null != peerScoreWeight && peerScoreWeight.compareTo(BigDecimal.ZERO) == 1) || CollUtil.isNotEmpty(peerScoreList)) {
                this.peerScoreFlag = "true";
                this.peerUserType = BusinessConstant.MUTUAL_USER_TYPES.contains(mutualUserType) ? mutualUserType + "Invite" : mutualUserType;
                this.peerUserValue = mutualUserValue;
            }
            if ((null != subScoreWeight && subScoreWeight.compareTo(BigDecimal.ZERO) == 1) || CollUtil.isNotEmpty(subScoreList)) {
                this.subScoreFlag = "true";
                this.subUserType = BusinessConstant.MUTUAL_USER_TYPES.contains(mutualUserType) ? mutualUserType + "Invite" : mutualUserType;
                this.subUserValue = mutualUserValue;
            }
        }
    }

    public List<Rater> builderSuperRater(boolean isRaterBack) {
        List<Rater> list = new ArrayList<>();
        if (this.superRater.isOpen()) {
            if (isRaterBack) {
                if (this.superRater.allOrNullRater().isEmpty()) {
                    list.add(new Rater("1", " "));
                    return list;
                }
                return this.superRater.allOrNullRater();
            }
        }
        return this.superRater.allRater();
    }

    public List<Rater> builderPeerRater(boolean isRaterBack) {
        List<Rater> list = new ArrayList<>();
        if (this.peerRater.isOpen()) {
            if (this.peerRater.getAppointer() != null) {
                if ("emp".equals(peerRater.getAppointer().getType())) {
                    list.add(new Rater(null, 5));
                    return list;
                } else {
                    return peerRater.getAppointer().getRaters();
                }
            } else if (this.peerRater.allRater().isEmpty() && isRaterBack) {
                list.add(new Rater("1", " "));
                return list;
            }
        }
        return this.peerRater.getRaters();
    }

    public List<Rater> builderSubRater(boolean isRaterBack) {
        List<Rater> list = new ArrayList<>();
        if (this.subRater.isOpen()) {
            if (this.subRater.getAppointer() != null) {
                if ("emp".equals(subRater.getAppointer().getType())) {
                    list.add(new Rater(null, 5));
                    return list;
                } else {
                    return subRater.getAppointer().getRaters();
                }
            } else if (this.subRater.allRater().isEmpty() && isRaterBack) {
                list.add(new Rater("1", " "));
                return list;
            }
        }
        return this.subRater.getRaters();
    }


    public List<Rater> builderAppointRater(boolean isRaterBack) {
        List<Rater> list = new ArrayList<>();
        if (this.appointRater.isOpen()) {
            if (isRaterBack) {
                if (this.appointRater.allOrNullRater().isEmpty()) {
                    list.add(new Rater("1", " "));
                    return list;
                }
                return this.appointRater.allOrNullRater();
            }
        }
        return this.appointRater.allRater();
    }

    public void clearAppointRater() {
        if (Objects.nonNull(this.peerRater) && this.peerRater.isOpen() && Objects.nonNull(this.peerRater.getAppointer())) {
            this.peerRater.setRaters(new ArrayList<>());
        }
        if (Objects.nonNull(this.subRater) && this.subRater.isOpen() && Objects.nonNull(this.subRater.getAppointer())) {
            this.subRater.setRaters(new ArrayList<>());
        }
    }

    @Override
    public EvalItemScoreRule clone() {
        return JSONUtil.toBean(JSONUtil.toJsonStr(this), EvalItemScoreRule.class);
    }

    public List<String> v1SubInviterEmpIds() {
        return v1InviterEmpIds(this.getSubUserType(), this.getSubUserValue());
    }

    public List<String> v1PeerInviterEmpIds() {
        return v1InviterEmpIds(this.getPeerUserType(), this.getPeerUserType());
    }

    private List<String> v1InviterEmpIds(String type, String value) {
        if ("examInvite".equals(type)) {
            return new ArrayList<>();
        }
        if (Arrays.asList("empInvite", "roleInvite", "managerInvite").contains(type)) {
            return StrUtil.splitTrim(value, ",");
        }
        return new ArrayList<>();
    }

    public void initV1PeerUser(List<Rater> emps) {
        if ("examInvite".equals(this.getPeerUserType())) {
            this.setPeerUserName("被考核人");
            return;
        }
        List<String> empIds = this.v1PeerInviterEmpIds();
        List<Rater> raters = emps.stream().filter(rater -> empIds.contains(rater.getEmpId())).collect(Collectors.toList());
        if ("empInvite".equals(this.getPeerUserType())) {
            this.setPeerUserName(raters.get(0).getEmpName());
            return;
        }
        if ("roleInvite".equals(this.getPeerUserType())) {
            this.setRoleAppointerPeers(raters);
            return;
        }
        if ("managerInvite".equals(this.getPeerUserType())) {
            this.setManagerAppointerPeers(raters);
        }
    }


    public void initV1SubUser(List<Rater> emps) {
        List<String> empIds = this.v1SubInviterEmpIds();
        List<Rater> raters = emps.stream().filter(rater -> empIds.contains(rater.getEmpId())).collect(Collectors.toList());
        if ("examInvite".equals(this.getPeerUserType())) {
            this.setSubUserName("被考核人");
            return;
        }
        if ("empInvite".equals(this.getPeerUserType())) {
            this.setSubUserName(raters.get(0).getEmpName());
            return;
        }
        if ("roleInvite".equals(this.getPeerUserType())) {
            this.setRoleAppointerSubs(raters);
            return;
        }
        if ("managerInvite".equals(this.getPeerUserType())) {
            this.setManagerAppointerSubs(raters);
        }
    }

    public void accRater(EvalItemScoreRule edit) {
        if (edit == null) {
            return;
        }
        this.setSelfRater(edit.getSelfRater());
        this.setPeerRater(edit.getPeerRater());
        this.setSubRater(edit.getSubRater());
        this.setSuperRater(edit.getSuperRater());
        this.setAppointRater(edit.getAppointRater());
    }

}
