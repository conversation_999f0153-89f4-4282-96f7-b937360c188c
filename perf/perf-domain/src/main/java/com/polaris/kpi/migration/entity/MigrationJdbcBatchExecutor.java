package com.polaris.kpi.migration.entity;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.nio.charset.StandardCharsets;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Date;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * JDBC批量执行工具类
 * 专门用于迁移业务，不影响正常业务的数据源
 */
@Slf4j
@Component
public class MigrationJdbcBatchExecutor {
    @Autowired
    @Qualifier("dingMigrationDataSourceProperties")  // 注入 dingMigrationDataSourceProperties 创建的数据源
    private DataSource migrationDataSource;

    public int executeBatchInsert(List<String> sqlStatements) {
        if (sqlStatements == null || sqlStatements.isEmpty()) {
            log.warn("SQL语句列表为空，跳过执行");
            return 0;
        }

        Connection connection = null;
        AtomicInteger successCount = new AtomicInteger(0);
        int totalCount = sqlStatements.size();
        try {
            // 使用 MigrationDataSourceConfig 创建的数据源获取连接
            connection = migrationDataSource.getConnection();
            connection.setAutoCommit(true);

            log.info("=== 开始批量执行 ===");
            log.info("总SQL数量: {}, 批处理大小: {}", totalCount, totalCount);
            log.info("使用数据源: {}", connection.getMetaData().getDatabaseProductName());

            int batchSuccessCount = executeBatchInsert(connection, sqlStatements);
            successCount.addAndGet(batchSuccessCount);
            log.info("=== 批量执行完成 ===");
            log.info("总数量: {},成功: {}", totalCount, successCount.get());

        } catch (SQLException e) {
            log.error("批量执行过程中发生异常", e);
            rollbackTransaction(connection);
            throw new RuntimeException("批量执行失败", e);
        }finally {
            closeResources(null, connection);
        }
        return successCount.get();
    }


    /**
     * 分批执行策略：避免单次SQL过大
     */
    private int executeBatchInsert(Connection connection, List<String> sqlStatements) throws SQLException {
        Statement statement = null;
        int successCount = 0;
        try {
            statement = connection.createStatement();
            int endIndex = sqlStatements.size();

            // 检查当前批次的总大小
            int currentBatchSize = calculateBatchSize(sqlStatements, 0, endIndex);
            int maxAllowedSize = 3 * 1024 * 1024; // 3MB安全边界

            if (currentBatchSize > maxAllowedSize) {
                // 当前批次过大，需要进一步分割
                log.warn("批次过大 ({} bytes > {} bytes), 自动分批执行", currentBatchSize, maxAllowedSize);
                // 尝试单条执行，找出具体哪条SQL有问题
                int subSuccess = executeStatementsIndividually(statement, sqlStatements, 0, endIndex);
                successCount += subSuccess;
            } else {
                // 正常批次执行
                int batchSuccess = executeSingleBatch(statement, sqlStatements, 0, endIndex);
                successCount += batchSuccess;
            }

        } catch (Exception e){
            log.error("批次执行异常 ",e);
        }finally {
            if (statement != null) {
                statement.close();
            }
        }
        return successCount;
    }

    /**
     * 计算批次大小
     */
    private int calculateBatchSize(List<String> sqlStatements, int startIndex, int endIndex) {
        int totalSize = 0;
        for (int i = startIndex; i < endIndex; i++) {
            if (i < sqlStatements.size()) {
                totalSize += getSqlSize(sqlStatements.get(i));
            }
        }
        return totalSize;
    }


    /**
     * 执行单个批次
     */
    private int executeSingleBatch(Statement statement,List<String> sqlStatements, int startIndex, int endIndex){
        try {
            statement.clearBatch();
            // 添加SQL到批次
            for (int j = startIndex; j < endIndex; j++) {
                String sql = sqlStatements.get(j);
                // 移除末尾分号，这是关键！
                if (sql.endsWith(";")) {
                    sql = sql.substring(0, sql.length() - 1).trim();
                }
                sql = sql.replaceAll("INSERT", "REPLACE");
                statement.addBatch(sql);
            }

            int[] results = statement.executeBatch();
            statement.clearBatch();
            log.debug("批次执行成功: {}-{}, 批次大小: {}", startIndex, endIndex - 1, results.length);
            // 统计结果
            int successCount = 0;
            for (int result : results) {
                if (result >= 0) {
                    successCount += result;
                }
            }

            log.debug("批次执行成功: {}-{}, 结果: {}", startIndex, endIndex - 1, results.length);
            return successCount;

        }catch (Exception e){
            log.error("批次执行异常 ",e);
            log.debug("批次执行异常，改用单个执行 {}-{}, 批次大小: {}", startIndex, endIndex - 1, 0);
            return executeStatementsIndividually(statement, sqlStatements, 0, endIndex);
        }
    }


    /**
     * 获取SQL大小
     */
    private int getSqlSize(String sql) {
        if (sql == null) return 0;
        return sql.getBytes(StandardCharsets.UTF_8).length;
    }

    /**
     * 单条执行SQL语句，找出问题SQL
     */
    private int executeStatementsIndividually(Statement statement, List<String> sqlStatements, int startIndex, int endIndex) {
        int successCount = 0;


        for (int i = startIndex; i < endIndex; i++) {
            String sql = sqlStatements.get(i);
            sql = sql.replaceAll("INSERT", "REPLACE");
            try {
                int result = statement.executeUpdate(sql);
                if (result >= 0) {
                    successCount += result;
                }
                log.debug("单条执行成功: 索引={}, 结果={}", i, result);

            } catch (SQLException e) {
                log.error("单条执行失败: 索引={}, SQL={}", i,
                        sql.substring(0, Math.min(300, sql.length())), e);

                // 记录失败的SQL到日志文件，便于后续分析
                logFailedSql(i, sql, e);
            }
        }

        return successCount;
    }

    /**
     * 执行单个批次
     */
    private int executeBatch(Statement statement, List<String> sqlStatements, int startIndex, int endIndex) throws SQLException {
        int batchSuccessCount = 0;
        statement.clearBatch();
        try {
            for (int i = startIndex; i < endIndex; i++) {
                String sql = sqlStatements.get(i);
                sql = sql.replaceAll("INSERT", "REPLACE");
                statement.addBatch(sql);
            }

            int[] results = statement.executeBatch();
            statement.clearBatch();

            // 统计结果
            for (int result : results) {
                if (result >= 0) {
                    batchSuccessCount += result;
                }
            }

        } catch (SQLException e) {
            log.error("批次执行失败，开始索引: {}, 结束索引: {}", startIndex, endIndex);

            // 尝试单条执行，找出具体哪条SQL有问题
            batchSuccessCount = executeStatementsIndividually(statement, sqlStatements, startIndex, endIndex);
        }

        return batchSuccessCount;
    }

    /**
     * 记录失败的SQL到日志
     */
    private void logFailedSql(int index, String sql, SQLException e) {
        log.error("=== 失败的SQL语句 ===");
        log.error("索引: {}", index);
        log.error("SQL: {}", sql);
        log.error("错误: {}", e.getMessage());
        log.error("=== 结束 ===");
    }


    /**
     * 获取表SELECT语句信息
     */
    public List<TableSelectInfo> getTableSelectStatements(String companyId) {
        List<TableSelectInfo> tableInfos = new ArrayList<>();

        String sql = "CALL generateSelectStatements(?)";

        try (Connection conn = migrationDataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sql)) {

            stmt.setString(1, companyId);

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    TableSelectInfo info = new TableSelectInfo();
                    info.setTableName(rs.getString("table_name"));
                    info.setSelectStatement(rs.getString("select_statement"));
                    info.setFilterType(rs.getString("filter_type"));
                    tableInfos.add(info);
                }
            }

        } catch (SQLException e) {
            log.error("获取表SELECT语句失败: companyId={}", companyId, e);
        }

        return tableInfos;
    }

    /**
     * 获取表数据数量
     */
    public int getTableDataCount(String selectSql) {
        try {
            // 将SELECT * 改为 SELECT COUNT(*)
            String countSql = selectSql.replaceFirst("SELECT \\*", "SELECT COUNT(*)");

            try (Connection conn = migrationDataSource.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(countSql);
                 ResultSet rs = stmt.executeQuery()) {

                if (rs.next()) {
                    return rs.getInt(1);
                }
            }

        } catch (Exception e) {
            log.error("获取表数据数量失败: {}", selectSql, e);
        }

        return 0;
    }

    /**
     * 执行SELECT查询
     */
    public List<Map<String, Object>> executeSelectQuery(String selectSql) {
        List<Map<String, Object>> dataList = new ArrayList<>();

        try (Connection conn = migrationDataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(selectSql);
             ResultSet rs = stmt.executeQuery()) {

            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            while (rs.next()) {
                Map<String, Object> row = new LinkedHashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    Object value = rs.getObject(i);
                    row.put(columnName, value);
                }
                dataList.add(row);
            }

        } catch (SQLException e) {
            log.error("执行SELECT查询失败: {}", selectSql, e);
        }

        return dataList;
    }
    /**
     * 分批查询数据
     */
    public List<Map<String, Object>> queryDataInBatches(String tableName,String selectSql, int offset, int limit) {
        try {
            // 移除原始SQL末尾的分号
            String cleanSql = selectSql.trim();
            if (cleanSql.endsWith(";")) {
                cleanSql = cleanSql.substring(0, cleanSql.length() - 1);
            }

            if(StrUtil.equals("new_emp",tableName)){
                cleanSql = cleanSql + " and `emp_id`  is not null ";
            }
            if(StrUtil.equals("perf_evaluation_level",tableName)){
                cleanSql = cleanSql + " and ((`step_id` !='null') or (step_id is null)) ";
            }
            // 添加LIMIT和OFFSET
            String paginatedSql = cleanSql + " LIMIT " + limit + " OFFSET " + offset;

            log.debug("分页SQL: {}", paginatedSql);

            return executeSelectQuery(paginatedSql);

        } catch (Exception e) {
            log.error("分批查询数据失败: offset={}, limit={}, originalSql={}", offset, limit, selectSql, e);
            return new ArrayList<>();
        }
    }


    /**
     * 生成单条INSERT语句
     */
    public String generateSingleInsertStatement(String tableName, Map<String, Object> row) {
        try {
            if (row == null || row.isEmpty()) {
                return "";
            }

            // 获取列名
            Set<String> columns = row.keySet();
            String columnList = columns.stream()
                    .map(col -> "`" + col + "`")
                    .collect(Collectors.joining(", "));

            // 生成INSERT语句
            StringBuilder insertSql = new StringBuilder();
            insertSql.append("INSERT INTO `").append(tableName).append("` (");
            insertSql.append(columnList);
            insertSql.append(") VALUES (");

            List<String> values = new ArrayList<>();
            for (String column : columns) {
                Object value = row.get(column);
                values.add(formatValue(value));
            }

            insertSql.append(String.join(", ", values));
            insertSql.append(");");

            return insertSql.toString();

        } catch (Exception e) {
            log.error("生成INSERT语句失败: tableName={}", tableName, e);
            return "";
        }
    }

    /**
     * 格式化字段值
     */
    private String formatValue(Object value) {
        if (value == null) {
            return "NULL";
        }

        if (value instanceof String) {
            return "'" + escapeString((String) value) + "'";
        }

        if (value instanceof Number) {
            return value.toString();
        }

        if (value instanceof Date) {
            return "'" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((Date) value) + "'";
        }

        if (value instanceof Boolean) {
            return (Boolean) value ? "1" : "0";
        }

        return "'" + escapeString(value.toString()) + "'";
    }

    /**
     * 转义字符串
     */
    private String escapeString(String str) {
        if (str == null) {
            return "";
        }
        return str.replace("'", "''").replace("\\", "\\\\");
    }


    /**
     * 回滚事务
     */
    private void rollbackTransaction(Connection connection) {
        try {
            if (connection != null) {
                connection.rollback();
                log.info("事务已回滚");
            }
        } catch (SQLException rollbackEx) {
            log.error("回滚事务失败", rollbackEx);
        }
    }


    /**
     * 关闭资源
     *
     * @param statement  Statement对象
     * @param connection Connection对象
     */
    private void closeResources(Statement statement, Connection connection) {
        try {
            if (statement != null) {
                statement.close();
            }
        } catch (SQLException e) {
            log.warn("关闭Statement失败", e);
        }

        try {
            if (connection != null) {
                connection.close();
            }
        } catch (SQLException e) {
            log.warn("关闭Connection失败", e);
        }
    }

}