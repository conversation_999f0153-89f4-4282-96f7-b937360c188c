package com.polaris.kpi.eval.domain.task.dmsvc;

import cn.com.polaris.kpi.ObjItem;
import cn.com.polaris.kpi.eval.*;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.polaris.kpi.common.KpiI18NException;
import com.polaris.kpi.eval.domain.group.entity.EvalGroupEmp;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalKpiType;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalOperation;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalRule;
import com.polaris.kpi.eval.domain.task.entity.empeval.KpiListWrap;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.*;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.eval.domain.temp.entity.*;
import com.polaris.kpi.eval.domain.temp.entity.std.StdTemp;
import com.polaris.kpi.org.domain.dept.entity.CompanyConf;
import com.polaris.sdk.type.AuditEnum;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 应用模板到考核规则领域服务
 * @author: lufei
 * @date: 2022/8/28 15:43
 * @param:
 * @return:
 **/
@Slf4j
@Service
public class ApplyTempDmSvc {
    private String tenantId;
    private String opEmpType;
    private CompanyConf companyConf;
    private StdTemp temp;
    @Getter
    private EmpEvalRule empEvalRule;
    private AdminTask adminTask;
    private EvalUser baseUser;
    private List<BusinessPlanItem> planItemList;
    private EmpId opEmpId;
    @Getter
    private EmpEvalOperation operation;
    @Getter
    private List<EvalAudit> indConfirmNodes = new ArrayList<>();
    public ApplyTempDmSvc(CompanyConf companyConf) {
        this.companyConf = companyConf;
    }

    public ApplyTempDmSvc() {
    }

    public ApplyTempDmSvc(EvalUser user, CompanyConf companyConf, StdTemp temp, String opEmpId, String opEmpType) {
        this.tenantId = companyConf.getCompanyId();
        this.companyConf = companyConf;
        this.temp = temp;
        this.opEmpId = new EmpId(opEmpId);
        this.opEmpType = opEmpType;
        this.baseUser = user;
        this.baseUser.checkExistEvalEmp();
        if (!TalentStatus.DRAW_UP_ING.getStatus().equals(user.getTaskStatus())) {
            throw new KpiI18NException("40010", "被考核人考核状态已发生改变，请刷新页面重新操作！！");
        }
    }
    public void init(EmpEvalRule empEvalRule, AdminTask adminTask, List<BusinessPlanItem> planItemList) {
        this.empEvalRule = Objects.isNull(empEvalRule) ? new EmpEvalRule() : empEvalRule;
        this.empEvalRule.setInitiator(null);//清除考核发起人。@WUWU
        this.adminTask = adminTask;
        this.planItemList = planItemList;
        log.info("经营计划指标目标值，taskUserIds={}", JSONObject.toJSONString(planItemList));

        this.baseUser.setEmpEvalRule(this.empEvalRule);
        this.baseUser.setTaskName(adminTask.getTaskName()); //生成360问卷考核实例需要
    }


    public EmpEvalRule applyTemp2(StdTemp temp, EvalUser user, EmpId opEmpId, List<BusinessPlanItem> planItemList) {
        EmpEvalRule empEvalRule = user.getEmpEvalRule();
        empEvalRule.copyRuleName(temp.getName());
        empEvalRule.copyRuleDesc(temp.getTemplDesc());
        empEvalRule.setEmpEvalId(user.getId());
        empEvalRule.setEvaluateType(StrUtil.isEmpty(temp.getEvaluateType()) ? "simple" : temp.getEvaluateType());
        empEvalRule.setCreatedUser(opEmpId.getId());
        empEvalRule.setCompanyId(user.getCompanyId());
        empEvalRule.setCreateTotalLevelType(temp.getCreateTotalLevelType());
        empEvalRule.setShowResultType(temp.getShowResultType());
        //typeWeightConf指标类别权重
        {
            TypeWeightConf typeWeightConf = new TypeWeightConf(temp.getTypeWeightSwitch(),
                    temp.getTypeWeightLimitFlag(), temp.getCheckItemWeightFlag(), temp.getCheckItemWeight());
            empEvalRule.setTypeWeightConf(typeWeightConf);
        }
        //scoreValueConf分值的设定
        {
            ScoreValueConf scoreValueConf = new ScoreValueConf();
            scoreValueConf.setCustomFullScore(temp.getCustomFullScore());
            scoreValueConf.setExceedFullScore(Boolean.valueOf(temp.getExceedFullScore()));
            scoreValueConf.setScoreRangeType(temp.getScoreRangeType());
            scoreValueConf.setBaseScore(temp.getBaseScore());
            empEvalRule.setScoreValueConf(scoreValueConf);
        }
        //confirmTask确认任务 管理任务读取
        //editExeIndi执行中修改指标 管理任务读取
        //评分环节s3Self
        PerfTemplEvaluate evaluate = temp.getEvaluate();
        if (Objects.nonNull(evaluate)) {
            {
                RaterNodeConf s3SelfRater = new RaterNodeConf();
                if (Boolean.TRUE.toString().equals(evaluate.getSelfScoreFlag())) {
                    s3SelfRater.setOpen(1);
                    s3SelfRater.setRateMode(evaluate.getSelfScoreRule());
                    s3SelfRater.setNodeWeight(evaluate.getSelfScoreWeight());
                    //s3SelfRater.setAnonymous();周期上的配置
                } else {
                    s3SelfRater.setOpen(0);
                }
                s3SelfRater.setSignatureFlag(JSONUtil.toBean(evaluate.getSignatureConf(), ScoreSign.class).isSelf());
                empEvalRule.setS3SelfRater(s3SelfRater);
            }
            //评分环节s3PeerRater
            {
                MutualNodeConf s3PeerRater = buildPeerNode(evaluate, user, opEmpId);
                s3PeerRater.setSignatureFlag(JSONUtil.toBean(evaluate.getSignatureConf(), ScoreSign.class).isPeer());
                empEvalRule.setS3PeerRater(s3PeerRater);
            }

            //s3SubRater
            {
                MutualNodeConf s3SubRater = buildSubNode(evaluate, user, opEmpId);
                s3SubRater.setSignatureFlag(JSONUtil.toBean(evaluate.getSignatureConf(), ScoreSign.class).isSub());
                empEvalRule.setS3SubRater(s3SubRater);
            }

            //s3SuperRater
            {
                S3SuperRaterConf s3SuperRater = new S3SuperRaterConf();
                s3SuperRater.setSuperiorScoreOrder(evaluate.getSuperiorScoreOrder());
                empEvalRule.setSuperiorScoreOrder(evaluate.getSuperiorScoreOrder());
                if (Boolean.TRUE.toString().equals(evaluate.getSuperiorScoreFlag())) {
                    s3SuperRater.setOpen(1);
                    List<PerfTemplEvaluateAudit> supRates = evaluate.getSuperiorScoreList();
                    s3SuperRater.setRateMode(evaluate.getSuperiorScoreRule());
                    s3SuperRater.setNodeWeight(evaluate.getSuperiorScoreWeight());
                    s3SuperRater.setNodeVacancyFlag(evaluate.getNodeVacancyFlag());
                    s3SuperRater.setAuditNodes(PerfTemplEvaluateAudit.skpiRepeatSuperScore(temp.getCompanyId(),user.getEmpId(),user.getEvalOrgId(),user.getOrgId(),opEmpId,
                            AuditEnum.SUPERIOR_SCORE.getScene(),evaluate.getNodeVacancyFlag(),supRates));
                } else {
                    s3SuperRater.setOpen(0);
                }
                s3SuperRater.setSignatureFlag(JSONUtil.toBean(evaluate.getSignatureConf(), ScoreSign.class).isSuperior());
                empEvalRule.setS3SuperRater(s3SuperRater);
            }
            //s3AppointRater
            {
                S3RaterBaseConf s3RaterBaseConf = new S3RaterBaseConf();
                if (Boolean.TRUE.toString().equals(evaluate.getAppointScoreFlag())) {
                    s3RaterBaseConf.setOpen(1);
                    List<PerfTemplEvaluateAudit> appointRates = evaluate.getAppointScoreList();
                    s3RaterBaseConf.setAuditNodes(PerfTemplEvaluateAudit.skpiRepeatSuperScore(temp.getCompanyId(),user.getEmpId(),user.getEvalOrgId(),user.getOrgId(),opEmpId,
                            AuditEnum.SUPERIOR_SCORE.getScene(),evaluate.getNodeVacancyFlag(),appointRates));
                    s3RaterBaseConf.setNodeVacancyFlag(evaluate.getNodeVacancyFlag());
                    s3RaterBaseConf.setNodeWeight(evaluate.getAppointScoreWeight());
                } else {
                    s3RaterBaseConf.setOpen(0);
                }
                s3RaterBaseConf.setSignatureFlag(JSONUtil.toBean(evaluate.getSignatureConf(), ScoreSign.class).isAppoint());
                empEvalRule.setS3AppointRater(s3RaterBaseConf);
            }
        }
        user.setEmpEvalRule(empEvalRule);
        toEvalKpi(temp, user, opEmpId, planItemList);
        Set<Rater> totalLevelRaters = temp.explainTotalRaters(new EmpId(user.getEmpId()),user.getEvalOrgId(),user.getOrgId(), opEmpId);
        empEvalRule.setTotalLevelRaters(new ArrayList<>(totalLevelRaters));
        return empEvalRule;
    }

    public void toEvalKpi(StdTemp temp, EvalUser user, EmpId opEmpId, List<BusinessPlanItem> planItemList) {
        List<EmpEvalKpiType> kpiTypes = new ArrayList<>();
        for (PerfTemplKpiType temKpi : temp.getKpiTypes()) {
            EmpEvalKpiType kpiType = new EmpEvalKpiType();
            kpiType.setCompanyId(user.getCompanyId());
            kpiType.setTaskUserId(user.getId());
            kpiType.setKpiTypeId(temKpi.getTypeId());
            kpiType.setKpiTypeName(temKpi.getTypeName());
            kpiType.setIsOkr(temKpi.getIsOkr());
            kpiType.setTypeOrder(temKpi.getOrder());
            kpiType.setReserveOkrWeight(temKpi.getReserveOkrWeight());
            kpiType.setKpiTypeClassify(temKpi.getClassify());
            kpiType.setMaxExtraScore(temKpi.getMaxExtraScore());
            kpiType.setImportOkrFlag(Boolean.FALSE.toString());
            kpiType.setKpiTypeWeight(temKpi.getTypeWeight());
            kpiType.setOpenOkrScore(temKpi.getOpenOkrScore());
            kpiType.setPlusSubInterval(temKpi.getPlusSubInterval());
            kpiType.setKpiTypeUsedFields(temKpi.getKpiTypeUsedFields());
            kpiType.setAsk360TempId(temKpi.getAsk360TempId());
            kpiType.setAsk360TempName(temKpi.getAsk360TempName());
            kpiType.setAsk360TempDesc(temKpi.getAsk360TempDesc());
            kpiType.setScoringType(temKpi.getScoringType());
            kpiType.setIndLevelGroupId(temKpi.getIndLevelGroupId());
            kpiType.setScoreOptType(temKpi.getScoreOptType());
            kpiType.setDes(temKpi.getDes());

            //展开评分人员
            explainTypeRater(user, user.getEmpId(), opEmpId, user.getEvalOrgId(), kpiType, temKpi, user.getOrgId(),temp.getNodeVacancyFlagValue());

            //完成值审核可以指定角色，需要解析成具体的人
            List<StaffConfItem> finishValueAudit = temKpi.finishValueOfItem();
            if (CollUtil.isNotEmpty(finishValueAudit)) {
                parseDirectionalScorer(user, finishValueAudit, opEmpId);
            }
            kpiType.setFinishValueAudit(finishValueAudit);

            if (temKpi.getTypeEvaluate() != null) {
                EvalItemScoreRule typeRule = toItemRule(temp.getNodeVacancyFlagValue(),temKpi.getTypeEvaluate(), user, opEmpId);
                kpiType.setTypeRule(typeRule);
            }
            if (temKpi.getItemLimitCnt() != null) {
                kpiType.setItemLimitCnt(JSONObject.parseObject(temKpi.getItemLimitCnt(), KpiItemLimitCnt.class));
            }
            if (temKpi.getIsTypeLocked() != null && StrUtil.isNotBlank(temKpi.getIsTypeLocked())) {
                kpiType.setLockedItems(JSONObject.parseArray(temKpi.getIsTypeLocked(), String.class));
            }
            if (CollUtil.isEmpty(temKpi.getItems())) {
                kpiTypes.add(kpiType);
                continue;
            }
            List<EvalKpi> items = new ArrayList<>();
            Map<String, BusinessPlanItem> planItemMap = CollUtil.toMap(planItemList, new HashMap<>(), p -> p.getIndexId());
            for (PerfTemplKpiItem tempItem : temKpi.getItems()) {
                EvalKpi kpi = Convert.convert(EvalKpi.class, tempItem);
                kpi.setId(null);
                kpi.setCompanyId(user.getCompanyId());
                kpi.setTaskUserId(user.getId());
                kpi.setTaskId(user.getTaskId());
                kpi.setEmpId(user.getEmpId());
                kpi.setOrgId(user.getOrgId());
                kpi.setKpiTypeId(temKpi.getTypeId());
                kpi.setKpiTypeName(temKpi.getTypeName());
                kpi.setKpiTypeWeight(temKpi.getTypeWeight());
                kpi.setFieldValueList(tempItem.getFieldValueList());
                //kpi.setKpiItemId(tempItem.getKpiItemId());
                //kpi.setKpiItemName(tempItem.getKpiItemName());
                kpi.setItemTargetValue(tempItem.getItemValue());
                kpi.setItemFinishValue(null);
                //kpi.setItemUnit(tempItem.getItemUnit());
                //kpi.setItemWeight(tempItem.getItemWeight());
                kpi.setFormulaType(tempItem.getFormulaType());
                kpi.setItemFieldCorr(tempItem.getItemFieldCorr());

                //kpi.setResultInputType(tempItem.getResultInputType());
                //kpi.setResultInputEmpId(tempItem.getResultInputEmpId());
                //kpi.setScoringRule(tempItem.getScoringRule());
                //kpi.setScorerType(tempItem.getScorerType());
                //如果录入人是指定主管/角色，则需要解析为具体的人
                buildInputUser(user, tempItem, kpi, opEmpId);

                //定向评分可以指定角色，需要解析成具体的人
                List<StaffConfItem> scorerObjId = tempItem.scorerOfItem();
                if (CollUtil.isNotEmpty(scorerObjId)) {
                    parseDirectionalScorer(user, scorerObjId, opEmpId);
                }
                kpi.setScorerObjId(scorerObjId);

                //完成值审核可以指定角色，需要解析成具体的人
                List<StaffConfItem> itemFinishValueAudit = tempItem.finishValueOfItem();
                if (CollUtil.isNotEmpty(itemFinishValueAudit)) {
                    parseDirectionalScorer(user, itemFinishValueAudit, opEmpId);
                }
                kpi.setFinishValueAudit(itemFinishValueAudit);

                kpi.setItemType(tempItem.getItemType());
                kpi.setMultipleReviewersType(tempItem.getMultipleReviewersType());
                kpi.setKpiTypeClassify(temKpi.getClassify());
                kpi.setPlusLimit(tempItem.getPlusLimit());
                kpi.setSubtractLimit(tempItem.getSubtractLimit());
                kpi.setMaxExtraScore(temKpi.getMaxExtraScore());
                kpi.setOrder(tempItem.getOrder());
                kpi.setThresholdJson(tempItem.getThresholdJson());
                kpi.setFormulaCondition(tempItem.getFormulaCondition());
                kpi.setItemFieldJson(tempItem.getItemFieldJson());
                kpi.setIsTypeLocked(StrUtil.isNotBlank(temKpi.getIsTypeLocked()) ?
                        Boolean.TRUE.toString() : Boolean.FALSE.toString());
                kpi.setIsOkr(temKpi.getIsOkr());
                kpi.setTypeOrder(tempItem.getOrder());
                kpi.setReserveOkrWeight(temKpi.getReserveOkrWeight());
                kpi.setItemScoreValue(tempItem.getItemScoreValue());
                kpi.setInputFormat(tempItem.getInputFormat());
                kpi.setShowTargetValue(tempItem.getShowTargetValue());
                kpi.setMustResultInput(tempItem.getMustResultInput());
                kpi.setShowFinishBar(tempItem.getShowFinishBar());
                kpi.setIsNewEmp(tempItem.getIsNewEmp());
                kpi.setItemFormula(tempItem.getItemFormula());
                kpi.setManagerLevel(tempItem.getManagerLevel());
                kpi.setItemFullScoreCfg(tempItem.getItemFullScoreCfg());
                kpi.setItemRule(tempItem.getItemRule());
                kpi.setPlusSubInterval(tempItem.getPlusSubInterval());
                kpi.setFieldValueList(tempItem.getFieldValueList());
                this.parseConfirmIndFlow(tempItem);
                BusinessPlanItem planItem = planItemMap.get(tempItem.getKpiItemId());
                if (Objects.nonNull(planItem)) {
                    if (planItem.getInfoList().size() == 1) {
                        kpi.setOkrGoalId(planItem.getInfoList().get(0).getObjectId());
                        kpi.setItemTargetValue(planItem.getInfoList().get(0).getTargetValue());
                        kpi.setItemFinishValue(planItem.getInfoList().get(0).getFinishValue());
                    }
                }
                //自动计算指标没有ItemEvaluate
                if (temp.isCustom() && Objects.nonNull(tempItem.getItemEvaluate())) {
                    if (!tempItem.isAutoScorerType()) {
                        PerfTemplItemEvaluate tempItemEvaluate = tempItem.getItemEvaluate();
                        //如果指标上设置了评分流程，则使用指标上设置的流程,指标的设置，用在默认流程设置上
                        if (tempItemEvaluate.hasItemScoreRule()) {
                            EvalItemScoreRule itemScoreRule = toItemRule(temp.getNodeVacancyFlagValue(),tempItemEvaluate, user, opEmpId);
                            kpi.setItemScoreRule(itemScoreRule);
                        } else {
                            //否则使用默认流程
                            EmpEvalRule empEvalRule = user.getEmpEvalRule();
                            kpi.setItemScoreRule(empEvalRule.asItemRule());
                        }
                    }
                }
                if (CollUtil.isNotEmpty(tempItem.getFormulaFieldList())) {
                    List<EvalFormulaField> formulas = Convert.toList(EvalFormulaField.class, tempItem.getFormulaFieldList());
                    if (CollUtil.isNotEmpty(formulas)) {
                        formulas.forEach(f -> f.cleanId());
                    }
                    kpi.copyFormulaFields(formulas);
                }
                items.add(kpi);
            }
            kpiType.setItems(items);
            kpiTypes.add(kpiType);
        }
        EmpEvalRule empEvalRule = user.getEmpEvalRule();
        empEvalRule.setKpiTypes(new KpiListWrap(kpiTypes));
    }
    private void parseConfirmIndFlow(PerfTemplKpiItem tempItem) {
        BaseAuditNode onIndNode = adminTask.getConfirmTask().findConfirmOnIndNode();
        ItemCustomFieldValue field = tempItem.findIndConfirmField();//字段是公用的同一个对象
        if (onIndNode != null && field != null) {
            if (StrUtil.isBlank(field.getFieldValue())) {
                return;
            }
            //更新自定字段 BaseAuditNode.toJsonString
            BaseAuditNode bean = JSONUtil.toBean(field.getFieldValue(), BaseAuditNode.class);
            field.setFieldValue(JSONUtil.toJsonStr(bean));
            if (CollUtil.isNotEmpty(bean.getRaters())) {
                return;
            }
            //生成指标audit记录
            EvalAudit confirmOnIndNode = JSONUtil.toBean(field.getFieldValue(), EvalAudit.class);
            confirmOnIndNode.setMultipleReviewersType(onIndNode.getMultiType());
            confirmOnIndNode.setModifyFlag(onIndNode.getModifyFlag());
            confirmOnIndNode.setEmpId(baseUser.getEmpId());
            confirmOnIndNode.setTaskId(baseUser.getTaskId());
            confirmOnIndNode.setOrgId(baseUser.getOrgId());
            confirmOnIndNode.setKpiItemId(field.getKpiItemId());
            confirmOnIndNode.setTransferFlag(onIndNode.getTransferFlag());
            confirmOnIndNode.setCreatedUser(opEmpId.getId());
            confirmOnIndNode.setCompanyId(new TenantId(tempItem.getCompanyId()));
            confirmOnIndNode.setApproverType(confirmOnIndNode.getApproverType());
            indConfirmNodes.add(confirmOnIndNode);
            bean.setRaters(confirmOnIndNode.parseRaterList());//

        }
    }
    //展开维度评分人员到考核表
    private void explainTypeRater(EvalUser user, String empId, EmpId opEmpId, String evalOrgId, EmpEvalKpiType kpiType, PerfTemplKpiType tmpType, String orgId,Integer nodeVacancyFlag) {
        MutualNodeConf beforePeerRater = Objects.nonNull(tmpType.getPeerRater())?tmpType.getPeerRater().clone():new MutualNodeConf(Boolean.FALSE.toString(),null,null);
        MutualNodeConf beforeSubRater = Objects.nonNull(tmpType.getSubRater())?tmpType.getSubRater().clone():new MutualNodeConf(Boolean.FALSE.toString(),null,null);
        S3SuperRaterConf beforeSuperRater = Objects.nonNull(tmpType.getSuperRater())?tmpType.getSuperRater().clone():new S3SuperRaterConf();
        S3RaterBaseConf beforeAppointRater = Objects.nonNull(tmpType.getAppointRater())?tmpType.getAppointRater().clone():new S3RaterBaseConf();

        tmpType.explainTypeRater(empId, opEmpId, evalOrgId, orgId,nodeVacancyFlag);
        kpiType.setSelfRater(tmpType.getSelfRater());
        kpiType.setPeerRater(BeanUtil.copyProperties(tmpType.getPeerRater(), MutualNodeConf.class));
        kpiType.setSubRater(BeanUtil.copyProperties(tmpType.getSubRater(), MutualNodeConf.class));
        kpiType.setSuperRater(tmpType.getSuperRater());
        kpiType.setAppointRater(tmpType.getAppointRater());
        //恢复模版类别rater配置
        tmpType.setPeerRater(beforePeerRater);
        tmpType.setSubRater(beforeSubRater);
        tmpType.setSuperRater(beforeSuperRater);
        tmpType.setAppointRater(beforeAppointRater);

        if (tmpType.peerRaterWaitAppoint()) {
            List<Rater> raters = parseWaitAppointRaters(user, tmpType.getPeerUserType(), tmpType.getPeerUserValue(), tmpType.getPeerUserName(), opEmpId);
            InviteMutualAudit mutualAudit = null;
            if (tmpType.openPeerInviteMutualAudit()) {
                mutualAudit = tmpType.getPeerRater().getAppointer().getInviteMutualAudit();
                List<Rater> auditRaters = parseWaitAppointRaters(user, mutualAudit.getApproverTypeConvert(),mutualAudit.getApproverInfo(), mutualAudit.getApproverName(), opEmpId);
                mutualAudit.setRaters(auditRaters);
            }
            kpiType.getPeerRater().setAppointer(new WaitAppoint(tmpType.getPeerUserType(), raters,mutualAudit,tmpType.getPeerUserValue()));
            kpiType.getPeerRater().setRaters(new ArrayList<>());
        }
        if (tmpType.subRaterWaitAppoint()) {
            List<Rater> raters = parseWaitAppointRaters(user, tmpType.getSubUserType(), tmpType.getSubUserValue(), tmpType.getSubUserName(), opEmpId);
            InviteMutualAudit mutualAudit = null;
            if (tmpType.openSubInviteMutualAudit()) {
                mutualAudit = tmpType.getSubRater().getAppointer().getInviteMutualAudit();
                List<Rater> auditRaters = parseWaitAppointRaters(user, mutualAudit.getApproverTypeConvert(),mutualAudit.getApproverInfo(), mutualAudit.getApproverName(), opEmpId);
                mutualAudit.setRaters(auditRaters);
            }
            kpiType.getSubRater().setAppointer(new WaitAppoint(tmpType.getSubUserType(), raters,mutualAudit,tmpType.getSubUserValue()));
            kpiType.getSubRater().setRaters(new ArrayList<>());
        }
    }

    public EvalItemScoreRule toItemRule(Integer nodeVacancyFlag,PerfTemplItemEvaluate tempItemEvaluate, EvalUser user, EmpId opEmpId) {
        EvalItemScoreRule itemScoreRule = new EvalItemScoreRule();
        //self自评
        {
            RaterNodeConf s3SelfRater = new RaterNodeConf();
            if (Boolean.TRUE.toString().equals(tempItemEvaluate.getSelfScoreFlag())) {
                s3SelfRater.setOpen(1);
                s3SelfRater.setRateMode("item");
                s3SelfRater.setNodeWeight(tempItemEvaluate.getSelfScoreWeight());
            } else {
                s3SelfRater.setOpen(0);
            }
            s3SelfRater.setSignatureFlag(JSONUtil.toBean(tempItemEvaluate.getSignatureConf(), ScoreSign.class).isSelf());
            itemScoreRule.setSelfRater(s3SelfRater);
        }
        //评分环节peerRater
        {
            MutualNodeConf peerRater = buildCustomPeerNode(tempItemEvaluate, user, opEmpId);
            peerRater.setSignatureFlag(JSONUtil.toBean(tempItemEvaluate.getSignatureConf(), ScoreSign.class).isPeer());
            itemScoreRule.setPeerRater(peerRater);
        }
        //subRater
        {
            MutualNodeConf subRater = buildCustomSubNode(tempItemEvaluate, user, opEmpId);
            subRater.setSignatureFlag(JSONUtil.toBean(tempItemEvaluate.getSignatureConf(), ScoreSign.class).isSub());
            itemScoreRule.setSubRater(subRater);
        }
        //supRater
        {
            S3SuperRaterConf supRater = new S3SuperRaterConf();
            if (Boolean.TRUE.toString().equals(tempItemEvaluate.getSuperiorScoreFlag())) {
                supRater.setOpen(1);
                List<PerfTemplEvaluateAudit> supRates = tempItemEvaluate.getSuperiorScoreList();
                supRater.setRateMode("item");
                supRater.setNodeWeight(tempItemEvaluate.getSuperiorScoreWeight());

                //"superiorScoreOrder": "sameTime",
                supRater.setAuditNodes(PerfTemplEvaluateAudit.skpiRepeatSuperScore(user.getCompanyId(),user.getEmpId(),user.getEvalOrgId(),user.getOrgId(),opEmpId,
                        AuditEnum.SUPERIOR_SCORE.getScene(),nodeVacancyFlag,supRates));
            } else {
                supRater.setOpen(0);
            }
            supRater.setSignatureFlag(JSONUtil.toBean(tempItemEvaluate.getSignatureConf(), ScoreSign.class).isSuperior());
            supRater.setNodeVacancyFlag(nodeVacancyFlag);
            EmpEvalRule empEvalRule = user.getEmpEvalRule();
            if (Objects.nonNull(empEvalRule)) {
                supRater.setSuperiorScoreOrder(empEvalRule.getSuperiorScoreOrder());
            }
            itemScoreRule.setSuperRater(supRater);
        }
        //appointRater
        {
            S3RaterBaseConf appointRater = new S3RaterBaseConf();
            if (Boolean.TRUE.toString().equals(tempItemEvaluate.getAppointScoreFlag())) {
                appointRater.setOpen(1);
                List<PerfTemplEvaluateAudit> appointRates = tempItemEvaluate.getAppointScoreList();
                appointRater.setRateMode("item");
                appointRater.setNodeWeight(tempItemEvaluate.getAppointScoreWeight());

                //"APPOINT_SCORE": "sameTime",
                appointRater.setAuditNodes(PerfTemplEvaluateAudit.skpiRepeatSuperScore(user.getCompanyId(),user.getEmpId(),user.getEvalOrgId(),user.getOrgId(),opEmpId,
                        AuditEnum.APPOINT_SCORE.getScene(),nodeVacancyFlag,appointRates));
            } else {
                appointRater.setOpen(0);
            }
            appointRater.setSignatureFlag(JSONUtil.toBean(tempItemEvaluate.getSignatureConf(), ScoreSign.class).isAppoint());
            appointRater.setNodeVacancyFlag(tempItemEvaluate.getNodeVacancyFlag());
            itemScoreRule.setAppointRater(appointRater);
        }
        return itemScoreRule;
    }


    private void parseDirectionalScorer(EvalUser user, List<StaffConfItem> confItemList, EmpId opEmpId) {
        for (StaffConfItem staffConfItem : confItemList) {
            List<ObjItem> parsedRaters = new ArrayList<>();
            if ("taskEmp".equals(staffConfItem.getObjType())) {
                parsedRaters.add(new ObjItem(user.getEmpId(), user.getEmpName()));
                staffConfItem.setObjItems(parsedRaters);
                continue;
            }
            if (!"role".equals(staffConfItem.getObjType()) && !"manager".equals(staffConfItem.getObjType())) {
                continue;
            }
            for (ObjItem objItem : staffConfItem.getObjItems()) {
                EvalAudit audit = new EvalAudit(staffConfItem.getObjType(), objItem.getObjId(),
                        user.getCompanyId(), new EmpId(user.getEmpId()), opEmpId, user.getEvalOrgId(), user.getOrgId());
                List<Rater> raters = audit.parseRaters();
                for (Rater rater : raters) {
                    ObjItem o = new ObjItem();
                    if ("role".equals(staffConfItem.getObjType())) {
                        o = new ObjItem(rater.getEmpId(), rater.getEmpName(), objItem.getObjId(), objItem.getObjName());
                    } else {
                        o = new ObjItem(rater.getEmpId(), rater.getEmpName(), Integer.valueOf(objItem.getObjId()), objItem.getObjName());
                    }
                    parsedRaters.add(o);
                }
            }
            staffConfItem.setObjItems(parsedRaters);
        }
    }

    public List<Rater> parseTemAudits(EvalUser user, List<PerfTemplEvaluateAudit> temAudits, BigDecimal weight, EmpId opEmpId) {
        List<Rater> raters = new ArrayList<>();
        for (PerfTemplEvaluateAudit temAudit : temAudits) {
            if ("user".equals(temAudit.getApproverType())) {
                Rater rater = new Rater(temAudit.getApproverInfo(), temAudit.getApproverEmpName(), weight);
                raters.add(rater);
                continue;
            }
            if ("role".equals(temAudit.getApproverType()) || StrUtil.isBlank(user.getEvalOrgId())) {
                EvalAudit audit = new EvalAudit(temAudit.getApproverType(), temAudit.getApproverInfo(),
                        user.getCompanyId(), new EmpId(user.getEmpId()), opEmpId, user.getEvalOrgId(), user.getOrgId(), temAudit.isHasExcludeAllManager());
                List<Rater> raterList = audit.parseRaters();
                if (CollUtil.isEmpty(raterList)) {
                    user.confEvalRuleErro("score");
                }
                raters.addAll(raterList);
                continue;
            }
            List<Rater> raterList;
            EvalAudit audit = new EvalAudit(temAudit.getApproverType(), temAudit.getApproverInfo(), user.getCompanyId(),
                    new EmpId(user.getEmpId()), opEmpId, user.getEvalOrgId(), user.getOrgId(), temAudit.isHasExcludeAllManager());
            if (user.evalOrg()) {
                raterList = audit.parseAtOrgMg();
            } else {
                raterList = audit.parseRaters();
            }
            if (CollUtil.isEmpty(raterList)) {
                user.confEvalRuleErro("score");
            }
            raters.addAll(raterList);
        }
        return raters;
    }

    private MutualNodeConf buildSubNode(PerfTemplEvaluate evaluate, EvalUser user, EmpId opEmpId) {
        MutualNodeConf s3SubRater = new MutualNodeConf();
        s3SubRater.setOpen(evaluate.subScoreFlag() ? 1 : 0);
        if (!s3SubRater.isOpen()) {
            return s3SubRater;
        }
        s3SubRater.setFucType(evaluate.getSubFucType());
        s3SubRater.setRateMode(evaluate.getSubScoreRule());
        s3SubRater.setNodeWeight(evaluate.getSubScoreWeight());
        s3SubRater.setNode(AuditEnum.SUB_SCORE.getScene());
        s3SubRater.setExcludeAllManager(Boolean.valueOf(evaluate.getSubExcludeAllManager()));
        s3SubRater.setScorerNumCof(JSONUtil.toBean(evaluate.getSubLimitScorerNumCof(), LimitScorerNumCof.class));
        if (evaluate.subRaterWaitAppoint()) {
            List<Rater> waitAppointRaters = parseWaitAppointRaters(user, evaluate.getSubUserType(),
                    evaluate.getSubUserValue(), evaluate.getSubUserName(), opEmpId);
            if (evaluate.openSubInviteMutualAudit()) {
                InviteMutualAudit audit = JSONUtil.toBean(evaluate.getSubInviteMutualAuditConf(),InviteMutualAudit.class);
                List<Rater> inviteMutualAuditRaters = parseWaitAppointRaters(user, audit.getApproverTypeConvert(),
                        audit.getApproverInfo(), audit.getApproverName(), opEmpId);
                audit.setRaters(inviteMutualAuditRaters);
                s3SubRater.setAppointer(new WaitAppoint(evaluate.matchPeerUserType(), waitAppointRaters,audit,evaluate.getSubUserValue()));
                return s3SubRater;
            }
            s3SubRater.setAppointer(new WaitAppoint(evaluate.matchSubUserType(),evaluate.getPeerUserValue(), waitAppointRaters));
            return s3SubRater;
        }
        List<PerfTemplEvaluateAudit> subScoreList = evaluate.unitaryMutualRater() ?
                evaluate.getSubScoreList() :
                evaluate.getSubScoreList().stream().filter(e -> user.getEmpId().equals(e.getEmpId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(subScoreList)) {
            //取默认的的互评人
            subScoreList = evaluate.getSubScoreList().stream().filter(e -> StrUtil.isBlank(e.getEmpId())).collect(Collectors.toList());
        }
        if (CollUtil.isEmpty(subScoreList)) {
            return s3SubRater;
        }
        PerfTemplEvaluateAudit sub = subScoreList.get(0);
        s3SubRater.setApproverType(sub.getApproverType());
        s3SubRater.setApproverInfo(sub.getApproverInfo());
        s3SubRater.setApprovalOrder(sub.getApprovalOrder());
        s3SubRater.builderParentStr(subScoreList);
        subScoreList.forEach(subS -> subS.setHasExcludeAllManager(Boolean.valueOf(evaluate.getSubExcludeAllManager())));
        List<Rater> raters = parseTemAudits(user, subScoreList, getScoreWeight(user.isOpenAvgWeightCompute(), evaluate.getSubScoreWeight()), opEmpId);
        s3SubRater.setRaters(raters);
        //处理重复人
        s3SubRater.repeatRater();
        if (!companyConf.allowSelfAsPeer()) {
            s3SubRater.filterMutualSelf(user.getEmpId());
        }
        //处理限制数
        s3SubRater.randomRaters();
        return s3SubRater;
    }

    private MutualNodeConf buildPeerNode(PerfTemplEvaluate evaluate, EvalUser user, EmpId opEmpId) {
        MutualNodeConf s3PeerRater = new MutualNodeConf();
        s3PeerRater.setOpen(evaluate.peerScoreFlag() ? 1 : 0);
        if (!s3PeerRater.isOpen()) {
            return s3PeerRater;
        }
        s3PeerRater.setFucType(evaluate.getPeerFucType());
        s3PeerRater.setRateMode(evaluate.getPeerScoreRule());
        s3PeerRater.setNodeWeight(evaluate.getPeerScoreWeight());
        s3PeerRater.setNode(AuditEnum.PEER_SCORE.getScene());
        s3PeerRater.setScorerNumCof(JSONUtil.toBean(evaluate.getPeerLimitScorerNumCof(), LimitScorerNumCof.class));
        s3PeerRater.setExcludeAllManager(Boolean.valueOf(evaluate.getPeerExcludeAllManager()));
        if (evaluate.peerRaterWaitAppoint()) {
            List<Rater> waitAppointRaters = parseWaitAppointRaters(user, evaluate.getPeerUserType(),
                    evaluate.getPeerUserValue(), evaluate.getPeerUserName(), opEmpId);
            if (evaluate.openPeerInviteMutualAudit()) {
                InviteMutualAudit audit = JSONUtil.toBean(evaluate.getPeerInviteMutualAuditConf(),InviteMutualAudit.class);
                List<Rater> inviteMutualAuditRaters = parseWaitAppointRaters(user, audit.getApproverTypeConvert(),
                        audit.getApproverInfo(), audit.getApproverName(), opEmpId);
                audit.setRaters(inviteMutualAuditRaters);
                s3PeerRater.setAppointer(new WaitAppoint(evaluate.matchPeerUserType(), waitAppointRaters,audit,evaluate.getPeerUserValue()));
                return s3PeerRater;
            }
            s3PeerRater.setAppointer(new WaitAppoint(evaluate.matchPeerUserType(),evaluate.getPeerUserValue(), waitAppointRaters));
            return s3PeerRater;
        }
        List<PerfTemplEvaluateAudit> peerScoreList = evaluate.unitaryMutualRater() ?
                evaluate.getPeerScoreList() :
                evaluate.getPeerScoreList().stream().filter(e -> user.getEmpId().equals(e.getEmpId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(peerScoreList)) {
            peerScoreList = evaluate.getPeerScoreList().stream().filter(e -> StrUtil.isBlank(e.getEmpId())).collect(Collectors.toList());
        }
        if (CollUtil.isEmpty(peerScoreList)) {
            return s3PeerRater;
        }
        PerfTemplEvaluateAudit peer = peerScoreList.get(0);
        s3PeerRater.setApproverType(peer.getApproverType());
        s3PeerRater.setApproverInfo(peer.getApproverInfo());
        //周期配置或管理任务配置
        s3PeerRater.setApprovalOrder(peer.getApprovalOrder());
        s3PeerRater.builderParentStr(peerScoreList);
        peerScoreList.forEach(peerS -> peerS.setHasExcludeAllManager(Boolean.valueOf(evaluate.getPeerExcludeAllManager())));
       // List<Rater> raters = parseTemAudits(user, peerScoreList, evaluate.getPeerScoreWeight(), opEmpId);
        //同级互评每个人100权重
        List<Rater> raters = parseTemAudits(user, peerScoreList, getScoreWeight(user.isOpenAvgWeightCompute(),evaluate.getPeerScoreWeight()), opEmpId);
        s3PeerRater.setRaters(raters);
        //处理重复人
        s3PeerRater.repeatRater();
        if (!companyConf.allowSelfAsPeer()) {
            s3PeerRater.filterMutualSelf(user.getEmpId());
        }
        //处理限制数
        s3PeerRater.randomRaters();
        return s3PeerRater;
    }

    private MutualNodeConf buildCustomSubNode(PerfTemplItemEvaluate tempItemEvaluate, EvalUser user, EmpId opEmpId) {
        MutualNodeConf subRater = new MutualNodeConf();
        subRater.setOpen(tempItemEvaluate.subScoreFlag() ? 1 : 0);
        if (!subRater.isOpen()) {
            return subRater;
        }
        subRater.setRateMode("item");
        subRater.setNodeWeight(tempItemEvaluate.getSubScoreWeight());
        subRater.setNode(AuditEnum.SUB_SCORE.getScene());
        subRater.setExcludeAllManager(Boolean.valueOf(tempItemEvaluate.getSubExcludeAllManager()));
        subRater.setScorerNumCof(JSONUtil.toBean(tempItemEvaluate.getSubLimitScorerNumCof(), LimitScorerNumCof.class));
        if (tempItemEvaluate.subRaterWaitAppoint()) {
            List<Rater> waitAppointRaters = parseWaitAppointRaters(user, tempItemEvaluate.getSubUserType(),
                    tempItemEvaluate.getSubUserValue(), tempItemEvaluate.getSubUserName(), opEmpId);
            InviteMutualAudit mutualAudit = null;
            if (tempItemEvaluate.openSubInviteMutualAudit()) {
                mutualAudit = JSONUtil.toBean(tempItemEvaluate.getSubInviteMutualAuditConf(),InviteMutualAudit.class);
                List<Rater> auditRaters = parseWaitAppointRaters(user, mutualAudit.getApproverTypeConvert(),
                        mutualAudit.getApproverInfo(), mutualAudit.getApproverName(), opEmpId);
                mutualAudit.setRaters(auditRaters);
            }
            subRater.setAppointer(new WaitAppoint(tempItemEvaluate.matchSubUserType(), waitAppointRaters,mutualAudit,tempItemEvaluate.getSubUserValue()));
            return subRater;
        }
        List<PerfTemplEvaluateAudit> subScoreList = tempItemEvaluate.unitarySubRater() ?
                tempItemEvaluate.getSubScoreList() :
                tempItemEvaluate.getSubScoreList().stream().filter(p -> user.getEmpId().equals(p.getEmpId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(subScoreList)) {
            user.confEvalRuleErro("score");
            return subRater;
        }
        /*if (CollUtil.isEmpty(subScoreList)) {
            throw new KpiI18NException(" temp mutual not can eval emp ", "模板中的下级互评人不能为被考核人，请调整模板互评人配置");
        }*/
        if (CollUtil.isNotEmpty(subScoreList)) {
            PerfTemplEvaluateAudit sub = subScoreList.get(0);
            subRater.setApproverType(sub.getApproverType());
            subRater.setApproverInfo(sub.getApproverInfo());
            subRater.setApprovalOrder(sub.getApprovalOrder());
            subRater.builderParentStr(subScoreList);
            subScoreList.forEach(subS -> subS.setHasExcludeAllManager(Boolean.valueOf(tempItemEvaluate.getSubExcludeAllManager())));
            subRater.setRaters(parseTemAudits(user, subScoreList, getScoreWeight(user.isOpenAvgWeightCompute(),tempItemEvaluate.getSubScoreWeight()), opEmpId));
            if (!companyConf.allowSelfAsPeer()) {
                subRater.filterMutualSelf(user.getEmpId());
            }
        }
        subRater.randomRaters();
        subRater.repeatRater();//去重
        return subRater;
    }

    private BigDecimal getScoreWeight(boolean isOpenAvgWeightCompute, BigDecimal scoreWeight) {
        if (isOpenAvgWeightCompute) {
            return scoreWeight;
        } else {
            return new BigDecimal("100.00");
        }
    }
    private MutualNodeConf buildCustomPeerNode(PerfTemplItemEvaluate tempItemEvaluate, EvalUser user, EmpId opEmpId) {
        MutualNodeConf peerRater = new MutualNodeConf();
        peerRater.setOpen(tempItemEvaluate.peerScoreFlag() ? 1 : 0);
        if (!peerRater.isOpen()) {
            return peerRater;
        }
        peerRater.setRateMode("item");
        peerRater.setNodeWeight(tempItemEvaluate.getPeerScoreWeight());
        peerRater.setNode(AuditEnum.PEER_SCORE.getScene());
        peerRater.setExcludeAllManager(Boolean.valueOf(tempItemEvaluate.getPeerExcludeAllManager()));
        peerRater.setScorerNumCof(JSONUtil.toBean(tempItemEvaluate.getPeerLimitScorerNumCof(), LimitScorerNumCof.class));
        if (tempItemEvaluate.peerRaterWaitAppoint()) {
            List<Rater> waitAppointRaters = parseWaitAppointRaters(user, tempItemEvaluate.getPeerUserType(),
                    tempItemEvaluate.getPeerUserValue(), tempItemEvaluate.getPeerUserName(), opEmpId);
            InviteMutualAudit mutualAudit = null;
            if (tempItemEvaluate.openPeerInviteMutualAudit()) {
                mutualAudit = JSONUtil.toBean(tempItemEvaluate.getPeerInviteMutualAuditConf(),InviteMutualAudit.class);
                List<Rater> auditRaters = parseWaitAppointRaters(user, mutualAudit.getApproverTypeConvert(),
                        mutualAudit.getApproverInfo(), mutualAudit.getApproverName(), opEmpId);
                mutualAudit.setRaters(auditRaters);
            }
            peerRater.setAppointer(new WaitAppoint(tempItemEvaluate.matchPeerUserType(), waitAppointRaters,mutualAudit,tempItemEvaluate.getPeerUserValue()));
            return peerRater;
        }
        //设置相同的互评分
        List<PerfTemplEvaluateAudit> peerScoreList = tempItemEvaluate.unitaryPeerRater() ?
                tempItemEvaluate.getPeerScoreList() :
                tempItemEvaluate.getPeerScoreList().stream().filter(p -> user.getEmpId().equals(p.getEmpId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(peerScoreList)) {
            user.confEvalRuleErro("score");
            return peerRater;
        }
//        if (CollUtil.isEmpty(peerScoreList)) {
//            throw new KpiI18NException(" temp mutual not can eval emp ", "模板中的同级互评人不能为被考核人，请调整模板同级互评人配置");
//        }
        if (CollUtil.isNotEmpty(peerScoreList)) {
            PerfTemplEvaluateAudit peer = peerScoreList.get(0);
            peerRater.setApproverType(peer.getApproverType());
            peerRater.setApproverInfo(peer.getApproverInfo());
            peerRater.setApprovalOrder(peer.getApprovalOrder());
            peerRater.builderParentStr(peerScoreList);
            peerScoreList.forEach(peerS -> peerS.setHasExcludeAllManager(Boolean.valueOf(tempItemEvaluate.getPeerExcludeAllManager())));
            peerRater.setRaters(parseTemAudits(user, peerScoreList,getScoreWeight(user.isOpenAvgWeightCompute(),tempItemEvaluate.getPeerScoreWeight()), opEmpId));
            if (!companyConf.allowSelfAsPeer()) {
                peerRater.filterMutualSelf(user.getEmpId());
            }
        }
        peerRater.randomRaters();
        peerRater.repeatRater();//去重
        return peerRater;
    }

    //解析一下由谁邀请互评人
    public List<Rater> parseWaitAppointRaters(EvalUser user, String userType, String userValue, String userName, EmpId opEmpId) {
        String approverType = "";
        String approverInfo = "";
        //由被考核人邀请互评人
        if ("examInvite".equals(userType)) {
            return new ArrayList<>();
        }
        //由指定人邀请
        if ("userInvite".equals(userType) || "empInvite".equals(userType)) {
            Rater rater = new Rater(userValue, userName);
            return Arrays.asList(rater);
        }
        if ("roleInvite".equals(userType)) {
            approverType = "role";
            approverInfo = userValue;
            EvalAudit audit = new EvalAudit(approverType, approverInfo, user.getCompanyId(),
                    new EmpId(user.getEmpId()), opEmpId, user.getEvalOrgId(), user.getOrgId());
            List<Rater> raters = audit.parseRaters();
            if (CollUtil.isEmpty(raters)) {
                user.setRuleConfStatus(106);
            }
            return raters;
        }
        if ("managerInvite".equals(userType)) {
            approverType = "manager";
            approverInfo = userValue;
        }
        if (user.evalOrg()) {
            EvalAudit audit = new EvalAudit(approverType, approverInfo, user.getCompanyId(),
                    opEmpId, opEmpId, user.getEvalOrgId(), user.getOrgId());
            List<Rater> raters = audit.parseAtOrgMg();
            if (CollUtil.isEmpty(raters)) {
                user.setRuleConfStatus(106);
            }
            return raters;
        }
        EvalAudit audit = new EvalAudit(approverType, approverInfo, user.getCompanyId(),
                new EmpId(user.getEmpId()), opEmpId, user.getEvalOrgId(), user.getOrgId());
        List<Rater> raters = audit.parseRaters();
        if (CollUtil.isEmpty(raters)) {
            user.setRuleConfStatus(106);
        }
        return raters;
    }

    private void buildInputUser(EvalUser user, PerfTemplKpiItem tempItem, EvalKpi kpi, EmpId opEmpId) {
        kpi.setResultInputEmpId(tempItem.getResultInputUserId());
        kpi.setResultInputEmpName(tempItem.getResultInputEmpName());
        if ("exam".equals(tempItem.getResultInputType())) {//如果是被考核人，也需要重新解析
            kpi.setResultInputEmpId(user.getEmpId());
            kpi.setResultInputEmpName(user.getEmpName());
            return;
        }
        if (!"manager".equals(tempItem.getResultInputType()) && !"role".equals(tempItem.getResultInputType())) {
            return;
        }
        List<Rater> raters = new ArrayList<>();
        if ("manager".equals(tempItem.getResultInputType())) {
            if (user.evalOrg()) {
                EvalAudit audit = new EvalAudit("manager", tempItem.getManagerLevel(), user.getCompanyId(), opEmpId, opEmpId, user.getEvalOrgId());
                raters = audit.parseAtOrgMg();
            } else {
                EvalAudit audit = new EvalAudit("manager", tempItem.getManagerLevel(), user.getCompanyId(), new EmpId(user.getEmpId()), opEmpId);
                raters = audit.parseRaters();
            }
        } else if ("role".equals(tempItem.getResultInputType())) {
            List<ObjItem> rates = new ArrayList<>();
            for (ObjItem objItem : tempItem.getInputRole()) {
                EvalAudit audit = new EvalAudit("role", objItem.getObjId(), user.getCompanyId(),
                        new EmpId(user.getEmpId()), opEmpId, user.getEvalOrgId(), user.getOrgId());
                raters = audit.parseRaters();
                for (Rater rater : raters) {
                    ObjItem o = new ObjItem(rater.getEmpId(), rater.getEmpName(), objItem.getObjId(), objItem.getObjName());
                    rates.add(o);
                }
            }
            kpi.setInputRole(rates);
        }
        if (CollUtil.isEmpty(raters)) {
            return;
        }
        StringBuilder inputEmpIdStr = new StringBuilder();
        StringBuilder inputEmpNameStr = new StringBuilder();
        for (Rater rater : raters) {
            inputEmpIdStr.append(rater.getEmpId()).append(",");
            inputEmpNameStr.append(rater.getEmpName()).append(",");
        }
        kpi.setResultInputEmpId(inputEmpIdStr.substring(0, inputEmpIdStr.length() - 1));
        kpi.setResultInputEmpName(inputEmpNameStr.substring(0, inputEmpNameStr.length() - 1));
    }

    public void parseEvaluate(String companyId, EvalUser user, EvalGroupEmp evalGroupEmp, String opEmpId) {

        PerfTemplEvaluate evaluate = evalGroupEmp.getEvaluate();

        if (Objects.nonNull(evaluate)) {
            {
                RaterNodeConf s3SelfRater = new RaterNodeConf();
                if (Boolean.TRUE.toString().equals(evaluate.getSelfScoreFlag())) {
                    s3SelfRater.setOpen(1);
                    s3SelfRater.setRateMode(evaluate.getSelfScoreRule());
                    s3SelfRater.setNodeWeight(evaluate.getSelfScoreWeight());
                    //s3SelfRater.setAnonymous();周期上的配置
                } else {
                    s3SelfRater.setOpen(0);
                }
                s3SelfRater.setSignatureFlag(JSONUtil.toBean(evaluate.getSignatureConf(), ScoreSign.class).isSelf());
                evalGroupEmp.setS3SelfRater(s3SelfRater);
            }

            //评分环节s3PeerRater
            {
                MutualNodeConf s3PeerRater = buildPeerNode(evaluate, user,new EmpId(opEmpId));
                s3PeerRater.setSignatureFlag(JSONUtil.toBean(evaluate.getSignatureConf(), ScoreSign.class).isPeer());
                evalGroupEmp.setS3PeerRater(s3PeerRater);
            }

            //s3SubRater
            {
                MutualNodeConf s3SubRater = buildSubNode(evaluate, user, new EmpId(opEmpId));
                s3SubRater.setSignatureFlag(JSONUtil.toBean(evaluate.getSignatureConf(), ScoreSign.class).isSub());
                evalGroupEmp.setS3SubRater(s3SubRater);
            }

            //s3SuperRater
            {
                S3SuperRaterConf s3SuperRater = new S3SuperRaterConf();
                s3SuperRater.setSuperiorScoreOrder(evaluate.getSuperiorScoreOrder());
                if (Boolean.TRUE.toString().equals(evaluate.getSuperiorScoreFlag())) {
                    s3SuperRater.setOpen(1);
                    List<PerfTemplEvaluateAudit> supRates = evaluate.getSuperiorScoreList();
                    s3SuperRater.setRateMode(evaluate.getSuperiorScoreRule());
                    s3SuperRater.setNodeWeight(evaluate.getSuperiorScoreWeight());
                    s3SuperRater.setNodeVacancyFlag(evaluate.getNodeVacancyFlag());
                    s3SuperRater.setAuditNodes(PerfTemplEvaluateAudit.skpiRepeatSuperScore(new TenantId(companyId),user.getEmpId(),user.getEvalOrgId(),user.getOrgId(),new EmpId(opEmpId),
                            AuditEnum.SUPERIOR_SCORE.getScene(),evaluate.getNodeVacancyFlag(),supRates));
                } else {
                    s3SuperRater.setOpen(0);
                }
                s3SuperRater.setSignatureFlag(JSONUtil.toBean(evaluate.getSignatureConf(), ScoreSign.class).isSuperior());
                evalGroupEmp.setS3SuperRater(s3SuperRater);
            }
            //s3AppointRater
            {
                S3RaterBaseConf s3RaterBaseConf = new S3RaterBaseConf();
                if (Boolean.TRUE.toString().equals(evaluate.getAppointScoreFlag())) {
                    s3RaterBaseConf.setOpen(1);
                    List<PerfTemplEvaluateAudit> appointRates = evaluate.getAppointScoreList();
                    s3RaterBaseConf.setAuditNodes(PerfTemplEvaluateAudit.skpiRepeatSuperScore(new TenantId(companyId),user.getEmpId(),user.getEvalOrgId(),user.getOrgId(),new EmpId(opEmpId),
                            AuditEnum.SUPERIOR_SCORE.getScene(),evaluate.getNodeVacancyFlag(),appointRates));
                    s3RaterBaseConf.setNodeVacancyFlag(evaluate.getNodeVacancyFlag());
                    s3RaterBaseConf.setNodeWeight(evaluate.getAppointScoreWeight());
                } else {
                    s3RaterBaseConf.setOpen(0);
                }
                s3RaterBaseConf.setSignatureFlag(JSONUtil.toBean(evaluate.getSignatureConf(), ScoreSign.class).isAppoint());
                evalGroupEmp.setS3AppointRater(s3RaterBaseConf);
            }
        }
    }
}
