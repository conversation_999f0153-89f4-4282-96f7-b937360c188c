package com.polaris.kpi.report.domain.entity;

import com.alibaba.fastjson.JSONArray;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class ReportWeightSetting implements Serializable {
    private String id;
    private String companyId;
    private Integer year;
    private String cycleType;
    private Integer month;
    private BigDecimal weight;
    private String isDeleted;
    private String createdUser;
    private Date createdTime;
    private String updatedUser;
    private Date updatedTime;

    public static List<ReportWeightSetting> initDefualtMonth(String year, String cycleType, String companyId, String createdUser) {
        List<ReportWeightSetting> weightSettingModels = JSONArray.parseArray("[{\"month\":1,\"weight\":8.33},{\"month\":2,\"weight\":8.33},{\"month\":3,\"weight\":8.33},{\"month\":4,\"weight\":8.33},{\"month\":5,\"weight\":8.33},{\"month\":6,\"weight\":8.33},{\"month\":7,\"weight\":8.33},{\"month\":8,\"weight\":8.33},{\"month\":9,\"weight\":8.33},{\"month\":10,\"weight\":8.33},{\"month\":11,\"weight\":8.33},{\"month\":12,\"weight\":8.37}]", ReportWeightSetting.class);
        weightSettingModels.forEach(l -> {
            l.setYear(Integer.parseInt(year));
            l.setCycleType(cycleType);
            l.setCompanyId(companyId);
            l.setCreatedUser(createdUser);
        });
        return weightSettingModels;
    }

    public static List<ReportWeightSetting> initDefualtQuarter(String year, String cycleType, String companyId, String empId) {
        List<ReportWeightSetting> weightSettingModels = JSONArray.parseArray("[{\"month\":1,\"weight\":25},{\"month\":2,\"weight\":25},{\"month\":3,\"weight\":25},{\"month\":4,\"weight\":25}]", ReportWeightSetting.class);
        weightSettingModels.forEach(l -> {
            l.setYear(Integer.parseInt(year));
            l.setCycleType(cycleType);
            l.setCompanyId(companyId);
            l.setCreatedUser(empId);
        });
        return weightSettingModels;
    }
}
