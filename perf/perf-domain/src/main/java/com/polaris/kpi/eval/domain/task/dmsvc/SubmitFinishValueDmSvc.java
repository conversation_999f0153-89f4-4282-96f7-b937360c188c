package com.polaris.kpi.eval.domain.task.dmsvc;

import cn.com.polaris.kpi.company.MsgSceneEnum;
import cn.com.polaris.kpi.eval.FinishValue;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.entity.empeval.ExcelFinishValue;
import com.polaris.kpi.eval.domain.task.entity.empeval.FinishValueReqValidationSvc;
import com.polaris.kpi.eval.domain.task.entity.empeval.KpiListWrap;
import com.polaris.kpi.eval.domain.task.event.KpiItemUpdateFinishedValueEvent;
import com.polaris.kpi.eval.domain.task.event.RejectFinishValueItemSubmitEvent;
import com.polaris.kpi.eval.domain.task.event.ThisStageEnded;
import com.polaris.kpi.eval.domain.task.event.msg.CancelTodoEvent;
import com.polaris.kpi.eval.domain.task.repo.AdminTaskRepo;
import com.polaris.kpi.eval.domain.task.repo.TaskKpiRepo;
import com.polaris.kpi.eval.domain.task.repo.TaskUserRepo;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.org.domain.dept.entity.CompanyConf;
import com.polaris.kpi.org.domain.emp.type.Emp;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.domain.task.dmsvc
 * @Author: suxiaoqiu
 * @Version: 2.0
 */
@Slf4j
public class SubmitFinishValueDmSvc extends  SaveFinishValueDmSvc{
    @Getter
    private EmpEvalMerge empEvalRule;
    @Getter
    private List<CancelTodoEvent> cancelTodoEvents = new ArrayList<>();
    public void accRule(EmpEvalMerge empEvalRule) {
        this.empEvalRule = empEvalRule;
    }

    public SubmitFinishValueDmSvc(CompanyConf conf, EvalUser evalUser, TenantId tenantId, EmpId opEmpId,
                                  AdminTask adminTask, Emp emp) {
        super(conf, evalUser, tenantId, opEmpId, adminTask, emp);
    }


    public boolean doComputed() {
        boolean computed = evalUser.tryComputeAutoScore(empEvalRule.getTypeWeightConf().isOpen(),
                empEvalRule.getScoreValueConf().getCustomFullScore(), empEvalRule.getScoreValueConf().isFullScoreRange(), empEvalRule.openItemAutoScoreMultiplWeight());
        if (computed) {
            SumScorerComputeDmSvc computeDmSvc = new SumScorerComputeDmSvc(evalUser, empEvalRule);
            computeDmSvc.computeSumScore();//提交完成值后需计算总分，环节总分，维度总分，指标总分
//            FinalWeightSumScore weightSumScore = empEvalRule.computeFinalScore(evalUser.getFinalItemAutoScore(), evalUser.isOpenAvgWeightCompute());
//            evalUser.computeLevel(weightSumScore, empEvalRule.getFinalScore(), empEvalRule.needComputeLevel());
        }
        return computed;
    }


    public void curentStageEndAndScorePass() {
        //环节结束事件
        new ThisStageEnded(empEvalRule, evalUser, TalentStatus.SCORING).publish();
        cancelTodoEvents.add(new CancelTodoEvent(tenantId, evalUser.getId(), MsgSceneEnum.TASK_SUBMIT_AUTO_ITEM_PROGRESS.getType()));
    }

    public void asyncSubmitFinishValueEvent(List<EvalKpi> rejectEvalKpis, EmpId opEmpIde) {
        if (CollUtil.isNotEmpty(rejectEvalKpis)) {
            //所有的驳回指标提交才进完成值审核
            if(isAllRejectKpiSubmited(rejectEvalKpis)) {
                new RejectFinishValueItemSubmitEvent(tenantId.getId(), evalUser.getId(), evalUser, opEmpIde, rejectEvalKpis).fire();
            }
           //取消驳回待办
            new CancelTodoEvent(tenantId, evalUser.getId(), Collections.singletonList(opEmpIde.getId()), Collections.singleton(MsgSceneEnum.REJECT_FINISH_VALUE.getType())).fire();
        } else {
            //version 保存一致    Integer opFinishValueType = 1;//提交
            new KpiItemUpdateFinishedValueEvent(tenantId, evalUser, opEmpIde, 1).fire();//tijiao
        }
    }

    public boolean isAllRejectKpiSubmited(List<EvalKpi> rejectEvalKpis) {
        return rejectEvalKpis.stream().allMatch(EvalKpi::isFinalSubmit);
    }

    public boolean checkFinishValueNoHavReq(List<FinishValue> finishValues) {
        // 校验维度和指标上的完成值必填
        FinishValueReqValidationSvc finishValueReqValidationSvc = new FinishValueReqValidationSvc(this.evalUser.getKpiTypes());
        if (!finishValueReqValidationSvc.allItemsCheckPassed(finishValues)) {
            log.error("存在完成值必填项未录入！taskUserId为:{}", this.evalUser.getId());
            return true;
        }
        return false;
    }

    public void cancelOtherToDo(List<String> submitItems) {
        /**可能存在多个录入人，如果其他录入人已提交需清理*/
        List<String> resultInputEmpIds = empEvalRule.getCurItemAllSubmitInputEmpIds(opEmpId.getId(), submitItems);
        log.info("录入人指标未全部提交仅更新,不进行后续操作【需特殊处理同一個指标多个录入人，其他人需要撤销待办log】,resultInputEmpIds:{}", resultInputEmpIds);
        if (CollUtil.isEmpty(resultInputEmpIds)) {
            return;
        }
        //特殊场景取消代办
        new CancelTodoEvent(tenantId, resultInputEmpIds, evalUser.getId(), MsgSceneEnum.TASK_SUBMIT_PROGRESS.getType()).publish();
    }

    public boolean checkIsCancelToDo() {
        // //校验该录入人指标是否已全部提交,如果未全部提交仅更新，不进行后续操作【需特殊处理同一個指标多个录入人，其他人需要撤销待办】
        return TalentStatus.CONFIRMED.getStatus().equals(evalUser.getTaskStatus()) && Objects.nonNull(opEmpId) && evalUser.getOpEmpExistsItemNotSubmitFinishValue();
    }

    public void appendCancelMsg(EmpId newOpEmpId) {
        /**可能存在多个录入人，清除待办的时候，统一清除*/
        List<String> resultInputEmpIds = empEvalRule.getResultInputEmpIds(opEmpId.getId());
        //取消代办
        cancelTodoEvents.add(new CancelTodoEvent(tenantId, resultInputEmpIds, evalUser.getId(), MsgSceneEnum.TASK_SUBMIT_PROGRESS.getType()));
        if (Objects.isNull(newOpEmpId)) {
            cancelTodoEvents.add(new CancelTodoEvent(tenantId, evalUser.getId(), MsgSceneEnum.TASK_SUBMIT_AUTO_ITEM_PROGRESS.getType()));
            return;
        }
        cancelTodoEvents.add(new CancelTodoEvent(tenantId, resultInputEmpIds, evalUser.getId(), MsgSceneEnum.TASK_SUBMIT_AUTO_ITEM_PROGRESS.getType()));
    }
}
