package com.polaris.kpi.migration.repo;

import com.polaris.kpi.migration.entity.TableSelectInfo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.zip.ZipOutputStream;

/**
 * 钉钉旧应用导出数据
 */
public interface IDingMigrationExportRepo {

    /**
     * 查出所需表SQL的查询语句
     * @return 查询语句
     */
    List<TableSelectInfo>  getTableSelectStatements(String companyId);

    /**
     * 从数据库导出数据[单个表]
     * @return 读取数据
     */
    boolean processTableDataWithStats(TableSelectInfo tableInfo, ZipOutputStream zipOut);

}