package com.polaris.kpi.setting.domain.entity;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.common.DelableDomain;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

/** {
 "id":"",
 "companyId":"公司id",
 "taskId":"考核任务id",
 "level":"层级",
 "auditEmpId":"审批人",
 "totalCnt":"处理总人数",
 "finishCnt":"已处理人数",
 "isDeleted":"",
 "createdUser":"创建用户",
 "createdTime":"创建时间",
 "updatedUser":"修改用户",
 "updatedTime":"修改时间",
 "version":"版本号",
 } ***/
@Setter
@Getter
@NoArgsConstructor
@Slf4j
public class TaskResultAuditSummary extends DelableDomain {

    private String id;//
    @JSONField(serialize = false)
    private TenantId companyId;//公司id
    private String taskId;//考核任务id
    private Integer level;//层级
    private String auditEmpId;//审批人
    private Integer totalCnt;//处理总人数
    private Integer finishCnt;//已处理人数
    private Integer status;     //0:待处理  1:已处理
    private String auditStatus;  //wait:待校准 doing:校准中 finished:已完成
    private List<ResultAuditFlowNodeRater> raters;

    public TaskResultAuditSummary( String companyId, String taskId, Integer level, String auditEmpId, Integer totalCnt, Integer finishCnt) {
        this.companyId = new TenantId(companyId);
        this.taskId = taskId;
        this.level = level;
        this.auditEmpId = auditEmpId;
        this.totalCnt = totalCnt;
        this.finishCnt = finishCnt;
    }

    public void mark(String adminEmpId,Integer level) {
        if (Objects.equals(this.auditEmpId,adminEmpId) && Objects.equals(this.level,level)) {
            this.finishCnt = this.finishCnt + 1;
        }
    }

    public boolean needFinish() {
        log.info(" this.totalCnt {} ,finishCnt {} ,status={},auditEmpId={},level={}", totalCnt, finishCnt, status, auditEmpId, level);
        return this.totalCnt <= finishCnt && (Objects.equals(this.status,0) || Objects.isNull(this.status));
    }

    public void markAuditStatus(List<EvalScoreResult> resultList) {
        if (CollUtil.isEmpty(resultList)) {
            this.auditStatus = "wait";
            return;
        }
        if (resultList.size() == this.totalCnt) {
            if (resultList.stream().allMatch(re -> StrUtil.isBlank(re.getAuditStatus()))) {
                this.auditStatus = "doing";
                return;
            }
            if (resultList.stream().allMatch(re -> re.isPassed())) {
                this.auditStatus = "finished";
                return;
            }
        }
        this.auditStatus = "wait";
    }

    public boolean waitOp() {
        return Objects.equals(this.auditStatus,"doing");
    }
}
