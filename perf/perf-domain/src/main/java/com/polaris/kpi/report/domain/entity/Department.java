package com.polaris.kpi.report.domain.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/8 11:05
 */
@Data
public class Department {

    private String id;
    private String name;
    private String parent_id;
    private List<Department> children;

    public CycleOrgSnap buildCycleOrgSnap(String companyId, String cycleId) {

        CycleOrgSnap cycleOrgSnap = new CycleOrgSnap();
        cycleOrgSnap.setCycleId(cycleId);
        cycleOrgSnap.setCompanyId(companyId);
        cycleOrgSnap.setOrgId(this.id);
        cycleOrgSnap.setOrgName(this.name);
        cycleOrgSnap.setParentOrgId(this.parent_id);
        return cycleOrgSnap;

    }
}
