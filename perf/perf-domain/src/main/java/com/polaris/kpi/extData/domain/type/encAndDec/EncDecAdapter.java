package com.polaris.kpi.extData.domain.type.encAndDec;


/**
 * @Author: xuxw
 * @Date: 2025/03/14 11:25
 * @Description:
 */
public abstract class EncDecAdapter implements EncDec{

    @Override
    public String enc(String plaintext){
        return "";
    }

    @Override
    public String enc(String plaintext, String secretKey){
        return "";
    }

    @Override
    public String dec(String ciphertext){
        return "";
    }

    public String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) hexString.append('0');
            hexString.append(hex);
        }
        return hexString.toString();
    }
}
