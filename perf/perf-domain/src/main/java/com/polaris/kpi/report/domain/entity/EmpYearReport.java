package com.polaris.kpi.report.domain.entity;

import com.polaris.kpi.common.DelableDomain;
import com.polaris.sdk.type.TenantId;

import java.util.Date;

public class EmpYearReport extends DelableDomain {
    private String id;//
    private Integer year;//年份
    private Integer status;//0：执行中  1：已完成
    private TenantId companyId;//

    public EmpYearReport() {
    }

    public EmpYearReport(TenantId companyId, Integer year, Integer status, String createUser) {
        this.companyId = companyId;
        this.year = year;
        this.status = status;
        this.createdUser = createUser;
        this.updatedUser = createUser;
        Date now = new Date();
        this.createdTime = now;
        this.updatedTime = now;
    }

    public TenantId getCompanyId() {
        return companyId;
    }

    public void setCompanyId(TenantId companyId) {
        this.companyId = companyId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public void finished() {
        this.status = 1;
    }
}
