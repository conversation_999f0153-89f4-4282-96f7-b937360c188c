package com.polaris.kpi.report.domain.entity;

import com.polaris.kpi.common.DelableDomain;
import com.polaris.kpi.common.Domain;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/2/8 17:09
 */
@Data
public class CycleOrgSnap extends Domain implements Serializable {

    private String id;
    private String companyId;
    private String cycleId;
    private String orgId;
    private String orgName;
    private String parentOrgId;

    public void initOnNew(String id) {
        this.id = id;
        this.createdTime = new Date();
        this.createdUser = "";
    }

}
