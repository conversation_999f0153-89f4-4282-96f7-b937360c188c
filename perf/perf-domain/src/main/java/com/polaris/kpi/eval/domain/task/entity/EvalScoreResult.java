package com.polaris.kpi.eval.domain.task.entity;

import cn.com.polaris.kpi.eval.IndexCalibration;
import cn.com.polaris.kpi.eval.MergeRsInfo;
import cn.com.polaris.kpi.eval.Pecent;
import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.perf.www.common.constant.BusinessConstant;
import com.polaris.kpi.eval.domain.task.entity.grade.Level;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * {
 * "id":"",
 * "companyId":"公司id",
 * "taskId":"考核任务id",
 * "orgId":"部门id",
 * "empId":"被考核人id",
 * "kpiTypeId":"指标类id",
 * "kpiItemId":"指标项id",
 * "scorerType":"评分人类型（自评/同级互评/下级互评/上级评/指定评分人）",
 * "scorerId":"评分人id",
 * "score":"评分分数",
 * "scoreWeight":"评分权重",
 * "scoreComment":"评分评语",
 * "scoreAttUrl":"评分附件url",
 * "reviewersType":"会签还是或签",
 * "auditStatus":"审核状态",
 * "finalScore":"没算阶段权重的分数",
 * "transferId":"转交来源id",
 * "approvalOrder":"审批顺序",
 * "createdUser":"创建用户",
 * "createdTime":"创建时间",
 * "updatedUser":"修改用户",
 * "updatedTime":"修改时间",
 * "plusScore":"加分",
 * "subtractScore":"减分",
 * "finalPlusScore":"加分加权计算得分",
 * "finalSubtractScore":"减分加权计算后得分",
 * "finalWeightScore":"最终加权后的分数",
 * "finalWeightPlusScore":"最终加权后的加分总分",
 * "finalWeightSubtractScore":"最终加权后的减分总分",
 * "scoreLevel":"打分人提交的绩效等级",
 * "noItemScore":"没算指标权重的分数",
 * "empScore":"评分人加权后的分数",
 * "isDeleted":"",
 * "taskAuditId":"审核节点id",
 * "modifyFlag":"是否可以修改",
 * "version":"版本号",
 * }
 ***/
@NoArgsConstructor
@Slf4j
@Getter
@Setter
public class EvalScoreResult extends BaseTypeScoreResult {

    private String taskId;//考核任务id
    private String orgId;//部门id
    private String empId;//被考核人id
    private String kpiTypeId;//指标类id
    private String kpiItemId;//指标项id
    private BigDecimal score;//评分分数
    private BigDecimal finalScore;//没算阶段权重的分数

    private BigDecimal plusScore;//加分
    private BigDecimal subtractScore;//减分
    private BigDecimal finalPlusScore;//加分加权计算得分
    private BigDecimal finalSubtractScore;//减分加权计算后得分
    private BigDecimal finalWeightScore;//最终加权后的分数
    private BigDecimal finalWeightPlusScore;//最终加权后的加分总分
    private BigDecimal finalWeightSubtractScore;//最终加权后的减分总分
    private BigDecimal noItemScore;//没算指标权重的分数
    private BigDecimal empScore;//评分人加权后的分数
    private String taskAuditId;//审核节点id
    private String modifyFlag;//是否可以修改
    private Integer version = 0;
    //private BigDecimal mergeWeight = BigDecimal.ZERO;//合并的权重 (结束评分功能要用)
    @JSONField(serialize = false, deserialize = false)
    List<MergeRsInfo> mergeRsInfos = new ArrayList<>();//结束评分功能要用
    @JSONField(serialize = false, deserialize = false)
    private BigDecimal waitMergeWeight = BigDecimal.ZERO;
    /**
     * 2023-03-20新增字段
     */
    private Integer calibrationType;        //校准类型（1：按人员校准 2：按层级校准  3：按指标校准）
    private String operateReason;           //校准内容
    private List<IndexCalibration> indexCalibration;        //指标校准时，用于存放json字符串
    private Integer isCommited; // 是否已经评过分 1是 0否
    private String perfCoefficient;//绩效系数
    private String vetoFlag;    //true为否决，false为不否决
    private boolean maySkip = false;    //是否可以跳过
    private String signatureUrl;        //签名
    private Integer skipType;       //0:异常  1:空缺跳过  2:重复跳过
    private Level scoreOption;        //评分的指标选项

    private Integer scoreTypeNode;      //环节顺序

    public EvalScoreResult(TenantId companyId, String taskId,
                           String orgId, String empId, String createdUser) {
        //this.id = UUID.randomUUID().toString();
        this.companyId = companyId;
        this.taskId = taskId;
        this.orgId = orgId;
        this.empId = empId;
        this.isDeleted = Boolean.FALSE.toString();
        this.createdUser = createdUser;
    }

    public EvalScoreResult(TenantId companyId, String taskId, String taskUserId,
                           String orgId, String empId, String scorerId, Integer approvalOrder) {
        this.companyId = companyId;
        this.taskId = taskId;
        this.taskUserId = taskUserId;
        this.orgId = orgId;
        this.empId = empId;
        this.scorerId = scorerId;
        this.approvalOrder = approvalOrder;
    }

    @JSONField(serialize = false, deserialize = false)
    private String scorerName;//评分人

    public EvalScoreResult(List<EvalScoreResult> scorers) {
        if (CollUtil.isNotEmpty(scorers)) {
            this.score = scorers.get(0).getScore();
        }
    }
    @JSONField(serialize = false, deserialize = false)
    private BigDecimal totalSplitWeight;//打总分时拆分的占比

    public void appendAuditor(String scene, String scorerId, String multiOrAndMode,
                              Integer approvalOrder, String auditId, String modifyFlag) {
        this.setScorerType(scene);
        this.setScorerId(scorerId);
        this.setReviewersType(multiOrAndMode);
        this.setApprovalOrder(approvalOrder);
        this.setTaskAuditId(auditId);
        this.setModifyFlag(modifyFlag);
    }

    public void appendItemId(String kpiItemId, String kpiTypeId) {
        this.setKpiItemId(kpiItemId);
        this.setKpiTypeId(kpiTypeId);
    }

    public boolean hasSubtractScore() {
        return this.subtractScore != null;
    }

    @JSONField(serialize = false)
    public boolean isPassed() {
        return BusinessConstant.PASS.equals(auditStatus) || BusinessConstant.TRANSFERRED.equals(auditStatus)
                || BusinessConstant.VACANCY_APPROVER_TYPE_SKIP.equals(auditStatus);
    }

    @JSONField(serialize = false)
    public boolean isWaitDispatch() {
        return StrUtil.equals("waitDispatch", this.auditStatus) || StrUtil.equals("wait", this.auditStatus);
    }

    public String asScoreKey() {
        return String.format("%s_%s_%s", kpiItemId, scorerType, scorerId);
    }

    public String asScoreNodeKey() {
        return String.format("%s_%s", taskAuditId, scorerId);
    }

    public String asOrderAndScoreTypeKey() {
        return String.format("%s_%s",scorerType,approvalOrder == null ? 1 : approvalOrder);
    }

    public String asOrderScoreKey() {
        return String.format("%s_%s_%s_%s", kpiItemId, scorerType, scorerId, approvalOrder == null ? 1 : approvalOrder);
    }

    public String asSubScoreKey() {
        return String.format("%s_%s_%s_%s", kpiItemId, scorerType, scorerId, taskAuditId);
    }

    public String scorerTypeKey() {
        return String.format("%s_%s_%s", kpiItemId, scorerType, approvalOrder);
    }

    public String asKpiItemKey() {
        return String.format("%s&%s", kpiItemId, kpiTypeId);
    }


    public void submitSrcScore(EvalScoreResult otherSubmit, String opEmpId) {
        this.setScoreComment(otherSubmit.getScoreComment());
        this.signatureUrl = otherSubmit.getSignatureUrl();
//        if (CollUtil.isNotEmpty(otherSubmit.getScoreAttUrl())) {//提交分数时不设定成空
//            this.scoreAttUrl = otherSubmit.getScoreAttUrl();
//        }
        this.setScoreLevel(otherSubmit.getScoreLevel());
        this.setUpdatedUser(opEmpId);
        //this.setAuditStatus(BusinessConstant.PASS);
        this.score = otherSubmit.submitedScore();
        this.plusScore = otherSubmit.getPlusScore();
        this.subtractScore = otherSubmit.getSubtractScore();
        passed();
    }

    //同一结点多人情况下的, 评分人之间的权重
    public BigDecimal percentRaterWeight() {
        return scoreWeight == null ? BigDecimal.ONE : scoreWeight.divide(Pecent.ONE_HUNDRED);
    }

    /**
     * @description: 类*指*人*环
     * @author: lufei
     * @date: 2022/5/16 14:32
     * @param: [typePecWgt, itemPecWgt, totalPecWeight]
     * @return: void
     **/
    public void computeFinalScore(BigDecimal typePecWgt, BigDecimal itemPecWgt, BigDecimal nodePecWeight) {
        if (score == null) {
            return;
        }
        BigDecimal raterWeight = percentRaterWeight();
        BigDecimal initScore = score == null ? BigDecimal.ZERO : score;
        finalScore = initScore.multiply(typePecWgt).multiply(itemPecWgt).multiply(raterWeight).setScale(5, RoundingMode.DOWN).setScale(4, RoundingMode.HALF_UP);
        finalWeightScore = finalScore.multiply(nodePecWeight).setScale(5, RoundingMode.DOWN).setScale(4, RoundingMode.HALF_UP);
        //以下旧逻辑中间计算值,确认是用来显示评分详情的
        empScore = initScore.multiply(raterWeight).setScale(5, RoundingMode.DOWN).setScale(4, RoundingMode.HALF_UP);
        noItemScore = empScore.multiply(nodePecWeight).setScale(5, RoundingMode.DOWN).setScale(4, RoundingMode.HALF_UP);
    }

    public void computePlusSubScore(BigDecimal nodePecWeight) {
        this.subtractScore = BigDecimal.ZERO;
        this.finalSubtractScore = BigDecimal.ZERO;
        this.finalWeightSubtractScore = BigDecimal.ZERO;
        this.plusScore = BigDecimal.ZERO;
        this.finalPlusScore = BigDecimal.ZERO;
        this.finalWeightPlusScore = BigDecimal.ZERO;
        if (Objects.isNull(this.score)) {
            this.score = BigDecimal.ZERO;
        }
        if (score.compareTo(BigDecimal.ZERO) < 0) {
            this.subtractScore = score;
            finalSubtractScore = raterWeightScore(subtractScore);
            finalWeightSubtractScore = finalWeightScore(subtractScore, nodePecWeight);
        } else {
            this.plusScore = score;
            finalPlusScore = raterWeightScore(plusScore);
            finalWeightPlusScore = finalWeightScore(plusScore, nodePecWeight);
        }
    }

    public static void main(String[] args) {
        EvalScoreResult result = new EvalScoreResult();
        result.setScore(new BigDecimal(88));
        result.computePlusSubScore(new BigDecimal("0.3"));
        System.out.println(result.getFinalSubtractScore());

        EvalScoreResult result2 = new EvalScoreResult();
        result2.setScore(new BigDecimal(68));
        result2.computePlusSubScore(new BigDecimal("0.6"));
        System.out.println(result2.getFinalSubtractScore());

        EvalScoreResult result3 = new EvalScoreResult();
        result3.setScore(new BigDecimal(68));
        result3.computePlusSubScore(new BigDecimal("0.6"));
        System.out.println(result3.getFinalSubtractScore());

        System.out.println(result.getFinalSubtractScore().add(result2.getFinalSubtractScore()).add(result3.getFinalSubtractScore()));
    }

    public void computePlusScore(BigDecimal nodePecWeight) {
        //this.plusScore = plusScore == null ? score : plusScore;//传参数旧的传的:plusScore,建议传score
        if (plusScore == null) {
            return;
        }
        finalPlusScore = raterWeightScore(plusScore);
        finalWeightPlusScore = finalWeightScore(plusScore, nodePecWeight);
    }

    //taskBiz.4721
    public void computeSubtractScore(BigDecimal nodePecWeight) {
        //this.subtractScore = subtractScore == null ? score : subtractScore;
        if (subtractScore == null) {
            return;
        }
        finalSubtractScore = raterWeightScore(subtractScore);
        finalWeightSubtractScore = finalWeightScore(subtractScore, nodePecWeight);
    }

    private BigDecimal raterWeightScore(BigDecimal raterScore) {
        BigDecimal initScore = raterScore == null ? BigDecimal.ZERO : raterScore;
        BigDecimal raterWeight = percentRaterWeight();
        return initScore.multiply(raterWeight).setScale(5, RoundingMode.DOWN).setScale(4, RoundingMode.HALF_UP);
    }

    private BigDecimal finalWeightScore(BigDecimal raterScore, BigDecimal nodePecWeight) {
        BigDecimal raterWeightScore = raterWeightScore(raterScore);
        return raterWeightScore.multiply(nodePecWeight).setScale(5, RoundingMode.DOWN).setScale(4, RoundingMode.HALF_UP);
    }

    public void splitTotalScore(BigDecimal totalScore) {
        this.score = totalScore;
    }

    public boolean isActive() {
        return this.score != null;
    }

    public void approvalOrderIfNull(int order) {
        if (approvalOrder == null) {
            this.approvalOrder = order;
        }
    }

    public void addMergeWeight(BigDecimal addWgt, String auditId, String scorerId) {
        scoreWeight = scoreWeight.add(addWgt);
        mergeRsInfos.add(new MergeRsInfo(auditId, scorerId));
        //if (approvalOrder == null || this.approvalOrder >= approvalOrder) {
        //    return;
        //}
        //this.approvalOrder = approvalOrder;
    }

    @JSONField(serialize = false)
    public boolean isSelfNode() {
        return SubScoreNodeEnum.isSelfScore(scorerType);
    }

    @JSONField(serialize = false)
    public boolean isAppointNode() {
        return SubScoreNodeEnum.isAppointScore(scorerType);
    }

    @JSONField(serialize = false)
    public boolean isItemNode() {
        return SubScoreNodeEnum.isItemScore(scorerType);
    }

    @JSONField(serialize = false)
    public boolean isManualNode() {
        return SubScoreNodeEnum.isManualNode(scorerType);
    }

    @JSONField(serialize = false)
    public boolean isSuperiorNode() {
        return SubScoreNodeEnum.isSuperioNode(scorerType);
    }

    @JSONField(serialize = false)
    public boolean isOrVisa() {
        return Objects.equals(this.reviewersType, "or");
    }

    @JSONField(serialize = false)
    public boolean isScorerNode() {
        return isSelfNode() || isAppointNode() || isItemNode() || isManualNode() || isSuperiorNode();
    }
    public void passed(BigDecimal score, String scoreLevel) {
        this.auditStatus = BusinessConstant.PASS;
        this.score = score;
        this.scoreLevel = scoreLevel;
    }

    public void doPassed(Boolean skipPass) {
        this.auditStatus = skipPass ? "skip" : BusinessConstant.PASS;
    }

    public void passed() {
        this.auditStatus = BusinessConstant.PASS;
        this.updatedTime = new Date();
        this.updatedUser = scorerId;
    }

    public void skipped() {
        this.auditStatus = "skip";
    }

    public void submitExtScore(BigDecimal plusScore, BigDecimal subtractScore) {
        this.plusScore = plusScore;
        this.subtractScore = subtractScore;

    }

    public BigDecimal submitedScore() {
        if (this.plusScore != null) {
            return plusScore;
        }
        if (this.subtractScore != null) {
            return subtractScore;
        }
        return this.score;
    }

    public boolean isDeleted() {
        return Boolean.TRUE.toString().equals(isDeleted);
    }

    public boolean isNotDeleted() {
        return Boolean.FALSE.toString().equals(isDeleted);
    }

    public boolean notPlusItemOrSubItem() {
        return plusScore == null && subtractScore == null;
    }

    public boolean matchOrder(EmpId opEmpId, Integer submitOrder) {
        if (submitOrder == null) {
            return scorerId.equals(opEmpId.getId());
        }
        return submitOrder.equals(approvalOrder) && scorerId.equals(opEmpId.getId());
    }

    public void accOp(TenantId tenantId, EmpId opEmpId, String orgId) {
        this.companyId = tenantId;
        this.updatedUser = opEmpId.getId();
        this.orgId = orgId;
    }

    public void transfer(String id, String scorerId) {
        this.transferId = id;
        this.scorerId = scorerId;
    }

    public void upScore(EvalScoreResult submited) {
        this.score = submited.score;
        this.finalScore = submited.finalScore;
        this.finalWeightScore = submited.finalWeightScore;
        this.empScore = submited.empScore;
        this.noItemScore = submited.noItemScore;

        this.plusScore = submited.plusScore;
        this.finalPlusScore = submited.finalPlusScore;
        this.finalWeightPlusScore = submited.finalWeightPlusScore;

        this.subtractScore = submited.subtractScore;
        this.finalSubtractScore = submited.finalSubtractScore;
        this.finalWeightSubtractScore = submited.finalWeightSubtractScore;
        this.passed();
        submited.passed();
    }

    public BigDecimal getPlusScore() {
        return plusScore;
    }

    public BigDecimal fWgtScoreNullZero() {
        return nullZero(finalWeightScore);
    }

    public BigDecimal fPlusScoreNullZero() {
        return nullZero(finalWeightPlusScore);
    }

    public BigDecimal fSubtractScoreNullZero() {
//        BigDecimal subScore = nullZero(finalWeightSubtractScore);
//        //减分如果是负数转成正数用于计算
//        subScore = subScore.abs().setScale(2, BigDecimal.ROUND_HALF_UP);
        return nullZero(finalWeightSubtractScore);
    }

    private BigDecimal nullZero(BigDecimal value) {
        return value == null ? BigDecimal.ZERO : value;
    }

    public boolean mergedWeight() {
        return CollUtil.isNotEmpty(mergeRsInfos);
    }

    @JSONField(serialize = false, deserialize = false)
    public List<String> getMergeRsAudits() {
        return mergeRsInfos.stream().map(info -> info.getAuditId()).collect(Collectors.toList());
    }

    public BigDecimal matchScore() {
        if (Objects.nonNull(empScore)) {
            return empScore;
        }
        return (Objects.isNull(finalPlusScore) ? BigDecimal.ZERO : finalPlusScore)
                .add(Objects.isNull(finalSubtractScore) ? BigDecimal.ZERO : finalSubtractScore);
//        if (Objects.nonNull(finalPlusScore)) {
//            return finalPlusScore;
//        }
//        if (Objects.nonNull(finalSubtractScore)) {
//            return finalSubtractScore;
//        }
//        return null;
    }

    public void reComputeFinalPlusScore(ScorerRateNode node) {
        if (null == plusScore || node.getSumOfMaxRate().compareTo(BigDecimal.ONE) == 0) {
            return;
        }

        if (node.wasSumOfMaxRateZero()) {
            this.finalWeightPlusScore = BigDecimal.ZERO;
            return;
        }
        BigDecimal weight = isSelfNode() ? node.getWeight() : null == scoreWeight ? node.getWeight() : scoreWeight;
        BigDecimal maxRate = plusScore.divide(node.getSumOfMaxRate(), 4, RoundingMode.HALF_UP);
        BigDecimal reComputeScore = maxRate.multiply(weight.divide(Pecent.ONE_HUNDRED, 4, RoundingMode.HALF_UP)).setScale(4, RoundingMode.HALF_UP);
        this.finalWeightPlusScore = reComputeScore;
    }

    public void reComputeFinalSubScore(ScorerRateNode node) {
        if (null == subtractScore || node.getSumOfMaxRate().compareTo(BigDecimal.ONE) == 0) {
            return;
        }
        if(node.wasSumOfMaxRateZero()){
            this.finalWeightSubtractScore = BigDecimal.ZERO;
            return;
        }
        BigDecimal weight = isSelfNode() ? node.getWeight() : null == scoreWeight ? node.getWeight() : scoreWeight;
        BigDecimal maxRate = subtractScore.divide(node.getSumOfMaxRate(), 4, RoundingMode.HALF_UP);
        BigDecimal reComputeScore = maxRate.multiply(weight.divide(Pecent.ONE_HUNDRED, 4, RoundingMode.HALF_UP)).setScale(5, RoundingMode.DOWN).setScale(4, RoundingMode.HALF_UP);
        this.finalWeightSubtractScore = reComputeScore;
    }

    //分拆成按指标打分.
    public EvalScoreResult totalMockAsItemScore(BigDecimal sumWght, BigDecimal itemPecWeight, boolean submitWithWeight) {
        EvalScoreResult result = new EvalScoreResult();
        result.setScoreComment(this.getScoreComment());
        if (CollUtil.isNotEmpty(this.getScoreAttUrl())) {//提交分数时不设定成空
            this.scoreAttUrl = this.getScoreAttUrl();
        }
        this.setScoreLevel(this.getScoreLevel());
        this.setUpdatedUser(this.scorerId);
        if (submitWithWeight) {
            result.score = this.score;
        } else {
            result.score = this.score;
        }
        return result;
    }


    /**
     * @param defaultCustomScoreConfig  普通指标默认评分分值
     * @param defaultPlusSubScoreConfig 加减分默认评分分值
     * @param isPlusSubScoreType        是否加分/减分/加减分
     * @return
     */
    public void markSkip(List<String> defaultCustomScoreConfig,
                         List<String> defaultPlusSubScoreConfig,
                         boolean isPlusSubScoreType) {

        //普通指标
        if (!isPlusSubScoreType) {
            if (defaultCustomScoreConfig.contains(this.scorerType)) {
                this.maySkip = true;
            }
            return;
        }
        //加减分指标
        if (defaultPlusSubScoreConfig.contains(this.scorerType)) {
            this.maySkip = true;
        }
    }

    public void accSkipRater(TenantId companyId, String taskUserI,String scorerId,String scorerName,String scorerType,Integer approveOrder) {
        this.companyId = companyId;
        this.taskUserId = taskUserI;
        this.scorerId = scorerId;
        this.scorerName = scorerName;
        this.scorerType = scorerType;
        this.approvalOrder = approveOrder;
    }
}
