package com.polaris.kpi.eval.domain.confirm.repo;

import com.polaris.kpi.eval.domain.confirm.entity.ConfirmNode;
import com.polaris.kpi.eval.domain.confirm.entity.ConfirmTaskFlow;
import com.polaris.kpi.eval.domain.task.entity.EvalAudit;
import com.polaris.kpi.eval.domain.task.entity.PerfModificationRecord;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.AffirmTaskConf;
import com.polaris.sdk.type.TenantId;

import java.util.List;

public interface ConfirmFlowRepo {
    ConfirmTaskFlow getConfirmFlow(AffirmTaskConf confirmTask, TenantId companyId, String taskUserId);

//    EmpEval getEmpEval(TenantId companyId, String taskUserId);

    void savePassedNode(ConfirmNode node, PerfModificationRecord record);
    void saveOrUpdateAuditIndFlow(List<EvalAudit> auditIndFlow, AffirmTaskConf confirmTask);

    void saveDispatchedNode(ConfirmNode node);

    void saveDispatchedNodes(TenantId companyId, String taskUserId, List<ConfirmNode> skipAllNodes);
}
