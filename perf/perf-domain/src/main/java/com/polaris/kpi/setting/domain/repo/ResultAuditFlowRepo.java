package com.polaris.kpi.setting.domain.repo;

import cn.com.polaris.kpi.eval.EvalReviewers;
import cn.com.polaris.kpi.eval.ResultAuditReviewers;
import com.polaris.kpi.eval.domain.task.entity.EvalAudit;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.setting.domain.entity.ResultAuditFlow;
import com.polaris.kpi.setting.domain.entity.ResultAuditFlowUser;
import com.polaris.kpi.setting.domain.entity.TaskResultAuditSummary;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;

import java.util.List;
import java.util.Map;


public interface ResultAuditFlowRepo {

    void saveAuditFlow(ResultAuditFlow flow, EvalUser evalUser);

    void fixSaveAuditFlow(ResultAuditFlow flow, EvalUser evalUser);

    void refreshAuditFlow(ResultAuditFlow flow, EvalUser evalUser);

    void refreshAuditFlow(String companyId,String opEmpId, List<EvalUser> evalUsers);

    void saveAuditFlowV2(EvalUser evalUser,List<ResultAuditFlowUser> flowUsers);

    void updateAuditFlow(ResultAuditFlow flow,Integer node);

    void updateOldAuditFlow(ResultAuditFlow flow);

    void deleteAllFlow(String companyId,List<String> taskUserId);

    void deleteAllFlowV2(String companyId, String taskId);

    void rejectAuditFlow(List<String> taskUserIds,Integer orderNode);

    ResultAuditFlow getResultAuditFlow(String companyId,String taskId,
                                              List<String> clearTaskUserIds,
                                              boolean isRefresh,
                                              List<EvalScoreResult> curLevelRs);


    ResultAuditFlow getResultAuditFlowV2(String companyId,String taskId,
                                       List<String> clearTaskUserIds);

    ResultAuditFlow getResultAuditFlowByUserId(String companyId,String taskUserId);

    List<ResultAuditFlowUser> listFlowUserByTaskId(String companyId,String taskId);

    TaskResultAuditSummary getSummary(String companyId,String taskId,String adminEmpId,Integer level);

    List<TaskResultAuditSummary> listSummaryByTaskId(String companyId,String taskId);

    List<TaskResultAuditSummary> listSummaryByTaskId(String companyId,String taskId,String auditEmpId);

    void saveTaskResultAuditSummary(List<TaskResultAuditSummary> taskResultAuditSummarys);

    int updateSummary(TaskResultAuditSummary auditSummary);

    void delTaskResultAuditSummary(String companyId,String taskId );

    void removeResultAuditFlow(String companyId,String taskId, String taskUserId);

    void refreshSummary(String companyId,String taskId);

    void rejectRefreshSummary(String companyId,String taskId);

    ListWrap<ResultAuditReviewers> reviewersListWrap(TenantId companyId, List<String> taskUserIds);

    Map<String,TaskResultAuditSummary> listByEmpIdSummaryAsMap(String companyId, List<String> taskIds, String adminEmpId);

    void addResultSummary(String companyId, EvalAudit evalAudit);

    void updateAuditNodeRater(String companyId, String nodeRaterId, String auditEmpId, Integer level, Integer status);
}
