package com.polaris.kpi.eval.domain.group.entity;

import com.polaris.kpi.common.DelableDomain;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.BaseAuditNode;
import com.polaris.kpi.eval.domain.temp.entity.PerfTemplEvaluate;
import com.polaris.kpi.eval.domain.temp.entity.std.StdTemp;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.ibatis.annotations.JsonAryColumn;
import org.apache.ibatis.annotations.JsonColumn;

import java.util.ArrayList;
import java.util.List;

/**
 * 考核关系组
 * <AUTHOR> yeyeyeoye
 * @date 2025/7/4 10:06
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class EvalGroup extends DelableDomain {
    private String id;
    private String groupName;
    private String templBaseId;
    private String templIsDeleted;
    private String companyId;
    private List<EvalGroupAdmin> evalGroupAdmins;
    private List<EvalGroupEmp> evalGroupEmps;
    private int groupNum;
    private String templName;
    private String evaluateType;//评分流程类型 ： simple-简易流程，360-360°考核流程，custom-自定义流程
    @JsonColumn
    private PerfTemplEvaluate evaluate;
    @JsonAryColumn(BaseAuditNode.class)
    private List<BaseAuditNode> auditResultFlowConf;

    public EvalGroup(String id, String groupName, String templBaseId, String companyId) {
        this.id = id;
        this.groupName = groupName;
        this.templBaseId = templBaseId;
        this.companyId = companyId;
        this.evalGroupAdmins = new ArrayList<>();
        this.evalGroupEmps = new ArrayList<>();
    }

    public EvalGroup(String id, String templBaseId, String groupName ,List<BaseAuditNode> auditResultFlowConf , String companyId) {
        this.id = id;
        this.templBaseId = templBaseId;
        this.groupName = groupName;
        this.auditResultFlowConf = auditResultFlowConf;
        this.companyId = companyId;
    }

    public void addAdmin(EvalGroupAdmin evalGroupAdmin) {
        this.evalGroupAdmins.add(evalGroupAdmin);
    }

    public void addAdmins(List<EvalGroupAdmin> admins) {
        this.evalGroupAdmins.addAll(admins);
    }

    public void addMembers(List<EvalGroupEmp> members) {
        this.evalGroupEmps.addAll(members);
    }

    public void removeMembers(List<String> ids) {
        this.evalGroupEmps.removeIf(e -> ids.contains(e.getId()));
    }

    public void markDeleted() {
        this.isDeleted = Boolean.TRUE.toString();
    }

    public void accAuditResultFlowConf(List<BaseAuditNode> auditResultAuditFlowConf){
        this.auditResultFlowConf = auditResultAuditFlowConf;
    }

    public void accTemplInfo(StdTemp stdTemp) {
        this.templName = stdTemp.getName();
        this.evaluateType = stdTemp.getEvaluateType();
        this.evaluate = stdTemp.getEvaluate();
    }
}
