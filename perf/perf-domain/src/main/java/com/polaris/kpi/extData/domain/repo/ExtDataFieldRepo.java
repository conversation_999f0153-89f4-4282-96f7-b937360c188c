package com.polaris.kpi.extData.domain.repo;

import com.polaris.kpi.extData.domain.entity.ExtDataField;
import com.polaris.kpi.extData.domain.entity.ExtDataFieldParam;

import java.util.List;

public interface ExtDataFieldRepo {
    String addDateField(ExtDataField field);
    void addDateFieldParam(List<ExtDataFieldParam> params, String fieldId);

    void updateDateField(ExtDataField extDataField);

    void updateDateFieldParam(List<ExtDataFieldParam> params);

    void delDateField(String id);

    void delDateFieldParam(String id);

    void delDateFieldParamByIds(List<String> ids);
}
