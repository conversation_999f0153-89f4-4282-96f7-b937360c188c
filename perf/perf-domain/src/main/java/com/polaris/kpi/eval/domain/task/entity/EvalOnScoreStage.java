package com.polaris.kpi.eval.domain.task.entity;

import cn.com.polaris.kpi.company.MsgSceneEnum;
import cn.com.polaris.kpi.eval.FinishValue;
import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.perf.www.common.em.OperationLogSceneEnum;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.empeval.*;
import com.polaris.kpi.org.domain.dept.entity.CompanyMsgCenter;
import com.polaris.sdk.type.ListWrap;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 评分阶段考核对象
 */
@Slf4j
public class EvalOnScoreStage {

    @Getter
    private final EvalUser evalUser;
    @Getter
    private final EmpEvalMerge empEval;
    @Getter
    private final AdminTask adminTask;
    @Getter
    private final Map<String,CompanyMsgCenter> centerMap = new HashMap<>(); //当前被考核人当前未处理的所有评分人待办，需要时，需过滤
    @Getter
    private  ListWrap<CompanyMsgCenter> centerWrap = new ListWrap<>(); //当前被考核人当前未处理的所有评分人待办，需要时，需过滤
    @Getter
    private final Map<String, EmpEvalKpiType> kpiTypeMap = new HashMap<>();
    @Getter
    private final Map<String,EvalKpi> kpiMap = new HashMap<>();
    @Getter
    private final List<EvalKpi> updatedKpis = new ArrayList<>();
    @Getter
    private final List<EmpEvalKpiType> updatedKpiTypes = new ArrayList<>();//仅修改维度
    @Getter
    private final List<OperationLog> opLogs = new ArrayList<>();
    @Getter
    private final List<EmpEvalScorer> scorers = new ArrayList<>();
    @Getter
    private final List<CompanyMsgCenter> cancelCenters = new ArrayList<>();//需取消本地系统的待办
    @Getter
    private final List<CompanyMsgCenter> needCancelRemoteMsg = new ArrayList<>();//根据情况取消远程的待办 cancelCenters包含了 cancelRemateCenters
    @Getter
    private List<V3SubmitedEvalNodeScore> nodeScores = new ArrayList<>();//提交评分人提交的环节
    @Getter
    private final List<String> msgScenes = new ArrayList<>();//提交评分人待办场景
    @Getter
    private final Set<String> scorerIds = new HashSet<>();//评分人
    @Getter
    private boolean haveSubmitTotalLevelNode;//存在提交打总等级节点

    public EvalOnScoreStage(EvalUser evalUser, EmpEvalMerge empEval, AdminTask adminTask) {
        this.evalUser = evalUser;
        this.empEval = empEval;
        this.adminTask = adminTask;
        this.empEval.initTypeWeight();//归一化类别权重
        this.empEval.getKpiTypes().getDatas().forEach(type -> {
            this.kpiTypeMap.put(type.getKpiTypeId(), type);
            type.getItems().forEach(kpi -> this.kpiMap.put(kpi.asKpiItemKey(), kpi));  //kpiMap
        });
    }

    public EvalOnScoreStage(EvalUser evalUser, EmpEvalMerge empEval, AdminTask adminTask, List<CompanyMsgCenter> centers) {
        this.evalUser = evalUser;
        this.empEval = empEval;
        this.adminTask = adminTask;
        this.empEval.initTypeWeight();//归一化类别权重
        if (Objects.nonNull(centers)) {
            centers.forEach(center -> {
                this.centerMap.put(center.getEmpId() + "_" + center.getBusinessScene(), center);
            });
            this.centerWrap = new ListWrap<>(centers).groupBy(CompanyMsgCenter::getBusinessScene);
        }
        this.empEval.getKpiTypes().getDatas().forEach(type -> {
            this.kpiTypeMap.put(type.getKpiTypeId(), type);
            type.getItems().forEach(kpi -> this.kpiMap.put(kpi.asKpiItemKey(), kpi));  //kpiMap
        });

    }

    public boolean checkReSubmited(String opEmpId, List<V3SubmitedEvalNodeScore> nodeScores) {
        //加载评分人评分环节,校验是否已提交
        EvalScorersWrap scorers = this.empEval.getEvalScorersWrap();
        if (Objects.isNull(scorers)) {
            //throw new KpiI18NException("msg.task.empeval.submitItemScoreFlowChange", "该被考核的评价流程已发生变化，暂无法提交");//评分流程发生变化
            return true;
        }
        EmpEvalScorer scorer = scorers.getCurEvalScorer(opEmpId);
        if (Objects.isNull(scorer)) {
            //throw new KpiI18NException("msg.task.empeval.submitItemScoreFlowChange", "该被考核的评价流程已发生变化，暂无法提交");//评分流程发生变化
            return true;
        }
        for (V3SubmitedEvalNodeScore nodeScore : nodeScores) {
            if (nodeScore.getWasTransferRater()){
                continue;
            }
            List<EmpEvalScorerNode>  curNodes = scorer.getCurScoreNode(nodeScore.getScorerType(), nodeScore.getApprovalOrder());
            if (isAllFinish(curNodes)) {
                return true;
            }
        }
        return false;
    }

    private boolean isAllFinish(List<EmpEvalScorerNode>  curNodes ) {
        //    throw new KpiI18NException("msg.task.empeval.reSubmitScore", "该被考核的评价已被他人完成了");//已或签,或者提交过
        return CollUtil.isEmpty(curNodes) || curNodes.stream().allMatch(EmpEvalScorerNode::isFinish);
    }

    public void saveScore(String opEmpId, List<V3SubmitedEvalNodeScore> nodeScores) {
        EvalScorersWrap scorersWrap = this.empEval.getEvalScorersWrap();
        EmpEvalScorer submitScorer = scorersWrap.submitScoreNs(opEmpId, nodeScores, evalUser, empEval, kpiTypeMap);//提交评分人环节
        nodeScores.forEach(nodeScore -> {
            log.info(" 单个环节saveScore ，taskUserId为:{},scoreType:{}", nodeScore.getTaskUserId(),nodeScore.getScorerType());
            this.handleSubmitLevel(nodeScore);//提交维度等级
            this.addLog(opEmpId, nodeScore);//拼接日志
            this.addCancelMsg(opEmpId, nodeScore); //取消当前评分人的代办
            this.submitTotalLevel(nodeScore);//提交总等级
        });
        this.scorers.add(submitScorer);
        this.nodeScores = nodeScores;//作为参数传递后续流程需要用到提交的数据
        this.collectMsg();//收集需远程处理的待办
        this.upReviewers();
    }


    private void submitTotalLevel(V3SubmitedEvalNodeScore nodeScore){
        if (!SubScoreNodeEnum.isTotalLevelScore(nodeScore.getScorerType()) || nodeScore.getWasTransferRater()){
            return;
        }
        log.info("提交总等级：stepId:{},scoreLevel:{}",nodeScore.getStepId(),nodeScore.getScoreLevel());
        this.evalUser.submitScoreLevel(nodeScore.getStepId(),nodeScore.getScoreLevel());//提交总等级
        this.haveSubmitTotalLevelNode = true;
    }

    private void collectMsg() {
        this.collectMsgSubmitProgress();//提交完成值待办
        this.collectMsgSelf();//处理自评的待办[ //单独处理下自评，自评需要关闭钉钉待办]
    }
    private void collectMsgSelf() {
        if (!submitSelfScore()) {
            return;
        }
        //取消当前评分人的代办
        String msgScene = empEval.isCustom() ? MsgSceneEnum.TASK_ALL_SCORE.getType() : MsgSceneEnum.TASK_SELF_SCORE.getType();
        log.info(" 单个任务，清除自评待办： addMsgSelf ，msgScene:{}", msgScene);
        this.msgScenes.add(msgScene);
        CompanyMsgCenter center =  this.getCenterFromMap(this.evalUser.getEmpId(), msgScene);
        if (Objects.nonNull(center)) {
            this.cancelCenters.add(center);
            this.needCancelRemoteMsg.add(center);
        }
    }

    private void collectMsgSubmitProgress() {
        if (centerWrap.groupGet(MsgSceneEnum.TASK_SUBMIT_PROGRESS.getType()) == null) {
            return;
        }
        log.info(" 单个任务： 提交完成值，清除待办");
        //取消完成值录入待办
        List<CompanyMsgCenter> progressCenters = centerWrap.groupGet(MsgSceneEnum.TASK_SUBMIT_PROGRESS.getType());
        //获取 empid
        this.scorerIds.addAll(progressCenters.stream().map(CompanyMsgCenter::getEmpId).collect(Collectors.toList()));
        this.cancelCenters.addAll(progressCenters);
        this.needCancelRemoteMsg.addAll(progressCenters);
    }

    private boolean submitSelfScore() {
        return this.nodeScores.stream()
                .anyMatch(node -> SubScoreNodeEnum.isSelfScore(node.getScorerType()));
    }
    public boolean selfScoreSkip() {
        return this.nodeScores.size() ==1 && this.nodeScores.stream()
                .anyMatch(cmd -> SubScoreNodeEnum.isSelfScore(cmd.getScorerType()));
    }

    public void accpScoreIds(List<String> scorerIds) {
        this.scorerIds.addAll(scorerIds);
    }

    public void accpCancelMsg(List<CompanyMsgCenter> cancelCenters) {
        this.cancelCenters.addAll(cancelCenters);
    }

    private void handleSubmitLevel(V3SubmitedEvalNodeScore nodeScore) {
        //如果是非计算型的节点直接退出提交, 无需更新维度及指标数据，仅更新节点状态
        if (!nodeScore.isComputeNode() || nodeScore.getWasTransferRater()){
            return;
        }
        Map<String, EmpEvalKpiType> upTypeMap = new HashMap<>(); //打等级需要提交更新的
        Map<String, EvalKpi> upKpiMap = new HashMap<>();//打等级需要提交更新的
        nodeScore.submitLevel(upTypeMap, upKpiMap, kpiTypeMap);//提交等级
        if (upTypeMap.size() > 0) {
            updatedKpiTypes.addAll(upTypeMap.values());
        }
        if (upKpiMap.size() > 0) {
            updatedKpis.addAll(upKpiMap.values());
        }
    }

    private void addCancelMsg(String opEmpId, V3SubmitedEvalNodeScore nodeScore) {
        if (nodeScore.getWasTransferRater()) {//转交提交的评分人无需清楚待办
            return;
        }
        //取消当前评分人的代办
        String msgScene = empEval.isCustom() ? MsgSceneEnum.TASK_ALL_SCORE.getType() : nodeScore.getNodeEnum().todoScenes();
        if (SubScoreNodeEnum.isTotalLevelScore(nodeScore.getScorerType())){
            msgScene = MsgSceneEnum.TASK_ALL_SCORE.getType();//评价总等级
        }
        this.msgScenes.add(msgScene);
        this.scorerIds.add(opEmpId);
        CompanyMsgCenter center = this.getCenterFromMap(opEmpId, msgScene);
        if (Objects.nonNull(center)) {
            this.cancelCenters.add(center);
        }
        if (StrUtil.equals(msgScene, MsgSceneEnum.TASK_SELF_SCORE.getType())) {
            CompanyMsgCenter selfCenter = this.getCenterFromMap(evalUser.getEmpId(), msgScene);
            if (Objects.nonNull(center)) {
                this.scorerIds.add(evalUser.getEmpId());
                this.cancelCenters.add(selfCenter);//如果当前待办是自评，需增加当前被考核人作为评分人，因为跳过责任人会丢失，待办也需要清除
            }
        }
    }

    private void upReviewers() {
        if (CollUtil.isNotEmpty(scorerIds)) {
            List<String> scorerIds = new ArrayList<>(this.scorerIds);
            this.evalUser.removeReviewers(scorerIds);  //更新责任人
        }
    }

    private CompanyMsgCenter getCenterFromMap(String empId, String msgScene) {
        if (empId == null || msgScene == null) {
            return null;
        }
        return this.centerMap.get(empId + "_" + msgScene);
    }

    private void addLog(String opEmpId, V3SubmitedEvalNodeScore submitNode) {
        if (!submitNode.getWasAddLog()) {
            return;
        }

        String scorerType = submitNode.getScorerType();
        String scoreType = submitNode.isSystemSkip() ? "auto_skip_score" : scorerType;
        scoreType = submitNode.isSelfSkip() ? "skip_self_score" : scoreType;//如果是自评跳过标记self_skip_score 场景
        String logScene = submitNode.isReCommit() ? OperationLogSceneEnum.MODIFY_SCORE.getScene() : scoreType;
        String descStr = "";
        JSONObject desc = new JSONObject();
        desc.put("scorerType", scorerType);
        if (SubScoreNodeEnum.fromStr(scorerType) == SubScoreNodeEnum.ITEM_SCORE) {
            desc.put("itemNames", submitNode.itemLogNames(kpiMap));
        }
        descStr = desc.toJSONString();
        if (SubScoreNodeEnum.isTotalLevelScore(scorerType)){
            descStr = OperationLogSceneEnum.TOTAL_LEVEL.getDesc();
        }
        OperationLog operationLog = new OperationLog(this.evalUser.getCompanyId().getId(), this.evalUser.getId(), logScene, opEmpId, descStr);
        operationLog.setSignatureUrl(submitNode.getSignatureUrl());
        this.opLogs.add(operationLog);
    }

    public List<CompanyMsgCenter> getCancelMsgCenters(boolean isCancelAll) {
        List<CompanyMsgCenter> cancelCenters = new ArrayList<>();
        if (isCancelAll) {
            cancelCenters.addAll(this.cancelCenters); //如果清除所有待办，则使用CancelCenters
        } else {
            cancelCenters.addAll(this.needCancelRemoteMsg);//只清除需要清除的
        }
        return cancelCenters;
    }
}
