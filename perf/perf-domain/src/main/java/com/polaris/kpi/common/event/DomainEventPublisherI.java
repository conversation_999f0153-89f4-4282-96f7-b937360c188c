//package com.polaris.kpi.common.event;
//
//import com.alibaba.cola.event.DomainEventI;
//import com.alibaba.cola.event.EventI;
//import com.alibaba.cola.event.EventStoreI;
//import com.polaris.sdk.type.TenantId;
//
///**
// * <AUTHOR> lufei
// * @date 2021/7/8 5:22 下午
// */
//public interface DomainEventPublisherI extends EventStoreI {
//
//    void publish(DomainEventI domainEvent);
//
//    String nextEventId();
//
//    <T> T getEvent(TenantId tenantId, String eventId, Class<T> domainType);
//
//    void error(EventI eventI);
//}
