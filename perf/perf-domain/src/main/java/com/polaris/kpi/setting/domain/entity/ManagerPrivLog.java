package com.polaris.kpi.setting.domain.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
@NoArgsConstructor
public class ManagerPrivLog {
    private String id;
    @JSONField(serialize = false)
    private String companyId;
    private String operationType;  //add=新增 update=编辑 del=删除 open=开启 close=关闭
    // batchOpen=批量开启 batchClose=批量关闭 batchDel=批量删除 synEvent = 同步事件
    private String beforeValue;
    private String afterValue;
    private String operationEmpId;
    private String operationEmpType;  //操作人类型（main=主管理员  child=子管理员）
    private String operationDesc;   //
    private String createdUser;
    private Date createdTime;

    public ManagerPrivLog(String companyId, String operationType, String beforeValue, String afterValue,
                                 String operationEmpId, String operationEmpType, String operationDesc) {
        this.companyId = companyId;
        this.operationType = operationType;
        this.beforeValue = beforeValue;
        this.afterValue = afterValue;
        this.operationEmpId = operationEmpId;
        this.operationEmpType = operationEmpType;
        this.operationDesc = operationDesc;
        this.createdUser = operationEmpId;
        this.createdTime = new Date();
    }
}
