package com.polaris.kpi.report.domain.event;

import com.polaris.kpi.org.domain.common.BaseEvent;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class CreateYearReportEven extends BaseEvent {

    private String year;
    private List<String> orgIds;
    private List<String> roleIds;
    private String companyId;
    private String loginEmpId;
    private boolean isAvg = true;      //是否平均值
    private Integer showType = 3;           //显示类型 1分数  2：等级  3：全部
    private Integer performanceType = 1;        //绩效类型 1=个人绩效，2=组织绩效



    public void start() {

    }
}
