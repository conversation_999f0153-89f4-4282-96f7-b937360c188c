package com.polaris.kpi.setting.domain.entity;

import cn.com.polaris.kpi.ManagerScope;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.Objects;

@Setter
@Getter
@NoArgsConstructor
public class ManagerPriv {
    private String id;
    private String companyId;
    private String empId;  //管理人ID
    private String managerEmpName;
    private Integer managerType;  //管理类型 1=主管管理  2=直属主管 4=普通员工
    private ManagerScope privConf;      //权限配置
    private Integer status;         //0=关闭  1=开启
    private String isDeleted;
    private String createdUser;//创建用户
    private Date createdTime;//创建时间
    private String updatedUser;//修改用户
    private Date updatedTime;//修改时间
    private String adminType;

    public void accOp(String companyId,String opEmpId,String adminType) {
        this.companyId = companyId;
        this.status = 1;
        this.adminType = adminType;
        this.createdUser = opEmpId;
        this.createdTime = new Date();
        this.isDeleted = Boolean.FALSE.toString();
    }

    public void init(String companyId,String opEmpId) {
        this.companyId = companyId;
        this.empId = opEmpId;
        this.status = 1;
        this.managerType = 1;
        this.privConf = new ManagerScope();
        this.privConf.initManager();
        this.createdUser = opEmpId;
        this.createdTime = new Date();
        this.isDeleted = Boolean.FALSE.toString();
    }

    /**是否主管*/
    @JSONField(serialize = false)
    public boolean isManager() {
        return (this.managerType & 1) > 0;
    }

    @JSONField(serialize = false)
    public boolean differ(ManagerScope privConf) {
        return !Objects.equals(JSONObject.toJSONString(this.privConf),JSONObject.toJSONString(privConf));
    }

    @JSONField(serialize = false)
    public ManagerScope builderPrivConf(Integer managerType) {
        ManagerScope scope = new ManagerScope();
        //不属于主管,取消所属部门及下级部门权限设置
        this.managerType = managerType;
        if ((managerType & 1) == 0 ) {
            scope.accOp(false,this.getPrivConf().getIsSuperPriv(),this.getPrivConf().getAppointScope());
        }
        if ((managerType & 1) > 0) {
            scope.accOp(true,this.getPrivConf().getIsSuperPriv(),this.getPrivConf().getAppointScope());
        }
        return scope;
    }

}
