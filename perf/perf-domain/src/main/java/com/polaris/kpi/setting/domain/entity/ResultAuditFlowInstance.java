package com.polaris.kpi.setting.domain.entity;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.common.DelableDomain;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.BaseAuditNode;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.*;
import java.util.stream.Collectors;

@Setter
@Getter
@NoArgsConstructor
public class ResultAuditFlowInstance extends DelableDomain {
    private String id;
    private TenantId companyId;
    private String taskId;
    private String taskUserId;
    private String orgId;
    private String name;  //实例名称（2024年一月考核设计部）
    private String md5Key;
    private List<ResultAuditFlowNode> flowNodes;  //节点
    private List<ResultAuditFlowUser> flowUsers;  //实例下的人员

    private ResultAuditFlowNode curFlowNode;  //当前节点。
    private ResultAuditFlowNode dispatchFlowNode;  //需要分发的节点

    private List<ResultAuditFlowNode> skipedNodes;

    public ResultAuditFlowInstance(String id,String companyId, String taskId,String taskUserId, String orgId, String name,String md5Key,String createUser) {
        this.id = id;
        this.companyId = new TenantId(companyId);;
        this.taskId = taskId;
        this.taskUserId = taskUserId;
        this.orgId = orgId;
        this.name = name;
        this.md5Key = md5Key;
        this.createdUser = createUser;
        this.createdTime = new Date();
    }

    public void accop(String companyId, String opEmpId, String taskId,String taskUserId, String orgId, String name) {
        this.companyId = new TenantId(companyId);;
        this.taskId = taskId;
        this.taskUserId = taskUserId;
        this.orgId = orgId;
        this.name = name;
        this.createdUser = opEmpId;
        this.createdTime = new Date();
    }

    public void refreshStatus() {
        if (CollUtil.isEmpty(this.flowNodes)) {
            return;
        }
        for (ResultAuditFlowNode flowNode : this.flowNodes) {
            flowNode.refreshStatus(flowNode.getApprovalOrder());
        }
    }


    public boolean macth(String taskUserId) {
        if (CollUtil.isEmpty(this.flowUsers)) {
            return false;
        }
       return this.flowUsers.stream().anyMatch(u -> Objects.equals(u.getTaskUserId(),taskUserId));
    }

    public ResultAuditFlowNode macthCurNode(Integer level) {
        if (CollUtil.isEmpty(this.flowNodes)) {
            return null;
        }
        return this.flowNodes.stream().filter(u -> Objects.equals(u.getApprovalOrder(),level)).findFirst().get();
    }

    public void start(Integer nodeOrder) {
        Integer curOrder = this.trySkip(nodeOrder);
        this.dispatch(curOrder);
    }

    public void dispatch(Integer nodeOrder) {
        if (nodeOrder > this.flowNodes.size()) {
            return;
        }
        this.curFlowNode = this.flowNodes.get(nodeOrder - 1);
    }

    public Integer trySkip(Integer nodeOrder) {
        List<ResultAuditFlowNode> skipNodeList = new ArrayList<>();
        while (nodeOrder <= flowNodes.size()) {
            ResultAuditFlowNode flowNode = this.flowNodes.get(nodeOrder - 1);
            if (flowNode.needSkipNode()) {
                flowNode.markSkip();
                skipNodeList.add(flowNode);
            }else {
                break;
            }
            if (nodeOrder + 1 > this.flowNodes.size()) {
                break;
            }
            nodeOrder++;
        }
        this.skipedNodes = skipNodeList;
        return nodeOrder;
    }

    public ResultAuditFlowNode nextFlowNode() {
        return this.flowNodes.get(this.curFlowNode.getApprovalOrder() - 1);
    }
}
