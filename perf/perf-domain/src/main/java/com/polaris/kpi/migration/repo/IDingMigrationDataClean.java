package com.polaris.kpi.migration.repo;

import com.polaris.kpi.migration.entity.DingMigrationTask;

import java.util.List;

public interface IDingMigrationDataClean {
    void migrationDataClean(DingMigrationTask dingMigrationTask) ;
    void migrationDataCollback(DingMigrationTask task, List<String> allTableNames);
    void migrationDataCollbackTest(String oldCompanyId, List<String> allTableNames);
    void save(DingMigrationTask task);
    void saveMigrationCompany(DingMigrationTask task);
    void update(DingMigrationTask task);
    DingMigrationTask getDingMigrationTask(String dingCorpId);
    DingMigrationTask getDingMigrationTaskByCompanyId(String companyId);
}
