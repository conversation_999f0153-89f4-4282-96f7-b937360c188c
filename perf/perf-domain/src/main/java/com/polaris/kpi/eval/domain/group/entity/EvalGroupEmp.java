package com.polaris.kpi.eval.domain.group.entity;

import com.polaris.kpi.common.DelableDomain;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.*;
import com.polaris.kpi.eval.domain.temp.entity.PerfTemplEvaluate;
import lombok.Data;
import org.apache.ibatis.annotations.JsonAryColumn;
import org.apache.ibatis.annotations.JsonColumn;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> ye<PERSON>yeoy<PERSON>
 * @date 2025/7/4 11:58
 */
@Data
public class EvalGroupEmp extends DelableDomain {
    private String id;
    private String empId;
    private String orgId;
    private String empName;
    private String orgName;
    private String avatar;
    private String groupId;
    private Byte isResigned; // 是否离职 0-否 1-是
    @JsonAryColumn(BaseAuditNode.class)
    private List<BaseAuditNode> auditResultFlowConf;
    private RaterNodeConf s3SelfRater;              //360或简易 自评人
    private MutualNodeConf s3PeerRater;                   //360或简易 同级互评人
    private MutualNodeConf s3SubRater;                    //360下级
    private S3SuperRaterConf s3SuperRater;          //360或简易 上级人
    private S3RaterBaseConf s3AppointRater; //指定评
    @JsonColumn
    private PerfTemplEvaluate evaluate;//非自定义
    private boolean auditResultConfRefDefault = Boolean.TRUE; //是否引用默认配置
    private boolean scoreConfRefDefault = Boolean.TRUE; //是否引用默认配置


    public EvalGroupEmp() {
    }

    public EvalGroupEmp(String id, String empId, String orgId) {
        this.id = id;
        this.empId = empId;
        this.orgId = orgId;
    }

    public boolean customScoreFlowConf(){
        return Objects.nonNull(evaluate);
    }

    /**
     * 从评价组中获取流程配置
     */
    public void accGroupConfIfNotHave(EvalGroup evalGroup) {
        if (Objects.isNull(auditResultFlowConf)){
            this.auditResultFlowConf = evalGroup.getAuditResultFlowConf();
        }
    }
}
