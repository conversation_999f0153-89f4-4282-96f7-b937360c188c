package com.polaris.kpi.eval.domain.stage.dmsvc;

import cn.com.polaris.kpi.KpiEmp;
import cn.hutool.core.convert.Convert;
import com.polaris.kpi.eval.domain.stage.repo.AutoValidEmpNameFinder;
import com.polaris.kpi.eval.domain.stage.repo.ResetEmpNameFinder;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTaskDiff;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTaskOperation;
import com.polaris.kpi.eval.domain.task.repo.AdminTaskRepo;
import com.polaris.kpi.org.domain.dept.dmsvc.EmpParser;
import com.polaris.kpi.org.domain.emp.entity.KpiEmployee;

import java.util.List;

//阶段配置变更比较器, 可复用,可以根据日志生成变更报告
public class StageConfDiffLogDmSvc {
    private EmpParser empFinder;
    private AutoValidEmpNameFinder autoFinder;
    private ResetEmpNameFinder resetFinder;
    private AdminTaskOperation operationLog;

    public StageConfDiffLogDmSvc(EmpParser empFinder, AutoValidEmpNameFinder autoFinder, ResetEmpNameFinder resetFinder) {
        this.empFinder = empFinder;
        this.autoFinder = autoFinder;
        this.resetFinder = resetFinder;
    }

    public AdminTaskDiff diffAdminTaskOperation(String tenantId, String taskId, List<String> resetTaskUserIds, String changedStage,
                                                AdminTask before, AdminTask after, String opEmpId, String opAdminType) {
        String resetEmpName = resetFinder.getResetEmpName(tenantId, resetTaskUserIds);
        String autoValidEmpName = autoFinder.getAutoValidEmpName(tenantId, taskId, opEmpId, changedStage);
        AdminTaskDiff compare = new AdminTaskDiff(before, after);
        this.operationLog = compare.doCompareDiff(resetEmpName, autoValidEmpName);
        if (!operationLog.changed()) {
            return null;
        }
        KpiEmp emp = empFinder.getEmp(opEmpId);
        operationLog.operate(emp, opAdminType);
        return compare;
    }

    public void saveDiffLog(AdminTaskRepo logRepo) {
        if (!operationLog.changed()) {
            return;
        }
        logRepo.saveDiffLog(this.operationLog);
    }
}
