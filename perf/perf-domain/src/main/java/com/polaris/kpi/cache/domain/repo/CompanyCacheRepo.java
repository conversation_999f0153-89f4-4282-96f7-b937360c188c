package com.polaris.kpi.cache.domain.repo;

import com.polaris.kpi.cache.domain.entity.CompanyCacheInfo;

public interface CompanyCacheRepo {

     void saveCompanyCacheInfo(CompanyCacheInfo model);

    void updateCompanyCacheInfo(CompanyCacheInfo model);

    void updateCompanyCacheValue(CompanyCacheInfo model);

     void deleteCache(String companyId, String linkId, String businessScene, String key, String updatedUser);

     void deleteCache(String companyId, String linkId, String businessScene, String updatedUser);

     void deleteCache(String companyId, String linkId, String updatedUser);

}
