package com.polaris.kpi.eval.domain.task.dmsvc;

import cn.com.polaris.kpi.eval.Name;
import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.util.StrUtil;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.chain.ChainNode;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.chain.ScoreNode;
import com.polaris.kpi.eval.domain.task.event.SendTodoForInputOnScoreEvent;
import com.polaris.kpi.eval.domain.task.event.talent.scoring.ChainNodeEnd;
import com.polaris.kpi.eval.domain.task.event.talent.scoring.ScoreNodeOfChainEnd;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.domain.task.dmsvc
 * @Author: suxiaoqiu
 * @CreateTime: 2025-03-03  09:22
 * @Description: 评分人提交评分后流程变化领域服务
 * @Version: 2.0
 */
@Slf4j
@Getter
public class SubmitScoreFlowV3DmSvc {
    private final TenantId companyId;
    private final String opEmpId;
    private final EvalUser taskUser;// 评分任务用户
    private final EmpEvalMerge evalMerge;
    private final List<V3SubmitedEvalNodeScore> nodeScores;//当前提交的评分环节
    private final V3SubmitedEvalNodeScore submitOneNode;//提交的第一个环节
    private final List<V3SubmitedEvalNodeScore> notEndCmds = new ArrayList<>();

    public SubmitScoreFlowV3DmSvc(TenantId companyId, String opEmpId, EvalUser taskUser, EmpEvalMerge evalMerge, List<V3SubmitedEvalNodeScore> nodeScores) {
        this.taskUser = taskUser;
        this.evalMerge = evalMerge;
        this.nodeScores = nodeScores;
        this.companyId = companyId;
        this.opEmpId = opEmpId;
        this.submitOneNode = this.buildSubmitOneNode();//提交的第一个节点，可以用于触发流程跳转
    }

    private V3SubmitedEvalNodeScore buildSubmitOneNode() {
        if (nodeScores.size() == 1) {
            return nodeScores.get(0);
        }
        //多个节点的，需要排除总等级提交【合并提交的需要以其他环节作为流程流转】
        List<V3SubmitedEvalNodeScore> noContainTotalLevelNodes = nodeScores.stream()
                .filter(nodeScore -> !SubScoreNodeEnum.isTotalLevelScore(nodeScore.getScorerType())).collect(Collectors.toList());

        //这里是处理同一个人同时占互评和定向评两个环节，前端提交过来的顺序定向评可能在前面，提交定向评是不触发评分环节流程的，所以当定向评分和其他环节评分一起提交过来的时候，以其他环节的scorerType提交为准。
        if (noContainTotalLevelNodes.size() > 1 && StrUtil.equals(noContainTotalLevelNodes.get(0).getScorerType(), "item_score")) {
            return noContainTotalLevelNodes.get(1);
        }
        return noContainTotalLevelNodes.get(0);
    }

    public void flowNodeEnd() {
        this.existsSubFlowIsWaitEnd();
        if (this.evalMerge.inputOnScoring()) {
            ///**如果该企业开启了评分环节，自动评分发送录入完成待办*/
            SendTodoForInputOnScoreEvent scoreEvent = new SendTodoForInputOnScoreEvent(this.companyId, taskUser.getId(),
                    new Name(this.evalMerge.getTaskName()), this.evalMerge.sendInputMsg());
            scoreEvent.fire();
        }

        if (!notEndCmds.isEmpty()) {
            log.info("上级评分依次分发");
            for (V3SubmitedEvalNodeScore notEndCmd : notEndCmds) {
                log.info(" notEndCmds scoreNode:{},nodeOrder :{}", notEndCmd.getScorerType(), notEndCmd.getApprovalOrder());
                ScoreNode scoreNode = evalMerge.currentScoreNoe(notEndCmd.getNodeEnum(), notEndCmd.getApprovalOrder());
                ScoreNodeOfChainEnd chainEnd = new ScoreNodeOfChainEnd(evalMerge, taskUser, scoreNode);
                chainEnd.fire();
            }
            return;
        }

        //如果当前是打总等级的，this.submitOneNode.getNodeEnum()，需要校验是否还有其他环节待提交，不然不能作为主层级判断环节结束
        if (SubScoreNodeEnum.isTotalLevelScore(this.submitOneNode.getScorerType())) {
            if (!evalMerge.v3NodeIsAllFinishedNoContainTotalLevel()) {
                log.info("V3打总等级的，存在其他待提交[完成]环节，不能作为主层级判断环节结束，仅仅只是提交");
                return;
            }
        }
        //层级是否结束
        ChainNode current = evalMerge.current(this.submitOneNode.getNodeEnum(), this.submitOneNode.getApprovalOrder());
        if (evalMerge.chainNodeIsEndV3(this.submitOneNode.getNodeEnum(), this.submitOneNode.getApprovalOrder())) {
            log.info("flowChainNodeEnd:V3评分层级结束:node:{}", this.submitOneNode.getScorerType());
            //下一个主层级
            ChainNodeEnd scoreNodeEnd = new ChainNodeEnd(evalMerge, taskUser, current);
            scoreNodeEnd.fire();
        }

        // todo 需要咨询下这个还需要吗？ 我认为，新版本的是不是不需要了？
//        if (evalMerge.isAnyNotDispactched(current)) {//补偿一下, 历史数据中有未分发的环节再补分发一次
//            log.info("V3通过AUTO补偿一下分发,有未分发的环节再补分发一次 {}", taskUser.getId());
//            ChainNode auto = evalMerge.current(SubScoreNodeEnum.AUTO, 1);
//            ChainNodeEnd scoreNodeEnd = new ChainNodeEnd(evalMerge, taskUser, auto);
//            scoreNodeEnd.fire();
//        }
    }


    private void existsSubFlowIsWaitEnd() {
        this.nodeScores.stream().filter(nodeScore -> !evalMerge.subFlowIsEnd(nodeScore.getNodeEnum(), nodeScore.getApprovalOrder())).forEach(notEndCmds::add);
    }
}
