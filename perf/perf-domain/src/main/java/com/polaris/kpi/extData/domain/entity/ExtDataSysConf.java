package com.polaris.kpi.extData.domain.entity;

import com.polaris.kpi.extData.domain.type.RequestParamInfo;
import com.polaris.kpi.common.DelableDomain;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.annotations.Ckey;

/**
 * @Author: xuxw
 * @Date: 2025/03/06 10:53
 * @Description:
 */
@Setter
@Getter
public class ExtDataSysConf extends DelableDomain {
    @Ckey
    private String id;
    private String extDataSysId;
    private Integer sort;
    private Integer confType;
    private String reqUrl;
    private String reqType;
    private String reqParamEncMode;
    private String resParamType;
    private String resParam;
    private String resParamDecMode;
    private String reqHeaders;
    private String queryParams;
    // 请求body类型【1.form-data,2.json,3.raw-text】
    private Integer queryBodyType;
    private String queryBody;
    private String companyId;

    public RequestParamInfo buildParam(){
        RequestParamInfo paramInfo = new RequestParamInfo();
        paramInfo.setReqUrl(this.getReqUrl());
        paramInfo.setReqType(this.getReqType());
        paramInfo.setReqParamEncMode(this.getReqParamEncMode());
        paramInfo.setResParamType(this.getResParamType());
        paramInfo.setReqHeaders(this.getReqHeaders());
        paramInfo.setQueryParams(this.getQueryParams());
        paramInfo.setQueryBodyType(this.getQueryBodyType());
        paramInfo.setQueryBody(this.getQueryBody());
        paramInfo.setResParamDecMode(this.getResParamDecMode());
        return paramInfo;
    }
}
