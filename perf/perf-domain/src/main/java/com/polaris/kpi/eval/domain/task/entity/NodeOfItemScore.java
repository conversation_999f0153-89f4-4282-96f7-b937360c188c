package com.polaris.kpi.eval.domain.task.entity;

import cn.com.polaris.kpi.eval.ScoreAttUrl;
import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import com.polaris.kpi.eval.domain.task.entity.grade.Level;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.domain.task.entity
 * @Author: lufei
 * @CreateTime: 2022-05-22  15:36
 * @Description: TODO
 * @Version: 1.0
 */
@Setter
@Getter
public class NodeOfItemScore {
    private String node;
    private BigDecimal score;
    private String vetoFlag;            //否决标识(true为否决，false为不否决)
    private BigDecimal nodeWeight;      //环节权重
    private boolean displayScore;       //是否展示分数
    private List<ScoreResultOfItem> scoreResults = new ArrayList<>();

    public NodeOfItemScore(String node) {
        this.node = node;
    }

    public boolean scoreIsNotNull() {
        return score != null && score.compareTo(BigDecimal.ZERO) != 0;
    }
    public void addItem(ScoreResultOfItem ofItem) {
        scoreResults.add(ofItem);
    }

    public void addItem(ScoreResultOfItem ofItem, BigDecimal finalScore) {
        scoreResults.add(ofItem);
        this.score = addNullTo(score, finalScore);
        if (!Objects.equals(this.vetoFlag, "true")) {
            this.vetoFlag = ofItem.itemVetoFlag;
        }
    }

    public void addItemV3(ScoreResultOfItem ofItem,EvalKpi kpi) {
        scoreResults.add(ofItem);
        if (!Objects.equals(this.vetoFlag, "true")) {
            this.vetoFlag = ofItem.itemVetoFlag;
        }
        if (SubScoreNodeEnum.isSelfScore(node)) {
            this.score = kpi.getItemSelfScore();
        } else if (SubScoreNodeEnum.isSuperioNode(node)) {
            this.score = kpi.getItemSuperiorScore();
        } else if (SubScoreNodeEnum.isSubNode(node)) {
            this.score = kpi.getItemSubScore();
        } else if (SubScoreNodeEnum.isPeerNode(node)) {
            this.score = kpi.getItemPeerScore();
        } else if (SubScoreNodeEnum.isAppointScore(node)) {
            this.score = kpi.getItemAppointScore();
        }else if (SubScoreNodeEnum.isItemScore(node)) {
            this.score = kpi.getItemScore();
        }
    }
    public static BigDecimal addNullTo(BigDecimal sum, BigDecimal score) {
        if (sum == null && score == null) {
            return null;
        }
        sum = sum == null ? BigDecimal.ZERO : sum;
        return score == null ? sum : sum.add(score);
    }

    //添加环节权重
    public void addNodeWeight(String node,EvalItemScoreRule itemScoreRule ) {
        if (itemScoreRule == null){
            return;
        }
        if (Objects.equals(node, "self_score")) {
            this.nodeWeight = itemScoreRule.getSelfScoreWeight();
        } else if (Objects.equals(node, "superior_score")) {
            this.nodeWeight = itemScoreRule.getSuperiorScoreWeight();
        } else if (Objects.equals(node, "sub_score")) {
            this.nodeWeight = itemScoreRule.getSubScoreWeight();
        } else if (Objects.equals(node, "peer_score")) {
            this.nodeWeight = itemScoreRule.getPeerScoreWeight();
        } else if (Objects.equals(node, "appoint_score")) {
            this.nodeWeight = itemScoreRule.getAppointScoreWeight();
        }
    }


    public static class ScoreResultOfItem {
        public BigDecimal scoreWeight;
        public String level;
        public String scorer;
        public String avatar;
        public String scorerId;
        public String auditStatus;

        public BigDecimal plusScore;
        public BigDecimal subtractScore;
        public BigDecimal score;

        public String scoreComment;
        public List<ScoreAttUrl> scoreAttUrl;
        public Level scoreOption;        //评分的指标选项
        public String itemVetoFlag;            //否决标识(true为否决，false为不否决)
        public Date createdTime;
        public ScoreResultOfItem(String scorerName, String avatar, String scorerId,Date createdTime) {
            this.scorer = scorerName;
            this.avatar = avatar;
            this.scorerId = scorerId;
            this.createdTime = createdTime;

        }

        public ScoreResultOfItem(String scorerName, String avatar, String scorerId,String itemVetoFlag,Date createdTime) {
            this.scorer = scorerName;
            this.avatar = avatar;
            this.scorerId = scorerId;
            this.itemVetoFlag = itemVetoFlag;
            this.createdTime = createdTime;
        }

        public void acceptComment(List<ScoreAttUrl> scoreAttUrl, String scoreComment) {
            this.scoreAttUrl = scoreAttUrl;
            this.scoreComment = scoreComment;
        }
        public void acceptScoreOption(Level scoreOption) {
            this.scoreOption = scoreOption;
        }
        public void acceptScore(BigDecimal score, BigDecimal plusScore, BigDecimal subtractScore) {
            this.score = score;
            this.plusScore = plusScore;
            this.subtractScore = subtractScore;
        }

        public void acceptScore(BigDecimal score) {
            this.score = score; //加减分也是score
        }
        public void acceptVetoFlag(String itemVetoFlag) {
            this.itemVetoFlag = itemVetoFlag;
        }

    }
}
