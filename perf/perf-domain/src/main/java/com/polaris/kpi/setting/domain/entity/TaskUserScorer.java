package com.polaris.kpi.setting.domain.entity;

import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import com.polaris.kpi.common.DelableDomain;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/18 09:41
 */
@Setter
@Getter
@NoArgsConstructor
public class TaskUserScorer extends DelableDomain {

    private String id;
    private String companyId;
    private String cycleId;
    private String taskId;
    private String taskName;
    private String empId;
    private String empName;
    private String taskUserId;
//    private String summaryId; //汇总ID
    private String scoreScene; //评分环节
    private Integer scoreLevel; //评分环节层级
    private String scorerId; //评分人ID
    private Integer readyStatus; //就绪状态 1-未就绪 2-就绪
    private Integer scoreStatus; //评分状态 1-未评分 2-已评分
    private Integer companyMsgStatus; //系统待办状态 1-不存在 2-存在

    public TaskUserScorer(String companyId, String cycleId, String taskId, String taskUserId) {
        this.companyId = companyId;
        this.cycleId = cycleId;
        this.taskId = taskId;
        this.taskUserId = taskUserId;
    }

    public void initStatus(){
        this.readyStatus = 1;
        this.scoreStatus = 1;
        this.companyMsgStatus = 1;
    }

    public void initTransferStatus(){
        this.readyStatus = 2;
        this.scoreStatus = 1;
        this.companyMsgStatus = 1;
    }

    public void populateTaskUserInfo(EvalUser taskUser){
        this.companyId = taskUser.getCompanyId().getId();
        this.cycleId = taskUser.getCycleId();
        this.empId = taskUser.getEmpId();
        this.empName = taskUser.getEmpName();
        this.taskName = taskUser.getTaskName();
        this.taskUserId = taskUser.getId();
        this.taskId = taskUser.getTaskId();
    }

    public void populateScorerInfo(String scoreScene, Integer scoreLevel, String scorerId, EmpEvalMerge evalMerge){
        if (evalMerge.isCustom()){
            this.scoreScene = SubScoreNodeEnum.CUSTOM.getScene();
        }else {
            this.scoreScene = scoreScene;
        }
        this.scoreLevel = scoreLevel;
        this.scorerId = scorerId;
    }


    public void init(String id){
        this.id = id;
        this.createdTime = new Date();
    }

}
