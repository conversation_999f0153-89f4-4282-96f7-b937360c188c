package com.polaris.kpi.eval.domain.cycle.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 周期内命中的绩优/绩差关联任务
 *
 * <AUTHOR>
 * @date 2024/9/28 15:34
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ConsecutiveTopResultHitTask {

    /**
     * 周期id
     */
    private String cycleId;

    /**
     * 周期名称
     */
    private String cycleName;

    /**
     * 符合规则的任务id
     */
    private String taskId;

    /**
     * 符合规则的任务名称
     */
    private String taskName;

    /**
     * 绩效分数
     */
    private Double perfScore;

    /**
     * 绩效等级名称
     */
    private String perfLevelName;

    /**
     * 绩效系数
     */
    private String perfWeight;

    private Integer showResultType; //结果呈现:1000(8)=系数,100(4)=总分,010(2)=总等级,001(1)=维度等级

}
