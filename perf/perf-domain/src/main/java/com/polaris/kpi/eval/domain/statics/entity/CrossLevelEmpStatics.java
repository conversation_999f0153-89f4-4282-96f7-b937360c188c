package com.polaris.kpi.eval.domain.statics.entity;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.common.DelableDomain;
import com.polaris.kpi.eval.domain.task.entity.Cycle;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.JsonAryColumn;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 周期考核跨级人员统计表
 *
 * <AUTHOR>
 * @date 2025/1/20 14:28
 */
@Data
@NoArgsConstructor
public class CrossLevelEmpStatics extends DelableDomain implements Serializable {

    /**
     * 唯一标识符，用于唯一标识一条考核数据记录。
     */
    private String id;

    /**
     * 公司的唯一标识符，代表该考核数据所属的公司。
     */
    private String companyId;

    /**
     * 周期的唯一标识符，用于标识该考核数据所处的考核周期。
     */
    private String cycleId;

    private String taskId;

    /**
     * 任务关联的用户的唯一标识符，与该考核任务相关的用户。
     */
    private String taskUserId;

    /**
     * 跨级类型，用于表示考核中的跨级情况。
     * 取值为 1 时表示上升，即考核层级向上跨越；
     * 取值为 2 时表示下降，即考核层级向下跨越。
     */
    private Integer crossLevelType;

    /**
     * 标识该考核是否为组织考核。
     * 1-个人考核 2-组织考核
     */
    private Integer performanceType;

    /**
     * 被考核人的唯一标识符，用于唯一标识被考核的员工。
     */
    private String empId;

    /**
     * 被考核人的姓名，记录被考核员工的真实姓名。
     */
    private String empName;

    /**
     * 被考核人的头像信息，可能是头像的存储路径或头像的唯一标识。
     */
    private String avatar;

    /**
     * 组织的唯一标识符，代表该考核数据所关联的组织。
     */
    private String orgId;

    /**
     * 组织的名称，对应 orgId 所代表组织的具体名称。
     */
    private String orgName;

    /**
     * 被考核组织的唯一标识符，若考核对象为组织时使用。
     */
    private String evalOrgId;

    /**
     * 被考核组织的名称，对应 evalOrgId 所代表组织的具体名称。
     */
    private String evalOrgName;

    /**
     * 组织名称的路径信息，可能表示组织在组织架构中的层级路径名称。
     */
    private String atOrgNamePath;

    /**
     * 组织编码的路径信息，可能表示组织在组织架构中的层级路径编码。
     */
    private String atOrgCodePath;

    /**
     * 考核跨级内容
     */
    @JsonAryColumn(CrossLevelGroup.class)
    private List<CrossLevelGroup> perfCrossData;

    public static CrossLevelEmpStatics buildInstance(EvalUser taskUser,String opEmpId,String companyId) {

        CrossLevelEmpStatics statics = new CrossLevelEmpStatics();
        statics.accTaskUser(taskUser);
        statics.setCompanyId(companyId);
        statics.setCreatedUser(opEmpId);
        statics.setUpdatedUser(opEmpId);
        return statics;

    }

    public void buildPerfCrossData(List<EvalUser> levelCrossUpTaskUsers, EvalUser taskUser, String type, Cycle lastCycle, Cycle currentCycle) {

        List<CrossLevelGroup> groups = new ArrayList<>();
        CrossLevelGroup lastGroup = new CrossLevelGroup();
        CrossLevelGroup currentGroup = new CrossLevelGroup();

        String currentCycleInfo =  buildCycleInfo(currentCycle);
        String lastCycleInfo =  buildCycleInfo(lastCycle);

        //收集成新的list
        List<CrossLevelTaskResult> lastResults = levelCrossUpTaskUsers.stream().map(evalUser -> {
            CrossLevelTaskResult result = new CrossLevelTaskResult();
            result.setEvaluationLevel(evalUser.getEvaluationLevel());
            result.setFinalScore(evalUser.getFinalScore());
            result.setPerfCoefficient(evalUser.getPerfCoefficient());
            result.setTaskName(evalUser.getTaskName());
            return result;
        }).collect(Collectors.toList());

        lastGroup.setIsCurrentCycle("false");
        lastGroup.setCycleInfo(lastCycleInfo);
        lastGroup.setYear(lastCycle.getCycleValue().getYear());
        lastGroup.setValue(lastCycle.getCycleValue().getValue());
        lastGroup.setCycleType(lastCycle.getCycleValue().getType());
        lastGroup.setCrossLevelTaskResults(lastResults);

        List<CrossLevelTaskResult> currentResults = new ArrayList<>();
        CrossLevelTaskResult current = new CrossLevelTaskResult();
        current.setEvaluationLevel(taskUser.getEvaluationLevel());
        current.setFinalScore(taskUser.getFinalScore());
        current.setPerfCoefficient(taskUser.getPerfCoefficient());
        current.setTaskName(taskUser.getTaskName());
        currentResults.add(current);
        currentGroup.setIsCurrentCycle("true");
        currentGroup.setCycleInfo(currentCycleInfo);
        currentGroup.setYear(currentCycle.getCycleValue().getYear());
        currentGroup.setValue(currentCycle.getCycleValue().getValue());
        currentGroup.setCycleType(currentCycle.getCycleValue().getType());
        currentGroup.setCrossLevelTaskResults(currentResults);

        groups.add(lastGroup);
        groups.add(currentGroup);

        this.perfCrossData = groups;
        this.crossLevelType = type.equals("up") ? 1 : 2;
    }

    private String buildCycleInfo(Cycle currentCycle) {

        Integer year = currentCycle.getCycleValue().getYear();
        String type = currentCycle.getCycleValue().getType();
        Integer value = currentCycle.getCycleValue().getValue();

        String cycleInfo;
        if (type.equals("month") || type.equals("cross_month")) {
            cycleInfo = year + "年" + value + "月";
        } else if (type.equals("quarter")) {
            cycleInfo = year + "年" + value + "季度";
        } else if (type.equals("half_year")){
            if (value == 1){
                cycleInfo = year + "年上半年";
            }else {
                cycleInfo = year + "年下半年";
            }
        } else {
            cycleInfo = year + "年";
        }
        return cycleInfo;
    }

    @JSONField(serialize = false)
    public boolean isNew() {
        return StrUtil.isBlank(id);
    }

    public void initOnNew(String id) {
        this.id = id;
        this.createdTime = new Date();
        this.updatedTime = new Date();
    }

    public void accTaskUser(EvalUser evalUser){
        this.empId = evalUser.getEmpId();
        this.empName = evalUser.getEmpName();
        this.avatar = evalUser.getAvatar();
        this.orgId = evalUser.getOrgId();
        this.orgName = evalUser.getEmpOrgName();
        this.evalOrgId = evalUser.getEvalOrgId();
        this.evalOrgName = evalUser.getEvalOrgName();
        this.atOrgNamePath = evalUser.getAtOrgNamePath();
        this.atOrgCodePath = evalUser.getAtOrgCodePath();
        this.taskUserId = evalUser.getId();
        this.taskId = evalUser.getTaskId();
        this.cycleId = evalUser.getCycleId();
        if (evalUser.getEvalOrgId() == null){
            this.performanceType = 1;
        }else {
            this.performanceType = 2;
        }

    }

}
