package com.polaris.kpi.eval.domain.task.entity.empeval;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.infr.task.ppojo.admintask
 * @Author: lufei
 * @CreateTime: 2022-09-10  20:46
 * @Description: TODO
 * @Version: 1.0
 */

import cn.com.polaris.kpi.KpiEmp;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.admineval.EvalRuleLogField;
import com.polaris.kpi.eval.domain.task.entity.admineval.EvalRuleOpLogMeta;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * {
 * "id":"操作记录id",
 * "empEvalId":"外键task_user.id, 员工任务id",
 * "companyId":"公司id",
 * "atStatus":"修改时所在阶段",
 * "changeType":"01=1=指标, 10=2=流程,11=3=指标+流程",
 * "indicator":"指标类修改,type_weight_conf,score_value_conf, _kpi表,_kpi_type表",
 * "confirmTask":"确认任务 对应模板 templAffirmJson:PerfTemplEvaluateAffirm",
 * "editExeIndi":"执行中修改指标日志记录  对应模板 templExecuteJson:PerfTemplEvaluateExecute",
 * "scoreConf":"enter_score,s3_self_rater,s3_mutual_rater,s3_super_rater ,score_veiw",
 * "auditResult":"结果校准,从templEvaluateJson:PerfTemplEvaluate 模板评价配置json 单独出来的",
 * "confirmResult":"结果确认,多个字段合并",
 * "publishResult":"结果公示,多个字段合并",
 * "appealConf":"结果申诉,多个字段合并",
 * "operationTime":"操作时间毫秒ms",
 * "operatorId":"操作人emp_id",
 * "operatorName":"操作人名",
 * "operatorAvatar":"操作人头像",
 * "createdTime":"记录创建时间",
 * }
 ***/
@Getter
@Setter
public class EmpEvalOperation {

    private String id;//操作记录id
    private String empEvalId;//外键task_user.id, 员工任务id
    private String companyId;//公司id
    private String atStatus;//修改时所在阶段
    //private Integer changeType;//01=1=指标, 10=2=流程,11=3=指标+流程
    private Integer changeType;// 1=新建考核表 2=应用考核模板 4=复制考核表 8=修改考核表 16=修改考核流程 32=修改考核部门
    private String adminType;//操作角色 main child
    private EvalRuleOpLogMeta baseInfo;//基本信息
    private List<EvalRuleOpLogMeta> indicator = new ArrayList<>();//指标类修改,type_weight_conf,score_value_conf, _kpi表,_kpi_type表
    private List<EvalRuleOpLogMeta> confirmTask = new ArrayList<>();//确认任务 对应模板 templAffirmJson:PerfTemplEvaluateAffirm
    private List<EvalRuleOpLogMeta> editExeIndi = new ArrayList<>();//执行中修改指标日志记录  对应模板 templExecuteJson:PerfTemplEvaluateExecute
    private List<EvalRuleOpLogMeta> scoreConf = new ArrayList<>();//enter_score,s3_self_rater,s3_mutual_rater,s3_super_rater ,score_veiw
    private List<EvalRuleOpLogMeta> auditResult = new ArrayList<>();//结果校准,从templEvaluateJson:PerfTemplEvaluate 模板评价配置json 单独出来的
    private List<EvalRuleOpLogMeta> interviewConf = new ArrayList<>();//结果面谈,
    private List<EvalRuleOpLogMeta> confirmResult = new ArrayList<>();//结果确认,多个字段合并
    private List<EvalRuleOpLogMeta> publishResult = new ArrayList<>();//结果公示,多个字段合并
    private List<EvalRuleOpLogMeta> appealConf = new ArrayList<>();//结果申诉,多个字段合并
    private List<EvalRuleOpLogMeta> deadLineConf = new ArrayList<>();//阶段截止时间配置
    private Long operationTime;//操作时间毫秒ms
    private String operatorId;//操作人emp_id
    private String operatorName;//操作人名
    private String operatorAvatar;//操作人头像
    private Date createdTime;//记录创建时间

    public EmpEvalOperation() {
    }

    public EmpEvalOperation(String empEvalId, String companyId) {
        this.empEvalId = empEvalId;
        this.companyId = companyId;
    }

    public EmpEvalOperation(String empEvalId, String companyId, String opEmpId, String opAdminType) {
        this.empEvalId = empEvalId;
        this.companyId = companyId;
        this.operatorId = opEmpId;
        this.adminType = opAdminType;
    }

    public EmpEvalOperation(String empEvalId, String companyId, String atStatus) {
        this.empEvalId = empEvalId;
        this.companyId = companyId;
        this.atStatus = atStatus;
    }

    public void addIndicator(EvalRuleOpLogMeta logMeta) {
        indicator.add(logMeta);
    }


    public boolean changed() {
        return changeType > 0;
    }

    public void drawUpIngLog(String id, String companyId, String empEvalId, KpiEmp opEmp) {
        this.id = id;
        this.companyId = companyId;
        this.empEvalId = empEvalId;
        this.atStatus = TalentStatus.DRAW_UP_ING.getStatus();
        this.adminType = opEmp.getAdminType();
        this.changeType = 1;
        this.operationTime = DateUtil.current();
        this.operatorName = opEmp.getEmpName();
        this.operatorId = opEmp.getEmpId();
        this.operatorAvatar = opEmp.getAvatar();
        this.createdTime = new Date();
    }

    /**
     * 新建考核表
     */
    public void empEvalCreated(String ruleName) {
        this.changeType = 1;
        this.atStatus = TalentStatus.DRAW_UP_ING.getStatus();
        if (this.baseInfo == null) {
            this.baseInfo = new EvalRuleOpLogMeta("考核表", "考核规则");
        }
        EvalRuleLogField field = new EvalRuleLogField("名称", StrUtil.EMPTY, ruleName);
        this.baseInfo.addField(field);
    }

    /**
     * 应用考核模板
     */
    public void tempApplied(String ruleName) {
        this.changeType = 2;
        this.atStatus = TalentStatus.DRAW_UP_ING.getStatus();
        if (this.baseInfo == null) {
            this.baseInfo = new EvalRuleOpLogMeta("考核表", "考核规则");
        }
        EvalRuleLogField field = new EvalRuleLogField("名称", StrUtil.EMPTY, ruleName);
        this.baseInfo.addField(field);
    }

    /**
     * 复制上次考核
     */
    public void copiedLatest(String ruleName) {
        this.changeType = 4;
        this.atStatus = TalentStatus.DRAW_UP_ING.getStatus();
        if (this.baseInfo == null) {
            this.baseInfo = new EvalRuleOpLogMeta("考核表", "考核规则");
        }
        EvalRuleLogField field = new EvalRuleLogField("名称", StrUtil.EMPTY, ruleName);
        this.baseInfo.addField(field);
    }

    public void operate(KpiEmp opEmp) {
        this.operatorId = opEmp.getEmpId();
        this.operatorName = opEmp.getEmpName();
        this.operatorAvatar = opEmp.getAvatar();
        this.operationTime = System.currentTimeMillis();
        this.adminType = opEmp.getAdminType();
    }

    public void operate(String operatorId, String operatorName, String operatorAvatar) {
        this.operatorId = operatorId;
        this.operatorName = operatorName;
        this.operatorAvatar = operatorAvatar;
        this.operationTime = System.currentTimeMillis();
    }

    public void setBaseInfo(EvalRuleOpLogMeta baseInfo) {
        if (baseInfo == null) {
            return;
        }
        this.baseInfo = baseInfo;
        this.changeType = 8;
    }

    public void setIndicator(List<EvalRuleOpLogMeta> indicator) {
        if (indicator == null || CollUtil.isEmpty(indicator)) {
            return;
        }
        this.indicator = indicator;
    }

    public void empEvalOrgInfoChanged(EvalUser evalUser, String orgName) {

        this.changeType = 32;
        this.atStatus = evalUser.getTaskStatus();
        if (this.baseInfo == null) {
            this.baseInfo = new EvalRuleOpLogMeta("考核详情", "员工考核部门信息");
        }
        EvalRuleLogField field = new EvalRuleLogField("员工考核部门信息", evalUser.getEmpOrgName(), orgName);
        this.baseInfo.addField(field);
    }
}
