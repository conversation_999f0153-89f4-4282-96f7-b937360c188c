package com.polaris.kpi.eval.domain.confirm.entity;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.eval.Rater;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.perf.www.common.em.OperationLogSceneEnum;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.domain.task.entity.OperationLog;
import com.polaris.kpi.org.domain.dept.repo.EmpFinder;
import com.polaris.sdk.type.AuditEnum;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
public class ConfirmDispatchNodeRs {
    private ConfirmNode node;
    private List<Rater> raters = new ArrayList<>();
    private List<EvalScoreResult> dispatchedResults;


    private List<ConfirmNode> skipNodes = new ArrayList<>();
    private List<Rater> skipRaters = new ArrayList<>();
    private List<EvalScoreResult> skipDispatchedResults = new ArrayList<>();

    public List<ConfirmNode> allNodes() {
        ArrayList<ConfirmNode> nodes = new ArrayList<>();
        if (node != null) {
            nodes.add(node);
        }
        nodes.addAll(skipNodes);
        return nodes;
    }

    public ConfirmDispatchNodeRs() {
    }

    public void addSkipDispatchNodeRs(ConfirmNode node, List<Rater> skipRaters, List<EvalScoreResult> dispatchedResults) {
        this.node = node;
        this.skipNodes.add(node);
        this.skipRaters.addAll(skipRaters);
        this.skipDispatchedResults.addAll(dispatchedResults);
    }

    public void setCurNode(ConfirmNode node, List<Rater> raters, List<EvalScoreResult> dispatchedResults) {
        this.node = node;
        this.raters = raters;
        this.dispatchedResults = dispatchedResults;
    }

    public ConfirmDispatchNodeRs(ConfirmNode node, List<Rater> skipRaters, List<EvalScoreResult> dispatchedResults) {
        this.node = node;
        this.skipRaters = skipRaters;
        this.dispatchedResults = dispatchedResults;
    }

    public List<? extends OperationLog> createSkipLog(TenantId companyId, String taskUserId, EmpFinder finder) {
        List<OperationLog> logList = new ArrayList<>();
        //空缺跳过的
        List<Rater> vacancy = CollUtil.filterNew(skipRaters, e -> Objects.equals(e.getSkipType(), 1));
        if (CollUtil.isNotEmpty(vacancy)) {
            JSONObject logDesc = new JSONObject();
            logDesc.put("scorerId", "-1");
            logDesc.put("scorerType", AuditEnum.CONFIRM_TASK.getScene());
            logDesc.put("scorerName", "空缺跳过");
            OperationLog log = new OperationLog(companyId.getId(), taskUserId, OperationLogSceneEnum.SKIP_REVIEWER.getScene(), "-1", logDesc.toJSONString());
            logList.add(log);
        }

        //重复跳过的
        List<Rater> repeat = CollUtil.filterNew(skipRaters, e -> Objects.equals(e.getSkipType(), 2));

        if (CollUtil.isNotEmpty(repeat)) {
            Set<String> empIds = repeat.stream().map(Rater::getEmpId).collect(Collectors.toSet());
            List<KpiEmp> kpiEmps = finder.listByEmp(companyId, empIds);
            JSONObject logDesc = new JSONObject();
            logDesc.put("scorerId", "-1");
            logDesc.put("scorerType", AuditEnum.CONFIRM_TASK.getScene());
            logDesc.put("scorerName", StrUtil.join(",", CollUtil.map(kpiEmps, e -> e.getEmpName(), true)) + "重复人员");
            OperationLog log = new OperationLog(companyId.getId(), taskUserId, OperationLogSceneEnum.SKIP_REVIEWER.getScene(), "-1", logDesc.toJSONString());
            logList.add(log);
        }
        return logList;
    }

    public boolean isOk() {
        return CollUtil.isNotEmpty(dispatchedResults);
    }

    public Set<String> dispatchRaterIds() {
        return dispatchedResults.stream().map(es -> es.getScorerId()).collect(Collectors.toSet());
    }

    public void skipPassAll() {
        if (CollUtil.isEmpty(skipDispatchedResults)) {
            return;
        }
        for (EvalScoreResult dispatchedResult : skipDispatchedResults) {
            dispatchedResult.passed();
        }
    }
}
