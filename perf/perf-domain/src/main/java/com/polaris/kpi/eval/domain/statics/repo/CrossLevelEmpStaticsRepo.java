package com.polaris.kpi.eval.domain.statics.repo;

import com.polaris.kpi.eval.domain.statics.entity.CrossLevelEmpStatics;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.report.domain.entity.CrossLevelCount;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/20 17:48
 */
public interface CrossLevelEmpStaticsRepo {

    void batchDelete(String companyId,String cycleId, Integer crossLevelType);

    void batchSave(List<CrossLevelEmpStatics> crossLevelEmps);

    void updateCrossCount(CrossLevelCount crossLevelCount);

}
