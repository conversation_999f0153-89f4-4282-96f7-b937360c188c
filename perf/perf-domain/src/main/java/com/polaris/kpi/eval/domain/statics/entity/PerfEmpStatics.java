package com.polaris.kpi.eval.domain.statics.entity;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.common.DelableDomain;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 周期任务员工考核统计表
 *
 * <AUTHOR>
 * @date 2025/1/20 14:24
 */
@Data
@NoArgsConstructor
public class PerfEmpStatics extends DelableDomain implements Serializable {

    /**
     * 唯一标识，用于区分不同的考核记录。
     */
    private String id;

    /**
     * 公司 ID，代表该考核所属的公司。
     */
    private String companyId;

    /**
     * 周期 ID，用于标识考核所处的周期。
     */
    private String cycleId;

    /**
     * 任务 ID，对应具体的考核任务。
     */
    private String taskId;

    /**
     * 任务名称，描述考核任务的具体内容。
     */
    private String taskName;

    /**
     * 任务用户 ID，关联执行该任务的用户。
     */
    private String taskUserId;

    /**
     * 是否组织考核的标识，用于判断本次考核是否为组织层面的考核。
     * 1-个人考核 2-组织考核
     */
    private Integer performanceType;

    /**
     * 被考核人 ID，唯一标识被考核的员工。
     */
    private String empId;

    /**
     * 被考核人姓名，记录被考核员工的真实姓名。
     */
    private String empName;

    /**
     * 被考核人头像的相关信息，可能是头像的存储路径或标识。
     */
    private String avatar;

    /**
     * 组织 ID，代表考核相关的组织。
     */
    private String orgId;

    /**
     * 组织名称，对应组织 ID 的具体组织名称。
     */
    private String orgName;

    /**
     * 被考核组织 ID，若考核对象为组织，则使用该 ID 进行标识。
     */
    private String evalOrgId;

    /**
     * 被考核组织名称，对应被考核组织 ID 的具体组织名称。
     */
    private String evalOrgName;

    /**
     * 组织名称路径，可能表示组织在层级结构中的名称路径。
     */
    private String atOrgNamePath;

    /**
     * 组织编码路径，可能表示组织在层级结构中的编码路径。
     */
    private String atOrgCodePath;

    /**
     * 考核分，使用 BigDecimal 类型存储，以保证数值的精度。
     */
    private BigDecimal finalScore;

    /**
     * 绩效等级，用字符串表示被考核对象的绩效等级，如 "优秀"、"良好" 等。
     */
    private String evaluationLevel;

    /**
     * 等级排序，整数类型，用于对绩效等级进行排序。
     */
    private Integer evaluationLevelSort;

    /**
     * 绩效系数，与绩效等级相关的系数，可能用于计算绩效奖金等。
     */
    private String perfCoefficient;

    /**
     * 高绩效或低绩效的标识。
     * 取值为 1 表示高绩效，取值为 2 表示低绩效。
     */
    private Integer levelHeight;

    public static PerfEmpStatics buildInstance(EvalUser taskUser) {

        PerfEmpStatics statics = new PerfEmpStatics();
        BeanUtils.copyProperties(taskUser, statics);
        statics.setTaskUserId(taskUser.getId());
        statics.setOrgName(taskUser.getEmpOrgName());
        statics.setCompanyId(taskUser.getCompanyId().getId());
        statics.setCreatedUser("");
        statics.setUpdatedUser("");
        return statics;
    }


    public void calcLevelInfo( Map<String, Set<String>> highLowLevels) {

        highLowLevels.get("highLevels").forEach(level -> {
            if (level.equals(getEvaluationLevel())) {
                this.levelHeight = 1;
            }
        });
        highLowLevels.get("lowLevels").forEach(level -> {
            if (level.equals(getEvaluationLevel())) {
                this.levelHeight = 2;
            }
        });

    }

    @JSONField(serialize = false)
    public boolean isNew() {
        return StrUtil.isBlank(id);
    }

    public void initOnNew(String id) {
        this.id = id;
        this.createdTime = new Date();
        this.createdUser = "";
    }


}
