package com.polaris.kpi.eval.domain.confirm.entity;

import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEval;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import lombok.Getter;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 重置到确认中
 * EvalScoreResult 全部删除
 * EvalAudit 全部audit_status还原成 null
 * 重新触发 指标确认环节
 * 指标不用修改
 */
@Getter
public class Reset2ConfirmEval extends EmpEval {
    private List<EvalAudit> audits;
    private List<EvalScoreResult> scoreResults;

    public Reset2ConfirmEval(EmpEvalMerge rule, EvalUser eval) {
        super(rule, eval);
    }

    public String getTaskId() {
        return eval.getTaskId();
    }

    public List<EvalKpi> allKpi() {
        return eval.getKpis();
    }

}
