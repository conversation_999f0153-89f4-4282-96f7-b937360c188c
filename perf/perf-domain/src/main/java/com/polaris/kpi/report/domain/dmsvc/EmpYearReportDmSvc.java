package com.polaris.kpi.report.domain.dmsvc;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.entity.grade.BaseScoreRange;
import com.polaris.kpi.eval.domain.task.entity.grade.EmpRefScoreRule;
import com.polaris.kpi.eval.domain.task.entity.grade.ScoreRange;
import com.polaris.kpi.eval.domain.task.entity.grade.ScoreRule;
import com.polaris.kpi.report.domain.entity.EmpYearItemRs;
import com.polaris.kpi.report.domain.entity.EmpYearReportItem;
import com.polaris.kpi.report.domain.entity.ReportWeightConf;
import com.polaris.sdk.type.ListWrap;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class EmpYearReportDmSvc {
    private List<? extends EmpYearReportItem> empReportItems;
    private ListWrap<EmpRefScoreRule> empRefScoreRules;
    private ListWrap<ScoreRange> scoreRangeGroup;

    public EmpYearReportDmSvc(List<? extends EmpYearReportItem> empReportItems, List<EmpRefScoreRule> empRefScoreRules, List<ScoreRange> scoreRanges) {
        this.empReportItems = empReportItems;
        this.empRefScoreRules = new ListWrap<>(empRefScoreRules).asMap(empRefScoreRule -> empRefScoreRule.getEmpId());
        this.scoreRangeGroup = new ListWrap<>(scoreRanges).groupBy(scoreRange -> scoreRange.getScoreRuleId());
    }

    public void matchLevelByScore(List<EmpYearReportItem> empReportItems, ScoreRule defaultRule) {
        for (EmpYearReportItem empReportItem : empReportItems) {
            EmpRefScoreRule empRefScoreRule = empRefScoreRules.mapGet(empReportItem.getEmpId());
            List<ScoreRange> scoreRanges = null;
            if (empRefScoreRule == null) {
                scoreRanges = defaultRule.getRanges();
            } else {
                scoreRanges = scoreRangeGroup.groupGet(empRefScoreRule.getRuleId());
            }
            if (CollUtil.isEmpty(scoreRanges)) {
                log.error("员工找不到规则:{},empRefScoreRule:{}", empReportItem.getEmpId(), JSONUtil.toJsonStr(empRefScoreRule));
                continue;
            }
            ScoreRule scoreRule = new ScoreRule(scoreRanges);
            empReportItem.matchYear(scoreRule);
            empReportItem.matchMonth(scoreRule);
        }
    }

    //计算加权的年度分
    public void computeWeightYearScore(ReportWeightConf weightConf) {
        for (EmpYearReportItem empReportItem : empReportItems) {
            empReportItem.computeWeightScore(weightConf);
        }
    }
}
