package com.polaris.kpi.extData.domain.type.encAndDec;

import com.polaris.kpi.common.KpiI18NException;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * @Author: xuxw
 * @Date: 2025/03/06 09:55
 * @Description:
 */
@Deprecated
// 咱不支持密钥加密
public class HmacSha256 extends EncDecAdapter {

    private static final String HMACSHA256 = "HmacSha256";

    @Override
    public String enc(String plaintext) {
        return "";
    }

    @Override
    public String enc(String plaintext, String secretKey) {
        try {
            Mac sha256_HMAC = Mac.getInstance(HMACSHA256);
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), HMACSHA256);
            sha256_HMAC.init(secretKeySpec);
            byte[] hmacBytes = sha256_HMAC.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hmacBytes);
        } catch (Exception e) {
            throw new KpiI18NException("enc.error", e.toString());
        }
    }
}
