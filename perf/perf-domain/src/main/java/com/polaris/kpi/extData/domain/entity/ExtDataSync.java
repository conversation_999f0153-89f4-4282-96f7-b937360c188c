package com.polaris.kpi.extData.domain.entity;

import cn.hutool.core.collection.CollUtil;
import com.polaris.kpi.common.DelableDomain;
import com.polaris.kpi.common.KpiI18NException;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.annotations.Ckey;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @Author: xuxw
 * @Date: 2025/03/15 15:32
 * @Description:
 */
@Setter
@Getter
public class ExtDataSync extends DelableDomain {
    @Ckey
    private String id;
    private String extDataFieldId;
    private String dingUserId;
    private String phone;
    private String empName;
    private String jobNum;
    private String companyId;
    private String orgId;
    private String orgName;
    private String employeeId;
    private String cycleTime;
    private String cycleStartTime;
    private String extData;
    private String uniqueKey;
    private List<ExtDataSync> addList;
    private List<ExtDataSync> updateList;

    public static List<ExtDataSync> buildMapping(List<ExtDataSync> extDataSyncs, List<KpiEmpInfo> employees, ExtDataField fieldInfo) {
        // 检查员工数据是否为空
        if (CollUtil.isNotEmpty(employees)) {
            // 根据优先级选择匹配策略
            if (fieldInfo.getUseDingUserId() == 1) {
                return syncDataByField(extDataSyncs, employees, KpiEmpInfo::getDingUserId, ExtDataSync::getDingUserId);
            }
            if (fieldInfo.getUseJobNum() == 1) {
                return syncDataByField(extDataSyncs, employees, KpiEmpInfo::getJobnumber, ExtDataSync::getJobNum);
            }
            if (fieldInfo.getUsePhone() == 1) {
                return syncDataByField(extDataSyncs, employees, KpiEmpInfo::getMobile, ExtDataSync::getPhone);
            }
            if (fieldInfo.getUseName() == 1) {
                return syncDataByField(extDataSyncs, employees, KpiEmpInfo::getEmpName, ExtDataSync::getEmpName);
            }
        }
        return extDataSyncs;
    }

    private static List<ExtDataSync> syncDataByField(
            List<ExtDataSync> extDataSyncs,
            List<KpiEmpInfo> employees,
            Function<KpiEmpInfo, String> fieldGetter,
            Function<ExtDataSync, String> syncFieldGetter) {

        Map<String, KpiEmpInfo> employeeMap = employees.stream()
                .collect(Collectors.toMap(fieldGetter, Function.identity(), (a, b) -> a));

        extDataSyncs.forEach(s -> {
            String fieldValue = syncFieldGetter.apply(s);
            KpiEmpInfo employee = employeeMap.get(fieldValue);
            if (employee != null) {
                s.setEmpName(employee.getEmpName());
                s.setJobNum(employee.getJobnumber());
                s.setPhone(employee.getMobile());
                s.setEmployeeId(employee.getEmployeeId());
                s.setOrgId(employee.getOrgId());
                s.setOrgName(employee.getOrgName());
            }
        });

        return extDataSyncs;
    }
}
