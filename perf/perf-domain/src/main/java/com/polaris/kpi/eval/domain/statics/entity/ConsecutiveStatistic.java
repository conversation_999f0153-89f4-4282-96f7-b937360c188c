package com.polaris.kpi.eval.domain.statics.entity;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.common.DelableDomain;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/2/19 11:22
 */
@Data
public class ConsecutiveStatistic extends DelableDomain implements Serializable {

    /**
     * 主键
     */
    private String id;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 考核周期id
     */
    private String cycleId;

    /**
     * 连续员工数，统计表保存的是周期内全公司人数
     */
    private Integer empCount;

    /**
     * 是否默认规则
     */
    private Boolean isRuleDefault;

    /**
     * 1 为员工绩效，2 为组织绩效
     */
    private Integer performanceType;

    /**
     * 规则类型，1 为连续绩优，2 为连续绩差
     */
    private Integer ruleType;

    public ConsecutiveStatistic(String companyId, String cycleId, Integer performanceType, Integer ruleType , Boolean ruleDefault){
        this.companyId = companyId;
        this.cycleId = cycleId;
        this.performanceType = performanceType;
        this.ruleType = ruleType;
        this.isRuleDefault = ruleDefault;
    }


    @JSONField(serialize = false)
    public boolean isNew() {
        return StrUtil.isBlank(id);
    }

    public void initOnNew(String id) {
        if (!isNew()) {
            return;
        }

        this.id = id;
        this.isDeleted = "false";
        this.createdTime = new Date();
        this.updatedTime = new Date();
        this.createdUser = "";
        this.updatedUser = "";
    }

}
