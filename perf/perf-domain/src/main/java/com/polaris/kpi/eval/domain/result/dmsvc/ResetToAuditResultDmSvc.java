package com.polaris.kpi.eval.domain.result.dmsvc;

import cn.com.polaris.kpi.company.MsgSceneEnum;
import cn.com.polaris.kpi.eval.OperationTypeEnum;
import com.alibaba.fastjson.JSONObject;
import com.perf.www.common.em.OperationLogSceneEnum;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.OperationLog;
import com.polaris.kpi.eval.domain.task.repo.TaskUserRepo;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.sdk.type.AuditEnum;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;

import java.util.*;

public class ResetToAuditResultDmSvc {
    private TenantId companyId;
    private String taskUserId;
    private String opEmpId;
    private String resetNode;
    private String operationType;
    private String resetReason;
    private EvalUser taskUser;

    @Getter
    private Set<String> clearScoreRsScenes = new HashSet<>();
    @Getter
    private OperationLog log;

    public ResetToAuditResultDmSvc(TenantId companyId, String opEmpId, String resetReason, EvalUser taskUser) {
        this.companyId = companyId;
        this.opEmpId = opEmpId;
        this.resetNode = TalentStatus.RESULTS_AUDITING.getStatus();
        this.operationType = OperationTypeEnum.CHANGED_CONFIG.getValue();
        this.resetReason = resetReason;
        this.taskUser = taskUser;
        this.taskUserId = taskUser.getId();
    }

    public OperationLog handResetStage(TaskUserRepo userRepo) {
        if (Objects.isNull(this.taskUser)) {
            return null;
        }
        TalentStatus current = TalentStatus.statusOf(taskUser.getTaskStatus());
        clearScoreRsScenes.add(MsgSceneEnum.TASK_WAIT_PUBLIC.getType());
        clearScoreRsScenes.add(MsgSceneEnum.TASK_RESULT_APPEAL.getType());  //考核申述
        // 清理数据
        if (current.afterEq(TalentStatus.RESULTS_AUDITING)) {//校准
            clearScoreRsScenes.addAll(Arrays.asList(MsgSceneEnum.TASK_RESULT_AUDIT.getType(), MsgSceneEnum.TASK_RESULT_AFFIRM.getType()));
            clearScoreRsScenes.add(AuditEnum.FINAL_RESULT_AUDIT.getScene());
            taskUser.resetFinalScoreAndLevel();
        }
        taskUser.setHasAppeal(Boolean.FALSE.toString());
        userRepo.updateTaskUser(taskUser);
        //userRepo.resetAuditStatus(companyId, taskUserId, clearScoreRsScenes);
        userRepo.resetStageAudit(companyId, taskUserId, clearScoreRsScenes, operationType, TalentStatus.RESULTS_AUDITING, taskUser);


        JSONObject logDesc = new JSONObject();
        logDesc.put("resetReason", resetReason);
        logDesc.put("resetNode", resetNode);
        //重置操作日志
        OperationLog operationLog = new OperationLog(companyId.getId(), taskUserId,
                OperationLogSceneEnum.RESET_TASK_TO_NODE.getScene(), opEmpId, logDesc.toJSONString());
        operationLog.setCreatedTime(new Date());
        operationLog.setOperationType(operationType);
        this.log = operationLog;
        return operationLog;
        //记录操作日志
        //opLogDao.addLog(logDo);
    }
}
