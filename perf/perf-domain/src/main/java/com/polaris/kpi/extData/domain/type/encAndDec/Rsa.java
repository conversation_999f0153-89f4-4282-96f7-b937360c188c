package com.polaris.kpi.extData.domain.type.encAndDec;

import com.polaris.kpi.common.KpiI18NException;

import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * @Author: xuxw
 * @Date: 2025/03/06 09:54
 * @Description:
 */
public class Rsa extends EncDecAdapter {

    private static final String RSA = "RSA";
    private KeyPair secretKey;

    @Override
    public String enc(String plaintext){
        try {
            Cipher cipher = Cipher.getInstance(RSA);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey.getPublic());
            byte[] encryptedBytes = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            throw new KpiI18NException("enc.error", e.toString());
        }
    }

    @Override
    public String dec(String ciphertext){
        try {
            Cipher cipher = Cipher.getInstance(RSA);
            cipher.init(Cipher.DECRYPT_MODE, secretKey.getPrivate());
            byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(ciphertext));
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new KpiI18NException("dec.error", e.toString());
        }
    }

    private static KeyPair generateRSAKeyPair(){
        KeyPairGenerator keyPairGen = null;
        try {
            keyPairGen = KeyPairGenerator.getInstance(RSA);
        } catch (NoSuchAlgorithmException e) {
            throw new KpiI18NException("getKey.error", e.toString());
        }
        keyPairGen.initialize(2048);
        return keyPairGen.generateKeyPair();
    }

    public Rsa(){
        this.secretKey = this.generateRSAKeyPair();
    }
}
