package com.polaris.kpi.extData.domain.dmsvc;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.polaris.kpi.common.KpiI18NException;
import com.polaris.kpi.extData.domain.entity.ExtDataField;
import com.polaris.kpi.extData.domain.entity.ExtDataFieldParam;
import com.polaris.kpi.extData.domain.repo.ExtDataSyncRepo;
import com.polaris.kpi.extData.domain.type.ApiParamAnalysis;
import com.polaris.kpi.extData.domain.type.ExtDataFieldParamValue;
import com.polaris.kpi.extData.domain.entity.ExtDataSync;
import com.polaris.kpi.extData.domain.type.HttpClient;
import com.polaris.kpi.extData.domain.type.ParamAnalysis;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: xuxw
 * @Date: 2025/02/24 11:45
 * @Description:
 */
@Slf4j
public class ExtDataSyncDmSvc {
    private ExtDataField fieldInfo;
    private List<ExtDataSync> res;

    public ExtDataSyncDmSvc(ExtDataField fieldInfo){
        this.fieldInfo = fieldInfo;
    }

    public ExtDataSyncDmSvc(List<ExtDataSync> res, ExtDataField fieldInfo){
        this.fieldInfo = fieldInfo;
        this.res = res;
    }

    public List<ExtDataSync> dataSync(){
        Map<String, String> mappingMap = new HashMap<>();
        this.fieldInfo.getParams().stream().forEach(s ->{
            if (s.getParamAction() == 1 || s.getParamAction() ==3){
                mappingMap.put(s.getParamMapping(), s.getParamEnName());
            }
        });
        List<ExtDataSync> result = new ArrayList<>();
        List<ExtDataFieldParam> params = this.buildRequestRes();
        for (ExtDataFieldParam p : params){
            ExtDataSync extDataSync = new ExtDataSync();
            JSONObject jsonObject = new JSONObject();
            List<ExtDataFieldParamValue> paramValues = p.getParamValues();
            paramValues.forEach(s -> {
                if (StrUtil.equals(mappingMap.get("dingUserId"), s.getParamEnName())){
                    extDataSync.setDingUserId(s.getValue().toString());
                }
                if (StrUtil.equals(mappingMap.get("name"), s.getParamEnName())){
                    extDataSync.setEmpName(s.getValue().toString());
                }
                if (StrUtil.equals(mappingMap.get("phone"), s.getParamEnName())){
                    extDataSync.setPhone(s.getValue().toString());
                }
                if (StrUtil.equals(mappingMap.get("jobNum"), s.getParamEnName())){
                    extDataSync.setJobNum(s.getValue().toString());
                }
                if (StrUtil.equals(mappingMap.get("uniqueKey"), s.getParamEnName())){
                    extDataSync.setUniqueKey(s.getValue().toString());
                }
                if (StrUtil.equals(mappingMap.get("cycleTime"),s.getParamEnName())){
                    String cycleTime = s.getValue().toString();
                    extDataSync.setCycleTime(cycleTime);
                    String cycleStartTime = ApiParamAnalysis.getDayOfMonth(cycleTime);
                    if (ObjectUtil.isNull(cycleStartTime)){
                        cycleStartTime = cycleTime;
                    }
                    extDataSync.setCycleStartTime(cycleStartTime);
                }
                jsonObject.put(s.getParamEnName(), s.getValue().toString());
            });
            extDataSync.setExtData(jsonObject.toJSONString());
            extDataSync.setExtDataFieldId(fieldInfo.getId());
            extDataSync.setCompanyId(fieldInfo.getCompanyId());
            result.add(extDataSync);
        }
        return result;
    }

    private List<ExtDataFieldParam> buildRequestRes() {
        HttpClient httpClient = new HttpClient();
        ResponseEntity<String> response;
        try {
            response = httpClient.sendRequest(fieldInfo.buildParam(), null);
        } catch (Exception e) {
            throw new KpiI18NException("param.error", "数据字段配置异常！");
        }

        log.info("外部数据接口返回结果{}", response.toString());
        ParamAnalysis paramAnalysis = new ParamAnalysis();
        JSONObject jsonObject = paramAnalysis.transferParam(response.getBody(), fieldInfo.getResParamType());
        // 解析接口返回参数
        List<ExtDataFieldParam> res = paramAnalysis
                .apiResParamAnals(jsonObject, fieldInfo.getJsonNodePath(), fieldInfo.build());
        log.info("解析接口返回结果{}", JSONArray.toJSONString(res));
        return res;
    }

    public void updateSyncData(ExtDataSyncRepo syncRepo) {
        for (ExtDataSync s : res){
            ExtDataSync data = syncRepo.listByUnique(s.getCompanyId(), fieldInfo.getId(), s.getUniqueKey(), s.getCycleTime());
            if (ObjectUtil.isNull(data)){
                syncRepo.save(s);
            }else {
                data.setExtData(s.getExtData());
                syncRepo.update(data);
            }
        }
    }
}
