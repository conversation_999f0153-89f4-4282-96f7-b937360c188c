package com.polaris.kpi.setting.domain.entity;

import com.polaris.kpi.common.DelableDomain;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.xpath.operations.Bool;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/3/18 10:40
 */
@Setter
@Getter
@NoArgsConstructor
public class ScorerTodoSummary extends DelableDomain {

    private String id;
    private String companyId;
    private String cycleId;
    private String taskId;
    private String taskName;
    private String scorerId; //评分人id
    private Integer needCnt; //发待办需要人数
    private Integer readyCnt; //发待办就绪人数
    private Integer scoredCnt; //评分完成人数
    private Integer scoreCnt; //评分人数
    private Integer todoStatus; //钉钉待办状态 0-未发送 1-发送处理中 2-处理完成
    private String thirdMsgId; //第三方消息ID

    public Boolean filterFinished() {
        if (Objects.isNull(needCnt) || Objects.isNull(readyCnt) || Objects.isNull(scoredCnt)) {
            return false;
        }
        if (needCnt > 0 && readyCnt > 0 && scoredCnt > 0) {
            //过滤掉已完成的，不重复新增汇总通知记录【重置个人，比如自评，会重新刷新新增】
            return needCnt.equals(readyCnt) && readyCnt.equals(scoredCnt);
        }
        return false;
    }

    public Boolean readyToSend() {
        return needCntEqualsReadyCnt()
                && notAllScored()
                && isNotEmptySummary();
    }

    public Boolean needCntEqualsReadyCnt() {
        return Objects.equals(needCnt, readyCnt);
    }

    public Boolean isNotEmptySummary() {
        return needCnt > 0;
    }

    public Boolean notAllScored() {
        return !Objects.equals(needCnt, scoredCnt);
    }

    public Boolean readyToClose() {
        return scoredCntEqualsReadyCnt()
                && existRemoteTodo();
    }

    public Boolean scoredCntEqualsReadyCnt() {
        return Objects.equals(scoredCnt, readyCnt);
    }

    public Boolean existRemoteTodo() {
        return todoStatus == 1;
    }


    public void init(String id){
        this.id = id;
        this.createdTime = new Date();
        todoStatus = 0;
    }

    public Boolean isNew() {
        return Objects.isNull(id);
    }

    public void copyFromOldOne(ScorerTodoSummary oldOne) {
        this.id = oldOne.id;
        this.companyId = oldOne.companyId;
        this.cycleId = oldOne.cycleId;
        this.taskId = oldOne.taskId;
        this.taskName = oldOne.taskName;
        this.scorerId = oldOne.scorerId;
        this.todoStatus = oldOne.todoStatus;
        this.thirdMsgId = oldOne.thirdMsgId;
        this.createdTime = oldOne.createdTime;
        this.version = oldOne.version;
        this.updatedTime = new Date();
    }

    public void copyBaseInfoFromOldOne(ScorerTodoSummary oldOne) {
        this.companyId = oldOne.companyId;
        this.cycleId = oldOne.cycleId;
        this.taskId = oldOne.taskId;
        this.taskName = oldOne.taskName;
        this.scorerId = oldOne.scorerId;
    }

    public void populateByTaskUser(EvalUser taskUser){
        this.companyId = taskUser.getCompanyId().getId();
        this.taskId = taskUser.getTaskId();
        this.taskName = taskUser.getTaskName();
        this.cycleId = taskUser.getCycleId();
        this.todoStatus = 0;
    }

    public void scorerId(String scorerId){
        this.scorerId = scorerId;
    }

}
