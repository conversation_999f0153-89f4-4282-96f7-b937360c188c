package com.polaris.kpi.eval.domain.task.entity.flow;

import cn.com.polaris.kpi.SkipEmp;
import cn.hutool.core.collection.CollUtil;
import com.polaris.kpi.common.KpiI18NException;
import com.polaris.kpi.eval.domain.task.entity.EvalAudit;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.domain.task.entity.calibrated.ResultAudit;
import com.polaris.kpi.eval.domain.task.entity.empeval.BaseScoreResult;
import com.polaris.sdk.type.AuditEnum;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.domain.task.entity.flow
 * @Author: lufei
 * @CreateTime: 2022-12-07  21:34
 * @Description: TODO
 * @Version: 1.0
 */
public class LevelAuditFlow {
    @Getter
    private String taskUserId;
    private List<LevelAuditFlowNode> chain = new ArrayList<>();
    @Getter
    private boolean isEnd;
    @Getter
    private boolean levelEnd;
    private LevelAuditFlowNode curLevel;

    public LevelAuditFlow() {
    }

    public List<String> curLevelEmpIds() {
        return curLevel.scoreRs().stream().map(rs -> rs.getScorerId()).collect(Collectors.toList());
    }

    public Collection<String> nextLevelEmpIds() {
        return curLevel.next.scoreRs().stream().map(rs -> rs.getScorerId()).collect(Collectors.toList());
    }
    public List<String> curLevelEmpIdsExcludeSkip() {
        return  curLevel.scoreRs().stream().filter(scoreResult -> !scoreResult.isPassed()).map(BaseScoreResult::getScorerId).collect(Collectors.toList());
    }
    public Integer currentLevel() {
        return curLevel.level;
    }

    public List<EvalAudit> nextAudits() {
        if (curLevel.next == null) {
            return Collections.emptyList();
        }
        return curLevel.next.subNodes;
    }

    public boolean nextIsNull() {
        return curLevel.next == null;
    }


    public List<EvalAudit> nextAllAudits() {
        List<EvalAudit> audits = new ArrayList();
        for (LevelAuditFlowNode levelNode : chain) {
            if (levelNode.level <= curLevel.level) {
                continue;
            }
            audits.addAll(levelNode.subNodes);
        }
        return audits;
    }

    public List<EvalScoreResult> nextAllRs(List<EvalAudit> audits) {
        if (CollUtil.isEmpty(audits)) {
            return new ArrayList<>();
        }
        List<EvalScoreResult> rss = new ArrayList<>();
        for (EvalAudit audit : audits) {
            if (audit.unDispatched()) {
                rss.addAll(audit.dispatchRaters());
            } else {
                rss.addAll(audit.getSubNodes());
            }
        }
        rss.forEach(r -> {
            r.setAuditStatus("pass");
        });
        return rss;
    }

    public List<EvalAudit> curAudits() {
        return curLevel.subNodes;
    }

    public List<EvalScoreResult> curLevelRs() {
        return curLevel.scoreRs();
    }
    public List<EvalScoreResult> getCurPassedScoreRs() {
        return curLevel.getCurPassedScoreRs();
    }
    public List<EvalScoreResult> nextLevelRs() {
        if (curLevel.next == null) {
            return Collections.emptyList();
        }
        return curLevel.next.scoreRs();
    }

    public int matchOpEmpOrder(EmpId opEmpId) {
        for (LevelAuditFlowNode levelNode : chain) {
            for (EvalAudit subNode : levelNode.subNodes) {
                if (CollUtil.isEmpty(subNode.getSubNodes())) {
                    continue;
                }
                for (EvalScoreResult node : subNode.getSubNodes()) {
                    if (opEmpId.getId().equals(node.getScorerId())) {
                        return levelNode.level;
                    }
                }
            }
        }
        throw new KpiI18NException("auditFlow.noActionRequired", "您无需操作");
    }

    public List<String> passAuditEmpId() {
        List<String> empIds = new ArrayList<>();
        for (EvalAudit subNode : this.curLevel.subNodes) {
            empIds.addAll(subNode.getSubNodes().stream().filter(result -> "pass".equals(result.getAuditStatus())).map(EvalScoreResult::getScorerId).collect(Collectors.toList()));
        }
        return empIds;
    }


    public LevelAuditFlow(String taskUserId, ListWrap<EvalAudit> allSubNodes) {
        this.taskUserId = taskUserId;
        allSubNodes.getGroups().forEach((level, subNodes) -> {
            if (chain.isEmpty()) {
                chain.add(new LevelAuditFlowNode(Integer.valueOf(level), subNodes));
            } else {
                LevelAuditFlowNode lastLevel = chain.get(chain.size() - 1);
                lastLevel.next = new LevelAuditFlowNode(Integer.valueOf(level), subNodes);
                chain.add(lastLevel.next);
            }
        });
    }

    public void dispatchFirst() {
        if (chain.isEmpty()) {
            return;
        }
        this.curLevel = chain.get(0);
        curLevel.dispatch();
    }

    public List<SkipEmp> autoSingleSkipRater(Integer nodeOrder) {
        if (CollUtil.isEmpty(this.chain)) {
            return new ArrayList<>();
        }
        if (nodeOrder > chain.size()) {
            return new ArrayList<>();
        }
        List<SkipEmp> skipEmps = new ArrayList<>();
        Integer order = nodeOrder;
        boolean skip = true;
        while (skip) {
//            LevelNode levelNode = chain.get(order - 1);
            LevelAuditFlowNode levelNode = getCurLevel(order);
            if (Objects.isNull(levelNode)) {
                if (Objects.equals(order, 1)) {
                    dispatchFirst();
                }
                break;
            }
            List<EvalAudit> skipAudits = CollUtil.filterNew(levelNode.subNodes, EvalAudit::nodeSkip);
            if (CollUtil.isEmpty(skipAudits)) {
                if (Objects.equals(order, 1)) {
                    dispatchFirst();
                }
                break;
            }
            this.submitPassAndDispatch(true, new EmpId(skipAudits.get(0).getApproverInfo()), order);
            for (EvalAudit subNode : skipAudits) {
                //记录跳过的数据
                if (Objects.equals(subNode.getSkipType(), 1)) {
                    skipEmps.add(new SkipEmp(subNode.getTaskUserId(), "-1", "空缺跳过", "-1", 1, subNode.getApprovalOrder()));
                }
                if (Objects.equals(subNode.getSkipType(), 2)) {
                    skipEmps.add(new SkipEmp(subNode.getTaskUserId(), subNode.getApproverInfo(), "重复跳过", "-1", 2, subNode.getApprovalOrder()));
                }
            }
            order++;
            if (Objects.equals(skipAudits.get(0).getMultipleReviewersType(), "and") && skipAudits.size() < levelNode.subNodes.size()) {
                this.levelEnd = false;
                break;
            }
            if (order > this.chain.size()) {
                break;
            }
        }
        return skipEmps;
    }

    public void submitPassAndDispatch(boolean skipped, EmpId opEmpId, Integer submitOrder) {
        this.submitPass(skipped, opEmpId, submitOrder);
        this.dispatchNextLevel();
    }

    public void submitPass(boolean skipped, EmpId opEmpId, Integer submitOrder) {
        if (submitOrder == null) {
            submitOrder = matchOpEmpOrder(opEmpId);
        }
        this.curLevel = getCurLevel(submitOrder);
        this.curLevel.finishSubNode(skipped, opEmpId);
    }

    public int dispatchNextLevel() {
        if (!curLevel.isFinished()) {
            return 1;
        }
        curLevel.end();
        this.levelEnd = true;
        if (curLevel.next == null) {
            this.isEnd = true;
            return 3;
        }
        curLevel.next.dispatch();
        return 2;
    }

    private LevelAuditFlowNode getCurLevel(Integer submitOrder) {
        for (LevelAuditFlowNode levelNode : chain) {
            if (levelNode.level == submitOrder) {
                return levelNode;
            }
        }
        return null;
    }

    public boolean curLevelIsFinished() {
        return curLevel.isFinished();
    }

    public void submitFinishedValueAuditPass(boolean skipped, EmpId opEmpId, Integer submitOrder) {
        if (submitOrder == null) {
            submitOrder = matchOpEmpOrder(opEmpId);
        }
        this.curLevel = chain.get(submitOrder - 1);
        this.curLevel.finishValueAuditSubNode(skipped, opEmpId);
        if (!curLevel.isFinished()) {
            return;
        }
        this.levelEnd = true;
        curLevel.end();
        if (curLevel.next == null) {
            this.isEnd = true;
            return;
        }
        curLevel.next.dispatch();
    }


    public void appendScore(ResultAudit resultAudit) {
        List<EvalScoreResult> scoreResults = curLevelRs();
        for (EvalScoreResult scoreResult : scoreResults) {
            //校准时应该只对当前操作了校准的人记录分数、否则如果校准设置了直属主管、直属主管中存在多个人时result中记录都一样，查询校准记录时区别不了具体操作校准的人
            if (resultAudit.getOwnerEmpId().equals(scoreResult.getScorerId())) {
                scoreResult.setScore(resultAudit.getFinalScore());
                scoreResult.setScoreLevel(resultAudit.getEvaluationLevel());
                scoreResult.setScoreComment(resultAudit.getScoreComment());
                scoreResult.setCalibrationType(resultAudit.getCalibrationType());
                scoreResult.setOperateReason(resultAudit.getOperateReason());
                scoreResult.setIndexCalibration(resultAudit.getItemScores());
                scoreResult.setPerfCoefficient(resultAudit.getPerfCoefficient());
            }
        }
    }
}
