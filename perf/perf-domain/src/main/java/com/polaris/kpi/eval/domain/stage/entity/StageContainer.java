package com.polaris.kpi.eval.domain.stage.entity;

import com.polaris.kpi.eval.domain.TaskEvalContext;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import lombok.Getter;

@Getter
public class StageContainer {
    private String name;
    private TalentStatus status;
    private int order;//执行顺序
    //    private List<Feature> features;//下一步进行设计 增加配置控制点后, 根据配置进行创建Feature.
    private TaskEvalContext context;

    public StageContainer(String name, TalentStatus status, int order) {
        this.name = name;
        this.status = status;
        this.order = order;
    }

    public void receiveContexts(TaskEvalContext context) {
        this.context = context;
    }

    public void startInit(TaskEvalContext context) {
        //features.forEach()
    }

    public void execute(TaskEvalContext context) {
        //features.forEach()
    }

    public void end(TaskEvalContext context) {
        //features.forEach()
    }
}
