package com.polaris.kpi.eval.domain.task.entity.empeval.ecfg;

import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.polaris.kpi.eval.domain.confirm.entity.ConfirmNode;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.admineval.EvalRuleLogField;
import com.polaris.kpi.eval.domain.task.entity.admineval.EvalRuleOpLogMeta;
import com.polaris.kpi.eval.domain.task.entity.flow.DisplayEvalFlow;
import com.polaris.kpi.eval.domain.task.entity.flow.LevelNode;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.org.domain.dept.dmsvc.ExtEmpParser;
import com.polaris.kpi.org.domain.dept.dmsvc.LevelLeaderParser;
import com.polaris.kpi.org.domain.dept.dmsvc.RoleParser;
import com.polaris.kpi.org.domain.dept.repo.DeptFinder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 确认流程的配置
 *
 * <AUTHOR> lufei
 * @date 2022/1/20 6:56 下午
 */
@Setter
@Getter
@NoArgsConstructor
public class AffirmTaskConf extends BaseEvalFlowConf<ConfirmNode> {
    //    protected List<ConfirmNode> auditNodes = new ArrayList<>();
    private String noChangeSkipFlag;//前1人未修改考核任务,则跳过审批流程, 第一位责任人未修改考核任务，则跳过后续的确认流程
    private String modifyAuditFlag;//修改后是否需要审核
    private Integer openConfirmLT = 0;// 关闭=0、 开启超时后终止=1、 开启超时后跳过=2
    private Integer confirmLTDay;//指标确认限时几天
    private String modifyItemDimension = "all";//修改指标维度  默认可以修改指标全部内容 "all","target", 后面可能扩展 "target,itemName,.."
    private BaseEvalFlowConf<BaseAuditNode> sendMsgConf;    //指标确认结束后-抄送给被考核人和主管

    public AffirmTaskConf(String confirmFlag, String noChangeSkipFlag) {
        this.noChangeSkipFlag = noChangeSkipFlag;
        this.open = StrUtil.equals("true", confirmFlag) ? 1 : 0;
    }

    public AffirmTaskConf(String confirmFlag, String noChangeSkipFlag, String modifyAuditFlag) {
        if (!Boolean.valueOf(confirmFlag)) {
            this.open = 0;
            return;
        }
        this.open = 1;
        this.noChangeSkipFlag = noChangeSkipFlag;
        this.modifyAuditFlag = modifyAuditFlag;
    }


    public List<EvalRuleOpLogMeta> compare(AffirmTaskConf after) {
        List<EvalRuleOpLogMeta> rs = new ArrayList<>();
        EvalRuleOpLogMeta logMeta = new EvalRuleOpLogMeta("修改指标确认和审核配置", "", TalentStatus.CONFIRMING.getStatus());
        logMeta.addField(EvalRuleLogField.createIf("指标确认", 1, 0, openName(), after.openName()));
        if (after.isOpen()) {
            logMeta.addField(EvalRuleLogField.createIf("允许修改指标", 1, 4, modifyFlagName(), after.modifyFlagName()));
            logMeta.addField(EvalRuleLogField.createIf("手动签名", 1, 4, confirmAuditSignName(), after.confirmAuditSignName()));
            logMeta.addField(EvalRuleLogField.createIf("同级审核多人时", 1, 4, multiTypeName(), after.multiTypeName()));
            logMeta.addField(EvalRuleLogField.createIf("修改指标维度", 1, 4, modifyItemDimensionName(), after.modifyItemDimensionName()));
            logMeta.addField(EvalRuleLogField.createIfByObj("审批流程", 1, 1, abstractFlowEmp("name", "id"), after.abstractFlowEmp("name", "id"), "name", "id"));
//            logMeta.addField(EvalRuleLogField.createIf("审批流程",1,1, abstractFlowName(), after.abstractFlowName()));
            logMeta.addField(EvalRuleLogField.createIf("完成通知", 1, 1, sendMsgAbstractFlowName(), after.sendMsgAbstractFlowName()));
            logMeta.addField(EvalRuleLogField.createIf("前一人未修改则跳过审批", 1, 4, noChangeSkipFlagName(), after.noChangeSkipFlagName()));
            logMeta.addField(EvalRuleLogField.createIf("允许转交", 1, 4, transferFlagName(), after.transferFlagName()));
            logMeta.addField(EvalRuleLogField.createIf("指标确认时限", 1, 4, itemConfirmOverTime(), after.itemConfirmOverTime()));
            logMeta.addField(EvalRuleLogField.createIf("人员重复跳过", 1, 4, empRepeatSkipName(), after.empRepeatSkipName()));
            logMeta.addField(EvalRuleLogField.createIf("节点人员空缺", 1, 4, nodeEmpVacancyName(), after.nodeEmpVacancyName()));

        }
        if (CollUtil.isNotEmpty(logMeta.getFields())) {
            rs.add(logMeta);
        }
        return rs;
    }

    private String modifyItemDimensionName() {
        if ("all".equals(modifyItemDimension)) {
            return "所有信息";
        }
        return "指标目标值";
    }

    public String itemConfirmOverTime() {
        if (openConfirmLT == null) {
            return "";
        }
        if (openConfirmLT == 1) {
            return "开启确认时限：" + confirmLTDay + "天后未确认自动终止";
        }
        if (openConfirmLT == 2) {
            return "开启确认时限：" + confirmLTDay + "天后未确认自动跳过";
        }
        return "关闭确认时限";
    }

    private String noChangeSkipFlagName() {
        return Boolean.valueOf(noChangeSkipFlag) ? "开启" : "关闭";
    }

    public Boolean noChangeSkip() {
        return Boolean.valueOf(noChangeSkipFlag);
    }

    public Boolean confirmTimeOutAutoTerminate() {
        return this.isOpen() && this.openConfirmLT == 1;
    }

    public Boolean confirmTimeOutAutoSkip() {
        return this.isOpen() && this.openConfirmLT == 2;
    }

    public String modifyItemDimension(String opEmpId, Integer curOrder) {
        ConfirmNode node = getCurAuditNode(opEmpId, curOrder);
        if (Objects.nonNull(node) && StrUtil.isNotBlank(node.getModifyItemDimension())) {
            return node.getModifyItemDimension();
        }
        return this.modifyItemDimension;
    }

    public String confirmAuditSign(String opEmpId, Integer curOrder) {
        ConfirmNode node = getCurAuditNode(opEmpId, curOrder);
        if (Objects.nonNull(node) && StrUtil.isNotBlank(node.getConfirmAuditSign())) {
            return node.getConfirmAuditSign();
        }
        return Boolean.FALSE.toString();
    }

    public ConfirmNode getCurAuditNode(String opEmpId, Integer curOrder) {
        if (CollUtil.isEmpty(this.auditNodes)) {
            return null;
        }
        Map<Integer, ConfirmNode> nodeMap = CollUtil.toMap(this.auditNodes, new HashMap<>(), node -> node.getApprovalOrder());
        ConfirmNode node = nodeMap.get(curOrder);
        if (node == null) {//指标变更时调用 empEvalForConfirm
            return node;
        }
        if (StrUtil.isBlank(node.getModifyItemDimension())) {
            node.setModifyItemDimension(this.modifyItemDimension);
        }
        return node;
    }


    public String sendMsgAbstractFlowName() {
        if (Objects.isNull(this.sendMsgConf)) {
            return null;
        }
        return this.sendMsgConf.abstractFlowName();
    }

    //是否配置有按指标审批结点
    public BaseAuditNode findConfirmOnIndNode() {
        for (BaseAuditNode auditNode : auditNodes) {
            if (auditNode.isOnIndAudit()) {
                return auditNode;
            }
        }
        return null;
    }
    public DisplayEvalFlow asDisplayEvalFlow() {
        DisplayEvalFlow auditFlow = new DisplayEvalFlow(EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT.getScene());
        for (BaseAuditNode auditNode : auditNodes) {
            LevelNode level = new LevelNode(auditNode.getApprovalOrder(), auditNode.getMultiType());
            level.computeStatus();
            auditFlow.addLevel(level);
        }
        return auditFlow;
    }

    public void mergeIndFlow(DisplayEvalFlow showFlow) {
        BaseAuditNode onIndNode = findConfirmOnIndNode();
        if (onIndNode == null) {
            return;
        }
        if (showFlow.getLevelsMap().containsKey(onIndNode.getApprovalOrder())) {
            return;
        }
        LevelNode level = new LevelNode(onIndNode.approvalOrder, onIndNode.multiType);
        level.waitScore();
        showFlow.addEmptyLevel(level);
        showFlow.sortLevels();
    }
    public String modifyFlagName() {
        if (auditNodes == null || CollUtil.isEmpty(auditNodes)) {
            return null;
        }
        List<String> flowNames = new ArrayList<>();
        for (ConfirmNode auditNode : auditNodes) {
            String modifyFlagName = auditNode.modifyFlagName();
            flowNames.add(modifyFlagName);
        }
        return CollUtil.join(CollUtil.filterNew(flowNames, StrUtil::isNotBlank), "->");
    }

    public String confirmAuditSignName() {
        if (auditNodes == null || CollUtil.isEmpty(auditNodes)) {
            return null;
        }
        List<String> flowNames = auditNodes.stream().map(node -> node.confirmAuditSignName())
                .collect(Collectors.toList());
        return CollUtil.join(CollUtil.filterNew(flowNames, name -> StrUtil.isNotBlank(name)), "->");
    }

    public String multiTypeName() {
        if (auditNodes == null || CollUtil.isEmpty(auditNodes)) {
            return null;
        }
        ConfirmNode confirmNode = auditNodes.get(0);
        return confirmNode.multiTypeName();
    }

    public String transferFlagName() {
        if (auditNodes == null || CollUtil.isEmpty(auditNodes)) {
            return null;
        }
        return auditNodes.get(0).transferFlagName();
    }

    public boolean openModify(Integer approvalOrder) {
        if (!isOpen() || CollUtil.isEmpty(getAuditNodes())) {
            return false;
        }
        Optional<ConfirmNode> first = getAuditNodes().stream().filter(a -> a.getApprovalOrder().equals(approvalOrder)).findFirst();
        if (!first.isPresent() || first.get().getModifyFlag() == null) {
            return false;
        }
        return Boolean.valueOf(first.get().getModifyFlag());
    }


    public void parseAuditRaters(EvalUser user, RoleParser roleParser, LevelLeaderParser leaderParser, ExtEmpParser empParser, DeptFinder deptFinder) {
        super.parseAuditRaters(user, roleParser, leaderParser, empParser, deptFinder);
        sendMsgConf.parseAuditRaters(user, roleParser, leaderParser, empParser, deptFinder);
    }

}

