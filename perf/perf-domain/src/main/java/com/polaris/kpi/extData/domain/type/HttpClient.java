package com.polaris.kpi.extData.domain.type;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.polaris.kpi.extData.domain.type.enums.SymbolEnum;
import com.polaris.kpi.common.KpiI18NException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.*;

import java.io.IOException;
import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Author: xuxw
 * @Date: 2025/03/05 18:44
 * @Description:
 */
@Slf4j
public class HttpClient {
    public static final Integer FORM_DATA = 1;
    public static final Integer JSON = 2;
    public static final Integer RAW_TEXT = 3;


    public ResponseEntity<String> sendRequest(RequestParamInfo paramInfo, Map<String, String> stepParamMap){
        if (ObjectUtil.isNull(paramInfo)){
            throw new KpiI18NException("param.error", "外部系统参数异常");
        }

        ApiParamAnalysis analysis = new ApiParamAnalysis();
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders httpHeaders = new HttpHeaders();
        String requestUrl = paramInfo.getReqUrl();

        if (StrUtil.isNotBlank(paramInfo.getQueryParams())){
            //模板参数分析
            Map<String, String> queryParamsMap = analysis.apiParamAnalysis(paramInfo.getQueryParams(), stepParamMap);
            StringBuilder urlBuilder = new StringBuilder(paramInfo.getReqUrl()).append(SymbolEnum.QUESTION.getSymbol());
            for (String key : queryParamsMap.keySet()) {
                urlBuilder.append(key).append(SymbolEnum.EQUAL.getSymbol())
                        .append(queryParamsMap.get(key)).append(SymbolEnum.AMPERSAND.getSymbol());
            }
            requestUrl = urlBuilder.toString();
        }
        if (StrUtil.isNotBlank(paramInfo.getReqHeaders())){
            //模板参数分析
            Map<String, String> requestHeadersMap = analysis.apiParamAnalysis(paramInfo.getReqHeaders(), stepParamMap);
            for (String key : requestHeadersMap.keySet()) {
                httpHeaders.add(key, requestHeadersMap.get(key));
            }
        }

        // token 信息
        if (StrUtil.isNotBlank(paramInfo.getToken())){
            String token = paramInfo.getToken();
            if (StrUtil.isNotBlank(paramInfo.getTokenPrefix())){
                token = paramInfo.getTokenPrefix() + token;
            }
            if (StrUtil.isNotBlank(paramInfo.getTokenName())){
                httpHeaders.set(paramInfo.getTokenName(), token);
            }
        }

        // 请求体信息
        HttpEntity<String> requestEntity;
        if (ObjectUtil.isNotNull(paramInfo.getQueryBodyType())){
            if (paramInfo.getQueryBodyType() == FORM_DATA){
                httpHeaders.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
                requestEntity = new HttpEntity<>(paramInfo.getQueryBody(), httpHeaders);
            } else if (paramInfo.getQueryBodyType() == JSON) {
                httpHeaders.setContentType(MediaType.APPLICATION_JSON);
                requestEntity = new HttpEntity<>(paramInfo.getQueryBody(), httpHeaders);
            }else if (paramInfo.getQueryBodyType() == RAW_TEXT){
                httpHeaders.setContentType(MediaType.TEXT_PLAIN);
                requestEntity = new HttpEntity<>(paramInfo.getQueryBody(), httpHeaders);
            }else {
                throw new KpiI18NException("request.error", "请求body类型异常");
            }
        }else {
            requestEntity = new HttpEntity<>(httpHeaders);
        }
        ResponseEntity<String> responseEntity;
        HttpMethod httpMethod = HttpMethod.valueOf(paramInfo.getReqType().toUpperCase());
        log.info("请求信息：requestUrl:{}，httpMethod：{}，requestEntity：{}", requestUrl, httpMethod, requestEntity);
        responseEntity = restTemplate.exchange(requestUrl, httpMethod, requestEntity, String.class);
        return responseEntity;
    }



    /**
     * 发送get请求
     * @param url
     * @param param
     * @param tokenName
     * @param token
     * @return
     */
    public String doGet(String url, Map<String, String> param, String tokenName, String token) {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        String resultString = "";
        CloseableHttpResponse response = null;
        try {
            URIBuilder builder = new URIBuilder(url);
            if (param != null) {
                for (String key : param.keySet()) {
                    builder.addParameter(key, param.get(key));
                }
            }
            URI uri = builder.build();
            HttpGet httpGet = new HttpGet(uri);
            if (StrUtil.isNotBlank(token)){
                httpGet.setHeader(tokenName, token);
            }
            response = httpclient.execute(httpGet);
            if (response.getStatusLine().getStatusCode() == 200) {
                resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                httpclient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return resultString;
    }

    /**
     * 发送post请求
     * @param url
     * @param param
     * @param tokenName
     * @param token
     * @return
     */
    public String doPost(String url, Map<String, String> param, String tokenName, String token) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String resultString = "";
        try {
            HttpPost httpPost = new HttpPost(url);
            if (StrUtil.isNotBlank(token)){
                httpPost.setHeader(tokenName, token);
            }
            if (param != null) {
                List<NameValuePair> paramList = new ArrayList<>();
                for (String key : param.keySet()) {
                    paramList.add(new BasicNameValuePair(key, param.get(key)));
                }

                UrlEncodedFormEntity entity = new UrlEncodedFormEntity(paramList);
                httpPost.setEntity(entity);
            }
            response = httpClient.execute(httpPost);
            resultString = EntityUtils.toString(response.getEntity(), "utf-8");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                response.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return resultString;
    }
}
