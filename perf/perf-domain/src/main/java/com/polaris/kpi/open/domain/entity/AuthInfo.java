package com.polaris.kpi.open.domain.entity;

import cn.hutool.core.lang.Assert;
import cn.hutool.crypto.digest.MD5;
import com.polaris.kpi.common.Domain;
import lombok.Getter;

import java.beans.ConstructorProperties;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

//开通接口调用的企业授权信息
@Getter
public class AuthInfo extends Domain {
    private String corpId;//要不要和钉钉一致？
    private String companyId;
    private String companyName;
    private String contact;
    private String accessKey;
    private String accessSecret;
    private String accessToken;
    private Date permStartTime;
    private Date permEndTime;
    private Long maxAccessLimit;
    private Long accessCount;
    private List<String> apis;
    private List<String> ips;

    @ConstructorProperties({"corpId", "companyId", "companyName", "contact", "accessKey", "accessSecret",
            "permStartTime", "permEndTime", "maxAccessLimit", "accessCount", "apis", "ips" })
    public AuthInfo(String corpId, String companyId, String companyName, String contact, String accessKey, String accessSecret,
                    Date permStartTime, Date permEndTime, Long maxAccessLimit, Long accessCount, List<String> apis,
                    List<String> ips) {
        this.corpId = corpId;
        this.companyId = companyId;
        this.companyName = companyName;
        this.contact = contact;
        this.accessKey = accessKey;
        this.accessSecret = accessSecret;
        this.permStartTime = permStartTime;
        this.permEndTime = permEndTime;
        this.maxAccessLimit = maxAccessLimit;
        this.accessCount = accessCount;
        this.apis = apis;
        this.ips = ips;
    }

    public String getSignature(Long timestamp) {
        MD5 md5 = MD5.create();
        return md5.digestHex(this.corpId + this.accessKey + this.accessSecret + timestamp);
    }

    public boolean verifySignature(String signature, Long timestamp) {
        if (Calendar.getInstance().getTimeInMillis() - timestamp > 600_000) {
            //超过了10分钟也无效
            return false;
        }
        String sign = getSignature(timestamp);
        return sign.equals(signature);
    }

    public void assertAuthExpires() {
        Calendar now = Calendar.getInstance();
        long endTime = permEndTime.getTime();
        Assert.isTrue(now.getTimeInMillis() <= endTime, "50010: auth expires");
    }

    public void assertAccessLimit() {
        if (this.maxAccessLimit == -1) {
            // -1 表示不限制
            return;
        }
        Assert.isTrue(this.accessCount <= this.maxAccessLimit, "50011, access forbidden");
    }
}
