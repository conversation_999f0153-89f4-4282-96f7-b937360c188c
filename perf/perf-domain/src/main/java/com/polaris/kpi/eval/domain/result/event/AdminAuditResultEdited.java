package com.polaris.kpi.eval.domain.result.event;

import cn.hutool.core.collection.CollUtil;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.org.domain.common.BaseEvent;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
public class AdminAuditResultEdited extends BaseEvent {

    private String taskId;
    private String opEmpId;
    private String opAdminType;
    private AdminTask before;
    private AdminTask after;
    @Setter
    private List<String> taskUserIds;
    private String changedStage=TalentStatus.RESULTS_AUDITING.getStatus();


    public AdminAuditResultEdited(String adminType, String opEmpId, AdminTask before, AdminTask after, List<String> taskUserIds) {
        super(before.getCompanyId().getId());
        this.taskId = before.getId();
        this.opEmpId = opEmpId;
        this.before = before;
        this.after = after;
        this.opAdminType = adminType;
        this.taskUserIds = CollUtil.isEmpty(taskUserIds) ? new ArrayList<>() : taskUserIds;
    }

    public void start() {

    }

    public boolean changedDeadLine() {
        return TalentStatus.DEAD_LINE.getStatus().equals(changedStage);
    }

    public boolean changedInputNotify() {
        return TalentStatus.INPUT_NOTIFY.getStatus().equals(changedStage);
    }

    public boolean selectAll() {
        return taskUserIds.size() == 1 && taskUserIds.contains("ALL");
    }
}
