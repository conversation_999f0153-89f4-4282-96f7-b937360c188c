package com.polaris.kpi.cache.domain.entity;

import cn.hutool.core.util.StrUtil;
import com.perf.www.common.em.OperationLogSceneEnum;
import com.polaris.kpi.common.DelableDomain;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.MutualNodeConf;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.RaterNodeConf;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.S3RaterBaseConf;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.S3SuperRaterConf;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.UUID;

@Data
@NoArgsConstructor
public class CompanyCacheInfo extends DelableDomain {
    private String id;
    private String companyId;
    private String linkId;
    private String businessScene;
    private String cacheKey;
    private String value;
    private String createdUserName;
    private RaterNodeConf s3SelfRater;              //360或简易 自评人
    private MutualNodeConf s3PeerRater;                   //360或简易 同级互评人
    private MutualNodeConf s3SubRater;                    //360下级
    private S3SuperRaterConf s3SuperRater;          //360或简易 上级人
    private S3RaterBaseConf s3AppointRater;

    public void accOp(TenantId tenantId, EmpId opEmpId, String taskUserId) {
        this.id = UUID.randomUUID().toString();
        this.companyId = tenantId.getId();
        this.linkId = taskUserId;
        this.businessScene = OperationLogSceneEnum.CHANGE_ITEM.getScene();
        this.cacheKey = taskUserId;
        this.createdTime = new Date();
        this.updatedTime = new Date();
        this.createdUser = opEmpId.getId();
        this.updatedUser = opEmpId.getId();
    }

    public boolean notChange() {
        return StrUtil.isBlank(value);
    }
}
