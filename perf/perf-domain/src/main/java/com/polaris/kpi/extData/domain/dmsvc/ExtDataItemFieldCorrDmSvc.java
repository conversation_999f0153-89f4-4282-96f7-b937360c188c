package com.polaris.kpi.extData.domain.dmsvc;

import cn.hutool.core.collection.CollUtil;
import com.polaris.kpi.extData.domain.entity.ExtDataItemFieldCorr;
import com.polaris.kpi.extData.domain.repo.ExtDataItemCorrRepo;

import java.util.List;

/**
 * @Author: xuxw
 * @Date: 2025/04/07 14:01
 * @Description:
 */
public class ExtDataItemFieldCorrDmSvc<T extends ExtDataItemFieldCorr> {
    private ExtDataItemCorrRepo<T> repo;
    private List<T> itemFieldCorrs;

    public ExtDataItemFieldCorrDmSvc(ExtDataItemCorrRepo<T> repo, List<T> itemFieldCorrs, String companyId, String createUser){
        this.repo = repo;
        this.itemFieldCorrs = itemFieldCorrs;
        this.itemFieldCorrs.forEach(s -> {
            s.setCompanyId(companyId);
            s.setCreatedUser(createUser);
        });
    }

    public void save(){
        if (CollUtil.isNotEmpty(itemFieldCorrs)){
            for (T t : itemFieldCorrs){
                t.setId(null);
            }
            repo.add(itemFieldCorrs);
        }
    }
}
