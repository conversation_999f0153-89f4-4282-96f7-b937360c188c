package com.polaris.kpi.eval.domain.confirm.dmsvc;

import cn.com.polaris.kpi.company.MsgSceneEnum;
import com.polaris.kpi.eval.domain.confirm.entity.AcceptSumbitRs;
import com.polaris.kpi.eval.domain.task.dmsvc.ClearTodoDmsSvc;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.org.domain.dept.repo.MsgCenterRepo;
import com.polaris.sdk.type.TenantId;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class ConfirmTaskClearDmSvc extends ClearTodoDmsSvc {

    public ConfirmTaskClearDmSvc(MsgCenterRepo centerRepo) {
        super(centerRepo);
    }


    public void clearPassed(TenantId tenantId, String linkId, AcceptSumbitRs acceptPass) {
        List<EvalScoreResult> passed = acceptPass.getPassed();
        Set<String> raterIds = passed.stream().map(es -> es.getScorerId()).collect(Collectors.toSet());
        super.clear(tenantId, linkId, MsgSceneEnum.confirmScene, new ArrayList<>(raterIds));
    }
}
