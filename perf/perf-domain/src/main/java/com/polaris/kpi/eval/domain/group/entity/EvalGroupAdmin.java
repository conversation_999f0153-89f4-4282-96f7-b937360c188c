package com.polaris.kpi.eval.domain.group.entity;

import com.polaris.kpi.common.DelableDomain;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date 2025/7/4 11:58
 */
@Getter
@Setter
public class EvalGroupAdmin extends DelableDomain {
    private String groupId;
    private String adminEmpId;
    private String avatar;
    private String empName;

    public EvalGroupAdmin(String empId) {
        this.adminEmpId = empId;
    }
}
