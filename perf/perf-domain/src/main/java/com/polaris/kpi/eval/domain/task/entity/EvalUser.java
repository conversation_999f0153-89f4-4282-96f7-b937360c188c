package com.polaris.kpi.eval.domain.task.entity;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.com.polaris.kpi.eval.*;
import cn.com.polaris.kpi.temp.CycleId;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.annotation.JSONField;
import com.perf.www.common.constant.BusinessConstant;
import com.perf.www.common.em.ScoreTypeEnum;
import com.polaris.kpi.common.DelableDomain;
import com.polaris.kpi.common.KpiI18NException;
import com.polaris.kpi.eval.domain.cycle.entity.MatchScoreRuleCondition;
import com.polaris.kpi.eval.domain.task.entity.HistoryEvalImport.HistoryEvalImportFields;
import com.polaris.kpi.eval.domain.task.entity.admineval.EmpEvalChangeResult;
import com.polaris.kpi.eval.domain.task.entity.calibrated.RankRuleScoreRangeSnap;
import com.polaris.kpi.eval.domain.task.entity.calibrated.ResultAudit;
import com.polaris.kpi.eval.domain.task.entity.calibrated.ResultRankInstance;
import com.polaris.kpi.eval.domain.task.entity.empeval.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.AuditResultConf;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.BaseAuditNode;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.MutualNodeConf;
import com.polaris.kpi.eval.domain.task.entity.grade.MatchedStep;
import com.polaris.kpi.eval.domain.task.entity.grade.ScoreRange;
import com.polaris.kpi.eval.domain.task.entity.interview.EvalTaskInterviewConf;
import com.polaris.kpi.eval.domain.task.repo.TaskUserRepo;
import com.polaris.kpi.eval.domain.task.type.SimpleTaskAudit;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.org.domain.dept.entity.CompanyMsgCenter;
import com.polaris.kpi.org.domain.emp.entity.LeaderManger;
import com.polaris.sdk.type.*;
import com.quick.common.util.date.DateTimeUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 ***/
@Component
@Slf4j
@Getter
@Setter
public class EvalUser extends DelableDomain implements MatchScoreRuleCondition {
    public static final int auditResultVersin = 1000;//校准优化分开后版本
    private static TaskUserRepo repo;
    private Collection<EvalScoreResult> auditTasks;

    @Autowired
    public void setRepo(TaskUserRepo repo) {
        EvalUser.repo = repo;
    }

    private String evalGroupId;

    private String evalGroupName;
    private String cycleId;//周期id
    private String id;//
    @JSONField(serialize = false)
    private TenantId companyId;//公司id
    private String taskId;//考核任务id
    private String empId;//被考核用户id
    private String avatar;//被考核用户头像
    private String orgId;//
    private String taskStatus;//任务状态
    private Integer subStatus;//任务子状态
    private BigDecimal finalScore;//考核最终评分
    private BigDecimal originalFinalScore;//原考核分数
    private String evaluationLevel;//考评等级
    //private String reviewersJson;//流程责任人列表
    private List<KpiEmp> reviewersJson;//流程责任人列表
    private String selfScoreFlag;//自评完成标识，true标识已完成
    private String manualScoreFlag;//互评完成标识，true表示已完成
    private String superiorScoreFlag;//上级评分完成标识
    private String itemScoreFlag;//指标评分完成标识
    private String resultAuditFlag;//最终结果审核完成标识
    private BigDecimal finalSelfScore;//自评最终得分
    private BigDecimal finalPeerScore;//同级互评最终得分
    private BigDecimal finalSubScore;//下级互评最终得分
    private BigDecimal finalSuperiorScore;//上级评分最终得分
    private BigDecimal finalItemScore;//指定指标评分人最终得分
    private String lastScoreComment;//最近一条评分审核修改理由
    private Date taskConfirmTime;//任务确认时间
    private Date taskScoreStartTime;//进入评分时间
    private BigDecimal finalPlusScore;//最终加分
    private BigDecimal finalSubtractScore;//最终减分
    private String publicFlag;//是否已公示
    private BigDecimal finalSelfPlusScore;//最终自评加分
    private BigDecimal finalPeerPlusScore;//最终同级互评加分
    private BigDecimal finalSubPlusScore;//最终下级互评加分
    private BigDecimal finalSuperiorPlusScore;//最终上级加分
    private BigDecimal finalSelfSubtractScore;//最终自评加减分
    private BigDecimal finalPeerSubtractScore;//最终同级互评减分
    private BigDecimal finalSubSubtractScore;//最终下级互评减分
    private BigDecimal finalSuperiorSubtractScore;//最终上级减分
    private BigDecimal finalItemPlusScore;//指定评分人最终加分
    private BigDecimal finalItemSubtractScore;//指定评分人最终减分
    private BigDecimal finalItemAutoScore;//自动评分指标最终分数
    //******************************************v3版计算优化过度字段********************************//
  // private BigDecimal v3FinalScore;//考核最终评分
    //private BigDecimal v3OriginalFinalScore;//原考核分数

    private BigDecimal v3FinalSelfScore;//自评最终得分
    private BigDecimal v3FinalPeerScore;//同级互评最终得分
    private BigDecimal v3FinalSubScore;//下级互评最终得分
    private BigDecimal v3FinalSuperiorScore;//上级评分最终得分
    private BigDecimal v3FinalItemScore;//定向指标评分人最终得分
    private BigDecimal v3FinalAppointScore;//指定评分人最终得分

    private BigDecimal v3SelfScore;//自评最终得分 未计算环节权重
    private BigDecimal v3PeerScore;//同级互评最终得分 未计算环节权重
    private BigDecimal v3SubScore;//下级互评最终得分 未计算环节权重
    private BigDecimal v3SuperiorScore;//上级评分最终得分 未计算环节权重
    private BigDecimal v3AppointScore;//指定指标评分人最终得分 未计算环节权重

    //******************************************v3版计算优化过度字段********************************//
    private String appealDeadLine;//申诉的截至时间
    private Integer tempTask;       //是否从模板创建任务,旧数据标识=1,新数据=0
    //考核表配置进度, 0=未配置, 100 = 进行中, 101=进行中有异常  200=完成ok
    private Integer ruleConfStatus;
    //101指标确认阶段责任人异常 102执行阶段 103校准阶段 104公示阶段 105申诉阶段 106评分人空缺 , 107:评分人离职,108：面谈【执行人异常】,109：面谈确认人异常
    private String ruleConfError;  //规则配置异常（401：自评 402：上级 403：下级  404：同级 405：指定 406：定向 501：公示范围异常  502：公示发起人异常）
    private String confirmDeadLine;//指标确认截止时间

    //public BigDecimal getFinalItemAutoScore() {
    //    return finalItemAutoScore == null ? BigDecimal.ZERO : finalItemAutoScore;
    //}
    private String enterScoreFlag;//已发起评分操作标识
    private String originalEvaluationLevel;//原始考评等级
    private String distributionFlag;//是否经过正态分布
    private String adjustReason;//调整绩效结果理由
    private BigDecimal totalPointsNum;//任务总积分
    private String signaturePic;//签名图片
    private String itemChangeUser;//指标变更人
    private String hasAppeal;//是否有申诉；true/false
    private String appealReceiverId;//申诉受理人id
    private String ccEmpIds;//评分抄送人、公示抄送人取值地方
    private Date inResultAffirmTime;//进入结果确认的时间
    private String isPublish;//某个任务下的某个被考核人是否可公示

    private Integer isNewEmp = 0;//是否新人任务 默认0=非新人考核任务，1=新人考核任务
    private String empName;//被考核用户名字
    private String empOrgName;//被考核人所属部门。复制时引用最新的部门名称
    private String copyNewEmpOrgName;//复制时引用最新的部门名称
    private String stepId;//等级名称id
    private String distributionBeforeStepId;//正态分布之前的stepId，用来还原等级名称id
    private String distributionEvalLevel; // 正态分布之前的等级
    private String allScored;
    private Integer inputFinishStatus;
    private String evalOrgName;//被考核组织名
    private String copyNewEvalOrgName;//复制时引用最新的部门名称
    private String evalOrgId;//被考核组织id
    private BigDecimal weightOfRef;//'在关联绩效的权重'
    private BigDecimal scoreOfRef;//'关联绩效后的得分
    private String perfCoefficient;//绩效系数
    private String originalPerfCoefficient;//原始绩效系数

    private String atOrgCodePath;//员工所在部门path,或者组织绩效path
    private String atOrgNamePath;//员工所在部门名字path,或者组织绩效名字path
    private Integer atOrgPathHight;//员工所在部门path高度,或者组织绩效的

    private Boolean scoreEndFlag = false;  //是否评分完成
    private Boolean opEmpExistsItemNotSubmitFinishValue = false;//操作人指标是否全部提交
    private boolean refAsk360Flag = false; //关联问卷
    private boolean hasAskEndScore = false; //问卷完成评价
    private Integer mutualEvalComputeType; //互评评分计算方式（0 平均权重，1-平均分）
    private String originalStepId;//原始等级名称id
    private Integer finishValueAuditStatus;//完成值审核状态 完成值审核结果：null-默认（包含历史数据），1-审核通过 ，2-审核驳回
    private Integer changeScene = 0;  //变更环节 1:指标确认  2：指标变更和审核 4：完成值录入  8：完成值审核  16：评分 32：校准 64：面谈 128：公示 256：结果申诉
    private boolean hasChangeOrg;    //变更部门信息
    private List<String> changedAuditScene = new ArrayList<>();
    private String empRank;
    private String empRankId;
    private String empPosition;
    private String empPositionId;
    // KPI类型结果 (key: kpiTypeId, value: 得分/等级)
    @JSONField(serialize = false)
    private List<HistoryEvalImportFields.KpiTypeResult> kpiTypeLevels;

    public void copyOrg(String atOrgCodePath, String orgNamePth, Integer pathHight) {
        this.atOrgCodePath = atOrgCodePath;
        this.atOrgNamePath = orgNamePth;
        this.atOrgPathHight = pathHight;
    }

    public void acceptOrgPath(String orgCode, String orgNamePth, Integer pathHight) {
        this.atOrgCodePath = "|" + orgCode;
        this.atOrgNamePath = orgNamePth;
        this.atOrgPathHight = pathHight;
    }

    //@JSONField(serialize = false, deserialize = false)
    private List<RankRuleScoreRangeSnap> scoreRanges;//等级分值评分范围

    public List<RankRuleScoreRangeSnap> getScoreRanges() {
        return scoreRanges;
    }

    public void setScoreRanges(List<RankRuleScoreRangeSnap> scoreRanges) {
        this.scoreRanges = scoreRanges;
    }

    //组织绩效+新人考核需要 兼容这个字段
    public void scoreRanges(List<ScoreRange> scoreRanges) {
        this.scoreRanges = Convert.toList(RankRuleScoreRangeSnap.class, scoreRanges);
    }

    private Boolean isBatch;        //是否批量发起考核

    private boolean sendMsg = true;

    private String empStatus;

    private String orgConCat;       //员工带有权限部门的拼接
    private String orgConCatAll;    //员工所有部门拼接
    private String taskName;        //任务名称
    private Set<String> resultInputEmpIds;

    //以上是持久状态
    public EvalUser() {
    }

    private String distributionId;

    public boolean hasAppeal() {
        return hasAppeal != null && Boolean.valueOf(hasAppeal);
    }

    //以下是计算状态,
    private EmpEvalRule empEvalRule;
    @Getter
    private List<EvalKpi> kpis;

    @JSONField(serialize = false, deserialize = false)
    private List<EmpEvalKpiType> kpiTypes;

    @JSONField(serialize = false, deserialize = false)
    private List<EvalOkrType> emptyTypes;//360与简易可以有空类型,自定不行
    //查最终结果审核人流程
    @JSONField(serialize = false, deserialize = false)
    @Getter
    private List<EvalAudit> resultAudits;
    //指标审核，此包含了确认
    @JSONField(serialize = false, deserialize = false)
    private List<EvalAudit> modifyItemAudits;
    //执行阶段指标审核
    @JSONField(serialize = false, deserialize = false)
    private List<EvalAudit> changeItemAudits;
    //完成值审核
    @JSONField(serialize = false, deserialize = false)
    private List<EvalAudit> finishValueAudits;
    @Deprecated
    @JSONField(serialize = false, deserialize = false)
    private EvalScoreRule scoreRule;//此字段只存了评分阶段的配置,但audit表也存了. 考虑去掉此表

    @JSONField(serialize = false, deserialize = false)
    @Getter
    private List<EvalAudit> subScorers;
    @JSONField(serialize = false, deserialize = false)
    @Getter
    private List<EvalAudit> peerScorers;

    public void setSupperScorers(List<EvalAudit> supperScorers) {
        this.supperScorers = supperScorers;
    }

    @JSONField(serialize = false, deserialize = false)
    @Getter
    private List<EvalAudit> supperScorers;

    //计算时兼容等级分值问题
    public List<EvalAudit> loadAudits(EvaluateAuditSceneEnum sceneEnum) {
        return repo.loadAudit(this, sceneEnum);
    }

    @JSONField(serialize = false, deserialize = false)
    @Getter
    private List<EvalAudit> appointScorers;

    @JSONField(serialize = false, deserialize = false)
    @Getter
    private List<EvalScoreResult> scoreResults;


    @JSONField(serialize = false, deserialize = false)
    @Getter
    private List<String> resetRaterNameIds = new ArrayList<>();

    public void setScoreResults(List<EvalScoreResult> scoreResults) {
        this.scoreResults = scoreResults;
    }

    @Getter
    private List<EvalAudit> allCustomAudits;

    private Map<String, List<RoleRefEmp>> roleRefEmps;
    private Map<String, List<LevelManager>> levelManagers;

    @JSONField(serialize = false, deserialize = false)
    private String scoreStartRuleType;
    @JSONField(serialize = false, deserialize = false)
    private Integer scoreStartRuleDay;
    private List<MsgTodoInfo> infos = new ArrayList<>(); //恢复终止任务时用于消息通知组装。
    private EmpEvalRule beforeEmpEvalRule;

    //复制考核规则时使用
    private String oldUserId;

    private EvalTaskInterviewConf evalTaskInterview;//面谈

    //批量校准时用
    private Integer resultAuditLevel;  //校准层级
    private List<String> repeatSkipEmpIds;
    private boolean isVacancySkipRater = false;
    private boolean allAutoCompute = false;

    private EvalScorersWrap evalScorersWrap;//评分人

    private String inputFinishChanged;
    private boolean customAuditResultFlow = false;

    public EvalTaskInterviewConf initEvalTaskInterview() {
        this.evalTaskInterview = new EvalTaskInterviewConf(Objects.isNull(this.empEvalRule) ? null : this.empEvalRule.getInterviewConf(), companyId, new EmpId(empId), new EmpId(createdUser));
        evalTaskInterview.isInterview(evaluationLevel, finalScore);
        return evalTaskInterview;
    }

    public void initResultAudits(List<EvalAudit> resultAudits) {
        eachBaseId(resultAudits);
        this.resultAudits = resultAudits;
    }

    public void initModifyItemAudits(List<EvalAudit> modifyItemAudits) {
        eachBaseId(modifyItemAudits);
        this.modifyItemAudits = modifyItemAudits;
    }

    public List<EvalAudit> listConfirmItemAudit() {
        return modifyItemAudits.stream().filter(modifyItemAudit -> !Objects.isNull(modifyItemAudit.getKpiItemId())).collect(Collectors.toList());
    }

    public void initChangeItemAudits(List<EvalAudit> changeItemAudits) {
        eachBaseId(changeItemAudits);
        this.changeItemAudits = changeItemAudits;
    }

    public void initScoreAudits(String needSelfScore, List<EvalAudit> subScorers,
                                List<EvalAudit> peerScorers,
                                List<EvalAudit> supperScorers,
                                List<EvalAudit> appointScorers) {
        eachBaseId(subScorers, 1);
        eachBaseId(peerScorers, 2);
        eachBaseId(supperScorers);
        eachBaseId(appointScorers);
        this.subScorers = removeSelfOpt(subScorers);
        this.peerScorers = removeSelfOpt(peerScorers);
        this.supperScorers = supperScorers;
        this.appointScorers = appointScorers;
        scoreRule = new EvalScoreRule(companyId, taskId, empId, orgId, id);
        scoreRule.setId(UUID.randomUUID().toString());
        scoreRule.setCreatedUser(this.createdUser);
        scoreRule.setNeedSelfScore(Boolean.valueOf(needSelfScore));
        scoreRule.setSuperiorScoreJson(JSONArray.toJSONString(changeAuditInfo(this.supperScorers)));
        scoreRule.setPeerScoreJson(JSONArray.toJSONString(changeAuditInfo(this.peerScorers)));
        scoreRule.setSubScoreJson(JSONArray.toJSONString(changeAuditInfo(this.subScorers)));
    }

    private List<SimpleTaskAudit> changeAuditInfo(List<EvalAudit> peerScorers) {
        return Convert.toList(SimpleTaskAudit.class, peerScorers);
    }

    private List<EvalAudit> removeSelfOpt(List<EvalAudit> subScorers) {
        if (subScorers.isEmpty()) {
            return subScorers;
        }
        final List<EvalAudit> audits = subScorers.stream()
                .filter(audit -> !empId.equals(audit.getApproverInfo())).collect(Collectors.toList());
        if (audits.size() == subScorers.size()) {
            return audits;
        }
        if (CollUtil.isEmpty(audits)) {
            return audits;
        }
        Pecent avgPect = new Pecent(Pecent.ONE_HUNDRED, audits.size());
        for (EvalAudit evalAudit : audits) {
            evalAudit.setWeight(avgPect.getAvgWeight());
        }
        //权重除不尽的差值加到第一位上
        audits.get(0).setWeight(avgPect.getAvgAddDiff());
        return audits;
    }


    public boolean isOpenAvgWeightCompute() {
        //是否开启平均权重计算
        return 0 == mutualEvalComputeType;
    }

    public boolean finalAutoScoreIsNull() {
        return null == this.finalItemAutoScore || BigDecimal.ZERO.compareTo(this.finalItemAutoScore) == 0;
    }

    private void eachBaseId(List<EvalAudit> resultAudits) {
        eachBaseId(resultAudits, null);
    }

    private void eachBaseId(List<EvalAudit> resultAudits, Integer fixedApprovalOrder) {
        if (CollUtil.isEmpty(resultAudits)) {
            return;
        }
        for (EvalAudit audit : resultAudits) {
            audit.setTaskId(taskId);
            audit.setEmpId(empId);
            audit.setOrgId(orgId);
            audit.setCreatedUser(createdUser);
            if (fixedApprovalOrder != null) {
                audit.setApprovalOrder(fixedApprovalOrder);
            }
            if (audit.getApproverType().equals(BusinessConstant.TASK_EMP)) {
                audit.setApproverInfo(empId);
            }
        }
    }

    //@ConstructorProperties({"companyId", "createUser", "taskId", "empId"})
    public EvalUser(TenantId companyId, EmpId createUser, String taskId, String empId) {
        Assert.notBlank(taskId);
        Assert.notBlank(empId);
        Assert.notNull(createUser);
        this.createdUser = createUser.getId();
        this.companyId = companyId;
        this.taskId = taskId;
        this.empId = empId;
        this.taskStatus = TalentStatus.PUBLISHED.getStatus();
        this.createdTime = new Date();
    }

    //@ConstructorProperties({"companyId", "createUser", "taskId", "empId"})
    public EvalUser(TenantId companyId, EmpId createUser, CycleId cycleId) {
        Assert.notNull(companyId);
        Assert.notNull(createUser);
        this.createdUser = createUser.getId();
        this.companyId = companyId;
        this.cycleId = cycleId.getId();
        this.taskStatus = TalentStatus.DRAW_UP_ING.getStatus();
        this.createdTime = new Date();
        this.tempTask = 0;
    }

    public EvalUser(String taskUserid, List<EmpEvalKpiType> kpiTypeList, List<EvalKpi> kpiItemList, TenantId companyId) {
        Assert.notNull(companyId);
        this.id = taskUserid;
        this.kpiTypes = kpiTypeList;
        this.kpis = kpiItemList;
        this.companyId = companyId;
    }

    public void initOnNew(Boolean publicFlag, List<EvalKpi> kpis) {
        this.publicFlag = publicFlag + "";
        this.kpis = kpis;
    }

    public void initEmptyType(List<EvalOkrType> okrTypes) {
        this.emptyTypes = okrTypes;
    }

    public boolean isNew() {
        return this.id == null;
    }

    public void initId(String id) {
        this.id = id;
        eachBaseId(changeItemAudits);
        eachBaseId(modifyItemAudits);
        eachBaseId(resultAudits);
        eachBaseId(subScorers);
        eachBaseId(peerScorers);
        eachBaseId(supperScorers);
        for (EvalKpi kpi : kpis) {
            kpi.initTaskUserId(id);
        }
        for (EvalOkrType okrType : emptyTypes) {
            okrType.initTaskUserId(id);
        }
        if (scoreRule != null) {
            scoreRule.setTaskUserId(id);
        }
    }

    public boolean modifyItemAuditIsEmpty() {
        return CollUtil.isEmpty(modifyItemAudits);
    }

    // 360及简易分发定向评分人 task
    public List<EvalScoreResult> dispatchDirectRaterItem() {
        List<EvalScoreResult> scoreResults = new ArrayList<>();
        for (EvalKpi kpi : lazyLoadKpiItems()) {
            if (kpi.openOkrScore()) {
                continue;
            }
            if (kpi.isDirectional()) {
                kpi.dispatchDirectRaterItem(scoreResults, roleRefEmps, levelManagers);
            }
        }
        acceptScorer(scoreResults);
        return scoreResults;
    }

    // 360及简易分发定自评任务项
    public List<EvalScoreResult> dispatchSelfRaterItem() {
        List<EvalScoreResult> raterItems = new ArrayList<>();
        List<EvalKpi> items = belongItems(SubScoreNodeEnum.SELF_SCORE, null);
        for (EvalKpi kpi : items) {
            if (kpi.openOkrScore()) {
                continue;
            }
            if (kpi.isAutoItem()) {
                continue;
            }
            kpi.dispatchSelfRaterItem(empId, raterItems);
        }
        acceptScorer(raterItems);
        return raterItems;
    }

    public EvalAudit curConfirmItemAuditNode(Integer node) {
        EvalAudit auditModel = getFirstAudit(modifyItemAudits, node);
        return auditModel;
    }

    public EvalAudit curResultAuditingNode(Integer node) {
        EvalAudit auditModel = getFirstAudit(loadResultAudits(), node);
        return auditModel;
    }

    private List<EvalAudit> loadResultAudits() {
        return repo.loadResultAudit(this);
    }

    private EvalAudit getFirstAudit(List<EvalAudit> audits, Integer node) {
        if (CollUtil.isEmpty(audits)) {
            return null;
        }
        audits.stream().sorted(Comparator.comparing(EvalAudit::getApprovalOrder));
        return audits.get(0);
    }

//    @JSONField(serialize = false, deserialize = false)
//    private Boolean isAllAutoType = true;

//    public void forAllKpiAuto(List<EvalKpi> kpis) {
//        for (EvalKpi kpi : kpis) {
//            if (!ScoreTypeEnum.AUTO.getType().equals(kpi.getScorerType())) {
//                isAllAutoType = false;
//                return;
//            }
//        }
//    }

    public Set<String> scoringInputEmp() {
        Set<String> inputEmpIds = new HashSet<>();
        List<EvalKpi> autoItems = getAutoItem();
        autoItems.stream().map(kpi -> inputEmpIds.addAll(kpi.inputEmp(empId))).collect(Collectors.toList());
        return inputEmpIds;
    }

    public Set<String> confirmedInputEmp(boolean openInputOnScoring, LeaderManger leaderMG) {
        log.info("confirmedInputEmp.leaderMG:{},kpis:{}", JSONUtil.toJsonStr(leaderMG), JSONUtil.toJsonStr(kpis));
        Set<String> inputEmpIds = new HashSet<>();
        kpis.stream().filter(i -> !i.isNotInputVal()).forEach(kpi -> inputEmpIds.addAll(kpi.parseInputEmp(empId, leaderMG)));
        return inputEmpIds;

//        if (!openInputOnScoring) {
//            kpis.stream().filter(i -> !"no".equals(i.getResultInputType())).forEach(kpi -> inputEmpIds.addAll(kpi.parseInputEmp(empId, leaderMG)));
//            return inputEmpIds;
//        }
//
//        List<EvalKpi> normalItems = getNormalItemExcloudNoInput();//排除掉自动计算指标和无需录入的
//        if (CollUtil.isNotEmpty(normalItems)) {
//            normalItems.stream().map(kpi -> inputEmpIds.addAll(kpi.inputEmp(empId))).collect(Collectors.toList());
//            return inputEmpIds;
//        }
//        List<EvalKpi> autoItems = getAutoItem();
//        autoItems.stream().map(kpi -> inputEmpIds.addAll(kpi.inputEmp(empId))).collect(Collectors.toList());
//        return inputEmpIds;
    }

    @JSONField(serialize = false)
    public List<EvalKpi> getAutoItem() {
        return this.kpis.stream().filter(evalKpi -> ScoreTypeEnum.AUTO.getType().equals(evalKpi.getScorerType())).collect(Collectors.toList());
    }

    public List<String> onlyInputAutoRaterIds() {
        List<String> autoInputEmpIds = getAutoItem().stream().flatMap(evalKpi -> evalKpi.getInputRaterIds().stream()).distinct().collect(Collectors.toList());
        List<String> normalInputEmpIds = getNormalItem().stream().flatMap(evalKpi -> evalKpi.getInputRaterIds().stream()).distinct().collect(Collectors.toList());
        return CollUtil.subtractToList(autoInputEmpIds, normalInputEmpIds);
    }

    @JSONField(serialize = false)
    public List<EvalKpi> getNormalItem() {
        return this.kpis.stream().filter(evalKpi -> !ScoreTypeEnum.AUTO.getType().equals(evalKpi.getScorerType())).collect(Collectors.toList());
    }

    @JSONField(serialize = false)
    public List<EvalKpi> getNormalItemExcloudNoInput() {
        return this.kpis.stream().filter(evalKpi -> !ScoreTypeEnum.AUTO.getType().equals(evalKpi.getScorerType())
                || !evalKpi.isNotNeedInput()).collect(Collectors.toList());
    }

    public void reviewers(List<KpiEmp> inputEmps) {
        this.reviewersJson = inputEmps;
    }

//    public void addReviewers(List<KpiEmp> addEmps) {
//        List<String> scorerIds = addEmps.stream().map(KpiEmp::getEmpId).collect(Collectors.toList());
//        this.removeReviewers(scorerIds);
//        this.reviewersJson.addAll(addEmps);
//    }


    public void removeReviewers(List<String> scorerIds) {
        if (CollUtil.isEmpty(reviewersJson)) {
            return;
        }
        reviewersJson.removeIf(kpiEmp -> scorerIds.contains(kpiEmp.getEmpId()));
    }

    public void replReviewers(List<KpiEmp> newEmps, String oldEmpId) {
        if (CollUtil.isEmpty(this.reviewersJson)) {
            this.reviewersJson = newEmps;
            return;
        }
        removeReviewers(Arrays.asList(oldEmpId));
        List<String> empIds = reviewersJson.stream().map(s -> s.getEmpId()).collect(Collectors.toList());
        newEmps = newEmps.stream().filter(s -> !empIds.contains(s.getEmpId())).collect(Collectors.toList());
        reviewersJson.addAll(newEmps);
    }

    /**
     * @description:
     * @author: lufei
     * @date: 2022/5/12 09:03
     * @param: [curStatus, reviewers:负责人信息]
     * @return: void
     **/
    public void curStatus(TalentStatus curStatus, List<KpiEmp> reviewers) {
        this.taskStatus = curStatus.getStatus();
        this.reviewers(reviewers);
    }

    @JSONField(serialize = false)
    public boolean isNewEmp() {
        return isNewEmp != null && 1 == isNewEmp;
    }

    public void shortOrg(String orgName, String empName) {
        this.empOrgName = orgName;
        this.empName = empName;
    }

    public void copyAtOrg(String orgId, String orgName, String empId, String empName, String avatar, String empRank, String empPosition) {
        this.orgId = orgId;
        this.empOrgName = orgName;
        this.empId = empId;
        this.empName = empName;
        this.avatar = avatar;
        this.empRank = empRank;
        this.empPosition = empPosition;
    }

    public void evalEmpOrg(String orgId, String orgName, String empId, String empName, String avatar) {
        this.orgId = orgId;
        this.empOrgName = orgName;
        this.empId = empId;
        this.empName = empName;
        this.avatar = avatar;
    }

    //指标空未制定
    @JSONField(serialize = false)
    public boolean kpiItemIsEmpty() {
        return CollUtil.isEmpty(kpis);
    }

    //指标未录入完成值
    @JSONField(serialize = false)
    public boolean kpiItemIsNotInput() {
        //自动评分指标及必填指标
        for (EvalKpi kpi : kpis) {
            if (kpi.isNotInput()) {
                return true;
            }
        }
        return false;
    }

    //指标除不需要录入的指标是否录入完成值
    @JSONField(serialize = false)
    public boolean isNotInputVal() {
        for (EvalKpi kpi : kpis) {
            if (kpi.isNotInputVal()) {
                return true;
            }
        }
        return false;
    }

    //必填指标未录入
    @JSONField(serialize = false)
    public boolean isMustNotInputVal() {
        for (EvalKpi kpi : kpis) {
            if (kpi.isMustNotInputVal()) {
                return true;
            }
        }
        return false;
    }

    //必填指标未提交
    @JSONField(serialize = false)
    public boolean isMustNotSubmitVal() {
        for (EvalKpi kpi : kpis) {
            if (kpi.isMustNotSubmitVal()) {
                return true;
            }
        }
        return false;
    }

    @JSONField(serialize = false)
    public boolean isNotSubmitVal() {
        for (EvalKpi kpi : kpis) {
            if (kpi.isNotSubmitVal()) {
                return true;
            }
        }
        return false;
    }

    @JSONField(serialize = false)
    public boolean isNoInputVal() {
        for (EvalKpi kpi : kpis) {
            if (kpi.isNotNeedInput()) {
                return true;
            }
        }
        return false;
    }

    //是否存在必填指标
    @JSONField(serialize = false)
    public boolean isExistMust() {
        Integer mustSize = CollUtil.filterNew(kpis, k -> k.isMustResultInput()).size();
        return mustSize > 0;
    }

    public void enterScoring() {
        this.taskStatus = TalentStatus.SCORING.getStatus();
        this.subStatus = TalentStatus.SCORING.getSubStatus();
    }

    //自动计算指标算分 来自:EvaluateTaskBiz.handleItemAutoScore
    public boolean tryComputeAutoScore(Boolean typeWeightOpen, BigDecimal fullScore,
                                       Boolean isFullScoreRange, Boolean openItemAutoScoreMultiplWeight) {
        log.info("启动自动计算:tryComputeAutoScore");
        int cnt = 0;
        this.finalItemAutoScore = null;
        List<EvalKpi> autoItems = autoItems();
        if (CollUtil.isEmpty(autoItems)) {
            return false;
        }
        for (EvalKpi kpiItem : autoItems) {
            cnt++;
            try {
                BigDecimal kpiItemAutoScore = kpiItem.computeAuto(typeWeightOpen, fullScore, openItemAutoScoreMultiplWeight, isFullScoreRange);
                this.finalItemAutoScore = FinalWeightSumScore.addNullZero(finalItemAutoScore, kpiItemAutoScore);
            } catch (Exception e) {
                kpiItem.setAutoScoreExFlag(Boolean.TRUE + "");
                kpiItem.setItemAutoScore(BigDecimal.ZERO);
                kpiItem.setItemOriginalScore(BigDecimal.ZERO);
                kpiItem.setItemFinalScore(BigDecimal.ZERO);
                kpiItem.setItemFinalOriginalScore(BigDecimal.ZERO);
                log.warn("自动计算分数异常:kpiItem:{}"  ,JSONUtil.toJsonStr(kpiItem));
                log.error("自动计算分数异常:" + e.getMessage(), e);
            }
        }
        //List<EvalKpi> openOkrScoreItems = openOkrScoreItems();
        return cnt > 0;
    }

    //是否已分发评分人
    public boolean customRaterDispatched() {
        for (EvalAudit allCustomAudit : lazyLoadCustomAudit()) {
            if (allCustomAudit.isDispatched()) {
                return true;
            }
        }
        return false;
    }

    private List<EvalAudit> lazyLoadCustomAudit() {
        if (CollUtil.isEmpty(allCustomAudits)) {
            repo.lazyLoadScoreCustomAuit(this);
        }
        return allCustomAudits;
    }

    //重置给了del=true 的audit 用来标记（解决，重置audit还没删除，后续派发的时间误检 以为派发）
    private void rmIsDelAudit() {
        if (CollUtil.isEmpty(allCustomAudits)) {
            return;
        }
        allCustomAudits = allCustomAudits.stream().filter(audit -> Boolean.FALSE.toString().equals(audit.getIsDeleted())).collect(Collectors.toList());
    }

    public void computeLevel(FinalWeightSumScore sumScore, BigDecimal finalScore, Boolean needComputeLevel) {
        log.info("计算最终得分：{}", finalScore);
        setFinalSelfScore(sumScore.getSelfScore());
        setFinalPeerScore(sumScore.getPeerScore());
        setFinalSubScore(sumScore.getSubScore());
        setFinalSuperiorScore(sumScore.getSuperiorScore());
        this.finalPlusScore = sumScore.getPlusSum();
        this.finalSubtractScore = sumScore.getSubtractSum();
        //计算指标指定评分人 与指定评分共用一个字段，不会同时存在
        setFinalItemScore(sumScore.mergeAppointAndItemScore());
        setOriginalFinalScore(finalScore);
        if (Objects.nonNull(finalScore)) {
            finalScore = finalScore.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        setFinalScore(finalScore);
        setFinalItemAutoScore(sumScore.getFinalAutoSum());
        //等级计算已集中剥离到 DetaLevelComputeDmSvc.
        setAllScored(Boolean.TRUE.toString());
        log.info("计算最终得分2：{}", this.finalScore);
        this.finalScore = null == this.finalScore ? BigDecimal.ZERO : this.finalScore;
        log.info("计算最终得分2如果为null,赋值0，不然全自动计算指标，会再待排名卡流程");
    }


    public void computeFinalScoreV3(TotalFinalNodeScoreV3 sumScore, BigDecimal finalScore) {
        log.info("==computeV3FinalScore计算最终得分：{},sumScore:{}", finalScore, JSONUtil.toJsonStr(sumScore));
        this.v3FinalSelfScore = sumScore.getFinalSelfScore();
        this.v3FinalPeerScore = sumScore.getFinalPeerScore();
        this.v3FinalSubScore = sumScore.getFinalSubScore();
        this.v3FinalSuperiorScore = sumScore.getFinalSuperiorScore();
        this.v3FinalAppointScore = sumScore.getFinalAppointScore();
        //计算指标指定评分人 与指定评分共用一个字段，不会同时存在
        this.v3FinalItemScore = sumScore.getFinalItemScore();
        this.originalFinalScore = finalScore;
        this.finalPlusScore = sumScore.getPlusSum();
        this.finalSubtractScore = sumScore.getSubtractSum();

        this.v3SelfScore = sumScore.getSelfScore();
        this.v3PeerScore = sumScore.getPeerScore();
        this.v3SubScore = sumScore.getSubScore();
        this.v3SuperiorScore = sumScore.getSuperiorScore();
        this.v3AppointScore = sumScore.getAppointScore();

        if (Objects.nonNull(finalScore)) {
            finalScore = finalScore.setScale(2, RoundingMode.HALF_UP);
            this.finalScore = finalScore;
        }
        log.info("==computeV3FinalScore计算最终得分2：{}", this.finalScore);
        this.finalScore = null == this.finalScore ? BigDecimal.ZERO : this.finalScore;
        log.info("==V3FinalTotalNodeScore计算最终得分2如果为null,赋值0，不然全自动计算指标，会再待排名卡流程");
    }

    public void computeFinalNodeScoreV3(TotalFinalNodeScoreV3 sumScore) {
        log.info("==computeFinalNodeScoreV3计算最终得分，sumScore:{}", JSONUtil.toJsonStr(sumScore));
        this.v3FinalSelfScore = sumScore.getFinalSelfScore();
        this.v3FinalPeerScore = sumScore.getFinalPeerScore();
        this.v3FinalSubScore = sumScore.getFinalSubScore();
        this.v3FinalSuperiorScore = sumScore.getFinalSuperiorScore();
        this.v3FinalAppointScore = sumScore.getFinalAppointScore();
        //计算指标指定评分人 与指定评分共用一个字段，不会同时存在
        this.v3FinalItemScore = sumScore.getFinalItemScore();
        this.finalPlusScore = sumScore.getPlusSum();
        this.finalSubtractScore = sumScore.getSubtractSum();

        this.v3SelfScore = sumScore.getSelfScore();
        this.v3PeerScore = sumScore.getPeerScore();
        this.v3SubScore = sumScore.getSubScore();
        this.v3SuperiorScore = sumScore.getSuperiorScore();
        this.v3AppointScore = sumScore.getAppointScore();
        log.info("==computeFinalNodeScoreV3计算最终环节得分2：{}", this.finalScore);
        log.info("==computeFinalNodeScoreV3计算最终环节得分2如果为null,赋值0，不然全自动计算指标，会再待排名卡流程");
    }


    public void computeV1Level() {
        //旧的按分计算等级 v1.0的
        MatchedStep range = new ResultRankInstance(this.scoreRanges)
                .computeLevelStep(this.finalScore);
        setEvaluationLevel(range.getStepName());
        setOriginalEvaluationLevel(range.getStepName());
        setStepId(range.getStepId());
        setPerfCoefficient(range.getPerfCoefficient());
        setOriginalPerfCoefficient(getPerfCoefficient());

    }

    //1.0的数据计算接口
    public void computeFinalScoreAndLevel(FinalWeightSumScore sumScore, BigDecimal finalScore, FinalPlusSubSumScore extratScore) {
        log.info("计算最终得分：{}", finalScore);
        setFinalSelfScore(sumScore.getSelfScore());
        setFinalPeerScore(sumScore.getPeerScore());
        setFinalSubScore(sumScore.getSubScore());
        setFinalSuperiorScore(sumScore.getSuperiorScore());
        setFinalPlusScore(extratScore.getPlusSum());
        setFinalSubtractScore(extratScore.getSubtractSum());
        //计算指标指定评分人 与指定评分共用一个字段，不会同时存在
        setFinalItemScore(sumScore.mergeAppointAndItemScore());
        setOriginalFinalScore(finalScore);
        setFinalScore(finalScore.setScale(2, BigDecimal.ROUND_HALF_UP));
        if (CollUtil.isNotEmpty(openOkrScoreItems())) {
            if (Objects.isNull(this.finalItemAutoScore)) {
                this.finalItemAutoScore = BigDecimal.ZERO;
            }
            if (Objects.isNull(this.originalFinalScore)) {
                this.originalFinalScore = BigDecimal.ZERO;
            }
            openOkrScoreItems().forEach(item -> {
                BigDecimal okrScore = item.getItemAutoScore();
                this.finalItemAutoScore = finalItemAutoScore.add(okrScore);
                this.originalFinalScore = originalFinalScore.add(okrScore);
            });
            this.finalScore = this.originalFinalScore;
        }
        //不再计算等级,等级统一到level阶段进行计算,这只计算分数.
//        ScoreRule rule = loadGradeScoreRule();
//        BaseScoreRange range = rule.matchRange(this.finalScore);
//        setEvaluationLevel(range.getStepName());
//        setOriginalEvaluationLevel(range.getStepName());
//        setStepId(range.getStepId());
        setAllScored(Boolean.TRUE.toString());
        log.info("计算最终得分2：{}", this.finalScore);
        this.finalScore = null == this.finalScore ? BigDecimal.ZERO : this.finalScore;
        log.info("计算最终得分2如果为null,赋值0，不然全自动计算指标，会再待排名卡流程");
    }


//    public void matchGrade() {
//        ScoreRule rule = loadGradeScoreRule();
//        BaseScoreRange range = rule.matchRange(finalScore);
//        setEvaluationLevel(range.getStepName());
//        setOriginalEvaluationLevel(range.getStepName());
//        setStepId(range.getStepId());
//    }

    public void markNoneLevel() {
        setEvaluationLevel("无等级");
        setOriginalEvaluationLevel("无等级");
    }

    //处理问卷得分
    @JSONField(serialize = false)
    public void handleAskScore(List<EmpEvalKpiType> askTypes) {
        if (CollUtil.isEmpty(askTypes)) {
            return;
        }
        this.refAsk360Flag = true;
        for (EmpEvalKpiType askType : askTypes) {
            if (Objects.isNull(askType.getAsk360EvalScore())) {
                return;
            }
        }
        this.hasAskEndScore = true;
        this.computeAskEvalScore(askTypes);
    }

    //问卷是否考核结束
    @JSONField(serialize = false)
    public boolean isAskScoreEnd(List<EmpEvalKpiType> askTypes) {
        if (CollUtil.isEmpty(askTypes)) {
            return true;
        }
        this.refAsk360Flag = true;
        for (EmpEvalKpiType askType : askTypes) {
            if (Objects.isNull(askType.getAsk360EvalScore())) {
                return false;
            }
        }
        return true;
    }

    @JSONField(serialize = false)
    public void computeAskEvalScore(List<EmpEvalKpiType> askTypes) {
        if (CollUtil.isEmpty(askTypes)) {
            return;
        }
        Map<String, BigDecimal> askEvalScoreMap = new HashMap<>();
        for (EmpEvalKpiType askType : askTypes) {
            if (Objects.isNull(askType.getAsk360EvalScore())) {
                return;
            }
            askEvalScoreMap.put(askType.getKpiTypeId(), askType.getAsk360EvalScore());
        }
        if (CollUtil.isEmpty(askEvalScoreMap)) {
            return;
        }
        BigDecimal askEvalScore = BigDecimal.ZERO;
        for (EmpEvalKpiType askType : askTypes) {
            BigDecimal score = askEvalScoreMap.get(askType.getKpiTypeId());
            if (Objects.nonNull(score)) {
                askEvalScore = askEvalScore.add(score.multiply(askType.pecWeight()));
            }
        }
        this.refreshScoreAndLevel(askEvalScore.setScale(5, RoundingMode.DOWN).setScale(4, RoundingMode.HALF_UP));
    }

    @JSONField(serialize = false)
    public void refreshScoreAndLevel(BigDecimal finalScore) {
        if (Objects.isNull(finalScore)) {
            return;
        }
        this.finalScore = this.finalScore.add(finalScore).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.originalFinalScore = this.finalScore;
//        this.refreshGrade();
    }

//    public void refreshGrade() {
//        ScoreRule rule = loadGradeScoreRule();
//        BaseScoreRange range = rule.matchRange(finalScore);
//        setEvaluationLevel(range.getStepName());
//        setOriginalEvaluationLevel(range.getStepName());
//        setStepId(range.getStepId());
//        setPerfCoefficient(range.computeCoefficient(finalScore));
//        setOriginalPerfCoefficient(getPerfCoefficient());
//    }

    public void setGrade(String stepName, String stepId, String perfCoefficient) {
        setEvaluationLevel(stepName);
        setOriginalEvaluationLevel(stepName);
        setStepId(stepId);
        setOriginalStepId(stepId);
        setPerfCoefficient(perfCoefficient);
        setOriginalPerfCoefficient(perfCoefficient);
    }

//    public ScoreRule loadGradeScoreRule() {
//        if (CollUtil.isEmpty(scoreRanges)) {
//            repo.loadGradeScoreRule(this);
//            return new ScoreRule(scoreRanges);
//        }
//        return new ScoreRule(scoreRanges);
//    }

    public void submitScoreLevel(String stepId,String stepName){
        setEvaluationLevel(stepName);
        setOriginalEvaluationLevel(stepName);
        setStepId(stepId);
        setOriginalStepId(stepId);
    }

    public void dispatchLazyLoad() {
        rmIsDelAudit();
        lazyLoadCustomAudit();
        repo.lazyLoadItemRule(this);
    }

    //开启需要上级依次评分，但是上级没有指标评（只有定向评分），supAudit加载之后也是空
    //s3类型评分串行
    public List<EvalScoreResult> dispatchSuperiorInTurnItems(Integer subOrder) {
        if (wasTempTask()) {
            if (CollUtil.isEmpty(supperScorers)) {
                this.supperScorers = repo.loadAudit(this, EvaluateAuditSceneEnum.SUPERIOR_SCORE);
            }
            if (CollUtil.isEmpty(supperScorers)) {
                return new ArrayList<>();
            }
            EvalAudit audit = getCurSupperAuditNode(subOrder);
            List<EvalKpi> normalItems = normalItems();
            List<EvalScoreResult> evalScoreResults = audit.dispatchKpiItems(normalItems, Collections.singletonList(SubScoreNodeEnum.SUPERIOR_SCORE.getScene()));
            acceptScorer(evalScoreResults);
            return evalScoreResults;
        }
        ////第N个子结点的所有指标 新2.0
        //List<EvalScoreResult> results = new ArrayList<>();
        //for (EvalKpi evalKpi : lazyLoadKpiItems()) {
        //    results.addAll(evalKpi.dis2SupperResult(subOrder));
        //    acceptScorer(results);
        //}
        return Collections.emptyList();
    }

    ////同级评 新2.0
    //public List<EvalScoreResult> dis2MuResults(SubScoreNodeEnum muNode) {
    //    List<EvalScoreResult> results = new ArrayList<>();
    //    for (EvalKpi evalKpi : lazyLoadKpiItems()) {
    //        results.addAll(evalKpi.dis2MuPeerResults(muNode));
    //        acceptScorer(results);
    //    }
    //    return results;
    //}

    //同级评-同级评中下级评分完成之后，展开同级评
    public List<EvalScoreResult> dispatchPeerAudit(List<EvalAudit> audits) {
        List<EvalScoreResult> results = new ArrayList<>();
        List<String> nodes = new ArrayList<>();
        nodes.add(SubScoreNodeEnum.PEER_SCORE.getScene());
        nodes.add(SubScoreNodeEnum.SUB_SCORE.getScene());
        for (EvalAudit audit : audits) {
            List<EvalScoreResult> raterItems = audit.dispatchKpiItems(normalItems(), nodes);
            if (CollUtil.isEmpty(raterItems)) {
                continue;
            }
            results.addAll(raterItems);
        }
        acceptScorer(results);
        return results;
    }

    //是否有简易 360的定向指标
    public boolean hasDirectionalItem() {
        lazyLoadKpiItems();
        List<EvalKpi> items = kpis.stream().filter(item -> item.isDirectional()).collect(Collectors.toList());
        return CollUtil.isNotEmpty(items);
    }

    //是否存在完成值审核
    public boolean hasFinishValueAudit(boolean isHavNoPassFinishValueAudit, boolean isReset) {
        if (TalentStatus.FINISH_VALUE_AUDIT.getStatus().equals(this.taskStatus) && isHavNoPassFinishValueAudit) {
            return true;
        }
        if (CollUtil.isEmpty(kpis)) {
            return false;
        }
        //过滤出需要录入的或者okr的指标
        List<EvalKpi> filterKpis = kpis.stream().filter(k -> !k.isNotNeedInput() || k.isOkrType()).collect(Collectors.toList());
        //如果指标上录入完成值都为不需要录入，则不用进入完成值审核阶段
        if (CollUtil.isEmpty(filterKpis)) {
            return false;
        }
        //如果是完成值审核环节，然后是重置的，判定是否开启完成值审核
        if (TalentStatus.FINISH_VALUE_AUDIT.getStatus().equals(this.taskStatus) && isReset
                && isOpenFinishValueAudit(filterKpis)) {
            return true;
        }
        if (TalentStatus.FINISH_VALUE_AUDIT.getStatus().equals(this.taskStatus)) {
            return false;
        }
        return isOpenFinishValueAudit(filterKpis);
    }

    public boolean isOpenFinishValueAudit(List<EvalKpi> filterKpis) {
        //过滤出需要录入的或者okr的指标
        List<EvalKpi> items = filterKpis.stream().filter(EvalKpi::isFinishValueAudit).collect(Collectors.toList());
        return CollUtil.isNotEmpty(items) || (Objects.nonNull(this.empEvalRule) && empEvalRule.getFinishValueAudit().isOpen());
    }

    //是否存在完成值审核 【完成值审核驳回，录入重新校验】 -- 此为特殊情况，环节还是完成值审核
    public boolean checkHasFinishValueAudit() {
        if (CollUtil.isEmpty(kpis)) {
            return false;
        }
        //过滤出需要录入的或者okr的指标
        List<EvalKpi> filterKpis = kpis.stream().filter(k -> !k.isNotNeedInput() || k.isOkrType()).collect(Collectors.toList());
        //如果指标上录入完成值都为不需要录入，则不用进入完成值审核阶段
        if (CollUtil.isEmpty(filterKpis)) {
            return false;
        }
        List<EvalKpi> items = filterKpis.stream().filter(EvalKpi::isFinishValueAudit).collect(Collectors.toList());
        return CollUtil.isNotEmpty(items) || (Objects.nonNull(this.empEvalRule) && empEvalRule.getFinishValueAudit().isOpen());
    }

    public boolean finisheValueAllPass() {
        if (CollUtil.isEmpty(kpis)) {
            return false;
        }
        //过滤出需要录入的或者okr的指标
        List<EvalKpi> filterKpis = kpis.stream().filter(k -> !k.isNotNeedInput() || k.isOkrType()).collect(Collectors.toList());
        if (CollUtil.isEmpty(filterKpis)) {
            return false;
        }
        if (this.isNotSubmitVal()) {
            return false;
        }
        for (EvalKpi kpi : filterKpis) {
            if ((kpi.isFinishValueAudit() || (Objects.nonNull(this.empEvalRule) && empEvalRule.getFinishValueAudit().isOpen()))
                    && kpi.getFinalSubmitFinishValue() == 0 && !kpi.isOkrType()) {
                return false;
            }
        }
        return true;
    }

    private List<EvalKpi> lazyLoadKpiItems() {
        repo.lazyLoadItemRule(this);
        return this.kpis;
    }

    public boolean customAllRaterSubmited() {
        String trueStr = Boolean.TRUE.toString();
        return trueStr.equals(selfScoreFlag)
                && trueStr.equals(manualScoreFlag)
                && trueStr.equals(superiorScoreFlag);
    }

    public boolean allScorerSubmited() {
        String trueStr = Boolean.TRUE.toString();
        return trueStr.equals(selfScoreFlag)
                && trueStr.equals(manualScoreFlag)
                && trueStr.equals(superiorScoreFlag)
                && trueStr.equals(itemScoreFlag);
    }

    public List<EvalKpi> autoItems() {
        List<EvalKpi> items = lazyLoadKpiItems();
        //repo.lazyLoadItemRule(this);//这里和lazyLoadKpiItems是一样的
        List<EvalKpi> collect = items.stream().filter(kpiItem -> kpiItem.isAutoItem()).collect(Collectors.toList());
        return collect;
    }

    public List<EvalKpi> openOkrScoreItems() {
        return kpis.stream().filter(kpiItem -> kpiItem.openOkrScore()).collect(Collectors.toList());
    }

    public List<EvalKpi> normalItems() {
        return lazyLoadKpiItems().stream().filter(item -> item.isNormal()).collect(Collectors.toList());
    }

    public List<EvalKpi> belongItems(SubScoreNodeEnum node, String belongEmpId) {
        return lazyLoadKpiItems().stream()
                .filter(item -> item.belongItem(node, belongEmpId))
                .collect(Collectors.toList());
    }

    public List<EvalKpi> allItems() {
        repo.lazyLoadItemRule(this);
        return kpis;
    }

    public EvalAudit getCurSupperAuditNode(Integer node) {
        return getFirstAudit(supperScorers, node);
    }

    //simpleChain.addNext(SCORING_SELF);
    //simpleChain.addNext(SCORING_APPOINT);
    //simpleChain.addNext(SCORING_PEER);
    //simpleChain.addNext(SCORING_SUP);
    public void finishSelfScore() {
        this.selfScoreFlag = Boolean.TRUE.toString();
    }

    public void finishItemScore() {
        //finishSelfScore();
        this.itemScoreFlag = Boolean.TRUE.toString();
    }

    public void finishManualScore() {
        //finishItemScore();
        finishSelfScore();
        this.manualScoreFlag = Boolean.TRUE.toString();
    }

    public void finishSuperScore() {
        finishManualScore();
        this.superiorScoreFlag = Boolean.TRUE.toString();
    }

    public void finishCustomScore() {
        this.selfScoreFlag = Boolean.TRUE.toString();
        this.itemScoreFlag = Boolean.TRUE.toString();
        this.manualScoreFlag = Boolean.TRUE.toString();
        this.superiorScoreFlag = Boolean.TRUE.toString();
    }

    public void initAppealDeadLine(Integer canAppealDay) {
        if (canAppealDay <= 0) {
            return;
        }
        this.appealDeadLine = new DateTime().plusDays(canAppealDay).toString("yyyy-MM-dd");
        LoggerFactory.getLogger(getClass()).info("结果申诉的截止时间：{}", appealDeadLine);
    }

    public boolean confedScoreNodes() {
        lazyLoadKpiItems();
        for (EvalKpi kpi : kpis) {
            if (kpi.hasItemScoreRule()) {
                return true;
            }
        }
        return false;
    }

    public void resultAffirmStarting(List<KpiEmp> kpiEmps) {
        this.inResultAffirmTime = new Date();
        curStatus(TalentStatus.RESULTS_AFFIRMING, kpiEmps);
    }

    public boolean hasSubAudit() {
        List<EvalAudit> evalAudits = loadAudits(EvaluateAuditSceneEnum.SUB_SCORE);
        return CollUtil.isNotEmpty(evalAudits);
    }

    public boolean hasPeerAudit() {
        List<EvalAudit> evalAudits = loadAudits(EvaluateAuditSceneEnum.PEER_SCORE);
        return CollUtil.isNotEmpty(evalAudits);
    }

    public boolean hasSupperAudit() {
        List<EvalAudit> evalAudits = loadAudits(EvaluateAuditSceneEnum.SUPERIOR_SCORE);
        return CollUtil.isNotEmpty(evalAudits);
    }

    public boolean hasAppointAudit() {
        List<EvalAudit> evalAudits = loadAudits(EvaluateAuditSceneEnum.APPOINT_SCORE);
        return CollUtil.isNotEmpty(evalAudits);
    }

    public boolean confedNoneSupKpi() {
        if (CollUtil.isEmpty(supperScorers)) {
            supperScorers = loadAudits(EvaluateAuditSceneEnum.SUPERIOR_SCORE);
        }
        return CollUtil.isEmpty(supperScorers);
    }

    public void confEvalRuleOk() {
        this.ruleConfStatus = 200;
    }

    public void confEvalRuleErrorOk() {
        this.ruleConfError = null;
    }

    @JSONField(serialize = false, deserialize = false)
    public boolean evalRuleIsErr() {
        return this.ruleConfStatus != 0 && this.ruleConfStatus != 100 && this.ruleConfStatus != 200;
    }

    @JSONField(serialize = false, deserialize = false)
    public boolean evalRuleConfIsErr() {
        return StrUtil.isNotBlank(this.ruleConfError);
    }

    public void confEvalRuleErro(String node) {
        if (AuditEnum.CONFIRM_TASK.getScene().equals(node)) {
            confEvalPublicErro("101");
            return;
        }
        if (AuditEnum.EDIT_EXE_INDI.getScene().equals(node)) {
            confEvalPublicErro("102");
            return;
        }
        if (AuditEnum.FINAL_RESULT_AUDIT.getScene().equals(node)) {
            confEvalPublicErro("103");
        }
//        if (AuditEnum.FINAL_RESULT_AUDIT.getScene().equals(node)) {
//            confEvalPublicErro("103");
//        }
        if (AuditEnum.FINISH_VALUE_AUDIT.getScene().equals(node)) {
            confEvalPublicErro("104");
            return;
        }
        if (AuditEnum.FINAL_RESULT_INTERVIEW_EXCUTE.getScene().equals(node)) {
            confEvalPublicErro("108");
            return;
        }
        if (AuditEnum.FINAL_RESULT_INTERVIEW_CONFIRM.getScene().equals(node)) {
            confEvalPublicErro("109");
            return;
        }
        if (AuditEnum.RESULT_APPEAL.getScene().equals(node)) {
            confEvalPublicErro("601");
            return;
        }
    }


    public void confEvalScoreRuleErro(List<String> scoreErrorNode) {
        if (CollUtil.isEmpty(scoreErrorNode)) {
            return;
        }
        List<String> indexs = new ArrayList<>();
        if (StringUtils.isNotBlank(this.ruleConfError)) {
            indexs.addAll(Arrays.asList(this.ruleConfError.split(",")));
        }
        for (String scoreNode : scoreErrorNode) {
            if ("super".equals(scoreNode)) {
                scoreNode = "superior";
            }
            scoreNode = scoreNode + "_score";
            if (scoreNode.equals(AuditEnum.SELF_SCORE.getScene())) {
                indexs.add("401");
                continue;
            }
            if (scoreNode.equals(AuditEnum.SUPERIOR_SCORE.getScene())) {
                indexs.add("402");
                continue;
            }
            if (scoreNode.equals(AuditEnum.SUB_SCORE.getScene())) {
                indexs.add("403");
                continue;
            }
            if (scoreNode.equals(AuditEnum.PEER_SCORE.getScene())) {
                indexs.add("404");
                continue;
            }
            if (scoreNode.equals(AuditEnum.APPOINT_SCORE.getScene())) {
                indexs.add("405");
                continue;
            }
            if (scoreNode.equals(AuditEnum.ITEM_SCORE.getScene())) {
                indexs.add("406");
                continue;
            }
        }
        this.ruleConfError = CollUtil.isEmpty(indexs) ? null : StringUtils.join(indexs, ",");
    }

    //邀请评分审核人异常
    public void confEvalInviteMutualErro(List<String> inviteMutualAuditErrorNode) {
        if (CollUtil.isEmpty(inviteMutualAuditErrorNode)) {
            return;
        }
        List<String> indexs = new ArrayList<>();
        if (StringUtils.isNotBlank(this.ruleConfError)) {
            indexs.addAll(Arrays.asList(this.ruleConfError.split(",")));
        }
        for (String invite : inviteMutualAuditErrorNode) {
            invite = invite + "_score";
            if (invite.equals(AuditEnum.SUB_SCORE.getScene())) {
                indexs.add("901");
                continue;
            }
            if (invite.equals(AuditEnum.PEER_SCORE.getScene())) {
                indexs.add("902");
                continue;
            }
        }
        this.ruleConfError = CollUtil.isEmpty(indexs) ? null : StringUtils.join(indexs, ",");
    }


    public void confEvalPublicErro(String errorCode) {
        List<String> indexs = new ArrayList<>();
        if (StringUtils.isBlank(errorCode)) {
            return;
        }
        if (StringUtils.isNotBlank(this.ruleConfError)) {
            indexs.addAll(Arrays.asList(this.ruleConfError.split(",")));
        }
        indexs.add(errorCode);
        CollUtil.distinct(indexs);
        this.ruleConfError = CollUtil.isEmpty(indexs) ? null : StringUtils.join(indexs, ",");
    }

    public void deadLineTimeError() {
        this.ruleConfError = "10001";
    }

    public boolean hasEmpEvalRule() {
        return ruleConfStatus > 100;
    }

    //从模板创建的任务
    public boolean wasTempTask() {
        return tempTask == 1;
    }

    public void acceptScorer(Collection<EvalScoreResult> auditTasks) {
        this.auditTasks = auditTasks;
        for (EvalScoreResult auditTask : auditTasks) {
            auditTask.setTaskUserId(id);
            auditTask.setOrgId(orgId);
        }
    }

    public void markScoreFlag() {
        for (EvalScoreResult auditTask : auditTasks) {
            if (auditTask.isSelfNode()) {
                this.selfScoreFlag = Boolean.FALSE.toString();
            }
            if (auditTask.isManualNode()) {
                this.manualScoreFlag = Boolean.FALSE.toString();
            }
            if (auditTask.isSuperiorNode()) {
                this.superiorScoreFlag = Boolean.FALSE.toString();
            }
            if (auditTask.isAppointNode()) {
                this.itemScoreFlag = Boolean.FALSE.toString();
            }
        }
        //如果需此环节,则初始化为false,评分完成则为true. 如果不需要环节则当已完成为true
        this.selfScoreFlag = selfScoreFlag == null ? "true" : selfScoreFlag;
        this.manualScoreFlag = manualScoreFlag == null ? "true" : manualScoreFlag;
        this.superiorScoreFlag = superiorScoreFlag == null ? "true" : superiorScoreFlag;
        this.itemScoreFlag = itemScoreFlag == null ? "true" : itemScoreFlag;
    }

    public boolean wasSelfScoreOk() {
        return Boolean.valueOf(selfScoreFlag);
    }


    public List<EvalKpi> needInputKpis() {
        if (CollUtil.isEmpty(kpis)) {
            return new ArrayList<>();
        }
        List<String> inputType = Arrays.asList("exam", "user");
        return kpis.stream().filter(kpi -> inputType.contains(kpi.getResultInputType())).collect(Collectors.toList());
    }

    public List<String> inputRaterIds() {
        if (CollUtil.isEmpty(kpis)) {
            return new ArrayList<>();
        }
        List<String> inputType = Arrays.asList("exam", "user", "manager", "role");
        List<EvalKpi> inputKpis = kpis.stream().filter(kpi -> inputType.contains(kpi.getResultInputType())).collect(Collectors.toList());
        if (CollUtil.isEmpty(inputKpis)) {
            return new ArrayList<>();
        }
        List<String> raterIdList = new ArrayList<>();
        inputKpis.forEach(kpi -> {
            List<String> raterIds = kpi.getInputRaterIds();
            raterIdList.addAll(raterIds);
        });
        return raterIdList;
    }

    public void canSubmitFinishValue(Boolean enterOnScoring, Boolean autoEnterOnScoring, List<String> submitItemIds) {
        //评分阶段前不限制更新完成值
        if (TalentStatus.beforeScoreStage().contains(taskStatus)) {
            return;
        }
        Boolean hasAutoItem = submitItemsExistAutoItem(submitItemIds);
        boolean hasScored = existSubmittedScore();
        if (!hasAutoItem) {
            //没有自动评分指标，也没有人评过分，还可以更新
            if (!hasScored) {
                return;
            }
            if (!enterOnScoring) {
                throw new KpiI18NException("30001", "有人评分之后不能修改完成值！");
            }
            return;
        }
        //存在自动评分指标
        if (!autoEnterOnScoring) {
            throw new KpiI18NException("30002", "自动评分指标在评分阶段不能修改完成值！");
        }
    }

    public boolean existSubmittedScore() {
        List<EvalScoreResult> scoreResults = getScoreResults();
        if (CollUtil.isEmpty(scoreResults)) {
            return false;
        }
        List<EvalScoreResult> passResults = scoreResults.stream().filter(result -> "pass".equals(result.getAuditStatus())).collect(Collectors.toList());
        if (CollUtil.isEmpty(passResults)) {
            return false;
        }
        List<EvalScoreResult> results = passResults.stream().filter(result -> result.getScorerType().contains("score")).collect(Collectors.toList());
        return CollUtil.isNotEmpty(results);
    }

    @JSONField(serialize = false)
    public boolean isInterviewStage() {
        return TalentStatus.RESULTS_INTERVIEW.getStatus().equals(taskStatus);
    }

    @JSONField(serialize = false)
    public boolean isScoreStage() {
        return TalentStatus.SCORING.getStatus().equals(taskStatus);
    }

    @JSONField(serialize = false)
    public boolean isTerminated() {
        return TalentStatus.TERMINATED.getStatus().equals(taskStatus) || Boolean.valueOf(isDeleted);
    }

    @JSONField(serialize = false)
    public boolean isNotScoreStage() {
        return !TalentStatus.SCORING.getStatus().equals(taskStatus);
    }

    public Boolean submitItemsExistAutoItem(List<String> submitItemIds) {
        List<EvalKpi> kpis = getKpis();
        for (EvalKpi kpi : kpis) {
            if (kpi.isAutoItem() && submitItemIds.contains(kpi.getId())) {
                return true;
            }
        }
        return false;
    }


    public boolean checkIsInputFinishValueEnterScoreType(Integer enterScoreType) {
        if (Objects.isNull(enterScoreType)) {
            return false;
        }
        return 2 == enterScoreType;
    }

    public Boolean opEmpExistsNotAutoItem(String opEmpId) {
        List<EvalKpi> kpis = getKpis();
        boolean isContainEmpId = false;
        boolean isExistsNotAutoItem = false;
        for (EvalKpi kpi : kpis) {
            //指标录入人为空，退出当次循环
            if (StrUtil.isBlank(kpi.getResultInputEmpId())) {
                continue;
            }
            //指标录入人包含当前操作人
            if (kpi.getResultInputEmpId().contains(opEmpId)) {
                isContainEmpId = true;
            }
            //存在非自动录入指标
            if (!kpi.isAutoItem()) {
                isExistsNotAutoItem = true;
            }
            if (isContainEmpId && isExistsNotAutoItem) {
                return true;
            }
        }
        return false;
    }

    public Boolean checkOpEmpExistsItemsNoSubmit(String submitEmpId, List<String> submitItemIds) {
        List<EvalKpi> kpis = getKpis();
        List<String> notSubItemIds = new ArrayList<>();
        for (EvalKpi kpi : kpis) {
            //指标录入人为空，退出当次循环
            if (StrUtil.isBlank(kpi.getResultInputEmpId())) {
                continue;
            }
            //指标录入人包含当前操作人，且指标非最终提交
            if (kpi.getResultInputEmpId().contains(submitEmpId) && !kpi.isFinalSubmit()) {
                notSubItemIds.add(kpi.getKpiItemId());
            }
        }

//        if (CollUtil.isEmpty(notSubItemIds)) {
//            return true;
//        }
        return submitItemIds.size() < notSubItemIds.size();
    }

    public Boolean canSubmitIfExistAutoItem(List<String> submitItemIds, Boolean autoEnterOnScoring) {
        Boolean exist = submitItemsExistAutoItem(submitItemIds);
        if (exist) {
            if (isScoreStage()) {
                return autoEnterOnScoring;
            }
            return true;
        }
        return true;
    }

    @JSONField(serialize = false)
    public Boolean isNextStage() {
        log.info("allScored = " + Boolean.TRUE.toString().equals(allScored));
        log.info("autoItemExistNoInput = " + !autoItemExistNoInput());
        log.info("taskStatus = " + taskStatus);
        return Boolean.TRUE.toString().equals(allScored) && (!autoItemExistNoInput()) && TalentStatus.SCORING.getStatus().equals(taskStatus);
    }

    public boolean autoItemExistNoInput() {
        List<EvalKpi> autoItems = kpis.stream().filter(kpi -> ScoreTypeEnum.AUTO.getType().equals(kpi.getScorerType())).collect(Collectors.toList());
        if (CollUtil.isEmpty(autoItems)) {
            return false;
        }
        List<EvalKpi> noInputItems = autoItems.stream().filter(item -> (Objects.isNull(item.getItemFinishValue()) || Objects.isNull(item.getItemAutoScore())))
                .collect(Collectors.toList());
        return CollUtil.isNotEmpty(noInputItems);
    }

    public void updateInputFinishStatus(Integer inputFinishStatus) {
        this.inputFinishStatus = inputFinishStatus;
    }

    public void updateInputFinishStatus() {
        if (kpis.isEmpty() || StrUtil.isEmpty(this.taskStatus) || TalentStatus.noInputStatus(this.taskStatus)) {
            this.inputFinishStatus = null;
            return;
        }
        if (TalentStatus.PUBLISHED.getStatus().equals(this.taskStatus)) {
            this.inputFinishStatus = null;
        }
        if (kpis.stream().allMatch(kpi -> kpi.isNotNeedInput())) {
            this.inputFinishStatus = InputFinishStatusEnum.UN_NEED_INPUT.getStatus();
            return;
        }
        if (this.kpis.stream().allMatch(kpi -> Objects.isNull(kpi.getItemFinishValue()) && Objects.isNull(kpi.getItemFinishValueText()) && !kpi.isWorkItemFinished())) {
            this.inputFinishStatus = InputFinishStatusEnum.NO_INPUT.getStatus();
            return;
        }
        if (this.kpis.stream().filter(kpi -> !kpi.isNotNeedInput()).allMatch(kpi -> Objects.nonNull(kpi.getItemFinishValue())
                || Objects.nonNull(kpi.getItemFinishValueText()) || kpi.isWorkItemFinished())) {
            this.inputFinishStatus = InputFinishStatusEnum.ALL_INPUT.getStatus();
            return;
        }
        this.inputFinishStatus = InputFinishStatusEnum.PORTION_INPUT.getStatus();
    }

    public void resetFinalScoreAndLevel() {
        this.finalScore = this.originalFinalScore;
        this.perfCoefficient = this.originalPerfCoefficient;
        this.evaluationLevel = this.originalEvaluationLevel;
        this.stepId = this.originalStepId;
        //无需反向推导等级id了, 重新进入评分中结束后,会再次正向计算等级,所以去掉
//        if (StrUtil.isNotBlank(originalEvaluationLevel)) {
//            BaseScoreRange range = loadGradeScoreRule().matchLevelByStepName(originalEvaluationLevel);
//          if(Objects.nonNull(range)){
//                setStepId(range.getStepId());
//            }
//        }
    }

    public void initAudits(List<EvalAudit> audits) {
        modifyItemAudits = audits.stream().filter(a -> AuditEnum.CONFIRM_TASK.getScene().equals(a.getScene())).collect(Collectors.toList());
        changeItemAudits = audits.stream().filter(a -> AuditEnum.EDIT_EXE_INDI.getScene().equals(a.getScene())).collect(Collectors.toList());
        finishValueAudits = audits.stream().filter(a -> AuditEnum.FINISH_VALUE_AUDIT.getScene().equals(a.getScene())).collect(Collectors.toList());
        resultAudits = audits.stream().filter(a -> AuditEnum.FINAL_RESULT_AUDIT.getScene().equals(a.getScene())).collect(Collectors.toList());
    }


    public List<EvalScoreResult> otherRaterWaitResults(EmpId opEmpId) {
        List<EvalScoreResult> opEmpResult = scoreResults.stream().filter(re -> re.getScorerId().equals(opEmpId.getId())).collect(Collectors.toList());
        String reviewersType = opEmpResult.get(0).getReviewersType();
        //如果当前这个人是会签，其他人相当于不需要确认
        if ("or".equals(reviewersType)) {
            return new ArrayList<>();
        }
        return scoreResults.stream().filter(r -> notEquals(opEmpId, r.getScorerId()) && waitAudit(r.getAuditStatus()))
                .collect(Collectors.toList());
    }

    private boolean waitAudit(String auditStatus) {
        return Objects.isNull(auditStatus);
    }

    public boolean notEquals(EmpId opEmpId, String scoreId) {
        return !opEmpId.getId().equals(scoreId);
    }

    public EvalAudit getNextNodeAudit(EmpId opEmpId, EvaluateAuditSceneEnum sceneEnum) {
        loadAudits(sceneEnum);
        List<EvalScoreResult> opEmpResult = scoreResults.stream().filter(re -> re.getScorerId().equals(opEmpId.getId())).collect(Collectors.toList());
        String taskAuditId = opEmpResult.get(0).getTaskAuditId();
        List<EvalAudit> evalAudits = modifyItemAudits.stream()
                .filter(audit -> Objects.isNull(audit.getStatus()))
                .filter(audit -> !taskAuditId.equals(audit.getId()))
                .sorted(Comparator.comparing(EvalAudit::getApprovalOrder)
                        .reversed())
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(evalAudits)) {
            return null;
        }
        return evalAudits.get(0);
    }

    public void confirmed() {
        this.taskStatus = TalentStatus.CONFIRMED.getStatus();
    }

    public boolean onReScoring() {
        TalentStatus talentStatus = TalentStatus.statusOf(taskStatus);
        if (talentStatus == null) {
            return false;
        }
        return talentStatus == TalentStatus.RESULTS_AUDITING
                || talentStatus == TalentStatus.WAIT_PUBLISHED
                || talentStatus == TalentStatus.SCORING;
    }

    public boolean onScoring() {
        TalentStatus talentStatus = TalentStatus.statusOf(taskStatus);
        if (talentStatus == null) {
            return false;
        }
        return talentStatus == TalentStatus.SCORING;
    }

    @JSONField(serialize = false)
    public boolean isSelf(String empId) {
        return StrUtil.equals(this.empId, empId);
    }

    @JSONField(serialize = false)
    public boolean isAppointStage(String status) {
        return status.equals(taskStatus);
    }

    //public void auditScore() {
    //    if (StrUtil.isBlank(stepId)) {
    //        return;
    //    }
    //    if (CollUtil.isEmpty(scoreRanges)) {
    //        repo.loadGradeScoreRule(this);
    //    }
    //    List<ScoreRange> scoreRangeList = scoreRanges.stream().filter(s -> stepId.equals(s.getStepId())).collect(Collectors.toList());
    //    if (CollUtil.isEmpty(scoreRangeList)) {
    //        return;
    //    }
    //    ScoreRange scoreRange = scoreRangeList.get(0);
    //    this.evaluationLevel = scoreRange.getStepName();
    //    this.stepId = scoreRange.getStepId();
    //}

    public void auditResult(TenantId tenantId, EmpId opEmpId, BigDecimal score,
                            String evaluationLevel, String stepId, String perfCoefficient, String scoreComment) {
        if (TalentStatus.statusOf(taskStatus) != TalentStatus.RESULTS_AUDITING) {
            throw new KpiI18NException("resultsAuditing.errStatus", "不是校准阶段,无需操作");
        }
        this.companyId = tenantId;
        this.updatedUser = opEmpId.getId();
        this.finalScore = score;
        this.evaluationLevel = evaluationLevel;
        this.stepId = stepId;
        this.removeReviewers(Arrays.asList(opEmpId.getId()));
        this.perfCoefficient = perfCoefficient;
        this.lastScoreComment = scoreComment;
    }

    public void auditResult(ResultAudit resultAudit) {
        if (TalentStatus.statusOf(taskStatus) != TalentStatus.RESULTS_AUDITING) {
            throw new KpiI18NException("resultsAuditing.errStatus", "不是校准阶段,无需操作");
        }
        this.companyId = resultAudit.getCompanyId();
        this.updatedUser = resultAudit.getOwnerEmpId();
        this.finalScore = resultAudit.getFinalScore();
        this.evaluationLevel = resultAudit.getEvaluationLevel();
        this.stepId = resultAudit.getStepId();
        this.removeReviewers(Arrays.asList(resultAudit.getOwnerEmpId()));
        this.perfCoefficient = resultAudit.getPerfCoefficient();
        this.lastScoreComment = resultAudit.getScoreComment();
    }

    public void submitItems(List<EmpEvalKpiType> submitTypes) {
        if (CollUtil.isEmpty(submitTypes)) {
            return;
        }
        List<EvalKpi> kpis = new ArrayList<>();
        for (EmpEvalKpiType submitType : submitTypes) {
            submitType.refEmpId(taskId, empId);
            List<? extends EvalKpi> subItems = submitType.getItems();
            if (CollUtil.isEmpty(subItems)) {
                continue;
            }
            kpis.addAll(subItems);
        }
        this.kpis = kpis;
        this.kpiTypes = submitTypes;
        if (Objects.nonNull(this.empEvalRule)) {
            this.empEvalRule.setKpiTypes(new KpiListWrap(kpiTypes)); //为了构建索引
        }
    }

    @JSONField(serialize = false)
    public boolean isTypeWeightOpen() {
        return empEvalRule.getTypeWeightConf().isOpen();
    }

    @JSONField(serialize = false)
    public boolean isCreatedStage() {
        return StrUtil.isBlank(this.taskStatus) || StrUtil.equals(TalentStatus.CREATED.getStatus(), this.taskStatus);
    }

    public String empEvalResultName() {
        StringBuilder builder = new StringBuilder();
        if (this.finalScore != null) {
            builder.append(this.finalScore.stripTrailingZeros());
        }
        builder.append("分/");
        builder.append(this.evaluationLevel);
        return builder.toString();
    }

    public String pointName() {
        if (this.totalPointsNum == null) {
            return StrUtil.EMPTY;
        }
        return "/积分" + this.totalPointsNum.stripTrailingZeros();
    }


    public boolean timeOutAppealDeadLine() {
        if (null == appealDeadLine) {
            return false;
        }
        long deadLime = DateUtil.parse(appealDeadLine).getTime();
        String time = DateUtil.format(new Date(), "yyyy-MM-dd");
        long now = DateUtil.parse(time).getTime();
        return now > deadLime;
    }

    public void rmItemRule() {
        if (CollUtil.isEmpty(kpis)) {
            return;
        }
        kpis.forEach(kpi -> {
            kpi.rmItemRule();
        });
    }

    public void initConfirmDeadLine(Integer limitDays) {
        if (limitDays == null) {
            confirmDeadLine = "";
            return;
        }
        DateTime toDate = new DateTime().plusDays(limitDays);
        confirmDeadLine = toDate.toString("yyyy-MM-dd");
    }

    //public void syncSubmitedScore(BigDecimal finalScore, String evaluationLevel, String stepId) {
    //    this.finalScore = finalScore;
    //    this.evaluationLevel = evaluationLevel;
    //    this.stepId = stepId;
    //}

    public boolean hasNameEmptyRaters() {
        return CollUtil.isNotEmpty(resetRaterNameIds);
    }

    public void endAppointScore() {
        for (EvalKpi kpi : kpis) {
            //整个指标都已经评分
            if (kpi.itemScored()) {
                continue;
            }
            //整个指标没有任何人评分
            if (kpi.notAnyRaterSubmited()) {
                kpi.submitScoreZero();
                finishCustomScore();
                continue;
            }
            //部分人评分
            kpi.reComputeItemWeight();
            kpi.reComputeRaterWeight();
        }
    }

    @JSONField(serialize = false)
    public List<String> getAppointRaters() {
        List<String> raters = new ArrayList<>();
        for (EvalKpi kpi : kpis) {
            raters.addAll(kpi.getAppointRaters());
        }
        return raters;
    }

    @JSONField(serialize = false)
    public List<EvalScoreResult> getItemResults() {
        List<EvalScoreResult> subNodes = new ArrayList<>();
        for (EvalKpi kpi : kpis) {
            subNodes.addAll(kpi.getSubNodes());
        }
        return subNodes;
    }

    public void checkEndScore(Boolean custom, Boolean onlyAppointScore) {
        if (!custom) {
            throw new KpiI18NException("100000", "非自定义考核不支持");
        }
        if (!TalentStatus.SCORING.getStatus().equals(taskStatus)) {
            throw new KpiI18NException("100001", "非评分阶段不支持");
        }
        if (!onlyAppointScore) {
            throw new KpiI18NException("100002", "除了指定评分环节，还存在其他评分环节，不支持");
        }
        //指定公司可以用这个接口 xzl todo
        //if(){
        //
        //}
    }

    public boolean afterStage(TalentStatus apiStatus) {
        return TalentStatus.statusOf(taskStatus).afterEq(apiStatus);
    }

    public boolean beforeStage(TalentStatus apiStatus) {
        return TalentStatus.statusOf(taskStatus).before(apiStatus);
    }

    public boolean eqStage(TalentStatus apiStatus) {
        return TalentStatus.statusOf(taskStatus) == apiStatus;
    }

    public boolean evalOrg() {
        return StrUtil.isNotBlank(evalOrgId);
    }

    public void addEvalEmp(String newOrgOwnerId, String newOrgOwnerOrgId, String newOrgOwnerOrgName,
                           String newOrgOwnerAvatar, String newOrgOwnerName) {
        this.empId = newOrgOwnerId;
        this.orgId = newOrgOwnerOrgId;
        this.empOrgName = newOrgOwnerOrgName;
        this.avatar = newOrgOwnerAvatar;
        this.empName = newOrgOwnerName;
    }

    public void checkExistEvalEmp() {
        if (StrUtil.isEmpty(empId)) {
            if (evalOrg()) {
                throw new KpiI18NException("40005", "请先配置部门负责人");
            }
            throw new KpiI18NException("40006", "被考核人为空");
        }
    }

    public void auditResult(BigDecimal score, String evaluationLevel, String adjustReason, String stepId, String perfCoefficient) {
        this.finalScore = score;
        //this.originalFinalScore = score;
        this.evaluationLevel = evaluationLevel;
        //this.originalEvaluationLevel = evaluationLevel;
        this.adjustReason = adjustReason;
        this.stepId = stepId;
        this.perfCoefficient = perfCoefficient;
    }

    public void clearScoreReviewersJson() {
        if (CollUtil.isEmpty(reviewersJson)) {
            return;
        }
        this.reviewersJson.clear();
    }
    public void clearScoreFlag() {
        this.finalScore = null;
        this.evaluationLevel = null;
        this.selfScoreFlag = null;
        this.manualScoreFlag = null;
        this.superiorScoreFlag = null;
        this.itemScoreFlag = null;
        this.resultAuditFlag = null;
        this.finalSelfScore = null;
        this.finalPeerScore = null;
        this.finalSubScore = null;
        this.finalSuperiorScore = null;
        this.finalItemScore = null;
        this.finalPlusScore = null;
        this.finalSubtractScore = null;
        this.distributionFlag = null;
        this.enterScoreFlag = null;
        this.publicFlag = null;
        this.finalItemAutoScore = null;
        this.finalSelfPlusScore = null;
        this.finalSelfSubtractScore = null;
        this.finalSuperiorPlusScore = null;
        this.finalSuperiorSubtractScore = null;
        this.perfCoefficient = null;
        this.scoreEndFlag = false;

        this.v3FinalSelfScore = null;
        this.v3FinalPeerScore = null;
        this.v3FinalSubScore = null;
        this.v3FinalSuperiorScore = null;
        this.v3FinalItemScore = null;
        this.v3FinalAppointScore = null;

        this.v3SelfScore = null;
        this.v3PeerScore = null;
        this.v3SubScore = null;
        this.v3SuperiorScore = null;
        this.v3AppointScore = null;
    }

    /**
     * 清理互评邀请的评分人
     * - 自定义模式下：清理类型维度与指标维度的 Peer/Sub Rater
     * - 非自定义模式下：清理 S3（顶层）Peer/Sub Rater
     * 注意：保持业务语义不变，并修复遇到 itemScoreRule==null 时提前 return 的问题
     */
    public void clearInviteMutualEmp() {
        if (Objects.isNull(this.empEvalRule)) {
            return;
        }

        // 自定义模式：类型维度 + 指标维度
        if (this.empEvalRule.isCustom()) {
            // 类型维度：Peer/Sub
            for (EmpEvalKpiType kpiType : kpiTypes) {
                clearRatersIfOpen(kpiType.getPeerRater());
                clearRatersIfOpen(kpiType.getSubRater());
            }
            // 指标维度：Peer/Sub
            for (EvalKpi kpi : this.kpis) {
                EvalItemScoreRule itemScoreRule = kpi.getItemScoreRule();
                if (itemScoreRule == null) {
                    // 原实现这里是 return，会中断后续清理；按质量提升改为跳过当前指标
                    continue;
                }
                clearRatersIfOpen(itemScoreRule.getPeerRater());
                clearRatersIfOpen(itemScoreRule.getSubRater());
            }
            return;
        }

        // 非自定义模式：S3 顶层 Peer/Sub
        clearRatersIfOpen(this.empEvalRule.getS3PeerRater());
        clearRatersIfOpen(this.empEvalRule.getS3SubRater());
    }

    // 私有方法：统一清理逻辑，满足单一职责与逻辑复用
    private void clearRatersIfOpen(MutualNodeConf rater) {
        if (Objects.isNull(rater)) {
            return;
        }
        if (!rater.isOpen()) {
            return;
        }
        // 仅当存在 Appointer 时才认为是“邀请/指派”的场景，清空 Raters
        if (Objects.nonNull(rater.getAppointer())) {
            rater.setRaters(Collections.emptyList());
        }
    }

    public void appedCopyEvalOrg(String evalOrgId, String evalOrgName) {
        this.evalOrgId = evalOrgId;
        this.evalOrgName = evalOrgName;
    }

    public void cleanInputTab() {
        if (CollUtil.isEmpty(kpis)) {
            return;
        }
        for (EvalKpi kpi : kpis) {
            kpi.setFinalSubmitFinishValue(0);
            kpi.setFinishValueAuditStatus(0);
            kpi.setFinishValueAuditReason(null);
        }
    }

    public void regainInput(ListWrap<EvalKpiInputBak> inputBakWrap) {
        if (CollUtil.isEmpty(kpis) || CollUtil.isEmpty(inputBakWrap.getDatas())) {
            return;
        }
        for (EvalKpi kpi : kpis) {
            List<EvalKpiInputBak> inputBaks = inputBakWrap.groupGet(kpi.getKpiItemId());
            if (CollUtil.isEmpty(inputBaks)) {
                continue;
            }
            kpi.setResultInputType(inputBaks.get(0).getResultInputType());
            String inputEmps = inputBaks.get(0).getResultInputEmpId();
            kpi.setResultInputEmpId(inputEmps == null ? "" : inputEmps);
        }
    }


    public void ifInitConfirmDealLine(Integer confirmLTDay) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DAY_OF_MONTH, confirmLTDay);
        confirmDeadLine = DateUtil.format(calendar.getTime(), "yyyy-MM-dd");
    }

    //单元测试：ResetNodeCheckTest
    public boolean notExistResetNode(String resetNode) {
        //当前任务所处环节在重置的环节之前，不能向后重置
        if (TalentStatus.statusOf(taskStatus).before(TalentStatus.statusOf(resetNode))) {
            return true;
        }
        EmpEvalRule empEvalRule = this.empEvalRule;
        if (Objects.isNull(empEvalRule)) {
            log.info("考核规则不存在！");
            return false;
        }
        //默认都会有执行中和评分中的流程，所以只需要检查指标确认和校准环节是否可以重置
        if (resetNode.equals(TalentStatus.CONFIRMING.getStatus())) {
            return !empEvalRule.getConfirmTask().isOpen();
        }
        if (resetNode.equals(TalentStatus.RESULTS_AUDITING.getStatus())) {
            return !empEvalRule.getAuditResult().isOpen();
        }
        return false;
    }

    /**
     * 处理纬度与指标字段字定义设置
     */
    public void builderUsedField(List<KpiTypeUsedField> typeUsedFieldList, List<KpiItemUsedField> itemUsedFieldList) {
        if (CollUtil.isNotEmpty(this.kpiTypes) && CollUtil.isNotEmpty(typeUsedFieldList)) {
            Map<String, List<KpiTypeUsedField>> groupMap = typeUsedFieldList.stream()
                    .collect(Collectors.groupingBy(KpiTypeUsedField::getKpiTypeId));
            this.kpiTypes.forEach(type -> {
                if (groupMap != null && groupMap.size() > 0 && groupMap.get(type.getKpiTypeId()) != null) {
                    type.setKpiTypeUsedFields(groupMap.get(type.getKpiTypeId()));
                }
                builderItemUsedField(type, itemUsedFieldList);
            });
        }
    }

    public void builderItemUsedField(EmpEvalKpiType type, List<KpiItemUsedField> itemUsedFieldList) {
        if (CollUtil.isNotEmpty(type.getItems()) && CollUtil.isNotEmpty(itemUsedFieldList)) {
            Map<String, List<KpiItemUsedField>> itemGroupMap = itemUsedFieldList.stream()
                    .collect(Collectors.groupingBy(KpiItemUsedField::getKpiItemId));
            type.getItems().forEach(item -> {
                if (itemGroupMap.get(item.getKpiItemId()) != null) {
                    List<KpiItemUsedField> itemUsedFields = itemGroupMap.get(item.getKpiItemId());
                    List<ItemCustomFieldValue> fieldValueList = new ArrayList<>();
                    if (itemUsedFields != null && itemUsedFields.size() > 0) {
                        itemUsedFields.forEach(obj -> {
                            ItemCustomFieldValue fieldValue = new ItemCustomFieldValue();
                            BeanUtils.copyProperties(obj, fieldValue);
                            fieldValue.setFieldName(obj.getName());
                            fieldValue.setFieldValue(obj.getValue());
                            fieldValue.setFieldStatus(obj.getStatus());
                            fieldValue.setId(obj.getFieldId());
                            fieldValue.setIsReq(obj.getReq());
                            fieldValueList.add(fieldValue);
                        });
                        item.setFieldValueList(fieldValueList);
                    }
                }
            });
        }
    }

    public Boolean notExistItems() {
        for (EmpEvalKpiType kpiType : this.kpiTypes) {
            if (CollUtil.isEmpty(kpiType.getItems()) && !kpiType.isOkr()) {
                return true;
            }
        }
        return false;
    }

    public void formatPerfCoefficient() {
        if (Objects.nonNull(this.perfCoefficient) && StrUtil.isNotBlank(this.perfCoefficient)) {
            BigDecimal coefficient = new BigDecimal(this.perfCoefficient);
            //系数如果小数位后面是0，需要去掉 再显示
            this.perfCoefficient = coefficient.stripTrailingZeros().toPlainString();
        }
    }

    public String matchDeadLineEndDate(String matchStatus) {
        if (Objects.isNull(empEvalRule)) {
            return null;
        }
        return empEvalRule.getDeadLineEndDate(matchStatus);
    }

    public void checkDeadLineError(boolean hasError) {
        if (hasError) {
            this.ruleConfError = "10001";
        } else {
            if (!"10001".equals(ruleConfError)) {
                return;
            }
            this.ruleConfError = null;
        }
    }

    public boolean hasEmptyType() {
        for (EmpEvalKpiType type : kpiTypes) {
            if (type.isOkr()) {
                continue;
            }
            List<? extends EvalKpi> items = type.getItems();
            if (items.isEmpty()) {
                log.info("非okr类别下指标为空，不进行跳过！");
                return true;
            }
        }
        return false;
    }

    public boolean hasError() {
        return this.ruleConfStatus != 200 || StringUtils.isNotBlank(this.ruleConfError);
    }


    //检查考核任务的管理范围
    public boolean hasEvalAdminScopeOrg() {
        return !hasEmpQuit() && !hasEmpDisable() && hasEqEvalOrgId();
    }

    public boolean hasEmpError() {
        return hasEmpQuit() || hasEmpDisable();
    }

    //员工是否已离职
    public boolean hasEmpQuit() {
        return Objects.equals(this.empStatus, "quit");
    }

    //员工是否已禁用
    public boolean hasEmpDisable() {
        return Objects.equals(this.empStatus, "leave");
    }

    //不在管理范围
    public boolean hasNotScope() {
        log.info("员工所属部门={}", this.orgConCatAll);
        return StrUtil.splitTrim(this.orgConCatAll, ",").contains(this.orgId) && !StrUtil.splitTrim(this.orgConCat, ",").contains(this.orgId);
    }

    //考核任务所属部门是否变更
    public boolean hasChangeDept() {
        return !hasEqEvalOrgId();
    }

    //管理范围内包含考核任务部门
    public boolean hasEqEvalOrgId() {
        List<String> orgIds = StrUtil.splitTrim(this.orgConCat, ",").stream().filter(org -> org.equals(this.orgId)).collect(Collectors.toList());
        return orgIds.size() > 0;
    }

    public String getDeadLineEndDate(String taskStatus) {
        if (Objects.nonNull(this.empEvalRule)) {
            return this.empEvalRule.getDeadLineEndDate(taskStatus);
        }
        return null;
    }

    /**
     * 截止时间拼接
     */
    public String deadLineStr(String taskStatus) {
        if (Objects.nonNull(this.empEvalRule)) {
            return this.empEvalRule.deadLineJoinStr(taskStatus);
        }
        return null;
    }


    /**
     * 执行环节是否自动进入评分
     *
     * @param cycleEndDate 周期结束日期
     * @return
     */
    @JSONField(serialize = false)
    public boolean confirmedAutoEnterScore(String cycleEndDate, boolean isReset, EmpEvalMerge evalMerge) {
        if (!isAutoEnter()) {
            return false;
        }
        //停留当前阶段
//        if (isConfirmDStay()) {
//            return false;
//        }
        //存在未导入指标的类别
        if (hasNotImportType()) {
            return false;
        }
        //未指定互评人
        if (evalMerge.notSetMutualScorer()) {
            return false;
        }
        String date = isDeadLineOpen() ? getDeadLineEndDate(TalentStatus.CONFIRMED.getStatus()) : DateTimeUtils.date2StrDate(DateTimeUtils.getDateMoveDay(DateTimeUtils.str2Date(cycleEndDate, "yyyy-MM-dd"), getScoreStartDateTime()));
        boolean isOvertime = isOvertime(date);
        if (!isOvertime) {
            return false;
        }
        //重置
        if (isReset) {
            return false;
        }
        if (hasFinishValueAudit(false, false)) {
            return false;
        }
//        //从FinishedValueAuditEvent处理中移过来
//        if (!finisheValueAllPass()) {
//            return false;
//        }
        return isExistMust() ? !isMustNotInputVal() : !isNotInputVal();
    }

    @JSONField(serialize = false)
    public boolean isAutoEnter() {
        return this.empEvalRule.getEnterScore().isAutoEnter() || isDeadLineAutoEnter();
    }

    @JSONField(serialize = false)
    public boolean isDeadLineAutoEnter() {
        return isDeadLineOpen() /*&& !isConfirmDStay()*/;
    }

    @JSONField(serialize = false)
    public boolean isDeadLineOpen() {
        if (Objects.nonNull(this.empEvalRule) && Objects.nonNull(this.empEvalRule.getDeadLineConf())) {
            if (this.empEvalRule.getDeadLineConf().isOpen()) {
                return true;
            }
        }
        return false;
    }

    @JSONField(serialize = false)
    public boolean isConfirmDStay() {
        if (!isDeadLineOpen()) {
            return false;
        }
        //停留当前阶段
        return this.empEvalRule.getDeadLineConf().getTaskConfirmDeadLine().getAutoSkip() == 0;
    }

    @JSONField(serialize = false)
    public boolean isConfirmedEnterScore(String cycleEndDate) {
        if (!isDeadLineOpen()) {
            log.info(" isConfirmedEnterScore.未开启截止超时，可自动进入评分---");
            return true;
        }

        // String date = isDeadLineOpen() ? getDeadLineEndDate(TalentStatus.CONFIRMED.getStatus()) : DateTimeUtils.date2StrDate(DateTimeUtils.getDateMoveDay(DateTimeUtils.str2Date(cycleEndDate, "yyyy-MM-dd"), getScoreStartDateTime()));
        // boolean isOvertime = isOvertime(date); //是否超时
        boolean isAutoSkipStay = this.empEvalRule.getDeadLineConf().getTaskConfirmDeadLine().getAutoSkip() == 0;//停留当前
        if (isAutoSkipStay) {
            log.info(" isConfirmedEnterScore.执行环节停留当前，不自动进入评分---");
            return false;
        }
        log.info(" isConfirmedEnterScore.执行进入评分---");
        //进入评分
        return true;
    }

    @JSONField(serialize = false)
    public boolean isOvertime(String date) {
        if (StrUtil.isBlank(date)) {
            return false;
        }
        LocalDate endDate = LocalDate.parse(date);
        if (LocalDate.now().isAfter(endDate)) {
            return true;
        }
        return false;
    }

    //非自动进入评分
    @JSONField(serialize = false)
    public boolean noCanAutoEnterScore(String endDate) {
        if (!isAutoEnter()) {
            return true;
        }
        String date = isDeadLineOpen() ? getDeadLineEndDate(TalentStatus.CONFIRMED.getStatus()) : DateTimeUtils.date2StrDate(DateTimeUtils.getDateMoveDay(DateTimeUtils.str2Date(endDate, "yyyy-MM-dd"), getScoreStartDateTime()));
        if (!isOvertime(date)) {
            //return isExistMust() ? isMustNotSubmitVal() : isNotSubmitVal();
            return isNotSubmitVal(); //20250701产品要求修改为，存在未提交的指标，不允许进入评分
        }
        return isExistMust() ? isMustNotInputVal() : false/*|| isConfirmDStay()*/;
    }

    @JSONField(serialize = false)
    public boolean noAutoEnterScore(String endDate, boolean needAutoEnterScore) {
        log.info("noCanAutoEnterScore,endDate：{},needAutoEnterScore：{}", endDate, needAutoEnterScore);
        return noCanAutoEnterScore(endDate) && !needAutoEnterScore;
    }


    public void removeItemDeleted() {
        this.kpiTypes = this.kpiTypes.stream().filter(type -> type.getIsDeleted().equals(Boolean.FALSE.toString()))
                .collect(Collectors.toList());
        List<EvalKpi> items = new ArrayList<>();
        for (EmpEvalKpiType kpiType : this.kpiTypes) {
            items.addAll(kpiType.getItems().stream().filter(item -> item.getIsDeleted().equals("false")).collect(Collectors.toList()));
        }
        this.kpis = items;
    }

    public String atOrgId() {
        return StrUtil.isNotBlank(evalOrgId) ? evalOrgId : orgId;
    }

    public void setItems(List<EvalKpi> kpis) {
        this.kpis = kpis;
    }


    public Integer getScoreStartDateTime() {
        if (Objects.nonNull(this.empEvalRule)) {
            if (this.empEvalRule.getEnterScore().getScoreStartRuleType().equals("before")) {
                return -this.empEvalRule.getEnterScore().getScoreStartRuleDay();
            }
            return this.empEvalRule.getEnterScore().getScoreStartRuleDay();
        }
        return 0;
    }

    @JSONField(serialize = false)
    public boolean hasNotImportType() {
        if (CollUtil.isEmpty(this.kpiTypes)) {
            return false;
        }
        for (EmpEvalKpiType kpiType : this.kpiTypes) {
            if (CollUtil.isEmpty(kpiType.getItems())) {
                return true;
            }
        }
        return false;
    }

    public void coverTaskStatus(List<CompanyMsgCenter> centers) {
        if (CollUtil.isEmpty(centers)) {
            return;
        }
        this.taskStatus = centers.get(0).getTaskStatus();
        this.companyId = new TenantId(centers.get(0).getCompanyId());
        if (Objects.equals(this.taskStatus, TalentStatus.FINISHED.getStatus())) {
            return;
        }
        if (isScoring() || isTaskChange()) {
            for (CompanyMsgCenter center : centers) {
                MsgTodoInfo info = new MsgTodoInfo();
                info.builder(this.taskId, this.taskName, this.id, this.empId, this.empName,
                        center.getBusinessScene(), Arrays.asList(center.getEmpId()), centers.get(0).getCompanyId());
                this.infos.add(info);
            }
            return;
        }
        MsgTodoInfo info = new MsgTodoInfo();
        info.builder(this.taskId, this.taskName, this.id, this.empId, this.empName,
                centers.get(0).getBusinessScene(), CollUtil.map(centers, c -> c.getEmpId(), true), centers.get(0).getCompanyId());
        this.infos.add(info);
        return;
    }

    @JSONField(serialize = false)
    public boolean isScoring() {
        return Objects.equals(this.taskStatus, TalentStatus.SCORING.getStatus());
    }

    @JSONField(serialize = false)
    public boolean isFinishValueAudit() {
        return Objects.equals(this.taskStatus, TalentStatus.FINISH_VALUE_AUDIT.getStatus());
    }

    @JSONField(serialize = false)
    public boolean isConfirmed() {
        return Objects.equals(this.taskStatus, TalentStatus.CONFIRMED.getStatus());
    }

    @JSONField(serialize = false)
    public boolean isConfirming() {
        return Objects.equals(this.taskStatus, TalentStatus.CONFIRMING.getStatus());
    }

    @JSONField(serialize = false)
    public boolean isResultsAuditing() {
        return Objects.equals(this.taskStatus, TalentStatus.RESULTS_AUDITING.getStatus());
    }

    @JSONField(serialize = false)
    public boolean isResultsAffirming() {
        return Objects.equals(this.taskStatus, TalentStatus.RESULTS_AFFIRMING.getStatus());
    }
    @JSONField(serialize = false)
    public boolean isFinished() {
        return Objects.equals(this.taskStatus, TalentStatus.FINISHED.getStatus());
    }

    @JSONField(serialize = false)
    public boolean isResultAppeal() {
        return Objects.equals(this.taskStatus, TalentStatus.RESULTS_APPEAL.getStatus());
    }
    @JSONField(serialize = false)
    public boolean isTaskChange() {
        return Objects.equals(this.taskStatus, TalentStatus.CHANGING.getStatus());
    }

    @JSONField(serialize = false)
    public boolean isAuditResultCollectSend() {
        return Objects.equals(this.getResultSendNotify(), 2);
    }

    public Integer getResultSendNotify() {
        if (Objects.isNull(this.empEvalRule)) {
            return null;
        }
        if (Objects.isNull(this.empEvalRule.getAuditResult())) {
            return null;
        }
        if (!this.empEvalRule.getAuditResult().isOpen()) {
            return null;
        }
        return this.empEvalRule.getAuditResult().getCollectSendNotify();
    }

    public void setResultSendNotify(Integer sendNotify) {
        if (Objects.isNull(this.empEvalRule)) {
            return;
        }
        if (Objects.isNull(this.empEvalRule.getAuditResult())) {
            return;
        }
        if (!this.empEvalRule.getAuditResult().isOpen()) {
            return;
        }
        this.empEvalRule.getAuditResult().setCollectSendNotify(sendNotify);
    }

    //因通知需要动态展示截止时间，拼接在被考核人后面
    public String joinDeadLineStr(String taskStatus) {
        //组装被考核人与截止时间
        if (StrUtil.isNotBlank(this.deadLineStr(taskStatus))) {
            return this.deadLineStr(taskStatus);
        }
        return "未开启";
    }


    //因通知需要动态展示截止时间
    public String joinTitleDeadLineStr(String taskStatus) {
        //组装被考核人与截止时间
        if (StrUtil.isNotBlank(this.deadLineStr(taskStatus))) {
            return "过期时间:" + this.deadLineStr(taskStatus);
        }
        return "";
    }

    @JSONField(serialize = false)
    public void builderKpiTypes(List<EmpEvalKpiType> kpiTypes) {
        if (CollUtil.isEmpty(kpiTypes)) {
            return;
        }
        this.kpiTypes = kpiTypes;
        this.kpis = kpiTypes.stream().flatMap(k -> k.getItems().stream()).collect(Collectors.toList());
    }


    public List<EvalScoreResult> curAuditResultLevelRs(String companyId, Integer approvalOrder) {
        if (Objects.isNull(this.empEvalRule)) {
            return new ArrayList<>();
        }
        if (!this.empEvalRule.getAuditResult().isOpen()) {
            return new ArrayList<>();
        }
        List<Rater> raters = this.empEvalRule.getAuditResult().getRaterByOrder(approvalOrder);
        List<EvalScoreResult> curLevelRs = new ArrayList<>();
        for (Rater rater : raters) {
            EvalScoreResult result = new EvalScoreResult(new TenantId(companyId), this.taskId, this.id, this.orgId, this.empId, rater.getEmpId(), approvalOrder);
            curLevelRs.add(result);
        }
        return curLevelRs;
    }

    public List<BaseAuditNode> getCurLevelResultAudit(Integer auditLevel) {
        if (Objects.isNull(this.empEvalRule)) {
            return new ArrayList<>();
        }
        AuditResultConf resultConf = this.empEvalRule.getAuditResult();
        if (Objects.isNull(resultConf) || !resultConf.isOpen()) {
            return new ArrayList<>();
        }
        List<BaseAuditNode> auditNodes = CollUtil.filterNew(resultConf.getAuditNodes(), r -> Objects.equals(r.getApprovalOrder(), auditLevel));
        return auditNodes;
    }

    @JSONField(serialize = false)
    public boolean isSkipResultAudit(Integer auditLevel) {
        List<BaseAuditNode> auditNodes = this.getCurLevelResultAudit(auditLevel);
        if (CollUtil.isEmpty(auditNodes)) {
            return false;
        }
        boolean skip = CollUtil.filterNew(auditNodes.get(0).getRaters(), r -> r.isSkip() || r.isError()).size() > 0;
        return skip;
    }

    @JSONField(serialize = false)
    public boolean isNormalRater(Integer auditLevel) {
        List<BaseAuditNode> auditNodes = this.getCurLevelResultAudit(auditLevel);
        if (CollUtil.isEmpty(auditNodes)) {
            return false;
        }
        boolean normal = CollUtil.filterNew(auditNodes.get(0).getRaters(), r -> Objects.isNull(r.getSkipType())).size() > 0;
        return normal;
    }

    @JSONField(serialize = false)
    public List<String> skipRaters(Integer auditLevel) {
        List<BaseAuditNode> auditNodes = this.getCurLevelResultAudit(auditLevel);
        if (CollUtil.isEmpty(auditNodes)) {
            return new ArrayList<>();
        }
        return CollUtil.filterNew(auditNodes.get(0).getRaters(), r -> r.isSkip() || r.isError()).stream().map(r -> r.getEmpId()).collect(Collectors.toList());
    }

    public void accCollecResult(Integer resultAuditLevel) {
        this.resultAuditLevel = resultAuditLevel;
        List<BaseAuditNode> auditNodes = this.getCurLevelResultAudit(resultAuditLevel);
        if (CollUtil.isEmpty(auditNodes)) {
            return;
        }
        if (CollUtil.isNotEmpty(this.kpis)) {
            Integer autoSize = CollUtil.filterNew(this.kpis, k -> k.isAutoItem()).size();
            if (Objects.equals(autoSize, this.kpis.size())) {
                this.allAutoCompute = true;
            }
        }
        this.repeatSkipEmpIds = auditNodes.get(0).getRaters().stream().filter(r -> Objects.equals(r.getSkipType(), 2)).map(Rater::getEmpId).collect(Collectors.toList());
        this.isVacancySkipRater = auditNodes.get(0).getRaters().stream().filter(r -> Objects.equals(r.getSkipType(), 1) || r.isError()).collect(Collectors.toList()).size() > 0;
    }

    public List<EvalScoreResult> accSkipResult(TenantId companyId, String opEmpId) {
        List<EvalScoreResult> scoreResults = new ArrayList<>();
        EvalScoreResult scoreResult = new EvalScoreResult();
        if (this.isVacancySkipRater()) {
            scoreResult.accSkipRater(companyId, this.id, "-1", "空缺跳过", "final_result_audit", this.resultAuditLevel);
            scoreResults.add(scoreResult);
            return scoreResults;
        }
        if (CollUtil.isNotEmpty(this.repeatSkipEmpIds)) {
            for (String repeatSkipEmpId : this.repeatSkipEmpIds) {
                scoreResult.accSkipRater(companyId, this.id, repeatSkipEmpId, "重复跳过", "final_result_audit", this.resultAuditLevel);
                scoreResults.add(scoreResult);
            }
        }
        return scoreResults;
    }


    @JSONField(serialize = false)
    public boolean isSingleAudit() {
        EmpEvalRule empEvalRule = this.empEvalRule;
        if (Objects.nonNull(empEvalRule)) {
            AuditResultConf auditResult = empEvalRule.getAuditResult();
            return Objects.equals(auditResult.getCollectSendNotify(), 1);
        }
        return false;
    }

    public boolean notNormalStatus() {
        return Objects.equals(this.taskStatus, "drawUpIng") || Objects.equals(this.taskStatus, "created") || Objects.equals(this.taskStatus, "terminated");
    }

    public boolean onLevel() {
        return TalentStatus.ON_LEVEL.matchSub(taskStatus, subStatus);
    }

    public boolean afterResultAudit() {
        return TalentStatus.statusOf(this.taskStatus).after(TalentStatus.RESULTS_AUDITING);
    }

    public boolean needChangedResultAudit(String changedStage) {
//        if (notNormalStatus()) {
//            return false;
//        }
        if (TalentStatus.statusOf(this.taskStatus).before(TalentStatus.RESULTS_AUDITING)
                && Objects.equals(changedStage, TalentStatus.RESULTS_AUDITING.getStatus())) {
            return true;
        }
        return false;
    }

    public boolean refreshAuditFlow(String changedStage) {
        return TalentStatus.statusOf(changedStage).beforeEq(TalentStatus.RESULTS_AUDITING)
                && TalentStatus.statusOf(this.getTaskStatus()).afterEq(TalentStatus.RESULTS_AUDITING);
    }

    public void removeCurOpEmpUpReviewer(List<String> resultInputEmpIds) {
        if (CollUtil.isEmpty(reviewersJson)) {
            return;
        }
        reviewersJson.removeIf(emp -> resultInputEmpIds.contains(emp.getEmpId()));
    }

    public List<String> auditScoreTypeConvertor() {
        List<String> list = new ArrayList<>();
        if (isConfirming()) {//执行-指标确认审核
            list.add(EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT.getScene());
            return list;
        }
        if (isConfirmed()) {//执行-
            list.add(EvaluateAuditSceneEnum.INPUT_PROGRESS.getScene());
            list.add(EvaluateAuditSceneEnum.FINISH_VALUE_AUDIT.getScene());
            list.add(EvaluateAuditSceneEnum.CHANGE_ITEM_AUDIT.getScene());
            return list;
        }
        if (isTaskChange()) {//变更中
            list.add(EvaluateAuditSceneEnum.CHANGE_ITEM_AUDIT.getScene());
            return list;
        }
        if (isFinishValueAudit()) {//完成值审核中
            list.add(EvaluateAuditSceneEnum.FINISH_VALUE_AUDIT.getScene());
            return list;
        }
        if (isScoring()) {//评分中
            list.addAll(EvaluateAuditSceneEnum.allScoreTypes());
            return list;
        }
        if (isResultsAuditing()) {//结果校准中
            list.add(EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene());
            return list;
        }
        if (isInterviewStage()) {//面谈
            list.add(EvaluateAuditSceneEnum.FINAL_RESULT_INTERVIEW.getScene());
            return list;
        }
        if (isResultsAffirming()) {//结果确认
            list.add(EvaluateAuditSceneEnum.PERF_RESULTS_AFFIRM.getScene());
            return list;
        }
        return list;
    }

    public Set<String> listAffirmTaskNotify(String opEmpId) {
        if (Objects.isNull(this.empEvalRule)) {
            return new HashSet<>();
        }
        if (Objects.isNull(this.empEvalRule.getConfirmTask())) {
            return new HashSet<>();
        }
        if (Objects.isNull(this.empEvalRule.getConfirmTask().getSendMsgConf())) {
            return new HashSet<>();
        }
        if (!this.empEvalRule.getConfirmTask().getSendMsgConf().isOpen()) {
            return new HashSet<>();
        }
        Set<String> list = this.empEvalRule.getConfirmTask().getSendMsgConf().getAuditNodes().stream()
                .flatMap(node -> node.getRaters().stream()).map(rater -> rater.getEmpId()).collect(Collectors.toSet());
        if (CollUtil.isEmpty(list)) {
            return list;
        }
        if (StrUtil.isNotBlank(opEmpId)) {
            list.remove(opEmpId);
        }
        return list;
    }

    public void inputFinishChanged(List<FinishValue> finishValues) {
        if (CollUtil.isEmpty(finishValues)) {
            return;
        }
        StringBuilder sb = new StringBuilder();
        ListWrap<EvalKpi> kpiMap = new ListWrap<>(this.kpis).asMap(evalKpi -> evalKpi.getId());
        int index = 1;
        for (FinishValue finishValue : finishValues) {
            if (finishValue.isPlanItem()) {
                continue;
            }
            EvalKpi oldKpi = kpiMap.mapGet(finishValue.getId());
            if (index > 5) {
                continue;
            }
            if (Objects.isNull(oldKpi)) {
                continue;
            }
            if (!oldKpi.isInputFinishChanged(finishValue)) {
                index++;
                continue;
            }
            if (Objects.nonNull(finishValue.getItemFinishValue())) {
                sb.append("●   ").append(oldKpi.getKpiItemName()).append("    ")
                        .append(finishValue.getItemFinishValue()).append(oldKpi.getItemUnit()).append("|")
                        .append(oldKpi.getPercent(finishValue.getItemFinishValue()) + "%").append(System.lineSeparator());
            }
            if (StrUtil.isNotBlank(finishValue.getItemFinishValueText())) {
                sb.append("●   ").append(oldKpi.getKpiItemName()).append("    ").append(finishValue.getItemFinishValueText()).append(System.lineSeparator());
            }
            if (StrUtil.isNotBlank(finishValue.getWorkItemFinishValue())) {
                sb.append("●   ").append(oldKpi.getKpiItemName()).append("    ").append(finishValue.getWorkItemFinishValue()).append(System.lineSeparator());
            }
            index++;
        }
        this.inputFinishChanged = sb.toString().replaceAll("(?<!\n)\n$", "");
    }

    public List<FinishValue> convertFinishValues(List<ExcelFinishValue> excelFinishValues) {
        List<FinishValue> finishValues = new ArrayList<>();
        ListWrap<ExcelFinishValue> kpiMap = new ListWrap<>(excelFinishValues).asMap(evalKpi -> evalKpi.getId());
        for (EvalKpi kpi : this.kpis) {
            FinishValue finishValue = kpi.convertFinishValue(kpiMap.mapGet(kpi.getId()));
            if (Objects.isNull(finishValue)) {
                continue;
            }
            finishValues.add(finishValue);
        }
        return finishValues;
    }

    public void changeEvalRule(EmpEvalChangeResult result, Integer changeStage) {
        this.changeScene(changeStage);
        this.empEvalRule.changeEvalRule(result, this.changeScene);
    }

    //异动时，计算可变更的环节
    // 1:指标确认  2：完成值录入  4：指标变更和审核  8：完成值审核  16：评分 32：校准 64：面谈 128：公示 256：结果申诉
    private void changeScene(Integer changeStage) {
        if ((changeStage & 1) > 0 && this.beforeStage(TalentStatus.CONFIRMING)) {
            this.changeScene = this.changeScene | 1;
            changedAuditScene.add(EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT.getScene());
        }
        if ((changeStage & 2) > 0 && this.beforeStage(TalentStatus.CONFIRMED)) {
            this.changeScene = this.changeScene | 2;
        }
        if ((changeStage & 4) > 0 && this.beforeStage(TalentStatus.CONFIRMED)) {
            this.changeScene = this.changeScene | 4;
        }
        if ((changeStage & 8) > 0 && this.beforeStage(TalentStatus.FINISH_VALUE_AUDIT)) {
            this.changeScene = this.changeScene | 8;
            changedAuditScene.add(EvaluateAuditSceneEnum.FINISH_VALUE_AUDIT.getScene());
        }
        if ((changeStage & 16) > 0 && this.beforeStage(TalentStatus.SCORING)) {
            this.changeScene = this.changeScene | 16;
            changedAuditScene.addAll(EvaluateAuditSceneEnum.allScoreTypes());
        }
        if ((changeStage & 32) > 0 && this.beforeStage(TalentStatus.RESULTS_AUDITING)) {
            this.changeScene = this.changeScene | 32;
            changedAuditScene.add(EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene());
        }
        //面谈
        if ((changeStage & 64) > 0 && this.beforeStage(TalentStatus.RESULTS_INTERVIEW)) {
            this.changeScene = this.changeScene | 64;
            changedAuditScene.add(EvaluateAuditSceneEnum.FINAL_RESULT_INTERVIEW.getScene());
        }
        if ((changeStage & 128) > 0) {
            this.changeScene = this.changeScene | 128;
        }
        if ((changeStage & 256) > 0) {
            this.changeScene = this.changeScene | 256;
        }
    }

    public static void main(String[] args) {
        System.out.println(TalentStatus.statusOf("resultsAuditing"));
        System.out.println(TalentStatus.statusOf("resultsAuditing").after(TalentStatus.RESULTS_AUDITING));
    }

    @Override
    public String taskUserId() {
        return id;
    }

    @Override
    public void applyScoreRange(List<RankRuleScoreRangeSnap> ranges) {
        setScoreRanges(ranges);
    }

    public boolean isInterviewStageAfter() {
        //当前考核任务是否在面谈环节之后
        return TalentStatus.afterStatus(TalentStatus.RESULTS_INTERVIEW).contains(this.taskStatus);
    }

    public boolean onFinished() {
        return TalentStatus.FINISHED.match(taskStatus);
    }

    public boolean hasCreatedLevel() {
        return StrUtil.isNotBlank(this.stepId);
    }

    public void addReviewers(List<KpiEmp> addEmps) {
        List<String> scorerIds = addEmps.stream().map(KpiEmp::getEmpId).collect(Collectors.toList());
        this.removeReviewers(scorerIds);
        if (CollUtil.isEmpty(this.reviewersJson)) {
            this.reviewersJson = new ArrayList<>();
        }
        this.reviewersJson.addAll(addEmps);
    }

    public void accAppealResult(BigDecimal lastModifiedScore, String lastModifiedLevel, BigDecimal lastModifiedPerfCoefficient, String lastModifiedStepId) {

        this.finalScore = lastModifiedScore;
        this.evaluationLevel = lastModifiedLevel;
        this.perfCoefficient = lastModifiedPerfCoefficient == null ? null : lastModifiedPerfCoefficient + "";
        this.stepId = lastModifiedStepId;
    }

    public void clearAutoScore() {
        List<EvalKpi> autoItems = autoItems();
        autoItems = autoItems.stream()
                .filter(s -> !StrUtil.equals(Boolean.TRUE.toString(), s.getIsDeleted()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(autoItems) && ObjectUtil.isNotNull(this.finalItemAutoScore)) {
            this.finalItemAutoScore = BigDecimal.ZERO;
        }
    }

    public void copyEvalGroupInfo(String evalGroupId, String evalGroupName) {
        this.evalGroupId = evalGroupId;
        this.evalGroupName = evalGroupName;
    }

    public void changeEmpEvalOrgBaseInfo(String orgId,String orgName) {
        this.orgId = orgId;
        this.empOrgName = orgName;
    }

    public void removeEvalRuleError(String errorCode) {
        if (StrUtil.isBlank(this.ruleConfError)) {
            return;
        }
        List<String> codes = StrUtil.splitTrim(this.ruleConfError, ",");
        codes.removeIf(code -> StrUtil.equals(code, errorCode));
        this.ruleConfError = StringUtils.join(codes, ",");
    }

    public void cleanAppealTimes() {
        this.empEvalRule.clearAppealFinishedNum();
    }

    public void resetRuleConfError(String node) {

        if (AuditEnum.CONFIRM_TASK.getScene().equals(node)) {
            clearEvalPublicErro("101");
            return;
        }
        if (AuditEnum.EDIT_EXE_INDI.getScene().equals(node)) {
            clearEvalPublicErro("102");
            return;
        }
        if (AuditEnum.FINAL_RESULT_AUDIT.getScene().equals(node)) {
            clearEvalPublicErro("103");
        }
        if (AuditEnum.FINISH_VALUE_AUDIT.getScene().equals(node)) {
            clearEvalPublicErro("104");
            return;
        }
        if (AuditEnum.FINAL_RESULT_INTERVIEW_EXCUTE.getScene().equals(node)) {
            clearEvalPublicErro("108");
            return;
        }
        if (AuditEnum.FINAL_RESULT_INTERVIEW_CONFIRM.getScene().equals(node)) {
            clearEvalPublicErro("109");
            return;
        }
        if (AuditEnum.RESULT_APPEAL.getScene().equals(node)) {
            clearEvalPublicErro("601");
        }
    }

    public void clearEvalPublicErro(String errorCode) {
        if (StringUtils.isNotBlank(this.ruleConfError)) {
            List<String> errors = StrUtil.splitTrim(this.ruleConfError, ",");
            errors.removeIf(code -> StrUtil.equals(code, errorCode));
            this.ruleConfError = StringUtils.join(errors, ",");
        }
    }
}

