package com.polaris.kpi.eval.domain.task.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.domain.task.entity
 * @Author: suxiaoqiu
 * @CreateTime: 2025-03-03  11:49
 * @Description: 维度权重的环节分
 * @Version: 2.0
 */
@Getter
@Setter
@Slf4j
public class TypeFinalNodeScoreV3 extends BaseFinalNodeScoreV3 {
    //    private BigDecimal typeFinalSelfScore; //维度自评得分
//    private BigDecimal typeFinalAppointScore;//维度定向得分
//    private BigDecimal typeItemScore;//维度指定评得分
//    private BigDecimal typeFinalPeerScore;//维度同级得分
//    private BigDecimal typeFinalSubScore;//维度下级得分
//    private BigDecimal typeFinalSuperiorScore;//维度上级得分
  //  private BigDecimal typeFinalWeightAutoSum; //维度自动计算指标总分 //带权重
    private BigDecimal typeSum; //维度得分 算过权重的 最终得分
    private BigDecimal typeScore; //维度得分 未算过维度权重的
    private BigDecimal typeTempSumScore; //临时汇总用于计算的总分
    //    private BigDecimal typeSelfScore;
//    private BigDecimal typeAppointScore;
//    private BigDecimal typePeerScore;
//    private BigDecimal typeSubScore;
//    private BigDecimal typeSuperiorScore;
    private BigDecimal typeFinalAutoSum; //维度自动计算指标总分 //不带权重


    private BigDecimal typePlusSum;
    private BigDecimal typeSubtractSum;
    private BigDecimal typeOkrScoreSum;
//    private Set<String> scoreTypes = new HashSet<>();
//    private Function<EvalScorerNodeKpiItem, BigDecimal> getScoreFunc;
//    private Function<EvalScorerNodeKpiItem, BigDecimal> getWeightScoreFunc;

//    public String scoreAsString() {
//        StringAppend sbb = new StringAppend("", " | ")
//                .appendSplitPair("自评 ", typeSelfScore)
//                .appendSplitPair("指定评 ", typeAppointScore)
//                .appendSplitPair("定向评 ", typeItemScore)
//                .appendSplitPair("同级评 ", typePeerScore)
//                .appendSplitPair("下级评 ", typeSubScore)
//                .appendSplitPair("上级评 ", typeSuperiorScore).deleteLastSplit();
//        return sbb.toString();
//    }

    public TypeFinalNodeScoreV3() {
//        this.getScoreFunc = EvalScorerNodeKpiItem::getItemWeightScore;// 打分*指标权重*评分人权重
//        this.getWeightScoreFunc = EvalScorerNodeKpiItem::getFinalWeightScore;// 打分*指标权重*评分人权重
    }

    public void addToTempSumScore(BigDecimal typeTempSumScore) {
        this.typeTempSumScore = addNullZero(this.typeTempSumScore, typeTempSumScore);
    }
    public void addToTypeNoWeightScore(BigDecimal typeNoWeightScore) {
        this.typeScore = addNullZero(this.typeScore, typeNoWeightScore);
    }
    public void sum() {
        this.sumTypeFinalScore();
        this.sumTypeScore();
    }

    public void sumTypeFinalScore() {
        BigDecimal sum = typeTempSumScore;
        sum = ItemFinalNodeScoreV3.addNullZero(sum, okrScoreSum);
        sum = ItemFinalNodeScoreV3.addNullZero(sum, plusSum);
        sum = ItemFinalNodeScoreV3.addNullZero(sum, subtractSum);
        sum = ItemFinalNodeScoreV3.addNullZero(sum, askEvalScore);
        sum = ItemFinalNodeScoreV3.addNullZero(sum, typeFinalAutoSum);
        this.typeSum = sum;
    }
//    public void sumTypeFinalScore() {
//        BigDecimal sum = BigDecimal.ZERO;
//        sum = ItemFinalNodeScoreV3.addNullZero(sum, finalSelfScore);
//        sum = ItemFinalNodeScoreV3.addNullZero(sum, finalPeerScore);
//        sum = ItemFinalNodeScoreV3.addNullZero(sum, finalSubScore);
//        sum = ItemFinalNodeScoreV3.addNullZero(sum, finalSuperiorScore);
//        sum = ItemFinalNodeScoreV3.addNullZero(sum, finalItemScore);
//        sum = ItemFinalNodeScoreV3.addNullZero(sum, finalAppointScore);
//        sum = ItemFinalNodeScoreV3.addNullZero(sum, okrScoreSum);
//        sum = ItemFinalNodeScoreV3.addNullZero(sum, plusSum);
//        sum = ItemFinalNodeScoreV3.addNullZero(sum, subtractSum);
//        sum = ItemFinalNodeScoreV3.addNullZero(sum, askEvalScore);
//        this.typeSum = sum;
//    }

    public void sumTypeScore() {
        BigDecimal sum = BigDecimal.ZERO;
        sum = ItemFinalNodeScoreV3.addNullZero(sum, okrScoreSum);
        sum = ItemFinalNodeScoreV3.addNullZero(sum, plusSum);
        sum = ItemFinalNodeScoreV3.addNullZero(sum, subtractSum);
        sum = ItemFinalNodeScoreV3.addNullZero(sum, askEvalScore);
        this.addToTypeNoWeightScore(sum);
    }


    public void computeAutoFinalWeightScore(BigDecimal autoScore, String scene) {
        //计算过环节权重的自动计算指标分
        addAutoWeightScoreByType(scene, autoScore);
        //未乘以环节权重的自动计算指标分
        addAutoWeightScoreByType(scene, autoScore);
        this.typeFinalAutoSum = BaseFinalNodeScoreV3.addNullZero(this.typeFinalAutoSum, autoScore);
    }


    public void computeAutoFinalWeightScore(BigDecimal autoScore, BigDecimal autoWeightScore, String scene) {
        //计算过环节权重的自动计算指标分
        addAutoWeightScoreByType(scene, autoWeightScore);
        //未乘以环节权重的自动计算指标分
        addAutoWeightScoreByType(scene, autoScore);
    //    this.typeFinalWeightAutoSum = BaseFinalNodeScoreV3.addNullZero(this.typeFinalWeightAutoSum, autoWeightScore);
    }

    public void nodeAddExtraWeightScore() {
     //   addExtraWeightScore(this.okrScoreSum);//okr
     //   addExtraWeightScore(this.plusSum);//加分
       // addExtraWeightScore(this.subtractSum);//减分
       // addExtraWeightScore(this.askEvalScore);//问卷分
    }

    public void addAutoScoreByType(String scorerType, BigDecimal autoWeightScore) {
        if (autoWeightScore == null) {
            return;
        }
        if ("self_score".equals(scorerType)) {
            selfScore = addNullZero(selfScore, autoWeightScore);
        }
        if ("peer_score".equals(scorerType)) {
            peerScore = addNullZero(peerScore, autoWeightScore);
        }
        if ("sub_score".equals(scorerType)) {
            subScore = addNullZero(subScore, autoWeightScore);
        }
        if ("appoint_score".equals(scorerType)) {
            appointScore = addNullZero(appointScore, autoWeightScore);
        }
        if ("superior_score".equals(scorerType)) {
            superiorScore = addNullZero(superiorScore, autoWeightScore);
        }
    }

    public void addAutoWeightScoreByType(String scorerType, BigDecimal autoWeightScore) {
        if (autoWeightScore == null) {
            return;
        }
        if ("self_score".equals(scorerType)) {
            finalSelfScore = addNullZero(finalSelfScore, autoWeightScore);
        }
//        if ("item_score".equals(scorerType)) {
//            finalItemScore = addNullZero(finalItemScore, autoWeightScore);
//        }
        if ("peer_score".equals(scorerType)) {
            finalPeerScore = addNullZero(finalPeerScore, autoWeightScore);
        }
        if ("appoint_score".equals(scorerType)) {
            finalAppointScore = addNullZero(finalAppointScore, autoWeightScore);
        }
        if ("sub_score".equals(scorerType)) {
            finalSubScore = addNullZero(finalSubScore, autoWeightScore);
        }
        if ("superior_score".equals(scorerType)) {
            finalSuperiorScore = addNullZero(finalSuperiorScore, autoWeightScore);
        }
    }

    public TypeFinalNodeScoreV3 addToSum(BigDecimal addTo) {
        typeSum = addNullZero(typeSum, addTo);
        return this;
    }

    public TypeFinalNodeScoreV3 subtractToSum(BigDecimal addTo) {
        if (addTo == null) {
            return this;
        }
        typeSum = addNullZero(typeSum, addTo.multiply(new BigDecimal(-1)));
        return this;
    }

    public static void main(String[] args) {
        System.out.println(addNullZero(new BigDecimal(10), null));
    }

    public void addToAutoItemScore(BigDecimal autoItemScore) {
        this.typeFinalAutoSum = addNullZero(this.typeFinalAutoSum, autoItemScore);
    }


    public List<ComputedResultScore> addItemScoreToType(BigDecimal itemWeight, ItemFinalNodeScoreV3 itemCs) {
        List<ComputedResultScore> crs = new ArrayList<>();
        Set<String> scoreTypes = itemCs.getScoreTypes();

        for (String scoreType : scoreTypes) {
            //没有环节权重【也无指标维度权重分】得分分
            BigDecimal noWeightScore = itemCs.getNoWeightScoreByType(scoreType);
            //乘以指标权重
            noWeightScore = initNullValue(noWeightScore).multiply(itemWeight);

            //有环节也有维度指标权重的分
            BigDecimal weightScore = itemCs.getFinalScoreByType(scoreType);


            ComputedResultScore resultScore = new ComputedResultScore(scoreType);
            resultScore.accpScore(weightScore, noWeightScore);
            crs.add(resultScore);

            this.addFinalScoreByType(scoreType, weightScore); // 计算维度环节得分（带了环节权重的）
            this.addNoWeightScoreByType(scoreType, noWeightScore);// 计算维环节得分(不带环节权重的维度环节得分)
        }

        //指标得分 【未乘以指标维度权重】
        BigDecimal itemScore = itemCs.getItemScore();
        //乘以指标权重就是维度权重
        itemScore = initNullValue(itemScore).multiply(itemWeight);
        this.addToTypeNoWeightScore(itemScore);
        return crs;
    }


    public List<ComputedResultScore> addItemSubtractOrPlusScoreToType(ItemFinalNodeScoreV3 itemCs) {
        List<ComputedResultScore> crs = new ArrayList<>();
        Set<String> scoreTypes = itemCs.getScoreTypes();
        for (String scoreType : scoreTypes) {
            //没有环节权重【也无指标维度权重分】得分分
            BigDecimal noWeightScore = itemCs.getNoWeightScoreByType(scoreType);
            BigDecimal weightScore = itemCs.getFinalScoreByType(scoreType);
            ComputedResultScore resultScore = new ComputedResultScore(scoreType);
            resultScore.accpScore(weightScore,noWeightScore);
            crs.add(resultScore);
            this.addNoWeightScoreByType(scoreType, noWeightScore);// 计算维环节得分(不带环节权重的维度环节得分)
            this.addFinalScoreByType(scoreType, weightScore);// 计算维环节得分(带环节权重的维度环节得分)
        }
        return crs;
    }


    public void coverNoWeightScoreByType(String scorerType, BigDecimal noWeightScore) {
        if (noWeightScore == null) {
            return;
        }
        scoreTypes.add(scorerType);
        if ("self_score".equals(scorerType)) {
            selfScore = noWeightScore;
        }
        if ("appoint_score".equals(scorerType)) {
            appointScore = noWeightScore;
        }
        if ("item_score".equals(scorerType)) {
            itemItemScore = noWeightScore;
        }
        if ("peer_score".equals(scorerType)) {
            peerScore = noWeightScore;
        }
        if ("sub_score".equals(scorerType)) {
            subScore = noWeightScore;
        }
        if ("superior_score".equals(scorerType)) {
            superiorScore = noWeightScore;
        }
    }
    public void coverWeightScoreByType(String scorerType, BigDecimal weightScore) {
        if (weightScore == null) {
            return;
        }
        scoreTypes.add(scorerType);
        if ("self_score".equals(scorerType)) {
            finalSelfScore = weightScore;
        }
        if ("appoint_score".equals(scorerType)) {
            finalAppointScore = weightScore;
        }
        if ("item_score".equals(scorerType)) {
            finalItemScore = weightScore;
        }
        if ("peer_score".equals(scorerType)) {
            finalPeerScore = weightScore;
        }
        if ("sub_score".equals(scorerType)) {
            finalSubScore = weightScore;
        }
        if ("superior_score".equals(scorerType)) {
            finalSuperiorScore = weightScore;
        }
    }
}
