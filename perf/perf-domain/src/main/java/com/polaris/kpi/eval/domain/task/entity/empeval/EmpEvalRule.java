package com.polaris.kpi.eval.domain.task.entity.empeval;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.com.polaris.kpi.eval.InputFinishStatusEnum;
import cn.com.polaris.kpi.eval.KpiTypeUsedField;
import cn.com.polaris.kpi.eval.Rater;
import cn.com.polaris.kpi.eval.ResultInputEmpPackage;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.perf.www.common.constant.BusinessConstant;
import com.perf.www.common.em.EvaluateTypeEnum;
import com.perf.www.common.utils.bean.Convert;
import com.polaris.kpi.eval.domain.task.entity.EvalFormulaField;
import com.polaris.kpi.eval.domain.task.entity.EvalItemScoreRule;
import com.polaris.kpi.eval.domain.task.entity.EvalKpi;
import com.polaris.kpi.eval.domain.task.entity.ScoreStageRule;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.admineval.EvalRuleLogField;
import com.polaris.kpi.eval.domain.task.entity.admineval.EvalRuleOpLogMeta;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.*;
import com.polaris.kpi.eval.domain.task.type.NodeWeight;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.org.domain.dept.entity.CompanyMsgCenter;
import com.polaris.sdk.type.AuditEnum;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 表:emp_eval_rule 由于task_base 拆分而来,承载任务配置相关的部分
 * 减少的字段参数建表语句
 * 考核表功能新加表,员工每次考核创建一条
 ***/
@Getter
@Setter
public class EmpEvalRule extends BaseEmpEvalRule {

    public EmpEvalRule() {
    }

    //private List<EmpEvalKpiType> kpiTypes;            //指标分类
    private Map<String, EmpEvalKpiType> kpiTypeMap = null;
    private ResultInputEmpPackage resultInputEmpPackage;
    @JSONField(serialize = false)
    public boolean isCustom() {
        return EvaluateTypeEnum.CUSTOM.getType().equals(this.evaluateType);
    }

    public void acceptOpEmp(TenantId tenantId, String opEmpId) {
        this.companyId = tenantId;
        this.createdUser = opEmpId;
        this.updatedUser = opEmpId;
        this.updatedTime = new Date();
    }

    public void initRefItems() {
        for (EmpEvalKpiType item : typeDatas()) {
            item.setCompanyId(companyId);
            item.setTaskUserId(empEvalId);
        }
    }


    public EmpEvalRule(TenantId companyId, String empEvalId, String evaluateType) {
        this.companyId = companyId;
        this.empEvalId = empEvalId;
        this.evaluateType = evaluateType;
    }

    public EmpEvalRule(TenantId companyId, String opEmpId, String taskId, String empId, ScoreStageRule conf) {
        this.companyId = companyId;
        this.empEvalId = conf.getEmpEvalId();
        this.evaluateType = conf.getEvaluateType();
        this.typeWeightConf = conf.getTypeWeightConf();
        this.scoreValueConf = conf.getScoreValueConf();
        this.s3SelfRater = conf.getS3SelfRater();
        this.s3PeerRater = conf.getS3PeerRater();
        this.s3SubRater = conf.getS3SubRater();
        this.s3SuperRater = conf.getS3SuperRater();
        this.s3AppointRater = conf.getS3AppointRater();
        this.kpiTypes = new KpiListWrap(conf.getKpiTypes(), EmpEvalMerge.type | EmpEvalMerge.item);
        this.totalLevelRaters = conf.getTotalLevelRaters();
        this.createTotalLevelType = conf.getCreateTotalLevelType();
        this.showResultType = conf.getShowResultType();
        //this.indexRaters = conf.getIndexRaters();
        initKpiType(taskId, empId, opEmpId);
        acceptOpEmp(companyId, opEmpId);
        copyRuleName(conf.getRuleName());
        copyRuleDesc(conf.getRuleDesc());
    }

    public EmpEvalRule(TenantId companyId, String empEvalId, AffirmTaskConf conf) {
        this.companyId = companyId;
        this.empEvalId = empEvalId;
        this.confirmTask = conf;
    }

    public EmpEvalRule(TenantId companyId, String empEvalId, EditExeIndiConf conf) {
        this.companyId = companyId;
        this.empEvalId = empEvalId;
        this.editExeIndi = conf;
    }

    public EmpEvalRule(TenantId companyId, String empEvalId, FinishValueAuditConf conf) {
        this.companyId = companyId;
        this.empEvalId = empEvalId;
        this.finishValueAudit = conf;
    }

    public EmpEvalRule(TenantId companyId, String empEvalId, AuditResultConf conf) {
        this.companyId = companyId;
        this.empEvalId = empEvalId;
        this.auditResult = conf;
    }

    public EmpEvalRule(TenantId companyId, String empEvalId, InterviewConf conf) {
        this.companyId = companyId;
        this.empEvalId = empEvalId;
        this.interviewConf = conf;
    }
    public EmpEvalRule(TenantId companyId, String empEvalId, PublishResultConf conf) {
        this.companyId = companyId;
        this.empEvalId = empEvalId;
        this.publishResult = conf;
    }

    public EmpEvalRule(TenantId companyId, String empEvalId, AppealConf conf) {
        this.companyId = companyId;
        this.empEvalId = empEvalId;
        this.appealConf = conf;
    }

    public EmpEvalRule(TenantId companyId, String empEvalId, DeadLineConf conf) {
        this.companyId = companyId;
        this.empEvalId = empEvalId;
        this.deadLineConf = conf;
    }

    public void accRaterConf(RaterNodeConf s3SelfRater,MutualNodeConf s3PeerRater,MutualNodeConf s3SubRater,
                       S3SuperRaterConf s3SuperRater, S3RaterBaseConf s3AppointRater) {
        if ((Objects.isNull(s3SelfRater) || !s3SelfRater.isOpen())
                && (Objects.isNull(s3PeerRater) || !s3PeerRater.isOpen())
                && (Objects.isNull(s3SubRater) || !s3SubRater.isOpen())
                && (Objects.isNull(s3SuperRater) || !s3SuperRater.isOpen())
                && (Objects.isNull(s3AppointRater) || !s3AppointRater.isOpen())) {
            return;
        }
            this.s3SelfRater = s3SelfRater;
            this.s3PeerRater = s3PeerRater;
            this.s3SubRater = s3SubRater;
            this.s3SuperRater = s3SuperRater;
            this.s3AppointRater = s3AppointRater;
    }

    public boolean checkConfErro() {
        //xzl todo
        return false;
        //return s3MutualRater.hasEmptyRater() || s3SuperRater.hasEmptyRater();
    }

    //清除复制上次的id
    public void clearCopyId() {

    }

    public String sameTime() {
        return this.isCustom() ? this.getSuperiorScoreWay() : this.getS3SuperRater().getSuperiorScoreOrder();
    }

    public String superNodeName() {
        if (s3SuperRater == null) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (Rater rater : s3SuperRater.allRater()) {
            sb.append(String.format("%s（%s％）", rater.getEmpName(), rater.getWeight()));
        }
        return sb.toString();
    }

    public boolean needConfirmTask() {
        return confirmTask.isOpen();
    }

    public void createName(String empName) {
        this.ruleName = empName + "的考核规则";
    }

    //public void creatRuleNameIfNeed(String cycleName, String empName, String ruleName) {
    //    if (StrUtil.isNotBlank(ruleName)) {
    //        this.ruleName = ruleName;
    //        return;
    //    }
    //    ruleName(cycleName, empName);
    //}

    public void copyRuleName(String fromName) {
        this.ruleName = fromName;
    }

    public void copyRuleDesc(String fromDesc) {
        this.ruleDesc = fromDesc;
    }

    public static void main(String[] args) {
        String name = "2022年1月周期xzl";
        List<String> list = Arrays.asList(name.split("周"));
        System.out.println(JSON.toJSONString(list));
        System.out.println(System.currentTimeMillis());
    }

    public void copyAskEvalRefId(List<EmpEvalKpiType> kpiTypes) {
        if (CollUtil.isEmpty(this.kpiTypes.getDatas())) {
            return;
        }
        Map<String,EmpEvalKpiType> kpiTypeMap = CollUtil.toMap(CollUtil.filterNew(this.kpiTypes.getDatas(),t -> t.isAskType()),new HashMap<>(),EmpEvalKpiType::getKpiTypeId);
        for (EmpEvalKpiType kpiType : kpiTypes) {
            if (kpiType.isAskType()) {
                EmpEvalKpiType type = kpiTypeMap.get(kpiType.getKpiTypeId());
                if (Objects.nonNull(type)) {
                    kpiType.setAsk360EvalId(type.getAsk360EvalId());
                }
            }
        }
    }

    public void initKpiType(String taskId, String empId, String opEmpId) {
        int sumCnt = 0;
        for (EmpEvalKpiType kpiType : getKpiTypes().getDatas()) {
            if (!this.isCustom()){
                kpiType.clearRaterConf();
            }
            kpiType.setCreatedTime(new Date());
            kpiType.setCreatedUser(opEmpId);
            kpiType.setIsDeleted("false");
            sumCnt += kpiType.getItems().size();
            for (EvalKpi item : kpiType.getItems()) {
                item.setFinishValueAuditReason(null);//驳回原因置为null--新增的
                item.setFinishValueAuditStatus(0);//0-未审核
                //兼容旧数据需要taskId,
                item.baseId(getCompanyId(), taskId, empId, new EmpId(opEmpId), getEmpEvalId());
                if (!this.isCustom()){
                    item.setItemScoreRule(null);
                }
                EvalItemScoreRule itemScoreRule = item.getItemScoreRule();
                List<EvalFormulaField> formulaFields = item.getFormulaFields();
                if (itemScoreRule != null) {
                    itemScoreRule.initBaseInfo(companyId, new EmpId(opEmpId), empEvalId, kpiType.getKpiTypeId(),
                            item.getKpiItemId(), taskId);
                    if (!itemScoreRule.hasScoreRule() && !"auto".equals(item.getScorerType())) {
                        itemScoreRule.asRaterConf(this.s3SelfRater, this.s3PeerRater, this.s3SubRater, this.s3SuperRater, this.s3AppointRater);
                    }
                }
                if (CollUtil.isNotEmpty(formulaFields)) {
                    for (EvalFormulaField formulaField : formulaFields) {
                        formulaField.initBaseInfo(companyId, new EmpId(opEmpId), empEvalId, item.getKpiItemId(), taskId);
                    }
                }
            }
            /**处理okr指标默认流程处理*/
            EvalItemScoreRule typeRule = kpiType.getTypeRule();
            if (typeRule != null) {
                if (!typeRule.hasScoreRule()) {
                    typeRule.asRaterConf(this.s3SelfRater, this.s3PeerRater, this.s3SubRater, this.s3SuperRater, this.s3AppointRater);
                }
            }
        }
        this.setIndicatorCnt(sumCnt);
    }

    public List<EvalKpi> getItems() {
        List<EvalKpi> items = new ArrayList<>();
        for (EmpEvalKpiType kpiType : typeDatas()) {
            List<? extends EvalKpi> kpis = kpiType.getItems();
            if (CollUtil.isEmpty(kpis)) {
                continue;
            }
            kpis.forEach(item -> {
                item.setCompanyId(companyId);
                item.setTaskUserId(empEvalId);
                item.setKpiTypeId(kpiType.getKpiTypeId());
                item.setItemLimitCnt(kpiType.getItemLimitCnt());
            });
            items.addAll(kpis);
        }
        return items;
    }

    public List<EvalKpi> getScoreItems() {
        List<EvalKpi> items = new ArrayList<>();
        for (EmpEvalKpiType kpiType : typeDatas()) {
            if (kpiType.isNoNeedScoreType()) {//维度无需评价
                continue;
            }
            List<? extends EvalKpi> kpis = kpiType.getItems();
            if (CollUtil.isEmpty(kpis)) {
                continue;
            }
            for (EvalKpi item : kpis) {
                //指标无需评价
                if (item.itemNoNeedScore(kpiType.getScoreOptType())) {
                    continue;
                }
                item.setCompanyId(companyId);
                item.setTaskUserId(empEvalId);
                item.setKpiTypeId(kpiType.getKpiTypeId());
                item.setItemLimitCnt(kpiType.getItemLimitCnt());
                items.add(item);
            }
        }
        return items;
    }

    public List<EvalFormulaField> getFormulaFields() {
        List<EvalFormulaField> formulaFields = new ArrayList<>();
        List<EvalKpi> items = getItems();
        if (CollUtil.isEmpty(items)) {
            return new ArrayList<>();
        }
        for (EvalKpi item : items) {
            List<EvalFormulaField> fields = item.getFormulaFields();
            if (CollUtil.isEmpty(fields)) {
                continue;
            }
            fields.forEach(f -> {
                f.setCompanyId(companyId);
                f.setTaskUserId(empEvalId);
                f.setKpiItemId(item.getId());
            });
            formulaFields.addAll(item.getFormulaFields());
        }
        return formulaFields;
    }

    public List<EvalItemScoreRule> getCustomItemRules() {
        List<EvalItemScoreRule> itemRules = new ArrayList<>();
        List<EvalKpi> items = getItems();
        if (CollUtil.isEmpty(items)) {
            return new ArrayList<>();
        }
        for (EvalKpi item : items) {
            EvalItemScoreRule rule = item.getItemScoreRule();
            if (Objects.isNull(rule)) {
                continue;
            }
            rule.setCompanyId(companyId);
            rule.setTaskUserId(empEvalId);
            rule.setKpiItemId(item.getId());
            rule.setKpiTypeId(item.getKpiTypeId());
            itemRules.add(rule);
        }
        return itemRules;
    }


    public String mutualRule() {
        if ((!s3PeerRater.isOpen()) && (!s3SubRater.isOpen())) {
            return "";
        }
        if (s3PeerRater.isOpen() && s3SubRater.isOpen()) {
            return s3PeerRater.getApprovalOrder() > s3SubRater.getApprovalOrder() ?
                    BusinessConstant.PEER_AFTER_SUB : BusinessConstant.PEER_AND_SUB;
        }
        if (s3PeerRater.isOpen()) {
            return BusinessConstant.PEER;
        }
        return BusinessConstant.SUB;
    }

    public EvalItemScoreRule itemRule(String scene) {
        List<EvalKpi> itemList = new ArrayList<>();
        typeDatas().forEach(kpiType -> {
            itemList.addAll(kpiType.getItems());
        });

        for (EvalKpi evalKpi : itemList) {
            if (EvaluateAuditSceneEnum.SELF_SCORE.getScene().equals(scene)) {
                if (evalKpi.getItemScoreRule().needSelfScore()) {
                    return evalKpi.getItemScoreRule();
                }
            }
            if (EvaluateAuditSceneEnum.SUPERIOR_SCORE.getScene().equals(scene)) {
                if (evalKpi.getItemScoreRule().needSupScore()) {
                    return evalKpi.getItemScoreRule();
                }
            }
        }
        return null;
    }

    public List<EmpEvalKpiType> typeDatas() {
        return kpiTypes.getDatas();
    }

    public boolean needSelfScore() {
        if (isCustom()) {
            List<EvalKpi> items = new ArrayList<>();
            for (EmpEvalKpiType kpiType : typeDatas()) {
                items.addAll(kpiType.getItems());
            }
            if (CollUtil.isEmpty(items)) {
                return false;
            }
            List<EvalKpi> needSelfScoreItems = items.stream()
                    .filter(item -> Objects.nonNull(item.itemScoreRule()))
                    .filter(item -> Objects.nonNull(item.itemScoreRule().getSelfRater()))
                    .filter(item -> item.itemScoreRule().getSelfRater().isOpen())
                    .collect(Collectors.toList());
            return CollUtil.isNotEmpty(needSelfScoreItems);
        }
        return s3SelfRater.isOpen();
    }

    public boolean needSuperScore() {
        return s3SuperRater.isOpen();
    }

    // region compare


    public EvalRuleOpLogMeta compareEvaluateType(EmpEvalRule after) {
        EvalRuleLogField nameField = EvalRuleLogField.createIf("", getEvaluateTypeName(), after.getEvaluateTypeName());
        EvalRuleOpLogMeta logMeta = new EvalRuleOpLogMeta("修改了类型设置", "");
        logMeta.addField(nameField);
        return logMeta;
    }

    public String getEvaluateTypeName() {
        return "simple".equals(evaluateType) ? "所有指标使用相同评分流程" : "根据指标配置不同的评分流程";
    }

    private void initKpiTypesMap() {
        if (CollUtil.isEmpty(typeDatas())) {
            return;
        }
        if (this.kpiTypeMap == null) {
            this.kpiTypeMap = new HashMap<>();
            for (EmpEvalKpiType kpiType : typeDatas()) {
                kpiTypeMap.put(kpiType.getKpiTypeId(), kpiType);
            }
        }
    }

    public EmpEvalKpiType getKpiType(String kpiTypeId) {
        this.initKpiTypesMap();
        if (this.kpiTypeMap != null && this.kpiTypeMap.containsKey(kpiTypeId)) {
            return this.kpiTypeMap.get(kpiTypeId);
        }
        return null;
    }

    public EvalItemScoreRule asItemRule() {
        EvalItemScoreRule itRule = new EvalItemScoreRule(companyId, empEvalId);
        itRule.asRaterConf(s3SelfRater, s3PeerRater, s3SubRater, s3SuperRater, s3AppointRater);
        if (Objects.nonNull(scoreConf)) {
            itRule.extendMutiConf(scoreConf);
        }
        return itRule;
    }

    public List<NodeWeight> getNodeWeight(String kpiItemId) {
        List<NodeWeight> nodeWeights = new ArrayList<>();
        if (isCustom()) {
            List<EvalKpi> items = getItems();
            EvalKpi evalKpi = items.stream().filter(item -> item.getKpiItemId().equals(kpiItemId)).collect(Collectors.toList()).get(0);
            return evalKpi.nodeWeight();
        }
        if (s3SelfRater.isOpen()) {
            NodeWeight nodeWeight = new NodeWeight(AuditEnum.SELF_SCORE.getScene(), s3SelfRater.getNodeWeight());
            nodeWeights.add(nodeWeight);
        }
        if (s3SuperRater.isOpen()) {
            NodeWeight nodeWeight = new NodeWeight(AuditEnum.SUPERIOR_SCORE.getScene(), s3SuperRater.getNodeWeight());
            nodeWeights.add(nodeWeight);
        }
        if (s3PeerRater.isOpen()) {
            NodeWeight nodeWeight = new NodeWeight(AuditEnum.PEER_SCORE.getScene(), s3PeerRater.getNodeWeight());
            nodeWeights.add(nodeWeight);
        }
        if (s3SubRater.isOpen()) {
            NodeWeight nodeWeight = new NodeWeight(AuditEnum.SUB_SCORE.getScene(), s3SubRater.getNodeWeight());
            nodeWeights.add(nodeWeight);
        }
        return nodeWeights;
    }

    public AuditResultConf toResultAudit(CycleAuditResultConf auditResult) {
        List<BaseAuditNode> auditNodes = new ArrayList<>();
        auditResult.getApproveConfs().forEach(a -> {
            BaseAuditNode auditNode = new BaseAuditNode(a.getApproveOrder(), a.getApproveType(), a.getApproveInfo(), a.getApproveName());
            auditNode.setMultiType(auditResult.getMultiType());
            auditNode.setTransferFlag(auditResult.getTransferFlag());
            auditNode.setRaters(Arrays.asList(new Rater(a.getApproveInfo(), a.getApproveName())));
            auditNodes.add(auditNode);
        });
        return new AuditResultConf(auditResult.getCommentReq(), auditNodes, auditResult.getOpen());
    }

    public void copyAdminTaskConf(AdminTask taskConf) {
        this.confirmTask = taskConf.getConfirmTask();
        this.editExeIndi = taskConf.getEditExeIndi();
        this.finishValueAudit = taskConf.getFinishValueAudit();
        this.enterScore = taskConf.getEnterScore();
        this.scoreConf = taskConf.getScoreConf();
        this.scoreView = taskConf.getScoreView();
        this.confirmResult = taskConf.getConfirmResult();
        this.commentConf = taskConf.getCommentConf();
        this.commentReqConf = taskConf.getCommentReqConf();

        this.auditResult = taskConf.getAuditResult();
        this.interviewConf =  taskConf.getInterviewConf();
        this.publishResult = taskConf.getPublishResult();
        this.appealConf = taskConf.getAppealConf();
        this.deadLineConf = taskConf.getDeadLineConf();
    }

    public void syncTaskConfirm(AffirmTaskConf confirmTask) {
        this.confirmTask = confirmTask;
    }

    public void syncEditExeIndi(EditExeIndiConf editExeIndi) {
        this.editExeIndi = editExeIndi;
    }

    public void syncFinishValueAudit(FinishValueAuditConf finishValueAudit) {
        this.finishValueAudit = finishValueAudit;
    }

    public void syncAuditResult(AuditResultConf auditResult) {
        if (auditResult.isOpen() && !auditResult.getAuditNodes().isEmpty()) {
            this.auditResult = auditResult;
            return;
        }
        this.auditResult = new AuditResultConf(0);
    }

    public void rmItemId() {
        for (EmpEvalKpiType kpiType : typeDatas()) {
            List<? extends EvalKpi> items = kpiType.getItems();
            if (CollUtil.isEmpty(items)) {
                continue;
            }
            for (EvalKpi item : items) {
                item.setId(null);
            }
        }
    }

    public Integer reChekConfStatus() {
        if (confirmTask.isOpen()) {
            if (curStageRaterIsEmpty(confirmTask.getAuditNodes())) {
                return 101;
            }
        }
        if (editExeIndi.auditIsOpen()) {
            if (curStageRaterIsEmpty(editExeIndi.getAuditNodes())) {
                return 102;
            }
        }
        if (auditResult.isOpen()) {
            if (curStageRaterIsEmpty(auditResult.getAuditNodes())) {
                return 103;
            }
        }
        if (appealConf.isOpen()) {
            if (curStageRaterIsEmpty(appealConf.getAppealFlowConf().getAuditNodes())) {
                return 601;
            }
        }
        return 200;
    }

    public <T extends BaseAuditNode> boolean curStageRaterIsEmpty(List<T> auditNodes) {
        for (BaseAuditNode auditNode : auditNodes) {
            if (auditNode.ratersIsEmpty()) {
                return true;
            }
        }
        return false;
    }

    public void reSetConf(ScoreStageRule conf, String taskId, String empId, String opEmpId) {
        copyRuleName(conf.getRuleName());
        copyRuleDesc(conf.getRuleDesc());
        changeItemInput(new KpiListWrap(conf.getKpiTypes()),empId);
        this.typeWeightConf = conf.getTypeWeightConf();
        this.scoreValueConf = conf.getScoreValueConf();
        this.evaluateType = conf.getEvaluateType();
        this.s3SelfRater = conf.getS3SelfRater();
        this.s3PeerRater = conf.getS3PeerRater();
        this.s3SubRater = conf.getS3SubRater();
        this.s3SuperRater = conf.getS3SuperRater();
        this.s3AppointRater = conf.getS3AppointRater();
        //
        copyAskEvalRefId(conf.getKpiTypes());
        this.kpiTypes = new KpiListWrap(conf.getKpiTypes(), EmpEvalMerge.type);
        initKpiType(taskId, empId, opEmpId);
        acceptOpEmp(companyId, opEmpId);
        useScoreConf();
        setTotalLevelRaters(conf.getTotalLevelRaters());
        setCreateTotalLevelType(conf.getCreateTotalLevelType());
        setShowResultType(conf.getShowResultType());
    }

    public void changeItemInput(KpiListWrap wrap,String empId) {
        if (CollUtil.isEmpty(wrap.getDatas())) {
            return;
        }
        this.resultInputEmpPackage = new ResultInputEmpPackage();
        Map<String,EvalKpi> itemMap = this.getItems().stream().collect(Collectors.toMap(k -> k.getKpiItemId(), Function.identity()));
        for (EmpEvalKpiType data : wrap.getDatas()) {
            if (CollUtil.isEmpty(data.getItems())) {
                continue;
            }
            data.buildItemCompareInputEmp(itemMap,this.resultInputEmpPackage,empId);
        }
        this.resultInputEmpPackage.addAllResultInputEmpIds(wrap.allItemsInputEmpIds(empId));
        this.resultInputEmpPackage.filterStillExistInputEmpId();   //防止还存在于责任人中的被清除了待办
    }

    public void replaceKpiOwner(String newOrgOwnerId) {
        for (EmpEvalKpiType kpiType : typeDatas()) {
            List<? extends EvalKpi> items = kpiType.getItems();
            if (CollUtil.isEmpty(items)) {
                continue;
            }
            for (EvalKpi item : items) {
                item.setEmpId(newOrgOwnerId);
                //如果录入人是被考核人自己需更改为新的责任人
                if (item.ifSelfInput()) {
                    item.setResultInputEmpId(newOrgOwnerId);
                }
                item.setId(null);
                List<EvalFormulaField> formulaFields = item.getFormulaFields();
                EvalItemScoreRule itemScoreRule = item.getItemScoreRule();
                if (CollUtil.isNotEmpty(formulaFields)) {
                    formulaFields.forEach(f -> f.setId(null));
                }
                if (itemScoreRule != null) {
                    itemScoreRule.setId(null);
                }
            }
        }
    }

    public void initIndicatorCnt() {
        int itemCnt = 0;
        for (EmpEvalKpiType kpiType : typeDatas()) {
            List<? extends EvalKpi> items = kpiType.getItems();
            itemCnt = itemCnt + items.size();
        }
        this.indicatorCnt = itemCnt;
    }

    public void initMutulRaters(boolean isOpenAvgWeightCompute) {
        if (!isOpenAvgWeightCompute) {
            if (Objects.nonNull(this.s3PeerRater)) {
                this.s3PeerRater.initRaterWeight();//互评初始化权重100%
            }
            if (Objects.nonNull(this.s3SubRater)) {
                this.s3SubRater.initRaterWeight();//互评初始化权重100%
            }
        }
    }

    public List<EvalItemScoreRule> inviteMutualEmp(String scene, Map<String, List<Rater>> mutuRaters) {
        List<EvalItemScoreRule> rsRules = new ArrayList<>();
        for (EmpEvalKpiType type : typeDatas()) {
            for (EvalKpi item : type.getItems()) {
                if (mutuRaters.containsKey(item.getKpiItemId())) {
                    List<Rater> raters = mutuRaters.get(item.getKpiItemId());
                    EvalItemScoreRule itemScoreRule = item.updateMutualRater(scene, raters);
                    rsRules.add(itemScoreRule);
                }
            }
        }
        return rsRules;
    }

    /**
     * 初始化维度字段配置
     */
    public void initKpiTypeUsedFields(List<KpiTypeUsedField> usedFieldDos) {
        /**组装维度字段配置数据*/
        if (CollUtil.isNotEmpty(typeDatas()) && CollUtil.isNotEmpty(usedFieldDos)) {
            Map<String, List<KpiTypeUsedField>> groupMap = usedFieldDos.stream()
                    .collect(Collectors.groupingBy(KpiTypeUsedField::getKpiTypeId));
            typeDatas().forEach(obj -> {
                if (groupMap.get(obj.getKpiTypeId()) != null) {
                    obj.setKpiTypeUsedFields(groupMap.get(obj.getKpiTypeId()));
                }
            });
        }
    }

    public List<String> kpiTypeIds() {
        return typeDatas().stream().map(EmpEvalKpiType::getKpiTypeId)
                .collect(Collectors.toList());
    }

    public String getDeadLineEndDate(String taskStatus) {
        if (Objects.nonNull(deadLineConf) && deadLineConf.isOpen()) {
            return deadLineConf.matchEndDate(taskStatus);
        }
        return null;
    }

    public String deadLineJoinStr(String taskStatus) {
        if (Objects.nonNull(deadLineConf) && deadLineConf.isOpen()) {
            return deadLineConf.joinConfStr(taskStatus);
        }
        return null;
    }

    public boolean deadLineTimeOuted() {
        if (Objects.nonNull(deadLineConf) && deadLineConf.isOpen()) {
            return deadLineConf.timeOuted(LocalDate.now());
        }
        return false;
    }

    public void clearAppointRater() {
        if (!this.isCustom()) {
            if (Objects.nonNull(this.s3PeerRater) && this.s3PeerRater.isOpen() && Objects.nonNull(this.s3PeerRater.getAppointer())) {
                this.s3PeerRater.setRaters(new ArrayList<>());
            }
            if (Objects.nonNull(this.s3SubRater) && this.s3SubRater.isOpen() && Objects.nonNull(this.s3SubRater.getAppointer())) {
                this.s3SubRater.setRaters(new ArrayList<>());
            }
            return;
        }
        for (EmpEvalKpiType data : this.kpiTypes.getDatas()) {
            if (data.isAskType()) {
                data.clearAppointRater();
            }
            for (EvalKpi item : data.getItems()) {
                EvalItemScoreRule scoreRule = item.getItemScoreRule();
                if (Objects.isNull(scoreRule)) {
                    continue;
                }
                scoreRule.clearAppointRater();
            }
        }
    }

    public boolean refreshInterviewExcuterEmpConf(List<KpiEmp> reviewrs) {
        if (Objects.isNull(this.interviewConf) || !this.interviewConf.isOpen() || CollUtil.isEmpty(reviewrs)) {
            return false;
        }

        List<Rater> raters = new ArrayList<>();
        for (KpiEmp emp : reviewrs) {
            raters.add(new Rater(emp.getEmpId(), emp.getEmpName(), emp.getAvatar()));
        }
        interviewConf.getInterviewExcutorInfo().setRaters(raters);
        return true;
    }

    public void builderRaterSkipType(String scene) {
        if (Objects.equals(AuditEnum.FINAL_RESULT_AUDIT.getScene(), scene)) {
            if (!this.auditResult.isEmpRepeatSkip()) {
                return;
            }
        }
        if (Objects.equals(AuditEnum.CONFIRM_TASK.getScene(), scene)) {
            if (!this.confirmTask.isEmpRepeatSkip()) {
                return;
            }
        }
        if (Objects.equals(AuditEnum.EDIT_EXE_INDI.getScene(), scene)) {
            if (!this.editExeIndi.isEmpRepeatSkip()) {
                return;
            }
        }
        List<BaseAuditNode> afterAuditNodes = new ArrayList<>();
        List<? extends BaseAuditNode> auditNodes = new ArrayList<>();
        if (Objects.equals(AuditEnum.FINAL_RESULT_AUDIT.getScene(), scene)) {
            auditNodes = this.auditResult.getAuditNodes();
            afterAuditNodes = Convert.convertListOnlyMatch(auditNodes,BaseAuditNode.class);
        }else if (Objects.equals(AuditEnum.CONFIRM_TASK.getScene(), scene)){
            auditNodes = this.confirmTask.getAuditNodes();
            afterAuditNodes = Convert.convertListOnlyMatch(auditNodes,BaseAuditNode.class);
        }else if (Objects.equals(AuditEnum.EDIT_EXE_INDI.getScene(), scene)) {
            auditNodes = this.editExeIndi.getAuditNodes();
            afterAuditNodes = Convert.convertListOnlyMatch(auditNodes,BaseAuditNode.class);
        }
        for (BaseAuditNode auditNode : auditNodes) {
            if (!Objects.equals(auditNode.getNode(), "final_result_audit")) {
                List<BaseAuditNode> nexts = afterAuditNodes.stream().filter(node -> Objects.equals(node.getApprovalOrder(), auditNode.getApprovalOrder() + 1)).collect(Collectors.toList());
                if (CollUtil.isEmpty(nexts)) {
                    continue;
                }
                BaseAuditNode nextAuditNode = nexts.get(0);
                if (Objects.isNull(nextAuditNode)) {
                    continue;
                }
                auditNode.markRepeat(nextAuditNode.getRaters());
                continue;
            }
            for (BaseAuditNode afterAuditNode : afterAuditNodes) {
                if (!Objects.equals(afterAuditNode.getApprovalOrder(), auditNode.getApprovalOrder())
                        && afterAuditNode.getApprovalOrder() > auditNode.getApprovalOrder()) {
                    auditNode.markRepeat(afterAuditNode.getRaters());
                }
            }
        }
    }



    public List<String> extractEmpIds(List<CompanyMsgCenter> msgCenters) {
        if (CollUtil.isEmpty(msgCenters)){
            return new ArrayList<>();
        }
        return  msgCenters.stream().map(CompanyMsgCenter::getEmpId).collect(Collectors.toList());
    }

    public List<String> findDifference(List<String> empIds, List<String> resultEmpIds) {
        if (CollUtil.isEmpty(empIds)){
            return new ArrayList<>();
        }
        return empIds.stream().filter(empId -> !resultEmpIds.contains(empId)).collect(Collectors.toList());
    }

    public boolean isAutoSkipContinueInterviewFlow() {
        if (Objects.isNull(deadLineConf) || Objects.isNull(deadLineConf.getTaskResultInterview()) || deadLineConf.getTaskResultInterview().getAutoSkip() != 1) {
            return false;
        }
        //1-继续进行流程（待办保留，内部流程继续)
        return 1 == deadLineConf.getTaskResultInterview().getAutoSkipHandlerType();
    }

    public void replaceResultAuditEmp(KpiEmp originalEmp,KpiEmp newEmp,Integer level) {
        if (Objects.isNull(this.auditResult)) {
            return;
        }
        if (CollUtil.isEmpty(this.auditResult.getAuditNodes())) {
            return;
        }
        for (BaseAuditNode auditNode : this.auditResult.getAuditNodes()) {
            auditNode.replaceRater(originalEmp,newEmp,level);
        }
    }

    @Override
    public EmpEvalRule clone() {
        return JSONUtil.toBean(JSONUtil.toJsonStr(this),EmpEvalRule.class);
    }

    public Integer computeInputFinishStatus(String taskStatus) {
        List<EvalKpi> kpis = getItems();
        if (kpis.isEmpty() || StrUtil.isEmpty(taskStatus) || TalentStatus.noInputStatus(taskStatus)) {
            return null;
        }
        if (TalentStatus.PUBLISHED.getStatus().equals(taskStatus)) {
            return null;
        }
        if (kpis.stream().allMatch(kpi -> "no".equals(kpi.getResultInputType()))) {
            return InputFinishStatusEnum.UN_NEED_INPUT.getStatus();
        }
        if (kpis.stream().allMatch(kpi -> Objects.isNull(kpi.getItemFinishValue()) && Objects.isNull(kpi.getItemFinishValueText()) && !kpi.isWorkItemFinished())) {
            return InputFinishStatusEnum.NO_INPUT.getStatus();
        }
        if (kpis.stream().filter(kpi -> !"no".equals(kpi.getResultInputType())).allMatch(kpi -> Objects.nonNull(kpi.getItemFinishValue())
                || Objects.nonNull(kpi.getItemFinishValueText()) || kpi.isWorkItemFinished())) {
            return InputFinishStatusEnum.ALL_INPUT.getStatus();
        }
        return InputFinishStatusEnum.PORTION_INPUT.getStatus();
    }

    public void clearAppealFinishedNum() {
        if (Objects.nonNull(appealConf) && appealConf.isOpen()){
            appealConf.setFinishedNumber(0);
        }
    }

    public void applyConfFromEvalGroup(List<BaseAuditNode> auditResultAuditFlowConf) {
        if (CollUtil.isEmpty(auditResultAuditFlowConf)) {
            return;
        }
        if (Objects.nonNull(auditResult)){
            this.auditResult.setAuditNodes(auditResultAuditFlowConf);
        }
    }

    public void initAllowSelfAsPeer(boolean allowSelfAsPeer) {
        s3PeerRater.initAllowSelfAsPeer(allowSelfAsPeer);
        s3SubRater.initAllowSelfAsPeer(allowSelfAsPeer);
        for (EmpEvalKpiType data : this.kpiTypes.getDatas()) {
            if(Objects.nonNull(data.getPeerRater())){
                data.getPeerRater().initAllowSelfAsPeer(allowSelfAsPeer);
            }
            if(Objects.nonNull(data.getSubRater())){
                data.getSubRater().initAllowSelfAsPeer(allowSelfAsPeer);
            }
        }
    }
}