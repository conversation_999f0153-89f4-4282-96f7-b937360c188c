package com.polaris.kpi.extData.domain.entity;

import com.polaris.kpi.extData.domain.type.ExtDataFieldParamValue;
import com.polaris.kpi.extData.domain.type.RequestParamInfo;
import com.polaris.kpi.extData.domain.type.enums.SymbolEnum;
import com.polaris.kpi.common.DelableDomain;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.annotations.Ckey;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: xuxw
 * @Date: 2025/03/11 11:59
 * @Description: 接口返回参数
 */
@Setter
@Getter
public class ExtDataField extends DelableDomain {
    @Ckey
    private String id;
    private String name;
    private String extDataSysId;
    private String extDataClsId;
    //是否关联数据【0-否，1-是】
    private Integer isCorrelation;
    //取值规则【1-累计求和，2-最大值】
    private Integer valueRule;
    private String tokenName;
    private String tokenPrefix;
    //唯一标识
    private String uniqueKey;
    private String reqUrl;
    //请求方式【GET，POST，HEAD】
    private String reqType;
    //请求头
    private String reqHeaders;
    //请求参数
    private String queryParams;
    //请求body类型【1.form-data,2.json,3.raw-text】
    private Integer queryBodyType;
    //请求body参数
    private String queryBody;
    // 返回参数类型【form-data，application/josn】
    private String resParamType;
    private String companyId;
    private Integer usePhone;
    private Integer useDingUserId;
    private Integer useName;
    private Integer useJobNum;
    private String jsonNodePath;
    private String reqParamEncMode;
    private String resParamDecMode;
    private String token;
    private Date lastUpdateTime;
    // 前端用来判断数据字段是否同步了数据，如果同步了则不让修改数据字段的规则
    private Integer wasSyncData = 0;
    private List<ExtDataFieldParam> params;

    public List<ExtDataFieldParamValue> build(){
        List<ExtDataFieldParamValue> res = new ArrayList<>();
        ExtDataFieldParamValue paramValue;
        for (ExtDataFieldParam param : params){
            paramValue = new ExtDataFieldParamValue();
            paramValue.setParamEnName(param.getParamEnName());
            res.add(paramValue);
        }
        return res;
    }

    public RequestParamInfo buildParam(){
        int index = uniqueKey.lastIndexOf(SymbolEnum.POINT.getSymbol());
        this.jsonNodePath = uniqueKey.substring(0, index);
        if (this.jsonNodePath.contains(SymbolEnum.SQUARE_BRACKETS.getSymbol())){
            this.jsonNodePath = this.jsonNodePath.replace(SymbolEnum.SQUARE_BRACKETS.getSymbol(), "");
        }
        RequestParamInfo paramInfo = new RequestParamInfo();
        paramInfo.setToken(this.getToken());
        paramInfo.setTokenName(this.getTokenName());
        paramInfo.setTokenPrefix(this.getTokenPrefix());
        paramInfo.setReqUrl(this.getReqUrl());
        paramInfo.setReqType(this.getReqType());
        paramInfo.setReqParamEncMode(this.getReqParamEncMode());
        paramInfo.setResParamDecMode(this.getResParamDecMode());
        paramInfo.setReqHeaders(this.getReqHeaders());
        paramInfo.setQueryParams(this.getQueryParams());
        paramInfo.setQueryBodyType(this.getQueryBodyType());
        paramInfo.setQueryBody(this.getQueryBody());
        return paramInfo;
    }
}
