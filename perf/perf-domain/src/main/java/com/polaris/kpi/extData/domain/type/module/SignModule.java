package com.polaris.kpi.extData.domain.type.module;

import cn.hutool.core.util.ObjectUtil;
import com.polaris.kpi.extData.domain.entity.ExtDataSysConf;
import com.polaris.kpi.extData.domain.type.SignParamAnalysis;
import com.polaris.kpi.extData.domain.type.encAndDec.EncDec;
import com.polaris.kpi.extData.domain.type.encAndDec.EncDecFactory;
import com.polaris.kpi.common.KpiI18NException;

import java.util.Map;

/**
 * @Author: xuxw
 * @Date: 2025/03/10 10:00
 * @Description: 签名组件
 */
public class SignModule {
    private ExtDataSysConf conf;

    public SignModule(ExtDataSysConf conf) {
        this.conf = conf;
    }

    public String execute(Map<String, String> stepMap){
        if (ObjectUtil.isNull(conf.getQueryParams())){
            throw new KpiI18NException("", "请求参数不能为空");
        }
        SignParamAnalysis analysis = new SignParamAnalysis();
        String res = analysis.signParamAnalysis(conf.getQueryParams(), stepMap);
        if (ObjectUtil.isNotNull(conf.getReqParamEncMode())){
            EncDec enc = EncDecFactory.init(conf.getReqParamEncMode());
            res = enc.enc(res);
        }
        return res;
    }
}
