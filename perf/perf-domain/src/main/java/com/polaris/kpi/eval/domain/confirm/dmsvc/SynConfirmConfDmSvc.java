package com.polaris.kpi.eval.domain.confirm.dmsvc;

import com.polaris.kpi.eval.domain.task.entity.OperationLog;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEval;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.AffirmTaskConf;
import com.polaris.kpi.org.domain.dept.dmsvc.ExtEmpParser;
import com.polaris.kpi.org.domain.dept.dmsvc.LevelLeaderParser;
import com.polaris.kpi.org.domain.dept.dmsvc.RoleParser;
import com.polaris.kpi.org.domain.dept.repo.DeptFinder;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class SynConfirmConfDmSvc {
    private TenantId companyId;
    private String opEmpId;
    private List<EmpEval> evals;
    @Getter
    private Set<String> clearScoreRsScenes = new HashSet<>();
    @Getter
    private List<OperationLog> logs = new ArrayList<>();

    private RoleParser roleParser;
    private LevelLeaderParser leaderParser;
    private ExtEmpParser empParser;
    private DeptFinder deptFinder;

    public SynConfirmConfDmSvc(TenantId companyId, String opEmpId, List<EmpEval> evals) {
        this.companyId = companyId;
        this.opEmpId = opEmpId;
        this.evals = evals;
    }


    public SynConfirmConfDmSvc initRepo(RoleParser roleParser, LevelLeaderParser leaderParser, ExtEmpParser empParser, DeptFinder deptFinder) {
        this.roleParser = roleParser;
        this.leaderParser = leaderParser;
        this.empParser = empParser;
        this.deptFinder = deptFinder;
        return this;
    }

    public void synConfirmConf(AffirmTaskConf newConf) {
        for (EmpEval eval : evals) {
            AffirmTaskConf clone = newConf.clones();//复制给每个成员,防止共用一个对象,导致所有成员考核表变成一样.
            if (clone.isOpen()) {
                clone.parseAuditRaters(eval.getEval(), roleParser, leaderParser, empParser, deptFinder);
            }
            eval.getRule().setConfirmTask(clone);
        }
    }
}
