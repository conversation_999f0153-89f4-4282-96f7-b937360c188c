package com.polaris.kpi.extData.domain.type;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.XmlUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.polaris.kpi.extData.domain.entity.ExtDataFieldParam;
import com.polaris.kpi.extData.domain.type.enums.SymbolEnum;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: xuxw
 * @Date: 2025/03/05 18:45
 * @Description:
 */
public class ParamAnalysis {

    private static final String XML = "XML";
    private static final String JSON = "JSON";
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * 参数转换成JSON对象
     *
     * @param param
     * @param paramType
     * @return
     */
    public JSONObject transferParam(String param, String paramType) {
        JSONObject jsonObject = null;
        if (StrUtil.equals(paramType, XML, true)) {
            jsonObject = xmlToJson(param);
        }
        if (StrUtil.equals(paramType, JSON, true)) {
            jsonObject = JSONObject.parseObject(param, JSONObject.class);
        }
        return jsonObject;
    }

    /**
     * xml转json
     *
     * @param xml
     * @return
     */
    private static JSONObject xmlToJson(String xml) {
        StringBuilder sb = new StringBuilder();
        sb.append(xml);
        if (sb.charAt(0) == '\"') {
            sb.deleteCharAt(0);
        }
        if (sb.charAt(sb.length() - 1) == '\"') {
            sb.deleteCharAt(sb.length() - 1);
        }
        Map<String, Object> stringObjectMap = XmlUtil.xmlToMap(sb.toString());
        String json = JSONObject.toJSONString(stringObjectMap);
        return JSONObject.parseObject(json, JSONObject.class);
    }

    public String getJsonString(String jsonString, String key) {
        JsonNode node = null;
        try {
            node = OBJECT_MAPPER.readTree(jsonString).at(SymbolEnum.SLASH.getSymbol() +
                    key.replace(SymbolEnum.POINT.getSymbol(), SymbolEnum.SLASH.getSymbol()));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        return node.asText();
    }


    public List<ExtDataFieldParam> apiResParamAnals(JSONObject jsonData, String path, List<ExtDataFieldParamValue> params){
        String[] nodes = path.split("\\.");

        Object current = jsonData;
        for (String node : nodes) {
            if (current instanceof JSONObject) {
                current = ((JSONObject) current).get(node);
            }
        }

        List<ExtDataFieldParam> res = new ArrayList<>();
        if (current instanceof JSONArray) {
            List<String> keys = params.stream()
                    .map(s -> s.getParamEnName())
                    .collect(Collectors.toList());
            ExtDataFieldParam param;
            for (Object o : (JSONArray) current) {
                param = new ExtDataFieldParam();
                JSONObject jsonObject = (JSONObject) o;
                List<ExtDataFieldParamValue> resParamValue = this.buildParamValue(jsonObject, keys);
                param.setParamValues(resParamValue);
                res.add(param);
            }
        }
        return res;
    }

    private List<ExtDataFieldParamValue> buildParamValue(JSONObject jsonObject, List<String> keys){
        List<ExtDataFieldParamValue> res = new ArrayList<>();
        ExtDataFieldParamValue paramValue;
        for (String key: keys){
            paramValue = new ExtDataFieldParamValue();
            Object object = jsonObject.get(key);
            String value = "";
            if (ObjectUtil.isNotNull(object)){
                value = jsonObject.get(key).toString();
            }
            paramValue.setParamEnName(key);
            paramValue.setValue(value);
            res.add(paramValue);
        }
        return res;
    }
}
