package com.polaris.kpi.eval.domain.confirm.dmsvc;

import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.com.polaris.kpi.company.TenantSysConf;
import cn.com.polaris.kpi.eval.ItemCustomFieldValue;
import cn.com.polaris.kpi.eval.Rater;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.ask.domain.acl.AskEvalAcl;
import com.polaris.kpi.eval.domain.task.entity.EvalAudit;
import com.polaris.kpi.eval.domain.task.entity.EvalKpi;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalKpiType;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalRule;
import com.polaris.kpi.eval.domain.task.entity.empeval.KpiListWrap;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.AffirmTaskConf;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.BaseAuditNode;
import com.polaris.kpi.eval.domain.task.entity.flow.LevelAuditFlow;
import com.polaris.kpi.eval.domain.task.repo.EmpEvalRuleRepo;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.Setter;

import java.util.*;
import java.util.stream.Collectors;

@Getter
@Setter
public class EditConfirmItemDmSvc extends DirectConfirmItemDmSvc {
    private TenantSysConf openConfirmOnInd;
    private BaseAuditNode onIndNode;
    @Getter
    private AffirmTaskConf confirmTask;

    @Getter
    private List<EvalAudit> onIndAudits;

    public EditConfirmItemDmSvc(TenantId companyId, String taskUserId, TenantSysConf openConfirmOnInd) {
        super();
        super.companyId = companyId;
        super.taskUserId = taskUserId;
        super.scene = EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT.getScene();
        this.openConfirmOnInd = openConfirmOnInd;
    }

    public void initFlow(LevelAuditFlow levelAuditFlow) {
        super.levelAuditFlow = levelAuditFlow;
    }

//    public EditConfirmItemDmSvc(TenantId companyId, String taskUserId, LevelAuditFlow levelAuditFlow, EvaluateAuditSceneEnum scene) {
//        super(companyId, taskUserId, levelAuditFlow, scene);
//    }

    //重新生成指标的评分流程
    public void resetCustomItemFlowIfOpt(EmpEvalRuleRepo empRuleRepo) {
        EmpEvalRule empEvalRule = empEval.getEmpEvalRule();
        if (Objects.nonNull(empEvalRule) && empEvalRule.isCustom()) {
            empEvalRule.setKpiTypes(new KpiListWrap(empEval.getKpiTypes()));
            empRuleRepo.customItemFlow(empEvalRule, empEval.getTaskId(), empEval.getEmpId());
        }
    }

    //删除问卷考
    public void delAsk360EvalIfOpt(AskEvalAcl askEvalAcl, List<String> delAsk360EvalIds, String opEmpId) {
        if (CollUtil.isNotEmpty(delAsk360EvalIds)) {
            askEvalAcl.delAsk360Eval(companyId.getId(), taskUserId, delAsk360EvalIds, opEmpId);
        }
    }


    public void editTypes(List<EmpEvalKpiType> kpiTypes) {
        empEval.submitItems(kpiTypes);
        this.empEval.setKpiTypes(kpiTypes);
    }

    public List<EvalKpi> items() {
        List<EvalKpi> kpis = new ArrayList<>();
        for (EmpEvalKpiType kpiType : empEval.getKpiTypes()) {
            if (CollUtil.isEmpty(kpiType.getItems())) {
                continue;
            }
            kpis.addAll(kpiType.getItems());
        }
        return kpis;
    }

    public boolean isOpenAuditInd(Integer approvalOrder) {
        if (Objects.isNull(approvalOrder) || approvalOrder != 1) {
            return false;
        }
        if (!openConfirmOnInd.isOpen()) {//开启运营后台开关
            return false;
        }
        //并配置在考核表上
        this.confirmTask = empEval.getEmpEvalRule().getConfirmTask();
        this.onIndNode = confirmTask.findConfirmOnIndNode();
        if (onIndNode == null) {
            return false;
        }
        return true;
    }

    //创建按指标的流程
    public List<EvalAudit> createAuditIndFlow(String opEmpId) {

        List<ItemCustomFieldValue> values = items().stream().flatMap(evalKpi -> evalKpi.getFieldValueList().stream())
                .filter(filed -> filed.isTypeEq(5) && StrUtil.isNotBlank(filed.getFieldValue()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(values)) {
            return new ArrayList<>();
        }
        onIndAudits = new ArrayList<>();
        Set<Rater> raters = new HashSet<>();
        values.stream().forEach(field -> {//此阶段是已解析过的具体人员
            BaseAuditNode bean = JSONUtil.toBean(field.getFieldValue(), BaseAuditNode.class);
            bean.getRaters().forEach(rater -> {
                EvalAudit audit = new EvalAudit(super.companyId, empEval.getId(), rater.getEmpId(), onIndNode.getApprovalOrder(), scene);
                audit.setMultipleReviewersType(onIndNode.getMultiType());
                audit.setModifyFlag(onIndNode.getModifyFlag());
                audit.setEmpId(empEval.getEmpId());
                audit.setTaskId(empEval.getTaskId());
                audit.setOrgId(empEval.getOrgId());
                audit.setKpiItemId(field.getKpiItemId());
                audit.setTransferFlag(onIndNode.getTransferFlag());
                audit.setCreatedUser(opEmpId);
                audit.setApproverType(bean.getApproverType());
                onIndAudits.add(audit);
                raters.add(rater);
            });

        });
        onIndNode.setRaters(new ArrayList<>(raters));
        return onIndAudits;
    }

}
