//package com.polaris.kpi.common.event;
//
//import com.alibaba.cola.event.DomainEventI;
//import com.polaris.kpi.org.domain.common.DomainEventPublisherI;
//import lombok.Data;
//import org.slf4j.MDC;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//
///**
// * <AUTHOR> lufei
// * @date 2021/7/8 11:45 上午
// */
//@Data
//@Component
//public class BaseEvent implements DomainEventI {
//    private static DomainEventPublisherI publisher;
//    protected String srcReqId;
//    protected String eventId;
//    protected String tenantId;
//    protected Integer error = 0;
//    protected String errorMsg;
//
//    public BaseEvent() {
//        if(publisher != null) {
//            this.eventId = publisher.nextEventId();
//        }
//        this.srcReqId = MDC.get("tid");
//        //final long now = Instant.now().getEpochSecond();
//    }
//
//    public BaseEvent(String tenantId) {
//        this();
//        this.srcReqId = MDC.get("tid");
//        this.tenantId = tenantId;
//    }
//
//    @Autowired
//    public void setPublisher(DomainEventPublisherI publisher) {
//        BaseEvent.publisher = publisher;
//    }
//
//    public String logId() {
//        return String.format("%s->[%s]=%s", srcReqId, getClass().getSimpleName(), eventId);
//    }
//
//    public void setId(String id) {
//        this.eventId = id;
//    }
//
//    public void publish() {
//        publisher.publish(this);
//    }
//
//    public void error(String exMsg) {
//        this.error = 1;
//        this.errorMsg = exMsg;
//        publisher.error(this);
//    }
//
//    @Override
//    public String id() {
//        return eventId;
//    }
//
//}
