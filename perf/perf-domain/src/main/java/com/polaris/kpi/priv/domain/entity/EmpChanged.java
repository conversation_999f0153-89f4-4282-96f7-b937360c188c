package com.polaris.kpi.priv.domain.entity;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
@Setter

public class EmpChanged {
    public static final String SEPARATOR = "|";
    private String companyId;

    private String empId;

    private List<String> beforeAtDeptCodes;      //原始部门路径

    private List<String> afterAtDeptCodes;      //新部门路径

    public Collection<String> beforDel() {
        Set<String> beforeAtDeptIdsSet = beforeAtDeptCodes.stream().flatMap(s -> StrUtil.splitTrim(s, SEPARATOR).stream()).collect(Collectors.toSet());
        Set<String> afterAtDeptIdsSet = afterAtDeptCodes.stream().flatMap(s -> StrUtil.splitTrim(s, SEPARATOR).stream()).collect(Collectors.toSet());
        return CollUtil.subtract(beforeAtDeptIdsSet ,afterAtDeptIdsSet);
    }

    public Collection<String> afterAdd() {
        Set<String> beforeAtDeptIdsSet = beforeAtDeptCodes.stream().flatMap(s -> StrUtil.splitTrim(s, SEPARATOR).stream()).collect(Collectors.toSet());
        Set<String> afterAtDeptIdsSet = afterAtDeptCodes.stream().flatMap(s -> StrUtil.splitTrim(s, SEPARATOR).stream()).collect(Collectors.toSet());
        return CollUtil.subtract(afterAtDeptIdsSet, beforeAtDeptIdsSet);
    }
}
