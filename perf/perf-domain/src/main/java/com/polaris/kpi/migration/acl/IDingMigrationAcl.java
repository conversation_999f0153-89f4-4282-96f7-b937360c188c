package com.polaris.kpi.migration.acl;

import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 钉钉迁移仓储接口
 * 定义所有迁移相关的仓储方法，包括DMS调用、OSS调用、HTTP回调等
 */
public interface IDingMigrationAcl {

    /**
     * 从OSS读取数据
     * @param dingCorpId 企业dingCorpId
     * @return 读取数据
     */
    List<String> readFilesFromOss(String dingCorpId);

    /**
     *
     * 从已下载再本地服务的文件中处理
     * @param dingCorpId dingCorpId
     * @param fileName fileName 本地文件地址的文件名
     * @return
     */
    boolean processMigrationFileByName(String dingCorpId, String fileName);

    /**
     * 从OSS导入到数据库
     * @param dingCorpId 企业ID
     * @param ossPath oss文件名（不带域名）
     * @return 是否导入成功
     */
    boolean importFromOssToDatabase(String dingCorpId, String ossPath);

    /**
     * 从手动导入到数据库
     * @param dingCorpId 企业ID
     * @param file file
     * @return 是否导入成功
     */
    boolean importFromOssToDatabaseForZip(String dingCorpId,  MultipartFile file);
    boolean importFromOssToDatabaseTest(String dingCorpId);
    /**
     * 调用钉钉关闭应用接口
     * @param companyId 企业ID
     * @return 是否关闭成功
     */
    boolean callDingTalkCloseApp(String companyId);
}