package com.polaris.kpi.common.event;

import com.polaris.kpi.org.domain.common.BaseEvent;
import lombok.Getter;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.common.event
 * @Author: lufei
 * @CreateTime: 2023-12-01  10:14
 * @Version: 1.0
 */
public class ExceptionEvent extends BaseEvent {
    @Getter
    private Throwable exception;
    private String companyId;
    @Getter
    private String remoteAddr;

    public ExceptionEvent(Throwable exception,String remoteAddr) {
        this.exception = exception;
        this.remoteAddr = remoteAddr;
    }
    public ExceptionEvent(Throwable exception) {
        this.exception = exception;
    }

    public void start() {

    }
}
