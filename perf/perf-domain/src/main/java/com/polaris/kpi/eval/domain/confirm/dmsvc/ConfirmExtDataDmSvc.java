package com.polaris.kpi.eval.domain.confirm.dmsvc;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.perf.www.common.utils.string.StringTool;
import com.polaris.kpi.eval.domain.confirm.repo.ExtDataSyncRepo;
import com.polaris.kpi.eval.domain.task.entity.EvalKpi;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.extData.domain.entity.ExtDataItemFieldCorr;
import com.polaris.kpi.extData.domain.entity.ExtDataSync;
import com.polaris.sdk.type.TenantId;

import java.math.BigDecimal;
import java.util.List;

public class ConfirmExtDataDmSvc {
    private EmpEvalMerge eval;
    private EvalUser taskUser;
    private AdminTask taskBase;

    public ConfirmExtDataDmSvc(EmpEvalMerge eval, EvalUser taskUser, AdminTask taskBase) {
        this.eval = eval;
        this.taskUser = taskUser;
        this.taskBase = taskBase;
    }

    public void buildConfirmExtData(ExtDataSyncRepo extDataSyncRepo) {
        TenantId tenantId = eval.getCompanyId();
//        PerfEvaluateTaskBaseDo taskBase = adminTaskDao.findTaskBase(tenantId, taskUser.getTaskId());
        eval.getKpiTypes().getDatas().stream().filter(type -> CollUtil.isNotEmpty(type.getItems())).flatMap(type -> type.getItems().stream()).forEach(item -> {
            // 考核指标完成值为空查询外部数据
            if (ObjectUtil.isNull(item.getItemFinishValue()) && ObjectUtil.isNull(item.getItemFinishValueText())) {
                List<ExtDataItemFieldCorr> itemFieldCorr = item.getItemFieldCorr();
                if (CollUtil.isNotEmpty(itemFieldCorr)) {
                    for (ExtDataItemFieldCorr corr : itemFieldCorr) {
                        ExtDataSync extData = extDataSyncRepo.findExtData(tenantId.getId(), corr.getExtDataFieldId(), taskUser.getEmpId(), taskBase.getCycleStartDate(), taskBase.getCycleEndDate());
                        if (ObjectUtil.isNotNull(extData)) {
                            JSONObject jsonObject = JSONObject.parseObject(extData.getExtData(), JSONObject.class);
                            jsonObject.getString(corr.getExtDataFieldParamEnName());
                            if (StrUtil.equals("finishValue", corr.getKpiItemFieldId())) {
                                String value = jsonObject.getString(corr.getExtDataFieldParamEnName());
                                if (ObjectUtil.isNotNull(value) && StringTool.isNumber(value)) {
                                    item.setItemFinishValue(new BigDecimal(value));
                                } else {
                                    item.setItemFinishValueText(value);
                                }
                            }
                        }
                    }
                }
            }
        });
    }
}
