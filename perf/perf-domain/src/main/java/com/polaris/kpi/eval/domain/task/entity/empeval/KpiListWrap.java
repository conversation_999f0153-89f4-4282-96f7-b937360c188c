package com.polaris.kpi.eval.domain.task.entity.empeval;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.com.polaris.kpi.eval.Pecent;
import cn.com.polaris.kpi.eval.Rater;
import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.chain.ScoreSceneWrap;
import com.polaris.kpi.eval.domain.task.entity.empeval.invite.InviteItem;
import com.polaris.kpi.eval.domain.task.entity.flow.AsDisplayFlowRs;
import com.polaris.sdk.type.ListWrap;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.lufei.ibatis.common.data.ToDataBuilder;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: lufei
 * @CreateTime: 2022-11-24  09:19
 */
@Setter
@Getter
@NoArgsConstructor
public class KpiListWrap extends ListWrap<EmpEvalKpiType> {
    private int modNum;//已加载的模块码
    @Setter
    @Getter
    private ListWrap<EvalAudit> oldTypeAudits;//1.0的okr分类级流程
    @Setter
    @Getter
    private List<EvalAudit> mergedAudits = new ArrayList<>();//1.0的okr指标级的流程

    public KpiListWrap(List<EmpEvalKpiType> datas, int mod) {
        super(datas);
        this.modNum = mod;
    }

    public KpiListWrap(List<EmpEvalKpiType> datas) {
        super(datas);
    }

    //归一化类别权重
    public void initWeight(TypeWeightConf weightConf, boolean submitWithWgt) {
        for (EmpEvalKpiType type : getDatas()) {
            type.initWeight(weightConf.isOpen(), submitWithWgt);
        }
    }

    public List<EvalScoreResult> sumTotalWeight(List<EvalScoreResult> submitedScores, EvalScoreResult total, Boolean submitWithWght) {
        List<EvalScoreResult> submitTotal = new ArrayList<>();
        if (total == null || !total.isActive()) {
            return submitTotal;
        }
        BigDecimal sumWgt = BigDecimal.ZERO;
        ListWrap<EvalScoreResult> wrap = new ListWrap<>(submitedScores).asMap(rs -> rs.getKpiItemId());
        for (EmpEvalKpiType type : getDatas()) {
            if (type.isPlusType() || type.isSubtractType() || type.isPlusSubType() || !type.isEvalScore()) {//+评等级
                continue;
            }
            if (type.isVoteType()) {
                continue;
            }
            for (EvalKpi item : type.getItems()) {
                EvalScoreResult result = wrap.mapGet(item.getKpiItemId());
                if (result == null || result.isActive()) {
                    continue;
                }
                if (submitWithWght) {
                    result.setTotalSplitWeight(item.getItemWeight().multiply(Pecent.ONE_HUNDRED));//带指标权重打分,当维度100%
                } else {
                    result.setTotalSplitWeight(Pecent.ONE_HUNDRED.multiply(Pecent.ONE_HUNDRED));//不带权重打分,当维度与指标都100%
                }
                submitTotal.add(result);
                sumWgt = sumWgt.add(item.getItemWeight().multiply(type.getKpiTypeWeight()));
            }
        }
        for (EvalScoreResult result : submitTotal) {
            if (result.getTotalSplitWeight() == null) {
                continue;
            }
            result.submitSrcScore(total, total.getUpdatedUser());
            BigDecimal score = sumWgt.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : result.getTotalSplitWeight().multiply(total.getScore()).divide(sumWgt, 2, BigDecimal.ROUND_HALF_UP);
            result.setScore(score);
        }
        return submitTotal;
    }

    //评分环节的配置
    public RaterNodeConf nodeRule(String scene) {
        for (EmpEvalKpiType type : getDatas()) {
            for (EvalKpi item : type.getItems()) {
                if (item.isAutoItem()) {
                    continue;
                }
                EvalItemScoreRule iItemScoreRule = item.getItemScoreRule();
                if (iItemScoreRule == null) {
                    continue;
                }
                if (EvaluateAuditSceneEnum.SELF_SCORE.getScene().equals(scene)
                        && iItemScoreRule.needSelfScore()) {
                    return iItemScoreRule.getSelfRater();
                }
                if (EvaluateAuditSceneEnum.SUPERIOR_SCORE.getScene().equals(scene)
                        && iItemScoreRule.needSupScore()) {
                    return iItemScoreRule.getSuperRater();
                }
            }
        }
        return new RaterNodeConf();//关闭
    }

    public List<EmpEvalKpiType> filterItem(Predicate<? super EvalKpi> condition) {
        List<EmpEvalKpiType> cloneTypes = new ArrayList<>();
        for (EmpEvalKpiType type : getDatas()) {
            List<EvalKpi> items = type.filter(condition);
            if (CollUtil.isNotEmpty(items)) {
                EmpEvalKpiType clone = type.clone();
                clone.setItems(items);
                cloneTypes.add(clone);//浅拷贝一下
            }
        }
        return cloneTypes;
    }

    //分发维度评分记录.
    public List<PerfEvalTypeResult> dispatchTypeIf(NodeFilter nodeFilter, String selEmpId, boolean isOpenAvgWeightCompute) {
        List<PerfEvalTypeResult> datas = new ArrayList<>();
        for (EmpEvalKpiType type : getDatas()) {
            if (type.isNoNeedScoreType()) { //是否是不需要评分的维度
                continue;
            }
            if (nodeFilter.matchTest(type)) {
                List<PerfEvalTypeResult> dispatchs = type.dispatch(nodeFilter.getNode(), nodeFilter.getNodeOrder(), selEmpId, isOpenAvgWeightCompute);
                datas.addAll(dispatchs);
            }
        }
        return datas;
    }

    public List<EvalScoreResult> dispatchIf(NodeFilter nodeFilter, boolean isOpenAvgWeightCompute) {
        List<EvalScoreResult> datas = new ArrayList<>();
        for (EmpEvalKpiType type : getDatas()) {
            if (type.isNoNeedScoreType()) { //是否是不需要评分的维度
                continue;
            }
            for (EvalKpi item : type.getItems()) {
                if (nodeFilter.matchTest(item)) {
                    List<EvalScoreResult> dispatchs = item.dispatch(nodeFilter.getNode(), nodeFilter.getNodeOrder(), isOpenAvgWeightCompute);
                    datas.addAll(dispatchs);
                }
            }
        }
        return datas;
    }

    public List<EvalScoreResult> dispatchDirectIf() {
        List<EvalScoreResult> datas = new ArrayList<>();
        for (EmpEvalKpiType type : getDatas()) {
            for (EvalKpi item : type.getItems()) {
                List<EvalScoreResult> results = item.dispatchDirectIf();
                datas.addAll(results);
            }
        }
        return datas;
    }

    public boolean nodeIsEnd(SubScoreNodeEnum node, int nodeOrder) {
        for (EmpEvalKpiType type : getDatas()) {
            for (EvalKpi item : type.getItems()) {
                item.setScoreOptType(type.getScoreOptType());
                if (!item.isOpenNode(node, nodeOrder)) {
                    continue;
                }
                if (!item.isDispatched(node, nodeOrder)) {
                    return false;
                }
                if (!item.nodeIsEnd(node, nodeOrder)) {
                    return false;
                }
            }

            if (type.isEvalLevel() && type.isOpenNode(node, nodeOrder, null)) {
                if (!type.isDispatched(node, nodeOrder)) {
                    return false;
                }
                if (!type.nodeIsEnd(node, nodeOrder)) {
                    return false;
                }
            }
        }
        return true;
    }

    public void extendsRaterRuleForEdit(EvalItemScoreRule global) {
        for (EmpEvalKpiType type : getDatas()) {
            type.extendsRaterRuleForEdit(global);
        }
    }

    //继承评分流程
    public void extendsRaterRule(EvalItemScoreRule global) {
        for (EmpEvalKpiType type : getDatas()) {
            type.extendsRaterRuleOpt(global);
        }
    }

    public List<EvalScoreResult> markOrModeScoreRs(SubScoreNodeEnum node, String submitId, int nodeOrder, String submitItemId) {
        EvalKpi kpiItem = getKpiItem(submitItemId);
        return kpiItem.markOrModeScoreRs(node, nodeOrder, submitId);
    }

    public List<EvalScoreResult> markTransferScoreRs(String scorerId, String submitId, String submitItemId) {
        EvalKpi kpiItem = getKpiItem(submitItemId);
        return kpiItem.markTransferScoreRs(scorerId, submitId);
    }

    public List<String> orFinishedScorer(List<String> orScorerIds) {
        List<String> passedScorerIds = this.datas.stream().filter(type -> CollUtil.isNotEmpty(type.getItems()))
                .flatMap(type -> {//合并维度与指标上的scoreRs
                    List<BaseTypeScoreResult> merge = new ArrayList<>();
                    merge.addAll(type.getWaitScoresOld());
                    for (EvalKpi item : type.getItems()) {
                        merge.addAll(item.getWaitScoresOld());
                    }
                    return merge.stream();
                }).filter(rs -> StrUtil.isNotBlank(rs.getScorerId())).collect(Collectors.groupingBy(rs -> rs.getScorerId()))
                .entrySet().stream().filter(kv -> {
                    for (BaseTypeScoreResult rs : kv.getValue()) {
                        if (!rs.isPassed()) {
                            return false;
                        }
                    }
                    return true;
                }).map(kv -> kv.getKey()).filter(s -> orScorerIds.contains(s)).collect(Collectors.toList());
        return passedScorerIds;
    }

    //或签过滤掉未完成的评分人
    public List<String> orFilterWithOutFinishScorer(List<String> orScorerIds) {
        if (this.datas == null || this.datas.isEmpty()) {
            return new ArrayList<>();
        }
        // 将 orScorerIds 转换为 Set，提高查找效率
        Set<String> scorerIdSet = new HashSet<>(orScorerIds);
        List<BaseTypeScoreResult> merge = new ArrayList<>();
        for (EmpEvalKpiType type : this.datas) {
            if (Objects.isNull(type)
                    || Objects.isNull(type.getItems())) {
                continue;
            }
            if (CollUtil.isEmpty(type.getWaitScoresOld())) {
                merge.addAll(type.getWaitScoresOld());
            }
            for (EvalKpi item : type.getItems()) {
                if (Objects.isNull(item) || CollUtil.isEmpty(item.getWaitScores())) {
                    continue;
                }
                merge.addAll(item.getWaitScoresOld());
            }
        }
        if (merge.isEmpty()) {
            return new ArrayList<>();
        }
        Map<String, List<BaseTypeScoreResult>> groupedByScorerId = new HashMap<>();
        for (BaseTypeScoreResult rs : merge) {
            if (rs == null || StrUtil.isBlank(rs.getScorerId()) || !orScorerIds.contains(rs.getScorerId())) {
                continue;
            }

            String scorerId = rs.getScorerId();
            groupedByScorerId.computeIfAbsent(scorerId, k -> new ArrayList<>()).add(rs);
        }

        // 筛选所有评分都通过的评分人
        return groupedByScorerId.entrySet().stream()
                .filter(entry -> entry.getValue().stream().allMatch(BaseTypeScoreResult::isPassed))
                .filter(entry -> scorerIdSet.contains(entry.getKey()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    public List<String> allScoreType() {
        List<String> rs = new ArrayList<>();
        for (EmpEvalKpiType data : getDatas()) {
            for (EvalKpi item : data.getItems()) {
                rs.addAll(item.allScoreType());
            }
        }
        rs.stream().distinct();
        return rs;
    }

    public boolean hasAutoItem() {
        for (EmpEvalKpiType type : datas) {
            for (EvalKpi item : type.getItems()) {
                if (item.isAutoItem()) {
                    return true;
                }
            }
        }
        return false;
    }

    public ListWrap<EvalScoreResult> scoreRs() {
        ListWrap<EvalScoreResult> wrap = new ListWrap<>();
        for (EmpEvalKpiType data : datas) {
            for (EvalKpi item : data.getItems()) {
                wrap.addAll(item.getWaitScoresOld());
            }
        }
        wrap.groupBy(evalScoreResult -> evalScoreResult.getKpiItemId());
        return wrap;
    }

    public Collection<String> scoreIds() {
        List<String> scorerIds = new ArrayList<>();
        if (CollUtil.isEmpty(datas)) {
            return Collections.emptyList();
        }
        List<String> itemScorerIds = datas.stream().filter(type -> !type.isEmpty()).flatMap(type -> type.getItems().stream())
                .filter(item -> !item.getWaitScoresOld().isEmpty())
                .flatMap(item -> item.getWaitScoresOld().stream())
                .map(EvalScoreResult::getScorerId).collect(Collectors.toList());
        scorerIds.addAll(itemScorerIds);

        List<String> typeScorerIds = datas.stream().filter(type -> CollUtil.isNotEmpty(type.getWaitScoresOld()))
                .flatMap(type -> type.getWaitScoresOld().stream()).map(BaseScoreResult::getScorerId).collect(Collectors.toList());
        scorerIds.addAll(typeScorerIds);
        return scorerIds;
    }


    public Collection<String> typeScoreIds() {
        if (CollUtil.isEmpty(datas)) {
            return Collections.emptyList();
        }
        return datas.stream().filter(type -> CollUtil.isNotEmpty(type.getWaitScoresOld()))
                .flatMap(type -> type.getWaitScoresOld().stream()).map(BaseScoreResult::getScorerId).collect(Collectors.toList());
    }

    public Boolean hasAppointNode() {
        for (EmpEvalKpiType type : datas) {
            if (CollUtil.isEmpty(type.getItems())) {
                continue;
            }
            for (EvalKpi item : type.getItems()) {
                if (item.isDirectional()) {
                    return true;
                }
                EvalItemScoreRule itemScoreRule = item.getItemScoreRule();
                if (itemScoreRule == null) {
                    continue;
                }
                if (itemScoreRule.needAppointScore()) {
                    return true;
                }
            }
        }
        return false;
    }

    public List<EvalItemScoreRule> itemScoreRules() {
        List<EvalItemScoreRule> rs = new ArrayList<>();
        for (EmpEvalKpiType type : datas) {
            if (CollUtil.isEmpty(type.getItems())) {
                continue;
            }
            for (EvalKpi item : type.getItems()) {
                EvalItemScoreRule itemScoreRule = item.getItemScoreRule();
                if (itemScoreRule == null) {
                    continue;
                }
                rs.add(itemScoreRule);
            }
        }
        return rs;
    }

    public boolean needSetPeerRater(String evalEmpId, String opEmpId, String queryType) {
        for (EmpEvalKpiType kpiType : datas) {
            if (kpiType.isAskType()) {
                if (kpiType.needSetPeerRater(evalEmpId, opEmpId, queryType)) {
                    return true;
                }
            }
            if (kpiType.isNoNeedScoreType()) {
                continue;
            }
            for (EvalKpi item : kpiType.getItems()) {
                if (item.isAutoItem()) {
                    continue;
                }
                if (kpiType.isEvalLevel()) {
                    if (kpiType.needSetPeerRater(evalEmpId, opEmpId, queryType)) {
                        return true;
                    }
                }
                EvalItemScoreRule itemScoreRule = item.getItemScoreRule();
                if (itemScoreRule == null) {
                    return false;
                }
                if (itemScoreRule.needSetPeerRater(evalEmpId, opEmpId, queryType)) {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean needSetSubRater(String evalEmpId, String opEmpId, String queryType) {
        for (EmpEvalKpiType kpiType : datas) {
            if (kpiType.isAskType()) {
                if (kpiType.needSetSubRater(evalEmpId, opEmpId, queryType)) {
                    return true;
                }
            }
            if (kpiType.isNoNeedScoreType()) {
                continue;
            }
            for (EvalKpi item : kpiType.getItems()) {
                if (item.isAutoItem()) {
                    continue;
                }
                if (kpiType.isEvalLevel()) {
                    if (kpiType.needSetSubRater(evalEmpId, opEmpId, queryType)) {
                        return true;
                    }
                }
                EvalItemScoreRule itemScoreRule = item.getItemScoreRule();
                if (itemScoreRule == null) {
                    return false;
                }
                if (itemScoreRule.needSetSubRater(evalEmpId, opEmpId, queryType)) {
                    return true;
                }
            }
        }
        return false;
    }

    public Boolean matchAnySuperScore(String scorerId) {
        for (EmpEvalKpiType type : datas) {
            if (type.matchAnySuperScore(scorerId)) {
                return true;
            }
            for (EvalKpi item : type.getItems()) {
                if (item.matchAnySuperScore(scorerId)) {
                    return true;
                }
            }
        }
        return false;
    }

    public Boolean matchAnySelfAutoScore(String scorerId) {
        for (EmpEvalKpiType type : datas) {
            for (EvalKpi item : type.getItems()) {
                if (item.matchAnySelfAutoScore(scorerId)) {
                    return true;
                }
            }
        }
        return false;
    }
    public Boolean matchAnySelfAutoScoreV3(String scorerId) {
        for (EmpEvalKpiType type : datas) {
            for (EvalKpi item : type.getItems()) {
                if (item.matchAnySelfAutoScoreV3(scorerId)) {
                    return true;
                }
            }
        }
        return false;
    }

    public Boolean matchAnySuperScoreV3(String scorerId) {
        for (EmpEvalKpiType type : datas) {
            if (type.matchAnySuperScoreV3(scorerId)) {
                return true;
            }
            for (EvalKpi item : type.getItems()) {
                if (item.matchAnySuperScoreV3(scorerId)) {
                    return true;
                }
            }
        }
        return false;
    }


    public List<Integer> typeAppointNodes() {
        Set<Integer> set = new HashSet<>();
        for (EmpEvalKpiType type : datas) {
            for (BaseAuditNode node : type.getAppointRater().auditNodes()) {
                set.add(node.getApprovalOrder());
            }
            if (CollUtil.isEmpty(type.getItems())) {
                continue;
            }
            for (EvalKpi item : type.getItems()) {
                if (item.isDirectional()) {
                    continue;
                }
                EvalItemScoreRule itemScoreRule = item.getItemScoreRule();
                if (itemScoreRule == null || Objects.isNull(itemScoreRule.getAppointRater())) {
                    continue;
                }
                if (itemScoreRule.needAppointScore()) {
                    for (BaseAuditNode node : itemScoreRule.getAppointRater().auditNodes()) {
                        set.add(node.getApprovalOrder());
                    }
                }
            }
        }
        ArrayList<Integer> integers = new ArrayList<>(set);
        Collections.sort(integers);
        return integers;
    }

    public ScoreSceneWrap buildNodes() {
        ScoreSceneWrap nodes = new ScoreSceneWrap();
        for (EmpEvalKpiType type : getDatas()) {
            if (type.isEvalLevel()) {
                EvalItemScoreRule rule =  type.getTypeRule();
                if (rule != null) {
                    rule.buildNodes(nodes);
                }
            }
            type.buildItemNodes(nodes);
        }
        return nodes;
    }

    //显示流程进度
    public AsDisplayFlowRs asDisplayFlow(AsDisplayFlowRs disFlows, String evalEmpId) {
        for (EmpEvalKpiType type : getDatas()) {
            type.asDisplayFlow(disFlows, evalEmpId);
            type.itemAsDisplayFlow(disFlows);
        }
        return disFlows;
    }

    public List<String> allItemsInputEmpIds(String evalEmpId) {
        List<String> inputEmpIds = getDatas().stream()
                .filter(it -> Objects.nonNull(it.getItems())) // 过滤掉 items 为 null 的数据，避免空指针
                .flatMap(it -> it.getItems().stream())
                .flatMap(o -> o.inputEmp(evalEmpId).stream())
                .distinct()
                .collect(Collectors.toList());
        return inputEmpIds;
    }

    public void transferScoringIf(String scoreType, String fromEmpId, KpiEmp toEmp, Boolean raterWasExist, Boolean wasCustom) {
        for (EmpEvalKpiType data : datas) {
            data.transferScoringIf(scoreType, fromEmpId, toEmp, raterWasExist, wasCustom);
            for (EvalKpi item : data.getItems()) {
                item.transferScoringIf(scoreType, fromEmpId, toEmp, raterWasExist, wasCustom);
            }
        }
    }

    public void transferScoringIfV3(String scoreType, String fromEmpId, KpiEmp toEmp, Boolean wasCustom) {
        datas.forEach(data -> {
            data.transferScoringIfV3(scoreType, fromEmpId, toEmp, wasCustom);
            data.getItems().forEach(item -> item.transferScoringIfV3(scoreType, fromEmpId, toEmp, wasCustom));
        });
    }

    public void skipRaterIf(String scoreType, String skipUserId) {
        for (EmpEvalKpiType type : datas) {
            type.skipRaterIf(scoreType, skipUserId);
            for (EvalKpi item : type.getItems()) {
                item.skipRaterIf(scoreType, skipUserId);
            }
        }

    }

    //任意分发则表环节已分发了
    public boolean isDispactched(SubScoreNodeEnum node, int order) {
        for (EmpEvalKpiType type : getDatas()) {
            for (EvalKpi item : type.getItems()) {
                if (!item.isOpenNode(node, order)) {
                    continue;
                }
                if (!item.isDispatched(node, order)) {
                    return false;
                }
            }
            if (type.isEvalLevel() && type.isOpenNode(node, order, null)) {
                if (!type.isDispatched(node, order)) {
                    return false;
                }
            }
        }
        return true;
    }

    public List<InviteItem> listMutualItem(String scene, String opEmpId, String evalEmpId, String queryType) {
        List<InviteItem> inviteItems = new ArrayList<>();
        for (EmpEvalKpiType kpiType : datas) {
            if (kpiType.isAskType()) {
                if (kpiType.needSetPeerRater(evalEmpId, opEmpId, queryType) && Objects.equals(scene, "peer_score")) {
                    InviteItem inviteItem = new InviteItem(kpiType.getKpiTypeId(), kpiType.getAsk360TempName(), evalEmpId, true, "ask360");
                    inviteItems.add(inviteItem);
                    continue;
                }
                if (kpiType.needSetSubRater(evalEmpId, opEmpId, queryType) && Objects.equals(scene, "sub_score")) {
                    InviteItem inviteItem = new InviteItem(kpiType.getKpiTypeId(), kpiType.getAsk360TempName(), evalEmpId, true, "ask360");
                    inviteItems.add(inviteItem);
                    continue;
                }
            }
            for (EvalKpi item : kpiType.getItems()) {
                if (item.isAutoItem()) {
                    continue;
                }
                if (kpiType.isEvalLevel()) {
                    if (kpiType.needSetPeerRater(evalEmpId, opEmpId, queryType) && Objects.equals(scene, "peer_score")) {
                        InviteItem inviteItem = new ToDataBuilder<>(item, InviteItem.class).data();
                        inviteItem.setScoreOptType(kpiType.getScoreOptType());
                        inviteItem.setTypeMutualAuditPos(kpiType.getPeerRater().getRaters());
                        inviteItem.accpOrder(kpiType.getTypeOrder(), item.getOrder());
                        inviteItems.add(inviteItem);
                        continue;
                    }
                    if (kpiType.needSetSubRater(evalEmpId, opEmpId, queryType) && Objects.equals(scene, "sub_score")) {
                        InviteItem inviteItem = new ToDataBuilder<>(item, InviteItem.class).data();
                        inviteItem.setScoreOptType(kpiType.getScoreOptType());
                        inviteItem.setTypeMutualAuditPos(kpiType.getSubRater().getRaters());
                        inviteItem.accpOrder(kpiType.getTypeOrder(), item.getOrder());
                        inviteItems.add(inviteItem);
                        continue;
                    }
                }
                EvalItemScoreRule itemScoreRule = item.getItemScoreRule();
                if (itemScoreRule == null) {
                    continue;
                }
                if (itemScoreRule.needSetPeerRater(item.getEmpId(), opEmpId, queryType) && Objects.equals(scene, "peer_score")) {
                    InviteItem inviteItem = new InviteItem(item.getKpiTypeId(), item.getKpiTypeName(), item.getKpiItemId(), item.getKpiItemName(), item.getItemRule(), item.getEmpId(), item.getScoringRule());
                    inviteItem.accpOrder(kpiType.getTypeOrder(), item.getOrder());
                    inviteItems.add(inviteItem);
                    continue;
                }
                if (itemScoreRule.needSetSubRater(item.getEmpId(), opEmpId, queryType) && Objects.equals(scene, "sub_score")) {
                    InviteItem inviteItem = new InviteItem(item.getKpiTypeId(), item.getKpiTypeName(), item.getKpiItemId(), item.getKpiItemName(), item.getItemRule(), item.getEmpId(), item.getScoringRule());
                    inviteItem.accpOrder(kpiType.getTypeOrder(), item.getOrder());
                    inviteItems.add(inviteItem);
                    continue;
                }
            }
        }
        return inviteItems;
    }

    public Set<String> listMutualAppointEmpId(String evalEmpId) {
        Set<String> appointEmpIds = new HashSet<>();
        for (EmpEvalKpiType kpiType : datas) {
            if (kpiType.isAskType()) {
                appointEmpIds.addAll(kpiType.appointEmpIds(evalEmpId));
                continue;
            }
            if (kpiType.isEvalLevel()) {
                appointEmpIds.addAll(kpiType.appointEmpIds(evalEmpId));
            }
            for (EvalKpi item : kpiType.getItems()) {
                if (item.isAutoItem()) {
                    continue;
                }
                EvalItemScoreRule itemScoreRule = item.getItemScoreRule();
                if (itemScoreRule == null) {
                    continue;
                }
                appointEmpIds.addAll(itemScoreRule.appointEmpIds(evalEmpId));
            }
        }
        return appointEmpIds;
    }

    public Set<String> scoreNode(String scorerId) {
        Set<String> scorerTypes = new HashSet<>();
        if (CollUtil.isEmpty(datas)) {
            return Collections.emptySet();
        }
        Set<String> itemNode = datas.stream().flatMap(type -> !type.isEmpty() ? type.getItems().stream() : Stream.empty())
                .flatMap(item -> !item.getWaitScores().isEmpty() ? item.getWaitScores().stream() : Stream.empty())
                .filter(scoreRs -> StrUtil.equals(scorerId, scoreRs.getScorerId()))
                .map(EvalScorerNodeScoreItemBase::getScorerType)
                .collect(Collectors.toSet());
        scorerTypes.addAll(itemNode);

        Set<String> typeNode = datas.stream().flatMap(type -> CollUtil.isNotEmpty(type.getWaitScoresOld()) ? type.getWaitScoresOld().stream() : Stream.empty())
                .filter(scoreRs -> StrUtil.equals(scorerId, scoreRs.getScorerId()))
                .map(BaseScoreResult::getScorerId).collect(Collectors.toSet());
        scorerTypes.addAll(typeNode);
        return scorerTypes;
    }

    public EvalKpi getKpiItem(String itemId) {
        for (EmpEvalKpiType type : this.datas) {
            EvalKpi evalKpi = type.getKpiItem(itemId);
            if (evalKpi != null) {
                return evalKpi;
            }
        }
        return null;
    }

    //仅所有指标互评人相同才会调用
    public void recomputeUseDelMinMax(SubScoreNodeEnum node, List<Rater> raters, boolean isOpenAvgWeightCompute) {
        NodeRaterListWrap raterWap = new NodeRaterListWrap(raters);
        for (NodeRater nodeRater : raterWap.getDatas()) {
            for (EmpEvalKpiType type : datas) {//收集互评人评分信息Rs
                nodeRater.addAllScoreRs(type.listItemScore(node, nodeRater.getScorerId()));
            }
        }
        raterWap.recomputeUseDelMinMax(isOpenAvgWeightCompute);
    }

}
