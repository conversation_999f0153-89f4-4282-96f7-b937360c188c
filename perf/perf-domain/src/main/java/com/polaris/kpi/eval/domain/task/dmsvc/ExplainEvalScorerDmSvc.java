package com.polaris.kpi.eval.domain.task.dmsvc;

import cn.hutool.core.bean.BeanUtil;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.*;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;


/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.domain.task.dmsvc
 * @Author: suxiaoqiu
 * @CreateTime: 2025-04-21  09:22
 * @Description: 解析评分人领域服务
 * @Version: 2.0
 */
@Slf4j
@Getter
public class ExplainEvalScorerDmSvc {
    private final EvalUser taskUser;// 任务用户
    private final EmpEvalRule rule;
    private final TenantId companyId;//
    private final String opEmpId;//当前操作人

    private EvalScorersWrap scorers = new EvalScorersWrap();//评分人

    public ExplainEvalScorerDmSvc(EvalUser taskUser, EmpEvalRule rule, TenantId companyId, String opEmpId) {
        this.taskUser = taskUser;
        this.rule = rule;
        this.companyId = companyId;
        this.opEmpId = opEmpId;
    }

    public ExplainEvalScorerDmSvc(EvalUser taskUser, EmpEvalMerge evalRule, TenantId companyId, String opEmpId) {
        this.taskUser = taskUser;
        EmpEvalRule rule = new EmpEvalRule();
        BeanUtil.copyProperties(evalRule, rule);
        this.rule = rule;
        this.companyId = companyId;
        this.opEmpId = opEmpId;
    }

    private boolean isCustom() {
        return rule.isCustom();
    }

    public void explainEvalScorer() {
        //解析出上级评是同时还是依次评
        this.rule.setSuperiorScoreOrder();

        AbstractEvalScorerDmSvc scorerDmSvc;
        if (isCustom()) {
            scorerDmSvc = new ExplainScoreCustomFlowDmSvc(taskUser, rule, companyId, opEmpId);//自定义评分流程
        } else {
            scorerDmSvc = new ExplainScoreStandardFlowDmSvc(taskUser, rule, companyId, opEmpId);//统一评分流程(标准流程)
        }
        scorerDmSvc.explainEvalScorer();//解析评分人
        scorerDmSvc.explainTotalEvalScorer();//解析打总等级
        this.scorers = scorerDmSvc.getScorers();

    }
}
