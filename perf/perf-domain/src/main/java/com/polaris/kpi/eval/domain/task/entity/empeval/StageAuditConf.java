package com.polaris.kpi.eval.domain.task.entity.empeval;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.ObjItem;
import cn.com.polaris.kpi.eval.Rater;
import cn.com.polaris.kpi.eval.RaterNode;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.perf.www.common.em.EvaluateTypeEnum;
import com.polaris.kpi.common.DelableDomain;
import com.polaris.kpi.eval.domain.task.entity.EvalItemScoreRule;
import com.polaris.kpi.eval.domain.task.entity.EvalKpi;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.*;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.JsonAryColumn;

import java.util.*;
import java.util.stream.Collectors;

@Getter
@Setter
@Slf4j
public class StageAuditConf extends DelableDomain {

    private String evaluateType;
    private String taskStatus;            //员工任务状态
    @JsonAryColumn(RaterNode.class)
    private List<RaterNode> indexRaters;                   //评分人索引,用于员工任务列表的显示,编辑时更新索引
    private ScoreSortConf scoreSortConf;         //2.0.0新版:新加字段 评分环节顺序配置
    @JsonAryColumn(RaterNode.class)
    private List<RaterNode> askIndexRaters;                   //评分人索引,用于员工任务列表的显示,编辑时更新索引
    private List<Rater> totalLevelRaters; //绩效总等级打分人员,只有具体人员, 从模板或表上解析后的结果
    private boolean needInputValue = true;
    private Set<Rater> inputFinishValueIndexRaters = new HashSet<>();                   //录入完成值索引,用于员工任务列表的显示,编辑时更新索引


    //评分配置
    private RaterNodeConf s3SelfRater;              //360或简易 自评人
    private MutualNodeConf s3PeerRater;                   //360或简易 同级互评人
    private MutualNodeConf s3SubRater;                    //360下级
    private S3SuperRaterConf s3SuperRater;          //360或简易 上级人
    private S3RaterBaseConf s3AppointRater;          //默认流程指定评分人

    //结果配置
    private AffirmTaskConf confirmTask;             //确认任务  对应模板 templAffirmJson:PerfTemplEvaluateAffirm
    private InputNotifyConf inputNotify;              //完成值录入通知设置
    private FinishValueAuditConf finishValueAudit;             //完成值审核
    private FinishValueAuditConf finishValueAuditAll;             //完成值审核(包含指标信息)
    private EditExeIndiConf editExeIndi;            //执行中修改指标  对应模板 templExecuteJson:PerfTemplEvaluateExecute
    private AuditResultConf auditResult;  //结果校准链
    private InterviewConf interviewConf;             //结果面谈,多个字段合并
    private ConfirmResultConf confirmResult;         //结果确认,多个字段合并
    private PublishResultConf publishResult;         //结果公示,多个字段合并
    private AppealConf appealConf;                   //结果申诉,多个字段合并
    private DeadLineConf deadLineConf;               //阶段截止时间配置

    @JSONField(serialize = false)
    private KpiListWrap kpiTypes;

    private Set<String> opStages = new HashSet<>();          //开启的环节
    private Integer scoreIndexType = 0;     //评分索引类型 1：评分 2：自动计算 4：okr


    public void copyEvalRule(EmpEvalRule evalRule, List<EmpEvalKpiType> kpiTypes, AdminTask adminTask) {
        this.setScoreSortConf(evalRule != null ? evalRule.getScoreSortConf() : adminTask.getScoreSortConf());
        this.setIndexRaters(evalRule != null ? evalRule.getIndexRaters() : null);
        this.setAskIndexRaters(evalRule != null ? evalRule.getAskIndexRaters() : null);
        this.setTotalLevelRaters(evalRule != null ? evalRule.getTotalLevelRaters() : null);
        this.setAuditResult(evalRule != null ? evalRule.getAuditResult() : null);
        this.setConfirmTask(evalRule.getConfirmTask());
        log.info("evalRule.getEditExeIndi() = {}", JSONUtil.toJsonStr(evalRule.getEditExeIndi()));
        this.setEditExeIndi(evalRule.getEditExeIndi());
        log.info("setEditExeIndi = {}", JSONUtil.toJsonStr(getEditExeIndi()));
        this.setInputNotify(evalRule.getInputNotify());
        this.setFinishValueAudit(evalRule.getFinishValueAudit());
        this.setAuditResult(evalRule.getAuditResult());
        this.setInterviewConf(evalRule.getInterviewConf());
        this.setPublishResult(evalRule.getPublishResult());
        this.setConfirmResult(evalRule.getConfirmResult());
        this.setAppealConf(evalRule.getAppealConf());

        this.setS3SelfRater(evalRule.getS3SelfRater());
        this.setS3PeerRater(evalRule.getS3PeerRater());
        this.setS3SubRater(evalRule.getS3SubRater());
        this.setS3AppointRater(evalRule.getS3AppointRater());
        this.setS3SuperRater(evalRule.getS3SuperRater());

        this.setEvaluateType(evalRule.getEvaluateType());

        this.setKpiTypes(new KpiListWrap(kpiTypes));

    }

    public void loadNodeAuditStatus(List<EvalScoreResult> groupGet,String empId,String empName) {
        ListWrap<EvalScoreResult> scoreTypeWrap = new ListWrap<>(groupGet).groupBy(EvalScoreResult::getScorerType);
        //指标确认
        if (Objects.nonNull(this.getConfirmTask()) && this.getConfirmTask().isOpen()) {
            opStages.add("confirming");
            markAuditStatus(this.getConfirmTask().getAuditNodes(),scoreTypeWrap.groupGet("modify_item_audit"),TalentStatus.CONFIRMING.getOrder());
        }
        //完成值录入
        markInputFinishValue(empId);
        //执行变更
        if (Objects.nonNull(this.getEditExeIndi()) && this.getEditExeIndi().auditIsOpen()) {
            opStages.add("changing");
            markAuditStatus(this.getEditExeIndi().getAuditNodes(),scoreTypeWrap.groupGet("change_item_audit"),TalentStatus.CHANGING.getOrder());
        }
        //完成值审核
        loadFinishValueAuditIndexRaters(scoreTypeWrap.groupGet("finish_value_audit"),opStages,empName);
        //评分
        opStages.add("scoring");
        markScoreRater(scoreTypeWrap.getDatas());

        //绩效校准
        if (Objects.nonNull(this.getAuditResult()) && this.getAuditResult().isOpen()) {
            opStages.add("resultsAuditing");
            markAuditStatus(this.getAuditResult().getAuditNodes(),scoreTypeWrap.groupGet("final_result_audit"),TalentStatus.RESULTS_AUDITING.getOrder());
        }
        //绩效面谈
        if (Objects.nonNull(this.getInterviewConf()) && this.getInterviewConf().isOpen()) {
            opStages.add("resultsInterview");
            markAuditStatus(this.getInterviewConf().getInterviewConfirmConf().getInterviewConfirmInfo(),scoreTypeWrap.groupGet("change_item_audit"),TalentStatus.CHANGING.getOrder());
        }
        if (Objects.nonNull(this.getAppealConf()) && this.getAppealConf().isOpen()) {
            opStages.add("resultsAppeal");
        }
        if (Objects.nonNull(this.getPublishResult()) && this.getPublishResult().isOpen()) {
            opStages.add("waitPublish");
        }
    }

    public void loadInterviewAuditStatus(boolean executeEnd,Map<String,Integer> confirmMap) {
        if (afterCurrentStage("resultsInterview")) {
            return;
        }
        if (beforeCurrentStage("resultsInterview")) {
            this.getInterviewConf().getInterviewExcutorInfo().markAll();
            if (this.getInterviewConf().getInterviewConfirmConf().isOpen()) {
                for (BaseAuditNode node : this.getInterviewConf().getInterviewConfirmConf().getInterviewConfirmInfo()) {
                    node.markAll();
                }
            }
        }else {
            this.getInterviewConf().getInterviewExcutorInfo().markAll(executeEnd ?  "finished" :"wait" );
            if (this.getInterviewConf().getInterviewConfirmConf().isOpen()) {
                for (BaseAuditNode node : this.getInterviewConf().getInterviewConfirmConf().getInterviewConfirmInfo()) {
                    for (Rater rater : node.getRaters()) {
                        Integer status = confirmMap.get(rater.getEmpId());
                        rater.setStatus(Objects.isNull(status) ? "wait" : (Objects.equals(status,0) ? "doing" : "finished"));
                    }
                }
            }
        }

    }

    private <T extends BaseAuditNode> void markAuditStatus(List<T> auditNodes, List<EvalScoreResult> results, Integer order) {
        //若为指标确认、执行变更等产生result记录的环节，则从auditResults中取值状态，标记状态，若已分发未通过则为"doing"

        //若未分发则为"wait" 若已通过则为"pass"

        //若为完成值录入等关联在指标上的则从types取。
        //1、判断当前考核状态，将当前状态之前且开启的环节都打上已审核标记
        ListWrap<EvalScoreResult> resultListWrap = new ListWrap<>(results).groupBy(rs -> String.valueOf(rs.getApprovalOrder()));
        if (order > TalentStatus.statusOf(this.taskStatus).getOrder()) {
            return;
        }
        auditNodes.forEach(node -> {
            node.markRater(resultListWrap.groupGet(String.valueOf(node.getApprovalOrder())));
        });
    }

    private void markScoreRater(List<EvalScoreResult> results) {
        initScoreIndexType();
        if (afterCurrentStage("scoring")) {
            return;
        }
        if (beforeCurrentStage("scoring")) {
            this.indexRaters.forEach(idx ->{
                idx.markAllRater();
            });
        }else {
            List<EvalScoreResult> filterList = CollUtil.filterNew(results,r -> r.isScorerNode());
            ListWrap<EvalScoreResult> resultListWrap = new ListWrap<>(filterList).groupBy(f -> f.getScorerType());
            for (RaterNode idx : this.indexRaters) {
                List<EvalScoreResult> nodeList = resultListWrap.groupGet(idx.getNode()+"_score");
                if (CollUtil.isEmpty(nodeList)) {
                    continue;
                }
                //执行中
                if (nodeList.stream().allMatch(rs -> StrUtil.isBlank(rs.getAuditStatus()))) {
                    idx.getRaters().forEach(r -> {r.doingStatus();});
                    continue;
                }
                //已完成
                if (nodeList.stream().allMatch(rs -> "pass".equals(rs.getAuditStatus()))) {
                    idx.getRaters().forEach(r -> {r.passStatus();});
                    continue;
                }
            }
        }
    }

    private void markInputFinishValue(String empId) {
        List<EvalKpi> items = this.kpiTypes.getDatas().stream().
                flatMap(type -> type.getItems().stream()).collect(Collectors.toList());
        if (CollUtil.isEmpty(items)) {
            return;
        }
        if (items.stream().allMatch(item -> item.isNotNeedInput())) {
            this.needInputValue = false;
            return;
        }
        for (EvalKpi item : items) {
            for (String s : item.inputEmp(empId)) {
                Rater rater = new Rater(s);
                List<EvalKpi> filterItems = CollUtil.filterNew(items,idx -> idx.inputEmp(empId).contains(s));
                if (CollUtil.isNotEmpty(filterItems)) {
                    if (filterItems.stream().allMatch(idx -> idx.isFinishValueFinishd())) {
                        rater.passStatus();
                    }else {
                        rater.doingStatus();
                    }
                }
                this.inputFinishValueIndexRaters.add(rater);
            }
        }
    }

    public void assembleFinishValueAudit(String empName) {
        this.finishValueAuditAll = this.finishValueAudit.clone();
        //处理指标上的完成值审核配置
        List<ObjItem> objItems = this.kpiTypes.getDatas().stream().
                flatMap(type -> type.getItems().stream()
                        .filter(item -> item.isFinishValueAudit())
                        .flatMap(finish -> finish.getFinishValueAudit().stream().flatMap(objItem -> objItem.getObjItems().stream()))).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(objItems)) {
            String multiType = this.finishValueAudit.isOpen() ? this.finishValueAudit.getAuditNodes().get(0).getMultiType() : "or";
            BaseAuditNode node1 = new BaseAuditNode(1,multiType);
            List<Rater> raters = new ArrayList<>();
            for (ObjItem objItem : objItems) {
                raters.add(new Rater(objItem.getObjId(),StrUtil.isBlank(objItem.getObjName().replaceAll(" ","")) ? empName : objItem.getObjName()));
            }
            node1.setRaters(raters);
            //开启完成值审核环节
            this.finishValueAuditAll.setOpen(1);
            this.finishValueAuditAll.addOrder();
            this.finishValueAuditAll.getAuditNodes().add(node1);
        }
        if (!this.finishValueAudit.isOpen()) {
            return;
        }
        this.finishValueAuditAll.getAuditNodes().sort(Comparator.comparing(BaseAuditNode::getApprovalOrder));
    }

    private void loadFinishValueAuditIndexRaters(List<EvalScoreResult> results,Set<String> opStages,String empName) {
        assembleFinishValueAudit(empName);
        if (!this.finishValueAuditAll.isOpen()) {
            return;
        }
        opStages.add("finishValueAudit");
        markAuditStatus(this.finishValueAuditAll.getAuditNodes(), results,TalentStatus.statusOf("finishValueAudit").getOrder());
    }

    public Set<String> getInputFinishValueEmpIds() {
        if (CollUtil.isEmpty(inputFinishValueIndexRaters)) {
            return new HashSet<>();
        }
        return inputFinishValueIndexRaters.stream().map(r -> r.getEmpId()).collect(Collectors.toSet());
    }

    public void matchInputEmpName(ListWrap<KpiEmp> emps) {
        if (Objects.nonNull(this.kpiTypes)) {
            kpiTypes.getDatas().forEach(type ->{
                type.loadInputEmps(emps);
            });
        }
        if (CollUtil.isEmpty(inputFinishValueIndexRaters)) {
            return;
        }
        for (Rater rater : inputFinishValueIndexRaters) {
            KpiEmp emp = emps.mapGet(rater.getEmpId());
            if (Objects.nonNull(emp)) {
                rater.setEmpName(emp.getEmpName());
            }
        }
        this.inputFinishValueIndexRaters = CollUtil.filterNew(this.inputFinishValueIndexRaters,index -> StrUtil.isNotBlank(index.getEmpName())).stream().collect(Collectors.toSet());

    }


    private boolean afterCurrentStage(String taskStatus) {
        return TalentStatus.statusOf(taskStatus).getOrder() > TalentStatus.statusOf(this.taskStatus).getOrder();
    }

    private boolean beforeCurrentStage(String taskStatus) {
        return TalentStatus.statusOf(taskStatus).getOrder() < TalentStatus.statusOf(this.taskStatus).getOrder();
    }


    public void initScoreIndexType() {
        if (CollUtil.isNotEmpty(this.indexRaters)) {
            this.scoreIndexType = scoreIndexType | 1;
            return;
        }
        if (isExistAutoItem()) {
            this.scoreIndexType = scoreIndexType | 2;
        }
        if (isExistOkr()) {
            this.scoreIndexType = scoreIndexType | 4;
        }
    }

    @JSONField(serialize = false)
    private boolean isExistAutoItem() {
        return this.kpiTypes.getDatas().stream().flatMap(type -> type.getItems().stream())
                .filter(rs -> rs.isAutoItem()).collect(Collectors.toList()).size() > 0;
    }

    @JSONField(serialize = false)
    private boolean isExistOkr() {
        return this.kpiTypes.getDatas().stream()
                .filter(type -> type.isOkr()).collect(Collectors.toList()).size() > 0;
    }

    @JSONField(serialize = false)
    public boolean isCustom() {
        return EvaluateTypeEnum.CUSTOM.getType().equals(this.evaluateType);
    }

    public void setItemRule(List<EvalItemScoreRule>itemScoreRules) {
        if (CollUtil.isEmpty(itemScoreRules)) {
            return;
        }
        if (Objects.isNull(this.kpiTypes)) {
            return;
        }
        ListWrap<EvalItemScoreRule> wrap = new ListWrap<>(itemScoreRules).groupBy(EvalItemScoreRule::getKpiTypeId);
        for (EmpEvalKpiType data : this.kpiTypes.getDatas()) {
            data.setItemRule(wrap.groupGet(data.getKpiTypeId()));
        }
    }

    //初始化评分人索引
    public List<RaterNode> initIndexRaters(String companyId,String empEvalId) {
        extendsRaterRule(companyId,empEvalId);
        EvalItemScoreRule global = new EvalItemScoreRule();
        global.setCompanyId(new TenantId(companyId));
        global.setTaskUserId(empEvalId);
        global.asRaterConf(s3SelfRater, s3PeerRater, s3SubRater, s3SuperRater, s3AppointRater);
        Map<String, RaterNode> nodeMap = new HashMap<>();
        Map<String, RaterNode> askNodeMap = new HashMap<>();
        for (EmpEvalKpiType kpiType : kpiTypes.getDatas()) {
            if (Objects.equals(kpiType.getKpiTypeClassify(), "ask360")) {
                if(kpiType.isOpenRaterRule()){
                    kpiType.initAskNodeRater(askNodeMap);
                }else {
                    global.initNodeRater(askNodeMap);
                }
                continue;
            }
            kpiType.initNodeRater(nodeMap);
        }
        Collection<RaterNode> values = nodeMap.values();
        Collection<RaterNode> askValues = askNodeMap.values();
        this.indexRaters = new ArrayList<>(values);
        this.askIndexRaters = new ArrayList<>(askValues);
        List<RaterNode> sorts = new ArrayList<>();
        List<RaterNode> askSorts = new ArrayList<>();
        ListWrap<RaterNode> wrap = new ListWrap<>(indexRaters).asMap(raterNode -> raterNode.getNode());
        ListWrap<RaterNode> askWrap = new ListWrap<>(askIndexRaters).asMap(raterNode -> raterNode.getNode());
        List<ScoreSortConf.SortItem> sortItems = scoreSortConf.sort();
        for (ScoreSortConf.SortItem sortItem : sortItems) {
            String nodeName = sortItem.isSelfNode() ? "self"
                    : sortItem.isAppointNode() ? "appoint"
                    : sortItem.isSuperNode() ? "super"
                    : sortItem.isSubNode() ? "sub"
                    : sortItem.isPeerNode() ? "peer" : "";
            RaterNode node = wrap.mapGet(nodeName);
            RaterNode askNode = askWrap.mapGet(nodeName);
            if (Objects.nonNull(node)) {
                sorts.add(node);
            }
            if (Objects.nonNull(askNode)) {
                askSorts.add(askNode);
            }
        }
        this.indexRaters = sorts;
        //添加打总等级
        if (CollUtil.isNotEmpty(this.totalLevelRaters)) {
            this.indexRaters.add(new RaterNode("totalLevel",this.totalLevelRaters));
        }
        //添加定向评
        if (Objects.nonNull(wrap.mapGet("item"))) {
            this.indexRaters.add(0,wrap.mapGet("item"));
        }
        this.askIndexRaters = askSorts;
        return indexRaters;
    }


    //接收指标分类并标准化 继承评分流程
    private void extendsRaterRule(String companyId,String empEvalId) {
        EvalItemScoreRule global = new EvalItemScoreRule();
        global.setCompanyId(new TenantId(companyId));
        global.setTaskUserId(empEvalId);
        global.asRaterConf(s3SelfRater, s3PeerRater, s3SubRater, s3SuperRater, s3AppointRater);
        this.kpiTypes.extendsRaterRule(global);
    }
}
