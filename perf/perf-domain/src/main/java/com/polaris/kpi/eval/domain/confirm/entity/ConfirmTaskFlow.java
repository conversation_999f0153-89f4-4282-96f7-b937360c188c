package com.polaris.kpi.eval.domain.confirm.entity;

import cn.com.polaris.kpi.eval.Rater;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.perf.www.common.em.OperationLogSceneEnum;
import com.polaris.kpi.eval.domain.task.entity.EvalAudit;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.domain.task.entity.OperationLog;
import com.polaris.kpi.eval.domain.task.entity.empeval.BaseScoreResult;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.BaseAuditNode;
import com.polaris.sdk.type.*;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 确认流程
 */
public class ConfirmTaskFlow {
    private TenantId companyId;
    private String taskUserId;
    private List<ConfirmNode> nodes;//当前层级的审核人员
    private GroupWrap<Integer, EvalScoreResult> rssGroups;
    private Boolean noChangeSkipAll;

    public ConfirmTaskFlow(TenantId companyId, String taskUserId, Boolean noChangeSkip, List<ConfirmNode> nodes,
                           List<EvalScoreResult> rssGroups, List<EvalAudit> eas) {
        this.companyId = companyId;
        this.taskUserId = taskUserId;
        this.noChangeSkipAll = noChangeSkip;
        this.nodes = nodes;
        this.rssGroups = new GroupWrap<>(rssGroups, BaseScoreResult::getApprovalOrder);
        nodes.forEach(node -> {
            if (node.isOnIndAudit()) {
                node.acceptItemAudits(eas);
            }
            List<EvalScoreResult> results = this.rssGroups.groupGet(node.getApprovalOrder());
            node.onResults(results);
            node.onStatus();
        });
    }

    public void refreshAuditIndFlow(List<EvalAudit> auditIndFlow){
        nodes.stream().filter(BaseAuditNode::isOnIndAudit).forEach(node -> node.acceptItemAudits(auditIndFlow));
    }



    public AcceptSumbitRs acceptPass(EmpId confirmEmpId, Integer approveOrder) {
        // AcceptSumbitRs rs = super.submitPass(onlySkipNode, confirmEmpId, approveOrder);
        ConfirmNode node = this.getNode(approveOrder);
        if (node != null) {
            node.acceptPass(confirmEmpId);
        }
        return new AcceptSumbitRs(node.isFinished(), node);
    }

    private ConfirmNode getNode(Integer approveOrder) {
        Optional<ConfirmNode> any = nodes.stream().filter(node -> approveOrder.equals(node.getApprovalOrder())).findAny();
        return any.isPresent() ? any.get() : null;
    }

    public OperationLog createLog(boolean onlySkipNode, EmpId confirmEmpId, String scorerName, String signUrl) {
        OperationLog logDo = new OperationLog(companyId.getId(), taskUserId,
                OperationLogSceneEnum.AUDIT_ITEM.getScene(), confirmEmpId.getId(), new Date());
        if (onlySkipNode) {
            JSONObject logDesc = new JSONObject();
            logDesc.put("scorerId", confirmEmpId.getId());
            logDesc.put("scorerType", AuditEnum.CONFIRM_TASK.getScene());
            logDesc.put("scorerName", scorerName);
            logDo.setDescription(logDesc.toJSONString());
            logDo.setBusinessScene(OperationLogSceneEnum.SKIP_REVIEWER.getScene());
        }
        if (StrUtil.isNotBlank(signUrl)) {
            logDo.setDescription(signUrl);
        }
        return logDo;
    }

    public ConfirmDispatchNodeRs dispatchNodeFromItemAudit(String taskId, String empId, String opEmpId, ConfirmNode nextNode) {
        ConfirmDispatchNodeRs dispatchNodeRs = new ConfirmDispatchNodeRs();
        List<EvalScoreResult> ess = nextNode.dispatchItemAudit(companyId, taskId, taskUserId, empId, opEmpId);
        Set<String> scorerIds = ess.stream().map(BaseScoreResult::getScorerId).collect(Collectors.toSet());
        List<Rater> rrs = scorerIds.stream().map(Rater::new).collect(Collectors.toList());
        dispatchNodeRs.setCurNode(nextNode, rrs, ess);
        return dispatchNodeRs;
    }

    private ConfirmNode getNextNode(Integer approvalOrder) {
        ConfirmNode nextNode = getNode(approvalOrder + 1);
        if (nextNode == null) {
            return null;
        }
        if (nextNode.auditIndFlowIsNull()) {//配置了指标审批人节点，但是没有审批人，需跳过
            nextNode = getNode(nextNode.getApprovalOrder() + 1);
            return nextNode;
        }
        return nextNode;
    }

    public ConfirmDispatchNodeRs dispatchNextNode(String taskId, String empId, String opEmpId, Integer approvalOrder) {
        //试派发
        ConfirmNode nextNode = getNextNode(approvalOrder);
        if (nextNode == null) {
            return new ConfirmDispatchNodeRs(null, null, null);
        }
        //指标审核人节点，单独派发，从audit 中获取
        if (nextNode.havAuditIndFlow()) {
            return dispatchNodeFromItemAudit(taskId, empId, opEmpId, nextNode);
        }
        ConfirmDispatchNodeRs dispatchNodeRs = new ConfirmDispatchNodeRs();
        List<Rater> skipRaters = null;
        while (nextNode != null && CollUtil.isNotEmpty(skipRaters = nextNode.skipRater())) {
            List<EvalScoreResult> ess = nextNode.dispatch(companyId, taskId, taskUserId, empId, opEmpId);
            dispatchNodeRs.addSkipDispatchNodeRs(nextNode, skipRaters, ess);
            nextNode = getNextNode(++approvalOrder);
        }
        dispatchNodeRs.skipPassAll();
        if (nextNode == null) {
            return dispatchNodeRs;
        }
        List<EvalScoreResult> ess = nextNode.dispatch(companyId, taskId, taskUserId, empId, opEmpId);
        dispatchNodeRs.setCurNode(nextNode, nextNode.getRaters(), ess);
        return dispatchNodeRs;
    }

    public List<ConfirmNode> dispacthAndPassAllNode(boolean noEdit, String taskId, String empId, String opEmpId, Integer approvalOrder) {
        List<ConfirmNode> rs = new ArrayList<>();
        if (noEdit && noChangeSkipAll) {//未修改跳过后面所有结点
            ConfirmDispatchNodeRs dispatchNodeRs;
            while ((dispatchNodeRs = this.dispatchNextNode(taskId, empId, opEmpId, approvalOrder++)).isOk()) {
                dispatchNodeRs.skipPassAll();
                rs.add(dispatchNodeRs.getNode());
            }
        }
        return rs;
    }

    public boolean isEnd() {
        for (ConfirmNode node : nodes) {
            if (CollUtil.isNotEmpty(node.skipRater())) {
                continue;
            }
            if (node.auditIndFlowIsNull()) {//指标审核人节点为空节点，需要跳过进入下个节点层级
                continue;
            }
            if (!node.isFinished()) {
                return false;
            }
        }
        return true;
    }

    public Integer getApproveOrder(EmpId confirmEmpId) {
        if (confirmEmpId == null || confirmEmpId.getId() == null) {
            return null;
        }
        String confirmId = confirmEmpId.getId();
        for (ConfirmNode node : nodes) {
            if (node.isFinished()) {
                continue;
            }

            MapWrap<String, List<EvalScoreResult>> rsMap = node.getRsMap();
            if (rsMap != null && rsMap.containsKey(confirmId)) {
                return node.getApprovalOrder();
            }
        }
        return null;
    }

    public ConfirmNode reject2Up(String opEmpId) {
        ConfirmNode rejectNode = this.getRejectNoe(opEmpId);
        ConfirmNode upNode = getNode(rejectNode.getApprovalOrder() - 1);
        while (reject2UpNeedSkipNode(upNode)) {
            upNode = getNode(upNode.getApprovalOrder() - 1);
        }
        return upNode;
    }

    private boolean reject2UpNeedSkipNode(ConfirmNode upNode) {
        if (upNode != null && CollUtil.isNotEmpty(upNode.skipRater())) {
            return true;
        }
        return upNode != null && upNode.auditIndFlowIsNull();
    }
    public ConfirmNode getRejectNoe(String opEmpId) {
        for (ConfirmNode node : nodes) {
            MapWrap<String, List<EvalScoreResult>> rsMap = node.getRsMap();
            List<EvalScoreResult> result = rsMap.get(opEmpId);
            if (result != null && !node.isAllPassed(result)) {
                return node;
            }
        }
        return null;
    }


//    public AcceptSumbitRs acceptReject(boolean onlySkipNode, EmpId confirmEmpId, Integer approveOrder) {
//        AcceptSumbitRs rs = super.submitReject(onlySkipNode, confirmEmpId, approveOrder);
//        return rs;
//    }


}
