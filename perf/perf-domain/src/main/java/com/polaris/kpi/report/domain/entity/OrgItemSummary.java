package com.polaris.kpi.report.domain.entity;

import com.polaris.kpi.common.DelableDomain;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/12 15:23
 */
@Data
public class OrgItemSummary extends DelableDomain {

    private String id;
    private String companyId;
    private String orgId;
    private String atOrgCodePath;
    private String itemId;
    private String itemName;
    private String itemUnit;
    private String cycleId;
    private BigDecimal targetValueSum;
    private BigDecimal finishValueSum;
    private Integer finishValueType;
    private Integer performanceType;

    public void acceptOrgAndItemInfo(String orgId, ItemAnalysisItemInfo item) {
        this.orgId = orgId;
        this.atOrgCodePath = item.getAtOrgCodePath();
        this.itemId = item.getItemId();
        this.itemName = item.getItemName();
        this.itemUnit = item.getItemUnit();
        this.targetValueSum = item.getTargetValueSum();
        this.finishValueSum = item.getFinishValueSum();
        this.finishValueType = item.getFinishValueType();
    }

    public void initOnNew(String seq, String empId) {
        this.id = seq;
        this.createdUser = empId;
        this.updatedUser = empId;
        this.createdTime = new Date();
        this.updatedTime = new Date();
    }

    public void acc(Integer performanceType, String companyId, String cycleId) {
        this.performanceType = performanceType;
        this.companyId = companyId;
        this.cycleId = cycleId;
    }
}
