package com.polaris.kpi.migration.entity;

import lombok.Data;


/**
 * 导出数据统计信息
 */
@Data
public class MigrationExportStatistics {
    private int totalTables;
    private int processedTables;
    private int skippedTables;
    private int totalInsertStatements;
    private long startTime;
    private long endTime;
    private String ossPath;

    public long getDuration() {
        return endTime - startTime;
    }

    public String getSummary() {
        return String.format("总表数: %d, 处理表数: %d, 跳过表数: %d, 总INSERT语句: %d, 耗时: %dms",
                totalTables, processedTables, skippedTables, totalInsertStatements, getDuration());
    }
}

