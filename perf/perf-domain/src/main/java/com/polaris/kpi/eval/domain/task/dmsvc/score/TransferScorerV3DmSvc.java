package com.polaris.kpi.eval.domain.task.dmsvc.score;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.polaris.kpi.common.KpiI18NException;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTaskOperation;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.entity.empeval.EvalScorersWrap;
import com.polaris.kpi.eval.domain.task.entity.empeval.score.TransferScorer;
import com.polaris.kpi.eval.domain.task.entity.msg.MsgTodoAggregate;
import com.polaris.kpi.org.domain.dept.entity.CompanyMsgCenter;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 评分转交领域服务
 */
@Getter
@Slf4j
public class TransferScorerV3DmSvc {
    private final TenantId companyId;//companyId
    private final String fromEmpId;//转出者
    private final String toEmpId;//转入者
    private final String opEmpId;//操作者

    private final EvalUser evalUser;
    private final EmpEvalMerge evalRule;
    private final EvalScorersWrap scorersWrap;
    private final ListWrap<CompanyMsgCenter> msgCentersWrap;

    private List<CompanyMsgCenter> fromCenterMsgs = new ArrayList<>();
    private List<CompanyMsgCenter> toCenterMsgs = new ArrayList<>();
    private final ListWrap<KpiEmp> raterGroup;//员工信息【操作人,fromEmpId,toEmpId】

    private EmpEvalScorer fromScorer; //转出者
    private  Set<String> msgScenes;
    private  EmpEvalScorer receiver; //接受者


    private Set<String> fromWaitScoreTypes = new HashSet<>();
    private List<EmpEvalScorerNode> listFromWaitSubmitNode =  new ArrayList<>();

    private final List<EmpEvalScorerNode> needSubmits = new ArrayList<>(); //(需要提交的节点)接受者
    private OperationLog operationLog;
    private AdminTaskOperation adminTaskOperation;


    public TransferScorerV3DmSvc(TenantId companyId, String opEmpId, String fromEmpId, String toEmpId, EvalUser evalUser,
                          EmpEvalMerge evalRule, EvalScorersWrap scorersWrap, ListWrap<CompanyMsgCenter> msgCentersWrap, List<KpiEmp> raters) {
        this.companyId = companyId;
        this.fromEmpId = fromEmpId;
        this.toEmpId = toEmpId;
        this.opEmpId = opEmpId;
        this.evalUser = evalUser;
        this.evalRule = evalRule;
        this.scorersWrap = scorersWrap;
        this.msgCentersWrap = msgCentersWrap;
        this.raterGroup = new ListWrap<>(raters).asMap(KpiEmp::getEmpId);
        initialize();
    }

    private void initialize() {
        initFromScorer(fromEmpId, scorersWrap);
        initToScorer(toEmpId);
        if (fromScorer != null) {
            this.listFromWaitSubmitNode = fromScorer.listWaitSubmitScoreNode();
        }
    }

    private void initToScorer(String toEmpId){
        if (msgCentersWrap.isEmpty()) {
            return;
        }
        toCenterMsgs = msgCentersWrap.groupGet(toEmpId);
    }
    private void initFromScorer(String fromEmpId,EvalScorersWrap scorersWrap){
        if (Objects.isNull(scorersWrap) || StrUtil.isBlank(fromEmpId)) {
            return;
        }
        fromScorer = scorersWrap.getCurEvalScorer(fromEmpId);
        fromCenterMsgs = msgCentersWrap.groupGet(fromEmpId);
        if (Objects.isNull(fromScorer)){
            return;
        }
        fromWaitScoreTypes = fromScorer.listWaitSubmitScoreType();
    }

    public void acceptMsgScenes( Set<String> msgScenes){
        this.msgScenes = msgScenes;
    }

    public boolean isSelfNode() {
        return CollUtil.isNotEmpty(fromWaitScoreTypes) && fromWaitScoreTypes.contains(SubScoreNodeEnum.SELF_SCORE.getScene());
    }
    public boolean isNullWaitSubmit() {
        return CollUtil.isEmpty(fromWaitScoreTypes);
    }

    /**
     *  1. 找到转出者
     *  2. 找到所有待提交节点
     *  3. 找到/创建接收者
     *  4. 接收者接受转出者的节点
     *  5. 标记转出者的环节为已转交
     *  6. 更新责任人
     *  7. 记录日志
     * @param fromEmpId 转出者
     * @param toEmpId 转入者
     * @param opAdminType 管理员类型
     */
    public void doTransferScoreV3(String fromEmpId, String toEmpId,String opAdminType) {
        log.info("开始进行转交操作：fromEmpId:{}, toEmpId:{},opAdminType:{}",fromEmpId,toEmpId,opAdminType);
        //1.准备转出者信息
        prepareFromScorer(fromEmpId);
        // 2. 准备接收者信息
        prepareReceiver(toEmpId);
        // 3. 执行转交
        executeTransfer();
        // 4. 更新相关数据
        updateRelatedData(fromEmpId, toEmpId, opAdminType);
        log.info("转交操作完成：needSubmits:{}", JSONUtil.toJsonStr(needSubmits));

        //转交修改考核规则
        KpiEmp kpiEmp = this.getEmp(toEmpId);
        evalRule.transferScoringIfV3(fromWaitScoreTypes, fromEmpId, kpiEmp,evalRule.isCustom());
        evalRule.initIndexRaters();
    }

    private void executeTransfer() {
        List<EmpEvalScorerNode> waitSubmitScorerNodes = fromScorer.listToForTransfer();//找到所有待提交节点   //可能包含多个环节
        List<EmpEvalScorerNode> waitSysSubmitNodes = receiver.receiveNodesForTransferV3(waitSubmitScorerNodes);//接收者接收节点
        fromScorer.toForTransfer(receiver.getEmpId(), waitSubmitScorerNodes);
        needSubmits.addAll(waitSysSubmitNodes);
    }
    private void prepareFromScorer(String fromEmpId) {
        fromScorer = scorersWrap.getCurEvalScorer(fromEmpId);
        checkErrorScorer(fromScorer);
    }
    private void prepareReceiver(String toEmpId) {
        receiver = scorersWrap.getCurEvalScorer(toEmpId);
        if (Objects.isNull(receiver)) {
            createNewReceiver(toEmpId);
        }
    }
    private void createNewReceiver(String toEmpId) {
        KpiEmp kpiEmp = this.getEmp(toEmpId);
        if (Objects.isNull(kpiEmp)) {
            throw new KpiI18NException("kpi.error.emp.not.exist", "被转交的人不存在");
        }
        receiver = new EmpEvalScorer(companyId.getId(),this.opEmpId,fromScorer.getTaskId(),fromScorer.getEmpId(),fromScorer.getTaskUserId(),
                kpiEmp.getEmpId(),kpiEmp.getEmpName(),kpiEmp.getAvatar());
    }

    private void updateRelatedData(String fromEmpId, String toEmpId, String opAdminType) {
        upReviewJson(fromEmpId, toEmpId);
        addLog(fromEmpId, toEmpId, opAdminType);
    }

    public void upReviewJson(String fromEmpId,String toEmpId){
        this.evalUser.replReviewers(Collections.singletonList(this.getEmp(toEmpId)), fromEmpId);//修改责任人
    }
    public List<EmpEvalScorer> listNeedSaveEmpEvalScorer() {
        List<EmpEvalScorer> scorers = new ArrayList<>();
        scorers.add(fromScorer);
        scorers.add(receiver);
        return scorers;
    }

    public KpiEmp getEmp(String empId) {
        return raterGroup.mapGet(empId);
    }

    public EmpEvalScorerNode getFromScoreNodeOne() {
        return listFromWaitSubmitNode.stream().findFirst().orElse(null);
    }
    private void checkErrorScorer(EmpEvalScorer scorer) {
        if (Objects.isNull(scorer)) {
            throw new KpiI18NException("node.notScorer", "无转交记录");
        }
    }

    private void addLog(String fromEmpId,String toEmpId,String opAdminType) {
        //记录日志
        JSONObject logDesc = new JSONObject();
        logDesc.put("empName", this.getEmp(toEmpId).getEmpName());
        logDesc.put("scorerType", this.getFromWaitScoreTypes());
        this.operationLog = new OperationLog(companyId.getId(), evalUser.getId(), "transfer_task", fromEmpId, logDesc.toJSONString());

        //管理员操作日志
        AdminTaskOperation operation = new AdminTaskOperation( evalUser.getTaskId(), companyId.getId());
        KpiEmp kpiEmp = new KpiEmp(evalUser.getEmpId(), evalUser.getEmpName(), evalUser.getEmpName());
        AdminTaskOperation.TransferInfo transferInfo = new AdminTaskOperation.TransferInfo(kpiEmp, this.getEmp(fromEmpId), this.getEmp(toEmpId));
        operation.transfer(this.getEmp(this.opEmpId), opAdminType, evalUser.getTaskStatus(), transferInfo);
        this.adminTaskOperation = operation;
    }
}
