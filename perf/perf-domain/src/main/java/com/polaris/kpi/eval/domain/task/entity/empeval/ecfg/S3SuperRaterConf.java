package com.polaris.kpi.eval.domain.task.entity.empeval.ecfg;

import cn.com.polaris.kpi.eval.Pecent;
import cn.com.polaris.kpi.eval.Rater;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.perf.www.common.constant.BusinessConstant;
import com.polaris.kpi.eval.domain.task.entity.EvalAudit;
import com.polaris.kpi.eval.domain.temp.entity.PerfTemplEvaluateAudit;
import com.polaris.sdk.type.AuditEnum;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.domain.task.entity.empeval.ecfg
 * @Author: lufei
 * @CreateTime: 2022-09-03  09:39
 * @Description: TODO
 * @Version: 1.0    at com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.S3SuperRaterConf.lambda$getAuditNode$2(S3SuperRaterConf.java:101)
 * //private String superiorScoreViewRule;//上级评人查看评分规则JSON
 * //private BigDecimal superiorScoreWeight;//上级评权重
 * //private String superiorScoreRule;//上级评分规则（打总分=total /指标打分=item）
 * //private String superiorScoreVacancy;//上级评分人空缺时规则
 * //private String superiorScoreOrder;//上级评顺序类型（同时/依次）
 */
@Getter
@Setter
public class S3SuperRaterConf extends S3RaterBaseConf {
    public static final String SAME_TIME = "sameTime";//评分人打分顺序-同时
    public static final String IN_TURN = "inTurn";//评分人打分顺序-依次
    private String superiorScoreOrder = SAME_TIME;  //上级评顺序（同时=sameTime|依次=inTurn）
    private Integer nodeVacancyFlag = null;  //是否处理直属主管空缺 null ==处理， (1-提示异常，需手动调整，2-系统自动跳过)

    public S3SuperRaterConf() {
    }

    public S3SuperRaterConf(String opeFlag, String rateMode, BigDecimal nodeWeight,
                            boolean anonymous, String superiorScoreOrder) {
        super(opeFlag, rateMode, nodeWeight, anonymous);
        this.superiorScoreOrder = superiorScoreOrder;
    }

    @Override
    public String scene() {
        return AuditEnum.SUPERIOR_SCORE.getScene();
    }
    @Override
    public S3SuperRaterConf clone() {
        return JSONUtil.toBean(JSONUtil.toJsonStr(this),S3SuperRaterConf.class);
    }

    @Override
    public void checkRequest() {
        if (!isOpen()) {
            return;
        }
        for (BaseAuditNode auditNode : auditNodes) {
            auditNode.setModifyFlag("false");
            auditNode.checkRequest();
            Assert.notNull(auditNode.getWeight(), "上级评第{}位的权重weight不能空", auditNode.getApprovalOrder());
            List<Rater> raters = auditNode.getRaters();
            if (CollUtil.isEmpty(raters)) {
                continue;
            }
            if (auditNode.isAndMode()) {
                Pecent avgPect = new Pecent(Pecent.ONE_HUNDRED, raters.size());
                raters.forEach(rater -> rater.setWeight(avgPect.getAvgWeight()));
                //权重除不尽的差值加到第一位上
                raters.get(0).setWeight(avgPect.getAvgAddDiff());
            } else {
                raters.forEach(rater -> rater.setWeight(Pecent.ONE_HUNDRED));
            }
        }
        super.checkRequest();
        Assert.notNull(nodeWeight, "上级评环节权重nodeWeight不能空");
    }

    public String superNodeName() {
        StringBuilder sb = new StringBuilder();
        for (Rater rater : allRater()) {
            if (rater.getWeight() != null) {
                sb.append(String.format("%s（%s％）", rater.getEmpName(), rater.getWeight()));
            } else {
                sb.append(StrUtil.format("{} ", rater.getEmpName()));
            }
        }
        if (isOpen() && CollUtil.isEmpty(allRater())) {
            sb.append("空缺");
        }
        return sb.toString();
    }

    public boolean superiorInSame() {
        return isOpen() && "sameTime".equals(this.superiorScoreOrder);
    }

    public String superiorInSameName() {
        if (Objects.equals(this.superiorScoreOrder, SAME_TIME)) {
            return "同时";
        }
        return "依次";
    }

    public boolean superiorInTurn() {
        return isOpen() && "inTurn".equals(this.superiorScoreOrder);
    }

    public boolean superInTurn () {
        return "inTurn".equals(this.superiorScoreOrder);
    }

    public List<BaseAuditNode> getAuditNode(int order) {
//        if (order > auditNodes.size()) {
//            return null;
//        }
        return auditNodes.stream().filter(baseAuditNode -> baseAuditNode.getApprovalOrder() == order).collect(Collectors.toList());
    }

    public List<Rater> allRater(int order) {
        List<Rater> raters = new ArrayList<>();
        if (open == 0 || CollUtil.isEmpty(auditNodes)) {
            return raters;
        }
        for (BaseAuditNode auditNode : auditNodes) {
            if (auditNode.getApprovalOrder().equals(order)) {
                auditNode.avgRaterWeight();
                raters.addAll(auditNode.getRaters());
            }
        }
        return raters;
    }

    //展开具体的评分人员
    public void explainRater(String empId, TenantId companyId, EmpId opEmpId, String evalOrgId, String orgId, Integer nodeVacancyFlag) {
        for (BaseAuditNode an : getAuditNodes()) {
            EvalAudit anSperNode = new EvalAudit(an.getApproverType(), an.getApproverInfo(), companyId, new EmpId(empId), opEmpId, evalOrgId, orgId, nodeVacancyFlag);
            an.setRaters(anSperNode.genAuditRaters());
        }
        auditNodes = PerfTemplEvaluateAudit.doRepeat(getAuditNodes(), nodeVacancyFlag);
    }
}
