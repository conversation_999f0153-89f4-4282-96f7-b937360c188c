//package com.polaris.kpi.eval.domain.confirm.dmsvc;
//
//import cn.hutool.core.bean.BeanUtil;
//import com.perf.www.common.constant.BusinessConstant;
//import com.perf.www.common.em.CompanyItemTypeEnum;
//import com.polaris.kpi.eval.domain.confirm.entity.BindOkrType;
//import com.polaris.kpi.eval.domain.task.entity.*;
//import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
//import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalKpiType;
//import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
//import com.polaris.kpi.eval.domain.task.entity.empeval.KpiListWrap;
//import com.polaris.sdk.type.EmpId;
//import com.polaris.sdk.type.ListWrap;
//
//import java.math.BigDecimal;
//import java.util.List;
//import java.util.UUID;
//
///**
// * @BelongsProject: perf-server
// * @BelongsPackage: com.polaris.kpi.eval.domain.task.dmsvc
// * @Author: lufei
// * @CreateTime: 2025-08-26  11:39
// * @Version: 1.0
// */
//public class OkrKpiTypeDmSvc {
//    private String opEmpId;
//    private EvalUser user;
//    private EmpEvalMerge evalMerge;
//    private KpiListWrap kpiTypes;//绩效系统中的维度
//    private List<BindOkrType> bindOkrTypes;//OKR系统中的关键任务
//
//    public OkrKpiTypeDmSvc(String opEmpId, EvalUser user, EmpEvalMerge evalMerge, KpiListWrap kpiTypes, List<BindOkrType> bindOkrTypes) {
//        this.opEmpId = opEmpId;
//        this.user = user;
//        this.evalMerge = evalMerge;
//        this.kpiTypes = kpiTypes;
//        this.bindOkrTypes = bindOkrTypes;
//    }
//
//    public void buildOkrKpis() {
//        for (BindOkrType typeAtOkr : this.bindOkrTypes) {
//            EmpEvalKpiType kpiType = kpiTypes.mapGet(typeAtOkr.getKpiTypeId());
//            int order = 1;
//            for (EvalRefOkr evalRefOkr : typeAtOkr.getKrList()) {
//                evalRefOkr.initEvalRefOkr(kpiType.getCompanyId().getId(), user.getTaskId(), user.getId(), user.getEmpId(), opEmpId, kpiType.getKpiTypeId());
//                if (!Boolean.TRUE.toString().equals(evalRefOkr.getIsDeleted())) {
//                    EvalKpi kpiItem = new EvalKpi();
//                    kpiItem.initBaseInfo(user.getCompanyId(), new EmpId(opEmpId), user.getId(), kpiType.getKpiTypeId());
//                    kpiItem.initBaseInfo2(user.getTaskId(), user.getEmpId(), user.getId());
//                    evalRefOkr.setTaskKpiId(kpiItem.getKpiItemId());//注意引用是记录id,不是kpiItemId kpiBiz.293行
//
//                    //兼容字段，不推荐使用，以后类别的字段去列表对应的表找，不要在指标里去找
//                    kpiItem.setKpiTypeName(kpiType.getKpiTypeName());
//                    kpiItem.setOpenOkrScore(kpiType.getOpenOkrScore());
//                    kpiItem.setKpiTypeWeight(kpiType.getKpiTypeWeight());
//                    kpiItem.setKpiTypeClassify(kpiType.getKpiTypeClassify());
//                    kpiItem.setItemWeight(evalRefOkr.getItemWeight());
//                    kpiItem.setKpiItemName(evalRefOkr.getActionName());
//                    kpiItem.setItemTargetValue(BigDecimal.ZERO);
//                    kpiItem.setItemUnit(evalRefOkr.getUnit());
//                    kpiItem.setResultInputType(BusinessConstant.NO);
//                    kpiItem.setScorerType(BusinessConstant.EXAM);
//                    kpiItem.setItemType(CompanyItemTypeEnum.NON_MEASURABLE.getType());
//                    kpiItem.setOrder(order++);
//                    kpiItem.setIsOkr(BusinessConstant.TRUE);
//                    kpiItem.setCreatedUser(opEmpId);
//                    kpiItem.setOkrScore(evalRefOkr.getOkrScore());
//
//                    kpiItem.setRefOkr(evalRefOkr);
//
//
//                    kpiItem.computeFinalOkrScore(evalMerge.getScoreValueConf().isFullScoreRange(), evalMerge.isTypeWeightOpen());
//                    if (kpiType.getTypeRule() != null && kpiType.getTypeRule().hasScoreRule()) {//自定
//                        EvalItemScoreRule targetRule = new EvalItemScoreRule();
//                        BeanUtil.copyProperties(kpiType.getTypeRule(), targetRule, true);
//                        kpiItem.customItemScoreRule(targetRule);
//                    }
//                    kpiType.getItems().add(kpiItem);
//                }
//            }
//        }
//    }
//}
