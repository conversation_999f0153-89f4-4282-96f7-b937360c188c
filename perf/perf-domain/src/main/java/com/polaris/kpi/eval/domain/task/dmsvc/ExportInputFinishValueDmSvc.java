package com.polaris.kpi.eval.domain.task.dmsvc;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.polaris.kpi.eval.domain.task.entity.EvalKpi;
import com.polaris.kpi.eval.domain.task.entity.EvalKpiInputValue;
import com.polaris.kpi.eval.domain.task.entity.ExportInputValue;
import com.polaris.sdk.type.ListWrap;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 导出录入完成值领域服务
 */
@Service
@Slf4j
public class ExportInputFinishValueDmSvc {

   private Integer performanceType;
   private String sheetName;
   private List<EvalKpiInputValue> headItems = new ArrayList<>();
   private List<ExportInputValue> dataList;
   private String title = "说明：\n" +
           "1、按指标批量导入\n" +
           "2、请按指标填写完成值和相应的更新说明\n" +
           "3、请勿修改表格其他内容，以免导致录入错误或录入失败；\n" +
           "4、您可以整行删除不需要录入的被考核人,整列删除不需要录入完成值的指标。";

   public void accOp(Integer performanceType, String sheetName, List<ExportInputValue> dataList) {
      this.performanceType = performanceType;
      this.sheetName = sheetName;
      this.dataList = dataList;
   }

   public void exportExcel(ByteArrayOutputStream outputStream) {
      if (CollUtil.isEmpty(this.dataList)) {
         return;
      }
      // 5. 写入数据
      EasyExcel.write(outputStream)
              .head(getHeads())
              .registerWriteHandler(new HorizontalCellStyleStrategy(getHeadStyleCellStyle(), getContentStyleCellStyle()))
              .registerWriteHandler(new ConditionStyleHandler())
              .registerWriteHandler(new FirstRowStyleHandler())
              .sheet(this.sheetName)
              .doWrite(generateDataRows());
   }

   /**
    * 表头样式
    * @return
    */
   private static WriteCellStyle getHeadStyleCellStyle() {
      WriteCellStyle headStyle = new WriteCellStyle();
      headStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
      headStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
      headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER); // 水平居中
      headStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
      WriteFont headFont = new WriteFont();
      headFont.setFontHeightInPoints((short) 12);
      headStyle.setWriteFont(headFont);
      return headStyle;
   }

   /**
    * 数据行样式
    * @return
    */
   private static WriteCellStyle getContentStyleCellStyle() {
      // 数据行居中
      WriteCellStyle contentStyle = new WriteCellStyle();
      contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER); // 水平居中
      contentStyle.setLocked(false);
      return contentStyle;
   }

   private List<List<String>> generateDataRows() {
      List<List<String>> rows = new ArrayList<>();
      if (CollUtil.isEmpty(this.dataList)) {
         return rows;
      }
      this.dataList.forEach(data -> {
         List<String> datas = new ArrayList<>();
         datas.add(data.getTaskUserId());
         datas.add(data.getDingUserId());
         if (Objects.equals(this.performanceType, 1)) {
            datas.add(data.getEmpName());
            datas.add(data.getJobnumber());
            datas.add(data.getEmpOrgName());
            datas.add(data.getOrgName1());
            datas.add(data.getOrgName2());
            datas.add(data.getOrgName3());
         } else {
            datas.add(data.getEvalOrgName());
         }
         datas.add(data.getTaskName());

         for (EvalKpiInputValue headItem : headItems) {
            Map<String, EvalKpiInputValue> putMap = new HashMap<>();
            if (CollUtil.isNotEmpty(data.getItems())) {
               for (EvalKpiInputValue itemPo : data.getItems()) {
                  putMap.put(itemPo.getKpiItemId() + "-" + itemPo.getKpiItemName(), itemPo);
               }
            }
            if (MapUtil.isNotEmpty(putMap) && putMap.containsKey(headItem.getKpiItemId() + "-" + headItem.getKpiItemName())) {
                EvalKpiInputValue kpi = putMap.get(headItem.getKpiItemId() + "-" + headItem.getKpiItemName());
               String finishValue = "";
               String finishValueComment = "";
               if (Objects.nonNull(kpi)) {
                  finishValue = kpi.getFinishVal();
                  finishValueComment = kpi.getFinishValueComment();
               }
               datas.add(finishValue);
               datas.add(finishValueComment);
            } else {
               datas.add("-");
               datas.add("-");
            }
         }
         rows.add(datas);
      });
      return rows;
   }


   private List<List<String>> getHeads() {
      List<List<String>> head = new ArrayList<>();
      head.add(createHeader(title,"taskUserId"));
      head.add(createHeader(title,"userId"));
      if (Objects.equals(this.performanceType, 1)) {
         head.add(createHeader(title,"姓名"));
         head.add(createHeader(title,"工号"));
         head.add(createHeader(title,"所属部门"));
         head.add(createHeader(title,"一级部门"));
         head.add(createHeader(title,"二级部门"));
         head.add(createHeader(title,"三级部门"));
      } else {
         head.add(createHeader(title,"考核部门"));
      }
      head.add(createHeader(title,"考核任务"));
      //动态列
      List<EvalKpiInputValue> items = this.dataList.stream().flatMap(data -> data.getItems().stream())
              .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getKpiItemId() + ";" + o.getKpiItemName()))), ArrayList::new));
      for (EvalKpiInputValue item : items) {
         head.add(createHeader(title,item.getItemIdJoinName() , "完成值"));
         head.add(createHeader(title,item.getItemIdJoinName(), "备注"));
      }
      this.headItems = items;
      return head;
   }


   private static List<String> createHeader(String... headNames) {
      List<String> head = new ArrayList<>();
      for (String name : headNames) {
         head.add(name);
      }
      return head;
   }

   // 条件样式处理
   static class ConditionStyleHandler implements CellWriteHandler {

      @Override
      public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {
      }

      @Override
      public void afterCellCreate(WriteSheetHolder writeSheetHolder,
                                  WriteTableHolder writeTableHolder, Cell cell,
                                  Head head, Integer relativeRowIndex, Boolean isHead) {
      }

      @Override
      public void afterCellDataConverted(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, CellData cellData, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {

      }

      @Override
      public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, List<CellData> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
         // 假设完成值在动态列的第1列（索引从0开始）
         int completionValueColumnIndex = 9; // 第一动态列的完成值位置（第4列）
         if (cell.getColumnIndex() >= completionValueColumnIndex) {
            if (Objects.equals(cell.getStringCellValue(), "-")) {
               setGrayStyle(cell, writeSheetHolder);
            }
         } else if (cell.getColumnIndex() == 0) {
            setGrayStyle(cell, writeSheetHolder);
         }
      }

      private void setGrayStyle(Cell cell, WriteSheetHolder holder) {
         Workbook workbook = holder.getSheet().getWorkbook();
         CellStyle style = workbook.createCellStyle();
         style.cloneStyleFrom(cell.getCellStyle());
         style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
         style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
         style.setVerticalAlignment(VerticalAlignment.CENTER);
         style.setLocked(true);
         cell.setCellStyle(style);
         holder.getSheet().protectSheet("your_password");
      }
   }

   static class FirstRowStyleHandler implements CellWriteHandler {
      private CellStyle firstRowStyle;
      @Override
      public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {
      }

      @Override
      public void afterCellCreate(WriteSheetHolder writeSheetHolder,
                                  WriteTableHolder writeTableHolder, Cell cell,
                                  Head head, Integer relativeRowIndex, Boolean isHead) {
      }

      @Override
      public void afterCellDataConverted(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, CellData cellData, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {

      }

      @Override
      public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, List<CellData> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
         if (cell.getRowIndex() == 0 ) {
            Row row = cell.getRow();
            firstRowStyle = getFirstRowStyle(writeSheetHolder,firstRowStyle);
            cell.setCellStyle(firstRowStyle);
            // 3. 设置行高（仅在第一个单元格处理时执行）
            if (cell.getColumnIndex() == 0) {
               // 方式一：固定行高（例如 30 点）
               row.setHeightInPoints(100);
            }
         }
      }

      /**
       * 顶部文本样式
       * @return
       */
      private static CellStyle getFirstRowStyle(WriteSheetHolder writeSheetHolder,CellStyle firstRowStyle) {
         if (Objects.isNull(firstRowStyle)) {
            firstRowStyle = writeSheetHolder.getSheet().getWorkbook().createCellStyle();
            // 水平靠左对齐
            firstRowStyle.setAlignment(HorizontalAlignment.LEFT);
            // 垂直顶部对齐（取消默认居中）
            firstRowStyle.setVerticalAlignment(VerticalAlignment.TOP);
            // 自动换行（可选，支持多行内容）
            firstRowStyle.setWrapText(true);
         }
         return firstRowStyle;
      }
   }

}