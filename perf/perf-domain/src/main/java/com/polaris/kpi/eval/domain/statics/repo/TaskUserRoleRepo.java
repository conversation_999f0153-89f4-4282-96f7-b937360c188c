//package com.polaris.kpi.eval.domain.statics.repo;
//
//import com.polaris.kpi.eval.domain.statics.entity.TaskUserRole;
//import com.polaris.kpi.eval.domain.task.entity.EvalUser;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @date 2025/2/10 13:35
// */
//public interface TaskUserRoleRepo {
//
//    List<TaskUserRole> buildTaskUserRoles(EvalUser taskUser);
//
//    void addOrUpdateTaskUserRole(List<TaskUserRole> taskUserRoles);
//
//    void deleteByTaskUserId(String id, String id1);
//
//}
