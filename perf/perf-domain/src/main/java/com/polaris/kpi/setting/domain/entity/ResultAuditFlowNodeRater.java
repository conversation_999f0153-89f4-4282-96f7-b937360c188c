package com.polaris.kpi.setting.domain.entity;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.common.DelableDomain;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.*;
import java.util.stream.Collectors;

@Setter
@Getter
@NoArgsConstructor
public class ResultAuditFlowNodeRater extends DelableDomain {

    private String id;
    private TenantId companyId;
    private String flowInstanceId;
    private String flowNodeId;
    private String taskUserId;
    private String auditEmpId;
    private Integer status;   // 审批状态(0:未开始  1:执行中 2:已完成 4:跳过完成)
    private Integer skipType;

    public void setSkipType(Integer skipType) {
        this.skipType = skipType;
    }

    private Integer level;
    private List<ResultAuditFlowUser> flowUsers = new ArrayList<>();  //要审核的员工
    private String multipleReviewersType;
    private String empId;
    private String taskStatus;

    public ResultAuditFlowNodeRater(String companyId, String auditEmpId) {
        this.companyId = new TenantId(companyId);
        this.auditEmpId = auditEmpId;
    }

    public ResultAuditFlowNodeRater(String id,String companyId, String flowInstanceId, String flowNodeId,String taskUserId,
                                      String auditEmpId, Integer status,String createdUser,Integer skipType,Integer level) {
        this.id = id;
        this.companyId = new TenantId(companyId);
        this.flowInstanceId = flowInstanceId;
        this.flowNodeId = flowNodeId;
        this.taskUserId = taskUserId;
        this.auditEmpId = auditEmpId;
        this.skipType = skipType;
        this.status = Objects.nonNull(skipType) ? 4 : status;
        this.level = level;
        this.createdUser = createdUser;
        this.createdTime = new Date();
    }
    public void accop(String companyId, String flowInstanceId, String flowNodeId,String taskUserId, String auditEmpId, String opEmpId) {
        this.companyId = new TenantId(companyId);
        this.flowInstanceId = flowInstanceId;
        this.flowNodeId = flowNodeId;
        this.taskUserId = taskUserId;
        this.auditEmpId = auditEmpId;
        this.createdUser = opEmpId;
        this.createdTime = new Date();
        this.status = 0;
    }
    //fix
    public void changeStatus(List<EvalScoreResult> curLevelRs,String opEmpId, boolean isAudit,Integer approvalOrder) {
        if (CollUtil.isEmpty(curLevelRs)) {
            return;
        }
        curLevelRs = curLevelRs.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                new TreeSet<>(Comparator.comparing(o -> o.getTaskId() + ";" + o.getEmpId() + ";" + o.getTaskUserId() + ";" + o.getScorerId() + ";" + o.getApprovalOrder()))), ArrayList::new));

        Map<String, EvalScoreResult> auditMap = CollStreamUtil.toIdentityMap(curLevelRs, e -> e.getTaskUserId() + "-" + e.getScorerId());
        for (ResultAuditFlowUser flowUser : flowUsers) {
            EvalScoreResult result = auditMap.get(flowUser.getTaskUserId() + "-" + this.auditEmpId);
            flowUser.upStatus(result);
        }
        List<Integer> status = CollUtil.map(this.flowUsers, u -> u.getStatus(), true).stream().distinct().collect(Collectors.toList());
        if (Objects.equals(this.status, 2)) {
            return;
        }
        if (status.size() == 1) {
            this.status = status.get(0);
        }
    }
    //fix
    public void refreshStatus() {
        if (CollUtil.isEmpty(this.flowUsers)) {
            return;
        }
        List<Integer> status = CollUtil.map(this.flowUsers, u -> u.getStatus(), true).stream().distinct().collect(Collectors.toList());
        if (Objects.equals(this.status, 2)) {
            return;
        }
        if (status.size() == 1) {
            this.status = status.get(0);
        }
    }

    //进入校准环节或层级
    @JSONField(serialize = false)
    public boolean isEnter() {
        return Objects.equals(this.status, 1);
    }

    @JSONField(serialize = false)
    public boolean isFinish() {
        return this.status >= 2;
    }

    //未开始
    @JSONField(serialize = false)
    public boolean isNotEnter() {
        return Objects.equals(this.status, 0);
    }

    //未开始
    @JSONField(serialize = false)
    public boolean isSkip() {
        return Objects.nonNull(this.skipType);
    }

    //需要跳过
    @JSONField(serialize = false)
    public boolean needSkip() {
        return Objects.nonNull(this.skipType);
    }

    public void mark(String adminEmpId,Integer level) {
        if (Objects.equals(this.auditEmpId,adminEmpId) && Objects.equals(this.level,level)) {
            this.status = 2;
        }
    }
}
