package com.polaris.kpi.extData.domain.entity;

import com.polaris.kpi.common.DelableDomain;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;
import org.apache.ibatis.annotations.Ckey;

import java.io.Serializable;

/**
 * @Author: xuxw
 * @Date: 2025/04/07 11:57
 * @Description:
 */
@Setter
@Getter
@NoArgsConstructor
public class ExtDataItemFieldCorr extends DelableDomain implements Serializable {
    @Ckey
    private String id;
    private String companyId;
    private String kpiItemId;
    private String kpiItemFieldId;
    private String extDataSysId;
    private String extDataClsId;
    private String extDataFieldId;
    private String extDataFieldParamId;
    @NonNull
    private String extDataFieldParamEnName;
}
