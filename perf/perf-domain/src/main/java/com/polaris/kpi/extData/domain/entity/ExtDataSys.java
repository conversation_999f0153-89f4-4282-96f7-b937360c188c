package com.polaris.kpi.extData.domain.entity;

import cn.hutool.core.date.DateUtil;
import com.polaris.kpi.extData.domain.type.TokenAnalysis;
import com.polaris.kpi.common.DelableDomain;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.annotations.Ckey;

import java.util.Date;
import java.util.List;

/**
 * @Author: xuxw
 * @Date: 2025/03/06 10:52
 * @Description:
 */
@Setter
@Getter
public class ExtDataSys extends DelableDomain {
    @Ckey
    private String id;
    private String extSysName;
    private String extSysDesc;
    private Integer extSysType;
    private String token;
    private String tokenAcquireMethod;
    private Integer tokenTimeout;
    private Date tokenTimeoutTime;
    private String companyId;
    private Integer isOpen;
    private Integer hasField = 0;
    private List<ExtDataSysConf> sysConfList;

    public Boolean tokenWasTimeout(){
        return new Date().after(tokenTimeoutTime);
    }

    public String  updateToken(){
        TokenAnalysis tokenAnalysis = new TokenAnalysis(sysConfList, this.tokenAcquireMethod);
        this.token = tokenAnalysis.analysisToken();
        this.tokenTimeoutTime = DateUtil.offsetSecond(new Date(), this.tokenTimeout);
        return this.token;
    }
}
