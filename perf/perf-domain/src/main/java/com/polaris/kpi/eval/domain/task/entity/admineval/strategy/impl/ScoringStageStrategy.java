package com.polaris.kpi.eval.domain.task.entity.admineval.strategy.impl;

import cn.hutool.core.collection.CollUtil;
import com.polaris.kpi.eval.domain.task.entity.admineval.EmpEvalChangeResult;
import com.polaris.kpi.eval.domain.task.entity.admineval.strategy.EvalStageChangeContext;
import com.polaris.kpi.eval.domain.task.entity.admineval.strategy.EvalStageChangeStrategy;
import com.polaris.kpi.eval.domain.task.entity.admineval.strategy.EvalStageConfig;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalKpiType;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import cn.com.polaris.kpi.eval.ChangeInfo;
import cn.com.polaris.kpi.eval.Rater;
import com.polaris.kpi.eval.domain.task.entity.empeval.ChangeStage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 评分阶段处理策略
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Component
@Slf4j
public class ScoringStageStrategy implements EvalStageChangeStrategy {
    
    @Override
    public boolean processStageChange(EvalStageChangeContext context) {
        
        // 检查是否在评分阶段之后
        if (!context.isAfterStage(getStageType())) {
            log.debug("跳过评分阶段处理: empId={}, 未到评分阶段之后", context.getEmpId());
            return false;
        }
        
        log.debug("开始处理评分阶段变更: empId={}", context.getEmpId());
        
        // 解析评分变更
        parseScoreChangeRaters(context);
        
        log.debug("完成评分阶段变更处理: empId={}", context.getEmpId());
        return true;
    }
    
    /**
     * 解析评分变更
     * 
     * @param context 处理上下文
     */
    private void parseScoreChangeRaters(EvalStageChangeContext context) {
        EmpEvalChangeResult result = context.getChangeResult();
        Set<ChangeInfo> infos = new HashSet<>();
        
        // 解析评分人
        if (!result.isCustom()) {
            // 非自定义模式：解析标准评分人
            context.getLoader().parseMutualNodeConfChangeRaters(
                context.getEmpId(), context.getEvalOrgId(), context.getOrgId(), 
                infos, result.getS3PeerRater()
            );
            
            context.getLoader().parseMutualNodeConfChangeRaters(
                context.getEmpId(), context.getEvalOrgId(), context.getOrgId(), 
                infos, result.getS3SubRater()
            );
            
            context.getLoader().skipRepeatChangeSuperScore(
                context.getEmpId(), context.getEvalOrgId(), context.getOrgId(), 
                infos, result.getS3SuperRater()
            );
            
            result.closeTypeRater();
        } else {

            // 自定义模式：解析默认流程评分人
            context.getLoader().parseMutualNodeConfChangeRaters(
                    context.getEmpId(), context.getEvalOrgId(), context.getOrgId(),
                    infos, result.getS3PeerRater()
            );

            context.getLoader().parseMutualNodeConfChangeRaters(
                    context.getEmpId(), context.getEvalOrgId(), context.getOrgId(),
                    infos, result.getS3SubRater()
            );

            context.getLoader().skipRepeatChangeSuperScore(
                    context.getEmpId(), context.getEvalOrgId(), context.getOrgId(),
                    infos, result.getS3SuperRater()
            );

            // 自定义模式：解析KPI类型评分人
            for (EmpEvalKpiType kpiType : result.getKpiTypes().getDatas()) {
                context.getLoader().parseScoreChangeRater(context.getEmpId(), context.getEvalOrgId(), context.getOrgId(), infos, kpiType);
            }
        }
        
        // 解析打总等级评分人
        parseTotalLevelRaters(context, infos);
        
        // 初始化索引评分人
        result.initIndexRaters(context.getLoader().getCompanyId(), result.getTaskUserId());
        
        // 设置变更标识
        if (CollUtil.isNotEmpty(infos)) {
            result.getScoreChanges().add(new ChangeStage("scoring", infos));
            result.setChangeStageType(result.getChangeStageType() | getChangeFlag());
            log.debug("评分人员变更成功: empId={}, changeCount={}", context.getEmpId(), infos.size());
        }
    }
    
    /**
     * 解析打总等级评分人
     * 
     * @param context 处理上下文
     * @param infos 变更信息集合
     */
    private void parseTotalLevelRaters(EvalStageChangeContext context, Set<ChangeInfo> infos) {
        EmpEvalChangeResult result = context.getChangeResult();
        
        if (CollUtil.isEmpty(result.getTotalLevelRaters())) {
            return;
        }
        
        List<Rater> raters = new ArrayList<>();
        
        for (Rater rater : result.getTotalLevelRaters()) {
            if (!rater.needParse()) {
                raters.addAll(result.getTotalLevelRaters());
                return;
            }
            
            String approverType = rater.isManager() ? "manager" : (rater.isRole() ? "role" : null);
            String approverInfo = rater.isManager() ? rater.getLevel().toString() : 
                                 (rater.isRole() ? rater.getRoleId() : null);
            
            raters.addAll(context.getLoader().parseBaseChangeRaters(
                context.getEmpId(), context.getEvalOrgId(), context.getOrgId(), infos,
                result.getTotalLevelRaters(), approverType, approverInfo
            ));
        }
        
        result.setTotalLevelRaters(raters);
    }
    
    @Override
    public TalentStatus getStageType() {
        return EvalStageConfig.SCORING.getStageType();
    }
    
    @Override
    public Integer getChangeFlag() {
        return EvalStageConfig.SCORING.getChangeFlag();
    }
    
    @Override
    public String getDescription() {
        return EvalStageConfig.SCORING.getDescription();
    }
    
    @Override
    public int getPriority() {
        return EvalStageConfig.SCORING.getPriority();
    }
}
