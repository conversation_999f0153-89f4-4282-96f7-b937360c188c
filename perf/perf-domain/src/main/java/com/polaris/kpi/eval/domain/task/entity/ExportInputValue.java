package com.polaris.kpi.eval.domain.task.entity;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@NoArgsConstructor
public class ExportInputValue {
    private String taskUserId;
    private String dingUserId;
    private String empName;
    private String jobnumber;
    private String empOrgName;
    private String orgName1;
    private String orgName2;
    private String orgName3;
    private String taskName;
    private String companyId;
    private String taskId;
    private String cycleId;
    private String empId;
    private String avatar;
    private String orgId;
    private String atOrgNamePath;
    private String evalOrgId;
    private String evalOrgName;
    private String canInputItemKeys;            //可录入的指标主键
    private List<EvalKpiInputValue> items;


    public void repaceOrgNamePath() {
        if (StrUtil.isEmpty(this.atOrgNamePath)) {
            return;
        }
        List<String> orgNames = StrUtil.splitTrim(this.atOrgNamePath, "|");
        //排除根部门
        orgNames.remove(0);
        if (CollUtil.isEmpty(orgNames)) {
            return;
        }
        this.orgName1 = orgNames.size() > 0 ? orgNames.get(0) : "";
        this.orgName2 = orgNames.size() > 1 ? orgNames.get(1) : "";
        this.orgName3 = orgNames.size() > 2 ? orgNames.get(2) : "";
    }
}
