package com.polaris.kpi.notice.domain.entity;

import com.polaris.kpi.common.DelableDomain;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Ckey;


/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.notice.ppojo
 * @Author: lufei
 * @CreateTime: 2022-06-23  16:52
 * @Description: TODO
 * @Version: 1.0
 * {
 * "id":"格式化的key",
 * "companyId":"公司id0",
 * "createdTime":"",
 * "updatedTime":"",
 * "version":"版本号",
 * }
 ***/
@Getter
@NoArgsConstructor
public class Tip extends DelableDomain {
    @Ckey
    private String id;//格式化的key
    @Ckey
    private String companyId;//公司id0

    public Tip(String id, String companyId) {
        this.id = id;
        this.companyId = companyId;
    }
}
