package com.polaris.kpi.eval.domain.task.entity.empeval;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.eval.Rater;
import cn.com.polaris.kpi.eval.RaterNode;
import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.perf.www.common.constant.BusinessConstant;
import com.polaris.kpi.common.DelableDomain;
import com.polaris.kpi.eval.domain.task.entity.BaseTypeScoreResult;
import com.polaris.kpi.eval.domain.task.entity.EvalItemScoreRule;
import com.polaris.kpi.eval.domain.task.entity.EvalKpi;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.chain.ScoreSceneWrap;
import com.polaris.kpi.eval.domain.task.entity.flow.AsDisplayFlowRs;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.domain.task.entity.empeval
 * @Author: lufei
 * @CreateTime: 2023-07-11  13:54
 * @Version: 1.0
 */
@Getter
@Setter
public class BaseEvalFlow extends DelableDomain {
    //维度下的评分流程
    protected RaterNodeConf selfRater;              //自评人
    protected MutualNodeConf peerRater;              //360或简易 同级互评人
    protected MutualNodeConf subRater;               //360下级
    protected S3SuperRaterConf superRater;          //上级人
    protected S3RaterBaseConf appointRater;         //指定评分人

    public void buildNodes(ScoreSceneWrap nodes) {
        if (isOpenSelfRater()) {
            nodes.addNodeOpt(SubScoreNodeEnum.SELF_SCORE, 1);
        }
        if (isOpenPeerRater()) {
            nodes.addNodeOpt(SubScoreNodeEnum.PEER_SCORE, 1);
        }
        if (isOpenSubRater()) {
            nodes.addNodeOpt(SubScoreNodeEnum.SUB_SCORE, 1);
        }
        if (isOpenSuperiorRater()) {
            for (BaseAuditNode auditNode : superRater.getAuditNodes()) {
                nodes.addNodeOpt(SubScoreNodeEnum.SUPERIOR_SCORE, auditNode.getApprovalOrder());
            }
        }
        if (isOpenAppointRater()) {
            List<BaseAuditNode> auditNodes = appointRater.getAuditNodes();
            for (int i = 0; i < auditNodes.size(); i++) {
                nodes.addNodeOpt(SubScoreNodeEnum.APPOINT_SCORE, i + 1);
            }
        }
    }


    @JSONField(serialize = false)
    public boolean isOpenSelfRater() {
        return selfRater != null && selfRater.isOpen();
    }

    @JSONField(serialize = false)
    public boolean isOpenPeerRater() {
        return peerRater != null && peerRater.isOpen();
    }

    public boolean isOpenPeerAppointerRater() {
        return Objects.nonNull(peerRater) && peerRater.isOpen() && Objects.nonNull(peerRater.getAppointer());
    }

    public boolean isOpenSubAppointerRater() {
        return Objects.nonNull(subRater) && subRater.isOpen() && Objects.nonNull(subRater.getAppointer());
    }

    @JSONField(serialize = false)
    public boolean isOpenSubRater() {
        return subRater != null && subRater.isOpen();
    }

    @JSONField(serialize = false)
    public boolean isOpenSuperiorRater() {
        return superRater != null && superRater.isOpen();
    }

    @JSONField(serialize = false)
    public boolean isOpenAppointRater() {
        return appointRater != null && appointRater.isOpen();
    }

    public INodeConf getNodeConf(SubScoreNodeEnum node, int order, String evalEmpId) {
        if (node == SubScoreNodeEnum.SELF_SCORE) {
            return new SelfRaterNodeConf(selfRater, evalEmpId);
        }
        if (node == SubScoreNodeEnum.PEER_SCORE) {
            return new StdNode(SubScoreNodeEnum.PEER_SCORE, BusinessConstant.REVIEWERS_TYPE_AND, peerRater.getRaters());
        }
        if (node == SubScoreNodeEnum.SUB_SCORE) {
            return new StdNode(SubScoreNodeEnum.SUB_SCORE, BusinessConstant.REVIEWERS_TYPE_AND, subRater.getRaters());
        }
        if (node == SubScoreNodeEnum.SUPERIOR_SCORE) {
            List<BaseAuditNode> nodes = superRater.getAuditNode(order);
            return CollUtil.isEmpty(nodes) ? null : new SuperRaterNodeConf(nodes);
        }
        if (node == SubScoreNodeEnum.APPOINT_SCORE) {
            appointRater.initRaterOrder();
            List<BaseAuditNode> nodes = appointRater.getAuditNode(order);
            return CollUtil.isEmpty(nodes) ? null : new AppointRaterNodeConf(nodes);
            //return new StdNode(SubScoreNodeEnum.APPOINT_SCORE, BaseAuditNode.MULTI_AND, appointRater.allRater());
        }
        throw new RuntimeException("not support SubScoreNodeEnum");
    }

    public BigDecimal getNodeWeight(String scorerType) {
        if (SubScoreNodeEnum.SELF_SCORE.getScene().equals(scorerType)) {
            return selfRater.getNodeWeight();
        }
        if (SubScoreNodeEnum.PEER_SCORE.getScene().equals(scorerType)) {
            return peerRater.getNodeWeight();
        }
        if (SubScoreNodeEnum.SUB_SCORE.getScene().equals(scorerType)) {
            return subRater.getNodeWeight();
        }
        if (SubScoreNodeEnum.SUPERIOR_SCORE.getScene().equals(scorerType)) {
            return superRater.getNodeWeight();
        }
        if (SubScoreNodeEnum.APPOINT_SCORE.getScene().equals(scorerType)) {
            return appointRater.getNodeWeight();
        }
        return BigDecimal.ZERO;
    }

    public boolean isOpenRaterRule() {
        return isOpenPeerRater() || isOpenSelfRater() || isOpenSubRater() || isOpenSuperiorRater() || isOpenAppointRater();
    }

    public boolean isOpenRaterRule(SubScoreNodeEnum node) {
        switch (node) {
            case SELF_SCORE:
                return isOpenSelfRater();
            case PEER_SCORE:
                return isOpenPeerRater();
            case SUB_SCORE:
                return isOpenSubRater();
            case SUPERIOR_SCORE:
                return isOpenSuperiorRater();
            case APPOINT_SCORE:
                return isOpenAppointRater();
            default:
                return false;
        }
    }

    public EvalItemScoreRule asItemRule(String kpiTypeId, String itemId) {
        EvalItemScoreRule typeRule = new EvalItemScoreRule();
        typeRule.setKpiTypeId(kpiTypeId);
        typeRule.setKpiItemId(itemId);
        typeRule.setSelfRater(selfRater);
        typeRule.setPeerRater(peerRater);
        typeRule.setSubRater(subRater);
        typeRule.setAppointRater(appointRater);
        typeRule.setSuperRater(superRater);
        return typeRule;
    }

    public RaterNode allSelfRater() {
        List<? extends IItemScoreRuleOwn> items = items();
        BaseEvalFlow typeRule = typeRule();
        if (CollUtil.isEmpty(items)) {//okr或空类别
            return null;
        }
        if (typeRule != null) {
            RaterNodeConf selfRater = typeRule.getSelfRater();
            if (selfRater.isOpen()) {
                Rater rater = new Rater(items.get(0).getEmpId(), 5);
                return new RaterNode("self", Arrays.asList(rater));
            }
        }

        for (IItemScoreRuleOwn item : items) {
            //自定义指标不存在ItemScoreRule
            if (item.isAutoItem()) {
                continue;
            }
            //和志林沟通后这里表示自定义模板只要有任何一个指标开启了自评，就要返回自评环节，所以在循环中retuen了
            if (Objects.isNull(item.itemScoreRule())) {
                continue;
            }
            RaterNodeConf selfRater = item.itemScoreRule().getSelfRater();
            if (selfRater.isOpen()) {
                Rater rater = new Rater(item.getEmpId(), 5);
                return new RaterNode("self", Arrays.asList(rater));
            }
        }
        return null;
    }


    public List<Rater> allAppointRater() {
        BaseEvalFlow typeRule = typeRule();
        if (typeRule != null) {
            RaterNodeConf appointRater = typeRule.getAppointRater();
            if (appointRater.isOpen()) {
                return typeRule.getAppointRater().allRater();
            }
        }
        List<? extends IItemScoreRuleOwn> items = items();
        List<Rater> rs = new ArrayList<>();
        if (isOpenAppointRater()) {
            rs.addAll(appointRater.allRater());
        }
        if (CollUtil.isEmpty(items)) {//okr或空类别
            return Collections.emptyList();
        }
        List<Rater> collect = items.stream().map(IItemScoreRuleOwn::itemScoreRule)
                .flatMap(itemRule -> itemRule == null || itemRule.getAppointRater() == null
                        ? Stream.empty() : itemRule.getAppointRater().allRater().stream()).collect(Collectors.toList());
        rs.addAll(collect);
        return rs;
    }

    public List<Rater> allSuperRater() {
        BaseEvalFlow typeRule = typeRule();
        if (typeRule != null) {
            RaterNodeConf superRater = typeRule.getSuperRater();
            if (superRater.isOpen()) {
                return typeRule.getSuperRater().allRater();
            }
        }
        List<Rater> rs = new ArrayList<>();
        if (isOpenSuperiorRater()) {
            rs.addAll(superRater.allRater());
        }
        List<? extends IItemScoreRuleOwn> items = items();
        if (CollUtil.isEmpty(items)) {//okr或空类别
            return Collections.emptyList();
        }
        List<Rater> itemRaters = items.stream().map(IItemScoreRuleOwn::itemScoreRule)
                .flatMap(itemRule -> itemRule == null || itemRule.getSuperRater() == null
                        ? Stream.empty() : itemRule.getSuperRater().allRater().stream()).collect(Collectors.toList());
        rs.addAll(itemRaters);
        return rs;
    }


    public List<Rater> allPeerRater() {
        BaseEvalFlow typeRule = typeRule();
        if (typeRule != null) {
            RaterNodeConf peerRater = typeRule.getPeerRater();
            if (peerRater.isOpen()) {
                return typeRule.getPeerRater().getRaters();
            }
        }
        List<Rater> rs = new ArrayList<>();
        if (isOpenPeerRater()) {
            rs.addAll(peerRater.allRater());
        }
        List<? extends IItemScoreRuleOwn> items = items();
        if (CollUtil.isEmpty(items)) {//okr或空类别
            return Collections.emptyList();
        }
        List<Rater> peerRaters = items.stream().map(IItemScoreRuleOwn::itemScoreRule)
                .flatMap(itemRule -> itemRule == null || itemRule.getPeerRater() == null
                        ? Stream.empty() : itemRule.getPeerRater().getRaters().stream()).collect(Collectors.toList());
        rs.addAll(peerRaters);
        return peerRaters;
    }

    public List<Rater> allSubRater() {
        BaseEvalFlow typeRule = typeRule();
        if (typeRule != null) {
            RaterNodeConf subRater = typeRule.getSubRater();
            if (subRater.isOpen()) {
                return typeRule.getSubRater().getRaters();
            }
        }
        List<Rater> rs = new ArrayList<>();
        if (isOpenSubRater()) {
            rs.addAll(subRater.allRater());
        }
        List<? extends IItemScoreRuleOwn> items = items();
        if (CollUtil.isEmpty(items)) {//okr或空类别
            return Collections.emptyList();
        }
        List<Rater> subRaters = items.stream().map(IItemScoreRuleOwn::itemScoreRule)
                .flatMap(itemRule -> itemRule == null || itemRule.getSubRater() == null
                        ? Stream.empty() : itemRule.getSubRater().getRaters().stream()).collect(Collectors.toList());
        rs.addAll(subRaters);
        return subRaters;
    }


    protected List<? extends IItemScoreRuleOwn> items() {
        return null;
    }

    protected BaseEvalFlow typeRule() {
        return null;
    }


    public void asDisplayFlow(AsDisplayFlowRs nodes, String empId, List<? extends BaseTypeScoreResult> waitScores) {
        if (isOpenSelfRater()) {
            nodes.addNodeOpt(SubScoreNodeEnum.SELF_SCORE, 1, Arrays.asList(new Rater(empId)), filterRs(waitScores, SubScoreNodeEnum.SELF_SCORE, 1));
        }
        if (isOpenPeerRater()) {
            nodes.addNodeOpt(SubScoreNodeEnum.PEER_SCORE, 1, peerRater.getRaters(), filterRs(waitScores, SubScoreNodeEnum.PEER_SCORE, 1));
        }
        if (isOpenSubRater()) {
            nodes.addNodeOpt(SubScoreNodeEnum.SUB_SCORE, 1, subRater.getRaters(), filterRs(waitScores, SubScoreNodeEnum.SUB_SCORE, 1));
        }
        if (isOpenSuperiorRater()) {
            for (BaseAuditNode auditNode : superRater.getAuditNodes()) {
                Integer order = auditNode.getApprovalOrder();
                nodes.addNodeOpt(SubScoreNodeEnum.SUPERIOR_SCORE, order, auditNode.getRaters(), filterRs(waitScores, SubScoreNodeEnum.SUPERIOR_SCORE, order));
            }
        }
        if (isOpenAppointRater()) {
            for (BaseAuditNode auditNode : appointRater.getAuditNodes()) {
                Integer order = auditNode.getApprovalOrder();
                nodes.addNodeOpt(SubScoreNodeEnum.APPOINT_SCORE, order, auditNode.getRaters(), filterRs(waitScores, SubScoreNodeEnum.APPOINT_SCORE, order));
            }
        }
    }

    private static List<BaseTypeScoreResult> filterRs(List<? extends BaseTypeScoreResult> waitScores, SubScoreNodeEnum nodeEnum, Integer order) {
        if (CollUtil.isEmpty(waitScores)) {
            return new ArrayList<>();
        }
        return waitScores.stream().filter(rs -> StrUtil.equals(nodeEnum.getScene(), rs.getScorerType()))
                .filter(rs -> order.equals(rs.approvalOrder))
                .collect(Collectors.toList());
    }


    private static void addIndexRater(Map<String, RaterNode> nodeMap, String nodeName, List<Rater> raters) {
        if (raters.isEmpty()) {
            return;
        }
        if (!nodeMap.containsKey(nodeName)) {
            RaterNode node = new RaterNode(nodeName, raters);
            nodeMap.put(nodeName, node);
        } else {
            nodeMap.get(nodeName).addAllRater(raters);
        }
    }

    private static void addIndexMutualNodeRater(Map<String, RaterNode> nodeMap, String nodeName, MutualNodeConf mutualNodeConf) {
        if (Objects.isNull(mutualNodeConf.getAppointer()) && CollUtil.isEmpty(mutualNodeConf.getRaters())) {
            return;
        }
        List<Rater> raters = new ArrayList<>();
        List<Rater> inviteRaters = new ArrayList<>();
        if (Objects.nonNull(mutualNodeConf.getAppointer())) {
            inviteRaters.addAll(mutualNodeConf.getAppointer().appointRaters());
        }
        if (CollUtil.isNotEmpty(mutualNodeConf.getRaters())) {
            raters.addAll(mutualNodeConf.getRaters());
        }
        if (!nodeMap.containsKey(nodeName)) {
            RaterNode node = new RaterNode(nodeName, raters, inviteRaters);
            nodeMap.put(nodeName, node);
        } else {
            if (CollUtil.isNotEmpty(raters)) {
                nodeMap.get(nodeName).addAllRater(raters);
            }
            if (CollUtil.isNotEmpty(inviteRaters)) {
                nodeMap.get(nodeName).addAllInviteRater(inviteRaters);
            }
        }
    }

    public void initNodeRater(Map<String, RaterNode> nodeMap) {
        //RaterNode selfRater = allSelfRater();
        if (Objects.nonNull(selfRater) && selfRater.isOpen()) {
            Rater rater = new Rater("", 5);
            addIndexRater(nodeMap, "self", Arrays.asList(rater));
        }
        if (superRater != null && superRater.isOpen()) {
            addIndexRater(nodeMap, "super", superRater.allRater());
        }
        if (peerRater != null && peerRater.isOpen()) {
            addIndexMutualNodeRater(nodeMap, "peer", peerRater);
        }
        if (subRater != null && subRater.isOpen()) {
            addIndexMutualNodeRater(nodeMap, "sub", subRater);
        }
        if (appointRater != null && appointRater.isOpen()) {
            addIndexRater(nodeMap, "appoint", appointRater.allRater());
        }
    }

    public void transferScoringIf(String scoreType, String fromEmpId, KpiEmp toEmp, Boolean raterWasExist, Boolean wasCustom) {
        if (SubScoreNodeEnum.isSuperioNode(scoreType) && superRater != null) {
            superRater.replaceRater(fromEmpId, toEmp);
        }
        if (SubScoreNodeEnum.isSubNode(scoreType) && subRater != null) {
            if (raterWasExist && !wasCustom) {
                subRater.replaceRater(fromEmpId, toEmp, raterWasExist);
            } else {
                subRater.replaceRater(fromEmpId, toEmp);
            }
        }
        if (SubScoreNodeEnum.isPeerNode(scoreType) && peerRater != null) {
            if (raterWasExist && !wasCustom) {
                peerRater.replaceRater(fromEmpId, toEmp, raterWasExist);
            } else {
                peerRater.replaceRater(fromEmpId, toEmp);
            }
        }
        if (SubScoreNodeEnum.isAppointScore(scoreType) && appointRater != null) {
            appointRater.replaceRater(fromEmpId, toEmp);
        }
    }

    public void transferScoringIfV3(String scoreType, String fromEmpId, KpiEmp toEmp, Boolean wasCustom) {
        if (SubScoreNodeEnum.isSuperioNode(scoreType) && superRater != null) {
            superRater.replaceRaterV3(fromEmpId, toEmp);
        }
        if (SubScoreNodeEnum.isSubNode(scoreType) && subRater != null) {
            subRater.replaceRaterV3(fromEmpId, toEmp);
        }
        if (SubScoreNodeEnum.isPeerNode(scoreType) && peerRater != null) {
            peerRater.replaceRaterV3(fromEmpId, toEmp);
        }
        if (SubScoreNodeEnum.isAppointScore(scoreType) && appointRater != null) {
            appointRater.replaceRaterV3(fromEmpId, toEmp);
        }
    }
    public void skipRaterIf(String scoreType, String skipUserId) {
        if (SubScoreNodeEnum.isSuperioNode(scoreType) && superRater != null) {
            superRater.skipRater(skipUserId);
        }
        if (SubScoreNodeEnum.isSubNode(scoreType) && subRater != null) {
            subRater.skipRater(skipUserId);
        }
        if (SubScoreNodeEnum.isPeerNode(scoreType) && peerRater != null) {
            peerRater.skipRater(skipUserId);
        }
        if (SubScoreNodeEnum.isAppointScore(scoreType) && appointRater != null) {
            appointRater.skipRater(skipUserId);
        }
    }

    public void extendMutiConf(ScoreConf scoreConf) {
        if (Objects.nonNull(selfRater) && selfRater.isOpen()) {
            selfRater.setAnonymous(Boolean.FALSE.toString());
        }
        if (Objects.nonNull(peerRater) && peerRater.isOpen()) {
            peerRater.extendMutiConf(scoreConf);
        }
        if (Objects.nonNull(subRater) && subRater.isOpen()) {
            subRater.extendMutiConf(scoreConf);
        }
        if (Objects.nonNull(superRater) && superRater.isOpen()) {
            superRater.extendMutiConf(scoreConf);
        }
        if (Objects.nonNull(appointRater) && appointRater.isOpen()) {
            appointRater.extendMutiConf(scoreConf);
        }
    }


}
