package com.polaris.kpi.migration.entity;

import com.polaris.kpi.common.DelableDomain;
import com.polaris.kpi.org.domain.dept.entity.Company;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class DingMigrationTask extends DelableDomain {
    private String id;
    private String companyId; //新应用新授权的公司id
    private String dingCorpId;
    private String dingAgentId;
    private String dingSpaceId;
    private String migrationCompanyJson;//迁移公司数据，备份（用于回滚恢复）
    private String ossPath;
    private String status;// 0:未开始 1:进行中 2:已完成 3:失败

    public void accept( String companyId, String dingCorpId, String dingAgentId, String dingSpaceId,String migrationCompanyJson,String ossPath){
        this.companyId = companyId;
        this.dingCorpId = dingCorpId;
        this.dingAgentId = dingAgentId;
        this.ossPath = ossPath;
        this.dingSpaceId = dingSpaceId;
        this.migrationCompanyJson = migrationCompanyJson;

        this.status = "1";
        this.createdTime = new Date();
        this.updatedTime = new Date();
    }

    public void importSuccess(){
        this.status = "2";
        this.updatedTime = new Date();
    }
    public void failure(){
        this.status = "3";
        this.updatedTime = new Date();
    }
    public String expressStatus() {
        if ("0".equals(this.status)) {
            return "未开始";
        }
        if ("1".equals(this.status)) {
            return "进行中";
        }
        if ("2".equals(this.status)) {
            return "已完成";
        }
        return "失败";
    }
}
