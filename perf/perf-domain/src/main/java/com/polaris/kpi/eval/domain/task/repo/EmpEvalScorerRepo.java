package com.polaris.kpi.eval.domain.task.repo;

import com.polaris.kpi.eval.domain.task.entity.EmpEvalScorer;
import com.polaris.kpi.eval.domain.task.entity.EmpEvalScorerNode;
import com.polaris.kpi.eval.domain.task.entity.empeval.EvalScorersWrap;
import com.polaris.kpi.eval.domain.task.entity.empeval.TransferScorerRs;

import java.util.List;
import java.util.Set;

/**
 * @description:
 * @author: suxiaoqiu
 * @date: 2025/2/25 12:08
 * @param:
 * @return:
 **/
public interface EmpEvalScorerRepo {
    List<EmpEvalScorer> listEmpEvalScorer(String companyId, String taskUserId);

    EvalScorersWrap getEmpEvalScorersWrap(String companyId, String taskUserId);

    EmpEvalScorer getEmpEvalScorer(String companyId, String taskUserId, String scorerId);

    TransferScorerRs getTransferScorerRs(String companyId, String taskUserId, String fromScorerId, String toScorerId);

    void saveBatchEmpEvalScorerForUsers(String companyId, String opEmpId,List<EvalScorersWrap> scorers);

    void saveBatchEmpEvalScorer(String companyId, String opEmpId,  String taskUserId, List<EmpEvalScorer> empEvalScorers);

    void saveBatchEmpEvalScorerAndClearOld(String companyId, String opEmpId,  String taskUserId, List<EmpEvalScorer> empEvalScorers);

    void addEmpEvalScorer(String companyId, String opEmpId, EmpEvalScorer scorer);

    void saveDispatchNode(List<EmpEvalScorer> scorers);

    void saveTotalScorerNode(List<EmpEvalScorer> scorers);

    void batchUpAndDelEmpEvalScorer(String companyId, String opEmpId, List<EmpEvalScorer> scorers);

    void batchAddEmpEvalScorer(String companyId, String taskUserId, List<EmpEvalScorer> scorers);

    void delEmpEvalScorer(String companyId, String taskUserId);

    void upEmpEvalScorer(String companyId, EmpEvalScorer scorer);

    void clearEmpEvalScorer(String companyId, String taskUserId);

    void resetScoreEmpEvalScorer(String companyId,String opEmpId,
                                 String taskUserId, List<EmpEvalScorer> empEvalScorers);

    // 新增：查询已按目标状态+版本处理过的用户ID
    Set<String> listProcessedTaskUsers(String companyId, List<String> taskUserIds, String status, Integer version);
}