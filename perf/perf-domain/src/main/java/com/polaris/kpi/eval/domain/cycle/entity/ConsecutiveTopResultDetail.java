package com.polaris.kpi.eval.domain.cycle.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

/**
 * 连续绩优/绩差结果详情类
 * <AUTHOR>
 * @date 2024/9/28 15:14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ConsecutiveTopResultDetail implements Comparable<ConsecutiveTopResultDetail>{

    /**
     * 周期开始时间
     */
    private String cycleStart;

    /**
     * 周期开始时间
     */
    private String cycleEnd;

    /**
     * 周期 年份
     */
    private String year;

    /**
     * 周期类型对应的value
     */
    private String value;

    /**
     * 根据周期类型和时间缩写的周期名称，如周期类型是月度，时间是2024年1月1日到3月31日，简称2024年1月~3月
     */
    private String cycleShortName;

    /**
     * 关联任务列表
     */
    private List<ConsecutiveTopResultHitTask> hitTaskList = new ArrayList<>();


    public void addHitTask(ConsecutiveTopResultHitTask hitTask) {
        this.hitTaskList.add(hitTask);
    }

    @Override
    public int compareTo(@NotNull ConsecutiveTopResultDetail other) {
        // 首先按 year 比较
        int yearComparison = this.year.compareTo(other.year);
        if (yearComparison != 0) {
            return yearComparison;
        }
        // 如果 year 相同，则按 value 比较
        return this.value.compareTo(other.value);
    }
}
