package com.polaris.kpi.setting.domain.type;

import lombok.Getter;

@Getter
public enum OperationType {
    ADD("add", "新增"),
    UPDATE("update", "编辑"),
    DEL("del", "删除"),
    OPEN("open", "开启"),
    CLOSE("close", "关闭"),
    BATCH_OPEN("batchOpen", "批量开启"),
    BATCH_CLOSE("batchClose", "批量关闭"),
    BATCH_DEL("batchDel", "批量删除"),
    SYN_EVENT_ADD("synEventAdd", "同步事件新增"),
    SYN_EVENT_UP("synEventUp", "同步事件编辑");

    private String type;
    private String desc;

    OperationType(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static OperationType typeOf(String type) {
        for (OperationType value : values()) {
            if (value.type.equals(type)) {
                return value;
            }
        }
        return null;
    }
}
