package com.polaris.kpi.eval.domain.cycle.entity;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.common.DelableDomain;
import com.polaris.kpi.common.Domain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Ckey;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/12 22:08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConsecutiveTopResultSummary extends DelableDomain implements Serializable {

    /**
     * 主键id
     */
    @Ckey
    private String id;

    private String companyId;

    private String cycleId;

//    private Integer consecutiveGoodCount;

    private Boolean consecutiveGoodCounted = false;

    private Boolean consecutiveGoodRuleDefault = false;

    private Date consecutiveGoodCountTime;

//    private Integer consecutiveBadCount;

    private Boolean consecutiveBadCounted = false;

    private Boolean consecutiveBadRuleDefault = false;

    private Date consecutiveBadCountTime;

    private Integer performanceType;

    public ConsecutiveTopResultSummary(String companyId, String cycleId ,Integer performanceType,String createdUser) {
        this.companyId = companyId;
        this.cycleId = cycleId;
        this.performanceType = performanceType;
        this.createdUser = createdUser;
    }


    @JSONField(serialize = false)
    public boolean isNew() {
        return StrUtil.isBlank(id);
    }


    public void initOnNew(String id) {
        if (!isNew()) {
            return;
        }
        this.createdTime = new Date();
        this.id = id;
        this.isDeleted = "false";
    }
}
