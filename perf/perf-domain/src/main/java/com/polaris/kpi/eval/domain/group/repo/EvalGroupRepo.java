package com.polaris.kpi.eval.domain.group.repo;

import com.polaris.acl.kpi.eval.domain.EvalEmp;
import com.polaris.kpi.eval.domain.group.entity.EvalGroup;
import com.polaris.kpi.eval.domain.group.entity.EvalGroupEmp;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.BaseAuditNode;
import com.polaris.kpi.eval.domain.temp.entity.PerfTemplEvaluate;
import org.lufei.ibatis.mapper.PagedList;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> ye<PERSON><PERSON>oy<PERSON>
 * @date 2025/7/4 10:33
 */
public interface EvalGroupRepo {
    EvalGroup findById(String id, String companyId);
    String save(EvalGroup group);
    void delete(String id);

    void batchDelete(List<String> ids);

    boolean existsByGroupName(String groupName, String companyId, String excludeId);

    PagedList<EvalGroup> pagedEvalGroup(List<String> orgIds, String companyId, String groupName, String opEmpId, int pageNo, int pageSize);

    PagedList<EvalGroupEmp> pagedEmpOfEvalGroup(String groupId, List<String> orgIds, List<String> empIds, int pageNo, int pageSize);

    void addEmpsToGroup(String groupId, String companyId, List<EvalGroupEmp> emps, EvalGroup evalGroup, String opEmpId);

    int removeGroupEmpsByIds(List<String> ids, EvalGroup evalGroup);

    List<String> listGroupIdByAdminEmpId(String companyId, String empId);

    Map<String, List<EvalGroupEmp>> mapGroupIdToEmpList(List<String> groupIds);

    Map<String, String> mapGroupIdToTemplBaseId(List<String> groupIds);

    Map<String, EvalGroup> mapEmpOrgToLatestEvalGroup(List<String> groupIds);

    /**
     * 获取考核组成员映射
     * @param evalGroupIds 考核组ID列表
     * @return key: empId_orgId, value: EvalGroupEmp
     */
    Map<String, EvalGroupEmp> mapEmpOrgToEvalGroupEmp(List<String> evalGroupIds);

    List<EvalEmp> listEvalEmp(List<String> groupIds);

    void batchUpdateEmpOfEvalGroupAuditResultFlow(String companyId, List<String> empOfEvalGroupIds, List<BaseAuditNode> auditResultFlowConf, String opEmpId);

    void batchUpdateEmpOfEvalGroupScoreFlow(String companyId, List<String> empOfEvalGroupIds, PerfTemplEvaluate evaluate, String opEmpId);

    void clearEmpEvaluate(String companyId, String groupId, String opEmpId);
}
