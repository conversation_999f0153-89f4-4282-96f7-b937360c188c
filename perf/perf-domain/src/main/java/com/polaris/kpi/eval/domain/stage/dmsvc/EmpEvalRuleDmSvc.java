package com.polaris.kpi.eval.domain.stage.dmsvc;

import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.com.polaris.kpi.company.TenantSysConf;
import cn.com.polaris.kpi.eval.ItemCustomFieldValue;
import cn.com.polaris.kpi.eval.Rater;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.perf.www.common.constant.BusinessConstant;
import com.perf.www.common.em.CompanyItemTypeEnum;
import com.polaris.kpi.eval.domain.confirm.entity.BindOkrType;
import com.polaris.kpi.eval.domain.confirm.repo.ConfirmEvalRuleRepo;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalKpiType;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalRule;
import com.polaris.kpi.eval.domain.task.entity.empeval.KpiListWrap;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.*;
import com.polaris.kpi.eval.domain.task.repo.EmpEvalRuleRepo;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.MapWrap;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


public class EmpEvalRuleDmSvc {
    @Getter
    private EmpEvalMerge evalRule;
    @Getter
    private EvalUser user;
    private KpiListWrap editedTypes;//绩效系统中的维度
    private List<BindOkrType> bindOkrTypes;//OKR系统中的关键任务
    @Getter
    private List<EvalKpi> okrItems = new ArrayList<>();//OKR系统中的关键任务-指标
    private boolean indexCofMdfEmp;
    private List<EvalAudit> finishAudits = new ArrayList<>();
    @Getter
    private List<EvalAudit> onIndAudits = new ArrayList<>();

    public EmpEvalRuleDmSvc(boolean indexCofMdfEmp, EmpEvalMerge evalRule, EvalUser empEval, List<EmpEvalKpiType> editedTypes, List<BindOkrType> bindOkrTypes) {
        this.evalRule = evalRule;
        this.user = empEval;
        this.editedTypes = new KpiListWrap(editedTypes);
        this.editedTypes.asMap(type -> type.getKpiTypeId());
        this.bindOkrTypes = bindOkrTypes;
        this.indexCofMdfEmp = indexCofMdfEmp;
    }

    public void processEvalRule(TenantSysConf openConfirmOnInd, Integer approveOrder, String opEmpId, List<String> delAsk360EvalIds) {
//        OkrKpiTypeDmSvc dmSvc=new OkrKpiTypeDmSvc()
        //导入okr
        this.processOkr(opEmpId);
//        if (Objects.nonNull(evalRule)) {
//            evalRule.setKpiTypes(new KpiListWrap(kpiTypes1));
//        }
        //完成值审批流程
        this.processFinishAudit(opEmpId);
        //删除问卷考核
        this.processAsk360(delAsk360EvalIds);

        //指标审核人流程 //开启了按指标确认审批,先保存提交过来的审批人
        if (isOpenAuditInd(openConfirmOnInd, approveOrder)) {
            createAuditIndFlow(opEmpId);
        }
    }

    public void processAsk360(List<String> delAsk360EvalIds) {
        if (CollUtil.isNotEmpty(delAsk360EvalIds)) {
            evalRule.delAsk360Type();
        }
    }

    public void processOkr(String opEmpId) {
        if (CollUtil.isEmpty(bindOkrTypes)) {
            return;
        }
        for (BindOkrType typeAtOkr : this.bindOkrTypes) {
            EmpEvalKpiType kpiType = editedTypes.mapGet(typeAtOkr.getKpiTypeId());
            int order = 1;
            for (EvalRefOkr evalRefOkr : typeAtOkr.getKrList()) {
                evalRefOkr.initEvalRefOkr(kpiType.getCompanyId().getId(), user.getTaskId(), user.getId(), user.getEmpId(), opEmpId, kpiType.getKpiTypeId());
                if (!Boolean.TRUE.toString().equals(evalRefOkr.getIsDeleted())) {
                    EvalKpi kpiItem = new EvalKpi();
                    kpiItem.initBaseInfo(user.getCompanyId(), new EmpId(opEmpId), user.getId(), kpiType.getKpiTypeId());
                    kpiItem.initBaseInfo2(user.getTaskId(), user.getEmpId(), user.getId());
                    evalRefOkr.setTaskKpiId(kpiItem.getKpiItemId());//注意引用是记录id,不是kpiItemId kpiBiz.293行

                    //兼容字段，不推荐使用，以后类别的字段去列表对应的表找，不要在指标里去找
                    kpiItem.setKpiTypeName(kpiType.getKpiTypeName());
                    kpiItem.setOpenOkrScore(kpiType.getOpenOkrScore());
                    kpiItem.setKpiTypeWeight(kpiType.getKpiTypeWeight());
                    kpiItem.setKpiTypeClassify(kpiType.getKpiTypeClassify());
                    kpiItem.setItemWeight(evalRefOkr.getItemWeight());
                    kpiItem.setKpiItemName(evalRefOkr.getActionName());
                    kpiItem.setItemTargetValue(BigDecimal.ZERO);
                    kpiItem.setItemUnit(evalRefOkr.getUnit());
                    kpiItem.setResultInputType(BusinessConstant.NO);
                    kpiItem.setScorerType(BusinessConstant.EXAM);
                    kpiItem.setItemType(CompanyItemTypeEnum.NON_MEASURABLE.getType());
                    kpiItem.setOrder(order++);
                    kpiItem.setIsOkr(BusinessConstant.TRUE);
                    kpiItem.setCreatedUser(opEmpId);
                    kpiItem.setOkrScore(evalRefOkr.getOkrScore());

                    kpiItem.setRefOkr(evalRefOkr);

                    kpiItem.computeFinalOkrScore(evalRule.getScoreValueConf().isFullScoreRange(), evalRule.isTypeWeightOpen());
                    if (kpiType.getTypeRule() != null && kpiType.getTypeRule().hasScoreRule()) {//自定
                        EvalItemScoreRule targetRule = new EvalItemScoreRule();
                        BeanUtil.copyProperties(kpiType.getTypeRule(), targetRule, true);
                        kpiItem.customItemScoreRule(targetRule);
                    }
                    kpiType.getItems().add(kpiItem);
                    okrItems.add(kpiItem);
                }
            }
        }
    }

    //更新维度及指标
    public void processItems(EmpId opEmpId, RaterNodeConf s3SelfRater, MutualNodeConf s3PeerRater,
                             MutualNodeConf s3SubRater, S3SuperRaterConf s3SuperRater, S3RaterBaseConf s3AppointRater) {
        user.clearAutoScore();
        KpiListWrap kpiTypes = evalRule.getKpiTypes();
        List<EmpEvalKpiType> datas = kpiTypes.getDatas();
        MapWrap<String, EmpEvalKpiType> oldTypes = new MapWrap<>(datas, type -> type.getKpiTypeId());
//        ListWrap<EmpEvalKpiType> oldTypes = kpiTypes.asMap(type -> type.getKpiTypeId());


        //更新回考核表规则
        evalRule.setKpiTypes(editedTypes);
        user.setKpiTypes(editedTypes.getDatas());
        if (indexCofMdfEmp) {//开启了确认中修改评分人,用于支持统一评分人修改
            evalRule.accRater(s3SelfRater, s3PeerRater, s3SubRater, s3SuperRater, s3AppointRater);
        }
        for (EmpEvalKpiType editType : editedTypes.getDatas()) {
            EmpEvalKpiType oldType = oldTypes.get(editType.getKpiTypeId());
            if (oldType == null) {
                continue;
            }
            editType.setTypeRule(oldType.getTypeRule());
            if (CollUtil.isEmpty(oldType.getItems())) {
                continue;
            }
            MapWrap<String, EvalKpi> oldItemMap = new MapWrap<>(oldType.getItems(), evalKpi -> evalKpi.getKpiItemId());
            for (EvalKpi editItem : editType.getItems()) {
                editItem.setEmpId(user.getEmpId());//兼容一下手机端没有传empId
                EvalKpi oldItem = oldItemMap.get(editItem.getKpiItemId());
                if (oldItem == null) {
                    continue;
                }
                EvalItemScoreRule oldRule = oldItem.getItemScoreRule();
                if (indexCofMdfEmp && oldRule != null) {//使用页面传入配置进行替换,用于支持自定评分人修改
                    oldRule.accRater(editItem.getItemScoreRule());
                }
                editItem.setItemScoreRule(oldRule);
            }
        }
        evalRule.extendsRaterRuleForEdit();
        evalRule.initIndexRaters();
    }

    //处理指标的完成值审批,任务上的审批不修改.
    private void processFinishAudit(String opEmpId) {
        // 完成值审批分为两级,指标为一级.任务为二级.
        // okr因为只能配置维度上,所以使用维度配置进行计算
        FinishValueAuditConf taskFinishAudit = evalRule.getFinishValueAudit();
        for (EmpEvalKpiType type : editedTypes.getDatas()) {
            if (CollUtil.isEmpty(type.getItems())) {
                return;
            }
            if (type.isOkr()) {
                type.finishAudit2Item();
            }
            for (EvalKpi kpiItem : type.getItems()) {
                List<EvalAudit> finishAudits = kpiItem.createFinishAudits(taskFinishAudit, evalRule.getCreatedUser());
                this.finishAudits.addAll(finishAudits);
            }
        }
        //任务上的完成值审批人,排在指标审批后一级
        int order = CollUtil.isNotEmpty(this.finishAudits) ? 2 : 1;
        List<EvalAudit> clones = taskFinishAudit.createTaskFinishAudits(user.getCompanyId(), user.getTaskId(), user.getEmpId(), user.getId(),
                order, evalRule.getCreatedUser());
        this.finishAudits.addAll(clones);
    }

    // 更新评分流程业务写在数据库里, 时间不夠拆出来,先复用一下
    private void processScorer(EmpEvalRuleRepo empRuleRepo) {
        if (Objects.nonNull(evalRule) && evalRule.isCustom() && indexCofMdfEmp) {
            EmpEvalRule convert = Convert.convert(EmpEvalRule.class, evalRule);
            empRuleRepo.customItemFlow(convert, user.getTaskId(), user.getEmpId(), indexCofMdfEmp);// 时间不夠拆出来,先复用一下. todo
        }
    }

    //创建按指标的流程
    public void createAuditIndFlow(String opEmpId) {
        List<ItemCustomFieldValue> values = items().stream().flatMap(evalKpi -> evalKpi.getFieldValueList().stream())
                .filter(filed -> filed.isTypeEq(5) && StrUtil.isNotBlank(filed.getFieldValue()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(values)) {
            return;
        }
        BaseAuditNode onIndNode = evalRule.getConfirmTask().findConfirmOnIndNode();
        List<EvalAudit> onIndAuditFlows = new ArrayList<>();
        Set<Rater> raters = new HashSet<>();
        values.stream().forEach(field -> {//此阶段是已解析过的具体人员
            BaseAuditNode bean = JSONUtil.toBean(field.getFieldValue(), BaseAuditNode.class);
            bean.getRaters().forEach(rater -> {
                EvalAudit audit = new EvalAudit(user.getCompanyId(), user.getId(), rater.getEmpId(), onIndNode.getApprovalOrder(), EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT.getScene());
                audit.setModifyFlag(onIndNode.getModifyFlag());
                audit.setMultipleReviewersType(onIndNode.getMultiType());
                audit.setEmpId(user.getEmpId());
                audit.setTaskId(user.getTaskId());
                audit.setOrgId(user.getOrgId());
                audit.setKpiItemId(field.getKpiItemId());
                audit.setTransferFlag(onIndNode.getTransferFlag());
                audit.setCreatedUser(opEmpId);
                audit.setApproverType(bean.getApproverType());
                onIndAuditFlows.add(audit);
                raters.add(rater);
            });

        });
        onIndAudits = onIndAuditFlows;
        onIndNode.setRaters(new ArrayList<>(raters));
    }

    public boolean isOpenAuditInd(TenantSysConf openConfirmOnInd, Integer approvalOrder) {
        if (Objects.isNull(approvalOrder)) {
            return false;
        }
        if (!openConfirmOnInd.isOpen()) {//开启运营后台开关
            return false;
        }
        //并配置在考核表上
        BaseAuditNode onIndNode = evalRule.getConfirmTask().findConfirmOnIndNode();
        return onIndNode != null;
    }

    public List<EvalKpi> items() {
        List<EvalKpi> kpis = new ArrayList<>();
        for (EmpEvalKpiType kpiType : editedTypes.getDatas()) {
            if (CollUtil.isEmpty(kpiType.getItems())) {
                continue;
            }
            kpis.addAll(kpiType.getItems());
        }
        return kpis;
    }

    public void saveEvalRule(String opEmpId, ConfirmEvalRuleRepo confirmEvalRuleRepo) {
        evalRule.setKpiTypes(editedTypes);
        user.setKpiTypes(editedTypes.getDatas());
        confirmEvalRuleRepo.updateEmpEvalRule(opEmpId, evalRule, user,onIndAudits);
    }

}
