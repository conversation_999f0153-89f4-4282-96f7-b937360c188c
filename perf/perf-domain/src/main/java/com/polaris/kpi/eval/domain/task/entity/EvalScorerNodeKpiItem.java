package com.polaris.kpi.eval.domain.task.entity;

import cn.com.polaris.kpi.eval.ItemScoreValue;
import cn.com.polaris.kpi.eval.Rater;
import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalKpiType;
import com.polaris.kpi.eval.domain.task.entity.empeval.V3SubmitedEvalNodeScore;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.INodeConf;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.ScoreValueConf;
import com.polaris.kpi.eval.domain.task.entity.grade.IndLevelGroup;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Setter
@Getter
@NoArgsConstructor
@Slf4j
public class EvalScorerNodeKpiItem extends EvalScorerNodeScoreItemBase {
    private String id;//
    private String scorerScoreNodeId;//关联empEvalScorerNode.id
    private String kpiTypeId;//指标类id
    private String kpiItemId;//指标id
    private BigDecimal noItemScore;//评分人权重得分score*scoreWeight*环节权重
    private BigDecimal scoreWeightScore;//评分人权重得分score*scoreWeight
    private BigDecimal itemWeightScore;//指标权重得分(scoreWeightScore*指标权重)
    private BigDecimal raterItemScore;//评分人指标得分(score*指标权重*维度权重)
    private Integer version = 0;
    private Integer plusSubType; //指标 1-加分|2-减分 |3-加减分|null = 普通类型


    @JSONField(serialize = false, deserialize = false)
    private BigDecimal totalSplitWeight;//打总分时拆分的占比


    // DECIMAL(10,4) 的最大值为 999999.9999
    final BigDecimal MAX_DECIMAL_10_4 = new BigDecimal("999999.9999");

    public EvalScorerNodeKpiItem(String companyId, String opEmpId, String taskId, String taskUserId, String empId,
                                 String kpiTypeId, String kpiItemId, Rater rater, INodeConf iNodeConf) {
        super(companyId, opEmpId, taskId, taskUserId, empId, rater, iNodeConf);
        this.kpiTypeId = kpiTypeId;
        this.kpiItemId = kpiItemId;
        this.createdUser = opEmpId;
        this.updatedUser = opEmpId;
        this.createdTime = new Date();
        this.updatedTime = new Date();
        this.version = 0;
    }

    public void submitSrcScore(EmpEvalScorerNode otherSubmit, String opEmpId) {
        this.setScoreComment(otherSubmit.getTotalComment());
        this.setScoreLevel(otherSubmit.getScoreLevel());
        this.setUpdatedUser(opEmpId);
        this.score = otherSubmit.getTotalScore();
    }

    public void initIdNull() {
        this.id = null;
        this.scorerScoreNodeId = null;
    }

    @JSONField(serialize = false)
    public boolean isSubtractType() {
        return null != plusSubType && 2 == plusSubType;
    }

    public void builderItemWaitScore(EvalKpi kpi, String dingCorpId, List<String> defaultCustomScoreConfig,
                                     List<String> defaultPlusSubScoreConfig,
                                     IndLevelGroup indLevelGroup,
                                     ScoreValueConf scoreValueConf) {
        if (Objects.nonNull(indLevelGroup)) {
            kpi.setIndLevelGroup(indLevelGroup);
        }
        boolean isEvalLevel = Objects.nonNull(kpi.getIndLevelGroup());
        boolean isPlusSubScoreType = kpi.isPlusSubType() || kpi.isPlusType() || isSubtractType();
        // waitScore.markSkip(defaultCustomScoreConfig, defaultPlusSubScoreConfig, isPlusSubScoreType);
        if (isPlusSubScoreType) {
            this.score = BigDecimal.ZERO;
            return;
        }
        if (kpi.isOneVoteVetoType()) {
            this.vetoFlag = Boolean.FALSE.toString();
        }
        ItemScoreValue scoreValue = JSONUtil.toBean(kpi.getItemScoreValue(), ItemScoreValue.class);
        if (isEvalLevel && !scoreValue.isSystemOption()) {
            if (kpi.isIndLevel()) {
                this.scoreLevel = kpi.getIndLevelGroup().getLevels().get(0).getName();
                return;
            }
            this.score = kpi.getIndLevelGroup().getLevels().get(0).getScore();
            return;
        }
        //对公司 dingCorpId ： ='ding522a66d1de4e7aeaa39a90f97fcb1e09', //北京德皓国际会计师事务所 特殊处理，不管配置什么，自动提交都分都置为0
        if (Objects.nonNull(dingCorpId) && filterCorpIds().contains(dingCorpId)) {
            this.score = BigDecimal.ZERO;
            return;
        }
        if (Objects.nonNull(scoreValueConf)) {
            if (scoreValueConf.submitWithWeight()) {
                this.score = (scoreValueConf.getCustomFullScore().multiply(kpi.getItemWeight().divide(new BigDecimal(100))));
                return;
            }
        }
        this.score = scoreValueConf.getCustomFullScore();
    }

    private static List<String> filterCorpIds() {
        // ding522a66d1de4e7aeaa39a90f97fcb1e09: 北京德皓国际会计师事务所
        // ding8b3d719c932b311dacaaa37764f94726: A+绩效演示公司
        // ding1ea98a74651c5ec2f2c783f7214b6d69: 绩效测试公司
        return Arrays.asList("ding522a66d1de4e7aeaa39a90f97fcb1e09", "ding8b3d719c932b311dacaaa37764f94726", "ding1ea98a74651c5ec2f2c783f7214b6d69");
    }

    public void initPlusType(EvalKpi kpi) {
        if (kpi.isPlusType()) {
            plusSubType = 1;
            return;
        }
        if (kpi.isSubtractType()) {
            plusSubType = 2;
            return;
        }
        if (kpi.isPlusSubType()) {
            plusSubType = 3;
        }
    }


    public void resetScoreNodeForWaitDispatch() {
        this.status = 0;
        this.score = null;
        this.noItemScore = null;
        this.scoreWeightScore = null;
        this.finalScore = null;
        this.finalWeightScore = null;
        this.scoreLevel = null;
        this.scoreComment = null;
        this.scoreAttUrl = null;
        this.scoreOption = null;
        this.vetoFlag = null;
        this.updatedTime = new Date();
    }

    public void accpScorerSubmitItemScore(EvalScorerNodeKpiItem submitItem) {
        this.score = submitItem.getScore();
        this.scoreComment = submitItem.getScoreComment();
        this.scoreAttUrl = submitItem.getScoreAttUrl();
        this.scoreLevel = submitItem.getScoreLevel();
        this.scoreOption = submitItem.getScoreOption();
        this.vetoFlag = submitItem.getVetoFlag();
        this.updatedTime = new Date();
    }

    public void accpItemScore(EvalScoreResult scoreResult) {
        //历史数据，接收评分数据
        this.accpItemScoreData(scoreResult);
        this.scoreWeightScore = scoreResult.getEmpScore();
        this.vetoFlag = scoreResult.getVetoFlag();
    }

    public void copyOtherNodeData(EvalScorerNodeKpiItem otherKpi) {
        //接收评分数据
        this.score = otherKpi.getScore();
        this.scoreComment = otherKpi.getScoreComment();
        this.scoreAttUrl = otherKpi.getScoreAttUrl();
        this.scoreLevel = otherKpi.getScoreLevel();
        this.scoreOption = otherKpi.getScoreOption();
        this.vetoFlag = otherKpi.getVetoFlag();
        this.updatedTime = otherKpi.getUpdatedTime();
        this.status = otherKpi.getStatus();
    }

    public String asKpiItemKey() {
        return String.format("%s&%s", kpiItemId, kpiTypeId);
    }

    /**
     * 生成业务去重键，用于EvalScorerNodeKpiItem的完整去重
     * 包含：指标ID + 指标类型ID + 评分人ID + 评分类型 + 审批顺序
     * 确保同一评分人在同一审批顺序下对同一指标的评分唯一性
     *
     * @return 业务去重键
     */
    public String asBusinessDeduplicationKey() {
        return String.format("%s&%s&%s&%s&%s",
                kpiItemId, kpiTypeId, scorerId, scorerType, approvalOrder);
    }

    private void accpItemScoreData(EvalScoreResult scoreResult) {
        this.id = scoreResult.getId();
        //历史数据，接收评分数据
        this.score = null != scoreResult.getPlusScore() ? scoreResult.getPlusScore() : null != scoreResult.getSubtractScore() ? scoreResult.getSubtractScore() : scoreResult.getScore();
        this.scoreWeightScore = scoreResult.getEmpScore();
        this.scoreComment = scoreResult.getScoreComment();
        this.scoreAttUrl = scoreResult.getScoreAttUrl();
        this.scoreLevel = scoreResult.getScoreLevel();
        this.scoreOption = scoreResult.getScoreOption();
        this.vetoFlag = scoreResult.getVetoFlag();
        this.updatedTime = scoreResult.getUpdatedTime();
    }

    /**
     * @description: 类*指*人
     * @author: suxiaoqiu
     * @date: 2022/5/16 14:32
     * @param: [typePecWgt, itemPecWgt]
     * @return: void
     **/
    public void computeScoreWeightScore(BigDecimal typePecWgt, BigDecimal itemPecWgt, BigDecimal nodeWeight) {
        if (this.score == null) {
            return;
        }
        BigDecimal raterWeight = percentRaterWeight();
        BigDecimal initScore = score;
        this.scoreWeightScore = capToDecimalLimit(initScore.multiply(raterWeight).setScale(5, RoundingMode.DOWN).setScale(4, RoundingMode.HALF_UP));
        this.itemWeightScore = capToDecimalLimit(initScore.multiply(itemPecWgt).multiply(raterWeight).setScale(5, RoundingMode.DOWN).setScale(4, RoundingMode.HALF_UP));
        this.finalScore = capToDecimalLimit(initScore.multiply(typePecWgt).multiply(itemPecWgt).multiply(raterWeight).setScale(5, RoundingMode.DOWN).setScale(4, RoundingMode.HALF_UP));
        this.finalWeightScore = capToDecimalLimit(this.finalScore.multiply(nodeWeight).setScale(5, RoundingMode.DOWN).setScale(4, RoundingMode.HALF_UP));
        this.noItemScore = capToDecimalLimit(scoreWeightScore.multiply(nodeWeight).setScale(5, RoundingMode.DOWN).setScale(4, RoundingMode.HALF_UP));
        this.raterItemScore = raterItemScore(initScore,typePecWgt, itemPecWgt);
    }

    /**
     * 将值限制在DECIMAL(10,4)的范围内，避免数据库截断错误
     * @param value 原始值
     * @return 限制后的值
     */
    private BigDecimal capToDecimalLimit(BigDecimal value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        // 如果超出最大值，返回最大值并记录日志
        if (value.abs().compareTo(MAX_DECIMAL_10_4) > 0) {
            log.warn("Score value {} exceeds DECIMAL(10,4) limit {}, capping to limit. kpiItemId={}, scorerId={}",
                    value, MAX_DECIMAL_10_4, kpiItemId, scorerId);
            return value.signum() < 0 ? MAX_DECIMAL_10_4.negate() : MAX_DECIMAL_10_4;
        }
        return value;
    }

    private BigDecimal raterItemScore(BigDecimal initScore,BigDecimal typePecWgt, BigDecimal itemPecWgt) {
        BigDecimal raterItemScore = initScore.multiply(typePecWgt).multiply(itemPecWgt).setScale(5, RoundingMode.DOWN).setScale(4, RoundingMode.HALF_UP);
        return capToDecimalLimit(raterItemScore);
    }

    /**
     * 从评分结果创建KPI指标项节点
     * @param result 评分结果
     * @return 创建的KPI指标项节点
     */
    public static EvalScorerNodeKpiItem createFromResult(EvalScoreResult result) {
        EvalScorerNodeKpiItem kpiItem = new EvalScorerNodeKpiItem();
        kpiItem.setId(result.getId());
        kpiItem.setKpiItemId(result.getKpiItemId());
        kpiItem.setKpiTypeId(result.getKpiTypeId());
        kpiItem.setCompanyId(result.getCompanyId());
        kpiItem.setTaskId(result.getTaskId());
        kpiItem.setTaskUserId(result.getTaskUserId());
        kpiItem.setEmpId(result.getEmpId());
        kpiItem.setScorerId(result.getScorerId());
        kpiItem.setMultiType(result.getReviewersType());
        // 设置评分信息 历史数据，接收评分数据
        kpiItem.setScore(null != result.getPlusScore() ? result.getPlusScore() : null != result.getSubtractScore() ? result.getSubtractScore() : result.getScore());
        kpiItem.setScoreWeight(result.getScoreWeight());
        kpiItem.setScoreComment(result.getScoreComment());
        kpiItem.setFinalScore(result.getFinalScore());
        kpiItem.setScoreLevel(result.getScoreLevel());
        kpiItem.setScoreOption(result.getScoreOption());
        kpiItem.setVetoFlag(result.getVetoFlag());
        kpiItem.setScoreAttUrl(result.getScoreAttUrl());
        kpiItem.setFinalWeightScore(result.getFinalWeightScore());
        kpiItem.setNoItemScore(result.getNoItemScore());
        //历史数据，接收评分数据
        // 设置状态
        if ("pass".equals(result.getAuditStatus())) {
            kpiItem.setStatus(2); // 已提交
        } else {
            kpiItem.setStatus(1); // 待提交
        }
        // 设置时间信息
        kpiItem.setCreatedTime(result.getCreatedTime());
        kpiItem.setUpdatedTime(result.getUpdatedTime());
        kpiItem.setCreatedUser(result.getCreatedUser());
        kpiItem.setUpdatedUser(result.getUpdatedUser());
        kpiItem.setVersion(result.getVersion());
        return kpiItem;
    }
    @Override
    public EvalScorerNodeKpiItem clone() {
        return JSONUtil.toBean(JSONUtil.toJsonStr(this), EvalScorerNodeKpiItem.class);
    }
}
