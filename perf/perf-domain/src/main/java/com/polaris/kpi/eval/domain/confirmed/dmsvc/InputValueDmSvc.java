package com.polaris.kpi.eval.domain.confirmed.dmsvc;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.dept.DeptLeader;
import cn.com.polaris.kpi.eval.Name;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.polaris.kpi.eval.domain.TaskEvalContext;
import com.polaris.kpi.eval.domain.task.entity.EvalKpi;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEval;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalKpiType;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalRule;
import com.polaris.kpi.eval.domain.task.event.KpiItemUpdateFinishedValueEvent;
import com.polaris.kpi.eval.domain.task.event.admineval.BatchEvalInputNotifySend;
import com.polaris.kpi.eval.domain.task.event.admineval.EvalInputNotifySend;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.org.domain.dept.dmsvc.EmpParser;
import com.polaris.kpi.org.domain.dept.dmsvc.LevelLeaderParser;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class InputValueDmSvc {
    private static final TalentStatus curStatus = TalentStatus.CONFIRMED;
    private TenantId tenantId;
    private AdminTask task;
    private EmpId opEmpId;
    private List<EmpEval> evals;
    private List<EmpEval> autoEnterScores = new ArrayList<>();
    private List<EmpEval> needInputs = new ArrayList<>();
    private BatchEvalInputNotifySend notifySend = new BatchEvalInputNotifySend();
    private EmpParser empParser;
    private KpiItemUpdateFinishedValueEvent kpiItemUpdateFinishedValueEvent;

    public InputValueDmSvc(TenantId tenantId, EmpId opEmpId, TaskEvalContext evalContext, EmpParser empParser) {
        this.tenantId = tenantId;
        this.opEmpId = opEmpId;
        this.task = evalContext.getTask();
        this.evals = evalContext.getEvals();
        this.empParser = empParser;
    }

    public void updateInputFinishStatus() {
        for (EmpEval eval : evals) {
            final EvalUser taskUser = eval.getEval();
            EmpEvalMerge evalMerge = eval.getRule(); //之前查的是all 验证下有无问题 ruleRepo.getEmpEvalMerge(taskUser.getCompanyId(), taskUser.getId(), EmpEvalMerge.all);
            evalMerge.initIndexRaters();//更新一下评价关系缓存
//            ruleRepo.updateIndexRaters(evalMerge);
            taskUser.curStatus(curStatus, Collections.emptyList());
            taskUser.updateInputFinishStatus();
            taskUser.getEmpEvalRule().computeInputFinishStatus(curStatus.getStatus());
            /**1.0升级2.0的任务需使用兼容后的EmpEvalRule*/
            if (taskUser.wasTempTask()) {
                EmpEvalRule rule = new EmpEvalRule();
                BeanUtil.copyProperties(evalMerge, rule, true);
                taskUser.setEmpEvalRule(rule);
            }
        }
    }

    public void confirmedAutoEnterScore(String cycleEnd, boolean isReset) {
        List<String> batchItemUpdateTaskUserIds = new ArrayList<>();
        for (EmpEval eval : evals) {
            needInputs.add(eval);
            EvalUser user = eval.getEval();
            if (user.confirmedAutoEnterScore(cycleEnd, isReset, eval.getRule())) {
                autoEnterScores.add(eval);
                batchItemUpdateTaskUserIds.add(user.getId());
            }
        }
        if (CollUtil.isNotEmpty(batchItemUpdateTaskUserIds)) {
            this.kpiItemUpdateFinishedValueEvent = new KpiItemUpdateFinishedValueEvent(tenantId, batchItemUpdateTaskUserIds, opEmpId);
        }
    }

    public void dispatchInputItems(LevelLeaderParser leaderParser) {
        Set<String> allEmpIds = new HashSet<>();
        for (EmpEval needInput : needInputs) {
            EvalUser taskUser = needInput.getEval();
            Set<String> inputEmpIds = needInput.getInputEmpIds();
            for (EmpEvalKpiType kpiType : needInput.getRule().getKpiTypes().getDatas()) {
                if (CollUtil.isEmpty(kpiType.getItems())) {
                    continue;
                }
                for (EvalKpi item : kpiType.getItems()) {
                    if (item.isNotNeedInput()) {
                        continue;
                    }
                    Collection<String> collection = item.inputEmp(taskUser.getEmpId());
                    if (CollUtil.isNotEmpty(collection)) {
                        inputEmpIds.addAll(collection);
                        continue;
                    }
                    if (item.needLeaderInput()) {
                        DeptLeader eqLevel = leaderParser.getEqLevel(taskUser.getAtOrgCodePath(), Integer.valueOf(item.getManagerLevel()), taskUser.getEmpId());
                        item.acceptInputEmp(eqLevel.leaderIds());
                        inputEmpIds.addAll(eqLevel.leaderIds());
                    }
                }
            }
            needInput.acceptInputEmpIds(inputEmpIds);
            allEmpIds.addAll(inputEmpIds);
        }
        empParser.getEmps(allEmpIds);

        for (EmpEval needInput : needInputs) {
            EvalUser taskUser = needInput.getEval();
            if (CollUtil.isEmpty(needInput.getInputEmpIds())) {
                log.info("无指标需要录入完成值,taskUserId:{}", taskUser.getId());
                continue;
            }
            List<KpiEmp> inputEmps = empParser.getEmps(needInput.getInputEmpIds());
            taskUser.reviewers(inputEmps); // 更新负责人信息
            EvalInputNotifySend inputNotifySend = new EvalInputNotifySend(needInput.getRule().getInputNotify(), taskUser, new Name(task.getTaskName()), needInput.getInputEmpIds());
            notifySend.addEvent(inputNotifySend);
        }
    }

    public void publishAll() {
        notifySend.publish();
        if (kpiItemUpdateFinishedValueEvent != null) {
            kpiItemUpdateFinishedValueEvent.publish();
        }

    }

    public List<EvalUser> allEvalUser() {
        return evals.stream().map(eval -> eval.getEval()).collect(Collectors.toList());
    }
}
