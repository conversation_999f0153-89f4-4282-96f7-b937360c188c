package com.polaris.kpi.cache.domain.entity;

import cn.com.polaris.kpi.ObjItem;
import cn.com.polaris.kpi.eval.*;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.entity.EvalFormulaField;
import com.polaris.kpi.eval.domain.task.entity.EvalItemScoreRule;
import com.polaris.kpi.eval.domain.task.entity.empeval.BaseEvalFlow;
import com.polaris.kpi.eval.domain.task.entity.grade.IndLevelGroup;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrGoal;
import com.polaris.kpi.extData.domain.entity.ExtDataItemFieldCorr;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor//指标确认时的暂存（和指标变更时候的暂存数据格式不一样）
public class ConfirmTypeCache extends BaseEvalFlow {
    private BigDecimal weightVal;
    private String kpiTypeId;
    private String kpiTypeName;
    private KpiItemLimitCnt itemLimitCnt;
    private BigDecimal reserveOkrWeight;
    private CacheType indexType;
    private String isDeleted;
    private String isEmptyType;
    private BigDecimal maxExtraScore;
    private String isTypeLocked;
    private List<CacheItem> indexList;
    private PlusSubInterval plusSubInterval;
    private IndLevelGroup indLevelGroup;        //生成人员考核时,从模板或者考核表复制来
    protected String indLevelGroupId;         //指标等级组id
    private Integer typeOrder;
    private Integer scoreOptType;        //# 0:等级[无等级组]indLevelId==null # 1:等级[设等级组] indLevelId=1000234  #2:评分[无选项组] indLevelId==null  # 4:评分[有选项组], indLevelId=1000235
    private String des;        //维度描述 20230921增加
    private String isOkr;
    List<KpiTypeUsedField> kpiTypeUsedFields;
    private List<StaffConfItem> finishValueAudit;//完成值审核
    private String kpiTypeClassify;
    private String isTemporary;
    private String ask360TempId;   // 360问卷模版ID
    private String ask360TempName;
    private Integer scoringType;   // 计分方式(1:问卷平均分(题目分数之和/题目数)  2:问卷总分(题目分数之和))
    private String ask360EvalId;  //360问卷考核实例ID
    private List<OkrGoal> okrGoals;

    public List<String> tolockedItems() {
        if (StrUtil.isBlank(this.isTypeLocked)) {
            return null; //null:前端表示不锁定, []:前端表示全锁定
        } else {
            return JSONUtil.toList(this.isTypeLocked, String.class);
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    public static class CacheType {
        private String isOkr;
        private String dicValue;
        private String sysDicType;
        private String sysDicTypeId;
        private String id;
        private String classify;
        private boolean isOld;
        private String isTemporary;
        private Integer typeOrder;
        private Integer scoreOptType;        //# 0:等级[无等级组]indLevelId==null # 1:等级[设等级组] indLevelId=1000234  #2:评分[无选项组] indLevelId==null  # 4:评分[有选项组], indLevelId=1000235
        private String des;        //维度描述 20230921增加
        List<KpiTypeUsedField> kpiTypeUsedFields;
        private List<StaffConfItem> finishValueAudit;//完成值审核
    }

    @Getter
    @Setter
    @NoArgsConstructor
    public static class CacheItem {
        private Integer finishValueType;//finishValueType 1 = 正向, 0 = 负向
        private BigDecimal itemPlanValue;
        private String assessRuleRemark;//==itemRule
        private String indexResult;
        private String indexRater;
        private String scoreRule;//==scoringRule
        private BigDecimal targetVal;
        private String unit;
        private String showTargetValue;
        private String inputFormat;
        private String itemPlanFlag;
        private String examineOperType;
        private BigDecimal weightVal;
        private List<Person> enterPerson;//录入人
        private String resultInputEmpId;
        private String resultInputEmpName;
        private String managerLevel;
        //private List<StaffConfItem> scorerObjId;//配置模板，考核表指标设置的定向评分人格式
        private List<DirectionalScoreInfo> scorerObjId;//暂存里面定向评分人的格式
        private String isDeleted;
        private IndexInfo indexInfo;
        private String reviewer;
        private String itemFormula;
        private String itemScoreValue;
        private List<EvalFormulaField> formulaFieldList;
        private String thresholdJson;
        private String itemFieldJson;
        private String id;
        private BigDecimal itemFinishValue;
        private Integer showFinishBar;
        private String itemFullScoreCfg;
        private String valueRequired;
        private KpiOtherReqField otherReqField; //指标其它属性必填配置json字符串（如完成值必填、附件必填、备注必填）
        private PlusSubInterval plusSubInterval;
        private Integer order;
        private Integer typeOrder;
        private List<StaffConfItem> finishValueAudit;//完成值审核
        private String itemTargetValueText;     //指标项文本类型目标值
        private String okrRefFlag;
        private List<ObjItem> inputRole;
        private String okrGoalId;            //okr经营计划指标关联的目标ID
        private EvalItemScoreRule itemScoreRule; //ItemEvaluate
        /**
         * okr相关字段
         */
        private String targetId;
        private String targetName;
        private String okrTaskName;
        private String okrTaskId;
        private String deptName;
        private String evaluateStartDate;
        private String evaluateEndDate;
        private String actionId;
        private BigDecimal startValue;
        private BigDecimal endValue;
        private String actionName;
        private BigDecimal finishValue;
        private BigDecimal selfScore;
        private BigDecimal superiorScore;
        private BigDecimal score;
        private BigDecimal progress;
        private String progressStatus;
        private BigDecimal weightValue;

        private Integer formulaType;
        private IndLevelGroup indLevelGroup;         //生成人员考核时,从模板或者考核表复制来
        protected String indLevelGroupId;   //指标等级组id

        private List<ItemCustomFieldValue> fieldValueList;      //指标自定义字段
        private List<ExtDataItemFieldCorr> itemFieldCorr;
        private Integer finishValueSource = 1; // 1-手动录入，2-外部数据对接
    }

    @Getter
    @Setter
    @NoArgsConstructor
    public static class DirectionalScoreInfo {
        private Integer managerLevel;//1
        private String managerName;//直属主管
        private String id;//线上的暂存的roleId=id
        private String roleId;
        private Integer empNum;
        private String groupId;
        private String dingRoleId;
        private String roleName;
        private String adminType;
        private String value;
        private String label;
        private String avatar;
        private String dingOrgId;
        private String dingUserId;
        private String empId;
        private String empName;
        private String orgId;
        private String orgName;
        private Boolean disabled;
        private String status;
        private String employeeId;
        private String name;
        private String type;

        //兼容数据
        public String getRoleId() {
            if (StringUtils.isEmpty(roleId)) {
                return id;
            }
            return roleId;
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    public static class Person {
        private String inputId;
        private String name;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    public static class IndexInfo {
        private String empId;
        private String id;
        private String itemName;
        private String itemType;
        private String itemUnit;
        private BigDecimal itemValue;
        private BigDecimal itemWeight;
        private String kpiTypeId;
        private String kpiTypeName;
        private BigDecimal kpiTypeWeight;
        private BigDecimal plusLimit;
        private BigDecimal subtractLimit;
        private String showTargetValue;
        private BigDecimal itemPlanValue;
        private Integer showFinishBar;
        private Integer isNewEmp;
        private String scorerType;
        private PlusSubInterval plusSubInterval;
    }
}
