package com.polaris.kpi.eval.domain.confirm.dmsvc;

import com.polaris.kpi.eval.domain.stage.dmsvc.BaseAuditNodeChecker;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalRule;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.AffirmTaskConf;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;

import java.util.List;

public class ConfirmTaskCheckDmSvc {

    private BaseAuditNodeChecker nodeChecker;

    public ConfirmTaskCheckDmSvc(BaseAuditNodeChecker nodeChecker) {
        this.nodeChecker = nodeChecker;
    }

    public void checkRuleErr(List<EvalUser> evalUsers) {
        for (EvalUser evalUser : evalUsers) {
            EmpEvalRule empEvalRule = evalUser.getEmpEvalRule();
            AffirmTaskConf confirmTask = empEvalRule.getConfirmTask();
            TalentStatus current = TalentStatus.statusOf(evalUser.getTaskStatus());
//            evalUser.setRuleConfError(null);
            evalUser.resetRuleConfError("modify_item_audit"); //fix : 只清理指标确认时的错误码
            if (confirmTask.isOpen() && current.beforeEq(TalentStatus.CONFIRMING)) {
                nodeChecker.parseCheckFlowError(evalUser, confirmTask.getAuditNodes(), "modify_item_audit", confirmTask.getNodeEmpVacancy());
            }
        }
    }
}
