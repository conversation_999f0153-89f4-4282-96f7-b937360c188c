package com.polaris.kpi.eval.domain.statics.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/24 15:08
 */
@Data
public class CrossLevelGroup {

    /**
     * 周期标准名称 如 2024年1月 2024年一季度
     */
    private String cycleInfo;

    /**
     * 统计考核周期类型
     */
    private String cycleType;

    /**
     * 周期 年份
     */
    private Integer year;

    /**
     * 周期类型对应的value
     */
    private Integer value;



    /**
     * 可能包含多个结果 88｜A｜1.0  99｜S｜1.2
     */
    private List<CrossLevelTaskResult> crossLevelTaskResults;

    /**
     * 是否当前周期 true｜false
     */
    private String isCurrentCycle;

}
