package com.polaris.kpi.common;

import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.sdk.type.TenantId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> lufei
 * @date 2020/10/1 10:28 PM
 */
@Data
public abstract class Domain implements Serializable {
    @JSONField(serialize = false, deserialize = false)
    protected Integer version = 0;
    @JSONField(serialize = false, deserialize = false)
    protected String createdUser;//
//    @JSONField(serialize = false, deserialize = false)
    protected Date createdTime;//
    @JSONField(serialize = false, deserialize = false)
    protected String updatedUser;//
    @JSONField(serialize = false, deserialize = false)
    protected Date updatedTime;//

    public void incrementVersion() {
        this.version++;
    }
}
