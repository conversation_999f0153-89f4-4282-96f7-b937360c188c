package com.polaris.kpi.setting.domain.entity;

import cn.com.polaris.kpi.ObjItem;
import cn.com.polaris.kpi.SkipEmp;
import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.eval.domain.task.entity.EvalAudit;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

@Setter
@Getter
@NoArgsConstructor
@Slf4j
public class ResultAuditFlow {
    @JSONField(serialize = false)
    private String companyId;
    private String opEmpId;
    private boolean isAudit;
    //可发送通知的数据
    List<ResultAuditFlowNodeRater> raterSend;
    private ResultAuditFlowInstance curInstance;
    private List<ResultAuditFlowInstance> instances;
    private List<ResultAuditFlowNode> flowNodes;
    private List<ResultAuditFlowNodeRater> flowNodeRaters;
    private List<TaskResultAuditSummary> taskResultAuditSummaries;

    public void accFlow(List<ResultAuditFlowInstance> instances,
                        List<ResultAuditFlowNode> flowNodes, List<ResultAuditFlowNodeRater> flowNodeRaters, List<TaskResultAuditSummary> taskResultAuditSummaries) {
        this.instances = instances;
        this.flowNodes = flowNodes;
        this.flowNodeRaters = flowNodeRaters;
        this.taskResultAuditSummaries = taskResultAuditSummaries;
    }

    public ResultAuditFlow(String companyId, String opEmpId) {
        this.companyId = companyId;
        this.opEmpId = opEmpId;
    }

    public void accop(String companyId, String opEmpId, boolean isAudit) {
        this.companyId = companyId;
        this.opEmpId = opEmpId;
        this.isAudit = isAudit;
    }

    //加载流程（状态、层级等信息）
    public void loadAuditFlow(List<EvalScoreResult> curLevelRs) {
        if (CollUtil.isEmpty(this.instances)) {
            log.debug(" ===========loadAuditFlow.instances is null");
            return;
        }
        if (CollUtil.isEmpty(curLevelRs)) {
            log.debug(" ===========loadAuditFlow.curLevelRs is null");
            return;
        }
        String taskUserId = curLevelRs.get(0).getTaskUserId();
        Integer level = curLevelRs.get(0).getApprovalOrder();
        ResultAuditFlowInstance instance = this.instances.stream().filter(i -> i.macth(taskUserId)).findAny().get();
        ResultAuditFlowNode resultAuditFlowNode = instance.macthCurNode(level);
        resultAuditFlowNode.buildStatus(curLevelRs, opEmpId, isAudit, level);
        log.debug(" ===========loadAuditFlow.resultAuditFlowNode:{}", JSONUtil.toJsonStr(resultAuditFlowNode));
    }

    public void refreshAuditFlow() {
        if (CollUtil.isEmpty(this.instances)) {
            return;
        }
        List<ResultAuditFlowNode> flowNodes = this.instances.stream().flatMap(instance -> instance.getFlowNodes().stream())
                .collect(Collectors.toList());
        for (ResultAuditFlowNode flowNode : flowNodes) {
            flowNode.refreshStatus(flowNode.getApprovalOrder());
        }
    }

    //组装流程
    public void builderFlow(List<ResultAuditFlowInstance> instanceList, List<ResultAuditFlowNode> nodes,
                            List<ResultAuditFlowNodeRater> nodeRaters, List<ResultAuditFlowUser> user) {

        Map<String, List<ResultAuditFlowNode>> nodeMap = CollStreamUtil.groupByKey(nodes, ResultAuditFlowNode::getFlowInstanceId);
        Map<String, List<ResultAuditFlowNodeRater>> nodeRaterMap = CollStreamUtil.groupByKey(nodeRaters, ResultAuditFlowNodeRater::getFlowNodeId);
        Map<String, List<ResultAuditFlowUser>> userMap = CollStreamUtil.groupByKey(user, ResultAuditFlowUser::getNodeRaterId);
        Map<String, List<ResultAuditFlowUser>> instanceUserMap = CollStreamUtil.groupByKey(user, ResultAuditFlowUser::getFlowInstanceId);

        instanceList.forEach(i -> {
            List<ResultAuditFlowNode> nodeList = nodeMap.get(i.getId());
            if (CollUtil.isNotEmpty(nodeList)) {
                for (ResultAuditFlowNode node : nodeList) {
                    List<ResultAuditFlowNodeRater> nodeRaterList = nodeRaterMap.get(node.getId());
                    if (CollUtil.isEmpty(nodeRaterList)) {
                        continue;
                    }
                    /**根据节点将被审核流程人员放入审核人下*/
                    for (ResultAuditFlowNodeRater flowNodeRater : nodeRaterList) {
                        flowNodeRater.setFlowUsers(userMap.get(flowNodeRater.getId()));
                    }
                    node.setNodeRaters(nodeRaterList);
                }
            }
            i.setFlowNodes(nodeList);
            i.setFlowUsers(instanceUserMap.get(i.getId()));
        });
        this.instances = instanceList.stream().filter(i -> CollUtil.isNotEmpty(i.getFlowUsers())).collect(Collectors.toList());
    }


    //可发送消息通知的审核人
    public void getSendNofityFlow(Integer curNode) {
        if (CollUtil.isEmpty(this.instances)) {
            return;
        }
        List<ResultAuditFlowNodeRater> nodePassRaters = new ArrayList<>();
        //取出审核人
        List<ResultAuditFlowNodeRater> allRaters = getSendAllRater(curNode);

        Map<String, List<ResultAuditFlowNodeRater>> raterMapGroup = CollStreamUtil.groupByKey(allRaters, ResultAuditFlowNodeRater::getAuditEmpId);
        raterMapGroup.forEach((key, value) -> {
            boolean pass = CollUtil.filterNew(value, r -> r.isEnter()).size() == CollUtil.filterNew(value, r -> !r.isFinish()).size();
            log.debug("==============pass:{}", pass);
            log.debug("==============key:{},value:{}", key, JSONUtil.toJsonStr(value));
            if (pass) {
                for (ResultAuditFlowNodeRater rater : value) {
                    ResultAuditFlowNodeRater nodeRater = new ResultAuditFlowNodeRater();
                    nodeRater.accop(this.companyId, rater.getFlowInstanceId(), rater.getFlowNodeId(), null, key, this.opEmpId);
                    nodeRater.setFlowUsers(rater.getFlowUsers());

                    log.debug("==============rater.getFlowUsers():{}", key, JSONUtil.toJsonStr(rater.getFlowUsers()));
                    nodePassRaters.add(nodeRater);
                }
            }
        });
        this.passRater(nodePassRaters);
    }

    private List<ResultAuditFlowNodeRater> getSendAllRater(Integer curNode) {
        if (Objects.isNull(curNode)) {
            return this.instances.stream().flatMap(instance -> instance.getFlowNodes().stream())
                    .flatMap(node -> node.getNodeRaters().stream().filter(r -> !r.isFinish()))
                    .collect(Collectors.toList());
        }
        return this.instances.stream().flatMap(instance -> instance.getFlowNodes().stream())
                .filter(node -> Objects.equals(node.getApprovalOrder(), curNode) && CollUtil.isNotEmpty(node.getNodeRaters()))
                .flatMap(node -> node.getNodeRaters().stream())
                .collect(Collectors.toList());
    }

    public void passRater(List<ResultAuditFlowNodeRater> raters) {
        if (CollUtil.isEmpty(raters)) {
            return;
        }
        if (CollUtil.isEmpty(this.instances)) {
            return;
        }
        //raters中存在A+B
        Map<String, List<ResultAuditFlowNodeRater>> ratersMap = raters.stream().collect(Collectors.groupingBy(n -> n.getAuditEmpId()));
        //raterSend已有A。
        if (CollUtil.isNotEmpty(this.raterSend)) {
            for (ResultAuditFlowNodeRater nodeRater : this.raterSend) {
                List<ResultAuditFlowNodeRater> nodeRaters = ratersMap.get(nodeRater.getAuditEmpId());
                if (CollUtil.isNotEmpty(nodeRaters)) {
                    List<ResultAuditFlowUser> userList = nodeRaters.stream().flatMap(n -> n.getFlowUsers().stream()).collect(Collectors.toList());
                    List<String> taskUserIds = nodeRater.getFlowUsers().stream().map(user -> user.getTaskUserId()).collect(Collectors.toList());
                    List<ResultAuditFlowUser> newUser = userList.stream().filter(u -> !taskUserIds.contains(u.getTaskUserId())).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(newUser)) {
                        nodeRater.getFlowUsers().addAll(newUser);
                    }
                }
            }
            //校准人B不存在raterSend中，插入。
            Map<String, List<ResultAuditFlowNodeRater>> filterMap = CollUtil.filterNew(raters, r -> !CollUtil.map(this.raterSend, s -> s.getAuditEmpId(), true).contains(r.getAuditEmpId()))
                    .stream().collect(Collectors.groupingBy(n -> n.getAuditEmpId()));
            List<ResultAuditFlowNodeRater> nodeRaters = new ArrayList<>();
            filterMap.forEach((k, v) -> {
                ResultAuditFlowNodeRater nodeRater = new ResultAuditFlowNodeRater(this.companyId, k);
                nodeRater.setFlowUsers(v.stream().flatMap(u -> u.getFlowUsers().stream()).collect(Collectors.toList()));
                nodeRaters.add(nodeRater);
            });
            this.raterSend.addAll(nodeRaters);
        } else {
            List<ResultAuditFlowNodeRater> nodeRaters = new ArrayList<>();
            ratersMap.forEach((k, v) -> {
                ResultAuditFlowNodeRater nodeRater = new ResultAuditFlowNodeRater(this.companyId, k);
                nodeRater.setFlowUsers(v.stream().flatMap(u -> u.getFlowUsers().stream()).collect(Collectors.toList()));
                nodeRaters.add(nodeRater);
            });
            this.raterSend = nodeRaters;
        }
        List<ResultAuditFlowNode> flowNodes = this.instances.stream().flatMap(instance -> instance.getFlowNodes().stream())
                .collect(Collectors.toList());
        for (ResultAuditFlowNode flowNode : flowNodes) {
            flowNode.passRater(raters);
        }
    }

    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    //只用来初始化
    public void builderFlowStatus(Map<String, ResultAuditFlowUser> flowUserMap) {
        if (CollUtil.isEmpty(this.flowNodeRaters)) {
            return;
        }
        for (ResultAuditFlowNodeRater flowNodeRater : this.flowNodeRaters) {
            ResultAuditFlowUser flowUser = flowUserMap.get(flowNodeRater.getTaskUserId() + "-" + flowNodeRater.getLevel());
            if (Objects.nonNull(flowUser)) {
                flowNodeRater.setStatus(Objects.equals(flowUser.getStatus(), 1) ? 2 : 0);
            }
        }

        //赋值层级状态
        Map<String, List<ResultAuditFlowNodeRater>> flowNodeRaterMap = this.flowNodeRaters.stream().collect(Collectors.groupingBy(r -> r.getFlowNodeId()));
        for (ResultAuditFlowNode flowNode : this.flowNodes) {
            flowNode.updateStatus(flowNodeRaterMap.get(flowNode.getId()));
        }
    }


    public void builderCurFlow(ResultAuditFlowInstance flowInstance, List<ResultAuditFlowNode> nodes,
                               List<ResultAuditFlowNodeRater> nodeRaters) {
        if (Objects.isNull(flowInstance)) {
            return;
        }
        Map<String, List<ResultAuditFlowNodeRater>> nodeRaterMap = CollStreamUtil.groupByKey(nodeRaters, ResultAuditFlowNodeRater::getFlowNodeId);
        for (ResultAuditFlowNode node : nodes) {
            List<ResultAuditFlowNodeRater> nodeRaterList = nodeRaterMap.get(node.getId());
            if (CollUtil.isEmpty(nodeRaterList)) {
                continue;
            }
            node.setNodeRaters(nodeRaterList);
        }
        flowInstance.setFlowNodes(nodes);
        this.curInstance = flowInstance;
    }

    public void updateFlowStatus(String adminEmpId, Integer level) {
        if (CollUtil.isEmpty(this.curInstance.getFlowNodes())) {
            return;
        }
        for (ResultAuditFlowNode flowNode : this.curInstance.getFlowNodes()) {
            if (!Objects.equals(flowNode.getApprovalOrder(), level)) {
                continue;
            }
            for (ResultAuditFlowNodeRater flowNodeRater : flowNode.getNodeRaters()) {
                flowNodeRater.mark(adminEmpId, level);
            }
            //赋值层级状态
            flowNode.updateStatus();
        }
    }


    public List<SkipEmp> skip(List<EvalAudit> finish, List<EvalAudit> dispatched) {
        //要跳过的节点
        List<ResultAuditFlowNode> skipNodeList = CollUtil.filterNew(this.flowNodes, r -> r.needSkipNode());
        if (CollUtil.isEmpty(skipNodeList)) {
            return new ArrayList<>();
        }
        List<SkipEmp> skipEmps = new ArrayList<>();
        Map<String, List<ResultAuditFlowNodeRater>> nodeRaterMap = this.flowNodeRaters.stream().collect(Collectors.groupingBy(ResultAuditFlowNodeRater::getFlowNodeId));
        int order = 0;
        for (ResultAuditFlowNode flowNode : skipNodeList) {
            List<ResultAuditFlowNodeRater> nodeRaterList = nodeRaterMap.get(flowNode.getId());
            if (CollUtil.isEmpty(nodeRaterList)) {
                continue;
            }
            //跳过节点的审核人
            for (ResultAuditFlowNodeRater nodeRater : nodeRaterList) {
                if (flowNode.isAndAudit() && !nodeRater.isSkip()) {
                    dispatched.add(new EvalAudit(new TenantId(this.companyId), nodeRater.getTaskUserId(), nodeRater.getAuditEmpId(), nodeRater.getLevel(), EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene()));
                    continue;
                }
                order = nodeRater.getLevel();
                updateFlowStatus(nodeRater.getAuditEmpId(), nodeRater.getLevel());
                //记录跳过的数据
                if (Objects.equals(nodeRater.getSkipType(), 1)) {
                    skipEmps.add(new SkipEmp(nodeRater.getTaskUserId(), "-1", "空缺跳过", "-1", 1, nodeRater.getLevel()));
                }
                if (Objects.equals(nodeRater.getSkipType(), 2)) {
                    skipEmps.add(new SkipEmp(nodeRater.getTaskUserId(), nodeRater.getAuditEmpId(), "重复跳过", "-1", 2, nodeRater.getLevel()));
                }
                finish.add(new EvalAudit(new TenantId(this.companyId), nodeRater.getTaskUserId(), nodeRater.getAuditEmpId(), nodeRater.getLevel(), EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene()));
            }
        }
        //没有因为会签需要分发的节点。找到跳过的下级
        if (CollUtil.isEmpty(dispatched)) {
            for (ResultAuditFlowNodeRater flowNodeRater : this.flowNodeRaters) {
                if (Objects.equals(flowNodeRater.getLevel(), order + 1)) {
                    dispatched.add(new EvalAudit(new TenantId(this.companyId), flowNodeRater.getTaskUserId(), flowNodeRater.getAuditEmpId(), flowNodeRater.getLevel(), EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene()));
                }
            }
        }
        return skipEmps;
    }

}
