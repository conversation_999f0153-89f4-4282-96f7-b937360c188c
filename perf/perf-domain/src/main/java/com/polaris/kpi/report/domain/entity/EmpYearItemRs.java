package com.polaris.kpi.report.domain.entity;

import cn.hutool.core.util.StrUtil;

import java.math.BigDecimal;
import java.util.Objects;

public class EmpYearItemRs {
    private Integer cnt;
    private BigDecimal score;
    private String level;
    private BigDecimal coefficient;
    private String fieldName;

    public void setGrade(String stepName, String perfCoefficient) {
        setLevel(stepName);
        if (StrUtil.isNotBlank(perfCoefficient)) {
            setCoefficient(new BigDecimal(perfCoefficient).setScale(2, BigDecimal.ROUND_HALF_UP));
        }else {
            setCoefficient(null);
        }
        if (!Objects.isNull(score)) {
            this.score = score.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
    }

    public Integer getCnt() {
        return cnt;
    }

    public void setCnt(Integer cnt) {
        this.cnt = cnt;
    }

    public BigDecimal getScore() {
        return score;
    }

    public void setScore(BigDecimal score) {
        this.score = score;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public BigDecimal getCoefficient() {
        return coefficient;
    }

    public void setCoefficient(BigDecimal coefficient) {
        this.coefficient = coefficient;
    }

    public boolean onlyOneTask() {
        return cnt == 1;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getFieldName() {
        return fieldName;
    }

    public String asShowStr(Integer showType) {
        if (score == null || showType == null) {
            return null;
        }
        BigDecimal formatScore = score.setScale(2, BigDecimal.ROUND_HALF_UP);
        if (showType == 1) {
            return formatScore + "";
        }
        if (showType == 2) {
            return level + " | " + coefficient;
        }
        if (showType == 3) {
            return formatScore + " | " + level + " | " + coefficient;
        }
        return null;
    }

    public void scaleScore() {
        if (score == null) {
            return;
        }
        this.score = score.setScale(2, BigDecimal.ROUND_HALF_UP);
    }
}
