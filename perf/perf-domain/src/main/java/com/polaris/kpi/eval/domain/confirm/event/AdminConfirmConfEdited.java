package com.polaris.kpi.eval.domain.confirm.event;

import cn.hutool.core.collection.CollUtil;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.AffirmTaskConf;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.org.domain.common.BaseEvent;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
public class AdminConfirmConfEdited extends BaseEvent {

    private String taskId;
    private String opEmpId;
    private String opAdminType;
    private AdminTask beforeTask;
    private AdminTask afterTask;
    private int resetType = 0; //重置类型 0=不重置 1=重置指定id,2=重置所有
    @Setter
    private List<String> needResetUserIds;
    private String changedStage = TalentStatus.CONFIRMING.getStatus();

    public AdminConfirmConfEdited(String companyId, String taskId,
                                  String adminType, String opEmpId,
                                  AdminTask beforeTask, AdminTask after,
                                  boolean resetAllAfterConfirm, List<String> needResetUserIds) {
        super(companyId);
        this.taskId = taskId;
        this.opEmpId = opEmpId;
        this.beforeTask = beforeTask;
        this.afterTask = after;
        this.needResetUserIds = resetAllAfterConfirm ? new ArrayList<>() : needResetUserIds;
        this.opAdminType = adminType;
        this.resetType = resetAllAfterConfirm ? 2 : CollUtil.isNotEmpty(needResetUserIds) ? 1 : 0;
    }

    public void start() {

    }

    public boolean resetAll() {
        return resetType == 2;
    }

    public boolean resetByIds() {
        return resetType == 1;
    }

    public boolean resetNone() {
        return resetType == 0;
    }
}
