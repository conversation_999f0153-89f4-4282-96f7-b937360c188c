package com.polaris.kpi.eval.domain.task.dmsvc.score;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.polaris.kpi.common.KpiI18NException;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.entity.empeval.EvalScorersWrap;
import com.polaris.kpi.org.domain.dept.entity.CompanyMsgCenter;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;


/**
 * @Author: suxiaoqiu
 * @Date: 2025/05/19 17:42
 * @Description: 评分人跳过
 */
@Slf4j
@Getter
@NoArgsConstructor
public class SkipScorerDmSvc {

    /**
     * 同级评、下级评跳过时的特殊标识符
     * 用于满足查询条件 transfer_to IS NOT NULL，但不表示真正的接收者
     * 同级评、下级评默认会签，跳过时仅跳过本人，无需转移节点给其他人
     * 使用下划线前缀避免与真实员工ID冲突
     */
    private static final String SKIP_NO_TRANSFER_MARKER = "_skip_self_only_";

    private TenantId tenantId;
    private String opEmpId;
    private EvalUser evalUser;
    private EmpEvalMerge evalRule;
    @Setter
    private EvalScorersWrap scorersWrap;// 所有评价人
    private ListWrap<CompanyMsgCenter> msgCentersWrap;
    private ListWrap<KpiEmp> raterGroup;//员工信息【操作人,fromEmpId,toEmpId】
    @Setter
    private ListWrap<EvalKpi> kpiWrap;

    private List<CompanyMsgCenter> fromCenterMsgs = new ArrayList<>();

    private EmpEvalScorer scorer; //转出者
    private final List<EmpEvalScorer> receivers = new ArrayList<>(); //(跳过评分人的)接受者

    private final List<EmpEvalScorerNode> needSubmits = new ArrayList<>(); //(需要提交的节点)接受者
    private OperationLog operationLog;

    private Set<String> msgScenes;

    public SkipScorerDmSvc(TenantId companyId, String opEmpId, EvalUser evalUser, EmpEvalMerge evalRule, EvalScorersWrap scorersWrap,
                           ListWrap<CompanyMsgCenter> msgCentersWrap, List<KpiEmp> raters) {
        this.tenantId = companyId;
        this.opEmpId = opEmpId;
        this.evalUser = evalUser;
        this.evalRule = evalRule;
        this.scorersWrap = scorersWrap;
        this.msgCentersWrap = msgCentersWrap;
        this.raterGroup = new ListWrap<>(raters).asMap(KpiEmp::getEmpId);
        this.buildKpiWrap();
    }


    /**
     * 检查是否允许跳过评分人
     *
     * @param skipScorerId 要跳过的评分人ID
     * @param skipScoreType 要跳过的评分类型
     * @throws KpiI18NException 如果不允许跳过则抛出异常
     */
    public void checkAllowSkip(String skipScorerId, String skipScoreType) {
        log.warn("跳过责任人校验,skipScorerId:{},skipScoreType:{}", skipScorerId, skipScoreType);
        // 获取待跳过的KPI项ID集合
        Set<String> kpiItemIds = listWaitSkipKpiItems(skipScorerId, skipScoreType);
        if (CollUtil.isEmpty(kpiItemIds)) {
            log.warn("没有可以跳过的记录");
            throw new KpiI18NException("task.unableSkip", "没有可以跳过的记录");
        }
        //查询当前跳过的环节所在评价的所有评分人节点
        List<EmpEvalScorerNode> nodes = this.scorersWrap.listSNByScoreType(skipScoreType);
        ListWrap<EmpEvalScorerNode> nodesWrap = new ListWrap<>(nodes).groupBy(EmpEvalScorerNode::getScorerId);
        // 检查是否只有一个评分人
        if (CollUtil.isEmpty(nodes) || nodesWrap.groupKeySet().size() == 1) {
            log.warn("仅剩一个评价人，无法跳过评分环节，您可转交评价人");
            throw new KpiI18NException("task.unableSkip", "仅剩一个评价人，无法跳过评分环节，您可转交评价人");
        }

        // 将 List<EvalScorerNodeKpiItem> allKpiItems 转换为<String,Set<String>>  解释：<kpiItemId,Set<ScorerId>>
        Map<String, Set<String>> kpiItemScorerMap = buildWaitSkipKpiItemScorerMap(nodes, kpiItemIds);
        if (MapUtil.isEmpty(kpiItemScorerMap)) {
            log.warn("没有可以跳过的指标记录");
            throw new KpiI18NException("task.unableSkip", "没有可以跳过的记录");
        }
        // 检查每个KPI项是否至少有一个其他评分人
        validateKpiItemScorers(kpiItemScorerMap, skipScorerId);
    }

    /**
     * 验证每个KPI项是否至少有一个其他评分人
     *
     * @param kpiItemScorerMap KPI项与评分人的映射关系
     * @param skipScorerId 要跳过的评分人ID
     */
    private void validateKpiItemScorers(Map<String, Set<String>> kpiItemScorerMap, String skipScorerId) {
        kpiItemScorerMap.forEach((kpiItemKey, scorerIds) -> {
            Set<String> waitSkipScorerIds = scorerIds.stream()
                    .filter(s -> !s.equals(skipScorerId))
                    .collect(Collectors.toSet());

            EvalKpi kpi = kpiWrap.mapGet(kpiItemKey);
            if (CollUtil.isEmpty(waitSkipScorerIds)) {
                log.warn("KPI项 '{}' 仅剩一个评价人，无法跳过", kpi.getKpiItemName());
                throw new KpiI18NException("task.unableSkip",
                        "'" + kpi.getKpiItemName() + "' 仅剩一个评价人，无法跳过评分环节，您可转交评价人");
            }
        });
    }

    private Set<String> listWaitSkipKpiItems(String skipScorerId, String skipScoreType) {
        EmpEvalScorer scorer = this.scorersWrap.getCurEvalScorer(skipScorerId);
        if (Objects.isNull(scorer)) {
            log.warn("无跳过评分人");
            return new HashSet<>();
        }

        List<EmpEvalScorerNode> waitSkipNodes = scorer.waitSubmitScoreNodesByScoreType(skipScoreType);//待提交的节点
        if (CollUtil.isEmpty(waitSkipNodes)) {
            log.warn("无跳过评分人的待跳过的环节记录");
            return new HashSet<>();
        }

        List<EvalScorerNodeKpiItem> waitSkipKpiItems = waitSkipNodes.stream().map(EmpEvalScorerNode::getAllKpiItems).flatMap(Collection::stream).collect(Collectors.toList());
        //Set<String> kpiItem
        return waitSkipKpiItems.stream().map(EvalScorerNodeKpiItem::getKpiItemId).collect(Collectors.toSet());
    }


    private void buildKpiWrap() {
        List<EvalKpi> kpis = evalRule.allItems();
        this.kpiWrap = new ListWrap<>(kpis).asMap(EvalKpi::asKpiItemKey);
    }

    private Map<String, Set<String>> buildWaitSkipKpiItemScorerMap(List<EmpEvalScorerNode> nodes, Set<String> kpiItemIds) {
        List<EvalScorerNodeKpiItem> allKpiItems = nodes.stream().map(EmpEvalScorerNode::getAllKpiItems).flatMap(Collection::stream).collect(Collectors.toList());
        allKpiItems.removeIf(item -> !kpiItemIds.contains(item.getKpiItemId()));//移除不包含待跳过的评价指标
        // 将 List<EvalScorerNodeKpiItem> allKpiItems 转换为<String,Set<String>>  解释：<kpiItemId,Set<ScorerId>>
        return allKpiItems.stream().collect(Collectors.groupingBy(EvalScorerNodeKpiItem::asKpiItemKey, Collectors.mapping(EvalScorerNodeKpiItem::getScorerId, Collectors.toSet())));
    }



    public void acceptMsgScenes(Set<String> msgScenes) {
        this.msgScenes = msgScenes;
    }

    /**
     * 获取需要保存的评分人列表
     *
     * @return 需要保存的评分人列表
     */
    public List<EmpEvalScorer> needSaveUpEmpEvalScorers() {
        List<EmpEvalScorer> scorers = new ArrayList<>();
        scorers.add(scorer);
        scorers.addAll(receivers);
        return scorers;
    }

    /**
     * 执行跳过评分人操作
     *
     * @param skipScorerId 要跳过的评分人ID
     * @param skipScoreType 要跳过的评分类型
     */
    public void skipRater(String skipScorerId, String skipScoreType) {
        log.info("------执行跳过责任人开始！-------params,skipScorerId:{},skipScoreType:{}", skipScorerId, skipScoreType);
        //1.allScorers 找到 获取被跳过的评分人scorer
        scorer = scorersWrap.getCurEvalScorer(skipScorerId);

        // 2. 获取待转移的节点
        List<EmpEvalScorerNode> waitTransferNodes = scorer.listSNForSkip(skipScoreType);
        checkErrorScorerNode(waitTransferNodes);
        log.info("------找到待转移的节点-------waitTransferNodes:{}", JSONUtil.toJsonStr(waitTransferNodes));

        //该考核任务存在评价总等级合并提交
        boolean existsTotalLevelWaitSubmit = scorer.existsTotalLevelWaitSubmit(skipScoreType);
        if (existsTotalLevelWaitSubmit) {
            List<EmpEvalScorerNode> waitTransferTotalLevelNodes = scorer.listSNForSkip(SubScoreNodeEnum.TOTAL_LEVEL.getScene());
            waitTransferNodes.addAll(waitTransferTotalLevelNodes);
        }

        // 3. 找出所有接收者并准备映射关系 装在 receiveScorerOrderMap ,receivesMap
        Map<String, List<Integer>> receiveScorerOrderMap = new HashMap<>();
        Map<Integer, List<String>> orderReceiveIdsMap = new HashMap<>();
        this.listReceives(skipScorerId, waitTransferNodes, receiveScorerOrderMap, orderReceiveIdsMap,existsTotalLevelWaitSubmit);

        // 4. 验证接收者
        Set<String> receiveScorerIds = receiveScorerOrderMap.keySet();
        checkErrorReceiverScorer(receiveScorerIds);
        log.info("------所有的接收者-------receiveScorerIds:{}", receiveScorerIds);

        // 5. 处理每个接收者
        // 判断是否为同级评或下级评（会签场景）
        boolean isMutualScore = SubScoreNodeEnum.mutualScoreScene().contains(skipScoreType);
        if (isMutualScore && CollUtil.isNotEmpty(receiveScorerIds)) {
            // 同级评、下级评：跳过节点复制，设置特殊标识符
            log.info("同级评/下级评存在多人，跳过节点复制，直接标记跳过: skipScoreType={}, receiveScorerIds={}", skipScoreType, receiveScorerIds);
            setSkipMarkerForMutualScore(waitTransferNodes, orderReceiveIdsMap);
            // 如果存在总等级节点，总等级节点需要正常处理接收者（复制节点）
            if (existsTotalLevelWaitSubmit) {
                List<EmpEvalScorerNode> totalLevelNodes = waitTransferNodes.stream()
                    .filter(node -> SubScoreNodeEnum.isTotalLevelScore(node.getScorerType()))
                    .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(totalLevelNodes)) {
                    log.info("存在总等级节点，总等级节点需要正常处理接收者（复制节点）");
                    processReceivers(skipScorerId, receiveScorerIds, totalLevelNodes);
                }
            }
        } else {
            // 上级评、指定评等场景，需要正常处理接收者（复制节点）
            processReceivers(skipScorerId, receiveScorerIds, waitTransferNodes);
        }
        log.info("------执行跳过责任人结束！-------needSubmits.siz():{}", needSubmits.size());

        // 6. 标记跳过的节点的 transferTo（必须执行，用于标记跳过状态）
        // 对于同级评、下级评，transferTo 设置为特殊标识符（满足查询条件 transfer_to IS NOT NULL，但不表示真正的接收者）
        // 对于上级评、指定评，transferTo 包含接收者列表（需要转移节点）
        List<EmpEvalScorerNode> skipSnWaitSubmits = scorer.markSkipForSN(waitTransferNodes, orderReceiveIdsMap);
        this.needSubmits.addAll(skipSnWaitSubmits);

        // 7. 更新评审人和日志
        this.removeReviewers(skipScorerId);
        this.addLog(skipScorerId, skipScoreType);
        log.info("------执行跳过责任人结束！-------needSubmits:{}", JSONUtil.toJsonStr(needSubmits));
        this.fromCenterMsgs = msgCentersWrap.groupGet(skipScorerId);
    }

    public List<CompanyMsgCenter> getMsgCenters() {
        //存在待提交的节点，不允许清除待办
        boolean existsWaitSubmit = existsWaitSubmit();
        if (existsWaitSubmit) {
            return new ArrayList<>();
        } else {
            return fromCenterMsgs;
        }
    }

    public boolean existsWaitSubmit() {
        return scorer.existsWaitSubmit();
    }

    /**
     * 处理接收者
     *
     * @param skipScorerId 要跳过的评分人ID
     * @param receiveScorerIds 接收者ID集合
     */
    private void processReceivers(String skipScorerId, Set<String> receiveScorerIds, List<EmpEvalScorerNode> waitTransferNodes) {
        for (String scorerId : receiveScorerIds) {
            // 如果当前接收者是被跳过的评分人，则跳过
            if (StrUtil.equals(skipScorerId, scorerId)) {
                log.info("当前接收者是被跳过的评分人，无需处理: scorerId={}", scorerId);
                continue;
            }

            // 获取接收者
            EmpEvalScorer receiver = scorersWrap.getCurEvalScorer(scorerId);

            // 接收者接收节点
            List<EmpEvalScorerNode> submitNodes = receiver.receiveNodesForSkip(waitTransferNodes);
            this.needSubmits.addAll(submitNodes);
            receivers.add(receiver);
        }
    }

    private void checkErrorScorerNode(List<EmpEvalScorerNode> scorerNodes) {
        if (CollUtil.isEmpty(scorerNodes)) {
            throw new KpiI18NException("task.unableSkip", "无可跳过记录");
        }
    }

    private void checkErrorReceiverScorer(Set<String> scorerIds) {
        if (CollUtil.isEmpty(scorerIds)) {
            throw new KpiI18NException("node.notScorer", "不支持跳过，无可跳过的节点！");
        }
    }

    /**
     * 为同级评、下级评设置跳过标识符
     * 同级评、下级评默认会签，跳过时仅跳过本人，无需转移节点给其他人
     * 注意：总等级节点不在此处理范围内，保持原有逻辑
     */
    private void setSkipMarkerForMutualScore(List<EmpEvalScorerNode> waitTransferNodes,
                                             Map<Integer, List<String>> orderReceiveIdsMap) {
        log.info("同级评/下级评存在多人，跳过节点复制，设置特殊标识符");
        for (EmpEvalScorerNode node : waitTransferNodes) {
            // 只处理同级评、下级评的节点，总等级节点保持原有逻辑
            if (SubScoreNodeEnum.mutualScoreScene().contains(node.getScorerType())) {
                orderReceiveIdsMap.put(node.getApprovalOrder(),
                    Collections.singletonList(SKIP_NO_TRANSFER_MARKER));
            }
        }
    }


//
//    private void listReceives(String skipScorerId,List<EmpEvalScorerNode> waitTansferNodes,
//                              Map<String,List<Integer>>  receiveScorerOrderMap,Map<Integer,List<EmpEvalScorer>> receivesMap ){
//        //allScorers 寻找接收者scorer receivers    // 同级 多个(或|会)。同级 1个 (或|会) -- .最后一个节点
//        for (EmpEvalScorerNode waitTransferNode : waitTansferNodes) {
//            //查询当前环节的接受人
//            List<EmpEvalScorer> receives = receives(skipScorerId,waitTransferNode);
//            if (CollUtil.isEmpty(receives)){
//                return;
//            }
//            for (EmpEvalScorer temp : receives) {
//                List<Integer> orders = new ArrayList<>();
//                if (receiveScorerOrderMap.containsKey(temp.getScorerId())){
//                    orders = receiveScorerOrderMap.get(temp.getScorerId());
//                }
//                orders.add(waitTransferNode.getApprovalOrder());
//                receiveScorerOrderMap.put(temp.getScorerId(),orders);
//            }
//            receivesMap.put(waitTransferNode.getApprovalOrder(),receives);
//        }
//    }

    //listScorerExcludeScorerIdAndOrder


    private void listReceives(String skipScorerId, List<EmpEvalScorerNode> waitTansferNodes, Map<String, List<Integer>> receiveScorerOrderMap,
                              Map<Integer, List<String>> orderReceiveIdsMap,boolean existsTotalLevelWaitSubmit) {
        Set<String> haveReceiveScorerTypes = new HashSet<>();
        //allScorers 寻找接收者scorer receivers    // 同级 多个(或|会)。同级 1个 (或|会) 【指标未全部包含剔除接收者】-- .最后一个节点
        for (EmpEvalScorerNode waitTransferNode : waitTansferNodes) {
            //查询当前环节的接受人
            List<EmpEvalScorer> receives = receives(skipScorerId, waitTransferNode);
            if (CollUtil.isEmpty(receives)) {
                return;
            }
            haveReceiveScorerTypes.add(waitTransferNode.getScorerType());
            Set<String> scorerIds = new HashSet<>();
            for (EmpEvalScorer temp : receives) {
                scorerIds.add(temp.getScorerId());
                List<Integer> orders = new ArrayList<>();
                if (receiveScorerOrderMap.containsKey(temp.getScorerId())) {
                    orders = receiveScorerOrderMap.get(temp.getScorerId());
                }
                orders.add(waitTransferNode.getApprovalOrder());
                receiveScorerOrderMap.put(temp.getScorerId(), orders);
            }

            orderReceiveIdsMap.put(waitTransferNode.getApprovalOrder(), new ArrayList<>(scorerIds));
        }
        //存在打总等级合并提交，需要跳过总等级，必须有总等级接收人，不然不允许跳过
        if (existsTotalLevelWaitSubmit && !haveReceiveScorerTypes.contains(SubScoreNodeEnum.TOTAL_LEVEL.getScene())){
            log.warn("该考核任务存在评价总等级合并提交，不允许跳过！！！");
            throw new KpiI18NException("task.unableSkip", "该考核任务存在评价总等级合并提交，不允许跳过！！！");
        }
    }

    private List<EmpEvalScorer> receives(String skipScorerId, EmpEvalScorerNode waitTransferNode) {
        //查询当前环节及层级 的其他评分人  //receives 存在多个 接收者为当前环节的责任人
        List<EmpEvalScorer> receives = this.scorersWrap.listReceiveExcludeScorerIdAndOrder(skipScorerId, waitTransferNode.getScorerType(), waitTransferNode.getApprovalOrder());//寻找当前节点的评分人
        if (CollUtil.isNotEmpty(receives)) {
            //需要针对筛选出来的接受人receives 通过scorerType order两个条件匹配, 过滤掉 指标未全部包含waitTansferNode 的指标的节点接受人
            removeNoContainAllTransferKpiItemsOfReceivers(waitTransferNode, receives);
        }
        if (CollUtil.isEmpty(receives)) { //es 为空 寻找当前环节层级的其他节点
            List<EmpEvalScorer> otherOrderNodeScorers = this.scorersWrap.listReceiveExcludeScoreTypeAndOrder(waitTransferNode.getScorerType(), waitTransferNode.getApprovalOrder());
            receives.addAll(otherOrderNodeScorers);
        }
        return receives;
    }

    private void removeNoContainAllTransferKpiItemsOfReceivers(EmpEvalScorerNode waitTransferNode,List<EmpEvalScorer> receives) {
        //需要针对筛选出来的接受人receives 通过scorerType order两个条件匹配, 过滤掉 指标未全部包含waitTansferNode 的指标的节点接受人
        Set<String> kpiItemKeys = waitTransferNode.kpiItemKeys();
        Iterator<EmpEvalScorer> iterator = receives.iterator();
        while(iterator.hasNext()){
            EmpEvalScorer scorer = iterator.next();
            boolean shouldRemove = true; // 默认移除，除非找到完全包含的节点
            for(EmpEvalScorerNode node : scorer.getScorerNodes()){
                if(node.needExcludeNode()){//排除不符合的节点
                    continue;
                }
                if(node.match(waitTransferNode.getScorerType(), waitTransferNode.getApprovalOrder())){
                    Set<String> scorerKpiItemKeys = node.kpiItemKeys();
                    // 如果该节点完全包含待转移节点的所有指标，则保留该评分人
                    if(scorerKpiItemKeys.containsAll(kpiItemKeys)){
                        shouldRemove = false;
                        break;
                    }
                }
            }
            if(shouldRemove){
                iterator.remove();
            }
        }
    }

    private void removeReviewers(String skipScorerId) {
        this.evalUser.removeReviewers(Collections.singletonList(skipScorerId));
    }

    private void addLog(String skipScorerId, String skipScorerType) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("empName", this.getEmp(skipScorerId).getEmpName());
        jsonObject.put("scorerType", Collections.singletonList(skipScorerType));
        this.operationLog = new OperationLog(tenantId.getId(), evalUser.getId(), "skip_task", opEmpId, jsonObject.toJSONString());
    }

    public KpiEmp getEmp(String empId) {
        return raterGroup.mapGet(empId);
    }

}

