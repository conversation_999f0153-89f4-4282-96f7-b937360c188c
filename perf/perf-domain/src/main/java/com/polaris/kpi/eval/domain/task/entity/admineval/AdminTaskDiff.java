package com.polaris.kpi.eval.domain.task.entity.admineval;

import cn.hutool.core.collection.CollUtil;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.*;
import com.polaris.kpi.eval.domain.task.type.InnerFields;
import com.polaris.sdk.type.AuditEnum;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Author: lufei
 * @CreateTime: 2022-09-11  12:11
 * @Version: 1.0
 */
@Getter
public class AdminTaskDiff {
    private AdminTask before;
    private AdminTask after;
    private List<EvalRuleLogField> scoreConfFields = new ArrayList<>();

    private List<EvalRuleOpLogMeta> affirmTask = new ArrayList<>();
    private List<EvalRuleOpLogMeta> editExeIndi = new ArrayList<>();
    private List<EvalRuleOpLogMeta> inputNotify = new ArrayList<>();
    private List<EvalRuleOpLogMeta> finishValueAudit = new ArrayList<>();

    private List<EvalRuleOpLogMeta> auditResult = new ArrayList<>();
    private List<EvalRuleOpLogMeta> interviewConf = new ArrayList<>();
    private List<EvalRuleOpLogMeta> confirmResult = new ArrayList<>();
    private List<EvalRuleOpLogMeta> publishResult = new ArrayList<>();
    private List<EvalRuleOpLogMeta> appealConf = new ArrayList<>();
    private List<EvalRuleOpLogMeta> deadLineConf = new ArrayList<>();

    private List<EvalRuleOpLogMeta> enterScore = new ArrayList<>();
    private List<EvalRuleOpLogMeta> scoreConf = new ArrayList<>();//enterScore,scoreSortConf,commentConf,scoreConf 四部分
    private List<EvalRuleOpLogMeta> scoreScoreConf = new ArrayList<>();
    private List<EvalRuleOpLogMeta> scoreView = new ArrayList<>();
    private List<EvalRuleOpLogMeta> commentConf = new ArrayList<>();
    public AdminTaskDiff(AdminTask before, AdminTask after) {
        this.before = before;
        this.after = after;
    }

    public AdminTaskOperation doCompareDiff(String resetEmpName,String autoValidEmpName) {

        AdminTaskOperation operation = new AdminTaskOperation(before.getId(), before.getCompanyId().getId());
        compare(before.getConfirmTask(), after.getConfirmTask());
        compare(before.getEditExeIndi(), after.getEditExeIndi());
        compare(before.getInputNotifyConf(), after.getInputNotifyConf());
        compare(before.getFinishValueAudit(), after.getFinishValueAudit());
        compare(before.getEnterScore(), after.getEnterScore());
        compare(before.getScoreConf(), after.getScoreConf());
        compare(before.getCommentConf(), after.getCommentConf());
        compare(before.getCommentReqConf(), after.getCommentReqConf());
        compare(before.getScoreSortConf(), after.getScoreSortConf());
        compare(before.getScoreView(), after.getScoreView());

        compare(before.getAuditResult(), after.getAuditResult());
        compare(before.getConfirmResult(), after.getConfirmResult());
        compare(before.getPublishResult(), after.getPublishResult());
        compare(before.getAppealConf(), after.getAppealConf());
        compare(before.getInterviewConf(), after.getInterviewConf());

        compare(before.getDeadLineConf(), after.getDeadLineConf());
        compare(before.getDeadLineConf().getTaskAffirmDeadLine(), after.getDeadLineConf().getTaskAffirmDeadLine(), "修改指标确认和审核截止时间");
        compare(before.getDeadLineConf().getTaskConfirmDeadLine(), after.getDeadLineConf().getTaskConfirmDeadLine(), "修改执行中截止时间");
        compare(before.getDeadLineConf().getFinishInputAudit(), after.getDeadLineConf().getFinishInputAudit(), "修改完成值审核截止时间");
        compare(before.getDeadLineConf().getTaskEvalScore(), after.getDeadLineConf().getTaskEvalScore(), "修改绩效评价截止时间");
        compare(before.getDeadLineConf().getTaskResultAudit(), after.getDeadLineConf().getTaskResultAudit(), "修改结果校准截止时间");
        compare(before.getDeadLineConf().getTaskResultInterview(), after.getDeadLineConf().getTaskResultInterview(), "修改结果面谈截止时间");
        compare(before.getDeadLineConf().getTaskResultConfirm(), after.getDeadLineConf().getTaskResultConfirm(), "修改结果确认截止时间");


        operation.setBaseInfo(before.compareBase(after));
        operation.setConfirmTask(affirmTask, resetEmpName,autoValidEmpName);
        operation.setEditExeIndi(editExeIndi, resetEmpName,autoValidEmpName);
        operation.setInputNotify(inputNotify);      //录入完成值通知无需重置
        operation.setFinishValueAuditConf(finishValueAudit,resetEmpName,autoValidEmpName);
        operation.setScoreConf(scoreConf, resetEmpName,autoValidEmpName);
        operation.setScoreSortConf(scoreScoreConf, resetEmpName,autoValidEmpName);
        operation.setScoreView(scoreView);

        operation.setAuditResult(auditResult, resetEmpName,autoValidEmpName);
        operation.setInterviewConf(interviewConf, resetEmpName,autoValidEmpName);
        operation.setConfirmResult(confirmResult, resetEmpName,autoValidEmpName);
        operation.setPublishResult(publishResult);
        operation.setAppealConf(appealConf);
        operation.setDeadLineConf(deadLineConf);
        ///操作类型:修改基本信息=01=1,修改考核规则=10=2,修改考核任务管理权限=100=4,增加考核人员=1000=8,删除考核人员=10000=16,终止考核任务=100000=32
        if (!affirmTask.isEmpty() || !editExeIndi.isEmpty() || !finishValueAudit.isEmpty() || !scoreConf.isEmpty() || !scoreView.isEmpty() || !auditResult.isEmpty()
               || !interviewConf.isEmpty() || !confirmResult.isEmpty() || !publishResult.isEmpty() || !appealConf.isEmpty() || !deadLineConf.isEmpty() || !inputNotify.isEmpty()) {
            operation.ruleChanged();
        }
        operation.setOperationTime(System.currentTimeMillis());
        return operation;
    }

    public List<String> getChangeScene() {
        List<String> scenes = new ArrayList<>();
        if (!affirmTask.isEmpty()) {
            scenes.add(AuditEnum.CONFIRM_TASK.getScene());
        }
        if (!editExeIndi.isEmpty()) {
            scenes.add(AuditEnum.EDIT_EXE_INDI.getScene());
        }
        if (!finishValueAudit.isEmpty()) {
            scenes.add(AuditEnum.FINISH_VALUE_AUDIT.getScene());
        }
        if (!scoreConf.isEmpty()) {
            scenes.addAll(AuditEnum.allScore());
        }
        if (!auditResult.isEmpty()) {
            scenes.add(AuditEnum.FINAL_RESULT_AUDIT.getScene());
        }
        if (!interviewConf.isEmpty()) {
            scenes.add(AuditEnum.FINAL_RESULT_INTERVIEW.getScene());
        }
        if (!confirmResult.isEmpty()) {
            scenes.add(AuditEnum.PERF_RESULTS_AFFIRM.getScene());
        }
        if (!publishResult.isEmpty()) {
            scenes.add(AuditEnum.RESULT_PUBLISH.getScene());
        }
        if (!appealConf.isEmpty()) {
            scenes.add(AuditEnum.RESULT_APPEAL.getScene());
        }
        return scenes;
    }

    public void addLogMetaIf(List<EvalRuleOpLogMeta> metas, EvalRuleOpLogMeta logMeta) {
        if (logMeta != null) {
            metas.add(logMeta);
        }
    }

    public void compare(ScoreValueConf before, ScoreValueConf after) {
        if (after == null) {
            return;
        }
        InnerFields<EvalRuleLogField> fields = before.compare(after);
        if (fields.isNotEmpty()) {
            scoreConfFields.addAll(fields.getFields());
        }
        if (!scoreConfFields.isEmpty()) {
            EvalRuleOpLogMeta logMeta = new EvalRuleOpLogMeta("修改考核设置", "");
            logMeta.setFields(scoreConfFields);
        }
    }

    //确认任务指标
    public void compare(AffirmTaskConf before, AffirmTaskConf after) {
        if (after == null) {
            return;
        }
        affirmTask.addAll(before.compare(after));
    }

    //执行中
    public void compare(EditExeIndiConf before, EditExeIndiConf after) {
        if (after == null) {
            return;
        }
        editExeIndi.addAll(before.compare(after));
    }

    //完成值录入通知
    public void compare(InputNotifyConf before, InputNotifyConf after) {
        if (after == null) {
            return;
        }
        inputNotify.addAll(before.compare(after));
    }

    //录入值审核
    public void compare(FinishValueAuditConf before, FinishValueAuditConf after) {
        if (after == null) {
            return;
        }
        finishValueAudit.addAll(before.compare(after));
    }

    //结果校准
    public void compare(AuditResultConf before, AuditResultConf after) {
        if (after == null) {
            return;
        }
        EvalRuleOpLogMeta compare = before.compare(after);
        if (compare != null && CollUtil.isNotEmpty(compare.getFields())) {
            auditResult.add(compare);
        }
    }

    //结果面谈
    public void compare(InterviewConf before, InterviewConf after) {
        if (after == null) {
            return;
        }
        if (Objects.isNull(before)) {
            before = new InterviewConf(0);
        }
        List<EvalRuleOpLogMeta> compare = before.compare(after);
        interviewConf.addAll(compare);
    }


    //结果确认
    public void compare(ConfirmResultConf before, ConfirmResultConf after) {
        if (after == null) {
            return;
        }
        List<EvalRuleOpLogMeta> compare = before.compare(after);
        confirmResult.addAll(compare);
    }
    //结果公示
    public void compare(PublishResultConf before, PublishResultConf after) {
        if (after == null) {
            return;
        }
        List<EvalRuleOpLogMeta> compare = before.compare(after);
        publishResult.addAll(compare);
    }
    //结果申诉
    public void compare(AppealConf before, AppealConf after) {
        if (after == null) {
            return;
        }
        List<EvalRuleOpLogMeta> compare = before.compare(after);
        appealConf.addAll(compare);
    }

    //阶段截止时间 各环节配置
    public void compare(TaskDeadLine before, TaskDeadLine after, String modifyStage) {
        if (before == null || after == null) {
            return;
        }
        List<EvalRuleOpLogMeta> compare = before.compare(after, modifyStage);
        deadLineConf.addAll(compare);
    }

    //阶段截止时间
    public void compare(DeadLineConf before, DeadLineConf after) {
        if (after == null) {
            return;
        }
        List<EvalRuleOpLogMeta> compare = before.compare(after);
        deadLineConf.addAll(compare);
    }

    //评分设置:进入评分配置
    public void compare(EnterScoreConf before, EnterScoreConf after) {
        if (after == null) {
            return;
        }
        EvalRuleOpLogMeta compare = before.compare(after);
        addLogMetaIf(scoreConf, compare);
    }
    //评分设置:评分环节顺序配置
    public void compare(ScoreSortConf before, ScoreSortConf after) {
        if (after == null) {
            return;
        }
        EvalRuleOpLogMeta compare = before.compare(after);
        addLogMetaIf(scoreConf, compare);
    }
    //评分设置:或签/会签、是否可转交
    public void compare(ScoreConf before, ScoreConf after) {
        if (after == null) {
            return;
        }
        EvalRuleOpLogMeta compare = before.compare(after);
        addLogMetaIf(scoreConf, compare);
    }
    //评分设置:评分内容查看权限
    public void compare(ScoreViewConf before, ScoreViewConf after) {
        if (after == null) {
            return;
        }
        EvalRuleOpLogMeta compare = before.compare(after);
        addLogMetaIf(scoreView, compare);
    }
    //评分设置:评语与总结
    public void compare(ScoreCommentConf before, ScoreCommentConf after) {
        if (after == null) {
            return;
        }
        EvalRuleOpLogMeta compare = before.compare(after);
        addLogMetaIf(scoreConf, compare);
    }

    //自定义评语评分设置
    public void compare(CommentReqConf before, CommentReqConf after){
        if (after == null){
            return;
        }
        EvalRuleOpLogMeta compare = before.compare(after);
        addLogMetaIf(scoreConf, compare);
    }

    public boolean hasChange(){
        //任务评分查看权限、结果确认、公示配置、申诉配置发生变更 任务下所有员工任务直接同步变更
        if (CollUtil.isNotEmpty(scoreView) || CollUtil.isNotEmpty(publishResult) || CollUtil.isNotEmpty(appealConf)) {
            return true;
        }
        return false;
    }

}
