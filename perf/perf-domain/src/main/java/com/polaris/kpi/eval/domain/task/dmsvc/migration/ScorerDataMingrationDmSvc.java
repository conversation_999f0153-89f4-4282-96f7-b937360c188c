package com.polaris.kpi.eval.domain.task.dmsvc.migration;

import cn.com.polaris.kpi.KpiEmp;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.polaris.kpi.eval.domain.task.dmsvc.ExplainEvalScorerDmSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.SumScorerComputeDmSvc;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.*;
import com.polaris.kpi.eval.domain.task.type.EvalScoreSummary;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.sdk.type.ListWrap;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.lufei.ibatis.common.data.ToDataBuilder;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import java.util.UUID;
import java.util.Optional;
import java.util.HashSet;
import java.util.Set;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.List;
import java.util.Date;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.domain.task.dmsvc
 * @Author: suxiaoqiu
 * @CreateTime: 2025-03-05  09:22
 * @Description: 评分人数据迁移领域服务
 * @Version: 2.0
 */
@Slf4j
@Getter
public class ScorerDataMingrationDmSvc {
    private final EvalUser taskUser;// 评分任务用户
    private final EmpEvalMerge evalMerge;
    private final List<BaseScoreResult> totalRs; //打总等级
    private final List<PerfEvalTypeResult> typeResultOfUser; //维度评价
    private List<EvalScoreResult> empScoreRs; //指标评分
    private final List<EvalScoreSummary> summarys; //指标评分
    private List<EmpEvalScorer> empEvalScorers = new ArrayList<>();//评分人

    private final Map<String, EvalKpi> kpiMap = new HashMap<>();
    private final Map<String, EmpEvalKpiType> kpiTypeMap = new HashMap<>();

    private final List<EvalScorerNodeKpiItem> scorerNodeKpiItems = new ArrayList<>();//汇总所有评价指标

    private final List<EmpEvalScorerNode> customScorerNodes = new ArrayList<>();//自定义指标流程评分人节点
    private String superiorScoreOrder;//上级依次还是同时

    private final EvalScorersWrap existsScorersWrap;//数据库已有的评价关系
    public ScorerDataMingrationDmSvc(EvalUser taskUser, EmpEvalMerge evalMerge,List<EvalScoreResult> empScoreRs,
                                     List<PerfEvalTypeResult> typeResultOfUser,List<BaseScoreResult> totalRs, List<EvalScoreSummary> summarys,
                                     EvalScorersWrap scorersWrap ) {
        this.taskUser = taskUser;
        this.evalMerge = evalMerge;
        this.empScoreRs = empScoreRs;
        this.typeResultOfUser = typeResultOfUser;
        this.totalRs = totalRs;
        this.summarys = summarys;
        if (!evalMerge.getKpiTypes().isEmpty()) {
            evalMerge.initTypeWeight();//init 维度权重
            this.initKpiMap(evalMerge);
        }
        this.existsScorersWrap = scorersWrap;
    }

    private void initKpiMap(EmpEvalMerge evalMerge){
        for (EvalKpi kpi : taskUser.getKpis()) {
            this.kpiMap.put(kpi.asKpiItemKey(), kpi);
        }
        for (EmpEvalKpiType type : evalMerge.getKpiTypes().getDatas()) {
            this.kpiTypeMap.put(type.getKpiTypeId(), type);
        }
    }

    public boolean isNoNeedUp() {
        //评价之前，是可以更新的
        if (TalentStatus.isBefore(this.taskUser.getTaskStatus(), TalentStatus.SCORING.getStatus())) {
            log.info(" -----评价之前，则需更新------taskUserID:{}", this.taskUser.getId());
            return false;
        }

        //评价中，评价环节后
        if (CollUtil.isEmpty(this.empScoreRs)
                && CollUtil.isEmpty(this.typeResultOfUser)
                && CollUtil.isEmpty(this.totalRs)
                && !existsScorersWrap.isEmpty()) {
            log.info(" -----无原来的评价关系,且无自动计算分，则无需更新------taskUserID:{}", this.taskUser.getId());
            return true;
        }
        if (!existsScorersWrap.isEmpty() && existsScorersWrap.isVersionChange()) {
            log.info(" -----评价关系版本号变化，则无需更新------taskUserID:{}", this.taskUser.getId());
            return true;
        }
        return false;
    }

    /**
     * 未考核完成的任务
     */
    public void handlerTaskNoFinished() {
        if(evalMerge.getKpiTypes().isEmpty()){
            log.warn(" -----handlerTaskNoFinished该考核无维度、指标------taskUserID:{}", this.taskUser.getId());
            return;
        }
        this.parserScorerNodeByRule();//解析评分人
        //  计算自动指标分
        log.info("开始自动评分 taskUserId:{}", taskUser.getId());
        boolean isTypeWeightOpen = evalMerge.isTypeWeightOpen();
        BigDecimal fullScore = evalMerge.getScoreValueConf().getCustomFullScore();
        boolean computed = !evalMerge.inputOnScoring() && taskUser.tryComputeAutoScore(isTypeWeightOpen, fullScore, evalMerge.getScoreValueConf().isFullScoreRange(), evalMerge.openItemAutoScoreMultiplWeight());
        log.info("自动评分 computed:{},autoScore:{}",computed,taskUser.getFinalItemAutoScore());
        this.fillAutoScoreForEvalKpi(computed);//填充计算后的指标得分
        this.evalMerge.accpEvalScorersWrap(this.empEvalScorers);
        if (TalentStatus.isBefore(this.taskUser.getTaskStatus(), TalentStatus.SCORING.getStatus())) {
            return;
        }
        //合并分发，已提交评分的scoreResult
        if (CollUtil.isEmpty(this.empScoreRs)
                && CollUtil.isEmpty(this.typeResultOfUser)
                && CollUtil.isEmpty(this.totalRs)) {
            log.info(" ------未进入评分中------taskUserID:{}", this.taskUser.getId());
            return;
        }

        this.filterRepeatData();//去重 指标评分
        this.corventScorerRs(); //如果有已完成的任务直接转换新的评分人结构，标注已完成
        this.evalMerge.accEvalScorerForItem();//给每个指标接收评分人信息
        SumScorerComputeDmSvc sumScorerComputeDmSvc = new SumScorerComputeDmSvc(taskUser, evalMerge);
        //计算环节得分
        sumScorerComputeDmSvc.computeSumMigrationScore();
    }

    public void  filterRepeatData(){
        List<EvalScoreResult> filteredResults = new ArrayList<>();
        for (EvalScoreResult scoreResult : empScoreRs) {
            if (StrUtil.isEmpty(scoreResult.getOrgId())){
                filteredResults.add(scoreResult);
                continue;
            }
            if (StrUtil.equals(scoreResult.getOrgId(), taskUser.getOrgId())) {
                filteredResults.add(scoreResult);
            }
        }
        this.empScoreRs = filteredResults;
    }

    /**
     * 填充自动计算的指标得分评分到评价指标
     *
     * @param computed 是否已计算
     */
    private void fillAutoScoreForEvalKpi( boolean computed) {
        if (!computed) {
            return;
        }
        List<EvalKpi> autoItems = taskUser.autoItems();
        if (CollUtil.isEmpty(autoItems)) {
            return;
        }
        ListWrap<EvalKpi> kpis = new ListWrap<>(autoItems).asMap(EvalKpi::getId);
        for (EvalKpi item : evalMerge.allItems()) {
            EvalKpi autoKpi = kpis.mapGet(item.getId());
            if (Objects.isNull(autoKpi)) {
                continue;
            }
            // 添加数据范围校验
            item.setItemScore(validateScore(autoKpi.getItemScore()));
            item.setItemOriginalScore(validateScore(autoKpi.getItemOriginalScore()));
        }
    }
    private static final Date REFERENCE_DATE = Date.from(
            LocalDate.of(2025, 6, 1).atStartOfDay(ZoneId.systemDefault()).toInstant());


    private BigDecimal validateScore(BigDecimal score) {
        if (score != null) {
            // 根据数据库字段定义调整范围
            if (score.compareTo(new BigDecimal("999999.99")) > 0) {
                return new BigDecimal("999999.99"); // 或者抛出异常
            }
            if (score.compareTo(new BigDecimal("-999999.99")) < 0) {
                return new BigDecimal("-999999.99");
            }
        }
        return score;
    }

    /**
     *  已完成的任务
     */
    public void handlerTaskFinished() {
        if(evalMerge.getKpiTypes().isEmpty()){
            log.warn(" -----handlerTaskFinished该考核无维度、指标------taskUserID:{}", this.taskUser.getId());
            return;
        }
        this.filterRepeatData();//去重 指标评分
        //taskUser 创建时间为2025-06-01之前的评分任务，已完成的任务使用result生成评价关系
        if (taskUser.getCreatedTime() != null && taskUser.getCreatedTime().before(REFERENCE_DATE)) {
            this.parserFinishedScorerNodeByRule();//解析评分人
        }else{
            this.parserScorerNodeByRule();
        }
        //  计算自动指标分
        log.info("开始自动评分 taskUserId:{}", taskUser.getId());
        boolean isTypeWeightOpen = evalMerge.isTypeWeightOpen();
        BigDecimal fullScore = evalMerge.getScoreValueConf().getCustomFullScore();
        boolean computed = !evalMerge.inputOnScoring() && taskUser.tryComputeAutoScore(isTypeWeightOpen, fullScore, evalMerge.getScoreValueConf().isFullScoreRange(), evalMerge.openItemAutoScoreMultiplWeight());
        log.info("自动评分 computed:{},autoScore:{}",computed,taskUser.getFinalItemAutoScore());
        this.fillAutoScoreForEvalKpi(computed);//填充计算后的指标得分
        evalMerge.accpEvalScorersWrap(empEvalScorers);
        //taskUser 创建时间为2025-06-01之前的评分任务，已完成的任务使用result生成评价关系
        if (taskUser.getCreatedTime() != null && taskUser.getCreatedTime().before(REFERENCE_DATE)) {
            this.covertFinishedScorerRs(); //已完成的任务直接转换新的评分人结构，标注已完成
        }else{
            this.corventScorerRs();
        }
        evalMerge.accEvalScorerForItem();//给每个指标接收评分人信息
        SumScorerComputeDmSvc sumScorerComputeDmSvc = new SumScorerComputeDmSvc(taskUser, evalMerge);
        //计算环节得分
        sumScorerComputeDmSvc.computeSumMigrationScore();
    }

    public void accScorerInfo(Map<String, KpiEmp> kpiMap){
        if (MapUtil.isEmpty(kpiMap) || CollUtil.isEmpty(empEvalScorers)){
            return;
        }

        //循环评分人empEvalScorers
        for (EmpEvalScorer empEvalScorer : empEvalScorers) {
            KpiEmp kpiEmp = kpiMap.get(empEvalScorer.getScorerId());
            if (Objects.isNull(kpiEmp)) {
                continue;
            }
            empEvalScorer.setScorerInfo(kpiEmp.getEmpName(), kpiEmp.getAvatar());
        }
    }

    //评分已完成的
    private void corventScorerRs() {
        if (evalMerge.getEvalScorersWrap().isEmpty()) {
            return;
        }

        evalMerge.getEvalScorersWrap().corventScorerRs(empScoreRs, typeResultOfUser, totalRs,summarys);//转换赋值
        evalMerge.getEvalScorersWrap().computeScorerNodeScore(evalMerge, taskUser);//计算
        evalMerge.getEvalScorersWrap().markOrModeScoreNsForMigration();//处理或签
        this.empEvalScorers = evalMerge.getEvalScorersWrap().getDatas();
    }


    //评分已完成的
    private void covertFinishedScorerRs() {
        if (CollUtil.isEmpty(empEvalScorers)) {
            return;
        }
        evalMerge.getEvalScorersWrap().computeScorerNodeScore(evalMerge, taskUser);//计算
        this.empEvalScorers = evalMerge.getEvalScorersWrap().getDatas();
    }

    public void parserScorerNodeByRule() {
        if (Objects.isNull(evalMerge)) {
            return;
        }
        EmpEvalRule evalRule = new ToDataBuilder<>(evalMerge, EmpEvalRule.class).data();
        ExplainEvalScorerDmSvc dmSvc = new ExplainEvalScorerDmSvc(taskUser, evalRule, taskUser.getCompanyId(), evalRule.getCreatedUser());
        dmSvc.explainEvalScorer();
        this.superiorScoreOrder = dmSvc.getRule().getSuperiorScoreOrder();
        this.empEvalScorers = dmSvc.getScorers().getDatas();
    }

    public void parserFinishedScorerNodeByRule() {
        if (Objects.isNull(evalMerge)) {
            return;
        }
        //解析出上级评是同时还是依次评
        this.evalMerge.setSuperiorScoreOrder();
        this.superiorScoreOrder =  this.evalMerge.getSuperiorScoreOrder();
        this.empEvalScorers = buildEmpEvalScorersFromScoreResults(empScoreRs, typeResultOfUser, totalRs, summarys);
    }

    public void applyVersionToScorers(Integer curVersion) {
        if (curVersion == null || this.empEvalScorers == null) {
            return;
        }
        for (EmpEvalScorer scorer : this.empEvalScorers) {
            scorer.setVersion(curVersion);
            if (scorer.getScorerNodes() == null) {
                continue;
            }
            for (EmpEvalScorerNode node : scorer.getScorerNodes()) {
                node.setVersion(curVersion);
                if (node.getScorerNodeKpiTypes() == null) {
                    continue;
                }
                for (EvalScorerNodeKpiType t : node.getScorerNodeKpiTypes()) {
                    if (t.getScorerNodeKpiItems() == null) {
                        continue;
                    }
                    for (EvalScorerNodeKpiItem it : t.getScorerNodeKpiItems()) {
                        it.setVersion(curVersion);
                    }
                }
            }
        }
    }

    /**
     * 从评分结果数据组装评分人对象结构
     */
    public List<EmpEvalScorer> buildEmpEvalScorersFromScoreResults(List<EvalScoreResult> empScoreRs, 
                                                                  List<PerfEvalTypeResult> typeResultOfUser,
                                                                  List<BaseScoreResult> totalRs,
                                                                  List<EvalScoreSummary> summarys) {
        // 处理转交逻辑
        List<EvalScoreResult> filteredEmpScoreRs = CollUtil.isEmpty(empScoreRs) ? 
                new ArrayList<>() : filterTransferredResults(empScoreRs);
        // 按评分人ID分组
        Map<String, List<EvalScoreResult>> empScoreGroupMap = filteredEmpScoreRs.stream()
                .collect(Collectors.groupingBy(EvalScoreResult::getScorerId));
        Map<String, List<PerfEvalTypeResult>> typeResultGroupMap = CollUtil.isEmpty(typeResultOfUser) ? 
                new HashMap<>() : typeResultOfUser.stream()
                .collect(Collectors.groupingBy(PerfEvalTypeResult::getScorerId));
        Map<String, List<BaseScoreResult>> totalRsGroupMap = CollUtil.isEmpty(totalRs) ? 
                new HashMap<>() : totalRs.stream()
                .collect(Collectors.groupingBy(BaseScoreResult::getScorerId));
        Map<String, List<EvalScoreSummary>> summaryGroupMap = CollUtil.isEmpty(summarys) ? 
                new HashMap<>() : summarys.stream()
                .collect(Collectors.groupingBy(EvalScoreSummary::getCreatedUser));
        
        // 获取所有评分人ID
        Set<String> allScorerIds = new HashSet<>();
        allScorerIds.addAll(empScoreGroupMap.keySet());
        allScorerIds.addAll(typeResultGroupMap.keySet());
        allScorerIds.addAll(totalRsGroupMap.keySet());
        allScorerIds.addAll(summaryGroupMap.keySet());
        
        List<EmpEvalScorer> empEvalScorers = new ArrayList<>();
        
        for (String scorerId : allScorerIds) {
            List<EvalScoreResult> scoreResults = empScoreGroupMap.getOrDefault(scorerId, new ArrayList<>());
            List<PerfEvalTypeResult> typeResults = typeResultGroupMap.getOrDefault(scorerId, new ArrayList<>());
            List<BaseScoreResult> totalResults = totalRsGroupMap.getOrDefault(scorerId, new ArrayList<>());
            List<EvalScoreSummary> scorerSummarys = summaryGroupMap.getOrDefault(scorerId, new ArrayList<>());
            
            // 创建评分人对象
            EmpEvalScorer empEvalScorer = createEmpEvalScorer(scorerId, scoreResults, typeResults, totalResults);
            
            if (empEvalScorer != null) {
                // 构建评分节点
                List<EmpEvalScorerNode> scorerNodes = buildScorerNodes(scoreResults, typeResults, totalResults, scorerSummarys);
                empEvalScorer.setScorerNodes(scorerNodes);
                empEvalScorer.upScorerStatus();
                empEvalScorers.add(empEvalScorer);
            }
        }
        
        return empEvalScorers;
    }

    /**
     * 处理转交逻辑：同一task_audit_id有多条数据时，优先选择transfer_id不为空的数据
     */
    private List<EvalScoreResult> filterTransferredResults(List<EvalScoreResult> empScoreRs) {
        if (CollUtil.isEmpty(empScoreRs)) {
            return new ArrayList<>();
        }
        empScoreRs.forEach(result -> {
            if (null == result.getApprovalOrder()) {
                result.setApprovalOrder(1);
            }
        });
        if(!taskUser.wasTempTask()){
            return empScoreRs;
        }

        List<EvalScoreResult> filteredResults = new ArrayList<>();
        List<EvalScoreResult> newEmpScoreRs = new ArrayList<>();
        empScoreRs.forEach(empScoreR -> {
            if (StrUtil.isBlank(empScoreR.getTaskAuditId())) {
                filteredResults.add(empScoreR);
            } else {
                newEmpScoreRs.add(empScoreR);
            }
        });

        // 过滤掉状态auditStatus为transferred的
        if (CollUtil.isEmpty(newEmpScoreRs)){
            return filteredResults;
        }

        newEmpScoreRs.stream().filter(newEmpScoreR -> !StrUtil.equals(newEmpScoreR.getAuditStatus(), "transferred")).forEach(filteredResults::add);
        return filteredResults;
    }

    /**
     * 创建单个评分人对象
     */
    private EmpEvalScorer createEmpEvalScorer(String scorerId, List<EvalScoreResult> scoreResults, 
                                             List<PerfEvalTypeResult> typeResults, List<BaseScoreResult> totalResults) {
        // 获取基础信息
        EvalScoreResult baseResult = CollUtil.isNotEmpty(scoreResults) ? scoreResults.get(0) : null;
        PerfEvalTypeResult baseTypeResult = CollUtil.isNotEmpty(typeResults) ? typeResults.get(0) : null;
        BaseScoreResult baseTotalResult = CollUtil.isNotEmpty(totalResults) ? totalResults.get(0) : null;
        
        if (baseResult == null && baseTypeResult == null && baseTotalResult == null) {
            return null;
        }
        
        // 创建评分人对象
        EmpEvalScorer empEvalScorer;
        if (baseResult != null) {
            empEvalScorer = new EmpEvalScorer(
                    taskUser.getCompanyId().getId(),
                    taskUser.getCreatedUser(),
                    baseResult.getTaskId(),
                    baseResult.getEmpId(),
                    baseResult.getTaskUserId(),
                    scorerId,
                    baseResult.getScorerName(),
                    null // 头像信息需要从其他地方获取
            );
        } else if (baseTypeResult != null) {
            empEvalScorer = new EmpEvalScorer(
                    taskUser.getCompanyId().getId(),
                    taskUser.getCreatedUser(),
                    taskUser.getTaskId(),
                    taskUser.getEmpId(),
                    baseTypeResult.getTaskUserId(),
                    scorerId,
                    baseTypeResult.getScorerName(),
                    null
            );
        } else {
            empEvalScorer = new EmpEvalScorer(
                    taskUser.getCompanyId().getId(),
                    taskUser.getCreatedUser(),
                    taskUser.getTaskId(),
                    taskUser.getEmpId(),
                    baseTotalResult.getTaskUserId(),
                    scorerId,
                    baseTotalResult.getScorerName(),
                    null
            );
        }
        
        return empEvalScorer;
    }

    /**
     * 构建评分节点列表
     */
    private List<EmpEvalScorerNode> buildScorerNodes(List<EvalScoreResult> scoreResults, 
                                                    List<PerfEvalTypeResult> typeResults, 
                                                    List<BaseScoreResult> totalResults,
                                                    List<EvalScoreSummary> summarys) {
        Map<String, EmpEvalScorerNode> nodeMap = new HashMap<>();
        
        // 处理指标评分结果
        if (CollUtil.isNotEmpty(scoreResults)) {
            processScoreResults(scoreResults, nodeMap);
        }
        
        // 处理维度评价结果
        if (CollUtil.isNotEmpty(typeResults)) {
            processTypeResults(typeResults, nodeMap);
        }
        
        // 处理总分评价结果（修正：不需要按order分组）
        if (CollUtil.isNotEmpty(totalResults)) {
            processTotalResults(totalResults, nodeMap);
        }
        
        // 处理评价总结
        if (CollUtil.isNotEmpty(summarys)) {
            processSummarys(summarys, nodeMap);
        }

        for (EmpEvalScorerNode value : nodeMap.values()) {
            value.upFinishedStatus();
        }
        return new ArrayList<>(nodeMap.values());
    }

    /**
     * 处理指标评分结果
     */
    private void processScoreResults(List<EvalScoreResult> scoreResults, Map<String, EmpEvalScorerNode> nodeMap) {
        // 分离total_前缀的评分结果
        List<EvalScoreResult> totalScoreResults = scoreResults.stream()
                .filter(result -> result.getScorerType().startsWith("total_"))
                .collect(Collectors.toList());
        
        List<EvalScoreResult> normalScoreResults = scoreResults.stream()
                .filter(result -> !result.getScorerType().startsWith("total_"))
                .collect(Collectors.toList());
        
        // 处理普通指标评分结果
        if (CollUtil.isNotEmpty(normalScoreResults)) {
            Map<String, List<EvalScoreResult>> scoreTypeGroupMap = normalScoreResults.stream()
                    .collect(Collectors.groupingBy(EvalScoreResult::asOrderAndScoreTypeKey));
            
            for (Map.Entry<String, List<EvalScoreResult>> entry : scoreTypeGroupMap.entrySet()) {
                List<EvalScoreResult> typeResults = entry.getValue();
                EvalScoreResult one = typeResults.get(0);
                String scorerType = one.getScorerType();
                Integer approvalOrder =  one.getApprovalOrder();
                EmpEvalScorerNode scorerNode = nodeMap.computeIfAbsent(entry.getKey(),
                        k -> EmpEvalScorerNode.createFromResult(scorerType, taskUser.getTaskId(),taskUser.getEmpId(), one));

                // 添加指标评分数据到节点
                addScoreResultsToNode(scorerNode, typeResults);
                
                // 处理对应的total_前缀评分结果
                String totalScorerType = "total_" + scorerType;
                List<EvalScoreResult> correspondingTotalResults = totalScoreResults.stream()
                        .filter(result -> result.getScorerType().equals(totalScorerType) && 
                                        result.getApprovalOrder().equals(approvalOrder))
                        .collect(Collectors.toList());
                
                if (CollUtil.isNotEmpty(correspondingTotalResults)) {
                    scorerNode.setRateMode("total");
                    scorerNode.accpScorerTotalScore(correspondingTotalResults.get(0));
                }
            }
        }
        
        // 处理独立的total_前缀评分结果
        if (CollUtil.isNotEmpty(totalScoreResults)) {
            Map<String, List<EvalScoreResult>> totalScoreTypeGroupMap = totalScoreResults.stream()
                    .collect(Collectors.groupingBy(EvalScoreResult::asOrderAndScoreTypeKey));
            
            for (Map.Entry<String, List<EvalScoreResult>> entry : totalScoreTypeGroupMap.entrySet()) {
                List<EvalScoreResult> typeResults = entry.getValue();
                EmpEvalScorerNode scorerNode = nodeMap.computeIfAbsent(entry.getKey(),
                        k -> EmpEvalScorerNode.createFromResult(typeResults.get(0).getScorerType(),taskUser.getTaskId(),taskUser.getEmpId(),typeResults.get(0), "total"));
                
                scorerNode.accpScorerTotalScore(typeResults.get(0));
            }
        }
    }

    /**
     * 处理维度评价结果
     */
    private void processTypeResults(List<PerfEvalTypeResult> typeResults, Map<String, EmpEvalScorerNode> nodeMap) {
        Map<String, List<PerfEvalTypeResult>> typeScoreGroupMap = typeResults.stream()
                .collect(Collectors.groupingBy(BaseTypeScoreResult::asOrderAndScoreTypeKey));
        
        for (Map.Entry<String, List<PerfEvalTypeResult>> entry : typeScoreGroupMap.entrySet()) {
            List<PerfEvalTypeResult> typeResultSubs = entry.getValue();
            EmpEvalScorerNode scorerNode = nodeMap.computeIfAbsent(entry.getKey(),
                    k -> EmpEvalScorerNode.createFromResult(typeResultSubs.get(0).getScorerType(),taskUser.getTaskId(),taskUser.getEmpId(), typeResultSubs.get(0)));
            
            // 添加维度评价数据到节点
            addTypeResultsToNode(scorerNode, typeResultSubs);
        }
    }

    /**
     * 处理总分评价结果
     */
    private void processTotalResults(List<BaseScoreResult> totalResults, Map<String, EmpEvalScorerNode> nodeMap) {
        for (BaseScoreResult totalResult : totalResults) {
            // 使用评分人ID作为节点键值
            String nodeKey = "total_" + totalResult.getScorerId();
            EmpEvalScorerNode scorerNode = nodeMap.computeIfAbsent(nodeKey, 
                    k -> EmpEvalScorerNode.createFromResult("total_score", taskUser.getTaskId(),taskUser.getEmpId(), totalResult));
            
            // 添加总分评价数据到节点
            scorerNode.accpScorerTotalScoreData(totalResult);
        }
    }

    /**
     * 处理评价总结
     */
    private void processSummarys(List<EvalScoreSummary> summarys, Map<String, EmpEvalScorerNode> nodeMap) {
        summarys.forEach(summary -> {
            EmpEvalScorerNode scorerNode = nodeMap.get(summary.getScoreType());
            if (scorerNode != null) {
                scorerNode.accpScorerSummary(summary);
            }
        });
    }

    /**
     * 添加指标评分结果到节点
     */
    private void addScoreResultsToNode(EmpEvalScorerNode scorerNode, List<EvalScoreResult> scoreResults) {
        Map<String, List<EvalScoreResult>> kpiTypeGroupMap = scoreResults.stream()
                .collect(Collectors.groupingBy(EvalScoreResult::getKpiTypeId));
        
        for (Map.Entry<String, List<EvalScoreResult>> entry : kpiTypeGroupMap.entrySet()) {
            String kpiTypeId = entry.getKey();
            List<EvalScoreResult> kpiTypeResults = entry.getValue();
            
            EvalScorerNodeKpiType existingKpiType = findOrCreateKpiTypeNode(scorerNode, kpiTypeId, kpiTypeResults.get(0));
            existingKpiType.addScoreResults(kpiTypeResults);
        }
    }

    /**
     * 添加维度评价结果到节点
     */
    private void addTypeResultsToNode(EmpEvalScorerNode scorerNode, List<PerfEvalTypeResult> typeResults) {
        typeResults.forEach(typeResult -> {
            EvalScorerNodeKpiType existingKpiType = findOrCreateKpiTypeNode(scorerNode, typeResult.getKpiTypeId(), typeResult);
            existingKpiType.setTypeResult(typeResult);
        });
    }

    /**
     * 查找或创建KPI类型节点
     */
    private EvalScorerNodeKpiType findOrCreateKpiTypeNode(EmpEvalScorerNode scorerNode, String kpiTypeId, Object baseResult) {
        Optional<EvalScorerNodeKpiType> existingKpiType = scorerNode.getScorerNodeKpiTypes().stream()
                .filter(kpiType -> kpiTypeId.equals(kpiType.getKpiTypeId()))
                .findFirst();
        
        if (existingKpiType.isPresent()) {
            return existingKpiType.get();
        }
        
        EvalScorerNodeKpiType newKpiType = EvalScorerNodeKpiType.createFromResult(taskUser.getTaskId(),taskUser.getEmpId(), kpiTypeId,baseResult);
        scorerNode.getScorerNodeKpiTypes().add(newKpiType);
        return newKpiType;
    }
}
