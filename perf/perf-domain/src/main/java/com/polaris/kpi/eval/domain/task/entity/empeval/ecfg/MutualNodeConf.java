package com.polaris.kpi.eval.domain.task.entity.empeval.ecfg;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.eval.*;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.perf.www.common.constant.BusinessConstant;
import com.polaris.kpi.eval.domain.task.dmsvc.EvalChangeDmSvc;
import com.polaris.kpi.eval.domain.task.entity.EvalAudit;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.temp.entity.PerfTemplEvaluateAudit;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.domain.task.entity.empeval.ecfg
 * @Author: lufei
 * @CreateTime: 2022-09-06  14:29
 * @Version: 1.0
 */
@Getter
@Setter
public class MutualNodeConf extends RaterNodeConf {
    //protected BigDecimal nodeWeight;  //环节权重 取值 0-100,示例90=90% ,
    //protected int open;
    protected WaitAppoint appointer;   //待指定互评人

    protected String approverType;        //审批人类型（指定级别主管=manage|指定人员=user|考核人=taskEmp）
    protected String approverInfo;        //指定对象id (指定级别主管=1|指定人员=员工id |考核人=员工id ） taskEmp
    protected String vacancyType;     //评分人空缺时规则 "superior","admin","user" 自定义选择部门:customOrg  所在部门:locationOrg
    protected String vacancyRater;    //评分人空缺时规则 "superior","admin","user"
    protected String vacancyApproveName;   //审批人名称
    protected String multiType;             //多个审核人时会签还是或签 and |or  旧:multipleReviewersType
    protected String transferFlag;          //是否可转交
    protected String modifyFlag;          //是否可转交
    protected Integer approvalOrder;        //审批顺序（从1开始递增）
    protected String node;                  //结点名 "sub_score=下级互评" ,"peer_score=同级互评"
    protected LimitScorerNumCof scorerNumCof;       //限制评分人数配置
    protected String parentStr;                  //存放多选的部门、角色等信息
    protected List<Rater> raters = new ArrayList<>();           //此环节审核人员多人
    protected boolean change = false;           //是否变更
    protected boolean excludeAllManager = false;       //排除部门主管
    protected Integer fucType;        //算法类型 null = 正常, 2= 使用去掉最高最低后计算
    protected boolean allowSelfAsPeer = false; //   是否允许自评人为互评人，默认不允许

    public MutualNodeConf() {
    }

    public MutualNodeConf(String openFlag, String rateMode, Boolean anonymous) {
        if (Boolean.TRUE.toString().equals(openFlag)) {
            this.open = 1;
            this.rateMode = rateMode;
            this.anonymous = anonymous + "";
            return;
        }
        this.open = 0;
    }

    public MutualNodeConf(String openFlag, String rateMode, Boolean anonymous, List<BaseAuditNode> baseAuditNodes) {
        if (Boolean.TRUE.toString().equals(openFlag)) {
            this.open = 1;
            this.rateMode = rateMode;
            this.anonymous = anonymous + "";
            BaseAuditNode auditNode = baseAuditNodes.get(0);
            if (auditNode != null) {
                this.approverType = auditNode.getApproverType();
            }
            return;
        }
        this.open = 0;
    }

    @Override
    public void checkRequest() {
        if (open == 1) {
            super.checkRequest();
            Assert.notNull(nodeWeight, "结点 nodeWeight 不能空");
        }
    }

    @Override
    public MutualNodeConf clone() {
        return JSONUtil.toBean(JSONUtil.toJsonStr(this),MutualNodeConf.class);
    }

    @JSONField(serialize = false)
    public Boolean isOpen() {
        return open == 1;
    }

    public boolean hasEmptyRater() {
        return CollUtil.isEmpty(raters);
    }

//    public void avgMutualRaterWeight() {
//        if (CollUtil.isEmpty(raters)) {
//            return;
//        }
//        Pecent avgPect = new Pecent(Pecent.ONE_HUNDRED, raters.size());
//        raters.forEach(rater -> rater.setWeight(avgPect.getAvgWeight()));
//        //权重除不尽的差值加到第一位上
//        raters.get(0).setWeight(avgPect.getAvgAddDiff());
//    }

    public boolean belongItem(String belongEmpId) {
        for (Rater rater : raters) {
            if (StrUtil.equals(rater.getEmpId(), belongEmpId)) {
                return true;
            }
        }
        return false;
    }

    public void avgWeight() {
        if (CollUtil.isEmpty(raters)) {
            return;
        }
        Pecent avgPect = new Pecent(Pecent.ONE_HUNDRED, raters.size());
        raters.forEach(rater -> rater.setWeight(avgPect.getAvgWeight()));
        //权重除不尽的差值加到第一位上
        raters.get(0).setWeight(avgPect.getAvgAddDiff());
    }

    public String nodeName() {
        StringBuilder sb = new StringBuilder();
        if (Objects.nonNull(this.appointer)) {
            return this.appointer.nodeName();
        }
        for (Rater rater : allRater()) {
            sb.append(String.format("%s（%s％）", rater.getEmpName(), rater.getWeight()));
        }
        return sb.toString();
    }

    public List<Rater> allRater() {
        if (open == 0) {
            return Collections.emptyList();
        }
        return raters;
    }

    public void initRaterWeight() {
        if (CollUtil.isEmpty(raters)) {
            return;
        }
        allRater().forEach(rater -> rater.setWeight(new BigDecimal("100.00")));
    }

    public void repeatRater() {
        if (CollUtil.isEmpty(raters)) {
            System.out.println("Raters collection is empty or null.");
            return;
        }

        // 使用线程安全的集合
        synchronized (this) {
            Set<String> uniqueEmpIds = new HashSet<>();
            for (Rater rater : raters) {
                uniqueEmpIds.add(rater.getEmpId());
            }

            // 更新 raters 中的 empIds 为去重后的结果
            raters.removeIf(rater -> !uniqueEmpIds.remove(rater.getEmpId()));
        }
    }

    public void extendMutiConf(ScoreConf scoreConf) {
        setVacancyType(scoreConf.getVacancyApproveType());
        setTransferFlag(scoreConf.getTransferFlag());
        setMultiType(BusinessConstant.REVIEWERS_TYPE_AND);
        setVacancyRater(scoreConf.getVacancyApproveInfo());
    }

    public List<Rater> explainRater(String empId, TenantId companyId, EmpId opEmpId, String evalOrgId, String orgId) {
        //List<Rater> rs = new ArrayList<>();
        if (isOrgAudit() || isRoleAudit()) {
            List<RaterParent> parentList = new ArrayList<>();
            for (Rater rater : getRaters()) {
                parentList.add(new RaterParent().builderRater(isOrgAudit() ? rater.getOrgId() : rater.getRoleId()
                        , isOrgAudit() ? rater.getOrgName() : rater.getRoleName()));
            }
            this.parentStr = JSONUtil.toJsonStr(parentList);
        }
        if (isRoleAudit()) {
            List<Rater> raterList = new ArrayList<>();
            for (Rater rater : getRaters()) {
                EvalAudit tmpAudit = new EvalAudit(approverType, rater.getRoleId(), companyId, new EmpId(empId), opEmpId, evalOrgId, orgId);
                raterList.addAll(tmpAudit.genAuditRaters());
            }
            this.raters = raterList;
        }
        if (isOrgAudit()) {
            List<Rater> raterList = new ArrayList<>();
            for (Rater rater : getRaters()) {
                EvalAudit tmpAudit = new EvalAudit(approverType, rater.getOrgId(), companyId, new EmpId(empId), opEmpId, evalOrgId, orgId);
                raterList.addAll(tmpAudit.genAuditRaters());
            }
            this.raters = raterList;
        }
        //去除同级互评人。
        if (CollUtil.isNotEmpty(this.raters)) {
            this.raters = CollUtil.filterNew(this.raters, r -> !Objects.equals(empId, r.getEmpId()));
        }

        repeatRater();//去重
        return raters;
    }


    public void refreshRater(String empId, TenantId companyId, String opEmpId, boolean refresh, String evalOrgId, String orgId,boolean isOpenAvgWeightCompute) {
        if (!isChange() && !refresh) {
            return;
        }
        List<Rater> raterList = new ArrayList<>();
        List<RaterParent> parentList = JSONUtil.toList(this.parentStr, RaterParent.class);
        if (CollUtil.isNotEmpty(parentList)) {
            for (RaterParent raterParent : parentList) {
                if (StrUtil.isBlank(raterParent.getApproverInfo())) {
                    continue;
                }
                EvalAudit tmpAudit = new EvalAudit(approverType, raterParent.getApproverInfo(), companyId, new EmpId(empId), new EmpId(opEmpId), evalOrgId, orgId, this.excludeAllManager);
                raterList.addAll(tmpAudit.genAuditRaters());
            }
        }
        if (isOrgAudit() || (isRoleAudit() && refresh)) {
            this.raters = raterList;
        }
        //没有开启允许自评人为互评人情况下 需要过滤掉自评人
        if (!allowSelfAsPeer) {
            this.filterMutualSelf(empId);
        }
        this.randomRaters();
        if (!isOpenAvgWeightCompute) {
            initRaterWeight();//初始化权重100% 后面计算平均分
        }
        this.change = false;
    }

    @JSONField(serialize = false)
    public boolean isRoleAudit() {
        return BusinessConstant.APPROVER_TYPE_ROLE.equals(this.getApproverType());
    }

    @JSONField(serialize = false)
    public boolean isManagerAudit() {
        return BusinessConstant.APPROVER_TYPE_MANAGER.equals(this.getApproverType());
    }

    @JSONField(serialize = false)
    public boolean isOrgAudit() {
        return BusinessConstant.APPROVER_TYPE_CUSTOM_ORG.equals(this.getApproverType())
                || BusinessConstant.APPROVER_TYPE_LOCATION_ORG.equals(this.getApproverType());
    }

    @JSONField(serialize = false)
    public boolean isCustomOrgAudit() {
        return BusinessConstant.APPROVER_TYPE_CUSTOM_ORG.equals(this.getApproverType());
    }

    public void replaceRater(String fromEmpId, KpiEmp toEmp) {
        for (Rater rater : raters) {
            if (StrUtil.equals(fromEmpId, rater.getEmpId())) {
                rater.setEmpId(toEmp.getEmpId());
                rater.setEmpName(toEmp.getEmpName());
                rater.setAvatar(toEmp.getAvatar());
            }
        }
    }

    public void replaceRater(String fromEmpId, KpiEmp toEmp, Boolean raterExist) {
        if (raterExist) {
            raters = raters.stream()
                    .filter(s -> !StrUtil.equals(s.getEmpId(), fromEmpId))
                    .collect(Collectors.toList());
        }else {
            this.replaceRater(fromEmpId, toEmp);
        }
    }


    public void replaceRaterV3(String fromEmpId, KpiEmp toEmp) {
        // raters 是否包含 toEmp
        boolean raterExist = raters.stream().anyMatch(rater -> StrUtil.equals(rater.getEmpId(), toEmp.getEmpId()));
        replaceRater(fromEmpId,toEmp,raterExist);
    }

    public void skipRater(String fromEmpId) {
        raters = raters.stream().filter(s -> !StrUtil.equals(s.getEmpId(), fromEmpId)).collect(Collectors.toList());
    }

    public boolean conainAppoint(String evalEmpId, String opEmpId) {
        if (appointer == null) {
            return false;
        }
        return appointer.isAppointer(opEmpId, evalEmpId,null);
    }

    /**
     * 过滤互评中为当前考核人的数据
     */
    public void filterMutualSelf(String empId) {
        if (CollUtil.isEmpty(this.raters)) {
            return;
        }
        this.raters = this.raters.stream().filter(e -> !empId.equals(e.getEmpId())).collect(Collectors.toList());
    }

    public String fucTypeName() {
        return Objects.isNull(this.fucType) ? "取平均分" : "去掉最高最低后再取平均分";
    }

    public void randomRaters() {
        if (Objects.isNull(this.scorerNumCof)) {
            return;
        }
        if (!this.scorerNumCof.isOpen()) {
            return;
        }
        if (this.scorerNumCof.isUnlimited()) {
            return;
        }
        if (this.scorerNumCof.isRegular()) {
            int numberToPick = this.scorerNumCof.getFixedNum(); // 定义想要随机选取的对象数量
            if (this.raters.size() <= numberToPick) {
                return;
            }
            this.raters = this.raters.stream().distinct().collect(Collectors.toList());
            this.raters = new Random()
                    .ints(0, this.raters.size())
                    .distinct()
                    .limit(numberToPick)
                    .boxed()
                    .map(this.raters::get)
                    .collect(Collectors.toCollection(ArrayList::new));
        }
    }

    public void builderParentStr(List<PerfTemplEvaluateAudit> templEvaluateAudits) {
        if (CollUtil.isEmpty(templEvaluateAudits)) {
            return;
        }
        if (isOrgAudit() || isRoleAudit()) {
            List<RaterParent> parentList = new ArrayList<>();
            for (PerfTemplEvaluateAudit templEvaluateAudit : templEvaluateAudits) {
                parentList.add(new RaterParent().builderRater(templEvaluateAudit.getApproverInfo(), templEvaluateAudit.getApproverEmpName()));
            }
            this.parentStr = JSONUtil.toJsonStr(parentList);
        }
    }

    public boolean onUseDelMinMax() {
        boolean open = fucType != null && fucType == 2;
        return open & raters.size() > 3;
    }

    @JSONField(serialize = false)
    public boolean isOpenAppointer() {
        return Objects.nonNull(this.appointer);
    }

    public void parseMutualNodeConfRaters(EvalUser user,  EmpId opEmpId) {
        if (!this.isOpen()) {
            return;
        }
        if (this.isOpenAppointer()) {
            this.appointer.parseWaitAppointRaters(user, opEmpId);
            if (this.appointer.isOpenInviteMutualAudit()) {
                this.appointer.getInviteMutualAudit().parseInviteMutualAuditRaters(user,opEmpId);
            }
            return;
        }
        if (this.isRoleAudit() || this.isOrgAudit()) {
            List<Rater> raterList = new ArrayList<>();
            List<RaterParent> parentList = JSONUtil.toList(this.getParentStr(), RaterParent.class);
            for (RaterParent raterParent : parentList) {
                raterList.addAll(parseRaters(user,this.getApproverType(), raterParent.getApproverInfo(),opEmpId));
            }
            this.setRaters(raterList);
            return;
        }
        this.setRaters(parseRaters(user,this.getApproverType(), this.getApproverInfo(),opEmpId));
    }

    public void parseMutualNodeConfChangeRaters(String empId, String evalOrgId, String orgId,Set<ChangeInfo> infos,EvalChangeDmSvc dmSvc) {
        if (!this.isOpen()) {
            return;
        }
        if (this.isOpenAppointer()) {
            this.appointer.parseWaitAppointChangeRaters(empId,evalOrgId,orgId,infos,dmSvc);
            if (this.appointer.isOpenInviteMutualAudit()) {
                this.appointer.getInviteMutualAudit().parseInviteMutualAuditChangeRaters(empId,evalOrgId,orgId,infos,dmSvc);
            }
            return;
        }
        if (this.isRoleAudit() || this.isOrgAudit()) {
            List<Rater> raterList = new ArrayList<>();
            List<RaterParent> parentList = JSONUtil.toList(this.getParentStr(), RaterParent.class);
            for (RaterParent raterParent : parentList) {
                List<Rater> raters = dmSvc.parseBaseChangeRaters(empId, evalOrgId, orgId, infos,
                        this.raters, this.approverType, raterParent.getApproverInfo());
                raterList.addAll(raters);
            }
            this.setRaters(CollUtil.distinct(raterList));
            return;
        }
        if (this.isManagerAudit()) {
            this.setRaters(dmSvc.parseBaseChangeRaters(empId, evalOrgId, orgId,infos,
                    this.raters, this.approverType, this.getApproverInfo()));
        }
    }


    //解析
    public List<Rater>  parseRaters(EvalUser user, String approverType,String approverInfo, EmpId opEmpId) {
        EvalAudit audit = new EvalAudit(approverType, approverInfo, user.getCompanyId(),
                user.evalOrg() ? opEmpId : new EmpId(user.getEmpId()), opEmpId, user.getEvalOrgId(), user.getOrgId());
        List<Rater> raters = user.evalOrg() ? audit.parseAtOrgMg() : audit.parseRaters();
        if (CollUtil.isEmpty(raters)) {
            user.setRuleConfStatus(106);
        }
        return raters;
    }

    public void initAllowSelfAsPeer(boolean allowSelfAsPeer) {
        this.allowSelfAsPeer = allowSelfAsPeer;
    }

    /**
     * 获取有效的评分人列表，用于构建RaterNode
     * 处理appointer逻辑：如果有appointer且类型为emp或空，返回默认评分人；否则返回appointer的评分人
     * 如果没有appointer，返回当前对象的评分人列表
     * 
     * @return 有效的评分人列表
     */
    public List<Rater> buildRaterNode() {
        if (!isOpen()) {
            return Collections.emptyList();
        }
        
        if (getAppointer() != null) {
            if ("emp".equals(getAppointer().getType()) || StrUtil.isBlank(getAppointer().getType())) {
                return Arrays.asList(new Rater(null, 5));
            } else {
                return getAppointer().getRaters();
            }
        } else {
            return getRaters();
        }
    }
}
