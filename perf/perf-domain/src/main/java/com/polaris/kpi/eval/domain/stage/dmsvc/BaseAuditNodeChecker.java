package com.polaris.kpi.eval.domain.stage.dmsvc;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.eval.Rater;
import cn.com.polaris.kpi.eval.RaterNode;
import cn.hutool.core.collection.CollUtil;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.*;
import com.polaris.kpi.org.domain.dept.dmsvc.EmpParser;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class BaseAuditNodeChecker {
    private TenantId companyId;
    private EmpParser empParser;
    private List<EvalUser> needUpateUsers = new ArrayList<>();

    public BaseAuditNodeChecker(TenantId companyId, EmpParser empParser) {
        this.companyId = companyId;
        this.empParser = empParser;
    }


    /**
     * @param evalUser
     * @param auditNodes
     * @param node
     * @param nodeEmpVacancy 节点人员空缺 1:系统自动跳过  2:提示异常  3:转交给考核任务发起人
     */
    public void parseCheckFlowError(EvalUser evalUser, List<? extends BaseAuditNode> auditNodes, String node, Integer nodeEmpVacancy) {
        if (auditNodes.stream().flatMap(a -> a.getRaters().stream().filter(r -> Objects.equals(r.getEmpId(), "-1"))).collect(Collectors.toList()).size() == auditNodes.size()) {
            evalUser.confEvalRuleErro(node);
            return;
        }
        for (BaseAuditNode auditNode : auditNodes) {
            if (auditNode.getRaters().isEmpty()) {
                if (Objects.equals(nodeEmpVacancy, 2) || Objects.equals(nodeEmpVacancy, 3)) {
                    evalUser.confEvalRuleErro(node);
                    break;
                }
            }
            if (isLackEmp(auditNode.getRaters().stream().map(Rater::getEmpId).collect(Collectors.toList()), nodeEmpVacancy, auditNodes.size())) {
                evalUser.confEvalRuleErro(node);
                break;
            }
        }
    }

    public boolean isLackEmp(List<String> raterEmpIds, Integer nodeEmpVacancy, Integer auditNodeSize) {
        List<KpiEmp> emps = empParser.getEmps(raterEmpIds);
        ListWrap<KpiEmp> kpiEmps = new ListWrap<>(emps).asMap(emp -> emp.getEmpId());
        for (String raterEmpId : raterEmpIds) {
            KpiEmp kpiEmp = kpiEmps.mapGet(raterEmpId);
            if (kpiEmp == null) {
                if (Objects.equals(auditNodeSize, 1) && Objects.equals(nodeEmpVacancy, 1)) {
                    return true;
                }
                if (Objects.equals(nodeEmpVacancy, 2) || Objects.equals(nodeEmpVacancy, 3)) {
                    return true;
                }
            }
        }
        return false;
    }

    public List<String> hasNodeLeavedRater(List<RaterNode> raterNodes, String opEmpId) {
        List<String> scoreErrorNode = new ArrayList<>();
        for (RaterNode raterNode : raterNodes) {
            List<String> raterEmpIds = new ArrayList<>();
            if ("self".equals(raterNode.getNode())) {
                raterEmpIds.add(opEmpId);
            } else if (("peer".equals(raterNode.getNode()) || "sub".equals(raterNode.getNode()))) {
                for (Rater rater : raterNode.getRaters()) {
                    raterEmpIds.add(Objects.equals(rater.getType(), 5) ? opEmpId : rater.getEmpId());
                }
            } else {
                if (raterNode.getRaters().isEmpty()) {
                    scoreErrorNode.add(raterNode.getNode());
                    continue;
                }
                raterEmpIds = raterNode.getRaters().stream().map(Rater::getEmpId).collect(Collectors.toList());
            }
            List<KpiEmp> emps = empParser.getEmps(raterEmpIds);
            ListWrap<KpiEmp> kpiEmps = new ListWrap<>(emps).asMap(emp -> emp.getEmpId());
            if (CollUtil.isEmpty(emps)) {
                scoreErrorNode.add(raterNode.getNode());
                continue;
            }
            for (String raterEmpId : raterEmpIds) {
                KpiEmp kpiEmp = kpiEmps.mapGet(raterEmpId);
                if (kpiEmp == null) {
                    scoreErrorNode.add(raterNode.getNode());
                }
            }
        }
        return scoreErrorNode.stream().distinct().collect(Collectors.toList());
    }


}
