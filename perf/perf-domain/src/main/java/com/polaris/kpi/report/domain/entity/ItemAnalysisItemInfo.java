package com.polaris.kpi.report.domain.entity;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/5/12 15:29
 */
@Data
public class ItemAnalysisItemInfo {

    private BigDecimal targetValueSum;
    private BigDecimal finishValueSum;
    private Integer finishValueType;
    private String orgId;
    private String atOrgCodePath;
    private String itemId;
    private String itemName;
    private String itemUnit;

}
