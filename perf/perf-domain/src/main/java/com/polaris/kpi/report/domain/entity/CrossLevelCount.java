package com.polaris.kpi.report.domain.entity;


import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.common.DelableDomain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/6/9 14:48
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CrossLevelCount extends DelableDomain {

    private String id;
    @JSONField(serialize = false)
    private String companyId;
    private String cycleId;
    private Integer upCountState;
    private Date upCountedTime;
    private Integer downCountState;
    private Date downCountedTime;
    private Integer performanceType;


    public CrossLevelCount(String companyId, String cycleId, Integer performanceType, Date date, String opEmpId) {
        this.performanceType = performanceType;
        this.companyId = companyId;
        this.cycleId = cycleId;
        this.createdTime = date;
        this.createdUser = opEmpId;
    }

    @JSONField(serialize = false)
    public Boolean isNew() {
        return Objects.isNull(id);
    }

    @JSONField(serialize = false)
    public void initOnNew(String id){
        this.id = id;
    }
}
