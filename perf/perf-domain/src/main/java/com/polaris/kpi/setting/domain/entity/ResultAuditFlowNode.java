package com.polaris.kpi.setting.domain.entity;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.common.DelableDomain;
import com.polaris.kpi.eval.domain.task.entity.EvalAudit;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.*;
import java.util.stream.Collectors;

@Setter
@Getter
@NoArgsConstructor
public class ResultAuditFlowNode extends DelableDomain {
    private String id;
    private TenantId companyId;
    private String flowInstanceId;
    private String taskUserId;
    private Integer approvalOrder;
    private String multipleReviewersType;  //会签/或签
    private Integer status;   // 审批状态(0:未开始  1:执行中 2:已完成 4:跳过完成 6:校准已完成)
    private Integer skipFlag;
    private List<ResultAuditFlowNodeRater> nodeRaters;  //审核人

    public ResultAuditFlowNode(String id,String companyId, String flowInstanceId,String taskUserId,
                                 Integer approvalOrder, String multipleReviewersType, Integer status,Integer skipFlag, String createdUser) {
        this.id = id;
        this.companyId = new TenantId(companyId);
        this.flowInstanceId = flowInstanceId;
        this.taskUserId = taskUserId;
        this.approvalOrder = approvalOrder;
        this.multipleReviewersType = multipleReviewersType;
        this.status = status;
        this.skipFlag = skipFlag;
        this.createdUser = createdUser;
        this.createdTime = new Date();
    }

    public void accop(String companyId, String flowInstanceId, String taskUserId, Integer approvalOrder, String multipleReviewersType, String opEmpId) {
        this.companyId = new TenantId(companyId);;
        this.flowInstanceId = flowInstanceId;
        this.taskUserId = taskUserId;
        this.approvalOrder = approvalOrder;
        this.multipleReviewersType = multipleReviewersType;
        this.createdUser = opEmpId;
        this.createdTime = new Date();
        this.status = 0;
    }

    public void buildStatus(List<EvalScoreResult> curLevelRs, String opEmpId, boolean isAudit, Integer approvalOrder) {
        if (CollUtil.isEmpty(this.nodeRaters)) {
            return;
        }
        if (CollUtil.isEmpty(curLevelRs)) {
            return;
        }
        for (ResultAuditFlowNodeRater nodeRater : this.nodeRaters) {
            nodeRater.changeStatus(curLevelRs, opEmpId, isAudit, approvalOrder);
        }
        List<Integer> status = CollUtil.map(this.nodeRaters, r -> r.getStatus(), true).stream().distinct().collect(Collectors.toList());
        if (status.size() == 1) {
            this.status = status.get(0);
        }
    }

    public void refreshStatus(Integer approvalOrder) {
        if (CollUtil.isEmpty(this.nodeRaters)) {
            return;
        }
        for (ResultAuditFlowNodeRater nodeRater : this.nodeRaters) {
            nodeRater.refreshStatus();
        }
        List<Integer> status = CollUtil.map(this.nodeRaters, r -> r.getStatus(), true).stream().distinct().collect(Collectors.toList());
        if (status.size() == 1) {
            this.status = status.get(0);
        }
    }


    //执行校准
    @JSONField(serialize = false)
    public boolean isFinish() {
        return Objects.equals(this.status, 2);
    }

    //进入校准
    @JSONField(serialize = false)
    public boolean isEnter() {
        return Objects.equals(this.status, 1);
    }


    public void passRater(List<ResultAuditFlowNodeRater> raters) {
        if (CollUtil.isEmpty(raters)) {
            return;
        }
        if (CollUtil.isEmpty(this.nodeRaters)) {
            return;
        }
        raters = raters.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                new TreeSet<>(Comparator.comparing(r -> r.getFlowNodeId() + ";" + r.getAuditEmpId()))), ArrayList::new));
        Map<String, ResultAuditFlowNodeRater> raterMap = CollStreamUtil.toIdentityMap(raters, r -> r.getFlowNodeId() + ";" + r.getAuditEmpId());
        for (ResultAuditFlowNodeRater nodeRater : this.nodeRaters) {
            if (Objects.nonNull(raterMap.get(nodeRater.getFlowNodeId() + ";" + nodeRater.getAuditEmpId()))) {
                nodeRater.setStatus(2);
            }
        }
        List<Integer> status = CollUtil.map(this.nodeRaters, r -> r.getStatus(), true).stream().distinct().collect(Collectors.toList());
        if (status.size() == 1) {
            this.status = status.get(0);
        }
    }


    @JSONField(serialize = false)
    public boolean isCurLevel(Integer approvalOrder, String taskUserId) {
        if (Objects.isNull(approvalOrder)) {
            return false;
        }
        if (!Objects.equals(this.approvalOrder, approvalOrder)) {
            return false;
        }
        for (ResultAuditFlowNodeRater nodeRater : this.getNodeRaters()) {
            for (ResultAuditFlowUser flowUser : nodeRater.getFlowUsers()) {
                if (Objects.equals(flowUser.getTaskUserId(), taskUserId)) {
                    return true;
                }
            }
        }
        return false;
    }


    //////////////////////////////////////////////////////////////////////////////////
    public void updateStatus(List<ResultAuditFlowNodeRater> flowNodeRaters) {
        if (CollUtil.isEmpty(flowNodeRaters)) {
            this.status = 0;
            return;
        }
        int totalSize = flowNodeRaters.size();
        int notEnterCnt = 0;
        int finishCnt = 0;
        for (ResultAuditFlowNodeRater flowNodeRater : flowNodeRaters) {
            if (flowNodeRater.isNotEnter()) {
                notEnterCnt++;
                continue;
            }
            if (flowNodeRater.isFinish()) {
                finishCnt++;
            }
        }
        this.status = Objects.equals(notEnterCnt, totalSize) ? 0 : (finishCnt == totalSize ? 2 : 1);
    }


    public void updateStatus() {
        if (CollUtil.isEmpty(this.nodeRaters)) {
            this.status = 0;
            return;
        }
        int totalSize = this.nodeRaters.size();
        int notEnterCnt = 0;
        int finishCnt = 0;
        for (ResultAuditFlowNodeRater flowNodeRater : this.nodeRaters) {
            if (flowNodeRater.isNotEnter()) {
                notEnterCnt++;
                continue;
            }
            if (flowNodeRater.isFinish()) {
                finishCnt++;
            }
        }
        this.status = Objects.equals(notEnterCnt, totalSize) ? 0 : (finishCnt == totalSize ? 2 : 1);
    }

    @JSONField(serialize = false)
    public boolean isAndAudit() {
        return Objects.equals(this.multipleReviewersType, "and");
    }

    @JSONField(serialize = false)
    public boolean needSkipNode() {
        if (isAndAudit()) {
            return nodeRaters.stream().allMatch(n -> n.needSkip());
        }
        return Objects.equals(this.skipFlag,1);
    }

    public void markSkip() {
        this.status = 4;
        return;
    }
}
