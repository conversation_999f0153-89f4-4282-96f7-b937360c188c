package com.polaris.kpi.eval.domain.task.entity.empeval;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.com.polaris.kpi.eval.*;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.perf.www.common.constant.BusinessConstant;
import com.polaris.kpi.eval.domain.task.dmsvc.EvalChangeDmSvc;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.admineval.EvalRuleLogField;
import com.polaris.kpi.eval.domain.task.entity.admineval.EvalRuleOpLogMeta;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.chain.ScoreSceneWrap;
import com.polaris.kpi.eval.domain.task.entity.flow.AsDisplayFlowRs;
import com.polaris.kpi.eval.domain.task.entity.grade.IndLevelGroup;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrGoal;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrKeyResult;
import com.polaris.kpi.eval.domain.task.type.InnerFields;
import com.polaris.kpi.eval.domain.task.type.NodeWeight;
import com.polaris.sdk.type.AuditEnum;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * {
 * "kpiTypeId":"指标类id",
 * "companyId":"公司id",
 * "taskUserId":"task user 表的id",
 * "kpiTypeName":"指标类名称",
 * "kpiTypeWeight":"指标类权重",
 * "isOkr":"是否OKR类别，true/false",
 * "typeOrder":"类别排序",
 * "reserveOkrWeight":"预留OKR权重",
 * "isDeleted":"是否删除",
 * "createdUser":"创建用户",
 * "createdTime":"创建时间",
 * "updatedUser":"修改用户",
 * "updatedTime":"修改时间",
 * "isTypeLocked":"类别锁定类型 ["typeWeight","addIndex","modifyIndex","deleteIndex"]",
 * "version":"版本号",
 * }
 ***/
@Getter
@Setter
@Slf4j
public class EmpEvalKpiType extends BaseEvalFlow implements Cloneable {

    protected String kpiTypeId;//指标类id
    @JSONField(serialize = false)
    private TenantId companyId;//公司id
    protected String taskUserId;//task user 表的id
    protected String kpiTypeName;//指标类名称
    protected BigDecimal kpiTypeWeight;//指标类权重
    protected String isOkr;//是否OKR类别，true/false
    protected Integer typeOrder;//类别排序
    protected BigDecimal reserveOkrWeight;//预留OKR权重
    //private List<String> isTypeLocked;//类别锁定类型 ["typeWeight","addIndex","modifyIndex","deleteIndex"] isTypeLocked
    protected List<String> lockedItems;//类别锁定类型 ["typeWeight","addIndex","modifyIndex","deleteIndex"] isTypeLocked
    protected String kpiTypeClassify;//指标类别，为空表示已有类别，custom自定义/plus加分项/subtract减分项
    protected BigDecimal maxExtraScore;//本类别最大加减分上限
    protected String importOkrFlag;//是否已导入OKR
    protected KpiItemLimitCnt itemLimitCnt;
    protected int openOkrScore;
    protected PlusSubInterval plusSubInterval;
    protected List<KpiTypeUsedField> kpiTypeUsedFields;        //使用的字定义字段集合
    private boolean isRaterBack = false;            //评分人为空的时候是否返回
    protected IndLevelGroup indLevelGroup;            //生成人员考核时,从模板或者考核表复制来
    protected String indLevelGroupId;               //指标等级组id >100 表示等级选项  , 1 = 分数选项, 0|null= 自定评分=维度是无需评分
    protected String typeLevel;                     //维度的等级
    protected List<StaffConfItem> finishValueAudit;//完成值审核
    protected Integer scoreOptType;        //# 0:等级[无等级组]indLevelId==null # 1:等级[设等级组] indLevelId=1000234  #2:评分[无选项组] indLevelId==null  # 4:评分[有选项组], indLevelId=1000235
    protected String des;        //维度描述 20230921增加
    protected String ask360TempId;   // 360问卷模版ID
    protected String ask360TempName;
    protected String ask360TempDesc;  //360问卷模版描述
    protected Integer scoringType;   // 计分方式(1:问卷平均分(题目分数之和/题目数)  2:问卷总分(题目分数之和))
    protected String ask360EvalId;  //360问卷考核实例ID
    protected BigDecimal ask360EvalScore;  //360问卷分数

    private BigDecimal typeFinalScore;  //维度最终得分
    private BigDecimal typeFinalOriginalScore;  //维度最终原始得分
    private BigDecimal typeScore;  //维度得分 【未计算维度权重】
    private BigDecimal typeOriginalScore;  //维度原始得分【未计算维度权重】
    private BigDecimal typeFinalSelfScore;  //维度最终自评得分
    private BigDecimal typeFinalPeerScore;  //维度最终同级得分
    private BigDecimal typeFinalSubScore;  //维度最终下级得分
    private BigDecimal typeFinalSuperiorScore;  //维度最终上级得分
    private BigDecimal typeFinalAppointScore;  //维度最终定向得分


    private BigDecimal typeFinalItemScore; //维度最终指定得分
    private BigDecimal typeItemScore; //维度最终指定得分
    private BigDecimal itemItemScore;//指标定向评评分 未乘以指标权重
    private BigDecimal typeSelfScore;  //维度自评得分(不带维度权重)
    private BigDecimal typePeerScore;  //维度同级得分(不带维度权重)
    private BigDecimal typeSubScore;  //维度下级得分(不带维度权重)
    private BigDecimal typeSuperiorScore;  //维度上级得分(不带维度权重)
    private BigDecimal typeAppointScore;  //维度定向得分(不带维度权重)


    public EmpEvalKpiType() {
    }

    protected List<OkrGoal> okrGoals;        //okr的目标

    //是否未开启此环节
    public boolean isOpenNode(SubScoreNodeEnum node, int nodeOrder, String evalEmpId) {
        if (!super.isOpenRaterRule(node)) {
            return false;
        }
        INodeConf nodeConf = super.getNodeConf(node, nodeOrder, evalEmpId);
        return nodeConf != null;
    }

    protected List<? extends IItemScoreRuleOwn> items() {
        return items;
    }

    protected BaseEvalFlow typeRule() {
        return typeRule;
    }

    public EmpEvalKpiType(TenantId companyId, String taskUserId, String kpiTypeId) {
        this.kpiTypeId = kpiTypeId;
        this.companyId = companyId;
        this.taskUserId = taskUserId;
    }

    public void accNameType(String kpiTypeName, String classify, Integer typeOrder, BigDecimal weight) {
        this.kpiTypeName = kpiTypeName;
        this.kpiTypeClassify = classify;
        this.typeOrder = typeOrder;
        this.kpiTypeWeight = weight;
    }

    private List<PerfEvalTypeResult> waitScoresOld = new ArrayList<>();//待提交的评分
    private List<NodeOfItemScore> alreadyScores = new ArrayList<>();//已提交的评分

    private List<EvalScorerNodeKpiType> waitScores = new ArrayList<>();//所有需提交的评分
    private List<EvalScorerNodeKpiType> alreadyScoreV3 = new ArrayList<>();//已提交的评分


    public void accOkr(String isOkr, BigDecimal reserveOkrWeight) {
        this.isOkr = isOkr;
        this.reserveOkrWeight = reserveOkrWeight;
        this.importOkrFlag = Boolean.FALSE.toString();
    }

    public void accLimit(KpiItemLimitCnt itemLimitCnt, List<String> lockedItems, BigDecimal maxExtraScore) {
        this.lockedItems = lockedItems;
        this.maxExtraScore = maxExtraScore;
        this.itemLimitCnt = itemLimitCnt;
    }

    public void acckpiTypeUsedFields(List<KpiTypeUsedField> kpiTypeUsedFields) {
        this.kpiTypeUsedFields = kpiTypeUsedFields;
    }

    protected List<EvalKpi> items = new ArrayList<>();//指标

    public List<StaffConfItem> finishValueOfType() {
        if (CollUtil.isEmpty(this.finishValueAudit)) {
            return initStaffConfItem();
        }
        return finishValueAudit;
    }
    public void orderItem() {
        if (CollUtil.isEmpty(this.items)) {
            return;
        }
        this.items.sort(Comparator.comparing(
                EvalKpi::getOrder,
                Comparator.nullsLast(Comparator.naturalOrder())
        ));
    }

    public List<StaffConfItem> initStaffConfItem() {
        List<StaffConfItem> confItemList = new ArrayList<>();
        confItemList.add(initRoleConf());
        confItemList.add(initUserConf());
        confItemList.add(initManagerConf());
        return confItemList;
    }

    public StaffConfItem initRoleConf() {
        StaffConfItem roleItem = new StaffConfItem();
        roleItem.setObjType("role");
        roleItem.setObjItems(Collections.emptyList());
        return roleItem;
    }

    public StaffConfItem initUserConf() {
        StaffConfItem roleItem = new StaffConfItem();
        roleItem.setObjType("user");
        roleItem.setObjItems(Collections.emptyList());
        return roleItem;
    }

    public StaffConfItem initManagerConf() {
        StaffConfItem roleItem = new StaffConfItem();
        roleItem.setObjType("manager");
        roleItem.setObjItems(Collections.emptyList());
        return roleItem;
    }


    public void initRefItems() {
        if (CollUtil.isEmpty(items)) {
            return;
        }
        for (EvalKpi item : items) {
            item.setCompanyId(companyId);
            item.setKpiTypeId(kpiTypeId);
            item.setKpiTypeName(kpiTypeName);
            item.setKpiTypeWeight(kpiTypeWeight);
            item.setIsOkr(isOkr);
            item.setTypeOrder(typeOrder);
            item.setReserveOkrWeight(reserveOkrWeight);
            item.setKpiTypeClassify(kpiTypeClassify);
            item.setMaxExtraScore(maxExtraScore);
            item.setTaskUserId(taskUserId);
        }
    }

    @JSONField(serialize = false)
    public boolean isEmpty() {
        return CollUtil.isEmpty(items);
    }

    @JSONField(serialize = false)
    public boolean isOkr() {
        return Boolean.valueOf(isOkr);
    }

    public RaterNode allSelfRater() {
        if (CollUtil.isEmpty(items)) {//okr或空类别
            return null;
        }
        if (typeRule != null) {
            RaterNodeConf selfRater = typeRule.getSelfRater();
            if (selfRater.isOpen()) {
                Rater rater = new Rater(null, 5);
                return new RaterNode("self", Arrays.asList(rater));
            }
        }
        for (EvalKpi item : items) {
            //自定义指标不存在ItemScoreRule
            if (StrUtil.equals("auto", item.getScorerType())) {
                continue;
            }
            //和志林沟通后这里表示自定义模板只要有任何一个指标开启了自评，就要返回自评环节，所以在循环中retuen了
            if (Objects.isNull(item.itemScoreRule())) {
                continue;
            }
            RaterNodeConf selfRater = item.itemScoreRule().getSelfRater();
            if (selfRater.isOpen()) {
                Rater rater = new Rater(item.getEmpId(), 5);
                return new RaterNode("self", Arrays.asList(rater));
            }
        }
        return null;
    }


    /**
     * 初始化指标自定义字段配置
     */
    public void initKpiItemUsedFields(List<KpiItemUsedField> usedFieldDos) {
        /**初始化指标自定义字段配置*/
        if (CollUtil.isNotEmpty(this.items) && CollUtil.isNotEmpty(usedFieldDos)) {
            Map<String, List<KpiItemUsedField>> groupMap = usedFieldDos.stream()
                    .collect(Collectors.groupingBy(KpiItemUsedField::getKpiItemId));
            this.items.forEach(obj -> {
                if (groupMap.get(obj.getKpiItemId()) != null) {
                    List<ItemCustomFieldValue> fieldValueList = new ArrayList<>();
                    List<KpiItemUsedField> itemUsedFields = groupMap.get(obj.getKpiItemId());
                    if (itemUsedFields != null && itemUsedFields.size() > 0) {
                        itemUsedFields.forEach(item -> {
                            ItemCustomFieldValue fieldValue = new ItemCustomFieldValue();
                            BeanUtils.copyProperties(item, fieldValue);
                            fieldValue.setFieldName(item.getName());
                            fieldValue.setFieldValue(item.getValue());
                            fieldValue.setFieldStatus(item.getStatus());
                            fieldValue.setId(item.getFieldId());
                            fieldValue.setIsReq(item.getReq());
                            fieldValueList.add(fieldValue);
                        });
                        obj.setFieldValueList(fieldValueList);
                    }
                }
            });
        }
    }

    //@JSONField(serialize = false)
    //public boolean isOpenSuperiorRater() {
    //    return indLevelGroup != null && super.isOpenSuperiorRater();
    //}
    //
    //@JSONField(serialize = false)
    //public boolean isOpenPeerRater() {
    //    return indLevelGroup != null && super.isOpenPeerRater();
    //}
    //
    //@JSONField(serialize = false)
    //public boolean isOpenSubRater() {
    //    return indLevelGroup != null && super.isOpenSubRater();
    //}
    //
    //@JSONField(serialize = false)
    //public boolean isOpenAppointRater() {
    //    return indLevelGroup != null && super.isOpenAppointRater();
    //}

    public List<Rater> allSuperRater() {
        if (typeRule != null) {
            RaterNodeConf superRater = typeRule.getSuperRater();
            if (superRater.isOpen()) {
                return typeRule.getSuperRater().allRater();
            }
        }
        List<Rater> collect = new ArrayList<>();
        List<Rater> typeRaters = typeAllSuperRater();
        log.info("typeRaters:{}", JSONUtil.toJsonStr(typeRaters));
        collect.addAll(typeRaters);//维度流程上级存在为空的,指标确认新增指标就会导致没有考核流程
        if (CollUtil.isEmpty(items)) {//okr或空类别
            return collect;
        }
        List<Rater> collectItems = items.stream().map(EvalKpi::getItemScoreRule)
                .flatMap(itemRule -> itemRule == null || itemRule.getSuperRater() == null
                        ? Stream.empty() : itemRule.builderSuperRater(this.isRaterBack).stream()).collect(Collectors.toList());
        collect.addAll(collectItems);
        return collect;
    }

    private List<Rater> typeAllSuperRater() {
        if (Objects.isNull(superRater) || !superRater.isOpen()) {
            return Collections.emptyList();
        }
        return superRater.builderSuperRater(this.isRaterBack);
    }

    public List<Rater> allPeerRater() {
        if (typeRule != null) {
            RaterNodeConf peerRater = typeRule.getPeerRater();
            if (peerRater.isOpen()) {
                return typeRule.getPeerRater().getRaters();
            }
        }

        if (CollUtil.isEmpty(items)) {//okr或空类别
            return Collections.emptyList();
        }
        List<Rater> peerRaters = items.stream().map(EvalKpi::getItemScoreRule)
                .flatMap(itemRule -> itemRule == null || itemRule.getPeerRater() == null
                        ? Stream.empty() : itemRule.builderPeerRater(this.isRaterBack).stream()).collect(Collectors.toList());
        peerRaters.addAll(peerRaters);
        return peerRaters;
    }

    public List<Rater> allSubRater() {
        if (typeRule != null) {
            RaterNodeConf subRater = typeRule.getSubRater();
            if (subRater.isOpen()) {
                return typeRule.getSubRater().getRaters();
            }
        }
        if (CollUtil.isEmpty(items)) {//okr或空类别
            return Collections.emptyList();
        }
        List<Rater> subRaters = items.stream().map(EvalKpi::getItemScoreRule)
                .flatMap(itemRule -> itemRule == null || itemRule.getSubRater() == null
                        ? Stream.empty() : itemRule.builderSubRater(this.isRaterBack).stream()).collect(Collectors.toList());
        subRaters.addAll(subRaters);
        return subRaters;
    }


    public List<Rater> allAppointRater() {
        if (typeRule != null) {
            RaterNodeConf appointRater = typeRule.getAppointRater();
            if (appointRater.isOpen()) {
                return typeRule.getAppointRater().allRater();
            }
        }
        if (CollUtil.isEmpty(items)) {//okr或空类别
            return Collections.emptyList();
        }
        List<Rater> collect = items.stream().map(EvalKpi::getItemScoreRule)
                .flatMap(itemRule -> itemRule == null || itemRule.getAppointRater() == null
                        ? Stream.empty() : itemRule.builderAppointRater(this.isRaterBack).stream()).collect(Collectors.toList());
        return collect;
    }

    public void reSetInfo(String taskUserId, String taskId) {
        if (getTypeRule() != null) {
            getTypeRule().setId(null);
        }
        this.kpiTypeId = null;
        this.taskUserId = taskUserId;
        if (isOkr() || isEmpty()) {
            //如果复用包含okr维度，不复制okr类别下的指标
            items = new ArrayList<>();
        }
        for (EvalKpi item : items) {
            item.setKpiTypeId(null);
            item.setId(null);
            item.setTaskId(taskId);
            item.setTaskUserId(taskUserId);

            item.setItemFinishValue(null);
            item.setItemFinishValueText(null);
            item.setFinalSubmitFinishValue(0);
            item.setExamineOperType(null);
            item.setItemAutoScore(null);
            item.setAutoScoreExFlag(null);
            item.setUrgingFlag(null);
            item.setWorkItemFinishValue(null);
            item.reSetAttrInfo(taskUserId, taskId);
            item.setCreatedTime(new Date());
            item.setUpdatedTime(new Date());
        }
    }

    public List<EvalRuleOpLogMeta> addNewLog() {
        List<EvalRuleOpLogMeta> rs = new ArrayList<>();
        EvalRuleOpLogMeta logMeta = new EvalRuleOpLogMeta(" 新增了指标类别", getKpiTypeName());
        //文档中新增的类别也有属性
        String before = StrUtil.EMPTY;
        logMeta.addField(EvalRuleLogField.createIf("类别名称", before, kpiTypeName));
        logMeta.addField(EvalRuleLogField.createIf("类别权重", before, kpiTypeWeightName()));
        logMeta.addField(EvalRuleLogField.createIf("类别属性", before, kpiTypeClassifyName()));
        logMeta.addField(EvalRuleLogField.createIf("锁定考核类别", before, lockedItemsName()));
        logMeta.addField(EvalRuleLogField.createIf("限制指标数量", before, itemLimitCntName()));

        rs.add(logMeta);
        for (EvalKpi item : getItems()) {
            rs.addAll(item.newLog());
        }
        return rs;
    }

    public EvalRuleOpLogMeta compare(EmpEvalKpiType after) {
        //List<EvalRuleOpLogMeta> rs = new ArrayList<>(); // 层级变了，指标缩进到类别的下一级
        double kpiTypeWeightVal = this.kpiTypeWeight == null ? 0 : this.kpiTypeWeight.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        double afterKpiTypeWeightVal = after.kpiTypeWeight == null ? 0 : after.kpiTypeWeight.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();

        int thisCode = Objects.hash(kpiTypeName, kpiTypeClassify, lockedItems, kpiTypeWeightVal, isOkr, kpiTypeUsedFields);
        int otherCode = Objects.hash(after.kpiTypeName, after.kpiTypeClassify, after.lockedItems, afterKpiTypeWeightVal, after.isOkr, after.kpiTypeUsedFields);

        EvalRuleOpLogMeta mdType = new EvalRuleOpLogMeta("修改了指标类别", kpiTypeName);
        if (thisCode != otherCode) {
            mdType.addField(EvalRuleLogField.createIf("类别名称", kpiTypeName, after.kpiTypeName));
            mdType.addField(EvalRuleLogField.createKpiIf("类别属性", kpiTypeClassifyName(), after.kpiTypeClassifyName()));
            mdType.addField(EvalRuleLogField.createIf("类别权重", kpiTypeWeightName(), after.kpiTypeWeightName()));
            mdType.addField(EvalRuleLogField.createIf("锁定考核类别", lockedItemsName(), after.lockedItemsName()));
            mdType.addField(EvalRuleLogField.createIf("限制指标数量", itemLimitCntName(), after.itemLimitCntName()));
            mdType.addField(EvalRuleLogField.createIf("完成值录入启用必填选项", kpiTypeUsedFieldUpName(), after.kpiTypeUsedFieldUpName()));
        }
        InnerFields<EvalRuleOpLogMeta> kpiItemMetas = compareItemDiff(after);//指标比较
        if (kpiItemMetas.isNotEmpty()) {
            mdType.addChildren(kpiItemMetas.getFields());
        }
        return mdType;
    }

    private InnerFields<EvalRuleOpLogMeta> compareItemDiff(EmpEvalKpiType after) {
        Map<String, EvalKpi> afterMap = after.getItems().stream().filter(type -> StrUtil.isNotBlank(type.getKpiItemId()))
                .collect(Collectors.toMap(item -> item.getKpiItemId(), v -> v));

        Map<String, EvalKpi> beforeMap = getItems().stream().filter(type -> StrUtil.isNotBlank(type.getKpiItemId()))
                .collect(Collectors.toMap(item -> item.getKpiItemId(), v -> v));
        InnerFields<EvalRuleOpLogMeta> kpiItemMetas = new InnerFields<>();
        for (EvalKpi afterItem : after.getItems()) {
            if (StrUtil.isBlank(afterItem.getKpiItemId())) {
                //新建指标
                List<EvalRuleOpLogMeta> newLogs = afterItem.newLog();
                kpiItemMetas.addAll(newLogs);
                continue;
            }
            if (beforeMap.containsKey(afterItem.getKpiItemId())) {
                EvalKpi beforeType = beforeMap.get(afterItem.getKpiItemId());
                List<EvalRuleOpLogMeta> modify = beforeType.compare(afterItem, after.getTypeKpiSyncReqConf());
                kpiItemMetas.addAll(modify);
            } else {
                //考核表考核设置中新加的指标也拥有 ID
                List<EvalRuleOpLogMeta> newLogs = afterItem.newLog();
                kpiItemMetas.addAll(newLogs);
            }
        }

        for (EvalKpi beforeItem : getItems()) {
            if (!afterMap.containsKey(beforeItem.getKpiItemId())) {
                kpiItemMetas.add(new EvalRuleOpLogMeta("删除了考核指标", beforeItem.getKpiItemName()));
            }
        }
        return kpiItemMetas;
    }

    private String lockedItemsName() {
        if (CollUtil.isEmpty(lockedItems)) {
            return "关闭";
        }
        //存储的是禁止的类型，界面要求展示允许的类型
        Map<String, String> openItems = new HashMap<>();
        //lockedItemCons.put("typeWeight", "修改类别权重"); //新版有专门的配置项 typeWeightConf
        openItems.put("addIndex", "添加指标");
        openItems.put("modifyIndex", "修改指标");
        openItems.put("deleteIndex", "删除指标");

        StringBuilder sb = new StringBuilder("开启");

        for (String lockedItem : lockedItems) {
            openItems.remove(lockedItem);//前端传过来的是禁止项，去掉禁止项之后剩下的是允许项
        }
        if (openItems.size() <= 0) {
            return sb.toString();
        }

        sb.append("(允许");
        int cnt = 0;
        for (Map.Entry<String, String> items : openItems.entrySet()) {
            if (cnt > 0) {
                sb.append("、");
            }
            sb.append(items.getValue());
            cnt++;
        }
        sb.append(")");
        return sb.toString();
    }

    private String kpiTypeUsedFieldUpName() {
        if (CollUtil.isEmpty(kpiTypeUsedFields)) {
            return "-";
        }
        StringBuilder sb = new StringBuilder("");
        for (KpiTypeUsedField usedField : kpiTypeUsedFields) {
            if (Objects.isNull(usedField.getKpiSyncReqConf())) {
                continue;
            }
            if (usedField.isOpenShow()) {
                sb.append(usedField.openName());//开启完成值必填，设置选项 开启
                if (usedField.getKpiSyncReqConf().isOpen()) {
                    sb.append(":").append(usedField.getKpiSyncReqConf().openName() + "，");//开启完成值必填，设置选项 开启
                    sb.append(usedField.getKpiSyncReqConf().getFinishValueLabelName());//开启完成值必填，设置选项
                } else {
                    sb.append(":").append(usedField.getKpiSyncReqConf().openName());//开启完成值必填，设置选项 开启
                }
            } else {
                sb.append(usedField.openName());//开启完成值必填，设置选项 关闭
            }

            break;
        }
        return sb.toString();
    }

    public KpiSyncReqConf getTypeKpiSyncReqConf() {
        if (CollUtil.isEmpty(getKpiTypeUsedFields())) {
            return null;
        }
        KpiSyncReqConf conf = null;
        for (KpiTypeUsedField usedField : getKpiTypeUsedFields()) {
            if (Objects.isNull(usedField.getKpiSyncReqConf())) {
                continue;
            }
            if ("finishValue".equals(usedField.getFieldId())) {
                conf = usedField.getKpiSyncReqConf();
                break;
            }
        }
        return conf;
    }

    private String itemLimitCntName() {
        if (this.itemLimitCnt == null || !this.itemLimitCnt.isOpen()) {
            return "关闭";
        }
        try {
            return itemLimitCnt.isFixRange() ?
                    StrUtil.format("开启(限制{}个指标)", itemLimitCnt.getMax()) :
                    StrUtil.format("开启(限制{}-{}个指标)", itemLimitCnt.getMin(), itemLimitCnt.getMax());
        } catch (Exception e) {
            log.error("转换限制指标个数错误: {}", e.getMessage());
            return "关闭";
        }
    }

    private String kpiTypeClassifyName() {
        boolean isOkr = Boolean.parseBoolean(this.isOkr);
        if (StrUtil.equals(kpiTypeClassify, "plus")) {
            return "加分类" + (isOkr ? "(OKR类)" : "");
        }
        if (StrUtil.equals(kpiTypeClassify, "subtract")) {
            return "减分类" + (isOkr ? "(OKR类)" : "");
        }
        if (StrUtil.equals(kpiTypeClassify, "workItem")) {
            return "工作事项";
        }
        if (StrUtil.isNotBlank(kpiTypeClassify)) {
            return "普通类" + (isOkr ? "(OKR类)" : "");
        }
        return kpiTypeClassify;
    }

    private String kpiTypeWeightName() {
        if (this.kpiTypeWeight == null) {
            return null;
        }
        return StrUtil.format("{}%", this.kpiTypeWeight.setScale(2, BigDecimal.ROUND_HALF_EVEN).intValue());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        EmpEvalKpiType that = (EmpEvalKpiType) o;
        return Objects.equals(kpiTypeName, that.kpiTypeName) && Objects.equals(lockedItems, that.lockedItems) && Objects.equals(kpiTypeClassify, that.kpiTypeClassify);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), kpiTypeName, lockedItems, kpiTypeClassify);
    }

    private void addIndexRater(Map<String, RaterNode> nodeMap, String nodeName, List<RaterNode> ratersNodes, List<Rater> raters) {
        if (raters.isEmpty()) {
            return;
        }
        if (!nodeMap.containsKey(nodeName)) {
            RaterNode node = new RaterNode(nodeName, raters);
            ratersNodes.add(node);
            nodeMap.put(nodeName, node);
        } else {
            nodeMap.get(nodeName).addAllRater(raters);
        }
    }

    public void addIndexRaterOpt(Map<String, RaterNode> nodeMap, List<RaterNode> ratersNodes) {
        RaterNode selfRater = allSelfRater();
        if (Objects.nonNull(selfRater) && !nodeMap.containsKey("self")) {
            nodeMap.put("self", selfRater);
            ratersNodes.add(selfRater);
        }
        addIndexRater(nodeMap, "super", ratersNodes, allSuperRater());
        addIndexRater(nodeMap, "peer", ratersNodes, allPeerRater());
        addIndexRater(nodeMap, "sub", ratersNodes, allSubRater());
        addIndexRater(nodeMap, "appoint", ratersNodes, allAppointRater());
        //addIndexRater(nodeMap, "item", ratersNodes, allAppointRater());
    }

    public void initNodeRater(Map<String, RaterNode> nodeMap) {
        if (CollUtil.isNotEmpty(items)) {
            for (EvalKpi item : items) {
                item.initNodeRater(nodeMap);
            }
        }
        if (isEvalLevel() && isOpenRaterRule()) {
            super.initNodeRater(nodeMap);
        }
    }


    public void initNodeRaterV3(Map<String, RaterNode> nodeMap) {
        if (typeRule != null) {
            initNodeRaterForTypeRuleExists(nodeMap);
            return;
        }
        if (isEvalLevel() || (isOkr() && this.openOkrScore == 0)) {
            super.initNodeRater(nodeMap);
        }
        initNodeRaterForItems(nodeMap);
    }

    private void initNodeRaterForTypeRuleExists(Map<String, RaterNode> nodeMap) {
        List<EvalKpi> items = this.getItems().stream()
                .filter(item -> item.isNormal() && Objects.isNull(item.getItemScoreRule())) //普通指标，且无指标流程的
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(items)) {//普通指标，且无指标流程的 如果无指标，则无需解析维度流程，仅需init指标流程评价人
            typeRule.initNodeRater(nodeMap);
        }
        this.initNodeRaterForItems(nodeMap);
    }

    private void initNodeRaterForItems(Map<String, RaterNode> nodeMap){
        if (CollUtil.isEmpty(items)) {//okr或空类别
            return;
        }
        for (EvalKpi item : items) {
            if(item.itemNoNeedScore(this.scoreOptType)){
                continue;
            }
            item.initNodeRater(nodeMap);
        }
    }


    public void initAskNodeRater(Map<String, RaterNode> nodeMap) {
        super.initNodeRater(nodeMap);
    }

    public void accpItems(List<EvalKpi> items, ListWrap<EvalItemScoreRule> ruleWrap) {

        if (CollUtil.isEmpty(items)) {
            return;
        }
        this.items = items;
        for (EvalKpi item : items) {
            item.setItemScoreRule(ruleWrap.mapGet(item.getKpiItemId()));
        }
    }

    public void initBaseInfo(TenantId tenantId, EmpId opEmpId, String evalUserId) {
        this.companyId = tenantId;
        this.updatedUser = opEmpId.getId();
        this.createdUser = opEmpId.getId();
        this.createdTime = new Date();
        this.updatedTime = new Date();
        this.taskUserId = evalUserId;
        if (CollUtil.isEmpty(items)) {
            return;
        }
        items.forEach(item -> {
            item.initBaseInfo(tenantId, opEmpId, evalUserId, kpiTypeId);
        });
    }

    public void checkReSubmited(ListWrap<EvalScoreResult> toSubmitWrap, EvalScoreResult total) {
        for (EvalKpi item : items) {
            EvalScoreResult submited = toSubmitWrap.mapGet(item.getKpiItemId());
            if (submited == null) {//提交的不是此分类的指标
                continue;
            }
            item.checkReSubmited(submited, pecWeight());
        }
    }

    public List<EvalScoreResult> submitItemScore(ListWrap<EvalScoreResult> submits, boolean submitWithWeight) {
        List<EvalScoreResult> computedRs = new ArrayList<>();
        for (EvalKpi item : items) {
            EvalScoreResult submited = submits.mapGet(item.getKpiItemId());
            if (submited == null) {//提交的不是此分类的指标
                continue;
            }
            submited.passed();
            BigDecimal okrGoalWeight = this.okrGoalWeight(item.getActionId());
            List<EvalScoreResult> computed = item.computeItemScore(submited, okrGoalWeight, submitWithWeight);
            computedRs.addAll(computed);
        }
        return computedRs;
    }

    public List<EvalScoreResult> fixItemScore(boolean submitWithWeight) {
        List<EvalScoreResult> computedRs = new ArrayList<>();
        for (EvalKpi item : items) {
            BigDecimal okrGoalWeight = this.okrGoalWeight(item.getActionId());
            List<EvalScoreResult> results = item.fixItemScore(okrGoalWeight, submitWithWeight);
            computedRs.addAll(results);
        }
        return computedRs;
    }

    public BigDecimal okrGoalWeight(String keyRsId) {
        if (!isOkr()) {
            return this.pecWeight();
        }
        if (CollUtil.isEmpty(okrGoals)) {
            return this.pecWeight();
        }
        for (OkrGoal okrGoal : okrGoals) {
            boolean contain = okrGoal.getKeyResults().stream().anyMatch(kr -> StrUtil.equals(kr.getKeyResultId(), keyRsId));
            if (contain) {
                return okrGoal.getWeight().divide(Pecent.ONE_HUNDRED).multiply(this.pecReserveOkrWeight());
            }
        }
        return this.pecReserveOkrWeight();
    }

    private BigDecimal pecReserveOkrWeight() {
        return reserveOkrWeight.divide(Pecent.ONE_HUNDRED);
    }

    //类别权重100比
    public BigDecimal pecWeight() {
        return kpiTypeWeight.divide(Pecent.ONE_HUNDRED);
    }

    //标准化权重
    public void initWeight(boolean openTypeWeight, boolean submitWithWgt) {
        if (isAskType() && !openTypeWeight){
            return;
        }
        if (!openTypeWeight) {
            this.kpiTypeWeight = Pecent.ONE_HUNDRED;
        }
        if (openTypeWeight && kpiTypeWeight == null) {
            kpiTypeWeight = BigDecimal.ZERO;
        }
        for (EvalKpi item : items) {
            item.initItemWeight(submitWithWgt);
        }
        if (isOkr() && openTypeWeight) {//开启维度权重计算时预留权重reserveOkrWeight与 维度权重 kpiTypeWeight保持一致
            this.reserveOkrWeight = kpiTypeWeight;
        }
    }

    public EmpEvalKpiType clone() {
        try {
            return (EmpEvalKpiType) super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
            return null;
        }
    }

    public List<EvalKpi> filter(Predicate<? super EvalKpi> condition) {
        if (CollUtil.isEmpty(items)) {
            return Collections.emptyList();
        }
        return items.stream().filter(condition).collect(Collectors.toList());
    }

    public void refEmpId(String taskId, String empId) {
        if (CollUtil.isEmpty(items)) {
            return;
        }
        items.forEach(item -> {
            item.setTaskId(taskId);
            item.setEmpId(empId);
        });
    }

    public void extendsRaterRuleForEdit(EvalItemScoreRule global) {
        //预留在分类上的指标流程, 当确认流程完成时自动会增加到指标上 故不用执行
        if (typeRule != null) {
            return;
        }
        if (CollUtil.isEmpty(items)) {
            return;
        }
        for (EvalKpi item : items) {
            //指标上没有流程, 尝试继承维度上有的流程
            EvalItemScoreRule typeRule = super.isOpenRaterRule() ? this.forItemRule(item.getKpiItemId()) : null;
            item.extendsRaterRuleOpt(global, typeRule);
        }
    }

    @Deprecated
    public void extendsRaterRuleOptOld(EvalItemScoreRule global) {
        //预留在分类上的指标流程, 当确认流程完成时自动会增加到指标上 故不用执行
        if (typeRule != null) {
            return;
        }
        if (CollUtil.isEmpty(items)) {
            return;
        }
        for (EvalKpi item : items) {
            //指标上没有流程, 尝试继承维度上有的流程
            EvalItemScoreRule typeRule = super.isOpenRaterRule() ? this.forItemRule(item.getKpiItemId()) : null;
            item.extendsRaterRuleOpt(global, typeRule);
        }
    }

    public void extendsRaterRuleOpt(EvalItemScoreRule global) {
        //预留在分类上的指标流程, 当确认流程完成时自动会增加到指标上 故不用执行
//        if (typeRule != null) {
//            return;
//        }
        //维度上没有流程则先继承 使用默认流程
        if (CollUtil.isNotEmpty(items)) {
            for (EvalKpi item : items) {
                if (this.typeRule != null) {
                    item.extendsRaterRuleOpt(global, this.typeRule);
                    continue;
                }
                //指标上没有流程, 尝试继承维度上有的流程
                EvalItemScoreRule typeRule = super.isOpenRaterRule() ? this.forItemRule(item.getKpiItemId()) : null;
                item.extendsRaterRuleOpt(global, typeRule);
            }
        }
        if (isEvalLevel() || isAskType()) {
            this.extendsGlobalRaterRule(global);
        }
    }

    public void extendsGlobalRaterRule(EvalItemScoreRule global) {
        if (super.isOpenRaterRule()) {
            return;
        }
        if (this.typeRule != null) {
            return;
        }
        this.typeRule = this.forItemRule(null);
        BeanUtils.copyProperties(global, typeRule);
        this.typeRule.setExtend(1);
        this.typeRule.setKpiTypeId(this.getKpiTypeId());
        this.typeRule.asRaterConf(typeRule.getSelfRater(), typeRule.getPeerRater(), typeRule.getSubRater(), typeRule.getSuperRater(), typeRule.getAppointRater());

        this.selfRater = new RaterNodeConf().extend(global.getSelfRater(), 1);
        this.peerRater = new MutualNodeConf().extend(global.getPeerRater(), 1);
        this.subRater = new MutualNodeConf().extend(global.getSubRater(), 1);
        this.superRater = new S3SuperRaterConf().extend(global.getSuperRater(), 1);

        if (Objects.isNull(global.getAppointRater())) {
            this.appointRater = new S3RaterBaseConf();
        } else {
            this.appointRater = global.getAppointRater();
        }
        this.typeRule = global;
    }

    @JSONField(serialize = false)
    public boolean isPlusType() {
        return BusinessConstant.KPI_TYPE_CLASSIFY_PLUS.equals(kpiTypeClassify);
    }

    @JSONField(serialize = false)
    public boolean isPlusSubType() {
        return BusinessConstant.KPI_TYPE_CLASSIFY_PLUS_SUB.equals(kpiTypeClassify);
    }

    @JSONField(serialize = false)
    public boolean isVoteType() {
        return "oneVoteVeto".equals(getKpiTypeClassify());
    }


    @JSONField(serialize = false)
    public boolean isSubtractType() {
        return BusinessConstant.KPI_TYPE_CLASSIFY_SUBTRACT.equals(kpiTypeClassify);
    }

    @JSONField(serialize = false)
    public boolean isWorkItemType() {
        return BusinessConstant.KPI_TYPE_CLASSIFY_WORK_ITEM.equals(kpiTypeClassify);
    }

    //领域返回 预留的指标的评分的流程
    private EvalItemScoreRule typeRule;

    @JSONField(name = "typeEvaluate", serialize = false)
    private ItemScoreRuleAtType oldTypeEvalRule;

    public EmpEvalKpiType(ItemScoreRuleAtType oldTypeEvalRule) {
        this.oldTypeEvalRule = oldTypeEvalRule;
    }

    //方法spring 等框架会调用, 请保留
    public void setOldTypeEvalRule(ItemScoreRuleAtType oldTypeEvalRule) {
        this.oldTypeEvalRule = oldTypeEvalRule;
        this.typeRule = oldTypeEvalRule != null ? oldTypeEvalRule.asRule() : null;
    }

    public void buildTypeRule(EvalItemScoreRule v1Rule){
        if (Objects.nonNull(v1Rule)){
            this.typeRule = v1Rule;
            return;
        }
        EvalItemScoreRule newTypeRule = new EvalItemScoreRule();
        BeanUtil.copyProperties(this, newTypeRule);
        if (super.isOpenRaterRule()){
            newTypeRule.customAsRaterConf();
            this.typeRule = newTypeRule;
        }
    }

    public void buildTypeRule(){
        if (super.isOpenRaterRule()){
            EvalItemScoreRule newTypeRule = new EvalItemScoreRule();
            BeanUtil.copyProperties(this, newTypeRule);
            newTypeRule.customAsRaterConf();
            this.typeRule = newTypeRule;
        }
    }


    public boolean filterWaitScore(boolean keepAllItem, boolean keepAutoItems, Map<String, KpiEmp> empMap, String opEmpId, String reScoreNode,
                                   boolean isOpenAvgWeightCompute) {
        buildAlreadyNodes(empMap, new EmpId(opEmpId), reScoreNode, isOpenAvgWeightCompute);
        Stream<PerfEvalTypeResult> itemRsStream = waitScoresOld.stream()
                .filter(typeRs -> !typeRs.isWaitDispatch())
                .filter(typeRs -> StrUtil.equals(typeRs.getScorerId(), opEmpId));
        if (StrUtil.isNotBlank(reScoreNode)) {//重新评分
            waitScoresOld = itemRsStream.filter(itemRs -> StrUtil.equals(itemRs.getScorerType(), reScoreNode)).collect(Collectors.toList());
        } else {
            waitScoresOld = itemRsStream.filter(itemRs -> !itemRs.isPassed()).collect(Collectors.toList());
        }
        List<EvalKpi> keepItems = new ArrayList<>();
        for (EvalKpi item : items) {
            //包含任一指标要评分指标
            if (item.filterWaitScore(keepAllItem, keepAutoItems, empMap, opEmpId, reScoreNode,isOpenAvgWeightCompute)) {
                keepItems.add(item);
            }
        }
        this.items = keepItems;
        //指定要查看所有指标|需要评维度|需要评维度下指标
        return keepAllItem || CollUtil.isNotEmpty(waitScoresOld) || CollUtil.isNotEmpty(keepItems);
    }
    public boolean filterWaitScoreV3(boolean keepAllItem,boolean keepAutoItems, String opEmpId, String reScoreNode,
                                   boolean isOpenAvgWeightCompute) {
        buildAlreadyNodesV3(new EmpId(opEmpId), reScoreNode, isOpenAvgWeightCompute);
        Stream<EvalScorerNodeKpiType> itemRsStream = waitScores.stream()
                .filter(typeRs -> !typeRs.isWaitDispatch())
                .filter(typeRs -> StrUtil.equals(typeRs.getScorerId(), opEmpId));
        if (StrUtil.isNotBlank(reScoreNode)) {//重新评分
            waitScores = itemRsStream
                    .filter(itemRs -> StrUtil.equals(itemRs.getScorerType(), reScoreNode)
                            && itemRs.isCanCompute())//待提交的需要是可以计算的指标
                    .collect(Collectors.toList());
        } else {
            waitScores = itemRsStream
                    .filter(itemRs -> !itemRs.isPassed() && itemRs.isCanCompute()) //待提交的需要是可以计算的指标
                    .collect(Collectors.toList());
        }
        List<EvalKpi> keepItems = new ArrayList<>();
        for (EvalKpi item : items) {
            //包含任一指标要评分指标
            if (item.filterWaitScoreV3(keepAllItem,keepAutoItems, opEmpId, reScoreNode)) {
                keepItems.add(item);
            }
        }
        this.items = keepItems;
        //指定要查看所有指标|需要评维度|需要评维度下指标
        return keepAllItem || CollUtil.isNotEmpty(waitScores) || CollUtil.isNotEmpty(keepItems);
    }

    public void acceptTypeRs(List<PerfEvalTypeResult> results) {
        this.waitScoresOld = results;
    }

    public void acceptScorerNodeTypeRs(List<EvalScorerNodeKpiType> results) {
        this.waitScores = results;
    }


    public void buildAlreadyNodes(Map<String, KpiEmp> empMap, EmpId opEmpId, String reSubmitNode, boolean isOpenAvgWeightCompute) {
        List<PerfEvalTypeResult> alreadys = waitScoresOld.stream().filter(typeRs -> typeRs.isPassed()).collect(Collectors.toList());
        ListWrap<PerfEvalTypeResult> nodeGroup = new ListWrap<>(alreadys).groupBy(scr -> scr.getScorerType());
        nodeGroup.getGroups().forEach((node, results1) -> {
            NodeOfItemScore nodeOfItemScore = new NodeOfItemScore(node);
            Set<String> scoreIds = new HashSet<>();
            for (PerfEvalTypeResult srs : results1) {
                KpiEmp kpiEmp = empMap.get(srs.getScorerId());
                NodeOfItemScore.ScoreResultOfItem ofItem = new NodeOfItemScore.ScoreResultOfItem(kpiEmp.getEmpName(), kpiEmp.getAvatar(), srs.getScorerId(), Objects.isNull(srs.getUpdatedTime()) ? srs.getCreatedTime() : srs.getUpdatedTime());
                ofItem.scoreWeight = srs.getScoreWeight();
                ofItem.acceptComment(srs.getScoreAttUrl(), srs.getScoreComment());
                ofItem.level = srs.scoreLevel;
                //加减分项分数统计和正常指标得分不是一个字段里面
                nodeOfItemScore.addItem(ofItem);
                if (null != srs.getScoreWeight() && srs.getScoreWeight().compareTo(BigDecimal.ZERO) != 0) {
                    scoreIds.add(srs.getScorerId());
                }
            }
            buildNodeOfItemScore(nodeOfItemScore, isOpenAvgWeightCompute, scoreIds);//互评计算平均分
            this.alreadyScores.add(nodeOfItemScore);
        });
        if (StrUtil.isNotBlank(reSubmitNode)) {
            waitScoresOld.removeIf(typeRs -> typeRs.isPassed() && !typeRs.matchBelong(reSubmitNode, opEmpId));
        } else {
            if (CollUtil.isNotEmpty(alreadys)) {
                List<String> resIds = alreadys.stream().map(s -> s.getId()).collect(Collectors.toList());
                waitScoresOld = waitScoresOld.stream().filter(s -> !resIds.contains(s.getId())).collect(Collectors.toList());
            }

        }

        if (CollUtil.isEmpty(this.getItems())) {
            return;
        }
        for (EvalKpi item : this.getItems()) {
            item.buildAlreadyNodesV3(opEmpId,reSubmitNode);
        }
    }

    public void buildAlreadyNodesV3(EmpId opEmpId, String reSubmitNode, boolean isOpenAvgWeightCompute) {
        List<EvalScorerNodeKpiType> alreadys = waitScores.stream().filter(EvalScorerNodeScoreItemBase::isPassed).collect(Collectors.toList());
        ListWrap<EvalScorerNodeKpiType> nodeGroup = new ListWrap<>(alreadys).groupBy(EvalScorerNodeScoreItemBase::getScorerType);
        waitScores.removeIf(EvalScorerNodeKpiType::isWaitDispatch);
        nodeGroup.getGroups().forEach((node, results1) -> {
            NodeOfItemScore nodeOfItemScore = new NodeOfItemScore(node);
            List<EvalScorerNodeKpiType>  tempResults = filterRepeat(results1);
            Set<String> scoreIds = new HashSet<>();
            //排除不可以展示的kpiItem
            //加减分项分数统计和正常指标得分不是一个字段里面
            tempResults.stream().filter(EvalScorerNodeScoreItemBase::isShow).forEachOrdered(srs -> {
                NodeOfItemScore.ScoreResultOfItem ofItem = new NodeOfItemScore.ScoreResultOfItem(srs.getScorerName(), srs.getScorerAvatar(), srs.getScorerId(), Objects.isNull(srs.getUpdatedTime()) ? srs.getCreatedTime() : srs.getUpdatedTime());
                ofItem.scoreWeight = srs.getScoreWeight();
                ofItem.acceptComment(srs.getScoreAttUrl(), srs.getScoreComment());
                ofItem.level = srs.getScoreLevel();
                ofItem.auditStatus = srs.getAuditStatus();
                nodeOfItemScore.addItem(ofItem);
                if (null != srs.getScoreWeight() && srs.getScoreWeight().compareTo(BigDecimal.ZERO) != 0) {
                    scoreIds.add(srs.getScorerId());
                }
            });
            buildNodeOfItemScore(nodeOfItemScore, isOpenAvgWeightCompute, scoreIds);//互评计算平均分
            this.alreadyScores.add(nodeOfItemScore);
        });
        if (StrUtil.isNotBlank(reSubmitNode)) {
            waitScores.removeIf(typeRs -> typeRs.isPassed()
                    && !typeRs.matchBelong(reSubmitNode, opEmpId)
                    && typeRs.isCanCompute());
        } else {
            if (CollUtil.isNotEmpty(alreadys)){
                List<String> resIds = alreadys.stream().map(EvalScorerNodeKpiType::getId).collect(Collectors.toList());
                waitScores = waitScores.stream()
                        .filter(s -> !resIds.contains(s.getId()))
                        .filter(EvalScorerNodeScoreItemBase::isCanCompute) //待提交的需要是可以计算的指标
                        .collect(Collectors.toList());
            }
        }

        waitScores =  filterRepeat(waitScores);//去重
        if (CollUtil.isEmpty(this.getItems())) {
            return;
        }
        for (EvalKpi item : this.getItems()) {
            item.buildAlreadyNodesV3(opEmpId,reSubmitNode);
        }
    }

    private List<EvalScorerNodeKpiType> filterRepeat(List<EvalScorerNodeKpiType>  waitScores){
        if (CollUtil.isEmpty(waitScores)) {
            return new ArrayList<>();
        }

        //waitScores  asScorerIdAndOrderKey 去重
        Set<String> uniqueKeys = new HashSet<>();
        return waitScores.stream()
                .filter(e -> uniqueKeys.add(e.asScorerIdAndOrderKey()))
                .collect(Collectors.toList());
    }

    private NodeOfItemScore buildNodeOfItemScore(NodeOfItemScore nodeOfItemScore, boolean isOpenAvgWeightCompute, Set<String> scoreIds) {
        if (isOpenAvgWeightCompute || CollUtil.isEmpty(scoreIds) || !EvaluateAuditSceneEnum.mutualScoreTypes().contains(nodeOfItemScore.getNode())) {
            return nodeOfItemScore;
        }

        int scoreNum = scoreIds.size();//互评计算平均分
        MutualScoreResultCompute compute = new MutualScoreResultCompute(nodeOfItemScore.getScore(), scoreNum);
        nodeOfItemScore.setScore(compute.computeAvgScore());
        log.info(" buildNodeOfItemScore.nodeOfItemScore:{},scoreNum:{}", JSONUtil.toJsonStr(nodeOfItemScore), scoreNum);
        return nodeOfItemScore;
    }

    public EvalItemScoreRule forItemRule(String itemId) {
        EvalItemScoreRule forItem = super.asItemRule(kpiTypeId, itemId);
        forItem.setCompanyId(companyId);
        forItem.setTaskUserId(taskUserId);
        return forItem;
    }


    public List<PerfEvalTypeResult> submitTypeScore(PerfEvalTypeResult submited, String opEmpId) {
        ListWrap<PerfEvalTypeResult> mapwrap = new ListWrap<>(waitScoresOld).asMap(perfEvalTypeResult -> perfEvalTypeResult.getId());
        PerfEvalTypeResult oldWait = mapwrap.mapGet(submited.getId());
        oldWait.submitSrcScore(submited, opEmpId);
        needUpdateLevel(oldWait);
        if (oldWait.isOrMode()) {
            List<PerfEvalTypeResult> collect = waitScoresOld.stream().filter(waitScore -> waitScore.isSameNode(oldWait)).collect(Collectors.toList());
            collect.forEach(waitScore -> waitScore.setAuditStatus(BusinessConstant.PASS));
            return collect;
        }
        return Arrays.asList(oldWait);
    }

    public void needUpdateLevel(PerfEvalTypeResult submited) {
        if (!this.isEvalLevel()) {
            submited.setUpdateTypeLevel(false);
            return;
        }
        List<PerfEvalTypeResult> alreadys = waitScoresOld.stream().filter(typeRs -> typeRs.isPassed())
                .filter(typeRs -> typeRs.getKpiTypeId().equals(submited.getKpiTypeId()))
                .collect(Collectors.toList());
        boolean containSup = new ListWrap<>(alreadys).mapTo(PerfEvalTypeResult::getScorerType).contains(SubScoreNodeEnum.SUPERIOR_SCORE.getScene());
        if (!containSup || (containSup && submited.isSupper())) {
            submited.setUpdateTypeLevel(true);
            return;
        }
    }

    public boolean matchAnySuperScore(String scorerId) {
        for (PerfEvalTypeResult waitScore : waitScoresOld) {
            if (waitScore.matchBelong(SubScoreNodeEnum.SUPERIOR_SCORE.getScene(), new EmpId(scorerId))) {
                return true;
            }
        }
        return false;
    }

    public boolean matchAnySuperScoreV3(String scorerId) {
        for (EvalScorerNodeKpiType waitScore : waitScores) {
            if (waitScore.matchBelong(SubScoreNodeEnum.SUPERIOR_SCORE.getScene(), new EmpId(scorerId))) {
                return true;
            }
        }
        return false;
    }

    public boolean matchAnySelfScore(String scorerId) {
        for (PerfEvalTypeResult waitScore : waitScoresOld) {
            if (waitScore.matchBelong(SubScoreNodeEnum.SELF_SCORE.getScene(), new EmpId(scorerId))) {
                return true;
            }
        }
        return false;
    }
    public boolean matchAnySelfScoreV3(String scorerId) {
        for (EvalScorerNodeKpiType waitScore : waitScores) {
            if (waitScore.matchBelong(SubScoreNodeEnum.SELF_SCORE.getScene(), new EmpId(scorerId))) {
                return true;
            }
        }
        return false;
    }
    public void replaceProperty(String taskUserId) {
        this.taskUserId = taskUserId;
    }

    public void initAudit(List<EvalAudit> ratersNodes, EmpId empId) {
        for (EvalKpi item : items) {
            EvalItemScoreRule rule = item.getItemScoreRule();
            S3SuperRaterConf superRater = rule.getSuperRater();
            if (superRater != null) {
                for (BaseAuditNode an : superRater.getAuditNodes()) {
                    addAudit(EvaluateAuditSceneEnum.SUPERIOR_SCORE.getScene(), an.getApproverType(), an.getApproverInfo(), ratersNodes, empId);
                }
            }
            MutualNodeConf peerRater = rule.getPeerRater();
            if (peerRater != null) {
                addAudit(EvaluateAuditSceneEnum.PEER_SCORE.getScene(), peerRater.getApproverType(), peerRater.getApproverInfo(), ratersNodes, empId);
            }
            MutualNodeConf subRater = rule.getSubRater();
            if (subRater != null) {
                addAudit(EvaluateAuditSceneEnum.SUB_SCORE.getScene(), subRater.getApproverType(), peerRater.getApproverInfo(), ratersNodes, empId);
            }
            S3RaterBaseConf appointRater = rule.getAppointRater();
            if (appointRater != null) {
                for (BaseAuditNode an : appointRater.getAuditNodes()) {
                    addAudit(EvaluateAuditSceneEnum.SUPERIOR_SCORE.getScene(), an.getApproverType(), an.getApproverInfo(), ratersNodes, empId);
                }
            }

        }

    }

    private void addAudit(String scene, String approverType, String approverInfo, List<EvalAudit> ratersNodes, EmpId empId) {
        EvalAudit tmpAudit = new EvalAudit(approverType, approverInfo, companyId, empId, empId);
        tmpAudit.setScene(scene);
        ratersNodes.add(tmpAudit);
    }

    public JSONObject toSimpleJson(Integer no, boolean typeWeightOpen) {
        JSONObject object = new JSONObject();
        object.put("no", no);
        object.put("showLine", true);
        String weight = "";
        if (!typeWeightOpen) {
            BigDecimal sum = new BigDecimal(0);
            for (EvalKpi item : items) {
                sum = sum.add(item.getItemWeight());
            }
            weight = sum.intValue() + "%";
        } else {
            weight = kpiTypeWeight.intValue() + "%";
        }
        String name = String.format("%s:(%s项, %s)", kpiTypeName, items == null ? 0 : items.size(), weight);
        object.put("name", name);
        return object;
    }

    public static void main(String[] args) {
        String kpiTypeName = String.format("%s:(%s项, %s)", "kpiTypeName", 1, "20%");
        System.out.println(kpiTypeName);
    }

    public EvalKpi getKpiItem(String itemId) {
        if (CollUtil.isEmpty(items)) {
            return null;
        }
        for (EvalKpi item : items) {
            if (StrUtil.equals(item.getKpiItemId(), itemId)) {
                return item;
            }
        }
        return null;
    }

    //代码重复 ,PerfEvalTypeResult
    public boolean isDispatched(SubScoreNodeEnum node, int nodeOrder) {
        List<PerfEvalTypeResult> scoreRsGroup = new ListWrap<>(waitScoresOld).groupBy(r -> r.getScorerType()).groupGet(node.getScene());
        if (CollUtil.isEmpty(scoreRsGroup)) {//当前指标下)已无需要提交的node环节(不包含未分发的
            return false;
        }
        List<PerfEvalTypeResult> matchOrders = scoreRsGroup.stream().filter(rs -> rs.getApprovalOrder() == nodeOrder && !rs.isWaitDispatch()).collect(Collectors.toList());
        return CollUtil.isNotEmpty(matchOrders);
    }

    //代码重复
    public boolean nodeIsEnd(SubScoreNodeEnum node, int nodeOrder) {
        List<PerfEvalTypeResult> scoreRsGroup = new ListWrap<>(waitScoresOld).groupBy(r -> r.getScorerType()).groupGet(node.getScene());
        if (CollUtil.isEmpty(scoreRsGroup)) {//当前指标下)已无需要提交的node环节(不包含未分发的
            return true;
        }
        List<PerfEvalTypeResult> matchOrders = scoreRsGroup.stream().filter(rs -> rs.getApprovalOrder() == nodeOrder).collect(Collectors.toList());
        if (matchOrders.isEmpty()) {//当前指标无此层级
            return true;
        }
        PerfEvalTypeResult lastRs = null;
        for (PerfEvalTypeResult result : scoreRsGroup) {
            if (result.getApprovalOrder() != nodeOrder) {
                continue;
            }
            lastRs = result;
            if (result.isAndMode() && !result.isPassed()) {
                return false;
            }
            if (result.isOrMode() && result.isPassed()) {
                return true;
            }
            //if (result.isItemNode()) {
            //    return true;
            //}
        }
        if (lastRs == null) {
            return false;
        }
        return lastRs.isAndMode() ? true : false;
    }

    public List<EvalScoreResult> listItemScore(SubScoreNodeEnum node, String scorerId) {
        return items.stream().flatMap(item -> item.listScoreRs(node, scorerId).stream()).collect(Collectors.toList());
    }

    public boolean needShowInputField() {
        if (this.isAllItemClosedInput()) {
            return false;
        }
        return isOpenFinishValue();
    }

    @JSONField(serialize = false)
    public boolean isAllItemClosedInput() {
        for (EvalKpi item : items) {
            if (!item.isNotNeedInput()) {
                return false;
            }
        }
        return true;
    }

    public boolean needShowTargetField() {
        for (EvalKpi item : items) {
            if (item.isOpenItemField()) {
                return true;
            }
        }
        return isOpenTargetValue();
    }

    public boolean needShowItemRuleField() {
        if (CollUtil.isNotEmpty(this.kpiTypeUsedFields)) {
            Map<String, KpiTypeUsedField> fieldMap = CollUtil.toMap(this.kpiTypeUsedFields, new HashMap<>(), field -> field.getFieldId());
            if (fieldMap.get("standard") != null) {
                return true;
            }
        }
        return false;
    }

    public boolean needShowScoringRuleField() {
        if (CollUtil.isNotEmpty(this.kpiTypeUsedFields)) {
            Map<String, KpiTypeUsedField> fieldMap = CollUtil.toMap(this.kpiTypeUsedFields, new HashMap<>(), field -> field.getFieldId());
            if (fieldMap.get("scoreRule") != null) {
                return true;
            }
        }
        return false;
    }

    public boolean needShowWeightField() {
        if (CollUtil.isNotEmpty(this.kpiTypeUsedFields)) {
            Map<String, KpiTypeUsedField> fieldMap = CollUtil.toMap(this.kpiTypeUsedFields, new HashMap<>(), field -> field.getFieldId());
            if (fieldMap.get("indexWeight") != null) {
                return true;
            }
        }
        return false;
    }

    public boolean needShowColumnFiled() {
        if (CollUtil.isNotEmpty(this.kpiTypeUsedFields)) {
            return true;
        }
        return false;
    }


    public void initTypeSignatureFlagIfOpt() {
        if (CollUtil.isEmpty(this.waitScoresOld)) {
            return;
        }
        for (PerfEvalTypeResult waitScore : this.waitScoresOld) {
            waitScore.installSignatureFlag(this.typeRule);
        }
    }

    public void initItemSignatureFlagIfOpt(boolean custom, RaterNodeConf s3SelfRater, MutualNodeConf s3PeerRater, MutualNodeConf s3SubRater, S3SuperRaterConf s3SuperRater, S3RaterBaseConf s3AppointRater) {
        if (CollUtil.isEmpty(this.getItems())) {
            return;
        }
        for (EvalKpi item : this.items) {
            item.initSignatureFlagIfOpt(custom, s3SelfRater, s3PeerRater, s3SubRater, s3SuperRater, s3AppointRater);
        }
    }

    public void setItemRule(List<EvalItemScoreRule> groupGet) {
        if (CollUtil.isEmpty(groupGet)) {
            return;
        }
        List<EvalItemScoreRule> typeRules = groupGet.stream().filter(scoreRule -> scoreRule.isTypeRule()).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(typeRules)) {
            this.typeRule = typeRules.get(0);
        }
        if (CollUtil.isEmpty(this.items)) {
            return;
        }
        ListWrap<EvalItemScoreRule> wrap = new ListWrap<>(groupGet).asMap(EvalItemScoreRule::getKpiItemId);
        for (EvalKpi item : this.items) {
            item.setItemScoreRule(wrap.mapGet(item.getKpiItemId()));
        }
    }

    public void parseStaffConfTypeScorer(String empId, String evalOrgId, String orgId, Set<ChangeInfo> infos, EvalChangeDmSvc dmSvc) {
        if (isFinishValueAudit()) {
            dmSvc.parseStaffConfTypeScorer(empId, evalOrgId, orgId, this.finishValueAudit, infos);
        }
        if (CollUtil.isEmpty(this.items)) {
            return;
        }
        this.items.forEach(item -> {
            dmSvc.parseStaffConfTypeScorer(empId, evalOrgId, orgId, item.getFinishValueAudit(), infos);
        });
    }

    public void parseInputFinishValueRaters(String evalOrgId, String orgId, Set<ChangeInfo> infos, EvalChangeDmSvc dmSvc) {
        if (CollUtil.isEmpty(this.getItems())) {
            return;
        }
        for (EvalKpi item : this.items) {
            item.parseInputFinishValueRaters(evalOrgId, orgId, infos, dmSvc);
        }
    }

    @JSONField(serialize = false)
    public boolean isFinishValueAudit() {
        if (CollUtil.isEmpty(this.finishValueAudit)) {
            return false;
        }
        List<StaffConfItem> confItemList = this.finishValueAudit.stream().filter(audit -> "taskEmp".equals(audit.getObjType()) || (!"taskEmp".equals(audit.getObjType()) && !audit.getObjItems().isEmpty())).collect(Collectors.toList());
        return confItemList.size() > 0;
    }

    public void changeItemInput(ListWrap<EvalKpi> itemWrap) {
        if (CollUtil.isEmpty(this.items)) {
            return;
        }
        for (EvalKpi item : this.items) {
            item.changeInput(itemWrap.mapGet(item.getKpiItemId()));
        }
    }

    public void changeTypeInputAudit(ListWrap<EmpEvalKpiType> typeWrap) {
        EmpEvalKpiType kpiType = typeWrap.mapGet(this.kpiTypeId);
        if (Objects.nonNull(this.finishValueAudit)) {
            if (Objects.nonNull(kpiType)) {
                this.finishValueAudit = kpiType.getFinishValueAudit();
            }
        }
        if (CollUtil.isEmpty(this.items)) {
            return;
        }
        ListWrap<EvalKpi> itemWrap = new ListWrap<>(kpiType.getItems()).asMap(EvalKpi::getKpiItemId);
        for (EvalKpi item : this.items) {
            item.changeInputAudit(itemWrap.mapGet(item.getKpiItemId()));
        }
    }

    public void changeTypeScore(EmpEvalKpiType mapGet) {
        if (Objects.isNull(mapGet)) {
            return;
        }
        this.peerRater = mapGet.getPeerRater();
        this.subRater = mapGet.getSubRater();
        this.superRater = mapGet.getSuperRater();
        this.appointRater = mapGet.getAppointRater();
        this.typeRule = mapGet.getTypeRule();
        if (CollUtil.isEmpty(this.items)) {
            return;
        }
        ListWrap<EvalKpi> itemWrap = new ListWrap<>(mapGet.getItems()).asMap(EvalKpi::getKpiItemId);
        for (EvalKpi item : this.items) {
            item.changeItemScore(itemWrap.mapGet(item.getKpiItemId()));
        }
    }

    public void loadInputEmps(ListWrap<KpiEmp> emps) {
        if (CollUtil.isEmpty(this.items)) {
            return;
        }
        this.items.forEach(item -> item.loadInputEmps(emps));
    }

    public void distinctKpiTypeUsedFields() {
        this.getKpiTypeUsedFields().stream().distinct();
    }

    public void finishAudit2Item() {
        if (CollUtil.isEmpty(this.getItems())) {
            return;
        }
        for (EvalKpi item : items) {
            item.setFinishValueAudit(this.finishValueAudit);
        }
    }

    public List<RaterNode> typeRaterCheck() {
        List<RaterNode> ratersNodes = new ArrayList<>();
        if (selfRater != null && selfRater.isOpen()) {
            ratersNodes.add(new RaterNode("self", new Rater(null, 5)));
        }
        if (superRater != null && superRater.isOpen()) {
            ratersNodes.add(new RaterNode("super", superRater.allOrNullRater(), false));
        }
        if (null != this.peerRater && peerRater.isOpen()) {
            ratersNodes.add(new RaterNode("peer", peerRater.buildRaterNode(), false));
        }
        if (null != this.subRater && subRater.isOpen()) {
            ratersNodes.add(new RaterNode("peer", subRater.buildRaterNode(), false));
        }
        return ratersNodes;
    }

    public void clearRaterConf() {
        this.selfRater = null;
        this.superRater = null;
        this.peerRater = null;
        this.subRater = null;
    }

    //按指标设定评分流程:导入okr或者空类别时,预留的评分流程
    @Getter
    @Setter
    @NoArgsConstructor
    public static class ItemScoreRuleAtType {
        public String isModified;
        public String kpiTypeId;

        @JSONField(name = "selfScoreFlag")
        public String selfOpen;
        @JSONField(name = "selfScoreWeight")
        public BigDecimal selfWeight;

        @JSONField(name = "peerScoreFlag")
        public String peerOpen;
        @JSONField(name = "peerScoreWeight")
        public BigDecimal peerWeight;
        @JSONField(name = "peerScoreList")
        public List<BaseAuditNode> peerNodes;

        @JSONField(name = "subScoreFlag")
        public String subOpen;
        @JSONField(name = "subScoreWeight")
        public BigDecimal subWeight;
        @JSONField(name = "subScoreList")
        public List<BaseAuditNode> subNodes;

        @JSONField(name = "superiorScoreFlag")
        public String superOpen;
        @JSONField(name = "superiorScoreWeight")
        public BigDecimal superWeight;
        @JSONField(name = "superiorScoreList")
        public List<BaseAuditNode> superNodes;

        @JSONField(name = "appointScoreFlag")
        public String appointOpen;
        @JSONField(name = "appointScoreWeight")
        public BigDecimal appointWeight;
        @JSONField(name = "appointScoreList")
        public List<BaseAuditNode> appointNodes;

        public EvalItemScoreRule asRule() {

            EvalItemScoreRule typeRule = new EvalItemScoreRule();
            //typeRule.setCreatedUser(evalRule.getCreatedUser());
            //typeRule.setUpdatedUser(evalRule.getCreatedUser());
            //typeRule.setId(evalCommonGen.get());
            //typeRule.setCompanyId(compay);
            //typeRule.setTaskId(taskId);
            //typeRule.setTaskUserId(evalRule.getEmpEvalId());

            typeRule.setKpiTypeId(kpiTypeId);
            typeRule.setKpiItemId("0");

            RaterNodeConf selfRater = this.getSelfRater();
            typeRule.setSelfRater(selfRater);
            //TODO @张旺, 同级邀请设定
            //typeRuleDo.setMutualUserType(typeRule.mu);
            MutualNodeConf peerRater = this.getPeerRater();
            typeRule.setPeerRater(peerRater);

            MutualNodeConf subRater = this.getSubRater();
            typeRule.setSubRater(subRater);

            S3RaterBaseConf appointRater = this.getAppointRater();
            typeRule.setAppointRater(appointRater);

            S3SuperRaterConf superRater = this.getSuperRater();
            typeRule.setSuperRater(superRater);
            return typeRule;
        }

        @JSONField(serialize = false)
        public RaterNodeConf getSelfRater() {
            RaterNodeConf s3Self = new RaterNodeConf(selfOpen,
                    "item", selfWeight, false);
            return s3Self;
        }

        public MutualNodeConf getSubRater() {
            return buildMutuRater(subOpen, subWeight, subNodes, false);
        }

        public MutualNodeConf getPeerRater() {
            return buildMutuRater(peerOpen, peerWeight, peerNodes, false);
        }

        private MutualNodeConf buildMutuRater(String peerOpen, BigDecimal peerWeight, List<BaseAuditNode> peerNodes, Boolean anonymous) {
            MutualNodeConf conf = new MutualNodeConf(peerOpen, "item", anonymous, peerNodes);
            if (CollUtil.isNotEmpty(peerNodes)) {
                conf.setRaters(peerNodes.get(0).getRaters());
            }
            conf.setNodeWeight(peerWeight);
            return conf;
        }

        public S3SuperRaterConf getSuperRater() {
            S3SuperRaterConf conf = new S3SuperRaterConf(superOpen, "item", superWeight, false, S3SuperRaterConf.SAME_TIME);
            conf.setAuditNodes(superNodes);
            return conf;
        }


        public S3RaterBaseConf getAppointRater() {
            S3SuperRaterConf conf = new S3SuperRaterConf(appointOpen, "item", appointWeight, false, S3SuperRaterConf.SAME_TIME);
            conf.setAuditNodes(appointNodes);
            return conf;
        }

    }

    public List<EvalScoreResult> computePlusSubMaxScore() {
        List<EvalScoreResult> scoreResults = new ArrayList<>();
        Map<String, ScorerRateNode> map = new HashMap<>();
        for (EvalKpi item : this.items) {
            ListWrap<EvalScoreResult> scoreResultListWrap = new ListWrap<>(item.getWaitScoresOld());
            scoreResultListWrap.groupBy(EvalScoreResult::getScorerType);
            scoreResultListWrap.getGroups().forEach((scorerType, values) -> {
                if (!map.containsKey(getKey(scorerType, item.getKpiItemId()))) {
                    map.put(getKey(scorerType, item.getKpiItemId()), new ScorerRateNode(scorerType));
                }
                ScorerRateNode node = map.get(getKey(scorerType, item.getKpiItemId()));
                for (EvalScoreResult value : values) {
                    BigDecimal score = null == value.getScore() ? BigDecimal.ZERO : value.getScore();
                    BigDecimal plusSubScore = score.compareTo(BigDecimal.ZERO) >= 0 ? value.getPlusScore() : value.getSubtractScore();
                    if (Objects.nonNull(item.itemScoreRule())) {
                        BigDecimal nodeWeight = item.getItemScoreRule().getNodeWeight(scorerType);
                        node.addScore(plusSubScore, nodeWeight);
                    } else {
                        //定向评分的指标
                        node.addScore(plusSubScore, Pecent.ONE_HUNDRED);
                    }
                }
            });
        }
        for (Map.Entry<String, ScorerRateNode> map1 : map.entrySet()) {
            ScorerRateNode node = map1.getValue();
            if (node.getSumScorer().compareTo(BigDecimal.ZERO) >= 0) {
                maxExtraScore = plusSubInterval.maxPlusScore();
                if (node.getSumScorer().compareTo(maxExtraScore) <= 0) {
                    continue;
                }
            } else {
                maxExtraScore = plusSubInterval.maxSubtractScore();
                if (node.getSumScorer().compareTo(maxExtraScore) >= 0) {
                    continue;
                }
            }
            node.compareRate(maxExtraScore);
        }
        for (EvalKpi item : items) {
            ListWrap<EvalScoreResult> evalScoreResultListWrap = new ListWrap<>(item.getWaitScoresOld());
            evalScoreResultListWrap.groupBy(EvalScoreResult::getScorerType);
            evalScoreResultListWrap.getGroups().forEach((scorerType, values) -> {
                for (EvalScoreResult result : values) {
                    if (null != result.getScore() && result.getScore().compareTo(BigDecimal.ZERO) >= 0) {
                        result.reComputeFinalPlusScore(map.get(getKey(result.getScorerType(), result.getKpiItemId())));
                        scoreResults.add(result);
                    } else {
                        result.reComputeFinalSubScore(map.get(getKey(result.getScorerType(), result.getKpiItemId())));
                        scoreResults.add(result);
                    }

                }
            });
        }
        return scoreResults;
    }

    @NotNull
    private String getKey(String scorerType, String kpiItemId) {
        return scorerType + "@" + kpiItemId;
    }

    public List<EvalScoreResult> computePlusMaxScore() {
        List<EvalScoreResult> scoreResults = new ArrayList<>();
        Map<String, ScorerRateNode> map = new HashMap<>();
        for (EvalKpi item : this.items) {
            ListWrap<EvalScoreResult> scoreResultListWrap = new ListWrap<>(item.getWaitScoresOld());
            scoreResultListWrap.groupBy(EvalScoreResult::getScorerType);
            scoreResultListWrap.getGroups().forEach((scorerType, values) -> {
                if (!map.containsKey(scorerType)) {
                    map.put(scorerType, new ScorerRateNode(scorerType));
                }
                ScorerRateNode node = map.get(scorerType);
                for (EvalScoreResult value : values) {
                    if (Objects.nonNull(item.itemScoreRule())) {
                        BigDecimal nodeWeight = item.getItemScoreRule().getNodeWeight(scorerType);
                        node.addScore(value.getPlusScore(), nodeWeight);
                    } else {
                        //定向评分的指标
                        node.addScore(value.getPlusScore(), Pecent.ONE_HUNDRED);
                    }
                }
            });
        }
        for (Map.Entry<String, ScorerRateNode> map1 : map.entrySet()) {
            ScorerRateNode node = map1.getValue();
//            if (isPlusSubType()) {
//                maxExtraScore = plusSubInterval.maxPlusScore();
//            }
            if (node.getSumScorer().compareTo(maxExtraScore) <= 0) {
                continue;
            }
            node.compareRate(maxExtraScore);
        }
        for (EvalKpi item : items) {
            ListWrap<EvalScoreResult> evalScoreResultListWrap = new ListWrap<>(item.getWaitScoresOld());
            evalScoreResultListWrap.groupBy(EvalScoreResult::getScorerType);
            evalScoreResultListWrap.getGroups().forEach((scorerType, values) -> {
                for (EvalScoreResult result : values) {
                    result.reComputeFinalPlusScore(map.get(result.getScorerType()));
                    scoreResults.add(result);
                }
            });
        }
        return scoreResults;
    }

    public List<EvalScoreResult> computeSubMaxScore() {
        List<EvalScoreResult> scoreResults = new ArrayList<>();
        Map<String, ScorerRateNode> map = new HashMap<>();
        for (EvalKpi item : this.items) {
            ListWrap<EvalScoreResult> scoreResultListWrap = new ListWrap<>(item.getWaitScoresOld());
            scoreResultListWrap.groupBy(EvalScoreResult::getScorerType);
            scoreResultListWrap.getGroups().forEach((scorerType, values) -> {
                if (!map.containsKey(scorerType)) {
                    map.put(scorerType, new ScorerRateNode(scorerType));
                }
                ScorerRateNode node = map.get(scorerType);
                for (EvalScoreResult value : values) {
                    if (Objects.nonNull(item.getItemScoreRule())) {
                        BigDecimal nodeWeight = item.getItemScoreRule().getNodeWeight(scorerType);
                        node.addScore(value.getSubtractScore(), nodeWeight);
                    } else {
                        //定向评分的指标
                        node.addScore(value.getSubtractScore(), Pecent.ONE_HUNDRED);
                    }
                }
            });
        }
        for (Map.Entry<String, ScorerRateNode> map1 : map.entrySet()) {
            ScorerRateNode node = map1.getValue();
//            if (isPlusSubType()) {
//                maxExtraScore = plusSubInterval.maxSubtractScore();
//            }
            if (node.getSumScorer().compareTo(maxExtraScore) <= 0) {
                continue;
            }
            map1.getValue().compareRate(maxExtraScore);
        }
        for (EvalKpi item : items) {
            ListWrap<EvalScoreResult> evalScoreResultListWrap = new ListWrap<>(item.getWaitScoresOld());
            evalScoreResultListWrap.groupBy(EvalScoreResult::getScorerType);
            evalScoreResultListWrap.getGroups().forEach((scorerType, values) -> {
                for (EvalScoreResult result : values) {
                    result.reComputeFinalSubScore(map.get(result.getScorerType()));
                    scoreResults.add(result);
                }
            });
        }
        return scoreResults;
    }

    public boolean hasMaxExtraScore() {
        return null != this.maxExtraScore;
    }

    public List<EvalScoreResult> resetScoreEmp(List<ScoreEmp> scoreEmps) {
        List<EvalScoreResult> scoreResults = new ArrayList<>();
        for (EvalKpi item : this.items) {
            ListWrap<EvalScoreResult> scoreResultListWrap = new ListWrap<>(item.getWaitScoresOld());
            scoreResultListWrap.getDatas().stream().filter(result -> "pass".equals(result.getAuditStatus())).forEach(r -> {
                for (ScoreEmp scoreEmp : scoreEmps) {
                    if (r.needResetResult(scoreEmp)) {
                        r.setAuditStatus(null);
                        scoreResults.add(r);
                    }
                }
            });
        }
        return scoreResults;
    }

    public List<EvalScoreResult> toWaitScoreEmp(String empId, String scoreType) {
        List<EvalScoreResult> scoreResults = new ArrayList<>();
        for (EvalKpi item : this.items) {
            ListWrap<EvalScoreResult> scoreResultListWrap = new ListWrap<>(item.getWaitScoresOld());
            scoreResultListWrap.getDatas().stream().filter(result -> result.getAuditStatus() == null).forEach(r -> {
                if (r.needToWaitResult(empId, scoreType)) {
                    r.setAuditStatus("wait");
                    scoreResults.add(r);
                }
            });
        }
        return scoreResults;
    }

    public Collection<? extends PerfEvalTypeResult> resetTypeScoreEmp(List<ScoreEmp> scoreEmps) {
        List<PerfEvalTypeResult> collect = waitScoresOld.stream().filter(result -> result.isPassed())
                .filter(result -> result.needResetResult(scoreEmps))
                .collect(Collectors.toList());
        return collect;
    }

    public Collection<? extends PerfEvalTypeResult> toWaitTypeScoreEmp(String empId, String scoreType) {
        List<PerfEvalTypeResult> collect = waitScoresOld.stream().filter(result -> result.isPassed())
                .filter(result -> result.needToWaitResult(empId, scoreType))
                .collect(Collectors.toList());
        return collect;
    }

    public boolean allScorePassed() {
        if (CollUtil.isNotEmpty(waitScoresOld)) {
            List<PerfEvalTypeResult> notPassed = waitScoresOld.stream().filter(typeRs -> !typeRs.isPassed()).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(notPassed)) {
                return false;
            }
        }
        for (EvalKpi item : this.items) {
            if (item.isAutoItem() || item.allScoreRsPassed()) {
                continue;
            } else {
                return false;
            }
        }
        return true;
    }

    public List<PerfEvalTypeResult> dispatch(SubScoreNodeEnum node, int order, String selfEmpId, boolean isOpenAvgWeightCompute) {
        List<PerfEvalTypeResult> raterResults = new ArrayList<>();
        if (!isEvalLevel()) {//维度不评
            return raterResults;
        }
        INodeConf iNodeConf = super.getNodeConf(node, order, selfEmpId);
        if (iNodeConf == null) {//有些标多级,有的一级. 有些指标是无f规则
            return raterResults;
        }
        List<PerfEvalTypeResult> rrs = dispacthNodeConf(iNodeConf, isOpenAvgWeightCompute);
        return rrs;
    }

    @Nullable
    private List<PerfEvalTypeResult> dispacthNodeConf(INodeConf iNodeConf, boolean isOpenAvgWeightCompute) {
        List<PerfEvalTypeResult> raterResults = new ArrayList<>();
        //重置后再次执行进入,使用已存在的scoreResult,解决已转交的scorer.
        List<PerfEvalTypeResult> alreadys = alreadyDispatch(iNodeConf);
        if (!alreadys.isEmpty()) {
            return alreadys;
        }
        if (iNodeConf.openedRaters().isEmpty()) {
            return raterResults;
        }

        // 开启了平均权重 或者不在不是同级|下级互评场景
        if (isOpenAvgWeightCompute || !SubScoreNodeEnum.mutualScoreScene().contains(iNodeConf.node().getScene())) {
            iNodeConf.avgWeight();
        }
        for (Rater scorer : iNodeConf.openedRaters()) {
            if (null == scorer.getWeight()) {
                scorer.setWeight(new BigDecimal("100.00"));
            }
            if (!isOpenAvgWeightCompute && SubScoreNodeEnum.mutualScoreScene().contains(iNodeConf.node().getScene())) {
                scorer.setWeight(new BigDecimal("100.00"));//开启平均分，且是互评场景，评分人权重置为100%
            }
            PerfEvalTypeResult result = createResult(iNodeConf.node(), iNodeConf.order(), iNodeConf.multiType(), scorer);
            raterResults.add(result);
        }
        return raterResults;
    }

    //重置后再次执行进入,使用已存在的scoreResult,解决已转交的scorer.
    public List<PerfEvalTypeResult> alreadyDispatch(INodeConf iNodeConf) {
        List<PerfEvalTypeResult> rs = new ArrayList<>();
        if (waitScoresOld == null || waitScoresOld.isEmpty()) {
            return Collections.emptyList();
        }
        List<PerfEvalTypeResult> nodeScores = new ListWrap<>(waitScoresOld).groupBy(r -> r.getScorerType()).groupGet(iNodeConf.node().getScene());
        for (PerfEvalTypeResult nodeScore : nodeScores) {
            //指定评分重置result的状态全部重置回去，不去分order
            if (nodeScore.isAppointNode() && nodeScore.getAuditStatus().equals("wait")) {
                nodeScore.doDispatched();
                rs.add(nodeScore);
            }
            //已分发再分发一次
            if (iNodeConf.order() == nodeScore.getApprovalOrder()) {
                nodeScore.doDispatched();
                rs.add(nodeScore);
            }
        }
        return rs;
    }


    private PerfEvalTypeResult createResult(SubScoreNodeEnum node, int order, String multiOrAndMode, Rater scorer) {

        PerfEvalTypeResult result = new PerfEvalTypeResult(companyId, taskUserId, kpiTypeId, node.getScene(), scorer.getEmpId(), scorer.getWeight());
        order = scorer.getApprovalOrder() != null ? scorer.getApprovalOrder() : order;
        result.setApprovalOrder(order);
        result.setReviewersType(multiOrAndMode);
        //result.appendAuditor(node.getScene(), scorer.getEmpId(), multiOrAndMode, order, null, null);
        //result.appendItemId(kpiItemId, kpiTypeId);
        //result.setScoreWeight(scorer.getWeight());
        //result.setTaskUserId(taskUserId);
        return result;
    }

    //评分
    @JSONField(serialize = false)
    public boolean isEvalScore() {
        return scoreOptType == 2 || scoreOptType == 4;
    }

    //评等级
    @JSONField(serialize = false)
    public boolean isEvalLevel() {
        return scoreOptType == 1 || scoreOptType == 8;//增加了8-仅需维度评价【指标无需评价】
    }


    //计算维度上的最终分
    public void computeTypeFinalScore(FinalWeightSumScore totalOfSum, boolean isOpenAvgWeightCompute) {
        List<? extends EvalKpi> needCompute = items;
        if (isPlusSubType()) {
            this.computePlusSubtract(totalOfSum, needCompute, isOpenAvgWeightCompute);
        } else if (isPlusType()) {
            this.computePlus(totalOfSum, needCompute, isOpenAvgWeightCompute);
        } else if (isSubtractType()) {
            this.computeSubtract(totalOfSum, needCompute, isOpenAvgWeightCompute);
        } else if (isOkr() && CollUtil.isNotEmpty(okrGoals)) {
            this.computeOkrV2(totalOfSum, needCompute, isOpenAvgWeightCompute);
        } else if (isOkr() && CollUtil.isEmpty(okrGoals)) {
            this.computeOkr(totalOfSum, needCompute, isOpenAvgWeightCompute);
        } else {
            this.computeNormal(totalOfSum, needCompute, isOpenAvgWeightCompute);
        }
    }

    private void computeTypeNodeScore(TypeFinalNodeScoreV3 typeScore) {
        typeScore.sum();//汇总
        this.setTypeNodeScore(typeScore);
    }

    public void setTypeNodeScore(TypeFinalNodeScoreV3 typeScore) {
        log.info("==setTypeNodeScore 计算维度最终得分：{}", typeScore.getTypeSum());
        this.typeFinalSelfScore = typeScore.getFinalSelfScore();
        this.typeFinalPeerScore = typeScore.getFinalPeerScore();
        this.typeFinalSubScore = typeScore.getFinalSubScore();
        this.typeFinalSuperiorScore = typeScore.getFinalSuperiorScore();
        this.typeFinalAppointScore = typeScore.getFinalAppointScore();

        this.typeSelfScore = typeScore.getSelfScore();
        this.typePeerScore = typeScore.getPeerScore();
        this.typeSubScore = typeScore.getSubScore();
        this.typeSuperiorScore = typeScore.getSuperiorScore();
        //计算指标指定评分人 与指定评分共用一个字段，不会同时存在
        this.typeFinalItemScore = typeScore.getFinalItemScore();
        this.typeItemScore = typeScore.getItemItemScore();
        this.typeAppointScore = typeScore.getAppointScore();
        typeScore.sum();
        this.typeFinalOriginalScore = typeScore.getTypeSum();
        this.typeFinalScore = typeScore.getTypeSum();
        this.typeScore = typeScore.getTypeScore();
        this.typeOriginalScore = typeScore.getTypeScore();
        if (Objects.nonNull(typeFinalScore)) {
            typeFinalScore = typeFinalScore.setScale(2, RoundingMode.HALF_UP);
        }
        log.info("==setTypeNodeScore计算维度得分：{}，typeScore：{}", this.typeFinalScore,typeScore);
        this.typeFinalScore = null == this.typeFinalScore ? BigDecimal.ZERO : this.typeFinalScore;
        log.info("==setTypeNodeScore计算维度得分如果为null，设置默认值");
    }

    public List<EvalKpi> autoItems() {
        return items.stream().filter(EvalKpi::isAutoItem).collect(Collectors.toList());
    }
    private void computeNormal(FinalWeightSumScore totalOfSum, List<? extends EvalKpi> needComputed, boolean isOpenAvgWeightCompute) {
        for (EvalKpi item : needComputed) {
            item.computeNormal(totalOfSum, isOpenAvgWeightCompute);
        }
    }

    //加减分项合并的分类
    public void computePlusSubtract(FinalWeightSumScore totalOfSum, List<? extends EvalKpi> items, boolean isOpenAvgWeightCompute) {
        BigDecimal typePlusSum = null;
        for (EvalKpi item : items) {
            //合并计算同一维度的
            typePlusSum = FinalWeightSumScore.addNullZero(typePlusSum, item.computePlusS1(isOpenAvgWeightCompute));
            typePlusSum = FinalWeightSumScore.addNullZero(typePlusSum, item.computeSubtract2(isOpenAvgWeightCompute));
        }
        if (typePlusSum == null) {
            return;
        }
        if (typePlusSum.signum() > 0) {
            typePlusSum = BaseLevelGroupIdConf.limitMax(plusSubInterval.maxPlusScore(), typePlusSum, StrUtil.isEmpty(plusSubInterval.getMax()));
        } else {
            typePlusSum = BaseLevelGroupIdConf.minSum(plusSubInterval.maxSubtractScore(), typePlusSum, StrUtil.isEmpty(plusSubInterval.getMin()));
        }
        totalOfSum.addToPlusSum(typePlusSum);
        log.info("===computePlusSubtract params:totalOfSum:{}", JSONUtil.toJsonStr(totalOfSum));
    }

    //计算加分项
    public void computePlus(FinalWeightSumScore totalOfSum, List<? extends EvalKpi> items, boolean isOpenAvgWeightCompute) {
        BigDecimal typeSum = null;
        for (EvalKpi item : items) {
            typeSum = BaseLevelGroupIdConf.limitMax(maxExtraScore, FinalWeightSumScore.addNullZero(typeSum, item.computePlus(isOpenAvgWeightCompute)), false);
        }
        totalOfSum.addToPlusSum(typeSum);
        log.info("===computePlus params:totalOfSum:{}", JSONUtil.toJsonStr(totalOfSum));
    }
    //计算减分分项
    public void computeSubtract(FinalWeightSumScore totalOfSum, List<? extends EvalKpi> items, boolean isOpenAvgWeightCompute) {
        BigDecimal typeSum = null;
        for (EvalKpi item : items) {
            typeSum = BaseLevelGroupIdConf.limitMax(maxExtraScore, FinalWeightSumScore.addNullZero(typeSum, item.computeSubtract(isOpenAvgWeightCompute)), false);
        }
        totalOfSum.addToSubtractSum(typeSum);
        log.info("===computeSubtract params:totalOfSum:{}", JSONUtil.toJsonStr(totalOfSum));
    }

    //支持带okr的o形式计算,o的分值结果存入o中
    public void computeOkrV2(FinalWeightSumScore totalOfSum, List<? extends EvalKpi> items, boolean isOpenAvgWeightCompute) {
        Map<String, OkrGoal> okrs = new HashMap<>();
        for (OkrGoal okrGoal : okrGoals) {
            for (OkrKeyResult keyResult : okrGoal.getKeyResults()) {
                okrs.put(keyResult.getKeyResultId(), okrGoal);
            }
        }
        for (EvalKpi item : items) {
            BigDecimal okrSum = item.computeOkr(totalOfSum, isOpenAvgWeightCompute);
            OkrGoal goal = okrs.get(item.getActionId());
            goal.sumScore(okrSum);//统计o的得分
        }
    }

    //计算减分分项
    public void computeOkr(FinalWeightSumScore totalOfSum, List<? extends EvalKpi> items, boolean isOpenAvgWeightCompute) {
        for (EvalKpi item : items) {
            item.computeOkr(totalOfSum, isOpenAvgWeightCompute);
        }
    }

    //是否开启录入完成值
    public boolean isOpenFinishValue() {
        if (CollUtil.isEmpty(this.kpiTypeUsedFields)) {
            return true;
        }
        Map<String, KpiTypeUsedField> map = this.kpiTypeUsedFields.stream()
                .collect(Collectors.toMap(KpiTypeUsedField::getFieldId, Function.identity()));
        if (map.get("finishValue") != null) {
            KpiTypeUsedField field = map.get("finishValue");
            if (field.isOpenShow()) {
                return true;
            }
        }
        return false;
    }

    //是否开启目标值
    public boolean isOpenTargetValue() {
        if (CollUtil.isEmpty(this.kpiTypeUsedFields)) {
            return true;
        }
        Map<String, KpiTypeUsedField> map = this.kpiTypeUsedFields.stream()
                .collect(Collectors.toMap(KpiTypeUsedField::getFieldId, Function.identity()));
        if (map.get("targetValue") != null) {
            KpiTypeUsedField field = map.get("targetValue");
            return field.isOpenShow();
        }
        return false;
    }

    public void buildItemNodes(ScoreSceneWrap nodes) {
        if (CollUtil.isEmpty(items)) {
            return;
        }
        for (EvalKpi item : items) {
            if (item.isDirectional()) {
                nodes.addNodeOpt(SubScoreNodeEnum.ITEM_SCORE, 1);
                continue;
            }
            if (item.getItemScoreRule() == null) {
                log.warn("指标没有流程 taskUserId:{} kpiItemId:{},id:{}", taskUserId, item.getKpiItemId(), item.getId());
                continue;
            }
            item.getItemScoreRule().buildNodes(nodes);
        }
    }

    public void asDisplayFlow(AsDisplayFlowRs nodes, String empId) {
        if (isEvalLevel()) {
            super.asDisplayFlow(nodes, empId, waitScoresOld);
        }
    }

    public void itemAsDisplayFlow(AsDisplayFlowRs disFlows) {
        if (CollUtil.isEmpty(items)) {
            return;
        }
        for (EvalKpi item : items) {
            if (item.openOkrScore() || item.isAutoItem()) {
                continue;
            }
            if (item.noNeedScore()) {
                continue;
            }
            item.asDisplayFlow(disFlows);
        }
    }

    public void initScoreOptType() {
        if (Objects.isNull(this.scoreOptType)) {
            this.scoreOptType = 2;
        }
    }

    public void buildItemCompareInputEmp(Map<String, EvalKpi> itemMap, ResultInputEmpPackage resultInputEmpPackage, String empId) {
        for (EvalKpi item : this.items) {
            EvalKpi evalKpi = itemMap.get(item.getKpiItemId());
            if (Objects.nonNull(evalKpi)) {
                item.compareInputEmp(resultInputEmpPackage, evalKpi);
            } else {
                item.compareInputEmp(resultInputEmpPackage, empId);
            }
        }
    }

    public void builderItemWaitScore(String dingCorpId, List<String> defaultCustomScoreConfig,
                                     List<String> defaultPlusSubScoreConfig, ScoreValueConf scoreValueConf) {
        if (CollUtil.isEmpty(this.items)) {
            return;
        }
        for (EvalKpi item : this.items) {
            item.builderItemWaitScore(dingCorpId, defaultCustomScoreConfig,
                    defaultPlusSubScoreConfig, this.indLevelGroup, scoreValueConf);
        }
    }


    public void builderItemWaitScoreV3(String dingCorpId,List<String> defaultCustomScoreConfig,
                                     List<String> defaultPlusSubScoreConfig, ScoreValueConf scoreValueConf) {
        if (CollUtil.isEmpty(this.items)) {
            return;
        }
        for (EvalKpi item : this.items) {
            item.builderItemWaitScoreV3(dingCorpId,defaultCustomScoreConfig,
                    defaultPlusSubScoreConfig, this.indLevelGroup, scoreValueConf);
        }
    }


    public void builderTypeWaitScore(List<String> defaultCustomScoreConfig,
                                     List<String> defaultPlusSubScoreConfig) {
        boolean isEvalLevel = Objects.nonNull(this.indLevelGroup);
        boolean isPlusSubScoreType = isPlusSubType() || isPlusType() || isSubtractType();
        for (PerfEvalTypeResult typeResult : this.waitScoresOld) {
            typeResult.markSkip(defaultCustomScoreConfig, defaultPlusSubScoreConfig, isPlusSubScoreType);
            if (isEvalLevel) {
                if (Objects.equals(this.indLevelGroup.getType(), 0)) {
                    typeResult.setScoreLevel(this.indLevelGroup.getLevels().get(0).getName());
                    continue;
                }
            }
        }
    }

    public void builderTypeWaitScoreV3(List<String> defaultCustomScoreConfig,
                                       List<String> defaultPlusSubScoreConfig) {
        boolean isEvalLevel = Objects.nonNull(this.indLevelGroup);
        boolean isPlusSubScoreType = isPlusSubType() || isPlusType() || isSubtractType();
        for (EvalScorerNodeKpiType typeResult : this.waitScores) {
            typeResult.markSkip(defaultCustomScoreConfig, defaultPlusSubScoreConfig, isPlusSubScoreType);
            if (isEvalLevel && Objects.equals(this.indLevelGroup.getType(), 0)) {
                typeResult.setScoreLevel(this.indLevelGroup.getLevels().get(0).getName());
            }
        }
    }

    public List<String> getPlanItemIds() {
        if (CollUtil.isNotEmpty(this.items)) {
            return CollUtil.filterNew(this.items, item -> item.isBusinessItem()).stream().map(EvalKpi::getKpiItemId).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    public void initOkrGoalId(Map<String, BusinessPlanItem> planItemMap) {
        for (EvalKpi item : this.items) {
            item.initOkrGoalId(planItemMap);
        }
    }

    public void refreshOkrGoalId(Map<String, BusinessPlanItem> planItemMap) {
        for (EvalKpi item : this.items) {
            if (!item.isBusinessItem()) {
                continue;
            }
            item.refreshOkrGoalId(planItemMap);
        }
    }

    public String getSuperiorScoreWay() {
        if (Objects.isNull(this.items)) {
            return null;
        }
        for (EvalKpi item : this.items) {
            if (StrUtil.isNotBlank(item.getSuperiorScoreWay())) {
                return item.getSuperiorScoreWay();
            }
        }
        return null;
    }

    @JSONField(serialize = false)
    public boolean isAskType() {
        return Objects.equals(this.kpiTypeClassify, "ask360");
    }


    public Set<String> appointEmpIds(String evalEmpId) {
        HashSet<String> appointEmpIds = new HashSet<>();
        Set<String> appointPeerRaterEmpIds = this.appointEmpIds(this.peerRater, evalEmpId);
        Set<String> appointSubRaterEmpIds = this.appointEmpIds(this.subRater, evalEmpId);
        appointEmpIds.addAll(appointPeerRaterEmpIds);
        appointEmpIds.addAll(appointSubRaterEmpIds);
        return appointEmpIds;
    }

    //是否需要指定互评人
    private Set<String> appointEmpIds(MutualNodeConf mutualNodeConf, String evalEmpId) {
        HashSet<String> appointEmpIds = new HashSet<>();
        if (mutualNodeConf != null && mutualNodeConf.isOpen() && mutualNodeConf.getAppointer() != null) {//新的配置
            return mutualNodeConf.getAppointer().appointEmpIds(evalEmpId);
        }
        return appointEmpIds;
    }

    /**
     * @param evalEmpId
     * @param opEmpId
     * @return
     */
//是否需要指定互评人
    public boolean needSetPeerRater(String evalEmpId, String opEmpId, String queryType) {
        return this.needSetRater(peerRater, evalEmpId, opEmpId, queryType);
    }

    //是否需要指定互评人
    public boolean needSetSubRater(String evalEmpId, String opEmpId, String queryType) {
        return this.needSetRater(subRater, evalEmpId, opEmpId, queryType);
    }

    //是否需要指定互评人
    private boolean needSetRater(MutualNodeConf mutualNodeConf, String evalEmpId, String opEmpId, String queryType) {
        if (Objects.isNull(mutualNodeConf) || !mutualNodeConf.isOpen()) {
            return false;
        }
        WaitAppoint appointer = mutualNodeConf.getAppointer();
        if (Objects.isNull(appointer)) {
            return false;
        }
        if (appointer.isOpenInviteMutualAudit() && StrUtil.isBlank(queryType)) {
            if (appointer != null && appointer.isAppointer(opEmpId, evalEmpId, queryType) && CollUtil.isEmpty(mutualNodeConf.getRaters())) {//新的配置
                return true;
            }
            return false;
        }
        if (appointer != null && appointer.isAppointer(opEmpId, evalEmpId, queryType)) {//新的配置
            return true;
        }
        return false;
    }

    public boolean notSetMutualScorer() {
        if (Objects.isNull(peerRater) || Objects.isNull(subRater)) {
            return false;
        }
        if (this.peerRater.isOpen() && this.subRater.isOpen()) {
            return CollUtil.isEmpty(this.peerRater.getRaters()) || CollUtil.isEmpty(this.subRater.getRaters());
        }
        if (this.peerRater.isOpen()) {
            return CollUtil.isEmpty(this.peerRater.getRaters());
        }
        if (this.subRater.isOpen()) {
            return CollUtil.isEmpty(this.subRater.getRaters());
        }
        return false;
    }

    //是否默认流程
    @JSONField(serialize = false)
    public boolean defaultFlow(boolean isCustom) {
        if (isCustom && (Objects.isNull(this.selfRater) || !this.selfRater.isOpen())
                && (Objects.isNull(this.peerRater) || !this.peerRater.isOpen())
                && (Objects.isNull(this.subRater) || !this.subRater.isOpen())
                && (Objects.isNull(this.superRater) || !this.superRater.isOpen())
                && (Objects.isNull(this.appointRater) || !this.appointRater.isOpen())) {
            return true;
        }
        return false;
    }

    public void accup(String companyId, String opEmpId) {
        this.companyId = new TenantId(companyId);
        this.updatedTime = new Date();
        this.updatedUser = opEmpId;
    }

    @JSONField(serialize = false)
    public boolean askAvgScore() {
        return Objects.equals(this.scoringType, 1);
    }

    public void setAskEvalScore(String ask360EvalId, BigDecimal ask360EvalScore, BigDecimal ask360EvalTotalScore) {
        if (Objects.equals(this.ask360EvalId, ask360EvalId)) {
            BigDecimal askScore = askAvgScore() ? ask360EvalScore : ask360EvalTotalScore;
            this.ask360EvalScore = askScore;
        }
    }

    @JSONField(serialize = false)
    public EvalKpi getAskItems() {
        EvalKpi kpi = new EvalKpi();
        kpi.setAskItem(this.taskUserId, this.kpiTypeId, this.ask360TempName, this.ask360TempDesc, this.ask360EvalScore);
        return kpi;
    }

    @JSONField(serialize = false)
    public boolean ask360NotSetMutualScorer() {
        if (!this.isAskType()) {
            return false;
        }
        if (Objects.isNull(peerRater) || Objects.isNull(subRater)) {
            return false;
        }
        if (this.peerRater.isOpen() && this.subRater.isOpen()) {
            return CollUtil.isEmpty(this.peerRater.getRaters()) || CollUtil.isEmpty(this.subRater.getRaters());
        }
        if (this.peerRater.isOpen()) {
            return CollUtil.isEmpty(this.peerRater.getRaters());
        }
        if (this.subRater.isOpen()) {
            return CollUtil.isEmpty(this.subRater.getRaters());
        }
        return false;
    }


    @JSONField(serialize = false)
    public boolean openPeerMutual() {
        if (Objects.nonNull(this.peerRater) && this.peerRater.isOpen()) {
            if (Objects.nonNull(this.peerRater.getAppointer())) {
                return true;
            }
        }
        return false;
    }

    @JSONField(serialize = false)
    public boolean openSubMutual() {
        if (Objects.nonNull(this.subRater) && this.subRater.isOpen()) {
            if (Objects.nonNull(this.subRater.getAppointer())) {
                return true;
            }
        }
        return false;
    }

    public void clearAppointRater() {
        if (Objects.nonNull(this.peerRater) && this.peerRater.isOpen() && Objects.nonNull(this.peerRater.getAppointer())) {
            this.peerRater.setRaters(new ArrayList<>());
        }
        if (Objects.nonNull(this.subRater) && this.subRater.isOpen() && Objects.nonNull(this.subRater.getAppointer())) {
            this.subRater.setRaters(new ArrayList<>());
        }
    }

    public void closeRater() {
        if (Objects.nonNull(this.selfRater)) {
            this.selfRater.setOpen(0);
        }
        if (Objects.nonNull(this.peerRater)) {
            this.peerRater.setOpen(0);
        }
        if (Objects.nonNull(this.subRater)) {
            this.subRater.setOpen(0);
        }
        if (Objects.nonNull(this.superRater)) {
            this.superRater.setOpen(0);
        }
        if (Objects.nonNull(this.appointRater)) {
            this.appointRater.setOpen(0);
        }
        if (CollUtil.isEmpty(this.items)) {
            return;
        }
        this.items.forEach(item -> {
            item.setItemScoreRule(null);
        });
    }

    public EmpEvalKpiType updateMutualRater(String scene, List<Rater> raters) {
        boolean isPeerScore = AuditEnum.PEER_SCORE.getScene().equals(scene);
        EmpEvalKpiType kpiType = this.clone();
        if (isPeerScore) {
            kpiType.getPeerRater().setRaters(raters);
        } else {
            kpiType.getSubRater().setRaters(raters);
        }
        return kpiType;
    }

    public List<KpiTypeUsedField> getKpiTypeUsedFields() {
        if (CollUtil.isEmpty(this.kpiTypeUsedFields)) {
            this.kpiTypeUsedFields = initSystemField();
            return this.kpiTypeUsedFields;
        }
        initSystemSort();
        return kpiTypeUsedFields;
    }

    private List<KpiTypeUsedField> initSystemField() {
        List<KpiTypeUsedField> fieldList = new ArrayList<>();
        String companyId = Objects.isNull(this.companyId) ? null : this.companyId.getId();
        if (this.isWorkItemType()) {
            fieldList.add(new KpiTypeUsedField("name", companyId, this.taskUserId, this.kpiTypeId, "事项名称", 1, 1, 1, 1));
            fieldList.add(new KpiTypeUsedField("unit", companyId, this.taskUserId, this.kpiTypeId, "单位", 1, 0, 2, 1));
            fieldList.add(new KpiTypeUsedField("entryTips", companyId, this.taskUserId, this.kpiTypeId, "录入提示", 1, 0, 3, 1));
            fieldList.add(new KpiTypeUsedField("indexWeight", companyId, this.taskUserId, this.kpiTypeId, "指标权重", 1, 1, 4, 1));
            fieldList.add(new KpiTypeUsedField("scoreValue", companyId, this.taskUserId, this.kpiTypeId, "评分范围", 1, 1, 5, 1));
            fieldList.add(new KpiTypeUsedField("finishValue", companyId, this.taskUserId, this.kpiTypeId, "完成值录入", 1, 0, 6, 1));
            return fieldList;
        }
        fieldList.add(new KpiTypeUsedField("name", companyId, this.taskUserId, this.kpiTypeId, "指标名称", 1, 1, 1, 1));
        fieldList.add(new KpiTypeUsedField("standard", companyId, this.taskUserId, this.kpiTypeId, "考核标准", 1, 0, 2, 1));
        fieldList.add(new KpiTypeUsedField("scoreRule", companyId, this.taskUserId, this.kpiTypeId, "计分规则", 1, 0, 3, 1));
        fieldList.add(new KpiTypeUsedField("targetValue", companyId, this.taskUserId, this.kpiTypeId, "目标值", 1, 0, 4, 1));
        fieldList.add(new KpiTypeUsedField("unit", companyId, this.taskUserId, this.kpiTypeId, "单位", 1, 1, 5, 1));
        if (this.isPlusType()) {
            fieldList.add(new KpiTypeUsedField("plusLimit", companyId, this.taskUserId, this.kpiTypeId, "加分上限", 1, 1, 6, 1));

        } else if (this.isSubtractType()) {
            fieldList.add(new KpiTypeUsedField("subtractLimit", companyId, this.taskUserId, this.kpiTypeId, "减分上限", 1, 1, 6, 1));

        } else if (this.isPlusSubType()) {
            fieldList.add(new KpiTypeUsedField("scoreSection", companyId, this.taskUserId, this.kpiTypeId, "评分区间", 1, 1, 6, 1));

        } else {
            fieldList.add(new KpiTypeUsedField("indexWeight", companyId, this.taskUserId, this.kpiTypeId, "指标权重", 1, 1, 6, 1));
            fieldList.add(new KpiTypeUsedField("scoreValue", companyId, this.taskUserId, this.kpiTypeId, "评分范围", 1, 1, 7, 1));
        }
        fieldList.add(new KpiTypeUsedField("finishValue", companyId, this.taskUserId, this.kpiTypeId, "完成值录入", 1, 0, 8, 1));
        return fieldList;
    }

    private void initSystemSort() {
        for (KpiTypeUsedField kpiTypeUsedField : this.kpiTypeUsedFields) {
            if (this.isWorkItemType()) {
                kpiTypeUsedField.initWorkItemSort();
                return;
            }
            kpiTypeUsedField.initSort();
        }
    }

    public Set<String> listTypeUsedFieldKey() {
        if (CollUtil.isEmpty(getKpiTypeUsedFields())) {
            return new HashSet<>();
        }
        return CollUtil.map(getKpiTypeUsedFields(), tf -> tf.getName(), true).stream().collect(Collectors.toSet());
    }

    public KpiTypeUsedField getTypeUsedField(String fieldId) {
        if (CollUtil.isEmpty(getKpiTypeUsedFields())) {
            return null;
        }
        Map<String, KpiTypeUsedField> fieldMap = CollUtil.toMap(getKpiTypeUsedFields(), new HashMap<>(), f -> f.getFieldId());
        return fieldMap.get(fieldId);
    }

    public void addTypeUsedField(String finishInfo, String targetInfo, String itemRule, String scoringRule, String kpiItemWeight) {
        if (CollUtil.isEmpty(this.items)) {
            return;
        }
        for (EvalKpi item : this.items) {
            if (this.needShowInputField()) {
                KpiTypeUsedField typeUsedField = this.getTypeUsedField("finishValue");
                if (Objects.nonNull(typeUsedField)) {
                    item.getFieldValueList().add(new ItemCustomFieldValue("finishValue", "完成情况", finishInfo, typeUsedField.getSort()));
                }
            }
            if (this.needShowTargetField()) {
                KpiTypeUsedField typeUsedField = this.getTypeUsedField("targetValue");
                if (Objects.nonNull(typeUsedField)) {
                    item.getFieldValueList().add(new ItemCustomFieldValue("targetValue", "目标", targetInfo, typeUsedField.getSort()));
                }
            }
            if (this.needShowItemRuleField()) {
                KpiTypeUsedField typeUsedField = this.getTypeUsedField("standard");
                if (Objects.nonNull(typeUsedField)) {
                    item.getFieldValueList().add(new ItemCustomFieldValue("standard", "考核标准", itemRule, typeUsedField.getSort()));
                }
            }
            if (this.needShowScoringRuleField()) {
                KpiTypeUsedField typeUsedField = this.getTypeUsedField("scoreRule");
                if (Objects.nonNull(typeUsedField)) {
                    item.getFieldValueList().add(new ItemCustomFieldValue("scoreRule", "计分规则", scoringRule, typeUsedField.getSort()));
                }
            }
            if (this.needShowWeightField()) {
                KpiTypeUsedField typeUsedField = this.getTypeUsedField("indexWeight");
                if (Objects.nonNull(typeUsedField)) {
                    item.getFieldValueList().add(new ItemCustomFieldValue("indexWeight", "权重", kpiItemWeight, typeUsedField.getSort()));
                }
            }
            for (ItemCustomFieldValue fieldValue : item.getFieldValueList()) {
                KpiTypeUsedField typeUsedField = this.getTypeUsedField(fieldValue.getFieldId());
                if (Objects.nonNull(typeUsedField)) {
                    fieldValue.setSort(typeUsedField.getSort());
                    fieldValue.setShow(typeUsedField.getShow());
                }
            }
        }
    }

    public void parseScoreRater(EvalUser user, EmpId opEmpId) {
        this.peerRater.parseMutualNodeConfRaters(user, opEmpId);
        this.subRater.parseMutualNodeConfRaters(user, opEmpId);
        this.superRater.skpiRepeatSuperScore(user, opEmpId);
        this.appointRater.parseNoSkipRaters(user, opEmpId);
        if (CollUtil.isEmpty(this.items)) {
            return;
        }
        //解析指标上的评分人流程
        this.items.forEach(item -> {
            item.parseScoreRater(user, opEmpId);
        });
    }

    public void parseScoreChangeRater(String empId, String evalOrgId, String orgId, Set<ChangeInfo> infos, EvalChangeDmSvc dmSvc) {
        parseTypeRuleChangeRater(empId, evalOrgId, orgId, infos, dmSvc);
        if (Objects.nonNull(this.getPeerRater())) {
            this.getPeerRater().parseMutualNodeConfChangeRaters(empId, evalOrgId, orgId, infos, dmSvc);
        }
        if (Objects.nonNull(this.getSubRater())) {
            this.getSubRater().parseMutualNodeConfChangeRaters(empId, evalOrgId, orgId, infos, dmSvc);
        }
        if (Objects.nonNull(this.getSuperRater())) {
            this.getSuperRater().skpiRepeatChangeSuperScore(empId, evalOrgId, orgId, infos, dmSvc);
        }
        if (Objects.nonNull(this.getAppointRater())) {
            this.getAppointRater().parseNoSkipChangeRaters(empId, evalOrgId, orgId, infos, dmSvc);
        }
        if (CollUtil.isEmpty(this.items)) {
            return;
        }
        //解析指标上的评分人流程
        this.items.forEach(item -> {
            item.parseScoreChangeRater(empId, evalOrgId, orgId, infos, dmSvc);
        });
    }

    public void parseTypeRuleChangeRater(String empId, String evalOrgId, String orgId, Set<ChangeInfo> infos, EvalChangeDmSvc dmSvc) {
        if (Objects.isNull(this.typeRule)) {
            return;
        }
        this.typeRule.getPeerRater().parseMutualNodeConfChangeRaters(empId, evalOrgId, orgId, infos, dmSvc);
        this.typeRule.getSubRater().parseMutualNodeConfChangeRaters(empId, evalOrgId, orgId, infos, dmSvc);
        this.typeRule.getSuperRater().skpiRepeatChangeSuperScore(empId, evalOrgId, orgId, infos, dmSvc);
        this.typeRule.getAppointRater().parseNoSkipChangeRaters(empId, evalOrgId, orgId, infos, dmSvc);
    }

    public boolean isTypeScore() {
        //是否开启维度评分
        return isOpenSuperiorRater()
                || isOpenSelfRater()
                || isOpenSubRater()
                || isOpenAppointRater()
                || isOpenPeerRater();
    }

    public List<NodeWeight> nodesWeight() {
        NodeWeight sup = new NodeWeight(AuditEnum.SUPERIOR_SCORE.getScene(), this.superRater == null ? BigDecimal.ZERO : this.superRater.getNodeWeight());
        NodeWeight self = new NodeWeight(AuditEnum.SELF_SCORE.getScene(), this.selfRater == null ? BigDecimal.ZERO : this.selfRater.getNodeWeight());
        NodeWeight peer = new NodeWeight(AuditEnum.PEER_SCORE.getScene(), this.peerRater == null ? BigDecimal.ZERO : this.peerRater.getNodeWeight());
        NodeWeight subPeer = new NodeWeight(AuditEnum.SUB_SCORE.getScene(), this.subRater == null ? BigDecimal.ZERO : this.subRater.getNodeWeight());
        NodeWeight appoint = new NodeWeight(AuditEnum.APPOINT_SCORE.getScene(), this.appointRater == null ? BigDecimal.ZERO : this.appointRater.getNodeWeight());
        return Arrays.asList(sup, self, peer, subPeer, appoint);
    }

    public Boolean isNoNeedScoreType() {
        return scoreOptType == 16;
    }

    public String isEmptyType() {
        return importOkrFlag;
    }

    /**
     * @param usedFieldDos
     * @see EmpDetailKpiTypePo#initCacheKpiTypes
     */
    public void initCacheKpiTypes(List<KpiTypeUsedField> usedFieldDos) {
        /**组装维度字段配置数据*/
        if (CollUtil.isNotEmpty(usedFieldDos) && CollUtil.isNotEmpty(this.getKpiTypeUsedFields())) {
            for (KpiTypeUsedField kpiTypeUsedField : this.getKpiTypeUsedFields()) {
                if (Objects.equals(kpiTypeUsedField.getFieldId(), "finishValue")) {
                    List<KpiTypeUsedField> fieldList = usedFieldDos.stream().filter(typeUse -> typeUse.getKpiTypeId().equals(kpiTypeUsedField.getKpiTypeId()))
                            .filter(usedField -> Objects.equals(usedField.getFieldId(), "finishValue")).collect(Collectors.toList());
                    if (Objects.isNull(kpiTypeUsedField.getKpiSyncReqConf())) {
                        //{"syncOpen":0,"reqItems":{"inputFinishValue":{"isReq":0,"desc":"完成值必填"},"attachment":{"isReq":0,"desc":"附件必填"},"comment":{"isReq":0,"desc":"备注必填"}}}
                        kpiTypeUsedField.setKpiSyncReqConf(JSONUtil.toBean("{\"syncOpen\":0,\"reqItems\":{\"inputFinishValue\":{\"isReq\":0,\"desc\":\"完成值必填\"},\"attachment\":{\"isReq\":0,\"desc\":\"附件必填\"},\"comment\":{\"isReq\":0,\"desc\":\"备注必填\"}}}", KpiSyncReqConf.class));
                    }
                    if (CollUtil.isNotEmpty(fieldList)) {
                        kpiTypeUsedField.setShow(fieldList.get(0).getShow());
                        return;
                    }
                }
            }
        }
    }

    public String getIsEmptyType() {
        return CollUtil.isEmpty(items) + "";
    }

    //此方法为json化使用返回页面,请不要删掉此方法
    public List<OkrTarget> getKrTree() {
        if (!BusinessConstant.TRUE.equals(getIsOkr())) {
            return null;
        }
        if (CollectionUtils.isEmpty(items)) {
            return null;
        }
        List<OkrTarget> targetList = new ArrayList();
        Map<String, List<EvalKpi>> targetMap = items.stream().filter(l -> StringUtils.isNotEmpty(l.getTargetId())).collect(Collectors.groupingBy(EvalKpi::getTargetId));
        targetMap.forEach((targetId, items) -> {
            OkrTarget refTargetPo = new OkrTarget();
            refTargetPo.setTargetId(targetId);
            targetList.add(refTargetPo);
            if (CollUtil.isEmpty(items)) {
                return;
            }
            EvalKpi evalKpi1 = items.get(0);
            if (evalKpi1 instanceof OkrEvalKpi) {
                OkrEvalKpi evalKpi = (OkrEvalKpi) evalKpi1;
                refTargetPo.setTargetName(evalKpi.getTargetName());
                refTargetPo.setDeptName(evalKpi.getDeptName());

                refTargetPo.setEvaluateStartDate(evalKpi.getEvaluateStartDate());
                refTargetPo.setEvaluateEndDate(evalKpi.getEvaluateEndDate());
                refTargetPo.setOkrTaskName(evalKpi.getOkrTaskName());
                refTargetPo.setOkrTaskId(evalKpi.getOkrTaskId());

                refTargetPo.setKrList(items);
                refTargetPo.setGoalWeight(evalKpi.getGoalWeight());
                refTargetPo.setTargetTags(evalKpi.getTargetTags());
                items.stream().filter(l -> l.getOrder() != null).sorted(Comparator.comparing(EvalKpi::getOrder));
            }
        });
        //增加进度
        if (CollUtil.isNotEmpty(okrGoals)) {
            ListWrap<OkrTarget> goalMap = new ListWrap<>(targetList).asMap(goal -> goal.getTargetId());
            for (OkrGoal okrGoal : okrGoals) {
                OkrTarget targetPo = goalMap.mapGet(okrGoal.getObjectiveId());
                if (targetPo == null) {
                    continue;
                }
                targetPo.setProgress(okrGoal.getProgress());
                targetPo.setHtmlProgress(okrGoal.getLatestProgress());
            }
        }
        //增加排序和目标上的index序号
        //targetList.sort(Comparator.comparing(OkrTarget::getEvaluateStartDate));
        targetList.forEach(targetPo -> targetPo.setIndex(targetList.indexOf(targetPo)));

        return targetList;
    }
}

