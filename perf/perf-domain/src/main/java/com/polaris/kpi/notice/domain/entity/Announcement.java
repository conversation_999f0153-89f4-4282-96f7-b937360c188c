package com.polaris.kpi.notice.domain.entity;

import lombok.Data;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.JsonColumn;

import java.util.List;

// {"title": "这是一个标题","contentItems": [{"itemContent": "第一干嘛啊"},{"itemContent": "第二干嘛啊"}]}
//公告实体
@Data
public class Announcement {
    private String id;
    @JsonColumn
    private Content content;
    private Boolean isReader;
    private String createdTime;
    private String empId;

    @Data
    public static class Content {
        private String title;
        @JsonColumn
        private List<ContentItem> contentItems;

        @Data
        public static class ContentItem {
            private String itemContent;
        }
    }

    public Announcement() {
    }

    public Announcement(String id, Boolean isReader, String empId) {
        if (StringUtils.isEmpty(id) || StringUtils.isEmpty(empId)) {
            throw new IllegalArgumentException(" id or empId must isNotEmpty");
        }
        this.id = id;
        this.isReader = isReader;
        this.empId = empId;
    }

    public Announcement(String id, Content content, Boolean isReader, String createdTime, String empId) {
        if (StringUtils.isEmpty(id) || StringUtils.isEmpty(empId)) {
            throw new IllegalArgumentException(" id or empId must isNotEmpty");
        }
        this.id = id;
        this.content = content;
        this.isReader = isReader;
        this.createdTime = createdTime;
        this.empId = empId;
    }

    public void reader() {
        this.isReader = true;
    }
}
