package com.polaris.kpi.migration.entity;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.URL;

@Slf4j
public class CommonsIODownloader {
    /**
     * 使用FileUtils下载
     */
    public static boolean downloadWithFileUtils(String fileUrl, String localPath) {
        try {
            FileUtils.copyURLToFile(new URL(fileUrl), new File(localPath));
            System.out.println("文件下载成功: " + localPath);
            return true;
        } catch (Exception e) {
            System.err.println("下载失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 使用IOUtils下载
     */
    public static boolean downloadWithIOUtils(String fileUrl, String localPath) {
        try (InputStream in = new URL(fileUrl).openStream();
             FileOutputStream out = new FileOutputStream(localPath)) {

            IOUtils.copy(in, out);
            System.out.println("文件下载成功: " + localPath);
            return true;

        } catch (Exception e) {
            System.err.println("下载失败: " + e.getMessage());
            return false;
        }
    }


    // 使用示例
    public static void main(String[] args) {
        String ossUrl = "https://zxb-online.oss-cn-beijing.aliyuncs.com/migration/ding29238e29f344df30/1756375212689/ding29238e29f344df30_1756375212689.zip";

        // 下载到指定路径
        CommonsIODownloader.downloadWithFileUtils(ossUrl, "/tmp/migration_file.zip");

        // 下载到指定目录
        boolean downloadedPath = CommonsIODownloader.downloadWithFileUtils(ossUrl, "/tmp/migration");
        System.out.println("下载路径: " + downloadedPath);
    }
}
