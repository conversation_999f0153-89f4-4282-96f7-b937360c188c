package com.polaris.kpi.setting.domain.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.common.DelableDomain;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Objects;


@Setter
@Getter
@NoArgsConstructor
public class ResultAuditFlowUser extends DelableDomain {
    private String id;
    private String companyId;
    private String flowInstanceId;
    private String nodeRaterId;
    private String multipleReviewersType;
    private String taskUserId;
    private Integer status;  //
    private Integer curLevel;  //当前层级

    //
    private String taskId;
    private String taskName;
    private String empId;
    private String taskStatus;

    private Boolean isSendUser = true;


    @JSONField(serialize = false)
    public boolean notSend(){
        return Objects.nonNull(isSendUser) && !isSendUser;
    }

    public void upStatus(EvalScoreResult result) {
        if (Objects.nonNull(result)) {
            if (Objects.equals(this.taskUserId, result.getTaskUserId())) {
                this.status = Objects.isNull(result.getAuditStatus()) ? 1 : (Objects.equals(result.getAuditStatus(),"pass") ? 2 : 0);
                this.curLevel = result.getApprovalOrder();
            }
        }
    }

    @JSONField(serialize = false)
    public boolean isFinish(Integer approvalOrder) {
        if (!Objects.equals(this.curLevel, approvalOrder)) {
            return false;
        }
        return true;
    }

    @JSONField(serialize = false)
    public boolean isAloneAudit() {
        return Objects.equals(this.multipleReviewersType, "and");
    }
}
