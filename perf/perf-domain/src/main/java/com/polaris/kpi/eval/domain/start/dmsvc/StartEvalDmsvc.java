//package com.polaris.kpi.eval.domain.start.dmsvc;
//
//import cn.com.polaris.kpi.company.MsgSceneEnum;
//import cn.hutool.core.collection.CollUtil;
//import com.perf.www.common.em.CompanyMsgActionEnum;
//import com.polaris.kpi.eval.domain.start.repo.StartTaskUserFinder;
//import com.polaris.kpi.eval.domain.task.entity.EvalUser;
//import com.polaris.kpi.eval.domain.task.entity.empeval.AsAdminTaskRule;
//import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
//import com.polaris.kpi.eval.domain.task.entity.empeval.EndStartStageRs;
//import com.polaris.kpi.eval.domain.task.entity.msg.MsgTodoAggregate;
//import com.polaris.kpi.eval.domain.task.entity.okr.OkrGoal;
//import com.polaris.kpi.eval.domain.task.event.ThisStageEnded;
//import com.polaris.kpi.eval.domain.task.event.msg.BatchMsgTodoAggregateEvent;
//import com.polaris.kpi.eval.domain.task.type.TalentStatus;
//import com.polaris.kpi.okr.domain.acl.IOkrAcl;
//import com.polaris.sdk.type.EmpId;
//import com.polaris.sdk.type.TenantId;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//
//import java.util.Arrays;
//import java.util.List;
//import java.util.stream.Collectors;
//
//@Service
//@Slf4j
//public class StartEvalDmsvc {
//    private StartTaskUserFinder finder;
//    private IOkrAcl iOkrAcl;
//
//    public EndStartStageRs batchEndStage(String companyId, String taskId, EmpId opEmpId, List<String> adminOrgIds, Integer performanceType) {
//        List<EvalUser> evalUsers = finder.listBaseEvalUser();
//        TenantId tenantId = new TenantId(companyId);
////            List<EvalUser> evalUsers = taskUserDao.listBaseEvalUser(tenantId, event.getTaskUserIds());
//        List<EvalUser> v1Users = evalUsers.stream().filter(evalUser -> evalUser.wasTempTask()).collect(Collectors.toList());
//        EndStartStageRs endStartStageRs = this.enterNextStageV1(companyId, opEmpId, v1Users);
//        List<EvalUser> v2Users = evalUsers.stream().filter(evalUser -> !evalUser.wasTempTask()).collect(Collectors.toList());
//        this.batchEnterNextStage(endStartStageRs, tenantId, v2Users, opEmpId);
//        return endStartStageRs;
//    }
//
//    public EndStartStageRs enterNextStageV1(String tenantId1, EmpId opEmpId, List<EvalUser> v1Users) {
//        TenantId tenantId = new TenantId(tenantId1);
//        EndStartStageRs executeFinisedRs = new EndStartStageRs(tenantId1);
//        BatchMsgTodoAggregateEvent batchMsgs = new BatchMsgTodoAggregateEvent(tenantId, opEmpId);
//
//        for (EvalUser taskUser : v1Users) {
//            // EvalUser taskUser = userRepo.getEmpEval(tenantId, curEvalId);
//            executeFinisedRs.addEval(taskUser);
//            log.info("考核任务是否存在异常 taskUserId:{},hasError:{},ruleConfError:{}", taskUser.getId(), taskUser.hasError(), taskUser.getRuleConfError());
//            if (taskUser.hasError()) {
//                continue;
//            }
//            log.info("开始发起考核任务 taskUserId:{}", taskUser.getId());
//            executeFinisedRs.addOkUser(taskUser);
//            AsAdminTaskRule cycleTask = executeFinisedRs.get(taskUser.getTaskId());
//            cycleTask = cycleTask == null ? empRuleRepo.getEmpEvalMerge(tenantId, taskUser.getId(), EmpEvalMerge.beforeScore) : cycleTask;
//            executeFinisedRs.addAdminTask(taskUser.getTaskId(), cycleTask);
//            // 给考核人发送通知与消息
//            if (!MsgSceneEnum.noExtMsgTodoCompanys.contains(taskUser.getCompanyId().getId())) {
//                MsgTodoAggregate msgTodoAggregate = new MsgTodoAggregate(tenantId, taskUser.getTaskId(), cycleTask.taskName(), taskUser.getEmpId(), taskUser.getId()).useScene(MsgSceneEnum.CREATE_TASK, CompanyMsgActionEnum.CONFIRM).addCenterMsg().addExtTempValue("org", "");
//                batchMsgs.addEvent(msgTodoAggregate);
//            }
//        }
//        batchMsgs.publish();
//        for (EvalUser eval : executeFinisedRs.getFinisedEvals()) {
//            new ThisStageEnded(executeFinisedRs.get(eval.getTaskId()), eval, TalentStatus.CREATED, opEmpId.getId()).fire();
//            List<OkrGoal> okrGoals = iOkrAcl.synOkrs(tenantId1, eval.getTaskId(), eval.getEmpId());
//            if (CollUtil.isEmpty(okrGoals)) {
//                continue;
//            }
//            tx.runTran(() -> {
//                this.importOkrItem(eval.getCompanyId(), eval, okrGoals, opEmpId);//单独一个事务
//            });
//
//        }
//        return executeFinisedRs;
//    }
//
//}
