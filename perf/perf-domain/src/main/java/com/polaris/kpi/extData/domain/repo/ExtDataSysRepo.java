package com.polaris.kpi.extData.domain.repo;

import com.polaris.kpi.extData.domain.entity.ExtDataSys;
import com.polaris.kpi.extData.domain.entity.ExtDataSysConf;

import java.util.List;

public interface ExtDataSysRepo {
    String addExtSys(ExtDataSys extDataSys);
    void addExtSysConf(List<ExtDataSysConf> sysConfList, String sysId);
    void updateExtSys(ExtDataSys extDataSys);
    void updateExtSysConf(List<ExtDataSysConf> sysConfList);
    void deleteExtSys(String id);
    void deleteExtSysConf(String id);
}
