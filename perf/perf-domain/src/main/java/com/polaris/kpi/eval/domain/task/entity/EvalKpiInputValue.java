package com.polaris.kpi.eval.domain.task.entity;


import cn.com.polaris.kpi.eval.ItemCustomFieldValue;
import cn.com.polaris.kpi.eval.KpiItemUsedField;
import cn.com.polaris.kpi.eval.KpiOtherReqField;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.entity.log.ItemDynamicLog;
import com.polaris.sdk.type.ListWrap;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.JsonColumn;
import org.lufei.ibatis.common.data.ToDataBuilder;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 * @date 2025/11/7 14:54
 */
@Setter
@Getter
@Slf4j
public class EvalKpiInputValue {

    private String id;
    private Date createTime;
    private List<ItemCustomFieldValue> fieldValueList = new ArrayList<>();
    private String files;
    private int finalSubmitFinishValue;//最终提交完成值标识
    private InputFinishValCache finishValCache;      //指标完成值暂存
    private String finishValueComment;
    private List<ItemDynamicLog> inputChangeRecord;      //
    private String inputFormat;//录入格式
    private String itemFieldJson;//指标关联的阈值字段
    private BigDecimal itemFinishValue;//指标项完成值
    private String itemFinishValueText;
    private String itemFormula;//指标计算公式
    private String itemRule;//考核规则
    private BigDecimal itemTargetValue;//指标项目标值
    private String itemTargetValueText; //指标项文本类型目标值
    private String itemType;//指标项类型（量化/非量化）
    private String itemUnit;//指标项单位
    private BigDecimal itemWeight;//指标项权重
    private String kpiItemId;//指标项id
    private String kpiItemName;//指标项名称
    private String kpiTypeClassify;//指标类别，为空表示已有类别，custom自定义/plus加分项/subtract减分项
    private String kpiTypeId;//指标类id
    private String kpiTypeName;//指标类名称
    private String okrGoalId;            //okr经营计划指标关联的目标ID
    @JsonColumn
    private KpiOtherReqField otherReqField; //指标其它属性必填配置json串（如完成值必填、附件必填、备注必填）
    private String scorerType;//指标评分类型（exam:按自定评分流程/user:定向[员工/主管]）
    private String taskUserId;//task user 表的id
    private String workItemFinishValue;//工作事项完成情况说明

    public String getFinishVal() {
        return Objects.nonNull(this.itemFinishValue) ? StrUtil.toString(this.itemFinishValue)
                : (StrUtil.isNotBlank(this.itemFinishValueText) ? this.itemFinishValueText : this.workItemFinishValue);
    }

    public String getItemIdJoinName() {
        StringBuilder sb = new StringBuilder();
        sb.append(this.kpiItemName).append(System.lineSeparator()).append("(指标ID:").append(this.kpiItemId).append(")");
        return sb.toString();
    }

    public void buildInputRecord(List<ItemDynamicLog> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        ListWrap<ItemDynamicLog> itemGroups = new ListWrap<>(list).groupBy(ItemDynamicLog::getKpiItemId);
        List<ItemDynamicLog> logs = itemGroups.groupGet(this.getKpiItemId());
        if (CollUtil.isNotEmpty(logs)) {
            this.inputChangeRecord = logs;
            this.finishValueComment = this.inputChangeRecord.get(0).getComment();
        }
    }

    /**
     * 初始化指标自定义字段配置
     */
    public void initKpiItemUsedFields(List<KpiItemUsedField> itemUsedFields) {
        /**初始化指标自定义字段配置*/
        if (CollUtil.isEmpty(itemUsedFields)) {
            return;
        }

        List<ItemCustomFieldValue> fieldValueList = new ArrayList<>();
        //只需要数值类型
        itemUsedFields.stream()
                .filter(KpiItemUsedField::isNum)
                .forEach(item -> {
                    ItemCustomFieldValue fieldValue = new ItemCustomFieldValue();
                    BeanUtils.copyProperties(item, fieldValue);
                    fieldValue.setFieldName(item.getName());
                    fieldValue.setFieldValue(item.getValue());
                    fieldValue.setFieldStatus(item.getStatus());
                    fieldValue.setId(item.getFieldId());
                    fieldValue.setIsReq(item.getReq());
                    fieldValueList.add(fieldValue);
                });
        this.fieldValueList = fieldValueList;
    }


    public void buildCacheFinishValue(InputFinishValCache valCache, InputFinishValCache importCache) {
        this.finishValCache = valCache;
        if (!Objects.isNull(valCache) && !Objects.isNull(importCache)) {
            this.itemFinishValue = importCache.notNullItemFinishValue(valCache.getItemFinishValue());
            this.itemFinishValueText = importCache.notNullitemFinishValueText(valCache.getItemFinishValueText());
            this.workItemFinishValue = importCache.notNullWorkItemFinishValue(valCache.getWorkItemFinishValue());
            this.finishValueComment = importCache.notNullComment(valCache.getFinishValueComment());
            this.files = JSONUtil.toJsonStr(importCache.notNullFiles(valCache.getFiles()));
        } else if (!Objects.isNull(importCache)) {
            this.itemFinishValue = importCache.getItemFinishValue();
            this.itemFinishValueText = importCache.getItemFinishValueText();
            this.workItemFinishValue = importCache.getWorkItemFinishValue();
            this.finishValueComment = importCache.getFinishValueComment();
            this.files = JSONUtil.toJsonStr(importCache.getFiles());
        } else if (!Objects.isNull(valCache)) {
            this.itemFinishValue = valCache.getItemFinishValue();
            this.itemFinishValueText = valCache.getItemFinishValueText();
            this.workItemFinishValue = valCache.getWorkItemFinishValue();
            this.finishValueComment = valCache.getFinishValueComment();
            this.files = JSONUtil.toJsonStr(valCache.getFiles());
        }
    }

    public void buildImportCacheFinishValue(ImportFinishValCache valCache) {
        this.finishValCache = new ToDataBuilder<>(valCache, InputFinishValCache.class).data();
        if (Objects.isNull(valCache)) {
            return;
        }
        this.itemFinishValue = valCache.getItemFinishValue();
        this.itemFinishValueText = valCache.getItemFinishValueText();
        this.workItemFinishValue = valCache.getWorkItemFinishValue();
        this.finishValueComment = valCache.getFinishValueComment();
        this.files = JSONUtil.toJsonStr(valCache.getFiles());
    }
}
