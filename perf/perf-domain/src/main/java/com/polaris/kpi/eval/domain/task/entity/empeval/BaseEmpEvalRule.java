package com.polaris.kpi.eval.domain.task.entity.empeval;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.eval.BusinessPlanItem;
import cn.com.polaris.kpi.eval.Rater;
import cn.com.polaris.kpi.eval.RaterNode;
import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.perf.www.common.constant.BusinessConstant;
import com.perf.www.common.em.EvaluateTypeEnum;
import com.polaris.kpi.common.DelableDomain;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.admineval.CommentReqConf;
import com.polaris.kpi.eval.domain.task.entity.admineval.EmpEvalChangeResult;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.*;
import com.polaris.kpi.eval.domain.task.entity.grade.IndLevelGroup;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.annotations.JsonColumn;

import java.math.BigDecimal;
import java.util.Date;
import java.util.*;
import java.util.Objects;
import java.util.function.BooleanSupplier;
import java.util.stream.Collectors;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.domain.task.entity.empeval
 * @Author: lufei
 * @CreateTime: 2022-11-24  09:14
 * @Version: 1.0
 */
@Getter
@Setter
public abstract class BaseEmpEvalRule extends DelableDomain {
    //旧的字段;
    //protected PerfTemplEvaluateExecute templExecuteJson;//模板执行阶段配置
    //protected PerfTemplEvaluate templEvaluateJson;//模板评价配置json
    //protected BigDecimal customFullScore;//自定义满分分值
    //protected Integer isNewEmp;//是否有新人任务

    protected String empEvalId;                       //外键task_user.id, 新加
    @JSONField(serialize = false)
    protected TenantId companyId;                     //公司id
    protected String ruleName;                        //考核规则名字
    protected String ruleDesc;                        //考核规则描述

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    protected String evaluateType;                    //评分流程类型 ： simple-简易流程，360-360°考核流程，custom-自定义流程
    protected TypeWeightConf typeWeightConf;          //指标类别权重
    protected ScoreValueConf scoreValueConf;          //分值的设定

    protected AffirmTaskConf confirmTask;             //确认任务  对应模板 templAffirmJson:PerfTemplEvaluateAffirm
    protected InputNotifyConf inputNotify;              //完成值录入通知设置
    protected FinishValueAuditConf finishValueAudit;             //完成值审核
    protected EditExeIndiConf editExeIndi;            //执行中修改指标  对应模板 templExecuteJson:PerfTemplEvaluateExecute
    //protected CustomScoreConf customScoreConf;      //自定的评分环节在指标上
    //以下评分配置

    protected ScoreConf scoreConf;                  //评分设置

    protected List<BaseScoreResult> totalLevelResults;

    protected List<EmpEvalScorerNode> totalLevelScorerNodes; //评价总等级

    public void setScoreConf(ScoreConf scoreConf) {
        this.scoreConf = scoreConf;
    }

    protected ScoreCommentConf commentConf;          //评语与总结配置
    protected CommentReqConf commentReqConf;

    protected EnterScoreConf enterScore;              //启动评分配置 ,多字段合并
    protected RaterNodeConf s3SelfRater;              //360或简易 自评人
    //protected MutualRaterConf s3MutualRater;          //360或简易 互评人
    protected MutualNodeConf s3PeerRater;                   //360或简易 同级互评人
    protected MutualNodeConf s3SubRater;                    //360下级
    protected S3SuperRaterConf s3SuperRater;          //360或简易 上级人
    protected S3RaterBaseConf s3AppointRater;          //默认流程指定评分人
    //protected SimpleScorerConf simpleItemScorer;     //360或简易 定向人
    protected ScoreViewConf scoreView;                 //可见范围
    //以下结果配置
    protected AuditResultConf auditResult;             //结果校准,从templEvaluateJson:PerfTemplEvaluate 模板评价配置json 单独出来的
    @JsonColumn
    protected InterviewConf interviewConf;             //结果面谈,多个字段合并
    protected ConfirmResultConf confirmResult;         //结果确认,多个字段合并
    protected PublishResultConf publishResult;         //结果公示,多个字段合并
    protected AppealConf appealConf;                   //结果申诉,多个字段合并
    @JsonColumn
    protected DeadLineConf deadLineConf;               //阶段截止时间配置
    protected String levelGroupId;//关联哪个等级组/perf_evaluation_level 的level_group_id
    @JSONField(serialize = false)
    protected List<RaterNode> indexRaters;                   //评分人索引,用于员工任务列表的显示,编辑时更新索引
    @JSONField(serialize = false)
    protected List<RaterNode> askIndexRaters;                   //评分人索引,用于员工任务列表的显示,编辑时更新索引
    protected Integer indicatorCnt;
    private int editStatus = 0;             //1=指标确认变更 2=执行阶段变更 4=校准阶段变更 8=公示阶段变更 16=申诉阶段变更 256=完成值审核（考核表配置）512=完成值审核变更
    protected String initiator;  //考核表发起人
    protected Integer showResultType; //'结果呈现:111=总分,总等级,维度等级'
    protected Integer createTotalLevelType; //'绩效总等级生成方式 1 = 自动, 2= 手动'
    protected List<Rater> totalLevelRaters; //绩效总等级打分人员,只有具体人员, 从模板或表上解析后的结果
    protected KpiListWrap kpiTypes;
    protected String superiorScoreOrder; //上级评顺序类型（同时/依次）
    private boolean isChanged = false;

    public void setKpiTypes(KpiListWrap kpiTypes) {
        this.kpiTypes = kpiTypes;
    }

    public BaseEmpEvalRule() {
    }

    protected ScoreSortConf scoreSortConf;         //2.0.0新版:新加字段 评分环节顺序配置

    @JSONField(serialize = false)
    public boolean isCustom() {
        return EvaluateTypeEnum.CUSTOM.getType().equals(this.evaluateType);
    }

    public FinishValueAuditConf getFinishValueAudit() {
        if (Objects.isNull(this.finishValueAudit)) {
            return new FinishValueAuditConf("false");
        }
        return finishValueAudit;
    }

    public String getMultiType() {
        String multiType = Objects.nonNull(this.scoreConf) ? this.scoreConf.getMultiType() : "and";
        if (StrUtil.isBlank(multiType)) {
            multiType = "and";
        }
        return multiType;
    }
    public boolean isTypeWeightOpen() {
        return typeWeightConf.isOpen();
    }

    protected RaterNodeConf nodeRule(String scene) {
        if (SubScoreNodeEnum.isSelfScore(scene)) {
            return getS3SelfRater();
        }
        if (SubScoreNodeEnum.isSuperioNode(scene)) {
            return getS3SuperRater();
        }
        if (SubScoreNodeEnum.isSubNode(scene)) {
            return getS3SubRater();
        }
        if (SubScoreNodeEnum.isPeerNode(scene)) {
            return getS3PeerRater();
        }
        return new RaterNodeConf();
    }

    public boolean isInitiator(String opEmpId) {
        return opEmpId.equals(this.initiator);
    }

    public boolean isBaseScoreOpen() {
        return scoreValueConf.getBaseScore() != null;
    }

    public BigDecimal baseScore() {
        BigDecimal baseScore = scoreValueConf.getBaseScore();
        return baseScore == null ? BigDecimal.ZERO : baseScore;
    }

    @JSONField(serialize = false)
    public boolean isOpenSelfRater() {
        return s3SelfRater != null && s3SelfRater.isOpen();
    }

    //初始化评分人索引
    public List<RaterNode> initIndexRaters() {
        String jsonStr = JSONUtil.toJsonStr(this);
        BaseEmpEvalRule bean = JSONUtil.toBean(jsonStr, this.getClass());
        bean.initIndexRatersInner();
        this.indexRaters = bean.indexRaters;
        this.askIndexRaters = bean.askIndexRaters;
        return indexRaters;
    }



    public List<RaterNode> initIndexRatersInner() {
        extendsRaterRule(getKpiTypes());
        Map<String, RaterNode> nodeMap = new HashMap<>();
        Map<String, RaterNode> askNodeMap = new HashMap<>();
        for (EmpEvalKpiType kpiType : kpiTypes.getDatas()) {
            if (Objects.equals(kpiType.getKpiTypeClassify(), "ask360")) {
                kpiType.initAskNodeRater(askNodeMap);
                continue;
            }
            kpiType.initNodeRaterV3(nodeMap);
        }
        Collection<RaterNode> values = nodeMap.values();
        Collection<RaterNode> askValues = askNodeMap.values();
        this.indexRaters = new ArrayList<>(values);
        this.askIndexRaters = new ArrayList<>(askValues);
        List<RaterNode> sorts = new ArrayList<>();
        List<RaterNode> askSorts = new ArrayList<>();
        ListWrap<RaterNode> wrap = new ListWrap<>(indexRaters).asMap(raterNode -> raterNode.getNode());
        ListWrap<RaterNode> askWrap = new ListWrap<>(askIndexRaters).asMap(raterNode -> raterNode.getNode());
        List<ScoreSortConf.SortItem> sortItems = scoreSortConf.sort();
        for (ScoreSortConf.SortItem sortItem : sortItems) {
            String nodeName = sortItem.isSelfNode() ? "self"
                    : sortItem.isAppointNode() ? "appoint"
                    : sortItem.isSuperNode() ? "super"
                    : sortItem.isSubNode() ? "sub"
                    : sortItem.isPeerNode() ? "peer" : "";
            RaterNode node = wrap.mapGet(nodeName);
            RaterNode askNode = askWrap.mapGet(nodeName);
            if (Objects.nonNull(node)) {
                sorts.add(node);
            }
            if (Objects.nonNull(askNode)) {
                askSorts.add(askNode);
            }
        }
        this.indexRaters = sorts;
        //添加打总等级
        if (CollUtil.isNotEmpty(this.totalLevelRaters)) {
            this.indexRaters.add(new RaterNode("totalLevel",this.totalLevelRaters));
        }
        //添加定向评
        if (Objects.nonNull(wrap.mapGet("item"))) {
            this.indexRaters.add(0,wrap.mapGet("item"));
        }
        this.askIndexRaters = askSorts;
        return indexRaters;
    }

    //初始化评分人索引
    public List<EvalAudit> initAudit(EmpId empId) {
        List<EvalAudit> ratersNodes = new ArrayList<>();
        for (EmpEvalKpiType kpiType : kpiTypes.getDatas()) {
            kpiType.initAudit(ratersNodes, empId);
        }
        return ratersNodes;
    }


    public boolean needComputeLevel() {
        if (createTotalLevelType == null) {
            return true;
        }
        return createTotalLevelType != null && createTotalLevelType == 1;
    }


    public String openAppointScore() {
        return openScore(getS3AppointRater());
    }

    private String openScore(RaterNodeConf raterConf) {
        if (raterConf != null) {
            return Boolean.toString(raterConf.isOpen());
        }
        return Boolean.FALSE.toString();
    }

    public String openSuperiorScore() {
        return openScore(s3SuperRater);
    }

    public String openSelfScore() {
        return openScore(s3SelfRater);
    }

    public String openMutualScoreFlag() {
        String peer = openScore(s3PeerRater);
        if (Boolean.valueOf(peer)) {
            return peer;
        }
        String sub = openScore(s3SubRater);
        if (Boolean.valueOf(sub)) {
            return sub;
        }
        return Boolean.FALSE.toString();
    }

    public void useScoreConf() {
        if (isCustom()) {
            customUseRule();
            return;
        }
        if (s3SelfRater != null && s3SelfRater.isOpen()) {
            s3SelfRater.setAnonymous(Boolean.FALSE.toString());
        }
        if (s3PeerRater != null && s3PeerRater.isOpen()) {
            s3PeerRater.extendMutiConf(scoreConf);
        }
        if (s3SubRater != null && s3SubRater.isOpen()) {
            s3SubRater.extendMutiConf(scoreConf);
        }
        if (s3SuperRater != null && s3SuperRater.isOpen()) {
            s3SuperRater.extendMutiConf(scoreConf);
        }
    }


    private void customUseRule() {
        for (EmpEvalKpiType kpiType : typeDatas()) {
            if (kpiType.getTypeRule() != null) {
                kpiType.getTypeRule().extendMutiConf(scoreConf);
            } else if (kpiType.isOpenRaterRule()) {
                kpiType.extendMutiConf(scoreConf);
                EvalItemScoreRule newTypeRule = new EvalItemScoreRule();
                BeanUtil.copyProperties(kpiType, newTypeRule);
                newTypeRule.customAsRaterConf();
                kpiType.setTypeRule(newTypeRule);
            }
            List<? extends EvalKpi> items = kpiType.getItems();
            if (CollUtil.isEmpty(items)) {
                continue;
            }
            for (EvalKpi item : items) {
                EvalItemScoreRule customItemScoreRule = item.getItemScoreRule();
                if (Objects.isNull(customItemScoreRule)) {
                    continue;
                }
                customItemScoreRule.extendMutiConf(scoreConf);
            }
        }
    }

    protected abstract Iterable<? extends EmpEvalKpiType> typeDatas();

    public boolean totalLevelScoreIsEnd() {
        for (BaseScoreResult totalLevelResult : totalLevelResults) {
            if (!totalLevelResult.isPassed()) {
                return false;
            }
        }
        return true;
    }

    public void initRuleBaseInfo(String opEmpId) {
        this.createdTime = new Date();
        this.updatedTime = new Date();
        this.createdUser = opEmpId;
        this.updatedUser = opEmpId;
    }

    //接收指标分类并标准化 继承评分流程
    public void extendsRaterRule(KpiListWrap kpiTypes) {
        this.kpiTypes = kpiTypes;
        EvalItemScoreRule global = new EvalItemScoreRule();
        global.setCompanyId(companyId);
        global.setTaskUserId(this.empEvalId);
        global.asRaterConf(s3SelfRater, s3PeerRater, s3SubRater, s3SuperRater, s3AppointRater);
        this.kpiTypes.extendsRaterRule(global);
        if (scoreConf != null) {
            this.useScoreConf();
        }
    }


    public void extendsRaterRuleForEdit() {
//        EvalItemScoreRule global = new EvalItemScoreRule();
//        global.setCompanyId(companyId);
//        global.setTaskUserId(this.empEvalId);
//        global.asRaterConf(s3SelfRater, s3PeerRater, s3SubRater, s3SuperRater, s3AppointRater);
        //this.kpiTypes.extendsRaterRuleForEdit(global);
        if (scoreConf != null) {
            this.useScoreConf();
        }
    }

    public void copyLevelGroup(List<IndLevelGroup> levelGroups) {
        if (CollUtil.isEmpty(levelGroups)) {
            return;
        }
        ListWrap<IndLevelGroup> asMap = new ListWrap<>(levelGroups).asMap(levelGroup -> levelGroup.getId());
        for (EmpEvalKpiType kpiType : typeDatas()) {
            IndLevelGroup typeLevelGroup = asMap.mapGet(kpiType.getIndLevelGroupId());
            kpiType.setIndLevelGroup(typeLevelGroup);
            List<? extends EvalKpi> items = kpiType.getItems();
            if (CollUtil.isEmpty(items)) {
                continue;
            }
            items.forEach(item -> {
                IndLevelGroup indLevelGroup = asMap.mapGet(item.getIndLevelGroupId());
                item.setIndLevelGroup(indLevelGroup);
            });
        }
    }

    protected void transferScoringIf(Collection<String> scoreTypes, String fromEmpId, KpiEmp toEmp, Boolean raterWasExist, Boolean wasCustom) {
        for (String scoreType : scoreTypes) {
            if (SubScoreNodeEnum.isSuperioNode(scoreType) && s3SuperRater != null) {
                s3SuperRater.replaceRater(fromEmpId, toEmp);
            }
            if (SubScoreNodeEnum.isSubNode(scoreType) && s3SubRater != null) {
                if (raterWasExist && !wasCustom) {
                    s3SubRater.replaceRater(fromEmpId, toEmp, raterWasExist);
                } else {
                    s3SubRater.replaceRater(fromEmpId, toEmp);
                }
            }
            if (SubScoreNodeEnum.isPeerNode(scoreType) && s3PeerRater != null) {
                if (raterWasExist && !wasCustom) {
                    s3PeerRater.replaceRater(fromEmpId, toEmp, raterWasExist);
                } else {
                    s3PeerRater.replaceRater(fromEmpId, toEmp);
                }
            }
            if (SubScoreNodeEnum.isAppointScore(scoreType) && s3AppointRater != null) {
                s3AppointRater.replaceRater(fromEmpId, toEmp);
            }
            KpiListWrap kpiTypes = getKpiTypes();
            kpiTypes.transferScoringIf(scoreType, fromEmpId, toEmp, raterWasExist, wasCustom);
        }
    }

    protected void transferScoringIfV3(Collection<String> scoreTypes, String fromEmpId, KpiEmp toEmp,  Boolean wasCustom) {
        scoreTypes.forEach(scoreType -> {
            if (SubScoreNodeEnum.isSuperioNode(scoreType) && s3SuperRater != null) {
                s3SuperRater.replaceRaterV3(fromEmpId, toEmp);
            }
            if (SubScoreNodeEnum.isSubNode(scoreType) && s3SubRater != null) {
                s3SubRater.replaceRaterV3(fromEmpId, toEmp);
            }
            if (SubScoreNodeEnum.isPeerNode(scoreType) && s3PeerRater != null) {
                s3PeerRater.replaceRaterV3(fromEmpId, toEmp);
            }
            if (SubScoreNodeEnum.isAppointScore(scoreType) && s3AppointRater != null) {
                s3AppointRater.replaceRaterV3(fromEmpId, toEmp);
            }
            KpiListWrap kpiTypes = getKpiTypes();
            kpiTypes.transferScoringIfV3(scoreType, fromEmpId, toEmp, wasCustom);

            //总等级替换
            replaceTotalLevelRaterV3(fromEmpId, toEmp);
        });
    }

    public void replaceTotalLevelRaterV3(String fromEmpId, KpiEmp toEmp) {
        if (CollUtil.isEmpty(totalLevelRaters)) {
            return;
        }
        // raters 是否包含 toEmp
        boolean raterExist = totalLevelRaters.stream().anyMatch(rater -> StrUtil.equals(rater.getEmpId(), toEmp.getEmpId()));
        if (raterExist) {
            totalLevelRaters = totalLevelRaters.stream()
                    .filter(s -> !StrUtil.equals(s.getEmpId(), fromEmpId))
                    .collect(Collectors.toList());
        } else {
            totalLevelRaters.stream().filter(rater -> StrUtil.equals(fromEmpId, rater.getEmpId())).forEachOrdered(rater -> {
                rater.setEmpId(toEmp.getEmpId());
                rater.setEmpName(toEmp.getEmpName());
                rater.setAvatar(toEmp.getAvatar());
            });
        }
    }

    protected void skipRater(Set<String> scoreTypes, String skipUserId) {
        for (String scoreType : scoreTypes) {
            if (SubScoreNodeEnum.isSuperioNode(scoreType) && s3SuperRater != null) {
                s3SuperRater.skipRater(skipUserId);
            }
            if (SubScoreNodeEnum.isSubNode(scoreType) && s3SubRater != null) {
                s3SubRater.skipRater(skipUserId);
            }
            if (SubScoreNodeEnum.isPeerNode(scoreType) && s3PeerRater != null) {
                s3PeerRater.skipRater(skipUserId);
            }
            if (SubScoreNodeEnum.isAppointScore(scoreType) && s3AppointRater != null) {
                s3AppointRater.skipRater(skipUserId);
            }
            KpiListWrap kpiTypes = getKpiTypes();
            kpiTypes.skipRaterIf(scoreType, skipUserId);
        }
    }

    public List<EmpEvalKpiType> inviteMutualTypeEmp(String scene, Map<String, List<Rater>> mutuRaters) {
        List<EmpEvalKpiType> kpiTypes = new ArrayList<>();
        for (EmpEvalKpiType type : typeDatas()) {
            if (mutuRaters.containsKey(type.getKpiTypeId())) {
                List<Rater> raters = mutuRaters.get(type.getKpiTypeId());
                kpiTypes.add(type.updateMutualRater(scene, raters));
            }
        }
        return kpiTypes;
    }

    public List<EvalItemScoreRule> inviteMutualEmp(String scene, Map<String, List<Rater>> mutuRaters) {
        List<EvalItemScoreRule> rsRules = new ArrayList<>();
        for (EmpEvalKpiType type : typeDatas()) {
            for (EvalKpi item : type.getItems()) {
                if (mutuRaters.containsKey(item.getKpiItemId())) {
                    List<Rater> raters = mutuRaters.get(item.getKpiItemId());
                    if (Objects.isNull(item.getItemScoreRule())) {
                        continue;
                    }
                    EvalItemScoreRule itemScoreRule = item.updateMutualRater(scene, raters);
                    rsRules.add(itemScoreRule);
                }
            }
        }
        return rsRules;
    }


    public List<EvalItemScoreRule> inviteMutualEmp(String scene, List<Rater> mutuRaters) {
        List<EvalItemScoreRule> rsRules = new ArrayList<>();
        for (EmpEvalKpiType type : typeDatas()) {
            for (EvalKpi item : type.getItems()) {
                if (Objects.isNull(item.getItemScoreRule())) {
                    continue;
                }
                EvalItemScoreRule itemScoreRule = item.updateMutualRater(scene, mutuRaters);
                rsRules.add(itemScoreRule);
            }
        }
        return rsRules;
    }

    public List<EvalItemScoreRule> listItemScoreRule() {
        List<EvalItemScoreRule> rsRules = new ArrayList<>();
        for (EmpEvalKpiType type : typeDatas()) {
            for (EvalKpi item : type.getItems()) {
                if (Objects.nonNull(item.getItemScoreRule())) {
                    rsRules.add(item.getItemScoreRule());
                }
            }
        }
        return rsRules;
    }

    public String buildPublishResult(BigDecimal finalScore, String evaluationLevel, String perfCoefficient) {
        return this.publishResult.getPubDimension(this.showResultType, finalScore, evaluationLevel, perfCoefficient);
    }

    public String getPlanItemIds() {
        List<String> planItemIds = new ArrayList<>();
        for (EmpEvalKpiType kpiType : this.kpiTypes.getDatas()) {
            if (CollUtil.isNotEmpty(kpiType.getItems())) {
                planItemIds.addAll(kpiType.getPlanItemIds());
            }
        }
        return StrUtil.join(",", planItemIds);
    }

    public void initOkrGoalId(List<BusinessPlanItem> planItemList) {
        if (CollUtil.isEmpty(planItemList)) {
            return;
        }
        Map<String, BusinessPlanItem> planItemMap = CollUtil.toMap(planItemList, new HashMap<>(), p -> p.getIndexId());
        if (CollUtil.isEmpty(this.kpiTypes.getDatas())) {
            return;
        }
        for (EmpEvalKpiType data : this.kpiTypes.getDatas()) {
            data.initOkrGoalId(planItemMap);
        }
    }

    public void refreshOkrGoalId(List<BusinessPlanItem> planItemList) {
        if (CollUtil.isEmpty(planItemList)) {
            return;
        }
        Map<String, BusinessPlanItem> planItemMap = CollUtil.toMap(planItemList, new HashMap<>(), p -> p.getIndexId());
        if (CollUtil.isEmpty(this.kpiTypes.getDatas())) {
            return;
        }
        for (EmpEvalKpiType data : this.kpiTypes.getDatas()) {
            data.refreshOkrGoalId(planItemMap);
        }
    }

    public String getSuperiorScoreWay() {
        if (Objects.isNull(this.kpiTypes.getDatas())) {
            return null;
        }
        for (EmpEvalKpiType data : this.kpiTypes.getDatas()) {
            if (StrUtil.isNotBlank(data.getSuperiorScoreWay())) {
                return data.getSuperiorScoreWay();
            }
        }
        return BusinessConstant.SUPERIOR_SCORE_ORDER_SAME_TIME;
    }

    public void setSuperiorScoreOrder() {
        //初始化s3到 指标上
        this.extendsRaterRule(this.kpiTypes);
        this.superiorScoreOrder = isCustom() ? this.getSuperiorScoreWay() : this.getS3SuperRater().getSuperiorScoreOrder();
    }

    @JSONField(serialize = false)
    public List<String> listAsk360EvalId() {
        List<String> ids = new ArrayList<>();
        for (EmpEvalKpiType kpiType : this.kpiTypes.getDatas()) {
            if (kpiType.isAskType()) {
                ids.add(kpiType.getAsk360EvalId());
            }
        }
        return ids;
    }

    @JSONField(serialize = false)
    public List<EmpEvalKpiType> listAsk360Types() {
        List<EmpEvalKpiType> askTypes = new ArrayList<>();
        for (EmpEvalKpiType kpiType : this.kpiTypes.getDatas()) {
            if (kpiType.isAskType()) {
                askTypes.add(kpiType);
            }
        }
        return askTypes;
    }

    @JSONField(serialize = false)
    public List<EmpEvalKpiType> listAsk360MutualTypes() {
        List<EmpEvalKpiType> askTypes = new ArrayList<>();
        for (EmpEvalKpiType kpiType : this.kpiTypes.getDatas()) {
            if (kpiType.isAskType()) {
                if (openPeerMutual() || openSubMutual() || kpiType.openPeerMutual() || kpiType.openSubMutual()) {
                    askTypes.add(kpiType);
                }
            }
        }
        return askTypes;
    }

    @JSONField(serialize = false)
    public boolean hasAskAllEndScore() {
        for (EmpEvalKpiType askType : this.listAsk360Types()) {
            if (Objects.isNull(askType.getAsk360EvalScore())) {
                return false;
            }
        }
        return true;
    }

    @JSONField(serialize = false)
    public boolean openPeerMutual() {
        if (Objects.nonNull(this.s3PeerRater) && this.s3PeerRater.isOpen()) {
            if (Objects.nonNull(this.s3PeerRater.getAppointer())) {
                return true;
            }
        }
        return false;
    }

    @JSONField(serialize = false)
    public boolean openSubMutual() {
        if (Objects.nonNull(this.s3SubRater) && this.s3SubRater.isOpen()) {
            if (Objects.nonNull(this.s3SubRater.getAppointer())) {
                return true;
            }
        }
        return false;
    }

    public void refreshIndexRaters(String node) {
        if (indexRaters == null) {
            indexRaters = new ArrayList<>();
        }
        if ("peer".equals(node) || "sub".equals(node)) {
            List<Rater> raterList = new ArrayList<>();
            for (EmpEvalKpiType kpiType : this.kpiTypes.getDatas()) {
                if (kpiType.isNoNeedScoreType()) {
                    continue;
                }
                for (EvalKpi item : kpiType.getItems()) {
                    if (Objects.equals(node, "peer")) {
                        if (Objects.nonNull(item.getItemScoreRule())) {
                            raterList.addAll(item.getItemScoreRule().getPeerRater().getRaters());
                        }
                    } else {
                        if (Objects.nonNull(item.getItemScoreRule())) {
                            raterList.addAll(item.getItemScoreRule().getSubRater().getRaters());
                        }
                    }
                }
                raterList = CollUtil.distinct(raterList);
            }
            boolean exist = CollUtil.filterNew(this.indexRaters, r -> Objects.equals(r.getNode(), node)).size() > 0;
            if (exist) {
                for (RaterNode indexRater : this.indexRaters) {
                    if (Objects.equals(indexRater.getNode(), node)) {
                        indexRater.setRaters(raterList);
                    }
                }
                return;
            }
            indexRaters.add(new RaterNode(node, raterList));
        }
        return;
    }

    public List<RaterNode> getInviteMutualAuditRater() {
        List<RaterNode> raterNodes = new ArrayList<>();
        if (!this.isCustom()) {
            if (Objects.nonNull(this.s3PeerRater) && this.s3PeerRater.isOpen() && Objects.nonNull(this.s3PeerRater.getAppointer())) {
                InviteMutualAudit inviteMutualAudit = this.s3PeerRater.getAppointer().getInviteMutualAudit();
                if (Objects.nonNull(inviteMutualAudit) && inviteMutualAudit.isOpen()) {
                    raterNodes.add(new RaterNode("peer", inviteMutualAudit.getRaters()));
                }
            }
            if (Objects.nonNull(this.s3SubRater) && this.s3SubRater.isOpen() && Objects.nonNull(this.s3SubRater.getAppointer())) {
                InviteMutualAudit inviteMutualAudit = this.s3SubRater.getAppointer().getInviteMutualAudit();
                if (Objects.nonNull(inviteMutualAudit) && inviteMutualAudit.isOpen()) {
                    raterNodes.add(new RaterNode("sub", inviteMutualAudit.getRaters()));
                }
            }
            return raterNodes;
        }
        for (EmpEvalKpiType kpiType : kpiTypes.getDatas()) {
            if (kpiType.isAskType()) {
                if (Objects.nonNull(kpiType.getPeerRater()) && kpiType.getPeerRater().isOpen() && Objects.nonNull(kpiType.getPeerRater().getAppointer())) {
                    InviteMutualAudit inviteMutualAudit = kpiType.getPeerRater().getAppointer().getInviteMutualAudit();
                    if (Objects.nonNull(inviteMutualAudit) && inviteMutualAudit.isOpen()) {
                        raterNodes.add(new RaterNode("peer", inviteMutualAudit.getRaters()));
                    }
                }
                if (Objects.nonNull(kpiType.getSubRater()) && kpiType.getSubRater().isOpen() && Objects.nonNull(kpiType.getSubRater().getAppointer())) {
                    InviteMutualAudit inviteMutualAudit = kpiType.getSubRater().getAppointer().getInviteMutualAudit();
                    if (Objects.nonNull(inviteMutualAudit) && inviteMutualAudit.isOpen()) {
                        raterNodes.add(new RaterNode("sub", inviteMutualAudit.getRaters()));
                    }
                }
            }
            for (EvalKpi item : kpiType.getItems()) {
                EvalItemScoreRule itemScoreRule = item.getItemScoreRule();
                if (Objects.isNull(itemScoreRule)) {
                    continue;
                }
                if (Objects.nonNull(itemScoreRule.getPeerRater()) && itemScoreRule.getPeerRater().isOpen() && Objects.nonNull(itemScoreRule.getPeerRater().getAppointer())) {
                    InviteMutualAudit inviteMutualAudit = itemScoreRule.getPeerRater().getAppointer().getInviteMutualAudit();
                    if (Objects.nonNull(inviteMutualAudit) && inviteMutualAudit.isOpen()) {
                        raterNodes.add(new RaterNode("peer", inviteMutualAudit.getRaters()));
                    }
                }
                if (Objects.nonNull(itemScoreRule.getSubRater()) && itemScoreRule.getSubRater().isOpen() && Objects.nonNull(itemScoreRule.getSubRater().getAppointer())) {
                    InviteMutualAudit inviteMutualAudit = itemScoreRule.getSubRater().getAppointer().getInviteMutualAudit();
                    if (Objects.nonNull(inviteMutualAudit) && inviteMutualAudit.isOpen()) {
                        raterNodes.add(new RaterNode("sub", inviteMutualAudit.getRaters()));
                    }
                }
            }
        }
        return raterNodes;
    }

    //获取邀请互评人
    public List<RaterNode> getInviteMutualRater(String evalEmpId) {
        List<RaterNode> raterNodes = new ArrayList<>();
        if (!this.isCustom()) {
            addRatersIfOpen(raterNodes, "peer", this.s3PeerRater.getAppointer(), evalEmpId, this::isOpenPeerAppointerRater);
            addRatersIfOpen(raterNodes, "sub", this.s3SubRater.getAppointer(), evalEmpId, this::isOpenSubAppointerRater);
            return raterNodes;
        }
        for (EmpEvalKpiType kpiType : kpiTypes.getDatas()) {
            if (kpiType.isAskType()) {
                addRatersIfOpen(raterNodes, "peer", kpiType.getPeerRater().getAppointer(), evalEmpId, kpiType::isOpenPeerAppointerRater);
                addRatersIfOpen(raterNodes, "sub", kpiType.getSubRater().getAppointer(), evalEmpId, kpiType::isOpenSubAppointerRater);
            }
            for (EvalKpi item : kpiType.getItems()) {
                EvalItemScoreRule itemScoreRule = item.getItemScoreRule();
                if (Objects.isNull(itemScoreRule)) {
                    continue;
                }
                addRatersIfOpen(raterNodes, "peer", itemScoreRule.getPeerRater().getAppointer(), evalEmpId, itemScoreRule::isOpenPeerAppointerRater);
                addRatersIfOpen(raterNodes, "sub", itemScoreRule.getSubRater().getAppointer(), evalEmpId, itemScoreRule::isOpenSubAppointerRater);
            }
        }
        return raterNodes;
    }

    private void addRatersIfOpen(List<RaterNode> raterNodes, String type, WaitAppoint appointer, String evalEmpId, BooleanSupplier isOpen) {
        if (appointer != null && isOpen.getAsBoolean()) {
            addRaters(raterNodes, type, appointer, evalEmpId);
        }
    }

    private void addRaters(List<RaterNode> raterNodes, String type, WaitAppoint appointer, String evalEmpId) {
        raterNodes.add(new RaterNode(type, appointer.appointRaters(evalEmpId)));
        InviteMutualAudit inviteMutualAudit = appointer.getInviteMutualAudit();
        if (Objects.nonNull(inviteMutualAudit) && inviteMutualAudit.isOpen()) {
            raterNodes.add(new RaterNode(type + "Audit", inviteMutualAudit.getRaters(), inviteMutualAudit.getMultiType()));
        }
    }

    public boolean isOpenPeerAppointerRater() {
        return Objects.nonNull(this.s3PeerRater) && s3PeerRater.isOpen() && Objects.nonNull(s3PeerRater.getAppointer());
    }

    public boolean isOpenSubAppointerRater() {
        return Objects.nonNull(s3SubRater) && s3SubRater.isOpen() && Objects.nonNull(s3SubRater.getAppointer());
    }
@JSONField(serialize = false)
    public Map<String, List<EvalScoreResult>> getScoreNodesOld() {
        if (CollUtil.isEmpty(this.kpiTypes.getDatas())) {
            return null;
        }
        List<EvalScoreResult> scoreResults = new ArrayList<>();
        for (EmpEvalKpiType data : this.kpiTypes.getDatas()) {
            List<EvalScoreResult> list = data.getItems().stream().filter(i -> !CollUtil.isEmpty(i.getWaitScoresOld())).flatMap(i -> i.getWaitScoresOld().stream()).collect(Collectors.toList());
            scoreResults.addAll(list);
        }
        return new ListWrap<>(scoreResults).groupBy(rs -> rs.getScorerType() + "-" + rs.getKpiItemId()).getGroups();
    }

    public Map<String, List<EvalScorerNodeKpiItem>> getScoreNodes() {
        if (CollUtil.isEmpty(this.kpiTypes.getDatas())) {
            return null;
        }
        List<EvalScorerNodeKpiItem> scoreResults = new ArrayList<>();
        for (EmpEvalKpiType data : this.kpiTypes.getDatas()) {
            List<EvalScorerNodeKpiItem> list = data.getItems().stream().filter(i -> !CollUtil.isEmpty(i.getWaitScores())).flatMap(i -> i.getWaitScores().stream()).collect(Collectors.toList());
            scoreResults.addAll(list);
        }
        return new ListWrap<>(scoreResults).groupBy(rs -> rs.getScorerType() + "-" + rs.getKpiItemId()).getGroups();
    }

    // 1:指标确认 2：完成值录入 4：指标变更和审核  8：完成值审核  16：评分 32：校准 64：面谈 128：公示 256：结果申诉
    public void changeEvalRule(EmpEvalChangeResult result, Integer changeStage) {
        if ((changeStage & 1) > 0 && this.confirmTask.isOpen()) {
            this.confirmTask = result.getConfirmTask();
            result.addVacancy(TalentStatus.CONFIRMING.getStatus());
        }
        //完成值录入
        if ((changeStage & 2) > 0) {
            this.changeInput(result.getKpiTypes());
            result.addVacancy("inputFinishValue");
        }
        if ((changeStage & 4) > 0 && this.editExeIndi.isOpen()) {
            this.editExeIndi = result.getEditExeIndi();
            result.addVacancy(TalentStatus.CHANGING.getStatus());
        }
        //完成值审核
        if ((changeStage & 8) > 0 && this.finishValueAudit.isOpen()) {
            this.finishValueAudit = result.getFinishValueAudit();
        }
        changeInputAudit(result.getKpiTypes());
        result.addVacancy(TalentStatus.FINISH_VALUE_AUDIT.getStatus());
        //评分
        if ((changeStage & 16) > 0) {
            this.changeScore(result);
            this.initIndexRaters();
            result.addVacancy(TalentStatus.SCORING.getStatus());
        }
        //校准
        if ((changeStage & 32) > 0 && this.auditResult.isOpen()) {
            this.auditResult = result.getAuditResult();
            result.addVacancy(TalentStatus.RESULTS_AUDITING.getStatus());
        }
        //面谈
        if ((changeStage & 64) > 0 && this.interviewConf.isOpen()) {
            this.interviewConf = result.getInterviewConf();
            result.addVacancy(TalentStatus.RESULTS_INTERVIEW.getStatus());
        }
        if ((changeStage & 128) > 0 && this.publishResult.isOpen()) {
            this.publishResult = result.getPublishResult();
            result.addVacancy(TalentStatus.WAIT_PUBLISHED.getStatus());
        }
        if ((changeStage & 256) > 0 && this.appealConf.isOpen()) {
            this.appealConf = result.getAppealConf();
            result.addVacancy(TalentStatus.RESULTS_APPEAL.getStatus());
        }
        this.isChanged = true;
    }

    private void changeInput(KpiListWrap kpiTypes) {
        if (Objects.isNull(kpiTypes)) {
            return;
        }
        ListWrap<EvalKpi> itemWrap = new ListWrap<>(kpiTypes.getDatas().stream().flatMap(type -> type.getItems().stream()).collect(Collectors.toList())).asMap(EvalKpi::getKpiItemId);
        for (EmpEvalKpiType data : this.kpiTypes.getDatas()) {
            data.changeItemInput(itemWrap);
        }
    }

    private void changeInputAudit(KpiListWrap kpiTypes) {
        if (Objects.isNull(kpiTypes)) {
            return;
        }
        if (CollUtil.isEmpty(this.kpiTypes.getDatas())) {
            return;
        }
        ListWrap<EmpEvalKpiType> typeWrap = new ListWrap<>(kpiTypes.getDatas()).asMap(EmpEvalKpiType::getKpiTypeId);
        for (EmpEvalKpiType data : this.kpiTypes.getDatas()) {
            data.changeTypeInputAudit(typeWrap);
        }
    }

    private void changeScore(EmpEvalChangeResult result) {

        this.s3PeerRater = result.getS3PeerRater();
        this.s3SubRater = result.getS3SubRater();
        this.s3SuperRater = result.getS3SuperRater();
        this.s3AppointRater = result.getS3AppointRater();
        this.totalLevelRaters = result.getTotalLevelRaters();

        if (!this.isCustom()) {
            this.kpiTypes.getDatas().forEach(EmpEvalKpiType::closeRater);
            return;
        }
        if (CollUtil.isEmpty(this.kpiTypes.getDatas())) {
            return;
        }
        ListWrap<EmpEvalKpiType> typeWrap = new ListWrap<>(result.getKpiTypes().getDatas()).asMap(EmpEvalKpiType::getKpiTypeId);
        for (EmpEvalKpiType data : this.kpiTypes.getDatas()) {
            data.changeTypeScore(typeWrap.mapGet(data.getKpiTypeId()));
        }
    }

    public void accRater(RaterNodeConf s3SelfRater, MutualNodeConf s3PeerRater,
                         MutualNodeConf s3SubRater, S3SuperRaterConf s3SuperRater, S3RaterBaseConf s3AppointRater) {
        this.setS3SelfRater(s3SelfRater);
        this.setS3PeerRater(s3PeerRater);
        this.setS3SubRater(s3SubRater);
        this.setS3SuperRater(s3SuperRater);
        this.setS3AppointRater(s3AppointRater);
        if (!isCustom()) {
            this.getKpiTypes().getDatas().forEach(type -> {
                type.closeRater();
            });
        }
    }
}
