//package com.polaris.kpi.eval.domain.confirm.entity;
//
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.util.StrUtil;
//import com.polaris.kpi.eval.domain.stage.entity.AuditNodesConf;
//
//import java.util.List;
//import java.util.Optional;
//import java.util.stream.Collectors;
//
//public class ConfirmAuditNodesConf extends AuditNodesConf<ConfirmNode> {
//
//    public String modifyFlagName() {
//        if (auditNodes == null || CollUtil.isEmpty(auditNodes)) {
//            return null;
//        }
//        List<String> flowNames = auditNodes.stream().map(node -> node.modifyFlagName())
//                .collect(Collectors.toList());
//        return CollUtil.join(CollUtil.filterNew(flowNames, name -> StrUtil.isNotBlank(name)), "->");
//    }
//
//    public String confirmAuditSignName() {
//        if (auditNodes == null || CollUtil.isEmpty(auditNodes)) {
//            return null;
//        }
//        List<String> flowNames = auditNodes.stream().map(node -> node.confirmAuditSignName())
//                .collect(Collectors.toList());
//        return CollUtil.join(CollUtil.filterNew(flowNames, name -> StrUtil.isNotBlank(name)), "->");
//    }
//
//    public String multiTypeName() {
//        if (auditNodes == null || CollUtil.isEmpty(auditNodes)) {
//            return null;
//        }
//        ConfirmNode confirmNode = auditNodes.get(0);
//        return confirmNode.multiTypeName();
//    }
//
//    public String transferFlagName() {
//        if (auditNodes == null || CollUtil.isEmpty(auditNodes)) {
//            return null;
//        }
//        return auditNodes.get(0).transferFlagName();
//    }
//
//    public boolean openModify(Integer approvalOrder) {
//        if (!isOpen() || CollUtil.isEmpty(getAuditNodes())) {
//            return false;
//        }
//        Optional<ConfirmNode> first = getAuditNodes().stream().filter(a -> a.getApprovalOrder().equals(approvalOrder)).findFirst();
//        if (!first.isPresent() || first.get().getModifyFlag() == null) {
//            return false;
//        }
//        return Boolean.valueOf(first.get().getModifyFlag());
//    }
//}
