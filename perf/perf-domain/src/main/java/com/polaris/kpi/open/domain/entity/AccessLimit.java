package com.polaris.kpi.open.domain.entity;

import cn.hutool.core.lang.Assert;
import com.polaris.kpi.common.Domain;
import lombok.Getter;

import java.beans.ConstructorProperties;
import java.util.concurrent.atomic.AtomicInteger;

//访问限制策略
@Getter
public class AccessLimit extends Domain {
    protected String key;
    protected Long maxCount;
    protected Long accessCount;
    protected AtomicInteger cacheCount;

    @ConstructorProperties({ "key", "maxCount", "accessCount" })
    public AccessLimit(String key, Long maxCount, Long accessCount) {
        Assert.notBlank(key, "access_limit_key_is_blank");
        //this.key = formatHourKey(key);
        this.key = key;
        this.maxCount = maxCount;
        this.accessCount = accessCount;
        this.cacheCount = new AtomicInteger(1);
    }

    public synchronized boolean increment() {
        int cacheCount = this.cacheCount.incrementAndGet();
        return accessCount + cacheCount < maxCount;
    }

    public void allowAccess() {
        Assert.isTrue(accessCount < maxCount, "50009: access limit");
    }

    public boolean needUpdate() {
        return this.cacheCount.intValue() > 100;
    }

    public void clearCacheCount() {
        this.accessCount = this.accessCount + this.cacheCount.get();
        this.cacheCount.set(0);
    }

    public int getCacheCount() {
        return this.cacheCount.get();
    }
}
