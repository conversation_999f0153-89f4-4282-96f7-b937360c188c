package com.polaris.kpi.eval.domain.stage.event;

import com.polaris.kpi.eval.domain.TaskEvalContext;
import com.polaris.kpi.org.domain.common.BaseEvent;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.Setter;
import org.apache.poi.ss.formula.functions.T;

/**
 * batch考核进入确认阶段
 *
 * <AUTHOR> lufei
 * @date 2025/7/23 4:55 下午
 */
@Getter
public class BatchWaitPublishedStageStart extends BaseEvent {
    @Getter
    private TaskEvalContext evalContext;
    @Setter
    private EmpId opEmpId;

    public BatchWaitPublishedStageStart(TenantId tenantId, EmpId opEmpId, TaskEvalContext evalContext) {
        super(tenantId.getId());
        this.evalContext = evalContext;
        this.opEmpId = opEmpId;
    }

    public void start() {
    }
}
