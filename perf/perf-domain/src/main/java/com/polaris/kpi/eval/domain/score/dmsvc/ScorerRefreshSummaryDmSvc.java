package com.polaris.kpi.eval.domain.score.dmsvc;

import cn.com.polaris.kpi.company.MsgSceneEnum;
import cn.com.polaris.kpi.eval.Name;
import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import com.polaris.kpi.eval.domain.task.acl.ScoreMessageSender;
import com.polaris.kpi.eval.domain.task.dmsvc.ScorerTodoDmSvc;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.empeval.ChainDispatchRs;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.entity.msg.MsgTodoAggregate;
import com.polaris.kpi.eval.domain.task.event.talent.ScoreSummaryMsgTodoEvent;
import com.polaris.kpi.eval.domain.task.repo.EmpEvalRuleRepo;
import com.polaris.kpi.eval.domain.task.repo.ScorerSummaryTodoRepo;
import com.polaris.kpi.org.domain.dept.entity.CompanyMsgCenter;
import com.polaris.kpi.org.domain.dept.repo.MsgCenterRepo;
import com.polaris.kpi.setting.domain.entity.ScorerTodoSummary;
import com.polaris.kpi.setting.domain.entity.TaskUserScorer;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class ScorerRefreshSummaryDmSvc {
    private ScorerSummaryTodoRepo summaryTodoRepo;
    private List<ScoreSummaryMsgTodoEvent> todoEvents = new ArrayList<>();

    public ScorerRefreshSummaryDmSvc(ScorerSummaryTodoRepo scorerSummaryTodoRepo) {
        this.summaryTodoRepo = scorerSummaryTodoRepo;
    }

    public void refresh(EmpEvalRuleRepo empRuleRepo, EvalUser user) {
        //环节同时评，并且上级同时评时，并且开启了汇总发送待办，处于考核发起后阶段编辑才预派发
        EmpEvalMerge evalMerge = empRuleRepo.getEmpEvalMerge(user.getCompanyId(), user.getId(), EmpEvalMerge.all);
        user.setTaskName(evalMerge.getTaskName());
        ScorerTodoDmSvc todoDmSvc = new ScorerTodoDmSvc(evalMerge, user);
        //先移除，再添加，对受影响的评分人重新汇总
        Set<String> scorerIds = summaryTodoRepo.removeScorerByTaskUser(user.getCompanyId().getId(), user.getId());
        if (todoDmSvc.support()) {
            ChainDispatchRs chainDispatchRs = todoDmSvc.preDispatch(user);
            scorerIds.addAll(summaryTodoRepo.saveScorerInstance(chainDispatchRs, user, evalMerge));
            todoDmSvc.setRepo(summaryTodoRepo);
            todoDmSvc.refreshSummary(scorerIds);
            List<ScorerTodoSummary> addSummaries = todoDmSvc.getAddSummaries();
            List<ScorerTodoSummary> updateSummaries = todoDmSvc.getUpdateSummaries();
            if (!addSummaries.isEmpty()) {
                summaryTodoRepo.batchAddSummaries(addSummaries);
                for (ScorerTodoSummary addSummary : addSummaries) {
                    if (addSummary.readyToSend()) {
                        List<TaskUserScorer> scorers = summaryTodoRepo.queryNeedSendSystemTodoScorers(addSummary.getCompanyId(), addSummary.getTaskId(), addSummary.getScorerId());
                        todoEvents.add(new ScoreSummaryMsgTodoEvent(addSummary.getCompanyId(), scorers, addSummary));
                    }
                }
            }
            if (!updateSummaries.isEmpty()) {
                summaryTodoRepo.batchUpdateSummaries(updateSummaries);
                for (ScorerTodoSummary updateSummary : updateSummaries) {
                    if (updateSummary.readyToSend()) {
                        List<TaskUserScorer> scorers = summaryTodoRepo.queryNeedSendSystemTodoScorers(updateSummary.getCompanyId(), updateSummary.getTaskId(), updateSummary.getScorerId());
                        todoEvents.add(new ScoreSummaryMsgTodoEvent(updateSummary.getCompanyId(), scorers, updateSummary));
                    }
                }
            }
        }
    }

    // 还需要进一步确认 summary 是否可以合并,内部就可支掉for处理 @Hehu
    public void startSendScoreSummaryMsgTodo(ScoreMessageSender messageSender, MsgCenterRepo companyMsgCenterRepo) {
        if (todoEvents.isEmpty()) {
            return;
        }
        for (ScoreSummaryMsgTodoEvent event : todoEvents) {
            String tenantId = event.getTenantId();
            List<TaskUserScorer> scorers = event.getScorers();
            ScorerTodoSummary summary = event.getSummary();

            //根据汇总内容发钉钉待办和通知
            if (summary.getTodoStatus() == 0) {
                //进行锁定
                Boolean lock = summaryTodoRepo.lockSummary(summary);
                if (!lock) {
                    return;
                }
                //成功则发待办
                String thirdMsgId = messageSender.scoreSummarySendTodo(summary, scorers);
                summary.setThirdMsgId(thirdMsgId);
                summary.setTodoStatus(1);
                summaryTodoRepo.addOrUpdateAfterSendRemoteTodo(summary);
            }

            //给所有没有系统待办的人发系统待办
            List<CompanyMsgCenter> msgCenters = scorers.stream().map(scorer -> {
                MsgSceneEnum toScene = SubScoreNodeEnum.todoScene(scorer.getScoreScene());
                CompanyMsgCenter center = new CompanyMsgCenter(new TenantId(tenantId), toScene,
                        scorer.getTaskUserId(), scorer.getScorerId());
                //获取URL
                MsgTodoAggregate msgTodoAggregate = new MsgTodoAggregate(new TenantId(scorer.getCompanyId()), scorer.getTaskId(), new Name(scorer.getTaskName()),
                        scorer.getEmpId(), scorer.getTaskUserId())
                        .useScene(toScene);
                msgTodoAggregate.build();
                String url = msgTodoAggregate.redirectUrl();
                center.attTask(url, scorer.getTaskId(), scorer.getTaskName(), null);
                return center;
            }).collect(Collectors.toList());
            companyMsgCenterRepo.saveBatch(msgCenters);
            //更新这部分发了系统待办的实例状态
            summaryTodoRepo.sendScorerCompanyMsg(scorers, summary);
        }
    }
}
