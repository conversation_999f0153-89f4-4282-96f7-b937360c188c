package com.polaris.kpi.eval.domain.confirm.repo;

import com.polaris.kpi.eval.domain.confirm.entity.ConfirmTaskFlow;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.AffirmTaskConf;
import com.polaris.kpi.eval.domain.task.entity.flow.LevelAuditFlow;
import com.polaris.sdk.type.MapWrap;
import com.polaris.sdk.type.TenantId;

import java.util.Collection;

//@FunctionalInterface
public interface BatchLoadConfirmFlowRepo {
    MapWrap<String, ConfirmTaskFlow> listConfirmAuditFlow(TenantId companyId, MapWrap<String, AffirmTaskConf> confs);

    MapWrap<String, LevelAuditFlow> listConfirmAuditFlowOld(TenantId companyId, Collection<String> taskUserIds);

}
