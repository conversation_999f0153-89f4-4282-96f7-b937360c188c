package com.polaris.kpi.eval.domain.task.acl;

import cn.com.polaris.kpi.eval.ScoreEmp;
import com.polaris.kpi.eval.domain.task.dmsvc.ScorerTodoDmSvc;
import com.polaris.kpi.eval.domain.task.entity.EvalOnScoreStage;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.empeval.ChainDispatchRs;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.entity.empeval.score.TransferScorer;
import com.polaris.kpi.org.domain.dept.entity.CompanyMsgCenter;
import com.polaris.kpi.setting.domain.entity.ScorerTodoSummary;
import com.polaris.sdk.type.TenantId;

import java.util.List;
import java.util.Set;


public interface ScoreMsgAcl {
    void clearScoreMsgTodo(TenantId companyId, EvalOnScoreStage scoreStage, boolean isCancelAll);

    void clearTsScoreMsgTodo(TenantId companyId, List<CompanyMsgCenter> cancelCenters);

    void clearScoreSummariesMsgTodo(TenantId companyId, List<ScorerTodoSummary> scorerTodoSummaries);

    void sendTsScoreMsg(TenantId companyId, String toEmpId, Set<String> msgScenes, List<CompanyMsgCenter> toMsgCenters,
                        EvalUser evalUser, EmpEvalMerge evalRule);

    void sendScoreMsg(TenantId companyId, Set<String> toEmpIds, String msgScene,EvalUser evalUser, EmpEvalMerge evalRule);

    void sendResetScoreMsg(TenantId companyId, List<ScoreEmp> scoreEmps, String resetReason, boolean isRejectToScore, ScorerTodoDmSvc dmSvc);

    List<String> sendScoreMsgForDispatched(ChainDispatchRs dispatchRs, EmpEvalMerge eval, EvalUser taskUser, List<String> wasEvalScorerIds);

    void sendSummaryScoreMsgForDispatched(EmpEvalMerge eval, EvalUser taskUser);
}
