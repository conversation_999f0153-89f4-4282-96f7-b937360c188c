package com.polaris.kpi.eval.domain.task.dmsvc;

import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.event.ThisStageEnded;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Getter
public class AllScorePassedDmSvc {

    private final EmpEvalMerge eval;
    private final EvalUser taskUser;
    private final Boolean isEndStage;
    private final Boolean isNeedInputForAutoItem;
    private final String tenantId;

    public AllScorePassedDmSvc(EmpEvalMerge eval, EvalUser taskUser, Boolean isEndStage, String tenantId, Boolean isNeedInputForAutoItem) {
        this.eval = eval;
        this.taskUser = taskUser;
        this.isEndStage = isEndStage;
        this.tenantId = tenantId;
        this.isNeedInputForAutoItem = isNeedInputForAutoItem;
    }

    public void reComputeFinalScore() {
        log.info("流程完成,直接结束评分阶段");
        eval.recomputeUseDelMinMaxOpt();//互评去高去低计算，之后再给评价的指标重新计算
        //   userRepo.batchUpdateScoreResult(evalScoreResults); save scorer node
        SumScorerComputeDmSvc dmSvc = new SumScorerComputeDmSvc(taskUser, eval);
        dmSvc.computeSumScore();//计算总评分，环节分
        taskUser.setAllScored(Boolean.TRUE.toString());
        taskUser.setFinalScore(dmSvc.getFinalScore());
        taskUser.setOriginalFinalScore(dmSvc.getFinalScore());
    }

    public void flowEnd() {
        if (eval.inputOnScoring() && isNeedInputForAutoItem) {
            return;
        }
        if (this.isEndStage) {
            new ThisStageEnded(eval, taskUser, TalentStatus.SCORING).publish();
        }
    }
}
