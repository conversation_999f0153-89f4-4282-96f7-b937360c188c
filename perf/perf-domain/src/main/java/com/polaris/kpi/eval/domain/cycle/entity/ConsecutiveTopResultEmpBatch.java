package com.polaris.kpi.eval.domain.cycle.entity;

import com.polaris.kpi.eval.domain.cycle.entity.TopResultOfCycle;
import com.polaris.kpi.eval.domain.cycle.type.StatisticRuleItem;
import com.polaris.sdk.type.TenantId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.ibatis.annotations.JsonAryColumn;
import org.apache.ibatis.annotations.JsonColumn;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/8 16:20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class ConsecutiveTopResultEmpBatch {
    private TenantId companyId;

    /**
     * 当前周期id
     */
    private String cycleId;

    /**
     * 绩效结果通知配置id
     */
    private String ruleConfigId;

    /**
     * 绩效结果通知规则名称
     */
    private String ruleName;

    private List<String> topResultEmpIdList;

    private List<TopResultOfCycle> historyTopResults;


    /**
     * 规则应用类型：1个人绩效类型 2组织绩效类型
     */
    private Integer ruleApplyType;

    /**
     * 规则类型（1持续绩优，2持续绩差）
     */
    private Integer ruleType;

    /**
     * 统计时长
     */
    private Integer statisticDuration;

    /**
     * 统计考核周期类型
     */
    private String cycleType;

    /**
     * 统计条件JSON对象[{"statisticType":"等级/总分",“statisticRuleItems”:[{"ruleId":"对象id","ruleName":"对象名称","statisticOperator":">"}]}]
     */
    @JsonAryColumn(StatisticRuleItem.class)
    private List<StatisticRuleItem> statisticRule;

    /**
     * 统计规则配置详情
     */
    @JsonColumn
    private PerfStatisticRule statisticRuleDetail;

}
