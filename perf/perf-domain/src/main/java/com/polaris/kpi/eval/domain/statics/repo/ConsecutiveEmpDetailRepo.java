package com.polaris.kpi.eval.domain.statics.repo;

import com.polaris.kpi.eval.domain.statics.entity.ConsecutiveEmpDetail;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/23 22:35
 */
public interface ConsecutiveEmpDetailRepo {

    void deleteBatch(String companyId, String cycleId, Integer ruleType, Integer performanceType);

    void addBatch(List<ConsecutiveEmpDetail> details);

    void deleteConsecutiveEmp(String companyId, String detailId);

}
