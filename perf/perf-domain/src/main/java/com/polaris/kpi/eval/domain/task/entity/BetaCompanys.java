package com.polaris.kpi.eval.domain.task.entity;

import java.util.Arrays;
import java.util.List;

public class BetaCompanys {
    public static List<String> closeCheckManagers = Arrays.asList("1226155",
            "ece4e403-43aa-47f2-bb19-a0dd18b8e98d",//沃克
            "9854cea0-d35b-4334-8c61-64a9060e9429"//沃克
    );//关闭排除人员公司
    public static List<String> removeCheckSelf = Arrays.asList("1226155","1002604",//睿人事:东娱传媒
        //  "ece4e403-43aa-47f2-bb19-a0dd18b8e98d",//沃克
            "9854cea0-d35b-4334-8c61-64a9060e9429"
    );//关闭排除人员公司

}
