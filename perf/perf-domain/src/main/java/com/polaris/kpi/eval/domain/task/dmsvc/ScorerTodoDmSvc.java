package com.polaris.kpi.eval.domain.task.dmsvc;

import cn.com.polaris.kpi.company.MsgSceneEnum;
import cn.com.polaris.kpi.eval.ScoreEmp;
import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.collection.CollUtil;
import com.polaris.acl.msg.domain.FinishWorkReq;
import com.polaris.kpi.eval.domain.task.acl.ScoreMsgAcl;
import com.polaris.kpi.eval.domain.task.entity.EmpEvalScorerNode;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.empeval.ChainDispatchRs;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.entity.empeval.score.SkipScorer;
import com.polaris.kpi.eval.domain.task.entity.empeval.score.TransferScorer;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.chain.ChainNode;
import com.polaris.kpi.eval.domain.task.entity.msg.MsgTodoAggregate;
import com.polaris.kpi.eval.domain.task.event.talent.ScoreSummaryEvent;
import com.polaris.kpi.eval.domain.task.event.talent.ScoreSummaryMsgTodoEvent;
import com.polaris.kpi.eval.domain.task.repo.ScorerSummaryTodoRepo;
import com.polaris.kpi.org.domain.dept.entity.CompanyMsgCenter;
import com.polaris.kpi.org.domain.dept.repo.MsgCenterRepo;
import com.polaris.kpi.setting.domain.entity.ScorerTodoSummary;
import com.polaris.kpi.setting.domain.entity.TaskUserScorer;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/18 13:37
 * 评分人汇总待办领域服务
 */
@Slf4j
public class ScorerTodoDmSvc {
    @Getter
    private EmpEvalMerge evalMerge;
    @Getter
    private EvalUser evalUser;
    @Getter
    public List<ScorerTodoSummary> addSummaries = new ArrayList<>();
    @Getter
    public List<ScorerTodoSummary> updateSummaries = new ArrayList<>();
    private static ScorerSummaryTodoRepo scorerSummaryTodoRepo;
    @Autowired
    public void setRepo(ScorerSummaryTodoRepo scorerSummaryTodoRepo) {
        ScorerTodoDmSvc.scorerSummaryTodoRepo = scorerSummaryTodoRepo;
    }

    public ScorerTodoDmSvc(EmpEvalMerge evalMerge, EvalUser evalUser) {
        this.evalMerge = evalMerge;
        this.evalUser = evalUser;
    }

    public ScorerTodoDmSvc(EvalUser evalUser) {
        this.evalUser = evalUser;
    }

    /**
     * 判断是否满足汇总评分条件
     * @return
     */
    public Boolean support() {
        //还要求是环节依次评，并且上级同时评时
        return summarySendOpen()
                && scoreSortParallel()
                && superSupport();
    }

    /**
     * 汇总待办开启
     * @return
     */
    public Boolean summarySendOpen() {
        return Objects.nonNull(evalMerge.getScoreConf())
                && Objects.nonNull(evalMerge.getScoreConf().getSummarySendTodo())
                && evalMerge.getScoreConf().getSummarySendTodo() == 2;
    }

    /**
     * 环节评分顺序是否是并行
     * @return
     */
    public Boolean scoreSortParallel() {
        if (Objects.isNull(evalMerge.getScoreSortConf())){
            return false;
        }
        return evalMerge.getScoreSortConf().getExeType() == 1;
    }

    /**
     * 上级评是否存在以及上级评是否满足同时评
     * @return
     */
    public Boolean superSupport(){
        if (Objects.isNull(evalMerge.getS3SuperRater())){
            return true;
        }
        return "sameTime".equals(evalMerge.getS3SuperRater().getSuperiorScoreOrder());
    }

    /**
     * 预派发第一批评分人
     *
     * @param evalUser
     */
    public ChainDispatchRs preDispatch(EvalUser evalUser) {

        ChainNode current = evalMerge.current(SubScoreNodeEnum.AUTO, 1);
        ChainNode next = current.getNext();
        return evalMerge.dispatchChainNode(next, evalUser.getEmpId(), evalUser.isOpenAvgWeightCompute());
    }

    public void clearSummary(ScorerSummaryTodoRepo scorerSummaryTodoRepo, ScoreMsgAcl scoreMsgAcl, TenantId companyId, Set<String> scorerIds) {
        if (CollUtil.isEmpty(scorerIds)){
            return;
        }
        //进行汇总评分下的处理
        HashSet<String> scorers = new HashSet<>(scorerIds);
        scorerSummaryTodoRepo.updateFinishScoreStatus(companyId.getId(), evalUser.getId(), scorers);
        this.refreshSummaryV3(scorerSummaryTodoRepo, scorers);
        List<ScorerTodoSummary> updateSummaries = this.getUpdateSummaries();
        scoreMsgAcl.clearScoreSummariesMsgTodo(companyId, updateSummaries);
        scorerSummaryTodoRepo.batchUpdateSummaries(updateSummaries);
    }

    /**
     * 刷新汇总情况
     * @param scorerIds
     */
    public void refreshSummaryV3(ScorerSummaryTodoRepo scorerSummaryTodoRepo,Set<String> scorerIds) {

        if (CollUtil.isEmpty(scorerIds)){
            return;
        }

        //当前汇总情况
        List<ScorerTodoSummary>  scorerTodoSummaries = scorerSummaryTodoRepo.listCountSummary(scorerIds, evalUser.getCompanyId().getId(), evalUser.getTaskId());
        ListWrap<ScorerTodoSummary> scorerTodoSummarieMap = new ListWrap<>(scorerTodoSummaries).asMap(ScorerTodoSummary::getScorerId);

        //如果存在旧的汇总对象，并且未关闭钉钉待办，需要更新统计人数
        List<ScorerTodoSummary>  oldSummarys = scorerSummaryTodoRepo.listScorerSummary(evalUser.getCompanyId().getId(), evalUser.getTaskId(),scorerIds);
        ListWrap<ScorerTodoSummary> oldSummaryMap = new ListWrap<>(oldSummarys).asMap(ScorerTodoSummary::getScorerId);
        for (String scorerId : scorerIds) {
            //当前汇总情况
            ScorerTodoSummary  scorerTodoSummary = scorerTodoSummarieMap.mapGet(scorerId);

            //如果存在旧的汇总对象，并且未关闭钉钉待办，需要更新统计人数
            ScorerTodoSummary  oldSummary = oldSummaryMap.mapGet(scorerId);
            if (Objects.nonNull(oldSummary)) {
                scorerTodoSummary.copyFromOldOne(oldSummary);
                updateSummaries.add(scorerTodoSummary);
            } else { //有新评分人加入
                scorerTodoSummary.populateByTaskUser(evalUser);
                scorerTodoSummary.scorerId(scorerId);
                addSummaries.add(scorerTodoSummary);
            }
        }
    }


    public void clearScorerSkipTodo(String skipScorerId,List<CompanyMsgCenter> cancelCenters, ScorerSummaryTodoRepo scorerSummaryTodoRepo, ScoreMsgAcl scoreMsgAcl){
        if (this.support()) {
            //处理汇总待办的评分人跳过逻辑，移除这个人的评分人实例，刷新汇总对象
            //移除
            Set<String> scorerIds = new HashSet<>();
            scorerIds.add(skipScorerId);
            scorerSummaryTodoRepo.removeScorer(evalUser.getId(), skipScorerId);
            this.refreshSummary(scorerIds);
            List<ScorerTodoSummary> updateSummaries = this.getUpdateSummaries();
            //进行待办处理
            scoreMsgAcl.clearScoreSummariesMsgTodo(evalUser.getCompanyId(), updateSummaries);
            scorerSummaryTodoRepo.batchUpdateSummaries(updateSummaries);
        }else{
            scoreMsgAcl.clearTsScoreMsgTodo(evalUser.getCompanyId(),cancelCenters);//单个通知清除待办
        }
    }

    /**
     * 刷新汇总情况
     * @param scorerIds
     */
    public void refreshSummary(Collection<String> scorerIds) {

        if (CollUtil.isEmpty(scorerIds)){
            return;
        }
        //如果旧汇总待办已经完成了，需要准备发送新的待办
        for (String scorerId : scorerIds) {
            //当前汇总情况
            ScorerTodoSummary scorerTodoSummary = scorerSummaryTodoRepo.countSummary(scorerId, evalUser.getCompanyId().getId(), evalUser.getTaskId());
            //如果存在旧的汇总对象，并且未关闭钉钉待办，需要更新统计人数
            ScorerTodoSummary oldSummary = scorerSummaryTodoRepo.getScorerSummary(evalUser.getCompanyId().getId(), evalUser.getTaskId(), scorerId);
            if (Objects.nonNull(oldSummary)) {
                scorerTodoSummary.copyFromOldOne(oldSummary);
                updateSummaries.add(scorerTodoSummary);
            } else {
                //过滤掉已完成的，不重复新增汇总通知记录【重置个人，比如自评，会重新刷新新增】
                if (scorerTodoSummary.filterFinished()){
                    continue;
                }
                //有新评分人加入
                scorerTodoSummary.populateByTaskUser(evalUser);
                scorerTodoSummary.scorerId(scorerId);
                addSummaries.add(scorerTodoSummary);
            }
        }
    }

    /**
     * 重置时，发送通知待办
     */
    public void resetScorerSummary(String companyId,String taskUserId,ScorerSummaryTodoRepo scorerSummaryTodoRepo,List<String> scorerIds){
        if (CollUtil.isEmpty(scorerIds)){
            return;
        }
        Set<String> scorerIdsSet = new HashSet<>(scorerIds);
        scorerSummaryTodoRepo.resetScorerScoreStatus(companyId, taskUserId, scorerIdsSet);
        this.refreshSummaryV3(scorerSummaryTodoRepo,scorerIdsSet);
        List<ScorerTodoSummary> updateSummaries = this.getUpdateSummaries();
        List<ScorerTodoSummary> addSummaries = this.getAddSummaries();
        scorerSummaryTodoRepo.batchAddSummaries(addSummaries);
        scorerSummaryTodoRepo.batchUpdateSummaries(updateSummaries);
        if (!updateSummaries.isEmpty()) {
            for (ScorerTodoSummary updateSummary : updateSummaries) {
                if (updateSummary.readyToSend()) {
                    List<TaskUserScorer> scorers = scorerSummaryTodoRepo.queryNeedSendSystemTodoScorers(updateSummary.getCompanyId(), updateSummary.getTaskId(), updateSummary.getScorerId());
                    new ScoreSummaryMsgTodoEvent(updateSummary.getCompanyId(), scorers, updateSummary).fire();
                }
            }
        }
        //转交或者重置的可能存在新的汇总对象需要立马发一遍待办的情况
        if (!addSummaries.isEmpty()) {
            for (ScorerTodoSummary addSummary : addSummaries) {
                if (addSummary.readyToSend()) {
                    List<TaskUserScorer> scorers = scorerSummaryTodoRepo.queryNeedSendSystemTodoScorers(addSummary.getCompanyId(), addSummary.getTaskId(), addSummary.getScorerId());
                    new ScoreSummaryMsgTodoEvent(addSummary.getCompanyId(), scorers, addSummary).fire();
                }
            }
        }
    }

    public void sendScoreMsg(TenantId companyId,List<ScoreEmp> scoreEmps,String resetReason, boolean isRejectToScore,
                             MsgCenterRepo centerRepo, ScorerSummaryTodoRepo scorerSummaryTodoRepo, ScoreMsgAcl scoreMsgAcl) {
        //汇总评分判断
        if (!this.support()) {
            //重新开启对应系统待办
            scoreEmps.forEach(scoreEmp -> {
                MsgSceneEnum toScene = SubScoreNodeEnum.todoScene(this.evalMerge.isCustom() ? SubScoreNodeEnum.CUSTOM.getScene() : scoreEmp.getScorerType());
                centerRepo.reOpenTodoMsg(companyId, this.evalUser.getId(), scoreEmp.getEmpId(), toScene.getType());

            });
            scoreMsgAcl.sendResetScoreMsg(companyId, scoreEmps,resetReason,isRejectToScore,this);//发送远程待办通知
        } else {
            List<String> scorerIds = scoreEmps.stream().map(ScoreEmp::getEmpId).collect(Collectors.toList());
            //所有被重置的评分人，需要重置评价状态为未评分，但是还在就绪状态,同时重新对涉及到的评分人进行汇总
            this.resetScorerSummary(companyId.getId(), evalUser.getId(), scorerSummaryTodoRepo, scorerIds);
        }
    }

    public void tsScorerSummaryToDo(String toEmpId,String fromEmpId,Set<String> msgScenes, List<CompanyMsgCenter> toMsgCenters,
                                   EmpEvalScorerNode fromScoreNodeOne,ScorerSummaryTodoRepo scorerSummaryTodoRepo, ScoreMsgAcl scoreMsgAcl){
        //发送新待办、通知
        scoreMsgAcl.sendTsScoreMsg(evalUser.getCompanyId(),toEmpId, msgScenes, toMsgCenters, evalUser, evalMerge);
        //汇总待办 todo 下面这段代码 会和上面发送待办重复吗？待考察
        if (this.support()){
            Set<String> scorerIds = new HashSet<>(Arrays.asList(fromEmpId, toEmpId));
            //移除
            scorerSummaryTodoRepo.removeScorer(this.evalUser.getId(),fromEmpId);
            sendTsScoreSummaryMsg(scorerIds,toEmpId,fromScoreNodeOne.getScorerType(), fromScoreNodeOne.getApprovalOrder(),scorerSummaryTodoRepo,scoreMsgAcl);
        }
    }

    public void tsSkipScorerSummaryToDo(Set<String> scorerIds, String msgScene, ScoreMsgAcl scoreMsgAcl) {
        //发送新待办、通知
        scoreMsgAcl.sendScoreMsg(evalUser.getCompanyId(), scorerIds, msgScene, evalUser, evalMerge);
    }

    public void sendTsScoreSummaryMsg( Set<String> scorerIds,String toEmpId, String scorerType, Integer approvalOrder,
                                       ScorerSummaryTodoRepo scorerSummaryTodoRepo, ScoreMsgAcl scoreMsgAcl) {
        //汇总待办 todo 下面这段代码 会和上面发送待办重复吗？待考察
        //转给新的评分人,需要判断是否存在，如果已经存在，无须增加？ TODO
        TaskUserScorer scorer = new TaskUserScorer();
        scorer.populateTaskUserInfo(evalUser);
        scorer.populateScorerInfo(scorerType, approvalOrder, toEmpId, evalMerge);
        scorer.initTransferStatus();
        scorerSummaryTodoRepo.addScorer(scorer);
        this.refreshSummary(scorerIds);
        List<ScorerTodoSummary> updateSummaries = this.getUpdateSummaries();
        List<ScorerTodoSummary> addSummaries = this.getAddSummaries();
        scorerSummaryTodoRepo.batchAddSummaries(addSummaries);
        scoreMsgAcl.clearScoreSummariesMsgTodo(evalUser.getCompanyId(), updateSummaries);
        for (ScorerTodoSummary updateSummary : updateSummaries) {
            if (updateSummary.readyToSend()) { //发送钉钉待办，系统待办，通知
                List<TaskUserScorer> scorers = scorerSummaryTodoRepo.queryNeedSendSystemTodoScorers(updateSummary.getCompanyId(), updateSummary.getTaskId(), updateSummary.getScorerId());
                new ScoreSummaryMsgTodoEvent(updateSummary.getCompanyId(), scorers, updateSummary).publish();
            }
        }
        scorerSummaryTodoRepo.batchUpdateSummaries(updateSummaries);
        if (!addSummaries.isEmpty()) {
            for (ScorerTodoSummary addSummary : addSummaries) {
                if (addSummary.readyToSend()) { //发送钉钉待办，系统待办，通知
                    List<TaskUserScorer> scorers = scorerSummaryTodoRepo.queryNeedSendSystemTodoScorers(addSummary.getCompanyId(), addSummary.getTaskId(), addSummary.getScorerId());
                    new ScoreSummaryMsgTodoEvent(addSummary.getCompanyId(), scorers, addSummary).publish();
                }
            }
        }
    }
    public Set<String> handleTodo(List<String> wasEvalScorerIds,
                                  ChainDispatchRs typeDispatchRs,
                                  ScorerSummaryTodoRepo scorerSummaryTodoRepo,
                                  ScoreMsgAcl scoreMsgAcl, boolean isSuperScoreNode) {
        Set<String> newSet = new HashSet<>();
        // 判断当前分发的评分 在上级是否评分过
        if (!this.support() || isSuperScoreNode) {
            //如果还是按照人员发送的，按原有逻辑直接发  发送通知与待办[上级分发的走单个发送逻辑]
            log.info("【不支持汇总发送待办或者上级分发发送待办】发送待办给分发的评分人，wasEvalScorerIds：{}", wasEvalScorerIds);
            List<String> scorerIds = scoreMsgAcl.sendScoreMsgForDispatched(typeDispatchRs, evalMerge, evalUser, wasEvalScorerIds);
            newSet.addAll(scorerIds);
            log.info("【不支持汇总发送待办或者上级分发发送待办】发送待办给分发的评分人，scorerIds:{}", scorerIds);
        } else {
            Set<String> scorerIds = handleCollectToDo(scorerSummaryTodoRepo, typeDispatchRs, scoreMsgAcl);
            newSet.addAll(scorerIds);
            log.info("【汇总发送】发送待办给分发的评分人，scorerIds:{},wasEvalScorerIds：{}", scorerIds, wasEvalScorerIds);
        }
        return newSet;
    }

    private Set<String> handleCollectToDo(ScorerSummaryTodoRepo scorerSummaryTodoRepo, ChainDispatchRs typeDispatchRs, ScoreMsgAcl scoreMsgAcl) {
        //使用独立的事务让更新提交
        Set<String> scorerIds = scorerSummaryTodoRepo.dispatchScorerInstance(typeDispatchRs, evalUser, evalMerge);
        new ScoreSummaryEvent(evalMerge.getCompanyId().getId(), scorerIds, evalMerge, evalUser).publish();
        Set<String> newSet = new HashSet<>(scorerIds);
        if (hasSelfScore(typeDispatchRs)) {//如果有自评，正常发
            scoreMsgAcl.sendSummaryScoreMsgForDispatched(evalMerge, evalUser);
            newSet.add(evalUser.getEmpId());
        }
        return newSet;
    }

    private boolean hasSelfScore(ChainDispatchRs typeDispatchRs) {
        Map<String, Set<String>> stringSetMap = typeDispatchRs.distinctMsgerV3();
        //只要存在自评的key，就认为有自评
        return stringSetMap.keySet().stream().anyMatch(key -> SubScoreNodeEnum.SELF_SCORE.getScene().equals(key));
    }

}
