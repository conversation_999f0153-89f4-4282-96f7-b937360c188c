package com.polaris.kpi.eval.domain;

import cn.hutool.core.collection.CollUtil;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEval;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.org.domain.dept.entity.CompanyConf;
import com.polaris.sdk.type.ListWrap;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;


/**
 * 任务级考核上下文
 */
@Getter
public class TaskEvalContext {
    private CompanyConf conf;
    private AdminTask task;
    // private ListWrap<EvalUser> evals;
    private List<EmpEval> evals;

    private ListWrap<EmpEval> evalMap;
    private int stageOrder;//正在执行中的阶段序号
    private boolean shouldContinue = true;

    public EvalUser getFirstUser() {
        if (CollUtil.isEmpty(evals)) {
            return null;
        }
        return evals.get(0).getEval();
    }

    public EmpEvalMerge getFirstRule() {
        if (CollUtil.isEmpty(evals)) {
            return null;
        }
        return evals.get(0).getRule();
    }

    public void initStageOrder(int stageOrder) {
        this.stageOrder = stageOrder;
    }

    public void stopExecution() {
        this.shouldContinue = false;
    }

    public boolean shouldContinue() {
        return shouldContinue;
    }

    public TaskEvalContext(AdminTask task, CompanyConf conf, List<EmpEval> evalMerges) {
        this.task = task;
        this.conf = conf;
        this.evals = evalMerges;
        this.evalMap = new ListWrap<>(evalMerges).asMap(eval -> eval.getEval().getId());
    }

    public Collection<String> evalIds() {
        return CollUtil.map(evals, eval -> eval.getEval().getId(), true);
    }

    public TaskEvalContext clone() {
        TaskEvalContext context = new TaskEvalContext(task, conf, new ArrayList<>());
        return context;
    }

    public void addMember(EmpEval eval) {
        this.evals.add(eval);
    }

    public EmpEval getEval(String taskUserId) {
        return this.evalMap.mapGet(taskUserId);
    }

}
