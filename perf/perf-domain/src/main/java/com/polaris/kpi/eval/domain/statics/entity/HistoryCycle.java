package com.polaris.kpi.eval.domain.statics.entity;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/19 11:26
 */
@Data
@AllArgsConstructor
public class HistoryCycle {

    private Integer sort;
    private String cycleTime;
    private List<HitTask> hitTasks;

    public HistoryCycle(Integer sort, String cycleTime, List<HitTask> hitTasks) {
        this.sort = sort;
        this.cycleTime = cycleTime;
        this.hitTasks = hitTasks;
    }

    /**
     * 周期开始时间
     */
    private String cycleStart;

    /**
     * 周期开始时间
     */
    private String cycleEnd;

    /**
     * 周期 年份
     */
    private String year;

    /**
     * 周期类型对应的value
     */
    private String value;





}
