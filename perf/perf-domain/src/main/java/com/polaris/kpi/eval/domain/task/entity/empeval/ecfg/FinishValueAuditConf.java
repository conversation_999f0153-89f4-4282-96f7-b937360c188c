package com.polaris.kpi.eval.domain.task.entity.empeval.ecfg;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.perf.www.common.constant.BusinessConstant;
import com.polaris.kpi.eval.domain.task.entity.EvalAudit;
import com.polaris.kpi.eval.domain.task.entity.admineval.EvalRuleLogField;
import com.polaris.kpi.eval.domain.task.entity.admineval.EvalRuleOpLogMeta;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

import static com.polaris.sdk.type.AuditEnum.FINISH_VALUE_AUDIT;

/**
 * 完成值审核的配置
 */
@Setter
@Getter
@NoArgsConstructor
public class FinishValueAuditConf extends BaseEvalFlowConf<BaseAuditNode> {
    private String noChangeSkipFlag;//前1人未修改考核任务,则跳过审批流程, 第一位责任人未修改考核任务，则跳过后续的确认流程

    public FinishValueAuditConf(String confirmFlag, String noChangeSkipFlag) {
        this.noChangeSkipFlag = noChangeSkipFlag;
        this.open = StrUtil.equals("true", confirmFlag) ? 1 : 0;
    }

    public FinishValueAuditConf(String confirmFlag) {
        this.open = StrUtil.equals("true", confirmFlag) ? 1 : 0;
    }


    @JSONField(serialize = false)
    private int level;//审批层. 指标为1层,任务为二层,

    public List<EvalRuleOpLogMeta> compare(FinishValueAuditConf after) {
        List<EvalRuleOpLogMeta> rs = new ArrayList<>();
        EvalRuleOpLogMeta logMeta = new EvalRuleOpLogMeta("完成值审核配置", "", TalentStatus.FINISH_VALUE_AUDIT.getStatus());
        logMeta.addField(EvalRuleLogField.createIf("完成值录入审核", 1, 0, openName(), after.openName()));
        if (after.isOpen()) {
            logMeta.addField(EvalRuleLogField.createIf("允许修改指标", 1, 4, modifyFlagName(), after.modifyFlagName()));
            logMeta.addField(EvalRuleLogField.createIf("同级审核多人时", 1, 4, multiTypeName(), after.multiTypeName()));
            logMeta.addField(EvalRuleLogField.createIfByObj("审批流程", 1, 1, abstractFlowEmp("name", "id"), after.abstractFlowEmp("name", "id"), "name", "id"));
//            logMeta.addField(EvalRuleLogField.createIf("审批流程",1,1,  abstractFlowName(), after.abstractFlowName()));
            logMeta.addField(EvalRuleLogField.createIf("前一人未修改则跳过审批", 1, 4, noChangeSkipFlagName(), after.noChangeSkipFlagName()));
            logMeta.addField(EvalRuleLogField.createIf("允许转交", 1, 4, transferFlagName(), after.transferFlagName()));
            logMeta.addField(EvalRuleLogField.createIf("人员重复跳过", 1, 4, empRepeatSkipName(), after.empRepeatSkipName()));
            logMeta.addField(EvalRuleLogField.createIf("节点人员空缺", 1, 4, nodeEmpVacancyName(), after.nodeEmpVacancyName()));
        }
        if (CollUtil.isNotEmpty(logMeta.getFields())) {
            rs.add(logMeta);
        }
        return rs;
    }

    private String noChangeSkipFlagName() {
        return Boolean.valueOf(noChangeSkipFlag) ? "开启" : "关闭";
    }

    public Boolean noChangeSkip() {
        return Boolean.valueOf(noChangeSkipFlag);
    }


    @Override
    public FinishValueAuditConf clone() {
        return JSONUtil.toBean(JSONUtil.toJsonStr(this), FinishValueAuditConf.class);
    }

    public List<EvalAudit> createTaskFinishAudits(TenantId companyId, String taskId, String empId, String taskUserId, int order,String createUser) {
        if (!this.isOpen()) {
            return new ArrayList<>();
        }
        List<EvalAudit> audits = new ArrayList<>();
        this.getAuditNodes().stream().forEach(node -> {
            node.getRaters().stream().forEach(rater -> {
                EvalAudit audit = new EvalAudit();
                //audit.setId(auditIdGen.get());
                audit.setCompanyId(companyId);
                audit.setTaskId(taskId);
                audit.setEmpId(empId);
                audit.setScene(FINISH_VALUE_AUDIT.getScene());
                audit.setApprovalOrder(order);
                audit.setApproverType("user");
                audit.setApproverInfo(rater.getEmpId());
                audit.setVacancyApproverType(BusinessConstant.VACANCY_APPROVER_TYPE_ADMIN);
                audit.setMultipleReviewersType(this.isOpen() ? this.getAuditNodes().get(0).getMultiType() : "and");
                audit.setTransferFlag(this.isOpen() ? this.getAuditNodes().get(0).getTransferFlag() : "false");
                audit.setVacancyApproverInfo(node.getVacancyApproverInfo());
                audit.setCreatedUser(createUser);
                audit.setUpdatedUser(createUser);
                audit.setTaskUserId(taskUserId);
                audits.add(audit);
            });
        });
        return audits;
    }
}

