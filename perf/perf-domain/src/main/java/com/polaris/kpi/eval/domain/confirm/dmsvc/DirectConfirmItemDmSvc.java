package com.polaris.kpi.eval.domain.confirm.dmsvc;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.SkipEmp;
import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.com.polaris.kpi.company.MsgSceneEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.perf.www.common.em.OperationLogSceneEnum;
import com.polaris.kpi.eval.domain.task.entity.CycleEval;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.OperationLog;
import com.polaris.kpi.eval.domain.task.entity.flow.LevelAuditFlow;
import com.polaris.kpi.eval.domain.task.entity.msg.MsgTodoAggregate;
import com.polaris.kpi.eval.domain.task.event.ThisStageEnded;
import com.polaris.kpi.eval.domain.task.event.msg.CancelTodoEvent;
import com.polaris.kpi.eval.domain.task.repo.EvaluateTaskRepo;
import com.polaris.kpi.eval.domain.task.repo.TaskUserRepo;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.sdk.type.AuditEnum;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 不编辑指标,仅确认.
 */
@Slf4j
public class DirectConfirmItemDmSvc {

    protected  String taskUserId;

    protected LevelAuditFlow levelAuditFlow;
    protected TenantId companyId;
    @Getter
    protected EvalUser empEval;
    protected CycleEval cycleEval;
    protected String scene;
    @Getter
    private List<OperationLog> logs = new ArrayList<>();
    @Getter
    private boolean isEnd = false;
    @Getter
    private List<String> recEmpId = new ArrayList<>();
    public static List<String> noExtMsgTodoCompanys = Arrays.asList("298e2e6d-4afe-4522-bd13-038c2db26dba", "1025901");


    public DirectConfirmItemDmSvc() {
    }
    public DirectConfirmItemDmSvc(TenantId companyId, String taskUserId, LevelAuditFlow levelAuditFlow, EvaluateAuditSceneEnum scene) {
        this.companyId = companyId;
        this.taskUserId = taskUserId;
        this.levelAuditFlow = levelAuditFlow;
        this.scene = scene.getScene();
    }

    public void initEvalUser(TaskUserRepo userRepo) {
        this.empEval = userRepo.getEmpEval(companyId, taskUserId);
    }

    public void initTask(EvaluateTaskRepo taskRepo, TaskUserRepo userRepo) {
        this.empEval = userRepo.getEmpEval(companyId, taskUserId);
        this.cycleEval = taskRepo.getMergeCycleEval(companyId, empEval.getId());
    }

    public List<EvalScoreResult> submitPass(boolean skipped, EmpId opEmpId, Integer submitOrder, String opEmpName, String confirmAuditSignUrl) {
        empEval.submitItems(empEval.getKpiTypes());
        empEval.removeItemDeleted();
        empEval.removeReviewers(Arrays.asList(opEmpId.getId()));
        log.info("levelAuditFlow={}",JSONUtil.toJsonStr(levelAuditFlow));
        levelAuditFlow.submitPass(skipped, opEmpId, submitOrder);
        OperationLog log = new OperationLog(companyId.getId(), taskUserId, OperationLogSceneEnum.AUDIT_ITEM.getScene(), opEmpId.getId(), new Date());
        if (skipped) {
            JSONObject logDesc = new JSONObject();
            logDesc.put("scorerId", opEmpId.getId());
            logDesc.put("scorerType", AuditEnum.CONFIRM_TASK.getScene());
            logDesc.put("scorerName", opEmpName);
            log.setDescription(logDesc.toJSONString());
            log.setBusinessScene(OperationLogSceneEnum.SKIP_REVIEWER.getScene());
        }
        if (StrUtil.isNotBlank(confirmAuditSignUrl)) {
            log.setDescription(confirmAuditSignUrl);
        }
        logs.add(log);
        List<EvalScoreResult> submitRs = levelAuditFlow.getCurPassedScoreRs();
        return submitRs;
    }

    public boolean dispatchNextLevel(TaskUserRepo userRepo) {
        if (levelAuditFlow.isEnd()) {
            return false;
        }
        levelAuditFlow.dispatchNextLevel();
        if (CollUtil.isNotEmpty(levelAuditFlow.nextLevelRs())) {
            userRepo.updateLevelFlow(levelAuditFlow.nextAudits(), levelAuditFlow.curLevelRs(), levelAuditFlow.nextLevelRs());
            recEmpId.addAll(levelAuditFlow.nextLevelEmpIds());
        } else {
            skip(userRepo, levelAuditFlow.currentLevel() + 1);
        }
        if (levelAuditFlow.isEnd()) {
            return false;
        }
        return true;
    }

    public void skip(TaskUserRepo userRepo, Integer nodeOrder) {
        List<EvalScoreResult> curLevelRs = levelAuditFlow.curLevelRs();
        List<SkipEmp> skipEmpList = levelAuditFlow.autoSingleSkipRater(nodeOrder);
        builderSingleLogs(userRepo, skipEmpList);
        if (!levelAuditFlow.isLevelEnd()) {
            userRepo.updateLevelFlow(levelAuditFlow.nextAudits(), curLevelRs, new ArrayList<>());
            this.isEnd = levelAuditFlow.isEnd();
            this.recEmpId = levelAuditFlow.curLevelEmpIds();
            this.recEmpId.removeAll(CollUtil.map(skipEmpList, e -> e.getSkipEmpId(), true));
            return;
        }
        userRepo.updateLevelFlow(levelAuditFlow.nextAudits(), curLevelRs, levelAuditFlow.nextLevelRs());
        log.debug(" =============levelAuditFlow.nextAudits():{}", JSONUtil.toJsonStr(levelAuditFlow.nextAudits()));
        this.isEnd = levelAuditFlow.isEnd();
        if (this.isEnd) {
            return;
        }
        this.recEmpId = (List<String>) levelAuditFlow.nextLevelEmpIds();
    }

    public void builderSingleLogs(TaskUserRepo userRepo, List<SkipEmp> skipEmpList) {
        if (CollUtil.isEmpty(skipEmpList)) {
            return;
        }
        //空缺跳过的
        List<SkipEmp> vacancy = CollUtil.filterNew(skipEmpList, e -> Objects.equals(e.getSkipType(), 1));
        //重复跳过的
        List<SkipEmp> repeat = CollUtil.filterNew(skipEmpList, e -> Objects.equals(e.getSkipType(), 2));
        List<OperationLog> logList = new ArrayList<>();
        if (CollUtil.isNotEmpty(vacancy)) {
            JSONObject logDesc = new JSONObject();
            logDesc.put("scorerId", "-1");
            logDesc.put("scorerType", this.scene);
            logDesc.put("scorerName", "空缺跳过");
            OperationLog log = new OperationLog(this.companyId.getId(), this.taskUserId, OperationLogSceneEnum.SKIP_REVIEWER.getScene(), "-1", logDesc.toJSONString());
            logList.add(log);
        }
        if (CollUtil.isNotEmpty(repeat)) {
            Set<String> empIds = repeat.stream().map(SkipEmp::getSkipEmpId).collect(Collectors.toSet());
            List<KpiEmp> kpiEmps = userRepo.listByEmp(this.companyId, empIds);
            JSONObject logDesc = new JSONObject();
            logDesc.put("scorerId", "-1");
            logDesc.put("scorerType", this.scene);
            logDesc.put("scorerName", StrUtil.join(",", CollUtil.map(kpiEmps, e -> e.getEmpName(), true)) + "重复人员");
            OperationLog log = new OperationLog(this.companyId.getId(), this.taskUserId, OperationLogSceneEnum.SKIP_REVIEWER.getScene(), "-1", logDesc.toJSONString());
            logList.add(log);
        }
        logs.addAll(logList);
    }

    public boolean endCurLevelIfOpt(String opEmpId) {
        ArrayList<String> empIds = new ArrayList<>();
        empIds.add(opEmpId); //清理当前提交人的待办
        boolean curLevelIsFinished = levelAuditFlow.curLevelIsFinished();
        if (curLevelIsFinished) {
            empIds.addAll(levelAuditFlow.curLevelEmpIds());//清理当前提交人同一层级结点的待办
            new CancelTodoEvent(companyId, empEval.getId(), empIds, MsgSceneEnum.confirmScene).publish();
        }
        return curLevelIsFinished;
    }

    public boolean endStageWithNoChangeSkip(String opEmpId) {
        if (!levelAuditFlow.curLevelIsFinished()) {
            return false;
        }
        boolean end = cycleEval.getConfirmTask().noChangeSkip() || levelAuditFlow.nextIsNull();
        if (end) {
            this.batchSendAffirmTask(opEmpId);
            new CancelTodoEvent(companyId, empEval.getId(), MsgSceneEnum.confirmScene).publish();
            new ThisStageEnded(cycleEval, empEval, TalentStatus.CONFIRMING, opEmpId).publish();
        }
        return end;
    }


    public boolean endStageIfOpt(String opEmpId) {
        if (levelAuditFlow.curLevelIsFinished() && levelAuditFlow.nextIsNull()) {
            this.batchSendAffirmTask(opEmpId);
            new CancelTodoEvent(companyId, empEval.getId(), MsgSceneEnum.confirmScene).publish();
            new ThisStageEnded(cycleEval, empEval, TalentStatus.CONFIRMING, opEmpId).publish();
            return true;
        }
        return false;
    }


    public void batchSendAffirmTask(String opEmpId) {
        Set<String> recEmpIds = empEval.listAffirmTaskNotify(opEmpId);
        if (CollUtil.isEmpty(recEmpIds)) {
            return;
        }
        MsgTodoAggregate msgSend = new MsgTodoAggregate(empEval.getCompanyId(), empEval.getTaskId(), cycleEval.getTaskName(), empEval.getEmpId(), empEval.getId()).useScene(MsgSceneEnum.TASK_CONFIRM_FINISH).addExtTempValue("evalEmpName", empEval.getEmpName()).addExtTempValue("remark", "指标审核通过，请查看");
        if (noExtMsgTodoCompanys.contains(empEval.getCompanyId().getId())) {
            msgSend.addCenterMsg().addRecEmpId(recEmpIds).publish();//只发内部待办
        } else {//只发送通知
            msgSend.sendExtMsg().addRecEmpId(recEmpIds).publish();
        }
    }

    public void sendMsgTodoAggregate() {
        new MsgTodoAggregate(empEval.getCompanyId(), empEval.getTaskId(), cycleEval.getTaskName(), empEval.getEmpId(), empEval.getId()).useScene(MsgSceneEnum.TASK_CONFIRM_AUDIT, null).addExtTempValue("evalEmpName", empEval.getEmpName()).addExtTempValue("deadLineDate", empEval.joinDeadLineStr(TalentStatus.CONFIRMING.getStatus())).sendExtMsg().addCenterMsg().sendExtTodo().addRecEmpId(this.getRecEmpId()).publish();
    }

    public void reviewers(TaskUserRepo userRepo, List<KpiEmp> reviewers) {
        empEval.reviewers(reviewers);
        userRepo.updateTaskUser(empEval);
    }
}
