package com.polaris.kpi.eval.domain.task.entity.empeval;

import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.eval.domain.task.entity.EmpEvalScorerNode;
import com.polaris.kpi.eval.domain.task.entity.EvalScorerNodeKpiItem;
import com.polaris.kpi.eval.domain.task.entity.EvalScorerNodeKpiType;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.domain.task.entity.empeval
 * @Author: suxia<PERSON>qiu
 * @CreateTime: 2025-04-25  10:16
 * @Description: 提交的结果分数
 * @Version: 2.0
 */
@Getter
@Setter
public class V3SubmitedEvalNodeScore extends EmpEvalScorerNode{
    private EmpId opEmpId;
    private boolean isReCommit = false;//是否二次提交
    private boolean isSystemSkip = false; //是否系统跳过
    private boolean isSelfSkip = false; //是否自评手动跳过
    private Boolean wasAddLog = true; //是否添加日志
    private Boolean wasTransferRater = false; //是否转交的评分人
    private String taskUserId;
    private String signatureUrl;  //签名地址
    private String logScene;
    private String stepId;
    private SubScoreNodeEnum nodeEnum;

    public void accpOpInfo(TenantId companyId, String opEmpId, boolean isReCommit, String signatureUrl,
                             boolean isSystemSkip, boolean wasTransferRater, boolean wasAddLog, boolean isSelfSkip) {
        this.companyId = companyId;
        this.opEmpId = new EmpId(opEmpId);
        this.logScene = this.getLogScene();
        this.isReCommit = isReCommit;
        this.signatureUrl = signatureUrl;
        this.isSystemSkip = isSystemSkip;
        if (Objects.nonNull(this.wasAddLog) && this.wasAddLog) {
            this.wasAddLog = wasAddLog;
        }
        this.isSelfSkip = isSelfSkip;
        this.wasTransferRater = wasTransferRater;
        if (this.isSelfSkip){
            setScorerNodeScore(BigDecimal.ZERO);//跳过自评，评分人分数是0
        }
    }


    public boolean isCanComputeScore() {
        return !this.needExcludeNode() && !this.isSelfSkip; //自评跳过不进行计算得分,及跳过的节点也不参与计算得分
    }

//    public EmpEvalScorerNode buildSubmitScorerNode() {
//        //这里是处理同一个人同时占互评和定向评两个环节，前端提交过来的顺序定向评可能在前面，提交定向评是不触发评分环节流程的，
//        // 所以当定向评分和其他环节评分一起提交过来的时候，以其他环节的scorerType提交为准。
//        if (scoreNodes.size() > 1 && scoreNodes.get(0).getScoreType().equals("item_score")) {
//            return scoreNodes.get(1);
//        }
//        return scoreNodes.get(0);
//    }
}
