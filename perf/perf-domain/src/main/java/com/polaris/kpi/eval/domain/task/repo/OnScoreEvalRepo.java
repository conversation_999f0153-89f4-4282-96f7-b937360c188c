package com.polaris.kpi.eval.domain.task.repo;

import cn.com.polaris.kpi.eval.ScoreEmp;
import com.polaris.kpi.eval.domain.task.dmsvc.migration.ScorerDataMingrationDmSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.SubmitOrEvalScorerV3DmSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.score.SkipScorerDmSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.score.TransferScorerV3DmSvc;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTaskOperation;
import com.polaris.kpi.eval.domain.task.entity.empeval.EvalScorersWrap;
import com.polaris.kpi.eval.domain.task.entity.empeval.KpiListWrap;
import com.polaris.kpi.org.domain.dept.entity.CompanyMsgCenter;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;

import java.util.List;

/**
 * 评分阶段的仓库
 */
public interface OnScoreEvalRepo {

    EvalOnScoreStage getOnScoreEval(TenantId companyId, String taskUserId);

    EvalResetScoreEmp getResetScoreEmpEval(TenantId companyId, String taskUserId,List<ScoreEmp> scoreEmps);

    TransferScorerV3DmSvc getTransferScorerEmpEval(TenantId companyId, EvalUser evalUser, String fromEmpId, String toEmpId, String opEmpId);

    SkipScorerDmSvc getSkipScorerEmpEval(TenantId companyId, String taskUserId, String skipScorerId, String skipScoreType, String opEmpId);

    ListWrap<ScorerDataMingrationDmSvc> listOnScoreEvalMingration(TenantId companyId, List<String> taskUserIds);

    ScorerDataMingrationDmSvc getOnScoreEvalMingration(TenantId companyId, String taskUserId);

    ListWrap<EvalOnScoreStage> listOnScoreEval(TenantId companyId, List<String> taskUserIds);

    void batchSaveScore(List<EvalOnScoreStage> evalOnScoreStage, String opEmpId);

    void saveEvalOrScorer(SubmitOrEvalScorerV3DmSvc dmSvc);

    void saveEvalUserNodeScore(EvalUser evalUser, KpiListWrap kpiTypes, String opEmpId);

    void saveKpiItemAutoScore(String opEmpId, List<EvalOnExeStage> recomutedKpiScores);

    void saveAutoScoreEvalUserNodeScore(EvalUser evalUser, KpiListWrap kpiTypes, String opEmpId, EvalScorersWrap evalScorersWrap);

    void saveEmpEvalSNScoreByTaskUserId(String companyId,String evalUserId,EvalScorersWrap evalScorersWrap);

    void saveEvalNodeScore(EvalUser evalUser, KpiListWrap kpiTypes, String opEmpId, EvalScorersWrap evalScorersWrap);

    void saveResetScoreEmp(EvalUser evalUser, String opEmpId, EvalResetScoreEmp resetScoreEmp);

    void saveRejectScoreEmp(EvalUser evalUser, ScoreReject scoreReject, List<EmpEvalScorer> empEvalScorers, OperationLog opLog);

    void saveTransferEval(EvalUser evalUser, List<CompanyMsgCenter> fromCenterMsgs, OperationLog log, AdminTaskOperation adminTaskOperation);

    void saveSkipEval(EvalUser evalUser, List<CompanyMsgCenter> fromCenterMsgs, OperationLog log);
}
