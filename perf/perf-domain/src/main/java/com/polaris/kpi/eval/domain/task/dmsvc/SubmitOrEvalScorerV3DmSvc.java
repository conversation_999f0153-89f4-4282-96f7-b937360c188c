package com.polaris.kpi.eval.domain.task.dmsvc;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.*;
import com.polaris.kpi.eval.domain.task.repo.EmpEvalScorerRepo;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.domain.task.dmsvc
 * @Author: suxiaoqiu
 * @CreateTime: 2025-03-03  09:22
 * @Description: 提交评分人的或签处理领域服务
 * @Version: 2.0
 */
@Slf4j
@Getter
public class SubmitOrEvalScorerV3DmSvc {
    @JSONField(serialize = false)
    private final TenantId companyId;
    private final String opEmpId;
    private final EvalUser evalUser;
    private final EmpEvalMerge empEval;
    private final List<V3SubmitedEvalNodeScore> nodeScores;//当前提交的评分环节

    private final EvalScorersWrap scorersWrap;//当前考核任务的评分人

    private final Map<String, EmpEvalKpiType> kpiTypeMap = new HashMap<>();
    private final Map<String,EvalKpi> kpiMap = new HashMap<>();
    private static final BigDecimal ONE_HUNDRED = new BigDecimal(100);
    private final boolean isReCommit = false;//是否二次提交

    @Getter
    private List<String> orFinishedScorerIds = new ArrayList<>();
    private List<String> scorerIds = new ArrayList<>();

    private  List<EmpEvalScorer> orWaitScorers = new ArrayList<>();//或签待提交的

    public SubmitOrEvalScorerV3DmSvc(TenantId companyId, String opEmpId, EvalUser evalUser, EmpEvalMerge empEval,
                                     List<V3SubmitedEvalNodeScore> nodeScores,Set<String> scorerIds) {
        this.companyId = companyId;
        this.opEmpId = opEmpId;
        this.evalUser = evalUser;
        this.empEval = empEval;
        this.nodeScores = nodeScores;
        this.scorersWrap = empEval.getEvalScorersWrap();
        this.scorerIds.addAll(scorerIds);
    }

    public void submitOrScorer() {
        if (Objects.isNull(scorersWrap)) {
            return;
        }
        //处理或签的评分人  //更新同级主结点或签方式子结点
        this.orWaitScorers = scorersWrap.markOrModeScoreNs(this.nodeScores);
        this.filterFinishedScorerIds();//筛选出或签后已完成的评分人，需要清除待办
        log.info("=====submitAnyWrapV3.submitOrScorer.orScorers.orWaitScorers:{},orFinishedScorerIds:{},opEmpId:{}", orWaitScorers.size(),orFinishedScorerIds,opEmpId);
        this.upReviewers();//更新user 责任人
    }

    private void upReviewers() {
       this.scorerIds.addAll(orFinishedScorerIds);
      //  this.scorerIds.add(opEmpId);
        if (CollUtil.isNotEmpty(orFinishedScorerIds)) {
            this.evalUser.removeReviewers(this.orFinishedScorerIds);  //更新责任人
        }
    }

    private void filterFinishedScorerIds() {
        if (CollUtil.isEmpty(orWaitScorers)) {
            return;
        }
        List<String> orFinishedScorerIds = new ArrayList<>();
        for (EmpEvalScorer orWaitScorer : orWaitScorers) {
            if (orWaitScorer.dispatchAllFinished()){
                orFinishedScorerIds.add(orWaitScorer.getScorerId());//如果 orWaitScorerTypes为空，则说明该评分人已经完成所有评分环节
            }
        }
        this.orFinishedScorerIds = orFinishedScorerIds;
    }

    public List<String> getOrScorerIds() {
        return orWaitScorers.stream().map(EmpEvalScorer::getScorerId).collect(Collectors.toList());
    }
}
