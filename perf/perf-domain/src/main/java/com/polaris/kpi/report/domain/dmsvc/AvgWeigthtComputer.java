package com.polaris.kpi.report.domain.dmsvc;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.infr.task.ppojo.report
 * @Author: lufei
 * @CreateTime: 2024-01-18  18:28
 * @Version: 1.0
 */
@Getter
@Setter
public class AvgWeigthtComputer {

    private List<ScoreElement> elements = new ArrayList<>();

    public AvgWeigthtComputer(List<BigDecimal> scores, Map<Integer, BigDecimal> weightMap) {
        for (int i = 0; i < scores.size(); i++) {
            BigDecimal score = scores.get(i);
            BigDecimal weight = weightMap.get(i + 1);
            elements.add(new ScoreElement(i, score, weight == null ? BigDecimal.ZERO : weight));
        }
    }

    public void compute() {
        BigDecimal noneSum = BigDecimal.ZERO;
        BigDecimal valueSum = BigDecimal.ZERO;
        for (ScoreElement element : elements) {
            if (element.score == null) {//无值
                noneSum = noneSum.add(element.weight);
            } else {
                valueSum = valueSum.add(element.weight);
            }
        }
        //if (noneSum == BigDecimal.ZERO) {
        //    return;
        //}
        for (ScoreElement element : elements) {
            element.computeRealWeight(valueSum, noneSum);
        }
    }

    public BigDecimal sumScore() {
        BigDecimal sumRealScore = BigDecimal.ZERO;
        for (ScoreElement element : elements) {
            if (element.getScore() != null) {
                sumRealScore = sumRealScore.add(element.getScore());
            }
        }
        return sumRealScore;
    }

    public BigDecimal getScore(Integer indx) {
        return elements.get(indx).score;
    }

    @Getter
    private static class ScoreElement {
        private Integer indx;
        private BigDecimal score;
        private BigDecimal weight;
        private BigDecimal realWeight;

        public ScoreElement(Integer indx, BigDecimal score, BigDecimal weight) {
            this.indx = indx;
//            this.score = StrUtil.isBlank(score) ? null : new BigDecimal(score);
            this.score = score;
            this.weight = weight;
        }

        public void computeRealWeight(BigDecimal valueSum, BigDecimal noneSum) {
            if (score == null) {
                return;
            }
            BigDecimal rate = isZero(valueSum) ? BigDecimal.ZERO : weight.divide(valueSum, 3, BigDecimal.ROUND_HALF_UP);
            realWeight = isZero(valueSum) ? BigDecimal.ZERO : weight.add(noneSum.multiply(rate));
            score = isZero(valueSum) ? BigDecimal.ZERO : this.score.multiply(realWeight).divide(new BigDecimal("100"), 3, BigDecimal.ROUND_HALF_UP);
        }

        public boolean isZero(BigDecimal value) {
            return (BigDecimal.ZERO).compareTo(value) == 0;
        }
    }
}
