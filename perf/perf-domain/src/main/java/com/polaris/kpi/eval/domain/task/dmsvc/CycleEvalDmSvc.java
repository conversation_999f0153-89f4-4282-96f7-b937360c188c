package com.polaris.kpi.eval.domain.task.dmsvc;

import cn.com.polaris.kpi.EmpStaff;
import cn.com.polaris.kpi.ItemDecompose;
import cn.com.polaris.kpi.company.MsgSceneEnum;
import cn.com.polaris.kpi.eval.EvaluationStaff;
import cn.com.polaris.kpi.eval.Name;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.perf.www.common.em.CompanyMsgActionEnum;
import com.perf.www.common.utils.date.DateTimeUtils;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalKpiType;
import com.polaris.kpi.eval.domain.task.entity.msg.MsgTodoAggregate;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.eval.domain.temp.entity.ExamGroup;
import com.polaris.kpi.eval.domain.temp.entity.PerfTemplEvaluateAudit;
import com.polaris.sdk.type.DmEventPublish;
import com.polaris.sdk.type.TenantId;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lufei
 * @date 2022/1/20 6:27 下午
 */
@Service
public class CycleEvalDmSvc {
    public static String MSG_URL_PREFIX = "#/login?toRouteName=taskDetail&";
    //泰斗微和垂天科技的不发工作通知和待办
    public static List<String> noExtMsgTodoCompanys = Arrays.asList("298e2e6d-4afe-4522-bd13-038c2db26dba","1025901");

    public CycleEval createCycleEval(Name name, String taskDesc, EvaluationStaff srcStaff, ExamGroup temp, List<EmpStaff> empStaffs) {
        if (StrUtil.isBlank(taskDesc)) {
            taskDesc = temp.getTemplDesc();
        }
        final CycleEval cycleEval = new CycleEval(name, taskDesc, temp.cycleType(), temp.getCustomFullScore());
        if (srcStaff != null) {
            cycleEval.setEvaluationStaff(srcStaff.getConfItems());//兼容旧数据 TODO 如果无用则删除
            cycleEval.setExcludeStaff(srcStaff.getExcludeStaff());//兼容旧数据 TODO 如果无用则删除
        }

        //copy公示相关配置到任务上
        cycleEval.copyPublicConf(temp.getPublicType(), temp.publicEmpJson(),
                temp.publicToEmp(), temp.getPublicDimension());

        cycleEval.copyResultAffirmConf(temp.getResultAffirm(), temp.getAffirmSignature(),
                temp.getCanAppeal(), temp.getAppealReceiver(), temp.getAutoResultAffirm(),
                temp.getAutoResultAffirmDay());
        //模板发起考核任务配置
        cycleEval.copyTemp(temp);
        cycleEval.setEmpStaffs(empStaffs);

        return cycleEval;
    }

    //分发考核人次的任务
    public EvalUser createEvalUser(Boolean isOpenPoint, CycleEval cycleEval,
                                   EmpStaff empStaff, Map<String, ItemDecompose> decomposeMap) {
        //   todo  生成 result 到达对应的阶段时生成
        //生成 taskUser
        EvalUser taskUser =
                new EvalUser(cycleEval.getCompanyId(), cycleEval.getCreatedUser(), cycleEval.getId(), empStaff.getEmpId());
        taskUser.shortOrg(empStaff.getOrgName(), empStaff.getEmpName());
        taskUser.setOrgId(empStaff.getOrgId());
        taskUser.acceptOrgPath(empStaff.getAtOrgCodePath(),empStaff.getAtOrgNamePath(),empStaff.getAtOrgPathHight());
        taskUser.setCycleId(cycleEval.getCycleId());
        taskUser.setTempTask(1);
        //初始化 perf_evaluate_task_kpi
        List<EvalKpi> kpis = cycleEval.cloneKpi(taskUser.getEmpId(), taskUser.getId(),
                isOpenPoint, decomposeMap);

        List<EmpEvalKpiType> types = cycleEval.cloneKpiTypes(taskUser.getId());
        taskUser.setKpiTypes(types);
        //    生成 kpiItem
        taskUser.initOnNew(cycleEval.isAutoPublic(), kpis);
        List<EvalOkrType> emptyTypes = cycleEval.cloneEmptyTypes(taskUser.getEmpId(), taskUser.getId());
        taskUser.initEmptyType(emptyTypes);
        //    生成 Audit
        taskUser.initResultAudits(convertAudit(cycleEval.resultAudits(), cycleEval.getCompanyId()));
        taskUser.initChangeItemAudits(convertAudit(cycleEval.changeItemAudits(), cycleEval.getCompanyId()));
        taskUser.initModifyItemAudits(convertAudit(cycleEval.modifyItemAudit(), cycleEval.getCompanyId()));
        //    生成 评分流程及评分人
        final List<EvalAudit> subScorers = convertAudit(cycleEval.subScorers(taskUser.getEmpId()), cycleEval.getCompanyId());
        final List<EvalAudit> peerScorers = convertAudit(cycleEval.peerScorers(taskUser.getEmpId()), cycleEval.getCompanyId());
        final List<EvalAudit> supperScorers = convertAudit(cycleEval.superiorScorers(), cycleEval.getCompanyId());
        final List<EvalAudit> appointScorers = convertAudit(cycleEval.appointScorers(), cycleEval.getCompanyId());
        taskUser.initScoreAudits(cycleEval.getTemplEvaluateJson().getSelfScoreFlag(), subScorers, peerScorers, supperScorers, appointScorers);
        //初始化公式字段
        return taskUser;
    }

    private List<EvalAudit> convertAudit(List<PerfTemplEvaluateAudit> resultAudits, TenantId tenantId) {
        if (CollUtil.isEmpty(resultAudits)) {
            return Collections.emptyList();
        }
        final List<EvalAudit> taskAudits = resultAudits.stream().map(tmpAudit -> {
            final EvalAudit auditModel = Convert.convert(EvalAudit.class, tmpAudit);
            auditModel.setId(UUID.randomUUID().toString());
            auditModel.setWeight(tmpAudit.getSuperiorScoreWeight());
            auditModel.setCompanyId(tenantId);
            return auditModel;
        }).collect(Collectors.toList());
        return taskAudits;
    }

    /**
     *
     * @param taskName
     * @param taskUser
     * @param inputEmpIds
     * @param sendType 发送类型 内部待办=1  外部消息=2  外部待办=4
     */
    public void batchCreateTodoTask(Name taskName, EvalUser taskUser, Set<String> inputEmpIds,int sendType) {
        if (CollUtil.isEmpty(inputEmpIds)) {
            return;
        }
        MsgTodoAggregate msgSend = new MsgTodoAggregate(taskUser.getCompanyId(), taskUser.getTaskId(), taskName, taskUser.getEmpId(), taskUser.getId())
                .useScene(MsgSceneEnum.TASK_SUBMIT_PROGRESS, CompanyMsgActionEnum.SUBMIT_PROGRESS)
                .addExtTempValue("evalEmpName",taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                .addExtTempValue("deadLineDate",taskUser.joinDeadLineStr(TalentStatus.CONFIRMED.getStatus()))
                .addTodoItem("msg.task.emp", taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName());
        if (noExtMsgTodoCompanys.contains(taskUser.getCompanyId().getId())) {
            msgSend.addCenterMsg().addRecEmpId(inputEmpIds).publish();//只发内部待办
        } else {//全部发送
            if ((sendType & 1) > 0) {
                msgSend.addCenterMsg();
            }
            if ((sendType & 2) > 0) {
                msgSend.sendExtMsg();
            }
            if ((sendType & 4) > 0) {
                msgSend.sendExtTodo();
            }
            msgSend.addRecEmpId(inputEmpIds).publish();
        }
    }

    public void batchSendAffirmTask(AdminTask adminTask, EvalUser taskUser, Set<String> recEmpIds) {
        if (CollUtil.isEmpty(recEmpIds)) {
            return;
        }
        MsgTodoAggregate msgSend = new MsgTodoAggregate(taskUser.getCompanyId(), taskUser.getTaskId(), new Name(adminTask.getTaskName()), taskUser.getEmpId(), taskUser.getId())
                .useScene(MsgSceneEnum.TASK_CONFIRM_FINISH)
                .addExtTempValue("evalEmpName", taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                .addExtTempValue("remark","指标审核通过，请查看");
        if (noExtMsgTodoCompanys.contains(taskUser.getCompanyId().getId())) {
            msgSend.addCenterMsg().addRecEmpId(recEmpIds).publish();//只发内部待办
        } else {//只发送通知
                msgSend.sendExtMsg().addRecEmpId(recEmpIds).publish();
        }
    }

    public void batchSendAffirmTask(CycleEval cycleEval, EvalUser taskUser, Set<String> recEmpIds) {
        if (CollUtil.isEmpty(recEmpIds)) {
            return;
        }
        MsgTodoAggregate msgSend = new MsgTodoAggregate(taskUser.getCompanyId(), taskUser.getTaskId(), cycleEval.getTaskName(), taskUser.getEmpId(), taskUser.getId())
                .useScene(MsgSceneEnum.TASK_CONFIRM_FINISH)
                .addExtTempValue("evalEmpName", taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                .addExtTempValue("remark","指标审核通过，请查看");
        if (noExtMsgTodoCompanys.contains(taskUser.getCompanyId().getId())) {
            msgSend.addCenterMsg().addRecEmpId(recEmpIds).publish();//只发内部待办
        } else {//只发送通知
            msgSend.sendExtMsg().addRecEmpId(recEmpIds).publish();
        }
    }

    public void batchSendFinishValueChanged(Name taskName, EvalUser taskUser,
                                            Set<String> recEmpIds,String empName,String opEmpId) {
        if (CollUtil.isEmpty(recEmpIds)) {
            return;
        }
        recEmpIds.remove(opEmpId);
        MsgTodoAggregate msgSend = new MsgTodoAggregate(taskUser.getCompanyId(), taskUser.getTaskId(), taskName, taskUser.getEmpId(), taskUser.getId())
                .useScene(MsgSceneEnum.FINISH_VALUE_CHANGED)
                .addExtTempValue("evalEmpName",taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                .addExtTempValue("inputName",empName)
                .addExtTempValue("updateDate", DateTimeUtils.date2StrDate(new Date()))
                .addExtTempValue("updateDetails",taskUser.getInputFinishChanged());
        if (StrUtil.isBlank(taskUser.getInputFinishChanged())) {
            return;
        }
        if (noExtMsgTodoCompanys.contains(taskUser.getCompanyId().getId())) {
            msgSend.addCenterMsg().addRecEmpId(recEmpIds).publish();//只发内部待办
        } else {//只发送通知
            msgSend.sendExtMsg().addRecEmpId(recEmpIds).publish();
        }
    }

    @DmEventPublish
    public void batchMsgTodoTask(MsgTodoInfo info) {
        if (CollUtil.isEmpty(info.getRecEmpId())) {
            return;
        }
        MsgTodoAggregate msgSend = new MsgTodoAggregate(new TenantId(info.getCompanyId()),
                info.getTaskId(), new Name(info.getTaskName()), info.getEvalEmpId(), info.getTaskUserId())
                .useScene(MsgSceneEnum.queryType(info.getBusinessScene()), CompanyMsgActionEnum.actionByType(info.getBusinessScene()))
                .addExtTempValue("evalEmpName", info.getEmpName())
                .addExtTempValue("deadLineDate",StrUtil.isBlank(info.getDeadLineDate()) ? "未开启" : info.getDeadLineDate())
                .addTodoItem("msg.task.emp", info.getEmpName());
        msgSend.addCenterMsg().sendExtMsg().sendExtTodo().addRecEmpId(info.getRecEmpId()).publish();
    }
}
