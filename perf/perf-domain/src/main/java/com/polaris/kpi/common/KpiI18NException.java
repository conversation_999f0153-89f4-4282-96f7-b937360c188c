package com.polaris.kpi.common;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;
import lombok.Setter;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.common
 * @Author: lufei
 * @CreateTime: 2022-12-03  11:50
 * @Description: TODO
 * @Version: 1.0
 */
@Getter
@Setter
public class KpiI18NException extends RuntimeException {
    private String code;
    private String msg;

    public KpiI18NException(String code) {
        this.code = code;
    }

    public KpiI18NException(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public boolean hasMsg() {
        return StrUtil.isNotBlank(msg);
    }

    public String getCode() {
        return code;
    }

    public String getI18NMsg() {
        return msg;
    }

    public String getI18NCode() {
        return code;
    }
}
