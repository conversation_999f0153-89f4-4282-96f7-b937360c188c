package com.polaris.kpi.setting.domain.repo;

import com.polaris.kpi.setting.domain.entity.ManagerPriv;
import com.polaris.kpi.setting.domain.entity.ManagerPrivLog;

import java.util.List;

public interface ManagerPrivRepo {

    /**新增部门管理权限*/
    void saveManagerPriv(ManagerPriv priv);

    /**编辑部门管理权限*/
    void updateManagerPriv(ManagerPriv priv);

    /**
     *批量操作部门管理权限状态
     * @param ids
     * @param operateType 操作类型  1=开启  2=关闭  3=删除
     */
    void batchOperate(List<String>ids,Integer operateType);

    /**插入日志*/
    void addLog(ManagerPrivLog privLog);

    /**批量插入日志*/
    void batchSaveLog(List<ManagerPrivLog> privLogList);

    void initManagerPriv(String companyId);

    void fullAddManagerPriv(String companyId);
}
