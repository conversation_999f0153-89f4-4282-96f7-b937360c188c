package com.polaris.kpi.eval.domain.task.dmsvc;

import cn.com.polaris.kpi.company.MsgSceneEnum;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.perf.www.common.em.OperationLogSceneEnum;
import com.polaris.kpi.ask.domain.acl.AskEvalAcl;
import com.polaris.kpi.eval.domain.task.entity.EvalKpiInputBak;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.OperationLog;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalRule;
import com.polaris.kpi.eval.domain.task.entity.empeval.EvalScorersWrap;
import com.polaris.kpi.eval.domain.task.event.admineval.EvalEmpOkrLockEdited;
import com.polaris.kpi.eval.domain.task.repo.EmpEvalScorerRepo;
import com.polaris.kpi.eval.domain.task.repo.EvalTaskInterviewRepo;
import com.polaris.kpi.eval.domain.task.repo.TaskKpiRepo;
import com.polaris.kpi.eval.domain.task.repo.TaskUserRepo;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.sdk.type.AuditEnum;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import org.slf4j.MDC;

import java.util.*;

public class ResetTaskDmSvc {
    private TenantId companyId;
    private String taskUserId;
    private String opEmpId;
    private String resetNode;
    private String operationType;
    private String resetReason;
    private EvalUser taskUser;

    @Getter
    private Set<String> clearScoreRsScenes = new HashSet<>();
    @Getter
    private OperationLog log;

    public ResetTaskDmSvc(TenantId companyId, String opEmpId, String resetNode,
                          String operationType, String resetReason, EvalUser taskUser) {
        this.companyId = companyId;
        this.opEmpId = opEmpId;
        this.resetNode = resetNode;
        this.operationType = operationType;
        this.resetReason = resetReason;
        this.taskUser = taskUser;
        this.taskUserId = taskUser.getId();
    }

    public void handResetStage(TaskKpiRepo kpiRepo, TaskUserRepo userRepo,
                               AskEvalAcl askEvalAcl, EvalTaskInterviewRepo interviewRepo, EmpEvalScorerRepo empEvalScorerRepo,  EmpEvalMerge evalMerge) {
        if (Objects.isNull(this.taskUser)) {
            return;
        }
        TalentStatus current = TalentStatus.statusOf(taskUser.getTaskStatus());
        TalentStatus toStage = TalentStatus.statusOf(resetNode);
        clearScoreRsScenes.add(MsgSceneEnum.TASK_WAIT_PUBLIC.getType());
        clearScoreRsScenes.add(MsgSceneEnum.TASK_RESULT_APPEAL.getType());  //考核申述
        // 清理数据
        if (toStage.beforeEq(TalentStatus.CONFIRMING) && TalentStatus.CONFIRMING.beforeEq(current)) {//确认中阶段
            clearScoreRsScenes.add(AuditEnum.CONFIRM_TASK.getScene());
            clearScoreRsScenes.addAll(MsgSceneEnum.confirmScene);
            clearScoreRsScenes.addAll(MsgSceneEnum.onConfirming());
            clearScoreRsScenes.add(MsgSceneEnum.SET_MUTUAL_AUDIT.getType());
            clearScoreRsScenes.add(MsgSceneEnum.REJECT_FINISH_VALUE.getType());
            clearScoreRsScenes.add(MsgSceneEnum.TASK_SUBMIT_AUTO_ITEM_PROGRESS.getType());
            //清空taskKpi最终提交的标识 @志林
            kpiRepo.reSetKpiSubmitTab(companyId, taskUserId, resetNode);
            taskUser.cleanInputTab();
            //恢复录入人
            ListWrap<EvalKpiInputBak> inputBakWrap = kpiRepo.listWrapInputBak(companyId, taskUserId);
            taskUser.regainInput(inputBakWrap);
            taskUser.clearScoreFlag();
            kpiRepo.reSetInput(taskUser.getCompanyId(), taskUser.getKpis());
            userRepo.resetScore(companyId, taskUserId);
            resetScoreBeforeExplainScorer(evalMerge, empEvalScorerRepo);//重置评价关系
            //修改问卷分发状态
            askEvalAcl.clearAskDispatchedStatus(companyId.getId(), taskUserId, opEmpId);
            taskUser.setFinishValueAuditStatus(0);
            userRepo.updateTaskUser(taskUser);
            //清除邀请的评价人
            taskUser.clearInviteMutualEmp();
            new EvalEmpOkrLockEdited(companyId, Arrays.asList(taskUserId), new EmpId(opEmpId), "del").publish();
        }
        if (toStage.beforeEq(TalentStatus.CONFIRMED) && TalentStatus.CONFIRMED.beforeEq(current)) {//执行
            //clearTodoScenes.addAll(MsgSceneEnum.onConfirmed());
            clearScoreRsScenes.addAll(MsgSceneEnum.onConfirming());
            clearScoreRsScenes.add(AuditEnum.EDIT_EXE_INDI.getScene());
            clearScoreRsScenes.add(MsgSceneEnum.INVITE_PEER_AUDIT.getType());
            clearScoreRsScenes.add(MsgSceneEnum.INVITE_SUB_AUDIT.getType());
            clearScoreRsScenes.add(MsgSceneEnum.SET_MUTUAL_AUDIT.getType());
            clearScoreRsScenes.add(MsgSceneEnum.REJECT_FINISH_VALUE.getType());
            clearScoreRsScenes.add(MsgSceneEnum.TASK_SUBMIT_AUTO_ITEM_PROGRESS.getType());
            //清空taskKpi最终提交的标识 @志林
            kpiRepo.reSetKpiSubmitTab(companyId, taskUserId, resetNode);
            taskUser.cleanInputTab();
            //恢复录入人
            ListWrap<EvalKpiInputBak> inputBakWrap = kpiRepo.listWrapInputBak(companyId, taskUserId);
            taskUser.regainInput(inputBakWrap);
            //清除评分标记
            taskUser.clearScoreFlag();
            userRepo.resetScore(companyId, taskUserId);
            resetScoreBeforeExplainScorer(evalMerge, empEvalScorerRepo);//重置评价关系
            //修改问卷分发状态
            askEvalAcl.clearAskDispatchedStatus(companyId.getId(), taskUserId, opEmpId);
            //清除录入值缓存
            //kpiRepo.deletedCacheInfo(companyId,taskUserId,new EmpId(opEmpId));
            taskUser.setFinishValueAuditStatus(0);
            userRepo.updateTaskUser(taskUser);
            //清除邀请的评价人
            taskUser.clearInviteMutualEmp();
            new EvalEmpOkrLockEdited(companyId, Arrays.asList(taskUserId), new EmpId(opEmpId), "del").publish();
        }
        if (toStage.beforeEq(TalentStatus.FINISH_VALUE_AUDIT) && TalentStatus.FINISH_VALUE_AUDIT.beforeEq(current)) {//完成值审核
            clearScoreRsScenes.add(AuditEnum.FINISH_VALUE_AUDIT.getScene());
           // clearScoreRsScenes.add(MsgSceneEnum.REJECT_FINISH_VALUE.getType());
            //清除评分标记
            taskUser.clearScoreFlag();
            userRepo.resetScore(companyId, taskUserId);
            resetScoreBeforeExplainScorer(evalMerge, empEvalScorerRepo);//重置评价关系
            //修改问卷分发状态
            askEvalAcl.clearAskDispatchedStatus(companyId.getId(), taskUserId, opEmpId);
        }
        if (toStage.beforeEq(TalentStatus.SCORING) && TalentStatus.SCORING.beforeEq(current)) {//评分
            clearScoreRsScenes.addAll(MsgSceneEnum.scoreScene);
            clearScoreRsScenes.addAll(AuditEnum.allScore());
            taskUser.clearScoreFlag();
            userRepo.resetScore(companyId, taskUserId);
            taskUser.clearScoreReviewersJson();
            resetScoreBeforeExplainScorer(evalMerge, empEvalScorerRepo);//重置评价关系
            //  EvalScorersWrap scorersWrap = empEvalScorerRepo.getEmpEvalScorersWrap(companyId.getId(), taskUserId);
            //scorersWrap.resetScoreStage();//重置评分人
            //empEvalScorerRepo.resetScoreEmpEvalScorer(companyId.getId(),opEmpId, taskUserId,scorersWrap.getDatas());//清除评分人及环节及指标分
            //修改问卷分发状态
            askEvalAcl.clearAskDispatchedStatus(companyId.getId(), taskUserId, opEmpId);
            if (evalMerge.inputOnScoring()){
                kpiRepo.reSetKpiSubmitTab(companyId, taskUserId, resetNode, evalMerge.inputOnScoring());
            }
        }
        if (toStage.beforeEq(TalentStatus.RESULTS_AUDITING) && TalentStatus.RESULTS_AUDITING.beforeEq(current)) {//校准
            clearScoreRsScenes.addAll(Arrays.asList(MsgSceneEnum.TASK_RESULT_AUDIT.getType(), MsgSceneEnum.TASK_RESULT_AFFIRM.getType()));
            clearScoreRsScenes.add(AuditEnum.FINAL_RESULT_AUDIT.getScene());
            taskUser.resetFinalScoreAndLevel();
        }

        if (toStage.beforeEq(TalentStatus.RESULTS_INTERVIEW) && TalentStatus.RESULTS_INTERVIEW.beforeEq(current)) {//结果面谈
            clearScoreRsScenes.addAll(MsgSceneEnum.interviewScene);
            clearScoreRsScenes.add(AuditEnum.FINAL_RESULT_INTERVIEW.getScene());
            interviewRepo.clearInterview(companyId, taskUserId);
        }
        if (toStage.beforeEq(TalentStatus.RESULTS_AFFIRMING) && TalentStatus.RESULTS_AFFIRMING.beforeEq(current)) {//结果确认
            clearScoreRsScenes.addAll(Arrays.asList(MsgSceneEnum.TASK_RESULT_AFFIRM.getType()));
            clearScoreRsScenes.add(AuditEnum.PERF_RESULTS_AFFIRM.getScene());
        }
        taskUser.setHasAppeal(Boolean.FALSE.toString());
        userRepo.updateTaskUser(taskUser);
        //userRepo.resetAuditStatus(companyId, taskUserId, clearScoreRsScenes);
        //清除申诉次数
        taskUser.cleanAppealTimes();
        userRepo.resetStageAudit(companyId, taskUserId, clearScoreRsScenes, operationType, toStage, taskUser);


        JSONObject logDesc = new JSONObject();
        logDesc.put("resetReason", resetReason);
        logDesc.put("resetNode", resetNode);
        //重置操作日志
        OperationLog operationLog = new OperationLog(companyId.getId(), taskUserId,
                OperationLogSceneEnum.RESET_TASK_TO_NODE.getScene(), opEmpId, logDesc.toJSONString());
        operationLog.setCreatedTime(new Date());
        operationLog.setOperationType(operationType);
        this.log = operationLog;
    }

    private void resetScoreBeforeExplainScorer(EmpEvalMerge evalMerge, EmpEvalScorerRepo empEvalScorerRepo) {
        //重置评分前重新进行下解析评价关系,删除是逻辑删除
        EmpEvalRule rule = new EmpEvalRule();
        BeanUtil.copyProperties(evalMerge, rule);
        ExplainEvalScorerDmSvc explainEvalScorerDmSvc = new ExplainEvalScorerDmSvc(taskUser, rule, companyId, rule.getCreatedUser());
        explainEvalScorerDmSvc.explainEvalScorer();
        empEvalScorerRepo.saveBatchEmpEvalScorerAndClearOld(companyId.getId(),  rule.getCreatedUser(),taskUser.getId(), explainEvalScorerDmSvc.getScorers().getDatas());
    }
}
