package com.polaris.kpi.operLog.domain;

import com.alibaba.fastjson.JSONObject;
import com.polaris.kpi.eval.domain.task.entity.OperationLog;
import com.polaris.kpi.eval.domain.task.entity.empeval.skipRater.SkipRater;
import com.polaris.kpi.org.domain.emp.type.Emp;
import com.polaris.sdk.type.TenantId;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: xw.xu
 * @Date: 2025/09/08 09:46
 * @Desc:
 */
public class OperateLogDmSvc {
    public List<OperationLog> buildOperateLog(TenantId tenantId, List<SkipRater> skipRaters, String scene, Emp opEmp){
        List<OperationLog> opLogs = new ArrayList<>();
        JSONObject log;
        OperationLog opLog;
        for (SkipRater skipRater : skipRaters){
            log = new JSONObject();
            log.put("skipUserName", skipRater.getSkipUserName());
            log.put("opEmpName", opEmp.getName());
            log.put("scene", scene);
            opLog = new OperationLog(tenantId.getId(), skipRater.getTaskUserId(), scene, opEmp.getId(), JSONObject.toJSONString(log));
            opLogs.add(opLog);
        }
        return opLogs;
    }
}
