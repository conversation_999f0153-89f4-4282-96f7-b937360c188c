package com.polaris.kpi.eval.domain.stage.entity;

import com.polaris.kpi.eval.domain.TaskEvalContext;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class StageContainerChain {

    @Getter
    private List<StageContainer> stages = new ArrayList<>();

    private void addNext(StageContainer container) {
        stages.add(container);
    }

    //未来增加配置控制点后, 根据配置进行创建.
    public static StageContainerChain initStageChain() {
        StageContainerChain stageChain = new StageContainerChain();
        stageChain.addNext(new StageContainer(TalentStatus.CREATED.getDesc(), TalentStatus.CREATED, 1));
        stageChain.addNext(new StageContainer(TalentStatus.CONFIRMING.getDesc(), TalentStatus.CONFIRMING, 2));
        stageChain.addNext(new StageContainer(TalentStatus.CONFIRMED.getDesc(), TalentStatus.CONFIRMED, 3));
        stageChain.addNext(new StageContainer(TalentStatus.FINISH_VALUE_AUDIT.getDesc(), TalentStatus.FINISH_VALUE_AUDIT, 4));
        stageChain.addNext(new StageContainer(TalentStatus.SCORING.getDesc(), TalentStatus.SCORING, 5));
        stageChain.addNext(new StageContainer(TalentStatus.ON_LEVEL.getDesc(), TalentStatus.ON_LEVEL, 6));
        stageChain.addNext(new StageContainer(TalentStatus.RESULTS_AUDITING.getDesc(), TalentStatus.RESULTS_AUDITING, 7));
        stageChain.addNext(new StageContainer(TalentStatus.RESULTS_INTERVIEW.getDesc(), TalentStatus.RESULTS_INTERVIEW, 8));
        stageChain.addNext(new StageContainer(TalentStatus.RESULTS_AFFIRMING.getDesc(), TalentStatus.RESULTS_AFFIRMING, 9));
        stageChain.addNext(new StageContainer(TalentStatus.WAIT_PUBLISHED.getDesc(), TalentStatus.WAIT_PUBLISHED, 10));
        stageChain.addNext(new StageContainer(TalentStatus.FINISHED.getDesc(), TalentStatus.FINISHED, 11));
        return stageChain;
    }

    public void execute(TaskEvalContext evalContext) {
        for (StageContainer stage : stages) {
            if (evalContext.shouldContinue()) {
                stage.execute(evalContext);
            }
        }
    }

    public void matchContext(TalentStatus curStatus, TaskEvalContext taskEvalCtx) {
        for (StageContainer stage : stages) {
            if (stage.getStatus() == curStatus) {
                taskEvalCtx.initStageOrder(stage.getOrder());
            }
        }
    }
}
