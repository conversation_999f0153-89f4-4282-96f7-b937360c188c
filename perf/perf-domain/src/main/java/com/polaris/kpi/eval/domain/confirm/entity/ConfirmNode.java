package com.polaris.kpi.eval.domain.confirm.entity;

import cn.com.polaris.kpi.eval.Rater;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.common.KpiI18NException;
import com.polaris.kpi.eval.domain.task.entity.EvalAudit;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.BaseAuditNode;
import com.polaris.sdk.type.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * @Author: lufei
 * @CreateTime: 2022-08-31  15:29
 * @Description: *_task_audit 表基类
 * @Version: 1.0
 */
@NoArgsConstructor
@Getter
@Setter
public class ConfirmNode extends BaseAuditNode {
    private String modifyItemDimension;      //修改指标维度  默认可以修改指标全部内容 "all","target", 后面可能扩展 "target,itemName,.."
    private String confirmAuditSign;        //手写签名开关
    @JSONField(serialize = false,deserialize = false)
    @Getter
    private MapWrap<String, List<EvalScoreResult>> rsMap;
    @JSONField(serialize = false)
    @Getter
    private List<EvalAudit> itemAudits = new ArrayList<>(); //指标审核人，一个人存在多条 EvalAudit
    @JSONField(serialize = false)
    @Getter
    private List<String> finishedScorerIds = new ArrayList<>();

    public ConfirmNode(Integer approvalOrder, String multiType) {
        super(approvalOrder, multiType);
    }

    public boolean havAuditIndFlow() {
        return isOnIndAudit() && CollUtil.isNotEmpty(itemAudits);
    }

    public boolean auditIndFlowIsNull() {
        return isOnIndAudit() && CollUtil.isEmpty(itemAudits);
    }

    public void acceptItemAudits(List<EvalAudit> auditIndFlow){
        itemAudits = auditIndFlow;
    }

    public List<String> listScorerId() {
        if (isOnIndAudit()) {
            return itemAudits.stream().map(EvalAudit::getApproverInfo).distinct().collect(Collectors.toList());
        }
        return raters.stream().map(Rater::getEmpId).distinct().collect(Collectors.toList());
    }

    public List<EvalScoreResult> listEvalScoreResult() {
        List<EvalScoreResult> ess = new ArrayList<>();
        for (String s : rsMap.keySet()) {
            ess.addAll(rsMap.get(s));
        }
        return ess;
    }

    public List<String> listTaskAuditIdFromPass( List<EvalScoreResult> ess){
        return ess.stream().filter(EvalScoreResult::isPassed).map(EvalScoreResult::getTaskAuditId).collect(Collectors.toList());
    }

    public void onResults(List<EvalScoreResult> rsOfNode) {
        ListWrap<EvalScoreResult> rsList = new ListWrap<>(rsOfNode).groupBy(EvalScoreResult::getScorerId);
        this.rsMap = new MapWrap<>();
        for (String s : rsList.groupKeySet()) {
            rsMap.put(s, rsList.groupGet(s));
        }
    }

    @NotNull
    public String modifyFlagName() {
        if (modifyFlag == null) {
            return "";
        }
        StringBuilder sb = auditFlowName();
        sb.append(Boolean.parseBoolean(modifyFlag) ? "允许修改|" : "不允许修改]");
        if (Boolean.parseBoolean(modifyFlag)) {
            sb.append(modifyItemDimensionName());
        }
        return sb.toString() + "]";
    }

    private String modifyItemDimensionName() {
        if ("all".equals(modifyItemDimension)) {
            return "所有信息";
        }
        return "指标目标值";
    }


    @NotNull
    public String confirmAuditSignName() {
        if (confirmAuditSign == null) {
            return "";
        }
        StringBuilder sb = auditFlowName();
        sb.append(Boolean.parseBoolean(confirmAuditSign) ? "需要签名]" : "不需要签名]");
        return sb.toString();
    }

    public boolean allPassed() {
        List<EvalScoreResult> rs = listEvalScoreResult();
        return isAllPassed(rs);
    }

    public void onStatus() {
        for (Rater rater : raters) {
            String status = null;
            List<EvalScoreResult> rs = rsMap.get(rater.getEmpId());
            if (CollUtil.isNotEmpty(rs)) {
                status = isAllPassed(rs) ? "finished" : "doing";
            }
            rater.onStatus(status);
        }
        //兼容一下转交
        if (allPassed()) {
            raters.forEach(rater -> rater.onStatus("finished"));
        }
        if (CollUtil.isEmpty(rsMap.getDatas())) {
            this.status = "wait";
            return;
        }
        if (raters.stream().allMatch(Rater::isFinised) && allPassed()) {
            this.status = "finished";
            return;
        }
        this.status = "dispatched";
    }

    public String getApprovalOrderStr() {
        return approvalOrder + "";
    }

//    private void acceptPass(EmpId confirmEmpId) {
//        EvalScoreResult submitResult = rsMap.get(confirmEmpId.getId());
//        if (submitResult == null) {
//            throw new KpiI18NException("10001", "非法操作,您无操作权限");
//        }
//        if (submitResult.isPassed()) {
//            throw new KpiI18NException("10002", "您的操作已提交,无需要重复提交");
//        }
//        submitResult.passed();
//        if (this.isAndMode()) {
//            raters.stream().filter(r -> r.getEmpId().equals(confirmEmpId.getId())).forEach(Rater::passStatus);
//        } else {//orMode 或签的另一些人员标记完成.
//            raters.forEach(Rater::passStatus);
//            rsMap.getDatas().forEach(EvalScoreResult::passed);
//        }
//
//        this.onStatus();
//    }

    public void acceptPass(EmpId confirmEmpId) {
        List<EvalScoreResult> submitSrs = rsMap.get(confirmEmpId.getId());
        if (CollUtil.isEmpty(submitSrs)) {
            throw new KpiI18NException("10001", "非法操作,您无操作权限");
        }
        if (isAllPassed(submitSrs)) {
            throw new KpiI18NException("10002", "您的操作已提交,无需要重复提交");
        }
        if (this.isAndMode()) {
            raters.stream().filter(r -> r.getEmpId().equals(confirmEmpId.getId())).forEach(Rater::passStatus);
            submitSrs.forEach(EvalScoreResult::passed);
            finishedScorerIds.add(confirmEmpId.getId());
        } else {//orMode 或签的另一些人员标记完成.
            Set<String> orFinishScorerIds = new HashSet<>();
            passedOrRs(submitSrs);
            rsMap.keySet().forEach(scorerId -> {
                if (isAllPassed(rsMap.get(scorerId))) {
                    orFinishScorerIds.add(scorerId);
                }
            });
            raters.stream().filter(rater -> orFinishScorerIds.contains(rater.getEmpId())).forEach(Rater::passStatus);
            finishedScorerIds = new ArrayList<>(orFinishScorerIds);
        }

        this.onStatus();
    }

    private void passedOrRs(List<EvalScoreResult> submitSrs) {
        if (isOnIndAudit()) {
            Set<String> kpiItemIdKey = submitSrs.stream().map(EvalScoreResult::getKpiItemId).collect(Collectors.toSet());
            rsMap.keySet().forEach(scorerId -> {
                List<EvalScoreResult> orSrs = rsMap.get(scorerId);
                orSrs.stream().filter(orSr -> kpiItemIdKey.contains(orSr.getKpiItemId())).forEach(EvalScoreResult::passed);
            });
            return;
        }
        rsMap.keySet().stream().map(scorerId -> rsMap.get(scorerId)).forEachOrdered(orSrs -> orSrs.forEach(EvalScoreResult::passed));
    }

    public boolean isAllPassed(List<EvalScoreResult> srs) {
        return srs.stream().allMatch(EvalScoreResult::isPassed);
    }

    public Boolean isFinished() {
        return StrUtil.equals("finished", status);
    }

    public List<Rater> skipRater() {
        return this.getRaters().stream().filter(rater -> rater.isSkip()).collect(Collectors.toList());
    }

    public List<EvalScoreResult> dispatch(TenantId companyId, String taskId, String taskUserId, String empId, String opEmpId) {
        List<EvalScoreResult> ess = raters.stream().map(rater -> {
            rater.doingStatus();
            EvalScoreResult result = this.createResult(companyId, taskId, taskUserId, empId, rater.getEmpId(), opEmpId,null,null);
            result.setSkipType(rater.getSkipType());
            return result;
        }).collect(Collectors.toList());
        this.onResults(ess);
        return ess;
    }

    public List<EvalScoreResult> dispatchItemAudit(TenantId companyId, String taskId, String taskUserId, String empId, String opEmpId) {
        List<EvalScoreResult> ess = new ArrayList<>();
        if (CollUtil.isEmpty(itemAudits)) {
            return ess;
        }
        ess = itemAudits.stream().map(itemAudit -> createResult(companyId, taskId, taskUserId, empId,
                itemAudit.approverInfo(), opEmpId, itemAudit.getId(),itemAudit.getKpiItemId())).collect(Collectors.toList());
        this.onResults(ess);
        return ess;
    }
    public EvalScoreResult createResult(TenantId companyId, String taskId, String taskUserId, String empId, String scorerId, String opEmpId, String taskAuditId, String kpiItemId) {
        EvalScoreResult result = new EvalScoreResult(companyId, taskId, taskUserId, null, empId, scorerId, this.approvalOrder);
        if (taskAuditId != null){
            id = taskAuditId;
        }
        result.appendAuditor(AuditEnum.CONFIRM_TASK.getScene(), scorerId, multiType, approvalOrder == null ? 1 : approvalOrder, id, modifyFlag);
        result.setCreatedUser(opEmpId);
        result.setUpdatedUser(opEmpId);
        result.setKpiItemId(kpiItemId);
        return result;
    }
}
