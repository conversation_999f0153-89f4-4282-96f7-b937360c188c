package com.polaris.kpi.eval.domain.task.entity.empeval.compute;

import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.entity.*;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
public class ItemComputeForOkrScoreV3 extends BaseItemComputeV3 {
    private BigDecimal okrScore = BigDecimal.ZERO;

    public ItemComputeForOkrScoreV3(EvalKpi kpi, boolean isOpenAvgWeightCompute) {
        super(kpi, isOpenAvgWeightCompute);
    }


    //计算okr分项
    public BigDecimal computeV3Okr(TotalFinalNodeScoreV3 totalOfSum, TypeFinalNodeScoreV3 typeScore,BigDecimal typeWeight,BigDecimal itemWeight) {
        if (kpi.openOkrScore()) {
            this.okrScore = kpi.getItemAutoScore();
            List<ComputedResultScore> scores =computeImportOkrScore();
            if (CollUtil.isEmpty(scores)){
                log.info("scores is null,okrScore:{},kpi:{}",okrScore, JSONUtil.toJsonStr(kpi));
                return BigDecimal.ZERO;
            }
            processComputedResults(scores);
            //计算互评环节的指标得分
            this.v3FinalWeightItemScore.sum();//计算指标环节得分
            kpi.setItemNodeScore();
            kpi.setV3FinalWeightItemScore(this.v3FinalWeightItemScore);
            this.addTypeScoreToTypeAndTotal(totalOfSum, typeScore, typeWeight, itemWeight);//维度环节得分  //累加到环节总分
            totalOfSum.addToOkrSum(this.okrScore);
           // totalOfSum.addToOkrSum(this.okrScore);
         //   typeScore.addToOkrSum(this.okrScore);
         //   this.v3FinalWeightItemScore.addToOkrSum(okrScore);
         //   this.v3FinalWeightItemScore.sum();
        //    kpi.setItemNodeScore();
         //   kpi.setV3FinalWeightItemScore(this.v3FinalWeightItemScore);
        } else {
            ItemComputeForNormalScoreV3 computeItemV3NormalScore = new ItemComputeForNormalScoreV3(kpi, isOpenAvgWeightCompute);
            computeItemV3NormalScore.computeV3Normal(totalOfSum, typeScore,  typeWeight, itemWeight);
        }
        log.info("okrScore:{}", okrScore);
        return this.okrScore;
    }

    private List<ComputedResultScore> computeImportOkrScore() {
        List<ComputedResultScore> scores = new ArrayList<>();
        if (Objects.isNull(kpi.getItemScoreRule())) {
            log.info("kpi.getItemScoreRule() is null");
            return scores;
        }

        if (null == okrScore){
            okrScore = BigDecimal.ZERO;
        }
        EvalItemScoreRule itemScoreRule = kpi.getItemScoreRule();
        if (itemScoreRule.needSelfScore()) {
            BigDecimal nodePecWeight = itemScoreRule.nodePecWeight(SubScoreNodeEnum.SELF_SCORE);
            BigDecimal selfWeight = okrScore.multiply(nodePecWeight);
            ComputedResultScore self = new ComputedResultScore(SubScoreNodeEnum.SELF_SCORE.getScene());
            self.accpScore(selfWeight, okrScore);
            scores.add(self);
        }
        if (itemScoreRule.needAppointScore()) {
            BigDecimal nodePecWeight = itemScoreRule.nodePecWeight(SubScoreNodeEnum.APPOINT_SCORE);
            ComputedResultScore appoint = new ComputedResultScore(SubScoreNodeEnum.APPOINT_SCORE.getScene());
            BigDecimal appointWeight = okrScore.multiply(nodePecWeight);
            appoint.accpScore(appointWeight, okrScore);
            scores.add(appoint);
        }
        if (itemScoreRule.needSubScore()) {
            BigDecimal nodePecWeight = itemScoreRule.nodePecWeight(SubScoreNodeEnum.SUB_SCORE);
            ComputedResultScore sub = new ComputedResultScore(SubScoreNodeEnum.SUB_SCORE.getScene());
            BigDecimal subWeight = okrScore.multiply(nodePecWeight);
            sub.accpScore(subWeight, okrScore);
            scores.add(sub);
        }
        if (itemScoreRule.needSupScore()) {
            BigDecimal nodePecWeight = itemScoreRule.nodePecWeight(SubScoreNodeEnum.SUPERIOR_SCORE);
            ComputedResultScore sup = new ComputedResultScore(SubScoreNodeEnum.SUPERIOR_SCORE.getScene());
            BigDecimal supWeight = okrScore.multiply(nodePecWeight);
            sup.accpScore(supWeight, okrScore);
            scores.add(sup);
        }
        if (itemScoreRule.needPeerScore()) {
            BigDecimal nodePecWeight = itemScoreRule.nodePecWeight(SubScoreNodeEnum.PEER_SCORE);
            ComputedResultScore peer = new ComputedResultScore(SubScoreNodeEnum.PEER_SCORE.getScene());
            BigDecimal peerWeight = okrScore.multiply(nodePecWeight);
            peer.accpScore(peerWeight, okrScore);
            scores.add(peer);
        }
        return scores;
    }
}
