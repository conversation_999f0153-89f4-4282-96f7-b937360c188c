package com.polaris.kpi.eval.domain.confirm.repo;

import com.polaris.kpi.eval.domain.task.entity.EvalAudit;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;

import java.util.List;

public interface ConfirmEvalRuleRepo {
//    void updateEmpEvalRule(EmpEvalMerge rule, KpiListWrap kpiTypes);

    void updateEmpEvalRule(String opEmpId, EmpEvalMerge rule, EvalUser evalUser, List<EvalAudit> onIndAudits);
}
