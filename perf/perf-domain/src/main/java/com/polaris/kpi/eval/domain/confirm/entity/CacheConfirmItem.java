package com.polaris.kpi.eval.domain.confirm.entity;

import com.polaris.kpi.eval.domain.task.entity.EvalKpi;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
public class CacheConfirmItem extends EvalKpi {

//    private BigDecimal itemPlanValue;
//    private String assessRuleRemark;//==itemRule
//    private String indexResult;
//    private String indexRater;
//    private String scoreRule;//==scoringRule
//    private BigDecimal targetVal;
//    private String unit;
//    private BigDecimal weightVal;
//
//    private ConfirmTypeCache.IndexInfo indexInfo;
//    private String valueRequired;

}
