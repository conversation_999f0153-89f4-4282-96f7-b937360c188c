package com.polaris.kpi.eval.domain.task.entity;

import cn.com.polaris.kpi.eval.ScoreEmp;
import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.common.DelableDomain;
import com.polaris.kpi.common.KpiI18NException;
import com.polaris.kpi.eval.domain.task.entity.empeval.BaseScoreResult;
import com.polaris.kpi.eval.domain.task.entity.empeval.PerfEvalTypeResult;
import com.polaris.kpi.eval.domain.task.type.EvalScoreSummary;
import com.polaris.sdk.type.AuditEnum;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.stream.Collectors;

@Setter
@Getter
@NoArgsConstructor
@Slf4j
public class EmpEvalScorer  extends DelableDomain {

    private String id;//
    @JSONField(serialize = false)
    private TenantId companyId;//公司id
    private String taskId;//考核任务id
    private String taskUserId;//员工任务id
    private String empId;//被考核人id
    private String scorerId;//评分人id
    private String scorerName;//评分人姓名
    private String scorerAvatar;//评分人头像
    private Integer status = 0;//0-未完成 1-已完成
    private List<EmpEvalScorerNode> scorerNodes = new ArrayList<>();//评分人评分环节



    public EmpEvalScorer(String companyId, String opEmpId, String taskId, String empId, String taskUserId
            , String scorerId, String scorerName, String scorerAvatar) {
        this.companyId = new TenantId(companyId);
        this.taskId = taskId;
        this.empId = empId;
        this.taskUserId = taskUserId;
        this.scorerId = scorerId;
        this.scorerName = scorerName;
        this.scorerAvatar = scorerAvatar;
        this.createdUser = opEmpId;
        this.updatedUser = opEmpId;
        this.createdTime = new Date();
        this.updatedTime = new Date();
        this.status = 0;//0-未完成
    }


    public boolean selfAlreadyRejected(){
        return this.scorerNodes.stream()
                .anyMatch(EmpEvalScorerNode::isSelfScoreRejected);
    }


    public Set<String> existsFinishedNScorerType(){
        Set<String> finishedScoreTypes = new HashSet<>();
        if (CollUtil.isEmpty(scorerNodes)){
            return finishedScoreTypes;
        }

        scorerNodes.stream().filter(EmpEvalScorerNode::isFinish)
                .filter(scorerNode -> !CollUtil.isEmpty(scorerNode.getScorerNodeKpiTypes()))
                .forEach(scorerNode -> scorerNode.getScorerNodeKpiTypes().forEach(scorerNodeKpiType -> {
            if (scorerNodeKpiType.onlyTypeEval() && scorerNodeKpiType.isScored()) {
                finishedScoreTypes.add(scorerNode.getScorerType());
                return;
            }
            if (CollUtil.isEmpty(scorerNodeKpiType.getScorerNodeKpiItems())) {
                return;
            }
            if (scorerNodeKpiType.getScorerNodeKpiItems().stream().anyMatch(EvalScorerNodeScoreItemBase::isScored)) {
                finishedScoreTypes.add(scorerNode.getScorerType());
            }
        }));
        return finishedScoreTypes;
    }

    public boolean selfFinished(){
        EmpEvalScorerNode selfScoreNode = this.getCurScoreNodeOne(SubScoreNodeEnum.SELF_SCORE.getScene(),1);
        return selfScoreNode.isFinish();
    }

    public void accId(String id) {
        if (Objects.isNull(this.id)) {
            this.id = id;
        }
    }

    public List<EvalScorerNodeKpiItem> listAllKpiItem() {//非维度考核
        List<EvalScorerNodeKpiItem> items = new ArrayList<>();
        for (EmpEvalScorerNode scorerNode : this.scorerNodes) {
            scorerNode.accItemScorerTypeAndOrder();//接收ScoreTypeAndOrder
            List<EvalScorerNodeKpiType> scorerNodeKpiTypes = scorerNode.getScorerNodeKpiTypes();
            List<EvalScorerNodeKpiItem> scorerNodeKpiItems = scorerNodeKpiTypes.stream()
                    .filter(scorerNodeKpiType -> !scorerNodeKpiType.onlyTypeEval())
                    .flatMap(scorerNodeKpiType -> scorerNodeKpiType.getScorerNodeKpiItems().stream())
                    .collect(Collectors.toList());
            items.addAll(scorerNodeKpiItems);
        }
        return items;
    }

    public List<EvalScorerNodeKpiItem> listItemScore(SubScoreNodeEnum node) {
        List<EvalScorerNodeKpiItem> items;
        List<EmpEvalScorerNode> scoreNodes = this. getScoreNodesByScoreType(node.getScene());
        if (CollUtil.isEmpty(scoreNodes)){
            return new ArrayList<>();
        }
        items = scoreNodes.stream()
                .filter(EmpEvalScorerNode::isComputeNode)
                .flatMap(scorerNode -> scorerNode.getAllKpiItems().stream()).collect(Collectors.toList());
        return items;
    }
    public boolean allScorePassed() {
        if (CollUtil.isEmpty(scorerNodes)){
            return true;
        }

        return this.scorerNodes.stream()
                .filter(scorerNode -> scorerNode.isSkipHiddenType()
                                    || scorerNode.isTransferType()
                                    || scorerNode.noHavDoTransfer()).allMatch(EmpEvalScorerNode::isPassed);
    }

    /**
     * 筛选出待转交的评分节点
     * @return 待转交的评分节点列表
     */
    public List<EmpEvalScorerNode> listToForTransfer() {
        List<EmpEvalScorerNode> waitSubmitScorerNodes =  this.listWaitSubmitScoreNode();
        this.validateWaitTransferNodes(waitSubmitScorerNodes);
        return waitSubmitScorerNodes.stream()
                .map(EmpEvalScorerNode::clone)
                .collect(Collectors.toList());
    }

    /**
     * 复制已完成节点数据到待转交节点
     * @return 需自动提交的节点列表
     */
    public List<EmpEvalScorerNode> copyFinishedDataForTransferNode() {
        List<EmpEvalScorerNode> sysAutoSubmitSn = new ArrayList<>();
        List<EmpEvalScorerNode> waitSubmitScoreNodes = this.listWaitSubmitScoreNode();//待提交的节点
        if (CollUtil.isEmpty(waitSubmitScoreNodes)) {
            return sysAutoSubmitSn;
        }

        Set<String> nodeIds = new HashSet<>();
        Set<String> scoreTypes = waitSubmitScoreNodes.stream().map(EmpEvalScorerNode::getScorerType).collect(Collectors.toSet());
        for (String scoreType : scoreTypes) {
            //获取已提交的环节层级
            List<EmpEvalScorerNode> finishedScoreNodes = this.getFinishedScoreNodesByScoreType(scoreType);
            if (CollUtil.isEmpty(finishedScoreNodes)) {
                continue;
            }

            boolean existsTotalLevelWaitSubmit = existsTotalLevelWaitSubmit(scoreType);
            //校验是否还存在未评价的指标
            waitSubmitScoreNodes.forEach(waitSubmit -> {
                //复制已提交的节点数据
                finishedScoreNodes.stream().filter(finishedScoreNode -> StrUtil.equals(waitSubmit.getScorerType(), finishedScoreNode.getScorerType())).forEachOrdered(waitSubmit::copyOtherNodeData);
                boolean allSubmitKpiItem = waitSubmit.allSubmitKpiItem();
                if (!allSubmitKpiItem || existsTotalLevelWaitSubmit) {
                    return;
                }
                nodeIds.add(waitSubmit.getId());
                sysAutoSubmitSn.add(waitSubmit);
            });
        }

        //处理重置后，需被自动跳过的节点
        waitSubmitScoreNodes.stream()
                .filter(waitSubmit -> !nodeIds.contains(waitSubmit.getId()) && waitSubmit.isSkipHiddenType())
                .forEach(sysAutoSubmitSn::add);
        return sysAutoSubmitSn;
    }

    public void copyFinishedDataForTransferNode( List<EmpEvalScorerNode> waitSubmitScoreNodes) {
        if (CollUtil.isEmpty(waitSubmitScoreNodes)) {
            log.warn("接收者接收转交的节点,waitSubmitScoreNodes为空！！！");
            return;
        }

        Set<String> scoreTypes = waitSubmitScoreNodes.stream().map(EmpEvalScorerNode::getScorerType).collect(Collectors.toSet());
        for (String scoreType : scoreTypes) {
            //获取已提交的环节层级
            List<EmpEvalScorerNode> finishedScoreNodes = this.getFinishedScoreNodesByScoreType(scoreType);
            if (CollUtil.isEmpty(finishedScoreNodes)) {
                log.info("接收者接收转交的节点,在接受者已有的节点未存已提交完成的节点！！！");
                continue;
            }
            //校验是否还存在未评价的指标
            //复制已提交的节点数据
            waitSubmitScoreNodes.forEach(waitSubmit ->
                    finishedScoreNodes.stream()
                            .filter(finishedScoreNode -> StrUtil.equals(waitSubmit.getScorerType(), finishedScoreNode.getScorerType()))
                            .forEachOrdered(waitSubmit::copyOtherNodeData));
            log.info("接收者接收转交的节点,在接受者已有的节点复制已提交的节点指标数据！！！");
        }
    }

    public void toForTransfer(String transferTo, List<EmpEvalScorerNode> waitTransferNodes) {
        ListWrap<EmpEvalScorerNode> waitTransferNodesWrap = new ListWrap<>(waitTransferNodes).asMap(EmpEvalScorerNode::getId);
        for (EmpEvalScorerNode scorerNode : scorerNodes) {
            EmpEvalScorerNode w = waitTransferNodesWrap.mapGet(scorerNode.getId());
            if (Objects.isNull(w)) {
                continue;
            }
            scorerNode.markAsNormalTransfer(transferTo); // 正常转交
        }
    }

    /**
     * 接收者接收转交的节点
     * @param transferNodes 转交的节点列表
     * @return 接收的节点列表
     */
    // 转入者行为 接收转出者(正常转交来的)
    public List<EmpEvalScorerNode> receiveNodesForTransferV3(List<EmpEvalScorerNode> transferNodes) {
        List<EmpEvalScorerNode> waitSubmitScoreNodes = new ArrayList<>();
        for (EmpEvalScorerNode transferNode : transferNodes) {
            EmpEvalScorerNode waitSubmit = copyNodeForTransfer(transferNode);
            waitSubmitScoreNodes.add(waitSubmit);
        }
        log.info("【转交】接收者接收转交的节点,waitSubmitScoreNodes:{},transferNodes.size:{}", JSONUtil.toJsonStr(waitSubmitScoreNodes),transferNodes.size());
        copyFinishedDataForTransferNode(waitSubmitScoreNodes);//复制已完成的数据
        this.upScorerStatus();
        return new ArrayList<>(waitSubmitScoreNodes);
    }

    /**
     * 标记节点为跳过 且记录transferTo转给了谁
     * @param waitTransferNodes 待转交的节点列表
     * @param orderReceiveIdsMap 接收者ID映射
     * @return 需要提交的节点列表
     */
    public List<EmpEvalScorerNode> markSkipForSN(List<EmpEvalScorerNode> waitTransferNodes,Map<Integer,List<String>> orderReceiveIdsMap) {
        if (CollUtil.isEmpty(waitTransferNodes)) {
            return new ArrayList<>();
        }
        List<EmpEvalScorerNode> newSubmitNodes = new ArrayList<>();
        ListWrap<EmpEvalScorerNode> waitTransferNodesWrap = new ListWrap<>(waitTransferNodes).asMap(EmpEvalScorerNode::getId);
        for (EmpEvalScorerNode scorerNode : scorerNodes) {
            EmpEvalScorerNode w = waitTransferNodesWrap.mapGet(scorerNode.getId());
            if (Objects.isNull(w)){
                continue;
            }
            List<String> transferTos = orderReceiveIdsMap.get(w.getApprovalOrder());
            log.info(" markSkipForSN:transferTos:{}",transferTos);
            scorerNode.setSkipTransferReceivers(orderReceiveIdsMap.get(w.getApprovalOrder()));
            newSubmitNodes.add(scorerNode);
        }
        this.upScorerStatus();
        return newSubmitNodes;
    }

    /**
     * 筛选出待跳过的节点
     * @param skipScoreType 跳过的评分类型
     * @return 待跳过的节点列表
     */
    // 转出者行为
    // transferToIf
    public List<EmpEvalScorerNode> listSNForSkip(String skipScoreType) {
        //2.找到所有节点    //筛选skipScorerId 环节是skipScoreType 的 sorerNode [sorerNode 存在多个]
        List<EmpEvalScorerNode> waitTransferNodes =  this.filterScorerNode(skipScoreType);
        if (CollUtil.isEmpty(waitTransferNodes)){
            return new ArrayList<>();
        }
        List<EmpEvalScorerNode> cloneNodes = new ArrayList<>();
        waitTransferNodes.forEach(scorerNode -> {
            scorerNode.markAsSkipTransfer(); // up transferStatus = 2 跳过类型转交 已转交
            cloneNodes.add(scorerNode.clone());//防止 被其他场景改变 所以使用 clone
        });
        return  cloneNodes;
    }

    /**
     * 接收者接收跳过的节点
     * @param transferNodes 转交的节点列表
     * @return 接收的节点列表
     */
    // 转入者行为
    public List<EmpEvalScorerNode> receiveNodesForSkip(List<EmpEvalScorerNode> transferNodes) {
        List<EmpEvalScorerNode> waitSubmitScoreNodes = new ArrayList<>();
        for (EmpEvalScorerNode transferNode : transferNodes) {
            // 如果是或签无需处理
            if (transferNode.isOrMode()) {
                log.info("【跳过】评分人[{}]跳过节点为或签模式，无需处理：scorerType={}, order={}, taskUserId={}", this.scorerId, transferNode.getScorerType(), transferNode.getApprovalOrder(), this.taskUserId);
                continue;
            }

            EmpEvalScorerNode copyNode = copySkipNode(transferNode);//复制
            this.scorerNodes.add(copyNode);
            waitSubmitScoreNodes.add(copyNode);
        }
        log.info("【跳过】接收者接收转交的节点,waitSubmitScoreNodes:{},transferNodes.size:{}", JSONUtil.toJsonStr(waitSubmitScoreNodes), transferNodes.size());
        copyFinishedDataForTransferNode(waitSubmitScoreNodes);//复制已完成的数据
        this.upScorerStatus();
        return waitSubmitScoreNodes;
    }



    private void validateWaitTransferNodes(List<EmpEvalScorerNode> waitTransferNodes){
        if (CollUtil.isEmpty(waitTransferNodes)){
            throw new KpiI18NException("node.nodeIsNull", "无跳过环节记录");
        }

        boolean allPassed = waitTransferNodes.stream().allMatch(EmpEvalScorerNode::isPassed);
        if (allPassed) {
            throw new KpiI18NException("node.nodeIsNull", "无可转交环节记录");
        }
    }

    /**
     * 复制节点用于转交
     * @param transferNode 转交的节点
     * @return 复制的节点
     */
    private EmpEvalScorerNode copyNodeForTransfer(EmpEvalScorerNode transferNode){
        //未评 直接复制转交节点
        EmpEvalScorerNode copyNode = copyTransferNode(transferNode);//复制
        this.scorerNodes.add(copyNode);
        return copyNode;
    }

    /**
     * 复制转交节点
     * @param transferNode 转交的节点
     * @return 复制的节点
     */
    private EmpEvalScorerNode copyTransferNode(EmpEvalScorerNode transferNode){
        EmpEvalScorerNode copyNode = transferNode.clone();//复制
        copyNode.accScorerInfo(this.scorerId,this.scorerName,this.scorerAvatar);
        copyNode.receiveFromForTransfer(transferNode.getScorerId());
        copyNode.initIdNull();//db需要重新生成
        return copyNode;
    }

    /**
     * 复制跳过节点
     * @param transferNode 转交的节点
     * @return 复制的节点
     */
    private EmpEvalScorerNode copySkipNode(EmpEvalScorerNode transferNode){
        EmpEvalScorerNode copyNode = transferNode.clone();//复制
        copyNode.accScorerInfo(this.scorerId,this.scorerName,this.scorerAvatar);
        copyNode.receiveFromForSkip(transferNode.getScorerId());
        copyNode.initIdNull();//db需要重新生成
        return copyNode;
    }



    //todo 筛选可跳过/转交的行为
    public List<EmpEvalScorerNode> filterScorerNode(String scoreType) {
        return waitSubmitScoreNodesByScoreType(scoreType);
    }

    public void addScorerNode(EmpEvalScorerNode scorerNode) {
        Optional<EmpEvalScorerNode> existingNode = this.scorerNodes.stream()
                .filter(sn -> sn.asOrderAndScorerTypeKey().equals(scorerNode.asOrderAndScorerTypeKey()))
                .findFirst();

        if (!existingNode.isPresent()) {//existingNode 不存在
            this.scorerNodes.add(scorerNode);
            return;
        }

        existingNode.get().acceptSignatureFlag(scorerNode.isSignatureFlag());//接收签名

        List<EvalScorerNodeKpiType> newKpiTypes = scorerNode.getScorerNodeKpiTypes();
        if (newKpiTypes == null) {
            newKpiTypes = new ArrayList<>();
        }
        // 合并相同环节的指标并去重
        existingNode.get().mergeAndRemoveDuplicates(newKpiTypes);
    }

    public boolean dispatchByScoreType(String scoreType, Integer order) {
        if (CollUtil.isEmpty(scorerNodes)) {
            return false;
        }
        //待提交 -== 已分发
        //需要兼容转交或者跳过后的数据
        //跳过后的节点
        boolean isDispatch = dispatchScoreNode(scoreType, order);

        if (StrUtil.equals(scoreType, SubScoreNodeEnum.TOTAL_LEVEL.getScene())
                || existsWaitDispatchNoContainTotalLevel()
                || !haveTotalLevel()) {
            return isDispatch;
        }
        //不存在非总等级的环节， 且有总等级需要评价 还需要将总等级一起分发
        dispatchScoreNodeForTotalLevel();
        return isDispatch;
    }

    private void dispatchScoreNodeForTotalLevel() {
        Integer order = 1;
        String scoreType = SubScoreNodeEnum.TOTAL_LEVEL.getScene();
        dispatchScoreNode(scoreType, order);
    }

    private boolean dispatchScoreNode(String scoreType, Integer order) {
        boolean isDispatch = false;
        //待提交 -== 已分发
        //需要兼容转交或者跳过后的数据
        //跳过后的节点
        for (EmpEvalScorerNode scorerNode : scorerNodes) {
            if (scorerNode.isCanDispatch(scoreType, order) && scorerNode.isWaitDispatch()) {
                if (StrUtil.isBlank(scorerNode.getTransferType()) //未转交和跳过的正常数据
                        || scorerNode.isTransferType() //转交后的节点
                        || scorerNode.isSkipType()
                        || scorerNode.isSkipHiddenType()) {
                    scorerNode.dispatch();
                    isDispatch = true;
                }
            }
        }
        return isDispatch;
    }

    public boolean existsWaitDispatchNoContainTotalLevel() {
        if (CollUtil.isEmpty(scorerNodes)) {
            return false;
        }
        return scorerNodes.stream().anyMatch(node -> node.isWaitDispatch() && !SubScoreNodeEnum.isTotalLevelScore(node.getScorerType()));
    }

    public boolean existsWaitSubmit() {
        if (CollUtil.isEmpty(scorerNodes)) {
            return true;
        }
        for (EmpEvalScorerNode scorerNode : scorerNodes) {
            if (scorerNode.needExcludeNode()) {
                continue;
            }
            if (scorerNode.isWaitSubmit()) {
                return true;
            }
        }
        return false;
    }

    public boolean haveTotalLevel() {
        if (CollUtil.isEmpty(scorerNodes)) { //存在有总等级待分发
            return false;
        }
        return scorerNodes.stream().anyMatch(node -> node.isWaitDispatch() && SubScoreNodeEnum.isTotalLevelScore(node.getScorerType()));
    }

    public boolean resetScoreTotalLevel() {
        //如果是打总等级则重置为待分发
        if (CollUtil.isEmpty(scorerNodes)) {
            return false;
        }

        boolean isReset = false;
        for (EmpEvalScorerNode node : scorerNodes) {
            if (SubScoreNodeEnum.isTotalLevelScore(node.getScorerType())) {
                isReset = node.resetScoreTotalLevel();
            }
        }
        return isReset;
    }
    public void accpTotalRs(BaseScoreResult total) {
        if (Objects.isNull(total)) {
            return;
        }
        List<EmpEvalScorerNode> scorerNode = this.getCurScoreNode(SubScoreNodeEnum.TOTAL_LEVEL.getScene(), 1);
        if (CollUtil.isNotEmpty(scorerNode)) {
            scorerNode.forEach(node -> node.accpScorerTotalScoreData(total));
        }
    }


    public void accpSummary(EvalScoreSummary summary) {
        if (Objects.isNull(summary)) {
            return;
        }
        List<EmpEvalScorerNode> nodes = this.getScoreNodesByScoreType(summary.getScoreType());
        if (CollUtil.isNotEmpty(nodes)) {
            nodes.forEach(scorerNode -> scorerNode.accpScorerSummary(summary));
        }
    }

    public void accpTypeRs(ListWrap<PerfEvalTypeResult> typeScoreType) {
        if (typeScoreType.isEmpty()) {
            return;
        }
        for (EmpEvalScorerNode node : scorerNodes) {
            List<PerfEvalTypeResult> list = typeScoreType.groupGet(node.asOrderAndScorerTypeKey());
            if (CollUtil.isEmpty(list)){
                continue;
            }
            node.accpTypeScoredData(list);
        }
    }

    public void accpScoreRs(ListWrap<EvalScoreResult> scoreResultWrap) {
        if (scoreResultWrap.isEmpty()) {
            return;
        }

        List<EvalScoreResult> totalScoreRs = new ArrayList<>();
        List<EvalScoreResult> itemScoreRs = new ArrayList<>();
        for(EvalScoreResult scoreResult : scoreResultWrap.getDatas()){
            if (AuditEnum.totalScenes().contains(scoreResult.getScorerType())){
                totalScoreRs.add(scoreResult);
                continue;
            }
            itemScoreRs.add(scoreResult);
        }

        ListWrap<EvalScoreResult> itemScoreRsGroup = new ListWrap<>(itemScoreRs).groupBy(EvalScoreResult::asOrderAndScoreTypeKey);
        ListWrap<EvalScoreResult> totalScoreRsGroup = new ListWrap<>(totalScoreRs).asMap(EvalScoreResult::getScorerType);
        for (EmpEvalScorerNode node : scorerNodes) {
            String scene = node.getTotalScene();
            if (node.nodesIsTotalScore() && StrUtil.isNotBlank(scene)) {
                EvalScoreResult totalScore = totalScoreRsGroup.mapGet(scene);
                node.accpScorerTotalScore(totalScore);//接收打总分，及评语数据
            }
            List<EvalScoreResult> list = itemScoreRsGroup.groupGet(node.asOrderAndScorerTypeKey());
            if (CollUtil.isEmpty(list)) {
                continue;
            }
            node.accpScoredData(list);//接收指标评分数据
        }
    }


    public List<EmpEvalScorerNode> listDispatchNode(Set<String> dispatchScoreTypes) {
        List<EmpEvalScorerNode> dispatchNodes = new ArrayList<>();
        if (CollUtil.isEmpty(scorerNodes)) {
            return dispatchNodes;
        }
        for (EmpEvalScorerNode node : scorerNodes) {
            if (dispatchScoreTypes.contains(node.getScorerType()) && node.isDispatch()) {
                dispatchNodes.add(node);
            }
        }
        return dispatchNodes;
    }

    public void deleteScoreNode(String scorerNodeId) {
        if (CollUtil.isEmpty(scorerNodes)) {
            return;
        }
        scorerNodes.removeIf(scorerNode -> StrUtil.equals(scorerNode.getId(), scorerNodeId));
    }

    public Set<String> listWaitSubmitScoreType() {
        Set<String> scoreTypes = new HashSet<>();
        if (CollUtil.isEmpty(scorerNodes)) {
            return scoreTypes;
        }

        for (EmpEvalScorerNode scorerNode : scorerNodes) {
            if (scorerNode.isWaitSubmit()) {
                scoreTypes.add(scorerNode.getScorerType());
            }
        }
        return scoreTypes;
    }

    public List<EvalScorerNodeKpiType> getTypeScoreNode() {//维度考核
        List<EvalScorerNodeKpiType> waitSubmitTypes = new ArrayList<>();
        if (CollUtil.isEmpty(scorerNodes)) {
            return waitSubmitTypes;
        }

        for (EmpEvalScorerNode scorerNode : scorerNodes) {
            waitSubmitTypes.addAll(scorerNode.listNeedTypeEval());
        }
        return waitSubmitTypes;
    }

    public EmpEvalScorerNode getTotalScoreNode() {
        if (CollUtil.isEmpty(scorerNodes)) {
            return null;
        }

        return scorerNodes.stream()
                .filter(scorerNode -> SubScoreNodeEnum.isTotalLevelScore(scorerNode.getScorerType())).findFirst().orElse(null);
    }
    public List<EmpEvalScorerNode> listWaitSubmitScoreNode() {
        List<EmpEvalScorerNode> nodes = new ArrayList<>();
        if (CollUtil.isEmpty(scorerNodes)) {
            return nodes;
        }

        return scorerNodes.stream().filter(EmpEvalScorerNode::isWaitSubmit).collect(Collectors.toList());
    }

    public List<EmpEvalScorerNode> listSubmitedScoreNode() {
        if (CollUtil.isEmpty(scorerNodes)) {
            return new ArrayList<>();
        }
        //重复提交的,筛选已提交的
        return scorerNodes.stream().filter(scorerNode -> scorerNode.isFinish() && scorerNode.isComputeNode()).collect(Collectors.toList());
    }


    public EmpEvalScorerNode curWaitSubmitScoreNode() {
        if (CollUtil.isEmpty(scorerNodes)) {
            return null;
        }

        return scorerNodes.stream().filter(EmpEvalScorerNode::isWaitSubmit).findFirst().orElse(null);
    }

    public boolean isScoreFinished(String scoreType) {
        if (CollUtil.isEmpty(scorerNodes)) {
            return false;
        }
        for (EmpEvalScorerNode scorerNode : scorerNodes) {
            if (StrUtil.equals(scorerNode.getScorerType(), scoreType) && scorerNode.isFinish()) {
                return true;
            }
        }
        return false;
    }

    public Set<String> allScoreType() {
        Set<String> scoreTypes = new HashSet<>();
        if (CollUtil.isEmpty(scorerNodes)) {
            return scoreTypes;
        }

        for (EmpEvalScorerNode scorerNode : scorerNodes) {
            scoreTypes.add(scorerNode.getScorerType());
        }
        return scoreTypes;
    }

    public boolean isDispatched(SubScoreNodeEnum node, int nodeOrder) {
        if (CollUtil.isEmpty(scorerNodes)) {
            return true;
        }
        boolean isDispatched = false;
        for (EmpEvalScorerNode scorerNode : scorerNodes) {
            if (StrUtil.equals(scorerNode.getScorerType(), node.getScene())
                    && Objects.equals(scorerNode.getApprovalOrder(), nodeOrder)
                    && scorerNode.isDispatched()) {
                isDispatched = true;
                break;
            }
        }
        return isDispatched;
    }

    public EmpEvalScorerNode getCurScoreNodeOne(String scoreType, Integer order) {
        if (CollUtil.isEmpty(scorerNodes)) {
            return null;
        }
        return scorerNodes.stream().filter(scorerNode -> StrUtil.equals(scorerNode.getScorerType(), scoreType) && Objects.equals(scorerNode.getApprovalOrder(), order)).findFirst().orElse(null);
    }


    public List<EmpEvalScorerNode> getCurScoreNode(String scoreType, Integer order) {
        List<EmpEvalScorerNode> curScoreNodes = new ArrayList<>();
        if (CollUtil.isEmpty(scorerNodes)) {
            return curScoreNodes;
        }
        for (EmpEvalScorerNode scorerNode : scorerNodes) {
            if (StrUtil.equals(scorerNode.getScorerType(), scoreType) && Objects.equals(scorerNode.getApprovalOrder(), order)) {
                curScoreNodes.add(scorerNode);
            }
        }
        return curScoreNodes;
    }

    public List<EmpEvalScorerNode> listCurScoreNodeByScorerType(String scoreType) {
        List<EmpEvalScorerNode> curScoreNodes = new ArrayList<>();
        if (CollUtil.isEmpty(scorerNodes)) {
            return curScoreNodes;
        }
        curScoreNodes = scorerNodes.stream().filter(node -> node.isComputeNode() && StrUtil.equals(node.getScorerType(), scoreType)).collect(Collectors.toList());
        return curScoreNodes;
    }

    public EmpEvalScorerNode getWaitSubmitTotalLevelScoreNode() {
        if (CollUtil.isEmpty(scorerNodes)) {
            return null;
        }
        for (EmpEvalScorerNode scorerNode : scorerNodes) {
            if (SubScoreNodeEnum.isTotalLevelScore(scorerNode.getScorerType()) && scorerNode.isWaitSubmit()) {
                return scorerNode;
            }
        }
        return null;
    }

    public EmpEvalScorerNode getCurCopyScoreNode(String scoreType, Integer order,String fromScorerId) {
        if (CollUtil.isEmpty(scorerNodes)) {
            return null;
        }
        return scorerNodes.stream().filter(scorerNode ->
                StrUtil.equals(scorerNode.getScorerType(), scoreType)
                && StrUtil.equals(fromScorerId, scorerNode.getTransferFrom())
                && Objects.equals(scorerNode.getApprovalOrder(), order)).findFirst().orElse(null);
    }


    public List<EmpEvalScorerNode> getFinishedScoreNodesByScoreType(String scoreType) {
        if (CollUtil.isEmpty(scorerNodes)) {
            return null;
        }
        List<EmpEvalScorerNode> nodes = new ArrayList<>();
        for (EmpEvalScorerNode node : scorerNodes) {
            if (node.isComputeNode() && StrUtil.equals(node.getScorerType(), scoreType) && node.isFinish()) {
                nodes.add(node);
            }
        }
        return nodes;
    }

    public List<EmpEvalScorerNode> waitSubmitScoreNodesByScoreType(String scoreType) {
        if (CollUtil.isEmpty(scorerNodes)) {
            return null;
        }
        List<EmpEvalScorerNode> nodes = new ArrayList<>();
        for (EmpEvalScorerNode node : scorerNodes) {
            if (node.isComputeNode() && StrUtil.equals(node.getScorerType(), scoreType) && node.isWaitSubmit()) {
                nodes.add(node);
            }
        }
        return nodes;
    }

    public List<EmpEvalScorerNode> listSNExcludeTotalLevel() {
        List<EmpEvalScorerNode> nodes = new ArrayList<>();
        if (CollUtil.isEmpty(scorerNodes)) {
            return nodes;
        }
        return this.scorerNodes.stream()
                .filter(scorerNode -> !SubScoreNodeEnum.isTotalLevelScore(scorerNode.getScorerType()) && scorerNode.isFlowNode())
                .collect(Collectors.toList());
    }

    public List<EmpEvalScorerNode> listTotalScoreNode() {
        if (CollUtil.isEmpty(scorerNodes)) {
            return null;
        }
        return scorerNodes.stream()
                .filter(node -> node.isFlowNode() && node.nodesIsTotalScore())
                .collect(Collectors.toList());
    }
    public List<EmpEvalScorerNode> getScoreNodesByScoreType(String scoreType) {
        if (CollUtil.isEmpty(scorerNodes)) {
            return null;
        }
        List<EmpEvalScorerNode> nodes = new ArrayList<>();
        for (EmpEvalScorerNode node : scorerNodes) {
            if (StrUtil.equals(node.getScorerType(), scoreType)) {
                nodes.add(node);
            }
        }
        return nodes;
    }

    public void setScorerInfo(String scorerName, String scorerAvatar) {
        this.scorerName = scorerName;
        this.scorerAvatar = scorerAvatar;
        if (CollUtil.isEmpty(scorerNodes)) {
            return;
        }
        scorerNodes.forEach(scorerNode -> scorerNode.setScorerInfo(scorerName, scorerAvatar));
    }

    public void accScorerInfo(String scorerId,String scorerName, String scorerAvatar) {
        this.scorerName = scorerName;
        this.scorerAvatar = scorerAvatar;
        if (CollUtil.isEmpty(scorerNodes)) {
            return;
        }
        scorerNodes.forEach(scorerNode -> scorerNode.accScorerInfo(scorerId,scorerName, scorerAvatar));
    }
    public Set<String> getScoreTypes() {
        Set<String> scoreTypes = new HashSet<>();
        if (CollUtil.isEmpty(scorerNodes)) {
            return scoreTypes;
        }
        return scorerNodes.stream().map(EmpEvalScorerNode::getScorerType).collect(Collectors.toSet());
    }

    //更新环节评分状态，评分人状态
    public void upStatus(String scoreType, Integer order, Integer status) {
        if (CollUtil.isEmpty(scorerNodes)) {
            return;
        }
        scorerNodes.forEach(scorerNode -> {
            if (StrUtil.equals(scorerNode.getScorerType(), scoreType) && Objects.equals(scorerNode.getApprovalOrder(), order)) {
                scorerNode.upStatus(status);
            }
        });
        upScorerStatus();
    }

    //重置 个人
    public boolean resetScoreNode(ScoreEmp scoreEmp) {
        boolean isReset = false;
        for (EmpEvalScorerNode scorerNode : scorerNodes) {
            if (scorerNode.isComputeNode() && scorerNode.isFinish() && scorerNode.needResetScoreNode(scoreEmp)) {
                scorerNode.waitSubmit();
                isReset = true;
            }
        }
        upScorerStatus();
        return isReset;
    }


    //重置 个人 驳回状态
    public boolean rejectScoreNode(ScoreEmp scoreEmp) {
        boolean isReset = false;
        for (EmpEvalScorerNode scorerNode : scorerNodes) {
            if (scorerNode.isComputeNode() && scorerNode.isFinish() && scorerNode.needResetScoreNode(scoreEmp)) {
                scorerNode.upStatusReject();
                isReset = true;
            }
        }
        upScorerStatus();
        return isReset;
    }



    //重置环节
    public void resetScoreNodeForWaitDispatch() {
        for (EmpEvalScorerNode scorerNode : scorerNodes) {
            if (scorerNode.noValidNode()){
                continue;
            }
            scorerNode.resetScoreNodeForWaitDispatch();
        }
        initStatus();
    }


    public boolean existsTotalLevelWaitSubmit(String scoreType) {
        if (CollUtil.isEmpty(scorerNodes)) {
            return false;
        }
        for (EmpEvalScorerNode scorerNode : scorerNodes) {
            //当前提交的环节非总等级环节，且存在打总等级待提交
            if (!StrUtil.equals(scoreType, SubScoreNodeEnum.TOTAL_LEVEL.getScene())
                    && scorerNode.isWaitSubmit()
                    && SubScoreNodeEnum.isTotalLevelScore(scorerNode.getScorerType())) {
                return true;
            }
        }
        return false;
    }

    public boolean markOrModeScoreNs(String scoreType, Integer order, Set<String> submittedKpiTypeIds, Set<String> submittedKpiItemKeys) {
        boolean marked = false;
        for (EmpEvalScorerNode scorerNode : scorerNodes) {
            if (!(scorerNode.isFlowNode() && StrUtil.equals(scorerNode.getScorerType(), scoreType) && Objects.equals(scorerNode.getApprovalOrder(), order))) {
                continue;
            }
            // 标记匹配范围内的维度/指标为通过
            if (CollUtil.isNotEmpty(scorerNode.getScorerNodeKpiTypes())) {
                for (EvalScorerNodeKpiType type : scorerNode.getScorerNodeKpiTypes()) {
                    boolean typeInScope = submittedKpiTypeIds != null && submittedKpiTypeIds.contains(type.getKpiTypeId());
                    if (!typeInScope) {
                        continue;
                    }
                    if (type.onlyTypeEval()) {
                        type.passed();
                        marked = true;
                        continue;
                    }
                    if (CollUtil.isEmpty(type.getScorerNodeKpiItems())) {
                        continue;
                    }
                    for (EvalScorerNodeKpiItem item : type.getScorerNodeKpiItems()) {
                        boolean itemInScope = submittedKpiItemKeys != null && submittedKpiItemKeys.contains(item.asKpiItemKey());
                        if (itemInScope) {
                            item.passed();
                            marked = true;
                        }
                    }
                }
            }
            // 若该节点下所有应提交的内容均已通过，则将节点置为通过
            boolean allPassed = true;
            if (CollUtil.isNotEmpty(scorerNode.getScorerNodeKpiTypes())) {
                outer:
                for (EvalScorerNodeKpiType type : scorerNode.getScorerNodeKpiTypes()) {
                    if (type.onlyTypeEval()) {
                        if (!type.isPassed()) { allPassed = false; break; }
                        continue;
                    }
                    if (CollUtil.isEmpty(type.getScorerNodeKpiItems())) {
                        continue;
                    }
                    for (EvalScorerNodeKpiItem item : type.getScorerNodeKpiItems()) {
                        if (!item.isPassed()) { allPassed = false; break outer; }
                    }
                }
            }
            if (allPassed) {
                scorerNode.markOrPassed();
            }
        }
        return marked;
    }



    public void initStatus() {
        this.status = 0;// 未完成
    }
    public void upScorerStatus() {
        if (isAllFinish()) {
            this.status = 1; //如果全部完成，则更新当前评分人状态 = 完成
        } else {
            this.status = 0;//如果未完成，更新当前评分人状态 = 未完成
        }
    }
    public boolean isAllFinish() {
        return CollUtil.isEmpty(scorerNodes) || scorerNodes.stream().allMatch(EmpEvalScorerNode::isFinish);
    }


    public boolean dispatchAllFinished() {
        if (CollUtil.isEmpty(scorerNodes)) {
            return true;
        }
        //过滤出已分发的node
        List<EmpEvalScorerNode> dispatchedNodes = new ArrayList<>();
        for (EmpEvalScorerNode scorerNode : scorerNodes) {
            if (scorerNode.isComputeNode() && scorerNode.isDispatched() && scorerNode.isWaitSubmit()) {//已分发，存在待提交的
                dispatchedNodes.add(scorerNode);
            }
        }
        return CollUtil.isEmpty(dispatchedNodes);
    }



    public boolean isFinished() {
        return status == 1;
    }

    public boolean nodeIsFinished(SubScoreNodeEnum node, int nodeOrder) {
        for (EmpEvalScorerNode scorerNode : this.scorerNodes) {
            if(scorerNode.isExistsNoFinish(node, nodeOrder)){
                // 如果节点匹配且未完成，则返回false
                //1.未做过转交 2.做过转交的数据 3.跳过的节点数据
                if (scorerNode.noHavDoTransfer() || scorerNode.isTransferType() || scorerNode.isSkipFlowType()){
                    return false;
                }
            }
        }
        return true;
    }

    public boolean nodeIsFinishedNoContainTotalLevel() {
        for (EmpEvalScorerNode scorerNode : this.scorerNodes) {
            if (SubScoreNodeEnum.isTotalLevelScore(scorerNode.getScorerType())) {
                continue;
            }
            if (!scorerNode.isFinish()) {
                // 如果节点匹配且未完成，则返回false
                //1.未做过转交 2.做过转交的数据 3.跳过的节点数据
                if (scorerNode.noHavDoTransfer() || scorerNode.isTransferType() || scorerNode.isSkipFlowType()) {
                    return false;
                }
            }
        }
        return true;
    }
    public boolean isOrMode() {
        return scorerNodes.stream().anyMatch(EmpEvalScorerNode::isOrMode);
    }
    public boolean match(SubScoreNodeEnum node, int order) {
        return scorerNodes.stream().anyMatch(scorerNode -> scorerNode.match(node, order));
    }
    public void addOrUpdateScorerNode(EmpEvalScorerNode scorerNode) {
        if (CollUtil.isEmpty(scorerNodes)) {
            scorerNodes = new ArrayList<>();
            scorerNodes.add(scorerNode);
            return;
        }
        scorerNodes.forEach(node -> {
            if (Objects.equals(node.getScorerType(), scorerNode.getScorerType()) && Objects.equals(node.getApprovalOrder(), scorerNode.getApprovalOrder())) {
                node.setScorerNodeKpiTypes(scorerNode.getScorerNodeKpiTypes());
            }else{
                scorerNodes.add(scorerNode);
            }
        });
    }
}

