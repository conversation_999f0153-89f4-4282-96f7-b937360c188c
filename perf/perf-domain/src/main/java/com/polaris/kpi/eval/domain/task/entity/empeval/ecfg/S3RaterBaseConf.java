package com.polaris.kpi.eval.domain.task.entity.empeval.ecfg;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.eval.ChangeInfo;
import cn.com.polaris.kpi.eval.Pecent;
import cn.com.polaris.kpi.eval.Rater;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.dmsvc.EvalChangeDmSvc;
import com.polaris.kpi.eval.domain.task.entity.EvalAudit;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.domain.task.entity.empeval
 * @Author: lufei
 * @CreateTime: 2022-09-01  10:47
 * @Description: 360及简易/评分环节设置
 * @Version: 1.0
 */
@Setter
@Getter
@Slf4j
public class S3RaterBaseConf extends RaterNodeConf {
    //private String mutualScoreViewRule;   //互评人查看评分规则JSON 统一转到viewRule去
    protected List<BaseAuditNode> auditNodes = new ArrayList<>();
    //@JSONField(serialize = false)
    //protected String scene;

    private Integer nodeVacancyFlag = null;  //是否处理直属主管空缺 null ==处理， (1-提示异常，需手动调整，2-系统自动跳过)

    public S3RaterBaseConf() {
    }

    public S3RaterBaseConf(String openFlag, String rateMode, BigDecimal nodeWeight, boolean anonymous) {
        super(openFlag, rateMode, nodeWeight, anonymous);
    }

    public void addAllNode(List<BaseAuditNode> nodes) {
        auditNodes.addAll(nodes);
    }

    @Override
    public S3RaterBaseConf clone() {
        return JSONUtil.toBean(JSONUtil.toJsonStr(this),S3RaterBaseConf.class);
    }

    public boolean hasEmptyRater() {
        if (open == 0) {
            return false;
        }
        if (CollUtil.isEmpty(auditNodes)) {
            return false;
        }
        for (BaseAuditNode auditNode : auditNodes) {
            if (auditNode.hasEmptyRater()) {
                return true;
            }
        }
        return false;
    }

    public List<Rater> allRater() {
        List<Rater> raters = new ArrayList<>();
        if (open == 0 || CollUtil.isEmpty(auditNodes)) {
            return raters;
        }
        for (BaseAuditNode auditNode : auditNodes) {
            raters.addAll(auditNode.getRaters());
        }
        return raters;
    }


    public List<Rater> builderSuperRater(boolean isRaterBack) {
        List<Rater> list = new ArrayList<>();
        if (this.isOpen()) {
            if (isRaterBack) {
                if (this.allOrNullRater().isEmpty()) {
                    list.add(new Rater("1", " "));
                    return list;
                }
                return this.allOrNullRater();
            }
        }
        return this.allRater();
    }
    /**
     * 如果有rater为空缺的，返回0
     */
    public List<Rater> allOrNullRater() {
        List<Rater> raters = new ArrayList<>();
        if (open == 0 || CollUtil.isEmpty(auditNodes)) {
            return raters;
        }
        for (BaseAuditNode auditNode : auditNodes) {
            if (auditNode.getRaters().isEmpty()) {
                raters.add(new Rater("0"));
                continue;
            }
            raters.addAll(auditNode.getRaters());
        }
        return raters;
    }

    public BaseAuditNode subNode(Integer subOrder) {
        if (open == 0 || CollUtil.isEmpty(auditNodes)) {
            return null;
        }
        for (BaseAuditNode auditNode : auditNodes) {
            if (auditNode.approvalOrder.equals(subOrder)) {
                return auditNode;
            }
        }
        return null;
    }

    public void extendMutiConf(ScoreConf scoreConf) {
        if (CollUtil.isNotEmpty(auditNodes)) {
            auditNodes.forEach(auditNode -> {
                auditNode.extendMutiConf(scoreConf);
            });
        }
    }

    public void checkRequest() {
        if (!isOpen()) {
            return;
        }
        for (BaseAuditNode auditNode : auditNodes) {
            auditNode.checkRequest();
            List<Rater> raters = auditNode.getRaters();
            if (CollUtil.isEmpty(raters)) {
                continue;
            }
            if (auditNode.isAndMode()) {
                Pecent avgPect = new Pecent(Pecent.ONE_HUNDRED, raters.size());
                raters.forEach(rater -> rater.setWeight(avgPect.getAvgWeight()));
                //权重除不尽的差值加到第一位上
                raters.get(0).setWeight(avgPect.getAvgAddDiff());
            } else {
                raters.forEach(rater -> rater.setWeight(Pecent.ONE_HUNDRED));
            }
        }
    }

    //子结点权重,评分流程才有权重,评分环节1->N子结点1->N人员
    //计算人员权重
    public void avgRaterWeight() {
        for (BaseAuditNode auditNode : auditNodes) {
            auditNode.avgRaterWeight();
        }
    }

    @Override
    public List<BaseAuditNode> auditNodes() {
        return auditNodes;
    }


    public static S3RaterBaseConf closeConf() {
        S3RaterBaseConf conf = new S3RaterBaseConf();
        conf.setOpen(0);
        conf.nodeWeight = BigDecimal.ZERO;
        return conf;
    }

    public void initRaterOrder() {
        int i = 0;
        for (BaseAuditNode auditNode : auditNodes) {
            i++;
            auditNode.initRaterOrder(i);
            auditNode.avgRaterWeight();
        }

    }

    public String getMultiType() {
        if (CollUtil.isEmpty(auditNodes)) {
            return "and";
        }
        return auditNodes.get(0).getMultiType();
    }

    public List<BaseAuditNode> getAuditNode(int order) {
        if (order > auditNodes.size()) {
            return null;
        }
        return auditNodes.stream().filter(baseAuditNode -> baseAuditNode.getApprovalOrder() == order).collect(Collectors.toList());
    }

    //展开具体的评分人员
    public void explainRater(String empId, TenantId companyId, EmpId opEmpId, String evalOrgId, String orgId,Integer nodeVacancyFlag) {
        for (BaseAuditNode an : getAuditNodes()) {
            EvalAudit anSperNode = new EvalAudit(an.getApproverType(), an.getApproverInfo(), companyId, new EmpId(empId), opEmpId, evalOrgId, orgId,nodeVacancyFlag);
            an.setRaters(anSperNode.genAuditRaters());
        }
        auditNodes = doRepeat(getAuditNodes(), nodeVacancyFlag);
    }


    public void replaceRater(String fromEmpId, KpiEmp toEmp) {
        getAuditNodes().stream().flatMap(auditNode -> auditNode.getRaters().stream()).filter(rater -> StrUtil.equals(fromEmpId, rater.getEmpId())).forEach(rater -> {
            rater.setEmpId(toEmp.getEmpId());
            rater.setEmpName(toEmp.getEmpName());
            rater.setAvatar(toEmp.getAvatar());
        });
    }


    public void replaceRaterV3(String fromEmpId, KpiEmp toEmp) {
        // raters 是否包含 toEmp
        getAuditNodes().forEach(auditNode -> {
            boolean raterExist = auditNode.getRaters().stream().anyMatch(rater -> StrUtil.equals(rater.getEmpId(), toEmp.getEmpId()));
            auditNode.replaceRater(fromEmpId, toEmp, raterExist);
        });
    }

    public void skipRater(String skipUserId) {

        BaseAuditNode nowNode = this.getNowNode(skipUserId);
        if (ObjectUtil.isNotNull(nowNode)){
            List<Rater> raters = nowNode.getRaters();
            // 如果当前节点存在多个审批人则移除当前审批人
            auditNodes = this.deleteRater(auditNodes, skipUserId);
            if (!(raters.size() > 1)){
                BigDecimal skipWeight = nowNode.getWeight();
                // 获取approvalOrder 最大的节点
                BaseAuditNode maxNode = auditNodes.stream()
                        .max(Comparator.comparing(BaseAuditNode::getApprovalOrder))
                        .get();
                for (BaseAuditNode node : auditNodes){
                    if (node.getApprovalOrder() == maxNode.getApprovalOrder()){
                        node.setWeight(node.getWeight().add(skipWeight));
                    }
                }
            }
        }
    }

    public List<BaseAuditNode> deleteRater(List<BaseAuditNode> auditNodes, String scorerId){
        auditNodes = auditNodes.stream()
                .peek(a -> a.setRaters(a.getRaters().stream()
                        .filter(rater -> !StrUtil.equals(scorerId, rater.getEmpId()))
                        .collect(Collectors.toList())))
                .filter(a -> CollUtil.isNotEmpty(a.getRaters()))
                .collect(Collectors.toList());
        return auditNodes;
    }

    public BaseAuditNode getNowNode(String scorerId){
        List<BaseAuditNode> auditNodes = getAuditNodes();
        // 获取跳过节点
        BaseAuditNode nowNode = null;
        for (BaseAuditNode node : auditNodes){
            for (Rater rater : node.getRaters()) {
                if (rater.getEmpId().equals(scorerId)){
                    nowNode = node;
                    break;
                }
            }
        }
        return nowNode;
    }

    public void skpiRepeatSuperScore(EvalUser user, EmpId opEmpId) {
        if (!this.isOpen()) {
            return;
        }
        log.info(" skpiRepeatSuperScore node：{},nodeVacancyFlag:{},opEmpId:{}", "superior_score", this.nodeVacancyFlag, opEmpId.getId());
        if (CollUtil.isEmpty(this.auditNodes)) {
            return ;
        }
        log.debug("skpiRepeatSuperScore.supRates:{}",JSONUtil.toJsonStr(this.auditNodes));
        for (BaseAuditNode auditNode : this.auditNodes) {
            auditNode.explainRaters(user.getEmpId(),user.getCompanyId(),opEmpId,user.getEvalOrgId(),user.getOrgId());
        }
        //去重
        log.debug("supBaseAudits:{}",JSONUtil.toJsonStr(this.auditNodes));
        this.auditNodes = doRepeat(this.auditNodes, nodeVacancyFlag);
    }

    public void skpiRepeatChangeSuperScore(String empId, String evalOrgId, String orgId,Set<ChangeInfo> infos,EvalChangeDmSvc dmSvc) {
        if (!this.isOpen()) {
            return;
        }
        log.info(" skpiRepeatChangeSuperScore node：{},nodeVacancyFlag:{},opEmpId:{}", "superior_score", this.nodeVacancyFlag, dmSvc.getOpEmpId());
        if (CollUtil.isEmpty(this.auditNodes)) {
            return ;
        }
        log.debug("skpiRepeatChangeSuperScore.supRates:{}",JSONUtil.toJsonStr(this.auditNodes));
        for (BaseAuditNode auditNode : this.auditNodes) {
            if (auditNode.isUserAudit() || auditNode.isTaskEmp()) {
                continue;
            }
            List<Rater> raters = new ArrayList<>();
            if (CollUtil.isEmpty(auditNode.getRaters())) {
                raters.addAll(dmSvc.parseBaseChangeRaters(empId, evalOrgId, orgId, infos,
                        auditNode.getRaters(), auditNode.getApproverType(), auditNode.getApproverInfo()));
            }else {
                for (Rater rater : auditNode.getRaters()) {
                    raters.addAll(dmSvc.parseBaseChangeRaters(empId,evalOrgId, orgId,infos,
                            auditNode.getRaters(), auditNode.getApproverType(), auditNode.isManager() ? StringUtils.isNotBlank(auditNode.getApproverInfo()) ? auditNode.getApproverInfo() : rater.getLevel().toString() : rater.getRoleId()));
                }
            }
            auditNode.setRaters(CollUtil.distinct(raters));
        }
        //去重
        log.debug("supBaseAudits:{}",JSONUtil.toJsonStr(this.auditNodes));
        this.auditNodes = doRepeat(this.auditNodes, nodeVacancyFlag);
    }

    public void parseNoSkipRaters(EvalUser user, EmpId opEmpId) {
        if (!this.isOpen()) {
            return;
        }
        if (CollUtil.isEmpty(this.auditNodes)) {
            return ;
        }
        for (BaseAuditNode auditNode : this.auditNodes) {
            auditNode.explainRaters(user.getEmpId(),user.getCompanyId(),opEmpId,user.getEvalOrgId(),user.getOrgId());
        }
        log.debug("supBaseAudits:{}",JSONUtil.toJsonStr(this.auditNodes));
    }

    public void parseNoSkipChangeRaters(String empId, String evalOrgId, String orgId,Set<ChangeInfo> infos,EvalChangeDmSvc dmSvc) {
        if (!this.isOpen()) {
            return;
        }
        if (CollUtil.isEmpty(this.auditNodes)) {
            return ;
        }
        for (BaseAuditNode auditNode : this.auditNodes) {
            if (auditNode.isUserAudit() || auditNode.isTaskEmp()) {
                continue;
            }
            List<Rater> raters = new ArrayList<>();
            if (CollUtil.isEmpty(auditNode.getRaters())) {
                raters.addAll(dmSvc.parseBaseChangeRaters(empId, evalOrgId, orgId, infos,
                        auditNode.getRaters(), auditNode.getApproverType(), auditNode.getApproverInfo()));
            }else {
                for (Rater rater : auditNode.getRaters()) {
                    raters.addAll(dmSvc.parseBaseChangeRaters(empId,evalOrgId, orgId,infos,
                            auditNode.getRaters(), auditNode.getApproverType(), auditNode.isManager() ? StringUtils.isNotBlank(auditNode.getApproverInfo()) ? auditNode.getApproverInfo() : rater.getLevel().toString() : rater.getRoleId()));
                }
            }
            auditNode.setRaters(CollUtil.distinct(raters));
        }
        log.debug("supBaseAudits:{}",JSONUtil.toJsonStr(this.auditNodes));
    }

    public static List<BaseAuditNode> doRepeat(List<BaseAuditNode> supBaseAudits, Integer nodeVacancyFlag) {
        if (CollUtil.isEmpty(supBaseAudits)) {
            log.warn(" 解析skpiRepeatSuperScore 评分人为空！");
            return supBaseAudits;
        }

        Map<String, Integer> lastSeenIndexMap = getLastSeenIndexMap(supBaseAudits);
        if (MapUtil.isEmpty(lastSeenIndexMap)) {
            log.warn(" 解析skpiRepeatSuperScore 每个评分人最后索引节点为空！");
            return supBaseAudits;
        }
        Set<Integer> delNullNodeSet = getDelNullNodeSet(supBaseAudits, lastSeenIndexMap);
        //获取raters不为空的节点。
        List<BaseAuditNode> tempSupBaseAudits = supBaseAudits.stream().filter(auditNode -> !CollUtil.isEmpty(auditNode.getRaters())).collect(Collectors.toList());
        if (CollUtil.isEmpty(tempSupBaseAudits)) {
            log.warn(" 解析skpiRepeatSuperScore 节点不存在评分人！");
            return tempSupBaseAudits;
        }
        //存在评分人空缺，提示异常，【并删除因去重空缺的节点】
        if (null != nodeVacancyFlag && 1 == nodeVacancyFlag && tempSupBaseAudits.size() < supBaseAudits.size()) {
            removeNullNode(delNullNodeSet,supBaseAudits);
            //再重新计算权重
            log.warn(" 解析skpiRepeatSuperScore 节点存在责任人空缺提示用户！supBaseAudits：{}",JSONUtil.toJsonStr(supBaseAudits));
            return supBaseAudits;
        }
        BigDecimal weight = supBaseAudits.stream()
                .filter(auditNode -> CollUtil.isEmpty(auditNode.getRaters()))
                .map(BaseAuditNode::getScoreWeight)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        //给最后一个节点赋值空节点的权重，并重新计算分配权重节点内每个评分人的权重
        setWeightLastNode(tempSupBaseAudits,weight);
        log.info(" 去重+合并权重 tempSupBaseAudits：{}", JSONUtil.toJsonStr(tempSupBaseAudits));
        supBaseAudits.clear();
        supBaseAudits.addAll(tempSupBaseAudits);
        return tempSupBaseAudits;
    }

    private static void setWeightLastNode(List<BaseAuditNode> tempSupBaseAudits, BigDecimal weight) {
        tempSupBaseAudits.forEach(auditNode -> {
            auditNode.setApprovalOrder(tempSupBaseAudits.indexOf(auditNode) + 1);
            if (tempSupBaseAudits.indexOf(auditNode) != tempSupBaseAudits.size() - 1) {
                return;
            }
            auditNode.setWeight(auditNode.getScoreWeight().add(weight));
            if (auditNode.getScoreWeight().compareTo(new BigDecimal(100)) > 0) {
                auditNode.setWeight(new BigDecimal(100));
            }
        });

        Pecent avgPect = new Pecent(tempSupBaseAudits.get(tempSupBaseAudits.size() - 1).getWeight(), tempSupBaseAudits.get(tempSupBaseAudits.size() - 1).getRaters().size());
        tempSupBaseAudits.get(tempSupBaseAudits.size() - 1).getRaters().forEach(auditNode -> auditNode.setWeight(avgPect.getAvgWeight()));
    }

    private static Map<String, Integer> getLastSeenIndexMap(List<BaseAuditNode> supBaseAudits) {
        Map<String, Integer> lastSeenIndexMap = new HashMap<>();
        for (int i = 0; i < supBaseAudits.size(); i++) {
            BaseAuditNode supBaseAudit = supBaseAudits.get(i);
            if (Objects.isNull(supBaseAudit) || CollUtil.isEmpty(supBaseAudit.getRaters())) {
                continue;
            }
            for (Rater rater : supBaseAudit.getRaters()) {
                //因重复人员,取最后一个就好。
                lastSeenIndexMap.put(rater.getEmpId(), i + 1);
            }
        }
        return lastSeenIndexMap;
    }

    private static Set<Integer> getDelNullNodeSet(List<BaseAuditNode> supBaseAudits,Map<String, Integer> lastSeenIndexMap){
        Set<Integer> delNullNodeSet = new HashSet<>();
        for (int i = 0; i < supBaseAudits.size(); i++) {
            BaseAuditNode supBaseAudit = supBaseAudits.get(i);
            List<Rater> updatedRaters = new ArrayList<>();
            for (Rater rater : supBaseAudit.getRaters()) {
                // 如果Rater的empId在lastSeenIndexMap中，并且对应的序号不大于当前的序号
                if (lastSeenIndexMap.containsKey(rater.getEmpId())&& lastSeenIndexMap.get(rater.getEmpId()) <= (i+1)) {
                    updatedRaters.add(rater);
                }
            }
            //当去重后的审核人为null,证明此节点为空节点，为提示空节点异常需删除此空节点准备。
            if (updatedRaters.size() == 0 && CollUtil.isNotEmpty(supBaseAudit.getRaters())) {
                delNullNodeSet.add(i);
            }
            supBaseAudit.setRaters(updatedRaters); // 更新BaseAuditNode的Rater列表
        }
        return delNullNodeSet;
    }

    private static void removeNullNode(Set<Integer> delNullNodeSet, List<BaseAuditNode> supBaseAudits) {
        List<Integer> indicesToRemove = new ArrayList<>();
        BigDecimal weight = new BigDecimal(0);
        for (int i = 0; i < supBaseAudits.size(); i++) {
            if (CollUtil.isEmpty(supBaseAudits.get(i).getRaters()) && CollUtil.isNotEmpty(delNullNodeSet) && delNullNodeSet.contains(i)) {
                indicesToRemove.add(i);
                weight = weight.add(supBaseAudits.get(i).getScoreWeight());
            }
        }
        if (CollUtil.isEmpty(indicesToRemove)) {
            return;
        }
        for (int index : indicesToRemove) {
            supBaseAudits.remove(index);
        }

        if (weight.compareTo(new BigDecimal(0)) > 0 && CollUtil.isNotEmpty(supBaseAudits)) {
            supBaseAudits.forEach(auditNode -> {
                auditNode.setApprovalOrder(supBaseAudits.indexOf(auditNode) + 1);
            });
            //需要重新计算权重,并吧权重赋值给最后一个节点
            BigDecimal lastWeight = supBaseAudits.get(supBaseAudits.size() - 1).getWeight();
            lastWeight = lastWeight.add(weight);
            if (lastWeight.compareTo(new BigDecimal(100)) > 0) {
                lastWeight = new BigDecimal(100);
            }

            supBaseAudits.get(supBaseAudits.size() - 1).setWeight(lastWeight);
            if (CollUtil.isNotEmpty(supBaseAudits.get(supBaseAudits.size() - 1).getRaters())) {
                Pecent avgPect = new Pecent(lastWeight, supBaseAudits.get(supBaseAudits.size() - 1).getRaters().size());
                supBaseAudits.get(supBaseAudits.size() - 1).getRaters().forEach(auditNode -> auditNode.setWeight(avgPect.getAvgWeight()));
            }
        }
    }

    public String appointRaterName() {
        StringBuilder sb = new StringBuilder();
        for (Rater rater : this.allRater()) {
            if (rater.getWeight() == null) {
                sb.append(StrUtil.format("{} ", rater.getEmpName()));
                continue;
            }
            sb.append(String.format("%s（%s％）", rater.getEmpName(), rater.getWeight()));
        }
        return sb.toString();
    }
}
