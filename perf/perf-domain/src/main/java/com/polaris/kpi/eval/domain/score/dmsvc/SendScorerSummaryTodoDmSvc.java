package com.polaris.kpi.eval.domain.score.dmsvc;

import cn.hutool.core.collection.CollUtil;
import com.polaris.acl.msg.face.MsgAcl;
import com.polaris.kpi.eval.domain.task.event.talent.ScoreSummaryMsgTodoEvent;
import com.polaris.kpi.eval.domain.task.repo.ScorerSummaryTodoRepo;
import com.polaris.kpi.setting.domain.entity.ScorerTodoSummary;
import com.polaris.kpi.setting.domain.entity.TaskUserScorer;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.MapWrap;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Hehu
 */
@Slf4j
public class SendScorerSummaryTodoDmSvc {
    private ScorerSummaryTodoRepo summaryTodoRepo;
    private MsgAcl msgAcl;

    public SendScorerSummaryTodoDmSvc(ScorerSummaryTodoRepo summaryTodoRepo, MsgAcl msgAcl) {
        this.summaryTodoRepo = summaryTodoRepo;
        this.msgAcl = msgAcl;
    }

    public void sendFor(List<String> taskUserIds, String companyId, String taskId) {
        MapWrap<String, ScorerTodoSummary> mapWrap = summaryTodoRepo.listScorerTodoSummary(taskUserIds, companyId, taskId);
        if (CollUtil.isEmpty(mapWrap.getDatas())) {
            return;
        }
        List<String> scorerIds = mapWrap.getDatas().stream().filter(summary -> summary.readyToSend()).map(summary -> summary.getScorerId()).collect(Collectors.toList());
        CompletableFuture.runAsync(() -> {
            ListWrap<TaskUserScorer> groups = summaryTodoRepo.listTodoScorers(companyId, taskId, scorerIds);
            groups.getGroups().forEach((scorerId, scorers) -> {
                ScorerTodoSummary summary = mapWrap.get(scorerId);
                if (summary != null && scorers != null) {
                    new ScoreSummaryMsgTodoEvent(companyId, scorers, summary).publish();
                }
            });
        });
    }
}