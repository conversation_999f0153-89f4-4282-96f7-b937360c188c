package com.polaris.kpi.eval.domain.confirm.entity;


import cn.com.polaris.kpi.eval.KpiTypeUsedField;
import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.cache.domain.entity.CompanyCacheInfo;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalKpiType;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.BaseAuditNode;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class ConfirmEmpEval extends EmpEvalMerge {
    @Getter
    private ConfirmNode curNode;//当前流程结点
    protected List<EmpEvalKpiType> kpiTypes;
    @Getter
    private BaseAuditNode confirmOnIndNode;//当前确认的层级
    @Getter
    private List<String> approveIndIds;//当前操作人要确认的指标id , []=表示不限制,需要确认所有
    public void buildScoreConf(CompanyCacheInfo cacheInfo, boolean indexCofMdfEmp) {
//        this.typeWeightConf = rule.getTypeWeightConf();
//        this.scoreValueConf = rule.getScoreValueConf();
        //组装统一评分流程
        if (indexCofMdfEmp) {
            this.s3SelfRater = Objects.isNull(cacheInfo) ? s3SelfRater : cacheInfo.getS3SelfRater();
            this.s3PeerRater = Objects.isNull(cacheInfo) ? s3PeerRater : cacheInfo.getS3PeerRater();
            this.s3SubRater = Objects.isNull(cacheInfo) ? s3SubRater : cacheInfo.getS3SubRater();
            this.s3SuperRater = Objects.isNull(cacheInfo) ? s3SuperRater : cacheInfo.getS3SuperRater();
            this.s3AppointRater = Objects.isNull(cacheInfo) ? s3AppointRater : cacheInfo.getS3AppointRater();
        }
//        AffirmTaskConf confirmTask = rule.getConfirmTask();
//        if (!confirmTask.isOpen()) {
//            modifyItemDimension = "all";
//            return;
//        }
//        this.curNode = confirmTask.getCurAuditNode(opEmpId, approveOrder);
//        modifyItemDimension = confirmTask.modifyItemDimension(opEmpId, approveOrder);
//        confirmAuditSign = confirmTask.confirmAuditSign(opEmpId, approveOrder);
    }

    public void matchCurOrder(Integer approveOrder, String opEmpId,List<String> approveIndIds) {
        this.curNode = confirmTask.getCurAuditNode(opEmpId, approveOrder);
        this.confirmOnIndNode = confirmTask.findConfirmOnIndNode();
        this.approveIndIds = approveIndIds;
    }

    @JSONField(serialize = false)
    public List<KpiTypeUsedField> getTypeUsedFields() {
        List<KpiTypeUsedField> kpiTypeUsedFields = new ArrayList<>();
        for (EmpEvalKpiType kpiType : this.typeDatas()) {
            kpiTypeUsedFields.addAll(kpiType.getKpiTypeUsedFields());
        }
        return kpiTypeUsedFields;
    }

    public List<EmpEvalKpiType> getTypes() {
        return super.kpiTypes.getDatas();
    }
}
