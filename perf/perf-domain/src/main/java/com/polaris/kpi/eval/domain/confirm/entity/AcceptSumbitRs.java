package com.polaris.kpi.eval.domain.confirm.entity;

import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.sdk.type.MapWrap;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
public class AcceptSumbitRs {
    protected Boolean nodeFinished;
    protected ConfirmNode node;

    public AcceptSumbitRs(Boolean nodeFinished, ConfirmNode node) {
        this.nodeFinished = nodeFinished;
        this.node = node;
    }

    public List<EvalScoreResult> getPassed() {
        List<EvalScoreResult> passed;
        MapWrap<String, List<EvalScoreResult>> rsMap = node.getRsMap();
        passed = rsMap.keySet().stream().map(rsMap::get).filter(esr -> node.isAllPassed(esr)).flatMap(Collection::stream).collect(Collectors.toList());
        return passed;
    }
}