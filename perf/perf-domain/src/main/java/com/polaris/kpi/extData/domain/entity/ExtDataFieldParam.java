package com.polaris.kpi.extData.domain.entity;

import com.polaris.kpi.extData.domain.type.ExtDataFieldParamValue;
import com.polaris.kpi.common.DelableDomain;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.annotations.Ckey;

import java.util.List;

/**
 * @Author: xuxw
 * @Date: 2025/03/11 14:06
 * @Description:
 */
@Setter
@Getter
public class ExtDataFieldParam extends DelableDomain {
    @Ckey
    private String id;
    private String extDataFieldId;
    //参数名称
    private String paramChName;
    private String paramEnName;
    //参数类型【1-数值，2-字符串，3-日期】
    private Integer paramType;
    //参数匹配
    private String paramMapping;
    //参数行为【1-匹配，2-对接，3-唯一标识】
    private Integer paramAction = 2;
    //获取方式
    private String mappingName;
    private String accessMode;
    private String companyId;
    private List<ExtDataFieldParamValue> paramValues;
}
