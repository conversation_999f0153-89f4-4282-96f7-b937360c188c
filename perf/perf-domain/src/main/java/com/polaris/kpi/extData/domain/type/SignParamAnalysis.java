package com.polaris.kpi.extData.domain.type;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.polaris.kpi.extData.domain.type.enums.SymbolEnum;
import com.polaris.kpi.common.KpiI18NException;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: xuxw
 * @Date: 2025/03/06 11:53
 * @Description: 鉴权组件参数解析
 */
public class SignParamAnalysis extends ParamAnalysis {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final Pattern DYNAMIC_PARAM_PATTERN = Pattern.compile("\\{(.*?)\\}");

    public String signParamAnalysis(String param, Map<String, String> stepMap) {
        try {
            Map<String, String> dynamicParams = new HashMap<>();

            Matcher matcher = DYNAMIC_PARAM_PATTERN.matcher(param);
            while (matcher.find()) {
                String dynamicParam = matcher.group(1);
                String resolvedValue = resolveDynamicParam(dynamicParam, stepMap);
                dynamicParams.put(dynamicParam, resolvedValue);
            }

            String result = param;
            for (Map.Entry<String, String> entry : dynamicParams.entrySet()) {
                result = result.replace(SymbolEnum.LEFT_CURLY_BRACE.getSymbol() +
                        entry.getKey() + SymbolEnum.RIGHT_CURLY_BRACE.getSymbol(), entry.getValue());
            }
            return result.replace(SymbolEnum.AMPERSAND.getSymbol(), "");
        } catch (Exception e) {
            throw new KpiI18NException("param.error", "外部数据系统签名参数解析异常");
        }
    }

    private static String resolveDynamicParam(String dynamicParam, Map<String, String> stepMap) {
        if (dynamicParam.startsWith(SymbolEnum.AT.getSymbol())) {
            return resolveStepValue(dynamicParam, stepMap);
        } else if (dynamicParam.equals("timestamp")) {
            return String.valueOf(Instant.now().getEpochSecond());
        } else {
            throw new KpiI18NException("param.error", "外部数据系统参数配置异常");
        }
    }

    private static String resolveStepValue(String dynamicParam, Map<String, String> stepMap) {
        try {
            String stepKey = dynamicParam.substring(0, 2);

            if (!stepMap.containsKey(stepKey)) {
                throw new KpiI18NException("param.error", "外部数据系统组件返回值异常");
            }

            String stepValue = stepMap.get(stepKey);

            if (dynamicParam.length() == 2) {
                return stepValue;
            }

            String path = dynamicParam.substring(3);
            JsonNode node = OBJECT_MAPPER.readTree(stepValue).at(SymbolEnum.SLASH.getSymbol()
                    + path.replace(SymbolEnum.POINT.getSymbol(), SymbolEnum.SLASH.getSymbol()));
            if (node.isMissingNode()) {
                throw new KpiI18NException("param.error", "外部数据系统参数解析异常");
            }
            return node.asText();
        } catch (Exception e) {
            throw new KpiI18NException("param.error", "外部数据系统组件返回值异常");
        }
    }
}
