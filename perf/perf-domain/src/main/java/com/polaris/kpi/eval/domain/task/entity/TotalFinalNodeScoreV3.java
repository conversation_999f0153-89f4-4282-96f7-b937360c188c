package com.polaris.kpi.eval.domain.task.entity;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.domain.task.entity
 * @Author: suxia<PERSON>qiu
 * @CreateTime: 2025-03-03  11:49
 * @Description: 权重的总分
 * @Version: 2.0
 */
@Getter
@Setter
@Slf4j
public class TotalFinalNodeScoreV3 extends BaseFinalNodeScoreV3 {
    //    private BigDecimal v3FinalSelfScore;//自评最终得分
//    private BigDecimal v3FinalPeerScore;//同级互评最终得分
//    private BigDecimal v3FinalSubScore;//下级互评最终得分
//    private BigDecimal v3FinalSuperiorScore;//上级评分最终得分
//    private BigDecimal v3FinalItemScore;//定向指标评分人最终得分
//    private BigDecimal v3FinalAppiontScore;//指定评分人最终得分
    private BigDecimal v3FinalWeightAutoSum;//自动计算总分 //带权重
    private BigDecimal v3Sum;
    private BigDecimal tempSumScore; //临时汇总用于计算的总分

//    private BigDecimal v3PlusSum;
//    private BigDecimal v3SubtractSum;
//    private BigDecimal v3OkrScoreSum;


    //---------------------------------未*环节权重---------------------------
//    private BigDecimal v3SelfScore;//自评最终得分 未计算环节权重
//    private BigDecimal v3PeerScore;//同级互评最终得分 未计算环节权重
//    private BigDecimal v3SubScore;//下级互评最终得分 未计算环节权重
//    private BigDecimal v3SuperiorScore;//上级评分最终得分 未计算环节权重
//    private BigDecimal v3AppiontScore;//指定指标评分人最终得分 未计算环节权重
    private BigDecimal v3FinalAutoSum; //自动计算总分 //不带权重

    private Map<String, TotalFinalNodeScoreV3.ScoreNodeWeight> nodeWeightMap = new HashMap<>();
//    private Function<EvalScorerNodeKpiItem, BigDecimal> getScoreFunc;
//    private Function<EvalScorerNodeKpiItem, BigDecimal> getMutualScoreFunc;
//    private Function<EvalScorerNodeKpiItem, BigDecimal> getFinalNoWeightScoreFunc;
//    private Function<EvalScorerNodeKpiItem, BigDecimal> getTypeNoWeightScoreFunc;

//    public String scoreAsString() {
//        StringAppend sbb = new StringAppend("", " | ")
//                .appendSplitPair("自评 ", v3SelfScore)
//                .appendSplitPair("指定评 ", v3AppiontScore)
//                .appendSplitPair("定向评 ", v3FinalItemScore)
//                .appendSplitPair("同级评 ", v3PeerScore)
//                .appendSplitPair("下级评 ", v3SubScore)
//                .appendSplitPair("上级评 ", v3SuperiorScore).deleteLastSplit();
//        return sbb.toString();
//    }

    public TotalFinalNodeScoreV3() {
        super();
//        this.getScoreFunc = EvalScorerNodeKpiItem::getFinalWeightScore; // 打分*指标权重*维度权重*评分人权重*环节权重
//        this.getMutualScoreFunc = EvalScorerNodeKpiItem::getScoreWeightScore; // 打分*评分人权重
//        this.getFinalNoWeightScoreFunc = EvalScorerNodeKpiItem::getFinalScore; //  打分*指标权重*维度权重*评分人权重
//        this.getTypeNoWeightScoreFunc = EvalScorerNodeKpiItem::getItemWeightScore; //  打分*指标权重*评分人权重
    }

    public BigDecimal sum() {
        addToSum(tempSumScore);
        addToSum(v3FinalAutoSum).addToSum(okrScoreSum).addToSum(plusSum).addToSum(subtractSum).addToSum(askEvalScore);
        return v3Sum;
    }
//    public BigDecimal sum() {
//        addToSum(finalSelfScore)
//                .addToSum(finalPeerScore)
//                .addToSum(finalSubScore)
//                .addToSum(finalSuperiorScore)
//                .addToSum(finalItemScore)
//                .addToSum(finalAppointScore);
//       //
//        //
//        // addToSum(plusSum).addToSum(subtractSum);
//        if (Objects.isNull(v3Sum)) {
//            log.info("计算出的总分为空，说明无需评分人评分的指标");
//            addToSum(v3FinalAutoSum).addToSum(okrScoreSum) ; //如果计算出来总分是空，说明是全自动计算指标，需将自动计算总分赋值给总分
//            log.info("计算出的总分为:v3Sum:{}",v3Sum);
//        }
//        return v3Sum;
//    }

    public void addToTempSumScore(BigDecimal tempSumScore) {
        this.tempSumScore = addNullZero(this.tempSumScore, tempSumScore);
    }

    private BigDecimal weightScoreAuto(BigDecimal itemAutoScore, BigDecimal nodeWeight) {
        BigDecimal finalWeightScoreAuto = BigDecimal.ZERO;
        if (BigDecimal.ZERO.compareTo(nodeWeight) == 0) {
            return finalWeightScoreAuto;
        }
        finalWeightScoreAuto = itemAutoScore.multiply(nodeWeight).setScale(5, RoundingMode.DOWN).setScale(4, RoundingMode.HALF_UP);

        return finalWeightScoreAuto;
    }

    public TotalFinalNodeScoreV3 addToSum(BigDecimal addTo) {
        v3Sum = addNullZero(v3Sum, addTo);
        return this;
    }

    public TotalFinalNodeScoreV3 subtractToSum(BigDecimal addTo) {
        if (addTo == null) {
            return this;
        }
        v3Sum = addNullZero(v3Sum, addTo.multiply(new BigDecimal(-1)));
        return this;
    }


    public static void main(String[] args) {
        System.out.println(addNullZero(new BigDecimal(10), null));
    }

    public ScoreNodeWeight getScoreNodeWeight(Map<String, ScoreNodeWeight> rsGroups, String scoreType) {
        if (!rsGroups.containsKey(scoreType)) {
            return null;
        }
        return rsGroups.get(scoreType);
    }

    public void addScoreNodeWeight(BigDecimal nodeWeight, String scoreType) {
        if (Objects.isNull(nodeWeight) || nodeWeightMap.containsKey(scoreType)) {
            return;
        }
        nodeWeightMap.put(scoreType, new TotalFinalNodeScoreV3.ScoreNodeWeight(scoreType, nodeWeight));
    }

    public void computeAutoFinalWeightScore(BigDecimal itemAutoScore, TypeFinalNodeScoreV3 typeScore) {
        if (CollUtil.isEmpty(scoreTypes)) {
            return;
        }
        for (String scoreType : scoreTypes) {
            if (StrUtil.equals("item_score", scoreType)) { //定向评不加自动计算分
                continue;
            }
            //计算过环节权重的自动计算指标分
            addAutoWeightScoreByType(scoreType, itemAutoScore);
            //未乘以权重的自动计算指标分
            addAutoScoreByType(scoreType, itemAutoScore);
            typeScore.computeAutoFinalWeightScore(itemAutoScore, scoreType);
        }
    }

    @Deprecated
    public void computeAutoFinalWeightScoreOld(BigDecimal itemAutoScore, TypeFinalNodeScoreV3 typeScore) {
        Set<String> scoreTypes = nodeWeightMap.keySet();
        if (CollUtil.isEmpty(scoreTypes)) {
            return;
        }
        for (String scoreType : scoreTypes) {
            TotalFinalNodeScoreV3.ScoreNodeWeight scoreNodeWeight = nodeWeightMap.get(scoreType);
            if (Objects.isNull(scoreNodeWeight) || StrUtil.equals("item_score", scoreType)) { //定向评不加自动计算分
                continue;
            }
            //计算过环节权重的自动计算指标分
            BigDecimal autoWeightScore = weightScoreAuto(itemAutoScore, scoreNodeWeight.getNodeWeight());
            addAutoWeightScoreByType(scoreType, autoWeightScore);
            //未乘以权重的自动计算指标分
            addAutoScoreByType(scoreType, itemAutoScore);
            typeScore.computeAutoFinalWeightScore(itemAutoScore, autoWeightScore, scoreType);
            this.v3FinalWeightAutoSum = TotalFinalNodeScoreV3.addNullZero(this.v3FinalWeightAutoSum, autoWeightScore);
        }
    }


    public void addTypeScoreToTotal(BigDecimal typeWeight, List<ComputedResultScore> crs) {
        for (ComputedResultScore resultScore : crs) {
            BigDecimal noNodeWeightScore = resultScore.getNoWeightScore();
            BigDecimal weightScore = resultScore.getWeightScore();
            noNodeWeightScore = initNullValue(noNodeWeightScore).multiply(typeWeight);
            weightScore = initNullValue(weightScore);

            this.addFinalScoreByType(resultScore.getScorerType(), weightScore); // 计算总环节得分(不带环节权重的环节得分)（带了环节权重的）
            this.addNoWeightScoreByType(resultScore.getScorerType(), noNodeWeightScore);// 总得分环节得分(不带环节权重的环节得分)
        }
    }


    public void addTypeScoreToTotal(List<ComputedResultScore> crs) {
        for (ComputedResultScore resultScore : crs) {
            BigDecimal noNodeWeightScore = resultScore.getNoWeightScore();
            BigDecimal weightScore = resultScore.getWeightScore();
            noNodeWeightScore = initNullValue(noNodeWeightScore);
            weightScore = initNullValue(weightScore);

            this.addFinalScoreByType(resultScore.getScorerType(), weightScore); // 计算总环节得分(不带环节权重的环节得分)（带了环节权重的）
            this.addNoWeightScoreByType(resultScore.getScorerType(), noNodeWeightScore);// 总得分环节得分(不带环节权重的环节得分)
        }
    }

    public void addSubtractOrPlusTypeScoreToTotal(List<ComputedResultScore> crs) {
        for (ComputedResultScore resultScore : crs) {
            BigDecimal noNodeWeightScore = resultScore.getNoWeightScore();
            noNodeWeightScore = initNullValue(noNodeWeightScore);

            BigDecimal weightScore = resultScore.getWeightScore();
            weightScore = initNullValue(weightScore);

            this.addNoWeightScoreByType(resultScore.getScorerType(), noNodeWeightScore);// 总得分环节得分(不带环节权重的环节得分)
            this.addFinalScoreByType(resultScore.getScorerType(), weightScore);// 总得分环节得分(带环节权重的环节得分)
        }
    }

    public void nodeAddExtraWeightScore() {
     //   addExtraWeightScore(this.okrScoreSum);//okr
     //   addExtraWeightScore(this.plusSum);//加分
      //  addExtraWeightScore(this.subtractSum);//减分
      //  addExtraWeightScore(this.askEvalScore);//问卷分
    }

    @Setter
    @Getter
    @NoArgsConstructor
    public static class ScoreNodeWeight {
        private String scorerType;
        private BigDecimal nodeWeight;//环节权重

        public ScoreNodeWeight(String scorerType, BigDecimal nodeWeight) {
            this.scorerType = scorerType;
            this.nodeWeight = nodeWeight;
        }
    }
}
