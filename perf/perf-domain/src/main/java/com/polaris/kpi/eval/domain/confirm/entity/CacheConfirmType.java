package com.polaris.kpi.eval.domain.confirm.entity;

import cn.hutool.core.collection.CollUtil;
import com.polaris.kpi.cache.domain.entity.ConfirmTypeCache;
import com.polaris.kpi.eval.domain.task.entity.EvalKpi;
import com.polaris.kpi.eval.domain.task.entity.OkrEvalKpi;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalKpiType;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Getter
@Setter
public class CacheConfirmType extends EmpEvalKpiType {

    public void fromCache(ConfirmTypeCache cache, String empId) {
        ConfirmTypeCache.CacheType typeInfo = cache.getIndexType();
        //private List<EmpEvalDetailKpiItemPo> taskKpiList;
        // this.kpiTypeId = Objects.nonNull(cache.getKpiTypeId()) ? cache.getKpiTypeId() : typeInfo.getId();
        this.kpiTypeId = Objects.nonNull(typeInfo) && Objects.nonNull(typeInfo.getId()) ? typeInfo.getId() : cache.getKpiTypeId();
        // this.kpiTypeName = Objects.nonNull(cache.getKpiTypeName()) ? cache.getKpiTypeName() : typeInfo.getDicValue();
        this.kpiTypeName = Objects.nonNull(typeInfo) && Objects.nonNull(typeInfo.getDicValue()) ? typeInfo.getDicValue() : cache.getKpiTypeName();
        this.kpiTypeWeight = cache.getWeightVal();
        this.itemLimitCnt = cache.getItemLimitCnt();
        this.isOkr = Objects.nonNull(cache.getIsOkr()) ? cache.getIsOkr() : typeInfo.getIsOkr();
        //this.typeOrder=typeInfo.
        this.kpiTypeClassify = Objects.nonNull(cache.getKpiTypeClassify()) ? cache.getKpiTypeClassify() : typeInfo.getClassify();
//        this.isTemporary = Objects.nonNull(cache.getIsTemporary()) ? cache.getIsTemporary() : typeInfo.getIsTemporary();
        this.typeOrder = Objects.nonNull(cache.getTypeOrder()) ? cache.getTypeOrder() : typeInfo.getTypeOrder();
        this.scoreOptType = Objects.nonNull(cache.getScoreOptType()) ? cache.getScoreOptType() : typeInfo.getScoreOptType() == null ? 2 : typeInfo.getScoreOptType();
        this.des = StringUtils.isNotBlank(cache.getDes()) ? cache.getDes() : typeInfo.getDes();
        this.reserveOkrWeight = cache.getReserveOkrWeight();
        this.maxExtraScore = cache.getMaxExtraScore();
//        this.isEmptyType = cache.getIsEmptyType();
        this.lockedItems = cache.tolockedItems();
        this.isDeleted = cache.getIsDeleted();
        this.plusSubInterval = cache.getPlusSubInterval();
        this.kpiTypeUsedFields = CollUtil.isNotEmpty(cache.getKpiTypeUsedFields()) ? cache.getKpiTypeUsedFields() : typeInfo.getKpiTypeUsedFields();
        this.indLevelGroupId = cache.getIndLevelGroupId();
        this.indLevelGroup = cache.getIndLevelGroup();
        this.appointRater = cache.getAppointRater();
        this.subRater = cache.getSubRater();
        this.peerRater = cache.getPeerRater();
        this.superRater = cache.getSuperRater();
        this.selfRater = cache.getSelfRater();
        this.finishValueAudit = CollUtil.isNotEmpty(cache.getFinishValueAudit()) ? cache.getFinishValueAudit() : typeInfo.getFinishValueAudit();
        this.ask360EvalId = cache.getAsk360EvalId();
        this.ask360TempId = cache.getAsk360TempId();
        this.ask360TempName = cache.getAsk360TempName();
        this.scoringType = cache.getScoringType();
        this.okrGoals = cache.getOkrGoals();
        List<ConfirmTypeCache.CacheItem> items = cache.getIndexList();
        if (CollUtil.isEmpty(items)) {
            return;
        }
        List<EvalKpi> kpis = new ArrayList<>();

        for (ConfirmTypeCache.CacheItem item : items) {
            if (Boolean.valueOf(item.getIsDeleted())) {
                continue;
            }
            EvalKpi kpi;
            if (Boolean.valueOf(isOkr)) {
                OkrEvalKpi kpi1 = new OkrEvalKpi();
                kpi1.setOkrTaskId(item.getOkrTaskId());
                kpi1.setOkrTaskName(item.getOkrTaskName());
                kpi1.setEvaluateStartDate(item.getEvaluateStartDate());
                kpi1.setEvaluateEndDate(item.getEvaluateEndDate());
                kpi = kpi1;
            } else {
                kpi = new CacheConfirmItem();
            }
            kpi.build(item);
            //防止维度名称和id暂存变更
            kpi.setKpiTypeId(kpiTypeId);
            kpi.setKpiTypeName(kpiTypeName);
            kpi.initFinishValueSource();
            kpi.setEmpId(empId);
            kpi.setExamineOperType(item.getExamineOperType());
            kpis.add(kpi);
        }
        this.items = kpis;
    }

}
