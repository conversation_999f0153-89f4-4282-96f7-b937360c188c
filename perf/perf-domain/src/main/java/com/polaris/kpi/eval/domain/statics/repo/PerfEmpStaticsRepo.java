package com.polaris.kpi.eval.domain.statics.repo;
import com.polaris.kpi.eval.domain.statics.entity.PerfEmpStatics;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;

/**
 * <AUTHOR>
 * @date 2025/1/20 17:48
 */
public interface PerfEmpStaticsRepo {

    PerfEmpStatics buildPerfEmp(EvalUser taskUser, AdminTask adminTask);

    void addOrUpdatePerfEmpStatics(PerfEmpStatics perfEmpStatics);

    void deleteByTaskUserId(String taskUserId,String companyId);

}
