package com.polaris.kpi.extData.domain.type.module;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.polaris.kpi.extData.domain.entity.ExtDataSysConf;
import com.polaris.kpi.extData.domain.type.HttpClient;
import com.polaris.kpi.extData.domain.type.encAndDec.EncDec;
import com.polaris.kpi.extData.domain.type.encAndDec.EncDecFactory;
import org.springframework.http.ResponseEntity;

import java.util.Map;

/**
 * @Author: xuxw
 * @Date: 2025/03/10 10:00
 * @Description: API组件
 */
public class ApiModule {

    private ExtDataSysConf conf;

    public ApiModule(ExtDataSysConf conf) {
        this.conf = conf;
    }

    public String execute(Map<String, String> stepMap) {
        HttpClient httpClient = new HttpClient();
        ResponseEntity<String> response = httpClient.sendRequest(conf.buildParam(), stepMap);
        String body = response.getBody();
        if (StrUtil.isNotEmpty(conf.getResParamDecMode())){
            EncDec enc = EncDecFactory.init(conf.getResParamDecMode());
            try {
                body = enc.dec(body);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return body;
    }
}
