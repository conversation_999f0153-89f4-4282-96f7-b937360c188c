package com.polaris.kpi.extData.domain.type;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.polaris.kpi.extData.domain.entity.ExtDataSysConf;
import com.polaris.kpi.extData.domain.type.enums.SymbolEnum;
import com.polaris.kpi.extData.domain.type.module.ApiModule;
import com.polaris.kpi.extData.domain.type.module.SignModule;
import com.polaris.kpi.common.KpiI18NException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: xuxw
 * @Date: 2025/03/05 18:45
 * @Description:
 */
public class TokenAnalysis {

    private static final Integer API_MODULE = 1;
    private static final Integer SIGN_MODULE = 2;
    private List<ExtDataSysConf> extDataSysConfList;
    private String tokenAcquireMethod;

    public TokenAnalysis(List<ExtDataSysConf> extDataSysConfList, String tokenAcquireMethod){
        this.extDataSysConfList = extDataSysConfList;
        this.tokenAcquireMethod = tokenAcquireMethod;
    }

    public String analysisToken(){
        // 根据外部系统的配置列表调用不同的组件
        if (CollUtil.isEmpty(this.extDataSysConfList)){
            throw new KpiI18NException("param.error", "外部系统获取token的配置不能为空！");
        }

        extDataSysConfList = extDataSysConfList.stream()
                .sorted((c1, c2) -> Integer.compare(c1.getSort(), c2.getSort()))
                .collect(Collectors.toList());
        Map<String, String> stepMap = new HashMap<>();
        SignModule signModule;
        ApiModule apiModule;
        for (ExtDataSysConf conf : extDataSysConfList){
            if (conf.getConfType() == SIGN_MODULE){
                signModule = new SignModule(conf);
                String res = signModule.execute(stepMap);
                stepMap.put(SymbolEnum.AT.getSymbol() + conf.getSort(), res);
            }
            if (conf.getConfType() == API_MODULE){
                apiModule = new ApiModule(conf);
                String res = apiModule.execute(stepMap);
                JSONObject jsonObject;
                if (ObjectUtil.isNotNull(conf.getResParamType())){
                    // 将接口返回值转换成json
                    ParamAnalysis paramAnalysis = new ParamAnalysis();
                    jsonObject = paramAnalysis.transferParam(res, conf.getResParamType());
                    stepMap.put(SymbolEnum.AT.getSymbol() + conf.getSort(), jsonObject.toJSONString());
                }else {
                    stepMap.put(SymbolEnum.AT.getSymbol() + conf.getSort(), res);
                }
            }
        }
        String res = stepMap.get(SymbolEnum.AT.getSymbol() + extDataSysConfList.size());
        ParamAnalysis paramAnalysis = new ParamAnalysis();
        String token = paramAnalysis.getJsonString(res, tokenAcquireMethod);
        return token;
    }
}
