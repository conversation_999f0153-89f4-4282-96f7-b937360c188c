package com.polaris.kpi.eval.domain.task.entity;

import cn.com.polaris.kpi.EmpStaff;
import cn.com.polaris.kpi.ItemDecompose;
import cn.com.polaris.kpi.eval.*;
import cn.com.polaris.kpi.temp.*;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.perf.www.common.constant.BusinessConstant;
import com.perf.www.common.em.EvaluateTypeEnum;
import com.perf.www.common.utils.string.StringTool;
import com.perf.www.dto.EvaluateScoreSettingDTO;
import com.polaris.kpi.eval.domain.task.entity.admineval.CommentReqConf;
import com.polaris.kpi.eval.domain.task.entity.empeval.AsAdminTaskRule;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalKpiType;
import com.polaris.kpi.eval.domain.task.entity.empeval.KpiListWrap;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.*;
import com.polaris.kpi.eval.domain.task.type.ScoringNodeEnum;
import com.polaris.kpi.eval.domain.task.type.ScoringStageChain;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.eval.domain.temp.entity.*;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * {
 * "id":"",
 * "companyId":"公司id",
 * "taskName":"考核任务名称",
 * "templBaseId":"考核模板id",
 * "templName":"考核模板名称",
 * "cycleStartDate":"考核周期起始",
 * "cycleEndDate":"考核周期截止",
 * "taskDesc":"考核任务描述",
 * "evaluationStaff":"考核员工JSON对象；[{"obj_type":"对象类型(部门/角色/岗位/指定员工)",“objItems”:[{"objId":"对象id","objName":"对象名称"}]}]",
 * "excludeStaff":"排除员工JSON对象；[{"empId":"员工id","empName":"员工姓名"}]",
 * "createTaskType":"发送考核方式（自动/手动）",
 * "createTaskDateType":"发起日期类型（开始前/开始后）",
 * "day":"发起日期值",
 * "visibleType":"可见范围类型(公司/部门/自己和上级)",
 * "taskStatus":"考核任务状态",
 * "isDeleted":"是否删除",
 * "createdUser":"创建用户",
 * "createdTime":"创建时间",
 * "updatedUser":"修改用户",
 * "updatedTime":"修改时间",
 * "cycleType":"考核周期类型",
 * "templDesc":"模板描述",
 * "templItemJson":"模板关联的指标json",
 * "templInitiateJson":"模板发起考核任务配置json",
 * "templAffirmJson":"模板确认指标配置json",
 * "templEvaluateJson":"模板评价配置json",
 * "scoreStartRuleType":"评分开始时间规则类型（周期结束前/后）",
 * "scoreStartRuleDay":"评分开始时间规则值",
 * "enterScoreMethod":"进入评分方式，手动manual，自动auto",
 * "publicType":"考核结果公示类型：auto:实时自动公示，afterFinished: 考核任务完成后自动公示，manual:考核任务完成后，由发起人手动公示",
 * "templBaseJson":"模板基础信息json",
 * "evaluateType":"评分流程类型 ： simple-简易流程，360-360°考核流程，custom-自定义流程",
 * "publicEmpJson":"手动公示人信息",
 * "templExecuteJson":"模板执行阶段配置",
 * "resultAffirm":"结果是否需（被考核人）确认；true/false",
 * "affirmSignature":"确认是否需（被考核人）签名；true/false",
 * "templPointsJson":"",
 * "canAppeal":"是否可申诉；true/false",
 * "appealReceiver":"申述受理人；格式：[{"obj_type":"user","objItems":[{"objId":"员工id","objName":"姓名"}]}]",
 * "customFullScore":"自定义满分分值",
 * "enterScoreEmpType":" 发起评分的人员type=1管理员,type=2考核员工",
 * "publicDimension":"公示维度",
 * "publicToEmp":"公示范围",
 * }
 * {@see com.perf.www.model.task.PerfEvaluateTaskBaseModel }
 *
 * <AUTHOR> lufei
 * @date 2022/1/20 5:41 下午
 * task_base
 */
@Setter
@Getter
public class CycleEval implements IGetNodeWeight, AsAdminTaskRule {
    private Integer totalCnt;//'参与人数'
    private Integer drawUpCnt;//'已制定人数'
    private Integer startCnt;//'已发起人数'
    private Integer finishCnt;//'已完成人数'
    private String id;//
    private String cycleId;//周期id
    private TenantId companyId;//公司id
    private Name taskName;//考核任务名称
    private String templBaseId;//考核模板id
    private String templName;//考核模板名称
    private String cycleStartDate;//考核周期起始
    private String cycleEndDate;//考核周期截止
    private String taskDesc;//考核任务描述
    private List<StaffConfItem> evaluationStaff;//考核员工JSON对象；[{"obj_type":"对象类型(部门/角色/岗位/指定员工)",“objItems”:[{"objId":"对象id","objName":"对象名称"}]}]
    private List<EmpStaff> excludeStaff;//排除员工JSON对象；[{"empId":"员工id","empName":"员工姓名"}]
    private String createTaskType;//发送考核方式（自动/手动）
    private String createTaskDateType;//发起日期类型（开始前/开始后）
    private Integer day;//发起日期值
    private String visibleType;//可见范围类型(公司/部门/自己和上级)
    private String taskStatus;//考核任务状态     draft,publishing,published
    private String isDeleted;//是否删除
    private EmpId createdUser;//创建用户
    private Date createdTime;//创建时间
    private EmpId updatedUser;//修改用户
    private Date updatedTime;//修改时间
    private CycleType cycleType;//考核周期类型
    private String templDesc;//模板描述
    private List<PerfTemplKpiType> templItemJson;//模板关联的指标分类json
    private PerfTemplEvaluateInitiate templInitiateJson;//模板发起考核任务配置json
    private PerfTemplEvaluateAffirm templAffirmJson;//模板确认指标配置json
    private PerfTemplEvaluate templEvaluateJson;//模板评价配置json
    private String scoreStartRuleType;//评分开始时间规则类型（周期结束前/后）
    private Integer scoreStartRuleDay;//评分开始时间规则值
    private String enterScoreMethod;//进入评分方式，手动manual，自动auto
    private PublicTypeEnum publicType;//考核结果公示类型：auto:实时自动公示，afterFinished: 考核任务完成后自动公示，manual:考核任务完成后，由发起人手动公示
    private List<PubExcutor> publicEmpJson;//手动公示人信息
    private List<PubToRater> publicToEmp;//公示给哪些人看

    private ExamGroup templBaseJson;//模板基础信息json
    private String evaluateType;//评分流程类型 ： simple-简易流程，360-360°考核流程，custom-自定义流程
    private Integer performanceType;                //绩效类型 1=个人绩效，2=组织绩效 默认=1
    private int isAuto = 0; // 是否自动发起考核【0-否，1-是】


    public PerfTemplEvaluateExecute getTemplExecuteJson() {
        if (Objects.isNull(templExecuteJson)) {
            PerfTemplEvaluateExecute execute = new PerfTemplEvaluateExecute();
            execute.setAuditOpen(0);
            execute.setCompanyId(this.companyId.getId());
            execute.setTemplBaseId(this.templBaseId);
            execute.setChangeUser("admin");
            execute.setChangeFlag("false");
            return execute;
        }
        return templExecuteJson;
    }

    private PerfTemplEvaluateExecute templExecuteJson;//模板执行阶段配置

    private String resultAffirm;//结果是否需（被考核人）确认；true/false
    private String affirmSignature;//确认是否需（被考核人）签名；true/false
    private String autoResultAffirm;//自动确认结果/true/false
    private Integer autoResultAffirmDay;//自动确认结果限制的时间

    //private List<PerfTemplItemPointRule> templPointsJson;//
    private String canAppeal;//是否可申诉；true/false
    private String appealReceiver;//申述受理人；格式：[{"obj_type":"user","objItems":[{"objId":"员工id","objName":"姓名"}]}]
    private BigDecimal customFullScore;//自定义满分分值
    private Integer enterScoreEmpType;// 发起评分的人员type=1管理员,type=2考核员工
    private Integer publicDimension;//公示维度
    // private String levelGroupId;//关联哪个等级组/perf_evaluation_level 的level_group_id
    private int isNewEmp;
    private List<PerfEvaluateTaskAuditInit> auditInits;

    private AffirmTaskConf confirmTask;             //确认任务  对应模板 templAffirmJson:PerfTemplEvaluateAffirm
    private EditExeIndiConf editExeIndi;            //执行中修改指标  对应模板 templExecuteJson:PerfTemplEvaluateExecute
    //以下评分配置
    private EnterScoreConf enterScore;              //启动评分配置 ,多字段合并
    private ScoreSortConf scoreSortConf;            //2.0.0新版:新加字段
    private ScoreViewConf scoreView;                 //可见范围
    //以下结果配置
    private AuditResultConf auditResult;             //结果校准,从templEvaluateJson:PerfTemplEvaluate 模板评价配置json 单独出来的
    private InterviewConf interviewConf;            //绩效面谈配置,多个字段合并
    private ConfirmResultConf confirmResult;         //结果确认,多个字段合并
    private PublishResultConf publishResult;         //结果公示,多个字段合并
    private AppealConf appealConf;                   //结果申诉,多个字段合并
    private ScoreCommentConf commentConf;          //评语与总结配置
    private CommentReqConf commentReqConf;
    private String scoreRuleSnapId;      //等级规则组快照id,周期上的规则
    public CycleEval() {
    }

    private boolean tmpTask;

    @JSONField(serialize = false, deserialize = false)
    private List<PerfTemplKpiType> kpiTypes;//kpi维度
    @JSONField(serialize = false, deserialize = false)
    private List<EmpStaff> empStaffs;
    @JSONField(serialize = false, deserialize = false)
    private Map<String, PerfTemplItemPointRule> pointRuleMap = new HashMap<>();
    @JSONField(serialize = false, deserialize = false)
    private Map<String, PerfTemplKpiType> kpiTypeMap = new HashMap<>();
//    @JSONField(serialize = false, deserialize = false)
//    private StageChain stageChain;
    //@JSONField(serialize = false, deserialize = false)
    //private List<EvalUser> evalUsers;

    public CycleEval(TenantId tenantId, Name taskName, CycleId cycleId) {
        this.companyId = tenantId;
        this.taskName = taskName;
        this.cycleId = cycleId.getId();
        this.taskStatus = "published";
    }


    //@ConstructorProperties({"taskName", "taskDesc", "cycleType", "customFullScore"})
    public CycleEval(Name taskName, String taskDesc, CycleType cycleType, BigDecimal customFullScore) {
        this.taskName = taskName;
        this.taskDesc = taskDesc;
        this.cycleType = cycleType;
        this.customFullScore = customFullScore;
    }


    public CycleEval(String id, String taskName) {
        this.id = id;
        this.taskName = new Name(taskName);
    }

    public void markTmpTask() {
        this.tmpTask = true;
    }

    public Boolean tmpTask() {
        return tmpTask;
    }


    //copy公示相关配置到任务上
    public void copyPublicConf(PublicTypeEnum publicType, List<PubExcutor> excutors,
                               List<PubToRater> pubToRaters, Integer publicDimension) {
        this.publicType = publicType;
        this.publicEmpJson = excutors;
        this.publicToEmp = CollUtil.isEmpty(pubToRaters) ? initPublicToEmp() : pubToRaters;
        this.publicDimension = publicDimension;
    }

    public List<PubToRater> initPublicToEmp() {
        return Arrays.asList(new PubToRater("emp"));
    }

    //copy结果确认配置到任务上
    public void copyResultAffirmConf(String resultAffirm, String affirmSignature,
                                     String canAppeal, String appealReceiver,
                                     String autoResultAffirm, Integer autoResultAffirmDay) {
        this.resultAffirm = resultAffirm;
        this.affirmSignature = affirmSignature;
        this.canAppeal = canAppeal;
        this.appealReceiver = appealReceiver;
        this.autoResultAffirm = autoResultAffirm;
        if (isAutoResultAffirm()) {
            Assert.isTrue(autoResultAffirmDay > 0);
        }
        this.autoResultAffirmDay = autoResultAffirmDay;
    }


    //copy模板评价配置
    public void copyEval(String scoreStartRuleType, Integer scoreStartRuleDay,
                         String enterScoreMethod, Integer enterScoreEmpType,
                         String evaluateType) {
        this.setScoreStartRuleType(scoreStartRuleType);
        this.setScoreStartRuleDay(scoreStartRuleDay);
        this.setEnterScoreMethod(enterScoreMethod);
        this.setEnterScoreEmpType(enterScoreEmpType);
        this.setEvaluateType(evaluateType);
    }


    @JSONField(serialize = false)
    public boolean isAutoResultAffirm() {
        return Boolean.TRUE.toString().equals(autoResultAffirm);
    }

    @JSONField(serialize = false)
    public boolean isNew() {
        return StrUtil.isBlank(id);
    }

    public void initOnNew(String id) {
        if (!isNew()) {
            return;
        }
        this.updatedUser = this.createdUser;
        this.taskStatus = BusinessConstant.PUBLISHING;
        this.id = id;
        this.isDeleted = "false";
    }

    public void copyTemp(ExamGroup temp) {
        this.isNewEmp = temp.getIsNewEmp();
        this.companyId = temp.getCompanyId();
        this.templBaseId = temp.getId();
        this.templName = temp.getName();
        this.templBaseJson = temp;
        if (evaluationStaff == null) {//发起时未指定人员则使用模板中的人员
            this.evaluationStaff = temp.getInitiate().getExamineObjData();
            this.excludeStaff = temp.getInitiate().getExcludeEmpData();
        }
        // 模板关联的指标
        this.templItemJson = temp.getKpiTypes();
        //模板发起考核任务配置
        this.templInitiateJson = temp.getInitiate();
        //模板确认指标配置
        this.templAffirmJson = temp.getAffirm();
        //模板执行阶段配置
        this.templExecuteJson = temp.getExecute();
        //模板积分配置
        //initPointMap(temp.getPointRules());
        initKpiType(temp.getKpiTypes());
        //模板评价配置
        final PerfTemplEvaluate evaluate = temp.getEvaluate();
        if (CollUtil.isNotEmpty(evaluate.getFinalAuditList())) {
            LoggerFactory.getLogger(getClass()).info("模板校准几个阶段：" + evaluate.getFinalAuditList().size());
        }
        this.templEvaluateJson = evaluate;
        this.copyEval(evaluate.getScoreStartRuleType(), evaluate.getScoreStartRuleDay(),
                evaluate.getEnterScoreMethod(), evaluate.getEnterScoreEmpType(), evaluate.getEvaluateType());
    }


    public boolean isAutoPublic() {
        return publicType.isAutoType();
    }

    public List<EvalKpi> cloneKpi(String empId, String taskUserId, boolean openPoint,
                                  Map<String, ItemDecompose> decomposeMap) {
        initKpiType(this.templItemJson);
        List<EvalKpi> rs = new ArrayList<>();
        kpiTypes.forEach(factor -> {
            if (factor.wasEmptyKpiType()) {
                return;
            }
            final List<EvalKpi> items = factor.indicatorsStream().map(item -> {
                if ("exam".equals(item.getResultInputType())) {
                    item.setResultInputUserId(empId);
                }
                EvalKpi convert = Convert.convert(EvalKpi.class, item);
                convert.setScorerObjId(item.scorerOfItem());
                //基础id字段
                convert.baseId(companyId, id, empId, createdUser, taskUserId);
                //类别字段
                convert.factor(factor.getTypeName(), factor.getTypeWeight(), factor.getClassify(),
                        factor.getMaxExtraScore(), factor.getIsTypeLocked(), factor.getIsOkr(),
                        factor.getOrder(), factor.getItemLimitCnt());
                convert.copyItem(item.getItemName(), item.getItemValue(), item.getResultInputUserId());
                convert.copyNewEmp(item.getIsNewEmp());
                //初始化公式字段
                List<EvalFormulaField> formulas =
                        Convert.toList(EvalFormulaField.class, item.getFormulaFieldList());
                convert.copyFormulaFields(formulas);

                //自定义流程无KpiTypeId 只有kpiItemId,
                if (isCustom() && item.getItemEvaluate() != null) {
                    EvalItemScoreRule scoreRule
                            = Convert.convert(EvalItemScoreRule.class, item.getItemEvaluate());
                    convert.customItemScoreRule(scoreRule);
                }

//                //积分配置
//                if (openPoint) {
//                    PerfTemplItemPointRule itemPointRule = pointRuleMap.get(item.getId());
//                    if (itemPointRule == null) {
//                        return convert;
//                    }
//                    List<PerfEvalRefPointDetail> evalDetails = itemPointRule.getPointDetailList().stream().map(tempDetail -> {
//                        PerfEvalRefPointDetail evalD = Convert.convert(PerfEvalRefPointDetail.class, tempDetail);
//                        evalD.setTaskUserId(taskUserId);
//                        evalD.setCreatedUser(this.createdUser.getId());
//                        evalD.setRefId(item.getId());
//                        return evalD;
//                    }).collect(Collectors.toList());
//                    convert.setPointsRule(evalDetails);
//                }
                //处理年度计划值
                final String key = StringTool.joinStr(empId, item.getKpiItemId(), "_");
                if (decomposeMap.containsKey(key)) {
                    convert.decomposeValue(decomposeMap.get(key).getValue());
                }
                return convert;
            }).collect(Collectors.toList());
            rs.addAll(items);
        });
        return rs;
    }

    private void initKpiType(List<PerfTemplKpiType> kpiTypes) {
        this.kpiTypes = kpiTypes;
        kpiTypes.forEach(kpiType -> {
            kpiType.setId(null);
            kpiTypeMap.put(kpiType.getTypeId(), kpiType);
        });
    }

    public List<EmpEvalKpiType> cloneKpiTypes(String taskUserId) {
        List<EmpEvalKpiType> typeRs = new ArrayList<>();
        for (PerfTemplKpiType kpiType : kpiTypes) {
            EmpEvalKpiType type = new EmpEvalKpiType(companyId, taskUserId, kpiType.getTypeId());
            type.accNameType(kpiType.getTypeName(), kpiType.getClassify(), kpiType.getOrder(), kpiType.getTypeWeight());
            type.accOkr(kpiType.getIsOkr(), kpiType.getReserveOkrWeight());
            KpiItemLimitCnt limitCnt = StrUtil.isNotBlank(kpiType.getItemLimitCnt()) ? JSONUtil.toBean(kpiType.getItemLimitCnt(), KpiItemLimitCnt.class) : null;
            List<String> lockedItems = StrUtil.isNotBlank(kpiType.getIsTypeLocked()) ? JSONUtil.toList(kpiType.getIsTypeLocked(), String.class) : null;
            type.accLimit(limitCnt, lockedItems, kpiType.getMaxExtraScore());
            type.acckpiTypeUsedFields(kpiType.getKpiTypeUsedFields());
            type.initScoreOptType();
            typeRs.add(type);
        }
        return typeRs;
    }

    //public void initPointMap(List<PerfTemplItemPointRule> pointRules) {
    //    this.templPointsJson = pointRules;
    //    if (pointRules == null) {
    //        return;
    //    }
    //    this.templPointsJson.forEach(pointRule -> {
    //        pointRuleMap.put(pointRule.getKpiItemId(), pointRule);
    //    });
    //}

    public List<String> empStaffIds() {
        return CollUtil.map(empStaffs, EmpStaff::getEmpId, true);
    }

    public List<EvalOkrType> cloneEmptyTypes(String empId, String taskUserId) {
        final List<EvalOkrType> rs = kpiTypes.stream().filter(PerfTemplKpiType::wasEmptyKpiType).map(factor -> {
            EvalOkrType convert = Convert.convert(EvalOkrType.class, factor);
            //基础id字段
            convert.baseId(id, empId, createdUser.getId(), taskUserId);
            //类别字段
            convert.factor(factor.getTypeName(), factor.getTypeWeight(), factor.getClassify(),
                    factor.getMaxExtraScore(), factor.getIsTypeLocked(), factor.getIsOkr(), factor.getOpenOkrScore(),
                    factor.getOrder());
            convert.reserveOkr(factor.getTypeId(), factor.getReserveOkrWeight());
            return convert;
        }).collect(Collectors.toList());
        return rs;
    }

    public void startTimeConf(String createTaskType, String createTaskDateType, Integer day) {
        this.createTaskType = createTaskType;
        this.createTaskDateType = createTaskDateType;
        this.day = day;
    }

    public void cycleDate(String cycleStartDate, String cycleEndDate) {
        this.cycleStartDate = cycleStartDate;
        this.cycleEndDate = cycleEndDate;
    }

    public boolean needAffirm(EvalUser taskUser) {
        if (taskUser.wasTempTask()) {
            return Boolean.parseBoolean(getTemplAffirmJson().getConfirmFlag());
        }
        return taskUser.getEmpEvalRule().needConfirmTask();
    }

    @Override
    public Integer limitConfirmDays() {
        if (!templAffirmJson.openConfirmLT()) {
            return null;
        }
        return templAffirmJson.getConfirmLTDay();
    }

    @JSONField(serialize = false)
    public boolean isCustom() {
        return EvaluateTypeEnum.CUSTOM.getType().equals(this.evaluateType);
    }

    @JSONField(serialize = false)
    public Boolean isTypeWeightOpen() {
        return "open".equals(getTemplBaseJson().getTypeWeightSwitch()) || "true".equals(getTemplBaseJson().getTypeWeightSwitch());
    }

    public BigDecimal nodePecWeight(SubScoreNodeEnum scoreNode) {
        if (this.templEvaluateJson == null) {
            return BigDecimal.ZERO;
        }
        return this.templEvaluateJson.nodePecWeight(scoreNode);
    }

    public String getScoreRangeType() {
        if (this.templBaseJson == null) {
            return null;
        }
        return this.templBaseJson.getScoreRangeType();
    }

    public final static String SCORE_RANGE_TYPE_WEIGHT_SCORE = "weightScore";

    public boolean isFullScoreRange() {
        return !SCORE_RANGE_TYPE_WEIGHT_SCORE.equalsIgnoreCase(getScoreRangeType());
    }

    //是否打开结果校准
    @JSONField(serialize = false)
    public boolean isResultAuditingOpen() {
        if (auditResult != null) {
            return auditResult.isOpen();
        }
        if (templEvaluateJson == null) {
            return false;
        }
        return Boolean.TRUE.toString().equals(templEvaluateJson.getAuditFlag());
    }


    //是否打开结果面谈
    @JSONField(serialize = false)
    public boolean isInterviewOpen() {
        if (Objects.nonNull(interviewConf)) {
            return interviewConf.isOpen();
        }
        return false;
    }

    //是否打开结果面谈确认签名
    @Override
    @JSONField(serialize = false)
    public boolean isInterviewConfirmSign() {
        if (Objects.nonNull(interviewConf)) {
            return interviewConf.isOpen() && Objects.nonNull(interviewConf.getInterviewConfirmConf()) && interviewConf.getInterviewConfirmConf().isSign();
        }
        return false;
    }

    //是否打开结果面谈执行转交
    @Override
    @JSONField(serialize = false)
    public boolean isInterviewExcuteTransfer() {
        if (Objects.nonNull(interviewConf)) {
            return interviewConf.isOpen() && Boolean.TRUE.toString().equals(interviewConf.getTransferFlag());
        }
        return false;
    }

    //结果是否需（被考核人）确认
    @JSONField(serialize = false)
    public boolean isResultAffirmOpen() {
        if (confirmResult != null) {
            return confirmResult.isOpen();
        }
        if (templEvaluateJson == null) {
            return false;
        }
        return Boolean.TRUE.toString().equals(resultAffirm);
    }

    @Override
    public boolean isPublishResultOpen() {
        if (null == this.publicType) {
            return false;
        }
        if (publicType.getType().equals("notPublic")) {
            return false;
        }
        return true;
    }

    public boolean isManualPublicOpen() {
        if (publishResult != null && publishResult.isOpen()) {
            return publishResult.isManual();
        }
        return publicType.isManualType();
    }

    public List<String> parsePublicEmp() {
        List<String> empIds = new ArrayList<>();
        for (PubExcutor pubExcutor : publicEmpJson) {
            if (pubExcutor.isStarter()) {
                empIds.addAll(Arrays.asList(createdUser.getId()));
            }
            if (pubExcutor.isFixUser()) {
                empIds.addAll(pubExcutor.fixEmpIds());
            }
        }
        return empIds;
    }

    @JSONField(serialize = false)
    public String getMutualScoreAttendRule() {
        return this.templEvaluateJson.getMutualScoreAttendRule();
    }

    public boolean usedPeerAfterSub() {
        return BusinessConstant.PEER_AFTER_SUB.equals(templEvaluateJson.getMutualScoreAttendRule());
    }

    public boolean usedPeerAndSub() {
        return BusinessConstant.PEER_AND_SUB.equals(templEvaluateJson.getMutualScoreAttendRule());
    }

    public boolean usedOnlyPeer() {
        return BusinessConstant.PEER.equals(templEvaluateJson.getMutualScoreAttendRule());
    }

    public boolean usedOnlySub() {
        return BusinessConstant.SUB.equals(templEvaluateJson.getMutualScoreAttendRule());
    }

    @JSONField(serialize = false)
    public boolean isBaseScoreOpen() {
        return Objects.nonNull(templBaseJson.getBaseScore());
    }

    @JSONField(serialize = false)
    public boolean canNotExceedFullScore() {
        return "true".equals(templBaseJson.getExceedFullScore());
    }

    public BigDecimal baseScore() {
        return templBaseJson.getBaseScore();
    }

    public ScoreNodeConf superNodeConf() {
        BigDecimal totalWeight = nodePecWeight(SubScoreNodeEnum.SUPERIOR_SCORE);
        return new ScoreNodeConf(isTypeWeightOpen(), totalWeight, isFullScoreRange());
    }

    public ScoreNodeConf peerNodeConf(boolean hasSubAudits) {
        BigDecimal totalWeight = nodePecWeight(SubScoreNodeEnum.PEER_SCORE);
        boolean useBoth = usedPeerAndSub() || usedPeerAfterSub();
        if (!hasSubAudits && useBoth) {//同时开启同级与下级评分,但下级评分人空了,转下级权重级同级
            BigDecimal subWeight = nodePecWeight(SubScoreNodeEnum.SUB_SCORE);
            totalWeight = totalWeight.add(subWeight);
        }
        return new ScoreNodeConf(isTypeWeightOpen(), totalWeight, isFullScoreRange());
    }

    public ScoreNodeConf subNodeConf() {
        BigDecimal totalWeight = nodePecWeight(SubScoreNodeEnum.SUB_SCORE);
        return new ScoreNodeConf(isTypeWeightOpen(), totalWeight, isFullScoreRange());
    }

    public ScoreNodeConf selfNodeConf() {
        BigDecimal totalWeight = nodePecWeight(SubScoreNodeEnum.SELF_SCORE);
        return new ScoreNodeConf(isTypeWeightOpen(), totalWeight, isFullScoreRange());
    }

    public ScoreNodeConf itemNodeConf() {
        return new ScoreNodeConf(isTypeWeightOpen(), Pecent.ONE, isFullScoreRange());
    }

    public boolean needSelfEval() {
        return templEvaluateJson.needSelfEval();
    }

    public boolean needMutualEval() {
        return this.templEvaluateJson.needMutualEval();
    }

    public boolean needSuperiorEval() {
        return templEvaluateJson.needSuperiorEval();
    }

    public boolean superiorInTurn() {
        return templEvaluateJson != null && templEvaluateJson.superiorInTurn();
    }

    public boolean superiorInSame() {
        return templEvaluateJson != null && templEvaluateJson.superiorInSame();
    }

    public void startAppealOn(TalentStatus talentStatus, EvalUser taskUser) {
        if (appealConf == null || !appealConf.isOpen()) {
            return;
        }
        if (appealConf.startAppealOn(talentStatus)) {
            taskUser.initAppealDeadLine(appealConf.getCanAppealDay());
//            taskUser.setAppealReceiverId(appealConf.getAppealReceiverId(taskUser.getCreatedUser()));
            return;
        }
        if (templBaseJson.startAppealOn(talentStatus)) {
            taskUser.initAppealDeadLine(templBaseJson.getCanAppealDay());
//            List<StaffConfItem> staffConfItems = JSONObject.parseArray(appealReceiver, StaffConfItem.class);
//            AppealConf appealConf = new AppealConf();
//            appealConf.setAppealReceiver(staffConfItems);
//            taskUser.setAppealReceiverId(appealConf.getAppealReceiverId(taskUser.getCreatedUser()));
        }
    }

    // 确认阶段指标审核人 MODIFY_ITEM_AUDIT
    public List<PerfTemplEvaluateAudit> modifyItemAudit() {
        return templAffirmJson.getAuditList();
    }

    //执行阶段指标审核 CHANGE_ITEM_AUDIT
    public List<PerfTemplEvaluateAudit> changeItemAudits() {
        return templExecuteJson != null ? templExecuteJson.getAuditList() : Collections.emptyList();
    }

    //查最终结果审核人流程
    public List<PerfTemplEvaluateAudit> resultAudits() {
        return templEvaluateJson.getFinalAuditList();
    }


    public List<PerfTemplEvaluateAudit> peerScorers(String empId) {
        if (isCustom()) {
            return kpiTypes.stream().flatMap(type -> type.peerAuditStream(empId))
                    .collect(Collectors.toList());
        }
        if (hasEmptyKpiType()) {
            return templEvaluateJson.getPeerScoreList(empId);
        }
        if (!hasNormalItems()) {
            return Collections.emptyList();
        }
        return templEvaluateJson.getPeerScoreList(empId);
    }

    public List<PerfTemplEvaluateAudit> subScorers(String empId) {
        if (isCustom()) {
            return kpiTypes.stream().flatMap(type -> type.subAuditStream(empId)).collect(Collectors.toList());
        }
        if (hasEmptyKpiType()) {
            return templEvaluateJson.getSubScoreList(empId);
        }
        if (!hasNormalItems()) {
            return Collections.emptyList();
        }
        return templEvaluateJson.getSubScoreList(empId);
    }

    public List<PerfTemplEvaluateAudit> superiorScorers() {
        if (isCustom()) {
            return kpiTypes.stream().flatMap(type -> type.superAuditStream()).collect(Collectors.toList());
        }
        if (hasEmptyKpiType()) {
            return templEvaluateJson.getSuperiorScoreList();
        }
        //空类别  //只有自动指标,   // 没有需要生成的指标
        if (!hasNormalItems()) {
            return Collections.emptyList();
        }
        return templEvaluateJson.getSuperiorScoreList();
    }

    public List<PerfTemplEvaluateAudit> appointScorers() {
        if (!isCustom()) {
            return Collections.emptyList();
        }
        return kpiTypes.stream().flatMap(type -> type.appointAuditStream()).collect(Collectors.toList());
    }

    private boolean hasEmptyKpiType() {
        for (PerfTemplKpiType kpiType : kpiTypes) {
            if (kpiType.isEmptyType()) {
                return true;
            }
        }
        return false;
    }

    private boolean hasNormalItems() {
        List<PerfTemplKpiItem> hasNoralItems = kpiTypes.stream().flatMap(type -> type.indicatorsStream())
                .filter(item -> item.isNormal()).collect(Collectors.toList());
        //没有正常评分的指标,就算开启了上级评分,也无需要生成上级评分结点
        return !hasNoralItems.isEmpty();
    }

    public List<String> kpiItemIds() {
        return getTemplItemJson().stream().flatMap(kpiType -> kpiType.indicatorsStream())
                .map(kpiItem -> kpiItem.getKpiItemId())
                .collect(Collectors.toList());
    }

    public boolean nonePublish() {
        if (publishResult != null && publishResult.isOpen()) {
            publishResult.isNotPublic();
        }
        return BusinessConstant.PUBLIC_TYPE_NO.equals(publicType);
    }


    public boolean totalScore(SubScoreNodeEnum scoreNodeEnum) {
        if (SubScoreNodeEnum.SELF_SCORE.getScene().equals(scoreNodeEnum.getScene())) {
            return this.templEvaluateJson != null
                    && EvaluateScoreSettingDTO.ScoreRule.TOTAL.equals(templEvaluateJson.getSelfScoreRule());
        }
        if (SubScoreNodeEnum.SUPERIOR_SCORE.getScene().equals(scoreNodeEnum.getScene())) {
            return this.templEvaluateJson != null
                    && EvaluateScoreSettingDTO.ScoreRule.TOTAL.equals(templEvaluateJson.getSuperiorScoreRule());
        }
        if (SubScoreNodeEnum.PEER_SCORE.getScene().equals(scoreNodeEnum.getScene())) {
            return this.templEvaluateJson != null
                    && EvaluateScoreSettingDTO.ScoreRule.TOTAL.equals(templEvaluateJson.getMutualScoreRule());
        }
        return false;
    }

    public void ifSignCheck(String signPicUrl) {
        if (confirmResult != null && confirmResult.needSign()) {
            throw new RuntimeException("error.sign.resultAffirm");
        }
        if (Boolean.valueOf(affirmSignature) && StrUtil.isBlank(signPicUrl)) {
            throw new RuntimeException("error.sign.resultAffirm");
        }
    }

    public ScoringStageChain.StageNode nextNodeOneByOne(ScoringNodeEnum curNode) {
        if (scoreSortConf == null) {//旧任务固定序列
            return ScoringNodeEnum.nextSim360Node(curNode);
        }
        return scoreSortConf.nextNodeOneByOne(curNode);
    }

    @Override
    public Name taskName() {
        return taskName;
    }

    @Override
    public TenantId companyId() {
        return companyId;
    }

    //兼容旧1.0没有typeRule
    public boolean mergeOkrTypeRuleIfOpt(EvalUser user, KpiListWrap okrTypes) {
        if (!isCustom() || !user.wasTempTask()) {
            return false;
        }
        okrTypes.asMap(type -> type.getKpiTypeId());
        for (PerfTemplKpiType itemEva : this.templItemJson) {
            PerfTemplItemEvaluate typeRule = itemEva.typeEvaluate();
            if (typeRule != null) {
                EmpEvalKpiType type = okrTypes.mapGet(itemEva.getTypeId());
                EvalItemScoreRule convert = Convert.convert(EvalItemScoreRule.class, typeRule);
                convert.initBaseInfo(type.getCompanyId(), new EmpId(type.getCreatedUser()), type.getTaskUserId(),
                        type.getKpiTypeId(), typeRule.getKpiItemId(), user.getTaskId());
                type.setTypeRule(convert);
            }
        }
        return true;
    }

    public boolean isOrgEval() {
        return performanceType != null && performanceType == 2;
    }
}
