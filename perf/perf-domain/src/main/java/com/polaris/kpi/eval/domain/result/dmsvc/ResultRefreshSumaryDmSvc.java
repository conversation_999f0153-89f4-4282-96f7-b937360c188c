package com.polaris.kpi.eval.domain.result.dmsvc;

import cn.hutool.core.collection.CollUtil;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.setting.domain.repo.ResultAuditFlowRepo;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class ResultRefreshSumaryDmSvc {
    private ResultAuditFlowRepo resultAuditFlowRepo;

    public ResultRefreshSumaryDmSvc(ResultAuditFlowRepo resultAuditFlowRepo) {
        this.resultAuditFlowRepo = resultAuditFlowRepo;
    }

    public void batchCreateResultAuditFlow(String companyId, String opEmpId, List<EvalUser> evalUsers) {
        List<EvalUser> filterUsers = new ArrayList<>();
        for (EvalUser evalUser : evalUsers) {
            if (!evalUser.isAuditResultCollectSend()) {//不是汇总通知,不是统一校准.
                continue;
            }
            filterUsers.add(evalUser);
        }
        if (CollUtil.isEmpty(filterUsers)) {
            return;
        }
        resultAuditFlowRepo.refreshAuditFlow(companyId, opEmpId, filterUsers);
        List<String> taskIds = evalUsers.stream()
                .map(EvalUser::getTaskId)
                .distinct()
                .collect(Collectors.toList());
        for (String taskId : taskIds){
            resultAuditFlowRepo.refreshSummary(companyId, taskId);
        }
    }

}
