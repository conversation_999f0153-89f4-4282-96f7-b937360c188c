package com.polaris.kpi.eval.domain.stage.dmsvc;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.eval.BusinessPlanItem;
import cn.com.polaris.kpi.eval.Rater;
import cn.com.polaris.kpi.eval.RaterNode;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.acl.IObItemPlanAcl;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.*;
import com.polaris.kpi.eval.domain.task.entity.interview.EvalTaskInterviewConf;
import com.polaris.kpi.eval.domain.task.repo.BatchUpdateEvalErrorStatus;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.org.domain.dept.dmsvc.EmpParser;
import com.polaris.kpi.org.domain.dept.repo.EmpFinder;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class EvalRuleStatusCheckDmsvc {
    private TenantId companyId;
    private EmpParser empParser;
    private List<EvalUser> needUpateUsers = new ArrayList<>();

    public EvalRuleStatusCheckDmsvc(TenantId companyId, EmpParser empParser) {
        this.companyId = companyId;
        this.empParser = empParser;
    }

    public void checkEvalRuleErrStatus(IObItemPlanAcl objectAcl, List<EvalUser> evalUsers) {
        for (EvalUser evalUser : evalUsers) {
            this.evalRuleStatusHasErr(evalUser, objectAcl);
        }
    }

    public void evalRuleStatusHasErr(EvalUser evalUser, IObItemPlanAcl objectAcl) {
        evalUser.setRuleConfStatus(200);
        evalUser.setRuleConfError(null);
        EmpEvalMerge empEvalRule = Convert.convert(EmpEvalMerge.class, evalUser.getEmpEvalRule());
        //EmpEvalMerge empEvalRule = new ToDataBuilder<>(evalUser.getEmpEvalRule(), EmpEvalMerge.class).data();
        if (empEvalRule == null) {
            return ;
        }
        log.debug("anewEvalRuleStatus.empEvalRule:{}", JSONUtil.toJsonStr(empEvalRule));
        TalentStatus current = TalentStatus.statusOf(evalUser.getTaskStatus());
        //任务确认阶段配置
        AffirmTaskConf confirmTask = empEvalRule.getConfirmTask();
        if (confirmTask.isOpen() && current.beforeEq(TalentStatus.CONFIRMING)) {
            parseCheckFlowError(evalUser, confirmTask.getAuditNodes(), "modify_item_audit", confirmTask.getNodeEmpVacancy());
        }
        //任务执行阶段配置
        EditExeIndiConf editExeIndi = empEvalRule.getEditExeIndi();
        if (editExeIndi.auditIsOpen() && current.beforeEq(TalentStatus.CONFIRMED)) {
            parseCheckFlowError(evalUser, editExeIndi.getAuditNodes(), "change_item_audit", editExeIndi.getNodeEmpVacancy());
        }
        //完成值审核阶段配置
        FinishValueAuditConf finishValueAudit = empEvalRule.getFinishValueAudit();
        if (finishValueAudit.isOpen() && current.beforeEq(TalentStatus.FINISH_VALUE_AUDIT)) {
            parseCheckFlowError(evalUser, finishValueAudit.getAuditNodes(), "finish_value_audit", 2);
        }
        //校准
        AuditResultConf auditResult = empEvalRule.getAuditResult();
        if (auditResult.isOpen() && current.beforeEq(TalentStatus.RESULTS_AUDITING)) {
            parseCheckFlowError(evalUser, auditResult.getAuditNodes(), "final_result_audit", auditResult.getNodeEmpVacancy());
        }
        //面谈
        InterviewConf interviewConf = empEvalRule.getInterviewConf();
        if (Objects.nonNull(interviewConf) && interviewConf.isOpen() && current.beforeEq(TalentStatus.RESULTS_INTERVIEW)) {
            EvalTaskInterviewConf evalTaskInterview = new EvalTaskInterviewConf(interviewConf, companyId, new EmpId(evalUser.getEmpId()), new EmpId(empEvalRule.getCreatedUser()));
            //执行人或者确认人为空需要提示
            if (Objects.nonNull(interviewConf.getInterviewExcutorInfo())) {
                // evalUser.confEvalPublicErro("108"); // 面谈执行人异常
                parseCheckFlowError(evalUser, Collections.singletonList(interviewConf.getInterviewExcutorInfo()), "final_result_interview_excute", interviewConf.getNodeEmpVacancy());
            }
            if (evalTaskInterview.getIsOpenConfirm() && CollUtil.isNotEmpty(interviewConf.getInterviewConfirmConf().getInterviewConfirmInfo())) {
                // 面谈确认人异常
                parseCheckFlowError(evalUser, interviewConf.getInterviewConfirmConf().getInterviewConfirmInfo(), "final_result_interview_confirm", interviewConf.getNodeEmpVacancy());
                // evalUser.confEvalPublicErro("109"); // 面谈确认人异常
            }
        }
        //阶段截止时间
        DeadLineConf deadLineConf = empEvalRule.getDeadLineConf();
        if (Objects.nonNull(deadLineConf) && deadLineConf.isOpen() && Objects.equals(current.getStatus(), "drawUpIng")) {
            if (deadLineConf.timeOuted(confirmTask.isOpen(), finishValueAudit.isOpen(), auditResult.isOpen(), Objects.nonNull(empEvalRule.getInterviewConf()) ? empEvalRule.getInterviewConf().isOpen() : false,
                    empEvalRule.getConfirmResult().isOpen(), LocalDate.now(), current)) {
                evalUser.deadLineTimeError(); // 截止时间异常
            }
        }

        /**申述异常*/
        AppealConf appealConf = empEvalRule.getAppealConf();
        if (appealConf.isOpen()) {
            if (isLackEmp(empEvalRule.parseAppealEmp(), null, 0)) {
                evalUser.confEvalPublicErro("601"); // 申述异常
            }
        }
        /**评分人异常*/
        if (current.beforeEq(TalentStatus.SCORING)) {
            evalUser.confEvalScoreRuleErro(hasNodeLeavedRater(empEvalRule.initRaters(), evalUser.getEmpId()));
        }
        if (current.beforeEq(TalentStatus.CONFIRMED)) {
            /**邀请评分人异常*/
            evalUser.confEvalInviteMutualErro(hasNodeLeavedRater(empEvalRule.getInviteMutualAuditRater(), evalUser.getEmpId()));
            /**录入人异常*/
            if (!empEvalRule.parseEntryClerkNoInput()) {
                if (empEvalRule.parseEntryClerkIsNull()) {
                    evalUser.confEvalPublicErro("701");
                } else {
                    if (isLackEmp(empEvalRule.parseEntryClerk(), 2, 0)) {
                        evalUser.confEvalPublicErro("702"); // 录入人异常
                    }
                }
            }
        }

        if (current.beforeEq(TalentStatus.FINISH_VALUE_AUDIT)) {
            //完成值审核异常
            if (!empEvalRule.kpiItemAllIsEmpt() && isLackEmp(empEvalRule.parseFinishedValueAudit(), 2, 0)) {
                evalUser.confEvalPublicErro("105"); //
            }
        }
        //经营计划指标异常
        List<BusinessPlanItem> planItemList = objectAcl.listBusinessPlanItem(evalUser, empEvalRule.getKpiTypes());
        empEvalRule.parseBusinessPlanItem(planItemList, evalUser);

        if (!evalUser.evalRuleIsErr()) {
            evalUser.confEvalRuleOk();//初始值ok
        }
        if (!evalUser.evalRuleConfIsErr()) {
            evalUser.confEvalRuleErrorOk();
        }
        needUpateUsers.add(evalUser);
    }

    /**
     * @param evalUser
     * @param auditNodes
     * @param node
     * @param nodeEmpVacancy 节点人员空缺 1:系统自动跳过  2:提示异常  3:转交给考核任务发起人
     */
    public <T extends BaseAuditNode> void parseCheckFlowError(EvalUser evalUser, List<T> auditNodes, String node, Integer nodeEmpVacancy) {
        if (auditNodes.stream().flatMap(a -> a.getRaters().stream().filter(r -> Objects.equals(r.getEmpId(), "-1"))).collect(Collectors.toList()).size() == auditNodes.size()) {
            evalUser.confEvalRuleErro(node);
            return;
        }
        for (BaseAuditNode auditNode : auditNodes) {
            if (auditNode.getRaters().isEmpty()) {
                if (Objects.equals(nodeEmpVacancy, 2) || Objects.equals(nodeEmpVacancy, 3)) {
                    evalUser.confEvalRuleErro(node);
                    break;
                }
            }
            if (isLackEmp(auditNode.getRaters().stream().map(Rater::getEmpId).collect(Collectors.toList()), nodeEmpVacancy, auditNodes.size())) {
                evalUser.confEvalRuleErro(node);
                break;
            }
        }
    }

    public boolean isLackEmp(List<String> raterEmpIds, Integer nodeEmpVacancy, Integer auditNodeSize) {
        List<KpiEmp> emps = empParser.getEmps(raterEmpIds);
        ListWrap<KpiEmp> kpiEmps = new ListWrap<>(emps).asMap(emp -> emp.getEmpId());
        for (String raterEmpId : raterEmpIds) {
            KpiEmp kpiEmp = kpiEmps.mapGet(raterEmpId);
            if (kpiEmp == null) {
                if (Objects.equals(auditNodeSize, 1) && Objects.equals(nodeEmpVacancy, 1)) {
                    return true;
                }
                if (Objects.equals(nodeEmpVacancy, 2) || Objects.equals(nodeEmpVacancy, 3)) {
                    return true;
                }
            }
        }
        return false;
    }


    public List<String> hasNodeLeavedRater(List<RaterNode> raterNodes, String opEmpId) {
        List<String> scoreErrorNode = new ArrayList<>();
        for (RaterNode raterNode : raterNodes) {
            List<String> raterEmpIds = new ArrayList<>();
            if ("self".equals(raterNode.getNode())) {
                raterEmpIds.add(opEmpId);
            } else if (("peer".equals(raterNode.getNode()) || "sub".equals(raterNode.getNode()))) {
                for (Rater rater : raterNode.getRaters()) {
                    raterEmpIds.add(Objects.equals(rater.getType(), 5) ? opEmpId : rater.getEmpId());
                }
            } else {
                if (raterNode.getRaters().isEmpty()) {
                    scoreErrorNode.add(raterNode.getNode());
                    continue;
                }
                raterEmpIds = raterNode.getRaters().stream().map(Rater::getEmpId).collect(Collectors.toList());
            }
            List<KpiEmp> emps = empParser.getEmps(raterEmpIds);
            ListWrap<KpiEmp> kpiEmps = new ListWrap<>(emps).asMap(emp -> emp.getEmpId());
            if (CollUtil.isEmpty(emps)) {
                scoreErrorNode.add(raterNode.getNode());
                continue;
            }
            for (String raterEmpId : raterEmpIds) {
                KpiEmp kpiEmp = kpiEmps.mapGet(raterEmpId);
                if (kpiEmp == null) {
                    scoreErrorNode.add(raterNode.getNode());
                }
            }
        }
        return scoreErrorNode.stream().distinct().collect(Collectors.toList());
    }

    public void saveErrStatus(BatchUpdateEvalErrorStatus empRuleRepo) {
        if (CollUtil.isNotEmpty(needUpateUsers)) {
            empRuleRepo.batchUpdateEvalErrorStatus(companyId, needUpateUsers);
        }
    }

    public static void main(String[] args) {
        ListWrap<KpiEmp> kpiEmps = new ListWrap<KpiEmp>(new ArrayList<>()).asMap(emp -> emp.getEmpId());
    }
}
