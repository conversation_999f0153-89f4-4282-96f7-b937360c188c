package com.polaris.kpi.eval.domain.task.dmsvc;

import cn.com.polaris.kpi.company.MsgSceneEnum;
import cn.com.polaris.kpi.eval.FinishValue;
import cn.com.polaris.kpi.eval.Name;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.perf.www.common.utils.date.DateTimeUtils;
import com.polaris.kpi.eval.domain.task.entity.EvalKpi;
import com.polaris.kpi.eval.domain.task.entity.EvalOnExeStage;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.FinalWeightSumScore;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.entity.empeval.FinishValueReqValidationSvc;
import com.polaris.kpi.eval.domain.task.entity.msg.MsgTodoAggregate;
import com.polaris.kpi.eval.domain.task.event.KpiItemUpdateFinishedValueEvent;
import com.polaris.kpi.eval.domain.task.event.RejectFinishValueItemSubmitEvent;
import com.polaris.kpi.eval.domain.task.event.ThisStageEnded;
import com.polaris.kpi.eval.domain.task.event.msg.BatchCancelTodo;
import com.polaris.kpi.eval.domain.task.event.msg.BatchMsgTodoAggregateEvent;
import com.polaris.kpi.eval.domain.task.event.msg.CancelTodoEvent;
import com.polaris.kpi.eval.domain.task.event.talent.exeing.BatchEndExeStageEvent;
import com.polaris.kpi.eval.domain.task.event.talent.exeing.BatchRejectValuesumbitedEvent;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.org.domain.dept.entity.CompanyConf;
import com.polaris.kpi.org.domain.emp.type.Emp;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 批量提交完成值领域服务
 * //批量加载考核人员,考核规则领域对象
 * //完成提交的对象,未来完全提交的对象分组记录
 * //未来完全提交的对象清理消息与待办,增加批量清与增加待办的业务领域
 * //所有人的指标完全提交的进下一个阶段.ÅÍØ
 * //有经营指标的,执行相关经营指标的更新业务
 */
@Slf4j
public class BatchSubmitFinishValueDmSvc {
    //泰斗微和垂天科技的不发工作通知和待办
    public static List<String> noExtMsgTodoCompanys = CycleEvalDmSvc.noExtMsgTodoCompanys;
    //    private Emp opEmp;//提交人
    private EmpId opEmpId;//提交人id
    private TenantId tenantId;
    private CompanyConf conf;
    private BigDecimal fullScoreValue;
    private ListWrap<EvalOnExeStage> waitSubmitEvals;//所有提交过来的考核
    private List<EvalOnExeStage> failed = new ArrayList<>();//存在完成值必填项未录入的
    @Getter
    private List<EvalOnExeStage> oks = new ArrayList<>();//存在完成值必填项未录入的
    @Getter
    private List<EvalOnExeStage> beEndStages = new ArrayList<>();//需要进入下个阶段
    private List<EvalOnExeStage> onStages = new ArrayList<>();//还有他人的指标没有更新要停留在执行阶段
    @Getter
    private List<EvalOnExeStage> recomutedKpiScores = new ArrayList<>();//重新计算了指标自动分的考核

    @Getter
    private List<FinishValue> planItemValues = new ArrayList<>();//isPlanItem
    //    private List<FinishValue> kpiItemValues = new ArrayList<>();//非绩效指标
    private List<CancelTodoEvent> cancelTodos = new ArrayList<>();//
    private List<RejectFinishValueItemSubmitEvent> rejectFinishValueItemSubmitEvents = new ArrayList<>();//
    private List<ThisStageEnded> endeds = new ArrayList<>();//
    private List<String> batchItemUpdateTaskUserIds = new ArrayList<>();
    private List<MsgTodoAggregate> msgTodoAggregates = new ArrayList<>();

    public BatchSubmitFinishValueDmSvc(EmpId opEmp, CompanyConf conf, BigDecimal fullScoreValue, ListWrap<EvalOnExeStage> waitSubmitEvals) {
        this.opEmpId = opEmp;
        this.tenantId = new TenantId(conf.getCompanyId());
        this.conf = conf;
        this.waitSubmitEvals = waitSubmitEvals;
        for (EvalOnExeStage eval : waitSubmitEvals.getDatas()) {
            eval.getEmpEval().setCompanyConf(conf);
        }
        this.fullScoreValue = fullScoreValue;
    }

    public void submitFinishValueOne(String taskUserId, List<FinishValue> finishValues) {
        EvalOnExeStage onExeEval = waitSubmitEvals.mapGet(taskUserId);
        FinishValueReqValidationSvc checker = new FinishValueReqValidationSvc(onExeEval.getEvalUser().getKpiTypes());
        if (!checker.allItemsCheckPassed(finishValues)) {  // 校验维度和指标上的完成值必填
            log.error("存在完成值必填项未录入！taskUserId为:{}", taskUserId);
            failed.add(onExeEval);
            return;
        }
        onExeEval.saveFinishValue(opEmpId.getId(), conf, finishValues);//提交完成值并更新
        oks.add(onExeEval);
        //校验该录入人指标是否已全部提交,如果未全部提交仅更新，不进行后续操作【需特殊处理同一個指标多个录入人，其他人需要撤销待办】
        if (onExeEval.hasSomeWaitSubmitItem()) {//多人录入同一个指标，撤销其他人待办，仅提交部分
            log.info("录入人指标未全部提交仅更新,不进行后续操作【需特殊处理同一個指标多个录入人，其他人需要撤销待办log】,当前操作人:resultInputEmpId:{}", opEmpId.getId());
            this.cancelOtherTodoOpt(onExeEval);
            onStages.add(onExeEval);
            return;
        }
        appendCancelMsg(onExeEval, opEmpId);
        asyncSubmitFinishValueEvent(onExeEval);
        List<FinishValue> itemFinishValues = onExeEval.planItem(finishValues);
        this.planItemValues.addAll(itemFinishValues);
        if (this.doComputed(onExeEval)) {//执行阶段为啥要计算评分呢?
            log.info("自动计算分taskUserId为:{}", taskUserId);
            recomutedKpiScores.add(onExeEval);
        }
        if (onExeEval.beEndThisStage()) {
            beEndStages.add(onExeEval);
            endeds.add(onExeEval.endThisStage());//收集修改通知状态。adminTaskRepo.updateInputNotifyJobStatus(cmd.getTenantId().getId(), StrUtil.splitTrim(cmd.getTaskUserId(), ","));
        }
    }


    private boolean doComputed(EvalOnExeStage onExeEval) {
        EvalUser evalUser = onExeEval.getEvalUser();
        EmpEvalMerge empEvalRule = onExeEval.getEmpEval();
        empEvalRule.setSystemFullScore(fullScoreValue);
        boolean computed = evalUser.tryComputeAutoScore(empEvalRule.getTypeWeightConf().isOpen(),
                empEvalRule.getScoreValueConf().getCustomFullScore(), empEvalRule.getScoreValueConf().isFullScoreRange(), empEvalRule.openItemAutoScoreMultiplWeight());
        if (computed) {
            SumScorerComputeDmSvc computeDmSvc = new SumScorerComputeDmSvc(evalUser, empEvalRule);
            computeDmSvc.computeSumScore();//提交完成值后需计算总分，环节总分，维度总分，指标总分
          //  FinalWeightSumScore weightSumScore = empEvalRule.computeFinalScore(evalUser.getFinalItemAutoScore(), evalUser.isOpenAvgWeightCompute());
            //evalUser.computeLevel(weightSumScore, empEvalRule.getFinalScore(), empEvalRule.needComputeLevel());
        }
        return computed;
    }

    public List<EvalOnExeStage> listReComputedKpiScoreInScoring() {
        List<EvalOnExeStage> onScoring = new ArrayList<>();
        ////重新计算了指标自动分的考核
        if (CollUtil.isEmpty(recomutedKpiScores)) {
            return new ArrayList<>();
        }
        recomutedKpiScores.stream().filter(reComputedKpiScore -> reComputedKpiScore.getEvalUser().isScoring()).forEachOrdered(reComputedKpiScore -> {
            onScoring.add(reComputedKpiScore);
            log.info("在评价中，重新计算了指标自动分的考核,taskUserId为:{}", reComputedKpiScore.getEvalUser().getId());
        });
        return onScoring;
    }

    public List<EvalOnExeStage> listReComputedKpiScoreInConfirmed() {
        List<EvalOnExeStage> confirmed = new ArrayList<>();
        ////重新计算了指标自动分的考核
        if (CollUtil.isEmpty(recomutedKpiScores)) {
            return new ArrayList<>();
        }
        recomutedKpiScores.stream().filter(reComputedKpiScore -> !reComputedKpiScore.getEvalUser().isScoring()).forEachOrdered(reComputedKpiScore -> {
            confirmed.add(reComputedKpiScore);
            log.info("在执行中，重新计算了指标自动分的考核,taskUserId为:{}", reComputedKpiScore.getEvalUser().getId());
        });
        return confirmed;
    }

    //这是坏代码
    private boolean cancelOtherTodoOpt(EvalOnExeStage onExeEval) {
        EvalUser evalUser = onExeEval.getEvalUser();
        // //校验该录入人指标是否已全部提交,如果未全部提交仅更新，不进行后续操作【需特殊处理同一個指标多个录入人，其他人需要撤销待办】
        boolean canClear = TalentStatus.CONFIRMED.getStatus().equals(evalUser.getTaskStatus()) && Objects.nonNull(opEmpId) && evalUser.getOpEmpExistsItemNotSubmitFinishValue();
        if (!canClear) {
            return false;
        }
        EmpEvalMerge empEvalRule = onExeEval.getEmpEval();
        /**可能存在多个录入人，如果其他录入人已提交需清理*/
        List<String> submitItemIds = onExeEval.getKpiItemValues().stream().map(item -> item.getId()).collect(Collectors.toList());
        List<String> resultInputEmpIds = empEvalRule.getCurItemAllSubmitInputEmpIds(opEmpId.getId(), submitItemIds);
        log.info("录入人指标未全部提交仅更新,不进行后续操作【需特殊处理同一個指标多个录入人，其他人需要撤销待办log】,resultInputEmpIds:{}", resultInputEmpIds);
        if (CollUtil.isEmpty(resultInputEmpIds)) {
            return false;
        }
        //特殊场景取消代办
        cancelTodos.add(new CancelTodoEvent(evalUser.getCompanyId(), resultInputEmpIds, evalUser.getId(), MsgSceneEnum.TASK_SUBMIT_PROGRESS.getType()));
        return true;
    }

    //此处设计有问题:应设计为完成值已提交事件, 在完成值审批模块进行监听,不扩散修改更多代码先放置,这是坏代码.@小秋
    private void asyncSubmitFinishValueEvent(EvalOnExeStage onExeEval) {
        List<EvalKpi> rejectEvalKpis = onExeEval.listRejectKpiItem();
        TenantId companyId = onExeEval.getAdminTask().getCompanyId();
        EvalUser evalUser = onExeEval.getEvalUser();
        if (CollUtil.isNotEmpty(rejectEvalKpis)) {
            //所有的驳回指标提交才进完成值审核
            if (this.isAllRejectKpiSubmited(rejectEvalKpis)) {
                RejectFinishValueItemSubmitEvent event = new RejectFinishValueItemSubmitEvent(companyId.getId(), evalUser.getId(), evalUser, opEmpId, rejectEvalKpis);
                rejectFinishValueItemSubmitEvents.add(event);
            }
            //取消驳回待办
            CancelTodoEvent cancelTodoEvent = new CancelTodoEvent(companyId, evalUser.getId(), Collections.singletonList(opEmpId.getId()), Collections.singleton(MsgSceneEnum.REJECT_FINISH_VALUE.getType()));
            cancelTodos.add(cancelTodoEvent);
        } else {
            batchItemUpdateTaskUserIds.add(evalUser.getId());
        }
    }

    public void batchSendFinishValueChanged(EvalOnExeStage onExeEval, Emp opEmp) {
        AdminTask adminTask = onExeEval.getAdminTask();
        Set<String> recEmpIds = adminTask.inputChangedRecEmpIds();
        Name taskName = new Name(adminTask.getTaskName());
        EvalUser taskUser = onExeEval.getEvalUser();
        if (CollUtil.isEmpty(recEmpIds)) {
            return;
        }
        recEmpIds.remove(opEmpId);
        MsgTodoAggregate msgSend = new MsgTodoAggregate(taskUser.getCompanyId(), taskUser.getTaskId(), taskName, taskUser.getEmpId(), taskUser.getId())
                .useScene(MsgSceneEnum.FINISH_VALUE_CHANGED)
                .addExtTempValue("evalEmpName",taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                .addExtTempValue("inputName", opEmp.getName())
                .addExtTempValue("updateDate", DateTimeUtils.date2StrDate(new Date()))
                .addExtTempValue("updateDetails", taskUser.getInputFinishChanged());
        if (StrUtil.isBlank(taskUser.getInputFinishChanged())) {
            return;
        }
        if (noExtMsgTodoCompanys.contains(taskUser.getCompanyId().getId())) {
            msgSend.addCenterMsg().addRecEmpId(recEmpIds);
        } else {//只发送通知
            msgSend.sendExtMsg().addRecEmpId(recEmpIds);
        }
        msgTodoAggregates.add(msgSend);
    }

    private boolean isAllRejectKpiSubmited(List<EvalKpi> rejectEvalKpis) {
        return rejectEvalKpis.stream().allMatch(EvalKpi::isFinalSubmit);
    }

    //提交完成值过业务发生过程中生成的各事件. 但这种设计并不很好. 提交完成值应该只包含完成值提交,
    //不包含取消代办,这些业务内容应该是在提交后发生的. 订阅完成值已提交事件中处理.不扩散修改先放这 这是坏代码
    public void publishEevent() {
        //cancel
        if (CollUtil.isNotEmpty(cancelTodos)) {
            new BatchCancelTodo(tenantId, cancelTodos, opEmpId).fire();
        }
        //end
        if (CollUtil.isNotEmpty(endeds)) {
            new BatchEndExeStageEvent(tenantId, endeds, opEmpId).fire();
        }
        //batchItemUpdateTaskUserIds
        if (CollUtil.isNotEmpty(batchItemUpdateTaskUserIds)) {
            new KpiItemUpdateFinishedValueEvent(tenantId, batchItemUpdateTaskUserIds, opEmpId).fire();//提交
        }
        if (CollUtil.isNotEmpty(rejectFinishValueItemSubmitEvents)) {
            new BatchRejectValuesumbitedEvent(tenantId, rejectFinishValueItemSubmitEvents, opEmpId).fire();
        }

        if (CollUtil.isNotEmpty(msgTodoAggregates)) {
            new BatchMsgTodoAggregateEvent(tenantId, msgTodoAggregates, opEmpId).fire();
        }
    }

    public void appendCancelMsg(EvalOnExeStage onExeEval,EmpId newOpEmpId) {
        EmpEvalMerge empEvalRule = onExeEval.getEmpEval();
        EvalUser evalUser = onExeEval.getEvalUser();
        /**可能存在多个录入人，清除待办的时候，统一清除*/
        List<String> resultInputEmpIds = empEvalRule.getResultInputEmpIds(opEmpId.getId());
        //取消代办
        cancelTodos.add(new CancelTodoEvent(tenantId, resultInputEmpIds, evalUser.getId(), MsgSceneEnum.TASK_SUBMIT_PROGRESS.getType()));
        if (Objects.isNull(newOpEmpId)) {
            cancelTodos.add(new CancelTodoEvent(tenantId, evalUser.getId(), MsgSceneEnum.TASK_SUBMIT_AUTO_ITEM_PROGRESS.getType()));
            return;
        }
        cancelTodos.add(new CancelTodoEvent(tenantId, resultInputEmpIds, evalUser.getId(), MsgSceneEnum.TASK_SUBMIT_AUTO_ITEM_PROGRESS.getType()));
    }


}
