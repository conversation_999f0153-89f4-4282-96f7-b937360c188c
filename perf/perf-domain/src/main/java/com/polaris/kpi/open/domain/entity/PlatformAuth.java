package com.polaris.kpi.open.domain.entity;

import cn.hutool.crypto.digest.MD5;
import com.polaris.kpi.common.Domain;
import lombok.Getter;

import java.beans.ConstructorProperties;
import java.util.Calendar;

/**
 * 外部平台鉴权信息实体
 * 用于/tenant/openDataPush接口的鉴权
 */
@Getter
public class PlatformAuth extends Domain {
    
    private String platId;
    private String platSecret;
    private Long timestamp;

    @ConstructorProperties({"platId", "platSecret", "timestamp"})
    public PlatformAuth(String platId, String platSecret, Long timestamp) {
        this.platId = platId;
        this.platSecret = platSecret;
        this.timestamp = timestamp;
    }
    
    /**
     * 生成MD5签名
     * 签名规则：MD5(appSecret + timestamp + appId)
     * @param timestamp 时间戳
     * @return 签名
     */
    public String generateSignature(Long timestamp) {
        if (this.platSecret == null || this.platId == null) {
            throw new IllegalStateException("platSecret 或 platId 为空，请检查配置");
        }
        
        MD5 md5 = MD5.create();
        String signString = this.platSecret + timestamp + this.platId;
        return md5.digestHex(signString);
    }
    
    /**
     * 验证签名
     * @param signature 待验证的签名
     * @param timestamp 时间戳
     * @return 验证结果
     */
    public boolean verifySignature(String signature, Long timestamp) {
        // 检查时间戳是否在有效期内（72小时）
        long currentTime = Calendar.getInstance().getTimeInMillis();
        if (currentTime - timestamp > 259200000L) {
            return false;
        }
        
        // 生成期望的签名
        String expectedSignature = generateSignature(timestamp);
        return expectedSignature.equals(signature);
    }
}
