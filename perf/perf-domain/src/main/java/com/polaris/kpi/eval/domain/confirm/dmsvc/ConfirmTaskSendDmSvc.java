package com.polaris.kpi.eval.domain.confirm.dmsvc;

import cn.com.polaris.kpi.company.MsgSceneEnum;
import cn.com.polaris.kpi.eval.Name;
import cn.hutool.core.collection.CollUtil;
import com.perf.www.common.em.CompanyMsgActionEnum;
import com.polaris.kpi.eval.domain.confirm.entity.ConfirmDispatchNodeRs;
import com.polaris.kpi.eval.domain.task.dmsvc.CycleEvalDmSvc;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.msg.MsgTodoAggregate;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.org.domain.dept.repo.MsgCenterRepo;
import com.polaris.sdk.type.TenantId;

import java.util.Set;

public class ConfirmTaskSendDmSvc {
    private MsgCenterRepo centerRepo;

    public ConfirmTaskSendDmSvc(MsgCenterRepo centerRepo) {
        this.centerRepo = centerRepo;
    }

    public void sendDispatched(TenantId tenantId, AdminTask task, EvalUser empEval,ConfirmDispatchNodeRs dispatchNodeRs) {
        Set<String> getRecEmpIds = dispatchNodeRs.dispatchRaterIds();
        // 给确认者发通知与消息
        MsgSceneEnum msgScene = dispatchNodeRs.getNode().getApprovalOrder() > 1 ? MsgSceneEnum.TASK_CONFIRM : MsgSceneEnum.TASK_ENACT;
        new MsgTodoAggregate(tenantId, task.getId(), new Name(task.getTaskName()), empEval.getEmpId(), empEval.getId())
                .useScene(msgScene, CompanyMsgActionEnum.CONFIRM)
                .addExtTempValue("evalEmpName", empEval.getEvalOrgName() == null ? empEval.getEmpName() : empEval.getEvalOrgName())
                .addExtTempValue("deadLineDate", empEval.joinDeadLineStr(TalentStatus.CONFIRMING.getStatus()))
                .addTodoItem("msg.task.emp", empEval.getEvalOrgName() == null ? empEval.getEmpName() : empEval.getEvalOrgName())
                .sendExtMsg().addCenterMsg().sendExtTodo()
                .addRecEmpId(getRecEmpIds).publish();
    }
    //结束时发抄送
    public void batchSendAffirmTask(AdminTask adminTask, EvalUser taskUser, Set<String> recEmpIds) {
        if (CollUtil.isEmpty(recEmpIds)) {
            return;
        }
        MsgTodoAggregate msgSend = new MsgTodoAggregate(taskUser.getCompanyId(), taskUser.getTaskId(), new Name(adminTask.getTaskName()), taskUser.getEmpId(), taskUser.getId())
                .useScene(MsgSceneEnum.TASK_CONFIRM_FINISH)
                .addExtTempValue("evalEmpName", taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                .addExtTempValue("remark", "指标审核通过，请查看");
        if (CycleEvalDmSvc.noExtMsgTodoCompanys.contains(taskUser.getCompanyId().getId())) {
            msgSend.addCenterMsg().addRecEmpId(recEmpIds).publish();//只发内部待办
        } else {//只发送通知
            msgSend.sendExtMsg().addRecEmpId(recEmpIds).publish();
        }
    }
}
