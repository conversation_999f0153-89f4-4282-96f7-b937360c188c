package com.polaris.kpi.extData.domain.type.encAndDec;

import com.polaris.kpi.common.KpiI18NException;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * @Author: xuxw
 * @Date: 2025/03/06 09:53
 * @Description:
 */
public class Aes extends EncDecAdapter {

    private static final String AES = "AES";
    private SecretKey secretKey;

    @Override
    public String enc(String plaintext){
        try {
            Cipher cipher = Cipher.getInstance(AES);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            byte[] encryptedBytes = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            throw new KpiI18NException("enc.error", e.toString());
        }

    }

    @Override
    public String dec(String ciphertext){
        try {
            Cipher cipher = Cipher.getInstance(AES);
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(ciphertext));
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new KpiI18NException("dec.error", e.toString());
        }
    }

    private static SecretKey generateAesKey(){
        KeyGenerator keyGen = null;
        try {
            keyGen = KeyGenerator.getInstance(AES);
        } catch (NoSuchAlgorithmException e) {
            throw new KpiI18NException("getKey.error", e.toString());
        }
        keyGen.init(128);
        return keyGen.generateKey();
    }

    public Aes(){
        this.secretKey = this.generateAesKey();
    }
}
