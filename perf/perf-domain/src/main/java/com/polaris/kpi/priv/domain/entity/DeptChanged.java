package com.polaris.kpi.priv.domain.entity;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.*;

@Getter
@Setter
public class DeptChanged {

    public static final String SEPARATOR = "|";
    private String orgId;

    private String status;

    private String pathBefore;      //原始部门路径

    private String pathAfter;      //新部门路径

    private String companyId;

    public Collection<String> beforeDelOrgIds() {
        Collection del = CollUtil.subtract(StrUtil.splitTrim(this.pathBefore, SEPARATOR), StrUtil.splitTrim(this.pathAfter, SEPARATOR));
        del.remove(this.orgId);
        return del;
    }

    public Collection<String> afterAddOrgIds() {
        Collection add = CollUtil.subtract(StrUtil.splitTrim(this.pathAfter, SEPARATOR), StrUtil.splitTrim(this.pathBefore, SEPARATOR));
        add.remove(this.orgId);
        return add;
    }
}
