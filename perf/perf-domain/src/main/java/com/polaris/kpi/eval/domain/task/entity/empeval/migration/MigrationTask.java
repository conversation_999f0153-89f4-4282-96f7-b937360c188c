package com.polaris.kpi.eval.domain.task.entity.empeval.migration;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicLong;

import com.polaris.kpi.eval.domain.task.entity.MigrationConfig;

/**
 * 迁移任务领域对象
 * 封装迁移任务的核心业务逻辑和状态管理
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-27
 */
@Getter
@Setter
@Slf4j
public class MigrationTask {
    
    /**
     * 迁移任务ID
     */
    private String taskId;
    
    /**
     * 迁移类型
     */
    private MigrationType migrationType;
    
    /**
     * 任务状态
     */
    private MigrationStatus status;
    
    /**
     * 公司ID
     */
    private String companyId;
    
    /**
     * 操作人员ID
     */
    private String operatorId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 完成时间
     */
    private LocalDateTime completeTime;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;
    
    /**
     * 总记录数
     */
    private final AtomicLong totalRecords = new AtomicLong(0);
    
    /**
     * 已处理记录数
     */
    private final AtomicLong processedRecords = new AtomicLong(0);
    
    /**
     * 成功记录数
     */
    private final AtomicLong successRecords = new AtomicLong(0);
    
    /**
     * 失败记录数
     */
    private final AtomicLong failureRecords = new AtomicLong(0);
    
    /**
     * 迁移配置
     */
    private MigrationConfig config;
    
    /**
     * 迁移进度
     */
    private MigrationProgress progress;
    
    /**
     * 迁移统计
     */
    private MigrationStatistics statistics;
    /**
     * 分片索引（用于并发迁移）
     */
    private Integer shardIndex;

    /**
     * 总分片数（用于并发迁移）
     */
    private Integer shardTotal;

    /**
     * 是否开启迁移过期时间是21年份的[0-不开启，1=开启]
     */
    private int openMigration21 = 0;

    /**
     * 迁移类型枚举
     */
    public enum MigrationType {
        FINISHED("已完成任务"),
        NO_FINISHED("未完成任务"),
        MIXED("混合任务");
        
        private final String description;
        
        MigrationType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 迁移状态枚举
     */
    public enum MigrationStatus {
        PENDING("待处理"),
        RUNNING("运行中"),
        PAUSED("已暂停"),
        COMPLETED("已完成"),
        FAILED("失败"),
        STOPPED("已停止");
        
        private final String description;
        
        MigrationStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    // 修改构造函数（第148行）
    public MigrationTask(String taskId, MigrationType migrationType, String companyId,
                         String operatorId, Integer shardIndex, Integer shardTotal) {
        this.taskId = taskId;
        this.migrationType = migrationType;
        this.companyId = companyId;
        this.operatorId = operatorId;
        this.shardIndex = shardIndex;
        this.shardTotal = shardTotal;
        this.status = MigrationStatus.PENDING;
        this.createTime = LocalDateTime.now();
        this.lastUpdateTime = LocalDateTime.now();
        this.progress = new MigrationProgress();
        this.statistics = new MigrationStatistics();
    }
    /**
     * 构造函数
     */
    public MigrationTask(String taskId, MigrationType migrationType, String companyId, String operatorId) {
        this.taskId = taskId;
        this.migrationType = migrationType;
        this.companyId = companyId;
        this.operatorId = operatorId;
        this.status = MigrationStatus.PENDING;
        this.createTime = LocalDateTime.now();
        this.lastUpdateTime = LocalDateTime.now();
        this.progress = new MigrationProgress();
        this.statistics = new MigrationStatistics();
    }
    
    /**
     * 启动迁移任务
     */
    public void start() {
        if (status != MigrationStatus.PENDING && status != MigrationStatus.PAUSED) {
            throw new IllegalStateException("迁移任务状态不允许启动: " + status);
        }
        
        this.status = MigrationStatus.RUNNING;
        this.startTime = LocalDateTime.now();
        this.lastUpdateTime = LocalDateTime.now();
        
        log.info("迁移任务启动: taskId={}, type={}, companyId={}", taskId, migrationType, companyId);
    }
    
    /**
     * 暂停迁移任务
     */
    public void pause() {
        if (status != MigrationStatus.RUNNING) {
            throw new IllegalStateException("迁移任务状态不允许暂停: " + status);
        }
        
        this.status = MigrationStatus.PAUSED;
        this.lastUpdateTime = LocalDateTime.now();
        
        log.info("迁移任务暂停: taskId={}", taskId);
    }
    
    /**
     * 完成迁移任务
     */
    public void complete() {
        if (status != MigrationStatus.RUNNING && status != MigrationStatus.PAUSED) {
            throw new IllegalStateException("迁移任务状态不允许完成: " + status);
        }
        
        this.status = MigrationStatus.COMPLETED;
        this.completeTime = LocalDateTime.now();
        this.lastUpdateTime = LocalDateTime.now();
        
        log.info("迁移任务完成: taskId={}, total={}, success={}, failure={}", 
                taskId, totalRecords.get(), successRecords.get(), failureRecords.get());
    }
    
    /**
     * 失败迁移任务
     */
    public void fail(String errorMessage) {
        this.status = MigrationStatus.FAILED;
        this.lastUpdateTime = LocalDateTime.now();
        
        log.error("迁移任务失败: taskId={}, error={}", taskId, errorMessage);
    }
    
    /**
     * 更新处理进度
     */
    public void updateProgress(long processed, long success, long failure) {
        this.processedRecords.set(processed);
        this.successRecords.set(success);
        this.failureRecords.set(failure);
        this.lastUpdateTime = LocalDateTime.now();
        
        // 更新进度信息
        if (progress != null) {
            progress.updateProgress(processed, totalRecords.get());
        }
        
        // 更新统计信息
        if (statistics != null) {
            statistics.updateStatistics(processed, success, failure);
        }
    }
    
    /**
     * 设置总记录数
     */
    public void setTotalRecords(long total) {
        this.totalRecords.set(total);
        if (progress != null) {
            progress.setTotalRecords(total);
        }
    }
    
    /**
     * 获取进度百分比
     */
    public double getProgressPercentage() {
        if (totalRecords.get() == 0) {
            return 0.0;
        }
        return (double) processedRecords.get() / totalRecords.get() * 100;
    }
    
    /**
     * 检查是否可以启动
     */
    public boolean canStart() {
        return status == MigrationStatus.PENDING || status == MigrationStatus.PAUSED;
    }
    
    /**
     * 检查是否可以暂停
     */
    public boolean canPause() {
        return status == MigrationStatus.RUNNING;
    }
    
    /**
     * 检查是否可以完成
     */
    public boolean canComplete() {
        return status == MigrationStatus.RUNNING || status == MigrationStatus.PAUSED;
    }
    
    /**
     * 检查是否可以恢复
     */
    public boolean canResume() {
        return status == MigrationStatus.PAUSED;
    }
    
    /**
     * 检查是否已完成
     */
    public boolean isCompleted() {
        return status == MigrationStatus.COMPLETED;
    }
    
    /**
     * 检查是否失败
     */
    public boolean isFailed() {
        return status == MigrationStatus.FAILED;
    }
    
    /**
     * 检查是否运行中
     */
    public boolean isRunning() {
        return status == MigrationStatus.RUNNING;
    }
    
    /**
     * 获取任务运行时长（秒）
     */
    public long getRunningDurationSeconds() {
        if (startTime == null) {
            return 0;
        }
        
        LocalDateTime endTime = completeTime != null ? completeTime : LocalDateTime.now();
        return java.time.Duration.between(startTime, endTime).getSeconds();
    }
    
    /**
     * 获取处理速度（记录/秒）
     */
    public double getProcessingSpeed() {
        long duration = getRunningDurationSeconds();
        if (duration == 0) {
            return 0.0;
        }
        return (double) processedRecords.get() / duration;
    }
    
    /**
     * 估算剩余时间（秒）
     */
    public long getEstimatedRemainingTimeSeconds() {
        double speed = getProcessingSpeed();
        if (speed == 0) {
            return 0;
        }
        
        long remaining = totalRecords.get() - processedRecords.get();
        return (long) (remaining / speed);
    }
} 