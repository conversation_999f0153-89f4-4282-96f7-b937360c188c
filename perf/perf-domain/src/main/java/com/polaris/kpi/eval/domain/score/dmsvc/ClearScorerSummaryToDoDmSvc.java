package com.polaris.kpi.eval.domain.score.dmsvc;

import cn.hutool.core.collection.CollUtil;
import com.polaris.acl.msg.domain.FinishWorkReq;
import com.polaris.acl.msg.face.MsgAcl;
import com.polaris.kpi.eval.domain.task.repo.ScorerSummaryTodoRepo;
import com.polaris.kpi.setting.domain.entity.ScorerTodoSummary;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.MapWrap;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
public class ClearScorerSummaryToDoDmSvc {
    private ScorerSummaryTodoRepo summaryTodoRepo;
    private MsgAcl msgAcl;

    public ClearScorerSummaryToDoDmSvc(ScorerSummaryTodoRepo summaryTodoRepo, MsgAcl msgAcl) {
        this.summaryTodoRepo = summaryTodoRepo;
        this.msgAcl = msgAcl;
    }

    public void clear(List<String> taskUserIds, String companyId, String taskId) {
        MapWrap<String, ScorerTodoSummary> mapWrap = summaryTodoRepo.listScorerTodoSummary(taskUserIds, companyId, taskId);
        if (CollUtil.isEmpty(mapWrap.getDatas())) {
            return;
        }
        for (ScorerTodoSummary summary : mapWrap.getDatas()) {
            CompletableFuture.runAsync(() -> {
                if (summary.readyToClose()) {
                    FinishWorkReq req = new FinishWorkReq(new TenantId(companyId), new EmpId(summary.getScorerId()), summary.getThirdMsgId());
                    try {
                        msgAcl.finishTodoWork(req);
                    } catch (Exception e) {
                        log.error("完成待办失败:" + e.getMessage(), e);
                    }
                }
            });
        }
        summaryTodoRepo.batchUpdateSummaries(mapWrap.getDatas());
    }
}
