package com.polaris.kpi.eval.domain.task.repo;

import cn.com.polaris.kpi.eval.KpiItemUsedField;
import com.polaris.kpi.eval.domain.task.entity.EvalAudit;
import com.polaris.kpi.eval.domain.task.entity.EvalItemScoreRule;
import com.polaris.kpi.eval.domain.task.entity.EvalKpi;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.empeval.BaseEmpEvalRule;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalOperation;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalRule;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.DeadLineConf;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;

import java.util.Collection;
import java.util.List;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.domain.task.repo
 * @Author: lufei
 * @CreateTime: 2022-09-01  22:46
 * @Description: 考核表Repo
 * @Version: 1.0
 */
public interface EmpEvalRuleRepo {
    //考核规则
    EmpEvalRule getEmpEvalRule(TenantId companyId, String taskUserId);

    EmpEvalMerge getEmpEvalMerge(TenantId companyId, String taskUserId, int mod);

    EmpEvalMerge getScoreEmpEvalMerge(TenantId companyId,String taskId, String taskUserId);

    EmpEvalMerge getEmpEvalMerge(TenantId companyId, String taskUserId, String opEmpId, int mod, int logMod);

    EmpEvalMerge getScoreEmpEvalDetail(TenantId companyId, String taskUserId, String opEmpId);

    EmpEvalMerge getScoreEmpEvalDetailV3(TenantId companyId, String taskUserId, String opEmpId);

    void addEmpEvalRule(EvalUser evalUser, EmpEvalRule evalRule, EmpEvalOperation operation, boolean refreshAudit);

    void updateEvalRuleStatus(String companyId, String taskUserId, Integer ruleConfStatus, String ruleConfError);

    void batchUpdateEvalRuleStatus(String companyId, List<String> taskUserIds, Integer ruleConfStatus, String ruleConfError);
    void updateItemUserdField(String companyId, String empEvalId, List<KpiItemUsedField> fields);

    void editEmpEvalRule(EvalUser evalUser, TalentStatus toStage);

    void editTempTaskAsRule(EvalUser evalUser, EmpEvalRule evalRule);

    void saveDiffLog(EmpEvalOperation operation);

    void batchSaveDiffLog(List<EmpEvalOperation> operations);

    void editScoreStageConf(EmpEvalRule rule, String taskId, String empId, List<String> raterNameIds,EvalUser evalUser);

    void editScoreStageConf(EmpEvalRule rule, String taskId, String empId, List<String> raterNameIds,
                            boolean refreshAudit, EvalUser evalUser);

    void editEmpEvalRule(EmpEvalRule newEmpEvalRule);

    void updateEmpEvalRule(BaseEmpEvalRule rule);

    void updateDeadLine(String taskId, EmpEvalRule rule);

    void updateSuperiorScoreOrder(TenantId tenantId, String empEvalId,String superiorScoreOrder);

    void updateSuperiorScoreOrderBatch(TenantId tenantId, List<EmpEvalRule> rules);

    void editDeadLineConf(TenantId tenantId, EmpEvalRule rule);

    void initiator(TenantId tenantId, List<String> taskUserIds, EmpId opEmpId);

    EmpEvalRule getBaseEmpEvalRule(TenantId tenantId, String empEvalId);

    void editExeIndiConf(EmpEvalRule rule, String taskId, String empId);

    void editConfirmConf(EmpEvalRule rule, String taskId, String empId);

    void editFinishedValueAuditConf(EmpEvalRule rule, String taskId, String empId, List<EvalKpi> items);

    void editAuditResultConf(EmpEvalRule rule, String taskId, String empId);

    void editInterviewResultConf(EmpEvalRule rule);

    void applyTemp(EvalUser user, EmpEvalRule empEvalRule, EmpEvalOperation operation);

    void applyTempBatch(TenantId companyId, List<EvalUser> users, List<EmpEvalOperation> operations);

    void batchAddEmpEvalRule(List<EvalUser> evalUsers, List<EmpEvalOperation> operations, boolean refreshAudit);

    void applyChangeEval(List<EvalUser> users);

    void clearRuleConf(TenantId companyId, String userId);

    void customItemFlow(EmpEvalRule empEvalRule, String taskId, String empId);

    void customItemFlow(EmpEvalRule empEvalRule, String taskId, String empId, boolean indexCofMdfEmp);

    void updateMutualRule(Collection<EvalItemScoreRule> changedRules);

    void addItemRule(EvalItemScoreRule custRule);

    void updateIndexRaters(EmpEvalMerge forIndexRaters);

    void updateInterviewConf(EmpEvalRule empEvalRule);

    void updateTransferScoringIf(EmpEvalMerge empEvalMerge);

    void updateTransferScoringIfV3(EmpEvalMerge empEvalMerge);

    void addFormulaFields(EmpEvalRule evalRule);

    void delEmpEvalRule(TenantId companyId, String taskUserId);

    void delEmpEvalRule(TenantId companyId, List<String> taskUserIds);

    List<EvalAudit> saveReusltAudit(EmpEvalRule rule, String taskUserId, String empId, String taskId, Integer level);

    void updateShowResultType(TenantId tenantId, List<String> taskUserIds, Integer showResultType);

    List<EvalScoreResult> loadEvalScoreRs(TenantId companyId, String taskUserId);
}
