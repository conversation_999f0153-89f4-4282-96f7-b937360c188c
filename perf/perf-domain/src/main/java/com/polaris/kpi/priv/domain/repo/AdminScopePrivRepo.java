package com.polaris.kpi.priv.domain.repo;


import java.util.Collection;
import java.util.List;

public interface AdminScopePrivRepo {

    void invalidAdminScopePriv(String companyId, Collection<String> groupIds, Collection<String> orgIds);

    void addAdminScopePrivOrg(List<String> groupIds,Collection<String> orgIds,String companyId);

    void delAdminScopePrivOrg(String companyId,List<String> groupIds);

    void delAdminScopePrivGroupOrg(String companyId,List<String> groupIds,Collection<String> orgIds);

    void delAdminScopePriv(String companyId,List<String> groupIds);

    String addAdminScopePrivGroup(String companyId,String adminEmpId,String adminEmpName);
}
