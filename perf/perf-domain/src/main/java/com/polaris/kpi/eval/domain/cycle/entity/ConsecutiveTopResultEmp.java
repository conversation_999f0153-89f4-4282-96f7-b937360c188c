package com.polaris.kpi.eval.domain.cycle.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.common.Domain;
//import com.polaris.kpi.eval.domain.cycle.type.ConsecutiveTopResultDetail;
import com.polaris.kpi.eval.domain.cycle.type.StatisticRuleItem;
import com.polaris.sdk.type.TenantId;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.ibatis.annotations.JsonAryColumn;
import org.apache.ibatis.annotations.JsonColumn;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 连续周期绩优/绩差员工表(ConsecutiveTopResultEmp)实体类
 *
 * <AUTHOR>
 * @date 2024/9/27 17:16
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class ConsecutiveTopResultEmp extends Domain implements Serializable {

    /**
     * 主键id
     */
    private String id;

    /**
     * 公司id
     */
    @JSONField(serialize = false)
    private TenantId companyId;

    /**
     * 当前周期id
     */
    private String cycleId;

    /**
     * 绩效结果通知配置id
     */
    private String ruleConfigId;

    /**
     * 绩效结果通知规则名称
     */
    private String ruleName;

    /**
     * 规则类型（1持续绩优，2持续绩差）
     */
    private Integer ruleType;

    /**
     * 被考核人id
     */
    private String empId;

    /**
     * 被考核人姓名
     */
    private String empName;

    /**
     * 考核员工头像
     */
    private String avatar;

    /**
     * 被考核人部门id
     */
    private String orgId;

    /**
     * 员工考核部门名
     */
    private String orgName;

    /**
     * 被考核组织id
     */
    private String evalOrgId;

    /**
     * 被考核组织名
     */
    private String evalOrgName;

    /**
     * 考核时员工所在部门codePath 以|连接
     */
    private String atOrgCodePath;

    /**
     * 考核时员工所在部门名字以|连接
     */
    private String atOrgNamePath;

    /**
     * 考核周期数据JSON对象：[{"cycleStart":"周期开始时间",”cycleEnd“:"周期结束时间",“cycleShortName”:"根据周期时间缩写的简称", hitTasks:[{“cycleId”:"周期id","cycleName":"周期名称","taskId":"任务id","taskName":"任务名称","perfScore":"绩效分数","perfLevelName":"绩效等级名称","perfWeight":"绩效系数"}]}]
     */
    @JsonAryColumn(ConsecutiveTopResultDetail.class)
    private List<ConsecutiveTopResultDetail> topResultData = new ArrayList<>();


    /**
     * 规则应用类型：1个人绩效类型 2组织绩效类型
     */
    private Integer ruleApplyType;

    /**
     * 统计时长
     */
    private Integer statisticDuration;

    /**
     * 统计考核周期类型
     */
    private String cycleType;

    /**
     * 统计条件JSON对象[{"statisticType":"等级/总分",“statisticRuleItems”:[{"ruleId":"对象id","ruleName":"对象名称","statisticOperator":">"}]}]
     */
    @JsonAryColumn(StatisticRuleItem.class)
    private List<StatisticRuleItem> statisticRule;

    /**
     * 统计规则配置详情
     */
    @JsonColumn
    private PerfStatisticRule statisticRuleDetail;

    /**
     * 有效状态: 0无效 1有效
     */
    private Integer invalidStatus;

    /**
     * 是否删除
     */
    private String isDeleted;

    public void addTopResultDetail(ConsecutiveTopResultDetail topResultDetail) {
        this.topResultData.add(topResultDetail);
    }
}
