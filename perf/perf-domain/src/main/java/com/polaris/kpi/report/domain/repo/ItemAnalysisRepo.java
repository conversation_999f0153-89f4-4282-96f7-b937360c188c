package com.polaris.kpi.report.domain.repo;

import com.polaris.kpi.report.domain.entity.OrgItemSummary;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/12 16:01
 */

public interface ItemAnalysisRepo {

    void batchSave(List<OrgItemSummary> orgItemSummaries, String empId);

    void delOrgItemSummary(String companyId, String cycleId, String empId, Integer performanceType);

    void countStart(String companyId, String cycleId, String empId, Integer performanceType);

    void countEnd(String companyId, String cycleId, String empId, Integer performanceType);

    boolean existsGoingCount(String companyId, String cycleId, String empId ,Integer performanceType);

}
