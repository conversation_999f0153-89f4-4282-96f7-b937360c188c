package com.polaris.kpi.report.domain.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.common.DelableDomain;
import com.polaris.kpi.eval.domain.task.entity.grade.BaseScoreRange;
import com.polaris.kpi.eval.domain.task.entity.grade.MatchedStep;
import com.polaris.kpi.eval.domain.task.entity.grade.ScoreRule;
import com.polaris.kpi.report.domain.dmsvc.AvgWeigthtComputer;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
public class EmpYearReportItem extends DelableDomain {

    @JSONField(serialize = false)
    private TenantId companyId;//公司id
    private String id;//
    private String reportId;//报告id,emp_year_report.id
    private String empId;//用户id
    private Integer year;//年份


    private EmpYearItemRs weightYearAtMonth;//月度加权计算出的年度结果

    private EmpYearItemRs weightYearAtQuarter = new EmpYearItemRs();//季度加权计算出的年度结果

    private EmpYearItemRs year1;//年度结果

    private EmpYearItemRs halfYear1;//上半年

    private EmpYearItemRs halfYear2;//下半年

    private EmpYearItemRs quarter1;//第一季度

    private EmpYearItemRs quarter2;//第二季度

    private EmpYearItemRs quarter3;//第三季度

    private EmpYearItemRs quarter4;//第四季度

    private EmpYearItemRs month1;//1月

    private EmpYearItemRs month2;//2月

    private EmpYearItemRs month3;//3月

    private EmpYearItemRs month4;//4月

    private EmpYearItemRs month5;//5月

    private EmpYearItemRs month6;//6月

    private EmpYearItemRs month7;//7月

    private EmpYearItemRs month8;//8月

    private EmpYearItemRs month9;//9月

    private EmpYearItemRs month10;//10月

    private EmpYearItemRs month11;//11月

    private EmpYearItemRs month12;//12月

    public List<EmpYearItemRs> allScoreRs() {
        return Arrays.asList(halfYear1, halfYear2, quarter1, quarter2, quarter2, quarter3, quarter4,
                month1, month2, month3, month4, month5, month6, month7, month8, month9, month10, month11, month12);
    }

    //计算加权的年度分
    public void computeWeightScore(ReportWeightConf weightConf) {
        weightYearAtMonth = new EmpYearItemRs();
        weightYearAtQuarter = new EmpYearItemRs();
        List<BigDecimal> scores = Arrays.asList(month1, month2, month3, month4, month5, month6, month7, month8, month9, month10, month11, month12)
                .stream().map(empYearItemRs -> empYearItemRs.getScore()).collect(Collectors.toList());
        AvgWeigthtComputer computer = new AvgWeigthtComputer(scores, weightConf.monthWeightMap());
        computer.compute();
        weightYearAtMonth.setScore(computer.sumScore());
//        finalScore = finalScore.add(computer.sumScore());

        List<BigDecimal> quarters = Arrays.asList(quarter1, quarter2, quarter3, quarter4)
                .stream().map(empYearItemRs -> empYearItemRs.getScore()).collect(Collectors.toList());
        AvgWeigthtComputer quarComputer = new AvgWeigthtComputer(quarters, weightConf.quarterWeightMap());
        quarComputer.compute();
        weightYearAtQuarter.setScore(quarComputer.sumScore());
    }

    public void matchMonth(ScoreRule scoreRule) {
        List<EmpYearItemRs> empYearItemRs = this.allScoreRs();
        for (EmpYearItemRs itemRs : empYearItemRs) {
            if (itemRs == null || itemRs.getScore() == null) {
                continue;
            }
            if (itemRs.onlyOneTask()) {//只有一个任务取原结果等级系数
                continue;
            }
            MatchedStep step = scoreRule.computeLevelStep(itemRs.getScore());
            if (step != null) {
                itemRs.setGrade(step.getStepName(), step.getPerfCoefficient());
            }
        }
    }

    public void matchYear(ScoreRule scoreRule) {
        MatchedStep matchRangAtMonth = scoreRule.computeLevelStep(weightYearAtMonth.getScore());
        if (matchRangAtMonth != null) {
            weightYearAtMonth.setGrade(matchRangAtMonth.getStepName(), matchRangAtMonth.getPerfCoefficient());
        }
        MatchedStep matchedStep = scoreRule.computeLevelStep(weightYearAtQuarter.getScore());
        if (matchedStep != null) {
            weightYearAtQuarter.setGrade(matchedStep.getStepName(), matchedStep.getPerfCoefficient());
        }
    }
}
