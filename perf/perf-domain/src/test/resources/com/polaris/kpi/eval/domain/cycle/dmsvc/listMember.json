[{"finalScore": 0, "refAsk360Flag": false, "originalFinalScore": 0, "version": 0, "orgId": "org1", "hasAskEndScore": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "", "isDeleted": "false", "atOrgPathHight": 1, "scoreEndFlag": false, "isNewEmp": 0, "id": "0", "sendMsg": true, "taskId": "t001", "infos": []}, {"finalScore": 10, "refAsk360Flag": false, "originalFinalScore": 10, "version": 0, "orgId": "org2", "hasAskEndScore": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "1|", "isDeleted": "false", "atOrgPathHight": 2, "scoreEndFlag": false, "isNewEmp": 0, "id": "1", "sendMsg": true, "taskId": "t001", "infos": []}, {"finalScore": 20, "refAsk360Flag": false, "originalFinalScore": 20, "version": 0, "orgId": "org3", "hasAskEndScore": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "2|1|", "isDeleted": "false", "atOrgPathHight": 3, "scoreEndFlag": false, "isNewEmp": 0, "id": "2", "sendMsg": true, "taskId": "t001", "infos": []}, {"finalScore": 30, "refAsk360Flag": false, "originalFinalScore": 30, "version": 0, "orgId": "org1", "hasAskEndScore": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "3|2|1|", "isDeleted": "false", "atOrgPathHight": 4, "scoreEndFlag": false, "isNewEmp": 0, "id": "3", "sendMsg": true, "taskId": "t001", "infos": []}, {"finalScore": 40, "refAsk360Flag": false, "originalFinalScore": 40, "version": 0, "orgId": "org2", "hasAskEndScore": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "4|3|2|1|", "isDeleted": "false", "atOrgPathHight": 5, "scoreEndFlag": false, "isNewEmp": 0, "id": "4", "sendMsg": true, "taskId": "t001", "infos": []}, {"finalScore": 50, "refAsk360Flag": false, "originalFinalScore": 50, "version": 0, "orgId": "org3", "hasAskEndScore": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "5|4|3|2|1|", "isDeleted": "false", "atOrgPathHight": 6, "scoreEndFlag": false, "isNewEmp": 0, "id": "5", "sendMsg": true, "taskId": "t001", "infos": []}, {"finalScore": 60, "refAsk360Flag": false, "originalFinalScore": 60, "version": 0, "orgId": "org1", "hasAskEndScore": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "6|5|4|3|2|1|", "isDeleted": "false", "atOrgPathHight": 7, "scoreEndFlag": false, "isNewEmp": 0, "id": "6", "sendMsg": true, "taskId": "t001", "infos": []}, {"finalScore": 70, "refAsk360Flag": false, "originalFinalScore": 70, "version": 0, "orgId": "org2", "hasAskEndScore": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "7|6|5|4|3|2|1|", "isDeleted": "false", "atOrgPathHight": 8, "scoreEndFlag": false, "isNewEmp": 0, "id": "7", "sendMsg": true, "taskId": "t001", "infos": []}, {"finalScore": 80, "refAsk360Flag": false, "originalFinalScore": 80, "version": 0, "orgId": "org3", "hasAskEndScore": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "8|7|6|5|4|3|2|1|", "isDeleted": "false", "atOrgPathHight": 9, "scoreEndFlag": false, "isNewEmp": 0, "id": "8", "sendMsg": true, "taskId": "t001", "infos": []}, {"finalScore": 0, "refAsk360Flag": false, "originalFinalScore": 0, "version": 0, "orgId": "org1", "hasAskEndScore": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "", "isDeleted": "false", "atOrgPathHight": 1, "scoreEndFlag": false, "isNewEmp": 0, "id": "0", "sendMsg": true, "taskId": "t002", "infos": []}, {"finalScore": 10, "refAsk360Flag": false, "originalFinalScore": 10, "version": 0, "orgId": "org2", "hasAskEndScore": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "1|", "isDeleted": "false", "atOrgPathHight": 2, "scoreEndFlag": false, "isNewEmp": 0, "id": "1", "sendMsg": true, "taskId": "t002", "infos": []}, {"finalScore": 20, "refAsk360Flag": false, "originalFinalScore": 20, "version": 0, "orgId": "org3", "hasAskEndScore": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "2|1|", "isDeleted": "false", "atOrgPathHight": 3, "scoreEndFlag": false, "isNewEmp": 0, "id": "2", "sendMsg": true, "taskId": "t002", "infos": []}, {"finalScore": 30, "refAsk360Flag": false, "originalFinalScore": 30, "version": 0, "orgId": "org1", "hasAskEndScore": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "3|2|1|", "isDeleted": "false", "atOrgPathHight": 4, "scoreEndFlag": false, "isNewEmp": 0, "id": "3", "sendMsg": true, "taskId": "t002", "infos": []}, {"finalScore": 40, "refAsk360Flag": false, "originalFinalScore": 40, "version": 0, "orgId": "org2", "hasAskEndScore": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "4|3|2|1|", "isDeleted": "false", "atOrgPathHight": 5, "scoreEndFlag": false, "isNewEmp": 0, "id": "4", "sendMsg": true, "taskId": "t002", "infos": []}, {"finalScore": 50, "refAsk360Flag": false, "originalFinalScore": 50, "version": 0, "orgId": "org3", "hasAskEndScore": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "5|4|3|2|1|", "isDeleted": "false", "atOrgPathHight": 6, "scoreEndFlag": false, "isNewEmp": 0, "id": "5", "sendMsg": true, "taskId": "t002", "infos": []}, {"finalScore": 60, "refAsk360Flag": false, "originalFinalScore": 60, "version": 0, "orgId": "org1", "hasAskEndScore": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "6|5|4|3|2|1|", "isDeleted": "false", "atOrgPathHight": 7, "scoreEndFlag": false, "isNewEmp": 0, "id": "6", "sendMsg": true, "taskId": "t002", "infos": []}, {"finalScore": 70, "refAsk360Flag": false, "originalFinalScore": 70, "version": 0, "orgId": "org2", "hasAskEndScore": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "7|6|5|4|3|2|1|", "isDeleted": "false", "atOrgPathHight": 8, "scoreEndFlag": false, "isNewEmp": 0, "id": "7", "sendMsg": true, "taskId": "t002", "infos": []}, {"finalScore": 80, "refAsk360Flag": false, "originalFinalScore": 80, "version": 0, "orgId": "org3", "hasAskEndScore": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "8|7|6|5|4|3|2|1|", "isDeleted": "false", "atOrgPathHight": 9, "scoreEndFlag": false, "isNewEmp": 0, "id": "8", "sendMsg": true, "taskId": "t002", "infos": []}]