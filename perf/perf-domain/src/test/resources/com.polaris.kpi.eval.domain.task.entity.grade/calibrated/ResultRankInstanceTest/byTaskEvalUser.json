[{"finalScore": 10, "refAsk360Flag": false, "originalFinalScore": 10, "version": 0, "orgId": "OrgId1", "hasAskEndScore": false, "allAutoCompute": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "OrgId0|", "isDeleted": "false", "atOrgPathHight": 1, "scoreEndFlag": false, "isVacancySkipRater": false, "isNewEmp": 0, "id": "1001", "sendMsg": true, "taskId": "t002", "infos": []}, {"finalScore": 20, "refAsk360Flag": false, "originalFinalScore": 20, "version": 0, "orgId": "OrgId2", "hasAskEndScore": false, "allAutoCompute": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "OrgId0|OrgId1|", "isDeleted": "false", "atOrgPathHight": 2, "scoreEndFlag": false, "isVacancySkipRater": false, "isNewEmp": 0, "id": "1002", "sendMsg": true, "taskId": "t002", "infos": []}, {"finalScore": 30, "refAsk360Flag": false, "originalFinalScore": 30, "version": 0, "orgId": "OrgId3", "hasAskEndScore": false, "allAutoCompute": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "OrgId0|OrgId1|OrgId2|", "isDeleted": "false", "atOrgPathHight": 3, "scoreEndFlag": false, "isVacancySkipRater": false, "isNewEmp": 0, "id": "1003", "sendMsg": true, "taskId": "t002", "infos": []}, {"finalScore": 40, "refAsk360Flag": false, "originalFinalScore": 40, "version": 0, "orgId": "OrgId4", "hasAskEndScore": false, "allAutoCompute": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "OrgId0|OrgId1|OrgId2|OrgId3|", "isDeleted": "false", "atOrgPathHight": 4, "scoreEndFlag": false, "isVacancySkipRater": false, "isNewEmp": 0, "id": "1004", "sendMsg": true, "taskId": "t002", "infos": []}, {"finalScore": 50, "refAsk360Flag": false, "originalFinalScore": 50, "version": 0, "orgId": "OrgId5", "hasAskEndScore": false, "allAutoCompute": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "OrgId0|OrgId1|OrgId2|OrgId3|OrgId4|", "isDeleted": "false", "atOrgPathHight": 5, "scoreEndFlag": false, "isVacancySkipRater": false, "isNewEmp": 0, "id": "1005", "sendMsg": true, "taskId": "t002", "infos": []}, {"finalScore": 60, "refAsk360Flag": false, "originalFinalScore": 60, "version": 0, "orgId": "OrgId6", "hasAskEndScore": false, "allAutoCompute": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "OrgId0|OrgId1|OrgId2|OrgId3|OrgId4|OrgId5|", "isDeleted": "false", "atOrgPathHight": 6, "scoreEndFlag": false, "isVacancySkipRater": false, "isNewEmp": 0, "id": "1006", "sendMsg": true, "taskId": "t002", "infos": []}, {"finalScore": 70, "refAsk360Flag": false, "originalFinalScore": 70, "version": 0, "orgId": "OrgId7", "hasAskEndScore": false, "allAutoCompute": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "OrgId0|OrgId1|OrgId2|OrgId3|OrgId4|OrgId5|OrgId6|", "isDeleted": "false", "atOrgPathHight": 7, "scoreEndFlag": false, "isVacancySkipRater": false, "isNewEmp": 0, "id": "1007", "sendMsg": true, "taskId": "t002", "infos": []}, {"finalScore": 80, "refAsk360Flag": false, "originalFinalScore": 80, "version": 0, "orgId": "OrgId8", "hasAskEndScore": false, "allAutoCompute": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "OrgId0|OrgId1|OrgId2|OrgId3|OrgId4|OrgId5|OrgId6|OrgId7|", "isDeleted": "false", "atOrgPathHight": 8, "scoreEndFlag": false, "isVacancySkipRater": false, "isNewEmp": 0, "id": "1008", "sendMsg": true, "taskId": "t002", "infos": []}, {"finalScore": 90, "refAsk360Flag": false, "originalFinalScore": 90, "version": 0, "orgId": "OrgId9", "hasAskEndScore": false, "allAutoCompute": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "OrgId0|OrgId1|OrgId2|OrgId3|OrgId4|OrgId5|OrgId6|OrgId7|OrgId8|", "isDeleted": "false", "atOrgPathHight": 9, "scoreEndFlag": false, "isVacancySkipRater": false, "isNewEmp": 0, "id": "1009", "sendMsg": true, "taskId": "t001", "infos": []}, {"finalScore": 50, "refAsk360Flag": false, "originalFinalScore": 50, "version": 0, "orgId": "OrgId5", "hasAskEndScore": false, "allAutoCompute": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "OrgId0|OrgId1|OrgId2|OrgId3|OrgId4|", "isDeleted": "false", "atOrgPathHight": 5, "scoreEndFlag": false, "isVacancySkipRater": false, "isNewEmp": 0, "id": "10025", "sendMsg": true, "taskId": "t001", "infos": []}, {"finalScore": 60, "refAsk360Flag": false, "originalFinalScore": 60, "version": 0, "orgId": "OrgId6", "hasAskEndScore": false, "allAutoCompute": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "OrgId0|OrgId1|OrgId2|OrgId3|OrgId4|OrgId5|", "isDeleted": "false", "atOrgPathHight": 6, "scoreEndFlag": false, "isVacancySkipRater": false, "isNewEmp": 0, "id": "10026", "sendMsg": true, "taskId": "t001", "infos": []}, {"finalScore": 70, "refAsk360Flag": false, "originalFinalScore": 70, "version": 0, "orgId": "OrgId7", "hasAskEndScore": false, "allAutoCompute": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "OrgId0|OrgId1|OrgId2|OrgId3|OrgId4|OrgId5|OrgId6|", "isDeleted": "false", "atOrgPathHight": 7, "scoreEndFlag": false, "isVacancySkipRater": false, "isNewEmp": 0, "id": "10027", "sendMsg": true, "taskId": "t001", "infos": []}, {"finalScore": 80, "refAsk360Flag": false, "originalFinalScore": 80, "version": 0, "orgId": "OrgId8", "hasAskEndScore": false, "allAutoCompute": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "OrgId0|OrgId1|OrgId2|OrgId3|OrgId4|OrgId5|OrgId6|OrgId7|", "isDeleted": "false", "atOrgPathHight": 8, "scoreEndFlag": false, "isVacancySkipRater": false, "isNewEmp": 0, "id": "10028", "sendMsg": true, "taskId": "t001", "infos": []}, {"finalScore": 90, "refAsk360Flag": false, "originalFinalScore": 90, "version": 0, "orgId": "OrgId9", "hasAskEndScore": false, "allAutoCompute": false, "resetRaterNameIds": [], "isAllAutoType": true, "atOrgCodePath": "OrgId0|OrgId1|OrgId2|OrgId3|OrgId4|OrgId5|OrgId6|OrgId7|OrgId8|", "isDeleted": "false", "atOrgPathHight": 9, "scoreEndFlag": false, "isVacancySkipRater": false, "isNewEmp": 0, "id": "10029", "sendMsg": true, "taskId": "t001", "infos": []}]