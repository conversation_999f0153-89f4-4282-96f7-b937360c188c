{"data": {"appealConf": {"appealReceiver": null, "canAppealDay": null, "open": 0, "resultAppealNode": null}, "auditResult": {"auditNodes": [], "commentReq": null, "mergeConf": null, "multiType": null, "open": 0, "transferFlag": null, "vacancyApproveInfo": null, "vacancyApproveName": null, "vacancyApproveType": null}, "baseScoreOpen": true, "commentConf": {"commentFlag": "notRequired", "commentRequiredHighValue": null, "commentRequiredValue": null, "plusOrSubComment": 0, "scoreSummarySwitch": -1}, "companyConf": {"canResSubmitInputFinish": 0, "enterOnDingMsg": 1, "enterOnScoring": 0, "evalEmpOnLogAuth": 1, "ignoreVacancyManager": 0, "indInput202308021": 1, "indLevelGroup": 1, "itemAuth": 1, "logAuthSet": null, "resultInputSendMsg": "true", "supIsCanAutoScoreItem": 1}, "confirmResult": {"auto": 0, "autoDay": null, "dimension": null, "open": 0, "sign": 0}, "confirmTask": {"auditNodes": [], "confirmLTDay": null, "modifyAuditFlag": null, "modifyItemDimension": "all", "noChangeSkipFlag": null, "open": 0, "openConfirmLT": 0}, "createTotalLevelType": 1, "customFullScore": null, "deadLineConf": {"finishInputAudit": null, "open": 0, "taskAffirmDeadLine": null, "taskConfirmDeadLine": null, "taskEvalScore": null, "taskResultAudit": null, "taskResultConfirm": null}, "editExeIndi": {"auditNodes": [], "auditOpen": null, "changeUsers": null, "open": 0}, "editStatus": 0, "empEvalId": "1204505", "enterScore": {"autoEnter": true, "enterScoreEmpType": 1, "enterScoreMethod": "auto", "scoreStartRuleDay": 1, "scoreStartRuleType": "before"}, "evaluateType": "simple", "finalScore": null, "finishValueAudit": {"auditNodes": [], "noChangeSkipFlag": null, "open": 0}, "fromOldTask": false, "indicatorCnt": 2, "initiator": "1093014", "kpiTypes": {"datas": [{"alreadyScores": [], "appointRater": null, "des": null, "finishValue": true, "finishValueAudit": null, "importOkrFlag": null, "indLevelGroup": null, "indLevelGroupId": null, "isOkr": "false", "itemLimitCnt": null, "items": [{"actionId": null, "actionName": null, "alreadyNodes": [], "appointRaters": [], "autoScoreExFlag": null, "backup": null, "deptName": null, "empId": "1216003", "endValue": null, "evaluateStartDate": null, "examineOperType": null, "fieldValueList": null, "files": null, "finalSubmitFinishValue": 0, "finishValue": null, "finishValueAudit": null, "finishValueComment": null, "formulaCondition": "[]", "formulaFields": [], "formulaType": 1, "id": "1211121", "indLevel": null, "indLevelGroup": null, "indLevelGroupId": null, "inputChangeRecord": null, "inputEmps": [{"adminType": null, "avatar": "https://static-legacy.dingtalk.com/media/lQLPDhtwSlDwYpfNAmDNAmCwP2waI3J1qnYCiKZNDcDTAA_608_608.png", "empId": "1216003", "empName": "李运营REP", "exUserId": "4932675738786678", "jobnumber": null, "status": "on_the_job"}], "inputFormat": "num", "isNewEmp": 0, "isOkr": "false", "isSubmit": null, "isTypeLocked": null, "itemActualFormula": null, "itemAutoScore": null, "itemFieldJson": "[]", "itemFinishValue": null, "itemFinishValueText": null, "itemFormula": "", "itemFullScoreCfg": "false", "itemLimitCnt": null, "itemPlanFlag": null, "itemRule": "1. 能改进现有业务的流程、方法和制度规定，寻找更合理的解决方案（3-5分）\n2. 能在现有制度，规定下开辟途径，灵活解决问题（2分）\n3. 创新能力差（1分）", "itemScoreRule": {"appointRater": {"anonymous": null, "auditNodes": [], "multiType": "and", "nodeWeight": null, "open": 0, "rateMode": null}, "appointScoreFlag": "false", "appointScoreWeight": null, "id": null, "kpiItemId": "0a79a8aa-2889-421f-81c9-902dc2119507", "kpiTypeId": "1136423", "mutualScoreAnonymous": null, "mutualScoreAttendRule": null, "mutualScoreFlag": "true", "mutualScoreVacancy": null, "mutualScoreViewRule": null, "mutualUserType": null, "mutualUserValue": null, "openRaterRule": true, "peerRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": "user", "modifyFlag": null, "multiType": "and", "node": "peer_score", "nodeWeight": 100, "open": 1, "rateMode": "item", "raters": [{"avatar": "https://static-legacy.dingtalk.com/media/lADPM5HikoKm7UnNAWPNAUQ_324_355.jpg", "dingUserId": null, "empId": "1093014", "empName": "思威", "level": null, "roleId": null, "roleName": null, "type": 0, "weight": null}], "roleAudit": false, "transferFlag": "true", "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "peerScoreFlag": null, "peerScoreWeight": 100, "peerUserName": null, "peerUserType": null, "peerUserValue": null, "selfRater": {"anonymous": null, "nodeWeight": null, "open": 0, "rateMode": null}, "selfScoreFlag": "false", "selfScoreViewRule": null, "selfScoreWeight": null, "subRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "subScoreFlag": null, "subScoreWeight": null, "subUserName": null, "subUserType": null, "subUserValue": null, "superRater": {"anonymous": null, "auditNodes": [{"approvalOrder": 1, "approverInfo": null, "approverName": null, "approverType": "user", "id": null, "modifyFlag": null, "multiType": "or", "node": "superior_score", "raters": [{"avatar": null, "dingUserId": null, "empId": "1267001", "empName": "小杨-02", "level": null, "roleId": null, "roleName": null, "type": 0, "weight": null}], "scoreWeight": 100, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": 100}], "multiType": "or", "nodeWeight": 0, "open": 1, "rateMode": "item", "superiorScoreOrder": "sameTime"}, "superiorScoreFlag": "true", "superiorScoreVacancy": null, "superiorScoreViewRule": null, "superiorScoreWeight": 0, "taskId": null, "taskUserId": "1204505"}, "itemScoreValue": null, "itemTags": null, "itemTargetValue": 0.0, "itemTargetValueText": null, "itemType": "non-measurable", "itemUnit": "%", "itemWeight": 20.0, "kpiItemId": "0a79a8aa-2889-421f-81c9-902dc2119507", "kpiItemName": "创新能力", "kpiTypeClassify": null, "kpiTypeId": "1136423", "kpiTypeName": "默认指标类别", "kpiTypeWeight": 0.0, "managerInput": false, "managerLevel": "", "maxExtraScore": 0.0, "multipleReviewersType": "or", "mustResultInput": 0, "notInput": false, "okrRefFlag": null, "okrScore": null, "okrTaskName": null, "okrType": false, "openOkrScore": 0, "order": 0, "orgId": null, "plusLimit": 0.0, "plusSubInterval": {"max": "", "min": ""}, "pointsNum": null, "progress": null, "progressStatus": null, "reserveOkrWeight": 80.0, "resultInputEmpId": "1216003", "resultInputEmpName": null, "resultInputType": "exam", "reviewer": null, "score": null, "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "scorerType": "exam", "scoringRule": "", "selfScore": null, "showFinishBar": 1, "showTargetValue": null, "startValue": null, "subNodes": [], "subtractLimit": 0.0, "superiorScore": null, "targetId": null, "targetName": null, "taskId": "1581101", "taskUserId": "1204505", "thresholdJson": "[]", "typeOrder": 0, "urgingFlag": null, "vetoFlag": null, "waitScores": [{"active": true, "approvalOrder": 1, "auditStatus": "pass", "calibrationType": null, "empId": "1216003", "empScore": 1.0, "finalPlusScore": null, "finalScore": 0.2, "finalSubtractScore": null, "finalWeightPlusScore": null, "finalWeightScore": 0.2, "finalWeightSubtractScore": null, "id": "1c0f103a-f1d3-458a-8760-325041629dcc", "indexCalibration": null, "isCommited": null, "kpiItemId": "0a79a8aa-2889-421f-81c9-902dc2119507", "kpiTypeId": "1136423", "modifyFlag": null, "noItemScore": 1.0, "notDeleted": true, "operateReason": null, "orgId": null, "perfCoefficient": null, "plusScore": null, "reviewersType": "and", "score": 1.0, "scoreAttUrl": null, "scoreComment": "", "scoreLevel": "", "scoreWeight": 100.0, "scorerId": "1093014", "scorerType": "peer_score", "subtractScore": null, "supper": false, "taskAuditId": null, "taskId": "1581101", "taskUserId": "1204505", "transferId": null, "version": 0, "vetoFlag": null}], "weightValue": null, "workItem": false, "workItemFinishValue": null, "workItemFinished": false}, {"actionId": null, "actionName": null, "alreadyNodes": [], "appointRaters": [], "autoScoreExFlag": null, "backup": null, "deptName": null, "empId": "1216003", "endValue": null, "evaluateStartDate": null, "examineOperType": null, "fieldValueList": null, "files": null, "finalSubmitFinishValue": 0, "finishValue": null, "finishValueAudit": null, "finishValueComment": null, "formulaCondition": "[]", "formulaFields": [], "formulaType": 1, "id": "1211122", "indLevel": null, "indLevelGroup": null, "indLevelGroupId": null, "inputChangeRecord": null, "inputEmps": [{"adminType": null, "avatar": "https://static-legacy.dingtalk.com/media/lQLPDhtwSlDwYpfNAmDNAmCwP2waI3J1qnYCiKZNDcDTAA_608_608.png", "empId": "1216003", "empName": "李运营REP", "exUserId": "4932675738786678", "jobnumber": null, "status": "on_the_job"}], "inputFormat": "num", "isNewEmp": 0, "isOkr": "false", "isSubmit": null, "isTypeLocked": null, "itemActualFormula": null, "itemAutoScore": null, "itemFieldJson": "[]", "itemFinishValue": null, "itemFinishValueText": null, "itemFormula": "", "itemFullScoreCfg": "false", "itemLimitCnt": null, "itemPlanFlag": null, "itemRule": "1. 团结同事与部门\n2. 有良好的团队和集体意识，互帮互助\n3. 自觉维护公司形象名誉，不说、不做有负面影响的事", "itemScoreRule": {"appointRater": {"anonymous": null, "auditNodes": [], "multiType": "and", "nodeWeight": null, "open": 0, "rateMode": null}, "appointScoreFlag": "false", "appointScoreWeight": null, "id": null, "kpiItemId": "33337ae2-4e18-4fbb-b8ee-fccc94634ae2", "kpiTypeId": "1136423", "mutualScoreAnonymous": null, "mutualScoreAttendRule": null, "mutualScoreFlag": "true", "mutualScoreVacancy": null, "mutualScoreViewRule": null, "mutualUserType": null, "mutualUserValue": null, "openRaterRule": true, "peerRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": "user", "modifyFlag": null, "multiType": "and", "node": "peer_score", "nodeWeight": 100, "open": 1, "rateMode": "item", "raters": [{"avatar": "https://static-legacy.dingtalk.com/media/lADPM5HikoKm7UnNAWPNAUQ_324_355.jpg", "dingUserId": null, "empId": "1093014", "empName": "思威", "level": null, "roleId": null, "roleName": null, "type": 0, "weight": null}], "roleAudit": false, "transferFlag": "true", "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "peerScoreFlag": null, "peerScoreWeight": 100, "peerUserName": null, "peerUserType": null, "peerUserValue": null, "selfRater": {"anonymous": null, "nodeWeight": null, "open": 0, "rateMode": null}, "selfScoreFlag": "false", "selfScoreViewRule": null, "selfScoreWeight": null, "subRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "subScoreFlag": null, "subScoreWeight": null, "subUserName": null, "subUserType": null, "subUserValue": null, "superRater": {"anonymous": null, "auditNodes": [{"approvalOrder": 1, "approverInfo": null, "approverName": null, "approverType": "user", "id": null, "modifyFlag": null, "multiType": "or", "node": "superior_score", "raters": [{"avatar": null, "dingUserId": null, "empId": "1267001", "empName": "小杨-02", "level": null, "roleId": null, "roleName": null, "type": 0, "weight": null}], "scoreWeight": 100, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": 100}], "multiType": "or", "nodeWeight": 0, "open": 1, "rateMode": "item", "superiorScoreOrder": "sameTime"}, "superiorScoreFlag": "true", "superiorScoreVacancy": null, "superiorScoreViewRule": null, "superiorScoreWeight": 0, "taskId": null, "taskUserId": "1204505"}, "itemScoreValue": null, "itemTags": null, "itemTargetValue": 0.0, "itemTargetValueText": null, "itemType": "non-measurable", "itemUnit": "%", "itemWeight": 80.0, "kpiItemId": "33337ae2-4e18-4fbb-b8ee-fccc94634ae2", "kpiItemName": "团队协调性", "kpiTypeClassify": null, "kpiTypeId": "1136423", "kpiTypeName": "默认指标类别", "kpiTypeWeight": 0.0, "managerInput": false, "managerLevel": "", "maxExtraScore": 0.0, "multipleReviewersType": "or", "mustResultInput": 0, "notInput": false, "okrRefFlag": null, "okrScore": null, "okrTaskName": null, "okrType": false, "openOkrScore": 0, "order": 1, "orgId": null, "plusLimit": 0.0, "plusSubInterval": {"max": "", "min": ""}, "pointsNum": null, "progress": null, "progressStatus": null, "reserveOkrWeight": 80.0, "resultInputEmpId": "1216003", "resultInputEmpName": null, "resultInputType": "exam", "reviewer": null, "score": null, "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "scorerType": "exam", "scoringRule": "", "selfScore": null, "showFinishBar": 1, "showTargetValue": null, "startValue": null, "subNodes": [], "subtractLimit": 0.0, "superiorScore": null, "targetId": null, "targetName": null, "taskId": "1581101", "taskUserId": "1204505", "thresholdJson": "[]", "typeOrder": 0, "urgingFlag": null, "vetoFlag": null, "waitScores": [{"active": true, "approvalOrder": 1, "auditStatus": "pass", "calibrationType": null, "empId": "1216003", "empScore": 2.0, "finalPlusScore": null, "finalScore": 1.6, "finalSubtractScore": null, "finalWeightPlusScore": null, "finalWeightScore": 1.6, "finalWeightSubtractScore": null, "id": "533a36da-06c9-4129-8c47-f388a84663f0", "indexCalibration": null, "isCommited": null, "kpiItemId": "33337ae2-4e18-4fbb-b8ee-fccc94634ae2", "kpiTypeId": "1136423", "modifyFlag": null, "noItemScore": 2.0, "notDeleted": true, "operateReason": null, "orgId": null, "perfCoefficient": null, "plusScore": null, "reviewersType": "and", "score": 2.0, "scoreAttUrl": null, "scoreComment": "", "scoreLevel": "", "scoreWeight": 100.0, "scorerId": "1093014", "scorerType": "peer_score", "subtractScore": null, "supper": false, "taskAuditId": null, "taskId": "1581101", "taskUserId": "1204505", "transferId": null, "version": 0, "vetoFlag": null}], "weightValue": null, "workItem": false, "workItemFinishValue": null, "workItemFinished": false}], "kpiTypeClassify": null, "kpiTypeId": "1136423", "kpiTypeName": "默认指标类别", "kpiTypeUsedFields": [], "kpiTypeWeight": 0.0, "lockedItems": [], "maxExtraScore": 0.0, "openOkrScore": 0, "openRaterRule": false, "peerRater": null, "plusSubInterval": {"max": "", "min": ""}, "raterBack": false, "reserveOkrWeight": 80.0, "scoreOptType": 2, "selfRater": null, "subRater": null, "superRater": null, "taskUserId": "1204505", "typeLevel": null, "typeOrder": 0, "typeRule": null, "waitScores": []}], "empty": false, "groups": null, "mergedAudits": [], "oldTypeAudits": null}, "levelGroupId": "", "manualPublicOpen": false, "notAllowExceed": false, "publishResult": {"dimension": 3, "notPublic": false, "opEmps": [], "open": 1, "toEmps": [{"dept": false, "objItems": null, "objType": "emp"}, {"dept": false, "objItems": null, "objType": "scoreEmp"}], "type": "afterFinished"}, "publishResultOpen": true, "resultAuditingOpen": false, "ruleName": "简易表", "s3AppointRater": {"anonymous": null, "auditNodes": [], "multiType": "and", "nodeWeight": null, "open": 0, "rateMode": null}, "s3PeerRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": "user", "modifyFlag": null, "multiType": "and", "node": "peer_score", "nodeWeight": 100, "open": 1, "rateMode": "item", "raters": [{"avatar": "https://static-legacy.dingtalk.com/media/lADPM5HikoKm7UnNAWPNAUQ_324_355.jpg", "dingUserId": null, "empId": "1093014", "empName": "思威", "level": null, "roleId": null, "roleName": null, "type": 0, "weight": null}], "roleAudit": false, "transferFlag": "true", "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "s3SelfRater": {"anonymous": null, "nodeWeight": null, "open": 0, "rateMode": null}, "s3SubRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "s3SuperRater": {"anonymous": null, "auditNodes": [{"approvalOrder": 1, "approverInfo": null, "approverName": null, "approverType": "user", "id": null, "modifyFlag": null, "multiType": "or", "node": "superior_score", "raters": [{"avatar": null, "dingUserId": null, "empId": "1267001", "empName": "小杨-02", "level": null, "roleId": null, "roleName": null, "type": 0, "weight": null}], "scoreWeight": 100, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": 100}], "multiType": "or", "nodeWeight": 0, "open": 1, "rateMode": "item", "superiorScoreOrder": "sameTime"}, "sameTime": false, "scoreChain": {"chain": [{"appointNode": false, "next": {"appointNode": false, "next": {"appointNode": false, "next": null, "scoreNodes": [{"appointNode": false, "itemNode": false, "nextSuper": null, "node": "SUPERIOR_SCORE", "order": 1}], "totalLevel": false, "type": 0}, "scoreNodes": [{"appointNode": false, "itemNode": false, "nextSuper": null, "node": "PEER_SCORE", "order": 1}, {"appointNode": false, "itemNode": true, "nextSuper": null, "node": "ITEM_SCORE", "order": 1}], "totalLevel": false, "type": 0}, "scoreNodes": [{"appointNode": false, "itemNode": false, "nextSuper": null, "node": "AUTO", "order": 1}], "totalLevel": false, "type": 0}, {"appointNode": false, "next": {"appointNode": false, "next": null, "scoreNodes": [{"appointNode": false, "itemNode": false, "nextSuper": null, "node": "SUPERIOR_SCORE", "order": 1}], "totalLevel": false, "type": 0}, "scoreNodes": [{"appointNode": false, "itemNode": false, "nextSuper": null, "node": "PEER_SCORE", "order": 1}, {"appointNode": false, "itemNode": true, "nextSuper": null, "node": "ITEM_SCORE", "order": 1}], "totalLevel": false, "type": 0}, {"appointNode": false, "next": null, "scoreNodes": [{"appointNode": false, "itemNode": false, "nextSuper": null, "node": "SUPERIOR_SCORE", "order": 1}], "totalLevel": false, "type": 0}]}, "scoreConf": {"multiType": "or", "transferFlag": "true", "vacancyApproveInfo": null, "vacancyApproveName": null, "vacancyApproveType": null}, "scoreSortConf": {"exeType": 0, "oneByOneChain": null, "sameTime": false, "sortItems": [{"name": "自评", "sort": 1, "type": 10}, {"name": "同级互评", "sort": 2, "type": 20}, {"name": "下级互评", "sort": 3, "type": 30}, {"name": "上级评分", "sort": 4, "type": 40}, {"name": "指定评", "sort": 5, "type": 50}]}, "scoreValueConf": {"baseScore": 0, "customFullScore": 100, "exceedFullScore": false, "fullScoreRange": true, "scoreRangeType": "fullScore"}, "scoreView": {"appointScoreAnonymous": "true", "appointScoreViewRule": {"anonymous": null, "appoint": "", "examinee": "", "mutual": "", "superior": ""}, "mutualScoreAnonymous": "true", "mutualScoreViewRule": {"anonymous": null, "appoint": "", "examinee": "", "mutual": "", "superior": ""}, "selfScoreViewRule": {"anonymous": null, "appoint": "", "examinee": "score,attach", "mutual": "", "superior": ""}, "superiorScoreAnonymous": "true", "superiorScoreViewRule": {"anonymous": null, "appoint": "score,attach", "examinee": "score,attach", "mutual": "score,attach", "superior": "score,attach"}}, "showResultType": 6, "stageChain": null, "submitWithWeight": false, "systemFullScore": null, "taskId": "1581101", "taskName": "1109-重置评分到个人", "totalLevelRaters": [], "totalLevelResults": [], "totalScoreRs": null, "typeWeightConf": {"checkItemWeight": 1, "itemWeightLimit": 100, "limit100Weight": 0, "open": 0}, "typeWeightOpen": false}, "errCode": null, "errMessage": null, "success": true}