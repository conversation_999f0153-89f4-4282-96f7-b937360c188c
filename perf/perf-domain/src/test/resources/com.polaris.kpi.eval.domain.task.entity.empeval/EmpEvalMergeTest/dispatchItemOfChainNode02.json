{"appealConf": {"appealReceiver": null, "canAppealDay": null, "open": 0, "resultAppealNode": null}, "auditResult": {"auditNodes": [{"approvalOrder": 1, "approverInfo": "1", "approverName": "直属主管", "approverType": "manager", "id": null, "modifyFlag": null, "multiType": "or", "node": "final_result_audit", "raters": [{"avatar": "", "dingUserId": "443739074137721579", "empId": "b458eec6-d7b9-4708-8605-7574c150131d", "empName": "闫晶晶", "level": 1, "roleId": null, "roleName": null, "type": 1, "weight": null}], "scoreWeight": null, "status": null, "transferFlag": null, "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": null}], "commentReq": 0, "mergeConf": null, "multiType": "or", "open": 1, "transferFlag": "true", "vacancyApproveInfo": "", "vacancyApproveName": "", "vacancyApproveType": "superior"}, "baseScoreOpen": true, "commentConf": {"commentFlag": "notRequired", "commentRequiredHighValue": null, "commentRequiredValue": null, "plusOrSubComment": 0, "scoreSummarySwitch": -1}, "companyConf": {"canResSubmitInputFinish": 0, "enterOnDingMsg": 1, "enterOnScoring": 0, "evalEmpOnLogAuth": 1, "ignoreVacancyManager": 0, "indInput202308021": 0, "indLevelGroup": 0, "itemAuth": 0, "logAuthSet": null, "resultInputSendMsg": "true", "resultRankOpen20231213": 0, "supIsCanAutoScoreItem": 0, "tempAuth": 0}, "confirmResult": {"auto": 0, "autoDay": null, "dimension": 11, "open": 0, "sign": 0}, "confirmTask": {"auditNodes": [{"approvalOrder": 1, "approverInfo": "1", "approverName": "直属主管", "approverType": "manager", "id": null, "modifyFlag": "true", "multiType": "or", "node": "modify_item_audit", "raters": [{"avatar": "", "dingUserId": "443739074137721579", "empId": "b458eec6-d7b9-4708-8605-7574c150131d", "empName": "闫晶晶", "level": 1, "roleId": null, "roleName": null, "type": 1, "weight": null}], "scoreWeight": null, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": null}, {"approvalOrder": 2, "approverInfo": "", "approverName": "被考核人", "approverType": "taskEmp", "id": null, "modifyFlag": "true", "multiType": "or", "node": "modify_item_audit", "raters": [{"avatar": null, "dingUserId": null, "empId": "96633281", "empName": "周星剑", "level": null, "roleId": null, "roleName": null, "type": 0, "weight": null}], "scoreWeight": null, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": null}], "confirmLTDay": 1, "modifyAuditFlag": "true", "modifyItemDimension": "all", "noChangeSkipFlag": "false", "open": 1, "openConfirmLT": 0}, "createTotalLevelType": 1, "customFullScore": null, "deadLineConf": {"finishInputAudit": null, "open": 0, "taskAffirmDeadLine": null, "taskConfirmDeadLine": null, "taskEvalScore": null, "taskResultAudit": null, "taskResultConfirm": null}, "editExeIndi": {"auditNodes": [{"approvalOrder": 1, "approverInfo": "eafd93f2-6e39-4cf8-9a4a-565ddbbec905", "approverName": "戴亚茹", "approverType": "user", "id": null, "modifyFlag": null, "multiType": "or", "node": "change_item_audit", "raters": [{"avatar": null, "dingUserId": null, "empId": "eafd93f2-6e39-4cf8-9a4a-565ddbbec905", "empName": "戴亚茹", "level": null, "roleId": null, "roleName": null, "type": 0, "weight": null}], "scoreWeight": null, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": null}], "auditOpen": 1, "changeUsers": ["admin"], "open": 1}, "editStatus": 0, "empEvalId": "2433247", "enterScore": {"autoEnter": false, "enterScoreEmpType": 1, "enterScoreMethod": "manual", "scoreStartRuleDay": 1, "scoreStartRuleType": "before"}, "evaluateType": "simple", "finalScore": null, "finishValueAudit": {"auditNodes": [], "noChangeSkipFlag": null, "open": 0}, "fromOldTask": false, "indicatorCnt": 10, "initiator": "f934918c-9ca4-450a-8791-fef6913ddce7", "inputNotify": null, "kpiTypes": {"datas": [{"alreadyScores": [], "appointRater": {"anonymous": null, "auditNodes": [], "multiType": "and", "nodeWeight": null, "open": 0, "rateMode": null}, "des": null, "finishValue": true, "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "importOkrFlag": "false", "indLevelGroup": null, "indLevelGroupId": null, "isOkr": "false", "itemLimitCnt": null, "items": [{"actionId": null, "actionName": null, "alreadyNodes": [], "appointRaters": [], "autoScoreExFlag": null, "backup": null, "deptName": null, "empId": "96633281", "endValue": null, "evaluateStartDate": null, "examineOperType": null, "fieldValueList": null, "files": null, "finalSubmit": false, "finalSubmitFinishValue": 0, "finishValue": null, "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueComment": null, "formulaCondition": "[]", "formulaFields": [], "formulaType": 1, "id": "6979311", "indLevel": null, "indLevelGroup": null, "indLevelGroupId": null, "inputChangeRecord": null, "inputEmps": [{"adminType": null, "avatar": "", "empId": "665f9d49-1373-494d-90dc-7b1f9082afc9", "empName": "吴梅丽", "exUserId": "515915286321564812", "jobnumber": "8020", "status": "on_the_job"}], "inputFormat": "num", "inputRole": null, "isNewEmp": 0, "isOkr": "false", "isSubmit": null, "isTypeLocked": "true", "itemActualFormula": null, "itemAutoScore": null, "itemFieldJson": "[]", "itemFinishValue": 0.0, "itemFinishValueText": null, "itemFormula": "", "itemFullScoreCfg": "false", "itemLimitCnt": null, "itemPlanFlag": null, "itemRule": "1、发生本中心或者中心员工承担主要责任的生产安全及火灾事故的重特大伤亡事故、重大火灾事故、重大环境污染事故、重大安全责任事故的；\n2、发生本中心或者中心员工承担主要责任的工安事故损失≥300元；\n3、发生本中心或者中心员工承担主要责任的工安事故损失＜300元；\n4、出现其它非主要责任的生产安全及火灾事故，或者其它重大事故以下，或者其它影响企业的负面事件，或者给企业造成经济损失的；\n5、出现外部质量监督主管部门抽检到本公司的产品不合格", "itemScoreRule": null, "itemScoreValue": "{\"type\":\"autoDefine\",\"value\":{\"from\":0,\"to\":\"\",\"step\":0.5}}", "itemTags": null, "itemTargetValue": 0.0, "itemTargetValueText": "", "itemType": "non-measurable", "itemUnit": "%", "itemWeight": 0.0, "kpiItemId": "ee2ab5fb-af80-4e40-a872-a81ddee5e63f", "kpiItemName": "生产安全和火灾事故", "kpiTypeClassify": "subtract", "kpiTypeId": "6f3704ca-97f5-4f44-84ad-c7be2feddd91", "kpiTypeName": "扣分项", "kpiTypeWeight": 0.0, "kr": false, "managerInput": false, "managerLevel": "", "maxExtraScore": 100.0, "multipleReviewersType": "or", "mustResultInput": 0, "notInput": false, "notInputVal": false, "okrRefFlag": null, "okrScore": null, "okrTaskName": null, "okrType": false, "openOkrScore": 0, "order": 0, "orgId": "38449673", "plusLimit": 0.0, "plusSubInterval": {"max": "", "min": ""}, "pointsNum": null, "progress": null, "progressStatus": null, "reserveOkrWeight": 0.0, "resultInputEmpId": "665f9d49-1373-494d-90dc-7b1f9082afc9", "resultInputEmpName": null, "resultInputType": "user", "reviewer": null, "score": null, "scorerObjId": [{"objItems": [{"managerLevel": null, "managerName": null, "objId": "f934918c-9ca4-450a-8791-fef6913ddce7", "objName": "林燕梅", "roleId": "9d6a8296-c607-4f64-8650-12554c2ed321", "roleName": "人事专员"}, {"managerLevel": null, "managerName": null, "objId": "87855356", "objName": "曾冬梅", "roleId": "9d6a8296-c607-4f64-8650-12554c2ed321", "roleName": "人事专员"}, {"managerLevel": null, "managerName": null, "objId": "fc2665c6-5b1c-4b85-9fce-413f21ef79ec", "objName": "苏过灵", "roleId": "9d6a8296-c607-4f64-8650-12554c2ed321", "roleName": "人事专员"}], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "scorerType": "user", "scoringRule": "1、直接责任人岗位绩效得分为0；相关责任人扣30分/次；部门/科室主管扣30分/次；中心主管扣30分/次；\n2、直接责任人扣30分/次；相关责任人扣25分/次；部门/科室主管扣20分/次；中心主管扣20分/次；\n3、直接责任人扣20分/次；相关责任人扣15分/次；部门/科室主管扣10分/次；中心主管扣10分/次；\n4、由绩效管理委员会确定扣分标准；\n5、直接责任人扣15分/次；相关责任人扣15分/次；部门/科室主管扣10分/次；中心主管扣10分/次。", "selfScore": null, "showFinishBar": 1, "showTargetValue": "", "startValue": null, "subNodes": [], "subtractLimit": 100.0, "superiorScore": null, "targetId": null, "targetName": null, "taskId": "1984054", "taskUserId": "2433247", "thresholdJson": "[]", "typeOrder": 3, "urgingFlag": null, "vetoFlag": null, "waitScores": [], "weightValue": null, "workItem": false, "workItemFinishValue": null, "workItemFinished": false}, {"actionId": null, "actionName": null, "alreadyNodes": [], "appointRaters": [], "autoScoreExFlag": null, "backup": null, "deptName": null, "empId": "96633281", "endValue": null, "evaluateStartDate": null, "examineOperType": null, "fieldValueList": null, "files": null, "finalSubmit": false, "finalSubmitFinishValue": 0, "finishValue": null, "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueComment": null, "formulaCondition": "[]", "formulaFields": [], "formulaType": 1, "id": "6979312", "indLevel": null, "indLevelGroup": null, "indLevelGroupId": null, "inputChangeRecord": null, "inputEmps": [{"adminType": null, "avatar": "", "empId": "fc2665c6-5b1c-4b85-9fce-413f21ef79ec", "empName": "苏过灵", "exUserId": "080648031533350813", "jobnumber": "506.0", "status": "on_the_job"}], "inputFormat": "num", "inputRole": null, "isNewEmp": 0, "isOkr": "false", "isSubmit": null, "isTypeLocked": "true", "itemActualFormula": null, "itemAutoScore": null, "itemFieldJson": "[]", "itemFinishValue": 0.0, "itemFinishValueText": null, "itemFormula": "", "itemFullScoreCfg": "false", "itemLimitCnt": null, "itemPlanFlag": null, "itemRule": "1、发生重大交通主责事故（公司车辆）、重大食品中毒事故、失窃事故；\n2、利用信息设备泄密及恶性操作等信息安全事件、经营信息泄露等。", "itemScoreRule": null, "itemScoreValue": "{\"type\":\"autoDefine\",\"value\":{\"from\":0,\"to\":\"\",\"step\":0.5}}", "itemTags": null, "itemTargetValue": 0.0, "itemTargetValueText": "", "itemType": "non-measurable", "itemUnit": "%", "itemWeight": 100.0, "kpiItemId": "6f0d4526-b500-4b9e-aab6-226b397f40d6", "kpiItemName": "其他事故", "kpiTypeClassify": "subtract", "kpiTypeId": "6f3704ca-97f5-4f44-84ad-c7be2feddd91", "kpiTypeName": "扣分项", "kpiTypeWeight": 0.0, "kr": false, "managerInput": false, "managerLevel": "", "maxExtraScore": 100.0, "multipleReviewersType": "or", "mustResultInput": 0, "notInput": false, "notInputVal": false, "okrRefFlag": null, "okrScore": null, "okrTaskName": null, "okrType": false, "openOkrScore": 0, "order": 1, "orgId": "38449673", "plusLimit": 0.0, "plusSubInterval": {"max": "", "min": ""}, "pointsNum": null, "progress": null, "progressStatus": null, "reserveOkrWeight": 0.0, "resultInputEmpId": "fc2665c6-5b1c-4b85-9fce-413f21ef79ec", "resultInputEmpName": null, "resultInputType": "user", "reviewer": null, "score": null, "scorerObjId": [{"objItems": [{"managerLevel": null, "managerName": null, "objId": "f934918c-9ca4-450a-8791-fef6913ddce7", "objName": "林燕梅", "roleId": "9d6a8296-c607-4f64-8650-12554c2ed321", "roleName": "人事专员"}, {"managerLevel": null, "managerName": null, "objId": "87855356", "objName": "曾冬梅", "roleId": "9d6a8296-c607-4f64-8650-12554c2ed321", "roleName": "人事专员"}, {"managerLevel": null, "managerName": null, "objId": "fc2665c6-5b1c-4b85-9fce-413f21ef79ec", "objName": "苏过灵", "roleId": "9d6a8296-c607-4f64-8650-12554c2ed321", "roleName": "人事专员"}], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "scorerType": "user", "scoringRule": "1、直接责任人岗位绩效得分为0；相关责任人扣30分/次；部门/科室主管扣30分/次；中心主管扣30分/次；\n2、直接责任人岗位绩效得分为0；相关责任人扣30分/次；部门/科室主管扣30分/次；中心主管扣30分/次。", "selfScore": null, "showFinishBar": 1, "showTargetValue": "", "startValue": null, "subNodes": [], "subtractLimit": 100.0, "superiorScore": null, "targetId": null, "targetName": null, "taskId": "1984054", "taskUserId": "2433247", "thresholdJson": "[]", "typeOrder": 3, "urgingFlag": null, "vetoFlag": null, "waitScores": [], "weightValue": null, "workItem": false, "workItemFinishValue": null, "workItemFinished": false}], "kpiTypeClassify": "subtract", "kpiTypeId": "6f3704ca-97f5-4f44-84ad-c7be2feddd91", "kpiTypeName": "扣分项", "kpiTypeUsedFields": [], "kpiTypeWeight": 0.0, "lockedItems": ["typeWeight", "addIndex", "modifyIndex", "deleteIndex"], "maxExtraScore": 100.0, "notExtType": false, "openOkrScore": 0, "openRaterRule": true, "peerRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "plusSubInterval": {"max": "", "min": ""}, "raterBack": false, "reserveOkrWeight": 0.0, "scoreOptType": 2, "selfRater": {"anonymous": null, "nodeWeight": null, "open": 0, "rateMode": null}, "subRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "superRater": {"anonymous": null, "auditNodes": [{"approvalOrder": 1, "approverInfo": "1", "approverName": null, "approverType": "manager", "id": null, "modifyFlag": null, "multiType": "or", "node": "superior_score", "raters": [{"avatar": null, "dingUserId": null, "empId": "b458eec6-d7b9-4708-8605-7574c150131d", "empName": "闫晶晶", "level": 1, "roleId": null, "roleName": null, "type": 1, "weight": null}], "scoreWeight": 100.0, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": 100.0}], "multiType": "or", "nodeWeight": 100.0, "open": 1, "rateMode": "item", "superiorScoreOrder": "sameTime"}, "taskUserId": "2433247", "typeLevel": null, "typeOrder": 3, "typeRule": null, "waitScores": []}, {"alreadyScores": [], "appointRater": {"anonymous": null, "auditNodes": [], "multiType": "and", "nodeWeight": null, "open": 0, "rateMode": null}, "des": null, "finishValue": true, "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "importOkrFlag": "false", "indLevelGroup": null, "indLevelGroupId": null, "isOkr": "false", "itemLimitCnt": null, "items": [{"actionId": null, "actionName": null, "alreadyNodes": [], "appointRaters": [], "autoScoreExFlag": null, "backup": null, "deptName": null, "empId": "96633281", "endValue": null, "evaluateStartDate": null, "examineOperType": null, "fieldValueList": null, "files": null, "finalSubmit": false, "finalSubmitFinishValue": 0, "finishValue": null, "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueComment": null, "formulaCondition": "[{\"companyId\":\"8439320d-157f-49ae-bdc6-d4d7107b492a\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"704ac637-26c8-4613-a43c-f7ecf7beca97\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true,\"kpiItemId\":\"883ca9f9-09ce-4e46-8ae9-00b612aeea0a\"},{\"companyId\":\"8439320d-157f-49ae-bdc6-d4d7107b492a\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"db6931f3-57ca-44be-9cc0-89dfe8de0d8f\",\"isSystemField\":\"false\",\"label\":\"目标值\",\"value\":0,\"yVal\":true,\"kpiItemId\":\"883ca9f9-09ce-4e46-8ae9-00b612aeea0a\"}]", "formulaFields": [], "formulaType": 1, "id": "6979313", "indLevel": null, "indLevelGroup": null, "indLevelGroupId": null, "inputChangeRecord": null, "inputEmps": null, "inputFormat": "num", "inputRole": null, "isNewEmp": 0, "isOkr": "false", "isSubmit": null, "isTypeLocked": "true", "itemActualFormula": null, "itemAutoScore": null, "itemFieldJson": "[{\"companyId\":\"8439320d-157f-49ae-bdc6-d4d7107b492a\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"704ac637-26c8-4613-a43c-f7ecf7beca97\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"companyId\":\"8439320d-157f-49ae-bdc6-d4d7107b492a\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"db6931f3-57ca-44be-9cc0-89dfe8de0d8f\",\"isSystemField\":\"false\",\"label\":\"目标值\",\"value\":0,\"yVal\":true}]", "itemFinishValue": null, "itemFinishValueText": null, "itemFormula": "", "itemFullScoreCfg": "false", "itemLimitCnt": null, "itemPlanFlag": null, "itemRule": "1、管理/技术创新获地级市以上奖励；\n2、给公司带来经济效益，≥50万；\n3、给企业带来重大经济效益的（≥100万）；\n4、其余重点突出贡献，根据实际情况由各中心提报。", "itemScoreRule": null, "itemScoreValue": "{\"type\":\"autoDefine\",\"value\":{\"from\":0,\"to\":\"\",\"step\":0.5}}", "itemTags": null, "itemTargetValue": 0.0, "itemTargetValueText": null, "itemType": "non-measurable", "itemUnit": "%", "itemWeight": 100.0, "kpiItemId": "883ca9f9-09ce-4e46-8ae9-00b612aeea0a", "kpiItemName": "重点突出贡献", "kpiTypeClassify": "plus", "kpiTypeId": "7b3d19e9-3f86-469a-9ac2-411b3a0d053e", "kpiTypeName": "加分项", "kpiTypeWeight": 0.0, "kr": false, "managerInput": false, "managerLevel": "", "maxExtraScore": 30.0, "multipleReviewersType": "or", "mustResultInput": 0, "notInput": false, "notInputVal": false, "okrRefFlag": null, "okrScore": null, "okrTaskName": null, "okrType": false, "openOkrScore": 0, "order": 0, "orgId": "38449673", "plusLimit": 30.0, "plusSubInterval": {"max": "", "min": ""}, "pointsNum": null, "progress": null, "progressStatus": null, "reserveOkrWeight": 0.0, "resultInputEmpId": null, "resultInputEmpName": null, "resultInputType": "no", "reviewer": null, "score": null, "scorerObjId": [{"objItems": [{"managerLevel": null, "managerName": null, "objId": "f934918c-9ca4-450a-8791-fef6913ddce7", "objName": "林燕梅", "roleId": "9d6a8296-c607-4f64-8650-12554c2ed321", "roleName": "人事专员"}, {"managerLevel": null, "managerName": null, "objId": "87855356", "objName": "曾冬梅", "roleId": "9d6a8296-c607-4f64-8650-12554c2ed321", "roleName": "人事专员"}, {"managerLevel": null, "managerName": null, "objId": "fc2665c6-5b1c-4b85-9fce-413f21ef79ec", "objName": "苏过灵", "roleId": "9d6a8296-c607-4f64-8650-12554c2ed321", "roleName": "人事专员"}], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "scorerType": "user", "scoringRule": "1、员工加10分，部门、中心各加5分；\n2、员工、部门、中心各加10分；\n3、员工、部门、中心各加15分；\n4、标准参照管理/技术加分标准。", "selfScore": null, "showFinishBar": 1, "showTargetValue": "", "startValue": null, "subNodes": [], "subtractLimit": 0.0, "superiorScore": null, "targetId": null, "targetName": null, "taskId": "1984054", "taskUserId": "2433247", "thresholdJson": "[]", "typeOrder": 2, "urgingFlag": null, "vetoFlag": null, "waitScores": [], "weightValue": null, "workItem": false, "workItemFinishValue": null, "workItemFinished": false}], "kpiTypeClassify": "plus", "kpiTypeId": "7b3d19e9-3f86-469a-9ac2-411b3a0d053e", "kpiTypeName": "加分项", "kpiTypeUsedFields": [], "kpiTypeWeight": 0.0, "lockedItems": ["typeWeight", "addIndex", "modifyIndex", "deleteIndex"], "maxExtraScore": 30.0, "notExtType": false, "openOkrScore": 0, "openRaterRule": true, "peerRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "plusSubInterval": {"max": "", "min": ""}, "raterBack": false, "reserveOkrWeight": 0.0, "scoreOptType": 2, "selfRater": {"anonymous": null, "nodeWeight": null, "open": 0, "rateMode": null}, "subRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "superRater": {"anonymous": null, "auditNodes": [{"approvalOrder": 1, "approverInfo": "1", "approverName": null, "approverType": "manager", "id": null, "modifyFlag": null, "multiType": "or", "node": "superior_score", "raters": [{"avatar": null, "dingUserId": null, "empId": "b458eec6-d7b9-4708-8605-7574c150131d", "empName": "闫晶晶", "level": 1, "roleId": null, "roleName": null, "type": 1, "weight": null}], "scoreWeight": 100.0, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": 100.0}], "multiType": "or", "nodeWeight": 100.0, "open": 1, "rateMode": "item", "superiorScoreOrder": "sameTime"}, "taskUserId": "2433247", "typeLevel": null, "typeOrder": 2, "typeRule": null, "waitScores": []}, {"alreadyScores": [], "appointRater": {"anonymous": null, "auditNodes": [], "multiType": "and", "nodeWeight": null, "open": 0, "rateMode": null}, "des": null, "finishValue": true, "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "importOkrFlag": "false", "indLevelGroup": null, "indLevelGroupId": null, "isOkr": "false", "itemLimitCnt": null, "items": [{"actionId": null, "actionName": null, "alreadyNodes": [], "appointRaters": [], "autoScoreExFlag": null, "backup": null, "deptName": null, "empId": "96633281", "endValue": null, "evaluateStartDate": null, "examineOperType": null, "fieldValueList": null, "files": null, "finalSubmit": true, "finalSubmitFinishValue": 1, "finishValue": null, "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueComment": null, "formulaCondition": "[]", "formulaFields": [], "formulaType": 1, "id": "6979314", "indLevel": null, "indLevelGroup": null, "indLevelGroupId": null, "inputChangeRecord": null, "inputEmps": [{"adminType": null, "avatar": "", "empId": "b458eec6-d7b9-4708-8605-7574c150131d", "empName": "闫晶晶", "exUserId": "443739074137721579", "jobnumber": "448", "status": "on_the_job"}], "inputFormat": "num", "inputRole": null, "isNewEmp": 0, "isOkr": "false", "isSubmit": null, "isTypeLocked": "false", "itemActualFormula": null, "itemAutoScore": null, "itemFieldJson": "[]", "itemFinishValue": 80.0, "itemFinishValueText": null, "itemFormula": "", "itemFullScoreCfg": "false", "itemLimitCnt": null, "itemPlanFlag": null, "itemRule": "及时、可行", "itemScoreRule": {"appointRater": {"anonymous": null, "auditNodes": [], "multiType": "and", "nodeWeight": null, "open": 0, "rateMode": null}, "appointScoreFlag": "false", "appointScoreWeight": null, "id": null, "kpiItemId": "9866e424-0250-4ab4-945c-5d20a03a4037", "kpiTypeId": "aae9a829-cc4a-4afe-b1ff-f86c930df705", "mutualScoreAnonymous": null, "mutualScoreAttendRule": null, "mutualScoreFlag": null, "mutualScoreVacancy": null, "mutualScoreViewRule": null, "mutualUserType": null, "mutualUserValue": null, "openRaterRule": true, "peerRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "peerScoreFlag": null, "peerScoreWeight": null, "peerUserName": null, "peerUserType": null, "peerUserValue": null, "selfRater": {"anonymous": null, "nodeWeight": null, "open": 0, "rateMode": null}, "selfScoreFlag": "false", "selfScoreViewRule": null, "selfScoreWeight": null, "subRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "subScoreFlag": null, "subScoreWeight": null, "subUserName": null, "subUserType": null, "subUserValue": null, "superRater": {"anonymous": null, "auditNodes": [{"approvalOrder": 1, "approverInfo": "1", "approverName": null, "approverType": "manager", "id": null, "modifyFlag": null, "multiType": "or", "node": "superior_score", "raters": [{"avatar": null, "dingUserId": null, "empId": "b458eec6-d7b9-4708-8605-7574c150131d", "empName": "闫晶晶", "level": 1, "roleId": null, "roleName": null, "type": 1, "weight": null}], "scoreWeight": 100.0, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": 100.0}], "multiType": "or", "nodeWeight": 100.0, "open": 1, "rateMode": "item", "superiorScoreOrder": "sameTime"}, "superiorScoreFlag": "true", "superiorScoreVacancy": null, "superiorScoreViewRule": null, "superiorScoreWeight": 100.0, "taskId": null, "taskUserId": "2433247"}, "itemScoreValue": "{\"type\":\"toMainScore\"}", "itemTags": null, "itemTargetValue": 0.0, "itemTargetValueText": "", "itemType": "non-measurable", "itemUnit": "%", "itemWeight": 20.0, "kpiItemId": "9866e424-0250-4ab4-945c-5d20a03a4037", "kpiItemName": "市场调研报告提交及时可行性", "kpiTypeClassify": "custom", "kpiTypeId": "aae9a829-cc4a-4afe-b1ff-f86c930df705", "kpiTypeName": "岗位绩效", "kpiTypeWeight": 80.0, "kr": false, "managerInput": false, "managerLevel": "", "maxExtraScore": 0.0, "multipleReviewersType": "or", "mustResultInput": 1, "notInput": false, "notInputVal": false, "okrRefFlag": null, "okrScore": null, "okrTaskName": null, "okrType": false, "openOkrScore": 0, "order": 0, "orgId": "38449673", "plusLimit": 0.0, "plusSubInterval": {"max": "", "min": ""}, "pointsNum": null, "progress": null, "progressStatus": null, "reserveOkrWeight": 0.0, "resultInputEmpId": "b458eec6-d7b9-4708-8605-7574c150131d", "resultInputEmpName": null, "resultInputType": "user", "reviewer": null, "score": null, "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "scorerType": "exam", "scoringRule": "及时指每季度次月5日前提交上个季度的市场调研报告；\n可行指会审通过；\n计分标准：每延迟1天提交，扣20分；可行性会审每被退回一次，扣10分。", "selfScore": null, "showFinishBar": 0, "showTargetValue": "", "startValue": null, "subNodes": [], "subtractLimit": 0.0, "superiorScore": null, "targetId": null, "targetName": null, "taskId": "1984054", "taskUserId": "2433247", "thresholdJson": "[]", "typeOrder": 0, "urgingFlag": null, "vetoFlag": null, "waitScores": [], "weightValue": null, "workItem": false, "workItemFinishValue": null, "workItemFinished": false}, {"actionId": null, "actionName": null, "alreadyNodes": [], "appointRaters": [], "autoScoreExFlag": null, "backup": null, "deptName": null, "empId": "96633281", "endValue": null, "evaluateStartDate": null, "examineOperType": null, "fieldValueList": null, "files": null, "finalSubmit": true, "finalSubmitFinishValue": 1, "finishValue": null, "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueComment": null, "formulaCondition": "[]", "formulaFields": [], "formulaType": 1, "id": "6979315", "indLevel": null, "indLevelGroup": null, "indLevelGroupId": null, "inputChangeRecord": null, "inputEmps": [{"adminType": null, "avatar": "", "empId": "b458eec6-d7b9-4708-8605-7574c150131d", "empName": "闫晶晶", "exUserId": "443739074137721579", "jobnumber": "448", "status": "on_the_job"}], "inputFormat": "num", "inputRole": null, "isNewEmp": 0, "isOkr": "false", "isSubmit": null, "isTypeLocked": "false", "itemActualFormula": null, "itemAutoScore": null, "itemFieldJson": "[]", "itemFinishValue": 80.0, "itemFinishValueText": null, "itemFormula": "", "itemFullScoreCfg": "false", "itemLimitCnt": null, "itemPlanFlag": null, "itemRule": "及时、准确", "itemScoreRule": {"appointRater": {"anonymous": null, "auditNodes": [], "multiType": "and", "nodeWeight": null, "open": 0, "rateMode": null}, "appointScoreFlag": "false", "appointScoreWeight": null, "id": null, "kpiItemId": "3daffcb4-7af8-4db4-b3f1-080b85ea6446", "kpiTypeId": "aae9a829-cc4a-4afe-b1ff-f86c930df705", "mutualScoreAnonymous": null, "mutualScoreAttendRule": null, "mutualScoreFlag": null, "mutualScoreVacancy": null, "mutualScoreViewRule": null, "mutualUserType": null, "mutualUserValue": null, "openRaterRule": true, "peerRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "peerScoreFlag": null, "peerScoreWeight": null, "peerUserName": null, "peerUserType": null, "peerUserValue": null, "selfRater": {"anonymous": null, "nodeWeight": null, "open": 0, "rateMode": null}, "selfScoreFlag": "false", "selfScoreViewRule": null, "selfScoreWeight": null, "subRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "subScoreFlag": null, "subScoreWeight": null, "subUserName": null, "subUserType": null, "subUserValue": null, "superRater": {"anonymous": null, "auditNodes": [{"approvalOrder": 1, "approverInfo": "1", "approverName": null, "approverType": "manager", "id": null, "modifyFlag": null, "multiType": "or", "node": "superior_score", "raters": [{"avatar": null, "dingUserId": null, "empId": "b458eec6-d7b9-4708-8605-7574c150131d", "empName": "闫晶晶", "level": 1, "roleId": null, "roleName": null, "type": 1, "weight": null}], "scoreWeight": 100.0, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": 100.0}], "multiType": "or", "nodeWeight": 100.0, "open": 1, "rateMode": "item", "superiorScoreOrder": "sameTime"}, "superiorScoreFlag": "true", "superiorScoreVacancy": null, "superiorScoreViewRule": null, "superiorScoreWeight": 100.0, "taskId": null, "taskUserId": "2433247"}, "itemScoreValue": "{\"type\":\"toMainScore\"}", "itemTags": null, "itemTargetValue": 0.0, "itemTargetValueText": "", "itemType": "measurable", "itemUnit": "天", "itemWeight": 10.0, "kpiItemId": "3daffcb4-7af8-4db4-b3f1-080b85ea6446", "kpiItemName": "行业产品线梳理及时性", "kpiTypeClassify": "custom", "kpiTypeId": "aae9a829-cc4a-4afe-b1ff-f86c930df705", "kpiTypeName": "岗位绩效", "kpiTypeWeight": 80.0, "kr": false, "managerInput": false, "managerLevel": "", "maxExtraScore": 0.0, "multipleReviewersType": "or", "mustResultInput": 1, "notInput": false, "notInputVal": false, "okrRefFlag": null, "okrScore": null, "okrTaskName": null, "okrType": false, "openOkrScore": 0, "order": 1, "orgId": "38449673", "plusLimit": 0.0, "plusSubInterval": {"max": "", "min": ""}, "pointsNum": null, "progress": null, "progressStatus": null, "reserveOkrWeight": 0.0, "resultInputEmpId": "b458eec6-d7b9-4708-8605-7574c150131d", "resultInputEmpName": null, "resultInputType": "user", "reviewer": null, "score": null, "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "scorerType": "exam", "scoringRule": "及时指每季度次月5日前更新/完善《行业产品线梳理表》并提交\n计分标准：每延迟1天提交，扣20分；准备性每错误一处一次，扣10分", "selfScore": null, "showFinishBar": 0, "showTargetValue": "", "startValue": null, "subNodes": [], "subtractLimit": 0.0, "superiorScore": null, "targetId": null, "targetName": null, "taskId": "1984054", "taskUserId": "2433247", "thresholdJson": "[]", "typeOrder": 0, "urgingFlag": null, "vetoFlag": null, "waitScores": [], "weightValue": null, "workItem": false, "workItemFinishValue": null, "workItemFinished": false}, {"actionId": null, "actionName": null, "alreadyNodes": [], "appointRaters": [], "autoScoreExFlag": null, "backup": null, "deptName": null, "empId": "96633281", "endValue": null, "evaluateStartDate": null, "examineOperType": null, "fieldValueList": null, "files": null, "finalSubmit": true, "finalSubmitFinishValue": 1, "finishValue": null, "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueComment": null, "formulaCondition": "[]", "formulaFields": [], "formulaType": 1, "id": "6979316", "indLevel": null, "indLevelGroup": null, "indLevelGroupId": null, "inputChangeRecord": null, "inputEmps": [{"adminType": null, "avatar": "", "empId": "b458eec6-d7b9-4708-8605-7574c150131d", "empName": "闫晶晶", "exUserId": "443739074137721579", "jobnumber": "448", "status": "on_the_job"}], "inputFormat": "num", "inputRole": null, "isNewEmp": 0, "isOkr": "false", "isSubmit": null, "isTypeLocked": "false", "itemActualFormula": null, "itemAutoScore": null, "itemFieldJson": "[]", "itemFinishValue": 80.0, "itemFinishValueText": null, "itemFormula": "", "itemFullScoreCfg": "false", "itemLimitCnt": null, "itemPlanFlag": null, "itemRule": "及时、可行", "itemScoreRule": {"appointRater": {"anonymous": null, "auditNodes": [], "multiType": "and", "nodeWeight": null, "open": 0, "rateMode": null}, "appointScoreFlag": "false", "appointScoreWeight": null, "id": null, "kpiItemId": "8af4e81a-827a-4c58-a2ca-bb0ffa9832f7", "kpiTypeId": "aae9a829-cc4a-4afe-b1ff-f86c930df705", "mutualScoreAnonymous": null, "mutualScoreAttendRule": null, "mutualScoreFlag": null, "mutualScoreVacancy": null, "mutualScoreViewRule": null, "mutualUserType": null, "mutualUserValue": null, "openRaterRule": true, "peerRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "peerScoreFlag": null, "peerScoreWeight": null, "peerUserName": null, "peerUserType": null, "peerUserValue": null, "selfRater": {"anonymous": null, "nodeWeight": null, "open": 0, "rateMode": null}, "selfScoreFlag": "false", "selfScoreViewRule": null, "selfScoreWeight": null, "subRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "subScoreFlag": null, "subScoreWeight": null, "subUserName": null, "subUserType": null, "subUserValue": null, "superRater": {"anonymous": null, "auditNodes": [{"approvalOrder": 1, "approverInfo": "1", "approverName": null, "approverType": "manager", "id": null, "modifyFlag": null, "multiType": "or", "node": "superior_score", "raters": [{"avatar": null, "dingUserId": null, "empId": "b458eec6-d7b9-4708-8605-7574c150131d", "empName": "闫晶晶", "level": 1, "roleId": null, "roleName": null, "type": 1, "weight": null}], "scoreWeight": 100.0, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": 100.0}], "multiType": "or", "nodeWeight": 100.0, "open": 1, "rateMode": "item", "superiorScoreOrder": "sameTime"}, "superiorScoreFlag": "true", "superiorScoreVacancy": null, "superiorScoreViewRule": null, "superiorScoreWeight": 100.0, "taskId": null, "taskUserId": "2433247"}, "itemScoreValue": "{\"type\":\"toMainScore\"}", "itemTags": null, "itemTargetValue": 0.0, "itemTargetValueText": "", "itemType": "non-measurable", "itemUnit": "%", "itemWeight": 25.0, "kpiItemId": "8af4e81a-827a-4c58-a2ca-bb0ffa9832f7", "kpiItemName": "行业应用PPT编制及时可行性", "kpiTypeClassify": "custom", "kpiTypeId": "aae9a829-cc4a-4afe-b1ff-f86c930df705", "kpiTypeName": "岗位绩效", "kpiTypeWeight": 80.0, "kr": false, "managerInput": false, "managerLevel": "", "maxExtraScore": 0.0, "multipleReviewersType": "or", "mustResultInput": 1, "notInput": false, "notInputVal": false, "okrRefFlag": null, "okrScore": null, "okrTaskName": null, "okrType": false, "openOkrScore": 0, "order": 2, "orgId": "38449673", "plusLimit": 0.0, "plusSubInterval": {"max": "", "min": ""}, "pointsNum": null, "progress": null, "progressStatus": null, "reserveOkrWeight": 0.0, "resultInputEmpId": "b458eec6-d7b9-4708-8605-7574c150131d", "resultInputEmpName": null, "resultInputType": "user", "reviewer": null, "score": null, "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "scorerType": "exam", "scoringRule": "及时指每季度次月5日前编制并提交\n可行指会审通过\n计分标准：每延迟1天提交，扣20分；可行性会审每被退回一次，扣10分", "selfScore": null, "showFinishBar": 0, "showTargetValue": "", "startValue": null, "subNodes": [], "subtractLimit": 0.0, "superiorScore": null, "targetId": null, "targetName": null, "taskId": "1984054", "taskUserId": "2433247", "thresholdJson": "[]", "typeOrder": 0, "urgingFlag": null, "vetoFlag": null, "waitScores": [], "weightValue": null, "workItem": false, "workItemFinishValue": null, "workItemFinished": false}, {"actionId": null, "actionName": null, "alreadyNodes": [], "appointRaters": [], "autoScoreExFlag": null, "backup": null, "deptName": null, "empId": "96633281", "endValue": null, "evaluateStartDate": null, "examineOperType": null, "fieldValueList": null, "files": null, "finalSubmit": true, "finalSubmitFinishValue": 1, "finishValue": null, "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueComment": null, "formulaCondition": "[]", "formulaFields": [], "formulaType": 1, "id": "6979317", "indLevel": null, "indLevelGroup": null, "indLevelGroupId": null, "inputChangeRecord": null, "inputEmps": [{"adminType": null, "avatar": "", "empId": "b458eec6-d7b9-4708-8605-7574c150131d", "empName": "闫晶晶", "exUserId": "443739074137721579", "jobnumber": "448", "status": "on_the_job"}], "inputFormat": "num", "inputRole": null, "isNewEmp": 0, "isOkr": "false", "isSubmit": null, "isTypeLocked": "false", "itemActualFormula": null, "itemAutoScore": null, "itemFieldJson": "[]", "itemFinishValue": 70.0, "itemFinishValueText": null, "itemFormula": "", "itemFullScoreCfg": "false", "itemLimitCnt": null, "itemPlanFlag": null, "itemRule": "及时、准确", "itemScoreRule": {"appointRater": {"anonymous": null, "auditNodes": [], "multiType": "and", "nodeWeight": null, "open": 0, "rateMode": null}, "appointScoreFlag": "false", "appointScoreWeight": null, "id": null, "kpiItemId": "a5aa54d2-7632-41fd-b3be-c61906b94b0e", "kpiTypeId": "aae9a829-cc4a-4afe-b1ff-f86c930df705", "mutualScoreAnonymous": null, "mutualScoreAttendRule": null, "mutualScoreFlag": null, "mutualScoreVacancy": null, "mutualScoreViewRule": null, "mutualUserType": null, "mutualUserValue": null, "openRaterRule": true, "peerRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "peerScoreFlag": null, "peerScoreWeight": null, "peerUserName": null, "peerUserType": null, "peerUserValue": null, "selfRater": {"anonymous": null, "nodeWeight": null, "open": 0, "rateMode": null}, "selfScoreFlag": "false", "selfScoreViewRule": null, "selfScoreWeight": null, "subRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "subScoreFlag": null, "subScoreWeight": null, "subUserName": null, "subUserType": null, "subUserValue": null, "superRater": {"anonymous": null, "auditNodes": [{"approvalOrder": 1, "approverInfo": "1", "approverName": null, "approverType": "manager", "id": null, "modifyFlag": null, "multiType": "or", "node": "superior_score", "raters": [{"avatar": null, "dingUserId": null, "empId": "b458eec6-d7b9-4708-8605-7574c150131d", "empName": "闫晶晶", "level": 1, "roleId": null, "roleName": null, "type": 1, "weight": null}], "scoreWeight": 100.0, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": 100.0}], "multiType": "or", "nodeWeight": 100.0, "open": 1, "rateMode": "item", "superiorScoreOrder": "sameTime"}, "superiorScoreFlag": "true", "superiorScoreVacancy": null, "superiorScoreViewRule": null, "superiorScoreWeight": 100.0, "taskId": null, "taskUserId": "2433247"}, "itemScoreValue": "{\"type\":\"toMainScore\"}", "itemTags": null, "itemTargetValue": 0.0, "itemTargetValueText": "", "itemType": "measurable", "itemUnit": "天", "itemWeight": 20.0, "kpiItemId": "a5aa54d2-7632-41fd-b3be-c61906b94b0e", "kpiItemName": "客户池准确度", "kpiTypeClassify": "custom", "kpiTypeId": "aae9a829-cc4a-4afe-b1ff-f86c930df705", "kpiTypeName": "岗位绩效", "kpiTypeWeight": 80.0, "kr": false, "managerInput": false, "managerLevel": "", "maxExtraScore": 0.0, "multipleReviewersType": "or", "mustResultInput": 1, "notInput": false, "notInputVal": false, "okrRefFlag": null, "okrScore": null, "okrTaskName": null, "okrType": false, "openOkrScore": 0, "order": 3, "orgId": "38449673", "plusLimit": 0.0, "plusSubInterval": {"max": "", "min": ""}, "pointsNum": null, "progress": null, "progressStatus": null, "reserveOkrWeight": 0.0, "resultInputEmpId": "b458eec6-d7b9-4708-8605-7574c150131d", "resultInputEmpName": null, "resultInputType": "user", "reviewer": null, "score": null, "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "scorerType": "exam", "scoringRule": "及时指每季度次月5日前收集、整理并提交\n计分标准：每延迟1天提交，扣20分；准确性稽核每被退回一次，扣10分", "selfScore": null, "showFinishBar": 0, "showTargetValue": "", "startValue": null, "subNodes": [], "subtractLimit": 0.0, "superiorScore": null, "targetId": null, "targetName": null, "taskId": "1984054", "taskUserId": "2433247", "thresholdJson": "[]", "typeOrder": 0, "urgingFlag": null, "vetoFlag": null, "waitScores": [], "weightValue": null, "workItem": false, "workItemFinishValue": null, "workItemFinished": false}, {"actionId": null, "actionName": null, "alreadyNodes": [], "appointRaters": [], "autoScoreExFlag": null, "backup": null, "deptName": null, "empId": "96633281", "endValue": null, "evaluateStartDate": null, "examineOperType": null, "fieldValueList": null, "files": null, "finalSubmit": true, "finalSubmitFinishValue": 1, "finishValue": null, "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueComment": null, "formulaCondition": "[]", "formulaFields": [], "formulaType": 1, "id": "6979318", "indLevel": null, "indLevelGroup": null, "indLevelGroupId": null, "inputChangeRecord": null, "inputEmps": [{"adminType": null, "avatar": "", "empId": "b458eec6-d7b9-4708-8605-7574c150131d", "empName": "闫晶晶", "exUserId": "443739074137721579", "jobnumber": "448", "status": "on_the_job"}], "inputFormat": "num", "inputRole": null, "isNewEmp": 0, "isOkr": "false", "isSubmit": null, "isTypeLocked": "false", "itemActualFormula": null, "itemAutoScore": null, "itemFieldJson": "[]", "itemFinishValue": 70.0, "itemFinishValueText": null, "itemFormula": "", "itemFullScoreCfg": "false", "itemLimitCnt": null, "itemPlanFlag": null, "itemRule": "及时、有效性", "itemScoreRule": {"appointRater": {"anonymous": null, "auditNodes": [], "multiType": "and", "nodeWeight": null, "open": 0, "rateMode": null}, "appointScoreFlag": "false", "appointScoreWeight": null, "id": null, "kpiItemId": "28476ae2-3453-428b-9b2f-a33dd4dc7ae2", "kpiTypeId": "aae9a829-cc4a-4afe-b1ff-f86c930df705", "mutualScoreAnonymous": null, "mutualScoreAttendRule": null, "mutualScoreFlag": null, "mutualScoreVacancy": null, "mutualScoreViewRule": null, "mutualUserType": null, "mutualUserValue": null, "openRaterRule": true, "peerRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "peerScoreFlag": null, "peerScoreWeight": null, "peerUserName": null, "peerUserType": null, "peerUserValue": null, "selfRater": {"anonymous": null, "nodeWeight": null, "open": 0, "rateMode": null}, "selfScoreFlag": "false", "selfScoreViewRule": null, "selfScoreWeight": null, "subRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "subScoreFlag": null, "subScoreWeight": null, "subUserName": null, "subUserType": null, "subUserValue": null, "superRater": {"anonymous": null, "auditNodes": [{"approvalOrder": 1, "approverInfo": "1", "approverName": null, "approverType": "manager", "id": null, "modifyFlag": null, "multiType": "or", "node": "superior_score", "raters": [{"avatar": null, "dingUserId": null, "empId": "b458eec6-d7b9-4708-8605-7574c150131d", "empName": "闫晶晶", "level": 1, "roleId": null, "roleName": null, "type": 1, "weight": null}], "scoreWeight": 100.0, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": 100.0}], "multiType": "or", "nodeWeight": 100.0, "open": 1, "rateMode": "item", "superiorScoreOrder": "sameTime"}, "superiorScoreFlag": "true", "superiorScoreVacancy": null, "superiorScoreViewRule": null, "superiorScoreWeight": 100.0, "taskId": null, "taskUserId": "2433247"}, "itemScoreValue": "{\"type\":\"toMainScore\"}", "itemTags": null, "itemTargetValue": 100.0, "itemTargetValueText": "", "itemType": "non-measurable", "itemUnit": "%", "itemWeight": 15.0, "kpiItemId": "28476ae2-3453-428b-9b2f-a33dd4dc7ae2", "kpiItemName": "三个三推动有效性", "kpiTypeClassify": "custom", "kpiTypeId": "aae9a829-cc4a-4afe-b1ff-f86c930df705", "kpiTypeName": "岗位绩效", "kpiTypeWeight": 80.0, "kr": false, "managerInput": false, "managerLevel": "", "maxExtraScore": 0.0, "multipleReviewersType": "or", "mustResultInput": 1, "notInput": false, "notInputVal": false, "okrRefFlag": null, "okrScore": null, "okrTaskName": null, "okrType": false, "openOkrScore": 0, "order": 4, "orgId": "38449673", "plusLimit": 0.0, "plusSubInterval": {"max": "", "min": ""}, "pointsNum": null, "progress": null, "progressStatus": null, "reserveOkrWeight": 0.0, "resultInputEmpId": "b458eec6-d7b9-4708-8605-7574c150131d", "resultInputEmpName": null, "resultInputType": "user", "reviewer": null, "score": null, "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "scorerType": "exam", "scoringRule": "三个三指1个用胶点规划3档产品、每1个产品设计3个配方、每1种原料寻找3个档次供应商各3家\n有效指每季度次月5日前编制3个3推动报告（随附原始报表）\n计分标准：每延迟1天提交，扣20分；每个3每未按计划推动一项，扣20分", "selfScore": null, "showFinishBar": 0, "showTargetValue": "", "startValue": null, "subNodes": [], "subtractLimit": 0.0, "superiorScore": null, "targetId": null, "targetName": null, "taskId": "1984054", "taskUserId": "2433247", "thresholdJson": "[]", "typeOrder": 0, "urgingFlag": null, "vetoFlag": null, "waitScores": [], "weightValue": null, "workItem": false, "workItemFinishValue": null, "workItemFinished": false}, {"actionId": null, "actionName": null, "alreadyNodes": [], "appointRaters": [], "autoScoreExFlag": null, "backup": null, "deptName": null, "empId": "96633281", "endValue": null, "evaluateStartDate": null, "examineOperType": null, "fieldValueList": null, "files": null, "finalSubmit": false, "finalSubmitFinishValue": 0, "finishValue": null, "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueComment": null, "formulaCondition": "[{\"companyId\":\"8439320d-157f-49ae-bdc6-d4d7107b492a\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"704ac637-26c8-4613-a43c-f7ecf7beca97\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true,\"kpiItemId\":\"79c35783-f65a-4386-97cf-4e8640179d2a\"},{\"companyId\":\"8439320d-157f-49ae-bdc6-d4d7107b492a\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"db6931f3-57ca-44be-9cc0-89dfe8de0d8f\",\"isSystemField\":\"false\",\"label\":\"目标值\",\"value\":0,\"yVal\":true,\"kpiItemId\":\"79c35783-f65a-4386-97cf-4e8640179d2a\"}]", "formulaFields": [], "formulaType": 1, "id": "6979319", "indLevel": null, "indLevelGroup": null, "indLevelGroupId": null, "inputChangeRecord": null, "inputEmps": null, "inputFormat": "num", "inputRole": null, "isNewEmp": 0, "isOkr": "false", "isSubmit": null, "isTypeLocked": "false", "itemActualFormula": null, "itemAutoScore": null, "itemFieldJson": "[{\"companyId\":\"8439320d-157f-49ae-bdc6-d4d7107b492a\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"704ac637-26c8-4613-a43c-f7ecf7beca97\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"companyId\":\"8439320d-157f-49ae-bdc6-d4d7107b492a\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"db6931f3-57ca-44be-9cc0-89dfe8de0d8f\",\"isSystemField\":\"false\",\"label\":\"目标值\",\"value\":0,\"yVal\":true}]", "itemFinishValue": null, "itemFinishValueText": null, "itemFormula": "", "itemFullScoreCfg": "false", "itemLimitCnt": null, "itemPlanFlag": null, "itemRule": "", "itemScoreRule": {"appointRater": {"anonymous": null, "auditNodes": [], "multiType": "and", "nodeWeight": null, "open": 0, "rateMode": null}, "appointScoreFlag": "false", "appointScoreWeight": null, "id": null, "kpiItemId": "79c35783-f65a-4386-97cf-4e8640179d2a", "kpiTypeId": "aae9a829-cc4a-4afe-b1ff-f86c930df705", "mutualScoreAnonymous": null, "mutualScoreAttendRule": null, "mutualScoreFlag": null, "mutualScoreVacancy": null, "mutualScoreViewRule": null, "mutualUserType": null, "mutualUserValue": null, "openRaterRule": true, "peerRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "peerScoreFlag": null, "peerScoreWeight": null, "peerUserName": null, "peerUserType": null, "peerUserValue": null, "selfRater": {"anonymous": null, "nodeWeight": null, "open": 0, "rateMode": null}, "selfScoreFlag": "false", "selfScoreViewRule": null, "selfScoreWeight": null, "subRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "subScoreFlag": null, "subScoreWeight": null, "subUserName": null, "subUserType": null, "subUserValue": null, "superRater": {"anonymous": null, "auditNodes": [{"approvalOrder": 1, "approverInfo": "1", "approverName": null, "approverType": "manager", "id": null, "modifyFlag": null, "multiType": "or", "node": "superior_score", "raters": [{"avatar": null, "dingUserId": null, "empId": "b458eec6-d7b9-4708-8605-7574c150131d", "empName": "闫晶晶", "level": 1, "roleId": null, "roleName": null, "type": 1, "weight": null}], "scoreWeight": 100.0, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": 100.0}], "multiType": "or", "nodeWeight": 100.0, "open": 1, "rateMode": "item", "superiorScoreOrder": "sameTime"}, "superiorScoreFlag": "true", "superiorScoreVacancy": null, "superiorScoreViewRule": null, "superiorScoreWeight": 100.0, "taskId": null, "taskUserId": "2433247"}, "itemScoreValue": "{\"type\":\"toMainScore\"}", "itemTags": null, "itemTargetValue": 0.0, "itemTargetValueText": null, "itemType": "non-measurable", "itemUnit": "%", "itemWeight": 10.0, "kpiItemId": "79c35783-f65a-4386-97cf-4e8640179d2a", "kpiItemName": "岗位工作满意度", "kpiTypeClassify": "custom", "kpiTypeId": "aae9a829-cc4a-4afe-b1ff-f86c930df705", "kpiTypeName": "岗位绩效", "kpiTypeWeight": 80.0, "kr": false, "managerInput": false, "managerLevel": "", "maxExtraScore": 0.0, "multipleReviewersType": "or", "mustResultInput": 0, "notInput": false, "notInputVal": false, "okrRefFlag": null, "okrScore": null, "okrTaskName": null, "okrType": false, "openOkrScore": 0, "order": 5, "orgId": "38449673", "plusLimit": 0.0, "plusSubInterval": {"max": "", "min": ""}, "pointsNum": null, "progress": null, "progressStatus": null, "reserveOkrWeight": 0.0, "resultInputEmpId": null, "resultInputEmpName": null, "resultInputType": "no", "reviewer": null, "score": null, "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "scorerType": "exam", "scoringRule": "本项为定性指标，主要维度包括但不限于工作任务完成情况、问题解决、跨中心/部门协作、学习与成长、工作态度等\n计分规则：由直接上级评分 ", "selfScore": null, "showFinishBar": 0, "showTargetValue": "", "startValue": null, "subNodes": [], "subtractLimit": 0.0, "superiorScore": null, "targetId": null, "targetName": null, "taskId": "1984054", "taskUserId": "2433247", "thresholdJson": "[]", "typeOrder": 0, "urgingFlag": null, "vetoFlag": null, "waitScores": [], "weightValue": null, "workItem": false, "workItemFinishValue": null, "workItemFinished": false}], "kpiTypeClassify": "custom", "kpiTypeId": "aae9a829-cc4a-4afe-b1ff-f86c930df705", "kpiTypeName": "岗位绩效", "kpiTypeUsedFields": [], "kpiTypeWeight": 80.0, "lockedItems": null, "maxExtraScore": 0.0, "notExtType": true, "openOkrScore": 0, "openRaterRule": true, "peerRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "plusSubInterval": {"max": "", "min": ""}, "raterBack": false, "reserveOkrWeight": 0.0, "scoreOptType": 2, "selfRater": {"anonymous": null, "nodeWeight": null, "open": 0, "rateMode": null}, "subRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "superRater": {"anonymous": null, "auditNodes": [{"approvalOrder": 1, "approverInfo": "1", "approverName": null, "approverType": "manager", "id": null, "modifyFlag": null, "multiType": "or", "node": "superior_score", "raters": [{"avatar": null, "dingUserId": null, "empId": "b458eec6-d7b9-4708-8605-7574c150131d", "empName": "闫晶晶", "level": 1, "roleId": null, "roleName": null, "type": 1, "weight": null}], "scoreWeight": 100.0, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": 100.0}], "multiType": "or", "nodeWeight": 100.0, "open": 1, "rateMode": "item", "superiorScoreOrder": "sameTime"}, "taskUserId": "2433247", "typeLevel": null, "typeOrder": 0, "typeRule": null, "waitScores": []}, {"alreadyScores": [], "appointRater": {"anonymous": null, "auditNodes": [], "multiType": "and", "nodeWeight": null, "open": 0, "rateMode": null}, "des": null, "finishValue": true, "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "importOkrFlag": "false", "indLevelGroup": null, "indLevelGroupId": null, "isOkr": "false", "itemLimitCnt": null, "items": [{"actionId": null, "actionName": null, "alreadyNodes": [], "appointRaters": [], "autoScoreExFlag": null, "backup": null, "deptName": null, "empId": "96633281", "endValue": null, "evaluateStartDate": null, "examineOperType": null, "fieldValueList": null, "files": null, "finalSubmit": false, "finalSubmitFinishValue": 0, "finishValue": null, "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueComment": null, "formulaCondition": "[]", "formulaFields": [], "formulaType": 1, "id": "6979320", "indLevel": null, "indLevelGroup": null, "indLevelGroupId": null, "inputChangeRecord": null, "inputEmps": null, "inputFormat": "num", "inputRole": null, "isNewEmp": 0, "isOkr": "false", "isSubmit": null, "isTypeLocked": "true", "itemActualFormula": null, "itemAutoScore": null, "itemFieldJson": "[]", "itemFinishValue": null, "itemFinishValueText": null, "itemFormula": "", "itemFullScoreCfg": "false", "itemLimitCnt": null, "itemPlanFlag": null, "itemRule": "无", "itemScoreRule": null, "itemScoreValue": "{\"type\":\"toMainScore\"}", "itemTags": null, "itemTargetValue": 0.0, "itemTargetValueText": "", "itemType": "non-measurable", "itemUnit": "%", "itemWeight": 100.0, "kpiItemId": "105dde03-f51c-4824-8a7d-7924e8dc826b", "kpiItemName": "组织绩效得分", "kpiTypeClassify": "custom", "kpiTypeId": "d2ca217e-8395-4580-bf86-e0bdf001bd9c", "kpiTypeName": "组织绩效", "kpiTypeWeight": 20.0, "kr": false, "managerInput": false, "managerLevel": "", "maxExtraScore": 0.0, "multipleReviewersType": "or", "mustResultInput": 0, "notInput": false, "notInputVal": false, "okrRefFlag": null, "okrScore": null, "okrTaskName": null, "okrType": false, "openOkrScore": 0, "order": 0, "orgId": "38449673", "plusLimit": 0.0, "plusSubInterval": {"max": "", "min": ""}, "pointsNum": null, "progress": null, "progressStatus": null, "reserveOkrWeight": 0.0, "resultInputEmpId": "", "resultInputEmpName": null, "resultInputType": "no", "reviewer": null, "score": null, "scorerObjId": [{"objItems": [{"managerLevel": null, "managerName": null, "objId": "f934918c-9ca4-450a-8791-fef6913ddce7", "objName": "林燕梅", "roleId": "9d6a8296-c607-4f64-8650-12554c2ed321", "roleName": "人事专员"}, {"managerLevel": null, "managerName": null, "objId": "87855356", "objName": "曾冬梅", "roleId": "9d6a8296-c607-4f64-8650-12554c2ed321", "roleName": "人事专员"}, {"managerLevel": null, "managerName": null, "objId": "fc2665c6-5b1c-4b85-9fce-413f21ef79ec", "objName": "苏过灵", "roleId": "9d6a8296-c607-4f64-8650-12554c2ed321", "roleName": "人事专员"}], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "scorerType": "user", "scoringRule": "按所属中心当月组织绩效得分", "selfScore": null, "showFinishBar": 1, "showTargetValue": "", "startValue": null, "subNodes": [], "subtractLimit": 0.0, "superiorScore": null, "targetId": null, "targetName": null, "taskId": "1984054", "taskUserId": "2433247", "thresholdJson": "[]", "typeOrder": 1, "urgingFlag": null, "vetoFlag": null, "waitScores": [], "weightValue": null, "workItem": false, "workItemFinishValue": null, "workItemFinished": false}], "kpiTypeClassify": "custom", "kpiTypeId": "d2ca217e-8395-4580-bf86-e0bdf001bd9c", "kpiTypeName": "组织绩效", "kpiTypeUsedFields": [], "kpiTypeWeight": 20.0, "lockedItems": ["typeWeight", "addIndex", "modifyIndex", "deleteIndex"], "maxExtraScore": 0.0, "notExtType": true, "openOkrScore": 0, "openRaterRule": true, "peerRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "plusSubInterval": {"max": "", "min": ""}, "raterBack": false, "reserveOkrWeight": 0.0, "scoreOptType": 2, "selfRater": {"anonymous": null, "nodeWeight": null, "open": 0, "rateMode": null}, "subRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "superRater": {"anonymous": null, "auditNodes": [{"approvalOrder": 1, "approverInfo": "1", "approverName": null, "approverType": "manager", "id": null, "modifyFlag": null, "multiType": "or", "node": "superior_score", "raters": [{"avatar": null, "dingUserId": null, "empId": "b458eec6-d7b9-4708-8605-7574c150131d", "empName": "闫晶晶", "level": 1, "roleId": null, "roleName": null, "type": 1, "weight": null}], "scoreWeight": 100.0, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": 100.0}], "multiType": "or", "nodeWeight": 100.0, "open": 1, "rateMode": "item", "superiorScoreOrder": "sameTime"}, "taskUserId": "2433247", "typeLevel": null, "typeOrder": 1, "typeRule": null, "waitScores": []}], "empty": false, "groups": null, "mergedAudits": [], "modNum": 767, "oldTypeAudits": null}, "levelGroupId": "", "manualPublicOpen": true, "notAllowExceed": false, "publishResult": {"dimension": 11, "notPublic": false, "opEmps": [{"objItems": [], "objType": "taskAdmin"}], "open": 1, "toEmps": [{"dept": false, "objItems": null, "objType": "emp"}], "type": "manual"}, "publishResultOpen": true, "resultAuditingOpen": true, "ruleName": "行业产品专员绩效合约(内勤)", "s3AppointRater": {"anonymous": null, "auditNodes": [], "multiType": "and", "nodeWeight": null, "open": 0, "rateMode": null}, "s3PeerRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "s3SelfRater": {"anonymous": null, "nodeWeight": null, "open": 0, "rateMode": null}, "s3SubRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "s3SuperRater": {"anonymous": null, "auditNodes": [{"approvalOrder": 1, "approverInfo": "1", "approverName": null, "approverType": "manager", "id": null, "modifyFlag": null, "multiType": "or", "node": "superior_score", "raters": [{"avatar": null, "dingUserId": null, "empId": "b458eec6-d7b9-4708-8605-7574c150131d", "empName": "闫晶晶", "level": 1, "roleId": null, "roleName": null, "type": 1, "weight": null}], "scoreWeight": 100.0, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": 100.0}], "multiType": "or", "nodeWeight": 100.0, "open": 1, "rateMode": "item", "superiorScoreOrder": "sameTime"}, "sameTime": true, "scoreChain": {"chain": [{"next": {"next": null, "scoreNodes": [{"appointNode": false, "nextSuper": null, "node": "ITEM_SCORE", "order": 1}, {"appointNode": false, "nextSuper": null, "node": "SUPERIOR_SCORE", "order": 1}], "totalLevel": false, "type": 0}, "scoreNodes": [{"appointNode": false, "nextSuper": null, "node": "AUTO", "order": 1}], "totalLevel": false, "type": 0}, {"next": null, "scoreNodes": null, "totalLevel": false, "type": 0}]}, "scoreConf": {"multiType": "or", "transferFlag": "true", "vacancyApproveInfo": null, "vacancyApproveName": null, "vacancyApproveType": null}, "scoreSortConf": {"exeType": 1, "oneByOneChain": null, "sameTime": true, "sortItems": [{"name": "自评", "sort": 1, "type": 10}, {"name": "同级互评", "sort": 2, "type": 20}, {"name": "下级互评", "sort": 3, "type": 30}, {"name": "上级评分", "sort": 4, "type": 40}, {"name": "指定评", "sort": 5, "type": 50}]}, "scoreValueConf": {"baseScore": 0.0, "customFullScore": 100, "exceedFullScore": false, "fullScoreRange": true, "scoreRangeType": "fullScore"}, "scoreView": {"appointScoreAnonymous": "false", "appointScoreViewRule": {"anonymous": null, "appoint": "", "examinee": "", "mutual": "", "superior": ""}, "mutualScoreAnonymous": "false", "mutualScoreViewRule": {"anonymous": null, "appoint": "", "examinee": "", "mutual": "", "superior": ""}, "selfScoreViewRule": {"anonymous": null, "appoint": "", "examinee": "score,attach", "mutual": "", "superior": ""}, "superiorScoreAnonymous": "false", "superiorScoreViewRule": {"anonymous": null, "appoint": "score,attach", "examinee": "", "mutual": "", "superior": "score,attach"}}, "showResultType": 6, "stageChain": null, "submitWithWeight": false, "systemFullScore": null, "taskId": "1984054", "taskName": "2023年第4季度市场中心绩效合约", "totalLevelRaters": [], "totalLevelResults": [], "totalScoreRs": null, "typeWeightConf": {"checkItemWeight": 1, "itemWeightLimit": 100, "limit100Weight": 1, "open": 1}, "typeWeightOpen": true}