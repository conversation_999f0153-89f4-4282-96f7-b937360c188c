{"appealConf": {"appealReceiver": null, "canAppealDay": null, "open": 0, "resultAppealNode": null}, "auditResult": {"auditNodes": [{"approvalOrder": 1, "approverInfo": "1", "approverName": "直属主管", "approverType": "manager", "id": null, "modifyFlag": null, "multiType": "or", "node": "final_result_audit", "raters": [{"avatar": "", "dingUserId": "443739074137721579", "empId": "b458eec6-d7b9-4708-8605-7574c150131d", "empName": "闫晶晶", "level": 1, "roleId": null, "roleName": null, "type": 1, "weight": null}], "scoreWeight": null, "status": null, "transferFlag": null, "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": null}], "commentReq": 0, "mergeConf": null, "multiType": "or", "open": 1, "transferFlag": "true", "vacancyApproveInfo": "", "vacancyApproveName": "", "vacancyApproveType": "superior"}, "baseScoreOpen": true, "commentConf": {"commentFlag": "notRequired", "commentRequiredHighValue": null, "commentRequiredValue": null, "plusOrSubComment": 0, "scoreSummarySwitch": -1}, "companyConf": {"canResSubmitInputFinish": 0, "enterOnDingMsg": 1, "enterOnScoring": 0, "evalEmpOnLogAuth": 1, "ignoreVacancyManager": 0, "indInput202308021": 0, "indLevelGroup": 0, "itemAuth": 0, "logAuthSet": null, "resultInputSendMsg": "true", "resultRankOpen20231213": 0, "supIsCanAutoScoreItem": 0, "tempAuth": 0}, "confirmResult": {"auto": 0, "autoDay": null, "dimension": 11, "open": 0, "sign": 0}, "confirmTask": {"auditNodes": [{"approvalOrder": 1, "approverInfo": "1", "approverName": "直属主管", "approverType": "manager", "id": null, "modifyFlag": "true", "multiType": "or", "node": "modify_item_audit", "raters": [{"avatar": "", "dingUserId": "443739074137721579", "empId": "b458eec6-d7b9-4708-8605-7574c150131d", "empName": "闫晶晶", "level": 1, "roleId": null, "roleName": null, "type": 1, "weight": null}], "scoreWeight": null, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": null}, {"approvalOrder": 2, "approverInfo": "", "approverName": "被考核人", "approverType": "taskEmp", "id": null, "modifyFlag": "true", "multiType": "or", "node": "modify_item_audit", "raters": [{"avatar": null, "dingUserId": null, "empId": "96633281", "empName": "周星剑", "level": null, "roleId": null, "roleName": null, "type": 0, "weight": null}], "scoreWeight": null, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": null}], "confirmLTDay": 1, "modifyAuditFlag": "true", "modifyItemDimension": "all", "noChangeSkipFlag": "false", "open": 1, "openConfirmLT": 0}, "createTotalLevelType": 1, "customFullScore": null, "deadLineConf": {"finishInputAudit": null, "open": 0, "taskAffirmDeadLine": null, "taskConfirmDeadLine": null, "taskEvalScore": null, "taskResultAudit": null, "taskResultConfirm": null}, "editExeIndi": {"auditNodes": [{"approvalOrder": 1, "approverInfo": "eafd93f2-6e39-4cf8-9a4a-565ddbbec905", "approverName": "戴亚茹", "approverType": "user", "id": null, "modifyFlag": null, "multiType": "or", "node": "change_item_audit", "raters": [{"avatar": null, "dingUserId": null, "empId": "eafd93f2-6e39-4cf8-9a4a-565ddbbec905", "empName": "戴亚茹", "level": null, "roleId": null, "roleName": null, "type": 0, "weight": null}], "scoreWeight": null, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": null}], "auditOpen": 1, "changeUsers": ["admin"], "open": 1}, "editStatus": 0, "empEvalId": "2433247", "enterScore": {"autoEnter": false, "enterScoreEmpType": 1, "enterScoreMethod": "manual", "scoreStartRuleDay": 1, "scoreStartRuleType": "before"}, "evaluateType": "simple", "finalScore": null, "finishValueAudit": {"auditNodes": [], "noChangeSkipFlag": null, "open": 0}, "fromOldTask": false, "indicatorCnt": 10, "initiator": "f934918c-9ca4-450a-8791-fef6913ddce7", "inputNotify": null, "kpiTypes": {"datas": [{"alreadyScores": [], "appointRater": {"anonymous": null, "auditNodes": [], "multiType": "and", "nodeWeight": null, "open": 0, "rateMode": null}, "des": null, "finishValue": true, "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "importOkrFlag": "false", "indLevelGroup": null, "indLevelGroupId": null, "isOkr": "false", "itemLimitCnt": null, "items": [{"actionId": null, "actionName": null, "alreadyNodes": [], "appointRaters": [], "autoScoreExFlag": null, "backup": null, "deptName": null, "empId": "96633281", "endValue": null, "evaluateStartDate": null, "examineOperType": null, "fieldValueList": null, "files": null, "finalSubmit": false, "finalSubmitFinishValue": 0, "finishValue": null, "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueComment": null, "formulaCondition": "[]", "formulaFields": [], "formulaType": 1, "id": "6979311", "indLevel": null, "indLevelGroup": null, "indLevelGroupId": null, "inputChangeRecord": null, "inputEmps": [{"adminType": null, "avatar": "", "empId": "665f9d49-1373-494d-90dc-7b1f9082afc9", "empName": "吴梅丽", "exUserId": "515915286321564812", "jobnumber": "8020", "status": "on_the_job"}], "inputFormat": "num", "inputRole": null, "isNewEmp": 0, "isOkr": "false", "isSubmit": null, "isTypeLocked": "true", "itemActualFormula": null, "itemAutoScore": null, "itemFieldJson": "[]", "itemFinishValue": 0.0, "itemFinishValueText": null, "itemFormula": "", "itemFullScoreCfg": "false", "itemLimitCnt": null, "itemPlanFlag": null, "itemRule": "1、发生本中心或者中心员工承担主要责任的生产安全及火灾事故的重特大伤亡事故、重大火灾事故、重大环境污染事故、重大安全责任事故的；\n2、发生本中心或者中心员工承担主要责任的工安事故损失≥300元；\n3、发生本中心或者中心员工承担主要责任的工安事故损失＜300元；\n4、出现其它非主要责任的生产安全及火灾事故，或者其它重大事故以下，或者其它影响企业的负面事件，或者给企业造成经济损失的；\n5、出现外部质量监督主管部门抽检到本公司的产品不合格", "itemScoreRule": null, "itemScoreValue": "{\"type\":\"autoDefine\",\"value\":{\"from\":0,\"to\":\"\",\"step\":0.5}}", "itemTags": null, "itemTargetValue": 0.0, "itemTargetValueText": "", "itemType": "non-measurable", "itemUnit": "%", "itemWeight": 0.0, "kpiItemId": "ee2ab5fb-af80-4e40-a872-a81ddee5e63f", "kpiItemName": "生产安全和火灾事故", "kpiTypeClassify": "subtract", "kpiTypeId": "6f3704ca-97f5-4f44-84ad-c7be2feddd91", "kpiTypeName": "扣分项", "kpiTypeWeight": 0.0, "kr": false, "managerInput": false, "managerLevel": "", "maxExtraScore": 100.0, "multipleReviewersType": "or", "mustResultInput": 0, "notInput": false, "notInputVal": false, "okrRefFlag": null, "okrScore": null, "okrTaskName": null, "okrType": false, "openOkrScore": 0, "order": 0, "orgId": "38449673", "plusLimit": 0.0, "plusSubInterval": {"max": "", "min": ""}, "pointsNum": null, "progress": null, "progressStatus": null, "reserveOkrWeight": 0.0, "resultInputEmpId": "665f9d49-1373-494d-90dc-7b1f9082afc9", "resultInputEmpName": null, "resultInputType": "user", "reviewer": null, "score": null, "scorerObjId": [{"objItems": [{"managerLevel": null, "managerName": null, "objId": "f934918c-9ca4-450a-8791-fef6913ddce7", "objName": "林燕梅", "roleId": "9d6a8296-c607-4f64-8650-12554c2ed321", "roleName": "人事专员"}, {"managerLevel": null, "managerName": null, "objId": "87855356", "objName": "曾冬梅", "roleId": "9d6a8296-c607-4f64-8650-12554c2ed321", "roleName": "人事专员"}, {"managerLevel": null, "managerName": null, "objId": "fc2665c6-5b1c-4b85-9fce-413f21ef79ec", "objName": "苏过灵", "roleId": "9d6a8296-c607-4f64-8650-12554c2ed321", "roleName": "人事专员"}], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "scorerType": "user", "scoringRule": "1、直接责任人岗位绩效得分为0；相关责任人扣30分/次；部门/科室主管扣30分/次；中心主管扣30分/次；\n2、直接责任人扣30分/次；相关责任人扣25分/次；部门/科室主管扣20分/次；中心主管扣20分/次；\n3、直接责任人扣20分/次；相关责任人扣15分/次；部门/科室主管扣10分/次；中心主管扣10分/次；\n4、由绩效管理委员会确定扣分标准；\n5、直接责任人扣15分/次；相关责任人扣15分/次；部门/科室主管扣10分/次；中心主管扣10分/次。", "selfScore": null, "showFinishBar": 1, "showTargetValue": "", "startValue": null, "subNodes": [], "subtractLimit": 100.0, "superiorScore": null, "targetId": null, "targetName": null, "taskId": "1984054", "taskUserId": "2433247", "thresholdJson": "[]", "typeOrder": 3, "urgingFlag": null, "vetoFlag": null, "waitScores": [], "weightValue": null, "workItem": false, "workItemFinishValue": null, "workItemFinished": false}, {"actionId": null, "actionName": null, "alreadyNodes": [], "appointRaters": [], "autoScoreExFlag": null, "backup": null, "deptName": null, "empId": "96633281", "endValue": null, "evaluateStartDate": null, "examineOperType": null, "fieldValueList": null, "files": null, "finalSubmit": false, "finalSubmitFinishValue": 0, "finishValue": null, "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueComment": null, "formulaCondition": "[]", "formulaFields": [], "formulaType": 1, "id": "6979312", "indLevel": null, "indLevelGroup": null, "indLevelGroupId": null, "inputChangeRecord": null, "inputEmps": [{"adminType": null, "avatar": "", "empId": "fc2665c6-5b1c-4b85-9fce-413f21ef79ec", "empName": "苏过灵", "exUserId": "080648031533350813", "jobnumber": "506.0", "status": "on_the_job"}], "inputFormat": "num", "inputRole": null, "isNewEmp": 0, "isOkr": "false", "isSubmit": null, "isTypeLocked": "true", "itemActualFormula": null, "itemAutoScore": null, "itemFieldJson": "[]", "itemFinishValue": 0.0, "itemFinishValueText": null, "itemFormula": "", "itemFullScoreCfg": "false", "itemLimitCnt": null, "itemPlanFlag": null, "itemRule": "1、发生重大交通主责事故（公司车辆）、重大食品中毒事故、失窃事故；\n2、利用信息设备泄密及恶性操作等信息安全事件、经营信息泄露等。", "itemScoreRule": null, "itemScoreValue": "{\"type\":\"autoDefine\",\"value\":{\"from\":0,\"to\":\"\",\"step\":0.5}}", "itemTags": null, "itemTargetValue": 0.0, "itemTargetValueText": "", "itemType": "non-measurable", "itemUnit": "%", "itemWeight": 100.0, "kpiItemId": "6f0d4526-b500-4b9e-aab6-226b397f40d6", "kpiItemName": "其他事故", "kpiTypeClassify": "subtract", "kpiTypeId": "6f3704ca-97f5-4f44-84ad-c7be2feddd91", "kpiTypeName": "扣分项", "kpiTypeWeight": 0.0, "kr": false, "managerInput": false, "managerLevel": "", "maxExtraScore": 100.0, "multipleReviewersType": "or", "mustResultInput": 0, "notInput": false, "notInputVal": false, "okrRefFlag": null, "okrScore": null, "okrTaskName": null, "okrType": false, "openOkrScore": 0, "order": 1, "orgId": "38449673", "plusLimit": 0.0, "plusSubInterval": {"max": "", "min": ""}, "pointsNum": null, "progress": null, "progressStatus": null, "reserveOkrWeight": 0.0, "resultInputEmpId": "fc2665c6-5b1c-4b85-9fce-413f21ef79ec", "resultInputEmpName": null, "resultInputType": "user", "reviewer": null, "score": null, "scorerObjId": [{"objItems": [{"managerLevel": null, "managerName": null, "objId": "f934918c-9ca4-450a-8791-fef6913ddce7", "objName": "林燕梅", "roleId": "9d6a8296-c607-4f64-8650-12554c2ed321", "roleName": "人事专员"}, {"managerLevel": null, "managerName": null, "objId": "87855356", "objName": "曾冬梅", "roleId": "9d6a8296-c607-4f64-8650-12554c2ed321", "roleName": "人事专员"}, {"managerLevel": null, "managerName": null, "objId": "fc2665c6-5b1c-4b85-9fce-413f21ef79ec", "objName": "苏过灵", "roleId": "9d6a8296-c607-4f64-8650-12554c2ed321", "roleName": "人事专员"}], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "scorerType": "user", "scoringRule": "1、直接责任人岗位绩效得分为0；相关责任人扣30分/次；部门/科室主管扣30分/次；中心主管扣30分/次；\n2、直接责任人岗位绩效得分为0；相关责任人扣30分/次；部门/科室主管扣30分/次；中心主管扣30分/次。", "selfScore": null, "showFinishBar": 1, "showTargetValue": "", "startValue": null, "subNodes": [], "subtractLimit": 100.0, "superiorScore": null, "targetId": null, "targetName": null, "taskId": "1984054", "taskUserId": "2433247", "thresholdJson": "[]", "typeOrder": 3, "urgingFlag": null, "vetoFlag": null, "waitScores": [], "weightValue": null, "workItem": false, "workItemFinishValue": null, "workItemFinished": false}], "kpiTypeClassify": "subtract", "kpiTypeId": "6f3704ca-97f5-4f44-84ad-c7be2feddd91", "kpiTypeName": "扣分项", "kpiTypeUsedFields": [], "kpiTypeWeight": 0.0, "lockedItems": ["typeWeight", "addIndex", "modifyIndex", "deleteIndex"], "maxExtraScore": 100.0, "notExtType": false, "openOkrScore": 0, "openRaterRule": true, "peerRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "plusSubInterval": {"max": "", "min": ""}, "raterBack": false, "reserveOkrWeight": 0.0, "scoreOptType": 2, "selfRater": {"anonymous": null, "nodeWeight": null, "open": 0, "rateMode": null}, "subRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "superRater": {"anonymous": null, "auditNodes": [{"approvalOrder": 1, "approverInfo": "1", "approverName": null, "approverType": "manager", "id": null, "modifyFlag": null, "multiType": "or", "node": "superior_score", "raters": [{"avatar": null, "dingUserId": null, "empId": "b458eec6-d7b9-4708-8605-7574c150131d", "empName": "闫晶晶", "level": 1, "roleId": null, "roleName": null, "type": 1, "weight": null}], "scoreWeight": 100.0, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": 100.0}], "multiType": "or", "nodeWeight": 100.0, "open": 1, "rateMode": "item", "superiorScoreOrder": "sameTime"}, "taskUserId": "2433247", "typeLevel": null, "typeOrder": 3, "typeRule": null, "waitScores": []}, {"alreadyScores": [], "appointRater": {"anonymous": null, "auditNodes": [], "multiType": "and", "nodeWeight": null, "open": 0, "rateMode": null}, "des": null, "finishValue": true, "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "importOkrFlag": "false", "indLevelGroup": null, "indLevelGroupId": null, "isOkr": "false", "itemLimitCnt": null, "items": [{"actionId": null, "actionName": null, "alreadyNodes": [], "appointRaters": [], "autoScoreExFlag": null, "backup": null, "deptName": null, "empId": "96633281", "endValue": null, "evaluateStartDate": null, "examineOperType": null, "fieldValueList": null, "files": null, "finalSubmit": false, "finalSubmitFinishValue": 0, "finishValue": null, "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueComment": null, "formulaCondition": "[{\"companyId\":\"8439320d-157f-49ae-bdc6-d4d7107b492a\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"704ac637-26c8-4613-a43c-f7ecf7beca97\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true,\"kpiItemId\":\"883ca9f9-09ce-4e46-8ae9-00b612aeea0a\"},{\"companyId\":\"8439320d-157f-49ae-bdc6-d4d7107b492a\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"db6931f3-57ca-44be-9cc0-89dfe8de0d8f\",\"isSystemField\":\"false\",\"label\":\"目标值\",\"value\":0,\"yVal\":true,\"kpiItemId\":\"883ca9f9-09ce-4e46-8ae9-00b612aeea0a\"}]", "formulaFields": [], "formulaType": 1, "id": "6979313", "indLevel": null, "indLevelGroup": null, "indLevelGroupId": null, "inputChangeRecord": null, "inputEmps": null, "inputFormat": "num", "inputRole": null, "isNewEmp": 0, "isOkr": "false", "isSubmit": null, "isTypeLocked": "true", "itemActualFormula": null, "itemAutoScore": null, "itemFieldJson": "[{\"companyId\":\"8439320d-157f-49ae-bdc6-d4d7107b492a\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"704ac637-26c8-4613-a43c-f7ecf7beca97\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"companyId\":\"8439320d-157f-49ae-bdc6-d4d7107b492a\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"db6931f3-57ca-44be-9cc0-89dfe8de0d8f\",\"isSystemField\":\"false\",\"label\":\"目标值\",\"value\":0,\"yVal\":true}]", "itemFinishValue": null, "itemFinishValueText": null, "itemFormula": "", "itemFullScoreCfg": "false", "itemLimitCnt": null, "itemPlanFlag": null, "itemRule": "1、管理/技术创新获地级市以上奖励；\n2、给公司带来经济效益，≥50万；\n3、给企业带来重大经济效益的（≥100万）；\n4、其余重点突出贡献，根据实际情况由各中心提报。", "itemScoreRule": null, "itemScoreValue": "{\"type\":\"autoDefine\",\"value\":{\"from\":0,\"to\":\"\",\"step\":0.5}}", "itemTags": null, "itemTargetValue": 0.0, "itemTargetValueText": null, "itemType": "non-measurable", "itemUnit": "%", "itemWeight": 100.0, "kpiItemId": "883ca9f9-09ce-4e46-8ae9-00b612aeea0a", "kpiItemName": "重点突出贡献", "kpiTypeClassify": "plus", "kpiTypeId": "7b3d19e9-3f86-469a-9ac2-411b3a0d053e", "kpiTypeName": "加分项", "kpiTypeWeight": 0.0, "kr": false, "managerInput": false, "managerLevel": "", "maxExtraScore": 30.0, "multipleReviewersType": "or", "mustResultInput": 0, "notInput": false, "notInputVal": false, "okrRefFlag": null, "okrScore": null, "okrTaskName": null, "okrType": false, "openOkrScore": 0, "order": 0, "orgId": "38449673", "plusLimit": 30.0, "plusSubInterval": {"max": "", "min": ""}, "pointsNum": null, "progress": null, "progressStatus": null, "reserveOkrWeight": 0.0, "resultInputEmpId": null, "resultInputEmpName": null, "resultInputType": "no", "reviewer": null, "score": null, "scorerObjId": [{"objItems": [{"managerLevel": null, "managerName": null, "objId": "f934918c-9ca4-450a-8791-fef6913ddce7", "objName": "林燕梅", "roleId": "9d6a8296-c607-4f64-8650-12554c2ed321", "roleName": "人事专员"}, {"managerLevel": null, "managerName": null, "objId": "87855356", "objName": "曾冬梅", "roleId": "9d6a8296-c607-4f64-8650-12554c2ed321", "roleName": "人事专员"}, {"managerLevel": null, "managerName": null, "objId": "fc2665c6-5b1c-4b85-9fce-413f21ef79ec", "objName": "苏过灵", "roleId": "9d6a8296-c607-4f64-8650-12554c2ed321", "roleName": "人事专员"}], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "scorerType": "user", "scoringRule": "1、员工加10分，部门、中心各加5分；\n2、员工、部门、中心各加10分；\n3、员工、部门、中心各加15分；\n4、标准参照管理/技术加分标准。", "selfScore": null, "showFinishBar": 1, "showTargetValue": "", "startValue": null, "subNodes": [], "subtractLimit": 0.0, "superiorScore": null, "targetId": null, "targetName": null, "taskId": "1984054", "taskUserId": "2433247", "thresholdJson": "[]", "typeOrder": 2, "urgingFlag": null, "vetoFlag": null, "waitScores": [], "weightValue": null, "workItem": false, "workItemFinishValue": null, "workItemFinished": false}], "kpiTypeClassify": "plus", "kpiTypeId": "7b3d19e9-3f86-469a-9ac2-411b3a0d053e", "kpiTypeName": "加分项", "kpiTypeUsedFields": [], "kpiTypeWeight": 0.0, "lockedItems": ["typeWeight", "addIndex", "modifyIndex", "deleteIndex"], "maxExtraScore": 30.0, "notExtType": false, "openOkrScore": 0, "openRaterRule": true, "peerRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "plusSubInterval": {"max": "", "min": ""}, "raterBack": false, "reserveOkrWeight": 0.0, "scoreOptType": 2, "selfRater": {"anonymous": null, "nodeWeight": null, "open": 0, "rateMode": null}, "subRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "superRater": {"anonymous": null, "auditNodes": [{"approvalOrder": 1, "approverInfo": "1", "approverName": null, "approverType": "manager", "id": null, "modifyFlag": null, "multiType": "or", "node": "superior_score", "raters": [{"avatar": null, "dingUserId": null, "empId": "b458eec6-d7b9-4708-8605-7574c150131d", "empName": "闫晶晶", "level": 1, "roleId": null, "roleName": null, "type": 1, "weight": null}], "scoreWeight": 100.0, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": 100.0}], "multiType": "or", "nodeWeight": 100.0, "open": 1, "rateMode": "item", "superiorScoreOrder": "sameTime"}, "taskUserId": "2433247", "typeLevel": null, "typeOrder": 2, "typeRule": null, "waitScores": []}, {"alreadyScores": [], "appointRater": {"anonymous": null, "auditNodes": [], "multiType": "and", "nodeWeight": null, "open": 0, "rateMode": null}, "des": null, "finishValue": true, "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "importOkrFlag": "false", "indLevelGroup": null, "indLevelGroupId": null, "isOkr": "false", "itemLimitCnt": null, "items": [{"actionId": null, "actionName": null, "alreadyNodes": [], "appointRaters": [], "autoScoreExFlag": null, "backup": null, "deptName": null, "empId": "96633281", "endValue": null, "evaluateStartDate": null, "examineOperType": null, "fieldValueList": null, "files": null, "finalSubmit": false, "finalSubmitFinishValue": 0, "finishValue": null, "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueComment": null, "formulaCondition": "[]", "formulaFields": [], "formulaType": 1, "id": "6979320", "indLevel": null, "indLevelGroup": null, "indLevelGroupId": null, "inputChangeRecord": null, "inputEmps": null, "inputFormat": "num", "inputRole": null, "isNewEmp": 0, "isOkr": "false", "isSubmit": null, "isTypeLocked": "true", "itemActualFormula": null, "itemAutoScore": null, "itemFieldJson": "[]", "itemFinishValue": null, "itemFinishValueText": null, "itemFormula": "", "itemFullScoreCfg": "false", "itemLimitCnt": null, "itemPlanFlag": null, "itemRule": "无", "itemScoreRule": null, "itemScoreValue": "{\"type\":\"toMainScore\"}", "itemTags": null, "itemTargetValue": 0.0, "itemTargetValueText": "", "itemType": "non-measurable", "itemUnit": "%", "itemWeight": 100.0, "kpiItemId": "105dde03-f51c-4824-8a7d-7924e8dc826b", "kpiItemName": "组织绩效得分", "kpiTypeClassify": "custom", "kpiTypeId": "d2ca217e-8395-4580-bf86-e0bdf001bd9c", "kpiTypeName": "组织绩效", "kpiTypeWeight": 20.0, "kr": false, "managerInput": false, "managerLevel": "", "maxExtraScore": 0.0, "multipleReviewersType": "or", "mustResultInput": 0, "notInput": false, "notInputVal": false, "okrRefFlag": null, "okrScore": null, "okrTaskName": null, "okrType": false, "openOkrScore": 0, "order": 0, "orgId": "38449673", "plusLimit": 0.0, "plusSubInterval": {"max": "", "min": ""}, "pointsNum": null, "progress": null, "progressStatus": null, "reserveOkrWeight": 0.0, "resultInputEmpId": "", "resultInputEmpName": null, "resultInputType": "no", "reviewer": null, "score": null, "scorerObjId": [{"objItems": [{"managerLevel": null, "managerName": null, "objId": "f934918c-9ca4-450a-8791-fef6913ddce7", "objName": "林燕梅", "roleId": "9d6a8296-c607-4f64-8650-12554c2ed321", "roleName": "人事专员"}, {"managerLevel": null, "managerName": null, "objId": "87855356", "objName": "曾冬梅", "roleId": "9d6a8296-c607-4f64-8650-12554c2ed321", "roleName": "人事专员"}, {"managerLevel": null, "managerName": null, "objId": "fc2665c6-5b1c-4b85-9fce-413f21ef79ec", "objName": "苏过灵", "roleId": "9d6a8296-c607-4f64-8650-12554c2ed321", "roleName": "人事专员"}], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "scorerType": "user", "scoringRule": "按所属中心当月组织绩效得分", "selfScore": null, "showFinishBar": 1, "showTargetValue": "", "startValue": null, "subNodes": [], "subtractLimit": 0.0, "superiorScore": null, "targetId": null, "targetName": null, "taskId": "1984054", "taskUserId": "2433247", "thresholdJson": "[]", "typeOrder": 1, "urgingFlag": null, "vetoFlag": null, "waitScores": [], "weightValue": null, "workItem": false, "workItemFinishValue": null, "workItemFinished": false}], "kpiTypeClassify": "custom", "kpiTypeId": "d2ca217e-8395-4580-bf86-e0bdf001bd9c", "kpiTypeName": "组织绩效", "kpiTypeUsedFields": [], "kpiTypeWeight": 20.0, "lockedItems": ["typeWeight", "addIndex", "modifyIndex", "deleteIndex"], "maxExtraScore": 0.0, "notExtType": true, "openOkrScore": 0, "openRaterRule": true, "peerRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "plusSubInterval": {"max": "", "min": ""}, "raterBack": false, "reserveOkrWeight": 0.0, "scoreOptType": 2, "selfRater": {"anonymous": null, "nodeWeight": null, "open": 0, "rateMode": null}, "subRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "superRater": {"anonymous": null, "auditNodes": [{"approvalOrder": 1, "approverInfo": "1", "approverName": null, "approverType": "manager", "id": null, "modifyFlag": null, "multiType": "or", "node": "superior_score", "raters": [{"avatar": null, "dingUserId": null, "empId": "b458eec6-d7b9-4708-8605-7574c150131d", "empName": "闫晶晶", "level": 1, "roleId": null, "roleName": null, "type": 1, "weight": null}], "scoreWeight": 100.0, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": 100.0}], "multiType": "or", "nodeWeight": 100.0, "open": 1, "rateMode": "item", "superiorScoreOrder": "sameTime"}, "taskUserId": "2433247", "typeLevel": null, "typeOrder": 1, "typeRule": null, "waitScores": []}], "empty": false, "groups": null, "mergedAudits": [], "modNum": 767, "oldTypeAudits": null}, "levelGroupId": "", "manualPublicOpen": true, "notAllowExceed": false, "publishResult": {"dimension": 11, "notPublic": false, "opEmps": [{"objItems": [], "objType": "taskAdmin"}], "open": 1, "toEmps": [{"dept": false, "objItems": null, "objType": "emp"}], "type": "manual"}, "publishResultOpen": true, "resultAuditingOpen": true, "ruleName": "行业产品专员绩效合约(内勤)", "s3AppointRater": {"anonymous": null, "auditNodes": [], "multiType": "and", "nodeWeight": null, "open": 0, "rateMode": null}, "s3PeerRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "s3SelfRater": {"anonymous": null, "nodeWeight": null, "open": 0, "rateMode": null}, "s3SubRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "s3SuperRater": {"anonymous": null, "auditNodes": [{"approvalOrder": 1, "approverInfo": "1", "approverName": null, "approverType": "manager", "id": null, "modifyFlag": null, "multiType": "or", "node": "superior_score", "raters": [{"avatar": null, "dingUserId": null, "empId": "b458eec6-d7b9-4708-8605-7574c150131d", "empName": "闫晶晶", "level": 1, "roleId": null, "roleName": null, "type": 1, "weight": null}], "scoreWeight": 100.0, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": 100.0}], "multiType": "or", "nodeWeight": 100.0, "open": 1, "rateMode": "item", "superiorScoreOrder": "sameTime"}, "sameTime": true, "scoreChain": null, "scoreConf": {"multiType": "or", "transferFlag": "true", "vacancyApproveInfo": null, "vacancyApproveName": null, "vacancyApproveType": null}, "scoreSortConf": {"exeType": 1, "oneByOneChain": null, "sameTime": true, "sortItems": [{"name": "自评", "sort": 1, "type": 10}, {"name": "同级互评", "sort": 2, "type": 20}, {"name": "下级互评", "sort": 3, "type": 30}, {"name": "上级评分", "sort": 4, "type": 40}, {"name": "指定评", "sort": 5, "type": 50}]}, "scoreValueConf": {"baseScore": 0.0, "customFullScore": 100, "exceedFullScore": false, "fullScoreRange": true, "scoreRangeType": "fullScore"}, "scoreView": {"appointScoreAnonymous": "false", "appointScoreViewRule": {"anonymous": null, "appoint": "", "examinee": "", "mutual": "", "superior": ""}, "mutualScoreAnonymous": "false", "mutualScoreViewRule": {"anonymous": null, "appoint": "", "examinee": "", "mutual": "", "superior": ""}, "selfScoreViewRule": {"anonymous": null, "appoint": "", "examinee": "score,attach", "mutual": "", "superior": ""}, "superiorScoreAnonymous": "false", "superiorScoreViewRule": {"anonymous": null, "appoint": "score,attach", "examinee": "", "mutual": "", "superior": "score,attach"}}, "showResultType": 6, "stageChain": null, "submitWithWeight": false, "systemFullScore": null, "taskId": "1984054", "taskName": "2023年第4季度市场中心绩效合约", "totalLevelRaters": [], "totalLevelResults": [], "totalScoreRs": null, "typeWeightConf": {"checkItemWeight": 1, "itemWeightLimit": 100, "limit100Weight": 1, "open": 1}, "typeWeightOpen": true}