{"appealConf": {"appealReceiver": null, "canAppealDay": null, "open": 0, "resultAppealNode": null}, "auditResult": {"auditNodes": [{"approvalOrder": 1, "approverInfo": "86012026", "approverName": "赵天成", "approverType": "user", "id": null, "modifyFlag": null, "multiType": "or", "node": null, "raters": [{"avatar": null, "dingUserId": null, "empId": "86012026", "empName": "赵天成", "level": null, "roleId": null, "roleName": null, "type": 0, "weight": null}], "scoreWeight": null, "status": null, "transferFlag": null, "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": null}], "commentReq": 0, "mergeConf": null, "multiType": "or", "open": 1, "transferFlag": "true", "vacancyApproveInfo": "", "vacancyApproveName": "", "vacancyApproveType": "superior"}, "baseScoreOpen": true, "commentConf": {"commentFlag": "notRequired", "commentRequiredHighValue": null, "commentRequiredValue": null, "plusOrSubComment": 0, "scoreSummarySwitch": -1}, "companyConf": {"canResSubmitInputFinish": 0, "enterOnDingMsg": 1, "enterOnScoring": 0, "evalEmpOnLogAuth": 1, "ignoreVacancyManager": 0, "indInput202308021": 0, "indLevelGroup": 0, "itemAuth": 0, "logAuthSet": null, "resultInputSendMsg": "true", "supIsCanAutoScoreItem": 0}, "confirmResult": {"auto": 0, "autoDay": null, "dimension": null, "open": 0, "sign": 0}, "confirmTask": {"auditNodes": [], "confirmLTDay": null, "modifyAuditFlag": null, "modifyItemDimension": "all", "noChangeSkipFlag": null, "open": 0, "openConfirmLT": 0}, "createTotalLevelType": 1, "customFullScore": null, "deadLineConf": {"finishInputAudit": null, "open": 0, "taskAffirmDeadLine": null, "taskConfirmDeadLine": null, "taskEvalScore": null, "taskResultAudit": null, "taskResultConfirm": null}, "editExeIndi": {"auditNodes": [], "auditOpen": null, "changeUsers": null, "open": 0}, "editStatus": 0, "empEvalId": "2400794", "enterScore": {"autoEnter": true, "enterScoreEmpType": 1, "enterScoreMethod": "auto", "scoreStartRuleDay": 5, "scoreStartRuleType": "before"}, "evaluateType": "simple", "finalScore": null, "finishValueAudit": {"auditNodes": [], "noChangeSkipFlag": null, "open": 0}, "fromOldTask": false, "indicatorCnt": 1, "initiator": "86010000", "inputNotify": null, "kpiTypes": {"datas": [{"alreadyScores": [], "appointRater": {"anonymous": null, "auditNodes": [], "multiType": "and", "nodeWeight": null, "open": 0, "rateMode": null}, "des": null, "finishValue": true, "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "importOkrFlag": null, "indLevelGroup": null, "indLevelGroupId": null, "isOkr": "false", "itemLimitCnt": null, "items": [{"actionId": null, "actionName": null, "alreadyNodes": [], "appointRaters": [], "autoScoreExFlag": null, "backup": null, "deptName": null, "empId": "86009999", "endValue": null, "evaluateStartDate": null, "examineOperType": null, "fieldValueList": null, "files": null, "finalSubmitFinishValue": 1, "finishValue": null, "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueComment": null, "formulaCondition": "[]", "formulaFields": [], "formulaType": 1, "id": "6581220", "indLevel": null, "indLevelGroup": null, "indLevelGroupId": null, "inputChangeRecord": null, "inputEmps": [{"adminType": null, "avatar": "https://static-legacy.dingtalk.com/media/lQLPM40639bPK_7NAYHNAYGwwcj3RIwOxxkEfpikDADGAA_385_385.png", "empId": "86009999", "empName": "吕连新", "exUserId": "2725562221853287", "jobnumber": "0017", "status": "on_the_job"}], "inputFormat": "text", "inputRole": null, "isNewEmp": 0, "isOkr": "false", "isSubmit": null, "isTypeLocked": null, "itemActualFormula": null, "itemAutoScore": null, "itemFieldJson": "[]", "itemFinishValue": null, "itemFinishValueText": "本周期主要完成以下工作：\n1.完成APB1.3版本研件定型发布\n2.完善SpuerlinkV2及客户反馈，支持浙江省台播出系统多个频道上线稳定运行\n3.推进eqm产品国产化工作，完成Driver代码开发，进入提测阶段；完成管理站代码开发约40%，iasp站代码开发约50%，回溯站开发约5%\n4.推进播出系统国产化开发工作。完成国产化广告站、编排站向下（Prolink）兼容开发及验证，发布中央台开始并行试用；完成国产化播出站需求宣讲及产品设计，进入ui设计；完成国产化管理站部分产品设计。\n5.推进跨平台ProDSV2客户端开发工作（将来也支持国产化），目前已经完成一轮测试，二轮提测中验证中\n6.完成云听App 6.15及6.16两个版本开发，增加部分功能及提升ui、交互体验开发，6.17湖北台需求版本，完成立项进入研发，完成两次升级上线任务。\n7.完成小欧助手app功能及用户体验提升迭代，并上线部署。\n8.完成国际台总控项目售前技术验证测试开发及演示任务\n9.完成ai能力中台语音合成引擎更换开发任务并上线\n10.推进完成云听App大部分客户部署迁移到云数机房任务\n11.推进浙江制播项目智慧广播子项目中能力中心部分的第三方能力接入（40%）及新业务界面开发（功能需要待所有能力接入完成后进入开发）\n12.推进浙广总控项目大屏优化反馈需求开发95%\n13.推进天津台索贝接口开发，索贝更换了接口（已经重新完成开发），需要重新开发及增加补充合同（增加了工时），增加部分，目前在商务阶段\n14.推进宁波融媒化生产播发平台集成项目开发，完成小程序开发任务，制播反馈支持开发任务（30%），融媒对接需求开发任务（40%），大屏定制开发任务（60%）\n15.完成田子坊项目验收支持工作，完成功能检测支持及田子坊虚拟街区小程序上线工作\n16.完成四川台8选1软件研发工作\n17.完成中央台广播与云听节目单同步服务研发工作\n18.推进博拓交付攻坚成像优化任务，接入CameraX，优化成像效果\n19.推进湖北台主线版本开发工作，完成一次迁移升级上线工作，推进6.17版本立项\n20.推进中央台版权管理软件系统开发需求（维保合同），完成度约为60%\n21.推进兰州台广播融媒体平台对接接口开发任务，完成度约为80%\n22.完成CS57路由控制站跨平台版本开发\n23.推进融媒体云技术资料更新及内部环境重建测试工作（约80%）\n24.代码安全技术攻关工作推进，目前技术路线基本定型，计划24年1季度推进各线安全加固改进\n25.推进各售前、售后支撑工作，包括但不限于：零跑汽车需求售前支撑、青岛台行风迁移售前、天津台售前支持、番禺台音乐播出反馈修改、中央台反馈及修改、安徽售前客户支持、四川售前支持、四川发射监测售前方案支持、 江西台VR演练售前支持等等\n\n上述工作，部门基本按计划推进，未收到客户负向反馈。", "itemFormula": "", "itemFullScoreCfg": "false", "itemLimitCnt": null, "itemPlanFlag": null, "itemRule": "手工录入", "itemScoreRule": {"appointRater": {"anonymous": null, "auditNodes": [], "multiType": "and", "nodeWeight": null, "open": 0, "rateMode": null}, "appointScoreFlag": "false", "appointScoreWeight": null, "id": "4127463", "kpiItemId": "c8f593db-6a5e-4c2c-9129-e566786f80dc", "kpiTypeId": "6581219", "mutualScoreAnonymous": null, "mutualScoreAttendRule": null, "mutualScoreFlag": "true", "mutualScoreVacancy": null, "mutualScoreViewRule": null, "mutualUserType": null, "mutualUserValue": null, "openRaterRule": true, "peerRater": {"anonymous": null, "appointer": {"raters": [], "type": "emp"}, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": "and", "node": "peer_score", "nodeWeight": 0, "open": 1, "rateMode": "item", "raters": [], "roleAudit": false, "transferFlag": "true", "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "peerScoreFlag": null, "peerScoreWeight": 0, "peerUserName": null, "peerUserType": null, "peerUserValue": null, "selfRater": {"anonymous": "false", "nodeWeight": 0, "open": 1, "rateMode": "item"}, "selfScoreFlag": "true", "selfScoreViewRule": null, "selfScoreWeight": 0, "subRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "subScoreFlag": null, "subScoreWeight": null, "subUserName": null, "subUserType": null, "subUserValue": null, "superRater": {"anonymous": null, "auditNodes": [{"approvalOrder": 1, "approverInfo": null, "approverName": null, "approverType": "user", "id": null, "modifyFlag": null, "multiType": "or", "node": "superior_score", "raters": [{"avatar": null, "dingUserId": null, "empId": "86012022", "empName": "唐卫平", "level": null, "roleId": null, "roleName": null, "type": 0, "weight": null}], "scoreWeight": 100, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": 100}], "multiType": "or", "nodeWeight": 100, "open": 1, "rateMode": "item", "superiorScoreOrder": "sameTime"}, "superiorScoreFlag": "true", "superiorScoreVacancy": null, "superiorScoreViewRule": null, "superiorScoreWeight": 100, "taskId": "2007525", "taskUserId": "2400794"}, "itemScoreValue": "{\"type\":\"toMainScore\"}", "itemTags": null, "itemTargetValue": 0.0, "itemTargetValueText": "", "itemType": "non-measurable", "itemUnit": "%", "itemWeight": 100.0, "kpiItemId": "c8f593db-6a5e-4c2c-9129-e566786f80dc", "kpiItemName": "月度绩效考核", "kpiTypeClassify": null, "kpiTypeId": "6581219", "kpiTypeName": "默认考核维度", "kpiTypeWeight": 0.0, "kr": false, "managerInput": false, "managerLevel": "", "maxExtraScore": 0.0, "multipleReviewersType": "or", "mustResultInput": 0, "notInput": false, "okrRefFlag": null, "okrScore": null, "okrTaskName": null, "okrType": false, "openOkrScore": 0, "order": 0, "orgId": null, "plusLimit": null, "plusSubInterval": {"max": "", "min": ""}, "pointsNum": null, "progress": null, "progressStatus": null, "reserveOkrWeight": 100.0, "resultInputEmpId": "86009999", "resultInputEmpName": null, "resultInputType": "exam", "reviewer": null, "score": null, "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "scorerType": "exam", "scoringRule": "1、业务以积分为准进行计算\n2、职能以智能考核尺度进行评分\n需详细描述得分细则情况", "selfScore": null, "showFinishBar": 1, "showTargetValue": "", "startValue": null, "subNodes": [], "subtractLimit": null, "superiorScore": null, "targetId": null, "targetName": null, "taskId": "2007525", "taskUserId": "2400794", "thresholdJson": "[]", "typeOrder": 0, "urgingFlag": null, "vetoFlag": null, "waitScores": [{"active": true, "approvalOrder": 1, "auditStatus": "pass", "calibrationType": null, "empId": "86009999", "empScore": 100.0, "finalPlusScore": null, "finalScore": 100.0, "finalSubtractScore": null, "finalWeightPlusScore": null, "finalWeightScore": 0.0, "finalWeightSubtractScore": null, "id": "6d5bde16-2d10-417f-b57c-bcb10b0fd089", "indexCalibration": null, "isCommited": null, "kpiItemId": "c8f593db-6a5e-4c2c-9129-e566786f80dc", "kpiTypeId": "6581219", "modifyFlag": null, "noItemScore": 0.0, "notDeleted": true, "operateReason": null, "orgId": null, "perfCoefficient": null, "plusScore": null, "reviewersType": "or", "score": 100.0, "scoreAttUrl": null, "scoreComment": "", "scoreLevel": "", "scoreWeight": 100.0, "scorerId": "86009999", "scorerType": "self_score", "subtractScore": null, "supper": false, "taskAuditId": null, "taskId": "2007525", "taskUserId": "2400794", "transferId": null, "version": 0, "vetoFlag": null}, {"active": true, "approvalOrder": 1, "auditStatus": "pass", "calibrationType": null, "empId": "86009999", "empScore": 100.0, "finalPlusScore": null, "finalScore": 100.0, "finalSubtractScore": null, "finalWeightPlusScore": null, "finalWeightScore": 100.0, "finalWeightSubtractScore": null, "id": "ffeb932e-aa5a-4071-a80a-03e5af3172be", "indexCalibration": null, "isCommited": null, "kpiItemId": "c8f593db-6a5e-4c2c-9129-e566786f80dc", "kpiTypeId": "6581219", "modifyFlag": null, "noItemScore": 100.0, "notDeleted": true, "operateReason": null, "orgId": null, "perfCoefficient": null, "plusScore": null, "reviewersType": "or", "score": 100.0, "scoreAttUrl": null, "scoreComment": "项目配合支持积极，在建项目配合正常，国产化业务系统的研发和推进希望能够在原有计划基础上加快，后续此事会比较重要，全国推进国产化转型工作会加速。", "scoreLevel": "", "scoreWeight": 100.0, "scorerId": "86012022", "scorerType": "superior_score", "subtractScore": null, "supper": true, "taskAuditId": null, "taskId": "2007525", "taskUserId": "2400794", "transferId": null, "version": 0, "vetoFlag": null}], "weightValue": null, "workItem": false, "workItemFinishValue": null, "workItemFinished": false}], "kpiTypeClassify": null, "kpiTypeId": "6581219", "kpiTypeName": "默认考核维度", "kpiTypeUsedFields": [{"adminType": 1, "companyId": "", "createdTime": "2023-12-26 08:41:01", "createdUser": null, "fieldId": "name", "isDeleted": "false", "kpiTypeId": "6581219", "name": "指标名称", "req": 1, "show": 1, "sort": null, "status": "valid", "type": 1, "updatedTime": "2023-10-23 15:22:40", "updatedUser": null}, {"adminType": 1, "companyId": "", "createdTime": "2023-12-26 08:41:01", "createdUser": null, "fieldId": "standard", "isDeleted": "false", "kpiTypeId": "6581219", "name": "考核标准", "req": 0, "show": 1, "sort": null, "status": "valid", "type": 1, "updatedTime": "2023-10-23 15:22:40", "updatedUser": null}, {"adminType": 1, "companyId": "", "createdTime": "2023-12-26 08:41:01", "createdUser": null, "fieldId": "scoreRule", "isDeleted": "false", "kpiTypeId": "6581219", "name": "计分规则", "req": 0, "show": 1, "sort": null, "status": "valid", "type": 1, "updatedTime": "2023-10-23 15:22:40", "updatedUser": null}, {"adminType": 1, "companyId": "", "createdTime": "2023-12-26 08:41:01", "createdUser": null, "fieldId": "targetValue", "isDeleted": "false", "kpiTypeId": "6581219", "name": "目标值", "req": 0, "show": 1, "sort": null, "status": "valid", "type": 1, "updatedTime": "2023-10-23 15:22:40", "updatedUser": null}, {"adminType": 1, "companyId": "", "createdTime": "2023-12-26 08:41:01", "createdUser": null, "fieldId": "unit", "isDeleted": "false", "kpiTypeId": "6581219", "name": "单位", "req": 0, "show": 1, "sort": null, "status": "valid", "type": 1, "updatedTime": "2023-10-23 15:22:40", "updatedUser": null}, {"adminType": 1, "companyId": "", "createdTime": "2023-12-26 08:41:01", "createdUser": null, "fieldId": "indexWeight", "isDeleted": "false", "kpiTypeId": "6581219", "name": "指标权重", "req": 1, "show": 1, "sort": null, "status": "valid", "type": 1, "updatedTime": "2023-10-23 15:22:40", "updatedUser": null}, {"adminType": 1, "companyId": "", "createdTime": "2023-12-26 08:41:01", "createdUser": null, "fieldId": "scoreValue", "isDeleted": "false", "kpiTypeId": "6581219", "name": "评分分值", "req": 1, "show": 1, "sort": null, "status": "valid", "type": 1, "updatedTime": "2023-10-23 15:22:40", "updatedUser": null}, {"adminType": 1, "companyId": "", "createdTime": "2023-12-26 08:41:01", "createdUser": null, "fieldId": "finishValue", "isDeleted": "false", "kpiTypeId": "6581219", "name": "完成值录入", "req": 0, "show": 1, "sort": null, "status": "valid", "type": 1, "updatedTime": "2023-10-23 15:22:40", "updatedUser": null}], "kpiTypeWeight": 0.0, "lockedItems": null, "maxExtraScore": 0.0, "notExtType": true, "openOkrScore": 0, "openRaterRule": true, "peerRater": {"anonymous": null, "appointer": {"raters": [], "type": "emp"}, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": "and", "node": "peer_score", "nodeWeight": 0, "open": 1, "rateMode": "item", "raters": [{"avatar": null, "dingUserId": null, "empId": "86012283", "empName": "余海", "level": null, "roleId": null, "roleName": null, "type": 0, "weight": 33.34}, {"avatar": null, "dingUserId": null, "empId": "86012249", "empName": "张得军", "level": null, "roleId": null, "roleName": null, "type": 0, "weight": 33.33}, {"avatar": null, "dingUserId": null, "empId": "86010000", "empName": "王世为", "level": null, "roleId": null, "roleName": null, "type": 0, "weight": 33.33}], "roleAudit": false, "transferFlag": "true", "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "plusSubInterval": {"max": "", "min": ""}, "raterBack": false, "reserveOkrWeight": 100.0, "scoreOptType": 2, "selfRater": {"anonymous": "false", "nodeWeight": 0, "open": 1, "rateMode": "item"}, "subRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "superRater": {"anonymous": null, "auditNodes": [{"approvalOrder": 1, "approverInfo": null, "approverName": null, "approverType": "user", "id": null, "modifyFlag": null, "multiType": "or", "node": "superior_score", "raters": [{"avatar": null, "dingUserId": null, "empId": "86012022", "empName": "唐卫平", "level": null, "roleId": null, "roleName": null, "type": 0, "weight": null}], "scoreWeight": 100, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": 100}], "multiType": "or", "nodeWeight": 100, "open": 1, "rateMode": "item", "superiorScoreOrder": "sameTime"}, "taskUserId": "2400794", "typeLevel": null, "typeOrder": 0, "typeRule": null, "waitScores": []}], "empty": false, "groups": null, "mergedAudits": [], "oldTypeAudits": null}, "levelGroupId": "", "manualPublicOpen": false, "notAllowExceed": false, "publishResult": {"dimension": 11, "notPublic": false, "opEmps": [], "open": 1, "toEmps": [{"dept": false, "objItems": null, "objType": "emp"}, {"dept": false, "objItems": null, "objType": "scoreEmp"}], "type": "afterFinished"}, "publishResultOpen": true, "resultAuditingOpen": true, "ruleName": "产研中心极简考核模版-含互评", "s3AppointRater": {"anonymous": null, "auditNodes": [], "multiType": "and", "nodeWeight": null, "open": 0, "rateMode": null}, "s3PeerRater": {"anonymous": null, "appointer": {"raters": [], "type": "emp"}, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": "and", "node": "peer_score", "nodeWeight": 0, "open": 1, "rateMode": "item", "raters": [{"avatar": null, "dingUserId": null, "empId": "86012283", "empName": "余海", "level": null, "roleId": null, "roleName": null, "type": 0, "weight": 33.34}, {"avatar": null, "dingUserId": null, "empId": "86012249", "empName": "张得军", "level": null, "roleId": null, "roleName": null, "type": 0, "weight": 33.33}, {"avatar": null, "dingUserId": null, "empId": "86010000", "empName": "王世为", "level": null, "roleId": null, "roleName": null, "type": 0, "weight": 33.33}], "roleAudit": false, "transferFlag": "true", "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "s3SelfRater": {"anonymous": "false", "nodeWeight": 0, "open": 1, "rateMode": "item"}, "s3SubRater": {"anonymous": null, "appointer": null, "approvalOrder": null, "approverInfo": null, "approverType": null, "modifyFlag": null, "multiType": null, "node": null, "nodeWeight": null, "open": 0, "rateMode": null, "raters": [], "roleAudit": false, "transferFlag": null, "vacancyApproveName": null, "vacancyRater": null, "vacancyType": null}, "s3SuperRater": {"anonymous": null, "auditNodes": [{"approvalOrder": 1, "approverInfo": null, "approverName": null, "approverType": "user", "id": null, "modifyFlag": null, "multiType": "or", "node": "superior_score", "raters": [{"avatar": null, "dingUserId": null, "empId": "86012022", "empName": "唐卫平", "level": null, "roleId": null, "roleName": null, "type": 0, "weight": null}], "scoreWeight": 100, "status": null, "transferFlag": "true", "vacancyApproverInfo": null, "vacancyApproverType": null, "weight": 100}], "multiType": "or", "nodeWeight": 100, "open": 1, "rateMode": "item", "superiorScoreOrder": "sameTime"}, "sameTime": false, "scoreChain": {"chain": [{"scoreNodes": [{"appointNode": false, "nextSuper": null, "node": "AUTO", "order": 1}], "totalLevel": false, "type": 0}, {"scoreNodes": [{"appointNode": false, "nextSuper": null, "node": "SELF_SCORE", "order": 1}, {"appointNode": false, "nextSuper": null, "node": "ITEM_SCORE", "order": 1}], "totalLevel": false, "type": 0}, {"scoreNodes": [{"appointNode": false, "nextSuper": null, "node": "PEER_SCORE", "order": 1}], "totalLevel": false, "type": 0}, {"scoreNodes": [{"appointNode": false, "nextSuper": null, "node": "SUPERIOR_SCORE", "order": 1}], "totalLevel": false, "type": 0}]}, "scoreConf": {"multiType": "or", "transferFlag": "true", "vacancyApproveInfo": null, "vacancyApproveName": null, "vacancyApproveType": null}, "scoreSortConf": {"exeType": 0, "oneByOneChain": null, "sameTime": false, "sortItems": [{"name": "自评", "sort": 1, "type": 10}, {"name": "同级互评", "sort": 2, "type": 20}, {"name": "下级互评", "sort": 3, "type": 30}, {"name": "上级评分", "sort": 4, "type": 40}, {"name": "指定评", "sort": 5, "type": 50}]}, "scoreValueConf": {"baseScore": 0, "customFullScore": 140, "exceedFullScore": false, "fullScoreRange": true, "scoreRangeType": "fullScore"}, "scoreView": {"appointScoreAnonymous": "true", "appointScoreViewRule": {"anonymous": null, "appoint": "score,attach", "examinee": "score,attach", "mutual": "score,attach", "superior": "score,attach"}, "mutualScoreAnonymous": "true", "mutualScoreViewRule": {"anonymous": null, "appoint": "", "examinee": "", "mutual": "", "superior": ""}, "selfScoreViewRule": {"anonymous": null, "appoint": "", "examinee": "score,attach", "mutual": "", "superior": ""}, "superiorScoreAnonymous": "true", "superiorScoreViewRule": {"anonymous": null, "appoint": "score,attach", "examinee": "score,attach", "mutual": "score,attach", "superior": "score,attach"}}, "showResultType": 6, "stageChain": null, "submitWithWeight": false, "systemFullScore": null, "taskId": "2007525", "taskName": "产研中心2023年12月业务考核周期PO及研究院PM考核", "totalLevelRaters": [], "totalLevelResults": [], "totalScoreRs": null, "typeWeightConf": {"checkItemWeight": 1, "itemWeightLimit": 100, "limit100Weight": 0, "open": 0}, "typeWeightOpen": false}