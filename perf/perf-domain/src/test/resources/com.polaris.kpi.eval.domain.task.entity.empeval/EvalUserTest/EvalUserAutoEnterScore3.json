{"empId": "12099075", "allScored": "true", "cycleId": "2178129", "refAsk360Flag": false, "changedAuditScene": [], "changeScene": 0, "orgId": "48733284", "allAutoCompute": false, "isAllAutoType": true, "atOrgCodePath": "|a1ea622e-5de2-4603-b436-17a22bd76296|52322689|48733284|", "isDeleted": "false", "tempTask": 0, "confirmDeadLine": "2025-03-06", "empName": "周颖", "isVacancySkipRater": false, "createdTime": 1741175500000, "hasChangeOrg": false, "id": "3388601", "sendMsg": true, "taskStatus": "finishValueAudit", "createdUser": "12099075", "updatedTime": 1741175530000, "finalScore": 50, "empOrgName": "一指阁学院", "atOrgNamePath": "一指阁|产品培训部|一指阁学院", "finalItemAutoScore": 50, "avatar": "https://static-legacy.dingtalk.com/media/lADPBbCc1e6vl57NBGXNBGU_1125_1125.jpg", "originalFinalScore": 50, "reviewersJson": [{"empId": "12099076", "avatar": "https://static-legacy.dingtalk.com/media/lALPDetfSCFRvXTNAyDNAyA_800_800.png", "exUserId": "manager6916", "jobnumber": "00001", "empName": "赵玉龙", "status": "on_the_job"}], "version": 7, "finalPlusScore": 0, "hasAskEndScore": false, "mutualEvalComputeType": 1, "companyId": {"id": "53557948-3376-49ab-a7e2-0862e4495995"}, "resetRaterNameIds": [], "ruleConfStatus": 200, "inputFinishStatus": 2, "atOrgPathHight": 3, "scoreEndFlag": false, "isNewEmp": 0, "opEmpExistsItemNotSubmitFinishValue": false, "finalSubtractScore": 0, "kpis": [{"alreadyNodes": [], "itemRule": "每月该人员名下的销售额", "reserveOkrWeight": 0, "showFinishBar": 1, "itemFinishValue": 5, "itemUnit": "元", "multipleReviewersType": "or", "taskUserId": "3388601", "itemScoreValue": "{\"type\":\"toMainScore\"}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "", "formulaType": 1, "createdTime": 1741175522000, "id": "20407818", "createdUser": "12099075", "scorerType": "auto", "order": 0, "itemFormula": " 完成值  /  目标值  * 100", "updatedTime": 1741175522000, "thresholdJson": "[{\"inputLabelArr\":[{\"companyId\":\"53557948-3376-49ab-a7e2-0862e4495995\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"112bb25c-74f9-4dfd-979e-bf4ddfe00bc7\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true,\"unit\":\"元\"},{\"label\":\"÷\",\"value\":\"/\",\"symbol\":true},{\"companyId\":\"53557948-3376-49ab-a7e2-0862e4495995\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"c5052522-1a82-487b-8378-79eaa1d86bf3\",\"isSystemField\":\"false\",\"label\":\"目标值\",\"value\":0,\"yVal\":true,\"unit\":\"元\"},{\"label\":\"×\",\"value\":\"*\",\"symbol\":true},{\"label\":1,\"value\":1},{\"label\":0,\"value\":0},{\"label\":0,\"value\":0}],\"cursorPosition\":7,\"conditionRelation\":\"||\",\"keyboardShow\":false,\"errMsg\":\"\",\"filterList\":[{\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":0}]}]", "typeOrder": 0, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "num", "version": 2, "companyId": {"id": "53557948-3376-49ab-a7e2-0862e4495995"}, "resultInputType": "user", "itemFullScoreCfg": "true", "mustResultInput": 1, "itemTargetValue": 10, "itemFieldJson": "[]", "formulaCondition": "[]", "empId": "12099075", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueAuditStatus": 0, "managerLevel": "", "kpiItemName": "门店销售额", "itemAutoScore": 20, "otherReqField": {"inputFinishValue": {"isReq": 1, "desc": "完成值必填"}, "attachment": {"isReq": 0, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 100, "itemTargetValueText": "", "inputRole": [], "itemWeight": 40, "scoringRule": "完成值➗目标值*权重*100", "kpiItemId": "fd3c16f8-8b3e-4b34-892d-b488ed18dbb6", "maxExtraScore": 0, "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "12099075", "kpiTypeName": "业绩与贡献", "kpiTypeId": "8df59c4d-ca98-45a4-8128-e4d3fcf7c28c", "isNewEmp": 0, "fieldValueList": [], "taskId": "2154632"}, {"alreadyNodes": [], "itemRule": "当月回客数*8%，有过购卡或消费记录并购买任意卡项", "reserveOkrWeight": 0, "showFinishBar": 1, "itemFinishValue": 5, "itemUnit": "个", "multipleReviewersType": "or", "taskUserId": "3388601", "itemScoreValue": "{\"type\":\"toMainScore\"}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "", "formulaType": 1, "finishValueType": 1, "createdTime": 1741175522000, "id": "20407819", "createdUser": "12099075", "scorerType": "auto", "order": 1, "itemFormula": " 完成值  /  目标值  * 100", "updatedTime": 1741175522000, "thresholdJson": "[{\"inputLabelArr\":[{\"companyId\":\"53557948-3376-49ab-a7e2-0862e4495995\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"112bb25c-74f9-4dfd-979e-bf4ddfe00bc7\",\"isSystemField\":\"false\",\"fieldId\":\"finishValue\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"label\":\"÷\",\"value\":\"/\",\"symbol\":true},{\"companyId\":\"53557948-3376-49ab-a7e2-0862e4495995\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"c5052522-1a82-487b-8378-79eaa1d86bf3\",\"isSystemField\":\"false\",\"fieldId\":\"targetValue\",\"label\":\"目标值\",\"value\":0,\"yVal\":true},{\"label\":\"×\",\"value\":\"*\",\"symbol\":true},{\"label\":1,\"value\":1},{\"label\":0,\"value\":0},{\"label\":0,\"value\":0}],\"cursorPosition\":7,\"conditionRelation\":\"||\",\"keyboardShow\":false,\"errMsg\":\"\",\"filterList\":[{\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":0}]}]", "typeOrder": 0, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "num", "version": 2, "companyId": {"id": "53557948-3376-49ab-a7e2-0862e4495995"}, "resultInputType": "user", "itemFullScoreCfg": "true", "mustResultInput": 1, "itemTargetValue": 10, "itemFieldJson": "[{\"companyId\":\"53557948-3376-49ab-a7e2-0862e4495995\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"112bb25c-74f9-4dfd-979e-bf4ddfe00bc7\",\"isSystemField\":\"false\",\"fieldId\":\"finishValue\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"companyId\":\"53557948-3376-49ab-a7e2-0862e4495995\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"c5052522-1a82-487b-8378-79eaa1d86bf3\",\"isSystemField\":\"false\",\"fieldId\":\"targetValue\",\"label\":\"目标值\",\"value\":0,\"yVal\":true}]", "formulaCondition": "[{\"companyFieldId\":\"112bb25c-74f9-4dfd-979e-bf4ddfe00bc7\",\"createdTime\":\"2024-12-06 18:44:54\",\"formulaFieldName\":\"完成值\",\"formulaFieldValue\":0,\"id\":\"17636103\",\"kpiItemId\":\"ce0e1317-fb04-4c80-bb52-4eef55ee9dd7\",\"taskId\":\"2154632\",\"taskUserId\":\"3388601\"},{\"companyFieldId\":\"c5052522-1a82-487b-8378-79eaa1d86bf3\",\"createdTime\":\"2024-12-06 18:44:54\",\"formulaFieldName\":\"目标值\",\"formulaFieldValue\":0,\"id\":\"17636104\",\"kpiItemId\":\"ce0e1317-fb04-4c80-bb52-4eef55ee9dd7\",\"taskId\":\"2154632\",\"taskUserId\":\"3388601\"}]", "empId": "12099075", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueAuditStatus": 0, "managerLevel": "", "kpiItemName": "续卡数", "itemAutoScore": 12.5, "otherReqField": {"inputFinishValue": {"isReq": 1, "desc": "完成值必填"}, "attachment": {"isReq": 0, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 100, "itemTargetValueText": "", "inputRole": [], "itemWeight": 25, "scoringRule": "完成值➗目标值*权重*100", "kpiItemId": "ce0e1317-fb04-4c80-bb52-4eef55ee9dd7", "maxExtraScore": 0, "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "12099075", "kpiTypeName": "业绩与贡献", "kpiTypeId": "8df59c4d-ca98-45a4-8128-e4d3fcf7c28c", "isNewEmp": 0, "fieldValueList": [], "taskId": "2154632"}, {"alreadyNodes": [], "itemRule": "新客成交数/新客数，（新客数：未有任何消费记录、成交数：当天有交易订单或无订单并购买任意卡项）", "reserveOkrWeight": 0, "showFinishBar": 1, "itemFinishValue": 5, "itemUnit": "%", "multipleReviewersType": "or", "taskUserId": "3388601", "itemScoreValue": "{\"type\":\"toMainScore\"}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "", "formulaType": 1, "createdTime": 1741175522000, "id": "20407820", "createdUser": "12099075", "scorerType": "auto", "order": 2, "itemFormula": " 完成值  /  目标值  * 100", "updatedTime": 1741175522000, "thresholdJson": "[{\"inputLabelArr\":[{\"companyId\":\"53557948-3376-49ab-a7e2-0862e4495995\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"112bb25c-74f9-4dfd-979e-bf4ddfe00bc7\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true,\"unit\":\"元\"},{\"label\":\"÷\",\"value\":\"/\",\"symbol\":true},{\"companyId\":\"53557948-3376-49ab-a7e2-0862e4495995\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"c5052522-1a82-487b-8378-79eaa1d86bf3\",\"isSystemField\":\"false\",\"label\":\"目标值\",\"value\":0,\"yVal\":true,\"unit\":\"元\"},{\"label\":\"×\",\"value\":\"*\",\"symbol\":true},{\"label\":1,\"value\":1},{\"label\":0,\"value\":0},{\"label\":0,\"value\":0}],\"cursorPosition\":7,\"conditionRelation\":\"||\",\"keyboardShow\":false,\"errMsg\":\"\",\"filterList\":[{\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":0}]}]", "typeOrder": 0, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "num", "version": 2, "companyId": {"id": "53557948-3376-49ab-a7e2-0862e4495995"}, "resultInputType": "user", "itemFullScoreCfg": "true", "mustResultInput": 1, "itemTargetValue": 10, "itemFieldJson": "[]", "formulaCondition": "[]", "empId": "12099075", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueAuditStatus": 0, "managerLevel": "", "kpiItemName": "新客成交率", "itemAutoScore": 17.5, "otherReqField": {"inputFinishValue": {"isReq": 1, "desc": "完成值必填"}, "attachment": {"isReq": 0, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 100, "itemTargetValueText": "", "inputRole": [], "itemWeight": 35, "scoringRule": "完成值➗目标值*权重*100", "kpiItemId": "aaf0277c-b98a-43f5-83a6-d5f9327e69c1", "maxExtraScore": 0, "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "12099075", "kpiTypeName": "业绩与贡献", "kpiTypeId": "8df59c4d-ca98-45a4-8128-e4d3fcf7c28c", "isNewEmp": 0, "fieldValueList": [], "taskId": "2154632"}, {"alreadyNodes": [], "itemRule": "提供付费健身机构的消费记录截图", "reserveOkrWeight": 0, "showFinishBar": 1, "itemUnit": "次", "multipleReviewersType": "or", "plusLimit": 3, "taskUserId": "3388601", "itemScoreValue": "{\"type\":\"toMainScore\"}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "", "formulaType": 1, "finishValueType": 1, "createdTime": 1741175522000, "id": "20407821", "createdUser": "12099075", "scorerType": "user", "order": 0, "itemFormula": "", "updatedTime": 1741175565000, "thresholdJson": "[]", "typeOrder": 1, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "num", "version": 1, "companyId": {"id": "53557948-3376-49ab-a7e2-0862e4495995"}, "resultInputType": "user", "itemFullScoreCfg": "false", "mustResultInput": 0, "itemTargetValue": 0, "itemFieldJson": "[{\"companyId\":\"53557948-3376-49ab-a7e2-0862e4495995\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"112bb25c-74f9-4dfd-979e-bf4ddfe00bc7\",\"isSystemField\":\"false\",\"fieldId\":\"finishValue\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"companyId\":\"53557948-3376-49ab-a7e2-0862e4495995\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"c5052522-1a82-487b-8378-79eaa1d86bf3\",\"isSystemField\":\"false\",\"fieldId\":\"targetValue\",\"label\":\"目标值\",\"value\":0,\"yVal\":true}]", "kpiTypeClassify": "plus", "formulaCondition": "[{\"companyId\":\"53557948-3376-49ab-a7e2-0862e4495995\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"112bb25c-74f9-4dfd-979e-bf4ddfe00bc7\",\"isSystemField\":\"false\",\"fieldId\":\"finishValue\",\"label\":\"完成值\",\"value\":0,\"yVal\":true,\"kpiItemId\":\"90387201-6b83-4a3c-9849-9a9fffbb1bf9\"},{\"companyId\":\"53557948-3376-49ab-a7e2-0862e4495995\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"c5052522-1a82-487b-8378-79eaa1d86bf3\",\"isSystemField\":\"false\",\"fieldId\":\"targetValue\",\"label\":\"目标值\",\"value\":0,\"yVal\":true,\"kpiItemId\":\"90387201-6b83-4a3c-9849-9a9fffbb1bf9\"}]", "empId": "12099075", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [{"objName": "周颖", "objId": "12099075"}], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueAuditStatus": 0, "managerLevel": "", "kpiItemName": "运动体验", "otherReqField": {"inputFinishValue": {"isReq": 0, "desc": "完成值必填"}, "attachment": {"isReq": 0, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "itemTargetValueText": "", "inputRole": [], "itemWeight": 0, "scoringRule": "健身一次加分1分，考核周期内上限3分", "kpiItemId": "90387201-6b83-4a3c-9849-9a9fffbb1bf9", "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "12099075", "kpiTypeName": "加分项", "kpiTypeId": "62adf091-1da7-4fa1-baf2-72fa20ba56a5", "isNewEmp": 0, "fieldValueList": [], "taskId": "2154632"}, {"alreadyNodes": [], "itemRule": "提供60min及以上时间推拿的消费记录截图", "reserveOkrWeight": 0, "showFinishBar": 1, "itemUnit": "次", "multipleReviewersType": "or", "plusLimit": 2, "taskUserId": "3388601", "itemScoreValue": "{\"type\":\"toMainScore\"}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "", "formulaType": 1, "finishValueType": 1, "createdTime": 1741175522000, "id": "20407822", "createdUser": "12099075", "scorerType": "exam", "order": 1, "itemFormula": "", "updatedTime": 1741175565000, "thresholdJson": "[]", "typeOrder": 1, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "num", "version": 1, "companyId": {"id": "53557948-3376-49ab-a7e2-0862e4495995"}, "resultInputType": "user", "itemFullScoreCfg": "false", "mustResultInput": 0, "itemTargetValue": 0, "itemFieldJson": "[{\"companyId\":\"53557948-3376-49ab-a7e2-0862e4495995\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"112bb25c-74f9-4dfd-979e-bf4ddfe00bc7\",\"isSystemField\":\"false\",\"fieldId\":\"finishValue\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"companyId\":\"53557948-3376-49ab-a7e2-0862e4495995\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"c5052522-1a82-487b-8378-79eaa1d86bf3\",\"isSystemField\":\"false\",\"fieldId\":\"targetValue\",\"label\":\"目标值\",\"value\":0,\"yVal\":true}]", "kpiTypeClassify": "plus", "formulaCondition": "[{\"companyId\":\"53557948-3376-49ab-a7e2-0862e4495995\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"112bb25c-74f9-4dfd-979e-bf4ddfe00bc7\",\"isSystemField\":\"false\",\"fieldId\":\"finishValue\",\"label\":\"完成值\",\"value\":0,\"yVal\":true,\"kpiItemId\":\"e7db45d4-e93c-4c7b-addf-2e6f7434f67c\"},{\"companyId\":\"53557948-3376-49ab-a7e2-0862e4495995\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"c5052522-1a82-487b-8378-79eaa1d86bf3\",\"isSystemField\":\"false\",\"fieldId\":\"targetValue\",\"label\":\"目标值\",\"value\":0,\"yVal\":true,\"kpiItemId\":\"e7db45d4-e93c-4c7b-addf-2e6f7434f67c\"}]", "empId": "12099075", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueAuditStatus": 0, "managerLevel": "", "kpiItemName": "推拿交流", "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "itemTargetValueText": "", "inputRole": [], "itemWeight": 0, "scoringRule": "推拿一次加分0.5分，考核周期内上限2分", "kpiItemId": "e7db45d4-e93c-4c7b-addf-2e6f7434f67c", "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "12099075", "kpiTypeName": "加分项", "kpiTypeId": "62adf091-1da7-4fa1-baf2-72fa20ba56a5", "isNewEmp": 0, "fieldValueList": [], "taskId": "2154632"}, {"alreadyNodes": [], "itemRule": "客户对其0投诉", "reserveOkrWeight": 0, "showFinishBar": 0, "itemUnit": "%", "multipleReviewersType": "or", "taskUserId": "3388601", "itemScoreValue": "{\"type\":\"toMainScore\"}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "", "formulaType": 1, "finishValueType": 1, "createdTime": 1741175522000, "id": "20407823", "createdUser": "12099075", "scorerType": "user", "order": 0, "itemFormula": "", "updatedTime": 1741175565000, "thresholdJson": "[]", "typeOrder": 2, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "num", "version": 1, "companyId": {"id": "53557948-3376-49ab-a7e2-0862e4495995"}, "resultInputType": "user", "itemFullScoreCfg": "false", "mustResultInput": 0, "itemTargetValue": 0, "itemFieldJson": "[{\"companyId\":\"53557948-3376-49ab-a7e2-0862e4495995\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"112bb25c-74f9-4dfd-979e-bf4ddfe00bc7\",\"isSystemField\":\"false\",\"fieldId\":\"finishValue\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"companyId\":\"53557948-3376-49ab-a7e2-0862e4495995\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"c5052522-1a82-487b-8378-79eaa1d86bf3\",\"isSystemField\":\"false\",\"fieldId\":\"targetValue\",\"label\":\"目标值\",\"value\":0,\"yVal\":true}]", "kpiTypeClassify": "subtract", "formulaCondition": "[{\"companyId\":\"53557948-3376-49ab-a7e2-0862e4495995\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"112bb25c-74f9-4dfd-979e-bf4ddfe00bc7\",\"isSystemField\":\"false\",\"fieldId\":\"finishValue\",\"label\":\"完成值\",\"value\":0,\"yVal\":true,\"kpiItemId\":\"c6859c9f-cb63-42ea-9d8d-5c0db84e0a26\"},{\"companyId\":\"53557948-3376-49ab-a7e2-0862e4495995\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"c5052522-1a82-487b-8378-79eaa1d86bf3\",\"isSystemField\":\"false\",\"fieldId\":\"targetValue\",\"label\":\"目标值\",\"value\":0,\"yVal\":true,\"kpiItemId\":\"c6859c9f-cb63-42ea-9d8d-5c0db84e0a26\"}]", "empId": "12099075", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [{"objName": "周颖", "objId": "12099075"}], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueAuditStatus": 0, "managerLevel": "", "kpiItemName": "客户满意度", "otherReqField": {"inputFinishValue": {"isReq": 0, "desc": "完成值必填"}, "attachment": {"isReq": 0, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "itemTargetValueText": "", "inputRole": [], "itemWeight": 0, "scoringRule": "0投诉：一条投诉扣除5分", "kpiItemId": "c6859c9f-cb63-42ea-9d8d-5c0db84e0a26", "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "12099075", "kpiTypeName": "减分项", "kpiTypeId": "fb4e9734-06c3-408e-83f1-e96472ca808d", "isNewEmp": 0, "fieldValueList": [], "taskId": "2154632"}, {"alreadyNodes": [], "itemRule": "门店所有订单收银结算0错误", "reserveOkrWeight": 0, "showFinishBar": 0, "itemUnit": "笔", "multipleReviewersType": "or", "taskUserId": "3388601", "itemScoreValue": "{\"type\":\"toMainScore\"}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "", "formulaType": 1, "finishValueType": 1, "createdTime": 1741175522000, "id": "20407824", "createdUser": "12099075", "scorerType": "user", "order": 1, "itemFormula": "", "updatedTime": 1741175522000, "thresholdJson": "[]", "typeOrder": 2, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "num", "version": 1, "companyId": {"id": "53557948-3376-49ab-a7e2-0862e4495995"}, "resultInputType": "no", "itemFullScoreCfg": "false", "mustResultInput": 0, "itemTargetValue": 0, "itemFieldJson": "[{\"companyId\":\"53557948-3376-49ab-a7e2-0862e4495995\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"112bb25c-74f9-4dfd-979e-bf4ddfe00bc7\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"companyId\":\"53557948-3376-49ab-a7e2-0862e4495995\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"c5052522-1a82-487b-8378-79eaa1d86bf3\",\"isSystemField\":\"false\",\"label\":\"目标值\",\"value\":0,\"yVal\":true}]", "kpiTypeClassify": "subtract", "formulaCondition": "[{\"companyId\":\"53557948-3376-49ab-a7e2-0862e4495995\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"112bb25c-74f9-4dfd-979e-bf4ddfe00bc7\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true,\"kpiItemId\":\"b2a4b390-03ea-45cf-9ef6-a2f4d0f7c0b9\"},{\"companyId\":\"53557948-3376-49ab-a7e2-0862e4495995\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"c5052522-1a82-487b-8378-79eaa1d86bf3\",\"isSystemField\":\"false\",\"label\":\"目标值\",\"value\":0,\"yVal\":true,\"kpiItemId\":\"b2a4b390-03ea-45cf-9ef6-a2f4d0f7c0b9\"}]", "empId": "12099075", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [{"objName": "高银凤", "objId": "*********"}], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueAuditStatus": 0, "managerLevel": "", "kpiItemName": "收银准确率", "otherReqField": {"inputFinishValue": {"isReq": 0, "desc": "完成值必填"}, "attachment": {"isReq": 0, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}, "isDeleted": "false", "finalSubmitFinishValue": 0, "kpiTypeWeight": 0, "itemTargetValueText": "", "inputRole": [], "itemWeight": 0, "scoringRule": "门店交易订单错误一笔扣除5分", "kpiItemId": "b2a4b390-03ea-45cf-9ef6-a2f4d0f7c0b9", "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "", "kpiTypeName": "减分项", "kpiTypeId": "fb4e9734-06c3-408e-83f1-e96472ca808d", "isNewEmp": 0, "fieldValueList": [], "taskId": "2154632"}], "taskId": "2154632", "infos": [], "empEvalRule": {"indexRaters": [{"inviteRaters": [], "node": "item", "raters": [{"empId": "12099075", "type": 0, "empName": "周颖", "status": "wait"}, {"empId": "*********", "type": 0, "empName": "高银凤", "status": "wait"}], "multiType": "or"}, {"inviteRaters": [], "node": "super", "raters": [{"empId": "12099076", "level": 1, "type": 1, "empName": "赵玉龙", "status": "wait"}], "multiType": "or"}], "initiator": "12099075", "editStatus": 0, "scoreConf": {"transferFlag": "true", "multiType": "or"}, "auditResult": {"empRepeatSkip": 0, "notFindAdminBySupAdmin": 1, "collectSendNotify": 1, "nodeEmpVacancy": 2, "auditNodes": [], "open": 0}, "finishValueAudit": {"empRepeatSkip": 0, "notFindAdminBySupAdmin": 1, "nodeEmpVacancy": 1, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "approverInfo": "1", "approverName": "直属主管", "raters": [{"empId": "12099076", "level": 1, "type": 2, "empName": "赵玉龙", "status": "wait"}], "multiType": "or", "approverType": "manager"}], "open": 1}, "s3AppointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "totalLevelRaters": [], "s3SuperRater": {"nodeWeight": 100, "superiorScoreOrder": "sameTime", "rateMode": "item", "signatureFlag": false, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "node": "superior_score", "raters": [{"empId": "12099076", "level": 1, "type": 1, "empName": "赵玉龙", "status": "wait"}], "multiType": "or", "approverType": "manager"}], "open": 1}, "isDeleted": "false", "isChanged": false, "ruleDesc": "", "ruleName": "运营专员", "scoreSortConf": {"sortItems": [{"sort": 1, "type": 10, "name": "自评"}, {"sort": 2, "type": 20, "name": "同级互评"}, {"sort": 3, "type": 30, "name": "下级互评"}, {"sort": 4, "type": 40, "name": "上级评分"}, {"sort": 5, "type": 50, "name": "指定评"}], "exeType": 1}, "createdTime": 1741175508000, "typeWeightConf": {"checkItemWeight": 1, "limit100Weight": 1, "itemWeightLimit": 100, "open": 1}, "enterScore": {"enterScoreEmpType": 1, "enterScoreMethod": "auto", "scoreStartRuleType": "before", "scoreStartRuleDay": 1}, "createdUser": "12099075", "updatedTime": 1741175550000, "confirmTask": {"sendMsgConf": {"auditNodes": [], "open": 0}, "openConfirmLT": 2, "noChangeSkipFlag": "false", "empRepeatSkip": 0, "notFindAdminBySupAdmin": 1, "confirmLTDay": 1, "nodeEmpVacancy": 2, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "modifyFlag": "false", "approverInfo": "1", "confirmAuditSign": "false", "approverName": "直属主管", "node": "modify_item_audit", "raters": [{"empId": "12099076", "level": 1, "avatar": "https://static-legacy.dingtalk.com/media/lALPDetfSCFRvXTNAyDNAyA_800_800.png", "type": 2, "dingUserId": "manager6916", "empName": "赵玉龙", "status": "wait"}], "multiType": "or", "modifyItemDimension": "all", "approverType": "manager"}, {"transferFlag": "true", "approvalOrder": 2, "modifyFlag": "false", "approverInfo": "", "approverName": "被考核人", "node": "modify_item_audit", "raters": [{"empId": "12099075", "type": 0, "empName": "周颖", "status": "wait"}], "multiType": "or", "modifyItemDimension": "all", "approverType": "taskEmp"}], "modifyItemDimension": "all", "open": 1}, "s3SelfRater": {"signatureFlag": false, "open": 0}, "interviewConf": {"empRepeatSkip": 0, "notFindAdminBySupAdmin": 1, "nodeEmpVacancy": 2, "open": 0}, "appealConf": {"open": 0}, "deadLineConf": {"open": 0}, "scoreValueConf": {"baseScore": 0, "exceedFullScore": true, "scoreRangeType": "fullScore"}, "createTotalLevelType": 1, "updatedUser": "12099075", "levelGroupId": "", "indicatorCnt": 7, "version": 0, "evaluateType": "simple", "commentReqConf": {"commentNodeSet": [{"index": 1, "scoreNodes": [], "commonItem": {"rules": [], "reqLevel": 2}, "scoreSummary": {"rules": [], "reqLevel": 3}, "plusOrSubItem": {"rules": [], "reqLevel": 2}}]}, "scoreView": {"mutualScoreAnonymous": "true", "mutualScoreViewRule": {"superior": "", "mutual": "", "appoint": "", "examinee": ""}, "superiorScoreAnonymous": "true", "appointScoreViewRule": {"superior": "", "mutual": "", "appoint": "", "examinee": ""}, "superiorScoreViewRule": {"superior": "score,attach", "mutual": "score,attach", "appoint": "score,attach", "examinee": "score,attach"}, "selfScoreViewRule": {"superior": "", "mutual": "", "appoint": "", "examinee": "score,attach"}, "appointScoreAnonymous": "true"}, "companyId": {"id": "53557948-3376-49ab-a7e2-0862e4495995"}, "publishResult": {"toEmps": [{"objType": "emp"}, {"objType": "scoreEmp"}], "type": "afterFinished", "scoreDetailPriv": {"scoreRemark": [], "scoreType": []}, "opEmps": [], "toDetailEmps": [], "dimension": 11, "open": 1}, "s3SubRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "s3PeerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "confirmResult": {"auto": 0, "sign": 1, "autoDay": 1, "scoreDetailPriv": {"scoreRemark": [], "scoreType": []}, "dimension": 3, "open": 1}, "editExeIndi": {"empRepeatSkip": 0, "notFindAdminBySupAdmin": 1, "nodeEmpVacancy": 2, "auditNodes": [], "open": 0}, "askIndexRaters": [], "commentConf": {"scoreSummarySwitch": -1, "commentFlag": "notRequired", "plusOrSubComment": 0}, "showResultType": 6, "empEvalId": "3388601"}}