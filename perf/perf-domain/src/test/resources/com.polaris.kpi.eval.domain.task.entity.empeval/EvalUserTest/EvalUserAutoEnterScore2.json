{"empId": "*********", "cycleId": "2174117", "refAsk360Flag": false, "inputFinishChanged": "●   实际成单数    10|125.00%\n●   达人抖音矩阵号使用者培训    10|100.00%\n●   达人抖音矩阵号运营    64|85.33%\n●   学习适应能力\n（分值5分）    对新知识、新政策、新趋势有较强洞察力，能较快关注并适应公司内部动态变化，面对挑战能找到解决方案，实践经验较为丰富。", "changedAuditScene": [], "changeScene": 0, "orgId": "52434666", "scoreResults": [{"empId": "*********", "updatedTime": 1740995743000, "approvalOrder": 1, "mergeRsInfos": [], "scoreWeight": 100, "modifyFlag": "false", "scorerId": "*********", "taskUserId": "3283802", "version": 0, "orgId": "", "taskAuditId": "29422370", "companyId": {"id": "1207671"}, "isDeleted": "false", "reviewersType": "or", "signatureFlag": false, "maySkip": false, "auditStatus": "pass", "createdTime": 1739002969000, "id": "b163f821-baf8-4df7-bb36-54d4462d058e", "updateTypeLevel": false, "taskId": "2148414", "waitMergeWeight": 0, "createdUser": "*********", "scorerType": "modify_item_audit"}], "allAutoCompute": false, "isAllAutoType": true, "atOrgCodePath": "|51434971|52434666|", "isDeleted": "false", "tempTask": 0, "confirmDeadLine": "", "empName": "王雪洋", "isVacancySkipRater": false, "createdTime": 1737682414000, "hasChangeOrg": false, "id": "3283802", "sendMsg": true, "taskStatus": "confirmed", "createdUser": "*********", "updatedTime": 1741000198000, "empOrgName": "达人事业部（顶佳文化）", "atOrgNamePath": "杭州捷点互动传媒有限公司|达人事业部（顶佳文化）", "kpiTypes": [{"reserveOkrWeight": 0, "scoreOptType": 2, "taskUserId": "3283802", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "des": "", "isDeleted": "false", "isRaterBack": false, "createdTime": 1740995743000, "kpiTypeWeight": 0, "createdUser": "*********", "updatedTime": 1740995743000, "scoringType": 1, "typeOrder": 0, "maxExtraScore": 0, "plusSubInterval": {"max": "", "min": ""}, "version": 0, "openOkrScore": 0, "isOkr": "false", "companyId": {"id": "1207671"}, "kpiTypeUsedFields": [{"updatedTime": 1719393943000, "adminType": 1, "show": 1, "sort": 1, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "指标名称", "kpiTypeId": "19346068", "createdTime": 1740995743000, "fieldId": "name", "req": 1, "status": "valid"}, {"updatedTime": 1719393943000, "adminType": 1, "show": 1, "sort": 2, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "考核标准", "kpiTypeId": "19346068", "createdTime": 1740995743000, "fieldId": "standard", "req": 0, "status": "valid"}, {"updatedTime": 1719393943000, "adminType": 1, "show": 0, "sort": 3, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "计分规则", "kpiTypeId": "19346068", "createdTime": 1740995743000, "fieldId": "scoreRule", "req": 0, "status": "valid"}, {"updatedTime": 1719393943000, "adminType": 1, "show": 1, "sort": 4, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "目标值", "kpiTypeId": "19346068", "createdTime": 1740995743000, "fieldId": "targetValue", "req": 1, "status": "valid"}, {"updatedTime": 1719393943000, "adminType": 1, "show": 0, "sort": 5, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "单位", "kpiTypeId": "19346068", "createdTime": 1740995743000, "fieldId": "unit", "req": 1, "status": "valid"}, {"updatedTime": 1719393943000, "adminType": 1, "show": 1, "sort": 6, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "指标权重", "kpiTypeId": "19346068", "createdTime": 1740995743000, "fieldId": "indexWeight", "req": 1, "status": "valid"}, {"updatedTime": 1719393943000, "adminType": 1, "show": 1, "sort": 7, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "评分范围", "kpiTypeId": "19346068", "createdTime": 1740995743000, "fieldId": "scoreValue", "req": 1, "status": "valid"}, {"updatedTime": 1719398857000, "adminType": 0, "show": 1, "sort": 8, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "评分标准及依据", "kpiTypeId": "19346068", "createdTime": 1740995743000, "fieldId": "5be6f1cb-7522-4e95-a5b3-55671c3021e7", "req": 0, "status": "valid"}, {"updatedTime": 1719393943000, "adminType": 1, "show": 1, "sort": 9, "taskUserId": "3283802", "type": 1, "kpiSyncReqConf": {"syncOpen": 0, "reqItems": {"inputFinishValue": {"isReq": 0, "desc": "完成值必填"}, "attachment": {"isReq": 0, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}}, "companyId": "", "isDeleted": "false", "name": "完成值录入", "kpiTypeId": "19346068", "createdTime": 1740995743000, "fieldId": "finishValue", "req": 1, "status": "valid"}, {"updatedTime": 1719398857000, "adminType": 0, "show": 0, "sort": 10, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "完成率", "kpiTypeId": "19346068", "createdTime": 1740995743000, "fieldId": "be85a901-62d3-4e30-934a-cd3eafa58232", "req": 0, "status": "valid"}, {"updatedTime": 1719398857000, "adminType": 0, "show": 0, "sort": 11, "taskUserId": "3283802", "type": 2, "companyId": "", "isDeleted": "false", "name": "分值", "kpiTypeId": "19346068", "createdTime": 1740995743000, "fieldId": "0cacbbaf-c479-4795-9315-27294cf70753", "req": 0, "status": "valid"}, {"updatedTime": 1719398857000, "adminType": 0, "show": 0, "sort": 12, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "备注", "kpiTypeId": "19346068", "createdTime": 1740995743000, "fieldId": "f4a38a48-8d60-47df-b73d-055a274bccfa", "req": 0, "status": "valid"}, {"updatedTime": 1719398857000, "adminType": 0, "show": 0, "sort": 13, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "团队提成及超额利润提成方案", "kpiTypeId": "19346068", "createdTime": 1740995743000, "fieldId": "575f06f7-7430-4f19-bb51-78852b9e203c", "req": 0, "status": "valid"}, {"updatedTime": 1719398857000, "adminType": 0, "show": 0, "sort": 14, "taskUserId": "3283802", "type": 2, "companyId": "", "isDeleted": "false", "name": "团队目标业绩", "kpiTypeId": "19346068", "createdTime": 1740995743000, "fieldId": "14b26a4d-4661-450f-ac1f-c378b304c6ac", "req": 0, "status": "valid"}, {"updatedTime": 1719398857000, "adminType": 0, "show": 0, "sort": 15, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "团队实际业绩", "kpiTypeId": "19346068", "createdTime": 1740995743000, "fieldId": "1b6dc33f-80ee-494c-979e-dd5aac160aec", "req": 0, "status": "valid"}, {"updatedTime": 1719398857000, "adminType": 0, "show": 0, "sort": 16, "taskUserId": "3283802", "type": 2, "companyId": "", "isDeleted": "false", "name": "团队gsv提成", "kpiTypeId": "19346068", "createdTime": 1740995743000, "fieldId": "299b5e1a-b8b8-46d5-9dd5-e1e3dbbe180f", "req": 0, "status": "valid"}, {"updatedTime": 1719398857000, "adminType": 0, "show": 0, "sort": 17, "taskUserId": "3283802", "type": 2, "companyId": "", "isDeleted": "false", "name": "团队利润提成", "kpiTypeId": "19346068", "createdTime": 1740995743000, "fieldId": "fbdaebf2-179b-4018-8097-ba40195dafac", "req": 0, "status": "valid"}, {"updatedTime": 1719398857000, "adminType": 0, "show": 0, "sort": 18, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "额外条件", "kpiTypeId": "19346068", "createdTime": 1740995743000, "fieldId": "32c82837-ac90-4f70-a8c2-fa40b6561c5a", "req": 0, "status": "valid"}, {"updatedTime": 1735009251000, "adminType": 0, "show": 0, "sort": 19, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "上月实际值作为本月制定目标参考", "kpiTypeId": "19346068", "createdTime": 1740995743000, "fieldId": "e7142536-34ee-4dce-b3b8-706f49732dbb", "req": 0, "status": "valid"}, {"updatedTime": 1735009251000, "adminType": 0, "show": 0, "sort": 20, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "目标", "kpiTypeId": "19346068", "createdTime": 1740995743000, "fieldId": "5bbc123d-c38c-402d-84e9-40f5c6df0a9d", "req": 0, "status": "valid"}, {"updatedTime": 1735009251000, "adminType": 0, "show": 0, "sort": 21, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "评分规则（按完成比例计算得分）", "kpiTypeId": "19346068", "createdTime": 1740995743000, "fieldId": "1d15d29d-3415-48f1-99e0-1076ccdf7918", "req": 0, "status": "valid"}, {"updatedTime": 1735009251000, "adminType": 0, "show": 0, "sort": 22, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "比例", "kpiTypeId": "19346068", "createdTime": 1740995743000, "fieldId": "0fafa688-342a-47e3-a3f2-dd72dd0a9304", "req": 0, "status": "valid"}], "kpiTypeName": "关键绩效考核指标", "kpiTypeId": "19346068", "items": [{"alreadyNodes": [], "itemRule": "当月达人“小圆圆不圆”账号，实际成交8单及以上（不包括刷单及退货，成单数以实际确收为准）", "reserveOkrWeight": 0, "showFinishBar": 1, "itemFinishValue": 10, "itemUnit": "", "multipleReviewersType": "or", "taskUserId": "3283802", "itemScoreValue": "{\"type\":\"autoDefine\",\"value\":{\"from\":0,\"to\":20,\"step\":0.1}}", "showTargetValue": "", "formulaType": 1, "finishValueType": 1, "createdTime": 1740995743000, "id": "19346069", "createdUser": "*********", "scorerType": "exam", "order": 0, "itemFormula": "", "updatedTime": 1740995743000, "thresholdJson": "[]", "typeOrder": 0, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "num", "version": 0, "companyId": {"id": "1207671"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 1, "itemTargetValue": 8, "itemFieldJson": "[{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"isSystemField\":\"false\",\"fieldId\":\"finishValue\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"isSystemField\":\"false\",\"fieldId\":\"targetValue\",\"label\":\"目标值\",\"value\":0,\"yVal\":true}]", "formulaCondition": "[{\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"完成值\",\"formulaFieldValue\":0,\"id\":\"16218940\",\"kpiItemId\":\"d35a8a38-b020-48d5-9c43-58a3b360d049\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"},{\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"目标值\",\"formulaFieldValue\":0,\"id\":\"16218941\",\"kpiItemId\":\"d35a8a38-b020-48d5-9c43-58a3b360d049\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"}]", "empId": "*********", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueAuditStatus": 0, "itemScoreRule": {"superRater": {"nodeWeight": 100, "superiorScoreOrder": "inTurn", "nodeVacancyFlag": 1, "rateMode": "item", "signatureFlag": false, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "node": "superior_score", "raters": [{"empId": "*********", "type": 0, "empName": "张志慧", "status": "wait"}], "multiType": "or", "approverType": "user"}], "open": 1}, "kpiItemId": "d35a8a38-b020-48d5-9c43-58a3b360d049", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "3283802", "version": 0, "superiorScoreWeight": 100, "superiorScoreFlag": "true", "companyId": {"id": "1207671"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "19346068", "taskId": "2148414", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "*********"}, "managerLevel": "", "kpiItemName": "实际成单数", "otherReqField": {"inputFinishValue": {"isReq": 1, "desc": "完成值必填"}, "attachment": {"isReq": 1, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "itemTargetValueText": "", "inputRole": [], "itemWeight": 20, "scoringRule": "Y≥100%\t                   30分\n80%＜Y＜100%      21~29分\n60%＜Y≤80%   \t11~20分\nY≤60%\t                 0~19分", "kpiItemId": "d35a8a38-b020-48d5-9c43-58a3b360d049", "maxExtraScore": 0, "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "*********", "kpiTypeName": "关键绩效考核指标", "kpiTypeId": "19346068", "isNewEmp": 0, "formulaFields": [{"updatedTime": 1740995743000, "kpiItemId": "d35a8a38-b020-48d5-9c43-58a3b360d049", "formulaFieldValue": 0, "companyFieldId": "0498c45f-7dc5-4e01-826e-99a7abb81e84", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "完成值", "createdTime": 1740995743000, "id": "17428800", "taskId": "2148414", "createdUser": "*********"}, {"updatedTime": 1740995743000, "kpiItemId": "d35a8a38-b020-48d5-9c43-58a3b360d049", "formulaFieldValue": 0, "companyFieldId": "26870015-d7f6-4339-8aec-7afc6540af36", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "目标值", "createdTime": 1740995743000, "id": "17429001", "taskId": "2148414", "createdUser": "*********"}], "fieldValueList": [{"updatedTime": 1739001953000, "kpiItemId": "d35a8a38-b020-48d5-9c43-58a3b360d049", "fieldName": "评分标准及依据", "isReq": 0, "type": 1, "fieldValue": "\"Y≥100% 20分\n90%≤Y＜100% 10~19分\n80%≤Y＜90% 1~9分\nY＜80% 0分\"", "companyId": "", "fieldStatus": "valid", "isDeleted": "false", "createdTime": 1740995743000, "id": "5be6f1cb-7522-4e95-a5b3-55671c3021e7", "fieldId": "5be6f1cb-7522-4e95-a5b3-55671c3021e7"}], "taskId": "2148414", "waitScores": []}, {"alreadyNodes": [], "itemRule": "培训达人抖音矩阵号的使用者", "reserveOkrWeight": 0, "showFinishBar": 1, "itemFinishValue": 10, "itemUnit": "", "multipleReviewersType": "or", "taskUserId": "3283802", "itemScoreValue": "{\"type\":\"autoDefine\",\"value\":{\"from\":0,\"to\":10,\"step\":0.1}}", "showTargetValue": "", "formulaType": 1, "finishValueType": 1, "createdTime": 1740995743000, "id": "19346070", "createdUser": "*********", "scorerType": "exam", "order": 1, "itemFormula": "", "updatedTime": 1740995743000, "thresholdJson": "[]", "typeOrder": 0, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "num", "version": 0, "companyId": {"id": "1207671"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 1, "itemTargetValue": 10, "itemFieldJson": "[{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"isSystemField\":\"false\",\"fieldId\":\"finishValue\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"isSystemField\":\"false\",\"fieldId\":\"targetValue\",\"label\":\"目标值\",\"value\":0,\"yVal\":true}]", "formulaCondition": "[{\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"完成值\",\"formulaFieldValue\":0,\"id\":\"16218942\",\"kpiItemId\":\"a527ffd9-53b7-4306-b0cb-902e9d4b2e96\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"},{\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"目标值\",\"formulaFieldValue\":0,\"id\":\"16218943\",\"kpiItemId\":\"a527ffd9-53b7-4306-b0cb-902e9d4b2e96\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"}]", "empId": "*********", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueAuditStatus": 0, "itemScoreRule": {"superRater": {"nodeWeight": 100, "superiorScoreOrder": "inTurn", "nodeVacancyFlag": 1, "rateMode": "item", "signatureFlag": false, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "node": "superior_score", "raters": [{"empId": "*********", "type": 0, "empName": "张志慧", "status": "wait"}], "multiType": "or", "approverType": "user"}], "open": 1}, "kpiItemId": "a527ffd9-53b7-4306-b0cb-902e9d4b2e96", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "3283802", "version": 0, "superiorScoreWeight": 100, "superiorScoreFlag": "true", "companyId": {"id": "1207671"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "19346068", "taskId": "2148414", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "*********"}, "managerLevel": "", "kpiItemName": "达人抖音矩阵号使用者培训", "otherReqField": {"inputFinishValue": {"isReq": 1, "desc": "完成值必填"}, "attachment": {"isReq": 1, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "itemTargetValueText": "", "inputRole": [], "itemWeight": 10, "scoringRule": "", "kpiItemId": "a527ffd9-53b7-4306-b0cb-902e9d4b2e96", "maxExtraScore": 0, "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "*********", "kpiTypeName": "关键绩效考核指标", "kpiTypeId": "19346068", "isNewEmp": 0, "formulaFields": [{"updatedTime": 1740995743000, "kpiItemId": "a527ffd9-53b7-4306-b0cb-902e9d4b2e96", "formulaFieldValue": 0, "companyFieldId": "0498c45f-7dc5-4e01-826e-99a7abb81e84", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "完成值", "createdTime": 1740995743000, "id": "17429002", "taskId": "2148414", "createdUser": "*********"}, {"updatedTime": 1740995743000, "kpiItemId": "a527ffd9-53b7-4306-b0cb-902e9d4b2e96", "formulaFieldValue": 0, "companyFieldId": "26870015-d7f6-4339-8aec-7afc6540af36", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "目标值", "createdTime": 1740995743000, "id": "17429003", "taskId": "2148414", "createdUser": "*********"}], "fieldValueList": [{"updatedTime": 1739001953000, "kpiItemId": "a527ffd9-53b7-4306-b0cb-902e9d4b2e96", "fieldName": "评分标准及依据", "isReq": 0, "type": 1, "fieldValue": "10分：完成度100%，当月培训后能独立进行切片视频剪辑与发布；培训效果好，培训内容落地性强\n7分：完成度≥80%，当月培训后80%以上能独立进行切片视频剪辑与发布；培训效果较好，培训内容部分可优化\n4分：完成度≥60%，当月培训后60%以上能独立进行切片视频剪辑与发布；培训效果一般，培训内容需要结合实际情况优化\n0分：完成度＜60%，当月培训后60%以下人员能独立进行切片视频剪辑与发布；培训效果不合格，培训内容需要重新进行梳理", "companyId": "", "fieldStatus": "valid", "isDeleted": "false", "createdTime": 1740995743000, "id": "5be6f1cb-7522-4e95-a5b3-55671c3021e7", "fieldId": "5be6f1cb-7522-4e95-a5b3-55671c3021e7"}], "taskId": "2148414", "waitScores": []}, {"alreadyNodes": [], "itemRule": "1、当月达人账号发布75条及以上有效短视频（不包含审核未通过或违规视频）\n2、每日更新3条以上\"", "reserveOkrWeight": 0, "showFinishBar": 1, "itemFinishValue": 64, "itemUnit": "", "multipleReviewersType": "or", "taskUserId": "3283802", "itemScoreValue": "{\"type\":\"autoDefine\",\"value\":{\"from\":0,\"to\":30,\"step\":0.1}}", "showTargetValue": "", "formulaType": 1, "finishValueType": 1, "createdTime": 1740995743000, "id": "19346071", "createdUser": "*********", "scorerType": "exam", "order": 2, "itemFormula": "", "updatedTime": 1740995743000, "thresholdJson": "[]", "typeOrder": 0, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "num", "version": 0, "companyId": {"id": "1207671"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 1, "itemTargetValue": 75, "itemFieldJson": "[{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"isSystemField\":\"false\",\"fieldId\":\"finishValue\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"isSystemField\":\"false\",\"fieldId\":\"targetValue\",\"label\":\"目标值\",\"value\":0,\"yVal\":true}]", "formulaCondition": "[{\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"完成值\",\"formulaFieldValue\":0,\"id\":\"16218944\",\"kpiItemId\":\"dd881440-3077-4a32-a9a3-867408f031ef\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"},{\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"目标值\",\"formulaFieldValue\":0,\"id\":\"16218945\",\"kpiItemId\":\"dd881440-3077-4a32-a9a3-867408f031ef\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"}]", "empId": "*********", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueAuditStatus": 0, "itemScoreRule": {"superRater": {"nodeWeight": 100, "superiorScoreOrder": "inTurn", "nodeVacancyFlag": 1, "rateMode": "item", "signatureFlag": false, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "node": "superior_score", "raters": [{"empId": "*********", "type": 0, "empName": "张志慧", "status": "wait"}], "multiType": "or", "approverType": "user"}], "open": 1}, "kpiItemId": "dd881440-3077-4a32-a9a3-867408f031ef", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "3283802", "version": 0, "superiorScoreWeight": 100, "superiorScoreFlag": "true", "companyId": {"id": "1207671"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "19346068", "taskId": "2148414", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "*********"}, "managerLevel": "", "kpiItemName": "达人抖音矩阵号运营", "otherReqField": {"inputFinishValue": {"isReq": 1, "desc": "完成值必填"}, "attachment": {"isReq": 1, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "itemTargetValueText": "", "inputRole": [], "itemWeight": 30, "scoringRule": "Y≥100%        20分 \n80%＜Y＜100%   15分 \n60%＜Y≤80% 10分 \nY≤60% 5分", "kpiItemId": "dd881440-3077-4a32-a9a3-867408f031ef", "maxExtraScore": 0, "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "*********", "kpiTypeName": "关键绩效考核指标", "kpiTypeId": "19346068", "isNewEmp": 0, "formulaFields": [{"updatedTime": 1740995743000, "kpiItemId": "dd881440-3077-4a32-a9a3-867408f031ef", "formulaFieldValue": 0, "companyFieldId": "0498c45f-7dc5-4e01-826e-99a7abb81e84", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "完成值", "createdTime": 1740995743000, "id": "17429004", "taskId": "2148414", "createdUser": "*********"}, {"updatedTime": 1740995743000, "kpiItemId": "dd881440-3077-4a32-a9a3-867408f031ef", "formulaFieldValue": 0, "companyFieldId": "26870015-d7f6-4339-8aec-7afc6540af36", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "目标值", "createdTime": 1740995743000, "id": "17429005", "taskId": "2148414", "createdUser": "*********"}], "fieldValueList": [{"updatedTime": 1739001953000, "kpiItemId": "dd881440-3077-4a32-a9a3-867408f031ef", "fieldName": "评分标准及依据", "isReq": 0, "type": 1, "fieldValue": "每日更新发布3条，月更新发布75条以上短视频：\n10分：当月视频发布总数量≥  75条；\n1-9分：75条＞当月视频发布总数量≥50条\n0分：当月视频发布总数量＜50条", "companyId": "", "fieldStatus": "valid", "isDeleted": "false", "createdTime": 1740995743000, "id": "5be6f1cb-7522-4e95-a5b3-55671c3021e7", "fieldId": "5be6f1cb-7522-4e95-a5b3-55671c3021e7"}], "taskId": "2148414", "waitScores": []}], "itemLimitCnt": {"max": "", "openItemLimit": "false", "min": ""}, "alreadyScores": [], "waitScores": []}, {"reserveOkrWeight": 0, "scoreOptType": 2, "taskUserId": "3283802", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "des": "", "isDeleted": "false", "isRaterBack": false, "createdTime": 1740995743000, "kpiTypeWeight": 0, "createdUser": "*********", "updatedTime": 1740995743000, "scoringType": 1, "typeOrder": 1, "maxExtraScore": 0, "plusSubInterval": {"max": "", "min": ""}, "version": 0, "openOkrScore": 0, "isOkr": "false", "companyId": {"id": "1207671"}, "kpiTypeUsedFields": [{"updatedTime": 1735954895000, "adminType": 1, "show": 1, "sort": 1, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "指标名称", "kpiTypeId": "19346072", "createdTime": 1740995743000, "fieldId": "name", "req": 1, "status": "valid"}, {"updatedTime": 1735954895000, "adminType": 1, "show": 1, "sort": 2, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "考核标准", "kpiTypeId": "19346072", "createdTime": 1740995743000, "fieldId": "standard", "req": 0, "status": "valid"}, {"updatedTime": 1735954895000, "adminType": 1, "show": 0, "sort": 3, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "计分规则", "kpiTypeId": "19346072", "createdTime": 1740995743000, "fieldId": "scoreRule", "req": 0, "status": "valid"}, {"updatedTime": 1735954895000, "adminType": 1, "show": 0, "sort": 4, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "目标值", "kpiTypeId": "19346072", "createdTime": 1740995743000, "fieldId": "targetValue", "req": 0, "status": "valid"}, {"updatedTime": 1735954895000, "adminType": 1, "show": 0, "sort": 5, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "单位", "kpiTypeId": "19346072", "createdTime": 1740995743000, "fieldId": "unit", "req": 0, "status": "valid"}, {"updatedTime": 1735954895000, "adminType": 1, "show": 1, "sort": 6, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "指标权重", "kpiTypeId": "19346072", "createdTime": 1740995743000, "fieldId": "indexWeight", "req": 1, "status": "valid"}, {"updatedTime": 1735954895000, "adminType": 1, "show": 1, "sort": 7, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "评分范围", "kpiTypeId": "19346072", "createdTime": 1740995743000, "fieldId": "scoreValue", "req": 1, "status": "valid"}, {"updatedTime": 1735954895000, "adminType": 1, "show": 1, "sort": 8, "taskUserId": "3283802", "type": 1, "kpiSyncReqConf": {"syncOpen": 0, "reqItems": {"inputFinishValue": {"isReq": 0, "desc": "完成值必填"}, "attachment": {"isReq": 0, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}}, "companyId": "", "isDeleted": "false", "name": "完成值录入", "kpiTypeId": "19346072", "createdTime": 1740995743000, "fieldId": "finishValue", "req": 0, "status": "valid"}, {"updatedTime": 1735954895000, "adminType": 0, "show": 0, "sort": 9, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "完成率", "kpiTypeId": "19346072", "createdTime": 1740995743000, "fieldId": "be85a901-62d3-4e30-934a-cd3eafa58232", "req": 0, "status": "valid"}, {"updatedTime": 1735954895000, "adminType": 0, "show": 1, "sort": 10, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "评分标准及依据", "kpiTypeId": "19346072", "createdTime": 1740995743000, "fieldId": "5be6f1cb-7522-4e95-a5b3-55671c3021e7", "req": 0, "status": "valid"}, {"updatedTime": 1735954895000, "adminType": 0, "show": 0, "sort": 11, "taskUserId": "3283802", "type": 2, "companyId": "", "isDeleted": "false", "name": "分值", "kpiTypeId": "19346072", "createdTime": 1740995743000, "fieldId": "0cacbbaf-c479-4795-9315-27294cf70753", "req": 0, "status": "valid"}, {"updatedTime": 1735954895000, "adminType": 0, "show": 0, "sort": 12, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "备注", "kpiTypeId": "19346072", "createdTime": 1740995743000, "fieldId": "f4a38a48-8d60-47df-b73d-055a274bccfa", "req": 0, "status": "valid"}, {"updatedTime": 1735954895000, "adminType": 0, "show": 0, "sort": 13, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "团队提成及超额利润提成方案", "kpiTypeId": "19346072", "createdTime": 1740995743000, "fieldId": "575f06f7-7430-4f19-bb51-78852b9e203c", "req": 0, "status": "valid"}, {"updatedTime": 1735954895000, "adminType": 0, "show": 0, "sort": 14, "taskUserId": "3283802", "type": 2, "companyId": "", "isDeleted": "false", "name": "团队目标业绩", "kpiTypeId": "19346072", "createdTime": 1740995743000, "fieldId": "14b26a4d-4661-450f-ac1f-c378b304c6ac", "req": 0, "status": "valid"}, {"updatedTime": 1735954895000, "adminType": 0, "show": 0, "sort": 15, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "团队实际业绩", "kpiTypeId": "19346072", "createdTime": 1740995743000, "fieldId": "1b6dc33f-80ee-494c-979e-dd5aac160aec", "req": 0, "status": "valid"}, {"updatedTime": 1735954895000, "adminType": 0, "show": 0, "sort": 16, "taskUserId": "3283802", "type": 2, "companyId": "", "isDeleted": "false", "name": "团队gsv提成", "kpiTypeId": "19346072", "createdTime": 1740995743000, "fieldId": "299b5e1a-b8b8-46d5-9dd5-e1e3dbbe180f", "req": 0, "status": "valid"}, {"updatedTime": 1735954895000, "adminType": 0, "show": 0, "sort": 17, "taskUserId": "3283802", "type": 2, "companyId": "", "isDeleted": "false", "name": "团队利润提成", "kpiTypeId": "19346072", "createdTime": 1740995743000, "fieldId": "fbdaebf2-179b-4018-8097-ba40195dafac", "req": 0, "status": "valid"}, {"updatedTime": 1735954895000, "adminType": 0, "show": 0, "sort": 18, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "额外条件", "kpiTypeId": "19346072", "createdTime": 1740995743000, "fieldId": "32c82837-ac90-4f70-a8c2-fa40b6561c5a", "req": 0, "status": "valid"}, {"updatedTime": 1735954895000, "adminType": 0, "show": 0, "sort": 19, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "上月实际值作为本月制定目标参考", "kpiTypeId": "19346072", "createdTime": 1740995743000, "fieldId": "e7142536-34ee-4dce-b3b8-706f49732dbb", "req": 0, "status": "valid"}, {"updatedTime": 1735954895000, "adminType": 0, "show": 0, "sort": 20, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "目标", "kpiTypeId": "19346072", "createdTime": 1740995743000, "fieldId": "5bbc123d-c38c-402d-84e9-40f5c6df0a9d", "req": 0, "status": "valid"}, {"updatedTime": 1735954895000, "adminType": 0, "show": 0, "sort": 21, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "评分规则（按完成比例计算得分）", "kpiTypeId": "19346072", "createdTime": 1740995743000, "fieldId": "1d15d29d-3415-48f1-99e0-1076ccdf7918", "req": 0, "status": "valid"}, {"updatedTime": 1735954895000, "adminType": 0, "show": 0, "sort": 22, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "比例", "kpiTypeId": "19346072", "createdTime": 1740995743000, "fieldId": "0fafa688-342a-47e3-a3f2-dd72dd0a9304", "req": 0, "status": "valid"}], "kpiTypeName": "非量化考核", "kpiTypeId": "19346072", "items": [{"alreadyNodes": [], "itemRule": "1、培训矩阵号视频剪辑与运营\n2、梳理培训过程sop", "reserveOkrWeight": 0, "showFinishBar": 0, "itemUnit": "", "multipleReviewersType": "or", "taskUserId": "3283802", "itemScoreValue": "{\"type\":\"autoDefine\",\"value\":{\"from\":0,\"to\":20,\"step\":0.5}}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "", "formulaType": 1, "finishValueType": 1, "createdTime": 1740995743000, "id": "19346073", "createdUser": "*********", "scorerType": "exam", "order": 0, "itemFormula": "", "updatedTime": 1740995743000, "thresholdJson": "[]", "typeOrder": 1, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "num", "version": 0, "companyId": {"id": "1207671"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 1, "itemTargetValue": 0, "itemFieldJson": "[{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"isSystemField\":\"false\",\"fieldId\":\"finishValue\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"isSystemField\":\"false\",\"fieldId\":\"targetValue\",\"label\":\"目标值\",\"value\":0,\"yVal\":true}]", "formulaCondition": "[{\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"完成值\",\"formulaFieldValue\":0,\"id\":\"16218946\",\"kpiItemId\":\"942224f9-e946-4950-958a-ff3fb9442fc3\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"},{\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"目标值\",\"formulaFieldValue\":0,\"id\":\"16218947\",\"kpiItemId\":\"942224f9-e946-4950-958a-ff3fb9442fc3\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"}]", "empId": "*********", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueAuditStatus": 0, "itemScoreRule": {"superRater": {"nodeWeight": 100, "superiorScoreOrder": "inTurn", "nodeVacancyFlag": 1, "rateMode": "item", "signatureFlag": false, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "node": "superior_score", "raters": [{"empId": "*********", "type": 0, "empName": "张志慧", "status": "wait"}], "multiType": "or", "approverType": "user"}], "open": 1}, "kpiItemId": "942224f9-e946-4950-958a-ff3fb9442fc3", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "3283802", "version": 0, "superiorScoreWeight": 100, "superiorScoreFlag": "true", "companyId": {"id": "1207671"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "19346072", "taskId": "2148414", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "*********"}, "managerLevel": "", "kpiItemName": "矩阵号使用者培训", "otherReqField": {"inputFinishValue": {"isReq": 0, "desc": "完成值必填"}, "attachment": {"isReq": 0, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "itemTargetValueText": "", "inputRole": [], "itemWeight": 20, "scoringRule": "", "kpiItemId": "942224f9-e946-4950-958a-ff3fb9442fc3", "maxExtraScore": 0, "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "*********", "kpiTypeName": "非量化考核", "kpiTypeId": "19346072", "isNewEmp": 0, "formulaFields": [{"updatedTime": 1740995743000, "kpiItemId": "942224f9-e946-4950-958a-ff3fb9442fc3", "formulaFieldValue": 0, "companyFieldId": "0498c45f-7dc5-4e01-826e-99a7abb81e84", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "完成值", "createdTime": 1740995743000, "id": "17429006", "taskId": "2148414", "createdUser": "*********"}, {"updatedTime": 1740995743000, "kpiItemId": "942224f9-e946-4950-958a-ff3fb9442fc3", "formulaFieldValue": 0, "companyFieldId": "26870015-d7f6-4339-8aec-7afc6540af36", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "目标值", "createdTime": 1740995743000, "id": "17429007", "taskId": "2148414", "createdUser": "*********"}], "fieldValueList": [{"updatedTime": 1735954895000, "kpiItemId": "942224f9-e946-4950-958a-ff3fb9442fc3", "fieldName": "评分标准及依据", "isReq": 0, "type": 1, "fieldValue": "20分：制定的培训计划详细、完善、流畅、成本低，培训结果超于预期目标，并能够根据培训情况梳理详细的sop，落地性强\n15分：制定的培训计划完善、流畅、成本一般，培训结果符合预期目标，并能够根据培训情况梳理具体的sop，落地性较强\n10分：制定的培训计划一般、流程问题较多、成本较高，培训结果低于预期目标，根据培训情况梳理的sop落地性较差\n5分：制定的培训计划不完善、问题多、成本高，培训结果符合远低于预期目标，不能够根据培训情况梳理具体的sop，落地性差", "companyId": "", "fieldStatus": "valid", "isDeleted": "false", "createdTime": 1740995743000, "id": "5be6f1cb-7522-4e95-a5b3-55671c3021e7", "fieldId": "5be6f1cb-7522-4e95-a5b3-55671c3021e7"}], "taskId": "2148414", "waitScores": []}], "itemLimitCnt": {"max": "", "openItemLimit": "false", "min": ""}, "alreadyScores": [], "waitScores": []}, {"reserveOkrWeight": 5, "scoreOptType": 2, "lockedItems": ["addIndex", "modifyIndex", "deleteIndex"], "taskUserId": "3283802", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "des": "", "isDeleted": "false", "isRaterBack": false, "createdTime": 1740995743000, "kpiTypeWeight": 0, "createdUser": "*********", "updatedTime": 1740995743000, "typeOrder": 2, "maxExtraScore": 0, "plusSubInterval": {"max": "", "min": ""}, "version": 0, "openOkrScore": 0, "isOkr": "false", "companyId": {"id": "1207671"}, "kpiTypeUsedFields": [{"updatedTime": 1719393943000, "adminType": 1, "show": 1, "sort": 1, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "指标名称", "kpiTypeId": "19346074", "createdTime": 1740995743000, "fieldId": "name", "req": 1, "status": "valid"}, {"updatedTime": 1719393943000, "adminType": 1, "show": 1, "sort": 2, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "考核标准", "kpiTypeId": "19346074", "createdTime": 1740995743000, "fieldId": "standard", "req": 0, "status": "valid"}, {"updatedTime": 1719393943000, "adminType": 1, "show": 0, "sort": 3, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "计分规则", "kpiTypeId": "19346074", "createdTime": 1740995743000, "fieldId": "scoreRule", "req": 0, "status": "valid"}, {"updatedTime": 1719393943000, "adminType": 1, "show": 0, "sort": 4, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "目标值", "kpiTypeId": "19346074", "createdTime": 1740995743000, "fieldId": "targetValue", "req": 1, "status": "valid"}, {"updatedTime": 1719393943000, "adminType": 1, "show": 0, "sort": 5, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "单位", "kpiTypeId": "19346074", "createdTime": 1740995743000, "fieldId": "unit", "req": 1, "status": "valid"}, {"updatedTime": 1719393943000, "adminType": 1, "show": 1, "sort": 6, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "指标权重", "kpiTypeId": "19346074", "createdTime": 1740995743000, "fieldId": "indexWeight", "req": 1, "status": "valid"}, {"updatedTime": 1719393943000, "adminType": 1, "show": 1, "sort": 7, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "评分分值", "kpiTypeId": "19346074", "createdTime": 1740995743000, "fieldId": "scoreValue", "req": 1, "status": "valid"}, {"updatedTime": 1719398933000, "adminType": 0, "show": 1, "sort": 8, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "评分标准及依据", "kpiTypeId": "19346074", "createdTime": 1740995743000, "fieldId": "5be6f1cb-7522-4e95-a5b3-55671c3021e7", "req": 0, "status": "valid"}, {"updatedTime": 1719393943000, "adminType": 1, "show": 1, "sort": 9, "taskUserId": "3283802", "type": 1, "kpiSyncReqConf": {"syncOpen": 0, "reqItems": {"inputFinishValue": {"isReq": 0, "desc": "完成值必填"}, "attachment": {"isReq": 0, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}}, "companyId": "", "isDeleted": "false", "name": "完成值录入", "kpiTypeId": "19346074", "createdTime": 1740995743000, "fieldId": "finishValue", "req": 1, "status": "valid"}, {"updatedTime": 1719398933000, "adminType": 0, "show": 0, "sort": 10, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "完成率", "kpiTypeId": "19346074", "createdTime": 1740995743000, "fieldId": "be85a901-62d3-4e30-934a-cd3eafa58232", "req": 0, "status": "valid"}, {"updatedTime": 1719398933000, "adminType": 0, "show": 0, "sort": 11, "taskUserId": "3283802", "type": 2, "companyId": "", "isDeleted": "false", "name": "分值", "kpiTypeId": "19346074", "createdTime": 1740995743000, "fieldId": "0cacbbaf-c479-4795-9315-27294cf70753", "req": 0, "status": "valid"}, {"updatedTime": 1719398933000, "adminType": 0, "show": 0, "sort": 12, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "备注", "kpiTypeId": "19346074", "createdTime": 1740995743000, "fieldId": "f4a38a48-8d60-47df-b73d-055a274bccfa", "req": 0, "status": "valid"}, {"updatedTime": 1719398933000, "adminType": 0, "show": 0, "sort": 13, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "团队提成及超额利润提成方案", "kpiTypeId": "19346074", "createdTime": 1740995743000, "fieldId": "575f06f7-7430-4f19-bb51-78852b9e203c", "req": 0, "status": "valid"}, {"updatedTime": 1719398933000, "adminType": 0, "show": 0, "sort": 14, "taskUserId": "3283802", "type": 2, "companyId": "", "isDeleted": "false", "name": "团队目标业绩", "kpiTypeId": "19346074", "createdTime": 1740995743000, "fieldId": "14b26a4d-4661-450f-ac1f-c378b304c6ac", "req": 0, "status": "valid"}, {"updatedTime": 1719398933000, "adminType": 0, "show": 0, "sort": 15, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "团队实际业绩", "kpiTypeId": "19346074", "createdTime": 1740995743000, "fieldId": "1b6dc33f-80ee-494c-979e-dd5aac160aec", "req": 0, "status": "valid"}, {"updatedTime": 1719398933000, "adminType": 0, "show": 0, "sort": 16, "taskUserId": "3283802", "type": 2, "companyId": "", "isDeleted": "false", "name": "团队gsv提成", "kpiTypeId": "19346074", "createdTime": 1740995743000, "fieldId": "299b5e1a-b8b8-46d5-9dd5-e1e3dbbe180f", "req": 0, "status": "valid"}, {"updatedTime": 1719398933000, "adminType": 0, "show": 0, "sort": 17, "taskUserId": "3283802", "type": 2, "companyId": "", "isDeleted": "false", "name": "团队利润提成", "kpiTypeId": "19346074", "createdTime": 1740995743000, "fieldId": "fbdaebf2-179b-4018-8097-ba40195dafac", "req": 0, "status": "valid"}, {"updatedTime": 1719398933000, "adminType": 0, "show": 0, "sort": 18, "taskUserId": "3283802", "type": 1, "companyId": "", "isDeleted": "false", "name": "额外条件", "kpiTypeId": "19346074", "createdTime": 1740995743000, "fieldId": "32c82837-ac90-4f70-a8c2-fa40b6561c5a", "req": 0, "status": "valid"}], "kpiTypeName": "工作态度", "kpiTypeId": "19346074", "items": [{"alreadyNodes": [], "itemRule": "1、有计划的开展工作，对工作内容/时间/数量/流程的安排及分配遵循合理性，有效性\n2、具备强大的行动力，能够迅速的将计划或制度实践，不拖延，不推诿，有实际结果。\n3、准时发送工作日报/周报/战报等相关数据报表，每少提交一次扣1分。", "reserveOkrWeight": 5, "showFinishBar": 0, "itemUnit": "%", "multipleReviewersType": "or", "taskUserId": "3283802", "itemScoreValue": "{\"type\":\"autoDefine\",\"value\":{\"from\":0,\"to\":5,\"step\":0.5}}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "", "formulaType": 1, "finishValueType": 1, "createdTime": 1740995743000, "id": "19346075", "createdUser": "*********", "scorerType": "exam", "order": 0, "itemFormula": "", "updatedTime": 1741000198000, "thresholdJson": "[]", "typeOrder": 2, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "text", "version": 0, "companyId": {"id": "1207671"}, "resultInputType": "user", "itemFullScoreCfg": "false", "mustResultInput": 1, "itemTargetValue": 0, "itemFieldJson": "[{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"isSystemField\":\"false\",\"fieldId\":\"finishValue\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"isSystemField\":\"false\",\"fieldId\":\"targetValue\",\"label\":\"目标值\",\"value\":0,\"yVal\":true}]", "formulaCondition": "[{\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"完成值\",\"formulaFieldValue\":0,\"id\":\"16218948\",\"kpiItemId\":\"396bffaa-c542-4827-9efd-579262724c4f\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"},{\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"目标值\",\"formulaFieldValue\":0,\"id\":\"16218949\",\"kpiItemId\":\"396bffaa-c542-4827-9efd-579262724c4f\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"}]", "empId": "*********", "itemType": "non-measurable", "itemFinishValueText": "日报应提交20次，实际提交17次", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueAuditStatus": 0, "itemScoreRule": {"superRater": {"nodeWeight": 100, "superiorScoreOrder": "inTurn", "nodeVacancyFlag": 1, "rateMode": "item", "signatureFlag": false, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "node": "superior_score", "raters": [{"empId": "*********", "type": 0, "empName": "张志慧", "status": "wait"}], "multiType": "or", "approverType": "user"}], "open": 1}, "kpiItemId": "396bffaa-c542-4827-9efd-579262724c4f", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "3283802", "version": 0, "superiorScoreWeight": 100, "superiorScoreFlag": "true", "companyId": {"id": "1207671"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "19346074", "taskId": "2148414", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "*********"}, "managerLevel": "", "kpiItemName": "计划性/执行能力\n（分值5分）", "otherReqField": {"inputFinishValue": {"isReq": 1, "desc": "完成值必填"}, "attachment": {"isReq": 0, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "itemTargetValueText": "", "inputRole": [], "itemWeight": 5, "scoringRule": "", "kpiItemId": "396bffaa-c542-4827-9efd-579262724c4f", "maxExtraScore": 0, "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "*********", "kpiTypeName": "工作态度", "kpiTypeId": "19346074", "isNewEmp": 0, "formulaFields": [{"updatedTime": 1740995743000, "kpiItemId": "396bffaa-c542-4827-9efd-579262724c4f", "formulaFieldValue": 0, "companyFieldId": "0498c45f-7dc5-4e01-826e-99a7abb81e84", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "完成值", "createdTime": 1740995743000, "id": "17429008", "taskId": "2148414", "createdUser": "*********"}, {"updatedTime": 1740995743000, "kpiItemId": "396bffaa-c542-4827-9efd-579262724c4f", "formulaFieldValue": 0, "companyFieldId": "26870015-d7f6-4339-8aec-7afc6540af36", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "目标值", "createdTime": 1740995743000, "id": "17429009", "taskId": "2148414", "createdUser": "*********"}], "fieldValueList": [{"updatedTime": 1735024790000, "kpiItemId": "396bffaa-c542-4827-9efd-579262724c4f", "fieldName": "评分标准及依据", "isReq": 0, "type": 1, "fieldValue": "5分：工作计划详尽，时间/数量/流程安排合理有效，行动力强，计划或制度执行迅速，无拖延，有显著实际结果。 \n4分：工作计划较为详尽，安排较为合理有效，行动力较强，计划或制度执行较为迅速，偶尔有拖延，实际结果良好。 \n3分：工作计划一般，安排基本合理，行动力一般，计划或制度执行一般，存在拖延，实际结果一般。 \n2分：工作计划不够详尽，安排不够合理，行动力弱，计划或制度执行缓慢，经常拖延，实际结果较差。 \n1分：工作计划混乱，安排不合理，行动力极弱，计划或制度执行严重滞后，严重拖延，实际结果极差。", "companyId": "", "fieldStatus": "valid", "isDeleted": "false", "createdTime": 1740995743000, "id": "5be6f1cb-7522-4e95-a5b3-55671c3021e7", "fieldId": "5be6f1cb-7522-4e95-a5b3-55671c3021e7"}], "taskId": "2148414", "waitScores": []}, {"alreadyNodes": [], "itemRule": "1、对新知识，新政策，新趋势的敏锐洞察力，能够及时关注达人及视频切片领域的动态变化\n2、对发布的视频内容进行组织诊断，对现有问题和潜在风险进行完善和规避，明确公司业务需求，确保切片稳定运行", "reserveOkrWeight": 5, "showFinishBar": 0, "itemUnit": "%", "multipleReviewersType": "or", "taskUserId": "3283802", "itemScoreValue": "{\"type\":\"autoDefine\",\"value\":{\"from\":0,\"to\":5,\"step\":0.5}}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "", "formulaType": 1, "finishValueType": 1, "createdTime": 1740995743000, "id": "19346076", "createdUser": "*********", "scorerType": "exam", "order": 1, "itemFormula": "", "updatedTime": 1740995743000, "thresholdJson": "[]", "typeOrder": 2, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "text", "version": 0, "companyId": {"id": "1207671"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 1, "itemTargetValue": 0, "itemFieldJson": "[{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"isSystemField\":\"false\",\"fieldId\":\"finishValue\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"isSystemField\":\"false\",\"fieldId\":\"targetValue\",\"label\":\"目标值\",\"value\":0,\"yVal\":true}]", "formulaCondition": "[{\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"完成值\",\"formulaFieldValue\":0,\"id\":\"16218950\",\"kpiItemId\":\"a824d730-5f3f-401b-b094-ac2c5b074b75\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"},{\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"目标值\",\"formulaFieldValue\":0,\"id\":\"16218951\",\"kpiItemId\":\"a824d730-5f3f-401b-b094-ac2c5b074b75\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"}]", "empId": "*********", "itemType": "non-measurable", "itemFinishValueText": "对新知识、新政策、新趋势有较强洞察力，能较快关注并适应公司内部动态变化，面对挑战能找到解决方案，实践经验较为丰富。", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueAuditStatus": 0, "itemScoreRule": {"superRater": {"nodeWeight": 100, "superiorScoreOrder": "inTurn", "nodeVacancyFlag": 1, "rateMode": "item", "signatureFlag": false, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "node": "superior_score", "raters": [{"empId": "*********", "type": 0, "empName": "张志慧", "status": "wait"}], "multiType": "or", "approverType": "user"}], "open": 1}, "kpiItemId": "a824d730-5f3f-401b-b094-ac2c5b074b75", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "3283802", "version": 0, "superiorScoreWeight": 100, "superiorScoreFlag": "true", "companyId": {"id": "1207671"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "19346074", "taskId": "2148414", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "*********"}, "managerLevel": "", "kpiItemName": "学习适应能力\n（分值5分）", "otherReqField": {"inputFinishValue": {"isReq": 1, "desc": "完成值必填"}, "attachment": {"isReq": 0, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "itemTargetValueText": "", "inputRole": [], "itemWeight": 5, "scoringRule": "", "kpiItemId": "a824d730-5f3f-401b-b094-ac2c5b074b75", "maxExtraScore": 0, "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "*********", "kpiTypeName": "工作态度", "kpiTypeId": "19346074", "isNewEmp": 0, "formulaFields": [{"updatedTime": 1740995743000, "kpiItemId": "a824d730-5f3f-401b-b094-ac2c5b074b75", "formulaFieldValue": 0, "companyFieldId": "0498c45f-7dc5-4e01-826e-99a7abb81e84", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "完成值", "createdTime": 1740995743000, "id": "17429010", "taskId": "2148414", "createdUser": "*********"}, {"updatedTime": 1740995743000, "kpiItemId": "a824d730-5f3f-401b-b094-ac2c5b074b75", "formulaFieldValue": 0, "companyFieldId": "26870015-d7f6-4339-8aec-7afc6540af36", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "目标值", "createdTime": 1740995743000, "id": "17429011", "taskId": "2148414", "createdUser": "*********"}], "fieldValueList": [{"updatedTime": 1733824214000, "kpiItemId": "a824d730-5f3f-401b-b094-ac2c5b074b75", "fieldName": "评分标准及依据", "isReq": 0, "type": 1, "fieldValue": "5分：对新知识、新政策、新趋势有极强洞察力，能迅速关注并适应公司内部动态变化，面对挑战能迅速找到解决方案，实践经验丰富。 \n4分：对新知识、新政策、新趋势有较强洞察力，能较快关注并适应公司内部动态变化，面对挑战能找到解决方案，实践经验较为丰富。 \n3分：对新知识、新政策、新趋势有一定洞察力，能一般关注并适应公司内部动态变化，面对挑战解决方案一般，实践经验一般。 \n2分：对新知识、新政策、新趋势洞察力较弱，关注并适应公司内部动态变化较慢，面对挑战解决方案较差，实践经验较少。 \n1分：对新知识、新政策、新趋势几乎没有洞察力，很难关注并适应公司内部动态变化，面对挑战难以找到解决方案，几乎没有实践经验。", "companyId": "", "fieldStatus": "valid", "isDeleted": "false", "createdTime": 1740995743000, "id": "5be6f1cb-7522-4e95-a5b3-55671c3021e7", "fieldId": "5be6f1cb-7522-4e95-a5b3-55671c3021e7"}], "taskId": "2148414", "waitScores": []}, {"alreadyNodes": [], "itemRule": "1、能够清晰，准确的用恰当的语言讲自己的想法，观点和信息有条理性的表达出来\n2、独立对接设计、运营，完成每日工作推进\n3、渠道人员应时需求，相关问题解答提问等\n4、策划端协作对接", "reserveOkrWeight": 5, "showFinishBar": 0, "itemUnit": "%", "multipleReviewersType": "or", "taskUserId": "3283802", "itemScoreValue": "{\"type\":\"autoDefine\",\"value\":{\"from\":0,\"to\":5,\"step\":0.5}}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "", "formulaType": 1, "finishValueType": 1, "createdTime": 1740995743000, "id": "19346077", "createdUser": "*********", "scorerType": "exam", "order": 2, "itemFormula": "", "updatedTime": 1740995743000, "thresholdJson": "[]", "typeOrder": 2, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "text", "version": 0, "companyId": {"id": "1207671"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 1, "itemTargetValue": 0, "itemFieldJson": "[{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"isSystemField\":\"false\",\"fieldId\":\"finishValue\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"isSystemField\":\"false\",\"fieldId\":\"targetValue\",\"label\":\"目标值\",\"value\":0,\"yVal\":true}]", "formulaCondition": "[{\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"完成值\",\"formulaFieldValue\":0,\"id\":\"16218952\",\"kpiItemId\":\"62e87b36-eb9b-4daa-a89b-a6e22e7975be\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"},{\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"目标值\",\"formulaFieldValue\":0,\"id\":\"16218953\",\"kpiItemId\":\"62e87b36-eb9b-4daa-a89b-a6e22e7975be\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"}]", "empId": "*********", "itemType": "non-measurable", "itemFinishValueText": "沟通和协调能力较强，能够有效解决跨部门合作中的问题。", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueAuditStatus": 0, "itemScoreRule": {"superRater": {"nodeWeight": 100, "superiorScoreOrder": "inTurn", "nodeVacancyFlag": 1, "rateMode": "item", "signatureFlag": false, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "node": "superior_score", "raters": [{"empId": "*********", "type": 0, "empName": "张志慧", "status": "wait"}], "multiType": "or", "approverType": "user"}], "open": 1}, "kpiItemId": "62e87b36-eb9b-4daa-a89b-a6e22e7975be", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "3283802", "version": 0, "superiorScoreWeight": 100, "superiorScoreFlag": "true", "companyId": {"id": "1207671"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "19346074", "taskId": "2148414", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "*********"}, "managerLevel": "", "kpiItemName": "沟通表达能力\n（分值5分）", "otherReqField": {"inputFinishValue": {"isReq": 1, "desc": "完成值必填"}, "attachment": {"isReq": 0, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "itemTargetValueText": "", "inputRole": [], "itemWeight": 5, "scoringRule": "", "kpiItemId": "62e87b36-eb9b-4daa-a89b-a6e22e7975be", "maxExtraScore": 0, "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "*********", "kpiTypeName": "工作态度", "kpiTypeId": "19346074", "isNewEmp": 0, "formulaFields": [{"updatedTime": 1740995743000, "kpiItemId": "62e87b36-eb9b-4daa-a89b-a6e22e7975be", "formulaFieldValue": 0, "companyFieldId": "0498c45f-7dc5-4e01-826e-99a7abb81e84", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "完成值", "createdTime": 1740995743000, "id": "17429012", "taskId": "2148414", "createdUser": "*********"}, {"updatedTime": 1740995743000, "kpiItemId": "62e87b36-eb9b-4daa-a89b-a6e22e7975be", "formulaFieldValue": 0, "companyFieldId": "26870015-d7f6-4339-8aec-7afc6540af36", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "目标值", "createdTime": 1740995743000, "id": "17429013", "taskId": "2148414", "createdUser": "*********"}], "fieldValueList": [{"updatedTime": 1733824214000, "kpiItemId": "62e87b36-eb9b-4daa-a89b-a6e22e7975be", "fieldName": "评分标准及依据", "isReq": 0, "type": 1, "fieldValue": "5分：沟通和协调能力极强，能够高效解决跨部门合作中的复杂问题。 \n4分：沟通和协调能力较强，能够有效解决跨部门合作中的问题。 \n3分：沟通和协调能力一般，能够处理跨部门合作中的常规问题。 \n2分：沟通和协调能力有待提高，解决跨部门合作中的问题存在困难。 \n1分：沟通和协调能力差，无法有效解决跨部门合作中的问题。", "companyId": "", "fieldStatus": "valid", "isDeleted": "false", "createdTime": 1740995743000, "id": "5be6f1cb-7522-4e95-a5b3-55671c3021e7", "fieldId": "5be6f1cb-7522-4e95-a5b3-55671c3021e7"}], "taskId": "2148414", "waitScores": []}, {"alreadyNodes": [], "itemRule": "1、维护品牌和公司形象，理解和遵守公司的各项规章制度，如考勤/员工行为规划/人事行政制度流程等。\n2、服从部门领导安排，充分理解自己的岗位职责及义务，不回避责任，做到事事有回应，件件有着落。", "reserveOkrWeight": 5, "showFinishBar": 0, "itemUnit": "%", "multipleReviewersType": "or", "taskUserId": "3283802", "itemScoreValue": "{\"type\":\"autoDefine\",\"value\":{\"from\":0,\"to\":5,\"step\":0.5}}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "", "formulaType": 1, "finishValueType": 1, "createdTime": 1740995743000, "id": "19346078", "createdUser": "*********", "scorerType": "exam", "order": 3, "itemFormula": "", "updatedTime": 1740995743000, "thresholdJson": "[]", "typeOrder": 2, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "text", "version": 0, "companyId": {"id": "1207671"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 1, "itemTargetValue": 0, "itemFieldJson": "[]", "formulaCondition": "[]", "empId": "*********", "itemType": "non-measurable", "itemFinishValueText": "始终维护品牌和公司形象，严格遵守公司规章制度，服从领导安排，充分理解岗位职责，不回避责任，事事有回应，件件有着落。", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueAuditStatus": 0, "itemScoreRule": {"superRater": {"nodeWeight": 100, "superiorScoreOrder": "inTurn", "nodeVacancyFlag": 1, "rateMode": "item", "signatureFlag": false, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "node": "superior_score", "raters": [{"empId": "*********", "type": 0, "empName": "张志慧", "status": "wait"}], "multiType": "or", "approverType": "user"}], "open": 1}, "kpiItemId": "c0324461-8f1a-4fe1-84c6-281cf4841c65", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "3283802", "version": 0, "superiorScoreWeight": 100, "superiorScoreFlag": "true", "companyId": {"id": "1207671"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "19346074", "taskId": "2148414", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "*********"}, "managerLevel": "", "kpiItemName": "行为价值观\n（分值5分）", "otherReqField": {"inputFinishValue": {"isReq": 1, "desc": "完成值必填"}, "attachment": {"isReq": 0, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "itemTargetValueText": "", "inputRole": [], "itemWeight": 5, "scoringRule": "", "kpiItemId": "c0324461-8f1a-4fe1-84c6-281cf4841c65", "maxExtraScore": 0, "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "*********", "kpiTypeName": "工作态度", "kpiTypeId": "19346074", "isNewEmp": 0, "formulaFields": [], "fieldValueList": [{"updatedTime": 1720175117000, "kpiItemId": "c0324461-8f1a-4fe1-84c6-281cf4841c65", "fieldName": "评分标准及依据", "isReq": 0, "type": 1, "fieldValue": "5分：始终维护品牌和公司形象，严格遵守公司规章制度，服从领导安排，充分理解岗位职责，不回避责任，事事有回应，件件有着落。 \n4分：通常维护品牌和公司形象，较好遵守公司规章制度，服从领导安排，较好理解岗位职责，偶尔回避责任，大多数事情有回应，大多数件有着落。\n 3分：偶尔维护品牌和公司形象，一般遵守公司规章制度，一般服从领导安排，一般理解岗位职责，有时回避责任，一些事情没有回应，一些件没有着落。 \n2分：很少维护品牌和公司形象，很少遵守公司规章制度，很少服从领导安排，很少理解岗位职责，经常回避责任，很多事情没有回应，很多件没有着落。 \n1分：从不维护品牌和公司形象，从不遵守公司规章制度，从不服从领导安排，从不理解岗位职责，总是回避责任，所有事情没有回应，所有件没有着落。", "companyId": "", "fieldStatus": "valid", "isDeleted": "false", "createdTime": 1740995743000, "id": "5be6f1cb-7522-4e95-a5b3-55671c3021e7", "fieldId": "5be6f1cb-7522-4e95-a5b3-55671c3021e7"}], "taskId": "2148414", "waitScores": []}], "itemLimitCnt": {"max": "", "openItemLimit": "false", "min": ""}, "alreadyScores": [], "waitScores": []}], "avatar": "", "reviewersJson": [{"empId": "*********", "avatar": "https://static-legacy.dingtalk.com/media/lQDPD2y0zQJ72tPNAo7NAhyw2tOClXSG7m8G0oJhwi7qAA_540_654.jpg", "exUserId": "*********", "jobnumber": "", "empName": "胡雨欣", "status": "on_the_job"}, {"empId": "*********", "avatar": "", "exUserId": "*********", "jobnumber": "", "empName": "王雪洋", "status": "on_the_job"}], "version": 6, "hasAskEndScore": false, "mutualEvalComputeType": 1, "companyId": {"id": "1207671"}, "resetRaterNameIds": [], "ruleConfStatus": 200, "inputFinishStatus": 2, "atOrgPathHight": 2, "scoreEndFlag": false, "isNewEmp": 0, "opEmpExistsItemNotSubmitFinishValue": false, "kpis": [{"alreadyNodes": [], "itemRule": "当月达人“小圆圆不圆”账号，实际成交8单及以上（不包括刷单及退货，成单数以实际确收为准）", "reserveOkrWeight": 0, "showFinishBar": 1, "itemFinishValue": 10, "itemUnit": "", "multipleReviewersType": "or", "taskUserId": "3283802", "itemScoreValue": "{\"type\":\"autoDefine\",\"value\":{\"from\":0,\"to\":20,\"step\":0.1}}", "showTargetValue": "", "formulaType": 1, "finishValueType": 1, "createdTime": 1740995743000, "id": "19346069", "createdUser": "*********", "scorerType": "exam", "order": 0, "itemFormula": "", "updatedTime": 1740995743000, "thresholdJson": "[]", "typeOrder": 0, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "num", "version": 0, "companyId": {"id": "1207671"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 1, "itemTargetValue": 8, "itemFieldJson": "[{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"isSystemField\":\"false\",\"fieldId\":\"finishValue\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"isSystemField\":\"false\",\"fieldId\":\"targetValue\",\"label\":\"目标值\",\"value\":0,\"yVal\":true}]", "formulaCondition": "[{\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"完成值\",\"formulaFieldValue\":0,\"id\":\"16218940\",\"kpiItemId\":\"d35a8a38-b020-48d5-9c43-58a3b360d049\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"},{\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"目标值\",\"formulaFieldValue\":0,\"id\":\"16218941\",\"kpiItemId\":\"d35a8a38-b020-48d5-9c43-58a3b360d049\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"}]", "empId": "*********", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueAuditStatus": 0, "itemScoreRule": {"superRater": {"nodeWeight": 100, "superiorScoreOrder": "inTurn", "nodeVacancyFlag": 1, "rateMode": "item", "signatureFlag": false, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "node": "superior_score", "raters": [{"empId": "*********", "type": 0, "empName": "张志慧", "status": "wait"}], "multiType": "or", "approverType": "user"}], "open": 1}, "kpiItemId": "d35a8a38-b020-48d5-9c43-58a3b360d049", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "3283802", "version": 0, "superiorScoreWeight": 100, "superiorScoreFlag": "true", "companyId": {"id": "1207671"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "19346068", "taskId": "2148414", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "*********"}, "managerLevel": "", "kpiItemName": "实际成单数", "otherReqField": {"inputFinishValue": {"isReq": 1, "desc": "完成值必填"}, "attachment": {"isReq": 1, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "itemTargetValueText": "", "inputRole": [], "itemWeight": 20, "scoringRule": "Y≥100%\t                   30分\n80%＜Y＜100%      21~29分\n60%＜Y≤80%   \t11~20分\nY≤60%\t                 0~19分", "kpiItemId": "d35a8a38-b020-48d5-9c43-58a3b360d049", "maxExtraScore": 0, "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "*********", "kpiTypeName": "关键绩效考核指标", "kpiTypeId": "19346068", "isNewEmp": 0, "formulaFields": [{"updatedTime": 1740995743000, "kpiItemId": "d35a8a38-b020-48d5-9c43-58a3b360d049", "formulaFieldValue": 0, "companyFieldId": "0498c45f-7dc5-4e01-826e-99a7abb81e84", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "完成值", "createdTime": 1740995743000, "id": "17428800", "taskId": "2148414", "createdUser": "*********"}, {"updatedTime": 1740995743000, "kpiItemId": "d35a8a38-b020-48d5-9c43-58a3b360d049", "formulaFieldValue": 0, "companyFieldId": "26870015-d7f6-4339-8aec-7afc6540af36", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "目标值", "createdTime": 1740995743000, "id": "17429001", "taskId": "2148414", "createdUser": "*********"}], "fieldValueList": [{"updatedTime": 1739001953000, "kpiItemId": "d35a8a38-b020-48d5-9c43-58a3b360d049", "fieldName": "评分标准及依据", "isReq": 0, "type": 1, "fieldValue": "\"Y≥100% 20分\n90%≤Y＜100% 10~19分\n80%≤Y＜90% 1~9分\nY＜80% 0分\"", "companyId": "", "fieldStatus": "valid", "isDeleted": "false", "createdTime": 1740995743000, "id": "5be6f1cb-7522-4e95-a5b3-55671c3021e7", "fieldId": "5be6f1cb-7522-4e95-a5b3-55671c3021e7"}], "taskId": "2148414", "waitScores": []}, {"alreadyNodes": [], "itemRule": "培训达人抖音矩阵号的使用者", "reserveOkrWeight": 0, "showFinishBar": 1, "itemFinishValue": 10, "itemUnit": "", "multipleReviewersType": "or", "taskUserId": "3283802", "itemScoreValue": "{\"type\":\"autoDefine\",\"value\":{\"from\":0,\"to\":10,\"step\":0.1}}", "showTargetValue": "", "formulaType": 1, "finishValueType": 1, "createdTime": 1740995743000, "id": "19346070", "createdUser": "*********", "scorerType": "exam", "order": 1, "itemFormula": "", "updatedTime": 1740995743000, "thresholdJson": "[]", "typeOrder": 0, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "num", "version": 0, "companyId": {"id": "1207671"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 1, "itemTargetValue": 10, "itemFieldJson": "[{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"isSystemField\":\"false\",\"fieldId\":\"finishValue\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"isSystemField\":\"false\",\"fieldId\":\"targetValue\",\"label\":\"目标值\",\"value\":0,\"yVal\":true}]", "formulaCondition": "[{\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"完成值\",\"formulaFieldValue\":0,\"id\":\"16218942\",\"kpiItemId\":\"a527ffd9-53b7-4306-b0cb-902e9d4b2e96\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"},{\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"目标值\",\"formulaFieldValue\":0,\"id\":\"16218943\",\"kpiItemId\":\"a527ffd9-53b7-4306-b0cb-902e9d4b2e96\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"}]", "empId": "*********", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueAuditStatus": 0, "itemScoreRule": {"superRater": {"nodeWeight": 100, "superiorScoreOrder": "inTurn", "nodeVacancyFlag": 1, "rateMode": "item", "signatureFlag": false, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "node": "superior_score", "raters": [{"empId": "*********", "type": 0, "empName": "张志慧", "status": "wait"}], "multiType": "or", "approverType": "user"}], "open": 1}, "kpiItemId": "a527ffd9-53b7-4306-b0cb-902e9d4b2e96", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "3283802", "version": 0, "superiorScoreWeight": 100, "superiorScoreFlag": "true", "companyId": {"id": "1207671"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "19346068", "taskId": "2148414", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "*********"}, "managerLevel": "", "kpiItemName": "达人抖音矩阵号使用者培训", "otherReqField": {"inputFinishValue": {"isReq": 1, "desc": "完成值必填"}, "attachment": {"isReq": 1, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "itemTargetValueText": "", "inputRole": [], "itemWeight": 10, "scoringRule": "", "kpiItemId": "a527ffd9-53b7-4306-b0cb-902e9d4b2e96", "maxExtraScore": 0, "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "*********", "kpiTypeName": "关键绩效考核指标", "kpiTypeId": "19346068", "isNewEmp": 0, "formulaFields": [{"updatedTime": 1740995743000, "kpiItemId": "a527ffd9-53b7-4306-b0cb-902e9d4b2e96", "formulaFieldValue": 0, "companyFieldId": "0498c45f-7dc5-4e01-826e-99a7abb81e84", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "完成值", "createdTime": 1740995743000, "id": "17429002", "taskId": "2148414", "createdUser": "*********"}, {"updatedTime": 1740995743000, "kpiItemId": "a527ffd9-53b7-4306-b0cb-902e9d4b2e96", "formulaFieldValue": 0, "companyFieldId": "26870015-d7f6-4339-8aec-7afc6540af36", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "目标值", "createdTime": 1740995743000, "id": "17429003", "taskId": "2148414", "createdUser": "*********"}], "fieldValueList": [{"updatedTime": 1739001953000, "kpiItemId": "a527ffd9-53b7-4306-b0cb-902e9d4b2e96", "fieldName": "评分标准及依据", "isReq": 0, "type": 1, "fieldValue": "10分：完成度100%，当月培训后能独立进行切片视频剪辑与发布；培训效果好，培训内容落地性强\n7分：完成度≥80%，当月培训后80%以上能独立进行切片视频剪辑与发布；培训效果较好，培训内容部分可优化\n4分：完成度≥60%，当月培训后60%以上能独立进行切片视频剪辑与发布；培训效果一般，培训内容需要结合实际情况优化\n0分：完成度＜60%，当月培训后60%以下人员能独立进行切片视频剪辑与发布；培训效果不合格，培训内容需要重新进行梳理", "companyId": "", "fieldStatus": "valid", "isDeleted": "false", "createdTime": 1740995743000, "id": "5be6f1cb-7522-4e95-a5b3-55671c3021e7", "fieldId": "5be6f1cb-7522-4e95-a5b3-55671c3021e7"}], "taskId": "2148414", "waitScores": []}, {"alreadyNodes": [], "itemRule": "1、当月达人账号发布75条及以上有效短视频（不包含审核未通过或违规视频）\n2、每日更新3条以上\"", "reserveOkrWeight": 0, "showFinishBar": 1, "itemFinishValue": 64, "itemUnit": "", "multipleReviewersType": "or", "taskUserId": "3283802", "itemScoreValue": "{\"type\":\"autoDefine\",\"value\":{\"from\":0,\"to\":30,\"step\":0.1}}", "showTargetValue": "", "formulaType": 1, "finishValueType": 1, "createdTime": 1740995743000, "id": "19346071", "createdUser": "*********", "scorerType": "exam", "order": 2, "itemFormula": "", "updatedTime": 1740995743000, "thresholdJson": "[]", "typeOrder": 0, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "num", "version": 0, "companyId": {"id": "1207671"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 1, "itemTargetValue": 75, "itemFieldJson": "[{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"isSystemField\":\"false\",\"fieldId\":\"finishValue\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"isSystemField\":\"false\",\"fieldId\":\"targetValue\",\"label\":\"目标值\",\"value\":0,\"yVal\":true}]", "formulaCondition": "[{\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"完成值\",\"formulaFieldValue\":0,\"id\":\"16218944\",\"kpiItemId\":\"dd881440-3077-4a32-a9a3-867408f031ef\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"},{\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"目标值\",\"formulaFieldValue\":0,\"id\":\"16218945\",\"kpiItemId\":\"dd881440-3077-4a32-a9a3-867408f031ef\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"}]", "empId": "*********", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueAuditStatus": 0, "itemScoreRule": {"superRater": {"nodeWeight": 100, "superiorScoreOrder": "inTurn", "nodeVacancyFlag": 1, "rateMode": "item", "signatureFlag": false, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "node": "superior_score", "raters": [{"empId": "*********", "type": 0, "empName": "张志慧", "status": "wait"}], "multiType": "or", "approverType": "user"}], "open": 1}, "kpiItemId": "dd881440-3077-4a32-a9a3-867408f031ef", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "3283802", "version": 0, "superiorScoreWeight": 100, "superiorScoreFlag": "true", "companyId": {"id": "1207671"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "19346068", "taskId": "2148414", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "*********"}, "managerLevel": "", "kpiItemName": "达人抖音矩阵号运营", "otherReqField": {"inputFinishValue": {"isReq": 1, "desc": "完成值必填"}, "attachment": {"isReq": 1, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "itemTargetValueText": "", "inputRole": [], "itemWeight": 30, "scoringRule": "Y≥100%        20分 \n80%＜Y＜100%   15分 \n60%＜Y≤80% 10分 \nY≤60% 5分", "kpiItemId": "dd881440-3077-4a32-a9a3-867408f031ef", "maxExtraScore": 0, "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "*********", "kpiTypeName": "关键绩效考核指标", "kpiTypeId": "19346068", "isNewEmp": 0, "formulaFields": [{"updatedTime": 1740995743000, "kpiItemId": "dd881440-3077-4a32-a9a3-867408f031ef", "formulaFieldValue": 0, "companyFieldId": "0498c45f-7dc5-4e01-826e-99a7abb81e84", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "完成值", "createdTime": 1740995743000, "id": "17429004", "taskId": "2148414", "createdUser": "*********"}, {"updatedTime": 1740995743000, "kpiItemId": "dd881440-3077-4a32-a9a3-867408f031ef", "formulaFieldValue": 0, "companyFieldId": "26870015-d7f6-4339-8aec-7afc6540af36", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "目标值", "createdTime": 1740995743000, "id": "17429005", "taskId": "2148414", "createdUser": "*********"}], "fieldValueList": [{"updatedTime": 1739001953000, "kpiItemId": "dd881440-3077-4a32-a9a3-867408f031ef", "fieldName": "评分标准及依据", "isReq": 0, "type": 1, "fieldValue": "每日更新发布3条，月更新发布75条以上短视频：\n10分：当月视频发布总数量≥  75条；\n1-9分：75条＞当月视频发布总数量≥50条\n0分：当月视频发布总数量＜50条", "companyId": "", "fieldStatus": "valid", "isDeleted": "false", "createdTime": 1740995743000, "id": "5be6f1cb-7522-4e95-a5b3-55671c3021e7", "fieldId": "5be6f1cb-7522-4e95-a5b3-55671c3021e7"}], "taskId": "2148414", "waitScores": []}, {"alreadyNodes": [], "itemRule": "1、培训矩阵号视频剪辑与运营\n2、梳理培训过程sop", "reserveOkrWeight": 0, "showFinishBar": 0, "itemUnit": "", "multipleReviewersType": "or", "taskUserId": "3283802", "itemScoreValue": "{\"type\":\"autoDefine\",\"value\":{\"from\":0,\"to\":20,\"step\":0.5}}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "", "formulaType": 1, "finishValueType": 1, "createdTime": 1740995743000, "id": "19346073", "createdUser": "*********", "scorerType": "exam", "order": 0, "itemFormula": "", "updatedTime": 1740995743000, "thresholdJson": "[]", "typeOrder": 1, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "num", "version": 0, "companyId": {"id": "1207671"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 0, "itemTargetValue": 0, "itemFieldJson": "[{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"isSystemField\":\"false\",\"fieldId\":\"finishValue\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"isSystemField\":\"false\",\"fieldId\":\"targetValue\",\"label\":\"目标值\",\"value\":0,\"yVal\":true}]", "formulaCondition": "[{\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"完成值\",\"formulaFieldValue\":0,\"id\":\"16218946\",\"kpiItemId\":\"942224f9-e946-4950-958a-ff3fb9442fc3\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"},{\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"目标值\",\"formulaFieldValue\":0,\"id\":\"16218947\",\"kpiItemId\":\"942224f9-e946-4950-958a-ff3fb9442fc3\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"}]", "empId": "*********", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueAuditStatus": 0, "itemScoreRule": {"superRater": {"nodeWeight": 100, "superiorScoreOrder": "inTurn", "nodeVacancyFlag": 1, "rateMode": "item", "signatureFlag": false, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "node": "superior_score", "raters": [{"empId": "*********", "type": 0, "empName": "张志慧", "status": "wait"}], "multiType": "or", "approverType": "user"}], "open": 1}, "kpiItemId": "942224f9-e946-4950-958a-ff3fb9442fc3", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "3283802", "version": 0, "superiorScoreWeight": 100, "superiorScoreFlag": "true", "companyId": {"id": "1207671"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "19346072", "taskId": "2148414", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "*********"}, "managerLevel": "", "kpiItemName": "矩阵号使用者培训", "otherReqField": {"inputFinishValue": {"isReq": 0, "desc": "完成值必填"}, "attachment": {"isReq": 0, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "itemTargetValueText": "", "inputRole": [], "itemWeight": 20, "scoringRule": "", "kpiItemId": "942224f9-e946-4950-958a-ff3fb9442fc3", "maxExtraScore": 0, "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "*********", "kpiTypeName": "非量化考核", "kpiTypeId": "19346072", "isNewEmp": 0, "formulaFields": [{"updatedTime": 1740995743000, "kpiItemId": "942224f9-e946-4950-958a-ff3fb9442fc3", "formulaFieldValue": 0, "companyFieldId": "0498c45f-7dc5-4e01-826e-99a7abb81e84", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "完成值", "createdTime": 1740995743000, "id": "17429006", "taskId": "2148414", "createdUser": "*********"}, {"updatedTime": 1740995743000, "kpiItemId": "942224f9-e946-4950-958a-ff3fb9442fc3", "formulaFieldValue": 0, "companyFieldId": "26870015-d7f6-4339-8aec-7afc6540af36", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "目标值", "createdTime": 1740995743000, "id": "17429007", "taskId": "2148414", "createdUser": "*********"}], "fieldValueList": [{"updatedTime": 1735954895000, "kpiItemId": "942224f9-e946-4950-958a-ff3fb9442fc3", "fieldName": "评分标准及依据", "isReq": 0, "type": 1, "fieldValue": "20分：制定的培训计划详细、完善、流畅、成本低，培训结果超于预期目标，并能够根据培训情况梳理详细的sop，落地性强\n15分：制定的培训计划完善、流畅、成本一般，培训结果符合预期目标，并能够根据培训情况梳理具体的sop，落地性较强\n10分：制定的培训计划一般、流程问题较多、成本较高，培训结果低于预期目标，根据培训情况梳理的sop落地性较差\n5分：制定的培训计划不完善、问题多、成本高，培训结果符合远低于预期目标，不能够根据培训情况梳理具体的sop，落地性差", "companyId": "", "fieldStatus": "valid", "isDeleted": "false", "createdTime": 1740995743000, "id": "5be6f1cb-7522-4e95-a5b3-55671c3021e7", "fieldId": "5be6f1cb-7522-4e95-a5b3-55671c3021e7"}], "taskId": "2148414", "waitScores": []}, {"alreadyNodes": [], "itemRule": "1、有计划的开展工作，对工作内容/时间/数量/流程的安排及分配遵循合理性，有效性\n2、具备强大的行动力，能够迅速的将计划或制度实践，不拖延，不推诿，有实际结果。\n3、准时发送工作日报/周报/战报等相关数据报表，每少提交一次扣1分。", "reserveOkrWeight": 5, "showFinishBar": 0, "itemUnit": "%", "multipleReviewersType": "or", "taskUserId": "3283802", "itemScoreValue": "{\"type\":\"autoDefine\",\"value\":{\"from\":0,\"to\":5,\"step\":0.5}}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "", "formulaType": 1, "finishValueType": 1, "createdTime": 1740995743000, "id": "19346075", "createdUser": "*********", "scorerType": "exam", "order": 0, "itemFormula": "", "updatedTime": 1741000198000, "thresholdJson": "[]", "typeOrder": 2, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "text", "version": 0, "companyId": {"id": "1207671"}, "resultInputType": "user", "itemFullScoreCfg": "false", "mustResultInput": 1, "itemTargetValue": 0, "itemFieldJson": "[{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"isSystemField\":\"false\",\"fieldId\":\"finishValue\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"isSystemField\":\"false\",\"fieldId\":\"targetValue\",\"label\":\"目标值\",\"value\":0,\"yVal\":true}]", "formulaCondition": "[{\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"完成值\",\"formulaFieldValue\":0,\"id\":\"16218948\",\"kpiItemId\":\"396bffaa-c542-4827-9efd-579262724c4f\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"},{\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"目标值\",\"formulaFieldValue\":0,\"id\":\"16218949\",\"kpiItemId\":\"396bffaa-c542-4827-9efd-579262724c4f\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"}]", "empId": "*********", "itemType": "non-measurable", "itemFinishValueText": "日报应提交20次，实际提交17次", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueAuditStatus": 0, "itemScoreRule": {"superRater": {"nodeWeight": 100, "superiorScoreOrder": "inTurn", "nodeVacancyFlag": 1, "rateMode": "item", "signatureFlag": false, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "node": "superior_score", "raters": [{"empId": "*********", "type": 0, "empName": "张志慧", "status": "wait"}], "multiType": "or", "approverType": "user"}], "open": 1}, "kpiItemId": "396bffaa-c542-4827-9efd-579262724c4f", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "3283802", "version": 0, "superiorScoreWeight": 100, "superiorScoreFlag": "true", "companyId": {"id": "1207671"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "19346074", "taskId": "2148414", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "*********"}, "managerLevel": "", "kpiItemName": "计划性/执行能力\n（分值5分）", "otherReqField": {"inputFinishValue": {"isReq": 1, "desc": "完成值必填"}, "attachment": {"isReq": 0, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "itemTargetValueText": "", "inputRole": [], "itemWeight": 5, "scoringRule": "", "kpiItemId": "396bffaa-c542-4827-9efd-579262724c4f", "maxExtraScore": 0, "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "*********", "kpiTypeName": "工作态度", "kpiTypeId": "19346074", "isNewEmp": 0, "formulaFields": [{"updatedTime": 1740995743000, "kpiItemId": "396bffaa-c542-4827-9efd-579262724c4f", "formulaFieldValue": 0, "companyFieldId": "0498c45f-7dc5-4e01-826e-99a7abb81e84", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "完成值", "createdTime": 1740995743000, "id": "17429008", "taskId": "2148414", "createdUser": "*********"}, {"updatedTime": 1740995743000, "kpiItemId": "396bffaa-c542-4827-9efd-579262724c4f", "formulaFieldValue": 0, "companyFieldId": "26870015-d7f6-4339-8aec-7afc6540af36", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "目标值", "createdTime": 1740995743000, "id": "17429009", "taskId": "2148414", "createdUser": "*********"}], "fieldValueList": [{"updatedTime": 1735024790000, "kpiItemId": "396bffaa-c542-4827-9efd-579262724c4f", "fieldName": "评分标准及依据", "isReq": 0, "type": 1, "fieldValue": "5分：工作计划详尽，时间/数量/流程安排合理有效，行动力强，计划或制度执行迅速，无拖延，有显著实际结果。 \n4分：工作计划较为详尽，安排较为合理有效，行动力较强，计划或制度执行较为迅速，偶尔有拖延，实际结果良好。 \n3分：工作计划一般，安排基本合理，行动力一般，计划或制度执行一般，存在拖延，实际结果一般。 \n2分：工作计划不够详尽，安排不够合理，行动力弱，计划或制度执行缓慢，经常拖延，实际结果较差。 \n1分：工作计划混乱，安排不合理，行动力极弱，计划或制度执行严重滞后，严重拖延，实际结果极差。", "companyId": "", "fieldStatus": "valid", "isDeleted": "false", "createdTime": 1740995743000, "id": "5be6f1cb-7522-4e95-a5b3-55671c3021e7", "fieldId": "5be6f1cb-7522-4e95-a5b3-55671c3021e7"}], "taskId": "2148414", "waitScores": []}, {"alreadyNodes": [], "itemRule": "1、对新知识，新政策，新趋势的敏锐洞察力，能够及时关注达人及视频切片领域的动态变化\n2、对发布的视频内容进行组织诊断，对现有问题和潜在风险进行完善和规避，明确公司业务需求，确保切片稳定运行", "reserveOkrWeight": 5, "showFinishBar": 0, "itemUnit": "%", "multipleReviewersType": "or", "taskUserId": "3283802", "itemScoreValue": "{\"type\":\"autoDefine\",\"value\":{\"from\":0,\"to\":5,\"step\":0.5}}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "", "formulaType": 1, "finishValueType": 1, "createdTime": 1740995743000, "id": "19346076", "createdUser": "*********", "scorerType": "exam", "order": 1, "itemFormula": "", "updatedTime": 1740995743000, "thresholdJson": "[]", "typeOrder": 2, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "text", "version": 0, "companyId": {"id": "1207671"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 1, "itemTargetValue": 0, "itemFieldJson": "[{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"isSystemField\":\"false\",\"fieldId\":\"finishValue\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"isSystemField\":\"false\",\"fieldId\":\"targetValue\",\"label\":\"目标值\",\"value\":0,\"yVal\":true}]", "formulaCondition": "[{\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"完成值\",\"formulaFieldValue\":0,\"id\":\"16218950\",\"kpiItemId\":\"a824d730-5f3f-401b-b094-ac2c5b074b75\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"},{\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"目标值\",\"formulaFieldValue\":0,\"id\":\"16218951\",\"kpiItemId\":\"a824d730-5f3f-401b-b094-ac2c5b074b75\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"}]", "empId": "*********", "itemType": "non-measurable", "itemFinishValueText": "对新知识、新政策、新趋势有较强洞察力，能较快关注并适应公司内部动态变化，面对挑战能找到解决方案，实践经验较为丰富。", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueAuditStatus": 0, "itemScoreRule": {"superRater": {"nodeWeight": 100, "superiorScoreOrder": "inTurn", "nodeVacancyFlag": 1, "rateMode": "item", "signatureFlag": false, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "node": "superior_score", "raters": [{"empId": "*********", "type": 0, "empName": "张志慧", "status": "wait"}], "multiType": "or", "approverType": "user"}], "open": 1}, "kpiItemId": "a824d730-5f3f-401b-b094-ac2c5b074b75", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "3283802", "version": 0, "superiorScoreWeight": 100, "superiorScoreFlag": "true", "companyId": {"id": "1207671"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "19346074", "taskId": "2148414", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "*********"}, "managerLevel": "", "kpiItemName": "学习适应能力\n（分值5分）", "otherReqField": {"inputFinishValue": {"isReq": 1, "desc": "完成值必填"}, "attachment": {"isReq": 0, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "itemTargetValueText": "", "inputRole": [], "itemWeight": 5, "scoringRule": "", "kpiItemId": "a824d730-5f3f-401b-b094-ac2c5b074b75", "maxExtraScore": 0, "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "*********", "kpiTypeName": "工作态度", "kpiTypeId": "19346074", "isNewEmp": 0, "formulaFields": [{"updatedTime": 1740995743000, "kpiItemId": "a824d730-5f3f-401b-b094-ac2c5b074b75", "formulaFieldValue": 0, "companyFieldId": "0498c45f-7dc5-4e01-826e-99a7abb81e84", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "完成值", "createdTime": 1740995743000, "id": "17429010", "taskId": "2148414", "createdUser": "*********"}, {"updatedTime": 1740995743000, "kpiItemId": "a824d730-5f3f-401b-b094-ac2c5b074b75", "formulaFieldValue": 0, "companyFieldId": "26870015-d7f6-4339-8aec-7afc6540af36", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "目标值", "createdTime": 1740995743000, "id": "17429011", "taskId": "2148414", "createdUser": "*********"}], "fieldValueList": [{"updatedTime": 1733824214000, "kpiItemId": "a824d730-5f3f-401b-b094-ac2c5b074b75", "fieldName": "评分标准及依据", "isReq": 0, "type": 1, "fieldValue": "5分：对新知识、新政策、新趋势有极强洞察力，能迅速关注并适应公司内部动态变化，面对挑战能迅速找到解决方案，实践经验丰富。 \n4分：对新知识、新政策、新趋势有较强洞察力，能较快关注并适应公司内部动态变化，面对挑战能找到解决方案，实践经验较为丰富。 \n3分：对新知识、新政策、新趋势有一定洞察力，能一般关注并适应公司内部动态变化，面对挑战解决方案一般，实践经验一般。 \n2分：对新知识、新政策、新趋势洞察力较弱，关注并适应公司内部动态变化较慢，面对挑战解决方案较差，实践经验较少。 \n1分：对新知识、新政策、新趋势几乎没有洞察力，很难关注并适应公司内部动态变化，面对挑战难以找到解决方案，几乎没有实践经验。", "companyId": "", "fieldStatus": "valid", "isDeleted": "false", "createdTime": 1740995743000, "id": "5be6f1cb-7522-4e95-a5b3-55671c3021e7", "fieldId": "5be6f1cb-7522-4e95-a5b3-55671c3021e7"}], "taskId": "2148414", "waitScores": []}, {"alreadyNodes": [], "itemRule": "1、能够清晰，准确的用恰当的语言讲自己的想法，观点和信息有条理性的表达出来\n2、独立对接设计、运营，完成每日工作推进\n3、渠道人员应时需求，相关问题解答提问等\n4、策划端协作对接", "reserveOkrWeight": 5, "showFinishBar": 0, "itemUnit": "%", "multipleReviewersType": "or", "taskUserId": "3283802", "itemScoreValue": "{\"type\":\"autoDefine\",\"value\":{\"from\":0,\"to\":5,\"step\":0.5}}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "", "formulaType": 1, "finishValueType": 1, "createdTime": 1740995743000, "id": "19346077", "createdUser": "*********", "scorerType": "exam", "order": 2, "itemFormula": "", "updatedTime": 1740995743000, "thresholdJson": "[]", "typeOrder": 2, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "text", "version": 0, "companyId": {"id": "1207671"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 1, "itemTargetValue": 0, "itemFieldJson": "[{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"isSystemField\":\"false\",\"fieldId\":\"finishValue\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"companyId\":\"1207671\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"isSystemField\":\"false\",\"fieldId\":\"targetValue\",\"label\":\"目标值\",\"value\":0,\"yVal\":true}]", "formulaCondition": "[{\"companyFieldId\":\"0498c45f-7dc5-4e01-826e-99a7abb81e84\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"完成值\",\"formulaFieldValue\":0,\"id\":\"16218952\",\"kpiItemId\":\"62e87b36-eb9b-4daa-a89b-a6e22e7975be\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"},{\"companyFieldId\":\"26870015-d7f6-4339-8aec-7afc6540af36\",\"createdTime\":\"2025-02-08 16:22:31\",\"formulaFieldName\":\"目标值\",\"formulaFieldValue\":0,\"id\":\"16218953\",\"kpiItemId\":\"62e87b36-eb9b-4daa-a89b-a6e22e7975be\",\"taskId\":\"2148414\",\"taskUserId\":\"3283802\"}]", "empId": "*********", "itemType": "non-measurable", "itemFinishValueText": "沟通和协调能力较强，能够有效解决跨部门合作中的问题。", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueAuditStatus": 0, "itemScoreRule": {"superRater": {"nodeWeight": 100, "superiorScoreOrder": "inTurn", "nodeVacancyFlag": 1, "rateMode": "item", "signatureFlag": false, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "node": "superior_score", "raters": [{"empId": "*********", "type": 0, "empName": "张志慧", "status": "wait"}], "multiType": "or", "approverType": "user"}], "open": 1}, "kpiItemId": "62e87b36-eb9b-4daa-a89b-a6e22e7975be", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "3283802", "version": 0, "superiorScoreWeight": 100, "superiorScoreFlag": "true", "companyId": {"id": "1207671"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "19346074", "taskId": "2148414", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "*********"}, "managerLevel": "", "kpiItemName": "沟通表达能力\n（分值5分）", "otherReqField": {"inputFinishValue": {"isReq": 1, "desc": "完成值必填"}, "attachment": {"isReq": 0, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "itemTargetValueText": "", "inputRole": [], "itemWeight": 5, "scoringRule": "", "kpiItemId": "62e87b36-eb9b-4daa-a89b-a6e22e7975be", "maxExtraScore": 0, "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "*********", "kpiTypeName": "工作态度", "kpiTypeId": "19346074", "isNewEmp": 0, "formulaFields": [{"updatedTime": 1740995743000, "kpiItemId": "62e87b36-eb9b-4daa-a89b-a6e22e7975be", "formulaFieldValue": 0, "companyFieldId": "0498c45f-7dc5-4e01-826e-99a7abb81e84", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "完成值", "createdTime": 1740995743000, "id": "17429012", "taskId": "2148414", "createdUser": "*********"}, {"updatedTime": 1740995743000, "kpiItemId": "62e87b36-eb9b-4daa-a89b-a6e22e7975be", "formulaFieldValue": 0, "companyFieldId": "26870015-d7f6-4339-8aec-7afc6540af36", "taskUserId": "3283802", "updatedUser": "*********", "version": 0, "companyId": {"id": "1207671"}, "isDeleted": "false", "formulaFieldName": "目标值", "createdTime": 1740995743000, "id": "17429013", "taskId": "2148414", "createdUser": "*********"}], "fieldValueList": [{"updatedTime": 1733824214000, "kpiItemId": "62e87b36-eb9b-4daa-a89b-a6e22e7975be", "fieldName": "评分标准及依据", "isReq": 0, "type": 1, "fieldValue": "5分：沟通和协调能力极强，能够高效解决跨部门合作中的复杂问题。 \n4分：沟通和协调能力较强，能够有效解决跨部门合作中的问题。 \n3分：沟通和协调能力一般，能够处理跨部门合作中的常规问题。 \n2分：沟通和协调能力有待提高，解决跨部门合作中的问题存在困难。 \n1分：沟通和协调能力差，无法有效解决跨部门合作中的问题。", "companyId": "", "fieldStatus": "valid", "isDeleted": "false", "createdTime": 1740995743000, "id": "5be6f1cb-7522-4e95-a5b3-55671c3021e7", "fieldId": "5be6f1cb-7522-4e95-a5b3-55671c3021e7"}], "taskId": "2148414", "waitScores": []}, {"alreadyNodes": [], "itemRule": "1、维护品牌和公司形象，理解和遵守公司的各项规章制度，如考勤/员工行为规划/人事行政制度流程等。\n2、服从部门领导安排，充分理解自己的岗位职责及义务，不回避责任，做到事事有回应，件件有着落。", "reserveOkrWeight": 5, "showFinishBar": 0, "itemUnit": "%", "multipleReviewersType": "or", "taskUserId": "3283802", "itemScoreValue": "{\"type\":\"autoDefine\",\"value\":{\"from\":0,\"to\":5,\"step\":0.5}}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "", "formulaType": 1, "finishValueType": 1, "createdTime": 1740995743000, "id": "19346078", "createdUser": "*********", "scorerType": "exam", "order": 3, "itemFormula": "", "updatedTime": 1740995743000, "thresholdJson": "[]", "typeOrder": 2, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "text", "version": 0, "companyId": {"id": "1207671"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 1, "itemTargetValue": 0, "itemFieldJson": "[]", "formulaCondition": "[]", "empId": "*********", "itemType": "non-measurable", "itemFinishValueText": "始终维护品牌和公司形象，严格遵守公司规章制度，服从领导安排，充分理解岗位职责，不回避责任，事事有回应，件件有着落。", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueAuditStatus": 0, "itemScoreRule": {"superRater": {"nodeWeight": 100, "superiorScoreOrder": "inTurn", "nodeVacancyFlag": 1, "rateMode": "item", "signatureFlag": false, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "node": "superior_score", "raters": [{"empId": "*********", "type": 0, "empName": "张志慧", "status": "wait"}], "multiType": "or", "approverType": "user"}], "open": 1}, "kpiItemId": "c0324461-8f1a-4fe1-84c6-281cf4841c65", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "3283802", "version": 0, "superiorScoreWeight": 100, "superiorScoreFlag": "true", "companyId": {"id": "1207671"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "19346074", "taskId": "2148414", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "*********"}, "managerLevel": "", "kpiItemName": "行为价值观\n（分值5分）", "otherReqField": {"inputFinishValue": {"isReq": 1, "desc": "完成值必填"}, "attachment": {"isReq": 0, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "itemTargetValueText": "", "inputRole": [], "itemWeight": 5, "scoringRule": "", "kpiItemId": "c0324461-8f1a-4fe1-84c6-281cf4841c65", "maxExtraScore": 0, "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "*********", "kpiTypeName": "工作态度", "kpiTypeId": "19346074", "isNewEmp": 0, "formulaFields": [], "fieldValueList": [{"updatedTime": 1720175117000, "kpiItemId": "c0324461-8f1a-4fe1-84c6-281cf4841c65", "fieldName": "评分标准及依据", "isReq": 0, "type": 1, "fieldValue": "5分：始终维护品牌和公司形象，严格遵守公司规章制度，服从领导安排，充分理解岗位职责，不回避责任，事事有回应，件件有着落。 \n4分：通常维护品牌和公司形象，较好遵守公司规章制度，服从领导安排，较好理解岗位职责，偶尔回避责任，大多数事情有回应，大多数件有着落。\n 3分：偶尔维护品牌和公司形象，一般遵守公司规章制度，一般服从领导安排，一般理解岗位职责，有时回避责任，一些事情没有回应，一些件没有着落。 \n2分：很少维护品牌和公司形象，很少遵守公司规章制度，很少服从领导安排，很少理解岗位职责，经常回避责任，很多事情没有回应，很多件没有着落。 \n1分：从不维护品牌和公司形象，从不遵守公司规章制度，从不服从领导安排，从不理解岗位职责，总是回避责任，所有事情没有回应，所有件没有着落。", "companyId": "", "fieldStatus": "valid", "isDeleted": "false", "createdTime": 1740995743000, "id": "5be6f1cb-7522-4e95-a5b3-55671c3021e7", "fieldId": "5be6f1cb-7522-4e95-a5b3-55671c3021e7"}], "taskId": "2148414", "waitScores": []}], "taskId": "2148414", "infos": [], "empEvalRule": {"indexRaters": [{"inviteRaters": [], "node": "super", "raters": [{"empId": "*********", "type": 0, "empName": "张志慧", "status": "wait"}], "multiType": "or"}], "initiator": "*********", "editStatus": 12, "scoreConf": {"transferFlag": "true", "multiType": "or"}, "auditResult": {"transferFlag": "true", "vacancyApproveInfo": "", "commentReq": 1, "vacancyApproveType": "superior", "empRepeatSkip": 0, "notFindAdminBySupAdmin": 1, "collectSendNotify": 1, "nodeEmpVacancy": 1, "auditNodes": [{"approvalOrder": 1, "approverInfo": "*********", "approverName": "魏志磊", "node": "final_result_audit", "raters": [{"empId": "*********", "type": 0, "empName": "严兰兰", "status": "wait"}], "multiType": "or", "approverType": "user"}], "multiType": "or", "vacancyApproveName": "", "open": 1}, "finishValueAudit": {"empRepeatSkip": 0, "notFindAdminBySupAdmin": 1, "nodeEmpVacancy": 2, "auditNodes": [], "open": 0}, "s3AppointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "totalLevelRaters": [], "s3SuperRater": {"nodeWeight": 100, "superiorScoreOrder": "inTurn", "nodeVacancyFlag": 1, "rateMode": "item", "signatureFlag": false, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "node": "superior_score", "raters": [{"empId": "*********", "type": 0, "empName": "张志慧", "status": "wait"}], "multiType": "or", "approverType": "user"}], "open": 1}, "isDeleted": "false", "isChanged": false, "ruleDesc": "注：本附件是劳动合同的有效组成部分，与劳动合同是具有同等法律效力的。\n绩效考核须由本人确认当月目标及考核结果。当员工线上操作目标确认及考核结果签字流程，即视为员工本人已清楚阅读、理解并接受以上考核，不得由他人代签或伪造行为，一经发现，则视为严重违反公司规章制度且当月绩效分数为0，相关人员承担连带责任。具体绩效管理规则可查看：https://alidocs.dingtalk.com/i/nodes/pGBa2Lm8aerY75XniQjMOL0v8gN7R35y?utm_scene=team_space\n绩效得分N=100：卓越\t持续且大幅度超越既定目标及要求\n90≤绩效得分＜100：优秀\t表现优秀，始终对目标有持续性超越\n80≤绩效得分＜90：有待提升\t基本达到或符合目标及要求\n60≤绩效得分＜80：仅仅及格\t与目标及要求存在较大差距，需进入绩效改进计划\n60＞绩效得分：不胜任\t不胜任当前岗位，被列入优化名单或降薪降职", "ruleName": "达人事业部考核表--达人项目专员", "scoreSortConf": {"sortItems": [{"sort": 1, "type": 10, "name": "自评"}, {"sort": 2, "type": 20, "name": "同级评"}, {"sort": 3, "type": 30, "name": "下级评"}, {"sort": 4, "type": 40, "name": "上级评"}, {"sort": 5, "type": 50, "name": "指定评"}], "exeType": 0}, "createdTime": 1737682452000, "typeWeightConf": {"checkItemWeight": 1, "limit100Weight": 0, "itemWeightLimit": 100, "open": 0}, "enterScore": {"enterScoreEmpType": 1, "enterScoreMethod": "auto", "scoreStartRuleType": "after", "scoreStartRuleDay": 1}, "createdUser": "*********", "updatedTime": 1740995743000, "confirmTask": {"sendMsgConf": {"auditNodes": [{"approverInfo": "", "approverName": "被考核人", "raters": [{"empId": "*********", "type": 0, "empName": "王雪洋", "status": "wait"}], "approverType": "taskEmp"}], "open": 1}, "openConfirmLT": 0, "noChangeSkipFlag": "false", "empRepeatSkip": 0, "notFindAdminBySupAdmin": 1, "confirmLTDay": 1, "nodeEmpVacancy": 1, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "modifyFlag": "false", "approverInfo": "", "confirmAuditSign": "true", "approverName": "被考核人", "raters": [{"empId": "*********", "type": 0, "empName": "王雪洋", "status": "wait"}], "multiType": "or", "modifyItemDimension": "all", "approverType": "taskEmp"}], "modifyItemDimension": "all", "open": 1}, "s3SelfRater": {"signatureFlag": false, "open": 0}, "interviewConf": {"empRepeatSkip": 0, "notFindAdminBySupAdmin": 1, "nodeEmpVacancy": 2, "open": 0}, "appealConf": {"open": 0}, "deadLineConf": {"open": 0}, "scoreValueConf": {"baseScore": 0, "exceedFullScore": false, "scoreRangeType": "weightScore"}, "createTotalLevelType": 1, "updatedUser": "*********", "levelGroupId": "", "indicatorCnt": 8, "version": 0, "evaluateType": "simple", "commentReqConf": {"commentNodeSet": [{"index": 1, "scoreNodes": [], "commonItem": {"rules": [], "reqLevel": 2}, "scoreSummary": {"rules": [], "reqLevel": 3}, "plusOrSubItem": {"rules": [], "reqLevel": 1}}]}, "scoreView": {"mutualScoreAnonymous": "true", "mutualScoreViewRule": {"superior": "", "mutual": "", "appoint": "", "examinee": ""}, "superiorScoreAnonymous": "true", "appointScoreViewRule": {"superior": "", "mutual": "", "appoint": "", "examinee": ""}, "superiorScoreViewRule": {"superior": "score,attach", "mutual": "score,attach", "appoint": "score,attach", "examinee": "score,attach"}, "selfScoreViewRule": {"superior": "", "mutual": "", "appoint": "", "examinee": "score,attach"}, "appointScoreAnonymous": "true"}, "companyId": {"id": "1207671"}, "publishResult": {"toEmps": [{"objType": "emp"}], "type": "afterFinished", "scoreDetailPriv": {"scoreRemark": ["self_remark", "item_remark", "peer_remark", "sub_remark", "superior_remark", "appoint_remark"], "scoreType": ["self_score"]}, "opEmps": [], "toDetailEmps": [], "dimension": 15, "open": 1}, "s3SubRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "s3PeerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "confirmResult": {"auto": 1, "sign": 1, "autoDay": 5, "scoreDetailPriv": {"scoreRemark": ["self_remark", "item_remark", "peer_remark", "sub_remark", "superior_remark", "appoint_remark"], "scoreType": ["self_score"]}, "dimension": 15, "open": 1}, "editExeIndi": {"empRepeatSkip": 0, "notFindAdminBySupAdmin": 1, "nodeEmpVacancy": 2, "auditNodes": [], "open": 0}, "askIndexRaters": [], "commentConf": {"scoreSummarySwitch": -1, "commentFlag": "notRequired", "plusOrSubComment": 1}, "showResultType": 14, "empEvalId": "3283802"}}