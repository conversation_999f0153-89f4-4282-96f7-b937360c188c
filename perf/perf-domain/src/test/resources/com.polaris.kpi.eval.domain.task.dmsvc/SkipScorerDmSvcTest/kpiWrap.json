[{"alreadyNodes": [], "itemRule": "<PERSON><PERSON><PERSON>", "reserveOkrWeight": 80, "showFinishBar": 0, "itemUnit": "分", "multipleReviewersType": "or", "taskUserId": "1417202", "showTargetValue": "false", "formulaType": 1, "finishValueType": 1, "createdTime": 1749626698000, "id": "1532405", "v3FinalWeightItemScore": {"scoreTypes": []}, "createdUser": "1441002", "scorerType": "exam", "order": 1, "itemFormula": "", "updatedTime": 1749626698000, "thresholdJson": "[]", "typeOrder": 0, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "num", "version": 0, "companyId": {"id": "5a031297-1b38-48ae-bc82-375849835203"}, "resultInputType": "no", "itemFullScoreCfg": "false", "mustResultInput": 0, "itemTargetValue": 0, "itemFieldJson": "[]", "formulaCondition": "[]", "empId": "1328001", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueAuditStatus": 0, "itemScoreRule": {"superRater": {"superiorScoreOrder": "sameTime", "nodeVacancyFlag": 1, "signatureFlag": false, "auditNodes": [], "open": 0}, "kpiItemId": "0779d4e1-afe1-4c13-8c48-225ce580972d", "subScoreWeight": 100, "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "mutualScoreFlag": "true", "appointScoreFlag": "false", "taskUserId": "1417202", "version": 0, "superiorScoreFlag": "false", "companyId": {"id": "5a031297-1b38-48ae-bc82-375849835203"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "1532404", "subRater": {"transferFlag": "true", "nodeWeight": 100, "change": false, "rateMode": "item", "node": "sub_score", "excludeAllManager": false, "raters": [{"empId": "1302001", "weight": 100, "avatar": "https://static-legacy.dingtalk.com/media/lADPD26eO1kRFzrNAbDNAbA_432_432.jpg", "type": 0, "empName": "苏小秋", "status": "wait"}, {"empId": "1441002", "weight": 100, "avatar": "", "type": 0, "empName": "何小虎", "status": "wait"}, {"empId": "1440001", "weight": 100, "avatar": "https://static-legacy.dingtalk.com/media/lQDPM44nV6ru9hLNA3bNA3awXYS6-2FsIQQEL-yUFkCvAA_886_886.jpg", "type": 0, "empName": "徐小伟", "status": "wait"}], "signatureFlag": false, "multiType": "and", "approverType": "user", "scorerNumCof": {"action": "regular", "open": "false"}, "open": 1}}, "managerLevel": "", "kpiItemName": "责任心", "otherReqField": {"inputFinishValue": {"isReq": 0, "desc": "完成值必填"}, "attachment": {"isReq": 0, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}, "isDeleted": "false", "finalSubmitFinishValue": 0, "finishValueSource": 1, "kpiTypeWeight": 0, "inputRole": [], "itemWeight": 80, "scoringRule": "12", "kpiItemId": "0779d4e1-afe1-4c13-8c48-225ce580972d", "maxExtraScore": 0, "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "", "kpiTypeName": "默认考核维度", "kpiTypeId": "1532404", "isNewEmp": 0, "fieldValueList": [], "taskId": "1710901"}, {"alreadyNodes": [], "itemRule": "<PERSON><PERSON><PERSON>", "reserveOkrWeight": 80, "showFinishBar": 0, "itemUnit": "分", "multipleReviewersType": "or", "taskUserId": "1417202", "itemScoreValue": "{\"type\":\"toMainScore\"}", "showTargetValue": "", "formulaType": 1, "finishValueType": 1, "createdTime": 1749626698000, "id": "1532406", "v3FinalWeightItemScore": {"scoreTypes": []}, "createdUser": "1441002", "scorerType": "exam", "order": 0, "itemFormula": "", "updatedTime": 1749626698000, "thresholdJson": "[]", "typeOrder": 0, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "num", "version": 0, "companyId": {"id": "5a031297-1b38-48ae-bc82-375849835203"}, "resultInputType": "no", "itemFullScoreCfg": "false", "mustResultInput": 0, "itemTargetValue": 0, "itemFieldJson": "[{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"268b0a46-c2bd-493d-aff8-8318aa7b02a8\",\"isSystemField\":\"false\",\"fieldId\":\"finishValue\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"e0674594-186f-4aa7-817d-30a2c9cee161\",\"isSystemField\":\"false\",\"fieldId\":\"targetValue\",\"label\":\"目标值\",\"value\":0,\"yVal\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"4ca8ffeb-6239-43e7-b67f-0259ba826184\",\"isSystemField\":\"false\",\"fieldId\":\"finishValue\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"1ea75c43-ff00-4ffa-be07-58f44f231a81\",\"isSystemField\":\"false\",\"fieldId\":\"targetValue\",\"label\":\"目标值\",\"value\":0,\"yVal\":true}]", "formulaCondition": "[{\"companyFieldId\":\"268b0a46-c2bd-493d-aff8-8318aa7b02a8\",\"createdTime\":\"2025-06-11 15:22:45\",\"formulaFieldName\":\"完成值\",\"formulaFieldValue\":0,\"id\":\"3155801\",\"kpiItemId\":\"10f1472c-1084-4b39-be5b-f9a47e56613d\",\"taskId\":\"1710901\",\"taskUserId\":\"1417202\"},{\"companyFieldId\":\"e0674594-186f-4aa7-817d-30a2c9cee161\",\"createdTime\":\"2025-06-11 15:22:45\",\"formulaFieldName\":\"目标值\",\"formulaFieldValue\":0,\"id\":\"3155802\",\"kpiItemId\":\"10f1472c-1084-4b39-be5b-f9a47e56613d\",\"taskId\":\"1710901\",\"taskUserId\":\"1417202\"},{\"companyFieldId\":\"4ca8ffeb-6239-43e7-b67f-0259ba826184\",\"createdTime\":\"2025-06-11 15:22:45\",\"formulaFieldName\":\"完成值\",\"formulaFieldValue\":0,\"id\":\"3155803\",\"kpiItemId\":\"10f1472c-1084-4b39-be5b-f9a47e56613d\",\"taskId\":\"1710901\",\"taskUserId\":\"1417202\"},{\"companyFieldId\":\"1ea75c43-ff00-4ffa-be07-58f44f231a81\",\"createdTime\":\"2025-06-11 15:22:45\",\"formulaFieldName\":\"目标值\",\"formulaFieldValue\":0,\"id\":\"3155804\",\"kpiItemId\":\"10f1472c-1084-4b39-be5b-f9a47e56613d\",\"taskId\":\"1710901\",\"taskUserId\":\"1417202\"}]", "empId": "1328001", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "finishValueAuditStatus": 0, "itemScoreRule": {"superRater": {"superiorScoreOrder": "sameTime", "nodeVacancyFlag": 1, "signatureFlag": false, "auditNodes": [], "open": 0}, "kpiItemId": "10f1472c-1084-4b39-be5b-f9a47e56613d", "subScoreWeight": 100, "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "mutualScoreFlag": "true", "appointScoreFlag": "false", "taskUserId": "1417202", "version": 0, "superiorScoreFlag": "false", "companyId": {"id": "5a031297-1b38-48ae-bc82-375849835203"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "1532404", "subRater": {"transferFlag": "true", "nodeWeight": 100, "change": false, "rateMode": "item", "node": "sub_score", "excludeAllManager": false, "raters": [{"empId": "1302001", "weight": 100, "avatar": "https://static-legacy.dingtalk.com/media/lADPD26eO1kRFzrNAbDNAbA_432_432.jpg", "type": 0, "empName": "苏小秋", "status": "wait"}, {"empId": "1441002", "weight": 100, "avatar": "", "type": 0, "empName": "何小虎", "status": "wait"}, {"empId": "1440001", "weight": 100, "avatar": "https://static-legacy.dingtalk.com/media/lQDPM44nV6ru9hLNA3bNA3awXYS6-2FsIQQEL-yUFkCvAA_886_886.jpg", "type": 0, "empName": "徐小伟", "status": "wait"}], "signatureFlag": false, "multiType": "and", "approverType": "user", "scorerNumCof": {"action": "regular", "open": "false"}, "open": 1}}, "managerLevel": "", "kpiItemName": "工作任务完成效率", "otherReqField": {"inputFinishValue": {"isReq": 0, "desc": "完成值必填"}, "attachment": {"isReq": 0, "desc": "附件必填"}, "comment": {"isReq": 0, "desc": "备注必填"}}, "isDeleted": "false", "finalSubmitFinishValue": 0, "finishValueSource": 1, "kpiTypeWeight": 0, "itemTargetValueText": "", "inputRole": [], "itemWeight": 20, "scoringRule": "12", "kpiItemId": "10f1472c-1084-4b39-be5b-f9a47e56613d", "maxExtraScore": 0, "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "", "kpiTypeName": "默认考核维度", "kpiTypeId": "1532404", "isNewEmp": 0, "fieldValueList": [], "taskId": "1710901"}]