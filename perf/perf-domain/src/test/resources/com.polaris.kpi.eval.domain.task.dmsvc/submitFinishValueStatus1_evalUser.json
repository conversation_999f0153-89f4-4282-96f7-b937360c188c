{"empId": "14604374", "allScored": "true", "cycleId": "2134235", "stepId": "2152089", "refAsk360Flag": false, "orgId": "13579195", "scoreResults": [{"empId": "14604374", "updatedTime": 1725611249000, "approvalOrder": 3, "mergeRsInfos": [], "scoreWeight": 100, "modifyFlag": "true", "scorerId": "*********", "taskUserId": "2880379", "version": 0, "orgId": "", "taskAuditId": "15977267", "companyId": {"id": "1011631"}, "isDeleted": "false", "reviewersType": "and", "signatureFlag": false, "maySkip": false, "auditStatus": "pass", "createdTime": 1725611143000, "id": "2126f0d0-5ef3-4c90-b14c-4c395b235b76", "updateTypeLevel": false, "taskId": "2093977", "waitMergeWeight": 0, "createdUser": "*********", "scorerType": "modify_item_audit"}, {"empId": "14604374", "updatedTime": 1725611143000, "approvalOrder": 2, "mergeRsInfos": [], "scoreWeight": 100, "modifyFlag": "true", "scorerId": "2853238", "taskUserId": "2880379", "version": 0, "orgId": "", "taskAuditId": "15977266", "companyId": {"id": "1011631"}, "isDeleted": "false", "reviewersType": "and", "signatureFlag": false, "maySkip": false, "auditStatus": "pass", "createdTime": 1724753259000, "id": "3785436a-9f76-4366-9d95-97f9e514fa96", "updateTypeLevel": false, "taskId": "2093977", "waitMergeWeight": 0, "createdUser": "*********", "scorerType": "modify_item_audit"}, {"empId": "14604374", "updatedTime": 1724753259000, "approvalOrder": 1, "mergeRsInfos": [], "scoreWeight": 100, "modifyFlag": "true", "scorerId": "14604374", "taskUserId": "2880379", "version": 0, "orgId": "", "taskAuditId": "15977265", "companyId": {"id": "1011631"}, "isDeleted": "false", "reviewersType": "and", "signatureFlag": false, "maySkip": false, "auditStatus": "pass", "createdTime": 1724751400000, "id": "dabfb268-c930-4154-bdcd-02a206385e21", "updateTypeLevel": false, "taskId": "2093977", "waitMergeWeight": 0, "createdUser": "*********", "scorerType": "modify_item_audit"}], "allAutoCompute": false, "isAllAutoType": true, "atOrgCodePath": "|1211945|13579305|13579195|", "isDeleted": "false", "tempTask": 0, "confirmDeadLine": "", "empName": "李泊翰", "isVacancySkipRater": false, "createdTime": 1724751385000, "id": "2880379", "sendMsg": true, "taskStatus": "confirmed", "createdUser": "*********", "updatedTime": 1724751398000, "finalScore": 80.68, "empOrgName": "运营四部 抖音运营", "originalEvaluationLevel": "B:良好", "atOrgNamePath": "青岛多乐多美品牌管理有限公司|TOC业务中心|运营四部 抖音运营", "evaluationLevel": "B:良好", "kpiTypes": [{"reserveOkrWeight": 0, "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "scoreOptType": 2, "lockedItems": ["typeWeight", "addIndex", "deleteIndex"], "taskUserId": "2880379", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "des": "", "isDeleted": "false", "isRaterBack": false, "createdTime": 1724753259000, "kpiTypeWeight": 100, "createdUser": "14604374", "updatedTime": 1724753259000, "typeOrder": 0, "maxExtraScore": 0, "updatedUser": "14604374", "version": 0, "openOkrScore": 0, "isOkr": "false", "companyId": {"id": "1011631"}, "kpiTypeUsedFields": [{"updatedTime": 1719391898000, "adminType": 1, "show": 1, "sort": 1, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "指标名称", "kpiTypeId": "13552659", "createdTime": 1725611249000, "fieldId": "name", "req": 1, "status": "valid"}, {"updatedTime": 1719391898000, "adminType": 1, "show": 1, "sort": 2, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "考核标准", "kpiTypeId": "13552659", "createdTime": 1725611249000, "fieldId": "standard", "req": 0, "status": "valid"}, {"updatedTime": 1719391898000, "adminType": 1, "show": 1, "sort": 3, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "计分规则", "kpiTypeId": "13552659", "createdTime": 1725611249000, "fieldId": "scoreRule", "req": 0, "status": "valid"}, {"updatedTime": 1719391898000, "adminType": 1, "show": 1, "sort": 4, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "目标值", "kpiTypeId": "13552659", "createdTime": 1725611249000, "fieldId": "targetValue", "req": 1, "status": "valid"}, {"updatedTime": 1719391898000, "adminType": 1, "show": 1, "sort": 5, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "单位", "kpiTypeId": "13552659", "createdTime": 1725611249000, "fieldId": "unit", "req": 1, "status": "valid"}, {"updatedTime": 1719391898000, "adminType": 1, "show": 1, "sort": 6, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "指标权重", "kpiTypeId": "13552659", "createdTime": 1725611249000, "fieldId": "indexWeight", "req": 1, "status": "valid"}, {"updatedTime": 1719391898000, "adminType": 1, "show": 1, "sort": 7, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "评分分值", "kpiTypeId": "13552659", "createdTime": 1725611249000, "fieldId": "scoreValue", "req": 1, "status": "valid"}, {"updatedTime": 1719391898000, "adminType": 1, "show": 1, "sort": 8, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "完成值录入", "kpiTypeId": "13552659", "createdTime": 1725611249000, "fieldId": "finishValue", "req": 1, "status": "valid"}], "kpiTypeName": "经营（财务、业务）指标", "kpiTypeId": "13552659", "kpiTypeClassify": "custom", "items": [{"alreadyNodes": [], "itemRule": "达人佣金与推广费用均不可超过公司预算情况下，销售额完成率=总成交金额/目标销售额*100%\n\n路径：财务部-财务助理-《运营四部月度财务报表》\n录入人：被考核人", "examineOperType": "update", "showFinishBar": 1, "itemFinishValue": 46.31, "itemUnit": "%", "taskUserId": "2880379", "itemScoreValue": "{\"type\":\"toMainScore\"}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "false", "formulaType": 1, "finishValueType": 1, "createdTime": 1724753259000, "id": "14112819", "createdUser": "14604374", "scorerType": "auto", "order": 0, "itemFormula": "((((完成值).toFixed(12)*1>=(90).toFixed(12)*1))&&('100'))||((((完成值).toFixed(12)*1>=(50).toFixed(12)*1))&&('70+(完成值-50)*30/40'))||((((完成值).toFixed(12)*1>=(40).toFixed(12)*1))&&('60+(完成值-40)'))||(((1))&&('60'))", "updatedTime": 1724753259000, "thresholdJson": "[{\"id\":0,\"cursorPosition\":0,\"calculationType\":1,\"inputLabelArr\":[{\"label\":1,\"value\":1},{\"label\":0,\"value\":0},{\"label\":0,\"value\":0}],\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"keyboardShow\":false,\"conditionRelation\":\"&&\",\"formulaIsError\":false,\"errMsg\":\"\",\"filterList\":[{\"head\":\"完成值\",\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":\"90\"}]},{\"id\":2,\"cursorPosition\":13,\"calculationType\":1,\"inputLabelArr\":[{\"label\":7,\"value\":7},{\"label\":0,\"value\":0},{\"label\":\"+\",\"value\":\"+\",\"symbol\":true},{\"label\":\"(\",\"value\":\"(\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"1adb2dd1-70f9-4454-8b3e-79efa469cea1\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"label\":\"-\",\"value\":\"-\",\"symbol\":true},{\"label\":5,\"value\":5},{\"label\":0,\"value\":0},{\"label\":\")\",\"value\":\")\",\"symbol\":true},{\"label\":\"×\",\"value\":\"*\",\"symbol\":true},{\"label\":3,\"value\":3},{\"label\":0,\"value\":0},{\"label\":\"÷\",\"value\":\"/\",\"symbol\":true},{\"label\":4,\"value\":4},{\"label\":0,\"value\":0}],\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"keyboardShow\":false,\"conditionRelation\":\"&&\",\"formulaIsError\":false,\"errMsg\":\"\",\"filterList\":[{\"head\":\"完成值\",\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":\"50\"}]},{\"id\":3,\"cursorPosition\":4,\"calculationType\":1,\"inputLabelArr\":[{\"label\":6,\"value\":6},{\"label\":0,\"value\":0},{\"label\":\"+\",\"value\":\"+\",\"symbol\":true},{\"label\":\"(\",\"value\":\"(\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"1adb2dd1-70f9-4454-8b3e-79efa469cea1\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"label\":\"-\",\"value\":\"-\",\"symbol\":true},{\"label\":4,\"value\":4},{\"label\":0,\"value\":0},{\"label\":\")\",\"value\":\")\",\"symbol\":true}],\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"keyboardShow\":false,\"conditionRelation\":\"&&\",\"formulaIsError\":false,\"errMsg\":\"\",\"filterList\":[{\"head\":\"完成值\",\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":\"40\"}]},{\"calculationType\":1,\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"inputLabelArr\":[{\"label\":6,\"value\":6},{\"label\":0,\"value\":0}],\"cursorPosition\":7,\"conditionRelation\":\"||\",\"keyboardShow\":false,\"errMsg\":\"\",\"filterList\":[{\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":0,\"head\":\"完成值\"}],\"id\":4}]", "typeOrder": 0, "inputFormat": "num", "updatedUser": "14604374", "version": 3, "companyId": {"id": "1011631"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 1, "itemTargetValue": 100, "itemFieldJson": "[]", "kpiTypeClassify": "custom", "formulaCondition": "[]", "empId": "14604374", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "itemScoreRule": {"superRater": {"superiorScoreOrder": "inTurn", "signatureFlag": false, "auditNodes": [], "open": 0}, "updatedTime": 1724751387000, "kpiItemId": "7e64f647-f2af-4763-abfd-07fe4eda39cb", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "2880379", "updatedUser": "*********", "version": 0, "superiorScoreFlag": "false", "companyId": {"id": "1011631"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "13552659", "createdTime": 1724751387000, "id": "9483360", "taskId": "2093977", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "14604374"}, "managerLevel": "", "kpiItemName": "销售额达成率（运营四部达播）", "itemAutoScore": 26.524, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 100, "inputRole": [], "itemWeight": 40, "scoringRule": "D:X≤40%：60分\nC:40%<X≤50%：每高1个百分点在60分基础上+1分\nB:50%<X≤90%：每高1个百分点在70分基础上+0.75分\nA:90%<X：100分", "kpiItemId": "7e64f647-f2af-4763-abfd-07fe4eda39cb", "maxExtraScore": 0, "appointAudits": [], "isTypeLocked": "[\"typeWeight\",\"addIndex\",\"deleteIndex\"]", "openOkrScore": 0, "resultInputEmpId": "14604374", "kpiTypeName": "经营（财务、业务）指标", "kpiTypeId": "13552659", "isNewEmp": 0, "formulaFields": [], "taskId": "2093977", "waitScores": []}, {"alreadyNodes": [], "itemRule": "若7日总ROI≥月度目标值，销售额完成率=当月总实际销售额/当月目标销售额*100%\n若7总ROI＜月度目标值，销售额完成率=0\n\n路径：运营四部-主管-《巨量千川数据》\n录入人：被考核人", "examineOperType": "update", "showFinishBar": 1, "itemFinishValue": 100, "itemUnit": "%", "taskUserId": "2880379", "itemScoreValue": "{\"type\":\"toMainScore\"}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "false", "formulaType": 1, "finishValueType": 1, "createdTime": 1724753259000, "id": "14112820", "createdUser": "14604374", "scorerType": "auto", "order": 1, "itemFormula": "((((完成值).toFixed(12)*1>=(90).toFixed(12)*1))&&('100'))||((((完成值).toFixed(12)*1>=(50).toFixed(12)*1))&&('70+(完成值-50)*30/40'))||((((完成值).toFixed(12)*1>=(40).toFixed(12)*1))&&('60+(完成值-40)'))||(((1))&&('60'))", "updatedTime": 1724753259000, "thresholdJson": "[{\"id\":0,\"cursorPosition\":0,\"calculationType\":1,\"inputLabelArr\":[{\"label\":1,\"value\":1},{\"label\":0,\"value\":0},{\"label\":0,\"value\":0}],\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"keyboardShow\":false,\"conditionRelation\":\"&&\",\"formulaIsError\":false,\"errMsg\":\"\",\"filterList\":[{\"head\":\"完成值\",\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":\"90\"}]},{\"id\":2,\"cursorPosition\":13,\"calculationType\":1,\"inputLabelArr\":[{\"label\":7,\"value\":7},{\"label\":0,\"value\":0},{\"label\":\"+\",\"value\":\"+\",\"symbol\":true},{\"label\":\"(\",\"value\":\"(\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"1adb2dd1-70f9-4454-8b3e-79efa469cea1\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"label\":\"-\",\"value\":\"-\",\"symbol\":true},{\"label\":5,\"value\":5},{\"label\":0,\"value\":0},{\"label\":\")\",\"value\":\")\",\"symbol\":true},{\"label\":\"×\",\"value\":\"*\",\"symbol\":true},{\"label\":3,\"value\":3},{\"label\":0,\"value\":0},{\"label\":\"÷\",\"value\":\"/\",\"symbol\":true},{\"label\":4,\"value\":4},{\"label\":0,\"value\":0}],\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"keyboardShow\":false,\"conditionRelation\":\"&&\",\"formulaIsError\":false,\"errMsg\":\"\",\"filterList\":[{\"head\":\"完成值\",\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":\"50\"}]},{\"id\":3,\"cursorPosition\":4,\"calculationType\":1,\"inputLabelArr\":[{\"label\":6,\"value\":6},{\"label\":0,\"value\":0},{\"label\":\"+\",\"value\":\"+\",\"symbol\":true},{\"label\":\"(\",\"value\":\"(\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"1adb2dd1-70f9-4454-8b3e-79efa469cea1\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"label\":\"-\",\"value\":\"-\",\"symbol\":true},{\"label\":4,\"value\":4},{\"label\":0,\"value\":0},{\"label\":\")\",\"value\":\")\",\"symbol\":true}],\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"keyboardShow\":false,\"conditionRelation\":\"&&\",\"formulaIsError\":false,\"errMsg\":\"\",\"filterList\":[{\"head\":\"完成值\",\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":\"40\"}]},{\"calculationType\":1,\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"inputLabelArr\":[{\"label\":6,\"value\":6},{\"label\":0,\"value\":0}],\"cursorPosition\":7,\"conditionRelation\":\"||\",\"keyboardShow\":false,\"errMsg\":\"\",\"filterList\":[{\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":0,\"head\":\"完成值\"}],\"id\":4}]", "typeOrder": 0, "inputFormat": "num", "updatedUser": "14604374", "version": 3, "companyId": {"id": "1011631"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 1, "itemTargetValue": 100, "itemFieldJson": "[]", "kpiTypeClassify": "custom", "formulaCondition": "[]", "empId": "14604374", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "itemScoreRule": {"superRater": {"superiorScoreOrder": "inTurn", "signatureFlag": false, "auditNodes": [], "open": 0}, "updatedTime": 1724751387000, "kpiItemId": "45f3286a-af69-413e-bebe-aea5f3085355", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "2880379", "updatedUser": "*********", "version": 0, "superiorScoreFlag": "false", "companyId": {"id": "1011631"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "13552659", "createdTime": 1724751387000, "id": "9483361", "taskId": "2093977", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "14604374"}, "managerLevel": "", "kpiItemName": "销售额达成率（运营四部）", "itemAutoScore": 30, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 100, "inputRole": [], "itemWeight": 30, "scoringRule": "D:X≤40%：60分\nC:40%<X≤50%：每高1个百分点在60分基础上+1分\nB:50%<X≤90%：每高1个百分点在70分基础上+0.75分\nA:90%<X：100分", "kpiItemId": "45f3286a-af69-413e-bebe-aea5f3085355", "maxExtraScore": 0, "appointAudits": [], "isTypeLocked": "[\"typeWeight\",\"addIndex\",\"deleteIndex\"]", "openOkrScore": 0, "resultInputEmpId": "14604374", "kpiTypeName": "经营（财务、业务）指标", "kpiTypeId": "13552659", "isNewEmp": 0, "formulaFields": [], "taskId": "2093977", "waitScores": []}, {"alreadyNodes": [], "itemRule": "有效开新数量：达人首单30天内销售额≥100元。\n有效开新达成率=有效开新数量/目标开新数量。\n\n路径：财务部-财务助理-《运营四部月度财务报表》\n录入人：被考核人", "examineOperType": "update", "showFinishBar": 1, "itemFinishValue": 85.42, "itemUnit": "%", "taskUserId": "2880379", "itemScoreValue": "{\"type\":\"toMainScore\"}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "false", "formulaType": 1, "finishValueType": 1, "createdTime": 1724753259000, "id": "14112821", "createdUser": "14604374", "scorerType": "auto", "order": 2, "itemFormula": "((((完成值).toFixed(12)*1>=(90).toFixed(12)*1))&&('100'))||((((完成值).toFixed(12)*1>=(50).toFixed(12)*1))&&('70+(完成值-50)*30/40'))||((((完成值).toFixed(12)*1>=(40).toFixed(12)*1))&&('60+(完成值-40)'))||(((1))&&('60'))", "updatedTime": 1724753259000, "thresholdJson": "[{\"id\":0,\"cursorPosition\":0,\"calculationType\":1,\"inputLabelArr\":[{\"label\":1,\"value\":1},{\"label\":0,\"value\":0},{\"label\":0,\"value\":0}],\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"keyboardShow\":false,\"conditionRelation\":\"&&\",\"formulaIsError\":false,\"errMsg\":\"\",\"filterList\":[{\"head\":\"完成值\",\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":\"90\"}]},{\"id\":2,\"cursorPosition\":13,\"calculationType\":1,\"inputLabelArr\":[{\"label\":7,\"value\":7},{\"label\":0,\"value\":0},{\"label\":\"+\",\"value\":\"+\",\"symbol\":true},{\"label\":\"(\",\"value\":\"(\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"1adb2dd1-70f9-4454-8b3e-79efa469cea1\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"label\":\"-\",\"value\":\"-\",\"symbol\":true},{\"label\":5,\"value\":5},{\"label\":0,\"value\":0},{\"label\":\")\",\"value\":\")\",\"symbol\":true},{\"label\":\"×\",\"value\":\"*\",\"symbol\":true},{\"label\":3,\"value\":3},{\"label\":0,\"value\":0},{\"label\":\"÷\",\"value\":\"/\",\"symbol\":true},{\"label\":4,\"value\":4},{\"label\":0,\"value\":0}],\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"keyboardShow\":false,\"conditionRelation\":\"&&\",\"formulaIsError\":false,\"errMsg\":\"\",\"filterList\":[{\"head\":\"完成值\",\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":\"50\"}]},{\"id\":3,\"cursorPosition\":4,\"calculationType\":1,\"inputLabelArr\":[{\"label\":6,\"value\":6},{\"label\":0,\"value\":0},{\"label\":\"+\",\"value\":\"+\",\"symbol\":true},{\"label\":\"(\",\"value\":\"(\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"1adb2dd1-70f9-4454-8b3e-79efa469cea1\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"label\":\"-\",\"value\":\"-\",\"symbol\":true},{\"label\":4,\"value\":4},{\"label\":0,\"value\":0},{\"label\":\")\",\"value\":\")\",\"symbol\":true}],\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"keyboardShow\":false,\"conditionRelation\":\"&&\",\"formulaIsError\":false,\"errMsg\":\"\",\"filterList\":[{\"head\":\"完成值\",\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":\"40\"}]},{\"calculationType\":1,\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"inputLabelArr\":[{\"label\":6,\"value\":6},{\"label\":0,\"value\":0}],\"cursorPosition\":7,\"conditionRelation\":\"||\",\"keyboardShow\":false,\"errMsg\":\"\",\"filterList\":[{\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":0,\"head\":\"完成值\"}],\"id\":4}]", "typeOrder": 0, "inputFormat": "num", "updatedUser": "14604374", "version": 3, "companyId": {"id": "1011631"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 1, "itemTargetValue": 100, "itemFieldJson": "[]", "kpiTypeClassify": "custom", "formulaCondition": "[]", "empId": "14604374", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "itemScoreRule": {"superRater": {"superiorScoreOrder": "inTurn", "signatureFlag": false, "auditNodes": [], "open": 0}, "updatedTime": 1724751387000, "kpiItemId": "1a3b5df1-ce39-4bac-90e9-21946ab20422", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "2880379", "updatedUser": "*********", "version": 0, "superiorScoreFlag": "false", "companyId": {"id": "1011631"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "13552659", "createdTime": 1724751387000, "id": "9483362", "taskId": "2093977", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "14604374"}, "managerLevel": "", "kpiItemName": "有效开新达成率（运营四部）", "itemAutoScore": 9.6565, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 100, "inputRole": [], "itemWeight": 10, "scoringRule": "D:X≤40%：60分\nC:40%<X≤50%：每高1个百分点在60分基础上+1分\nB:50%<X≤90%：每高1个百分点在70分基础上+0.75分\nA:90%<X：100分", "kpiItemId": "1a3b5df1-ce39-4bac-90e9-21946ab20422", "maxExtraScore": 0, "appointAudits": [], "isTypeLocked": "[\"typeWeight\",\"addIndex\",\"deleteIndex\"]", "openOkrScore": 0, "resultInputEmpId": "14604374", "kpiTypeName": "经营（财务、业务）指标", "kpiTypeId": "13552659", "isNewEmp": 0, "formulaFields": [], "taskId": "2093977", "waitScores": []}, {"alreadyNodes": [], "itemRule": "有效视频数量：产生消耗的视频数量；本项得分=有消耗的数量/目标数量*权重*100分\n\n路径：运营四部-主管-《巨量千川数据》\n录入人：被考核人", "examineOperType": "update", "showFinishBar": 1, "itemFinishValue": 95.67, "itemUnit": "%", "taskUserId": "2880379", "itemScoreValue": "{\"type\":\"toMainScore\"}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "false", "formulaType": 1, "finishValueType": 1, "createdTime": 1724753259000, "id": "14112822", "createdUser": "14604374", "scorerType": "auto", "order": 3, "itemFormula": "((((完成值).toFixed(12)*1>=(90).toFixed(12)*1))&&('100'))||((((完成值).toFixed(12)*1>=(50).toFixed(12)*1))&&('70+(完成值-50)*30/40'))||((((完成值).toFixed(12)*1>=(40).toFixed(12)*1))&&('60+(完成值-40)'))||(((1))&&('60'))", "updatedTime": 1724753259000, "thresholdJson": "[{\"id\":0,\"cursorPosition\":0,\"calculationType\":1,\"inputLabelArr\":[{\"label\":1,\"value\":1},{\"label\":0,\"value\":0},{\"label\":0,\"value\":0}],\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"keyboardShow\":false,\"conditionRelation\":\"&&\",\"formulaIsError\":false,\"errMsg\":\"\",\"filterList\":[{\"head\":\"完成值\",\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":\"90\"}]},{\"id\":2,\"cursorPosition\":13,\"calculationType\":1,\"inputLabelArr\":[{\"label\":7,\"value\":7},{\"label\":0,\"value\":0},{\"label\":\"+\",\"value\":\"+\",\"symbol\":true},{\"label\":\"(\",\"value\":\"(\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"1adb2dd1-70f9-4454-8b3e-79efa469cea1\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"label\":\"-\",\"value\":\"-\",\"symbol\":true},{\"label\":5,\"value\":5},{\"label\":0,\"value\":0},{\"label\":\")\",\"value\":\")\",\"symbol\":true},{\"label\":\"×\",\"value\":\"*\",\"symbol\":true},{\"label\":3,\"value\":3},{\"label\":0,\"value\":0},{\"label\":\"÷\",\"value\":\"/\",\"symbol\":true},{\"label\":4,\"value\":4},{\"label\":0,\"value\":0}],\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"keyboardShow\":false,\"conditionRelation\":\"&&\",\"formulaIsError\":false,\"errMsg\":\"\",\"filterList\":[{\"head\":\"完成值\",\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":\"50\"}]},{\"id\":3,\"cursorPosition\":4,\"calculationType\":1,\"inputLabelArr\":[{\"label\":6,\"value\":6},{\"label\":0,\"value\":0},{\"label\":\"+\",\"value\":\"+\",\"symbol\":true},{\"label\":\"(\",\"value\":\"(\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"1adb2dd1-70f9-4454-8b3e-79efa469cea1\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"label\":\"-\",\"value\":\"-\",\"symbol\":true},{\"label\":4,\"value\":4},{\"label\":0,\"value\":0},{\"label\":\")\",\"value\":\")\",\"symbol\":true}],\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"keyboardShow\":false,\"conditionRelation\":\"&&\",\"formulaIsError\":false,\"errMsg\":\"\",\"filterList\":[{\"head\":\"完成值\",\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":\"40\"}]},{\"calculationType\":1,\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"inputLabelArr\":[{\"label\":6,\"value\":6},{\"label\":0,\"value\":0}],\"cursorPosition\":7,\"conditionRelation\":\"||\",\"keyboardShow\":false,\"errMsg\":\"\",\"filterList\":[{\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":0,\"head\":\"完成值\"}],\"id\":4}]", "typeOrder": 0, "inputFormat": "num", "updatedUser": "14604374", "version": 3, "companyId": {"id": "1011631"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 1, "itemTargetValue": 100, "itemFieldJson": "[]", "kpiTypeClassify": "custom", "formulaCondition": "[]", "empId": "14604374", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "itemScoreRule": {"superRater": {"superiorScoreOrder": "inTurn", "signatureFlag": false, "auditNodes": [], "open": 0}, "updatedTime": 1724751387000, "kpiItemId": "2afffc24-6aab-46a3-a6ea-fe0eaa3ed5da", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "2880379", "updatedUser": "*********", "version": 0, "superiorScoreFlag": "false", "companyId": {"id": "1011631"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "13552659", "createdTime": 1724751387000, "id": "9483363", "taskId": "2093977", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "14604374"}, "managerLevel": "", "kpiItemName": "有效视频数量（运营四部）", "itemAutoScore": 10, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 100, "inputRole": [], "itemWeight": 10, "scoringRule": "D:X≤40%：60分\nC:40%<X≤50%：每高1个百分点在60分基础上+1分\nB:50%<X≤90%：每高1个百分点在70分基础上+0.75分\nA:90%<X：100分", "kpiItemId": "2afffc24-6aab-46a3-a6ea-fe0eaa3ed5da", "maxExtraScore": 0, "appointAudits": [], "isTypeLocked": "[\"typeWeight\",\"addIndex\",\"deleteIndex\"]", "openOkrScore": 0, "resultInputEmpId": "14604374", "kpiTypeName": "经营（财务、业务）指标", "kpiTypeId": "13552659", "isNewEmp": 0, "formulaFields": [], "taskId": "2093977", "waitScores": []}, {"alreadyNodes": [], "itemRule": "考核期内，ERP系统9号仓、批发仓中超40天（天数=可发库存数量/近30天销量）SKU个数为0\n\n路径：财务部-主管-《部门绩效（库存周转）-2024年》\n录入人：被考核人", "examineOperType": "update", "showFinishBar": 1, "itemFinishValue": 11, "itemUnit": "个", "taskUserId": "2880379", "itemScoreValue": "{\"type\":\"toMainScore\"}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "false", "formulaType": 2, "finishValueType": 1, "createdTime": 1724753259000, "id": "14112823", "createdUser": "14604374", "scorerType": "auto", "order": 4, "itemFormula": "((((100+目标值-完成值*5)<0))&&('0')) || (((1))&&('100+目标值-完成值*5'))", "updatedTime": 1724753259000, "thresholdJson": "[{\"label\":\"如果\",\"value\":\"if\",\"symbol\":true},{\"label\":\"(\",\"value\":\"(\",\"symbol\":true},{\"label\":\"(\",\"value\":\"(\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"指标得分\",\"companyFieldId\":\"score\",\"isSystemField\":\"false\",\"label\":\"指标得分\",\"value\":0,\"yVal\":true},{\"label\":\"=\",\"value\":\"=\",\"symbol\":true},{\"label\":1,\"value\":1},{\"label\":0,\"value\":0},{\"label\":0,\"value\":0},{\"label\":\"+\",\"value\":\"+\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e9\",\"isSystemField\":\"false\",\"label\":\"目标值\",\"value\":0,\"yVal\":true},{\"label\":\"-\",\"value\":\"-\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"1adb2dd1-70f9-4454-8b3e-79efa469cea1\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"label\":\"x\",\"value\":\"*\",\"symbol\":true},{\"label\":5,\"value\":5},{\"label\":\")\",\"value\":\")\",\"symbol\":true},{\"label\":\"<\",\"value\":\"<\",\"symbol\":true},{\"label\":0,\"value\":0},{\"label\":\")\",\"value\":\")\",\"symbol\":true},{\"label\":\"{\",\"value\":\"{\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"指标得分\",\"companyFieldId\":\"score\",\"isSystemField\":\"false\",\"label\":\"指标得分\",\"value\":0,\"yVal\":true},{\"label\":\"=\",\"value\":\"=\",\"symbol\":true},{\"label\":0,\"value\":0},{\"label\":\"}\",\"value\":\"}\",\"symbol\":true},{\"label\":\"br\",\"value\":\"br\"},{\"label\":\"否则\",\"value\":\"else\",\"symbol\":true},{\"label\":\"{\",\"value\":\"{\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"指标得分\",\"companyFieldId\":\"score\",\"isSystemField\":\"false\",\"label\":\"指标得分\",\"value\":0,\"yVal\":true},{\"label\":\"=\",\"value\":\"=\",\"symbol\":true},{\"label\":1,\"value\":1},{\"label\":0,\"value\":0},{\"label\":0,\"value\":0},{\"label\":\"+\",\"value\":\"+\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e9\",\"isSystemField\":\"false\",\"label\":\"目标值\",\"value\":0,\"yVal\":true},{\"label\":\"-\",\"value\":\"-\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"1adb2dd1-70f9-4454-8b3e-79efa469cea1\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"label\":\"x\",\"value\":\"*\",\"symbol\":true},{\"label\":5,\"value\":5},{\"label\":\"}\",\"value\":\"}\",\"symbol\":true}]", "typeOrder": 0, "inputFormat": "num", "updatedUser": "14604374", "version": 3, "companyId": {"id": "1011631"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 1, "itemTargetValue": 0, "itemFieldJson": "[]", "kpiTypeClassify": "custom", "formulaCondition": "[]", "empId": "14604374", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "itemScoreRule": {"superRater": {"superiorScoreOrder": "inTurn", "signatureFlag": false, "auditNodes": [], "open": 0}, "updatedTime": 1724751387000, "kpiItemId": "2c1816f0-079e-46d3-ad67-343443246c03", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "2880379", "updatedUser": "*********", "version": 0, "superiorScoreFlag": "false", "companyId": {"id": "1011631"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "13552659", "createdTime": 1724751387000, "id": "9483364", "taskId": "2093977", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "14604374"}, "managerLevel": "", "kpiItemName": "超时周转SKU数（9+批发仓）", "itemAutoScore": 4.5, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 100, "inputRole": [], "itemWeight": 10, "scoringRule": "-5分/个", "kpiItemId": "2c1816f0-079e-46d3-ad67-343443246c03", "maxExtraScore": 0, "appointAudits": [], "isTypeLocked": "[\"typeWeight\",\"addIndex\",\"deleteIndex\"]", "openOkrScore": 0, "resultInputEmpId": "14604374", "kpiTypeName": "经营（财务、业务）指标", "kpiTypeId": "13552659", "isNewEmp": 0, "formulaFields": [], "taskId": "2093977", "waitScores": []}], "itemLimitCnt": {"max": "", "openItemLimit": "false", "min": ""}, "alreadyScores": [], "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "waitScores": []}, {"reserveOkrWeight": 0, "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "scoreOptType": 2, "lockedItems": ["typeWeight", "addIndex", "deleteIndex"], "taskUserId": "2880379", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "des": "", "isDeleted": "false", "isRaterBack": false, "createdTime": 1724753259000, "kpiTypeWeight": 0, "createdUser": "14604374", "updatedTime": 1724753259000, "typeOrder": 1, "updatedUser": "14604374", "version": 0, "openOkrScore": 0, "isOkr": "false", "companyId": {"id": "1011631"}, "kpiTypeUsedFields": [{"updatedTime": 1719391898000, "adminType": 1, "show": 1, "sort": 1, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "指标名称", "kpiTypeId": "13552665", "createdTime": 1725611249000, "fieldId": "name", "req": 1, "status": "valid"}, {"updatedTime": 1719391898000, "adminType": 1, "show": 1, "sort": 2, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "考核标准", "kpiTypeId": "13552665", "createdTime": 1725611249000, "fieldId": "standard", "req": 0, "status": "valid"}, {"updatedTime": 1719391898000, "adminType": 1, "show": 1, "sort": 3, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "计分规则", "kpiTypeId": "13552665", "createdTime": 1725611249000, "fieldId": "scoreRule", "req": 0, "status": "valid"}, {"updatedTime": 1719391898000, "adminType": 1, "show": 1, "sort": 4, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "目标值", "kpiTypeId": "13552665", "createdTime": 1725611249000, "fieldId": "targetValue", "req": 1, "status": "valid"}, {"updatedTime": 1719391898000, "adminType": 1, "show": 1, "sort": 5, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "单位", "kpiTypeId": "13552665", "createdTime": 1725611249000, "fieldId": "unit", "req": 1, "status": "valid"}, {"updatedTime": 1719391898000, "adminType": 1, "show": 1, "sort": 6, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "减分上限", "kpiTypeId": "13552665", "createdTime": 1725611249000, "fieldId": "subtractLimit", "req": 1, "status": "valid"}, {"updatedTime": 1719391898000, "adminType": 1, "show": 1, "sort": 7, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "完成值录入", "kpiTypeId": "13552665", "createdTime": 1725611249000, "fieldId": "finishValue", "req": 1, "status": "valid"}], "kpiTypeName": "内外客户满意度", "kpiTypeId": "13552665", "kpiTypeClassify": "subtract", "items": [{"alreadyNodes": [], "itemRule": "1.外部投诉（三级投诉）：\n一级：外部客户投诉至工商等政府监管部门，经核实确因个人行为导致；\n二级：外部客户投诉至总经理处，经核实确因个人行为导致；\n三级：外部客户投诉至部门负责人处，经核实确因个人行为导致；\n（如给公司造成一定经济损失，根据公司制度标准执行）\n2.内部投诉（三级投诉）：\n因员工/部门间工作协作导致内部客户投诉，遵循先向协作部门主管（三级）进行投诉，未能解决问题再向总经办（二级）进行投诉，经协调后仍未解决的，可投诉至总经理（一级）进行最终处理。\n\n路径：总经办-专员-《内外投诉台账》\n录入人：上级负责人（部门负责人）", "examineOperType": "update", "showFinishBar": 1, "itemFinishValue": 0, "itemUnit": "次", "taskUserId": "2880379", "itemScoreValue": "{\"type\":\"toMainScore\"}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "", "formulaType": 1, "finishValueType": 1, "createdTime": 1724753259000, "id": "14112824", "createdUser": "14604374", "scorerType": "exam", "order": 0, "itemFormula": "", "updatedTime": 1724753259000, "thresholdJson": "[]", "typeOrder": 1, "inputFormat": "num", "updatedUser": "14604374", "version": 1, "companyId": {"id": "1011631"}, "resultInputType": "manager", "itemFullScoreCfg": "false", "mustResultInput": 0, "itemTargetValue": 0, "itemFieldJson": "[]", "kpiTypeClassify": "subtract", "formulaCondition": "[]", "empId": "14604374", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "itemScoreRule": {"superRater": {"nodeWeight": 100, "superiorScoreOrder": "sameTime", "nodeVacancyFlag": 1, "rateMode": "item", "signatureFlag": false, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "approverInfo": "1", "node": "superior_score", "raters": [{"empId": "2853238", "level": 1, "weight": 100, "type": 2, "empName": "张树才"}], "multiType": "and", "approverType": "manager"}], "open": 1}, "updatedTime": 1724751387000, "kpiItemId": "ad7ace04-ea1b-4611-b905-cd82c315441f", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "2880379", "updatedUser": "*********", "version": 0, "superiorScoreWeight": 100, "superiorScoreFlag": "true", "companyId": {"id": "1011631"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "13552665", "createdTime": 1724751387000, "id": "9483365", "taskId": "2093977", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "14604374"}, "managerLevel": "1", "kpiItemName": "内外投诉", "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "itemTargetValueText": "", "inputRole": [], "itemWeight": 100, "scoringRule": "此项扣分无上限。\n1.外部投诉（三级投诉）：\n三级：-5分/次，二级：-10分/次，一级：视严重程度扣减，无上限；\n2.内部投诉（三级投诉）：\n三级：-3分/次，二级：-5分/次，一级：-10分/次；", "kpiItemId": "ad7ace04-ea1b-4611-b905-cd82c315441f", "appointAudits": [], "isTypeLocked": "[\"typeWeight\",\"addIndex\",\"deleteIndex\"]", "openOkrScore": 0, "resultInputEmpId": "2853238", "kpiTypeName": "内外客户满意度", "kpiTypeId": "13552665", "isNewEmp": 0, "formulaFields": [], "taskId": "2093977", "waitScores": []}], "itemLimitCnt": {"max": "", "openItemLimit": "false", "min": ""}, "alreadyScores": [], "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "waitScores": []}, {"reserveOkrWeight": 0, "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "scoreOptType": 2, "lockedItems": ["typeWeight", "deleteIndex", "addIndex"], "taskUserId": "2880379", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "des": "", "isDeleted": "false", "isRaterBack": false, "createdTime": 1724753259000, "kpiTypeWeight": 0, "createdUser": "14604374", "updatedTime": 1724753259000, "typeOrder": 2, "maxExtraScore": 0, "plusSubInterval": {"max": "", "min": ""}, "updatedUser": "14604374", "version": 0, "openOkrScore": 0, "isOkr": "false", "companyId": {"id": "1011631"}, "kpiTypeUsedFields": [{"updatedTime": 1719391898000, "adminType": 1, "show": 1, "sort": 1, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "指标名称", "kpiTypeId": "13552667", "createdTime": 1725611249000, "fieldId": "name", "req": 1, "status": "valid"}, {"updatedTime": 1719391898000, "adminType": 1, "show": 1, "sort": 2, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "考核标准", "kpiTypeId": "13552667", "createdTime": 1725611249000, "fieldId": "standard", "req": 0, "status": "valid"}, {"updatedTime": 1719391898000, "adminType": 1, "show": 1, "sort": 3, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "计分规则", "kpiTypeId": "13552667", "createdTime": 1725611249000, "fieldId": "scoreRule", "req": 0, "status": "valid"}, {"updatedTime": 1719391898000, "adminType": 1, "show": 1, "sort": 4, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "目标值", "kpiTypeId": "13552667", "createdTime": 1725611249000, "fieldId": "targetValue", "req": 1, "status": "valid"}, {"updatedTime": 1719391898000, "adminType": 1, "show": 1, "sort": 5, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "单位", "kpiTypeId": "13552667", "createdTime": 1725611249000, "fieldId": "unit", "req": 1, "status": "valid"}, {"updatedTime": 1719391898000, "adminType": 1, "show": 1, "sort": 6, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "评分区间", "kpiTypeId": "13552667", "createdTime": 1725611249000, "fieldId": "scoreSection", "req": 1, "status": "valid"}, {"updatedTime": 1719391898000, "adminType": 1, "show": 1, "sort": 7, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "完成值录入", "kpiTypeId": "13552667", "createdTime": 1725611249000, "fieldId": "finishValue", "req": 1, "status": "valid"}], "kpiTypeName": "学习与发展", "kpiTypeId": "13552667", "kpiTypeClassify": "plusSub", "items": [{"alreadyNodes": [], "itemRule": "本项由部门负责人根据员工当月价值观、制度（行为规范、业务规范等各项制度）的表现，参考积分记录和奖惩记录进行评价。\n\n路径：悦积分\n录入人：上级负责人", "examineOperType": "update", "showFinishBar": 1, "itemFinishValue": -10, "itemUnit": "分", "taskUserId": "2880379", "itemScoreValue": "{\"type\":\"toMainScore\"}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "", "formulaType": 1, "finishValueType": 1, "createdTime": 1724753259000, "id": "14112825", "createdUser": "14604374", "scorerType": "exam", "order": 0, "itemFormula": "", "updatedTime": 1728455272000, "thresholdJson": "[]", "typeOrder": 2, "plusSubInterval": {"max": "5", "min": "-5"}, "inputFormat": "num", "updatedUser": "14604374", "version": 1, "companyId": {"id": "1011631"}, "resultInputType": "manager", "itemFullScoreCfg": "false", "mustResultInput": 0, "itemTargetValue": 0, "itemFieldJson": "[]", "kpiTypeClassify": "plusSub", "formulaCondition": "[]", "empId": "14604374", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "itemScoreRule": {"superRater": {"nodeWeight": 100, "superiorScoreOrder": "sameTime", "nodeVacancyFlag": 1, "rateMode": "item", "signatureFlag": false, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "approverInfo": "1", "node": "superior_score", "raters": [{"empId": "2853238", "level": 1, "weight": 100, "type": 2, "empName": "张树才"}], "multiType": "and", "approverType": "manager"}], "open": 1}, "updatedTime": 1724751387000, "kpiItemId": "bb9829b8-c810-4242-b5dc-9bdd5bf48fb0", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "2880379", "updatedUser": "*********", "version": 0, "superiorScoreWeight": 100, "superiorScoreFlag": "true", "companyId": {"id": "1011631"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "13552667", "createdTime": 1724751387000, "id": "9483366", "taskId": "2093977", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "14604374"}, "managerLevel": "1", "kpiItemName": "文化认同", "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "itemTargetValueText": "", "inputRole": [], "itemWeight": 100, "scoringRule": "最高±5分，本项如有录入分值，上级主管须对评分事项具体说明。", "kpiItemId": "bb9829b8-c810-4242-b5dc-9bdd5bf48fb0", "maxExtraScore": 0, "appointAudits": [], "isTypeLocked": "[\"typeWeight\",\"deleteIndex\",\"addIndex\"]", "openOkrScore": 0, "resultInputEmpId": "2853238", "kpiTypeName": "学习与发展", "kpiTypeId": "13552667", "isNewEmp": 0, "formulaFields": [], "taskId": "2093977", "waitScores": []}, {"alreadyNodes": [], "itemRule": "培训计划完成率=部门业务、技能类培训场次/目标培训场次*100%\n\n路径：总经办-专员-《多乐培训满意度调查》\n录入人：部门负责人（被考核人）", "examineOperType": "update", "showFinishBar": 1, "itemUnit": "%", "taskUserId": "2880379", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "false", "formulaType": 1, "finishValueType": 1, "createdTime": 1724753259000, "id": "14112827", "createdUser": "14604374", "scorerType": "exam", "order": 1, "itemFormula": "", "updatedTime": 1728443016000, "thresholdJson": "[]", "typeOrder": 2, "plusSubInterval": {"max": "0", "min": "-10"}, "inputFormat": "num", "updatedUser": "14604374", "version": 1, "companyId": {"id": "1011631"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 0, "itemTargetValue": 100, "itemFieldJson": "[]", "kpiTypeClassify": "plusSub", "formulaCondition": "[]", "empId": "14604374", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "itemScoreRule": {"superRater": {"nodeWeight": 100, "superiorScoreOrder": "sameTime", "nodeVacancyFlag": 1, "rateMode": "item", "signatureFlag": false, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "approverInfo": "1", "node": "superior_score", "raters": [{"empId": "2853238", "level": 1, "weight": 100, "type": 2, "empName": "张树才"}], "multiType": "and", "approverType": "manager"}], "open": 1}, "updatedTime": 1724751387000, "kpiItemId": "0a105550-2090-4fec-9754-08516c7adf15", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "2880379", "updatedUser": "*********", "version": 0, "superiorScoreWeight": 100, "superiorScoreFlag": "true", "companyId": {"id": "1011631"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "13552667", "createdTime": 1724751387000, "id": "9483367", "taskId": "2093977", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "14604374"}, "managerLevel": "", "kpiItemName": "培训计划完成率", "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "inputRole": [], "itemWeight": 0, "scoringRule": "扣除分值=10*(1-计划完成率）", "kpiItemId": "0a105550-2090-4fec-9754-08516c7adf15", "maxExtraScore": 0, "appointAudits": [], "isTypeLocked": "[\"typeWeight\",\"deleteIndex\",\"addIndex\"]", "openOkrScore": 0, "resultInputEmpId": "14604374", "kpiTypeName": "学习与发展", "kpiTypeId": "13552667", "isNewEmp": 0, "formulaFields": [], "taskId": "2093977", "waitScores": []}], "itemLimitCnt": {"max": "", "openItemLimit": "false", "min": ""}, "alreadyScores": [], "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "waitScores": []}, {"reserveOkrWeight": 0, "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "scoreOptType": 2, "lockedItems": ["typeWeight", "addIndex", "deleteIndex"], "taskUserId": "2880379", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "des": "", "isDeleted": "false", "isRaterBack": false, "createdTime": 1724753259000, "kpiTypeWeight": 0, "createdUser": "14604374", "updatedTime": 1724753259000, "typeOrder": 3, "updatedUser": "14604374", "version": 0, "openOkrScore": 0, "isOkr": "false", "companyId": {"id": "1011631"}, "kpiTypeUsedFields": [{"updatedTime": 1719453043000, "adminType": 1, "show": 1, "sort": 1, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "指标名称", "kpiTypeId": "13552670", "createdTime": 1725611249000, "fieldId": "name", "req": 1, "status": "valid"}, {"updatedTime": 1719453043000, "adminType": 1, "show": 1, "sort": 2, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "考核标准", "kpiTypeId": "13552670", "createdTime": 1725611249000, "fieldId": "standard", "req": 0, "status": "valid"}, {"updatedTime": 1719453043000, "adminType": 1, "show": 1, "sort": 3, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "计分规则", "kpiTypeId": "13552670", "createdTime": 1725611249000, "fieldId": "scoreRule", "req": 0, "status": "valid"}, {"updatedTime": 1719453043000, "adminType": 1, "show": 1, "sort": 4, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "目标值", "kpiTypeId": "13552670", "createdTime": 1725611249000, "fieldId": "targetValue", "req": 1, "status": "valid"}, {"updatedTime": 1719453043000, "adminType": 1, "show": 1, "sort": 5, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "单位", "kpiTypeId": "13552670", "createdTime": 1725611249000, "fieldId": "unit", "req": 1, "status": "valid"}, {"updatedTime": 1719453043000, "adminType": 1, "show": 1, "sort": 6, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "加分上限", "kpiTypeId": "13552670", "createdTime": 1725611249000, "fieldId": "plusLimit", "req": 1, "status": "valid"}, {"updatedTime": 1719453043000, "adminType": 1, "show": 1, "sort": 7, "taskUserId": "2880379", "type": 1, "companyId": "", "isDeleted": "false", "name": "完成值录入", "kpiTypeId": "13552670", "createdTime": 1725611249000, "fieldId": "finishValue", "req": 1, "status": "valid"}], "kpiTypeName": "内部流程建设", "kpiTypeId": "13552670", "kpiTypeClassify": "plus", "items": [{"alreadyNodes": [], "itemRule": "1.制度规范：部门负责人主动新建、修订、优化部门内部制度规范、操作手册等文件，当月完成发布并进行部门内宣贯及签字确认；\n2.业务流程：部门负责人针对部门内、部门交叉业务的流程进行优化梳理，经相关部门评估，切实能有效提高工作效率或降低成本。\n\n路径：总经办-专员-《公司制度流程台账》\n录入人：部门负责人（被考核人）", "examineOperType": "update", "showFinishBar": 1, "itemUnit": "项", "taskUserId": "2880379", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "false", "formulaType": 1, "finishValueType": 1, "createdTime": 1724753259000, "id": "14112829", "createdUser": "14604374", "scorerType": "exam", "order": 0, "itemFormula": "", "updatedTime": 1728443016000, "thresholdJson": "[]", "typeOrder": 3, "inputFormat": "num", "updatedUser": "14604374", "version": 1, "companyId": {"id": "1011631"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 0, "itemTargetValue": 0, "itemFieldJson": "[]", "kpiTypeClassify": "plus", "formulaCondition": "[]", "empId": "14604374", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "itemScoreRule": {"superRater": {"nodeWeight": 100, "superiorScoreOrder": "sameTime", "nodeVacancyFlag": 1, "rateMode": "item", "signatureFlag": false, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "approverInfo": "1", "node": "superior_score", "raters": [{"empId": "2853238", "level": 1, "weight": 100, "type": 2, "empName": "张树才"}], "multiType": "and", "approverType": "manager"}], "open": 1}, "updatedTime": 1724751387000, "kpiItemId": "4e995611-dc95-4169-b273-bfdbe2f47f38", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "2880379", "updatedUser": "*********", "version": 0, "superiorScoreWeight": 100, "superiorScoreFlag": "true", "companyId": {"id": "1011631"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "13552670", "createdTime": 1724751387000, "id": "9483368", "taskId": "2093977", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "14604374"}, "managerLevel": "", "kpiItemName": "制度流程体系建设", "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "inputRole": [], "itemWeight": 100, "scoringRule": "按照当月实际签发的制度或流程数量加分。\n制度或业务流程等进行版本重大调整（含新建），每项制度或流程加5分。进行部分条款调整的，每项制度或流程加2分。", "kpiItemId": "4e995611-dc95-4169-b273-bfdbe2f47f38", "appointAudits": [], "isTypeLocked": "[\"typeWeight\",\"addIndex\",\"deleteIndex\"]", "openOkrScore": 0, "resultInputEmpId": "14604374", "kpiTypeName": "内部流程建设", "kpiTypeId": "13552670", "isNewEmp": 0, "formulaFields": [], "taskId": "2093977", "waitScores": []}], "itemLimitCnt": {"max": "", "openItemLimit": "false", "min": ""}, "alreadyScores": [], "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "waitScores": []}], "finalItemAutoScore": 80.6805, "scoreRanges": [{"max": 200000, "fieldJson": "", "minAppendEqual": 1, "stepId": "2152088", "version": 0, "coeffType": 1, "scoreRuleId": "2152092", "min": 90, "isDeleted": "false", "stepName": "A:优秀", "createdTime": 1719158400000, "id": "16070169", "place": 2, "scoreRuleName": "系统默认"}, {"max": 90, "fieldJson": "", "minAppendEqual": 1, "stepId": "2152089", "version": 0, "coeffType": 1, "scoreRuleId": "2152092", "min": 70, "isDeleted": "false", "stepName": "B:良好", "createdTime": 1719158400000, "id": "16070170", "place": 2, "scoreRuleName": "系统默认"}, {"max": 70, "fieldJson": "", "minAppendEqual": 1, "stepId": "2152090", "version": 0, "coeffType": 1, "scoreRuleId": "2152092", "min": 60, "isDeleted": "false", "stepName": "C:待改进", "createdTime": 1719158400000, "id": "16070171", "place": 2, "scoreRuleName": "系统默认"}, {"max": 60, "fieldJson": "", "minAppendEqual": 1, "stepId": "2152091", "version": 0, "coeffType": 1, "scoreRuleId": "2152092", "min": 0, "isDeleted": "false", "stepName": "D:不胜任", "rate": 99, "createdTime": 1719158400000, "id": "16070172", "place": 2, "scoreRuleName": "系统默认"}], "avatar": "https://static-legacy.dingtalk.com/media/lADPD3W5Yjl9zx3NAzvNAzw_828_827.jpg", "originalFinalScore": 80.6805, "reviewersJson": [{"empId": "14604374", "avatar": "https://static-legacy.dingtalk.com/media/lADPD3W5Yjl9zx3NAzvNAzw_828_827.jpg", "exUserId": "032809223926310708", "jobnumber": "DL0125", "empName": "李泊翰", "status": "on_the_job"}], "version": 8, "hasAskEndScore": false, "companyId": {"id": "1011631"}, "resetRaterNameIds": [], "ruleConfStatus": 200, "opEmpAllItemSubmitFinishValue": false, "inputFinishStatus": 2, "atOrgPathHight": 3, "scoreEndFlag": false, "isNewEmp": 0, "kpis": [{"alreadyNodes": [], "itemRule": "达人佣金与推广费用均不可超过公司预算情况下，销售额完成率=总成交金额/目标销售额*100%\n\n路径：财务部-财务助理-《运营四部月度财务报表》\n录入人：被考核人", "examineOperType": "update", "showFinishBar": 1, "itemFinishValue": 46.31, "itemUnit": "%", "taskUserId": "2880379", "itemScoreValue": "{\"type\":\"toMainScore\"}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "false", "formulaType": 1, "finishValueType": 1, "createdTime": 1724753259000, "id": "14112819", "createdUser": "14604374", "scorerType": "auto", "order": 0, "itemFormula": "((((完成值).toFixed(12)*1>=(90).toFixed(12)*1))&&('100'))||((((完成值).toFixed(12)*1>=(50).toFixed(12)*1))&&('70+(完成值-50)*30/40'))||((((完成值).toFixed(12)*1>=(40).toFixed(12)*1))&&('60+(完成值-40)'))||(((1))&&('60'))", "updatedTime": 1724753259000, "thresholdJson": "[{\"id\":0,\"cursorPosition\":0,\"calculationType\":1,\"inputLabelArr\":[{\"label\":1,\"value\":1},{\"label\":0,\"value\":0},{\"label\":0,\"value\":0}],\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"keyboardShow\":false,\"conditionRelation\":\"&&\",\"formulaIsError\":false,\"errMsg\":\"\",\"filterList\":[{\"head\":\"完成值\",\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":\"90\"}]},{\"id\":2,\"cursorPosition\":13,\"calculationType\":1,\"inputLabelArr\":[{\"label\":7,\"value\":7},{\"label\":0,\"value\":0},{\"label\":\"+\",\"value\":\"+\",\"symbol\":true},{\"label\":\"(\",\"value\":\"(\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"1adb2dd1-70f9-4454-8b3e-79efa469cea1\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"label\":\"-\",\"value\":\"-\",\"symbol\":true},{\"label\":5,\"value\":5},{\"label\":0,\"value\":0},{\"label\":\")\",\"value\":\")\",\"symbol\":true},{\"label\":\"×\",\"value\":\"*\",\"symbol\":true},{\"label\":3,\"value\":3},{\"label\":0,\"value\":0},{\"label\":\"÷\",\"value\":\"/\",\"symbol\":true},{\"label\":4,\"value\":4},{\"label\":0,\"value\":0}],\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"keyboardShow\":false,\"conditionRelation\":\"&&\",\"formulaIsError\":false,\"errMsg\":\"\",\"filterList\":[{\"head\":\"完成值\",\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":\"50\"}]},{\"id\":3,\"cursorPosition\":4,\"calculationType\":1,\"inputLabelArr\":[{\"label\":6,\"value\":6},{\"label\":0,\"value\":0},{\"label\":\"+\",\"value\":\"+\",\"symbol\":true},{\"label\":\"(\",\"value\":\"(\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"1adb2dd1-70f9-4454-8b3e-79efa469cea1\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"label\":\"-\",\"value\":\"-\",\"symbol\":true},{\"label\":4,\"value\":4},{\"label\":0,\"value\":0},{\"label\":\")\",\"value\":\")\",\"symbol\":true}],\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"keyboardShow\":false,\"conditionRelation\":\"&&\",\"formulaIsError\":false,\"errMsg\":\"\",\"filterList\":[{\"head\":\"完成值\",\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":\"40\"}]},{\"calculationType\":1,\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"inputLabelArr\":[{\"label\":6,\"value\":6},{\"label\":0,\"value\":0}],\"cursorPosition\":7,\"conditionRelation\":\"||\",\"keyboardShow\":false,\"errMsg\":\"\",\"filterList\":[{\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":0,\"head\":\"完成值\"}],\"id\":4}]", "typeOrder": 0, "inputFormat": "num", "updatedUser": "14604374", "version": 3, "companyId": {"id": "1011631"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 1, "itemTargetValue": 100, "itemFieldJson": "[]", "kpiTypeClassify": "custom", "formulaCondition": "[]", "empId": "14604374", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "itemScoreRule": {"superRater": {"superiorScoreOrder": "inTurn", "signatureFlag": false, "auditNodes": [], "open": 0}, "updatedTime": 1724751387000, "kpiItemId": "7e64f647-f2af-4763-abfd-07fe4eda39cb", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "2880379", "updatedUser": "*********", "version": 0, "superiorScoreFlag": "false", "companyId": {"id": "1011631"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "13552659", "createdTime": 1724751387000, "id": "9483360", "taskId": "2093977", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "14604374"}, "managerLevel": "", "kpiItemName": "销售额达成率（运营四部达播）", "itemAutoScore": 26.524, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 100, "inputRole": [], "itemWeight": 40, "scoringRule": "D:X≤40%：60分\nC:40%<X≤50%：每高1个百分点在60分基础上+1分\nB:50%<X≤90%：每高1个百分点在70分基础上+0.75分\nA:90%<X：100分", "kpiItemId": "7e64f647-f2af-4763-abfd-07fe4eda39cb", "maxExtraScore": 0, "appointAudits": [], "isTypeLocked": "[\"typeWeight\",\"addIndex\",\"deleteIndex\"]", "openOkrScore": 0, "resultInputEmpId": "14604374", "kpiTypeName": "经营（财务、业务）指标", "kpiTypeId": "13552659", "isNewEmp": 0, "formulaFields": [], "taskId": "2093977", "waitScores": []}, {"alreadyNodes": [], "itemRule": "若7日总ROI≥月度目标值，销售额完成率=当月总实际销售额/当月目标销售额*100%\n若7总ROI＜月度目标值，销售额完成率=0\n\n路径：运营四部-主管-《巨量千川数据》\n录入人：被考核人", "examineOperType": "update", "showFinishBar": 1, "itemFinishValue": 100, "itemUnit": "%", "taskUserId": "2880379", "itemScoreValue": "{\"type\":\"toMainScore\"}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "false", "formulaType": 1, "finishValueType": 1, "createdTime": 1724753259000, "id": "14112820", "createdUser": "14604374", "scorerType": "auto", "order": 1, "itemFormula": "((((完成值).toFixed(12)*1>=(90).toFixed(12)*1))&&('100'))||((((完成值).toFixed(12)*1>=(50).toFixed(12)*1))&&('70+(完成值-50)*30/40'))||((((完成值).toFixed(12)*1>=(40).toFixed(12)*1))&&('60+(完成值-40)'))||(((1))&&('60'))", "updatedTime": 1724753259000, "thresholdJson": "[{\"id\":0,\"cursorPosition\":0,\"calculationType\":1,\"inputLabelArr\":[{\"label\":1,\"value\":1},{\"label\":0,\"value\":0},{\"label\":0,\"value\":0}],\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"keyboardShow\":false,\"conditionRelation\":\"&&\",\"formulaIsError\":false,\"errMsg\":\"\",\"filterList\":[{\"head\":\"完成值\",\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":\"90\"}]},{\"id\":2,\"cursorPosition\":13,\"calculationType\":1,\"inputLabelArr\":[{\"label\":7,\"value\":7},{\"label\":0,\"value\":0},{\"label\":\"+\",\"value\":\"+\",\"symbol\":true},{\"label\":\"(\",\"value\":\"(\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"1adb2dd1-70f9-4454-8b3e-79efa469cea1\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"label\":\"-\",\"value\":\"-\",\"symbol\":true},{\"label\":5,\"value\":5},{\"label\":0,\"value\":0},{\"label\":\")\",\"value\":\")\",\"symbol\":true},{\"label\":\"×\",\"value\":\"*\",\"symbol\":true},{\"label\":3,\"value\":3},{\"label\":0,\"value\":0},{\"label\":\"÷\",\"value\":\"/\",\"symbol\":true},{\"label\":4,\"value\":4},{\"label\":0,\"value\":0}],\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"keyboardShow\":false,\"conditionRelation\":\"&&\",\"formulaIsError\":false,\"errMsg\":\"\",\"filterList\":[{\"head\":\"完成值\",\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":\"50\"}]},{\"id\":3,\"cursorPosition\":4,\"calculationType\":1,\"inputLabelArr\":[{\"label\":6,\"value\":6},{\"label\":0,\"value\":0},{\"label\":\"+\",\"value\":\"+\",\"symbol\":true},{\"label\":\"(\",\"value\":\"(\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"1adb2dd1-70f9-4454-8b3e-79efa469cea1\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"label\":\"-\",\"value\":\"-\",\"symbol\":true},{\"label\":4,\"value\":4},{\"label\":0,\"value\":0},{\"label\":\")\",\"value\":\")\",\"symbol\":true}],\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"keyboardShow\":false,\"conditionRelation\":\"&&\",\"formulaIsError\":false,\"errMsg\":\"\",\"filterList\":[{\"head\":\"完成值\",\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":\"40\"}]},{\"calculationType\":1,\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"inputLabelArr\":[{\"label\":6,\"value\":6},{\"label\":0,\"value\":0}],\"cursorPosition\":7,\"conditionRelation\":\"||\",\"keyboardShow\":false,\"errMsg\":\"\",\"filterList\":[{\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":0,\"head\":\"完成值\"}],\"id\":4}]", "typeOrder": 0, "inputFormat": "num", "updatedUser": "14604374", "version": 3, "companyId": {"id": "1011631"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 1, "itemTargetValue": 100, "itemFieldJson": "[]", "kpiTypeClassify": "custom", "formulaCondition": "[]", "empId": "14604374", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "itemScoreRule": {"superRater": {"superiorScoreOrder": "inTurn", "signatureFlag": false, "auditNodes": [], "open": 0}, "updatedTime": 1724751387000, "kpiItemId": "45f3286a-af69-413e-bebe-aea5f3085355", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "2880379", "updatedUser": "*********", "version": 0, "superiorScoreFlag": "false", "companyId": {"id": "1011631"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "13552659", "createdTime": 1724751387000, "id": "9483361", "taskId": "2093977", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "14604374"}, "managerLevel": "", "kpiItemName": "销售额达成率（运营四部）", "itemAutoScore": 30, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 100, "inputRole": [], "itemWeight": 30, "scoringRule": "D:X≤40%：60分\nC:40%<X≤50%：每高1个百分点在60分基础上+1分\nB:50%<X≤90%：每高1个百分点在70分基础上+0.75分\nA:90%<X：100分", "kpiItemId": "45f3286a-af69-413e-bebe-aea5f3085355", "maxExtraScore": 0, "appointAudits": [], "isTypeLocked": "[\"typeWeight\",\"addIndex\",\"deleteIndex\"]", "openOkrScore": 0, "resultInputEmpId": "14604374", "kpiTypeName": "经营（财务、业务）指标", "kpiTypeId": "13552659", "isNewEmp": 0, "formulaFields": [], "taskId": "2093977", "waitScores": []}, {"alreadyNodes": [], "itemRule": "有效开新数量：达人首单30天内销售额≥100元。\n有效开新达成率=有效开新数量/目标开新数量。\n\n路径：财务部-财务助理-《运营四部月度财务报表》\n录入人：被考核人", "examineOperType": "update", "showFinishBar": 1, "itemFinishValue": 85.42, "itemUnit": "%", "taskUserId": "2880379", "itemScoreValue": "{\"type\":\"toMainScore\"}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "false", "formulaType": 1, "finishValueType": 1, "createdTime": 1724753259000, "id": "14112821", "createdUser": "14604374", "scorerType": "auto", "order": 2, "itemFormula": "((((完成值).toFixed(12)*1>=(90).toFixed(12)*1))&&('100'))||((((完成值).toFixed(12)*1>=(50).toFixed(12)*1))&&('70+(完成值-50)*30/40'))||((((完成值).toFixed(12)*1>=(40).toFixed(12)*1))&&('60+(完成值-40)'))||(((1))&&('60'))", "updatedTime": 1724753259000, "thresholdJson": "[{\"id\":0,\"cursorPosition\":0,\"calculationType\":1,\"inputLabelArr\":[{\"label\":1,\"value\":1},{\"label\":0,\"value\":0},{\"label\":0,\"value\":0}],\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"keyboardShow\":false,\"conditionRelation\":\"&&\",\"formulaIsError\":false,\"errMsg\":\"\",\"filterList\":[{\"head\":\"完成值\",\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":\"90\"}]},{\"id\":2,\"cursorPosition\":13,\"calculationType\":1,\"inputLabelArr\":[{\"label\":7,\"value\":7},{\"label\":0,\"value\":0},{\"label\":\"+\",\"value\":\"+\",\"symbol\":true},{\"label\":\"(\",\"value\":\"(\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"1adb2dd1-70f9-4454-8b3e-79efa469cea1\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"label\":\"-\",\"value\":\"-\",\"symbol\":true},{\"label\":5,\"value\":5},{\"label\":0,\"value\":0},{\"label\":\")\",\"value\":\")\",\"symbol\":true},{\"label\":\"×\",\"value\":\"*\",\"symbol\":true},{\"label\":3,\"value\":3},{\"label\":0,\"value\":0},{\"label\":\"÷\",\"value\":\"/\",\"symbol\":true},{\"label\":4,\"value\":4},{\"label\":0,\"value\":0}],\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"keyboardShow\":false,\"conditionRelation\":\"&&\",\"formulaIsError\":false,\"errMsg\":\"\",\"filterList\":[{\"head\":\"完成值\",\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":\"50\"}]},{\"id\":3,\"cursorPosition\":4,\"calculationType\":1,\"inputLabelArr\":[{\"label\":6,\"value\":6},{\"label\":0,\"value\":0},{\"label\":\"+\",\"value\":\"+\",\"symbol\":true},{\"label\":\"(\",\"value\":\"(\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"1adb2dd1-70f9-4454-8b3e-79efa469cea1\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"label\":\"-\",\"value\":\"-\",\"symbol\":true},{\"label\":4,\"value\":4},{\"label\":0,\"value\":0},{\"label\":\")\",\"value\":\")\",\"symbol\":true}],\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"keyboardShow\":false,\"conditionRelation\":\"&&\",\"formulaIsError\":false,\"errMsg\":\"\",\"filterList\":[{\"head\":\"完成值\",\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":\"40\"}]},{\"calculationType\":1,\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"inputLabelArr\":[{\"label\":6,\"value\":6},{\"label\":0,\"value\":0}],\"cursorPosition\":7,\"conditionRelation\":\"||\",\"keyboardShow\":false,\"errMsg\":\"\",\"filterList\":[{\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":0,\"head\":\"完成值\"}],\"id\":4}]", "typeOrder": 0, "inputFormat": "num", "updatedUser": "14604374", "version": 3, "companyId": {"id": "1011631"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 1, "itemTargetValue": 100, "itemFieldJson": "[]", "kpiTypeClassify": "custom", "formulaCondition": "[]", "empId": "14604374", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "itemScoreRule": {"superRater": {"superiorScoreOrder": "inTurn", "signatureFlag": false, "auditNodes": [], "open": 0}, "updatedTime": 1724751387000, "kpiItemId": "1a3b5df1-ce39-4bac-90e9-21946ab20422", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "2880379", "updatedUser": "*********", "version": 0, "superiorScoreFlag": "false", "companyId": {"id": "1011631"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "13552659", "createdTime": 1724751387000, "id": "9483362", "taskId": "2093977", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "14604374"}, "managerLevel": "", "kpiItemName": "有效开新达成率（运营四部）", "itemAutoScore": 9.6565, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 100, "inputRole": [], "itemWeight": 10, "scoringRule": "D:X≤40%：60分\nC:40%<X≤50%：每高1个百分点在60分基础上+1分\nB:50%<X≤90%：每高1个百分点在70分基础上+0.75分\nA:90%<X：100分", "kpiItemId": "1a3b5df1-ce39-4bac-90e9-21946ab20422", "maxExtraScore": 0, "appointAudits": [], "isTypeLocked": "[\"typeWeight\",\"addIndex\",\"deleteIndex\"]", "openOkrScore": 0, "resultInputEmpId": "14604374", "kpiTypeName": "经营（财务、业务）指标", "kpiTypeId": "13552659", "isNewEmp": 0, "formulaFields": [], "taskId": "2093977", "waitScores": []}, {"alreadyNodes": [], "itemRule": "有效视频数量：产生消耗的视频数量；本项得分=有消耗的数量/目标数量*权重*100分\n\n路径：运营四部-主管-《巨量千川数据》\n录入人：被考核人", "examineOperType": "update", "showFinishBar": 1, "itemFinishValue": 95.67, "itemUnit": "%", "taskUserId": "2880379", "itemScoreValue": "{\"type\":\"toMainScore\"}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "false", "formulaType": 1, "finishValueType": 1, "createdTime": 1724753259000, "id": "14112822", "createdUser": "14604374", "scorerType": "auto", "order": 3, "itemFormula": "((((完成值).toFixed(12)*1>=(90).toFixed(12)*1))&&('100'))||((((完成值).toFixed(12)*1>=(50).toFixed(12)*1))&&('70+(完成值-50)*30/40'))||((((完成值).toFixed(12)*1>=(40).toFixed(12)*1))&&('60+(完成值-40)'))||(((1))&&('60'))", "updatedTime": 1724753259000, "thresholdJson": "[{\"id\":0,\"cursorPosition\":0,\"calculationType\":1,\"inputLabelArr\":[{\"label\":1,\"value\":1},{\"label\":0,\"value\":0},{\"label\":0,\"value\":0}],\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"keyboardShow\":false,\"conditionRelation\":\"&&\",\"formulaIsError\":false,\"errMsg\":\"\",\"filterList\":[{\"head\":\"完成值\",\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":\"90\"}]},{\"id\":2,\"cursorPosition\":13,\"calculationType\":1,\"inputLabelArr\":[{\"label\":7,\"value\":7},{\"label\":0,\"value\":0},{\"label\":\"+\",\"value\":\"+\",\"symbol\":true},{\"label\":\"(\",\"value\":\"(\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"1adb2dd1-70f9-4454-8b3e-79efa469cea1\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"label\":\"-\",\"value\":\"-\",\"symbol\":true},{\"label\":5,\"value\":5},{\"label\":0,\"value\":0},{\"label\":\")\",\"value\":\")\",\"symbol\":true},{\"label\":\"×\",\"value\":\"*\",\"symbol\":true},{\"label\":3,\"value\":3},{\"label\":0,\"value\":0},{\"label\":\"÷\",\"value\":\"/\",\"symbol\":true},{\"label\":4,\"value\":4},{\"label\":0,\"value\":0}],\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"keyboardShow\":false,\"conditionRelation\":\"&&\",\"formulaIsError\":false,\"errMsg\":\"\",\"filterList\":[{\"head\":\"完成值\",\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":\"50\"}]},{\"id\":3,\"cursorPosition\":4,\"calculationType\":1,\"inputLabelArr\":[{\"label\":6,\"value\":6},{\"label\":0,\"value\":0},{\"label\":\"+\",\"value\":\"+\",\"symbol\":true},{\"label\":\"(\",\"value\":\"(\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"1adb2dd1-70f9-4454-8b3e-79efa469cea1\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"label\":\"-\",\"value\":\"-\",\"symbol\":true},{\"label\":4,\"value\":4},{\"label\":0,\"value\":0},{\"label\":\")\",\"value\":\")\",\"symbol\":true}],\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"keyboardShow\":false,\"conditionRelation\":\"&&\",\"formulaIsError\":false,\"errMsg\":\"\",\"filterList\":[{\"head\":\"完成值\",\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":\"40\"}]},{\"calculationType\":1,\"stairLabelMap\":{\"head\":\"完成值\",\"target\":\"目标值\",\"defineVal\":\"\",\"scoreType\":1,\"addInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":1},\"minusInfo\":{\"basics\":\"\",\"every\":\"\",\"number\":\"\",\"total\":\"\",\"everySymbol\":2}},\"inputLabelArr\":[{\"label\":6,\"value\":6},{\"label\":0,\"value\":0}],\"cursorPosition\":7,\"conditionRelation\":\"||\",\"keyboardShow\":false,\"errMsg\":\"\",\"filterList\":[{\"relation\":\">=\",\"target\":\"anyValue\",\"defineVal\":0,\"head\":\"完成值\"}],\"id\":4}]", "typeOrder": 0, "inputFormat": "num", "updatedUser": "14604374", "version": 3, "companyId": {"id": "1011631"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 1, "itemTargetValue": 100, "itemFieldJson": "[]", "kpiTypeClassify": "custom", "formulaCondition": "[]", "empId": "14604374", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "itemScoreRule": {"superRater": {"superiorScoreOrder": "inTurn", "signatureFlag": false, "auditNodes": [], "open": 0}, "updatedTime": 1724751387000, "kpiItemId": "2afffc24-6aab-46a3-a6ea-fe0eaa3ed5da", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "2880379", "updatedUser": "*********", "version": 0, "superiorScoreFlag": "false", "companyId": {"id": "1011631"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "13552659", "createdTime": 1724751387000, "id": "9483363", "taskId": "2093977", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "14604374"}, "managerLevel": "", "kpiItemName": "有效视频数量（运营四部）", "itemAutoScore": 10, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 100, "inputRole": [], "itemWeight": 10, "scoringRule": "D:X≤40%：60分\nC:40%<X≤50%：每高1个百分点在60分基础上+1分\nB:50%<X≤90%：每高1个百分点在70分基础上+0.75分\nA:90%<X：100分", "kpiItemId": "2afffc24-6aab-46a3-a6ea-fe0eaa3ed5da", "maxExtraScore": 0, "appointAudits": [], "isTypeLocked": "[\"typeWeight\",\"addIndex\",\"deleteIndex\"]", "openOkrScore": 0, "resultInputEmpId": "14604374", "kpiTypeName": "经营（财务、业务）指标", "kpiTypeId": "13552659", "isNewEmp": 0, "formulaFields": [], "taskId": "2093977", "waitScores": []}, {"alreadyNodes": [], "itemRule": "考核期内，ERP系统9号仓、批发仓中超40天（天数=可发库存数量/近30天销量）SKU个数为0\n\n路径：财务部-主管-《部门绩效（库存周转）-2024年》\n录入人：被考核人", "examineOperType": "update", "showFinishBar": 1, "itemFinishValue": 11, "itemUnit": "个", "taskUserId": "2880379", "itemScoreValue": "{\"type\":\"toMainScore\"}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "false", "formulaType": 2, "finishValueType": 1, "createdTime": 1724753259000, "id": "14112823", "createdUser": "14604374", "scorerType": "auto", "order": 4, "itemFormula": "((((100+目标值-完成值*5)<0))&&('0')) || (((1))&&('100+目标值-完成值*5'))", "updatedTime": 1724753259000, "thresholdJson": "[{\"label\":\"如果\",\"value\":\"if\",\"symbol\":true},{\"label\":\"(\",\"value\":\"(\",\"symbol\":true},{\"label\":\"(\",\"value\":\"(\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"指标得分\",\"companyFieldId\":\"score\",\"isSystemField\":\"false\",\"label\":\"指标得分\",\"value\":0,\"yVal\":true},{\"label\":\"=\",\"value\":\"=\",\"symbol\":true},{\"label\":1,\"value\":1},{\"label\":0,\"value\":0},{\"label\":0,\"value\":0},{\"label\":\"+\",\"value\":\"+\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e9\",\"isSystemField\":\"false\",\"label\":\"目标值\",\"value\":0,\"yVal\":true},{\"label\":\"-\",\"value\":\"-\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"1adb2dd1-70f9-4454-8b3e-79efa469cea1\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"label\":\"x\",\"value\":\"*\",\"symbol\":true},{\"label\":5,\"value\":5},{\"label\":\")\",\"value\":\")\",\"symbol\":true},{\"label\":\"<\",\"value\":\"<\",\"symbol\":true},{\"label\":0,\"value\":0},{\"label\":\")\",\"value\":\")\",\"symbol\":true},{\"label\":\"{\",\"value\":\"{\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"指标得分\",\"companyFieldId\":\"score\",\"isSystemField\":\"false\",\"label\":\"指标得分\",\"value\":0,\"yVal\":true},{\"label\":\"=\",\"value\":\"=\",\"symbol\":true},{\"label\":0,\"value\":0},{\"label\":\"}\",\"value\":\"}\",\"symbol\":true},{\"label\":\"br\",\"value\":\"br\"},{\"label\":\"否则\",\"value\":\"else\",\"symbol\":true},{\"label\":\"{\",\"value\":\"{\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"指标得分\",\"companyFieldId\":\"score\",\"isSystemField\":\"false\",\"label\":\"指标得分\",\"value\":0,\"yVal\":true},{\"label\":\"=\",\"value\":\"=\",\"symbol\":true},{\"label\":1,\"value\":1},{\"label\":0,\"value\":0},{\"label\":0,\"value\":0},{\"label\":\"+\",\"value\":\"+\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e9\",\"isSystemField\":\"false\",\"label\":\"目标值\",\"value\":0,\"yVal\":true},{\"label\":\"-\",\"value\":\"-\",\"symbol\":true},{\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"1adb2dd1-70f9-4454-8b3e-79efa469cea1\",\"isSystemField\":\"false\",\"label\":\"完成值\",\"value\":0,\"yVal\":true},{\"label\":\"x\",\"value\":\"*\",\"symbol\":true},{\"label\":5,\"value\":5},{\"label\":\"}\",\"value\":\"}\",\"symbol\":true}]", "typeOrder": 0, "inputFormat": "num", "updatedUser": "14604374", "version": 3, "companyId": {"id": "1011631"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 1, "itemTargetValue": 0, "itemFieldJson": "[]", "kpiTypeClassify": "custom", "formulaCondition": "[]", "empId": "14604374", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "itemScoreRule": {"superRater": {"superiorScoreOrder": "inTurn", "signatureFlag": false, "auditNodes": [], "open": 0}, "updatedTime": 1724751387000, "kpiItemId": "2c1816f0-079e-46d3-ad67-343443246c03", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "2880379", "updatedUser": "*********", "version": 0, "superiorScoreFlag": "false", "companyId": {"id": "1011631"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "13552659", "createdTime": 1724751387000, "id": "9483364", "taskId": "2093977", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "14604374"}, "managerLevel": "", "kpiItemName": "超时周转SKU数（9+批发仓）", "itemAutoScore": 4.5, "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 100, "inputRole": [], "itemWeight": 10, "scoringRule": "-5分/个", "kpiItemId": "2c1816f0-079e-46d3-ad67-343443246c03", "maxExtraScore": 0, "appointAudits": [], "isTypeLocked": "[\"typeWeight\",\"addIndex\",\"deleteIndex\"]", "openOkrScore": 0, "resultInputEmpId": "14604374", "kpiTypeName": "经营（财务、业务）指标", "kpiTypeId": "13552659", "isNewEmp": 0, "formulaFields": [], "taskId": "2093977", "waitScores": []}, {"alreadyNodes": [], "itemRule": "1.外部投诉（三级投诉）：\n一级：外部客户投诉至工商等政府监管部门，经核实确因个人行为导致；\n二级：外部客户投诉至总经理处，经核实确因个人行为导致；\n三级：外部客户投诉至部门负责人处，经核实确因个人行为导致；\n（如给公司造成一定经济损失，根据公司制度标准执行）\n2.内部投诉（三级投诉）：\n因员工/部门间工作协作导致内部客户投诉，遵循先向协作部门主管（三级）进行投诉，未能解决问题再向总经办（二级）进行投诉，经协调后仍未解决的，可投诉至总经理（一级）进行最终处理。\n\n路径：总经办-专员-《内外投诉台账》\n录入人：上级负责人（部门负责人）", "examineOperType": "update", "showFinishBar": 1, "itemFinishValue": 0, "itemUnit": "次", "taskUserId": "2880379", "itemScoreValue": "{\"type\":\"toMainScore\"}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "", "formulaType": 1, "finishValueType": 1, "createdTime": 1724753259000, "id": "14112824", "createdUser": "14604374", "scorerType": "exam", "order": 0, "itemFormula": "", "updatedTime": 1724753259000, "thresholdJson": "[]", "typeOrder": 1, "inputFormat": "num", "updatedUser": "14604374", "version": 1, "companyId": {"id": "1011631"}, "resultInputType": "manager", "itemFullScoreCfg": "false", "mustResultInput": 0, "itemTargetValue": 0, "itemFieldJson": "[]", "kpiTypeClassify": "subtract", "formulaCondition": "[]", "empId": "14604374", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "itemScoreRule": {"superRater": {"nodeWeight": 100, "superiorScoreOrder": "sameTime", "nodeVacancyFlag": 1, "rateMode": "item", "signatureFlag": false, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "approverInfo": "1", "node": "superior_score", "raters": [{"empId": "2853238", "level": 1, "weight": 100, "type": 2, "empName": "张树才"}], "multiType": "and", "approverType": "manager"}], "open": 1}, "updatedTime": 1724751387000, "kpiItemId": "ad7ace04-ea1b-4611-b905-cd82c315441f", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "2880379", "updatedUser": "*********", "version": 0, "superiorScoreWeight": 100, "superiorScoreFlag": "true", "companyId": {"id": "1011631"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "13552665", "createdTime": 1724751387000, "id": "9483365", "taskId": "2093977", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "14604374"}, "managerLevel": "1", "kpiItemName": "内外投诉", "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "itemTargetValueText": "", "inputRole": [], "itemWeight": 100, "scoringRule": "此项扣分无上限。\n1.外部投诉（三级投诉）：\n三级：-5分/次，二级：-10分/次，一级：视严重程度扣减，无上限；\n2.内部投诉（三级投诉）：\n三级：-3分/次，二级：-5分/次，一级：-10分/次；", "kpiItemId": "ad7ace04-ea1b-4611-b905-cd82c315441f", "appointAudits": [], "isTypeLocked": "[\"typeWeight\",\"addIndex\",\"deleteIndex\"]", "openOkrScore": 0, "resultInputEmpId": "2853238", "kpiTypeName": "内外客户满意度", "kpiTypeId": "13552665", "isNewEmp": 0, "formulaFields": [], "taskId": "2093977", "waitScores": []}, {"alreadyNodes": [], "itemRule": "本项由部门负责人根据员工当月价值观、制度（行为规范、业务规范等各项制度）的表现，参考积分记录和奖惩记录进行评价。\n\n路径：悦积分\n录入人：上级负责人", "examineOperType": "update", "showFinishBar": 1, "itemFinishValue": -10, "itemUnit": "分", "taskUserId": "2880379", "itemScoreValue": "{\"type\":\"toMainScore\"}", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "", "formulaType": 1, "finishValueType": 1, "createdTime": 1724753259000, "id": "14112825", "createdUser": "14604374", "scorerType": "exam", "order": 0, "itemFormula": "", "updatedTime": 1728455272000, "thresholdJson": "[]", "typeOrder": 2, "plusSubInterval": {"max": "5", "min": "-5"}, "inputFormat": "num", "updatedUser": "14604374", "version": 1, "companyId": {"id": "1011631"}, "resultInputType": "manager", "itemFullScoreCfg": "false", "mustResultInput": 0, "itemTargetValue": 0, "itemFieldJson": "[]", "kpiTypeClassify": "plusSub", "formulaCondition": "[]", "empId": "14604374", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "itemScoreRule": {"superRater": {"nodeWeight": 100, "superiorScoreOrder": "sameTime", "nodeVacancyFlag": 1, "rateMode": "item", "signatureFlag": false, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "approverInfo": "1", "node": "superior_score", "raters": [{"empId": "2853238", "level": 1, "weight": 100, "type": 2, "empName": "张树才"}], "multiType": "and", "approverType": "manager"}], "open": 1}, "updatedTime": 1724751387000, "kpiItemId": "bb9829b8-c810-4242-b5dc-9bdd5bf48fb0", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "2880379", "updatedUser": "*********", "version": 0, "superiorScoreWeight": 100, "superiorScoreFlag": "true", "companyId": {"id": "1011631"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "13552667", "createdTime": 1724751387000, "id": "9483366", "taskId": "2093977", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "14604374"}, "managerLevel": "1", "kpiItemName": "文化认同", "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "itemTargetValueText": "", "inputRole": [], "itemWeight": 100, "scoringRule": "最高±5分，本项如有录入分值，上级主管须对评分事项具体说明。", "kpiItemId": "bb9829b8-c810-4242-b5dc-9bdd5bf48fb0", "maxExtraScore": 0, "appointAudits": [], "isTypeLocked": "[\"typeWeight\",\"deleteIndex\",\"addIndex\"]", "openOkrScore": 0, "resultInputEmpId": "2853238", "kpiTypeName": "学习与发展", "kpiTypeId": "13552667", "isNewEmp": 0, "formulaFields": [], "taskId": "2093977", "waitScores": []}, {"alreadyNodes": [], "itemRule": "培训计划完成率=部门业务、技能类培训场次/目标培训场次*100%\n\n路径：总经办-专员-《多乐培训满意度调查》\n录入人：部门负责人（被考核人）", "examineOperType": "update", "showFinishBar": 1, "itemUnit": "%", "taskUserId": "2880379", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "false", "formulaType": 1, "finishValueType": 1, "createdTime": 1724753259000, "id": "14112827", "createdUser": "14604374", "scorerType": "exam", "order": 1, "itemFormula": "", "updatedTime": 1728443016000, "thresholdJson": "[]", "typeOrder": 2, "plusSubInterval": {"max": "0", "min": "-10"}, "inputFormat": "num", "updatedUser": "14604374", "version": 1, "companyId": {"id": "1011631"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 0, "itemTargetValue": 100, "itemFieldJson": "[]", "kpiTypeClassify": "plusSub", "formulaCondition": "[]", "empId": "14604374", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "itemScoreRule": {"superRater": {"nodeWeight": 100, "superiorScoreOrder": "sameTime", "nodeVacancyFlag": 1, "rateMode": "item", "signatureFlag": false, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "approverInfo": "1", "node": "superior_score", "raters": [{"empId": "2853238", "level": 1, "weight": 100, "type": 2, "empName": "张树才"}], "multiType": "and", "approverType": "manager"}], "open": 1}, "updatedTime": 1724751387000, "kpiItemId": "0a105550-2090-4fec-9754-08516c7adf15", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "2880379", "updatedUser": "*********", "version": 0, "superiorScoreWeight": 100, "superiorScoreFlag": "true", "companyId": {"id": "1011631"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "13552667", "createdTime": 1724751387000, "id": "9483367", "taskId": "2093977", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "14604374"}, "managerLevel": "", "kpiItemName": "培训计划完成率", "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "inputRole": [], "itemWeight": 0, "scoringRule": "扣除分值=10*(1-计划完成率）", "kpiItemId": "0a105550-2090-4fec-9754-08516c7adf15", "maxExtraScore": 0, "appointAudits": [], "isTypeLocked": "[\"typeWeight\",\"deleteIndex\",\"addIndex\"]", "openOkrScore": 0, "resultInputEmpId": "14604374", "kpiTypeName": "学习与发展", "kpiTypeId": "13552667", "isNewEmp": 0, "formulaFields": [], "taskId": "2093977", "waitScores": []}, {"alreadyNodes": [], "itemRule": "1.制度规范：部门负责人主动新建、修订、优化部门内部制度规范、操作手册等文件，当月完成发布并进行部门内宣贯及签字确认；\n2.业务流程：部门负责人针对部门内、部门交叉业务的流程进行优化梳理，经相关部门评估，切实能有效提高工作效率或降低成本。\n\n路径：总经办-专员-《公司制度流程台账》\n录入人：部门负责人（被考核人）", "examineOperType": "update", "showFinishBar": 1, "itemUnit": "项", "taskUserId": "2880379", "finishValueAudit": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "showTargetValue": "false", "formulaType": 1, "finishValueType": 1, "createdTime": 1724753259000, "id": "14112829", "createdUser": "14604374", "scorerType": "exam", "order": 0, "itemFormula": "", "updatedTime": 1728443016000, "thresholdJson": "[]", "typeOrder": 3, "inputFormat": "num", "updatedUser": "14604374", "version": 1, "companyId": {"id": "1011631"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 0, "itemTargetValue": 0, "itemFieldJson": "[]", "kpiTypeClassify": "plus", "formulaCondition": "[]", "empId": "14604374", "itemType": "measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "itemScoreRule": {"superRater": {"nodeWeight": 100, "superiorScoreOrder": "sameTime", "nodeVacancyFlag": 1, "rateMode": "item", "signatureFlag": false, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "approverInfo": "1", "node": "superior_score", "raters": [{"empId": "2853238", "level": 1, "weight": 100, "type": 2, "empName": "张树才"}], "multiType": "and", "approverType": "manager"}], "open": 1}, "updatedTime": 1724751387000, "kpiItemId": "4e995611-dc95-4169-b273-bfdbe2f47f38", "peerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "appointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "appointScoreFlag": "false", "taskUserId": "2880379", "updatedUser": "*********", "version": 0, "superiorScoreWeight": 100, "superiorScoreFlag": "true", "companyId": {"id": "1011631"}, "selfScoreFlag": "false", "selfRater": {"signatureFlag": false, "open": 0}, "isDeleted": "false", "kpiTypeId": "13552670", "createdTime": 1724751387000, "id": "9483368", "taskId": "2093977", "subRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "createdUser": "14604374"}, "managerLevel": "", "kpiItemName": "制度流程体系建设", "isDeleted": "false", "finalSubmitFinishValue": 1, "kpiTypeWeight": 0, "inputRole": [], "itemWeight": 100, "scoringRule": "按照当月实际签发的制度或流程数量加分。\n制度或业务流程等进行版本重大调整（含新建），每项制度或流程加5分。进行部分条款调整的，每项制度或流程加2分。", "kpiItemId": "4e995611-dc95-4169-b273-bfdbe2f47f38", "appointAudits": [], "isTypeLocked": "[\"typeWeight\",\"addIndex\",\"deleteIndex\"]", "openOkrScore": 0, "resultInputEmpId": "14604374", "kpiTypeName": "内部流程建设", "kpiTypeId": "13552670", "isNewEmp": 0, "formulaFields": [], "taskId": "2093977", "waitScores": []}], "taskId": "2093977", "infos": [], "empEvalRule": {"indexRaters": [{"node": "super", "raters": [{"empId": "2853238", "level": 1, "weight": 100, "type": 2, "empName": "张树才"}]}], "initiator": "*********", "editStatus": 0, "scoreConf": {"transferFlag": "true", "multiType": "and"}, "auditResult": {"transferFlag": "true", "vacancyApproveInfo": "", "commentReq": 1, "vacancyApproveType": "superior", "empRepeatSkip": 0, "notFindAdminBySupAdmin": 1, "collectSendNotify": 1, "nodeEmpVacancy": 2, "auditNodes": [{"approvalOrder": 1, "approverInfo": "1", "approverName": "直属主管", "node": "final_result_audit", "raters": [{"empId": "2853238", "level": 1, "avatar": "https://static-legacy.dingtalk.com/media/lADPDgQ93RLgqB3NA1bNAtA_720_854.jpg", "type": 2, "dingUserId": "14306258853863", "empName": "张树才"}], "multiType": "and", "approverType": "manager"}, {"approvalOrder": 2, "approverInfo": "247f37dc-5459-4c6a-8abe-17dff4b7448e", "approverName": "薪酬绩效", "node": "final_result_audit", "raters": [{"empId": "*********", "roleId": "247f37dc-5459-4c6a-8abe-17dff4b7448e", "avatar": "https://static-legacy.dingtalk.com/media/lQDPM5w8lGXT6X_NAljNAliwQEtsGQMy7s4GfpSDU1l0AA_600_600.jpg", "type": 0, "dingUserId": "305905456234243546", "empName": "袁传芹", "roleName": "薪酬绩效"}], "multiType": "and", "approverType": "role"}], "multiType": "and", "vacancyApproveName": "", "open": 1}, "finishValueAudit": {"empRepeatSkip": 0, "notFindAdminBySupAdmin": 1, "nodeEmpVacancy": 2, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "approverInfo": "1", "approverName": "直属主管", "raters": [{"empId": "2853238", "level": 1, "type": 2, "empName": "张树才"}], "multiType": "and", "approverType": "manager"}, {"transferFlag": "true", "approvalOrder": 2, "approverInfo": "247f37dc-5459-4c6a-8abe-17dff4b7448e", "approverName": "薪酬绩效", "raters": [{"empId": "*********", "roleId": "247f37dc-5459-4c6a-8abe-17dff4b7448e", "type": 0, "empName": "袁传芹", "roleName": "薪酬绩效"}], "multiType": "and", "approverType": "role"}], "open": 1}, "s3AppointRater": {"signatureFlag": false, "auditNodes": [], "open": 0}, "totalLevelRaters": [], "s3SuperRater": {"superiorScoreOrder": "inTurn", "signatureFlag": false, "auditNodes": [], "open": 0}, "isDeleted": "false", "ruleName": "运营四部主管2024年第1版", "createdTime": 1724751387000, "typeWeightConf": {"checkItemWeight": 1, "limit100Weight": 1, "itemWeightLimit": 100, "open": 1}, "enterScore": {"enterScoreEmpType": 2, "enterScoreMethod": "auto", "scoreStartRuleType": "before", "scoreStartRuleDay": 1}, "createdUser": "*********", "updatedTime": 1725611250000, "confirmTask": {"openConfirmLT": 0, "noChangeSkipFlag": "false", "empRepeatSkip": 0, "notFindAdminBySupAdmin": 1, "confirmLTDay": 1, "nodeEmpVacancy": 2, "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "modifyFlag": "true", "approverInfo": "", "confirmAuditSign": "false", "approverName": "被考核人", "node": "modify_item_audit", "raters": [{"empId": "14604374", "type": 0, "empName": "李泊翰"}], "multiType": "and", "modifyItemDimension": "target", "approverType": "taskEmp"}, {"transferFlag": "true", "approvalOrder": 2, "modifyFlag": "true", "approverInfo": "1", "approverName": "直属主管", "node": "modify_item_audit", "raters": [{"empId": "2853238", "level": 1, "avatar": "https://static-legacy.dingtalk.com/media/lADPDgQ93RLgqB3NA1bNAtA_720_854.jpg", "type": 2, "dingUserId": "14306258853863", "empName": "张树才"}], "multiType": "and", "modifyItemDimension": "target", "approverType": "manager"}, {"transferFlag": "true", "approvalOrder": 3, "modifyFlag": "true", "approverInfo": "247f37dc-5459-4c6a-8abe-17dff4b7448e", "approverName": "薪酬绩效", "node": "modify_item_audit", "raters": [{"empId": "*********", "roleId": "247f37dc-5459-4c6a-8abe-17dff4b7448e", "avatar": "https://static-legacy.dingtalk.com/media/lQDPM5w8lGXT6X_NAljNAliwQEtsGQMy7s4GfpSDU1l0AA_600_600.jpg", "type": 0, "dingUserId": "305905456234243546", "empName": "袁传芹", "roleName": "薪酬绩效"}], "multiType": "and", "modifyItemDimension": "target", "approverType": "role"}], "modifyItemDimension": "all", "open": 1}, "s3SelfRater": {"signatureFlag": false, "open": 0}, "interviewConf": {"transferFlag": "true", "empRepeatSkip": 0, "notFindAdminBySupAdmin": 1, "templId": "", "templName": "", "interviewConfirmConf": {"isSign": 1, "interviewConfirmInfo": [{"approverInfo": "", "approverName": "被考核人", "raters": [{"empId": "14604374", "type": 0, "empName": "李泊翰"}], "approverType": "taskEmp"}, {"approverInfo": "1", "approverName": "直属主管", "raters": [{"empId": "2853238", "level": 1, "type": 2, "empName": "张树才"}], "approverType": "manager"}, {"approverInfo": "247f37dc-5459-4c6a-8abe-17dff4b7448e", "approverName": "薪酬绩效", "raters": [{"empId": "*********", "roleId": "247f37dc-5459-4c6a-8abe-17dff4b7448e", "type": 0, "empName": "袁传芹", "roleName": "薪酬绩效"}], "approverType": "role"}], "open": 1}, "nodeEmpVacancy": 2, "interviewExcutorInfo": {"approverInfo": "1", "approverName": "直属主管", "raters": [{"empId": "2853238", "level": 1, "type": 2, "empName": "张树才"}], "approverType": "manager"}, "interviewEmpConf": {"type": 2, "conditions": [{"compare": "", "type": 1, "stepIds": ["C:待改进", "D:不胜任"]}]}, "open": 1}, "appealConf": {"resultAppealNode": 50, "canAppealDay": 0, "appealReceiver": [{"objItems": [{"objName": "张树才", "objId": "2853238"}], "objType": "user"}], "open": 1}, "deadLineConf": {"taskResultAudit": {"firstUrgingTime": {"minute": 0, "second": 0, "hour": 10}, "endDate": "2024-08-09", "autoSkip": 0, "schedule": 4, "firstUrgingDay": 1, "secondUrgingTime": {"minute": 0, "second": 0, "hour": 10}, "day": 9, "autoUrging": 1}, "finishInputAudit": {"firstUrgingTime": {"minute": 0, "second": 0, "hour": 10}, "endDate": "2024-08-07", "autoSkip": 0, "schedule": 4, "firstUrgingDay": 1, "secondUrgingTime": {"minute": 0, "second": 0, "hour": 10}, "day": 7, "autoUrging": 1}, "taskConfirmDeadLine": {"firstUrgingTime": {"minute": 0, "second": 0, "hour": 10}, "endDate": "2024-08-05", "autoSkip": 1, "schedule": 4, "firstUrgingDay": 1, "secondUrgingTime": {"minute": 0, "second": 0, "hour": 10}, "day": 5, "autoUrging": 1}, "taskEvalScore": {"firstUrgingTime": {"minute": 0, "second": 0, "hour": 10}, "endDate": "2024-08-08", "autoSkip": 0, "schedule": 4, "firstUrgingDay": 1, "secondUrgingTime": {"minute": 0, "second": 0, "hour": 10}, "day": 8, "autoUrging": 1}, "taskResultConfirm": {"firstUrgingTime": {"minute": 0, "second": 0, "hour": 10}, "endDate": "2024-08-11", "autoSkip": 0, "schedule": 4, "firstUrgingDay": 1, "secondUrgingTime": {"minute": 0, "second": 0, "hour": 10}, "day": 11, "autoUrging": 1}, "taskAffirmDeadLine": {"firstUrgingTime": {"minute": 0, "second": 0, "hour": 10}, "endDate": "2024-06-21", "autoSkip": 0, "schedule": 1, "firstUrgingDay": 1, "secondUrgingTime": {"minute": 0, "second": 0, "hour": 10}, "day": 10, "autoUrging": 1}, "taskResultInterview": {"firstUrgingTime": {"minute": 0, "second": 0, "hour": 10}, "endDate": "2024-08-10", "autoSkip": 0, "schedule": 4, "firstUrgingDay": 1, "secondUrgingTime": {"minute": 0, "second": 0, "hour": 10}, "day": 10, "autoUrging": 1}, "open": 0}, "scoreValueConf": {"baseScore": 0, "exceedFullScore": true, "scoreRangeType": "fullScore", "customFullScore": 120}, "createTotalLevelType": 1, "updatedUser": "*********", "levelGroupId": "", "indicatorCnt": 9, "version": 0, "evaluateType": "custom", "scoreView": {"mutualScoreAnonymous": "true", "mutualScoreViewRule": {"superior": "", "mutual": "", "appoint": "", "examinee": ""}, "superiorScoreAnonymous": "true", "appointScoreViewRule": {"superior": "", "mutual": "", "appoint": "", "examinee": ""}, "superiorScoreViewRule": {"superior": "score,attach", "mutual": "score,attach", "appoint": "score,attach", "examinee": "score,attach"}, "selfScoreViewRule": {"superior": "", "mutual": "", "appoint": "", "examinee": "score,attach"}, "appointScoreAnonymous": "true"}, "companyId": {"id": "1011631"}, "publishResult": {"toEmps": [{"objType": "emp"}], "type": "afterFinished", "scoreDetailPriv": {"scoreRemark": ["self_remark", "item_remark", "peer_remark", "sub_remark", "superior_remark", "appoint_remark"], "scoreType": ["self_score", "superior_score", "peer_score", "appoint_score", "sub_score", "item_score"]}, "opEmps": [{"objItems": [], "objType": "taskAdmin"}], "toDetailEmps": [], "dimension": 11, "open": 1}, "s3SubRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "s3PeerRater": {"change": false, "excludeAllManager": false, "raters": [], "signatureFlag": false, "open": 0}, "confirmResult": {"auto": 0, "sign": 1, "autoDay": 1, "scoreDetailPriv": {"scoreRemark": ["self_remark", "item_remark", "peer_remark", "sub_remark", "superior_remark", "appoint_remark"], "scoreType": ["self_score", "peer_score", "appoint_score", "item_score", "sub_score", "superior_score"]}, "dimension": 11, "open": 1}, "editExeIndi": {"empRepeatSkip": 0, "notFindAdminBySupAdmin": 1, "nodeEmpVacancy": 2, "auditNodes": [], "open": 0}, "askIndexRaters": [], "commentConf": {"scoreSummarySwitch": 0, "commentFlag": "notRequired", "plusOrSubComment": 1}, "showResultType": 6, "empEvalId": "2880379"}}