{"auditSetting": {"empRepeatSkip": 0, "nodeEmpVacancy": 2, "notFindAdminBySupAdmin": 0}, "empRepeatSkip": 0, "interviewConfirmConf": {"interviewConfirmInfo": [{"approverInfo": "1602577", "approverName": "魏郁春", "approverType": "user", "modifyFlag": "false", "raters": [{"avatar": "https://static-legacy.dingtalk.com/media/lADPDhYBP7KmaXrMoMyg_160_160.jpg", "dingUserId": "01054955115039383315", "empId": "1602577", "empName": "魏郁春", "orgId": "1004202", "orgName": "设计", "type": 0}], "roleAudit": false}, {"approverInfo": "1009187", "approverName": "李志旭", "approverType": "user", "modifyFlag": "false", "raters": [{"avatar": "https://static-legacy.dingtalk.com/media/lQLPDhtwSlDwYpfNAmDNAmCwP2waI3J1qnYCiKZNDcDTAA_608_608.png", "dingUserId": "493267573826201284", "empId": "1009187", "empName": "李志旭", "orgId": "1010909", "orgName": "六组,所在三级部门", "type": 0}], "roleAudit": false}], "isSign": 0, "open": 1}, "interviewEmpConf": {"conditions": [], "type": 1}, "interviewExcutorInfo": {"approverInfo": "1602577", "approverName": "魏郁春", "approverType": "user", "raters": [{"empId": "1602577", "empName": "魏郁春", "type": 0}], "roleAudit": false}, "nodeEmpVacancy": 2, "notFindAdminBySupAdmin": 0, "open": 1, "templId": "9839b271-1cca-11ef-a997-000c29165335", "templName": "汉堡法则", "transferFlag": "false"}