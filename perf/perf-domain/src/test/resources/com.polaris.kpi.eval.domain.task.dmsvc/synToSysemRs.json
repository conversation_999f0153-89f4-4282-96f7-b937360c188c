{"ranges": [{"max": 200000, "fieldJson": "[{\"formulaFieldValue\":0,\"formulaFieldName\":\"考核任务得分\",\"companyFieldId\":\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\",\"isSystemField\":\"false\",\"label\":\"考核任务得分\",\"value\":0,\"yVal\":true},{\"label\":\"×\",\"value\":\"*\",\"symbol\":true},{\"label\":0,\"value\":0},{\"label\":\".\",\"value\":\".\"},{\"label\":0,\"value\":0},{\"label\":1,\"value\":1}]", "minAppendEqual": 0, "stepId": "2021211", "version": 0, "coeffType": 1, "scoreRuleId": "", "min": 60, "isDeleted": "false", "stepName": "卓越", "rate": 0, "perfCoefficient": "10", "place": 2}, {"max": 60, "fieldJson": "[{\"formulaFieldValue\":0,\"formulaFieldName\":\"考核任务得分\",\"companyFieldId\":\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\",\"isSystemField\":\"false\",\"label\":\"考核任务得分\",\"value\":0,\"yVal\":true},{\"label\":\"×\",\"value\":\"*\",\"symbol\":true},{\"label\":0,\"value\":0},{\"label\":\".\",\"value\":\".\"},{\"label\":0,\"value\":0},{\"label\":1,\"value\":1}]", "minAppendEqual": 0, "stepId": "2021212", "version": 0, "coeffType": 1, "scoreRuleId": "", "min": 0, "isDeleted": "false", "stepName": "优秀", "rate": 0, "perfCoefficient": "8", "place": 2}], "fieldJson": "[{\"formulaFieldValue\":0,\"formulaFieldName\":\"考核任务得分\",\"companyFieldId\":\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\",\"isSystemField\":\"false\",\"label\":\"考核任务得分\",\"value\":0,\"yVal\":true},{\"label\":\"×\",\"value\":\"*\",\"symbol\":true},{\"label\":0,\"value\":0},{\"label\":\".\",\"value\":\".\"},{\"label\":0,\"value\":0},{\"label\":1,\"value\":1}]", "rankScope": {"type": 1, "scope": -1}, "version": 0, "levelDefType": 1, "coeffType": 1, "system": 0, "isDeleted": "false", "levelRateConf": {"rates": [{"op": "=", "sortNo": 1, "rate": 100, "levels": []}], "startCnt": "5", "open": 0}, "name": "员工等级组", "id": "2039512", "place": 2, "perfCoefficient": "考核任务得分*0.01", "onCycleType": 63}