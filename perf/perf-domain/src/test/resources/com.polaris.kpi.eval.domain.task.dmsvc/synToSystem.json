{"newSnap": {"cycleId": "1815701", "ruleId": "2039512", "id": "1002401", "system": 0, "name": "员工等级组", "onCycleType": 63, "coeffType": 1, "levelDefType": 1, "place": 2, "perfCoefficient": "考核任务得分*0.01", "fieldJson": "[{\"formulaFieldValue\":0,\"formulaFieldName\":\"考核任务得分\",\"companyFieldId\":\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\",\"isSystemField\":\"false\",\"label\":\"考核任务得分\",\"value\":0,\"yVal\":true},{\"label\":\"×\",\"value\":\"*\",\"symbol\":true},{\"label\":0,\"value\":0},{\"label\":\".\",\"value\":\".\"},{\"label\":0,\"value\":0},{\"label\":1,\"value\":1}]", "levelRateConf": {"open": 0, "startCnt": 5, "rates": [{"rate": 100, "op": "=", "sortNo": 1, "levels": [], "exceptCnt": null, "exceptRange": null}]}, "rankScope": {"scope": -1, "type": 1}, "scoreRanges": [{"scoreRuleId": "", "stepId": "2021211", "min": 60, "max": 200000, "coeffType": 1, "place": 2, "rate": "", "fieldJson": "[{\"formulaFieldValue\":0,\"formulaFieldName\":\"考核任务得分\",\"companyFieldId\":\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\",\"isSystemField\":\"false\",\"label\":\"考核任务得分\",\"value\":0,\"yVal\":true},{\"label\":\"×\",\"value\":\"*\",\"symbol\":true},{\"label\":0,\"value\":0},{\"label\":\".\",\"value\":\".\"},{\"label\":0,\"value\":0},{\"label\":1,\"value\":1}]", "perfCoefficient": 10, "minAppendEqual": 0, "snapId": "1002401", "stepName": "卓越"}, {"scoreRuleId": "", "stepId": "2021212", "min": 0, "max": 60, "coeffType": 1, "place": 2, "rate": "", "fieldJson": "[{\"formulaFieldValue\":0,\"formulaFieldName\":\"考核任务得分\",\"companyFieldId\":\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\",\"isSystemField\":\"false\",\"label\":\"考核任务得分\",\"value\":0,\"yVal\":true},{\"label\":\"×\",\"value\":\"*\",\"symbol\":true},{\"label\":0,\"value\":0},{\"label\":\".\",\"value\":\".\"},{\"label\":0,\"value\":0},{\"label\":1,\"value\":1}]", "perfCoefficient": 8, "minAppendEqual": 0, "snapId": "1002401", "stepName": "优秀"}]}, "addEmps": [], "toSystem": 1, "opType": 6}