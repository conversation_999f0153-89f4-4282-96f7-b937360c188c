package com.polaris.kpi.eval.domain.task.entity;

import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;

public class AdminTaskTest {

    @Test
    @DisplayName("需要1自动进入评分")
    public void needAutoEnterScore() {
        String json="{\"readerIds\":[],\"cycleId\":\"2087517\",\"scoreConf\":{\"transferFlag\":\"true\",\"multiType\":\"or\"},\"auditResult\":{\"empRepeatSkip\":0,\"notFindAdminBySupAdmin\":1,\"nodeEmpVacancy\":2,\"auditNodes\":[],\"open\":0},\"performanceType\":1,\"finishValueAudit\":{\"empRepeatSkip\":0,\"notFindAdminBySupAdmin\":1,\"nodeEmpVacancy\":2,\"auditNodes\":[],\"open\":0},\"totalCnt\":44,\"isDeleted\":\"false\",\"adminIds\":[],\"scoreSortConf\":{\"sortItems\":[{\"sort\":1,\"type\":10,\"name\":\"自评\"},{\"sort\":2,\"type\":20,\"name\":\"同级评\"},{\"sort\":3,\"type\":30,\"name\":\"下级评\"},{\"sort\":4,\"type\":40,\"name\":\"上级评分\"},{\"sort\":5,\"type\":50,\"name\":\"指定评\"}],\"exeType\":0},\"tmpTask\":false,\"createdTime\":1727582004000,\"drawUpCnt\":44,\"startCnt\":44,\"id\":\"2104869\",\"enterScore\":{\"enterScoreEmpType\":1,\"enterScoreMethod\":\"auto\",\"scoreStartRuleType\":\"before\",\"scoreStartRuleDay\":1},\"taskStatus\":\"published\",\"createdUser\":\"84791778\",\"updatedTime\":1731030006000,\"confirmTask\":{\"openConfirmLT\":0,\"noChangeSkipFlag\":\"false\",\"empRepeatSkip\":0,\"notFindAdminBySupAdmin\":1,\"confirmLTDay\":1,\"nodeEmpVacancy\":1,\"auditNodes\":[{\"transferFlag\":\"true\",\"approvalOrder\":1,\"modifyFlag\":\"true\",\"approverInfo\":\"\",\"confirmAuditSign\":\"false\",\"approverName\":\"被考核人\",\"raters\":[],\"multiType\":\"or\",\"modifyItemDimension\":\"all\",\"approverType\":\"taskEmp\"},{\"transferFlag\":\"true\",\"approvalOrder\":2,\"modifyFlag\":\"true\",\"approverInfo\":\"1\",\"approverName\":\"直属主管\",\"raters\":[],\"multiType\":\"or\",\"modifyItemDimension\":\"all\",\"approverType\":\"manager\"}],\"modifyItemDimension\":\"all\",\"open\":1},\"interviewConf\":{\"empRepeatSkip\":0,\"notFindAdminBySupAdmin\":1,\"nodeEmpVacancy\":2,\"open\":0},\"appealConf\":{\"open\":0},\"deadLineConf\":{\"open\":0},\"finishCnt\":1,\"updatedUser\":\"\",\"version\":0,\"cycleEndDate\":\"2024-10-31\",\"cycleStartDate\":\"2024-10-01\",\"scoreView\":{\"mutualScoreAnonymous\":\"true\",\"mutualScoreViewRule\":{\"superior\":\"\",\"mutual\":\"\",\"appoint\":\"\",\"examinee\":\"\"},\"superiorScoreAnonymous\":\"true\",\"appointScoreViewRule\":{\"superior\":\"\",\"mutual\":\"\",\"appoint\":\"\",\"examinee\":\"\"},\"superiorScoreViewRule\":{\"superior\":\"score,attach\",\"mutual\":\"score,attach\",\"appoint\":\"score,attach\",\"examinee\":\"score,attach\"},\"selfScoreViewRule\":{\"superior\":\"\",\"mutual\":\"\",\"appoint\":\"\",\"examinee\":\"score,attach\"},\"appointScoreAnonymous\":\"true\"},\"companyId\":{\"id\":\"1032278\"},\"publishResult\":{\"toEmps\":[{\"objType\":\"emp\"},{\"objType\":\"scoreEmp\"}],\"type\":\"afterFinished\",\"scoreDetailPriv\":{\"scoreRemark\":[\"self_remark\",\"superior_remark\"],\"scoreType\":[\"self_score\",\"superior_score\"]},\"opEmps\":[],\"toDetailEmps\":[],\"dimension\":11,\"open\":1},\"readers\":[],\"confirmResult\":{\"auto\":0,\"sign\":0,\"scoreDetailPriv\":{\"scoreRemark\":[\"self_remark\",\"superior_remark\"],\"scoreType\":[\"self_score\",\"superior_score\"]},\"dimension\":11,\"open\":1},\"editExeIndi\":{\"empRepeatSkip\":0,\"notFindAdminBySupAdmin\":1,\"nodeEmpVacancy\":2,\"auditNodes\":[],\"open\":0},\"taskName\":\"2024年10月线下事业中心-城市主管&城市业务考核\",\"commentConf\":{\"scoreSummarySwitch\":-1,\"commentFlag\":\"notRequired\",\"plusOrSubComment\":0},\"inputNotifyConf\":{\"sendType\":1},\"admins\":[]}";
        //2024-10-01 cycle_start_date    cycle_end_date 2024-10-31
        AdminTask task = JSONUtil.toBean(json,AdminTask.class);
        boolean bol = task.needAutoEnterScore("2024-10-01", "2024-10-31");
        System.out.println(bol);
    }
}
