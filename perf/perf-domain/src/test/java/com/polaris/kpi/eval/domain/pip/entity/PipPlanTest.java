package com.polaris.kpi.eval.domain.pip.entity;

import cn.com.polaris.kpi.EmpStaff;
import cn.com.polaris.kpi.eval.Rater;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.polaris.kpi.eval.domain.pip.plan.entity.*;
import com.polaris.kpi.eval.domain.pip.temp.entity.PipField;
import com.polaris.kpi.eval.domain.pip.temp.entity.PipTempl;
import com.polaris.kpi.eval.domain.pip.temp.entity.PipTemplType;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;


public class PipPlanTest {

    @Test
    public void createPipPlan(){
        String companyId = "ece4e403-43aa-47f2-bb19-a0dd18b8e98d";
        String opEmpId = "1299001";
        //List<EmpStaff> empStaffs, PipTempl templ
        List<EmpStaff> empStaffs = new ArrayList<>();
        EmpStaff staff = new EmpStaff("1","张三");
        staff.setEmpAvatar("1333333321");
        EmpStaff staff2 = new EmpStaff("2","2222");
        staff2.setEmpAvatar("23232");
        empStaffs.add(staff2);

        PipTempl templ = new PipTempl("测试用例改进模板1");
        templ.accUp(companyId,opEmpId);

        List<PipTemplType> types = new ArrayList<>();
        PipTemplType type = new PipTemplType("peix","s",1);
        List<PipField> fields= new ArrayList<>();
        PipField field = new PipField("字段1","table",1,1,new ArrayList<>(),"");
        fields.add(field);

        PipField field2 = new PipField("字段22","table",1,1,new ArrayList<>(),"");
        fields.add(field2);
        type.setFields(fields);
        types.add(type);

        PipTemplType type2 = new PipTemplType("mub","s",1);
        List<PipField> fieldstype1= new ArrayList<>();
        PipField fieldtype1 = new PipField("字段1","table",1,1,new ArrayList<>(),"");
        fieldstype1.add(fieldtype1);

        PipField fieldtype2= new PipField("字段22","table",1,1,new ArrayList<>(),"");
        fieldstype1.add(fieldtype2);
        type.setFields(fieldstype1);
        types.add(type2);

        templ.setTypes(types);

        PipPlan pipPlan = new PipPlan();
        List<PipPlan> list = pipPlan.createPipPlan(empStaffs, templ);
        System.out.println(JSONUtil.toJsonStr(list));
    }

    @Test
    public void initEvalChain() {
        String companyId = "ece4e403-43aa-47f2-bb19-a0dd18b8e98d";
        String opEmpId = "1299001";
        String pipEvalId = "1000203";
        PipGoalConfirmConf goalConfirmRater = new PipGoalConfirmConf();
        goalConfirmRater.setOpen(1);
        List<PipGoalAuditNode> auditNodes = new ArrayList<>();
        PipGoalAuditNode auditNode1= new PipGoalAuditNode();
        auditNode1.setNodeTitle("目标制定");
        auditNode1.setNodeType("goal_created");
        auditNode1.setApprovalOrder(1);
        auditNode1.setApproverType("taskEmp");
        auditNode1.setApproverInfo("1299001");
        auditNode1.setApproverName("张三");
        List<Rater> raters = new ArrayList<>();
        raters.add(new Rater("111","张三"));
        auditNode1.setRaters(raters);
        auditNodes.add(auditNode1);

        PipGoalAuditNode auditNode2= new PipGoalAuditNode();
        auditNode2.setNodeTitle("目标确定");
        auditNode2.setNodeType("goal_created");
        auditNode2.setApprovalOrder(1);
        auditNode2.setApproverType("taskEmp");
        auditNode2.setApproverInfo("1299001");
        auditNode2.setApproverName("张三");
        List<Rater> raters2 = new ArrayList<>();
        raters2.add(new Rater("111","张三"));
        auditNode2.setRaters(raters2);
        auditNodes.add(auditNode2);

        goalConfirmRater.setAuditNodes(auditNodes);

        PipGoalExcuteConf excuteRater = new PipGoalExcuteConf();
        excuteRater.setOpen(1);
        excuteRater.setAuditNodes(new ArrayList<>());

        PipScoreConf scoreRater = new PipScoreConf();
        scoreRater.setOpen(1);
        List<PipScoreAuditNode> scoreAuditNodes = new ArrayList<>();
        PipScoreAuditNode scoreauditNode1= new PipScoreAuditNode();
        scoreauditNode1.setNodeTitle("目标制定");
        scoreauditNode1.setNodeType("goal_created");
        scoreauditNode1.setApprovalOrder(1);
        scoreauditNode1.setApproverType("taskEmp");
        scoreauditNode1.setApproverInfo("1299001");
        scoreauditNode1.setApproverName("张三");
        List<Rater> scoreraters1 = new ArrayList<>();
        raters.add(new Rater("111","张三"));
        auditNode1.setRaters(scoreraters1);
        scoreAuditNodes.add(scoreauditNode1);

        PipScoreAuditNode scoreauditNode2= new PipScoreAuditNode();
        scoreauditNode2.setNodeTitle("目标确定");
        scoreauditNode2.setNodeType("goal_created");
        scoreauditNode2.setApprovalOrder(1);
        scoreauditNode2.setApproverType("taskEmp");
        scoreauditNode2.setApproverInfo("1299001");
        scoreauditNode2.setApproverName("张三");
        List<Rater> scoreraters2 = new ArrayList<>();
        raters2.add(new Rater("111","张三"));
        scoreauditNode2.setRaters(scoreraters2);
        scoreAuditNodes.add(scoreauditNode2);

        PipScoreAuditNode scoreauditNode3= new PipScoreAuditNode();
        scoreauditNode3.setNodeTitle("目标确定");
        scoreauditNode3.setNodeType("goal_created");
        scoreauditNode3.setApprovalOrder(1);
        scoreauditNode3.setApproverType("taskEmp");
        scoreauditNode3.setApproverInfo("1299001");
        scoreauditNode3.setApproverName("张三");
        List<Rater> scoreraters3 = new ArrayList<>();
        raters2.add(new Rater("111","张三"));
        scoreauditNode3.setRaters(scoreraters3);
        scoreAuditNodes.add(scoreauditNode3);

        scoreRater.setAuditNodes(scoreAuditNodes);

        PipResultAffirmConf resultAffirmConf = new PipResultAffirmConf();
        scoreRater.setOpen(1);
        resultAffirmConf.setResultConfirmType(1);
        PipEvalRule rule = new PipEvalRule(companyId,opEmpId,"1299001",pipEvalId,goalConfirmRater,excuteRater,scoreRater,resultAffirmConf);

        PipEval eval = new PipEval("1299001","张三","","","","","","","","");
        eval.setEvalRule(rule);
        eval.initEvalChain(companyId,opEmpId);
        System.out.println(JSONUtil.toJsonStr(eval.getChain()));
    }
}