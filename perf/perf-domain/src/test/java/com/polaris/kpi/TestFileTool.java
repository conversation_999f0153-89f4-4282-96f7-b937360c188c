package com.polaris.kpi;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONUtil;
import com.polaris.sdk.common.JsonFileTool;

import java.net.URL;
import java.util.List;

public class TestFileTool {


    public static <T> T toBean(Class pathCls, String fileName, Class<T> cls) {
        //"com.polaris.kpi.eval.app.task.appsvc/testEmpEvalAppSvc/addScoreStageRule.json"
        URL addScoreStageRule = pathCls.getResource(fileName);
        System.out.println(pathCls.getResource(".").getPath());
        String ruleStr = FileUtil.readString(addScoreStageRule.getPath(), "utf-8");
        T cmd = JSONUtil.toBean(ruleStr, cls);
        return cmd;
    }

    public static <T> List<T> toList(Class pathCls, String fileName, Class<T> cls) {
        //"com.polaris.kpi.eval.app.task.appsvc/testEmpEvalAppSvc/addScoreStageRule.json"
        URL addScoreStageRule = pathCls.getResource(fileName);
        System.out.println(pathCls.getResource(".").getPath());
        String ruleStr = FileUtil.readString(addScoreStageRule.getPath(), "utf-8");
        List<T> list = JSONUtil.toList(ruleStr, cls);
        return list;
    }
}
