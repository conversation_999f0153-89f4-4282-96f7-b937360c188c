package com.polaris.kpi.eval.domain.task.entity.empeval;

import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.AffirmTaskConf;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.AuditResultConf;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.jupiter.api.Test;

public class ResetNodeCheckTest {

    @Test
    public void should_return_false_when_eval_rule_is_null() {
        //given
        EvalUser taskUser = buildScoringEvalUser();
        taskUser.setEmpEvalRule(null);
        //when
        boolean b = taskUser.notExistResetNode(TalentStatus.SCORING.getStatus());
        //then
        Assert.assertFalse(b);
    }

    @Test
    public void should_return_true_when_current_status_is_before_reset_status() {
        //given
        EvalUser taskUser = buildScoringEvalUser();
        //when
        boolean b = taskUser.notExistResetNode(TalentStatus.RESULTS_AUDITING.getStatus());
        //then
        Assert.assertTrue(b);
    }

    @Test
    public void should_return_false_when_confirm_task_is_open() {
        //given
        EvalUser taskUser = buildScoringEvalUser();
        EmpEvalRule empEvalRule = new EmpEvalRule();
        empEvalRule.setConfirmTask(buildOpenConfirmTask());
        taskUser.setEmpEvalRule(empEvalRule);
        //when
        boolean b = taskUser.notExistResetNode(TalentStatus.CONFIRMING.getStatus());
        //then
        Assert.assertFalse(b);
    }

    @Test
    public void should_return_true_when_confirm_task_is_close() {
        //given
        EvalUser taskUser = buildScoringEvalUser();
        EmpEvalRule empEvalRule = new EmpEvalRule();
        empEvalRule.setConfirmTask(buildCloseConfirmTask());
        taskUser.setEmpEvalRule(empEvalRule);
        //when
        boolean b = taskUser.notExistResetNode(TalentStatus.CONFIRMING.getStatus());
        //then
        Assert.assertTrue(b);
    }

    @Test
    public void should_return_false_when_audit_result_is_open() {
        //given
        EvalUser taskUser = buildFinishedEvalUser();
        EmpEvalRule empEvalRule = new EmpEvalRule();
        empEvalRule.setAuditResult(buildOpenAuditResult());
        taskUser.setEmpEvalRule(empEvalRule);
        //when
        boolean b = taskUser.notExistResetNode(TalentStatus.RESULTS_AUDITING.getStatus());
        //then
        Assert.assertFalse(b);
    }

    @Test
    public void should_return_true_when_audit_result_is_close() {
        //given
        EvalUser taskUser = buildFinishedEvalUser();
        EmpEvalRule empEvalRule = new EmpEvalRule();
        empEvalRule.setAuditResult(buildCloseAuditResult());
        taskUser.setEmpEvalRule(empEvalRule);

        //when
        boolean b = taskUser.notExistResetNode(TalentStatus.RESULTS_AUDITING.getStatus());
        //then
        Assert.assertTrue(b);
    }

    @NotNull
    private EvalUser buildScoringEvalUser() {
        EvalUser taskUser = new EvalUser();
        taskUser.setTaskStatus(TalentStatus.SCORING.getStatus());
        return taskUser;
    }

    @NotNull
    private EvalUser buildFinishedEvalUser() {
        EvalUser taskUser = new EvalUser();
        taskUser.setTaskStatus(TalentStatus.FINISHED.getStatus());
        return taskUser;
    }

    private AffirmTaskConf buildOpenConfirmTask() {
        AffirmTaskConf affirmTaskConf = new AffirmTaskConf();
        affirmTaskConf.setOpen(1);
        return affirmTaskConf;
    }

    private AffirmTaskConf buildCloseConfirmTask() {
        AffirmTaskConf affirmTaskConf = new AffirmTaskConf();
        affirmTaskConf.setOpen(0);
        return affirmTaskConf;
    }

    private AuditResultConf buildOpenAuditResult() {
        AuditResultConf auditResultConf = new AuditResultConf();
        auditResultConf.setOpen(1);
        return auditResultConf;
    }

    private AuditResultConf buildCloseAuditResult() {
        AuditResultConf auditResultConf = new AuditResultConf();
        auditResultConf.setOpen(0);
        return auditResultConf;
    }
}
