package com.polaris.kpi.eval.domain.task.entity.empeval.ecfg;

import org.junit.Assert;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

class ScoreValueConfTest {

    @Test
    @DisplayName("自定上限")
    void finalScoreLimit1() {
        BigDecimal customFullScore = new BigDecimal(99);
        ScoreValueConf scoreValueConf = new ScoreValueConf(customFullScore, true, "fullScore", BigDecimal.ZERO);
        BigDecimal limit = scoreValueConf.finalScoreLimit(new BigDecimal(100), new BigDecimal(100));
        System.out.println(limit);
        Assert.assertTrue(limit.compareTo(customFullScore) == 0);

        BigDecimal srcFinalScore = new BigDecimal(80);
        limit = scoreValueConf.finalScoreLimit(new BigDecimal(100), srcFinalScore);
        System.out.println(limit);
        Assert.assertTrue(limit.compareTo(srcFinalScore) == 0);
    }

    @Test
    @DisplayName("系统上限")
    void finalScoreLimit2() {
        ScoreValueConf scoreValueConf = new ScoreValueConf(null, true, "fullScore", BigDecimal.ZERO);
        BigDecimal sysFullScore = new BigDecimal(100);
        BigDecimal limit = scoreValueConf.finalScoreLimit(sysFullScore, new BigDecimal(200));
        System.out.println(limit);
        Assert.assertTrue(limit.compareTo(sysFullScore) == 0);

        BigDecimal srcFinalScore = new BigDecimal(89);
        limit = scoreValueConf.finalScoreLimit(sysFullScore, srcFinalScore);
        System.out.println(limit);
        Assert.assertTrue(limit.compareTo(srcFinalScore) == 0);
    }

    @Test
    @DisplayName("null")
    void finalScoreLimit3() {
        ScoreValueConf scoreValueConf = new ScoreValueConf(null, true, "fullScore", BigDecimal.ZERO);
        BigDecimal sysFullScore = new BigDecimal(100);
        BigDecimal limit = scoreValueConf.finalScoreLimit(sysFullScore, new BigDecimal(200));
        System.out.println(limit);
        Assert.assertTrue(limit.compareTo(sysFullScore) == 0);

        BigDecimal srcFinalScore = new BigDecimal(89);
        limit = scoreValueConf.finalScoreLimit(null, srcFinalScore);
        System.out.println(limit);
        Assert.assertTrue(limit.compareTo(srcFinalScore) == 0);
    }
}