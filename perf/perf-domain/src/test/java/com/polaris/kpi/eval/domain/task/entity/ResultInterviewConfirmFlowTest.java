package com.polaris.kpi.eval.domain.task.entity;

import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.BaseAuditNode;
import com.polaris.kpi.eval.domain.task.entity.interview.ResultInterviewConfirmFlow;
import com.polaris.sdk.common.JsonFileTool;
import com.polaris.sdk.type.TenantId;
import org.junit.Test;

import java.util.List;

public class ResultInterviewConfirmFlowTest {

    @Test
    public void getResultInterviewConfirmFlow() {
        List<BaseAuditNode> confirmNodes = JsonFileTool.toList("com.polaris.kpi.eval.domain.task.dmsvc/listInterviewConfirmFlowNode.json", BaseAuditNode.class);
        ResultInterviewConfirmFlow flow = new ResultInterviewConfirmFlow("5a031297-1b38-48ae-bc82-375849835203", "1602577", "1351501","false",0, confirmNodes);

       // flow.initConfirmFlowNodes(confirmNodes);
        System.out.println(JSONUtil.toJsonStr(flow));
    }
}
