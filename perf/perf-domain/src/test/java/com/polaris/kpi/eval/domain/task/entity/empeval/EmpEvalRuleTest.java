

package com.polaris.kpi.eval.domain.task.entity.empeval;

import cn.com.polaris.kpi.ObjItem;
import cn.com.polaris.kpi.eval.Rater;
import cn.com.polaris.kpi.eval.StaffConfItem;
import cn.com.polaris.kpi.temp.PubExcutor;
import cn.com.polaris.kpi.temp.PubToRater;
import cn.com.polaris.kpi.temp.PublicTypeEnum;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.polaris.kpi.eval.domain.confirm.entity.ConfirmNode;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.*;
import com.polaris.sdk.type.TenantId;
import org.junit.Assert;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;


public class EmpEvalRuleTest {
    //360与简易考核规则
    @Test
    public void simpleEmpEvalRule() {
        EmpEvalRule evalRule = new EmpEvalRule();
        evalRule.setEmpEvalId("1052203");
        evalRule.setCompanyId(new TenantId("ece4e403-43aa-47f2-bb19-a0dd18b8e98d"));
        evalRule.setRuleName("考核规则名字");
        evalRule.setEvaluateType("simple");

        TypeWeightConf typeWeightConf = new TypeWeightConf();
        typeWeightConf.setOpen(1);
        typeWeightConf.setLimit100Weight(1);
        evalRule.setTypeWeightConf(typeWeightConf);
        {
            //确认任务
            AffirmTaskConf confirmTask = new AffirmTaskConf();
            confirmTask.setOpen(1);
            confirmTask.setNoChangeSkipFlag("true");
            List<ConfirmNode> auditNodes = new ArrayList<>();
            ConfirmNode node1 = new ConfirmNode(1, "taskEmp");
            node1.allowOperation("false", "true");
            auditNodes.add(node1);
            confirmTask.setAuditNodes(auditNodes);
            evalRule.setConfirmTask(confirmTask);
        }

        {//编辑执行中的指标
            EditExeIndiConf editExeIndi = new EditExeIndiConf();
            editExeIndi.setOpen(1);
            editExeIndi.setChangeUsers(Arrays.asList("taskEmp"));
            editExeIndi.setAuditOpen(0);
            List<BaseAuditNode> auditNodes2 = new ArrayList<>();
            Rater rater = new Rater("1093003", "Mr. wei", new BigDecimal(100));
            BaseAuditNode node1 = new BaseAuditNode(1, BaseAuditNode.MULTI_OR, rater);
            node1.allowOperation("false", "true");
            auditNodes2.add(node1);
            editExeIndi.setAuditNodes(auditNodes2);
            evalRule.setEditExeIndi(editExeIndi);
        }

        {//启动评分
            EnterScoreConf enterScore = new EnterScoreConf();
            enterScore.setEnterScoreMethod("auto");
            enterScore.setScoreStartRuleType("before");
            enterScore.setScoreStartRuleDay(5);
            evalRule.setEnterScore(enterScore);
        }
        {   //自评
            RaterNodeConf simpleSelfScorer = new RaterNodeConf();
            simpleSelfScorer.setRateMode("item");
            simpleSelfScorer.setNodeWeight(new BigDecimal(20));
            simpleSelfScorer.setAnonymous("true");
            //simpleSelfScorer.setVacancyType("user");//"superior","admin","user"
            //simpleSelfScorer.setVacancyRater("1093003");//"superior","admin","user"
            evalRule.setS3SelfRater(simpleSelfScorer);
        }

        {   //互评人
           //MutualRaterConf s3MutualRaterConf = new MutualRaterConf("true", "item", true, MutualRaterConf.PEER_AND_SUB);
           //MutualNodeConf peerNode = new MutualNodeConf(1, BaseAuditNode.MULTI_AND);
           //peerNode.rater("1093003", "Mr. wei");
           //s3MutualRaterConf.setPeerNode(peerNode);

           //MutualNodeConf subNode = new MutualNodeConf(2, BaseAuditNode.MULTI_AND);
           //subNode.rater("1216002", "老詹");
           //s3MutualRaterConf.setSubNode(subNode);
           ////evalRule.setS3MutualRater(s3MutualRaterConf);
        }


        {   //上级评
            S3SuperRaterConf s3SuperRaterConf = new S3SuperRaterConf("true", "item", new BigDecimal(50), true, S3SuperRaterConf.IN_TURN);
            List<BaseAuditNode> auditNodes = new ArrayList<>();
            BaseAuditNode node1 = new BaseAuditNode(1, BaseAuditNode.MULTI_OR);
            node1.addRater(new Rater(1, 1, "1093003", "Mr. wei", new BigDecimal(80)));

            auditNodes.add(node1);

            BaseAuditNode node2 = new BaseAuditNode(2, BaseAuditNode.MULTI_OR);
            node2.addRater(new Rater(1, 1, "1216002", "老詹", new BigDecimal(20)));
            auditNodes.add(node2);
            s3SuperRaterConf.setAuditNodes(auditNodes);
            evalRule.setS3SuperRater(s3SuperRaterConf);
        }
        evalRule.setConfirmResult(new ConfirmResultConf(1, 1, 2));

        List<PubExcutor> opEmps = new ArrayList<>();
        opEmps.add(new PubExcutor(PubExcutor.OBJ_TYPE_ADMIN));
        List<PubToRater> toEmps = new ArrayList<>();
        toEmps.add(new PubToRater(PubToRater.OBJ_TYPE_SELF));
        PublishResultConf publishResult = new PublishResultConf(PublicTypeEnum.manual, opEmps, toEmps, 3);
        evalRule.setPublishResult(publishResult);

        ArrayList<StaffConfItem> appealReceiver = new ArrayList<>();
        appealReceiver.add(new StaffConfItem("user", Arrays.asList(new ObjItem("1216002", "老詹"))));
//        evalRule.setAppealConf(new AppealConf(70, appealReceiver));

        evalRule.setScoreValueConf(new ScoreValueConf(null, false, "fullScore", BigDecimal.ZERO));
        String str = "[{\"kpiTypeName\": \"默认类别\", \"kpiTypeWeight\": \"100\", \"isOkr\": \"false\", \"typeOrder\": 0, \"reserveOkrWeight\": null, \"items\": [{\"order\": 0, \"inputFormat\": null, \"kpiItemId\": \"e90b9cf4-ba81-417a-8160-59fbf4400962\", \"kpiItemName\": \"测试自定义标签\", \"itemType\": \"measurable\", \"itemValue\": 0, \"itemUnit\": \"平方米\", \"parentKpiTypeId\": null, \"itemRule\": \"没有\", \"scoringRule\": \"没有\", \"resultInputType\": \"exam\", \"mustResultInput\": false, \"resultInputUserId\": \"\", \"managerLevel\": \"\", \"scorerObjId\": \"[{\\\"obj_type\\\":\\\"role\\\",\\\"objItems\\\":[]},{\\\"obj_type\\\":\\\"user\\\",\\\"objItems\\\":[]}]\", \"scorerType\": \"exam\", \"itemFormula\": \"\", \"formulaFieldList\": [], \"formulaCondition\": \"[]\", \"thresholdJson\": \"[]\", \"itemWeight\": 100, \"multipleReviewersType\": \"or\", \"plusLimit\": 0, \"subtractLimit\": 0, \"itemFieldJson\": \"[]\", \"itemScoreValue\": null, \"showTargetValue\": \"false\", \"showFinishBar\": 1, \"itemFullScoreCfg\": \"false\", \"isNewEmp\": 0}]}]";

        evalRule.setKpiTypes(new KpiListWrap(JSON.parseArray(str, EmpEvalKpiType.class)) );
        System.out.println(JSON.toJSONString(evalRule));

    }


    @Test
    public void builderRaterSkipType() {
        AuditResultConf resultConf = JSONUtil.toBean("{\"auditNodes\":[{\"approvalOrder\":1,\"approverInfo\":\"1602589\",\"approverName\":\"杨思威\",\"approverType\":\"user\",\"confirmAuditSign\":null,\"id\":null,\"modifyFlag\":null,\"modifyItemDimension\":null,\"multiType\":\"or\",\"node\":\"final_result_audit\",\"nodeTitle\":null,\"nodeType\":null,\"raters\":[{\"avatar\":null,\"dingUserId\":null,\"empId\":\"1602589\",\"empName\":\"杨思威\",\"level\":null,\"type\":0,\"weight\":\"\",\"roleId\":null,\"roleName\":null,\"unionId\":\"null_1602589\"}],\"roleAudit\":false,\"scoreWeight\":null,\"status\":null,\"transferFlag\":null,\"vacancyApproverInfo\":null,\"vacancyApproverType\":null,\"weight\":\"\"},{\"approvalOrder\":2,\"approverInfo\":\"1024060\",\"approverName\":\"杨思威\",\"approverType\":\"user\",\"confirmAuditSign\":null,\"id\":null,\"modifyFlag\":null,\"modifyItemDimension\":null,\"multiType\":\"or\",\"node\":\"final_result_audit\",\"nodeTitle\":null,\"nodeType\":null,\"raters\":[{\"avatar\":null,\"dingUserId\":null,\"empId\":\"1024060\",\"empName\":\"吴武\",\"level\":null,\"type\":0,\"weight\":\"\",\"roleId\":null,\"roleName\":null,\"unionId\":\"null_1024060\"}],\"roleAudit\":false,\"scoreWeight\":null,\"status\":null,\"transferFlag\":null,\"vacancyApproverInfo\":null,\"vacancyApproverType\":null,\"weight\":\"\"},{\"approvalOrder\":3,\"approverInfo\":\"1602589\",\"approverName\":\"杨思威\",\"approverType\":\"user\",\"confirmAuditSign\":null,\"id\":null,\"modifyFlag\":null,\"modifyItemDimension\":null,\"multiType\":\"or\",\"node\":\"final_result_audit\",\"nodeTitle\":null,\"nodeType\":null,\"raters\":[{\"avatar\":null,\"dingUserId\":null,\"empId\":\"1602589\",\"empName\":\"杨思威\",\"level\":null,\"type\":0,\"weight\":\"\",\"roleId\":null,\"roleName\":null,\"unionId\":\"null_1602589\"}],\"roleAudit\":false,\"scoreWeight\":null,\"status\":null,\"transferFlag\":null,\"vacancyApproverInfo\":null,\"vacancyApproverType\":null,\"weight\":\"\"},{\"approvalOrder\":4,\"approverInfo\":\"\",\"approverName\":\"杨思威\",\"approverType\":\"user\",\"confirmAuditSign\":null,\"id\":null,\"modifyFlag\":null,\"modifyItemDimension\":null,\"multiType\":\"or\",\"node\":\"final_result_audit\",\"nodeTitle\":null,\"nodeType\":null,\"raters\":[{\"avatar\":\"https://static-legacy.dingtalk.com/media/lADPM5HikxN4fzvNA6PNA-4_1006_931.jpg\",\"dingUserId\":\"056410084579655806\",\"empId\":\"1602582\",\"empName\":\"Sasha\",\"type\":0,\"weight\":\"\",\"unionId\":\"undefined_1602582\"}],\"roleAudit\":false,\"scoreWeight\":null,\"status\":null,\"transferFlag\":null,\"vacancyApproverInfo\":null,\"vacancyApproverType\":null,\"weight\":\"\"}],\"auditSetting\":{\"empRepeatSkip\":0,\"nodeEmpVacancy\":1,\"notFindAdminBySupAdmin\":0},\"collectSendNotify\":2,\"commentReq\":0,\"empRepeatSkip\":0,\"mergeConf\":null,\"multiType\":\"or\",\"nodeEmpVacancy\":1,\"notFindAdminBySupAdmin\":0,\"open\":1,\"transferFlag\":\"true\",\"vacancyApproveInfo\":\"\",\"vacancyApproveName\":\"\",\"vacancyApproveType\":\"superior\"}",AuditResultConf.class);
        EmpEvalRule evalRule = new EmpEvalRule();
        evalRule.setAuditResult(JSONUtil.toBean("{\"auditNodes\":[{\"approvalOrder\":1,\"approverInfo\":\"1602589\",\"approverName\":\"杨思威\",\"approverType\":\"user\",\"confirmAuditSign\":null,\"id\":null,\"modifyFlag\":null,\"modifyItemDimension\":null,\"multiType\":\"or\",\"node\":\"final_result_audit\",\"nodeTitle\":null,\"nodeType\":null,\"raters\":[{\"avatar\":null,\"dingUserId\":null,\"empId\":\"1602589\",\"empName\":\"杨思威\",\"level\":null,\"type\":0,\"weight\":\"\",\"roleId\":null,\"roleName\":null,\"unionId\":\"null_1602589\"}],\"roleAudit\":false,\"scoreWeight\":null,\"status\":null,\"transferFlag\":null,\"vacancyApproverInfo\":null,\"vacancyApproverType\":null,\"weight\":\"\"},{\"approvalOrder\":2,\"approverInfo\":\"1024060\",\"approverName\":\"杨思威\",\"approverType\":\"user\",\"confirmAuditSign\":null,\"id\":null,\"modifyFlag\":null,\"modifyItemDimension\":null,\"multiType\":\"or\",\"node\":\"final_result_audit\",\"nodeTitle\":null,\"nodeType\":null,\"raters\":[{\"avatar\":null,\"dingUserId\":null,\"empId\":\"1024060\",\"empName\":\"吴武\",\"level\":null,\"type\":0,\"weight\":\"\",\"roleId\":null,\"roleName\":null,\"unionId\":\"null_1024060\"}],\"roleAudit\":false,\"scoreWeight\":null,\"status\":null,\"transferFlag\":null,\"vacancyApproverInfo\":null,\"vacancyApproverType\":null,\"weight\":\"\"},{\"approvalOrder\":3,\"approverInfo\":\"1602589\",\"approverName\":\"杨思威\",\"approverType\":\"user\",\"confirmAuditSign\":null,\"id\":null,\"modifyFlag\":null,\"modifyItemDimension\":null,\"multiType\":\"or\",\"node\":\"final_result_audit\",\"nodeTitle\":null,\"nodeType\":null,\"raters\":[{\"avatar\":null,\"dingUserId\":null,\"empId\":\"1602589\",\"empName\":\"杨思威\",\"level\":null,\"type\":0,\"weight\":\"\",\"roleId\":null,\"roleName\":null,\"unionId\":\"null_1602589\"}],\"roleAudit\":false,\"scoreWeight\":null,\"status\":null,\"transferFlag\":null,\"vacancyApproverInfo\":null,\"vacancyApproverType\":null,\"weight\":\"\"},{\"approvalOrder\":4,\"approverInfo\":\"\",\"approverName\":\"杨思威\",\"approverType\":\"user\",\"confirmAuditSign\":null,\"id\":null,\"modifyFlag\":null,\"modifyItemDimension\":null,\"multiType\":\"or\",\"node\":\"final_result_audit\",\"nodeTitle\":null,\"nodeType\":null,\"raters\":[{\"avatar\":\"https://static-legacy.dingtalk.com/media/lADPM5HikxN4fzvNA6PNA-4_1006_931.jpg\",\"dingUserId\":\"056410084579655806\",\"empId\":\"1602582\",\"empName\":\"Sasha\",\"type\":0,\"weight\":\"\",\"unionId\":\"undefined_1602582\"}],\"roleAudit\":false,\"scoreWeight\":null,\"status\":null,\"transferFlag\":null,\"vacancyApproverInfo\":null,\"vacancyApproverType\":null,\"weight\":\"\"}],\"auditSetting\":{\"empRepeatSkip\":0,\"nodeEmpVacancy\":1,\"notFindAdminBySupAdmin\":0},\"collectSendNotify\":2,\"commentReq\":0,\"empRepeatSkip\":0,\"mergeConf\":null,\"multiType\":\"or\",\"nodeEmpVacancy\":1,\"notFindAdminBySupAdmin\":0,\"open\":1,\"transferFlag\":\"true\",\"vacancyApproveInfo\":\"\",\"vacancyApproveName\":\"\",\"vacancyApproveType\":\"superior\"}",AuditResultConf.class));
        evalRule.builderRaterSkipType("final_result_audit");
        Assert.assertTrue(Objects.equals(JSONUtil.toJsonStr(resultConf),JSONUtil.toJsonStr(evalRule.getAuditResult())));
    }
}