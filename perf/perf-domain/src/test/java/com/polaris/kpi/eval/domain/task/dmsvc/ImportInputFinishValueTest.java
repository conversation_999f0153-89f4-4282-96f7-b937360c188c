package com.polaris.kpi.eval.domain.task.dmsvc;

import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.perf.www.common.utils.file.FileUtil;
import com.polaris.kpi.eval.domain.task.entity.empeval.ExcelFinishValue;
import com.polaris.sdk.common.JsonFileTool;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;
import org.junit.jupiter.api.Test;
import org.springframework.core.io.ClassPathResource;

import java.util.*;

public class ImportInputFinishValueTest {


    @Data
    public class BaseExcelData {

        private Map<Integer, String> head;
        private Map<String, Integer> headIndex;
        private List<Map<String, String>> datas = new ArrayList();

        public void setHead(Map<Integer, String> head) {
            this.head = head;
            headIndex = new HashMap<>();
            head.forEach((key, value) -> {
                headIndex.put(value, key);
            });
        }

        public String getValue(Map cellMap, String columName) {
            final Integer index = this.getHeadIndex().get(columName);
            final String value = MapUtils.getString(cellMap, index);
            return value;
        }

        public void addData(Map data) {
            datas.add(data);
        }
    }

    @Test
    public void improtInputFinishValue() {
        BaseExcelData importData = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.dmsvc/baseExcelData.json", BaseExcelData.class);
        List<Map<String, String>> datas = importData.getDatas();
        for (Map<String, String> map : datas) {
            // 先收集需要删除的键（避免遍历时修改导致的异常）
            List<String> keysToRemove = new ArrayList<>();
            for (Map.Entry<String, String> entry : map.entrySet()) {
                if (Objects.equals("-", entry.getValue()) || (Integer.valueOf(entry.getKey()) > 0 && Integer.valueOf(entry.getKey()) < 9)) {
                    keysToRemove.add(entry.getKey());
                }
            }
            // 统一删除所有需要移除的键
            for (String key : keysToRemove) {
                map.remove(key);
            }
        }
        System.out.println(JSONUtil.toJsonStr(datas));
        Map<Integer, String> head = importData.getHead();
        // 先收集需要删除的键（避免遍历时修改导致的异常）
        List<Integer> keysToRemove = new ArrayList<>();
        for (Map.Entry<Integer, String> entry : head.entrySet()) {
            if (Integer.valueOf(entry.getKey()) > 0 && Integer.valueOf(entry.getKey()) < 9) {
                keysToRemove.add(entry.getKey());
            }
        }
        // 统一删除所有需要移除的键
        for (Integer key : keysToRemove) {
            head.remove(key);
        }
        System.out.println(JSONUtil.toJsonStr(head));
        List<ExcelFinishValue> finishValues = new ArrayList<>();
        for (Map<String, String> map : datas) {
            List<Map<String, String>> maps = groupMapByKeyProximity(map, 1, 2);
            for (Map<String, String> stringMap : maps) {
                if (stringMap.size() == 1) {
                    continue;
                }
                ExcelFinishValue finishValue = new ExcelFinishValue();
                finishValue.setTaskUserId(maps.get(0).get("0"));
                int index = 1;
                for (Map.Entry<String, String> entry : stringMap.entrySet()) {
                    finishValue.setKpiItemName(head.get(Integer.valueOf(entry.getKey())));
                    if (index == 1) {
                        finishValue.setFinishValue(entry.getValue());
                        index++;
                    }else {
                        finishValue.setComment(entry.getValue());
                    }
                }
                finishValues.add(finishValue);
            }
        }
        System.out.println(JSONUtil.toJsonStr(finishValues));
    }


    public static List<Map<String, String>> groupMapByKeyProximity(Map<String, String> map, int threshold, int size) {
        List<KeyEntry> keyEntries = new ArrayList<>();
        for (String key : map.keySet()) {
            try {
                int numericKey = Integer.parseInt(key);
                keyEntries.add(new KeyEntry(key, numericKey));
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("Key is not a valid integer: " + key, e);
            }
        }

        keyEntries.sort(Comparator.comparingInt(KeyEntry::getNumericKey));

        List<List<KeyEntry>> groups = new ArrayList<>();
        if (keyEntries.isEmpty()) {
            return new ArrayList<>();
        }

        List<KeyEntry> currentGroup = new ArrayList<>();
        currentGroup.add(keyEntries.get(0));
        groups.add(currentGroup);

        for (int i = 1; i < keyEntries.size(); i++) {
            KeyEntry current = keyEntries.get(i);
            KeyEntry lastInGroup = currentGroup.get(currentGroup.size() - 1);
            int diff = current.getNumericKey() - lastInGroup.getNumericKey();
            if (diff <= threshold) {
                if (currentGroup.size() >= size) {
                    currentGroup = new ArrayList<>();
                    currentGroup.add(current);
                    groups.add(currentGroup);
                    continue;
                }
                currentGroup.add(current);
            } else {
                currentGroup = new ArrayList<>();
                currentGroup.add(current);
                groups.add(currentGroup);
            }
        }

        List<Map<String, String>> result = new ArrayList<>();
        for (List<KeyEntry> group : groups) {
            Map<String, String> subMap = new LinkedHashMap<>();
            for (KeyEntry entry : group) {
                subMap.put(entry.originalKey, map.get(entry.originalKey));
            }
            result.add(subMap);
        }

        return result;
    }


    private static class KeyEntry {
        private final String originalKey;
        private final int numericKey;

        public KeyEntry(String originalKey, int numericKey) {
            this.originalKey = originalKey;
            this.numericKey = numericKey;
        }

        public int getNumericKey() {
            return numericKey;
        }
    }


    @Test
    public void improtInputFinishValue2() {
//        String fileName = FileUtil.getTemplatePath("批量导入完成值");
//        ClassPathResource resource = new ClassPathResource(fileName);
//        ImportFinishValueHeaderListener listener = new ImportFinishValueHeaderListener();
//        EasyExcel.read("src/test/resources/批量导入完成值.xlsx", listener)
//                .headRowNumber(2) // 重要：设置表头行数为2
//                .sheet()
//                .doRead();
//        System.out.println(JSONUtil.toJsonStr(listener));
        // 获取解析后的数据
    }

}
