package com.polaris.kpi.eval.domain.task.entity;

import cn.com.polaris.kpi.eval.Rater;
import cn.hutool.core.collection.CollUtil;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.INodeConf;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * EmpEvalScorerNode.mergeAndRemoveDuplicates 方法测试
 * 验证去重逻辑和新数据替换旧数据的功能
 */
class EmpEvalScorerNodeTest {

    @Mock
    private Rater mockRater;
    
    @Mock
    private INodeConf mockNodeConf;

    private EmpEvalScorerNode scorerNode;
    private String companyId = "company1";
    private String opEmpId = "emp1";
    private String taskId = "task1";
    private String taskUserId = "taskUser1";
    private String empId = "emp1";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // Mock设置
        when(mockRater.getWeight()).thenReturn(BigDecimal.valueOf(100));
        when(mockRater.getEmpId()).thenReturn("scorer1");
        when(mockRater.getEmpName()).thenReturn("评分人1");
        when(mockRater.getAvatar()).thenReturn("avatar1");
        when(mockNodeConf.order()).thenReturn(1);
        when(mockNodeConf.multiType()).thenReturn("and");
        when(mockNodeConf.node()).thenReturn(null); // 简化处理
        
        scorerNode = new EmpEvalScorerNode();
        scorerNode.setScorerNodeKpiTypes(new ArrayList<>());
    }

    @Test
    void testMergeAndRemoveDuplicates_WithNewDataReplacement() {
        // 准备测试数据
        Date oldTime = new Date(System.currentTimeMillis() - 10000); // 10秒前
        Date newTime = new Date(); // 现在
        
        // 创建现有的KPI类型和项目
        EvalScorerNodeKpiType existingType = createKpiType("type1");
        EvalScorerNodeKpiItem existingItem = createKpiItem("type1", "item1", "scorer1", "self", 1, oldTime);
        existingType.getScorerNodeKpiItems().add(existingItem);
        scorerNode.getScorerNodeKpiTypes().add(existingType);
        
        // 创建新的KPI类型和项目（相同的业务键，但更新的时间）
        EvalScorerNodeKpiType newType = createKpiType("type1");
        EvalScorerNodeKpiItem newItem = createKpiItem("type1", "item1", "scorer1", "self", 1, newTime);
        newItem.setScore(BigDecimal.valueOf(95)); // 新的分数
        newItem.setScoreComment("更新的评语");
        newType.getScorerNodeKpiItems().add(newItem);
        
        List<EvalScorerNodeKpiType> newKpiTypes = new ArrayList<>();
        newKpiTypes.add(newType);
        
        // 执行合并去重
        scorerNode.mergeAndRemoveDuplicates(newKpiTypes);
        
        // 验证结果
        assertEquals(1, scorerNode.getScorerNodeKpiTypes().size());
        EvalScorerNodeKpiType resultType = scorerNode.getScorerNodeKpiTypes().get(0);
        assertEquals(1, resultType.getScorerNodeKpiItems().size());
        
        EvalScorerNodeKpiItem resultItem = resultType.getScorerNodeKpiItems().get(0);
        // 验证使用了新数据
        assertEquals(BigDecimal.valueOf(95), resultItem.getScore());
        assertEquals("更新的评语", resultItem.getScoreComment());
        assertEquals(newTime, resultItem.getUpdatedTime());
    }

    @Test
    void testMergeAndRemoveDuplicates_WithOldDataKept() {
        // 准备测试数据 - 现有数据更新
        Date oldTime = new Date(System.currentTimeMillis() - 10000); // 10秒前
        Date newTime = new Date(); // 现在
        
        // 创建现有的KPI类型和项目（更新的时间）
        EvalScorerNodeKpiType existingType = createKpiType("type1");
        EvalScorerNodeKpiItem existingItem = createKpiItem("type1", "item1", "scorer1", "self", 1, newTime);
        existingItem.setScore(BigDecimal.valueOf(90));
        existingItem.setScoreComment("现有评语");
        existingType.getScorerNodeKpiItems().add(existingItem);
        scorerNode.getScorerNodeKpiTypes().add(existingType);
        
        // 创建新的KPI类型和项目（相同的业务键，但更旧的时间）
        EvalScorerNodeKpiType newType = createKpiType("type1");
        EvalScorerNodeKpiItem newItem = createKpiItem("type1", "item1", "scorer1", "self", 1, oldTime);
        newItem.setScore(BigDecimal.valueOf(85));
        newItem.setScoreComment("旧的评语");
        newType.getScorerNodeKpiItems().add(newItem);
        
        List<EvalScorerNodeKpiType> newKpiTypes = new ArrayList<>();
        newKpiTypes.add(newType);
        
        // 执行合并去重
        scorerNode.mergeAndRemoveDuplicates(newKpiTypes);
        
        // 验证结果 - 应该保留现有的更新数据
        assertEquals(1, scorerNode.getScorerNodeKpiTypes().size());
        EvalScorerNodeKpiType resultType = scorerNode.getScorerNodeKpiTypes().get(0);
        assertEquals(1, resultType.getScorerNodeKpiItems().size());
        
        EvalScorerNodeKpiItem resultItem = resultType.getScorerNodeKpiItems().get(0);
        // 验证保留了现有的更新数据
        assertEquals(BigDecimal.valueOf(90), resultItem.getScore());
        assertEquals("现有评语", resultItem.getScoreComment());
        assertEquals(newTime, resultItem.getUpdatedTime());
    }

    @Test
    void testMergeAndRemoveDuplicates_WithNewItems() {
        // 准备测试数据
        Date currentTime = new Date();
        
        // 创建现有的KPI类型和项目
        EvalScorerNodeKpiType existingType = createKpiType("type1");
        EvalScorerNodeKpiItem existingItem = createKpiItem("type1", "item1", "scorer1", "self", 1, currentTime);
        existingType.getScorerNodeKpiItems().add(existingItem);
        scorerNode.getScorerNodeKpiTypes().add(existingType);
        
        // 创建新的KPI项目（不同的业务键）
        EvalScorerNodeKpiType newType = createKpiType("type1");
        EvalScorerNodeKpiItem newItem = createKpiItem("type1", "item2", "scorer1", "self", 1, currentTime);
        newType.getScorerNodeKpiItems().add(newItem);
        
        List<EvalScorerNodeKpiType> newKpiTypes = new ArrayList<>();
        newKpiTypes.add(newType);
        
        // 执行合并去重
        scorerNode.mergeAndRemoveDuplicates(newKpiTypes);
        
        // 验证结果 - 应该有两个项目
        assertEquals(1, scorerNode.getScorerNodeKpiTypes().size());
        EvalScorerNodeKpiType resultType = scorerNode.getScorerNodeKpiTypes().get(0);
        assertEquals(2, resultType.getScorerNodeKpiItems().size());
    }

    private EvalScorerNodeKpiType createKpiType(String kpiTypeId) {
        EvalScorerNodeKpiType type = new EvalScorerNodeKpiType();
        type.setKpiTypeId(kpiTypeId);
        type.setScorerNodeKpiItems(new ArrayList<>());
        return type;
    }

    private EvalScorerNodeKpiItem createKpiItem(String kpiTypeId, String kpiItemId, 
                                               String scorerId, String scorerType, 
                                               Integer approvalOrder, Date updatedTime) {
        EvalScorerNodeKpiItem item = new EvalScorerNodeKpiItem();
        item.setKpiTypeId(kpiTypeId);
        item.setKpiItemId(kpiItemId);
        item.setScorerId(scorerId);
        item.setScorerType(scorerType);
        item.setApprovalOrder(approvalOrder);
        item.setUpdatedTime(updatedTime);
        return item;
    }
}
