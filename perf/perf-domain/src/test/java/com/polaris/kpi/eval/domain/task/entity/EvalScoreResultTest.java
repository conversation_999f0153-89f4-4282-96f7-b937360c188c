package com.polaris.kpi.eval.domain.task.entity;

import cn.com.polaris.kpi.eval.Pecent;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalKpiType;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalRule;
import com.polaris.kpi.eval.domain.task.entity.empeval.KpiListWrap;
import com.polaris.sdk.type.ListWrap;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

import static net.sf.ezmorph.test.ArrayAssertions.assertEquals;


public class EvalScoreResultTest {
    @InjectMocks
    private EvalScoreResult evalScoreResult;

    @Mock
    private ScorerRateNode scorerRateNode;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    @DisplayName("计算最终加分-有效数值")
    public void reComputeFinalPlusScoreWithValidInputs() {
        // Arrange
        BigDecimal sumOfMaxRate = new BigDecimal("2.0000");
        BigDecimal weight = new BigDecimal("0.5000");
        BigDecimal plusScore = new BigDecimal("1.0000");
        EvalScoreResult result = new EvalScoreResult();
        ScorerRateNode scorerRateNode = new ScorerRateNode("sub_score");
        scorerRateNode.setSumOfMaxRate(sumOfMaxRate);
        scorerRateNode.setWeight(weight);
        result.setPlusScore(plusScore);

        // Act
        result.reComputeFinalPlusScore(scorerRateNode);

        // Assert
        BigDecimal expectedFinalWeightPlusScore = plusScore.divide(sumOfMaxRate, 4, RoundingMode.HALF_UP)
                .multiply(weight.divide(Pecent.ONE_HUNDRED, 4, RoundingMode.HALF_UP)).setScale(4, RoundingMode.HALF_UP);
        assertEquals(expectedFinalWeightPlusScore, result.getFinalWeightPlusScore());
    }

    @Test
    @DisplayName("计算最终加分-SumOfMaxRate为0")
    public void reComputeFinalPlusScoreWithZeroSumOfMaxRate() {
        // Arrange
        BigDecimal sumOfMaxRate = BigDecimal.ZERO;
        BigDecimal weight = new BigDecimal("0.5000");
        BigDecimal plusScore = new BigDecimal("1.0000");

        EvalScoreResult result = new EvalScoreResult();
        result.setPlusScore(plusScore);

        ScorerRateNode scorerRateNode = new ScorerRateNode("sub_score");
        scorerRateNode.setSumOfMaxRate(sumOfMaxRate);
        scorerRateNode.setWeight(weight);

       // Mockito.when(scorerRateNode.getSumOfMaxRate()).thenReturn(BigDecimal.ZERO);
        // Act
        result.reComputeFinalPlusScore(scorerRateNode);

        // Assert
        assertEquals(null, result.getFinalWeightPlusScore());
    }

    @Test
    @DisplayName("计算最终加分-PlusScore为null")
    public void reComputeFinalPlusScoreWithNullPlusScore() {
        // Arrange
        BigDecimal sumOfMaxRate = new BigDecimal("2.0000");
        BigDecimal weight = new BigDecimal("0.5000");

        EvalScoreResult result = new EvalScoreResult();
        result.setPlusScore(null);

        ScorerRateNode scorerRateNode = new ScorerRateNode("sup_score");
        scorerRateNode.setSumOfMaxRate(sumOfMaxRate);
        scorerRateNode.setWeight(weight);

        // Act
        result.reComputeFinalPlusScore(scorerRateNode);

        // Assert
        assertEquals(null, result.getFinalWeightPlusScore());
    }
    @Test
    @DisplayName("计算最终减分-有效数值")
    public void reComputeFinalSubScoreWithValidInputs() {
        // Arrange
        BigDecimal sumOfMaxRate = new BigDecimal("2.0000");
        BigDecimal weight = new BigDecimal("0.5000");
        BigDecimal subtractScore = new BigDecimal("1.0000");

        EvalScoreResult result = new EvalScoreResult();
        result.setSubtractScore(subtractScore);

        ScorerRateNode scorerRateNode = new ScorerRateNode("sub_score");
        scorerRateNode.setSumOfMaxRate(sumOfMaxRate);
        scorerRateNode.setWeight(weight);

      //  Mockito.when(scorerRateNode.getSumOfMaxRate()).thenReturn(sumOfMaxRate);
        //Mockito.when(scorerRateNode.getWeight()).thenReturn(weight);
        result.setSubtractScore(subtractScore);

        // Act
        result.reComputeFinalSubScore(scorerRateNode);

        // Assert
        BigDecimal expectedFinalWeightSubtractScore = subtractScore.divide(sumOfMaxRate, 4, RoundingMode.HALF_UP)
                .multiply(weight.divide(Pecent.ONE_HUNDRED, 4, RoundingMode.HALF_UP)).setScale(4, RoundingMode.HALF_UP);
        assertEquals(expectedFinalWeightSubtractScore, result.getFinalWeightSubtractScore());
    }

    @Test
    @DisplayName("计算最终减分-SumOfMaxRate为0")
    public void reComputeFinalSubScoreWithZeroSumOfMaxRate() {
        // Arrange

        // Arrange
        BigDecimal sumOfMaxRate = BigDecimal.ZERO;
        BigDecimal weight = new BigDecimal("0.5000");
        BigDecimal subtractScore = new BigDecimal("1.0000");

        EvalScoreResult result = new EvalScoreResult();
        result.setSubtractScore(subtractScore);

        ScorerRateNode scorerRateNode = new ScorerRateNode("sub_score");
        scorerRateNode.setSumOfMaxRate(sumOfMaxRate);
        scorerRateNode.setWeight(weight);

        //Mockito.when(scorerRateNode.getSumOfMaxRate()).thenReturn(BigDecimal.ZERO);

        // Act
        result.reComputeFinalSubScore(scorerRateNode);

        // Assert
        assertEquals(null, result.getFinalWeightSubtractScore());
    }

    @Test
    @DisplayName("计算最终减分-SubtractScore为null")
    public void reComputeFinalSubScoreWithNullSubtractScore() {
        // Arrange
        //Mockito.when(scorerRateNode.getSumOfMaxRate()).thenReturn(new BigDecimal("2.0000"));
       // Mockito.when(scorerRateNode.getWeight()).thenReturn(new BigDecimal("0.5000"));
        BigDecimal sumOfMaxRate = new BigDecimal("2.0000");
        BigDecimal weight = new BigDecimal("0.5000");

        EvalScoreResult result = new EvalScoreResult();
        result.setSubtractScore(null);

        ScorerRateNode scorerRateNode = new ScorerRateNode("sup_score");
        scorerRateNode.setSumOfMaxRate(sumOfMaxRate);
        scorerRateNode.setWeight(weight);

        // Act
        result.reComputeFinalSubScore(scorerRateNode);

        // Assert
        assertEquals(null, result.getFinalWeightSubtractScore());
    }
}
