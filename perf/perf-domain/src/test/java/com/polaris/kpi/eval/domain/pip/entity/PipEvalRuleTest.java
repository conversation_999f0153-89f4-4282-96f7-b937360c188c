package com.polaris.kpi.eval.domain.pip.entity;

import cn.com.polaris.kpi.eval.Rater;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.pip.plan.entity.*;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;


public class PipEvalRuleTest {

    @Test
    public void newPipEvalRule() {
        String companyId = "ece4e403-43aa-47f2-bb19-a0dd18b8e98d";
        String opEmpId = "1299001";
        String pipEvalId = "1000203";
        PipGoalConfirmConf goalConfirmRater = new PipGoalConfirmConf();
        goalConfirmRater.setOpen(1);
        List<PipGoalAuditNode> auditNodes = new ArrayList<>();
        PipGoalAuditNode auditNode1= new PipGoalAuditNode();
        auditNode1.setNodeTitle("目标制定");
        auditNode1.setNodeType("goal_created");
        auditNode1.setApprovalOrder(1);
        auditNode1.setApproverType("taskEmp");
        auditNode1.setApproverInfo("1299001");
        auditNode1.setApproverName("张三");
        List<Rater> raters = new ArrayList<>();
        raters.add(new Rater("111","张三"));
        auditNode1.setRaters(raters);
        auditNodes.add(auditNode1);

        PipGoalAuditNode auditNode2= new PipGoalAuditNode();
        auditNode2.setNodeTitle("目标确定");
        auditNode2.setNodeType("goal_created");
        auditNode2.setApprovalOrder(1);
        auditNode2.setApproverType("taskEmp");
        auditNode2.setApproverInfo("1299001");
        auditNode2.setApproverName("张三");
        List<Rater> raters2 = new ArrayList<>();
        raters2.add(new Rater("111","张三"));
        auditNode1.setRaters(raters2);
        auditNodes.add(auditNode2);

        goalConfirmRater.setAuditNodes(auditNodes);

        PipGoalExcuteConf excuteRater = new PipGoalExcuteConf();
        excuteRater.setOpen(1);
        excuteRater.setAuditNodes(new ArrayList<>());

        PipScoreConf scoreRater = new PipScoreConf();
        scoreRater.setOpen(1);
        List<PipScoreAuditNode> scoreAuditNodes = new ArrayList<>();
        PipScoreAuditNode scoreauditNode1= new PipScoreAuditNode();
        scoreauditNode1.setNodeTitle("自评");
        scoreauditNode1.setNodeType("self_score");
        scoreauditNode1.setApprovalOrder(1);
        scoreauditNode1.setApproverType("taskEmp");
        scoreauditNode1.setApproverInfo("1299001");
        scoreauditNode1.setApproverName("张三");
        List<Rater> scoreraters1 = new ArrayList<>();
        raters.add(new Rater("111","张三"));
        auditNode1.setRaters(scoreraters1);
        scoreAuditNodes.add(scoreauditNode1);

        PipScoreAuditNode scoreauditNode2= new PipScoreAuditNode();
        auditNode2.setNodeTitle("上级平");
        auditNode2.setNodeType("super_score");
        auditNode2.setApprovalOrder(2);
        auditNode2.setApproverType("manager");
        auditNode2.setApproverInfo("1");
        auditNode2.setApproverName("直属主管");
        List<Rater> scoreraters2 = new ArrayList<>();
        raters2.add(new Rater("1","直属主管"));
        auditNode1.setRaters(scoreraters2);
        scoreAuditNodes.add(scoreauditNode2);

        scoreRater.setAuditNodes(scoreAuditNodes);

        PipResultAffirmConf resultAffirmConf = new PipResultAffirmConf();
        scoreRater.setOpen(1);
        resultAffirmConf.setResultConfirmType(1);

        PipEvalRule rule = new PipEvalRule(companyId,opEmpId,"1299001",pipEvalId,goalConfirmRater,excuteRater,scoreRater,resultAffirmConf);
        System.out.println(JSONUtil.toJsonStr(rule));
    }
}