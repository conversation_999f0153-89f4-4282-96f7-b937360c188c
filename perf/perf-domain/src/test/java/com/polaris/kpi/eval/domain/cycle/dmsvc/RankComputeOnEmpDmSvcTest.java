package com.polaris.kpi.eval.domain.cycle.dmsvc;

import cn.com.polaris.kpi.eval.RankScope;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.TestFileTool;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.calibrated.ResultRankInstance;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalRule;
import com.polaris.kpi.eval.domain.task.repo.EmpEvalRuleRepo;
import com.polaris.kpi.eval.domain.task.repo.ScoreRuleSnapRepo;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class RankComputeOnEmpDmSvcTest {
    private String companyId = "";
    private ScoreRuleSnapRepo snapRepo = Mockito.mock(ScoreRuleSnapRepo.class);
    private EmpEvalRuleRepo empRuleRepo;

    @Before
    public void setUp() throws Exception {
        Mockito.when(snapRepo.listSnapMember(new TenantId(companyId), "1000002")).thenReturn(TestFileTool.toList(RankComputeOnEmpDmSvcTest.class, "listSnapMember.json", EvalUser.class));
        Mockito.when(snapRepo.listSnapMember(new TenantId(companyId), "1000003")).thenReturn(TestFileTool.toList(RankComputeOnEmpDmSvcTest.class, "listMember2.json", EvalUser.class));
        Mockito.when(snapRepo.listSnapMember(new TenantId(companyId), "1000004")).thenReturn(TestFileTool.toList(RankComputeOnEmpDmSvcTest.class, "listMember3.json", EvalUser.class));
        Mockito.when(snapRepo.listSnapMember(new TenantId(companyId), "1000001")).thenReturn(TestFileTool.toList(RankComputeOnEmpDmSvcTest.class, "listMember3.json", EvalUser.class));
        mockRuleRepo();
    }

    private void mockRuleRepo() {
        empRuleRepo = Mockito.mock(EmpEvalRuleRepo.class);
        EmpEvalRule t = new EmpEvalRule();
        t.setCreateTotalLevelType(1);
        Mockito.when(empRuleRepo.getBaseEmpEvalRule(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(t);
    }

    @Test
    @DisplayName("按分生成等级")
    public void testRecomputeResultByScore() {
        ResultRankInstance snap = TestFileTool.toBean(this.getClass(), "snap.json", ResultRankInstance.class);
//        ResultRankInstance snap = JsonFileTool.toBean(path + "/snap.json", ResultRankInstance.class);
        RankComputeOnEmpDmSvc compute = new RankComputeOnEmpDmSvc(new TenantId(companyId), snap);
        compute.forEachRecomputeResult(snapRepo, empRuleRepo);
        List<EvalUser> members = compute.getMembers();
        EvalUser evalUser1 = members.stream().filter(evalUser -> evalUser.getId().equals("100001")).findFirst().get();
        Assert.assertTrue(evalUser1.getStepId().equals("2004001"));
        Assert.assertTrue(evalUser1.getPerfCoefficient().equals("1"));

        EvalUser evalUser2 = members.stream().filter(evalUser -> evalUser.getId().equals("100002")).findFirst().get();
        Assert.assertTrue(evalUser2.getStepId().equals("1000005"));
        Assert.assertTrue(evalUser2.getPerfCoefficient().equals("0.8"));

        EvalUser evalUser3 = members.stream().filter(evalUser -> evalUser.getId().equals("100003")).findFirst().get();
        Assert.assertTrue(evalUser3.getStepId().equals("1000006"));
        Assert.assertTrue(evalUser3.getPerfCoefficient().equals("0.6"));
        compute.batchUpdateResult(snapRepo, new TenantId(companyId));
    }

    @Test
    @DisplayName("按排名生成等级-所有all")
    public void testRecomputeResultByRank() {
        ResultRankInstance snap = TestFileTool.toBean(this.getClass(), "snapByRank.json", ResultRankInstance.class);
        snap.setRankScope(new RankScope(1, -1));//所有
        snap.setLevelDefType(2);
        snap.setId("1000002");
        RankComputeOnEmpDmSvc compute = new RankComputeOnEmpDmSvc(new TenantId(companyId), snap);
        compute.forEachRecomputeResult(snapRepo, empRuleRepo);
        List<EvalUser> members = compute.getMembers();
        new ListWrap<>(members).groupBy(u -> u.getTaskId()).getGroups().forEach((s, evalUsers) -> {
            Assert.assertTrue(evalUsers.stream().filter(evalUser -> evalUser.getStepId().equals("S")).count() == 1L);//9*10%
            Assert.assertTrue(evalUsers.stream().filter(evalUser -> evalUser.getStepId().equals("A")).count() == 5L);//9*60%
            Assert.assertTrue(evalUsers.stream().filter(evalUser -> evalUser.getStepId().equals("D")).count() == 3L);//9*30%
        });
        compute.batchUpdateResult(snapRepo, new TenantId(companyId));
    }

    @Test
    @DisplayName("按排名生成等级-按所在部门")
    public void testRecomputeResultByRank2() {
        ResultRankInstance snap = TestFileTool.toBean(this.getClass(), "snapByRank.json", ResultRankInstance.class);
        snap.setRankScope(new RankScope(2, 0));//按所在部门
        snap.setLevelDefType(2);
        snap.setId("1000002");
        RankComputeOnEmpDmSvc compute = new RankComputeOnEmpDmSvc(new TenantId(companyId), snap);
        compute.forEachRecomputeResult(snapRepo, empRuleRepo);
        List<EvalUser> members = compute.getMembers();
        new ListWrap<>(members).groupBy(u -> u.getTaskId()).getGroups().forEach((s, evalUsers) -> {
            new ListWrap<>(evalUsers).groupBy(u -> u.getOrgId()).getGroups().forEach((orgId, evalUsers1) -> {
                for (EvalUser evalUser : evalUsers1) {
                    System.out.println(evalUser.getTaskId() + ":" + evalUser.getOrgId() + ":" + evalUser.getStepId() + ":" + evalUser.getFinalScore());
                }
                Assert.assertTrue(evalUsers1.stream().filter(evalUser -> evalUser.getStepId().equals("S")).count() == 0L);//3*10%
                Assert.assertTrue(evalUsers1.stream().filter(evalUser -> evalUser.getStepId().equals("A")).count() == 2L);//3*60%
                Assert.assertTrue(evalUsers1.stream().filter(evalUser -> evalUser.getStepId().equals("D")).count() == 1L);//3*30%

            });

        });
        compute.batchUpdateResult(snapRepo, new TenantId(companyId));
    }


    @Test
    @DisplayName("按排名生成等级-按部门层级")
    public void testRecomputeResultByRank3() {
        ResultRankInstance snap = TestFileTool.toBean(this.getClass(), "snapByRank.json", ResultRankInstance.class);
        snap.setRankScope(new RankScope(2, 3));//按所3级部门
        snap.setLevelDefType(2);
        snap.setId("1000003");
        RankComputeOnEmpDmSvc compute = new RankComputeOnEmpDmSvc(new TenantId(companyId), snap);
        compute.forEachRecomputeResult(snapRepo, empRuleRepo);
        List<EvalUser> members = compute.getMembers();

        ListWrap<EvalUser> lvGroups = new ListWrap<>(members)
                .groupBy(u -> u.getAtOrgPathHight() >= 3 ? "2" : "1");
        ListWrap<EvalUser> bigLvs = new ListWrap<>(lvGroups.groupGet("2")).groupBy(u -> StrUtil.splitTrim(u.getAtOrgCodePath(), "|").get(3 - 1));

        Assert.assertTrue(bigLvs.getGroups().size() == 1);


        bigLvs.getGroups().forEach((key, evalUsers1) -> {
            Assert.assertTrue(StrUtil.equals(key, "3"));
            System.out.println("大于3级的部门分组,按各人员所有部门分组orgKey:" + key);
            for (EvalUser evalUser : evalUsers1) {
                System.out.println(evalUser.getTaskId() + ":" + evalUser.getAtOrgCodePath() + ":" + evalUser.getStepId() + ":" + evalUser.getFinalScore());
            }
            Assert.assertTrue(evalUsers1.stream().filter(evalUser -> evalUser.getStepId().equals("S")).count() == 1L);//6*10%
            Assert.assertTrue(evalUsers1.stream().filter(evalUser -> evalUser.getStepId().equals("A")).count() == 4L);//6*60%
            Assert.assertTrue(evalUsers1.stream().filter(evalUser -> evalUser.getStepId().equals("D")).count() == 1L);//6*30%
        });

        ListWrap<EvalUser> lowLvs = new ListWrap<>(lvGroups.groupGet("1")).groupBy(u -> u.getOrgId());
        Assert.assertTrue(lowLvs.getGroups().size() == 2);
        lowLvs.getGroups().forEach((orgId, evalUsers1) -> {
            System.out.println("小于3级的部门分组,按各人员所有部门分组org:" + orgId);
            for (EvalUser evalUser : evalUsers1) {
                System.out.println(evalUser.getTaskId() + ":" + evalUser.getAtOrgCodePath() + ":" + evalUser.getStepId() + ":" + evalUser.getFinalScore());
            }
//            Assert.assertTrue(evalUsers1.stream().filter(evalUser -> evalUser.getStepId().equals("S")).count() == 0L);//1*10%
//            Assert.assertTrue(evalUsers1.stream().filter(evalUser -> evalUser.getStepId().equals("A")).count() == 2L);//1*60%
//            Assert.assertTrue(evalUsers1.stream().filter(evalUser -> evalUser.getStepId().equals("D")).count() == 1L);//1*30%
        });

        compute.batchUpdateResult(snapRepo, new TenantId(companyId));
    }

    @Test
    public void name() {
        List<EvalUser> list = new ArrayList<>();
        for (int i = 1; i < 9; i++) {
            EvalUser evalUser = new EvalUser();
            evalUser.setId("100003");
            evalUser.setTaskId("t01");
            evalUser.setAtOrgCodePath(path(i));
            evalUser.setAtOrgPathHight(i);
            evalUser.setOrgId((i) + "");
            BigDecimal finalScore = new BigDecimal(i * 10);
            evalUser.setFinalScore(finalScore);
            evalUser.setOriginalFinalScore(finalScore);
            list.add(evalUser);
        }
        System.out.println(JSONUtil.toJsonStr(list));
    }

    private String path(int lv) {
        StringBuilder sb = new StringBuilder();
        for (int i = 1; i <= lv; i++) {
            sb.append(i).append("|");
        }
        return sb.toString();
    }
}