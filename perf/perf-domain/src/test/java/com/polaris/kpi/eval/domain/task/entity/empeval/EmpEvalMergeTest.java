package com.polaris.kpi.eval.domain.task.entity.empeval;

import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.BaseAuditNode;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.chain.ChainNode;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.chain.ScoreChain;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.chain.ScoreNode;
import com.polaris.sdk.common.JsonFileTool;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;

import java.io.File;
import java.net.URL;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

public class EmpEvalMergeTest {
    @Test
    public void name() {
        ScoreNode first = new ScoreNode(SubScoreNodeEnum.SUPERIOR_SCORE, 1);
        ScoreNode superTial = first;
        List<BaseAuditNode> auditNodes = new ArrayList<>();

        BaseAuditNode node1 = new BaseAuditNode();
        node1.setApprovalOrder(1);
        BaseAuditNode node2 = new BaseAuditNode();
        node2.setApprovalOrder(2);
        BaseAuditNode node3 = new BaseAuditNode();
        node3.setApprovalOrder(3);
        auditNodes.add(node1);
        auditNodes.add(node2);
        auditNodes.add(node3);
        for (int i = 1; i < auditNodes.size(); i++) {//第二个开始
            BaseAuditNode node = auditNodes.get(i);
            ScoreNode tmp = new ScoreNode(SubScoreNodeEnum.SUPERIOR_SCORE, node.getApprovalOrder());
            superTial.setNextSuper(tmp);
            superTial = tmp;
        }
        System.out.println(first.getNextSuper().getNextSuper().hasNextSuper());
    }

    @Test
    public void skipFinishedChainNode() {
        URL json = EmpEvalKpiTypeTest.class.getClassLoader().getResource("com.polaris.kpi.eval.domain.task.entity.empeval/empEvalMerge-skipFinishedChainNode.json");
        System.out.println("json:" + json.getFile());
        EmpEvalMerge evalMerge = JSONUtil.readJSONObject(new File(json.getFile()), Charset.forName("utf-8")).toBean(EmpEvalMerge.class);
        System.out.println(evalMerge.getScoreChain());

    }

    @Test
    public void orFilterWithOutFinishScorer() {
        //com.polaris.kpi.eval.domain.task.entity.empeval/KpiListWrapTest/KpiSubmitWrapData.json
        List<EmpEvalKpiType> list = JsonFileTool.toList("com.polaris.kpi.eval.domain.task.entity.empeval/KpiListWrapTest/KpiSubmitWrapData.json",EmpEvalKpiType.class);
        KpiListWrap wrap = new KpiListWrap(list);
        List<String> orScorerIds = new ArrayList<>();
        orScorerIds.add("1328001");
        System.out.println("json:" + JSONUtil.toJsonStr(wrap));
        List<String>  passScorerId= wrap.orFilterWithOutFinishScorer(orScorerIds);
        System.out.println(passScorerId);

    }


    @Test
    @DisplayName("只开默认评分流程+单个普通指标+线上异常指标多了流程")
    public void dispatchChainNodeV3() {
        EmpEvalMerge evalMerge = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.entity.empeval/EmpEvalMergeTest/dispatchSuperOfChainNode01.json", EmpEvalMerge.class);
        ChainNode scoreNode = new ChainNode(SubScoreNodeEnum.PEER_SCORE, 1);
        ChainDispatchRs chainDispatchRs = evalMerge.dispatchChainNodeV3(scoreNode, false);
        System.out.println(chainDispatchRs);
    }

    @Test
    @DisplayName("只开默认评分流程+单个普通指标+线上异常指标多了流程")
    public void dispatchSuperOfChainNode01() {
        EmpEvalMerge evalMerge = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.entity.empeval/EmpEvalMergeTest/dispatchSuperOfChainNode01.json", EmpEvalMerge.class);
        ScoreNode scoreNode = new ScoreNode(SubScoreNodeEnum.PEER_SCORE, 1);
        ChainDispatchRs chainDispatchRs = evalMerge.dispatchSuperOfChainNode(scoreNode, "2309421");
        System.out.println(chainDispatchRs);
    }

    @Test
    @DisplayName("simple:开默认评分流程+定向指标程")
    public void dispatchItemOfChainNode02() {
        EmpEvalMerge eval = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.entity.empeval/EmpEvalMergeTest/dispatchItemOfChainNode02.json", EmpEvalMerge.class);
        //eval.initSameScoreChain();
        eval.initTurnScoreChain();
        ChainNode chainNode = eval.getScoreChain().getChain().get(0);
        ChainNode next = null == chainNode ? null : chainNode.getNext();
        next = eval.skipFinishedChainNode(next);//跳过已完成chainNode
        boolean dispactched = eval.isDispactched(next);
        Assert.assertTrue(dispactched == false);
        System.out.println(dispactched);
    }

    @Test
    @DisplayName("simple:定向指标程")
    public void dispatchItemOfChainNode03() {
        EmpEvalMerge eval = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.entity.empeval/EmpEvalMergeTest/dispatchItem03.json", EmpEvalMerge.class);
        //eval.initSameScoreChain();
        eval.initTurnScoreChain();
        ChainNode chainNode = eval.getScoreChain().getChain().get(0);
        ChainNode next = null == chainNode ? null : chainNode.getNext();
        next = eval.skipFinishedChainNode(next);//跳过已完成chainNode
        boolean dispactched = eval.isDispactched(next);
        Assert.assertTrue(dispactched == false);
        System.out.println(dispactched);
    }

    @Test
    @DisplayName("查询完成值录入人")
    public void getResultInputEmpIds() {
        //  EmpEvalMerge eval = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.entity.empeval/EmpEvalMergeTest/dispatchItem03.json", EmpEvalMerge.class);
        EmpEvalMerge m = new EmpEvalMerge();
        String json = "[{\"superRater\":{\"superiorScoreOrder\":\"sameTime\",\"signatureFlag\":false,\"auditNodes\":[],\"open\":0},\"reserveOkrWeight\":0,\"peerRater\":{\"change\":false,\"excludeAllManager\":false,\"raters\":[],\"signatureFlag\":false,\"open\":0},\"appointRater\":{\"signatureFlag\":false,\"auditNodes\":[],\"open\":0},\"scoreOptType\":2,\"taskUserId\":\"1285101\",\"finishValueAudit\":[{\"objItems\":[],\"objType\":\"role\"},{\"objItems\":[],\"objType\":\"user\"},{\"objItems\":[],\"objType\":\"manager\"}],\"des\":\"\",\"selfRater\":{\"nodeWeight\":100,\"rateMode\":\"item\",\"signatureFlag\":false,\"anonymous\":\"false\",\"open\":1},\"isDeleted\":\"false\",\"isRaterBack\":false,\"createdTime\":1728459541000,\"kpiTypeWeight\":0,\"updatedTime\":1728478254000,\"scoringType\":1,\"typeOrder\":1,\"maxExtraScore\":2,\"plusSubInterval\":{\"max\":\"\",\"min\":\"\"},\"version\":0,\"importOkrFlag\":\"false\",\"openOkrScore\":0,\"isOkr\":\"false\",\"companyId\":{\"id\":\"5a031297-1b38-48ae-bc82-375849835203\"},\"kpiTypeUsedFields\":[{\"updatedTime\":1728459490000,\"adminType\":1,\"show\":1,\"sort\":1,\"taskUserId\":\"1285101\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"指标名称\",\"kpiTypeId\":\"4dfd8798-7661-47e4-9a6f-0ee3c2340e8a\",\"createdTime\":1728459541000,\"fieldId\":\"name\",\"req\":1,\"status\":\"valid\"},{\"updatedTime\":1728459490000,\"adminType\":1,\"show\":1,\"sort\":2,\"taskUserId\":\"1285101\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"考核标准\",\"kpiTypeId\":\"4dfd8798-7661-47e4-9a6f-0ee3c2340e8a\",\"createdTime\":1728459541000,\"fieldId\":\"standard\",\"req\":0,\"status\":\"valid\"},{\"updatedTime\":1728459490000,\"adminType\":1,\"show\":1,\"sort\":3,\"taskUserId\":\"1285101\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"计分规则\",\"kpiTypeId\":\"4dfd8798-7661-47e4-9a6f-0ee3c2340e8a\",\"createdTime\":1728459541000,\"fieldId\":\"scoreRule\",\"req\":0,\"status\":\"valid\"},{\"updatedTime\":1728459490000,\"adminType\":1,\"show\":1,\"sort\":4,\"taskUserId\":\"1285101\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"目标值\",\"kpiTypeId\":\"4dfd8798-7661-47e4-9a6f-0ee3c2340e8a\",\"createdTime\":1728459541000,\"fieldId\":\"targetValue\",\"req\":1,\"status\":\"valid\"},{\"updatedTime\":1728459490000,\"adminType\":1,\"show\":1,\"sort\":5,\"taskUserId\":\"1285101\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"单位\",\"kpiTypeId\":\"4dfd8798-7661-47e4-9a6f-0ee3c2340e8a\",\"createdTime\":1728459541000,\"fieldId\":\"unit\",\"req\":1,\"status\":\"valid\"},{\"updatedTime\":1728459490000,\"adminType\":1,\"show\":1,\"sort\":6,\"taskUserId\":\"1285101\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"减分上限\",\"kpiTypeId\":\"4dfd8798-7661-47e4-9a6f-0ee3c2340e8a\",\"createdTime\":1728459541000,\"fieldId\":\"subtractLimit\",\"req\":1,\"status\":\"valid\"},{\"updatedTime\":1728459490000,\"adminType\":1,\"show\":1,\"sort\":7,\"taskUserId\":\"1285101\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"完成值录入\",\"kpiTypeId\":\"4dfd8798-7661-47e4-9a6f-0ee3c2340e8a\",\"createdTime\":1728459541000,\"fieldId\":\"finishValue\",\"req\":1,\"status\":\"valid\"},{\"updatedTime\":1728459490000,\"adminType\":0,\"show\":0,\"sort\":8,\"taskUserId\":\"1285101\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"指标定义\",\"kpiTypeId\":\"4dfd8798-7661-47e4-9a6f-0ee3c2340e8a\",\"createdTime\":1728459541000,\"fieldId\":\"c6acb37a-797f-410e-ba77-1a463fce7ca8\",\"req\":0,\"status\":\"valid\",\"unitSwitch\":0},{\"updatedTime\":1728459490000,\"adminType\":0,\"show\":0,\"sort\":9,\"taskUserId\":\"1285101\",\"type\":3,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"截止时间\",\"kpiTypeId\":\"4dfd8798-7661-47e4-9a6f-0ee3c2340e8a\",\"createdTime\":1728459541000,\"fieldId\":\"d48cd958-df8e-4e7e-bd4a-0f097a32e225\",\"req\":0,\"status\":\"valid\"},{\"updatedTime\":1728459490000,\"adminType\":0,\"show\":0,\"sort\":10,\"taskUserId\":\"1285101\",\"type\":2,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"出勤天数\",\"kpiTypeId\":\"4dfd8798-7661-47e4-9a6f-0ee3c2340e8a\",\"createdTime\":1728459541000,\"fieldId\":\"dd42ab1d-dacb-4f55-a7b9-ccd668c1bdb5\",\"req\":0,\"status\":\"valid\",\"unitSwitch\":1},{\"updatedTime\":1728459490000,\"adminType\":0,\"show\":0,\"sort\":11,\"taskUserId\":\"1285101\",\"type\":2,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"门槛值\",\"kpiTypeId\":\"4dfd8798-7661-47e4-9a6f-0ee3c2340e8a\",\"createdTime\":1728459541000,\"fieldId\":\"8996358f-0b4e-41e0-b9a4-0929d3edeba7\",\"req\":0,\"status\":\"valid\",\"unitSwitch\":1},{\"updatedTime\":1728459490000,\"adminType\":0,\"show\":0,\"sort\":12,\"taskUserId\":\"1285101\",\"type\":2,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"挑战值\",\"kpiTypeId\":\"4dfd8798-7661-47e4-9a6f-0ee3c2340e8a\",\"createdTime\":1728459541000,\"fieldId\":\"1d022010-dfad-4b1e-8679-7fb636dad3a9\",\"req\":0,\"status\":\"valid\",\"unitSwitch\":1},{\"updatedTime\":1728459490000,\"adminType\":0,\"show\":0,\"sort\":13,\"taskUserId\":\"1285101\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"门槛值门槛值门槛值门槛值门槛值门槛值门槛\",\"kpiTypeId\":\"4dfd8798-7661-47e4-9a6f-0ee3c2340e8a\",\"createdTime\":1728459541000,\"fieldId\":\"8fc2435f-cf44-4544-98f3-9e5a421c1d98\",\"req\":0,\"status\":\"valid\",\"unitSwitch\":0},{\"updatedTime\":1728459490000,\"adminType\":0,\"show\":0,\"sort\":14,\"taskUserId\":\"1285101\",\"type\":2,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"测试单位\",\"kpiTypeId\":\"4dfd8798-7661-47e4-9a6f-0ee3c2340e8a\",\"createdTime\":1728459541000,\"fieldId\":\"7d110da2-2866-4d68-8ad0-180d99c74f19\",\"req\":0,\"status\":\"valid\",\"unitSwitch\":1}],\"kpiTypeName\":\"减分\",\"kpiTypeId\":\"4dfd8798-7661-47e4-9a6f-0ee3c2340e8a\",\"kpiTypeClassify\":\"subtract\",\"items\":[{\"subtractLimit\":2,\"alreadyNodes\":[],\"itemRule\":\"做好各开发项目的周边关系，周边民房监测，协调督促项目水、电、电梯、园林等各专业班组施工；积极配合工程施工开展，保证各施工工程正常施工\\n\",\"reserveOkrWeight\":0,\"showFinishBar\":1,\"itemUnit\":\"次\",\"multipleReviewersType\":\"or\",\"taskUserId\":\"1285101\",\"itemScoreValue\":\"{\\\"type\\\":\\\"toMainScore\\\"}\",\"finishValueAudit\":[{\"objItems\":[],\"objType\":\"role\"},{\"objItems\":[],\"objType\":\"user\"},{\"objItems\":[],\"objType\":\"manager\"}],\"showTargetValue\":\"\",\"formulaType\":1,\"finishValueType\":1,\"createdTime\":1728459490000,\"id\":\"1318004\",\"createdUser\":\"1009187\",\"scorerType\":\"exam\",\"order\":0,\"itemFormula\":\"\",\"updatedTime\":1728459490000,\"inputEmps\":[{\"empId\":\"1009187\",\"avatar\":\"https://static-legacy.dingtalk.com/media/lQLPDhtwSlDwYpfNAmDNAmCwP2waI3J1qnYCiKZNDcDTAA_608_608.png\",\"exUserId\":\"493267573826201284\",\"jobnumber\":\"200011\",\"empName\":\"李志旭\",\"status\":\"on_the_job\"}],\"thresholdJson\":\"[]\",\"typeOrder\":1,\"plusSubInterval\":{\"max\":\"\",\"min\":\"\"},\"inputFormat\":\"num\",\"version\":3,\"companyId\":{\"id\":\"5a031297-1b38-48ae-bc82-375849835203\"},\"resultInputType\":\"user\",\"itemFullScoreCfg\":\"false\",\"mustResultInput\":0,\"itemTargetValue\":0,\"itemFieldJson\":\"[{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"完成值\\\",\\\"companyFieldId\\\":\\\"268b0a46-c2bd-493d-aff8-8318aa7b02a8\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"fieldId\\\":\\\"finishValue\\\",\\\"label\\\":\\\"完成值\\\",\\\"value\\\":0,\\\"yVal\\\":true},{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"目标值\\\",\\\"companyFieldId\\\":\\\"e0674594-186f-4aa7-817d-30a2c9cee161\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"fieldId\\\":\\\"targetValue\\\",\\\"label\\\":\\\"目标值\\\",\\\"value\\\":0,\\\"yVal\\\":true},{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"完成值\\\",\\\"companyFieldId\\\":\\\"4ca8ffeb-6239-43e7-b67f-0259ba826184\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"fieldId\\\":\\\"finishValue\\\",\\\"label\\\":\\\"完成值\\\",\\\"value\\\":0,\\\"yVal\\\":true},{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"目标值\\\",\\\"companyFieldId\\\":\\\"1ea75c43-ff00-4ffa-be07-58f44f231a81\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"fieldId\\\":\\\"targetValue\\\",\\\"label\\\":\\\"目标值\\\",\\\"value\\\":0,\\\"yVal\\\":true}]\",\"kpiTypeClassify\":\"subtract\",\"formulaCondition\":\"[{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"完成值\\\",\\\"companyFieldId\\\":\\\"268b0a46-c2bd-493d-aff8-8318aa7b02a8\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"fieldId\\\":\\\"finishValue\\\",\\\"label\\\":\\\"完成值\\\",\\\"value\\\":0,\\\"yVal\\\":true,\\\"kpiItemId\\\":\\\"45a34c79-6354-4909-9c49-24ad4b38a705\\\"},{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"目标值\\\",\\\"companyFieldId\\\":\\\"e0674594-186f-4aa7-817d-30a2c9cee161\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"fieldId\\\":\\\"targetValue\\\",\\\"label\\\":\\\"目标值\\\",\\\"value\\\":0,\\\"yVal\\\":true,\\\"kpiItemId\\\":\\\"45a34c79-6354-4909-9c49-24ad4b38a705\\\"},{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"完成值\\\",\\\"companyFieldId\\\":\\\"4ca8ffeb-6239-43e7-b67f-0259ba826184\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"fieldId\\\":\\\"finishValue\\\",\\\"label\\\":\\\"完成值\\\",\\\"value\\\":0,\\\"yVal\\\":true,\\\"kpiItemId\\\":\\\"45a34c79-6354-4909-9c49-24ad4b38a705\\\"},{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"目标值\\\",\\\"companyFieldId\\\":\\\"1ea75c43-ff00-4ffa-be07-58f44f231a81\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"fieldId\\\":\\\"targetValue\\\",\\\"label\\\":\\\"目标值\\\",\\\"value\\\":0,\\\"yVal\\\":true,\\\"kpiItemId\\\":\\\"45a34c79-6354-4909-9c49-24ad4b38a705\\\"}]\",\"empId\":\"1009187\",\"itemType\":\"measurable\",\"scorerObjId\":[{\"objItems\":[],\"objType\":\"role\"},{\"objItems\":[],\"objType\":\"user\"},{\"objItems\":[],\"objType\":\"manager\"}],\"itemScoreRule\":{\"superRater\":{\"superiorScoreOrder\":\"sameTime\",\"signatureFlag\":false,\"auditNodes\":[],\"open\":0},\"kpiItemId\":\"45a34c79-6354-4909-9c49-24ad4b38a705\",\"peerRater\":{\"change\":false,\"excludeAllManager\":false,\"raters\":[],\"signatureFlag\":false,\"open\":0},\"appointRater\":{\"signatureFlag\":false,\"auditNodes\":[],\"open\":0},\"appointScoreFlag\":\"false\",\"taskUserId\":\"1285101\",\"version\":0,\"superiorScoreFlag\":\"false\",\"companyId\":{\"id\":\"5a031297-1b38-48ae-bc82-375849835203\"},\"selfScoreFlag\":\"true\",\"selfRater\":{\"nodeWeight\":100,\"rateMode\":\"item\",\"signatureFlag\":false,\"anonymous\":\"false\",\"open\":1},\"selfScoreWeight\":100,\"isDeleted\":\"false\",\"kpiTypeId\":\"4dfd8798-7661-47e4-9a6f-0ee3c2340e8a\",\"subRater\":{\"change\":false,\"excludeAllManager\":false,\"raters\":[],\"signatureFlag\":false,\"open\":0}},\"managerLevel\":\"\",\"orgId\":\"1010909\",\"kpiItemName\":\"因周边情况没有协调好造成对施工工程造成影响的次数\",\"isDeleted\":\"false\",\"finalSubmitFinishValue\":0,\"kpiTypeWeight\":0,\"itemTargetValueText\":\"\",\"inputRole\":[],\"itemWeight\":0,\"scoringRule\":\"造成影响出现一次扣3分，3次全扣\",\"kpiItemId\":\"45a34c79-6354-4909-9c49-24ad4b38a705\",\"maxExtraScore\":2,\"appointAudits\":[],\"isTypeLocked\":\"false\",\"openOkrScore\":0,\"isOkr\":\"false\",\"resultInputEmpId\":\"1009187\",\"kpiTypeName\":\"减分\",\"kpiTypeId\":\"4dfd8798-7661-47e4-9a6f-0ee3c2340e8a\",\"isNewEmp\":0,\"formulaFields\":[],\"taskId\":\"1638701\",\"waitScores\":[]}],\"itemLimitCnt\":{\"max\":\"\",\"openItemLimit\":\"false\",\"min\":\"\"},\"alreadyScores\":[],\"subRater\":{\"change\":false,\"excludeAllManager\":false,\"raters\":[],\"signatureFlag\":false,\"open\":0},\"waitScores\":[]},{\"superRater\":{\"superiorScoreOrder\":\"sameTime\",\"signatureFlag\":false,\"auditNodes\":[],\"open\":0},\"reserveOkrWeight\":0,\"peerRater\":{\"change\":false,\"excludeAllManager\":false,\"raters\":[],\"signatureFlag\":false,\"open\":0},\"appointRater\":{\"signatureFlag\":false,\"auditNodes\":[],\"open\":0},\"scoreOptType\":2,\"taskUserId\":\"1285101\",\"finishValueAudit\":[{\"objItems\":[],\"objType\":\"role\"},{\"objItems\":[],\"objType\":\"user\"},{\"objItems\":[],\"objType\":\"manager\"}],\"des\":\"\",\"selfRater\":{\"nodeWeight\":100,\"rateMode\":\"item\",\"signatureFlag\":false,\"anonymous\":\"false\",\"open\":1},\"isDeleted\":\"false\",\"isRaterBack\":false,\"createdTime\":1728459541000,\"kpiTypeWeight\":0,\"updatedTime\":1728478254000,\"scoringType\":1,\"typeOrder\":2,\"maxExtraScore\":0,\"plusSubInterval\":{\"max\":\"3\",\"min\":\"2\"},\"version\":0,\"importOkrFlag\":\"false\",\"openOkrScore\":0,\"isOkr\":\"false\",\"companyId\":{\"id\":\"5a031297-1b38-48ae-bc82-375849835203\"},\"kpiTypeUsedFields\":[{\"updatedTime\":1728459490000,\"adminType\":1,\"show\":1,\"sort\":1,\"taskUserId\":\"1285101\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"指标名称\",\"kpiTypeId\":\"dd852534-5401-41ab-87a1-3edc73a671aa\",\"createdTime\":1728459541000,\"fieldId\":\"name\",\"req\":1,\"status\":\"valid\"},{\"updatedTime\":1728459490000,\"adminType\":1,\"show\":1,\"sort\":2,\"taskUserId\":\"1285101\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"考核标准\",\"kpiTypeId\":\"dd852534-5401-41ab-87a1-3edc73a671aa\",\"createdTime\":1728459541000,\"fieldId\":\"standard\",\"req\":0,\"status\":\"valid\"},{\"updatedTime\":1728459490000,\"adminType\":1,\"show\":1,\"sort\":3,\"taskUserId\":\"1285101\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"计分规则\",\"kpiTypeId\":\"dd852534-5401-41ab-87a1-3edc73a671aa\",\"createdTime\":1728459541000,\"fieldId\":\"scoreRule\",\"req\":0,\"status\":\"valid\"},{\"updatedTime\":1728459490000,\"adminType\":1,\"show\":1,\"sort\":4,\"taskUserId\":\"1285101\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"目标值\",\"kpiTypeId\":\"dd852534-5401-41ab-87a1-3edc73a671aa\",\"createdTime\":1728459541000,\"fieldId\":\"targetValue\",\"req\":1,\"status\":\"valid\"},{\"updatedTime\":1728459490000,\"adminType\":1,\"show\":1,\"sort\":5,\"taskUserId\":\"1285101\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"单位\",\"kpiTypeId\":\"dd852534-5401-41ab-87a1-3edc73a671aa\",\"createdTime\":1728459541000,\"fieldId\":\"unit\",\"req\":1,\"status\":\"valid\"},{\"updatedTime\":1728459490000,\"adminType\":1,\"show\":1,\"sort\":6,\"taskUserId\":\"1285101\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"评分区间\",\"kpiTypeId\":\"dd852534-5401-41ab-87a1-3edc73a671aa\",\"createdTime\":1728459541000,\"fieldId\":\"scoreSection\",\"req\":1,\"status\":\"valid\"},{\"updatedTime\":1728459490000,\"adminType\":1,\"show\":1,\"sort\":7,\"taskUserId\":\"1285101\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"完成值录入\",\"kpiTypeId\":\"dd852534-5401-41ab-87a1-3edc73a671aa\",\"createdTime\":1728459541000,\"fieldId\":\"finishValue\",\"req\":1,\"status\":\"valid\"},{\"updatedTime\":1728459490000,\"adminType\":0,\"show\":0,\"sort\":8,\"taskUserId\":\"1285101\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"指标定义\",\"kpiTypeId\":\"dd852534-5401-41ab-87a1-3edc73a671aa\",\"createdTime\":1728459541000,\"fieldId\":\"c6acb37a-797f-410e-ba77-1a463fce7ca8\",\"req\":0,\"status\":\"valid\",\"unitSwitch\":0},{\"updatedTime\":1728459490000,\"adminType\":0,\"show\":0,\"sort\":9,\"taskUserId\":\"1285101\",\"type\":3,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"截止时间\",\"kpiTypeId\":\"dd852534-5401-41ab-87a1-3edc73a671aa\",\"createdTime\":1728459541000,\"fieldId\":\"d48cd958-df8e-4e7e-bd4a-0f097a32e225\",\"req\":0,\"status\":\"valid\"},{\"updatedTime\":1728459490000,\"adminType\":0,\"show\":0,\"sort\":10,\"taskUserId\":\"1285101\",\"type\":2,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"出勤天数\",\"kpiTypeId\":\"dd852534-5401-41ab-87a1-3edc73a671aa\",\"createdTime\":1728459541000,\"fieldId\":\"dd42ab1d-dacb-4f55-a7b9-ccd668c1bdb5\",\"req\":0,\"status\":\"valid\",\"unitSwitch\":1},{\"updatedTime\":1728459490000,\"adminType\":0,\"show\":0,\"sort\":11,\"taskUserId\":\"1285101\",\"type\":2,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"门槛值\",\"kpiTypeId\":\"dd852534-5401-41ab-87a1-3edc73a671aa\",\"createdTime\":1728459541000,\"fieldId\":\"8996358f-0b4e-41e0-b9a4-0929d3edeba7\",\"req\":0,\"status\":\"valid\",\"unitSwitch\":1},{\"updatedTime\":1728459490000,\"adminType\":0,\"show\":0,\"sort\":12,\"taskUserId\":\"1285101\",\"type\":2,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"挑战值\",\"kpiTypeId\":\"dd852534-5401-41ab-87a1-3edc73a671aa\",\"createdTime\":1728459541000,\"fieldId\":\"1d022010-dfad-4b1e-8679-7fb636dad3a9\",\"req\":0,\"status\":\"valid\",\"unitSwitch\":1},{\"updatedTime\":1728459490000,\"adminType\":0,\"show\":0,\"sort\":13,\"taskUserId\":\"1285101\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"门槛值门槛值门槛值门槛值门槛值门槛值门槛\",\"kpiTypeId\":\"dd852534-5401-41ab-87a1-3edc73a671aa\",\"createdTime\":1728459541000,\"fieldId\":\"8fc2435f-cf44-4544-98f3-9e5a421c1d98\",\"req\":0,\"status\":\"valid\",\"unitSwitch\":0},{\"updatedTime\":1728459490000,\"adminType\":0,\"show\":0,\"sort\":14,\"taskUserId\":\"1285101\",\"type\":2,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"测试单位\",\"kpiTypeId\":\"dd852534-5401-41ab-87a1-3edc73a671aa\",\"createdTime\":1728459541000,\"fieldId\":\"7d110da2-2866-4d68-8ad0-180d99c74f19\",\"req\":0,\"status\":\"valid\",\"unitSwitch\":1}],\"kpiTypeName\":\"加减分\",\"kpiTypeId\":\"dd852534-5401-41ab-87a1-3edc73a671aa\",\"kpiTypeClassify\":\"plusSub\",\"items\":[{\"alreadyNodes\":[],\"itemRule\":\"部门工程预算平均误差率控制在10%以内\",\"reserveOkrWeight\":0,\"showFinishBar\":1,\"itemFinishValue\":90,\"itemUnit\":\"%\",\"multipleReviewersType\":\"or\",\"taskUserId\":\"1285101\",\"itemScoreValue\":\"{\\\"type\\\":\\\"toMainScore\\\"}\",\"finishValueAudit\":[{\"objItems\":[],\"objType\":\"role\"},{\"objItems\":[],\"objType\":\"user\"},{\"objItems\":[],\"objType\":\"manager\"}],\"showTargetValue\":\"\",\"formulaType\":1,\"finishValueType\":1,\"createdTime\":1728459490000,\"id\":\"1318005\",\"createdUser\":\"1009187\",\"scorerType\":\"exam\",\"order\":0,\"itemFormula\":\"\",\"updatedTime\":1728478413000,\"inputEmps\":[{\"empId\":\"1009187\",\"avatar\":\"https://static-legacy.dingtalk.com/media/lQLPDhtwSlDwYpfNAmDNAmCwP2waI3J1qnYCiKZNDcDTAA_608_608.png\",\"exUserId\":\"493267573826201284\",\"jobnumber\":\"200011\",\"empName\":\"李志旭\",\"status\":\"on_the_job\"}],\"thresholdJson\":\"[]\",\"typeOrder\":2,\"plusSubInterval\":{\"max\":\"3\",\"min\":\"2\"},\"inputFormat\":\"num\",\"version\":3,\"companyId\":{\"id\":\"5a031297-1b38-48ae-bc82-375849835203\"},\"resultInputType\":\"user\",\"itemFullScoreCfg\":\"false\",\"mustResultInput\":0,\"itemTargetValue\":22,\"itemFieldJson\":\"[{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"完成值\\\",\\\"companyFieldId\\\":\\\"268b0a46-c2bd-493d-aff8-8318aa7b02a8\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"fieldId\\\":\\\"finishValue\\\",\\\"label\\\":\\\"完成值\\\",\\\"value\\\":0,\\\"yVal\\\":true},{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"目标值\\\",\\\"companyFieldId\\\":\\\"e0674594-186f-4aa7-817d-30a2c9cee161\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"fieldId\\\":\\\"targetValue\\\",\\\"label\\\":\\\"目标值\\\",\\\"value\\\":0,\\\"yVal\\\":true},{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"完成值\\\",\\\"companyFieldId\\\":\\\"4ca8ffeb-6239-43e7-b67f-0259ba826184\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"fieldId\\\":\\\"finishValue\\\",\\\"label\\\":\\\"完成值\\\",\\\"value\\\":0,\\\"yVal\\\":true},{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"目标值\\\",\\\"companyFieldId\\\":\\\"1ea75c43-ff00-4ffa-be07-58f44f231a81\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"fieldId\\\":\\\"targetValue\\\",\\\"label\\\":\\\"目标值\\\",\\\"value\\\":0,\\\"yVal\\\":true}]\",\"kpiTypeClassify\":\"plusSub\",\"formulaCondition\":\"[{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"完成值\\\",\\\"companyFieldId\\\":\\\"268b0a46-c2bd-493d-aff8-8318aa7b02a8\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"fieldId\\\":\\\"finishValue\\\",\\\"label\\\":\\\"完成值\\\",\\\"value\\\":0,\\\"yVal\\\":true,\\\"kpiItemId\\\":\\\"4110698d-0b97-4e2f-a57a-851a2700e28b\\\"},{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"目标值\\\",\\\"companyFieldId\\\":\\\"e0674594-186f-4aa7-817d-30a2c9cee161\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"fieldId\\\":\\\"targetValue\\\",\\\"label\\\":\\\"目标值\\\",\\\"value\\\":0,\\\"yVal\\\":true,\\\"kpiItemId\\\":\\\"4110698d-0b97-4e2f-a57a-851a2700e28b\\\"},{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"完成值\\\",\\\"companyFieldId\\\":\\\"4ca8ffeb-6239-43e7-b67f-0259ba826184\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"fieldId\\\":\\\"finishValue\\\",\\\"label\\\":\\\"完成值\\\",\\\"value\\\":0,\\\"yVal\\\":true,\\\"kpiItemId\\\":\\\"4110698d-0b97-4e2f-a57a-851a2700e28b\\\"},{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"目标值\\\",\\\"companyFieldId\\\":\\\"1ea75c43-ff00-4ffa-be07-58f44f231a81\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"fieldId\\\":\\\"targetValue\\\",\\\"label\\\":\\\"目标值\\\",\\\"value\\\":0,\\\"yVal\\\":true,\\\"kpiItemId\\\":\\\"4110698d-0b97-4e2f-a57a-851a2700e28b\\\"}]\",\"empId\":\"1009187\",\"itemType\":\"measurable\",\"scorerObjId\":[{\"objItems\":[],\"objType\":\"role\"},{\"objItems\":[],\"objType\":\"user\"},{\"objItems\":[],\"objType\":\"manager\"}],\"itemScoreRule\":{\"superRater\":{\"superiorScoreOrder\":\"sameTime\",\"signatureFlag\":false,\"auditNodes\":[],\"open\":0},\"kpiItemId\":\"4110698d-0b97-4e2f-a57a-851a2700e28b\",\"peerRater\":{\"change\":false,\"excludeAllManager\":false,\"raters\":[],\"signatureFlag\":false,\"open\":0},\"appointRater\":{\"signatureFlag\":false,\"auditNodes\":[],\"open\":0},\"appointScoreFlag\":\"false\",\"taskUserId\":\"1285101\",\"version\":0,\"superiorScoreFlag\":\"false\",\"companyId\":{\"id\":\"5a031297-1b38-48ae-bc82-375849835203\"},\"selfScoreFlag\":\"true\",\"selfRater\":{\"nodeWeight\":100,\"rateMode\":\"item\",\"signatureFlag\":false,\"anonymous\":\"false\",\"open\":1},\"selfScoreWeight\":100,\"isDeleted\":\"false\",\"kpiTypeId\":\"dd852534-5401-41ab-87a1-3edc73a671aa\",\"subRater\":{\"change\":false,\"excludeAllManager\":false,\"raters\":[],\"signatureFlag\":false,\"open\":0}},\"managerLevel\":\"\",\"orgId\":\"1010909\",\"kpiItemName\":\"工程预算误差率\",\"isDeleted\":\"false\",\"finalSubmitFinishValue\":1,\"kpiTypeWeight\":0,\"itemTargetValueText\":\"\",\"inputRole\":[],\"itemWeight\":0,\"scoringRule\":\"\",\"kpiItemId\":\"4110698d-0b97-4e2f-a57a-851a2700e28b\",\"maxExtraScore\":0,\"appointAudits\":[],\"isTypeLocked\":\"false\",\"openOkrScore\":0,\"isOkr\":\"false\",\"resultInputEmpId\":\"1009187\",\"kpiTypeName\":\"加减分\",\"kpiTypeId\":\"dd852534-5401-41ab-87a1-3edc73a671aa\",\"isNewEmp\":0,\"formulaFields\":[],\"taskId\":\"1638701\",\"waitScores\":[]}],\"itemLimitCnt\":{\"max\":\"\",\"openItemLimit\":\"false\",\"min\":\"\"},\"alreadyScores\":[],\"subRater\":{\"change\":false,\"excludeAllManager\":false,\"raters\":[],\"signatureFlag\":false,\"open\":0},\"waitScores\":[]}]";
        List<EmpEvalKpiType> list = JSONUtil.toList(json, EmpEvalKpiType.class);
        m.setKpiTypes(new KpiListWrap(list));
        List<String> lists = m.getResultInputEmpIds("1009187");
        System.out.println(lists.size() == 2);
    }

    @Test
    @DisplayName("查询完成值录入人")
    public void getResultInputEmpIds2() {
        //  EmpEvalMerge eval = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.entity.empeval/EmpEvalMergeTest/dispatchItem03.json", EmpEvalMerge.class);
        EmpEvalMerge m = new EmpEvalMerge();
        String json = "[{\"reserveOkrWeight\":3,\"scoreOptType\":2,\"lockedItems\":[\"addIndex\",\"modifyIndex\",\"deleteIndex\"],\"taskUserId\":\"3076585\",\"finishValueAudit\":[{\"objItems\":[],\"objType\":\"role\"},{\"objItems\":[],\"objType\":\"user\"},{\"objItems\":[],\"objType\":\"manager\"}],\"des\":\"权重10%\",\"isDeleted\":\"false\",\"isRaterBack\":false,\"createdTime\":1731496366000,\"kpiTypeWeight\":0,\"createdUser\":\"*********\",\"updatedTime\":1727341090000,\"typeOrder\":1,\"maxExtraScore\":0,\"plusSubInterval\":{\"max\":\"\",\"min\":\"\"},\"version\":0,\"openOkrScore\":0,\"isOkr\":\"false\",\"companyId\":{\"id\":\"1200196\"},\"kpiTypeUsedFields\":[{\"updatedTime\":1718673812000,\"adminType\":1,\"show\":1,\"sort\":1,\"taskUserId\":\"3076585\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"指标名称\",\"kpiTypeId\":\"16529306\",\"createdTime\":1731496366000,\"fieldId\":\"name\",\"req\":1,\"status\":\"valid\"},{\"updatedTime\":1718673812000,\"adminType\":1,\"show\":1,\"sort\":2,\"taskUserId\":\"3076585\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"考核标准\",\"kpiTypeId\":\"16529306\",\"createdTime\":1731496366000,\"fieldId\":\"standard\",\"req\":0,\"status\":\"valid\"},{\"updatedTime\":1718673812000,\"adminType\":1,\"show\":1,\"sort\":3,\"taskUserId\":\"3076585\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"计分规则\",\"kpiTypeId\":\"16529306\",\"createdTime\":1731496366000,\"fieldId\":\"scoreRule\",\"req\":0,\"status\":\"valid\"},{\"updatedTime\":1718673812000,\"adminType\":1,\"show\":0,\"sort\":4,\"taskUserId\":\"3076585\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"目标值\",\"kpiTypeId\":\"16529306\",\"createdTime\":1731496366000,\"fieldId\":\"targetValue\",\"req\":1,\"status\":\"valid\"},{\"updatedTime\":1718673812000,\"adminType\":1,\"show\":0,\"sort\":5,\"taskUserId\":\"3076585\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"单位\",\"kpiTypeId\":\"16529306\",\"createdTime\":1731496366000,\"fieldId\":\"unit\",\"req\":1,\"status\":\"valid\"},{\"updatedTime\":1718673812000,\"adminType\":1,\"show\":1,\"sort\":6,\"taskUserId\":\"3076585\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"指标权重\",\"kpiTypeId\":\"16529306\",\"createdTime\":1731496366000,\"fieldId\":\"indexWeight\",\"req\":1,\"status\":\"valid\"},{\"updatedTime\":1718673812000,\"adminType\":1,\"show\":1,\"sort\":7,\"taskUserId\":\"3076585\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"评分分值\",\"kpiTypeId\":\"16529306\",\"createdTime\":1731496366000,\"fieldId\":\"scoreValue\",\"req\":1,\"status\":\"valid\"},{\"updatedTime\":1718673812000,\"adminType\":1,\"show\":0,\"sort\":8,\"taskUserId\":\"3076585\",\"type\":1,\"kpiSyncReqConf\":{\"syncOpen\":0,\"reqItems\":{\"inputFinishValue\":{\"isReq\":0,\"desc\":\"完成值必填\"},\"attachment\":{\"isReq\":0,\"desc\":\"附件必填\"},\"comment\":{\"isReq\":0,\"desc\":\"备注必填\"}}},\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"完成值录入\",\"kpiTypeId\":\"16529306\",\"createdTime\":1731496366000,\"fieldId\":\"finishValue\",\"req\":1,\"status\":\"valid\"}],\"kpiTypeName\":\"价值观\",\"kpiTypeId\":\"16529306\",\"items\":[{\"alreadyNodes\":[],\"itemRule\":\"讲真话：工作中知无不言，言无不尽，拒绝“说假话”，拒绝“真话说一半”。站在专业角度讲真话，主动分享专业意见，不做旁观者。\\n真诚地讲：出于善意、公心地讲，不畏惧冲突，真诚坦荡，把问题放在桌面上讨论解决。 听者能正确理解，不会把讲真误解为恶意针对。\",\"reserveOkrWeight\":3,\"showFinishBar\":0,\"itemUnit\":\"%\",\"multipleReviewersType\":\"or\",\"taskUserId\":\"3076585\",\"itemScoreValue\":\"{\\\"type\\\":\\\"toMainScore\\\"}\",\"showTargetValue\":\"true\",\"formulaType\":1,\"finishValueType\":1,\"createdTime\":1731496366000,\"id\":\"16529307\",\"createdUser\":\"*********\",\"scorerType\":\"exam\",\"order\":0,\"itemFormula\":\"\",\"updatedTime\":1731496366000,\"thresholdJson\":\"[]\",\"typeOrder\":1,\"plusSubInterval\":{\"max\":\"\",\"min\":\"\"},\"inputFormat\":\"text\",\"version\":2,\"companyId\":{\"id\":\"1200196\"},\"resultInputType\":\"no\",\"itemFullScoreCfg\":\"false\",\"mustResultInput\":0,\"itemTargetValue\":0,\"itemFieldJson\":\"[{\\\"companyId\\\":\\\"1200196\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"完成值\\\",\\\"companyFieldId\\\":\\\"9df89216-59a9-4a52-916b-33ad5a70d963\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"完成值\\\",\\\"value\\\":0,\\\"yVal\\\":true,\\\"unit\\\":\\\"\\\"},{\\\"companyId\\\":\\\"1200196\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"目标值\\\",\\\"companyFieldId\\\":\\\"ffe59b49-85e5-4709-9fc2-3a897bd207cc\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"目标值\\\",\\\"value\\\":0,\\\"yVal\\\":true,\\\"unit\\\":\\\"\\\"}]\",\"formulaCondition\":\"[{\\\"companyId\\\":\\\"1200196\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"完成值\\\",\\\"companyFieldId\\\":\\\"9df89216-59a9-4a52-916b-33ad5a70d963\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"完成值\\\",\\\"value\\\":0,\\\"yVal\\\":true,\\\"unit\\\":\\\"\\\",\\\"kpiItemId\\\":\\\"41b86018-dcbe-49f7-be26-ead088bb5225\\\"},{\\\"companyId\\\":\\\"1200196\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"目标值\\\",\\\"companyFieldId\\\":\\\"ffe59b49-85e5-4709-9fc2-3a897bd207cc\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"目标值\\\",\\\"value\\\":0,\\\"yVal\\\":true,\\\"unit\\\":\\\"\\\",\\\"kpiItemId\\\":\\\"41b86018-dcbe-49f7-be26-ead088bb5225\\\"}]\",\"empId\":\"*********\",\"itemType\":\"non-measurable\",\"scorerObjId\":[{\"objItems\":[],\"objType\":\"role\"},{\"objItems\":[],\"objType\":\"user\"},{\"objItems\":[],\"objType\":\"manager\"}],\"finishValueAuditStatus\":0,\"itemScoreRule\":{\"superRater\":{\"nodeWeight\":100,\"superiorScoreOrder\":\"sameTime\",\"nodeVacancyFlag\":1,\"signatureFlag\":false,\"auditNodes\":[{\"transferFlag\":\"true\",\"approvalOrder\":1,\"weight\":100,\"node\":\"superior_score\",\"raters\":[{\"empId\":\"*********\",\"level\":1,\"type\":1,\"empName\":\"张爱香\"}],\"multiType\":\"or\",\"approverType\":\"manager\"}],\"open\":1},\"updatedTime\":1731496366000,\"kpiItemId\":\"41b86018-dcbe-49f7-be26-ead088bb5225\",\"peerRater\":{\"transferFlag\":\"true\",\"nodeWeight\":0,\"change\":false,\"node\":\"peer_score\",\"excludeAllManager\":false,\"raters\":[],\"signatureFlag\":false,\"multiType\":\"and\",\"appointer\":{\"type\":\"emp\",\"raters\":[],\"inviteMutualAudit\":{\"approverInfo\":\"1ba36678-95b9-4b0c-898b-9eb8359dcdb8\",\"approverName\":\"HRBP\",\"raters\":[{\"empId\":\"*********\",\"roleId\":\"1ba36678-95b9-4b0c-898b-9eb8359dcdb8\",\"type\":0,\"empName\":\"张爱香\",\"roleName\":\"HRBP\"}],\"multiType\":\"or\",\"approverType\":\"role\",\"open\":1}},\"scorerNumCof\":{\"action\":\"regular\",\"open\":\"false\"},\"open\":1},\"appointRater\":{\"signatureFlag\":false,\"auditNodes\":[],\"open\":0},\"mutualScoreFlag\":\"true\",\"appointScoreFlag\":\"false\",\"taskUserId\":\"3076585\",\"updatedUser\":\"*********\",\"version\":1,\"superiorScoreWeight\":100,\"superiorScoreFlag\":\"true\",\"companyId\":{\"id\":\"1200196\"},\"selfScoreFlag\":\"true\",\"selfRater\":{\"nodeWeight\":0,\"signatureFlag\":false,\"anonymous\":\"false\",\"open\":1},\"selfScoreWeight\":0,\"isDeleted\":\"false\",\"kpiTypeId\":\"16529306\",\"createdTime\":1731496366000,\"id\":\"11512046\",\"taskId\":\"2119611\",\"peerScoreWeight\":0,\"subRater\":{\"change\":false,\"excludeAllManager\":false,\"raters\":[],\"signatureFlag\":false,\"open\":0},\"createdUser\":\"*********\"},\"managerLevel\":\"\",\"kpiItemName\":\"讲真\",\"otherReqField\":{\"inputFinishValue\":{\"isReq\":0,\"desc\":\"完成值必填\"},\"attachment\":{\"isReq\":0,\"desc\":\"附件必填\"},\"comment\":{\"isReq\":0,\"desc\":\"备注必填\"}},\"isDeleted\":\"false\",\"finalSubmitFinishValue\":0,\"kpiTypeWeight\":0,\"itemTargetValueText\":\"\",\"inputRole\":[],\"itemWeight\":3,\"scoringRule\":\"\",\"kpiItemId\":\"41b86018-dcbe-49f7-be26-ead088bb5225\",\"maxExtraScore\":0,\"appointAudits\":[],\"openOkrScore\":0,\"isOkr\":\"false\",\"resultInputEmpId\":\"\",\"kpiTypeName\":\"价值观\",\"kpiTypeId\":\"16529306\",\"isNewEmp\":0,\"formulaFields\":[{\"updatedTime\":1731496366000,\"kpiItemId\":\"41b86018-dcbe-49f7-be26-ead088bb5225\",\"formulaFieldValue\":0,\"companyFieldId\":\"9df89216-59a9-4a52-916b-33ad5a70d963\",\"taskUserId\":\"3076585\",\"updatedUser\":\"*********\",\"version\":0,\"companyId\":{\"id\":\"1200196\"},\"isDeleted\":\"false\",\"formulaFieldName\":\"完成值\",\"createdTime\":1731496366000,\"id\":\"12601279\",\"taskId\":\"2119611\",\"createdUser\":\"*********\"},{\"updatedTime\":1731496366000,\"kpiItemId\":\"41b86018-dcbe-49f7-be26-ead088bb5225\",\"formulaFieldValue\":0,\"companyFieldId\":\"ffe59b49-85e5-4709-9fc2-3a897bd207cc\",\"taskUserId\":\"3076585\",\"updatedUser\":\"*********\",\"version\":0,\"companyId\":{\"id\":\"1200196\"},\"isDeleted\":\"false\",\"formulaFieldName\":\"目标值\",\"createdTime\":1731496366000,\"id\":\"12601280\",\"taskId\":\"2119611\",\"createdUser\":\"*********\"}],\"fieldValueList\":[],\"taskId\":\"2119611\",\"waitScores\":[]},{\"alreadyNodes\":[],\"itemRule\":\"拿结果说话：行必果，为过程鼓掌，为结果买单，不让雷锋吃亏，让优秀的人脱颖而出。\\n快速响应和行动： 言必行，快速响应，迅速行动，拒绝摸鱼，鼓励奋斗。\",\"reserveOkrWeight\":3,\"showFinishBar\":0,\"itemUnit\":\"%\",\"multipleReviewersType\":\"or\",\"taskUserId\":\"3076585\",\"itemScoreValue\":\"{\\\"type\\\":\\\"toMainScore\\\"}\",\"showTargetValue\":\"true\",\"formulaType\":1,\"finishValueType\":1,\"createdTime\":1731496366000,\"id\":\"16529308\",\"createdUser\":\"*********\",\"scorerType\":\"exam\",\"order\":1,\"itemFormula\":\"\",\"updatedTime\":1731496366000,\"thresholdJson\":\"[]\",\"typeOrder\":1,\"plusSubInterval\":{\"max\":\"\",\"min\":\"\"},\"inputFormat\":\"text\",\"version\":2,\"companyId\":{\"id\":\"1200196\"},\"resultInputType\":\"no\",\"itemFullScoreCfg\":\"false\",\"mustResultInput\":0,\"itemTargetValue\":0,\"itemFieldJson\":\"[{\\\"companyId\\\":\\\"1200196\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"完成值\\\",\\\"companyFieldId\\\":\\\"9df89216-59a9-4a52-916b-33ad5a70d963\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"完成值\\\",\\\"value\\\":0,\\\"yVal\\\":true,\\\"unit\\\":\\\"\\\"},{\\\"companyId\\\":\\\"1200196\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"目标值\\\",\\\"companyFieldId\\\":\\\"ffe59b49-85e5-4709-9fc2-3a897bd207cc\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"目标值\\\",\\\"value\\\":0,\\\"yVal\\\":true,\\\"unit\\\":\\\"\\\"}]\",\"formulaCondition\":\"[{\\\"companyId\\\":\\\"1200196\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"完成值\\\",\\\"companyFieldId\\\":\\\"9df89216-59a9-4a52-916b-33ad5a70d963\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"完成值\\\",\\\"value\\\":0,\\\"yVal\\\":true,\\\"unit\\\":\\\"\\\",\\\"kpiItemId\\\":\\\"5ef7f62c-59a3-4242-a64d-b219132e0b5c\\\"},{\\\"companyId\\\":\\\"1200196\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"目标值\\\",\\\"companyFieldId\\\":\\\"ffe59b49-85e5-4709-9fc2-3a897bd207cc\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"目标值\\\",\\\"value\\\":0,\\\"yVal\\\":true,\\\"unit\\\":\\\"\\\",\\\"kpiItemId\\\":\\\"5ef7f62c-59a3-4242-a64d-b219132e0b5c\\\"}]\",\"empId\":\"*********\",\"itemType\":\"non-measurable\",\"scorerObjId\":[{\"objItems\":[],\"objType\":\"role\"},{\"objItems\":[],\"objType\":\"user\"},{\"objItems\":[],\"objType\":\"manager\"}],\"finishValueAuditStatus\":0,\"itemScoreRule\":{\"superRater\":{\"nodeWeight\":100,\"superiorScoreOrder\":\"sameTime\",\"nodeVacancyFlag\":1,\"signatureFlag\":false,\"auditNodes\":[{\"transferFlag\":\"true\",\"approvalOrder\":1,\"weight\":100,\"node\":\"superior_score\",\"raters\":[{\"empId\":\"*********\",\"level\":1,\"type\":1,\"empName\":\"张爱香\"}],\"multiType\":\"or\",\"approverType\":\"manager\"}],\"open\":1},\"updatedTime\":1731496366000,\"kpiItemId\":\"5ef7f62c-59a3-4242-a64d-b219132e0b5c\",\"peerRater\":{\"transferFlag\":\"true\",\"nodeWeight\":0,\"change\":false,\"node\":\"peer_score\",\"excludeAllManager\":false,\"raters\":[],\"signatureFlag\":false,\"multiType\":\"and\",\"appointer\":{\"type\":\"emp\",\"raters\":[],\"inviteMutualAudit\":{\"approverInfo\":\"1ba36678-95b9-4b0c-898b-9eb8359dcdb8\",\"approverName\":\"HRBP\",\"raters\":[{\"empId\":\"*********\",\"roleId\":\"1ba36678-95b9-4b0c-898b-9eb8359dcdb8\",\"type\":0,\"empName\":\"张爱香\",\"roleName\":\"HRBP\"}],\"multiType\":\"or\",\"approverType\":\"role\",\"open\":1}},\"scorerNumCof\":{\"action\":\"regular\",\"open\":\"false\"},\"open\":1},\"appointRater\":{\"signatureFlag\":false,\"auditNodes\":[],\"open\":0},\"mutualScoreFlag\":\"true\",\"appointScoreFlag\":\"false\",\"taskUserId\":\"3076585\",\"updatedUser\":\"*********\",\"version\":1,\"superiorScoreWeight\":100,\"superiorScoreFlag\":\"true\",\"companyId\":{\"id\":\"1200196\"},\"selfScoreFlag\":\"true\",\"selfRater\":{\"nodeWeight\":0,\"signatureFlag\":false,\"anonymous\":\"false\",\"open\":1},\"selfScoreWeight\":0,\"isDeleted\":\"false\",\"kpiTypeId\":\"16529306\",\"createdTime\":1731496366000,\"id\":\"11512047\",\"taskId\":\"2119611\",\"peerScoreWeight\":0,\"subRater\":{\"change\":false,\"excludeAllManager\":false,\"raters\":[],\"signatureFlag\":false,\"open\":0},\"createdUser\":\"*********\"},\"managerLevel\":\"\",\"kpiItemName\":\"求实\",\"otherReqField\":{\"inputFinishValue\":{\"isReq\":0,\"desc\":\"完成值必填\"},\"attachment\":{\"isReq\":0,\"desc\":\"附件必填\"},\"comment\":{\"isReq\":0,\"desc\":\"备注必填\"}},\"isDeleted\":\"false\",\"finalSubmitFinishValue\":0,\"kpiTypeWeight\":0,\"itemTargetValueText\":\"\",\"inputRole\":[],\"itemWeight\":4,\"scoringRule\":\"\",\"kpiItemId\":\"5ef7f62c-59a3-4242-a64d-b219132e0b5c\",\"maxExtraScore\":0,\"appointAudits\":[],\"openOkrScore\":0,\"isOkr\":\"false\",\"resultInputEmpId\":\"\",\"kpiTypeName\":\"价值观\",\"kpiTypeId\":\"16529306\",\"isNewEmp\":0,\"formulaFields\":[{\"updatedTime\":1731496366000,\"kpiItemId\":\"5ef7f62c-59a3-4242-a64d-b219132e0b5c\",\"formulaFieldValue\":0,\"companyFieldId\":\"9df89216-59a9-4a52-916b-33ad5a70d963\",\"taskUserId\":\"3076585\",\"updatedUser\":\"*********\",\"version\":0,\"companyId\":{\"id\":\"1200196\"},\"isDeleted\":\"false\",\"formulaFieldName\":\"完成值\",\"createdTime\":1731496366000,\"id\":\"12601281\",\"taskId\":\"2119611\",\"createdUser\":\"*********\"},{\"updatedTime\":1731496366000,\"kpiItemId\":\"5ef7f62c-59a3-4242-a64d-b219132e0b5c\",\"formulaFieldValue\":0,\"companyFieldId\":\"ffe59b49-85e5-4709-9fc2-3a897bd207cc\",\"taskUserId\":\"3076585\",\"updatedUser\":\"*********\",\"version\":0,\"companyId\":{\"id\":\"1200196\"},\"isDeleted\":\"false\",\"formulaFieldName\":\"目标值\",\"createdTime\":1731496366000,\"id\":\"12601282\",\"taskId\":\"2119611\",\"createdUser\":\"*********\"}],\"fieldValueList\":[],\"taskId\":\"2119611\",\"waitScores\":[]},{\"alreadyNodes\":[],\"itemRule\":\"创新突破：勇于突破，敢于创造和创新，主动变化，不墨守成规。\\n拥抱变化： 敏捷捕捉并适应外部市场环境的变化，随时迎接挑战，不被温水煮青蛙。\",\"reserveOkrWeight\":3,\"showFinishBar\":0,\"itemUnit\":\"%\",\"multipleReviewersType\":\"or\",\"taskUserId\":\"3076585\",\"itemScoreValue\":\"{\\\"type\\\":\\\"toMainScore\\\"}\",\"showTargetValue\":\"true\",\"formulaType\":1,\"finishValueType\":1,\"createdTime\":1731496366000,\"id\":\"16529309\",\"createdUser\":\"*********\",\"scorerType\":\"exam\",\"order\":2,\"itemFormula\":\"\",\"updatedTime\":1731496366000,\"thresholdJson\":\"[]\",\"typeOrder\":1,\"plusSubInterval\":{\"max\":\"\",\"min\":\"\"},\"inputFormat\":\"text\",\"version\":2,\"companyId\":{\"id\":\"1200196\"},\"resultInputType\":\"no\",\"itemFullScoreCfg\":\"false\",\"mustResultInput\":0,\"itemTargetValue\":0,\"itemFieldJson\":\"[{\\\"companyId\\\":\\\"1200196\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"完成值\\\",\\\"companyFieldId\\\":\\\"9df89216-59a9-4a52-916b-33ad5a70d963\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"完成值\\\",\\\"value\\\":0,\\\"yVal\\\":true,\\\"unit\\\":\\\"\\\"},{\\\"companyId\\\":\\\"1200196\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"目标值\\\",\\\"companyFieldId\\\":\\\"ffe59b49-85e5-4709-9fc2-3a897bd207cc\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"目标值\\\",\\\"value\\\":0,\\\"yVal\\\":true,\\\"unit\\\":\\\"\\\"}]\",\"formulaCondition\":\"[{\\\"companyId\\\":\\\"1200196\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"完成值\\\",\\\"companyFieldId\\\":\\\"9df89216-59a9-4a52-916b-33ad5a70d963\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"完成值\\\",\\\"value\\\":0,\\\"yVal\\\":true,\\\"unit\\\":\\\"\\\",\\\"kpiItemId\\\":\\\"3938b959-dd91-4398-9572-4b99e0fefd24\\\"},{\\\"companyId\\\":\\\"1200196\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"目标值\\\",\\\"companyFieldId\\\":\\\"ffe59b49-85e5-4709-9fc2-3a897bd207cc\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"目标值\\\",\\\"value\\\":0,\\\"yVal\\\":true,\\\"unit\\\":\\\"\\\",\\\"kpiItemId\\\":\\\"3938b959-dd91-4398-9572-4b99e0fefd24\\\"}]\",\"empId\":\"*********\",\"itemType\":\"non-measurable\",\"scorerObjId\":[{\"objItems\":[],\"objType\":\"role\"},{\"objItems\":[],\"objType\":\"user\"},{\"objItems\":[],\"objType\":\"manager\"}],\"finishValueAuditStatus\":0,\"itemScoreRule\":{\"superRater\":{\"nodeWeight\":100,\"superiorScoreOrder\":\"sameTime\",\"nodeVacancyFlag\":1,\"signatureFlag\":false,\"auditNodes\":[{\"transferFlag\":\"true\",\"approvalOrder\":1,\"weight\":100,\"node\":\"superior_score\",\"raters\":[{\"empId\":\"*********\",\"level\":1,\"type\":1,\"empName\":\"张爱香\"}],\"multiType\":\"or\",\"approverType\":\"manager\"}],\"open\":1},\"updatedTime\":1731496366000,\"kpiItemId\":\"3938b959-dd91-4398-9572-4b99e0fefd24\",\"peerRater\":{\"transferFlag\":\"true\",\"nodeWeight\":0,\"change\":false,\"node\":\"peer_score\",\"excludeAllManager\":false,\"raters\":[],\"signatureFlag\":false,\"multiType\":\"and\",\"appointer\":{\"type\":\"emp\",\"raters\":[],\"inviteMutualAudit\":{\"approverInfo\":\"1ba36678-95b9-4b0c-898b-9eb8359dcdb8\",\"approverName\":\"HRBP\",\"raters\":[{\"empId\":\"*********\",\"roleId\":\"1ba36678-95b9-4b0c-898b-9eb8359dcdb8\",\"type\":0,\"empName\":\"张爱香\",\"roleName\":\"HRBP\"}],\"multiType\":\"or\",\"approverType\":\"role\",\"open\":1}},\"scorerNumCof\":{\"action\":\"regular\",\"open\":\"false\"},\"open\":1},\"appointRater\":{\"signatureFlag\":false,\"auditNodes\":[],\"open\":0},\"mutualScoreFlag\":\"true\",\"appointScoreFlag\":\"false\",\"taskUserId\":\"3076585\",\"updatedUser\":\"*********\",\"version\":1,\"superiorScoreWeight\":100,\"superiorScoreFlag\":\"true\",\"companyId\":{\"id\":\"1200196\"},\"selfScoreFlag\":\"true\",\"selfRater\":{\"nodeWeight\":0,\"signatureFlag\":false,\"anonymous\":\"false\",\"open\":1},\"selfScoreWeight\":0,\"isDeleted\":\"false\",\"kpiTypeId\":\"16529306\",\"createdTime\":1731496366000,\"id\":\"11512048\",\"taskId\":\"2119611\",\"peerScoreWeight\":0,\"subRater\":{\"change\":false,\"excludeAllManager\":false,\"raters\":[],\"signatureFlag\":false,\"open\":0},\"createdUser\":\"*********\"},\"managerLevel\":\"\",\"kpiItemName\":\"思变\",\"otherReqField\":{\"inputFinishValue\":{\"isReq\":0,\"desc\":\"完成值必填\"},\"attachment\":{\"isReq\":0,\"desc\":\"附件必填\"},\"comment\":{\"isReq\":0,\"desc\":\"备注必填\"}},\"isDeleted\":\"false\",\"finalSubmitFinishValue\":0,\"kpiTypeWeight\":0,\"itemTargetValueText\":\"\",\"inputRole\":[],\"itemWeight\":3,\"scoringRule\":\"\",\"kpiItemId\":\"3938b959-dd91-4398-9572-4b99e0fefd24\",\"maxExtraScore\":0,\"appointAudits\":[],\"openOkrScore\":0,\"isOkr\":\"false\",\"resultInputEmpId\":\"\",\"kpiTypeName\":\"价值观\",\"kpiTypeId\":\"16529306\",\"isNewEmp\":0,\"formulaFields\":[{\"updatedTime\":1731496366000,\"kpiItemId\":\"3938b959-dd91-4398-9572-4b99e0fefd24\",\"formulaFieldValue\":0,\"companyFieldId\":\"9df89216-59a9-4a52-916b-33ad5a70d963\",\"taskUserId\":\"3076585\",\"updatedUser\":\"*********\",\"version\":0,\"companyId\":{\"id\":\"1200196\"},\"isDeleted\":\"false\",\"formulaFieldName\":\"完成值\",\"createdTime\":1731496366000,\"id\":\"12601283\",\"taskId\":\"2119611\",\"createdUser\":\"*********\"},{\"updatedTime\":1731496366000,\"kpiItemId\":\"3938b959-dd91-4398-9572-4b99e0fefd24\",\"formulaFieldValue\":0,\"companyFieldId\":\"ffe59b49-85e5-4709-9fc2-3a897bd207cc\",\"taskUserId\":\"3076585\",\"updatedUser\":\"*********\",\"version\":0,\"companyId\":{\"id\":\"1200196\"},\"isDeleted\":\"false\",\"formulaFieldName\":\"目标值\",\"createdTime\":1731496366000,\"id\":\"12601284\",\"taskId\":\"2119611\",\"createdUser\":\"*********\"}],\"fieldValueList\":[],\"taskId\":\"2119611\",\"waitScores\":[]}],\"itemLimitCnt\":{\"max\":\"\",\"openItemLimit\":\"false\",\"min\":\"\"},\"alreadyScores\":[],\"waitScores\":[]},{\"reserveOkrWeight\":0,\"scoreOptType\":2,\"taskUserId\":\"3076585\",\"finishValueAudit\":[{\"objItems\":[],\"objType\":\"role\"},{\"objItems\":[],\"objType\":\"user\"},{\"objItems\":[],\"objType\":\"manager\"}],\"des\":\"权重90%，10-8.6分（含）为优秀，8.6-6分（含）为符合，6-5分（含）为不达预期，5分以下为不能胜任工作。\",\"isDeleted\":\"false\",\"isRaterBack\":false,\"createdTime\":1731496366000,\"kpiTypeWeight\":0,\"createdUser\":\"*********\",\"updatedTime\":1727341090000,\"scoringType\":1,\"typeOrder\":0,\"maxExtraScore\":0,\"plusSubInterval\":{\"max\":\"\",\"min\":\"\"},\"version\":0,\"openOkrScore\":0,\"isOkr\":\"false\",\"companyId\":{\"id\":\"1200196\"},\"kpiTypeUsedFields\":[{\"updatedTime\":1718673812000,\"adminType\":1,\"show\":1,\"sort\":1,\"taskUserId\":\"3076585\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"指标名称\",\"kpiTypeId\":\"16529310\",\"createdTime\":1731496366000,\"fieldId\":\"name\",\"req\":1,\"status\":\"valid\"},{\"updatedTime\":1718673812000,\"adminType\":1,\"show\":1,\"sort\":2,\"taskUserId\":\"3076585\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"考核标准\",\"kpiTypeId\":\"16529310\",\"createdTime\":1731496366000,\"fieldId\":\"standard\",\"req\":0,\"status\":\"valid\"},{\"updatedTime\":1718673812000,\"adminType\":1,\"show\":0,\"sort\":3,\"taskUserId\":\"3076585\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"计分规则\",\"kpiTypeId\":\"16529310\",\"createdTime\":1731496366000,\"fieldId\":\"scoreRule\",\"req\":0,\"status\":\"valid\"},{\"updatedTime\":1718673812000,\"adminType\":1,\"show\":0,\"sort\":4,\"taskUserId\":\"3076585\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"目标值\",\"kpiTypeId\":\"16529310\",\"createdTime\":1731496366000,\"fieldId\":\"targetValue\",\"req\":1,\"status\":\"valid\"},{\"updatedTime\":1718673812000,\"adminType\":1,\"show\":0,\"sort\":5,\"taskUserId\":\"3076585\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"单位\",\"kpiTypeId\":\"16529310\",\"createdTime\":1731496366000,\"fieldId\":\"unit\",\"req\":1,\"status\":\"valid\"},{\"updatedTime\":1718673812000,\"adminType\":1,\"show\":1,\"sort\":6,\"taskUserId\":\"3076585\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"指标权重\",\"kpiTypeId\":\"16529310\",\"createdTime\":1731496366000,\"fieldId\":\"indexWeight\",\"req\":1,\"status\":\"valid\"},{\"updatedTime\":1718673812000,\"adminType\":1,\"show\":1,\"sort\":7,\"taskUserId\":\"3076585\",\"type\":1,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"评分分值\",\"kpiTypeId\":\"16529310\",\"createdTime\":1731496366000,\"fieldId\":\"scoreValue\",\"req\":1,\"status\":\"valid\"},{\"updatedTime\":1718673812000,\"adminType\":1,\"show\":1,\"sort\":8,\"taskUserId\":\"3076585\",\"type\":1,\"kpiSyncReqConf\":{\"syncOpen\":0,\"reqItems\":{\"inputFinishValue\":{\"isReq\":0,\"desc\":\"完成值必填\"},\"attachment\":{\"isReq\":0,\"desc\":\"附件必填\"},\"comment\":{\"isReq\":0,\"desc\":\"备注必填\"}}},\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"完成值录入\",\"kpiTypeId\":\"16529310\",\"createdTime\":1731496366000,\"fieldId\":\"finishValue\",\"req\":1,\"status\":\"valid\"}],\"kpiTypeName\":\"业绩达成\",\"kpiTypeId\":\"16529310\",\"items\":[{\"alreadyNodes\":[],\"itemRule\":\"对于本考核周期内的工作目标完成情况，重点描述亮点业绩，要求内容具体、有数据或事实描述\",\"reserveOkrWeight\":0,\"showFinishBar\":0,\"itemUnit\":\"%\",\"multipleReviewersType\":\"or\",\"taskUserId\":\"3076585\",\"itemScoreValue\":\"{\\\"type\\\":\\\"toMainScore\\\"}\",\"finishValueAudit\":[{\"objItems\":[],\"objType\":\"role\"},{\"objItems\":[],\"objType\":\"user\"},{\"objItems\":[],\"objType\":\"manager\"}],\"showTargetValue\":\"\",\"formulaType\":1,\"finishValueType\":1,\"createdTime\":1731496366000,\"id\":\"16529311\",\"createdUser\":\"*********\",\"scorerType\":\"exam\",\"order\":0,\"itemFormula\":\"\",\"updatedTime\":1731496366000,\"thresholdJson\":\"[]\",\"typeOrder\":0,\"plusSubInterval\":{\"max\":\"\",\"min\":\"\"},\"inputFormat\":\"text\",\"version\":2,\"companyId\":{\"id\":\"1200196\"},\"resultInputType\":\"exam\",\"itemFullScoreCfg\":\"false\",\"mustResultInput\":1,\"itemTargetValue\":0,\"itemFieldJson\":\"[]\",\"formulaCondition\":\"[]\",\"empId\":\"*********\",\"itemType\":\"non-measurable\",\"itemFinishValueText\":\"1【招聘】：Q4入职17人，横琴审核为主，部分北京外包岗\\n2【优化】：Q4离职15人，其中11人为被动。配合业务实施优化，其中珠海8人无成本，占比73%，剩余技术3人虽有偿优化，但HC缩减\\n3【业务线年会】：思考并确定伴手礼及聚餐场地，统筹并确保推进顺利\\n4【活动/赛事支持】：配合VIP答谢宴及统筹干部10人安吉活动；协调落地巅峰赛事海报3版更新\\n5【OTD】：文化照片墙更新2版本，加增1版面及美观度调整；季度评优落地；战略会协助；干部名单校准；组织架构每月更新；新人180天跟进试用期考核提醒 \\n6【C&B】：HC收集和预算确认；Q4绩效实施；每月外包费预估及5部门60人+费用确认；月绩效奖金确认每月3个部门55人+审批确认；技术离职补偿金申请审批 \\n7【SSC】：SSC制度&流程发布前建议提报、员工续签/转正/居住证确定\\n8【行政IT】：珠海摄像头增设、断电协调、药品消毒品配置、集团礼品协调；北京\\n9【团队建设】：组织干部庆生1场；参与增长部密室团建&审核活动，增强配合粘性\",\"scorerObjId\":[{\"objItems\":[],\"objType\":\"role\"},{\"objItems\":[],\"objType\":\"user\"},{\"objItems\":[],\"objType\":\"manager\"}],\"finishValueAuditStatus\":0,\"itemScoreRule\":{\"superRater\":{\"nodeWeight\":100,\"superiorScoreOrder\":\"sameTime\",\"nodeVacancyFlag\":1,\"signatureFlag\":false,\"auditNodes\":[{\"transferFlag\":\"true\",\"approvalOrder\":1,\"weight\":100,\"node\":\"superior_score\",\"raters\":[{\"empId\":\"*********\",\"level\":1,\"type\":1,\"empName\":\"张爱香\"}],\"multiType\":\"or\",\"approverType\":\"manager\"}],\"open\":1},\"updatedTime\":1731496366000,\"kpiItemId\":\"65ef4d22-e82f-4c2b-bd71-275583dfa88d\",\"peerRater\":{\"transferFlag\":\"true\",\"nodeWeight\":0,\"change\":false,\"node\":\"peer_score\",\"excludeAllManager\":false,\"raters\":[],\"signatureFlag\":false,\"multiType\":\"and\",\"appointer\":{\"type\":\"emp\",\"raters\":[],\"inviteMutualAudit\":{\"approverInfo\":\"1ba36678-95b9-4b0c-898b-9eb8359dcdb8\",\"approverName\":\"HRBP\",\"raters\":[{\"empId\":\"*********\",\"roleId\":\"1ba36678-95b9-4b0c-898b-9eb8359dcdb8\",\"type\":0,\"empName\":\"张爱香\",\"roleName\":\"HRBP\"}],\"multiType\":\"or\",\"approverType\":\"role\",\"open\":1}},\"scorerNumCof\":{\"action\":\"regular\",\"open\":\"false\"},\"open\":1},\"appointRater\":{\"signatureFlag\":false,\"auditNodes\":[],\"open\":0},\"mutualScoreFlag\":\"true\",\"appointScoreFlag\":\"false\",\"taskUserId\":\"3076585\",\"updatedUser\":\"*********\",\"version\":1,\"superiorScoreWeight\":100,\"superiorScoreFlag\":\"true\",\"companyId\":{\"id\":\"1200196\"},\"selfScoreFlag\":\"true\",\"selfRater\":{\"nodeWeight\":0,\"signatureFlag\":false,\"anonymous\":\"false\",\"open\":1},\"selfScoreWeight\":0,\"isDeleted\":\"false\",\"kpiTypeId\":\"16529310\",\"createdTime\":1731496366000,\"id\":\"11512049\",\"taskId\":\"2119611\",\"peerScoreWeight\":0,\"subRater\":{\"change\":false,\"excludeAllManager\":false,\"raters\":[],\"signatureFlag\":false,\"open\":0},\"createdUser\":\"*********\"},\"managerLevel\":\"\",\"kpiItemName\":\"工作目标完成情况\",\"otherReqField\":{\"inputFinishValue\":{\"isReq\":1,\"desc\":\"完成值必填\"},\"attachment\":{\"isReq\":0,\"desc\":\"附件必填\"},\"comment\":{\"isReq\":0,\"desc\":\"备注必填\"}},\"isDeleted\":\"false\",\"finalSubmitFinishValue\":1,\"kpiTypeWeight\":0,\"itemTargetValueText\":\"\",\"inputRole\":[],\"itemWeight\":90,\"scoringRule\":\"\",\"kpiItemId\":\"65ef4d22-e82f-4c2b-bd71-275583dfa88d\",\"maxExtraScore\":0,\"appointAudits\":[],\"openOkrScore\":0,\"isOkr\":\"false\",\"resultInputEmpId\":\"*********\",\"kpiTypeName\":\"业绩达成\",\"kpiTypeId\":\"16529310\",\"isNewEmp\":0,\"formulaFields\":[],\"fieldValueList\":[],\"taskId\":\"2119611\",\"waitScores\":[]}],\"itemLimitCnt\":{\"max\":\"\",\"openItemLimit\":\"false\",\"min\":\"\"},\"alreadyScores\":[],\"waitScores\":[]}]";
        List<EmpEvalKpiType> list = JSONUtil.toList(json, EmpEvalKpiType.class);
        m.setKpiTypes(new KpiListWrap(list));
        List<String> lists = m.getResultInputEmpIds("*********");
        System.out.println(JSONUtil.toJsonStr(lists));
    }
    @Test
    @DisplayName("current")
    public void current() {
        // String cmdjson ="{\"totalAttUrl\":[],\"isReCommit\":false,\"isSystemSkip\":false,\"signatureUrl\":\"\",\"scoreSummarys\":[],\"taskUserId\":\"2965570\",\"itemScoreList\":[{\"kpiItemId\":\"67812a4d-9862-4a0e-9c8a-d1bbddc1ffd6\",\"approvalOrder\":1,\"scoreComment\":\"督导分80.3分\",\"mergeRsInfos\":[],\"version\":0,\"scoreAttUrl\":[],\"score\":23.94,\"isDeleted\":\"false\",\"signatureFlag\":false,\"kpiTypeId\":\"fe89e024-6b96-4287-a5da-bced2a10f219\",\"maySkip\":false,\"id\":\"b50dfad1-1484-461c-b553-53a85ff41797\",\"updateTypeLevel\":false,\"waitMergeWeight\":0},{\"kpiItemId\":\"26fd76f0-49ca-4d17-8180-5c3b93cafc16\",\"approvalOrder\":1,\"scoreComment\":\"完成112.32\",\"mergeRsInfos\":[],\"version\":0,\"scoreAttUrl\":[],\"score\":20,\"isDeleted\":\"false\",\"signatureFlag\":false,\"kpiTypeId\":\"fe89e024-6b96-4287-a5da-bced2a10f219\",\"maySkip\":false,\"id\":\"536917bc-aa68-45cb-a836-471f206a5251\",\"updateTypeLevel\":false,\"waitMergeWeight\":0},{\"kpiItemId\":\"e359a1e6-8c18-4d11-93c1-71f367b9a1b6\",\"approvalOrder\":1,\"scoreComment\":\"不达标完成98.01\",\"mergeRsInfos\":[],\"version\":0,\"scoreAttUrl\":[],\"score\":0,\"isDeleted\":\"false\",\"signatureFlag\":false,\"kpiTypeId\":\"fe89e024-6b96-4287-a5da-bced2a10f219\",\"maySkip\":false,\"id\":\"165bf254-1aa2-4f33-9793-04a0520affe0\",\"updateTypeLevel\":false,\"waitMergeWeight\":0},{\"kpiItemId\":\"ac3202e2-71b7-4ccf-baa1-fa1c146ee677\",\"approvalOrder\":1,\"scoreComment\":\"合理安排 \",\"mergeRsInfos \":[],\"version \":0,\"scoreAttUrl \":[],\"score \":5,\"isDeleted \":\"false \",\"signatureFlag \":false,\"kpiTypeId \":\"fe89e024 - 6 b96 - 4287 - a5da - bced2a10f219 \",\"maySkip \":false,\"id \":\"d2046cb5 - 026 c - 4 b64 - 9 cbf - 1 f8dbdf7fe06 \",\"updateTypeLevel \":false,\"waitMergeWeight \":0},{\"kpiItemId \":\"d527819c - 3079 - 4316 - 83 ac - 14 fe8cc50c04 \",\"approvalOrder \":1,\"scoreComment \":\"无异常 \",\"mergeRsInfos \":[],\"version \":0,\"scoreAttUrl \":[],\"score \":5,\"isDeleted \":\"false \",\"signatureFlag \":false,\"kpiTypeId \":\"fe89e024 - 6 b96 - 4287 - a5da - bced2a10f219 \",\"maySkip \":false,\"id \":\"433 f49cc - 6900 - 4 f45 - 8 baa - 89657 f2b37b3 \",\"updateTypeLevel \":false,\"waitMergeWeight \":0},{\"kpiItemId \":\"e05cf74a - e792 - 4 b51 - 9 a64 - 717 df9cd3959 \",\"approvalOrder \":1,\"scoreComment \":\"按要求执行 \",\"mergeRsInfos \":[],\"version \":0,\"scoreAttUrl \":[],\"score \":5,\"isDeleted \":\"false \",\"signatureFlag \":false,\"kpiTypeId \":\"fe89e024 - 6 b96 - 4287 - a5da - bced2a10f219 \",\"maySkip \":false,\"id \":\"c85bfc72 - 81 de - 4285 - 8 b5f - bd4ca4d6237b \",\"updateTypeLevel \":false,\"waitMergeWeight \":0},{\"kpiItemId \":\"e28880b2 - 942 c - 44 c1 - b8f9 - b2a88225f22c \",\"approvalOrder \":1,\"scoreComment \":\"及时回复 \",\"mergeRsInfos \":[],\"version \":0,\"scoreAttUrl \":[],\"score \":5,\"isDeleted \":\"false \",\"signatureFlag \":false,\"kpiTypeId \":\"fe89e024 - 6 b96 - 4287 - a5da - bced2a10f219 \",\"maySkip \":false,\"id \":\"187 b05a6 - 4675 - 4 cbe - b725 - f16a283c8ff8 \",\"updateTypeLevel \":false,\"waitMergeWeight \":0},{\"kpiItemId \":\"90 f20cf7 - f12a - 4 dbb - aac1 - 024283 f1702f \",\"approvalOrder \":1,\"scoreComment \":\"安全生产 \",\"mergeRsInfos \":[],\"version \":0,\"scoreAttUrl \":[],\"score \":5,\"isDeleted \":\"false \",\"signatureFlag \":false,\"kpiTypeId \":\"fe89e024 - 6 b96 - 4287 - a5da - bced2a10f219 \",\"maySkip \":false,\"id \":\"ff85591e - 9 d84 - 4 d81 - b281 - 99983 ca32a49 \",\"updateTypeLevel \":false,\"waitMergeWeight \":0},{\"kpiItemId \":\"b6548450 - 01 c7 - 4106 - 8 c60 - 5 d0dfca4db0d \",\"approvalOrder \":1,\"scoreComment \":\"安全通过 \",\"mergeRsInfos \":[],\"version \":0,\"scoreAttUrl \":[],\"score \":5,\"isDeleted \":\"false \",\"signatureFlag \":false,\"kpiTypeId \":\"fe89e024 - 6 b96 - 4287 - a5da - bced2a10f219 \",\"maySkip \":false,\"id \":\"22 f200ab - 3 d0e - 4e a5 - a140 - 9 fbe0c2c1cbb \",\"updateTypeLevel \":false,\"waitMergeWeight \":0},{\"kpiItemId \":\"f77a69a3 - 3237 - 43 b2 - 8401 - 8 b632b514970 \",\"approvalOrder \":1,\"scoreComment \":\"按时完成临时交办事项 \",\"mergeRsInfos \":[],\"version \":0,\"scoreAttUrl \":[],\"score \":10,\"isDeleted \":\"false \",\"signatureFlag \":false,\"kpiTypeId \":\"fe89e024 - 6 b96 - 4287 - a5da - bced2a10f219 \",\"maySkip \":false,\"id \":\"d0a7b0c5 - 8 fbd - 4 b8e - 96e f - 4 da2bda2d05d \",\"updateTypeLevel \":false,\"waitMergeWeight \":0}],\"scoreNode \":\"SELF_SCORE \",\"typeScores \":[],\"scorerType \":\"self_score \"}";
        //SubmitScoreCmd cmd = JSONUtil.toBean(cmdjson, SubmitScoreCmd.class);
        String evalMergejson = "{\"readerIds\":[],\"cycleId\":\"2141210\",\"scoreConf\":{\"transferFlag\":\"true\",\"multiType\":\"or\"},\"auditResult\":{\"empRepeatSkip\":0,\"notFindAdminBySupAdmin\":1,\"nodeEmpVacancy\":2,\"auditNodes\":[],\"open\":0},\"performanceType\":1,\"finishValueAudit\":{\"empRepeatSkip\":0,\"notFindAdminBySupAdmin\":1,\"nodeEmpVacancy\":2,\"auditNodes\":[],\"open\":0},\"totalCnt\":85,\"isDeleted\":\"false\",\"adminIds\":[],\"scoreSortConf\":{\"sortItems\":[{\"sort\":1,\"type\":10,\"name\":\"自评\"},{\"sort\":2,\"type\":20,\"name\":\"同级评\"},{\"sort\":3,\"type\":30,\"name\":\"下级评\"},{\"sort\":4,\"type\":40,\"name\":\"上级评分\"},{\"sort\":5,\"type\":50,\"name\":\"指定评\"}],\"exeType\":0},\"tmpTask\":false,\"createdTime\":1727573161000,\"drawUpCnt\":85,\"startCnt\":85,\"id\":\"2104813\",\"enterScore\":{\"enterScoreEmpType\":1,\"enterScoreMethod\":\"auto\",\"scoreStartRuleType\":\"before\",\"scoreStartRuleDay\":1},\"taskStatus\":\"published\",\"createdUser\":\"132858133\",\"updatedTime\":1728440934000,\"confirmTask\":{\"openConfirmLT\":0,\"empRepeatSkip\":0,\"notFindAdminBySupAdmin\":1,\"nodeEmpVacancy\":2,\"auditNodes\":[],\"modifyItemDimension\":\"all\",\"open\":0},\"interviewConf\":{\"empRepeatSkip\":0,\"notFindAdminBySupAdmin\":1,\"nodeEmpVacancy\":2,\"open\":0},\"appealConf\":{\"open\":0},\"deadLineConf\":{\"open\":0},\"finishCnt\":23,\"updatedUser\":\"\",\"version\":0,\"cycleEndDate\":\"2024-09-30\",\"cycleStartDate\":\"2024-09-01\",\"scoreView\":{\"mutualScoreAnonymous\":\"false\",\"mutualScoreViewRule\":{\"superior\":\"score,attach\",\"mutual\":\"score,attach\",\"appoint\":\"score,attach\",\"examinee\":\"score,attach\"},\"superiorScoreAnonymous\":\"false\",\"appointScoreViewRule\":{\"superior\":\"score,attach\",\"mutual\":\"score,attach\",\"appoint\":\"score,attach\",\"examinee\":\"score,attach\"},\"superiorScoreViewRule\":{\"superior\":\"score,attach\",\"mutual\":\"score,attach\",\"appoint\":\"score,attach\",\"examinee\":\"score,attach\"},\"selfScoreViewRule\":{\"superior\":\"score,attach\",\"mutual\":\"score,attach\",\"appoint\":\"score,attach\",\"examinee\":\"score,attach\"},\"appointScoreAnonymous\":\"false\"},\"companyId\":{\"id\":\"1205410\"},\"publishResult\":{\"toEmps\":[{\"objType\":\"emp\"}],\"type\":\"afterFinished\",\"scoreDetailPriv\":{\"scoreRemark\":[\"self_remark\",\"item_remark\",\"peer_remark\",\"sub_remark\",\"superior_remark\",\"appoint_remark\"],\"scoreType\":[]},\"opEmps\":[],\"toDetailEmps\":[],\"dimension\":11,\"open\":1},\"readers\":[],\"confirmResult\":{\"auto\":0,\"sign\":0,\"open\":0},\"editExeIndi\":{\"empRepeatSkip\":0,\"notFindAdminBySupAdmin\":1,\"nodeEmpVacancy\":2,\"auditNodes\":[],\"open\":0},\"taskName\":\"2024年9月考核\",\"commentConf\":{\"scoreSummarySwitch\":-1,\"commentFlag\":\"notRequired\",\"plusOrSubComment\":0},\"inputNotifyConf\":{\"sendType\":1},\"admins\":[]}";
        EmpEvalMerge evalMerge = JSONUtil.toBean(evalMergejson, EmpEvalMerge.class);
        ScoreChain scoreChain = new ScoreChain();
        scoreChain.currentScoreNoe(SubScoreNodeEnum.SELF_SCORE,1);
        evalMerge.setScoreChain(scoreChain);
        ChainNode current = evalMerge.current(SubScoreNodeEnum.SELF_SCORE,1);
        System.out.println(current);
    }

    @Test
    @DisplayName("transferScorerIds")
    public void transferScorerIds() {
        List<String> scorerIds = new ArrayList<>();
        scorerIds.add("146978264");
        //35b3e53b-18b3-4926-923b-796f2017a2bc de890d6b-567f-47b4-8733-c3452f1d03d0 3
        //d6888b5c-2103-43d1-b6d7-97cf3b0c5e11  4223ba6d-06f5-42d4-a91c-12a44e284ab2 1
        //83f4205d-5cf6-4570-9b2a-88fe6d5435ac 7ff6a84b-e07b-4419-8f8e-bf0431c2406f  1
        List<EvalScoreResult> totalEvalRs = new ArrayList<>();
        EvalScoreResult result1 =new EvalScoreResult();
        result1.setKpiTypeId("35b3e53b-18b3-4926-923b-796f2017a2bc");
        result1.setKpiItemId("de890d6b-567f-47b4-8733-c3452f1d03d0");
        result1.setScorerId("146978264");
        result1.setApprovalOrder(3);
        EvalScoreResult result2 =new EvalScoreResult();
        result2.setKpiTypeId("d6888b5c-2103-43d1-b6d7-97cf3b0c5e11");
        result2.setKpiItemId("4223ba6d-06f5-42d4-a91c-12a44e284ab2");
        result2.setScorerId("146978264");
        result2.setApprovalOrder(1);
        EvalScoreResult result3 =new EvalScoreResult();
        result3.setKpiTypeId("83f4205d-5cf6-4570-9b2a-88fe6d5435ac");
        result3.setKpiItemId("4223ba6d-06f5-42d4-a91c-12a44e284ab2");
        result3.setScorerId("146978264");
        result3.setApprovalOrder(1);
        totalEvalRs.add(result1);
        totalEvalRs.add(result2);
        totalEvalRs.add(result3);

        EmpEvalMerge evalMerge = new EmpEvalMerge();
        evalMerge.setTotalEvalRs(totalEvalRs);
        List<String> scoreIds = evalMerge.transferScorerIds(scorerIds,"146978264");
        System.out.println(scoreIds);
    }
}