package com.polaris.kpi.eval.domain.task.dmsvc;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.dmsvc.score.SkipScorerDmSvc;
import com.polaris.kpi.eval.domain.task.entity.EmpEvalScorer;
import com.polaris.kpi.eval.domain.task.entity.EvalKpi;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.empeval.EvalScorersWrap;
import com.polaris.kpi.eval.domain.task.entity.empeval.score.SkipScorer;
import com.polaris.sdk.common.JsonFileTool;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.*;

public class SkipScorerDmSvcTest {
    public static TenantId companyId = new TenantId("5a031297-1b38-48ae-bc82-375849835203");
    public static String taskUserId = "1280601";

    @Test
    @DisplayName("下级跳过校验")
    public void checkSub() {
        EvalScorersWrap scorers = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.dmsvc/SkipScorerDmSvcTest/EvalScorersWrap-sub.json", EvalScorersWrap.class);
        List<EvalKpi> kpis  = JsonFileTool.toList("com.polaris.kpi.eval.domain.task.dmsvc/SkipScorerDmSvcTest/kpiWrap.json", EvalKpi.class);
        ListWrap<EvalKpi> kpiWrap = new ListWrap<>(kpis).asMap(EvalKpi::asKpiItemKey);;
        SkipScorerDmSvc dmSvc = new SkipScorerDmSvc();
        dmSvc.setKpiWrap(kpiWrap);
        dmSvc.setScorersWrap(scorers);
        dmSvc.checkAllowSkip("1440001","sub_score");
    }
    @Test
    public void skip() {
        EvalUser user = new EvalUser();
        List<EmpEvalScorer> scorers = JsonFileTool.toList("com.polaris.kpi.eval.domain.task.dmsvc/SkipScorerDmSvcTest/EmpEvalScorer.json", EmpEvalScorer.class);
        EvalScorersWrap scorersWrap = new EvalScorersWrap(scorers);
        SkipScorer ss = new SkipScorer();
        ss.setScorersWrap(scorersWrap);
        ss.setEvalUser(user);
        Set<String> msgScenes = new HashSet<>();

        List<KpiEmp> kpiEmps = new ArrayList<>();
        kpiEmps.add(new KpiEmp("1602589","杨思威","https://static-legacy.dingtalk.com/media/lADPM5HikoKm7UnNAWPNAUQ_324_355.jpg"));
        kpiEmps.add(new KpiEmp("1302001","苏小秋","https://static-legacy.dingtalk.com/media/lADPD26eO1kRFzrNAbDNAbA_432_432.jpg"));
        ListWrap<KpiEmp> raterGroup = new ListWrap<>(kpiEmps).asMap(KpiEmp::getEmpId);
        ss.setRaterGroup(raterGroup);
        SkipScorerDmSvc skipScorerDmSvc = new SkipScorerDmSvc(companyId,taskUserId,null,null,null,null,null);

        skipScorerDmSvc.skipRater("1302001","superior_score");
        System.out.println(JSONUtil.toJsonStr(skipScorerDmSvc.needSaveUpEmpEvalScorers()));
    }
}
