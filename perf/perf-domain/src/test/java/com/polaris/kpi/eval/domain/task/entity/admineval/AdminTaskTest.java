package com.polaris.kpi.eval.domain.task.entity.admineval;

import org.junit.Assert;
import org.junit.Test;

import static org.junit.Assert.*;

public class AdminTaskTest {

    @Test
    public void wasOnTaskMod() {
        AdminTask adminTask = new AdminTask();
        adminTask.setScoreRuleSnapId("-10000");
        System.out.println(adminTask.wasOnTaskMod());
        Assert.assertFalse(adminTask.wasOnTaskMod());

        adminTask.setScoreRuleSnapId(null);
        System.out.println(adminTask.wasOnTaskMod());
        Assert.assertFalse(adminTask.wasOnTaskMod());


        adminTask.setScoreRuleSnapId("10001");
        System.out.println(adminTask.wasOnTaskMod());
        Assert.assertTrue(adminTask.wasOnTaskMod());

    }
}