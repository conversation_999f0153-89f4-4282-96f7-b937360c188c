package com.polaris.kpi.eval.domain.task.dmsvc;

import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.sdk.common.JsonFileTool;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.List;
import java.util.Objects;

public class RejectConfirmDmSvcTest {

    @Test
    @DisplayName("重复驳回")
    public void needReject() {
        List<EvalScoreResult> resultList = JsonFileTool.toList("com.polaris.kpi.eval.domain.task.dmsvc/taskUserDao.listResultByScoreTypes.json", EvalScoreResult.class);
        RejectConfirmDmSvc confirmDmSvc = new RejectConfirmDmSvc(resultList);
        Assert.assertTrue(confirmDmSvc.needReject());
        Assert.assertTrue(Objects.equals(confirmDmSvc.getOrderLevel(),3));
        Assert.assertTrue(Objects.equals(confirmDmSvc.getUpLevel(),1));
    }

    @Test
    @DisplayName("不跳过驳回")
    public void needReject2() {
        List<EvalScoreResult> resultList = JsonFileTool.toList("com.polaris.kpi.eval.domain.task.dmsvc/taskUserDao.listResultByScoreTypes2.json", EvalScoreResult.class);
        RejectConfirmDmSvc confirmDmSvc = new RejectConfirmDmSvc(resultList);
        Assert.assertTrue(confirmDmSvc.needReject());
        Assert.assertTrue(Objects.equals(confirmDmSvc.getOrderLevel(),3));
        Assert.assertTrue(Objects.equals(confirmDmSvc.getUpLevel(),2));
    }
    @Test
    @DisplayName("空缺驳回")
    public void needReject3() {
        List<EvalScoreResult> resultList = JsonFileTool.toList("com.polaris.kpi.eval.domain.task.dmsvc/taskUserDao.listResultByScoreTypes3.json", EvalScoreResult.class);
        RejectConfirmDmSvc confirmDmSvc = new RejectConfirmDmSvc(resultList);
        Assert.assertTrue(confirmDmSvc.needReject());
        Assert.assertTrue(Objects.equals(confirmDmSvc.getOrderLevel(),3));
        Assert.assertTrue(Objects.equals(confirmDmSvc.getUpLevel(),1));
    }

    @Test
    @DisplayName("驳回上级")
    public void needReject4() {
        List<EvalScoreResult> resultList = JsonFileTool.toList("com.polaris.kpi.eval.domain.task.dmsvc/taskUserDao.listResultByScoreTypes4.json", EvalScoreResult.class);
        RejectConfirmDmSvc confirmDmSvc = new RejectConfirmDmSvc(resultList);
        Assert.assertTrue(confirmDmSvc.needReject());
        Assert.assertTrue(Objects.equals(confirmDmSvc.getOrderLevel(),3));
        Assert.assertTrue(Objects.equals(confirmDmSvc.getUpLevel(),1));
    }
}