package com.polaris.kpi.eval.domain.task.dmsvc;

import cn.com.polaris.kpi.AuditSetting;
import cn.com.polaris.kpi.EmpStaff;
import cn.com.polaris.kpi.KpiDeptCode;
import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.company.TenantSysConf;
import cn.com.polaris.kpi.dept.DeptLeader;
import cn.com.polaris.kpi.dept.KpiRole;
import cn.com.polaris.kpi.dept.Leader;
import cn.com.polaris.kpi.eval.Rater;
import com.polaris.kpi.eval.domain.task.entity.PeerRaterParseRule;
import com.polaris.kpi.eval.domain.task.entity.RaterParseRule;
import com.polaris.kpi.org.domain.dept.dmsvc.ExtEmpParser;
import com.polaris.sdk.type.EmpId;
import com.polaris.kpi.org.domain.dept.dmsvc.LevelLeaderParser;
import com.polaris.kpi.org.domain.dept.dmsvc.RoleParser;
import com.polaris.kpi.org.domain.dept.repo.DeptFinder;
import com.polaris.sdk.type.TenantId;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class RaterRuleParserDmSvcTest {

    @Mock
    private TenantId tenantId;
    @Mock
    private LevelLeaderParser leaderParser;
    @Mock
    private RoleParser roleParser;
    @Mock
    private ExtEmpParser empParser;
    @Mock
    private DeptFinder deptFinder;
    @Mock
    private AuditSetting auditSetting;
    @Mock
    private TenantSysConf sysConf;

    private RaterRuleParserDmSvc raterRuleParserDmSvc;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // 重置所有模拟对象，确保每个测试方法都有干净的模拟对象
        reset(tenantId, leaderParser, roleParser, empParser, deptFinder, auditSetting, sysConf);
        raterRuleParserDmSvc = new RaterRuleParserDmSvc(tenantId, leaderParser, roleParser, empParser, deptFinder, auditSetting, sysConf);
    }

    @Test
    public void testParseOrgEmpByLevel_WithEmptyOrgEmps() {
        // 准备测试数据
        String orgId = "org123";
        Integer orgLevel = 1;
        String evalEmpId = "emp123";
        PeerRaterParseRule parseRule = mock(PeerRaterParseRule.class);

        // 模拟行为
        when(deptFinder.listEmpByDeptLevel(tenantId, orgId, orgLevel)).thenReturn(Collections.emptyList());
        when(parseRule.vacancyToOther(auditSetting)).thenReturn(false);
        when(auditSetting.nodeEmpVacancySkip()).thenReturn(true);

        // 执行测试
        List<Rater> result = raterRuleParserDmSvc.parseOrgEmpByLevel(parseRule, orgId, orgLevel, evalEmpId);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("-1", result.get(0).getEmpId());
        assertEquals("", result.get(0).getEmpName());
        assertEquals(Integer.valueOf(1), result.get(0).getSkipType());
    }

    @Test
    public void testParseOrgEmpByLevel_WithOrgEmps() {
        // 准备测试数据
        String orgId = "org123";
        Integer orgLevel = 1;
        String evalEmpId = "emp123";
        PeerRaterParseRule parseRule = mock(PeerRaterParseRule.class);

        List<EmpStaff> orgEmps = new ArrayList<>();
        EmpStaff empStaff1 = new EmpStaff();
        empStaff1.setEmpId("emp1");
        empStaff1.setEmpName("Employee 1");
        empStaff1.setOrgId("org1");
        empStaff1.setOrgName("Org 1");

        EmpStaff empStaff2 = new EmpStaff();
        empStaff2.setEmpId("emp2");
        empStaff2.setEmpName("Employee 2");
        empStaff2.setOrgId("org2");
        empStaff2.setOrgName("Org 2");

        orgEmps.add(empStaff1);
        orgEmps.add(empStaff2);

        // 模拟行为
        when(deptFinder.listEmpByDeptLevel(tenantId, orgId, orgLevel)).thenReturn(orgEmps);
        when(sysConf.isOpen()).thenReturn(false);

        // 执行测试
        List<Rater> result = raterRuleParserDmSvc.parseOrgEmpByLevel(parseRule, orgId, orgLevel, evalEmpId);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        assertEquals("emp1", result.get(0).getEmpId());
        assertEquals("Employee 1", result.get(0).getEmpName());
        assertEquals("org1", result.get(0).getOrgId());
        assertEquals("Org 1", result.get(0).getOrgName());

        assertEquals("emp2", result.get(1).getEmpId());
        assertEquals("Employee 2", result.get(1).getEmpName());
        assertEquals("org2", result.get(1).getOrgId());
        assertEquals("Org 2", result.get(1).getOrgName());
    }

    @Test
    public void testParseOrgEmp_WithEmptyOrgEmps() {
        // 准备测试数据
        String orgId = "org123";
        String evalOrgId = "evalOrg123";
        RaterParseRule parseRule = mock(RaterParseRule.class);

        // 模拟行为
        when(deptFinder.listEmpByDeptId(tenantId, orgId)).thenReturn(Collections.emptyList());
        when(parseRule.vacancyToOther(auditSetting)).thenReturn(false);
        when(auditSetting.nodeEmpVacancySkip()).thenReturn(true);

        // 执行测试
        List<Rater> result = raterRuleParserDmSvc.parseOrgEmp(parseRule, orgId);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("-1", result.get(0).getEmpId());
        assertEquals("", result.get(0).getEmpName());
        assertEquals(Integer.valueOf(1), result.get(0).getSkipType());
    }

    @Test
    public void testParseOrgEmp_WithOrgEmps() {
        // 准备测试数据
        String orgId = "org123";
        String evalOrgId = "evalOrg123";
        RaterParseRule parseRule = mock(RaterParseRule.class);

        List<EmpStaff> orgEmps = new ArrayList<>();
        EmpStaff empStaff1 = new EmpStaff();
        empStaff1.setEmpId("emp1");
        empStaff1.setEmpName("Employee 1");
        empStaff1.setOrgId("org1");
        empStaff1.setOrgName("Org 1");

        EmpStaff empStaff2 = new EmpStaff();
        empStaff2.setEmpId("emp2");
        empStaff2.setEmpName("Employee 2");
        empStaff2.setOrgId("org2");
        empStaff2.setOrgName("Org 2");

        orgEmps.add(empStaff1);
        orgEmps.add(empStaff2);

        // 模拟行为
        when(deptFinder.listEmpByDeptId(tenantId, orgId)).thenReturn(orgEmps);

        // 执行测试
        List<Rater> result = raterRuleParserDmSvc.parseOrgEmp(parseRule, orgId);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        assertEquals("emp1", result.get(0).getEmpId());
        assertEquals("Employee 1", result.get(0).getEmpName());
        assertEquals("org1", result.get(0).getOrgId());
        assertEquals("Org 1", result.get(0).getOrgName());

        assertEquals("emp2", result.get(1).getEmpId());
        assertEquals("Employee 2", result.get(1).getEmpName());
        assertEquals("org2", result.get(1).getOrgId());
        assertEquals("Org 2", result.get(1).getOrgName());
    }

    @Test
    public void testParseEvalLeader_WithDirectLeader() {
        // 准备测试数据
        String fromDeptId = "dept123";
        String evalEmpId = "emp123";
        Integer level = 1;
        RaterParseRule parseRule = mock(RaterParseRule.class);

        Rater directLeader = new Rater("leader1", "Leader 1", "avatar1", "exUserId1");

        // 模拟行为
        when(empParser.getDirectLeader(evalEmpId)).thenReturn(directLeader);

        // 执行测试
        List<Rater> result = raterRuleParserDmSvc.parseEvalLeader(parseRule, fromDeptId, evalEmpId, level);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("leader1", result.get(0).getEmpId());
        assertEquals("Leader 1", result.get(0).getEmpName());
        assertEquals("avatar1", result.get(0).getAvatar());
        assertEquals("exUserId1", result.get(0).getDingUserId());
    }

    @Test
    public void testParseEvalLeader_WithoutDirectLeader() {
        // 准备测试数据
        String fromDeptId = "dept123";
        String evalEmpId = "emp123";
        Integer level = 1;
        RaterParseRule parseRule = mock(RaterParseRule.class);

        // 模拟行为
        when(empParser.getDirectLeader(evalEmpId)).thenReturn(null);

        DeptLeader deptLeader = new DeptLeader();
        List<Leader> leaders = new ArrayList<>();
        Leader leader = new Leader();
        leader.setLeaderId("leader1");
        leader.setLeaderName("Leader 1");
        leader.setLeaderAvatar("avatar1");
        leader.setExtLeaderUserId("exUserId1");
        leader.setLeaderType(1);
        leaders.add(leader);
        deptLeader.setLeaders(leaders);

        when(leaderParser.getEqLevel(fromDeptId, level,"10001")).thenReturn(deptLeader);

        // 执行测试
        List<Rater> result = raterRuleParserDmSvc.parseEvalLeader(parseRule, fromDeptId, evalEmpId, level);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("leader1", result.get(0).getEmpId());
        assertEquals("Leader 1", result.get(0).getEmpName());
        assertEquals("avatar1", result.get(0).getAvatar());
        assertEquals("exUserId1", result.get(0).getDingUserId());
        assertEquals(Integer.valueOf(1), Integer.valueOf(result.get(0).getType()));
    }

    @Test
    public void testParseDeptLeader_WithEqLevel() {
        // 准备测试数据
        String fromDeptId = "dept123";
        Integer level = 2;
        RaterParseRule parseRule = mock(RaterParseRule.class);

        DeptLeader deptLeader = new DeptLeader();
        List<Leader> leaders = new ArrayList<>();
        Leader leader = new Leader();
        leader.setLeaderId("leader1");
        leader.setLeaderName("Leader 1");
        leader.setLeaderAvatar("avatar1");
        leader.setExtLeaderUserId("exUserId1");
        leader.setLeaderType(1);
        leaders.add(leader);
        deptLeader.setLeaders(leaders);

        // 模拟行为
        when(leaderParser.getEqLevel(fromDeptId, level,"10001")).thenReturn(deptLeader);

        // 执行测试
        List<Rater> result = raterRuleParserDmSvc.parseDeptLeader(parseRule, fromDeptId, level,"10001");

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("leader1", result.get(0).getEmpId());
        assertEquals("Leader 1", result.get(0).getEmpName());
        assertEquals("avatar1", result.get(0).getAvatar());
        assertEquals("exUserId1", result.get(0).getDingUserId());
        assertEquals(Integer.valueOf(1), Integer.valueOf(result.get(0).getType()));
    }

    @Test
    public void testParseDeptLeader_WithoutEqLevel_VacancyToOther() {
        // 准备测试数据
        String fromDeptId = "dept123";
        Integer level = 2;
        RaterParseRule parseRule = mock(RaterParseRule.class);
        EmpId empId = mock(EmpId.class);

        // 模拟行为
        when(leaderParser.getEqLevel(fromDeptId, level,empId.getId())).thenReturn(null);
        when(parseRule.vacancyToOther(auditSetting)).thenReturn(true);
        when(parseRule.getOther()).thenReturn(empId);
        when(empId.getId()).thenReturn("otherEmpId");

        KpiEmp otherEmp = new KpiEmp();
        otherEmp.setEmpId("otherEmpId");
        otherEmp.setEmpName("Other Employee");
        otherEmp.setAvatar("otherAvatar");
        otherEmp.setExUserId("otherExUserId");

        when(empParser.getEmp("otherEmpId")).thenReturn(otherEmp);

        // 执行测试
        List<Rater> result = raterRuleParserDmSvc.parseDeptLeader(parseRule, fromDeptId, level,"10001");

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("otherEmpId", result.get(0).getEmpId());
        assertEquals("Other Employee", result.get(0).getEmpName());
        assertEquals("otherAvatar", result.get(0).getAvatar());
        assertEquals("otherExUserId", result.get(0).getDingUserId());
    }

    @Test
    public void testParseDeptLeader_WithoutEqLevel_OnNullToUp() {
        // 准备测试数据
        String fromDeptId = "dept123";
        Integer level = 2;
        RaterParseRule parseRule = mock(RaterParseRule.class);

        // 模拟行为
        when(leaderParser.getEqLevel(fromDeptId, level,"10001")).thenReturn(null);
        when(parseRule.vacancyToOther(auditSetting)).thenReturn(false);
        when(auditSetting.nodeEmpVacancySkip()).thenReturn(false);
        when(auditSetting.nodeEmpVacancyTipsError()).thenReturn(false);
        when(auditSetting.openSupAdminAudit()).thenReturn(true);

        List<Leader> onNullToUpLeaders = new ArrayList<>();
        Leader leader = new Leader();
        leader.setLeaderId("leader2");
        leader.setLeaderName("Leader 2");
        leader.setLeaderAvatar("avatar2");
        leader.setExtLeaderUserId("exUserId2");
        leader.setLeaderType(1);
        onNullToUpLeaders.add(leader);

        when(leaderParser.getOnNullToUp(fromDeptId, level,"10001")).thenReturn(onNullToUpLeaders);

        // 执行测试
        List<Rater> result = raterRuleParserDmSvc.parseDeptLeader(parseRule, fromDeptId, level,"10001");

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("leader2", result.get(0).getEmpId());
        assertEquals("Leader 2", result.get(0).getEmpName());
        assertEquals("avatar2", result.get(0).getAvatar());
        assertEquals("exUserId2", result.get(0).getDingUserId());
        assertEquals(Integer.valueOf(1), Integer.valueOf(result.get(0).getType()));
    }

    @Test
    public void testParseRoleMember_WithRoleMembers() {
        // 准备测试数据
        String roleId = "role123";
        RaterParseRule parseRule = mock(RaterParseRule.class);

        KpiRole role = new KpiRole();
        List<KpiRole.RoleMember> roleMembers = new ArrayList<>();
        KpiRole.RoleMember member1 = new KpiRole.RoleMember();
        member1.setEmpId("emp1");
        KpiRole.RoleMember member2 = new KpiRole.RoleMember();
        member2.setEmpId("emp2");
        roleMembers.add(member1);
        roleMembers.add(member2);
        role.setRoleMembers(roleMembers);

        KpiEmp emp1 = new KpiEmp();
        emp1.setEmpId("emp1");
        emp1.setEmpName("Employee 1");
        emp1.setAvatar("avatar1");
        emp1.setExUserId("exUserId1");

        KpiEmp emp2 = new KpiEmp();
        emp2.setEmpId("emp2");
        emp2.setEmpName("Employee 2");
        emp2.setAvatar("avatar2");
        emp2.setExUserId("exUserId2");

        // 模拟行为
        when(roleParser.getRole(roleId)).thenReturn(role);
        when(empParser.getEmps(Arrays.asList("emp1", "emp2"))).thenReturn(Arrays.asList(emp1, emp2));

        // 执行测试
        List<Rater> result = raterRuleParserDmSvc.parseRoleMember(parseRule, roleId);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        assertEquals("emp1", result.get(0).getEmpId());
        assertEquals("Employee 1", result.get(0).getEmpName());
        assertEquals("avatar1", result.get(0).getAvatar());
        assertEquals("exUserId1", result.get(0).getDingUserId());
        assertEquals(Integer.valueOf(8), Integer.valueOf(result.get(0).getType()));

        assertEquals("emp2", result.get(1).getEmpId());
        assertEquals("Employee 2", result.get(1).getEmpName());
        assertEquals("avatar2", result.get(1).getAvatar());
        assertEquals("exUserId2", result.get(1).getDingUserId());
        assertEquals(Integer.valueOf(8), Integer.valueOf(result.get(1).getType()));
    }

    @Test
    public void testParseRoleMember_WithoutRoleMembers() {
        // 准备测试数据
        String roleId = "role123";
        RaterParseRule parseRule = mock(RaterParseRule.class);
        EmpId empId = mock(EmpId.class);

        // 模拟行为
        when(roleParser.getRole(roleId)).thenReturn(null);
        when(parseRule.vacancyToOther(auditSetting)).thenReturn(true);
        when(parseRule.getOther()).thenReturn(empId);
        when(empId.getId()).thenReturn("otherEmpId");

        KpiEmp otherEmp = new KpiEmp();
        otherEmp.setEmpId("otherEmpId");
        otherEmp.setEmpName("Other Employee");
        otherEmp.setAvatar("otherAvatar");
        otherEmp.setExUserId("otherExUserId");

        when(empParser.getEmp("otherEmpId")).thenReturn(otherEmp);

        // 执行测试
        List<Rater> result = raterRuleParserDmSvc.parseRoleMember(parseRule, roleId);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("otherEmpId", result.get(0).getEmpId());
        assertEquals("Other Employee", result.get(0).getEmpName());
        assertEquals("otherAvatar", result.get(0).getAvatar());
        assertEquals("otherExUserId", result.get(0).getDingUserId());
    }

    @Test
    public void testMatchRoleScope_WithMatchingRoleMembers() {
        // 准备测试数据
        String roleId = "role123";
        String matchDeptId = "dept123";
        String matchEmpId = "emp123";
        RaterParseRule parseRule = mock(RaterParseRule.class);

        KpiDeptCode orgCode = new KpiDeptCode();
        orgCode.setOrgCode("orgCode123");

        KpiRole role = new KpiRole();
        List<KpiRole.RoleMember> roleMembers = new ArrayList<>();
        KpiRole.RoleMember member1 = new KpiRole.RoleMember();
        member1.setEmpId("emp1");
        KpiRole.RoleMember member2 = new KpiRole.RoleMember();
        member2.setEmpId("emp2");
        roleMembers.add(member1);
        roleMembers.add(member2);
        role.setRoleMembers(roleMembers);

        KpiEmp emp1 = new KpiEmp();
        emp1.setEmpId("emp1");
        emp1.setEmpName("Employee 1");
        emp1.setAvatar("avatar1");
        emp1.setExUserId("exUserId1");

        // 模拟行为
        when(deptFinder.getOrgCode(tenantId, matchDeptId)).thenReturn(orgCode);
        when(roleParser.getRole(roleId)).thenReturn(role);
        when(member1.matchScope("orgCode123", matchEmpId)).thenReturn(true);
        when(member2.matchScope("orgCode123", matchEmpId)).thenReturn(false);
        when(empParser.getEmps(Collections.singletonList("emp1"))).thenReturn(Collections.singletonList(emp1));

        // 执行测试
        List<Rater> result = raterRuleParserDmSvc.matchRoleScope(parseRule, roleId, matchDeptId, matchEmpId);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("emp1", result.get(0).getEmpId());
        assertEquals("Employee 1", result.get(0).getEmpName());
        assertEquals("avatar1", result.get(0).getAvatar());
        assertEquals("exUserId1", result.get(0).getDingUserId());
        assertEquals(Integer.valueOf(8), Integer.valueOf(result.get(0).getType()));
    }

    @Test
    public void testMatchRoleScope_WithoutMatchingRoleMembers() {
        // 准备测试数据
        String roleId = "role123";
        String matchDeptId = "dept123";
        String matchEmpId = "emp123";
        RaterParseRule parseRule = mock(RaterParseRule.class);
        EmpId empId = mock(EmpId.class);

        KpiDeptCode orgCode = new KpiDeptCode();
        orgCode.setOrgCode("orgCode123");

        KpiRole role = new KpiRole();
        List<KpiRole.RoleMember> roleMembers = new ArrayList<>();
        KpiRole.RoleMember member1 = new KpiRole.RoleMember();
        member1.setEmpId("emp1");
        KpiRole.RoleMember member2 = new KpiRole.RoleMember();
        member2.setEmpId("emp2");
        roleMembers.add(member1);
        roleMembers.add(member2);
        role.setRoleMembers(roleMembers);

        // 模拟行为
        when(deptFinder.getOrgCode(tenantId, matchDeptId)).thenReturn(orgCode);
        when(roleParser.getRole(roleId)).thenReturn(role);
        when(member1.matchScope("orgCode123", matchEmpId)).thenReturn(false);
        when(member2.matchScope("orgCode123", matchEmpId)).thenReturn(false);
        when(parseRule.vacancyToOther(auditSetting)).thenReturn(true);
        when(parseRule.getOther()).thenReturn(empId);
        when(empId.getId()).thenReturn("otherEmpId");

        KpiEmp otherEmp = new KpiEmp();
        otherEmp.setEmpId("otherEmpId");
        otherEmp.setEmpName("Other Employee");
        otherEmp.setAvatar("otherAvatar");
        otherEmp.setExUserId("otherExUserId");

        when(empParser.getEmp("otherEmpId")).thenReturn(otherEmp);

        // 执行测试
        List<Rater> result = raterRuleParserDmSvc.matchRoleScope(parseRule, roleId, matchDeptId, matchEmpId);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("otherEmpId", result.get(0).getEmpId());
        assertEquals("Other Employee", result.get(0).getEmpName());
        assertEquals("otherAvatar", result.get(0).getAvatar());
        assertEquals("otherExUserId", result.get(0).getDingUserId());
    }
}