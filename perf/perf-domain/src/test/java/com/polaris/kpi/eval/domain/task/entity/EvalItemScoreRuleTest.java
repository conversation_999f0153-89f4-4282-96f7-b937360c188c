package com.polaris.kpi.eval.domain.task.entity;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.MutualNodeConf;
import org.junit.Test;

import static org.junit.Assert.*;

public class EvalItemScoreRuleTest {

    @Test
    public void name() {
        EvalItemScoreRule rule = new EvalItemScoreRule();
        JSONObject jsonObject = JSONUtil.parseObj("{\"appointer\":{\"raters\":[{\"empId\":\"9f37b15e-59f0-474c-8bb0-f6fecb74b3a9\",\"empName\":\"冯博\",\"type\":0}],\"type\":\"manager\"},\"multiType\":\"and\",\"node\":\"peer_score\",\"nodeWeight\":40.00,\"open\":1,\"rateMode\":\"item\",\"raters\":[],\"transferFlag\":\"false\",\"vacancyRater\":\"\",\"vacancyType\":\"superior\"}");
        MutualNodeConf mutualNodeConf = jsonObject.toBean(MutualNodeConf.class);
        rule.setPeerRater(mutualNodeConf);
        boolean rs = rule.needSetPeerRater("32557265-d859-4ddd-bef6-d183e3e707d4", "9f37b15e-59f0-474c-8bb0-f6fecb74b3a9","add");
        System.out.println(rs);
    }
}