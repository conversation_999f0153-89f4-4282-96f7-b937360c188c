package com.polaris.kpi.eval.domain.task.dmsvc;

import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.entity.flow.LevelAuditFlow;
import com.polaris.kpi.setting.domain.entity.ResultAuditFlow;
import com.polaris.sdk.common.JsonFileTool;
import org.junit.Assert;
import org.junit.Test;

import java.util.Objects;

import static org.junit.Assert.*;

public class SkipAuditNodeDmSvcTest {

    public static String companyId = "5a031297-1b38-48ae-bc82-375849835203";

    public static String taskUserId = "1283501";

    @Test
    public void startSkip() {
        LevelAuditFlow flow = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.dmsvc/TaskUserRepoImpl.loadAuditFlow.json",LevelAuditFlow.class);
        SkipAuditNodeDmSvc nodeDmSvc = new SkipAuditNodeDmSvc(companyId,taskUserId,flow, EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT.getScene());
        LevelAuditFlow auditFlow = nodeDmSvc.startSkipTest(2);
        Assert.assertTrue(Objects.equals(JSONUtil.toJsonStr(auditFlow.nextAudits()),"[{\"empId\":\"1024060\",\"transferFlag\":\"true\",\"multipleReviewersType\":\"or\",\"subResultSize\":0,\"subNodes\":[],\"taskUserId\":\"1283501\",\"orgId\":\"\",\"scene\":\"modify_item_audit\",\"isDeleted\":\"false\",\"createdTime\":1726036896000,\"id\":\"1979710\",\"approverType\":\"user\",\"createdUser\":\"1009187\",\"updatedTime\":1726123272000,\"approvalOrder\":2,\"vacancyApproverInfo\":\"1009187\",\"mergeWeightDel\":0,\"finished\":false,\"modifyFlag\":\"true\",\"updatedUser\":\"1009187\",\"version\":0,\"approverInfo\":\"1024060\",\"companyId\":{\"id\":\"5a031297-1b38-48ae-bc82-375849835203\"},\"vacancyApproverType\":\"admin\",\"waitSubNodes\":[],\"hasExcludeAllManager\":false,\"taskId\":\"1637201\"}]"));
    }
}