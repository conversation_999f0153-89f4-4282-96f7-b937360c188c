package com.polaris.kpi.org.domain.dept.dmsvc;

import cn.com.polaris.kpi.KpiEmp;
import com.polaris.kpi.org.domain.dept.repo.EmpFinder;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

public class EmpParserTest {

    @Test
    public void getEmp() {
        List<KpiEmp> kpiEmps = new ArrayList<>();
        kpiEmps.add(new KpiEmp("1", "name"));
        EmpFinder empFinder = (tenantId, empIds) -> kpiEmps;

        EmpParser empParser = new EmpParser(empFinder, null);
        KpiEmp emp = empParser.getEmp("1");
        Assert.assertTrue(emp.getEmpName().equals("name"));
        KpiEmp emp2 = empParser.getEmp("2");
        Assert.assertTrue(emp2 == null);
    }

    @Test
    public void getEmps() {

        List<KpiEmp> kpiEmps = new ArrayList<>();
        kpiEmps.add(new KpiEmp("1", "name"));
        EmpFinder empFinder = (tenantId, empIds) -> kpiEmps;

        EmpParser empParser = new EmpParser(empFinder, null);
        KpiEmp emp = empParser.getEmp("1");
        Assert.assertTrue(emp.getEmpName().equals("name"));
    }
}