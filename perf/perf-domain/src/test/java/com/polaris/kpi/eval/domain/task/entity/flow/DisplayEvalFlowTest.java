package com.polaris.kpi.eval.domain.task.entity.flow;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.eval.InterviewStatusEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.polaris.kpi.eval.domain.task.entity.interview.EvalTaskInterview;
import com.polaris.sdk.type.AuditEnum;
import org.joda.time.DateTime;
import org.junit.Test;

import java.util.*;
import java.util.stream.Collectors;

public class DisplayEvalFlowTest {

    @Test
    public void sort() {
        List<DisplayEvalFlow> flows = new ArrayList<>();
        DisplayEvalFlow wait = new DisplayEvalFlow();
        wait.setScene(AuditEnum.SELF_SCORE.getScene());
        wait.setStatus("wait");
        flows.add(wait);

        DisplayEvalFlow doing = new DisplayEvalFlow();
        doing.setStartTime(new DateTime().plusDays(-5).toDate());
        doing.setStatus("doing");
        doing.setScene(AuditEnum.PEER_SCORE.getScene());
        flows.add(doing);


        DisplayEvalFlow doing2 = new DisplayEvalFlow();
        doing2.setStartTime(new DateTime().plusDays(-4).toDate());
        doing2.setStatus("doing");
        doing2.setScene(AuditEnum.PEER_SCORE.getScene());
        flows.add(doing2);


        DisplayEvalFlow finished = new DisplayEvalFlow();
        finished.setStartTime(new DateTime().plusDays(-10).toDate());
        finished.setScene(AuditEnum.SUPERIOR_SCORE.getScene());
        finished.setStatus("finished");
        flows.add(finished);


        DisplayEvalFlow finished2 = new DisplayEvalFlow();
        finished2.setStartTime(new DateTime().plusDays(-9).toDate());
        finished2.setStatus("finished");
        finished2.setScene(AuditEnum.SUPERIOR_SCORE.getScene());
        flows.add(finished2);
        List<DisplayEvalFlow> collect = flows.stream().sorted((a, b) -> {
            if (a.dispatchOrder() != b.dispatchOrder()) {
                return a.dispatchOrder() - b.dispatchOrder();
            }
            if (a.timeOrder() != b.timeOrder()) {
                long l = a.timeOrder() - b.timeOrder();
                return Integer.valueOf(l + "");
            }
            return a.sceneOrder() - b.sceneOrder();
        }).collect(Collectors.toList());
        for (DisplayEvalFlow flow : collect) {
            String time = flow.getStartTime() == null ? null : flow.getStartTime().toLocaleString();
            System.out.println(time + ":" + flow.getStatus());
        }
    }

    @Test
    public void sort2() {
        String str ="[{\"executeOrderList\":[{\"executorList\":[{\"approvalOrder\":1,\"auditStatus\":\"finished\",\"avatar\":\"https://static-legacy.dingtalk.com/media/lQDPDhs8hjOHypfNAvvNAm2wNrB_t36oonMCM9YJSUCJAA_621_763.jpg\",\"empId\":\"1093001\",\"empName\":\"静雯\",\"empStatus\":\"on_the_job\",\"finish\":true,\"finishTime\":1671021140000}],\"order\":0,\"reviewType\":\"and\"}],\"scene\":\"self_score\",\"startTime\":1671020902000,\"status\":\"finished\"},{\"executeOrderList\":[{\"executorList\":[{\"approvalOrder\":1,\"auditStatus\":\"finished\",\"avatar\":\"https://static-legacy.dingtalk.com/media/lADPDhYBP7KmaXrMoMyg_160_160.jpg\",\"empId\":\"1093003\",\"empName\":\"Mr. wei\",\"empStatus\":\"on_the_job\",\"finish\":true,\"finishTime\":1671021328000}],\"order\":1,\"reviewType\":\"and\"}],\"scene\":\"peer_score\",\"startTime\":1671021207000,\"status\":\"finished\"},{\"executeOrderList\":[{\"executorList\":[{\"approvalOrder\":1,\"auditStatus\":\"finished\",\"avatar\":\"\",\"empId\":\"1093014\",\"empName\":\"思威\",\"empStatus\":\"on_the_job\",\"finish\":true,\"finishTime\":1671021207000}],\"order\":1,\"reviewType\":\"and\"}],\"scene\":\"sub_score\",\"startTime\":1671021140000,\"status\":\"finished\"},{\"executeOrderList\":[{\"executorList\":[{\"approvalOrder\":1,\"auditStatus\":\"finished\",\"avatar\":\"\",\"empId\":\"1090069\",\"empName\":\"Sasha\",\"empStatus\":\"on_the_job\",\"finish\":true,\"finishTime\":1671021433000}],\"order\":1,\"reviewType\":\"or\"},{\"executorList\":[{\"approvalOrder\":2,\"auditStatus\":\"finished\",\"avatar\":\"https://static-legacy.dingtalk.com/media/lADPDg7mQqMlUJPNArzNArw_700_700.jpg\",\"empId\":\"1090071\",\"empName\":\"杨浩\",\"empStatus\":\"on_the_job\",\"finish\":true,\"finishTime\":1671021467000}],\"order\":2,\"reviewType\":\"or\"},{\"executorList\":[{\"approvalOrder\":3,\"auditStatus\":\"finished\",\"avatar\":\"\",\"empId\":\"1090038\",\"empName\":\"路飞\",\"empStatus\":\"on_the_job\",\"finish\":true,\"finishTime\":1671021568000}],\"order\":3,\"reviewType\":\"or\"}],\"scene\":\"superior_score\",\"startTime\":1671021328000,\"status\":\"finished\"}]";
        List<DisplayEvalFlow> flows = JSONObject.parseArray(str,DisplayEvalFlow.class);
        List<DisplayEvalFlow> collect = flows.stream().sorted((a, b) -> {
            if (a.dispatchOrder() != b.dispatchOrder()) {
                return a.dispatchOrder() - b.dispatchOrder();
            }
            if (a.timeOrder() != b.timeOrder()) {
                long l = a.timeOrder() - b.timeOrder();
                return Integer.valueOf(l + "");
            }
            return a.sceneOrder() - b.sceneOrder();
        }).collect(Collectors.toList());
        for (DisplayEvalFlow flow : collect) {
            String time = flow.getStartTime() == null ? null : flow.getStartTime().toLocaleString();
            System.out.println(time + ":" + flow.getStatus());
        }
    }

    @Test
    public void constructorBean() {
        List<DisplayEvalFlow> flows = new ArrayList<>();
        List<String> interviewExcuteEmpIds = Arrays.asList("1024060", "1024061");
        EvalTaskInterview interviewPo = new EvalTaskInterview();
        interviewPo.setExcuterId("1024061");
        List<String> notFinishExcuter = Objects.nonNull(interviewPo) && StrUtil.isNotBlank(interviewPo.getExcuterId()) ?
                new ArrayList<>() : new ArrayList<>(interviewExcuteEmpIds);
        Map<String, KpiEmp> ratersMap = new HashMap<>();
        ratersMap.put("1024060", new KpiEmp("1024060", "wuwu"));
        ratersMap.put("1024061", new KpiEmp("1024061", "zx"));
        DisplayEvalFlow auditFlow = new DisplayEvalFlow(AuditEnum.FINAL_RESULT_INTERVIEW.getScene(),
                Objects.nonNull(interviewPo) && StrUtil.isNotBlank(interviewPo.getExcuterId()) ?
                        "finished" : "wait", "or", 1, ratersMap, interviewExcuteEmpIds, notFinishExcuter);
        flows.add(auditFlow);

        System.out.println(JSONUtil.toJsonStr(auditFlow));
        interviewPo.setRecorderId("1024060");
        if (Objects.nonNull(interviewPo) && StrUtil.isNotBlank(interviewPo.getRecorderId())) {
            Map<String, KpiEmp> ratersInputMap = new HashMap<>();
            ratersInputMap.put("1024060", new KpiEmp("1024060", "wuwu"));

            List<String> inputEmpIdNotFinishs = Arrays.asList();

            List<String> inputEmpId = Collections.singletonList(interviewPo.getRecorderId());
            DisplayEvalFlow auditFlowInput = new DisplayEvalFlow(AuditEnum.FINAL_RESULT_INTERVIEW.getScene(),
                    CollUtil.isEmpty(inputEmpIdNotFinishs) ? "finished" : "wait", "or", 2, ratersInputMap,
                    inputEmpId, inputEmpIdNotFinishs);
            flows.add(auditFlowInput);
        }


        //确认人
        List<String> interviewConfirmEmpIds = Arrays.asList("1024060");
        System.out.println(JSONUtil.toJsonStr(flows));

        List<String> notConfirmEmpIds = Arrays.asList("1024060");
        Map<String, KpiEmp> ratersMap2 = new HashMap<>();
        ratersMap2.put("1024060", new KpiEmp("1024060", "wuwu"));
        ratersMap2.put("1024061", new KpiEmp("1024061", "zx"));
        DisplayEvalFlow auditFlow2 = new DisplayEvalFlow(AuditEnum.FINAL_RESULT_INTERVIEW_CONFIRM.getScene(),
                Objects.nonNull(interviewPo) && InterviewStatusEnum.INTERVIEW_FINISH.getType().equals(interviewPo.getInterviewStatus()) ?
                        "finished" : "wait", "and", 3, ratersMap, interviewConfirmEmpIds, notConfirmEmpIds);
        flows.add(auditFlow2);

        System.out.println(JSONUtil.toJsonStr(flows));

    }
}