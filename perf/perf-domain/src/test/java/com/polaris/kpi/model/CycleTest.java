package com.polaris.kpi.model;

import org.junit.Test;

/**
 * <AUTHOR> lufei
 * @date 2022/3/2 6:55 下午
 */
public class CycleTest {

    @Test
    public void getCycleName() {
        /*Cycle cycle = new Cycle(new TenantId("1"), new CycleValue(2022, CycleTypeEnum.MONTH.getType(), 10));
        System.out.println(cycle.getCycleName());
        Assert.assertTrue(cycle.getCycleName().equals("2022年10月度考核周期"));

        cycle = new Cycle(new TenantId("1"), new CycleValue(2022, CycleTypeEnum.QUARTER.getType(), 3));
        System.out.println(cycle.getCycleName());
        Assert.assertTrue(cycle.getCycleName().equals("2022年3季度考核周期"));

        cycle = new Cycle(new TenantId("1"), new CycleValue(2022, CycleTypeEnum.HALF_YEAR.getType(), 1));
        System.out.println(cycle.getCycleName());
        Assert.assertTrue(cycle.getCycleName().equals("2022年上半年考核周期"));

        cycle = new Cycle(new TenantId("1"), new CycleValue(2022, CycleTypeEnum.HALF_YEAR.getType(), 2));
        System.out.println(cycle.getCycleName());
        Assert.assertTrue(cycle.getCycleName().equals("2022年下半年考核周期"));
    }


    @Test
    public void getCycleQueryId() {
        Cycle cycle = new Cycle(new TenantId("1"), new CycleValue(2022, CycleTypeEnum.MONTH.getType(), 10));
        System.out.println(cycle.queryId());
        // Assert.assertTrue(cycle.getCycleName().equals("2022年10月度考核周期"));
        cycle = new Cycle(new TenantId("1"), new CycleValue(2022, CycleTypeEnum.CROSS_MONTH.getType(), 10));
        System.out.println(cycle.queryId());

        cycle = new Cycle(new TenantId("1"), new CycleValue(2022, CycleTypeEnum.QUARTER.getType(), 3));
        System.out.println(cycle.queryId());
        // Assert.assertTrue(cycle.getCycleName().equals("2022年3季度考核周期"));

        cycle = new Cycle(new TenantId("1"), new CycleValue(2022, CycleTypeEnum.HALF_YEAR.getType(), 1));
        System.out.println(cycle.queryId());

        cycle = new Cycle(new TenantId("1"), new CycleValue(2022, CycleTypeEnum.HALF_YEAR.getType(), 2));
        System.out.println(cycle.queryId());
    }*/
    }
}