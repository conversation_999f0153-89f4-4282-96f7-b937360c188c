package com.polaris.kpi.eval.domain.task.entity;

import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.InterviewConf;
import com.polaris.sdk.common.JsonFileTool;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;

public class InterviewConfTest {

    @Test
    @DisplayName("勾选：指定主管")
    public void fixUpInterviewConfirmConfTest10() {
        InterviewConf conf = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.dmsvc/listInterviewConfirmFlowNodeManger.json"
                , InterviewConf.class);
        conf.fixUpInterviewConfirmConf();
        System.out.println(JSONUtil.toJsonStr(conf));
    }
    @Test
    @DisplayName("勾选：指定角色多个")
    public void fixUpInterviewConfirmConfTest11() {
        InterviewConf conf = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.dmsvc/listInterviewConfirmFlowNodeRoles2.json"
                , InterviewConf.class);
        conf.fixUpInterviewConfirmConf();
        System.out.println(JSONUtil.toJsonStr(conf));
    }
    @Test
    @DisplayName("勾选：被考核人，指定角色多个")
    public void fixUpInterviewConfirmConfTest1() {
        InterviewConf conf = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.dmsvc/listInterviewConfirmFlowNodeRoles.json"
                , InterviewConf.class);
        conf.fixUpInterviewConfirmConf();
        System.out.println(JSONUtil.toJsonStr(conf));
    }

    @Test
    @DisplayName("勾选：被考核人，指定员工多个")
    public void fixUpInterviewConfirmConfTest2() {
        InterviewConf conf = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.dmsvc/listInterviewConfirmFlowNodeMutuilUsers.json"
                , InterviewConf.class);
        conf.fixUpInterviewConfirmConf();
        System.out.println(JSONUtil.toJsonStr(conf));
    }

    @Test
    @DisplayName("所有都勾选：被考核人，直属主管，角色，指定员工")
    public void fixUpInterviewConfirmConfTest3() {
        InterviewConf conf = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.dmsvc/listInterviewConfirmFlowNodeAllType.json"
                , InterviewConf.class);
        conf.fixUpInterviewConfirmConf();
        System.out.println(JSONUtil.toJsonStr(conf));
    }

    @Test
    @DisplayName("勾选：被考核人，指定角色单个")
    public void fixUpInterviewConfirmConfTest4() {
        InterviewConf conf = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.dmsvc/listInterviewConfirmFlowNodeOneRoles.json"
                , InterviewConf.class);
        conf.fixUpInterviewConfirmConf();
        System.out.println(JSONUtil.toJsonStr(conf));
    }

    @Test
    @DisplayName("rule：被考核人，指定角色单个")
    public void RuleInterviewConfirmFlowNodeTest1() {
        InterviewConf conf = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.dmsvc/RuleInterviewConfirmFlowNode.json"
                , InterviewConf.class);
        conf.fixUpInterviewConfirmConf();
        System.out.println(JSONUtil.toJsonStr(conf));
    }

    //{"auditSetting":{"empRepeatSkip":0,"nodeEmpVacancy":2,"notFindAdminBySupAdmin":0},"empRepeatSkip":0,"interviewConfirmConf":{"interviewConfirmInfo":[{"approverInfo":"","approverName":"被考核人","approverType":"taskEmp","raters":[{"empId":"1302001","empName":"苏小秋","status":"wait","type":0}],"roleAudit":false},{"approverInfo":"1","approverName":"直属主管","approverType":"manager","raters":[{"empId":"1602582","empName":"Sasha","level":2,"status":"wait","type":1}],"roleAudit":false},{"approverInfo":"c8ae1eec-8c5e-466a-a667-d9520468fb00,2720bc73-3c74-4440-8f08-1a0569e422cd,11cd5a5f-4461-47d3-b16f-08582b43ec82","approverName":"测试默认角色,出纳,质检","approverType":"role","raters":[],"roleAudit":true},{"approverInfo":"1069028,1602582","approverName":"苏华（客户成功）,Sasha","approverType":"user","raters":[{"empId":"1069028,1602582","empName":"苏华（客户成功）,Sasha","status":"wait","type":0}],"roleAudit":false}],"isSign":0,"open":1},"interviewEmpConf":{"conditions":[],"type":1},"interviewExcutorInfo":{"approverInfo":"1302001","approverName":"苏小秋","approverType":"user","raters":[{"empId":"1302001","empName":"苏小秋","status":"wait","type":0}],"roleAudit":false},"nodeEmpVacancy":2,"notFindAdminBySupAdmin":0,"open":1,"templId":"9839b271-1cca-11ef-a997-000c29165335","templName":"汉堡法则","transferFlag":"false"}
}
