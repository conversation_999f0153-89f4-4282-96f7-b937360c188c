package com.polaris.kpi.eval.domain.task.type;

import org.junit.Assert;
import org.junit.Test;

public class TalentStatusTest {

    @Test
    public void match() {
        Assert.assertFalse(TalentStatus.ON_LEVEL.matchSub(null, null));
        Assert.assertFalse(TalentStatus.ON_LEVEL.matchSub(TalentStatus.SCORING.getStatus(), null));
        Assert.assertFalse(TalentStatus.ON_LEVEL.matchSub(TalentStatus.SCORING.getStatus(), 1));
        Assert.assertFalse(TalentStatus.ON_LEVEL.matchSub(TalentStatus.SCORING.getStatus(), 3));
        Assert.assertTrue(TalentStatus.ON_LEVEL.matchSub(TalentStatus.SCORING.getStatus(), 2));
    }
}