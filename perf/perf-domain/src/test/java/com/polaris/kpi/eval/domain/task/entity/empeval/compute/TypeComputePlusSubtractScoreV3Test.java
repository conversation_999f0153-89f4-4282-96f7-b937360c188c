package com.polaris.kpi.eval.domain.task.entity.empeval.compute;

import cn.com.polaris.kpi.eval.PlusSubInterval;
import com.polaris.kpi.eval.domain.task.entity.ComputedResultScore;
import com.polaris.kpi.eval.domain.task.entity.EvalKpi;
import com.polaris.kpi.eval.domain.task.entity.TotalFinalNodeScoreV3;
import com.polaris.kpi.eval.domain.task.entity.TypeFinalNodeScoreV3;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalKpiType;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.powermock.api.mockito.PowerMockito.mock;
import static org.powermock.api.mockito.PowerMockito.when;

public class TypeComputePlusSubtractScoreV3Test {
    private EmpEvalKpiType mockType;
    private EvalKpi mockItem1, mockItem2;
    private TypeFinalNodeScoreV3 typeNodeScore;
    private TotalFinalNodeScoreV3 totalOfSum;
    private TypeComputeForPlusSubtractScoreV3 strategy;

    @BeforeEach
    void setUp() {
        // 初始化被测对象
        mockType = mock(EmpEvalKpiType.class);
        mockItem1 = mock(EvalKpi.class);
        mockItem2 = mock(EvalKpi.class);

        // 设置类型返回 items
        when(mockType.getItems()).thenReturn(Arrays.asList(mockItem1, mockItem2));
        when(mockType.isPlusSubType()).thenReturn(true);

        // 设置 interval
        PlusSubInterval interval = new PlusSubInterval("50","-30");
        when(mockType.getPlusSubInterval()).thenReturn(interval);

        // 初始化 nodeScore 和 totalOfSum
        typeNodeScore = new TypeFinalNodeScoreV3();
        totalOfSum = new TotalFinalNodeScoreV3();

        // ✅ 实例化 strategy 并调用 init 方法
        strategy = new TypeComputeForPlusSubtractScoreV3();
        strategy.init(typeNodeScore, mockType, false);

        // 初始化策略并注入依赖
        // 手动设置 typePlusSum 初始值
        try {
            Field typePlusSumField = TypeComputeForPlusSubtractScoreV3.class.getDeclaredField("typePlusSum");
            typePlusSumField.setAccessible(true);
            typePlusSumField.set(strategy, BigDecimal.ZERO);

            Field noWeightTypePlusSumField = TypeComputeForPlusSubtractScoreV3.class.getDeclaredField("noWeightTypePlusSum");
            noWeightTypePlusSumField.setAccessible(true);
            noWeightTypePlusSumField.set(strategy, BigDecimal.ZERO);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    @Test
    public void testComputeV3PlusSubtract_withValidItems_shouldCalculateCorrectly() {
        // 模拟 ItemComputePlusScoreV3 返回加分
        ItemComputeForPlusScoreV3 plusMock = mock(ItemComputeForPlusScoreV3.class);
        when(plusMock.computeV3PlusS1()).thenReturn(new ComputedResultScore(BigDecimal.valueOf(10), BigDecimal.ZERO));

        ItemComputeForSubtractScoreV3 subtractMock = mock(ItemComputeForSubtractScoreV3.class);
        when(subtractMock.computeV3Subtract2()).thenReturn(new ComputedResultScore(BigDecimal.valueOf(-5), BigDecimal.ZERO));

        for (EvalKpi item : strategy.needCompute) {
            strategy.setTypePlusSum(strategy.getTypePlusSum().add(BigDecimal.valueOf(10))); // plus
            strategy.setTypePlusSum(strategy.getTypePlusSum().add(BigDecimal.valueOf(-5))); // subtract
        }

        strategy.compute(totalOfSum,true);

        Assert.assertEquals(BigDecimal.valueOf(10), strategy.getTypePlusSum());
        assertEquals(BigDecimal.valueOf(10), totalOfSum.getPlusSum());
    }

    @Test
    public void testComputeV3PlusSubtract_withNullInterval_shouldNotThrowException() {
        when(mockType.getPlusSubInterval()).thenReturn(null);

        strategy = new TypeComputeForPlusSubtractScoreV3();
        strategy.init(typeNodeScore, mockType, false);

        strategy.compute(totalOfSum,true); // 不应抛异常
        assertNull(strategy.getTypePlusSum()); // 因为没有 interval
    }
}
