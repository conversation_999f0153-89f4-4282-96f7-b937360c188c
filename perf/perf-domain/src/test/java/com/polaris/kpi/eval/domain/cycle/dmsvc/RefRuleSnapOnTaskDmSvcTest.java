package com.polaris.kpi.eval.domain.cycle.dmsvc;

import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.cycle.repo.SnapOnTaskRepo;
import com.polaris.kpi.eval.domain.cycle.type.RefRuleSnapOnTask;
import com.polaris.kpi.eval.domain.task.entity.grade.ScoreRule;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.internal.matchers.Any;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


public class RefRuleSnapOnTaskDmSvcTest {
    RefRuleSnapOnTaskDmSvc refRuleDmSvc = new RefRuleSnapOnTaskDmSvc();
    SnapOnTaskRepo onTaskRepo;

    @Before
    public void before() {
        onTaskRepo = Mockito.mock(SnapOnTaskRepo.class);
        List<RefRuleSnapOnTask> onTasks = new ArrayList<>();
        onTasks.add(new RefRuleSnapOnTask("t001", "1000001"));
        onTasks.add(new RefRuleSnapOnTask("t002", "1000001"));
        ListWrap<RefRuleSnapOnTask> map = new ListWrap<>(onTasks).asMap(refRuleSnapOnTask -> refRuleSnapOnTask.getTaskId());
        Mockito.when(onTaskRepo.listRefRuleSnapByTaskId(new TenantId("10001"), Arrays.asList("t001", "t002"))).thenReturn(map);
    }

    @Test
    public void accOp() {
        refRuleDmSvc.accOp("10001", "10001", "main");
        Assert.assertTrue(refRuleDmSvc.getCompanyId().getId().equals("10001"));
    }

    @Test
    public void replaceRefRuleSnapOnTask() {
        List<RefRuleSnapOnTask> onTasks = new ArrayList<>();
        onTasks.add(new RefRuleSnapOnTask("t001", "1000001"));
        onTasks.add(new RefRuleSnapOnTask("t002", "1000002"));
        refRuleDmSvc.setSubmitRefs(onTasks);

        refRuleDmSvc.accOp("10001", "10001", "main");
        Assert.assertTrue(refRuleDmSvc.getCompanyId().getId().equals("10001"));
        refRuleDmSvc.loadOldRefSnapRule(onTaskRepo);
        refRuleDmSvc.checkAllOnMatch();
        refRuleDmSvc.replaceRefRuleSnapOnTask();
        Assert.assertTrue(refRuleDmSvc.getReplaceRefs().size() == 1);
        RefRuleSnapOnTask onTask = refRuleDmSvc.getReplaceRefs().get(0);
        System.out.println(JSONUtil.toJsonStr(onTask));
        Assert.assertTrue("{\"snapId\":\"1000002\",\"taskId\":\"t002\"}".equals(JSONUtil.toJsonStr(onTask)));


    }

    @Test
    public void saveRefRuleSnapOnTask() {
        refRuleDmSvc.saveRefRuleSnapOnTask(onTaskRepo);
    }
}