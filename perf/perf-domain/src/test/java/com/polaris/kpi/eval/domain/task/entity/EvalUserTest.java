package com.polaris.kpi.eval.domain.task.entity;

import com.polaris.kpi.TestFileTool;
import com.polaris.sdk.common.JsonFileTool;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class EvalUserTest {

    @Test
    void removeEvalRuleError_WhenRuleConfErrorIsBlank_ShouldDoNothing() {
        EvalUser evalUser = new EvalUser();
        evalUser.setRuleConfError("");
        evalUser.removeEvalRuleError("101");
        assertEquals("", evalUser.getRuleConfError());
    }

    @Test
    void removeEvalRuleError_WhenErrorCodeExists_ShouldRemoveErrorCode() {
        EvalUser evalUser = new EvalUser();
        evalUser.setRuleConfError("101,102,103");
        evalUser.removeEvalRuleError("102");
        assertEquals("101,103", evalUser.getRuleConfError());
    }

    @Test
    void removeEvalRuleError_WhenErrorCodeDoesNotExist_ShouldNotModifyRuleConfError() {
        EvalUser evalUser = new EvalUser();
        evalUser.setRuleConfError("101,102,103");
        evalUser.removeEvalRuleError("104");
        assertEquals("101,102,103", evalUser.getRuleConfError());
    }



    @Test
    void removeEvalRuleError_WhenRuleConfErrorIsNull_ShouldDoNothing() {
        EvalUser evalUser = new EvalUser();
        evalUser.setRuleConfError(null);
        evalUser.removeEvalRuleError("101");
        assertNull(evalUser.getRuleConfError());
    }


    @Test
    void noCanAutoEnterScore() {
        EvalUser evalUser  = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.entity.empeval/EvalUserTest/EvalUserNoAutoEnterScore5.json", EvalUser.class);
        System.out.println(evalUser.noCanAutoEnterScore("2025-10-31"));
//        assertNull(evalUser.getRuleConfError());
    }
}