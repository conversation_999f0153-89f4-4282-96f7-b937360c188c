package com.polaris.kpi.eval.domain.task.dmsvc;

import cn.com.polaris.kpi.eval.FinishValue;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.polaris.kpi.eval.domain.task.entity.EvalKpi;
import com.polaris.kpi.eval.domain.task.entity.EvalKpiInputValue;
import com.polaris.kpi.eval.domain.task.entity.ExportInputValue;
import com.polaris.kpi.eval.domain.task.entity.empeval.ExcelFinishValue;
import com.polaris.sdk.common.JsonFileTool;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.junit.jupiter.api.Test;

import java.util.*;
import java.util.stream.Collectors;

public class ExportInputFinishValueTest {

    private Integer performanceType = 1;
    private String sheetName = "批量导入完成值";
    private List<EvalKpiInputValue> headItems = new ArrayList<>();
    private List<ExportInputValue> dataList;
    private String title = "说明：\n" +
            "1、按指标批量导入\n" +
            "2、请按指标填写完成值和相应的更新说明\n" +
            "3、请勿修改表格其他内容，以免导致录入错误或录入失败；\n" +
            "4、您可以整行删除不需要录入的被考核人,整列删除不需要录入完成值的指标。";

    @Test
    public void exportInputFinishValue() {
        String fileName = "output.xlsx";
        this.dataList = JsonFileTool.toList("com.polaris.kpi.eval.domain.task.dmsvc/exportInputFinishValue.json", ExportInputValue.class);
        if (CollUtil.isEmpty(dataList)) {
            return;
        }
        List<List<String>> head = getHeads();
        addDynamicColumn(head);
        // 5. 写入数据
        EasyExcel.write(fileName)
                .head(head)
                .registerWriteHandler(new HorizontalCellStyleStrategy(getHeadStyleCellStyle(),getContentStyleCellStyle()))
                .registerWriteHandler(new ConditionStyleHandler())
                .registerWriteHandler(new FirstRowStyleHandler())
                .sheet(this.sheetName)
                .doWrite(generateDataRows());
    }

    /**
     * 表头样式
     * @return
     */
    private static WriteCellStyle getHeadStyleCellStyle() {
        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER); // 水平居中
        headStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
        WriteFont headFont = new WriteFont();
        headFont.setFontHeightInPoints((short) 12);
        headStyle.setWriteFont(headFont);
        return headStyle;
    }

    /**
     * 数据行样式
     * @return
     */
    private static WriteCellStyle getContentStyleCellStyle() {
        // 数据行居中
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER); // 水平居中
        contentStyle.setLocked(false);
        return contentStyle;
    }



    private List<List<String>> generateDataRows() {
        List<List<String>> rows = new ArrayList<>();
        if (CollUtil.isEmpty(this.dataList)) {
            return rows;
        }
        this.dataList.forEach(data -> {
            List<String> datas = new ArrayList<>();
            if (Objects.equals(this.performanceType, 1)) {
                datas.add(data.getTaskUserId());
                datas.add(data.getDingUserId());
                datas.add(data.getEmpName());
                datas.add(data.getJobnumber());
                datas.add(data.getEmpOrgName());
                datas.add(data.getOrgName1());
                datas.add(data.getOrgName2());
                datas.add(data.getOrgName3());
            } else {
                datas.add(data.getEvalOrgName());
            }
            datas.add(data.getTaskName());

            for (EvalKpiInputValue headItem : headItems) {
                Map<String, String> putMap = new HashMap<>();
                if (CollUtil.isNotEmpty(data.getItems())) {
                    for (EvalKpiInputValue itemPo : data.getItems()) {
                        putMap.put(itemPo.getKpiItemId() + "-" + itemPo.getKpiItemName(), "");
                    }
                }
                if (MapUtil.isNotEmpty(putMap) && putMap.containsKey(headItem.getKpiItemId() + "-" + headItem.getKpiItemName())) {
                    datas.add("");
                    datas.add("");
                } else {
                    datas.add("-");
                    datas.add("-");
                }
            }
            rows.add(datas);
        });
        return rows;
    }


    private List<List<String>> getHeads() {
        List<List<String>> head = new ArrayList<>();
        if (Objects.equals(this.performanceType, 1)) {
            head.add(createHeader(title,"taskUserId"));
            head.add(createHeader(title,"userId"));
            head.add(createHeader(title,"姓名"));
            head.add(createHeader(title,"工号"));
            head.add(createHeader(title,"所属部门"));
            head.add(createHeader(title,"一级部门"));
            head.add(createHeader(title,"二级部门"));
            head.add(createHeader(title,"三级部门"));
        } else {
            head.add(createHeader(title,"考核部门"));
        }
        head.add(createHeader(title,"考核任务"));
        return head;
    }

    private void addDynamicColumn(List<List<String>> head) {
        List<EvalKpiInputValue> items = this.dataList.stream().flatMap(data -> data.getItems().stream())
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getKpiItemId() + ";" + o.getKpiItemName()))), ArrayList::new));
        for (EvalKpiInputValue item : items) {
            head.add(createHeader(title,item.getKpiItemName(), "完成值"));
            head.add(createHeader(title,item.getKpiItemName(), "备注"));
        }
        this.headItems = items;
    }

    private static List<String> createHeader(String... headNames) {
        List<String> head = new ArrayList<>();
        for (String name : headNames) {
            head.add(name);
        }
        return head;
    }


    // 条件样式处理
    static class ConditionStyleHandler implements CellWriteHandler {
        @Override
        public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {
        }

        @Override
        public void afterCellCreate(WriteSheetHolder writeSheetHolder,
                                    WriteTableHolder writeTableHolder, Cell cell,
                                    Head head, Integer relativeRowIndex, Boolean isHead) {
        }

        @Override
        public void afterCellDataConverted(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, CellData cellData, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {

        }

        @Override
        public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, List<CellData> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
            // 假设完成值在动态列的第1列（索引从0开始）
            int completionValueColumnIndex = 9; // 第一动态列的完成值位置（第4列）
            if (cell.getColumnIndex() >= completionValueColumnIndex) {
                if (Objects.equals(cell.getStringCellValue(), "-")) {
                    setGrayStyle(cell, writeSheetHolder);
                }
            } else if (cell.getColumnIndex() == 0) {
                setGrayStyle(cell, writeSheetHolder);
            }
        }

        private void setGrayStyle(Cell cell, WriteSheetHolder holder) {
            Workbook workbook = holder.getSheet().getWorkbook();
            CellStyle style = workbook.createCellStyle();
            style.cloneStyleFrom(cell.getCellStyle());
            style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            style.setVerticalAlignment(VerticalAlignment.CENTER);
            style.setLocked(true);
            cell.setCellStyle(style);
            holder.getSheet().protectSheet("123");
        }
    }

    static class FirstRowStyleHandler implements CellWriteHandler {
        private CellStyle firstRowStyle;
        @Override
        public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {
        }

        @Override
        public void afterCellCreate(WriteSheetHolder writeSheetHolder,
                                    WriteTableHolder writeTableHolder, Cell cell,
                                    Head head, Integer relativeRowIndex, Boolean isHead) {
        }

        @Override
        public void afterCellDataConverted(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, CellData cellData, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {

        }

        @Override
        public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, List<CellData> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
            if (cell.getRowIndex() == 0 ) {
                Row row = cell.getRow();
                firstRowStyle = getFirstRowStyle(writeSheetHolder,firstRowStyle);
                cell.setCellStyle(firstRowStyle);
                // 3. 设置行高（仅在第一个单元格处理时执行）
                if (cell.getColumnIndex() == 0) {
                    // 方式一：固定行高（例如 30 点）
                    row.setHeightInPoints(100);
                }
            }
        }

        /**
         * 顶部文本样式
         * @return
         */
        private static CellStyle getFirstRowStyle(WriteSheetHolder writeSheetHolder,CellStyle firstRowStyle) {
            if (Objects.isNull(firstRowStyle)) {
                firstRowStyle = writeSheetHolder.getSheet().getWorkbook().createCellStyle();
                // 水平靠左对齐
                firstRowStyle.setAlignment(HorizontalAlignment.LEFT);
                // 垂直顶部对齐（取消默认居中）
                firstRowStyle.setVerticalAlignment(VerticalAlignment.TOP);
                // 自动换行（可选，支持多行内容）
                firstRowStyle.setWrapText(true);
            }
            return firstRowStyle;
        }
    }

    // 合并策略：合并第一行的 A1:H6 区域
    public static class MergeSixRowsStrategy extends AbstractMergeStrategy {
        @Override
        protected void merge(Sheet sheet, Cell cell, Head head, Integer relativeRowIndex) {
            if (cell.getRowIndex() == 0 && cell.getColumnIndex() == 0) {
                // 合并行范围：0~5（共6行），列范围：0~7（共8列）
                sheet.addMergedRegion(new CellRangeAddress(0, 5, 0, 7));
            }
        }
    }

    @Data
    public class BaseExcelData {

        private Map<Integer, String> head;
        private Map<String, Integer> headIndex;
        private List<Map<String, String>> datas = new ArrayList();

        public void setHead(Map<Integer, String> head) {
            this.head = head;
            headIndex = new HashMap<>();
            head.forEach((key, value) -> {
                headIndex.put(value, key);
            });
        }

        public String getValue(Map cellMap, String columName) {
            final Integer index = this.getHeadIndex().get(columName);
            final String value = MapUtils.getString(cellMap, index);
            return value;
        }

        public void addData(Map data) {
            datas.add(data);
        }
    }

    @Test
    public void improtInputFinishValue() {
        BaseExcelData importData = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.dmsvc/baseExcelData.json", BaseExcelData.class);
        List<Map<String, String>> datas = importData.getDatas();
        for (Map<String, String> map : datas) {
            // 先收集需要删除的键（避免遍历时修改导致的异常）
            List<String> keysToRemove = new ArrayList<>();
            for (Map.Entry<String, String> entry : map.entrySet()) {
                if (Objects.equals("-", entry.getValue()) || (Integer.valueOf(entry.getKey()) > 0 && Integer.valueOf(entry.getKey()) < 9)) {
                    keysToRemove.add(entry.getKey());
                }
            }
            // 统一删除所有需要移除的键
            for (String key : keysToRemove) {
                map.remove(key);
            }
        }
        System.out.println(JSONUtil.toJsonStr(datas));
        Map<Integer, String> head = importData.getHead();
        // 先收集需要删除的键（避免遍历时修改导致的异常）
        List<Integer> keysToRemove = new ArrayList<>();
        for (Map.Entry<Integer, String> entry : head.entrySet()) {
            if (Integer.valueOf(entry.getKey()) > 0 && Integer.valueOf(entry.getKey()) < 9) {
                keysToRemove.add(entry.getKey());
            }
        }
        // 统一删除所有需要移除的键
        for (Integer key : keysToRemove) {
            head.remove(key);
        }
        System.out.println(JSONUtil.toJsonStr(head));
        List<ExcelFinishValue> finishValues = new ArrayList<>();
        for (Map<String, String> map : datas) {
            List<Map<String, String>> maps = groupMapByKeyProximity(map, 1, 2);
            for (Map<String, String> stringMap : maps) {
                if (stringMap.size() == 1) {
                    continue;
                }
                ExcelFinishValue finishValue = new ExcelFinishValue();
                finishValue.setTaskUserId(maps.get(0).get("0"));
                int index = 1;
                for (Map.Entry<String, String> entry : stringMap.entrySet()) {
                    finishValue.setKpiItemName(head.get(Integer.valueOf(entry.getKey())));
                    if (index == 1) {
                        finishValue.setFinishValue(entry.getValue());
                        index++;
                    }else {
                        finishValue.setComment(entry.getValue());
                    }
                }
                finishValues.add(finishValue);
            }
        }
        System.out.println(JSONUtil.toJsonStr(finishValues));
    }


    public static List<Map<String, String>> groupMapByKeyProximity(Map<String, String> map, int threshold, int size) {
        List<KeyEntry> keyEntries = new ArrayList<>();
        for (String key : map.keySet()) {
            try {
                int numericKey = Integer.parseInt(key);
                keyEntries.add(new KeyEntry(key, numericKey));
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("Key is not a valid integer: " + key, e);
            }
        }

        keyEntries.sort(Comparator.comparingInt(KeyEntry::getNumericKey));

        List<List<KeyEntry>> groups = new ArrayList<>();
        if (keyEntries.isEmpty()) {
            return new ArrayList<>();
        }

        List<KeyEntry> currentGroup = new ArrayList<>();
        currentGroup.add(keyEntries.get(0));
        groups.add(currentGroup);

        for (int i = 1; i < keyEntries.size(); i++) {
            KeyEntry current = keyEntries.get(i);
            KeyEntry lastInGroup = currentGroup.get(currentGroup.size() - 1);
            int diff = current.getNumericKey() - lastInGroup.getNumericKey();
            if (diff <= threshold) {
                if (currentGroup.size() >= size) {
                    currentGroup = new ArrayList<>();
                    currentGroup.add(current);
                    groups.add(currentGroup);
                    continue;
                }
                currentGroup.add(current);
            } else {
                currentGroup = new ArrayList<>();
                currentGroup.add(current);
                groups.add(currentGroup);
            }
        }

        List<Map<String, String>> result = new ArrayList<>();
        for (List<KeyEntry> group : groups) {
            Map<String, String> subMap = new LinkedHashMap<>();
            for (KeyEntry entry : group) {
                subMap.put(entry.originalKey, map.get(entry.originalKey));
            }
            result.add(subMap);
        }

        return result;
    }


    private static class KeyEntry {
        private final String originalKey;
        private final int numericKey;

        public KeyEntry(String originalKey, int numericKey) {
            this.originalKey = originalKey;
            this.numericKey = numericKey;
        }

        public int getNumericKey() {
            return numericKey;
        }
    }
}
