package com.polaris.kpi.eval.domain.task.entity;

import cn.com.polaris.kpi.eval.StaffConfItem;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalKpiType;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalRule;
import com.polaris.kpi.eval.domain.task.entity.empeval.KpiListWrap;
import com.polaris.sdk.type.ListWrap;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;


public class EmpEvalRuleTest {


    @Test
    public void replaceKpiOwner() {
        String json="[{\"id\":\"1270542\",\"taskUserId\":\"1250442\",\"kpiTypeId\":\"1015801\",\"kpiTypeName\":\"默认考核维度\",\"kpiItemId\":\"87c4f333-8d58-4fd6-8b7e-45b11534fe7a\",\"kpiItemName\":\"培训达成（第二阶段）\",\"resultInputType\":\"exam\",\"resultInputEmpId\":\"1100018\",\"empId\":\"1100018\"},{\"id\":\"1270543\",\"taskUserId\":\"1250442\",\"kpiTypeId\":\"1015801\",\"kpiTypeName\":\"默认考核维度\",\"kpiItemId\":\"8c0ee6a6-eab8-44bd-82b2-0edb76af8768\",\"kpiItemName\":\"阶段工作任务（如需）\",\"resultInputType\":\"exam\",\"resultInputEmpId\":\"1100018\",\"empId\":\"1100018\"}]";
        List<EmpEvalKpiType> kpiTypelist = JSONUtil.parseArray(json).toList(EmpEvalKpiType.class);
        KpiListWrap kpiTypes = (KpiListWrap) new ListWrap();
        kpiTypes.setDatas(kpiTypelist);
        String newOrgOwnerId="1001";
        EmpEvalRule rule = new EmpEvalRule();
        rule.setKpiTypes(kpiTypes);
        rule.replaceKpiOwner(newOrgOwnerId);

        Assert.assertTrue(rule.getKpiTypes().getDatas().get(0).getItems().get(0).getResultInputEmpId().equals("1001"));
    }
}
