package com.polaris.kpi.eval.domain.task.dmsvc;

import com.polaris.kpi.eval.domain.task.entity.EmpEvalScorer;
import com.polaris.kpi.eval.domain.task.entity.EmpEvalScorerNode;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.entity.empeval.EvalScorersWrap;
import com.polaris.kpi.eval.domain.task.entity.empeval.V3SubmitedEvalNodeScore;
import com.polaris.sdk.type.TenantId;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.doNothing;

public class SubmitOrEvalScorerV3DmSvcTest {
    @Mock
    private EvalScorersWrap scorersWrap;

    @Mock
    private EmpEvalMerge empEval;

    @Mock
    private EvalUser evalUser;

    @InjectMocks
    private SubmitOrEvalScorerV3DmSvc submitOrEvalScorerV3DmSvc;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        TenantId tenantId = new TenantId("1");
        String opEmpId = "1";
        List<V3SubmitedEvalNodeScore> nodeScores = new ArrayList<>();
        V3SubmitedEvalNodeScore nodeScore = new V3SubmitedEvalNodeScore();
        nodeScore.setScorerType("type1");
        nodeScore.setApprovalOrder(1);
        nodeScores.add(nodeScore);

        EmpEvalScorer scorer = new EmpEvalScorer();
        scorer.setScorerId("scorer1");
        EmpEvalScorerNode scorerNode = new EmpEvalScorerNode();
        scorerNode.setScorerType("type1");
        scorerNode.setApprovalOrder(1);
        scorerNode.setStatus(1);
        scorer.getScorerNodes().add(scorerNode);

        when(empEval.getEvalScorersWrap()).thenReturn(scorersWrap);
        when(scorersWrap.markOrModeScoreNs(anyList())).thenReturn(Arrays.asList(scorer));

        submitOrEvalScorerV3DmSvc = new SubmitOrEvalScorerV3DmSvc(tenantId, opEmpId, evalUser, empEval, nodeScores,null);
    }

    @Test
    public void submitOrScorer_ScorersWrapIsNull_DoesNothing() {
        submitOrEvalScorerV3DmSvc = new SubmitOrEvalScorerV3DmSvc(new TenantId("1"), "1", null, null, new ArrayList<>(),null);
        submitOrEvalScorerV3DmSvc.submitOrScorer();
        verify(scorersWrap, never()).markOrModeScoreNs(anyList());
    }

    @Test
    public void submitOrScorer_MarkOrModeScoreNsReturnsNonEmptyList_UpdatesOrWaitScorers() {
        submitOrEvalScorerV3DmSvc.submitOrScorer();
        assertEquals(1, submitOrEvalScorerV3DmSvc.getOrWaitScorers().size());
    }

    @Test
    public void submitOrScorer_MarkOrModeScoreNsReturnsEmptyList_OrWaitScorersIsEmpty() {
        when(scorersWrap.markOrModeScoreNs(anyList())).thenReturn(new ArrayList<>());
        submitOrEvalScorerV3DmSvc.submitOrScorer();
        assertEquals(0, submitOrEvalScorerV3DmSvc.getOrWaitScorers().size());
    }

    @Test
    public void submitOrScorer_FilterFinishedScorerIds_SomeFinishedScorers() {
        submitOrEvalScorerV3DmSvc.submitOrScorer();
        assertEquals(1, submitOrEvalScorerV3DmSvc.getOrFinishedScorerIds().size());
    }

    @Test
    public void submitOrScorer_FilterFinishedScorerIds_NoFinishedScorers() {
        EmpEvalScorer scorer = new EmpEvalScorer();
        scorer.setScorerId("scorer1");
        EmpEvalScorerNode scorerNode = new EmpEvalScorerNode();
        scorerNode.setScorerType("type2");
        scorerNode.setApprovalOrder(2);
        scorerNode.setStatus(1);
        scorer.getScorerNodes().add(scorerNode);
        when(scorersWrap.markOrModeScoreNs(anyList())).thenReturn(Arrays.asList(scorer));
        submitOrEvalScorerV3DmSvc.submitOrScorer();
        assertEquals(0, submitOrEvalScorerV3DmSvc.getOrFinishedScorerIds().size());
    }

    @Test
    public void submitOrScorer_UpReviewers_ExecutesSuccessfully() {
        doNothing().when(evalUser).removeReviewers(anyList());
        submitOrEvalScorerV3DmSvc.submitOrScorer();
        verify(evalUser, times(1)).removeReviewers(anyList());
    }
}
