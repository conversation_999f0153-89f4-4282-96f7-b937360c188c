package com.polaris.kpi.eval.domain.cycle.dmsvc;

import com.polaris.kpi.eval.domain.cycle.repo.SnapOnTaskRepo;
import com.polaris.kpi.eval.domain.task.entity.calibrated.ResultRankInstance;
import com.polaris.kpi.eval.domain.task.entity.grade.ScoreRule;
import com.polaris.kpi.eval.domain.task.repo.GradeRepo;
import com.polaris.kpi.eval.domain.task.repo.ScoreRuleSnapRepo;
import com.polaris.sdk.type.TenantId;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.mockito.Mockito;

public class CreateRuleSnapOnTaskDmSvcTest {

    @Test
    @DisplayName("传入规则id-任务上创建新快照")
    public void createRankRuleSnapOnTask3() {
        ScoreRuleSnapRepo snapRepo = Mockito.mock(ScoreRuleSnapRepo.class);
        TenantId companyId = new TenantId("companyId");
        String cycleId = "cycleId";
        String ruleId = "ruleId";
        String snapId = "snapId";
        Mockito.when(snapRepo.getMainRankInstanceByRuleId(companyId, cycleId, ruleId)).thenReturn(null);

        GradeRepo gradeRepo = Mockito.mock(GradeRepo.class);
        Mockito.when(gradeRepo.getScoreRule(companyId, ruleId)).thenReturn(new ScoreRule());

        CreateRuleSnapOnTaskDmSvc dmSvc = new CreateRuleSnapOnTaskDmSvc(companyId, cycleId, ruleId);//不存在
        dmSvc.createRankRuleSnapOnTask(gradeRepo);
        Assert.assertTrue(dmSvc.getRule() != null);

        SnapOnTaskRepo onTaskRepo = Mockito.mock(SnapOnTaskRepo.class);
        Mockito.doNothing().when(onTaskRepo).saveScoreRuleSnapOnTask(cycleId, snapId, dmSvc.getRule());
        dmSvc.saveRankRuleSnapOnTask(onTaskRepo,snapId);
    }

}