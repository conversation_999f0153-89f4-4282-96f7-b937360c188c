package com.polaris.kpi.eval.domain.task.entity.grade;

import com.polaris.sdk.type.TenantId;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;

/**
 * <AUTHOR> lufei
 * @date 2022/3/23 4:17 下午
 */
public class GradeTest {
    private Grade grade;
    private TenantId tenantId = new TenantId("00001");

    @Before
    public void setUp() throws Exception {
        final ArrayList<GradeStep> steps = new ArrayList<>();
        final GradeStep e = new GradeStep(tenantId, "1");
        e.setId("1");
        steps.add(e);
        final GradeStep e2 = new GradeStep(tenantId, "2");
        e2.setId("2");
        steps.add(e2);
        grade = new Grade(tenantId, steps);
    }

    @Test
    public void addStep() {
        grade.addStep(1, "1", "4");
        Assert.assertTrue(grade.getSteps().get(1).getName().equals("4"));
        grade.addStep(-1, "1", "5");
        Assert.assertTrue(grade.getSteps().get(0).getName().equals("5"));
        for (GradeStep step : grade.getSteps()) {
            System.out.println(step.getName());
        }
    }
}