package com.polaris.kpi.eval.domain.task.entity.calibrated;

import org.junit.Assert;
import org.junit.Test;

import java.math.BigDecimal;

import static org.junit.Assert.*;

public class RankRuleScoreRangeSnapTest {

    @Test
    public void computeRankRange() {
        int totalCnt = 2;
        RankRuleScoreRangeSnap snap4 = new RankRuleScoreRangeSnap(new BigDecimal(8), "S");
        int to = snap4.computeRankRange(0, totalCnt);
        Assert.assertTrue(to - snap4.getRangeFrom() == 0);
        System.out.println(snap4);

        RankRuleScoreRangeSnap snap3 = new RankRuleScoreRangeSnap(new BigDecimal(12), "A");
        to = snap3.computeRankRange(to, totalCnt);
        System.out.println(snap3);
        Assert.assertTrue(to - snap3.getRangeFrom() == 0);

        RankRuleScoreRangeSnap snap2 = new RankRuleScoreRangeSnap(new BigDecimal(60), "B");
        to = snap2.computeRankRange(to, totalCnt);
        System.out.println(snap2);
        Assert.assertTrue(to - snap2.getRangeFrom() == 1);

        RankRuleScoreRangeSnap snap1 = new RankRuleScoreRangeSnap(new BigDecimal(15), "C");
        to = snap1.computeRankRange(to, totalCnt);
        System.out.println(snap1);
        Assert.assertTrue(to - snap1.getRangeFrom() == 0);

        RankRuleScoreRangeSnap snap = new RankRuleScoreRangeSnap(new BigDecimal(5), "D");
        to = snap.computeLastRankRange(to, totalCnt);
        System.out.println(snap);
        Assert.assertTrue(to - snap.getRangeFrom() == 1);
    }

    @Test
    public void computeRankRange2() {
        int totalCnt = 29;
        RankRuleScoreRangeSnap snap4 = new RankRuleScoreRangeSnap(new BigDecimal(8), "S");
        int to = snap4.computeRankRange(0, totalCnt);
        System.out.println(snap4);
        Assert.assertTrue(to - snap4.getRangeFrom() == 2);
//        Assert.assertTrue(to == 0);

        RankRuleScoreRangeSnap snap3 = new RankRuleScoreRangeSnap(new BigDecimal(12), "A");
        to = snap3.computeRankRange(to, totalCnt);
        System.out.println(snap3);
        Assert.assertTrue(to - snap3.getRangeFrom() == 3);
//        Assert.assertTrue(to == 0);

        RankRuleScoreRangeSnap snap2 = new RankRuleScoreRangeSnap(new BigDecimal(60), "B");
        to = snap2.computeRankRange(to, totalCnt);
        System.out.println(snap2);
        Assert.assertTrue(to - snap2.getRangeFrom() == 17);

        RankRuleScoreRangeSnap snap1 = new RankRuleScoreRangeSnap(new BigDecimal(15), "C");
        to = snap1.computeRankRange(to, totalCnt);
        System.out.println(snap1);
        Assert.assertTrue(to - snap1.getRangeFrom() == 4);

        RankRuleScoreRangeSnap snap = new RankRuleScoreRangeSnap(new BigDecimal(5), "D");
        to = snap.computeLastRankRange(to, totalCnt);
        System.out.println(snap);
        Assert.assertTrue(to - snap.getRangeFrom() == 3);
    }


    @Test
    public void computeRankRange3() {
        int totalCnt = 38;
        RankRuleScoreRangeSnap snap4 = new RankRuleScoreRangeSnap(new BigDecimal(8), "S");
        int to = snap4.computeRankRange(0, totalCnt);
        System.out.println(snap4);
        Assert.assertTrue(to - snap4.getRangeFrom() == 3);

        RankRuleScoreRangeSnap snap3 = new RankRuleScoreRangeSnap(new BigDecimal(12), "A");
        to = snap3.computeRankRange(to, totalCnt);
        System.out.println(snap3);
        Assert.assertTrue(to - snap3.getRangeFrom() == 5);

        RankRuleScoreRangeSnap snap2 = new RankRuleScoreRangeSnap(new BigDecimal(60), "B");
        to = snap2.computeRankRange(to, totalCnt);
        System.out.println(snap2);
        Assert.assertTrue(to - snap2.getRangeFrom() == 23);

        RankRuleScoreRangeSnap snap1 = new RankRuleScoreRangeSnap(new BigDecimal(15), "C");
        to = snap1.computeRankRange(to, totalCnt);
        System.out.println(snap1);
        Assert.assertTrue(to - snap1.getRangeFrom() == 6);

        RankRuleScoreRangeSnap snap = new RankRuleScoreRangeSnap(new BigDecimal(5), "D");
        to = snap.computeLastRankRange(to, totalCnt);
        System.out.println(snap);
        Assert.assertTrue(to - snap.getRangeFrom() == 1);
    }

    @Test
    public void computeRankRange4() {
        int totalCnt = 100;
        RankRuleScoreRangeSnap snap4 = new RankRuleScoreRangeSnap(new BigDecimal(8), "S");
        int to = snap4.computeRankRange(0, totalCnt);
        System.out.println(snap4);
        Assert.assertTrue(to - snap4.getRangeFrom() == 8);

        RankRuleScoreRangeSnap snap3 = new RankRuleScoreRangeSnap(new BigDecimal(12), "A");
        to = snap3.computeRankRange(to, totalCnt);
        System.out.println(snap3);
        Assert.assertTrue(to - snap3.getRangeFrom() == 12);

        RankRuleScoreRangeSnap snap2 = new RankRuleScoreRangeSnap(new BigDecimal(60), "B");
        to = snap2.computeRankRange(to, totalCnt);
        System.out.println(snap2);
        Assert.assertTrue(to - snap2.getRangeFrom() == 60);

        RankRuleScoreRangeSnap snap1 = new RankRuleScoreRangeSnap(new BigDecimal(15), "C");
        to = snap1.computeRankRange(to, totalCnt);
        System.out.println(snap1);
        Assert.assertTrue(to - snap1.getRangeFrom() == 15);

        RankRuleScoreRangeSnap snap = new RankRuleScoreRangeSnap(new BigDecimal(5), "D");
        to = snap.computeLastRankRange(to, totalCnt);
        System.out.println(snap);
        Assert.assertTrue(to - snap.getRangeFrom() == 5);
    }
}