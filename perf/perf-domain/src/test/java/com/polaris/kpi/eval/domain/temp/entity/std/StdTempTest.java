package com.polaris.kpi.eval.domain.temp.entity.std;

import com.alibaba.fastjson.JSONObject;
import com.polaris.kpi.eval.domain.temp.entity.PerfTemplKpiType;
import com.polaris.sdk.type.TenantId;
import org.junit.Assert;
import org.junit.Test;



public class StdTempTest {

    @Test
    public void initSynthTemp() {
        //综合示例模板
        TenantId companyId = new TenantId("001");
        String opEmpId = "opEmpId001";
        StdTemp synthTemp = StdTemp.initSynthTemp(companyId, opEmpId, "【示例】综合绩效考核模板", null, PerfTemplKpiType::initSynthExampleKpiType);
        JSONObject jsonRs = (JSONObject) JSONObject.toJSON(synthTemp);
        System.out.println(jsonRs.toJSONString());
        StdTemp expBean = JSONObject.parseObject("{\"checkItemWeight\":100,\"kpiTypes\":[{\"classify\":\"custom\",\"reserveOkrWeight\":100,\"scoreOptType\":2,\"typeName\":\"工作能力\",\"okr\":false,\"order\":0,\"maxExtraScore\":0,\"openRaterRule\":false,\"openOkrScore\":0,\"isOkr\":\"false\",\"emptyType\":false,\"typeId\":\"b060634b-a108-445e-9745-254bb7fad483\",\"typeWeight\":0,\"items\":[{\"subtractLimit\":0,\"itemRule\":\"1.熟练掌握本岗位所需理论知识并能灵活运用\\n2.熟悉本职工作流程和要求\\n3.具有不断学习本岗位技能知识的行动并运用到实际工作当中\",\"showFinishBar\":1,\"itemUnit\":\"%\",\"multipleReviewersType\":\"or\",\"plusLimit\":0,\"itemScoreValue\":\"{\\\"type\\\":\\\"toMainScore\\\"}\",\"order\":0,\"scorerType\":\"exam\",\"inputFormat\":\"num\",\"resultInputType\":\"exam\",\"itemFullScoreCfg\":\"false\",\"mustResultInput\":0,\"itemFieldJson\":\"\",\"formulaCondition\":\"\",\"itemType\":\"non-measurable\",\"inputUserIds\":[],\"itemValue\":100,\"scorerObjId\":\"\",\"kpiItemName\":\"专业技能\",\"formulaFieldList\":[{\"companyId\":\"001\",\"kpiItemId\":\"6c9132e8-6de7-4b30-a179-65132e01a5e8\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"7853d725-c749-42b4-8b1f-3b7133a33ed0\",\"templBaseId\":\"1d0a65b8-5503-4a05-bfbe-0344a0ab8842\"},{\"companyId\":\"001\",\"kpiItemId\":\"6c9132e8-6de7-4b30-a179-65132e01a5e8\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"4559e9c1-15cf-4506-b249-f584c05561dd\",\"templBaseId\":\"1d0a65b8-5503-4a05-bfbe-0344a0ab8842\"}],\"itemWeight\":20,\"kpiItemId\":\"6c9132e8-6de7-4b30-a179-65132e01a5e8\",\"isNewEmp\":0},{\"subtractLimit\":0,\"itemRule\":\"1.对于领导安排的工作能运用最合理的方法采取行动\\n2.对工作中遇到的问题能够想方法去解决并及时反馈给上级领导\",\"showFinishBar\":1,\"itemUnit\":\"%\",\"multipleReviewersType\":\"or\",\"plusLimit\":0,\"itemScoreValue\":\"{\\\"type\\\":\\\"toMainScore\\\"}\",\"order\":1,\"scorerType\":\"exam\",\"inputFormat\":\"num\",\"resultInputType\":\"exam\",\"itemFullScoreCfg\":\"false\",\"mustResultInput\":0,\"itemFieldJson\":\"\",\"formulaCondition\":\"\",\"itemType\":\"non-measurable\",\"inputUserIds\":[],\"itemValue\":100,\"scorerObjId\":\"\",\"kpiItemName\":\"工作执行力\",\"formulaFieldList\":[{\"companyId\":\"001\",\"kpiItemId\":\"2daef59a-72db-473b-a6a6-0f568428e40b\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"7b4430fa-b21c-4973-99f7-48a96a0bf66f\",\"templBaseId\":\"1d0a65b8-5503-4a05-bfbe-0344a0ab8842\"},{\"companyId\":\"001\",\"kpiItemId\":\"2daef59a-72db-473b-a6a6-0f568428e40b\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"70f5186c-2de7-43d1-9507-5ce9ab40fda8\",\"templBaseId\":\"1d0a65b8-5503-4a05-bfbe-0344a0ab8842\"}],\"itemWeight\":20,\"kpiItemId\":\"2daef59a-72db-473b-a6a6-0f568428e40b\",\"isNewEmp\":0},{\"subtractLimit\":0,\"itemRule\":\"1.能清晰的表达自己需求、能倾听沟通者的需求\\n2.与各同事有效地进行沟通信息的能力，沟通上能达到了预期的目标，或满足了沟通者的需要。\",\"showFinishBar\":1,\"itemUnit\":\"%\",\"multipleReviewersType\":\"or\",\"plusLimit\":0,\"itemScoreValue\":\"{\\\"type\\\":\\\"toMainScore\\\"}\",\"order\":2,\"scorerType\":\"exam\",\"inputFormat\":\"num\",\"resultInputType\":\"exam\",\"itemFullScoreCfg\":\"false\",\"mustResultInput\":0,\"itemFieldJson\":\"\",\"formulaCondition\":\"\",\"itemType\":\"non-measurable\",\"inputUserIds\":[],\"itemValue\":100,\"scorerObjId\":\"\",\"kpiItemName\":\"沟通能力\",\"formulaFieldList\":[{\"companyId\":\"001\",\"kpiItemId\":\"d2dca781-cf15-4ea1-ae13-5bea138972b4\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"dd713349-f487-443e-b49a-3a131d472263\",\"templBaseId\":\"1d0a65b8-5503-4a05-bfbe-0344a0ab8842\"},{\"companyId\":\"001\",\"kpiItemId\":\"d2dca781-cf15-4ea1-ae13-5bea138972b4\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"2ffb7322-b614-4d17-a2da-b95eca0d810a\",\"templBaseId\":\"1d0a65b8-5503-4a05-bfbe-0344a0ab8842\"}],\"itemWeight\":10,\"kpiItemId\":\"d2dca781-cf15-4ea1-ae13-5bea138972b4\",\"isNewEmp\":0}]},{\"classify\":\"custom\",\"reserveOkrWeight\":100,\"scoreOptType\":2,\"typeName\":\"工作态度\",\"okr\":false,\"order\":1,\"maxExtraScore\":0,\"openRaterRule\":false,\"openOkrScore\":0,\"isOkr\":\"false\",\"emptyType\":false,\"typeId\":\"f219b253-4662-4cb1-b3f1-d51df0b7d937\",\"typeWeight\":0,\"items\":[{\"subtractLimit\":0,\"itemRule\":\"1.严格遵守规章制度，按时出勤，迟到或早退1次扣2分；旷工一次扣6分；请假1次以上扣3分；扣完为止；\\n2.忘记打卡4次以上每多一次扣1分，扣完为止；\\n3.周报少提交一次扣2分，累计计算，扣完为止；\",\"showFinishBar\":1,\"itemUnit\":\"%\",\"multipleReviewersType\":\"or\",\"plusLimit\":0,\"itemScoreValue\":\"{\\\"type\\\":\\\"toMainScore\\\"}\",\"order\":0,\"scorerType\":\"exam\",\"inputFormat\":\"num\",\"resultInputType\":\"exam\",\"itemFullScoreCfg\":\"false\",\"mustResultInput\":0,\"itemFieldJson\":\"\",\"formulaCondition\":\"\",\"itemType\":\"measurable\",\"inputUserIds\":[],\"itemValue\":100,\"scorerObjId\":\"\",\"kpiItemName\":\"纪律性\",\"formulaFieldList\":[{\"companyId\":\"001\",\"kpiItemId\":\"20c231b0-ec9e-4a16-a425-3742f835b80c\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"b3cce73f-9388-4536-8264-067dc48e5399\",\"templBaseId\":\"1d0a65b8-5503-4a05-bfbe-0344a0ab8842\"},{\"companyId\":\"001\",\"kpiItemId\":\"20c231b0-ec9e-4a16-a425-3742f835b80c\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"3ee03f77-7455-4807-aa95-9d76487ecc58\",\"templBaseId\":\"1d0a65b8-5503-4a05-bfbe-0344a0ab8842\"}],\"itemWeight\":10,\"kpiItemId\":\"20c231b0-ec9e-4a16-a425-3742f835b80c\",\"isNewEmp\":0},{\"subtractLimit\":0,\"itemRule\":\"1.团结同事与部门；\\n2.团结同事与部门；\\n3.自觉维护公司形象名誉，不说、不做有负面影响的事\",\"showFinishBar\":1,\"itemUnit\":\"%\",\"multipleReviewersType\":\"or\",\"plusLimit\":0,\"itemScoreValue\":\"{\\\"type\\\":\\\"toMainScore\\\"}\",\"order\":1,\"scorerType\":\"exam\",\"inputFormat\":\"num\",\"resultInputType\":\"exam\",\"itemFullScoreCfg\":\"false\",\"mustResultInput\":0,\"itemFieldJson\":\"\",\"formulaCondition\":\"\",\"itemType\":\"non-measurable\",\"inputUserIds\":[],\"itemValue\":100,\"scorerObjId\":\"\",\"kpiItemName\":\"团队协调性\",\"formulaFieldList\":[{\"companyId\":\"001\",\"kpiItemId\":\"e989cbad-5842-4b26-8a54-1902380c0425\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"a921e89f-f7e3-4135-82bb-4eca6cac6642\",\"templBaseId\":\"1d0a65b8-5503-4a05-bfbe-0344a0ab8842\"},{\"companyId\":\"001\",\"kpiItemId\":\"e989cbad-5842-4b26-8a54-1902380c0425\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"d76a0d37-7fcc-4523-a809-e63e4b888f6c\",\"templBaseId\":\"1d0a65b8-5503-4a05-bfbe-0344a0ab8842\"}],\"itemWeight\":10,\"kpiItemId\":\"e989cbad-5842-4b26-8a54-1902380c0425\",\"isNewEmp\":0},{\"subtractLimit\":0,\"itemRule\":\"1.客户第一，心态上、工作中反映出来的高度负责责任意识。\\n2.团队建设：团队目标达成、团队素质与能力提高、团队氛围良好、团队凝聚力强、团队执行力强。\",\"showFinishBar\":1,\"itemUnit\":\"分\",\"multipleReviewersType\":\"or\",\"plusLimit\":0,\"itemScoreValue\":\"{\\\"type\\\":\\\"toMainScore\\\"}\",\"order\":2,\"scorerType\":\"exam\",\"inputFormat\":\"num\",\"resultInputType\":\"exam\",\"itemFullScoreCfg\":\"false\",\"mustResultInput\":0,\"itemFieldJson\":\"\",\"formulaCondition\":\"\",\"itemType\":\"measurable\",\"inputUserIds\":[],\"itemValue\":80,\"scorerObjId\":\"\",\"kpiItemName\":\"责任心\",\"formulaFieldList\":[{\"companyId\":\"001\",\"kpiItemId\":\"31a94cec-e328-40bf-af6c-de53e9459f95\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"ee73104f-930b-411a-855f-b10708050e2a\",\"templBaseId\":\"1d0a65b8-5503-4a05-bfbe-0344a0ab8842\"},{\"companyId\":\"001\",\"kpiItemId\":\"31a94cec-e328-40bf-af6c-de53e9459f95\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"0ace35c2-a309-442a-ab68-de5d39a9e97b\",\"templBaseId\":\"1d0a65b8-5503-4a05-bfbe-0344a0ab8842\"}],\"itemWeight\":10,\"kpiItemId\":\"31a94cec-e328-40bf-af6c-de53e9459f95\",\"isNewEmp\":0}]},{\"classify\":\"custom\",\"reserveOkrWeight\":100,\"scoreOptType\":2,\"typeName\":\"工作业绩\",\"okr\":false,\"order\":2,\"maxExtraScore\":0,\"openRaterRule\":false,\"openOkrScore\":0,\"isOkr\":\"false\",\"emptyType\":false,\"typeId\":\"d289f10d-e29d-4668-b966-45dee1dd7791\",\"typeWeight\":0,\"items\":[{\"subtractLimit\":0,\"itemRule\":\"目标制定：制定部门月、季、年度目标及分解目标的质量、公平性和准确性。\\n目标达成度：工作目标达成情况。\\n工作量：如期完成工作任务量。\\n工作质量：工作效率高且完成质量高。\",\"showFinishBar\":1,\"itemUnit\":\"万\",\"multipleReviewersType\":\"or\",\"plusLimit\":0,\"itemScoreValue\":\"{\\\"type\\\":\\\"toMainScore\\\"}\",\"order\":0,\"scorerType\":\"exam\",\"inputFormat\":\"num\",\"resultInputType\":\"exam\",\"itemFullScoreCfg\":\"false\",\"mustResultInput\":0,\"itemFieldJson\":\"\",\"formulaCondition\":\"\",\"itemType\":\"measurable\",\"inputUserIds\":[],\"itemValue\":80,\"scorerObjId\":\"\",\"kpiItemName\":\"目标达成率\",\"formulaFieldList\":[{\"companyId\":\"001\",\"kpiItemId\":\"79d7736a-fdd0-4142-92a6-c58f0c844647\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"55d3ee8e-1601-452a-a872-f6ec6ffaf350\",\"templBaseId\":\"1d0a65b8-5503-4a05-bfbe-0344a0ab8842\"},{\"companyId\":\"001\",\"kpiItemId\":\"79d7736a-fdd0-4142-92a6-c58f0c844647\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"023b8efa-3262-47f5-8edf-be80c4c89c97\",\"templBaseId\":\"1d0a65b8-5503-4a05-bfbe-0344a0ab8842\"}],\"itemWeight\":20,\"kpiItemId\":\"79d7736a-fdd0-4142-92a6-c58f0c844647\",\"isNewEmp\":0}]},{\"classify\":\"plus\",\"reserveOkrWeight\":100,\"scoreOptType\":2,\"typeName\":\"加分项\",\"okr\":false,\"order\":3,\"maxExtraScore\":10,\"openRaterRule\":false,\"openOkrScore\":0,\"isOkr\":\"false\",\"emptyType\":false,\"typeId\":\"cbd745cb-3482-4dfa-87e5-6c4887e41cb8\",\"typeWeight\":0,\"items\":[{\"subtractLimit\":0,\"itemRule\":\"重大突破加分项：\\n1、解决重大突破问题，加2分\\n2、解决重大危机，加5分\\n3、解决重复性出现的问题，加3分\",\"showFinishBar\":1,\"itemUnit\":\"%\",\"multipleReviewersType\":\"or\",\"plusLimit\":10,\"itemScoreValue\":\"{\\\"type\\\":\\\"toMainScore\\\"}\",\"order\":0,\"scorerType\":\"exam\",\"inputFormat\":\"num\",\"resultInputType\":\"exam\",\"itemFullScoreCfg\":\"false\",\"mustResultInput\":0,\"itemFieldJson\":\"\",\"formulaCondition\":\"\",\"itemType\":\"non-measurable\",\"inputUserIds\":[],\"scorerObjId\":\"\",\"kpiItemName\":\"加分项\",\"formulaFieldList\":[{\"companyId\":\"001\",\"kpiItemId\":\"eaa989ef-6f94-4b65-9af3-15bc13e5fcd1\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"b0a3083d-abb5-4ed3-9cd8-db51ef2b1d9d\",\"templBaseId\":\"1d0a65b8-5503-4a05-bfbe-0344a0ab8842\"},{\"companyId\":\"001\",\"kpiItemId\":\"eaa989ef-6f94-4b65-9af3-15bc13e5fcd1\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"c5bb4982-7a2d-405f-84bc-084c2ff81d52\",\"templBaseId\":\"1d0a65b8-5503-4a05-bfbe-0344a0ab8842\"}],\"itemWeight\":0,\"kpiItemId\":\"eaa989ef-6f94-4b65-9af3-15bc13e5fcd1\",\"isNewEmp\":0}]},{\"classify\":\"subtract\",\"reserveOkrWeight\":100,\"scoreOptType\":2,\"typeName\":\"减分项\",\"okr\":false,\"order\":4,\"maxExtraScore\":10,\"openRaterRule\":false,\"openOkrScore\":0,\"isOkr\":\"false\",\"emptyType\":false,\"typeId\":\"a7e12cf1-1d69-4ff2-81bd-14ebb6d706c6\",\"typeWeight\":0,\"items\":[{\"subtractLimit\":10,\"itemRule\":\"1、客户投诉，扣5分\\n2、员工投诉，扣2分\\n3、出现重大事故，扣5分\",\"showFinishBar\":1,\"itemUnit\":\"%\",\"multipleReviewersType\":\"or\",\"plusLimit\":0,\"itemScoreValue\":\"{\\\"type\\\":\\\"toMainScore\\\"}\",\"order\":0,\"scorerType\":\"exam\",\"inputFormat\":\"num\",\"resultInputType\":\"exam\",\"itemFullScoreCfg\":\"false\",\"mustResultInput\":0,\"itemFieldJson\":\"\",\"formulaCondition\":\"\",\"itemType\":\"non-measurable\",\"inputUserIds\":[],\"scorerObjId\":\"\",\"kpiItemName\":\"减分项\",\"formulaFieldList\":[{\"companyId\":\"001\",\"kpiItemId\":\"36a41655-bb7d-4d06-b2c6-0d5b92728ba6\",\"formulaFieldValue\":0,\"formulaFieldName\":\"目标值\",\"companyFieldId\":\"b646c0ed-d9b1-4fb6-9f32-038bcce2b0a0\",\"templBaseId\":\"1d0a65b8-5503-4a05-bfbe-0344a0ab8842\"},{\"companyId\":\"001\",\"kpiItemId\":\"36a41655-bb7d-4d06-b2c6-0d5b92728ba6\",\"formulaFieldValue\":0,\"formulaFieldName\":\"完成值\",\"companyFieldId\":\"be3e1e2f-eb8e-42ff-ad92-7b4b0a7f825c\",\"templBaseId\":\"1d0a65b8-5503-4a05-bfbe-0344a0ab8842\"}],\"itemWeight\":0,\"kpiItemId\":\"36a41655-bb7d-4d06-b2c6-0d5b92728ba6\",\"isNewEmp\":0}]}],\"baseScore\":0,\"exceedFullScore\":\"false\",\"evaluateType\":\"simple\",\"typeWeightSwitch\":\"close\",\"checkItemWeightFlag\":\"true\",\"isExample\":1,\"typeWeightLimitFlag\":\"false\",\"name\":\"【示例】综合绩效考核模板\",\"id\":\"1d0a65b8-5503-4a05-bfbe-0344a0ab8842\",\"evaluate\":{\"subScoreRule\":\"item\",\"superiorScoreWeight\":80,\"plusOrSubComment\":0,\"selfScoreWeight\":20,\"createdUser\":\"999999\",\"superiorScoreRule\":\"item\",\"peerScoreList\":[],\"superiorScoreOrder\":\"sameTime\",\"superiorScoreFlag\":\"true\",\"selfScoreFlag\":\"true\",\"subScoreList\":[],\"superiorScoreList\":[{\"approvalOrder\":1,\"approverEmpName\":\"直属主管\",\"approverInfo\":\"1\",\"scene\":\"superior_score\",\"superiorScoreWeight\":100,\"approverType\":\"manager\"}],\"peerScoreRule\":\"item\",\"selfScoreRule\":\"item\",\"scoreSummarySwitch\":0,\"peerScoreFlag\":\"false\",\"subScoreFlag\":\"false\"},\"status\":\"published\"}", StdTemp.class);
        Assert.assertTrue(expBean.getName().equals(synthTemp.getName()));
        Assert.assertEquals("", new Integer(2), synthTemp.getKpiTypes().get(0).getScoreOptType());

        //中高层管理员示例模板
        StdTemp adminTemp = StdTemp.initSynthTemp(companyId, opEmpId, "【示例】中高层管理员考核模板", "weightScore", PerfTemplKpiType::initAdminExampleKpiType);
        Assert.assertEquals("", new Integer(2), adminTemp.getKpiTypes().get(0).getScoreOptType());
        System.out.println(JSONObject.toJSONString(adminTemp));

        //销售经理考核模板
        StdTemp salesTemp = StdTemp.initSynthTemp(companyId, opEmpId, "【示例】销售经理考核模板", "weightScore", PerfTemplKpiType::initSalesExampleKpiType);
        System.out.println(JSONObject.toJSONString(salesTemp));
        Assert.assertEquals("", new Integer(2), salesTemp.getKpiTypes().get(0).getScoreOptType());

    }
}