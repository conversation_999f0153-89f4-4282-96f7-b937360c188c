package com.polaris.kpi.ExternalData;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.polaris.kpi.extData.domain.type.ApiParamAnalysis;
import com.polaris.kpi.extData.domain.type.HttpClient;
import com.polaris.kpi.extData.domain.type.ParamAnalysis;
import com.polaris.kpi.extData.domain.type.encAndDec.Aes;
import com.polaris.kpi.extData.domain.type.encAndDec.Md5;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;


/**
 * @Author: xuxw
 * @Date: 2025/02/24 19:15
 * @Description:
 */
public class ExternalDataTest {


    @Test
    public void aesDmSvcTest() throws Exception {
        Aes aes = new Aes();
//        String xxww112345 = aesDmSvc.enc("xxww112345");
//        System.out.println("xxww112345 = " + xxww112345);
//        String dec = aesDmSvc.dec(xxww112345);
//        System.out.println("dec = " + dec);

//        RsaDmSvc rsaDmSvc = new RsaDmSvc();
//        String xxww112345 = rsaDmSvc.enc("xxww112345");
//        System.out.println("xxww112345 = " + xxww112345);
//        String dec = rsaDmSvc.dec(xxww112345);
//        System.out.println("dec = " + dec);
        String xxww112345 = new Md5().enc("xxww112345");
        System.out.println("xxww112345 = " + xxww112345);
    }


    @Test
    public void testSignParamAnalysis() throws Exception {
        String param1 = "{\n" +
                "\"data\":{\n" +
                "\"info\":{\n" +
                "\"appKey\":\"112233\"\n" +
                "}\n" +
                "}\n" +
                "}";
        String param2 = "secert1122";
        Map<String, String> param = new HashMap<>();
        param.put("@1", param1);
        param.put("@2", param2);

//        SignParamAnalysisDmSvc dmSvc = new SignParamAnalysisDmSvc();
//        String s = dmSvc.signParamAnalysis("xxw+&beijixing+&{timestamp}", param);
//        Md5DmSvc md5DmSvc = new Md5DmSvc();
//        String enc = md5DmSvc.enc(s);
//        System.out.println("s = " + s);
//        System.out.println("s = " + enc);

        ApiParamAnalysis dmSvc = new ApiParamAnalysis();
        Map<String, String> stringStringMap = dmSvc.apiParamAnalysis("key1=zhangsan&key2=112233", null);
        System.out.println("stringStringMap = " + stringStringMap);
    }


    @Test
    public void getToken(){
        HashMap<String, String> param = new HashMap<>();
        param.put("taskId","1680904");
        HttpClient httpClient = new HttpClient();
        String s = httpClient.doPost("http://kpiuat.pekhr.com:8084/v2/api/admin/eval/getAdminTaskCopyRecord", param,
                "Token","kt0OLAlSSBQxYDfKdyw17ex4uKfykKWH+u3Rcrs0X44LWwc80TjoNlRDOSbvZ1m6Mjt84NvwduS9rUciTjFxjBxHw54sDO9p++Hatlm6dCM=");
        System.out.println("s = " + s);
    }

    @Test
    public void testXmlToJson(){
        String xml = "<root>\n" +
                "    <items>\n" +
                "        <item>\n" +
                "            <name>Apple</name>\n" +
                "            <price>1.5</price>\n" +
                "        </item>\n" +
                "        <item>\n" +
                "            <name>Banana</name>\n" +
                "            <price>0.8</price>\n" +
                "        </item>\n" +
                "    </items>\n" +
                "</root>";
        ParamAnalysis paramAnalysis = new ParamAnalysis();
        JSONObject jsonObject = paramAnalysis.transferParam(xml, "xml");
        System.out.println("jsonObject = " + jsonObject);
    }

    @Test
    public void testJson(){
        String json = "{\n" +
                "\"externalDataSystemId\":\"10301\",\n" +
                "\"name\":\"考勤\",\n" +
                "\"classifyId\":\"12198618-dbe8-4a1c-bf24-42192b56534c\",\n" +
                "\"uniqueKey\":\"data.xx.xxw\",\n" +
                "\"interfaceUrl\":\"https://xx/xxxx/xxxx\",\n" +
                "\"requestType\":\"POST\",\n" +
                "\"parameterType\":\"form-data\",\n" +
                "\"parameterValue\":\"userId=123456&companyId=88998877\",\n" +
                "\"matchParams\":[\n" +
                "{\"paramName\":\"手机号\",\n" +
                "\"paramType\":1,\n" +
                "\"accessMode\":\"data.xx.xxx\"\n" +
                "},\n" +
                "{\n" +
                "\"paramName\":\"工号\",\n" +
                "\"paramType\":1,\n" +
                "\"accessMode\":\"data.xx.xxx\"\n" +
                "}\n" +
                "],\n" +
                "\"interfaceParams\":[\n" +
                "{\"paramName\":\"数据时间\",\n" +
                "\"paramType\":2,\n" +
                "\"accessMode\":\"data.xx.xxx\"\n" +
                "},\n" +
                "{\n" +
                "\"paramName\":\"缺勤次数\",\n" +
                "\"paramType\":2,\n" +
                "\"accessMode\":\"data.xx.xxx\"\n" +
                "}\n" +
                "]\n" +
                "}";
//        ParamAnalysisUtil.transferParam(json, "json");
    }

    @Test
    public void testTransferJson(){
        String jsonString = "{\n" +
                "\"data\":{\n" +
                "\"students\":[\n" +
                "{\n" +
                "\"name\":\"bobo\",\n" +
                "\"age\":20\n" +
                "},\n" +
                "{\n" +
                "\"name\":\"Alice\",\n" +
                "\"age\":21\n" +
                "}\n" +
                "]\n" +
                "}\n" +
                "}";
        JSONObject jsonData = JSON.parseObject(jsonString);

//        // 动态路径配置
        String[] paths = {"data.students[].name", "data.students[].age"};

        // 提取值并建立对应关系
        ParamAnalysis paramAnalysis = new ParamAnalysis();
//        List<Map<String, Object>> result = paramAnalysis.apiResParamAnal(jsonData,"data.students[]", Arrays.asList("name", "age"));
//        for (Map<String, Object> map : result) {
//            System.out.println("Name: " + map.get("name") +
//                    ", Age: " + map.get("age"));
//        }

    }

    @Test
    public void apiParamAnalysis(){

        ApiParamAnalysis dmSvc = new ApiParamAnalysis();
        Map<String, String> map = new HashMap<>();
        String s = "{\n" +
                "\"data\":{\n" +
                "\"info\":{\n" +
                "\"token\":\"112233\"\n" +
                "}\n" +
                "}\n" +
                "}";
        map.put("@2",s);
        Map<String, String> stringStringMap = dmSvc.apiParamAnalysis("username=zhangsan&key2={@2.data.info.token}", map);
        System.out.println("stringStringMap = " + stringStringMap);
        ParamAnalysis paramAnalysis = new ParamAnalysis();
        String jsonString = paramAnalysis.getJsonString(s, "data.info.token");
        System.out.println("jsonString = " + jsonString);
    }

}
