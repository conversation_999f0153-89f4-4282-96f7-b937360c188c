package com.polaris.kpi.eval.domain.task.dmsvc;

import cn.hutool.core.collection.CollUtil;
import com.polaris.kpi.eval.domain.task.dmsvc.migration.ScorerDataMingrationDmSvc;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.sdk.common.JsonFileTool;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.List;
import java.util.Objects;

public class ScorerDataMingrationDmSvcTest {

    @Test
    @DisplayName("解析处理考核任务已完成的")
    public void handlerTaskFinished() {
        List<EvalScoreResult> resultList = JsonFileTool.toList("com.polaris.kpi.eval.domain.task.dmsvc/ScorerDataMingrationDmSvcTest/taskFinished/taskFinished/evalScoreResults.json", EvalScoreResult.class);
        EmpEvalMerge evalMerge = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.dmsvc/ScorerDataMingrationDmSvcTest/taskFinished/taskFinished/evalMerge.json", EmpEvalMerge.class);
        EvalUser evalUser = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.dmsvc/ScorerDataMingrationDmSvcTest/taskFinished/taskFinished/evalUser.json", EvalUser.class);
        ScorerDataMingrationDmSvc dataMingrationDmSvc = new ScorerDataMingrationDmSvc(evalUser, evalMerge,resultList,null,null,null,null);
        dataMingrationDmSvc.handlerTaskFinished();
        Assert.assertTrue(CollUtil.isNotEmpty(dataMingrationDmSvc.getEmpEvalScorers()));
        Assert.assertEquals(2, dataMingrationDmSvc.getEmpEvalScorers().size());
        Assert.assertTrue(CollUtil.isNotEmpty(dataMingrationDmSvc.getEmpEvalScorers().get(0).getScorerNodes()));
        Assert.assertEquals(2, dataMingrationDmSvc.getEmpEvalScorers().size());
        Assert.assertEquals(1, dataMingrationDmSvc.getEmpEvalScorers().get(0).getScorerNodes().size());
        Assert.assertTrue(CollUtil.isNotEmpty(dataMingrationDmSvc.getEmpEvalScorers().get(1).getScorerNodes()));
        Assert.assertEquals(1, dataMingrationDmSvc.getEmpEvalScorers().get(1).getScorerNodes().size());
    }

    @Test
    @DisplayName("不跳过驳回")
    public void handlerTaskNoFinished() {
        List<EvalScoreResult> resultList = JsonFileTool.toList("com.polaris.kpi.eval.domain.task.dmsvc/taskUserDao.listResultByScoreTypes2.json", EvalScoreResult.class);
        RejectConfirmDmSvc confirmDmSvc = new RejectConfirmDmSvc(resultList);
        Assert.assertTrue(confirmDmSvc.needReject());
        Assert.assertTrue(Objects.equals(confirmDmSvc.getOrderLevel(),3));
        Assert.assertTrue(Objects.equals(confirmDmSvc.getUpLevel(),2));
    }

}