package com.polaris.kpi.eval.domain.task.dmsvc;

import cn.com.polaris.kpi.eval.ResultAuditReviewers;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.entity.EvalAudit;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.setting.domain.entity.*;
import com.polaris.kpi.setting.domain.repo.ResultAuditFlowRepo;
import com.polaris.sdk.common.JsonFileTool;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class SkipResultAuditDmSvcTest {

    public static String companyId = "5a031297-1b38-48ae-bc82-375849835203";

    public static String taskUserId = "1280601";

    @Autowired
    private ResultAuditFlowRepo resultAuditFlowRepo = new ResultAuditFlowRepo() {
        @Override
        public void saveAuditFlow(ResultAuditFlow flow, EvalUser evalUser) {

        }

        @Override
        public void fixSaveAuditFlow(ResultAuditFlow flow, EvalUser evalUser) {

        }

        @Override
        public void updateAuditNodeRater(String companyId, String nodeRaterId, String auditEmpId, Integer level, Integer status) {

        }

        @Override
        public void refreshAuditFlow(ResultAuditFlow flow, EvalUser evalUser) {

        }

        @Override
        public void refreshAuditFlow(String companyId, String opEmpId, List<EvalUser> evalUsers) {

        }

        @Override
        public void saveAuditFlowV2(EvalUser evalUser, List<ResultAuditFlowUser> flowUsers) {

        }

        @Override
        public void updateAuditFlow(ResultAuditFlow flow, Integer node) {

        }

        @Override
        public void updateOldAuditFlow(ResultAuditFlow flow) {

        }

        @Override
        public void deleteAllFlow(String companyId,List<String> taskUserId) {

        }

        @Override
        public void deleteAllFlowV2(String companyId, String taskId) {

        }

        @Override
        public void rejectAuditFlow(List<String> taskUserIds, Integer orderNode) {

        }

        @Override
        public ResultAuditFlow getResultAuditFlow(String companyId, String taskId, List<String> clearTaskUserIds, boolean isRefresh, List<EvalScoreResult> curLevelRs) {
            return null;
        }

        @Override
        public ResultAuditFlow getResultAuditFlowV2(String companyId, String taskId, List<String> clearTaskUserIds) {
            ResultAuditFlow flow = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.dmsvc/getResultAuditFlowV2.json", ResultAuditFlow.class);
            return flow;
        }

        @Override
        public ResultAuditFlow getResultAuditFlowByUserId(String companyId, String taskUserId) {
            ResultAuditFlow flow = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.dmsvc/getResultAuditFlowByUserId.json", ResultAuditFlow.class);
            return flow;
        }


        @Override
        public List<ResultAuditFlowUser> listFlowUserByTaskId(String companyId, String taskId) {
            List<ResultAuditFlowUser> flowUsers = JsonFileTool.toList("com.polaris.kpi.eval.domain.task.dmsvc/listFlowUserByTaskId.flowUsers.json", ResultAuditFlowUser.class);
            return flowUsers;
        }


        @Override
        public TaskResultAuditSummary getSummary(String companyId, String taskId, String adminEmpId, Integer level) {
            return null;
        }

        @Override
        public List<TaskResultAuditSummary> listSummaryByTaskId(String companyId, String taskId) {
            return null;
        }

        @Override
        public List<TaskResultAuditSummary> listSummaryByTaskId(String companyId, String taskId, String auditEmpId) {
            return null;
        }

        @Override
        public void saveTaskResultAuditSummary(List<TaskResultAuditSummary> taskResultAuditSummarys) {

        }

        @Override
        public int updateSummary(TaskResultAuditSummary auditSummary) {
            return 0;
        }

        @Override
        public void delTaskResultAuditSummary(String companyId, String taskId) {

        }

        @Override
        public void removeResultAuditFlow(String companyId, String taskId, String taskUserId) {

        }

        @Override
        public void refreshSummary(String companyId, String taskId) {

        }

        @Override
        public void rejectRefreshSummary(String companyId, String taskId) {

        }

        @Override
        public ListWrap<ResultAuditReviewers> reviewersListWrap(TenantId companyId, List<String> taskUserIds) {
            return null;
        }

        @Override
        public Map<String, TaskResultAuditSummary> listByEmpIdSummaryAsMap(String companyId, List<String> taskIds, String adminEmpId) {
            return null;
        }

        @Override
        public void addResultSummary(String companyId, EvalAudit evalAudit) {

        }
    };


    @Test
    public void startSkip() {
        Integer nodeOrder = 5;
        ResultAuditFlow auditFlow = resultAuditFlowRepo.getResultAuditFlowByUserId(companyId, taskUserId);
        //获取当前考核人的实例
        ResultAuditFlowInstance instance = auditFlow.getCurInstance();
        //启动流程实例
        instance.start(nodeOrder);
        List<ResultAuditFlowNode> skipedNodes = instance.getSkipedNodes();
//        if (CollUtil.isEmpty(skipedNodes)) {
//            return;
//        }
        boolean isEnd = nodeOrder + skipedNodes.size() > instance.getFlowNodes().size();
        Assert.assertTrue(!isEnd);
        Integer curLevel = instance.getCurFlowNode().getApprovalOrder();
        List<String> recEmpId = instance.getCurFlowNode().getNodeRaters().stream()
                .filter(r -> !r.needSkip()).map(ResultAuditFlowNodeRater::getAuditEmpId).collect(Collectors.toList());
        Assert.assertTrue(Objects.equals(curLevel,4));
        Assert.assertTrue(Objects.equals(JSONUtil.toJsonStr(auditFlow.getCurInstance().getFlowNodes()),JSONUtil.toJsonStr(JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.dmsvc/getResultAuditFlowByUserId_result.json", ResultAuditFlow.class).getCurInstance().getFlowNodes())));
        Assert.assertTrue(Objects.equals(JSONUtil.toJsonStr(recEmpId),"[\"1602582\"]"));
    }

}