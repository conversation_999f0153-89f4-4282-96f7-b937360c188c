package com.polaris.kpi.eval.domain.task.entity.empeval;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.perf.www.common.constant.BusinessConstant;
import com.polaris.kpi.eval.domain.task.entity.EvalKpi;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.TypeWeightConf;
import com.polaris.sdk.common.JsonFileTool;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


class KpiListWrapTest {

    @Test
    void orFinishedScorer() {
        KpiListWrap wrap = new KpiListWrap();
        EmpEvalKpiType type = new EmpEvalKpiType();
        ArrayList<PerfEvalTypeResult> typeRs = new ArrayList<>();
        PerfEvalTypeResult tRs = tRs("1001", "pass");
        typeRs.add(tRs);
        type.setWaitScoresOld(typeRs);
        List<EvalKpi> items = new ArrayList<>();
        EvalKpi item = new EvalKpi();
        items.add(item);
        List<EvalScoreResult> scores = new ArrayList<>();
        scores.add(rs("1001", "item1001", "pass"));
        item.setWaitScoresOld(scores);

        scores.add(rs("1002", "item1001", "pass"));
        scores.add(rs("1003", "item1001", ""));


        type.setItems(items);
        EvalKpi item2 = new EvalKpi();
        items.add(item2);
        List<EvalScoreResult> scores2 = new ArrayList<>();
        scores2.add(rs("1002", "item1002", "pass"));
        scores2.add(rs("1001", "item1002", "pass"));
        item2.setWaitScoresOld(scores2);


        EvalKpi item3 = new EvalKpi();
        items.add(item3);
        List<EvalScoreResult> scores3 = new ArrayList<>();
        scores3.add(rs("1003", "item1004", ""));
        item3.setWaitScoresOld(scores3);

        wrap.add(type);

        List<String> passed = wrap.orFinishedScorer(Arrays.asList("1001", "1002", "1003"));
        System.out.println(passed);
        Assert.assertTrue(StrUtil.equals("[1002, 1001]", passed.toString()));


        List<String> passed2 = wrap.orFinishedScorer(Arrays.asList("1001", "1003"));
        Assert.assertTrue(passed2.toString().equals("[1001]"));
        System.out.println(passed2);
    }

    @NotNull
    private static EvalScoreResult rs(String scorerId, String itemId, String status) {
        EvalScoreResult rs = new EvalScoreResult();
        rs.setScorerId(scorerId);
        rs.setKpiItemId(itemId);
        rs.setAuditStatus(status);
        return rs;
    }


    @NotNull
    private static PerfEvalTypeResult tRs(String scorerId, String status) {
        PerfEvalTypeResult rs = new PerfEvalTypeResult();
        rs.setScorerId(scorerId);
        rs.setAuditStatus(status);
        return rs;
    }

    @Test
    @DisplayName("打总分-加分一票维度-指标评等级-不覆盖")
    void sumTotalWeight() {
        KpiListWrap kpiListWrap = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.entity.empeval/KpiListWrapTest/sumTotalWeight.json", KpiListWrap.class);
        String json = JsonFileTool.readString("com.polaris.kpi.eval.domain.task.entity.empeval/KpiListWrapTest/submitedCmd.json");
        JSONObject jsonObject = JSONUtil.parseObj(json);
        List<EvalScoreResult> itemScoreList = jsonObject.getJSONArray("itemScoreList").toList(EvalScoreResult.class);
        EvalScoreResult itemScore = itemScoreList.get(0);
        EvalScoreResult total = new EvalScoreResult(itemScore.getCompanyId(), itemScore.getTaskId(), "orgId", itemScore.getEmpId(), "opEMpId");
        total.setTaskUserId(itemScore.getTaskUserId());
        total.setScorerType("total_superior_score");
        total.setScorerId(itemScore.getScorerId());
        total.setScore(new BigDecimal(98));
        total.setScoreComment("totalComment");
        total.setSignatureUrl("signatureUrl");
        //total.setScoreAttUrl(totalAttUrl);
        total.setAuditStatus(BusinessConstant.PASS);
        kpiListWrap.initWeight(new TypeWeightConf(1, 1, new BigDecimal(100)), false);//归一化类别权重

        List<EvalScoreResult> itemOntotals = kpiListWrap.sumTotalWeight(itemScoreList, total, false);
        Assert.assertTrue(itemOntotals.size() == 2);
        for (EvalScoreResult matchTotal : itemOntotals) {
            Assert.assertTrue(matchTotal.getKpiTypeId().equals("d7869ad5-4e24-11ef-97ec-000c29165335"));
            Assert.assertTrue(matchTotal.getScore() != null);
            System.out.println(JSONUtil.toJsonStr(matchTotal));
        }
    }

    @Test
    @DisplayName("上级评分打总分-维度不评-指标评等级-不覆盖")
    void submitSuperScore() {
        KpiListWrap kpiListWrap = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.entity.empeval/KpiListWrapTest/kpiItems.json", KpiListWrap.class);
        String json = JsonFileTool.readString("com.polaris.kpi.eval.domain.task.entity.empeval/KpiListWrapTest/submitedSupperCmd.json");
        JSONObject jsonObject = JSONUtil.parseObj(json);
        List<EvalScoreResult> itemScoreList = jsonObject.getJSONArray("itemScoreList").toList(EvalScoreResult.class);
        EvalScoreResult itemScore = itemScoreList.get(0);
        EvalScoreResult total = new EvalScoreResult(itemScore.getCompanyId(), itemScore.getTaskId(), "orgId", itemScore.getEmpId(), "opEMpId");
        total.setTaskUserId(itemScore.getTaskUserId());
        total.setScorerType("total_superior_score");
        total.setScorerId(itemScore.getScorerId());
        total.setScore(new BigDecimal(10));
        total.setScoreComment("totalComment");
        total.setSignatureUrl("signatureUrl");
        //total.setScoreAttUrl(totalAttUrl);
        total.setAuditStatus(BusinessConstant.PASS);
        kpiListWrap.initWeight(new TypeWeightConf(1, 1, new BigDecimal(100)), false);//归一化类别权重

        List<EvalScoreResult> itemOntotals = kpiListWrap.sumTotalWeight(itemScoreList, total, false);
        Assert.assertTrue(itemOntotals.size() == 2);
        for (EvalScoreResult matchTotal : itemOntotals) {
            Assert.assertTrue(!matchTotal.getKpiTypeId().equals("d849e120-8dd2-4363-8001-88c74ed75b59"));
            Assert.assertTrue(matchTotal.getScore() != null);
            System.out.println(JSONUtil.toJsonStr(matchTotal));
        }
    }
}