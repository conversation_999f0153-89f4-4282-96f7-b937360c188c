package com.polaris.kpi.eval.domain.task.dmsvc;

import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import com.polaris.kpi.eval.domain.task.dmsvc.score.SkipScorerDmSvc;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalRule;
import com.polaris.kpi.eval.domain.task.entity.empeval.EvalScorersWrap;
import com.polaris.sdk.common.JsonFileTool;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.ArrayList;
import java.util.List;


public class ExplainEvalScorerDmSvcTest {
    public static TenantId companyId = new TenantId("5a031297-1b38-48ae-bc82-375849835203");
    public static String taskUserId = "1280601";
    public static String opEmpId = "1302001";

    @Test
    @DisplayName("【自定义流程-自评+指定评【多层级】】解析评价关系")
    public void explainEvalScorer() {
        EvalUser taskUser = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.dmsvc/ExplainEvalScorerDmSvcTest/EvalUser.json", EvalUser.class);
        EmpEvalRule rule  = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.dmsvc/ExplainEvalScorerDmSvcTest/Rule.json", EmpEvalRule.class);
        ExplainEvalScorerDmSvc dmSvc = new ExplainEvalScorerDmSvc(taskUser, rule, companyId, opEmpId);
        dmSvc.explainEvalScorer();
        System.out.println(dmSvc.getScorers().getDatas());

        EvalScorersWrap scorers = dmSvc.getScorers();
        List<EmpEvalScorer> appointScore = new ArrayList<>();
        for(EmpEvalScorer scorer:scorers.getDatas()){
            for(EmpEvalScorerNode node:scorer.getScorerNodes()){
                if(SubScoreNodeEnum.isAppointScore(node.getScorerType())){
                    appointScore.add(scorer);
                    break;
                }
            }
        }
        System.out.println("定向评价人数：" + appointScore.size());
        Assert.assertEquals(2, appointScore.size());
    }
}
