package com.polaris.kpi.open.domain.entity;

import cn.hutool.crypto.digest.MD5;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Date;

import static org.junit.Assert.*;

public class AuthInfoTest {
    //
    //"corpId": "dingd2a976881f424c2da1320dcb25e91351",
    //        "companyId": "7c928ecd-05d0-42b3-9ce3-a1934bffd9f5",
    //        "companyName": "数犀专属钉钉样板",
    //        "contact": "",
    //        "accessKey": "da1320dcb",
    //        "accessSecret": "d2a976881f424c2da1320dcb25e91351",
    @Test
    public void getSignature() {
        AuthInfo authInfo = new AuthInfo("dingd2a976881f424c2da1320dcb25e91351","xx","xx","","da1320dcb","d2a976881f424c2da1320dcb25e91351",
                new Date(),new Date(),1000L,100L,new ArrayList<>(),new ArrayList<>());
        MD5 md5 = MD5.create();
        boolean b = authInfo.verifySignature("4cbc5b5ee3d9b84582f5f3abfba497eb", 1686797922L);
        System.out.println(b);
    }

    @Test
    public void name() {
        System.out.println(new Date(1686797922L).toString());
    }
}