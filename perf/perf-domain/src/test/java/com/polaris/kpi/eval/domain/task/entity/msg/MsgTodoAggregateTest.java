package com.polaris.kpi.eval.domain.task.entity.msg;

import cn.com.polaris.kpi.eval.Name;
import com.perf.www.common.em.CompanyMsgActionEnum;
import cn.com.polaris.kpi.company.MsgSceneEnum;
import com.polaris.sdk.type.TenantId;
import junit.framework.TestCase;

public class MsgTodoAggregateTest extends TestCase {
//    public void testInstance() {
//        MsgTodoAggregate.host = "xxxx";
//        MsgTodoAggregate.TODO_URL_PREFIX = "TODO_URL_PREFIX";
//
//        MsgTodoAggregate msgTodoAggregate = new MsgTodoAggregate(new TenantId("11"), "taskId", new Name("getTaskName"), "empId", "taskUser.getId()")
//                .useScene(MsgSceneEnum.TASK_SELF_SCORE, CompanyMsgActionEnum.SELF_SCORE)
//                .sendExtTodo().sendExtMsg().addRecEmpId("empId");
//        MsgTodoAggregate build = msgTodoAggregate.build();
//        for (MsgTodoAggregate.FormItem formItem : build.getFormItems()) {
//            System.out.println(formItem.getTitle());
//        }
//
//    }

}
