package com.polaris.kpi.eval.domain.task.entity.admineval.strategy;

import com.polaris.kpi.eval.domain.task.entity.admineval.EmpEvalChangeLoader;
import com.polaris.kpi.eval.domain.task.entity.admineval.EmpEvalChangeResult;
import com.polaris.kpi.eval.domain.task.entity.admineval.strategy.impl.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.KpiListWrap;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 考核阶段变更处理器测试类
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@RunWith(PowerMockRunner.class)
public class EvalStageChangeProcessorTest {

    @Mock
    private EmpEvalChangeLoader mockLoader;

    private EvalStageChangeProcessor processor;
    private EmpEvalChangeResult changeResult;

    @Before
    public void setUp() {
        // 创建所有策略实例
        List<EvalStageChangeStrategy> strategies = Arrays.asList(
            new ConfirmingStageStrategy(),
            new InputFinishValueStageStrategy(),
            new ExecuteChangeStageStrategy(),
            new FinishValueAuditStageStrategy(),
            new ScoringStageStrategy(),
            new ResultsAuditingStageStrategy(),
            new ResultsInterviewStageStrategy(),
            new ResultsAppealStageStrategy()
        );
        
        processor = new EvalStageChangeProcessor(strategies);
        
        // 创建真实的变更结果对象用于测试
        changeResult = new EmpEvalChangeResult();
        changeResult.setEmpId("emp001");
        changeResult.setEmpName("测试员工");
        changeResult.setTaskStatus(TalentStatus.SCORING.getStatus()); // 设置为评分阶段
        
        // 初始化必要的字段
        changeResult.setKpiTypes(new KpiListWrap());
        changeResult.setStageChanges(new java.util.ArrayList<>());
        changeResult.setScoreChanges(new java.util.ArrayList<>());
        changeResult.setChangeStageType(0);
    }
    
    @Test
    public void testProcessorInitialization() {
        // 验证处理器初始化
        assertNotNull(processor);
        assertEquals(8, processor.getStrategies().size());

        // 验证策略按优先级排序
        List<EvalStageChangeStrategy> strategies = processor.getStrategies();
        for (int i = 0; i < strategies.size() - 1; i++) {
            assertTrue("策略应该按优先级排序",
                      strategies.get(i).getPriority() <= strategies.get(i + 1).getPriority());
        }
    }

    @Test
    public void testProcessAllStagesWithNullParameters() {
        // 测试空参数处理 - 不应该抛出异常
        try {
            processor.processAllStages(null, mockLoader);
            processor.processAllStages(changeResult, null);
            processor.processAllStages(null, null);
        } catch (Exception e) {
            fail("不应该抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testProcessAllStagesNormalFlow() {
        // 模拟加载器的基本方法
        when(mockLoader.getCompanyId()).thenReturn("company001");

        // 执行处理 - 不应该抛出异常
        try {
            processor.processAllStages(changeResult, mockLoader);
        } catch (Exception e) {
            fail("不应该抛出异常: " + e.getMessage());
        }

        // 验证基本信息
        assertEquals("emp001", changeResult.getEmpId());
        assertEquals("测试员工", changeResult.getEmpName());
    }

    @Test
    public void testGetStrategyByStageType() {
        // 测试根据阶段类型获取策略
        EvalStageChangeStrategy strategy = processor.getStrategyByStageType(TalentStatus.CONFIRMING);
        assertNotNull(strategy);
        assertTrue(strategy instanceof ConfirmingStageStrategy);

        strategy = processor.getStrategyByStageType(TalentStatus.SCORING);
        assertNotNull(strategy);
        assertTrue(strategy instanceof ScoringStageStrategy);

        // 测试不存在的阶段类型
        strategy = processor.getStrategyByStageType(TalentStatus.FINISHED);
        assertNull(strategy);
    }

    @Test
    public void testStrategyPriorities() {
        // 验证各策略的优先级设置正确
        List<EvalStageChangeStrategy> strategies = processor.getStrategies();

        // 指标确认应该是第一个
        assertEquals(ConfirmingStageStrategy.class, strategies.get(0).getClass());
        assertEquals(10, strategies.get(0).getPriority());

        // 结果申述应该是最后一个
        assertEquals(ResultsAppealStageStrategy.class, strategies.get(strategies.size() - 1).getClass());
        assertEquals(80, strategies.get(strategies.size() - 1).getPriority());
    }

    @Test
    public void testStrategyDescriptions() {
        // 验证策略描述
        List<EvalStageChangeStrategy> strategies = processor.getStrategies();

        for (EvalStageChangeStrategy strategy : strategies) {
            assertNotNull(strategy.getDescription());
            assertFalse(strategy.getDescription().isEmpty());
            assertNotNull(strategy.getStageType());
            assertNotNull(strategy.getChangeFlag());
        }
    }
}
