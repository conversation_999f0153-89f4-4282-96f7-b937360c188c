package com.polaris.kpi.eval.domain.task.dmsvc;

import cn.com.polaris.kpi.KpiEmp;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.dmsvc.score.TransferScorerV3DmSvc;
import com.polaris.kpi.eval.domain.task.entity.EmpEvalScorer;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.empeval.EvalScorersWrap;
import com.polaris.kpi.eval.domain.task.entity.empeval.score.TransferScorer;
import com.polaris.sdk.common.JsonFileTool;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class TransferScorerDmSvcTest {
    public static TenantId companyId = new TenantId("5a031297-1b38-48ae-bc82-375849835203");
    public static String taskUserId = "1280601";


    @Test
    @DisplayName("转交接收的人不存在转出的评价节点")
    public void doTransferScoreV3Test1() {
        EvalUser user = new EvalUser();
        List<EmpEvalScorer> scorers = JsonFileTool.toList("com.polaris.kpi.eval.domain.task.dmsvc/TransferScorerDmSvcTest/EmpEvalScorer.json", EmpEvalScorer.class);
        for(EmpEvalScorer scorer:scorers){
            scorer.setCompanyId(companyId);
        }
        EvalScorersWrap scorersWrap = new EvalScorersWrap(scorers);
        TransferScorer ts = new TransferScorer();
        ts.setScorersWrap(scorersWrap);
        ts.setEvalUser(user);

        List<KpiEmp> kpiEmps = new ArrayList<>();
        kpiEmps.add(new KpiEmp("1602589","杨思威","https://static-legacy.dingtalk.com/media/lADPM5HikoKm7UnNAWPNAUQ_324_355.jpg"));
        kpiEmps.add(new KpiEmp("1302001","苏小秋","https://static-legacy.dingtalk.com/media/lADPD26eO1kRFzrNAbDNAbA_432_432.jpg"));
        ListWrap<KpiEmp> raterGroup = new ListWrap<>(kpiEmps).asMap(KpiEmp::getEmpId);
        ts.setRaterGroup(raterGroup);
        TransferScorerV3DmSvc dmSvc = new TransferScorerV3DmSvc(companyId,"1302001",null,
                null,null,null,null,null,null);
        dmSvc.doTransferScoreV3("1302001","1602589","admin");
        System.out.println(JSONUtil.toJsonStr(dmSvc.listNeedSaveEmpEvalScorer()));
    }



    @Test
    @DisplayName("转交接收的人存在转出的评价节点")
    public void doTransferScoreV3Test2() {
        EvalUser user = new EvalUser();
        List<EmpEvalScorer> scorers = JsonFileTool.toList("com.polaris.kpi.eval.domain.task.dmsvc/TransferScorerDmSvcTest/EmpEvalScorer2.json", EmpEvalScorer.class);
        for(EmpEvalScorer scorer:scorers){
            scorer.setCompanyId(companyId);
        }
        EvalScorersWrap scorersWrap = new EvalScorersWrap(scorers);
        TransferScorer ts = new TransferScorer();
        ts.setScorersWrap(scorersWrap);
        ts.setEvalUser(user);

        List<KpiEmp> kpiEmps = new ArrayList<>();
        kpiEmps.add(new KpiEmp("1302001","苏小秋","https://static-legacy.dingtalk.com/media/lADPD26eO1kRFzrNAbDNAbA_432_432.jpg"));
        kpiEmps.add(new KpiEmp("1441002","何小虎","https://static-legacy.dingtalk.com/media/lADPD26eO1kRFzrNAbDNAbA_432_432.jpg"));
        ListWrap<KpiEmp> raterGroup = new ListWrap<>(kpiEmps).asMap(KpiEmp::getEmpId);
        ts.setRaterGroup(raterGroup);
        TransferScorerV3DmSvc dmSvc = new TransferScorerV3DmSvc(companyId,"1302001",null,
                null,null,null,null,null,null);
        dmSvc.doTransferScoreV3("1302001","1441002","admin");
        System.out.println(JSONUtil.toJsonStr(dmSvc.listNeedSaveEmpEvalScorer()));
    }

    @Test
    @DisplayName("转交接收的人已评转出的评价节点")
    public void doTransferScoreV3Test3() {
        EvalUser user = new EvalUser();
        List<EmpEvalScorer> scorers = JsonFileTool.toList("com.polaris.kpi.eval.domain.task.dmsvc/TransferScorerDmSvcTest/EmpEvalScorer4.json", EmpEvalScorer.class);
        for(EmpEvalScorer scorer:scorers){
            scorer.setCompanyId(companyId);
        }
        EvalScorersWrap scorersWrap = new EvalScorersWrap(scorers);
        TransferScorer ts = new TransferScorer();
        ts.setScorersWrap(scorersWrap);
        ts.setEvalUser(user);
        List<KpiEmp> kpiEmps = new ArrayList<>();
        kpiEmps.add(new KpiEmp("1302001","苏小秋","https://static-legacy.dingtalk.com/media/lADPD26eO1kRFzrNAbDNAbA_432_432.jpg"));
        kpiEmps.add(new KpiEmp("1441002","何小虎","https://static-legacy.dingtalk.com/media/lADPD26eO1kRFzrNAbDNAbA_432_432.jpg"));
        ListWrap<KpiEmp> raterGroup = new ListWrap<>(kpiEmps).asMap(KpiEmp::getEmpId);
        ts.setRaterGroup(raterGroup);
        TransferScorerV3DmSvc dmSvc = new TransferScorerV3DmSvc(companyId,"1302001",null,
                null,null,null,null,null,null);
        dmSvc.doTransferScoreV3("1441002","1302001","admin");
        System.out.println(JSONUtil.toJsonStr(dmSvc.listNeedSaveEmpEvalScorer()));
    }

    @Test
    @DisplayName("转交接收的人未评转出的评价节点")
    public void doTransferScoreV3Test4() {
        EvalUser user = new EvalUser();
        List<EmpEvalScorer> scorers = JsonFileTool.toList("com.polaris.kpi.eval.domain.task.dmsvc/TransferScorerDmSvcTest/EmpEvalScorer3.json", EmpEvalScorer.class);
        for(EmpEvalScorer scorer:scorers){
            scorer.setCompanyId(companyId);
        }
        EvalScorersWrap scorersWrap = new EvalScorersWrap(scorers);
        TransferScorer ts = new TransferScorer();
        ts.setScorersWrap(scorersWrap);
        ts.setEvalUser(user);

        List<KpiEmp> kpiEmps = new ArrayList<>();
        kpiEmps.add(new KpiEmp("1302001","苏小秋","https://static-legacy.dingtalk.com/media/lADPD26eO1kRFzrNAbDNAbA_432_432.jpg"));
        kpiEmps.add(new KpiEmp("1440001","徐小伟","https://static-legacy.dingtalk.com/media/lADPD26eO1kRFzrNAbDNAbA_432_432.jpg"));
        ListWrap<KpiEmp> raterGroup = new ListWrap<>(kpiEmps).asMap(KpiEmp::getEmpId);
        ts.setRaterGroup(raterGroup);
        TransferScorerV3DmSvc dmSvc = new TransferScorerV3DmSvc(companyId,"1302001",null,
                null,null,null,null,null,null);
        dmSvc.doTransferScoreV3("1440001","1302001","admin");
        System.out.println(JSONUtil.toJsonStr(dmSvc.listNeedSaveEmpEvalScorer()));
    }
}
