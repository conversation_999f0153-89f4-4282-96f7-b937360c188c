package com.polaris.kpi.eval.domain.task.entity.empeval;

import cn.com.polaris.kpi.eval.FinishValue;
import cn.hutool.json.JSONUtil;
import org.junit.Test;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/30 14:23
 */
public class FinishValueReqValidationSvcTest {
    @Test
    public void check() throws IllegalAccessException {
        String jsonStr = "[{\n" +
                "\t\t\t\"superRater\": {\n" +
                "\t\t\t\t\"superiorScoreOrder\": \"sameTime\",\n" +
                "\t\t\t\t\"signatureFlag\": false,\n" +
                "\t\t\t\t\"auditNodes\": [],\n" +
                "\t\t\t\t\"open\": 0\n" +
                "\t\t\t},\n" +
                "\t\t\t\"reserveOkrWeight\": 35,\n" +
                "\t\t\t\"peerRater\": {\n" +
                "\t\t\t\t\"change\": false,\n" +
                "\t\t\t\t\"excludeAllManager\": false,\n" +
                "\t\t\t\t\"raters\": [],\n" +
                "\t\t\t\t\"signatureFlag\": false,\n" +
                "\t\t\t\t\"open\": 0\n" +
                "\t\t\t},\n" +
                "\t\t\t\"appointRater\": {\n" +
                "\t\t\t\t\"signatureFlag\": false,\n" +
                "\t\t\t\t\"auditNodes\": [],\n" +
                "\t\t\t\t\"open\": 0\n" +
                "\t\t\t},\n" +
                "\t\t\t\"scoreOptType\": 2,\n" +
                "\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\"finishValueAudit\": [{\n" +
                "\t\t\t\t\"objItems\": [],\n" +
                "\t\t\t\t\"objType\": \"role\"\n" +
                "\t\t\t}, {\n" +
                "\t\t\t\t\"objItems\": [],\n" +
                "\t\t\t\t\"objType\": \"user\"\n" +
                "\t\t\t}, {\n" +
                "\t\t\t\t\"objItems\": [],\n" +
                "\t\t\t\t\"objType\": \"manager\"\n" +
                "\t\t\t}],\n" +
                "\t\t\t\"des\": \"Ĭ�Ͽ���ά������\",\n" +
                "\t\t\t\"selfRater\": {\n" +
                "\t\t\t\t\"nodeWeight\": 100,\n" +
                "\t\t\t\t\"rateMode\": \"item\",\n" +
                "\t\t\t\t\"signatureFlag\": false,\n" +
                "\t\t\t\t\"anonymous\": \"false\",\n" +
                "\t\t\t\t\"open\": 1\n" +
                "\t\t\t},\n" +
                "\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\"isRaterBack\": false,\n" +
                "\t\t\t\"createdTime\": 1730200722000,\n" +
                "\t\t\t\"kpiTypeWeight\": 0,\n" +
                "\t\t\t\"createdUser\": \"1318001\",\n" +
                "\t\t\t\"updatedTime\": 1730200722000,\n" +
                "\t\t\t\"scoringType\": 1,\n" +
                "\t\t\t\"typeOrder\": 0,\n" +
                "\t\t\t\"maxExtraScore\": 0,\n" +
                "\t\t\t\"plusSubInterval\": {\n" +
                "\t\t\t\t\"max\": \"\",\n" +
                "\t\t\t\t\"min\": \"\"\n" +
                "\t\t\t},\n" +
                "\t\t\t\"version\": 0,\n" +
                "\t\t\t\"openOkrScore\": 0,\n" +
                "\t\t\t\"isOkr\": \"false\",\n" +
                "\t\t\t\"companyId\": {\n" +
                "\t\t\t\t\"id\": \"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\"\n" +
                "\t\t\t},\n" +
                "\t\t\t\"kpiTypeUsedFields\": [{\n" +
                "\t\t\t\t\"updatedTime\": 1729593502000,\n" +
                "\t\t\t\t\"adminType\": 1,\n" +
                "\t\t\t\t\"show\": 1,\n" +
                "\t\t\t\t\"sort\": 1,\n" +
                "\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\"type\": 1,\n" +
                "\t\t\t\t\"companyId\": \"\",\n" +
                "\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\"name\": \"ָ������\",\n" +
                "\t\t\t\t\"kpiTypeId\": \"92e28c6d-ec09-4db4-80cf-bd1b4158fcf7\",\n" +
                "\t\t\t\t\"createdTime\": 1730200722000,\n" +
                "\t\t\t\t\"fieldId\": \"name\",\n" +
                "\t\t\t\t\"req\": 1,\n" +
                "\t\t\t\t\"status\": \"valid\"\n" +
                "\t\t\t}, {\n" +
                "\t\t\t\t\"updatedTime\": 1729593502000,\n" +
                "\t\t\t\t\"adminType\": 1,\n" +
                "\t\t\t\t\"show\": 1,\n" +
                "\t\t\t\t\"sort\": 2,\n" +
                "\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\"type\": 1,\n" +
                "\t\t\t\t\"companyId\": \"\",\n" +
                "\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\"name\": \"���˱�\u05FC\",\n" +
                "\t\t\t\t\"kpiTypeId\": \"92e28c6d-ec09-4db4-80cf-bd1b4158fcf7\",\n" +
                "\t\t\t\t\"createdTime\": 1730200722000,\n" +
                "\t\t\t\t\"fieldId\": \"standard\",\n" +
                "\t\t\t\t\"req\": 0,\n" +
                "\t\t\t\t\"status\": \"valid\"\n" +
                "\t\t\t}, {\n" +
                "\t\t\t\t\"updatedTime\": 1729593502000,\n" +
                "\t\t\t\t\"adminType\": 1,\n" +
                "\t\t\t\t\"show\": 1,\n" +
                "\t\t\t\t\"sort\": 3,\n" +
                "\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\"type\": 1,\n" +
                "\t\t\t\t\"companyId\": \"\",\n" +
                "\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\"name\": \"�Ʒֹ���\",\n" +
                "\t\t\t\t\"kpiTypeId\": \"92e28c6d-ec09-4db4-80cf-bd1b4158fcf7\",\n" +
                "\t\t\t\t\"createdTime\": 1730200722000,\n" +
                "\t\t\t\t\"fieldId\": \"scoreRule\",\n" +
                "\t\t\t\t\"req\": 0,\n" +
                "\t\t\t\t\"status\": \"valid\"\n" +
                "\t\t\t}, {\n" +
                "\t\t\t\t\"updatedTime\": 1729593502000,\n" +
                "\t\t\t\t\"adminType\": 1,\n" +
                "\t\t\t\t\"show\": 1,\n" +
                "\t\t\t\t\"sort\": 4,\n" +
                "\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\"type\": 1,\n" +
                "\t\t\t\t\"companyId\": \"\",\n" +
                "\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\"name\": \"Ŀ��ֵ\",\n" +
                "\t\t\t\t\"kpiTypeId\": \"92e28c6d-ec09-4db4-80cf-bd1b4158fcf7\",\n" +
                "\t\t\t\t\"createdTime\": 1730200722000,\n" +
                "\t\t\t\t\"fieldId\": \"targetValue\",\n" +
                "\t\t\t\t\"req\": 1,\n" +
                "\t\t\t\t\"status\": \"valid\"\n" +
                "\t\t\t}, {\n" +
                "\t\t\t\t\"updatedTime\": 1729593502000,\n" +
                "\t\t\t\t\"adminType\": 1,\n" +
                "\t\t\t\t\"show\": 1,\n" +
                "\t\t\t\t\"sort\": 5,\n" +
                "\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\"type\": 1,\n" +
                "\t\t\t\t\"companyId\": \"\",\n" +
                "\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\"name\": \"��λ\",\n" +
                "\t\t\t\t\"kpiTypeId\": \"92e28c6d-ec09-4db4-80cf-bd1b4158fcf7\",\n" +
                "\t\t\t\t\"createdTime\": 1730200722000,\n" +
                "\t\t\t\t\"fieldId\": \"unit\",\n" +
                "\t\t\t\t\"req\": 1,\n" +
                "\t\t\t\t\"status\": \"valid\"\n" +
                "\t\t\t}, {\n" +
                "\t\t\t\t\"updatedTime\": 1729593502000,\n" +
                "\t\t\t\t\"adminType\": 1,\n" +
                "\t\t\t\t\"show\": 1,\n" +
                "\t\t\t\t\"sort\": 6,\n" +
                "\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\"type\": 1,\n" +
                "\t\t\t\t\"companyId\": \"\",\n" +
                "\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\"name\": \"ָ��Ȩ��\",\n" +
                "\t\t\t\t\"kpiTypeId\": \"92e28c6d-ec09-4db4-80cf-bd1b4158fcf7\",\n" +
                "\t\t\t\t\"createdTime\": 1730200722000,\n" +
                "\t\t\t\t\"fieldId\": \"indexWeight\",\n" +
                "\t\t\t\t\"req\": 1,\n" +
                "\t\t\t\t\"status\": \"valid\"\n" +
                "\t\t\t}, {\n" +
                "\t\t\t\t\"updatedTime\": 1729593502000,\n" +
                "\t\t\t\t\"adminType\": 1,\n" +
                "\t\t\t\t\"show\": 1,\n" +
                "\t\t\t\t\"sort\": 7,\n" +
                "\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\"type\": 1,\n" +
                "\t\t\t\t\"companyId\": \"\",\n" +
                "\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\"name\": \"���ַ�ֵ\",\n" +
                "\t\t\t\t\"kpiTypeId\": \"92e28c6d-ec09-4db4-80cf-bd1b4158fcf7\",\n" +
                "\t\t\t\t\"createdTime\": 1730200722000,\n" +
                "\t\t\t\t\"fieldId\": \"scoreValue\",\n" +
                "\t\t\t\t\"req\": 1,\n" +
                "\t\t\t\t\"status\": \"valid\"\n" +
                "\t\t\t}, {\n" +
                "\t\t\t\t\"updatedTime\": 1729593502000,\n" +
                "\t\t\t\t\"adminType\": 1,\n" +
                "\t\t\t\t\"show\": 1,\n" +
                "\t\t\t\t\"sort\": 8,\n" +
                "\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\"type\": 1,\n" +
                "\t\t\t\t\"kpiSyncReqConf\": {\n" +
                "\t\t\t\t\t\"syncOpen\": 0,\n" +
                "\t\t\t\t\t\"reqItems\": {\n" +
                "\t\t\t\t\t\t\"inputFinishValue\": {\n" +
                "\t\t\t\t\t\t\t\"isReq\": 0,\n" +
                "\t\t\t\t\t\t\t\"desc\": \"���ֵ����\"\n" +
                "\t\t\t\t\t\t},\n" +
                "\t\t\t\t\t\t\"attachment\": {\n" +
                "\t\t\t\t\t\t\t\"isReq\": 0,\n" +
                "\t\t\t\t\t\t\t\"desc\": \"��������\"\n" +
                "\t\t\t\t\t\t},\n" +
                "\t\t\t\t\t\t\"comment\": {\n" +
                "\t\t\t\t\t\t\t\"isReq\": 0,\n" +
                "\t\t\t\t\t\t\t\"desc\": \"��ע����\"\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t}\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t\"companyId\": \"\",\n" +
                "\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\"name\": \"���ֵ¼��\",\n" +
                "\t\t\t\t\"kpiTypeId\": \"92e28c6d-ec09-4db4-80cf-bd1b4158fcf7\",\n" +
                "\t\t\t\t\"createdTime\": 1730200722000,\n" +
                "\t\t\t\t\"fieldId\": \"finishValue\",\n" +
                "\t\t\t\t\"req\": 1,\n" +
                "\t\t\t\t\"status\": \"valid\"\n" +
                "\t\t\t}, {\n" +
                "\t\t\t\t\"updatedTime\": 1729768711000,\n" +
                "\t\t\t\t\"adminType\": 0,\n" +
                "\t\t\t\t\"show\": 0,\n" +
                "\t\t\t\t\"sort\": 9,\n" +
                "\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\"type\": 2,\n" +
                "\t\t\t\t\"companyId\": \"\",\n" +
                "\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\"name\": \"��սֵ\",\n" +
                "\t\t\t\t\"kpiTypeId\": \"92e28c6d-ec09-4db4-80cf-bd1b4158fcf7\",\n" +
                "\t\t\t\t\"createdTime\": 1730200722000,\n" +
                "\t\t\t\t\"fieldId\": \"fd3f19f5-a446-401e-9708-52a97e742690\",\n" +
                "\t\t\t\t\"req\": 0,\n" +
                "\t\t\t\t\"status\": \"valid\",\n" +
                "\t\t\t\t\"unitSwitch\": 1\n" +
                "\t\t\t}, {\n" +
                "\t\t\t\t\"updatedTime\": 1729768711000,\n" +
                "\t\t\t\t\"adminType\": 0,\n" +
                "\t\t\t\t\"show\": 0,\n" +
                "\t\t\t\t\"sort\": 10,\n" +
                "\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\"type\": 1,\n" +
                "\t\t\t\t\"companyId\": \"\",\n" +
                "\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\"name\": \"�ı�ֵ\",\n" +
                "\t\t\t\t\"kpiTypeId\": \"92e28c6d-ec09-4db4-80cf-bd1b4158fcf7\",\n" +
                "\t\t\t\t\"createdTime\": 1730200722000,\n" +
                "\t\t\t\t\"fieldId\": \"1f692913-2b75-4212-a79d-252646a25cf2\",\n" +
                "\t\t\t\t\"req\": 0,\n" +
                "\t\t\t\t\"status\": \"valid\",\n" +
                "\t\t\t\t\"unitSwitch\": 0\n" +
                "\t\t\t}, {\n" +
                "\t\t\t\t\"updatedTime\": 1729768711000,\n" +
                "\t\t\t\t\"adminType\": 0,\n" +
                "\t\t\t\t\"show\": 0,\n" +
                "\t\t\t\t\"sort\": 11,\n" +
                "\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\"type\": 2,\n" +
                "\t\t\t\t\"companyId\": \"\",\n" +
                "\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\"name\": \"�ż�ֵ\",\n" +
                "\t\t\t\t\"kpiTypeId\": \"92e28c6d-ec09-4db4-80cf-bd1b4158fcf7\",\n" +
                "\t\t\t\t\"createdTime\": 1730200722000,\n" +
                "\t\t\t\t\"fieldId\": \"07c9bfa9-8fb4-4565-ad4b-d31c32067fa2\",\n" +
                "\t\t\t\t\"req\": 0,\n" +
                "\t\t\t\t\"status\": \"valid\",\n" +
                "\t\t\t\t\"unitSwitch\": 0\n" +
                "\t\t\t}, {\n" +
                "\t\t\t\t\"updatedTime\": 1729768711000,\n" +
                "\t\t\t\t\"adminType\": 0,\n" +
                "\t\t\t\t\"show\": 0,\n" +
                "\t\t\t\t\"sort\": 12,\n" +
                "\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\"type\": 2,\n" +
                "\t\t\t\t\"companyId\": \"\",\n" +
                "\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\"name\": \"ռ�Ƚ��\",\n" +
                "\t\t\t\t\"kpiTypeId\": \"92e28c6d-ec09-4db4-80cf-bd1b4158fcf7\",\n" +
                "\t\t\t\t\"createdTime\": 1730200722000,\n" +
                "\t\t\t\t\"fieldId\": \"e8be3183-93bf-4545-8590-ee5709f24ad4\",\n" +
                "\t\t\t\t\"req\": 0,\n" +
                "\t\t\t\t\"status\": \"valid\",\n" +
                "\t\t\t\t\"unitSwitch\": 1\n" +
                "\t\t\t}],\n" +
                "\t\t\t\"kpiTypeName\": \"Ĭ�Ͽ���ά��\",\n" +
                "\t\t\t\"kpiTypeId\": \"92e28c6d-ec09-4db4-80cf-bd1b4158fcf7\",\n" +
                "\t\t\t\"items\": [{\n" +
                "\t\t\t\t\"alreadyNodes\": [],\n" +
                "\t\t\t\t\"itemRule\": \"\",\n" +
                "\t\t\t\t\"reserveOkrWeight\": 35,\n" +
                "\t\t\t\t\"showFinishBar\": 1,\n" +
                "\t\t\t\t\"itemFinishValue\": 1,\n" +
                "\t\t\t\t\"itemUnit\": \"Ԫ\",\n" +
                "\t\t\t\t\"multipleReviewersType\": \"or\",\n" +
                "\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\"itemScoreValue\": \"{\\\"type\\\":\\\"toMainScore\\\"}\",\n" +
                "\t\t\t\t\"finishValueAudit\": [{\n" +
                "\t\t\t\t\t\"objItems\": [],\n" +
                "\t\t\t\t\t\"objType\": \"role\"\n" +
                "\t\t\t\t}, {\n" +
                "\t\t\t\t\t\"objItems\": [{\n" +
                "\t\t\t\t\t\t\"objName\": \"����\",\n" +
                "\t\t\t\t\t\t\"objId\": \"1318001\"\n" +
                "\t\t\t\t\t}],\n" +
                "\t\t\t\t\t\"objType\": \"user\"\n" +
                "\t\t\t\t}, {\n" +
                "\t\t\t\t\t\"objItems\": [],\n" +
                "\t\t\t\t\t\"objType\": \"manager\"\n" +
                "\t\t\t\t}],\n" +
                "\t\t\t\t\"showTargetValue\": \"\",\n" +
                "\t\t\t\t\"formulaType\": 1,\n" +
                "\t\t\t\t\"finishValueType\": 1,\n" +
                "\t\t\t\t\"createdTime\": 1730200723000,\n" +
                "\t\t\t\t\"id\": \"1300801\",\n" +
                "\t\t\t\t\"createdUser\": \"1318001\",\n" +
                "\t\t\t\t\"scorerType\": \"exam\",\n" +
                "\t\t\t\t\"order\": 0,\n" +
                "\t\t\t\t\"itemFormula\": \"\",\n" +
                "\t\t\t\t\"updatedTime\": 1730273703000,\n" +
                "\t\t\t\t\"inputEmps\": [{\n" +
                "\t\t\t\t\t\"empId\": \"1318001\",\n" +
                "\t\t\t\t\t\"avatar\": \"\",\n" +
                "\t\t\t\t\t\"exUserId\": \"28610769081287531\",\n" +
                "\t\t\t\t\t\"empName\": \"����\",\n" +
                "\t\t\t\t\t\"status\": \"on_the_job\"\n" +
                "\t\t\t\t}],\n" +
                "\t\t\t\t\"thresholdJson\": \"[]\",\n" +
                "\t\t\t\t\"typeOrder\": 0,\n" +
                "\t\t\t\t\"plusSubInterval\": {\n" +
                "\t\t\t\t\t\"max\": \"\",\n" +
                "\t\t\t\t\t\"min\": \"\"\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t\"inputFormat\": \"num\",\n" +
                "\t\t\t\t\"version\": 0,\n" +
                "\t\t\t\t\"companyId\": {\n" +
                "\t\t\t\t\t\"id\": \"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\"\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t\"resultInputType\": \"exam\",\n" +
                "\t\t\t\t\"itemFullScoreCfg\": \"false\",\n" +
                "\t\t\t\t\"mustResultInput\": 1,\n" +
                "\t\t\t\t\"itemTargetValue\": 0,\n" +
                "\t\t\t\t\"itemFieldJson\": \"[{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"���ֵ\\\",\\\"companyFieldId\\\":\\\"268b0a46-c2bd-493d-aff8-8318aa7b02a8\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"fieldId\\\":\\\"finishValue\\\",\\\"label\\\":\\\"���ֵ\\\",\\\"value\\\":0,\\\"yVal\\\":true},{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"Ŀ��ֵ\\\",\\\"companyFieldId\\\":\\\"e0674594-186f-4aa7-817d-30a2c9cee161\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"fieldId\\\":\\\"targetValue\\\",\\\"label\\\":\\\"Ŀ��ֵ\\\",\\\"value\\\":0,\\\"yVal\\\":true},{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"���ֵ\\\",\\\"companyFieldId\\\":\\\"4ca8ffeb-6239-43e7-b67f-0259ba826184\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"fieldId\\\":\\\"finishValue\\\",\\\"label\\\":\\\"���ֵ\\\",\\\"value\\\":0,\\\"yVal\\\":true},{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"Ŀ��ֵ\\\",\\\"companyFieldId\\\":\\\"1ea75c43-ff00-4ffa-be07-58f44f231a81\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"fieldId\\\":\\\"targetValue\\\",\\\"label\\\":\\\"Ŀ��ֵ\\\",\\\"value\\\":0,\\\"yVal\\\":true}]\",\n" +
                "\t\t\t\t\"formulaCondition\": \"[{\\\"companyFieldId\\\":\\\"268b0a46-c2bd-493d-aff8-8318aa7b02a8\\\",\\\"createdTime\\\":\\\"2024-10-26 19:08:53\\\",\\\"formulaFieldName\\\":\\\"���ֵ\\\",\\\"formulaFieldValue\\\":0,\\\"id\\\":\\\"2027001\\\",\\\"kpiItemId\\\":\\\"9bb96c7a-5942-4085-93f3-a01d394304c3\\\",\\\"taskId\\\":\\\"1632606\\\",\\\"taskUserId\\\":\\\"1271401\\\"},{\\\"companyFieldId\\\":\\\"e0674594-186f-4aa7-817d-30a2c9cee161\\\",\\\"createdTime\\\":\\\"2024-10-26 19:08:53\\\",\\\"formulaFieldName\\\":\\\"Ŀ��ֵ\\\",\\\"formulaFieldValue\\\":0,\\\"id\\\":\\\"2027002\\\",\\\"kpiItemId\\\":\\\"9bb96c7a-5942-4085-93f3-a01d394304c3\\\",\\\"taskId\\\":\\\"1632606\\\",\\\"taskUserId\\\":\\\"1271401\\\"},{\\\"companyFieldId\\\":\\\"4ca8ffeb-6239-43e7-b67f-0259ba826184\\\",\\\"createdTime\\\":\\\"2024-10-26 19:08:53\\\",\\\"formulaFieldName\\\":\\\"���ֵ\\\",\\\"formulaFieldValue\\\":0,\\\"id\\\":\\\"2027003\\\",\\\"kpiItemId\\\":\\\"9bb96c7a-5942-4085-93f3-a01d394304c3\\\",\\\"taskId\\\":\\\"1632606\\\",\\\"taskUserId\\\":\\\"1271401\\\"},{\\\"companyFieldId\\\":\\\"1ea75c43-ff00-4ffa-be07-58f44f231a81\\\",\\\"createdTime\\\":\\\"2024-10-26 19:08:53\\\",\\\"formulaFieldName\\\":\\\"Ŀ��ֵ\\\",\\\"formulaFieldValue\\\":0,\\\"id\\\":\\\"2027004\\\",\\\"kpiItemId\\\":\\\"9bb96c7a-5942-4085-93f3-a01d394304c3\\\",\\\"taskId\\\":\\\"1632606\\\",\\\"taskUserId\\\":\\\"1271401\\\"}]\",\n" +
                "\t\t\t\t\"empId\": \"1318001\",\n" +
                "\t\t\t\t\"itemType\": \"measurable\",\n" +
                "\t\t\t\t\"scorerObjId\": [{\n" +
                "\t\t\t\t\t\"objItems\": [],\n" +
                "\t\t\t\t\t\"objType\": \"role\"\n" +
                "\t\t\t\t}, {\n" +
                "\t\t\t\t\t\"objItems\": [],\n" +
                "\t\t\t\t\t\"objType\": \"user\"\n" +
                "\t\t\t\t}, {\n" +
                "\t\t\t\t\t\"objItems\": [],\n" +
                "\t\t\t\t\t\"objType\": \"manager\"\n" +
                "\t\t\t\t}],\n" +
                "\t\t\t\t\"finishValueAuditStatus\": 0,\n" +
                "\t\t\t\t\"itemScoreRule\": {\n" +
                "\t\t\t\t\t\"superRater\": {\n" +
                "\t\t\t\t\t\t\"superiorScoreOrder\": \"sameTime\",\n" +
                "\t\t\t\t\t\t\"signatureFlag\": false,\n" +
                "\t\t\t\t\t\t\"auditNodes\": [],\n" +
                "\t\t\t\t\t\t\"open\": 0\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"kpiItemId\": \"9bb96c7a-5942-4085-93f3-a01d394304c3\",\n" +
                "\t\t\t\t\t\"peerRater\": {\n" +
                "\t\t\t\t\t\t\"change\": false,\n" +
                "\t\t\t\t\t\t\"excludeAllManager\": false,\n" +
                "\t\t\t\t\t\t\"raters\": [],\n" +
                "\t\t\t\t\t\t\"signatureFlag\": false,\n" +
                "\t\t\t\t\t\t\"open\": 0\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"appointRater\": {\n" +
                "\t\t\t\t\t\t\"signatureFlag\": false,\n" +
                "\t\t\t\t\t\t\"auditNodes\": [],\n" +
                "\t\t\t\t\t\t\"open\": 0\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"appointScoreFlag\": \"false\",\n" +
                "\t\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\t\"version\": 0,\n" +
                "\t\t\t\t\t\"superiorScoreFlag\": \"false\",\n" +
                "\t\t\t\t\t\"companyId\": {\n" +
                "\t\t\t\t\t\t\"id\": \"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"selfScoreFlag\": \"true\",\n" +
                "\t\t\t\t\t\"selfRater\": {\n" +
                "\t\t\t\t\t\t\"nodeWeight\": 100,\n" +
                "\t\t\t\t\t\t\"rateMode\": \"item\",\n" +
                "\t\t\t\t\t\t\"signatureFlag\": false,\n" +
                "\t\t\t\t\t\t\"anonymous\": \"false\",\n" +
                "\t\t\t\t\t\t\"open\": 1\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"selfScoreWeight\": 100,\n" +
                "\t\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\t\"kpiTypeId\": \"92e28c6d-ec09-4db4-80cf-bd1b4158fcf7\",\n" +
                "\t\t\t\t\t\"subRater\": {\n" +
                "\t\t\t\t\t\t\"change\": false,\n" +
                "\t\t\t\t\t\t\"excludeAllManager\": false,\n" +
                "\t\t\t\t\t\t\"raters\": [],\n" +
                "\t\t\t\t\t\t\"signatureFlag\": false,\n" +
                "\t\t\t\t\t\t\"open\": 0\n" +
                "\t\t\t\t\t}\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t\"managerLevel\": \"\",\n" +
                "\t\t\t\t\"kpiItemName\": \"�������ֵָ��-��������¼��\",\n" +
                "\t\t\t\t\"otherReqField\": {\n" +
                "\t\t\t\t\t\"inputFinishValue\": {\n" +
                "\t\t\t\t\t\t\"isReq\": 1,\n" +
                "\t\t\t\t\t\t\"desc\": \"���ֵ����\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"attachment\": {\n" +
                "\t\t\t\t\t\t\"isReq\": 0,\n" +
                "\t\t\t\t\t\t\"desc\": \"��������\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"comment\": {\n" +
                "\t\t\t\t\t\t\"isReq\": 0,\n" +
                "\t\t\t\t\t\t\"desc\": \"��ע����\"\n" +
                "\t\t\t\t\t}\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\"finalSubmitFinishValue\": 1,\n" +
                "\t\t\t\t\"kpiTypeWeight\": 0,\n" +
                "\t\t\t\t\"itemTargetValueText\": \"\",\n" +
                "\t\t\t\t\"inputRole\": [],\n" +
                "\t\t\t\t\"itemWeight\": 26,\n" +
                "\t\t\t\t\"scoringRule\": \"eee\",\n" +
                "\t\t\t\t\"kpiItemId\": \"9bb96c7a-5942-4085-93f3-a01d394304c3\",\n" +
                "\t\t\t\t\"maxExtraScore\": 0,\n" +
                "\t\t\t\t\"appointAudits\": [],\n" +
                "\t\t\t\t\"openOkrScore\": 0,\n" +
                "\t\t\t\t\"isOkr\": \"false\",\n" +
                "\t\t\t\t\"resultInputEmpId\": \"1318001\",\n" +
                "\t\t\t\t\"kpiTypeName\": \"Ĭ�Ͽ���ά��\",\n" +
                "\t\t\t\t\"kpiTypeId\": \"92e28c6d-ec09-4db4-80cf-bd1b4158fcf7\",\n" +
                "\t\t\t\t\"isNewEmp\": 0,\n" +
                "\t\t\t\t\"formulaFields\": [{\n" +
                "\t\t\t\t\t\"updatedTime\": 1730200722000,\n" +
                "\t\t\t\t\t\"kpiItemId\": \"9bb96c7a-5942-4085-93f3-a01d394304c3\",\n" +
                "\t\t\t\t\t\"formulaFieldValue\": 0,\n" +
                "\t\t\t\t\t\"companyFieldId\": \"268b0a46-c2bd-493d-aff8-8318aa7b02a8\",\n" +
                "\t\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\t\"updatedUser\": \"1318001\",\n" +
                "\t\t\t\t\t\"version\": 0,\n" +
                "\t\t\t\t\t\"companyId\": {\n" +
                "\t\t\t\t\t\t\"id\": \"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\t\"formulaFieldName\": \"���ֵ\",\n" +
                "\t\t\t\t\t\"createdTime\": 1730200722000,\n" +
                "\t\t\t\t\t\"id\": \"2027701\",\n" +
                "\t\t\t\t\t\"taskId\": \"1632606\",\n" +
                "\t\t\t\t\t\"createdUser\": \"1318001\"\n" +
                "\t\t\t\t}, {\n" +
                "\t\t\t\t\t\"updatedTime\": 1730200722000,\n" +
                "\t\t\t\t\t\"kpiItemId\": \"9bb96c7a-5942-4085-93f3-a01d394304c3\",\n" +
                "\t\t\t\t\t\"formulaFieldValue\": 0,\n" +
                "\t\t\t\t\t\"companyFieldId\": \"e0674594-186f-4aa7-817d-30a2c9cee161\",\n" +
                "\t\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\t\"updatedUser\": \"1318001\",\n" +
                "\t\t\t\t\t\"version\": 0,\n" +
                "\t\t\t\t\t\"companyId\": {\n" +
                "\t\t\t\t\t\t\"id\": \"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\t\"formulaFieldName\": \"Ŀ��ֵ\",\n" +
                "\t\t\t\t\t\"createdTime\": 1730200722000,\n" +
                "\t\t\t\t\t\"id\": \"2027702\",\n" +
                "\t\t\t\t\t\"taskId\": \"1632606\",\n" +
                "\t\t\t\t\t\"createdUser\": \"1318001\"\n" +
                "\t\t\t\t}, {\n" +
                "\t\t\t\t\t\"updatedTime\": 1730200722000,\n" +
                "\t\t\t\t\t\"kpiItemId\": \"9bb96c7a-5942-4085-93f3-a01d394304c3\",\n" +
                "\t\t\t\t\t\"formulaFieldValue\": 0,\n" +
                "\t\t\t\t\t\"companyFieldId\": \"4ca8ffeb-6239-43e7-b67f-0259ba826184\",\n" +
                "\t\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\t\"updatedUser\": \"1318001\",\n" +
                "\t\t\t\t\t\"version\": 0,\n" +
                "\t\t\t\t\t\"companyId\": {\n" +
                "\t\t\t\t\t\t\"id\": \"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\t\"formulaFieldName\": \"���ֵ\",\n" +
                "\t\t\t\t\t\"createdTime\": 1730200722000,\n" +
                "\t\t\t\t\t\"id\": \"2027703\",\n" +
                "\t\t\t\t\t\"taskId\": \"1632606\",\n" +
                "\t\t\t\t\t\"createdUser\": \"1318001\"\n" +
                "\t\t\t\t}, {\n" +
                "\t\t\t\t\t\"updatedTime\": 1730200722000,\n" +
                "\t\t\t\t\t\"kpiItemId\": \"9bb96c7a-5942-4085-93f3-a01d394304c3\",\n" +
                "\t\t\t\t\t\"formulaFieldValue\": 0,\n" +
                "\t\t\t\t\t\"companyFieldId\": \"1ea75c43-ff00-4ffa-be07-58f44f231a81\",\n" +
                "\t\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\t\"updatedUser\": \"1318001\",\n" +
                "\t\t\t\t\t\"version\": 0,\n" +
                "\t\t\t\t\t\"companyId\": {\n" +
                "\t\t\t\t\t\t\"id\": \"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\t\"formulaFieldName\": \"Ŀ��ֵ\",\n" +
                "\t\t\t\t\t\"createdTime\": 1730200722000,\n" +
                "\t\t\t\t\t\"id\": \"2027704\",\n" +
                "\t\t\t\t\t\"taskId\": \"1632606\",\n" +
                "\t\t\t\t\t\"createdUser\": \"1318001\"\n" +
                "\t\t\t\t}],\n" +
                "\t\t\t\t\"taskId\": \"1632606\",\n" +
                "\t\t\t\t\"waitScores\": []\n" +
                "\t\t\t}, {\n" +
                "\t\t\t\t\"alreadyNodes\": [],\n" +
                "\t\t\t\t\"itemRule\": \"\",\n" +
                "\t\t\t\t\"reserveOkrWeight\": 35,\n" +
                "\t\t\t\t\"showFinishBar\": 1,\n" +
                "\t\t\t\t\"itemUnit\": \"Ԫ\",\n" +
                "\t\t\t\t\"multipleReviewersType\": \"or\",\n" +
                "\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\"itemScoreValue\": \"{\\\"type\\\":\\\"toMainScore\\\"}\",\n" +
                "\t\t\t\t\"finishValueAudit\": [{\n" +
                "\t\t\t\t\t\"objItems\": [],\n" +
                "\t\t\t\t\t\"objType\": \"role\"\n" +
                "\t\t\t\t}, {\n" +
                "\t\t\t\t\t\"objItems\": [],\n" +
                "\t\t\t\t\t\"objType\": \"user\"\n" +
                "\t\t\t\t}, {\n" +
                "\t\t\t\t\t\"objItems\": [],\n" +
                "\t\t\t\t\t\"objType\": \"manager\"\n" +
                "\t\t\t\t}],\n" +
                "\t\t\t\t\"showTargetValue\": \"\",\n" +
                "\t\t\t\t\"formulaType\": 1,\n" +
                "\t\t\t\t\"finishValueType\": 1,\n" +
                "\t\t\t\t\"createdTime\": 1730200723000,\n" +
                "\t\t\t\t\"id\": \"1300802\",\n" +
                "\t\t\t\t\"createdUser\": \"1318001\",\n" +
                "\t\t\t\t\"scorerType\": \"exam\",\n" +
                "\t\t\t\t\"order\": 1,\n" +
                "\t\t\t\t\"itemFormula\": \"\",\n" +
                "\t\t\t\t\"updatedTime\": 1730200723000,\n" +
                "\t\t\t\t\"inputEmps\": [{\n" +
                "\t\t\t\t\t\"empId\": \"1310001\",\n" +
                "\t\t\t\t\t\"avatar\": \"https://static-legacy.dingtalk.com/media/lQDPM4LJ3qGkS4XNAYDNAYCwihhNII8HoD0GRO-k6mD5AA_384_384.jpg\",\n" +
                "\t\t\t\t\t\"exUserId\": \"285069272228238995\",\n" +
                "\t\t\t\t\t\"empName\": \"������\",\n" +
                "\t\t\t\t\t\"status\": \"on_the_job\"\n" +
                "\t\t\t\t}],\n" +
                "\t\t\t\t\"thresholdJson\": \"[]\",\n" +
                "\t\t\t\t\"typeOrder\": 0,\n" +
                "\t\t\t\t\"plusSubInterval\": {\n" +
                "\t\t\t\t\t\"max\": \"\",\n" +
                "\t\t\t\t\t\"min\": \"\"\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t\"inputFormat\": \"num\",\n" +
                "\t\t\t\t\"version\": 0,\n" +
                "\t\t\t\t\"companyId\": {\n" +
                "\t\t\t\t\t\"id\": \"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\"\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t\"resultInputType\": \"user\",\n" +
                "\t\t\t\t\"itemFullScoreCfg\": \"false\",\n" +
                "\t\t\t\t\"mustResultInput\": 1,\n" +
                "\t\t\t\t\"itemTargetValue\": 0,\n" +
                "\t\t\t\t\"itemFieldJson\": \"[{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"���ֵ\\\",\\\"companyFieldId\\\":\\\"268b0a46-c2bd-493d-aff8-8318aa7b02a8\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"fieldId\\\":\\\"finishValue\\\",\\\"label\\\":\\\"���ֵ\\\",\\\"value\\\":0,\\\"yVal\\\":true},{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"Ŀ��ֵ\\\",\\\"companyFieldId\\\":\\\"e0674594-186f-4aa7-817d-30a2c9cee161\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"fieldId\\\":\\\"targetValue\\\",\\\"label\\\":\\\"Ŀ��ֵ\\\",\\\"value\\\":0,\\\"yVal\\\":true},{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"���ֵ\\\",\\\"companyFieldId\\\":\\\"4ca8ffeb-6239-43e7-b67f-0259ba826184\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"fieldId\\\":\\\"finishValue\\\",\\\"label\\\":\\\"���ֵ\\\",\\\"value\\\":0,\\\"yVal\\\":true},{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"Ŀ��ֵ\\\",\\\"companyFieldId\\\":\\\"1ea75c43-ff00-4ffa-be07-58f44f231a81\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"fieldId\\\":\\\"targetValue\\\",\\\"label\\\":\\\"Ŀ��ֵ\\\",\\\"value\\\":0,\\\"yVal\\\":true}]\",\n" +
                "\t\t\t\t\"formulaCondition\": \"[{\\\"companyFieldId\\\":\\\"268b0a46-c2bd-493d-aff8-8318aa7b02a8\\\",\\\"createdTime\\\":\\\"2024-10-26 19:08:53\\\",\\\"formulaFieldName\\\":\\\"���ֵ\\\",\\\"formulaFieldValue\\\":0,\\\"id\\\":\\\"2027005\\\",\\\"kpiItemId\\\":\\\"5c50390d-d688-4056-a97c-71e29cdeaf03\\\",\\\"taskId\\\":\\\"1632606\\\",\\\"taskUserId\\\":\\\"1271401\\\"},{\\\"companyFieldId\\\":\\\"e0674594-186f-4aa7-817d-30a2c9cee161\\\",\\\"createdTime\\\":\\\"2024-10-26 19:08:53\\\",\\\"formulaFieldName\\\":\\\"Ŀ��ֵ\\\",\\\"formulaFieldValue\\\":0,\\\"id\\\":\\\"2027006\\\",\\\"kpiItemId\\\":\\\"5c50390d-d688-4056-a97c-71e29cdeaf03\\\",\\\"taskId\\\":\\\"1632606\\\",\\\"taskUserId\\\":\\\"1271401\\\"},{\\\"companyFieldId\\\":\\\"4ca8ffeb-6239-43e7-b67f-0259ba826184\\\",\\\"createdTime\\\":\\\"2024-10-26 19:08:53\\\",\\\"formulaFieldName\\\":\\\"���ֵ\\\",\\\"formulaFieldValue\\\":0,\\\"id\\\":\\\"2027007\\\",\\\"kpiItemId\\\":\\\"5c50390d-d688-4056-a97c-71e29cdeaf03\\\",\\\"taskId\\\":\\\"1632606\\\",\\\"taskUserId\\\":\\\"1271401\\\"},{\\\"companyFieldId\\\":\\\"1ea75c43-ff00-4ffa-be07-58f44f231a81\\\",\\\"createdTime\\\":\\\"2024-10-26 19:08:53\\\",\\\"formulaFieldName\\\":\\\"Ŀ��ֵ\\\",\\\"formulaFieldValue\\\":0,\\\"id\\\":\\\"2027008\\\",\\\"kpiItemId\\\":\\\"5c50390d-d688-4056-a97c-71e29cdeaf03\\\",\\\"taskId\\\":\\\"1632606\\\",\\\"taskUserId\\\":\\\"1271401\\\"}]\",\n" +
                "\t\t\t\t\"empId\": \"1318001\",\n" +
                "\t\t\t\t\"itemType\": \"measurable\",\n" +
                "\t\t\t\t\"scorerObjId\": [{\n" +
                "\t\t\t\t\t\"objItems\": [],\n" +
                "\t\t\t\t\t\"objType\": \"role\"\n" +
                "\t\t\t\t}, {\n" +
                "\t\t\t\t\t\"objItems\": [],\n" +
                "\t\t\t\t\t\"objType\": \"user\"\n" +
                "\t\t\t\t}, {\n" +
                "\t\t\t\t\t\"objItems\": [],\n" +
                "\t\t\t\t\t\"objType\": \"manager\"\n" +
                "\t\t\t\t}],\n" +
                "\t\t\t\t\"finishValueAuditStatus\": 0,\n" +
                "\t\t\t\t\"itemScoreRule\": {\n" +
                "\t\t\t\t\t\"superRater\": {\n" +
                "\t\t\t\t\t\t\"superiorScoreOrder\": \"sameTime\",\n" +
                "\t\t\t\t\t\t\"signatureFlag\": false,\n" +
                "\t\t\t\t\t\t\"auditNodes\": [],\n" +
                "\t\t\t\t\t\t\"open\": 0\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"kpiItemId\": \"5c50390d-d688-4056-a97c-71e29cdeaf03\",\n" +
                "\t\t\t\t\t\"peerRater\": {\n" +
                "\t\t\t\t\t\t\"change\": false,\n" +
                "\t\t\t\t\t\t\"excludeAllManager\": false,\n" +
                "\t\t\t\t\t\t\"raters\": [],\n" +
                "\t\t\t\t\t\t\"signatureFlag\": false,\n" +
                "\t\t\t\t\t\t\"open\": 0\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"appointRater\": {\n" +
                "\t\t\t\t\t\t\"signatureFlag\": false,\n" +
                "\t\t\t\t\t\t\"auditNodes\": [],\n" +
                "\t\t\t\t\t\t\"open\": 0\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"appointScoreFlag\": \"false\",\n" +
                "\t\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\t\"version\": 0,\n" +
                "\t\t\t\t\t\"superiorScoreFlag\": \"false\",\n" +
                "\t\t\t\t\t\"companyId\": {\n" +
                "\t\t\t\t\t\t\"id\": \"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"selfScoreFlag\": \"true\",\n" +
                "\t\t\t\t\t\"selfRater\": {\n" +
                "\t\t\t\t\t\t\"nodeWeight\": 100,\n" +
                "\t\t\t\t\t\t\"rateMode\": \"item\",\n" +
                "\t\t\t\t\t\t\"signatureFlag\": false,\n" +
                "\t\t\t\t\t\t\"anonymous\": \"false\",\n" +
                "\t\t\t\t\t\t\"open\": 1\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"selfScoreWeight\": 100,\n" +
                "\t\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\t\"kpiTypeId\": \"92e28c6d-ec09-4db4-80cf-bd1b4158fcf7\",\n" +
                "\t\t\t\t\t\"subRater\": {\n" +
                "\t\t\t\t\t\t\"change\": false,\n" +
                "\t\t\t\t\t\t\"excludeAllManager\": false,\n" +
                "\t\t\t\t\t\t\"raters\": [],\n" +
                "\t\t\t\t\t\t\"signatureFlag\": false,\n" +
                "\t\t\t\t\t\t\"open\": 0\n" +
                "\t\t\t\t\t}\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t\"managerLevel\": \"\",\n" +
                "\t\t\t\t\"kpiItemName\": \"ָ���û��������ֵָ��-ָ���û�¼��\",\n" +
                "\t\t\t\t\"otherReqField\": {\n" +
                "\t\t\t\t\t\"inputFinishValue\": {\n" +
                "\t\t\t\t\t\t\"isReq\": 1,\n" +
                "\t\t\t\t\t\t\"desc\": \"���ֵ����\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"attachment\": {\n" +
                "\t\t\t\t\t\t\"isReq\": 0,\n" +
                "\t\t\t\t\t\t\"desc\": \"��������\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"comment\": {\n" +
                "\t\t\t\t\t\t\"isReq\": 0,\n" +
                "\t\t\t\t\t\t\"desc\": \"��ע����\"\n" +
                "\t\t\t\t\t}\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\"finalSubmitFinishValue\": 0,\n" +
                "\t\t\t\t\"kpiTypeWeight\": 0,\n" +
                "\t\t\t\t\"itemTargetValueText\": \"\",\n" +
                "\t\t\t\t\"inputRole\": [],\n" +
                "\t\t\t\t\"itemWeight\": 39,\n" +
                "\t\t\t\t\"scoringRule\": \"\",\n" +
                "\t\t\t\t\"kpiItemId\": \"5c50390d-d688-4056-a97c-71e29cdeaf03\",\n" +
                "\t\t\t\t\"maxExtraScore\": 0,\n" +
                "\t\t\t\t\"appointAudits\": [],\n" +
                "\t\t\t\t\"openOkrScore\": 0,\n" +
                "\t\t\t\t\"isOkr\": \"false\",\n" +
                "\t\t\t\t\"resultInputEmpId\": \"1310001\",\n" +
                "\t\t\t\t\"kpiTypeName\": \"Ĭ�Ͽ���ά��\",\n" +
                "\t\t\t\t\"kpiTypeId\": \"92e28c6d-ec09-4db4-80cf-bd1b4158fcf7\",\n" +
                "\t\t\t\t\"isNewEmp\": 0,\n" +
                "\t\t\t\t\"formulaFields\": [{\n" +
                "\t\t\t\t\t\"updatedTime\": 1730200722000,\n" +
                "\t\t\t\t\t\"kpiItemId\": \"5c50390d-d688-4056-a97c-71e29cdeaf03\",\n" +
                "\t\t\t\t\t\"formulaFieldValue\": 0,\n" +
                "\t\t\t\t\t\"companyFieldId\": \"268b0a46-c2bd-493d-aff8-8318aa7b02a8\",\n" +
                "\t\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\t\"updatedUser\": \"1318001\",\n" +
                "\t\t\t\t\t\"version\": 0,\n" +
                "\t\t\t\t\t\"companyId\": {\n" +
                "\t\t\t\t\t\t\"id\": \"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\t\"formulaFieldName\": \"���ֵ\",\n" +
                "\t\t\t\t\t\"createdTime\": 1730200722000,\n" +
                "\t\t\t\t\t\"id\": \"2027705\",\n" +
                "\t\t\t\t\t\"taskId\": \"1632606\",\n" +
                "\t\t\t\t\t\"createdUser\": \"1318001\"\n" +
                "\t\t\t\t}, {\n" +
                "\t\t\t\t\t\"updatedTime\": 1730200722000,\n" +
                "\t\t\t\t\t\"kpiItemId\": \"5c50390d-d688-4056-a97c-71e29cdeaf03\",\n" +
                "\t\t\t\t\t\"formulaFieldValue\": 0,\n" +
                "\t\t\t\t\t\"companyFieldId\": \"e0674594-186f-4aa7-817d-30a2c9cee161\",\n" +
                "\t\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\t\"updatedUser\": \"1318001\",\n" +
                "\t\t\t\t\t\"version\": 0,\n" +
                "\t\t\t\t\t\"companyId\": {\n" +
                "\t\t\t\t\t\t\"id\": \"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\t\"formulaFieldName\": \"Ŀ��ֵ\",\n" +
                "\t\t\t\t\t\"createdTime\": 1730200722000,\n" +
                "\t\t\t\t\t\"id\": \"2027706\",\n" +
                "\t\t\t\t\t\"taskId\": \"1632606\",\n" +
                "\t\t\t\t\t\"createdUser\": \"1318001\"\n" +
                "\t\t\t\t}, {\n" +
                "\t\t\t\t\t\"updatedTime\": 1730200722000,\n" +
                "\t\t\t\t\t\"kpiItemId\": \"5c50390d-d688-4056-a97c-71e29cdeaf03\",\n" +
                "\t\t\t\t\t\"formulaFieldValue\": 0,\n" +
                "\t\t\t\t\t\"companyFieldId\": \"4ca8ffeb-6239-43e7-b67f-0259ba826184\",\n" +
                "\t\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\t\"updatedUser\": \"1318001\",\n" +
                "\t\t\t\t\t\"version\": 0,\n" +
                "\t\t\t\t\t\"companyId\": {\n" +
                "\t\t\t\t\t\t\"id\": \"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\t\"formulaFieldName\": \"���ֵ\",\n" +
                "\t\t\t\t\t\"createdTime\": 1730200722000,\n" +
                "\t\t\t\t\t\"id\": \"2027707\",\n" +
                "\t\t\t\t\t\"taskId\": \"1632606\",\n" +
                "\t\t\t\t\t\"createdUser\": \"1318001\"\n" +
                "\t\t\t\t}, {\n" +
                "\t\t\t\t\t\"updatedTime\": 1730200722000,\n" +
                "\t\t\t\t\t\"kpiItemId\": \"5c50390d-d688-4056-a97c-71e29cdeaf03\",\n" +
                "\t\t\t\t\t\"formulaFieldValue\": 0,\n" +
                "\t\t\t\t\t\"companyFieldId\": \"1ea75c43-ff00-4ffa-be07-58f44f231a81\",\n" +
                "\t\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\t\"updatedUser\": \"1318001\",\n" +
                "\t\t\t\t\t\"version\": 0,\n" +
                "\t\t\t\t\t\"companyId\": {\n" +
                "\t\t\t\t\t\t\"id\": \"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\t\"formulaFieldName\": \"Ŀ��ֵ\",\n" +
                "\t\t\t\t\t\"createdTime\": 1730200722000,\n" +
                "\t\t\t\t\t\"id\": \"2027708\",\n" +
                "\t\t\t\t\t\"taskId\": \"1632606\",\n" +
                "\t\t\t\t\t\"createdUser\": \"1318001\"\n" +
                "\t\t\t\t}],\n" +
                "\t\t\t\t\"taskId\": \"1632606\",\n" +
                "\t\t\t\t\"waitScores\": []\n" +
                "\t\t\t}, {\n" +
                "\t\t\t\t\"alreadyNodes\": [],\n" +
                "\t\t\t\t\"itemRule\": \"\",\n" +
                "\t\t\t\t\"reserveOkrWeight\": 35,\n" +
                "\t\t\t\t\"showFinishBar\": 1,\n" +
                "\t\t\t\t\"itemFinishValue\": 0,\n" +
                "\t\t\t\t\"itemUnit\": \"��\",\n" +
                "\t\t\t\t\"multipleReviewersType\": \"or\",\n" +
                "\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\"itemScoreValue\": \"{\\\"type\\\":\\\"toMainScore\\\"}\",\n" +
                "\t\t\t\t\"finishValueAudit\": [{\n" +
                "\t\t\t\t\t\"objItems\": [],\n" +
                "\t\t\t\t\t\"objType\": \"role\"\n" +
                "\t\t\t\t}, {\n" +
                "\t\t\t\t\t\"objItems\": [],\n" +
                "\t\t\t\t\t\"objType\": \"user\"\n" +
                "\t\t\t\t}, {\n" +
                "\t\t\t\t\t\"objItems\": [],\n" +
                "\t\t\t\t\t\"objType\": \"manager\"\n" +
                "\t\t\t\t}],\n" +
                "\t\t\t\t\"showTargetValue\": \"\",\n" +
                "\t\t\t\t\"formulaType\": 1,\n" +
                "\t\t\t\t\"finishValueType\": 1,\n" +
                "\t\t\t\t\"createdTime\": 1730200723000,\n" +
                "\t\t\t\t\"id\": \"1300803\",\n" +
                "\t\t\t\t\"createdUser\": \"1318001\",\n" +
                "\t\t\t\t\"scorerType\": \"exam\",\n" +
                "\t\t\t\t\"order\": 2,\n" +
                "\t\t\t\t\"itemFormula\": \"\",\n" +
                "\t\t\t\t\"updatedTime\": 1730273703000,\n" +
                "\t\t\t\t\"inputEmps\": [{\n" +
                "\t\t\t\t\t\"empId\": \"1318001\",\n" +
                "\t\t\t\t\t\"avatar\": \"\",\n" +
                "\t\t\t\t\t\"exUserId\": \"28610769081287531\",\n" +
                "\t\t\t\t\t\"empName\": \"����\",\n" +
                "\t\t\t\t\t\"status\": \"on_the_job\"\n" +
                "\t\t\t\t}],\n" +
                "\t\t\t\t\"thresholdJson\": \"[]\",\n" +
                "\t\t\t\t\"typeOrder\": 0,\n" +
                "\t\t\t\t\"plusSubInterval\": {\n" +
                "\t\t\t\t\t\"max\": \"\",\n" +
                "\t\t\t\t\t\"min\": \"\"\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t\"inputFormat\": \"num\",\n" +
                "\t\t\t\t\"version\": 0,\n" +
                "\t\t\t\t\"companyId\": {\n" +
                "\t\t\t\t\t\"id\": \"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\"\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t\"resultInputType\": \"exam\",\n" +
                "\t\t\t\t\"itemFullScoreCfg\": \"false\",\n" +
                "\t\t\t\t\"mustResultInput\": 0,\n" +
                "\t\t\t\t\"itemTargetValue\": 12,\n" +
                "\t\t\t\t\"itemFieldJson\": \"[{\\\"companyId\\\":\\\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"Ŀ��ֵ\\\",\\\"companyFieldId\\\":\\\"4e40871c-b5ce-4564-b5d8-f89bdfdd7fec\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"fieldId\\\":\\\"targetValue\\\",\\\"label\\\":\\\"Ŀ��ֵ\\\",\\\"value\\\":0,\\\"yVal\\\":true},{\\\"companyId\\\":\\\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"���ֵ\\\",\\\"companyFieldId\\\":\\\"eb88708b-9af9-49c8-af29-b3f1c1c6d75a\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"fieldId\\\":\\\"finishValue\\\",\\\"label\\\":\\\"���ֵ\\\",\\\"value\\\":0,\\\"yVal\\\":true}]\",\n" +
                "\t\t\t\t\"formulaCondition\": \"[{\\\"companyFieldId\\\":\\\"4e40871c-b5ce-4564-b5d8-f89bdfdd7fec\\\",\\\"createdTime\\\":\\\"2024-10-26 19:08:53\\\",\\\"formulaFieldName\\\":\\\"Ŀ��ֵ\\\",\\\"formulaFieldValue\\\":0,\\\"id\\\":\\\"2027009\\\",\\\"kpiItemId\\\":\\\"d60cc283-f925-452a-8f4d-09b18b500be4\\\",\\\"taskId\\\":\\\"1632606\\\",\\\"taskUserId\\\":\\\"1271401\\\"},{\\\"companyFieldId\\\":\\\"eb88708b-9af9-49c8-af29-b3f1c1c6d75a\\\",\\\"createdTime\\\":\\\"2024-10-26 19:08:53\\\",\\\"formulaFieldName\\\":\\\"���ֵ\\\",\\\"formulaFieldValue\\\":0,\\\"id\\\":\\\"2027010\\\",\\\"kpiItemId\\\":\\\"d60cc283-f925-452a-8f4d-09b18b500be4\\\",\\\"taskId\\\":\\\"1632606\\\",\\\"taskUserId\\\":\\\"1271401\\\"}]\",\n" +
                "\t\t\t\t\"empId\": \"1318001\",\n" +
                "\t\t\t\t\"itemType\": \"measurable\",\n" +
                "\t\t\t\t\"scorerObjId\": [{\n" +
                "\t\t\t\t\t\"objItems\": [],\n" +
                "\t\t\t\t\t\"objType\": \"role\"\n" +
                "\t\t\t\t}, {\n" +
                "\t\t\t\t\t\"objItems\": [],\n" +
                "\t\t\t\t\t\"objType\": \"user\"\n" +
                "\t\t\t\t}, {\n" +
                "\t\t\t\t\t\"objItems\": [],\n" +
                "\t\t\t\t\t\"objType\": \"manager\"\n" +
                "\t\t\t\t}],\n" +
                "\t\t\t\t\"finishValueAuditStatus\": 0,\n" +
                "\t\t\t\t\"itemScoreRule\": {\n" +
                "\t\t\t\t\t\"superRater\": {\n" +
                "\t\t\t\t\t\t\"superiorScoreOrder\": \"sameTime\",\n" +
                "\t\t\t\t\t\t\"signatureFlag\": false,\n" +
                "\t\t\t\t\t\t\"auditNodes\": [],\n" +
                "\t\t\t\t\t\t\"open\": 0\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"kpiItemId\": \"d60cc283-f925-452a-8f4d-09b18b500be4\",\n" +
                "\t\t\t\t\t\"peerRater\": {\n" +
                "\t\t\t\t\t\t\"change\": false,\n" +
                "\t\t\t\t\t\t\"excludeAllManager\": false,\n" +
                "\t\t\t\t\t\t\"raters\": [],\n" +
                "\t\t\t\t\t\t\"signatureFlag\": false,\n" +
                "\t\t\t\t\t\t\"open\": 0\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"appointRater\": {\n" +
                "\t\t\t\t\t\t\"signatureFlag\": false,\n" +
                "\t\t\t\t\t\t\"auditNodes\": [],\n" +
                "\t\t\t\t\t\t\"open\": 0\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"appointScoreFlag\": \"false\",\n" +
                "\t\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\t\"version\": 0,\n" +
                "\t\t\t\t\t\"superiorScoreFlag\": \"false\",\n" +
                "\t\t\t\t\t\"companyId\": {\n" +
                "\t\t\t\t\t\t\"id\": \"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"selfScoreFlag\": \"true\",\n" +
                "\t\t\t\t\t\"selfRater\": {\n" +
                "\t\t\t\t\t\t\"nodeWeight\": 100,\n" +
                "\t\t\t\t\t\t\"rateMode\": \"item\",\n" +
                "\t\t\t\t\t\t\"signatureFlag\": false,\n" +
                "\t\t\t\t\t\t\"anonymous\": \"false\",\n" +
                "\t\t\t\t\t\t\"open\": 1\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"selfScoreWeight\": 100,\n" +
                "\t\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\t\"kpiTypeId\": \"92e28c6d-ec09-4db4-80cf-bd1b4158fcf7\",\n" +
                "\t\t\t\t\t\"subRater\": {\n" +
                "\t\t\t\t\t\t\"change\": false,\n" +
                "\t\t\t\t\t\t\"excludeAllManager\": false,\n" +
                "\t\t\t\t\t\t\"raters\": [],\n" +
                "\t\t\t\t\t\t\"signatureFlag\": false,\n" +
                "\t\t\t\t\t\t\"open\": 0\n" +
                "\t\t\t\t\t}\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t\"managerLevel\": \"\",\n" +
                "\t\t\t\t\"kpiItemName\": \"���ֵ������\",\n" +
                "\t\t\t\t\"otherReqField\": {\n" +
                "\t\t\t\t\t\"inputFinishValue\": {\n" +
                "\t\t\t\t\t\t\"isReq\": 0,\n" +
                "\t\t\t\t\t\t\"desc\": \"���ֵ����\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"attachment\": {\n" +
                "\t\t\t\t\t\t\"isReq\": 0,\n" +
                "\t\t\t\t\t\t\"desc\": \"��������\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"comment\": {\n" +
                "\t\t\t\t\t\t\"isReq\": 0,\n" +
                "\t\t\t\t\t\t\"desc\": \"��ע����\"\n" +
                "\t\t\t\t\t}\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\"finalSubmitFinishValue\": 1,\n" +
                "\t\t\t\t\"kpiTypeWeight\": 0,\n" +
                "\t\t\t\t\"itemTargetValueText\": \"\",\n" +
                "\t\t\t\t\"inputRole\": [],\n" +
                "\t\t\t\t\"itemWeight\": 35,\n" +
                "\t\t\t\t\"scoringRule\": \"\",\n" +
                "\t\t\t\t\"kpiItemId\": \"d60cc283-f925-452a-8f4d-09b18b500be4\",\n" +
                "\t\t\t\t\"maxExtraScore\": 0,\n" +
                "\t\t\t\t\"appointAudits\": [],\n" +
                "\t\t\t\t\"openOkrScore\": 0,\n" +
                "\t\t\t\t\"isOkr\": \"false\",\n" +
                "\t\t\t\t\"resultInputEmpId\": \"1318001\",\n" +
                "\t\t\t\t\"kpiTypeName\": \"Ĭ�Ͽ���ά��\",\n" +
                "\t\t\t\t\"kpiTypeId\": \"92e28c6d-ec09-4db4-80cf-bd1b4158fcf7\",\n" +
                "\t\t\t\t\"isNewEmp\": 0,\n" +
                "\t\t\t\t\"formulaFields\": [{\n" +
                "\t\t\t\t\t\"updatedTime\": 1730200722000,\n" +
                "\t\t\t\t\t\"kpiItemId\": \"d60cc283-f925-452a-8f4d-09b18b500be4\",\n" +
                "\t\t\t\t\t\"formulaFieldValue\": 0,\n" +
                "\t\t\t\t\t\"companyFieldId\": \"4e40871c-b5ce-4564-b5d8-f89bdfdd7fec\",\n" +
                "\t\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\t\"updatedUser\": \"1318001\",\n" +
                "\t\t\t\t\t\"version\": 0,\n" +
                "\t\t\t\t\t\"companyId\": {\n" +
                "\t\t\t\t\t\t\"id\": \"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\t\"formulaFieldName\": \"Ŀ��ֵ\",\n" +
                "\t\t\t\t\t\"createdTime\": 1730200722000,\n" +
                "\t\t\t\t\t\"id\": \"2027709\",\n" +
                "\t\t\t\t\t\"taskId\": \"1632606\",\n" +
                "\t\t\t\t\t\"createdUser\": \"1318001\"\n" +
                "\t\t\t\t}, {\n" +
                "\t\t\t\t\t\"updatedTime\": 1730200722000,\n" +
                "\t\t\t\t\t\"kpiItemId\": \"d60cc283-f925-452a-8f4d-09b18b500be4\",\n" +
                "\t\t\t\t\t\"formulaFieldValue\": 0,\n" +
                "\t\t\t\t\t\"companyFieldId\": \"eb88708b-9af9-49c8-af29-b3f1c1c6d75a\",\n" +
                "\t\t\t\t\t\"taskUserId\": \"1271401\",\n" +
                "\t\t\t\t\t\"updatedUser\": \"1318001\",\n" +
                "\t\t\t\t\t\"version\": 0,\n" +
                "\t\t\t\t\t\"companyId\": {\n" +
                "\t\t\t\t\t\t\"id\": \"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\"\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t\"isDeleted\": \"false\",\n" +
                "\t\t\t\t\t\"formulaFieldName\": \"���ֵ\",\n" +
                "\t\t\t\t\t\"createdTime\": 1730200722000,\n" +
                "\t\t\t\t\t\"id\": \"2027710\",\n" +
                "\t\t\t\t\t\"taskId\": \"1632606\",\n" +
                "\t\t\t\t\t\"createdUser\": \"1318001\"\n" +
                "\t\t\t\t}],\n" +
                "\t\t\t\t\"taskId\": \"1632606\",\n" +
                "\t\t\t\t\"waitScores\": []\n" +
                "\t\t\t}],\n" +
                "\t\t\t\"itemLimitCnt\": {\n" +
                "\t\t\t\t\"max\": \"\",\n" +
                "\t\t\t\t\"openItemLimit\": \"false\",\n" +
                "\t\t\t\t\"min\": \"\"\n" +
                "\t\t\t},\n" +
                "\t\t\t\"alreadyScores\": [],\n" +
                "\t\t\t\"subRater\": {\n" +
                "\t\t\t\t\"change\": false,\n" +
                "\t\t\t\t\"excludeAllManager\": false,\n" +
                "\t\t\t\t\"raters\": [],\n" +
                "\t\t\t\t\"signatureFlag\": false,\n" +
                "\t\t\t\t\"open\": 0\n" +
                "\t\t\t},\n" +
                "\t\t\t\"waitScores\": []\n" +
                "\t\t}]";

        String finishValuesStr = "[{\n" +
                "\t\t\"id\": \"1300802\",\n" +
                "\t\t\"kpiTypeId\": \"92e28c6d-ec09-4db4-80cf-bd1b4158fcf7\",\n" +
                "\t\t\"kpiItemId\": \"5c50390d-d688-4056-a97c-71e29cdeaf03\",\n" +
                "\t\t\"finishValueComment\": \"124\",\n" +
                "\t\t\"workItemFinishValue\": null,\n" +
                "\t\t\"kpiItemName\": \"指定用户审批完成值指标-指定用户录入\",\n" +
                "\t\t\"files\": [{\n" +
                "\t\t\t\"conType\": \"pic\",\n" +
                "\t\t\t\"url\": \"https://topscrm.oss-cn-hangzhou.aliyuncs.com/info/6zTyQjyTHH.png\"\n" +
                "\t\t}],\n" +
                "\t\t\"itemFinishValue\": null\n" +
                "\t}]";
        List<EmpEvalKpiType> empEvalKpiTypes = JSONUtil.parseArray(jsonStr).toList(EmpEvalKpiType.class);
        List<FinishValue> finishValues = JSONUtil.parseArray(finishValuesStr).toList(FinishValue.class);
        FinishValueReqValidationSvc finishValueReqValidationSvc = new FinishValueReqValidationSvc(empEvalKpiTypes);
        if (finishValueReqValidationSvc.allItemsCheckPassed(finishValues)) {
            System.out.println("校验通过！");
        } else {
            System.out.println("校验未通过！");
        }
    }
}
