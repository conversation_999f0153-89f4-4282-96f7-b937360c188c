package com.polaris.kpi.eval.domain.cycle.dmsvc;

import com.polaris.kpi.eval.domain.cycle.repo.SnapOnEmpRepo;
import com.polaris.kpi.eval.domain.cycle.repo.SnapOnTaskRepo;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.calibrated.ResultRankInstance;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.event.ThisStageEnded;
import com.polaris.kpi.eval.domain.task.repo.EmpEvalRuleRepo;
import com.polaris.kpi.eval.domain.task.repo.TaskUserRepo;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.org.domain.common.BaseEvent;
import com.polaris.kpi.org.domain.common.DomainEventPublisherI;
import com.polaris.sdk.common.JsonFileTool;
import com.polaris.sdk.type.TenantId;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.mockito.Mockito;

public class DetaLevelComputeDmSvcTest {

    SnapOnEmpRepo onEmpRepo = Mockito.mock(SnapOnEmpRepo.class);
    SnapOnTaskRepo onTaskRepo = Mockito.mock(SnapOnTaskRepo.class);

//    @Test
//    @DisplayName("需进入排名")
//    public void needOnLevelStageOnEmp() {
//        AdminTask adminTask = new AdminTask();
//        TenantId companyId = new TenantId("5a031297-1b38-48ae-bc82-375849835203");
//        String taskId = "1637601";
//        String taskUserId = "1284402";
//        String snapId = "1012403";
//
//        adminTask.setCompanyId(companyId);
//        adminTask.setId(taskId);
//
//        Mockito.when(onEmpRepo.getBelongSnapId(companyId, taskUserId)).thenReturn(snapId);
//        ResultRankInstance instance = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.dmsvc/DetaRankDmSvcTest/instance.json", ResultRankInstance.class);
//        Mockito.when(onEmpRepo.getResultRankInstance(companyId, snapId)).thenReturn(instance);
//
//        EvalUser taskUser = new EvalUser();
//        taskUser.setId(taskUserId);
//        taskUser.setIsNewEmp(0);
//        DetaLevelByScoreComputeDmSvc dmSvc = new DetaLevelByScoreComputeDmSvc(adminTask, taskUser);
//        Assert.assertTrue(dmSvc.createLevelByScore(onEmpRepo));//需要进入排名
//
//
//        Mockito.when(onEmpRepo.getEvalStatusCount(companyId, taskId))
//                .thenReturn(new OnTaskStatusCount(snapId, 1, 2, 0))
//                .thenReturn(new OnTaskStatusCount(snapId, 0, 2, 0));
//
//        Assert.assertTrue(!dmSvc.needComputeOnCount(onEmpRepo));//还不需要启动排名
//        Assert.assertTrue(dmSvc.needComputeOnCount(onEmpRepo));//需要启动排名
//        RankCompute rankCompute = dmSvc.computeLevelByRank(onTaskRepo, onEmpRepo, empRuleRepo);
//        Assert.assertNotNull(rankCompute);
//    }

    TaskUserRepo userRepo = Mockito.mock(TaskUserRepo.class);
    EmpEvalRuleRepo empRuleRepo = Mockito.mock(EmpEvalRuleRepo.class);

    @Test
    @DisplayName("无需要进入排名状态")
    public void needOnLevelStageSkip() {
        AdminTask adminTask = new AdminTask();
        TenantId companyId = new TenantId("5a031297-1b38-48ae-bc82-375849835203");
        adminTask.setCompanyId(companyId);
        adminTask.setId("1637601");
        String taskUserId = "1284402";
        EvalUser taskUser = new EvalUser();
        taskUser.setId(taskUserId);
        taskUser.setIsNewEmp(0);
        DetaLevelByScoreComputeDmSvc dmSvc = new DetaLevelByScoreComputeDmSvc(adminTask, taskUser);
        String snapId = "1012403";
        Mockito.when(onEmpRepo.getBelongSnapId(companyId, taskUserId)).thenReturn(snapId);
        ResultRankInstance instance = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.dmsvc/DetaRankDmSvcTest/instance.json", ResultRankInstance.class);
        instance.setLevelDefType(1);//改为按分数生成等级
        Mockito.when(onEmpRepo.getResultRankInstance(companyId, snapId)).thenReturn(instance);
        dmSvc.initSnap(onEmpRepo);
        Assert.assertTrue(!dmSvc.createLevelByScore(onEmpRepo));//不需要排名

        EmpEvalMerge t = new EmpEvalMerge();
        t.setCompanyId(companyId);
        Mockito.when(empRuleRepo.getEmpEvalMerge(companyId, taskUserId, EmpEvalMerge.all)).thenReturn(t);
        EvalUser t1 = new EvalUser();
        t1.setCompanyId(companyId);
        Mockito.when(userRepo.getBaseTaskUser(companyId, taskUserId)).thenReturn(t1);
        new BaseEvent().setPublisher(Mockito.mock(DomainEventPublisherI.class));
        ThisStageEnded thisStageEnded = dmSvc.skipOnLevel(empRuleRepo);
        Assert.assertNotNull(thisStageEnded);
        Assert.assertTrue(thisStageEnded.getCurStatus() == TalentStatus.ON_LEVEL);
    }


    @Test
    public void computeLevelByScore() {

    }

}