package com.polaris.kpi.eval.domain.task.entity.grade;

import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import java.math.BigDecimal;

//@RunWith(org.junit.platform.runner.JUnitPlatform.class)
//@RunWith(Parameterized.class)
public class CheckRateTest {

    @ParameterizedTest
    @CsvSource({
            ">=,20,21,,",
            ">=,20,20,,",
            ">=,20,10,-10,-9.80",

            ">,20,21,,",
            ">,20,20,0,0.00",
            ">,20,10,-10,-9.80",

            "<=,20,21,1,0.98",
            "<=,20,20,,",
            "<=,20,10,,",

            "<,20,21,1,0.98",
            "<,20,20,0,0.00",
            "<,20,10,,"
    })
    public void doCheckRate(String op, Integer ruleRate, Integer realRate, Integer exceptRange, String empCnt) {
        CheckRate checkRate = new CheckRate(new BigDecimal(ruleRate));
        checkRate.setOp(op);
        checkRate = checkRate.doCheckRate(98, 0, new BigDecimal(realRate), null);
        if (exceptRange == null) {
            Assert.assertNull(checkRate.getExceptRange());
            Assert.assertNull(checkRate.getExceptCnt());
        } else {
            Assert.assertEquals("比例不符合", new BigDecimal(exceptRange), checkRate.getExceptRange());
            Assert.assertEquals("异常人数不符合", new BigDecimal(empCnt), checkRate.getExceptCnt());

            Assert.assertTrue("比例不符合", new BigDecimal(exceptRange).compareTo(checkRate.getExceptRange()) == 0);
            Assert.assertTrue("异常人数不符合", new BigDecimal(empCnt).compareTo(checkRate.getExceptCnt()) == 0);
        }
    }

    @ParameterizedTest
    @CsvSource({
            "<=,20,43,,"
    })
    public void doCheckRate2(String op, Integer ruleRate, Integer realRate, Integer exceptRange, String empCnt) {
        CheckRate checkRate = new CheckRate(new BigDecimal(ruleRate));
        checkRate.setOp(op);
        checkRate = checkRate.doCheckRate(7, 0, new BigDecimal(realRate), null);
        if (exceptRange == null) {
            Assert.assertNull(checkRate.getExceptRange());
            Assert.assertNull(checkRate.getExceptCnt());
        } else {
            Assert.assertEquals("比例不符合", new BigDecimal(exceptRange), checkRate.getExceptRange());
            Assert.assertEquals("异常人数不符合", new BigDecimal(empCnt), checkRate.getExceptCnt());

            Assert.assertTrue("比例不符合", new BigDecimal(exceptRange).compareTo(checkRate.getExceptRange()) == 0);
            Assert.assertTrue("异常人数不符合", new BigDecimal(empCnt).compareTo(checkRate.getExceptCnt()) == 0);
        }
    }


    @ParameterizedTest
    @CsvSource({
            ">=,20,21,,",
            ">=,20,20,,",
            ">=,20,10,-10,-10.00",

            ">,20,21,,",
            ">,20,20,0,0.00",
            ">,20,10,-10,-10.00",

            "<=,20,21,1,1.00",
            "<=,20,20,,",
            "<=,20,10,,",

            "<,20,21,1,1.00",
            "<,20,20,0,0.00",
            "<,20,10,,"
    })
    public void doCheckRateWith100(String op, Integer ruleRate, Integer realRate, Integer exceptRange, String empCnt) {
        CheckRate checkRate = new CheckRate(new BigDecimal(ruleRate));
        checkRate.setOp(op);
        checkRate.doCheckRate(100, 0, new BigDecimal(realRate), null);
        if (exceptRange == null) {
            Assert.assertNull(checkRate.getExceptRange());
            Assert.assertNull(checkRate.getExceptCnt());
        } else {
            Assert.assertEquals("比例不符合", new BigDecimal(exceptRange), checkRate.getExceptRange());
            Assert.assertEquals("异常人数不符合", new BigDecimal(empCnt), checkRate.getExceptCnt());

            Assert.assertTrue("比例不符合", new BigDecimal(exceptRange).compareTo(checkRate.getExceptRange()) == 0);
            Assert.assertTrue("异常人数不符合", new BigDecimal(empCnt).compareTo(checkRate.getExceptCnt()) == 0);
        }
    }
}