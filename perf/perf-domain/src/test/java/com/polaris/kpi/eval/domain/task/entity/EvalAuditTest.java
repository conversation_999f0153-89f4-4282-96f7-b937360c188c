package com.polaris.kpi.eval.domain.task.entity;

import cn.com.polaris.kpi.eval.Rater;
import cn.com.polaris.kpi.eval.LevelManager;
import org.junit.Assert;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

public class EvalAuditTest {

    @Test
    public void parseRaters() {
        LevelManager levelManager = new LevelManager();
        levelManager.setSOrgId("52ad7c9f-82d7-432e-b9fe-8a60f3317032");
        levelManager.setPOrgId("ee7cefc3-bd75-43a7-af0e-3f69c66d5e5c");
        levelManager.setManagerId("7f053b21-615d-4509-9501-5d792e7294f9");
        levelManager.setManagerName("陈亚东");
        levelManager.setLevel(2);


        EvalAudit evalAudit = new EvalAudit();
        evalAudit.setCreatedUser("admin");

        evalAudit.setApprovalOrder(1);
        evalAudit.setApproverInfo("1");
        evalAudit.setApproverType("manager");


        evalAudit.setVacancyApproverType("superior");
        evalAudit.setLevelManagers(Arrays.asList(levelManager));
        //List<Rater> raters = evalAudit.parseRaters();
        //Assert.assertTrue(raters.get(0).getEmpId().equals("7f053b21-615d-4509-9501-5d792e7294f9"));
        //System.out.println(raters);
    }

    @Test
    public void parseRatersAdmin() {
        LevelManager levelManager = new LevelManager();
        levelManager.setSOrgId("52ad7c9f-82d7-432e-b9fe-8a60f3317032");
        levelManager.setPOrgId("ee7cefc3-bd75-43a7-af0e-3f69c66d5e5c");
        levelManager.setManagerId("7f053b21-615d-4509-9501-5d792e7294f9");
        levelManager.setManagerName("陈亚东");
        levelManager.setLevel(2);


        EvalAudit evalAudit = new EvalAudit();
        evalAudit.setCreatedUser("adminId");

        evalAudit.setApprovalOrder(1);
        evalAudit.setApproverInfo("1");
        evalAudit.setApproverType("manager");


        evalAudit.setVacancyApproverType("admin");
        evalAudit.setLevelManagers(Arrays.asList(levelManager));
        //List<Rater> raters = evalAudit.parseRaters();
        //Assert.assertTrue(raters.get(0).getEmpId().equals("adminId"));
        //System.out.println(raters);
    }

}