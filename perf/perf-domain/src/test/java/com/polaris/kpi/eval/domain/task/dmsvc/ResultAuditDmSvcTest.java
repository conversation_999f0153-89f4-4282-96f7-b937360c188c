package com.polaris.kpi.eval.domain.task.dmsvc;

import cn.com.polaris.kpi.eval.ResultAuditReviewers;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.entity.EvalAudit;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.setting.domain.entity.*;
import com.polaris.kpi.setting.domain.repo.ResultAuditFlowRepo;
import com.polaris.sdk.common.JsonFileTool;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

class ResultAuditDmSvcTest {

    public static String companyId = "5a031297-1b38-48ae-bc82-375849835203";

    public static String taskUserId = "1280601";

    @Autowired
    private ResultAuditFlowRepo resultAuditFlowRepo = new ResultAuditFlowRepo() {
        @Override
        public void saveAuditFlow(ResultAuditFlow flow, EvalUser evalUser) {

        }

        @Override
        public void fixSaveAuditFlow(ResultAuditFlow flow, EvalUser evalUser) {

        }

        @Override
        public void updateAuditNodeRater(String companyId, String nodeRaterId, String auditEmpId, Integer level, Integer status) {

        }

        @Override
        public void refreshAuditFlow(ResultAuditFlow flow, EvalUser evalUser) {

        }

        @Override
        public void refreshAuditFlow(String companyId, String opEmpId, List<EvalUser> evalUsers) {

        }

        @Override
        public void saveAuditFlowV2(EvalUser evalUser, List<ResultAuditFlowUser> flowUsers) {

        }

        @Override
        public void updateAuditFlow(ResultAuditFlow flow, Integer node) {

        }

        @Override
        public void updateOldAuditFlow(ResultAuditFlow flow) {

        }

        @Override
        public void deleteAllFlow(String companyId,List<String> taskUserId) {

        }

        @Override
        public void deleteAllFlowV2(String companyId, String taskId) {

        }

        @Override
        public void rejectAuditFlow(List<String> taskUserIds, Integer orderNode) {

        }

        @Override
        public ResultAuditFlow getResultAuditFlow(String companyId, String taskId, List<String> clearTaskUserIds, boolean isRefresh, List<EvalScoreResult> curLevelRs) {
            return null;
        }

        @Override
        public ResultAuditFlow getResultAuditFlowV2(String companyId, String taskId, List<String> clearTaskUserIds) {
            ResultAuditFlow flow = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.dmsvc/getResultAuditFlowV2.json", ResultAuditFlow.class);
            return flow;
        }

        @Override
        public ResultAuditFlow getResultAuditFlowByUserId(String companyId, String taskUserId) {
            ResultAuditFlow flow = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.dmsvc/getResultAuditFlowByUserId_result.json", ResultAuditFlow.class);
            return flow;
        }

        @Override
        public List<ResultAuditFlowUser> listFlowUserByTaskId(String companyId, String taskId) {
            List<ResultAuditFlowUser> flowUsers = JsonFileTool.toList("com.polaris.kpi.eval.domain.task.dmsvc/listFlowUserByTaskId.flowUsers.json", ResultAuditFlowUser.class);
            return flowUsers;
        }

        @Override
        public TaskResultAuditSummary getSummary(String companyId, String taskId, String adminEmpId, Integer level) {
            TaskResultAuditSummary summary = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.dmsvc/getSummary.json", TaskResultAuditSummary.class);
            return summary;
        }

        @Override
        public List<TaskResultAuditSummary> listSummaryByTaskId(String companyId, String taskId) {
            return null;
        }

        @Override
        public List<TaskResultAuditSummary> listSummaryByTaskId(String companyId, String taskId, String auditEmpId) {
            return null;
        }

        @Override
        public void saveTaskResultAuditSummary(List<TaskResultAuditSummary> taskResultAuditSummarys) {

        }

        @Override
        public int updateSummary(TaskResultAuditSummary auditSummary) {
            return 0;
        }

        @Override
        public void delTaskResultAuditSummary(String companyId, String taskId) {

        }

        @Override
        public void removeResultAuditFlow(String companyId, String taskId, String taskUserId) {

        }

        @Override
        public void refreshSummary(String companyId, String taskId) {

        }

        @Override
        public void rejectRefreshSummary(String companyId, String taskId) {

        }

        @Override
        public ListWrap<ResultAuditReviewers> reviewersListWrap(TenantId companyId, List<String> taskUserIds) {
            return null;
        }

        @Override
        public Map<String, TaskResultAuditSummary> listByEmpIdSummaryAsMap(String companyId, List<String> taskIds, String adminEmpId) {
            return null;
        }

        @Override
        public void addResultSummary(String companyId, EvalAudit evalAudit) {

        }
    };


    @Test
    void loadResultAuditFlow() {
        System.out.println(JSONUtil.toJsonStr(resultAuditFlowRepo.getResultAuditFlowV2("a","a",null)));
    }

    @Test
    void initNewResultAuditFlow() {
        List<ResultAuditFlowUser> flowUsers = resultAuditFlowRepo.listFlowUserByTaskId("5a031297-1b38-48ae-bc82-375849835203", "1634001");
        ResultAuditFlowDmSvc flowDmSvc = new ResultAuditFlowDmSvc("5a031297-1b38-48ae-bc82-375849835203", "1634001",null);
        flowDmSvc.loadResultAuditFlow(resultAuditFlowRepo);
        flowDmSvc.initNewResultAuditFlow(flowUsers);
        System.out.println(JSONUtil.toJsonStr(flowDmSvc.getTaskResultAuditSummaries()));
        Assert.assertTrue(Objects.equals(JSONUtil.toJsonStr(flowDmSvc.getTaskResultAuditSummaries()),"[{\"level\":1,\"finishCnt\":0,\"version\":0,\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"totalCnt\":2,\"isDeleted\":\"false\",\"auditEmpId\":\"1069028\",\"taskId\":\"1634001\"},{\"level\":2,\"finishCnt\":0,\"version\":0,\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"totalCnt\":2,\"isDeleted\":\"false\",\"auditEmpId\":\"1602589\",\"taskId\":\"1634001\"}]"));
    }

    @Test
    void getTaskResultAuditSummaries() {
    }

//    @Test
//    void start() {
//        EvalUser evalUser = JSONUtil.toBean("{\"empId\":\"1068035\",\"allScored\":\"true\",\"cycleId\":\"1164902\",\"stepId\":\"2004001\",\"refAsk360Flag\":false,\"orgId\":\"a03b54a8-2c86-4dba-ba74-b28d806f9fab\",\"allAutoCompute\":false,\"isAllAutoType\":true,\"atOrgCodePath\":\"|a03b54a8-2c86-4dba-ba74-b28d806f9fab|\",\"isDeleted\":\"false\",\"finalPeerScore\":100,\"tempTask\":0,\"empName\":\"柴文沁\",\"isVacancySkipRater\":false,\"createdTime\":1724930986000,\"id\":\"1281101\",\"perfCoefficient\":\"1.5\",\"sendMsg\":true,\"taskStatus\":\"resultsAuditing\",\"lastScoreComment\":\"\",\"createdUser\":\"1009187\",\"updatedTime\":1724931353000,\"finalScore\":200,\"empOrgName\":\"绩效测试公司\",\"originalEvaluationLevel\":\"卓越\",\"atOrgNamePath\":\"绩效测试公司\",\"taskScoreStartTime\":1724931237000,\"evaluationLevel\":\"卓越\",\"scoreRanges\":[{\"max\":200000,\"fieldJson\":\"[]\",\"minAppendEqual\":0,\"stepId\":\"2004001\",\"version\":0,\"coeffType\":1,\"scoreRuleId\":\"1000006\",\"min\":90,\"isDeleted\":\"false\",\"stepName\":\"卓越\",\"rate\":-1,\"createdTime\":1724256000000,\"id\":\"2079815\",\"perfCoefficient\":\"1.5\",\"place\":4,\"scoreRuleName\":\"系统默认\"},{\"max\":90,\"fieldJson\":\"[{\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"考核任务得分\\\",\\\"companyFieldId\\\":\\\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"考核任务得分\\\",\\\"value\\\":0,\\\"yVal\\\":true},{\\\"label\\\":\\\"×\\\",\\\"value\\\":\\\"*\\\",\\\"symbol\\\":true},{\\\"label\\\":0,\\\"value\\\":0},{\\\"label\\\":\\\".\\\",\\\"value\\\":\\\".\\\"},{\\\"label\\\":0,\\\"value\\\":0},{\\\"label\\\":1,\\\"value\\\":1}]\",\"minAppendEqual\":0,\"stepId\":\"1000005\",\"version\":0,\"coeffType\":1,\"scoreRuleId\":\"1000006\",\"min\":80,\"isDeleted\":\"false\",\"stepName\":\"优秀\",\"createdTime\":1724256000000,\"id\":\"2079816\",\"perfCoefficient\":\"1.2\",\"place\":4,\"scoreRuleName\":\"系统默认\"},{\"max\":80,\"fieldJson\":\"[{\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"考核任务得分\\\",\\\"companyFieldId\\\":\\\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"考核任务得分\\\",\\\"value\\\":0,\\\"yVal\\\":true},{\\\"label\\\":\\\"×\\\",\\\"value\\\":\\\"*\\\",\\\"symbol\\\":true},{\\\"label\\\":0,\\\"value\\\":0},{\\\"label\\\":\\\".\\\",\\\"value\\\":\\\".\\\"},{\\\"label\\\":0,\\\"value\\\":0},{\\\"label\\\":1,\\\"value\\\":1}]\",\"minAppendEqual\":0,\"stepId\":\"1000006\",\"version\":0,\"coeffType\":1,\"scoreRuleId\":\"1000006\",\"min\":70,\"isDeleted\":\"false\",\"stepName\":\"良好\",\"createdTime\":1724256000000,\"id\":\"2079817\",\"perfCoefficient\":\"1\",\"place\":4,\"scoreRuleName\":\"系统默认\"},{\"max\":70,\"fieldJson\":\"[{\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"考核任务得分\\\",\\\"companyFieldId\\\":\\\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"考核任务得分\\\",\\\"value\\\":0,\\\"yVal\\\":true},{\\\"label\\\":\\\"×\\\",\\\"value\\\":\\\"*\\\",\\\"symbol\\\":true},{\\\"label\\\":0,\\\"value\\\":0},{\\\"label\\\":\\\".\\\",\\\"value\\\":\\\".\\\"},{\\\"label\\\":0,\\\"value\\\":0},{\\\"label\\\":1,\\\"value\\\":1}]\",\"minAppendEqual\":0,\"stepId\":\"1000007\",\"version\":0,\"coeffType\":1,\"scoreRuleId\":\"1000006\",\"min\":0,\"isDeleted\":\"false\",\"stepName\":\"合格\",\"createdTime\":1724256000000,\"id\":\"2079818\",\"perfCoefficient\":\"0.8\",\"place\":4,\"scoreRuleName\":\"系统默认\"}],\"avatar\":\"https://static-legacy.dingtalk.com/media/lQDPD2_BJZydZRvNAZnNAZmwAObPuS5cJ7EFdvldbjGQAA_409_409.jpg\",\"originalFinalScore\":200,\"reviewersJson\":[{\"empId\":\"1016001\",\"avatar\":\"https://static-legacy.dingtalk.com/media/lADPDgQ9rZ6XW3HNAkPNAeo_490_579.jpg\",\"exUserId\":\"0609271322699895\",\"empName\":\"吴萧\",\"status\":\"on_the_job\"},{\"empId\":\"12627917-53e2-4604-9665-5ad204778882\",\"avatar\":\"\",\"exUserId\":\"21541832661165519\",\"jobnumber\":\"\",\"empName\":\"罗伟飞\",\"status\":\"on_the_job\"},{\"empId\":\"1602582\",\"avatar\":\"https://static-legacy.dingtalk.com/media/lADPM5HikxN4fzvNA6PNA-4_1006_931.jpg\",\"exUserId\":\"056410084579655806\",\"jobnumber\":\"\",\"empName\":\"Sasha\",\"status\":\"on_the_job\"},{\"empId\":\"1602589\",\"avatar\":\"https://static-legacy.dingtalk.com/media/lADPM5HikoKm7UnNAWPNAUQ_324_355.jpg\",\"exUserId\":\"01112841511626225388\",\"jobnumber\":\"CS1999\",\"empName\":\"杨思威\",\"status\":\"on_the_job\"}],\"updatedUser\":\"1009187\",\"version\":28,\"hasAskEndScore\":false,\"originalPerfCoefficient\":\"1.5\",\"companyId\":{\"id\":\"5a031297-1b38-48ae-bc82-375849835203\"},\"resetRaterNameIds\":[],\"ruleConfStatus\":200,\"inputFinishStatus\":1,\"atOrgPathHight\":1,\"scoreEndFlag\":true,\"isNewEmp\":0,\"taskId\":\"1632809\",\"infos\":[],\"empEvalRule\":{\"indexRaters\":[{\"node\":\"peer\",\"raters\":[{\"empId\":\"1009187\",\"weight\":100,\"type\":0,\"empName\":\"李志旭\"}]}],\"initiator\":\"1009187\",\"editStatus\":0,\"scoreConf\":{\"transferFlag\":\"true\",\"multiType\":\"or\"},\"auditResult\":{\"transferFlag\":\"true\",\"vacancyApproveInfo\":\"\",\"commentReq\":0,\"vacancyApproveType\":\"superior\",\"empRepeatSkip\":1,\"notFindAdminBySupAdmin\":1,\"collectSendNotify\":2,\"nodeEmpVacancy\":1,\"auditNodes\":[{\"approvalOrder\":1,\"approverInfo\":\"1\",\"approverName\":\"直属主管\",\"node\":\"final_result_audit\",\"raters\":[{\"empId\":\"1069028\",\"level\":1,\"avatar\":\"https://static-legacy.dingtalk.com/media/lADPM3yRn5N00wrNAtXNAtU_725_725.jpg\",\"type\":1,\"dingUserId\":\"022065193419-2046032772\",\"empName\":\"苏华（客户成功）\"}],\"multiType\":\"or\",\"approverType\":\"manager\"},{\"approvalOrder\":2,\"approverInfo\":\"2\",\"approverName\":\"二级主管\",\"node\":\"final_result_audit\",\"raters\":[{\"empId\":\"1009187\",\"avatar\":\"https://static-legacy.dingtalk.com/media/lQLPDhtwSlDwYpfNAmDNAmCwP2waI3J1qnYCiKZNDcDTAA_608_608.png\",\"type\":0,\"dingUserId\":\"493267573826201284\",\"empName\":\"李志旭\"}],\"multiType\":\"or\",\"approverType\":\"manager\"},{\"approvalOrder\":3,\"approverInfo\":\"1000003\",\"approverName\":\"主管理员\",\"node\":\"final_result_audit\",\"raters\":[{\"empId\":\"1016001\",\"roleId\":\"1000003\",\"avatar\":\"https://static-legacy.dingtalk.com/media/lADPDgQ9rZ6XW3HNAkPNAeo_490_579.jpg\",\"type\":0,\"dingUserId\":\"0609271322699895\",\"empName\":\"吴萧\",\"roleName\":\"主管理员\"},{\"empId\":\"12627917-53e2-4604-9665-5ad204778882\",\"roleId\":\"1000003\",\"avatar\":\"\",\"type\":0,\"dingUserId\":\"21541832661165519\",\"empName\":\"罗伟飞\",\"roleName\":\"主管理员\"},{\"empId\":\"1602582\",\"roleId\":\"1000003\",\"avatar\":\"https://static-legacy.dingtalk.com/media/lADPM5HikxN4fzvNA6PNA-4_1006_931.jpg\",\"type\":0,\"dingUserId\":\"056410084579655806\",\"empName\":\"Sasha\",\"roleName\":\"主管理员\"},{\"empId\":\"1602589\",\"roleId\":\"1000003\",\"avatar\":\"https://static-legacy.dingtalk.com/media/lADPM5HikoKm7UnNAWPNAUQ_324_355.jpg\",\"type\":0,\"dingUserId\":\"01112841511626225388\",\"empName\":\"杨思威\",\"roleName\":\"主管理员\"}],\"multiType\":\"or\",\"approverType\":\"role\"},{\"approvalOrder\":4,\"approverInfo\":\"1024060\",\"approverName\":\"吴武\",\"node\":\"final_result_audit\",\"raters\":[{\"empId\":\"1024060\",\"type\":0,\"empName\":\"吴武\"}],\"multiType\":\"or\",\"approverType\":\"user\"}],\"multiType\":\"or\",\"vacancyApproveName\":\"\",\"open\":1},\"finishValueAudit\":{\"empRepeatSkip\":0,\"notFindAdminBySupAdmin\":1,\"nodeEmpVacancy\":2,\"auditNodes\":[],\"open\":0},\"s3AppointRater\":{\"signatureFlag\":false,\"auditNodes\":[],\"open\":0},\"totalLevelRaters\":[],\"s3SuperRater\":{\"superiorScoreOrder\":\"sameTime\",\"signatureFlag\":false,\"auditNodes\":[],\"open\":0},\"isDeleted\":\"false\",\"ruleName\":\"李志旭-简易模版\",\"createdTime\":1724931105000,\"typeWeightConf\":{\"checkItemWeight\":1,\"limit100Weight\":0,\"itemWeightLimit\":100,\"open\":0},\"enterScore\":{\"enterScoreEmpType\":1,\"enterScoreMethod\":\"auto\",\"scoreStartRuleType\":\"before\",\"scoreStartRuleDay\":1},\"createdUser\":\"1009187\",\"updatedTime\":1724989000000,\"confirmTask\":{\"openConfirmLT\":0,\"empRepeatSkip\":0,\"notFindAdminBySupAdmin\":1,\"nodeEmpVacancy\":2,\"auditNodes\":[],\"modifyItemDimension\":\"all\",\"open\":0},\"s3SelfRater\":{\"signatureFlag\":false,\"open\":0},\"interviewConf\":{\"empRepeatSkip\":0,\"notFindAdminBySupAdmin\":1,\"nodeEmpVacancy\":2,\"open\":0},\"appealConf\":{\"open\":0},\"deadLineConf\":{\"open\":0},\"scoreValueConf\":{\"baseScore\":100,\"exceedFullScore\":false,\"scoreRangeType\":\"fullScore\"},\"createTotalLevelType\":1,\"levelGroupId\":\"\",\"indicatorCnt\":2,\"version\":0,\"evaluateType\":\"simple\",\"scoreView\":{\"mutualScoreAnonymous\":\"true\",\"mutualScoreViewRule\":{\"superior\":\"\",\"mutual\":\"\",\"appoint\":\"\",\"examinee\":\"\"},\"superiorScoreAnonymous\":\"true\",\"appointScoreViewRule\":{\"superior\":\"\",\"mutual\":\"\",\"appoint\":\"\",\"examinee\":\"\"},\"superiorScoreViewRule\":{\"superior\":\"score,attach\",\"mutual\":\"score,attach\",\"appoint\":\"score,attach\",\"examinee\":\"score,attach\"},\"selfScoreViewRule\":{\"superior\":\"\",\"mutual\":\"\",\"appoint\":\"\",\"examinee\":\"score,attach\"},\"appointScoreAnonymous\":\"true\"},\"companyId\":{\"id\":\"5a031297-1b38-48ae-bc82-375849835203\"},\"publishResult\":{\"toEmps\":[{\"objType\":\"emp\"}],\"type\":\"afterFinished\",\"scoreDetailPriv\":{\"scoreRemark\":[\"self_remark\",\"item_remark\",\"peer_remark\",\"sub_remark\",\"superior_remark\",\"appoint_remark\"],\"scoreType\":[]},\"opEmps\":[],\"toDetailEmps\":[],\"dimension\":15,\"open\":1},\"s3SubRater\":{\"change\":false,\"excludeAllManager\":false,\"raters\":[],\"signatureFlag\":false,\"open\":0},\"s3PeerRater\":{\"transferFlag\":\"true\",\"nodeWeight\":100,\"approvalOrder\":1,\"change\":false,\"rateMode\":\"item\",\"approverInfo\":\"1009187\",\"node\":\"peer_score\",\"excludeAllManager\":false,\"raters\":[{\"empId\":\"1009187\",\"weight\":100,\"type\":0,\"empName\":\"李志旭\"}],\"signatureFlag\":false,\"multiType\":\"and\",\"approverType\":\"user\",\"scorerNumCof\":{\"action\":\"regular\",\"open\":\"false\"},\"open\":1},\"confirmResult\":{\"auto\":0,\"sign\":0,\"open\":0},\"editExeIndi\":{\"empRepeatSkip\":0,\"notFindAdminBySupAdmin\":1,\"nodeEmpVacancy\":2,\"auditNodes\":[],\"open\":0},\"askIndexRaters\":[],\"commentConf\":{\"scoreSummarySwitch\":-1,\"commentFlag\":\"notRequired\",\"plusOrSubComment\":0},\"showResultType\":14,\"empEvalId\":\"1281101\"}}\n",EvalUser.class);
//        ResultAuditDmSvc dmSvc = new ResultAuditDmSvc(evalUser,3,companyId,"9999");
//        dmSvc.setRepo(resultAuditFlowRepo);
//        dmSvc.executeStart("1602582");
//    }

    @Test
    void executeUniteResultAudit() {
        ResultAuditFlow resultAuditFlow = resultAuditFlowRepo.getResultAuditFlowByUserId(companyId, taskUserId);
        if (Objects.isNull(resultAuditFlow)) {
            return;
        }
        resultAuditFlow.updateFlowStatus("1602582",4);
        Assert.assertTrue(Objects.equals(JSONUtil.toJsonStr(resultAuditFlow.getCurInstance().getFlowNodes()),JSONUtil.toJsonStr(JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.dmsvc/getResultAuditFlowByUserId_result.json", ResultAuditFlow.class).getCurInstance().getFlowNodes())));

    }

    @Test
    public void refreshSummaryFinishCnt() {
        TaskResultAuditSummary summary = resultAuditFlowRepo.getSummary(companyId, "1632809", "1602582", 4);
        //修改概要信息
        summary.mark("1602582",4);
        Assert.assertTrue(Objects.equals(JSONUtil.toJsonStr(summary),"{\"updatedTime\":1724899104000,\"level\":4,\"finishCnt\":1,\"version\":0,\"companyId\":\"\",\"totalCnt\":1,\"raters\":[{\"updatedTime\":1724899104000,\"flowNodeId\":\"1029104\",\"level\":4,\"taskUserId\":\"1280601\",\"version\":0,\"flowInstanceId\":\"1025901\",\"flowUsers\":[],\"companyId\":\"\",\"isDeleted\":\"false\",\"auditEmpId\":\"1602582\",\"createdTime\":1724899104000,\"id\":\"1029104\",\"createdUser\":\"1009187\",\"status\":0}],\"isDeleted\":\"false\",\"auditEmpId\":\"1602582\",\"createdTime\":1724899104000,\"id\":\"1001901\",\"taskId\":\"1632809\"}"));
    }

    @Test
    public void initSendRater() {
        TaskResultAuditSummary summary = JSONUtil.toBean("{\"updatedTime\":1724899104000,\"level\":4,\"finishCnt\":1,\"version\":0,\"companyId\":\"\",\"totalCnt\":1,\"raters\":[{\"updatedTime\":1724899104000,\"flowNodeId\":\"1029104\",\"level\":4,\"taskUserId\":\"1280601\",\"version\":0,\"flowInstanceId\":\"1025901\",\"flowUsers\":[],\"companyId\":\"\",\"isDeleted\":\"false\",\"auditEmpId\":\"1602582\",\"createdTime\":1724899104000,\"id\":\"1029104\",\"createdUser\":\"1009187\",\"status\":0}],\"isDeleted\":\"false\",\"auditEmpId\":\"1602582\",\"createdTime\":1724899104000,\"id\":\"1001901\",\"taskId\":\"1632809\"}",TaskResultAuditSummary.class);
        if (Objects.isNull(summary)) {
            return;
        }
        List<ResultAuditFlowNodeRater> sendRater = new ArrayList<>();
        if (summary.needFinish()) {
            sendRater = summary.getRaters();
        }
        Assert.assertTrue(CollUtil.isNotEmpty(sendRater));
    }
}