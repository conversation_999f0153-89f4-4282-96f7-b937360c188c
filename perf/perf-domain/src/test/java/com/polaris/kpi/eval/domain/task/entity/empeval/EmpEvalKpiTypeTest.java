package com.polaris.kpi.eval.domain.task.entity.empeval;

import cn.com.polaris.kpi.eval.RaterNode;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.sdk.common.JsonFileTool;
import org.junit.Test;

import java.net.URL;
import java.util.List;

public class EmpEvalKpiTypeTest {

    @Test
    public void itemScoreRuleAtType() {

        URL json = EmpEvalKpiTypeTest.class.getClassLoader().getResource("com.polaris.kpi.eval.domain.task.entity.empeval/ItemScoreRuleAtType.json");
        System.out.println("json:" + json);
        String ruleStr = FileUtil.readString(json.getPath(), "utf-8");
        EmpEvalKpiType.ItemScoreRuleAtType ruleAtType = JSON.parseObject(ruleStr, EmpEvalKpiType.ItemScoreRuleAtType.class);
        System.out.println(JSON.toJSONString(ruleAtType));
    }

    @Test
    public void allSelfRater() {
        EvalUser evalUser  = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.entity.empeval/EvalUserTest/EvalUserAutoEnterScore4.json", EvalUser.class);
        List<EmpEvalKpiType> kpiTypes = evalUser.getKpiTypes();
        for(EmpEvalKpiType kpiType : kpiTypes){
            RaterNode node = kpiType.allSelfRater();
            System.out.println(JSON.toJSONString(node));
        }
    }
}