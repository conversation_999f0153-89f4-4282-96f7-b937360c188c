package com.polaris.kpi.eval.domain.task.entity;

import cn.com.polaris.kpi.eval.*;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.BaseAuditNode;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.StdNode;
import com.polaris.kpi.eval.domain.task.entity.js.JSElComputer;
import com.polaris.kpi.eval.domain.task.entity.js.JSScriptComputer;
import com.polaris.sdk.type.ListWrap;
import org.jetbrains.annotations.NotNull;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.xidea.el.Expression;
import org.xidea.el.impl.ExpressionImpl;

import java.math.BigDecimal;
import java.util.*;


public class EvalKpiTest {
    @Test
    public void checkPassedByKpiItemFinishValue() {
        EvalKpi evalKpi = new EvalKpi();
        evalKpi.setFinishValue(null);
        evalKpi.setItemFinishValueText("");
        evalKpi.setWorkItemFinishValue("");
        evalKpi.setFinishValueComment("");
        evalKpi.setFiles("");

        String reqjson = "{\"attachment\":{\"desc\":\"附件必填\",\"isReq\":0},\"comment\":{\"desc\":\"备注必填\",\"isReq\":0},\"inputFinishValue\":{\"desc\":\"完成值必填\",\"isReq\":1}}";
        KpiOtherReqField otherReqField = JSONUtil.toBean(reqjson, KpiOtherReqField.class);
        evalKpi.setOtherReqField(otherReqField);
        boolean bool1 = evalKpi.checkPassedByKpiItemFinishValue();
        System.out.println("为空，bool1=" + bool1);
        Assert.assertEquals(bool1, false);

        evalKpi.setItemFinishValueText("23232");
        bool1 = evalKpi.checkPassedByKpiItemFinishValue();
        System.out.println("为空，bool1=" + bool1);
        Assert.assertEquals(bool1, true);


        String reqjson2 = "{\"attachment\":{\"desc\":\"附件必填\",\"isReq\":1},\"comment\":{\"desc\":\"备注必填\",\"isReq\":0},\"inputFinishValue\":{\"desc\":\"完成值必填\",\"isReq\":0}}";
        KpiOtherReqField otherReqField2 = JSONUtil.toBean(reqjson2, KpiOtherReqField.class);
        evalKpi.setOtherReqField(otherReqField2);
        bool1 = evalKpi.checkPassedByKpiItemFinishValue();
        System.out.println("为空，bool1=" + bool1);
        Assert.assertEquals(bool1, false);


        evalKpi.setFiles("111");
        bool1 = evalKpi.checkPassedByKpiItemFinishValue();
        System.out.println("为空，bool1=" + bool1);
        Assert.assertEquals(bool1, true);

        evalKpi.setFiles("");
        String reqjson3 = "{\"attachment\":{\"desc\":\"附件必填\",\"isReq\":0},\"comment\":{\"desc\":\"备注必填\",\"isReq\":1},\"inputFinishValue\":{\"desc\":\"完成值必填\",\"isReq\":0}}";
        KpiOtherReqField otherReqField3 = JSONUtil.toBean(reqjson3, KpiOtherReqField.class);
        evalKpi.setOtherReqField(otherReqField3);
        bool1 = evalKpi.checkPassedByKpiItemFinishValue();
        System.out.println("为空，bool1=" + bool1);
        Assert.assertEquals(bool1, false);

        evalKpi.setFinishValueComment("2342343");
        bool1 = evalKpi.checkPassedByKpiItemFinishValue();
        System.out.println("为空，bool1=" + bool1);
        Assert.assertEquals(bool1, true);

    }
    @Test
    public void checkPassedByKpiItemFinishValue2() {
        EvalKpi evalKpi = new EvalKpi();
        evalKpi.setFinishValue(null);
        evalKpi.setItemFinishValueText("");
        evalKpi.setWorkItemFinishValue("");
        evalKpi.setFinishValueComment("");
        evalKpi.setFiles("");

        String reqjson = "{\"attachment\":{\"desc\":\"附件必填\",\"isReq\":0},\"comment\":{\"desc\":\"备注必填\",\"isReq\":0},\"inputFinishValue\":{\"desc\":\"完成值必填\",\"isReq\":0}}";
        KpiOtherReqField otherReqField = JSONUtil.toBean(reqjson, KpiOtherReqField.class);
        evalKpi.setOtherReqField(otherReqField);
        boolean bool1 = evalKpi.checkPassedByKpiItemFinishValue();
        System.out.println("为空，bool1=" + bool1);
        Assert.assertEquals(bool1, true);

        evalKpi.setItemFinishValueText("23232");
        bool1 = evalKpi.checkPassedByKpiItemFinishValue();
        System.out.println("为空，bool1=" + bool1);
        Assert.assertEquals(bool1, true);
    }

    @Test
    public void checkPassedByKpiItemFinishValue3() {
        EvalKpi evalKpi = new EvalKpi();
        evalKpi.setFinishValue(null);
        evalKpi.setItemFinishValueText("");
        evalKpi.setWorkItemFinishValue("");
        evalKpi.setFinishValueComment("");
        evalKpi.setFiles("");

        String reqjson = "{\"attachment\":{\"desc\":\"附件必填\",\"isReq\":1},\"comment\":{\"desc\":\"备注必填\",\"isReq\":1},\"inputFinishValue\":{\"desc\":\"完成值必填\",\"isReq\":1}}";
        KpiOtherReqField otherReqField = JSONUtil.toBean(reqjson, KpiOtherReqField.class);
        evalKpi.setOtherReqField(otherReqField);
        boolean bool1 = evalKpi.checkPassedByKpiItemFinishValue();
        System.out.println("为空1，bool1=" + bool1);
        Assert.assertEquals(bool1, false);

        evalKpi.setItemFinishValueText("23232");
        bool1 = evalKpi.checkPassedByKpiItemFinishValue();
        System.out.println("为空，bool1=" + bool1);
        Assert.assertEquals(bool1, false);

        evalKpi.setFinishValueComment("2342343");
        bool1 = evalKpi.checkPassedByKpiItemFinishValue();
        System.out.println("为空3，bool1=" + bool1);
        Assert.assertEquals(bool1, false);

        evalKpi.setFiles("23233");
        bool1 = evalKpi.checkPassedByKpiItemFinishValue();
        System.out.println("为空4，bool1=" + bool1);
        Assert.assertEquals(bool1, true);
    }
    @Test
    public void allItemsCheckPassedByKpiItem() {
        EvalKpi evalKpi = new EvalKpi();
        evalKpi.setFinishValue(null);
        evalKpi.setItemFinishValueText("");
        evalKpi.setWorkItemFinishValue("");
        evalKpi.setFinishValueComment("");
        evalKpi.setFiles("");

        String reqjson = "{\"attachment\":{\"desc\":\"附件必填\",\"isReq\":0},\"comment\":{\"desc\":\"备注必填\",\"isReq\":0},\"inputFinishValue\":{\"desc\":\"完成值必填\",\"isReq\":0}}";
        String finishValuejson = "[{\"files\":[],\"finalSubmitFinishValue\":0,\"id\":\"17685045\",\"itemFinishValueText\":\"123\",\"kpiItemId\":\"b11ef560-56b7-40cd-875e-d0b276c0d143\",\"kpiTypeId\":\"1652110\"}]";
        KpiOtherReqField otherReqField = JSONUtil.toBean(reqjson, KpiOtherReqField.class);
        List<FinishValue> finishValues  = JSONUtil.toList(finishValuejson, FinishValue.class);
        evalKpi.setOtherReqField(otherReqField);
        boolean bool1 = evalKpi.allItemsCheckPassedByKpiItem(finishValues);
        System.out.println("为空1，bool1=" + bool1);
        //Assert.assertEquals(bool1, false);

    }
    @Test
    public void computeAuto() {
        EvalKpi evalKpi = new EvalKpi();
        //evalKpi.setFormulaFields(fields);
        //(完成值>=目标值))&&(100))||(((1))&&(完成值/目标值*100))，计算后的结果=100,参数={"目标值":15450000.00,"完成值":7395203.40}
        evalKpi.setItemFormula("(((完成值>=目标值))&&(100))||(((1))&&(完成值/目标值*100))");
        evalKpi.setFormulaFields(new ArrayList<>());
        evalKpi.setItemTargetValue(new BigDecimal(15450000.00));
        evalKpi.setItemFinishValue(new BigDecimal(7395203.40));

        evalKpi.setKpiTypeWeight(new BigDecimal(100));
        evalKpi.setItemWeight(new BigDecimal(15.00));

        evalKpi.computeAuto(false, new BigDecimal(100),true,false);

        BigDecimal itemAutoScore = evalKpi.getItemAutoScore();
        System.out.println(itemAutoScore);
    }

    @Test
    public void computeAutoWeight() {
        EvalKpi evalKpi = new EvalKpi();
        //evalKpi.setFormulaFields(fields);
        //(完成值>=目标值))&&(100))||(((1))&&(完成值/目标值*100))，计算后的结果=100,参数={"目标值":15450000.00,"完成值":7395203.40}
        evalKpi.setItemFormula("(完成值/目标值)*100/指标权重");
        evalKpi.setFormulaFields(new ArrayList<>());
        evalKpi.setItemTargetValue(new BigDecimal(10));
        evalKpi.setItemFinishValue(new BigDecimal(2));

        evalKpi.setKpiTypeWeight(new BigDecimal(100));
        evalKpi.setItemWeight(new BigDecimal(10.00));

        evalKpi.computeAuto(false, new BigDecimal(100),true,true);
        BigDecimal itemAutoScore = evalKpi.getItemAutoScore();
        System.out.println(itemAutoScore);

        evalKpi.computeAuto(true, new BigDecimal(100),false,true);
        itemAutoScore = evalKpi.getItemAutoScore();
        System.out.println(itemAutoScore);


        evalKpi.computeAuto(true, new BigDecimal(100),false,false);
        itemAutoScore = evalKpi.getItemAutoScore();
        System.out.println(itemAutoScore);
    }


    @Test
    public void name() {
        Map<String, Object> param = new HashMap<>();
        String itemFormula = "(((完成值>=目标值))&&(100))||(((1))&&(完成值/目标值*100))";
        param.put("完成值", new BigDecimal(7395203.40));
        param.put("目标值", new BigDecimal(15450000.00));
        Object res = new JSScriptComputer(itemFormula).eval(param);
        System.out.println(res);
    }

    @Test
    public void format() {
        String itemFormula = "(((完成值>=目标值))&&(100))||(((1))&&(完成值/目标值*100))";
        Expression el = new ExpressionImpl(itemFormula);
        HashMap param = new HashMap();
        param.put("完成值", new BigDecimal(7395203.40));
        param.put("目标值", new BigDecimal(15450000.00));
        Object evaluate = el.evaluate(param);
        System.out.println(evaluate);
    }

    @Test
    public void zro() {
        String itemFormula = "(((完成值>=目标值))&&(100))||(((1))&&(完成值/目标值*100))";
        JSElComputer el = new JSElComputer(itemFormula);
        HashMap param = new HashMap();
        param.put("完成值", new BigDecimal(-7395203.40));
        param.put("目标值", new BigDecimal(0));
        Object evaluate = el.eval(param);
        System.out.println(evaluate);
    }

    @Test
    public void zro2() {
        String itemFormula = "(((完成值>=目标值))&&(100))||(((1))&&(完成值/目标值*100))";
        Expression el = new ExpressionImpl(itemFormula);
        HashMap param = new HashMap();
        param.put("完成值", new BigDecimal(-7395203.40));
        param.put("目标值", new BigDecimal(0));
        Object evaluate = el.evaluate(param);
        System.out.println(evaluate);
    }

    @Before
    public void setUp() throws Exception {
    }

    @After
    public void tearDown() throws Exception {
    }

    @Test
    public void alreadyDispatch() {
        //已分发后,重置后,再执行分发
        EvalKpi evalKpi = new EvalKpi();
        ListWrap<EvalScoreResult> waits = new ListWrap<>(new ArrayList<>());
        EvalScoreResult data = buildScoreRs("10001", SubScoreNodeEnum.SUPERIOR_SCORE.getScene(), 1);
        waits.add(data);

        EvalScoreResult data2 = buildScoreRs("10002", SubScoreNodeEnum.SUPERIOR_SCORE.getScene(), 1);
        waits.add(data2);
        evalKpi.setWaitScoresOld(waits.getDatas());


        List<Rater> raters = new ArrayList<>();
        raters.add(new Rater("10001"));
        raters.add(new Rater("10002"));
        List<EvalScoreResult> results = evalKpi.hasWaitDispatchRs(new StdNode(SubScoreNodeEnum.SUPERIOR_SCORE, BaseAuditNode.MULTI_OR, raters));
        Assert.assertEquals(2, results.size());
    }

    @Test
    public void alreadyDispatch2() {
        //已分发后,10002->10001 转交了,重置后,再执行分发
        EvalKpi evalKpi = new EvalKpi();
        List<EvalScoreResult> waits = new ArrayList<>();
        List<Rater> raters = Arrays.asList(new Rater("10002"));


        EvalScoreResult data = buildScoreRs("10001", SubScoreNodeEnum.SUPERIOR_SCORE.getScene(), 1);
        waits.add(data);

        EvalScoreResult data2 = buildScoreRs("10001", SubScoreNodeEnum.SUPERIOR_SCORE.getScene(), 1);
        waits.add(data2);
        evalKpi.setWaitScoresOld(waits);


        List<EvalScoreResult> results = evalKpi.hasWaitDispatchRs(new StdNode(SubScoreNodeEnum.SUPERIOR_SCORE, BaseAuditNode.MULTI_OR, raters));
        Assert.assertEquals(2, results.size());
    }


    @Test
    public void alreadyDispatch3() {
        //同一个人同一指标上有多个环节 已分发后, 区分其它环节 ,重置后,再执行分发
        EvalKpi evalKpi = new EvalKpi();
        List<EvalScoreResult> waits = new ArrayList<>();
        evalKpi.setWaitScoresOld(waits);

        EvalScoreResult data = buildScoreRs("10001", SubScoreNodeEnum.SUPERIOR_SCORE.getScene(), 1);
        waits.add(data);

        EvalScoreResult data2 = buildScoreRs("10001", SubScoreNodeEnum.SUPERIOR_SCORE.getScene(), 1);
        waits.add(data2);

        EvalScoreResult super3 = buildScoreRs("10003", SubScoreNodeEnum.SUPERIOR_SCORE.getScene(), 1);
        waits.add(super3);

        EvalScoreResult peer1 = buildScoreRs("10003", SubScoreNodeEnum.PEER_SCORE.getScene(), 1);
        waits.add(peer1);

        EvalScoreResult peer2 = buildScoreRs("10001", SubScoreNodeEnum.PEER_SCORE.getScene(), 1);
        waits.add(peer2);

        List<Rater> raters = Arrays.asList(new Rater("10002"));

        //分发L1上级评分
        StdNode iNodeConf = new StdNode(SubScoreNodeEnum.SUPERIOR_SCORE, BaseAuditNode.MULTI_OR, raters);
        iNodeConf.setOrder(1);
        List<EvalScoreResult> results = evalKpi.hasWaitDispatchRs(iNodeConf);
        for (EvalScoreResult result : results) {
            System.out.println(String.format("type:%s, order:%s,scorerId:%s", result.getScorerType(), result.getApprovalOrder(), result.getScorerId()));
        }
        Assert.assertEquals(3, results.size());
    }


    @NotNull
    private EvalScoreResult buildScoreRs(String scorerId, String scene, int order) {
        EvalScoreResult data = new EvalScoreResult();
        data.setScorerType(scene);
        data.setScorerId(scorerId);
        data.setApprovalOrder(order);
        return data;
    }

    @Test
    public void compareInputEmp() {
        EvalKpi evalKpi = new EvalKpi();
        evalKpi.setResultInputType("no");
        String packJson = "{\"oldInputEmpIds\":[],\"resultInputEmpIds\":[],\"curInputEmpIds\":[]}";
        String newEvalKpiJson = "{\"alreadyNodes\":[],\"itemRule\":\"做好各开发项目的周边关系，周边民房监测，协调督促项目水、电、电梯、园林等各专业班组施工；积极配合工程施工开展，保证各施工工程正常施工\\n\",\"reserveOkrWeight\":36,\"showFinishBar\":0,\"itemUnit\":\"次\",\"multipleReviewersType\":\"or\",\"taskUserId\":\"1280038\",\"itemScoreValue\":\"{\\\"type\\\":\\\"toMainScore\\\"}\",\"showTargetValue\":\"\",\"formulaType\":1,\"finishValueType\":1,\"createdTime\":1724665909000,\"id\":\"1305220\",\"createdUser\":\"1302001\",\"scorerType\":\"exam\",\"order\":0,\"itemFormula\":\"\",\"updatedTime\":1724665909000,\"thresholdJson\":\"[]\",\"typeOrder\":0,\"plusSubInterval\":{\"max\":\"\",\"min\":\"\"},\"inputFormat\":\"num\",\"version\":0,\"companyId\":{\"id\":\"5a031297-1b38-48ae-bc82-375849835203\"},\"resultInputType\":\"exam\",\"itemFullScoreCfg\":\"false\",\"mustResultInput\":0,\"itemTargetValue\":22,\"itemFieldJson\":\"[{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"完成值\\\",\\\"companyFieldId\\\":\\\"268b0a46-c2bd-493d-aff8-8318aa7b02a8\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"完成值\\\",\\\"value\\\":0,\\\"yVal\\\":true},{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"目标值\\\",\\\"companyFieldId\\\":\\\"e0674594-186f-4aa7-817d-30a2c9cee161\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"目标值\\\",\\\"value\\\":0,\\\"yVal\\\":true},{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"完成值\\\",\\\"companyFieldId\\\":\\\"4ca8ffeb-6239-43e7-b67f-0259ba826184\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"完成值\\\",\\\"value\\\":0,\\\"yVal\\\":true},{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"目标值\\\",\\\"companyFieldId\\\":\\\"1ea75c43-ff00-4ffa-be07-58f44f231a81\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"目标值\\\",\\\"value\\\":0,\\\"yVal\\\":true}]\",\"itemLimitCnt\":{\"max\":\"\",\"openItemLimit\":\"false\",\"min\":\"\"},\"formulaCondition\":\"[{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"完成值\\\",\\\"companyFieldId\\\":\\\"268b0a46-c2bd-493d-aff8-8318aa7b02a8\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"完成值\\\",\\\"value\\\":0,\\\"yVal\\\":true,\\\"kpiItemId\\\":\\\"45a34c79-6354-4909-9c49-24ad4b38a705\\\"},{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"目标值\\\",\\\"companyFieldId\\\":\\\"e0674594-186f-4aa7-817d-30a2c9cee161\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"目标值\\\",\\\"value\\\":0,\\\"yVal\\\":true,\\\"kpiItemId\\\":\\\"45a34c79-6354-4909-9c49-24ad4b38a705\\\"},{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"完成值\\\",\\\"companyFieldId\\\":\\\"4ca8ffeb-6239-43e7-b67f-0259ba826184\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"完成值\\\",\\\"value\\\":0,\\\"yVal\\\":true,\\\"kpiItemId\\\":\\\"45a34c79-6354-4909-9c49-24ad4b38a705\\\"},{\\\"companyId\\\":\\\"5a031297-1b38-48ae-bc82-375849835203\\\",\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"目标值\\\",\\\"companyFieldId\\\":\\\"1ea75c43-ff00-4ffa-be07-58f44f231a81\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"目标值\\\",\\\"value\\\":0,\\\"yVal\\\":true,\\\"kpiItemId\\\":\\\"45a34c79-6354-4909-9c49-24ad4b38a705\\\"}]\",\"empId\":\"1009187\",\"itemType\":\"measurable\",\"scorerObjId\":[{\"objItems\":[],\"objType\":\"role\"},{\"objItems\":[],\"objType\":\"user\"},{\"objItems\":[],\"objType\":\"manager\"}],\"managerLevel\":\"\",\"kpiItemName\":\"因周边情况没有协调好造成对施工工程造成影响的次数\",\"isDeleted\":\"false\",\"finalSubmitFinishValue\":0,\"kpiTypeWeight\":0,\"itemTargetValueText\":\"\",\"inputRole\":[],\"itemWeight\":22,\"scoringRule\":\"造成影响出现一次扣3分，3次全扣\",\"kpiItemId\":\"45a34c79-6354-4909-9c49-24ad4b38a705\",\"maxExtraScore\":0,\"appointAudits\":[],\"openOkrScore\":0,\"isOkr\":\"false\",\"resultInputEmpId\":\"\",\"kpiTypeName\":\"默认考核维度\",\"kpiTypeId\":\"5066c388-f3b2-400e-80b9-55892119bbc6\",\"isNewEmp\":0,\"formulaFields\":[{\"updatedTime\":1724665908000,\"kpiItemId\":\"45a34c79-6354-4909-9c49-24ad4b38a705\",\"formulaFieldValue\":0,\"companyFieldId\":\"268b0a46-c2bd-493d-aff8-8318aa7b02a8\",\"taskUserId\":\"1280038\",\"updatedUser\":\"1302001\",\"version\":0,\"companyId\":{\"id\":\"5a031297-1b38-48ae-bc82-375849835203\"},\"isDeleted\":\"false\",\"formulaFieldName\":\"完成值\",\"createdTime\":1724665908000,\"id\":\"2034161\",\"taskId\":\"1635114\",\"createdUser\":\"1302001\"},{\"updatedTime\":1724665908000,\"kpiItemId\":\"45a34c79-6354-4909-9c49-24ad4b38a705\",\"formulaFieldValue\":0,\"companyFieldId\":\"e0674594-186f-4aa7-817d-30a2c9cee161\",\"taskUserId\":\"1280038\",\"updatedUser\":\"1302001\",\"version\":0,\"companyId\":{\"id\":\"5a031297-1b38-48ae-bc82-375849835203\"},\"isDeleted\":\"false\",\"formulaFieldName\":\"目标值\",\"createdTime\":1724665908000,\"id\":\"2034162\",\"taskId\":\"1635114\",\"createdUser\":\"1302001\"},{\"updatedTime\":1724665908000,\"kpiItemId\":\"45a34c79-6354-4909-9c49-24ad4b38a705\",\"formulaFieldValue\":0,\"companyFieldId\":\"4ca8ffeb-6239-43e7-b67f-0259ba826184\",\"taskUserId\":\"1280038\",\"updatedUser\":\"1302001\",\"version\":0,\"companyId\":{\"id\":\"5a031297-1b38-48ae-bc82-375849835203\"},\"isDeleted\":\"false\",\"formulaFieldName\":\"完成值\",\"createdTime\":1724665908000,\"id\":\"2034163\",\"taskId\":\"1635114\",\"createdUser\":\"1302001\"},{\"updatedTime\":1724665908000,\"kpiItemId\":\"45a34c79-6354-4909-9c49-24ad4b38a705\",\"formulaFieldValue\":0,\"companyFieldId\":\"1ea75c43-ff00-4ffa-be07-58f44f231a81\",\"taskUserId\":\"1280038\",\"updatedUser\":\"1302001\",\"version\":0,\"companyId\":{\"id\":\"5a031297-1b38-48ae-bc82-375849835203\"},\"isDeleted\":\"false\",\"formulaFieldName\":\"目标值\",\"createdTime\":1724665908000,\"id\":\"2034164\",\"taskId\":\"1635114\",\"createdUser\":\"1302001\"}],\"taskId\":\"1635114\"}";
        ResultInputEmpPackage resultInputEmpPackage = JSONUtil.toBean(packJson, ResultInputEmpPackage.class);
        EvalKpi newEvalKpi = JSONUtil.toBean(newEvalKpiJson, EvalKpi.class);
        evalKpi.compareInputEmp(resultInputEmpPackage, newEvalKpi);
        System.out.println(JSONUtil.toJsonStr(resultInputEmpPackage));
       Assert.assertTrue(resultInputEmpPackage.getCurInputEmpIds().size() == 0);
       Assert.assertTrue(resultInputEmpPackage.getOldInputEmpIds().size() == 1);
    }

    @Test
    public void showFinishBarName() {
        EvalKpi evalKpi = new EvalKpi();
        evalKpi.setShowFinishBar(null);
        evalKpi.showFinishBarName();
    }
}