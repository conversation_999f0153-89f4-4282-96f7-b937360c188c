package com.polaris.kpi.eval.domain.task.entity.calibrated;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.polaris.kpi.eval.domain.task.entity.admineval.EvalRuleOpLogMeta;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.grade.CheckRate;
import com.polaris.sdk.common.JsonFileTool;
import com.polaris.sdk.type.ListWrap;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


public class ResultRankInstanceTest {

    @Test
    @DisplayName("有异常提示")
    public void checkRateWithExcept() {
        List<LevelRate> targetLevelRates = JsonFileTool.toList("com.polaris.kpi.eval.domain.task.entity.grade/calibrated/TargetCheckRates.json", LevelRate.class);
        //System.out.println(JSONObject.toJSONString(targetLevelRates));
        ResultRankInstance resultRankInstance = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.entity.grade/calibrated/ResultRankInstance.json", ResultRankInstance.class);
        //System.out.println(JSONObject.toJSONString(resultRankInstance));
        List<CheckRate> checkRates1 = resultRankInstance.checkRateExcept(100, targetLevelRates);
        System.out.println("提示:" + JSONObject.toJSONString(checkRates1));
        for (CheckRate checkRate : checkRates1) {
            if (checkRate.getSortNo() == 1) {
                Assert.assertEquals(new BigDecimal("5"), checkRate.getExceptRange());
                Assert.assertEquals(new BigDecimal("5.00"), checkRate.getExceptCnt());
            }

            if (checkRate.getSortNo() == 2) {
                Assert.assertEquals(new BigDecimal(-10), checkRate.getExceptRange());
                Assert.assertEquals(new BigDecimal("-10.00"), checkRate.getExceptCnt());
            }
        }
    }

    @Test
    @DisplayName("有异常提示2")
    public void checkRateWithExcept2() {
        List<LevelRate> targetLevelRates = JsonFileTool.toList("com.polaris.kpi.eval.domain.task.entity.grade/calibrated/TargetCheckRates.json", LevelRate.class);
        System.out.println(JSONObject.toJSONString(targetLevelRates));
        ResultRankInstance resultRankInstance = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.entity.grade/calibrated/ResultRankInstance.json", ResultRankInstance.class);
        System.out.println(JSONObject.toJSONString(resultRankInstance));
        List<CheckRate> checkRates1 = resultRankInstance.checkRateExcept(100, targetLevelRates);
        for (CheckRate checkRate : checkRates1) {
            Assert.assertTrue(new BigDecimal(5).compareTo(checkRate.getExceptRange()) == 0);
        }
        System.out.println(JSONObject.toJSONString(checkRates1));
    }

    @Test
    public void compareDiff() {
        ResultRankInstance before = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.entity.grade/calibrated/before.json", ResultRankInstance.class);
        ResultRankInstance after = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.entity.grade/calibrated/after.json", ResultRankInstance.class);

        EvalRuleOpLogMeta x = after.compareDiff(before);
        System.out.println(JSONUtil.toJsonStr(x));


    }

    @Test
    public void createLevelByRank() {
        List<EvalUser> toRanks = JsonFileTool.toList("com/polaris/kpi/eval/domain/cycle/dmsvc/RankComputeOnTaskDmSvcTest/byTaskEvalUser.json", EvalUser.class);
        ResultRankInstance snap = JsonFileTool.toBean("com/polaris/kpi/eval/domain/cycle/dmsvc/RankComputeOnTaskDmSvcTest/snapRankOnAll.json", ResultRankInstance.class);
        Map<String, List<EvalUser>> groups = new ListWrap<>(toRanks).groupBy(u -> u.getTaskId()).getGroups();

        List<EvalUser> evalUsers = groups.get("t001");
        List<EvalUser> rs = snap.createLevelByRank(evalUsers.size(), evalUsers);
        Assert.assertTrue(evalUsers.stream().filter(evalUser -> evalUser.getStepId().equals("S")).count() == 1L);//6*10%
        Assert.assertTrue("应该有4个A等级", evalUsers.stream().filter(evalUser -> evalUser.getStepId().equals("A")).count() == 4L);//6*60%
        Assert.assertTrue(evalUsers.stream().filter(evalUser -> evalUser.getStepId().equals("D")).count() == 1L);//6*30%

        List<EvalUser> evalUsers2 = groups.get("t002");
        snap.createLevelByRank(evalUsers2.size(), evalUsers2);
        Assert.assertTrue(evalUsers2.stream().filter(evalUser -> evalUser.getStepId().equals("S")).count() == 1L);//8*10%
        Assert.assertTrue(evalUsers2.stream().filter(evalUser -> evalUser.getStepId().equals("A")).count() == 5L);//8*60%
        Assert.assertTrue(evalUsers2.stream().filter(evalUser -> evalUser.getStepId().equals("D")).count() == 2L);//8*30%
    }
}