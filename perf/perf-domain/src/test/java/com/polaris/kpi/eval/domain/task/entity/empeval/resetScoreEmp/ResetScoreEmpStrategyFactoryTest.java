package com.polaris.kpi.eval.domain.task.entity.empeval.resetScoreEmp;

import com.polaris.kpi.eval.domain.task.entity.empeval.score.ResetScoreEmpStrategy;
import com.polaris.kpi.eval.domain.task.entity.empeval.score.ResetScoreEmpStrategyFactory;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import org.junit.Test;

import java.util.List;

import static org.junit.Assert.*;

public class ResetScoreEmpStrategyFactoryTest {

    @Test
    public void listStrategy(){
        ResetScoreEmpStrategyFactory factory = new ResetScoreEmpStrategyFactory();
        List<ResetScoreEmpStrategy> strategieBeans = factory.listStrategy(TalentStatus.FINISHED.getStatus(), null, null, null);
        assertEquals(5, strategieBeans.size());
    }
}
