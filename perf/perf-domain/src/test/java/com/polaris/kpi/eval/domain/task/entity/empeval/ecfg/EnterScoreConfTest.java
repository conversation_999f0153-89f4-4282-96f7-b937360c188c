package com.polaris.kpi.eval.domain.task.entity.empeval.ecfg;

import com.alibaba.fastjson.JSON;
import org.joda.time.DateTime;
import org.junit.Assert;
import org.junit.Test;

public class EnterScoreConfTest {


    @Test
    public void notAuto() {
        String conJson = "{\"autoEnter\":true,\"enterScoreEmpType\":1,\"enterScoreMethod\":\"manual\",\"scoreStartRuleDay\":1,\"scoreStartRuleType\":\"after_cycle_start\"}";
        EnterScoreConf enterScoreConf = JSON.parseObject(conJson, EnterScoreConf.class);
        Assert.assertFalse(enterScoreConf.needAutoEnterScore("2023-06-01", "2023-03-31"));
        System.out.println(enterScoreConf.needAutoEnterScore("2023-06-01", "2023-03-31"));
    }

    @Test
    public void isAutoEnterAfterCycleEnd() {
        String conJson = "{\"autoEnter\":true,\"enterScoreEmpType\":1,\"enterScoreMethod\":\"auto\",\"scoreStartRuleDay\":1,\"scoreStartRuleType\":\"after\"}";
        EnterScoreConf enterScoreConf = JSON.parseObject(conJson, EnterScoreConf.class);
        Assert.assertTrue(enterScoreConf.needAutoEnterScore("2023-03-01", "2023-03-31"));
        System.out.println(enterScoreConf.needAutoEnterScore("2023-03-01", "2023-03-31"));
    }
    @Test
    public void isAutoEnterBeforeCycleEnd() {
        String conJson = "{\"autoEnter\":true,\"enterScoreEmpType\":1,\"enterScoreMethod\":\"auto\",\"scoreStartRuleDay\":1,\"scoreStartRuleType\":\"before\"}";
        EnterScoreConf enterScoreConf = JSON.parseObject(conJson, EnterScoreConf.class);
        Assert.assertTrue(enterScoreConf.needAutoEnterScore("2024-10-01", "2024-10-31"));
        System.out.println(enterScoreConf.needAutoEnterScore("2024-10-01", "2024-10-31"));
    }

    @Test
    public void isAutoEnterAfterCycleStart() {
        String conJson = "{\"autoEnter\":true,\"enterScoreEmpType\":1,\"enterScoreMethod\":\"auto\",\"scoreStartRuleDay\":1,\"scoreStartRuleType\":\"after_cycle_start\"}";
        EnterScoreConf enterScoreConf = JSON.parseObject(conJson, EnterScoreConf.class);
        Assert.assertTrue(enterScoreConf.needAutoEnterScore("2023-06-01", "2023-03-31"));
        System.out.println(enterScoreConf.needAutoEnterScore("2023-06-01", "2023-03-31"));

        DateTime now = new DateTime();
        String cycleStart = now.toString("yyyy-MM-dd");
        String cycleEnd = now.plusYears(1).toString("yyyy-MM-dd");
        boolean need = enterScoreConf.needAutoEnterScore(cycleStart, cycleEnd);
        Assert.assertFalse(need);
        System.out.println(need);
    }
}