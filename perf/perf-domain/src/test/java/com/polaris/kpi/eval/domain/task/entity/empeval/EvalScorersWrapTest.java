package com.polaris.kpi.eval.domain.task.entity.empeval;

import com.polaris.kpi.eval.domain.task.entity.EmpEvalScorer;
import com.polaris.kpi.eval.domain.task.entity.EmpEvalScorerNode;
import com.polaris.kpi.eval.domain.task.entity.EvalScorerNodeKpiItem;
import com.polaris.kpi.eval.domain.task.entity.EvalScorerNodeKpiType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;

public class EvalScorersWrapTest {

    private EvalScorersWrap evalScorersWrap;

    @BeforeEach
    public void setUp() {
        evalScorersWrap = new EvalScorersWrap();
    }

    @Test
    public void testAddEvalScorer_EmptyList() {
        // 准备数据
        EmpEvalScorerNode scorerNode = createScorerNode("scorer1", "scoreType1", "kpiItemId1");

        // 执行方法
        evalScorersWrap.addEvalScorer(scorerNode);

        // 验证结果
        assertEquals(1, evalScorersWrap.getDatas().size());
        EmpEvalScorer scorer = evalScorersWrap.getDatas().get(0);
        assertEquals("scorer1", scorer.getScorerId());
        assertEquals(1, scorer.getScorerNodes().size());
        assertEquals("scoreType1", scorer.getScorerNodes().get(0).getScorerType());
        assertEquals(1, scorer.getScorerNodes().get(0).getScorerNodeKpiTypes().size());
        assertEquals("kpiItemId1", scorer.getScorerNodes().get(0).getScorerNodeKpiTypes().get(0).getKpiTypeId());
    }
    @Test
    public void testAddEvalScorer_ScorerExists() {
        // 准备数据
        EmpEvalScorerNode scorerNode1 = createScorerNode("scorer1", "scoreType1", "kpiItemId1");
        EmpEvalScorerNode scorerNode2 = createScorerNode("scorer1", "scoreType2", "kpiItemId2");

        // 添加第一个评分节点
        evalScorersWrap.addEvalScorer(scorerNode1);

        // 执行方法
        evalScorersWrap.addEvalScorer(scorerNode2);

        // 验证结果
        assertEquals(1, evalScorersWrap.getDatas().size());
        EmpEvalScorer scorer = evalScorersWrap.getDatas().get(0);
        assertEquals("scorer1", scorer.getScorerId());
        assertEquals(2, scorer.getScorerNodes().size());

        // 验证第一个评分节点
        assertEquals("scoreType1", scorer.getScorerNodes().get(0).getScorerType());
        assertEquals(1, scorer.getScorerNodes().get(0).getScorerNodeKpiTypes().size());
        assertEquals("kpiItemId1", scorer.getScorerNodes().get(0).getScorerNodeKpiTypes().get(0).getKpiTypeId());

        // 验证第二个评分节点
        assertEquals("scoreType2", scorer.getScorerNodes().get(1).getScorerType());
        assertEquals(1, scorer.getScorerNodes().get(1).getScorerNodeKpiTypes().size());
        assertEquals("kpiItemId2", scorer.getScorerNodes().get(1).getScorerNodeKpiTypes().get(0).getKpiTypeId());
    }

    @Test
    public void testAddEvalScorer_NodeExists() {
        // 准备数据
        EmpEvalScorerNode scorerNode1 = createScorerNode("scorer1", "scoreType1", "kpiItemId1");
        EmpEvalScorerNode scorerNode2 = createScorerNode("scorer1", "scoreType1", "kpiItemId2");

        // 添加第一个评分节点
        evalScorersWrap.addEvalScorer(scorerNode1);

        // 执行方法
        evalScorersWrap.addEvalScorer(scorerNode2);

        // 验证结果
        assertEquals(1, evalScorersWrap.getDatas().size());
        EmpEvalScorer scorer = evalScorersWrap.getDatas().get(0);
        assertEquals("scorer1", scorer.getScorerId());
        assertEquals(1, scorer.getScorerNodes().size());

        // 验证评分节点
        assertEquals("scoreType1", scorer.getScorerNodes().get(0).getScorerType());
        assertEquals(2, scorer.getScorerNodes().get(0).getScorerNodeKpiTypes().size());
        assertEquals("kpiItemId1", scorer.getScorerNodes().get(0).getScorerNodeKpiTypes().get(0).getKpiTypeId());
        assertEquals("kpiItemId2", scorer.getScorerNodes().get(0).getScorerNodeKpiTypes().get(1).getKpiTypeId());
    }

    @Test
    public void testAddEvalScorer_NodeExistsWithDuplicateKpiItem() {
        // 准备数据
        EmpEvalScorerNode scorerNode1 = createScorerNode("scorer1", "scoreType1", "kpiItemId1");
        EmpEvalScorerNode scorerNode2 = createScorerNode("scorer1", "scoreType1", "kpiItemId1");

        // 添加第一个评分节点
        evalScorersWrap.addEvalScorer(scorerNode1);

        // 执行方法
        evalScorersWrap.addEvalScorer(scorerNode2);

        // 验证结果
        assertEquals(1, evalScorersWrap.getDatas().size());
        EmpEvalScorer scorer = evalScorersWrap.getDatas().get(0);
        assertEquals("scorer1", scorer.getScorerId());
        assertEquals(1, scorer.getScorerNodes().size());

        // 验证评分节点
        assertEquals("scoreType1", scorer.getScorerNodes().get(0).getScorerType());
        assertEquals(1, scorer.getScorerNodes().get(0).getScorerNodeKpiTypes().size());
        assertEquals("kpiItemId1", scorer.getScorerNodes().get(0).getScorerNodeKpiTypes().get(0).getKpiTypeId());
    }
    @Test
    public void testAddEvalScorer_NullScorerNode() {
        // 执行方法
        evalScorersWrap.addEvalScorer(null);

        // 验证结果
        assertEquals(0, evalScorersWrap.getDatas().size());
    }
    @Test
    public void testAddEvalScorer_NullKpiItems() {
        // 准备数据
        EmpEvalScorerNode scorerNode = createScorerNode("scorer1", "scoreType1", null);

        // 执行方法
        evalScorersWrap.addEvalScorer(scorerNode);

        // 验证结果
        assertEquals(1, evalScorersWrap.getDatas().size());
        EmpEvalScorer scorer = evalScorersWrap.getDatas().get(0);
        assertEquals("scorer1", scorer.getScorerId());
        assertEquals(1, scorer.getScorerNodes().size());
        assertEquals("scoreType1", scorer.getScorerNodes().get(0).getScorerType());
        assertEquals(0, scorer.getScorerNodes().get(0).getScorerNodeKpiTypes().size());
    }

    private EmpEvalScorerNode createScorerNode(String scorerId, String scoreType, String kpiItemId) {
        EmpEvalScorerNode scorerNode = new EmpEvalScorerNode();
        scorerNode.setScorerId(scorerId);
        scorerNode.setScorerType(scoreType);
        if (kpiItemId != null) {
            List<EvalScorerNodeKpiType> kpitypes = new ArrayList<>();
            EvalScorerNodeKpiType type = new EvalScorerNodeKpiType();

            List<EvalScorerNodeKpiItem> kpiItems = new ArrayList<>();
            EvalScorerNodeKpiItem kpiItem = new EvalScorerNodeKpiItem();
            kpiItem.setKpiItemId(kpiItemId);
            kpiItems.add(kpiItem);
            type.setScorerNodeKpiItems(kpiItems);
            scorerNode.setScorerNodeKpiTypes(kpitypes);
        }
        return scorerNode;
    }
}
