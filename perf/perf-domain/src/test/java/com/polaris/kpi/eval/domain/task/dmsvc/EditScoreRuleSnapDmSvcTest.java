package com.polaris.kpi.eval.domain.task.dmsvc;

import cn.hutool.crypto.digest.MD5;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.cycle.dmsvc.RankComputeOnEmpDmSvc;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminCycleOperation;
import com.polaris.kpi.eval.domain.task.entity.calibrated.ResultRankInstance;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalRule;
import com.polaris.kpi.eval.domain.task.entity.grade.ScoreRule;
import com.polaris.kpi.eval.domain.task.repo.EmpEvalRuleRepo;
import com.polaris.kpi.eval.domain.task.repo.ScoreRuleSnapRepo;
import com.polaris.sdk.common.JsonFileTool;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import java.util.List;

public class EditScoreRuleSnapDmSvcTest {
    private EditScoreRuleSnapDmSvc snapDmSvc = new EditScoreRuleSnapDmSvc();
    private ScoreRuleSnapRepo ruleSpapRepo = Mockito.mock(ScoreRuleSnapRepo.class);
    private EmpEvalRuleRepo empRuleRepo = Mockito.mock(EmpEvalRuleRepo.class);

    private void mockRuleRepo() {
        empRuleRepo = Mockito.mock(EmpEvalRuleRepo.class);
        EmpEvalRule t = new EmpEvalRule();
        t.setCreateTotalLevelType(1);
        Mockito.when(empRuleRepo.getBaseEmpEvalRule(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(t);
    }
    @Before
    public void setUp() throws Exception {
        List<EvalUser> list = JsonFileTool.toList("com.polaris.kpi.eval.domain.task.dmsvc/EditScoreRuleSnapDmSvcTest/listSnapMember.json", EvalUser.class);
        Mockito.when(ruleSpapRepo.listSnapMember(ArgumentMatchers.any(), ArgumentMatchers.anyString())).thenReturn(list);
        mockRuleRepo();
    }

    @Test
    public void appendOpEmp() {
        List<EvalUser> list = ruleSpapRepo.listSnapMember(new TenantId("com"), "snapId");
        System.out.println(JSONUtil.toJsonStr(list));
    }

    @Test
    public void changeScoreRuleSnap() {

        String path = "com.polaris.kpi.eval.domain.task.dmsvc";
        ResultRankInstance oldSnap = JsonFileTool.toBean(path + "/beforeSnap.json", ResultRankInstance.class);
        snapDmSvc.setOpType(1);
        snapDmSvc.setBeforeSnap(oldSnap);
        ResultRankInstance newSnap = JsonFileTool.toBean(path + "/newSnap.json", ResultRankInstance.class);
        snapDmSvc.setNewSnap(newSnap);

        TenantId tenantId = new TenantId("5a031297-1b38-48ae-bc82-375849835203");
        snapDmSvc.accOp(tenantId, new EmpId("10001"), "main");
        snapDmSvc.changeScoreRuleSnap();

        RankComputeOnEmpDmSvc computeOnTaskDmSvc = new RankComputeOnEmpDmSvc(tenantId, snapDmSvc.getNewSnap());
        boolean recomputeResult = computeOnTaskDmSvc.forEachRecomputeResult(ruleSpapRepo,empRuleRepo);
        Assert.assertTrue(recomputeResult);

        List<EvalUser> members = computeOnTaskDmSvc.getMembers();
        System.out.println(members.get(0).getEvaluationLevel());

        AdminCycleOperation opLog = snapDmSvc.getOpLog();
        String jsonStr = JSONUtil.toJsonStr(opLog);
        JSONObject bean = JSONUtil.toBean(jsonStr, JSONObject.class);
        bean.remove("operationTime");
        System.out.println("reaLog" + bean.toString());
        System.out.println("reaLog" + MD5.create().digestHex(bean.toString()));

        JSONObject expLog = JsonFileTool.toBean(path + "/log.json", JSONObject.class);
        expLog.remove("operationTime");
        System.out.println("expLog" + expLog.toString());
        System.out.println("expLog" + MD5.create().digestHex(expLog.toString()));
        Assert.assertEquals(MD5.create().digestHex(bean.toString()), MD5.create().digestHex(expLog.toString()));
    }

    @Test
    public void changeScoreRuleSnap2() {
//        EditScoreRuleSnapDmSvc snapDmSvc = new EditScoreRuleSnapDmSvc();
//        ResultRankInstance newSnap = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.entity.empeval/newSnap.json", ResultRankInstance.class);
//        snapDmSvc.setNewSnap(newSnap);
//        newSnap.setScoreRanges(new ArrayList<>());
//        ResultRankInstance resultRankInstance = snapDmSvc.changeScoreRuleSnap();
//
//        String expt = JsonFileTool.readString("com.polaris.kpi.eval.domain.task.entity.empeval/newSnapExet.json");
//
//        Assert.assertEquals(JSONUtil.toJsonStr(resultRankInstance), expt);
//
//
//        AdminCycleOperation opLog = snapDmSvc.getOpLog();
//        String expLog = JsonFileTool.readString("com.polaris.kpi.eval.domain.task.entity.empeval/log.json");
//        Assert.assertEquals(JSONUtil.toJsonStr(opLog), expLog);

    }

    @Test
    public void saveScoreRuleSnap() {
        snapDmSvc.saveScoreRuleSnap(ruleSpapRepo);
    }

    @Test
    public void needRecomute() {
        Assert.assertTrue(!snapDmSvc.needRecomute());
        snapDmSvc.setOpType(2);
        Assert.assertTrue(snapDmSvc.needRecomute());
    }


    @Test
    public void batchUpdateResult() {
    }

    @Test
    public void synToSystem() {
        String path = "com.polaris.kpi.eval.domain.task.dmsvc";
        EditScoreRuleSnapDmSvc snapDmSvc = JsonFileTool.toBean(path + "/synToSystem.json", EditScoreRuleSnapDmSvc.class);
        ResultRankInstance oldSnap = JsonFileTool.toBean(path + "/beforeSnap.json", ResultRankInstance.class);
        snapDmSvc.setOpType(1);
        snapDmSvc.setBeforeSnap(oldSnap);
        Assert.assertTrue(snapDmSvc.needSynToSystem());
        System.out.println(snapDmSvc.needSynToSystem());

        System.out.println(JSONUtil.toJsonStr(snapDmSvc.getNewSnap()));
        ScoreRule obj = snapDmSvc.synToSystem();
        String exp = JsonFileTool.readString(path + "/synToSysemRs.json");
        Assert.assertTrue(JSONUtil.toJsonStr(obj).equals(exp));
    }
}