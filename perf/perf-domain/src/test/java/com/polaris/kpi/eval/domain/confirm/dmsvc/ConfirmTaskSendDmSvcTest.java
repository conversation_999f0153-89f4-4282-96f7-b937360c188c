//package com.polaris.kpi.eval.domain.confirm.dmsvc;
//
//import cn.com.polaris.kpi.company.MsgSceneEnum;
//import cn.com.polaris.kpi.eval.Name;
//import com.polaris.kpi.eval.domain.task.entity.EvalUser;
//import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
//import com.polaris.kpi.eval.domain.task.entity.msg.MsgTodoAggregate;
//import com.polaris.kpi.org.domain.dept.repo.MsgCenterRepo;
//import com.polaris.sdk.type.TenantId;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockitoAnnotations;
//
//import java.util.Set;
//
//import static org.mockito.Mockito.*;
//
//class ConfirmTaskSendDmSvcTest {
//
//    @Mock
//    private MsgCenterRepo centerRepo;
//
//    @InjectMocks
//    private ConfirmTaskSendDmSvc confirmTaskSendDmSvc;
//
//    @BeforeEach
//    void setUp() {
//        MockitoAnnotations.openMocks(this);
//    }
//
//    @Test
//    void sendDispatched_ShouldPublishMessageWithValidParams() {
//        // Arrange
//        TenantId tenantId = new TenantId("testTenant");
//        AdminTask task = new AdminTask("task1", "Test Task");
//        EvalUser empEval = new EvalUser("emp1", "Emp Name", "org1");
//        ConfirmDispatchNodeRs dispatchNodeRs = new ConfirmDispatchNodeRs(Set.of("rater1", "rater2"));
//
//        // Act
//        confirmTaskSendDmSvc.sendDispatched(tenantId, task, empEval, dispatchNodeRs);
//
//        // Assert
//        verify(centerRepo, times(1)).publish(any(MsgTodoAggregate.class));
//    }
//
//    @Test
//    void batchSendAffirmTask_ShouldSkipIfRecEmpIdsIsEmpty() {
//        // Arrange
//        AdminTask adminTask = new AdminTask("task1", "Test Task");
//        EvalUser taskUser = new EvalUser("emp1", "Emp Name", "org1");
//
//        // Act
//        confirmTaskSendDmSvc.batchSendAffirmTask(adminTask, taskUser, Set.of());
//
//        // Assert
//        verify(centerRepo, never()).publish(any(MsgTodoAggregate.class));
//    }
//}
//        AdminTask adminTask = new AdminTask("task1", "Test Task");
//        EvalUser taskUser = new EvalUser("emp1", "Emp Name", "org1");
//
//        // Act
//        confirmTaskSendDmSvc.batchSendAffirmTask(adminTask, taskUser, Set.of());
//
//        // Assert
//        verify(centerRepo, never()).publish(any(MsgTodoAggregate.class));
//    }
//}