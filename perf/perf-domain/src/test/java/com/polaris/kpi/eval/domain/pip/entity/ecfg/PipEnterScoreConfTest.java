package com.polaris.kpi.eval.domain.pip.entity.ecfg;

import com.alibaba.fastjson.JSON;
import com.polaris.kpi.eval.domain.pip.plan.entity.PipScoreConf;
import org.joda.time.DateTime;
import org.junit.Assert;
import org.junit.Test;

public class PipEnterScoreConfTest {


    @Test
    public void notAuto() {
        String conJson = "{\"enterScoreMethod\":\"manual\",\"scoreStartRuleDay\":1,\"autoEnterScoreCycleEnd\":\"before\"}";
        PipScoreConf enterScoreConf = JSON.parseObject(conJson, PipScoreConf.class);
        Assert.assertFalse(enterScoreConf.needAutoEnterScore("2024-08-20"));
        System.out.println(enterScoreConf.needAutoEnterScore("2023-08-20"));
    }

    @Test
    public void isAutoEnterAfterCycleEnd() {
        String conJson = "{\"enterScoreMethod\":\"auto\",\"scoreStartRuleDay\":1,\"autoEnterScoreCycleEnd\":\"after\"}";
        PipScoreConf enterScoreConf = JSON.parseObject(conJson, PipScoreConf.class);
        Assert.assertTrue(enterScoreConf.needAutoEnterScore("2024-08-19"));
        System.out.println(enterScoreConf.needAutoEnterScore("2024-08-19"));
    }

    @Test
    public void isAutoEnterBeforeCycleStart() {
        String conJson = "{\"enterScoreMethod\":\"auto\",\"scoreStartRuleDay\":1,\"autoEnterScoreCycleEnd\":\"before\"}";
        PipScoreConf enterScoreConf = JSON.parseObject(conJson, PipScoreConf.class);
        Assert.assertTrue(enterScoreConf.needAutoEnterScore("2024-08-18"));
        System.out.println(enterScoreConf.needAutoEnterScore("2024-08-18"));

        DateTime now = new DateTime();
        String cycleEnd = now.plusYears(1).toString("yyyy-MM-dd");
        boolean need = enterScoreConf.needAutoEnterScore(cycleEnd);
        Assert.assertFalse(need);
        System.out.println(need);
    }
}