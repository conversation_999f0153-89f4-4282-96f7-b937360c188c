//package com.polaris.kpi.eval.domain.confirm.dmsvc;
//
//import cn.com.polaris.kpi.company.MsgSceneEnum;
//import com.polaris.kpi.eval.domain.confirm.entity.AcceptSumbitRs;
//import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
//import com.polaris.kpi.org.domain.dept.repo.MsgCenterRepo;
//import com.polaris.sdk.type.TenantId;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockitoAnnotations;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Set;
//import java.util.stream.Collectors;
//
//import static org.mockito.Mockito.*;
//
//class ConfirmTaskClearDmSvcTest {
//
//    @Mock
//    private MsgCenterRepo centerRepo;
//
//    @InjectMocks
//    private ConfirmTaskClearDmSvc confirmTaskClearDmSvc;
//
//    @BeforeEach
//    void setUp() {
//        MockitoAnnotations.openMocks(this);
//    }
//
//    @Test
//    void clearPassed_ShouldCallSuperClearWithValidParams() {
//        // Arrange
//        TenantId tenantId = new TenantId("testTenant");
//        String linkId = "testLinkId";
//        AcceptSumbitRs acceptPass = new AcceptSumbitRs();
//        List<EvalScoreResult> passed = new ArrayList<>();
//        passed.add(new EvalScoreResult("scorer1", 90));
//        passed.add(new EvalScoreResult("scorer2", 85));
//        acceptPass.setPassed(passed);
//
//        // Act
//        confirmTaskClearDmSvc.clearPassed(tenantId, linkId, acceptPass);
//
//        // Assert
//        Set<String> expectedRaterIds = passed.stream().map(EvalScoreResult::getScorerId).collect(Collectors.toSet());
//        verify(centerRepo).clear(tenantId, linkId, MsgSceneEnum.confirmScene, new ArrayList<>(expectedRaterIds));
//    }
//}
//    }
//
//    @Test
//    void clearPassed_ShouldHandleEmptyPassedList() {
//        // Arrange
//        TenantId tenantId = new TenantId("testTenant");
//        String linkId = "testLinkId";
//        AcceptSumbitRs acceptPass = new AcceptSumbitRs();
//        acceptPass.setPassed(new ArrayList<>());
//
//        // Act
//        confirmTaskClearDmSvc.clearPassed(tenantId, linkId, acceptPass);
//
//        // Assert
//        verify(centerRepo).clear(tenantId, linkId, MsgSceneEnum.confirmScene, new ArrayList<>());
//    }
//}