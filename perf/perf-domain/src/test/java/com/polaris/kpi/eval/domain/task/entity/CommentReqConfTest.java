package com.polaris.kpi.eval.domain.task.entity;

import com.alibaba.fastjson.JSON;
import com.polaris.kpi.eval.domain.task.entity.admineval.CommentReqConf;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: xuxw
 * @Date: 2025/01/14 19:10
 * @Description:
 */
public class CommentReqConfTest {

    @Test
    public void compare(){
        String before = "{\n" +
                "    \"commentNodeSet\": [\n" +
                "        {\n" +
                "            \"index\": \"2\",\n" +
                "            \"scoreNodes\": [\n" +
                "                  \"superior_score\"\n" +
                "              ],\n" +
                "            \"commonItem\": {\n" +
                "                \"rules\": [\n" +
                "                    {\n" +
                "                        \"op\": \">=\",\n" +
                "                        \"value\": 90\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"op\": \"<=\",\n" +
                "                        \"value\": 60\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"reqLevel\": 4\n" +
                "            },\n" +
                "            \n" +
                "            \"plusOrSubItem\": {\n" +
                "                \"reqLevel\": 2\n" +
                "            },\n" +
                "            \"scoreSummary\": {\n" +
                "                \"rules\": [\n" +
                "                    {\n" +
                "                        \"dimension\": 1,\n" +
                "                        \"op\": \"<=\",\n" +
                "                        \"value\": 60\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"reqLevel\": 4\n" +
                "            }\n" +
                "        }\n" +
                "    ]\n" +
                "}";

        String after ="{\n" +
                "    \"commentNodeSet\": [\n" +
                "        {\n" +
                "            \"index\": \"2\",\n" +
                "            \"scoreNodes\": [\n" +
                "                  \"superior_score\",\n" +
                "                  \"peer_score\"\n" +
                "              ],\n" +
                "            \"commonItem\": {\n" +
                "                \"rules\": [\n" +
                "                    {\n" +
                "                        \"op\": \">=\",\n" +
                "                        \"value\": 80\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"op\": \"<=\",\n" +
                "                        \"value\": 60\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"reqLevel\": 4\n" +
                "            },\n" +
                "            \n" +
                "            \"plusOrSubItem\": {\n" +
                "                \"reqLevel\": 3\n" +
                "            },\n" +
                "            \"scoreSummary\": {\n" +
                "                \"rules\": [\n" +
                "                    {\n" +
                "                        \"dimension\": 1,\n" +
                "                        \"op\": \"<=\",\n" +
                "                        \"value\": 60\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"reqLevel\": 4\n" +
                "            }\n" +
                "        }\n" +
                "    ]\n" +
                "}";

        CommentReqConf beforeConf = JSON.parseObject(before, CommentReqConf.class);
        CommentReqConf afterConf = JSON.parseObject(after, CommentReqConf.class);
        beforeConf.compare(afterConf);
    }
}
