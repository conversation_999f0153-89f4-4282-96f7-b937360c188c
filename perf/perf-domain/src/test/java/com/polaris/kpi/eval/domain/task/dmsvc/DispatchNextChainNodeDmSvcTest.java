package com.polaris.kpi.eval.domain.task.dmsvc;

import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.empeval.ChainDispatchRs;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.chain.ChainNode;
import com.polaris.sdk.common.JsonFileTool;
import com.polaris.sdk.type.TenantId;
import org.junit.Test;


public class DispatchNextChainNodeDmSvcTest {
    public static TenantId companyId = new TenantId("5a031297-1b38-48ae-bc82-375849835203");
    public static String taskUserId = "1280601";


    @Test
    public void handleNextNodeDispatchRs() {
        ChainNode chainNode = new ChainNode(SubScoreNodeEnum.SUPERIOR_SCORE,1,0);
        //{"scoreNodes":[{"node":"SUPERIOR_SCORE","order":1}],"type":0}  chainNode
        EmpEvalMerge eval = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.dmsvc/DispatchNextChainNodeDmSvcTest/evalMerge.json", EmpEvalMerge.class);
        EvalUser user = new EvalUser();
        //EmpEvalMerge eval, EvalUser taskUser, ChainNode next, String tenantId
        ChainDispatchRs rs = eval.dispatchChainNodeV3(chainNode, user.isOpenAvgWeightCompute());//只分发未分发的,已分发的不再发
        System.out.println(JSONUtil.toJsonStr(rs));
    }
}
