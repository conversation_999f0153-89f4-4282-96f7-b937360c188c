package com.polaris.kpi.eval.domain.cycle.dmsvc;

import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.calibrated.ResultRankInstance;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;

public class RankGroupTest {

    @Test
    public void conditionIsOk() {
        ArrayList<EvalUser> evalUsers = new ArrayList<>();
        evalUsers.add(createUser(TalentStatus.CONFIRMING));
        evalUsers.add(createUser(TalentStatus.CONFIRMED));
        evalUsers.add(createUser(TalentStatus.SCORING));
        evalUsers.add(createUser(TalentStatus.ON_LEVEL));

        RankGroup rankGroup = new RankGroup(new ResultRankInstance(), "any", evalUsers);
        Assert.assertFalse(rankGroup.conditionIsOk());
        System.out.println(rankGroup.conditionIsOk());
    }

    @Test
    public void conditionIsOk2() {
        ArrayList<EvalUser> evalUsers = new ArrayList<>();
        evalUsers.add(createUser(TalentStatus.SCORING));
        evalUsers.add(createUser(TalentStatus.SCORING));
        evalUsers.add(createUser(TalentStatus.SCORING));
        evalUsers.add(createUser(TalentStatus.ON_LEVEL));
        evalUsers.add(createUser(TalentStatus.RESULTS_AUDITING));
        evalUsers.add(createUser(TalentStatus.RESULTS_INTERVIEW));

        RankGroup rankGroup = new RankGroup(new ResultRankInstance(), "any", evalUsers);
        Assert.assertFalse(rankGroup.conditionIsOk());
        System.out.println(rankGroup.conditionIsOk());
    }

    @Test
    public void conditionIsOk3() {
        ArrayList<EvalUser> evalUsers = new ArrayList<>();
        evalUsers.add(createUser(TalentStatus.ON_LEVEL));
        evalUsers.add(createUser(TalentStatus.ON_LEVEL));
        evalUsers.add(createUser(TalentStatus.ON_LEVEL));
        evalUsers.add(createUser(TalentStatus.RESULTS_AUDITING));
        evalUsers.add(createUser(TalentStatus.RESULTS_INTERVIEW));
        RankGroup rankGroup = new RankGroup(new ResultRankInstance(), "any", evalUsers);
        Assert.assertTrue(rankGroup.conditionIsOk());
        System.out.println(rankGroup.conditionIsOk());
    }


    @NotNull
    private static EvalUser createUser(TalentStatus status) {
        EvalUser evalUser = new EvalUser();
        evalUser.setTaskStatus(status.getStatus());
        evalUser.setSubStatus(status.getSubStatus());
        return evalUser;
    }
}