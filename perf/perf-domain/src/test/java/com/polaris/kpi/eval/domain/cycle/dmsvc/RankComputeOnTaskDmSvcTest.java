package com.polaris.kpi.eval.domain.cycle.dmsvc;

import com.polaris.kpi.TestFileTool;
import com.polaris.kpi.eval.domain.cycle.type.RefRuleSnapOnTask;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.calibrated.ResultRankInstance;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalRule;
import com.polaris.kpi.eval.domain.task.repo.EmpEvalRuleRepo;
import com.polaris.kpi.eval.domain.task.repo.ScoreRuleSnapRepo;
import com.polaris.sdk.common.JsonFileTool;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class RankComputeOnTaskDmSvcTest {
    private String path = "com.polaris.kpi.eval.domain.cycle.dmsvc";
    private String companyId = "10001";
    private TenantId companyId1 = new TenantId(companyId);
    private EmpEvalRuleRepo empRuleRepo;

    @Before
    public void setUp() throws Exception {
        mockRuleRepo();
    }

    @Test
    @DisplayName("按分数生成等级")
    public void recomputeResultByScore() {
        String snapId = "1000001";
        List<EvalUser> scores = JsonFileTool.toList("com/polaris/kpi/eval/domain/cycle/dmsvc/RankComputeOnTaskDmSvcTest/recomputeResultByScore/byScoreUsers.json", EvalUser.class);
        ScoreRuleSnapRepo snapRepo = Mockito.mock(ScoreRuleSnapRepo.class);
        Mockito.when(snapRepo.listSnapMember(companyId1, snapId)).thenReturn(scores);

        ResultRankInstance snap = TestFileTool.toBean(this.getClass(), "RankComputeOnTaskDmSvcTest/recomputeResultByScore/byScoreSnap.json", ResultRankInstance.class);
        Mockito.when(snapRepo.getResultRankInstance(companyId1, snapId)).thenReturn(snap);


        List<RefRuleSnapOnTask> onTasks = new ArrayList<>();

        onTasks.add(new RefRuleSnapOnTask("t001", snapId));
//        ResultRankInstance snap = JsonFileTool.toBean(path + "/snap.json", ResultRankInstance.class);
        RankComputeOnTaskDmSvc compute = new RankComputeOnTaskDmSvc(companyId1, onTasks);
        compute.forEachRecomputeResult(snapRepo, empRuleRepo);
        List<EvalUser> members = compute.getMembers();
        EvalUser evalUser1 = members.stream().filter(evalUser -> evalUser.getId().equals("100001")).findFirst().get();
        Assert.assertTrue(evalUser1.getStepId().equals("2004001"));
        Assert.assertTrue(evalUser1.getPerfCoefficient().equals("1"));

        EvalUser evalUser2 = members.stream().filter(evalUser -> evalUser.getId().equals("100002")).findFirst().get();
        Assert.assertTrue(evalUser2.getStepId().equals("1000005"));
        Assert.assertTrue(evalUser2.getPerfCoefficient().equals("0.8"));

        EvalUser evalUser3 = members.stream().filter(evalUser -> evalUser.getId().equals("100003")).findFirst().get();
        Assert.assertTrue(evalUser3.getStepId().equals("1000006"));
        Assert.assertTrue(evalUser3.getPerfCoefficient().equals("0.6"));
        compute.batchUpdateResult(snapRepo, companyId1);
    }

    @Test
    @DisplayName("任务模式-按排名生成等级-任务全员")
    public void testRecomputeResultByRank() {
        List<EvalUser> toRanks = JsonFileTool.toList("com/polaris/kpi/eval/domain/cycle/dmsvc/RankComputeOnTaskDmSvcTest/byTaskEvalUser.json", EvalUser.class);
        List<RefRuleSnapOnTask> onTasks = new ArrayList<>();
        String instanceId = "1000002";
        onTasks.add(new RefRuleSnapOnTask("t001", instanceId));
        onTasks.add(new RefRuleSnapOnTask("t002", instanceId));
        ScoreRuleSnapRepo snapRepo = Mockito.mock(ScoreRuleSnapRepo.class);


        Mockito.when(snapRepo.listNeedRankTaskUser(ArgumentMatchers.any(), ArgumentMatchers.nullable(String.class))).thenReturn(toRanks);
        //指定4级部门进行排名
        ResultRankInstance snap = JsonFileTool.toBean("com/polaris/kpi/eval/domain/cycle/dmsvc/RankComputeOnTaskDmSvcTest/snapRankOnAll.json", ResultRankInstance.class);
        Mockito.when(snapRepo.getResultRankInstance(companyId1, instanceId)).thenReturn(snap);

        RankComputeOnTaskDmSvc compute = new RankComputeOnTaskDmSvc(companyId1, onTasks);
        compute.forEachRecomputeResult(snapRepo, empRuleRepo);
        compute.batchUpdateResult(snapRepo, companyId1);

        List<EvalUser> members = compute.getMembers();
        System.out.println(members.size());
        Map<String, List<EvalUser>> groups = new ListWrap<>(members).groupBy(u -> u.getTaskId()).getGroups();
        {
            List<EvalUser> evalUsers = groups.get("t001");
            Assert.assertTrue(evalUsers.stream().filter(evalUser -> evalUser.getStepId().equals("S")).count() == 1L);//6*10%
            Assert.assertTrue("应该有4个A等级", evalUsers.stream().filter(evalUser -> evalUser.getStepId().equals("A")).count() == 4L);//6*60%
            Assert.assertTrue(evalUsers.stream().filter(evalUser -> evalUser.getStepId().equals("D")).count() == 1L);//6*30%
        }

        {
            List<EvalUser> evalUsers = groups.get("t002");
            Assert.assertTrue(evalUsers.stream().filter(evalUser -> evalUser.getStepId().equals("S")).count() == 1L);//8*10%
            Assert.assertTrue(evalUsers.stream().filter(evalUser -> evalUser.getStepId().equals("A")).count() == 5L);//8*60%
            Assert.assertTrue(evalUsers.stream().filter(evalUser -> evalUser.getStepId().equals("D")).count() == 2L);//8*30%
        }

    }

    @Test
    @DisplayName("任务模式-按排名生成等级-所属部门")
    public void snapWithByOrgId() {
        String spapId = "1000003";
        String taskId = "t002";
        //准备测试类及数据
        List<RefRuleSnapOnTask> onTasks = Arrays.asList(new RefRuleSnapOnTask(taskId, spapId));

        ScoreRuleSnapRepo snapRepo = Mockito.mock(ScoreRuleSnapRepo.class);
        ResultRankInstance snap = TestFileTool.toBean(this.getClass(), "snapWithByOrgId.json", ResultRankInstance.class);
        Mockito.when(snapRepo.getResultRankInstance(companyId1, spapId)).thenReturn(snap);

        List<EvalUser> list = TestFileTool.toList(this.getClass(), "snapWithByOrgId-listNeedRankTaskUser.json", EvalUser.class);
        Mockito.when(snapRepo.listNeedRankTaskUser(companyId1, taskId)).thenReturn(list);

        //执行测试
        RankComputeOnTaskDmSvc compute = new RankComputeOnTaskDmSvc(companyId1, onTasks);
        compute.forEachRecomputeResult(snapRepo, empRuleRepo);

        //验证结果
        System.out.println("分组数:" + compute.getGroups().getGroups().size());
        List<EvalUser> evalUsers = compute.getGroups().groupGet("OrgId7");
        Assert.assertTrue(evalUsers.size() == 6);
        Assert.assertTrue(evalUsers.stream().filter(evalUser -> evalUser.getStepId().equals("S")).count() == 1L);//6*10%
        Assert.assertTrue(evalUsers.stream().filter(evalUser -> evalUser.getStepId().equals("A")).count() == 4L);//6*60%
        Assert.assertTrue(evalUsers.stream().filter(evalUser -> evalUser.getStepId().equals("D")).count() == 1L);//6*30%
        compute.batchUpdateResult(snapRepo, companyId1);
    }

    @Test
    @DisplayName("任务模式-按排名生成等级-指定部门层级")
    public void testRecomputeResultByRank2ByOrgLevel() {
        String spapId = "1000002";
        String taskId = "t002";
        TenantId companyId1 = new TenantId("10001");
        ScoreRuleSnapRepo snapRepo = Mockito.mock(ScoreRuleSnapRepo.class);
        ResultRankInstance snap = JsonFileTool.toBean("com/polaris/kpi/eval/domain/cycle/dmsvc/RankComputeOnTaskDmSvcTest/testRecomputeResultByRank2ByOrgLevel/byOrgLevelSnap.json", ResultRankInstance.class);
        Mockito.when(snapRepo.getResultRankInstance(companyId1, spapId)).thenReturn(snap);
        List<EvalUser> list = JsonFileTool.toList("com/polaris/kpi/eval/domain/cycle/dmsvc/RankComputeOnTaskDmSvcTest/testRecomputeResultByRank2ByOrgLevel/byOrgLevelUsers.json", EvalUser.class);
        Mockito.when(snapRepo.listNeedRankTaskUser(companyId1, taskId)).thenReturn(list);

        List<RefRuleSnapOnTask> onTasks = new ArrayList<>();
        onTasks.add(new RefRuleSnapOnTask(taskId, spapId));

        RankComputeOnTaskDmSvc compute = new RankComputeOnTaskDmSvc(companyId1, onTasks);
        compute.forEachRecomputeResult(snapRepo, empRuleRepo);
        System.out.println("分组数:" + compute.getGroups().getGroups().size());
        compute.getGroups().getGroups().forEach((level, evalUsers) -> {
            if (level.equals("5")) {
                System.out.println("大于" + level + "的数量" + evalUsers.size());
                evalUsers.stream().allMatch(evalUser -> evalUser.getAtOrgPathHight() >= 5);
                Assert.assertTrue(evalUsers.stream().filter(evalUser -> evalUser.getStepId().equals("S")).count() == 1L);//10*10%
                Assert.assertTrue(evalUsers.stream().filter(evalUser -> evalUser.getStepId().equals("A")).count() == 6L);//10*60%
                Assert.assertTrue(evalUsers.stream().filter(evalUser -> evalUser.getStepId().equals("D")).count() == 3L);//10*30%
            } else {
                System.out.println(level + "级的数量" + evalUsers.size());//各层级内排名
                evalUsers.stream().allMatch(evalUser -> evalUser.getAtOrgPathHight() <= 4);
                Assert.assertTrue(evalUsers.stream().filter(evalUser -> evalUser.getStepId().equals("A")).count() == 1L);//1*60%
            }
        });
        compute.batchUpdateResult(snapRepo, companyId1);
    }

    private void mockRuleRepo() {
        empRuleRepo = Mockito.mock(EmpEvalRuleRepo.class);
        EmpEvalRule t = new EmpEvalRule();
        t.setCreateTotalLevelType(1);
        Mockito.when(empRuleRepo.getBaseEmpEvalRule(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(t);
    }

}