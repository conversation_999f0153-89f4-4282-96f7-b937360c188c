package com.polaris.kpi.org.domain.emp.entity;

import cn.com.polaris.kpi.eval.LevelManager;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.migrationsupport.EnableJUnit4MigrationSupport;

import java.util.ArrayList;
import java.util.List;

@EnableJUnit4MigrationSupport
public class LeaderMangerTest {
    private LeaderManger mg;

    @Before
    public void setUp() throws Exception {
        mg = new LeaderManger((companyId, empId) -> {
            List<LevelManager> leaders = new ArrayList<>();
            LevelManager e = new LevelManager();
            e.setLevel(1);
            leaders.add(e);

            LevelManager e2 = new LevelManager();
            e2.setLevel(2);
            leaders.add(e2);

            LevelManager e3 = new LevelManager();
            e3.setLevel(3);
            leaders.add(e3);
            return leaders;
        });
        mg.load(new TenantId("1"), new EmpId("emp1"));
        Assert.assertTrue(mg.maxLevel() == 3);
        //System.out.println(mg.maxLevel());

    }

    @Test
    public void maxLevel() {
        Assert.assertTrue(mg.maxLevel() == 3);
        System.out.println(mg.maxLevel());

    }

    @Test
    public void getLeader() {
        List<LevelManager> leader = mg.getLeader(2);
        for (LevelManager levelManager : leader) {
            Assert.assertTrue(levelManager.getLevel() == 2);
            System.out.println(levelManager.getLevel());
        }
    }

    @Test
    public void getLeaderPlus() {
        mg = new LeaderManger((companyId, empId) -> {
            List<LevelManager> leaders = new ArrayList<>();
            //LevelManager e = new LevelManager();
            //e.setLevel(1);
            //leaders.add(e);

            //LevelManager e2 = new LevelManager();
            //e2.setLevel(2);
            //leaders.add(e2);

            LevelManager e3 = new LevelManager();
            e3.setLevel(3);
            leaders.add(e3);
            return leaders;
        });
        mg.load(new TenantId("1"), new EmpId("emp1"));
        Assert.assertTrue(mg.maxLevel() == 3);

        List<LevelManager> leader = mg.getLeaderPlus(1);
        Assert.assertTrue(!leader.isEmpty());
        for (LevelManager levelManager : leader) {
            Assert.assertTrue(levelManager.getLevel() == 3);
            System.out.println(levelManager.getLevel());
        }
    }

    @Test
    public void hasDirectLeader() {

        mg = new LeaderManger((companyId, empId) -> {
            List<LevelManager> leaders = new ArrayList<>();
            LevelManager e = new LevelManager();
            e.setLevel(1);
            e.setDirect(true);
            leaders.add(e);

            LevelManager e2 = new LevelManager();
            e2.setLevel(2);
            leaders.add(e2);

            LevelManager e3 = new LevelManager();
            e3.setLevel(3);
            leaders.add(e3);
            return leaders;
        });
        mg.load(new TenantId("1"), new EmpId("emp1"));
        Assert.assertTrue(mg.maxLevel() == 3);

        Assert.assertTrue(mg.hasDirectLeader());
        Assert.assertTrue(mg.getDirectLeader() != null);
    }

}