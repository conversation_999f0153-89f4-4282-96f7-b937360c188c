<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>perf</artifactId>
        <groupId>com.perf.www</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>perf-domain</artifactId>

    <dependencies>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-okhttp</artifactId>
        </dependency>

        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-core</artifactId>
        </dependency>

        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-jackson</artifactId>
        </dependency>

        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-gson</artifactId>
        </dependency>

        <dependency>
            <groupId>io.github.openfeign.form</groupId>
            <artifactId>feign-form</artifactId>
        </dependency>

        <dependency>
            <groupId>com.perf.www</groupId>
            <artifactId>perf-domain-export</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.polaris.kpi</groupId>
            <artifactId>syn-job-domain</artifactId>
            <version>1.0.5</version>
        </dependency>
        <dependency>
            <groupId>com.polaris.sdk</groupId>
            <artifactId>polaris-sdk-type</artifactId>
        </dependency>
        <dependency>
            <groupId>com.perf.www</groupId>
            <artifactId>perf-type</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.xidea</groupId>
            <artifactId>jsel</artifactId>
            <version>0.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>cola-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>cola-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.perf.www</groupId>
            <artifactId>perf-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.polaris.common</groupId>
            <artifactId>polaris-org-acl-domain</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>RELEASE</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-migrationsupport</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.polaris.sdk</groupId>
            <artifactId>polaris-base-common</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.roaringbitmap</groupId>
            <artifactId>RoaringBitmap</artifactId>
            <version>0.9.25</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.14</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <artifactId>mockito-junit-jupiter</artifactId>
            <groupId>org.mockito</groupId>
            <version>3.3.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <artifactId>mockito-core</artifactId>
            <groupId>org.mockito</groupId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
        </dependency>

        <!--        <dependency>-->
<!--            <groupId>com.polaris.ask</groupId>-->
<!--            <artifactId>ask-type</artifactId>-->
<!--            <version>${polaris-ask-version}</version>-->
<!--        </dependency>-->
    </dependencies>

    <!--    <build>-->
    <!--        <plugins>-->
    <!--            <plugin>-->
    <!--                <groupId>org.mybatis.generator</groupId>-->
    <!--                <artifactId>mybatis-generator-maven-plugin</artifactId>-->
    <!--                <version>1.3.2</version>-->
    <!--            </plugin>-->
    <!--        </plugins>-->
    <!--    </build>-->

</project>