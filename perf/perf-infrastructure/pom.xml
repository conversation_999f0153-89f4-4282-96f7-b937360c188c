<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>perf</artifactId>
        <groupId>com.perf.www</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>perf-infrastructure</artifactId>

    <build>
        <plugins>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.2</version>
            </plugin>
        </plugins>
    </build>

    <dependencies>

        <dependency>
            <groupId>com.polaris.common</groupId>
            <artifactId>polaris-org-acl-app</artifactId>
        </dependency>

        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>1.10.6</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>ch.vorburger.mariaDB4j</groupId>-->
        <!--            <artifactId>mariaDB4j</artifactId>-->
        <!--            <scope>test</scope>-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>ch.vorburger.mariaDB4j</groupId>-->
        <!--            <artifactId>mariaDB4j-app</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>ch.vorburger.mariaDB4j</groupId>
            <artifactId>mariaDB4j-db-mac64</artifactId>

        </dependency>
        <dependency>
            <groupId>ch.vorburger.mariaDB4j</groupId>
            <artifactId>mariaDB4j-core</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>ch.vorburger.mariaDB4j</groupId>-->
        <!--            <artifactId>mariaDB4j-db-mac64</artifactId>-->
        <!--            <scope>test</scope>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>ch.vorburger.mariaDB4j</groupId>-->
        <!--            <artifactId>mariaDB4j-db-win32</artifactId>-->
        <!--            <scope>test</scope>-->
        <!--        </dependency>-->


        <dependency>
            <groupId>com.polaris.common</groupId>
            <artifactId>polaris-common-test</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.h2database</groupId>-->
        <!--            <artifactId>h2</artifactId>-->
        <!--        </dependency>-->


        <dependency>
            <groupId>com.perf.www</groupId>
            <artifactId>it-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.0</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>3.5.l6.4</version>
        </dependency>
        <dependency>
            <groupId>cn.com.seendio.polaris</groupId>
            <artifactId>polaris-excel</artifactId>
            <version>1.0.2</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>ooxml-schemas</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>



        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>5.3.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>5.3.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-migrationsupport</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
            <scope>test</scope>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.junit.platform</groupId>-->
        <!--            <artifactId>junit-platform-commons</artifactId>-->
        <!--            <scope>test</scope>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.junit.platform</groupId>-->
        <!--            <artifactId>junit-platform-console</artifactId>-->
        <!--            <scope>test</scope>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-launcher</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-runner</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-suite-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-surefire-provider</artifactId>
            <version>1.3.2</version>
            <scope>test</scope>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.junit.vintage</groupId>-->
        <!--            <artifactId>junit-vintage-engine</artifactId>-->
        <!--            <version>5.3.2</version>-->
        <!--            <scope>test</scope>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>cola-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>cola-common</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.perf.www</groupId>
            <artifactId>perf-domain</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.perf.www</groupId>
            <artifactId>perf-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.perf.www</groupId>
            <artifactId>perf-type</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.polaris.sdk</groupId>
            <artifactId>polaris-sdk-type</artifactId>
        </dependency>
        <dependency>
            <groupId>com.polaris.sdk</groupId>
            <artifactId>polaris-sdk-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.polaris.common</groupId>
            <artifactId>polaris-org-acl-infrastructure</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.polaris.sdk</groupId>
            <artifactId>polaris-base-common</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>


</project>