{"empId": "1216003", "scoreAsString": "自评 88.00  |  同级评 88.00 ｜ 上级评 88.00", "summarys": [{"summary": "8888888很很好子", "dateTime": "2022-10-11 17:50:07", "avatar": {"altMeta": "", "pictureStyle": {"width": 30, "height": 30, "scalePattern": "NONE"}}, "name": "**"}], "hasSummary": true, "companyName": "沃克科技有限公司", "cycleId": "1157301", "cycleStart": "2024.02.01", "taskUserId": "1215001", "fs": 66, "cycle": {"updatedTime": 1707125765000, "cycleEnd": "2024-02-29", "cycleStart": "2024-02-01", "updatedUser": "1093014", "version": 0, "companyId": {"id": "ece4e403-43aa-47f2-bb19-a0dd18b8e98d"}, "evalCnt": 9, "isNewCycle": 1, "createdTime": 1706094920000, "id": "1157301", "cycleValue": {"cycleEnd": "2024-02-29", "year": 2024, "cycleStart": "2024-02-01", "type": "month", "name": "2024年2月", "value": 2}, "cycleStatus": "normal", "createdUser": "1093014"}, "tenantConf": {"updatedTime": 1688460513000, "confCode": "childAdminSeeAnonymous_20240110", "updatedUser": "", "confContent": "{}", "version": 0, "confDesc": "子管理可查看已匿名评分信", "companyId": "ece4e403-43aa-47f2-bb19-a0dd18b8e98d", "createdTime": 1688460513000, "open": 0, "createdUser": ""}, "post": "研发", "empName": "李运营REP", "taskStatus": "finished", "empOrgName": "A测试部门", "types": [{"kpiTypeName": "默认指标类别", "kpiTypeWeight": "-", "typeWrap": {"isOkr": false, "is360": false, "items": [{"itemRule": "1. 能改进现有业务的流程、方法和制度规定，寻找更合理的解决方案（3-5分）\n2. 能在现有制度，规定下开辟途径，灵活解决问题（2分）\n3. 创新能力差（1分）", "scoreValues": {"superior_score": "88.00", "peer_score": "66.00", "itemScore": "13.20", "iScore": "100.20"}, "targetInfo": "目标值：0.0000%", "sumScore": {"superiorScore": 88, "scoreTypes": ["superior_score", "peer_score"], "sum": 154, "itemScore": 66, "peerScore": 66}, "scoreNodes": ["superior_score", "peer_score"], "kpiItemName": "创新能力", "finishInfo": "完成值：\n完成情况：", "kpiItemWeight": "20.00%"}, {"itemRule": "1. 团结同事与部门\n2. 有良好的团队和集体意识，互帮互助\n3. 自觉维护公司形象名誉，不说、不做有负面影响的事", "scoreValues": {"superior_score": "88.00", "peer_score": "66.00", "itemScore": "52.80", "iScore": "100.20"}, "targetInfo": "目标值：0.0000%", "sumScore": {"superiorScore": 88, "scoreTypes": ["superior_score", "peer_score"], "sum": 154, "itemScore": 66, "peerScore": 66}, "scoreNodes": ["superior_score", "peer_score"], "kpiItemName": "团队协调性", "finishInfo": "完成值：\n完成情况：", "kpiItemWeight": "80.00%"}]}}, {"kpiTypeName": "okr类别2", "kpiTypeWeight": "-", "typeWrap": {"isOkr": true, "is360": false, "items": [{"isOkr": true, "okrGoalName": "为第一季度新订单与渠道", "okrKrName": "签单10个A类客户,实现销售600万", "scoreValues": {"superior_score": "88.00", "peer_score": "66.00", "itemScore": "13.20"}, "targetInfo": "目标值：0.0000%", "sumScore": {"superiorScore": 88, "scoreTypes": ["superior_score", "peer_score"], "sum": 154, "itemScore": 66, "peerScore": 66}, "scoreNodes": ["superior_score", "peer_score"], "finishInfo": "完成值：\n完成情况：", "kpiItemWeight": "20.00%"}, {"isOkr": true, "okrGoalName": "为第一季度新订单与渠道", "okrKrName": "新增加5个合作渠道,实现销售500万", "scoreValues": {"superior_score": "88.00", "peer_score": "66.00", "itemScore": "52.80"}, "targetInfo": "目标值：0.0000%", "sumScore": {"superiorScore": 88, "scoreTypes": ["superior_score", "peer_score"], "sum": 154, "itemScore": 66, "peerScore": 66}, "scoreNodes": ["superior_score", "peer_score"], "finishInfo": "完成值：\n完成情况：", "kpiItemWeight": "80.00%"}, {"isOkr": true, "okrGoalName": "为第一季度新GMV", "okrKrName": "签单10个A类客户,实现销售600万", "scoreValues": {"superior_score": "88.00", "peer_score": "66.00", "itemScore": "13.20"}, "targetInfo": "目标值：0.0000%", "sumScore": {"superiorScore": 88, "scoreTypes": ["superior_score", "peer_score"], "sum": 154, "itemScore": 66, "peerScore": 66}, "scoreNodes": ["superior_score", "peer_score"], "finishInfo": "完成值：\n完成情况：", "kpiItemWeight": "20.00%"}, {"isOkr": true, "okrGoalName": "为第一季度新GMV", "okrKrName": "新增加5个合作渠道,实现销售500万", "scoreValues": {"superior_score": "88.00", "peer_score": "66.00", "itemScore": "52.80"}, "targetInfo": "目标值：0.0000%", "sumScore": {"superiorScore": 88, "scoreTypes": ["superior_score", "peer_score"], "sum": 154, "itemScore": 66, "peerScore": 66}, "scoreNodes": ["superior_score", "peer_score"], "finishInfo": "完成值：\n完成情况：", "kpiItemWeight": "80.00%"}]}}, {"kpiTypeName": "基层员工价值观考核", "kpiTypeWeight": "-", "typeWrap": {"isOkr": false, "is360": true, "items": [{"scoringType": "问卷总分", "ask360EvalScore": 82.0}]}}], "cycleEnd": "2024.02.29", "el": "A", "sumScore": {"superiorScore": 88, "scoreTypes": ["superior_score", "peer_score"], "sum": 154, "itemScore": 66, "peerScore": 66}, "refEvals": [], "avatar": "https://static-legacy.dingtalk.com/media/lQLPDhtwSlDwYpfNAmDNAmCwP2waI3J1qnYCiKZNDcDTAA_608_608.png", "evalRule": {"indexRaters": [{"node": "peer", "raters": [{"empId": "1093014", "avatar": "https://static-legacy.dingtalk.com/media/lADPM5HikoKm7UnNAWPNAUQ_324_355.jpg", "type": 0, "empName": "思威"}]}, {"node": "super", "raters": [{"empId": "1090038", "type": 0, "empName": "路飞"}]}], "initiator": "1090038", "editStatus": 0, "scoreConf": {"transferFlag": "true", "multiType": "or"}, "auditResult": {"auditNodes": [], "open": 0}, "finishValueAudit": {"auditNodes": [], "open": 0}, "s3AppointRater": {"auditNodes": [], "open": 0}, "totalLevelRaters": [], "s3SuperRater": {"nodeWeight": 0, "superiorScoreOrder": "sameTime", "rateMode": "item", "auditNodes": [{"transferFlag": "true", "approvalOrder": 1, "weight": 100, "node": "superior_score", "raters": [{"empId": "1090038", "type": 0, "empName": "路飞"}], "multiType": "or", "approverType": "user"}], "open": 1}, "isDeleted": "false", "ruleName": "简易表", "createdTime": 1707125765000, "typeWeightConf": {"checkItemWeight": 1, "limit100Weight": 0, "itemWeightLimit": 100, "open": 0}, "submitWithWeight": false, "enterScore": {"enterScoreEmpType": 1, "enterScoreMethod": "auto", "scoreStartRuleType": "before", "scoreStartRuleDay": 1}, "createdUser": "1090038", "updatedTime": 1707125810000, "finalScore": 154, "fromOldTask": false, "confirmTask": {"openConfirmLT": 0, "auditNodes": [], "modifyItemDimension": "all", "open": 0}, "s3SelfRater": {"open": 0}, "appealConf": {"open": 0}, "deadLineConf": {"open": 0}, "scoreValueConf": {"baseScore": 0, "exceedFullScore": false, "scoreRangeType": "fullScore"}, "kpiTypes": {"datas": [{"updatedTime": 1707125803000, "reserveOkrWeight": 80, "typeOrder": 0, "maxExtraScore": 0, "scoreOptType": 2, "plusSubInterval": {"max": "", "min": ""}, "lockedItems": [], "taskUserId": "1215001", "version": 0, "openOkrScore": 0, "isOkr": "false", "companyId": {"id": "ece4e403-43aa-47f2-bb19-a0dd18b8e98d"}, "isDeleted": "false", "isRaterBack": false, "kpiTypeName": "默认指标类别", "kpiTypeId": "1136423", "createdTime": 1707125803000, "kpiTypeWeight": 0, "items": [{"subtractLimit": 0, "alreadyNodes": [], "itemRule": "1. 能改进现有业务的流程、方法和制度规定，寻找更合理的解决方案（3-5分）\n2. 能在现有制度，规定下开辟途径，灵活解决问题（2分）\n3. 创新能力差（1分）", "reserveOkrWeight": 80, "showFinishBar": 1, "itemUnit": "%", "multipleReviewersType": "or", "plusLimit": 0, "taskUserId": "1215001", "formulaType": 1, "createdTime": 1707125803000, "id": "1224001", "createdUser": "1090038", "scorerType": "exam", "order": 0, "itemFormula": "", "updatedTime": 1707125803000, "thresholdJson": "[]", "typeOrder": 0, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "num", "version": 1, "companyId": {"id": "ece4e403-43aa-47f2-bb19-a0dd18b8e98d"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 0, "itemTargetValue": 0, "itemFieldJson": "[]", "formulaCondition": "[]", "empId": "1216003", "itemType": "non-measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "managerLevel": "", "kpiItemName": "创新能力", "isDeleted": "false", "finalSubmitFinishValue": 0, "kpiTypeWeight": 0, "inputRole": [], "itemWeight": 20, "scoringRule": "", "kpiItemId": "0a79a8aa-2889-421f-81c9-902dc2119507", "maxExtraScore": 0, "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "1216003", "kpiTypeName": "默认指标类别", "kpiTypeId": "1136423", "isNewEmp": 0, "taskId": "1590801", "waitScores": [{"noItemScore": 66, "empId": "1216003", "scoreLevel": "", "mergeRsInfos": [], "scoreWeight": 100, "scorerId": "1093014", "taskUserId": "1215001", "score": 66, "isDeleted": "false", "reviewersType": "and", "empScore": 66, "createdTime": 1707125817000, "id": "ab4bb4d1-b924-4884-9855-d16078fdf4ac", "updateTypeLevel": false, "createdUser": "1090038", "scorerType": "peer_score", "updatedTime": 1707125872000, "kpiItemId": "0a79a8aa-2889-421f-81c9-902dc2119507", "finalScore": 13.2, "approvalOrder": 1, "scoreComment": "互评6666666", "updatedUser": "1093014", "version": 0, "companyId": {"id": "ece4e403-43aa-47f2-bb19-a0dd18b8e98d"}, "kpiTypeId": "1136423", "auditStatus": "pass", "finalWeightScore": 13.2, "taskId": "1590801", "waitMergeWeight": 0}, {"noItemScore": 0, "empId": "1216003", "scoreLevel": "", "mergeRsInfos": [], "scoreWeight": 100, "scorerId": "1090038", "taskUserId": "1215001", "score": 88, "isDeleted": "false", "reviewersType": "or", "empScore": 88, "createdTime": 1707125873000, "id": "da8938cb-6c00-4cb7-8280-673341080dd4", "updateTypeLevel": false, "createdUser": "1090038", "scorerType": "superior_score", "updatedTime": 1707125929000, "kpiItemId": "0a79a8aa-2889-421f-81c9-902dc2119507", "finalScore": 17.6, "approvalOrder": 1, "scoreComment": "上级888", "updatedUser": "1090038", "version": 0, "companyId": {"id": "ece4e403-43aa-47f2-bb19-a0dd18b8e98d"}, "kpiTypeId": "1136423", "auditStatus": "pass", "finalWeightScore": 0, "taskId": "1590801", "waitMergeWeight": 0}]}, {"subtractLimit": 0, "alreadyNodes": [], "itemRule": "1. 团结同事与部门\n2. 有良好的团队和集体意识，互帮互助\n3. 自觉维护公司形象名誉，不说、不做有负面影响的事", "reserveOkrWeight": 80, "showFinishBar": 1, "itemUnit": "%", "multipleReviewersType": "or", "plusLimit": 0, "taskUserId": "1215001", "formulaType": 1, "createdTime": 1707125803000, "id": "1224002", "createdUser": "1090038", "scorerType": "exam", "order": 1, "itemFormula": "", "updatedTime": 1707125803000, "thresholdJson": "[]", "typeOrder": 0, "plusSubInterval": {"max": "", "min": ""}, "inputFormat": "num", "version": 1, "companyId": {"id": "ece4e403-43aa-47f2-bb19-a0dd18b8e98d"}, "resultInputType": "exam", "itemFullScoreCfg": "false", "mustResultInput": 0, "itemTargetValue": 0, "itemFieldJson": "[]", "formulaCondition": "[]", "empId": "1216003", "itemType": "non-measurable", "scorerObjId": [{"objItems": [], "objType": "role"}, {"objItems": [], "objType": "user"}, {"objItems": [], "objType": "manager"}], "managerLevel": "", "kpiItemName": "团队协调性", "isDeleted": "false", "finalSubmitFinishValue": 0, "kpiTypeWeight": 0, "inputRole": [], "itemWeight": 80, "scoringRule": "", "kpiItemId": "33337ae2-4e18-4fbb-b8ee-fccc94634ae2", "maxExtraScore": 0, "appointAudits": [], "openOkrScore": 0, "isOkr": "false", "resultInputEmpId": "1216003", "kpiTypeName": "默认指标类别", "kpiTypeId": "1136423", "isNewEmp": 0, "taskId": "1590801", "waitScores": [{"noItemScore": 66, "empId": "1216003", "scoreLevel": "", "mergeRsInfos": [], "scoreWeight": 100, "scorerId": "1093014", "taskUserId": "1215001", "score": 66, "isDeleted": "false", "reviewersType": "and", "empScore": 66, "createdTime": 1707125817000, "id": "1f296ec8-070b-4055-9555-4f41276f5035", "updateTypeLevel": false, "createdUser": "1090038", "scorerType": "peer_score", "updatedTime": 1707125872000, "kpiItemId": "33337ae2-4e18-4fbb-b8ee-fccc94634ae2", "finalScore": 52.8, "approvalOrder": 1, "scoreComment": "互评6666666", "updatedUser": "1093014", "version": 0, "companyId": {"id": "ece4e403-43aa-47f2-bb19-a0dd18b8e98d"}, "kpiTypeId": "1136423", "auditStatus": "pass", "finalWeightScore": 52.8, "taskId": "1590801", "waitMergeWeight": 0}, {"noItemScore": 0, "empId": "1216003", "scoreLevel": "", "mergeRsInfos": [], "scoreWeight": 100, "scorerId": "1090038", "taskUserId": "1215001", "score": 88, "isDeleted": "false", "reviewersType": "or", "empScore": 88, "createdTime": 1707125873000, "id": "816b8e06-2008-452c-89d1-6358b09e9364", "updateTypeLevel": false, "createdUser": "1090038", "scorerType": "superior_score", "updatedTime": 1707125929000, "kpiItemId": "33337ae2-4e18-4fbb-b8ee-fccc94634ae2", "finalScore": 70.4, "approvalOrder": 1, "scoreComment": "上级888", "updatedUser": "1090038", "version": 0, "companyId": {"id": "ece4e403-43aa-47f2-bb19-a0dd18b8e98d"}, "kpiTypeId": "1136423", "auditStatus": "pass", "finalWeightScore": 0, "taskId": "1590801", "waitMergeWeight": 0}]}], "alreadyScores": [], "createdUser": "1090038", "waitScores": []}], "modNum": 3, "mergedAudits": []}, "createTotalLevelType": 1, "updatedUser": "1090038", "levelGroupId": "", "indicatorCnt": 2, "version": 0, "evaluateType": "simple", "scoreView": {"mutualScoreAnonymous": "true", "mutualScoreViewRule": {"superior": "", "mutual": "", "appoint": "", "examinee": ""}, "superiorScoreAnonymous": "true", "appointScoreViewRule": {"superior": "", "mutual": "", "appoint": "", "examinee": ""}, "superiorScoreViewRule": {"superior": "score,attach", "mutual": "score,attach", "appoint": "score,attach", "examinee": "score,attach"}, "selfScoreViewRule": {"superior": "", "mutual": "", "appoint": "", "examinee": "score,attach"}, "appointScoreAnonymous": "true"}, "companyId": {"id": "ece4e403-43aa-47f2-bb19-a0dd18b8e98d"}, "publishResult": {"toEmps": [{"objType": "emp"}, {"objType": "scoreEmp"}], "type": "afterFinished", "opEmps": [], "toDetailEmps": [], "dimension": 11, "open": 1}, "s3SubRater": {"raters": [], "open": 0}, "s3PeerRater": {"transferFlag": "true", "nodeWeight": 100, "rateMode": "item", "node": "peer_score", "raters": [{"empId": "1093014", "avatar": "https://static-legacy.dingtalk.com/media/lADPM5HikoKm7UnNAWPNAUQ_324_355.jpg", "type": 0, "empName": "思威"}], "multiType": "and", "approverType": "user", "open": 1}, "confirmResult": {"auto": 0, "sign": 1, "dimension": 11, "open": 1}, "editExeIndi": {"auditNodes": [], "open": 0}, "commentConf": {"scoreSummarySwitch": -1, "commentFlag": "notRequired", "plusOrSubComment": 0}, "showResultType": 6, "empEvalId": "1215001", "notAllowExceed": false}, "scoreView": {"mutualScoreAnonymous": "true", "mutualScoreViewRule": {"superior": "", "mutual": "", "appoint": "", "examinee": ""}, "superiorScoreAnonymous": "true", "appointScoreViewRule": {"superior": "", "mutual": "", "appoint": "", "examinee": ""}, "superiorScoreViewRule": {"superior": "score,attach", "mutual": "score,attach", "appoint": "score,attach", "examinee": "score,attach"}, "selfScoreViewRule": {"superior": "", "mutual": "", "appoint": "", "examinee": "score,attach"}, "appointScoreAnonymous": "true"}, "signPic": {"altMeta": ""}, "taskName": "2024年2月签名与匿名", "taskId": "1590801", "signImg": "https://topscrm.oss-cn-hangzhou.aliyuncs.com/info/sGZr4Qwsnd.png", "hasRefEvals": false}