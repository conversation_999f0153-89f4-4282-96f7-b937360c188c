-- 部门数据
INSERT INTO emp_organization (id, company_id, org_id, org_name, org_code, parent_org_id, status, type, ding_org_id) VALUES
(1, 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1026452', '人力资源部', '1026452', NULL, 'valid', 'dept', 'ding123'),
(2, 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1026453', '财务部', '1026453', NULL, 'valid', 'dept', 'ding124'),
(3, 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1026454', '人力资源部-招聘组', '1026452|1026454', '1026452', 'valid', 'dept', 'ding125');

-- 员工数据
INSERT INTO employee_base_info (id, company_id, employee_id, name, status, is_delete, ding_user_id) VALUES
(1, 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1090038', '张三', 'on_the_job', 'false', 'ding001'),
(2, 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1090039', '李四', 'on_the_job', 'false', 'ding002'),
(3, 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1090040', '王五', 'on_the_job', 'false', 'ding003');

-- 部门员工关联数据
INSERT INTO emp_ref_org (id, company_id, emp_id, org_id, ref_type) VALUES
(1, 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1090038', '1026452', 'org'),
(2, 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1090039', '1026453', 'org'),
(3, 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1090040', '1026454', 'org');

-- 部门主管数据
INSERT INTO emp_ref_org (id, company_id, emp_id, org_id, ref_type) VALUES
(4, 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1090038', '1026452', 'manager'),
(5, 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1090039', '1026453', 'manager');
