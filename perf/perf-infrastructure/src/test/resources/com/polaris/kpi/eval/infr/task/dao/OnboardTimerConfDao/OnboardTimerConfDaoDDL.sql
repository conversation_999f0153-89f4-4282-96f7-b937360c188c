drop table if exists onboard_timer_conf;
create table onboard_timer_conf
(
    `id`            bigint comment '主键' primary key,
    `company_id`    varchar(50) not null comment '公司id',
    `confirm_timer` text comment '确认提醒配置 {"open":1,conf:["timerType": 2, "id": "10000", "modCode": 1000001, "eachType": 1,"conf":{"beforDays":0,"afterDays":46,"type":1,"runTime":"11:00"}]}',
    `scoring_timer` text comment '评分提醒配置 {"open":1,[{"timerType": 2, "id": "10000", "modCode": 1000002, "eachType": 1, "conf": {"beforDays": 0, "afterDays": 0, "type": 1, "runTime": "11:00"}}]}',
    `created_time`  datetime    default now(),
    `created_user`  varchar(50) default '',
    `updated_time`  datetime    default now(),
    `updated_user`  varchar(50) default '',
    `is_deleted`    varchar(5)  default 'false',
    `version`       int         default 0 comment '版本号',
    index otc_idx_companyId (company_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 comment '新人考核的定时提醒配置';

drop table if exists onboard_timer_item;
create table onboard_timer_item
(
    `id`                    bigint comment '主键' primary key,
    `company_id`            varchar(50) not null comment '公司id',
    `onboard_timer_conf_id` bigint      not null comment 'onboard_timer_conf.id',
    `mod_code`              bigint      not null comment '子模块编号:[1000001=新人任务确认],[1000002=新人评分提醒管理员],[1000003=新人评分提醒评分人]',
    `conf`                  text comment 'json配置对象',
    `timer_type`            int         default 1 comment '执行分类: forEach执行=2, 单次执行=1',
    `each_type`             int         default 1 comment 'forEach分类: 每天=1, 每小时=2',
    `exe_cond_sql`          text comment '检测执行条件的sql,参数中要的company_id',
    `biz_method`            text comment '执行业务操作方法',
    `descr`                 varchar(128) comment '业务配置描述',
    `lock_id`               bigint      default 0 comment '并发执行的标记锁id,0=未锁定, 大于0表示执行的锁id',
    `created_time`          datetime    default now(),
    `created_user`          varchar(50) default '',
    `updated_time`          datetime    default now(),
    `updated_user`          varchar(50) default '',
    `is_deleted`            varchar(5)  default 'false',
    `version`               int         default 0 comment '版本号',
    index oti_idx_companyId (company_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 comment '新人考核的定时提醒配置';

drop table if exists onboard_timer_log;
create table onboard_timer_log
(
    `id`                    bigint comment '主键' primary key,
    `company_id`            varchar(50) not null comment '公司id',
    `onboard_timer_conf_id` bigint      not null comment '子模块编号:[1000001=新人任务确认],[1000002=新人评分提醒]',
    `conf`                  text comment 'json配置对象',
    `lock_id`               bigint      default 0 comment '并发执行的标记锁id,0=未锁定, 大于0表示执行的锁id',
    `created_time`          datetime    default now(),
    `created_user`          varchar(50) default '',
    `updated_time`          datetime    default now(),
    `updated_user`          varchar(50) default '',
    `is_deleted`            varchar(5)  default 'false',
    `version`               int         default 0 comment '版本号',
    index otl_idx_companyId (company_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 comment '新人考核的定时提醒执行记录';