
#
# SELECT distinct u.org_id FROM perf_evaluate_task_user as u INNER JOIN emp_eval_rule as r on r.company_id = u.company_id and r.emp_eval_id = u.id INNER JOIN result_rank_instance_member as rrim on rrim.company_id= u.company_id and u.id = rrim.task_user_id WHERE (u.is_deleted='false')
# select *from score_rule  where company_id = 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d';
# select *from score_range  where company_id = 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d';
# select * from grade_step where company_id = 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d' ;
# select * from dept_ref_rule where company_id = 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d';
#
# select c.* from perf_evaluate_cycle c where c.id = '1156703' ;
# select * from perf_evaluate_task_base b where   id = '1584301';
# select * from perf_evaluate_task_user  where task_id ='1584301' ;
# select * from emp_eval_rule where emp_eval_id in(select id from perf_evaluate_task_user  where task_id ='1584301' )
# select * from perf_evaluate_task_score_result where task_id ='1584301' and is_deleted = 'false' ;
#
# select * from perf_evaluate_task_audit where task_id ='1584301' and is_deleted = 'false' ;
# select * from perf_evaluate_task_kpi where task_id ='1584301' and is_deleted = 'false'  ;
# select * from perf_evaluate_task_item_score_rule where task_id ='1584301' ;
# select * from perf_evaluate_task_ref_okr where task_id ='1584301' and is_deleted = 'false' ;
# select * from perf_evaluate_task_okr_type where task_id ='1584301' and is_deleted = 'false' ;
# select * from emp_eval_kpi_type where  task_user_id ='1857576' and is_deleted = 'false' ;
# select * from perf_evaluate_task_score_rule where task_id ='1584301' and is_deleted = 'false' ;
# select * from company_msg_center where link_id = '1857576' and handler_status = 'false';
# select * from operation_log where ref_id = '1857576';

insert  into  result_rank_instance_member(company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time)
select company_id, '10001', id, emp_id, emp_org_id, 1, 'level1', created_time, updated_time from perf_evaluate_task_user where task_id ='1584301';
1011037, 1011602, 1013211, 1018301, 1019701
select *FROM perf_evaluate_task_user where task_id='1584301';
select *from result_rank_instance_member;

SELECT min(u.at_org_path_hight) at_org_path_hight
FROM perf_evaluate_task_user as u
         INNER JOIN emp_eval_rule as r on r.company_id = u.company_id and r.emp_eval_id = u.id
         INNER JOIN result_rank_instance_member as rrim on rrim.company_id = u.company_id and u.id = rrim.task_user_id
WHERE (u.is_deleted = 'false' AND u.company_id = 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d');

SELECT distinct u.org_id
FROM perf_evaluate_task_user as u
         INNER JOIN emp_eval_rule as r on r.company_id = u.company_id and r.emp_eval_id = u.id
         INNER JOIN result_rank_instance_member as rrim on rrim.company_id = u.company_id and u.id = rrim.task_user_id
WHERE (u.is_deleted = 'false' AND u.company_id = 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d' AND rrim.distribution_id = '10001');


SELECT p.org_id FROM emp_organization as p WHERE (p.parent_org_id = '1011037' AND p.company_id = 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d' AND p.status='valid');



SELECT IFNULL(u.evaluation_level,'none') evaluation_level,count(1) cnt FROM perf_evaluate_task_user as u INNER JOIN emp_eval_rule as r on r.company_id = u.company_id and r.emp_eval_id = u.id
    INNER JOIN result_rank_instance_member as rrim on rrim.company_id= u.company_id and u.id = rrim.task_user_id
WHERE (u.is_deleted='false' AND u.company_id = 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d' AND rrim.distribution_id = '10001' AND u.at_org_code_path like '%|1019701|%' )
GROUP BY u.evaluation_level;


SELECT u.task_status, IFNULL(u.evaluation_level, 'none') level, count(1) cnt
FROM perf_evaluate_task_user as u
         INNER JOIN emp_eval_rule as r on r.company_id = u.company_id and r.emp_eval_id = u.id
         INNER JOIN result_rank_instance_member as rrim on rrim.company_id = u.company_id and u.id = rrim.task_user_id
WHERE (u.is_deleted = 'false' AND u.company_id = 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d' AND rrim.distribution_id = '10001' AND
       u.at_org_code_path LIKE CONCAT('%', '|1011037|', '%'))
GROUP BY u.task_status,u.evaluation_level


    ece4e403-43aa-47f2-bb19-a0dd18b8e98d(String), 10001(String), |1011037|(String)
SELECT IFNULL(u.evaluation_level,'none') evaluation_level,count(1) cnt FROM perf_evaluate_task_user as u INNER JOIN emp_eval_rule as r on r.company_id = u.company_id and r.emp_eval_id = u.id INNER JOIN result_rank_instance_member as rrim on rrim.company_id= u.company_id and u.id = rrim.task_user_id WHERE (u.is_deleted='false' AND u.company_id = ? AND rrim.distribution_id = ? AND u.at_org_code_path = ? ) GROUP BY u.evaluation_level
    ece4e403-43aa-47f2-bb19-a0dd18b8e98d(String), 10001(String), 1011037(String)
