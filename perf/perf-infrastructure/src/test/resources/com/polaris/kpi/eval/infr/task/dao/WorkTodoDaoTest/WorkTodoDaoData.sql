
INSERT INTO perf_evaluate_cycle (id, company_id, name, year, type, value, cycle_start, cycle_end, eval_cnt, from_task_id, created_user, updated_user, created_time, updated_time, version, cycle_status, is_new_cycle) VALUES (1022101, 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '路飞重构周期01', 2021, 'month', 1, '2021-01-01', '2021-01-31', 50, '0', '1093014', '', '2022-06-07 11:12:50', '2022-08-24 14:35:31', 0, 'normal', 0);
INSERT INTO perf_evaluate_cycle (id, company_id, name, year, type, value, cycle_start, cycle_end, eval_cnt, from_task_id, created_user, updated_user, created_time, updated_time, version, cycle_status, is_new_cycle) VALUES (1119401, 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '变更演示', 2022, 'month', 11, '2022-11-01', '2022-11-30', 12, '0', '1093014', '1093014', '2022-12-03 10:03:03', '2023-02-22 16:35:38', 0, 'normal', 1);
INSERT INTO perf_evaluate_cycle (id, company_id, name, year, type, value, cycle_start, cycle_end, eval_cnt, from_task_id, created_user, updated_user, created_time, updated_time, version, cycle_status, is_new_cycle) VALUES (1134603, 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '2022年9月周期', 2022, 'month', 9, '2022-09-01', '2022-09-30', 26, '0', '1093017', '1093017', '2022-12-30 09:52:44', '2022-12-31 17:23:07', 0, 'normal', 1);
INSERT INTO perf_evaluate_cycle (id, company_id, name, year, type, value, cycle_start, cycle_end, eval_cnt, from_task_id, created_user, updated_user, created_time, updated_time, version, cycle_status, is_new_cycle) VALUES (1141001, 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '2023年2月周期评分人匿名', 2023, 'month', 2, '2023-02-01', '2023-02-28', 7, '0', '1090071', '1090071', '2023-02-19 14:36:08', '2023-02-22 15:32:33', 0, 'normal', 1);
INSERT INTO perf_evaluate_cycle (id, company_id, name, year, type, value, cycle_start, cycle_end, eval_cnt, from_task_id, created_user, updated_user, created_time, updated_time, version, cycle_status, is_new_cycle) VALUES (1155601, 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '2023年8月考核周期测试', 2023, 'month', 8, '2023-08-01', '2023-08-31', 10, '0', '1090071', '1090071', '2023-08-23 17:32:04', '2023-09-13 10:57:32', 0, 'normal', 1);

INSERT INTO perf_evaluate_task_base (id, company_id, task_name, templ_base_id, templ_name, cycle_start_date, cycle_end_date, task_desc, evaluation_staff, exclude_staff, create_task_type, create_task_date_type, day, visible_type, task_status, is_deleted, created_user, created_time, updated_user, updated_time, cycle_type, templ_desc, templ_item_json, templ_initiate_json, templ_affirm_json, templ_evaluate_json, score_start_rule_type, score_start_rule_day, enter_score_method, public_type, templ_base_json, evaluate_type, public_emp_json, templ_execute_json, result_affirm, affirm_signature, templ_points_json, can_appeal, appeal_receiver, custom_full_score, enter_score_emp_type, public_dimension, public_to_emp, auto_result_affirm, auto_result_affirm_day, level_group_id, version, cycle_id, is_new_emp, total_cnt, draw_up_cnt, start_cnt, finish_cnt, confirm_task, edit_exe_indi, enter_score, audit_result, publish_result, comment_conf, score_sort_conf, appeal_conf, score_view, confirm_result, performance_type, score_conf, finish_value_audit, dead_line_conf) VALUES ('1032001', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '通知00-0622', 'd65e1235-bf0d-435f-a6e6-0e1695546c3a', 'lufei_自定自评', '2021-01-01', '2021-01-31', '', '[{"objItems":[{"objId":"1090038","objName":"路飞"}],"objType":"user"}]', '[]', 'manual', 'beforeOn', 0, null, 'published', 'false', '1090038', '2022-06-22 15:09:57', '1090038', '2022-06-22 15:10:04', 'month', null, '[{"companyId":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d","createdTime":1654935751000,"createdUser":"1090038","createdUserName":"路飞","emptyType":false,"isDeleted":"false","isOkr":"false","isTemporary":"true","isTypeLocked":"","items":[{"appointScoreFlag":"false","companyId":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d","createdUserName":"詹懿力","formulaCondition":"[]","id":"42a68c63-fec7-466a-b8d1-634a76e3b408","isDeleted":"false","isNewEmp":0,"isTemporary":"false","itemEvaluate":{"appointScoreFlag":"false","companyId":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d","createdTime":1654935751000,"createdUser":"1090038","id":"e55b64c0-62ff-4891-93df-aaf2090c2d15","isDeleted":"false","kpiItemId":"2746ab41-bef0-4abc-bd1d-6d875023b3ed","kpiTypeId":"","mutualScoreAnonymous":"","mutualScoreAttendRule":"","mutualScoreFlag":"false","mutualScoreVacancy":"","mutualScoreViewRule":"","mutualUserType":"user","peerScoreWeight":0.00,"selfScoreFlag":"true","selfScoreViewRule":"","selfScoreWeight":100.00,"subScoreWeight":0.00,"superiorScoreFlag":"false","superiorScoreVacancy":"","superiorScoreViewRule":"null","templBaseId":"d65e1235-bf0d-435f-a6e6-0e1695546c3a"},"itemFieldJson":"[]","itemFormula":"","itemName":"新建考核指标","itemPlanValue":10.00,"itemRule":"11111111","itemType":"measurable","itemUnit":"立方米","itemValue":20.00,"itemWeight":100.00,"kpiItemId":"2746ab41-bef0-4abc-bd1d-6d875023b3ed","kpiItemName":"新建考核指标","kpiTypeId":"19e75fb2-41d3-4bd8-97f0-9e9cb4786210","multipleReviewersType":"or","mustResultInput":0,"mutualScoreFlag":"false","mutualUserType":"user","order":0,"plusLimit":0.00,"resultInputType":"exam","resultInputUserId":"","scorerObjId":"[{\\"obj_type\\":\\"role\\",\\"objItems\\":[]},{\\"obj_type\\":\\"user\\",\\"objItems\\":[]}]","scorerType":"exam","scoringRule":"222222","selfScoreFlag":"true","showFinishBar":1,"showTargetValue":"false","subtractLimit":0.00,"superiorScoreFlag":"false","templBaseId":"d65e1235-bf0d-435f-a6e6-0e1695546c3a","thresholdJson":"[]","version":0}],"maxExtraScore":0.00,"okr":false,"order":0,"reserveOkrWeight":100.00,"templBaseId":"d65e1235-bf0d-435f-a6e6-0e1695546c3a","typeId":"19e75fb2-41d3-4bd8-97f0-9e9cb4786210","typeName":"默认指标类别","typeWeight":0.00,"updatedTime":1654935751000}]', '{"companyId":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d","createdTime":1654935723000,"createdUser":"1090038","enableAddReduce":"false","examineObjData":[{"objItems":[],"objType":"dept"},{"objItems":[{"objId":"1090038","objName":"路飞"}],"objType":"user"},{"objItems":[],"objType":"role"}],"excludeEmpData":[],"id":"a29a3dda-6a23-48c4-b0ae-b4bf219a684c","initiatorList":[{"empId":"1090038","empName":"路飞"}],"initiatorType":"manager","isAllEmptyType":"false","isAllOkrType":"false","isDeleted":"false","leaveEmpIds":[],"taskDesc":"","templBaseId":"d65e1235-bf0d-435f-a6e6-0e1695546c3a","updatedTime":1654935751000}', '{"auditList":[],"companyId":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d","confirmFlag":"false","createdTime":1654935751000,"createdUser":"1090038","id":"2b4aac6c-347c-4ede-8a71-0fa48bbf8acf","isDeleted":"false","multipleReviewersType":"or","newFlag":"true","noChangeSkipFlag":"false","templBaseId":"d65e1235-bf0d-435f-a6e6-0e1695546c3a","updatedTime":1654935751000,"vacancyApproverType":"superior"}', '{"appointScoreViewRule":"{\\"anonymous\\":\\"false\\"}","auditFlag":"false","commentFlag":"notRequired","companyId":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d","createdTime":1654935752000,"createdUser":"1090038","duplicateType":"false","enterScoreEmpType":1,"enterScoreMethod":"auto","evaluateType":"custom","finalAuditList":[],"id":"685ab6ca-a42e-4744-891b-732da6034f8a","isAddAuditComment":0,"isDeleted":"false","multipleReviewersType":"or","mutualScoreAnonymous":"true","roleVacancyType":"skip","scoreStartRuleDay":1,"scoreStartRuleType":"before","selfScoreFlag":"true","selfScoreViewRule":"{\\"mutual\\":\\"\\",\\"superior\\":\\"\\",\\"appoint\\":\\"\\",\\"examinee\\":\\"score,attach\\"}","selfScoreWeight":0.00,"submitLevelFlag":"false","superiorScoreViewRule":"{\\"anonymous\\":\\"false\\"}","superiorScoreWeight":100.00,"templBaseId":"d65e1235-bf0d-435f-a6e6-0e1695546c3a","typeWeightLimitFlag":"true","typeWeightSwitch":"close","updatedTime":1654935752000}', 'before', 1, 'auto', 'auto', '{"affirmSignature":"false","appealReceiver":"","autoResultAffirm":"false","autoResultAffirmDay":0,"baseScore":0.00,"canAppeal":"false","canAppealDay":10,"checkItemWeight":100,"checkItemWeightFlag":"true","cycleType":"month","evaluateType":"custom","id":"d65e1235-bf0d-435f-a6e6-0e1695546c3a","isNewEmp":0,"name":"lufei_自定自评","newEmp":false,"pointsRule":"close","post":"","publicDimension":3,"publicEmpJson":"[{\\"objItems\\":[],\\"objType\\":\\"taskAdmin\\"}]","publicToEmp":"[{\\"objType\\":\\"emp\\"},{\\"objType\\":\\"scoreEmp\\"},{\\"objType\\":\\"ccEmp\\"}]","publicType":"auto","resultAffirm":"false","resultAppealNode":70,"scoreRangeType":"fullScore","status":"published","templDesc":"","typeWeightLimitFlag":"true","typeWeightSwitch":"close","version":0}', 'custom', '[{"fixUser":false,"objItems":[],"objType":"taskAdmin","starter":true}]', '{"auditList":[],"auditOpen":1,"changeFlag":"false","changeUser":"admin","companyId":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d","createdTime":1654935752000,"createdUser":"1090038","id":"52c7f642-473e-400b-9f1d-cafc2d14ef93","isDeleted":"false","multipleReviewersType":"or","templBaseId":"d65e1235-bf0d-435f-a6e6-0e1695546c3a","updatedTime":1654935752000,"vacancyApproverType":"superior"}', 'false', 'false', '[{"itemType":"measurable","itemUnit":"立方米","kpiItemId":"2746ab41-bef0-4abc-bd1d-6d875023b3ed","kpiItemName":"新建考核指标","kpiTypeId":"19e75fb2-41d3-4bd8-97f0-9e9cb4786210","kpiTypeName":"默认指标类别"}]', 'false', '', null, 1, 3, '[{"objType":"emp"},{"objType":"scoreEmp"},{"objType":"ccEmp"}]', 'false', 0, '', 0, 1022101, 0, 1, 0, 1, 0, null, null, '', null, null, null, null, null, null, null, 1, null, null, null);
INSERT INTO perf_evaluate_task_base (id, company_id, task_name, templ_base_id, templ_name, cycle_start_date, cycle_end_date, task_desc, evaluation_staff, exclude_staff, create_task_type, create_task_date_type, day, visible_type, task_status, is_deleted, created_user, created_time, updated_user, updated_time, cycle_type, templ_desc, templ_item_json, templ_initiate_json, templ_affirm_json, templ_evaluate_json, score_start_rule_type, score_start_rule_day, enter_score_method, public_type, templ_base_json, evaluate_type, public_emp_json, templ_execute_json, result_affirm, affirm_signature, templ_points_json, can_appeal, appeal_receiver, custom_full_score, enter_score_emp_type, public_dimension, public_to_emp, auto_result_affirm, auto_result_affirm_day, level_group_id, version, cycle_id, is_new_emp, total_cnt, draw_up_cnt, start_cnt, finish_cnt, confirm_task, edit_exe_indi, enter_score, audit_result, publish_result, comment_conf, score_sort_conf, appeal_conf, score_view, confirm_result, performance_type, score_conf, finish_value_audit, dead_line_conf) VALUES ('1122501', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '指标确认修改', null, null, null, null, null, null, null, null, null, null, null, 'published', 'false', '1093017', '2022-12-03 13:38:30', '1093017', '2023-08-22 19:45:04', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, '', null, null, null, 3, null, 'false', 0, '', 0, 1119401, 0, 2, 2, 1, 1, '{"adminType":"child","openConfirmLT":0,"modifyAuditFlag":"true","opEmpId":"1093014","noChangeSkipFlag":"false","tenantId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"auditNodes":[{"transferFlag":"true","approvalOrder":1,"modifyFlag":"true","approverInfo":"","approverName":"被考核人","raters":[],"multiType":"or","approverType":"taskEmp"},{"transferFlag":"false","approvalOrder":2,"modifyFlag":"true","approverInfo":"1093017","approverName":"志林","raters":[],"multiType":"or","approverType":"user"}],"modifyItemDimension":"all","taskId":"1122501","open":1}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"auditNodes":[],"commentReq":0,"mergeConf":1,"multiType":"or","open":0,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"notPublic":false,"open":0}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"exeType":0,"sameTime":false,"sortItems":[{"name":"自评","sort":1,"type":10},{"name":"同级互评","sort":2,"type":20},{"name":"下级互评","sort":3,"type":30},{"name":"上级评分","sort":4,"type":40},{"name":"指定评","sort":5,"type":50}]}', '{"appealReceiver":[{"objItems":[],"objType":"taskAdmin"}],"canAppealDay":10,"open":0,"resultAppealNode":70}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"score,attach","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"score,attach","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auto":0,"dimension":3,"open":0,"sign":0}', 1, '{"multiType":"or","transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', null, null);
INSERT INTO perf_evaluate_task_base (id, company_id, task_name, templ_base_id, templ_name, cycle_start_date, cycle_end_date, task_desc, evaluation_staff, exclude_staff, create_task_type, create_task_date_type, day, visible_type, task_status, is_deleted, created_user, created_time, updated_user, updated_time, cycle_type, templ_desc, templ_item_json, templ_initiate_json, templ_affirm_json, templ_evaluate_json, score_start_rule_type, score_start_rule_day, enter_score_method, public_type, templ_base_json, evaluate_type, public_emp_json, templ_execute_json, result_affirm, affirm_signature, templ_points_json, can_appeal, appeal_receiver, custom_full_score, enter_score_emp_type, public_dimension, public_to_emp, auto_result_affirm, auto_result_affirm_day, level_group_id, version, cycle_id, is_new_emp, total_cnt, draw_up_cnt, start_cnt, finish_cnt, confirm_task, edit_exe_indi, enter_score, audit_result, publish_result, comment_conf, score_sort_conf, appeal_conf, score_view, confirm_result, performance_type, score_conf, finish_value_audit, dead_line_conf) VALUES ('1534610', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '2022年9月周期研发2部考核', null, null, null, null, null, null, null, null, null, null, null, 'published', 'false', '1093001', '2022-12-30 15:06:53', '1093001', '2023-08-22 19:45:14', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, '', null, null, null, 3, null, 'false', 0, '', 0, 1134603, 0, 3, 1, 1, 0, '{"auditNodes":[{"approvalOrder":1,"approverInfo":"","approverType":"taskEmp","modifyFlag":"true","multiType":"or","raters":[],"transferFlag":"true"}],"confirmLTDay":0,"modifyAuditFlag":"true","noChangeSkipFlag":"false","open":1,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"auditNodes":[],"commentReq":0,"mergeConf":1,"multiType":"or","open":0,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"notPublic":false,"open":0}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"exeType":0,"sameTime":false,"sortItems":[{"name":"自评","sort":1,"type":10},{"name":"同级互评","sort":2,"type":20},{"name":"下级互评","sort":3,"type":30},{"name":"上级评分","sort":4,"type":40},{"name":"指定评","sort":5,"type":50}]}', '{"appealReceiver":[{"objItems":[],"objType":"taskAdmin"}],"canAppealDay":10,"open":0,"resultAppealNode":70}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"score,attach","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"score,attach","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auto":0,"dimension":3,"open":0,"sign":0}', 1, '{"multiType":"or","transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', null, null);
INSERT INTO perf_evaluate_task_base (id, company_id, task_name, templ_base_id, templ_name, cycle_start_date, cycle_end_date, task_desc, evaluation_staff, exclude_staff, create_task_type, create_task_date_type, day, visible_type, task_status, is_deleted, created_user, created_time, updated_user, updated_time, cycle_type, templ_desc, templ_item_json, templ_initiate_json, templ_affirm_json, templ_evaluate_json, score_start_rule_type, score_start_rule_day, enter_score_method, public_type, templ_base_json, evaluate_type, public_emp_json, templ_execute_json, result_affirm, affirm_signature, templ_points_json, can_appeal, appeal_receiver, custom_full_score, enter_score_emp_type, public_dimension, public_to_emp, auto_result_affirm, auto_result_affirm_day, level_group_id, version, cycle_id, is_new_emp, total_cnt, draw_up_cnt, start_cnt, finish_cnt, confirm_task, edit_exe_indi, enter_score, audit_result, publish_result, comment_conf, score_sort_conf, appeal_conf, score_view, confirm_result, performance_type, score_conf, finish_value_audit, dead_line_conf) VALUES ('1544903', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '2023年2月周直属上级显示考核', null, null, null, null, null, null, null, null, null, null, null, 'published', 'false', '1090071', '2023-02-21 19:16:45', '1090071', '2023-08-22 19:45:25', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, '', null, null, null, 3, null, 'false', 0, '', 0, 1141001, 0, 11, 9, 5, 0, '{"auditNodes":[{"approvalOrder":1,"approverInfo":"","approverName":"被考核人","approverType":"taskEmp","modifyFlag":"true","multiType":"or","raters":[],"transferFlag":"true"},{"approvalOrder":2,"approverInfo":"1","approverName":"直属主管","approverType":"manager","modifyFlag":"true","multiType":"or","raters":[],"transferFlag":"true"}],"confirmLTDay":0,"modifyAuditFlag":"true","modifyItemDimension":"all","noChangeSkipFlag":"false","open":1,"openConfirmLT":0}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1","approverType":"manager","multiType":"or","raters":[],"transferFlag":"true"}],"auditOpen":1,"changeUsers":["admin","taskEmp"],"open":1}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090071","approverName":"杨浩","approverType":"user","raters":[]}],"commentReq":0,"mergeConf":1,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"notPublic":false,"open":0}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"exeType":0,"sameTime":false,"sortItems":[{"name":"自评","sort":1,"type":10},{"name":"同级互评","sort":2,"type":20},{"name":"下级互评","sort":3,"type":30},{"name":"上级评分","sort":4,"type":40},{"name":"指定评","sort":5,"type":50}]}', '{"appealReceiver":[{"objItems":[],"objType":"taskAdmin"}],"canAppealDay":10,"open":0,"resultAppealNode":70}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auto":0,"dimension":3,"open":0,"sign":0}', 1, '{"multiType":"or","transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', null, null);
INSERT INTO perf_evaluate_task_base (id, company_id, task_name, templ_base_id, templ_name, cycle_start_date, cycle_end_date, task_desc, evaluation_staff, exclude_staff, create_task_type, create_task_date_type, day, visible_type, task_status, is_deleted, created_user, created_time, updated_user, updated_time, cycle_type, templ_desc, templ_item_json, templ_initiate_json, templ_affirm_json, templ_evaluate_json, score_start_rule_type, score_start_rule_day, enter_score_method, public_type, templ_base_json, evaluate_type, public_emp_json, templ_execute_json, result_affirm, affirm_signature, templ_points_json, can_appeal, appeal_receiver, custom_full_score, enter_score_emp_type, public_dimension, public_to_emp, auto_result_affirm, auto_result_affirm_day, level_group_id, version, cycle_id, is_new_emp, total_cnt, draw_up_cnt, start_cnt, finish_cnt, confirm_task, edit_exe_indi, enter_score, audit_result, publish_result, comment_conf, score_sort_conf, appeal_conf, score_view, confirm_result, performance_type, score_conf, finish_value_audit, dead_line_conf) VALUES ('1574901', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '2023年8月跳转宜搭', null, null, '2023-08-01', '2023-08-31', null, null, null, null, null, null, null, 'published', 'false', '1090038', '2023-08-28 15:18:39', '1090038', '2023-09-01 10:22:37', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, '', null, null, null, 3, null, 'false', 0, '', 0, 1155601, 0, 3, 3, 3, 1, '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"auditNodes":[],"open":0}', '{"dimension":3,"notPublic":false,"opEmps":[],"open":1,"toEmps":[{"dept":false,"objType":"emp"},{"dept":false,"objType":"scoreEmp"}],"type":"afterFinished"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"exeType":0,"sameTime":false,"sortItems":[{"name":"自评","sort":1,"type":10},{"name":"同级互评","sort":2,"type":20},{"name":"下级互评","sort":3,"type":30},{"name":"上级评分","sort":4,"type":40},{"name":"指定评","sort":5,"type":50}]}', '{"open":0}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auto":0,"open":0,"sign":0}', 1, '{"multiType":"or","transferFlag":"true"}', null, null);


INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1022501', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1032001', '1090038', '1011037', 'scoring', null, null, null, '[{"empId":"1090038","exUserId":"21541832661165519","empName":"路飞"}]', null, null, null, 'true', null, null, null, null, null, null, null, null, '2022-06-23 09:00:48', 'false', '1090038', '2022-06-22 15:09:58', null, '2022-06-23 09:00:49', null, null, 'true', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'true', 1, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2007507","max":200000.00,"min":90.00,"scoreRuleId":"1000008","stepId":"1000080","stepName":"A","version":0},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2007508","max":90.00,"min":59.00,"scoreRuleId":"1000008","stepId":"1000081","stepName":"B","version":0},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2007509","max":59.00,"min":0.00,"scoreRuleId":"1000008","stepId":"1000082","stepName":"C","version":0}]', 0, '路飞', '运营部门', null, null, 1022101, null, null, 0, 1, 0, null, '', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1011037|', null, 2);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1125016', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1122501', '1090038', '1011037', 'scoring', null, null, null, '[{"avatar":"","empId":"1090038","empName":"路飞","exUserId":"21541832661165519"}]', null, null, null, null, null, null, null, null, null, null, null, null, '2023-02-22 16:35:20', 'false', '1093017', '2022-12-03 16:18:22', null, '2023-02-22 16:35:09', null, null, null, null, null, null, null, null, null, null, null, null, null, 0.000, null, null, null, null, null, null, null, null, null, null, null, null, 2, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2023907","max":200000.00,"min":50.00,"scoreRuleId":"2014706","stepId":"1000480","stepName":"D"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2023908","max":50.00,"min":5.00,"scoreRuleId":"2014706","stepId":"2014636","stepName":"优秀"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2023909","max":5.00,"min":0.00,"scoreRuleId":"2014706","stepId":"2014637","stepName":"良好"}]', 0, '路飞', '运营部门', null, null, 1119401, null, null, 200, 0, 0, 1, '', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1011037|', null, 2);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1138433', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1534610', '1090038', '1011037', 'confirming', null, null, null, '[{"avatar":"","empId":"1090038","empName":"路飞","exUserId":"21541832661165519"}]', null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1093001', '2022-12-30 15:06:58', null, '2022-12-30 15:07:29', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 3, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2023907","max":200000.00,"min":50.00,"scoreRuleId":"2014706","stepId":"1000480","stepName":"D"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2023908","max":50.00,"min":5.00,"scoreRuleId":"2014706","stepId":"2014636","stepName":"优秀"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2023909","max":5.00,"min":0.00,"scoreRuleId":"2014706","stepId":"2014637","stepName":"良好"}]', 0, '路飞', '运营部门', null, null, 1134603, null, null, 200, 0, 0, 1, '', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1011037|', null, 2);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1151520', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1544903', '1090038', '1011037', 'confirming', null, null, null, '[{"avatar":"","empId":"1090038","empName":"路飞","exUserId":"21541832661165519"}]', null, null, null, null, null, null, null, null, null, null, null, null, null, 'true', '1090071', '2023-02-21 19:21:36', null, '2023-02-22 14:48:53', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 2, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2027301","max":200000.00,"min":85.00,"scoreRuleId":"1000039","stepId":"1000477","stepName":"A"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2027302","max":85.00,"min":60.00,"scoreRuleId":"1000039","stepId":"1000478","stepName":"B"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2027303","max":60.00,"min":0.00,"scoreRuleId":"1000039","stepId":"1000479","stepName":"C"}]', null, '路飞', '运营部门', null, null, 1141001, null, null, 200, 0, 0, null, '', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1011037|', null, 2);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1196802', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1574901', '1207001', '1011037', 'scoring', 338.00, 338.00, 'A', '[{"avatar":"","empId":"1207001","empName":"赵六","exUserId":"16613293433714957","status":"on_the_job"}]', null, null, null, null, null, null, null, null, null, null, null, null, '2023-08-31 18:48:32', 'false', '1090038', '2023-08-31 18:35:12', null, '2023-08-31 18:38:00', null, null, null, null, null, null, null, null, null, null, null, null, null, 338.000, null, 'A', null, null, null, null, null, null, null, null, null, null, 14, 'true', 1000477, '[{"coeffType":1,"fieldJson":"[]","id":"2052401","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"0.6","place":4,"rate":-1.00,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000477","stepName":"A"},{"coeffType":1,"fieldJson":"[]","id":"2052402","max":90.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"0.1","place":4,"rate":-1.00,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000478","stepName":"B"},{"coeffType":1,"fieldJson":"[]","id":"2052403","max":70.00,"min":60.00,"minAppendEqual":0,"perfCoefficient":"1","place":4,"rate":-1.00,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000479","stepName":"C"}]', 0, '赵六', '运营部门', null, null, 1155601, null, null, 200, 0, 0, 3, '', null, null, null, null, null, null, null, '1.00', '1.00', null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1011037|', null, 2);


INSERT INTO company_msg_center (id, company_id, business_scene, emp_id, link_id, att_content, url, third_msg_id, handler_status, is_urgent, params, third_msg_flag, created_user, created_time, updated_time, is_ignore) VALUES ('00bf02fd-cf39-429e-9419-1835a2a333ee', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', 'task_all_score', '1090038', '1022501', '{"taskName":"通知00-0622","taskId":"1032001"}', 'https://kpitest.pekhr.com/#/login?toRouteName=taskDetail&taskBaseId=1032001&empId=1090038&orgId=&action=customScore', null, 'false', null, '2746ab41-bef0-4abc-bd1d-6d875023b3ed', 'true', null, '2022-06-23 09:00:49', '2022-06-23 09:00:49', null);
INSERT INTO company_msg_center (id, company_id, business_scene, emp_id, link_id, att_content, url, third_msg_id, handler_status, is_urgent, params, third_msg_flag, created_user, created_time, updated_time, is_ignore) VALUES ('2530d521-7211-46b4-90cd-d0d1f887fca5', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', 'task_submit_progress', '1090038', '1022501', '{"taskName":"通知00-0622","taskId":"1032001"}', 'https://kpitest.pekhr.com/#/login?toRouteName=taskDetail&taskBaseId=1032001&empId=1090038&action=submitProgress&timestamp=1655881810231&orgId=', 'record6897dfcee329b599d5938b0227a3578c', 'true', null, null, 'true', null, '2022-06-22 15:10:11', '2022-06-23 09:00:48', null);
INSERT INTO company_msg_center (id, company_id, business_scene, emp_id, link_id, att_content, url, third_msg_id, handler_status, is_urgent, params, third_msg_flag, created_user, created_time, updated_time, is_ignore) VALUES ('483b02d0-7272-4010-b64d-ced6b23f472d', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', 'task_all_score', '1090038', '1022501', '{"taskName":"通知00-0622","taskId":"1032001"}', 'https://kpitest.pekhr.com/#/login?toRouteName=taskDetail&taskBaseId=1032001&empId=1090038&orgId=&action=customScore', 'record26014d72bebdc03baaeb8eaf09a0071f', 'false', null, '2746ab41-bef0-4abc-bd1d-6d875023b3ed', 'true', null, '2022-06-23 09:00:49', '2022-06-23 09:00:50', null);
INSERT INTO company_msg_center (id, company_id, business_scene, emp_id, link_id, att_content, url, third_msg_id, handler_status, is_urgent, params, third_msg_flag, created_user, created_time, updated_time, is_ignore) VALUES ('8916690d-9cbd-4395-827b-40ec56eb28a4', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', 'task_all_score', '1090038', '1022501', '{"taskName":"通知00-0622","taskId":"1032001"}', 'https://kpitest.pekhr.com/#/login?toRouteName=taskDetail&taskBaseId=1032001&empId=1090038&action=customScore&timestamp=1655946005493&orgId=', null, 'false', null, null, null, null, '2022-06-23 09:00:06', '2022-06-23 09:00:06', null);
INSERT INTO company_msg_center (id, company_id, business_scene, emp_id, link_id, att_content, url, third_msg_id, handler_status, is_urgent, params, third_msg_flag, created_user, created_time, updated_time, is_ignore) VALUES ('f8363859-36b7-40f5-89be-6ea3cb2d5cd1', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', 'task_all_score', '1090038', '1022501', '{"taskName":"通知00-0622","taskId":"1032001"}', 'https://kpitest.pekhr.com/#/login?toRouteName=taskDetail&taskBaseId=1032001&empId=1090038&action=customScore&timestamp=1655946048555&orgId=', null, 'false', null, null, null, null, '2022-06-23 09:00:49', '2022-06-23 09:00:49', null);
INSERT INTO company_msg_center (id, company_id, business_scene, emp_id, link_id, att_content, url, third_msg_id, handler_status, is_urgent, params, third_msg_flag, created_user, created_time, updated_time, is_ignore) VALUES ('03c4ef46-08d8-4ef3-a8c2-68b3ac54c11c', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', 'task_wait_self_score', '1090038', '1125016', '{"taskName":"指标确认修改","taskId":"1122501"}', 'https://kpitest.pekhr.com/#/login?toRouteName=taskDetail&taskBaseId=1122501&empId=1090038&action=selfScore&timestamp=1677054920468&orgId=&taskUserId=1125016', 'record93e917fb50f950bfa2812f840a761973', 'false', null, null, 'true', null, '2023-02-22 16:35:21', '2023-02-22 16:35:21', null);
INSERT INTO company_msg_center (id, company_id, business_scene, emp_id, link_id, att_content, url, third_msg_id, handler_status, is_urgent, params, third_msg_flag, created_user, created_time, updated_time, is_ignore) VALUES ('efe51a86-48b7-4e2d-9c33-b3cdcdea7649', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', 'task_submit_progress', '1090038', '1125016', '{"taskName":"指标确认修改","taskId":"1122501"}', 'https://kpitest.pekhr.com/#/login?toRouteName=taskDetail&taskBaseId=1122501&empId=1090038&action=submitProgress&timestamp=1677054910023&orgId=&taskUserId=1125016', 'record93e917fb50f950bffb2e74a7adc36693', 'false', null, null, 'true', null, '2023-02-22 16:35:10', '2023-02-22 16:35:10', null);
INSERT INTO company_msg_center (id, company_id, business_scene, emp_id, link_id, att_content, url, third_msg_id, handler_status, is_urgent, params, third_msg_flag, created_user, created_time, updated_time, is_ignore) VALUES ('83b63f74-5ad2-44e2-8578-74bfecc009ff', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', 'task_submit_progress', '1090038', '1138433', '{"taskName":"2022年9月周期研发2部考核","taskId":"1534610"}', 'https://kpitest.pekhr.com/#/login?toRouteName=taskDetail&taskBaseId=1534610&empId=1090038&action=submitProgress&timestamp=1673581507437&orgId=&taskUserId=1138433', 'recordece100f99eac8d9c0da2cc8313487d05', 'true', null, null, 'true', null, '2023-01-13 11:45:08', '2023-01-13 11:45:40', null);
INSERT INTO company_msg_center (id, company_id, business_scene, emp_id, link_id, att_content, url, third_msg_id, handler_status, is_urgent, params, third_msg_flag, created_user, created_time, updated_time, is_ignore) VALUES ('851ce611-5ef5-436a-87f6-e992f36296ea', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', 'task_confirm', '1090038', '1138433', '{"taskName":"2022年9月周期研发2部考核","taskId":"1534610"}', 'https://kpitest.pekhr.com/#/login?toRouteName=taskDetail&taskBaseId=1534610&empId=1090038&action=&timestamp=1673581540592&orgId=&taskUserId=1138433', 'recordb5512b4706f6646b71476ec33939c51b', 'false', null, null, 'true', null, '2023-01-13 11:45:41', '2023-01-13 11:45:41', null);
INSERT INTO company_msg_center (id, company_id, business_scene, emp_id, link_id, att_content, url, third_msg_id, handler_status, is_urgent, params, third_msg_flag, created_user, created_time, updated_time, is_ignore) VALUES ('90b17ea4-12dd-4b18-8e45-8ceeff0f1935', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', 'task_confirm', '1090038', '1138433', '{"taskName":"2022年9月周期研发部考核","taskId":"1534610"}', 'http://kpitest.pekhr.com:8084/#/login?toRouteName=taskDetail&taskBaseId=1534610&empId=1090038&action=&timestamp=1672384049580&orgId=', 'recordff5ac742462ad13e39ad0c8b24b0595c', 'true', null, null, 'true', null, '2022-12-30 15:07:30', '2023-01-13 11:45:08', null);
INSERT INTO company_msg_center (id, company_id, business_scene, emp_id, link_id, att_content, url, third_msg_id, handler_status, is_urgent, params, third_msg_flag, created_user, created_time, updated_time, is_ignore) VALUES ('41fa1398-61d1-4b3a-a5fa-170c426700f0', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', 'task_confirm', '1090038', '1151520', '{"taskName":"2023年2月周直属上级显示考核","taskId":"1544903"}', 'https://kpitest.pekhr.com/#/login?toRouteName=taskDetail&taskBaseId=1544903&empId=1090038&action=&timestamp=1676980508074&orgId=&taskUserId=1151520', 'recordaa928ef670f5852c8ccd79595b0fb575', 'false', null, null, 'true', null, '2023-02-21 19:55:08', '2023-02-21 19:55:08', null);
INSERT INTO company_msg_center (id, company_id, business_scene, emp_id, link_id, att_content, url, third_msg_id, handler_status, is_urgent, params, third_msg_flag, created_user, created_time, updated_time, is_ignore) VALUES ('b461a15d-0ce3-4ba6-bc67-48ca4796d2f0', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', 'task_confirm', '1090038', '1151520', '{"taskName":"2023年2月周直属上级显示考核","taskId":"1544903"}', 'https://kpitest.pekhr.com/#/login?toRouteName=taskDetail&taskBaseId=1544903&empId=1090038&action=&timestamp=1676978510273&orgId=&taskUserId=1151520', 'recordaa928ef670f5852c7738e4ffbec63285', 'true', null, null, 'true', null, '2023-02-21 19:21:51', '2023-02-21 19:55:08', null);
INSERT INTO company_msg_center (id, company_id, business_scene, emp_id, link_id, att_content, url, third_msg_id, handler_status, is_urgent, params, third_msg_flag, created_user, created_time, updated_time, is_ignore) VALUES ('eee4502a-a920-4800-b496-9616613180a2', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', 'task_submit_progress', '1090038', '1196802', '{"taskName":"2023年8月跳转宜搭","taskId":"1574901"}', 'http://kpitest.pekhr.com:8084/#/login?toRouteName=taskDetail&taskBaseId=1574901&empId=1207001&action=submitProgress&timestamp=1693533393308&orgId=&talentEvalId=1196802', 'recordf224283a59580505393a4ce7ef28d8bd', 'false', null, null, 'true', null, '2023-09-01 09:56:34', '2023-09-01 09:56:34', null);

