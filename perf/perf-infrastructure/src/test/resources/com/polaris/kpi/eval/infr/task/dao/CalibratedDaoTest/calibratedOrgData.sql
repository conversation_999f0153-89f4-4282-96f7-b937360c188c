INSERT INTO perf_evaluate_cycle (id, company_id, name, year, type, value, cycle_start, cycle_end, eval_cnt, from_task_id, created_user, updated_user, created_time, updated_time, version, cycle_status, is_new_cycle) VALUES (1156703, 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '2023年3月', 2023, 'month', 3, '2023-03-01', '2023-03-31', 30, '0', '1093014', '1093014', '2023-12-01 19:42:55', '2023-12-16 09:23:13', 0, 'normal', 1);
INSERT INTO perf_evaluate_task_base (id, company_id, task_name, templ_base_id, templ_name, cycle_start_date, cycle_end_date, task_desc, evaluation_staff, exclude_staff, create_task_type, create_task_date_type, day, visible_type, task_status, is_deleted, created_user, created_time, updated_user, updated_time, cycle_type, templ_desc, templ_item_json, templ_initiate_json, templ_affirm_json, templ_evaluate_json, score_start_rule_type, score_start_rule_day, enter_score_method, public_type, templ_base_json, evaluate_type, public_emp_json, templ_execute_json, result_affirm, affirm_signature, templ_points_json, can_appeal, appeal_receiver, custom_full_score, enter_score_emp_type, public_dimension, public_to_emp, auto_result_affirm, auto_result_affirm_day, level_group_id, version, cycle_id, is_new_emp, total_cnt, draw_up_cnt, start_cnt, finish_cnt, confirm_task, edit_exe_indi, enter_score, audit_result, publish_result, comment_conf, score_sort_conf, appeal_conf, score_view, confirm_result, performance_type, score_conf, finish_value_audit, dead_line_conf) VALUES ('1584301', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '2023年3月-等级分布-lufei', null, null, '2023-03-01', '2023-03-31', null, null, null, null, null, null, null, 'published', 'false', '1090038', '2023-12-16 09:22:46', '1090038', '2023-12-16 09:24:35', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, '', null, null, null, 3, null, 'false', 0, '', 0, 1156703, 0, 28, 25, 0, 0, '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","raters":[]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","raters":[]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"notPublic":false,"open":0}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"exeType":0,"sameTime":false,"sortItems":[{"name":"自评","sort":1,"type":10},{"name":"同级评","sort":2,"type":20},{"name":"下级评","sort":3,"type":30},{"name":"上级评分","sort":4,"type":40},{"name":"指定评","sort":5,"type":50}]}', '{"open":0}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auto":0,"open":0,"sign":0}', 1, '{"multiType":"or","transferFlag":"true"}', '{"auditNodes":[],"open":0}', '{"open":0}');


INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208601', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1090038', '1011037', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:24:30', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 2, null, null, '[{"coeffType":1,"fieldJson":"[]","id":"2052401","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"0.6","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000477","stepName":"A"},{"coeffType":1,"fieldJson":"[]","id":"2052402","max":90.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"0.1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000478","stepName":"B"},{"coeffType":1,"fieldJson":"[]","id":"2052403","max":70.00,"min":60.00,"minAppendEqual":0,"perfCoefficient":"1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000479","stepName":"C"}]', 0, '路飞', '运营部门', null, null, 1156703, null, null, 200, 0, 0, null, '', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1011037|', null, 2);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208602', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1090069', '1011037', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:24:31', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 2, null, null, '[{"coeffType":1,"id":"2052409","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"2","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000477","stepName":"A"},{"coeffType":1,"id":"2052410","max":90.00,"min":80.00,"minAppendEqual":0,"perfCoefficient":"1.8","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000478","stepName":"B"},{"coeffType":1,"id":"2052411","max":80.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"1.6","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000479","stepName":"C"},{"coeffType":1,"id":"2052412","max":70.00,"min":0.00,"minAppendEqual":0,"perfCoefficient":"1.4","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000480","stepName":"D"}]', 0, 'Sasha', '运营部门', null, null, 1156703, null, null, 200, 0, 0, null, 'https://static-legacy.dingtalk.com/media/lADPM5HikxN4fzvNA6PNA-4_1006_931.jpg', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1011037|', null, 2);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208603', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1207001', '1011037', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:24:31', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 2, null, null, '[{"coeffType":1,"fieldJson":"[]","id":"2052401","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"0.6","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000477","stepName":"A"},{"coeffType":1,"fieldJson":"[]","id":"2052402","max":90.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"0.1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000478","stepName":"B"},{"coeffType":1,"fieldJson":"[]","id":"2052403","max":70.00,"min":60.00,"minAppendEqual":0,"perfCoefficient":"1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000479","stepName":"C"}]', 0, '赵六', '运营部门', null, null, 1156703, null, null, 200, 0, 0, null, '', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1011037|', null, 2);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208604', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1267001', '1011037', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:24:31', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 2, null, null, '[{"coeffType":1,"fieldJson":"[]","id":"2052401","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"0.6","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000477","stepName":"A"},{"coeffType":1,"fieldJson":"[]","id":"2052402","max":90.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"0.1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000478","stepName":"B"},{"coeffType":1,"fieldJson":"[]","id":"2052403","max":70.00,"min":60.00,"minAppendEqual":0,"perfCoefficient":"1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000479","stepName":"C"}]', 0, '小杨-02', '运营部门', null, null, 1156703, null, null, 200, 0, 0, null, '', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1011037|', null, 2);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208605', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1093014', '1015004', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:24:31', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 2, null, null, '[{"coeffType":1,"id":"2052409","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"2","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000477","stepName":"A"},{"coeffType":1,"id":"2052410","max":90.00,"min":80.00,"minAppendEqual":0,"perfCoefficient":"1.8","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000478","stepName":"B"},{"coeffType":1,"id":"2052411","max":80.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"1.6","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000479","stepName":"C"},{"coeffType":1,"id":"2052412","max":70.00,"min":0.00,"minAppendEqual":0,"perfCoefficient":"1.4","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000480","stepName":"D"}]', 0, '思威', '行政一步', null, null, 1156703, null, null, 200, 0, 0, null, 'https://static-legacy.dingtalk.com/media/lADPM5HikoKm7UnNAWPNAUQ_324_355.jpg', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1011041|1015004|', null, 3);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208606', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1256044', '1017301', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:24:32', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 2, null, null, '[{"coeffType":1,"fieldJson":"[]","id":"2052413","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"1.5","place":4,"scoreRuleId":"2039906","scoreRuleName":"添加系数","stepId":"1000477","stepName":"A"},{"coeffType":1,"fieldJson":"[]","id":"2052414","max":90.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"1","place":4,"scoreRuleId":"2039906","scoreRuleName":"添加系数","stepId":"1000478","stepName":"B"},{"coeffType":1,"fieldJson":"[]","id":"2052415","max":70.00,"min":60.00,"minAppendEqual":0,"perfCoefficient":"0.8","place":4,"scoreRuleId":"2039906","scoreRuleName":"添加系数","stepId":"1000479","stepName":"C"},{"coeffType":1,"fieldJson":"[]","id":"2052416","max":60.00,"min":0.00,"minAppendEqual":0,"perfCoefficient":"0.5","place":4,"scoreRuleId":"2039906","scoreRuleName":"添加系数","stepId":"1000480","stepName":"D"}]', 0, '吴武', '行政一部门', null, null, 1156703, null, null, 200, 0, 0, null, '', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1011041|1015004|1017301|', null, 4);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208607', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1090038', '1015502', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:24:32', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 2, null, null, '[{"coeffType":1,"fieldJson":"[]","id":"2052401","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"0.6","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000477","stepName":"A"},{"coeffType":1,"fieldJson":"[]","id":"2052402","max":90.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"0.1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000478","stepName":"B"},{"coeffType":1,"fieldJson":"[]","id":"2052403","max":70.00,"min":60.00,"minAppendEqual":0,"perfCoefficient":"1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000479","stepName":"C"}]', 0, '路飞', '三级部门', null, null, 1156703, null, null, 200, 0, 0, null, '', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1011602|1015502|', null, 3);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208608', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1090071', '1017608', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:24:32', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 2, null, null, '[{"coeffType":1,"fieldJson":"[]","id":"2052401","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"0.6","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000477","stepName":"A"},{"coeffType":1,"fieldJson":"[]","id":"2052402","max":90.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"0.1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000478","stepName":"B"},{"coeffType":1,"fieldJson":"[]","id":"2052403","max":70.00,"min":60.00,"minAppendEqual":0,"perfCoefficient":"1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000479","stepName":"C"}]', 0, '杨浩', '四级部门', null, null, 1156703, null, null, 200, 0, 0, null, 'https://static-legacy.dingtalk.com/media/lADPDg7mQqMlUJPNArzNArw_700_700.jpg', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1011602|1015502|1017608|', null, 4);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208609', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1093009', '1011040', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:23:10', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, null, null, '[{"coeffType":1,"fieldJson":"[]","id":"2052401","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"0.6","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000477","stepName":"A"},{"coeffType":1,"fieldJson":"[]","id":"2052402","max":90.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"0.1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000478","stepName":"B"},{"coeffType":1,"fieldJson":"[]","id":"2052403","max":70.00,"min":60.00,"minAppendEqual":0,"perfCoefficient":"1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000479","stepName":"C"}]', 0, '北极星支持', '人事部', null, null, 1156703, null, null, 200, 0, 0, null, 'https://static-legacy.dingtalk.com/media/lQLPDhvoM-EeHRfNAyDNAyCwDVH5NBf4Q64DTR0w4AAdAA_800_800.png', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1011602|1011040|', null, 3);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208610', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1207002', '1011602', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:24:33', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 2, null, null, '[{"coeffType":1,"fieldJson":"[]","id":"2052401","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"0.6","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000477","stepName":"A"},{"coeffType":1,"fieldJson":"[]","id":"2052402","max":90.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"0.1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000478","stepName":"B"},{"coeffType":1,"fieldJson":"[]","id":"2052403","max":70.00,"min":60.00,"minAppendEqual":0,"perfCoefficient":"1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000479","stepName":"C"}]', 0, '王丽', '新人入职', null, null, 1156703, null, null, 200, 0, 0, null, '', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1011602|', null, 2);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208611', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1208001', '1011602', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:24:33', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, null, null, '[{"coeffType":1,"fieldJson":"[]","id":"2052401","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"0.6","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000477","stepName":"A"},{"coeffType":1,"fieldJson":"[]","id":"2052402","max":90.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"0.1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000478","stepName":"B"},{"coeffType":1,"fieldJson":"[]","id":"2052403","max":70.00,"min":60.00,"minAppendEqual":0,"perfCoefficient":"1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000479","stepName":"C"}]', 0, '赵王', '新人入职', null, null, 1156703, null, null, 200, 0, 0, null, '', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1011602|', null, 2);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208612', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1264001', '1011040', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:24:33', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 2, null, null, '[{"coeffType":1,"fieldJson":"[]","id":"2052401","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"0.6","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000477","stepName":"A"},{"coeffType":1,"fieldJson":"[]","id":"2052402","max":90.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"0.1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000478","stepName":"B"},{"coeffType":1,"fieldJson":"[]","id":"2052403","max":70.00,"min":60.00,"minAppendEqual":0,"perfCoefficient":"1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000479","stepName":"C"}]', 0, '测试号', '人事部', null, null, 1156703, null, null, 200, 0, 0, null, '', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1011602|1011040|', null, 3);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208613', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1265001', '1011040', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:24:33', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, null, null, '[{"coeffType":1,"fieldJson":"[]","id":"2052401","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"0.6","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000477","stepName":"A"},{"coeffType":1,"fieldJson":"[]","id":"2052402","max":90.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"0.1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000478","stepName":"B"},{"coeffType":1,"fieldJson":"[]","id":"2052403","max":70.00,"min":60.00,"minAppendEqual":0,"perfCoefficient":"1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000479","stepName":"C"}]', 0, '章鱼', '人事部', null, null, 1156703, null, null, 200, 0, 0, null, '', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1011602|1011040|', null, 3);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208614', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1265002', '1011040', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:24:34', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 2, null, null, '[{"coeffType":1,"fieldJson":"[]","id":"2052401","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"0.6","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000477","stepName":"A"},{"coeffType":1,"fieldJson":"[]","id":"2052402","max":90.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"0.1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000478","stepName":"B"},{"coeffType":1,"fieldJson":"[]","id":"2052403","max":70.00,"min":60.00,"minAppendEqual":0,"perfCoefficient":"1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000479","stepName":"C"}]', 0, '章鱼', '人事部', null, null, 1156703, null, null, 200, 0, 0, null, 'https://static-legacy.dingtalk.com/media/lQDPDhunyPGMnB_NAzzNAzywzNUD8a8wSlQC45JxCUB5AA_828_828.jpg', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1011602|1011040|', null, 3);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208615', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1090069', '1013211', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:24:34', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 2, null, null, '[{"coeffType":1,"id":"2052409","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"2","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000477","stepName":"A"},{"coeffType":1,"id":"2052410","max":90.00,"min":80.00,"minAppendEqual":0,"perfCoefficient":"1.8","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000478","stepName":"B"},{"coeffType":1,"id":"2052411","max":80.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"1.6","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000479","stepName":"C"},{"coeffType":1,"id":"2052412","max":70.00,"min":0.00,"minAppendEqual":0,"perfCoefficient":"1.4","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000480","stepName":"D"}]', 0, 'Sasha', '融云北极星的合作伙伴', null, null, 1156703, null, null, 200, 0, 0, null, 'https://static-legacy.dingtalk.com/media/lADPM5HikxN4fzvNA6PNA-4_1006_931.jpg', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1013211|', null, 2);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208616', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1090071', '1013211', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:24:34', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 2, null, null, '[{"coeffType":1,"fieldJson":"[]","id":"2052401","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"0.6","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000477","stepName":"A"},{"coeffType":1,"fieldJson":"[]","id":"2052402","max":90.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"0.1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000478","stepName":"B"},{"coeffType":1,"fieldJson":"[]","id":"2052403","max":70.00,"min":60.00,"minAppendEqual":0,"perfCoefficient":"1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000479","stepName":"C"}]', 0, '杨浩', '融云北极星的合作伙伴', null, null, 1156703, null, null, 200, 0, 0, null, 'https://static-legacy.dingtalk.com/media/lADPDg7mQqMlUJPNArzNArw_700_700.jpg', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1013211|', null, 2);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208617', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1093014', '1013211', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:24:34', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 2, null, null, '[{"coeffType":1,"id":"2052409","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"2","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000477","stepName":"A"},{"coeffType":1,"id":"2052410","max":90.00,"min":80.00,"minAppendEqual":0,"perfCoefficient":"1.8","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000478","stepName":"B"},{"coeffType":1,"id":"2052411","max":80.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"1.6","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000479","stepName":"C"},{"coeffType":1,"id":"2052412","max":70.00,"min":0.00,"minAppendEqual":0,"perfCoefficient":"1.4","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000480","stepName":"D"}]', 0, '思威', '融云北极星的合作伙伴', null, null, 1156703, null, null, 200, 0, 0, null, 'https://static-legacy.dingtalk.com/media/lADPM5HikoKm7UnNAWPNAUQ_324_355.jpg', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1013211|', null, 2);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208618', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1263001', '1013212', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:24:35', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, null, null, '[{"coeffType":1,"fieldJson":"[]","id":"2052401","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"0.6","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000477","stepName":"A"},{"coeffType":1,"fieldJson":"[]","id":"2052402","max":90.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"0.1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000478","stepName":"B"},{"coeffType":1,"fieldJson":"[]","id":"2052403","max":70.00,"min":60.00,"minAppendEqual":0,"perfCoefficient":"1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000479","stepName":"C"}]', 0, '李梦思', '部门', null, null, 1156703, null, null, 200, 0, 0, null, 'https://static-legacy.dingtalk.com/media/lQDPM4rim5kl9TjNAYDNAg6wZIPOQcdOczQDnQJsQAB3AA_526_384.jpg', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1013211|1013212|', null, 3);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208619', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1090069', '1018301', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:24:35', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 2, null, null, '[{"coeffType":1,"id":"2052409","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"2","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000477","stepName":"A"},{"coeffType":1,"id":"2052410","max":90.00,"min":80.00,"minAppendEqual":0,"perfCoefficient":"1.8","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000478","stepName":"B"},{"coeffType":1,"id":"2052411","max":80.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"1.6","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000479","stepName":"C"},{"coeffType":1,"id":"2052412","max":70.00,"min":0.00,"minAppendEqual":0,"perfCoefficient":"1.4","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000480","stepName":"D"}]', 0, 'Sasha', '设计部', null, null, 1156703, null, null, 200, 0, 0, null, 'https://static-legacy.dingtalk.com/media/lADPM5HikxN4fzvNA6PNA-4_1006_931.jpg', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1018301|', null, 2);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208620', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1093003', '1018302', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:24:35', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 2, null, null, '[{"coeffType":1,"fieldJson":"[{\\"formulaFieldValue\\":0,\\"formulaFieldName\\":\\"考核任务得分\\",\\"companyFieldId\\":\\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\\",\\"isSystemField\\":\\"false\\",\\"label\\":\\"考核任务得分\\",\\"value\\":0,\\"yVal\\":true},{\\"label\\":\\"×\\",\\"value\\":\\"*\\",\\"symbol\\":true},{\\"label\\":0,\\"value\\":0},{\\"label\\":\\".\\",\\"value\\":\\".\\"},{\\"label\\":0,\\"value\\":0},{\\"label\\":1,\\"value\\":1}]","id":"2053908","max":200000.00,"min":0.00,"minAppendEqual":0,"perfCoefficient":"1","place":2,"scoreRuleId":"2053904","scoreRuleName":"按分数排名","stepId":"1000477","stepName":"A"},{"coeffType":1,"fieldJson":"[{\\"formulaFieldValue\\":0,\\"formulaFieldName\\":\\"考核任务得分\\",\\"companyFieldId\\":\\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\\",\\"isSystemField\\":\\"false\\",\\"label\\":\\"考核任务得分\\",\\"value\\":0,\\"yVal\\":true},{\\"label\\":\\"×\\",\\"value\\":\\"*\\",\\"symbol\\":true},{\\"label\\":0,\\"value\\":0},{\\"label\\":\\".\\",\\"value\\":\\".\\"},{\\"label\\":0,\\"value\\":0},{\\"label\\":1,\\"value\\":1}]","id":"2053909","max":0.00,"min":0.00,"minAppendEqual":0,"perfCoefficient":"0.6","place":2,"scoreRuleId":"2053904","scoreRuleName":"按分数排名","stepId":"2029601","stepName":"通过"},{"coeffType":1,"fieldJson":"[{\\"formulaFieldValue\\":0,\\"formulaFieldName\\":\\"考核任务得分\\",\\"companyFieldId\\":\\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\\",\\"isSystemField\\":\\"false\\",\\"label\\":\\"考核任务得分\\",\\"value\\":0,\\"yVal\\":true},{\\"label\\":\\"×\\",\\"value\\":\\"*\\",\\"symbol\\":true},{\\"label\\":0,\\"value\\":0},{\\"label\\":\\".\\",\\"value\\":\\".\\"},{\\"label\\":0,\\"value\\":0},{\\"label\\":1,\\"value\\":1}]","id":"2053910","max":0.00,"min":0.00,"minAppendEqual":0,"perfCoefficient":"0.2","place":2,"scoreRuleId":"2053904","scoreRuleName":"按分数排名","stepId":"1000478","stepName":"B"}]', 0, 'Mr. wei', '套娃组', null, null, 1156703, null, null, 200, 0, 0, null, 'https://static-legacy.dingtalk.com/media/lADPDhYBP7KmaXrMoMyg_160_160.jpg', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1018301|1018302|', null, 3);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208621', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1093014', '1018302', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:23:12', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, null, null, '[{"coeffType":1,"id":"2052409","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"2","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000477","stepName":"A"},{"coeffType":1,"id":"2052410","max":90.00,"min":80.00,"minAppendEqual":0,"perfCoefficient":"1.8","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000478","stepName":"B"},{"coeffType":1,"id":"2052411","max":80.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"1.6","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000479","stepName":"C"},{"coeffType":1,"id":"2052412","max":70.00,"min":0.00,"minAppendEqual":0,"perfCoefficient":"1.4","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000480","stepName":"D"}]', 0, '思威', '套娃组', null, null, 1156703, null, null, 200, 0, 0, null, 'https://static-legacy.dingtalk.com/media/lADPM5HikoKm7UnNAWPNAUQ_324_355.jpg', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1018301|1018302|', null, 3);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208622', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1093014', '1018303', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:23:12', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, null, null, '[{"coeffType":1,"id":"2052409","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"2","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000477","stepName":"A"},{"coeffType":1,"id":"2052410","max":90.00,"min":80.00,"minAppendEqual":0,"perfCoefficient":"1.8","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000478","stepName":"B"},{"coeffType":1,"id":"2052411","max":80.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"1.6","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000479","stepName":"C"},{"coeffType":1,"id":"2052412","max":70.00,"min":0.00,"minAppendEqual":0,"perfCoefficient":"1.4","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000480","stepName":"D"}]', 0, '思威', '没主管', null, null, 1156703, null, null, 200, 0, 0, null, 'https://static-legacy.dingtalk.com/media/lADPM5HikoKm7UnNAWPNAUQ_324_355.jpg', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1018301|1018302|1018303|', null, 4);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208623', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1109001', '1018301', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:23:12', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, '[{"coeffType":1,"id":"2052409","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"2","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000477","stepName":"A"},{"coeffType":1,"id":"2052410","max":90.00,"min":80.00,"minAppendEqual":0,"perfCoefficient":"1.8","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000478","stepName":"B"},{"coeffType":1,"id":"2052411","max":80.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"1.6","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000479","stepName":"C"},{"coeffType":1,"id":"2052412","max":70.00,"min":0.00,"minAppendEqual":0,"perfCoefficient":"1.4","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000480","stepName":"D"}]', 0, '小杨', '设计部', null, null, 1156703, null, null, 0, 0, 0, null, '', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1018301|', null, 2);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208624', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1216003', '1018304', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:23:13', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, null, null, '[{"coeffType":1,"id":"2052409","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"2","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000477","stepName":"A"},{"coeffType":1,"id":"2052410","max":90.00,"min":80.00,"minAppendEqual":0,"perfCoefficient":"1.8","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000478","stepName":"B"},{"coeffType":1,"id":"2052411","max":80.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"1.6","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000479","stepName":"C"},{"coeffType":1,"id":"2052412","max":70.00,"min":0.00,"minAppendEqual":0,"perfCoefficient":"1.4","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000480","stepName":"D"}]', 0, '李运营REP', 'A测试部门', null, null, 1156703, null, null, 200, 0, 0, null, 'https://static-legacy.dingtalk.com/media/lQLPDhtwSlDwYpfNAmDNAmCwP2waI3J1qnYCiKZNDcDTAA_608_608.png', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1018301|1018302|1018303|1018304|', null, 5);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208625', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1233038', '1018301', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:23:12', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, '[{"coeffType":1,"id":"2052409","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"2","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000477","stepName":"A"},{"coeffType":1,"id":"2052410","max":90.00,"min":80.00,"minAppendEqual":0,"perfCoefficient":"1.8","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000478","stepName":"B"},{"coeffType":1,"id":"2052411","max":80.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"1.6","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000479","stepName":"C"},{"coeffType":1,"id":"2052412","max":70.00,"min":0.00,"minAppendEqual":0,"perfCoefficient":"1.4","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000480","stepName":"D"}]', 0, '吴箫', '设计部', null, null, 1156703, null, null, 0, 0, 0, null, 'https://static-legacy.dingtalk.com/media/lADPDgQ9rZ6XW3HNAkPNAeo_490_579.jpg', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1018301|', null, 2);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208626', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1276001', '1018301', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:23:12', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, '[{"coeffType":1,"id":"2052409","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"2","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000477","stepName":"A"},{"coeffType":1,"id":"2052410","max":90.00,"min":80.00,"minAppendEqual":0,"perfCoefficient":"1.8","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000478","stepName":"B"},{"coeffType":1,"id":"2052411","max":80.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"1.6","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000479","stepName":"C"},{"coeffType":1,"id":"2052412","max":70.00,"min":0.00,"minAppendEqual":0,"perfCoefficient":"1.4","place":4,"scoreRuleId":"2039212","scoreRuleName":"绩效系数","stepId":"1000480","stepName":"D"}]', 0, '刘莉莉', '设计部', null, null, 1156703, null, null, 0, 0, 0, null, '', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1018301|', null, 2);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208627', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1093001', '1019701', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:23:13', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, null, null, '[{"coeffType":1,"fieldJson":"[]","id":"2052401","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"0.6","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000477","stepName":"A"},{"coeffType":1,"fieldJson":"[]","id":"2052402","max":90.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"0.1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000478","stepName":"B"},{"coeffType":1,"fieldJson":"[]","id":"2052403","max":70.00,"min":60.00,"minAppendEqual":0,"perfCoefficient":"1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000479","stepName":"C"}]', 0, '静雯', '销售部', null, null, 1156703, null, null, 200, 0, 0, null, 'https://static-legacy.dingtalk.com/media/lQDPDhs8hjOHypfNAvvNAm2wNrB_t36oonMCM9YJSUCJAA_621_763.jpg', null, null, null, null, null, null, null, null, null, null, '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1019701|', null, 2);
INSERT INTO  perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id, eval_org_name, eval_org_id, score_end_time, weight_of_ref, score_of_ref, perf_coefficient, original_perf_coefficient, rule_conf_error, at_org_code_path, at_org_name_path, at_org_path_hight) VALUES ('1208628', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1584301', '1234001', '1019701', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2023-12-16 09:23:08', null, '2023-12-16 09:23:13', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, null, null, '[{"coeffType":1,"fieldJson":"[]","id":"2052401","max":200000.00,"min":90.00,"minAppendEqual":0,"perfCoefficient":"0.6","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000477","stepName":"A"},{"coeffType":1,"fieldJson":"[]","id":"2052402","max":90.00,"min":70.00,"minAppendEqual":0,"perfCoefficient":"0.1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000478","stepName":"B"},{"coeffType":1,"fieldJson":"[]","id":"2052403","max":70.00,"min":60.00,"minAppendEqual":0,"perfCoefficient":"1","place":4,"scoreRuleId":"1000039","scoreRuleName":"系统默认","stepId":"1000479","stepName":"C"}]', 0, '吴销售', '销售部', null, null, 1156703, null, null, 200, 0, 0, null, 'https://static-legacy.dingtalk.com/media/lQLPM4lbhmGLs3bNAyDNAyCweHBNK7HmJC4EQgEmm8DNAA_800_800.png', null, null, null, null, null, null, null, null, null, '404', '|f223b509-fbda-4ccc-97bc-aeb7f115723f|1019701|', null, 2);

INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208601', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '等级组', 'custom', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0,"customFullScore":100,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"nodeWeight":100,"open":1}', null, '{"auditNodes":[],"multiType":"and","open":0,"superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[{"node":"self","raters":[{"empId":"","type":5}]},{"node":"super","raters":[{"empId":"1090038","empName":"路飞","type":0},{"empId":"1090071","empName":"杨浩","type":0}]}]', 2, 'true', '1090038', '2023-12-16 09:23:09', '1090038', '2023-12-16 09:24:30', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 2, '[{"empId":"1090071","empName":"杨浩","type":0}]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208602', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '维度设置评价流程', 'custom', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[],"multiType":"and","open":0,"superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[{"node":"self","raters":[{"empId":"","type":5}]},{"node":"super","raters":[{"empId":"1090069","empName":"Sasha","type":0}]}]', 4, 'true', '1090038', '2023-12-16 09:23:09', '1090038', '2023-12-16 09:24:30', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"approverType":"role","node":"peer_score","nodeWeight":100,"open":1,"raters":[{"empId":"1233038","empName":"吴箫","roleId":"6c9242e3-0012-44f7-96b2-32865660c0f5","roleName":"研发岗位","type":3},{"empId":"1234001","empName":"吴销售","roleId":"6c9242e3-0012-44f7-96b2-32865660c0f5","roleName":"研发岗位","type":3},{"empId":"1280001","empName":"苏华","roleId":"6c9242e3-0012-44f7-96b2-32865660c0f5","roleName":"研发岗位","type":3}],"roleAudit":true}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208603', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '011', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"anonymous":"false","nodeWeight":100,"open":1,"rateMode":"item"}', null, '{"auditNodes":[],"multiType":"and","open":0,"superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[{"node":"self","raters":[{"empId":"","type":5}]}]', 2, 'true', '1090038', '2023-12-16 09:23:09', '1090038', '2023-12-16 09:24:31', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, null, null, null, null, '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208604', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '复制', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0,"customFullScore":100,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[{"approvalOrder":1,"approverType":"user","multiType":"or","node":"superior_score","raters":[{"empId":"1093014","empName":"思威","type":0}],"scoreWeight":100,"transferFlag":"true","weight":100}],"multiType":"or","nodeWeight":100,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[{"node":"super","raters":[{"empId":"1093014","empName":"思威","type":0}]}]', 2, 'true', '1090038', '2023-12-16 09:23:10', '1090038', '2023-12-16 09:24:31', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208605', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '等级改维度', 'custom', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[],"multiType":"and","open":0,"superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[{"node":"self","raters":[{"empId":"","type":5}]},{"node":"super","raters":[{"empId":"1090069","empName":"Sasha","level":1,"type":1}]}]', 2, 'true', '1090038', '2023-12-16 09:23:10', '1090038', '2023-12-16 09:24:31', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208606', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '自定义考核表', 'custom', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[],"multiType":"and","open":0,"superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[{"node":"peer","raters":[{"avatar":"","empId":"1090038","empName":"路飞","type":0}]},{"node":"super","raters":[{"empId":"1090038","empName":"路飞","type":0}]}]', 2, 'true', '1090038', '2023-12-16 09:23:10', '1090038', '2023-12-16 09:24:31', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208607', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '等级组', 'custom', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0,"customFullScore":100,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"nodeWeight":100,"open":1}', null, '{"auditNodes":[],"multiType":"and","open":0,"superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[{"node":"self","raters":[{"empId":"","type":5}]},{"node":"super","raters":[{"empId":"1090038","empName":"路飞","type":0},{"empId":"1090071","empName":"杨浩","type":0}]}]', 2, 'true', '1090038', '2023-12-16 09:23:10', '1090038', '2023-12-16 09:24:32', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 2, '[{"empId":"1090071","empName":"杨浩","type":0}]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208608', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '指标确认卡流程', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":1,"open":0}', '{"baseScore":0.00,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1","approverType":"manager","multiType":"or","node":"superior_score","raters":[{"empId":"1093014","empName":"思威","type":0}],"scoreWeight":100.00,"transferFlag":"true","weight":100.00}],"multiType":"or","nodeWeight":100.00,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[{"node":"super","raters":[{"empId":"1093014","empName":"思威","type":0}]}]', 2, 'true', '1090038', '2023-12-16 09:23:10', '1090038', '2023-12-16 09:24:32', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', null, null, null, '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208610', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '自定义的表', 'custom', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '', null, null, '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[{"node":"self","raters":[{"empId":"","type":5}]},{"node":"super","raters":[{"empId":"1093014","empName":"思威","type":0}]}]', 1, 'true', '1090038', '2023-12-16 09:23:11', '1090038', '2023-12-16 09:24:32', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', null, null, 0, null, null, null, null, null, '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208612', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '评等级切评分', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"anonymous":"false","nodeWeight":100,"open":1,"rateMode":"item"}', null, '{"auditNodes":[],"multiType":"and","open":0,"superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[{"node":"self","raters":[{"empId":"","type":5}]}]', 2, 'true', '1090038', '2023-12-16 09:23:11', '1090038', '2023-12-16 09:24:33', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208614', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '复制', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0.00,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1","approverType":"manager","multiType":"or","node":"superior_score","raters":[{"avatar":"https://static-legacy.dingtalk.com/media/lQDPDhs8hjOHypfNAvvNAm2wNrB_t36oonMCM9YJSUCJAA_621_763.jpg","empId":"1093001","empName":"静雯","type":0}],"scoreWeight":100.00,"transferFlag":"true","weight":100.00}],"multiType":"or","nodeWeight":100.00,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[{"node":"super","raters":[{"avatar":"https://static-legacy.dingtalk.com/media/lQDPDhs8hjOHypfNAvvNAm2wNrB_t36oonMCM9YJSUCJAA_621_763.jpg","empId":"1093001","empName":"静雯","type":0}]}]', 2, 'true', '1090038', '2023-12-16 09:23:11', '1090038', '2023-12-16 09:24:33', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', null, null, null, '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208615', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '维度设置评价流程', 'custom', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[],"multiType":"and","open":0,"superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[{"node":"self","raters":[{"empId":"","type":5}]},{"node":"super","raters":[{"empId":"1090069","empName":"Sasha","type":0}]}]', 4, 'true', '1090038', '2023-12-16 09:23:11', '1090038', '2023-12-16 09:24:34', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"approverType":"role","node":"peer_score","nodeWeight":100,"open":1,"raters":[{"empId":"1233038","empName":"吴箫","roleId":"6c9242e3-0012-44f7-96b2-32865660c0f5","roleName":"研发岗位","type":3},{"empId":"1234001","empName":"吴销售","roleId":"6c9242e3-0012-44f7-96b2-32865660c0f5","roleName":"研发岗位","type":3},{"empId":"1280001","empName":"苏华","roleId":"6c9242e3-0012-44f7-96b2-32865660c0f5","roleName":"研发岗位","type":3}],"roleAudit":true}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208616', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '指标确认卡流程', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":1,"open":0}', '{"baseScore":0.00,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1","approverType":"manager","multiType":"or","node":"superior_score","raters":[{"empId":"1093014","empName":"思威","type":0}],"scoreWeight":100.00,"transferFlag":"true","weight":100.00}],"multiType":"or","nodeWeight":100.00,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[{"node":"super","raters":[{"empId":"1093014","empName":"思威","type":0}]}]', 2, 'true', '1090038', '2023-12-16 09:23:11', '1090038', '2023-12-16 09:24:34', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', null, null, null, '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208617', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '等级改维度', 'custom', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[],"multiType":"and","open":0,"superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[{"node":"self","raters":[{"empId":"","type":5}]},{"node":"super","raters":[{"empId":"1090069","empName":"Sasha","level":1,"type":1}]}]', 2, 'true', '1090038', '2023-12-16 09:23:12', '1090038', '2023-12-16 09:24:34', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208619', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '维度设置评价流程', 'custom', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[],"multiType":"and","open":0,"superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[{"node":"self","raters":[{"empId":"","type":5}]},{"node":"super","raters":[{"empId":"1090069","empName":"Sasha","type":0}]}]', 4, 'true', '1090038', '2023-12-16 09:23:12', '1090038', '2023-12-16 09:24:35', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"approverType":"role","node":"peer_score","nodeWeight":100,"open":1,"raters":[{"empId":"1233038","empName":"吴箫","roleId":"6c9242e3-0012-44f7-96b2-32865660c0f5","roleName":"研发岗位","type":3},{"empId":"1234001","empName":"吴销售","roleId":"6c9242e3-0012-44f7-96b2-32865660c0f5","roleName":"研发岗位","type":3},{"empId":"1280001","empName":"苏华","roleId":"6c9242e3-0012-44f7-96b2-32865660c0f5","roleName":"研发岗位","type":3}],"roleAudit":true}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208620', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '上级+自评（普通指标）', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"anonymous":"false","nodeWeight":20,"open":1,"rateMode":"item"}', null, '{"auditNodes":[{"approvalOrder":1,"approverType":"user","multiType":"or","node":"superior_score","raters":[{"empId":"1093003","empName":"Mr. wei","type":0}],"scoreWeight":100,"transferFlag":"true","weight":100}],"multiType":"or","nodeWeight":80,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[{"node":"self","raters":[{"empId":"","type":5}]},{"node":"super","raters":[{"empId":"1093003","empName":"Mr. wei","type":0}]}]', 2, 'true', '1090038', '2023-12-16 09:23:12', '1090038', '2023-12-16 09:24:35', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, null, null, null, null, '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208621', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '等级改维度', 'custom', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[],"multiType":"and","open":0,"superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[{"node":"self","raters":[{"empId":"","type":5}]},{"node":"super","raters":[{"empId":"1090069","empName":"Sasha","level":1,"type":1}]}]', 2, 'false', '1090038', '2023-12-16 09:23:12', '1090038', '2023-12-16 09:23:12', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208622', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '等级改维度', 'custom', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[],"multiType":"and","open":0,"superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[{"node":"self","raters":[{"empId":"","type":5}]},{"node":"super","raters":[{"empId":"1090069","empName":"Sasha","level":1,"type":1}]}]', 2, 'false', '1090038', '2023-12-16 09:23:12', '1090038', '2023-12-16 09:23:12', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208624', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '简易表', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"anonymous":"false","nodeWeight":100,"open":1,"rateMode":"item"}', null, '{"auditNodes":[],"multiType":"and","open":0,"superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[{"node":"self","raters":[{"empId":"","type":5}]}]', 2, 'false', '1090038', '2023-12-16 09:23:12', '1090038', '2023-12-16 09:23:12', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', null, null, null, '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208627', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '销售部年度考核表', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0,"customFullScore":100,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[{"approvalOrder":1,"approverType":"user","multiType":"or","node":"superior_score","raters":[{"empId":"1093014","empName":"思威","type":0}],"scoreWeight":100,"transferFlag":"true","weight":100}],"multiType":"or","nodeWeight":100,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[{"node":"super","raters":[{"empId":"1093014","empName":"思威","type":0}]}]', 4, 'false', '1090038', '2023-12-16 09:23:13', '1090038', '2023-12-16 09:23:13', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208628', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '吴箫测试考核表', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"anonymous":"false","nodeWeight":10,"open":1,"rateMode":"item"}', null, '{"auditNodes":[{"approvalOrder":1,"approverType":"user","multiType":"or","node":"superior_score","raters":[{"empId":"1093014","empName":"思威","type":0}],"scoreWeight":100,"transferFlag":"true","weight":100}],"multiType":"or","nodeWeight":50,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[{"node":"self","raters":[{"empId":"","type":5}]},{"node":"peer","raters":[{"empId":"1216002","empName":"老詹","type":0}]},{"node":"sub","raters":[{"empId":"1090069","empName":"Sasha","type":0}]},{"node":"super","raters":[{"empId":"1093014","empName":"思威","type":0}]}]', 10, 'false', '1090038', '2023-12-16 09:23:13', '1090038', '2023-12-16 09:23:13', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"approverType":"user","multiType":"and","node":"peer_score","nodeWeight":20,"open":1,"rateMode":"item","raters":[{"empId":"1216002","empName":"老詹","type":0}],"roleAudit":false,"transferFlag":"true"}', '{"approverType":"user","multiType":"and","node":"sub_score","nodeWeight":20,"open":1,"rateMode":"item","raters":[{"empId":"1090069","empName":"Sasha","type":0}],"roleAudit":false,"transferFlag":"true"}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', null, null, null, '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208601', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '等级分布-自动计算-lufei', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0.00,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1","approverType":"manager","multiType":"or","node":"superior_score","raters":[{"avatar":"","empId":"1090038","empName":"路飞","type":0}],"scoreWeight":100.00,"transferFlag":"true","weight":100.00}],"multiType":"or","nodeWeight":100.00,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[]', 1, 'false', '1090038', '2023-12-16 09:23:09', '1090038', '2023-12-16 09:23:09', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208602', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '等级分布-自动计算-lufei', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0.00,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1","approverType":"manager","multiType":"or","node":"superior_score","raters":[{"avatar":"","empId":"1090038","empName":"路飞","type":0}],"scoreWeight":100.00,"transferFlag":"true","weight":100.00}],"multiType":"or","nodeWeight":100.00,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[]', 1, 'false', '1090038', '2023-12-16 09:23:09', '1090038', '2023-12-16 09:23:09', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208603', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '等级分布-自动计算-lufei', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0.00,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1","approverType":"manager","multiType":"or","node":"superior_score","raters":[{"avatar":"","empId":"1090038","empName":"路飞","type":0}],"scoreWeight":100.00,"transferFlag":"true","weight":100.00}],"multiType":"or","nodeWeight":100.00,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[]', 1, 'false', '1090038', '2023-12-16 09:23:09', '1090038', '2023-12-16 09:23:09', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208604', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '等级分布-自动计算-lufei', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0.00,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1","approverType":"manager","multiType":"or","node":"superior_score","raters":[{"avatar":"","empId":"1090038","empName":"路飞","type":0}],"scoreWeight":100.00,"transferFlag":"true","weight":100.00}],"multiType":"or","nodeWeight":100.00,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[]', 1, 'false', '1090038', '2023-12-16 09:23:10', '1090038', '2023-12-16 09:23:10', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208605', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '等级分布-自动计算-lufei', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0.00,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1","approverType":"manager","multiType":"or","node":"superior_score","raters":[{"empId":"1090069","empName":"Sasha","level":1,"type":2}],"scoreWeight":100.00,"transferFlag":"true","weight":100.00}],"multiType":"or","nodeWeight":100.00,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[]', 1, 'false', '1090038', '2023-12-16 09:23:10', '1090038', '2023-12-16 09:23:10', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208606', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '等级分布-自动计算-lufei', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0.00,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1","approverType":"manager","multiType":"or","node":"superior_score","raters":[{"empId":"1093014","empName":"思威","level":1,"type":1}],"scoreWeight":100.00,"transferFlag":"true","weight":100.00}],"multiType":"or","nodeWeight":100.00,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[]', 1, 'false', '1090038', '2023-12-16 09:23:10', '1090038', '2023-12-16 09:23:10', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208607', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '等级分布-自动计算-lufei', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0.00,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1","approverType":"manager","multiType":"or","node":"superior_score","raters":[{"avatar":"","empId":"1090038","empName":"路飞","type":0}],"scoreWeight":100.00,"transferFlag":"true","weight":100.00}],"multiType":"or","nodeWeight":100.00,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[]', 1, 'false', '1090038', '2023-12-16 09:23:10', '1090038', '2023-12-16 09:23:10', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208608', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '等级分布-自动计算-lufei', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0.00,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1","approverType":"manager","multiType":"or","node":"superior_score","raters":[{"empId":"1090038","empName":"路飞","level":2,"type":1}],"scoreWeight":100.00,"transferFlag":"true","weight":100.00}],"multiType":"or","nodeWeight":100.00,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[]', 1, 'false', '1090038', '2023-12-16 09:23:10', '1090038', '2023-12-16 09:23:10', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208609', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '等级分布-自动计算-lufei', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0.00,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1","approverType":"manager","multiType":"or","node":"superior_score","raters":[{"avatar":"","empId":"1090038","empName":"路飞","type":0}],"scoreWeight":100.00,"transferFlag":"true","weight":100.00}],"multiType":"or","nodeWeight":100.00,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[]', 1, 'false', '1090038', '2023-12-16 09:24:32', null, '2023-12-16 09:24:32', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208610', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '等级分布-自动计算-lufei', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0.00,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1","approverType":"manager","multiType":"or","node":"superior_score","raters":[{"avatar":"","empId":"1090038","empName":"路飞","type":0}],"scoreWeight":100.00,"transferFlag":"true","weight":100.00}],"multiType":"or","nodeWeight":100.00,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[]', 1, 'false', '1090038', '2023-12-16 09:23:11', '1090038', '2023-12-16 09:23:11', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208611', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '等级分布-自动计算-lufei', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0.00,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1","approverType":"manager","multiType":"or","node":"superior_score","raters":[{"avatar":"","empId":"1090038","empName":"路飞","type":0}],"scoreWeight":100.00,"transferFlag":"true","weight":100.00}],"multiType":"or","nodeWeight":100.00,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[]', 1, 'false', '1090038', '2023-12-16 09:24:33', null, '2023-12-16 09:24:33', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208612', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '等级分布-自动计算-lufei', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0.00,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1","approverType":"manager","multiType":"or","node":"superior_score","raters":[{"avatar":"","empId":"1090038","empName":"路飞","type":0}],"scoreWeight":100.00,"transferFlag":"true","weight":100.00}],"multiType":"or","nodeWeight":100.00,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[]', 1, 'false', '1090038', '2023-12-16 09:23:11', '1090038', '2023-12-16 09:23:11', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208613', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '等级分布-自动计算-lufei', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0.00,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1","approverType":"manager","multiType":"or","node":"superior_score","raters":[{"avatar":"","empId":"1090038","empName":"路飞","type":0}],"scoreWeight":100.00,"transferFlag":"true","weight":100.00}],"multiType":"or","nodeWeight":100.00,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[]', 1, 'false', '1090038', '2023-12-16 09:24:33', null, '2023-12-16 09:24:33', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208614', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '等级分布-自动计算-lufei', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0.00,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1","approverType":"manager","multiType":"or","node":"superior_score","raters":[{"avatar":"","empId":"1090038","empName":"路飞","type":0}],"scoreWeight":100.00,"transferFlag":"true","weight":100.00}],"multiType":"or","nodeWeight":100.00,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[]', 1, 'false', '1090038', '2023-12-16 09:23:11', '1090038', '2023-12-16 09:23:11', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208615', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '等级分布-自动计算-lufei', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0.00,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1","approverType":"manager","multiType":"or","node":"superior_score","raters":[{"empId":"1093014","empName":"思威","level":1,"type":1},{"empId":"1090071","empName":"杨浩","level":1,"type":1}],"scoreWeight":100.00,"transferFlag":"true","weight":100.00}],"multiType":"or","nodeWeight":100.00,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[]', 1, 'false', '1090038', '2023-12-16 09:23:11', '1090038', '2023-12-16 09:23:11', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208616', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '等级分布-自动计算-lufei', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0.00,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1","approverType":"manager","multiType":"or","node":"superior_score","raters":[{"empId":"1093014","empName":"思威","level":0,"type":1},{"avatar":"","empId":"1090038","empName":"路飞","type":0}],"scoreWeight":100.00,"transferFlag":"true","weight":100.00}],"multiType":"or","nodeWeight":100.00,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[]', 1, 'false', '1090038', '2023-12-16 09:23:11', '1090038', '2023-12-16 09:23:11', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208617', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '等级分布-自动计算-lufei', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0.00,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1","approverType":"manager","multiType":"or","node":"superior_score","raters":[{"empId":"1090069","empName":"Sasha","level":1,"type":2}],"scoreWeight":100.00,"transferFlag":"true","weight":100.00}],"multiType":"or","nodeWeight":100.00,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[]', 1, 'false', '1090038', '2023-12-16 09:23:12', '1090038', '2023-12-16 09:23:12', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208618', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '等级分布-自动计算-lufei', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0.00,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1","approverType":"manager","multiType":"or","node":"superior_score","raters":[{"empId":"1093014","empName":"思威","level":2,"type":1},{"empId":"1090071","empName":"杨浩","level":2,"type":1}],"scoreWeight":100.00,"transferFlag":"true","weight":100.00}],"multiType":"or","nodeWeight":100.00,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[]', 1, 'false', '1090038', '2023-12-16 09:24:34', null, '2023-12-16 09:24:34', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208619', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '等级分布-自动计算-lufei', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0.00,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1","approverType":"manager","multiType":"or","node":"superior_score","raters":[{"avatar":"","empId":"1090038","empName":"路飞","type":0}],"scoreWeight":100.00,"transferFlag":"true","weight":100.00}],"multiType":"or","nodeWeight":100.00,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[]', 1, 'false', '1090038', '2023-12-16 09:23:12', '1090038', '2023-12-16 09:23:12', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');
INSERT INTO   emp_eval_rule (emp_eval_id, company_id, rule_name, evaluate_type, type_weight_conf, score_value_conf, confirm_task, edit_exe_indi, enter_score, s3_self_rater, s3_mutual_rater, s3_super_rater, score_view, audit_result, confirm_result, publish_result, appeal_conf, level_group_id, index_raters, indicator_cnt, is_deleted, created_user, created_time, updated_user, updated_time, version, score_conf, comment_conf, s3_peer_rater, s3_sub_rater, edit_status, initiator, s3_appoint_rater, show_result_type, create_total_level_type, total_level_raters, finish_value_audit, dead_line_conf) VALUES ('1208620', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '等级分布-自动计算-lufei', 'simple', '{"checkItemWeight":1,"itemWeightLimit":100,"limit100Weight":0,"open":0}', '{"baseScore":0.00,"exceedFullScore":false,"fullScoreRange":true,"scoreRangeType":"fullScore"}', '{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}', '{"auditNodes":[],"open":0}', '{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}', '{"open":0}', null, '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1","approverType":"manager","multiType":"or","node":"superior_score","raters":[{"empId":"1093014","empName":"思威","level":1,"type":2}],"scoreWeight":100.00,"transferFlag":"true","weight":100.00}],"multiType":"or","nodeWeight":100.00,"open":1,"rateMode":"item","superiorScoreOrder":"sameTime"}', '{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}', '{"auditNodes":[{"approvalOrder":1,"approverInfo":"1090038","approverName":"路飞","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1090038","empName":"路飞","type":0}]},{"approvalOrder":2,"approverInfo":"1256044","approverName":"吴武","approverType":"user","multiType":"or","node":"final_result_audit","raters":[{"empId":"1256044","empName":"吴武","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}', '{"auto":0,"open":0,"sign":0}', '{"notPublic":false,"open":0}', '{"open":0}', '', '[]', 1, 'false', '1090038', '2023-12-16 09:23:12', '1090038', '2023-12-16 09:23:12', 0, '{"multiType":"or","transferFlag":"true"}', '{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}', '{"open":0,"raters":[],"roleAudit":false}', '{"open":0,"raters":[],"roleAudit":false}', 0, null, '{"auditNodes":[],"multiType":"and","open":0}', 6, 1, '[]', '{"auditNodes":[],"open":0}', '{"open":0}');


INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208601', '1090038', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208602', '1090069', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208603', '1207001', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208604', '1267001', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208605', '1093014', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208606', '1256044', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208607', '1090038', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208608', '1090071', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208609', '1093009', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208610', '1207002', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208611', '1208001', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208612', '1264001', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208613', '1265001', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208614', '1265002', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208615', '1090069', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208616', '1090071', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208617', '1093014', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208618', '1263001', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208619', '1090069', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208620', '1093003', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208621', '1093014', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208622', '1093014', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208623', '1109001', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208624', '1216003', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208625', '1233038', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208626', '1276001', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208627', '1093001', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
INSERT INTO   result_rank_instance_member (company_id, distribution_id, task_user_id, emp_id, emp_org_id, rank_order, rank_level, created_time, updated_time, created_user, updated_user, is_deleted, version) VALUES ('ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '10001', '1208628', '1234001', null, 1, 'level1', '2023-12-16', '2023-12-16', '', '', 'false', 0);
