drop TABLE if exists sequence;
CREATE TABLE sequence
(
    name          VARCHAR(50) NOT NULL,
    current_value INT         NOT NULL,
    increment     INT         NOT NULL DEFAULT 100,
    PRIMARY KEY (name)
) ENGINE = InnoDB;

-- # 获取当前sequence的值（返回当前值,增量）
CREATE FUNCTION seq_currval(seq_name VARCHAR(50)) RETURNS varchar(128) CHARSET utf8
    DETERMINISTIC
BEGIN
    DECLARE retval VARCHAR(64);
    SET retval = '-999999999,null';
SELECT concat(CAST(current_value AS CHAR), ',', CAST(increment AS CHAR))
INTO retval
FROM sequence
WHERE name = seq_name;
RETURN retval;
END $
DELIMITER ;

-- # 设置sequence值
DROP FUNCTION IF EXISTS seq_setval;
DELIMITER $
CREATE FUNCTION seq_setval(seq_name VARCHAR(50), value INTEGER) RETURNS varchar(128) CHARSET utf8
    DETERMINISTIC
BEGIN
UPDATE sequence
SET current_value = value
WHERE name = seq_name;
RETURN seq_currval(seq_name);
END $
DELIMITER ;

-- # 获取下一个sequence值

DROP FUNCTION IF EXISTS seq_nextval;
DELIMITER $
CREATE FUNCTION seq_nextval(seq_name VARCHAR(50)) RETURNS varchar(128) CHARSET utf8
    DETERMINISTIC
BEGIN
UPDATE sequence
SET current_value = current_value + increment
WHERE name = seq_name;
RETURN seq_currval(seq_name);
END $
DELIMITER ;

drop TABLE if exists test;
CREATE TABLE test
(
    name VARCHAR(50) NOT NULL,
    PRIMARY KEY (name)
) ENGINE = InnoDB;

insert into test(name) value ("name");