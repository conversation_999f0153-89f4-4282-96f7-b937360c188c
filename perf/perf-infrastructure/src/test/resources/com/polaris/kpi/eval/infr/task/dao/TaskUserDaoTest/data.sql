INSERT INTO `employee_base_info` (`id`,`employee_id`,`company_id`,`org_id`,`account_id`,`name`,`nickname`,`sex`,`mobile`,`id_type`,`id_num`,`type`,`status`,`entry_date`,`avatar`,`birthday`,`create_id`,`created_time`,`updated_time`,`name_chinese`,`hierarchy`,`nation`,`native_place`,`political_party`,`house_reg_type`,`house_reg_addr`,`living_addr`,`office_addr`,`highest_edu`,`marital_status`,`child_status`,`linkman_name`,`linkman_type`,`linkman_tel`,`is_delete`,`source`,`photo`,`memo`,`qy_user_id`,`ding_user_id`,`we_user_id`,`open_id`,`union_id`,`state_code`,`mobile_num`,`email`,`locale`,`ding_role_name`,`ding_manager_id`,`version`,`last_login_time`,`jobnumber`,`post`) VALUES ('********','********','1070324',null,'********','卢怡',null,null,null,null,null,'regular','on_the_job',null,'https://static-legacy.dingtalk.com/media/lADPD4Bh4Ek7TBPNBHDNBHA_1136_1136.jpg',null,null,'2023-06-12 08:52:18.158000','2023-12-08 13:38:33.109000','luyi卢怡ly',null,null,null,null,null,null,null,null,null,null,null,null,null,null,'false',null,null,null,null,'*****************',null,null,null,null,null,null,null,null,'',10,'2023-11-29 14:02:11','','核算员');

INSERT INTO `perf_evaluate_task_base` (`id`,`company_id`,`task_name`,`templ_base_id`,`templ_name`,`cycle_start_date`,`cycle_end_date`,`task_desc`,`evaluation_staff`,`exclude_staff`,`create_task_type`,`create_task_date_type`,`day`,`visible_type`,`task_status`,`is_deleted`,`created_user`,`created_time`,`updated_user`,`updated_time`,`cycle_type`,`templ_desc`,`templ_item_json`,`templ_initiate_json`,`templ_affirm_json`,`templ_evaluate_json`,`score_start_rule_type`,`score_start_rule_day`,`enter_score_method`,`public_type`,`templ_base_json`,`evaluate_type`,`public_emp_json`,`templ_execute_json`,`result_affirm`,`affirm_signature`,`templ_points_json`,`can_appeal`,`appeal_receiver`,`custom_full_score`,`enter_score_emp_type`,`public_dimension`,`public_to_emp`,`auto_result_affirm`,`auto_result_affirm_day`,`level_group_id`,`cycle_id`,`version`,`is_new_emp`,`total_cnt`,`draw_up_cnt`,`start_cnt`,`finish_cnt`,`confirm_task`,`edit_exe_indi`,`enter_score`,`score_sort_conf`,`score_view`,`audit_result`,`confirm_result`,`publish_result`,`appeal_conf`,`comment_conf`,`performance_type`,`score_conf`,`finish_value_audit`,`dead_line_conf`) VALUES ('1981461','1070324','2023年11月考核',null,null,'2023-10-01','2023-10-31',null,null,null,null,null,null,null,'published','true','34065808','2023-10-24 09:28:50','34065808','2023-10-24 10:18:35',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'',null,null,null,3,null,'false',0,'',1985759,0,0,40,39,0,0,'{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}','{"auditNodes":[],"open":0}','{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"before"}','{"exeType":0,"sameTime":false,"sortItems":[{"name":"自评","sort":1,"type":10},{"name":"同级互评","sort":2,"type":20},{"name":"下级互评","sort":3,"type":30},{"name":"上级评分","sort":4,"type":40},{"name":"指定评","sort":5,"type":50}]}','{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}','{"auditNodes":[{"approvalOrder":1,"approverInfo":"1","approverName":"直属主管","approverType":"manager","raters":[]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}','{"auto":0,"dimension":3,"open":1,"sign":0}','{"dimension":3,"notPublic":false,"opEmps":[],"open":1,"toEmps":[{"dept":false,"objType":"emp"},{"dept":false,"objType":"scoreEmp"}],"type":"afterFinished"}','{"open":0}','{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}',1,'{"multiType":"or","transferFlag":"true"}','{"auditNodes":[],"open":0}','{"open":0}');
INSERT INTO `perf_evaluate_task_base` (`id`,`company_id`,`task_name`,`templ_base_id`,`templ_name`,`cycle_start_date`,`cycle_end_date`,`task_desc`,`evaluation_staff`,`exclude_staff`,`create_task_type`,`create_task_date_type`,`day`,`visible_type`,`task_status`,`is_deleted`,`created_user`,`created_time`,`updated_user`,`updated_time`,`cycle_type`,`templ_desc`,`templ_item_json`,`templ_initiate_json`,`templ_affirm_json`,`templ_evaluate_json`,`score_start_rule_type`,`score_start_rule_day`,`enter_score_method`,`public_type`,`templ_base_json`,`evaluate_type`,`public_emp_json`,`templ_execute_json`,`result_affirm`,`affirm_signature`,`templ_points_json`,`can_appeal`,`appeal_receiver`,`custom_full_score`,`enter_score_emp_type`,`public_dimension`,`public_to_emp`,`auto_result_affirm`,`auto_result_affirm_day`,`level_group_id`,`cycle_id`,`version`,`is_new_emp`,`total_cnt`,`draw_up_cnt`,`start_cnt`,`finish_cnt`,`confirm_task`,`edit_exe_indi`,`enter_score`,`score_sort_conf`,`score_view`,`audit_result`,`confirm_result`,`publish_result`,`appeal_conf`,`comment_conf`,`performance_type`,`score_conf`,`finish_value_audit`,`dead_line_conf`) VALUES ('1981463','1070324','2023年11月考核（昆仑）',null,null,'2023-10-01','2023-10-31',null,null,null,null,null,null,null,'published','false','34046406','2023-10-24 09:44:26','34046406','2023-11-30 21:04:22',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'',null,null,null,3,null,'false',0,'',1985759,0,0,39,39,39,34,'{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}','{"auditNodes":[],"open":0}','{"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleType":"after","scoreStartRuleDay":1}','{"sortItems":[{"sort":1,"type":10,"name":"自评"},{"sort":2,"type":20,"name":"同级互评"},{"sort":3,"type":30,"name":"下级互评"},{"sort":4,"type":40,"name":"上级评分"},{"sort":5,"type":50,"name":"指定评"}],"exeType":1}','{"mutualScoreAnonymous":"true","mutualScoreViewRule":{"superior":"","mutual":"","appoint":"","examinee":""},"superiorScoreAnonymous":"true","appointScoreViewRule":{"superior":"","mutual":"","appoint":"","examinee":""},"superiorScoreViewRule":{"superior":"score,attach","mutual":"score,attach","appoint":"score,attach","examinee":"score,attach"},"selfScoreViewRule":{"superior":"","mutual":"","appoint":"","examinee":"score,attach"},"appointScoreAnonymous":"true"}','{"transferFlag":"true","vacancyApproveInfo":"","adminType":"main","commentReq":0,"vacancyApproveType":"superior","taskUserIds":[],"opEmpId":"34065808","tenantId":{"id":"1070324"},"auditNodes":[{"approvalOrder":1,"approverInfo":"2338941","approverName":"餐厅经理","raters":[],"approverType":"role"}],"multiType":"or","taskId":"1981463","vacancyApproveName":"","open":1}','{"auto": 1, "open": 1, "sign": 0, "taskId": "1981463", "autoDay": 1, "opEmpId": "34046406", "adminType": "child", "companyId": {"id": "1070324"}, "dimension": 15, "taskUserIds": ["2343527", "2343524", "2343525", "2343526", "2308407", "2308408", "2308409", "2308410", "2308411", "2308412", "2308413", "2308414", "2308415", "2308326", "2308327", "2308328", "2308329", "2308330", "2308331", "2308332", "2308333", "2308334", "2308314", "2308315", "2308316", "2308317", "2308318", "2308319", "2308320", "2308321", "2308402", "2308403", "2308404", "2308405"]}','{"open": 1, "type": "afterFinished", "opEmps": [], "toEmps": [{"dept": false, "objType": "emp"}, {"dept": false, "objType": "scoreEmp"}], "dimension": 15, "notPublic": false}','{"adminType":"main","resultAppealNode":50,"opEmpId":"34065808","canAppealDay":10,"appealReceiver":[{"objItems":[{"objName":"左立佳","objId":"34046406"}],"objType":"user"}],"tenantId":{"id":"1070324"},"taskId":"1981463","open":1}','{"scoreSummarySwitch":-1,"commentFlag":"notRequired","plusOrSubComment":0}',1,'{"transferFlag":"true","multiType":"or"}','{"auditNodes":[],"open":0}','{"open":0}');

INSERT INTO `perf_evaluate_task_user` (`id`,`company_id`,`task_id`,`emp_id`,`org_id`,`task_status`,`final_score`,`original_final_score`,`evaluation_level`,`reviewers_json`,`self_score_flag`,`manual_score_flag`,`superior_score_flag`,`item_score_flag`,`result_audit_flag`,`final_self_score`,`final_peer_score`,`final_sub_score`,`final_superior_score`,`final_item_score`,`last_score_comment`,`task_confirm_time`,`task_score_start_time`,`is_deleted`,`created_user`,`created_time`,`updated_user`,`updated_time`,`final_plus_score`,`final_subtract_score`,`public_flag`,`final_self_plus_score`,`final_peer_plus_score`,`final_sub_plus_score`,`final_superior_plus_score`,`final_self_subtract_score`,`final_peer_subtract_score`,`final_sub_subtract_score`,`final_superior_subtract_score`,`final_item_plus_score`,`final_item_subtract_score`,`final_item_auto_score`,`enter_score_flag`,`original_evaluation_level`,`distribution_flag`,`adjust_reason`,`total_points_num`,`signature_pic`,`item_change_user`,`has_appeal`,`appeal_receiver_id`,`cc_emp_ids`,`in_result_affirm_time`,`is_publish`,`all_scored`,`step_id`,`score_ranges`,`version`,`is_new_emp`,`emp_name`,`emp_org_name`,`emp_org_id`,`appeal_dead_line`,`org_name_list`,`org_changed`,`input_finish_status`,`confirm_dead_line`,`distribution_before_step_id`,`cycle_id`,`temp_task`,`rule_conf_status`,`avatar`,`score_end_time`,`eval_org_name`,`eval_org_id`,`weight_of_ref`,`score_of_ref`,`perf_coefficient`,`original_perf_coefficient`,`rule_conf_error`) VALUES ('2308335','1070324','1981463','********','20750280','resultsAuditing',94.20,94.20,'合格','[{"avatar":"","empId":"34046406","empName":"左立佳","exUserId":"536013105424095342","jobnumber":"10级1档","status":"on_the_job"}]',null,null,null,null,null,null,null,null,9.100,5.100,null,null,'2023-11-21 08:04:02','false','34046406','2023-10-24 09:54:40',null,'2023-11-22 09:57:38',80.000,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'合格',null,null,null,null,null,null,null,null,null,null,'true','3020778','[{"coeffType":2,"fieldJson":"[{\\"label\\":1,\\"value\\":1},{\\"symbol\\":true,\\"label\\":\\"+\\",\\"value\\":\\"+\\"},{\\"symbol\\":true,\\"label\\":\\"(\\",\\"value\\":\\"(\\"},{\\"yVal\\":true,\\"formulaFieldValue\\":0,\\"formulaFieldName\\":\\"考核任务得分\\",\\"companyFieldId\\":\\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\\",\\"label\\":\\"考核任务得分\\",\\"value\\":0,\\"isSystemField\\":\\"false\\"},{\\"symbol\\":true,\\"label\\":\\"-\\",\\"value\\":\\"-\\"},{\\"label\\":1,\\"value\\":1},{\\"label\\":0,\\"value\\":0},{\\"label\\":0,\\"value\\":0},{\\"symbol\\":true,\\"label\\":\\")\\",\\"value\\":\\")\\"},{\\"symbol\\":true,\\"label\\":\\"×\\",\\"value\\":\\"*\\"},{\\"label\\":0,\\"value\\":0},{\\"label\\":\\".\\",\\"value\\":\\".\\"},{\\"label\\":0,\\"value\\":0},{\\"label\\":1,\\"value\\":1}]","id":"4086401","max":200000.00,"min":110.00,"minAppendEqual":0,"perfCoefficient":"1 +  (  考核任务得分  - 100 )  * 0.01","place":2,"scoreRuleId":"3020787","scoreRuleName":"系统默认","stepId":"3020776","stepName":"优秀"},{"coeffType":2,"fieldJson":"[{\\"label\\":1,\\"value\\":1},{\\"symbol\\":true,\\"label\\":\\"+\\",\\"value\\":\\"+\\"},{\\"symbol\\":true,\\"label\\":\\"(\\",\\"value\\":\\"(\\"},{\\"yVal\\":true,\\"formulaFieldValue\\":0,\\"formulaFieldName\\":\\"考核任务得分\\",\\"companyFieldId\\":\\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\\",\\"label\\":\\"考核任务得分\\",\\"value\\":0,\\"isSystemField\\":\\"false\\"},{\\"symbol\\":true,\\"label\\":\\"-\\",\\"value\\":\\"-\\"},{\\"label\\":1,\\"value\\":1},{\\"label\\":0,\\"value\\":0},{\\"label\\":0,\\"value\\":0},{\\"symbol\\":true,\\"label\\":\\")\\",\\"value\\":\\")\\"},{\\"symbol\\":true,\\"label\\":\\"×\\",\\"value\\":\\"*\\"},{\\"label\\":0,\\"value\\":0},{\\"label\\":\\".\\",\\"value\\":\\".\\"},{\\"label\\":0,\\"value\\":0},{\\"label\\":1,\\"value\\":1}]","id":"4086402","max":110.00,"min":100.00,"minAppendEqual":0,"perfCoefficient":"1 +  (  考核任务得分  - 100 )  * 0.01","place":2,"scoreRuleId":"3020787","scoreRuleName":"系统默认","stepId":"3020777","stepName":"良好"},{"coeffType":2,"fieldJson":"[{\\"label\\":1,\\"value\\":1},{\\"symbol\\":true,\\"label\\":\\"+\\",\\"value\\":\\"+\\"},{\\"symbol\\":true,\\"label\\":\\"(\\",\\"value\\":\\"(\\"},{\\"yVal\\":true,\\"formulaFieldValue\\":0,\\"formulaFieldName\\":\\"考核任务得分\\",\\"companyFieldId\\":\\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\\",\\"label\\":\\"考核任务得分\\",\\"value\\":0,\\"isSystemField\\":\\"false\\"},{\\"symbol\\":true,\\"label\\":\\"-\\",\\"value\\":\\"-\\"},{\\"label\\":1,\\"value\\":1},{\\"label\\":0,\\"value\\":0},{\\"label\\":0,\\"value\\":0},{\\"symbol\\":true,\\"label\\":\\")\\",\\"value\\":\\")\\"},{\\"symbol\\":true,\\"label\\":\\"×\\",\\"value\\":\\"*\\"},{\\"label\\":0,\\"value\\":0},{\\"label\\":\\".\\",\\"value\\":\\".\\"},{\\"label\\":0,\\"value\\":0},{\\"label\\":1,\\"value\\":1}]","id":"4086403","max":100.00,"min":59.99,"minAppendEqual":0,"perfCoefficient":"1 +  (  考核任务得分  - 100 )  * 0.01","place":2,"scoreRuleId":"3020787","scoreRuleName":"系统默认","stepId":"3020778","stepName":"合格"},{"coeffType":2,"fieldJson":"[{\\"label\\":1,\\"value\\":1},{\\"symbol\\":true,\\"label\\":\\"+\\",\\"value\\":\\"+\\"},{\\"symbol\\":true,\\"label\\":\\"(\\",\\"value\\":\\"(\\"},{\\"yVal\\":true,\\"formulaFieldValue\\":0,\\"formulaFieldName\\":\\"考核任务得分\\",\\"companyFieldId\\":\\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\\",\\"label\\":\\"考核任务得分\\",\\"value\\":0,\\"isSystemField\\":\\"false\\"},{\\"symbol\\":true,\\"label\\":\\"-\\",\\"value\\":\\"-\\"},{\\"label\\":1,\\"value\\":1},{\\"label\\":0,\\"value\\":0},{\\"label\\":0,\\"value\\":0},{\\"symbol\\":true,\\"label\\":\\")\\",\\"value\\":\\")\\"},{\\"symbol\\":true,\\"label\\":\\"×\\",\\"value\\":\\"*\\"},{\\"label\\":0,\\"value\\":0},{\\"label\\":\\".\\",\\"value\\":\\".\\"},{\\"label\\":0,\\"value\\":0},{\\"label\\":1,\\"value\\":1}]","id":"4086404","max":59.99,"min":0.00,"minAppendEqual":0,"perfCoefficient":"1 +  (  考核任务得分  - 100 )  * 0.01","place":2,"scoreRuleId":"3020787","scoreRuleName":"系统默认","stepId":"3020779","stepName":"不合格"}]',10,0,'卢怡','综合管理',null,null,null,0,1,null,null,1985759,0,200,'https://static-legacy.dingtalk.com/media/lADPD3zUYKPX37TNA-zNA-w_1004_1004.jpg',null,null,null,null,null,'0.94','0.94',null);

INSERT INTO `perf_evaluate_task_score_result` (`id`,`company_id`,`task_id`,`org_id`,`emp_id`,`kpi_type_id`,`kpi_item_id`,`scorer_type`,`scorer_id`,`score`,`score_weight`,`score_comment`,`score_att_url`,`reviewers_type`,`audit_status`,`final_score`,`transfer_id`,`approval_order`,`created_user`,`created_time`,`updated_user`,`updated_time`,`plus_score`,`subtract_score`,`final_plus_score`,`final_subtract_score`,`final_weight_score`,`final_weight_plus_score`,`final_weight_subtract_score`,`score_level`,`no_item_score`,`emp_score`,`is_deleted`,`task_audit_id`,`modify_flag`,`version`,`merge_rs_infos`,`task_user_id`,`calibration_type`,`operate_reason`,`index_calibration`,`perf_coefficient`,`veto_flag`) VALUES ('073f2aa1-11df-42fd-aa02-d4d435a97000','1070324','1981463',null,'********','15805723-eab7-4b88-9f2d-b6cb46ffeec4','3059df15-b633-444e-9af1-90b87b3f83f6','superior_score','34046406',7.000,100.00,'',null,'or','pass',7.000,null,1,'97019879','2023-11-21 08:04:02','34046406','2023-12-11 16:21:19',null,null,null,null,4.900,null,null,'',4.900,7.000,'false',null,null,2,'[]','2308335',null,null,null,null,null);
INSERT INTO `perf_evaluate_task_score_result` (`id`,`company_id`,`task_id`,`org_id`,`emp_id`,`kpi_type_id`,`kpi_item_id`,`scorer_type`,`scorer_id`,`score`,`score_weight`,`score_comment`,`score_att_url`,`reviewers_type`,`audit_status`,`final_score`,`transfer_id`,`approval_order`,`created_user`,`created_time`,`updated_user`,`updated_time`,`plus_score`,`subtract_score`,`final_plus_score`,`final_subtract_score`,`final_weight_score`,`final_weight_plus_score`,`final_weight_subtract_score`,`score_level`,`no_item_score`,`emp_score`,`is_deleted`,`task_audit_id`,`modify_flag`,`version`,`merge_rs_infos`,`task_user_id`,`calibration_type`,`operate_reason`,`index_calibration`,`perf_coefficient`,`veto_flag`) VALUES ('1a7c5276-dd74-4976-b2bd-35ca1344983b','1070324','1981463',null,'********','1a1590b6-b4fb-4674-a4da-5ebdfad737b3','7d7d2206-418f-4b17-ba74-6c7d1692a9f2','appoint_score','34069905',0.000,100.00,'',null,'or','pass',null,null,1,'97019879','2023-11-22 09:27:27','34069905','2023-12-11 16:21:19',0.000,0.000,0.000,0.000,null,0.000,0.000,null,null,null,'false',null,null,3,'[]','2308335',null,null,null,null,null);
INSERT INTO `perf_evaluate_task_score_result` (`id`,`company_id`,`task_id`,`org_id`,`emp_id`,`kpi_type_id`,`kpi_item_id`,`scorer_type`,`scorer_id`,`score`,`score_weight`,`score_comment`,`score_att_url`,`reviewers_type`,`audit_status`,`final_score`,`transfer_id`,`approval_order`,`created_user`,`created_time`,`updated_user`,`updated_time`,`plus_score`,`subtract_score`,`final_plus_score`,`final_subtract_score`,`final_weight_score`,`final_weight_plus_score`,`final_weight_subtract_score`,`score_level`,`no_item_score`,`emp_score`,`is_deleted`,`task_audit_id`,`modify_flag`,`version`,`merge_rs_infos`,`task_user_id`,`calibration_type`,`operate_reason`,`index_calibration`,`perf_coefficient`,`veto_flag`) VALUES ('34b18a45-2e6c-4462-a84f-a7f2339a9315','1070324','1981463',null,'********','7bcc0133-7f61-470c-bd76-3def0ea5e0f4','562f8067-efac-484b-a040-a7fec5762847','appoint_score','34069905',40.000,100.00,'',null,'or','pass',null,null,1,'97019879','2023-11-22 09:27:27','34069905','2023-12-11 16:21:19',40.000,0.000,40.000,0.000,null,12.000,0.000,null,null,null,'false',null,null,3,'[]','2308335',null,null,null,null,null);
INSERT INTO `perf_evaluate_task_score_result` (`id`,`company_id`,`task_id`,`org_id`,`emp_id`,`kpi_type_id`,`kpi_item_id`,`scorer_type`,`scorer_id`,`score`,`score_weight`,`score_comment`,`score_att_url`,`reviewers_type`,`audit_status`,`final_score`,`transfer_id`,`approval_order`,`created_user`,`created_time`,`updated_user`,`updated_time`,`plus_score`,`subtract_score`,`final_plus_score`,`final_subtract_score`,`final_weight_score`,`final_weight_plus_score`,`final_weight_subtract_score`,`score_level`,`no_item_score`,`emp_score`,`is_deleted`,`task_audit_id`,`modify_flag`,`version`,`merge_rs_infos`,`task_user_id`,`calibration_type`,`operate_reason`,`index_calibration`,`perf_coefficient`,`veto_flag`) VALUES ('425f7d42-0fa9-4f8c-a347-a69bc4a992b9','1070324','1981463',null,'********','15805723-eab7-4b88-9f2d-b6cb46ffeec4','3059df15-b633-444e-9af1-90b87b3f83f6','appoint_score','34069905',8.000,100.00,'',null,'or','pass',8.000,null,1,'97019879','2023-11-22 09:27:27','34069905','2023-12-11 16:21:19',null,null,null,null,2.400,null,null,'',2.400,8.000,'false',null,null,2,'[]','2308335',null,null,null,null,null);
INSERT INTO `perf_evaluate_task_score_result` (`id`,`company_id`,`task_id`,`org_id`,`emp_id`,`kpi_type_id`,`kpi_item_id`,`scorer_type`,`scorer_id`,`score`,`score_weight`,`score_comment`,`score_att_url`,`reviewers_type`,`audit_status`,`final_score`,`transfer_id`,`approval_order`,`created_user`,`created_time`,`updated_user`,`updated_time`,`plus_score`,`subtract_score`,`final_plus_score`,`final_subtract_score`,`final_weight_score`,`final_weight_plus_score`,`final_weight_subtract_score`,`score_level`,`no_item_score`,`emp_score`,`is_deleted`,`task_audit_id`,`modify_flag`,`version`,`merge_rs_infos`,`task_user_id`,`calibration_type`,`operate_reason`,`index_calibration`,`perf_coefficient`,`veto_flag`) VALUES ('4f542cad-b2cf-4825-a403-fce6fdcd258d','1070324','1981463',null,'********','15805723-eab7-4b88-9f2d-b6cb46ffeec4','84cb03cc-a85b-43c3-940f-0282275dfa5b','appoint_score','34069905',9.000,100.00,'',null,'or','pass',9.000,null,1,'97019879','2023-11-22 09:27:27','34069905','2023-12-11 16:21:19',null,null,null,null,2.700,null,null,'',2.700,9.000,'false',null,null,2,'[]','2308335',null,null,null,null,null);
INSERT INTO `perf_evaluate_task_score_result` (`id`,`company_id`,`task_id`,`org_id`,`emp_id`,`kpi_type_id`,`kpi_item_id`,`scorer_type`,`scorer_id`,`score`,`score_weight`,`score_comment`,`score_att_url`,`reviewers_type`,`audit_status`,`final_score`,`transfer_id`,`approval_order`,`created_user`,`created_time`,`updated_user`,`updated_time`,`plus_score`,`subtract_score`,`final_plus_score`,`final_subtract_score`,`final_weight_score`,`final_weight_plus_score`,`final_weight_subtract_score`,`score_level`,`no_item_score`,`emp_score`,`is_deleted`,`task_audit_id`,`modify_flag`,`version`,`merge_rs_infos`,`task_user_id`,`calibration_type`,`operate_reason`,`index_calibration`,`perf_coefficient`,`veto_flag`) VALUES ('5de5f8b5-7a9f-479b-9012-0caf75b04210','1070324','1981463',null,'********','1a1590b6-b4fb-4674-a4da-5ebdfad737b3','d7f47da6-2a11-4d81-9583-adeaf1a597ac','superior_score','34046406',0.000,100.00,'',null,'or','pass',null,null,1,'97019879','2023-11-21 08:04:02','34046406','2023-12-11 16:21:19',0.000,0.000,0.000,0.000,null,0.000,0.000,null,null,null,'false',null,null,3,'[]','2308335',null,null,null,null,null);
INSERT INTO `perf_evaluate_task_score_result` (`id`,`company_id`,`task_id`,`org_id`,`emp_id`,`kpi_type_id`,`kpi_item_id`,`scorer_type`,`scorer_id`,`score`,`score_weight`,`score_comment`,`score_att_url`,`reviewers_type`,`audit_status`,`final_score`,`transfer_id`,`approval_order`,`created_user`,`created_time`,`updated_user`,`updated_time`,`plus_score`,`subtract_score`,`final_plus_score`,`final_subtract_score`,`final_weight_score`,`final_weight_plus_score`,`final_weight_subtract_score`,`score_level`,`no_item_score`,`emp_score`,`is_deleted`,`task_audit_id`,`modify_flag`,`version`,`merge_rs_infos`,`task_user_id`,`calibration_type`,`operate_reason`,`index_calibration`,`perf_coefficient`,`veto_flag`) VALUES ('6a30c6dc-95a8-4ef9-8f1f-5badcd7e80db','1070324','1981463','','********',null,null,'final_result_audit','34046406',null,100.00,null,null,'or',null,null,null,1,'34046406','2023-11-22 09:57:39',null,'2023-11-22 09:57:39',null,null,null,null,null,null,null,null,null,null,'false','5432344',null,0,'[]','2308335',null,null,null,null,null);
INSERT INTO `perf_evaluate_task_score_result` (`id`,`company_id`,`task_id`,`org_id`,`emp_id`,`kpi_type_id`,`kpi_item_id`,`scorer_type`,`scorer_id`,`score`,`score_weight`,`score_comment`,`score_att_url`,`reviewers_type`,`audit_status`,`final_score`,`transfer_id`,`approval_order`,`created_user`,`created_time`,`updated_user`,`updated_time`,`plus_score`,`subtract_score`,`final_plus_score`,`final_subtract_score`,`final_weight_score`,`final_weight_plus_score`,`final_weight_subtract_score`,`score_level`,`no_item_score`,`emp_score`,`is_deleted`,`task_audit_id`,`modify_flag`,`version`,`merge_rs_infos`,`task_user_id`,`calibration_type`,`operate_reason`,`index_calibration`,`perf_coefficient`,`veto_flag`) VALUES ('70eaf5d7-6ebb-46c9-becc-510e3a30b2cd','1070324','1981463',null,'********','7bcc0133-7f61-470c-bd76-3def0ea5e0f4','b0c8915b-1b48-4424-b5bb-ea3a88917145','superior_score','34046406',15.000,100.00,'',null,'or','pass',null,null,1,'97019879','2023-11-21 08:04:02','34046406','2023-12-11 16:21:19',15.000,0.000,15.000,0.000,null,10.500,0.000,null,null,null,'false',null,null,3,'[]','2308335',null,null,null,null,null);
INSERT INTO `perf_evaluate_task_score_result` (`id`,`company_id`,`task_id`,`org_id`,`emp_id`,`kpi_type_id`,`kpi_item_id`,`scorer_type`,`scorer_id`,`score`,`score_weight`,`score_comment`,`score_att_url`,`reviewers_type`,`audit_status`,`final_score`,`transfer_id`,`approval_order`,`created_user`,`created_time`,`updated_user`,`updated_time`,`plus_score`,`subtract_score`,`final_plus_score`,`final_subtract_score`,`final_weight_score`,`final_weight_plus_score`,`final_weight_subtract_score`,`score_level`,`no_item_score`,`emp_score`,`is_deleted`,`task_audit_id`,`modify_flag`,`version`,`merge_rs_infos`,`task_user_id`,`calibration_type`,`operate_reason`,`index_calibration`,`perf_coefficient`,`veto_flag`) VALUES ('72693e6a-f17e-4e00-ba34-65cc1b209921','1070324','1981463',null,'********','1a1590b6-b4fb-4674-a4da-5ebdfad737b3','d7f47da6-2a11-4d81-9583-adeaf1a597ac','appoint_score','34069905',0.000,100.00,'',null,'or','pass',null,null,1,'97019879','2023-11-22 09:27:27','34069905','2023-12-11 16:21:19',0.000,0.000,0.000,0.000,null,0.000,0.000,null,null,null,'false',null,null,3,'[]','2308335',null,null,null,null,null);
INSERT INTO `perf_evaluate_task_score_result` (`id`,`company_id`,`task_id`,`org_id`,`emp_id`,`kpi_type_id`,`kpi_item_id`,`scorer_type`,`scorer_id`,`score`,`score_weight`,`score_comment`,`score_att_url`,`reviewers_type`,`audit_status`,`final_score`,`transfer_id`,`approval_order`,`created_user`,`created_time`,`updated_user`,`updated_time`,`plus_score`,`subtract_score`,`final_plus_score`,`final_subtract_score`,`final_weight_score`,`final_weight_plus_score`,`final_weight_subtract_score`,`score_level`,`no_item_score`,`emp_score`,`is_deleted`,`task_audit_id`,`modify_flag`,`version`,`merge_rs_infos`,`task_user_id`,`calibration_type`,`operate_reason`,`index_calibration`,`perf_coefficient`,`veto_flag`) VALUES ('86367496-eeca-4c48-a384-ce717c106a24','1070324','1981463',null,'********','1a1590b6-b4fb-4674-a4da-5ebdfad737b3','7d7d2206-418f-4b17-ba74-6c7d1692a9f2','superior_score','34046406',0.000,100.00,'',null,'or','pass',null,null,1,'97019879','2023-11-21 08:04:02','34046406','2023-12-11 16:21:19',0.000,0.000,0.000,0.000,null,0.000,0.000,null,null,null,'false',null,null,3,'[]','2308335',null,null,null,null,null);
INSERT INTO `perf_evaluate_task_score_result` (`id`,`company_id`,`task_id`,`org_id`,`emp_id`,`kpi_type_id`,`kpi_item_id`,`scorer_type`,`scorer_id`,`score`,`score_weight`,`score_comment`,`score_att_url`,`reviewers_type`,`audit_status`,`final_score`,`transfer_id`,`approval_order`,`created_user`,`created_time`,`updated_user`,`updated_time`,`plus_score`,`subtract_score`,`final_plus_score`,`final_subtract_score`,`final_weight_score`,`final_weight_plus_score`,`final_weight_subtract_score`,`score_level`,`no_item_score`,`emp_score`,`is_deleted`,`task_audit_id`,`modify_flag`,`version`,`merge_rs_infos`,`task_user_id`,`calibration_type`,`operate_reason`,`index_calibration`,`perf_coefficient`,`veto_flag`) VALUES ('879a1d83-c1ae-44e6-9942-52b3387d6401','1070324','1981463',null,'********','7bcc0133-7f61-470c-bd76-3def0ea5e0f4','b75a6b2c-2451-41f9-b940-f5bdff7112a8','superior_score','34046406',25.000,100.00,'',null,'or','pass',null,null,1,'97019879','2023-11-21 08:04:02','34046406','2023-12-11 16:21:19',25.000,0.000,25.000,0.000,null,17.500,0.000,null,null,null,'false',null,null,3,'[]','2308335',null,null,null,null,null);
INSERT INTO `perf_evaluate_task_score_result` (`id`,`company_id`,`task_id`,`org_id`,`emp_id`,`kpi_type_id`,`kpi_item_id`,`scorer_type`,`scorer_id`,`score`,`score_weight`,`score_comment`,`score_att_url`,`reviewers_type`,`audit_status`,`final_score`,`transfer_id`,`approval_order`,`created_user`,`created_time`,`updated_user`,`updated_time`,`plus_score`,`subtract_score`,`final_plus_score`,`final_subtract_score`,`final_weight_score`,`final_weight_plus_score`,`final_weight_subtract_score`,`score_level`,`no_item_score`,`emp_score`,`is_deleted`,`task_audit_id`,`modify_flag`,`version`,`merge_rs_infos`,`task_user_id`,`calibration_type`,`operate_reason`,`index_calibration`,`perf_coefficient`,`veto_flag`) VALUES ('be2139f5-7054-4a9a-9f0a-b8c7ae8b6b81','1070324','1981463',null,'********','7bcc0133-7f61-470c-bd76-3def0ea5e0f4','b0c8915b-1b48-4424-b5bb-ea3a88917145','appoint_score','34069905',15.000,100.00,'',null,'or','pass',null,null,1,'97019879','2023-11-22 09:27:27','34069905','2023-12-11 16:21:19',15.000,0.000,15.000,0.000,null,4.500,0.000,null,null,null,'false',null,null,3,'[]','2308335',null,null,null,null,null);
INSERT INTO `perf_evaluate_task_score_result` (`id`,`company_id`,`task_id`,`org_id`,`emp_id`,`kpi_type_id`,`kpi_item_id`,`scorer_type`,`scorer_id`,`score`,`score_weight`,`score_comment`,`score_att_url`,`reviewers_type`,`audit_status`,`final_score`,`transfer_id`,`approval_order`,`created_user`,`created_time`,`updated_user`,`updated_time`,`plus_score`,`subtract_score`,`final_plus_score`,`final_subtract_score`,`final_weight_score`,`final_weight_plus_score`,`final_weight_subtract_score`,`score_level`,`no_item_score`,`emp_score`,`is_deleted`,`task_audit_id`,`modify_flag`,`version`,`merge_rs_infos`,`task_user_id`,`calibration_type`,`operate_reason`,`index_calibration`,`perf_coefficient`,`veto_flag`) VALUES ('c131cee2-a6ac-485d-ad2a-0589e8d2e444','1070324','1981463',null,'********','15805723-eab7-4b88-9f2d-b6cb46ffeec4','84cb03cc-a85b-43c3-940f-0282275dfa5b','superior_score','34046406',6.000,100.00,'',null,'or','pass',6.000,null,1,'97019879','2023-11-21 08:04:02','34046406','2023-12-11 16:21:19',null,null,null,null,4.200,null,null,'',4.200,6.000,'false',null,null,2,'[]','2308335',null,null,null,null,null);
INSERT INTO `perf_evaluate_task_score_result` (`id`,`company_id`,`task_id`,`org_id`,`emp_id`,`kpi_type_id`,`kpi_item_id`,`scorer_type`,`scorer_id`,`score`,`score_weight`,`score_comment`,`score_att_url`,`reviewers_type`,`audit_status`,`final_score`,`transfer_id`,`approval_order`,`created_user`,`created_time`,`updated_user`,`updated_time`,`plus_score`,`subtract_score`,`final_plus_score`,`final_subtract_score`,`final_weight_score`,`final_weight_plus_score`,`final_weight_subtract_score`,`score_level`,`no_item_score`,`emp_score`,`is_deleted`,`task_audit_id`,`modify_flag`,`version`,`merge_rs_infos`,`task_user_id`,`calibration_type`,`operate_reason`,`index_calibration`,`perf_coefficient`,`veto_flag`) VALUES ('c76bc562-1fde-40ca-ace1-c8cf00b71388','1070324','1981463',null,'********','63b2f098-33c4-4c46-a9d0-7ca28ae5c4fb','7ed6e149-6267-4943-91bc-ac1023e98ba7','appoint_score','34069905',null,100.00,'',null,'or','pass',null,null,1,'97019879','2023-11-22 09:27:27','34069905','2023-12-11 16:21:19',null,null,null,null,null,null,null,'',null,null,'false',null,null,2,'[]','2308335',null,null,null,null,'false');
INSERT INTO `perf_evaluate_task_score_result` (`id`,`company_id`,`task_id`,`org_id`,`emp_id`,`kpi_type_id`,`kpi_item_id`,`scorer_type`,`scorer_id`,`score`,`score_weight`,`score_comment`,`score_att_url`,`reviewers_type`,`audit_status`,`final_score`,`transfer_id`,`approval_order`,`created_user`,`created_time`,`updated_user`,`updated_time`,`plus_score`,`subtract_score`,`final_plus_score`,`final_subtract_score`,`final_weight_score`,`final_weight_plus_score`,`final_weight_subtract_score`,`score_level`,`no_item_score`,`emp_score`,`is_deleted`,`task_audit_id`,`modify_flag`,`version`,`merge_rs_infos`,`task_user_id`,`calibration_type`,`operate_reason`,`index_calibration`,`perf_coefficient`,`veto_flag`) VALUES ('ce3106f6-9e8b-419c-81ae-1e86b6a105df','1070324','1981463',null,'********','63b2f098-33c4-4c46-a9d0-7ca28ae5c4fb','7ed6e149-6267-4943-91bc-ac1023e98ba7','superior_score','34046406',null,100.00,'',null,'or','pass',null,null,1,'97019879','2023-11-21 08:04:02','34046406','2023-12-11 16:21:19',null,null,null,null,null,null,null,'',null,null,'false',null,null,2,'[]','2308335',null,null,null,null,'false');
INSERT INTO `perf_evaluate_task_score_result` (`id`,`company_id`,`task_id`,`org_id`,`emp_id`,`kpi_type_id`,`kpi_item_id`,`scorer_type`,`scorer_id`,`score`,`score_weight`,`score_comment`,`score_att_url`,`reviewers_type`,`audit_status`,`final_score`,`transfer_id`,`approval_order`,`created_user`,`created_time`,`updated_user`,`updated_time`,`plus_score`,`subtract_score`,`final_plus_score`,`final_subtract_score`,`final_weight_score`,`final_weight_plus_score`,`final_weight_subtract_score`,`score_level`,`no_item_score`,`emp_score`,`is_deleted`,`task_audit_id`,`modify_flag`,`version`,`merge_rs_infos`,`task_user_id`,`calibration_type`,`operate_reason`,`index_calibration`,`perf_coefficient`,`veto_flag`) VALUES ('d3f9f2d1-8f91-4e82-a19b-769661b39e7b','1070324','1981463',null,'********','7bcc0133-7f61-470c-bd76-3def0ea5e0f4','b75a6b2c-2451-41f9-b940-f5bdff7112a8','appoint_score','34069905',25.000,100.00,'',null,'or','pass',null,null,1,'97019879','2023-11-22 09:27:27','34069905','2023-12-11 16:21:19',25.000,0.000,25.000,0.000,null,7.500,0.000,null,null,null,'false',null,null,3,'[]','2308335',null,null,null,null,null);
INSERT INTO `perf_evaluate_task_score_result` (`id`,`company_id`,`task_id`,`org_id`,`emp_id`,`kpi_type_id`,`kpi_item_id`,`scorer_type`,`scorer_id`,`score`,`score_weight`,`score_comment`,`score_att_url`,`reviewers_type`,`audit_status`,`final_score`,`transfer_id`,`approval_order`,`created_user`,`created_time`,`updated_user`,`updated_time`,`plus_score`,`subtract_score`,`final_plus_score`,`final_subtract_score`,`final_weight_score`,`final_weight_plus_score`,`final_weight_subtract_score`,`score_level`,`no_item_score`,`emp_score`,`is_deleted`,`task_audit_id`,`modify_flag`,`version`,`merge_rs_infos`,`task_user_id`,`calibration_type`,`operate_reason`,`index_calibration`,`perf_coefficient`,`veto_flag`) VALUES ('f6850f31-db61-42cf-b46c-15444db95b03','1070324','1981463',null,'********','7bcc0133-7f61-470c-bd76-3def0ea5e0f4','562f8067-efac-484b-a040-a7fec5762847','superior_score','34046406',40.000,100.00,'',null,'or','pass',null,null,1,'97019879','2023-11-21 08:04:02','34046406','2023-12-11 16:21:19',40.000,0.000,40.000,0.000,null,28.000,0.000,null,null,null,'false',null,null,3,'[]','2308335',null,null,null,null,null);
INSERT INTO `perf_evaluate_cycle` (`id`,`company_id`,`name`,`year`,`type`,`value`,`cycle_start`,`cycle_end`,`eval_cnt`,`from_task_id`,`created_user`,`updated_user`,`created_time`,`updated_time`,`version`,`cycle_status`,`is_new_cycle`) VALUES (1985759,'1070324','2023年11月',2023,'month',11,'2023-10-21','2023-11-20',39,'0','96576553','97019879','2023-09-15 16:28:32','2023-11-14 09:48:42',1,'normal',1);

INSERT INTO `emp_eval_rule` (`emp_eval_id`,`company_id`,`rule_name`,`evaluate_type`,`type_weight_conf`,`score_value_conf`,`confirm_task`,`edit_exe_indi`,`enter_score`,`s3_self_rater`,`s3_peer_rater`,`s3_sub_rater`,`s3_super_rater`,`score_view`,`audit_result`,`confirm_result`,`publish_result`,`appeal_conf`,`level_group_id`,`index_raters`,`indicator_cnt`,`score_conf`,`comment_conf`,`is_deleted`,`created_user`,`created_time`,`updated_user`,`updated_time`,`version`,`edit_status`,`initiator`,`s3_appoint_rater`,`show_result_type`,`create_total_level_type`,`total_level_raters`,`finish_value_audit`,`dead_line_conf`) VALUES ('2308335','1070324','朝阳项目部餐厅办事员月度绩效考核表','custom','{"checkItemWeight":0,"itemWeightLimit":100,"limit100Weight":0,"open":0}','{"baseScore":0,"exceedFullScore":false,"fullScoreRange":false,"scoreRangeType":"weightScore"}','{"auditNodes":[],"modifyItemDimension":"all","open":0,"openConfirmLT":0}','{"auditNodes":[],"open":0}','{"autoEnter":true,"enterScoreEmpType":1,"enterScoreMethod":"auto","scoreStartRuleDay":1,"scoreStartRuleType":"after"}','{"open":0}','{"open":0,"raters":[],"roleAudit":false}','{"open":0,"raters":[],"roleAudit":false}','{"auditNodes":[{"approvalOrder":1,"approverType":"manager","multiType":"or","node":"superior_score","raters":[{"empId":"34046406","empName":"左立佳","level":2,"type":1}],"scoreWeight":100,"transferFlag":"true","weight":100}],"multiType":"or","nodeWeight":70,"open":1,"superiorScoreOrder":"sameTime"}','{"appointScoreAnonymous":"true","appointScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"mutualScoreAnonymous":"true","mutualScoreViewRule":{"appoint":"","examinee":"","mutual":"","superior":""},"selfScoreViewRule":{"appoint":"","examinee":"score,attach","mutual":"","superior":""},"superiorScoreAnonymous":"true","superiorScoreViewRule":{"appoint":"score,attach","examinee":"score,attach","mutual":"score,attach","superior":"score,attach"}}','{"auditNodes":[{"approvalOrder":1,"approverInfo":"2338941","approverName":"餐厅经理","approverType":"role","multiType":"or","raters":[{"avatar":"","dingUserId":"536013105424095342","empId":"34046406","empName":"左立佳","roleId":"2338941","roleName":"餐厅经理","type":0}]}],"commentReq":0,"multiType":"or","open":1,"transferFlag":"true","vacancyApproveInfo":"","vacancyApproveName":"","vacancyApproveType":"superior"}','{"auto": 1, "open": 1, "sign": 0, "autoDay": 1, "dimension": 15}','{"open": 1, "type": "afterFinished", "opEmps": [], "toEmps": [{"dept": false, "objType": "emp"}, {"dept": false, "objType": "scoreEmp"}], "dimension": 15, "notPublic": false}','{"appealReceiver":[{"objItems":[{"objId":"34046406","objName":"左立佳"}],"objType":"user"}],"canAppealDay":10,"open":1,"resultAppealNode":50}','','[{"node":"super","raters":[{"empId":"34046406","empName":"左立佳","level":2,"type":1}]},{"node":"appoint","raters":[{"empId":"34069905","empName":"王吟吟","roleId":"2338932","type":3}]}]',8,'{"multiType":"or","transferFlag":"true"}','{"commentFlag":"notRequired","plusOrSubComment":0,"scoreSummarySwitch":-1}','false','97019879','2023-10-24 09:55:23','97019879','2023-11-29 14:02:23',0,0,'34046406','{"auditNodes":[{"approvalOrder":1,"approverType":"role","multiType":"or","node":"appoint_score","raters":[{"empId":"34069905","empName":"王吟吟","roleId":"2338932","type":3}],"scoreWeight":100,"transferFlag":"true","weight":100}],"multiType":"or","nodeWeight":30,"open":1}',6,1,'[]','{"auditNodes":[],"open":0}','{"open":0}');

# select * from system_admin_set;
# select * from role;
# select * from role_ref_emp;
# select * from ref_eval ;
# select * from perf_evaluate_task_user;
# select * from employee_base_info;
# select * from perf_evaluate_task_score_result;
# select * from emp_organization;

#
# SELECT u.*,
#        e.name,
#        e.is_delete                                                                                   as leaved,
#        b.task_name,
#        b.evaluate_type,
#        u.emp_org_name                                                                                as org_name,
#        (SELECT GROUP_CONCAT(r.role_name)
#         FROM role r
#         WHERE r.company_id = '1070324'
#           and r.is_deleted = 'false'
#           AND r.id in (SELECT role_id
#                        FROM role_ref_emp
#                        WHERE company_id = '1070324' AND is_deleted = 'false' AND emp_id = u.emp_id)) as roleName,
#        ru.create_total_level_type,
#        concat(cyc.cycle_start, ' 至 ', cyc.cycle_end)                                                    cycle_date
# FROM perf_evaluate_task_user as u
#          INNER JOIN employee_base_info as e on e.company_id = u.company_id AND e.employee_id = u.emp_id
#          INNER JOIN perf_evaluate_task_base as b on u.company_id = b.company_id and u.task_id = b.id
#          INNER JOIN perf_evaluate_cycle as cyc on b.company_id = cyc.company_id and b.cycle_id = cyc.id
#          LEFT OUTER JOIN emp_eval_rule as ru
#                          on u.company_id = ru.company_id and u.id = ru.emp_eval_id AND ru.is_deleted = 'false'
# WHERE (u.is_deleted = ? AND b.is_deleted = ? AND u.task_id in (?))
# GROUP BY u.id
# ORDER BY u.final_score DESC
# LIMIT 0 , 10
