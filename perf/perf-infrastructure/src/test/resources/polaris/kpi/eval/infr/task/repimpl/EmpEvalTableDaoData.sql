
-- 5 条
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1057707', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1068401', '1208001', '1011602', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090071', '2022-10-09 11:30:04', null, '2022-10-09 11:30:04', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015402","max":200000.00,"min":50.00,"scoreRuleId":"2015401","stepId":"1000477","stepName":"A"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015403","max":50.00,"min":0.00,"scoreRuleId":"2015401","stepId":"1000480","stepName":"D"}]', null, '赵王', '新人入职', null, null, 1028016, null, null, 0, 0, 0, null, '', null, null);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1111810', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1112201', '1208001', '1011602', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1093014', '2022-11-08 15:33:11', null, '2022-11-08 15:33:11', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015402","max":200000.00,"min":50.00,"scoreRuleId":"2015401","stepId":"1000477","stepName":"A"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015403","max":50.00,"min":0.00,"scoreRuleId":"2015401","stepId":"1000480","stepName":"D"}]', null, '赵王', '新人入职', null, null, 1112401, null, null, 0, 0, 0, null, '', null, null);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1112203', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1112306', '1208001', '1011602', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'true', '1093017', '2022-11-09 11:19:47', null, '2022-11-09 18:11:34', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015402","max":200000.00,"min":50.00,"scoreRuleId":"2015401","stepId":"1000477","stepName":"A"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015403","max":50.00,"min":0.00,"scoreRuleId":"2015401","stepId":"1000480","stepName":"D"}]', null, '赵王', '新人入职', null, null, 1111702, null, null, 200, 0, 0, null, '', null, null);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1113001', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1112305', '1208001', '1011602', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090071', '2022-11-10 15:57:24', null, '2022-11-17 19:14:47', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015402","max":200000.00,"min":50.00,"scoreRuleId":"2015401","stepId":"1000477","stepName":"A"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015403","max":50.00,"min":0.00,"scoreRuleId":"2015401","stepId":"1000480","stepName":"D"}]', null, '赵王', '新人入职', null, null, 1112301, null, null, 200, 0, 0, null, '', null, null);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1114705', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1115004', '1208001', '1011602', 'confirming', null, null, null, '[{"empId":"1090071","avatar":"https://static-legacy.dingtalk.com/media/lADPDg7mQqMlUJPNArzNArw_700_700.jpg","exUserId":"012625253735848641","empName":"杨浩"}]', null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090071', '2022-11-16 19:19:46', null, '2022-11-16 19:20:10', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015402","max":200000.00,"min":50.00,"scoreRuleId":"2015401","stepId":"1000477","stepName":"A"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015403","max":50.00,"min":0.00,"scoreRuleId":"2015401","stepId":"1000480","stepName":"D"}]', null, '赵王', '新人入职', null, null, 1114504, null, null, 200, 0, 0, null, '', null, null);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1115404', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1115602', '1208001', '1011602', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090071', '2022-11-18 11:33:39', null, '2022-11-18 11:34:32', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015402","max":200000.00,"min":50.00,"scoreRuleId":"2015401","stepId":"1000477","stepName":"A"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015403","max":50.00,"min":0.00,"scoreRuleId":"2015401","stepId":"1000480","stepName":"D"}]', null, '赵王', '新人入职', null, null, 1115202, null, null, 200, 0, 0, null, '', null, null);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1116403', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1116203', '1208001', '1011602', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1216003', '2022-11-20 18:53:09', null, '2022-11-20 18:53:09', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015402","max":200000.00,"min":50.00,"scoreRuleId":"2015401","stepId":"1000477","stepName":"A"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015403","max":50.00,"min":0.00,"scoreRuleId":"2015401","stepId":"1000480","stepName":"D"}]', null, '赵王', '新人入职', null, null, 1115702, null, null, 0, 0, 0, null, '', null, null);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1117702', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1116502', '1208001', '1011602', 'finished', 100.00, 100.00, 'A', '[{"avatar":"","empId":"1208001","empName":"赵王","exUserId":"16613350568899191"}]', 'true', 'true', 'true', 'true', null, 0.000, 0.000, 0.000, 100.000, 0.000, null, null, '2022-11-21 21:06:24', 'false', '1090071', '2022-11-21 19:36:03', '1208001', '2022-11-21 21:08:20', null, null, 'true', null, null, null, null, null, null, null, null, null, null, 0.000, null, 'A', null, null, null, null, null, null, null, null, null, null, 11, 'true', 1000477, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015402","max":200000.00,"min":50.00,"scoreRuleId":"2015401","stepId":"1000477","stepName":"A"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015403","max":50.00,"min":0.00,"scoreRuleId":"2015401","stepId":"1000480","stepName":"D"}]', null, '赵王', '新人入职', null, null, 1114502, null, null, 200, 0, 0, 1, '', null, 1000477);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1119913', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1118801', '1208001', '1011602', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'true', '1093003', '2022-11-25 11:02:31', null, '2022-11-25 11:02:36', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015402","max":200000.00,"min":50.00,"scoreRuleId":"2015401","stepId":"1000477","stepName":"A"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015403","max":50.00,"min":0.00,"scoreRuleId":"2015401","stepId":"1000480","stepName":"D"}]', null, '赵王', '新人入职', null, null, 1117108, null, null, 0, 0, 0, null, '', null, null);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1119947', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1118703', '1208001', '1011602', 'scoring', null, null, null, '[{"avatar":"https://static-legacy.dingtalk.com/media/lADPDg7mQqMlUJPNArzNArw_700_700.jpg","empId":"1090071","empName":"杨浩","exUserId":"012625253735848641"}]', null, null, null, 'true', null, null, null, null, null, null, null, null, '2022-11-25 15:28:10', 'false', '1090071', '2022-11-25 15:27:29', null, '2022-11-25 15:28:04', null, null, null, null, null, null, null, null, null, null, null, null, null, 0.000, null, null, null, null, null, null, null, null, null, null, null, null, 2, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015402","max":200000.00,"min":50.00,"scoreRuleId":"2015401","stepId":"1000477","stepName":"A"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015403","max":50.00,"min":0.00,"scoreRuleId":"2015401","stepId":"1000480","stepName":"D"}]', null, '赵王', '新人入职', null, null, 1114502, null, null, 200, 0, 0, null, '', null, null);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1121705', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1120001', '1208001', '1011602', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'true', '1093003', '2022-11-29 16:33:35', null, '2022-11-30 14:11:43', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015402","max":200000.00,"min":50.00,"scoreRuleId":"2015401","stepId":"1000477","stepName":"A"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015403","max":50.00,"min":0.00,"scoreRuleId":"2015401","stepId":"1000480","stepName":"D"}]', null, '赵王', '新人入职', null, null, 1118001, null, null, 0, 0, 0, null, '', null, null);


INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1055705', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1066505', '1207002', '1011602', 'scoring', null, null, null, '[{"empId":"1207002","avatar":"","exUserId":"16613298027913200","empName":"王丽"}]', null, null, null, 'true', null, null, null, null, null, null, null, null, '2022-09-14 09:00:02', 'true', '1093001', '2022-09-13 18:57:18', null, '2022-09-14 09:00:02', null, null, 'true', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'true', 1, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015402","max":200000.00,"min":50.00,"scoreRuleId":"2015401","stepId":"1000477","stepName":"A"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015403","max":50.00,"min":0.00,"scoreRuleId":"2015401","stepId":"1000480","stepName":"D"}]', 1, '王丽', '新人入职', null, null, 0, null, null, 0, 1, 0, null, '', null, null);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1056901', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1067701', '1207002', '1011602', 'confirmed', null, null, null, '[{"empId":"1207002","avatar":"","exUserId":"16613298027913200","empName":"王丽"}]', null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2022-09-26 10:52:02', null, '2022-09-26 18:04:25', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015402","max":200000.00,"min":50.00,"scoreRuleId":"2015401","stepId":"1000477","stepName":"A"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015403","max":50.00,"min":0.00,"scoreRuleId":"2015401","stepId":"1000480","stepName":"D"}]', null, '王丽', '新人入职', null, null, 1066901, null, null, 0, 0, 0, null, '', null, null);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1057206', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1067901', '1207002', '1011602', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090038', '2022-09-27 10:21:33', null, '2022-10-12 14:05:53', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015402","max":200000.00,"min":50.00,"scoreRuleId":"2015401","stepId":"1000477","stepName":"A"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015403","max":50.00,"min":0.00,"scoreRuleId":"2015401","stepId":"1000480","stepName":"D"}]', null, '王丽', '新人入职', null, null, 1029158, null, null, 0, 0, 0, null, '', null, null);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1057706', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1068401', '1207002', '1011602', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090071', '2022-10-09 11:30:04', null, '2022-10-09 11:30:04', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015402","max":200000.00,"min":50.00,"scoreRuleId":"2015401","stepId":"1000477","stepName":"A"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015403","max":50.00,"min":0.00,"scoreRuleId":"2015401","stepId":"1000480","stepName":"D"}]', null, '王丽', '新人入职', null, null, 1028016, null, null, 0, 0, 0, null, '', null, null);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1110811', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1111301', '1207002', '1011602', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'true', '1093017', '2022-11-06 18:25:57', null, '2022-11-06 19:03:20', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015402","max":200000.00,"min":50.00,"scoreRuleId":"2015401","stepId":"1000477","stepName":"A"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015403","max":50.00,"min":0.00,"scoreRuleId":"2015401","stepId":"1000480","stepName":"D"}]', null, '王丽', '新人入职', null, null, 1111201, null, null, 0, 0, 0, null, '', null, null);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1111809', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1112201', '1207002', '1011602', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1093014', '2022-11-08 15:33:11', null, '2022-11-08 15:33:11', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015402","max":200000.00,"min":50.00,"scoreRuleId":"2015401","stepId":"1000477","stepName":"A"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015403","max":50.00,"min":0.00,"scoreRuleId":"2015401","stepId":"1000480","stepName":"D"}]', null, '王丽', '新人入职', null, null, 1112401, null, null, 0, 0, 0, null, '', null, null);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1112602', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1112305', '1207002', '1011602', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'true', '1090071', '2022-11-10 10:19:06', null, '2022-11-17 19:13:53', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015402","max":200000.00,"min":50.00,"scoreRuleId":"2015401","stepId":"1000477","stepName":"A"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015403","max":50.00,"min":0.00,"scoreRuleId":"2015401","stepId":"1000480","stepName":"D"}]', null, '王丽', '新人入职', null, null, 1112301, null, null, 200, 0, 0, null, '', null, null);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1113503', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1112401', '1207002', '1011602', 'confirming', null, null, null, '[{"avatar":"","empId":"1207002","empName":"王丽","exUserId":"16613298027913200"}]', null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1216003', '2022-11-14 09:59:57', null, '2022-11-17 11:12:07', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015402","max":200000.00,"min":50.00,"scoreRuleId":"2015401","stepId":"1000477","stepName":"A"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015403","max":50.00,"min":0.00,"scoreRuleId":"2015401","stepId":"1000480","stepName":"D"}]', null, '王丽', '新人入职', null, null, 1112615, null, null, 200, 0, 0, null, '', null, null);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1113903', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1114104', '1207002', '1011602', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1093017', '2022-11-14 19:10:29', null, '2022-11-14 19:10:29', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015402","max":200000.00,"min":50.00,"scoreRuleId":"2015401","stepId":"1000477","stepName":"A"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015403","max":50.00,"min":0.00,"scoreRuleId":"2015401","stepId":"1000480","stepName":"D"}]', null, '王丽', '新人入职', null, null, 1113703, null, null, 0, 0, 0, null, '', null, null);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1115302', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1112305', '1207002', '1011602', 'scoring', null, null, null, '[{"avatar":"","empId":"1207002","empName":"王丽","exUserId":"16613298027913200"}]', null, null, null, null, null, null, null, null, null, null, null, null, '2023-02-02 17:18:35', 'false', '1090071', '2022-11-17 19:13:57', null, '2023-02-02 17:18:35', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015402","max":200000.00,"min":50.00,"scoreRuleId":"2015401","stepId":"1000477","stepName":"A"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015403","max":50.00,"min":0.00,"scoreRuleId":"2015401","stepId":"1000480","stepName":"D"}]', null, '王丽', '新人入职', null, null, 1112301, null, null, 200, 0, 0, 1, '', null, null);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1115413', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1115602', '1207002', '1011602', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'false', '1090071', '2022-11-18 11:34:16', null, '2022-11-18 11:34:16', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015402","max":200000.00,"min":50.00,"scoreRuleId":"2015401","stepId":"1000477","stepName":"A"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015403","max":50.00,"min":0.00,"scoreRuleId":"2015401","stepId":"1000480","stepName":"D"}]', null, '王丽', '新人入职', null, null, 1115202, null, null, 0, 0, 0, null, '', null, null);


INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1001102', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1058902', '1090071', '1013211', 'drawUpIng', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'true', '1093017', '2022-11-01 16:29:33', null, '2022-11-03 15:23:37', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015402","max":200000.00,"min":50.00,"scoreRuleId":"2015401","stepId":"1000477","stepName":"A"},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2015403","max":50.00,"min":0.00,"scoreRuleId":"2015401","stepId":"1000480","stepName":"D"}]', null, '杨浩', '产品部', null, null, 1070102, null, null, 200, 0, 0, null, 'https://static-legacy.dingtalk.com/media/lADPDg7mQqMlUJPNArzNArw_700_700.jpg', null, null);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1013308', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1022504', '1090071', '1013211', 'scoring', null, null, null, null, 'true', 'true', null, null, null, null, null, null, null, null, null, null, '2022-06-07 20:14:48', 'false', '1100018', '2022-06-07 17:04:29', null, '2022-06-07 20:15:35', null, null, null, null, null, null, null, null, null, null, null, null, null, 0.000, null, null, null, null, null, null, null, null, null, null, null, 'true', 6, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2006011","max":200000.00,"min":40.00,"scoreRuleId":"2005204","stepId":"1000080","stepName":"A","version":0},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2006012","max":40.00,"min":0.00,"scoreRuleId":"2005204","stepId":"1000082","stepName":"C","version":0}]', 0, '杨浩', '产品部', null, null, 17, null, null, 0, 1, 0, null, 'https://static-legacy.dingtalk.com/media/lADPDg7mQqMlUJPNArzNArw_700_700.jpg', null, null);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1013331', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1022510', '1090071', '1013211', 'scoring', null, null, null, null, 'true', 'true', null, null, null, null, null, null, null, null, null, null, '2022-06-07 20:48:52', 'false', '1093014', '2022-06-07 20:35:52', null, '2022-06-07 20:35:53', null, null, 'true', null, null, null, null, null, null, null, null, null, null, 0.000, null, null, null, null, null, null, null, null, null, null, null, 'true', 3, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2006011","max":200000.00,"min":40.00,"scoreRuleId":"2005204","stepId":"1000080","stepName":"A","version":0},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2006012","max":40.00,"min":0.00,"scoreRuleId":"2005204","stepId":"1000082","stepName":"C","version":0}]', 0, '杨浩', '产品部', null, null, 13, null, null, 0, 1, 0, null, 'https://static-legacy.dingtalk.com/media/lADPDg7mQqMlUJPNArzNArw_700_700.jpg', null, null);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1014214', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1023505', '1090071', '1013211', 'scoring', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, '2022-06-09 10:04:35', 'false', '1093014', '2022-06-09 10:01:35', null, '2022-06-13 10:18:32', null, null, null, null, null, null, null, null, null, null, null, null, null, 0.000, null, null, null, null, null, null, null, null, null, null, null, 'true', 3, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2007106","max":200000.00,"min":90.00,"scoreRuleId":"1000008","stepId":"1000080","stepName":"A","version":0},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2007107","max":90.00,"min":70.00,"scoreRuleId":"1000008","stepId":"1000083","stepName":"D","version":0},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2007108","max":70.00,"min":0.00,"scoreRuleId":"1000008","stepId":"2005901","stepName":"E","version":0}]', 0, '杨浩', '产品部', null, null, 16, null, null, 0, 1, 0, null, 'https://static-legacy.dingtalk.com/media/lADPDg7mQqMlUJPNArzNArw_700_700.jpg', null, null);
INSERT INTO perf_evaluate_task_user (id, company_id, task_id, emp_id, org_id, task_status, final_score, original_final_score, evaluation_level, reviewers_json, self_score_flag, manual_score_flag, superior_score_flag, item_score_flag, result_audit_flag, final_self_score, final_peer_score, final_sub_score, final_superior_score, final_item_score, last_score_comment, task_confirm_time, task_score_start_time, is_deleted, created_user, created_time, updated_user, updated_time, final_plus_score, final_subtract_score, public_flag, final_self_plus_score, final_peer_plus_score, final_sub_plus_score, final_superior_plus_score, final_self_subtract_score, final_peer_subtract_score, final_sub_subtract_score, final_superior_subtract_score, final_item_plus_score, final_item_subtract_score, final_item_auto_score, enter_score_flag, original_evaluation_level, distribution_flag, adjust_reason, total_points_num, signature_pic, item_change_user, has_appeal, appeal_receiver_id, cc_emp_ids, in_result_affirm_time, is_publish, version, all_scored, step_id, score_ranges, is_new_emp, emp_name, emp_org_name, appeal_dead_line, eval_table_name, cycle_id, org_name_list, emp_org_id, rule_conf_status, temp_task, org_changed, input_finish_status, avatar, confirm_dead_line, distribution_before_step_id) VALUES ('1014235', 'ece4e403-43aa-47f2-bb19-a0dd18b8e98d', '1023514', '1090071', '1013211', 'confirmed', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'true', '1093014', '2022-06-09 10:28:41', '1093014', '2022-06-09 11:03:54', null, null, 'true', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'true', 0, null, null, '[{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2007106","max":200000.00,"min":90.00,"scoreRuleId":"1000008","stepId":"1000080","stepName":"A","version":0},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2007107","max":90.00,"min":70.00,"scoreRuleId":"1000008","stepId":"1000083","stepName":"D","version":0},{"companyId":{"id":"ece4e403-43aa-47f2-bb19-a0dd18b8e98d"},"id":"2007108","max":70.00,"min":0.00,"scoreRuleId":"1000008","stepId":"2005901","stepName":"E","version":0}]', 0, '杨浩', '产品部', null, null, 14, null, null, 0, 1, 0, null, 'https://static-legacy.dingtalk.com/media/lADPDg7mQqMlUJPNArzNArw_700_700.jpg', null, null);


