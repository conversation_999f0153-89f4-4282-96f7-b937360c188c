create table company
(
    id                          varchar(40)  default '0'                               not null comment '自增id'
        primary key,
    code                        varchar(10)                                            null comment '公司code',
    name                        varchar(512)                                           null comment '公司名称',
    abbr_name                   varchar(20)                                            null comment '公司简称',
    business                    varchar(127)                                           null comment '公司业务方向',
    legal_person                varchar(45)                                            null comment '公司负责人',
    legal_person_mobile         varchar(200)                                           null comment '公司联系人电话',
    legal_person_email          varchar(45)                                            null comment '公司联系人邮箱',
    business_license            varchar(256)                                           null comment '工商执照',
    credit_code                 varchar(50)                                            null comment '社会统一信用代码',
    logo                        varchar(256)                                           null comment '公司LOGO',
    address                     varchar(512)                                           null comment '公司地址',
    scale                       varchar(32)                                            null comment '企业规模',
    city                        varchar(255)                                           null comment '公司所在城市',
    license                     varchar(50)                                            null comment '开户许可证',
    license_img                 varchar(255)                                           null comment '开户许可证',
    head_name                   varchar(50)                                            null comment '法人名称',
    head_id_num                 varchar(50)                                            null comment '法人证件号码',
    head_id_type                varchar(50)                                            null comment '法人证件类型',
    head_img1                   varchar(255)                                           null comment '法人证件正面',
    head_img2                   varchar(255)                                           null comment '法人证件反面',
    agent_img1                  varchar(255)                                           null comment '经办人身份证正面',
    agent_img2                  varchar(255)                                           null comment '经办人身份证反面',
    approval_status             varchar(10)                                            null comment '审核状态（review:审核中，pass:通过，unpass:不通过）',
    approval_memo               varchar(200)                                           null comment '审核备注',
    app_perm                    text                                                   null comment 'app权限json格式',
    open_push                   varchar(10)                                            null comment '开启推送（true|false）',
    sync_type                   varchar(10)                                            null comment '同步类型（auto:自动,manu:手动）',
    ding_corp_id                varchar(200)                                           null comment '钉钉企业id',
    ding_agent_id               varchar(200)                                           null comment '钉钉授权方应用id',
    ding_space_id               varchar(20)                                            null comment '企业钉盘id',
    we_corp_id                  varchar(200)                                           null comment 'welink企业id',
    status                      varchar(10)  default 'using'                           null comment '状态using：正常使用中，close：删除',
    company_type                varchar(10)                                            null comment '公司类型（review:待审核-默认不需要审核,simple-极简版，enterprise-企业版）',
    emp_limit                   int                                                    null comment '人数上限',
    perm_start_time             date                                                   null comment '权限开始日期',
    perm_end_time               date                                                   null comment '权限结束日期',
    target_num                  int                                                    null comment '目标数量',
    action_num                  int                                                    null comment '成果数量',
    created_time                datetime                                               null comment '记录创建时间',
    updated_time                datetime                                               null comment '记录更新时间',
    indicator_num               int                                                    null comment '指标数量',
    enter_on_scoring            int          default 0                                 null comment '评分阶段可以录入完成值 0=关, 1=打开',
    result_input_send_msg       varchar(10)  default 'true'                            null comment '是否发送录入完成值代办',
    sup_is_can_auto_score_item  int          default 0                                 null comment '上级是否能看自动评分指标明细',
    can_res_submit_input_finish int          default 0                                 null comment '自动评分指标能多次录入完成值',
    version                     int          default 0                                 null comment '版本号',
    order_from                  int          default 0                                 null comment '订单来源 0=默认, 1 = hrm,2 =mark_place, 3=',
    eval_emp_on_log_auth        int(1)       default 1                                 null comment '被考核人打开日志权限',
    enter_on_ding_msg           int(1)       default 1                                 null comment '钉钉代办开关',
    log_auth_set                varchar(100) default 'taskEmp,participant,createdUser' null comment '日志权限设置，taskEmp=被考核人,participant=参与人,createdUser=创建人',
    ignore_vacancy_manager      int          default 0                                 null comment '忽略空缺主管',
    emp_entry_auto_open         int(1)       default 0                                 null comment '员工自动开启开关 默认0=关闭，1=开启',
    app_version                 varchar(174) default '1009002'                         null comment '当前企业的应用版本',
    last_version                varchar(174) default '1009002'                         null comment '企业可以升级的版本',
    open_org_performance        int          default 0                                 null comment '组织绩效开关 默认=0关闭、1=开启',
    constraint idx_c_ding_corp_id
        unique (ding_corp_id)
);


-- auto-generated definition
create table emp_ref_org
(
    id           varchar(50) not null comment 'id'
        primary key,
    company_id   varchar(50) null comment '公司id',
    emp_id       varchar(50) null comment '员工id',
    org_id       varchar(50) null comment '部门id',
    ref_type     varchar(10) null comment '关联类型(org:员工关联部门,manager:部门负责人)',
    is_main_org  varchar(10) null comment '是否为主部门：true/false',
    created_time datetime    null comment '创建时间',
    updated_time datetime    null comment '更新时间'
)
    comment '员工关联部门表';
create index idx_empid_orgid
    on emp_ref_org (emp_id, org_id);
create index idx_ero_cid
    on emp_ref_org (company_id);
create index idx_ero_oid
    on emp_ref_org (org_id);
create index idx_orgid_reftype_empid
    on emp_ref_org (org_id, ref_type, emp_id);


-- auto-generated definition
create table employee_base_info
(
    id              varchar(50)                      null,
    employee_id     varchar(50)                      not null comment '主键员工ID'
        primary key,
    company_id      varchar(50)                      null comment '公司ID',
    org_id          varchar(50)                      null comment '部门ID',
    account_id      varchar(50)                      null comment '账号ID',
    name            varchar(128) collate utf8mb4_bin null comment '姓名',
    nickname        varchar(128)                     null comment '花名',
    sex             varchar(10)                      null comment '性别',
    mobile          varchar(200)                     null,
    id_type         varchar(50)                      null comment '证件类型',
    id_num          varchar(50)                      null comment '证件号码',
    type            varchar(50)                      null comment '员工类型（正式-regular；试用-probation；实习-practice；兼职-parttimer）',
    status          varchar(50)                      null comment '员工状态（在职-on_the_job；待离职-pending_leave；已离职-leave）',
    entry_date      date                             null comment '入职日期',
    avatar          varchar(256)                     null comment '头像链接地址',
    birthday        date                             null comment '公历出生日期',
    create_id       varchar(50)                      null comment '创建人ID',
    created_time    datetime(6)                      null comment '创建时间',
    updated_time    datetime(6)                      null comment '更新时间',
    name_chinese    varchar(256)                     null comment '查询关键字',
    hierarchy       varchar(128)                     null comment '职级',
    nation          varchar(50)                      null comment '民族',
    native_place    varchar(128)                     null comment '籍贯',
    political_party varchar(32)                      null comment '政治面貌',
    house_reg_type  varchar(32)                      null comment '户籍性质',
    house_reg_addr  varchar(300)                     null comment '户籍地址',
    living_addr     varchar(300)                     null comment '现住地址',
    office_addr     varchar(300)                     null comment '办公地址',
    highest_edu     varchar(64)                      null comment '最高学历',
    marital_status  varchar(32)                      null comment '婚姻状态',
    child_status    varchar(32)                      null comment '子女状态',
    linkman_name    varchar(100)                     null comment '联系人姓名',
    linkman_type    varchar(50)                      null comment '联系人关系',
    linkman_tel     varchar(100)                     null comment '联系人电话',
    is_delete       varchar(10) default ''           null comment '删除标识；否，是',
    source          varchar(30)                      null comment '多语言字段',
    photo           varchar(255)                     null comment '照片',
    memo            varchar(255)                     null comment '备注',
    qy_user_id      varchar(50)                      null comment '企业微信用户id',
    ding_user_id    varchar(50)                      null comment '钉钉用户id',
    we_user_id      varchar(50)                      null comment 'welink用户id',
    open_id         varchar(255)                     null comment '第三方（钉钉，企微等）平台的open id',
    union_id        varchar(255)                     null comment '第三方（钉钉，企微等）平台的union id',
    state_code      varchar(10)                      null comment '手机号对应的国家号',
    mobile_num      varchar(20)                      null comment '用户的手机号,需要在开发者后台申请',
    email           varchar(50)                      null comment '用户的个人邮箱',
    locale          varchar(30)                      null comment '多语言的语言代码，用中划线',
    ding_role_name  varchar(50)                      null comment '钉钉角色名称',
    ding_manager_id varchar(255)                     null comment '钉钉直属主管id',
    version         int         default 0            null comment '新版 1=旧版，2=新版',
    last_login_time datetime                         null,
    jobnumber       varchar(50) default ''           null comment '工号',
    post            varchar(50) default ''           null comment '岗位'
);

create index company_emp
    on employee_base_info (employee_id, company_id);

create index idx_company_status_delete
    on employee_base_info (company_id);

create index idx_companyid_isdelete
    on employee_base_info (company_id, is_delete);

create index idx_ebi_cid
    on employee_base_info (status);

create index idx_ebi_del
    on employee_base_info (is_delete);


-- auto-generated definition
create table emp_organization
(
    id            varchar(50)   null,
    org_id        varchar(50)   not null comment '部门ID'
        primary key,
    org_code      varchar(1000) null comment '部门代码路径，所有父节点加上自身节点的节点ID，采用|号拼接生成',
    company_id    varchar(50)   not null comment '公司ID',
    org_name      varchar(128)  null comment '部门名称',
    parent_org_id varchar(50)   null comment '父级部门ID',
    manager_id    varchar(50)   null comment '负责人ID',
    executor_id   varchar(50)   null comment '执行人id',
    status        varchar(50)   null comment '部门状态:有效；无效',
    type          varchar(50)   null comment '部门类型:顶级节点部门；普通部门',
    name_chinese  varchar(512)  null comment '搜索关键字',
    created_time  datetime      null comment '创建时间',
    updated_time  datetime      null comment '更新时间',
    sort_num      bigint(11)    null comment '排序',
    top_report_id varchar(50)   null comment '最高汇报人ID',
    staff_num     int(20)       null comment '人员编制',
    org_num       varchar(50)   null comment '部门编码',
    build_time    datetime      null comment '成立日期',
    memo          varchar(255)  null comment '备注',
    qy_org_id     varchar(50)   null comment '企业微信对应id',
    ding_org_id   varchar(50)   null comment '钉钉对应id',
    we_org_id     varchar(50)   null comment 'welink对应id',
    version       int default 0 null comment '版本号'
)
    comment '部门';

create index idx_companyid_orgname
    on emp_organization (company_id, org_name);

create index idx_eo_cid
    on emp_organization (company_id);

create index idx_eo_sta
    on emp_organization (status);

create table admin_manage_org
(
    company_id   varchar(50)            not null comment '企业id',
    org_id       varchar(50) default '' null comment '部门id',
    admin_emp_id varchar(50) default '' null comment '管理员id',
    created_user varchar(50) default '' null,
    updated_user varchar(50) default '' null,
    created_time datetime               null,
    updated_time datetime               null,
    version      int         default 0  null comment '版本号',
    constraint uk_admin_org
        unique (admin_emp_id, org_id)
)
    comment '管理员管理的部门表';

create index idx_company_id
    on admin_manage_org (company_id);

create index idx_org_id
    on admin_manage_org (org_id);


-- auto-generated definition
create table system_admin_set
(
    id               varchar(50)              not null comment 'id'
        primary key,
    company_id       varchar(50)              null comment '公司id',
    emp_id           varchar(50)              null comment '员工id',
    status           varchar(10)              null comment 'valid：有效，invalid：无效',
    created_time     datetime                 null comment '创建时间',
    updated_time     datetime                 null comment '更新时间',
    menu_purview     text collate utf8mb4_bin null comment '菜单权限',
    manage_purview   text collate utf8mb4_bin null comment '管理权限',
    template_purview text                     null,
    operate_purview  text                     null comment '操作权限',
    admin_type       varchar(5)               null comment '主：main;子：child',
    created_user     varchar(50)              null,
    updated_user     varchar(50)              null,
    version          int default 0            null comment '版本字段'
);

create index company_id
    on system_admin_set (company_id);

