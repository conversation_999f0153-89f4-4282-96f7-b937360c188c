create table perf_evaluate_task_user
(
    id                            varchar(50)                      not null
        primary key,
    company_id                    varchar(50)                      null comment '公司id',
    task_id                       varchar(50)                      null comment '考核任务id',
    emp_id                        varchar(50)                      null comment '被考核用户id',
    org_id                        varchar(50)                      null,
    task_status                   varchar(50)                      null comment '任务状态',
    final_score                   decimal(11, 2)                   null comment '考核最终评分',
    original_final_score          decimal(11, 2)                   null comment '原考核分数',
    evaluation_level              varchar(20)                      null comment '考评等级',
    reviewers_json                text                             null comment '流程责任人列表',
    self_score_flag               varchar(10)                      null comment '自评完成标识，true标识已完成',
    manual_score_flag             varchar(10)                      null comment '互评完成标识，true表示已完成',
    superior_score_flag           varchar(10)                      null comment '上级评分完成标识',
    item_score_flag               varchar(10)                      null comment '指标评分完成标识',
    result_audit_flag             varchar(10)                      null comment '最终结果审核完成标识',
    final_self_score              decimal(10, 3)                   null comment '自评最终得分',
    final_peer_score              decimal(10, 3)                   null comment '同级互评最终得分',
    final_sub_score               decimal(10, 3)                   null comment '下级互评最终得分',
    final_superior_score          decimal(10, 3)                   null comment '上级评分最终得分',
    final_item_score              decimal(10, 3)                   null comment '指定指标评分人最终得分',
    last_score_comment            varchar(255)                     null comment '最近一条评分审核修改理由',
    task_confirm_time             datetime                         null comment '任务确认时间',
    task_score_start_time         datetime                         null comment '进入评分时间',
    is_deleted                    varchar(10)                      null comment '是否删除',
    created_user                  varchar(50)                      null comment '创建用户',
    created_time                  datetime                         null comment '创建时间',
    updated_user                  varchar(50)                      null comment '修改用户',
    updated_time                  datetime                         null comment '修改时间',
    final_plus_score              decimal(10, 2)                   null comment '最终加分',
    final_subtract_score          decimal(10, 2)                   null comment '最终减分',
    public_flag                   varchar(10)                      null comment '是否已公示',
    final_self_plus_score         decimal(10, 3)                   null comment '最终自评加分',
    final_peer_plus_score         decimal(10, 3)                   null comment '最终同级互评加分',
    final_sub_plus_score          decimal(10, 3)                   null comment '最终下级互评加分',
    final_superior_plus_score     decimal(10, 3)                   null comment '最终上级加分',
    final_self_subtract_score     decimal(10, 3)                   null comment '最终自评加减分',
    final_peer_subtract_score     decimal(10, 3)                   null comment '最终同级互评减分',
    final_sub_subtract_score      decimal(10, 3)                   null comment '最终下级互评减分',
    final_superior_subtract_score decimal(10, 3)                   null comment '最终上级减分',
    final_item_plus_score         decimal(10, 3)                   null comment '指定评分人最终加分',
    final_item_subtract_score     decimal(10, 3)                   null comment '指定评分人最终减分',
    final_item_auto_score         decimal(10, 3)                   null comment '自动评分指标最终分数',
    enter_score_flag              varchar(10)                      null comment '已发起评分操作标识',
    original_evaluation_level     varchar(20)                      null comment '原始考评等级',
    distribution_flag             varchar(10)                      null comment '是否经过正态分布',
    adjust_reason                 varchar(255)                     null comment '调整绩效结果理由',
    total_points_num              decimal(10, 2)                   null comment '任务总积分',
    signature_pic                 varchar(500) collate utf8mb4_bin null comment '签名图片',
    item_change_user              varchar(50)                      null comment '指标变更人',
    has_appeal                    varchar(10) collate utf8mb4_bin  null comment '是否有申诉；true/false',
    appeal_receiver_id            varchar(50) collate utf8mb4_bin  null comment '申诉受理人id',
    cc_emp_ids                    text                             null comment '评分抄送人、公示抄送人取值地方',
    in_result_affirm_time         date                             null comment '进入结果确认的时间',
    is_publish                    varchar(10) default 'true'       null comment '某个任务下的某个被考核人是否可公示',
    version                       int         default 0            null comment '版本号',
    all_scored                    varchar(10)                      null comment '所有评分人都评分完成',
    step_id                       int                              null comment '等阶id',
    score_ranges                  text                             null comment '每个人都带着等级分值组走',
    is_new_emp                    int         default 0            null comment '是否新人任务',
    emp_name                      varchar(50) default ''           null comment '员工名字',
    emp_org_name                  varchar(50) default ''           null comment '员工考核部门名',
    appeal_dead_line              varchar(50)                      null comment '结果申诉截止时间',
    eval_table_name               varchar(50)                      null comment '考核表名称',
    cycle_id                      bigint                           null comment '周期id',
    org_name_list                 text                             null,
    emp_org_id                    text                             null,
    rule_conf_status              int         default 0            null comment '考核表配置进度, 0=未配置, 100 = 进行中, 101=进行中有异常  200=完成ok ',
    temp_task                     int         default 1            null comment '是否从模板创建任务,旧数据标识',
    org_changed                   int         default 0            null comment '标记考核人员部门已变更 1=已变更, 0=无变更, 3(11)=有变更已处理',
    input_finish_status           int(1)                           null comment '完成值录入情况：0=不需要录入、1=未录入、2=部分录入、3=全部录入',
    avatar                        varchar(256)                     null comment '头像链接地址',
    confirm_dead_line             varchar(50)                      null comment '指标确认截止时间',
    distribution_before_step_id   int                              null comment '正态分布之前的stepId'
) comment '考核任务-用户信息' charset = utf8mb4;

create index company_emp_perf_evaluate_task_user
    on perf_evaluate_task_user (company_id, emp_id, is_deleted);

create index company_id_perf_evaluate_task_user
    on perf_evaluate_task_user (company_id);

create index idx_cycle_id_perf_evaluate_task_user
    on perf_evaluate_task_user (cycle_id);

create index idx_task_id_perf_evaluate_task_user
    on perf_evaluate_task_user (task_id);


drop table if exists emp_eval_table;
create table emp_eval_table
(
    company_id       varchar(50)            not null comment '公司id',
    id               varchar(50)            not null comment '主键:考核表id' primary key,
    emp_id           varchar(50)            not null comment '外键员工id:emloyee_info.employee_id',
    is_default       int          default 0 comment '是否员工的默认考核表',
    rule_name        varchar(250) default '' comment '考核表名字',
    evaluate_type    varchar(10)            not null comment '评分流程类型 ： simple-简易流程，360-360°考核流程，custom-自定义流程',
    type_weight_conf varchar(128) default '' comment '指标类别权重 json',
    score_value_conf varchar(128) default '' comment '分值的设定 json',
    s3_self_rater    varchar(128) default '' comment '360或简易 自评人',
    s3_peer_rater    text comment '同级互评人 json',
    s3_sub_rater     text comment '同级互评人 json',
    s3_super_rater   text comment '360或简易 上级人 json',
    score_view       text                   null comment '可见范围配置 json',
    index_raters     text comment '评分人索引,用于员工列表的显示,编辑时更新索引',
    indicator_cnt    int          default 0 null comment '指标数统计',
    comment_conf     varchar(225)           null comment '评语与总结配置',
    is_deleted       varchar(5)   default 'false' comment '是否删除',
    created_user     varchar(50)            null comment '创建用户',
    created_time     datetime               null comment '创建时间',
    updated_user     varchar(50)            null comment '修改用户',
    updated_time     datetime               null comment '修改时间',
    version          int          default 0 null comment '版本号',
    key idx_companyId_empId_emp_eval_table (company_id, emp_id)
) comment '员工的考核表,一个员工可以有多个考核表' charset = utf8mb4;





