{"updatedTime": 1703666587000, "fieldJson": "[{\"formulaFieldValue\":0,\"formulaFieldName\":\"考核任务得分\",\"companyFieldId\":\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\",\"isSystemField\":\"false\",\"label\":\"考核任务得分\",\"value\":0,\"yVal\":true},{\"label\":\"×\",\"value\":\"*\",\"symbol\":true},{\"label\":0,\"value\":0},{\"label\":\".\",\"value\":\".\"},{\"label\":0,\"value\":0},{\"label\":1,\"value\":1}]", "scoreRanges": [{"coeffType": 1, "createdTime": "2024-01-02 10:23:00", "levelDefType": null, "max": 200000.0, "min": 90.0, "minAppendEqual": 0, "perfCoefficient": "1", "place": 4, "rangeFrom": 0, "rangeTo": 0, "rate": -1.0, "scoreRuleId": "2051404", "scoreRuleName": null, "snapId": "1000201", "stepId": "2004001", "stepName": "S"}, {"coeffType": 1, "createdTime": "2024-01-02 10:23:00", "fieldJson": "[{\"formulaFieldValue\":0,\"formulaFieldName\":\"考核任务得分\",\"companyFieldId\":\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\",\"isSystemField\":\"false\",\"label\":\"考核任务得分\",\"value\":0,\"yVal\":true},{\"label\":\"×\",\"value\":\"*\",\"symbol\":true},{\"label\":0,\"value\":0},{\"label\":\".\",\"value\":\".\"},{\"label\":0,\"value\":0},{\"label\":1,\"value\":1}]", "levelDefType": null, "max": 90.0, "min": 70.0, "minAppendEqual": 0, "perfCoefficient": "0.8", "place": 4, "rangeFrom": 0, "rangeTo": 0, "rate": -1.0, "scoreRuleId": "2051404", "scoreRuleName": null, "snapId": "1000201", "stepId": "1000005", "stepName": "A"}, {"coeffType": 1, "createdTime": "2024-01-02 10:23:00", "fieldJson": "[{\"formulaFieldValue\":0,\"formulaFieldName\":\"考核任务得分\",\"companyFieldId\":\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\",\"isSystemField\":\"false\",\"label\":\"考核任务得分\",\"value\":0,\"yVal\":true},{\"label\":\"×\",\"value\":\"*\",\"symbol\":true},{\"label\":0,\"value\":0},{\"label\":\".\",\"value\":\".\"},{\"label\":0,\"value\":0},{\"label\":1,\"value\":1}]", "levelDefType": null, "max": 70.0, "min": 0.0, "minAppendEqual": 0, "perfCoefficient": "0.6", "place": 4, "rangeFrom": 0, "rangeTo": 0, "rate": -1.0, "scoreRuleId": "2051404", "scoreRuleName": null, "snapId": "1000201", "stepId": "1000006", "stepName": "B"}], "rankScope": {"type": 1, "scope": -1}, "cycleId": "1835401", "updatedUser": "", "version": 0, "excludeEmps": [], "levelDefType": 1, "coeffType": 1, "companyId": {"id": "5a031297-1b38-48ae-bc82-375849835203"}, "system": 0, "isDeleted": "false", "name": "全公司使用", "createdTime": 1703666587000, "id": "1000001", "place": 4, "perfCoefficient": "考核任务得分*0.01", "onCycleType": 63, "ruleId": "2051404", "createdUser": "12627917-53e2-4604-9665-5ad204778882"}