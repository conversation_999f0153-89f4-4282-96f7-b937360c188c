package cn.com.seendio.polaris.code;//package com.quick.xo.erp.dao.mapper;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.CaseFormat;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.dao.AutoDaoImpl;
import org.lufei.ibatis.mapper.BaseAutoMapper;
import org.lufei.ibatis.tool.Tool;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by lufei on 2017/9/12.
 */
public class UserCoder {

    protected TestContext context;
    protected BaseAutoMapper mapper;
    protected AutoDaoImpl baseDao;
    private String a;

    @Before
    public void setUp() throws Exception {
        context = new TestContext("configuration.xml");
        context.init(BaseAutoMapper.class);
        mapper = context.getMapper(BaseAutoMapper.class);
        baseDao = new AutoDaoImpl();
        baseDao.setAutoMapper(mapper);
    }

    public static void main(String[] args) {
        System.out.println("xxxx");
    }

    @Test
    public void col2Pojo() throws Exception {
        // buyer_profit buyer_contact
        String table = "employee_core";
        List<Map> maps = mapper.nativeSelectAll(Map.class, "SHOW FULL COLUMNS FROM  " + table, null);
        // List<Map> maps = mapper.selectBySql("SHOW FULL COLUMNS FROM train_course_directory_file", null);
        List<Field> list = maps.stream().map(pojo -> {
            String type = pojo.get("type").toString();
            if(type.contains("varchar") || type.contains("text") || type.startsWith("char")) {
                type = String.class.getSimpleName();
            }
            if(type.startsWith("bigint")) {
                type = Long.class.getSimpleName();
            }
            if(type.startsWith("tinyint")) {
                type = Integer.class.getSimpleName();
            }
            if(type.startsWith("int")) {
                type = Integer.class.getSimpleName();
            }

            if(type.contains("timestamp")) {
                type = Date.class.getSimpleName();
            }
            if(type.contains("date")) {
                type = Date.class.getSimpleName();
            }
            if(type.contains("bit")) {
                type = Boolean.class.getSimpleName();
            }
            if(type.contains("decimal")) {
                type = BigDecimal.class.getSimpleName();
            }
            String col = pojo.get("field").toString();
            String field = CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, col);
            Field filed = new Field(col, field, pojo.get("comment").toString(), type);
            return filed;
        }).collect(Collectors.toList());
        System.out.println("class:" + CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, table));

        // list.forEach(field -> {
        //     System.out.print(String.format("p.`%s`= a.`%s`,", field.getCol(), field.getCol()));
        // });

        System.out.println();
        list.forEach(field -> {
            System.out.println(field.toString());
        });
        System.out.println("{");
        list.forEach(field -> {
            System.out.println(field.jsonString());
        });
        System.out.println("}");
        context.commit();
    }


    @Test
    public void pojo2Table() throws Exception {

        final Class cls = String.class;//换成DO类
        final String table = Tool.toUnderLine(cls.getSimpleName());
        StringBuilder createStr = new StringBuilder(String.format(" CREATE TABLE `%s` (", table));
        createStr.append("\n");
        List<java.lang.reflect.Field> fields = Tool.getInstanceFields(cls);
        fields.forEach(field -> {
            final String name = field.getName();
            final String underLine = Tool.toUnderLine(name);
            String col = null;
            if(field.getType() == String.class) {
                col = String.format("`%s` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL,", underLine);
            } else if(field.getType() == Long.class) {
                col = String.format("`%s` bigint(20) DEFAULT NULL,", underLine);
            } else if(field.getType() == Integer.class) {
                col = String.format("`%s` int(10)  DEFAULT NULL,", underLine);
            } else if(field.getType() == BigDecimal.class) {
                col = String.format("`%s` decimal(6,2)  DEFAULT NULL,", underLine);
            } else if(field.getType() == Boolean.class) {
                col = String.format("`%s` bit(1)  DEFAULT NULL,", underLine);
            } else {
                col = String.format("`%s` text  DEFAULT NULL,", JSONObject.toJSON(underLine).toString());
            }
            createStr.append(col).append("\n");

        });
        createStr.deleteCharAt(createStr.lastIndexOf(","));
        createStr.append(") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;");
        System.out.println(createStr);

    }


}
