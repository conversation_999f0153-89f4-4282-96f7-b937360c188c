package cn.com.seendio.polaris.construct;

import cn.com.polaris.kpi.temp.CycleTypeEnum;

import java.beans.ConstructorProperties;

/**
 * <AUTHOR> lufei
 * @date 2022/3/6 8:36 上午
 */
public class CycleValueT {
    private Integer year;
    private String type;
    private Integer value;

    @ConstructorProperties ({"year", "type", "value"})
    public CycleValueT(Integer year, String type, Integer value) {
        CycleTypeEnum.check(type, value);
        this.year = year;
        this.type = type;
        this.value = value;
    }
}
