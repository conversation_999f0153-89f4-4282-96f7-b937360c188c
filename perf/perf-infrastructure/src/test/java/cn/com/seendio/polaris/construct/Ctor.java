package cn.com.seendio.polaris.construct;


import org.apache.poi.ss.formula.functions.T;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> lufei
 * @date 2022/3/6 8:40 上午
 */
public class Ctor {
    // private Constructor cur;
    // private Map<String, Ctor> paramCtors;//参数的
    // private Map<Integer, Ctor> paramIdxCtors = new HashMap<>();//下标参数的
    //
    // public Ctor(Constructor cur) {
    //     this.cur = cur;
    // }
    //
    // public static Ctor createCtor(Class type) {
    //     this.cur = getCtor(type);
    //     final Class<?>[] parameterTypes = cur.getParameterTypes();
    //
    //     for (int i = 0; i < parameterTypes.length; i++) {
    //         paramIdxCtors.put(i, parameterTypes[i]);
    //     }
    // }
    //
    // public void newObj(Object[] args) {
    //     try {
    //         final Object o = cur.newInstance(args);
    //     } catch (Exception e) {
    //         e.printStackTrace();
    //     }
    //     this.cur = cur;
    // }
    //
    //
    // public static Ctor buildCtor(Class<?> type) {
    //     Constructor<?> curCtor = parseCurCtor(type);
    //     if(curCtor.getParameterTypes().length == 0) {
    //
    //     }
    // }

}
