package cn.com.seendio.polaris.code;

import cn.hutool.http.HtmlUtil;
import org.junit.Test;

public class HtmlTest {
    @Test
    public void cleanHtmlTag() {

        String removeHtmlTag = HtmlUtil.cleanHtmlTag("<span class=\"atag\" aid=\"d5f1acee-f639-4271-9d9f-b78b5778dedf\" uk=\"41aa2131-df46-45d9-9d4c-a0018bb78075\"\n" +
                "      contenteditable=\"false\">@涂丽华&nbsp;</span>");
        System.out.println(removeHtmlTag);
    }

    @Test
    public void removeHtmlTag() {
        String removeHtmlTag = HtmlUtil.removeHtmlTag("键盘控飞、支持键盘控制飞机前进、后退、转向等常规操作<span class=\"atag\" aid=\"6085ae2f-2536-4df9-9925-e859f066ee04\" uk=\"e1b94800-2e62-43ad-a699-ed17a36ccc9a\" contenteditable=\"false\">@王伟&nbsp;</span>\u200B", "span","div");
        System.out.println(removeHtmlTag);

        String removeHtmlTag2 = HtmlUtil.removeHtmlTag("盘外ASIN属性聚类(Group、SKU、产品线)<span class=\"atag\" aid=\"3570c4b1-471a-4ffb-9f1f-395c33fbead6\" uk=\"4eccb2d6-2f33-4865-a84a-390ee2fe5a4f\" contenteditable=\"false\">@陈丹阳&nbsp;</span>盘外ASIN属性聚类(Group、SKU、产品线)","span","div");
        System.out.println(removeHtmlTag2);
    }

    @Test
    public void removeHtmlTagNull() {
        String removeHtmlTag = HtmlUtil.removeHtmlTag(null);
        System.out.println(removeHtmlTag);
    }
}
