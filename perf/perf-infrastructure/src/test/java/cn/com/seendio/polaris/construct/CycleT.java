package cn.com.seendio.polaris.construct;

import cn.com.polaris.kpi.eval.CycleValue;
import cn.com.polaris.kpi.temp.CycleId;
import cn.hutool.core.lang.Assert;
import com.polaris.sdk.type.TenantId;

import java.beans.ConstructorProperties;

/**
 * <AUTHOR> luf<PERSON>
 * @date 2022/3/6 8:35 上午
 */
public class CycleT {
    private CycleId id;
    private TenantId tenantId;
    private CycleValue cycleValue;
    private Integer empCnt = 0;//周期里里面的被考核人数量
    private Long cycleStart;//周期开始时间点
    private Long cycleEnd;//周期结束时间点

    //考核周期类型，和考核时间段
    @ConstructorProperties ({"tenantId", "cycleValue"})
    public CycleT(TenantId tenantId, CycleValue cycleValue) {
        Assert.notNull(tenantId);
        Assert.notNull(cycleValue);
        this.tenantId = tenantId;
        this.cycleValue = cycleValue;
    }
}
