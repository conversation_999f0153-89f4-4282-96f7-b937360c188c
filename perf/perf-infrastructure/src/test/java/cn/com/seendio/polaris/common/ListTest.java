package cn.com.seendio.polaris.common;

import com.alibaba.fastjson.JSONObject;
import org.junit.Test;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> lufei
 * @date 2020/10/1 11:26 PM
 */
public class ListTest {
    @Test
    public void name() throws Exception {
        Class pojo = new TestBean().getClass();
        // 首先得到pojo所定义的字段
        Field[] fields = pojo.getDeclaredFields();
        for (Field curField : fields) {
            // 设置字段可访问（必须，否则报错）
            curField.setAccessible(true);

            Class<?> curFieldType = curField.getType();
            // 集合List元素
            Class listFieldActualType = getFieldActualType(curField);
            System.out.println(listFieldActualType);
        }
    }

    @Test
    public void test1() throws Exception {

        TestBean testBean = new TestBean();
        Class pojo = testBean.getClass();
        // 首先得到pojo所定义的字段
        Field[] fields = pojo.getDeclaredFields();
        for (Field curField : fields) {
            // 设置字段可访问（必须，否则报错）
            curField.setAccessible(true);
            Class<?> curFieldType = curField.getType();
            if (isSet(curField)) {
                // 集合List元素
                Class listFieldActualType = getFieldActualType(curField);
                List list = JSONObject.parseArray("[{}]", listFieldActualType);
                curField.set(testBean, list);
            }
        }
    }

    public Boolean initSetField(Field curField) throws InstantiationException, IllegalAccessException {
        Class<?> curFieldType = curField.getType();
        return curFieldType.equals(Set.class);
    }

    public Boolean isSet(Field curField) throws InstantiationException, IllegalAccessException {
        Class<?> curFieldType = curField.getType();
        return curFieldType.equals(Set.class);
    }

    public Class getFieldActualType(Field curField) throws InstantiationException, IllegalAccessException {
        Class<?> curFieldType = curField.getType();
        if (curFieldType.equals(List.class) || curFieldType.equals(Set.class)) {
            // 当前集合的泛型类型
            Type genericType = curField.getGenericType();
            if (null == genericType) {
                return null;
            }
            if (genericType instanceof ParameterizedType) {
                ParameterizedType pt = (ParameterizedType) genericType;
                // 得到泛型里的class类型对象
                Class<?> actualTypeArgument = (Class<?>) pt.getActualTypeArguments()[0];
                return actualTypeArgument;
            }
        }
        return null;
    }

    @Test
    public void json() throws Exception {
        // JSONObject.parse
    }
}
