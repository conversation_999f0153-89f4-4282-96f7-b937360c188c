package cn.com.seendio.polaris.config;// package cn.com.seendio.polaris.config;
//
// import com.alibaba.fastjson.JSONObject;
// import junit.framework.TestCase;
// import org.apache.shardingsphere.sql.parser.core.SQLParseKernel;
// import org.apache.shardingsphere.sql.parser.core.rule.registry.ParseRuleRegistry;
// import org.apache.shardingsphere.sql.parser.sql.statement.SQLStatement;
// import org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement;
//
// /**
//  * <AUTHOR> lufei
//  * @date 2021/3/20 23:33
//  */
// public class SQLParseTest extends TestCase {
//     public void testName() {
//         String sql = "SELECT per.per_additional_version, per.accounting_no, per.org_name, " +
//                 "per.type_cn, per.employee_id, per.post_id, per.history, " +
//                 "per.org_id, per.supporting_elderly, per.housing_rent, per.post_name, per.continuing_education, per.housing_loan_interest, per.name, per.tenant_id, per.accounting_year, per.children_education, per.id, per.serious_illness_treatment, per.id_num" +
//                 " FROM v_salary_per_additional as per " +
//                 "WHERE per.tenant_id = ? AND per.accounting_no = ? AND per.per_additional_version = 0";
//         SQLStatement result = new SQLParseKernel(ParseRuleRegistry.getInstance(), "MySQL", sql).parse();
//         final SelectStatement result1 = (SelectStatement) result;
//         System.out.println(result1.getTables());
//     }
//
//     public void testName2() {
//
//         String sql = "SELECT tp.late_minutes, tp.emp_id, tp.leave_early_times, tp.id_card_no, tp.err_msg, tp.leave14_days, tp.work_out_hours, tp.work_rest_hours, tp.leave12_days, tp.annual_leave_days, tp.rest_days, tp.leave_early_minutes, tp.post, tp.gmt_update, tp.in_leak_times, tp.big_late_times, tp.big_late_minutes, tp.id, tp.absenteeism_hours, tp.sick_leave_hours, tp.`group`, tp.matter_leave_days, tp.leave13_days, tp.accounting_no, tp.org, tp.leave10_days, tp.out_leak_times, tp.absenteeism_days, tp.wor_overtime_hours, tp.will_work_rest1, tp.emp_no, tp.will_work_rest2, tp.will_work_rest3, tp.gmt_create, tp.version, tp.due_days, tp.late_times, tp.absenteeism_late_days, tp.due_minutes, tp.err_code, tp.name, tp.tenant_id, tp.leave11_days\n" +
//                 // "FROM attend_month_report_tmp as tp " +
//                 "WHERE (tp.tenant_id ='2' AND tp.accounting_no = '2')";
//         SQLStatement result = new SQLParseKernel(ParseRuleRegistry.getInstance(), "MySQL", sql).parse();
//         final SelectStatement result1 = (SelectStatement) result;
//         System.out.println(result1.getTables());
//     }
//
//     public void testInsertSelect() {
//
//         String sql = "INSERT INTO attend_salary (id, tenant_id, emp_id, accounting_no,  agroup, org, emp_no, post, due_days, rest_days, due_minutes, late_times, late_minutes, big_late_times, big_late_minutes, absenteeism_late_days, leave_early_times, leave_early_minutes, in_leak_times, out_leak_times, absenteeism_days, absenteeism_hours, work_out_hours, annual_leave_days, matter_leave_days, sick_leave_hours, work_rest_hours, leave14_days, leave13_days, leave12_days, leave11_days, leave10_days, wor_overtime_hours, will_work_rest1, will_work_rest2, will_work_rest3, gmt_update, gmt_create, version)\n" +
//                 "select id,  10001, employee_id, accounting_no, agroup, org, emp_no, post, due_days, rest_days, due_minutes, late_times, late_minutes, big_late_times, big_late_minutes, absenteeism_late_days, leave_early_times, leave_early_minutes, in_leak_times, out_leak_times, absenteeism_days, absenteeism_hours, work_out_hours, annual_leave_days, matter_leave_days, sick_leave_hours, work_rest_hours, leave14_days, leave13_days, leave12_days, leave11_days, leave10_days, wor_overtime_hours, will_work_rest1, will_work_rest2, will_work_rest3, gmt_update, gmt_create, version\n" +
//                 "src v_attend_month_report_tmp  where employee_id is not null  and tenant_id=10001 and accounting_no=202101";
//         SQLStatement result = new SQLParseKernel(ParseRuleRegistry.getInstance(), "MySQL", sql).parse();
//         // final SelectStatement result1 = (SelectStatement) result;
//         System.out.println(JSONObject.toJSONString(result));
//     }
//
//     public void testInsertSelect2() {
//
//         String sql = "INSERT INTO  attend_salary_1 (id, tenant_id, emp_id, accounting_no, agroup, org, emp_no, post, due_days, rest_days, due_minutes, late_times, late_minutes, big_late_times, big_late_minutes, absenteeism_late_days, leave_early_times, leave_early_minutes, in_leak_times, out_leak_times, absenteeism_days, business_travel_hours, work_out_hours, annual_leave_days, matter_leave_days, sick_leave_hours, work_rest_hours, leave14_days, leave13_days, leave12_days, leave11_days, leave10_days, wor_overtime_hours, will_work_rest1, will_work_rest2, will_work_rest3, salary, gmt_update, gmt_create, version)\n" +
//                 " VALUES (101805, 10001, '101337', 202103, '考勤', '北极星-磊哥战队', null, '研发工程师', 10, 11, 4691, null, null, null, null, null, null, null, null, null, null, null, null, '4', null, null, '22', null, null, null, null, null, null, null, null, null, null, null, null, 0);\n" ;
//         SQLStatement result = new SQLParseKernel(ParseRuleRegistry.getInstance(), "MySQL", sql).parse();
//         // final SelectStatement result1 = (SelectStatement) result;
//         System.out.println(result);
//     }
// }