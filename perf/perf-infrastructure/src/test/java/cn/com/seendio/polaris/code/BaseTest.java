package cn.com.seendio.polaris.code;

import cn.hutool.crypto.SecureUtil;
import org.apache.ibatis.io.Resources;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * Created by lufei on 2017/6/8.
 */
public class BaseTest {

    protected SqlSession sqlSession;

    public void init(List<Class> types) {
        org.apache.ibatis.logging.LogFactory.useSlf4jLogging();
        String resource = "configuration.xml";
        InputStream inputStream = null;
        try {
            inputStream = Resources.getResourceAsStream(resource);
            SqlSessionFactory sqlSessionFactory = new SqlSessionFactoryBuilder().build(inputStream);
            sqlSession = sqlSessionFactory.openSession(true);
            types.stream().forEach(x -> sqlSession.getConfiguration().addMapper(x));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public void init(Class... types) {
        init(true, types);
    }

    public void init(boolean switchTransaction, Class... types) {
        org.apache.ibatis.logging.LogFactory.useSlf4jLogging();
        String resource = "configuration.xml";
        InputStream inputStream = null;
        try {
            inputStream = Resources.getResourceAsStream(resource);
            SqlSessionFactory sqlSessionFactory = new SqlSessionFactoryBuilder().build(inputStream);
            sqlSession = sqlSessionFactory.openSession(switchTransaction);
            for (Class type : types) {
                sqlSession.getConfiguration().addMapper(type);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    public <T> T getMapper(Class<T> type) {
        T mapper = sqlSession.getMapper(type);
        return mapper;
    }

    public void commit() {
        sqlSession.commit();
    }


}
