package cn.com.seendio.polaris.code;


/**
 * Created by lufei on 2018/4/12.
 */
public class Field {
    private String col;
    private String name;
    private String comment;
    private String type;
    private boolean ckey = false;

    public Field(String col, String name, String comment, String type) {
        this.col = col;
        this.name = name;
        this.comment = comment;
        this.type = type;
    }

    @Override
    public String toString() {
        if(ckey) {
            return String.format("@Ckey\nprivate %s %s;//%s", type, name, comment);
        }
        return String.format("private %s %s;//%s", type, name, comment);
    }

    public String jsonString() {
        return String.format(" \"%s\":\"%s\",", name, comment, comment);
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCol() {
        return col;
    }

    public void setCol(String col) {
        this.col = col;
    }

    public void setCkey(boolean ckey) {
        this.ckey = ckey;
    }
}
