// package cn.com.seendio.polaris.config;// package cn.com.seendio.polaris.config;
// //
//
// import com.alibaba.fastjson.JSONObject;
// import junit.framework.TestCase;
// import org.antlr.v4.runtime.tree.ParseTree;
// import org.apache.shardingsphere.sql.parser.api.SQLParserEngine;
//
// /**
//  * <AUTHOR> lufei
//  * @date 2021/3/20 23:33
//  */
// public class SQLParse411Test extends TestCase {
//     public void testName() {
//         String sql = "INSERT INTO attend_salary (id, tenant_id, emp_id, accounting_no,  agroup, org, emp_no, post, due_days, rest_days, due_minutes, late_times, late_minutes, big_late_times, big_late_minutes, absenteeism_late_days, leave_early_times, leave_early_minutes, in_leak_times, out_leak_times, absenteeism_days, business_travel_hours, work_out_hours, annual_leave_days, matter_leave_days, sick_leave_hours, work_rest_hours, leave14_days, leave13_days, leave12_days, leave11_days, leave10_days, wor_overtime_hours, will_work_rest1, will_work_rest2, will_work_rest3, gmt_update, gmt_create, version)\n" +
//                 "select id, tenant_id, employee_id, accounting_no, agroup, org, emp_no, post, due_days, rest_days, due_minutes, late_times, late_minutes, big_late_times, big_late_minutes, absenteeism_late_days, leave_early_times, leave_early_minutes, in_leak_times, out_leak_times, absenteeism_days, business_travel_hours, work_out_hours, annual_leave_days, matter_leave_days, sick_leave_hours, work_rest_hours, leave14_days, leave13_days, leave12_days, leave11_days, leave10_days, wor_overtime_hours, will_work_rest1, will_work_rest2, will_work_rest3, gmt_update, gmt_create, version\n" +
//                 "src v_attend_month_report_tmp  where employee_id is not null ";
//         final ParseTree mySQL = new SQLParserEngine("MySQL").parse(sql, true);
//         System.out.println(JSONObject.toJSONString(mySQL));
//     }
//
//     public void testName2() {
//         String sql = "  select seq_nextval('attend_month_report') id src test  ";
//         final ParseTree mySQL = new SQLParserEngine("MySQL").parse(sql, false);
//         System.out.println(mySQL);
//     }
//
//
// }