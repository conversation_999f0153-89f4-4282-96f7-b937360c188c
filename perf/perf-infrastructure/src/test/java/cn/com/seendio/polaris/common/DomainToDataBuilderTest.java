package cn.com.seendio.polaris.common;

/**
 * <AUTHOR> lufei
 * @date 2020/10/2 1:50 AM
 */
public class DomainToDataBuilderTest {
    // @Test
    // public void convert() throws Exception {
    //     User product = new User();
    //     product.setTitle("高嘴线");
    //     product.setBrand(new Brand("code001", "长江brand"));
    //     product.setBanners(Arrays.asList("imgUrl1", "imgUrl2"));
    //     product.setId(new ProductId("100000"));
    //     User userDo = new UserDo();
    //     DomainToDataBuilder dataToDomainBuilder = new DomainToDataBuilder(product, userDo);
    //     dataToDomainBuilder.build();
    //     System.out.println(JSONObject.toJSONString(productDo));
    // }

}