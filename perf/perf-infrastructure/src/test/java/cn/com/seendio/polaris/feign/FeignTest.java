// package cn.com.seendio.polaris.feign;
//
// import com.alibaba.cola.dto.Response;
// import feign.Feign;
// import feign.Request;
// import feign.Retryer;
// import feign.jackson.JacksonDecoder;
// import feign.jackson.JacksonEncoder;
// import org.junit.Before;
// import org.junit.Test;
//
// import java.util.HashMap;
// import java.util.List;
//
// /**
//  * <AUTHOR> admin
//  * @date 2021/4/21 6:28 下午
//  */
// public class FeignTest {
//     protected RemoteService service;
//
//     @Before
//     public void setUp() throws Exception {
//         service = Feign.builder()
//                 //添加编码器
//                 .encoder(new JacksonEncoder())
//                 .decoder(new JacksonDecoder())
//                 .options(new Request.Options(1000, 3500))
//                 .retryer(new Retryer.Default(5000, 5000, 3))
//                 .target(RemoteService.class, "https://wsltest.topscrm.cn/");
//     }
//
//     @Test
//     public void name() {
//         final HashMap<String, String> map = new HashMap<>();
//         map.put("tk", "eyJ0aW1lIjoiMTYxOTAwMDk5MiIsImtleSI6InRkcyJ9.eyJ0ZW5hbnRJZCI6IjEwMDA0IiwiaWQiOiIxMDI0MDMifQ==.4ec0a4c0ddfb2f7bc8910890095550bd");
//         final Response response = service.getSocial("100302", map);
//         System.out.println(response);
//
//     }
//
//     public static void main(String... args) {
//         GitHub github = Feign.builder()
//                 .decoder(new JacksonDecoder())
//                 .target(GitHub.class, "https://api.github.com");
//         // Fetch and print a list of the contributors after this library.
//         List<Contributor> contributors = github.contributors("OpenFeign", "feign");
//         for (Contributor contributor : contributors) {
//             System.out.println(contributor.login + " (" + contributor.contributions + ")");
//         }
//     }
// }
