package cn.com.seendio.polaris.code;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.lufei.ibatis.dao.AutoDaoImpl;
import org.lufei.ibatis.mapper.BaseAutoMapper;

import java.io.File;
import java.nio.charset.Charset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by lufei on 2017/9/12.
 */
public class InitTableCoder {

    protected TestContext context;
    protected BaseAutoMapper mapper;
    protected AutoDaoImpl baseDao;
    String path = "/Users/<USER>/code/kpi-sql/kpi-sql/work/线上/2025/迁移-东方未来/数据/";

    @BeforeClass
    public static void beforeClass() throws Exception {
//        beforeClass("");
    }


    @Before
    public void setUp() throws Exception {
//        context = new TestContext("tenantConf.xml");
//        context.init(BaseAutoMapper.class);
//        mapper = context.getMapper(BaseAutoMapper.class);
//        baseDao = new AutoDaoImpl();
//        baseDao.setAutoMapper(mapper);
    }


    @Test
    public void parseTableName() {

//        select_from_admin_cycle_operation_where.sql
//        FileUtil.mkdir(new File())
//        String path = "/Users/<USER>/code/kpi-sql/kpi-sql/work/线上/2025/迁移-东方未来/数据";
        List<String> names = FileUtil.listFileNames(path + "/srcdata/");
        int maxRow = 0;
        String maxRowName = "";
        for (String name : names) {
            File srcFile = new File(path + "/srcdata/" + name);
            String nameClear = name.replaceAll("select_from_", "").replaceAll("_wh(.*)", "").replaceAll("\\.sql", "");
            nameClear = StrUtil.removeSuffix(nameClear, "_w");
            nameClear = nameClear + ".sql";
//            System.out.println(nameClear + "<---" + name);
            System.out.println(nameClear);
            List<String> inserts = FileUtil.readLines(srcFile, Charset.defaultCharset());
            if (CollUtil.isEmpty(inserts)) {
//                System.out.println("空文件:" + name);
                continue;
            }
            File clearFile = FileUtil.newFile(path + "/targetdata/" + nameClear);
            if (maxRow < inserts.size()) {
                maxRow = inserts.size();
                maxRowName = name;
            }
            String tableName = nameClear.replace(".sql", "");
            List<String> lines = inserts.stream().map(insert -> insert.replaceAll("MY_TABLE", tableName)).collect(Collectors.toList());
            FileUtil.writeString("", clearFile, "utf-8");
            FileUtil.appendLines(lines, clearFile, "utf-8");
        }
        System.out.println("maxRow:" + maxRow);
        System.out.println("maxRowName:" + maxRowName);
//        File ddlFile = new File("/Users/<USER>/code/kpi-sql/kpi-sql/work/合品/合品KPI表.sql");
//        FileUtil.writeString("", ddlFile, "utf-8");
//        List<String> talbeNames = FileUtil.readLines(new File("/Users/<USER>/code/kpi-sql/kpi-sql/work/合品/KPI表名.txt"), Charset.defaultCharset());
//        for (String table : talbeNames) {
//            List<Map> maps = mapper.nativeSelectAll(Map.class, "show create  table  " + table, null);
//            Object createTable = maps.get(0).get("create Table");
////            System.out.println(createTable.toString());
//            String replace = createTable.toString().replace("COLLATE=utf8mb4_bin", "")
//                    .replace("collate = utf8mb4_bin", "")
//                    .replace("COLLATE utf8mb4_bin", "");
//            System.out.println(replace);
//            FileUtil.appendLines(Arrays.asList(replace + ";\n"), ddlFile, "utf-8");
//        }
    }

    @Test
    public void replaceEmpId() {
        //替换原来的从企微导出的 empid
        String file = "/Users/<USER>/code/kpi-sql/kpi-sql/work/线上/2025/迁移-东方未来/数据/replcae/emp_id.txt";
//        String targetPath = path + "/targetdata/";
        String r1Path = path + "/r3/";
        List<String> names = FileUtil.listFileNames(r1Path);
        List<String> empIdLines = FileUtil.readLines(file, Charset.defaultCharset());
        empIdLines.remove(0);
        for (String empIdLine : empIdLines) {
            empIdLine = empIdLine.replaceAll("\"", "");
            System.out.println(empIdLine);
            for (String name : names) {
                File file1 = new File(r1Path + name);
                replaceEmp(empIdLine, file1);
            }
        }
        //替换empId: $userName=woI9vQEAAAJ6uXTvgilYa8IQ2kZEkh3w$
        //替换原的来员工名字: $userName=woI9vQEAAAJ6uXTvgilYa8IQ2kZEkh3w$
        //替换原的来员工名字: woI9vQEAAAJ6uXTvgilYa8IQ2kZEkh3w-> ding_user_id
        //加载能匹配的对应关系
    }

    @Test
    public void replaceCompanyId() {
        //替换原来的从企微导出的 empid
//        String targetPath = path + "/targetdata/";
        String r1Path = path + "/r1/";
        List<String> names = FileUtil.listFileNames(r1Path);
        for (String name : names) {
            System.out.println(name);
            File file1 = new File(r1Path + name);
            String sqls = FileUtil.readString(file1, Charset.defaultCharset());
            sqls = sqls.replaceAll("1018618", "1233152");
            FileUtil.writeString(sqls, file1, "utf-8");
        }
    }


    @Test
    public void replaceEmpId2() {
        //替换原来的从企微导出的 empid
        String r1Path = path + "/r1/";
        String empIdLine = "\"于枫\",\"woI9vQEAAAdcHWiB5yC1bgn3KLj94OcA\",\"0423603057649949\",\"1412182\",\"*********\",\"https://rescdn.qqmail.com/node/wwmng/wwmng/style/images/independent/DefaultAvatar$73ba92b5.png\",\"https://static-legacy.dingtalk.com/media/lQDPM5khrojnMcPNAhzNAhywqJMKsKPXkz8HrxcMwDaHAA_540_540.jpg\"\n";
        empIdLine = empIdLine.replaceAll("\"", "");
        System.out.println(empIdLine);

        File file1 = new File(r1Path + "/admin_cycle_operation.sql");

        replaceEmp(empIdLine, file1);
//        FileUtil.writeString(sqls, file1, "utf-8");
        //替换empId: $userName=woI9vQEAAAJ6uXTvgilYa8IQ2kZEkh3w$
        //替换原的来员工名字: $userName=woI9vQEAAAJ6uXTvgilYa8IQ2kZEkh3w$
        //替换原的来员工名字: woI9vQEAAAJ6uXTvgilYa8IQ2kZEkh3w-> ding_user_id
        //加载能匹配的对应关系
    }

    private static void replaceEmp(String empIdLine, File file1) {
        System.out.println(file1.getName());
        EmpReplace empReplace = new EmpReplace(empIdLine);
        String sqls = FileUtil.readString(file1, Charset.defaultCharset());
        sqls = sqls.replaceAll(empReplace.wecomName(), empReplace.name);
        sqls = sqls.replaceAll(String.format("'%s'", empReplace.oldDingUserId), String.format("'%s'", empReplace.newDingUserId));
        sqls = sqls.replaceAll(String.format("'%s'", empReplace.oldId), String.format("'%s'", empReplace.newId));
        sqls = sqls.replaceAll(String.format("'%s'", empReplace.oAvatar), String.format("'%s'", empReplace.nAvatar));
//        System.out.println(sqls);
        FileUtil.writeString(sqls, file1, "utf-8");
    }

    public static class EmpReplace {
        public String name;
        public String oldDingUserId;
        public String newDingUserId;
        public String oldId;
        public String newId;
        public String oAvatar;
        public String nAvatar;

        public EmpReplace(String value) {
            String[] split = StrUtil.split(value, ",");
            this.name = split[0];
            this.oldDingUserId = split[1];
            this.newDingUserId = split[2];
            this.oldId = split[3];
            this.newId = split[4];
            this.oAvatar = split[5];
            this.nAvatar = split[6];
        }

        public String wecomName() {
            return "\\$userName=" + oldDingUserId + "\\$";
        }

        public String wecomDeptName() {
//            $departmentName=13$
            return "\\$departmentName=" + oldDingUserId + "\\$";
        }
    }


    public static class DeptReplace {
        public String name;
        public String oldDingOrgId;
        public String newDingOrgId;
        public String oldId;
        public String newId;

        public DeptReplace(String value) {
            String[] split = StrUtil.split(value, ",");
            this.name = split[0];
            this.oldDingOrgId = split[1];
            this.newDingOrgId = split[2];
            this.oldId = split[3];
            this.newId = split[4];
        }

        public String wecomDeptName() {
//            $departmentName=13$
            return "\\$departmentName=" + oldDingOrgId + "\\$";
        }
    }

    private static void replaceDept(String empIdLine, File file1) {
        DeptReplace empReplace = new DeptReplace(empIdLine);
        String sqls = FileUtil.readString(file1, Charset.defaultCharset());
        sqls = sqls.replaceAll(empReplace.wecomDeptName(), empReplace.name);
        sqls = sqls.replaceAll(String.format("'%s'", empReplace.oldDingOrgId), String.format("'%s'", empReplace.newDingOrgId));
        sqls = sqls.replaceAll(String.format("'%s'", empReplace.oldId), String.format("'%s'", empReplace.newId));
//        System.out.println(sqls);
        FileUtil.writeString(sqls, file1, "utf-8");
    }

    @Test
    public void replaceOrgId() {
        //替换原来的从企微导出的 deptId
        //替换原的部部门名字: $departmentName=15$ ->
        //加载能匹配的对应关系
        String file = "/Users/<USER>/code/kpi-sql/kpi-sql/work/线上/2025/迁移-东方未来/数据/replcae/dept_id.txt";
        String targetPath = path + "/targetdata/";
        String r1Path = path + "/r1/";
//        List<String> names = FileUtil.listFileNames(targetPath);
        List<String> names = Arrays.asList("perf_evaluate_task_kpi.sql");
        List<String> empIdLines = FileUtil.readLines(file, Charset.defaultCharset());
        empIdLines.remove(0);
        for (String empIdLine : empIdLines) {
            empIdLine = empIdLine.replaceAll("\"", "");
            System.out.println(empIdLine);
            for (String name : names) {
                if ("employee_base_info.sql".equals(name)) {
                    continue;
                }
                System.out.println(name);
                File file1 = new File(r1Path + name);
                replaceDept(empIdLine, file1);
            }
        }
    }

    @Test
    public void replaceAll() {
        replaceCompanyId();
        createSeqIdAndReplaceOther();
        replaceOrgId();
        replaceEmpId();
    }


    @Test
    public void createSeqIdAndReplaceOther() {
        String file = "/Users/<USER>/code/kpi-sql/kpi-sql/work/线上/2025/迁移-东方未来/数据/replcae/seq.txt";

        List<String> seqLines = FileUtil.readLines(new File(file), Charset.defaultCharset());
        for (String seqLine : seqLines) {
            if (seqLine.startsWith("#")) {
                continue;
            }
            Seq seq = new Seq(seqLine);
            if (CollUtil.isEmpty(seq.refTables)) {
                continue;
            }
            //替换自己的表
            File self = new File(path + "/r1/" + seq.table + ".sql");
            if (!self.exists()) {
                continue;
            }
            System.out.println("seq.table=========" + seq.table);
            //旧id->新id
            // INSERT INTO perf_evaluate_task_item_score_rule(id, company_id, task_id, task_user_id, kpi_item_id, self_score_flag, self_score_view_rule, self_score_weight, mutual_score_flag, mutual_score_attend_rule, mutual_score_anonymous, mutual_score_vacancy, mutual_score_view_rule, peer_score_weight, sub_score_weight, superior_score_flag, superior_score_view_rule, superior_score_weight, superior_score_vacancy, appoint_score_flag, appoint_score_weight, created_user, created_time, updated_user, updated_time, is_deleted, kpi_type_id, mutual_user_type, version, mutual_user_value, self_rater, mutual_rater, super_rater, appoint_rater, peer_rater, sub_rater) VALUES ('2266026', '1233152', '1879739', '1964892', '8613e7f7-7861-494f-89b9-8acb24da746d', 'true', null, 0.00, null, null, null, null, null, null, null, 'true', null, 100.00, null, 'false', null, null, '2024-10-23 13:58:39', null, '2024-10-23 13:58:39', 'false', '56c89552-072c-4bf1-b261-5f94fa13947a', null, 0, null, '{"anonymous":"false","nodeWeight":0,"open":1,"signatureFlag":false}', null, '{"auditNodes":[{"approvalOrder":1,"approverType":"manager","multiType":"or","node":"superior_score","raters":[{"empId":"1412182","empName":"于枫","level":1,"type":1}],"roleAudit":false,"scoreWeight":100,"transferFlag":"true","weight":100}],"multiType":"or","nodeVacancyFlag":1,"nodeWeight":100,"open":1,"signatureFlag":true,"superiorScoreOrder":"inTurn"}', '{"auditNodes":[],"multiType":"and","open":0,"signatureFlag":false}', '{"change":false,"excludeAllManager":false,"open":0,"raters":[],"signatureFlag":false}', '{"change":false,"excludeAllManager":false,"open":0,"raters":[],"signatureFlag":false}');
            String selfString = FileUtil.readString(self, Charset.defaultCharset());
            String[] inserts = selfString.split("\\);\n");
            List<String> replaces = new ArrayList<>();
            //找旧id,生成新id

            Map<String, String> talbeMapSqls = new HashMap<>();
            for (String refTable : seq.refTables) {
                //找对对应文件,替换
                File ref = new File(path + "/r1/" + refTable + ".sql");
                if (!ref.exists()) {
                    continue;
                }
                //旧id->新id
                String sqls = FileUtil.readString(ref, Charset.defaultCharset());
                talbeMapSqls.put(refTable, sqls);
            }
            int l = 0;
            for (String insert : inserts) {
                String oldId = parseId(insert);
                String newId = seq.nextId(oldId);
                l++;
                System.out.println("line:" + l);

                for (String refTable : seq.refTables) {
                    //找对对应文件,替换
                    String sqls = talbeMapSqls.get(refTable);
                    if (sqls == null) {
                        continue;
                    }
                    //旧id->新id
                    String replaceAll = sqls.replaceAll(oldId, newId);
                    talbeMapSqls.put(refTable, replaceAll);
                }

                talbeMapSqls.forEach((refTable, replaceAll) -> {
//                    System.out.println(replaceAll);
                    FileUtil.writeString(replaceAll, new File(path + "/r3/" + refTable + ".sql"), Charset.defaultCharset());
                });

            }
//            FileUtil.writeLines(replaces, self, Charset.defaultCharset());
        }
//        List<String> okrs = FileUtil.readLines(new File("/Users/<USER>/code/kpi-sql/kpi-sql/work/合品/OKR表名.txt"), Charset.defaultCharset());
//        Collection<String> intersection = CollUtil.intersection(kpis, okrs);
//        for (String s : intersection) {
//            System.out.println(s);
//        }
    }


    @Test
    public void parseSql() {
        String sql = "INSERT INTO perf_evaluate_task_item_score_rule(id, company_id, task_id, task_user_id, kpi_item_id, self_score_flag, self_score_view_rule, self_score_weight, mutual_score_flag, mutual_score_attend_rule, mutual_score_anonymous, mutual_score_vacancy, mutual_score_view_rule, peer_score_weight, sub_score_weight, superior_score_flag, superior_score_view_rule, superior_score_weight, superior_score_vacancy, appoint_score_flag, appoint_score_weight, created_user, created_time, updated_user, updated_time, is_deleted, kpi_type_id, mutual_user_type, version, mutual_user_value, self_rater, mutual_rater, super_rater, appoint_rater, peer_rater, sub_rater) VALUES ('2284914', '1233152', '1880435', '1966855', 'c5b69dad-f08c-4d26-92ff-395068c51a34', 'true', null, 0.00, null, null, null, null, null, null, null, 'true', null, 100.00, null, 'false', null, null, '2024-10-28 14:10:25', null, '2024-10-28 14:10:25', 'false', '2011ac48-f50a-44e8-8433-85609f49f700', null, 0, null, '{\"anonymous\":\"false\",\"nodeWeight\":0.00,\"open\":1,\"signatureFlag\":false}', null, '{\"auditNodes\":[{\"approvalOrder\":1,\"approverInfo\":\"1\",\"approverType\":\"manager\",\"multiType\":\"or\",\"node\":\"superior_score\",\"raters\":[{\"empId\":\"1412170\",\"empName\":\"李卫康\",\"level\":1,\"type\":2,\"weight\":100.00}],\"roleAudit\":false,\"scoreWeight\":100.00,\"transferFlag\":\"true\",\"weight\":100.00}],\"multiType\":\"or\",\"nodeVacancyFlag\":1,\"nodeWeight\":100.00,\"open\":1,\"signatureFlag\":true,\"superiorScoreOrder\":\"inTurn\"}', '{\"auditNodes\":[],\"multiType\":\"and\",\"open\":0,\"signatureFlag\":false}', '{\"change\":false,\"excludeAllManager\":false,\"open\":0,\"raters\":[],\"signatureFlag\":false}', '{\"change\":false,\"excludeAllManager\":false,\"open\":0,\"raters\":[],\"signatureFlag\":false}')";
        String id = parseId(sql);
        System.out.println(id);
//        if(id.startsWith("'")){
//            
//        }
    }

    public String parseId(String sql) {

        String[] split = sql.split("\\) VALUES \\(");
        List<String> names = StrUtil.splitTrim(split[0].split("\\(")[1], ",");
        int indx = 0;
        for (int i = 0; i < names.size(); i++) {
            if (names.get(i).equals("id")) {
                indx = i;
                break;
            }
        }
        List<String> values = StrUtil.splitTrim(split[1].split("\\(")[0], ",");
        return values.get(indx);
    }

    @Test
    public void seq() {
        Seq seq = new Seq("\"admin_scope_priv_group\",\"1174400\",\"100\",[\"admin_scope_priv_group\",\"admin_scope_priv_group_of_emp\",\"admin_scope_priv_group_org\"]");
    }
}
