// package cn.com.seendio.polaris.feign;
//
// import com.alibaba.cola.dto.Response;
// import feign.*;
//
// import java.util.Map;
//
// /**
//  * <AUTHOR> admin
//  * @date 2021/4/21 6:28 下午
//  */
// public interface RemoteService {
//     @Headers ({"Content-Type: application/x-www-form-urlencoded", "Accept: application/json"})
//     @RequestLine ("POST /polaris/tenant/emp/getSocial?toDoEmpId={toDoEmpId}")
//     Response getSocial(@Param (value = "toDoEmpId") String employeeId, @HeaderMap Map map);
// }
