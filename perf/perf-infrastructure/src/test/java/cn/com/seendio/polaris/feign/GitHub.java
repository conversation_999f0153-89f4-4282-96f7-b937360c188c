// package cn.com.seendio.polaris.feign;
//
// import feign.Param;
// import feign.RequestLine;
//
// import java.util.List;
//
// /**
//  * <AUTHOR> admin
//  * @date 2021/4/21 7:14 下午
//  */
// public interface GitHub {
//     // RequestLine注解声明请求方法和请求地址,可以允许有查询参数
//     @RequestLine ("GET /repos/{owner}/{repo}/contributors")
//     List<Contributor> contributors(@Param ("owner") String owner, @Param ("repo") String repo);
//
// }
