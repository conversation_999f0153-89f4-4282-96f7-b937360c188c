package cn.com.seendio.polaris.code;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;

import java.util.List;

public class Seq {
    //        "admin_cycle_operation","1358300","100",["admin_cycle_operation"]
    public String table;
    public Long max;
    public List<String> refTables;

    public Seq(String value) {
        String[] split = StrUtil.split(value, "\"100\",");
        String[] split1 = split[0].replaceAll("\"", "").split(",");
        this.table = split1[0];
        this.max = Long.valueOf(split1[1].replaceAll("\"", ""));
        System.out.println("seq#" + value);
        this.refTables = JSONUtil.parseArray(split[1]).toList(String.class);
    }

    public String nextId(String oldId) {
        max--;
        if (oldId.startsWith("'")) {
            return String.format("'%s'", max);
        }
        return max + "";
    }
}