package cn.com.seendio.polaris.code;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.CaseFormat;
import com.polaris.kpi.eval.infr.task.BaseDaoTest;
import com.polaris.kpi.org.infr.company.ppojo.KpiNempOrderDo;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.lufei.ibatis.dao.AutoDaoImpl;
import org.lufei.ibatis.mapper.BaseAutoMapper;
import org.lufei.ibatis.tool.Tool;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Created by lufei on 2017/9/12.
 */
public class TenantCoder extends BaseDaoTest {

    protected TestContext context;
    protected BaseAutoMapper mapper;
    protected AutoDaoImpl baseDao;

    @BeforeClass
    public static void beforeClass() throws Exception {
        beforeClass("");
    }


    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.init(BaseAutoMapper.class);
        mapper = context.getMapper(BaseAutoMapper.class);
        baseDao = new AutoDaoImpl();
        baseDao.setAutoMapper(mapper);
    }

    public static void main(String[] args) {
        System.out.println("xxxx");
    }

    @Test
    public void testSeq() {
        Set set = new HashSet<>();
        new Thread(() -> {
            while (true) {
                final String id = baseDao.nextLongAsStr("test_ind");
                // context.commit();
                System.out.println("id" + id);
                if (set.contains(id)) {
                    System.out.println("重复id" + id);
                    break;
                }
                set.add(id);
            }
        }).run();

        new Thread(() -> {
            while (true) {
                final String id = baseDao.nextLongAsStr("test_ind");
                // context.commit();
                System.out.println("id" + id);
                if (set.contains(id)) {
                    System.out.println("重复id" + id);
                    break;
                }
                set.add(id);
            }
        }).run();
    }

    @Test
    public void batchColumn2Bean() {
        for (String table : Arrays.asList(
                "perf_evaluate_okr_goal"
        )) {
            buildBean(table);
        }
    }


    @Test
    public void checkNameLength() {
        List<String> cols = new ArrayList<>();
        for (String table : Arrays.asList(
                "admin_cycle_operation",
                "admin_manage_org",
                "admin_of_cycle",
                "admin_of_task",
                "admin_task_operation",
                "announcement",
                "app_version",
                "app_version_script",
                "company",
                "company_220525",
                "company_220816",
                "company_auto_free",
                "company_cache_info",
                "company_category",
                "company_dic",
                "company_formula_field",
                "company_init_industry",
                "company_item_decompose",
                "company_item_rel_org",
                "company_item_rel_tag",
                "company_kpi_item",
                "company_kpi_item_custom_field",
                "company_msg_center",
                "company_msg_rel_score_result",
                "company_notice",
                "company_perf_level_header",
                "company_perf_level_quota",
                "company_score_group",
                "company_score_group_copy",
                "company_score_group_member",
                "company_sys_setting",
                "company_tag",
                "cycle_eval_rule",
                "dept_leader_syn",
                "dept_ref_rule",
                "emp_apply_use",
                "emp_eval_kpi_type",
                "emp_eval_operation",
                "emp_eval_rule",
                "emp_eval_table",
                "emp_organization",
                "emp_ref_announcement",
                "emp_ref_org",
                "emp_ref_org_220818",
                "emp_ref_score_rule",
                "emp_table_item_rule",
                "emp_table_kpi_item",
                "emp_table_kpi_type",
                "emp_table_operation",
                "employee_base_info",
                "employee_base_info_copy",
                "employee_syn",
                "eval_kpi_input_bak",
                "global_sys_param",
                "grade_step",
                "gray_publish",
                "hrm_solution_order",
                "kpi_nemp_order",
                "kpi_nemp_used_log",
                "last_cycle_task_conf",
                "media",
                "my_id",
                "new_emp",
                "onboard_timer_conf",
                "onboard_timer_item",
                "onboard_timer_log",
                "open_access_cache",
                "open_access_limit",
                "open_auth_info",
                "operation_log",
                "org_eval_owner",
                "perf_evaluate_cycle",
                "perf_evaluate_item_notice",
                "perf_evaluate_score_summary",
                "perf_evaluate_task_action",
                "perf_evaluate_task_appeal_batch",
                "perf_evaluate_task_appeal_info",
                "perf_evaluate_task_audit",
                "perf_evaluate_task_audit_init",
                "perf_evaluate_task_base",
                "perf_evaluate_task_coach",
                "perf_evaluate_task_discuss",
                "perf_evaluate_task_file",
                "perf_evaluate_task_formula_field",
                "perf_evaluate_task_item_score_rule",
                "perf_evaluate_task_kpi",
                "perf_evaluate_task_okr_type",
                "perf_evaluate_task_ref_okr",
                "perf_evaluate_task_score_result",
                "perf_evaluate_task_score_result_tmp",
                "perf_evaluate_task_score_rule",
                "perf_evaluate_task_user",
                "perf_evaluation_level",
                "perf_evaluation_level_group",
                "perf_evaluation_task_level",
                "perf_item_ref_point_detail",
                "perf_level_group_rel_temp",
                "perf_modification_record",
                "perf_templ_base",
                "perf_templ_evaluate",
                "perf_templ_evaluate_affirm",
                "perf_templ_evaluate_audit",
                "perf_templ_evaluate_execute",
                "perf_templ_evaluate_initiate",
                "perf_templ_formula_field",
                "perf_templ_item_evaluate",
                "perf_templ_item_point_rule",
                "perf_templ_kpi_item",
                "perf_templ_kpi_type",
                "perf_templ_org",
                "ps_cache",
                "ref_eval",
                "report_weight_setting",
                "role",
                "role_ref_emp",
                "schedule_temp",
                "score_range",
                "score_rule",
                "sequence",
                "sv_in_service",
                "sys_dic_info",
                "sys_dic_type",
                "sys_init_industry",
                "sys_init_temp",
                "sys_init_temp_rel_item",
                "sys_kpi_item",
                "system_admin_set",
                "system_iteration",
                "task_fix_finish_status",
                "temp_company",
                "tenant",
                "tenant_sys_conf",
                "test",
                "tg_tmp",
                "third_api_info",
                "third_api_invoke_log",
                "third_platform_setting",
                "tip",
                "tip_read",
                "tmp_help_index",
                "train_appointment",
                "use_item_log"
        )) {
            List<Map> maps = mapper.nativeSelectAll(Map.class, "SHOW FULL COLUMNS FROM  " + table, null);

            for (Map col : maps) {
                String type = col.get("type").toString();
                if (type.startsWith("varchar")) {
                    //System.out.println(type);
                    Matcher matcher = Pattern.compile("[1-9]\\d*").matcher(type);
                    if (matcher.find() && Integer.valueOf(matcher.group()) < 50) {
                        //System.out.println(matcher.group());
                        String colStr = String.format("%s.%s %s comment(%s)", table, col.get("field"), type, col.get("comment"));
                        cols.add(colStr);
                    }
                }

            }
        }

        for (String col : cols) {
            System.out.println("字段:" + col);
        }

    }

    @Test
    public void col2Pojo() throws Exception {
        //  "factor_eval_result_read", "indicator_eval_result_read", "talent_eval_result_read",
        String table = "task_result_audit_summary";
        buildBean(table);
    }

    private void buildBean(String table) {
        List<String> exculd = Arrays.asList("companyId", "isDeleted", "createdUser", "createdTime", "updatedUser", "updatedTime");
        List<Map> maps = mapper.nativeSelectAll(Map.class, "SHOW FULL COLUMNS FROM  " + table, null);
        // List<Map> maps = mapper.selectBySql("SHOW FULL COLUMNS FROM train_course_directory_file", null);
        List<Field> list = maps.stream().map(pojo -> {
            String type = pojo.get("type").toString();
            String fieldName = pojo.get("field").toString();
            if (type.contains("varchar") || type.contains("text") || type.startsWith("char")) {
                type = String.class.getSimpleName();
            }
            if (type.endsWith("id") && type.startsWith("bigint")) {
                type = Long.class.getSimpleName();
            }
            if (type.startsWith("bigint")) {
                if (fieldName.endsWith("id")) {
                    type = String.class.getSimpleName();
                } else {
                    type = Long.class.getSimpleName();
                }
            }
            if (type.startsWith("tinyint") || type.startsWith("smallint")) {
                type = Integer.class.getSimpleName();
            }
            if (type.startsWith("int")) {
                type = Integer.class.getSimpleName();
            }

            if (type.contains("timestamp")) {
                type = Date.class.getSimpleName();
            }
            if (type.contains("date")) {
                type = Date.class.getSimpleName();
            }
            if (type.contains("bit")) {
                type = Boolean.class.getSimpleName();
            }
            if (type.contains("decimal")) {
                type = BigDecimal.class.getSimpleName();
            }
            String col = pojo.get("field").toString();
            String field = CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, col);
            Field filed = new Field(col, field, pojo.get("comment").toString(), type);
            if (fieldName.equals("id")) {
                filed.setCkey(true);
            }
            if (fieldName.equals("tenant_id")) {
                filed.setCkey(true);
            }

            if (fieldName.equals("company_id")) {
                filed.setCkey(true);
            }
            return filed;
        }).collect(Collectors.toList());
        System.out.println("import lombok.Getter;\n" +
                "import lombok.NoArgsConstructor;\n" +
                "import lombok.Setter;\n" +
                "import org.apache.ibatis.annotations.Ckey;");
        System.out.println("/** {");
        list.forEach(field -> {
            System.out.println(field.jsonString());
        });
        System.out.println("} ***/");


        System.out.println(String.format("" +

                "@Setter\n" +
                "@Getter\n" +
                "@NoArgsConstructor\n" +
                "public class %sDo extends DelData {", CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.UPPER_CAMEL, table)));
        // list.forEach(field -> {
        //     System.out.print(String.format("p.`%s`= a.`%s`,", field.getCol(), field.getCol()));
        // });
        ArrayList<String> colums = new ArrayList<>();
        colums.addAll(exculd);
        colums.addAll(Arrays.asList("gmtUpdate", "gmtCreate", "version"));
        System.out.println();
        list.forEach(field -> {
            if (colums.contains(field.getName())) {
                return;
            }
            System.out.println(field.toString());
        });
        System.out.println("}");
        // System.out.println("{");
        // list.forEach(field -> {
        //     System.out.println(field.jsonString());
        // });
        // System.out.println("}");
        context.commit();
    }

    @Test
    public void pojo2Table() throws Exception {
        final Class cls = KpiNempOrderDo.class;//换成DO类
        final String table = Tool.toUnderLine(cls.getSimpleName());
        StringBuilder createStr = new StringBuilder(String.format(" CREATE TABLE `%s` (", table));
        createStr.append("\n");
        List<java.lang.reflect.Field> fields = Tool.getInstanceFields(cls);
        fields.forEach(field -> {
            final String name = field.getName();
            final String underLine = Tool.toUnderLine(name);
            String col = null;
            if (field.getType() == String.class) {
                col = String.format("`%s` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL ", underLine);
            } else if (field.getType() == Long.class) {
                col = String.format("`%s` bigint(20) DEFAULT NULL ", underLine);
            } else if (field.getType() == Integer.class) {
                col = String.format("`%s` int(10)  DEFAULT NULL ", underLine);
            } else if (field.getType() == BigDecimal.class) {
                col = String.format("`%s` decimal(6,2)  DEFAULT NULL ", underLine);
            } else if (field.getType() == Boolean.class) {
                col = String.format("`%s` bit(1)  DEFAULT NULL ", underLine);
            } else {
                col = String.format("`%s` text  DEFAULT NULL ", JSONObject.toJSON(underLine).toString());
            }
            createStr.append(col);
            ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
            if (annotation != null) {
                final int index = annotation.value().length > 1 ? 1 : 0;
                createStr.append(" comment '").append(annotation.value()[index]).append("',").append("\n");
            } else {
                createStr.append("comment '',").append("\n");
            }

        });
        createStr.deleteCharAt(createStr.lastIndexOf(","));
        createStr.append(") ENGINE=InnoDB  comment '' DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;");
        System.out.println(createStr);

    }


}
