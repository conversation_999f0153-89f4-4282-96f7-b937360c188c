package cn.com.seendio.polaris.external.tenant;// package cn.com.seendio.polaris.external.tenant;
//
// import cn.com.seendio.polaris.repository.salary.pojo.SalaryBarPo;
// import cn.com.seendio.polaris.repository.salary.query.BaseComputeSalaryQuery;
// import cn.com.seendio.polaris.types.domain.auth.TenantId;
// import com.alibaba.cola.dto.MultiResponse;
// import junit.framework.TestCase;
// import org.junit.Test;
// import org.junit.runner.RunWith;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
// import org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration;
// import org.springframework.boot.test.context.SpringBootTest;
// import org.springframework.cloud.netflix.ribbon.RibbonAutoConfiguration;
// import org.springframework.cloud.openfeign.EnableFeignClients;
// import org.springframework.cloud.openfeign.FeignAutoConfiguration;
// import org.springframework.cloud.openfeign.ribbon.FeignRibbonClientAutoConfiguration;
// import org.springframework.context.annotation.Import;
// import org.springframework.test.context.junit4.SpringRunner;
//
// /**
//  * <AUTHOR> admin
//  * @date 2021/4/15 4:13 下午
//  */
// @RunWith (SpringRunner.class)
// // @ContextConfiguration (classes = {
// //         // PlatTenantAclSvc.class,
// // })
//
// @SpringBootTest (classes = PlatTenantAclSvcTest.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
// @EnableFeignClients (value = "cn.com.seendio.polaris.external.tenant")
// @ImportAutoConfiguration ({RibbonAutoConfiguration.class, FeignRibbonClientAutoConfiguration.class, FeignAutoConfiguration.class})
// @Import ({FeignAutoConfiguration.class, HttpMessageConvertersAutoConfiguration.class})
// public class PlatTenantAclSvcTest extends TestCase {
//     @Autowired
//     private PlatTenantAclSvc aclSvc;
//
//     public void setUp() throws Exception {
//         super.setUp();
//     }
//
//     public void tearDown() throws Exception {
//     }
//
//     @Test
//     public void testHello() {
//         aclSvc.hello("", "");
//     }
//
//     @Test
//     public void testPagedSalaryBar() {
//         BaseComputeSalaryQuery query = new BaseComputeSalaryQuery();
//         query.setTenantId(new TenantId("1"));
//         final MultiResponse<SalaryBarPo> response = aclSvc.pagedSalaryBar("eyJ0aW1lIjoiMTYxODIyMjM3MiIsImV4cCI6IjE2MTgyNTgzNzIiLCJrZXkiOiJ4YyJ9.eyJ0ZW5hbnRJZCI6IjEwMDA0IiwiZW1wIjoiMTAwMzAxIiwiaWQiOiIxMDA5MDIifQ==.015165c1940ca1a88c08e7f064f4dbaf",
//                 query);
//         System.out.println(response.getData());
//     }
// }