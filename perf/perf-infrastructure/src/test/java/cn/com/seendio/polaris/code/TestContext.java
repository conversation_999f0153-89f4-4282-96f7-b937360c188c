package cn.com.seendio.polaris.code;

import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.io.Resources;
import org.apache.ibatis.jdbc.ScriptRunner;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.lufei.ibatis.mapper.BaseAutoMapper;

import javax.sql.DataSource;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.Reader;
import java.io.StringWriter;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * Created by lufei on 2017/7/20.
 */
public class TestContext {
    public static final String SQL_MAP_CONFIG = "sqlmap.xml";

    protected SqlSession sqlSession;
    protected SqlSessionFactory sqlSessionFactory;
    protected BaseAutoMapper mapper;
    @Getter
    protected DomainDaoImpl domainDao;

    public TestContext() {
        try {
            sqlSessionFactory = new SqlSessionFactoryBuilder().build(Resources.getResourceAsReader(SQL_MAP_CONFIG));
            // sqlSessionFactory = new SqlSessionFactoryBuilder().build(inputStream);
            sqlSession = sqlSessionFactory.openSession(true);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    public TestContext(String res) {
        try {
            sqlSessionFactory = new SqlSessionFactoryBuilder().build(Resources.getResourceAsReader(res));
            sqlSession = sqlSessionFactory.openSession(true);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public TestContext init(Class... types) {
        for (Class cls : types) {
            sqlSession.getConfiguration().addMapper(cls);
        }
        return this;
    }

    public void start() {
        init(BaseAutoMapper.class);
        mapper = getMapper(BaseAutoMapper.class);
        domainDao = new DomainDaoImpl();
        domainDao.setAutoMapper(mapper);
    }


    public <T> T getMapper(Class<T> type) {
        T mapper = sqlSession.getMapper(type);
        return mapper;
    }

    public TestContext commit() {
        sqlSession.commit();
        return this;
    }


    public static void runScript(DataSource ds, String resource) {

        Connection connection = null;
        try {
            connection = ds.getConnection();
            StringWriter sw = new StringWriter();
            PrintWriter logWriter = new PrintWriter(System.out);


            StringWriter swErr = new StringWriter();
            PrintWriter logWriterErr = new PrintWriter(System.err);
            ScriptRunner runner = new ScriptRunner(connection);
            runner.setAutoCommit(true);
            runner.setStopOnError(false);
            runner.setLogWriter(logWriter);
            runner.setErrorLogWriter(logWriterErr);
            runner.setLogWriter(logWriter);
            runScript(runner, resource);
            System.out.println(swErr.toString());
        } catch (Exception e) {

        } finally {
            try {
                connection.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    public static void runScript(ScriptRunner runner, String resource) throws IOException, SQLException {
        Reader reader = Resources.getResourceAsReader(resource);
        try {
            runner.runScript(reader);
        } finally {
            reader.close();
        }
    }

    public SqlSessionFactory getSqlSessionFactory() {
        return sqlSessionFactory;
    }
}
