package com.polaris.kpi.eval.infr.task.dao;

import ch.vorburger.exec.ManagedProcessException;
import com.alibaba.fastjson.JSONObject;
import com.polaris.kpi.eval.infr.task.BaseDaoTest;
import com.polaris.kpi.eval.infr.task.ppojo.worktodo.MyScoreWorkTaskBaseOptPo;
import com.polaris.kpi.eval.infr.task.ppojo.worktodo.ScoreWorkTodoPo;
import com.polaris.kpi.eval.infr.task.query.worktodo.ScoreWorkTodoQuery;
import com.polaris.kpi.eval.infr.task.query.worktodo.WorkTodoQuery;
import com.polaris.sdk.common.JsonFileTool;
import org.junit.*;
import org.junit.jupiter.api.DisplayName;

import java.util.Arrays;
import java.util.List;

public class WorkTodoDaoTest extends BaseDaoTest {

    protected static WorkTodoDao todoDao = new WorkTodoDao();
    protected String companyId = "ece4e403-43aa-47f2-bb19-a0dd18b8e98d";

    @BeforeClass
    public static void beforeClass() throws Exception {
        BaseDaoTest.beforeClass("com/polaris/kpi/eval/infr/task/dao/WorkTodoDaoTest/WorkTodoDaoData.sql");
        System.out.println("完成beforeClass");
    }

    @Before
    public void setUp() throws Exception {
        super.setDao(todoDao, "domainDao");
    }

    @After
    public void tearDown() throws Exception {

    }

    @AfterClass
    public static void clean() throws ManagedProcessException {
        db.run("drop database acs");
        db.stop();
    }

    @Test
    public void listMyTodoTask() {
        WorkTodoQuery query = new WorkTodoQuery();
        query.setScene("wait_score");
        query.buildQueryParam();
        query.accOp(companyId, "1090038");
        List<MyScoreWorkTaskBaseOptPo> optPos = todoDao.listMyTodoTask(query);
        for (MyScoreWorkTaskBaseOptPo optPo : optPos) {
            Assert.assertNotNull(optPo.getTaskName());
            Assert.assertNotNull(optPo.getId());
        }
        System.out.println("listMyTodoTask.size:" + optPos.size());
        Assert.assertTrue(optPos.size() > 0);
    }

    @Test
    public void listMyTodoTask2() {
        System.out.println("listMyTodoTask2");
    }

    @Test
    public void listMyTodoTask3() {
        System.out.println("listMyTodoTask3");
    }

    @Test
    @DisplayName("pagedMyScoreTodoTask")
    public void pagedMyScoreTodoTask() {
        //ScoreWorkTodoQuery query = ScoreWorkTodoQuery.builder().companyId(companyId)
        //        .orgIds(Arrays.asList("1011037")).performanceType(1).opEmpId("1090038")
        //        .build();
        ScoreWorkTodoQuery query = new ScoreWorkTodoQuery();
        query.setOrgIds(Arrays.asList("1011037"));
        query.setPerformanceType(1);
        query.accOp(companyId, "1090038");
        query.setPageNo(1);
        query.setPageSize(40);
        query.setEvalEmpName("路飞");
        List<ScoreWorkTodoPo> optPos = todoDao.pagedMyScoreTodoTask(query);
        System.out.println("返回size:" + optPos.size());
        System.out.println("返回内容:" + JSONObject.toJSONString(optPos));
        String expected = JsonFileTool.readString("com/polaris/kpi/eval/infr/task/dao/WorkTodoDaoTest/listMyTodoTask.json");
        Assert.assertEquals("返回条数不同:", 2, optPos.size());
        Assert.assertEquals("返回内容不同", expected, JSONObject.toJSONString(optPos));
    }

    @Test
    public void pagedWaitScoreEmpEval() {
    }
}