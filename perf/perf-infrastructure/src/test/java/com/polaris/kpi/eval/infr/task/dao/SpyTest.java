package com.polaris.kpi.eval.infr.task.dao;

import com.polaris.test.base.TestContext;
import org.hamcrest.core.IsEqual;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
 import static org.mockito.Mockito.*;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.infr.task.dao
 * @Author: lufei
 * @CreateTime: 2023-03-02  08:08
 * @Description: TODO
 * @Version: 1.0
 */
@RunWith(MockitoJUnitRunner.class)
public class SpyTest {

    protected static TestContext context;
    protected static OnboardTimerConfDao timerConfDao = new OnboardTimerConfDao();
    protected static String companyId = "ece4e403-43aa-47f2-bb19-a0dd18b8e98d";


    @BeforeClass
    public static void beforeClass() throws Exception {
        context = new TestContext("com/polaris/kpi/base/db.properties");
        //context.runScript("com/polaris/kpi/base/baseDDL.sql");
        //context.runScript("com/polaris/kpi/eval/infr/task/dao/OnboardTimerConfDao/OnboardTimerConfDaoDDL.sql");
        //context.runScript("com/polaris/kpi/eval/infr/task/dao/OnboardTimerConfDao/OnboardTimerConfDaoData.sql");
        context.confDomainDaoFor(timerConfDao, "dao");
    }

    @Test
    public void name() {
        DomainDaoImpl dao = context.getDomainDao();
        DomainDaoImpl spy = Mockito.spy(dao);
        //Mockito.doReturn("90000").when(spy).nextLongAsStr("a");
        Mockito.doReturn("90000").when(spy).nextLongAsStr("a");

        Mockito.doReturn("90000").when(spy).nextLongAsStr("a");
        //Mockito.when(spy.nextLongAsStr(Mockito.eq("a"))).thenReturn("90000");
        String a = spy.nextLongAsStr("a");
        System.out.println(a);
        Assert.assertThat(a, IsEqual.equalTo("90000"));
    }

    @Spy
    private SpyClass spy = new SpyClass("10000");

    //@Test
    //public void testLinkedListSpyCorrect() {
    //    // 让我们来模拟一个LinkedList
    //    List<String> list = new LinkedList<>();
    //    List<String> spy = spy(list);
    //    // 必须使用doReturn来插桩
    //    doReturn("foo").when(spy).get(0);
    //    assertEquals("foo", spy.get(0));
    //}

    @Test
    public void spy2() {
        Mockito.doReturn("xxxx").when(spy).nextLongAsStr("a");
        String a = spy.nextLongAsStr("a");
        System.out.println(a);
    }


    public static class SpyClass {
        private String value;

        public SpyClass(String value) {
            this.value = value;
        }

        public String nextLongAsStr(String seqName) {
            System.out.println("nextLongAsStr");
            return value;
        }
    }


    // 返回多个值的示例
    @Test
    public void testMoreThanOneReturnValue()  {
        Iterator<String> i= mock(Iterator.class);
        when(i.next()).thenReturn("Mockito").thenReturn("rocks");
        String result= i.next()+" "+i.next();
        //assert
        //assertEquals("Mockito rocks", result);
    }
}
