package com.polaris.kpi.eval.infr.cycle.repimpl;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.cycle.entity.SaveTopResultOfCycle;
import com.polaris.kpi.eval.domain.cycle.entity.TopResultOfCycle;
import com.polaris.kpi.eval.infr.cycle.dao.PerfStatisticRuleDao;
import com.polaris.kpi.eval.infr.cycle.dao.TopResultOfCycleDao;
import com.polaris.kpi.eval.infr.cycle.query.TopResultOfCycleQuery;
import com.polaris.sdk.type.TenantId;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.dao.DomainDaoImpl;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/11 16:37
 */
public class TopResultOfCycleRepoTest {
    protected TestContext context;
    private TopResultOfCycleDao topResultOfCycleDao;
    private TopResultOfCycleRepoImpl topResultOfCycleRepo;
    protected PerfStatisticRuleRepoImpl perfStatisticRuleRepo;
    protected PerfStatisticRuleDao perfStatisticRuleDao;
    protected final static TenantId companyId = new TenantId("5a031297-1b38-48ae-bc82-375849835203") ;
    protected final static String createdUser = "1390043" ;


    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        DomainDaoImpl domainDao = context.getDomainDao();
        topResultOfCycleDao = new TopResultOfCycleDao();
        topResultOfCycleDao.setDomainDao(domainDao);

        topResultOfCycleRepo = new TopResultOfCycleRepoImpl();
        topResultOfCycleRepo.setDomainDao(domainDao);

        perfStatisticRuleRepo = new PerfStatisticRuleRepoImpl();
        perfStatisticRuleRepo.setDomainDao(domainDao);

        perfStatisticRuleDao = new PerfStatisticRuleDao();
        perfStatisticRuleDao.setDomainDao(domainDao);
    }

    @Test
    public void addTopResultOfCycleBatch(){
        String jsonStr = "{\"cycleEnd\":\"2023-03-31\",\"cycleId\":\"1814401\",\"cycleStart\":\"2023-03-01\",\"opEmpId\":\"1003028\",\"tenantId\":{\"id\":\"5a031297-1b38-48ae-bc82-375849835203\"},\"resultList\":[{\"empId\":\"1003028\",\"finalScore\":99,\"orgName\":\"后端\",\"atOrgNamePath\":\"绩效测试公司|研发部|后端\",\"year\":\"2023\",\"stepId\":\"2006275\",\"evaluationLevel\":\"A\",\"avatar\":\"\",\"sort\":0,\"version\":0,\"orgId\":\"25dd56ee-d438-494b-9bdc-12538316b721\",\"atOrgCodePath\":\"|a03b54a8-2c86-4dba-ba74-b28d806f9fab|1003901|25dd56ee-d438-494b-9bdc-12538316b721|\",\"stepName\":\"A\",\"empName\":\"小杨\",\"taskName\":\"0214-超时Bug验证-申诉超时\",\"value\":\"2\",\"taskId\":\"1811620\"}]}\n";
        List<String> cycleIds = Collections.singletonList("1814401");
        SaveTopResultOfCycle saveTopResultOfCycle = JSONUtil.toBean(jsonStr, SaveTopResultOfCycle.class);
        TopResultOfCycleQuery query = new TopResultOfCycleQuery();
        query.setCycleIds(cycleIds);
        query.setEmpIds(Collections.singletonList("1003028"));
        List<TopResultOfCycle> result = topResultOfCycleDao.listByCycleIdsAndRuleId(query);

        topResultOfCycleRepo.addTopResultOfCycleBatch(saveTopResultOfCycle);
        List<TopResultOfCycle> result2 = topResultOfCycleDao.listByCycleIdsAndRuleId(query);
        Assert.assertTrue(CollUtil.isEmpty(result));
        Assert.assertTrue(CollUtil.isNotEmpty(result2));
    }

    @Test
    public void delByCycleIdAndRuleId(){
        topResultOfCycleRepo.delByCycleIdAndRuleId("1814401",null,1);
    }
}
