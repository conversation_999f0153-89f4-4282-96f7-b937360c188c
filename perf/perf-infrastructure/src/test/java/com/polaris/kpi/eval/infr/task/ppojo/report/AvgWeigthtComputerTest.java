package com.polaris.kpi.eval.infr.task.ppojo.report;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.polaris.kpi.report.domain.dmsvc.AvgWeigthtComputer;
import com.polaris.kpi.report.domain.entity.ReportWeightSetting;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class AvgWeigthtComputerTest {

    @Test
    public void compute() {
        //初始化权重数据
        List<ReportWeightSetting> weights = JSONArray.parseArray("[{\"month\":1,\"weight\":8.33},{\"month\":2,\"weight\":8.33},{\"month\":3,\"weight\":8.33},{\"month\":4,\"weight\":8.33},{\"month\":5,\"weight\":8.33},{\"month\":6,\"weight\":8.33},{\"month\":7,\"weight\":8.33},{\"month\":8,\"weight\":8.33},{\"month\":9,\"weight\":8.33},{\"month\":10,\"weight\":8.33},{\"month\":11,\"weight\":8.33},{\"month\":12,\"weight\":8.37}]", ReportWeightSetting.class);
        Map<Integer, BigDecimal> weightMap = new HashMap<>();
        for (ReportWeightSetting weight : weights) {
            weightMap.put(weight.getMonth(), weight.getWeight());
        }
        List<BigDecimal> scores = Arrays.asList(new BigDecimal("10"), null, new BigDecimal("12"), new BigDecimal("13"), null, null, null, null, null, null, null, null);
        AvgWeigthtComputer computer = new AvgWeigthtComputer(scores, weightMap);
        computer.compute();
        System.out.println(JSON.toJSON(computer.getElements()));
    }

    @Test
    public void compute2() {
        //初始化权重数据
        List<ReportWeightSetting> weights = JSONArray.parseArray("[{\"month\":1,\"weight\":25},{\"month\":2,\"weight\":25},{\"month\":3,\"weight\":25},{\"month\":4,\"weight\":25}]", ReportWeightSetting.class);
        Map<Integer, BigDecimal> weightMap = new HashMap<>();
        for (ReportWeightSetting weight : weights) {
            weightMap.put(weight.getMonth(), weight.getWeight());
        }
        List<BigDecimal> scores = Arrays.asList(new BigDecimal("10"), null, new BigDecimal("10"), null);
        AvgWeigthtComputer computer = new AvgWeigthtComputer(scores, weightMap);
        computer.compute();
        System.out.println(JSON.toJSON(computer.getElements()));
        System.out.println(computer.getScore(0));
        System.out.println(computer.getScore(1));
        System.out.println(computer.getScore(2));
        System.out.println(computer.getScore(3));
    }
}