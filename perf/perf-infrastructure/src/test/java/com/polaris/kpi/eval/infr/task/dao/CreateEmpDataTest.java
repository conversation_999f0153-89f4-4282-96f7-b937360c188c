package com.polaris.kpi.eval.infr.task.dao;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.polaris.kpi.org.infr.dept.dao.KpiOrgDao;
import com.polaris.kpi.org.infr.emp.pojo.EmpRefOrgDo;
import com.polaris.kpi.org.infr.emp.pojo.EmployeeBaseInfoDo;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.dao.DomainDaoImpl;

import java.util.Date;
import java.util.UUID;

public class CreateEmpDataTest {
    protected TestContext context;
    protected KpiOrgDao kpiOrgDao;
    private DomainDaoImpl domainDao;

    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        kpiOrgDao = new KpiOrgDao();
        kpiOrgDao.setDomainDao(context.getDomainDao());
        domainDao = context.getDomainDao();
    }

    //创建大量员工信息用于考核
    @Test
    public void createEmp() {
//        String orgId = "1026704";// 1026201
//        extracted("红","1026705",100);//红-100
//        extracted("绿","1026701",200);//红-100
//        extracted("蓝","1026702",300);//红-100
//        extracted("黄","1026703",400);//红-100
        extracted("紫","1026704",500);//红-100
    }

    private void extracted(String namePre,String orgId,int cnt) {
        for (int i = 1; i < cnt; i++) {
            EmployeeBaseInfoDo infoDo = new EmployeeBaseInfoDo();
            String name = namePre + i + "号";
            infoDo.setName(name);
            String companyId = "5a031297-1b38-48ae-bc82-************";
            infoDo.setCompanyId(companyId);
            String id = domainDao.nextLongAsStr("employee");
            infoDo.setCreatedTime(new Date());
            infoDo.setIsDelete("false");

            infoDo.setId(id);
            infoDo.setEmployeeId(id);
            infoDo.setAccountId(id);
            infoDo.setOnTheJobStatus(2);
            infoDo.setType("2");
            infoDo.setStatus("on_the_job");
            infoDo.setOnTheJobStatus(2);
            infoDo.setEntryDate(new Date());
            String nameChinese = PinyinUtil.getPinyin(name, "")
                    + infoDo.getName()
                    + PinyinUtil.getFirstLetter(name, "");
            infoDo.setNameChinese(nameChinese);
            domainDao.save(infoDo);
            EmpRefOrgDo ref = new EmpRefOrgDo();
            ref.setEmpId(id);
            ref.setOrgId(orgId);
            ref.setId(UUID.randomUUID().toString());
            ref.setCompanyId(companyId);
            ref.setCreatedTime(new Date());
            ref.setRefType("org");
            domainDao.save(ref);
        }
        context.commit();
    }

    public static void main(String[] args) {
        System.out.println(PinyinUtil.getPinyin("你好"));
    }
}
