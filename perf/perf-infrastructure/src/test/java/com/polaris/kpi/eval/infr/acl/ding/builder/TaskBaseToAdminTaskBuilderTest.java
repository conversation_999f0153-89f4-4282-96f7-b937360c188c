package com.polaris.kpi.eval.infr.acl.ding.builder;

import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.ScoreViewConf;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskBaseDo;
import org.junit.Assert;
import org.junit.Test;


public class TaskBaseToAdminTaskBuilderTest {

    @Test
    public void build() {
        PerfEvaluateTaskBaseDo taskDo = new PerfEvaluateTaskBaseDo();
        taskDo.setCompanyId("1");
        taskDo.setCreatedUser("10001");
        taskDo.setTaskName("taskName");
        taskDo.setCycleId("cycleId01");
        taskDo.setTemplAffirmJson("{\"auditList\":[],\"companyId\":\"6c871db3-df23-4c8b-ac9d-a988af12506c\",\"confirmFlag\":\"false\",\"confirmLTDay\":10,\"createdTime\":1683274568000,\"createdUser\":\"f30da8bc-674d-474d-a43f-79cf40bfddf7\",\"id\":\"1146121\",\"isDeleted\":\"false\",\"newFlag\":\"true\",\"noChangeSkipFlag\":\"false\",\"openConfirmLT\":0,\"templBaseId\":\"3920e0b5-e306-4598-89ea-c71cce86964c\",\"updatedTime\":1683274568000,\"updatedUser\":\"f30da8bc-674d-474d-a43f-79cf40bfddf7\"}");
        taskDo.setTemplExecuteJson("{\"auditList\":[],\"auditOpen\":0,\"changeFlag\":\"true\",\"changeUser\":\"admin\",\"companyId\":\"6c871db3-df23-4c8b-ac9d-a988af12506c\",\"createdTime\":1683274568000,\"createdUser\":\"f30da8bc-674d-474d-a43f-79cf40bfddf7\",\"id\":\"1145076\",\"isDeleted\":\"false\",\"new\":false,\"templBaseId\":\"3920e0b5-e306-4598-89ea-c71cce86964c\",\"updatedTime\":1683274568000,\"updatedUser\":\"f30da8bc-674d-474d-a43f-79cf40bfddf7\"}");
        taskDo.setTemplBaseJson("{\"affirmSignature\":\"true\",\"appealReceiver\":\"[{\\\"obj_type\\\":\\\"taskAdmin\\\",\\\"objItems\\\":[]}]\",\"autoResultAffirm\":\"true\",\"autoResultAffirmDay\":2,\"baseScore\":0.00,\"canAppeal\":\"true\",\"canAppealDay\":10,\"checkItemWeight\":100,\"checkItemWeightFlag\":\"true\",\"cycleType\":\"month\",\"evaluateType\":\"simple\",\"exceedFullScore\":\"false\",\"id\":\"3920e0b5-e306-4598-89ea-c71cce86964c\",\"isNewEmp\":0,\"matchChildOrg\":0,\"name\":\"【2022年供应链开发部】KPI考核\",\"newEmp\":false,\"onboardAutoStart\":0,\"onboardCycleCnt\":0,\"onboardCycleUnit\":0,\"pointsRule\":\"close\",\"post\":\"\",\"publicDimension\":3,\"publicEmpJson\":\"[{\\\"objItems\\\":[],\\\"obj_type\\\":\\\"taskAdmin\\\"}]\",\"publicToEmp\":\"[{\\\"obj_type\\\":\\\"emp\\\"},{\\\"obj_type\\\":\\\"scoreEmp\\\"}]\",\"publicType\":\"auto\",\"resultAffirm\":\"true\",\"resultAppealNode\":70,\"resultConfirmType\":50,\"scoreRangeType\":\"weightScore\",\"status\":\"published\",\"templDesc\":\"月度考核\",\"typeWeightLimitFlag\":\"true\",\"typeWeightSwitch\":\"close\",\"version\":0}");
        taskDo.setTemplEvaluateJson("{\"appointScoreViewRule\":\"{}\",\"auditFlag\":\"true\",\"commentFlag\":\"required\",\"companyId\":\"6c871db3-df23-4c8b-ac9d-a988af12506c\",\"createdTime\":1683274568000,\"createdUser\":\"f30da8bc-674d-474d-a43f-79cf40bfddf7\",\"enterScoreEmpType\":1,\"enterScoreMethod\":\"auto\",\"evaluateType\":\"simple\",\"finalAuditList\":[{\"approvalOrder\":1,\"approverInfo\":\"f30da8bc-674d-474d-a43f-79cf40bfddf7\",\"approverType\":\"user\",\"companyId\":\"6c871db3-df23-4c8b-ac9d-a988af12506c\",\"createdTime\":1683274568000,\"createdUser\":\"f30da8bc-674d-474d-a43f-79cf40bfddf7\",\"id\":\"e437445b-f8e5-4387-9d75-8a321e81ac27\",\"isDeleted\":\"false\",\"multipleReviewersType\":\"or\",\"scene\":\"final_result_audit\",\"tempBaseId\":\"3920e0b5-e306-4598-89ea-c71cce86964c\",\"transferFlag\":\"true\",\"updatedTime\":1683274568000,\"vacancyApproverType\":\"superior\"}],\"id\":\"33fbc1e0-dc5a-4183-b58f-08235fa163e4\",\"isAddAuditComment\":0,\"isDeleted\":\"false\",\"peerScoreList\":[],\"plusOrSubComment\":0,\"scoreStartRuleDay\":1,\"scoreStartRuleType\":\"before\",\"scoreSummarySwitch\":-1,\"selfScoreFlag\":\"true\",\"selfScoreRule\":\"item\",\"selfScoreViewRule\":\"{\\\"mutual\\\":\\\"\\\",\\\"superior\\\":\\\"score,attach\\\",\\\"appoint\\\":\\\"\\\",\\\"examinee\\\":\\\"score,attach\\\"}\",\"selfScoreWeight\":0.00,\"subScoreList\":[],\"submitLevelFlag\":\"false\",\"superiorScoreFlag\":\"true\",\"superiorScoreList\":[{\"approvalOrder\":1,\"approverInfo\":\"43e3b61c-b118-4a65-84f5-bd492af118a5\",\"approverType\":\"user\",\"companyId\":\"6c871db3-df23-4c8b-ac9d-a988af12506c\",\"createdTime\":1683274567000,\"createdUser\":\"f30da8bc-674d-474d-a43f-79cf40bfddf7\",\"empId\":\"\",\"id\":\"05580320-7ae1-432b-b631-6d9abdf98844\",\"isDefault\":\"\",\"isDeleted\":\"false\",\"kpiItemId\":\"\",\"kpiTypeId\":\"\",\"multipleReviewersType\":\"or\",\"scene\":\"superior_score\",\"scoreRule\":\"\",\"superiorScoreWeight\":70.00,\"tempBaseId\":\"3920e0b5-e306-4598-89ea-c71cce86964c\",\"transferFlag\":\"true\",\"vacancyApproverInfo\":\"\",\"vacancyApproverType\":\"superior\"},{\"approvalOrder\":2,\"approverInfo\":\"f30da8bc-674d-474d-a43f-79cf40bfddf7\",\"approverType\":\"user\",\"companyId\":\"6c871db3-df23-4c8b-ac9d-a988af12506c\",\"createdTime\":1683274567000,\"createdUser\":\"f30da8bc-674d-474d-a43f-79cf40bfddf7\",\"empId\":\"\",\"id\":\"7147ceec-0289-4cb7-8e42-0eb4cf2cb3ec\",\"isDefault\":\"\",\"isDeleted\":\"false\",\"kpiItemId\":\"\",\"kpiTypeId\":\"\",\"multipleReviewersType\":\"or\",\"scene\":\"superior_score\",\"scoreRule\":\"\",\"superiorScoreWeight\":10.00,\"tempBaseId\":\"3920e0b5-e306-4598-89ea-c71cce86964c\",\"transferFlag\":\"true\",\"vacancyApproverInfo\":\"\",\"vacancyApproverType\":\"superior\"},{\"approvalOrder\":3,\"approverInfo\":\"043829d6-5fdc-42a5-b1ef-63c9f745ba92\",\"approverType\":\"user\",\"companyId\":\"6c871db3-df23-4c8b-ac9d-a988af12506c\",\"createdTime\":1683274567000,\"createdUser\":\"f30da8bc-674d-474d-a43f-79cf40bfddf7\",\"empId\":\"\",\"id\":\"b6a07d67-bf48-4908-b895-edbfd7be65ac\",\"isDefault\":\"\",\"isDeleted\":\"false\",\"kpiItemId\":\"\",\"kpiTypeId\":\"\",\"multipleReviewersType\":\"or\",\"scene\":\"superior_score\",\"scoreRule\":\"\",\"superiorScoreWeight\":20.00,\"tempBaseId\":\"3920e0b5-e306-4598-89ea-c71cce86964c\",\"transferFlag\":\"true\",\"vacancyApproverInfo\":\"\",\"vacancyApproverType\":\"superior\"}],\"superiorScoreOrder\":\"inTurn\",\"superiorScoreRule\":\"item\",\"superiorScoreViewRule\":\"{\\\"anonymous\\\":\\\"false\\\",\\\"appoint\\\":\\\"\\\",\\\"examinee\\\":\\\"score,attach\\\",\\\"mutual\\\":\\\"\\\",\\\"superior\\\":\\\"score,attach\\\"}\",\"superiorScoreWeight\":100.00,\"templBaseId\":\"3920e0b5-e306-4598-89ea-c71cce86964c\",\"typeWeightLimitFlag\":\"true\",\"typeWeightSwitch\":\"close\",\"updatedTime\":1683274568000}");
        taskDo.setPublicEmpJson("[{\"objItems\":[],\"obj_type\":\"taskAdmin\"}]");
        taskDo.setPublicType("auto");
        TaskBaseToAdminTaskBuilder builder = new TaskBaseToAdminTaskBuilder(taskDo);
        ScoreViewConf scoreView = builder.getAdminTask().getScoreView();
        Assert.assertEquals("{\"superior\":\"score,attach\",\"mutual\":\"\",\"appoint\":\"\",\"examinee\":\"score,attach\"}", JSONUtil.toJsonStr(scoreView.getSuperiorScoreViewRule()));
        System.out.println(JSONUtil.toJsonStr(scoreView.getSuperiorScoreViewRule()));
    }

    @Test
    public void name() {
        //PerfTemplEvaluate  eval =
        // JSONUtil.parseObj(superiorScoreViewRule).toBean(ViewRule.class);
    }
}