package com.polaris.kpi;

import com.perf.www.common.token.TokenInfo;
import org.junit.Test;

public class TokenTest {
    @Test
    public void createToken() {
        String par = "14070406\t1011500\t\t14070406\t张欣蕊";//
        // String par ="5ef7c3a9-a664-436b-9d24-406208a38f09\t13a87c4a-6e5e-4910-b356-e1fa644155d7\t\ta162a978-b1d8-4f7e-8513-880b92d69c9b\t袁良华";//
        final String[] ary = par.split("\t");
        TokenInfo tokenInfo = new TokenInfo(ary[2], "1090038","ece4e403-43aa-47f2-bb19-a0dd18b8e98d", 7200 * 30000, "main");
        System.out.println(tokenInfo.getToken());
    }

    @Test
    public void parseToken() {
        TokenInfo tokenInfo1 = TokenInfo.parseToken(
                "tx7Reb2V28XEnAo5t+SIjMtRL0KM4WfRYGv7YfhdhD3EM5aTd5XyB1rY64eESpkxwwQ1/fGyyX/BNx+r0/TaricKdPpb1IBHgTtFmXhek7Y="
        );
        System.out.println(tokenInfo1);
        System.out.println(new TokenInfo(tokenInfo1.getAccountId(),tokenInfo1.getEmpId(),tokenInfo1.getCompanyId(),tokenInfo1.getExpire(),"main").getToken());

    }
}
