package com.polaris.kpi.eval.infr.cycle.dao;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.cycle.entity.PerfStatisticRule;
import com.polaris.kpi.eval.infr.cycle.ppojo.PerfStatisticRuleEmpPo;
import com.polaris.sdk.type.TenantId;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.dao.DomainDaoImpl;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/25 18:16
 */
public class PerfStatisticRuleDaoTest{
    protected TestContext context;
    private PerfStatisticRuleDao perfStatisticRuleDao;
    protected final static TenantId companyId = new TenantId("5a031297-1b38-48ae-bc82-375849835203") ;
    protected final static String createdUser = "1390043" ;


    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        DomainDaoImpl domainDao = context.getDomainDao();
        perfStatisticRuleDao = new PerfStatisticRuleDao();
        perfStatisticRuleDao.setDomainDao(domainDao);
    }

    @Test
    public void existRuleName(){
        boolean existRuleName = perfStatisticRuleDao.existRuleName(companyId, "100501", "连续绩差规则222");
        System.out.println(existRuleName);
    }

    @Test
    public void findRuleById(){
        String id = "100501";
        PerfStatisticRule ruleById = perfStatisticRuleDao.findRuleById(companyId, id);
        Assert.assertTrue(ObjectUtil.isNotNull(ruleById));
    }

    @Test
    public void getRuleById(){
        String id = "100501";
        PerfStatisticRuleEmpPo ruleById = perfStatisticRuleDao.getRuleById(companyId, id);
        Assert.assertTrue(ObjectUtil.isNotNull(ruleById));
    }

    @Test
    public void listPerfStatisticRule(){
        List<PerfStatisticRuleEmpPo> statisticRuleList = perfStatisticRuleDao.listPerfStatisticRule(companyId,null,null);
        System.out.println(JSONUtil.toJsonStr(statisticRuleList));
    }


    @Test
    public void listRuleByExcludeIds(){
        List<String> list = new ArrayList<>();
        list.add(null);
        perfStatisticRuleDao.listRuleByExcludeIds(companyId,"year",2,1, list);
    }

    
    
    
}
