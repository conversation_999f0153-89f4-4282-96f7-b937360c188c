package com.polaris.kpi.eval.infr.pip;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.pip.plan.entity.PipPlan;
import com.polaris.kpi.eval.infr.pip.plan.dao.PipPlanDao;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

public class PipPlanDaoTest {
    protected TestContext context;
    protected PipPlanDao pipPlanDao;


    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        pipPlanDao = new PipPlanDao();
        pipPlanDao.setDomainDao(context.getDomainDao());
    }

    @Test
    public void getPipPlanBase() {
        PipPlan plan = pipPlanDao.getPipPlanBase("ece4e403-43aa-47f2-bb19-a0dd18b8e98d","1003902");
        Assert.assertTrue(Objects.nonNull(plan));
        System.out.println(JSONUtil.toJsonStr(plan));
    }

    @Test
    public void listPipPlanBase() {
        List<String> planIds = Arrays.asList("1003902","1003903");
        List<PipPlan> plans = pipPlanDao.listPipPlanBase("ece4e403-43aa-47f2-bb19-a0dd18b8e98d",planIds);
        Assert.assertTrue(plans.size() ==2);
        System.out.println(JSONUtil.toJsonStr(plans));
    }
    @Test
    public void existPipPlanName() {
        boolean bool = pipPlanDao.existPipPlanName("ece4e403-43aa-47f2-bb19-a0dd18b8e98d","张三改进计划",null);
        Assert.assertTrue(!bool);
        System.out.println(bool);
    }
}