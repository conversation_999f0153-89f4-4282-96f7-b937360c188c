package com.polaris.kpi.eval.infr.pip;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.pip.plan.entity.PipEvalRule;
import com.polaris.kpi.eval.infr.pip.plan.dao.PipEvalRuleDao;
import com.polaris.kpi.eval.infr.pip.plan.ppojo.PipEvalRulePo;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

public class PipEvalRuleDaoTest {
    protected TestContext context;
    protected PipEvalRuleDao pipEvalRuleDao;


    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        pipEvalRuleDao = new PipEvalRuleDao();
        pipEvalRuleDao.setDomainDao(context.getDomainDao());
    }

    @Test
    public void getPipEvalRuleBase() {
        PipEvalRule evalRuleBase = pipEvalRuleDao.getPipEvalRuleBase("5a031297-1b38-48ae-bc82-375849835203", "1000105");
        System.out.println(JSONUtil.toJsonStr(evalRuleBase));
    }

    @Test
    public void listPipEvalRule() {
        List<PipEvalRule>  evalRules = pipEvalRuleDao.listPipEvalRule("5a031297-1b38-48ae-bc82-375849835203", Arrays.asList("1000105"));
        System.out.println(JSONUtil.toJsonStr(evalRules));
    }
    @Test
    public void getPipEvalRule() {
        PipEvalRulePo rule = pipEvalRuleDao.getPipEvalRule("5a031297-1b38-48ae-bc82-375849835203", "1000105");
        System.out.println(JSONUtil.toJsonStr(rule));
    }
}