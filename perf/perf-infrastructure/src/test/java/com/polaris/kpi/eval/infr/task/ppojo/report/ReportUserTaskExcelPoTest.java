package com.polaris.kpi.eval.infr.task.ppojo.report;

import cn.com.polaris.kpi.company.TenantSysConf;
import cn.hutool.json.JSONUtil;
import com.perf.www.vo.task.item.PerfEvaluateTaskItemDynamicVO;
import com.polaris.kpi.eval.domain.task.entity.EvalKpi;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskScoreResultDo;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskScoreResultPo;
import org.junit.Assert;
import org.junit.Test;

import java.util.*;


public class ReportUserTaskExcelPoTest {

    @Test
    public void repaceOrgNamePath() {
        ReportUserTaskExcelPo excelPo = new ReportUserTaskExcelPo();
        excelPo.setAtOrgNamePath("沃克科技有限公司|融云北极星的合作伙伴|新人入职|三级部门");
        excelPo.repaceOrgNamePath();
        Assert.assertTrue(Objects.equals(excelPo.getOrgName1(),"融云北极星的合作伙伴"));
        Assert.assertTrue(Objects.equals(excelPo.getOrgName2(),"新人入职"));
        Assert.assertTrue(Objects.equals(excelPo.getOrgName3(),"四级部门"));
    }
    @Test
    public void notOkrItemFinishValueUpLogs() {
        String log="[{\"content\":\"{\\\"kpiItemName\\\":\\\"销售合同额\\\",\\\"comment\\\":\\\"安全管理评分项2\\\"}\",\"empAvatar\":\"https://static-legacy.dingtalk.com/media/lADPM5HikoKm7UnNAWPNAUQ_324_355.jpg\",\"businessScene\":\"add_ind_ext_score_item\",\"afterValue\":\"-5\",\"empName\":\"思威\",\"createdTime\":\"2023-09-05 17:08:00\"}]\n";
        List<PerfEvaluateTaskItemDynamicVO> itemDynamicVOs = JSONUtil.toList(log, PerfEvaluateTaskItemDynamicVO.class);
        ReportUserTaskExcelPo excelPo = new ReportUserTaskExcelPo();
        String logs = excelPo.notOkrItemFinishValueUpLogs(itemDynamicVOs);
        System.out.println(logs);
    }

    @Test
    public void builderRes() {
        ReportUserTaskExcelPo excelPo = new ReportUserTaskExcelPo();
        excelPo.setAtOrgNamePath("沃克科技有限公司|融云北极星的合作伙伴|新人入职|三级部门");
        excelPo.setKpiTypeClassify("");
        String confJson ="{\"updatedTime\":1724920783000,\"confCode\":\"childAdminSeeAnonymous_20240110\",\"updatedUser\":\"\",\"confContent\":\"\",\"version\":0,\"confDesc\":\"子管理员可查看已匿名评分人信息\",\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"createdTime\":1723792429000,\"open\":0,\"createdUser\":\"\"}";
        TenantSysConf conf = JSONUtil.toBean(confJson,TenantSysConf.class);
        Set<String> userIds = new HashSet<>();
        String downUrl = "https://topscrm.oss-cn-hangzhou.aliyuncs.com";
        String resultsJson ="[{\"empId\":\"1009187\",\"updatedTime\":1730282945000,\"kpiItemId\":\"4110698d-0b97-4e2f-a57a-851a2700e28b\",\"approvalOrder\":1,\"scorerName\":\"李志旭\",\"scoreWeight\":100,\"mergeRsInfos\":[],\"scorerId\":\"1009187\",\"taskUserId\":\"1287101\",\"version\":0,\"orgId\":\"1010909\",\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"isDeleted\":\"false\",\"reviewersType\":\"or\",\"kpiTypeId\":\"7ceb5ad4-685f-46f0-b029-4a2ae817db25\",\"createdTime\":1730282945000,\"id\":\"0804911a-1393-43f0-a6cb-8bce56b5781c\",\"taskId\":\"1636901\",\"createdUser\":\"1009187\",\"scorerType\":\"self_score\"},{\"empId\":\"1009187\",\"updatedTime\":1730282945000,\"kpiItemId\":\"a08a8d9c-cbd9-4196-9498-a50fdfaebdda\",\"approvalOrder\":1,\"scorerName\":\"李志旭\",\"scoreWeight\":100,\"mergeRsInfos\":[],\"scorerId\":\"1009187\",\"taskUserId\":\"1287101\",\"version\":0,\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"isDeleted\":\"false\",\"reviewersType\":\"or\",\"kpiTypeId\":\"1e9bddd5-8a91-4209-ab27-a02684589557\",\"createdTime\":1730282945000,\"id\":\"3030f7f3-170a-4b3b-ac4f-3b1627db2385\",\"taskId\":\"1636901\",\"createdUser\":\"1009187\",\"scorerType\":\"self_score\"},{\"empId\":\"1009187\",\"updatedTime\":1730282945000,\"kpiItemId\":\"45a34c79-6354-4909-9c49-24ad4b38a705\",\"approvalOrder\":1,\"scorerName\":\"李志旭\",\"scoreWeight\":100,\"mergeRsInfos\":[],\"scorerId\":\"1009187\",\"taskUserId\":\"1287101\",\"version\":0,\"orgId\":\"1010909\",\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"isDeleted\":\"false\",\"reviewersType\":\"or\",\"kpiTypeId\":\"7ceb5ad4-685f-46f0-b029-4a2ae817db25\",\"createdTime\":1730282945000,\"id\":\"8fc28f7a-507a-4c3b-bd17-04e9974032d6\",\"taskId\":\"1636901\",\"createdUser\":\"1009187\",\"scorerType\":\"self_score\"},{\"empId\":\"1009187\",\"updatedTime\":1730282945000,\"kpiItemId\":\"f2c4b885-c08f-4053-9f6d-7273299c5c99\",\"approvalOrder\":1,\"scorerName\":\"李志旭\",\"scoreWeight\":100,\"mergeRsInfos\":[],\"scorerId\":\"1009187\",\"taskUserId\":\"1287101\",\"version\":0,\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"isDeleted\":\"false\",\"reviewersType\":\"or\",\"kpiTypeId\":\"1e9bddd5-8a91-4209-ab27-a02684589557\",\"createdTime\":1730282945000,\"id\":\"a995e6fe-31f6-466b-92c1-20ad28e4f924\",\"taskId\":\"1636901\",\"createdUser\":\"1009187\",\"scorerType\":\"self_score\"},{\"empId\":\"1009187\",\"updatedTime\":1729165276000,\"approvalOrder\":1,\"scorerName\":\"李志旭\",\"scoreWeight\":100,\"mergeRsInfos\":[],\"scorerId\":\"1009187\",\"modifyFlag\":\"true\",\"taskUserId\":\"1287101\",\"version\":1,\"orgId\":\"\",\"taskAuditId\":\"1986503\",\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"isDeleted\":\"false\",\"reviewersType\":\"or\",\"auditStatus\":\"pass\",\"createdTime\":1729165266000,\"id\":\"aecca42c-3c46-4285-a530-7d880ce38b24\",\"taskId\":\"1636901\",\"createdUser\":\"1009187\",\"scorerType\":\"modify_item_audit\"},{\"empId\":\"1009187\",\"updatedTime\":1730282945000,\"kpiItemId\":\"147152d1-fa85-4420-96bf-e6d50697445b\",\"approvalOrder\":1,\"scorerName\":\"李志旭\",\"scoreWeight\":100,\"mergeRsInfos\":[],\"scorerId\":\"1009187\",\"taskUserId\":\"1287101\",\"version\":0,\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"isDeleted\":\"false\",\"reviewersType\":\"or\",\"kpiTypeId\":\"1e9bddd5-8a91-4209-ab27-a02684589557\",\"createdTime\":1730282945000,\"id\":\"aedc7552-5ae6-4e12-ab21-263a6e91bd3d\",\"taskId\":\"1636901\",\"createdUser\":\"1009187\",\"scorerType\":\"self_score\"}]";
        List< PerfEvaluateTaskScoreResultPo > results= JSONUtil.toList(resultsJson,PerfEvaluateTaskScoreResultPo.class);
        List< PerfEvaluateTaskItemDynamicVO > itemDynamicVOs =new ArrayList<>();
        //{"1287101":"自评:李志旭\n"}
        Map<String, String> map = new HashMap<>();
        map.put("1287101","自评:李志旭\\n");
        Map<String, String> refMap = new HashMap<>();
        //{"1287101":"指定人录入+OKR"}
        Map<String, String> ruleMap = new HashMap<>();
        ruleMap.put("1287101","指定人录入+OKR\\n");
        PerfEvaluateTaskScoreResultDo calibration = null;

        String updateOkrItemJson ="{\"alreadyNodes\":[],\"empId\":\"1009187\",\"itemType\":\"non-measurable\",\"itemFinishValue\":64,\"itemUnit\":\"%\",\"actionValueSet\":\"[{\\\"key\\\":\\\"targetValue\\\",\\\"name\\\":\\\"起始值\\\",\\\"nameEn\\\":\\\"Initial value\\\",\\\"value\\\":\\\"0\\\",\\\"required\\\":true},{\\\"key\\\":\\\"targetValue1\\\",\\\"name\\\":\\\"承诺值\\\",\\\"nameEn\\\":\\\"Commitment value\\\",\\\"value\\\":\\\"\\\",\\\"required\\\":false},{\\\"key\\\":\\\"targetEndValue\\\",\\\"name\\\":\\\"挑战值\\\",\\\"nameEn\\\":\\\"Challenge value\\\",\\\"value\\\":\\\"100\\\",\\\"required\\\":true}]\",\"taskUserId\":\"1287101\",\"finishValueAudit\":[{\"objItems\":[],\"objType\":\"role\"},{\"objItems\":[],\"objType\":\"user\"},{\"objItems\":[],\"objType\":\"manager\"}],\"finishValue\":64,\"kpiItemName\":\"目标达成率提高10%\",\"isDeleted\":\"false\",\"krSuperiorContent\":\"888888\",\"createdTime\":1730282941000,\"finalSubmitFinishValue\":0,\"id\":\"83d8a40e-4c05-4834-991b-937fa4d8b23d\",\"kpiTypeWeight\":0,\"itemWeight\":7,\"createdUser\":\"1009187\",\"scorerType\":\"exam\",\"order\":1,\"updatedTime\":1730282941000,\"kpiItemId\":\"a08a8d9c-cbd9-4196-9498-a50fdfaebdda\",\"krSelfContent\":\"3333\",\"okrActionUpdates\":[{\"lastActionProgressStatus\":\"behind\",\"targetName\":\"打造高绩效的北极星团队\",\"actionProgressStatus\":\"behind\",\"lastTargetProgress\":75.9,\"lastFinishValue\":64,\"taskProgressId\":\"add4818f-f3e7-463c-bb8b-30f26c482880\",\"taskTargetsId\":\"24811475-1c4f-4e1b-b604-bb30acfe258d\",\"type\":\"updateKpi\",\"content\":\"\",\"finishValue\":64,\"targetProgress\":75.9,\"targetUnit\":\"%\",\"actionContent\":\"目标达成率提高10%\",\"createdBy\":\"\",\"actionId\":\"7761247f-fe7c-466c-80f9-0d0e55d3ff35\",\"createdTime\":1726284355000,\"days\":46,\"progress\":64,\"confidenceIndex\":4,\"id\":\"3d292861-6be8-4ff2-b7ae-6eeb57e3b062\"},{\"lastActionProgressStatus\":\"behind\",\"targetName\":\"打造高绩效的北极星团队\",\"actionProgressStatus\":\"behind\",\"lastTargetProgress\":75.9,\"lastFinishValue\":64,\"taskProgressId\":\"add4818f-f3e7-463c-bb8b-30f26c482880\",\"taskTargetsId\":\"24811475-1c4f-4e1b-b604-bb30acfe258d\",\"type\":\"updateKpi\",\"content\":\"\",\"finishValue\":64,\"targetProgress\":75.9,\"targetUnit\":\"%\",\"actionContent\":\"目标达成率提高10%\",\"createdBy\":\"\",\"actionId\":\"7761247f-fe7c-466c-80f9-0d0e55d3ff35\",\"createdTime\":1726107386000,\"days\":48,\"progress\":64,\"confidenceIndex\":4,\"id\":\"059d624b-201a-47d4-8527-4a1db32cb35d\"},{\"lastActionProgressStatus\":\"behind\",\"targetName\":\"打造高绩效的北极星团队\",\"actionProgressStatus\":\"behind\",\"lastTargetProgress\":75.9,\"lastFinishValue\":64,\"taskProgressId\":\"add4818f-f3e7-463c-bb8b-30f26c482880\",\"taskTargetsId\":\"24811475-1c4f-4e1b-b604-bb30acfe258d\",\"type\":\"updateKpi\",\"content\":\"\",\"finishValue\":64,\"targetProgress\":75.9,\"targetUnit\":\"%\",\"actionContent\":\"目标达成率提高10%\",\"createdBy\":\"\",\"actionId\":\"7761247f-fe7c-466c-80f9-0d0e55d3ff35\",\"createdTime\":1722776104000,\"days\":87,\"progress\":64,\"confidenceIndex\":4,\"id\":\"590c9373-79e0-4a2e-8ff8-ccf1cd3b8a65\"},{\"lastActionProgressStatus\":\"behind\",\"targetName\":\"打造高绩效的北极星团队\",\"actionProgressStatus\":\"behind\",\"lastTargetProgress\":75.9,\"lastFinishValue\":64,\"taskProgressId\":\"add4818f-f3e7-463c-bb8b-30f26c482880\",\"taskTargetsId\":\"24811475-1c4f-4e1b-b604-bb30acfe258d\",\"type\":\"updateKpi\",\"content\":\"\",\"finishValue\":64,\"targetProgress\":75.9,\"targetUnit\":\"%\",\"actionContent\":\"目标达成率提高10%\",\"createdBy\":\"\",\"actionId\":\"7761247f-fe7c-466c-80f9-0d0e55d3ff35\",\"createdTime\":1722776016000,\"days\":87,\"progress\":64,\"confidenceIndex\":4,\"id\":\"83393510-9f2a-48c9-9fb8-da6a2891e336\"},{\"lastActionProgressStatus\":\"behind\",\"targetName\":\"打造高绩效的北极星团队\",\"actionProgressStatus\":\"behind\",\"lastTargetProgress\":75.9,\"lastFinishValue\":64,\"taskProgressId\":\"add4818f-f3e7-463c-bb8b-30f26c482880\",\"taskTargetsId\":\"24811475-1c4f-4e1b-b604-bb30acfe258d\",\"type\":\"updateKpi\",\"content\":\"6575685\",\"finishValue\":64,\"targetProgress\":75.9,\"targetUnit\":\"%\",\"actionContent\":\"目标达成率提高10%\",\"createdBy\":\"\",\"actionId\":\"7761247f-fe7c-466c-80f9-0d0e55d3ff35\",\"createdTime\":1722754314000,\"days\":87,\"progress\":64,\"confidenceIndex\":4,\"id\":\"487936ff-6e8e-4b2b-8151-fdd1c173321c\"},{\"lastActionProgressStatus\":\"behind\",\"targetName\":\"打造高绩效的北极星团队\",\"actionProgressStatus\":\"behind\",\"lastTargetProgress\":75.9,\"lastFinishValue\":64,\"taskProgressId\":\"add4818f-f3e7-463c-bb8b-30f26c482880\",\"taskTargetsId\":\"24811475-1c4f-4e1b-b604-bb30acfe258d\",\"type\":\"updateKpi\",\"content\":\"\",\"finishValue\":64,\"targetProgress\":75.9,\"targetUnit\":\"%\",\"actionContent\":\"目标达成率提高10%\",\"createdBy\":\"\",\"actionId\":\"7761247f-fe7c-466c-80f9-0d0e55d3ff35\",\"createdTime\":1722583538000,\"days\":89,\"progress\":64,\"confidenceIndex\":4,\"id\":\"7231dc89-2a89-4a2b-a128-314171948893\"},{\"lastActionProgressStatus\":\"behind\",\"targetName\":\"打造高绩效的北极星团队\",\"actionProgressStatus\":\"behind\",\"lastTargetProgress\":75.9,\"lastFinishValue\":64,\"taskProgressId\":\"add4818f-f3e7-463c-bb8b-30f26c482880\",\"taskTargetsId\":\"24811475-1c4f-4e1b-b604-bb30acfe258d\",\"type\":\"updateKpi\",\"content\":\"\",\"finishValue\":64,\"targetProgress\":75.9,\"targetUnit\":\"%\",\"actionContent\":\"目标达成率提高10%\",\"createdBy\":\"\",\"actionId\":\"7761247f-fe7c-466c-80f9-0d0e55d3ff35\",\"createdTime\":1722583162000,\"days\":89,\"progress\":64,\"confidenceIndex\":4,\"id\":\"9a637b9a-e964-414e-b50a-812813851a38\"},{\"lastActionProgressStatus\":\"behind\",\"targetName\":\"打造高绩效的北极星团队\",\"actionProgressStatus\":\"behind\",\"lastTargetProgress\":75.9,\"lastFinishValue\":64,\"taskProgressId\":\"add4818f-f3e7-463c-bb8b-30f26c482880\",\"taskTargetsId\":\"24811475-1c4f-4e1b-b604-bb30acfe258d\",\"type\":\"updateKpi\",\"content\":\"\",\"finishValue\":64,\"targetProgress\":75.9,\"targetUnit\":\"%\",\"actionContent\":\"目标达成率提高10%\",\"createdBy\":\"\",\"actionId\":\"7761247f-fe7c-466c-80f9-0d0e55d3ff35\",\"createdTime\":1722430604000,\"days\":91,\"progress\":64,\"confidenceIndex\":4,\"id\":\"25052bc5-a9f3-4939-89f1-018d04fadd8c\"},{\"lastActionProgressStatus\":\"behind\",\"targetName\":\"打造高绩效的北极星团队\",\"actionProgressStatus\":\"behind\",\"lastTargetProgress\":75.9,\"lastFinishValue\":64,\"taskProgressId\":\"add4818f-f3e7-463c-bb8b-30f26c482880\",\"taskTargetsId\":\"24811475-1c4f-4e1b-b604-bb30acfe258d\",\"type\":\"updateKpi\",\"content\":\"\",\"finishValue\":64,\"targetProgress\":75.9,\"targetUnit\":\"%\",\"actionContent\":\"目标达成率提高10%\",\"createdBy\":\"\",\"actionId\":\"7761247f-fe7c-466c-80f9-0d0e55d3ff35\",\"createdTime\":1722430594000,\"days\":91,\"progress\":64,\"confidenceIndex\":4,\"id\":\"80ef9621-19ed-44e6-95b1-a6c7c784d483\"},{\"lastActionProgressStatus\":\"behind\",\"targetName\":\"打造高绩效的北极星团队\",\"actionProgressStatus\":\"behind\",\"lastTargetProgress\":75.9,\"lastFinishValue\":64,\"taskProgressId\":\"add4818f-f3e7-463c-bb8b-30f26c482880\",\"taskTargetsId\":\"24811475-1c4f-4e1b-b604-bb30acfe258d\",\"type\":\"updateKpi\",\"content\":\"\",\"finishValue\":64,\"targetProgress\":75.9,\"targetUnit\":\"%\",\"actionContent\":\"目标达成率提高10%\",\"createdBy\":\"\",\"actionId\":\"7761247f-fe7c-466c-80f9-0d0e55d3ff35\",\"createdTime\":1722429504000,\"days\":91,\"progress\":64,\"confidenceIndex\":4,\"id\":\"52da655d-fe0c-4a4f-b64d-bf7e7d6d28c6\"},{\"lastActionProgressStatus\":\"behind\",\"targetName\":\"打造高绩效的北极星团队\",\"actionProgressStatus\":\"behind\",\"lastTargetProgress\":75.9,\"lastFinishValue\":64,\"taskProgressId\":\"add4818f-f3e7-463c-bb8b-30f26c482880\",\"taskTargetsId\":\"24811475-1c4f-4e1b-b604-bb30acfe258d\",\"type\":\"updateKpi\",\"content\":\"\",\"finishValue\":64,\"targetProgress\":75.9,\"targetUnit\":\"%\",\"actionContent\":\"目标达成率提高10%\",\"createdBy\":\"\",\"actionId\":\"7761247f-fe7c-466c-80f9-0d0e55d3ff35\",\"createdTime\":1722429440000,\"days\":91,\"progress\":64,\"confidenceIndex\":4,\"id\":\"3933069e-8033-40f2-8894-99a66001bad2\"},{\"lastActionProgressStatus\":\"behind\",\"createdByName\":\"李志旭\",\"targetName\":\"打造高绩效的北极星团队\",\"actionProgressStatus\":\"behind\",\"lastTargetProgress\":75.9,\"lastFinishValue\":64,\"taskProgressId\":\"add4818f-f3e7-463c-bb8b-30f26c482880\",\"taskTargetsId\":\"24811475-1c4f-4e1b-b604-bb30acfe258d\",\"avatar\":\"https://static-legacy.dingtalk.com/media/lQLPDhtwSlDwYpfNAmDNAmCwP2waI3J1qnYCiKZNDcDTAA_608_608.png\",\"type\":\"update\",\"content\":\"33\",\"finishValue\":64,\"targetProgress\":75.9,\"targetUnit\":\"%\",\"actionContent\":\"目标达成率提高10%\",\"createdBy\":\"ed3b222a-85ec-4614-994b-cf3f1073e4c2\",\"actionId\":\"7761247f-fe7c-466c-80f9-0d0e55d3ff35\",\"createdTime\":1715306839000,\"days\":173,\"progress\":64,\"confidenceIndex\":4,\"id\":\"df00b477-1788-4553-88fa-61fc2d9cc581\"},{\"lastActionProgressStatus\":\"smoothly\",\"createdByName\":\"李志旭\",\"targetName\":\"打造高绩效的北极星团队\",\"actionProgressStatus\":\"behind\",\"lastTargetProgress\":69.3,\"lastFinishValue\":44,\"taskProgressId\":\"add4818f-f3e7-463c-bb8b-30f26c482880\",\"taskTargetsId\":\"24811475-1c4f-4e1b-b604-bb30acfe258d\",\"avatar\":\"https://static-legacy.dingtalk.com/media/lQLPDhtwSlDwYpfNAmDNAmCwP2waI3J1qnYCiKZNDcDTAA_608_608.png\",\"changeIndex\":1,\"type\":\"update\",\"content\":\"饭打撒发撒旦法萨芬阿发发大水发生发顺丰啊四方达所发生的法术法谁发的阿萨法大事发生懂法守法大沙发\",\"finishValue\":64,\"targetProgress\":75.9,\"targetUnit\":\"%\",\"actionContent\":\"目标达成率提高10%\",\"createdBy\":\"ed3b222a-85ec-4614-994b-cf3f1073e4c2\",\"actionId\":\"7761247f-fe7c-466c-80f9-0d0e55d3ff35\",\"createdTime\":1715306731000,\"days\":173,\"progress\":64,\"confidenceIndex\":4,\"id\":\"c21693e9-1df2-4691-9617-66a23de9f17c\"},{\"createdByName\":\"李志旭\",\"targetName\":\"打造高绩效的北极星团队\",\"actionProgressStatus\":\"smoothly\",\"lastTargetProgress\":0,\"lastFinishValue\":0,\"taskProgressId\":\"add4818f-f3e7-463c-bb8b-30f26c482880\",\"taskTargetsId\":\"24811475-1c4f-4e1b-b604-bb30acfe258d\",\"avatar\":\"https://static-legacy.dingtalk.com/media/lQLPDhtwSlDwYpfNAmDNAmCwP2waI3J1qnYCiKZNDcDTAA_608_608.png\",\"type\":\"update\",\"content\":\"\",\"finishValue\":44,\"targetProgress\":14.5,\"targetUnit\":\"%\",\"actionContent\":\"目标达成率提高10%\",\"createdBy\":\"ed3b222a-85ec-4614-994b-cf3f1073e4c2\",\"actionId\":\"7761247f-fe7c-466c-80f9-0d0e55d3ff35\",\"createdTime\":1715248838000,\"days\":174,\"progress\":44,\"confidenceIndex\":3,\"id\":\"261212cd-7ee8-43d1-898c-1ddea6341ad5\"}],\"krSuperiorScore\":88.7,\"okrRefFlag\":\"true\",\"appointAudits\":[],\"version\":0,\"openOkrScore\":0,\"isOkr\":\"true\",\"companyId\":{\"id\":\"5a031297-1b38-48ae-bc82-375849835203\"},\"resultInputType\":\"no\",\"kpiTypeName\":\"OKR\",\"itemTargetValue\":0,\"kpiTypeId\":\"1e9bddd5-8a91-4209-ab27-a02684589557\",\"okrScore\":88.7,\"actionId\":\"7761247f-fe7c-466c-80f9-0d0e55d3ff35\",\"kpiTypeClassify\":\"custom\",\"taskId\":\"1636901\",\"krSelfScore\":33}";
        EvalKpi updateOkrItem = JSONUtil.toBean(updateOkrItemJson,EvalKpi.class);
        excelPo.builderRes(conf,userIds,downUrl,results,itemDynamicVOs,map,refMap,ruleMap,calibration,updateOkrItem);
        System.out.println(JSONUtil.toJsonStr(excelPo));
    }
}