package com.polaris.kpi.eval.infr.cycle.repimpl;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.cycle.entity.PerfStatisticRule;
import com.polaris.kpi.eval.infr.cycle.dao.PerfStatisticRuleDao;
import com.polaris.sdk.type.TenantId;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.dao.DomainDaoImpl;

import java.util.Objects;

public class PerfStatisticRuleRepoTest {
    protected TestContext context;
    protected PerfStatisticRuleRepoImpl perfStatisticRuleRepo;
    protected PerfStatisticRuleDao perfStatisticRuleDao;
    protected final static TenantId companyId = new TenantId("5a031297-1b38-48ae-bc82-375849835203") ;
    protected final static String createdUser = "1390043" ;


    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        perfStatisticRuleRepo = new PerfStatisticRuleRepoImpl();
        DomainDaoImpl domainDao = context.getDomainDao();
        perfStatisticRuleRepo.setDomainDao(domainDao);

        perfStatisticRuleDao = new PerfStatisticRuleDao();
        perfStatisticRuleDao.setDomainDao(domainDao);
    }

    @Test
    public void addPerfStatisticRule() {
        String ruleJson ="{\n" +
                "    \"ruleName\": \"连续绩差规则2\",\n" +
                "    \"ruleType\": 1,\n" +
                "    \"resNotifyOpen\": 0,\n" +
                "    \"resNotifyStaff\": [],\n" +
                "    \"pipOpen\": 0,\n" +
                "    \"ruleApplyRange\": [\n" +
                "        {\n" +
                "            \"objType\": \"dept\",\n" +
                "            \"objItems\": [\n" +
                "                {\n" +
                "                    \"objId\": \"1003901\",\n" +
                "                    \"objName\": \"研发部\"\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ],\n" +
                "    \"ruleApplyType\": 1,\n" +
                "    \"statisticDuration\": \"2\",\n" +
                "    \"cycleType\": \"month\",\n" +
                "    \"statisticRule\": [\n" +
                "        {\n" +
                "            \"statisticType\": \"grade\",\n" +
                "            \"statisticItems\": [\n" +
                "                {\n" +
                "                    \"ruleId\": \"2003905\",\n" +
                "                    \"ruleName\": \"特差\",\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        System.out.println(ruleJson);
        PerfStatisticRule perfStatisticRule = JSONUtil.toBean(ruleJson, PerfStatisticRule.class);
        perfStatisticRule.setCompanyId(companyId);
        perfStatisticRule.setCreatedUser(createdUser);
        String id = perfStatisticRuleRepo.addPerfStatisticRule(perfStatisticRule);
        Assert.assertTrue(StringUtils.isNotBlank(id));
        System.out.println(id);
        context.commit();


    }

    @Test
    public void updatePerfStatisticRule() {
        String ruleJson = "{\n" +
                "    \"ruleConfigId\": \"100501\",\n" +
                "    \"ruleName\": \"连续绩差规则666\",\n" +
                "    \"ruleType\": 1,\n" +
                "    \"resNotifyOpen\": 0,\n" +
                "    \"resNotifyStaff\": [],\n" +
                "    \"pipOpen\": 0,\n" +
                "    \"ruleApplyRange\": [\n" +
                "        {\n" +
                "            \"objType\": \"dept\",\n" +
                "            \"objItems\": [\n" +
                "                {\n" +
                "                    \"objId\": \"1003901\",\n" +
                "                    \"objName\": \"研发部\"\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ],\n" +
                "    \"ruleApplyType\": 1,\n" +
                "    \"statisticDuration\": \"2\",\n" +
                "    \"cycleType\": \"month\",\n" +
                "    \"statisticRule\": [\n" +
                "        {\n" +
                "            \"statisticType\": \"grade\",\n" +
                "            \"statisticItems\": [\n" +
                "                {\n" +
                "                    \"ruleId\": \"2003905\",\n" +
                "                    \"ruleName\": \"特差\",\n" +
                "                },\n" +
                "            ]\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        PerfStatisticRule perfStatisticRule = JSONUtil.toBean(ruleJson, PerfStatisticRule.class);
        perfStatisticRule.setCompanyId(companyId);
        perfStatisticRule.setVersion(7);
        int update = perfStatisticRuleRepo.updatePerfStatisticRule(perfStatisticRule);
        context.commit();
        System.out.println(update);
    }

    @Test
    public void delPerfStatisticRule() {
        String id = "100601";
        perfStatisticRuleRepo.delPerfStatisticRule(companyId, id);
        PerfStatisticRule ruleById = perfStatisticRuleDao.findRuleById(companyId, id);
        Assert.assertTrue(Objects.isNull(ruleById));
    }
}