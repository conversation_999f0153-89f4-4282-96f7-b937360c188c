package com.polaris.kpi.eval.infr.temp;

import cn.com.polaris.kpi.temp.TempId;
import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.temp.entity.std.StdTemp;
import com.polaris.kpi.eval.infr.task.BaseDaoTest;
import com.polaris.kpi.eval.infr.task.repimpl.TaskUserRepoImpl;
import com.polaris.kpi.eval.infr.temp.repimpl.StdTempRepoImpl;
import com.polaris.sdk.type.TenantId;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.dao.DomainDaoImpl;

/**
 * <AUTHOR>
 * @date 2024/10/29 11:17
 */
public class StdTempRepoImplTest  extends BaseDaoTest {
    private StdTempRepoImpl stdTempRepo;
    protected TestContext context;
    private DomainDaoImpl domainDao;
    private TaskUserRepoImpl taskUserRepoImpl;

    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        stdTempRepo = new StdTempRepoImpl();
        taskUserRepoImpl = new TaskUserRepoImpl();
        domainDao = context.getDomainDao();
        stdTempRepo.setDomainDao(context.getDomainDao());
        taskUserRepoImpl.setDomainDao(context.getDomainDao());
    }

    @Test
    public void loadKpi(){
        StdTemp temp = new StdTemp();
        temp.setCompanyId(new TenantId("ece4e403-43aa-47f2-bb19-a0dd18b8e98d"));
        temp.setId(new TempId("1049901"));
        StdTemp stdTemp = stdTempRepo.getStdTemp(new TenantId("ece4e403-43aa-47f2-bb19-a0dd18b8e98d"), new TempId("1049901"));
        System.out.println(JSONUtil.toJsonStr(stdTemp));


    }
}
