package com.polaris.kpi.setting.repimpl;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.infr.task.BaseDaoTest;
import com.polaris.kpi.eval.infr.task.repimpl.TaskUserRepoImpl;
import com.polaris.kpi.setting.domain.entity.ResultAuditFlow;
import com.polaris.kpi.setting.domain.entity.ResultAuditFlowUser;
import com.polaris.kpi.setting.domain.entity.TaskResultAuditSummary;
import com.polaris.sdk.type.TenantId;
import org.junit.Before;
import org.junit.jupiter.api.Test;
import org.lufei.ibatis.dao.DomainDaoImpl;

import java.util.List;

class ResultAuditFlowRepoImplTest extends BaseDaoTest {

    private ResultAuditFlowRepoImpl repo;
    protected TestContext context;
    private DomainDaoImpl domainDao;
    private TaskUserRepoImpl taskUserRepoImpl;

    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        repo = new ResultAuditFlowRepoImpl();
        taskUserRepoImpl = new TaskUserRepoImpl();
        domainDao = context.getDomainDao();
        repo.setDomainDao(context.getDomainDao());
        taskUserRepoImpl.setDomainDao(context.getDomainDao());
    }

    @Test
    void getResultAuditFlowV2() throws Exception {
        setUp();
        final ResultAuditFlow resultAuditFlowV2 = repo.getResultAuditFlowV2("5a031297-1b38-48ae-bc82-375849835203", "1634001", null);
        System.out.println(JSONUtil.toJsonStr(resultAuditFlowV2));
    }

    @Test
    void listFlowUserByTaskId() throws Exception {
        setUp();
        List<ResultAuditFlowUser> flowUsers = repo.listFlowUserByTaskId("5a031297-1b38-48ae-bc82-375849835203", "1634001");
        System.out.println(JSONUtil.toJsonStr(flowUsers));
    }

    @Test
    void getResultAuditFlowByUserId() throws Exception {
        setUp();
        ResultAuditFlow flow = repo.getResultAuditFlowByUserId("5a031297-1b38-48ae-bc82-375849835203", "1280601");
        System.out.println(JSONUtil.toJsonStr(flow));
    }


    @Test
    void getSummary() throws Exception {
        setUp();
        TaskResultAuditSummary summary = repo.getSummary("5a031297-1b38-48ae-bc82-375849835203", "1632809","1602582",4);
        System.out.println(JSONUtil.toJsonStr(summary));
    }

    @Test
    void getTaskUser() throws Exception {
        setUp();
        System.out.println(JSONUtil.toJsonStr(taskUserRepoImpl.getBaseTaskUserAndRule(new TenantId("5a031297-1b38-48ae-bc82-375849835203"),"1281101")));
    }
}