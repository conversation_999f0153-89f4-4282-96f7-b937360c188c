package com.polaris.kpi.eval.infr.cycle.repimpl;

import cn.com.seendio.polaris.code.TestContext;
import com.polaris.kpi.org.infr.emp.dao.KpiEmpDao;
import com.polaris.sdk.type.TenantId;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.dao.DomainDaoImpl;

/**
 * <AUTHOR>
 * @date 2024/10/21 9:17
 */
public class CycleRepoTest {
    protected TestContext context;
    private CycleRepoImpl cycleRepo;
    private KpiEmpDao kpiEmpDao;
    protected final static TenantId companyId = new TenantId("5a031297-1b38-48ae-bc82-375849835203") ;
    protected final static String createdUser = "1390043" ;

    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        cycleRepo = new CycleRepoImpl();
        DomainDaoImpl domainDao = context.getDomainDao();
        cycleRepo.setDomainDao(domainDao);
        kpiEmpDao = new KpiEmpDao();
        kpiEmpDao.setDomainDao(domainDao);
        cycleRepo.setKpiEmpDao(kpiEmpDao);
    }

    @Test
    public void updateArchiveStatus(){
        cycleRepo.updateArchiveStatus(companyId.getId(), "1164402", createdUser, 1, "0815-校准优化", "main");
        context.commit();
    }
}
