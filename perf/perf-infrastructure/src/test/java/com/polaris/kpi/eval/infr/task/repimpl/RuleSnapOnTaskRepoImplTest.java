package com.polaris.kpi.eval.infr.task.repimpl;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.polaris.kpi.eval.domain.cycle.type.OnTaskStatusCount;
import com.polaris.kpi.eval.domain.cycle.type.RefRuleSnapOnTask;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.calibrated.ResultRankInstance;
import com.polaris.kpi.eval.infr.cycle.ppojo.RefRuleSnapOnTaskPo;
import com.polaris.kpi.eval.infr.cycle.query.RefRuleSnapOnTaskQuery;
import com.polaris.kpi.org.domain.dept.type.TaskId;
import com.polaris.sdk.common.JsonFileTool;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.dao.AutoDaoImpl;
import org.lufei.ibatis.mapper.PagedList;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class RuleSnapOnTaskRepoImplTest {
    protected TestContext context;
    protected AutoDaoImpl domainDao;
    protected RuleSnapOnTaskRepoImpl rankRuleSnapDao = new RuleSnapOnTaskRepoImpl();

    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        domainDao = context.getDomainDao();
        rankRuleSnapDao.setDomainDao(context.getDomainDao());
    }

    @Test
    public void pagedRefRuleSnapOnTask() {
        RefRuleSnapOnTaskQuery query = new RefRuleSnapOnTaskQuery();
        query.setPageNo(1);
        query.setPageSize(10);
        query.setCompanyId("5a031297-1b38-48ae-bc82-375849835203");
        query.setCycleId("1835401");
        query.required();
        System.out.println(JSONUtil.toJsonStr(query));
        PagedList<RefRuleSnapOnTaskPo> pageds = rankRuleSnapDao.pagedRefRuleSnapOnTask(query);
        List<RefRuleSnapOnTaskPo> collect = pageds.stream().filter(r -> StrUtil.isNotBlank(r.getSnapId())).collect(Collectors.toList());
        Assert.assertTrue(collect.size() == 1);
        String real = JSONUtil.toJsonStr(collect);
        System.out.println(real);
        Assert.assertEquals("[{\"performanceType\":1,\"ruleName\":\"全公司使用\",\"taskName\":\"12月-周期-完整流程\",\"snapId\":\"1000001\",\"taskId\":\"1891001\"}]", real);


        List<RefRuleSnapOnTaskPo> blanks = pageds.stream().filter(r -> StrUtil.isBlank(r.getSnapId())).collect(Collectors.toList());
        Assert.assertTrue(blanks.size() == 9);
        MultiResponse<RefRuleSnapOnTaskPo> rs = MultiResponse.of(pageds.getData(), pageds.getPageNo(), pageds.getTotalRow(), pageds.getPageSize());
        System.out.println(JSONUtil.toJsonStr(rs));
    }

    @Test
    public void updateScoreRuleSnap() {
        ResultRankInstance newSnap = JsonFileTool.toBean("xxnewSnap.json", ResultRankInstance.class);
        rankRuleSnapDao.updateScoreRuleSnap(newSnap, new ArrayList<>(), new ArrayList<>());
        context.commit();
//        JsonFileTool.toBean("",)
//        PagedList<RefRuleSnapOnTaskPo> pageds = rankRuleSnapDao.updateScoreRuleSnap(query);

    }

    @Test
    public void listSnapMember() {
        List<EvalUser> list = rankRuleSnapDao.listSnapMember(new TenantId("5a031297-1b38-48ae-bc82-375849835203"), "10001", null);
        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void listRefRuleSnapBySnapId() {
        ListWrap<RefRuleSnapOnTask> wrap = rankRuleSnapDao.listRefRuleSnapBySnapId(new TenantId("5a031297-1b38-48ae-bc82-375849835203"), "10001");
        String jsonStr = JSONUtil.toJsonStr(wrap.getDatas());
        System.out.println(jsonStr);
        Assert.assertEquals("[{\"snapId\":\"10001\",\"taskId\":\"0005848f-caa9-4774-8d5a-833ef860884a\"}]", jsonStr);
        Assert.assertTrue(wrap.getDatas().size() == 1);
    }

    @Test
    public void saveRefRuleSnapOnTask() {
        List<RefRuleSnapOnTask> onTasks = new ArrayList<>();
        onTasks.add(new RefRuleSnapOnTask("t002", "1000001"));
        rankRuleSnapDao.saveRefRuleSnapOnTask(new TenantId("1001"), onTasks);
        context.commit();
//        context.commit();
    }

    @Test
    public void listNeedRankTaskUser() {
        List<EvalUser> list1 = rankRuleSnapDao.listNeedRankTaskUser(new TenantId("5a031297-1b38-48ae-bc82-375849835203"), "1891001");
        Assert.assertTrue(list1.isEmpty());
        List<EvalUser> list2 = rankRuleSnapDao.listNeedRankTaskUser(new TenantId("5a031297-1b38-48ae-bc82-375849835203"), "3ebc56d9-54a5-4bd5-83c2-5dd294109c4a");
        System.out.println(JSONUtil.toJsonStr(list2));
        System.out.println(list2.size() == 1);
    }

    @Test
    public void listNeedRankTaskUserT() {
        UpdateBuilder updateBuilder = UpdateBuilder.build("a_table").set("a", null)
                .whereEq("a", 3);
        System.out.println(updateBuilder.getSql());
        System.out.println(updateBuilder.getParams());
        domainDao.update(updateBuilder);
    }


    @Test
    public void getEvalStatusCount() {
        OnTaskStatusCount evalStatusCount = rankRuleSnapDao.getEvalStatusCount(new TenantId("5a031297-1b38-48ae-bc82-375849835203"), "1637083");
        Assert.assertTrue(evalStatusCount.getScoringCnt() == 1);
        Assert.assertTrue(evalStatusCount.getOnLevelCnt() == 0);
        Assert.assertTrue(evalStatusCount.getAfterScoringCnt() == 0);
    }

    @Test
    public void saveScoreRuleSnapOnTask() {

    }

    @Test
    public void listSnapMemberOnLevelOrg() {
        List<EvalUser> list = rankRuleSnapDao.listSnapMemberOnLevelOrg(new TenantId("5a031297-1b38-48ae-bc82-375849835203"), "1637083", new TaskId("1001"), 3, "|1|ab|c");
        System.out.println(list);
    }

    @Test
    public void replaceAll() {
        System.out.println("a03b54a8-2c86-4dba-ba74-b28d806f9fab|1003901|1004202".replaceAll("\\|",""));
    }




    @Test
    public void listSnapMemberOnAtOrg() {
        List<EvalUser> list = rankRuleSnapDao.listSnapMemberOnAtOrg(new TenantId("5a031297-1b38-48ae-bc82-375849835203"), "1637083", new TaskId("1001"), "aaa");
        System.out.println(list);
    }


}