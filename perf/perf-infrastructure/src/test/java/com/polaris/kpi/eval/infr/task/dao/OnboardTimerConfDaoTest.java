package com.polaris.kpi.eval.infr.task.dao;

import cn.com.polaris.kpi.eval.TimerConf;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.entity.onbord.OnboardTimerConf;
import com.polaris.kpi.eval.domain.task.entity.onbord.OnboardTimerItem;
import com.polaris.kpi.eval.domain.task.entity.onbord.TimerItemConf;
import com.polaris.kpi.eval.infr.task.ppojo.newemp.OnboardTimerConfDo;
import com.polaris.test.base.TestContext;

import static org.hamcrest.MatcherAssert.*;
import static org.hamcrest.core.IsEqual.*;

import org.hamcrest.core.IsEqual;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.concurrent.atomic.AtomicLong;

@RunWith(MockitoJUnitRunner.class)
public class OnboardTimerConfDaoTest {
    protected TestContext context;
    protected OnboardTimerConfDao timerConfDao = new OnboardTimerConfDao();
    protected String companyId = "ece4e403-43aa-47f2-bb19-a0dd18b8e98d";

    @Before
    public void setUp() throws Exception {
        context = new TestContext("com/polaris/kpi/base/db.properties");
        //context.runScript("com/polaris/kpi/base/baseDDL.sql");
        context.runScriptH2("com/polaris/kpi/eval/infr/task/dao/OnboardTimerConfDao/OnboardTimerConfDaoDDL.sql");
        //context.runScriptH2("com/polaris/kpi/eval/infr/task/dao/OnboardTimerConfDao/OnboardTimerConfDaoData.sql");
        context.confDomainDaoFor(timerConfDao, "dao");
        DomainDaoImpl spy = context.getDomainDao();
        Mockito.doReturn("100001", "100002", "100003", "100004", "100005", "100006", "100007").when(spy).nextLongAsStr(onboardTimerConf);
        //Mockito.doReturn("100001", "100002", "100003", "100004", "100005", "100006", "100007").when(spy).nextLongAsStr(onboardTimerItem);
        AtomicLong initId = new AtomicLong(100001L);
        Mockito.doAnswer(invocationOnMock -> initId.incrementAndGet() + "").when(spy).nextLongAsStr(onboardTimerItem);
    }

    private static String onboardTimerConf = "onboard_timer_conf";
    private static String onboardTimerItem = "onboard_timer_item";

    @Test
    public void saveTimerConf() {
        OnboardTimerConf conf = new OnboardTimerConf();
        conf.op(companyId, "100038");
        {
            //-- 1000001=新人任务确认 未确认/审核考核任务时，每天  11:00 提醒相关人员确认/审核
            OnboardTimerItem onboardTimerItem = new OnboardTimerItem(companyId, 100001L, new TimerConf(1, 40, 0, "11:00"), 2, 1);
            TimerItemConf timerItemConf = new TimerItemConf(1, Arrays.asList(onboardTimerItem));
            conf.setConfirmTimer(timerItemConf);
        }
        {
            //-- 1000002=提醒评分人和任务管理员，在评分开始后的第46天，每天11:00提醒评分人和任务管理员，直至完成评分 forEach执行=2,forEach分类: 每天=1, 每小时=2
            OnboardTimerItem eachItem = new OnboardTimerItem(companyId, 1000002L, new TimerConf(1, 46, 0, "11:00"), 2, 1);
            //-- 1000003=在评分开始后的第30天未评分，在7:00发送工作通知提醒评分人进行评分
            OnboardTimerItem onceItem = new OnboardTimerItem(companyId, 1000003L, new TimerConf(1, 30, 0, "7:00"), 1, 0);
            TimerItemConf timerItemConf = new TimerItemConf(1, Arrays.asList(eachItem, onceItem));
            conf.setScoringTimer(timerItemConf);
        }
        timerConfDao.saveTimerConf(conf);
        System.out.println(JSONUtil.toJsonStr(conf));
        OnboardTimerConfDo find = timerConfDao.getTimerConf(companyId);
        Assert.assertTrue(find.getId().equals(conf.getId()));
        Assert.assertTrue(find.getConfirmTimer().getTimers().size() == conf.getConfirmTimer().getTimers().size());
        Assert.assertTrue(find.getScoringTimer().getTimers().size() == conf.getScoringTimer().getTimers().size());
        Assert.assertTrue(find.getScoringTimer().getTimers().get(0).getConf().getRunTime().equals("11:00"));
    }

    @Test
    public void saveTimerForUpdate() {
        //构建数据
        OnboardTimerConf editConf = JSONUtil.parseObj("{\"confirmTimer\":{\"timers\":[{\"conf\":{\"beforDays\":0,\"afterDays\":40,\"type\":1,\"runTime\":\"11:00\"},\"version\":0,\"companyId\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\",\"onboardTimerConfId\":\"100001\",\"timerType\":2,\"isDeleted\":\"false\",\"modCode\":100001,\"eachType\":1}],\"open\":1},\"version\":0,\"companyId\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\",\"isDeleted\":\"false\",\"scoringTimer\":{\"timers\":[{\"conf\":{\"beforDays\":0,\"afterDays\":46,\"type\":1,\"runTime\":\"11:00\"},\"version\":0,\"companyId\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\",\"onboardTimerConfId\":\"100001\",\"timerType\":2,\"isDeleted\":\"false\",\"id\":\"100002\",\"modCode\":1000002,\"eachType\":1},{\"conf\":{\"beforDays\":0,\"afterDays\":30,\"type\":1,\"runTime\":\"7:00\"},\"version\":0,\"companyId\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\",\"onboardTimerConfId\":\"100001\",\"timerType\":1,\"isDeleted\":\"false\",\"id\":\"100003\",\"modCode\":1000003,\"eachType\":0}],\"open\":1},\"createdUser\":\"100038\"}\n")
                .toBean(OnboardTimerConf.class);
        editConf.setCompanyId(companyId);
        timerConfDao.saveTimerConf(editConf);

        //修改
        OnboardTimerItem onboardTimerItem = editConf.getScoringTimer().getTimers().get(0);
        onboardTimerItem.getConf().setRunTime("12:00");
        timerConfDao.saveTimerConf(editConf);

        //验证
        OnboardTimerConfDo findEdit = timerConfDao.getTimerConf(companyId);
        OnboardTimerItem findItem = findEdit.getScoringTimer().getTimers().get(0);
        OnboardTimerItem editItem = editConf.getScoringTimer().getTimers().get(0);
        assertThat(findItem.getEachType(), IsEqual.equalTo(editItem.getEachType()));
        assertThat(findItem.getTimerType(), equalTo(editItem.getTimerType()));
        assertThat(JSONUtil.toJsonStr(findItem.getConf()), equalTo(JSONUtil.toJsonStr(editItem.getConf())));
        assertThat(findItem.getConf().getRunTime(), equalTo("12:00"));
    }

    //@Test
    //public void testListTimerConf() {
    //    List<OnboardTimerConfPo> confPos = timerConfDao.listTimerConf(companyId);
    //    Assert.assertEquals("数量不对", confPos.size(), 3);
    //    Map<String, OnboardTimerConfPo> rsMap = CollUtil.toMap(confPos, new HashMap<>(), p -> p.getId());
    //    {
    //        OnboardTimerConfPo first = rsMap.get("10000");
    //        System.out.println(JSONUtil.toJsonStr(first));
    //        Assert.assertTrue("", JSONUtil.toJsonStr(first).equals("{\"conf\":{\"beforDays\":0,\"afterDays\":0,\"type\":1,\"runTime\":\"11:00\"},\"companyId\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\",\"timerType\":1,\"id\":\"10000\",\"modCode\":1000001,\"eachType\":1}"));
    //    }
    //    {
    //        OnboardTimerConfPo first = rsMap.get("10001");
    //        System.out.println(JSONUtil.toJsonStr(first));
    //        Assert.assertTrue("", JSONUtil.toJsonStr(first).equals("{\"conf\":{\"beforDays\":0,\"afterDays\":46,\"type\":1,\"runTime\":\"11:00\"},\"companyId\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\",\"timerType\":2,\"id\":\"10001\",\"modCode\":1000002,\"eachType\":1}"));
    //    }
    //    {
    //        OnboardTimerConfPo first = rsMap.get("10002");
    //        System.out.println(JSONUtil.toJsonStr(first));
    //        Assert.assertTrue("", JSONUtil.toJsonStr(first).equals("{\"conf\":{\"beforDays\":0,\"afterDays\":30,\"type\":1,\"runTime\":\"7:00\"},\"companyId\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\",\"timerType\":1,\"id\":\"10002\",\"modCode\":1000003}"));
    //    }
    //}


}