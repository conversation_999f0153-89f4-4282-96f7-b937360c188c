package com.polaris.kpi.eval.infr.task.repimpl;

import cn.com.seendio.polaris.code.TestContext;
import com.polaris.kpi.eval.domain.task.dmsvc.RejectFinishedValueDmSvc;
import com.polaris.sdk.type.TenantId;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.dao.DomainDaoImpl;

/**
 * <AUTHOR>
 * @date 2024/11/5 19:01
 */
public class TaskAuditRepoImplTest {
    protected TestContext context;
    protected TaskAuditRepoImpl taskAuditRepo;
    private TenantId tenantId = new TenantId("ece4e403-43aa-47f2-bb19-a0dd18b8e98d");

    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        taskAuditRepo = new TaskAuditRepoImpl();
        DomainDaoImpl domainDao = context.getDomainDao();
        taskAuditRepo.setDomainDao(domainDao);
    }

    @Test
    public void batchResetItemAuditStatus(){
        RejectFinishedValueDmSvc dmsvc = new RejectFinishedValueDmSvc();
        taskAuditRepo.batchResetItemAuditStatus(dmsvc, "dispatched");
        context.commit();
    }
}
