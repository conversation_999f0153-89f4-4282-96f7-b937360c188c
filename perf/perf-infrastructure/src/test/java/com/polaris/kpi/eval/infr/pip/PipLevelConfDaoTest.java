package com.polaris.kpi.eval.infr.pip;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.pip.plan.entity.PipLevelRuleConf;
import com.polaris.kpi.eval.infr.pip.plan.dao.PipLevelConfDao;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.List;
import java.util.Objects;

public class PipLevelConfDaoTest {
    protected TestContext context;
    protected PipLevelConfDao pipLevelConfDao;


    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        pipLevelConfDao = new PipLevelConfDao();
        pipLevelConfDao.setDomainDao(context.getDomainDao());
    }

    @Test
    public void listPipLevelConf() {
        List<PipLevelRuleConf>  ruleConfs = pipLevelConfDao.listPipLevelConf("ece4e403-43aa-47f2-bb19-a0dd18b8e98d");
        Assert.assertTrue(Objects.nonNull(ruleConfs));
        System.out.println(JSONUtil.toJsonStr(ruleConfs));
    }

    @Test
    public void existLevelRuleName() {
        boolean bool = pipLevelConfDao.existLevelRuleName("ece4e403-43aa-47f2-bb19-a0dd18b8e98d","评价组1",null);
        Assert.assertTrue(!bool);
        System.out.println(bool);
    }
}