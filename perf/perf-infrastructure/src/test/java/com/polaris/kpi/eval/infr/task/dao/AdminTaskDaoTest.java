package com.polaris.kpi.eval.infr.task.dao;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskBaseDo;
import com.polaris.kpi.eval.infr.task.query.admin.AdminTaskQuery;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.dao.DomainDaoImpl;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/18 10:25
 */
public class AdminTaskDaoTest {
    protected TestContext context;
    private AdminTaskDao adminTaskDao;
    protected final static TenantId companyId = new TenantId("5a031297-1b38-48ae-bc82-375849835203") ;
    protected final static String createdUser = "1009069" ;


    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        DomainDaoImpl domainDao = context.getDomainDao();
        adminTaskDao = new AdminTaskDao();
        adminTaskDao.setDomainDao(domainDao);
    }

    @Test
    public void pagedAdminTask(){
        AdminTaskQuery query = new AdminTaskQuery();
        query.setPerformanceType(1);
        query.setEnd(1);
        query.setTenantId(companyId);
        query.setOpEmpId(new EmpId(createdUser));
        query.setPageNo(1);
        query.setPageSize(20);
        adminTaskDao.pagedAdminTask(query);
    }

    @Test
    public void pagedManageEvalTask(){
        AdminTaskQuery query = new AdminTaskQuery();
        query.setPerformanceType(1);
        query.setEnd(1);
        query.setTenantId(companyId);
        query.setOpEmpId(new EmpId(createdUser));
        query.setPageNo(1);
        query.setPageSize(20);
        adminTaskDao.pagedManageEvalTask(query);
    }

    @Test
    public void getTaskById(){
        PerfEvaluateTaskBaseDo byTaskId = adminTaskDao.getByTaskId(companyId.getId(), "1632605");
        System.out.println(JSONUtil.toJsonStr(byTaskId));
    }

    @Test
    public void listCycleUnArchivedTask(){
        List<PerfEvaluateTaskBaseDo> unArchivedTask = adminTaskDao.listCycleUnArchivedTask(companyId.getId(), "1165910");
        System.out.println(JSONUtil.toJsonStr(unArchivedTask));
    }

    @Test
    public void countHasAppealUser(){
        System.out.println(adminTaskDao.countHasAppealUser("ece4e403-43aa-47f2-bb19-a0dd18b8e98d", "1024826"));
    }
}
