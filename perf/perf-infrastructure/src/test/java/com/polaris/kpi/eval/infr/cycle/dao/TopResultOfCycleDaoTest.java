package com.polaris.kpi.eval.infr.cycle.dao;

import cn.com.polaris.kpi.ObjItem;
import cn.com.polaris.kpi.eval.StaffConfItem;
import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.cycle.entity.PerfStatisticRelatedUser;
import com.polaris.kpi.eval.domain.cycle.entity.PerfStatisticRule;
import com.polaris.kpi.eval.domain.cycle.entity.SaveTopResultOfCycle;
import com.polaris.kpi.eval.domain.cycle.entity.TopResultOfCycle;
import com.polaris.kpi.eval.domain.cycle.type.DefaultPerfStatisticRuleEnum;
import com.polaris.kpi.eval.domain.task.entity.Cycle;
import com.polaris.kpi.eval.infr.cycle.ppojo.PerfStatisticRuleEmpPo;
import com.polaris.kpi.eval.infr.cycle.query.TopResultOfCycleQuery;
import com.polaris.kpi.eval.infr.task.dao.CycleDao;
import com.polaris.kpi.org.infr.dept.dao.KpiOrgDao;
import com.polaris.sdk.type.TenantId;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.dao.DomainDaoImpl;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/11 12:00
 */
public class TopResultOfCycleDaoTest {
    protected TestContext context;
    private PerfStatisticRuleDao perfStatisticRuleDao;
    private TopResultOfCycleDao topResultOfCycleDao;
    protected final static TenantId companyId = new TenantId("5a031297-1b38-48ae-bc82-375849835203") ;
    protected final static String createdUser = "1390043" ;
    protected final static String deptId = "a03b54a8-2c86-4dba-ba74-b28d806f9fab" ;
    protected final static String cycleId = "1814401" ;
    private KpiOrgDao kpiOrgDao;
    private CycleDao cycleDao;


    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        DomainDaoImpl domainDao = context.getDomainDao();
        perfStatisticRuleDao = new PerfStatisticRuleDao();
        perfStatisticRuleDao.setDomainDao(domainDao);
        kpiOrgDao = new KpiOrgDao();
        kpiOrgDao.setDomainDao(domainDao);
        topResultOfCycleDao = new TopResultOfCycleDao();
        topResultOfCycleDao.setDomainDao(domainDao);
        cycleDao = new CycleDao();
        cycleDao.setDomainDao(domainDao);
    }

    @Test
    public void countTaskTopResultByRules(){
        PerfStatisticRuleEmpPo perfStatisticRule = perfStatisticRuleDao.getRuleById(companyId, "101001");

        // 根据规则，查规则下面适用的员工列表
        Set<String> deptIdList = new HashSet<>();
        Set<String> roleIdList = new HashSet<>();
        Set<String> fixEmpIdList = new HashSet<>();
        List<TopResultOfCycle> historyTopResults = new ArrayList<>();

        List<StaffConfItem> ruleApplyRange = perfStatisticRule.getRuleApplyRange();
        for (StaffConfItem staffConfItem : ruleApplyRange) {
            if (staffConfItem.isDeptType()) {
                List<String> deptIds = staffConfItem.getObjItems().stream().map(ObjItem::getObjId).collect(Collectors.toList());
                deptIdList.addAll(deptIds);
            }
            if (staffConfItem.isRoleType()) {
                List<String> roleIds = staffConfItem.getObjItems().stream().map(ObjItem::getObjId).collect(Collectors.toList());
                roleIdList.addAll(roleIds);
            }
            if (staffConfItem.isFixEmpType()) {
                List<String> fixEmpIds = staffConfItem.getObjItems().stream().map(ObjItem::getObjId).collect(Collectors.toList());
                fixEmpIdList.addAll(fixEmpIds);
            }
        }
        boolean isOraEval = perfStatisticRule.getRuleApplyType() == 1 ? false : true;
        TopResultOfCycleQuery query = new TopResultOfCycleQuery();
        query.setOrgEval(isOraEval);
        //个人绩效,查部门下/角色下的人
        if (!isOraEval) {
            Set<String> empIdSet = new HashSet<>();
            if (CollUtil.isNotEmpty(deptIdList)) {
                List<String> allChildDeptIdList = kpiOrgDao.listAllChildOrgIds(companyId, new ArrayList<>(deptIdList));
                List<PerfStatisticRelatedUser> userByOrgIds = perfStatisticRuleDao.queryRelatedUserByOrgIds(companyId, allChildDeptIdList);
                if (CollUtil.isNotEmpty(userByOrgIds)) {
                    empIdSet.addAll(userByOrgIds.stream().map(PerfStatisticRelatedUser::getEmployeeId).collect(Collectors.toSet()));
                }
            }
            if (CollUtil.isNotEmpty(roleIdList)) {
                List<String> allChildDeptIdList = kpiOrgDao.listAllChildOrgIds(companyId, new ArrayList<>(deptIdList));
                List<PerfStatisticRelatedUser> userByRoleIds = perfStatisticRuleDao.queryRelatedUserByRoleIds(companyId, allChildDeptIdList);
                if (CollUtil.isNotEmpty(userByRoleIds)) {
                    empIdSet.addAll(userByRoleIds.stream().map(PerfStatisticRelatedUser::getEmployeeId).collect(Collectors.toSet()));
                }
            }

            historyTopResults = topResultOfCycleDao.countTaskTopResultByRules(empIdSet, Collections.singletonList("1816403"), perfStatisticRule.getStatisticRule(), isOraEval);

        }
        System.out.println(JSONUtil.toJsonStr(historyTopResults));
    }

    @Test
    public void countHistoryTopResultByEmpIds(){
        TopResultOfCycleQuery query = new TopResultOfCycleQuery();
        Cycle cycle = cycleDao.find(companyId.getId(), cycleId);
        PerfStatisticRuleEmpPo perfStatisticRule = perfStatisticRuleDao.getRuleById(companyId, "101001");
        Map<String, List<Cycle>> cycleMap = cycleDao.mapConsecutiveCycleByYearValue(
                companyId.getId(), perfStatisticRule.getCycleType(), cycle.getYear(), cycle.getValue(), perfStatisticRule.getStatisticDuration());
        // 同周期，只需要有任意一个任务满足绩优/绩差条件，则是连续的
        for (String yearAndValue : cycleMap.keySet()) {
            List<String> cycleIds = cycleMap.get(yearAndValue).stream().map(Cycle::getId).collect(Collectors.toList());
            query.setCycleIds(cycleIds);
            query.setEmpIds(Collections.singletonList("1003028"));
            query.setRuleConfigId(perfStatisticRule.getRuleConfigId());
            query.setStatisticRule(perfStatisticRule.getStatisticRule());

            List<TopResultOfCycle> result = topResultOfCycleDao.countHistoryTopResultByEmpIds(query);
            System.out.println(JSONUtil.toJsonStr(result));
        }
    }

    @Test
    public void listByCycleIdsAndRuleId(){
        TopResultOfCycleQuery query = new TopResultOfCycleQuery();
        Cycle cycle = cycleDao.find(companyId.getId(), cycleId);
        PerfStatisticRuleEmpPo perfStatisticRule = perfStatisticRuleDao.getRuleById(companyId, "101001");
        Map<String, List<Cycle>> cycleMap = cycleDao.mapConsecutiveCycleByYearValue(
                companyId.getId(), perfStatisticRule.getCycleType(), cycle.getYear(), cycle.getValue(), perfStatisticRule.getStatisticDuration());
        // 同周期，只需要有任意一个任务满足绩优/绩差条件，则是连续的
        for (String yearAndValue : cycleMap.keySet()) {
            List<String> cycleIds = cycleMap.get(yearAndValue).stream().map(Cycle::getId).collect(Collectors.toList());
            query.setCycleIds(cycleIds);
            query.setEmpIds(Collections.singletonList("1003028"));
            query.setRuleConfigId(perfStatisticRule.getRuleConfigId());
            query.setStatisticRule(perfStatisticRule.getStatisticRule());

            List<TopResultOfCycle> result = topResultOfCycleDao.listByCycleIdsAndRuleId(query);
            System.out.println(JSONUtil.toJsonStr(result));
        }
    }

    @Test
    public void countTaskTopResultByDefaultRule(){
        Cycle cycle = cycleDao.find(companyId.getId(), cycleId);
        List<PerfStatisticRule> statisticRuleList = DefaultPerfStatisticRuleEnum.getByCycleType(cycle.getType());
        for (PerfStatisticRule perfStatisticRule : statisticRuleList) {
        Set<String> empIdSet = new HashSet<>();
        empIdSet.add("1003028");
        // 根据默认规则，统计绩优/绩差员工列表
        List<TopResultOfCycle> result = topResultOfCycleDao.countTaskTopResultByDefaultRule(empIdSet, Collections.singletonList("1816403"), perfStatisticRule.getRuleType());
        System.out.println(JSONUtil.toJsonStr(result));
        }

    }

    @Test
    public void countHistoryTopResultByDefaultRule(){
        TopResultOfCycleQuery query = new TopResultOfCycleQuery();
        Cycle cycle = cycleDao.find(companyId.getId(), cycleId);
        PerfStatisticRule perfStatisticRule = DefaultPerfStatisticRuleEnum.getByCycleTypeAndRuleType(cycle.getType(), 1);
        Map<String, List<Cycle>> cycleMap = cycleDao.mapConsecutiveCycleByYearValue(
                companyId.getId(), perfStatisticRule.getCycleType(), cycle.getYear(), cycle.getValue(), perfStatisticRule.getStatisticDuration());
        // 同周期，只需要有任意一个任务满足绩优/绩差条件，则是连续的
        for (String yearAndValue : cycleMap.keySet()) {
            List<String> cycleIds = cycleMap.get(yearAndValue).stream().map(Cycle::getId).collect(Collectors.toList());
            query.setCycleIds(cycleIds);
            query.setEmpIds(Collections.singletonList("1003028"));
            query.setRuleConfigId(perfStatisticRule.getRuleConfigId());
            query.setStatisticRule(perfStatisticRule.getStatisticRule());

            List<TopResultOfCycle> result = topResultOfCycleDao.countHistoryTopResultByDefaultRule(query,perfStatisticRule.getRuleType());
            System.out.println(JSONUtil.toJsonStr(result));
            System.out.println("------");
            SaveTopResultOfCycle saveData = new SaveTopResultOfCycle();
            saveData.setResultList(result)
                    .setCycleId(cycleId)
                    .setCycleStart(cycle.getCycleStart())
                    .setCycleEnd(cycle.getCycleEnd())
                    .setRuleConfigId(perfStatisticRule.getRuleConfigId())
                    .setRuleType(perfStatisticRule.getRuleType())
                    .setTenantId(companyId)
                    .setOpEmpId(createdUser);
            System.out.println(saveData);
            System.out.println(JSONUtil.toJsonStr(saveData));
        }

    }

    @Test
    public void listRuleByCycleIdAndPerfType(){
        List<PerfStatisticRule> resultList = topResultOfCycleDao.listRuleByCycleIdAndPerfType("1166301",1);
        System.out.println(JSONUtil.toJsonStr(resultList));
    }
}
