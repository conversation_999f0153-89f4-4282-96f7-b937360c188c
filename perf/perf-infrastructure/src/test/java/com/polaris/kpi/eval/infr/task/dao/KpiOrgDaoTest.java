package com.polaris.kpi.eval.infr.task.dao;

import cn.com.polaris.kpi.EmpStaff;
import cn.com.polaris.kpi.eval.LevelManager;
import cn.com.polaris.kpi.eval.Rater;
import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.polaris.acl.kpi.eval.domain.EvalEmp;
import com.polaris.kpi.eval.domain.task.entity.EvalAudit;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.EmpEvalByStatusPo;
import com.polaris.kpi.eval.infr.task.query.KpiEmpQuery;
import com.polaris.kpi.eval.infr.task.query.empeval.EmpEvalAtCycleQuery;
import com.polaris.kpi.eval.infr.task.query.empeval.EmpEvalAtTaskQuery2;
import com.polaris.kpi.org.infr.dept.dao.KpiOrgDao;
import com.polaris.kpi.org.infr.dept.pojo.CurOrgStructPo;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.lufei.ibatis.mapper.PagedList;

import java.util.ArrayList;
import java.util.List;

public class KpiOrgDaoTest {
    protected TestContext context;
    protected KpiOrgDao kpiOrgDao;
    private DomainDaoImpl domainDao;


    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        kpiOrgDao = new KpiOrgDao();
        kpiOrgDao.setDomainDao(context.getDomainDao());
    }

    @Test
    public void listChild1OrgAndEmpR() {
        String json="{\"orgId\":\"a03b54a8-2c86-4dba-ba74-b28d806f9fab\",\"onlySeeEnable\":true,\"onTheJobStatus\":null,\"entryDateStart\":\"\",\"entryDateEnd\":\"\"}";
        KpiEmpQuery query = JSON.parseObject(json,KpiEmpQuery.class);
        query.setTenantId(new TenantId("ce4e403-43aa-47f2-bb19-a0dd18b8e98d"));

        CurOrgStructPo structPo = kpiOrgDao.listChild1OrgAndEmpR(query);
        System.out.println(JSONUtil.toJsonStr(structPo));
    }

    @Test
    public void listChild1OrgAndEmp() {
        String json="{\"orgId\":\"a03b54a8-2c86-4dba-ba74-b28d806f9fab\",\"onlySeeEnable\":true,\"onTheJobStatus\":null,\"entryDateStart\":\"\",\"entryDateEnd\":\"\"}";
        KpiEmpQuery query = JSON.parseObject(json,KpiEmpQuery.class);
        query.setTenantId(new TenantId("ce4e403-43aa-47f2-bb19-a0dd18b8e98d"));
        CurOrgStructPo structPo = kpiOrgDao.listChild1OrgAndEmp(query);
        System.out.println(JSONUtil.toJsonStr(structPo));
    }


//    @Test
//    public void tryFilterSelfManager() {
//        List<LevelManager> managers = JSONUtil.toList("[{\"direct\":false,\"level\":1,\"managerId\":\"*********\",\"managerName\":\"徐鑫\",\"pOrgId\":\"51995941\",\"sOrgId\":\"51995941\"},{\"direct\":false,\"level\":2,\"managerId\":\"*********\",\"managerName\":\"潘承金\",\"pOrgId\":\"51995869\",\"sOrgId\":\"51995941\"}]",LevelManager.class);
//        EmpId empId = new EmpId("*********");
//        List<LevelManager> managers1 = kpiOrgDao.tryFilterSelfManager(empId,managers,1);
//        System.out.println(JSONUtil.toJsonStr(managers1));
//    }

    @Test
    public void listEmpsManager() {
        TenantId companyId = new TenantId("5a031297-1b38-48ae-bc82-375849835203");
        EmpId empId = new EmpId("*********");
        String orgId = null;
        List<LevelManager> managers1 = kpiOrgDao.listEmpsManager(companyId,empId,orgId,1);
        System.out.println(JSONUtil.toJsonStr(managers1));
    }

    @Test
    @DisplayName("部门等级选择的：末级部门")
    public void listEmpByDeptLevel0() {
        TenantId companyId = new TenantId("5a031297-1b38-48ae-bc82-375849835203");
        String orgId = null;
        List<EmpStaff> staffs = kpiOrgDao.listEmpByDeptLevel(companyId, "0", orgId, "*********",false);
        System.out.println(JSONUtil.toJsonStr(staffs));
    }

    @Test
    @DisplayName("部门等级选择的：一级部门")
    public void listEmpByDeptLevel1() {
        TenantId companyId = new TenantId("5a031297-1b38-48ae-bc82-375849835203");
        String orgId = null;
        List<EmpStaff> staffs = kpiOrgDao.listEmpByDeptLevel(companyId, "1", orgId, "*********",false);
        System.out.println(JSONUtil.toJsonStr(staffs));
    }

//    @Test
//    public void reSortLevel() {
//        List<LevelManager> managers = JSONUtil.toList("[{\"pOrgId\":\"51995869\",\"level\":2,\"sOrgId\":\"51995941\",\"direct\":false,\"managerId\":\"*********\",\"managerName\":\"潘承金\"}]",LevelManager.class);
//        kpiOrgDao.reSortLevel(managers);
//        System.out.println(JSONUtil.toJsonStr(managers));
//    }
//
//    @Test
//    public void parseCurLevelManger() {
//        List<LevelManager> managers = JSONUtil.toList("[{\"pOrgId\":\"51995869\",\"level\":1,\"sOrgId\":\"51995941\",\"direct\":false,\"managerId\":\"*********\",\"managerName\":\"潘承金\"}]",LevelManager.class);
//        EvalAudit evalAudit = new EvalAudit();
//        List<Rater> raters = evalAudit.parseCurLevelManger(managers,1);
//        System.out.println(JSONUtil.toJsonStr(raters));
//
//    }
}