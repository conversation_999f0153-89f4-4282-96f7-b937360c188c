package com.polaris.kpi.eval.infr.cycle.dao;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.infr.cycle.ppojo.ConsecutiveTopResultEmpPo;
import com.polaris.kpi.eval.infr.cycle.ppojo.CycleTopResultRulePo;
import com.polaris.kpi.eval.infr.cycle.ppojo.EmpPerfStatisticPo;
import com.polaris.kpi.eval.infr.cycle.query.ConsecutiveTopResultQuery;
import com.polaris.kpi.eval.infr.task.dao.CycleDao;
import com.polaris.kpi.org.infr.dept.dao.KpiOrgDao;
import com.polaris.sdk.type.TenantId;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.lufei.ibatis.mapper.PagedList;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/11 16:23
 */
public class ConsecutiveTopResultEmpDaoTest {
    protected TestContext context;
    private ConsecutiveTopResultEmpDao consecutiveTopResultEmpDao;
    private PerfStatisticRuleDao perfStatisticRuleDao;
    private TopResultOfCycleDao topResultOfCycleDao;
    protected final static TenantId companyId = new TenantId("5a031297-1b38-48ae-bc82-375849835203") ;
    protected final static String createdUser = "1390043" ;
    protected final static String deptId = "a03b54a8-2c86-4dba-ba74-b28d806f9fab" ;
    protected final static String cycleId = "1163701" ;
    private KpiOrgDao kpiOrgDao;
    private CycleDao cycleDao;


    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        DomainDaoImpl domainDao = context.getDomainDao();
        consecutiveTopResultEmpDao = new ConsecutiveTopResultEmpDao();
        consecutiveTopResultEmpDao.setDomainDao(domainDao);
        perfStatisticRuleDao = new PerfStatisticRuleDao();
        perfStatisticRuleDao.setDomainDao(domainDao);
        kpiOrgDao = new KpiOrgDao();
        kpiOrgDao.setDomainDao(domainDao);
        topResultOfCycleDao = new TopResultOfCycleDao();
        topResultOfCycleDao.setDomainDao(domainDao);
        cycleDao = new CycleDao();
        cycleDao.setDomainDao(domainDao);
    }

    @Test
    public void countTopResultStatistic() {
        EmpPerfStatisticPo resultStatisticPo = consecutiveTopResultEmpDao.countTopResultStatistic(companyId.getId(), cycleId, 1);
        System.out.println(resultStatisticPo);
    }

    @Test
    public void getTopResultRuleList() {
        List<CycleTopResultRulePo> topResultRuleList = consecutiveTopResultEmpDao.getTopResultRuleList(companyId, cycleId, 1, 1);
        System.out.println(JSONUtil.toJsonStr(topResultRuleList));
    }

    @Test
    public void pagedTopResultEmp() {
        String jsonStr = "{\n" +
                "    \"cycleId\": \"1163701\",\n" +
                "    \"ruleType\": 1,\n" +
                "    \"ruleApplyType\": 1,\n" +
                "    \"pageNo\": \"1\",\n" +
                "    \"pageSize\": \"1\",\n" +
                "    \"orgIds\": [\n" +
                "        \"1010402\"\n" +
                "    ],\n" +
                "    \"ruleConfigId\": \"100902\"\n" +
                "}";
        ConsecutiveTopResultQuery query = JSONUtil.toBean(jsonStr, ConsecutiveTopResultQuery.class);
        query.setTenantId(companyId);
        PagedList<ConsecutiveTopResultEmpPo> resultEmpPos = consecutiveTopResultEmpDao.pagedTopResultEmp(query);
        System.out.println(JSONUtil.toJsonStr(resultEmpPos));
    }
}
