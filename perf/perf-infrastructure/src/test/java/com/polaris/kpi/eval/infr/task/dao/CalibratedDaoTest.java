package com.polaris.kpi.eval.infr.task.dao;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.polaris.kpi.eval.domain.task.entity.calibrated.LevelRate;
import com.polaris.kpi.eval.domain.task.entity.calibrated.ResultRankInstance;
import com.polaris.kpi.eval.domain.task.entity.grade.CheckRate;
import com.polaris.kpi.eval.domain.task.entity.grade.LevelRateConf;
import com.polaris.kpi.eval.infr.task.BaseDaoTest;
import com.polaris.kpi.eval.infr.task.ppojo.calibrated.CalibratedOrgStaPo;
import com.polaris.kpi.eval.infr.task.ppojo.calibrated.ResultAuditCacheDo;
import com.polaris.kpi.eval.infr.task.query.calibrated.CalibratedOrgQry;
import com.polaris.sdk.type.ListWrap;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

public class CalibratedDaoTest extends BaseDaoTest {
    private CalibratedDao calibratedDao;
    private CalibratedOnTaskDao calibratedOnTaskDao;
    private String opEmpId = "1090038";

    @BeforeClass
    public static void beforeClass() throws Exception {
        beforeClass("com/polaris/kpi/eval/infr/task/dao/CalibratedDaoTest/calibratedOrgData.sql");
    }

    @Before
    public void setUp() throws Exception {
        calibratedDao = new CalibratedDao();
        calibratedOnTaskDao = new CalibratedOnTaskDao();
        setDao(calibratedDao, "domainDao");
        setDao(calibratedOnTaskDao, "domainDao");
    }

    @Test
    public void calibratedOrg() {
        CalibratedOrgQry qry = new CalibratedOrgQry();
        qry.accOp(companyId, opEmpId);
        qry.setDistributionId("10001");
        System.out.println(JSONObject.toJSONString(qry));
        //qry.setOrgId();
        List<CalibratedOrgStaPo> orgStaPos = calibratedDao.calibratedOrg(qry);
        Assert.assertTrue(orgStaPos.size() > 0);
        String s = JSONObject.toJSONString(orgStaPos);
        //Assert.assertEquals(s,FileUtil.readString(new File(""),""));
        for (CalibratedOrgStaPo orgStaPo : orgStaPos) {
            Assert.assertTrue(CollUtil.isNotEmpty(orgStaPo.getLevelRates()));
        }
        System.out.println(s);
    }


    private List<CalibratedOrgStaPo> build(){
        List<CalibratedOrgStaPo> staOrgs = new ArrayList<>();
        CalibratedOrgStaPo staPo1 =new CalibratedOrgStaPo();//1026452 人力资源部  3
        staPo1.setOrgId("1026452");
        List<LevelRate> levelRates = new ArrayList<>();
        LevelRate rate = new LevelRate("A(优秀)",3);
        rate.setTaskStatus("resultsAuditing");
        levelRates.add(rate);

        LevelRate rate2 = new LevelRate("B(合格)",3);
        rate2.setTaskStatus("resultsAuditing");
        levelRates.add(rate2);

        LevelRate rate3 = new LevelRate("none",1);
        rate3.setTaskStatus("scoring");
        levelRates.add(rate3);
        staPo1.setLevelRates(levelRates);
        staOrgs.add(staPo1);

        CalibratedOrgStaPo staPo2 =new CalibratedOrgStaPo();//1026413
        staPo2.setOrgId("1026413");
        List<LevelRate> levelRates2 = new ArrayList<>();
        LevelRate rate22 = new LevelRate("B(合格)",3);
        rate22.setTaskStatus("resultsAuditing");
        levelRates2.add(rate22);
        staPo2.setLevelRates(levelRates2);
        staOrgs.add(staPo2);

        CalibratedOrgStaPo staPo3=new CalibratedOrgStaPo();//1026453
        staPo3.setOrgId("1026453");
        List<LevelRate> levelRates31 = new ArrayList<>();
        LevelRate rate31 = new LevelRate("A(优秀)",1);
        rate31.setTaskStatus("resultsAuditing");
        levelRates31.add(rate31);

        LevelRate rate32 = new LevelRate("B(合格)",3);
        rate32.setTaskStatus("resultsAuditing");
        levelRates31.add(rate32);

        LevelRate rate33 = new LevelRate("D(不合格)",1);
        rate33.setTaskStatus("resultsAuditing");
        levelRates31.add(rate33);

        staPo3.setLevelRates(levelRates31);
        staOrgs.add(staPo3);

        CalibratedOrgStaPo staPo4 =new CalibratedOrgStaPo();//1026475
        staPo4.setOrgId("1026475");
        List<LevelRate> levelRates4 = new ArrayList<>();
        LevelRate rate41 = new LevelRate("none",7);
        rate41.setTaskStatus("resultsAuditing");
        levelRates4.add(rate41);
        staPo4.setLevelRates(levelRates4);
        staOrgs.add(staPo4);
        return staOrgs;
    }

    @Test
    public void calibratedOrgtest() {
        CalibratedOrgQry qry = new CalibratedOrgQry();
        qry.accOp(companyId, opEmpId);
        qry.setDistributionId("10001");
        System.out.println(JSONObject.toJSONString(qry));
        //qry.setOrgId();
        //List<CalibratedOrgStaPo> orgStaPos = calibratedOnTaskDao.calibratedOrgtest(qry);
        List<CalibratedOrgStaPo> staOrgs = build();
        CalibratedOrgStaPo total = new CalibratedOrgStaPo("total", "全部", 0);
        for (CalibratedOrgStaPo staOrg : staOrgs) {
            staOrg.parseLevelRates(staOrg.getLevelRates());
            total.addSum(staOrg.getLevelRates(), staOrg.getSumCnt());
        }
        staOrgs.add(total);
        staOrgs.removeIf(orgStaPo -> orgStaPo.getSumCnt() == 0 && orgStaPo.noneEqualZero());
        System.out.println(JSONUtil.toJsonStr(staOrgs));
        System.out.println("=========================================================");
        ResultRankInstance rankInstance2 = new  ResultRankInstance();
        String json3 = "{\"open\":1,\"rates\":[{\"levels\":[{\"id\":\"2082012\",\"name\":\"A(优秀)\"}],\"op\":\"<=\",\"rate\":20,\"sortNo\":1}],\"startCnt\":\"1\"}";
        LevelRateConf levelRateConf2 = JSONUtil.toBean(json3,LevelRateConf.class);
        rankInstance2.setLevelRateConf(levelRateConf2);
        for (CalibratedOrgStaPo orgStaPo : staOrgs) {
            List<CheckRate> chekRsRates = rankInstance2.checkRateExcept(orgStaPo.getSumCnt(), orgStaPo.getLevelRates());
            orgStaPo.setErrorRates(chekRsRates);
        }
        System.out.println(JSONUtil.toJsonStr(staOrgs));
        System.out.println("=========================================================");


        String json2 = "[{\"levelRates\":[{\"cnt\":3,\"level\":\"A(优秀)\",\"rate\":43.00},{\"cnt\":3,\"level\":\"B(合格)\",\"rate\":43.00},{\"cnt\":1,\"level\":\"none\"}],\"orgId\":\"1026452\",\"sumCnt\":7},{\"levelRates\":[{\"cnt\":9,\"level\":\"B(合格)\",\"rate\":41.00},{\"cnt\":4,\"level\":\"A(优秀)\",\"rate\":18.00},{\"cnt\":8,\"level\":\"none\",\"rate\":36.00}],\"orgId\":\"total\",\"orgName\":\"全部\",\"sumCnt\":22}]";
        List<CalibratedOrgStaPo> orgStaPos = JSONUtil.toList(json2,CalibratedOrgStaPo.class);
        System.out.println(JSONUtil.toJsonStr(orgStaPos));
        ResultRankInstance rankInstance = new  ResultRankInstance();
        //{"open":1,"rates":[{"levels":[{"id":"2082012","name":"A(优秀)"}],"op":"<=","rate":20,"sortNo":1}],"startCnt":"1"}
        String json = "{\"open\":1,\"rates\":[{\"levels\":[{\"id\":\"2082012\",\"name\":\"A(优秀)\"}],\"op\":\"<=\",\"rate\":20,\"sortNo\":1}],\"startCnt\":\"1\"}";
        LevelRateConf levelRateConf = JSONUtil.toBean(json,LevelRateConf.class);
        rankInstance.setLevelRateConf(levelRateConf);
        for (CalibratedOrgStaPo orgStaPo : orgStaPos) {
            List<CheckRate> chekRsRates = rankInstance.checkRateExcept(orgStaPo.getSumCnt(), orgStaPo.getLevelRates());
            orgStaPo.setErrorRates(chekRsRates);
        }
        System.out.println(JSONUtil.toJsonStr(orgStaPos));
//{"open":1,"rates":[{"levels":[{"id":"2082012","name":"A(优秀)"}],"op":"<=","rate":20,"sortNo":1}],"startCnt":"1"}

        //[{"levelRates":[{"cnt":3,"level":"A(优秀)","rate":43.00},{"cnt":3,"level":"B(合格)","rate":43.00},{"cnt":1,"level":"none"}],"orgId":"1026452","sumCnt":7},{"levelRates":[{"cnt":3,"level":"B(合格)","rate":43.00},{"cnt":3,"level":"A(优秀)","rate":43.00},{"cnt":1,"level":"none","rate":14.00}],"orgId":"total","orgName":"全部","sumCnt":7}]
        Assert.assertTrue(orgStaPos.size() > 0);
        String s = JSONObject.toJSONString(orgStaPos);
        //Assert.assertEquals(s,FileUtil.readString(new File(""),""));
        for (CalibratedOrgStaPo orgStaPo : orgStaPos) {
            Assert.assertTrue(CollUtil.isNotEmpty(orgStaPo.getLevelRates()));
        }
        System.out.println(s);
    }

    @Test
    public void calibratedOrgtest2() {
        String orgJson = "[{\"orgName\":\"党工纪检办\",\"levelRateMap\":{},\"orgId\":\"1026413\",\"levelRates\":[{\"level\":\"B(合格)\",\"cnt\":3,\"taskUserId\":\"1285610,1285612,1285618\",\"distributionId\":\"1012702\",\"rate\":100,\"taskStatus\":\"resultsAuditing\"},{\"level\":\"none\",\"cnt\":0}],\"sumCnt\":3},{\"orgName\":\"人力资源部\",\"levelRateMap\":{},\"orgId\":\"1026452\",\"levelRates\":[{\"level\":\"A(优秀)\",\"cnt\":3,\"taskUserId\":\"1285609,1285611,1285628\",\"distributionId\":\"1012702\",\"rate\":43,\"taskStatus\":\"result sAuditing\"},{\"level\":\"B(合格)\",\"cnt\":3,\"taskUserId\":\"1285614,1285620,1285625\",\"distributionId\":\"1012702\",\"rate\":43,\"taskStatus\":\"resultsAuditing\"},{\"level\":\"none\",\"cnt\":1,\"notEnterTaskUserId\":\"1285613\"}],\"sumCnt\":7},{\"orgName\":\"法务部\",\"levelRateMap\":{},\"orgId\":\"1026453\",\"levelRates\":[{\"level\":\"A(优秀)\",\"cnt\":1,\"taskUserId\":\"1285624\",\"distributionId\":\"1012702\",\"rate\":20,\"taskStatus\":\"resultsAuditing\"},{\"level\":\"B(合格)\",\"cnt\":3,\"taskUserId\":\"1285617,1285619,1285627\",\"distributionId\":\"1012702\",\"rate\":60,\"taskStatus\":\"resultsAuditing\"},{\"level\":\"D(不合格)\",\"cnt\":1,\"taskUserId\":\"1285622\",\"distributionId\":\"1012702\",\"rate\":20,\"taskStatus\":\"resultsAuditing\"},{\"level\":\"none\",\"cnt\":0}],\"sumCnt\":5},{\"orgName\":\"财务／薪酬管理部\",\"levelRateMap\":{},\"orgId\":\"1026475\",\"levelRates\":[{\"level\":\"none\",\"cnt\":7,\"notEnterTaskUserId\":\"1285607,1285616,1285621,1285623,1285626,1285629,1285630\"}],\"sumCnt\":7},{\"orgName\":\"全部\",\"levelRateMap\":{\"A(优秀)\":{\"level\":\"A(优秀)\",\"cnt\":4,\"rate\":18},\"none\":{\"level\":\"none\",\"cnt\":8,\"rate\":36,\"notEnterTaskUserId\":\"1285613,1285607,1285616,1285621,1285623,1285626,1285629,1285630\"},\"D(不合格)\":{\"level\":\"D(不合格)\",\"cnt\":1,\"rate\":5},\"B(合格)\":{\"level\":\"B(合格)\",\"cnt\":9,\"rate\":41}},\"orgId\":\"total\",\"levelRates\":[{\"level\":\"B(合格)\",\"cnt\":9,\"rate\":41},{\"level\":\"A(优秀)\",\"cnt\":4,\"rate\":18},{\"level\":\"none\",\"cnt\":8,\"rate\":36,\"notEnterTaskUserId\":\"1285613,1285607,1285616,1285621,1285623,1285626,1285629,1285630\"},{\"level\":\"D(不合格)\",\"cnt\":1,\"rate\":5}],\"sumCnt\":22}]";
        List<CalibratedOrgStaPo> staOrgs = JSONUtil.toList(orgJson,CalibratedOrgStaPo.class);

        String rankjson= "{\"updatedTime\":1732161312000,\"fieldJson\":\"[{\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"考核任务得分\\\",\\\"companyFieldId\\\":\\\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"考核任务得分\\\",\\\"value\\\":0,\\\"yVal\\\":true},{\\\"label\\\":\\\"×\\\",\\\"value\\\":\\\"*\\\",\\\"symbol\\\":true},{\\\"label\\\":0,\\\"value\\\":0},{\\\"label\\\":\\\".\\\",\\\"value\\\":\\\".\\\"},{\\\"label\\\":0,\\\"value\\\":0},{\\\"label\\\":1,\\\"value\\\":1}]\",\"rankScope\":{\"type\":1,\"scope\":-1},\"cycleId\":\"1166902\",\"updatedUser\":\"\",\"version\":2,\"levelDefType\":1,\"coeffType\":1,\"companyId\":{\"id\":\"1146644\"},\"system\":1,\"isDeleted\":\"false\",\"levelRateConf\":{\"rates\":[{\"op\":\"<=\",\"sortNo\":1,\"rate\":20,\"levels\":[{\"version\":0,\"isDeleted\":\"false\",\"name\":\"A(优秀)\",\"id\":\"2082012\"}]}],\"startCnt\":\"1\",\"open\":1},\"name\":\"系统默认\",\"createdTime\":1731899288000,\"id\":\"1012702\",\"place\":2,\"perfCoefficient\":\"考核任务得分*0.01\",\"onCycleType\":63,\"ruleId\":\"2082016\",\"createdUser\":\"1398099\"}";
        ResultRankInstance rankInstance2 = JSONUtil.toBean(rankjson,ResultRankInstance.class);
        for (CalibratedOrgStaPo orgStaPo : staOrgs) {
            List<CheckRate> chekRsRates = rankInstance2.checkRateExcept(orgStaPo.getSumCnt(), orgStaPo.getLevelRates());
            orgStaPo.setErrorRates(chekRsRates);
        }
        System.out.println(JSONUtil.toJsonStr(staOrgs));
        System.out.println("=========================================================");
    }

    @Test
    public void directChildOrg() {
        CalibratedOrgQry qry = new CalibratedOrgQry();
        qry.accOp(companyId, opEmpId);
        qry.setDistributionId("10001");
        //qry.setOrgId();
        List<String> orgIds = calibratedDao.directChildOrg(qry);
        System.out.println(orgIds);
        Assert.assertEquals("[1011037, 1011602, 1013211, 1018301, 1019701]", orgIds.toString());
    }

    @Test
    public void directChildOrgWithOrgId() {
        CalibratedOrgQry qry = new CalibratedOrgQry();
        qry.accOp(companyId, opEmpId);
        qry.setDistributionId("10001");
        qry.setOrgId("1011037");
        List<String> orgIds = calibratedDao.directChildOrg(qry);
        System.out.println(orgIds);
        Assert.assertEquals("[1015501, 1017501]", orgIds.toString());
    }

    @Test
    public void pagedCalibrateTaskUser() {
    }

    @Test
    public void listAutitResultCache() {
        ListWrap<ResultAuditCacheDo> resultAuditCacheDoListWrap = new ListWrap<ResultAuditCacheDo>().asMap(cacheDo -> cacheDo.getTaskUserId());
        System.out.println(resultAuditCacheDoListWrap);
        System.out.println(resultAuditCacheDoListWrap.mapGet("1"));
    }
}