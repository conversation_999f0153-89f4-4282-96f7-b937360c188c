package com.polaris.kpi.eval.infr.task.repimpl;

import com.polaris.kpi.eval.infr.task.ppojo.emptable.EmpForTablePo;
import com.polaris.kpi.eval.infr.task.query.emptable.EmpForTableQuery;
import com.polaris.test.base.TestContext;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.lufei.ibatis.mapper.PagedList;

import java.util.Arrays;

public class EmpEvalTableDaoTest {
    private String companyId = "ece4e403-43aa-47f2-bb19-a0dd18b8e98d";
    private static TestContext context;
    private static EmpEvalTableDao tableDao = new EmpEvalTableDao();

    @BeforeClass
    public static void beforeClass() throws Exception {
        context = new TestContext("db.properties");
        context.runScriptH2("polaris/kpi/eval/infr/org/orgDDL.sql");
        context.runScriptH2("polaris/kpi/eval/infr/org/orgData.sql");
        context.runScriptH2("polaris/kpi/eval/infr/task/repimpl/EmpEvalTableDao.sql");
        context.runScriptH2("polaris/kpi/eval/infr/task/repimpl/EmpEvalTableDaoData.sql");
        context.confDomainDaoFor(tableDao, "domainDao");
    }


    @Test
    public void pagedEmpForTableR() {
        {
            //{"pageNo":1,"pageSize":10,"orderField":"taskNum","orderType":"","empIds":[],"deptIds":["1011040"]}
            EmpForTableQuery qry = new EmpForTableQuery();
            //qry.setOrderField("taskNum");
            //qry.setOrderType("");
            //qry.setDeptIds(Arrays.asList("1011040"));
            qry.setCompanyId(companyId);
            qry.setAdminType("child");
            qry.setAdminEmpId("1100018");
            PagedList<EmpForTablePo> emps = tableDao.pagedEmpForTableOnChildAdmin(qry);
            Assert.assertEquals(3, emps.size());
            Assert.assertEquals("1页", 1, emps.getTotalPage().intValue());
            Assert.assertEquals(3, emps.getTotalRow().intValue());

        }

        {
            //{"pageNo":1,"pageSize":10,"orderField":"taskNum","orderType":"","empIds":[],"deptIds":["1011040"]}
            EmpForTableQuery qry = new EmpForTableQuery();
            qry.setCompanyId(companyId);
            qry.setAdminType("child");
            qry.setAdminEmpId("1100018xx");
            PagedList<EmpForTablePo> emps = tableDao.pagedEmpForTableOnChildAdmin(qry);
            Assert.assertEquals(0, emps.size());
        }

        {
            //{"pageNo":1,"pageSize":10,"orderField":"taskNum","orderType":"","empIds":[],"deptIds":["1011040"]}
            EmpForTableQuery qry = new EmpForTableQuery();
            qry.setCompanyId(companyId);
            qry.setAdminType("main");
            qry.setOrderField("total_task_num");
            qry.setOrderType("desc");
            PagedList<EmpForTablePo> emps = tableDao.pagedEmpForTableOnMainAdmin(qry);
            Assert.assertEquals("多于三个10一页", 10, emps.size());
            Assert.assertEquals("16个员工", 16, emps.getTotalRow().intValue());
            Assert.assertEquals("2页", 2, emps.getTotalPage().intValue());
            EmpForTablePo last = null;
            for (EmpForTablePo emp : emps) {
                if (last == null) {
                    last = emp;
                    continue;
                }
                Assert.assertTrue(!last.getEmpId().equals(emp.getEmpId()));
                Assert.assertTrue(last.getTotalTaskNum() >= emp.getTotalTaskNum());
                last = emp;
            }
        }
        {
            //{"pageNo":1,"pageSize":10,"orderField":"taskNum","orderType":"","empIds":[],"deptIds":["1011040"]}
            EmpForTableQuery qry = new EmpForTableQuery();
            qry.setCompanyId(companyId);
            qry.setAdminType("main");
            qry.setOrderField("total_task_num");
            qry.setOrderType("desc");
            qry.setDeptIds(Arrays.asList("1011602"));
            PagedList<EmpForTablePo> emps = tableDao.pagedEmpForTableOnMainAdmin(qry);
            Assert.assertEquals("2行", 2, emps.size());
            Assert.assertEquals("共2行", 2, emps.getTotalRow().intValue());
            Assert.assertEquals("1页", 1, emps.getTotalPage().intValue());
            EmpForTablePo last = null;
            for (EmpForTablePo emp : emps) {
                if (last == null) {
                    last = emp;
                    continue;
                }
                Assert.assertTrue(!last.getEmpId().equals(emp.getEmpId()));
                Assert.assertTrue(last.getTotalTaskNum() >= emp.getTotalTaskNum());
                last = emp;
            }
        }

    }
}