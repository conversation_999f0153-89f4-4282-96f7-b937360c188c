package com.polaris.kpi.eval.infr.cycle.repimpl;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.cycle.entity.ConsecutiveTopResultEmpBatch;
import com.polaris.kpi.eval.infr.cycle.dao.PerfStatisticRuleDao;
import com.polaris.kpi.eval.infr.cycle.dao.TopResultOfCycleDao;
import com.polaris.kpi.eval.infr.cycle.ppojo.ConsecutiveTopResultEmpDo;
import com.polaris.kpi.org.infr.emp.dao.KpiEmpDao;
import com.polaris.sdk.type.TenantId;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.dao.DomainDaoImpl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/11 18:09
 */
public class ConsecutiveTopResultEmpRepoTest {

    protected TestContext context;
    private TopResultOfCycleDao topResultOfCycleDao;
    private TopResultOfCycleRepoImpl topResultOfCycleRepo;
    private ConsecutiveTopResultEmpRepoImpl consecutiveTopResultEmpRepo;
    private CycleRepoImpl cycleRepo;
    private KpiEmpDao kpiEmpDao;
    protected PerfStatisticRuleRepoImpl perfStatisticRuleRepo;
    protected PerfStatisticRuleDao perfStatisticRuleDao;
    protected final static TenantId companyId = new TenantId("5a031297-1b38-48ae-bc82-375849835203") ;
    protected final static String createdUser = "1390043" ;


    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        DomainDaoImpl domainDao = context.getDomainDao();
        cycleRepo = new CycleRepoImpl();
        cycleRepo.setDomainDao(context.getDomainDao());
        kpiEmpDao = new KpiEmpDao();
        kpiEmpDao.setDomainDao(domainDao);

        topResultOfCycleDao = new TopResultOfCycleDao();
        topResultOfCycleDao.setDomainDao(domainDao);

        consecutiveTopResultEmpRepo = new ConsecutiveTopResultEmpRepoImpl();
        consecutiveTopResultEmpRepo.setDomainDao(domainDao);
        consecutiveTopResultEmpRepo.setCycleRepo(cycleRepo);
        consecutiveTopResultEmpRepo.setKpiEmpDao(kpiEmpDao);

        topResultOfCycleRepo = new TopResultOfCycleRepoImpl();
        topResultOfCycleRepo.setDomainDao(domainDao);

        perfStatisticRuleRepo = new PerfStatisticRuleRepoImpl();
        perfStatisticRuleRepo.setDomainDao(domainDao);



        perfStatisticRuleDao = new PerfStatisticRuleDao();
        perfStatisticRuleDao.setDomainDao(domainDao);
    }

    @Test
    public void addResultBatch(){
        String jsonStr = "{\n" +
                "\"cycleType\": \"year\",\n" +
                "\"statisticRule\": [{\n" +
                "\"statisticType\": \"grade\",\n" +
                "\"statisticItems\": [{\n" +
                "\"ruleName\": \"A\",\n" +
                "\"ruleId\": \"2006275\"\n" +
                "}]\n" +
                "}],\n" +
                "\"companyId\": {\n" +
                "\"id\": \"5a031297-1b38-48ae-bc82-375849835203\"\n" +
                "},\n" +
                "\"topResultEmpIdList\": [\"1602589\"],\n" +
                "\"historyTopResults\": [{\n" +
                "\"empId\": \"1602589\",\n" +
                "\"year\": \"2024\",\n" +
                "\"stepId\": \"2004001\",\n" +
                "\"orgId\": \"1024001\",\n" +
                "\"atOrgCodePath\": \"|a03b54a8-2c86-4dba-ba74-b28d806f9fab|1023623|1023621|1024001|\",\n" +
                "\"empName\": \"杨思威\",\n" +
                "\"perfWeight\": \"1.5\",\n" +
                "\"value\": \"2024\",\n" +
                "\"finalScore\": 99.0,\n" +
                "\"atOrgNamePath\": \"绩效测试公司|所在一级部门|所在二级部门|所在三级部门\",\n" +
                "\"orgName\": \"绩效测试公司\",\n" +
                "\"evaluationLevel\": \"A\",\n" +
                "\"avatar\": \"https://static-legacy.dingtalk.com/media/lADPM5HikoKm7UnNAWPNAUQ_324_355.jpg\",\n" +
                "\"taskName\": \"绩效测试公司-lzx\",\n" +
                "\"taskId\": \"1635402\"\n" +
                "}],\n" +
                "\"ruleApplyType\": 1,\n" +
                "\"ruleType\": 1,\n" +
                "\"cycleId\": \"1165601\",\n" +
                "\"ruleName\": \"年度绩优规则\",\n" +
                "\"ruleConfigId\": \"101401\",\n" +
                "\"statisticRuleDetail\": {\n" +
                "\"statisticDuration\": 1,\n" +
                "\"cycleType\": \"year\",\n" +
                "\"statisticRule\": [{\n" +
                "\"statisticType\": \"grade\",\n" +
                "\"statisticItems\": [{\n" +
                "\"ruleName\": \"A\",\n" +
                "\"ruleId\": \"2006275\"\n" +
                "}]\n" +
                "}],\n" +
                "\"resNotifyStaff\": [],\n" +
                "\"ruleApplyRange\": [{\n" +
                "\"objItems\": [{\n" +
                "\"objId\": \"1010905\",\n" +
                "\"objName\": \"三组\"\n" +
                "}, {\n" +
                "\"objId\": \"1010909\",\n" +
                "\"objName\": \"六组\"\n" +
                "}, {\n" +
                "\"objId\": \"1010701\",\n" +
                "\"objName\": \"权限10：57\"\n" +
                "}, {\n" +
                "\"objId\": \"1010703\",\n" +
                "\"objName\": \"权限10：58\"\n" +
                "}, {\n" +
                "\"objId\": \"1008601\",\n" +
                "\"objName\": \"钉531/0411\"\n" +
                "}, {\n" +
                "\"objId\": \"1010401\",\n" +
                "\"objName\": \"钉事件\"\n" +
                "}, {\n" +
                "\"objId\": \"1010907\",\n" +
                "\"objName\": \"销售一部\"\n" +
                "}, {\n" +
                "\"objId\": \"1025501\",\n" +
                "\"objName\": \"主管测试\"\n" +
                "}, {\n" +
                "\"objId\": \"1023621\",\n" +
                "\"objName\": \"所在二级部门\"\n" +
                "}, {\n" +
                "\"objId\": \"1023626\",\n" +
                "\"objName\": \"前端一组\"\n" +
                "}, {\n" +
                "\"objId\": \"1004201\",\n" +
                "\"objName\": \"产品\"\n" +
                "}, {\n" +
                "\"objId\": \"6320d255-c4d7-48b7-acb6-1cb76e2bff57\",\n" +
                "\"objName\": \"前端\"\n" +
                "}, {\n" +
                "\"objId\": \"1004202\",\n" +
                "\"objName\": \"设计\"\n" +
                "}, {\n" +
                "\"objId\": \"1026201\",\n" +
                "\"objName\": \"部门成员用后删\"\n" +
                "}],\n" +
                "\"objType\": \"dept\"\n" +
                "}],\n" +
                "\"ruleApplyType\": 1,\n" +
                "\"pipOpen\": 0,\n" +
                "\"ruleType\": 1,\n" +
                "\"resNotifyOpen\": 0,\n" +
                "\"createdTime\": 1728895793000,\n" +
                "\"ruleName\": \"年度绩优规则\",\n" +
                "\"ruleConfigId\": \"101401\"\n" +
                "},\n" +
                "\"statisticDuration\": 1\n" +
                "}";
        ConsecutiveTopResultEmpBatch resultEmpBatch = JSONUtil.toBean(jsonStr, ConsecutiveTopResultEmpBatch.class);
        consecutiveTopResultEmpRepo.addResultBatch(companyId,Collections.singletonList(resultEmpBatch),null,"system");
        context.commit();
    }

    @Test
    public void delBatchByCycleIdAndRuleId(){
        List<ConsecutiveTopResultEmpDo> delList = new ArrayList<>();
        ConsecutiveTopResultEmpDo resultEmpDo = new ConsecutiveTopResultEmpDo();
        resultEmpDo.setCycleId("1814401");
        resultEmpDo.setRuleConfigId(null);
        delList.add(resultEmpDo);
        consecutiveTopResultEmpRepo.delBatchByCycleIdAndRuleId(delList,true);
        context.commit();
    }

    @Test
    public void removeTopResultEmp(){
        consecutiveTopResultEmpRepo.removeTopResultEmp(companyId.getId(),"100101", createdUser,"main");
        context.commit();
    }
}
