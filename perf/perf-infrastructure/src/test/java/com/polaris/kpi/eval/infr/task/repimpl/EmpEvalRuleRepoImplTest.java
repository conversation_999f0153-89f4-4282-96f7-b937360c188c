package com.polaris.kpi.eval.infr.task.repimpl;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskScoreResultDo;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.dao.DomainDaoImpl;


public class EmpEvalRuleRepoImplTest {
    private String companyId = "5a031297-1b38-48ae-bc82-375849835203";
    protected TestContext context;
    private EmpEvalRuleRepoImpl evalRuleRepo;
    private DomainDaoImpl domainDao;

    @Before
    public void setUp() throws Exception {
        context = new cn.com.seendio.polaris.code.TestContext("tenantConf.xml");
        context.start();
        evalRuleRepo = new EmpEvalRuleRepoImpl();
        domainDao = context.getDomainDao();
        evalRuleRepo.setDomainDao(context.getDomainDao());
    }


    @Test
    public void updateScoreResultTaskAuditId() {
        String id = "904a8e61-9cc6-431f-95ac-89e0da70a476";
        String taskAuditId = "1967615";
        evalRuleRepo.updateScoreResultTaskAuditId(id, taskAuditId);

        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "sc")
                .clearSelect().select("sc.*").setRsType(PerfEvaluateTaskScoreResultDo.class)
                .whereEq("company_id", companyId)
                .whereEq("id", id);
        PerfEvaluateTaskScoreResultDo scoreResultDo = domainDao.findOne(comQB);
        Assert.assertTrue(scoreResultDo.getTaskAuditId().equals(taskAuditId));
        System.out.println(JSONUtil.toJsonStr(scoreResultDo));
    }
}
