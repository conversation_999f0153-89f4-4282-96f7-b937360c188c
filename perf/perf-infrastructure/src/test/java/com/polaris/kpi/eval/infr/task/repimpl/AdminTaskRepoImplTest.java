package com.polaris.kpi.eval.infr.task.repimpl;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.cycle.repo.CycleRepo;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.AdminTaskCopyConf;
import com.polaris.kpi.eval.domain.task.type.TaskEval;
import com.polaris.kpi.eval.infr.cycle.repimpl.CycleRepoImpl;
import com.polaris.kpi.eval.infr.task.dao.AdminTaskDao;
import com.polaris.kpi.org.infr.emp.dao.KpiEmpDao;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.dao.DomainDaoImpl;

import java.util.Collections;

public class AdminTaskRepoImplTest {
    protected TestContext context;
    protected AdminTaskRepoImpl adminTaskRepo;
    private DomainDaoImpl domainDao;
    private AdminTaskDao adminTaskDao;
    private KpiEmpDao kpiEmpDao;
    private CycleRepoImpl cycleRepo;
    private final static TenantId companyId = new TenantId("5a031297-1b38-48ae-bc82-375849835203") ;
    private final static String createdUser = "1390043" ;


    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        adminTaskRepo = new AdminTaskRepoImpl();
        domainDao = context.getDomainDao();
        adminTaskDao = new AdminTaskDao();
        adminTaskDao.setDomainDao(domainDao);
        kpiEmpDao = new KpiEmpDao();
        kpiEmpDao.setDomainDao(domainDao);
        cycleRepo = new CycleRepoImpl();
        cycleRepo.setDomainDao(context.getDomainDao());
        adminTaskRepo.setDomainDao(context.getDomainDao());
        adminTaskRepo.setAdminTaskDao(adminTaskDao);
        adminTaskRepo.setKpiEmpDao(kpiEmpDao);
        adminTaskRepo.setCycleRepo(cycleRepo);
    }

    @Test
    public void copyEvalUsers() {
        //dev db
        String adminTaskJson="{\"fromTaskId\":\"1618607\",\"readerIds\":[],\"opAdminType\":\"main\",\"copyEvalRule\":0,\"cycleId\":\"1160105\",\"scoreConf\":{\"transferFlag\":\"true\",\"multiType\":\"or\"},\"auditResult\":{\"collectSendNotify\":1,\"auditNodes\":[],\"open\":0},\"performanceType\":1,\"finishValueAudit\":{\"auditNodes\":[],\"open\":0},\"totalCnt\":0,\"isDeleted\":\"false\",\"adminIds\":[\"1093014\"],\"scoreSortConf\":{\"sortItems\":[{\"sort\":1,\"type\":10,\"name\":\"自评\"},{\"sort\":2,\"type\":20,\"name\":\"同级评\"},{\"sort\":3,\"type\":30,\"name\":\"下级评\"},{\"sort\":4,\"type\":40,\"name\":\"上级评分\"},{\"sort\":5,\"type\":50,\"name\":\"指定评\"}],\"exeType\":0},\"tmpTask\":false,\"createdTime\":1724745812984,\"drawUpCnt\":0,\"copyEvalUser\":1,\"startCnt\":0,\"id\":\"1618902\",\"enterScore\":{\"enterScoreEmpType\":1,\"enterScoreMethod\":\"auto\",\"scoreStartRuleType\":\"before\",\"scoreStartRuleDay\":1},\"taskStatus\":\"published\",\"createdUser\":\"1093014\",\"confirmTask\":{\"openConfirmLT\":0,\"auditNodes\":[],\"modifyItemDimension\":\"all\",\"open\":0},\"interviewConf\":{\"open\":0},\"appealConf\":{\"open\":0},\"deadLineConf\":{\"open\":0},\"finishCnt\":0,\"updatedUser\":\"1093014\",\"version\":0,\"cycleEndDate\":\"2024-08-31\",\"cycleStartDate\":\"2024-08-01\",\"scoreView\":{\"mutualScoreAnonymous\":\"true\",\"mutualScoreViewRule\":{\"superior\":\"\",\"mutual\":\"\",\"appoint\":\"\",\"examinee\":\"\"},\"superiorScoreAnonymous\":\"true\",\"appointScoreViewRule\":{\"superior\":\"\",\"mutual\":\"\",\"appoint\":\"\",\"examinee\":\"\"},\"superiorScoreViewRule\":{\"superior\":\"score,attach\",\"mutual\":\"score,attach\",\"appoint\":\"score,attach\",\"examinee\":\"score,attach\"},\"selfScoreViewRule\":{\"superior\":\"\",\"mutual\":\"\",\"appoint\":\"\",\"examinee\":\"score,attach\"},\"appointScoreAnonymous\":\"true\"},\"companyId\":{\"id\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\"},\"publishResult\":{\"toEmps\":[{\"objType\":\"emp\"}],\"type\":\"afterFinished\",\"scoreDetailPriv\":{\"scoreRemark\":[\"self_remark\",\"item_remark\",\"peer_remark\",\"sub_remark\",\"superior_remark\",\"appoint_remark\"],\"scoreType\":[]},\"opEmps\":[],\"toDetailEmps\":[],\"dimension\":15,\"open\":1},\"readers\":[],\"isEdit\":false,\"confirmResult\":{\"auto\":0,\"sign\":0,\"open\":0},\"editExeIndi\":{\"auditNodes\":[],\"open\":0},\"taskName\":\"复制部门修改名称133222\",\"commentConf\":{\"scoreSummarySwitch\":-1,\"commentFlag\":\"notRequired\",\"plusOrSubComment\":0},\"inputNotifyConf\":{\"sendType\":1},\"admins\":[{\"adminEmpId\":\"1093014\",\"main\":1,\"priv\":6}]}";
        AdminTask adminTask = JSONUtil.toBean(adminTaskJson,AdminTask.class);
        String confJson = "{\"copyEvalRule\":0,\"copyEvalUser\":1}";
        AdminTaskCopyConf copyConf = JSONUtil.toBean(confJson,AdminTaskCopyConf.class);
        TaskEval empss = adminTaskRepo.copyEvalUsers(adminTask,"1618607",new EmpId("1093014"),"main",copyConf,null);
        System.out.println(JSONUtil.toJsonStr(empss));
        Assert.assertTrue(empss.getOpEmpEvals().size() == 1);
    }

    @Test
    public void refreshCntOfTask() {
       adminTaskRepo.refreshCntOfTask(companyId.getId(),"1637065");
    }

    @Test
    public void getAdminTask() {
        AdminTask adminTask = adminTaskRepo.getAdminTask(companyId, "1629428");
        System.out.println(adminTask.getArchiveStatus());
    }

    @Test
    public void updateArchiveStatus(){
        AdminTask adminTask = new AdminTask();
        adminTask.setId("1629428");
        adminTask.setCompanyId(companyId);
        adminTask.setUpdatedUser(createdUser);
        adminTask.setArchiveStatus(0);
        adminTask.setTotalCnt(3);
        adminTaskRepo.updateArchiveStatus(adminTask, 1, "main");
        context.commit();
    }

    @Test
    public void updateBatchArchiveStatus(){
        AdminTask adminTask = new AdminTask();
        adminTask.setId("1629428");
        adminTask.setCompanyId(companyId);
        adminTask.setUpdatedUser(createdUser);
        adminTask.setArchiveStatus(0);
        adminTask.setTotalCnt(3);
        adminTask.setCycleId("1164402");
        adminTask.setTaskName("0815-校准优化-汇总校准前-终止被考核人");
        adminTaskRepo.updateBatchArchiveStatus(Collections.singletonList(adminTask), 1, "main");
        context.commit();
    }
}
