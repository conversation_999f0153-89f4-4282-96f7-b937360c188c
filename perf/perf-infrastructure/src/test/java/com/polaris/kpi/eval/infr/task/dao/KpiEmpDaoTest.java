package com.polaris.kpi.eval.infr.task.dao;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.polaris.kpi.eval.infr.task.query.KpiEmpQuery;
import com.polaris.kpi.org.infr.dept.dao.KpiOrgDao;
import com.polaris.kpi.org.infr.dept.pojo.CurOrgStructPo;
import com.polaris.kpi.org.infr.emp.dao.KpiEmpDao;
import com.polaris.kpi.org.infr.emp.pojo.KpiEmpPo;
import com.polaris.sdk.type.TenantId;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.lufei.ibatis.mapper.PagedList;

public class KpiEmpDaoTest {
    protected TestContext context;
    protected KpiEmpDao kpiEmpDao;
    private DomainDaoImpl domainDao;


    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        kpiEmpDao = new KpiEmpDao();
        kpiEmpDao.setDomainDao(context.getDomainDao());
    }

    @Test
    public void pagedEmp() {
        String json="{\"orgId\":\"a03b54a8-2c86-4dba-ba74-b28d806f9fab\",\"onlySeeEnable\":true,\"onTheJobStatus\":null,\"entryDateStart\":\"\",\"entryDateEnd\":\"\"}";
        KpiEmpQuery query = JSON.parseObject(json,KpiEmpQuery.class);
        query.setTenantId(new TenantId("ce4e403-43aa-47f2-bb19-a0dd18b8e98d"));

        PagedList<KpiEmpPo> kpiEmpPos = kpiEmpDao.pagedEmp(query);
        System.out.println(JSONUtil.toJsonStr(kpiEmpPos));
    }
}