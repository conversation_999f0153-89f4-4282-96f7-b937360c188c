package com.polaris.kpi.org.infr.emp.pojo;

import javassist.ClassPool;
import javassist.CtClass;
import javassist.CtField;
import javassist.NotFoundException;
import javassist.bytecode.ConstPool;
import javassist.bytecode.FieldInfo;
import javassist.bytecode.annotation.Annotation;
import net.sf.cglib.beans.BeanCopier;
import net.sf.cglib.beans.BeanGenerator;
import net.sf.cglib.beans.BeanMap;
import org.apache.commons.beanutils.BasicDynaBean;
import org.apache.commons.beanutils.BasicDynaClass;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.beanutils.DynaProperty;
import org.apache.ibatis.javassist.bytecode.AnnotationsAttribute;
import org.junit.Test;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Iterator;
import java.util.Map;

public class CglibTest {

    @Test
    public void name() throws InvocationTargetException, IllegalAccessException {
        DynaProperty property = new DynaProperty("id", String.class);
        DynaProperty property1 = new DynaProperty("name", String.class);
        BasicDynaClass basicDynaClass = new BasicDynaClass("user", null, new DynaProperty[]{property, property1});
        Class dynaBeanClass = basicDynaClass.getDynaBeanClass();
        System.out.println(dynaBeanClass);
        BasicDynaBean basicDynaBean = new BasicDynaBean(basicDynaClass);
        EmployeeBaseInfoDo user = new EmployeeBaseInfoDo();
        user.setId("");
        user.setName("name1");
        user.setDingRoleName("123");

        BeanUtils.copyProperties(basicDynaBean, user);
        Map<String, Object> map = basicDynaBean.getMap();

        Iterator<String> it = map.keySet().iterator();
        while (it.hasNext()) {
            String key = it.next();
            System.out.println(key + ":" + map.get(key));
        }
    }

    @Test
    public void name2() {

        BeanGenerator beanGenerator = new BeanGenerator();
        beanGenerator.addProperty("id", String.class);
        beanGenerator.addProperty("name", String.class);
        Object obj = beanGenerator.create();
        System.out.println("class:" + obj);
        BeanMap beanMap = BeanMap.create(obj);
        BeanCopier copier = BeanCopier.create(EmployeeBaseInfoDo.class, obj.getClass(), false);


        EmployeeBaseInfoDo user = new EmployeeBaseInfoDo();
        user.setId("124");
        user.setName("name1");
        user.setDingRoleName("123");


        copier.copy(user, obj, null);
        System.out.println(beanMap.get("name"));
        Class clazz = obj.getClass();

        Method[] methods = clazz.getDeclaredMethods();
        for (int i = 0; i < methods.length; i++) {
            System.out.println(methods[i].getName());
        }
    }

    public static void addAnnotation(String className, String attributeName, String typeName) {
        try {
            ClassPool pool = ClassPool.getDefault();
            CtClass ct = pool.get(className);
            CtField cf = ct.getField(attributeName);
            FieldInfo fieldInfo = cf.getFieldInfo();
            javassist.bytecode.AnnotationsAttribute attribute = (javassist.bytecode.AnnotationsAttribute) fieldInfo.getAttribute(AnnotationsAttribute.visibleTag);
            ConstPool cp = fieldInfo.getConstPool();
            Annotation annotation = new Annotation(typeName, cp);
            System.out.println("添加注解" + annotation);
            attribute.addAnnotation(annotation);
            System.out.println("添加后的所有注解" + Arrays.toString(attribute.getAnnotations()));
        } catch (NotFoundException e) {
            System.out.println("此类不存在" + e);
        }
    }
}
