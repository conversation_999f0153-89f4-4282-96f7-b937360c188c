package com.polaris.kpi.eval.infr.pip;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.polaris.acl.dept.repository.DeptEmpDao;
import com.polaris.kpi.eval.domain.pip.plan.entity.PipEvalScorerResult;
import com.polaris.kpi.eval.infr.pip.msg.dao.PipEvalMsgDao;
import com.polaris.kpi.eval.infr.pip.plan.dao.PipEvalScorerResultDao;
import com.polaris.kpi.eval.infr.pip.plan.ppojo.PipEvalScorerResultPo;
import com.polaris.kpi.eval.infr.pip.plan.query.PipEvalResultQuery;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.mapper.PagedList;

import java.util.List;

public class PipEvalScoreResultDaoTest {
    protected TestContext context;
    protected PipEvalScorerResultDao pipEvalScorerResultDao;


    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        pipEvalScorerResultDao = new PipEvalScorerResultDao();
        pipEvalScorerResultDao.setDomainDao(context.getDomainDao());


        PipEvalMsgDao msgDao = new PipEvalMsgDao();
        msgDao.setDomainDao(context.getDomainDao());

        DeptEmpDao empDao = new DeptEmpDao();
        msgDao.setEmpDao(empDao);

        pipEvalScorerResultDao.setMsgDao(msgDao);
    }

    @Test
    public void getPipEvalScorerResult() {
        PipEvalScorerResult scorerResult = pipEvalScorerResultDao.getPipEvalScorerResult("5a031297-1b38-48ae-bc82-375849835203", "1000105","1302001");
        System.out.println(JSONUtil.toJsonStr(scorerResult));
    }

    @Test
    public void pagedPipEvalScorerResult() {
        PipEvalResultQuery query=new PipEvalResultQuery();
        query.setCompanyId("5a031297-1b38-48ae-bc82-375849835203");
        query.setPipEvalId( "1000105");
        query.setPageNo(1);
        query.setPageSize(10);
        PagedList<PipEvalScorerResultPo> scorerResult = pipEvalScorerResultDao.pagedPipEvalScorerResult(query);
        System.out.println(JSONUtil.toJsonStr(scorerResult));
    }
    @Test
    public void listPipScorerResult() {
        List<PipEvalScorerResult> scorerResult = pipEvalScorerResultDao.listPipScorerResult("5a031297-1b38-48ae-bc82-375849835203", "1000105");
        System.out.println(JSONUtil.toJsonStr(scorerResult));
    }
}