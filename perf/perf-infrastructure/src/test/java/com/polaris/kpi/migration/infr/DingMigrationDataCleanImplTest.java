package com.polaris.kpi.migration.infr;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.polaris.acl.dept.pojo.CompanyDo;
import com.polaris.kpi.eval.infr.pip.plan.dao.PipPlanDao;
import com.polaris.kpi.migration.impl.DingMigrationDataCleanImpl;
import com.polaris.kpi.migration.repo.IDingMigrationDataClean;
import com.polaris.kpi.migration.entity.DingMigrationTask;
import com.polaris.kpi.migration.pojo.DingMigrationTaskDo;
import com.polaris.kpi.org.infr.company.dao.CompanyDaoImpl;
import com.polaris.sdk.type.TenantId;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.dao.AutoBaseDao;
import org.lufei.ibatis.dao.AutoDaoImpl;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * DingMigrationDataCleanImpl 单元测试
 */
public class DingMigrationDataCleanImplTest {
    
    protected TestContext context;
    protected DomainDaoImpl domainDao;

    @Spy
    @InjectMocks
    private DingMigrationDataCleanImpl dingMigrationDataClean;
    
    @Mock
    private CompanyDaoImpl companyDao;

    @Mock
    private AutoBaseDao autoBaseDao;

    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        domainDao = context.getDomainDao();
        autoBaseDao = context.getDomainDao();

        CompanyDaoImpl companyDao = new CompanyDaoImpl();
        companyDao.setDomainDao(context.getDomainDao());
        companyDao.setAutoBaseDao(autoBaseDao);

        MockitoAnnotations.openMocks(this);
        dingMigrationDataClean.setDomainDao(domainDao);
        dingMigrationDataClean.setCompanyDao(companyDao);
    }
    
    @Test
    public void testSave() {
        // 准备测试数据
        DingMigrationTask migrationTask = new DingMigrationTask();
        migrationTask.setId(UUID.randomUUID().toString());
        migrationTask.setCompanyId("test_company_123");
        migrationTask.setDingCorpId("test_corp_123");
        migrationTask.setDingAgentId("test_agent_123");
        migrationTask.setDingSpaceId("test_space_123");
        migrationTask.setOssPath("migration/test_corp_123/1234567890123");
        migrationTask.setStatus("1");
        
        // 执行测试
        dingMigrationDataClean.save(migrationTask);
        
        // 验证结果 - 由于使用了真实的domainDao，这里验证没有异常抛出即可
        Assert.assertNotNull("任务ID应该已设置", migrationTask.getId());

        DingMigrationTask newTask =  dingMigrationDataClean.getDingMigrationTask(migrationTask.getDingCorpId());
        // 验证方法调用
        Assert.assertEquals(migrationTask.getDingCorpId(), newTask.getDingCorpId());
    }
    
    @Test
    public void testUpdate() {
        // 准备测试数据
        DingMigrationTask migrationTask = new DingMigrationTask();
        migrationTask.setDingCorpId(("test_corp_123"));
        migrationTask.setStatus("SUCCESS");
        
        // 执行测试
        dingMigrationDataClean.update(migrationTask);
        
        // 验证方法调用
        DingMigrationTask newTask =  dingMigrationDataClean.getDingMigrationTask(migrationTask.getDingCorpId());
        // 验证方法调用
        Assert.assertEquals(migrationTask.getStatus(), newTask.getStatus());
    }
    
    @Test
    public void testGetDingMigrationTask() {
        // 准备测试数据
       // String dingCorpId = "test_corp_456";
        String dingCorpId = "test_corp_123";

        // 创建模拟的查询结果
        DingMigrationTaskDo mockTaskDo = new DingMigrationTaskDo();
        mockTaskDo.setId("test_task_456");
        mockTaskDo.setDingCorpId(dingCorpId);
        mockTaskDo.setStatus("PENDING");
        // 执行测试
        DingMigrationTask result = dingMigrationDataClean.getDingMigrationTask(dingCorpId);
        System.out.println(JSONUtil.toJsonStr(result));
        // 验证结果
        Assert.assertNotNull("查询结果不应为空", result);
        Assert.assertEquals("钉钉企业ID应该匹配", dingCorpId, result.getDingCorpId());
        Assert.assertEquals("任务ID应该匹配", "test_task_456", result.getId());
        
        // 验证方法调用
        verify(domainDao, times(1)).findDomain(any(ComQB.class), eq(DingMigrationTask.class));
    }
    
    @Test
    public void testGetDingMigrationTask_NotFound() {
        // 准备测试数据
        String dingCorpId = "test_corp_not_found";
        
        // Mock domainDao返回null
        // 执行测试
        DingMigrationTask result = dingMigrationDataClean.getDingMigrationTask(dingCorpId);
        
        // 验证结果
        Assert.assertNull("未找到任务时应该返回null", result);

    }
    
    @Test
    public void testMigrationDataClean() {
        // 准备测试数据
        DingMigrationTask migrationTask = new DingMigrationTask();
        migrationTask.setCompanyId("test_company_789");
        migrationTask.setDingCorpId("test_corp_789");
        migrationTask.setDingAgentId("test_agent_789");
        migrationTask.setDingSpaceId("test_space_789");
        
        // Mock CompanyDao的查询结果
        com.polaris.acl.dept.pojo.CompanyDo mockCompany = new com.polaris.acl.dept.pojo.CompanyDo();
        mockCompany.setId("test_company_789");
        mockCompany.setDingAgentId("old_agent_789");
        mockCompany.setDingSpaceId("old_space_789");
        
        when(companyDao.findByDingCorpId("test_corp_789")).thenReturn(mockCompany);
        
        // 执行测试
        dingMigrationDataClean.migrationDataClean(migrationTask);

        CompanyDo company = companyDao.findByDingCorpId("test_corp_789");
        Assert.assertEquals("钉钉应用ID应该更新", "test_agent_789", company.getDingAgentId());
        Assert.assertEquals("钉钉spaceID应该更新", "test_space_789", company.getDingSpaceId());

        // 验证删除操作
        verify(domainDao, times(6)).delete(any()); // 6个表的删除操作
    }
    
    @Test
    public void testMigrationDataClean_CompanyNotFound() {
        // 准备测试数据
        DingMigrationTask migrationTask = new DingMigrationTask();
        migrationTask.setCompanyId("test_company_not_found");
        migrationTask.setDingCorpId("test_corp_not_found");
        
        // Mock CompanyDao返回null
        when(companyDao.findByDingCorpId("test_corp_not_found")).thenReturn(null);
        
        // 执行测试
        dingMigrationDataClean.migrationDataClean(migrationTask);
        
        // 验证方法调用
        verify(companyDao, times(1)).updateStatus("test_company_not_found", "close");
        verify(companyDao, times(1)).findByDingCorpId("test_corp_not_found");
        verify(companyDao, times(1)).updateStatus("test_company_not_found", "using"); // 回滚状态
        
        // 验证删除操作
        verify(domainDao, times(6)).delete(any()); // 6个表的删除操作
    }
    
    @Test
    public void testMigrationDataCollback() {
        // 准备测试数据
        DingMigrationTask migrationTask = new DingMigrationTask();
        migrationTask.setCompanyId("test_company_rollback");
        migrationTask.setDingCorpId("test_corp_rollback");
        
        List<String> allTableNames = new ArrayList<>();
        allTableNames.add("company");
        allTableNames.add("employee");
        allTableNames.add("performance");
        
        // Mock CompanyDao的查询结果
        com.polaris.acl.dept.pojo.CompanyDo mockCompany = new com.polaris.acl.dept.pojo.CompanyDo();
        mockCompany.setId("test_company_rollback");
        
        when(companyDao.findByDingCorpId("test_corp_rollback")).thenReturn(mockCompany);
        
        // 执行测试
        dingMigrationDataClean.migrationDataCollback(migrationTask, allTableNames);
        
        // 验证方法调用
        verify(companyDao, times(1)).findByDingCorpId("test_corp_rollback");
        verify(companyDao, times(1)).updateStatus("test_company_rollback", "using");
        
        // 验证删除操作
        verify(domainDao, times(3)).executeBySql(anyString()); // 3个表的删除操作
    }
    
    @Test
    public void testMigrationDataCollback_CompanyNotFound() {
        // 准备测试数据
        DingMigrationTask migrationTask = new DingMigrationTask();
        migrationTask.setCompanyId("test_company_rollback_not_found");
        migrationTask.setDingCorpId("test_corp_rollback_not_found");
        
        List<String> allTableNames = new ArrayList<>();
        allTableNames.add("company");
        
        // Mock CompanyDao返回null
        when(companyDao.findByDingCorpId("test_corp_rollback_not_found")).thenReturn(null);
        
        // 执行测试
        dingMigrationDataClean.migrationDataCollback(migrationTask, allTableNames);
        
        // 验证方法调用
        verify(companyDao, times(1)).findByDingCorpId("test_corp_rollback_not_found");
        verify(companyDao, times(1)).updateStatus("test_company_rollback_not_found", "using");
        
        // 验证删除操作 - 即使公司不存在，也会执行删除操作
        verify(domainDao, times(1)).executeBySql(anyString());
    }
    
    @Test
    public void testDeleteOldData() {
        // 准备测试数据
        String companyId = "test_company_delete";
        List<String> allTableNames = new ArrayList<>();
        allTableNames.add("company");
        allTableNames.add("employee");
        allTableNames.add("performance");
        
        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method deleteOldDataMethod = DingMigrationDataCleanImpl.class
                    .getDeclaredMethod("deleteOldData", String.class, List.class);
            deleteOldDataMethod.setAccessible(true);
            
            // 执行测试
            deleteOldDataMethod.invoke(dingMigrationDataClean, companyId, allTableNames);
            
            // 验证方法调用
            verify(domainDao, times(3)).executeBySql(anyString());
            
        } catch (Exception e) {
            Assert.fail("反射调用失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testSaveAndUpdateWorkflow() {
        // 测试完整的保存和更新工作流
        String dingCorpId = "test_corp_workflow";
        
        // 1. 保存任务
        DingMigrationTask migrationTask = new DingMigrationTask();
        migrationTask.setDingCorpId(dingCorpId);
        migrationTask.setStatus("1");
        
        dingMigrationDataClean.save(migrationTask);
     //   verify(domainDao, times(1)).save(any(DingMigrationTaskDo.class));
        
        // 2. 更新任务状态
        migrationTask.setStatus("2");
        dingMigrationDataClean.update(migrationTask);
     //   verify(domainDao, times(1)).update(DingMigrationTaskDo.class, migrationTask);
        
        // 3. 查询任务
        DingMigrationTaskDo mockTaskDo = new DingMigrationTaskDo();
        mockTaskDo.setDingCorpId(dingCorpId);
        mockTaskDo.setStatus("2");
        DingMigrationTask result = dingMigrationDataClean.getDingMigrationTask(dingCorpId);
        Assert.assertNotNull("查询结果不应为空", result);
        Assert.assertEquals("状态应该匹配", "SUCCESS", result.getStatus());
    }
} 