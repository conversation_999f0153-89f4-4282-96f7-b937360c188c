package com.polaris.kpi.eval.infr.pip;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.pip.plan.entity.PipPlanType;
import com.polaris.kpi.eval.infr.pip.plan.dao.PipPlanTypeDao;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.List;
import java.util.Objects;

public class PipPlanTypeDaoTest {
    protected TestContext context;
    protected PipPlanTypeDao pipPlanTypeDao;


    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        pipPlanTypeDao = new PipPlanTypeDao();
        pipPlanTypeDao.setDomainDao(context.getDomainDao());
    }

    @Test
    public void getPipPlanTypeBase() {
        List<PipPlanType> types = pipPlanTypeDao.getPipPlanTypeBase("ece4e403-43aa-47f2-bb19-a0dd18b8e98d","1003902");
        Assert.assertTrue(Objects.nonNull(types));
        System.out.println(JSONUtil.toJsonStr(types));
    }
}