package com.polaris.kpi.eval.infr.task.repimpl;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.polaris.kpi.eval.domain.pip.plan.entity.PipEval;
import com.polaris.kpi.eval.domain.pip.plan.type.PipTalentStatus;
import com.polaris.kpi.eval.infr.pip.plan.dao.*;
import com.polaris.kpi.eval.infr.pip.plan.repimpl.PipEvalRepoImpl;
import com.polaris.kpi.org.infr.emp.dao.KpiEmpDao;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.dao.DomainDaoImpl;

import java.util.List;
import java.util.Objects;

public class PipEvalRepoTest {
    protected TestContext context;
    protected PipEvalRepoImpl pipEvalRepo;
    protected PipPlanDao pipPlanDao;
    protected PipLevelConfDao pipLevelConfDao;

    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        pipEvalRepo = new PipEvalRepoImpl();
        DomainDaoImpl domainDao = context.getDomainDao();
        pipEvalRepo.setDomainDao(domainDao);

        PipEvalDao evalDao = new PipEvalDao();
        evalDao.setDomainDao(context.getDomainDao());
        pipEvalRepo.setPipEvalDao(evalDao);

        PipChainDao pipChainDao = new PipChainDao();
        pipChainDao.setDomainDao(context.getDomainDao());
        pipEvalRepo.setPipChainDao(pipChainDao);

        PipEvalItemDao pipEvalItemDao = new PipEvalItemDao();
        pipEvalItemDao.setDomainDao(context.getDomainDao());
        pipEvalRepo.setPipEvalItemDao(pipEvalItemDao);

        PipEvalRuleDao pipEvalRuleDao = new PipEvalRuleDao();
        pipEvalRuleDao.setDomainDao(context.getDomainDao());
        pipEvalRepo.setPipEvalRuleDao(pipEvalRuleDao);

        KpiEmpDao kpiEmpDao = new KpiEmpDao();
        pipEvalRepo.setKpiEmpDao(kpiEmpDao);

        PipEvalScorerResultDao scorerResultDao = new PipEvalScorerResultDao();
        scorerResultDao.setDomainDao(context.getDomainDao());
        pipEvalRepo.setPipEvalScorerResultDao(scorerResultDao);
    }

    @Test
    public void getPipEval() {
        PipEval eval = pipEvalRepo.getPipEval("5a031297-1b38-48ae-bc82-375849835203","1003902");
        Assert.assertTrue(Objects.nonNull(eval));
        System.out.println(JSONUtil.toJsonStr(eval));
    }

    @Test
    public void saveBatchPipEvalRule() {
        String planjson = "[{\"empId\":\"1009187\",\"updatedTime\":1724749749160,\"confirmResultConf\":{\"resultConfirmType\":2,\"open\":1},\"types\":[{\"updatedTime\":1724418998000,\"type\":\"form\",\"updatedUser\":\"1602577\",\"version\":0,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"绩效回顾\",\"createdTime\":1724418998000,\"typeId\":\"1000001\",\"id\":\"1000001\",\"fields\":[{\"fieldName\":\"技术不足\",\"sort\":1,\"type\":\"text\",\"version\":0,\"isDeleted\":\"false\",\"options\":[],\"typeId\":\"\",\"fieldId\":\"b2701f08-fb8d-4638-a22c-dcd5cb11844d\",\"req\":0},{\"fieldName\":\"问题创新\",\"sort\":2,\"type\":\"text\",\"version\":0,\"isDeleted\":\"false\",\"options\":[],\"typeId\":\"\",\"fieldId\":\"fb853d7a-9ab0-4866-badd-9f69531075ce\",\"req\":0}],\"createdUser\":\"1602577\",\"order\":1},{\"updatedTime\":1724418998000,\"type\":\"table\",\"updatedUser\":\"1602577\",\"version\":0,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"行动目标\",\"createdTime\":1724418998000,\"typeId\":\"1000002\",\"id\":\"1000002\",\"fields\":[{\"fieldName\":\"行动目标\",\"sort\":1,\"type\":\"text\",\"version\":0,\"isDeleted\":\"false\",\"options\":[],\"typeId\":\"\",\"fieldId\":\"25c0287d-09de-4c6e-bf4b-d6b761af76c4\",\"req\":0},{\"fieldName\":\"优先级\",\"format\":\"1\",\"sort\":2,\"type\":\"radio\",\"version\":0,\"isDeleted\":\"false\",\"options\":[{\"label\":\"{\\\"color\\\":\\\"#FFFFFF\\\",\\\"background\\\":\\\"rgba(242, 86, 67, 1)\\\"}\",\"value\":\"极高\"},{\"label\":\"{\\\"color\\\":\\\"rgba(242, 86, 67, 1)\\\",\\\"background\\\":\\\"rgba(242, 86, 67, 0.12)\\\"}\",\"value\":\"高\"},{\"label\":\"{\\\"color\\\":\\\"rgba(26, 31, 38, 0.6)\\\",\\\"background\\\":\\\"rgba(245, 246, 247, 1)\\\"}\",\"value\":\"中\"},{\"label\":\"{\\\"color\\\":\\\"rgba(26, 31, 38, 0.6)\\\",\\\"background\\\":\\\"rgba(245, 246, 247, 1)\\\"}\",\"value\":\"低\"},{\"label\":\"{\\\"color\\\":\\\"#1A1F26\\\",\\\"background\\\":\\\"rgba(242, 86, 67, 0.24)\\\"}\",\"value\":\"a\"}],\"typeId\":\"\",\"fieldId\":\"ceda47d5-ccb7-4f74-9ccb-ea6375c68a5b\",\"req\":0},{\"fieldName\":\"起止时间\",\"format\":\"3\",\"sort\":3,\"type\":\"date\",\"version\":0,\"isDeleted\":\"false\",\"options\":[{\"label\":\"日期\",\"value\":\"1\"},{\"label\":\"日期范围\",\"value\":\"2\"},{\"label\":\"日期时间\",\"value\":\"3\"},{\"label\":\"日期时间范围\",\"value\":\"4\"}],\"typeId\":\"\",\"fieldId\":\"52898ad6-509f-4954-ab4f-2afb98efad82\",\"req\":0},{\"fieldName\":\"行动描述\",\"sort\":4,\"type\":\"text\",\"version\":0,\"isDeleted\":\"false\",\"options\":[],\"typeId\":\"\",\"fieldId\":\"c183bb4b-3bb8-4ca9-966c-5eaddf939025\",\"req\":0},{\"fieldName\":\"附件\",\"sort\":5,\"type\":\"file\",\"version\":0,\"isDeleted\":\"false\",\"options\":[],\"typeId\":\"\",\"fieldId\":\"dc317314-5128-4cb8-b180-0816c6468437\",\"req\":0}],\"createdUser\":\"1602577\",\"order\":2},{\"updatedTime\":1724418998000,\"type\":\"table\",\"updatedUser\":\"1602577\",\"version\":0,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"培训计划\",\"createdTime\":1724418998000,\"typeId\":\"1000003\",\"id\":\"1000003\",\"fields\":[{\"fieldName\":\"培训标题\",\"sort\":1,\"type\":\"text\",\"version\":0,\"isDeleted\":\"false\",\"options\":[],\"typeId\":\"\",\"fieldId\":\"6d186913-c47a-40c3-85f8-3fc8428f490a\",\"req\":0},{\"fieldName\":\"培训时间\",\"format\":\"3\",\"sort\":2,\"type\":\"date\",\"version\":0,\"isDeleted\":\"false\",\"options\":[{\"label\":\"日期\",\"value\":\"1\"},{\"label\":\"日期范围\",\"value\":\"2\"},{\"label\":\"日期时间\",\"value\":\"3\"},{\"label\":\"日期时间范围\",\"value\":\"4\"}],\"typeId\":\"\",\"fieldId\":\"b361ff7f-cd98-4a96-99c7-41f715dc2897\",\"req\":0},{\"fieldName\":\"培训地址\",\"sort\":3,\"type\":\"text\",\"version\":0,\"isDeleted\":\"false\",\"options\":[],\"typeId\":\"\",\"fieldId\":\"b80fa014-f9fa-4489-9173-3c4eec767af0\",\"req\":0},{\"fieldName\":\"培训目标\",\"sort\":4,\"type\":\"text\",\"version\":0,\"isDeleted\":\"false\",\"options\":[],\"typeId\":\"\",\"fieldId\":\"19d02750-46a1-4823-a188-2b646c2c231c\",\"req\":0},{\"fieldName\":\"附件\",\"sort\":5,\"type\":\"file\",\"version\":0,\"isDeleted\":\"false\",\"options\":[],\"typeId\":\"\",\"fieldId\":\"feb0aae1-4f50-4e56-90ab-ef0170bef913\",\"req\":0}],\"createdUser\":\"1602577\",\"order\":3}],\"goalConfirmConf\":{\"auditNodes\":[{\"transferFlag\":\"false\",\"approvalOrder\":1,\"modifyFlag\":\"true\",\"openSign\":\"false\",\"nodeType\":\"goal_created\",\"allowEditGoal\":\"true\",\"approverInfo\":\"\",\"confirmAuditSign\":\"false\",\"raters\":[{\"empId\":\"1009187\",\"type\":0,\"empName\":\"李志旭\"}],\"nodeTitle\":\"目标制定\",\"approverType\":\"taskEmp\"}],\"multiType\":\"or\",\"open\":1},\"goalExcuteConf\":{\"auditNodes\":[{\"approvalOrder\":1,\"approverInfo\":\"\",\"approverName\":\"被考核人\",\"raters\":[],\"approverType\":\"taskEmp\"}],\"open\":1},\"scoreConf\":{\"enterScoreMethod\":\"auto\",\"pipLevelConf\":{\"type\":1,\"version\":0,\"isDeleted\":\"false\",\"name\":\"aa\",\"id\":\"1000001\",\"ruleOptions\":[{\"name\":\"a\",\"desc\":\"a\"}]},\"scoreStartRuleDay\":1,\"scoreOrderType\":\"sameTime\",\"auditNodes\":[{\"approvalOrder\":1,\"nodeType\":\"self_score\",\"approverInfo\":\"\",\"approverName\":\"被考核人\",\"raters\":[{\"empId\":\"1009187\",\"type\":0,\"empName\":\"李志旭\"}],\"nodeTitle\":\"自评\",\"openSummaryReq\":0,\"approverType\":\"taskEmp\"},{\"approvalOrder\":2,\"nodeType\":\"super_score\",\"approverInfo\":\"1\",\"approverName\":\"直属主管\",\"raters\":[{\"empId\":\"12627917-53e2-4604-9665-5ad204778882\",\"level\":1,\"type\":1,\"empName\":\"罗伟飞\"}],\"nodeTitle\":\"上级评\",\"openSummaryReq\":0,\"approverType\":\"manager\"}],\"multiType\":\"or\",\"autoEnterScoreCycleEnd\":\"before\",\"openScoreAnonymous\":0,\"open\":1},\"updatedUser\":\"1302001\",\"version\":0,\"cycleEndDate\":\"2024-08-31\",\"empAvatar\":\"https://static-legacy.dingtalk.com/media/lQLPDhtwSlDwYpfNAmDNAmCwP2waI3J1qnYCiKZNDcDTAA_608_608.png\",\"cycleStartDate\":\"2024-08-01\",\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"templId\":\"1000001\",\"pipEvals\":[{\"empId\":\"1009187\",\"empOrgName\":\"所在四级部门\",\"planName\":\"张三改进计划\",\"evalRule\":{\"updatedTime\":1724749749341,\"indexRaters\":[{\"node\":\"scoring\",\"raters\":[{\"empId\":\"1009187\",\"type\":0,\"empName\":\"李志旭\"},{\"empId\":\"12627917-53e2-4604-9665-5ad204778882\",\"level\":1,\"type\":1,\"empName\":\"罗伟飞\"}]}],\"scoreRater\":{\"enterScoreMethod\":\"auto\",\"pipLevelConf\":{\"type\":1,\"version\":0,\"isDeleted\":\"false\",\"name\":\"aa\",\"id\":\"1000001\",\"ruleOptions\":[{\"name\":\"a\",\"desc\":\"a\"}]},\"scoreStartRuleDay\":1,\"scoreOrderType\":\"sameTime\",\"auditNodes\":[{\"approvalOrder\":1,\"nodeType\":\"self_score\",\"approverInfo\":\"\",\"approverName\":\"被考核人\",\"raters\":[{\"empId\":\"1009187\",\"type\":0,\"empName\":\"李志旭\"}],\"nodeTitle\":\"自评\",\"openSummaryReq\":0,\"approverType\":\"taskEmp\"},{\"approvalOrder\":2,\"nodeType\":\"super_score\",\"approverInfo\":\"1\",\"approverName\":\"直属主管\",\"raters\":[{\"empId\":\"12627917-53e2-4604-9665-5ad204778882\",\"level\":1,\"type\":1,\"empName\":\"罗伟飞\"}],\"nodeTitle\":\"上级评\",\"openSummaryReq\":0,\"approverType\":\"manager\"}],\"multiType\":\"or\",\"autoEnterScoreCycleEnd\":\"before\",\"openScoreAnonymous\":0,\"open\":1},\"updatedUser\":\"1302001\",\"version\":0,\"resultAffirmConf\":{\"resultConfirmType\":2,\"open\":1},\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"goalConfirmRater\":{\"auditNodes\":[{\"transferFlag\":\"false\",\"approvalOrder\":1,\"modifyFlag\":\"true\",\"openSign\":\"false\",\"nodeType\":\"goal_created\",\"allowEditGoal\":\"true\",\"approverInfo\":\"\",\"confirmAuditSign\":\"false\",\"raters\":[{\"empId\":\"1009187\",\"type\":0,\"empName\":\"李志旭\"}],\"nodeTitle\":\"目标制定\",\"approverType\":\"taskEmp\"}],\"multiType\":\"or\",\"open\":1},\"isDeleted\":\"false\",\"excuteRater\":{\"auditNodes\":[{\"approvalOrder\":1,\"approverInfo\":\"\",\"approverName\":\"被考核人\",\"raters\":[],\"approverType\":\"taskEmp\"}],\"open\":1},\"createdTime\":1724749749341,\"createdUser\":\"1302001\"},\"version\":0,\"cycleEndDate\":\"2024-08-31\",\"empAvatar\":\"https://static-legacy.dingtalk.com/media/lQLPDhtwSlDwYpfNAmDNAmCwP2waI3J1qnYCiKZNDcDTAA_608_608.png\",\"empAllOrgId\":\"1025002\",\"cycleStartDate\":\"2024-08-01\",\"isDeleted\":\"false\",\"empName\":\"李志旭\",\"empOrgId\":\"1025002\",\"empAllOrgName\":\"所在四级部门\"}],\"isDeleted\":\"false\",\"templName\":\"啊啊啊\",\"empName\":\"李志旭\",\"name\":\"张三改进计划\",\"createdTime\":1724749749160,\"createdUser\":\"1302001\",\"desc\":\"234234\"},{\"empId\":\"1302001\",\"updatedTime\":1724749749160,\"confirmResultConf\":{\"resultConfirmType\":2,\"open\":1},\"types\":[{\"updatedTime\":1724418998000,\"type\":\"form\",\"updatedUser\":\"1602577\",\"version\":0,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"绩效回顾\",\"createdTime\":1724418998000,\"typeId\":\"1000001\",\"id\":\"1000001\",\"fields\":[{\"fieldName\":\"技术不足\",\"sort\":1,\"type\":\"text\",\"version\":0,\"isDeleted\":\"false\",\"options\":[],\"typeId\":\"\",\"fieldId\":\"b2701f08-fb8d-4638-a22c-dcd5cb11844d\",\"req\":0},{\"fieldName\":\"问题创新\",\"sort\":2,\"type\":\"text\",\"version\":0,\"isDeleted\":\"false\",\"options\":[],\"typeId\":\"\",\"fieldId\":\"fb853d7a-9ab0-4866-badd-9f69531075ce\",\"req\":0}],\"createdUser\":\"1602577\",\"order\":1},{\"updatedTime\":1724418998000,\"type\":\"table\",\"updatedUser\":\"1602577\",\"version\":0,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"行动目标\",\"createdTime\":1724418998000,\"typeId\":\"1000002\",\"id\":\"1000002\",\"fields\":[{\"fieldName\":\"行动目标\",\"sort\":1,\"type\":\"text\",\"version\":0,\"isDeleted\":\"false\",\"options\":[],\"typeId\":\"\",\"fieldId\":\"25c0287d-09de-4c6e-bf4b-d6b761af76c4\",\"req\":0},{\"fieldName\":\"优先级\",\"format\":\"1\",\"sort\":2,\"type\":\"radio\",\"version\":0,\"isDeleted\":\"false\",\"options\":[{\"label\":\"{\\\"color\\\":\\\"#FFFFFF\\\",\\\"background\\\":\\\"rgba(242, 86, 67, 1)\\\"}\",\"value\":\"极高\"},{\"label\":\"{\\\"color\\\":\\\"rgba(242, 86, 67, 1)\\\",\\\"background\\\":\\\"rgba(242, 86, 67, 0.12)\\\"}\",\"value\":\"高\"},{\"label\":\"{\\\"color\\\":\\\"rgba(26, 31, 38, 0.6)\\\",\\\"background\\\":\\\"rgba(245, 246, 247, 1)\\\"}\",\"value\":\"中\"},{\"label\":\"{\\\"color\\\":\\\"rgba(26, 31, 38, 0.6)\\\",\\\"background\\\":\\\"rgba(245, 246, 247, 1)\\\"}\",\"value\":\"低\"},{\"label\":\"{\\\"color\\\":\\\"#1A1F26\\\",\\\"background\\\":\\\"rgba(242, 86, 67, 0.24)\\\"}\",\"value\":\"a\"}],\"typeId\":\"\",\"fieldId\":\"ceda47d5-ccb7-4f74-9ccb-ea6375c68a5b\",\"req\":0},{\"fieldName\":\"起止时间\",\"format\":\"3\",\"sort\":3,\"type\":\"date\",\"version\":0,\"isDeleted\":\"false\",\"options\":[{\"label\":\"日期\",\"value\":\"1\"},{\"label\":\"日期范围\",\"value\":\"2\"},{\"label\":\"日期时间\",\"value\":\"3\"},{\"label\":\"日期时间范围\",\"value\":\"4\"}],\"typeId\":\"\",\"fieldId\":\"52898ad6-509f-4954-ab4f-2afb98efad82\",\"req\":0},{\"fieldName\":\"行动描述\",\"sort\":4,\"type\":\"text\",\"version\":0,\"isDeleted\":\"false\",\"options\":[],\"typeId\":\"\",\"fieldId\":\"c183bb4b-3bb8-4ca9-966c-5eaddf939025\",\"req\":0},{\"fieldName\":\"附件\",\"sort\":5,\"type\":\"file\",\"version\":0,\"isDeleted\":\"false\",\"options\":[],\"typeId\":\"\",\"fieldId\":\"dc317314-5128-4cb8-b180-0816c6468437\",\"req\":0}],\"createdUser\":\"1602577\",\"order\":2},{\"updatedTime\":1724418998000,\"type\":\"table\",\"updatedUser\":\"1602577\",\"version\":0,\"companyId\":\"\",\"isDeleted\":\"false\",\"name\":\"培训计划\",\"createdTime\":1724418998000,\"typeId\":\"1000003\",\"id\":\"1000003\",\"fields\":[{\"fieldName\":\"培训标题\",\"sort\":1,\"type\":\"text\",\"version\":0,\"isDeleted\":\"false\",\"options\":[],\"typeId\":\"\",\"fieldId\":\"6d186913-c47a-40c3-85f8-3fc8428f490a\",\"req\":0},{\"fieldName\":\"培训时间\",\"format\":\"3\",\"sort\":2,\"type\":\"date\",\"version\":0,\"isDeleted\":\"false\",\"options\":[{\"label\":\"日期\",\"value\":\"1\"},{\"label\":\"日期范围\",\"value\":\"2\"},{\"label\":\"日期时间\",\"value\":\"3\"},{\"label\":\"日期时间范围\",\"value\":\"4\"}],\"typeId\":\"\",\"fieldId\":\"b361ff7f-cd98-4a96-99c7-41f715dc2897\",\"req\":0},{\"fieldName\":\"培训地址\",\"sort\":3,\"type\":\"text\",\"version\":0,\"isDeleted\":\"false\",\"options\":[],\"typeId\":\"\",\"fieldId\":\"b80fa014-f9fa-4489-9173-3c4eec767af0\",\"req\":0},{\"fieldName\":\"培训目标\",\"sort\":4,\"type\":\"text\",\"version\":0,\"isDeleted\":\"false\",\"options\":[],\"typeId\":\"\",\"fieldId\":\"19d02750-46a1-4823-a188-2b646c2c231c\",\"req\":0},{\"fieldName\":\"附件\",\"sort\":5,\"type\":\"file\",\"version\":0,\"isDeleted\":\"false\",\"options\":[],\"typeId\":\"\",\"fieldId\":\"feb0aae1-4f50-4e56-90ab-ef0170bef913\",\"req\":0}],\"createdUser\":\"1602577\",\"order\":3}],\"goalConfirmConf\":{\"auditNodes\":[{\"transferFlag\":\"false\",\"approvalOrder\":1,\"modifyFlag\":\"true\",\"openSign\":\"false\",\"nodeType\":\"goal_created\",\"allowEditGoal\":\"true\",\"approverInfo\":\"\",\"confirmAuditSign\":\"false\",\"raters\":[{\"empId\":\"1302001\",\"type\":0,\"empName\":\"苏小秋\"}],\"nodeTitle\":\"目标制定\",\"approverType\":\"taskEmp\"}],\"multiType\":\"or\",\"open\":1},\"goalExcuteConf\":{\"auditNodes\":[{\"approvalOrder\":1,\"approverInfo\":\"\",\"approverName\":\"被考核人\",\"raters\":[],\"approverType\":\"taskEmp\"}],\"open\":1},\"scoreConf\":{\"enterScoreMethod\":\"auto\",\"pipLevelConf\":{\"type\":1,\"version\":0,\"isDeleted\":\"false\",\"name\":\"aa\",\"id\":\"1000001\",\"ruleOptions\":[{\"name\":\"a\",\"desc\":\"a\"}]},\"scoreStartRuleDay\":1,\"scoreOrderType\":\"sameTime\",\"auditNodes\":[{\"approvalOrder\":1,\"nodeType\":\"self_score\",\"approverInfo\":\"\",\"approverName\":\"被考核人\",\"raters\":[{\"empId\":\"1302001\",\"type\":0,\"empName\":\"苏小秋\"}],\"nodeTitle\":\"自评\",\"openSummaryReq\":0,\"approverType\":\"taskEmp\"},{\"approvalOrder\":2,\"nodeType\":\"super_score\",\"approverInfo\":\"1\",\"approverName\":\"直属主管\",\"raters\":[{\"empId\":\"1602582\",\"level\":1,\"type\":1,\"empName\":\"Sasha\"}],\"nodeTitle\":\"上级评\",\"openSummaryReq\":0,\"approverType\":\"manager\"}],\"multiType\":\"or\",\"autoEnterScoreCycleEnd\":\"before\",\"openScoreAnonymous\":0,\"open\":1},\"updatedUser\":\"1302001\",\"version\":0,\"cycleEndDate\":\"2024-08-31\",\"empAvatar\":\"https://static-legacy.dingtalk.com/media/lADPD26eO1kRFzrNAbDNAbA_432_432.jpg\",\"cycleStartDate\":\"2024-08-01\",\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"templId\":\"1000001\",\"pipEvals\":[{\"empId\":\"1302001\",\"empOrgName\":\"前端一组222\",\"planName\":\"张三改进计划\",\"evalRule\":{\"updatedTime\":1724749749384,\"indexRaters\":[{\"node\":\"scoring\",\"raters\":[{\"empId\":\"1302001\",\"type\":0,\"empName\":\"苏小秋\"},{\"empId\":\"1602582\",\"level\":1,\"type\":1,\"empName\":\"Sasha\"}]}],\"scoreRater\":{\"enterScoreMethod\":\"auto\",\"pipLevelConf\":{\"type\":1,\"version\":0,\"isDeleted\":\"false\",\"name\":\"aa\",\"id\":\"1000001\",\"ruleOptions\":[{\"name\":\"a\",\"desc\":\"a\"}]},\"scoreStartRuleDay\":1,\"scoreOrderType\":\"sameTime\",\"auditNodes\":[{\"approvalOrder\":1,\"nodeType\":\"self_score\",\"approverInfo\":\"\",\"approverName\":\"被考核人\",\"raters\":[{\"empId\":\"1302001\",\"type\":0,\"empName\":\"苏小秋\"}],\"nodeTitle\":\"自评\",\"openSummaryReq\":0,\"approverType\":\"taskEmp\"},{\"approvalOrder\":2,\"nodeType\":\"super_score\",\"approverInfo\":\"1\",\"approverName\":\"直属主管\",\"raters\":[{\"empId\":\"1602582\",\"level\":1,\"type\":1,\"empName\":\"Sasha\"}],\"nodeTitle\":\"上级评\",\"openSummaryReq\":0,\"approverType\":\"manager\"}],\"multiType\":\"or\",\"autoEnterScoreCycleEnd\":\"before\",\"openScoreAnonymous\":0,\"open\":1},\"updatedUser\":\"1302001\",\"version\":0,\"resultAffirmConf\":{\"resultConfirmType\":2,\"open\":1},\"companyId\":\"5a031297-1b38-48ae-bc82-375849835203\",\"goalConfirmRater\":{\"auditNodes\":[{\"transferFlag\":\"false\",\"approvalOrder\":1,\"modifyFlag\":\"true\",\"openSign\":\"false\",\"nodeType\":\"goal_created\",\"allowEditGoal\":\"true\",\"approverInfo\":\"\",\"confirmAuditSign\":\"false\",\"raters\":[{\"empId\":\"1302001\",\"type\":0,\"empName\":\"苏小秋\"}],\"nodeTitle\":\"目标制定\",\"approverType\":\"taskEmp\"}],\"multiType\":\"or\",\"open\":1},\"isDeleted\":\"false\",\"excuteRater\":{\"auditNodes\":[{\"approvalOrder\":1,\"approverInfo\":\"\",\"approverName\":\"被考核人\",\"raters\":[],\"approverType\":\"taskEmp\"}],\"open\":1},\"createdTime\":1724749749384,\"createdUser\":\"1302001\"},\"version\":0,\"cycleEndDate\":\"2024-08-31\",\"empAvatar\":\"https://static-legacy.dingtalk.com/media/lADPD26eO1kRFzrNAbDNAbA_432_432.jpg\",\"empAllOrgId\":\"1023626\",\"cycleStartDate\":\"2024-08-01\",\"isDeleted\":\"false\",\"empName\":\"苏小秋\",\"empOrgId\":\"1023626\",\"empAllOrgName\":\"前端一组222\"}],\"isDeleted\":\"false\",\"templName\":\"啊啊啊\",\"empName\":\"苏小秋\",\"name\":\"张三改进计划\",\"createdTime\":1724749749160,\"createdUser\":\"1302001\",\"desc\":\"234234\"}]";
        List<PipEval> evals = JSON.parseArray(planjson, PipEval.class);
        pipEvalRepo.saveBatchPipEvalRule("5a031297-1b38-48ae-bc82-375849835203", "1009187", evals);

        PipEval eval = pipEvalRepo.getPipEval("5a031297-1b38-48ae-bc82-375849835203", "1003902");
        Assert.assertTrue(Objects.nonNull(eval));
        Assert.assertTrue(Objects.nonNull(eval.getItems()));
        Assert.assertTrue(Objects.nonNull(eval.getEvalRule()));
        Assert.assertTrue(Objects.nonNull(eval.getChain()));
    }

    @Test
    public void deletePipEval() {
        pipEvalRepo.deletePipEval("5a031297-1b38-48ae-bc82-375849835203", "1009187", "1000402");

        PipEval eval = pipEvalRepo.getPipEval("5a031297-1b38-48ae-bc82-375849835203", "1000402");
        Assert.assertTrue(Objects.isNull(eval));
    }

    @Test
    public void updatePipStage() {
        pipEvalRepo.updatePipStage("5a031297-1b38-48ae-bc82-375849835203", "1000302", PipTalentStatus.EXCUTING);

        PipEval eval = pipEvalRepo.getPipEval("5a031297-1b38-48ae-bc82-375849835203", "1000302");
        Assert.assertTrue(Objects.nonNull(eval));
        Assert.assertTrue(eval.getPipStatus().equals("excuting"));
    }

    @Test
    public void updatePipEval() {
        PipEval eval = pipEvalRepo.getPipEval("5a031297-1b38-48ae-bc82-375849835203", "1000105");
        eval.setSummary("12321321");
        pipEvalRepo.updatePipEval(eval);

        PipEval neweval = pipEvalRepo.getPipEval("5a031297-1b38-48ae-bc82-375849835203", "1000105");
        Assert.assertTrue(Objects.nonNull(neweval));
        Assert.assertTrue(neweval.getSummary().equals("12321321"));
    }
}