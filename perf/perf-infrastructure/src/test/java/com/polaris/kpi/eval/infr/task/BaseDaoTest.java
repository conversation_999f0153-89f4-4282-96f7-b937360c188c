package com.polaris.kpi.eval.infr.task;

import ch.vorburger.exec.ManagedProcessException;
import ch.vorburger.mariadb4j.DB;
import ch.vorburger.mariadb4j.DBConfigurationBuilder;
import cn.hutool.core.util.StrUtil;
import com.polaris.test.base.TestContext;
import org.junit.AfterClass;

import java.io.IOException;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.infr.task
 * @Author: lufei
 * @CreateTime: 2023-12-15  22:38
 * @Description: TODO
 * @Version: 1.0
 */
public class BaseDaoTest {

    protected static TestContext context;
    protected String companyId = "ece4e403-43aa-47f2-bb19-a0dd18b8e98d";
    protected static DB db;

    public static void beforeClass(String dataSql) throws Exception {
//        System.out.println("开始beforeClass");
//        DBConfigurationBuilder configBuilder = DBConfigurationBuilder.newBuilder();
//        //configBuilder.setUnpackingFromClasspath(false);
//        configBuilder.setPort(3388);
//        configBuilder.setDataDir("../tree/db/BaseTest");
//        db = DB.newEmbeddedDB(configBuilder.build());
//        db.start();
//        db.run("drop database if exists acs");
//        db.createDB("acs", "acs", "acs");
//        db.source("com/polaris/kpi/base/schemal.sql", "acs", "acs", "acs");
//        db.source("com/polaris/kpi/base/testData.sql", "acs", "acs", "acs");
//        if (StrUtil.isNotBlank(dataSql)) {
//            db.source(dataSql, "acs", "acs", "acs");
//        }
//        System.out.println("完成beforeClass");
        context = new TestContext("com/polaris/kpi/base/db.properties");
    }

    protected void setDao(Object dao, String filedBaseDao) throws IOException {
        context.confDomainDaoFor(dao, filedBaseDao);
    }

//    @AfterClass
//    public static void clean() throws ManagedProcessException {
//        db.run("drop database acs");
//        db.stop();
//    }

}
