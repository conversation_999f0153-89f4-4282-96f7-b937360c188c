package com.polaris.kpi.eval.infr.temp.builder;

import cn.com.seendio.polaris.code.TestContext;
import com.alibaba.fastjson.JSONObject;
import com.polaris.kpi.eval.infr.task.repimpl.TaskUserRepoImpl;
import com.polaris.kpi.eval.infr.temp.repimpl.StdTempRepoImpl;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.dao.DomainDaoImpl;

import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2024/10/29 16:57
 */
public class StdTemp2DataBuilderTest {
    private DomainDaoImpl domainDao;
    protected TestContext context;

    private final Supplier<String> kpiTypeGen = () -> domainDao.nextLongAsStr("perf_templ_kpi_type");
    private final Supplier<String> kpiItemGen = () -> domainDao.nextLongAsStr("perf_templ_kpi_item");

    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        domainDao = context.getDomainDao();
    }

    @Test
    public void build(){
        String kpiSyncJson = "{\"syncOpen\":1,\"reqItems\":{\"input_finish_value\":{\"isReq\":1,\"desc\":\"完成值必填\"},\"attachment\":{\"isReq\":0,\"desc\":\"附件必填\"},\"comment\":{\"isReq\":0,\"desc\":\"备注必填\"}}}";
        String otherReq = "{\"input_finish_value\":{\"isReq\":1,\"desc\":\"完成值必填\"},\"attachment\":{\"isReq\":0,\"desc\":\"附件必填\"},\"comment\":{\"isReq\":0,\"desc\":\"备注必填\"}}";
        JSONObject kpiSyncObj = JSONObject.parseObject(kpiSyncJson);
        JSONObject otherReqObj = JSONObject.parseObject(otherReq);
        // 检查syncOpen是否等于1
        if (kpiSyncObj.getInteger("syncOpen") == 1) {
            JSONObject reqItems = kpiSyncObj.getJSONObject("reqItems");

            // 遍历otherReqObj的键值对
            for (String key : otherReqObj.keySet()) {
                JSONObject otherReqItem = otherReqObj.getJSONObject(key);
                JSONObject reqItem = reqItems.getJSONObject(key);

                // 检查键是否存在于reqItems中
                if (reqItem != null) {
                    if (reqItem.getInteger("isReq") == 1 ){
                        // 比较isReq值是否相同
                        boolean isReqSame = otherReqItem.getInteger("isReq").equals(reqItem.getInteger("isReq"));
                        if(!isReqSame) {
                            System.out.println("维度与指标未保持一致！");
                        } else {
                            System.out.println("维度与指标保持一致！");
                        }
                    } else {
                        System.out.println("维度与指标无须保持一致！");
                    }
                }
            }
        } else {
            System.out.println("syncOpen is not equal to 1");
        }
    }
}
