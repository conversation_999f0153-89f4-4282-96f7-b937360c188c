package com.polaris.kpi.eval.infr.pip;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.pip.plan.entity.PipEvalItem;
import com.polaris.kpi.eval.infr.pip.plan.dao.PipEvalItemDao;
import com.polaris.sdk.type.ListWrap;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

public class PipEvalItemDaoTest {
    protected TestContext context;
    protected PipEvalItemDao pipEvalItemDao;


    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        pipEvalItemDao = new PipEvalItemDao();
        pipEvalItemDao.setDomainDao(context.getDomainDao());
    }

    @Test
    public void listPipEvalItem() {
        List<PipEvalItem> items = pipEvalItemDao.listPipEvalItem("5a031297-1b38-48ae-bc82-375849835203", Arrays.asList("1000105", "1000106"));
        System.out.println(JSONUtil.toJsonStr(items));
    }

    @Test
    public void ListWrapPipEvalItem() {
        ListWrap<PipEvalItem> evalItems = pipEvalItemDao.ListWrapPipEvalItem("ece4e403-43aa-47f2-bb19-a0dd18b8e98d", "1000105");
        System.out.println(JSONUtil.toJsonStr(evalItems));
    }
}