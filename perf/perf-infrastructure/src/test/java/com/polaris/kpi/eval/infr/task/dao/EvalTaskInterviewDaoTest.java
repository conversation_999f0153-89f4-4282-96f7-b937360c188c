package com.polaris.kpi.eval.infr.task.dao;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.polaris.kpi.eval.infr.task.ppojo.interview.po.AdminTaskInterviewPo;
import com.polaris.kpi.eval.infr.task.ppojo.interview.po.InterviewCntPo;
import com.polaris.kpi.eval.infr.task.query.interview.EvalTaskInterviewQuery;
import com.polaris.sdk.type.TenantId;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.mapper.PagedList;

public class EvalTaskInterviewDaoTest {
    protected TestContext context;
    protected EvalTaskInterviewDao evalTaskInterviewDao;


    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        evalTaskInterviewDao = new EvalTaskInterviewDao();
        evalTaskInterviewDao.setDomainDao(context.getDomainDao());
    }

    @Test
    public void getInterviewCnt() {
        EvalTaskInterviewQuery qry = new EvalTaskInterviewQuery();
        qry.setCompanyId(new TenantId("5a031297-1b38-48ae-bc82-375849835203"));
        qry.setTaskId("1635301");
        qry.setCycleId("");
        InterviewCntPo cntPo = evalTaskInterviewDao.getInterviewCnt(qry);
        System.out.println(JSONUtil.toJsonStr(cntPo));
    }

    @Test
    public void pageTaskInterview() {
        String queryJson = "{\"opAdminType\":\"child\",\"cycleId\":\"\",\"pageSize\":20,\"opEmpId\":{\"id\":\"1299001\"},\"taskIdList\":[],\"empIdList\":[],\"companyId\":{\"id\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\"},\"orgIdList\":[],\"pageNo\":1,\"taskId\":\"1630902\"}";
        EvalTaskInterviewQuery query = JSON.parseObject(queryJson, EvalTaskInterviewQuery.class);
        query.setCompanyId(new TenantId("ece4e403-43aa-47f2-bb19-a0dd18b8e98d"));
        PagedList<AdminTaskInterviewPo> pageds = evalTaskInterviewDao.pageTaskInterview(query);
        System.out.println(JSONUtil.toJsonStr(pageds));
    }
}