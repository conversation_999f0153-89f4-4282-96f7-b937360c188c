package com.polaris.kpi.eval.infr.pip;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.pip.plan.entity.PipEvalCompletionLog;
import com.polaris.kpi.eval.infr.pip.plan.dao.PipEvalCompletionLogDao;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

public class PipEvalCompletionLogDaoTest {
    protected TestContext context;
    protected PipEvalCompletionLogDao pipEvalCompletionLogDao;


    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        pipEvalCompletionLogDao = new PipEvalCompletionLogDao();
        pipEvalCompletionLogDao.setDomainDao(context.getDomainDao());
    }

    @Test
    public void listPipEvalCompletionLog() {
        List<PipEvalCompletionLog> completionLogs = pipEvalCompletionLogDao.listPipEvalCompletionLog("5a031297-1b38-48ae-bc82-375849835203", "1000105","1000001");
        System.out.println(JSONUtil.toJsonStr(completionLogs));
    }
}