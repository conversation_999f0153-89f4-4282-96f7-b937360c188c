package com.polaris.kpi.eval.infr.task.dao;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.perf.www.vo.task.query.EvaluateTaskKpiQueryVO;
import com.polaris.kpi.eval.domain.task.entity.EvalKpi;
import com.polaris.kpi.eval.domain.task.entity.ExportInputValue;
import com.polaris.kpi.eval.infr.task.query.TaskKpiItemQuery;
import com.polaris.kpi.org.domain.dept.entity.CompanyConf;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.lufei.ibatis.dao.DomainDaoImpl;

import java.util.List;

public class EvalKpiDaoTest {
    protected TestContext context;
    private DomainDaoImpl domainDao;
    private EvalKpiDao evalKpiDao;

    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        
        evalKpiDao = new EvalKpiDao();
        domainDao = context.getDomainDao();
        evalKpiDao.setDomainDao(context.getDomainDao());
    }
    @Test
    @DisplayName("分页查询历史指标")
    public void pageTaskKpiItem() {
        TaskKpiItemQuery query = new TaskKpiItemQuery();
        query.setCompanyId("ece4e403-43aa-47f2-bb19-a0dd18b8e98d");
        query.setTaskUserId("1273903");
        List<EvalKpi> pos = evalKpiDao.pageTaskKpiItem(query);
        System.out.println(JSONUtil.toJsonStr(pos));
    }
    @Test
    @DisplayName("批量完成值录入查询列表")
    public void queryFinishValueList() {
        String json = "{\"empId\":\"\",\"kpiItemId\":\"\",\"orderBy\":\"emp\",\"performanceType\":1,\"companyId\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\",\"taskId\":\"\",\"createdUser\":\"1100018\"}";
        String confjson = "{\"supIsCanAutoScoreItem\":1,\"enterOnDingMsg\":1,\"ignoreVacancyManager\":0,\"indLevelGroup\":1,\"canResSubmitInputFinish\":0,\"indInput202308021\":0,\"evalEmpOnLogAuth\":1,\"companyId\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\",\"enterOnScoring\":0,\"dics\":[{\"sysDicType\":\"unit\",\"sysDicTypeId\":\"1\",\"dicValue\":\"立方米\",\"companyId\":\"\",\"id\":\"051786f2-deb5-4e73-b6a8-7597555eca06\"},{\"sysDicType\":\"unit\",\"sysDicTypeId\":\"1\",\"dicValue\":\"公顷\",\"companyId\":\"\",\"id\":\"10bb700b-fbbf-4857-9161-16576c62c274\"},{\"sysDicType\":\"unit\",\"sysDicTypeId\":\"1\",\"dicValue\":\"万美元\",\"companyId\":\"\",\"id\":\"191f3749-5343-4142-ab70-fadc09471043\"},{\"sysDicType\":\"unit\",\"sysDicTypeId\":\"1\",\"dicValue\":\"完全\",\"companyId\":\"\",\"id\":\"1a1ec442-7da8-4e4f-962e-6beb363ceed7\"},{\"sysDicType\":\"unit\",\"sysDicTypeId\":\"1\",\"dicValue\":\"米\",\"companyId\":\"\",\"id\":\"1cf92304-919c-496c-9e87-3f09833a7828\"},{\"sysDicType\":\"unit\",\"sysDicTypeId\":\"1\",\"dicValue\":\"1\",\"companyId\":\"\",\"id\":\"2c620972-50bc-4aec-a05b-d1edfe91ca2d\"},{\"sysDicType\":\"kpiType\",\"sysDicTypeId\":\"2\",\"dicValue\":\"行政类\",\"companyId\":\"\",\"id\":\"3b72b4e1-437e-48b9-8b66-4897d17a61c5\"},{\"sysDicType\":\"kpiType\",\"sysDicTypeId\":\"2\",\"dicValue\":\"售后类\",\"companyId\":\"\",\"id\":\"3c9ea3ec-f480-4450-bce6-f483bca74559\"},{\"sysDicType\":\"kpiType\",\"sysDicTypeId\":\"2\",\"dicValue\":\"价值观类\",\"companyId\":\"\",\"id\":\"4a683074-bd39-4494-8fa1-8d060da04838\"},{\"sysDicType\":\"kpiType\",\"sysDicTypeId\":\"2\",\"dicValue\":\"业绩类\",\"companyId\":\"\",\"id\":\"5471c3e4-72e2-4cfd-96c9-ed0b90fe00ee\"},{\"sysDicType\":\"unit\",\"sysDicTypeId\":\"1\",\"dicValue\":\"分\",\"companyId\":\"\",\"id\":\"6b5a0210-1e25-4915-a3db-57a83ebda0ab\"},{\"sysDicType\":\"unit\",\"sysDicTypeId\":\"1\",\"dicValue\":\"元\",\"companyId\":\"\",\"id\":\"6b6f85b8-dc77-434b-881f-b17e4fb1b3be\"},{\"sysDicType\":\"unit\",\"sysDicTypeId\":\"1\",\"dicValue\":\"aa\",\"companyId\":\"\",\"id\":\"74f5ac96-e4db-42fb-b1b1-81f62cd58c34\"},{\"sysDicType\":\"unit\",\"sysDicTypeId\":\"1\",\"dicValue\":\"次\",\"companyId\":\"\",\"id\":\"7da2faa3-fe31-4935-81aa-3f7eedc656f5\"},{\"sysDicType\":\"unit\",\"sysDicTypeId\":\"1\",\"dicValue\":\"家\",\"companyId\":\"\",\"id\":\"ce042617-ec88-4444-9555-6fbf3e8e4951\"},{\"sysDicType\":\"unit\",\"isTemporary\":\"false\",\"sysDicTypeId\":\"1\",\"dicValue\":\"%\",\"companyId\":\"\",\"id\":\"d0f353b7-aade-444c-a190-0ed9a087277c\"},{\"sysDicType\":\"unit\",\"sysDicTypeId\":\"1\",\"dicValue\":\"11\",\"companyId\":\"\",\"id\":\"e927c230-0ddd-46e4-9483-e13b5cc4dc25\"},{\"sysDicType\":\"unit\",\"sysDicTypeId\":\"1\",\"dicValue\":\"万元\",\"companyId\":\"\",\"id\":\"ff0dcf76-2aa4-43bf-9151-9218d8c7b151\"}],\"tempAuth\":1,\"openConfs\":[{\"updatedTime\":1729582103000,\"confCode\":\"ask360_auth\",\"updatedUser\":\"\",\"confContent\":\"\",\"version\":0,\"confDesc\":\"360问卷权限开关\",\"companyId\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\",\"createdTime\":1729582103000,\"open\":0,\"createdUser\":\"\"},{\"updatedTime\":1729582103000,\"confCode\":\"auto_score_add_weight_auth\",\"updatedUser\":\"\",\"confContent\":\"\",\"version\":0,\"confDesc\":\"加和-自动计算指标得分乘以指标权重\",\"companyId\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\",\"createdTime\":1729582103000,\"open\":1,\"createdUser\":\"\"},{\"updatedTime\":1729582103000,\"confCode\":\"auto_skip_score\",\"updatedUser\":\"\",\"confContent\":\"\",\"version\":0,\"confDesc\":\"自动跳过评分是否开启\",\"companyId\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\",\"createdTime\":1729582103000,\"open\":0,\"createdUser\":\"\"},{\"updatedTime\":1729582103000,\"confCode\":\"business_plan_item_auth\",\"updatedUser\":\"\",\"confContent\":\"\",\"version\":0,\"confDesc\":\"经营计划指标权限是否开启\",\"companyId\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\",\"createdTime\":1729582103000,\"open\":0,\"createdUser\":\"\"},{\"updatedTime\":1729236990000,\"confCode\":\"childAdminSeeAnonymous_20240110\",\"updatedUser\":\"\",\"confContent\":\"\",\"version\":0,\"confDesc\":\"子管理员可查看已匿名评分人信息\",\"companyId\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\",\"createdTime\":1688460513000,\"open\":1,\"createdUser\":\"\"},{\"updatedTime\":1729582103000,\"confCode\":\"ind_input_202308021\",\"updatedUser\":\"\",\"confContent\":\"\",\"version\":0,\"confDesc\":\"指标加减分录入是否开启\",\"companyId\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\",\"createdTime\":1729582103000,\"open\":0,\"createdUser\":\"\"},{\"updatedTime\":1729582103000,\"confCode\":\"ind_level_group_202306027\",\"updatedUser\":\"\",\"confContent\":\"\",\"version\":0,\"confDesc\":\"指标等级组是否开启\",\"companyId\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\",\"createdTime\":1729582103000,\"open\":1,\"createdUser\":\"\"},{\"updatedTime\":1729582103000,\"confCode\":\"item_auth\",\"updatedUser\":\"\",\"confContent\":\"\",\"version\":0,\"confDesc\":\"指标权限是否开启\",\"companyId\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\",\"createdTime\":1729582103000,\"open\":1,\"createdUser\":\"\"},{\"updatedTime\":1729582103000,\"confCode\":\"item_finish_value_auth\",\"updatedUser\":\"\",\"confContent\":\"\",\"version\":0,\"confDesc\":\"指标完成值审核是否开启\",\"companyId\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\",\"createdTime\":1729582103000,\"open\":1,\"createdUser\":\"\"},{\"updatedTime\":1729236990000,\"confCode\":\"mainAdminSeeAnonymous_20240202\",\"updatedUser\":\"\",\"confContent\":\"\",\"version\":0,\"confDesc\":\"主管理员可查看已匿名评分人信息\",\"companyId\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\",\"createdTime\":1713334106000,\"open\":1,\"createdUser\":\"\"},{\"updatedTime\":1687165898000,\"confCode\":\"org_emp_cnt_init_20230619\",\"updatedUser\":\"\",\"confContent\":\"{}\",\"version\":0,\"confDesc\":\"组织及人员构建打开,授权事件后全量构建后会自动打\",\"companyId\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\",\"createdTime\":1687165898000,\"open\":1,\"createdUser\":\"\"},{\"updatedTime\":1729582103000,\"confCode\":\"pip_auth\",\"updatedUser\":\"\",\"confContent\":\"\",\"version\":0,\"confDesc\":\"pip权限开关\",\"companyId\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\",\"createdTime\":1729582103000,\"open\":1,\"createdUser\":\"\"},{\"updatedTime\":1729236990000,\"confCode\":\"resultAuditorSeeAnonymous_20240202\",\"updatedUser\":\"\",\"confContent\":\"\",\"version\":0,\"confDesc\":\"绩效校准人可查看已匿名评分人信息\",\"companyId\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\",\"createdTime\":1688460513000,\"open\":1,\"createdUser\":\"\"},{\"updatedTime\":1729582103000,\"confCode\":\"result_rank_open_20231213\",\"updatedUser\":\"\",\"confContent\":\"\",\"version\":0,\"confDesc\":\" 强制等级分布开关\",\"companyId\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\",\"createdTime\":1729582103000,\"open\":0,\"createdUser\":\"\"},{\"updatedTime\":1729582103000,\"confCode\":\"temp_auth\",\"updatedUser\":\"\",\"confContent\":\"\",\"version\":0,\"confDesc\":\"模板权限是否开启\",\"companyId\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\",\"createdTime\":1729582103000,\"open\":1,\"createdUser\":\"\"},{\"updatedTime\":1682401440000,\"confCode\":\"type_cate_priv_202304025\",\"updatedUser\":\"\",\"confContent\":\"\",\"version\":0,\"confDesc\":\"模板查询权限是否开启\",\"companyId\":\"ece4e403-43aa-47f2-bb19-a0dd18b8e98d\",\"createdTime\":1682401440000,\"open\":0,\"createdUser\":\"\"}],\"itemAuth\":1,\"resultRankOpen20231213\":0,\"resultInputSendMsg\":\"true\"}";
        EvaluateTaskKpiQueryVO query = JSON.parseObject(json, EvaluateTaskKpiQueryVO.class);
        CompanyConf companyConf = JSON.parseObject(confjson, CompanyConf.class);
        List<ExportInputValue> pos = evalKpiDao.pagedFinishValue(query, companyConf);
        System.out.println(JSONUtil.toJsonStr(pos));
    }
}
