package com.polaris.kpi.eval.infr.task.builder;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.BaseAuditNode;
import com.polaris.kpi.eval.domain.task.entity.interview.ResultInterviewConfirmFlow;
import com.polaris.kpi.eval.infr.task.dao.AdminTaskDao;
import com.polaris.sdk.common.JsonFileTool;
import com.polaris.sdk.type.TenantId;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.dao.DomainDaoImpl;

import java.util.List;
import java.util.function.Supplier;

public class ResultInterviewConfirmDataBdTest {
    protected TestContext context;
    protected DomainDaoImpl domainDao ;

    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        domainDao = context.getDomainDao();
    }

    @Test
    public void buildFirstFlowTest() {
        //ResultInterviewConfirmFlow.json
        ResultInterviewConfirmFlow flow = JsonFileTool.toBean("com.polaris.kpi.eval.domain.task.dmsvc/ResultInterviewConfirmFlow.json", ResultInterviewConfirmFlow.class);

        Supplier<String> resultInterviewConfirmFlowIdGen = () -> domainDao.nextLongAsStr("result_interview_confirm_flow");
        Supplier<String> resultInterviewConfirmFlowNodeIdGen = () -> domainDao.nextLongAsStr("result_interview_confirm_flow_node");
        Supplier<String> resultInterviewConfirmIdGen = () -> domainDao.nextLongAsStr("perf_eval_task_interview_confirm");

        ResultInterviewConfirmDataBd bd = new ResultInterviewConfirmDataBd(flow, resultInterviewConfirmFlowIdGen, resultInterviewConfirmFlowNodeIdGen, resultInterviewConfirmIdGen);
        bd.buildFirstFlow();
        // flow.initConfirmFlowNodes(confirmNodes);
        System.out.println(JSONUtil.toJsonStr(bd));
    }
}
