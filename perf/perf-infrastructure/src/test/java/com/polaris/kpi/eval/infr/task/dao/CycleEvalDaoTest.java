package com.polaris.kpi.eval.infr.task.dao;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.infr.cycle.dao.CycleEvalDao;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.CycleAnalyseCntPo;
import org.junit.Before;
import org.junit.Test;


public class CycleEvalDaoTest {
    protected TestContext context;
    protected CycleEvalDao cycleEvalDao;

    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        cycleEvalDao = new CycleEvalDao();
        cycleEvalDao.setDomainDao(context.getDomainDao());
    }

    @Test
    public void getCycleAnalyseCnt() {
        CycleAnalyseCntPo cntPo = cycleEvalDao.getCycleAnalyseCnt("ece4e403-43aa-47f2-bb19-a0dd18b8e98d",
                "1159402",null,"","main");
        System.out.println(JSONUtil.toJsonStr(cntPo));
    }
}
