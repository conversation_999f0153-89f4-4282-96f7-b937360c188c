package com.polaris.kpi.eval.infr.task.dao;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.data.Pictures;
import com.deepoove.poi.data.TableRenderData;
import com.deepoove.poi.data.TextRenderData;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.ExportEvalTablePolicy;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.ExportData;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.ExportEvalPo;
import com.polaris.sdk.common.JsonFileTool;
import lombok.Getter;
import lombok.Setter;
import org.junit.Test;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;


public class EmpEvalWordExportTest {

    @Getter
    @Setter
    private static class KpiItem {
        private String kpiItemName = "销售净额（万元）";
        private String kpiItemWeight = "80%";
        private String itemRule = "考核标准：原则上，以销售额扣除直接外购成本之后金额，销售额是指签订的合同金额，一般签订正式的合同且合同资料齐全，在签订合同的当期确认。同时考虑到公司客户特殊性，在无合同有回款的情况下，按回款额确认销售额,在回款的当期确认。直接外购成本是指从外部购买设备、服务等发生的非公司自主性消耗支出";
        private String targetInfo = "目标值：16000元\n挑战值：18000元";
        private String finishInfo = "目标值：20000元\n" +
                "完成情况：完成情况完成情况完成情况完成情况完成情况完成情况完成情况完成情况完成情况完成情况完成情况";
        private String score = "35";
    }

    @Getter
    @Setter
    private static class RefEval {
        private String taskName = "2023年10月绩效考核 ";
        private String weight = "30";
        private boolean isMain;
        private String no;
        private String score = "88.88";

        public RefEval(Integer indx, String refEvalName, String weight, boolean isMain, String score) {
            this.taskName = refEvalName;
            this.weight = weight;
            this.isMain = isMain;
            this.score = score;
            if (isMain) {
                this.no = "当前";
            } else {
                this.no = "T" + indx;
            }
        }
    }

    @Getter
    @Setter
    private static class Type {
        private String kpiTypeName;
        private String kpiTypeWeight;
        private String des;
        private List<KpiItem> items = new ArrayList<KpiItem>() {{
            add(new KpiItem());
        }};

        public Type(String typeName, String typeWeight, String typeDesc) {
            this.kpiTypeName = typeName;
            this.kpiTypeWeight = typeWeight;
            this.des = typeDesc;
        }

        private TableRenderData itemsTable;
    }


    @Getter
    @Setter
    private static class Summary {
        private PictureRenderData raterImg;
        private String raterName = "2023年10月绩效考核";
        private String summary = "30";
        private String dateTime;


        public Summary(String raterImg, String raterName, String summary, Date date) {
            this.raterImg = Pictures.of(raterImg).size(30, 30).create();
            this.raterName = raterName;
            this.summary = summary;
            this.dateTime = DateUtil.formatDateTime(date);
        }
    }

    @Getter
    @Setter
    private static class TaskUser {
        private BigDecimal finalScore = new BigDecimal("100");//考核最终评分
        private String evaluationLevel = "高级";//考评等级
        private String perfCoefficient;//绩效系数
        private boolean hasRefEvals = true;
        private RefEval main = new RefEval(0, "2023年10月技术部2023年10月技术部2023年10月技术部2023年10月技术部", "30", true, "80.2");

        private List<RefEval> refEvals = new ArrayList<RefEval>() {{
            //add(new RefEval(0, "2023年10月绩效考核", "30", true, "80.0"));
            add(new RefEval(1, "2023年11月技术部绩效考核", "30", false, "80.2"));
            add(new RefEval(2, "2023年12月技术部绩效考核", "40", false, "80.3"));
        }};
        private List<Summary> summarys = new ArrayList<Summary>() {{
            add(new Summary("https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fsafe-img.xhscdn.com%2Fbw1%2F89e0676b-c0c3-4959-bbed-bc1cf0470250%3FimageView2%2F2%2Fw%2F1080%2Fformat%2Fjpg&refer=http%3A%2F%2Fsafe-img.xhscdn.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1708491994&t=df83f24fe6537d9f988dcc557ffb7c72", "张三", "整体表现优化，工作完成度还可以，但是有时严重拖延整体表现优化，工作完成度还可以，但是有时严重拖延整体表现优化，工作完成度还可以，但是\n" +
                    "时严重拖延整体表现优化，工作完成度还可以，但是有时严重拖延", new Date()));
        }};
        //private Summary summary = new Summary("https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fsafe-img.xhscdn.com%2Fbw1%2F89e0676b-c0c3-4959-bbed-bc1cf0470250%3FimageView2%2F2%2Fw%2F1080%2Fformat%2Fjpg&refer=http%3A%2F%2Fsafe-img.xhscdn.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1708491994&t=df83f24fe6537d9f988dcc557ffb7c72", "张三", "整体表现优化，工作完成度还可以，但是有时严重拖延整体表现优化，工作完成度还可以，但是有时严重拖延整体表现优化，工作完成度还可以，但是\n" +
        //        "时严重拖延整体表现优化，工作完成度还可以，但是有时严重拖延", new Date());
        private List<Type> types;
        private PictureRenderData avatar;
        private TextRenderData empName;
        private String orgName = "技术部";
        private String post = "技术总监";
        private String cycleStart = "2023.01.01";
        private String cycleEnd = "2023.12.31";
        private PictureRenderData raterImg = Pictures.of("https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fsafe-img.xhscdn.com%2Fbw1%2F89e0676b-c0c3-4959-bbed-bc1cf0470250%3FimageView2%2F2%2Fw%2F1080%2Fformat%2Fjpg&refer=http%3A%2F%2Fsafe-img.xhscdn.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1708491994&t=df83f24fe6537d9f988dcc557ffb7c72").size(26, 26).create();

        public TaskUser(PictureRenderData avatar, TextRenderData name, String orgName, String post) {
            this.avatar = avatar;
            this.empName = name;
            this.orgName = orgName;
            this.post = post;
        }

    }

    //多个同一页导出
    @Test
    public void forEach() throws IOException {

        ExportData exportData = JsonFileTool.toBean("com/polaris/kpi/eval/infr/task/dao/exportEval/forEach.json", ExportData.class);
        Configure config = Configure.builder()
                .bind("items", new ExportEvalTablePolicy())
                .build();
        XWPFTemplate template = XWPFTemplate.compile("/Users/<USER>/work/project/perf-server/all-in-one/src/main/resources/templates/模板-考核表each.docx", config)
                .render(exportData);
        template.writeToFile("/Users/<USER>/work/project/perf-server/tree/forEach.docx");
    }

    @Test
    public void parseTypes() throws IOException {
        ExportEvalPo evalPo = JsonFileTool.toBean("com/polaris/kpi/eval/infr/task/dao/exportEval/exportEval2.json", ExportEvalPo.class);
        Configure config = Configure.builder()
                .bind("typeWrap", new ExportEvalTablePolicy())
                //.bind("labors", policy)
                .build();
        if (StrUtil.isNotBlank(evalPo.getSignImg())) {
            evalPo.setSignPic(Pictures.of(evalPo.getSignImg()).size(120, 50).create());
        }
        List<URL> resources = ClassUtil.getResources(".");
        // 可换成 web版本的response的outputStream
        String writeToFile = resources.get(0).getPath() + "/exportEvalZip.docx";
        String temp = resources.get(0).getPath().replaceAll("perf/perf-infrastructure/target/test-classes/", "all-in-one/src/main/resources/templates/模板-无评分详情.docx");
        XWPFTemplate template = XWPFTemplate.compile(temp, config)
                .render(evalPo);
        template.writeToFile(writeToFile);
    }

    @Test
    public void map() {
        HashMap<String, String> map = new HashMap<>();
        map.put("自评", "20");
        map.put("上主", "20");
        System.out.println(JSON.toJSONString(map));
    }
}