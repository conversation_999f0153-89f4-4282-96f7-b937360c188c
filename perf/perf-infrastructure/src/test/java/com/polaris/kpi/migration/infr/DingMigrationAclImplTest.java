package com.polaris.kpi.migration.infr;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.core.io.IoUtil;
import cn.hutool.json.JSONUtil;
import com.perf.www.common.utils.oss.OssUtil;
import com.polaris.kpi.migration.acl.DingMigrationAclImpl;
import com.polaris.kpi.migration.acl.IDingMigrationAcl;
import com.polaris.sdk.type.TenantId;
import org.apache.commons.io.FilenameUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.dao.AutoDaoImpl;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * DingMigrationAclImpl 单元测试
 */
public class DingMigrationAclImplTest {
    
    protected TestContext context;
    protected DomainDaoImpl domainDao;
    
    @Spy
    @InjectMocks
    private DingMigrationAclImpl dingMigrationAcl;
    
    private OssUtil ossUtil;
    
    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        domainDao = context.getDomainDao();
        
        MockitoAnnotations.openMocks(this);
        dingMigrationAcl.setDomainDao(domainDao);
        ossUtil = new OssUtil("LTAI5t3iEAY1ZVk3","vo3B2CF7wdRrRYfLYPsKA3LhEBBTnj","https://oss-cn-beijing.aliyuncs.com/","zxb-online","http://zxb-online.oss-cn-beijing.aliyuncs.com/default.jpg",
                "https:/zxb-online.oss-cn-beijing.aliyuncs.com/");
    }


    @Test
    public void testUpTest() throws FileNotFoundException {
        String filePath = "F:/work/数据迁移/dingDataMigrationForOem/111/ding29238e29f344df30.zip";
        File file = new File(filePath);
        //file转流
        InputStream fileStream = new FileInputStream(file);
        String timestamp = String.valueOf(System.currentTimeMillis());
        String baseFileName = FilenameUtils.getBaseName(file.getName());
        String ossDirectory = String.format("migration/%s/%s", "ding29238e29f344df30", timestamp);
        String ossFilePath = String.format("%s/%s_%s.zip", ossDirectory,baseFileName, timestamp);
        boolean uploadSuccess = ossUtil.uploadFile(fileStream, ossFilePath);
        System.out.println("上传成功: " + uploadSuccess);
        System.out.println("ossFilePath: " + ossFilePath);//migration/ding8b3d719c932b311dacaaa37764f94726/1756372054589/migration_data_1756372054589.zip
        System.out.println("all ossFilePath: " +  OssUtil.getImageOssHost() + ossFilePath);//migration/ding8b3d719c932b311dacaaa37764f94726/1756372054589/migration_data_1756372054589.zip
    }

    @Test
    public void testGetFileTest() throws Exception {
        //all ossFilePath: https://zxb-online.oss-cn-beijing.aliyuncs.com/migration/ding29238e29f344df30/1756375212689/ding29238e29f344df30_1756375212689.zip
        //all ossFilePath: https://zxb-online.oss-cn-beijing.aliyuncs.com/migration/ding29238e29f344df30/1756373705226/migration_data_1756373705226.zip
        //fileName: migration/ding29238e29f344df30/1756374383858/ding29238e29f344df30_1756374383858.zip
        //https://zxb-online.oss-cn-beijing.aliyuncs.com/migration/export/1211641/1211641_1756389484717.zip
        //https://zxb-online.oss-cn-beijing.aliyuncs.com/migration/export/1027158/1027158_1756432730213.zip
        InputStream stream = ossUtil.getFile("migration/export/1027158/1027158_1756432730213.zip");
        System.out.println("InputStream: " + stream);

        if (stream != null) {
            try {
                // 包装为BufferedInputStream以支持mark/reset
                BufferedInputStream bufferedStream = new BufferedInputStream(stream);

                // 1. 检查文件类型（通过文件头判断）
                bufferedStream.mark(8); // 标记位置
                byte[] header = new byte[4];
                int bytesRead = bufferedStream.read(header);

                if (bytesRead == 4) {
                    // 检查是否是ZIP文件
                    if (header[0] == 0x50 && header[1] == 0x4B) { // "PK"
                        System.out.println("检测到ZIP文件");

                        // 重置到开始位置
                        bufferedStream.reset();
                        processZipFile(bufferedStream);
                    } else {
                        // 重置到开始位置
                        bufferedStream.reset();
                        String content = IoUtil.readUtf8(bufferedStream);
                        System.out.println("文本文件内容: " + content);
                    }
                }
            } finally {
                stream.close();
            }
        }
    }

    private void processZipFile(InputStream zipStream) throws IOException {
        try (ZipInputStream zipIn = new ZipInputStream(zipStream)) {
            ZipEntry entry;
            while ((entry = zipIn.getNextEntry()) != null) {
                System.out.println("ZIP文件条目: " + entry.getName());
                System.out.println("文件大小: " + entry.getSize() + " bytes");

                if (entry.getName().endsWith(".sql")) {
                    // 读取SQL文件内容
                    String sqlContent = IoUtil.readUtf8(zipIn);
                    System.out.println("SQL文件内容: " + sqlContent);
                }
            }
            zipIn.closeEntry();
        }
    }
    @Test
    public void testReadFilesFromOss() {
        // 准备测试数据
        String dingCorpId = "test_corp_123";
        String filePrefix = "migration." + dingCorpId;
        
        // 模拟OSS返回的文件列表
        List<String> mockFiles = new ArrayList<>();
        mockFiles.add("migration.test_corp_123.1234567890123.file1.sql");
        mockFiles.add("migration.test_corp_123.1234567890123.file2.sql");
        mockFiles.add("migration.test_corp_123.1234567890123.migration_data_1234567890123.zip");
        
        // Mock OSS工具类
    //    when(ossUtil.listKeys(filePrefix)).thenReturn(mockFiles);
        
        // 执行测试
        List<String> result = dingMigrationAcl.readFilesFromOss(dingCorpId);
        System.out.println("Files: " + result);
        // 验证结果
        Assert.assertNotNull("返回结果不应为空", result);
        Assert.assertEquals("文件数量应该匹配", 3, result.size());
        Assert.assertTrue("应该包含SQL文件", result.stream().anyMatch(f -> f.endsWith(".sql")));
        Assert.assertTrue("应该包含ZIP文件", result.stream().anyMatch(f -> f.endsWith(".zip")));
        
        // 验证方法调用
        verify(ossUtil, times(1)).listKeys(filePrefix);
    }
    
    @Test
    public void testReadFilesFromOss_EmptyResult() {
        // 准备测试数据
        String dingCorpId = "test_corp_456";
        String filePrefix = "migration." + dingCorpId;
        
        // Mock OSS返回空列表
      //  when(ossUtil.listKeys(filePrefix)).thenReturn(new ArrayList<>());
        
        // 执行测试
        List<String> result = dingMigrationAcl.readFilesFromOss(dingCorpId);
        
        // 验证结果
        Assert.assertNotNull("返回结果不应为空", result);
        Assert.assertTrue("结果应该是空列表", result.isEmpty());
        
        // 验证方法调用
        verify(ossUtil, times(1)).listKeys(filePrefix);
    }
    
    @Test
    public void testReadFilesFromOss_Exception() {
        // 准备测试数据
        String dingCorpId = "test_corp_789";
        String filePrefix = "migration." + dingCorpId;
        
        // Mock OSS抛出异常
        when(ossUtil.listKeys(filePrefix)).thenThrow(new RuntimeException("OSS连接失败"));
        
        // 执行测试
        List<String> result = dingMigrationAcl.readFilesFromOss(dingCorpId);
        
        // 验证结果
        Assert.assertNotNull("返回结果不应为空", result);
        Assert.assertTrue("异常情况下应该返回空列表", result.isEmpty());
        
        // 验证方法调用
        verify(ossUtil, times(1)).listKeys(filePrefix);
    }
    
    @Test
    public void testImportFromOssToDatabase() {
        // 准备测试数据
        String dingCorpId = "test_corp_import";
        String ossPath = "migration/test_corp_import/1234567890123";
        
        // Mock OSS返回文件列表
        List<String> mockFiles = new ArrayList<>();
        mockFiles.add("migration.test_corp_import.1234567890123.migration_data_1234567890123.zip");
        
        // Mock OSS工具类
      //  when(ossUtil.listKeys(anyString())).thenReturn(mockFiles);
        
        // 模拟ZIP文件内容
        String mockZipContent = "PK..."; // 模拟ZIP文件头
        InputStream mockInputStream = new ByteArrayInputStream(mockZipContent.getBytes());
      //  when(ossUtil.getFile(anyString())).thenReturn(mockInputStream);
        
        // 执行测试
        boolean result = dingMigrationAcl.importFromOssToDatabase(dingCorpId, ossPath);
        
        // 验证结果
        Assert.assertTrue("导入应该成功", result);
        
        // 验证方法调用
        verify(ossUtil, times(1)).listKeys(anyString());
    }
    
    @Test
    public void testImportFromOssToDatabase_NoFiles() {
        // 准备测试数据
        String dingCorpId = "test_corp_no_files";
        String ossPath = "migration/test_corp_no_files/1234567890123";
        
        // Mock OSS返回空列表
      //  when(ossUtil.listKeys(anyString())).thenReturn(new ArrayList<>());
        
        // 执行测试
        boolean result = dingMigrationAcl.importFromOssToDatabase(dingCorpId, ossPath);
        
        // 验证结果
        Assert.assertFalse("没有文件时导入应该失败", result);
        
        // 验证方法调用
    //    verify(ossUtil, times(1)).listKeys(anyString());
    }
    
    @Test
    public void testImportFromOssToDatabase_Exception() {
        // 准备测试数据
        String dingCorpId = "test_corp_exception";
        String ossPath = "migration/test_corp_exception/1234567890123";
        
        // Mock OSS抛出异常
        when(ossUtil.listKeys(anyString())).thenThrow(new RuntimeException("OSS连接失败"));
        
        // 执行测试
        boolean result = dingMigrationAcl.importFromOssToDatabase(dingCorpId, ossPath);
        
        // 验证结果
        Assert.assertFalse("异常情况下导入应该失败", result);
        
        // 验证方法调用
        verify(ossUtil, times(1)).listKeys(anyString());
    }
    
    @Test
    public void testImportFromOssToDatabase_WithSqlFiles() {
        // 准备测试数据
        String dingCorpId = "test_corp_sql";
        String ossPath = "migration/test_corp_sql/1234567890123";
        
        // Mock OSS返回SQL文件列表
        List<String> mockFiles = new ArrayList<>();
        mockFiles.add("migration.test_corp_sql.1234567890123.company.sql");
        mockFiles.add("migration.test_corp_sql.1234567890123.employee.sql");
        
        // Mock OSS工具类
       // when(ossUtil.listKeys(anyString())).thenReturn(mockFiles);
        
        // 模拟SQL文件内容
        String mockSqlContent = "INSERT INTO company (id, name) VALUES ('1', 'Test Company');";
        InputStream mockInputStream = new ByteArrayInputStream(mockSqlContent.getBytes());
      //  when(ossUtil.getFile(anyString())).thenReturn(mockInputStream);
        
        // 执行测试
        boolean result = dingMigrationAcl.importFromOssToDatabase(dingCorpId, ossPath);
        
        // 验证结果
        Assert.assertTrue("SQL文件导入应该成功", result);
        
        // 验证方法调用
     //   verify(ossUtil, times(1)).listKeys(anyString());
     //   verify(ossUtil, times(2)).getFile(anyString());
    }
    @Test
    public void importFromOssToDatabaseTest() {
        // 准备测试数据
        String dingCorpId = "ding666666";
        // 执行测试
        boolean result = dingMigrationAcl.importFromOssToDatabaseTest(dingCorpId);
        // 验证结果
        Assert.assertTrue("SQL文件导入应该成功", result);

        // 验证方法调用
     //   verify(ossUtil, times(1)).listKeys(anyString());
     //   verify(ossUtil, times(2)).getFile(anyString());
    }

    @Test
    public void testUploadFromOssToDatabase_WithSqlFiles2() {
        // 准备测试数据
        String dingCorpId = "test_corp_sql";
        String ossPath = "migration/test_corp_sql/1234567890123";

        // Mock OSS返回SQL文件列表
        List<String> mockFiles = new ArrayList<>();
        mockFiles.add("migration.test_corp_sql.1234567890123.company.sql");
        mockFiles.add("migration.test_corp_sql.1234567890123.employee.sql");

        // Mock OSS工具类
        // when(ossUtil.listKeys(anyString())).thenReturn(mockFiles);

        // 模拟SQL文件内容
        String mockSqlContent = "INSERT INTO company (id, name) VALUES ('1', 'Test Company');";
        InputStream mockInputStream = new ByteArrayInputStream(mockSqlContent.getBytes());
        //  when(ossUtil.getFile(anyString())).thenReturn(mockInputStream);
        // 1. 生成统一的OSS目录结构
        String timestamp = String.valueOf(System.currentTimeMillis());
        String ossDirectory = String.format("migration/%s/%s", dingCorpId, timestamp);

        // 2. 生成文件名（保持原始文件名，便于识别）
        String originalFileName = "EXPORT_SQL.zip";
        String fileExtension = FilenameUtils.getExtension(originalFileName);

        // 3. 构建OSS文件路径
        String ossFilePath;
        if ("zip".equalsIgnoreCase(fileExtension)) {
            // ZIP文件直接放在目录下
            ossFilePath = String.format("%s/migration_data_%s.zip", ossDirectory, timestamp);
        } else {
            // 其他文件按原文件名保存
            String baseFileName = FilenameUtils.getBaseName(originalFileName);
            ossFilePath = String.format("%s/%s_%s.%s", ossDirectory, baseFileName, timestamp, fileExtension);
        }

        System.out.println("上传迁移文件OSS请求: dingCorpId=" + dingCorpId + ", originalFileName=" + originalFileName + ", ossFilePath=" + ossFilePath);

        // 4. 上传文件到OSS
        boolean uploadSuccess = ossUtil.uploadFile(mockInputStream, ossFilePath);
        System.out.println("上传迁移文件OSS请求: uploadSuccess=" + uploadSuccess);

        // 验证方法调用
        //   verify(ossUtil, times(1)).listKeys(anyString());
        //   verify(ossUtil, times(2)).getFile(anyString());
    }
} 