package com.polaris.kpi.eval.infr.pip;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.pip.plan.entity.PipChain;
import com.polaris.kpi.eval.domain.pip.plan.entity.PipChainNode;
import com.polaris.kpi.eval.infr.pip.plan.dao.PipChainDao;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

public class PipChainDaoTest {
    protected TestContext context;
    protected Pip<PERSON>hainDao pipChainDao;


    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        pipChainDao = new PipChainDao();
        pipChainDao.setDomainDao(context.getDomainDao());
    }

    @Test
    public void listNeedSetMutualNode() {
        List<PipChainNode> chainNodes = pipChainDao.listNeedSetMutualNode("5a031297-1b38-48ae-bc82-375849835203", "1000105");
        System.out.println(JSONUtil.toJsonStr(chainNodes));
    }

    @Test
    public void getPipEvalChainBase() {
        PipChain chain = pipChainDao.getPipEvalChainBase("5a031297-1b38-48ae-bc82-375849835203", "1000105");
        System.out.println(JSONUtil.toJsonStr(chain));
    }
    @Test
    public void listPipEvalChainBase() {
        List<PipChain> chains = pipChainDao.listPipEvalChainBase("5a031297-1b38-48ae-bc82-375849835203", Arrays.asList("1000105","1000106"));
        System.out.println(JSONUtil.toJsonStr(chains));
    }
}