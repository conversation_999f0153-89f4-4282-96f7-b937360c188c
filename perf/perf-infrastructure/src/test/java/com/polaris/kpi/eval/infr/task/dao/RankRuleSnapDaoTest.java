package com.polaris.kpi.eval.infr.task.dao;

import cn.com.polaris.kpi.temp.CycleTypeEnum;
import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.calibrated.RankRuleScoreRangeSnap;
import com.polaris.kpi.eval.domain.task.entity.calibrated.ResultRankInstance;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskUserDo;
import com.polaris.kpi.eval.infr.task.ppojo.calibrated.CycleScoreRuleOptPo;
import com.polaris.kpi.org.infr.emp.pojo.EmpOrganizationDo;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.builder.BatchUpdateBuilder;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.dao.AutoDaoImpl;
import org.lufei.ibatis.mapper.PagedList;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


public class RankRuleSnapDaoTest {

    protected TestContext context;
    protected AutoDaoImpl domainDao;
    protected RankRuleSnapDao rankRuleSnapDao = new RankRuleSnapDao();

    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        domainDao = context.getDomainDao();
        rankRuleSnapDao.setDomainDao(context.getDomainDao());
    }

    @Test
    public void listMemberOfRuleSnap() {
        PagedList<String> empNames = rankRuleSnapDao.listMemberNameOfRuleSnap("5a031297-1b38-48ae-bc82-375849835203", "1000001");
        if (empNames.getTotalRow() < 8) {
            Assert.assertTrue(empNames.size() == empNames.getTotalRow());
        } else {
            Assert.assertTrue(empNames.size() == 8 && empNames.getTotalRow() >= 8);
            System.out.println(JSONUtil.toJsonStr(empNames));
        }
    }

    @Test
    public void listRuleSnapOfCycle() {
    }


    @Test
    public void batchUpdateResult() {
        ArrayList<EvalUser> members = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            EvalUser evalUser1 = new EvalUser();
            evalUser1.setCompanyId(new TenantId("5a031297-1b38-48ae-bc82-375849835203"));
            evalUser1.setId("01884890-b7e6-4fc3-bbe5-77949641699" + i);
            evalUser1.setOriginalEvaluationLevel("A" + i);
            evalUser1.setEvaluationLevel("A" + i);
            evalUser1.setStepId("10000" + i);
            evalUser1.setOriginalPerfCoefficient("1.0" + i);
            evalUser1.setPerfCoefficient("1.0" + i);
            members.add(evalUser1);
        }
        rankRuleSnapDao.batchUpdateResult(members, new TenantId("5a031297-1b38-48ae-bc82-375849835203"));
        PerfEvaluateTaskUserDo byId = domainDao.findById(PerfEvaluateTaskUserDo.class, "01884890-b7e6-4fc3-bbe5-779496416990");
        Assert.assertEquals("A0", byId.getEvaluationLevel());
        System.out.println(byId.getEvaluationLevel());

        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .whereEq("originalEvaluationLevel", "A1");
        Assert.assertTrue(domainDao.listAll(comQB).isEmpty());

//        context.commit();
    }

    @Test
    public void listScoreRulOnCycle() {
        List<CycleScoreRuleOptPo> ruleOptPos = rankRuleSnapDao.listScoreRuleOnCycle("5a031297-1b38-48ae-bc82-375849835203", "1835401");
        Assert.assertTrue(ruleOptPos.size() == 1);
        String realRs = JSONUtil.toJsonStr(ruleOptPos);
        boolean equals = realRs.equals("[{\"cycleId\":\"1835401\",\"name\":\"全公司使用\",\"ruleId\":\"2051404\",\"snapId\":\"1000001\",\"isSnap\":true}]");
        Assert.assertTrue(equals);
        System.out.println(realRs);
    }


    @Test
    public void findResultRankInstance() {
        ResultRankInstance instance = rankRuleSnapDao.findResultRankInstance(new TenantId("5a031297-1b38-48ae-bc82-375849835203"), "1000001");
        System.out.println(JSONUtil.toJsonStr(instance));
    }

    @Test
    public void getResultRankInstance() {
        ResultRankInstance instance = rankRuleSnapDao.getResultRankInstance(new TenantId("5a031297-1b38-48ae-bc82-375849835203"), "1000001");
        System.out.println(JSONUtil.toJsonStr(instance));
        Assert.assertTrue(JSONUtil.toJsonStr(instance).equals("{\"updatedTime\":1703666587000,\"fieldJson\":\"[{\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"考核任务得分\\\",\\\"companyFieldId\\\":\\\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"考核任务得分\\\",\\\"value\\\":0,\\\"yVal\\\":true},{\\\"label\\\":\\\"×\\\",\\\"value\\\":\\\"*\\\",\\\"symbol\\\":true},{\\\"label\\\":0,\\\"value\\\":0},{\\\"label\\\":\\\".\\\",\\\"value\\\":\\\".\\\"},{\\\"label\\\":0,\\\"value\\\":0},{\\\"label\\\":1,\\\"value\\\":1}]\",\"rankScope\":{\"type\":1,\"scope\":-1},\"cycleId\":\"1835401\",\"scoreRanges\":[{\"updatedTime\":1703666587000,\"max\":200000,\"fieldJson\":\"[{\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"考核任务得分\\\",\\\"companyFieldId\\\":\\\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"考核任务得分\\\",\\\"value\\\":0,\\\"yVal\\\":true},{\\\"label\\\":\\\"×\\\",\\\"value\\\":\\\"*\\\",\\\"symbol\\\":true},{\\\"label\\\":0,\\\"value\\\":0},{\\\"label\\\":\\\".\\\",\\\"value\\\":\\\".\\\"},{\\\"label\\\":0,\\\"value\\\":0},{\\\"label\\\":1,\\\"value\\\":1}]\",\"minAppendEqual\":0,\"stepId\":\"2004001\",\"rangeFrom\":0,\"version\":0,\"coeffType\":1,\"rangeTo\":0,\"companyId\":{\"id\":\"5a031297-1b38-48ae-bc82-375849835203\"},\"scoreRuleId\":\"2051404\",\"min\":90,\"isDeleted\":\"false\",\"rate\":-1,\"createdTime\":1703666587000,\"perfCoefficient\":\"1\",\"place\":4,\"snapId\":\"1000001\",\"createdUser\":\"12627917-53e2-4604-9665-5ad204778882\"},{\"updatedTime\":1703666587000,\"max\":90,\"fieldJson\":\"[{\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"考核任务得分\\\",\\\"companyFieldId\\\":\\\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"考核任务得分\\\",\\\"value\\\":0,\\\"yVal\\\":true},{\\\"label\\\":\\\"×\\\",\\\"value\\\":\\\"*\\\",\\\"symbol\\\":true},{\\\"label\\\":0,\\\"value\\\":0},{\\\"label\\\":\\\".\\\",\\\"value\\\":\\\".\\\"},{\\\"label\\\":0,\\\"value\\\":0},{\\\"label\\\":1,\\\"value\\\":1}]\",\"minAppendEqual\":0,\"stepId\":\"1000005\",\"rangeFrom\":0,\"version\":0,\"coeffType\":1,\"rangeTo\":0,\"companyId\":{\"id\":\"5a031297-1b38-48ae-bc82-375849835203\"},\"scoreRuleId\":\"2051404\",\"min\":70,\"isDeleted\":\"false\",\"rate\":-1,\"createdTime\":1703666587000,\"perfCoefficient\":\"0.8\",\"place\":4,\"snapId\":\"1000001\",\"createdUser\":\"12627917-53e2-4604-9665-5ad204778882\"},{\"updatedTime\":1703666587000,\"max\":70,\"fieldJson\":\"[{\\\"formulaFieldValue\\\":0,\\\"formulaFieldName\\\":\\\"考核任务得分\\\",\\\"companyFieldId\\\":\\\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\\\",\\\"isSystemField\\\":\\\"false\\\",\\\"label\\\":\\\"考核任务得分\\\",\\\"value\\\":0,\\\"yVal\\\":true},{\\\"label\\\":\\\"×\\\",\\\"value\\\":\\\"*\\\",\\\"symbol\\\":true},{\\\"label\\\":0,\\\"value\\\":0},{\\\"label\\\":\\\".\\\",\\\"value\\\":\\\".\\\"},{\\\"label\\\":0,\\\"value\\\":0},{\\\"label\\\":1,\\\"value\\\":1}]\",\"minAppendEqual\":0,\"stepId\":\"1000006\",\"rangeFrom\":0,\"version\":0,\"coeffType\":1,\"rangeTo\":0,\"companyId\":{\"id\":\"5a031297-1b38-48ae-bc82-375849835203\"},\"scoreRuleId\":\"2051404\",\"min\":0,\"isDeleted\":\"false\",\"rate\":-1,\"createdTime\":1703666587000,\"perfCoefficient\":\"0.6\",\"place\":4,\"snapId\":\"1000001\",\"createdUser\":\"12627917-53e2-4604-9665-5ad204778882\"}],\"updatedUser\":\"\",\"version\":0,\"excludeEmps\":[],\"levelDefType\":1,\"coeffType\":1,\"companyId\":{\"id\":\"5a031297-1b38-48ae-bc82-375849835203\"},\"system\":0,\"isDeleted\":\"false\",\"name\":\"全公司使用\",\"createdTime\":1703666587000,\"id\":\"1000001\",\"place\":4,\"perfCoefficient\":\"考核任务得分*0.01\",\"onCycleType\":63,\"ruleId\":\"2051404\",\"createdUser\":\"12627917-53e2-4604-9665-5ad204778882\"}\n"));
//        {"updatedTime":1703666587000,"fieldJson":"[{\"formulaFieldValue\":0,\"formulaFieldName\":\"考核任务得分\",\"companyFieldId\":\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\",\"isSystemField\":\"false\",\"label\":\"考核任务得分\",\"value\":0,\"yVal\":true},{\"label\":\"×\",\"value\":\"*\",\"symbol\":true},{\"label\":0,\"value\":0},{\"label\":\".\",\"value\":\".\"},{\"label\":0,\"value\":0},{\"label\":1,\"value\":1}]","rankScope":{"type":1,"scope":-1},"cycleId":"1835401","scoreRanges":[{"updatedTime":1703666587000,"max":200000,"fieldJson":"[{\"formulaFieldValue\":0,\"formulaFieldName\":\"考核任务得分\",\"companyFieldId\":\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\",\"isSystemField\":\"false\",\"label\":\"考核任务得分\",\"value\":0,\"yVal\":true},{\"label\":\"×\",\"value\":\"*\",\"symbol\":true},{\"label\":0,\"value\":0},{\"label\":\".\",\"value\":\".\"},{\"label\":0,\"value\":0},{\"label\":1,\"value\":1}]","minAppendEqual":0,"stepId":"2004001","rangeFrom":0,"version":0,"coeffType":1,"rangeTo":0,"companyId":{"id":"5a031297-1b38-48ae-bc82-375849835203"},"scoreRuleId":"2051404","min":90,"isDeleted":"false","rate":-1,"createdTime":1703666587000,"perfCoefficient":"1","place":4,"snapId":"1000001","createdUser":"12627917-53e2-4604-9665-5ad204778882"},{"updatedTime":1703666587000,"max":90,"fieldJson":"[{\"formulaFieldValue\":0,\"formulaFieldName\":\"考核任务得分\",\"companyFieldId\":\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\",\"isSystemField\":\"false\",\"label\":\"考核任务得分\",\"value\":0,\"yVal\":true},{\"label\":\"×\",\"value\":\"*\",\"symbol\":true},{\"label\":0,\"value\":0},{\"label\":\".\",\"value\":\".\"},{\"label\":0,\"value\":0},{\"label\":1,\"value\":1}]","minAppendEqual":0,"stepId":"1000005","rangeFrom":0,"version":0,"coeffType":1,"rangeTo":0,"companyId":{"id":"5a031297-1b38-48ae-bc82-375849835203"},"scoreRuleId":"2051404","min":70,"isDeleted":"false","rate":-1,"createdTime":1703666587000,"perfCoefficient":"0.8","place":4,"snapId":"1000001","createdUser":"12627917-53e2-4604-9665-5ad204778882"},{"updatedTime":1703666587000,"max":70,"fieldJson":"[{\"formulaFieldValue\":0,\"formulaFieldName\":\"考核任务得分\",\"companyFieldId\":\"0e7b3dc8-3ca8-4d06-9207-e94b93bd05e0\",\"isSystemField\":\"false\",\"label\":\"考核任务得分\",\"value\":0,\"yVal\":true},{\"label\":\"×\",\"value\":\"*\",\"symbol\":true},{\"label\":0,\"value\":0},{\"label\":\".\",\"value\":\".\"},{\"label\":0,\"value\":0},{\"label\":1,\"value\":1}]","minAppendEqual":0,"stepId":"1000006","rangeFrom":0,"version":0,"coeffType":1,"rangeTo":0,"companyId":{"id":"5a031297-1b38-48ae-bc82-375849835203"},"scoreRuleId":"2051404","min":0,"isDeleted":"false","rate":-1,"createdTime":1703666587000,"perfCoefficient":"0.6","place":4,"snapId":"1000001","createdUser":"12627917-53e2-4604-9665-5ad204778882"}],"updatedUser":"","version":0,"excludeEmps":[],"levelDefType":1,"coeffType":1,"companyId":{"id":"5a031297-1b38-48ae-bc82-375849835203"},"system":0,"isDeleted":"false","name":"全公司使用","createdTime":1703666587000,"id":"1000001","place":4,"perfCoefficient":"考核任务得分*0.01","onCycleType":63,"ruleId":"2051404","createdUser":"12627917-53e2-4604-9665-5ad204778882"}
    }


    @Test
    public void getResultRankInstance2() {
        ResultRankInstance instance = rankRuleSnapDao.getResultRankInstance(new TenantId("5a031297-1b38-48ae-bc82-375849835203"), "1012403");
        System.out.println(JSONUtil.toJsonStr(instance));
    }


    @Test
    public void listSysScoreRule() {
        System.out.println(CycleTypeEnum.MONTH.getCode());
        List<CycleScoreRuleOptPo> ruleOptPos = rankRuleSnapDao.listSysScoreRule("5a031297-1b38-48ae-bc82-375849835203", CycleTypeEnum.MONTH);
        Assert.assertTrue(CollUtil.isNotEmpty(ruleOptPos));
    }

    @Test
    public void batchUpdate() {
        EmpOrganizationDo eo = new EmpOrganizationDo();
        eo.setOrgId("orgId_1000");
        eo.setDirectEmpCnt(10);
        eo.setChildEmpCnt(20);
        BatchUpdateBuilder up = BatchUpdateBuilder.buildTable("emp_organization")
                .addSetCaseProp("directEmpCnt", "orgId:=")
                .addSetCaseProp("childEmpCnt", "orgId:=")
                .addBean(eo)
                .whereEq("company_id", "1000001001001")
                .whereUseIn("orgId");

        up.buildSql();
        System.out.println(up.getSql());
        System.out.println(up.getParams());
    }

    @Test
    public void listSnapTask() {
        PagedList<String> taskNames = rankRuleSnapDao.listSnapTask("5a031297-1b38-48ae-bc82-375849835203", "1000001");
        for (String taskName : taskNames) {
            Assert.assertEquals("1201-公示初始化数据-不公示", taskName);
            System.out.println(taskName);
        }
    }

    @Test
    public void listResultRankInstance() {
        List<ResultRankInstance> instances = rankRuleSnapDao.listResultRankInstance(new TenantId("5a031297-1b38-48ae-bc82-375849835203"), Arrays.asList("1000001"));
        Assert.assertTrue(instances.isEmpty());

        instances = rankRuleSnapDao.listResultRankInstance(new TenantId("5a031297-1b38-48ae-bc82-375849835203"), Arrays.asList("1167503"));
        System.out.println(JSONUtil.toJsonStr(instances));
        Assert.assertTrue(!instances.isEmpty());
        Assert.assertTrue(instances.size() == 4);
        for (ResultRankInstance instance : instances) {
            Assert.assertTrue(CollUtil.isNotEmpty(instance.getScoreRanges()));
        }
    }

    @Test
    public void listSnapScoreRangeGroup() {
        ListWrap<RankRuleScoreRangeSnap> snapListWrap = rankRuleSnapDao.listSnapScoreRangeGroup(new TenantId("5a031297-1b38-48ae-bc82-375849835203"), Arrays.asList("1167503"));
        System.out.println(snapListWrap.getGroups().size() == 4);
    }

    @Test
    public void fixClearSnap() {
        rankRuleSnapDao.fixClearSnap(new TenantId("5a031297-1b38-48ae-bc82-375849835203"), "1168201");
        context.commit();
    }
}