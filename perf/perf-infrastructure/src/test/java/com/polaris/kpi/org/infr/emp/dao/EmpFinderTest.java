package com.polaris.kpi.org.infr.emp.dao;

import cn.com.polaris.kpi.KpiEmp;
import com.polaris.kpi.eval.infr.task.BaseDaoTest;
import com.polaris.kpi.org.domain.dept.repo.EmpFinder;
import com.polaris.sdk.type.TenantId;
import org.junit.BeforeClass;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;

/**
 * EmpFinder接口实现的单元测试
 * 
 * 测试KpiEmpDao类中实现的EmpFinder接口的listByEmp方法
 * 该方法根据员工ID列表获取员工信息
 * 
 * <AUTHOR>
 */
public class EmpFinderTest extends BaseDaoTest {

    private static KpiEmpDao kpiEmpDao;

    @BeforeClass
    public static void beforeClass() throws Exception {
        BaseDaoTest.beforeClass("com/polaris/kpi/org/infr/emp/dao/EmpFinderTest/EmpFinderTestData.sql");
        kpiEmpDao = new KpiEmpDao();
    }

    @Before
    public void setUp() throws Exception {
        super.setDao(kpiEmpDao, "domainDao");
    }

    /**
     * 测试场景：传入空的员工ID列表
     * 
     * 预期结果：返回空列表，不抛出异常
     */
    @Test
    public void testListByEmpWithEmptyEmpIds() {
        TenantId tenantId = new TenantId(companyId);
        List<KpiEmp> result = kpiEmpDao.listByEmp(tenantId, Collections.emptyList());

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试场景：传入不存在的员工ID
     * 
     * 预期结果：返回空列表，不抛出异常
     */
    @Test
    public void testListByEmpWithEmptyEmpIds2() {
        TenantId tenantId = new TenantId(companyId);
        List<String> empIds = Arrays.asList("1090038xx");
        List<KpiEmp> result = kpiEmpDao.listByEmp(tenantId, empIds);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试场景：传入单个有效的员工ID
     * 
     * 预期结果：返回包含该员工信息的列表，列表大小为1
     */
    @Test
    public void testListByEmpWithSingleEmpId() {
        TenantId tenantId = new TenantId(companyId);
        List<String> empIds = Arrays.asList("1090038");

        List<KpiEmp> result = kpiEmpDao.listByEmp(tenantId, empIds);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("1090038", result.get(0).getEmpId());
    }

    /**
     * 测试场景：传入多个有效的员工ID
     * 
     * 预期结果：返回包含所有员工信息的列表，列表大小等于传入的员工ID数量
     */
    @Test
    public void testListByEmpWithMultipleEmpIds() {
        TenantId tenantId = new TenantId(companyId);
        List<String> empIds = Arrays.asList("1090069", "1090071", "1093001");

        List<KpiEmp> result = kpiEmpDao.listByEmp(tenantId, empIds);

        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.stream().anyMatch(e -> "1090069".equals(e.getEmpId())));
        assertTrue(result.stream().anyMatch(e -> "1090071".equals(e.getEmpId())));
        assertTrue(result.stream().anyMatch(e -> "1093001".equals(e.getEmpId())));
    }
}
