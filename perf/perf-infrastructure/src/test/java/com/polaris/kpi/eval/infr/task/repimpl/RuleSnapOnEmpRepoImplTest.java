package com.polaris.kpi.eval.infr.task.repimpl;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.org.domain.dept.type.TaskId;
import com.polaris.sdk.type.TenantId;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.dao.AutoDaoImpl;

import java.util.List;

public class RuleSnapOnEmpRepoImplTest {

    protected TestContext context;
    protected AutoDaoImpl domainDao;
    protected RuleSnapOnEmpRepoImpl rankRuleSnapDao = new RuleSnapOnEmpRepoImpl();

    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        domainDao = context.getDomainDao();
        rankRuleSnapDao.setDomainDao(context.getDomainDao());
    }

    @Test
    public void listSnapMember() {
        List<EvalUser> list = rankRuleSnapDao.listSnapMember(new TenantId("5a031297-1b38-48ae-bc82-375849835203"), "1000001");
        for (EvalUser evalUser : list) {
            System.out.println(JSONUtil.toJsonStr(evalUser));
        }

    }

    @Test
    public void getBlongSnapId() {
        String blongSnapId = rankRuleSnapDao.getBelongSnapId(new TenantId("5a031297-1b38-48ae-bc82-375849835203"), "2013505");
        Assert.assertTrue("1000001".equals(blongSnapId));
    }

    @Test
    public void listSnapMemberOnTaskId() {
        List<EvalUser> list = rankRuleSnapDao.listSnapMemberOnTaskId(new TenantId("5a031297-1b38-48ae-bc82-375849835203"), "2013505", new TaskId("xxx"));
    }

    @Test
    public void listSnapMemberOnLevelOrg() {
        List<EvalUser> list = rankRuleSnapDao.listSnapMemberOnLevelOrg(new TenantId("5a031297-1b38-48ae-bc82-375849835203"), "2013505", new TaskId("xxx"), 2, "org|org2");
    }

    @Test
    public void listSnapMemberOnAtOrg() {
        List<EvalUser> list = rankRuleSnapDao.listSnapMemberOnAtOrg(new TenantId("5a031297-1b38-48ae-bc82-375849835203"), "2013505", new TaskId("xxx"), "org|org2");
    }
}