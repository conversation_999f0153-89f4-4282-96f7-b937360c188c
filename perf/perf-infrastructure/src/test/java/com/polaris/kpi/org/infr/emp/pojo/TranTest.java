package com.polaris.kpi.org.infr.emp.pojo;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.polaris.sdk.type.ListWrap;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.junit.Test;

import java.io.File;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

public class TranTest {
    @Test
    public void matchExtUserId() {
        String charset = "utf-8";
        List<String> wecoms = FileUtil.readLines(" /Users/<USER>/work/2024项目计划/迁移/wcom-dept.txt", charset);
        List<String> dings = FileUtil.readLines(" /Users/<USER>/work/2024项目计划/迁移/ding-dept.txt", charset);


        List<TDept> dingDepts = dings.stream().map(wecom -> {
//            38661034	571370461	董事会	江苏汇名天然气集团有限公司
            List<String> strings = StrUtil.splitTrim(wecom, "\t");
//            System.out.println(strings);
            String extDeptId = strings.get(1);
            String parentName = strings.size() == 4 ? strings.get(3) : null;
            String orgName = strings.get(2);
            TDept dingDept = new TDept(orgName, parentName, extDeptId);
            dingDept.setOrgId(strings.get(0));
//            System.out.println(dingDept);
            return dingDept;
        }).collect(Collectors.toList());
        ListWrap<TDept> dingDeptMap = new ListWrap<>(dingDepts).asMap(tDept -> tDept.mapKey());

        List<TDept> wecomDepts = wecoms.stream().map(wecom -> {
            List<String> strings = StrUtil.splitTrim(wecom, "\t");
//            System.out.println(strings);
            TDept wecomDept = strings.size() == 2 ? new TDept(strings.get(0), null, strings.get(1)) : new TDept(strings.get(0), strings.get(1), strings.get(2));
//            System.out.println(wecomDept);
            TDept dDept = dingDeptMap.mapGet(wecomDept.mapKey());
//            System.out.println( "wecom部门:"+wecomDept);
//            System.out.println("match.ding:" + dDept);
            wecomDept.setOrgId(dDept != null ? dDept.getOrgId() : null);

            return wecomDept;
        }).collect(Collectors.toList());
        wecomDepts.sort(Comparator.comparing(TDept::sortValue));
        for (TDept wecomDept : wecomDepts) {
            if (StrUtil.equals("1", wecomDept.getExtDeptId())) {
                wecomDept.setOrgId("38661020");
            }
            if (StrUtil.isBlank(wecomDept.getOrgId())) {
                continue;
            }
            System.out.println(wecomDept.toSqlString("1137209"));
        }

    }

    @Getter
    @Setter
    @NoArgsConstructor
    public static class TDept {
        private String orgName;
        private String parentName;
        private String extDeptId;
        private String orgId;

        public TDept(String orgName, String parentName, String extDeptId) {
            this.orgName = orgName;
            this.parentName = parentName;
            this.extDeptId = extDeptId;
        }

        public String mapKey() {
            return orgName + "_" + parentName;
        }

        @Override
        public String toString() {
            return "WecomDept{" +
                    "orgName='" + orgName + '\'' +
                    ", parentName='" + parentName + '\'' +
                    ", extDeptId='" + extDeptId + '\'' +
                    ", orgId='" + orgId + '\'' +
                    '}';
        }

        public String toSqlString(String companyId) {
            if (orgId == null) {
                return null;
            }
            return String.format("update emp_organization  set org_id='%s' where company_id='%s' and ding_org_id='%s';", orgId, companyId, extDeptId);
        }

        public Integer sortValue() {
            if (StrUtil.equals("extDeptId", extDeptId)) {
                return -1;
            }
            return StrUtil.isBlank(extDeptId) ? -1 : Integer.valueOf(extDeptId);
        }
    }

    @Test
    public void name() {
        String charset = "utf-8";
        String path = "/Users/<USER>/Downloads/2025-01-15-13-57-16_EXPORT_SQL_17382756_385/1";
        List<String> names = FileUtil.listFileNames(path);
        File file = new File(path + "/merge1.sql");
        for (String name : names) {
            if(name.equals("2025-01-15-13-0.sql")){
                continue;
            }
            List<String> sqls = FileUtil.readLines(path + "/" + name, charset);
            FileUtil.appendLines(sqls, file, charset);
        }
        System.out.println(file.length());
//        List<String> dings = FileUtil.readLines(" /Users/<USER>/work/2024项目计划/迁移/ding-dept.txt", charset);

    }

    @Test
    public void name2() {
        String charset = "utf-8";
        String path = "/Users/<USER>/code/kpi-sql/kpi-sql/work/线上/2025/迁移/1/2025-01-15-13-57-16_EXPORT_SQL_17382756_879_6.sql";
//        List<String> names = FileUtil.listFileNames(path);
        File file = new File(path + "/merge1.sql");
        List<String> sqls = FileUtil.readLines(path, charset);
        int start = 1022566;
        for (int i = 0; i < sqls.size(); i++) {
            start++;
            String sql = sqls.get(i).replace("1022566",start+"");
            System.out.println(sql);
        }
//        FileUtil.appendLines(sqls, file, charset);

    }
}
