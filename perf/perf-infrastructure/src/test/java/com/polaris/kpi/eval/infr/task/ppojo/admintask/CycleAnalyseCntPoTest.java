package com.polaris.kpi.eval.infr.task.ppojo.admintask;

import cn.hutool.json.JSONUtil;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;
import java.util.Objects;

import static org.junit.Assert.*;

public class CycleAnalyseCntPoTest {

    @Test
    public void builderGradeCnt() {
        List<CycleGradeCntPo> cntPos =JSONUtil.toList("[{\"empcnt\":53,\"gradeName\":\"A\",\"proportion\":9.91,\"stepId\":\"13286703\"},{\"empcnt\":1,\"gradeName\":\"A\",\"proportion\":0.19,\"stepId\":\"2852890\"},{\"empcnt\":105,\"gradeName\":\"B\",\"proportion\":19.63,\"stepId\":\"13286704\"},{\"empcnt\":254,\"gradeName\":\"C\",\"proportion\":47.48,\"stepId\":\"13286705\"},{\"empcnt\":65,\"gradeName\":\"D\",\"proportion\":12.15,\"stepId\":\"13286706\"},{\"empcnt\":20,\"gradeName\":\"E\",\"proportion\":3.74,\"stepId\":\"13286707\"},{\"empcnt\":1,\"gradeName\":\"B\",\"proportion\":0.19,\"stepId\":\"2852348\"}]",CycleGradeCntPo.class);
        CycleAnalyseCntPo cycleAnalyseCntPo = new CycleAnalyseCntPo();
        cycleAnalyseCntPo.builderGradeCnt(cntPos);
        Assert.assertTrue(Objects.equals(JSONUtil.toJsonStr(cycleAnalyseCntPo.getGradeCntPos()),"[{\"gradeName\":\"A\",\"proportion\":10.1,\"empcnt\":54},{\"gradeName\":\"B\",\"proportion\":19.82,\"empcnt\":106},{\"gradeName\":\"C\",\"proportion\":47.48,\"empcnt\":254},{\"gradeName\":\"D\",\"proportion\":12.15,\"empcnt\":65},{\"gradeName\":\"E\",\"proportion\":3.74,\"empcnt\":20}]"));
    }
}