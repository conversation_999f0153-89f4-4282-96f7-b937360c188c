package com.polaris.kpi.eval.infr.task.repimpl;

import cn.com.seendio.polaris.code.TestContext;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.dao.DomainDaoImpl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/5 16:01
 */
public class TaskKpiRepoImplTest {
    protected TestContext context;
    protected TaskKpiRepoImpl taskKpiRepo;

    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        taskKpiRepo = new TaskKpiRepoImpl();
        DomainDaoImpl domainDao = context.getDomainDao();
        taskKpiRepo.setDomainDao(domainDao);
    }

}
