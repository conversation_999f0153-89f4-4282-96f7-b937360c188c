package com.polaris.kpi.eval.infr.task.dao;

import ch.vorburger.exec.ManagedProcessException;
import ch.vorburger.mariadb4j.DB;
import ch.vorburger.mariadb4j.DBConfigurationBuilder;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskKpiPo;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.EvalResultPo;
import com.polaris.kpi.eval.infr.task.query.EvalUserQuery;
import com.polaris.test.base.TestContext;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.lufei.ibatis.mapper.PagedList;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

public class TaskUserDaoTest {

    protected static TestContext context;
    protected static TaskUserDao userDao = new TaskUserDao();
    protected String companyId = "ece4e403-43aa-47f2-bb19-a0dd18b8e98d";
    private static int setUpCnt = 0;
    private static DB db;

    @BeforeClass
    public static void beforeClass() throws Exception {
        setUpCnt++;
        System.out.println("setUpCnt:" + setUpCnt);
        DBConfigurationBuilder configBuilder = DBConfigurationBuilder.newBuilder();
        configBuilder.setPort(3388);
        configBuilder.setDataDir("../tree/db/TaskUserDaoTest");
        db = DB.newEmbeddedDB(configBuilder.build());
        db.start();
        db.createDB("acs", "acs", "acs");
        db.source("com/polaris/kpi/base/schemal.sql", "acs", "acs", "acs");
        db.source("com/polaris/kpi/eval/infr/task/dao/TaskUserDaoTest/data.sql", "acs", "acs", "acs");
        context = new TestContext("com/polaris/kpi/eval/infr/task/dao/WorkTodoDaoTest/workTodoDao.properties");
        context.confDomainDaoFor(userDao, "autoBaseDao");
    }

    @AfterClass
    public static void clean() throws ManagedProcessException {
        db.run("drop database acs");
        db.stop();
    }

    @Test
    @DisplayName("查询未完成完成值录入的考核")
    public void listNoFinishValueEval() {
        List<PerfEvaluateTaskKpiPo> evalResultPos = userDao.listNoFinishValueEval("5a031297-1b38-48ae-bc82-375849835203", Arrays.asList("1341501"));
        System.out.println(JSONUtil.toJsonStr(evalResultPos));
    }
    @Test
    @DisplayName("单元的测试")
    public void pageEvaluateTaskUser() {
        EvalUserQuery query = new EvalUserQuery();
        query.setCompanyId("1070324");
        query.setTaskId("1981463");
        PagedList<EvalResultPo> evalResultPos = userDao.pagedEvalResult(query);
        Assert.assertTrue(evalResultPos.size() == 1);
        Assert.assertEquals("finalAppointScore",evalResultPos.get(0).getFinalAppointScore(),new BigDecimal("5.100"));
        System.out.println(evalResultPos);
    }
}