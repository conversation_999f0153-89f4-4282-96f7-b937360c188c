package com.polaris.kpi.eval.infr.task.dao;

import cn.com.polaris.kpi.company.TenantSysConf;
import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.infr.task.query.report.TaskReportQry;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

public class EvaluateTaskDaoTest {
    protected TestContext context;
    protected EvaluateTaskDao evaluateTaskDao;

    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        evaluateTaskDao = new EvaluateTaskDao();
        evaluateTaskDao.setDomainDao(context.getDomainDao());
    }

    @Test
    public void pagedTaskDetail() {
//        TenantSysConf conf,
//        //TaskReportQry query
//        ListWrap<EvalScoreResult> resultListWrap = evaluateTaskDao
//                .pagedTaskDetail(new TenantId("5a031297-1b38-48ae-bc82-375849835203"), "1280040");
//
//        for (EvalScoreResult result:resultListWrap.getDatas()){
//            if (StrUtil.isNotBlank(result.getTransferId())) {
//                Assert.assertTrue(result.getScorerId().equals("1602582"));
//            }
//        }
//        System.out.println(JSONUtil.toJsonStr(resultListWrap.getDatas()));
//        System.out.println(JSONUtil.toJsonStr(resultListWrap));
    }

    @Test
    public void listScoreStageBeforeScoreResults() {
        ListWrap<EvalScoreResult> resultListWrap = evaluateTaskDao
                .listScoreStageBeforeScoreResults(new TenantId("5a031297-1b38-48ae-bc82-375849835203"), "1280040");
        for (EvalScoreResult result:resultListWrap.getDatas()){
            if (StrUtil.isNotBlank(result.getTransferId())) {
                Assert.assertTrue(result.getScorerId().equals("1602582"));
            }
        }
        System.out.println(JSONUtil.toJsonStr(resultListWrap.getDatas()));
        System.out.println(JSONUtil.toJsonStr(resultListWrap));
    }
}
