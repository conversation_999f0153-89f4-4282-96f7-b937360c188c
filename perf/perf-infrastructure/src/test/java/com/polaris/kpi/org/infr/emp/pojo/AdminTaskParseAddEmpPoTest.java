package com.polaris.kpi.org.infr.emp.pojo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.polaris.acl.kpi.eval.domain.EvalEmp;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.AdminTaskParseAddEmpPo;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;

public class AdminTaskParseAddEmpPoTest {
    @Test
    public void buildRepeatEmps() {
        String json1="[{\"empId\":\"1093014\",\"atOrgName\":\"行政一部\",\"avatar\":\"https://static-legacy.dingtalk.com/media/lADPM5HikoKm7UnNAWPNAUQ_324_355.jpg\",\"atOrgId\":\"1015004\",\"empName\":\"思威\"}]";
        String json2="[{\"empId\":\"1093014\",\"empOrgName\":\"融云北极星的合作伙伴\",\"refAsk360Flag\":false,\"version\":0,\"orgId\":\"1013211\",\"hasAskEndScore\":false,\"resetRaterNameIds\":[],\"isAllAutoType\":true,\"isDeleted\":\"false\",\"empName\":\"思威\",\"scoreEndFlag\":false,\"isNewEmp\":0,\"sendMsg\":true,\"taskStatus\":\"drawUpIng\",\"infos\":[]}]";
        List<EvalEmp> allEmps = JSONUtil.parseArray(json1).toList(EvalEmp.class);
        List<EvalUser> existed = JSONUtil.parseArray(json2).toList(EvalUser.class);
        AdminTaskParseAddEmpPo po = new AdminTaskParseAddEmpPo();
        po.buildRepeatEmps(allEmps,existed);
        Assert.assertTrue(CollUtil.isNotEmpty(po.getRepeatEmps()));
        Assert.assertTrue(po.getRepeatEmps().size() == 1);
        Assert.assertTrue(po.getRepeatEmps().get(0).getEmpId().equals("1093014"));
        Assert.assertTrue(po.getRepeatEmps().get(0).getOrgs().size() ==2);
        System.out.println(JSONUtil.toJsonStr(po));
    }

}
