package com.polaris.kpi.eval.infr.cycle.dao;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.infr.cycle.ppojo.CycleConfPo;
import com.polaris.kpi.eval.infr.cycle.ppojo.CyclePo;
import com.polaris.kpi.eval.infr.cycle.query.CycleQuery;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.lufei.ibatis.mapper.PagedList;

/**
 * <AUTHOR>
 * @date 2024/10/18 9:51
 */
public class CycleEvalDaoTest {
    protected TestContext context;
    private CycleEvalDao cycleEvalDao;
    protected final static TenantId companyId = new TenantId("5a031297-1b38-48ae-bc82-375849835203") ;
    protected final static String createdUser = "1390043" ;


    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        DomainDaoImpl domainDao = context.getDomainDao();
        cycleEvalDao = new CycleEvalDao();
        cycleEvalDao.setDomainDao(domainDao);
    }

    @Test
    public void pagedNormalCycle(){
        CycleQuery cycleQuery = new CycleQuery();
        cycleQuery.setOpAdminType("main");
        cycleQuery.setCompanyId(companyId.getId());
        cycleQuery.setPermission(1);
        cycleQuery.setOpEmpId(createdUser);
        cycleQuery.setPageNo(1);
        cycleQuery.setPageSize(10);
        PagedList<CyclePo> cyclePos = cycleEvalDao.pagedNormalCycle(cycleQuery);
        System.out.println(JSONUtil.toJsonStr(cyclePos));
    }

    @Test
    public void pagedAllTerminatedCycle(){
        CycleQuery cycleQuery = new CycleQuery();
        cycleQuery.setOpAdminType("main");
        cycleQuery.setCompanyId(companyId.getId());
        cycleQuery.setPageNo(1);
        cycleQuery.setPageSize(20);
        cycleQuery.setCycleStart("2024-10-01");
        cycleQuery.setCycleEnd("2024-10-31");
        PagedList<CyclePo> cyclePos = cycleEvalDao.pagedAllTerminatedCycle(cycleQuery);
        System.out.println(JSONUtil.toJsonStr(cyclePos));
    }

    @Test
    public void pagedTerminatedCycle(){
        CycleQuery cycleQuery = new CycleQuery();
        cycleQuery.setOpAdminType("child");
        cycleQuery.setOpEmpId("1009069");
        cycleQuery.setCompanyId(companyId.getId());
        cycleQuery.setPageNo(1);
        cycleQuery.setPageSize(20);
        cycleQuery.setCycleStart("2024-10-01");
        cycleQuery.setCycleEnd("2024-10-31");
        PagedList<CyclePo> cyclePos = cycleEvalDao.pagedTerminatedCycle(cycleQuery);
        System.out.println(JSONUtil.toJsonStr(cyclePos));
    }

    @Test
    public void findCycleConf(){
        CycleConfPo cycleConf = cycleEvalDao.findCycleConf(companyId.getId(), "25");
        System.out.println(JSONUtil.toJsonStr(cycleConf.getArchiveStatus()));
    }
}
