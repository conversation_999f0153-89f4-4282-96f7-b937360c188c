package com.polaris.kpi.eval.infr.pip;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.pip.plan.entity.PipEval;
import com.polaris.kpi.eval.infr.pip.msg.dao.PipEvalMsgDao;
import com.polaris.kpi.eval.infr.pip.plan.dao.*;
import com.polaris.kpi.eval.infr.pip.plan.ppojo.AdminPipEvalPo;
import com.polaris.kpi.eval.infr.pip.plan.ppojo.PipEvalDetailPo;
import com.polaris.kpi.eval.infr.pip.plan.query.AdminPipEvalQuery;
import com.polaris.sdk.type.ListWrap;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.mapper.PagedList;

import java.util.Arrays;
import java.util.List;

public class PipEvalDaoTest {
    protected TestContext context;
    protected PipEvalDao pipEvalDao;


    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        pipEvalDao = new PipEvalDao();
        pipEvalDao.setDomainDao(context.getDomainDao());

        PipPlanDao pipPlanDao = new PipPlanDao();
        pipPlanDao.setDomainDao(context.getDomainDao());
        pipEvalDao.setPipPlanDao(pipPlanDao);

        PipChainDao pipChainDao = new PipChainDao();
        pipChainDao.setDomainDao(context.getDomainDao());
        pipEvalDao.setPipChainDao(pipChainDao);

        PipEvalItemDao pipEvalItemDao = new PipEvalItemDao();
        pipEvalItemDao.setDomainDao(context.getDomainDao());
        pipEvalDao.setPipEvalItemDao(pipEvalItemDao);

        PipEvalRuleDao pipEvalRuleDao = new PipEvalRuleDao();
        pipEvalRuleDao.setDomainDao(context.getDomainDao());
        pipEvalDao.setPipEvalRuleDao(pipEvalRuleDao);

        PipPlanTypeDao pipPlanTypeDao = new PipPlanTypeDao();
        pipPlanTypeDao.setDomainDao(context.getDomainDao());
        pipEvalDao.setPipPlanTypeDao(pipPlanTypeDao);

        PipEvalMsgDao pipEvalMsgDao = new PipEvalMsgDao();
        pipEvalMsgDao.setDomainDao(context.getDomainDao());
        pipEvalDao.setPipEvalMsgDao(pipEvalMsgDao);
    }

    @Test
    public void pagedPipEval() {
        AdminPipEvalQuery query = new AdminPipEvalQuery();
        query.setCompanyId("5a031297-1b38-48ae-bc82-375849835203");
        query.setPageNo(1);
        query.setPageSize(10);
        query.setPipStatusList(Arrays.asList("scoring"));
        PagedList<AdminPipEvalPo> pos = pipEvalDao.pagedPipEval(query);
        System.out.println(JSONUtil.toJsonStr(pos));
    }

    @Test
    public void listAutoEnterScorePipEval() {
        List<String> list = pipEvalDao.listAutoEnterScorePipEval("5a031297-1b38-48ae-bc82-375849835203");
        System.out.println(JSONUtil.toJsonStr(list));
    }

    @Test
    public void getPipEvalDetail() {
        PipEvalDetailPo detailPo = pipEvalDao.getPipEvalDetail("5a031297-1b38-48ae-bc82-375849835203", "1602589", "1000105");
        System.out.println(JSONUtil.toJsonStr(detailPo));
    }

    @Test
    public void listExcutedPipEval() {
        List<PipEval> evals = pipEvalDao.listExcutedPipEval("5a031297-1b38-48ae-bc82-375849835203", Arrays.asList("1000105", "1000106"));
        System.out.println(JSONUtil.toJsonStr(evals));
    }

    @Test
    public void getPipEvalBase() {
        PipEval pipEval = pipEvalDao.getPipEvalBase("5a031297-1b38-48ae-bc82-375849835203", "1000105");
        System.out.println(JSONUtil.toJsonStr(pipEval));
    }


    @Test
    public void listPipEvalBase() {
        List<PipEval> pipEvals = pipEvalDao.listPipEvalBase("5a031297-1b38-48ae-bc82-375849835203", Arrays.asList("1000105", "1000106"));
        System.out.println(JSONUtil.toJsonStr(pipEvals));
    }

    @Test
    public void listWrapPipEvalBase() {
        ListWrap<PipEval> pipEvals = pipEvalDao.listWrapPipEvalBase("5a031297-1b38-48ae-bc82-375849835203", Arrays.asList("1000502", "1000105"));
        System.out.println(JSONUtil.toJsonStr(pipEvals));
    }
}