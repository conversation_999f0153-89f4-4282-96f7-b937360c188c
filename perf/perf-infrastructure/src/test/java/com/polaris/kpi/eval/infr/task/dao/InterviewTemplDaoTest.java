package com.polaris.kpi.eval.infr.task.dao;

import cn.com.seendio.polaris.code.TestContext;
import com.polaris.sdk.type.TenantId;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.dao.DomainDaoImpl;

public class InterviewTemplDaoTest {
    protected TestContext context;
    protected InterviewTemplDao interviewTemplDao;
    private DomainDaoImpl domainDao;

    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        interviewTemplDao = new InterviewTemplDao();
        domainDao = context.getDomainDao();
        interviewTemplDao.setDomainDao(context.getDomainDao());
    }

    @Test
    public void initInterviewTempl() throws Exception {
        setUp();
        interviewTemplDao.initInterviewTempl(new TenantId("1001"));
    }
}