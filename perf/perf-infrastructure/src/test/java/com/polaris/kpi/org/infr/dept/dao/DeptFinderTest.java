package com.polaris.kpi.org.infr.dept.dao;

import cn.com.polaris.kpi.EmpStaff;
import cn.com.polaris.kpi.KpiDeptCode;
import com.polaris.kpi.eval.infr.task.BaseDaoTest;
import com.polaris.kpi.org.domain.dept.repo.DeptFinder;
import com.polaris.sdk.type.TenantId;
import org.junit.BeforeClass;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

import static org.junit.Assert.*;

/**
 * DeptFinder接口实现的单元测试
 *
 * 测试KpiOrgDao类中实现的DeptFinder接口的方法：
 * 1. getOrgCode - 获取组织代码
 * 2. listEmpByDeptId - 根据部门ID列出员工
 * 3. listEmpByDeptLevel - 根据部门级别列出员工
 *
 * <AUTHOR>
 */
public class DeptFinderTest extends BaseDaoTest {

    private static DeptFinder deptFinder;
    private static KpiOrgDao kpiOrgDao;

    @BeforeClass
    public static void beforeClass() throws Exception {
        BaseDaoTest.beforeClass("com/polaris/kpi/org/infr/dept/dao/DeptFinderTest/DeptFinderTestData.sql");
        kpiOrgDao = new KpiOrgDao();
        deptFinder = kpiOrgDao;
    }

    @Before
    public void setUp() throws Exception {
        super.setDao(kpiOrgDao, "dao");
    }

    /**
     * 测试场景：获取存在的组织代码
     *
     * 预期结果：返回正确的组织代码对象，包含orgId和orgCode
     */
    @Test
    public void testGetOrgCodeWithExistingOrg() {
        TenantId tenantId = new TenantId(companyId);
        String existingOrgId = "1026452"; // 人力资源部

        KpiDeptCode result = deptFinder.getOrgCode(tenantId, existingOrgId);

        assertNotNull(result);
        assertEquals(existingOrgId, result.getOrgId());
        assertNotNull(result.getOrgCode());
    }

    /**
     * 测试场景：获取不存在的组织代码
     *
     * 预期结果：返回null
     */
    @Test
    public void testGetOrgCodeWithNonExistingOrg() {
        TenantId tenantId = new TenantId(companyId);
        String nonExistingOrgId = "non_existing_org";

        KpiDeptCode result = deptFinder.getOrgCode(tenantId, nonExistingOrgId);

        assertNull(result);
    }

    /**
     * 测试场景：根据部门ID列出员工 - 存在的部门
     *
     * 预期结果：返回非空列表，包含该部门的员工
     */
    @Test
    public void testListEmpByDeptIdWithExistingDept() {
        TenantId tenantId = new TenantId(companyId);
        String existingOrgId = "1026452"; // 人力资源部
        String evalOrgId = "1026452";

        List<EmpStaff> result = deptFinder.listEmpByDeptId(tenantId, existingOrgId);

        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    /**
     * 测试场景：根据部门ID列出员工 - 不存在的部门
     *
     * 预期结果：返回空列表
     */
    @Test
    public void testListEmpByDeptIdWithNonExistingDept() {
        TenantId tenantId = new TenantId(companyId);
        String nonExistingOrgId = "non_existing_org";
        String evalOrgId = "1026452";

        List<EmpStaff> result = deptFinder.listEmpByDeptId(tenantId, nonExistingOrgId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试场景：根据部门级别列出员工 - 有效的部门级别
     *
     * 预期结果：返回非空列表，包含该级别的员工
     */
    @Test
    public void testListEmpByDeptLevelWithValidLevel() {
        TenantId tenantId = new TenantId(companyId);
        String fromOrgId = "1018304"; // A测试部门
        Integer validLevel = 1; // 一级部门

        List<EmpStaff> result = deptFinder.listEmpByDeptLevel(tenantId, fromOrgId, validLevel);

        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    /**
     * 测试场景：根据部门级别列出员工 - 无效的部门级别
     *
     * 预期结果：返回空列表
     */
    @Test
    public void testListEmpByDeptLevelWithInvalidLevel() {
        TenantId tenantId = new TenantId(companyId);
        String fromOrgId = "1018304"; // A测试部门
        Integer invalidLevel = 99; // 不存在的级别

        List<EmpStaff> result = deptFinder.listEmpByDeptLevel(tenantId, fromOrgId, invalidLevel);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试场景：根据部门级别列出员工 - 无效的部门级别
     *
     * 预期结果：返回空列表
     */
    @Test
    public void testListEmpByDeptLevelWithLevel() {
        TenantId tenantId = new TenantId(companyId);
        String fromOrgId = "1018304"; // A测试部门
        Integer level = 2; // 正常的级别

        List<EmpStaff> result = deptFinder.listEmpByDeptLevel(tenantId, fromOrgId, level);

        assertNotNull(result);
        assertNotNull(result.size()==1);
    }

    @Test
    public void testListEmpByDeptLevelWithLevel1() {
        TenantId tenantId = new TenantId(companyId);
        String fromOrgId = "1018304"; // A测试部门
        Integer level = 1; // 正常的级别

        List<EmpStaff> result = deptFinder.listEmpByDeptLevel(tenantId, fromOrgId, level);

        assertNotNull(result);
        assertTrue(result.size()==4);
    }
}
