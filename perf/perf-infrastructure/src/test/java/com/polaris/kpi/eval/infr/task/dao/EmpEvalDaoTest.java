package com.polaris.kpi.eval.infr.task.dao;

import cn.com.seendio.polaris.code.TestContext;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.polaris.acl.kpi.eval.domain.EvalEmp;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.EmpEvalByStatusPo;
import com.polaris.kpi.eval.infr.task.query.empeval.EmpEvalAtCycleQuery;
import com.polaris.kpi.eval.infr.task.query.empeval.EmpEvalAtTaskQuery2;
import com.polaris.kpi.eval.infr.task.query.empeval.ParseAddEmpEvalQuery;
import com.polaris.sdk.type.TenantId;
import org.junit.Before;
import org.junit.Test;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.lufei.ibatis.mapper.PagedList;

import java.util.ArrayList;
import java.util.List;

public class EmpEvalDaoTest {
    protected TestContext context;
    protected EmpEvalDao empEvalDao;
    private DomainDaoImpl domainDao;


    @Before
    public void setUp() throws Exception {
        context = new TestContext("tenantConf.xml");
        context.start();
        empEvalDao = new EmpEvalDao();
        domainDao = context.getDomainDao();
        empEvalDao.setDomainDao(context.getDomainDao());
    }

    @Test
    public void listExistAtCycle() {
        List<EvalEmp> emps = new ArrayList<>();
        emps.add(new EvalEmp("1299001", "苏小秋", "avtar", "orgId1234", "大部门"));
        EmpEvalAtCycleQuery query = new EmpEvalAtCycleQuery(new TenantId("ece4e403-43aa-47f2-bb19-a0dd18b8e98d"), "1617813", emps);
        List<EvalEmp> empss = empEvalDao.listExistAtCycle(query);
        System.out.println(JSONUtil.toJsonStr(empss));
    }

    @Test
    public void listExistEmpAtCycle() {
        List<EvalEmp> emps = new ArrayList<>();
        emps.add(new EvalEmp("1299001", "苏小秋", "avtar", "orgId1234", "大部门"));
        EmpEvalAtCycleQuery query = new EmpEvalAtCycleQuery(new TenantId("ece4e403-43aa-47f2-bb19-a0dd18b8e98d"), "1617813", emps);
        List<EvalUser> empss = empEvalDao.listExistEmpAtCycle(query);
        System.out.println(JSONUtil.toJsonStr(empss));
    }

    @Test
    public void listParseAddEmpEmps() {
        List<EvalEmp> emps = new ArrayList<>();
        emps.add(new EvalEmp("1299001", "苏小秋", "avtar", "orgId1234", "大部门"));
        ParseAddEmpEvalQuery query = new ParseAddEmpEvalQuery();
        query.setTenantId(new TenantId("ece4e403-43aa-47f2-bb19-a0dd18b8e98d"));
        query.setEmps(emps);
        List<EvalEmp> empss = empEvalDao.listParseAddEmpEmps(query);
        System.out.println(JSONUtil.toJsonStr(empss));
    }

    @Test
    public void pagedEmpEvalByStatus() {
        String json="{\"pageNo\":1,\"pageSize\":20,\"taskStatuss\":[],\"isMyCreated\":true,\"cycleId\":\"\",\"taskIds\":[\"1616709\"],\"empIds\":[],\"reviewerIds\":[],\"roleIds\":[],\"inputValueStatus\":[],\"performanceType\":1,\"empStatus\":\"\",\"onTheJobStatus\":\"\",\"entryDateStart\":\"2022-11-23\",\"entryDateEnd\":null,\"sortOrder\":\"\",\"sortFiled\":\"\",\"orgIds\":[],\"isOrgEval\":\"false\"}";
        EmpEvalAtTaskQuery2 query = JSON.parseObject(json,EmpEvalAtTaskQuery2.class);
        query.setCompanyId("ce4e403-43aa-47f2-bb19-a0dd18b8e98d");
        PagedList<EmpEvalByStatusPo> list = empEvalDao.pagedEmpEvalByStatus(query);
        System.out.println(JSONUtil.toJsonStr(list));
    }

    @Test
    public void listNotAuditResult() {
        TenantId companyId = new TenantId("ce4e403-43aa-47f2-bb19-a0dd18b8e98d");
        String empEvalId = "1270003";
        String scorerId = "1299001";
        List<String> scorerTypes = new ArrayList<>();
        scorerTypes.add("superior_score");
        List<EvalScoreResult> list = empEvalDao.listNotAuditResult(companyId, empEvalId, scorerId, scorerTypes);
        System.out.println(JSONUtil.toJsonStr(list));
    }
}