package com.polaris.kpi.cache.infr.repimpl;

import cn.hutool.core.util.StrUtil;
import com.polaris.kpi.cache.domain.entity.CompanyCacheInfo;
import com.polaris.kpi.cache.domain.repo.CompanyCacheRepo;
import com.polaris.kpi.eval.infr.task.ppojo.CompanyCacheInfoDo;
import org.apache.commons.lang3.StringUtils;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.common.data.ToDataBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.UUID;

@Component
public class CompanyCacheRepoImpl implements CompanyCacheRepo {

    @Autowired
    private DomainDaoImpl domainDao;

    @Override
    public void saveCompanyCacheInfo(CompanyCacheInfo model) {
        //先清除
        this.deleteCache(model.getCompanyId(),model.getLinkId(),model.getBusinessScene(),model.getCreatedUser());
        model.setIsDeleted("false");
        model.setId(UUID.randomUUID().toString());
        CompanyCacheInfoDo infoDo = new ToDataBuilder<>(model, CompanyCacheInfoDo.class).data();
        domainDao.save(infoDo);
    }

    @Override
    public void updateCompanyCacheInfo(CompanyCacheInfo model) {
        if (StringUtils.isEmpty(model.getId())){
            return;
        }
        UpdateBuilder updateBuilder = UpdateBuilder.build("company_cache_info").setBean(model).whereEq("id", model.getId());
        domainDao.update(updateBuilder);
    }

    @Override
    public void updateCompanyCacheValue(CompanyCacheInfo model) {
        if (StringUtils.isEmpty(model.getId())){
            return;
        }
        UpdateBuilder updateBuilder = UpdateBuilder.build("company_cache_info")
                .set("value",model.getValue())
                .set("updated_time",new Date())
                .whereEq("id", model.getId());
        domainDao.update(updateBuilder);
    }

    @Override
    public void deleteCache(String companyId, String linkId, String businessScene, String key, String updatedUser) {
        if (StringUtils.isAnyBlank(companyId, linkId, businessScene, key)){
            return;
        }
        UpdateBuilder updateBuilder = UpdateBuilder.build("company_cache_info").set("is_deleted", "true").set("updated_user", updatedUser).whereEq("company_id", companyId)
                .whereEq("link_id", linkId).whereEq("business_scene", businessScene).whereEq("is_deleted", "false");
        domainDao.update(updateBuilder);
    }

    @Override
    public void deleteCache(String companyId, String linkId, String businessScene, String updatedUser) {
        if (StringUtils.isAnyBlank(companyId, linkId, businessScene)){
            return;
        }
        UpdateBuilder updateBuilder = UpdateBuilder.build("company_cache_info").set("is_deleted", "true").set("updated_user", updatedUser).whereEq("company_id", companyId)
                .whereEq("link_id", linkId)
                .whereIn("business_scene", StrUtil.splitTrim(businessScene,","))
                .whereEq("is_deleted", "false");
        domainDao.update(updateBuilder);
    }

    @Override
    public void deleteCache(String companyId, String linkId, String updatedUser) {
        UpdateBuilder updateBuilder = UpdateBuilder.build("company_cache_info")
                .set("is_deleted", "true").
                        set("updated_user", updatedUser)
                .whereEq("company_id", companyId)
                .whereEq("link_id", linkId)
                .whereEq("is_deleted", "false");
        domainDao.update(updateBuilder);
    }

}
