package com.polaris.kpi.eval.infr.statics.ppojo;

import com.polaris.kpi.eval.domain.task.entity.Cycle;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/19 19:59
 */
@Data
public class ConsecutiveEmpBasePo {

    private String companyId;
    private String cycleId;
    private String cycleTime;
    private String taskId;
    private String taskName;
    private String taskUserId;
    private String empId;
    private String empName;
    private String avatar;
    private String orgId;
    private String orgName;
    private String atOrgCodePath;
    private String atOrgNamePath;
    private String finalScore;
    private String evaluationLevel;
    private String perfCoefficient;
    private String showResultType;


}
