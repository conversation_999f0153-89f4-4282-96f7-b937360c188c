package com.polaris.kpi.report.infr.pojo;


import com.polaris.kpi.eval.domain.task.entity.Cycle;
import com.polaris.kpi.eval.domain.task.entity.empeval.ComputeFinishValueProgress;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/23 11:41
 */
@Getter
@Setter
@NoArgsConstructor
public class OrgItemYearlyFinishValueCountPo {

    private String orgId;
    private String orgName;
    private Integer orgPathHeight;
    private Boolean isYearlySummary = false;
    private String cycleType;
    private Integer cycleYear;
//    private String itemId;
//    private String itemName;
    private List<OrgYearlyItemTargetAndFinishValue> orgYearlyItemTargetAndFinishValues;

    public void accCycleInfo(Cycle cycle) {
        this.cycleType = cycle.getType();
        this.cycleYear = cycle.getYear();
    }

    public void completeCycles(){
        List<Integer> existIndexes = orgYearlyItemTargetAndFinishValues.stream().map(OrgYearlyItemTargetAndFinishValue::getCycleValue).collect(Collectors.toList());
        List<Integer> notExistIndexes = new ArrayList<>();
        List<Integer> monthAllIndexes = Arrays.asList(1,2,3,4,5,6,7,8,9,10,11,12);
        List<Integer> quarterAllIndexes = Arrays.asList(1,2,3,4);
        List<Integer> halfYearAllIndexes = Arrays.asList(1,2);

        if ("month".equals(cycleType) || "cross_month".equals(cycleType)){
            notExistIndexes = monthAllIndexes.stream().filter(index -> !existIndexes.contains(index)).collect(Collectors.toList());
        }
        if ("quarter".equals(cycleType)){
            notExistIndexes = quarterAllIndexes.stream().filter(index -> !existIndexes.contains(index)).collect(Collectors.toList());
        }
        if ("half_year".equals(cycleType)){
            notExistIndexes = halfYearAllIndexes.stream().filter(index -> !existIndexes.contains(index)).collect(Collectors.toList());
        }
        for (Integer index : notExistIndexes) {
            OrgYearlyItemTargetAndFinishValue orgYearly = new OrgYearlyItemTargetAndFinishValue( index, cycleType, cycleYear.toString());
            orgYearlyItemTargetAndFinishValues.add(orgYearly);
        }

        //按照cycleValue升序排序
        orgYearlyItemTargetAndFinishValues.sort(Comparator.comparing(OrgYearlyItemTargetAndFinishValue::getCycleValue));
    }

    public void putInSummary(ComputeFinishValueProgress progress) {

        OrgYearlyItemTargetAndFinishValue summary = new OrgYearlyItemTargetAndFinishValue();
        summary.setCycleId("年度累计");
        summary.accProcess(progress);

        for (OrgYearlyItemTargetAndFinishValue orgItemTrend : orgYearlyItemTargetAndFinishValues) {
            summary.setTargetValue(summary.getTargetValue().add(orgItemTrend.getTargetValue()));
            summary.setFinishValue(summary.getFinishValue().add(orgItemTrend.getFinishValue()));
            summary.accItemInfo(orgItemTrend);
        }

        summary.computeFinishRate();
        summary.removeZero();
        orgYearlyItemTargetAndFinishValues.add(0,summary);
    }
}
