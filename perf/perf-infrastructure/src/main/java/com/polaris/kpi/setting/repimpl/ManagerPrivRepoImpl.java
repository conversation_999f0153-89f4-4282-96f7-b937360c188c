package com.polaris.kpi.setting.repimpl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.polaris.kpi.org.infr.emp.pojo.EmpRefOrgDo;
import com.polaris.kpi.setting.domain.entity.ManagerPriv;
import com.polaris.kpi.setting.domain.entity.ManagerPrivLog;
import com.polaris.kpi.setting.domain.repo.ManagerPrivRepo;
import com.polaris.kpi.setting.ppojo.ManagerPrivDo;
import com.polaris.kpi.setting.ppojo.ManagerPrivLogDo;
import com.polaris.kpi.setting.ppojo.ManagerPrivPo;
import lombok.extern.slf4j.Slf4j;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.common.data.ToDataBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class ManagerPrivRepoImpl implements ManagerPrivRepo {

    public static final String managerPrivSeq = "manager_priv";
    public static final String managerPrivLogSeq = "manager_priv_log";

    @Autowired
    private DomainDaoImpl domainDao;

    @Override
    public void saveManagerPriv(ManagerPriv priv) {
        if (Objects.isNull(priv)) {
            return;
        }
        ManagerPrivDo privDo = new ToDataBuilder<>(priv,ManagerPrivDo.class).data();
        privDo.setId(domainDao.nextLongAsStr(managerPrivSeq));
        domainDao.save(privDo);
    }

    @Override
    public void updateManagerPriv(ManagerPriv priv) {
        if (Objects.isNull(priv) || Objects.isNull(priv.getPrivConf())) {
            return;
        }
        UpdateBuilder up = UpdateBuilder.build(ManagerPrivDo.class)
                .set("priv_conf", JSONObject.toJSONString(priv.getPrivConf()))
                .whereEq("companyId", priv.getCompanyId())
                .whereEq("id", priv.getId());
        if (Objects.nonNull(priv.getManagerType())) {
            up.set("manager_type", priv.getManagerType());
        }
        domainDao.update(up);
    }

    @Override
    public void batchOperate(List<String> ids, Integer operateType) {
        if (CollUtil.isEmpty(ids) || Objects.isNull(operateType)) {
            return;
        }
        UpdateBuilder up = UpdateBuilder.build(ManagerPrivDo.class)
                .whereInReq("id", ids);
        if (operateType == 1 || operateType == 2) {
            up.set("status",operateType == 1 ? 1 : 0);
        }
        if (operateType == 3) {
            up.set("is_deleted",Boolean.TRUE.toString());
        }
        domainDao.update(up);
    }

    @Override
    public void addLog(ManagerPrivLog privLog) {
        if (Objects.isNull(privLog)) {
            return;
        }
        ManagerPrivLogDo logDo = new ManagerPrivLogDo();
        BeanUtil.copyProperties(privLog,logDo);
        logDo.setId(domainDao.nextLongAsStr(managerPrivLogSeq));
        domainDao.save(logDo);
    }

    @Override
    public void batchSaveLog(List<ManagerPrivLog> privLogList) {
        if (CollUtil.isEmpty(privLogList)) {
            return;
        }
        List<ManagerPrivLogDo> privLogDoList = new ArrayList<>();
        privLogList.forEach(l ->{
            ManagerPrivLogDo logDo = new ManagerPrivLogDo();
            BeanUtil.copyProperties(l,logDo);
            logDo.setId(domainDao.nextLongAsStr(managerPrivLogSeq));
            privLogDoList.add(logDo);
        });
        domainDao.saveBatch(privLogDoList);
    }

    @Override
    public void initManagerPriv(String companyId) {
        if (StrUtil.isBlank(companyId)) {
            return;
        }
        ComQB comQB = ComQB.build(EmpRefOrgDo.class)
                .whereEq("company_id", companyId)
                .whereEq("ref_type","manager").groupBy("emp_id");
        List<EmpRefOrgDo> orgDoList = domainDao.listAll(comQB);
        if (CollUtil.isEmpty(orgDoList)) {
            return;
        }
        List<ManagerPrivDo> privDoList = new ArrayList<>();
        for (EmpRefOrgDo empRefOrgDo : orgDoList) {
            ManagerPriv priv = new ManagerPriv();
            priv.init(companyId,empRefOrgDo.getEmpId());
            priv.setId(domainDao.nextLongAsStr(managerPrivSeq));
            privDoList.add(new ToDataBuilder<>(priv,ManagerPrivDo.class).data());
        }
        domainDao.saveBatch(privDoList);
    }

    @Override
    public void fullAddManagerPriv(String companyId) {
        if (StrUtil.isBlank(companyId)) {
            return;
        }
        ComQB comQB = ComQB.build(EmpRefOrgDo.class)
                .whereEq("company_id", companyId)
                .whereEq("ref_type","manager").groupBy("emp_id");
        List<EmpRefOrgDo> orgDoList = domainDao.listAll(comQB);
        if (CollUtil.isEmpty(orgDoList)) {
            return;
        }
        ComQB listQB = ComQB.build(ManagerPrivDo.class)
                .clearSelect().select("emp_id").setRsType(String.class)
                .whereEq("company_id", companyId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        List<String> list = domainDao.listAll(listQB);
        List<ManagerPrivDo> privDoList = new ArrayList<>();
        for (EmpRefOrgDo empRefOrgDo : orgDoList) {
            if (list.contains(empRefOrgDo.getEmpId())) {
                continue;
            }
            ManagerPriv priv = new ManagerPriv();
            priv.init(companyId,empRefOrgDo.getEmpId());
            priv.setId(domainDao.nextLongAsStr(managerPrivSeq));
            privDoList.add(new ToDataBuilder<>(priv,ManagerPrivDo.class).data());
        }
        domainDao.saveBatch(privDoList);
    }

}
