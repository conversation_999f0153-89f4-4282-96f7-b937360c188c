package com.polaris.kpi.open.infr.ppojo;

import com.polaris.kpi.common.infr.DataObj;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.ibatis.annotations.Ckey;
import org.apache.ibatis.annotations.JsonAryColumn;
import org.apache.ibatis.annotations.Table;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Table(value = "open_auth_info")
public class AuthInfoDo extends DataObj {

    private String corpId;//要不要和钉钉一致？
    @Ckey
    private String companyId;
    private String companyName;
    private String contact;
    private String accessKey;
    private String accessSecret;
    private String accessToken;
    private Date permStartTime;
    private Date permEndTime;
    private Long maxAccessLimit;
    private Long accessCount;
    @JsonAryColumn(value = String.class)
    private List<String> apis;
    @JsonAryColumn(value = String.class)
    private List<String> ips;
    private Date createdTime;//
    private Date updatedTime;//

}
