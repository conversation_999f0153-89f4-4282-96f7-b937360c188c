package com.polaris.kpi.setting.ppojo;


import com.polaris.kpi.common.infr.DelData;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.ibatis.annotations.Ckey;

import java.util.Date;
import java.util.Objects;

@Setter
@Getter
@NoArgsConstructor
public class ResultAuditFlowNodeRaterDo extends DelData {

    @Ckey
    private String id;
    private String flowInstanceId;
    private String flowNodeId;
    private String taskUserId;
    private String auditEmpId;
    private Integer status;   // 审批状态(0:未开始  1:执行中 2:已完成 4:校准已完成)
    private Integer skipType;
    private Integer level;
    public ResultAuditFlowNodeRaterDo(String id,String companyId, String flowInstanceId, String flowNodeId,String taskUserId,
                                      String auditEmpId, Integer status,String createdUser,Integer skipType,Integer level) {
        this.id = id;
        this.companyId = companyId;
        this.flowInstanceId = flowInstanceId;
        this.flowNodeId = flowNodeId;
        this.taskUserId = taskUserId;
        this.auditEmpId = auditEmpId;
        this.skipType = skipType;
        this.status = Objects.nonNull(skipType) ? 4 : status;
        this.level = level;
        this.createdUser = createdUser;
        this.createdTime = new Date();
    }
}
