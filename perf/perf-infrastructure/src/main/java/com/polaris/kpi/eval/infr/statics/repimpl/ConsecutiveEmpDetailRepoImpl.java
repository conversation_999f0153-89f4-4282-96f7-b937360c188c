package com.polaris.kpi.eval.infr.statics.repimpl;

import com.polaris.kpi.eval.domain.statics.entity.ConsecutiveEmpDetail;
import com.polaris.kpi.eval.domain.statics.repo.ConsecutiveEmpDetailRepo;
import com.polaris.kpi.eval.infr.statics.ppojo.ConsecutiveEmpDetailDo;
import org.lufei.ibatis.builder.DeleteBuilder;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/23 22:42
 */
@Component
public class ConsecutiveEmpDetailRepoImpl implements ConsecutiveEmpDetailRepo {

    public static final String consecutiveEmpDetailSeq = "consecutive_emp_detail";


    @Resource
    private DomainDaoImpl domainDao;

    @Override
    public void deleteBatch(String companyId, String cycleId, Integer ruleType, Integer performanceType) {

        DeleteBuilder delete = DeleteBuilder.build(ConsecutiveEmpDetailDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("cycle_id", cycleId)
                .whereEqReq("consecutive_type", ruleType)
                .whereEqReq("performance_type", performanceType);
        domainDao.delete(delete);
    }

    @Override
    public void addBatch(List<ConsecutiveEmpDetail> details) {

        details.forEach(detail -> {
            detail.initOnNew(domainDao.nextLongAsStr(consecutiveEmpDetailSeq));
        });
        domainDao.saveBatch(details);
    }

    @Override
    public void deleteConsecutiveEmp(String companyId, String detailId) {
        UpdateBuilder update = UpdateBuilder.build(ConsecutiveEmpDetailDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("id", detailId)
                .set("is_deleted", Boolean.TRUE.toString());

        domainDao.update(update);
    }
}
