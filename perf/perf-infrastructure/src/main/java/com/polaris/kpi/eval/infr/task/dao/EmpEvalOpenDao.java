package com.polaris.kpi.eval.infr.task.dao;

import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalRule;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.PublishResultConf;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskUserDo;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.AdminOfTaskDo;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.EmpEvalRuleDo;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.EvalAuthOnSelfPo;
import com.polaris.kpi.eval.infr.task.ppojo.open.eval.OpenEvalDetailsPo;
import com.polaris.kpi.eval.infr.task.query.empeval.EvalAuthQuery;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 开放接口查询部分dao
 */
@Component
public class EmpEvalOpenDao {
    @Autowired
    private DomainDaoImpl domainDao;

    public List<EvalAuthOnSelfPo> listEvalAuthOnSelf(EvalAuthQuery query, String employeeId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .leftJoin(EmpEvalRuleDo.class, "r")
                .appendOn(" r.company_id = u.company_id and  r.emp_eval_id = u.id and r.is_deleted='false' ")
                .clearSelect().select("r.publish_result,u.id emp_eval_id").setRsType(EmpEvalRuleDo.class)
                .whereEqReq("u.company_id", query.getCompanyId())
                .whereEqReq("u.emp_id", employeeId)
                .whereInReq("u.id", query.getEvalIds());
        List<EmpEvalRuleDo> rules = domainDao.listAll(comQB);
        List<EvalAuthOnSelfPo> rs = rules.stream().map(rule -> {
            PublishResultConf conf = rule.getPublishResult();
            EvalAuthOnSelfPo auth = new EvalAuthOnSelfPo(rule.getEmpEvalId(), conf.wasPublish2Self() ?
                    conf.getRealDimension(rule.getShowResultType()) : 0);
            return auth;
        }).collect(Collectors.toList());
        return rs;
    }

    public List<EvalAuthOnSelfPo> listEvalAuthOnAdmin(EvalAuthQuery query, String employeeId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .leftJoin(AdminOfTaskDo.class, "aot")
                .appendOn(" aot.company_id = u.company_id and  aot.task_id = u.task_id  and aot.admin_emp_id = '" + employeeId + "'")
                .clearSelect().setRsType(EvalAuthOnSelfPo.class)
                .select(" if(aot.admin_emp_id is null,0,7) data_auth ,u.id eval_id  ")
                .whereEqReq("u.company_id", query.getCompanyId())
                .whereInReq("u.id", query.getEvalIds());
        List<EvalAuthOnSelfPo> rules = domainDao.listAllDomain(comQB, EvalAuthOnSelfPo.class);
        return rules;
    }

    public OpenEvalDetailsPo findEvalDetail(String companyId, String taskUserId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .clearSelect()
                .select("emp_id, emp_org_name, org_id, task_id, task_status, final_score, " +
                        "evaluation_level, perf_coefficient, v3_final_self_score, v3_final_superior_score")
                .setRsType(OpenEvalDetailsPo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("id", taskUserId)
                .whereEq("is_deleted", "false");
        return domainDao.findOne(comQB);
    }
}
