package com.polaris.kpi.open.infr.repimpl;

import cn.hutool.core.lang.Assert;
import com.polaris.kpi.open.domain.entity.AccessLimit;
import com.polaris.kpi.open.domain.rep.AccessLimitRep;
import com.polaris.kpi.open.infr.ppojo.AccessLimitDo;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.common.DomainToDataBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class AccessLimitRepImpl implements AccessLimitRep {

    @Autowired
    private DomainDaoImpl domainDao;

    @Override
    public void store(AccessLimit accessLimit) {
        AccessLimitDo accessLimitDo = new AccessLimitDo();
        DomainToDataBuilder<AccessLimit> builder = new DomainToDataBuilder<>(accessLimit, accessLimitDo);
        builder.build();
        int r = this.domainDao.update(accessLimitDo);
        if (r > 0) {
            return;
        }
        this.domainDao.save(accessLimitDo);
    }

    @Override
    public AccessLimit get(String key) {
        Assert.notBlank(key, "access_limit_get_key_is_blank");
        ComQB comQB = ComQB.build(AccessLimitDo.class)
                .whereEq("key", key)
                .limit(0, 1);
        return this.domainDao.findDomain(comQB, AccessLimit.class);
    }

    @Override
    public AccessLimit increment(String key, Long count) {
        Assert.notBlank(key, "access_limit_increment_key_is_blank");
        Assert.isTrue(count > 0, "access_limit_count_less_then_zero");
        UpdateBuilder update = UpdateBuilder.build(AccessLimitDo.class)
                .appendSet(" accessCount = accessCount + " + count )
                .whereEq("key", key);
        this.domainDao.update(update);
        return get(key);
    }
}
