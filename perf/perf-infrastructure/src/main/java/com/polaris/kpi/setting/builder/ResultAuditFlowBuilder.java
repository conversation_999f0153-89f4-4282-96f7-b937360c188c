package com.polaris.kpi.setting.builder;

import cn.com.polaris.kpi.eval.Rater;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.google.common.base.Supplier;
import com.polaris.kpi.eval.domain.task.entity.EvalAudit;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.AuditResultConf;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.BaseAuditNode;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.setting.domain.entity.*;
import com.polaris.kpi.setting.ppojo.*;
import lombok.Getter;
import lombok.Setter;
import org.lufei.ibatis.common.data.ToDataBuilder;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Getter
public class ResultAuditFlowBuilder {
    private String companyId;
    private String opEmpId;
    private Supplier<String> instanceIdGen;
    private Supplier<String> nodeIdGen;
    private Supplier<String> nodeRaterIdGen;
    private Supplier<String> userIdGen;
    private EvalUser taskUser;
    private String md5Key;
    @Setter
    private String instanceId;

    private List<ResultAuditFlowInstanceDo> instances = new ArrayList<>();
    private List<ResultAuditFlowNodeDo> nodes = new ArrayList<>();
    private List<ResultAuditFlowNodeRaterDo> nodeRaters = new ArrayList<>();
    private List<ResultAuditFlowUserDo> userDos = new ArrayList<>();
    private List<TaskResultAuditSummaryDo> summaryDos = new ArrayList<>();

    public ResultAuditFlowBuilder() {
    }

    public ResultAuditFlowBuilder(Supplier<String> instanceIdGen, Supplier<String> nodeIdGen,
                                  Supplier<String> nodeRaterIdGen, Supplier<String> userIdGen, EvalUser taskUser, String companyId, String opEmpId) {
        this.instanceIdGen = instanceIdGen;
        this.nodeIdGen = nodeIdGen;
        this.nodeRaterIdGen = nodeRaterIdGen;
        this.userIdGen = userIdGen;
        this.taskUser = taskUser;
        this.companyId = companyId;
        this.opEmpId = opEmpId;
    }

    public ResultAuditFlowBuilder(Supplier<String> instanceIdGen, Supplier<String> nodeIdGen,
                                  Supplier<String> nodeRaterIdGen, Supplier<String> userIdGen, String companyId, String opEmpId) {
        this.instanceIdGen = instanceIdGen;
        this.nodeIdGen = nodeIdGen;
        this.nodeRaterIdGen = nodeRaterIdGen;
        this.userIdGen = userIdGen;
        this.companyId = companyId;
        this.opEmpId = opEmpId;
    }

    public void build() {
        if (Objects.isNull(this.taskUser)) {
            return;
        }
        if (CollUtil.isEmpty(this.taskUser.getResultAudits())) {
            return;
        }
        String instanceId = StrUtil.isNotBlank(this.instanceId) ? this.instanceId : instanceIdGen.get();

        ResultAuditFlowInstanceDo instanceDo = new ResultAuditFlowInstanceDo(instanceId, companyId,
                taskUser.getTaskId(), taskUser.getId(), taskUser.getOrgId(), null, this.md5Key, opEmpId);
        instances.add(instanceDo);


        Map<Integer, List<EvalAudit>> mapGroup = this.taskUser.getResultAudits().stream()
                .collect(Collectors.groupingBy(EvalAudit::getApprovalOrder));
        mapGroup.forEach((k, v) -> {
            //节点
            String nodeId = nodeIdGen.get();
            ResultAuditFlowNodeDo nodeDo = new ResultAuditFlowNodeDo(nodeId, companyId,
                    instanceId, taskUser.getId(), k, v.get(0).getMultipleReviewersType(), 0, CollUtil.filterNew(v, a -> a.nodeSkip()).size() > 0 ? 1 : 0, opEmpId);
            nodes.add(nodeDo);
            //节点审核员
            for (EvalAudit au : v) {
                String nodeRaterId = nodeRaterIdGen.get();
                ResultAuditFlowNodeRaterDo raterDo = new ResultAuditFlowNodeRaterDo(nodeRaterId, companyId, instanceId,
                        nodeId, taskUser.getId(), au.getApproverInfo(), 0, opEmpId, au.getSkipType(), k);
                nodeRaters.add(raterDo);
//                ResultAuditFlowUserDo flowUserDo = new ResultAuditFlowUserDo(userIdGen.get(), companyId, instanceId, nodeRaterId, taskUser.getId(),
//                        au.getMultipleReviewersType(), 0, k, opEmpId);
//                userDos.add(flowUserDo);
            }
        });
    }

    public void buildV2() {
        if (Objects.isNull(this.taskUser)) {
            return;
        }
        if (CollUtil.isEmpty(this.taskUser.getResultAudits())) {
            return;
        }
        String instanceId = StrUtil.isNotBlank(this.instanceId) ? this.instanceId : instanceIdGen.get();

        ResultAuditFlowInstanceDo instanceDo = new ResultAuditFlowInstanceDo(instanceId, companyId,
                taskUser.getTaskId(), taskUser.getId(), taskUser.getOrgId(), null, this.md5Key, opEmpId);
        instances.add(instanceDo);

        //如果是已经进校准的用户,需要对rater根据实际情况标记状态
        //只处理当前层级
        AtomicReference<Integer> status = new AtomicReference<>(0);
        Integer onProcess;
        if (TalentStatus.RESULTS_AUDITING.getStatus().equals(taskUser.getTaskStatus())) {
            //获取进行中的校准层级
            onProcess = taskUser.getScoreResults().stream()
                    .filter(a -> a.getScorerType().equals("final_result_audit"))
                    .filter(a -> a.getAuditStatus() == null)
                    .mapToInt(EvalScoreResult::getApprovalOrder).max()
                    .orElse(-1);
        } else {
            onProcess = -1;
        }

        Map<Integer, List<EvalAudit>> mapGroup = this.taskUser.getResultAudits().stream()
                .collect(Collectors.groupingBy(EvalAudit::getApprovalOrder));
        mapGroup.forEach((k, v) -> {
            if (k <= onProcess) {
                status.set(2);
            }else {
                status.set(0);
            }
            //节点
            String nodeId = nodeIdGen.get();
            ResultAuditFlowNodeDo nodeDo = new ResultAuditFlowNodeDo(nodeId, companyId,
                    instanceId, taskUser.getId(), k, v.get(0).getMultipleReviewersType(), 0, CollUtil.filterNew(v, a -> a.nodeSkip()).size() > 0 ? 1 : 0, opEmpId);
            nodes.add(nodeDo);
            //节点审核员
            for (EvalAudit au : v) {
                String nodeRaterId = nodeRaterIdGen.get();
                ResultAuditFlowNodeRaterDo raterDo = new ResultAuditFlowNodeRaterDo(nodeRaterId, companyId, instanceId,
                        nodeId, taskUser.getId(), au.getApproverInfo(), status.get(), opEmpId, au.getSkipType(), k);
                nodeRaters.add(raterDo);

            }
        });
    }


    public void build(EvalAudit audit,ResultAuditFlowInstance flowInstance) {
        String instanceId = null;
        if (Objects.nonNull(flowInstance)) {
            instanceId = flowInstance.getId();
        }else {
            instanceId = StrUtil.isNotBlank(this.instanceId) ? this.instanceId : instanceIdGen.get();
            ResultAuditFlowInstanceDo instanceDo = new ResultAuditFlowInstanceDo(instanceId, companyId,
                    taskUser.getTaskId(), taskUser.getId(), taskUser.getOrgId(), null, this.md5Key, audit.getCreatedUser());
            instances.add(instanceDo);
        }

        //节点
        String nodeId = nodeIdGen.get();
        ResultAuditFlowNodeDo nodeDo = new ResultAuditFlowNodeDo(nodeId, companyId,
                instanceId, audit.getTaskUserId(), audit.getApprovalOrder(), audit.getMultipleReviewersType(), 0, audit.nodeSkip() ? 1 : 0, audit.getCreatedUser());
        nodes.add(nodeDo);
        //节点审核员
        String nodeRaterId = nodeRaterIdGen.get();
        ResultAuditFlowNodeRaterDo raterDo = new ResultAuditFlowNodeRaterDo(nodeRaterId, companyId, instanceId,
                nodeId, audit.getTaskUserId(), audit.getApproverInfo(), 0, audit.getCreatedUser(), audit.getSkipType(), audit.getApprovalOrder());
        nodeRaters.add(raterDo);
    }

    /**
     * 根据taskId+节点+审核人员生成MD5
     */
    public String initFlowMD5() {
        if (CollUtil.isEmpty(this.taskUser.getResultAudits())) {
            return null;
        }
        Map<Integer, List<EvalAudit>> mapGroup = this.taskUser.getResultAudits().stream()
                .collect(Collectors.groupingBy(EvalAudit::getApprovalOrder));
        if (CollUtil.isEmpty(mapGroup)) {
            return null;
        }
        mapGroup = MapUtil.sort(mapGroup);
        StringBuilder sb = new StringBuilder();
        sb.append(this.taskUser.getTaskId());
        mapGroup.forEach((k, v) -> {
            //节点
            sb.append(k);
            //节点审核员
            v.sort(Comparator.comparing(EvalAudit::getApproverInfo));
            for (EvalAudit au : v) {
                sb.append(au.getApproverInfo()).append(au.getMultipleReviewersType());
            }
        });
        this.md5Key = DigestUtil.md5Hex(sb.toString());
        return md5Key;
    }

    public void converOld(ResultAuditFlow flow) {
        for (ResultAuditFlowNode flowNode : flow.getFlowNodes()) {
            ResultAuditFlowNodeDo nodeDo = new ToDataBuilder<>(flowNode, ResultAuditFlowNodeDo.class).data();
            nodeDo.setCompanyId(flow.getCompanyId());
            nodeDo.setUpdatedTime(new Date());
            this.nodes.add(nodeDo);
        }
        for (ResultAuditFlowNodeRater flowNodeRater : flow.getFlowNodeRaters()) {
            ResultAuditFlowNodeRaterDo raterDo = new ToDataBuilder<>(flowNodeRater, ResultAuditFlowNodeRaterDo.class).data();
            raterDo.setCompanyId(flow.getCompanyId());
            raterDo.setUpdatedTime(new Date());
            this.nodeRaters.add(raterDo);
        }
    }

    public void conver(ResultAuditFlow flow) {
        ResultAuditFlowInstance flowInstance = flow.getCurInstance();
        if (Objects.isNull(flowInstance)) {
            return;
        }
        ResultAuditFlowInstanceDo instanceDo = new ToDataBuilder<>(flowInstance, ResultAuditFlowInstanceDo.class).data();
        instanceDo.setCompanyId(flow.getCompanyId());
        instanceDo.setUpdatedTime(new Date());
        this.instances.add(instanceDo);
        for (ResultAuditFlowNode flowNode : flowInstance.getFlowNodes()) {
            ResultAuditFlowNodeDo nodeDo = new ToDataBuilder<>(flowNode, ResultAuditFlowNodeDo.class).data();
            nodeDo.setCompanyId(flow.getCompanyId());
            nodeDo.setUpdatedTime(new Date());
            this.nodes.add(nodeDo);
            for (ResultAuditFlowNodeRater nodeRater : flowNode.getNodeRaters()) {
                ResultAuditFlowNodeRaterDo raterDo = new ToDataBuilder<>(nodeRater, ResultAuditFlowNodeRaterDo.class).data();
                raterDo.setCompanyId(flow.getCompanyId());
                raterDo.setUpdatedTime(new Date());
                this.nodeRaters.add(raterDo);
            }
        }
    }

    //初始化校准环节审核
    public void initResultAudit() {
        if (CollUtil.isEmpty(this.taskUser.getResultAudits())) {
            List<EvalAudit> evalAudits = new ArrayList<>();
            AuditResultConf auditResult = this.taskUser.getEmpEvalRule().getAuditResult();
            for (BaseAuditNode auditNode : auditResult.getAuditNodes()) {
                for (Rater rater : auditNode.getRaters()) {
                    EvalAudit audit = new EvalAudit(rater.getEmpId(), auditNode.getApprovalOrder(), auditNode.getMultiType(), rater.getSkipType());
                    evalAudits.add(audit);
                }
            }
            this.taskUser.setResultAudits(evalAudits);
        }
    }
}

