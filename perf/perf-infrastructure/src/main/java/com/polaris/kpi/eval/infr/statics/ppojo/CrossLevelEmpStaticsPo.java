package com.polaris.kpi.eval.infr.statics.ppojo;

import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.eval.domain.statics.entity.CrossLevelGroup;
import lombok.Data;
import org.apache.ibatis.annotations.JsonAryColumn;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/22 11:39
 */
@Data
public class CrossLevelEmpStaticsPo {

    /**
     * 唯一标识符，用于唯一标识一条考核数据记录。
     */
    private String id;
    @JSONField(serialize = false)
    private String companyId;
    /**
     * 周期的唯一标识符，用于标识该考核数据所处的考核周期。
     */
    @JSONField(serialize = false)
    private String cycleId;

    /**
     * 任务关联的用户的唯一标识符，与该考核任务相关的用户。
     */
    private String taskUserId;

    /**
     * 跨级类型，用于表示考核中的跨级情况。
     * 取值为 1 时表示上升，即考核层级向上跨越；
     * 取值为 2 时表示下降，即考核层级向下跨越。
     */
    private Integer crossLevelType;

    /**
     * 标识该考核是否为组织考核。
     * 1-个人 2-组织。
     */
    @JSONField(serialize = false)
    private Integer performanceType;

    /**
     * 被考核人的唯一标识符，用于唯一标识被考核的员工。
     */
    private String empId;

    /**
     * 被考核人的姓名，记录被考核员工的真实姓名。
     */
    private String empName;

    /**
     * 被考核人的头像信息，可能是头像的存储路径或头像的唯一标识。
     */
    private String avatar;

    /**
     * 组织的唯一标识符，代表该考核数据所关联的组织。
     */
    private String orgId;

    /**
     * 组织的名称，对应 orgId 所代表组织的具体名称。
     */
    private String orgName;

    /**
     * 被考核组织的唯一标识符，若考核对象为组织时使用。
     */
    private String evalOrgId;

    /**
     * 被考核组织的名称，对应 evalOrgId 所代表组织的具体名称。
     */
    private String evalOrgName;

    /**
     * 组织名称的路径信息，可能表示组织在组织架构中的层级路径名称。
     */
    private String atOrgNamePath;

    /**
     * 组织编码的路径信息，可能表示组织在组织架构中的层级路径编码。
     */
    @JSONField(serialize = false)
    private String atOrgCodePath;

    @JsonAryColumn(CrossLevelGroup.class)
    private List<CrossLevelGroup> perfCrossData;

    private boolean leaved;


}
