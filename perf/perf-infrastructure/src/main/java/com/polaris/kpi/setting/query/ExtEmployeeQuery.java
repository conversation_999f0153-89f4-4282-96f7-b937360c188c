package com.polaris.kpi.setting.query;


import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.lufei.ibatis.mapper.PagedQuery;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 * @date 2025/9/23 10:39
 */
@Getter
@Setter
@NoArgsConstructor
public class ExtEmployeeQuery extends PagedQuery {

    private String corpId;
    private String name;

    public void check() {
        if (Objects.isNull(corpId)) {
            throw new IllegalArgumentException("corpId is null");
        }
    }
}
