package com.polaris.kpi.eval.infr.confirm.dao;

import cn.com.polaris.kpi.eval.KpiTypeUsedField;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.cache.domain.entity.CompanyCacheInfo;
import com.polaris.kpi.cache.domain.entity.ConfirmTypeCache;
import com.polaris.kpi.eval.domain.confirm.entity.CacheConfirmType;
import com.polaris.kpi.eval.domain.confirm.entity.ConfirmEmpEval;
import com.polaris.kpi.eval.domain.stage.repo.ListEvalUserFinder;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEval;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalKpiType;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.eval.infr.task.dao.BaseEvalContextDao;
import com.polaris.kpi.eval.infr.task.ppojo.CompanyCacheInfoDo;
import com.polaris.sdk.type.AuditEnum;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.lufei.ibatis.builder.ComQB;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Component
@Slf4j
public class ConfirmContextDao extends BaseEvalContextDao {

    public List<EmpEval> pagedBeforeConfirm(TenantId companyId, String adminEmpId, String taskId, int pageNo, Integer performanceType) {
        ListEvalUserFinder finder = () -> super.pagedEvalUser(companyId, adminEmpId, taskId, performanceType,
                TalentStatus.beforeStatus(TalentStatus.CONFIRMING), pageNo, 100);
        return super.listEmpEval(companyId, finder);
    }

    public List<EmpEval> pagedAfterConfirm(TenantId companyId, String adminEmpId, String taskId, List<String> userIds,
                                           int pageNo, Integer performanceType) {
        ListEvalUserFinder finder = () -> super.pagedEvalUser(companyId, adminEmpId, taskId, performanceType,
                TalentStatus.afterEqStatus(TalentStatus.CONFIRMING), pageNo, 100);
        ListEvalUserFinder finder2 = () -> super.pagedEvalUser(companyId, userIds, pageNo, 100);
        return super.listEmpEval(companyId, CollUtil.isEmpty(userIds) ? finder : finder2);
    }


    public EmpEval getEmpEval(TenantId companyId, String taskUserId) {
        List<EmpEval> evals = super.listEmpEval(companyId, () -> super.mapBaseTaskUser(companyId, Arrays.asList(taskUserId)));
        if (CollUtil.isEmpty(evals)) {
            return null;
        }
        return evals.get(0);
    }

    public List<EmpEvalKpiType> getConfirmCache(ConfirmEmpEval rule, TenantId companyId, String scene, String taskUserId, String empId, boolean indexCofMdfEmp) {
        List<KpiTypeUsedField> kpiTypeUsedFields = rule.getTypeUsedFields();
        ComQB comQB = ComQB.build(CompanyCacheInfoDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("business_scene", scene)
                .whereEqReq("link_id", taskUserId)
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        CompanyCacheInfo cacheInfo = domainDao.findDomain(comQB, CompanyCacheInfo.class);
        rule.buildScoreConf(cacheInfo,indexCofMdfEmp);
        List<EmpEvalKpiType> pos = new ArrayList<>();
        if (cacheInfo == null) {
            return pos;
        }
        List<ConfirmTypeCache> confirmTypeCaches = JSONUtil.toList(cacheInfo.getValue(), ConfirmTypeCache.class);

        for (ConfirmTypeCache cache : confirmTypeCaches) {
//            EmpEvalKpiType type = cache.toEvalType();
            if (Boolean.valueOf(cache.getIsDeleted())) {
                continue;
            }
            CacheConfirmType type = new CacheConfirmType();
            type.fromCache(cache, empId);
            type.initCacheKpiTypes(kpiTypeUsedFields);
            pos.add(type);
        }

        return pos;
    }


}
