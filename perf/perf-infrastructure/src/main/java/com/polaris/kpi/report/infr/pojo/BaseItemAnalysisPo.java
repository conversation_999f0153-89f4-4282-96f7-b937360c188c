package com.polaris.kpi.report.infr.pojo;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.eval.domain.task.entity.empeval.ComputeFinishValueProgress;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/7/23 10:08
 */
@Setter
@Getter
@NoArgsConstructor
public class BaseItemAnalysisPo {

    private String itemId;
    private String itemName;
    private String itemUnit;
    private Integer finishValueType;
    private BigDecimal targetValue = BigDecimal.ZERO;
    private BigDecimal finishValue = BigDecimal.ZERO;
    private String finishRate;
    @JSONField(serialize = false)
    private ComputeFinishValueProgress progress;

    public void accProcess(ComputeFinishValueProgress progress){
        this.progress = progress;
    }

    public void computeFinishRate(){
        finishRate = progress.getPercent(targetValue, finishValue, finishValueType);
    }

    public void removeZero(){
        if (StrUtil.isNotBlank(this.finishRate)) {
            BigDecimal value = new BigDecimal(this.finishRate);
            this.finishRate = value.stripTrailingZeros().toPlainString();
        }
    }
}
