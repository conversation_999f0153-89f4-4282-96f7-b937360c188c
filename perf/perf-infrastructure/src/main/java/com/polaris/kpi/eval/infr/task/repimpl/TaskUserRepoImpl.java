package com.polaris.kpi.eval.infr.task.repimpl;

import cn.com.polaris.kpi.EvalOrg;
import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.com.polaris.kpi.eval.*;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.perf.www.common.constant.BusinessConstant;
import com.perf.www.common.em.OperationLogSceneEnum;
import com.perf.www.common.utils.string.StringTool;
import com.perf.www.model.kpi.CompanyCategoryModel;
import com.polaris.kpi.cache.domain.entity.CompanyCacheInfo;
import com.polaris.kpi.common.KpiI18NException;
import com.polaris.kpi.eval.domain.confirm.repo.BatchLoadConfirmFlowRepo;
import com.polaris.kpi.eval.domain.confirm.repo.ConfirmEvalRuleRepo;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.admineval.EmpEvalChangeResult;
import com.polaris.kpi.eval.domain.task.entity.admineval.OpEmpEval;
import com.polaris.kpi.eval.domain.task.entity.empeval.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.chain.ScoreNode;
import com.polaris.kpi.eval.domain.task.entity.flow.LevelAuditFlow;
import com.polaris.kpi.eval.domain.task.entity.flow.ScoreNodeFlow;
import com.polaris.kpi.eval.domain.task.entity.grade.ScoreRule;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrGoal;
import com.polaris.kpi.eval.domain.task.repo.BatchUpdateBaseEvalUser;
import com.polaris.kpi.eval.domain.task.repo.TaskUserRepo;
import com.polaris.kpi.eval.domain.task.type.*;
import com.polaris.kpi.eval.infr.task.builder.Audit3TypeRuleBd;
import com.polaris.kpi.eval.infr.task.builder.EmpEvalScorerBatchDataBd;
import com.polaris.kpi.eval.infr.task.builder.EmpRule2DataBd;
import com.polaris.kpi.eval.infr.task.builder.TaskUserBuilder;
import com.polaris.kpi.eval.infr.task.dao.*;
import com.polaris.kpi.eval.infr.task.ppojo.*;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.AdminTaskOperationDo;
import com.polaris.kpi.eval.infr.task.ppojo.ask360.PerfEvalRefAskDo;
import com.polaris.kpi.eval.infr.task.ppojo.calibrated.ResultAuditBatchItemDo;
import com.polaris.kpi.eval.infr.task.ppojo.calibrated.ResultAuditCacheDo;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.*;
import com.polaris.kpi.eval.infr.task.ppojo.okr.OkrGoalDo;
import com.polaris.kpi.org.domain.dept.type.TaskId;
import com.polaris.kpi.org.domain.emp.entity.KpiEmployee;
import com.polaris.kpi.org.infr.company.ppojo.CompanyMsgCenterDo;
import com.polaris.kpi.org.infr.emp.dao.KpiEmpDao;
import com.polaris.kpi.org.infr.emp.pojo.EmpOrganizationDo;
import com.polaris.kpi.org.infr.emp.pojo.EmployeeBaseInfoDo;
import com.polaris.kpi.org.infr.emp.pojo.RoleRefEmpDo;
import com.polaris.kpi.setting.domain.entity.ResultAuditFlowNode;
import com.polaris.kpi.setting.ppojo.ResultAuditFlowNodeDo;
import com.polaris.kpi.setting.ppojo.ResultAuditFlowNodeRaterDo;
import com.polaris.kpi.setting.ppojo.ResultAuditFlowUserDo;
import com.polaris.sdk.type.AuditEnum;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.lufei.ibatis.builder.*;
import org.lufei.ibatis.common.data.ToDataBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.infr.task.repimpl
 * @Author: lufei
 * @CreateTime: 2022-05-10  17:53
 * @Description: TODO
 * @Version: 1.0
 */
@Component
@Slf4j
public class TaskUserRepoImpl implements TaskUserRepo, BatchUpdateBaseEvalUser {
    public static final String taskUserSeq = "perf_evaluate_task_user";
    //public static final String taskUserSeq = "perf_evaluate_task_base";
    public static final String auditSeq = "perf_evaluate_task_audit";
    public static final String kpiItemSeq = "perf_evaluate_task_kpi";
    public static final String evalCommonSeq = "perf_evaluate";
    public static final String taskFormulaField = "task_formula_field";
    //public static final String taskItemScoreRule = "task_item_score_rule";
    public static final String perfEvalTotalLevelResultSeq = "perf_eval_total_level_result";
    public static final String perfTaskChangeBatch = "perf_task_change_batch";
    public static final String perfTaskChangeBatchDetail = "perf_task_change_batch_detail";
    public static final String scoreRejectSeq = "score_reject";
    public static final String diffLog = "log";


    @Autowired
    private DomainDaoImpl domainDao;
    @Autowired
    private GradeDao gradeDao;
    @Autowired
    private TaskUserDao userDao;
    @Autowired
    private EvalKpiDao kpiDao;
    @Autowired
    private OpLogDao opLogDao;
    @Autowired
    private EmpEvalDao empEvalDao;
    @Autowired
    private KpiEmpDao kpiEmpDao;
    @Autowired
    private TaskKpiItemDao taskKpiItemDao;

    private final Supplier<String> auditIdGen = () -> domainDao.nextLongAsStr(auditSeq);
    private final Supplier<String> evalCommonGen = () -> domainDao.nextLongAsStr(evalCommonSeq);
    private final Supplier<String> kpiItemGen = () -> domainDao.nextLongAsStr(kpiItemSeq);
    private final Supplier<String> taskFormulaFieldGen = () -> domainDao.nextLongAsStr(taskFormulaField);
    public void setDomainDao(DomainDaoImpl domainDao) {
        this.domainDao = domainDao;
    }

    public void setOpLogDao(OpLogDao opLogDao) {
        this.opLogDao = opLogDao;
    }

    public void setTaskUserDao(TaskUserDao userDao) {
        this.userDao = userDao;
    }

    @Override
    public EvalUser getBaseTaskUser(TenantId companyId, String taskUserId) {
        EvalUser evalUser = userDao.getBaseEvalUser(companyId, taskUserId);
        if (Objects.isNull(evalUser)) {
            throw new KpiI18NException("eval task is not exist", "考核任务不存在或已被删除！");
        }
        return evalUser;
    }

    @Override
    public EvalUser getTaskUser(TenantId tenantId, String taskUserId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("id", taskUserId);
        EvalUser taskUser = this.domainDao.findDomain(comQB, EvalUser.class);
        Assert.notNull(taskUser, "missed_task_user: {}, {}", tenantId.getId(), taskUserId);
        ComQB ruleQb = ComQB.build(EmpEvalRuleDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("emp_eval_id", taskUser.getId())
                .appendWhere("is_deleted ='false'");
        EmpEvalRule empEvalRule = domainDao.findDomain(ruleQb, EmpEvalRule.class);
        taskUser.setEmpEvalRule(empEvalRule);
        ComQB scoreComQb = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "pets")
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", taskUser.getId())
                .whereEq("is_deleted", Boolean.FALSE.toString());
        List<EvalScoreResult> scoreResults = this.domainDao.listAllDomain(scoreComQb, EvalScoreResult.class);
        taskUser.setScoreResults(scoreResults);
        loadKpis(taskUser);
        return taskUser;
    }

    @Override
    public EvalUser getBaseTaskUserAndRule(TenantId tenantId, String taskUserId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEqReq("id", taskUserId);
        EvalUser taskUser = this.domainDao.findDomain(comQB, EvalUser.class);
        Assert.notNull(taskUser, "missed_task_user: {}, {}", tenantId.getId(), taskUserId);
        ComQB ruleQb = ComQB.build(EmpEvalRuleDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("emp_eval_id", taskUser.getId())
                .appendWhere("is_deleted ='false'");
        EmpEvalRule empEvalRule = domainDao.findDomain(ruleQb, EmpEvalRule.class);
        taskUser.setEmpEvalRule(empEvalRule);
        return taskUser;
    }


    @Override
    public void lazyLoadItemRule(EvalUser userEval) {
        if (CollUtil.isEmpty(userEval.getKpis())) {
            lazyLoadKpiItem(userEval);
        }
        if (CollUtil.isEmpty(userEval.getKpis())) {
            return;
        }
        if (userEval.getKpis().get(0).itemScoreRule() != null) {
            return;
        }
        List<String> roleIds = new ArrayList<>();
        List<LevelManager> managers = new ArrayList<>();
        ListWrap<EvalItemScoreRule> ruleMap = kpiDao.listEmpEvalItemRule(userEval.getCompanyId(), userEval.getId());
        ListWrap<EvalFormulaField> formulaFields = kpiDao.listEvalEmpFormulaField(userEval.getCompanyId(), userEval.getId());
        List<KpiItemUsedField> kpiItemUsedFields = getItemUsedFieldDoList(userEval.getCompanyId(), userEval.getId());
        Map<String, List<KpiItemUsedField>> kpiItemUserFieldGroupMap = kpiItemUsedFields.stream().collect(Collectors.groupingBy(KpiItemUsedField::getKpiItemId));

        ListWrap<EvalScorerNodeKpiItem> nodeKpiItemWrap = kpiDao.listEvalScoreNodeKpiItem(userEval.getCompanyId(), userEval.getId(), null);
        nodeKpiItemWrap.groupBy(EvalScorerNodeKpiItem::getKpiItemId);

        ListWrap<EvalScoreResult> empScoreRs = kpiDao.listEvalEmpScoreRs(userEval.getCompanyId(), userEval.getId(), AuditEnum.scoreScenes(), null);
        empScoreRs.groupBy(EvalScoreResult::getKpiItemId);

        for (EvalKpi kpi : userEval.getKpis()) {
            if (userEval.getEmpEvalRule() != null && !userEval.getEmpEvalRule().isCustom()) {//新任务
                EmpEvalRule empEvalRule = userEval.getEmpEvalRule();
                EvalItemScoreRule scoreRule = empEvalRule.asItemRule();
                kpi.customItemScoreRule(scoreRule);
            } else {
                EvalItemScoreRule scoreRule = ruleMap.mapGet(kpi.getKpiItemId());
                kpi.customItemScoreRule(scoreRule);
            }
            List<EvalFormulaField> fields = formulaFields.groupGet(kpi.getKpiItemId());
            kpi.formulaFields(fields);

            //组装kpiitem自定义字段
            if (MapUtil.isNotEmpty(kpiItemUserFieldGroupMap) && kpiItemUserFieldGroupMap.containsKey(kpi.getKpiItemId())) {
                kpi.initKpiItemUsedFields(kpiItemUserFieldGroupMap.get(kpi.getKpiItemId()));
            }
//
//            List<EvalScoreResult> results = empScoreRs.groupGet(kpi.getKpiItemId());
//            kpi.setWaitScoresOld(results);
            List<EvalScorerNodeKpiItem> scorerNodeKpiItems = nodeKpiItemWrap.groupGet(kpi.getKpiItemId());
            kpi.setWaitScores(scorerNodeKpiItems);

//            if (CollUtil.isNotEmpty(kpi.getScorerObjId())) {
//                appointManager(userEval.getCompanyId(), userEval.getCreatedUser(), managers, kpi);
//            }
            roleIds.addAll(kpi.appointRoleIds());
        }

        if (!managers.isEmpty()) {
            Map<String, List<LevelManager>> managerMap = managers.stream().collect(Collectors.groupingBy(manager -> manager.getLevel().toString()));
            userEval.setLevelManagers(managerMap);
        }

        if (CollUtil.isEmpty(roleIds)) {
            LoggerFactory.getLogger(getClass()).info("roleIds empty");
            return;
        }
        LoggerFactory.getLogger(getClass()).info("指标中指定角色评分：{}", JSONUtil.toJsonStr(roleIds));
        ComQB roleComQb = ComQB.build(RoleRefEmpDo.class, "r")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn("r.company_id= e.company_id and r.emp_id=e.employee_id")
                .clearSelect()
                .select("e.employee_id as empId,e.name as empName,r.role_id as roleId")
                .setRsType(RoleRefEmp.class)
                .whereEq("r.company_id", userEval.getCompanyId().getId())
                .whereEq("e.is_delete", Boolean.FALSE.toString())
                .whereEq("r.is_deleted", Boolean.FALSE.toString())
                .whereIn("role_id", roleIds);
        List<RoleRefEmp> roleRefEmps = domainDao.listAll(roleComQb);
        if (CollUtil.isEmpty(roleRefEmps)) {
            return;
        }
        Map<String, List<RoleRefEmp>> roleMap = roleRefEmps.stream().collect(Collectors.groupingBy(role -> role.getRoleId()));
        userEval.setRoleRefEmps(roleMap);
    }

    @Override
    public void lazyLoadScoreCustomAuit(EvalUser taskUser) {
        ComQB comQB = ComQB.build(TaskAuditDo.class)
                .whereEqReq("company_id", taskUser.getCompanyId().getId())
                .whereEqReq("task_id", taskUser.getTaskId())
                .whereEqReq("task_user_id", taskUser.getId())
                .whereIn("scene", EvaluateAuditSceneEnum.customScoreRuleScenes())
                .whereEq("is_deleted", "false")
                .orderByAsc("approval_order");
        List<EvalAudit> audits = domainDao.listAllDomain(comQB, EvalAudit.class);
        taskUser.setAllCustomAudits(audits);
    }

    @Override
    public void updateAutoScore(EvalUser taskUser) {
        int count = domainDao.update(PerfEvaluateTaskUserDo.class, taskUser);
        if (count > 0) {
            taskUser.incrementVersion();
        }
        List<EvalKpi> items = taskUser.autoItems();
        List<PerfEvaluateTaskKpiDo> datas = items.stream()
                .map(taskKpi -> new ToDataBuilder<>(taskKpi, PerfEvaluateTaskKpiDo.class).data())
                .collect(Collectors.toList());
        for (PerfEvaluateTaskKpiDo data : datas) {
            domainDao.update(data);
        }
    }

    @Override
    public void batchUpdateDispacthed(List<EvalAudit> allCustomAudits) {
        if (CollUtil.isEmpty(allCustomAudits)) {
            return;
        }
        List<String> auditIds = allCustomAudits.stream().map(perfEvaluateTaskAudit -> perfEvaluateTaskAudit.getId()).collect(Collectors.toList());
        UpdateBuilder up = UpdateBuilder.build(TaskAuditDo.class)
                .whereInReq("id", auditIds)
                .whereEqReq("companyId", allCustomAudits.get(0).getCompanyId().getId())
                .appendSet("status ='dispatched'");
        domainDao.update(up);
    }

    @Override
    public void batchUpdate(List<EvalAudit> allCustomAudits) {
        if (CollUtil.isEmpty(allCustomAudits)) {
            return;
        }
        for (EvalAudit audit : allCustomAudits) {
            domainDao.updateNoCheck(TaskAuditDo.class, audit);
        }
    }

    public void batchDeleted(List<EvalAudit> allCustomAudits) {
        if (CollUtil.isEmpty(allCustomAudits)) {
            return;
        }
        List<String> auditIds = allCustomAudits.stream().map(perfEvaluateTaskAudit -> perfEvaluateTaskAudit.getId()).collect(Collectors.toList());
        UpdateBuilder up = UpdateBuilder.build(TaskAuditDo.class)
                .whereInReq("id", auditIds)
                .whereEqReq("companyId", allCustomAudits.get(0).getCompanyId().getId())
                .appendSet("is_deleted ='true'");
        domainDao.update(up);
    }

    @Override
    public void addTaskUser(EvalUser taskUser) {
        if (!taskUser.isNew()) {
            return;
        }
        final Supplier<String> auditIdGen = () -> domainDao.nextLongAsStr(auditSeq);
        final Supplier<String> evalCommonGen = () -> domainDao.nextLongAsStr(evalCommonSeq);
        final Supplier<String> kpiItemGen = () -> domainDao.nextLongAsStr(kpiItemSeq);
        final Supplier<String> taskFormulaFieldGen = () -> domainDao.nextLongAsStr(taskFormulaField);
        taskUser.initId(domainDao.nextLongAsStr(taskUserSeq));
        final TaskUserBuilder builder = new TaskUserBuilder(taskUser, kpiItemGen, auditIdGen, evalCommonGen, taskFormulaFieldGen);
        builder.build();
        domainDao.saveBatch(builder.getKpis());
        domainDao.saveBatch(builder.getTypeDos());
        domainDao.saveBatch(builder.getItemUsedFieldDos());
        domainDao.saveBatch(builder.getUsedFieldDos());
        domainDao.saveBatch(builder.getOkrTypes());
        domainDao.saveBatch(builder.allAuditDatas());
        if (CollUtil.isNotEmpty(builder.getCustomItemRules())) {
            domainDao.saveBatch(builder.getCustomItemRules());
        }
        if (CollUtil.isNotEmpty(builder.getFormulaFields())) {
            domainDao.saveBatch(builder.getFormulaFields());
        }
        domainDao.save(builder.getScoreRule());
        domainDao.add(PerfEvaluateTaskUserDo.class, taskUser);
        //新人考核是直接模板发起的, 需要启动记录日志,考核表模式是1 先创建人员 2 再发起的考核. 在2步是记录了
        if (taskUser.isNewEmp()) {
            OperationLogDo operationLog =
                    new OperationLogDo(taskUser.getCompanyId().getId(),
                            taskUser.getId(),
                            OperationLogSceneEnum.CREATED_TASK.getScene(),
                            taskUser.getCreatedUser(),
                            taskUser.getCreatedTime());
            domainDao.save(operationLog);
        }
    }

    @Override
    public boolean lockTaskUser(TenantId companyId, String taskUserId) {
        UpdateBuilder up = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                .appendSet("version = version")
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("id", taskUserId);
        int count = domainDao.update(up);
        return count > 0;
    }

    @Override
    public boolean updateTaskUserRuleConfStatus(String opEmpId, EvalUser taskUser) {
        UpdateBuilder up = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                .set("rule_conf_status", taskUser.getRuleConfStatus())
                .set("rule_conf_error", taskUser.getRuleConfError())
                .set("updated_time", new Date())
                .set("updated_user", opEmpId)
                .whereEqReq("company_id", taskUser.getCompanyId().getId())
                .whereEqReq("id", taskUser.getId());
        int count = domainDao.update(up);
        return count > 0;
    }

    @Override
    public void updateTaskUser(EvalUser taskUser) {
        int count = domainDao.update(PerfEvaluateTaskUserDo.class, taskUser);
        if (count > 0) {
            taskUser.incrementVersion();
        }
    }

    //
//    UPDATE perf_evaluate_task_user
//    SET updated_time                                     = ?,
//    version                                          = version + 1,
//    perf_evaluate_task_user.cycle_id                 = ?,
//    perf_evaluate_task_user.at_org_code_path         = ?,
//    perf_evaluate_task_user.created_time             = ?,
//    perf_evaluate_task_user.id                       = ?,
//    perf_evaluate_task_user.task_status              = ?,
//    perf_evaluate_task_user.created_user             = ?,
//    perf_evaluate_task_user.updated_time             = ?,
//    perf_evaluate_task_user.emp_org_name             = ?,
//    perf_evaluate_task_user.at_org_name_path         = ?,
//    perf_evaluate_task_user.mutual_eval_compute_type = ?,
//    perf_evaluate_task_user.company_id               = ?,
//    perf_evaluate_task_user.rule_conf_status         = ?,
//    perf_evaluate_task_user.score_end_flag           = ?,
//    perf_evaluate_task_user.emp_id                   = ?,
//    perf_evaluate_task_user.org_id                   = ?,
//    perf_evaluate_task_user.is_deleted               = ?,
//    perf_evaluate_task_user.temp_task                = ?,
//    perf_evaluate_task_user.emp_name                 = ?,
//    perf_evaluate_task_user.at_org_path_hight        = ?,
//    perf_evaluate_task_user.is_new_emp               = ?,
//    perf_evaluate_task_user.task_id                  = ?
//    WHERE (id = ? AND company_id = ?);
    @Override
    public void updateBaseEvalUser(TenantId companyId, List<EvalUser> members) {
        if (CollUtil.isEmpty(members)) {
            return;
        }
        List<PerfEvaluateTaskUserDo> list = Convert.toList(PerfEvaluateTaskUserDo.class, members);
        members.forEach(evalUser -> evalUser.setUpdatedTime(new Date()));
        BatchUpdateBuilder up = BatchUpdateBuilder.buildTable("perf_evaluate_task_user")
                .addSetCaseProp("updatedTime", "id:=")
                .addSetCaseProp("empName", "id:=")
                .addSetCaseProp("atOrgCodePath", "id:=")
                .addSetCaseProp("atOrgPathHight", "id:=")
                .addSetCaseProp("atOrgNamePath", "id:=")
                .addSetCaseProp("taskStatus", "id:=")
                .addSetCaseProp("empOrgName", "id:=")
                .addSetCaseProp("mutualEvalComputeType", "id:=")
                .addSetCaseProp("ruleConfStatus", "id:=")
                .addSetCaseProp("scoreEndFlag", "id:=")
                .addSetCaseProp("tempTask", "id:=")
                .addSetCaseProp("isNewEmp", "id:=")
                .addSetCaseProp("inputFinishStatus", "id:=")
                .addSetCaseProp("reviewersJson", "id:=")
                .addSetCaseProp("evalGroupId", "id:=")
                .addSetCaseProp("evalGroupName", "id:=")
                .addSetCaseProp("confirmDeadLine", "id:=")
                .addAllBean(list)
                .whereEq("companyId", companyId.getId())
                .whereUseIn("id");
        domainDao.updateBatch(up);
    }

    public void updateEvalScore(EvalUser taskUser) {
        this.updateTaskUser(taskUser);
        if (CollUtil.isEmpty(taskUser.getKpiTypes())) {
            return;
        }
        for (EmpEvalKpiType kpiType : taskUser.getKpiTypes()) {
            if (CollUtil.isNotEmpty(kpiType.getOkrGoals())) {
                //保存goal到表中
                for (OkrGoal okrGoal : kpiType.getOkrGoals()) {
                    domainDao.update(OkrGoalDo.class, okrGoal);
                }
            }
        }
    }

    public void revokeDistribution(List<String> taskUserIds, String companyId) {
        if (CollectionUtils.isEmpty(taskUserIds)) {
            return;
        }
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(Integer.class).setSql("UPDATE perf_evaluate_task_user SET evaluation_level = distribution_eval_level,step_id=distribution_before_step_id, distribution_flag = 'false' WHERE company_id = #{companyId}  " +
                " AND original_evaluation_level is NOT NULL and distribution_eval_level is not null");
        sqlBuilder.append("AND id in").append(StringTool.getInStr(taskUserIds).toString());
        sqlBuilder.setValue("companyId", companyId);
        domainDao.nativeExecute(sqlBuilder);
    }

    @Override
    public void notChekUpdateTaskUser(EvalUser taskUser) {
        int count = domainDao.updateNoCheck(PerfEvaluateTaskUserDo.class, taskUser);
        if (count > 0) {
            taskUser.incrementVersion();
        }
    }

    @Override
    public void updateStage(TenantId companyId, String evalUserId, TalentStatus stageStatus) {
        final UpdateBuilder up = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                .set("taskStatus", stageStatus.getStatus())
                .whereEqReq("companyId", companyId.getId())
                .whereEqReq("id", evalUserId);
        domainDao.update(up);
    }

    @Override
    public void updateStage(EvalUser evalUser) {
        final UpdateBuilder up = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                .set("taskStatus", evalUser.getTaskStatus())
                .appendSet("version=version+1")
                .whereEqReq("companyId", evalUser.getCompanyId().getId())
                .whereEqReq("id", evalUser.getId())
                .whereEq("version", evalUser.getVersion());
        domainDao.update(up);
    }

    @Override
    public void updateStageSubStaus(EvalUser evalUser) {
        final UpdateBuilder up = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                .set("taskStatus", evalUser.getTaskStatus())
                .set("sub_status", evalUser.getSubStatus())
                .appendSet("version=version+1")
                .whereEqReq("companyId", evalUser.getCompanyId().getId())
                .whereEqReq("id", evalUser.getId());
//                .whereEq("version", evalUser.getVersion());
        domainDao.update(up);
    }

    @Override
    public void batchUpdateStageSubStaus(TenantId tenantId, List<EvalUser> evalUsers) {
        List<String> ids = evalUsers.stream().map(evalUser -> evalUser.getId()).collect(Collectors.toList());
        BatchUpdateBuilder batchUpdate = BatchUpdateBuilder.buildTable("perf_evaluate_task_user")
                .addSetCaseProp("taskStatus", "id:=")
                .addSetCaseProp("subStatus", "id:=")
                //.appendSet("version=version+1")
                .addAllBean(evalUsers)
                .whereEq("companyId", tenantId.getId())
                .whereIn("id", ids);
        domainDao.updateBatch(batchUpdate);
    }

    @Override
    public String saveChangeBatch(String companyId, String opEmpId, String taskId) {
        //先删除
        final UpdateBuilder up = UpdateBuilder.build(PerfTaskChangeBatchDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("companyId", companyId)
                .whereEqReq("task_id", taskId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.update(up);
        //详情的也同步删除
        final UpdateBuilder detailUp = UpdateBuilder.build(PerfTaskChangeBatchDetailDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("companyId", companyId)
                .whereEqReq("task_id", taskId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.update(detailUp);

        //新增
        final Supplier<String> changeBatchIdGen = () -> domainDao.nextLongAsStr(perfTaskChangeBatch);
        PerfTaskChangeBatchDo batchDo = new PerfTaskChangeBatchDo();
        String id = changeBatchIdGen.get();
        batchDo.setId(id);
        batchDo.setCompanyId(companyId);
        batchDo.setTaskId(taskId);
        batchDo.setStatus("load");
        batchDo.setCreatedUser(opEmpId);
        batchDo.setCreatedTime(new Date());
        batchDo.setIsDeleted(Boolean.FALSE.toString());
        domainDao.save(batchDo);
        return id;
    }

    @Override
    public void upChangeBatchStatus(String companyId, String batchId, String status) {
        this.upChangeBatchStatus(companyId, batchId, status, null);
    }

    @Override
    public void upChangeBatchStatus(String companyId, String batchId, String status, Integer totalCnt) {
        final UpdateBuilder up = UpdateBuilder.build(PerfTaskChangeBatchDo.class)
                .set("status", status)
                .whereEqReq("companyId", companyId)
                .whereEqReq("id", batchId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        if (Objects.nonNull(totalCnt)) {
            up.set("total_cnt", totalCnt);
        }
        domainDao.update(up);
    }

    @Override
    public void upChangeBatch(String companyId, String batchId, String status, Integer finishdCnt, List<VacancyError> vacancyErrors, Integer totalCnt) {
        final UpdateBuilder up = UpdateBuilder.build(PerfTaskChangeBatchDo.class)
                .set("status", status)
                .set("finishd_cnt", finishdCnt)
                .set("total_cnt", totalCnt)
                .whereEqReq("companyId", companyId)
                .whereEqReq("id", batchId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        if (CollUtil.isNotEmpty(vacancyErrors)) {
            up.set("error_cnt", vacancyErrors.size());
            up.set("vacancy_errors", JSONUtil.toJsonStr(vacancyErrors));
        }
        domainDao.update(up);
    }

    @Override
    public void batchAddChangeResult(String companyId, String opEmpId, List<EmpEvalChangeResult> changeResults) {
        if (CollUtil.isEmpty(changeResults)) {
            return;
        }
        final Supplier<String> changeResultIdGen = () -> domainDao.nextLongAsStr(perfTaskChangeBatchDetail);
        List<PerfTaskChangeBatchDetailDo> detailDos = new ArrayList<>();
        changeResults.forEach(result -> {
            result.setCompanyId(companyId);
            PerfTaskChangeBatchDetailDo detailDo = new ToDataBuilder<>(result, PerfTaskChangeBatchDetailDo.class).data();
            detailDo.setId(changeResultIdGen.get());
            detailDo.setCreatedUser(opEmpId);
            detailDo.setCreatedTime(new Date());
            detailDo.setIsDeleted(Boolean.FALSE.toString());
            detailDos.add(detailDo);
        });
        domainDao.saveBatch(detailDos);
    }

    @Override
    public void updateChangeResult(EmpEvalChangeResult result) {
        PerfTaskChangeBatchDetailDo detailDo = new ToDataBuilder<>(result, PerfTaskChangeBatchDetailDo.class).data();
        UpdateBuilder updateBuilder = UpdateBuilder.build("perf_task_change_batch_detail")
                .setBean(detailDo)
                .whereEq("id", detailDo.getId());
        if (Objects.isNull(result.getOrgChangeInfo())) {
            updateBuilder.set("org_change_info", null);
        }
        domainDao.update(updateBuilder);
    }

    @Override
    public void updateRejectScoreRs(EvalUser evalUser, ScoreReject scoreReject) {

        this.updateTaskUser(evalUser);

        scoreReject.initOnNew(domainDao.nextIntId(scoreRejectSeq));
        ScoreRejectDo data = new ToDataBuilder<>(scoreReject, ScoreRejectDo.class).data();
        domainDao.save(data);

//        if (clearTodo){
//            final UpdateBuilder up = UpdateBuilder.build(TaskAuditDo.class)
//                    .set("status", evalAudit.getStatus())
//                    .set("updated_time", new Date())
//                    .set("updated_user", evalAudit.getUpdatedUser())
//                    .whereEqReq("id", evalAudit.getId());
//            domainDao.update(up);
//        }
    }

    @Override
    public void updateAuditStatusToWait(TenantId companyId, String taskUserId, ChainDispatchRs dispatchRs) {
        List<String> resultIds = dispatchRs.getItemResults().mapTo(EvalScoreResult::getId);
        if (CollUtil.isNotEmpty(resultIds)) {
            UpdateBuilder builder = UpdateBuilder.build(PerfEvaluateTaskScoreResultDo.class)
                    .appendSet("audit_status = 'wait'")
                    .whereEqReq("company_id", companyId.getId())
                    .whereEqReq("task_user_id", taskUserId)
                    .whereInReq("id", resultIds);
            this.domainDao.update(builder);
        }

        List<String> typeResultIds = new ListWrap<>(dispatchRs.getTypeResults()).mapTo(PerfEvalTypeResult::getId);
        if (CollUtil.isNotEmpty(typeResultIds)) {
            UpdateBuilder upType = UpdateBuilder.build(PerfEvalTypeResultDo.class)
                    .appendSet("audit_status = 'wait'")
                    .whereEqReq("company_id", companyId.getId())
                    .whereEqReq("task_user_id", taskUserId)
                    .whereInReq("id", typeResultIds);
            this.domainDao.update(upType);
        }
    }

    @Override
    public void batchUpdateEvalUserOrgInfo(String companyId, List<EvalUser> evalUsers) {

        BatchUpdateBuilder up = BatchUpdateBuilder.buildTable("perf_evaluate_task_user")
                .addSetCaseProp("orgId", "id:=")
                .addSetCaseProp("empOrgName", "id:=")
                .addSetCaseProp("atOrgCodePath", "id:=")
                .addSetCaseProp("atOrgNamePath", "id:=")
                .addSetCaseProp("atOrgPathHight", "id:=")
                .addSetCaseProp("updatedUser", "id:=")
                .addAllBean(evalUsers)
                .whereEq("companyId", companyId)
                .whereUseIn("id");
        domainDao.updateBatch(up);
    }

    @Override
    public void updateTypeRefAskEval(String companyId, String taskUserId, List<EmpEvalKpiType> kpiTypes) {
        List<EmpEvalKpiTypeDo> dos = Convert.toList(EmpEvalKpiTypeDo.class, kpiTypes);
        BatchUpdateBuilder up = BatchUpdateBuilder.buildTable("emp_eval_kpi_type")
                .addSetCaseProp("ask360EvalId", "kpiTypeId:=")
                .addAllBean(dos)
                .whereEq("companyId", companyId)
                .whereEq("taskUserId", taskUserId)
                .whereUseIn("kpiTypeId");
        domainDao.updateBatch(up);
    }

    @Override
    public int updateStage(TenantId companyId, List<String> evalUserIds, TalentStatus stageStatus) {
        final UpdateBuilder up = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                .set("taskStatus", stageStatus.getStatus())
                .whereEqReq("companyId", companyId.getId())
                .whereInReq("id", evalUserIds)
                .whereEq("rule_conf_status", 200)
                .appendWhere("rule_conf_error is null");
        return domainDao.update(up);
    }

    public String enterScoreType(Integer enterScoreType) {
        if (Objects.isNull(enterScoreType)) {
            return OperationLogSceneEnum.MANUAL_ENTER_SCORING.getScene();
        }

        if (enterScoreType == 1) {
            return OperationLogSceneEnum.AUTO_ENTER_SCORING.getScene();
        }
        if (enterScoreType == 2) {
            return OperationLogSceneEnum.INPUT_FINISHED_VALUE_ENTER_SCORING.getScene();
        }
        if (enterScoreType == 3) {//完成值审核进入评分
            return "finished_value_audit_enter_score";
        }
        if (enterScoreType == 4) {//执行进入评分阶段
            return "confirmed_enter_score";
        }

        return "";
    }

    @Override
    public int enterScoreingStage(EvalUser taskUser, EmpId opEmpId, List<EvalKpi> planItems, Integer enterScoreType) {
        final UpdateBuilder up = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                .set("taskStatus", TalentStatus.SCORING.getStatus())
                .set("task_score_start_time", new Date())
                .set("score_ranges", JSONUtil.toJsonStr(taskUser.getScoreRanges()))
                .whereEqReq("companyId", taskUser.getCompanyId().getId())
                .whereEq("id", taskUser.getId())
                .whereEq("version", taskUser.getVersion());
        int row = domainDao.update(up);
        if (row > 0 && opEmpId != null) { // taskBiz.3188行,记录操做日志
            OperationLogDo opLog = new OperationLogDo(taskUser.getCompanyId().getId(),
                    taskUser.getId(), EnterScoreTypeEnum.enterScoreType(enterScoreType),
                    opEmpId.getId(), new Date());
            domainDao.save(opLog);
        }

        /**处理经营计划指标目标值实例化入库*/
        if (CollUtil.isNotEmpty(planItems)) {
            for (EvalKpi planItem : planItems) {
                UpdateBuilder kpiUp = UpdateBuilder.build(PerfEvaluateTaskKpiDo.class)
                        .set("item_target_value", planItem.getItemTargetValue())
                        .set("updated_time", new Date())
                        .whereEqReq("companyId", planItem.getCompanyId().getId())
                        .whereEqReq("id", planItem.getId());
                domainDao.update(kpiUp);
            }
        }
        return row;
    }

    //确实属于新加的score results，请不要提前设置 id
    @Override
    public void batchSaveScoreResult(Collection<EvalScoreResult> auditTasks) {
        if (CollUtil.isEmpty(auditTasks)) {
            return;
        }
        String companyId = "";
        List<String> updateScoreResults = new ArrayList<>();
        List<PerfEvaluateTaskScoreResultDo> saveScoreResults = new ArrayList<>();
        for (EvalScoreResult auditTask : auditTasks) {
            if (auditTask.createNew()) {//新score result才要保存
                auditTask.setId(UUID.randomUUID().toString());
                PerfEvaluateTaskScoreResultDo resultDo = new ToDataBuilder<>(auditTask, PerfEvaluateTaskScoreResultDo.class).data();
                saveScoreResults.add(resultDo);
            } else {
                companyId = auditTask.getCompanyId().getId();
                PerfEvaluateTaskScoreResultDo resultDo = new ToDataBuilder<>(auditTask, PerfEvaluateTaskScoreResultDo.class).data();
                updateScoreResults.add(resultDo.getId());
            }
        }

        if (CollUtil.isNotEmpty(saveScoreResults)) {
            domainDao.saveBatch(saveScoreResults);
        }
        if (CollUtil.isNotEmpty(updateScoreResults)) {
            UpdateBuilder builder = UpdateBuilder.build(PerfEvaluateTaskScoreResultDo.class)
                    .appendSet("audit_status = null") //需要把 waitDispatch 状态的 scoreResult 设置为null，否则重置之后不会展示评分按钮
                    .set("updated_time", new Date())
                    .whereEqReq("company_id", companyId)
                    .whereInReq("id", updateScoreResults);
            this.domainDao.update(builder);
        }
    }

    @Override
    public void fixBatchUpdateScoreResult(List<EvalScoreResult> results) {
        if (CollUtil.isEmpty(results)) {
            return;
        }
        for (EvalScoreResult result : results) {
            PerfEvaluateTaskScoreResultDo resultDo = new ToDataBuilder<>(result, PerfEvaluateTaskScoreResultDo.class).data();
            resultDo.setUpdatedTime(new Date());
            this.domainDao.update(resultDo);
        }
    }

    @Override
    public void batchUpdateScoreResult(List<EvalScoreResult> results) {
        if (CollUtil.isEmpty(results)) {
            return;
        }
        for (EvalScoreResult result : results) {
            PerfEvaluateTaskScoreResultDo resultDo = new ToDataBuilder<>(result, PerfEvaluateTaskScoreResultDo.class).data();
            resultDo.setUpdatedTime(new Date());
            this.domainDao.update(resultDo);
        }
        //维度的也转交
        for (EvalScoreResult result : results) {
            PerfEvalTypeResultDo resultDo = new ToDataBuilder<>(result, PerfEvalTypeResultDo.class).data();
            resultDo.setUpdatedTime(new Date());
            this.domainDao.update(resultDo);
        }
    }

    public void batchUpdateScoreResult(List<EvalScoreResult> results, Boolean isDeleted) {
        if (CollUtil.isEmpty(results)) {
            return;
        }
        for (EvalScoreResult result : results) {
            PerfEvaluateTaskScoreResultDo resultDo = new ToDataBuilder<>(result, PerfEvaluateTaskScoreResultDo.class).data();
            resultDo.setUpdatedTime(new Date());
            resultDo.setIsDeleted(Boolean.TRUE.toString());
            this.domainDao.update(resultDo);
        }
        //维度的也转交
        for (EvalScoreResult result : results) {
            PerfEvalTypeResultDo resultDo = new ToDataBuilder<>(result, PerfEvalTypeResultDo.class).data();
            resultDo.setUpdatedTime(new Date());
            resultDo.setIsDeleted(Boolean.TRUE.toString());
            this.domainDao.update(resultDo);
        }
    }

    @Override
    public void batchFixScoreResult(List<EvalScoreResult> results) {
        if (CollUtil.isEmpty(results)) {
            return;
        }
        for (EvalScoreResult result : results) {
            UpdateBuilder up = UpdateBuilder.build(PerfEvaluateTaskScoreResultDo.class)
                    .set("final_weight_score", result.getFinalWeightScore())
                    .set("final_weight_plus_score", result.getFinalWeightPlusScore())
                    .set("final_weight_subtract_score", result.getFinalWeightSubtractScore())
                    .set("score_weight", result.getScoreWeight())
                    .whereEqReq("companyId", result.getCompanyId().getId())
                    .whereEqReq("id", result.getId());
            this.domainDao.update(up);
        }
    }


    @Override
    public List<EvalKpi> listTaskKpis(TenantId companyId, String taskUserId) {
        final ComQB comQB = ComQB.build(PerfEvaluateTaskKpiDo.class, "k")
                .whereEq("companyId", companyId.getId())
                .whereEq("taskUserId", taskUserId)
                .appendWhere("is_deleted='false'");
        return domainDao.listAllDomain(comQB, EvalKpi.class);
    }

    @Override
    public void dispatch(EvalAudit curAuditNode) {
        final UpdateBuilder up = UpdateBuilder.build(TaskAuditDo.class)
                .appendSet("status='dispatched'")
                .whereEqReq("companyId", curAuditNode.getCompanyId().getId())
                .whereEqReq("id", curAuditNode.getId());
        domainDao.update(up);
    }

    @Override
    public void saveDispatchNode(List<ScoreNode> scoreNodes, ChainDispatchRs dispatchRs) {
        List<EvalScoreResult> datas = dispatchRs.getItemResults().getDatas();
        if (CollUtil.isNotEmpty(datas)) {
            for (ScoreNode scoreNode : scoreNodes) {
                EvalScoreResult result = datas.get(0);
                final UpdateBuilder up = UpdateBuilder.build(TaskAuditDo.class)
                        .appendSet("status='dispatched'")
                        .whereEqReq("companyId", result.getCompanyId().getId())
                        .whereEqReq("scene", scoreNode.getNode().getScene())
                        .whereEqReq("taskUserId", result.getTaskUserId());
                if (!scoreNode.isAppointNode()) {
                    up.whereEqReq("approvalOrder", scoreNode.getOrder());
                }
                domainDao.update(up);
            }
        }
        //List<EvalScoreResult> results = new ArrayList<>(datas);
        //List<EvalScoreResult> adds = results.stream().filter(rs -> rs.createNew()).collect(Collectors.toList());
        //保存新加的并更新已有的
        batchSaveScoreResult(datas);
        saveDispatchTotalLevel(dispatchRs.getTotalResults());
        //results.removeAll(adds);
        //更新已有的 todo
        //for (EvalScoreResult up : datas) {
        //    UpdateBuilder upQB = UpdateBuilder.build(PerfEvaluateTaskScoreResultDo.class)
        //            .whereEqReq("company_id", result.getCompanyId().getId())
        //            .whereEqReq("task_user_id", result.getTaskUserId())
        //            .whereEqReq("id", up.getId())
        //            .set("updated_time", new Date())
        //            .appendSet("auditStatus = null");
        //    this.domainDao.update(upQB);
        //}
        //增加维度分发的结果
        batchSaveTypeScoreResult(dispatchRs.getTypeResults());

    }

    public void saveDispatchTotalLevel(List<BaseScoreResult> levelResults) {
        if (CollUtil.isEmpty(levelResults)) {
            return;
        }
        List<String> updateIds = new ArrayList<>();
        List<BaseScoreResult> saveScoreResults = new ArrayList<>();
        for (BaseScoreResult levelResult : levelResults) {
            ComQB existQ = ComQB.build(PerfEvalTotalLevelResultDo.class)
                    .whereEqReq("companyId", levelResult.getCompanyId().getId())
                    .whereEqReq("taskUserId", levelResult.getTaskUserId())
                    .whereEqReq("scorerId", levelResult.getScorerId())
                    .whereEqReq("isDeleted", "false");
            PerfEvalTotalLevelResultDo exist = domainDao.findOne(existQ);
            if (exist == null) {
                levelResult.setId(domainDao.nextLongAsStr(perfEvalTotalLevelResultSeq));
                saveScoreResults.add(levelResult);
            } else {
                updateIds.add(exist.getId());
            }
        }

        if (CollUtil.isNotEmpty(saveScoreResults)) {
            domainDao.addBatch(PerfEvalTotalLevelResultDo.class, saveScoreResults);
        }

        if (CollUtil.isNotEmpty(updateIds)) {
            UpdateBuilder builder = UpdateBuilder.build(PerfEvalTotalLevelResultDo.class)
                    .appendSet("audit_status = null") //需要把 waitDispatch 状态的 scoreResult 设置为null，否则重置之后不会展示评分按钮
                    .whereEqReq("company_id", levelResults.get(0).getCompanyId().getId())
                    .whereInReq("id", updateIds);
            this.domainDao.update(builder);
        }
    }

    //确实属于新加的score results，请不要提前设置 id
    private void batchSaveTypeScoreResult(Collection<PerfEvalTypeResult> typeResults) {
        if (CollUtil.isEmpty(typeResults)) {
            return;
        }
        String companyId = "";
        List<String> updateIds = new ArrayList<>();
        List<PerfEvalTypeResultDo> saveScoreResults = new ArrayList<>();
        for (PerfEvalTypeResult typeResult : typeResults) {
            if (typeResult.createNew()) {//新score result才要保存
                typeResult.setId(UUID.randomUUID().toString());
                PerfEvalTypeResultDo resultDo = new ToDataBuilder<>(typeResult, PerfEvalTypeResultDo.class).data();
                saveScoreResults.add(resultDo);
            } else {
                companyId = typeResult.getCompanyId().getId();
                PerfEvalTypeResultDo resultDo = new ToDataBuilder<>(typeResult, PerfEvalTypeResultDo.class).data();
                updateIds.add(resultDo.getId());
            }
        }

        if (CollUtil.isNotEmpty(saveScoreResults)) {
            domainDao.saveBatch(saveScoreResults);
        }
        if (CollUtil.isNotEmpty(updateIds)) {
            UpdateBuilder builder = UpdateBuilder.build(PerfEvalTypeResultDo.class)
                    .appendSet("audit_status = null") //需要把 waitDispatch 状态的 scoreResult 设置为null，否则重置之后不会展示评分按钮
                    .whereEqReq("company_id", companyId)
                    .whereInReq("id", updateIds);
            this.domainDao.update(builder);
        }
    }


    @Override
    public void updateLevelFlow(List<EvalAudit> evalAudits, List<EvalScoreResult> curRs, List<EvalScoreResult> nextRs) {
        if (CollUtil.isNotEmpty(evalAudits)) {
            ComQB auditQB = ComQB.build(TaskAuditDo.class)
                    .whereEqReq("companyId", evalAudits.get(0).getCompanyId().getId())
                    .whereEqReq("task_user_id", evalAudits.get(0).getTaskUserId())
                    .whereEq("scene", evalAudits.get(0).getScene())
                    .whereLowEq("approval_order", evalAudits.get(0).getApprovalOrder())
                    .whereEq("is_deleted", Boolean.FALSE.toString());
            List<EvalAudit> audits = domainDao.listAllDomain(auditQB, EvalAudit.class);
            for (EvalAudit audit : audits) {
                if (StrUtil.isBlank(audit.getStatus())) {
                    final UpdateBuilder up = UpdateBuilder.build(TaskAuditDo.class)
                            .appendSet("status='dispatched'")
                            .whereEqReq("companyId", audit.getCompanyId().getId())
                            .whereEqReq("id", audit.getId());
                    domainDao.update(up);

                    //如果层级小于当前传递来的层级，表示跳过，补发result记录
                    if (audit.getApprovalOrder() < evalAudits.get(0).getApprovalOrder()) {
                        EvalScoreResult scoreResultDo = new ToDataBuilder<>(audit, EvalScoreResult.class).data();
                        scoreResultDo.setId(null);
                        scoreResultDo.setScoreWeight(new BigDecimal(100));
                        scoreResultDo.setScorerId(audit.getApproverInfo());
                        scoreResultDo.setScorerType(audit.getScene());
                        scoreResultDo.setTaskAuditId(audit.getId());
                        //如果是会签，则只修改跳过的状态。
                        if (audit.isAndMode() && Objects.isNull(audit.getSkipType())) {
                            //存在会签时，有下节点，并且nextRs传的空，需初始化
                            if (CollUtil.isEmpty(nextRs)) {
                                nextRs = new ArrayList<>();
                            }
                            nextRs.add(scoreResultDo);
                            continue;
                        }
                        scoreResultDo.setAuditStatus("pass");
                        nextRs.add(scoreResultDo);
                    }
                }
            }
        }
        batchUpdateScoreResult(curRs);
        //如果是从result派发出来的，则不用新增result
        if (CollUtil.isNotEmpty(nextRs)) {
            ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                    .clearSelect().select("id").setRsType(String.class)
                    .whereInReq("id", nextRs.stream().map(n -> n.getId()).collect(Collectors.toList()))
                    .whereEq("is_deleted", Boolean.FALSE.toString());
            List<String> waitDispatchedRS = domainDao.listAll(comQB);
            if (CollUtil.isNotEmpty(waitDispatchedRS)) {
                batchDispacthedResults(nextRs);
                return;
            }
        }
        batchSaveScoreResult(nextRs);
    }

    @Override
    public void updateResultLevelFlow(String companyId, ResultAuditFlowNode curFlowNode, String opEmpId) {

        ComQB auditQB = ComQB.build(TaskAuditDo.class)
                .whereEqReq("companyId", companyId)
                .whereEq("task_user_id", curFlowNode.getTaskUserId())
                .whereEq("scene", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene())
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereLowEq("approval_order", curFlowNode.getApprovalOrder())
                .orderBy("approval_order");
        List<EvalAudit> audits = domainDao.listAllDomain(auditQB, EvalAudit.class);
        List<PerfEvaluateTaskScoreResultDo> saveScoreResults = new ArrayList<>();
        log.debug("updateResultLevelFlow.audits:{}", JSONUtil.toJsonStr(audits));
        // 评分结束后机会给下个校准人分发scorerResult， 提交校准的时候根据audit再次插入scorerResult，应该排查当前校准人，
        // 要不然当前校准人的scorerResult就会重复
        for (EvalAudit audit : audits) {
            if (Objects.equals(audit.getStatus(), "dispatched") && audit.nodeSkip()) {
                UpdateBuilder up = UpdateBuilder.build(PerfEvaluateTaskScoreResultDo.class)
                        .appendSet("audit_status='pass'")
                        .whereEqReq("companyId", audit.getCompanyId().getId())
                        .whereEqReq("task_user_id", curFlowNode.getTaskUserId())
                        .whereEqReq("task_audit_id", audit.getId());
                domainDao.update(up);
                continue;
            }
            UpdateBuilder up = UpdateBuilder.build(TaskAuditDo.class)
                    .appendSet("status='dispatched'")
                    .whereEqReq("companyId", audit.getCompanyId().getId())
                    .whereEqReq("id", audit.getId());
            domainDao.update(up);

            PerfEvaluateTaskScoreResultDo scoreResultDo = new ToDataBuilder<>(audit, PerfEvaluateTaskScoreResultDo.class).data();
            scoreResultDo.setId(UUID.randomUUID().toString());
//            scoreResultDo.setCompanyId(audit.getCompanyId().getId());
//            scoreResultDo.setTaskId(audit.getTaskId());
//            scoreResultDo.setEmpId(audit.getEmpId());
//            scoreResultDo.setScorerType(audit.getScene());
//            scoreResultDo.setScorerId(audit.getApproverInfo());
//            scoreResultDo.setScoreWeight(new BigDecimal(100));
//            scoreResultDo.setApprovalOrder(audit.getApprovalOrder());
//            scoreResultDo.setTaskUserId(audit.getTaskUserId());
            scoreResultDo.setScoreWeight(new BigDecimal(100));
            scoreResultDo.setScorerId(audit.getApproverInfo());
            scoreResultDo.setScorerType(audit.getScene());
            scoreResultDo.setTaskAuditId(audit.getId());
            if (!Objects.equals(audit.getApprovalOrder(), curFlowNode.getApprovalOrder()) || audit.nodeSkip()) {
                scoreResultDo.setAuditStatus("pass");
            }
//            if (StrUtil.isNotBlank(opEmpId) && StrUtil.equals(opEmpId, audit.getApproverInfo())) {
//                continue;
//            }
            saveScoreResults.add(scoreResultDo);
        }
        domainDao.saveBatch(saveScoreResults);
    }

    private void batchDispacthedResults(List<EvalScoreResult> nextRs) {
        UpdateBuilder update = UpdateBuilder.build(PerfEvaluateTaskScoreResultDo.class)
                .set("audit_status", null)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereInReq("id", nextRs.stream().map(n -> n.getId()).collect(Collectors.toList()));
        domainDao.update(update);
    }

    @Override
    public void delResultAudit(String companyId, String taskUserId) {
        UpdateBuilder updateBuilder = UpdateBuilder.build(PerfEvaluateTaskScoreResultDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", companyId)
                .whereEqReq("taskUserId", taskUserId)
                .whereEq("is_deleted", "false")
                .whereEqReq("scorer_type", "final_result_audit");
        domainDao.update(updateBuilder);

        //清除audit分发标识
        UpdateBuilder auditUp = UpdateBuilder.build(TaskAuditDo.class)
                .set("status", null)
                .whereEqReq("company_id", companyId)
                .whereEqReq("taskUserId", taskUserId)
                .whereEq("is_deleted", "false")
                .whereEqReq("scene", "final_result_audit");
        domainDao.update(auditUp);

        UpdateBuilder del = UpdateBuilder.build(ResultAuditCacheDo.class)
                .appendSet("is_deleted='true'")
                .whereEq("companyId", companyId)
                .whereEq("task_user_id", taskUserId);
        domainDao.update(del);
    }

    @Override
    public void lazyLoadKpiItem(EvalUser taskUser) {
        List<EvalKpi> kpis = listTaskKpis(taskUser.getCompanyId(), taskUser.getId());
        taskUser.setKpis(kpis);
    }

    //360与简单流程的不包含有self_score
    @Override
    public ScoreNodeFlow getScoreNodeFlow(TenantId companyId, String taskUserId, SubScoreNodeEnum sceneEnum) {
        return getScoreNodeFlow(companyId, taskUserId, sceneEnum, false);
    }

    //360与简单流程的不包含有self_score
    @Override
    public ScoreNodeFlow getScoreNodeFlow(TenantId companyId, String taskUserId, SubScoreNodeEnum sceneEnum, boolean isPassed) {
        List<EvalAudit> mainNodes;
        List<EvalScoreResult> subNodes;
        boolean isMutPeer = sceneEnum == SubScoreNodeEnum.PEER_SCORE || sceneEnum == SubScoreNodeEnum.SUB_SCORE;
        List<String> scenes = isMutPeer ? Arrays.asList(SubScoreNodeEnum.PEER_SCORE.getScene(), SubScoreNodeEnum.SUB_SCORE.getScene())
                : Arrays.asList(sceneEnum.getScene());
        mainNodes = userDao.listAuditByScenes(companyId, taskUserId, scenes);
        subNodes = userDao.listResultByScoreTypes(companyId, taskUserId, scenes);
        if (sceneEnum == SubScoreNodeEnum.SELF_SCORE) {//兼容补上自评的audit
            EvalAudit audit = new EvalAudit();
            audit.asDefualtNode(BusinessConstant.DISPATCHED, SubScoreNodeEnum.SELF_SCORE.getScene(), subNodes);
            return new ScoreNodeFlow(sceneEnum.getScene(), Arrays.asList(audit), subNodes);
        }

        Map<Integer, List<EvalScoreResult>> subOrderGroups = subNodes.stream().collect(Collectors.groupingBy(result -> result.getApprovalOrder()));
        for (EvalAudit mainNode : mainNodes) {
            List<EvalScoreResult> subNodeGroup = subOrderGroups.get(mainNode.getApprovalOrder());
            mainNode.setSubNodes(subNodeGroup);
        }
        return new ScoreNodeFlow(sceneEnum.getScene(), mainNodes, subNodes);
    }

    @Override
    public void finishedScoreNodeFlag(EvalUser evalUser) {
        UpdateBuilder up = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                .set("item_score_flag", evalUser.getItemScoreFlag())
                .set("self_score_flag", evalUser.getSelfScoreFlag())
                .set("manual_score_flag", evalUser.getManualScoreFlag())
                .set("superior_score_flag", evalUser.getSuperiorScoreFlag())
                .set("updatedTime", new Date())
                .whereEqReq("company_id", evalUser.getCompanyId().getId())
                .whereEqReq("id", evalUser.getId());
        domainDao.update(up);
    }

    @Override
    public void finishedScoreFlag(String companyId, String userId, SubScoreNodeEnum node) {
        UpdateBuilder up = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                .set("updatedTime", new Date())
                .whereEqReq("company_id", companyId)
                .whereEqReq("id", userId);
        if (node == SubScoreNodeEnum.ITEM_SCORE) {
            up.appendSet("item_score_flag='true'");
        }
        if (node == SubScoreNodeEnum.SELF_SCORE) {
            up.appendSet("self_score_flag='true'");
        }
        if (node == SubScoreNodeEnum.PEER_SCORE) {
            up.appendSet("manual_score_flag='true'");
        }
        if (node == SubScoreNodeEnum.SUPERIOR_SCORE) {
            up.appendSet("superior_score_flag='true'");
        }
        if (node == SubScoreNodeEnum.SUB_SCORE) {
            up.appendSet("manual_score_flag='true'");
        }
        domainDao.update(up);
    }

    @Override
    public List<String> delTaskUser(String companyId, String taskUserIds, String updatedEmp) {
        UpdateBuilder user = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .set("updated_user", updatedEmp)
                .whereEq("company_id", companyId)
                .whereIn("id", Arrays.asList(taskUserIds.split(",")));
        domainDao.update(user);

        ComQB task = ComQB.build(PerfEvaluateTaskUserDo.class)
                .clearSelect().select("task_id").setRsType(String.class)
                .whereEq("company_id", companyId)
                .whereIn("id", Arrays.asList(taskUserIds.split(",")))
                .groupBy("task_id");
        return domainDao.listAll(task);
    }

    @Override
    public List<EvalAudit> loadResultAudit(EvalUser taskUser) {
        return loadAudit(taskUser, EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT);
    }


    @Override
    public List<EvalAudit> loadAudit(EvalUser taskUser, EvaluateAuditSceneEnum sceneEnum) {
        List<EvalAudit> audits = userDao.listAuditByScene(taskUser.getCompanyId(), taskUser.getId(), sceneEnum.getScene());
        if (sceneEnum == EvaluateAuditSceneEnum.SUPERIOR_SCORE) {
            taskUser.setSupperScorers(audits);
        }
        if (sceneEnum == EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT) {
            taskUser.setResultAudits(audits);
        }
        if (sceneEnum == EvaluateAuditSceneEnum.PEER_SCORE) {
            taskUser.setPeerScorers(audits);
        }
        if (sceneEnum == EvaluateAuditSceneEnum.SUB_SCORE) {
            taskUser.setSubScorers(audits);
        }
        if (sceneEnum == EvaluateAuditSceneEnum.APPOINT_SCORE) {
            taskUser.setAppointScorers(audits);
        }
        if (sceneEnum == EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT) {
            taskUser.setModifyItemAudits(audits);
        }
        if (sceneEnum == EvaluateAuditSceneEnum.CHANGE_ITEM_AUDIT) {
            taskUser.setChangeItemAudits(audits);
        }
        return audits;
    }

//    @Override
//    public void loadGradeScoreRule(EvalUser evalUser) {
//        ScoreRule empScoreRule = gradeDao.getEmpScoreRule(evalUser.getCompanyId(), evalUser.getEmpId(), evalUser.getOrgId());
//        evalUser.setScoreRanges(empScoreRule.getRanges());
//    }

    @Override
    public void clearTotalLevelScoreResult(TenantId companyId, String taskUserId) {
        UpdateBuilder user = UpdateBuilder.build(PerfEvalTotalLevelResultDo.class)
                .appendSet("audit_status='wait'")
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEqReq("is_deleted", "false");
        domainDao.update(user);
    }

    @Override
    public void openTotalLevelScoreResult(TenantId companyId, String taskUserId) {
        UpdateBuilder user = UpdateBuilder.build(PerfEvalTotalLevelResultDo.class)
                .appendSet("audit_status=null")
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEqReq("is_deleted", "false");
        domainDao.update(user);
    }

    //加载员工考核领域 考核表新加,替换
    public EvalUser getEmpEval(TenantId companyId, String taskUserId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "eu")
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("id", taskUserId)
                .appendWhere("is_deleted ='false'");
        EvalUser taskUser = this.domainDao.findDomain(comQB, EvalUser.class);
        Assert.notNull(taskUser, "missed_task_user: company_id={}, task_user_id={}, emp_id={}",
                companyId.getId(), taskUserId);
        loadEmpEvalAttr(companyId, taskUser, Boolean.TRUE);
        ComQB scoreComQb = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_id", taskUser.getTaskId())
                .whereEqReq("task_user_id", taskUser.getId())
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEq("org_id", taskUser.getOrgId());
        List<EvalScoreResult> scoreResults = this.domainDao.listAllDomain(scoreComQb, EvalScoreResult.class);
        taskUser.setScoreResults(scoreResults);
        return taskUser;
    }

    private void loadEmpEvalAttr(TenantId companyId, EvalUser taskUser, Boolean loadItemRule) {
        //EmpEvalRule
        ComQB comQB = ComQB.build(EmpEvalRuleDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("emp_eval_id", taskUser.getId())
                .appendWhere("is_deleted ='false'");
        EmpEvalRule empEvalRule = domainDao.findDomain(comQB, EmpEvalRule.class);
        ComQB auditQB = ComQB.build(TaskAuditDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_user_id", taskUser.getId())
                .appendWhere("is_deleted ='false'");
        List<EvalAudit> audits = domainDao.listAllDomain(auditQB, EvalAudit.class);
        taskUser.initAudits(audits);
        taskUser.setEmpEvalRule(empEvalRule);
        loadKpis(taskUser);
        if (loadItemRule) {
            lazyLoadItemRule(taskUser);
        }
    }

    @Override
    public EvalUser getLastEmpEval(TenantId tenantId, String empId, String evaluateType, String curEvalId, String evalOrgId) {
        ComQB qb = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(EmpEvalRule.class, "r")
                .appendOn("u.company_id=r.company_id and u.id=r.emp_eval_id")
                .clearSelect().select("u.*").setRsType(PerfEvaluateTaskUserDo.class)
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereEq("r.is_deleted", Boolean.FALSE.toString())
                .whereEqReq("u.company_id", tenantId.getId());
        if (StrUtil.isNotBlank(evalOrgId)) {
            qb.whereEq("u.eval_org_id", evalOrgId);
        } else {
            qb.whereEqReq("u.emp_id", empId);
        }
        qb.whereEq("r.evaluate_type", evaluateType)
                .whereNotEqReq("u.id", curEvalId)
                //.appendWhere("u.task_status ='finished'")
                .orderByDesc("u.created_time ")
                .limit(0, 1);
        EvalUser lastEmpEval = domainDao.findDomain(qb, EvalUser.class);
        loadEmpEvalAttr(tenantId, lastEmpEval, Boolean.TRUE);
        return lastEmpEval;
    }


    @Override
    public List<MutualUser> listMutualUserValue(TenantId companyId, TaskId taskId, String taskUserId, String kpiItemId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskItemScoreRuleDo.class)
                .clearSelect().select(" mutual_user_type,mutual_user_value,peer_score_weight,sub_score_weight ")
                .setRsType(MutualUser.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_id", taskId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("is_deleted", "false")
                .whereIn("mutual_user_type", BusinessConstant.MUTUAL_USER_TYPES);
        if (StrUtil.isNotEmpty(kpiItemId)) {
            comQB.whereEq("kpi_item_id", kpiItemId);
        }
        return domainDao.listAll(comQB);
    }

    @Override
    public void deleteMutualAudit(TenantId companyId, TaskId taskId, String scene, String kpiItemId, List<String> approverInfos) {
        UpdateBuilder user = UpdateBuilder.build(TaskAuditDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_id", taskId.getId())
                .whereEqReq("kpi_item_id", kpiItemId)
                .whereEqReq("scene", scene)
                .whereIn("approver_info", approverInfos);
        domainDao.update(user);
    }

    @Override
    public void addEvalUserSummary(List<EvalScoreSummary> evalScoreSummarys, EmpId empId) {
        if (CollUtil.isEmpty(evalScoreSummarys) || Objects.isNull(empId)) {
            return;
        }
        List<EvalScoreSummary> lefts = evalScoreSummarys;
//        List<EvalScoreSummary> lefts = evalScoreSummarys.stream().filter(sm -> StrUtil.isNotBlank(sm.getSummary())).collect(Collectors.toList());
        if (CollUtil.isEmpty(lefts)) {
            return;
        }
        DeleteBuilder del = DeleteBuilder.build(EvalScoreSummaryDo.class)
                .whereEqReq("company_id", lefts.get(0).getCompanyId())
                .whereEqReq("taskUserId", lefts.get(0).getTaskUserId())
                .whereEqReq("scoreType", lefts.get(0).getScoreType())
                .whereEqReq("created_user", empId.getId());
        domainDao.delete(del);

        lefts.forEach(summary -> {
            summary.initBaseInfo(empId);
        });
        List<EvalScoreSummaryDo> summaryDos = lefts.stream().map(summary -> {
            return new ToDataBuilder<>(summary, EvalScoreSummaryDo.class).data();
        }).collect(Collectors.toList());
        domainDao.saveBatch(summaryDos);
    }

    @Override
    public void modifyScoreSummary(List<EvalScoreSummary> scoreSummarys, EmpId opEmpId, String scorerType) {
        if (CollUtil.isEmpty(scoreSummarys)) {
            return;
        }
        List<EvalScoreSummaryDo> summaryDos = scoreSummarys.stream().map(s -> {
            return new ToDataBuilder<>(s, EvalScoreSummaryDo.class).data();
        }).collect(Collectors.toList());

        summaryDos.forEach(summaryDo -> {
            UpdateBuilder update = UpdateBuilder.build(EvalScoreSummaryDo.class)
                    .setBean(summaryDo)
                    .whereEqReq("task_user_id", summaryDo.getTaskUserId())
                    .whereEqReq("created_user", opEmpId.getId())
                    .whereEqReq("score_type", scorerType);
            domainDao.update(update);
        });
    }

    public void delScoreSummary(TenantId tenantId, String taskUserId) {
        UpdateBuilder update = UpdateBuilder.build(EvalScoreSummaryDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", taskUserId);
        domainDao.update(update);
    }

    public void delScoreSummary(TenantId tenantId, TaskId taskId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .clearSelect().select("id").setRsType(String.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_id", taskId.getId())
                .whereEq("is_deleted", Boolean.FALSE.toString());
        List<String> userIds = domainDao.listAll(comQB);

        if (CollUtil.isEmpty(userIds)) {
            return;
        }

        UpdateBuilder update = UpdateBuilder.build(EvalScoreSummaryDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", tenantId.getId())
                .whereIn("task_user_id", userIds);
        domainDao.update(update);
    }

    @Override
    public ScoreStageRule buildScoreStageRule(TenantId tenantId, String empEvalId, AdminTask adminTask) {
        ScoreStageRule rule = new ScoreStageRule();
        ScoreRuleValue scoreRule = adminTask.getScoreRuleValue();
        rule.setTypeWeightConf(adminTask.getTypeWeightConf());
        rule.setScoreValueConf(adminTask.getScoreValueConf());
        rule.setEmpEvalId(empEvalId);
        rule.setEvaluateType(adminTask.getEvaluateType());

        List<EvalAudit> audits = getEvalAudits(tenantId, empEvalId);
        Map<String, List<EvalAudit>> auditMap = audits.stream().collect(Collectors.groupingBy(audit -> audit.getScene()));
        List<EmpEvalKpiType> kpiTypes = kpiDao.listEmpEvalKpiTypeV3(tenantId, empEvalId).getDatas();
        ListWrap<EvalKpi> kpiItemWrap = kpiDao.listEmpEvalKpiItem(tenantId, empEvalId);
        ListWrap<EvalItemScoreRule> itemRuleWrap = kpiDao.listEmpEvalItemRule(tenantId, empEvalId).asMap(r -> r.getKpiItemId());
        List<EvalItemScoreRule> itemRules = itemRuleWrap.getDatas();
        if (adminTask.isCustom()) {
            getCustomRaters(itemRules, scoreRule, audits);
            kpiItemWrap.getDatas().forEach(kpi -> {
                EvalItemScoreRule itemScoreRule = itemRuleWrap.mapGet(kpi.getKpiItemId());
                if (itemScoreRule != null) {
                    kpi.setItemScoreRule(itemScoreRule);
                }
            });
        }
        kpiTypes.forEach(type -> {
            List<EvalKpi> items = kpiItemWrap.groupGet(type.getKpiTypeId());
            type.setItems(items);
        });
        rule.setIndicatorCnt(kpiItemWrap.getDatas().size());
        rule.setKpiTypes(kpiTypes);
        if (adminTask.isCustom()) {
            return rule;
        }
        rule.setS3SelfRater(getSelfRater(scoreRule));
        rule.setS3PeerRater(getMutualRaters(scoreRule, auditMap, AuditEnum.PEER_SCORE.getScene()));
        rule.setS3SubRater(getMutualRaters(scoreRule, auditMap, AuditEnum.SUB_SCORE.getScene()));
        rule.setS3SuperRater(getSupRaters(scoreRule, auditMap));
        return rule;
    }

    @Override
    public List<EvalAudit> listEvalAuditsByScene(TenantId tenantId, String empEvalId, String scene) {
        ComQB comQB = ComQB.build(TaskAuditDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", empEvalId)
                .whereEqReq("scene", scene)
                .whereEqReq("is_deleted", Boolean.FALSE.toString())
                .orderByAsc("approval_order");
        List<EvalAudit> audits = domainDao.listAllDomain(comQB, EvalAudit.class);
        return audits;
    }

    private List<EvalAudit> getEvalAudits(TenantId tenantId, String empEvalId) {
        ComQB comQB = ComQB.build(TaskAuditDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", empEvalId)
                .whereIn("scene", AuditEnum.scoreScenes())
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        List<EvalAudit> audits = domainDao.listAllDomain(comQB, EvalAudit.class);
        return audits;
    }


    //TODO

    public void getCustomRaters(List<EvalItemScoreRule> itemRules, ScoreRuleValue scoreRuleValue, List<EvalAudit> audits) {
        if (CollUtil.isEmpty(itemRules)) {
            return;
        }
        for (EvalItemScoreRule itemRule : itemRules) {
            List<EvalAudit> itemAudits = audits.stream().filter(a -> itemRule.getKpiItemId().equals(a.getKpiItemId())).collect(Collectors.toList());
            if (CollUtil.isEmpty(itemAudits)) {
                continue;
            }
            Map<String, List<EvalAudit>> sceneMap = itemAudits.stream().collect(Collectors.groupingBy(a -> a.getScene()));

            for (String scene : sceneMap.keySet()) {
                if (AuditEnum.SELF_SCORE.getScene().equals(scene)) {
                    itemRule.setSelfRater(getSelfRater(scoreRuleValue));
                    continue;
                }
                if (AuditEnum.PEER_SCORE.getScene().equals(scene)) {
                    itemRule.setPeerRater(getMutualRaters(scoreRuleValue, sceneMap, AuditEnum.PEER_SCORE.getScene()));
                    continue;
                }
                if (AuditEnum.SUB_SCORE.getScene().equals(scene)) {
                    itemRule.setSubRater(getMutualRaters(scoreRuleValue, sceneMap, AuditEnum.SUB_SCORE.getScene()));
                    continue;
                }
                if (AuditEnum.APPOINT_SCORE.getScene().equals(scene)) {
                    S3RaterBaseConf appointRaters = getAppointRaters(scoreRuleValue, sceneMap);
                    itemRule.setAppointRater(appointRaters);
                    continue;
                }
                if (AuditEnum.SUPERIOR_SCORE.getScene().equals(scene)) {
                    S3SuperRaterConf supRaters = getSupRaters(scoreRuleValue, sceneMap);
                    itemRule.setSuperRater(supRaters);
                }
            }
        }
    }

    public RaterNodeConf getSelfRater(ScoreRuleValue scoreRule) {
        return new RaterNodeConf(scoreRule.getSelfScoreFlag(),
                scoreRule.getSelfScoreRule(), scoreRule.getSelfScoreWeight(), false);
    }

    public MutualNodeConf getMutualRaters(ScoreRuleValue scoreRule, Map<String, List<EvalAudit>> auditMap, String scene) {
        MutualNodeConf peerConf = new MutualNodeConf(scoreRule.getMutualScoreFlag(),
                scoreRule.getMutualScoreRule(), Boolean.valueOf(scoreRule.getMutualScoreAnonymous()));
        peerConf.setNodeWeight(scoreRule.getPeerScoreWeight());
        List<BaseAuditNode> nodes = getAuditNodes(auditMap.get(scene));
        if (CollUtil.isEmpty(nodes)) {
            peerConf.setOpen(0);
            return peerConf;
        }
        BeanUtils.copyProperties(nodes.get(0), peerConf);
        peerConf.setNode(scene);
        if (nodes.size() > 1) {
            for (int i = 1; i < nodes.size(); i++) {
                peerConf.getRaters().addAll(nodes.get(i).getRaters());
            }
        }
        return peerConf;
    }

    public S3SuperRaterConf getSupRaters(ScoreRuleValue scoreRule, Map<String, List<EvalAudit>> auditMap) {
        S3SuperRaterConf s3Super = new S3SuperRaterConf(scoreRule.getSuperiorScoreFlag(),
                scoreRule.getSuperiorScoreRule(),
                scoreRule.getSuperiorScoreWeight(),
                Boolean.valueOf(scoreRule.getSuperiorScoreAnonymous()),
                scoreRule.getSuperiorScoreOrder());
        List<BaseAuditNode> nodes = getAuditNodes(auditMap.get(AuditEnum.SUPERIOR_SCORE.getScene()));
        s3Super.addAllNode(nodes);
        return s3Super;
    }

    public S3RaterBaseConf getAppointRaters(ScoreRuleValue scoreRule, Map<String, List<EvalAudit>> auditMap) {
        S3RaterBaseConf appointRater = new S3RaterBaseConf("true", "item", new BigDecimal(100),
                Boolean.valueOf(scoreRule.getAppointScoreAnonymous()));
        List<BaseAuditNode> nodes = getAuditNodes(auditMap.get(AuditEnum.APPOINT_SCORE.getScene()));
        appointRater.addAllNode(nodes);
        return appointRater;
    }

    public List<BaseAuditNode> getAuditNodes(List<EvalAudit> evalAudits) {
        if (CollUtil.isEmpty(evalAudits)) {
            return new ArrayList<>();
        }
        List<BaseAuditNode> nodes = evalAudits.stream().map(audit -> {
            List<Rater> raters = audit.parseRaters();
            BaseAuditNode node = new BaseAuditNode(audit.getApprovalOrder(), audit.getMultipleReviewersType());
            node.setWeight(audit.getWeight());
            node.allowOperation(audit.getTransferFlag(), audit.getModifyFlag());
            node.addAllRater(raters);
            return node;
        }).collect(Collectors.toList());
        return nodes;
    }

    public List<EvalUser> listTONotConfirm() {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .whereEq("task_status", TalentStatus.CONFIRMING.getStatus())
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereNotEq("task_status", TalentStatus.TERMINATED.getStatus())
                .appendWhere(" confirm_dead_line is not null  and confirm_dead_line< '" +
                        new DateTime().toString("yyyy-MM-dd") + "' ");
        return domainDao.listAllDomain(comQB, EvalUser.class);
    }

    public List<EvalUser> listNotConfirmEvalUser(String companyId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .whereEq("company_id", companyId)
                .whereEq("task_status", TalentStatus.CONFIRMING.getStatus())
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .appendWhere(" (confirm_dead_line is not null and confirm_dead_line != '') and confirm_dead_line< '" +
                        new DateTime().toString("yyyy-MM-dd") + "' ");
        List<EvalUser> evalUsers = domainDao.listAllDomain(comQB, EvalUser.class);
        if (CollUtil.isEmpty(evalUsers)) {
            return new ArrayList<>();
        }
        for (EvalUser evalUser : evalUsers) {
            ComQB rule = ComQB.build(EmpEvalRuleDo.class)
                    .whereEq("company_id", companyId)
                    .whereEqReq("emp_eval_id", evalUser.getId())
                    .whereEqReq("is_deleted", "false");
            EmpEvalRule empEvalRule = domainDao.findDomain(rule, EmpEvalRule.class);
            evalUser.setEmpEvalRule(empEvalRule);
            loadKpis(evalUser);
        }
        //过滤出没有截止日期设置
        evalUsers = evalUsers.stream().filter(e -> Objects.nonNull(e.getEmpEvalRule()))
                .filter(e -> Objects.isNull(e.getEmpEvalRule().getDeadLineConf())
                        || !e.getEmpEvalRule().getDeadLineConf().isOpen()).collect(Collectors.toList());
        return evalUsers;
    }

    @Override
    public void batchInitScoreRanges(List<EvalUser> userList) {
        if (CollUtil.isNotEmpty(userList)) {
            for (EvalUser evalUser : userList) {
                UpdateBuilder user = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                        .set("score_ranges", JSONUtil.toJsonStr(evalUser.getScoreRanges()))
                        .whereEqReq(" id ", evalUser.getId())
                        .whereEqReq("company_id", evalUser.getCompanyId().getId());
                domainDao.update(user);
            }
        }
    }

    @Override
    public List<String> listRefAskEvalIds(TenantId tenantId, List<String> taskUserIds) {
        ComQB comQB = ComQB.build(PerfEvalRefAskDo.class)
                .clearSelect().select("DISTINCT ask360_eval_id").setRsType(String.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereInReq("task_user_id", taskUserIds)
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        return domainDao.listAll(comQB);
    }

    @Override
    public String getAskRefTaskUserId(TenantId tenantId, String ask360EvalId) {
        ComQB comQB = ComQB.build(PerfEvalRefAskDo.class)
                .clearSelect().select("DISTINCT task_user_id").setRsType(String.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("ask360_eval_id", ask360EvalId)
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        return domainDao.findOne(comQB);
    }

    @Override
    public void delPerfEvalRefAskEval(TenantId tenantId, List<String> taskUserIds) {
        UpdateBuilder ref = UpdateBuilder.build(PerfEvalRefAskDo.class)
                .set(" is_deleted ", Boolean.TRUE.toString())
                .whereEqReq("company_id", tenantId.getId())
                .whereInReq("task_user_id", taskUserIds)
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        domainDao.update(ref);
    }

    @Override
    public void delPerfEvalRefAskEval(TenantId tenantId, String taskUserId, List<String> ask360EvalIds) {
        UpdateBuilder ref = UpdateBuilder.build(PerfEvalRefAskDo.class)
                .set(" is_deleted ", Boolean.TRUE.toString())
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereInReq("ask360_eval_id", ask360EvalIds)
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        domainDao.update(ref);
    }

    @Override
    public void addRefAskEval(String companyId, String taskUserId, String ask360EvalId, String opEmpId) {
        PerfEvalRefAskDo askDo = new PerfEvalRefAskDo();
        askDo.accop(companyId, taskUserId, ask360EvalId, opEmpId);
        domainDao.save(askDo);
    }

    @Override
    public void upFinalResultAuditOrder(String companyId, String taskUserId, String taskAuditId, Integer approvalOrder) {
        UpdateBuilder result = UpdateBuilder.build(PerfEvaluateTaskScoreResultDo.class)
                .set(" approval_order ", approvalOrder)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEqReq("task_audit_id", taskAuditId);
        domainDao.update(result);
    }

    @Override
    public List<ScoreRule> loadScoreRule(String companyId, List<String> scoreRuleIds) {
        return gradeDao.listScoreRule(companyId, scoreRuleIds);
    }

    @Override
    public List<KpiEmp> listByEmp(TenantId tenantId, Collection<String> empIds) {
        if (CollUtil.isEmpty(empIds)) {
            return Collections.emptyList();
        }
        final ComQB comQB = ComQB.build(EmployeeBaseInfoDo.class, "e")
                .clearSelect().setRsType(KpiEmp.class)
                .select("status, employee_id  as emp_id,  name as emp_name, ding_user_id ex_user_id , avatar,jobnumber")
                .whereEq("companyId", tenantId.getId())
                .whereIn("employeeId", empIds);
        return domainDao.listAll(comQB);
    }

    @Override
    public LevelAuditFlow reloadKpiItemsAuditFlow(TenantId companyId, String taskUserId, EvaluateAuditSceneEnum modifyItemAudit, List<String> rejectKpiItems) {
        List<EvalAudit> mainAudits = userDao.listAuditByScene(companyId, taskUserId, modifyItemAudit.getScene());
        // 加载指标审批人和任务上的审批人
        List<EvalAudit> allAudits = mainAudits.stream().filter(audit -> rejectKpiItems.contains(audit.getKpiItemId()) || StringUtils.isEmpty(audit.getKpiItemId())).collect(Collectors.toList());
        List<EvalScoreResult> rss = userDao.listResultByScoreType(companyId, taskUserId, modifyItemAudit.getScene());
        List<EvalScoreResult> waitRss = userDao.listWaitDispatchResult(companyId, taskUserId, modifyItemAudit.getScene());
        ListWrap<EvalScoreResult> rsGroup = new ListWrap<>(rss).groupBy(rs -> rs.getTaskAuditId());
        ListWrap<EvalScoreResult> waitRsGroup = new ListWrap<>(waitRss).groupBy(rs -> rs.getTaskAuditId());
        for (EvalAudit audit : allAudits) {
            audit.setSubNodes(rsGroup.groupGet(audit.getId()));
            audit.setWaitSubNodes(waitRsGroup.groupGet(audit.getId()));
        }
        ListWrap<EvalAudit> allSubNodes = new ListWrap<>(allAudits).groupBy(evalAudit -> evalAudit.getApprovalOrder() + "");
        LevelAuditFlow flowRs = new LevelAuditFlow(taskUserId, allSubNodes);
        return flowRs;
    }

    @Override
    public void updateInputFinishStatus(Integer status, String taskUserId, TenantId companyId) {
        UpdateBuilder user = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                .set(" input_finish_status ", status)
                .whereEqReq(" id ", taskUserId)
                .whereEqReq("company_id", companyId.getId());
        domainDao.update(user);
    }

    @Override
    public void fixUpdateTaskUser(EvalUser taskUser) {
        UpdateBuilder user = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                .set(" input_finish_status ", taskUser.getInputFinishStatus())
                .whereEqReq(" id ", taskUser.getId())
                .whereEqReq("company_id", taskUser.getCompanyId().getId());
        domainDao.update(user);
    }

    @Override
    public void fixInputFinishStatus(List<String> taskUserIds) {
        List<TaskFixFinishStatusDo> taskFixFinishStatusDos = new ArrayList<>();
        taskUserIds.stream().forEach(taskUserId -> {
            TaskFixFinishStatusDo taskFixFinishStatusDo = new TaskFixFinishStatusDo();
            taskFixFinishStatusDo.setTaskUserId(taskUserId);
            taskFixFinishStatusDos.add(taskFixFinishStatusDo);
        });
        domainDao.saveBatch(taskFixFinishStatusDos);
    }

//    @Override
//    public void autoStopNotConfirm() {
//        List<EvalUser> users = listTONotConfirm();
//        if (CollUtil.isEmpty(users)) {
//            LoggerFactory.getLogger(getClass()).info("没有符合确认超时自动终止的员工任务");
//            return;
//        }
//        List<OperationLogDo> logDos = new ArrayList<>();
//        for (EvalUser user : users) {
//            user.setFinalScore(BigDecimal.ZERO);
//            user.matchGrade();
//            OperationLogDo logDo = new OperationLogDo(user.getCompanyId().getId(), user.getId(),
//                    OperationLogSceneEnum.CONFIRM_TIME_OUT_AUTO_TERMINATE.getScene(), "-1", new Date());
//            logDos.add(logDo);
//            domainDao.update(PerfEvaluateTaskUserDo.class, user);
//        }
//        UpdateBuilder update = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
//                .set("task_status", TalentStatus.TERMINATED.getStatus())
//                .whereEq("task_status", TalentStatus.CONFIRMING.getStatus())
//                .whereEq("is_deleted", Boolean.FALSE.toString())
//                .whereNotEq("task_status", TalentStatus.TERMINATED.getStatus())
//                .appendWhere(" confirm_dead_line is not null  and confirm_dead_line< '" +
//                        new DateTime().toString("yyyy-MM-dd") + "' ");
//
//        domainDao.saveBatch(logDos);
//        domainDao.update(update);
//    }

    @Override
    public ScoreStageRule getScoreStageRule(TenantId tenantId, String taskUserId, EvalUser evalUser) {
        //EmpEvalRule
        ComQB comQB = ComQB.build(EmpEvalRuleDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("emp_eval_id", taskUserId)
                .appendWhere("is_deleted ='false'");
        EmpEvalRule empEvalRule = domainDao.findDomain(comQB, EmpEvalRule.class);

        ComQB kpiTypeQB = ComQB.build(EmpEvalKpiTypeDo.class)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .orderBy("type_order");
        List<EmpEvalKpiType> kpiTypes = domainDao.listAllDomain(kpiTypeQB, EmpEvalKpiType.class);

        /**查询维度配置*/
        List<KpiTypeUsedField> usedFieldDos = getTypeUsedFieldDoList(tenantId, taskUserId);

        /**查询指标自定义字段配置*/
        List<KpiItemUsedField> itemUsedFieldDos = getItemUsedFieldDoList(tenantId, taskUserId);

        List<String> kpiTypeIds = kpiTypes.stream().filter(kpiType -> kpiType.isOkr()).map(type -> type.getKpiTypeId()).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(kpiTypeIds)) {
            ComQB okrTypeQ = ComQB.build(PerfEvaluateTaskOkrTypeDo.class)
                    .whereEq("is_deleted", Boolean.FALSE.toString())
                    .whereEqReq("company_id", tenantId.getId())
                    .whereEqReq("task_user_id", taskUserId);
            List<PerfEvaluateTaskOkrTypeDo> okrTypeDos = domainDao.listAll(okrTypeQ);
            ListWrap<PerfEvaluateTaskOkrTypeDo> typeWraps = new ListWrap<>(okrTypeDos).groupBy(type -> type.getKpiTypeId());
            for (EmpEvalKpiType kpiType : kpiTypes) {
                List<PerfEvaluateTaskOkrTypeDo> okrTypes = typeWraps.getGroups().get(kpiType.getKpiTypeId());
                if (CollUtil.isEmpty(okrTypes)) {
                    continue;
                }
                kpiType.setOpenOkrScore(okrTypes.get(0).getOpenOkrScore());
            }
        }

        //kpi
        ComQB kpiItemQB = ComQB.build(PerfEvaluateTaskKpiDo.class)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", taskUserId);
        List<EvalKpi> evalKpis = domainDao.listAllDomain(kpiItemQB, EvalKpi.class);
        InnerGroup<String, EvalKpi> kpiGroup = new InnerGroup<>();
        InnerFields<String> resultInputEmpIds = new InnerFields<>();//用来查询完成值录入人的名字
        for (EvalKpi evalKpi : evalKpis) {
            kpiGroup.put(evalKpi.getKpiTypeId(), evalKpi);
            resultInputEmpIds.appendStr(evalKpi.getResultInputEmpId(), ",");
        }
        for (EmpEvalKpiType kpiType : kpiTypes) {
            kpiGroup.get(kpiType.getKpiTypeId(), 0).ifPresent(x -> kpiType.setItemLimitCnt(x.getItemLimitCnt()));
        }

        List<String> kpiItemIds = CollUtil.map(evalKpis, k -> k.getKpiItemId(), true);
        //回填指标所属分类
        if (CollUtil.isNotEmpty(kpiItemIds)) {
            ComQB categoryQB = ComQB.build(CompanyKpiItemDo.class, "c")
                    .leftJoin(CompanyCategoryModel.class, "m")
                    .appendOn("c.company_id = m.company_id and c.category_id = m.id and m.is_deleted = 'false'")
                    .clearSelect().select("c.id as kpiItemId, c.category_id as categoryId,m.category_name as categoryName")
                    .setRsType(EvalItemPo.class)
                    .whereEq("c.is_deleted", Boolean.FALSE.toString())
                    .whereEqReq("c.company_id", tenantId.getId())
                    .whereIn("c.id", kpiItemIds);
            List<EvalItemPo> items = domainDao.listAll(categoryQB);
            if (CollUtil.isNotEmpty(items)) {
                Map<String, EvalItemPo> kpiMap = CollUtil.toMap(items, new HashMap<>(), k -> k.getKpiItemId());
                for (EvalKpi evalKpi : evalKpis) {
                    EvalItemPo category = kpiMap.get(evalKpi.getKpiItemId());
                    if (Objects.nonNull(category)) {
                        evalKpi.accCategory(category.getCategoryId(), category.getCategoryName());
                    }
                    evalKpi.initFinishValueSource();
                }
            }
        }

        //回填完成值录入人的名字, !!!增加一个完成值录入人名字的字段可以省掉这段回填代码
        if (resultInputEmpIds.isNotEmpty()) {
            //查询完成值录入人的名字
            List<String> empIds = resultInputEmpIds.unique();
            ComQB empQb = ComQB.build(EmployeeBaseInfoDo.class)
                    .clearSelect()
                    .select("employee_id as empId, name as empName")
                    .setRsType(KpiEmp.class)
                    .whereEqReq("company_id", tenantId.getId())
                    .whereEqReq("is_delete", "false")
                    .whereInReq("employee_id", empIds);
            List<KpiEmp> kpiEmps = this.domainDao.listAll(empQb);
            if (CollUtil.isNotEmpty(kpiEmps)) {
                Map<String, String> kpiEmpIdNameMap = kpiEmps.stream().collect(Collectors.toMap(KpiEmp::getEmpId, KpiEmp::getEmpName));
                for (EvalKpi evalKpi : evalKpis) {
                    InnerFields<String> kpiResultInputEmpIds = new InnerFields<>();//用来查询完成值录入人的名字
                    kpiResultInputEmpIds.appendStr(evalKpi.getResultInputEmpId(), ",");
                    if (kpiResultInputEmpIds.isEmpty()) {
                        continue;
                    }
                    InnerFields<String> kpiResultInputEmpNames = new InnerFields<>();
                    for (String field : kpiResultInputEmpIds.getFields()) {
                        kpiResultInputEmpNames.add(kpiEmpIdNameMap.get(field));
                    }
                    evalKpi.setResultInputEmpName(kpiResultInputEmpNames.join(","));
                }
            }
        }

        List<EvalItemScoreRule> itemRules = new ArrayList<>();
        if (empEvalRule.isCustom()) {
            //item_rule
            ComQB itemRuleQB = ComQB.build(PerfEvaluateTaskItemScoreRuleDo.class)
                    .whereEq("is_deleted", Boolean.FALSE.toString())
                    .whereEqReq("company_id", tenantId.getId())
                    .whereEqReq("task_user_id", taskUserId);
            itemRules.addAll(domainDao.listAllDomain(itemRuleQB, EvalItemScoreRule.class));
        }

        Map<String, List<EvalItemScoreRule>> itemRuleMap = itemRules.stream().filter(f -> StrUtil.isNotEmpty(f.getKpiItemId())).collect(Collectors.groupingBy(f -> f.getKpiItemId()));

        //kpi自动计算的公式
        ComQB itemFormulaFiledQB = ComQB.build(PerfEvaluateTaskFormulaFieldDo.class)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", taskUserId);
        List<EvalFormulaField> itemFormulaFields = domainDao.listAllDomain(itemFormulaFiledQB, EvalFormulaField.class);
        Map<String, List<EvalFormulaField>> itemFieldMap = itemFormulaFields.stream().collect(Collectors.groupingBy(f -> f.getKpiItemId()));
        evalKpis.forEach(item -> {
            if (Objects.nonNull(itemFieldMap)) {
                List<EvalFormulaField> fields = itemFieldMap.get(item.getKpiItemId());
                item.setFormulaFields(fields);
            }
            if (Objects.nonNull(itemRuleMap)) {
                List<EvalItemScoreRule> evalItemScoreRules = itemRuleMap.get(item.getKpiItemId());
                if (CollUtil.isNotEmpty(evalItemScoreRules)) {
                    item.setItemScoreRule(evalItemScoreRules.get(0));
                }
            }
        });

        Map<String, List<EvalKpi>> itemTypeMap = evalKpis.stream().collect(Collectors.groupingBy(kpi -> kpi.getKpiTypeId()));

        List<EvalItemScoreRule> typeRuleList = itemRules.stream().filter(scoreRule -> scoreRule.isTypeRule()).collect(Collectors.toList());
        Audit3TypeRuleBd typeRuleBd = new Audit3TypeRuleBd(kpiTypes);
        ListWrap<EvalItemScoreRule> typeRules = typeRuleBd.build2();
        ListWrap<EvalItemScoreRule> preTypeRues = !typeRules.isEmpty() ? typeRules : new ListWrap<>(typeRuleList).asMap(EvalItemScoreRule::getKpiTypeId);

        kpiTypes.forEach(type -> {
            List<EvalKpi> kpis = itemTypeMap.get(type.getKpiTypeId());
            EvalItemScoreRule typeRule = preTypeRues.mapGet(type.getKpiTypeId());
            type.setTypeRule(typeRule);
            type.setItems(kpis == null ? Collections.emptyList() : kpis);
            /**组装指标自定义字段*/
            type.initKpiItemUsedFields(itemUsedFieldDos);
        });
        empEvalRule.setIndicatorCnt(evalKpis.size());
        empEvalRule.setKpiTypes(new KpiListWrap(kpiTypes));
        empEvalRule.initKpiTypeUsedFields(usedFieldDos);
        ScoreStageRule stageRule = Convert.convert(ScoreStageRule.class, empEvalRule);
        stageRule.setRuleConfStatus(evalUser.getRuleConfStatus());
        stageRule.setRuleConfError(evalUser.getRuleConfError());
        stageRule.setKpiTypes(empEvalRule.getKpiTypes().getDatas());
        return stageRule;
    }

    /**
     * 查询纬度字段字定义配置
     */
    public List<KpiTypeUsedField> getTypeUsedFieldDoList(TenantId tenantId, String taskUserId) {
        ComQB used = ComQB.build(EmpEvalTypeUsedFieldDo.class, "tu")
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .appendWhere("is_deleted='false'")
                .orderByAsc("sort");
        return domainDao.listAllDomain(used, KpiTypeUsedField.class);
    }

    /**
     * 查询指标字段字定义配置
     */
    public List<KpiItemUsedField> getItemUsedFieldDoList(TenantId tenantId, String taskUserId) {
        ComQB itemUsed = ComQB.build(PerfEvaluateItemUsedFieldDo.class, "tu")
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .appendWhere("is_deleted='false'")
                .orderByAsc("sort");
        return domainDao.listAllDomain(itemUsed, KpiItemUsedField.class);
    }

    @Override
    public List<EvalAudit> getCustomAudits(TenantId tenantId, String taskId, String empId) {
        ComQB comQB = ComQB.build(TaskAuditDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_id", taskId)
                .whereEqReq("emp_id", empId)
                .whereIn("scene", AuditEnum.scoreScenes())
                .whereEq("is_deleted", Boolean.FALSE.toString());
        return domainDao.listAllDomain(comQB, EvalAudit.class);
    }

    @Override
    public EmpEvalRuleFlow getEmpEvalRuleFlow(TenantId tenantId, String taskUserId, EvalUser evalUser) {
        ComQB comQB = ComQB.build(EmpEvalRuleDo.class)
                .whereEqReq("emp_eval_id", taskUserId)
                .whereEqReq("company_id", tenantId.getId())
                .whereEq("is_deleted", Boolean.FALSE.toString());
        EmpEvalRule domain = domainDao.findDomain(comQB, EmpEvalRule.class);

        EmpEvalRuleFlow convert = Convert.convert(EmpEvalRuleFlow.class, domain);
        convert.setRuleConfError(evalUser.getRuleConfError());
        convert.setRuleConfStatus(evalUser.getRuleConfStatus());
        if (Objects.nonNull(convert.getDeadLineConf())) {
            convert.getDeadLineConf().initTaskResultInterview();
        }
        return convert;
    }

    @Override
    public void markNonePublish(String companyId, List<String> taskUserIds) {
        UpdateBuilder update = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                .appendSet("is_publish='false'")
                .whereEqReq("companyId", companyId)
                .whereInReq("id", taskUserIds);
        domainDao.update(update);
    }

    @Override
    public void markPublish(String companyId, List<String> taskUserIds) {
        UpdateBuilder update = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                .appendSet("is_publish='true'")
                .whereInReq("companyId", taskUserIds)
                .whereInReq("id", taskUserIds);
        domainDao.update(update);
    }


    public void saveConfirmItem(EvalUser evalUser, PerfModificationRecord record, EmpId opEmpId) {
        if (CollUtil.isEmpty(evalUser.getKpiTypes())) {
            return;
        }
        TenantId tenantId = evalUser.getCompanyId();
        String evalUserId = evalUser.getId();
        List<EmpEvalKpiType> kpiTypes = evalUser.getKpiTypes();
        //List<EmpEvalKpiType> evalKpis = kpiTypes.stream().filter(obj -> obj.getItems() != null && obj.getItems().size() > 0).collect(Collectors.toList());
        boolean empEvalRuleIsNull = Objects.isNull(evalUser.getEmpEvalRule());
        delEmpEvalKpi(tenantId, evalUserId, kpiTypes, empEvalRuleIsNull);
        //OperationLogDo operationLogDo = new OperationLogDo(tenantId.getId(), evalUserId, OperationLogSceneEnum.AUDIT_ITEM.getScene(), opEmpId.getId(), new Date());
        //opLogDao.addLog(operationLogDo);
        //删除缓存
        deleteCache(tenantId, evalUserId, opEmpId.getId(), AuditEnum.CONFIRM_TASK.getScene());
        saveEmpEvalKpi(empEvalRuleIsNull, evalUser, kpiTypes, evalUser.getEmpEvalRule());
        domainDao.add(PerfModificationRecordDo.class, record);
    }



    public void updateKpiItem(EvalUser evalUser, PerfModificationRecord record, EmpId opEmpId) {
        if (CollUtil.isEmpty(evalUser.getKpiTypes())) {
            return;
        }
        TenantId tenantId = evalUser.getCompanyId();
        String evalUserId = evalUser.getId();
        List<EmpEvalKpiType> kpiTypes = evalUser.getKpiTypes();
        /**清除okr指标项*/
        kpiTypes.forEach(obj -> {
            obj.getItems().removeIf(filter -> "true".equals(obj.getIsOkr()));
        });
        //List<EmpEvalKpiType> evalKpis = kpiTypes.stream().filter(obj -> obj.getItems() != null && obj.getItems().size() > 0).collect(Collectors.toList());
        boolean empEvalRuleIsNull = Objects.isNull(evalUser.getEmpEvalRule());
        delEmpEvalKpi(tenantId, evalUserId, kpiTypes, empEvalRuleIsNull);
        //OperationLogDo operationLogDo = new OperationLogDo(tenantId.getId(), evalUserId, OperationLogSceneEnum.AUDIT_ITEM.getScene(), opEmpId.getId(), new Date());
        //opLogDao.addLog(operationLogDo);
        //删除缓存
        deleteCache(tenantId, evalUserId, opEmpId.getId(), AuditEnum.CONFIRM_TASK.getScene());
        saveEmpEvalKpi(empEvalRuleIsNull, evalUser, kpiTypes, evalUser.getEmpEvalRule());
        domainDao.add(PerfModificationRecordDo.class, record);
    }

    public void deleteCache(TenantId tenantId, String evalUserId, String opEmpId, String scene) {
        UpdateBuilder updateBuilder = UpdateBuilder.build("company_cache_info")
                .set("is_deleted", "true")
                .set("updated_user", opEmpId)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("link_id", evalUserId)
                .whereEq("business_scene", scene)
                .whereEq("is_deleted", "false");
        domainDao.update(updateBuilder);
    }

    public void passConfirmResult(TenantId tenantId, String evalUserId, String opEmpId, OperationLogSceneEnum sceneEnum) {
        UpdateBuilder result = UpdateBuilder.build(PerfEvaluateTaskScoreResultDo.class)
                .set("audit_status", "pass")
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", evalUserId)
                .whereEq("scorer_type", EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT.getScene())
                .whereEqReq("scorer_id", opEmpId);
        domainDao.update(result);
        OperationLogDo operationLogDo = new OperationLogDo(tenantId.getId(), evalUserId, sceneEnum.getScene(),
                opEmpId, new Date());
        domainDao.save(operationLogDo);
    }

    public void passAllConfirmResult(TenantId tenantId, String evalUserId, String opEmpId, OperationLogSceneEnum sceneEnum) {
        UpdateBuilder result = UpdateBuilder.build(PerfEvaluateTaskScoreResultDo.class)
                .set("audit_status", "pass")
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", evalUserId)
                .whereEq("scorer_type", EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT.getScene());
        domainDao.update(result);
        OperationLogDo operationLogDo = new OperationLogDo(tenantId.getId(), evalUserId, sceneEnum.getScene(),
                opEmpId, new Date());
        domainDao.save(operationLogDo);
    }

    public void saveEmpEvalKpi(Boolean empEvalRuleIsNull, EvalUser empEval, List<EmpEvalKpiType> kpiTypes, EmpEvalRule empEvalRule) {
        if (empEvalRuleIsNull) {
            saveEmpEvalKpi(kpiTypes, empEvalRule);//如果不存在规则，则直接保存kpiTypes
            return;
        }
        saveEmpEvalKpi(empEval);
    }

    private void saveEmpEvalKpi(List<EmpEvalKpiType> kpiTypes, EmpEvalRule empEvalRule) {
        List<EvalKpi> items = new ArrayList<>();
        kpiTypes = kpiTypes.stream().filter(type -> type.getIsDeleted().equals(Boolean.FALSE.toString()))
                .collect(Collectors.toList());
        for (EmpEvalKpiType kpiType : kpiTypes) {
            items.addAll(kpiType.getItems().stream().filter(item -> item.getIsDeleted().equals("false")).collect(Collectors.toList()));
        }
        EvalUser evalUser = new EvalUser(kpiTypes.get(0).getTaskUserId(), kpiTypes, items, kpiTypes.get(0).getCompanyId());
        TaskUserBuilder builder = new TaskUserBuilder(evalUser, empEvalRule, kpiItemGen, auditIdGen, evalCommonGen, taskFormulaFieldGen);
        builder.build();
        domainDao.saveBatch(builder.getTypeDos());
        domainDao.saveBatch(builder.getKpis());
        domainDao.saveBatch(builder.getUsedFieldDos());
        domainDao.saveBatch(builder.getItemUsedFieldDos());
        domainDao.saveBatch(builder.getFormulaFields());
        domainDao.saveBatch(builder.builderItemFinishValueAudit());
    }

    private void saveEmpEvalKpi(EvalUser evalUser) {
        final Supplier<String> auditIdGen = () -> domainDao.nextLongAsStr(auditSeq);
        final Supplier<String> evalCommonGen = () -> domainDao.nextLongAsStr(evalCommonSeq);
        final Supplier<String> kpiItemGen = () -> domainDao.nextLongAsStr(kpiItemSeq);
        final Supplier<String> taskFormulaFieldGen = () -> domainDao.nextLongAsStr(taskFormulaField);
        List<EmpEvalKpiType> kpiTypes = evalUser.getKpiTypes();
        EmpEvalRule empEvalRule = evalUser.getEmpEvalRule();
        List<EvalKpi> items = new ArrayList<>();
        kpiTypes = kpiTypes.stream().filter(type -> type.getIsDeleted().equals(Boolean.FALSE.toString()))
                .collect(Collectors.toList());
        for (EmpEvalKpiType kpiType : kpiTypes) {
            items.addAll(kpiType.getItems().stream().filter(item -> item.getIsDeleted().equals("false")).collect(Collectors.toList()));
        }
        empEvalRule.setKpiTypes(new KpiListWrap(kpiTypes));//赋值筛选删除之后的维度
        EmpRule2DataBd builder = new EmpRule2DataBd(empEvalRule, kpiItemGen, auditIdGen, evalCommonGen, taskFormulaFieldGen);
        builder.initOrg(evalUser.getEvalOrgId(), evalUser.getOrgId(),evalUser.getEmpId(), evalUser.getEmpName(), evalUser.getAvatar());
        builder.build2Data(evalUser.getTaskId(), evalUser.getEmpId());
        domainDao.saveBatch(builder.getTypeDos());
        domainDao.saveReplaceBatch(builder.getKpiItemDos());
        domainDao.saveBatch(builder.getUsedFieldDos());
        domainDao.saveBatch(builder.getItemUsedFieldDos());
        domainDao.saveBatch(builder.getFormulaFields());
        domainDao.saveBatch(builder.getFinishValueAudits());
        domainDao.saveBatch(builder.getScorerAuditDos());
    }

    public void delEmpEvalKpi(TenantId tenantId, String evalUserId, List<EmpEvalKpiType> kpiTypes, Boolean empEvalRuleIsNull) {
        List<String> kpiItemIds = new ArrayList<>();
        List<String> kpiTypeIds = new ArrayList<>();
        List<String> deletedKrItemIds = new ArrayList<>();
        if (kpiTypes != null && kpiTypes.size() > 0) {
            kpiTypes.forEach(obj -> {
                if (obj.getItems() != null && obj.getItems().size() > 0) {
                    kpiItemIds.addAll(obj.getItems().stream().map(EvalKpi::getKpiItemId).distinct().collect(Collectors.toList()));
                    deletedKrItemIds.addAll(obj.getItems().stream().filter(o -> o.isKr()).map(EvalKpi::getId).collect(Collectors.toList()));
                }
            });
            kpiTypeIds = kpiTypes.stream().map(EmpEvalKpiType::getKpiTypeId).distinct().collect(Collectors.toList());
        }

        UpdateBuilder type = UpdateBuilder.build(EmpEvalKpiType.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", evalUserId);
        //.whereIn("kpi_type_id", kpiTypeIds);

        UpdateBuilder typeUsed = UpdateBuilder.build(EmpEvalTypeUsedFieldDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", evalUserId)
                .appendWhere("is_deleted='false'");

        UpdateBuilder okrType = UpdateBuilder.build(PerfEvaluateTaskOkrTypeDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", evalUserId)
                .appendWhere("is_deleted='false'")
                .whereIn("kpi_type_id", kpiTypeIds);

//        UpdateBuilder taskItemScoreRule = UpdateBuilder.build(PerfEvaluateTaskItemScoreRuleDo.class)
//                .set("is_deleted", Boolean.TRUE.toString())
//                .whereEqReq("company_id", tenantId.getId())
//                .whereEqReq("task_user_id", evalUserId)
//                .whereIn("kpi_item_id", kpiItemIds);

        DeleteBuilder item = DeleteBuilder.build(PerfEvaluateTaskKpiDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .appendWhere("is_deleted='false'")
                .whereEqReq("task_user_id", evalUserId)
                .whereIn("kpi_item_id", kpiItemIds);

        UpdateBuilder itemUsed = UpdateBuilder.build(PerfEvaluateItemUsedFieldDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", evalUserId)
                .appendWhere("is_deleted='false'")
                .whereIn("kpi_item_id", kpiItemIds);

        UpdateBuilder filed = UpdateBuilder.build(PerfEvaluateTaskFileDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", evalUserId)
                .appendWhere("is_deleted='false'")
                .whereIn("kpi_item_id", kpiItemIds);

        UpdateBuilder taskAudit = UpdateBuilder.build(TaskAuditDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", evalUserId)
                .whereIn("kpi_item_id", kpiItemIds)
                .whereEqReq("scene", AuditEnum.FINISH_VALUE_AUDIT.toString());
        // domainDao.update(taskAudit);
        //不为空，才需要执行
        if (!empEvalRuleIsNull) {
            UpdateBuilder audit = UpdateBuilder.build(TaskAuditDo.class)
                    .set("is_deleted", Boolean.TRUE.toString())
                    .whereEqReq("company_id", tenantId.getId())
                    .whereEqReq("task_user_id", evalUserId)
                    .appendWhere("scene like '%score%'")
                    .whereEq("is_deleted", Boolean.FALSE.toString());
            domainDao.update(audit);
        }

        UpdateBuilder formula = UpdateBuilder.build(PerfEvaluateTaskFormulaFieldDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", evalUserId);
        domainDao.update(formula);

        domainDao.update(type);
        domainDao.update(okrType);
        if (CollUtil.isNotEmpty(kpiItemIds)) {
            domainDao.delete(item);
            domainDao.update(filed);
            domainDao.update(taskAudit);
        }
        domainDao.update(typeUsed);
        domainDao.update(itemUsed);
//        domainDao.update(itemRule);
//        domainDao.update(taskItemScoreRule);
        //直接删除关联kr的指标，防止新增时id指标
        if (CollUtil.isNotEmpty(deletedKrItemIds)) {
            DeleteBuilder delItem = DeleteBuilder.build(PerfEvaluateTaskKpiDo.class)
                    .whereInReq("id", deletedKrItemIds);
            domainDao.delete(delItem);
        }

    }

    @Override
    public LevelAuditFlow loadAuditFlow(TenantId companyId, String taskUserId, EvaluateAuditSceneEnum modifyItemAudit) {
        List<EvalAudit> mainAudits = userDao.listAuditByScene(companyId, taskUserId, modifyItemAudit.getScene());
        List<EvalScoreResult> rss = userDao.listResultByScoreType(companyId, taskUserId, modifyItemAudit.getScene());
        List<EvalScoreResult> waitRss = userDao.listWaitDispatchResult(companyId, taskUserId, modifyItemAudit.getScene());
        ListWrap<EvalScoreResult> rsGroup = new ListWrap<>(rss).groupBy(rs -> rs.getTaskAuditId());
        ListWrap<EvalScoreResult> waitRsGroup = new ListWrap<>(waitRss).groupBy(rs -> rs.getTaskAuditId());
        for (EvalAudit audit : mainAudits) {
            audit.setSubNodes(rsGroup.groupGet(audit.getId()));
            audit.setWaitSubNodes(waitRsGroup.groupGet(audit.getId()));
        }
        ListWrap<EvalAudit> allSubNodes = new ListWrap<>(mainAudits).groupBy(evalAudit -> evalAudit.getApprovalOrder() + "");
        LevelAuditFlow flowRs = new LevelAuditFlow(taskUserId, allSubNodes);
        //flow.build(null, mainAudits, subNodes);
        return flowRs;
    }

    public ListWrap<LevelAuditFlow> listConfirmAuditFlow(TenantId companyId, Collection<String> taskUserIds, EvaluateAuditSceneEnum modifyItemAudit) {
        List<String> seces = Arrays.asList(modifyItemAudit.getScene());
        ListWrap<EvalAudit> mainAuditGrupss = userDao.listAuditByScenes(companyId, taskUserIds, seces);
        ListWrap<EvalScoreResult> rssGroups = userDao.listResultByScoreTypes(companyId, taskUserIds, seces, false);
        ListWrap<EvalScoreResult> waitRssGroups = userDao.listWaitDispatchResult(companyId, taskUserIds, modifyItemAudit.getScene());
        ListWrap<LevelAuditFlow> flowRss = new ListWrap<>();
        mainAuditGrupss.getGroups().forEach((taskUserId, mainAudits) -> {
            List<EvalScoreResult> rss = rssGroups.groupGet(taskUserId);
            List<EvalScoreResult> waitRss = waitRssGroups.groupGet(taskUserId);
            ListWrap<EvalScoreResult> rsGroup = new ListWrap<>(rss).groupBy(rs -> rs.getTaskAuditId());
            ListWrap<EvalScoreResult> waitRsGroup = new ListWrap<>(waitRss).groupBy(rs -> rs.getTaskAuditId());
            for (EvalAudit audit : mainAudits) {
                audit.setSubNodes(rsGroup.groupGet(audit.getId()));
                audit.setWaitSubNodes(waitRsGroup.groupGet(audit.getId()));
            }
            ListWrap<EvalAudit> allSubNodes = new ListWrap<>(mainAudits).groupBy(evalAudit -> evalAudit.getApprovalOrder() + "");
            LevelAuditFlow flowRs = new LevelAuditFlow(taskUserId,allSubNodes);
            flowRss.add(flowRs);

        });
        return flowRss.asMap(f -> f.getTaskUserId());
    }

    @Override
    public void saveApplyInfo(TenantId tenantId, String taskUserId, List<EmpEvalKpiType> info, PerfModificationRecord record, String changeReason) {
        if (Objects.isNull(info)) {
            return;
        }
        CompanyCacheInfoDo cache = new CompanyCacheInfoDo(tenantId.getId(), record.getCreatedUser(), taskUserId);
        cache.addCacheValue(info);
        domainDao.save(cache);
        UpdateBuilder user = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                .set("task_status", TalentStatus.CHANGING.getStatus())
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("id", taskUserId);
        domainDao.update(user);
    }

    @Override
    public void saveAuditOpLog(TenantId tenantId, String taskUserId, EmpId opEmpId, String recordValue, String changeReason) {
        OperationLogDo model =
                new OperationLogDo(tenantId.getId(), taskUserId, OperationLogSceneEnum.CHANGE_ITEM.getScene(), opEmpId.getId(), changeReason);
        domainDao.save(model);
        PerfModificationRecord record = new PerfModificationRecord();
        record.accOp(tenantId, opEmpId, taskUserId, OperationLogSceneEnum.CHANGE_ITEM.getScene(), TalentStatus.CHANGING.getStatus());
        record.setValue(StringUtils.isBlank(recordValue) ? changeReason : recordValue);
        domainDao.add(PerfModificationRecordDo.class, record);
    }

    //参数empId 为了兼容1.0，1.0的缓存里面没有empId
    public List<EmpEvalKpiType> listCacheKpi(TenantId tenantId, String taskUserId, String empId, List<String> scenes) {
        ComQB comQB = ComQB.build(CompanyCacheInfoDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereInReq("business_scene", scenes)
                .whereEqReq("link_id", taskUserId)
                .whereEqReq("cache_key", taskUserId)
                .whereEqReq("is_deleted", Boolean.FALSE.toString())
                .orderByDesc("created_time")
                .limit(0, 1);
        CompanyCacheInfo cacheInfo = domainDao.findDomain(comQB, CompanyCacheInfo.class);
        if (cacheInfo == null || StrUtil.isBlank(cacheInfo.getValue())) {
            return Collections.emptyList();
        }
        //List<ChangeItemStageCache> cacheKpiTypes = JSONUtil.parseArray(cacheInfo.getValue()).toList(ChangeItemStageCache.class);
        List<EmpChangeItemStagePo> cacheKpiTypes = JSONObject.parseArray(cacheInfo.getValue(), EmpChangeItemStagePo.class);
        List<EmpEvalKpiType> evalKpiTypes = new ArrayList<>();
        for (EmpChangeItemStagePo cacheKpiType : cacheKpiTypes) {
            /*if (cacheKpiType.isDeleted()) {
                continue;
            }*/
//            if (CollUtil.isNotEmpty(cacheKpiType.getItems())) {
//                List<EmpChangeItemStagePo.CacheKpiItem> items = cacheKpiType.getItems().stream().filter(item -> !"delete".equals(item.getExamineOperType()))
//                        .collect(Collectors.toList());
//                if (CollUtil.isNotEmpty(items)) {
//                    items.forEach(item -> item.empIdIfNeed(empId));
//                }
//                cacheKpiType.setItems(items);
//            }
            EmpEvalKpiType kpiType = Convert.convert(EmpEvalKpiType.class, cacheKpiType);
            List<EvalKpi> kpis = JSONObject.parseArray(JSON.toJSONString(cacheKpiType.getItems()), EvalKpi.class);
            kpiType.setItems(kpis);
            kpiType.initBaseInfo(new TenantId(cacheInfo.getCompanyId()), new EmpId(cacheInfo.getCreatedUser()), cacheInfo.getLinkId());
            evalKpiTypes.add(kpiType);
        }
        return evalKpiTypes;
    }


    @Override
    public void changeItem(EvalUser empEval, EmpId opEmpId, String changeReason) {
        TenantId tenantId = empEval.getCompanyId();
        String taskUserId = empEval.getId();
        List<EmpEvalKpiType> kpiTypes = empEval.getEmpEvalRule().getKpiTypes().getDatas();
        EmpEvalRule empEvalRule = empEval.getEmpEvalRule();
        UpdateBuilder user = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                .set("task_status", TalentStatus.CONFIRMED.getStatus())
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("id", taskUserId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        OperationLogDo oplog = new OperationLogDo(tenantId.getId(), taskUserId, OperationLogSceneEnum.CHANGE_ITEM_SUCCESS.getScene(), opEmpId.getId(), "");

        /**清除okr指标项*/
        kpiTypes.forEach(obj -> {
            obj.getItems().removeIf(filter -> "true".equals(obj.getIsOkr()));
        });
        //List<EmpEvalKpiType> evalKpis = kpiTypes.stream().filter(obj -> obj.getItems() != null && obj.getItems().size() > 0).collect(Collectors.toList());
        //List<EmpEvalKpiType> okrKpis = kpiTypes.stream().filter(obj -> obj.getItems() == null || obj.getItems().size() == 0).collect(Collectors.toList());
        boolean empEvalRuleIsNull = Objects.isNull(empEvalRule);
        delEmpEvalKpi(tenantId, taskUserId, kpiTypes, empEvalRuleIsNull);
        domainDao.update(user);
        domainDao.save(oplog);
        deleteCache(tenantId, taskUserId, opEmpId.getId(), AuditEnum.EDIT_EXE_INDI.getScene());
        empEval.setKpiTypes(kpiTypes);
        saveEmpEvalKpi(empEvalRuleIsNull, empEval, kpiTypes, empEvalRule);
    }

    @Override
    public void terminateEmpEval(String companyId, String opEmpId, String opAdminType, String taskId, List<String> taskUserIds) {
        UpdateBuilder update = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                .set("task_status", TalentStatus.TERMINATED.getStatus())
                .set("created_user", opEmpId)
                .whereInReq("id", taskUserIds)
                .whereEqReq("company_id", companyId);
        domainDao.update(update);

        UpdateBuilder msg = UpdateBuilder.build(CompanyMsgCenterDo.class)
                .set("handler_status", Boolean.TRUE.toString())
                .whereInReq("id", taskUserIds)
                .whereEqReq("company_id", companyId);
        domainDao.update(msg);

        List<OperationLogDo> logs = new ArrayList<>();
        taskUserIds.forEach(userId -> {
            OperationLogDo logDo = new OperationLogDo(companyId, TalentStatus.TERMINATED.getStatus(), userId, opEmpId);
            logs.add(logDo);
        });
        domainDao.saveBatch(logs);

        //插入考核任务操作日志
        AdminTaskOperationDo operationDo
                = AdminTaskOperationDo.buildBase(companyId, taskId, opAdminType, opEmpId);
        operationDo.endTaskEvalChanged();

        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .clearSelect().select("emp_id,emp_name,avatar,emp_org_name as orgName,eval_org_name as evalOrgName,id as taskUserId")
                .setRsType(OpEmpEval.class)
                .whereEqReq("company_id", companyId)
                .whereInReq("id", taskUserIds);
        List<OpEmpEval> empEvals = domainDao.listAll(comQB);
        operationDo.setTerminateEmps(empEvals);
        KpiEmployee opEmp = kpiEmpDao.findEmployee(new TenantId(companyId), opEmpId);
        operationDo.opName(opEmp.getName(), opEmp.getAvatar());
        operationDo.setId(domainDao.nextLongAsStr(diffLog));
        domainDao.save(operationDo);
    }

    @Override
    public void rejectConfirmItem(String companyId, String opEmpId, String taskUserId, String rejectReason, Integer afterOrder) {
//        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
//                .whereEqReq("company_id", companyId)
//                .whereEqReq("scorer_id", opEmpId)
//                .whereEqReq("task_user_id", taskUserId)
//                .whereEq("scorer_type", EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT.getScene())
//                .whereEq("is_deleted", Boolean.FALSE.toString())
//                .appendWhere(" approval_order!=1 ")
//                .appendWhere("audit_status is null")
//                .whereEqReq("scorer_id", opEmpId)
//                .orderBy("approval_order");
//        List<EvalScoreResult> rejScoreRss = domainDao.listAllDomain(comQB, EvalScoreResult.class);
//        if (CollUtil.isEmpty(rejScoreRss)) {
//            throw new RuntimeException("error.unable.reject");
//        }
//        int afterOrder = rejScoreRss.get(0).getApprovalOrder() - 1;
        //指标1级节点直接置为null，因为发起评分来派发一级节点，后续没有入口来派发
        UpdateBuilder reject = UpdateBuilder.build(PerfEvaluateTaskScoreResultDo.class)
                .set("audit_status", null)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("scorer_type", EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT.getScene())
                .appendWhere("(audit_status != 'transferred' or audit_status is null)")
                .whereEq("approval_order", afterOrder)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.update(reject);
        //一级以上的节点标记waitDispatched 后续用来派发
        UpdateBuilder reject2 = UpdateBuilder.build(PerfEvaluateTaskScoreResultDo.class)
                .set("audit_status", "waitDispatch")
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("scorer_type", EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT.getScene())
                .appendWhere("(audit_status != 'transferred' or audit_status is null)")
                .whereBig("approval_order", afterOrder)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.update(reject2);
        //驳回日志
        OperationLogDo log = new OperationLogDo(companyId, taskUserId, OperationLogSceneEnum.ITEM_AUDIT_REJECT.getScene(), opEmpId, rejectReason);
        domainDao.save(log);
        //变更记录
        PerfModificationRecord record = new PerfModificationRecord();
        record.accOp(new TenantId(companyId), new EmpId(opEmpId), taskUserId, EvaluateAuditSceneEnum.CONFIRM_ITEM.getScene(), BusinessConstant.REJECT, rejectReason);
        domainDao.add(PerfModificationRecordDo.class, record);
    }

    @Override
    public EvalAudit getFirstConfirmNode(String companyId, String taskUserId) {
        ComQB comQB = ComQB.build(TaskAuditDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEq("scene", AuditEnum.CONFIRM_TASK.getScene())
                .appendWhere(" status is null ")
                .orderBy("approval_order");
        return domainDao.findDomain(comQB, EvalAudit.class);
    }

    @Override
    public void doResultAudit(EvalUser before, EvalUser submit, String scoreComment, List<EvalScoreResult> results) {
        String companyId = submit.getCompanyId().getId();
        String taskUserId = submit.getId();
        UpdateBuilder user = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                .set("final_score", submit.getFinalScore())
                .set("evaluation_level", submit.getEvaluationLevel())
                .set("last_score_comment", scoreComment)
                .set("step_id", submit.getStepId())
                .set("perf_coefficient", submit.getPerfCoefficient())
                .whereEqReq("id", submit.getId())
                .whereEqReq("company_id", companyId)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEq("org_id", submit.getOrgId());
        domainDao.update(user);
        batchUpdateScoreResult(results);
        String beforeValue = Objects.nonNull(before.getFinalScore()) ? before.getFinalScore().toString() + "/" + before.getEvaluationLevel() + "/" + before.getPerfCoefficient() : "";
        String afterValue = Objects.nonNull(submit.getFinalScore()) ? submit.getFinalScore().toString() + "/" + submit.getEvaluationLevel() + "/" + submit.getPerfCoefficient() : "";
        OperationLogDo log = new OperationLogDo(companyId, taskUserId, OperationLogSceneEnum.FINAL_RESULT_AUDIT.getScene(),
                submit.getUpdatedUser(), beforeValue, afterValue, scoreComment);
        domainDao.save(log);

        //驳回标记为完成
        UpdateBuilder up = UpdateBuilder.build(ResultAuditBatchItemDo.class)
                .set("status", 2)
                .whereEqReq("company_id", companyId)
                .whereEq("taskUserId", submit.getId());
        domainDao.update(up);

    }

    @Override
    public void doResultAudit(EvalUser submit, List<EvalScoreResult> results) {
        String companyId = submit.getCompanyId().getId();
        updateTaskUser(submit);
        batchUpdateScoreResult(results);
        //驳回标记为完成
        UpdateBuilder up = UpdateBuilder.build(ResultAuditBatchItemDo.class)
                .set("status", 2)
                .whereEqReq("company_id", companyId)
                .whereEq("taskUserId", submit.getId());
        domainDao.update(up);
    }

    @Override
    public void rejectChangeItem(String companyId, String opEmpId, String taskUserId, String reason) {
        ComQB queryBuilder = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .whereEq("company_id", companyId)
                .whereEq("task_user_id", taskUserId)
                .whereEq("scorer_type", EvaluateAuditSceneEnum.CHANGE_ITEM_AUDIT.getScene())
                .whereIsNull("audit_status")
                .whereEq("scorer_id", opEmpId)
                .whereEq("is_deleted", "false");
        List<PerfEvaluateTaskScoreResultDo> resultDos = domainDao.listAll(queryBuilder);
        if (CollUtil.isEmpty(resultDos)) {
            throw new RuntimeException("error.auditrecord.empty");
        }
        UpdateBuilder cache = UpdateBuilder.build(CompanyCacheInfoDo.class)
                .set("is_deleted", "true")
                .set("updated_user", opEmpId)
                .whereEq("company_id", companyId)
                .whereEq("link_id", taskUserId)
                .whereEq("business_scene", AuditEnum.EDIT_EXE_INDI.getScene())
                .whereEq("is_deleted", "false");
        int one = domainDao.update(cache);
        if (one <= 0) {
            throw new RuntimeException("error.auditrecord.empty");
        }
        UpdateBuilder result = UpdateBuilder.build(PerfEvaluateTaskScoreResultDo.class)
                .set("audit_status", "waitDispatch")
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .appendWhere("(audit_status != 'transferred' or audit_status is null)")
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEq("scorer_type", AuditEnum.EDIT_EXE_INDI.getScene());
        domainDao.update(result);

        //
        saveModification(new TenantId(companyId), new EmpId(opEmpId), taskUserId, OperationLogSceneEnum.CHANGE_ITEM.getScene(), BusinessConstant.REJECT, reason);

        OperationLogDo logDo = new OperationLogDo(companyId,
                taskUserId, OperationLogSceneEnum.CHANGE_ITEM_AUDIT_REJECT.getScene(), opEmpId, reason);
        domainDao.save(logDo);
    }

    @Override
    public void saveModification(TenantId tenantId, EmpId opEmpId, String taskUserId, String businessScene, String status, String reason) {
        PerfModificationRecord record = new PerfModificationRecord();
        record.accOp(tenantId, opEmpId, taskUserId, OperationLogSceneEnum.CHANGE_ITEM.getScene(), status, reason);
        domainDao.add(PerfModificationRecordDo.class, record);
    }

    @Override
    public void batchSaveModification(List<PerfModificationRecord> recordList) {
        domainDao.addBatch(PerfModificationRecordDo.class, recordList);
    }

    public void loadKpis(EvalUser empEval) {
        List<EmpEvalKpiType> types = kpiDao.listEmpEvalKpiTypeV3(empEval.getCompanyId(), empEval.getId()).getDatas();
        ListWrap<EvalKpi> itemsWrap = kpiDao.listEmpEvalKpiItem(empEval.getCompanyId(), empEval.getId());
        itemsWrap.groupBy(item -> item.getKpiTypeId());
        List<KpiItemUsedField> kpiItemUsedFields = getItemUsedFieldDoList(empEval.getCompanyId(), empEval.getId());
        for (EmpEvalKpiType type : types) {
            if (CollUtil.isEmpty(itemsWrap.groupGet(type.getKpiTypeId()))) {
                continue;
            }
            type.setItems(itemsWrap.groupGet(type.getKpiTypeId()));
            type.initKpiItemUsedFields(kpiItemUsedFields);
        }
        empEval.setKpis(itemsWrap.getDatas());
        empEval.setKpiTypes(types);
        /**处理纬度与指标字段字定义设置*/
        //empEval.builderUsedField(getTypeUsedFieldDoList(empEval.getCompanyId(),empEval.getId()),getItemUsedFieldDoList(empEval.getCompanyId(),empEval.getId()));
    }

    @Override
    public void saveSubmitScore(EmpEvalMerge evalMerge, List<PerfEvalTypeResult> typeRss, List<EvalScoreResult> submiteds,
                                EvalScoreResult total, List<EvalScoreSummary> summarys, boolean reCommit, boolean isSystemSkip, String signatureUrl, boolean wasAddLog) {
        String scorerId = CollUtil.isNotEmpty(typeRss) ? typeRss.get(0).getScorerId() : (CollUtil.isNotEmpty(submiteds) ? submiteds.get(0).getScorerId() : "-1");

        DeleteBuilder cacheQB = DeleteBuilder.build(SubmitScoreCacheDo.class, "c")
                .whereEq("companyId", evalMerge.getCompanyId().getId())
                .whereEq("taskUserId", evalMerge.getEmpEvalId())
                .whereEq("ownerEmpId", scorerId);
        domainDao.delete(cacheQB);

        //更新维度评价
        uppdateTypeScore(typeRss);
        if (CollUtil.isEmpty(submiteds)) {
            if (CollUtil.isNotEmpty(typeRss)) {
                String opEmpId = addLog(evalMerge, reCommit, typeRss, submiteds, isSystemSkip, signatureUrl, wasAddLog);
                //添加评分总结 仅维度评价不支持添加评分总结
                if (wasAddLog) {
                    this.addEvalUserSummary(summarys, new EmpId(opEmpId));
                }
            }
            return;
        }
        //更新指标评分
        batchUpdateScoreResult(submiteds);
        //更新指标等级
        List<EvalScoreResult> updateLevels = submiteds.stream().filter(typeRs -> typeRs.isUpdateTypeLevel()).collect(Collectors.toList());
        for (EvalScoreResult updateLevel : updateLevels) {
            UpdateBuilder up = UpdateBuilder.build(PerfEvaluateTaskKpiDo.class)
                    .set("indLevel", updateLevel.getScoreLevel())
                    .whereEqReq("companyId", updateLevel.getCompanyId().getId())
                    .whereEqReq("kpiTypeId", updateLevel.getKpiTypeId())
                    .whereEqReq("taskUserId", updateLevel.getTaskUserId())
                    .whereEqReq("kpiItemId", updateLevel.getKpiItemId());
            this.domainDao.update(up);
        }
        if (total != null && (total.isActive() || StrUtil.isNotBlank(total.getScoreComment()))) {
            UpdateBuilder up = UpdateBuilder.build(PerfEvaluateTaskScoreResultDo.class)
                    .appendSet("is_deleted='true'")
                    .whereEqReq("companyId", total.getCompanyId().getId())
                    .whereEqReq("taskUserId", total.getTaskUserId())
                    .whereEqReq("scorerId", total.getScorerId())
                    .whereEqReq("scorerType", total.getScorerType());
            domainDao.update(up);
            domainDao.add(PerfEvaluateTaskScoreResultDo.class, total);
        }
        String opEmpId = addLog(evalMerge, reCommit, typeRss, submiteds, isSystemSkip, signatureUrl, wasAddLog);
        //添加评分总结
        if (wasAddLog) {
            this.addEvalUserSummary(summarys, new EmpId(opEmpId));
        }
    }

    @Override
    public void saveSubmitScore(EmpEvalMerge evalMerge, List<PerfEvalTypeResult> typeRss,
                                List<EvalScoreResult> submiteds, EvalScoreResult total, List<EvalScoreSummary> summarys,
                                boolean reCommit, boolean isSystemSkip, String signatureUrl,
                                List<String> orFinishedScorerIds, boolean wasAddLog) {
        if (CollUtil.isNotEmpty(orFinishedScorerIds)) {
            //如果是或签提交的评分人，需要将其暂存的评分数据去除。
            DeleteBuilder cacheQB = DeleteBuilder.build(SubmitScoreCacheDo.class, "c")
                    .whereEq("companyId", evalMerge.getCompanyId().getId())
                    .whereEq("taskUserId", evalMerge.getEmpEvalId())
                    .whereIn("ownerEmpId", orFinishedScorerIds);
            domainDao.delete(cacheQB);
        }
        saveSubmitScore(evalMerge, typeRss, submiteds, total, summarys, reCommit, isSystemSkip, signatureUrl, wasAddLog);
    }

    @Override
    public void saveSubmitScoreV3(TenantId companyId, String taskUserId, List<EmpEvalKpiType> types, List<EvalKpi> submitedKpis) {
        //更新维度评价
        BatchUpdateBuilder upType = BatchUpdateBuilder.buildTable("perf_evaluate_task_kpi")
                .addSetCaseProp("typeLevel", "id:=")
                .addAllBean(submitedKpis)
                .whereEq("companyId", companyId)
                .whereEq("taskUserId", taskUserId)
                .whereEq("is_deleted", "false")
                .whereUseIn("id");
        //up 更新指标等级
        BatchUpdateBuilder upItem = BatchUpdateBuilder.buildTable("perf_evaluate_task_kpi")
                .addSetCaseProp("indLevel", "id:=")
                .addAllBean(submitedKpis)
                .whereEq("companyId", companyId)
                .whereEq("taskUserId", taskUserId)
                .whereEq("is_deleted", "false")
                .whereUseIn("id");
        this.domainDao.updateBatch(upType);
        this.domainDao.updateBatch(upItem);
    }

    public String addLog(EmpEvalMerge evalMerge, boolean reCommit, List<PerfEvalTypeResult> typeRss,
                         List<EvalScoreResult> submiteds, boolean isSystemSkip, String signatureUrl, boolean wasAddLog) {
        String scorerType = null;
        String scorerId = null;
        if (CollUtil.isNotEmpty(submiteds)) {
            scorerType = submiteds.get(0).getScorerType();
            scorerId = submiteds.get(0).getScorerId();
        } else if (CollUtil.isNotEmpty(typeRss)) {
            scorerType = typeRss.get(0).getScorerType();
            scorerId = typeRss.get(0).getScorerId();
        } else {
            return null;
        }
        String scoreType = isSystemSkip ? "auto_skip_score" : scorerType;
        String logScene = reCommit ? OperationLogSceneEnum.MODIFY_SCORE.getScene() : scoreType;
        String descStr = "";
        JSONObject desc = new JSONObject();
        desc.put("scorerType", scorerType);
        if (SubScoreNodeEnum.fromStr(scorerType) == SubScoreNodeEnum.ITEM_SCORE) {
            desc.put("itemNames", evalMerge.itemLogNames(submiteds));
        }
        descStr = desc.toJSONString();
        EmpId opEmpId = new EmpId(scorerId);
        if (wasAddLog) {
            opLogDao.addLog(new OperationLogDo(evalMerge.getCompanyId().getId(), evalMerge.getEmpEvalId(), logScene, opEmpId.getId(), descStr, signatureUrl));
        }
        return scorerId;
    }

    private void uppdateTypeScore(List<PerfEvalTypeResult> results) {
        if (CollUtil.isEmpty(results)) {
            return;
        }
        for (PerfEvalTypeResult result : results) {
            PerfEvalTypeResultDo resultDo = new ToDataBuilder<>(result, PerfEvalTypeResultDo.class).data();
            resultDo.setUpdatedTime(new Date());
            this.domainDao.update(resultDo);
        }
        List<PerfEvalTypeResult> updateLevels = results.stream().filter(typeRs -> typeRs.isUpdateTypeLevel()).collect(Collectors.toList());
        for (PerfEvalTypeResult updateLevel : updateLevels) {
            UpdateBuilder up = UpdateBuilder.build(EmpEvalKpiTypeDo.class)
                    .set("typeLevel", updateLevel.getScoreLevel())
                    .whereEqReq("companyId", updateLevel.getCompanyId().getId())
                    .whereEqReq("kpiTypeId", updateLevel.getKpiTypeId())
                    .whereEqReq("taskUserId", updateLevel.getTaskUserId());
            this.domainDao.update(up);
        }
    }

    @Override
    public void resetStageAudit(TenantId companyId, String taskUserId, Set<String> scenes, String operationType, TalentStatus toStage, EvalUser evalUser) {
        EmpEvalRule empEvalRule = evalUser.getEmpEvalRule();
        UpdateBuilder upAudit = UpdateBuilder.build(TaskAuditDo.class)
                .set("status", null)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("taskUserId", taskUserId)
                .whereInReq("scene", scenes);
        domainDao.update(upAudit);
        //修改了任务配置触发重置，或者重置到评分前的阶段，需要清除result重新生成评分流程信息。（注：执行中可以修改考核流程，如果之前进入过评分生成的result数据需要清除掉）
        boolean del = OperationTypeEnum.CHANGED_CONFIG.getValue().equals(operationType) || toStage.beforeEq(TalentStatus.SCORING) || toStage.equals(TalentStatus.RESULTS_AUDITING);
        //如果是修改了周期或任务上的配置导致重置，则清除result中的数据重新根据audit生成
        UpdateBuilder updateBuilder = UpdateBuilder.build(PerfEvaluateTaskScoreResultDo.class)
                .appendSet(del ? "is_deleted='true'" : "audit_status='wait'")
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("taskUserId", taskUserId)
                .whereEq("is_deleted", "false")
                .whereInReq("scorer_type", scenes);
        domainDao.update(updateBuilder);

        UpdateBuilder tyDel = UpdateBuilder.build(PerfEvalTypeResultDo.class)
                .appendSet(del ? "is_deleted='true'" : "audit_status='wait'")
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("scorer_type", scenes)
                .whereEqReq("task_user_id", taskUserId);
        domainDao.update(tyDel);

        UpdateBuilder totalDel = UpdateBuilder.build(PerfEvalTotalLevelResultDo.class)
                .appendSet(del ? "is_deleted='true'" : "audit_status= 'wait'")
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_user_id", taskUserId);
        domainDao.update(totalDel);

        UpdateBuilder deadLine = UpdateBuilder.build(DeadLineJobDo.class)
                .appendSet(" execute_status = 0 ")
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEqReq("is_deleted", "false");
        domainDao.update(deadLine);

        //清除申述记录
//        ComQB queryBuilder = ComQB.build(PerfEvaluateTaskAppealBatchDo.class)
//                .whereEqReq("company_id", companyId.getId())
//                .whereEqReq("task_user_id", taskUserId)
//                .whereEq("is_deleted", "false");
//        List<PerfEvaluateTaskAppealBatchDo> batchDos = domainDao.listAll(queryBuilder);
//        if (CollUtil.isNotEmpty(batchDos)) {
//            List<String> batchIds = CollUtil.map(batchDos,PerfEvaluateTaskAppealBatchDo::getId,true);
//            UpdateBuilder appealBatch = UpdateBuilder.build(PerfEvaluateTaskAppealBatchDo.class)
//                    .appendSet(" is_deleted = 'true' ")
//                    .whereEqReq("company_id", companyId.getId())
//                    .whereInReq("id", batchIds)
//                    .whereEqReq("is_deleted", "false");
//            domainDao.update(appealBatch);
//            UpdateBuilder appealInfo = UpdateBuilder.build(PerfEvaluateTaskAppealInfoDo.class)
//                    .appendSet(" is_deleted = 'true' ")
//                    .whereEqReq("company_id", companyId.getId())
//                    .whereInReq("appeal_batch_id", batchIds)
//                    .whereEqReq("is_deleted", "false");
//            domainDao.update(appealInfo);
//        }
        //验证在申诉中，部分审核人处理了，但是非全员处理完成，此时重置考核任务，处理完的审核人的记录清空
        UpdateBuilder appealUpdateBuilder = UpdateBuilder.build(PerfEvaluateTaskAppealBatchDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEq("task_user_id", taskUserId)
                .whereEq("appeal_status","wait")
                .whereEq("is_deleted", "false");
        domainDao.update(appealUpdateBuilder);

        if (Objects.nonNull(empEvalRule)) {
            UpdateBuilder empEvalRuleUp = UpdateBuilder.build(EmpEvalRuleDo.class)
                    .set("s3PeerRater", JSON.toJSONString(empEvalRule.getS3PeerRater()))
                    .set("s3SubRater", JSON.toJSONString(empEvalRule.getS3SubRater()))
                    .whereEqReq("company_id", companyId.getId())
                    .whereEqReq("emp_eval_id", taskUserId)
                    .whereEqReq("is_deleted", "false");
            domainDao.update(empEvalRuleUp);
        }

        for (EmpEvalKpiType kpiType : evalUser.getKpiTypes()) {
            UpdateBuilder upType = UpdateBuilder.build(EmpEvalKpiTypeDo.class)
                    .set("peerRater", Objects.isNull(kpiType.getPeerRater()) ? null : JSON.toJSONString(kpiType.getPeerRater()))
                    .set("subRater", Objects.isNull(kpiType.getSubRater()) ? null : JSON.toJSONString(kpiType.getSubRater()))
                    .whereEqReq("company_id", companyId.getId())
                    .whereEqReq("task_user_id", taskUserId)
                    .whereEqReq("kpi_type_id", kpiType.getKpiTypeId())
                    .whereEqReq("is_deleted", "false");
            domainDao.update(upType);
        }

        for (EvalKpi kpi : evalUser.getKpis()) {
            if (Objects.isNull(kpi.getItemScoreRule())) {
                continue;
            }
            UpdateBuilder itemRuleUp = UpdateBuilder.build(PerfEvaluateTaskItemScoreRuleDo.class)
                    .set("peerRater", Objects.isNull(kpi.getItemScoreRule().getPeerRater()) ? null : JSON.toJSONString(kpi.getItemScoreRule().getPeerRater()))
                    .set("subRater", Objects.isNull(kpi.getItemScoreRule().getSubRater()) ? null : JSON.toJSONString(kpi.getItemScoreRule().getSubRater()))
                    .whereEqReq("company_id", companyId.getId())
                    .whereEqReq("task_user_id", taskUserId)
                    .whereEqReq("kpi_item_id", kpi.getKpiItemId())
                    .whereEqReq("is_deleted", "false");
            domainDao.update(itemRuleUp);
        }

        ComQB comQB = ComQB.build(ResultAuditFlowUserDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        ResultAuditFlowUserDo flowUserDo = domainDao.findOne(comQB);
        if (Objects.nonNull(flowUserDo)) {
            if (toStage.before(TalentStatus.RESULTS_AUDITING)) {
                UpdateBuilder flowUser = UpdateBuilder.build(ResultAuditFlowUserDo.class)
                        .set("status", 0)
                        .set("multiple_reviewers_type", empEvalRule.getAuditResult().getMultiType())
                        .whereEqReq("company_id", companyId.getId())
                        .whereEqReq("task_user_id", taskUserId)
                        .whereEqReq("is_deleted", "false");
                domainDao.update(flowUser);
            }
            if (toStage.before(TalentStatus.RESULTS_AUDITING)) {
                UpdateBuilder flowNode = UpdateBuilder.build(ResultAuditFlowNodeDo.class)
                        .set("status", 0)
                        .whereEqReq("company_id", companyId.getId())
                        .whereEqReq("flow_instance_id", flowUserDo.getFlowInstanceId())
                        .whereEqReq("is_deleted", "false");
                domainDao.update(flowNode);
            }
            if (toStage.before(TalentStatus.RESULTS_AUDITING)) {
                UpdateBuilder flowNodeRater = UpdateBuilder.build(ResultAuditFlowNodeRaterDo.class)
                        .set("status", 0)
                        .whereEqReq("company_id", companyId.getId())
                        .whereEqReq("flow_instance_id", flowUserDo.getFlowInstanceId())
                        .whereEqReq("is_deleted", "false");
                domainDao.update(flowNodeRater);
            }
        }

    }

    @Override
    public void resetScore(TenantId companyId, String taskUserId) {
        UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_user").set("final_score", null).set("evaluation_level", null).set("original_final_score", null).set("original_evaluation_level", null).set("self_score_flag", null)
                .set("manual_score_flag", null).set("superior_score_flag", null).set("item_score_flag", null).set("result_audit_flag", null).set("final_self_score", null)
                .set("final_peer_score", null).set("final_sub_score", null).set("final_superior_score", null).set("final_item_score", null).set("final_plus_score", null)
                .set("final_subtract_score", null).set("distribution_flag", null).set("enter_score_flag", null).set("public_flag", null).set("final_item_auto_score", null)
                .set("final_self_plus_score", null).set("final_self_subtract_score", null).set("final_superior_plus_score", null).set("final_superior_subtract_score", null)
                .set("v3_final_self_score", null).set("v3_final_peer_score", null).set("v3_final_sub_score", null).set("v3_final_appoint_score", null)
                .set("v3_final_superior_score", null).set("v3_final_item_score", null).set("v3_self_score", null).set("v3_peer_score", null).set("v3_sub_score", null).set("v3_appoint_score", null)
                .set("v3_superior_score", null)
                .set("step_id", null)
                .whereEq("companyId", companyId.getId())
                .whereEq("id", taskUserId);
        domainDao.update(updateBuilder);
        //按环节重置清空按人员重置评分记录，流程需要重新走
        UpdateBuilder clearResetScoreNode = UpdateBuilder.build(EvalScorerNode.class)
                .set("is_deleted", "true")
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_user_id", taskUserId);
        domainDao.update(clearResetScoreNode);

        //清除关联的问卷分
        List<EmpEvalKpiType> askTypes = taskKpiItemDao.listAsk360Type(companyId.getId(), taskUserId);
        boolean isUp360Score = CollUtil.isNotEmpty(askTypes);
        UpdateBuilder upType = UpdateBuilder.build("emp_eval_kpi_type")
                .set("type_final_score", null) .set("type_final_original_score", null).set("type_score", null) .set("type_original_score", null).set("type_final_self_score", null)
                .set("type_final_peer_score", null) .set("type_final_sub_score", null).set("type_final_superior_score", null)
                .set("type_final_item_score", null).set("type_item_score", null).set("type_final_appoint_score", null).set("type_self_score", null)
                .set("type_peer_score", null) .set("type_sub_score", null).set("type_superior_score", null) .set("type_appoint_score", null)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        if (isUp360Score){
            upType.set("ask360_eval_score", null);
        }
        domainDao.update(upType);
        //清除指标环节得分
        UpdateBuilder upKpi = UpdateBuilder.build("perf_evaluate_task_kpi")
                .set("item_final_score", null) .set("item_final_original_score", null).set("item_score", null) .set("item_original_score", null).set("item_final_self_score", null)
                .set("item_final_peer_score", null) .set("item_final_sub_score", null).set("item_final_superior_score", null)
                .set("item_item_score", null).set("item_final_item_score", null).set("item_final_appoint_score", null).set("item_self_score", null)
                .set("item_peer_score", null) .set("item_sub_score", null).set("item_superior_score", null) .set("item_appoint_score", null)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        domainDao.update(upKpi);
        delScoreSummary(companyId, taskUserId);
    }

    @Override
    public void updateKpiItem(List<EvalKpi> items) {
        for (EvalKpi item : items) {
            domainDao.update(PerfEvaluateTaskKpiDo.class, item);
        }
    }

    @Override
    public EvalUser getCustomTaskUser(TenantId tenantId, String curUserId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("id", curUserId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        EvalUser user = domainDao.findDomain(comQB, EvalUser.class);
        lazyLoadKpiItem(user);
        loadAudit(user, EvaluateAuditSceneEnum.APPOINT_SCORE);
        List<EvalKpi> kpis = user.getKpis();
        List<EvalAudit> appointAudits = user.getAppointScorers();
        List<EvalScoreResult> results = userDao.listResultByScoreType(tenantId, curUserId, SubScoreNodeEnum.APPOINT_SCORE.getScene());
        //List<EvalScoreResult> results = listScoreResultByScoreType(user.getCompanyId(), user.getTaskId(), new EmpId(user.getEmpId()), SubScoreNodeEnum.APPOINT_SCORE);
        for (EvalAudit appointAudit : appointAudits) {
            for (EvalScoreResult result : results) {
                if (!appointAudit.getId().equals(result.getTaskAuditId())) {
                    continue;
                }
                appointAudit.getSubNodes().add(result);
            }
        }
        for (EvalKpi kpi : kpis) {
            kpi.matchAudits(appointAudits);
        }
        return user;
    }

    @Override
    public void recordScoringEnd(TenantId companyId, String taskUserId) {
        UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                .whereEqReq("companyId", companyId.getId())
                .whereEqReq("id", taskUserId)
                .set("score_end_time", new Date());
    }

    @Override
    public void updateAuditStatus(TenantId companyId, String taskUserId, ChainDispatchRs dispatchRs) {
        List<String> resultIds = dispatchRs.getItemResults().mapTo(EvalScoreResult::getId);
        if (CollUtil.isNotEmpty(resultIds)) {
            UpdateBuilder builder = UpdateBuilder.build(PerfEvaluateTaskScoreResultDo.class)
                    .appendSet("audit_status = null")
                    .whereEqReq("company_id", companyId.getId())
                    .whereEqReq("task_user_id", taskUserId)
                    .whereInReq("id", resultIds);
            this.domainDao.update(builder);
        }

        List<String> typeResultIds = new ListWrap<>(dispatchRs.getTypeResults()).mapTo(PerfEvalTypeResult::getId);
        if (CollUtil.isNotEmpty(typeResultIds)) {
            UpdateBuilder upType = UpdateBuilder.build(PerfEvalTypeResultDo.class)
                    .appendSet("audit_status = null")
                    .whereEqReq("company_id", companyId.getId())
                    .whereEqReq("task_user_id", taskUserId)
                    .whereInReq("id", typeResultIds);
            this.domainDao.update(upType);
        }

        UpdateBuilder up = UpdateBuilder.build(PerfEvaluateTaskScoreResultDo.class)
                .appendSet("is_deleted = true")
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("scorer_type", "final_result_audit");
        this.domainDao.update(up);
    }

    @Override
    public void updateAuditStatusToRejected(TenantId companyId, String taskUserId, ChainDispatchRs dispatchRs) {
        List<String> resultIds = dispatchRs.getItemResults().mapTo(EvalScoreResult::getId);
        if (CollUtil.isNotEmpty(resultIds)) {
            UpdateBuilder builder = UpdateBuilder.build(PerfEvaluateTaskScoreResultDo.class)
                    .appendSet("audit_status = 'reject'")
                    .whereEqReq("company_id", companyId.getId())
                    .whereEqReq("task_user_id", taskUserId)
                    .whereInReq("id", resultIds);
            this.domainDao.update(builder);
        }

        List<String> typeResultIds = new ListWrap<>(dispatchRs.getTypeResults()).mapTo(PerfEvalTypeResult::getId);
        if (CollUtil.isNotEmpty(typeResultIds)) {
            UpdateBuilder upType = UpdateBuilder.build(PerfEvalTypeResultDo.class)
                    .appendSet("audit_status = 'reject'")
                    .whereEqReq("company_id", companyId.getId())
                    .whereEqReq("task_user_id", taskUserId)
                    .whereInReq("id", typeResultIds);
            this.domainDao.update(upType);
        }
    }

    @Override
    public void updateTypeLevel(TenantId tenantId, String taskUserId, List<AuditResultEvalType> types) {
        if (CollUtil.isEmpty(types)) {
            return;
        }
        for (AuditResultEvalType type : types) {
            UpdateBuilder up = UpdateBuilder.build(EmpEvalKpiTypeDo.class)
                    .set("type_level", type.getTypeLevel())
                    .whereEqReq("company_id", tenantId.getId())
                    .whereEqReq("kpi_type_id", type.getKpiTypeId())
                    .whereEqReq("task_user_id", taskUserId);
            this.domainDao.update(up);
        }
    }


    @Override
    public void rmCacheOfOpEmpId(TenantId tenantId, EmpId opEmpId, String taskUserId, String scene) {
        UpdateBuilder delCacheQ = UpdateBuilder.build(CompanyCacheInfoDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("link_id", taskUserId)
                .whereEqReq("business_scene", scene)
                .whereEqReq("cache_key", opEmpId.getId());
        domainDao.update(delCacheQ);
    }

    //新增加进来的部门要支持添加到考核任务中
    public List<EvalOrg> loadEvalOrgOwner(TenantId tenantId, List<String> evalOrgIds) {
        ComQB comQB = ComQB.build(EmpOrganizationDo.class, "o")
                .leftJoin(OrgEvalOwnerDo.class, "owner")
                .appendOn("owner.company_id =o.company_id and owner.org_id=o.org_id AND owner.is_deleted = 'false' ")
                .leftJoin(EmployeeBaseInfoDo.class, "e")
                .appendOn("owner.company_id=e.company_id and owner.org_owner_id=e.employee_id and e.is_delete='false' ")
                .clearSelect()
                .select("o.org_id as eval_org_id,o.org_name as eval_org_name,owner.org_owner_org_id")
                .select("owner.org_owner_org_name,e.employee_id as org_owner_id,e.name as org_owner_name,e.avatar as org_owner_avatar")
                .setRsType(EvalOrg.class)
                .whereEqReq("o.company_id", tenantId.getId())
                .whereEq("o.status", "valid")
                .whereInReq("o.org_id", evalOrgIds);
        return domainDao.listAll(comQB);
    }

    @Override
    public void saveSubmitTotalLevel(BaseScoreResult levelResult, String stepId) {
        OperationLogSceneEnum totalLevel = OperationLogSceneEnum.TOTAL_LEVEL;
        opLogDao.addSumitScoreLog(levelResult.getCompanyId(), levelResult.getTaskUserId(), new EmpId(levelResult.getScorerId()), totalLevel.getScene(), totalLevel.getDesc(), null);

        //将其暂存的评分数据去除。
        DeleteBuilder cacheQB = DeleteBuilder.build(SubmitScoreCacheDo.class, "c")
                .whereEq("companyId", levelResult.getCompanyId().getId())
                .whereEq("taskUserId", levelResult.getTaskUserId())
                .whereEq("ownerEmpId", levelResult.getScorerId());
        domainDao.delete(cacheQB);

        UpdateBuilder up = UpdateBuilder.build(PerfEvalTotalLevelResultDo.class)
                .set("auditStatus", levelResult.getAuditStatus())
                .set("scoreLevel", levelResult.getScoreLevel())
                .set("scoreComment", levelResult.getScoreComment())
                .set("scoreAttUrl", levelResult.getScoreAttUrl())
                .whereEqReq("company_id", levelResult.getCompanyId().getId())
                .whereEqReq("id", levelResult.getId());
        this.domainDao.update(up);

        UpdateBuilder upUser = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                .set("evaluationLevel", levelResult.getScoreLevel())
                .set("originalEvaluationLevel", levelResult.getScoreLevel())
                .set("stepId", stepId)
                .whereEqReq("company_id", levelResult.getCompanyId().getId())
                .whereEqReq("id", levelResult.getTaskUserId());
        this.domainDao.update(upUser);
    }

    @Override
    public void saveAuditTypeLevel(TenantId tenantId, String taskUserId, List<AuditResultEvalType> types) {
        if (CollUtil.isEmpty(types)) {
            return;
        }
        for (AuditResultEvalType type : types) {
            UpdateBuilder upUser = UpdateBuilder.build(EmpEvalKpiTypeDo.class)
                    .set("typeLevel", type.getTypeLevel())
                    .whereEqReq("company_id", tenantId.getId())
                    .whereEqReq("taskUserId", taskUserId)
                    .whereEqReq("kpiTypeId", type.getKpiTypeId());
            this.domainDao.update(upUser);
        }

    }

    @Override
    public List<String> listNotConfirmEvalUserCompanyIds() {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .clearSelect().select("distinct company_id").setRsType(String.class)
                .whereEq("task_status", TalentStatus.CONFIRMING.getStatus())
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .appendWhere(" (confirm_dead_line is not null and confirm_dead_line != '') and confirm_dead_line< '" +
                        new DateTime().toString("yyyy-MM-dd") + "' ");
        List<String> companyIds = domainDao.listAll(comQB);
        return companyIds;
    }
}