package com.polaris.kpi.setting.ppojo;

import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.common.infr.DelData;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.ibatis.annotations.Ckey;

import java.util.Date;

@Setter
@Getter
@NoArgsConstructor
public class ManagerPrivLogDo {
    @Ckey
    private String id;
    @JSONField(serialize = false)
    private String companyId;
    private String operationType;  //add=新增 update=编辑 del=删除 open=开启 close=关闭
    // batchOpen=批量开启 batchClose=批量关闭 batchDel=批量删除 synEvent = 同步事件
    private String beforeValue;
    private String afterValue;
    private String operationEmpId;    //操作人
    private String operationEmpType;  //操作人类型（main=主管理员  child=子管理员）
    private String operationDesc;   //
    private String createdUser;
    private Date createdTime;

}
