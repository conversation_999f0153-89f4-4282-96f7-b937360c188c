package com.polaris.kpi.eval.infr.task.dao;

import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.collection.CollUtil;
import com.polaris.kpi.eval.domain.task.entity.EmpEvalScorerNode;
import com.polaris.kpi.eval.domain.task.entity.EvalScorerNodeKpiItem;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.*;
import com.polaris.sdk.type.ListWrap;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * <AUTHOR> suxia<PERSON><PERSON>u
 * @date 2025/3/15 1:56 下午
 */
@Component
public class EvalScorerNodeKpiItemDao {
    @Autowired
    private DomainDaoImpl domainDao;

    public void setDomainDao(DomainDaoImpl domainDao) {
        this.domainDao = domainDao;
    }
    public List<EmpEvalSkipRatersPo> listSkipScorerKpiItemIds(String taskUserId, String companyId,Set<String> scorerNodeIds) {
        ComQB comQB = ComQB.build(EmpScorerScoreKpiItemDo.class)
                .clearSelect()
                .select("scorer_type as scoreType, scorer_id as skipUserId,scorer_name as raterName,scorer_avatar as avatar")
                .setRsType(EmpEvalSkipRatersPo.class)
                .whereEqReq("task_user_id", taskUserId)
                .whereEqReq("company_id", companyId)
                .whereInReq("scorer_score_node_id",scorerNodeIds)
                .whereEqReq("is_deleted", "false")
                .whereEqReq("status", 1) //评分中
                .whereIsNull("score")
                .whereNotNull("kpi_item_id")
                .groupBy("scorer_id", "scorer_type");
        return this.domainDao.listAll(comQB);
    }
    public ListWrap<EvalSNSkipItemPo> listSkipScorerKpiItemIdsGroupItem(String taskUserId, String companyId,Set<String> scorerNodeIds) {
        ComQB comQB = ComQB.build(EmpScorerScoreKpiItemDo.class)
                .clearSelect()
                .select("kpi_item_id, scorer_type, GROUP_CONCAT(scorer_id) as scorerId")
                .setRsType(EvalSNSkipItemPo.class)
                .whereEqReq("task_user_id", taskUserId)
                .whereInReq("scorer_score_node_id",scorerNodeIds)
                .whereEqReq("company_id", companyId)
                .whereEqReq("is_deleted", "false")
               // .whereInReq("status", 1) //评分中
                .whereIsNull("score")
                .whereNotNull("kpi_item_id")
                .groupBy("kpi_item_id ,scorer_type");
        List<EvalSNSkipItemPo> skipItemPos = this.domainDao.listAll(comQB);
        if (CollUtil.isNotEmpty(skipItemPos)){
            skipItemPos.forEach(EvalSNSkipItemPo::buildScorerIds);
        }
        return new ListWrap<>(skipItemPos).asMap(EvalSNSkipItemPo::getKpiItemId);
    }

    public List<EmpEvalSkipRatersV3Po> listScorerNodeSkipRaters(String taskUserId, String companyId){
        ComQB scoreNodeComQB = ComQB.build(EmpEvalScorerNodeDo.class)
                .clearSelect().setRsType(EmpEvalSkipRatersV3Po.class)
                .select("scorer_type as scoreType, scorer_id as skipUserId,scorer_name as raterName,scorer_avatar as avatar")
                .whereEqReq("company_id",companyId)
                .whereEqReq("task_user_id", taskUserId)
                .appendWhere("scorer_type !='self_score'")
                .appendWhere("((transfer_type is null) OR (transfer_type = 'transfer' AND transfer_from IS NOT NULL AND transfer_to IS NULL))")
                .whereInReq("scorer_type", SubScoreNodeEnum.listScoreScene())
                .whereEq("status", 1)
                .whereEq("is_deleted", "false")
                .groupBy("scorer_type,scorer_id");
        return domainDao.listAll(scoreNodeComQB);
    }
}
