package com.polaris.kpi.report.infr.pojo;

import cn.com.polaris.kpi.eval.CycleValue;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.eval.domain.task.entity.empeval.ComputeFinishValueProgress;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/5/12 22:06
 */
@Data
public class ItemTargetAndFinishValueInCyclePo {

    private String itemId;
    private String itemName;
    private String itemUnit;
    private Integer finishValueType;
    private BigDecimal targetValue;
    private BigDecimal finishValue;
    private String finishRate;
    private String cycleId;
    private Integer cycleValue;
    private String cycleType;
    private String cycleYear;

    @JSONField(serialize = false)
    private ComputeFinishValueProgress progress;

    public ItemTargetAndFinishValueInCyclePo(Integer cycleValue, String cycleType, String cycleYear) {
        this.cycleValue = cycleValue;
        this.cycleType = cycleType;
        this.cycleYear = cycleYear;
    }

    public void accProcess(ComputeFinishValueProgress progress){
        this.progress = progress;
    }

    public void computeFinishRate(){
        finishRate = progress.getPercent(targetValue, finishValue, finishValueType);
    }

    public void removeZero(){
        if (StrUtil.isNotBlank(this.finishRate)) {
            BigDecimal value = new BigDecimal(this.finishRate);
            this.finishRate = value.stripTrailingZeros().toPlainString();
        }
    }
}
