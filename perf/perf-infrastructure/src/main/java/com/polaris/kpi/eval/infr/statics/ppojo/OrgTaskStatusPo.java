package com.polaris.kpi.eval.infr.statics.ppojo;

import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/6 13:48
 */
@Getter
@Setter
@NoArgsConstructor
public class OrgTaskStatusPo {

    private String statusName;//状态名称；
    private Integer sort;//排序
    private String scoreScene;//评分环节

    public void calcSort(){
        TalentStatus talentStatus = TalentStatus.statusOf(this.getStatusName());
        sort = talentStatus.getOrder();
    }

}
