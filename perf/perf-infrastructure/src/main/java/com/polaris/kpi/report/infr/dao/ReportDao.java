package com.polaris.kpi.report.infr.dao;

import cn.com.polaris.kpi.ObjItem;
import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.com.polaris.kpi.eval.IndexCalibration;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.perf.www.common.constant.BusinessConstant;
import com.perf.www.common.utils.string.StringTool;
import com.perf.www.cons.AdminType;
import com.perf.www.domain.entity.company.EmpOrganizationModel;
import com.perf.www.domain.entity.company.EmpRefOrgModel;
import com.perf.www.domain.entity.company.SystemAdminSetModel;
import com.polaris.kpi.eval.domain.task.entity.Cycle;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.eval.infr.cycle.ppojo.AdminOfCycleDo;
import com.polaris.kpi.eval.infr.task.ppojo.*;
import com.polaris.kpi.eval.infr.task.ppojo.grade.GradeStepDo;
import com.polaris.kpi.eval.infr.task.ppojo.report.EmpYearReportItemPo;
import com.polaris.kpi.eval.infr.task.query.report.PerfAnalysisQuery;
import com.polaris.kpi.eval.infr.task.query.report.ReportQuery;
import com.polaris.kpi.eval.infr.task.query.report.TaskReportQry;
import com.polaris.kpi.org.infr.emp.pojo.EmpOrganizationDo;
import com.polaris.kpi.org.infr.emp.pojo.EmployeeBaseInfoDo;
import com.polaris.kpi.report.domain.entity.EmpYearReport;
import com.polaris.kpi.report.domain.entity.EmpYearReportItem;
import com.polaris.kpi.report.infr.pojo.EmpYearReportDo;
import com.polaris.kpi.report.infr.pojo.EmpYearReportItemDo;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.DeleteBuilder;
import org.lufei.ibatis.builder.NativeSQLBuilder;
import org.lufei.ibatis.common.data.ToDataBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.lufei.ibatis.mapper.PagedList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Component
@Slf4j
public class ReportDao {
    @Autowired
    private DomainDaoImpl domainDao;

    public static final String diffLog = "log";

    public PagedList<EmpYearReportItemPo> pagedYearReport(ReportQuery query) {
        final ComQB comQB = ComQB.build(EmpYearReportItemDo.class, "r")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn("r.company_id = e.company_id  and r.emp_id = e.employee_id")
                .clearSelect().setRsType(EmpYearReportItemPo.class)
                .select("r.*,e.jobnumber,e.`name` as empName,e.avatar emp_avatar, CASE WHEN e.is_resigned != 0 THEN 1 ELSE 0 END as leaved," +
                        "(select GROUP_CONCAT(o.org_name) FROM emp_organization o WHERE o.company_id = '" + query.getCompanyId() + "' " +
                        "and o.org_id in (select org_id from emp_ref_org where emp_id = e.employee_id and ref_type = 'org')) as orgName," +
                        "(SELECT GROUP_CONCAT(r.role_name) FROM role r WHERE r.company_id = '" + query.getCompanyId() + "' and r.is_deleted = 'false' " +
                        "AND r.id in (SELECT role_id FROM role_ref_emp WHERE company_id = '" + query.getCompanyId() + "' AND is_deleted = 'false' AND " +
                        "emp_id = e.employee_id))  as roleName")
                .whereEq("r.company_id", query.getCompanyId())
                .whereEq("r.year", query.getYear())
                .whereIn("r.empId", StrUtil.splitTrim(query.getEmpId(), ","))
                .whereIn("r.empId", query.getEmpIds())
                .whereEq("r.is_deleted", Boolean.FALSE.toString());
        if (CollUtil.isNotEmpty(query.getOrgIds())) {
            comQB.appendWhere("   EXISTS(SELECT id FROM emp_ref_org WHERE company_id = '" + query.getCompanyId() + "' AND emp_id = e.employee_id " +
                    "AND ref_type = 'org' AND org_id in ('" + StringUtils.join(query.getOrgIds(), "', '") + "'))");
        }
        if (CollectionUtils.isNotEmpty(query.getRoleIds())) {
            comQB.appendWhere("  EXISTS(SELECT id FROM role_ref_emp WHERE company_id = '" + query.getCompanyId() + "' AND is_deleted = 'false'" +
                    "    AND e.employee_id = emp_id AND role_id in ('" + StringUtils.join(query.getRoleIds(), "', '") + "'))");
        }

//        comQB.whereEq("e.is_delete", Boolean.FALSE.toString());
        buildEmployeeStatusWhere(query, comQB);

        if (StringUtils.isNotBlank(query.getOrderField()) && StringUtils.isNotBlank(query.getOrderType())
                && (!"finalScore".equals(query.getOrderField()))) {
            comQB.orderBy("r." + query.getOrderField() + " " + query.getOrderType());
        }
        comQB.setPage(query.getPageNo(), query.getPageSize());
        PagedList<EmpYearReportItemPo> reportPos = domainDao.listPage(comQB);
        if (CollUtil.isEmpty(reportPos.getData())) {
            return reportPos;
        }
        return reportPos;
    }

    /**
     * 构建员工状态筛选条件
     *
     * @param query 查询参数
     * @param comQB SQL 构建器
     */
    private void buildEmployeeStatusWhere(ReportQuery query, ComQB comQB) {
        List<String> onTheJobStatusList = query.getOnTheJobStatus();
        String empStatus = query.getEmpStatus();

        boolean hasOnTheJobStatus = CollUtil.isNotEmpty(onTheJobStatusList);
        boolean hasEmpStatus = StrUtil.isNotBlank(empStatus);

        if (hasOnTheJobStatus && !hasEmpStatus) {
            comQB.whereIn("e.on_the_job_status", onTheJobStatusList);
        } else if (hasOnTheJobStatus) {
            String inSql = StringTool.getInStrSql(onTheJobStatusList);
            comQB.appendWhere(" (e.on_the_job_status in " + inSql +
                    " or e.is_resigned in '" + StringTool.getInStrSql(toResignedValue(empStatus)) + "')");
        } else if (hasEmpStatus) {
            comQB.appendWhere("e.is_resigned in " + StringTool.getInStrSql(toResignedValue(empStatus)));
        }
    }

    /**
     * 将 empStatus 转换为 is_resigned 字段值
     * on_the_job => 0，leave => 1
     */
    private List<String> toResignedValue(String empStatus) {
        return "leave".equalsIgnoreCase(empStatus) ? Arrays.asList("1", "-1") : Arrays.asList("0");
    }



    private void handleEmpStatus(ReportQuery query, ComQB comQB) {
        if (StrUtil.isBlank(query.getEmpStatus())) {
            return;
        }
        if (StrUtil.isNotBlank(query.getEmpStatus()) && CollUtil.isEmpty(query.getOnTheJobStatus())) {
            boolean isOnTheJob = "on_the_job".equals(query.getEmpStatus());
            comQB.whereEq("e.is_delete", Boolean.toString(!isOnTheJob));
        }
    }

    private String isLeave(String empStatus) {
        return "leave".equals(empStatus) ? Boolean.TRUE.toString() : Boolean.FALSE.toString();
    }

    public String getCreateYearReportStatus(String companyId, String year) {
        ComQB comQB = ComQB.build(EmpYearReportDo.class, "r")
                .clearSelect().select("if(r.status=1,'finish','publish')").setRsType(String.class)
                .whereEq("r.company_id", companyId)
                .whereEq("r.year", year)
                .whereEq("r.is_deleted", Boolean.FALSE.toString());
        String one = domainDao.findOne(comQB);
        if (StrUtil.isBlank(one)) {
            return "finish";
        }
        return one;
    }

    //绩效分析根据条件先查询员工
    public List<String> pagedNeedReportEmpIds(ReportQuery query) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(PerfEvaluateTaskBaseDo.class, "b").appendOn("u.company_id = b.company_id and u.task_id = b.id and b.is_deleted = 'false'")
                .join(CycleDo.class, "c").appendOn("b.company_id = c.company_id and b.cycle_id = c.id")
                .clearSelect()
                .select(" distinct u.emp_id")
                .setRsType(String.class)
                .whereIn("u.emp_id", query.getEmpIds())
                .whereEq("u.company_id", query.getCompanyId())
                .whereIn("u.task_status", Arrays.asList("finished,resultsAuditing,resultsAffirming,waitPublish".split(",")))
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereEq("b.performance_type", query.getPerformanceType())
                .whereEq("c.year", query.getYear())
                .setPage(query.getPageNo(), query.getPageSize());
        return domainDao.listPage(comQB);
    }

    public List<EmpYearReportItem> listSummaryYearReport(TenantId companyId, String year, List<String> empIds, boolean isAvg, Integer performanceType) {
        Integer intYear = Integer.valueOf(year);
        ReportSqlBuilder sqlBuilder = new ReportSqlBuilder(intYear, empIds, isAvg);
        sqlBuilder.createQuerySql(companyId.getId(), performanceType);
        return domainDao.listAllDomain(sqlBuilder.getComQb(), EmpYearReportItem.class);
    }

    //等级分布
    public List<GradeDistributionPo> listGradeDistribution(ReportQuery query) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskBaseDo.class, "b")
                .join(PerfEvaluateTaskUserDo.class, "u")
                .appendOn("b.company_id = u.company_id AND u.task_id = b.id")
                .join(GradeStepDo.class, "gs")
                .appendOn("u.step_id = gs.id and u.company_id = gs.company_id")
                .leftJoin(CycleDo.class, "ec").appendOn("b.company_id = u.company_id AND b.cycle_id = ec.id")
                .clearSelect().select("u.step_id, u.evaluation_level as name, gs.sort as 'order', count(1) as value")
                .setRsType(GradeDistributionPo.class)
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereEq("b.is_deleted", Boolean.FALSE.toString())
                .whereEq("b.task_status", BusinessConstant.PUBLISHED)
                .whereIn("u.task_status", Arrays.asList(TalentStatus.FINISHED.getStatus(), TalentStatus.TERMINATED.getStatus()))
                .whereEq("b.company_id", query.getCompanyId())
                .whereEq("b.cycle_id", query.getCycleId());
        if (StringUtils.isNotEmpty(query.getTaskId())) {
            comQB.whereIn("b.id", Arrays.asList(query.getTaskId().split(",")));
            if (CollUtil.isNotEmpty(query.getAdminOrgIds())) {
                if (Objects.equals(query.getPerformanceType(), 2)) {
                    comQB.whereIn("u.eval_org_id", query.getAdminOrgIds());
                } else {
                    String sql = StringTool.getInStrSql(query.getAdminOrgIds());
                    comQB.appendWhere("(u.org_id in " + sql + " OR u.temp_task = 1)");
                }
            }
        }
        if (StringUtils.isNotEmpty(query.getTaskIds())) {
            comQB.whereIn("b.id", Arrays.asList(query.getTaskIds().split(",")));
        }
        //权限控制
        if (StringUtils.isNotBlank(query.getOrgId())) {
            comQB.appendWhere("   EXISTS(SELECT id FROM emp_ref_org WHERE company_id = '" + query.getCompanyId() + "' AND emp_id = u.emp_id " +
                    "AND ref_type = 'org' AND org_id in ('" + StringUtils.join(query.getOrgIds(), "', '") + "'))");
        }
        //时间段
        if (StringUtils.isNotBlank(query.getCycleStartDate()) && StringUtils.isNotBlank(query.getCycleEndDate())) {
            //            comQB.appendWhere("(ec.cycle_start BETWEEN '" + query.getCycleStartDate() + "' and '" + query.getCycleEndDate() + "' OR " +
//                    "ec.cycle_end BETWEEN '" + query.getCycleStartDate() + "' and '" + query.getCycleEndDate() + "')");
            comQB.appendWhere("(ec.cycle_start BETWEEN #{startDate} and #{endDate} OR ec.cycle_end BETWEEN #{cycleStartDate} and #{cycleEndDate})"
                    , query.getCycleStartDate(), query.getCycleEndDate(), query.getCycleStartDate(), query.getCycleEndDate());

        }
        // 角色
        if (StringUtils.isNotBlank(query.getRoleId())) {
            comQB.appendWhere("EXISTS (SELECT id FROM role_ref_emp WHERE company_id = '" + query.getCompanyId() + "' AND " +
                    "is_deleted = 'false' AND u.emp_id = emp_id AND role_id in " + StringTool.getInStrSql(query.getRoleId()) + ")");
        }
        comQB.groupBy("u.evaluation_level");
        return domainDao.listAll(comQB);
    }

    //部门分析等级
    public List<OrgAnaylsisStepPo> listOrgAnaylsisLevels(ReportQuery query) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskBaseDo.class, "b")
                .join(PerfEvaluateTaskUserDo.class, "u")
                .appendOn("b.company_id = u.company_id AND u.task_id = b.id")
                .join(GradeStepDo.class, "gs")
                .appendOn("u.step_id = gs.id and u.company_id = gs.company_id")
                .leftJoin(CycleDo.class, "ec").appendOn("b.company_id = u.company_id AND b.cycle_id = ec.id")
                .clearSelect().select("u.evaluation_level as stepName, gs.sort as 'order'")
                .setRsType(OrgAnaylsisStepPo.class)
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereEq("b.is_deleted", Boolean.FALSE.toString())
                .whereEq("b.company_id", query.getCompanyId())
                .whereEq("b.cycle_id", query.getCycleId());

        comQB.appendWhere(" u.evaluation_level is not null");
        comQB.groupBy("u.evaluation_level");
        comQB.orderBy("gs.sort");
        return domainDao.listAll(comQB);
    }

    //一级或者二级...组织，根据层高查询
    public PagedList<OrgAnaylsisStepExcelPo> pagedExporOrgLevelDistribution(ReportQuery query, List<OrgAnaylsisStepPo> stepPos) {
        PagedList<OrgAnaylsisStepExcelPo> excelPos = new PagedList<>();
        excelPos.setPageNo(query.getPageNo());
        excelPos.setPageSize(query.getPageSize());
        //查询数据
        PagedList<OrgGradeDistributionPo> pageds = pageOrgPathHigh(query);
        if (pageds.isEmpty()) {
            return excelPos;
        }
        //组装数据
        OrgAnaylsisStepExcelPo excelPo = new OrgAnaylsisStepExcelPo();
        excelPos.addAll(excelPo.buildData(pageds.getData(), query.getOrgLevelShowType(), stepPos));
        excelPos.setTotalPage(pageds.getTotalPage());
        excelPos.setTotalRow(pageds.getTotalRow());
        return excelPos;
    }

    //一级或者二级...组织，根据层高查询
    public PagedList<OrgGradeDistributionPo> pageOrgPathHigh(ReportQuery query) {
        ComQB comQB1 = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .clearSelect().select(" distinct SUBSTRING_INDEX(SUBSTRING_INDEX(u.at_org_code_path, '|', " + (query.getOrgPathHight() + 2) + "    ),'|',-1 ) AS org_id ,u.`company_id`")
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereEq("u.company_id", query.getCompanyId())
                .whereEq("u.cycle_id", query.getCycleId());

        ComQB qb = ComQB.build().fromQ(comQB1, "tu")
                .join(EmpOrganizationDo.class, "o").appendOn("o.company_id = tu.company_id AND o.org_id = tu.org_id")
                .clearSelect().select("tu.org_id,o.`org_name`")
                .setRsType(OrgGradeDistributionPo.class)
                .whereEq("tu.company_id", query.getCompanyId())
                .appendWhere(" tu.org_id is not null ");
        qb.setPage(query.getPageNo(), query.getPageSize());

        PagedList<OrgGradeDistributionPo> pagedList = domainDao.listPage(qb);
        if (pagedList.isEmpty()) {
            return pagedList;
        }
        //组装计算，人次和占比
        calPercent(pagedList.getData(), query.getCompanyId(), query.getCycleId(), query.getTaskIds());

        if (query.getPageNo() == 1) {
            //计算汇总人次,仅再第一页显示汇总
            OrgGradeDistributionPo sumPo = getSumOrgLevel(query);
            pagedList.getData().add(0, sumPo);
        }
        return pagedList;
    }

    //查询部门汇总等级分析
    public OrgGradeDistributionPo getSumOrgLevel(ReportQuery query) {
        OrgGradeDistributionPo orgSumNum = getOrgSumNum(query);

        if (Objects.isNull(orgSumNum)) {
            return new OrgGradeDistributionPo();
        }
        //组装计算，人次和占比
        List<String> taskStatusList = Arrays.asList(TalentStatus.FINISHED.getStatus(), TalentStatus.RESULTS_AUDITING.getStatus(),
                TalentStatus.WAIT_PUBLISHED.getStatus(), TalentStatus.RESULTS_APPEAL.getStatus(), TalentStatus.RESULTS_AFFIRMING.getStatus(), TalentStatus.RESULTS_INTERVIEW.getStatus(), TalentStatus.TERMINATED.getStatus());

        orgSumNum.setStepCntList(getOrgSumLevelNum(query.getCompanyId(), query.getCycleId(), query.getOrgPathHight(), taskStatusList));
        orgSumNum.setOrgName("汇总");
        orgSumNum.calSum();
        //计算汇总人次
        return orgSumNum;
    }


    private void calPercent(List<OrgGradeDistributionPo> list, String companyId, String cycleId, String taskIds) {
        List<String> taskStatusList = Arrays.asList(TalentStatus.FINISHED.getStatus(), TalentStatus.RESULTS_AUDITING.getStatus(),
                TalentStatus.WAIT_PUBLISHED.getStatus(), TalentStatus.RESULTS_APPEAL.getStatus(), TalentStatus.RESULTS_AFFIRMING.getStatus(), TalentStatus.RESULTS_INTERVIEW.getStatus(), TalentStatus.TERMINATED.getStatus());

        for (OrgGradeDistributionPo po : list) {
            OrgGradeDistributionPo cnt = getOrgNum(companyId, cycleId, po.getOrgId());
            if (Objects.isNull(cnt)) {
                continue;
            }
            po.setAssessNum(cnt.getAssessNum());
            po.setFinishedNum(cnt.getFinishedNum());
            //计算每个等级的人次和占比
            List<StepCntPercent> cntPercents = getOrgLevelNum(companyId, cycleId, po.getOrgId(), taskIds, taskStatusList);
            if (CollUtil.isEmpty(cntPercents)) {
                continue;
            }
            po.calStepPercent(cntPercents);
        }
    }

    //汇总层级数量
    public OrgGradeDistributionPo getOrgSumNum(ReportQuery query) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .clearSelect().select("count(u.id) as assessNum,COUNT( IF ( u.task_status in ('finished','resultsAuditing','resultsAffirming','waitPublish','resultsAppeal','resultsInterview','terminated') and u.final_score is not null, u.id, NULL ) ) AS finishedNum")
                .setRsType(OrgGradeDistributionPo.class)
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereEq("u.company_id", query.getCompanyId())
                .appendWhere("u.at_org_path_hight >= " + (query.getOrgPathHight() + 1) + "")
                .whereEq("u.cycle_id", query.getCycleId());
        return domainDao.findOne(comQB);
    }

    //一级或者二级...组织，根据层高查询完成次
    public OrgGradeDistributionPo getOrgNum(String companyId, String cycleId, String orgId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .clearSelect().select("count(u.id) as assessNum,COUNT( IF ( u.task_status in ('finished','resultsAuditing','resultsAffirming','waitPublish','resultsAppeal','resultsInterview','terminated') and u.final_score is not null, u.id, NULL ) ) AS finishedNum")
                .setRsType(OrgGradeDistributionPo.class)
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereEq("u.company_id", companyId)
                .appendWhere("INSTR(u.at_org_code_path, '" + orgId + "')")
                .whereEq("u.cycle_id", cycleId);
        return domainDao.findOne(comQB);
    }

    //计算某个组织下（包含子节点）等级人次
    public List<StepCntPercent> getOrgSumLevelNum(String companyId, String cycleId, Integer orgPathHight, List<String> taskStatusList) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .clearSelect().select("count(1) as num,u.cycle_id,u.evaluation_level as step_name")
                .setRsType(StepCntPercent.class)
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereEq("u.company_id", companyId)
                //   .whereIn("u.task_status", taskStatusList)
                .appendWhere("u.at_org_path_hight >= " + (orgPathHight + 1) + "")
                .whereEq("u.cycle_id", cycleId);
        if (CollUtil.isNotEmpty(taskStatusList)) {
            comQB.appendWhere(" (u.task_status in " + StringTool.getInStrSql(taskStatusList) +
                    " and u.final_score is not null)");
        }
        comQB.groupBy("u.evaluation_level");
        return domainDao.listAll(comQB);
    }

    //计算某个组织下（包含子节点）等级人次
    public List<StepCntPercent> getOrgLevelNum(String companyId, String cycleId, String orgId, String taskIds, List<String> taskStatusList) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .clearSelect().select("count(1) as num,u.cycle_id,u.evaluation_level as step_name")
                .setRsType(StepCntPercent.class)
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereEq("u.company_id", companyId)
                //.whereIn("u.task_status", taskStatusList)
                .appendWhere("INSTR(u.at_org_code_path, '" + orgId + "')")
                .whereEq("u.cycle_id", cycleId);

        if (CollUtil.isNotEmpty(taskStatusList)) {
            comQB.appendWhere(" (u.task_status in " + StringTool.getInStrSql(taskStatusList) +
                    " and u.final_score is not null)");
        }

        if (StringUtils.isNotEmpty(taskIds)) {
            comQB.whereIn("u.task_id", Arrays.asList(taskIds.split(",")));
        }
        comQB.groupBy("u.evaluation_level");
        return domainDao.listAll(comQB);
    }

    public PagedList<ReportItemAnalyseExcelPo> PageItemScoreExcel(TaskReportQry queryVO) {
        NativeSQLBuilder sqlBuilder = buildItemScoreRankSqlExcel(queryVO);
        sqlBuilder.setPage(queryVO.getPageNo(), queryVO.getPageSize());
        return buildItemScoreExcel(domainDao.listPage(sqlBuilder), queryVO);
    }

    /**
     * 格式化评分
     */
    public PagedList<ReportItemAnalyseExcelPo> buildItemScoreExcel(PagedList<ReportItemAnalyseExcelPo> pagedList, TaskReportQry queryVO) {
        if (CollUtil.isEmpty(pagedList)) {
            return pagedList;
        }// 提前返回,避免不带taskUserId条件查询
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "sc")
                .clearSelect().select("task_user_id,index_calibration")
                .whereEqReq("company_id",queryVO.getCompanyId())
                .whereIn("task_user_id", pagedList.getData().stream().map(obj -> obj.getTaskUserId()).distinct().collect(Collectors.toList()))
                .whereEq("scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene())
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEq("audit_status", "pass")
                .orderBy("approval_order desc")
                .limit(0, 999999);
        ComQB resultQb = ComQB.build().fromQ(comQB, "a").setRsType(PerfEvaluateTaskScoreResultDo.class)
                .groupBy("a.task_user_id");
        List<PerfEvaluateTaskScoreResultDo> resultDos = domainDao.listAll(resultQb);
        if (resultDos != null && resultDos.size() > 0) {
            pagedList.getData().forEach(obj -> {
                resultDos.forEach(item -> {
                    if (obj.getTaskUserId().equals(item.getTaskUserId()) && item.getIndexCalibration() != null && item.getIndexCalibration().size() > 0) {
                        Map<String, IndexCalibration> map = item.getIndexCalibration().stream().collect(Collectors.toMap(IndexCalibration::getKpiItemId, Function.identity()));
                        if (map.get(obj.getKpiItemId()) != null) {
                            IndexCalibration indexCalibration = map.get(obj.getKpiItemId());
                            obj.setItemScore(indexCalibration.getScore());
                        }
                    }
                });
            });
            if ("score".equals(queryVO.getOrderField())) {
                pagedList.getData().sort(Comparator.comparing(ReportItemAnalyseExcelPo::getItemScore).reversed());
            }
        }
        return pagedList;
    }


    private NativeSQLBuilder buildItemScoreRankSqlExcel(TaskReportQry queryVO) {
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(ReportItemAnalyseExcelPo.class).setSql("SELECT * FROM (SELECT u.emp_id,if(b.performance_type = 1,e.`name`,u.eval_org_name) as empName,e.`avatar` as emp_avatar,b.task_name,if(k.scorer_type = 'auto', IFNULL(k.item_auto_score,0), IFNULL(s.score,0)) AS itemScore,k.item_target_value,k.item_finish_value,k.item_unit,k.task_id,k.task_user_id,k.kpi_item_id,k.finish_value_type as finishValueType,  " +
                "(if(b.performance_type = 1,(select GROUP_CONCAT(o.org_name) FROM emp_organization o WHERE o.company_id = #{companyId} and o.org_id in (select ro.org_id from emp_ref_org ro where ro.emp_id = u.emp_id and ro.ref_type = 'org')),(select GROUP_CONCAT(o.org_name) FROM emp_organization o WHERE o.company_id = #{companyId} and o.org_id in (select parent_org_id from emp_organization where org_id = u.eval_org_id)))) as orgName,  " +
                "if(b.performance_type = 1,(SELECT GROUP_CONCAT(r.role_name) FROM role r WHERE r.company_id = #{companyId} and r.is_deleted = 'false' AND r.id in (SELECT role_id FROM role_ref_emp WHERE company_id = #{companyId} AND is_deleted = 'false' AND emp_id = u.emp_id)),null) as roleName  " +
                "FROM perf_evaluate_task_user u  " +
                "JOIN perf_evaluate_task_base b ON b.company_id = #{companyId} AND u.task_id = b.id  " +
                "JOIN perf_evaluate_cycle ec ON b.company_id = #{companyId} AND b.cycle_id = ec.id " +
                "JOIN employee_base_info e ON e.company_id = #{companyId} AND e.employee_id = u.emp_id  " +
                "LEFT JOIN (SELECT s.emp_id,s.task_id,SUM(IFNULL(s.no_item_score,0) + IFNULL(s.final_plus_score, 0) + IFNULL(s.final_subtract_score, 0)) as score   " +
                "FROM perf_evaluate_task_score_result s WHERE s.company_id = #{companyId}   " +
                "AND s.kpi_item_id = #{kpiItemId} and s.is_deleted = 'false' GROUP BY s.emp_id,s.task_id  " +
                ") s ON u.task_id = s.task_id AND u.emp_id = s.emp_id  " +
                "JOIN perf_evaluate_task_kpi k ON k.company_id = #{companyId} AND u.id = k.task_user_id AND k.is_deleted = 'false' AND k.kpi_item_id = #{kpiItemId} " +
                "WHERE u.company_id = #{companyId} AND u.is_deleted = 'false' AND b.is_deleted = 'false' AND b.task_status = 'published' AND u.task_status = 'finished'");
        setTaskUserCountParam(queryVO, sqlBuilder);
        sqlBuilder.setValue("kpiItemId", queryVO.getItemId());
        sqlBuilder.append(")t");
        if ("finishValue".equals(queryVO.getOrderField())) {
            sqlBuilder.append("ORDER BY item_finish_value ").append(queryVO.getOrderType());
        } else if ("targetValue".equals(queryVO.getOrderField())) {
            sqlBuilder.append("ORDER BY item_target_value").append(queryVO.getOrderType());
        } else if ("score".equals(queryVO.getOrderField())) {
            sqlBuilder.append("ORDER BY itemScore").append(queryVO.getOrderType());
        } else {
            sqlBuilder.append("ORDER BY task_id DESC");
        }
        return sqlBuilder;
    }

    private void setTaskUserCountParam(TaskReportQry queryVO, NativeSQLBuilder sqlBuilder) {
        sqlBuilder.appendIfOpt("AND ec.type = #{type}", "type", queryVO.getCycleType());
        if (StringUtils.isNotBlank(queryVO.getCycleStartDate()) && StringUtils.isNotBlank(queryVO.getCycleEndDate())) {
            sqlBuilder.append("AND (ec.cycle_start BETWEEN #{cycleStartDate} and #{cycleEndDate} OR ec.cycle_end BETWEEN #{cycleStartDate} and #{cycleEndDate})");
            sqlBuilder.setValue("cycleStartDate", queryVO.getCycleStartDate());
            sqlBuilder.setValue("cycleEndDate", queryVO.getCycleEndDate());
        }
        if (StringUtils.isNotBlank(queryVO.getCycleId())) {
            sqlBuilder.append("AND b.cycle_id in").append(StringTool.getInStrSql(queryVO.getCycleId()));
        }
        if (StringUtils.isNotBlank(queryVO.getOrgId())) {
            sqlBuilder.append("AND EXISTS (SELECT id FROM emp_ref_org WHERE company_id = #{companyId} AND emp_id = u.emp_id AND ref_type = 'org' AND org_id in").append(StringTool.getInStrSql(queryVO.getOrgId())).append(")");
        }
        if (StringUtils.isNotBlank(queryVO.getRoleId())) {
            sqlBuilder.append("AND EXISTS (SELECT id FROM role_ref_emp WHERE company_id = #{companyId} AND is_deleted = 'false' AND u.emp_id = emp_id AND role_id in").append(StringTool.getInStrSql(queryVO.getRoleId())).append(")");
        }
        if (StringUtils.isNotBlank(queryVO.getTaskId())) {
            sqlBuilder.append("AND u.task_id in").append(StringTool.getInStrSql(queryVO.getTaskId()));
        }
        if (StringUtils.isNotBlank(queryVO.getTaskIds()) && StringUtils.isBlank(queryVO.getTaskId())) {
            sqlBuilder.append("AND u.task_id in").append(StringTool.getInStrSql(queryVO.getTaskIds()));
        }
        if (StringUtils.isNotBlank(queryVO.getEmpId())) {
            sqlBuilder.append("AND u.emp_id in").append(StringTool.getInStrSql(queryVO.getEmpId()));
            sqlBuilder.append(" and u.eval_org_id is null ");
        }
        if (StrUtil.isNotBlank(queryVO.getEvalOrgId())) {
            sqlBuilder.append(" and u.eval_org_id = #{evalOrgId} ");
        }
        if (StringUtils.isNotBlank(queryVO.getLevel())) {
            sqlBuilder.append("AND u.evaluation_level in").append(StringTool.getInStrSql(queryVO.getLevel()));
        }

        sqlBuilder.setValue("companyId", queryVO.getCompanyId());
        sqlBuilder.setValue("evalOrgId", queryVO.getEvalOrgId());
    }

    //从adminSetDao迁移过来, 解决依赖问题,先存放于此
    public void getReportAuth(ReportQuery query) {
        log.info("query参数：{}", JSONObject.toJSON(query));
        query.setNoOrgPerm(false);
        SystemAdminSetModel admin = findByEmpId(query.getCompanyId(), query.getLoginEmpId());
        if (Objects.isNull(admin)) {
            query.setNoOrgPerm(true);
            return;
        }
        if (Arrays.asList(AdminType.MAIN, AdminType.APP).contains(admin.getAdminType())
                || SystemAdminSetModel.ManagePurview.COMPANY.equals(admin.getManagePurview().getManageScope())) {
            return;
        }
        List<String> orgIdList = extractManageOrgId(admin);
        if (SystemAdminSetModel.ManagePurview.CURRENT_ORG.equals(admin.getManagePurview().getManageScope())) {
            List<String> queryOrgIds = query.getOrgIds().stream().filter(o -> orgIdList.contains(o)).collect(Collectors.toList());
            if (CollUtil.isEmpty(queryOrgIds)) {
                query.setNoOrgPerm(true);
                return;
            }
            query.setOrgIds(queryOrgIds);
            return;
        }
        List<ObjItem> items = listAllChildOrg(new TenantId(query.getCompanyId()), new ArrayList<>(orgIdList));
        List<String> allOrgIds = items.stream().map(item -> item.getObjId()).distinct().collect(Collectors.toList());
        log.info("获取有权限的部门：{}", JSONObject.toJSON(allOrgIds));
        if (CollectionUtils.isEmpty(query.getOrgIds())) {
            query.setOrgIds(allOrgIds);
            if (CollectionUtils.isEmpty(query.getOrgIds())) {
                query.setNoOrgPerm(true);
            }
            return;
        }
        List<String> queryOrgIds = query.getOrgIds().stream().filter(o -> allOrgIds.contains(o)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(queryOrgIds)) {
            query.setNoOrgPerm(true);
            return;
        }
        query.setOrgIds(queryOrgIds);
        log.info("请求参数11：{}", JSONObject.toJSON(query));
    }

    //查询所有子级部门
    public List<ObjItem> listAllChildOrg(TenantId tenantId, List<String> orgIds) {
        if (CollUtil.isEmpty(orgIds)) {
            return new ArrayList<>();
        }
        ComQB orgComQb = ComQB.build(EmpOrganizationDo.class, "s")
                .join(EmpOrganizationDo.class, "p")
                .appendOn("s.company_id=p.company_id and instr(s.org_code,p.org_code) ")
                .clearSelect().select("s.org_id as objId,s.org_name as objName")
                .setRsType(ObjItem.class)
                .whereIn(" p.org_id", orgIds)
                .whereEq("p.company_id", tenantId.getId())
                .appendWhere("s.status='valid'")
                .groupBy(" s.org_id");
        return domainDao.listAll(orgComQb);
    }

    public List<String> extractManageOrgId(SystemAdminSetModel adminSetModel) {
        List<String> orgIdSet = new ArrayList<>();
        if (adminSetModel != null && StringUtils.isNotBlank(adminSetModel.getCompanyId())) {
            ComQB<EmpOrganizationModel> orgComQB = ComQB.build(EmpOrganizationModel.class)
                    .whereEq("company_id", adminSetModel.getCompanyId())
                    .whereEq("status", "valid");
            List<EmpOrganizationModel> orgList = domainDao.listAll(orgComQB);
            if (CollectionUtils.isNotEmpty(orgList)) {
                if (Arrays.asList(AdminType.MAIN, AdminType.APP).contains(adminSetModel.getAdminType())
                        || SystemAdminSetModel.ManagePurview.COMPANY.equals(adminSetModel.getManagePurview().getManageScope())) {
                    orgIdSet.addAll(orgList.stream().map(EmpOrganizationModel::getOrgId).collect(Collectors.toList()));
                } else if (SystemAdminSetModel.ManagePurview.APPOINT_ORG.equals(adminSetModel.getManagePurview().getManageScope())) {
                    String orgIds = adminSetModel.getManagePurview().getOrgIds();
                    if (StringUtils.isNotBlank(orgIds)) {
                        orgIdSet.addAll(Arrays.asList(orgIds.split(",")));
                    }
                } else {
                    if (StringUtils.isNotBlank(adminSetModel.getEmpId())) {
                        //查所在部门
                        ComQB<EmpRefOrgModel> refOrgQB = ComQB.build(EmpRefOrgModel.class).whereEq("emp_id", adminSetModel.getEmpId())
                                .whereEq("ref_type", "org");
                        List<EmpRefOrgModel> refList = domainDao.listAll(refOrgQB);
                        if (CollectionUtils.isNotEmpty(refList)) {
                            if (SystemAdminSetModel.ManagePurview.CURRENT_LOW_ORG.equals(adminSetModel.getManagePurview().getManageScope())) {
                                refList.forEach(r -> {
                                    List<EmpOrganizationModel> orgIds = orgList.stream().filter(o -> o.getOrgCode().contains(r.getOrgId())).collect(Collectors.toList());
                                    List<String> subOrgIdList = null;
                                    if (CollectionUtils.isNotEmpty(orgIds)) {
                                        subOrgIdList = orgIds.stream().map(EmpOrganizationModel::getOrgId).collect(Collectors.toList());
                                    }
                                    //List<String> subOrgIdList = orgList.stream().filter(o -> o.getOrgCode().contains(r.getOrgId()))
                                    //        .map(EmpOrganizationModel::getOrgId).collect(Collectors.toList());
                                    if (CollectionUtils.isNotEmpty(subOrgIdList)) {
                                        orgIdSet.addAll(subOrgIdList);
                                    }
                                });
                            } else if (SystemAdminSetModel.ManagePurview.CURRENT_ORG.equals(adminSetModel.getManagePurview().getManageScope())) {
                                orgIdSet.addAll(refList.stream().map(EmpRefOrgModel::getOrgId).collect(Collectors.toList()));
                            }
                        }
                    }
                }

            }
        }
        return orgIdSet;
    }

    public SystemAdminSetModel findByEmpId(String companyId, String empId) {
        if (StringUtils.isAnyBlank(companyId, empId)) {
            return null;
        }
        ComQB<SystemAdminSetModel> comQB = ComQB.build(SystemAdminSetModel.class)
                .whereEq("company_id", companyId)
                .whereEq("emp_id", empId)
                .whereEq("status", "valid");
        return domainDao.findOne(comQB);
    }

    public void relaceSave(EmpYearReport yearReport) {
        ComQB existQb = ComQB.build(EmpYearReportDo.class, "y")
                .whereEq("company_id", yearReport.getCompanyId().getId())
                .whereEq("year", yearReport.getYear());
        EmpYearReportDo exist = domainDao.findOne(existQb);
        if (exist != null) {
            yearReport.setId(exist.getId());
            yearReport.setCreatedTime(exist.getCreatedTime());
            yearReport.setCreatedUser(exist.getCreatedUser());
        } else {
            yearReport.setId(domainDao.nextLongAsStr(diffLog));
        }
        EmpYearReportDo data = new ToDataBuilder<>(yearReport, EmpYearReportDo.class).data();
        domainDao.saveReplace(data);
    }

    public void saveEmpReportItems(EmpYearReport yearReport, List<EmpYearReportItem> empReportItems) {
        for (EmpYearReportItem item : empReportItems) {
            item.setReportId(yearReport.getId());
            item.setCompanyId(yearReport.getCompanyId());
            item.setId(domainDao.nextLongAsStr(diffLog));
            item.setCreatedUser(yearReport.getUpdatedUser());
        }
        domainDao.addBatch(EmpYearReportItemDo.class, empReportItems);
    }

    public void clearEmpReportItems(EmpYearReport yearReport) {
        this.relaceSave(yearReport);
        DeleteBuilder del = DeleteBuilder.build("emp_year_report_item")
                .whereEqReq("company_id", yearReport.getCompanyId().getId())
                .whereEqReq("report_id", yearReport.getId());
        domainDao.delete(del);
    }

    public Cycle getNewestCycle(PerfAnalysisQuery query) {
        ComQB cycleQB = ComQB.build(CycleDo.class)
                .whereEqReq("company_id", query.getCompanyId())
                .whereIn("id",query.getCycleIds())
                .orderByDesc("created_time")
                .limit(0,1);
        return domainDao.findDomain(cycleQB,Cycle.class);
    }

    public List<String> getChildAdminCycleIds(PerfAnalysisQuery query) {
        ComQB comQB = ComQB.build(AdminOfCycleDo.class)
                .clearSelect().select(" cycle_id ")
                .setRsType(String.class)
                .whereEqReq("company_id", query.getCompanyId())
                .whereEqReq("emp_id",query.getOpEmpId())
                .whereEqReq("is_deleted", "false");
        return domainDao.listAll(comQB);
    }
}
