//package com.polaris.kpi.eval.infr.statics.dao;
//
//import com.polaris.kpi.eval.infr.statics.ppojo.ConsecutiveEmpDetailDo;
//import com.polaris.kpi.eval.infr.statics.ppojo.ConsecutiveEmpDetailPo;
//import com.polaris.kpi.eval.infr.task.query.report.PerfAnalysisQuery;
//import org.lufei.ibatis.builder.ComQB;
//import org.lufei.ibatis.dao.DomainDaoImpl;
//import org.lufei.ibatis.mapper.PagedList;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.List;
//
///**
// * <AUTHOR>
// * @date 2025/2/23 22:32
// */
//@Component
//public class ConsecutiveEmpDetailDao extends StaticBaseDao{
//
//    @Resource
//    private DomainDaoImpl autoBaseDao;
//
//    public Integer countResult(PerfAnalysisQuery qry) {
//
//        ComQB comQB = ComQB.build(ConsecutiveEmpDetailDo.class, "a")
//                .clearSelect().select(" count(*) ")
//                .setRsType(Integer.class)
//                .whereEqReq("company_id", qry.getCompanyId())
//                .whereEqReq("cycle_id", qry.getCycleId())
//                .whereEqReq("performance_type", qry.getPerformanceType())
//                .whereEqReq("consecutive_type", qry.getRuleType())
//                .whereEqReq("is_deleted", Boolean.FALSE.toString());
//        addCommonCondition(qry, comQB);
//        return autoBaseDao.findOne(comQB);
//    }
//
//    public List<String> getHitRuleDetails(PerfAnalysisQuery query) {
//
//        ComQB comQB = ComQB.build(ConsecutiveEmpDetailDo.class , "a")
//                .clearSelect().select("perf_statistic_rule")
//                .setRsType(String.class)
//                .whereEqReq("company_id", query.getCompanyId())
//                .whereEqReq("cycle_id", query.getCycleId())
//                .whereEqReq("performance_type", query.getPerformanceType())
//                .whereEqReq("consecutive_type", query.getRuleType())
//                .whereEqReq("is_deleted", Boolean.FALSE.toString())
//                .groupBy("perf_statistic_rule ");
//        addCommonCondition(query, comQB);
//        return autoBaseDao.listAll(comQB);
//    }
//
//    public PagedList<ConsecutiveEmpDetailPo> pagedConsecutiveEmpDetails(PerfAnalysisQuery query) {
//
//        ComQB comQB = ComQB.build(ConsecutiveEmpDetailDo.class, "a")
//                .clearSelect().select("*")
//                .setRsType(ConsecutiveEmpDetailPo.class)
//                .whereEqReq("company_id", query.getCompanyId())
//                .whereEqReq("cycle_id", query.getCycleId())
//                .whereEqReq("performance_type", query.getPerformanceType())
//                .whereEqReq("consecutive_type", query.getRuleType())
//                .whereEqReq("is_deleted", Boolean.FALSE.toString())
//                .orderByDesc("final_score")
//                .setPage(query.getPageNo(), query.getPageSize());
//        if (query.getRuleConfigId() == null){
//            comQB.whereIsNull("hit_rule_id");
//        }else {
//            comQB.whereEqReq("hit_rule_id",query.getRuleConfigId());
//        }
//        addCommonCondition(query, comQB);
//        return autoBaseDao.listPage(comQB);
//    }
//}
