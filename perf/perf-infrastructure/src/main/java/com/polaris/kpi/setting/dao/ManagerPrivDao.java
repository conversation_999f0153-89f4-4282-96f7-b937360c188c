package com.polaris.kpi.setting.dao;

import cn.com.polaris.kpi.AppointScope;
import cn.com.polaris.kpi.KpiDept;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.polaris.kpi.eval.infr.task.query.empeval.MyManagerEmpEvalQuery;
import com.polaris.kpi.eval.infr.task.query.report.PerfAnalysisQuery;
import com.polaris.kpi.org.domain.emp.entity.KpiEmployee;
import com.polaris.kpi.org.infr.company.ppojo.CompanySysSettingDo;
import com.polaris.kpi.org.infr.emp.pojo.EmpOrganizationDo;
import com.polaris.kpi.org.infr.emp.pojo.EmpRefOrgDo;
import com.polaris.kpi.org.infr.emp.pojo.EmployeeBaseInfoDo;
import com.polaris.kpi.setting.domain.type.OperationType;
import com.polaris.kpi.setting.ppojo.ManagerPrivDo;
import com.polaris.kpi.setting.ppojo.ManagerPrivLogDo;
import com.polaris.kpi.setting.ppojo.ManagerPrivLogPo;
import com.polaris.kpi.setting.ppojo.ManagerPrivPo;
import com.polaris.kpi.setting.query.ManagerPrivLogQuery;
import com.polaris.kpi.setting.query.ManagerPrivQuery;
import lombok.extern.slf4j.Slf4j;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.lufei.ibatis.mapper.PagedList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class ManagerPrivDao {
    @Autowired
    private DomainDaoImpl domainDao;

    public ManagerPrivPo findById(String id) {
        if (Objects.isNull(id)) {
            return null;
        }
        ComQB comQB = ComQB.build(ManagerPrivDo.class,"p")
                .clearSelect().select("p.*").setRsType(ManagerPrivPo.class)
                .whereEq("p.id", id);
        return domainDao.findOne(comQB);
    }

    public ManagerPrivPo findByEmpId(String companyId, String empId) {
        if (Objects.isNull(empId)) {
            return null;
        }
        ComQB comQB = ComQB.build(ManagerPrivDo.class,"p")
                .clearSelect().select("p.*").setRsType(ManagerPrivPo.class)
                .whereEq("p.company_id",companyId)
                .whereEq("p.emp_id", empId)
                .whereEq("p.is_deleted",Boolean.FALSE.toString());
        return domainDao.findOne(comQB);
    }

    public List<ManagerPrivPo> listByIdList(String companyId, List<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        ComQB comQB = ComQB.build(ManagerPrivDo.class, "p")
                .join(EmployeeBaseInfoDo.class, "e").appendOn("p.company_id = e.company_id and p.emp_id = e.employee_id")
                .clearSelect().select("p.*,e.name as managerEmpName ").setRsType(ManagerPrivPo.class)
                .whereEqReq("p.company_id", companyId)
                .whereInReq("p.id", ids);
        return domainDao.listAll(comQB);
    }

    public PagedList<ManagerPrivPo> pagedManagerPrivList(ManagerPrivQuery query) {
        ComQB comQB = ComQB.build(ManagerPrivDo.class,"p")
                .join(EmployeeBaseInfoDo.class,"e").appendOn("p.company_id = e.company_id and p.emp_id = e.employee_id and is_delete = 'false'")
                .join(EmpRefOrgDo.class,"f").appendOn("p.company_id = f.company_id and p.emp_id = f.emp_id and f.ref_type = 'org'")
                .join(EmpOrganizationDo.class,"o").appendOn("f.company_id = o.company_id and f.org_id = o.org_id")
                .clearSelect().select("p.*")
                .select("GROUP_CONCAT(o.org_name) as orgName")
                .select("e.name as managerEmpName ").setRsType(ManagerPrivPo.class)
                .whereEq("p.company_id", query.getCompanyId())
                .whereEq("e.is_resigned", Boolean.FALSE.toString())
                .whereEq("p.is_deleted", Boolean.FALSE.toString());
        if (CollUtil.isNotEmpty(query.getEmpIds())) {
            comQB.whereIn("p.emp_id",query.getEmpIds());
        }
        if (CollUtil.isNotEmpty(query.getOrgIds())) {
            comQB.whereIn("f.org_id",query.getOrgIds());
        }
        comQB.groupBy("p.id");
        comQB.orderByDesc("e.created_time");
        comQB.setPage(query.getPageNo(), query.getPageSize());
        return domainDao.listPage(comQB);
    }

    public List<String> listEmpIdAll(String companyId) {
        ComQB comQB = ComQB.build(ManagerPrivDo.class)
                .clearSelect().select("emp_id").setRsType(String.class)
                .whereEq("company_id", companyId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        return domainDao.listAll(comQB);
    }

    public boolean isOpenIManage(String companyId,String opEmpId) {
        ComQB openComQB = ComQB.build(CompanySysSettingDo.class)
                .clearSelect().select("count(id)").setRsType(Integer.class)
                .whereEq("company_id", companyId)
                .whereEq("setting_type","managerPriv")
                .whereEq("value","1")
                .whereEq("is_deleted", Boolean.FALSE.toString());
        Integer openCount = domainDao.findOne(openComQB);
        if (openCount == 0) {
            return false;
        }
        ComQB comQB = ComQB.build(ManagerPrivDo.class)
                .clearSelect().select("count(id)").setRsType(Integer.class)
                .whereEq("company_id", companyId)
                .whereEq("emp_id",opEmpId)
                .whereEq("status",1)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        Integer userOpenCount = domainDao.findOne(comQB);
        if (userOpenCount == 0 ) {
            return false;
        }
        return true;
    }


    public PagedList<ManagerPrivLogPo> pagedManagerPrivLog(ManagerPrivLogQuery query) {
        ComQB comQB = ComQB.build(ManagerPrivLogDo.class,"p")
                .join(EmployeeBaseInfoDo.class,"e").appendOn("p.company_id = e.company_id and p.operation_emp_id = e.employee_id")
                .clearSelect().select("p.*")
                .select("e.name as operationEmpName,e.avatar as operationEmpAvatar ").setRsType(ManagerPrivLogPo.class)
                .whereEq("p.company_id", query.getCompanyId())
                .whereIn("p.operation_emp_id", query.getOperationEmpIds());
        if (StrUtil.isNotBlank(query.getStartDate())) {
            comQB.appendWhere("DATE_FORMAT(p.created_time,'%Y-%m-%d') >= '"+query.getStartDate()+"'");
        }
        if (StrUtil.isNotBlank(query.getEndDate())) {
            comQB.appendWhere("DATE_FORMAT(p.created_time,'%Y-%m-%d') <= '"+query.getEndDate()+"'");
        }
        //排除系统同步的日志
        comQB.whereNotIn("p.operation_type", Arrays.asList(OperationType.SYN_EVENT_ADD.getType(),OperationType.SYN_EVENT_UP.getType()));
        comQB.orderByDesc("p.created_time");
        comQB.setPage(query.getPageNo(), query.getPageSize());
        return domainDao.listPage(comQB);
    }

    /**查询员工信息及员工类型*/
    public KpiEmployee findEmp(String companyId, String empId){
        ComQB comQB = ComQB.build(EmployeeBaseInfoDo.class)
                .clearSelect().select("employee_id as id,name,ding_user_id").setRsType(KpiEmployee.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("employee_id", empId)
                .limit(0, 1);
        KpiEmployee emp = domainDao.findOne(comQB);
        /**查询是否为部门主管*/
        ComQB manangerQB = ComQB.build(EmpRefOrgDo.class)
                .clearSelect().select("count(id)").setRsType(Integer.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("emp_id", empId)
                .whereEq("ref_type","manager");
        Integer manangerNum = domainDao.findOne(manangerQB);
        /**查询是否直属主管*/
        ComQB superQB = ComQB.build(EmployeeBaseInfoDo.class)
                .clearSelect().select("count(id)").setRsType(Integer.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("ding_manager_id", emp.getDingUserId());
        Integer superNum = domainDao.findOne(superQB);
        emp.initEmpPrivType(manangerNum,superNum);
        return emp;
    }

    /**
     * 组装当前管理人权限
     * @param query
     * @return
     */
    public MyManagerEmpEvalQuery builderManagerPriv(MyManagerEmpEvalQuery query) {
        ManagerPrivPo priv = findByEmpId(query.getCompanyId(),query.getOpEmpId());
        if (Objects.isNull(priv)) {
            return query;
        }
        List<String> privOrgIds = new ArrayList<>();
        List<String> privEmpIds = new ArrayList<>();
        List<String> orgIdList = new ArrayList<>();
        if (priv.isManagerPriv()) {
            ComQB manangerQB = ComQB.build(EmpRefOrgDo.class)
                    .clearSelect().select("org_id").setRsType(String.class)
                    .whereEqReq("company_id", query.getCompanyId())
                    .whereEqReq("emp_id", query.getOpEmpId())
                    .whereEq("ref_type","manager");
            orgIdList = domainDao.listAll(manangerQB);
            if (CollUtil.isNotEmpty(orgIdList)) {
                for (String s : orgIdList) {
                    /**查询当前部门以及下级部门*/
                    ComQB orgQB = ComQB.build(EmpOrganizationDo.class)
                            .clearSelect().select("org_id").setRsType(String.class)
                            .whereEqReq("company_id", query.getCompanyId())
                            .appendWhere("instr(org_code, '"+s+"')");
                    privOrgIds.addAll(domainDao.listAll(orgQB));
                }
            }
        }
        if (priv.isSuperPriv()) {
            ComQB empQB = ComQB.build(EmployeeBaseInfoDo.class,"e")
                    .leftJoin(EmployeeBaseInfoDo.class,"eb").appendOn("e.company_id = eb.company_id and e.ding_user_id = eb.ding_manager_id")
                    .clearSelect().select("eb.employee_id").setRsType(String.class)
                    .whereEqReq("e.company_id", query.getCompanyId())
                    .whereEqReq("e.employee_id", query.getOpEmpId());
            privEmpIds.addAll(domainDao.listAll(empQB));
        }
        //指定范围权限
        AppointScope appointScope = priv.getPrivConf().getAppointScope();
        if (Objects.nonNull(appointScope)) {
            //指定部门
            if (CollUtil.isNotEmpty(appointScope.getAppointOrg())) {
                for (KpiDept kpiDept : appointScope.getAppointOrg()) {
                    /**查询当前部门以及下级部门*/
                    ComQB orgQB = ComQB.build(EmpOrganizationDo.class)
                            .clearSelect().select("org_id").setRsType(String.class)
                            .whereEqReq("company_id", query.getCompanyId())
                            .appendWhere("instr(org_code, '"+kpiDept.getOrgId()+"')");
                    privOrgIds.addAll(domainDao.listAll(orgQB));
                }
            }
            //指定人员
            privEmpIds.addAll(CollUtil.map(appointScope.getAppointEmp(),e -> e.getEmpId(),true));
        }
        //如果是组织绩效，需排除管理的部门
        if (query.getPerformanceType() == 2 ) {
           privOrgIds.removeAll(orgIdList);
        }
        query.setPriv(privEmpIds,privOrgIds);
        return query;
    }

    /**
     * 组装当前管理人权限
     * @param query
     * @return
     */
    public PerfAnalysisQuery builderManagerPriv(PerfAnalysisQuery query) {
        ManagerPrivPo priv = findByEmpId(query.getCompanyId(),query.getOpEmpId());
        if (Objects.isNull(priv)) {
            return query;
        }
        List<String> privOrgIds = new ArrayList<>();
        List<String> privEmpIds = new ArrayList<>();
        List<String> orgIdList = new ArrayList<>();
        if (priv.isManagerPriv()) {
            ComQB manangerQB = ComQB.build(EmpRefOrgDo.class)
                    .clearSelect().select("org_id").setRsType(String.class)
                    .whereEqReq("company_id", query.getCompanyId())
                    .whereEqReq("emp_id", query.getOpEmpId())
                    .whereEq("ref_type","manager");
            orgIdList = domainDao.listAll(manangerQB);
            if (CollUtil.isNotEmpty(orgIdList)) {
                for (String s : orgIdList) {
                    /**查询当前部门以及下级部门*/
                    ComQB orgQB = ComQB.build(EmpOrganizationDo.class)
                            .clearSelect().select("org_id").setRsType(String.class)
                            .whereEqReq("company_id", query.getCompanyId())
                            .appendWhere("instr(org_code, '"+s+"')");
                    privOrgIds.addAll(domainDao.listAll(orgQB));
                }
            }
        }
        if (priv.isSuperPriv()) {
            ComQB empQB = ComQB.build(EmployeeBaseInfoDo.class,"e")
                    .leftJoin(EmployeeBaseInfoDo.class,"eb").appendOn("e.company_id = eb.company_id and e.ding_user_id = eb.ding_manager_id")
                    .clearSelect().select("eb.employee_id").setRsType(String.class)
                    .whereEqReq("e.company_id", query.getCompanyId())
                    .whereEqReq("e.employee_id", query.getOpEmpId());
            privEmpIds.addAll(domainDao.listAll(empQB));
        }
        //指定范围权限
        AppointScope appointScope = priv.getPrivConf().getAppointScope();
        if (Objects.nonNull(appointScope)) {
            //指定部门
            if (CollUtil.isNotEmpty(appointScope.getAppointOrg())) {
                for (KpiDept kpiDept : appointScope.getAppointOrg()) {
                    /**查询当前部门以及下级部门*/
                    ComQB orgQB = ComQB.build(EmpOrganizationDo.class)
                            .clearSelect().select("org_id").setRsType(String.class)
                            .whereEqReq("company_id", query.getCompanyId())
                            .appendWhere("instr(org_code, '"+kpiDept.getOrgId()+"')");
                    privOrgIds.addAll(domainDao.listAll(orgQB));
                }
            }
            //指定人员
            privEmpIds.addAll(CollUtil.map(appointScope.getAppointEmp(),e -> e.getEmpId(),true));
        }
        //如果是组织绩效，需排除管理的部门
        if (query.getPerformanceType() == 2 ) {
            privOrgIds.removeAll(orgIdList);
        }
        query.setPriv(privEmpIds,privOrgIds);
        return query;
    }
}
