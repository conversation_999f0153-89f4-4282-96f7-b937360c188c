package com.polaris.kpi.setting.ppojo;

import cn.com.polaris.kpi.ManagerScope;
import com.polaris.kpi.common.infr.DelData;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.ibatis.annotations.Ckey;
import org.apache.ibatis.annotations.JsonColumn;

@Setter
@Getter
@NoArgsConstructor
public class ManagerPrivDo extends DelData {

    @Ckey
    private String id;
    private String empId;
    private Integer managerType;  //管理类型 1=主管管理  2=直属主管 4=普通员工
    @JsonColumn
    private ManagerScope privConf;      //权限配置
    private Integer status;         //0=关闭  1=开启

    public boolean wasOpen() {
        return status == 1;
    }
}
