package com.polaris.kpi.common.infr;

import cn.hutool.core.util.IdUtil;
import com.alibaba.cola.event.DomainEventI;
import com.alibaba.cola.event.EventBusI;
import com.alibaba.cola.event.EventI;
import com.polaris.kpi.org.domain.common.DomainEventPublisherI;
import com.polaris.kpi.org.type.TraceKey;
import com.polaris.sdk.type.TenantId;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.CompletableFuture;

/**
 * 本地事件发送器
 */
@Component
public class DomainEventPublisher implements DomainEventPublisherI {
    public static String eventSeq = "event";
//    @Autowired
//    private EventBusI eventBus;
    @Autowired
    private ApplicationEventPublisher eventBus;
//    @Autowired
//    private DomainDaoImpl domainDao;

    public void publish(DomainEventI domainEvent) {
        String srcId = MDC.get(TraceKey.TID);
        String newTid = String.format(srcId + "-%s[%s]", domainEvent.getClass().getSimpleName(), this.nextEventId());

        CompletableFuture.runAsync(() -> {
            MDC.put("tid", newTid);
            eventBus.publishEvent(domainEvent);
        });
//        eventBus.asyncFire(domainEvent);
    }

    @Override
    public String nextEventId() {
        return IdUtil.fastSimpleUUID().substring(21);
//        return domainDao.nextLongAsStr(eventSeq);
    }

    public <T> T getEvent(TenantId tenantId, String eventId, Class<T> domainType) {
        // final ComQB qb = ComQB.build(KpiEventDo.class)
        //         .whereEq("tenantId", tenantId.getId())
        //         .whereEq("eventId", eventId);
        // return domainDao.findDomain(qb, domainType);
        return null;
    }

    @Transactional
    public void save(EventI eventI) {
        // KpiEventDo dataTo = new KpiEventDo();
        // new DomainToDataBuilder<>(eventI, dataTo).build();
        // final Object json = JSONObject.toJSON(eventI);
        // dataTo.setEvent(json.toString());
        // dataTo.setJavaType(eventI.getClass().getSimpleName());
        // domainDao.save(dataTo);
    }

    @Transactional
    public void error(EventI eventI) {
        // KpiEventDo dataTo = new KpiEventDo();
        // new DomainToDataBuilder<>(eventI, dataTo).build();
        // final UpdateBuilder up = UpdateBuilder.build(KpiEventDo.class)
        //         .whereEq("tenantId", dataTo.getCompanyId())
        //         .whereEq("eventId", dataTo.getEventId())
        //         .appendSet("error = 1")
        //         .set("errorMsg", dataTo.getErrorMsg());
        // domainDao.update(up);
    }

    public static void main(String[] args) throws ClassNotFoundException {
        final Class<?> aClass = Class.forName("com.polaris.kpi.common.infr.MyConstructor");
        System.out.println(IdUtil.fastSimpleUUID());
    }
}
