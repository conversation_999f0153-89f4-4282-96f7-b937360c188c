package com.polaris.kpi.config;

//import com.polaris.dingtalk.conf.DingApiConf;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <h3>此处添加注释</h3>
 * created by Bruce<PERSON>R on 2020/9/22
 */
@Component
public class DingApiProperties {

    @Value ("${ding.SSOSecret}")
    private String SSOSecret;
    /**
     * 钉钉开放平台-应用运营-(当前应用)-商品码
     */
    @Value ("${ding.goodsCode}")
    private String goodsCode;
    /**
     * 商品上架后，钉钉给予
     */
    @Value ("${ding.service.token}")
    private String serviceToken;
    @Value ("${ding.appId}")
    private String appId;
    @Value ("${ding.inAppCourseCode}")
    private String inAppCourseCode;
    /**
     * 智能人事一体化钉钉获取绩效应用权限接口与钉钉约定的accessKey
     */
    @Value ("${ding.hrm.accessKey:052c5e0a4202373488dca14996f3f2e2}")
    private String accessKey;
    /**
     * 智能人事一体化钉钉分配的tenant id:正式环境29，测试环境28
     */
    @Value ("${ding.hrm.tenantId:29}")
    private String tenantId;

    public String getSSOSecret() {
        return SSOSecret;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public String getServiceToken() {
        return serviceToken;
    }

    public String getAppId() {
        return appId;
    }

    public String getInAppCourseCode() {
        return inAppCourseCode;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public String getTenantId() {
        return tenantId;
    }
}
