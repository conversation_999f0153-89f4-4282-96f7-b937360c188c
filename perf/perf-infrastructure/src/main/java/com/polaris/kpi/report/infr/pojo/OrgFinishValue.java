package com.polaris.kpi.report.infr.pojo;


import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/7/22 17:40
 */
@Setter
@Getter
@NoArgsConstructor
public class OrgFinishValue extends BaseItemAnalysisPo{

    private String orgId;
    private String orgName;

    public void accItemInfo(OrgFinishValue value) {
        if (this.getItemName()!= null){
            return;
        }
        this.setItemId(value.getItemId());
        this.setItemName(value.getItemName());
        this.setItemUnit(value.getItemUnit());
        this.setFinishValueType(value.getFinishValueType());
    }
}
