package com.polaris.kpi.report.infr.pojo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.apache.ibatis.annotations.JsonColumn;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/16 11:07
 */
@Data
public class ItemTargetAndFinishValueTrendPo {

    private String itemId;
    private String cycleType;
    private Integer cycleYear;
//    private String itemName;
//    private String itemUnit;
//    private Integer itemOrder;
    private List<ItemTargetAndFinishValueInCyclePo> itemTargetAndFinishValueInCycles;


    public ItemTargetAndFinishValueTrendPo(String itemId, String cycleType) {
        this.itemId = itemId;
        this.cycleType = cycleType;
    }

    public ItemTargetAndFinishValueTrendPo(String itemId, String cycleType, Integer cycleYear) {
        this.itemId = itemId;
        this.cycleType = cycleType;
        this.cycleYear = cycleYear;
    }

    public void completeCycles(){
        List<Integer> existIndexes = itemTargetAndFinishValueInCycles.stream().map(ItemTargetAndFinishValueInCyclePo::getCycleValue).collect(Collectors.toList());
        List<Integer> notExistIndexes = new ArrayList<>();
        List<Integer> monthAllIndexes = Arrays.asList(1,2,3,4,5,6,7,8,9,10,11,12);
        List<Integer> quarterAllIndexes = Arrays.asList(1,2,3,4);
        List<Integer> halfYearAllIndexes = Arrays.asList(1,2);

        if ("month".equals(cycleType) || "cross_month".equals(cycleType)){
            notExistIndexes = monthAllIndexes.stream().filter(index -> !existIndexes.contains(index)).collect(Collectors.toList());
        }
        if ("quarter".equals(cycleType)){
            notExistIndexes = quarterAllIndexes.stream().filter(index -> !existIndexes.contains(index)).collect(Collectors.toList());
        }
        if ("half_year".equals(cycleType)){
            notExistIndexes = halfYearAllIndexes.stream().filter(index -> !existIndexes.contains(index)).collect(Collectors.toList());
        }
        for (Integer index : notExistIndexes) {
            ItemTargetAndFinishValueInCyclePo itemTargetAndFinishValueInCyclePo = new ItemTargetAndFinishValueInCyclePo( index, cycleType, cycleYear.toString());
            itemTargetAndFinishValueInCycles.add(itemTargetAndFinishValueInCyclePo);
        }

        //按照cycleValue升序排序
        itemTargetAndFinishValueInCycles.sort(Comparator.comparing(ItemTargetAndFinishValueInCyclePo::getCycleValue));
    }
}
