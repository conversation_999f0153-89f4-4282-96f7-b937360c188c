package com.polaris.kpi.eval.infr.summary.dao;

import com.perf.www.cons.EmployeeStatus;
import com.perf.www.domain.entity.company.EmployeeBaseInfoModel;
import com.perf.www.vo.report.query.ReportTaskQueryVO;
import com.polaris.kpi.eval.infr.summary.pojo.QuarterlySummaryPo;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class KpiSummaryDao {
    @Resource
    private DomainDaoImpl autoBaseDao;

    //季度总结/季度报表
    public List<QuarterlySummaryPo> listQuarterlySummary(ReportTaskQueryVO query) {
        ComQB<QuarterlySummaryPo> comQB = ComQB.buildDiff(QuarterlySummaryPo.class, "v_quarterly_summary", "vqs")
                .join(EmployeeBaseInfoModel.class, "e")
                .on("vqs.emp_id", "e.employee_id")
                .clearSelect()
                .select(" e.`name` AS empName,\n" +
                        " vqs.emp_id,\n" +
                        " AVG(CASE WHEN vqs.quarter = 1 THEN final_score END) AS quarter1,\n" +
                        " AVG(CASE WHEN vqs.quarter = 2 THEN final_score END) AS quarter2,\n" +
                        " AVG(CASE WHEN vqs.quarter = 3 THEN final_score END) AS quarter3,\n" +
                        " AVG(CASE WHEN vqs.quarter = 4 THEN final_score END) as quarter4")
                .setRsType(QuarterlySummaryPo.class)
                .whereEq("vqs.company_id", "e.company_id")
                .whereEq("e.status", EmployeeStatus.ON_THE_JOB)
                .whereEq("e.is_delete", Boolean.FALSE.toString())
                .whereEq("e.company_id", query.getCompanyId())
                .whereEq("vqs.company_id", query.getCompanyId())
                .whereEq("vqs.cycle_type", query.getCycleType())
                .whereEq("DATE_FORMAT(vqs.cycle_start_date, '%Y')", query.getYear())
                .setPage(query.getPageNo(), query.getPageSize());
        return autoBaseDao.listPage(comQB);
    }
}
