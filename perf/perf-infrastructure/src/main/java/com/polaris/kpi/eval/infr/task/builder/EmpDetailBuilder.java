package com.polaris.kpi.eval.infr.task.builder;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.eval.Rater;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.func.VoidFunc;
import cn.hutool.core.lang.func.VoidFunc0;
import cn.hutool.core.util.StrUtil;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.empeval.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.AppealConf;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrGoal;
import com.polaris.kpi.eval.infr.acl.ding.builder.TaskBaseToAdminTaskBuilder;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskAppealBatchDo;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskBaseDo;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskScoreResultDo;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.EmpEvalDetailPo;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.ListEmpEvalScorerWrap;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.ListScoreResultWrap;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.PerfEvalTypeResultDo;
import com.polaris.kpi.org.domain.dept.entity.CompanyMsgCenter;
import com.polaris.kpi.org.domain.dept.entity.Employee;
import com.polaris.kpi.org.infr.emp.pojo.EmployeeBaseInfoDo;
import com.polaris.sdk.type.AuditEnum;
import com.polaris.sdk.type.ListWrap;
import lombok.Getter;
import org.lufei.ibatis.common.DataToDomainBuilder;

import java.util.*;
import java.util.stream.Collectors;

public class EmpDetailBuilder {
    @Getter
    private EmpEvalDetailPo eval;
    @Getter
    private EvalUser taskUser;
    @Getter
    private PerfEvaluateTaskBaseDo taskBase;
    @Getter
    private AdminTask adminTask;
    @Getter
    private Cycle cycle;
    @Getter
    private KpiListWrap kpiTypes;
    @Getter
    private ListWrap<EvalScoreResult> allItemRs;
    @Getter
    private ListWrap<PerfEvalTypeResult> typeScores;
    @Getter
    private List<EmpEvalScorerNode> scorerNodes;//评分环节节点
    @Getter
    private List<Rater> peerRaters;
    @Getter
    private List<CompanyMsgCenter> msgs;
    @Getter
    private List<OkrGoal> goals;
    @Getter
    private List<PerfEvaluateTaskAppealBatchDo> appealBatchDos;
    @Getter
    private EmpEvalMerge evalRule;
    private Set<String> allEmpIds = new HashSet<>();
    private List<Employee> emps;
    private List<EmpConvertFunc> empFuncs = new ArrayList<>();

    public EmpDetailBuilder(EvalUser taskUser) {
        this.taskUser = taskUser;
        this.eval = Convert.convert(EmpEvalDetailPo.class, taskUser);
    }

    public void convertTaskBase(PerfEvaluateTaskBaseDo taskBase) {
        this.taskBase = taskBase;
        this.adminTask = new TaskBaseToAdminTaskBuilder(taskBase).getAdminTask();
        if (taskUser.isNewEmp()) {
            this.cycle = new Cycle(taskUser.getCompanyId(), taskBase.getCycleStartDate(), taskBase.getCycleEndDate());
        }
    }

    public boolean needCycle() {
        return !taskUser.isNewEmp();
    }

    public void convertCycle(Cycle cycle) {
        this.cycle = cycle;
    }

    public void convertKpiType(KpiListWrap kpiTypes) {
        this.kpiTypes = kpiTypes;
        Set<String> inpuEmpIds = kpiTypes.getDatas().stream().flatMap(type -> type.getItems().stream())
                .flatMap(evalKpi -> StrUtil.splitTrim(evalKpi.getResultInputEmpId(), ",")
                        .stream()).collect(Collectors.toSet());
        this.allEmpIds.addAll(inpuEmpIds);
    }

    public void convertGoals(List<OkrGoal> goals) {
        this.goals = goals;
    }

    public void convertScorerNodes( List<EmpEvalScorerNode> scorerNodes) {
        this.scorerNodes = scorerNodes;
    }

    public void convertScoreRs(ListWrap<EvalScoreResult> allItemRs, ListWrap<PerfEvalTypeResult> typeScores) {
        this.allItemRs = allItemRs.groupBy(rs -> rs.getScorerType());//按场景分组
        this.typeScores = typeScores;
        typeScores.groupBy(evalScoreResult -> evalScoreResult.getKpiTypeId());
        //评分scoreRs
        List<EvalScoreResult> raterRss = allItemRs.getDatas().stream().filter(rs -> AuditEnum.scoreScenes().contains(rs.getScorerType())).collect(Collectors.toList());
        ListWrap<EvalScoreResult> empScoreRs = new ListWrap<>(raterRss).groupBy(EvalScoreResult::getKpiItemId);
        for (EmpEvalKpiType type : kpiTypes.getDatas()) {
            type.acceptTypeRs(typeScores.groupGet(type.getKpiTypeId()));
            for (EvalKpi item : type.getItems()) {
                List<EvalScoreResult> results = empScoreRs.groupGet(item.getKpiItemId());
                item.setWaitScoresOld(results);
            }
        }
    }

    public List<String> v1InviterEmpIds() {
        List<String> empIds = new ArrayList<>();
        for (EmpEvalKpiType kpiType : kpiTypes.getDatas()) {
            List<? extends EvalKpi> items = kpiType.getItems();
            if (CollUtil.isEmpty(items)) {
                continue;
            }
            for (EvalKpi item : items) {
                EvalItemScoreRule itemScoreRule = item.getItemScoreRule();
                if (itemScoreRule != null) {
                    empIds.addAll(itemScoreRule.v1PeerInviterEmpIds());
                    empIds.addAll(itemScoreRule.v1SubInviterEmpIds());
                }
            }
        }
        return empIds;
    }

    public void convertV1Peer() {
//        employee_id as emp_id, name as emp_name
        List<String> v1InitEmpIds = this.v1InviterEmpIds();
        this.peerRaters = this.emps.stream().filter(emp -> v1InitEmpIds.contains(emp.getEmployeeId()))
                .map(emp -> new Rater(emp.getEmployeeId(), emp.getName()))
                .collect(Collectors.toList());
        for (EmpEvalKpiType kpiType : kpiTypes.getDatas()) {
            List<? extends EvalKpi> items = kpiType.getItems();
            if (CollUtil.isEmpty(items)) {
                continue;
            }
            for (EvalKpi item : items) {
                EvalItemScoreRule itemScoreRule = item.getItemScoreRule();
                if (itemScoreRule == null) {
                    continue;
                }
                itemScoreRule.mutualScoreV1();
                if (itemScoreRule.peerRaterWaitAppoint()) {
                    itemScoreRule.initV1PeerUser(peerRaters);
                }
                if (itemScoreRule.subRaterWaitAppoint()) {
                    itemScoreRule.initV1SubUser(peerRaters);
                }
            }
        }
    }

    public void convertMsgs(List<CompanyMsgCenter> msgs) {
        this.msgs = msgs;
        List<String> reviewersIds = msgs.stream().map(msg -> msg.getEmpId()).collect(Collectors.toList());
        this.allEmpIds.addAll(reviewersIds);
    }

    public List<CompanyMsgCenter> listMsgCenter(String opEmpId, List<String> scences) {
        List<CompanyMsgCenter> collect = msgs.stream().filter(msg -> scences.contains(msg.getBusinessScene()))
                .filter(msg -> Boolean.FALSE.toString().equals(msg.getHandlerStatus()))
                .filter(msg -> StrUtil.equals(opEmpId, msg.getEmpId()))
                .collect(Collectors.toList());
        return collect;
    }
    public List<String> listMsgCenterEmpId(List<String> scences) {
        List<CompanyMsgCenter> collect = msgs.stream().filter(msg -> scences.contains(msg.getBusinessScene()))
                .filter(msg -> Boolean.FALSE.toString().equals(msg.getHandlerStatus())).collect(Collectors.toList());
        return collect.stream().map(CompanyMsgCenter::getEmpId).collect(Collectors.toList());
    }



    public ListScoreResultWrap curScoreRss(String opEmpId, List<String> scences) {
        List<EvalScoreResult> rss = allItemRs.getDatas().stream()
                .filter(rs -> StrUtil.isNotBlank(opEmpId) ? StrUtil.equals(opEmpId, rs.getScorerId()) : true)
                .filter(rs -> StrUtil.isBlank(rs.getAuditStatus()) || "reject".equals(rs.getAuditStatus()))
                .filter(rs -> rs.isNotDeleted())
                .filter(rs -> scences.contains(rs.getScorerType())).collect(Collectors.toList());

        List<PerfEvalTypeResult> typeRs = typeScores.getDatas().stream()
                .filter(rs -> StrUtil.isNotBlank(opEmpId) ? StrUtil.equals(opEmpId, rs.getScorerId()) : true)
                .filter(rs -> StrUtil.isBlank(rs.getAuditStatus()) || "reject".equals(rs.getAuditStatus()))
                .filter(rs -> scences.contains(rs.getScorerType())).collect(Collectors.toList());
        List<CompanyMsgCenter> msgCenters = listMsgCenter(opEmpId, scences);

        return new ListScoreResultWrap(Convert.toList(PerfEvaluateTaskScoreResultDo.class, rss), Convert.toList(PerfEvalTypeResultDo.class, typeRs), msgCenters);
//        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "s")
//                .whereEqReq("company_id", tenantId)
//                .whereEqReq("task_user_id", taskUserId)
//                .whereIsNull("audit_status")
//                .whereInReq("scorer_type", scences)
//                .whereEq("scorer_id", opEmpId)
//                .whereEq("is_deleted", Boolean.FALSE.toString());
//        List<PerfEvaluateTaskScoreResultDo> rss = domainDao.listAll(comQB);

//        ComQB typQb = ComQB.build(PerfEvalTypeResultDo.class, "type")
//                .whereEqReq("company_id", tenantId)
//                .whereEqReq("task_user_id", taskUserId)
//                .whereIsNull("audit_status")
//                .whereInReq("scorer_type", scences)
//                .whereEq("scorer_id", opEmpId)
//                .whereEq("is_deleted", Boolean.FALSE.toString());
//        List<PerfEvalTypeResultDo> typeRs = domainDao.listAll(typQb);

//
//        ComQB msgQb = ComQB.build(CompanyMsgCenterDo.class)
//                .whereEqReq("company_id", tenantId)
//                .whereEqReq("link_id", taskUserId)
//                .whereInReq("business_scene", scences)
//                .whereEq("emp_id", opEmpId)
//                .whereEq("handler_status", Boolean.FALSE.toString());
//        List<CompanyMsgCenter> msgCenters = domainDao.listAll(msgQb);

//        return new ListScoreResultWrap(rss, typeRs, msgCenters);
    }

    public ListEmpEvalScorerWrap curEmpEvalScorerNodesV3(String opEmpId, List<String> scences) {
        List<EmpEvalScorerNode> rss = scorerNodes.stream().filter(EmpEvalScorerNode::isWaitSubmit).collect(Collectors.toList());
        List<CompanyMsgCenter> msgCenters = listMsgCenter(opEmpId, scences);
        return new ListEmpEvalScorerWrap(Convert.toList(EmpEvalScorerNode.class, rss), msgCenters);
    }

    public EmpEvalScorerNode curEmpEvalTotalLevelScorerNodes(String opEmpId) {
        return scorerNodes.stream().filter(scorerNode -> StrUtil.equals(opEmpId, scorerNode.getScorerId())
                && scorerNode.isWaitSubmit()
                && scorerNode.isTotalLevel()).findFirst().orElse(null);
    }

    public ListEmpEvalScorerWrap curEmpEvalScorerNodes(String opEmpId, List<String> scences) {
        List<EmpEvalScorerNode> rss = scorerNodes.stream().filter(EmpEvalScorerNode::isWaitSubmit).collect(Collectors.toList());

        List<PerfEvalTypeResult> typeRs = typeScores.getDatas().stream()
                .filter(rs -> !StrUtil.isNotBlank(opEmpId) || StrUtil.equals(opEmpId, rs.getScorerId()))
                .filter(rs -> StrUtil.isBlank(rs.getAuditStatus()))
                .filter(rs -> scences.contains(rs.getScorerType())).collect(Collectors.toList());
        List<CompanyMsgCenter> msgCenters = listMsgCenter(opEmpId, scences);
        return new ListEmpEvalScorerWrap(Convert.toList(EmpEvalScorerNode.class, rss), Convert.toList(PerfEvalTypeResultDo.class, typeRs), msgCenters);
    }


    public ListScoreResultWrap curScoreRss(String opEmpId, String scence) {
        return this.curScoreRss(opEmpId, Arrays.asList(scence));
    }

    public boolean hasTodoTask(String opEmpId, List<String> auditEnums) {
        List<CompanyMsgCenter> nums = listMsgCenter(opEmpId, auditEnums);
        return nums.size() > 0;
    }

    public void convertAppealBatch(List<PerfEvaluateTaskAppealBatchDo> appealBatchDos, AppealConf appealConf, String opEmpId) {
        this.appealBatchDos = appealBatchDos;
        boolean exist = appealBatchDos.stream().anyMatch(bt -> StrUtil.equals("handled", bt.getAppealStatus()));
        eval.setHasWaitReadAppeal(exist);

        Optional<PerfEvaluateTaskAppealBatchDo> wait = appealBatchDos.stream().filter(bat -> StrUtil.equals(bat.getAppealStatus(), "wait")).findAny();

        PerfEvaluateTaskAppealBatch appealBatch = wait.isPresent() ? Convert.convert(PerfEvaluateTaskAppealBatch.class,wait.get()) : null;
        eval.appendAppealPriv(appealConf, opEmpId, appealBatch);
    }

//    public boolean hasTodoTask(String tenantId, String opEmpId, String taskUserId, List<String> auditEnums) {
//        ComQB comQB = ComQB.build(CompanyMsgCenterDo.class, "todo")
//                .clearSelect().select("count(1)").setRsType(int.class)
//                .whereEqReq("company_id", tenantId)
//                .whereEqReq("link_id", taskUserId)
//                .whereInReq("business_scene", auditEnums)
//                .appendWhere("handler_status='false'")
//                .whereEqReq("emp_id", opEmpId);
//        int num = domainDao.findOne(comQB);
//        return num > 0;
//    }

    public void convertHasSubmitedScore() {
        boolean hasSubmited = allItemRs.getDatas().stream().filter(rs -> rs.isPassed()).anyMatch(rs -> StrUtil.contains(rs.getScorerType(), "score"));
        eval.setHasScoreFlag(hasSubmited + "");
    }


    public void convertHasSubmitedScoreV3() {
        boolean hasSubmited = false;
        for (EmpEvalScorerNode scorerNode : scorerNodes) {
            if (scorerNode.needExcludeNode()){
                continue;
            }
            if (scorerNode.isFinish()) {
                hasSubmited = true;
                break;
            }
        }
        eval.setHasScoreFlag(String.valueOf(hasSubmited));
    }

    public void convertEvalBind(List<EvalBind> otherEvalBinds) {
        BindEvalResult rs = new BindEvalResult(taskUser.getId(), taskUser.getWeightOfRef(), taskBase.getId(), taskBase.getTaskName());
        rs.setBinds(otherEvalBinds);
        eval.setBindEvalResult(rs);
    }

    List<BaseScoreResult> levelResults;

    public void convertLevelResults(List<BaseScoreResult> levelResults) {
        this.levelResults = levelResults;
    }

    public void convertEvalRule(EmpEvalMerge evalRule) {
        this.evalRule = evalRule;
        evalRule.setInputNotify(adminTask.getInputNotifyConf());
        evalRule.setTotalLevelResults(levelResults);
        //初始化s3到 指标上
        evalRule.extendsRaterRule(kpiTypes);

        if (evalRule.isSameTime()) {
            evalRule.initSameScoreChain();
        } else {
            evalRule.initTurnScoreChain();
        }
        //签名开关
        evalRule.initSignatureFlag();
    }


    public List<String> allEmpIds() {
        allEmpIds.add(taskUser.getCreatedUser());
        List<String> scoreIds = allItemRs.getDatas().stream().filter(rs -> StrUtil.isNotBlank(rs.getScorerId())).map(r -> r.getScorerId()).collect(Collectors.toList());
        allEmpIds.addAll(scoreIds);
        allEmpIds.addAll(this.v1InviterEmpIds());
        allEmpIds.add(evalRule.getInitiator());
        return new ArrayList<>(allEmpIds);
    }

    public void convertEmpInfo(List<Employee> raters) {
        this.emps = raters;
        ListWrap<Employee> empWrap = new ListWrap<>(emps).asMap(Employee::getEmployeeId);
        for (EvalScoreResult scoreResult : allItemRs.getDatas()) {
            if (StrUtil.isEmpty(scoreResult.getScorerId())) {
                continue;
            }
            Employee empInfo = empWrap.mapGet(scoreResult.getScorerId());
            if (Objects.isNull(empInfo)) {
                continue;
            }
            scoreResult.setScorerName(empInfo.getName());
        }
        for (EmpConvertFunc empFunc : empFuncs) {
            empFunc.call(emps);
        }
        this.convertCreater();
    }

    public void convertCreater() {
        String createEmpId = StrUtil.isEmpty(evalRule.getInitiator()) ? taskUser.getCreatedUser() : evalRule.getInitiator();
        Optional<Employee> any = emps.stream().filter(emp -> emp.getEmployeeId().equals(createEmpId)).findAny();
        if (any.isPresent()) {
            Employee creater = any.get();
            eval.acceptCreater(Objects.nonNull(creater) ? creater.getAvatar() : null, Objects.nonNull(creater) ? creater.getName() : null, taskUser.getCreatedTime());
        }
    }

    public void addEmpConvertFunc(EmpConvertFunc voidFunc0) {
        empFuncs.add(voidFunc0);
    }

    public void loadInputEmps() {
        List<KpiEmp> inpuEmpMap = emps.stream().map(t -> {
            KpiEmp kpiEmp = new KpiEmp(t.getEmployeeId(), t.getName(), t.getAvatar());
            kpiEmp.addJobnumber(t.getJobnumber(), t.getStatus());
            return kpiEmp;
        }).collect(Collectors.toList());
        List<EvalKpi> kpiItems = kpiTypes.getDatas().stream().flatMap(type -> type.getItems().stream())
                .collect(Collectors.toList());
        kpiItems.forEach(item -> item.loadInputEmps(new ListWrap<>(inpuEmpMap).asMap(emp -> emp.getEmpId())));
    }

    public void loadV1Reviewers() {
        if (taskUser.wasTempTask()) {//处理一下当前负责人/升级数据有bug
            List<String> reviewersIds = msgs.stream().map(msg -> msg.getEmpId()).collect(Collectors.toList());
            List<KpiEmp> collect = emps.stream().filter(kemp -> reviewersIds.contains(kemp.getEmployeeId())).map(t -> {
                KpiEmp kpiEmp = new KpiEmp(t.getEmployeeId(), t.getName(), t.getAvatar());
                kpiEmp.addJobnumber(t.getJobnumber(), t.getStatus());
                return kpiEmp;
            }).collect(Collectors.toList());
            taskUser.reviewers(collect);
        }
    }

    public void addEmpId(String empId) {
        allEmpIds.add(empId);
    }

    public void convertaAppealName() {
        if (StrUtil.isBlank(eval.getAppealReceiverId())) {
            return;
        }
        String appealReceiverId = eval.getAppealReceiverId();
        List<Employee> collect = emps.stream().filter(employee -> employee.getEmployeeId().equals(appealReceiverId)).collect(Collectors.toList());
        if(!collect.isEmpty()){
            eval.accAppealName(collect.get(0).getName());
        }
    }
}
