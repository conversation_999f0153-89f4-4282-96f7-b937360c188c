package com.polaris.kpi.eval.infr.task.ppojo.empeval;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.company.TenantSysConf;
import cn.com.polaris.kpi.eval.ScoreAttUrl;
import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.polaris.kpi.eval.domain.cycle.entity.MatchScoreRuleCondition;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.admineval.CommentReqConf;
import com.polaris.kpi.eval.domain.task.entity.calibrated.RankRuleScoreRangeSnap;
import com.polaris.kpi.eval.domain.task.entity.empeval.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.*;
import com.polaris.kpi.eval.domain.task.entity.flow.DisplayEvalFlow;
import com.polaris.kpi.eval.domain.task.type.EvalScoreSummary;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.EvalScorerNodeScorePo;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.EvalScorerTypeScoresPo;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: lufei
 * @CreateTime: 2023-07-12  18:22
 * @Description: 提交评分页面查询 evalForScore
 * @Version: 1.0
 */
@Getter
@Setter
public class EmpEvalForScoreV3Po implements MatchScoreRuleCondition {
    private Set<String> myTaskScoreTypes;//我的评分环节
    private List<String> scoreTypes;//所有的评分结点
    private List<EmpEvalKpiType> waitScoreTypes = new ArrayList<>();//  需要提交评分评分的维度
    private List<EvalScoreSummary> scoreSummarys;//评分总结

    private BaseScoreResult totalLevelRsOld;//  需要提交评分总评 【弃用】
    private EmpEvalScorerNode totalLevelRs;//  需要提交评分总评
    private BigDecimal totalScore;
    private List<ScoreAttUrl> totalScoreAttUrl; //总分附件
    private String totalComment;
    private List<RankRuleScoreRangeSnap> scoreRanges;//等级分值评分范围
//    private List<ScoreRange> scoreRanges;//等级分值评分范围

    private String empName;
    private String avatar;
    private String orgName;
    private String empId;
    private BigDecimal predictionScore;

    private String taskName;
    private List<KpiEmp> reviewers;//流程责任人列表
    private String cycleStart;//周期开始时间点
    private String cycleEnd;//周期结束时间点
    private String cycleId;//周期id

    private boolean isSameTime;//是否并行评分 用于控制页面使用串行提交接口还是并行提交接口
    private String taskBaseId;
    private String taskUserId;
    private String companyId;
    private String taskStatus;

    private Integer plusOrSubComment;
    private Integer scoreSummarySwitch;

    private BigDecimal fullScore;       //满分
    private BigDecimal baseScore;               //基准分： 考核任务总分=基准分+普通类别得分+加分类别得分-减分类别得分
    private String selfRateMode;    //自评评分模式 旧selfScoreRule
    private String subRateMode;     //下级评分模式 旧mutualScoreRule
    private String peerRateMode;    //同级评分模式 旧mutualScoreRule
    private String superRateMode;   //上级评分模式  旧superiorScoreRule
    //- s3  评分规则
    public String mutualScoreRule;  //旧mutualScoreRule
    public String evaluateType;
    public BigDecimal superiorScoreWeight;
    public BigDecimal subScoreWeight;
    public BigDecimal selfScoreWeight;
    public BigDecimal peerScoreWeight;
    private ScoreViewConf viewConf;
    //- s3  评分规则 end

    private String mutualScoreAnonymousConfig;

    private String commentFlag;
    private Integer commentRequiredValue;
    private Integer commentRequiredHighValue;
    private CommentReqConf commentReqConf;
    //以下来自己 : templBaseJson
    private String publicType;//北京以乘特殊处理：除了自动公示其他任务在公示前即使开启了可见范围也不要显示
    private String typeWeightSwitch;//判断是否开启类别权重
    private String scoreRangeType;//评分方式：0-满分*权重
    private List<FinalScore> nodeScores;//用于展示评分整体情况数据
    private String evalOrgId;
    private String evalOrgName;
    private Integer isReCommit;//是否操作过重置后再重新提交

    private BigDecimal finalScore;
    private BigDecimal finalItemAutoScore;//自动评分指标最终分数

    private BigDecimal finalItemScore;//指标指定得分
    private BigDecimal selfScore;//自评得分
    private BigDecimal peerScore;//同级互评得分
    private BigDecimal subScore;//下级互评得分
    private BigDecimal superiorScore;//上级评分
    private BigDecimal appointScore;//指定评分

    private Integer createTotalLevelType;       //绩效总等级生成方式 1 = 自动, 2= 手动
    private Integer orderBy;            //排序
    private String snapId; //等级规则组快照id,周期上的规则
    private boolean isOpenAvgWeightCompute; //是否开启平均权重计算
    private String showRejectScoreButton = "false"; // false-不显示, true-显示
    private String rejectReason;
    private Integer showResultType;

    private List<EvalScorerTypeScoresPo> evalNodeScorers = new ArrayList<>(); //评分人列表

    public EmpEvalForScoreV3Po(EmpEvalMerge rule, EvalUser eval,String opEmpId, String reSubmitNode, ScoreReject scoreReject, TenantSysConf selfIsCanAutoScoreItem) {
        baseInfo(rule.getTaskId(), eval.getId(), eval.getCompanyId().getId(), eval.getTaskStatus(), eval.getFinalScore());
        buildEvalOrg(eval.getEvalOrgId(), eval.getEvalOrgName());
        acceptEmp(eval.getEmpId(), eval.getAvatar(), eval.getEmpOrgName(), eval.getEmpName());
        EmpEvalScorer curEvalScorer = rule.getEvalScorersWrap().getCurEvalScorer(opEmpId);
        // empEvalForScore
        this.finalItemAutoScore =eval.getFinalItemAutoScore();
        this.reviewers = eval.getReviewersJson();
        this.setScoreTypes(new ArrayList<>(new HashSet(rule.allScoreTypeV3())));
        this.taskName = rule.getTaskName();
        this.evaluateType = rule.getEvaluateType();
        this.isSameTime = rule.getScoreSortConf().isSameTime();//需要低层数据兼容
        this.publicType = rule.getPublishResult().getType();
        this.viewConf = rule.getScoreView();
        this.showResultType = rule.getShowResultType();
//        this.scoreRanges = eval.getScoreRanges();
        this.createTotalLevelType = rule.getCreateTotalLevelType();
        this.isOpenAvgWeightCompute = eval.isOpenAvgWeightCompute();
        this.buildComment(rule.getCommentConf());
        this.commentReqConf = rule.getCommentReqConf();
        this.buildScoreConf(rule.getTypeWeightConf(), rule.getScoreValueConf());
        this.buildRaterMode(rule.selfConf(), rule.peerConf(), rule.subConf(), rule.supperConf());
//        FinalWeightSumScore weightSumScore = rule.computeFinalScore(eval.getFinalItemAutoScore(), EvalScoreResult::getFinalScore,isOpenAvgWeightCompute);
//        this.buildFinalNodeScore(weightSumScore);
        this.buildFinalNodeScoreV3(eval);
        for (EmpEvalKpiType type : rule.getKpiTypes().getDatas()) {
            for (EvalKpi item : type.getItems()) {
                //执行继承维度等级操作
                item.extendsTypeLevelOpt(type.getIndLevelGroup());
                if (item.isAutoItem()){//特殊处理下自动计算指标分的显示
                    item.setItemAutoScore(item.getItemScore());
                }
            }
        }
        if (curEvalScorer != null) {
            EmpEvalScorerNode totalLevelNode = curEvalScorer.getWaitSubmitTotalLevelScoreNode();//获取待提交的打等级节点
            if (totalLevelNode != null) {
                this.totalLevelRs = totalLevelNode;
            }
//            if (hasMyScore(opEmpId, rule.getKpiTypes()) && totalLevelNode != null) {
//                this.totalLevelRs = null;
//            } else {
//                this.totalLevelRs = totalLevelNode;
//            }
        }

        if(hasMyScore(opEmpId, rule.getKpiTypes())) {
            this.filterMyScoreRsV3(rule.getCompanyConf().keepAllItem(), keepAutoItem(selfIsCanAutoScoreItem), rule.getKpiTypes(), opEmpId, reSubmitNode);
        }else{
            this.waitScoreTypes = rule.getKpiTypes().getDatas();
            for (EmpEvalKpiType waitScoreType : waitScoreTypes) {
                waitScoreType.buildAlreadyNodesV3( new EmpId(opEmpId), reSubmitNode,eval.isOpenAvgWeightCompute());
            }
        }
//        if (this.totalLevelRs == null) {//不是打总等级,要过滤一下维度与指标显示
//            this.filterMyScoreRsV3(rule.getCompanyConf().keepAllItem(),keepAutoItem(selfIsCanAutoScoreItem),rule.getKpiTypes(), opEmpId, reSubmitNode);
//        } else {
//            this.waitScoreTypes = rule.getKpiTypes().getDatas();
//            for (EmpEvalKpiType waitScoreType : waitScoreTypes) {
//                waitScoreType.buildAlreadyNodesV3( new EmpId(opEmpId), reSubmitNode,eval.isOpenAvgWeightCompute());
//            }
//        }

        this.buildMyNode(rule.getKpiTypes(),opEmpId);
        this.builderSignatureFlag();
        this.buildEvalNodeScorers(rule);
        this.buildSelfReject(rule, scoreReject);//自评驳回
        this.buildBaseScore(eval);//构建基础分
    }

    private boolean keepAutoItem(TenantSysConf selfIsCanAutoScoreItem){
        return Objects.nonNull(selfIsCanAutoScoreItem) && selfIsCanAutoScoreItem.getOpen() == 1;
    }

    private void buildBaseScore(EvalUser eval) {
        superiorScore = valueIsZero(eval.getV3SuperiorScore()) ? null : eval.getV3SuperiorScore();
        subScore = valueIsZero(eval.getV3SubScore()) ? null : eval.getV3SubScore();
        peerScore = valueIsZero(eval.getV3PeerScore()) ? null : eval.getV3PeerScore();
        selfScore = valueIsZero(eval.getV3SelfScore()) ? null : eval.getV3SelfScore();
        finalItemScore = valueIsZero(eval.getV3AppointScore()) ? null : eval.getV3AppointScore();
    }

    private boolean valueIsZero(BigDecimal value){
        if (value == null){
            return false;
        }
        return value.compareTo(BigDecimal.ZERO) == 0;
    }

    private void buildSelfReject(EmpEvalMerge rule,ScoreReject scoreReject){
        //是否展示驳回按钮，要求任务上开启设置，并且当前是上级评，并且存在已经评分的自评
        if ( rule.getScoreConf().superRejectSelfScoreEnabled()
                && CollUtil.isNotEmpty(this.getMyTaskScoreTypes())
                && this.getMyTaskScoreTypes().contains("superior_score")){
            //自评的条件在里面一层进行判断
            //只要有一个EmpEvalKpiType，获取alreadyScores,包含node为selfScore的对象 或者 EmpEvalKpiType的items里面存在alreadyNodes包含node为selfScore的对象
//            AtomicBoolean selfScoreFlag = new AtomicBoolean(false);
//            scoreV2Po.getWaitScoreTypes().stream()
//                    .flatMap(type -> Stream.concat(
//                            type.getAlreadyScores().stream().map(score -> score.getNode()),
//                            type.getItems().stream()
//                                    .flatMap(item -> item.getAlreadyNodes().stream())
//                                    .map(node -> node.getNode())
//                    ))
//                    .filter(node -> "self_score".equals(node))
//                    .findFirst()
//                    .ifPresent(node -> selfScoreFlag.set(true));
//
//            scoreV2Po.setShowRejectScoreButton(String.valueOf(selfScoreFlag.get()));
            boolean isFinished = rule.selfIsFinished(empId);
            this.showRejectScoreButton = String.valueOf(isFinished);
        }
        //当前是否是被驳回的自评，是否展示驳回详情
        boolean selfRejected = rule.selfAlreadyRejectedV3();
        if (selfRejected && Objects.nonNull(scoreReject)) {
            this.rejectReason = scoreReject.getRejectReason();
        }
    }

    private void buildEvalNodeScorers(EmpEvalMerge rule){
        //   evalNodeScorers
        EvalScorersWrap evalScorersWrap = rule.getEvalScorersWrap();
        ScoreSortConf scoreConf = rule.getScoreSortConf();
        List<EvalScorerTypeScoresPo> scorerNodeScorePos = new ArrayList<>();
        List<EmpEvalScorerNode> allScorerNodes = evalScorersWrap.allScorerNodes();
        ListWrap<EmpEvalScorerNode> scorerNodeScorePoListWrap = new ListWrap<>(allScorerNodes).groupBy(EmpEvalScorerNode::getScorerType);
        for (String scoreType : scorerNodeScorePoListWrap.groupKeySet()) {
            EvalScorerTypeScoresPo scorerNodeScorePo = new EvalScorerTypeScoresPo(scoreType);
            scorerNodeScorePo.setNodeOrder(scoreConf.getSort(scoreConf.sceneOrderAtSortType(scoreType)));
            List<EmpEvalScorerNode> scorerNodes = scorerNodeScorePoListWrap.groupGet(scoreType);
            scorerNodeScorePo.acceptScorerNodes(scorerNodes);
            scorerNodeScorePos.add(scorerNodeScorePo);
        }
        //根据 nodeSort 排序 升序
        scorerNodeScorePos.sort(Comparator.comparingInt(EvalScorerTypeScoresPo::getNodeOrder));
        this.evalNodeScorers = scorerNodeScorePos;
    }

    private boolean hasMyScore(String opEmpId, KpiListWrap kpiTypes) {
        for (EmpEvalKpiType type : kpiTypes.getDatas()) {
            List<EvalScorerNodeKpiType> collect = type.getWaitScores().stream().filter(typeRs -> StrUtil.equals(typeRs.getScorerId(), opEmpId) && !typeRs.isPassed()).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collect)) {
                return true;
            }
            for (EvalKpi item : type.getItems()) {
                List<EvalScorerNodeKpiItem> collect1 = item.getWaitScores().stream().filter(typeRs -> StrUtil.equals(typeRs.getScorerId(), opEmpId) && !typeRs.isPassed()).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(collect1)) {
                    return true;
                }
            }
        }
        return false;
    }

    private void builderSignatureFlag() {
        if (CollUtil.isEmpty(this.waitScoreTypes)) {
            return;
        }
        for (EmpEvalKpiType waitScoreType : this.waitScoreTypes) {
            if (CollUtil.isNotEmpty(waitScoreType.getWaitScores())) {
                for (PerfEvalTypeResult waitScore : waitScoreType.getWaitScoresOld()) {
                    waitScore.initSimpleSignatureFlag(waitScoreType.getSelfRater(),
                            waitScoreType.getPeerRater(),
                            waitScoreType.getSubRater(),
                            waitScoreType.getSuperRater(),
                            waitScoreType.getAppointRater());
                }
            }
            if (CollUtil.isEmpty(waitScoreType.getItems())) {
                continue;
            }
            for (EvalKpi item : waitScoreType.getItems()) {
                if (CollUtil.isNotEmpty(item.getWaitScores())) {
                    for (EvalScorerNodeKpiItem waitScore : item.getWaitScores()) {
                        waitScore.setScorerType(waitScore.getScorerType());
                        waitScore.installSignatureFlag(item.getItemScoreRule());
                    }
                }
            }
        }
    }

    public void baseInfo(String taskBaseId, String taskUserId, String companyId, String taskStatus, BigDecimal finalScore) {
        this.taskBaseId = taskBaseId;
        this.taskUserId = taskUserId;
        this.companyId = companyId;
        this.taskStatus = taskStatus;
        this.finalScore = finalScore;
    }


    public void buildComment(ScoreCommentConf commentConf) {
        if (commentConf == null) {
            return;
        }
        this.scoreSummarySwitch = commentConf.getScoreSummarySwitch();
        this.plusOrSubComment = commentConf.getPlusOrSubComment();
        this.commentFlag = commentConf.getCommentFlag();
        this.commentRequiredValue = commentConf.getCommentRequiredValue();
        this.commentRequiredHighValue = commentConf.getCommentRequiredHighValue();
    }


    public void buildRaterMode(RaterNodeConf self, RaterNodeConf peer,
                               RaterNodeConf sub, RaterNodeConf superCnf) {
        this.selfRateMode = self.getRateMode();
        this.selfScoreWeight = self.getNodeWeight();
        this.peerRateMode = peer.getRateMode();
        this.peerScoreWeight = peer.getNodeWeight();
        this.subRateMode = sub.getRateMode();
        this.subScoreWeight = sub.getNodeWeight();
        this.superRateMode = superCnf.getRateMode();
        this.superiorScoreWeight = superCnf.getNodeWeight();
    }

    public void buildScoreConf(TypeWeightConf typeWeightConf, ScoreValueConf scoreValueConf) {
        this.typeWeightSwitch = typeWeightConf.isOpen() ? Boolean.TRUE.toString() : Boolean.FALSE.toString();
        this.scoreRangeType = scoreValueConf.getScoreRangeType();
        this.fullScore = scoreValueConf.getCustomFullScore();
        this.baseScore = scoreValueConf.getBaseScore();
    }

    public void acceptEmp(String empId, String avatar, String orgName, String empName) {
        this.empId = empId;
        this.orgName = orgName;
        this.empName = empName;
        this.avatar = avatar;
    }


    public void filterMyScoreRs(boolean openKeepItem4Super, boolean openKeepItem4Self,Map<String, KpiEmp> empMap,
                                KpiListWrap kpiTypes, String scorerId, String reSubmitNode) {
        waitScoreTypes = new ArrayList<>();
        boolean keepAllItems = kpiTypes.matchAnySuperScore(scorerId) && openKeepItem4Super;
        boolean keepAutoItems = (kpiTypes.matchAnySelfAutoScoreV3(scorerId) && openKeepItem4Self);
        for (EmpEvalKpiType type : kpiTypes.getDatas()) {
            boolean keepType = type.filterWaitScore(keepAllItems,keepAutoItems, empMap, scorerId, reSubmitNode,this.isOpenAvgWeightCompute);
            if (keepType) {
                waitScoreTypes.add(type);
            }
        }
    }

    public void filterMyScoreRsV3(boolean openKeepItem4Super,boolean openKeepItem4Self, KpiListWrap kpiTypes, String scorerId, String reSubmitNode) {
        waitScoreTypes = new ArrayList<>();
        boolean keepAllItems = kpiTypes.matchAnySuperScoreV3(scorerId) && openKeepItem4Super;
        boolean keepAutoItems = (kpiTypes.matchAnySelfAutoScoreV3(scorerId) && openKeepItem4Self);
        for (EmpEvalKpiType type : kpiTypes.getDatas()) {
            boolean keepType = type.filterWaitScoreV3(keepAllItems,keepAutoItems, scorerId, reSubmitNode,this.isOpenAvgWeightCompute);
            if (keepType) {
                waitScoreTypes.add(type);
            }
        }
    }
    public static class FinalScore {
        public String node;
        public BigDecimal score;
        public String level;

        public FinalScore(String node, BigDecimal score) {
            this.node = node;
            this.score = score;
        }

        public FinalScore(String node, EvalUser eval) {
            this.node = node;
            this.score = getScoreByType(eval,node);
        }

        public BigDecimal getScoreByType(EvalUser eval,String scorerType) {
            if ("self_score".equals(scorerType)) {
                return eval.getV3SelfScore();
            }
            if ("appoint_score".equals(scorerType)) {
                return eval.getV3AppointScore();
            }
            if ("item_score".equals(scorerType)) {
                return eval.getV3FinalItemScore();
            }
            if ("peer_score".equals(scorerType)) {
                return eval.getV3PeerScore();
            }
            if ("sub_score".equals(scorerType)) {
                return eval.getV3SubScore();
            }
            if ("superior_score".equals(scorerType)) {
                return eval.getV3SuperiorScore();
            }
            return null;
        }
    }

    public void buildFinalNodeScoreV3(EvalUser eval) {
        this.nodeScores = scoreTypes.stream()
                .map(scoreType -> new FinalScore(scoreType, eval))
                .collect(Collectors.toList());
    }
    public void buildFinalNodeScore(FinalWeightSumScore weightSumScore) {
        this.nodeScores = scoreTypes.stream()
                .map(scoreType -> new FinalScore(scoreType, weightSumScore.getScoreByType(scoreType)))
                .collect(Collectors.toList());
    }

    public void buildEvalOrg(String evalOrgId, String evalOrgName) {
        this.evalOrgId = evalOrgId;
        this.evalOrgName = evalOrgName;
    }

    //需要我评分的环节汇总, 页面需要支持展示为总结
    private void buildMyNode(KpiListWrap kpiTypes, String opEmpId) {
        for (EmpEvalKpiType type : kpiTypes.getDatas()) {
            for (EvalKpi item : type.getItems()) {
                //执行继承维度等级操作
                item.extendsTypeLevelOpt(type.getIndLevelGroup());
            }
        }
        myTaskScoreTypes = new HashSet<>();
        Set<String> typeScoreTypes = kpiTypes.getDatas().stream().flatMap(type -> type.getWaitScores().stream())
                .filter(typRs -> !typRs.isWaitDispatch())
                .filter(itemRs -> StrUtil.equals(itemRs.getScorerId(), opEmpId))
                .map(EvalScorerNodeScoreItemBase::getScorerType).collect(Collectors.toSet());
        myTaskScoreTypes.addAll(typeScoreTypes);
        Set<String> itemScoreTypes = kpiTypes.getDatas().stream().flatMap(type -> type.getItems().stream()).flatMap(item -> item.getWaitScores().stream())
                .filter(itemRs -> !itemRs.isWaitDispatch())
                .filter(itemRs -> StrUtil.equals(itemRs.getScorerId(), opEmpId))
                .map(EvalScorerNodeScoreItemBase::getScorerType).collect(Collectors.toSet());
        myTaskScoreTypes.addAll(itemScoreTypes);
    }

    public void appendCacheScore(SubmitScoreCacheDo cacheDo) {
        if (cacheDo == null) {
            return;
        }

        if (Objects.nonNull(cacheDo.getTotalLevelRs())){//总等级
            EmpEvalScorerNode totalLeveNode = new EmpEvalScorerNode();
            BeanUtil.copyProperties(cacheDo.getTotalLevelRs(),totalLeveNode);
            totalLeveNode.setTotalComment(cacheDo.getTotalLevelRs().getScoreComment());
            this.totalLevelRs = totalLeveNode;
        }

        this.predictionScore = cacheDo.getPredictionScore();
        this.scoreSummarys = cacheDo.scoreSummarys();
        this.totalScore = cacheDo.totalScore();
        this.totalScoreAttUrl = cacheDo.totalScoreAttUrl();
        this.totalComment = cacheDo.totalComment();
        ListWrap<EvalScoreResult> itemRsCache = cacheDo.itemRsCache();
        ListWrap<PerfEvalTypeResult> typeRsCache = cacheDo.typeRsCache();
        for (EmpEvalKpiType waitScoreType : this.getWaitScoreTypes()) {
            if (!typeRsCache.isEmpty()) {
                for (EvalScorerNodeKpiType typeRs : waitScoreType.getWaitScores()) {
                    PerfEvalTypeResult typeCacheRs = typeRsCache.mapGet(typeRs.getId());
                    if (typeCacheRs == null) {
                        continue;
                    }
                    typeRs.setScoreComment(typeCacheRs.getScoreComment());
                    typeRs.setScoreLevel(typeCacheRs.getScoreLevel());
                    typeRs.setScoreAttUrl(typeCacheRs.getScoreAttUrl());
                }
            }
            if(itemRsCache.isEmpty()){
                continue;
            }
            for (EvalKpi item : waitScoreType.getItems()) {
                for (EvalScorerNodeKpiItem scoreR : item.getWaitScores()) {
                    EvalScoreResult crs = itemRsCache.mapGet(scoreR.getId());
                    if (crs == null) {
                        continue;
                    }
                    BigDecimal score;
                    if (crs.getSubtractScore() != null){
                        score = crs.getSubtractScore();
                    }else if (crs.getPlusScore() != null){
                        score = crs.getPlusScore();
                    }else{
                        score = crs.getScore();
                    }
                    scoreR.setScore(score);
                    scoreR.setScoreComment(crs.getScoreComment());
                    scoreR.setScoreAttUrl(crs.getScoreAttUrl());
                    scoreR.setScoreLevel(crs.getScoreLevel());
                    scoreR.setVetoFlag(crs.getVetoFlag());
                    scoreR.setScoreOption(crs.getScoreOption());
                }
            }
        }
    }

    @Override
    public String taskUserId() {
        return taskUserId;
    }

    @Override
    public void applyScoreRange(List<RankRuleScoreRangeSnap> ranges) {
        setScoreRanges(ranges);
    }
}
