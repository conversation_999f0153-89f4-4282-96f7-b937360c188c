package com.polaris.kpi.eval.infr.statics.dao;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.perf.www.common.utils.string.StringTool;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskUserDo;
import com.polaris.kpi.eval.infr.task.query.report.PerfAnalysisQuery;
import org.lufei.ibatis.builder.ComQB;

/**
 * <AUTHOR>
 * @date 2025/2/10 15:46
 */
public class StaticBaseDao {

    //添加通用查询条件
    public void addCommonCondition(PerfAnalysisQuery query, ComQB builder){

        ComQB exist = ComQB.build(PerfEvaluateTaskUserDo.class, "ca");
        exist.clearSelect().select(" 1 ");
        exist.appendWhere(" a.task_user_id = ca.id");
        //company_id
        String companyId = query.getCompanyId();
        exist.appendWhere ("ca.company_id  = '" + companyId + "'");
        //cycle_id
        String cycleId = query.getCycleId();
        exist.appendWhere("ca.cycle_id = '" + cycleId + "'");
        //performance_type
        Integer performanceType = query.getPerformanceType();
        if (performanceType == 1){
            //org_id
            if (CollUtil.isNotEmpty(query.getOrgIds())){
                String string = StringTool.getInStr(query.getOrgIds()).toString();
                exist.appendWhere(" ca.org_id in  " + string);
            }
            exist.appendWhere(" ca.eval_org_name is null");
        }else {
            //org_id
            if (CollUtil.isNotEmpty(query.getOrgIds())){
                String string = StringTool.getInStr(query.getOrgIds()).toString();
                exist.appendWhere(" ca.eval_org_id in  " + string);
            }
            exist.appendWhere(" ca.eval_org_name is not null");
        }
//        exist.appendWhere(" ca.performance_type = '" + performanceType + "'");
        //is_deleted
        exist.appendWhere(" ca.is_deleted = 'false'");
        //status
        exist.appendWhere("ca.task_status in('finished','terminated') and ca.final_score is not null");

        //task_id
        if (CollUtil.isNotEmpty(query.getTaskIds())){
            String string = StringTool.getInStr(query.getTaskIds()).toString();
            exist.appendWhere(" ca.task_id in  " + string );
        }
        //role_id
        if (CollUtil.isNotEmpty(query.getRoleIds())){
            String string = StringTool.getInStr(query.getRoleIds()).toString();
            exist.appendWhere("exists ( SELECT 1 FROM role_ref_emp AS r " +
                    "                               WHERE r.emp_id = ca.emp_id and r.role_id IN " + string + " and " +
                    " r.status = 'valid' and is_deleted = 'false' )");
        }

        if (query.getIsManageAnalysis()){
            addJoinManagePrivilegeCondition(query,exist);
        }


        builder.appendWhere(" exists (" + exist.getSql() + ")" );

    }

    public void addBaseCommonCondition(PerfAnalysisQuery query, ComQB builder){

        //company_id
        String companyId = query.getCompanyId();
        builder.appendWhere ("a.company_id  = '" + companyId + "'");
        //cycle_id
        String cycleId = query.getCycleId();
        builder.appendWhere("a.cycle_id = '" + cycleId + "'");
        //performance_type
        Integer performanceType = query.getPerformanceType();
        if (performanceType == 1){
            //org_id
            if (CollUtil.isNotEmpty(query.getOrgIds())){
                String string = StringTool.getInStr(query.getOrgIds()).toString();
                builder.appendWhere(" a.org_id in  " + string);
            }
            builder.appendWhere(" a.eval_org_name is null");
        }else {
            //org_id
            if (CollUtil.isNotEmpty(query.getOrgIds())){
                String string = StringTool.getInStr(query.getOrgIds()).toString();
                builder.appendWhere(" a.eval_org_id in  " + string);
            }
            builder.appendWhere(" a.eval_org_name is not null");
        }
        //is_deleted
        builder.appendWhere(" a.is_deleted = 'false'");

        //task_id
        if (CollUtil.isNotEmpty(query.getTaskIds())){
            String string = StringTool.getInStr(query.getTaskIds()).toString();
            builder.appendWhere(" a.task_id in  " + string );
        }
        //role_id
        if (CollUtil.isNotEmpty(query.getRoleIds())){
            String string = StringTool.getInStr(query.getRoleIds()).toString();
            builder.appendWhere("exists ( SELECT 1 FROM role_ref_emp AS r " +
                    "                               WHERE r.emp_id = a.emp_id and r.role_id IN " + string + " and " +
                    " r.status = 'valid' and is_deleted = 'false' )");
        }

    }

    public void addBaseCommonConditionNoOrg(PerfAnalysisQuery query, ComQB builder){

        //company_id
        String companyId = query.getCompanyId();
        builder.appendWhere ("a.company_id  = '" + companyId + "'");
        //cycle_id
        String cycleId = query.getCycleId();
        builder.appendWhere("a.cycle_id = '" + cycleId + "'");
        //performance_type
        Integer performanceType = query.getPerformanceType();
        if (performanceType == 1){
            builder.appendWhere(" a.eval_org_name is null");
        }else {
            builder.appendWhere(" a.eval_org_name is not null");
        }
        //is_deleted
        builder.appendWhere(" a.is_deleted = 'false'");

        //这里不过滤状态

        //task_id
        if (CollUtil.isNotEmpty(query.getTaskIds())){
            String string = StringTool.getInStr(query.getTaskIds()).toString();
            builder.appendWhere(" a.task_id in  " + string );
        }
        //role_id
        if (CollUtil.isNotEmpty(query.getRoleIds())){
            String string = StringTool.getInStr(query.getRoleIds()).toString();
            builder.appendWhere("exists ( SELECT 1 FROM role_ref_emp AS r " +
                    "                               WHERE r.emp_id = a.emp_id and r.role_id IN " + string + " and " +
                    " r.status = 'valid' and is_deleted = 'false' )");
        }

    }

    //团队考核的权限条件
    public void addManagePrivilegeCondition(PerfAnalysisQuery qry, ComQB comQB) {

        if (qry.getPerformanceType() == 2) {
            comQB.whereIn("a.eval_org_id", qry.getPrivOrgIds());
        } else {
            StringBuilder sb = new StringBuilder();
            //个人绩效排除当前人的记录
            comQB.appendWhere("a.emp_id <> '" +  qry.getOpEmpId() + "'");
            if (CollUtil.isNotEmpty(qry.getPrivOrgIds())) {
                String sqlPrivOrg = StringTool.getInStrSql(qry.getPrivOrgIds());
                sb.append("a.org_id in ");
                sb.append(sqlPrivOrg);
                if (CollUtil.isNotEmpty(qry.getPrivEmpIds())) {
                    sb.append(" OR ");
                }
            }
            if (CollUtil.isNotEmpty(qry.getPrivEmpIds())) {
                String sqlPrivEmp = StringTool.getInStrSql(qry.getPrivEmpIds());
                sb.append("a.emp_id in ");
                sb.append(sqlPrivEmp);
            }
            if (StrUtil.isNotBlank(sb.toString())) {
                comQB.appendWhere("(" + sb.toString() + ")");
            }
        }

    }

    //团队考核的权限条件
    public void addJoinManagePrivilegeCondition(PerfAnalysisQuery qry, ComQB comQB) {

        if (qry.getPerformanceType() == 2) {
            comQB.whereIn("ca.eval_org_id", qry.getPrivOrgIds());
        } else {
            StringBuilder sb = new StringBuilder();
            //个人绩效排除当前人的记录
            comQB.appendWhere("ca.emp_id <> '" + qry.getOpEmpId() + "'");
            if (CollUtil.isNotEmpty(qry.getPrivOrgIds())) {
                String sqlPrivOrg = StringTool.getInStrSql(qry.getPrivOrgIds());
                sb.append("ca.org_id in ");
                sb.append(sqlPrivOrg);
                if (CollUtil.isNotEmpty(qry.getPrivEmpIds())) {
                    sb.append(" OR ");
                }
            }
            if (CollUtil.isNotEmpty(qry.getPrivEmpIds())) {
                String sqlPrivEmp = StringTool.getInStrSql(qry.getPrivEmpIds());
                sb.append("ca.emp_id in ");
                sb.append(sqlPrivEmp);
            }
            if (StrUtil.isNotBlank(sb.toString())) {
                comQB.appendWhere("(" + sb.toString() + ")");
            }
        }

    }

}
