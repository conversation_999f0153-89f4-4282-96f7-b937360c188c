package com.polaris.kpi.eval.infr.task.ppojo.backlog;

import cn.com.polaris.kpi.eval.KpiItemUsedField;
import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.perf.www.common.constant.BusinessConstant;
import com.perf.www.common.utils.bean.Convert;
import com.polaris.kpi.eval.domain.cycle.entity.MatchScoreRuleCondition;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.calibrated.RankRuleScoreRangeSnap;
import com.polaris.kpi.eval.domain.task.entity.calibrated.ResultAudit;
import com.polaris.kpi.eval.domain.task.entity.empeval.*;
import com.polaris.kpi.eval.infr.task.ppojo.HistoryScorePo;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskItemDynamicPo;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskScoreResultDo;
import com.polaris.kpi.eval.infr.task.ppojo.ResultAuditRecordPo;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.EmpEvalTaskScore2Po;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.EmpEvalTaskScore3Po;
import com.polaris.kpi.eval.infr.task.ppojo.calibrated.ResultAuditCacheDo;
import com.polaris.sdk.type.ListWrap;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.JsonAryColumn;
import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 评分详情人员基本信息
 */
@Getter
@Setter
@NoArgsConstructor
public class ScoreDetailsPo implements MatchScoreRuleCondition {
    private BigDecimal baseScore;//基准分
    private String companyId;
    private String cycleId;//周期id
    private String taskUserId;
    private String empId;
    private String taskEmpName;
    private BigDecimal finalScore;          //最终得分
    private String evaluationLevel;         //等级
    private String perfCoefficient;
    private BigDecimal originalFinalScore;

    private BigDecimal selfScore;//自评得分
    private BigDecimal peerScore;//同级互评得分
    private BigDecimal subScore;//下级互评得分
    private BigDecimal superiorScore;//上级评分
    private BigDecimal appointScore;//指定评分
    private BigDecimal finalItemScore;//指定得分
    private Set<String> scoreTypes;

    private String originalEvaluationLevel;     //原始等级
    private String originalPerfCoefficient;     //原始系数
    private String scoreComment;
    private String stepId;
    @JsonAryColumn(HistoryScorePo.HScore.class)
    private List<HistoryScorePo.HScore> historyScore;        //历史分值
    private String orgName;             //部门名称
    private String orgId;               //部门ID
    private String evaluateType;        //评分流程类型 ： simple-简易流程，360-360°考核流程，custom-自定义流程
    private String scoreTotal;          //打总评分类型字符换，逗号分割
    @JsonAryColumn(RankRuleScoreRangeSnap.class)
    private List<RankRuleScoreRangeSnap> scoreRanges;//等级分值评分范围
    private List<CalibratedEmpEvalKpiTypePo> kpiTypePoList;      //指标信息
    private List<EvalScoreResult> totalScoresOld;
    private List<EmpEvalScorerNode> totalScores; //V3新版打总分
    private Integer showResultType;
    private Integer levelDefType; //等级评定规则类型:1=按分数评定等级(旧数据), 2 = 按分数排名评等级
    private String typeWeightSwitch;//判断是否开启类别权重
    private EmpEvalTaskScore3Po.Evaluate templEvaluateInfo;
    private List<AuditResultEvalType> types;
    private int scoreSummarySwitch;   //评分总结开关 0非必填/1必填/-1关闭 默认=-1
    private int calibrationStatus;      //校准状态
    private String auditVersion = "v1";//v1=1.0 可单个校准, v2=高级校准:可批量提交
    private Integer mutualEvalComputeType;        //平均权重计算类别 0 -平均权重，1-平均分
    private Boolean openAvgWeightCompute = false; //是否开启平均权重计算


    //不同环节的评分列表
    @JSONField(name = "scorerTypeScores")
    private List<ScoreNodeScore> scorerTypeScores = new ArrayList<>();

    public void accOpenAvgWeightCompute() {
        //是否开启平均权重计算
        this.openAvgWeightCompute = 0 == mutualEvalComputeType;
    }

    public void loadCache(ResultAuditCacheDo cacheDo) {
        if (cacheDo == null) {
            return;
        }
        this.finalScore = cacheDo.getFinalScore();
        this.stepId = cacheDo.getStepId();
        this.evaluationLevel = cacheDo.getEvaluationLevel();
        this.perfCoefficient = cacheDo.getPerfCoefficient();
        this.scoreComment = cacheDo.getScoreComment();
        this.types = cacheDo.getTypeScores();
    }

    public void accScoreDetailsInfo(EmpEvalMerge evalRule, List<CalibratedPerfEvaluateTaskResultPo> itemResultList,
                                    List<CalibratedPerfEvaluateTaskResultPo> scoreResultPos, List<KpiItemUsedField> kpiItemUsedFields,
                                    ListWrap<ResultAuditCacheDo> cacheWrap, List<CalibratedPerfEvaluateTaskResultPo> resultAuditList,
                                    CycleEval taskBaseModel,Map<String, ResultAuditRecordPo> recordPoMap){
        if (Objects.isNull(evalRule)) {
            return;
        }
        if (CollUtil.isNotEmpty(recordPoMap)) {
            ResultAuditRecordPo recordPo = recordPoMap.get(taskUserId);
            if (Objects.nonNull(recordPo)) {
                this.scoreComment = recordPo.getScoreComment();
            }
        }
        this.templEvaluateInfo = cn.hutool.core.convert.Convert.convert(EmpEvalTaskScore3Po.Evaluate.class, taskBaseModel);
        this.templEvaluateInfo.buildRaterMode(evalRule.selfConf(), evalRule.peerConf(), evalRule.subConf(), evalRule.supperConf());
        this.templEvaluateInfo.buildScoreViewRule(taskBaseModel.getTemplEvaluateJson(), taskBaseModel.getTaskStatus(), evalRule);
        this.templEvaluateInfo.setItemEvaluateList(evalRule.getKpiTypes().itemScoreRules());
        this.templEvaluateInfo.setIsAddAuditComment(evalRule.getAuditResult().getCommentReq());
        this.templEvaluateInfo.setAuditFlag(evalRule.getAuditResult().isOpen() ? "true" : "false");
        this.templEvaluateInfo.buildScoreConf(evalRule.getTypeWeightConf(), evalRule.getScoreValueConf());
        this.typeWeightSwitch = evalRule.getTypeWeightConf().isOpen() ? Boolean.TRUE.toString() : Boolean.FALSE.toString();
        this.kpiTypePoList = Convert.convertListOnlyMatch(evalRule.getKpiTypes().getDatas(), CalibratedEmpEvalKpiTypePo.class);
        this.setScoreItems(evalRule, itemResultList, scoreResultPos, kpiItemUsedFields, getIndexResultAudits(resultAuditList), cacheWrap);
    }
    public void accScoreDetailsInfo(EmpEvalMerge evalRule,
                                    ListWrap<ResultAuditCacheDo> cacheWrap,
                                    List<CalibratedPerfEvaluateTaskResultPo> resultAuditList,
                                    CycleEval taskBaseModel,
                                    Map<String, ResultAuditRecordPo> recordPoMap){
        if (Objects.isNull(evalRule)) {
            return;
        }
        if (CollUtil.isNotEmpty(recordPoMap)) {
            ResultAuditRecordPo recordPo = recordPoMap.get(taskUserId);
            if (Objects.nonNull(recordPo)) {
                this.scoreComment = recordPo.getScoreComment();
            }
        }
        this.templEvaluateInfo = cn.hutool.core.convert.Convert.convert(EmpEvalTaskScore3Po.Evaluate.class, taskBaseModel);
        this.templEvaluateInfo.buildRaterMode(evalRule.selfConf(), evalRule.peerConf(), evalRule.subConf(), evalRule.supperConf());
        this.templEvaluateInfo.buildScoreViewRule(taskBaseModel.getTemplEvaluateJson(), taskBaseModel.getTaskStatus(), evalRule);
        this.templEvaluateInfo.setItemEvaluateList(evalRule.getKpiTypes().itemScoreRules());
        this.templEvaluateInfo.setIsAddAuditComment(evalRule.getAuditResult().getCommentReq());
        this.templEvaluateInfo.setAuditFlag(evalRule.getAuditResult().isOpen() ? "true" : "false");
        this.templEvaluateInfo.buildScoreConf(evalRule.getTypeWeightConf(), evalRule.getScoreValueConf());
        this.typeWeightSwitch = evalRule.getTypeWeightConf().isOpen() ? Boolean.TRUE.toString() : Boolean.FALSE.toString();
        this.buildKpiTypePos(evalRule, getIndexResultAudits(resultAuditList), cacheWrap);
        this.scoreTypes = evalRule.listEvalScorerNodeTypes();//页面需要校准的类型?? 这里优化后只有评分的环节，是否影响校准需要验证
        if (CollUtil.isNotEmpty(scoreTypes)) {
            scoreTypes.remove(SubScoreNodeEnum.TOTAL_LEVEL.getScene());
        }
        this.convertTotalScore(evalRule);//打总分
        this.buildScoreTypeScore();
    }

    private void buildScoreTypeScore() {
        if (CollUtil.isEmpty(this.scoreTypes)){
            return;
        }
        this.scoreTypes.forEach(scoreType ->{
            BigDecimal score = getNoWeightScoreByType(scoreType);
            BigDecimal nodeWeight = this.templEvaluateInfo.getNodeWeightByScoreType(scoreType);
            ScoreNodeScore scoreNodeScore = new ScoreNodeScore(scoreType,nodeWeight,score);
            this.scorerTypeScores.add(scoreNodeScore);
        });
    }

    public BigDecimal getNoWeightScoreByType(String scorerType) {
        if ("self_score".equals(scorerType)) {
            return selfScore;
        }
        if ("appoint_score".equals(scorerType)) {
            return appointScore ;
        }
        if ("item_score".equals(scorerType)) {
            return finalItemScore;
        }
        if ("peer_score".equals(scorerType)) {
            return peerScore;
        }
        if ("sub_score".equals(scorerType)) {
            return subScore;
        }
        if ("superior_score".equals(scorerType)) {
            return superiorScore;
        }
        return null;
    }
    private void convertTotalScore(EmpEvalMerge evalRule) {
        if (evalRule.getEvalScorersWrap().isEmpty()) {
            return;
        }
        EvalScorersWrap wrap = evalRule.getEvalScorersWrap();
        this.totalScores = wrap.listTotalScoreNode();
        if (CollUtil.isNotEmpty(totalScores)) {
            Set<String> scoreTotalScorerTypes = totalScores.stream().map(EvalScorerNodeBase::getScorerType).collect(Collectors.toSet());
            this.scoreTotal = CollUtil.join(scoreTotalScorerTypes, ",");
        }
    }
    public Map<String, List<PerfEvaluateTaskScoreResultDo>> getIndexResultAudits(List<CalibratedPerfEvaluateTaskResultPo> resultAuditList) {
        List<PerfEvaluateTaskScoreResultDo> indexResultAudits = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultAuditList)) {
            for (CalibratedPerfEvaluateTaskResultPo resultPo : resultAuditList) {
                if (CollUtil.isNotEmpty(resultPo.getIndexCalibration())) {
                    resultPo.getIndexCalibration().forEach(index -> {
                        index.setApprovalOrder(resultPo.getApprovalOrder());
                    });
                    indexResultAudits.addAll(resultPo.getIndexCalibration());
                }
            }
        }
        /**如果存在指标校准的记录,根据指标名称分组*/
        return indexResultAudits.stream().collect(Collectors.groupingBy(PerfEvaluateTaskScoreResultDo::getKpiItemId));
    }

    public void setScoreItems(EmpEvalMerge evalRule, List<CalibratedPerfEvaluateTaskResultPo> itemResultList,
                              List<CalibratedPerfEvaluateTaskResultPo> scoreResultPos, List<KpiItemUsedField> kpiItemUsedFields,
                              Map<String, List<PerfEvaluateTaskScoreResultDo>> indexResultAuditMap, ListWrap<ResultAuditCacheDo> cacheWrap) {
        if (CollUtil.isEmpty(itemResultList)) {
            return;
        }
        List<EvalKpi> okItems = evalRule.okrItems();
        Map<String, List<CalibratedPerfEvaluateTaskResultPo>> itemGroupByType = itemResultList.stream().collect(Collectors.groupingBy(rgs -> rgs.getKpiTypeId()));
        Map<String, List<CalibratedPerfEvaluateTaskResultPo>> scoreGroupByItem = scoreResultPos.stream().collect(Collectors.groupingBy(rgs -> rgs.getKpiItemId()));
        for (CalibratedEmpEvalKpiTypePo kpiTypePo : this.kpiTypePoList) {
            if (kpiTypePo.isAsk360Type()) {
                continue;
            }
            List<CalibratedPerfEvaluateTaskResultPo> items = itemGroupByType.get(kpiTypePo.getKpiTypeId());
            for (CalibratedPerfEvaluateTaskResultPo item : items) {
                if (StringUtils.isNotBlank(item.getIndLevelGroupId()) && Integer.valueOf(item.getIndLevelGroupId()) == 1
                        && item.getIndLevelGroup() == null) {
                    CalibratedEmpEvalKpiTypePo typePo = new ListWrap<>(kpiTypePoList).asMap(type -> type.getKpiTypeId()).mapGet(item.getKpiTypeId());
                    item.setIndLevelGroup(typePo != null ? typePo.getIndLevelGroup() : null);
                }
                item.setResultPoList(scoreGroupByItem.get(item.getKpiItemId()));
                //完成情况
                EvalKpi kpiItem = evalRule.getKpiTypes().getKpiItem(item.getKpiItemId());
                item.setInputChangeRecord(kpiItem.getInputChangeRecord());
                this.setItemScore(item, indexResultAuditMap);
                if (cacheWrap.mapGet(taskUserId) != null) {
                    String kpiItemId = item.getKpiItemId();
                    ResultAuditCacheDo resultAuditCacheDo = cacheWrap.mapGet(taskUserId);
                    item.loadCache(resultAuditCacheDo.getItemScores(kpiItemId)
                            , resultAuditCacheDo.getItemVetoFlag(kpiItemId)
                            , resultAuditCacheDo.getItemScoreLevel(kpiItemId));
                }
                //okr评分评语
                if (CollUtil.isNotEmpty(okItems)) {
                    Map<String, EvalKpi> resultMap = okItems.stream().collect(Collectors.toMap(EvalKpi::getId, kpi -> kpi, (a, b) -> b));
                    if (resultMap.containsKey(item.getId()) && Objects.nonNull(resultMap.get(item.getId()))) {
                        item.accepOkr(resultMap.get(item.getId()));
                    }
                }

            }
            kpiTypePo.setResultPoListOld(items);
            kpiTypePo.initKpiItemUsedFields(kpiItemUsedFields);
        }
        this.kpiTypePoList.sort(Comparator.comparing(CalibratedEmpEvalKpiTypePo::getTypeOrder));
    }


    public void buildKpiTypePos(EmpEvalMerge evalRule,
                                Map<String, List<PerfEvaluateTaskScoreResultDo>> indexResultAuditMap,
                                ListWrap<ResultAuditCacheDo> cacheWrap) {
        List<CalibratedEmpEvalKpiTypePo> kpiTypeScores = new ArrayList<>();
        for (EmpEvalKpiType type : evalRule.getKpiTypes().getDatas()) {
            CalibratedEmpEvalKpiTypePo kpiTypeScore = cn.hutool.core.convert.Convert.convert(CalibratedEmpEvalKpiTypePo.class, type);
            this.buildAlreadyNodesV3(kpiTypeScore,type.getAlreadyScoreV3());
            List<CalibratedEmpEvalKpiItemPo> kpiItemScores = new ArrayList<>();
            Set<String> typeScorerTypes = new HashSet<>();
            type.orderItem();//根据order 排序 升序
            if (type.isAskType()) {
                kpiTypeScores.add(kpiTypeScore);
                continue;
            }
            //根据维度筛选对应的评分节点
            for (EvalKpi item : type.getItems()) {
                CalibratedEmpEvalKpiItemPo convertItem = cn.hutool.core.convert.Convert.convert(CalibratedEmpEvalKpiItemPo.class, item);
                convertItem.setActionId(item.getActionId());
                convertItem.setIndLevelGroup(item.getIndLevelGroup());
                //添加okr评分评语
                convertItem.acceptOkrValue(item);
                convertItemScoreNodeKpi(item, convertItem,typeScorerTypes);//转换接收指标评价人及环节
                setBaseItemScore(kpiTypeScore.getKpiTypeClassify(), item, convertItem);//转换接收指标各个环节得分
                hanlderResultAudit(convertItem,indexResultAuditMap);//处理校准记录
                if (cacheWrap.mapGet(taskUserId) != null) { //处理缓存
                    String kpiItemId = item.getKpiItemId();
                    ResultAuditCacheDo resultAuditCacheDo = cacheWrap.mapGet(taskUserId);
                    convertItem.loadCache(resultAuditCacheDo.getItemScores(kpiItemId)
                            , resultAuditCacheDo.getItemVetoFlag(kpiItemId)
                            , resultAuditCacheDo.getItemScoreLevel(kpiItemId));
                }
                //接收指标完成值更新记录
                convertItem.setInputChangeRecord(item.getInputChangeRecord());
                kpiItemScores.add(convertItem);
            }
            kpiTypeScore.setScoreTypes(typeScorerTypes);
            kpiTypeScore.setResultPoList(kpiItemScores);
            kpiTypeScores.add(kpiTypeScore);
        }
        this.kpiTypePoList = kpiTypeScores;
        if (CollUtil.isEmpty(kpiTypeScores)) {
            this.kpiTypePoList.sort(Comparator.comparing(CalibratedEmpEvalKpiTypePo::getTypeOrder));
        }
    }


    private void convertItemScoreNodeKpi(EvalKpi item, CalibratedEmpEvalKpiItemPo convertItem, Set<String> typeScorerTypes) {
        Map<String, List<EvalScorerNodeKpiItem>> scoreNodeKpis = new ListWrap<>(item.getWaitScores()).groupBy(EvalScorerNodeKpiItem::getScorerType).getGroups();
        if (MapUtil.isEmpty(scoreNodeKpis)){
            return;
        }
        //评分结果按结点分组
        scoreNodeKpis.forEach((node, results) -> {
            //用于显示的，这里results要做下去重，对复制后评价的数据需要去重,根据scorerId去重
            List<EmpEvalTaskScore3Po.SubmitedScoreResult> submitedScoreResults = new ArrayList<>();
            List<EvalScorerNodeKpiItem>  tempResults = filterItemRepeat(results);
            tempResults.forEach(scorerNodeKpiItem -> {
                if (!scorerNodeKpiItem.isShow()){
                    return; //排除不可以展示的kpiItem
                }
                EmpEvalTaskScore3Po.SubmitedScoreResult convertRs = cn.hutool.core.convert.Convert.convert(EmpEvalTaskScore3Po.SubmitedScoreResult.class, scorerNodeKpiItem);
                convertRs.setScorer(scorerNodeKpiItem.getScorerName());
                convertRs.setAvatar(scorerNodeKpiItem.getScorerAvatar());
                convertRs.setVetoFlag(scorerNodeKpiItem.getVetoFlag());
                submitedScoreResults.add(convertRs);
            });

            typeScorerTypes.add(node);
            BigDecimal noWeightScore = convertItem.getNoWeightScoreByType(node);//指标环节得分
            convertItem.addScoreNode(node, noWeightScore, submitedScoreResults,false);
        });
    }

    public void buildAlreadyNodesV3(CalibratedEmpEvalKpiTypePo kpiTypeScore, List<EvalScorerNodeKpiType> waitScores) {
        List<EvalScorerNodeKpiType> alreadys = waitScores.stream().filter(EvalScorerNodeKpiType::isPassed).collect(Collectors.toList());
        ListWrap<EvalScorerNodeKpiType> nodeGroup = new ListWrap<>(alreadys).groupBy(EvalScorerNodeKpiType::getScorerType);
        nodeGroup.getGroups().forEach((node, results1) -> {
            //用于显示的，这里results要做下去重，对复制后评价的数据需要去重,根据scorerId去重
            List<EvalScorerNodeKpiType>  tempResults = filterRepeat(results1);
            List<EmpEvalTaskScore3Po.SubmitedScoreResult> submitedScoreResults = new ArrayList<>();
            //排除不可以展示的kpiItem
            tempResults.stream().filter(EvalScorerNodeScoreItemBase::isShow).forEachOrdered(srs -> {
                EmpEvalTaskScore3Po.SubmitedScoreResult convertRs = cn.hutool.core.convert.Convert.convert(EmpEvalTaskScore3Po.SubmitedScoreResult.class, srs);
                convertRs.setScorer(srs.getScorerName());
                convertRs.setAvatar(srs.getScorerAvatar());
                convertRs.setVetoFlag(srs.getVetoFlag());
                submitedScoreResults.add(convertRs);
            });
            kpiTypeScore.addScoreNode(node, null, submitedScoreResults, false);
        });
    }
    private List<EvalScorerNodeKpiItem> filterItemRepeat(List<EvalScorerNodeKpiItem> rs){
        if (CollUtil.isEmpty(rs)) {
            return new ArrayList<>();
        }

        //waitScores  asScorerIdAndOrderKey 去重
        Set<String> uniqueKeys = new HashSet<>();
        return rs.stream()
                .filter(e -> uniqueKeys.add(e.asScorerIdAndOrderKey()))
                .collect(Collectors.toList());
    }
    private List<EvalScorerNodeKpiType> filterRepeat(List<EvalScorerNodeKpiType>  rs){
        if (CollUtil.isEmpty(rs)) {
            return new ArrayList<>();
        }

        //waitScores  asScorerIdAndOrderKey 去重
        Set<String> uniqueKeys = new HashSet<>();
        return rs.stream()
                .filter(e -> uniqueKeys.add(e.asScorerIdAndOrderKey()))
                .collect(Collectors.toList());
    }

    private void setBaseItemScore(String kpiTypeClassify, EvalKpi item, CalibratedEmpEvalKpiItemPo itemScore) {
        if (null != item.getItemItemScore()) {
            itemScore.setItemItemScore(item.getItemScore());//前端使用的是ItemScore作为定向评分(未乘以指标权重)，这里要单独设置下
        }
        //先看是不是自动打分
        if (isAuto(itemScore.getScorerType(), item.getOpenOkrScore())) {
            itemScore.setAutoScore(item.getItemScore());
            itemScore.setFinalWeightScore(item.getItemAutoScore());
            itemScore.setItemFinalScore(item.getItemAutoScore());
            return;
        }

        itemScore.setFinalWeightScore(item.getItemFinalScore());//计算指标得分【等于各个指标的环节分之和】
        itemScore.setItemFinalScore(item.getItemFinalScore());//计算指标得分【等于各个指标的环节分之和】
        List<EmpEvalTaskScore3Po.ScoreNodeScore> scoreDetails = itemScore.getScoreNodeScores();//scoreNodeScores
        if (CollUtil.isEmpty(scoreDetails)) {
            return;
        }
        //非加减分类，加分类，减分类
        if (!typeClassifyIsPlusAndSub(kpiTypeClassify) && Objects.equals(kpiTypeClassify, "oneVoteVeto")) {
            itemScore.setVetoFlag(getVetoFlag(scoreDetails));
        }
    }
    private boolean isAuto(String scorerType,int openOkrScore){
        return BusinessConstant.SCORER_TYPE_AUTO.equals(scorerType)|| openOkrScore == 1;
    }


    private boolean scoreIsNull(BigDecimal score){
        return Objects.isNull(score);
    }

    private void initFinalScoreRecord(CalibratedEmpEvalKpiItemPo item, StringBuilder finalScoreRecord, String vetoFlag) {
        item.setFinalScoreRecord(finalScoreRecord.toString());
        item.setVetoFlag(vetoFlag);
    }

    public void hanlderResultAudit(CalibratedEmpEvalKpiItemPo item, Map<String, List<PerfEvaluateTaskScoreResultDo>> indexResultAuditMap) {
        /**如果map不为空表示有按指标校准*/
        StringBuilder finalScoreRecord = new StringBuilder();
        String vetoFlag = StrUtil.isBlank(item.getVetoFlag()) ? "false" : "true";
        if (MapUtil.isEmpty(indexResultAuditMap)) {
            if (Objects.equals(item.getKpiTypeClassify(), "oneVoteVeto")) {
                finalScoreRecord.append(vetoFlag);
            } else {
                if (!scoreIsNull(item.getFinalWeightScore())) {
                    finalScoreRecord.append(item.getFinalWeightScore().setScale(2, RoundingMode.HALF_UP));
                }
            }
            initFinalScoreRecord(item, finalScoreRecord,vetoFlag);
            return;
        }

        List<PerfEvaluateTaskScoreResultDo> resultDos = indexResultAuditMap.get(item.getKpiItemId());
        if (CollUtil.isEmpty(resultDos)) {
            initFinalScoreRecord(item, finalScoreRecord,vetoFlag);
            return;
        }

        /**按审核排序*/
        resultDos.sort(Comparator.comparing(PerfEvaluateTaskScoreResultDo::getApprovalOrder).reversed());
        for (int i = 0; i < resultDos.size(); i++) {
            BigDecimal score = resultDos.get(i).getScore();
            String itemVetoFlag = resultDos.get(i).getVetoFlag();
            finalScoreRecord.append(score == null ? StringUtils.isBlank(itemVetoFlag) ? "" : itemVetoFlag : score.setScale(2, RoundingMode.HALF_UP)).append("<<");
        }
        finalScoreRecord.append(scoreIsNull(item.getFinalWeightScore()) ? StringUtils.isBlank(vetoFlag) ? "" : vetoFlag : item.getFinalWeightScore().setScale(2, RoundingMode.HALF_UP));
        /**因为经过倒叙排列，所以直接取第一个下标的对象的值。*/
        PerfEvaluateTaskScoreResultDo first = resultDos.get(0);
        BigDecimal score =first.getScore();
        item.setFinalWeightScore(scoreIsNull(score) ? null : score.setScale(2, RoundingMode.HALF_UP));
        item.setIndLevel(first.getScoreLevel());
        vetoFlag = first.getVetoFlag();
        initFinalScoreRecord(item, finalScoreRecord,vetoFlag);
    }
    public boolean typeClassifyIsPlusAndSub(String kpiTypeClassify) {
        return BusinessConstant.KPI_TYPE_CLASSIFY_PLUS_SUB.equals(kpiTypeClassify)
                || BusinessConstant.KPI_TYPE_CLASSIFY_PLUS.equals(kpiTypeClassify)
                || BusinessConstant.KPI_TYPE_CLASSIFY_SUBTRACT.equals(kpiTypeClassify);
    }
    private String getVetoFlag(List<EmpEvalTaskScore3Po.ScoreNodeScore> scoreDetailList) {
        if (CollectionUtils.isEmpty(scoreDetailList)) {
            return null;
        }
        int vetoFlagIdx = 0;
        for (EmpEvalTaskScore3Po.ScoreNodeScore scoreNodeScore : scoreDetailList) {
            List<EmpEvalTaskScore3Po.SubmitedScoreResult> scoreResultList = scoreNodeScore.getSubmitedRs();
            if (CollUtil.isEmpty(scoreResultList)) {
                continue;
            }
            vetoFlagIdx += scoreResultList.stream().filter(rs -> Objects.equals(rs.getVetoFlag(), Boolean.TRUE.toString())).collect(Collectors.toList()).size();
        }
        if (vetoFlagIdx > 0) {
            return Boolean.TRUE.toString();
        }
        return Boolean.FALSE.toString();
    }


    public void setItemScore(CalibratedPerfEvaluateTaskResultPo item, Map<String, List<PerfEvaluateTaskScoreResultDo>> indexResultAuditMap) {
        /**如果map不为空表示有按指标校准*/
        StringBuilder finalScoreRecord = new StringBuilder();
        int vetoFlagCnt = CollUtil.isEmpty(item.getResultPoList()) ? 0 : CollUtil.filterNew(item.getResultPoList(), r -> Objects.equals(r.getVetoFlag(), "true")).size();
        String vetoFlag = vetoFlagCnt == 0 ? "false" : "true";
        if (CollUtil.isNotEmpty(indexResultAuditMap)) {
            if (indexResultAuditMap.get(item.getKpiItemId()) != null) {
                List<PerfEvaluateTaskScoreResultDo> resultDo = indexResultAuditMap.get(item.getKpiItemId());
                /**按审核排序*/
                resultDo.sort(Comparator.comparing(PerfEvaluateTaskScoreResultDo::getApprovalOrder).reversed());
                for (int i = 0; i < resultDo.size(); i++) {
                    BigDecimal score = resultDo.get(i).getScore();
                    String itemVetoFlag = resultDo.get(i).getVetoFlag();
                    finalScoreRecord.append(score == null ? StringUtils.isBlank(itemVetoFlag) ? "" : itemVetoFlag : score.setScale(2, BigDecimal.ROUND_HALF_UP)).append("<<");
                }
                finalScoreRecord.append(item.getFinalWeightScore() == null ? StringUtils.isBlank(vetoFlag) ? "" : vetoFlag : item.getFinalWeightScore().setScale(2, BigDecimal.ROUND_HALF_UP));
                /**因为经过倒叙排列，所以直接取第一个下标的对象的值。*/
                BigDecimal score = resultDo.get(0).getScore();
                item.setFinalWeightScore(score == null ? null : score.setScale(2, BigDecimal.ROUND_HALF_UP));
                item.setIndLevel(resultDo.get(0).getScoreLevel());
                vetoFlag = resultDo.get(0).getVetoFlag();
            }
        } else {
            if (item.getFinalWeightScore() != null) {
                finalScoreRecord.append(item.getFinalWeightScore().setScale(2, BigDecimal.ROUND_HALF_UP));
            } else {
                finalScoreRecord.append(vetoFlag);
            }
        }
        item.setFinalScoreRecord(finalScoreRecord.toString());
        item.setVetoFlag(vetoFlag);
    }

    @Override
    public String taskUserId() {
        return taskUserId;
    }

    @Override
    public void applyScoreRange(List<RankRuleScoreRangeSnap> ranges) {
        this.setScoreRanges(ranges);
        this.levelDefType = CollUtil.isNotEmpty(ranges) ? ranges.get(0).getLevelDefType() : null;
    }

    //将初评分数追加入历史分值
    public void loadHistoryScore(HistoryScorePo historyScore) {
        if (historyScore == null) {
            this.historyScore = originaScoreStr();
            return;
        }
        this.historyScore = historyScore.getHistoryScore();
        this.historyScore.addAll(originaScoreStr());
    }

    @NotNull
    private List<HistoryScorePo.HScore> originaScoreStr() {
        return Arrays.asList(new HistoryScorePo.HScore(originalFinalScore, originalEvaluationLevel,originalPerfCoefficient));
    }


    @Getter
    @Setter
    @NoArgsConstructor
    public static class ScoreNodeScore {

        private String scorerType;//node
        private BigDecimal nodeWeight;//
        private BigDecimal score;//环节分

        public ScoreNodeScore(String scorerType, BigDecimal nodeWeight, BigDecimal score) {
            this.scorerType = scorerType;
            this.nodeWeight = nodeWeight;
            this.score = score;
        }
    }
}
