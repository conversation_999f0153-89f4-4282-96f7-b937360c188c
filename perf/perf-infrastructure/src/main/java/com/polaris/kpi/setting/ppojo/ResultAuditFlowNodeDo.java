package com.polaris.kpi.setting.ppojo;


import com.polaris.kpi.common.infr.DelData;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.ibatis.annotations.Ckey;

import java.util.Date;

@Setter
@Getter
@NoArgsConstructor
public class ResultAuditFlowNodeDo extends DelData {

    @Ckey
    private String id;
    private String flowInstanceId;
    private String taskUserId;
    private Integer approvalOrder;
    private String multipleReviewersType;  //会签/或签
    private Integer status;   // 审批状态(0:未开始  1:执行中 2:已完成 4:已驳回)
    private Integer skipFlag;
    public ResultAuditFlowNodeDo(String id,String companyId, String flowInstanceId,String taskUserId,
                                 Integer approvalOrder, String multipleReviewersType, Integer status,Integer skipFlag, String createdUser) {
        this.id = id;
        this.companyId = companyId;
        this.flowInstanceId = flowInstanceId;
        this.taskUserId = taskUserId;
        this.approvalOrder = approvalOrder;
        this.multipleReviewersType = multipleReviewersType;
        this.status = status;
        this.skipFlag = skipFlag;
        this.createdUser = createdUser;
        this.createdTime = new Date();
    }
}
