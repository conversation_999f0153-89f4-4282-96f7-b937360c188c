package com.polaris.kpi.setting.query;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.lufei.ibatis.mapper.PagedList;
import org.lufei.ibatis.mapper.PagedQuery;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class ManagerPrivLogQuery extends PagedQuery {
    private String companyId;
    private String startDate;
    private String endDate;
    private List<String> operationEmpIds;

    public void accOp(String companyId) {
        this.companyId = companyId;
    }
}
