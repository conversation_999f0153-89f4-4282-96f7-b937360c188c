package com.polaris.kpi.report.infr.dao;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.perf.www.common.utils.string.StringTool;
import com.polaris.kpi.eval.infr.task.ppojo.*;
import com.polaris.kpi.eval.infr.task.ppojo.report.DirectSubordinatePerformancePo;
import com.polaris.kpi.ind.infr.ppojo.UserFocusItemDo;
import com.polaris.kpi.org.domain.emp.entity.KpiEmployee;
import com.polaris.kpi.org.infr.emp.pojo.EmpOrganizationDo;
import com.polaris.kpi.org.infr.emp.pojo.EmpRefOrgDo;
import com.polaris.kpi.org.infr.emp.pojo.EmployeeBaseInfoDo;
import com.polaris.kpi.report.domain.entity.ItemAnalysisItemInfo;
import com.polaris.kpi.report.domain.entity.ItemAnalysisOrgInfo;
import com.polaris.kpi.report.infr.pojo.*;
import com.polaris.kpi.report.infr.query.ItemAnalysisQuery;
import com.polaris.sdk.type.TenantId;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.lufei.ibatis.mapper.PagedList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/12 15:56
 */
@Component
public class ItemAnalysisDao {

    @Autowired
    protected DomainDaoImpl domainDao;

    public void setDomainDao(DomainDaoImpl domainDao) {
        this.domainDao = domainDao;
    }


    public List<ItemAnalysisOrgInfo> listItemAnalysisOrgInfo(String companyId, int pageNo, List<String> orgIds) {

        ComQB comQB = ComQB.build(EmpOrganizationDo.class).clearSelect()
                .select("org_id,org_name")
                .select("(LENGTH(org_code) - LENGTH(REPLACE(org_code, '|', ''))) - 1 AS org_path_height")
                .select("direct_child_org_cnt,parent_org_id")
                .setRsType(ItemAnalysisOrgInfo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("status", "valid")
                .whereIn("org_id", orgIds)
                .orderBy("org_path_height")
                .limit((pageNo - 1) * 20, pageNo * 20);

        return domainDao.listAll(comQB);
    }

    public List<ItemAnalysisItemInfo> listItemAnalysisItemInfo(String companyId , List<String> pagedOrgIds, List<String> itemIds, String cycleId, Integer performanceType) {

        if (CollUtil.isEmpty(itemIds)){
            return new ArrayList<>();
        }

        ComQB comQB = ComQB.build(PerfEvaluateTaskKpiDo.class, "k")
                .join(PerfEvaluateTaskUserDo.class, "u")
                .appendOn(" k.company_id = u.company_id and u.id = k.task_user_id " +
                        "and u.is_deleted = 'false' and u.task_status <> 'drawUpIng' ")
                .clearSelect()
                .select("sum(k.item_finish_value) as finishValueSum")
                .select("sum(k.item_target_value) as targetValueSum")
                .select("IF("+ performanceType + " = 1, u.org_id, u.eval_org_id) AS org_id,k.kpi_item_id as itemId,k.kpi_item_name as itemName,k.item_unit ,k.finish_value_type ,u.at_org_code_path")
                .setRsType(ItemAnalysisItemInfo.class)
                .whereIn("k.kpi_item_id", itemIds)
                .whereEqReq("u.cycle_id", cycleId)
                .whereEqReq("k.company_id", companyId)
                .whereEqReq("k.is_deleted", Boolean.FALSE.toString())
                .appendWhere("EXISTS ( SELECT 1\n" +
                        "              FROM perf_evaluate_task_base tb\n" +
                        "              WHERE tb.id = k.task_id\n" +
                        "                AND tb.cycle_id = " + cycleId +
                        "                AND tb.performance_type =  "+ performanceType +
                        "                AND tb.is_deleted = 'false' )");

        if (performanceType == 1){
            comQB.whereIn("u.org_id", pagedOrgIds)
                    .groupBy( "u.org_id,k.kpi_item_id");
        }else {
            comQB.whereIn("u.eval_org_id", pagedOrgIds)
                    .groupBy( "u.eval_org_id,k.kpi_item_id");
        }
        return domainDao.listAll(comQB);
    }

    public PagedList<ItemFinishValueComparePo> pagedItemFinishValueCount(ItemAnalysisQuery query) {

        ComQB comQB = ComQB.build(UserFocusItemDo.class,"i")
                .join(CompanyKpiItemDo.class, "k")
                .appendOn(" i.item_id = k.id and i.company_id = k.company_id")
                .clearSelect().select("i.item_id,k.item_name,k.item_unit,i.item_order")
                .setRsType(ItemFinishValueComparePo.class)
                .whereEqReq("i.company_id", query.getCompanyId())
                .whereEqReq("i.emp_id", query.getOpEmpId())
                .whereEqReq("i.is_deleted", Boolean.FALSE.toString())
                .setPage(query.getPageNo(), query.getPageSize())
                .orderBy("i.item_order");
        return domainDao.listPage(comQB);
    }

    public List<ItemFinishValuePo> listItemFinishValues(ItemAnalysisQuery query) {

        ComQB comQB = ComQB.build(OrgItemSummaryDo.class).clearSelect()
                .select(" sum(finish_value_sum) as totalFinishValue ,item_id,item_name,cycle_id ,finish_value_type,item_unit")
                .setRsType(ItemFinishValuePo.class)
                .whereEqReq("company_id", query.getCompanyId())
                .whereIn("org_id", query.getOrgIds())
                .whereIn("org_id", query.getPrivOrgIds())
                .whereInReq("item_id", query.getItemIds())
                .whereEqReq("performance_type", query.getPerformanceType())
                .whereEqReq("is_deleted", Boolean.FALSE.toString())
                .groupBy("item_id");
        if (StringUtils.isNotBlank(query.getCycleId())){
            comQB.whereEqReq("cycle_id", query.getCycleId());
        }
        if (CollUtil.isNotEmpty(query.getCycleIds())){
            comQB.whereIn("cycle_id", query.getCycleIds());
        }
        return domainDao.listAll(comQB);
    }

    public PagedList<OrgItemFinishValuePo> pagedOrgItemFinishValueCount(ItemAnalysisQuery query) {

        ComQB subQuery = ComQB.build(OrgItemSummaryDo.class, "a");
        subQuery.clearSelect()
                .select(" distinct SUBSTRING_INDEX(SUBSTRING_INDEX(a.at_org_code_path, '|', " + (query.getOrgPathHeight() + 2) + "    ),'|',-1 ) AS org_id ,a.`company_id`")
                .whereEqReq("a.company_id", query.getCompanyId())
                .whereEqReq("a.cycle_id", query.getCycleId())
                .whereInReq("a.item_id", query.getItemIds())
                .whereEqReq("a.performance_type", query.getPerformanceType())
                .whereInReq("a.org_id", query.getOrgIds())
                .whereEqReq("a.is_deleted", Boolean.FALSE.toString());
        ComQB qb = ComQB.build().fromQ(subQuery, "tu")
                .join(EmpOrganizationDo.class, "o").appendOn("o.company_id = tu.company_id AND o.org_id = tu.org_id")
                .clearSelect().select("tu.org_id,o.`org_name`," + query.getOrgPathHeight() + " AS orgPathHeight ,o.direct_child_org_cnt")
                .setRsType(OrgItemFinishValuePo.class)
                .whereEq("tu.company_id", query.getCompanyId())
                .appendWhere(" tu.org_id is not null ");
        qb.setPage(query.getPageNo(), query.getPageSize());
        return domainDao.listPage(qb);
    }


    public List<ItemTargetAndFinishValuePo> listItemTargetAndFinishValueWithCycle(ItemAnalysisQuery query) {

        ComQB comQB = ComQB.build(OrgItemSummaryDo.class).clearSelect()
                .select(" sum(finish_value_sum) as finishValue," +
                        "sum(target_value_sum) as targetValue," +
                        "item_id,item_name,cycle_id ,finish_value_type ,item_unit")
                .setRsType(ItemTargetAndFinishValuePo.class)
                .whereEqReq("company_id", query.getCompanyId())
                .whereIn("org_id", query.getOrgIds())
                .whereIn("item_id", query.getItemIds())
                .whereEqReq("performance_type", query.getPerformanceType())
                .whereEqReq("is_deleted", Boolean.FALSE.toString())
                .groupBy("item_id");
        if (StringUtils.isNotBlank(query.getCycleId())){
            comQB.whereEqReq("cycle_id", query.getCycleId());
        }
        return domainDao.listAll(comQB);
    }

    public List<ItemTargetAndFinishValueInCyclePo> listItemTargetAndFinishValueInCycle(ItemAnalysisQuery query) {

        ComQB comQB = ComQB.build(OrgItemSummaryDo.class,"s")
                .join(CycleDo.class,"c")
                .appendOn("s.cycle_id = c.id and s.company_id = c.company_id")
                .clearSelect().select(" sum(s.finish_value_sum) as finishValue," +
                        "sum(s.target_value_sum) as targetValue," +
                        "s.item_id,s.item_name,c.id as cycleId,c.type as cycleType,c.year as cycleYear,c.value as cycleValue,s.finish_value_type,s.item_unit")
                .setRsType(ItemTargetAndFinishValueInCyclePo.class)
                .whereEqReq("s.company_id", query.getCompanyId())
                .whereIn("s.org_id", query.getOrgIds())
                .whereInReq("s.cycle_id",query.getCycleIds())
                .whereEqReq("s.item_id", query.getItemId())
                .whereEqReq("s.performance_type", query.getPerformanceType())
                .whereEqReq("s.is_deleted", Boolean.FALSE.toString())
                .groupBy("c.type,c.year,c.value,s.item_id");
        return domainDao.listAll(comQB);
    }

    public List<ItemTargetAndFinishValueTrendPo> listExistsItem(ItemAnalysisQuery query) {

        ComQB comQB = ComQB.build(OrgItemSummaryDo.class,"s").clearSelect()
                .join(UserFocusItemDo.class,"i")
                .appendOn("s.company_id=i.company_id and s.item_id=i.item_id and i.is_deleted = 'false'")
                .select(" s.item_id,s.item_name,s.item_unit,i.item_order")
                .setRsType(ItemTargetAndFinishValueTrendPo.class)
                .whereEqReq("s.company_id", query.getCompanyId())
                .whereIn("s.org_id", query.getOrgIds())
                .whereEqReq("i.emp_id", query.getOpEmpId())
                .whereEqReq("s.cycle_id",query.getCycleId())
                .whereEqReq("s.performance_type", query.getPerformanceType())
                .whereEqReq("s.is_deleted", Boolean.FALSE.toString())
                .groupBy("s.item_id");
        return domainDao.listAll(comQB);
    }

    public FocusItemCountPo queryLastOrgItemCountTime(String companyId, String cycleId, Integer performanceType) {

        ComQB comQB = ComQB.build(FocusItemCountDo.class)
                .clearSelect()
                .select("*")
                .setRsType(FocusItemCountPo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("cycle_id", cycleId)
                .whereEqReq("performance_type", performanceType)
                .whereEqReq("is_deleted",Boolean.FALSE.toString());
        return domainDao.findOne(comQB);

    }

    public List<String> listSummaryOrgIds(ItemAnalysisQuery query) {

        ComQB subQuery = ComQB.build(OrgItemSummaryDo.class, "a");
        subQuery.clearSelect()
                .select(" distinct SUBSTRING_INDEX(SUBSTRING_INDEX(a.at_org_code_path, '|', " + (query.getOrgPathHeight() + 2) + "    ),'|',-1 ) AS org_id ,a.`company_id`")
                .whereEqReq("a.company_id", query.getCompanyId())
                .whereEqReq("a.cycle_id", query.getCycleId())
                .whereInReq("a.item_id", query.getItemIds())
                .whereEqReq("a.performance_type", query.getPerformanceType())
                .whereInReq("a.org_id", query.getOrgIds())
                .whereEqReq("a.is_deleted", Boolean.FALSE.toString());
        ComQB qb = ComQB.build().fromQ(subQuery, "tu")
                .join(EmpOrganizationDo.class, "o").appendOn("o.company_id = tu.company_id AND o.org_id = tu.org_id")
                .clearSelect().select("tu.org_id")
                .setRsType(String.class)
                .whereEq("tu.company_id", query.getCompanyId())
                .appendWhere(" tu.org_id is not null ");

        return domainDao.listAll(qb);

    }

    public List<ItemTargetAndFinishValuePo> listItemsCnt(ItemAnalysisQuery query) {

        return null;
    }

    public Integer getItemUpdatedEmpCnt(ItemAnalysisQuery query) {

        return null;
    }

    public Integer getItemTotalEmpCnt(ItemAnalysisQuery query) {

        return null;
    }

    public List<OrgFinishValue> listOrgItemFinishValues(ItemAnalysisQuery query) {

        ComQB comQB = ComQB.build(OrgItemSummaryDo.class).clearSelect()
                .select(" sum(finish_value_sum) as finishValue," +
                        "sum(target_value_sum) as targetValue," +
                        "item_id," +
                        "item_name," +
                        "finish_value_type," +
                        "item_unit," +
                        "org_id")
                .setRsType(OrgFinishValue.class)
                .whereEqReq("company_id", query.getCompanyId())
                .whereIn("org_id", query.getOrgIds())
                .whereIn("item_id", query.getItemIds())
                .whereEqReq("performance_type", query.getPerformanceType())
                .whereEqReq("is_deleted", Boolean.FALSE.toString())
                .whereEqReq("cycle_id", query.getCycleId())
                .groupBy("item_id", "org_id");
        return domainDao.listAll(comQB);
    }

    public PagedList<ItemEmpFinishValuePo> pagedEmp(ItemAnalysisQuery query) {

        ComQB comQB = ComQB.build(PerfEvaluateTaskBaseDo.class, "tb")
                .join(PerfEvaluateTaskUserDo.class, "u")
                .appendOn("u.company_id = tb.company_id and u.task_id = tb.id and tb.is_deleted = 'false'")
                .clearSelect()
                .select("IF("+ query.getPerformanceType() + " = 1, u.org_id, u.eval_org_id) AS orgId")
                .select("IF("+ query.getPerformanceType() + " = 1, u.emp_org_name, u.eval_org_name) AS orgName")
                .select("u.emp_id")
                .select("u.emp_name")
                .select("u.avatar")
                .select("u.task_id")
                .select("u.id as taskUserId,tb.task_name")
                .select("u.at_org_name_path")
                .setRsType(ItemEmpFinishValuePo.class)
                .whereEqReq("u.company_id", query.getCompanyId())
                .whereEqReq("u.cycle_id",query.getCycleId())
                .whereEqReq("u.is_deleted",Boolean.FALSE.toString())
                .whereNotEq("u.task_status", "drawUpIng")
                .orderBy("tb.task_name,u.emp_id")
                .setPage(query.getPageNo(), query.getPageSize());

        if (query.getPerformanceType() == 1){
            comQB.whereIn("u.org_id",query.getOrgIds())
            .whereIsNull("u.eval_org_id");
        }else {
            comQB.whereIn("u.eval_org_id",query.getOrgIds())
            .whereNotNull("u.eval_org_id");
        }

        return domainDao.listPage(comQB);
    }

    public List<TaskUserWithItemFinishPo> listItemEmpFinishValue(ItemAnalysisQuery query) {

        ComQB comQB = ComQB.build(PerfEvaluateTaskKpiDo.class, "k")
                .join(PerfEvaluateTaskUserDo.class, "u")
                .appendOn(" k.company_id = u.company_id and u.id = k.task_user_id " +
                        "and u.is_deleted = 'false' and u.task_status <> 'drawUpIng' ")
                .clearSelect()
                .select("sum(k.item_finish_value) as finishValue")
                .select("sum(k.item_target_value) as targetValue")
                .select("u.id as taskUserId," +
                        "k.kpi_item_id as itemId," +
                        "k.kpi_item_name as itemName," +
                        "k.item_unit," +
                        "k.finish_value_type")
                .setRsType(TaskUserWithItemFinishPo.class)
                .whereIn("k.kpi_item_id", query.getItemIds())
                .whereIn("u.id", query.getTaskUserIds())
                .whereEqReq("u.cycle_id", query.getCycleId())
                .whereEqReq("k.company_id", query.getCompanyId())
                .whereEqReq("k.is_deleted", Boolean.FALSE.toString())
                .appendWhere("EXISTS ( SELECT 1\n" +
                        "              FROM perf_evaluate_task_base tb\n" +
                        "              WHERE tb.id = k.task_id\n" +
                        "                AND tb.cycle_id = " + query.getCycleId() +
                        "                AND tb.performance_type = " + query.getPerformanceType() +
                        "                AND tb.is_deleted = 'false' )")
                .groupBy("u.id,k.kpi_item_id");
        return domainDao.listAll(comQB);
    }

    public PagedList<OrgItemYearlyFinishValueCountPo> pagedOrgItemYearlyFinishValueCount(ItemAnalysisQuery query) {

        ComQB subQuery = ComQB.build(OrgItemSummaryDo.class, "a");
        subQuery.clearSelect()
                .select(" distinct SUBSTRING_INDEX(SUBSTRING_INDEX(a.at_org_code_path, '|', " + (query.getOrgPathHeight() + 2) + "    ),'|',-1 ) AS org_id ,a.`company_id`")
                .whereEqReq("a.company_id", query.getCompanyId())
                .whereEqReq("a.cycle_id", query.getCycleId())
                .whereEqReq("a.item_id", query.getItemId())
                .whereEqReq("a.performance_type", query.getPerformanceType())
                .whereInReq("a.org_id", query.getOrgIds())
                .whereEqReq("a.is_deleted", Boolean.FALSE.toString());
        ComQB qb = ComQB.build().fromQ(subQuery, "tu")
                .join(EmpOrganizationDo.class, "o").appendOn("o.company_id = tu.company_id AND o.org_id = tu.org_id")
                .clearSelect().select("tu.org_id,o.`org_name`," + query.getOrgPathHeight() + " AS orgPathHeight")
                .setRsType(OrgItemYearlyFinishValueCountPo.class)
                .whereEq("tu.company_id", query.getCompanyId())
                .appendWhere(" tu.org_id is not null ");
        qb.setPage(query.getPageNo(), query.getPageSize());
        return domainDao.listPage(qb);
    }

    public List<OrgYearlyItemTargetAndFinishValue> listOrgYearlyItems(ItemAnalysisQuery query) {

        ComQB comQB = ComQB.build(OrgItemSummaryDo.class,"s")
                .join(CycleDo.class,"c")
                .appendOn("s.cycle_id = c.id and s.company_id = c.company_id")
                .clearSelect().select(" sum(s.finish_value_sum) as finishValue," +
                        "sum(s.target_value_sum) as targetValue," +
                        "s.item_id,s.item_name,c.type as cycleType,c.year as cycleYear,c.value as cycleValue,s.finish_value_type,s.item_unit," +
                        "s.org_id")
                .setRsType(OrgYearlyItemTargetAndFinishValue.class)
                .whereEqReq("s.company_id", query.getCompanyId())
                .whereIn("s.org_id", query.getOrgIds())
                .whereInReq("s.cycle_id",query.getCycleIds())
                .whereEqReq("s.item_id", query.getItemId())
                .whereEqReq("s.performance_type", query.getPerformanceType())
                .whereEqReq("s.is_deleted", Boolean.FALSE.toString())
                .groupBy("c.type,c.year,c.value,s.item_id,s.org_id");
        return domainDao.listAll(comQB);
    }

    public List<OrgYearlyItemTargetAndFinishValue> listOrgYearlySummaryItems(ItemAnalysisQuery query) {

        ComQB comQB = ComQB.build(OrgItemSummaryDo.class,"s")
                .join(CycleDo.class,"c")
                .appendOn("s.cycle_id = c.id and s.company_id = c.company_id")
                .clearSelect().select(" sum(s.finish_value_sum) as finishValue," +
                        "sum(s.target_value_sum) as targetValue," +
                        "s.item_id,s.item_name,c.type as cycleType,c.year as cycleYear,c.value as cycleValue,s.finish_value_type,s.item_unit")
                .setRsType(OrgYearlyItemTargetAndFinishValue.class)
                .whereEqReq("s.company_id", query.getCompanyId())
                .whereIn("s.org_id", query.getOrgIds())
                .whereInReq("s.cycle_id",query.getCycleIds())
                .whereEqReq("s.item_id", query.getItemId())
                .whereEqReq("s.performance_type", query.getPerformanceType())
                .whereEqReq("s.is_deleted", Boolean.FALSE.toString())
                .groupBy("c.type,c.year,c.value,s.item_id");
        return domainDao.listAll(comQB);
    }

    public List<OrgItemTrendPo> listOrgItemYearlyFinishValueTrend(ItemAnalysisQuery query) {

        ComQB comQB = ComQB.build(OrgItemSummaryDo.class,"s")
                .join(CycleDo.class,"c")
                .appendOn("s.cycle_id = c.id and s.company_id = c.company_id")
                .clearSelect().select(" sum(s.finish_value_sum) as finishValue," +
                        "sum(s.target_value_sum) as targetValue," +
                        "s.item_id,s.item_name," +
                        "c.type as cycleType," +
                        "c.year as cycleYear," +
                        "c.value as cycleValue," +
                        "s.finish_value_type," +
                        "s.item_unit," +
                        "s.org_id")
                .setRsType(OrgItemTrendPo.class)
                .whereEqReq("s.company_id", query.getCompanyId())
                .whereIn("s.org_id", query.getOrgIds())
                .whereInReq("s.cycle_id",query.getCycleIds())
                .whereEqReq("s.item_id", query.getItemId())
                .whereEqReq("s.performance_type", query.getPerformanceType())
                .whereEqReq("s.is_deleted", Boolean.FALSE.toString())
                .groupBy("c.type,c.year,c.value,s.item_id,s.org_id");
        return domainDao.listAll(comQB);
    }

    public PagedList<EmpItemYearlyFinishValueCountPo> pagedEmpYearly(ItemAnalysisQuery query) {

        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .clearSelect()
//                .select("IF("+ query.getPerformanceType() + " = 1, u.emp_id, u.eval_org_id) AS empId")
//                .select("IF("+ query.getPerformanceType() + " = 1, u.emp_name, u.eval_org_name) AS empName")
                .select("u.avatar")
                .select("u.emp_id")
                .select("u.emp_name")
                .select("u.eval_org_id")
                .select("u.eval_org_name")
                .setRsType(EmpItemYearlyFinishValueCountPo.class)
                .whereIn("u.org_id",query.getOrgIds())
                .whereEqReq("u.company_id", query.getCompanyId())
                .whereEqReq("u.cycle_id",query.getCycleId())
                .whereEqReq("u.is_deleted",Boolean.FALSE.toString())
                .setPage(query.getPageNo(), query.getPageSize());

        if (query.getPerformanceType() == 1){
            comQB.select("GROUP_CONCAT(DISTINCT u.emp_org_name SEPARATOR ',') AS empOrgNames")
                    .whereIsNull("u.eval_org_id")
                    .groupBy("u.emp_id")
                    .orderBy("u.at_org_path_hight,u.emp_id");
        }else {
            comQB.whereNotNull("u.eval_org_id")
                    .groupBy("u.eval_org_id")
                    .orderBy("u.at_org_path_hight,u.eval_org_id");
        }

        return domainDao.listPage(comQB);
    }

    public List<EmpYearlyItemTargetAndFinishValue> listEmpYearlyItems(ItemAnalysisQuery query) {

        ComQB comQB = ComQB.build(PerfEvaluateTaskKpiDo.class, "k")
                .join(PerfEvaluateTaskUserDo.class, "u")
                .appendOn(" k.company_id = u.company_id and u.id = k.task_user_id " +
                        "and u.is_deleted = 'false' and u.task_status <> 'drawUpIng' ")
                .join(CycleDo.class,"c")
                .appendOn("u.cycle_id = c.id and u.company_id = c.company_id")
                .clearSelect()
                .select("sum(k.item_finish_value) as finishValue")
                .select("sum(k.item_target_value) as targetValue")
                .select("u.emp_id," +
                        "k.kpi_item_id as itemId," +
                        "k.kpi_item_name as itemName," +
                        "k.item_unit," +
                        "k.finish_value_type," +
                        "c.type as cycleType," +
                        "c.year as cycleYear," +
                        "c.value as cycleValue")
                .setRsType(EmpYearlyItemTargetAndFinishValue.class)
                .whereEqReq("k.kpi_item_id", query.getItemId())
                .whereIn("u.emp_id", query.getEmpIds())
                .whereIsNull("u.eval_org_id")
                .whereIn("u.cycle_id", query.getCycleIds())
                .whereEqReq("k.company_id", query.getCompanyId())
                .whereEqReq("k.is_deleted", Boolean.FALSE.toString())
                .groupBy("c.type,c.year,c.value,u.emp_id");

        return domainDao.listAll(comQB);
    }

    public List<EmpYearlyItemTargetAndFinishValue> listEvalOrgYearlyItems(ItemAnalysisQuery query) {

        ComQB comQB = ComQB.build(OrgItemSummaryDo.class,"s")
                .join(CycleDo.class,"c")
                .appendOn("s.cycle_id = c.id and s.company_id = c.company_id")
                .clearSelect().select(" sum(s.finish_value_sum) as finishValue," +
                        "sum(s.target_value_sum) as targetValue," +
                        "s.item_id," +
                        "s.org_id as evalOrgId," +
                        "s.item_name," +
                        "c.type as cycleType," +
                        "c.year as cycleYear," +
                        "c.value as cycleValue," +
                        "s.finish_value_type," +
                        "s.item_unit")
                .setRsType(EmpYearlyItemTargetAndFinishValue.class)
                .whereEqReq("s.company_id", query.getCompanyId())
                .whereIn("s.org_id", query.getOrgIds())
                .whereInReq("s.cycle_id",query.getCycleIds())
                .whereEqReq("s.item_id", query.getItemId())
                .whereEqReq("s.performance_type", query.getPerformanceType())
                .whereEqReq("s.is_deleted", Boolean.FALSE.toString())
                .groupBy("c.type,c.year,c.value,s.org_id");
        return domainDao.listAll(comQB);
    }

    public PagedList<ItemEmpFinishValuePo> pagedDirectBelowEmp(ItemAnalysisQuery query, KpiEmployee employee) {

        //收集直属下级empId,包含直属下级和部门下级

        //找到自己的dingUserId
        String dingUserId = employee.getDingUserId();
        //收集emp_id去重
        //查询到各种意义下的直属下级进行遍历
        ComQB qb = ComQB.build(EmployeeBaseInfoDo.class, "a")
                .clearSelect().select("employee_id")
                .setRsType(String.class)
                .whereEqReq("company_id", query.getCompanyId())
                .whereEq("ding_manager_id", dingUserId);
        List<String> empIds = domainDao.listAll(qb);

        //查询当前人员所在的orgId
        ComQB orgIdQB = ComQB.build(EmpRefOrgDo.class, "a")
                .clearSelect().select("org_id")
                .setRsType(String.class)
                .whereEqReq("company_id", query.getCompanyId())
                .whereEqReq("emp_id", query.getOpEmpId())
                .whereEqReq("ref_type", "manager");
        List<String> manageOrgIds = domainDao.listAll(orgIdQB);

        if (CollUtil.isNotEmpty(manageOrgIds)){
            //查询部门下直属的员工
            ComQB empIdQB = ComQB.build(EmpRefOrgDo.class, "a")
                    .clearSelect().select("emp_id")
                    .setRsType(String.class)
                    .whereEqReq("company_id", query.getCompanyId())
                    .whereInReq("org_id", manageOrgIds)
                    .whereEqReq("ref_type", "org")
                    .appendWhere("emp_id not in ( select emp_id from emp_ref_org where company_id = '"+query.getCompanyId()
                            +"' and org_id in "+ StringTool.getInStrSql(manageOrgIds) +" and ref_type = 'manager' )");

            List<String> subEmpIds = domainDao.listAll(empIdQB);

            //两个list去重
            empIds.addAll(subEmpIds);
            empIds = empIds.stream().distinct().collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(empIds)){ //无直接管理人员
            return new PagedList<>();
        }

        ComQB comQB = ComQB.build(PerfEvaluateTaskBaseDo.class, "tb")
                .join(PerfEvaluateTaskUserDo.class, "u")
                .appendOn("u.company_id = tb.company_id and u.task_id = tb.id and tb.is_deleted = 'false'")
                .clearSelect()
                .select("u.emp_id,u.emp_name")
                .select("u.avatar,u.org_id")
                .select("u.emp_org_name as orgName,u.task_id")
                .select("u.id as taskUserId,tb.task_name")
                .select("u.at_org_name_path")
                .setRsType(ItemEmpFinishValuePo.class)
                .whereIn("u.org_id",query.getOrgIds())
                .whereIn("u.emp_id", empIds)
                .whereEqReq("u.company_id", query.getCompanyId())
                .whereEqReq("u.cycle_id",query.getCycleId())
                .whereEqReq("u.is_deleted",Boolean.FALSE.toString())
                .whereIsNull("u.eval_org_id")
                .orderBy("tb.task_name,u.emp_id")
                .setPage(query.getPageNo(), query.getPageSize());

        return domainDao.listPage(comQB);

    }
}
