package com.polaris.kpi.eval.infr.companyItem.ppojo;


import com.polaris.kpi.common.infr.DelData;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.annotations.Ckey;
import org.apache.ibatis.annotations.Table;

@Setter
@Getter
@Table("company_item_used_field")
public class CompanyItemUsedFieldDo extends DelData {

    @Ckey
    private String fieldId;     //company_kpi_item_custom_field.id
    private String kpiItemId;      //company_kpi_item.id
    private String name;            //字段名称【复制于company_kpi_item_custom_field】
    private String value;
    private Integer type;       //字段类型（1：输入框  2：开关 3：下拉选 4：复选 5：img）【复制于company_kpi_item_custom_field】
    private Integer req;        //是否必填（0：否  1：是）【复制于company_kpi_item_custom_field】
    private String status;      //指标字段状态（valid：有效，invalid：无效）【复制于company_kpi_item_custom_field】
    private Integer show;       //是否显示（0：否  1：是）
    private Integer sort;       //指标字段排序值
    private Integer adminType;  //1:系统默认字段  0：自定义字段
    private String fieldUnit;       //字段单位

}
