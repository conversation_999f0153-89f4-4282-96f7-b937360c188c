package com.polaris.kpi.eval.infr.statics.repimpl;

import com.polaris.kpi.eval.domain.statics.entity.ConsecutiveStatistic;
import com.polaris.kpi.eval.domain.statics.repo.ConsecutiveStatisticRepo;
import com.polaris.kpi.eval.infr.statics.ppojo.ConsecutiveStatisticDo;
import org.lufei.ibatis.builder.DeleteBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/2/23 22:41
 */
@Component
public class ConsecutiveStatisticRepoImpl implements ConsecutiveStatisticRepo {

    public static final String consecutiveStatisticSeq = "consecutive_statistic";

    @Resource
    private DomainDaoImpl domainDao;

    @Override
    public void deleteStatics(String companyId, String cycleId, Integer ruleType, Integer performanceType) {

        DeleteBuilder delete = DeleteBuilder.build(ConsecutiveStatisticDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("cycle_id", cycleId)
                .whereEqReq("rule_type", ruleType)
                .whereEqReq("performance_type", performanceType);

        domainDao.delete(delete);

    }

    @Override
    public void addStatics(ConsecutiveStatistic statics) {
        statics.initOnNew(domainDao.nextLongAsStr(consecutiveStatisticSeq));
        domainDao.save(statics);

    }
}
