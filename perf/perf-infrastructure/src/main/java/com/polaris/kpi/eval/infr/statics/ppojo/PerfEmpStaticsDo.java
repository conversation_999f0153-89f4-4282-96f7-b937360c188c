package com.polaris.kpi.eval.infr.statics.ppojo;

import com.polaris.kpi.common.infr.BaseWithCompanyIdData;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.ibatis.annotations.Ckey;
import org.apache.ibatis.annotations.Table;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/1/20 20:33
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Table("perf_emp_statics")
public class PerfEmpStaticsDo extends BaseWithCompanyIdData {


    /**
     * 唯一标识符，用于唯一标识该考核记录，在数据存储和查询中作为主键使用。
     */
    @Ckey
    private String id;

    /**
     * 周期 ID，标识该考核所属的特定周期，可用于按周期对考核数据进行分组和统计。
     */
    private String cycleId;

    /**
     * 任务 ID，唯一标识此次考核所关联的任务，方便与具体的任务信息进行关联和查询。
     */
    private String taskId;

    /**
     * 任务名称，明确此次考核所对应的任务的具体名称，便于直观识别任务内容。
     */
    private String taskName;

    /**
     * 任务用户 ID，关联执行该任务的用户，使用 @Ckey 注解可能有特定业务用途，如作为某种关键标识。
     */
    @Ckey
    private String taskUserId;

    /**
     * 是否组织考核的标识，用于判断此次考核是否为组织层面的考核，可能的取值为特定的字符串，如 "Y" 表示是，"N" 表示否。
     */
    private Integer performanceType;

    /**
     * 被考核人 ID，唯一标识被考核的员工，可用于关联员工的其他相关信息。
     */
    private String empId;

    /**
     * 被考核人姓名，记录被考核员工的真实姓名，方便直观查看被考核对象。
     */
    private String empName;

    /**
     * 被考核人头像信息，可能是头像的存储路径或唯一标识，用于展示被考核人的形象。
     */
    private String avatar;

    /**
     * 组织 ID，标识该考核所关联的组织，可用于组织相关的统计和管理。
     */
    private String orgId;

    /**
     * 组织名称，对应组织 ID 的具体组织名称，便于识别组织信息。
     */
    private String orgName;

    private String roleNames;

    /**
     * 被考核组织 ID，若考核对象为组织时，用于唯一标识该被考核组织。
     */
    private String evalOrgId;

    /**
     * 被考核组织名称，对应被考核组织 ID 的具体组织名称，方便了解考核的组织对象。
     */
    private String evalOrgName;

    /**
     * 组织名称路径，可能表示组织在组织架构中的层级路径名称，以特定分隔符连接各层级组织名称。
     */
    private String atOrgNamePath;

    /**
     * 组织编码路径，可能表示组织在组织架构中的层级路径编码，以特定分隔符连接各层级组织编码。
     */
    private String atOrgCodePath;

    /**
     * 考核分，使用 BigDecimal 类型存储以保证数值的精度，反映被考核对象在此次考核中的最终得分。
     */
    private BigDecimal finalScore;

    /**
     * 绩效等级，用字符串表示被考核对象的绩效等级，如 "优秀"、"良好"、"合格"、"不合格" 等。
     */
    private String evaluationLevel;

    /**
     * 等级排序，整数类型，用于对绩效等级进行排序，可用于按等级高低进行数据排列和统计。
     */
    private Integer evaluationLevelSort;

    /**
     * 绩效系数，与绩效等级相关的系数，可能用于计算绩效奖金等，通常为一个特定的数值字符串。
     */
    private String perfCoefficient;

    /**
     * 高绩效或低绩效的标识。取值为 1 表示高绩效，取值为 2 表示低绩效，用于快速区分绩效水平。
     */
    private Integer levelHeight;

}
