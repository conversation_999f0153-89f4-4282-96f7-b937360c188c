//package com.polaris.kpi.report.infr.repimpl;
//
//import com.polaris.kpi.report.domain.entity.CycleOrgSnap;
//import com.polaris.kpi.report.domain.entity.Department;
//import com.polaris.kpi.report.domain.repo.CycleOrgSnapRepo;
//import com.polaris.kpi.report.infr.pojo.CycleOrgSnapDo;
//import com.quick.common.util.date.DateTimeUtils;
//import org.lufei.ibatis.builder.DeleteBuilder;
//import org.lufei.ibatis.builder.NativeSQLBuilder;
//import org.lufei.ibatis.dao.DomainDaoImpl;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.UUID;
//
///**
// * <AUTHOR>
// * @date 2025/2/8 17:11
// */
//@Component
//public class CycleOrgSnapRepoImpl implements CycleOrgSnapRepo {
//
//    @Resource
//    private DomainDaoImpl domainDao;
//
//    public void updateCycleOrgSnap(String companyId, String cycleId, Department root) {
//
//        // 先删除
//        DeleteBuilder build = DeleteBuilder.build(CycleOrgSnapDo.class);
//        build.whereEq("companyId", companyId).whereEq("cycleId", cycleId);
//        domainDao.delete(build);
//
//        // 收集需要插入的 CycleOrgSnap 对象
//        List<CycleOrgSnap> snaps = new ArrayList<>();
//        collectCycleOrgSnaps(companyId, cycleId, root, snaps);
//
//        // 批量插入
//        domainDao.saveBatch(snaps, "cycle_org_snap");
//    }
//
//    @Override
//    public void insertIfNotExist(String companyId, String cycleId, Department node) {
//
//        // 构建 NativeSQLBuilder 对象
//        NativeSQLBuilder<CycleOrgSnapDo> builder = NativeSQLBuilder.build(CycleOrgSnapDo.class);
//
//        StringBuilder sql = new StringBuilder("INSERT INTO cycle_org_snap " +
//                "(id , company_id , cycle_id ,org_id ,org_name ,parent_org_id ,created_user ,created_time,updated_user ,updated_time ,version) VALUES ");
//
//        sql.append(String.format("('%s', '%s', '%s', '%s', '%s', '%s','%s', '%s', '%s', '%s' , '%d' )",
//                UUID.randomUUID(),
//                companyId,
//                cycleId,
//                node.getId(),
//                node.getName(),
//                node.getParent_id(),
//                "",
//                DateTimeUtils.now2StrDateTime(),
//                "",
//                DateTimeUtils.now2StrDateTime(),
//                0
//        ));
//
//        String insertPart = sql.toString();
//        // 要更新的字段列表
//        String[] updateFields = {"updated_time"};
//        // 构造 ON DUPLICATE KEY UPDATE 部分的 SQL
//        StringBuilder updatePart = new StringBuilder(" ON DUPLICATE KEY UPDATE ");
//        int index = 0;
//        for (String field : updateFields) {
//            if (index > 0) {
//                updatePart.append(", ");
//            }
//            updatePart.append(field).append(" = VALUES(").append(field).append(")");
//            index++;
//        }
//
//        // 合并 SQL
//        String finalSql = insertPart + updatePart;
//        builder.setSql(finalSql);
//        domainDao.nativeExecute(builder);
//    }
//
//    private void collectCycleOrgSnaps(String companyId, String cycleId, Department node, List<CycleOrgSnap> snaps) {
//
//        // 构建 CycleOrgSnap 对象
//        CycleOrgSnap snap = node.buildCycleOrgSnap(companyId, cycleId);
//        snap.initOnNew(UUID.randomUUID().toString());
//        snaps.add(snap);
//
//        // 递归收集子节点的 CycleOrgSnap 对象
//        for (Department child : node.getChildren()) {
//            collectCycleOrgSnaps(companyId, cycleId, child, snaps);
//        }
//    }
//}
