package com.polaris.kpi.report.infr.pojo;


import com.polaris.kpi.eval.domain.task.entity.Cycle;
import com.polaris.kpi.eval.domain.task.entity.empeval.ComputeFinishValueProgress;
import com.polaris.kpi.report.infr.query.ItemAnalysisQuery;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/23 15:07
 */
@Getter
@Setter
@NoArgsConstructor
public class OrgItemYearlyFinishValueTrendPo {

//    private String cycleId;
    private Integer cycleValue;
    private String cycleType;
    private String cycleYear;
    private String itemId;
    private String itemName;
    private List<OrgItemTrendPo> orgItemTrends;


    public OrgItemYearlyFinishValueTrendPo(Integer cycleValue, String cycleType, String cycleYear) {
        this.cycleValue = cycleValue;
        this.cycleType = cycleType;
        this.cycleYear = cycleYear;
    }

    public void accCycleInfo(Cycle cycleInThisYear, Integer cycleValue) {
//        this.cycleId = cycleInThisYear.getId();
        this.cycleValue = cycleValue;
        this.cycleType = cycleInThisYear.getType();
        this.cycleYear = String.valueOf(cycleInThisYear.getYear());
    }

    public void accItemInfo(ItemAnalysisQuery query) {
        this.itemId = query.getItemId();
        this.itemName = query.getItemName();
    }

    public void putInSummary(ComputeFinishValueProgress progress) {

        OrgItemTrendPo summary = new OrgItemTrendPo();
        summary.setOrgName("总体完成情况");
        summary.accProcess(progress);

        for (OrgItemTrendPo orgItemTrend : orgItemTrends) {
            summary.setTargetValue(summary.getTargetValue().add(orgItemTrend.getTargetValue()));
            summary.setFinishValue(summary.getFinishValue().add(orgItemTrend.getFinishValue()));
            summary.accItemInfo(orgItemTrend);
        }

        summary.computeFinishRate();
        summary.removeZero();
        orgItemTrends.add(summary);
    }

}
