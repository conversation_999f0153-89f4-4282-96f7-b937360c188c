package com.polaris.kpi.report.infr.dao;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import com.perf.www.common.utils.date.DateTimeUtils;
import com.perf.www.common.utils.string.StringTool;
import com.polaris.kpi.eval.domain.task.entity.grade.GradeStep;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.eval.infr.statics.dao.PerfEmpStaticsDao;
import com.polaris.kpi.eval.infr.statics.dao.StaticBaseDao;
import com.polaris.kpi.eval.infr.statics.ppojo.*;
import com.polaris.kpi.eval.infr.task.ppojo.*;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.CycleLevelCntPo;
import com.polaris.kpi.eval.infr.task.ppojo.interview.EvalTaskInterviewOperationDo;
import com.polaris.kpi.eval.infr.task.ppojo.report.DirectSubordinatePerformancePo;
import com.polaris.kpi.eval.infr.task.ppojo.report.EvalDataOverViewPo;
import com.polaris.kpi.eval.infr.task.query.report.PerfAnalysisQuery;
import com.polaris.kpi.org.domain.emp.entity.KpiEmployee;
import com.polaris.kpi.org.infr.emp.dao.KpiEmpDao;
import com.polaris.kpi.org.infr.emp.pojo.EmpOrganizationDo;
import com.polaris.kpi.org.infr.emp.pojo.EmpRefOrgDo;
import com.polaris.kpi.org.infr.emp.pojo.EmployeeBaseInfoDo;
import com.polaris.kpi.report.infr.pojo.TaskFilterPo;
import com.polaris.sdk.type.TenantId;
import org.apache.commons.collections.CollectionUtils;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.lufei.ibatis.mapper.PagedList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/7 00:04
 */
@Component
public class AnalysisDao extends StaticBaseDao {

    @Autowired
    private DomainDaoImpl domainDao;
    @Autowired
    private KpiEmpDao kpiEmployeeDao;
    @Autowired
    private PerfEmpStaticsDao perfEmpStaticsDao;


    private void calPercent(List<OrgLevelDistributionPo> list, PerfAnalysisQuery query) {

        for (OrgLevelDistributionPo po : list) {

            po.calHasChild(getChildOrgs(po.getOrgId(),query));

            OrgLevelDistributionPo cnt = getOrgNumByReportQuery(query,po.getOrgId());
            if (Objects.isNull(cnt)) {
                continue;
            }
            po.setAssessNum(cnt.getAssessNum());
            po.setFinishedNum(cnt.getFinishedNum());
            //计算每个等级的人次和占比
            List<LevelCntPercent> cntPercents = getOrgLevelNumByReportQuery(query,po.getOrgId());
            if (CollUtil.isEmpty(cntPercents)) {
                continue;
            }
            po.calcLevelPercent(cntPercents);
            po.calAverageMaxMin(cntPercents);
        }
    }

    private int getChildOrgs(String orgId, PerfAnalysisQuery query){

        ComQB comQB = ComQB.build(EmpOrganizationDo.class,"a")
                .clearSelect()
                .select("direct_child_org_cnt")
                .setRsType(EmpOrganizationDo.class)
                .whereEqReq("company_id", query.getCompanyId())
                .whereEqReq("org_id", orgId);

        EmpOrganizationDo one = domainDao.findOne(comQB);
        return one.getDirectChildOrgCnt();
    }

    private OrgLevelDistributionPo getOrgNumByReportQuery(PerfAnalysisQuery query, String orgId) {

        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "a");
        comQB.clearSelect()
                .select(" a.*")
                .setRsType(OrgLevelDistributionPo.class);
        addBaseCommonConditionNoOrg(query, comQB);

        if (query.getIsManageAnalysis()){
            addManagePrivilegeCondition(query,comQB);
        }

        comQB.appendWhere("INSTR(a.at_org_code_path, '" + orgId + "')");

        ComQB comQB1 = ComQB.build()
                .fromQ(comQB, "aa")
                .clearSelect()
                .select("count(aa.id) as assessNum," +
                        "COUNT( IF ( aa.task_status in "+  StringTool.getInStrSql(query.getAlreadyScored()) +
                        "and aa.final_score is not null, aa.id, NULL ) ) AS finishedNum")
                .setRsType(OrgLevelDistributionPo.class);

        return domainDao.findOne(comQB1);

    }

    private List<LevelCntPercent> getOrgLevelNumByReportQuery(PerfAnalysisQuery query, String orgId) {

        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "a");
        comQB.clearSelect().select("  a.*")
                .setRsType(LevelCntPercent.class);
        addBaseCommonConditionNoOrg(query, comQB);
        if (query.getIsManageAnalysis()){
            addManagePrivilegeCondition(query,comQB);
        }
        comQB.appendWhere("INSTR(a.at_org_code_path, '" + orgId + "')");
        comQB.whereIn("a.task_status",query.getAlreadyScored());
        comQB.whereNotNull("a.final_score");

        ComQB comQB1 = ComQB.build().fromQ(comQB, "aa")
                .clearSelect().select("count(1) as num,aa.cycle_id,aa.evaluation_level as levelName," +
                        "sum(aa.final_score) as totalScore,max(aa.final_score) as maxScore,min(aa.final_score) as minScore")
                .setRsType(LevelCntPercent.class);
        comQB1.groupBy("aa.evaluation_level");
        return domainDao.listAll(comQB1);
    }

    public List<OrgLevelPo> listOrgAnalysisLevels(PerfAnalysisQuery query) {

        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "a");
        comQB.clearSelect().select("a.evaluation_level as levelName");
        comQB.setRsType(OrgLevelPo.class);
        addBaseCommonCondition(query,comQB);

        if (query.getIsManageAnalysis()){
            addManagePrivilegeCondition(query,comQB);
        }

        comQB.whereIn("a.task_status",query.getAlreadyScored());
        comQB.whereNotNull("a.final_score");
        comQB.groupBy("a.evaluation_level");
        return domainDao.listAll(comQB);

    }

    public List<TaskFilterPo> listTasksWithCycleId(String companyId, String cycleId) {

        ComQB taskQB = ComQB.build(PerfEvaluateTaskBaseDo.class,"a")
                .clearSelect().select("a.id as taskId,a.task_name ")
                .setRsType(TaskFilterPo.class)
                .whereEqReq("a.company_id", companyId)
                .whereEqReq("a.cycle_id",cycleId)
                .appendWhere("a.is_deleted = 'false'")
                .orderBy("a.created_time desc");
        return domainDao.listAll(taskQB);

    }

    public PagedList<OrgLevelDistributionPo> pageOrgLevelDistribution(PerfAnalysisQuery query) {

        ComQB comQB1 = ComQB.build(PerfEvaluateTaskUserDo.class, "a");
        comQB1.clearSelect()
                .select(" distinct SUBSTRING_INDEX(SUBSTRING_INDEX(a.at_org_code_path, '|', " + (query.getOrgPathHight() + 2) + "    ),'|',-1 ) AS org_id ,a.`company_id`");
        addBaseCommonCondition(query,comQB1);
        if (query.getIsManageAnalysis()){
            addManagePrivilegeCondition(query,comQB1);
        }

        comQB1.whereIn("a.task_status",query.getAlreadyScored());
        comQB1.whereNotNull("a.final_score");

        ComQB qb = ComQB.build().fromQ(comQB1, "tu")
                .join(EmpOrganizationDo.class, "o").appendOn("o.company_id = tu.company_id AND o.org_id = tu.org_id")
                .clearSelect().select("tu.org_id,o.`org_name`," + query.getOrgPathHight() + " AS orgPathHight")
                .setRsType(OrgLevelDistributionPo.class)
                .whereEq("tu.company_id", query.getCompanyId())
                .appendWhere(" tu.org_id is not null ");

        qb.setPage(query.getPageNo(), query.getPageSize());
        PagedList<OrgLevelDistributionPo> pagedList = domainDao.listPage(qb);
        if (pagedList.isEmpty()) {
            return pagedList;
        }
        //组装计算，人次和占比
        calPercent(pagedList.getData(), query );

        if (query.getPageNo() == 1) {
            //计算汇总人次,仅再第一页显示汇总
            OrgLevelDistributionPo sumPo = getSumOrgLevel(query);
            pagedList.getData().add(0, sumPo);
        }
        return pagedList;
    }

    private OrgLevelDistributionPo getSumOrgLevel(PerfAnalysisQuery query) {

        OrgLevelDistributionPo orgSumNum = getOrgSumNum(query);

        if (Objects.isNull(orgSumNum)) {
            return new OrgLevelDistributionPo();
        }

        orgSumNum.setLevelCntPercents(getOrgSumLevelNum(query));
        orgSumNum.setOrgName("汇总");
        orgSumNum.calSum();
        orgSumNum.calAverageMaxMin(orgSumNum.getLevelCntPercents());
        //计算汇总人次
        return orgSumNum;

    }

    private List<LevelCntPercent> getOrgSumLevelNum(PerfAnalysisQuery query) {

        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "a");
        comQB.clearSelect().select(" a.*")
                .setRsType(LevelCntPercent.class);
        addBaseCommonCondition(query, comQB);
        if (query.getIsManageAnalysis()){
            addManagePrivilegeCondition(query,comQB);
        }
        comQB.appendWhere("a.at_org_path_hight > " + (query.getOrgPathHight()) + "");
        comQB.whereIn("a.task_status",query.getAlreadyScored());
        comQB.whereNotNull("a.final_score");

        ComQB comQB1 = ComQB.build().fromQ(comQB, "aa")
                .clearSelect().select("count(1) as num,aa.cycle_id,aa.evaluation_level as levelName," +
                        "sum(aa.final_score) as totalScore,max(aa.final_score) as maxScore,min(aa.final_score) as minScore")
                .setRsType(LevelCntPercent.class);
        comQB1.groupBy("aa.evaluation_level");
        return domainDao.listAll(comQB1);

    }

    private OrgLevelDistributionPo getOrgSumNum(PerfAnalysisQuery query) {

        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "a");
        comQB.clearSelect()
                .select(" a.*")
                .setRsType(OrgLevelDistributionPo.class);
        addBaseCommonCondition(query, comQB);
        if (query.getIsManageAnalysis()){
            addManagePrivilegeCondition(query,comQB);
        }
        comQB.appendWhere("a.at_org_path_hight > " + (query.getOrgPathHight()) + "");
        ComQB comQB1 = ComQB.build().fromQ(comQB, "aa")
                .clearSelect()
                .select("count(aa.id) as assessNum," +
                        "COUNT( IF ( aa.task_status in "+ StringTool.getInStrSql(query.getAlreadyScored()) +" " +
                        "and aa.final_score is not null, aa.id, NULL ) ) AS finishedNum")
                .setRsType(OrgLevelDistributionPo.class);

        return domainDao.findOne(comQB1);
    }

    public List<OrgTaskStatusPo> listOrgAnalysisStatus(PerfAnalysisQuery query) {

        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "a");
        comQB.clearSelect().select("CASE WHEN a.task_status = '" + TalentStatus.FINISH_VALUE_AUDIT.getStatus() + "' " +
                "THEN '" + TalentStatus.CONFIRMED.getStatus() + "' ELSE a.task_status END as statusName");
        comQB.setRsType(OrgTaskStatusPo.class);
        comQB.whereNotEq("a.task_status", TalentStatus.DRAW_UP_ING.getStatus());
        addBaseCommonCondition(query,comQB);

        if (query.getIsManageAnalysis()){
            addManagePrivilegeCondition(query,comQB);
        }

        comQB.groupBy("CASE WHEN a.task_status = '" + TalentStatus.FINISH_VALUE_AUDIT.getStatus() + "' " +
                "THEN '" + TalentStatus.CONFIRMED.getStatus() + "' ELSE a.task_status END");
        return domainDao.listAll(comQB);
    }

    public PagedList<OrgTaskStatusDistributionPo> pagedOrgStatusDistribution(PerfAnalysisQuery query) {

        ComQB comQB1 = ComQB.build(PerfEvaluateTaskUserDo.class, "a");
        comQB1.clearSelect()
                .select(" distinct SUBSTRING_INDEX(SUBSTRING_INDEX(a.at_org_code_path, '|', " + (query.getOrgPathHight() + 2) + "    ),'|',-1 ) AS org_id ,a.`company_id`");
        addBaseCommonCondition(query,comQB1);
        if (query.getIsManageAnalysis()){
            addManagePrivilegeCondition(query,comQB1);
        }

        ComQB qb = ComQB.build().fromQ(comQB1, "tu")
                .join(EmpOrganizationDo.class, "o").appendOn("o.company_id = tu.company_id AND o.org_id = tu.org_id")
                .clearSelect().select("tu.org_id,o.`org_name`," + query.getOrgPathHight() + " AS orgPathHight")
                .setRsType(OrgTaskStatusDistributionPo.class)
                .whereEq("tu.company_id", query.getCompanyId())
                .appendWhere(" tu.org_id is not null ");

        qb.setPage(query.getPageNo(), query.getPageSize());
        PagedList<OrgTaskStatusDistributionPo> pagedList = domainDao.listPage(qb);
        if (pagedList.isEmpty()) {
            return pagedList;
        }
        //组装计算，人次和占比
        calStatusPercent(pagedList.getData(), query);

        if (query.getPageNo() == 1) {
            //计算汇总人次,仅再第一页显示汇总
            OrgTaskStatusDistributionPo sumPo = getSumOrgStatus(query);
            pagedList.getData().add(0, sumPo);
        }
        return pagedList;
    }

    private OrgTaskStatusDistributionPo getSumOrgStatus(PerfAnalysisQuery query) {

        OrgTaskStatusDistributionPo orgSumNum = getOrgStatusSumNum(query);

        if (Objects.isNull(orgSumNum)) {
            return new OrgTaskStatusDistributionPo();
        }
        orgSumNum.setStatusCntPercents(getOrgPerStatusNum(query));
        orgSumNum.setOrgName("汇总");
        orgSumNum.calSum();

        //统计超期情况
        orgSumNum.setTaskTimeOuts(getSumOrgTaskTimeOuts(query));
        orgSumNum.calTimeOut();

        return orgSumNum;
    }

    private List<TaskTimeOutPo> getSumOrgTaskTimeOuts(PerfAnalysisQuery query) {

        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "a")
                .leftJoin(DeadLineJobDo.class, "er")
                .appendOn("a.id =er.task_user_id  and a.task_id=er.task_id and a.task_status=er.business_type  " +
                        "AND a.company_id =er.company_id  AND er.is_deleted =  'false'");
        comQB.clearSelect()
                .select(" a.id,a.task_status,er.end_date")
                .setRsType(TaskTimeOutPo.class);
        addBaseCommonConditionNoOrg(query, comQB);

        if (query.getIsManageAnalysis()){
            addManagePrivilegeCondition(query,comQB);
        }

        comQB.appendWhere("a.at_org_path_hight > " + query.getOrgPathHight() + "");

        ComQB comQB1 = ComQB.build()
                .fromQ(comQB, "aa")
                .clearSelect().select("aa.id as taskUserId,aa.task_status as status,aa.end_date")
                .setRsType(TaskTimeOutPo.class);

        return domainDao.listAll(comQB1);
    }

    private List<StatusCntPercent> getOrgPerStatusNum(PerfAnalysisQuery query) {

        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "a");
        comQB.clearSelect().select(" a.*")
                .setRsType(StatusCntPercent.class);
        addBaseCommonCondition(query, comQB);
        if (query.getIsManageAnalysis()){
            addManagePrivilegeCondition(query,comQB);
        }
        comQB.appendWhere("a.at_org_path_hight > " + (query.getOrgPathHight()) + "");
        comQB.whereNotEq("a.task_status", TalentStatus.DRAW_UP_ING.getStatus());

        ComQB comQB1 = ComQB.build().fromQ(comQB, "aa")
                .clearSelect().select("count(1) as num,aa.cycle_id," +
                        "CASE WHEN aa.task_status = 'finishValueAudit' THEN 'confirmed' ELSE aa.task_status END as taskStatus")
                .setRsType(StatusCntPercent.class);
        comQB1.groupBy("CASE WHEN aa.task_status = 'finishValueAudit' THEN 'confirmed' ELSE aa.task_status END");
        return domainDao.listAll(comQB1);
    }

    private OrgTaskStatusDistributionPo getOrgStatusSumNum(PerfAnalysisQuery query) {

        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "a");
        comQB.clearSelect()
                .select(" a.*")
                .setRsType(OrgTaskStatusDistributionPo.class);
        addBaseCommonCondition(query, comQB);
        if (query.getIsManageAnalysis()){
            addManagePrivilegeCondition(query,comQB);
        }
        comQB.appendWhere("a.at_org_path_hight > " + (query.getOrgPathHight()) + "");
        ComQB comQB1 = ComQB.build().fromQ(comQB, "aa")
                .clearSelect().select("count(aa.id) as assessNum")
                .setRsType(OrgTaskStatusDistributionPo.class);

        return domainDao.findOne(comQB1);
    }

    private void calStatusPercent(List<OrgTaskStatusDistributionPo> data, PerfAnalysisQuery query) {

        for (OrgTaskStatusDistributionPo po : data) {

            po.calHasChild(getChildOrgs(po.getOrgId(),query));
            OrgTaskStatusDistributionPo cnt = getOrgStatusNumByReportQuery(query,po.getOrgId());
            if (Objects.isNull(cnt)) {
                continue;
            }
            po.setAssessNum(cnt.getAssessNum());
            //计算每个等级的人次和占比
            List<StatusCntPercent> cntPercents = getOrgNumPerStatusByReportQuery(query,po.getOrgId());
            if (CollUtil.isEmpty(cntPercents)) {
                continue;
            }
            po.calcLevelPercent(cntPercents);

            po.setTaskTimeOuts(getTaskTimeOuts(query,po.getOrgId()));
            po.calTimeOut();
        }

    }

    private List<TaskTimeOutPo> getTaskTimeOuts(PerfAnalysisQuery query, String orgId) {

        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "a")
                .leftJoin(DeadLineJobDo.class, "er")
                .appendOn("a.id =er.task_user_id  and a.task_id=er.task_id and a.task_status=er.business_type  " +
                        "AND a.company_id =er.company_id  AND er.is_deleted =  'false'");
        comQB.clearSelect()
                .select(" a.id,a.task_status,er.end_date")
                .setRsType(TaskTimeOutPo.class);
        addBaseCommonConditionNoOrg(query, comQB);

        if (query.getIsManageAnalysis()){
            addManagePrivilegeCondition(query,comQB);
        }

        comQB.appendWhere("INSTR(a.at_org_code_path, '" + orgId + "')");

        ComQB comQB1 = ComQB.build()
                .fromQ(comQB, "aa")
                .clearSelect().select("aa.id as taskUserId,aa.task_status as status,aa.end_date")
                .setRsType(TaskTimeOutPo.class);

        return domainDao.listAll(comQB1);

    }

    /**
     * 计算每个状态的人次和占比
     * @param query
     * @param orgId
     * @return
     */
    private List<StatusCntPercent> getOrgNumPerStatusByReportQuery(PerfAnalysisQuery query, String orgId) {

        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "a");
        comQB.clearSelect().select(" a.*")
                .setRsType(StatusCntPercent.class);
        addBaseCommonConditionNoOrg(query, comQB);
        if (query.getIsManageAnalysis()){
            addManagePrivilegeCondition(query,comQB);
        }
        comQB.appendWhere("INSTR(a.at_org_code_path, '" + orgId + "')");
        comQB.whereNotEq("a.task_status", TalentStatus.DRAW_UP_ING.getStatus());

        ComQB comQB1 = ComQB.build().fromQ(comQB, "aa")
                .clearSelect().select("count(1) as num,aa.cycle_id," +
                        "CASE WHEN aa.task_status = 'finishValueAudit' THEN 'confirmed' ELSE aa.task_status END as taskStatus")
                .setRsType(StatusCntPercent.class);
        comQB1.groupBy("CASE WHEN aa.task_status = 'finishValueAudit' THEN 'confirmed' ELSE aa.task_status END");
        return domainDao.listAll(comQB1);
    }

    /**
     * 计算总人数
     * @param query
     * @param orgId
     * @return
     */
    private OrgTaskStatusDistributionPo getOrgStatusNumByReportQuery(PerfAnalysisQuery query, String orgId) {

        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "a");
        comQB.clearSelect()
                .select(" a.*")
                .setRsType(OrgTaskStatusDistributionPo.class);
        addBaseCommonConditionNoOrg(query, comQB);

        if (query.getIsManageAnalysis()){
            addManagePrivilegeCondition(query,comQB);
        }

        comQB.appendWhere("INSTR(a.at_org_code_path, '" + orgId + "')");

        ComQB comQB1 = ComQB.build()
                .fromQ(comQB, "aa")
                .clearSelect().select("count(aa.id) as assessNum")
                .setRsType(OrgTaskStatusDistributionPo.class);

        return domainDao.findOne(comQB1);
    }

    public TaskTimeOutNumPo queryTaskTimeOutNum(PerfAnalysisQuery query) {

        //超时是判断当前阶段是否超时

        TaskTimeOutNumPo taskTimeOutNumPo = new TaskTimeOutNumPo();

        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "a")
                .leftJoin(DeadLineJobDo.class, "er")
                .appendOn("a.id =er.task_user_id  and a.task_id=er.task_id and a.task_status=er.business_type  " +
                        "AND a.company_id =er.company_id  AND er.is_deleted = 'false'");
        comQB.clearSelect()
                .select(" a.id,a.task_status,er.end_date")
                .setRsType(TaskTimeOutPo.class);
        addBaseCommonCondition(query, comQB);

        if (query.getIsManageAnalysis()){
            addManagePrivilegeCondition(query,comQB);
        }

        ComQB comQB1 = ComQB.build()
                .fromQ(comQB, "aa")
                .clearSelect().select("aa.id as taskUserId,aa.task_status as status,aa.end_date")
                .setRsType(TaskTimeOutPo.class);

        List<TaskTimeOutPo> taskTimeOuts = domainDao.listAll(comQB1);

        taskTimeOutNumPo.setTaskTimeOuts(taskTimeOuts);
        taskTimeOutNumPo.calTimeOut();

        return taskTimeOutNumPo;
    }

    public EvalDataOverViewPo queryEvalDataOverView(PerfAnalysisQuery query) {

        EvalDataOverViewPo dataOverView = new EvalDataOverViewPo();
        //统计所有人数和已完成人数
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class,"a");
        comQB.clearSelect()
                .select(" a.*");
        addBaseCommonCondition(query, comQB);

        if (query.getIsManageAnalysis()){
            addManagePrivilegeCondition(query,comQB);
        }

        List<PerfEvaluateTaskUserDo> taskUserDoList = domainDao.listAllDomain(comQB,PerfEvaluateTaskUserDo.class);
        dataOverView.builderCnt(taskUserDoList);
        if (CollUtil.isEmpty(taskUserDoList)) {
            return dataOverView;
        }
        List<String> taskUserIds = taskUserDoList.stream().map(PerfEvaluateTaskUserDo::getId).collect(Collectors.toList());
        //绩效面谈人次
        ComQB coachCnt = ComQB.build(EvalTaskInterviewOperationDo.class, "io")
                .join(PerfEvaluateTaskUserDo.class, "u")
                .appendOn(" io.company_id = u.company_id and io.task_user_id = u.id ")
                .clearSelect().select(" count(distinct u.id) ")
                .setRsType(Integer.class)
                .whereEqReq("u.company_id", query.getCompanyId())
                .whereInReq("u.id", taskUserIds)
                .whereEq("u.is_deleted", "false");
        dataOverView.setCoachCnt(domainDao.findOne(coachCnt));

        //统计申诉人数
        ComQB appealCnt = ComQB.build(PerfEvaluateTaskAppealInfoDo.class, "io")
                .join(PerfEvaluateTaskUserDo.class, "u")
                .appendOn(" io.company_id = u.company_id and io.task_id = u.task_id and io.emp_id = u.emp_id ")
                .clearSelect().select(" count(distinct u.id) ")
                .setRsType(Integer.class)
                .whereEqReq("u.company_id", query.getCompanyId())
                .whereInReq("u.id", taskUserIds)
                .whereEq("u.is_deleted", "false")
                .whereEq("io.is_deleted", "false");

        dataOverView.setResultAppealCnt(domainDao.findOne(appealCnt));
        return dataOverView;
    }

    public PagedList<DirectSubordinatePerformancePo> pagedDirectSubordinatePerformance(PerfAnalysisQuery query) {

        KpiEmployee employee = kpiEmployeeDao.findEmployee(new TenantId(query.getCompanyId()), query.getOpEmpId());
        //找到自己的dingUserId
        String dingUserId = employee.getDingUserId();
        //收集emp_id去重
        //查询到各种意义下的直属下级进行遍历
        ComQB qb = ComQB.build(EmployeeBaseInfoDo.class, "a")
                .clearSelect().select("employee_id")
                .setRsType(String.class)
                .whereEqReq("company_id", query.getCompanyId())
                .whereEq("ding_manager_id", dingUserId);
        List<String> empIds = domainDao.listAll(qb);

        //查询当前人员所在的orgId
        ComQB orgIdQB = ComQB.build(EmpRefOrgDo.class, "a")
                .clearSelect().select("org_id")
                .setRsType(String.class)
                .whereEqReq("company_id", query.getCompanyId())
                .whereEqReq("emp_id", query.getOpEmpId())
                .whereEqReq("ref_type", "manager");
        List<String> manageOrgIds = domainDao.listAll(orgIdQB);

        if (CollUtil.isNotEmpty(manageOrgIds)){
            //查询部门下直属的员工
            ComQB empIdQB = ComQB.build(EmpRefOrgDo.class, "a")
                    .clearSelect().select("emp_id")
                    .setRsType(String.class)
                    .whereEqReq("company_id", query.getCompanyId())
                    .whereInReq("org_id", manageOrgIds)
                    .whereEqReq("ref_type", "org")
                    .appendWhere("emp_id not in ( select emp_id from emp_ref_org where company_id = '"+query.getCompanyId()
                            +"' and org_id in "+ StringTool.getInStrSql(manageOrgIds) +" and ref_type = 'manager' )");

            List<String> subEmpIds = domainDao.listAll(empIdQB);

            //两个list去重
            empIds.addAll(subEmpIds);
            empIds = empIds.stream().distinct().collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(empIds)){ //无直接管理人员
            return new PagedList<>();
        }

        ComQB result = ComQB.build(EmployeeBaseInfoDo.class, "a")
                .leftJoin(EmpRefOrgDo.class, "o")
                .appendOn("a.company_id = o.company_id and a.employee_id = o.emp_id")
                .leftJoin(EmpOrganizationDo.class, "o1")
                .appendOn("o.company_id = o1.company_id and o.org_id = o1.org_id")
                .clearSelect().select("a.company_id,a.employee_id as empId,a.name as empName, concat(o1.org_name) as orgName")
                .setRsType(DirectSubordinatePerformancePo.class)
                .whereEqReq("a.company_id", query.getCompanyId())
                .whereIn("a.employee_id", empIds)
                .groupBy("a.employee_id");
        result.setPage(query.getPageNo(), query.getPageSize());

        return domainDao.listPage(result);
    }

    public PagedList<SinglePerfRankStaticsPo> pagedSinglePerfRank(PerfAnalysisQuery qry) {

        ComQB query = ComQB.build(PerfEvaluateTaskUserDo.class, "a");
        query.join(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("a.task_id=b.id and b.is_deleted='false'");
        query.join(EmployeeBaseInfoDo.class,"e")
                .appendOn("a.emp_id = e.employee_id and a.company_id = e.company_id");
        query.clearSelect().select(" a.*,b.task_name,CASE WHEN e.is_resigned != 0 THEN 1 ELSE 0 END as leaved ")
                .setRsType(SinglePerfRankStaticsPo.class);
        addBaseCommonCondition(qry, query);
        query.whereIn("a.task_status",qry.getAlreadyScored());
        query.whereNotNull("a.final_score");

        ComQB rank = ComQB.build()
                .clearSelect()
                .select(" @rank := 0, @last_score := NULL, @last_rank := 0 ");
        //排名统计
        ComQB comQB = ComQB.build()
                .fromQ(query, "view").joinQ(rank, "r").appendOn(" 1 = 1 ")
                .clearSelect()
                .select("view.at_org_name_path, view.id as taskUserId,view.emp_name,view.emp_id,view.avatar,view.final_score,view.evaluation_level,view.perf_coefficient,view.emp_org_name,view.task_name, @rank := @rank + 1," +
                        "@last_rank := CASE WHEN @last_score = view.final_score THEN @last_rank WHEN @last_score := view.final_score THEN @rank END AS `rank` ");

        ComQB orderQb = ComQB.build().fromQ(comQB, "t")
                .clearSelect()
                .select("t.at_org_name_path, t.taskUserId,t.emp_name,t.emp_id,t.avatar,t.final_score,t.evaluation_level,t.perf_coefficient," +
                        "t.emp_org_name as orgName,t.task_name ,t.`rank` ")
                .setRsType(SinglePerfRankStaticsPo.class);

        //根据指定字段排序排序、默认根据排名排序
        qry.sort(orderQb);

        orderQb.setPage(qry.getPageNo(), qry.getPageSize());
        return domainDao.listPage(orderQb);

    }

    public PagedList<MultiplePerfRankStaticsPo> pagedMultiplePerfRank(PerfAnalysisQuery qry) {

        String string = StringTool.getInStr(qry.getAlreadyScored()).toString();
        //周期分析列表数据
        ComQB query = ComQB.build(PerfEvaluateTaskUserDo.class, "a")
                .join(EmployeeBaseInfoDo.class,"e")
                .appendOn("a.emp_id = e.employee_id and a.company_id = e.company_id")
                .clearSelect().select(" a.emp_name,a.avatar,a.emp_id,a.at_org_code_path,a.at_org_name_path," +
                        " a.emp_org_id as org_id,a.emp_org_name as org_name," +
                        " COUNT( a.id ) AS totalTask," +
                        "COUNT( IF ( a.task_status in "+ string +", a.id, NULL ) ) AS finishedTask," +
                        " SUM( IF ( a.task_status in "+ string +", a.final_score, 0 ) )  AS totalScore,CASE WHEN e.is_resigned != 0 THEN 1 ELSE 0 END as leaved ")

                .whereNotEq("a.task_status", "drawUpIng")
                .groupBy("a.emp_id,a.at_org_code_path")
                .orderBy(" totalScore desc ");

        addBaseCommonCondition(qry, query);
        if (qry.getIsManageAnalysis()){
            addManagePrivilegeCondition(qry, query);
        }

        ComQB rank = ComQB.build()
                .clearSelect()
                .select(" @rank := 0, @last_score := NULL, @last_rank := 0 ");
        //周期分析列表排名统计
        ComQB comQB = ComQB.build()
                .fromQ(query, "view").joinQ(rank, "r").appendOn(" 1 = 1 ")
                .clearSelect().select(" view.emp_name,view.avatar,view.emp_id,view.org_id,view.at_org_code_path,view.at_org_name_path,view.org_name," +
                        "view.totalTask,view.finishedTask,view.totalScore, @rank := @rank + 1," +
                        "@last_rank := CASE WHEN @last_score = view.totalScore THEN @last_rank WHEN @last_score := view.totalScore THEN @rank END AS `rank` ");

        ComQB orderQb = ComQB.build().fromQ(comQB, "t")
                .clearSelect().select(" t.emp_name,t.avatar,t.emp_id,t.org_id,t.at_org_code_path,t.at_org_name_path,t.org_name," +
                        "t.totalTask as assessTaskCount,t.finishedTask as finishTaskCount ,t.totalScore,t.`rank` ")
                .setRsType(MultiplePerfRankStaticsPo.class);

        //根据指定字段排序排序、默认根据排名排序
        qry.sortByTotalScore(orderQb);

        orderQb.setPage(qry.getPageNo(), qry.getPageSize());
        return domainDao.listPage(orderQb);
    }

    public PagedList<OrgAnalysisLevelExcelPo> pagedExportOrgLevelDistribution(PerfAnalysisQuery query, List<OrgLevelPo> stepPos) {

        PagedList<OrgAnalysisLevelExcelPo> excelPos = new PagedList<>();
        excelPos.setPageNo(query.getPageNo());
        excelPos.setPageSize(query.getPageSize());
        //查询数据
        PagedList<OrgLevelDistributionPo> pageds = pageOrgLevelDistribution(query);
        if (pageds.isEmpty()) {
            return excelPos;
        }
        //组装数据
        OrgAnalysisLevelExcelPo excelPo = new OrgAnalysisLevelExcelPo();
        excelPos.addAll(excelPo.buildData(pageds.getData(), query.getOrgLevelShowType(), stepPos));
        excelPos.setTotalPage(pageds.getTotalPage());
        excelPos.setTotalRow(pageds.getTotalRow());
        return excelPos;

    }

    /**
     * 统计高低绩效
     * @param query
     * @return
     */
    public HighLowLevelAnalysisPo queryHighLowEmpCountsV1(PerfAnalysisQuery query) {

        HighLowLevelAnalysisPo analysisPo = new HighLowLevelAnalysisPo();
        //获得当前周期下的考核等级组
        Map<String, Set<String>> highLowLevels = perfEmpStaticsDao.queryHighLowLevels(query.getCompanyId(),query.getCycleId());
        //获取当前范围内的高低绩效数据
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "a");
        comQB.clearSelect().select("count(*)")
                .setRsType(Integer.class);
        addBaseCommonCondition(query, comQB);
        if (query.getIsManageAnalysis()){
            addManagePrivilegeCondition(query,comQB);
        }
        comQB.whereIn("a.task_status",query.getAlreadyScored());
        comQB.whereNotNull("a.final_score");
        comQB.whereIn("a.evaluation_level",highLowLevels.get("highLevels"));
        Integer highCount = domainDao.findOne(comQB);
        analysisPo.setHighCount(highCount);
        if (highCount > 0){
            ComQB comQB1 = ComQB.build(PerfEvaluateTaskUserDo.class, "a");
            comQB1.clearSelect().select("emp_name,avatar,final_score,evaluation_level,emp_org_name as orgName,perf_coefficient")
                    .setRsType(EmpPerf.class);
            addBaseCommonCondition(query, comQB1);
            if (query.getIsManageAnalysis()){
                addManagePrivilegeCondition(query,comQB1);
            }
            comQB1.whereIn("a.task_status",query.getAlreadyScored());
            comQB1.whereNotNull("a.final_score");
            comQB1.whereIn("a.evaluation_level",highLowLevels.get("highLevels"));
            comQB1.orderBy("a.final_score desc");
            comQB1.limit(0,6);
            analysisPo.setHighLevelEmps(domainDao.listAllDomain(comQB1, EmpPerf.class));
        }

        //获取当前范围内的高低绩效数据
        ComQB comQB2 = ComQB.build(PerfEvaluateTaskUserDo.class, "a");
        comQB2.clearSelect().select("count(*)")
                .setRsType(Integer.class);
        addBaseCommonCondition(query, comQB2);
        if (query.getIsManageAnalysis()){
            addManagePrivilegeCondition(query,comQB2);
        }
        comQB2.whereIn("a.task_status",query.getAlreadyScored());
        comQB2.whereNotNull("a.final_score");
        comQB2.whereIn("a.evaluation_level",highLowLevels.get("lowLevels"));
        Integer lowCount = domainDao.findOne(comQB2);
        analysisPo.setLowCount(lowCount);
        if (lowCount > 0){
            ComQB comQB3 = ComQB.build(PerfEvaluateTaskUserDo.class, "a");
            comQB3.clearSelect().select("emp_name,avatar,final_score,evaluation_level,emp_org_name as orgName,perf_coefficient")
                    .setRsType(EmpPerf.class);
            addBaseCommonCondition(query, comQB3);
            if (query.getIsManageAnalysis()){
                addManagePrivilegeCondition(query,comQB3);
            }
            comQB3.whereIn("a.task_status",query.getAlreadyScored());
            comQB3.whereNotNull("a.final_score");
            comQB3.whereIn("a.evaluation_level",highLowLevels.get("lowLevels"));
            comQB3.orderBy("a.final_score asc");
            comQB3.limit(0,6);
            analysisPo.setLowLevelEmps(domainDao.listAllDomain(comQB3, EmpPerf.class));
        }

        return analysisPo;
    }

    public PagedList<PerfEmpStaticsPo> pagedHighLowLevelV1(PerfAnalysisQuery query) {

        //获得当前周期下的考核等级组
        Map<String, Set<String>> highLowLevels = perfEmpStaticsDao.queryHighLowLevels(query.getCompanyId(),query.getCycleId());
        Set<String> levelNames;
        Integer levelHeight = 0;
        if (query.getHighLowPerf() == 1){
            levelHeight = 1;
            levelNames = highLowLevels.get("highLevels");
        }else {
            levelHeight = 2;
            levelNames = highLowLevels.get("lowLevels");
        }

        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "a");
        comQB.join(PerfEvaluateTaskBaseDo.class, "b").appendOn("a.company_id=b.company_id and a.task_id=b.id and b.is_deleted='false'");
        comQB.clearSelect().select("a.*,a.emp_org_name as orgName,b.task_name," + levelHeight + " as levelHeight")
                .setRsType(PerfEmpStaticsPo.class);
        addBaseCommonCondition(query, comQB);
        if (query.getIsManageAnalysis()){
            addManagePrivilegeCondition(query,comQB);
        }
        comQB.whereIn("a.task_status",query.getAlreadyScored());
        comQB.whereNotNull("a.final_score");
        comQB.whereIn("a.evaluation_level",levelNames);
        if (levelHeight == 1){
            comQB.orderBy("a.final_score desc");
        }else {
            comQB.orderBy("a.final_score asc");
        }
        comQB.setPage(query.getPageNo(),query.getPageSize());
        return domainDao.listPage(comQB);

    }

    public void deleteCrossLevel(String companyId, String crossLevelId) {

        UpdateBuilder delete = UpdateBuilder.build(CrossLevelEmpStaticsDo.class)
                .set("is_deleted", "true")
                .set("updated_time", DateTimeUtils.now2StrDateTime())
                .whereEqReq("id", crossLevelId)
                .whereEqReq("company_id", companyId);
        domainDao.update(delete);

    }

    public List<CycleLevelCntPo> calcLevelDistribution(PerfAnalysisQuery query,List<GradeStep> steps) {

        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "a");
        comQB.clearSelect().select("a.*");
        addBaseCommonCondition(query,comQB);

        if (query.getIsManageAnalysis()){
            addManagePrivilegeCondition(query,comQB);
        }

        comQB.whereIn("a.task_status",query.getAlreadyScored());
        comQB.whereNotNull("a.final_score");
        List<PerfEvaluateTaskUserDo> filterList = domainDao.listAll(comQB);

        if (CollUtil.isEmpty(filterList)) {
            return new ArrayList<>();
        }

        BigDecimal size = new BigDecimal(filterList.size());

//        //获得已排序的等级组
//        ComQB getGradeNames = ComQB.build(RankRuleScoreRangeSnapDo.class, "rrs")
//                .join(ResultRankInstanceDo.class, "rri")
//                .appendOn(" rrs.snap_id = rri.id ")
//                .clearSelect().select(" Distinct(step_name) ")
//                .setRsType(String.class)
//                .whereEqReq("rri.cycle_id", query.getCycleId())
//                .whereEqReq("rrs.is_deleted", "false")
//                .orderByDesc("rrs.max");
//        List<String> gradeNames = domainDao.listAll(getGradeNames);

        List<CycleLevelCntPo> gradeCntPos = new ArrayList<>();
        Map<String, List<PerfEvaluateTaskUserDo>> userMap = CollStreamUtil.groupByKey(CollUtil.filterNew(filterList, user -> Objects.nonNull(user.getStepId())), PerfEvaluateTaskUserDo::getStepId);
        userMap.forEach((k,v) ->{
            CycleLevelCntPo cntPo = new CycleLevelCntPo();
            BigDecimal empCnt = new BigDecimal(v.size());
            cntPo.setEmpCnt(empCnt);
            cntPo.setStepId(k);
            cntPo.builderGradeName(v);
            cntPo.calcPercent(empCnt,size);
            cntPo.calcSort(steps); //计算排序
            gradeCntPos.add(cntPo);
        });
        gradeCntPos.sort(Comparator.comparing(CycleLevelCntPo::getSort));
        return gradeCntPos;
    }
}
