//package com.polaris.kpi.eval.infr.statics.dao;
//
//import cn.hutool.core.collection.CollStreamUtil;
//import cn.hutool.core.collection.CollUtil;
//import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskUserDo;
//import com.polaris.kpi.eval.infr.task.ppojo.admintask.CycleLevelCntPo;
//import com.polaris.kpi.eval.infr.task.ppojo.calibrated.RankRuleScoreRangeSnapDo;
//import com.polaris.kpi.eval.infr.task.ppojo.calibrated.ResultRankInstanceDo;
//import com.polaris.kpi.eval.infr.task.query.report.PerfAnalysisQuery;
//import org.lufei.ibatis.builder.ComQB;
//import org.lufei.ibatis.dao.DomainDaoImpl;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.util.*;
//
///**
// * <AUTHOR>
// * @date 2025/2/4 10:43
// */
//@Component
//public class LevelDistributionStaticsDao extends StaticBaseDao{
//
//    @Resource
//    private DomainDaoImpl autoBaseDao;
//
//    public void setDomainDao(DomainDaoImpl domainDao) {
//        this.autoBaseDao = domainDao;
//    }
//
//    public List<CycleLevelCntPo> calcLevelDistribution(PerfAnalysisQuery query) {
//
//        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "a");
//        comQB.clearSelect().select("a.*");
//        addBaseCommonCondition(query,comQB);
//
//        if (query.getIsManageAnalysis()){
//            addManagePrivilegeCondition(query,comQB);
//        }
//
//        comQB.whereIn("a.task_status",query.getAlreadyScored());
//        comQB.whereNotNull("a.final_score");
//        List<PerfEvaluateTaskUserDo> filterList = autoBaseDao.listAll(comQB);
//
//        if (CollUtil.isEmpty(filterList)) {
//            return new ArrayList<>();
//        }
//
//        BigDecimal size = new BigDecimal(filterList.size());
//
//        //获得已排序的等级组
//        ComQB getGradeNames = ComQB.build(RankRuleScoreRangeSnapDo.class, "rrs")
//                .join(ResultRankInstanceDo.class, "rri")
//                .appendOn(" rrs.snap_id = rri.id ")
//                .clearSelect().select(" Distinct(step_name) ")
//                .setRsType(String.class)
//                .whereEqReq("rri.cycle_id", query.getCycleId())
//                .whereEqReq("rrs.is_deleted", "false")
//                .orderByDesc("rrs.max");
//        List<String> gradeNames = autoBaseDao.listAll(getGradeNames);
//
//        List<CycleLevelCntPo> gradeCntPos = new ArrayList<>();
//        Map<String, List<PerfEvaluateTaskUserDo>> userMap = CollStreamUtil.groupByKey(CollUtil.filterNew(filterList, user -> Objects.nonNull(user.getStepId())), PerfEvaluateTaskUserDo::getStepId);
//        userMap.forEach((k,v) ->{
//            CycleLevelCntPo cntPo = new CycleLevelCntPo();
//            BigDecimal empCnt = new BigDecimal(v.size());
//            cntPo.setEmpCnt(empCnt);
//            cntPo.setStepId(k);
//            cntPo.builderGradeName(v);
//            cntPo.calcPercent(empCnt,size);
//            cntPo.calcSort(gradeNames); //计算排序
//            gradeCntPos.add(cntPo);
//        });
//
//        return gradeCntPos;
//    }
//}
