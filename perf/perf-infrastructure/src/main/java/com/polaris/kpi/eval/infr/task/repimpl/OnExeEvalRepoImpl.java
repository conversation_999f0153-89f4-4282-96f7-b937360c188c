package com.polaris.kpi.eval.infr.task.repimpl;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.eval.KpiItemUsedField;
import cn.com.polaris.kpi.eval.KpiTypeUsedField;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.empeval.*;
import com.polaris.kpi.eval.domain.task.entity.log.ItemDynamicLog;
import com.polaris.kpi.eval.domain.task.ext.IOkrAclSvc;
import com.polaris.kpi.eval.domain.task.repo.OnExeEvalRepo;
import com.polaris.kpi.eval.infr.task.builder.Audit2TypeRuleBd;
import com.polaris.kpi.eval.infr.task.builder.Audit3TypeRuleBd;
import com.polaris.kpi.eval.infr.task.builder.TmpEvalConf2EmpEvalRuleBd;
import com.polaris.kpi.eval.infr.task.dao.BatchScoreEvalMergeDao;
import com.polaris.kpi.eval.infr.task.ppojo.*;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import org.lufei.ibatis.builder.BatchUpdateBuilder;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.common.data.ToDataBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class OnExeEvalRepoImpl implements OnExeEvalRepo {
    private final String taskFileSeq = "task_file";
    @Autowired
    private DomainDaoImpl domainDao;
    @Autowired
    private BatchScoreEvalMergeDao batchDao;
    @Autowired
    private IOkrAclSvc evalOkrAcl;

    @Override
    public ListWrap<EvalOnExeStage> listOnExeEval(TenantId companyId, List<String> taskUserIds) {
        ListWrap<EvalUser> evalUserMap = batchDao.mapBaseTaskUser(companyId, taskUserIds);
        ListWrap<EmpEvalMerge> evalRuleMap = batchDao.mapBaseEvalMerge(companyId, taskUserIds);
        ListWrap<BaseScoreResult> totalRsGroups = batchDao.listTotalLevelRs(companyId, taskUserIds);
        List<String> taskIds = CollUtil.map(evalUserMap.getDatas(), evalUser -> evalUser.getTaskId(), true);
        ListWrap<AdminTask> adminTaskMap = batchDao.mapAdminTaskMerge(companyId, taskIds);
        ListWrap<EmpEvalKpiType> typeGroups = batchDao.listEvalKpiType(companyId, taskUserIds);
        ListWrap<EvalKpi> kpiItemGroups = batchDao.listEmpEvalKpiItem(companyId, taskUserIds);
        ListWrap<PerfEvalTypeResult> typeScoreRsGroups = batchDao.listTypeScoreRs(companyId, taskUserIds);
        ListWrap<EvalScoreResult> itemScoreRsGroups = batchDao.listItemScoreRs(companyId, taskUserIds);
        ListWrap<EvalItemScoreRule> itemRuleGroups = batchDao.listEmpEvalItemRule(companyId, taskUserIds);
        ListWrap<EvalAudit> typeAuditGroups = batchDao.listTypeAudit(companyId, taskUserIds);
        ListWrap<EvalAudit> scoreAuditGroups = batchDao.listScoreAudit(companyId, taskUserIds);
        ListWrap<EvalItemScoreRule> preTypeRuleGroups = batchDao.listPreTypeRule(companyId, taskUserIds);
        ListWrap<ItemDynamicLog> itemLogGroup = batchDao.listItemDynamic(companyId, taskUserIds);
//        ListWrap<SubmitScoreCacheDo> cacheGroups = batchDao.mapScoreCache(companyId, taskUserIds, opEmpId);
        ListWrap<OkrKRPo> okrRefGroups = batchDao.listOkrKRs(companyId, taskUserIds);
        //只有一个公司使用这个,后面加个配置来控制
        ListWrap<KpiTypeUsedField> typeFieldGroups = batchDao.listTypeUsedField(companyId, taskUserIds);
        ListWrap<KpiItemUsedField> itemFieldGroups = batchDao.listItemUsedField(companyId, taskUserIds);

        //各种临时id收集区
        Set<String> scorerEmpIds = new HashSet<>();
        Set<String> itemIds = new HashSet<>();
        List<EvalOnExeStage> rs = new ArrayList<>();
        for (EvalUser user : evalUserMap.getDatas()) {
            AdminTask task = adminTaskMap.mapGet(user.getTaskId());
            EmpEvalMerge evalRule = evalRuleMap.mapGet(user.getId());
            rs.add(new EvalOnExeStage(user, evalRule, task));
            List<EmpEvalKpiType> kpiTypes = typeGroups.groupGet(user.getId());
            KpiListWrap kpiTypesWrap = new KpiListWrap(kpiTypes);
            //指标
            ListWrap<EvalKpi> itemWrap = new ListWrap<>(kpiItemGroups.groupGet(user.getId())).groupBy(EvalKpi::getKpiTypeId);
            List<EvalAudit> typeAudits = typeAuditGroups.groupGet(user.getId());
            //兼容taskUser中的指标信息,后面排查一下使用情况.
            user.setKpis(itemWrap.getDatas());
            user.setKpiTypes(kpiTypes);

            List<PerfEvalTypeResult> typeResultOfUser = typeScoreRsGroups.groupGet(user.getId());
            ListWrap<PerfEvalTypeResult> typeScoreRs = new ListWrap<>(typeResultOfUser).groupBy(evalScoreResult -> evalScoreResult.getKpiTypeId());

            List<EvalScoreResult> empScoreRs = itemScoreRsGroups.groupGet(user.getId());
            ListWrap<EvalScoreResult> itemScoreGroup = new ListWrap<>(empScoreRs).groupBy(result -> result.getKpiItemId());
            List<EvalItemScoreRule> preTypeRules = preTypeRuleGroups.groupGet(user.getId());
            List<OkrKRPo> okrKRPos = okrRefGroups.groupGet(user.getId());
            ListWrap<OkrKRPo> itemOkrMap = new ListWrap<>(okrKRPos).asMap(OkrKRPo::getKpiItemId);

            ListWrap<EvalItemScoreRule> ruleWrap = new ListWrap<>(itemRuleGroups.groupGet(user.getId())).asMap(EvalItemScoreRule::getKpiItemId);
            List<EvalAudit> audits = scoreAuditGroups.groupGet(user.getId());
            ListWrap<ItemDynamicLog> itemLogs = new ListWrap<>(itemLogGroup.groupGet(user.getId())).groupBy(log -> log.getKpiItemId());
            ListWrap<KpiTypeUsedField> typeFields = new ListWrap<>(typeFieldGroups.groupGet(user.getId())).groupBy(field -> field.getKpiTypeId());
            List<KpiItemUsedField> kpiItemFields = itemFieldGroups.groupGet(user.getId());

            if (Objects.isNull(evalRule)) {//V1的数据
                evalRule = task.buildAsEvalRule(user.getId());//转主阶段的配置
                TmpEvalConf2EmpEvalRuleBd builder = new TmpEvalConf2EmpEvalRuleBd(task.getTmpEvalFlow(), task.getId(), user.getEmpId(), companyId);
                builder.buildTo(evalRule); //转化评分流程配置
                builder.merge4V1(kpiTypesWrap, evalRule.isCustom(), empScoreRs, audits);
                evalRuleMap.mapData().put(user.getId(), evalRule);
            } else {
                evalRule.fromTaskConf(task.getTaskName(), task.getId(), task.getScoreConf(), task.getScoreSortConf());
            }


            Audit3TypeRuleBd typeRuleBd = new Audit3TypeRuleBd(kpiTypes);
            ListWrap<EvalItemScoreRule> typeRules = typeRuleBd.build2();
            ListWrap<EvalItemScoreRule> preTypeRues = !typeRules.isEmpty() ? typeRules : new ListWrap<>(preTypeRules).asMap(EvalItemScoreRule::getKpiTypeId);
//            Audit2TypeRuleBd typeRuleBd = new Audit2TypeRuleBd(typeAudits);
//            ListWrap<EvalItemScoreRule> preTypeRues = !typeAudits.isEmpty() ? typeRuleBd.build() : new ListWrap<>(preTypeRules).asMap(tRule -> tRule.getKpiTypeId());
            for (EmpEvalKpiType type : kpiTypes) {
                type.setKpiTypeUsedFields(typeFields.groupGet(type.getKpiTypeId()));
                type.acceptTypeRs(typeScoreRs.groupGet(type.getKpiTypeId()));
                type.setItems(itemWrap.groupGet(type.getKpiTypeId()));
                type.initKpiItemUsedFields(kpiItemFields);
                //指标规则
                for (EvalKpi item : itemWrap.getDatas()) {
                    item.setItemScoreRule(ruleWrap.mapGet(item.getKpiItemId()));
                    List<EvalScoreResult> results = itemScoreGroup.groupGet(item.getKpiItemId());
                    item.setWaitScoresOld(results);
                    List<ItemDynamicLog> logs = itemLogs.groupGet(item.getKpiItemId());
                    item.setInputChangeRecord(logs);
                    itemIds.add(item.getKpiItemId());

                    OkrKRPo okrKRPo = itemOkrMap.mapGet(item.getKpiItemId());
                    if (okrKRPo != null) {
                        item.setTarget(okrKRPo.getTargetId(), okrKRPo.getTargetName());
                        item.setActionId(okrKRPo.getActionId());
                    }
                }
                type.setTypeRule(preTypeRues.mapGet(type.getKpiTypeId()));
            }
            evalRule.setTotalLevelResults(totalRsGroups.groupGet(user.getId()));
            evalRule.extendsRaterRule(kpiTypesWrap);
            //自定义满分分值 如果为null 则使用系统提供分值
//            if (evalRule.getScoreValueConf().getCustomFullScore() == null) {
//                evalRule.getScoreValueConf().setCustomFullScore(fullScoreValue);
//            }
            evalRule.initScoreChain();
//            evalRule.setCompanyConf(conf);
            evalOkrAcl.loadOkrIf(companyId, evalRule.okrItems(), user.getEmpId());
            scorerEmpIds.addAll(evalRule.getKpiTypes().scoreIds());
        }
        //二次查询,组装员工名字信息
        ListWrap<KpiEmp> kpiEmpMap = batchDao.mapKpiEmp(companyId, scorerEmpIds);
        Map<String, List<String>> itemTagNameMap = batchDao.listItemTagName(companyId, itemIds);
        for (EvalUser user : evalUserMap.getDatas()) {
            List<EvalScoreResult> empScoreRs = itemScoreRsGroups.groupGet(user.getId());
            for (EvalScoreResult scoreResult : empScoreRs) {
                if (StrUtil.isEmpty(scoreResult.getScorerId())) {
                    continue;
                }
                KpiEmp kpiEmp = kpiEmpMap.mapGet(scoreResult.getScorerId());
                if (kpiEmp == null) {
                    continue;
                }
                scoreResult.setScorerName(kpiEmp.getEmpName());
            }
            List<EmpEvalKpiType> kpiTypes = typeGroups.groupGet(user.getId());
            for (EmpEvalKpiType kpiType : kpiTypes) {
                for (EvalKpi item : kpiType.getItems()) {
                    List<String> tagNames = itemTagNameMap.get(item.getKpiItemId());
                    item.setItemTags(tagNames);
                }
            }
        }
        return new ListWrap<>(rs).asMap(evalAtTask -> evalAtTask.getEvalUser().getId());
    }

    @Override
    public void batchSaveSubmitedFinishValue(List<EvalOnExeStage> onExeStageEvals, String opEmpId) {
        EvalUser first = onExeStageEvals.get(0).getEvalUser();
        List<OperationLogDo> logDoList = new ArrayList<>();

        List<PerfEvaluateTaskFileDo> fileDataList = new ArrayList<>();

        BatchUpdateBuilder upInputStatus = BatchUpdateBuilder.buildTable("perf_evaluate_task_user")
                .addSetCaseProp("inputFinishStatus", "id:=")
                .whereEq("companyId", first.getCompanyId().getId())
                .whereUseIn("id");

        BatchUpdateBuilder upKpi = BatchUpdateBuilder.buildTable("perf_evaluate_task_kpi")
                .addSetCaseProp("itemFinishValue", "taskUserId:=", "id:=")
                .addSetCaseProp("workItemFinishValue", "taskUserId:=", "id:=")
                .addSetCaseProp("itemFinishValueText", "taskUserId:=", "id:=")
                .addSetCaseProp("finalSubmitFinishValue", "taskUserId:=", "id:=")
                .whereEq("companyId", first.getCompanyId().getId())
                .whereUseIn("id");

        BatchUpdateBuilder upFile = BatchUpdateBuilder.buildTable("perf_evaluate_task_file")
                .addSetCaseProp("isDeleted", "taskUserId:=", "kpiItemId:=")
                .whereEq("company_id", first.getCompanyId().getId())
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereUseIn("taskUserId");

        BatchUpdateBuilder clearCache = BatchUpdateBuilder.buildTable("input_finish_val_cache")
                .addSetCaseProp("isDeleted", "taskUserId:=", "kpiItemId:=")
                .whereEq("company_id", first.getCompanyId().getId())
                .whereEq("operate_emp_id", opEmpId)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereUseIn("taskUserId");

        for (EvalOnExeStage onExeStageEval : onExeStageEvals) {
            EvalUser evalUser = onExeStageEval.getEvalUser();
            upInputStatus.addBean(evalUser);

            //记录日志 SUBMIT_FINISH_VALUE
            List<OperationLogDo> logDos = Convert.toList(OperationLogDo.class, onExeStageEval.getOpLogs());
            logDoList.addAll(logDos);
            List<EvalKpi> updatedKpis = onExeStageEval.getUpdatedKpis();
            for (EvalKpi evalKpi : updatedKpis) {
                PerfEvaluateTaskKpiDo data = new ToDataBuilder<>(evalKpi, PerfEvaluateTaskKpiDo.class).data();
                upKpi.addBean(data);
                clearCache.addBean(data);
                PerfEvaluateTaskFileDo fileDo = new ToDataBuilder<>(evalKpi, PerfEvaluateTaskFileDo.class).data();
                fileDo.setIsDeleted(Boolean.TRUE.toString());
                upFile.addBean(fileDo);
            }
            if (CollUtil.isEmpty(onExeStageEval.getFiles())) {
                continue;
            }
            List<PerfEvaluateTaskFileDo> fileDos = Convert.toList(PerfEvaluateTaskFileDo.class, onExeStageEval.getFiles());
            fileDos.forEach(fileDo -> fileDo.setId(domainDao.nextLongAsStr(taskFileSeq)));
            fileDataList.addAll(fileDos);
        }
        domainDao.updateBatch(upInputStatus);
        domainDao.updateBatch(upKpi);
        domainDao.saveBatch(logDoList); //插入日志
        domainDao.updateBatch(upFile);
        domainDao.saveBatch(fileDataList);//插入文件
        domainDao.updateBatch(clearCache);  //更新后清除缓存(如果是单个更新，不能清除全部暂存)
    }

    @Override
    public void updateInputNotifyJobStatus(String companyId, List<EvalOnExeStage> onExeStages) {
        List<String> userIds = onExeStages.stream().map(evalOnExeStage -> evalOnExeStage.getEvalUser().getId()).collect(Collectors.toList());
        if (CollUtil.isEmpty(userIds)) {
            return;
        }
        UpdateBuilder up = UpdateBuilder.build(InputNotifyJobConfDo.class)
                .set("execute_status", 1)
                .whereEqReq("company_id", companyId)
                .whereInReq("task_user_id", userIds)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.update(up);
    }

    @Override
    public void saveKpiItemAutoScore(List<EvalOnExeStage> recomutedKpiScores) {
        for (EvalOnExeStage eval : recomutedKpiScores) {
            List<EvalKpi> items = eval.getEvalUser().autoItems();
            List<PerfEvaluateTaskKpiDo> datas = items.stream()
                    .map(taskKpi -> new ToDataBuilder<>(taskKpi, PerfEvaluateTaskKpiDo.class).data())
                    .collect(Collectors.toList());
            for (PerfEvaluateTaskKpiDo data : datas) {
                domainDao.update(data);
            }
        }
    }
}
