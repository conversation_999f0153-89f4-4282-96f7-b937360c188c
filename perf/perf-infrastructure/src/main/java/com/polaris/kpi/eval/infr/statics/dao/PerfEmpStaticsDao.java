package com.polaris.kpi.eval.infr.statics.dao;

import cn.hutool.core.collection.CollUtil;
import com.polaris.kpi.eval.infr.statics.ppojo.*;
import com.polaris.kpi.eval.infr.task.ppojo.calibrated.RankRuleScoreRangeSnapDo;
import com.polaris.kpi.eval.infr.task.ppojo.calibrated.ResultRankInstanceDo;
import com.polaris.kpi.eval.infr.task.query.report.PerfAnalysisQuery;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.lufei.ibatis.mapper.PagedList;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/1/20 14:38
 */
@Component
public class PerfEmpStaticsDao extends StaticBaseDao{

    @Resource
    private DomainDaoImpl autoBaseDao;

    public void setDomainDao(DomainDaoImpl domainDao) {
        this.autoBaseDao = domainDao;
    }

    public Map<String, Set<String>> queryHighLowLevels(String companyId,String cycleId) {
        Map<String, Set<String>> resultMap = new HashMap<>();
        Set<String> highLevels = new HashSet<>();
        Set<String> lowLevels = new HashSet<>();

        // 1. 查询周期内所有等级组 snapIds（保留原始查询逻辑）
        List<String> snapIds = autoBaseDao.listAll(
                ComQB.build(ResultRankInstanceDo.class)
                        .clearSelect()
                        .select("id")
                        .setRsType(String.class)
                        .whereEqReq("company_id", companyId)
                        .whereEqReq("cycle_id", cycleId)
                        .whereEqReq("is_deleted", "false")
                        .orderByDesc("id")
        );

        if (CollUtil.isEmpty(snapIds)) {
            resultMap.put("highLevels", highLevels);
            resultMap.put("lowLevels", lowLevels);
            return resultMap;
        }

        // 2. 一次性查询所有 snapIds 的等级数据（合并 N 次查询为 1 次）
        List<RankRuleScoreRangeSnapDo> dbResults = autoBaseDao.listAll(
                ComQB.build(RankRuleScoreRangeSnapDo.class)
                        .clearSelect()
                        .select("snap_id", "step_name")  // 同时查询 snap_id 和 step_name
                        .setRsType(RankRuleScoreRangeSnapDo.class)             // 返回 Object[] 格式
                        .whereIn("snap_id", snapIds)        // IN 查询所有 snapIds
                        .whereEqReq("company_id",companyId)
                        .whereEqReq("is_deleted", "false")
                        .orderByAsc("snap_id")              // 先按 snap_id 分组排序
                        .orderByDesc("max")                 // 再按 max 降序保证组内顺序
        );

        // 3. 内存中分组处理（替代多次数据库查询）
        String currentSnapId = null;
        List<String> currentGroup = new ArrayList<>();

        for (RankRuleScoreRangeSnapDo snapDo : dbResults) {
            String snapId = snapDo.getSnapId();
            String stepName = snapDo.getStepName();

            // 遇到新 snapId 时处理上一个分组
            if (!snapId.equals(currentSnapId)) {
                processGroup(currentGroup, highLevels, lowLevels);
                currentSnapId = snapId;
                currentGroup = new ArrayList<>();
            }
            currentGroup.add(stepName);
        }

        // 处理最后一个分组
        processGroup(currentGroup, highLevels, lowLevels);

        // 4. 构建返回结果
        resultMap.put("highLevels", highLevels);
        resultMap.put("lowLevels", lowLevels);
        return resultMap;
    }

    // 辅助方法：处理单个 snapId 的分组数据
    private void processGroup(List<String> group, Set<String> highLevels, Set<String> lowLevels) {
        if (group.size() < 2){
            return;
        }
        if (CollUtil.isNotEmpty(group)) {
            highLevels.add(group.get(0));                  // 最高等级（max 排序后首元素）
            lowLevels.add(group.get(group.size() - 1));    // 最低等级（末元素）
        }
    }

//    public HighLowLevelAnalysisPo queryHighLowEmpCount(PerfAnalysisQuery query) {
//
//        ComQB comQB = ComQB.build(PerfEmpStaticsDo.class, "a");
//        comQB.clearSelect().select("SUM(CASE WHEN a.level_height = 1 THEN 1 ELSE 0 END) AS highCount," +
//                        "SUM(CASE WHEN a.level_height = 2 THEN 1 ELSE 0 END) AS lowCount")
//                .setRsType(HighLowLevelAnalysisPo.class);
//        addCommonCondition(query, comQB);
//
//        return autoBaseDao.findOne(comQB);
//    }

//    public void queryHighLowEmpList(PerfAnalysisQuery qry, HighLowLevelAnalysisPo analysisPo) {
//
//        if (analysisPo.getHighCount() != 0){
//
//            ComQB comQB = ComQB.build(PerfEmpStaticsDo.class, "a");
//            comQB.clearSelect().select(" a.*")
//                    .setRsType(EmpPerf.class);
//            addCommonCondition(qry,comQB);
//            comQB.whereEqReq("a.level_height",1);
//            comQB.orderBy(" a.final_score desc ");
//            comQB.limit(0,8);
//            List<EmpPerf> highEmpPerfs = autoBaseDao.listAll(comQB);
//            analysisPo.setHighLevelEmps(highEmpPerfs);
//        }else {
//            analysisPo.setHighLevelEmps(new ArrayList<>());
//        }
//
//        if (analysisPo.getLowCount() != 0){
//            ComQB comQB2 = ComQB.build(PerfEmpStaticsDo.class, "a");
//            comQB2.clearSelect().select(" a.*")
//                    .setRsType(EmpPerf.class);
//            addCommonCondition(qry,comQB2);
//            comQB2.whereEqReq("a.level_height",2);
//            comQB2.orderBy(" a.final_score desc ");
//            comQB2.limit(0,8);
//            List<EmpPerf> lowEmpPerfs = autoBaseDao.listAll(comQB2);
//            analysisPo.setLowLevelEmps(lowEmpPerfs);
//        }else {
//            analysisPo.setLowLevelEmps(new ArrayList<>());
//        }
//
//    }

//    public HighLowLevelAnalysisPo queryHighLowEmpCounts(PerfAnalysisQuery query) {
//
//        HighLowLevelAnalysisPo analysisPo = queryHighLowEmpCount(query);
//        if (Objects.nonNull(analysisPo)) {
//            queryHighLowEmpList(query, analysisPo);
//        }
//        return analysisPo;
//    }

//    public PagedList<PerfEmpStaticsPo> pagedHighLowLevel(PerfAnalysisQuery qry) {
//
//        ComQB comQB = ComQB.build(PerfEmpStaticsDo.class, "a");
//        comQB.clearSelect().select(" a.*")
//                .setRsType(PerfEmpStaticsPo.class);
//        addCommonCondition(qry, comQB);
//        comQB.whereEqReq("a.level_height", qry.getHighLowPerf());
//        comQB.orderBy("a.final_score desc ");
//        comQB.setPage(qry.getPageNo(), qry.getPageSize());
//        return autoBaseDao.listPage(comQB);
//    }

}
