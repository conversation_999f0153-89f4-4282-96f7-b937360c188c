package com.polaris.kpi.setting.dao;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskBaseDo;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskUserDo;
import com.polaris.kpi.setting.domain.entity.*;
import com.polaris.kpi.setting.ppojo.ResultAuditFlowInstanceDo;
import com.polaris.kpi.setting.ppojo.ResultAuditFlowNodeDo;
import com.polaris.kpi.setting.ppojo.ResultAuditFlowNodeRaterDo;
import com.polaris.kpi.setting.ppojo.ResultAuditFlowUserDo;
import lombok.extern.slf4j.Slf4j;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ResultAuditFlowDao {
    @Autowired
    private DomainDaoImpl domainDao;

    public boolean existFlow(String taskId,String md5Key) {
        ComQB qb = ComQB.build(ResultAuditFlowInstanceDo.class)
                .whereEqReq("task_id", taskId)
                .whereEqReq("md5_key",md5Key)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        ResultAuditFlowInstanceDo instanceDo = domainDao.findOne(qb);
        if (Objects.nonNull(instanceDo)) {
            return true;
        }
        return false;
    }

}
