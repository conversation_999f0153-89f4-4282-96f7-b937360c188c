package com.polaris.kpi.eval.infr.statics.ppojo;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/6 13:48
 */
@Getter
@Setter
@NoArgsConstructor
public class OrgLevelPo {

    private String levelName;//等级名称；
    private Integer sort;//排序

    public void calcSort(List<String> levelNames){
        this.sort = levelNames.indexOf(this.levelName);
    }

}
