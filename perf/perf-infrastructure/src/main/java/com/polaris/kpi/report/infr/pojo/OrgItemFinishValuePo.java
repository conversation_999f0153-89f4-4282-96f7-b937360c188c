package com.polaris.kpi.report.infr.pojo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/12 21:52
 */
@Data
public class OrgItemFinishValuePo {

    private String orgId;
    private String orgName;
    private Integer orgPathHeight;
    private Integer directChildOrgCnt;
    private List<ItemTargetAndFinishValuePo> itemTargetAndFinishValues;


//    public OrgItemFinishValuePo(String orgName, Integer orgPathHeight) {
//        this.orgName = orgName;
//        this.orgPathHeight = orgPathHeight;
//    }
}
