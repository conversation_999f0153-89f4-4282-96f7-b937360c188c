package com.polaris.kpi.eval.infr.task.ppojo.admintask;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.com.polaris.kpi.eval.*;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.perf.www.common.constant.BusinessConstant;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.RaterNodeConf;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.ScoreValueConf;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.TypeWeightConf;
import com.polaris.kpi.eval.domain.task.entity.grade.IndLevelGroup;
import com.polaris.kpi.eval.domain.task.entity.grade.LevelRateConf;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrActionUpdate;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrGoal;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.eval.domain.temp.entity.PerfTemplEvaluate;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskItemDynamicPo;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskScoreResultDo;
import com.polaris.sdk.type.ListWrap;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.infr.task.ppojo.admintask
 * @Author: suxiaoqiu
 * @CreateTime: 2025-03-15  16:57
 * @Description: 评分详情
 * @Version: 2.0
 */
@Setter
@Getter
@NoArgsConstructor
@Slf4j
public class EmpEvalTaskScore3Po {

    private Evaluate templEvaluateInfo;
    private ScoreResult scoreResult;
    private String itemScoreFlag;//是否有指定评分
    private String typeWeightSwitch;//类别权重
    private Integer showResultType; //'结果呈现:111=总分,总等级,维度等级'
    private Integer createTotalLevelType; //'绩效总等级生成方式 1 = 自动, 2= 手动'
    private Integer mutualEvalComputeType; //互评评分计算方式（0 平均权重，1-平均分）
    private boolean isOpenAvgWeightCompute; //是否开启平均权重计算
    private LevelRateConf levelRateConf; //规则组

    public EmpEvalTaskScore3Po(String opEmpId, EvalUser taskUser, EmpEvalMerge evalRule, Map<String, KpiEmp> scoreEmpMap, CycleEval taskBase,
                               PerfEvaluateTaskScoreResultDo indexCalibration, BindEvalResult bindEvalResult, ListWrap<PerfEvaluateTaskItemDynamicPo> itemDynamiclistWrap) {
        this.showResultType = evalRule.getShowResultType();
        this.createTotalLevelType = evalRule.getCreateTotalLevelType();
        this.isOpenAvgWeightCompute = taskUser.isOpenAvgWeightCompute();
        this.templEvaluateInfo = Convert.convert(Evaluate.class, taskBase);
        this.templEvaluateInfo.buildRaterMode(evalRule.selfConf(), evalRule.peerConf(), evalRule.subConf(), evalRule.supperConf());
        this.templEvaluateInfo.buildScoreViewRule(taskBase.getTemplEvaluateJson(), taskUser.getTaskStatus(), evalRule);
        this.templEvaluateInfo.setItemEvaluateList(evalRule.getKpiTypes().itemScoreRules());
        this.templEvaluateInfo.setIsAddAuditComment(evalRule.getAuditResult().getCommentReq());
        this.templEvaluateInfo.setAuditFlag(evalRule.getAuditResult().isOpen() ? "true" : "false");
        this.templEvaluateInfo.buildScoreConf(evalRule.getTypeWeightConf(), evalRule.getScoreValueConf());
        this.templEvaluateInfo.buildOpenScorerNode(evalRule.getEvalScorersWrap());
        TypeWeightConf typeWeightConf = evalRule.getTypeWeightConf();
        KpiListWrap kpiTypes = evalRule.getKpiTypes();
        this.itemScoreFlag = kpiTypes.hasAppointNode() + "";
        this.typeWeightSwitch = typeWeightConf.isOpen() + "";
        this.scoreResult = Convert.convert(ScoreResult.class, taskUser);
        this.scoreResult.finalSuperiorScore = taskUser.getV3SuperiorScore();
        this.scoreResult.finalSubScore = taskUser.getV3SubScore();
        this.scoreResult.finalPeerScore = taskUser.getV3PeerScore();
        this.scoreResult.finalSelfScore = taskUser.getV3SelfScore();
        this.scoreResult.finalItemScore = taskUser.getV3FinalItemScore();
        this.scoreResult.finalAppointScore = taskUser.getV3AppointScore();
        this.scoreResult.setScoreTypes(evalRule.listEvalScorerNodeTypes());//页面需要校准的类型?? 这里优化后只有评分的环节，是否影响校准需要验证
        this.scoreResult.setBindEvalResult(bindEvalResult);
        List<KpiTypeScore> kpiTypeScores = new ArrayList<>();
        AtomicBoolean displaySelfScore = new AtomicBoolean(true);
        AtomicBoolean displaySuperiorScore = new AtomicBoolean(true);
        AtomicBoolean displaySubScore = new AtomicBoolean(true);
        AtomicBoolean displayPeerScore = new AtomicBoolean(true);
        for (EmpEvalKpiType type : kpiTypes.getDatas()) {
            KpiTypeScore kpiTypeScore = Convert.convert(KpiTypeScore.class, type);
            kpiTypeScore.setOkrGoals(type.getOkrGoals());
            kpiTypeScore.buildAlreadyNodesV3(type.getWaitScores());//维度的评分流程复用 ，这里不做变更
            List<TaskUserKpiItemScore> kpiItemScores = new ArrayList<>();
            Set<String> typeScorerTypes = new HashSet<>();
            type.orderItem();//根据order 排序 升序
            //根据维度筛选对应的评分节点
            for (EvalKpi item : type.getItems()) {
                TaskUserKpiItemScore convertItem = Convert.convert(TaskUserKpiItemScore.class, item);
                convertItem.setActionId(item.getActionId());
                convertItem.setIndLevelGroup(item.getIndLevelGroup());
                //添加okr评分评语
                convertItem.acceptOkrValue(item);
                convertItemScoreNodeKpi(item, convertItem, typeScorerTypes,displaySelfScore,displaySuperiorScore,displaySubScore,displayPeerScore);//转换接收指标评价人及环节
                setItemScore(kpiTypeScore.getKpiTypeClassify(), item, convertItem, indexCalibration);//转换接收指标各个环节得分
                //接收指标完成值更新记录
                if (!itemDynamiclistWrap.isEmpty()) {
                    List<PerfEvaluateTaskItemDynamicPo> dynamicPos = itemDynamiclistWrap.groupGet(item.getKpiItemId());
                    convertItem.setInputChangeRecord(dynamicPos);
                }
                kpiItemScores.add(convertItem);
            }
            kpiTypeScore.setScoreTypes(typeScorerTypes);
            if (kpiTypeScore.isAskType()) {
                kpiTypeScore.computeAsk360TypeWeight();//360问卷计算权重
            }
            kpiTypeScore.setKpiItemScores(kpiItemScores);
            kpiTypeScores.add(kpiTypeScore);

        }
        this.scoreResult.finalSuperiorScore = displaySuperiorScore.get() ? this.scoreResult.finalSuperiorScore : null;
        this.scoreResult.finalSubScore = displaySubScore.get() ?  this.scoreResult.finalSubScore : null;
        this.scoreResult.finalPeerScore = displayPeerScore.get() ? this.scoreResult.finalPeerScore : null;
        this.scoreResult.finalSelfScore = displaySelfScore.get() ?  this.scoreResult.finalSelfScore : null;
        scoreResult.setKpiTypes(kpiTypeScores);
        convertTotalRs(evalRule);//转换打总等级的
    }

    private boolean valueIsNull(BigDecimal value) {
        return value == null;
    }

    private boolean valueIsZero(BigDecimal value) {
        if (value == null) {
            return false;
        }
        return value.compareTo(BigDecimal.ZERO) == 0;
    }

    private void convertItemScoreNodeKpi(EvalKpi item, TaskUserKpiItemScore convertItem, Set<String> typeScorerTypes,
                                         AtomicBoolean displaySelfScore, AtomicBoolean displaySuperiorScore,AtomicBoolean displaySubScore,
                                         AtomicBoolean displayPeerScore ) {
        Map<String, List<EvalScorerNodeKpiItem>> scoreNodeKpis = new ListWrap<>(item.getWaitScores()).groupBy(EvalScorerNodeKpiItem::getScorerType).getGroups();
        if (MapUtil.isEmpty(scoreNodeKpis)) {
            return;
        }
        ListWrap<EvalScorerNodeKpiItem> waitNodeGroup = new ListWrap<>(item.getWaitScores()).groupBy(EvalScorerNodeKpiItem::getScorerType);
        //评分结果按结点分组
        scoreNodeKpis.forEach((node, results) -> {
            //用于显示的，这里results要做下去重，对复制后评价的数据需要去重,根据scorerId去重
            List<SubmitedScoreResult> submitedScoreResults = new ArrayList<>();
            List<EvalScorerNodeKpiItem>  tempResults = filterRepeat(results);
            tempResults.forEach(scorerNodeKpiItem -> {
                if (!scorerNodeKpiItem.isShow()) {
                    return; //排除不可以展示的kpiItem
                }
                SubmitedScoreResult convertRs = Convert.convert(SubmitedScoreResult.class, scorerNodeKpiItem);
                convertRs.setScorer(scorerNodeKpiItem.getScorerName());
                convertRs.setAvatar(scorerNodeKpiItem.getScorerAvatar());
                convertRs.setVetoFlag(scorerNodeKpiItem.getVetoFlag());
                submitedScoreResults.add(convertRs);
            });
            boolean hasUnpassed = false;
            for (EvalScorerNodeKpiItem rs : waitNodeGroup.groupGet(node)) {
                if (rs.isCanShowNode() && !rs.isPassed()) {
                    hasUnpassed = true;
                    break;
                }
            }
            if (hasUnpassed){
                if (EvaluateAuditSceneEnum.isSelfScore(node)){
                    displaySelfScore.set(false);
                } else if (EvaluateAuditSceneEnum.SUPERIOR_SCORE.getScene().equals(node)){
                    displaySuperiorScore.set(false);
                } else if (EvaluateAuditSceneEnum.isSubScore(node)){
                    displaySubScore.set(false);
                } else if (EvaluateAuditSceneEnum.isPeerScore(node)){
                    displayPeerScore.set(false);
                }
            }
            boolean displayScore = !hasUnpassed;
            //okr导入分数，默认显示
            if (item.openOkrScore() && Boolean.parseBoolean(item.getIsOkr())) {
                displayScore = true;
            }
            typeScorerTypes.add(node);
            BigDecimal noWeightScore = convertItem.getNoWeightScoreByType(node);//指标环节得分
            convertItem.addScoreNode(node, noWeightScore, submitedScoreResults, displayScore);
        });
    }
    private List<EvalScorerNodeKpiItem> filterRepeat(List<EvalScorerNodeKpiItem>  waitScores){
        if (CollUtil.isEmpty(waitScores)) {
            return new ArrayList<>();
        }

        //waitScores  asScorerIdAndOrderKey 去重
        Set<String> uniqueKeys = new HashSet<>();
        return waitScores.stream()
                .filter(e -> uniqueKeys.add(e.asScorerIdAndOrderKey()))
                .collect(Collectors.toList());
    }
    private void convertTotalRs(EmpEvalMerge evalRule) {
        if (evalRule.getEvalScorersWrap().isEmpty()) {
            return;
        }

        EvalScorersWrap wrap = evalRule.getEvalScorersWrap();
        List<EmpEvalScorerNode> scorerNodes = wrap.listTotalScoreNode();
        scorerNodes.forEach(totalRs -> {
            SubmitedScoreResult convertRs = Convert.convert(SubmitedScoreResult.class, totalRs);
            convertRs.setScorer(totalRs.getScorerName());
            convertRs.setAvatar(totalRs.getScorerAvatar());
            scoreResult.addTotal(convertRs);
        });
    }

    private boolean isAuto(String scorerType, int openOkrScore) {
        return BusinessConstant.SCORER_TYPE_AUTO.equals(scorerType) || openOkrScore == 1;
    }

    private boolean isResultAudit(PerfEvaluateTaskScoreResultDo indexCalibration) {
        //判断是否有指标校准分数
        return indexCalibration != null && indexCalibration.getIndexCalibration() != null && indexCalibration.getIndexCalibration().size() > 0;
    }

    private void replaceResultAuditScore(TaskUserKpiItemScore itemScore, PerfEvaluateTaskScoreResultDo indexCalibration) {
        indexCalibration.getIndexCalibration().forEach(obj -> {
            if (itemScore.getKpiItemId().equals(obj.getKpiItemId())) {
                itemScore.setItemFinalScore(obj.getScore());
                itemScore.setIndLevel(obj.getScoreLevel());
            }
        });
    }

    private void replaceResultAuditScoreVetoFlag(TaskUserKpiItemScore itemScore, PerfEvaluateTaskScoreResultDo indexCalibration) {
        indexCalibration.getIndexCalibration().forEach(obj -> {
            if (itemScore.getKpiItemId().equals(obj.getKpiItemId())) {
                itemScore.setItemFinalScore(obj.getScore());
                itemScore.setIndLevel(obj.getScoreLevel());
                itemScore.setVetoFlag(obj.getVetoFlag());
            }
        });
    }

    public boolean typeClassifyIsPlusAndSub(String kpiTypeClassify) {
        return BusinessConstant.KPI_TYPE_CLASSIFY_PLUS_SUB.equals(kpiTypeClassify)
                || BusinessConstant.KPI_TYPE_CLASSIFY_PLUS.equals(kpiTypeClassify)
                || BusinessConstant.KPI_TYPE_CLASSIFY_SUBTRACT.equals(kpiTypeClassify);
    }

    private void setItemScore(String kpiTypeClassify, EvalKpi item, TaskUserKpiItemScore itemScore, PerfEvaluateTaskScoreResultDo indexCalibration) {
        //先看是不是自动打分
        if (isAuto(itemScore.getScorerType(), itemScore.getOpenOkrScore())) {
            itemScore.setAutoScore(item.getItemScore());
            itemScore.setItemFinalScore(item.getItemAutoScore());
            itemScoreHandler(item, itemScore);

            ///**判断是否有指标校准分数*/
            if (isResultAudit(indexCalibration)) {
                replaceResultAuditScore(itemScore, indexCalibration);//替换校准的分数
            }
            return;
        }
        List<ScoreNodeScore> scoreDetails = itemScore.getScoreNodeScores();//scoreNodeScores
        if (CollUtil.isEmpty(scoreDetails)) {
            return;
        }
        itemScore.setItemFinalScore(item.getItemFinalScore());//计算指标得分【等于各个指标的环节分之和】
        itemScore.setSelfScore(item.getItemSelfScore());
        itemScore.setPeerScore(item.getItemPeerScore());
        itemScore.setSubScore(item.getItemSubScore());
        itemScore.setSupScore(item.getItemSuperiorScore());
        itemScore.setAppointScore(item.getItemAppointScore());
        itemScoreHandler(item, itemScore);

        //非加减分类，加分类，减分类
        if (!typeClassifyIsPlusAndSub(kpiTypeClassify)) {
            if (Objects.equals(kpiTypeClassify, "oneVoteVeto")) {
                itemScore.setVetoFlag(getVetoFlag(scoreDetails));
            }
        }
        if (isResultAudit(indexCalibration)) {
            replaceResultAuditScoreVetoFlag(itemScore, indexCalibration);
        }
    }

    private void itemScoreHandler(EvalKpi item, TaskUserKpiItemScore itemScore) {
        if (null != item.getItemItemScore()) {
            itemScore.setItemScore(item.getItemScore());//前端使用的是ItemScore作为定向评分，这里要单独设置下
        } else {
            itemScore.setItemScore(null);
        }
    }

    private String getVetoFlag(List<ScoreNodeScore> scoreDetailList) {
        if (CollectionUtils.isEmpty(scoreDetailList)) {
            return null;
        }
        int vetoFlagIdx = 0;
        for (ScoreNodeScore scoreNodeScore : scoreDetailList) {
            List<SubmitedScoreResult> scoreResultList = scoreNodeScore.getSubmitedRs();
            if (CollUtil.isEmpty(scoreResultList)) {
                continue;
            }
            vetoFlagIdx += scoreResultList.stream().filter(rs -> Objects.equals(rs.getVetoFlag(), Boolean.TRUE.toString())).collect(Collectors.toList()).size();
        }
        if (vetoFlagIdx > 0) {
            return Boolean.TRUE.toString();
        }
        return Boolean.FALSE.toString();
    }

    @Setter
    @Getter
    @NoArgsConstructor
    public static class Evaluate {

        private String templBaseId;//模板基础id
        private String scoreStartRuleType;//评分开始时间规则类型（周期结束前/后）
        private Integer scoreStartRuleDay;//评分开始时间规则值
        private String selfScoreFlag;//是否自评
        private String selfScoreRule;//自评评分规则（打总分/指标打分）
        private String selfScoreViewRule;//被考核人查看评分规则JSON
        private BigDecimal selfScoreWeight;//自评权重
        private String mutualScoreFlag;//旧的互评标识字段，2.0后使用peer和sub分开同级和下级：同级互评开关
        private String mutualScoreRule;//旧的互评标识字段，2.0后使用peer和sub分开同级和下级：同级互评规则（打总分/指标打分）
        private BigDecimal peerScoreWeight;//同级互评权重
        private String mutualScoreAttendRule;//互评参与规则
        private String mutualScoreAnonymous;//互评人姓名是否匿名
        private String mutualScoreVacancy;//互评人空缺时规则
        private String mutualScoreViewRule;//互评人查看评分规则JSON
        private String peerScoreFlag;//同级互评开关
        private String peerScoreRule;//同级互评规则（打总分/指标打分）
        private String subScoreFlag;//下级互评开关
        private BigDecimal subScoreWeight;//下级互评权重
        private String subScoreRule;//下级互评规则（打总分/指标打分）
        private String superiorScoreFlag;//是否上级评
        private String superiorScoreOrder;//上级评顺序类型（同时/依次）
        private String superiorScoreViewRule;//上级评人查看评分规则JSON
        private BigDecimal superiorScoreWeight;//上级评权重
        private String superiorScoreRule;//上级评分规则（打总分=total /指标打分=item）
        private String superiorScoreVacancy;//上级评分人空缺时规则

        private String itemScoreFlag;//是否有定向评分
        private String appointScoreFlag;//是否有指定评分
        private String auditFlag;//结果是否审批
        private int scoreSummarySwitch;//评分总结开关 0非必填/1必填/-1关闭 默认=-1
        private String commentFlag;//评语填写类型（选填/必填/按评分值）/required(必填)/notRequired/condition（按条件）/close(关闭)
        private int plusOrSubComment;//加减分评语填设置=1(必填)/0(不必填)/-1关闭
        private Integer commentRequiredValue;//评语按评分值规则值, 低于某个比例
        private Integer commentRequiredHighValue;//评语按评分值规则值, 高于某个比例
        private String enterScoreMethod;//进入评分方式，手动manual，自动auto
        private String evaluateType;//评分流程类型 ： simple-简易流程，360-360°考核流程，custom-自定义流程
        private String appointScoreViewRule;//指定评分查看规则
        private String multipleReviewersType;//多个责任人或签还是会签
        private String duplicateType;//当责任人担任多个评分人时,true表示去重，false表示不去重
        private String roleVacancyType;//指定角色空缺时,skip跳过，taskAdmin转交给考核任务发起人
        private String submitLevelFlag;//评分时是否提交绩效等级，true/false
        private String mutualUserType;//旧版字段 包含同级/下级指定评分人类型 //user=指定人 role=角色 userInvite=指定谁来邀请 roleInvite=由角色来邀请
        private String peerUserType;//同级指定评分人类型
        private String peerUserValue;//同级指定人类型id
        private String subUserType;//下级指定评分人类型
        private String subUserValue;//下级指定人类型id
        private String peerUserName;//同级评指定人姓名
        private String subUserName;//下级评指定人姓名

        private Integer enterScoreEmpType;// 发起评分的人员type=1管理员,type=2考核员工,
        private Integer isAddAuditComment;//是否需要校准备注

        private String superiorScoreJson;
        private String finalAuditJson;
        private String typeWeightSwitch;
        private String typeWeightLimitFlag;
        private String modifyAuditFlag;
        private String itemEvaluateJson;
        private String peerScoreJson;

        private String subScoreJson;

        private String superiorScoreAnonymous;
        private String appointScoreAnonymous;

        /**
         * 指标自定义的流程
         */
        private List<EvalItemScoreRule> itemEvaluateList;
        private ScoreDetailPriv scoreDetailPriv;

        public void buildRaterMode(RaterNodeConf self, RaterNodeConf peer,
                                   RaterNodeConf sub, RaterNodeConf superCnf) {
            this.selfScoreRule = self == null ? null : self.getRateMode();
            this.selfScoreWeight = self == null ? null : self.getNodeWeight();
            this.peerScoreRule = peer == null ? null : peer.getRateMode();
            this.peerScoreWeight = peer == null ? null : peer.getNodeWeight();
            this.subScoreRule = sub == null ? null : sub.getRateMode();
            this.subScoreWeight = sub == null ? null : sub.getNodeWeight();
            this.superiorScoreRule = superCnf == null ? null : superCnf.getRateMode();
            this.superiorScoreWeight = superCnf == null ? null : superCnf.getNodeWeight();
        }

        public void buildOpenScorerNode(EvalScorersWrap evalScorersWrap) {
            if (evalScorersWrap.isEmpty()) {
                return;
            }
            ListWrap<EmpEvalScorerNode> nodeWrap = evalScorersWrap.listSNExcludeTotalLevel();
            this.selfScoreFlag = evalScorersWrap.openScorerNode(SubScoreNodeEnum.SELF_SCORE.getScene(), nodeWrap) + "";
            this.peerScoreFlag = evalScorersWrap.openScorerNode(SubScoreNodeEnum.PEER_SCORE.getScene(), nodeWrap) + "";
            this.subScoreFlag = evalScorersWrap.openScorerNode(SubScoreNodeEnum.SUB_SCORE.getScene(), nodeWrap) + "";
            this.subScoreFlag = evalScorersWrap.openScorerNode(SubScoreNodeEnum.SUPERIOR_SCORE.getScene(), nodeWrap) + "";
            this.appointScoreFlag = evalScorersWrap.openScorerNode(SubScoreNodeEnum.APPOINT_SCORE.getScene(), nodeWrap) + "";
            this.superiorScoreFlag = evalScorersWrap.openScorerNode(SubScoreNodeEnum.SUPERIOR_SCORE.getScene(), nodeWrap) + "";//指定评分
            this.itemScoreFlag = evalScorersWrap.openScorerNode(SubScoreNodeEnum.ITEM_SCORE.getScene(), nodeWrap) + "";//定向评分
        }

        public BigDecimal getNodeWeightByScoreType(String scorerType) {
            if ("self_score".equals(scorerType)) {
                return selfScoreWeight;
            }

            if ("item_score".equals(scorerType)) {
                return BigDecimal.ZERO;
            }
            if ("peer_score".equals(scorerType)) {
                return peerScoreWeight;
            }
            if ("sub_score".equals(scorerType)) {
                return subScoreWeight;
            }
            if ("superior_score".equals(scorerType)) {
                return superiorScoreWeight;
            }
            return null;
        }

        public void buildScoreConf(TypeWeightConf typeWeightConf, ScoreValueConf scoreValueConf) {
            this.typeWeightSwitch = typeWeightConf.isOpen() ? Boolean.TRUE.toString() : Boolean.FALSE.toString();
        }

        public void buildScoreViewRule(PerfTemplEvaluate evaluate, String taskStatus, EmpEvalMerge evalRule) {
            if (Objects.isNull(evaluate)) {
                return;
            }
            this.selfScoreViewRule = evaluate.getSelfScoreViewRule();
            this.mutualScoreViewRule = evaluate.getMutualScoreViewRule();
            this.superiorScoreViewRule = evaluate.getSuperiorScoreViewRule();
            this.appointScoreViewRule = evaluate.getAppointScoreViewRule();

            //兼容1.0
            /*if (temTask) {
                if (StrUtil.isNotBlank(evaluate.getSuperiorScoreAnonymous())) {
                    Map map = JSONObject.parseObject(evaluate.getSuperiorScoreAnonymous(), Map.class);
                    this.superiorScoreAnonymous = String.valueOf(map.get("anonymous"));
                }
                if (StrUtil.isNotBlank(evaluate.getAppointScoreAnonymous())) {
                    Map map = JSONObject.parseObject(evaluate.getAppointScoreAnonymous(), Map.class);
                    this.appointScoreAnonymous = String.valueOf(map.get("anonymous"));
                }
                if (StrUtil.isNotBlank(evaluate.getMutualScoreAnonymous())) {
                    Map map = JSONObject.parseObject(evaluate.getMutualScoreAnonymous(), Map.class);
                    this.mutualScoreAnonymous = String.valueOf(map.get("anonymous"));
                }
                return;
            }*/

            this.mutualScoreAnonymous = evaluate.getMutualScoreAnonymous();
            this.superiorScoreAnonymous = evaluate.getSuperiorScoreAnonymous();
            this.appointScoreAnonymous = evaluate.getAppointScoreAnonymous();

            TalentStatus current = TalentStatus.statusOf(taskStatus);
            if (current.beforeEq(TalentStatus.RESULTS_AUDITING)) {
                return;
            }
            if (Objects.equals(taskStatus, TalentStatus.RESULTS_AFFIRMING.getStatus())) {
                this.scoreDetailPriv = evalRule.getConfirmResult().getScoreDetailPriv();
            } else {
                this.scoreDetailPriv = evalRule.getPublishResult().getScoreDetailPriv();
            }
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    public static class ScoreResult {
        private String id;//taskUser.id
        private String empId;
        private String empName;
        private String taskId;
        private BigDecimal finalSelfScore;//最终自评得分
        private BigDecimal finalPeerScore;//最终同级互评得分
        private BigDecimal finalSubScore;//最终下级互评得分
        private BigDecimal finalSuperiorScore;//最终上级评分
        private BigDecimal finalItemScore;//定向指定得分
        private BigDecimal finalAppointScore;//指标得分
        private BigDecimal finalScore;//最终得分
        private String evaluationLevel;//考核等级
        private BigDecimal scoreOfRef;      //关联绩效后的分数
        private BindEvalResult bindEvalResult;      //关联绩效数据
        private Set<String> scoreTypes;
        private BigDecimal finalPlusScore;//最终加分
        private BigDecimal finalSubtractScore;//最终减分
        private BigDecimal finalItemAutoScore;//最终自动评分
        @JSONField(name = "kpiTypeScoreDetailList")
        private List<KpiTypeScore> kpiTypes;//指标类考核得分
        @JSONField(name = "totalScoreDetailList")
        private List<SubmitedScoreResult> totalRs = new ArrayList<>();//总分
        private String perfCoefficient;// 系数

        public void addTotal(SubmitedScoreResult result) {
            totalRs.add(result);
        }

    }

    @Getter
    @Setter
    @NoArgsConstructor
    public static class KpiTypeScore {

        private String kpiTypeId;
        private String kpiTypeName;
        private String kpiTypeClassify;
        private BigDecimal maxExtraScore;
        private String isOkr;
        private Integer typeOrder;
        private BigDecimal kpiTypeWeight;
        private PlusSubInterval plusSubInterval;
        protected String typeLevel;                 //维度的等级
        private List<KpiTypeUsedField> kpiTypeUsedFields;        //使用的字定义字段集合
        //private List<PerfEvalTypeResult> waitScores;
        private String des;        //维度描述 20230921增加
        private List<OkrGoal> okrGoals;        //okr的目标
        private BigDecimal ask360EvalScore;
        private String ask360TempId;   // 360问卷模版ID
        private String ask360TempName;
        private Integer scoringType;   // 计分方式(1:问卷平均分(题目分数之和/题目数)  2:问卷总分(题目分数之和))
        private String ask360EvalId;  //360问卷考核实例ID
        private String ask360TempDesc;      //360问卷描述
        private Set<String> scoreTypes = new HashSet<>();

        @JSONField(serialize = false, deserialize = false)
        public boolean isOkrType() {
            return Boolean.TRUE.toString().equals(isOkr);
        }

        @JSONField(serialize = false)
        public boolean isAskType() {
            return Objects.equals(this.kpiTypeClassify, "ask360");
        }

        @JSONField(name = "kpiItemScoreDetailList")
        private List<TaskUserKpiItemScore> kpiItemScores;

        private List<NodeOfItemScore> alreadyScores = new ArrayList<>();//已提交的评分

        public void buildAlreadyNodes(Map<String, KpiEmp> empMap, List<PerfEvalTypeResult> waitScores) {
            List<PerfEvalTypeResult> alreadys = waitScores.stream().filter(BaseScoreResult::isPassed).collect(Collectors.toList());
            ListWrap<PerfEvalTypeResult> nodeGroup = new ListWrap<>(alreadys).groupBy(BaseTypeScoreResult::getScorerType);
            nodeGroup.getGroups().forEach((node, results1) -> {
                NodeOfItemScore nodeOfItemScore = new NodeOfItemScore(node);
                for (PerfEvalTypeResult srs : results1) {
                    KpiEmp kpiEmp = empMap.get(srs.getScorerId());
                    NodeOfItemScore.ScoreResultOfItem ofItem = new NodeOfItemScore.ScoreResultOfItem(kpiEmp.getEmpName(), kpiEmp.getAvatar(), srs.getScorerId(), Objects.isNull(srs.getUpdatedTime()) ? srs.getCreatedTime() : srs.getUpdatedTime());
                    ofItem.scoreWeight = srs.getScoreWeight();
                    ofItem.acceptComment(srs.getScoreAttUrl(), srs.getScoreComment());
                    ofItem.level = srs.getScoreLevel();
                    ofItem.auditStatus = srs.getAuditStatus();
                    //加减分项分数统计和正常指标得分不是一个字段里面
                    nodeOfItemScore.addItem(ofItem);
                }
                this.alreadyScores.add(nodeOfItemScore);
            });
        }

        public void buildAlreadyNodesV3(List<EvalScorerNodeKpiType> waitScores) {
            List<EvalScorerNodeKpiType> alreadys = waitScores.stream().filter(EvalScorerNodeKpiType::isPassed).collect(Collectors.toList());
            ListWrap<EvalScorerNodeKpiType> nodeGroup = new ListWrap<>(alreadys).groupBy(EvalScorerNodeKpiType::getScorerType);
            nodeGroup.getGroups().forEach((node, results1) -> {
                //用于显示的，这里results要做下去重，对复制后评价的数据需要去重,根据scorerId去重
                NodeOfItemScore nodeOfItemScore = new NodeOfItemScore(node);
                List<EvalScorerNodeKpiType>  tempResults = filterRepeat(results1);
                //排除不可以展示的kpiItem
                //加减分项分数统计和正常指标得分不是一个字段里面
                tempResults.stream().filter(EvalScorerNodeScoreItemBase::isShow).forEachOrdered(srs -> {
                    NodeOfItemScore.ScoreResultOfItem ofItem = new NodeOfItemScore.ScoreResultOfItem(srs.getScorerName(), srs.getScorerAvatar(), srs.getScorerId(), Objects.isNull(srs.getUpdatedTime()) ? srs.getCreatedTime() : srs.getUpdatedTime());
                    ofItem.scoreWeight = srs.getScoreWeight();
                    ofItem.acceptComment(srs.getScoreAttUrl(), srs.getScoreComment());
                    ofItem.level = srs.getScoreLevel();
                    ofItem.auditStatus = srs.getAuditStatus();
                    nodeOfItemScore.addItem(ofItem);
                });
                this.alreadyScores.add(nodeOfItemScore);
            });
        }
        private List<EvalScorerNodeKpiType> filterRepeat(List<EvalScorerNodeKpiType>  waitScores){
            if (CollUtil.isEmpty(waitScores)) {
                return new ArrayList<>();
            }

            //waitScores  asScorerIdAndOrderKey 去重
            Set<String> uniqueKeys = new HashSet<>();
            return waitScores.stream()
                    .filter(e -> uniqueKeys.add(e.asScorerIdAndOrderKey()))
                    .collect(Collectors.toList());
        }
        @JSONField(serialize = false)
        public boolean isZero(BigDecimal value) {
            return (BigDecimal.ZERO).compareTo(value) == 0;
        }

        public void computeAsk360TypeWeight() {
            if (Objects.isNull(this.ask360EvalScore)) {
                return;
            }
            if (isZero(this.ask360EvalScore)) {
                return;
            }
            this.ask360EvalScore = this.ask360EvalScore.multiply(this.kpiTypeWeight.divide(Pecent.ONE_HUNDRED)).setScale(2, BigDecimal.ROUND_HALF_UP);
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    public static class TaskUserKpiItemScore {
        private String actionId;
        private String kpiItemId;
        private String kpiItemName;
        private Integer finishValueType;
        private Integer showFinishBar;
        private String indLevel;// 指标的等级
        private IndLevelGroup indLevelGroup;
        //不同环节的评分列表
        @JSONField(name = "scorerTypeScoreDetailList")
        private List<ScoreNodeScore> scoreNodeScores = new ArrayList<>();
        private List<PerfEvaluateTaskItemDynamicPo> inputChangeRecord;

        public void addScoreNode(String scorerType, List<SubmitedScoreResult> submitedRs) {
            scoreNodeScores.add(new ScoreNodeScore(scorerType, submitedRs));
        }

        public void addScoreNode(String scorerType, BigDecimal scorerNodeScore, List<EmpEvalTaskScore3Po.SubmitedScoreResult> submitedRs,boolean displayScore) {
            scoreNodeScores.add(new EmpEvalTaskScore3Po.ScoreNodeScore(scorerType, scorerNodeScore, submitedRs,displayScore));
        }


        private String itemRule;

        private String scoringRule;

        private BigDecimal plusLimit;

        private BigDecimal subtractLimit;

        private Integer order;

        private BigDecimal itemWeight;

        private BigDecimal itemFullScore;

        private String itemScoreValue;

        private BigDecimal selfScore;

        private BigDecimal peerScore;

        private BigDecimal subScore;

        private BigDecimal supScore;

        private BigDecimal appointScore;

        private BigDecimal autoScore;

        private String autoScoreExFlag;//自动算分异常标识

        private BigDecimal itemScore;//定向得分

        private BigDecimal PlusOrSubtractScore;

        private BigDecimal itemFinalScore;
        /**
         * 自动算分计算公式
         */
        private String itemFormula;
        /**
         * 公式配置的详情
         */
        private String thresholdJson;
        /**
         * 目标值
         */
        private BigDecimal itemTargetValue;
        /**
         * 完成值
         */
        private BigDecimal itemFinishValue;

        private String itemUnit;
        private String resultInputType;

        private String scorerType;

        private String itemFieldJson;

        private String workItemFinishValue;

        private String inputFormat;

        private String itemFinishValueText;

        private List<String> itemTags;

        private String itemFullScoreCfg;

        private int openOkrScore;

        private PlusSubInterval plusSubInterval;

        private List<ItemCustomFieldValue> fieldValueList;      //指标自定义字段

        private Integer formulaType;

        private String itemTargetValueText; //指标项文本类型目标值

        private String vetoFlag;            //否决标识(true为否决，false为不否决)

        private String actionValueSet; //kr
        private BigDecimal krItemFinishValue;
        private BigDecimal krFinishValue;
        private BigDecimal okrScore;
        private BigDecimal krSelfScore;
        private String krSelfContent;
        private BigDecimal krSuperiorScore;
        private String krSuperiorContent;
        private List<OkrActionUpdate> okrActionUpdates;        //okr更新记录对象集合


        private BigDecimal itemItemScore;//指标定向评评分 未乘以指标权重
        private BigDecimal itemSelfScore;//指标自评得分（不带指标权重）
        private BigDecimal itemPeerScore;//指标同级互评得分（不带指标权重）
        private BigDecimal itemSubScore;//指标下级互评得分（不带指标权重）
        private BigDecimal itemSuperiorScore;//指标上级得分（不带指标权重）
        private BigDecimal itemAppointScore;//指标指定评（不带指标权重）

        public void acceptOkrValue(EvalKpi item) {
            this.okrScore = item.getOkrScore();
            this.krItemFinishValue = item.getFinishValue();
            this.krFinishValue = item.getFinishValue();
            this.actionValueSet = item.getActionValueSet();
            this.krSelfScore = item.getKrSelfScore();
            this.krSelfContent = item.getKrSelfContent();
            this.krSuperiorScore = item.getKrSuperiorScore();
            this.krSuperiorContent = item.getKrSuperiorContent();
            this.okrActionUpdates = item.getOkrActionUpdates();
        }

        private boolean isNotNullScore(BigDecimal score) {
            return null != score && BigDecimal.ZERO.compareTo(score) != 0;
        }

        public BigDecimal getNoWeightScoreByType(String scorerType) {
            if ("self_score".equals(scorerType)) {
                return itemSelfScore;
            }
            if ("appoint_score".equals(scorerType)) {
                return itemAppointScore;
            }
            if ("item_score".equals(scorerType)) {
                return itemItemScore;
            }
            if ("peer_score".equals(scorerType)) {
                return itemPeerScore;
            }
            if ("sub_score".equals(scorerType)) {
                return itemSubScore;
            }
            if ("superior_score".equals(scorerType)) {
                return itemSuperiorScore;
            }
            return null;
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    public static class ScoreNodeScore {

        private String scorerType;//node
        private BigDecimal score;//环节分
        private String scoreLevel;//环节等级
        private String vetoFlag;//是否一票否决
        @JSONField(name = "scoreResultList")
        private List<SubmitedScoreResult> submitedRs;
        private boolean displayScore;       //是否展示分数

        public ScoreNodeScore(String scorerType, List<SubmitedScoreResult> submitedRs) {
            this.scorerType = scorerType;
            this.submitedRs = submitedRs;
        }

        public ScoreNodeScore(String scorerType, BigDecimal score, List<SubmitedScoreResult> submitedRs,boolean displayScore) {
            this.scorerType = scorerType;
            this.score = score;
            this.submitedRs = submitedRs;
            this.displayScore = displayScore;
            if (CollUtil.isNotEmpty(submitedRs)) {
                //需要已提交的
                List<SubmitedScoreResult> passedRs = submitedRs.stream().filter(SubmitedScoreResult::havePassed).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(passedRs)) {
                    passedRs.sort(Comparator.comparing(PerfEvaluateTaskScoreResultDo::getApprovalOrder).reversed());
                    EmpEvalTaskScore3Po.SubmitedScoreResult convertRs = passedRs.get(0);
                    this.scoreLevel = convertRs.getScoreLevel();
                    for (SubmitedScoreResult passedR : passedRs) { //获取环节否决，存在否决，则环节结果否决
                        if (StrUtil.isNotBlank(passedR.getVetoFlag())) {
                            this.vetoFlag = passedR.getVetoFlag();
                            continue;
                        }
                        if (Boolean.parseBoolean(passedR.getVetoFlag())) {
                            this.vetoFlag = convertRs.getVetoFlag();
                            break;
                        }
                    }
                }
            }
        }
    }

    //SELECT r.*, e. NAME scorer, e.avatar,
    @Getter
    @Setter
    @NoArgsConstructor
    public static class SubmitedScoreResult extends PerfEvaluateTaskScoreResultDo {
        /**
         * 评分人名称
         */
        private String scorer;
        private String avatar;
        ///**
        // * 评分人状态，见{@link com.perf.www.cons.EmployeeStatus}
        // */
        //private String scorerStatus;

        public boolean hasScore() {
            return super.getScore() != null;
        }

        public boolean havePassed() {
            return BusinessConstant.PASS.equals(this.getAuditStatus());
        }

    }
}