package com.polaris.kpi.eval.infr.statics.dao;

import cn.hutool.core.collection.CollUtil;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.infr.statics.ppojo.*;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskBaseDo;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskUserDo;
import com.polaris.kpi.eval.infr.task.ppojo.calibrated.RankRuleScoreRangeSnapDo;
import com.polaris.kpi.eval.infr.task.ppojo.calibrated.ResultRankInstanceDo;
import com.polaris.kpi.eval.infr.task.query.report.PerfAnalysisQuery;
import com.polaris.kpi.org.infr.emp.pojo.EmployeeBaseInfoDo;
import com.polaris.kpi.report.domain.entity.CrossLevelCount;
import com.polaris.sdk.type.ListWrap;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.lufei.ibatis.mapper.PagedList;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/1/20 14:34
 */
@Component
public class CrossLevelEmpStaticsDao extends StaticBaseDao{

    @Resource
    private DomainDaoImpl autoBaseDao;

    public void setDomainDao(DomainDaoImpl domainDao) {
        this.autoBaseDao = domainDao;
    }

    public Map<String, Set<String>> queryCrossLevels(String companyId, String evaluationLevel, List<String> lastCycles) {
        Map<String, Set<String>> result = new HashMap<>();
        Set<String> upLevels = new HashSet<>();
        Set<String> downLevels = new HashSet<>();
        result.put("up", upLevels);
        result.put("down", downLevels);

        if (CollUtil.isEmpty(lastCycles)) {
            return result;
        }

        // 1. 批量查询所有周期对应的等级组ID（保留原始逻辑）
        List<String> rankInstances = autoBaseDao.listAll(
                ComQB.build(ResultRankInstanceDo.class, "r")
                        .clearSelect()
                        .select("r.id")
                        .setRsType(String.class)
                        .whereIn("r.cycle_id", lastCycles)
                        .whereEqReq("r.company_id", companyId)
                        .whereEqReq("r.is_deleted", "false")
                        .orderByDesc("r.id")
        );

        if (CollUtil.isEmpty(rankInstances)) {
            return result;
        }

        // 2. 批量查询所有等级组的数据（关键优化）
        List<RankRuleScoreRangeSnapDo> dbResults = autoBaseDao.listAll(
                ComQB.build(RankRuleScoreRangeSnapDo.class, "rr")
                        .clearSelect()
                        .select("rr.snap_id", "rr.step_name")  // 同时查询 snap_id 和 step_name
                        .setRsType(RankRuleScoreRangeSnapDo.class)
                        .whereIn("rr.snap_id", rankInstances)  // IN 查询代替循环单查
                        .whereEqReq("rr.company_id", companyId)
                        .whereEqReq("rr.is_deleted", "false")
                        .orderByAsc("rr.snap_id")              // 按等级组排序
                        .orderByDesc("rr.max")                 // 保持原排序逻辑
        );

        // 3. 内存处理分组数据
        processGroupedData(dbResults, evaluationLevel, upLevels, downLevels);

        return result;
    }



    /**
     * 处理分组数据并填充跨级结果
     */
    private void processGroupedData(List<RankRuleScoreRangeSnapDo> dbResults, String evaluationLevel,
                                    Set<String> upLevels, Set<String> downLevels) {
        String currentSnapId = null;
        List<String> currentLevels = new ArrayList<>();

        for (RankRuleScoreRangeSnapDo snapDo : dbResults) {
            String snapId = snapDo.getSnapId();
            String stepName = snapDo.getStepName();

            // 分组处理逻辑
            if (!snapId.equals(currentSnapId)) {
                processSingleGroup(currentLevels, evaluationLevel, upLevels, downLevels);
                currentSnapId = snapId;
                currentLevels = new ArrayList<>();
            }
            currentLevels.add(stepName);
        }

        // 处理最后一个组
        processSingleGroup(currentLevels, evaluationLevel, upLevels, downLevels);
    }

    /**
     * 处理单个等级组的跨级逻辑
     */
    private void processSingleGroup(List<String> levels, String evaluationLevel,
                                    Set<String> upLevels, Set<String> downLevels) {
        if (CollUtil.isEmpty(levels) || !levels.contains(evaluationLevel)) {
            return;
        }

        int index = levels.indexOf(evaluationLevel);

        // 处理跨级提升
        if (index > 1) {
            upLevels.addAll(levels.subList(0, index - 1));  // 优化循环为批量添加
        }

        // 处理跨级下降
        if (levels.size() - index > 2) {
            downLevels.addAll(levels.subList(index + 2, levels.size()));
        }
    }

    public List<EvalUser> queryPrevTaskUserWithLevelCross(EvalUser taskUser, String companyId ,Set<String> levels, List<String> lastCycles) {

        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class,"a")
                .join(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("a.task_id = b.id")
                .clearSelect().select(" a.id,a.emp_name,a.final_score, a.evaluation_level,a.perf_coefficient,b.task_name")
                .setRsType(EvalUser.class)
                .whereEq("a.company_id", companyId)
                .whereEq("a.org_id",taskUser.getOrgId())
                .whereIn("a.evaluation_level", levels)
                .whereEqReq("a.emp_id",taskUser.getEmpId())
                .whereIn("a.cycle_id", lastCycles)
                .whereEqReq("a.is_deleted", "false")
                .whereEqReq("b.is_deleted", "false");

        return autoBaseDao.listAll(comQB);
    }

    public ListWrap<EvalUser> queryGroupedLevelCrossPrevTaskUser(List<String> taskUserIds, String companyId , Set<String> levels, List<String> lastCycles , Integer performanceType) {

        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class,"a")
                .join(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("a.task_id = b.id")
                .clearSelect().select(" a.id,a.emp_id,a.org_id,a.emp_name,a.final_score, a.evaluation_level,a.perf_coefficient,b.task_name")
                .setRsType(EvalUser.class)
                .whereEq("a.company_id", companyId)
                .whereIn("a.evaluation_level", levels)
                .whereIn("a.emp_id",taskUserIds)
                .whereIn("a.cycle_id", lastCycles)
                .whereEqReq("b.performance_type", performanceType)
                .whereEqReq("a.is_deleted", "false")
                .whereEqReq("b.is_deleted", "false");

        List<EvalUser> evalUsers = autoBaseDao.listAll(comQB);
        return new ListWrap<>(evalUsers).groupBy(user -> user.getEmpId() + "_" + user.getOrgId());
    }


    public CrossLevelAnalysisPo queryCrossLevelEmpCounts(PerfAnalysisQuery query) {

        CrossLevelAnalysisPo analysisPo = queryCrossLevelCount(query);
        if (Objects.nonNull(analysisPo)){
            queryCrossLevelList(query,analysisPo);
        }else {
            analysisPo = new CrossLevelAnalysisPo(0,0);
        }
        return analysisPo;
    }

    private CrossLevelAnalysisPo queryCrossLevelCount(PerfAnalysisQuery qry) {

        CrossLevelAnalysisPo one = new CrossLevelAnalysisPo(0,0);
        //查询统计对象
        CrossLevelCount crossLevelCount = getCrossLevelCount(qry.getCompanyId(), qry.getCycleId(), qry.getPerformanceType());
        if (Objects.isNull(crossLevelCount)){
            return null;
        }

        ComQB comQB = ComQB.build(CrossLevelEmpStaticsDo.class, "a");
        comQB.clearSelect().select("SUM(CASE WHEN a.cross_level_type = 1 THEN 1 ELSE 0 END) AS upCount," +
                        "SUM(CASE WHEN a.cross_level_type = 2 THEN 1 ELSE 0 END) AS downCount")
                .setRsType(CrossLevelAnalysisPo.class);
        addCommonCondition(qry,comQB);
        comQB.whereEqReq("a.is_deleted","false");
        CrossLevelAnalysisPo analysisPo= autoBaseDao.findOne(comQB);
        if (Objects.nonNull(analysisPo)){
            one.setUpCount(analysisPo.getUpCount());
            one.setDownCount(analysisPo.getDownCount());
        }
        one.accCrossLevelCount(crossLevelCount);
        return one;
    }

    private void queryCrossLevelList(PerfAnalysisQuery qry, CrossLevelAnalysisPo analysisPo) {

        if (analysisPo.getUpCount() != 0){

            ComQB comQB = ComQB.build(CrossLevelEmpStaticsDo.class, "a");
            comQB.clearSelect().select("a.*")
                    .setRsType(EmpPerf.class);
            addCommonCondition(qry,comQB);
            comQB.whereEqReq("a.cross_level_type",1);
            comQB.orderBy(" a.created_time desc ");
            comQB.whereEqReq("a.is_deleted","false");
            comQB.limit(0,8);

            List<EmpPerf> highEmpPerfs = autoBaseDao.listAll(comQB);
            analysisPo.setUpEmpPerfs(highEmpPerfs);
        }else {
            analysisPo.setUpEmpPerfs(new ArrayList<>());
        }

        if (analysisPo.getDownCount() != 0){
            ComQB comQB2 = ComQB.build(CrossLevelEmpStaticsDo.class, "a");
            comQB2.clearSelect().select(" a.*")
                    .setRsType(EmpPerf.class);
            addCommonCondition(qry,comQB2);
            comQB2.whereEqReq("a.cross_level_type",2);
            comQB2.orderBy(" a.created_time desc ");
            comQB2.whereEqReq("a.is_deleted","false");
            comQB2.limit(0,8);

            List<EmpPerf> lowEmpPerfs = autoBaseDao.listAll(comQB2);
            analysisPo.setDownEmpPerfs(lowEmpPerfs);
        }else {
            analysisPo.setDownEmpPerfs(new ArrayList<>());
        }
    }

    public PagedList<CrossLevelEmpStaticsPo> pagedCrossLevel(PerfAnalysisQuery qry) {

        ComQB comQB = ComQB.build(CrossLevelEmpStaticsDo.class, "a")
                .join(EmployeeBaseInfoDo.class,"e")
                .appendOn("a.emp_id = e.employee_id and a.company_id = e.company_id");
        comQB.clearSelect().select(" a.*,CASE WHEN e.is_resigned != 0 THEN 1 ELSE 0 END as leaved")
                .setRsType(CrossLevelEmpStaticsPo.class);
        addCommonCondition(qry, comQB);
        comQB.whereEqReq("a.cross_level_type", qry.getCrossLevelType());
        comQB.whereEqReq("a.is_deleted","false");
        comQB.orderBy("a.emp_name asc , a.org_id asc ");
        comQB.setPage(qry.getPageNo(), qry.getPageSize());
        return autoBaseDao.listPage(comQB);
    }


    public CrossLevelCount getCrossLevelCount(String companyId, String cycleId, Integer performanceType) {

        ComQB comQB = ComQB.build(CrossLevelCountDo.class, "a");
        comQB.whereEqReq("a.company_id", companyId)
                .whereEqReq("a.cycle_id", cycleId)
                .whereEqReq("a.performance_type", performanceType)
                .whereEqReq("a.is_deleted", Boolean.FALSE.toString());
        return autoBaseDao.findDomain(comQB, CrossLevelCount.class);
    }
}
