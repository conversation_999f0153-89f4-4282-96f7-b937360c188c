package com.polaris.kpi.statistics.dao;

import com.polaris.kpi.statistics.ppojo.ActivePopulationDo;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: xuxw
 * @Date: 5/12/24 AM9:11
 * @Description:
 */
@Component
public class ActivePopulationDao {

    private static String sequenceName = "company_active_population_statistics";

    @Autowired
    private DomainDaoImpl domainDao;

    public ActivePopulationDo queryLoginRecord(String companyId, String empId, int year, int month) {
        ComQB build = ComQB.build(ActivePopulationDo.class, "a")
                .setRsType(ActivePopulationDo.class)
                .whereEq("company_id", companyId)
                .whereEq("created_user", empId)
                .whereEq("year", year)
                .whereEq("month", month);
        return domainDao.findOne(build);
    }

    public int updateLoginRecord(String id, String loginRecord, Integer loginCount) {
        UpdateBuilder updateBuilder = UpdateBuilder.build("company_active_population_statistics")
                .set("login_record", loginRecord)
                .set("login_count", loginCount)
                .whereEq("id", id);
        return domainDao.update(updateBuilder);
    }

    public int addLoginRecord(ActivePopulationDo add) {
        add.setId(domainDao.nextLongAsStr(sequenceName));
        return domainDao.add(ActivePopulationDo.class, add);
    }
}
