package com.polaris.kpi.org.infr.company.dao;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.perf.www.common.em.OperationLogSceneEnum;
import com.perf.www.cons.CompanyType;
import com.perf.www.domain.entity.company.CompanyModel;
import com.perf.www.domain.entity.company.OperationLogDo;
import com.perf.www.model.dic.CompanyDicModel;
import com.perf.www.model.dic.CompanySysSettingModel;
import com.polaris.acl.dept.pojo.CompanyDo;
import com.polaris.kpi.common.KpiI18NException;
import com.polaris.kpi.eval.infr.task.ppojo.HistoryEvalImportFieldsPo;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskBaseDo;
import com.polaris.kpi.org.domain.dept.entity.Company;
import com.polaris.kpi.org.domain.dept.entity.CompanyConf;
import com.polaris.kpi.org.infr.company.ppojo.EmployeePo;
import com.polaris.kpi.org.infr.emp.pojo.EmployeeBaseInfoDo;
import com.polaris.kpi.org.infr.emp.pojo.SystemAdminSetDo;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDate;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.DeleteBuilder;
import org.lufei.ibatis.builder.NativeSQLBuilder;
import org.lufei.ibatis.builder.QueryBuilder;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.dao.AutoBaseDao;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.lufei.ibatis.mapper.PagedList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * <h3>此处添加注释</h3>
 * created by Bruce.R on 2020/9/8
 */
@Component
@Slf4j
public class CompanyDaoImpl implements CompanyDao {
    @Resource
    private AutoBaseDao autoBaseDao;
    @Autowired
    private DomainDaoImpl domainDao;
    @Autowired
    private TenantSysConfDao confDao;

    public void setDomainDao(DomainDaoImpl domainDao) {
        this.domainDao = domainDao;
    }

    public void setAutoBaseDao(AutoBaseDao autoBaseDao) {
        this.autoBaseDao = autoBaseDao;
    }

    /**
     * @param companyId
     * @return
     * @see CompanyDaoImpl#getById(TenantId)
     */
    @Deprecated
    public CompanyModel findById(String companyId) {
        return autoBaseDao.findById(CompanyModel.class, companyId);
    }

    public CompanyDo getCompany(TenantId tenantId) {
        return domainDao.findById(CompanyDo.class, tenantId.getId());
    }

    public CompanyDo findByDingCorpId(String corpId) {
        return autoBaseDao.findOneByColName(CompanyDo.class, "ding_corp_id", corpId);
    }

    public CompanyModel findByCorpId(String corpId) {
        return autoBaseDao.findOneByColName(CompanyModel.class, "ding_corp_id", corpId);
    }

    public String queryCompanyMaxCode() {
        ComQB<CompanyModel> comQB = ComQB.build(CompanyModel.class).clearSelect().select("max(code) code");
        CompanyModel one = autoBaseDao.findOne(comQB);
        if (one != null) {
            return one.getCode();
        }
        return null;
    }

    //public String insert(CompanyModel company) {
    //    company.setCreatedTime(null);
    //    company.setUpdatedTime(null);
    //    autoBaseDao.save(company);
    //    return company.getId();
    //}


    public void update(CompanyModel company) {
        UpdateBuilder updateBuilder = UpdateBuilder.build(CompanyModel.class).setBean(company).whereEq("id", company.getId());
        autoBaseDao.update(updateBuilder);
    }

    public List<CompanyModel> getCompanyBySyncType(String syncType) {
        ComQB<CompanyModel> comQB = ComQB.build(CompanyModel.class).whereEq("sync_type", syncType);
        return autoBaseDao.listAll(comQB);
    }

    public CompanyModel getCompanyByCorpId(String qyCorpId, String dingCorpId, String weCorpId) {
        if (StringUtils.isNotEmpty(qyCorpId)) {
            return autoBaseDao.findOneByColName(CompanyModel.class, "corp_id", qyCorpId);
        } else if (StringUtils.isNotEmpty(dingCorpId)) {
            return autoBaseDao.findOneByColName(CompanyModel.class, "ding_corp_id", dingCorpId);
        } else if (StringUtils.isNotEmpty(weCorpId)) {
            return autoBaseDao.findOneByColName(CompanyModel.class, "we_corp_id", weCorpId);
        }
        return null;
    }


    public List<CompanyModel> queryAllActiveCompany() {
        return autoBaseDao.listByColName(CompanyModel.class, "status", "using");
    }


    public List<CompanyModel> queryAllRunningCompany() {
        ComQB<CompanyModel> comQB = ComQB.build(CompanyModel.class).whereEq("status", "using").whereBigEq("perm_end_time", LocalDate.now().toDate());
        return autoBaseDao.listAll(comQB);
    }

    public List<Company> allRunningCompany(Integer version) {
        ComQB<CompanyDo> comQB = ComQB.build(CompanyDo.class)
                .select("id,status,perm_end_time")
                .whereEq("status", "using")
                .whereBigEq("appVersion", version)
                .whereBigEq("perm_end_time", LocalDate.now().toDate());
        return domainDao.listAllDomain(comQB, Company.class);
    }

    public PagedList<Company> pagedAllCompanyV2(Integer version, Integer pageNo, Integer pageSize) {
        ComQB comQB = ComQB.build(CompanyDo.class)
                .clearSelect().setRsType(Company.class)
                .select("id,status,perm_end_time")
                .whereBigEq("appVersion", version)
                .whereBigEq("perm_end_time", LocalDate.now().toDate());
        comQB.setPage(pageNo, pageSize);
        return domainDao.listPage(comQB);
    }

    public PagedList<Company> pagedAllCompanyV2ForMigration21(Integer version, Integer pageNo, Integer pageSize,
                                                              Integer shardIndex,Integer shardTotal) {
        ComQB comQB = ComQB.build(CompanyDo.class)
                .clearSelect().setRsType(Company.class)
                .select("id,status,perm_end_time")
                .whereBigEq("appVersion", version)
                .appendWhere("(perm_end_time   < '2025-10-01' or perm_end_time  like '21%')");

        // 只有指定分片参数时才添加分片条件
        if (shardIndex != null && shardTotal != null && shardTotal > 1) {
            comQB.appendWhere("MOD(CRC32(id), " + shardTotal + ") = " + shardIndex);
            log.info("Using shard: {}/{}, hash condition added", shardIndex, shardTotal);
        }

        comQB.setPage(pageNo, pageSize);
        return domainDao.listPage(comQB);
    }

    public PagedList<Company> pagedAllCompanyV2WithShard(Integer version, Integer pageNo,
                                                         Integer pageSize,
                                                         Integer shardIndex,
                                                         Integer shardTotal) {
        ComQB comQB = ComQB.build(CompanyDo.class)
                .clearSelect().setRsType(Company.class)
                .select("id,status,perm_end_time")
                .whereBigEq("appVersion", version)
                .whereBigEq("perm_end_time", new LocalDate(2025, 10, 1).toDate())

                .appendWhere("perm_end_time not like '21%'");

        // 只有指定分片参数时才添加分片条件
        if (shardIndex != null && shardTotal != null && shardTotal > 1) {
            comQB.appendWhere("MOD(CRC32(id), " + shardTotal + ") = " + shardIndex);
            log.info("Using shard: {}/{}, hash condition added", shardIndex, shardTotal);
        }

        comQB.setPage(pageNo, pageSize);
        return domainDao.listPage(comQB);
    }

    public PagedList<CompanyModel> listPagedCompany(String keyword, String companyType, boolean permEnd, Integer pageNo, Integer pageSize) {
        NativeSQLBuilder<CompanyModel> comQB = NativeSQLBuilder.build(CompanyModel.class).setSql(
                "select * from company where 1=1"
        );
        if (StringUtils.isNotEmpty(companyType) && companyType.equals(CompanyType.TRIAL)) {
            comQB.append("and company_type in ('review','trial')");
        } else {
            comQB.appendIfOpt("and company_type = #{companyType}", "companyType", companyType);
        }
        if (permEnd) {
            comQB.append("and (perm_end_time is null or perm_end_time >=DATE_FORMAT(now(),'%Y-%m-%d')) ");
        }
        comQB.appendIfOpt("and (code like concat('%',#{keyword},'%') or name like concat('%',#{keyword},'%') or legal_person like concat('%',#{keyword},'%'))", "keyword", keyword);
        comQB.append("order by field(approval_status,'review') desc,created_time desc");
        comQB.setPage(pageNo, pageSize);
        return autoBaseDao.listPage(comQB);
    }


    public PagedList<CompanyModel> pagedForCrmCompany(Long updateTime, Integer pageNo, Integer pageSize) {
        final ComQB comQB = ComQB.build(CompanyDo.class)
                .setRsType(CompanyModel.class)
                .appendWhere("1=1")
                .whereBigEq("updatedTime", new Date(updateTime))
                .setPage(pageNo, pageSize);
        return autoBaseDao.listPage(comQB);
    }

    public PagedList<CompanyDo> fixPagedCompany(int pageSize) {
        final ComQB comQB = ComQB.build(CompanyDo.class)
                .clearSelect().select("id,status")
                .appendWhere("`last_version` ='2000000' ")
                .appendWhere("`app_version` = '1009002'")
                .appendWhere("`company_type` = 'trial' ")
                .setPage(1, pageSize);
        return autoBaseDao.listPage(comQB);
    }


    public void saveEmpLimitLog(String companyId, Integer empLimit, Integer oldEmpLimit) {
        JSONObject before = new JSONObject();
        before.put("empLimit", oldEmpLimit);

        JSONObject after = new JSONObject();
        after.put("empLimit", empLimit);

        OperationLogDo logModel = new OperationLogDo();
        logModel.setId(UUID.randomUUID().toString());
        logModel.setCompanyId(companyId);
        logModel.setRefId(companyId);
        logModel.setBusinessScene(OperationLogSceneEnum.COMPANY_PERM_SET.getScene());
        logModel.setBeforeValue(JSONObject.toJSONString(before));
        logModel.setAfterValue(JSONObject.toJSONString(after));
        logModel.setCreatedUser("dingding");
        logModel.setOperationType("update");
        autoBaseDao.save(logModel);
    }
/*

    public String getSuiteTicket() {
        ComQB<OpenSyncBizDataModel> qb = ComQB.buildDiff(OpenSyncBizDataModel.class, "ding_cloud_push.open_sync_biz_data", "d")
                .whereEq("bizType", 2).orderByDesc("gmtCreate");
        List<OpenSyncBizDataModel> list = autoBaseDao.listAll(qb);
        if (CollectionUtils.isNotEmpty(list)) {
            DingDingSuiteTicketBizData ticket = JSONObject.parseObject(list.get(0).getBizData(), DingDingSuiteTicketBizData.class);
            return ticket.getSuiteTicket();
        }
        return "";
    }
*/

    public Company getById(TenantId companyId) {
        final ComQB comQB = ComQB.build(CompanyDo.class, "c")
                .whereEq("id", companyId.getId());
        return domainDao.findDomain(comQB, Company.class);
    }

    public Map<String, Company> listCompanyAsMap(List<String> companyIds) {
        final ComQB comQB = ComQB.build(CompanyDo.class, "c")
                .whereIn("id", companyIds);
        List<Company> companies = domainDao.listAllDomain(comQB, Company.class);
        if (companies.isEmpty()) {
            return CollUtil.newHashMap();
        }
        return CollUtil.toMap(companies, new HashMap<>(), Company::getId);
    }

    /**
     * @description: 参看新dao {@link TenantSysConfDao#findCompanyConf(TenantId)}
     * @author: lufei
     * @date: 2024/1/13 10:04
     * @param: [companyId]
     * @return: com.polaris.kpi.org.domain.dept.entity.CompanyConf
     **/
    @Deprecated
    public CompanyConf findCompanyConf(TenantId companyId) {
        return confDao.findCompanyConf(companyId);
       /* ComQB comQB = ComQB.build(CompanyConfDo.class, "c")
                .clearSelect().select("id company_id,enter_on_scoring,result_input_send_msg,sup_is_can_auto_score_item")
                .select("can_res_submit_input_finish,eval_emp_on_log_auth,enter_on_ding_msg,ignore_vacancy_manager")
                .whereEqReq("id", companyId.getId());
        CompanyConf companyConf = domainDao.findDomain(comQB, CompanyConf.class);

        ComQB itemAuthQb = ComQB.build(TenantSysConfDo.class, "sc")
                .whereEqReq("company_id", companyId.getId());
        List<TenantSysConfDo> confDos = domainDao.listAll(itemAuthQb);
        ListWrap<TenantSysConfDo> listWrap = new ListWrap<>(confDos).asMap(confDo -> confDo.getConfCode());
        TenantSysConfDo confDo = listWrap.mapGet(TenantSysConfEnum.ITEM_AUTH.getType());
        companyConf.setItemAuth(confDo == null ? 0 : confDo.getOpen());

        TenantSysConfDo indConf = listWrap.mapGet(TenantSysConfDo.ind_level_group_202306027);
        companyConf.setIndLevelGroup(indConf == null ? 0 : indConf.getOpen());

        TenantSysConfDo indInput = listWrap.mapGet(TenantSysConfDo.ind_input_202308021);
        companyConf.setIndInput202308021(indInput == null ? 0 : indInput.getOpen());

        TenantSysConfDo tempAuth = listWrap.mapGet(TenantSysConfEnum.TEMP_AUTH.getType());
        companyConf.setTempAuth(tempAuth == null ? 0 : tempAuth.getOpen());
        TenantSysConfDo rank = listWrap.mapGet(TenantSysConfDo.result_rank_open_20231213);
        companyConf.setResultRankOpen20231213(rank == null ? 0 : rank.getOpen());

        return companyConf;
        */
    }

    public BigDecimal findFullScoreValue(TenantId companyId) {
        ComQB value = ComQB.build(CompanySysSettingModel.class)
                .clearSelect().select(" value ")
                .setRsType(BigDecimal.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEq("setting_type", "fullScore")
                .whereEq("is_deleted", "false");
        BigDecimal fullScore = autoBaseDao.findOne(value);
        return fullScore == null ? BigDecimal.valueOf(100) : fullScore;
    }

    @Override
    public Integer getAutoOpenFlag(String companyId) {
        ComQB value = ComQB.build(CompanyDo.class)
                .clearSelect().select(" emp_entry_auto_open ")
                .setRsType(Integer.class)
                .whereEqReq("id", companyId);
        return autoBaseDao.findOne(value);
    }

    public void updateAutoOpenFlag(String companyId, Integer openFlag) {
        UpdateBuilder up = UpdateBuilder.build("company")
                .set("emp_entry_auto_open", openFlag)
                .whereEqReq("id", companyId);
        domainDao.update(up);
    }

    public void updateStatus(String companyId, String status) {
        UpdateBuilder up = UpdateBuilder.build("company")
                .set("status", status)
                .whereEqReq("id", companyId);
        domainDao.update(up);
    }

    public void updateDingAgentId(String companyId, String dingAgentId, String dingSpaceId) {
        UpdateBuilder up = UpdateBuilder.build("company")
                .set("ding_agent_id", dingAgentId)
                .set("ding_space_id", dingSpaceId)
                .whereEqReq("id", companyId);
        domainDao.update(up);
    }

    public void delMigrationCompanyData(String companyId) {
        DeleteBuilder del = DeleteBuilder.build("company")
                .whereEqReq("id", companyId);
        domainDao.delete(del);
    }

    @Override
    public List<CompanyDo> getCompany(String companyName) {
        ComQB value = ComQB.build(CompanyDo.class)
                .setRsType(CompanyDo.class)
                .whereLike("name", companyName);
        return autoBaseDao.listAll(value);
    }

    public List<EmployeePo> getEmployee(String companyId, String empName) {
        ComQB value = ComQB.build(EmployeeBaseInfoDo.class)
                .setRsType(EmployeePo.class)
                .whereEqReq("company_id", companyId)
                .whereEq("is_delete", "false")
                .whereLike("name", empName);
        return autoBaseDao.listAll(value);
    }

    //随机取一个主管理员
    public EmployeePo getAdminEmp(String companyId) {
        ComQB comQB = ComQB.build(SystemAdminSetDo.class, "s")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn(" s.company_id = e.company_id and s.emp_id = e.employee_id ")
                .clearSelect().select(" e.* ")
                .setRsType(EmployeePo.class)
                .whereEqReq("s.company_id", companyId)
                .whereEq("s.admin_type", "main")
                .whereEq("s.status", "valid")
                .whereEq("e.is_delete", "false")
                .whereEq("e.status", "on_the_job")
                .limit(0, 1);
        EmployeePo admin = autoBaseDao.findOne(comQB);
        if (Objects.isNull(admin)) {
            throw new KpiI18NException("not exists admin", "该公司不存在有效的主管理员！");
        }
        return admin;
    }

    public EmployeePo getEmployeeByCompanyIdAndEmpName(String companyId, String empName, String employeeId) {
        ComQB value = ComQB.build(EmployeeBaseInfoDo.class)
                .setRsType(EmployeePo.class)
                .whereEqReq("company_id", companyId)
                .whereEq("is_delete", "false")
                .whereEq("status", "on_the_job")
                .whereEq("name", empName)
                .whereEq("employee_id", employeeId)
                .limit(0, 1);
        EmployeePo emp = autoBaseDao.findOne(value);
        if (Objects.isNull(emp)) {
            throw new KpiI18NException("not exists employee", "不存在的对应的员工：" + empName);
        }
        return emp;
    }

    //判断当前公司是否存在考核任务，如果没有则进入引导页面，如果已经创建过则不进入引导页面
    public Boolean needGuide(String companyId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskBaseDo.class)
                .clearSelect().select(" count(1)")
                .setRsType(Integer.class)
                .whereEqReq("company_id", companyId);
        Integer cnt = autoBaseDao.findOne(comQB);
        return cnt == 0;
    }

    public List<HistoryEvalImportFieldsPo.KpiTypeResult> lidtCompanyKpiTypes(String companyId) {

        ComQB queryBuilder = ComQB.build(CompanyDicModel.class)
                .clearSelect().select("id as kpiTypeId,dic_value as kpiTypeName")
                .setRsType(HistoryEvalImportFieldsPo.KpiTypeResult.class)
                .whereEq("is_deleted", "false")
                .whereEq("sys_dic_type", CompanyDicModel.KPI_TYPE)
                .whereEq("company_id", companyId)
                .appendWhere("(is_temporary is null or is_temporary = 'false')");
        return autoBaseDao.listAll(queryBuilder);
    }
}
