package com.polaris.kpi.org.infr.dept.dao;

import cn.com.polaris.kpi.*;
import cn.com.polaris.kpi.company.TenantSysConf;
import cn.com.polaris.kpi.eval.EmpRefOrg;
import cn.com.polaris.kpi.eval.EvaluationStaff;
import cn.com.polaris.kpi.eval.LevelManager;
import cn.com.polaris.kpi.eval.SimpleEmpInfo;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.perf.www.common.utils.string.StringTool;
import com.perf.www.cons.EmployeeStatus;
import com.perf.www.domain.entity.company.EmpOrganizationModel;
import com.perf.www.dto.TaskUserOrg;
import com.perf.www.model.dic.CompanySysSettingModel;
import com.polaris.acl.dept.pojo.AdminSetDo;
import com.polaris.acl.dept.pojo.org.EmpPositionDo;
import com.polaris.acl.dept.pojo.org.EmpRankDo;
import com.polaris.acl.dept.pojo.role.RoleDo;
import com.polaris.acl.dept.pojo.role.RoleRefEmpPo;
import com.polaris.acl.kpi.eval.domain.EvalEmp;
import com.polaris.kpi.eval.domain.task.entity.BetaCompanys;
import com.polaris.kpi.eval.domain.task.entity.admineval.OpEmpEval;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalOrgInfo;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskKpiDo;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskUserDo;
import com.polaris.kpi.eval.infr.task.ppojo.ShowSupOrgPo;
import com.polaris.kpi.eval.infr.task.query.KpiEmpQuery;
import com.polaris.kpi.org.domain.dept.repo.DeptFinder;
import com.polaris.kpi.org.domain.emp.entity.KpiEmployee;
import com.polaris.kpi.org.domain.emp.repo.KpiEmpRepo;
import com.polaris.kpi.org.domain.emp.type.Emp;
import com.polaris.kpi.org.infr.company.dao.TenantSysConfDao;
import com.polaris.kpi.org.infr.company.ppojo.AdminScopePrivGroupOfEmpDo;
import com.polaris.kpi.org.infr.company.ppojo.AdminScopePrivGroupOrgDo;
import com.polaris.kpi.org.infr.company.ppojo.CompanyDo;
import com.polaris.kpi.org.infr.company.ppojo.TenantSysConfDo;
import com.polaris.kpi.org.infr.dept.pojo.AdminManageOrgDo;
import com.polaris.kpi.org.infr.dept.pojo.CurOrgStructPo;
import com.polaris.kpi.org.infr.dept.query.PrivOrgQuery;
import com.polaris.kpi.org.infr.emp.pojo.*;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.lufei.ibatis.mapper.PagedList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lufei
 * @date 2022/2/16 3:22 下午
 */
@Component
@Slf4j
public class KpiOrgDao extends BaseAdminPrivDao implements KpiEmpRepo , DeptFinder {

    @Resource
    private DomainDaoImpl dao;

    public void setDomainDao(DomainDaoImpl domainDao) {
        this.dao = domainDao;
    }

    @Autowired
    private TenantSysConfDao tenantSysConfDao;

    public boolean isOrgEmpCntOpen(String companyId) {
        ComQB qb = ComQB.build(TenantSysConfDo.class, "conf")
                .whereEq("companyId", companyId)
                .appendWhere("open= 1")
                .whereEq("confCode", TenantSysConfDo.org_emp_cnt_init_20230619);
        TenantSysConfDo open = dao.findOne(qb);
        return open != null;
    }

    public List<OrganizationPo> listChild1OrgR(String companyId, String orgId) {
        final ComQB qb = ComQB.build(EmpOrganizationDo.class, "o")
                .setRsType(OrganizationPo.class)
                .clearSelect().select("org_id,org_name,name_chinese,parent_org_id")
                .select("distinct_total_emp_cnt as emp_cnt,direct_child_org_cnt as l1_child_cnt")
                .whereEq("parent_org_id", orgId)
                .whereEq("companyId", companyId)
                .appendWhere("status='valid'");
        if (StrUtil.isBlank(orgId)) {
            qb.whereIsNull("parent_org_id");
        }
        final List<OrganizationPo> childOrgs = dao.listAll(qb);
        if (childOrgs.isEmpty()) {
            return childOrgs;
        }
        //统计子部门的主管
        List<String> childOrgIds = childOrgs.stream().map(OrganizationPo::getOrgId).collect(Collectors.toList());
        final Map<String, List<OrgManagerPo>> orgManagerGroups = listManangers(childOrgIds);
        childOrgs.forEach(org -> {
            org.updateManangers(orgManagerGroups.get(org.getOrgId()));
        });
        return childOrgs;
    }

    public List<OrganizationPo> listChild1Org(String companyId, String orgId) {
        final ComQB qb = ComQB.build(EmpOrganizationDo.class, "o")
                .setRsType(OrganizationPo.class)
                .whereEq("parent_org_id", orgId)
                .whereEq("companyId", companyId)
                .appendWhere("status='valid'");
        if (StrUtil.isBlank(orgId)) {
            qb.whereIsNull("parent_org_id");
        }
        final List<OrganizationPo> childOrgs = dao.listAll(qb);
        if (childOrgs.isEmpty()) {
            return childOrgs;
        }
        List<String> childOrgIds = childOrgs.stream().map(OrganizationPo::getOrgId).collect(Collectors.toList());
        // 递归统计子部门人数
        ComQB cntEmpQ = ComQB.build(EmpRefOrgDo.class, "ref")
                .join(EmployeeBaseInfoDo.class, "ebi").appendOn("ref.company_id = ebi.company_id and ebi.employee_id = ref.emp_id")
                .join(EmpOrganizationDo.class, "s").appendOn("ref.company_id = s.company_id  and  s.org_id =ref.org_id")
                .join(EmpOrganizationDo.class, "p").appendOn("s.company_id=p.company_id and instr(s.org_code,p.org_code) ")
                .clearSelect().select(" p.org_id,count(distinct ebi.employee_id) emp_cnt")
                .setRsType(OrganizationPo.class)
                .whereIn(" p.org_id", childOrgIds)
                .appendWhere("ref.ref_type='org'")
                .whereEq("ref.companyId", companyId)
                .appendWhere("ebi.is_delete='false'")
                .appendWhere("s.status='valid'")
                .groupBy(" p.org_id");
        List<OrganizationPo> empCntAtOrgs = dao.listAll(cntEmpQ);
        Map<String, OrganizationPo> cntMap = empCntAtOrgs.stream().collect(Collectors.toMap(OrganizationPo::getOrgId, v -> v));

        // 统计子部门数
        ComQB childOrgCntQ = ComQB.build(EmpOrganizationDo.class, "s")
                .join(EmpOrganizationDo.class, "p").appendOn("s.parent_org_id = p.org_id and s.company_id=p.company_id")
                .clearSelect().select(" p.org_id,count(1) l1_child_cnt")
                .setRsType(OrganizationPo.class)
                .whereIn(" p.org_id", childOrgIds)
                .appendWhere("s.status='valid'")
                .groupBy(" p.org_id");
        final List<OrganizationPo> childOrgCnts = dao.listAll(childOrgCntQ);
        Map<String, OrganizationPo> childOrgCntMap = childOrgCnts.stream().collect(Collectors.toMap(OrganizationPo::getOrgId, v -> v));

        //统计子部门的主管
        final Map<String, List<OrgManagerPo>> orgManagerGroups = listManangers(childOrgIds);

        childOrgs.forEach(org -> {
            org.updateEmpCnt(cntMap.get(org.getOrgId()));
            org.updateL1Child(childOrgCntMap.get(org.getOrgId()));
            org.updateManangers(orgManagerGroups.get(org.getOrgId()));
        });
        return childOrgs;
    }

    private Map<String, List<OrgManagerPo>> listManangers(List<String> childOrgIds) {
        ComQB orgManagerQ = ComQB.build(EmpRefOrgDo.class, "ref")
                .join(EmployeeBaseInfoDo.class, "ebi").appendOn("ref.company_id = ebi.company_id and ebi.employee_id = ref.emp_id")
                .clearSelect().select(" ref.org_id,ref.emp_id ,ebi.name").setRsType(OrgManagerPo.class)
                .appendWhere("ref.ref_type='manager'")
                .appendWhere("ebi.is_delete='false'")
                .whereIn("ref.org_id", childOrgIds);
        final List<OrgManagerPo> mangers = dao.listAll(orgManagerQ);
        final Map<String, List<OrgManagerPo>> orgManagerGroups = mangers.stream().collect(Collectors.groupingBy(OrgManagerPo::getOrgId));
        return orgManagerGroups;
    }

    public OrgEmpSearchPo searchOrgEmp(String companyId, Keyword keyword) {
        final ComQB qb = ComQB.build(EmpOrganizationDo.class, "o")
                .leftJoin(EmpOrganizationDo.class, "p").appendOn(" p.company_id = o.company_id  and  p.org_id =ifnull(o.parent_org_id,0)")
                .clearSelect().select("o.org_name,o.org_id,p.org_name parent_org_name")
                .setRsType(OrganizationPo.class)
                .whereLike("o.org_name", keyword.getValue())
                .whereEq("o.companyId", companyId)
                .appendWhere("o.status='valid'");
        List<OrganizationPo> orgs = dao.listAll(qb);

        ComQB empQ = ComQB.build(EmpRefOrgDo.class, "ref")
                .join(EmployeeBaseInfoDo.class, "ebi").appendOn("ref.company_id = ebi.company_id and ebi.employee_id = ref.emp_id")
                .join(EmpOrganizationDo.class, "s").appendOn("ref.company_id = s.company_id  and  s.org_id =ref.org_id")
                .clearSelect().select("distinct ebi.name, ebi.employee_id id  ")
                .setRsType(Emp.class)
                .whereLike("ebi.name", keyword.getValue())
                .whereEq("s.companyId", companyId)
                .appendWhere("ebi.is_delete='false'");
        if (!orgs.isEmpty()) {
            List<String> orgIds = orgs.stream().map(OrganizationPo::getOrgId).collect(Collectors.toList());
            final Map<String, List<OrgManagerPo>> orgManagerGroups = listManangers(orgIds);
            orgs.forEach(org -> {//主管理信息
                org.updateManangers(orgManagerGroups.get(org.getOrgId()));
            });
            empQ.and().startPt().whereLike("ebi.name", keyword.getValue())
                    .or().whereIn("s.org_id", orgIds).endPt();
        }
        List<Emp> emps = dao.listAll(empQ);
        return new OrgEmpSearchPo(orgs, emps);
    }

    public List<EmpStaff> listEmpByStaffConfItem(TenantId comapnyId, EvaluationStaff staff) {
        List<EmpStaff> emps = new ArrayList<>();
        if (staff.hasFixEmp()) {
            List<EmpStaff> fixEmps = listEmpStaffByIds(comapnyId, staff.fixEmpIds());
            emps.addAll(fixEmps);
        }
        if (staff.hasRoleRule()) {
            List<EmpStaff> roleEmps = listEmpByRoleId(comapnyId, StringUtils.join(staff.roleIds(), ","));
            emps.addAll(roleEmps);
        }
        if (staff.hasDeptRule()) {
            List<EmpStaff> deptEmps = listEmpStaffDept(comapnyId, staff.deptIds());
            emps.addAll(deptEmps);
        }
        if (staff.hasLevelManger()) {
            List<LevelManager> levelManagers = listEmpsManager(comapnyId, new EmpId(staff.levelEmpId()));
            List<EmpStaff> empStaffs = staff.matchLevel(levelManagers);
            emps.addAll(empStaffs);
        }
        if (staff.hasExcludeStaff()) {
            emps.removeIf(empStaff -> staff.inExcludeStaff(empStaff.getEmpId()));
        }
        return emps;
    }

    private ComQB buildSelectOrgQ(TenantId companyId) {
        final ComQB selectQ = ComQB.build(EmpOrganizationDo.class, "o")
                .clearSelect()
                .select("GROUP_CONCAT(o.org_name)")
                .whereEq("o.company_id", companyId.getId())
                .appendWhere("o.org_id in (select org_id from emp_ref_org where emp_id = e.employee_id and ref_type = 'org')");
        return selectQ;
    }

    @Override
    public List<EmpStaff> listEmpByRoleId(TenantId companyId, String roleId) {
        return listEmpStaffByRole(companyId, Arrays.asList(roleId), null, null);
    }

    @Override
    public List<EmpStaff> listEmpByRoleId(TenantId companyId, String roleId, String orgId, String empId) {
        return listEmpStaffByRole(companyId, Arrays.asList(roleId), orgId, empId);
    }

    public List<EmpStaff> listEmpStaffByRole(TenantId companyId, List<String> roleIds, String orgId, String empId) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return new ArrayList<>();
        }
        final ComQB selectQ = buildSelectOrgQ(companyId);
        final ComQB qb = ComQB.build(RoleRefEmpDo.class, "r")
                .leftJoin(RoleDo.class, "ro").appendOn("r.company_id = ro.company_id and r.role_id = ro.id")
                .leftJoin(EmployeeBaseInfoDo.class, "e").appendOn("r.emp_id = e.employee_id  and r.company_id = e.company_id")
                .clearSelect().select(" DISTINCT r.emp_id,e.`name` as empName,r.role_id as roleId, ro.role_name as roleName ")
                .select("scope_org_ids , scope_emps")
                .select(String.format("(%s) as %s", selectQ.getSql(), "org_name")).setRsType(RoleRefEmpPo.class)
                .whereEq("r.company_id", companyId.getId())
                .appendWhere(" r.`status` = 'valid' and e.is_delete = 'false' and e.status = 'on_the_job' and r.is_deleted = 'false' ")
                .appendWhere(" e.status = 'on_the_job'")
                .whereIn("r.role_id", roleIds);
        qb.getParams().putAll(selectQ.getParams());
        List<RoleRefEmpPo> list = dao.listAll(qb);
        log.debug("查询出的角色关联人员:{}", JSONUtil.toJsonStr(list));
        boolean isManageScope = true;
        ComQB query = ComQB.build(CompanySysSettingModel.class, "r").whereEq("company_id", companyId.getId())
                .whereEq("setting_type", "roleRefEmpManageScope").whereEq("is_deleted", "false");
        CompanySysSettingModel settingModel = dao.findOne(query);
        if (settingModel != null) {
            if (settingModel.getValue().equals(Boolean.FALSE.toString())) {
                isManageScope = false;
            }
        }
        log.debug("是否开启了按管理范围审核:{}", isManageScope);
        if (!isManageScope || (StrUtil.isBlank(orgId) && StrUtil.isBlank(empId))) {
            return Convert.toList(EmpStaff.class, list);
        }
        String orgCode = "";
        if (StrUtil.isBlank(orgId)) {
            orgCode = parseEmpOrgCode(companyId.getId(), empId);
        } else {
            KpiDeptCode org = getOrgCode(companyId, orgId);
            if (Objects.isNull(org)) {
                //return Convert.toList(EmpStaff.class, list);
                orgCode = "-1";
            } else {
                orgCode = org.getOrgCode().replace("|", ",");
            }
        }
        //过滤出管理范围未设置与该任务的所属部门在管理范围内的人员
        String finalOrgCode = orgCode;
        log.debug("*****finalOrgCode*****:{}", finalOrgCode);
        list = list.stream().filter(refEmp ->
                        // 情况1：scopeOrgIds为空（无组织限制）
                        CollUtil.isEmpty(refEmp.getScopeOrgIds()) && CollUtil.isEmpty(refEmp.getScopeEmps()) ||
                                // 情况2：scopeOrgIds包含当前org（有组织权限）
                                (CollUtil.isNotEmpty(refEmp.getScopeOrgIds()) &&
                                        refEmp.getScopeOrgIds().stream()
                                                .anyMatch(org -> Objects.nonNull(org) && finalOrgCode.contains(org))) ||
                                // 情况3：scopeEmpIds包含当前empId（有员工权限）
                                (CollUtil.isNotEmpty(refEmp.getScopeEmps()) &&
                                        refEmp.getScopeEmps().stream()
                                                .anyMatch(emp -> Objects.nonNull(emp) && emp.getEmpId().equals(empId)))
                )
                .collect(Collectors.toList());
        log.debug("根据管理范围审核的审核人:{}", JSONUtil.toJsonStr(list));
        return Convert.toList(EmpStaff.class, list);
    }

    private String parseEmpOrgCode(String companyId, String empId) {
        List<String> orgCodes = listOrgCodes(empId, companyId);
        List<String> codeList = new ArrayList<>();
        orgCodes.forEach(org -> {
            codeList.addAll(Arrays.asList(org.replace("|", ","), ","));
        });
        return StringUtils.join(codeList, ",");
    }


    public List<EmpStaff> listEmpStaffByIds(TenantId companyId, List<String> empIds) {
        if (CollectionUtils.isEmpty(empIds)) {
            return null;
        }

        final ComQB selectQ = buildSelectOrgQ(companyId);
        final ComQB qb = ComQB.build(EmpRefOrgDo.class, "r")
                .leftJoin(EmployeeBaseInfoDo.class, "e").appendOn("r.emp_id = e.employee_id  and r.company_id = e.company_id")
                .clearSelect().select(" DISTINCT r.emp_id, r.org_id, e.`name` as empName ,e.avatar as empAvatar")
                .select(String.format("(%s) as %s", selectQ.getSql(), "org_name")).setRsType(EmpStaff.class)
                .whereEq("r.company_id", companyId.getId())
                .appendWhere(" r.ref_type = 'org' and e.is_delete = 'false'")
                //.appendWhere(" e.status = 'on_the_job'")
                .whereIn("r.emp_id", empIds);
        qb.getParams().putAll(selectQ.getParams());
        return dao.listAll(qb);
    }

    public List<EmpStaff> listEmpStaffDept(TenantId companyId, List<String> deptIds) {
        if (CollectionUtils.isEmpty(deptIds)) {
            return new ArrayList<>();
        }
        final ComQB selectQ = buildSelectOrgQ(companyId);
        final ComQB qb = ComQB.build(EmpRefOrgDo.class, "r")
                .leftJoin(EmployeeBaseInfoDo.class, "e").appendOn("r.emp_id = e.employee_id  and r.company_id = e.company_id")
                .clearSelect().select(" DISTINCT r.emp_id,e.`name` as empName ")
                .select(String.format("(%s) as %s", selectQ.getSql(), "org_name")).setRsType(EmpStaff.class)
                .whereEq("r.company_id", companyId.getId())
                .appendWhere(" r.ref_type = 'org' and e.is_delete = 'false'")
                .appendWhere(" e.status = 'on_the_job'")
                .whereIn("r.org_id", deptIds);
        qb.getParams().putAll(selectQ.getParams());
        return dao.listAll(qb);
    }

    public Map<String, String> mapOrgIdToName(String tenantId, List<String> orgIds) {
        if (orgIds == null || orgIds.isEmpty()) {
            return Collections.emptyMap();
        }

        ComQB comQB = ComQB.build(EmpOrganizationDo.class, "o")
                .clearSelect()
                .select("o.org_id", "org_id")
                .select("o.org_name", "org_name")
                .whereEq("o.company_id", tenantId)
                .whereInReq("o.org_id", new ArrayList<>(orgIds));

        List<EmpOrganizationDo> orgList = dao.listAll(comQB);

        return orgList.stream().collect(Collectors.toMap(EmpOrganizationDo::getOrgId, EmpOrganizationDo::getOrgName));
    }


    //查员工的各级主管
    public List<LevelManager> listEmpsManager(TenantId companyId, EmpId empId, String atOrgId, Integer level, boolean filterLevel1IfDirect) {
        List<LevelManager> tmpRs = new ArrayList<>();
        //直属主管，从员工mangerId查找
        LevelManager directMg = findDirectMgEmpId(companyId, empId.getId());
        if (null != directMg) {
            tmpRs.add(directMg);
        }
        ////查询员工所在部门
        List<String> atOrgIds = StrUtil.isBlank(atOrgId) ? listAtOrgIds(companyId, empId, false) : Arrays.asList(atOrgId);
        log.info("查询员工所在部门={}", JSON.toJSONString(atOrgIds));
        if (CollUtil.isEmpty(atOrgIds) || atOrgIds.get(0) == null) {
            return tmpRs;
        }
        //检查员工自己是主管情况
        List<LevelManager> atOrgManagers = listAtOrgManager(companyId, atOrgIds, false, false);
        log.info("listAtOrgManager.atOrgManagers={}", JSON.toJSONString(atOrgManagers));
        atOrgManagers = filterDirectLevelManagers(empId, atOrgManagers);
        log.info("tryFilterSelfManager.atOrgManagers={}", JSON.toJSONString(atOrgManagers));
        //存在直属主管时，过滤掉1级部门主管，避免直属主管和部门一级主管为同一人时，权重会累计为200%
        if (null != directMg) {
            atOrgManagers.removeIf(mg -> mg.eqLevel(1));
        }
        log.info("atOrgManagers={}", JSON.toJSONString(atOrgManagers));
        Set<String> keys = new HashSet<>();
        //按人与层级去重
        for (LevelManager atOrgMg : atOrgManagers) {
            if (keys.contains(atOrgMg.levelEmpKey())) {
                continue;
            }
            tmpRs.add(atOrgMg);
            keys.add(atOrgMg.levelEmpKey());
        }

        if (openIgnoreVacancyManager(companyId.getId())) {
            reSortLevel(tmpRs);
        }
        log.info("tmpRs={}", JSON.toJSONString(tmpRs));
        return tmpRs;
    }

    //查员工的各级主管
    public List<LevelManager> listEmpsManager(TenantId companyId, EmpId empId, String atOrgId, Integer level) {
        List<LevelManager> tmpRs = new ArrayList<>();
        //直属主管，从员工mangerId查找
        LevelManager directMg = findDirectMgEmpId(companyId, empId.getId());
        if (null != directMg) {
            tmpRs.add(directMg);
        }
        ////查询员工所在部门
        List<String> atOrgIds = StrUtil.isBlank(atOrgId) ? listAtOrgIds(companyId, empId) : Arrays.asList(atOrgId);
        log.info("查询员工所在部门={}", JSON.toJSONString(atOrgIds));
        if (CollUtil.isEmpty(atOrgIds) || atOrgIds.get(0) == null) {
            tmpRs.add(directMg);
            return tmpRs;
        }
        //检查员工自己是主管情况
        List<LevelManager> atOrgManagers = listAtOrgManager(companyId, atOrgIds);
        log.info("listAtOrgManager.atOrgManagers={}", JSON.toJSONString(atOrgManagers));
        atOrgManagers = tryFilterSelfManager(empId, atOrgManagers, level);
        log.info("tryFilterSelfManager.atOrgManagers={}", JSON.toJSONString(atOrgManagers));
        //存在直属主管时，过滤掉1级部门主管，避免直属主管和部门一级主管为同一人时，权重会累计为200%
        if (null != directMg) {
            atOrgManagers.removeIf(mg -> mg.eqLevel(1));
        }
        log.info("atOrgManagers={}", JSON.toJSONString(atOrgManagers));
        Set<String> keys = new HashSet<>();
        //按人与层级去重
        for (LevelManager atOrgMg : atOrgManagers) {
            if (keys.contains(atOrgMg.levelEmpKey())) {
                continue;
            }
            tmpRs.add(atOrgMg);
            keys.add(atOrgMg.levelEmpKey());
        }

        if (openIgnoreVacancyManager(companyId.getId())) {
            reSortLevel(tmpRs);
        }
        log.info("tmpRs={}", JSON.toJSONString(tmpRs));
        return tmpRs;
    }

    //查员工的各级主管
    public List<LevelManager> listEmpsManager(TenantId companyId, EmpId empId) {
        return listEmpsManager(companyId, empId, null, null);
    }

    private void reSortLevel(List<LevelManager> tmpRs) {
        //分组后重新排序,过滤自己是主管并且存在同级主管的场景
        Map<Integer, List<LevelManager>> map = tmpRs.stream()
                .filter(l -> l.getLevel() != 0)
                .collect(Collectors.groupingBy(l -> l.getLevel()));
        Collection<List<LevelManager>> values = map.values();
        int i = 1;
        for (List<LevelManager> value : values) {
            for (LevelManager levelManager : value) {
                levelManager.setLevel(i);
            }
            i++;
        }
    }

    @Nullable
    private List<LevelManager> tryFilterSelfManager(EmpId empId, List<LevelManager> atOrgManagers, Integer level) {
        List<LevelManager> selfMgs = CollUtil.filterNew(atOrgManagers, mg -> mg.isManagerSelf(empId.getId()));
        //自己不是主管
        if (CollUtil.isEmpty(selfMgs)) {
            return atOrgManagers;
        }
        //包含自己是主管,考核人作为主管所在的部门主管remove
        atOrgManagers.removeAll(selfMgs);
        //
        //List<String> selfOrgs = selfMgs.stream().map(m -> m.getSOrgId()).collect(Collectors.toList());
        //atOrgManagers.removeIf(m -> selfOrgs.contains(m.getSOrgId()));

        for (LevelManager selfMg : selfMgs) {
            for (LevelManager supperLevel : atOrgManagers) {
                if (supperLevel.getSOrgId().equals(selfMg.getSOrgId()) && (supperLevel.getLevel() > selfMg.getLevel())) {
                    supperLevel.subLevel();
                }
            }
        }
        return atOrgManagers;
    }

    /**
     * 过滤一级主管（包含自己和同级其他主管）
     * @param empId 当前员工ID
     * @param managers 待过滤的主管列表
     * @return 过滤后的主管列表（可能为null）
     */
    @Nullable
    private List<LevelManager> filterDirectLevelManagers(EmpId empId, List<LevelManager> managers) {
        // 1. 找出自己作为一级主管的记录
        List<LevelManager> selfAsDirectManagers = managers.stream()
                .filter(mg -> mg.isManagerSelf(empId.getId()) && mg.getLevel() == 1)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(selfAsDirectManagers)) {
            return managers;
        }

        // 2. 收集需要过滤的部门ID（仅限一级主管所在部门）
        Set<String> targetDeptIds = selfAsDirectManagers.stream()
                .map(LevelManager::getSOrgId)
                .collect(Collectors.toSet());

        // 3. 执行过滤（自己和同级一级主管）
        managers.removeIf(mg ->
                targetDeptIds.contains(mg.getSOrgId()) &&
                        mg.getLevel() == 1 &&
                        (mg.isManagerSelf(empId.getId()) || isPeerManager(mg, selfAsDirectManagers))
        );

        // 4. 调整更高层级的主管级别（保持原有逻辑）
        selfAsDirectManagers.forEach(selfMg ->
                managers.stream()
                        .filter(higherMg ->
                                higherMg.getSOrgId().equals(selfMg.getSOrgId()) &&
                                        higherMg.getLevel() > selfMg.getLevel())
                        .forEach(LevelManager::subLevel)
        );

        return managers;
    }

    // 判断是否为同级主管
    private boolean isPeerManager(LevelManager mg, List<LevelManager> selfManagers) {
        return selfManagers.stream()
                .anyMatch(self ->
                        self.getSOrgId().equals(mg.getSOrgId()) &&
                                self.getLevel() == mg.getLevel());
    }

    /**
     * 过滤员工自己作为主管的记录及同部门同级别的其他主管
     * @param empId 当前员工ID
     * @param managers 待过滤的主管列表
     * @param level 目标层级（可选）
     * @return 过滤后的主管列表（可能为null）
     */
    @Nullable
    private List<LevelManager> filterSelfAndPeerManagers(EmpId empId, List<LevelManager> managers, Integer level) {
        // 1. 找出自己作为主管的记录
        List<LevelManager> selfManagers = CollUtil.filterNew(managers, mg -> mg.isManagerSelf(empId.getId()));
        if (CollUtil.isEmpty(selfManagers)) {
            return managers;
        }

        // 2. 收集需要过滤的部门ID和层级
        Set<String> targetOrgIds = selfManagers.stream()
                .map(LevelManager::getSOrgId)
                .collect(Collectors.toSet());
        Set<Integer> targetLevels = selfManagers.stream()
                .map(LevelManager::getLevel)
                .collect(Collectors.toSet());

        // 3. 执行过滤（自己和同级主管）
        managers.removeIf(mg ->
                mg.isManagerSelf(empId.getId()) ||
                        (targetOrgIds.contains(mg.getSOrgId()) && targetLevels.contains(mg.getLevel())));

        // 4. 调整更高层级的主管级别
        selfManagers.forEach(selfMg ->
                managers.stream()
                        .filter(higherMg ->
                                higherMg.getSOrgId().equals(selfMg.getSOrgId()) &&
                                        higherMg.getLevel() > selfMg.getLevel()
                        )
                        .forEach(LevelManager::subLevel)
        );

        return managers;
    }


    public LevelManager getLevel1Manager(TenantId companyId, String empId, String orgId) {
        final ComQB comQB = ComQB.build(EmpOrganizationDo.class, "p")
                .join(EmpRefOrgDo.class, "pm").appendOn("pm.company_id = p.company_id and pm.org_id = p.org_id and pm.ref_type = 'manager'")
                .join(EmployeeBaseInfoDo.class, "memp").appendOn(" memp.company_id = pm.company_id and memp.employee_id = pm.emp_id")
                .clearSelect().select("p.org_id  p_org_id,pm.emp_id manager_id,memp.name manager_name ")
                .setRsType(LevelManager.class)
                .whereEq("pm.company_id", companyId.getId())
                .whereEqReq("pm.org_id", orgId)
                .limit(0, 1);
        LevelManager orgMg = dao.findOne(comQB);
        return orgMg;
    }


    public LevelManager findDirectMgEmpId(TenantId tenantId, String empId) {
        log.info(" findDirectMgEmpId params,tenantId:{},empid:{}", tenantId.getId(), empId);
        if (StrUtil.isBlank(empId)) {
            return null;
        }
        ComQB comQB = ComQB.build(EmployeeBaseInfoDo.class, "e")
                .clearSelect().select("ding_manager_id").setRsType(String.class)
                .whereEq("company_id", tenantId.getId())
                .whereEq("employeeId", empId)
                .limit(0, 1);
        String dingMgId = dao.findOne(comQB);
        log.info(" findDirectMgEmpId dingMgId:{}", dingMgId);
        if (StrUtil.isBlank(dingMgId)) {
            return null;
        }
        ComQB mgQ = ComQB.build(EmployeeBaseInfoDo.class, "e")
                .whereEq("company_id", tenantId.getId())
                .whereEq("ding_user_id", dingMgId)
                .whereEq("is_delete", Boolean.FALSE.toString())
                .whereEq("status", EmployeeStatus.ON_THE_JOB)
                .clearSelect().select("employee_id as manager_id,name manager_name, 1 level")
                .setRsType(LevelManager.class)
                .limit(0, 1);
        LevelManager one = dao.findOne(mgQ);
        log.info(" findDirectMgEmpId one:{}", JSONUtil.toJsonStr(one));
        if (one == null) {
            return one;
        }
        one.setDirect(true);
        return one;
    }

    public List<LevelManager> listAtOrgManager(TenantId companyId, List<String> atOrgIds) {
        if (atOrgIds.isEmpty()) {
            return new ArrayList<>();
        }
        final ComQB comQB = ComQB.build(EmpOrganizationDo.class, "s")
                .join(EmpOrganizationDo.class, "p").appendOn("s.company_id = p.company_id and instr(s.org_code, p.org_code) and p.status='valid'")
                .join(EmpRefOrgDo.class, "pm").appendOn("pm.company_id = p.company_id and pm.org_id = p.org_id and pm.ref_type = 'manager'")
                .join(EmployeeBaseInfoDo.class, "memp").appendOn(" memp.company_id = pm.company_id and memp.employee_id = pm.emp_id")
                .clearSelect()
                .select("s.org_id  s_org_id, p.org_id  p_org_id, p.org_name AS p_org_name, pm.emp_id manager_id,memp.name manager_name ")
                .select("FORMAT(      (length(s.org_code)) - (length(replace(s.org_code, '|', '')) ), 0     ) " +
                        "- FORMAT(      (length(p.org_code)) - (length(replace(p.org_code, '|', '')) ), 0     )+1  level")
                .setRsType(LevelManager.class)
                .whereEq("memp.status", EmployeeStatus.ON_THE_JOB)
                .whereEq("memp.is_delete", Boolean.FALSE.toString())
                .whereEq("s.company_id", companyId.getId())
                .whereInReq("s.org_id", atOrgIds)
                .orderBy(" level ");
        List<LevelManager> levelManagers = dao.listAll(comQB);
        log.info("查找出来的主管们：{}", JSON.toJSONString(levelManagers));
        return levelManagers;
    }

    /**
     * 查询部门主管链
     * @param companyId 公司ID
     * @param atOrgIds 部门ID列表
     * @param checkDeptStatus 是否检查部门状态为valid
     * @param checkEmpStatus 是否检查员工在职状态
     * @return 主管层级列表
     */
    public List<LevelManager> listAtOrgManager(
            TenantId companyId,
            List<String> atOrgIds,
            boolean checkDeptStatus,
            boolean checkEmpStatus
    ) {
        if (CollUtil.isEmpty(atOrgIds)) {
            return Collections.emptyList();
        }

        final ComQB comQB = ComQB.build(EmpOrganizationDo.class, "s")
                .join(EmpOrganizationDo.class, "p").appendOn("s.company_id = p.company_id and instr(s.org_code, p.org_code)")
                .join(EmpRefOrgDo.class, "pm").appendOn("pm.company_id = p.company_id and pm.org_id = p.org_id and pm.ref_type = 'manager'")
                .join(EmployeeBaseInfoDo.class, "memp").appendOn(" memp.company_id = pm.company_id and memp.employee_id = pm.emp_id")
                .clearSelect()
                .select("s.org_id  s_org_id, p.org_id  p_org_id, p.org_name AS p_org_name, pm.emp_id manager_id,memp.name manager_name ")
                .select("FORMAT(      (length(s.org_code)) - (length(replace(s.org_code, '|', '')) ), 0     ) " +
                        "- FORMAT(      (length(p.org_code)) - (length(replace(p.org_code, '|', '')) ), 0     )+1  level")
                .setRsType(LevelManager.class)
                .whereEq("memp.is_delete", Boolean.FALSE.toString())
                .whereEq("s.company_id", companyId.getId())
                .whereInReq("s.org_id", atOrgIds)
                .orderBy(" level ");
        List<LevelManager> levelManagers = dao.listAll(comQB);
        log.info("查找出来的主管们：{}", JSON.toJSONString(levelManagers));

        // 动态添加条件
        if (checkDeptStatus) {
            comQB.appendOn("and p.status='valid'");
        }
        if (checkEmpStatus) {
            comQB.whereEq("memp.status", EmployeeStatus.ON_THE_JOB);
        }

        return dao.listAll(comQB);
    }

    public List<String> listAtOrgIds(TenantId companyId, EmpId empId) {
        ComQB refOrgQ = ComQB.build(EmpRefOrgDo.class, "ref")
                .join(EmpOrganizationDo.class, "s")
                .appendOn("s.company_id = ref.company_id and s.org_id = ref.org_id and ref.ref_type = 'org' and s.status='valid'")
                .clearSelect().select("ref.org_id,ref.company_id").setRsType(EmpRefOrgDo.class)
                .whereEq("ref.company_id", companyId.getId())
                .whereEq("ref.emp_id", empId.getId());
        List<EmpRefOrgDo> refDos = dao.listAll(refOrgQ);
        if (CollUtil.isEmpty(refDos)) {
            return Collections.emptyList();
        }
        return refDos.stream().map(empRefOrgDo -> empRefOrgDo.getOrgId()).collect(Collectors.toList());
    }

    /**
     * 获取员工所属部门ID列表
     * @param companyId 公司ID
     * @param empId 员工ID
     * @param checkOrgStatus 是否检查部门状态为valid
     * @return 部门ID列表
     */
    public List<String> listAtOrgIds(TenantId companyId, EmpId empId, boolean checkOrgStatus) {
        ComQB refOrgQ = ComQB.build(EmpRefOrgDo.class, "ref")
                .join(EmpOrganizationDo.class, "s")
                .appendOn("s.company_id = ref.company_id and s.org_id = ref.org_id and ref.ref_type = 'org'")
                .clearSelect()
                .select("ref.org_id, ref.company_id")
                .setRsType(EmpRefOrgDo.class)
                .whereEq("ref.company_id", companyId.getId())
                .whereEq("ref.emp_id", empId.getId());

        // 动态添加部门状态条件
        if (checkOrgStatus) {
            refOrgQ.appendOn("and s.status='valid'");
        }

        List<EmpRefOrgDo> refDos = dao.listAll(refOrgQ);
        return refDos.stream()
                .map(EmpRefOrgDo::getOrgId)
                .collect(Collectors.toList());
    }


    public List<EmpRefOrgDo> listAtOrgIds(TenantId companyId, List<String> empIds) {
        ComQB refOrgQ = ComQB.build(EmpRefOrgDo.class, "ref")
                .join(EmpOrganizationDo.class, "s")
                .appendOn("s.company_id = ref.company_id and s.org_id = ref.org_id and ref.ref_type = 'org' and s.status='valid'")
                .clearSelect().select("ref.org_id,ref.company_id,ref.emp_id").setRsType(EmpRefOrgDo.class)
                .whereEq("ref.company_id", companyId.getId())
                .whereInReq("ref.emp_id", empIds);
        List<EmpRefOrgDo> refDos = dao.listAll(refOrgQ);
        return refDos;
    }

    public ListWrap<EmpRefOrg> listAtOrgWrap(TenantId companyId, List<String> empIds) {
        ComQB refOrgQ = ComQB.build(EmpRefOrgDo.class, "ref")
                .join(EmpOrganizationDo.class, "s")
                .appendOn("s.company_id = ref.company_id and s.org_id = ref.org_id and ref.ref_type = 'org' and s.status='valid'")
                .clearSelect().select("ref.org_id,ref.company_id,ref.emp_id,s.org_id,s.org_name,s.org_code").setRsType(EmpRefOrg.class)
                .whereEq("ref.company_id", companyId.getId())
                .whereInReq("ref.emp_id", empIds);
        List<EmpRefOrg> refOrgs = dao.listAll(refOrgQ);
        return new ListWrap<>(refOrgs).groupBy(EmpRefOrg::getEmpId);
    }


    public List<KpiDept> list3OrgNames(TenantId tenantId, List<String> orgIds) {
        final ComQB qb = ComQB.build(EmpOrganizationDo.class, "o")
                .clearSelect().select("org_name,org_id")
                .setRsType(KpiDept.class)
                .whereIn("org_id", orgIds)
                .whereEq("company_id", tenantId.getId())
                .appendWhere("status='valid'")
                .limit(0, 3);
        return dao.listAllDomain(qb, KpiDept.class);
    }

    public List<KVPair> listOrgName(TenantId tenantId, List<String> orgIds) {
        final ComQB qb = ComQB.build(EmpOrganizationDo.class, "o")
                .clearSelect().select("org_name name,org_id id")
                .setRsType(KVPair.class)
                .whereIn("org_id", orgIds)
                .whereEq("company_id", tenantId.getId())
                .appendWhere("status='valid'");
        return dao.listAll(qb);
    }

    public List<KpiDept> listOrgs(TenantId tenantId, List<String> orgIds) {
        final ComQB qb = ComQB.build(EmpOrganizationDo.class, "o")
                .clearSelect().select("org_name,org_id, ding_org_id  ext_dept_id ")
                .setRsType(KpiDept.class)
                .whereIn("org_id", orgIds)
                .whereEq("company_id", tenantId.getId())
                .appendWhere("status='valid'");
        return dao.listAllDomain(qb, KpiDept.class);
    }

    public List<EmpStaff> loadStaff(TenantId companyId, EvaluationStaff raterStaff) {
        return listEmpByStaffConfItem(companyId, raterStaff);
    }

    public List<String> listOrgCodes(String empId, String companyId) {
        log.info("empId =" + empId, "companyId = " + companyId);
        final ComQB empQb = ComQB.build(EmpRefOrgDo.class, "o")
                .clearSelect()
                .select(" org_id ")
                .setRsType(String.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("emp_id", empId);
        List<String> orgIds = dao.listAll(empQb);
        if (orgIds.isEmpty()) {
            return new ArrayList<>();
        }
        final ComQB orgQb = ComQB.build(EmpOrganizationDo.class, "o")
                .clearSelect()
                .select(" org_code ")
                .setRsType(String.class)
                .whereEq("company_id", companyId)
                .whereIn("org_id", orgIds);
        return dao.listAll(orgQb);
    }

    public TaskUserOrg getTaskUserOrg(String[] orgIds, String companyId) {
        final ComQB orgQb = ComQB.build(EmpOrganizationDo.class, "o")
                .clearSelect()
                .select(" GROUP_CONCAT( org_id ORDER by org_code SEPARATOR '/') as orgIds, " +
                        " GROUP_CONCAT( org_name ORDER by org_code SEPARATOR '/') as orgNames, type")
                .setRsType(TaskUserOrg.class)
                .whereEq("company_id", companyId)
                .whereIn("org_id", Arrays.asList(orgIds));
        return dao.findOne(orgQb);
    }


    public CurOrgStructPo listChild1OrgAndEmpR(KpiEmpQuery query) {
        ComQB comQB = ComQB.build(EmpOrganizationDo.class)
                .clearSelect().select("org_id,ding_org_id,org_name,org_code ")
                .select("distinct_total_emp_cnt as emp_cnt,direct_child_org_cnt as l1_child_cnt")
                .setRsType(CurOrgStructPo.Org.class)
                .whereEq("company_id", query.getTenantId().getId())
                .whereEq("status", "valid");
        if (StrUtil.isBlank(query.getOrgId())) {
            comQB.appendWhere("parent_org_id is null");
        } else {
            comQB.whereEq("parent_org_id", query.getOrgId());
        }
        List<CurOrgStructPo.Org> childOrgs = dao.listAll(comQB);
        String curOrgId = StrUtil.isBlank(query.getOrgId()) ? childOrgs.get(0).getOrgId() : query.getOrgId();

        //if (onlySeeEnable) {
        //    comQB.whereEq("e.status", EmployeeStatus.ON_THE_JOB);
        //}

        ComQB empComQB = ComQB.build(EmpOrganizationDo.class, "eo")
                .join(EmpRefOrgDo.class, "ero")
                .appendOn("eo.company_id = ero.company_id and eo.org_id = ero.org_id")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn("ero.company_id = e.company_id and ero.emp_id = e.employee_id")
                .leftJoin(AdminSetDo.class, "a")
                .appendOn("e.company_id=a.company_id and e.employee_id=a.emp_id and a.status = 'valid'")
                .clearSelect().select("eo.org_id,eo.org_name,eo.ding_org_id,e.employee_id as empId,e.name as empName,e.status,e.ding_user_id,e.avatar,a.admin_type")
                .select("e.on_the_job_status,e.post_rank,e.type,e.entry_date,e.is_resigned")
                .setRsType(CurOrgStructPo.Emp.class)
                .whereEq("eo.status", "valid")
                .whereEq("e.status", "on_the_job")
                .whereEq("e.is_delete", Boolean.FALSE.toString())
                .whereEq("eo.org_id", curOrgId)
                .whereEq("ero.ref_type", "org")
                .whereEq("e.company_id", query.getTenantId().getId())
                .groupBy("ero.org_id, e.employee_id");
        if (StrUtil.isNotBlank(query.getType())) {
            empComQB.whereEq("e.type", query.getType());
        }
        buildEmployeeStatusWhere(query.getOnTheJobStatus(), query.getEmpStatus(), "e", empComQB);
        appendWhereEntryDate(empComQB, query.getEntryDateStart(), query.getEntryDateEnd());
        List<CurOrgStructPo.Emp> emps = dao.listAll(empComQB);
        return new CurOrgStructPo(emps, childOrgs);
    }

    /**
     * 构建员工状态查询条件
     *
     * @param onTheJobStatus 在职状态 (1: 在职, 其他值)
     * @param empStatus      员工状态 (如 "resigned" 表示离职)
     * @param tableAlias     表别名 (如 "e", "emp" 等)
     * @param comQB          ComQB 查询构建器
     */
    private void buildEmployeeStatusWhere(Integer onTheJobStatus, String empStatus, String tableAlias, ComQB comQB) {
        boolean hasOnTheJobStatus = Objects.nonNull(onTheJobStatus);
        boolean hasEmpStatus = StrUtil.isNotBlank(empStatus);

        if (hasOnTheJobStatus && !hasEmpStatus) {
            comQB.whereEq(tableAlias + ".on_the_job_status", onTheJobStatus);
        } else if (hasOnTheJobStatus) {
            comQB.appendWhere(" (" + tableAlias + ".on_the_job_status = " + onTheJobStatus +
                    " OR " + tableAlias + ".is_resigned in " + StringTool.getInStrSql(toResignedValue(empStatus)) + "')");
        } else if (hasEmpStatus) {
            comQB.appendWhere(tableAlias + ".is_resigned in " + StringTool.getInStrSql(toResignedValue(empStatus)));
        }
    }

    /**
     * 将 empStatus 转换为 is_resigned 字段值（字符串）
     * "on_the_job" => "0"，"leave" => "1"
     */
    private List<String> toResignedValue(String empStatus) {
        return "leave".equalsIgnoreCase(empStatus) ? Arrays.asList("1", "-1") : Arrays.asList("0");
    }

    @Deprecated
    public CurOrgStructPo listChild1OrgAndEmp(KpiEmpQuery query) {
        String companyId = query.getTenantId().getId();
        if (StringUtils.isBlank(query.getOrgId())) {
            return rootOrg(query.getTenantId(), query.getOnlySeeEnable());
        }
        ComQB empComQB = ComQB.build(EmpOrganizationDo.class, "eo")
                .join(EmpRefOrgDo.class, "ero")
                .appendOn("eo.company_id = ero.company_id and eo.org_id = ero.org_id")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn("ero.company_id = e.company_id and ero.emp_id = e.employee_id")
                .leftJoin(AdminSetDo.class, "a")
                .appendOn("e.company_id=a.company_id and e.employee_id=a.emp_id and a.status = 'valid'")
                .clearSelect().select("eo.org_id,eo.org_name,eo.ding_org_id,e.employee_id as empId,e.name as empName,e.status,e.ding_user_id,e.avatar,a.admin_type")
                .select("e.on_the_job_status,e.post_rank,e.type,e.entry_date")
                .setRsType(CurOrgStructPo.Emp.class)
                .whereEq("eo.status", "valid")
                .whereEq("e.is_delete", Boolean.FALSE.toString())
                .whereEq("eo.org_id", query.getOrgId())
                .whereEq("ero.ref_type", "org")
                .whereEq("e.company_id", companyId)
                .groupBy("ero.org_id, e.employee_id");
        buildEmployeeStatusWhere(query.getOnTheJobStatus(), query.getEmpStatus(), "e", empComQB);
        if (StrUtil.isNotBlank(query.getType())) {
            empComQB.whereEq("e.type", query.getType());
        }
        appendWhereEntryDate(empComQB, query.getEntryDateStart(), query.getEntryDateEnd());
        List<CurOrgStructPo.Emp> emps = dao.listAll(empComQB);

        ComQB orgComQB = ComQB.build(EmpOrganizationDo.class)
                .clearSelect().select("org_id,org_name,ding_org_id").setRsType(CurOrgStructPo.Org.class)
                .whereEq("company_id", companyId)
                .whereEq("status", "valid");
        if (StringUtils.isBlank(query.getOrgId())) {
            ComQB comQB = ComQB.build(EmpOrganizationDo.class)
                    .whereEq("company_id", companyId)
                    .whereEq("type", EmpOrganizationModel.ROOT)
                    .whereEq("status", "valid");
            EmpOrganizationDo rootOrg = dao.findOne(comQB);
            orgComQB.whereEq("parent_org_id", rootOrg.getOrgId());
        } else {
            orgComQB.whereEq("parent_org_id", query.getOrgId());
        }
        List<CurOrgStructPo.Org> orgs = dao.listAll(orgComQB);
        if (CollUtil.isEmpty(orgs)) {
            return new CurOrgStructPo(emps, orgs);
        }
        orgEmpCnt(new TenantId(companyId), query.getOnlySeeEnable(), orgs);
        return new CurOrgStructPo(emps, orgs);
    }

    private void appendWhereEntryDate(ComQB qb1, String entryDateStart, String entryDateEnd) {
        if (StrUtil.isNotBlank(entryDateStart) && StrUtil.isNotBlank(entryDateEnd)) {
            qb1.appendWhere("e.entry_date BETWEEN '" + entryDateStart + "' and '" + entryDateEnd + "'");
            return;
        }

        if (StrUtil.isNotBlank(entryDateStart)) {
            qb1.whereBigEqReq("e.entry_date", entryDateStart);
            return;
        }
        if (StrUtil.isNotBlank(entryDateEnd)) {
            qb1.whereLowEqReq("e.entry_date", entryDateEnd);
        }
    }

    private void orgEmpCnt(TenantId companyId, Boolean onlySeeEnable, List<CurOrgStructPo.Org> orgs) {
        List<String> childOrgIds = orgs.stream().map(org -> org.getOrgId()).distinct().collect(Collectors.toList());
        // 递归统计子部门人数
        ComQB cntEmpQ = ComQB.build(EmpRefOrgDo.class, "ref")
                .join(EmployeeBaseInfoDo.class, "ebi").appendOn("ref.company_id = ebi.company_id and ebi.employee_id = ref.emp_id")
                .join(EmpOrganizationDo.class, "s").appendOn("ref.company_id = s.company_id  and  s.org_id =ref.org_id")
                .join(EmpOrganizationDo.class, "p").appendOn("s.company_id=p.company_id and instr(s.org_code,p.org_code) ")
                .clearSelect().select(" p.org_id,count(ebi.employee_id) emp_cnt")
                .setRsType(CurOrgStructPo.Org.class)
                .whereIn(" p.org_id", childOrgIds)
                .appendWhere("ref.ref_type='org'")
                .whereEq("ref.companyId", companyId.getId());
        if (onlySeeEnable) {
            cntEmpQ.whereEq("ebi.status", EmployeeStatus.ON_THE_JOB);
        }
        cntEmpQ.appendWhere("ebi.is_delete='false'")
                .appendWhere("s.status='valid'")
                .groupBy(" p.org_id");
        List<CurOrgStructPo.Org> empCntAtOrgs = dao.listAll(cntEmpQ);
        Map<String, CurOrgStructPo.Org> cntMap = empCntAtOrgs.stream().collect(Collectors.toMap(CurOrgStructPo.Org::getOrgId, v -> v));
        // 统计子部门数
        ComQB childOrgCntQ = ComQB.build(EmpOrganizationDo.class, "s")
                .join(EmpOrganizationDo.class, "p").appendOn("s.parent_org_id = p.org_id and s.company_id=p.company_id")
                .clearSelect().select(" p.org_id,count(1) l1_child_cnt")
                .setRsType(CurOrgStructPo.Org.class)
                .whereIn(" p.org_id", childOrgIds)
                .appendWhere("s.status='valid'")
                .groupBy(" p.org_id");
        final List<CurOrgStructPo.Org> childOrgCnts = dao.listAll(childOrgCntQ);
        Map<String, CurOrgStructPo.Org> childOrgCntMap = childOrgCnts.stream().collect(Collectors.toMap(CurOrgStructPo.Org::getOrgId, v -> v));
        orgs.forEach(org -> {
            org.updateEmpCnt(cntMap.get(org.getOrgId()));
            org.updateL1Child(childOrgCntMap.get(org.getOrgId()));
        });
    }

    private CurOrgStructPo rootOrg(TenantId tenantId, Boolean onlySeeEnable) {
        ComQB comQB = ComQB.build(EmpOrganizationDo.class)
                .clearSelect().select("org_id,ding_org_id,org_name").setRsType(CurOrgStructPo.Org.class)
                .whereEq("company_id", tenantId.getId())
                .whereEq("status", "valid")
                .appendWhere("parent_org_id is null");
        CurOrgStructPo.Org rootOrg = dao.findOne(comQB);
        if (Objects.isNull(rootOrg)) {
            return null;
        }
        //统计人数
        ComQB empCntQb = ComQB.build(EmployeeBaseInfoDo.class, "e")
                .join(EmpRefOrgDo.class, "ero")
                .appendOn("e.company_id=ero.company_id and e.employee_id=ero.emp_id")
                .clearSelect().select("count(1)").setRsType(Integer.class)
                .whereEq("e.company_id", tenantId.getId())
                .whereEq("e.is_delete", Boolean.FALSE.toString());
        if (onlySeeEnable) {
            empCntQb.whereEq("e.status", EmployeeStatus.ON_THE_JOB);
        }
        empCntQb.whereEq("ero.ref_type", "org");
        Integer empCnt = dao.findOne(empCntQb);
        //下级部门数
        ComQB childOrgCntQb = ComQB.build(EmpOrganizationDo.class)
                .clearSelect().select(" count(1)").setRsType(Integer.class)
                .whereEq("company_id", tenantId.getId())
                .whereEq("status", "valid")
                .whereEq("parent_org_id", rootOrg.getOrgId());
        Integer childOrgCnt = dao.findOne(childOrgCntQb);
        rootOrg.setEmpCnt(empCnt);
        rootOrg.setL1ChildCnt(childOrgCnt);
        return new CurOrgStructPo(Arrays.asList(rootOrg));
    }

    //查询所有子级部门Id
    public List<String> listAllChildOrgIds(TenantId tenantId, List<String> orgIds) {
        List<ObjItem> items = listAllChildOrg(tenantId, orgIds);
        if (CollUtil.isEmpty(items)) {
            return new ArrayList<>();
        }
        return items.stream().map(item -> item.getObjId()).distinct().collect(Collectors.toList());
    }

    // 查询所有子级部门Id
    public List<String> listAllChildOrgIds(TenantId tenantId, List<String> orgIds, String status) {
        List<ObjItem> items = listAllChildOrg(tenantId, orgIds, status);
        if (CollUtil.isEmpty(items)) {
            return new ArrayList<>();
        }
        return items.stream().map(item -> item.getObjId()).distinct().collect(Collectors.toList());
    }

    public List<String> listOrg(TenantId tenantId, List<String> orgNames) {
        ComQB comQB = ComQB.build(EmpOrganizationDo.class)
                .clearSelect().select("org_id").setRsType(String.class)
                .whereEq("company_id", tenantId.getId())
                .whereEq("status", "valid")
                .whereIn("org_name", orgNames);
        return dao.listAll(comQB);
    }

    public Map<String, EmpOrganizationDo> listOrg(String companyId, List<String> orgIds) {
        ComQB comQB = ComQB.build(EmpOrganizationDo.class)
                .clearSelect().select("org_id,ding_org_id")
                .whereEqReq("company_id", companyId)
                .whereEq("status", "valid")
                .whereIn("org_id", orgIds);
        List<EmpOrganizationDo> orgs = dao.listAll(comQB);
        if (CollUtil.isEmpty(orgs)) {
            return new HashMap<>();
        }
        return CollUtil.toMap(orgs, new HashMap<>(), r -> r.getOrgId());
    }

    public List<KpiDeptCode> listOrgCode(TenantId tenantId, List<String> orgIds) {
        return this.listOrgCode(tenantId, orgIds, "valid");
    }

    public List<KpiDeptCode> listOrgCode(TenantId tenantId, List<String> orgIds, String status) {
        ComQB comQB = ComQB.build(EmpOrganizationDo.class)
                .clearSelect().select("org_code,org_id").setRsType(KpiDeptCode.class)
                .whereEq("company_id", tenantId.getId())
                .whereEq("status", status)
                .whereIn("org_id", orgIds);
        return dao.listAll(comQB);
    }

    public KpiDeptCode getOrgCode(TenantId tenantId, String orgId) {
        ComQB comQB = ComQB.build(EmpOrganizationDo.class)
                .clearSelect().select("org_code,org_id").setRsType(KpiDeptCode.class)
                .whereEq("company_id", tenantId.getId())
                .whereEq("status", "valid")
                .whereEq("org_id", orgId);
        return dao.findOne(comQB);
    }


    //查询所有子级部门
    public List<ObjItem> listAllChildOrg(TenantId tenantId, List<String> orgIds) {
        if (CollUtil.isEmpty(orgIds)) {
            return new ArrayList<>();
        }
        ComQB orgComQb = ComQB.build(EmpOrganizationDo.class, "s")
                .join(EmpOrganizationDo.class, "p")
                .appendOn("s.company_id=p.company_id and instr(s.org_code,p.org_code) ")
                .clearSelect().select("s.org_id as objId,s.org_name as objName")
                .setRsType(ObjItem.class)
                .whereIn(" p.org_id", orgIds)
                .whereEq("p.company_id", tenantId.getId())
                .appendWhere("s.status='valid'")
                .groupBy(" s.org_id");
        return dao.listAll(orgComQb);
    }

    // 查询所有子级部门
    public List<ObjItem> listAllChildOrg(TenantId tenantId, List<String> orgIds, String status) {
        if (CollUtil.isEmpty(orgIds)) {
            return new ArrayList<>();
        }
        ComQB orgComQb = ComQB.build(EmpOrganizationDo.class, "s")
                .join(EmpOrganizationDo.class, "p")
                .appendOn("s.company_id=p.company_id and instr(s.org_code,p.org_code) ")
                .clearSelect().select("s.org_id as objId,s.org_name as objName")
                .setRsType(ObjItem.class)
                .whereIn(" p.org_id", orgIds)
                .whereEq("p.company_id", tenantId.getId())
                .whereEq("s.status", status)
                .groupBy(" s.org_id");
        return dao.listAll(orgComQb);
    }

    public boolean openIgnoreVacancyManager(String companyId) {
        ComQB comQB = ComQB.build(CompanyDo.class)
                .clearSelect().select(" ignore_vacancy_manager ")
                .setRsType(String.class)
                .whereEqReq("id", companyId);
        return "1".equals(dao.findOne(comQB));
    }

    public KpiEmployee findEmployee(TenantId companyId, String empId) {
        ComQB comQB = ComQB.build(EmployeeBaseInfoDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("employee_id", empId)
                .limit(0, 1);
        return dao.findDomain(comQB, KpiEmployee.class);
    }

    public List<KpiEmployee> listKpiEmployee(TenantId companyId, List<String> empIds) {
        ComQB comQB = ComQB.build(EmployeeBaseInfoDo.class)
                .clearSelect().select(" employee_id ,  name , avatar ")
                .setRsType(KpiEmployee.class)
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("employee_id", empIds);
        return dao.listAll(comQB);
    }

    public List<KpiEmp> listKpiEmployee(TenantId companyId) {
        ComQB comQB = ComQB.build(EmployeeBaseInfoDo.class)
                .clearSelect().select(" employee_id  emp_id,  name   emp_name, ding_user_id ex_user_id")
                .setRsType(KpiEmp.class)
                .whereEqReq("company_id", companyId.getId());
        return dao.listAll(comQB);
    }

    public List<OpEmpEval> listOpEmpEval(TenantId companyId, List<String> empIds) {
        ComQB comQB = ComQB.build(EmployeeBaseInfoDo.class)
                .clearSelect().select("employee_id empId, name empName, avatar")
                .setRsType(OpEmpEval.class)
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("employee_id", empIds);
        return dao.listAll(comQB);
    }

    @Override
    public SimpleEmpInfo getSimpleEmpInfo(String companyId, String empId) {
        ComQB<SimpleEmpInfo> comQB = ComQB.build(EmployeeBaseInfoDo.class)
                .clearSelect()
                .select("employee_id as empId, name as empName,ding_manager_id as dingManagerId")
                .setRsType(SimpleEmpInfo.class)
                .whereEq("company_id", companyId)
                .whereEq("employee_id", empId)
                .whereEq("status", "on_the_job")
                .whereEq("is_delete", "false");
        return dao.findOne(comQB);
    }

    @Override
    public SimpleEmpInfo getSimpleEmpInfoByDingUserId(String companyId, String dingManagerId) {
        ComQB<SimpleEmpInfo> comQB = ComQB.build(EmployeeBaseInfoDo.class)
                .clearSelect()
                .select("employee_id as empId, name as empName,ding_manager_id as dingManagerId")
                .setRsType(SimpleEmpInfo.class)
                .whereEq("company_id", companyId)
                .whereEq("ding_user_id", dingManagerId)
                .whereEq("status", "on_the_job")
                .whereEq("is_delete", "false");
        return dao.findOne(comQB);
    }

    public List<KpiEmp> listEmployee(TenantId companyId) {
        ComQB comQB = ComQB.build(EmployeeBaseInfoDo.class)
                .clearSelect().select(" employee_id  emp_id,  name   emp_name, ding_user_id ex_user_id")
                .setRsType(KpiEmp.class)
                .whereEqReq("company_id", companyId.getId());
        return dao.listAll(comQB);
    }

    public List<KpiDeptTran> listOrgs(TenantId tenantId) {
        final ComQB qb = ComQB.build(EmpOrganizationDo.class, "o")
                .clearSelect().select("org_name,org_id, ding_org_id  ext_dept_id ,parent_org_id")
                .setRsType(KpiDeptTran.class)
                .whereEq("company_id", tenantId.getId());
        return dao.listAll(qb);
    }

    public PagedList<CurOrgStructPo.Org> pagedPrivOrg(PrivOrgQuery query) {
        if (query.isCompanyScope()) {
            ComQB comQB = ComQB.build(EmpOrganizationDo.class, "e")
                    .leftJoin(EmpOrganizationDo.class, "e2")
                    .appendOn("e.org_id = e2.parent_org_id and e.company_id = e2.company_id and e2.status = 'valid'")
                    .clearSelect()
                    .select("e.org_id, e.org_name, e.ding_org_id, count(e2.id) as l1_child_cnt")
                    .setRsType(CurOrgStructPo.Org.class)
                    .whereEq("e.status", "valid")
                    .whereEqReq("e.company_id", query.getCompanyId().getId());
            if (query.needLikeSql()) {
                comQB.and().startPt().whereLike(" e.name_chinese ", query.getKeyword()).endPt();
            } else if (query.wasRootOrgId()) {
                comQB.whereEq("e.type", "root");
            } else {
                comQB.whereEqReq("e.parent_org_id", query.getOrgId());
            }
            comQB.groupBy("e.org_id")
                    .setPage(query.getPageNo(), query.getPageSize());
            return dao.listPage(comQB);
        }
        //只有部分或者没有部门权限 ,模糊匹配 部门拉平
        if (query.needLikeSql()) {
            ComQB comQB = ComQB.build(AdminManageOrgDo.class, "a")
                    .join(EmpOrganizationDo.class, "o")
                    .appendOn("a.company_id = o.company_id and a.org_id = o.org_id and a.admin_emp_id = '" + query.getAdminId() + "'")
                    .leftJoin(EmpOrganizationDo.class, "s")
                    .appendOn("s.parent_org_id = o.org_id and s.company_id = o.company_id and s.status='valid'")
                    .clearSelect()
                    .select("o.org_id,o.org_name,o.ding_org_id,count(s.id) as l1_child_cnt")
                    .setRsType(CurOrgStructPo.Org.class)
                    .whereEqReq("o.company_id", query.getCompanyId().getId())
                    .whereEq("o.status", "valid")
                    .and().startPt().whereLike(" o.name_chinese ", query.getKeyword()).endPt()
                    .groupBy("o.org_id")
                    .setPage(query.getPageNo(), query.getPageSize());
            return dao.listPage(comQB);
        }
        //只有部分或者没有部门权限 部门树
        ComQB comQB = ComQB.build(AdminManageOrgDo.class, "b")
                .join(EmpOrganizationDo.class, "s")
                .appendOn("b.company_id = s.company_id and b.org_id = s.org_id and b.admin_emp_id='" + query.getAdminId() + "'")
                .leftJoin(AdminManageOrgDo.class, "p")
                .appendOn("p.org_id = s.parent_org_id and p.company_id = s.company_id and p.admin_emp_id='" + query.getAdminId() + "'")
                //连这个表，是查这个部门下还有多个直属下级有多少部门数量
                .leftJoin(EmpOrganizationDo.class, "s2")
                .appendOn("s2.company_id=b.company_id and s2.parent_org_id=b.org_id and s2.status='valid'")
                .clearSelect()
                .select("s.org_id,s.org_name,s.ding_org_id,count(s2.id) as l1_child_cnt")
                .setRsType(CurOrgStructPo.Org.class)
                .whereEqReq("s.company_id", query.getCompanyId().getId());
        if (query.wasRootOrgId()) {
            comQB.appendWhere("p.org_id is null");
        } else {
            comQB.whereEqReq("s.parent_org_id", query.getOrgId());
        }
        comQB.groupBy("s.org_id")
                .setPage(query.getPageNo(), query.getPageSize());
        return dao.listPage(comQB);
    }

    public List<ShowSupOrgPo> showSupOrg(TenantId tenantId, List<ShowSupOrg> supOrgs) {
        List<KpiDeptCode> codes = listOrgCode(tenantId, supOrgs.stream().map(s -> s.getOrgId()).collect(Collectors.toList()));
        Set<String> orgCodeAsIds = new HashSet<>();
        for (KpiDeptCode code : codes) {
            orgCodeAsIds.addAll(Arrays.asList(code.getOrgCode().split("\\|")));
        }
        ComQB comQB = ComQB.build(EmpOrganizationDo.class)
                .whereEq("status", "valid")
                .whereInReq("org_id", orgCodeAsIds);
        List<EmpOrganizationDo> codeOrgs = dao.listAll(comQB);
        ListWrap<EmpOrganizationDo> codeOrgWarp = new ListWrap<>(codeOrgs).groupBy(EmpOrganizationDo::getOrgId);

        for (KpiDeptCode code : codes) {
            StringBuilder names = new StringBuilder();
            for (String codeId : Arrays.asList(code.getOrgCode().split("\\|"))) {
                List<EmpOrganizationDo> orgs = codeOrgWarp.groupGet(codeId);
                if (CollUtil.isEmpty(orgs)) {
                    continue;
                }
                names.append(orgs.get(0).getOrgName()).append("/");
            }
            code.setOrgCode(names.substring(0, names.length() - 1));
        }

        ListWrap<KpiDeptCode> deptWrap = new ListWrap<>(codes).groupBy(KpiDeptCode::getOrgId);
        List<ShowSupOrgPo> showSupOrgPos = new ArrayList<>();
        for (ShowSupOrg supOrg : supOrgs) {
            List<KpiDeptCode> kpiDeptCodes = deptWrap.groupGet(supOrg.getOrgId());
            if (CollUtil.isEmpty(kpiDeptCodes)) {
                continue;
            }
            ShowSupOrgPo showSupOrgPo = new ShowSupOrgPo(supOrg.getTaskUserId(), kpiDeptCodes.get(0).getOrgCode());
            showSupOrgPos.add(showSupOrgPo);
        }
        return showSupOrgPos;
    }

    @Override
    public boolean atOrgManager(TenantId tenantId, String evalOrgId, String empId) {
        ComQB comQB = ComQB.build(EmpRefOrgDo.class)
                .clearSelect().select("count(1)").setRsType(int.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("org_id", evalOrgId)
                .whereEqReq("emp_id", empId)
                .whereEq("ref_type", "manager");
        int cnt = dao.findOne(comQB);
        return cnt > 0;
    }

    @Override
    public List<EmpStaff> listEmpByDeptId(TenantId companyId, String orgId, String evalOrgId, String evalEmpId, boolean hasExcludeAllManager) {
        log.info("部门ID={}", orgId);
        List<ObjItem> orgs = listAllChildOrg(companyId, Arrays.asList(orgId));
        if (CollUtil.isEmpty(orgs)) {
            return new ArrayList<>();
        }
        List<String> filterEmpIds = new ArrayList<>();
        ComQB orgQ = ComQB.build(EmpRefOrgDo.class, "eo")
                .leftJoin(EmployeeBaseInfoDo.class, "e").appendOn("eo.company_id = e.company_id and eo.emp_id = e.employee_id")
                .leftJoin(EmpOrganizationDo.class, "o").appendOn("eo.company_id = o.company_id and eo.org_id = o.org_id")
                .clearSelect().select("e.employee_id as empId,name as empName,o.org_id as orgId,o.org_name as orgName").setRsType(EmpStaff.class)
                .whereEq("eo.company_id", companyId.getId())
                .whereEq("eo.ref_type", "org")
                .appendWhere("e.is_delete = 'false' and e.status = 'on_the_job'")
                .whereInReq("eo.org_id", CollUtil.map(orgs, o -> o.getObjId(), true));
        List<EmpStaff> raters = dao.listAll(orgQ);
        //排除被考核人
        TenantSysConf sysConf = tenantSysConfDao.findTenantConf(companyId.getId(), "self_as_peer_20240113", 0);
        if (!sysConf.isOpen()) {
            raters = raters.stream().filter(e -> !evalEmpId.equals(e.getEmpId())).collect(Collectors.toList());
        }
        if (BetaCompanys.closeCheckManagers.contains(companyId.getId())) {
            return raters;
        }
        log.info("部门员工={}", JSONUtil.toJsonStr(raters));
        //不排除所有部门主管 只排除直属主管以及汇报线上
        //直属主管
        LevelManager directMg = findDirectMgEmpId(companyId, evalEmpId);
        if (Objects.nonNull(directMg)) {
            log.info("直属主管ID={}", JSONUtil.toJsonStr(directMg.getManagerId()));
            filterEmpIds.add(directMg.getManagerId());
        }
        if (!hasExcludeAllManager) {
            //查询部门主管链路
            List<LevelManager> levelManagers = listAtOrgManager(companyId, Arrays.asList(evalOrgId));
            log.info("得到的主管们={}", JSONUtil.toJsonStr(levelManagers));
            //直属主管
            if (CollUtil.isNotEmpty(levelManagers)) {
                filterEmpIds.addAll(CollUtil.map(levelManagers, level -> level.getManagerId(), true));
                log.info("汇报线主管ID={}", JSONUtil.toJsonStr(filterEmpIds));
            }
            log.info("排除汇报线主管后的人员={}", JSONUtil.toJsonStr(CollUtil.filterNew(raters, r -> !filterEmpIds.contains(r.getEmpId()))));
            return CollUtil.filterNew(raters, r -> !filterEmpIds.contains(r.getEmpId()));
        }

        ComQB orgPrivQB = ComQB.build(EmpRefOrgDo.class, "eo")
                .clearSelect().select("DISTINCT eo.emp_id").setRsType(String.class)
                .whereEq("eo.company_id", companyId.getId())
                .whereEq("eo.ref_type", "manager")
                .whereInReq("eo.org_id", CollUtil.map(orgs, o -> o.getObjId(), true));
        List<String> managerEmpIds = dao.listAll(orgPrivQB);
        if (CollUtil.isNotEmpty(managerEmpIds)) {
            filterEmpIds.addAll(managerEmpIds);
        }
        return CollUtil.filterNew(raters, r -> !filterEmpIds.contains(r.getEmpId()));
    }

    public List<EmpStaff> listEmpByDeptId(TenantId companyId, String orgId) {
        log.info("部门ID={}", orgId);
        List<ObjItem> orgs = listAllChildOrg(companyId, Arrays.asList(orgId));
        if (CollUtil.isEmpty(orgs)) {
            return new ArrayList<>();
        }
        ComQB orgQ = ComQB.build(EmpRefOrgDo.class, "eo")
                .leftJoin(EmployeeBaseInfoDo.class, "e").appendOn("eo.company_id = e.company_id and eo.emp_id = e.employee_id")
                .leftJoin(EmpOrganizationDo.class, "o").appendOn("eo.company_id = o.company_id and eo.org_id = o.org_id")
                .clearSelect().select("e.employee_id as empId,name as empName,o.org_id as orgId,o.org_name as orgName").setRsType(EmpStaff.class)
                .whereEq("eo.company_id", companyId.getId())
                .whereEq("eo.ref_type", "org")
                .appendWhere("e.is_delete = 'false' and e.status = 'on_the_job'")
                .whereInReq("eo.org_id", CollUtil.map(orgs, o -> o.getObjId(), true));
        List<EmpStaff> raters = dao.listAll(orgQ);
        return raters;
    }

    @Override
    public List<EmpStaff> listEmpByDeptLevel(TenantId companyId, String orgLevel, String orgId, String evalEmpId, boolean hasExcludeManager) {
        KpiDeptCode code = getOrgCode(companyId, orgId);
        if (Objects.isNull(code)) {
            return new ArrayList<>();
        }
        List<String> orgPaths = StrUtil.splitTrim(code.getOrgCode(), "|");
        if (Integer.valueOf(orgLevel) >= orgPaths.size()) {
            return new ArrayList<>();
        }
        if (Objects.equals(Integer.valueOf(orgLevel), 0)) {
            List<EmpStaff> list = listEmpByDeptId(companyId, orgPaths.get(orgPaths.size() - 1), orgId, evalEmpId, hasExcludeManager);
            //list去重根据empId
            return new ArrayList<>(list.stream().collect(Collectors.toMap(EmpStaff::getEmpId, emp -> emp, (e1, e2) -> e1)).values());
        }
        return listEmpByDeptId(companyId, orgPaths.get(Integer.valueOf(orgLevel)), orgId, evalEmpId, hasExcludeManager);
    }

    @Override
    public List<EmpStaff> listEmpByDeptLevel(TenantId companyId,String fromOrgId, Integer orgLevel) {
        KpiDeptCode code = getOrgCode(companyId, fromOrgId);
        if (Objects.isNull(code)) {
            return new ArrayList<>();
        }
        List<String> orgPaths = StrUtil.splitTrim(code.getOrgCode(), "|");
        if (Integer.valueOf(orgLevel) >= orgPaths.size()) {
            return new ArrayList<>();
        }
        if (Objects.equals(Integer.valueOf(orgLevel), 0)) {
            List<EmpStaff> list = listEmpByDeptId(companyId, orgPaths.get(orgPaths.size() - 1));
            //list去重根据empId
            return new ArrayList<>(list.stream().collect(Collectors.toMap(EmpStaff::getEmpId, emp -> emp, (e1, e2) -> e1)).values());
        }
        return this.listEmpByDeptId(companyId, orgPaths.get(orgLevel));
    }

    @Override
    public List<EmpStaff> listPeerOrgEmp(TenantId companyId, String opEmpId) {
        ComQB orgQ = ComQB.build(EmpRefOrgDo.class, "eo")
                .leftJoin(EmployeeBaseInfoDo.class, "e").appendOn("eo.company_id = e.company_id and eo.emp_id = e.employee_id")
                .leftJoin(EmpOrganizationDo.class, "o").appendOn("eo.company_id = o.company_id and eo.org_id = o.org_id")
                .clearSelect().select("e.employee_id as empId,name as empName,o.org_id as orgId,o.org_name as orgName").setRsType(EmpStaff.class)
                .whereEq("eo.company_id", companyId.getId())
                .whereEq("eo.ref_type", "org")
                .whereEqReq("eo.emp_id", opEmpId)
                .appendWhere("e.is_delete = 'false' and e.status = 'on_the_job'")
                .whereEq("o.status", "valid");
        return dao.listAll(orgQ);
    }

    @Override
    public List<EvalEmp> findEmpByOrgIds(TenantId companyId, List<String> orgIds) {
        ComQB comQB = ComQB.build(EmpOrganizationDo.class, "s")
                .join(EmpOrganizationDo.class, "p")
                .appendOn("s.company_id=p.company_id and instr(s.org_code,p.org_code) ")
                .clearSelect().select("s.org_id")
                .setRsType(String.class)
                .whereIn(" p.org_id", orgIds)
                .whereEqReq("p.company_id", companyId.getId())
                .appendWhere("s.status='valid'")
                .groupBy(" s.org_id");

        List<String> childOrgIds = dao.listAll(comQB);
        ComQB comQB1 = ComQB.build(EmployeeBaseInfoDo.class, "e")
                .join(EmpRefOrgDo.class, "o")
                .appendOn("e.company_id=o.company_id and e.employee_id=o.emp_id")
                .clearSelect()
                .select("DISTINCT e.employee_id as empId, o.org_id as atOrgId, e.name as empName")
                .setRsType(EvalEmp.class)
                .whereInReq("o.org_id", childOrgIds)
                .whereEqReq("e.company_id", companyId.getId());
        return dao.listAll(comQB1);
    }

    public List<KpiOrgSupNames> listOrgSupNames(TenantId tenantId, List<String> curOrgIds) {
        if (CollUtil.isEmpty(curOrgIds)) {
            return new ArrayList<>();
        }
        //获取这些部门的code
        List<KpiDeptCode> curOrgCodes = listOrgCode(tenantId, curOrgIds);
        if (CollUtil.isEmpty(curOrgCodes)) {
            return new ArrayList<>();
        }
        //转换为orgIds
        Set<String> codeToIds = new HashSet<>();

        curOrgCodes.forEach(c -> {
            List<String> splitOrgIds = Arrays.asList(c.getOrgCode().split("\\|"));
            if (CollUtil.isNotEmpty(splitOrgIds)) {
                codeToIds.addAll(splitOrgIds);
            }
        });
        ComQB comQB = ComQB.build(EmpOrganizationDo.class)
                .clearSelect().select("org_id,org_name")
                .whereEqReq("company_id", tenantId.getId())
                .whereInReq("org_id", codeToIds)
                .whereEq("status", "valid");
        List<EmpOrganizationDo> orgDos = dao.listAll(comQB);

        ListWrap<EmpOrganizationDo> orgWrap = new ListWrap<>(orgDos).groupBy(EmpOrganizationDo::getOrgId);
        List<KpiOrgSupNames> supNamesList = new ArrayList<>();
        //code 换成名字
        for (KpiDeptCode curOrgCode : curOrgCodes) {
            StringBuilder supNames = new StringBuilder();
            for (String code : Arrays.asList(curOrgCode.getOrgCode().split("\\|"))) {
                List<EmpOrganizationDo> dos = orgWrap.groupGet(code);
                if (CollUtil.isEmpty(dos)) {
                    continue;
                }
                supNames.append(dos.get(0).getOrgName()).append("/");
            }
            if (StrUtil.isBlank(supNames)) {
                continue;
            }
            KpiOrgSupNames names = new KpiOrgSupNames(curOrgCode.getOrgId(), supNames.substring(0, supNames.length() - 1));
            names.setExtOrgCode(curOrgCode.getOrgCode());
            supNamesList.add(names);
        }
        return supNamesList;
    }


    public KpiOrgSupNames getOrgPath(TenantId tenantId, String orgId) {
        ComQB codeQ = ComQB.build(EmpOrganizationDo.class)
                .clearSelect().select("org_code,org_id").setRsType(KpiOrgSupNames.class)
                .whereEq("company_id", tenantId.getId())
                //.whereEq("status", "valid")
                .whereEqReq("org_id", orgId);
        KpiOrgSupNames code = dao.findOne(codeQ);
        List<String> codeToIds = code.codeToIds();
        ComQB comQB = ComQB.build(EmpOrganizationDo.class)
                .clearSelect().select("org_id,org_name,ding_org_id ext_dept_id")
                .setRsType(KpiDept.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereInReq("org_id", codeToIds);
        //.whereEq("status", "valid");
        List<KpiDept> pathOrgs = dao.listAll(comQB);
        code.buildPath(pathOrgs);
        return code;
    }

    public List<LevelManager> listOrgEvalManager(TenantId tenantId, List<String> orgIds) {
        List<LevelManager> atOrgManager = listAtOrgManager(tenantId, orgIds);
        if (CollUtil.isEmpty(atOrgManager)) {
            return new ArrayList<>();
        }
        atOrgManager.removeIf(m -> "1".equals(String.valueOf(m.getLevel())));
        if (CollUtil.isEmpty(atOrgManager)) {
            return new ArrayList<>();
        }
        List<String> managerIds = atOrgManager.stream().map(m -> m.getManagerId()).collect(Collectors.toList());
        ComQB comQB = ComQB.build(EmployeeBaseInfoDo.class)
                .whereEq("is_delete", Boolean.FALSE.toString())
                .whereEqReq("company_id", tenantId.getId())
                .whereInReq("employee_id", managerIds);
        List<EmployeeBaseInfoDo> emps = dao.listAll(comQB);
        ListWrap<EmployeeBaseInfoDo> empWrap = new ListWrap<>(emps).groupBy(EmployeeBaseInfoDo::getEmployeeId);
        for (LevelManager manager : atOrgManager) {
            if (StrUtil.isEmpty(manager.getManagerId())) {
                continue;
            }
            List<EmployeeBaseInfoDo> empList = empWrap.groupGet(manager.getManagerId());
            if (CollUtil.isEmpty(empList)) {
                continue;
            }
            EmployeeBaseInfoDo emp = empList.get(0);
            manager.setManagerAvatar(emp.getAvatar());
            manager.subLevel();
        }
        return atOrgManager;
    }

    public CurOrgStructPo listChild1OrgAndEmpPriv(PrivOrgQuery query) {
        StringBuilder sb = new StringBuilder(" eo.company_id = o.company_id and eo.org_id = o.org_id ");
        if (!query.isCompanyScope()) {
            sb.append(" and o.admin_emp_id = '" + query.getAdminId() + "'");
        }

        ComQB sp = ComQB.build(EmpOrganizationDo.class, "eo");
        if (!query.isCompanyScope()) {
            sp.join(AdminManageOrgDo.class, "o");
            sp.appendOn(sb.toString());
        }
        sp.clearSelect().select(" eo.* ");

        ComQB comQB = ComQB.build().fromQ(sp, "s")
                .leftJoinQ(sp, "p")
                .appendOn(" s.company_id = p.company_id and p.org_id = s.parent_org_id  ")
                .clearSelect().select(" s.org_id,s.org_name,s.ding_org_id ")
                .setRsType(CurOrgStructPo.Org.class)
                .whereEqReq("s.company_id", query.getCompanyId().getId())
                .whereEq("s.status", "valid")
                .appendWhere(" p.id is null ")
                .groupBy(" s.org_id ");

        List<CurOrgStructPo.Org> orgs = dao.listAll(comQB);
        if (CollUtil.isEmpty(orgs)) {
            return new CurOrgStructPo(orgs);
        }
        orgEmpCnt(query.getCompanyId(), query.getOnlySeeEnable(), orgs);
        return new CurOrgStructPo(orgs);
    }


    public List<EmpRefOrgDo> listFixAtOrgIds(TenantId companyId, EmpId empId, List<String> orgName) {
        ComQB refOrgQ = ComQB.build(EmpRefOrgDo.class, "ref")
                .join(EmpOrganizationDo.class, "s")
                .appendOn("s.company_id = ref.company_id and s.org_id = ref.org_id and ref.ref_type = 'org' and s.status='valid'")
                .clearSelect().select("ref.org_id,ref.company_id").setRsType(EmpRefOrgDo.class)
                .whereEqReq("ref.company_id", companyId.getId())
                .whereEqReq("ref.emp_id", empId.getId())
                .whereIn("s.orgName", orgName);
        return dao.listAll(refOrgQ);
    }

    public List<String> listEmpIdByOrg(String companyId, String orgId) {
        ComQB refOrgQ = ComQB.build(EmpRefOrgDo.class, "ref")
                .clearSelect().select("DISTINCT ref.emp_id").setRsType(String.class)
                .whereEqReq("ref.company_id", companyId)
                .whereEqReq("ref.org_id", orgId);
        return dao.listAll(refOrgQ);
    }

    public void addImportEmp(TransferPlatEmpDo data) {
        Long id = dao.nextLongId("");
        data.setId(id);
        dao.save(data);
    }

    public PagedList<TransferPlatEmpDo> pagedTransferPlatEmp(String companyId, int pageNo, int pageSize) {
        ComQB comQB = ComQB.build(TransferPlatEmpDo.class, "emp")
                .whereEqReq("emp.company_id", companyId)
                .setPage(pageNo, pageSize);
        return dao.listPage(comQB);
    }

    public PagedList<TransferPlatDeptDo> pagedTransferPlatDept(String companyId, int pageNo, int pageSize) {
        ComQB comQB = ComQB.build(TransferPlatDeptDo.class, "dept")
                .whereEqReq("dept.company_id", companyId)
                .setPage(pageNo, pageSize);
        return dao.listPage(comQB);
    }

    public void updateEmpExtUserId(TransferPlatEmpDo platEmpDo) {
        UpdateBuilder up = UpdateBuilder.build(TransferPlatEmpDo.class)
                .set("to_ext_user_id", platEmpDo.getToExtUserId())
                .whereEq("companyId", platEmpDo.getCompanyId())
                .whereEqReq("id", platEmpDo.getId());
        dao.update(up);
    }

    public void updateDeptExtOrgId(TransferPlatDeptDo platEmpDo) {
        UpdateBuilder up = UpdateBuilder.build(TransferPlatEmpDo.class)
                .set("to_ext_org_id", platEmpDo.getToExtOrgId())
                .whereEq("companyId", platEmpDo.getCompanyId())
                .whereEqReq("id", platEmpDo.getId());
        dao.update(up);
    }

    public List<String> listOrgIdWithAllFocusItem(String companyId, String cycleId, Integer performanceType, List<String> itemIds) {

        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u");
        comQB.join(PerfEvaluateTaskKpiDo.class, "k")
                .appendOn(" k.company_id = u.company_id and u.id = k.task_user_id " +
                        "and u.is_deleted = 'false' and u.task_status <> 'drawUpIng' ")
                .clearSelect().select("IF(" + performanceType + " = 1, u.org_id, u.eval_org_id) AS org_id")
                .setRsType(String.class)
                .whereIn("k.kpi_item_id", itemIds)
                .whereEqReq("k.company_id", companyId)
                .whereEqReq("k.is_deleted", Boolean.FALSE.toString())
                .whereEqReq("u.cycle_id", cycleId)
                .appendWhere("EXISTS ( SELECT 1\n" +
                        "              FROM perf_evaluate_task_base tb\n" +
                        "              WHERE tb.id = k.task_id\n" +
                        "                AND tb.cycle_id = " + cycleId +
                        "                AND tb.performance_type =  " + performanceType +
                        "                AND tb.is_deleted = 'false' )");
        if (performanceType == 1) {
            comQB.groupBy("u.org_id");
        } else {
            comQB.groupBy("u.eval_org_id");
        }
        return dao.listAll(comQB);
    }

    public List<OrganizationPo> listPrivRootOrg(String companyId, List<String> orgIdList) {

        final ComQB qb = ComQB.build(EmpOrganizationDo.class, "o")
                .setRsType(OrganizationPo.class)
                .whereIn("org_id", orgIdList)
                .whereEq("companyId", companyId)
                .appendWhere("status='valid'");
        final List<OrganizationPo> rootOrgs = dao.listAll(qb);
        if (rootOrgs.isEmpty()) {
            return rootOrgs;
        }
//        List<String> childOrgIds = rootOrgs.stream().map(OrganizationPo::getOrgId).collect(Collectors.toList());
        // 递归统计子部门人数
        ComQB cntEmpQ = ComQB.build(EmpRefOrgDo.class, "ref")
                .join(EmployeeBaseInfoDo.class, "ebi").appendOn("ref.company_id = ebi.company_id and ebi.employee_id = ref.emp_id")
                .join(EmpOrganizationDo.class, "s").appendOn("ref.company_id = s.company_id  and  s.org_id =ref.org_id")
                .join(EmpOrganizationDo.class, "p").appendOn("s.company_id=p.company_id and instr(s.org_code,p.org_code) ")
                .clearSelect().select(" p.org_id,count(distinct ebi.employee_id) emp_cnt")
                .setRsType(OrganizationPo.class)
                .whereIn(" p.org_id", orgIdList)
                .appendWhere("ref.ref_type='org'")
                .whereEq("ref.companyId", companyId)
                .appendWhere("ebi.is_delete='false'")
                .appendWhere("s.status='valid'")
                .groupBy(" p.org_id");
        List<OrganizationPo> empCntAtOrgs = dao.listAll(cntEmpQ);
        Map<String, OrganizationPo> cntMap = empCntAtOrgs.stream().collect(Collectors.toMap(OrganizationPo::getOrgId, v -> v));

        // 统计子部门数
        ComQB childOrgCntQ = ComQB.build(EmpOrganizationDo.class, "s")
                .join(EmpOrganizationDo.class, "p").appendOn("s.parent_org_id = p.org_id and s.company_id=p.company_id")
                .clearSelect().select(" p.org_id,count(1) l1_child_cnt")
                .setRsType(OrganizationPo.class)
                .whereIn(" p.org_id", orgIdList)
                .appendWhere("s.status='valid'")
                .groupBy(" p.org_id");
        final List<OrganizationPo> childOrgCnts = dao.listAll(childOrgCntQ);
        Map<String, OrganizationPo> childOrgCntMap = childOrgCnts.stream().collect(Collectors.toMap(OrganizationPo::getOrgId, v -> v));

        //统计子部门的主管
        final Map<String, List<OrgManagerPo>> orgManagerGroups = listManangers(orgIdList);

        rootOrgs.forEach(org -> {
            org.updateEmpCnt(cntMap.get(org.getOrgId()));
            org.updateL1Child(childOrgCntMap.get(org.getOrgId()));
            org.updateManangers(orgManagerGroups.get(org.getOrgId()));
        });
        return rootOrgs;


    }

    public List<OrganizationPo> listPrivRootOrgR(String companyId, List<String> orgIdList) {

        final ComQB qb = ComQB.build(EmpOrganizationDo.class, "o")
                .setRsType(OrganizationPo.class)
                .clearSelect().select("org_id,org_name,name_chinese,parent_org_id")
                .select("distinct_total_emp_cnt as emp_cnt,direct_child_org_cnt as l1_child_cnt")
                .whereIn("org_id", orgIdList)
                .whereEq("companyId", companyId)
                .appendWhere("status='valid'");

        final List<OrganizationPo> rootOrgs = dao.listAll(qb);
        if (rootOrgs.isEmpty()) {
            return rootOrgs;
        }
        //统计子部门的主管
//        List<String> childOrgIds = rootOrgs.stream().map(OrganizationPo::getOrgId).collect(Collectors.toList());
        final Map<String, List<OrgManagerPo>> orgManagerGroups = listManangers(orgIdList);
        rootOrgs.forEach(org -> {
            org.updateManangers(orgManagerGroups.get(org.getOrgId()));
        });
        return rootOrgs;
    }

    public List<String> listOrgIdsAtHeight(String companyId, List<String> privOrgIds, Integer orgPathHeight) {

        ComQB comQB = ComQB.build(EmpOrganizationDo.class, "o")
                .clearSelect()
                .select(" distinct SUBSTRING_INDEX(SUBSTRING_INDEX(org_code, '|', " + (orgPathHeight + 1) + "    ),'|',-1 ) AS org_id ")
                .setRsType(String.class)
                .whereIn("org_id", privOrgIds)
                .whereEq("companyId", companyId)
                .whereEqReq("status", "valid");

        return dao.listAll(comQB);
    }


    @Override
    public List<KpiEmp> listByEmp(TenantId companyId, List<String> empIds) {
        if (CollUtil.isEmpty(empIds)) {
            return Collections.emptyList();
        }
        final ComQB comQB = ComQB.build(EmployeeBaseInfoDo.class, "e")
                .clearSelect().setRsType(KpiEmp.class)
                .select("status, employee_id  as emp_id,  name as emp_name, ding_user_id ex_user_id , avatar,jobnumber")
                .whereEq("companyId", companyId.getId())
                .whereIn("employeeId", empIds);
        return dao.listAll(comQB);
    }

    public EmpEvalOrgInfo getEmpEvalOrgInfo(String companyId, String orgId) {

        return null;
    }

    public List<EmpRankDo> listRank(String companyId, String status) {
        ComQB comQB = ComQB.build(EmpRankDo.class)
                .setRsType(EmpRankDo.class)
                .whereEqReq("company_id", companyId);
        if (StrUtil.isNotEmpty(status)){
            comQB.whereEqReq("status", status);
        }
        return domainDao.listAll(comQB);
    }

    public List<EmpPositionDo> listPosition(String companyId, String status) {
        ComQB comQB = ComQB.build(EmpPositionDo.class)
                .setRsType(EmpPositionDo.class)
                .whereEqReq("company_id", companyId);
        if (StrUtil.isNotEmpty(status)){
            comQB.whereEqReq("status", status);
        }
        return domainDao.listAll(comQB);
    }

    public List<EmpRankDo> listRankByNames(TenantId companyId, Set<String> rankNames){
        ComQB comQB = ComQB.build(EmpRankDo.class)
                .setRsType(EmpRankDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("rank_name", rankNames);
        return domainDao.listAll(comQB);
    }

    public List<EmpPositionDo> listPositionByNames(TenantId companyId, Set<String> postNames){
        ComQB comQB = ComQB.build(EmpPositionDo.class)
                .setRsType(EmpPositionDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("post_name", postNames);
        return domainDao.listAll(comQB);
    }
}
