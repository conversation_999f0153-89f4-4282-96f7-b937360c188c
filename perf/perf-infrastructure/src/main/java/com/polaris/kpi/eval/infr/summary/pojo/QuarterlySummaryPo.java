package com.polaris.kpi.eval.infr.summary.pojo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import lombok.Data;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

@Data
//表头行高
@HeadRowHeight(34)
//列宽
@ColumnWidth(24)
//内容行高
//@ContentRowHeight(20)
//表头字体设置
@HeadFontStyle(fontHeightInPoints = 9)
//表头背景
@HeadStyle(fillBackgroundColor = 1)
//内容字体样式
@ContentFontStyle(fontHeightInPoints = 10)
//内容样式 wrapped自动换行
@ContentStyle(borderLeft = BorderStyle.THIN, borderRight = BorderStyle.THIN, borderBottom = BorderStyle.THIN, borderTop = BorderStyle.THIN, wrapped = true, horizontalAlignment = HorizontalAlignment.CENTER)
//季度总结/季度报表
public class QuarterlySummaryPo {
    @ExcelProperty("姓名")
    private String empName;
    @ExcelIgnore
    private String empId;
    @ExcelProperty("名称部门")
    private String orgName;
    @ExcelProperty("角色名称")
    private String roleName;
    @ExcelProperty("第一季度")
    private String quarter1;
    @ExcelProperty("第二季度")
    private String quarter2;
    @ExcelProperty("第三季度")
    private String quarter3;
    @ExcelProperty("第四季度")
    private String quarter4;
}
