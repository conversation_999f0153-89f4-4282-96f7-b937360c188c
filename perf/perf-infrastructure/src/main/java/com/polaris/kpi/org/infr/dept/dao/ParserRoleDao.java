package com.polaris.kpi.org.infr.dept.dao;

import cn.com.polaris.kpi.dept.KpiRole;
import cn.hutool.json.JSONUtil;
import com.perf.www.model.dic.CompanySysSettingModel;
import com.polaris.acl.dept.pojo.role.RoleDo;
import com.polaris.acl.dept.pojo.role.RoleRefEmpDo;
import com.polaris.kpi.org.domain.dept.repo.RoleEmpFinder;
import com.polaris.kpi.org.infr.emp.pojo.RoleMemberDo;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.MapWrap;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSON;
import org.apache.commons.collections4.CollectionUtils;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;

@Component
@Slf4j
public class ParserRoleDao implements RoleEmpFinder {

    @Autowired
    private DomainDaoImpl domainDao;


    @Override
    public boolean getMatchScope(TenantId tenantId) {
        ComQB query = ComQB.build(CompanySysSettingModel.class, "r")
                .whereEq("company_id", tenantId.getId())
                .whereEq("setting_type", "roleRefEmpManageScope")
                .whereEq("is_deleted", "false");
        CompanySysSettingModel settingModel = domainDao.findOne(query);
        log.debug("是否开启了按管理范围审核:{}", JSONUtil.toJsonStr(settingModel));
        return settingModel == null || Boolean.parseBoolean(settingModel.getValue());
    }

    public MapWrap<String, KpiRole> listRole(TenantId companyId, Collection<String> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return new MapWrap<>();
        }
        ComQB roleQ = ComQB.build(RoleDo.class, "r")
                .clearSelect().setRsType(KpiRole.class)
                .select("id role_id,role_name ")
                .whereEqReq("r.company_id", companyId.getId())
                .appendWhere("r.is_deleted = 'false' ")
                .appendWhere("r.is_group = 'false' ")
                .whereIn("r.id", roleIds);
        List<KpiRole> roles = domainDao.listAll(roleQ);

        ComQB qb = ComQB.build(RoleRefEmpDo.class, "r").clearSelect().setRsType(RoleMemberDo.class)
                .select("r.emp_id, r.role_id,r.scope_org_ids , JSON_EXTRACT(r.scope_emps, '$[*].empId') scope_emps")
                .whereEq("r.company_id", companyId.getId())
                .appendWhere("r.`status` = 'valid' and r.is_deleted = 'false' ")
                .whereIn("r.role_id", roleIds);
        List<KpiRole.RoleMember> members = domainDao.listAllDomain(qb, KpiRole.RoleMember.class);
        log.debug("查询出的角色关联人员:{}", JSONUtil.toJsonStr(members));
        ListWrap<KpiRole.RoleMember> roleGroups = new ListWrap<>(members).groupBy(r -> r.getRoleId());

        for (KpiRole role : roles) {
            List<KpiRole.RoleMember> roleMembers = roleGroups.groupGet(role.getRoleId());
            role.setRoleMembers(roleMembers);
        }
        MapWrap<String, KpiRole> roleMapWrap = new MapWrap<>(roles, kpiRole -> kpiRole.getRoleId());
        return roleMapWrap;
    }

}
