package com.polaris.kpi.open.infr.ppojo;

import com.polaris.kpi.common.infr.DataObj;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.ibatis.annotations.Ckey;
import org.apache.ibatis.annotations.Table;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@Table(value = "open_access_cache")
public class AccessCacheDo extends DataObj {
    @Ckey
    protected String key;
    protected String val;
    protected Long expireIn;//毫秒
    protected Date dbTimestamp;//数据库时间戳
    protected Date createdTime;//
    protected Date updatedTime;//

    public AccessCacheDo(String key, String val, Long expireIn) {
        this.key = key;
        this.val = val;
        this.expireIn = expireIn;
    }
}
