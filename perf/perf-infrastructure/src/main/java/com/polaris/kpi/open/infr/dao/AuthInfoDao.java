package com.polaris.kpi.open.infr.dao;

import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.open.domain.entity.AccessLimit;
import com.polaris.kpi.open.domain.entity.AuthInfo;
import com.polaris.kpi.open.infr.ppojo.AccessLimitDo;
import com.polaris.kpi.open.infr.ppojo.AccessCacheDo;
import com.polaris.kpi.open.infr.ppojo.AuthInfoDo;
import com.polaris.kpi.org.domain.dept.type.CorpId;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.common.DomainToDataBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Calendar;
import java.util.Optional;

@Component
@Slf4j
public class AuthInfoDao {

    private DomainDaoImpl domainDao;
    @Autowired
    public void setDomainDao(DomainDaoImpl domainDao) {
        this.domainDao = domainDao;
    }

    private static final long DEFAULT_EXPIRE = 4200_000L;
    private static TimedCache<String, Object> cache = new TimedCache<>(DEFAULT_EXPIRE);//默认70分钟
    private static TimedCache<String, AccessLimit> accessCache = new TimedCache<>(DEFAULT_EXPIRE);
    private final static String ACCESS_TOKEN_KEY = "accessToken";
    private String getAccessTokenKey(String accessToken) {
        return ACCESS_TOKEN_KEY + ":" + accessToken;
    }

    private static String[] openApis = {
            "/open/tenant/getAccessToken",
            "/open/tenant/eval/pagedTask",
            "/open/eval/pagedTalentEval"
    };

    public String[] getOpenApis() {
        return AuthInfoDao.openApis;
    }

    private CacheVo getCacheVo(String key) {
        ComQB comQB = ComQB.build(AccessCacheDo.class)
                .clearSelect()
                .select("`key`,val,expire_in,db_timestamp,unix_timestamp(CURRENT_TIMESTAMP()) as db_now")
                .setRsType(CacheVo.class)
                .whereEq("`key`", key)
                .limit(0, 1);
        return this.domainDao.findOne(comQB);
    }

    //仅在注册时初始化
    public void initAccessToken(CorpId corpId) {
        String key = corpId.getCorpId();
        String token = key + "_" + UUID.fastUUID().toString(true);
        cache.put(key, token, DEFAULT_EXPIRE);
        cache.put(getAccessTokenKey(token), key, DEFAULT_EXPIRE);
        AccessCacheDo cacheDo = new AccessCacheDo(key, token, DEFAULT_EXPIRE);
        this.domainDao.save(cacheDo);
    }

    public String getAccessToken(CorpId corpId) {
        String key = corpId.getCorpId();
        if (cache.containsKey(key)) {
            String token = (String) cache.get(key, false);
            if (StringUtils.isNotBlank(token)) {
                return token;
            }
        }

        CacheVo cacheVo = getCacheVo(key);
        if (cacheVo != null && cacheVo.isValid()) {
            cache.put(key, cacheVo.getVal(), cacheVo.getExpireInSeconds());
            cache.put(getAccessTokenKey(cacheVo.getVal()), key, cacheVo.getExpireInSeconds());
            return cacheVo.getVal();
        }

        String token = key + "_" + UUID.fastUUID().toString(true);
        cache.put(key, token, DEFAULT_EXPIRE);
        cache.put(getAccessTokenKey(token), key, DEFAULT_EXPIRE);

        UpdateBuilder update = UpdateBuilder.build(AccessCacheDo.class)
                .set("val", token)
                .set("expireIn", DEFAULT_EXPIRE)
                .appendSet(" db_timestamp=now() ")
                .whereEq("`key`", key);

        // 数据库给默认值，只有更新
        this.domainDao.update(update);
        return token;
    }

    @Data
    @NoArgsConstructor
    private static class CacheVo extends AccessCacheDo {
        //读数据库时读取数据的当前时间
        protected Long dbNow;//数据中的当前时间,用于计算本地缓存的过期时间

        @JSONField(serialize = false)
        public boolean isValid() {
            return getExpireInSeconds() < this.expireIn;
        }

        //剩余的过期时间
        @JSONField(serialize = false)
        public long getExpireInSeconds() {
            long ts = this.dbTimestamp.getTime();
            long expiresIn = (this.dbNow * 1000 - ts);
            return expiresIn;
        }
    }

    public CorpId getValidCorpId(String accessToken) {

        String key = getAccessTokenKey(accessToken);
        String corpId;
        if (cache.containsKey(key)) {
            corpId = (String) cache.get(key, false);
            if (StringUtils.isNotBlank(corpId)) {
                return new CorpId(corpId);
            }
        }
        String[] infos = accessToken.split("_");
        Assert.isTrue(infos.length == 2, "50003, invalid access token");
        corpId = infos[0];

        CacheVo cacheVo = getCacheVo(corpId);
        Assert.notNull(cacheVo, "50004, invalid access token");
        Assert.isTrue(accessToken.equals(cacheVo.getVal()), "50006, invalid access token");
        Assert.isTrue(cacheVo.isValid(), "50005, invalid access token");

        cache.put(key, corpId, cacheVo.getExpireInSeconds());
        return new CorpId(corpId);
    }

    public CorpId getCorpId(String accessToken) {
        String[] infos = accessToken.split("_");
        Assert.isTrue(infos.length == 2, "50003, invalid access token");
        String corpId = infos[0];
        return new CorpId(corpId);
    }

    private String formatHourKey(String originKey) {
        Calendar now = Calendar.getInstance();
        String format = "ACCESS_COUNT:%s:%s%s%s:%s";
        return String.format(format, originKey, now.get(Calendar.YEAR), now.get(Calendar.MONTH), now.get(Calendar.DATE),
                now.get(Calendar.HOUR_OF_DAY));
    }

    AccessLimit getAccessLimit(String key) {
        ComQB comQB = ComQB.build(AccessLimitDo.class)
                .whereEq("`key`", key)
                .limit(0, 1);
        return this.domainDao.findDomain(comQB, AccessLimit.class);
    }

    @Transactional
    public void increment(CorpId corpId) {
        String key = formatHourKey(corpId.getCorpId());
        AccessLimit limit;
        if (accessCache.containsKey(key)) {
            limit = accessCache.get(key, false);
            limit.increment();
        } else {
            UpdateBuilder update = UpdateBuilder.build(AccessLimitDo.class)
                    .appendSet("max_count=max_count")
                    .whereEq("`key`", key);
            this.domainDao.update(update);

            limit = getAccessLimit(key);
            if (limit == null) {
                limit = new AccessLimit(key, 1000L, 1L);
                AccessLimitDo limitDo = new AccessLimitDo();
                new DomainToDataBuilder<AccessLimit>(limit, limitDo).build();
                this.domainDao.save(limitDo);
            } else {
                limit.increment();
            }

        }

        accessCache.put(key, limit);

        if (limit.needUpdate()) {
            updateAccessCount(limit, key, corpId);
        }
    }

    private synchronized void updateAccessCount(AccessLimit limit, String key, CorpId corpId) {
        int plusCnt = limit.getCacheCount();

        if (plusCnt == 0) {
            return;
        }

        limit.clearCacheCount();

        UpdateBuilder builder = UpdateBuilder.build(AccessLimitDo.class)
                .appendSet("access_count=access_count + " + plusCnt)
                .whereEq("`key`", key);
        this.domainDao.update(builder);

        UpdateBuilder updateTotal = UpdateBuilder.build(AuthInfoDo.class)
                .appendSet("access_count=access_count + " + plusCnt)
                .whereEq("corp_id", corpId.getCorpId());
        this.domainDao.update(updateTotal);
    }

    public Optional<AccessLimit> allowAccess(CorpId corpId) {
        String key = formatHourKey(corpId.getCorpId());
        if (!accessCache.containsKey(key)) {
            return Optional.empty();
        }
        AccessLimit limit = accessCache.get(key, false);
        return Optional.of(limit);
    }
}
