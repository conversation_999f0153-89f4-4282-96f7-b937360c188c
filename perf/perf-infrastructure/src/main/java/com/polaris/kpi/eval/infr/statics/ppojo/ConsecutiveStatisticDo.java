package com.polaris.kpi.eval.infr.statics.ppojo;

import com.polaris.kpi.common.infr.BaseWithCompanyIdData;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.ibatis.annotations.Table;

/**
 * <AUTHOR>
 * @date 2025/2/23 23:13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Table("consecutive_statistic")
public class ConsecutiveStatisticDo  extends BaseWithCompanyIdData {

    /**
     * 主键
     */
    private String id;

    /**
     * 考核周期id
     */
    private String cycleId;

    /**
     * 连续员工数，统计表保存的是周期内全公司人数
     */
    private Integer userCount;

    /**
     * 是否默认规则
     */
    private Boolean ruleDefault;

    /**
     * 1 为员工绩效，2 为组织绩效
     */
    private Integer performanceType;

    /**
     * 规则类型，1 为连续绩优，2 为连续绩差
     */
    private Integer ruleType;

}
