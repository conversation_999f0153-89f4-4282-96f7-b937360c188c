package com.polaris.kpi.setting.ppojo;

import com.polaris.kpi.common.infr.DelData;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.ibatis.annotations.Ckey;

import java.util.Date;

/**
 * 校准审批流程实例
 */
@Setter
@Getter
@NoArgsConstructor
public class ResultAuditFlowInstanceDo extends DelData {

    @Ckey
    private String id;
    private String taskId;
    private String taskUserId;
    private String orgId;
    private String name;  //实例名称（2024年一月考核设计部）
    private String md5Key;

    public ResultAuditFlowInstanceDo(String id,String companyId, String taskId,String taskUserId, String orgId, String name,String md5Key,String createUser) {
        this.id = id;
        this.companyId = companyId;
        this.taskId = taskId;
        this.taskUserId = taskUserId;
        this.orgId = orgId;
        this.name = name;
        this.md5Key = md5Key;
        this.createdUser = createUser;
        this.createdTime = new Date();
    }
}
