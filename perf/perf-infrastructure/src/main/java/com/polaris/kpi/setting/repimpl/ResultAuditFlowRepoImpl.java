package com.polaris.kpi.setting.repimpl;

import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.com.polaris.kpi.company.MsgSceneEnum;
import cn.com.polaris.kpi.eval.EvalReviewers;
import cn.com.polaris.kpi.eval.ResultAuditReviewers;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.base.Supplier;
import com.polaris.kpi.eval.domain.task.dmsvc.ResultAuditFlowDmSvc;
import com.polaris.kpi.eval.domain.task.entity.EvalAudit;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskBaseDo;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskScoreResultDo;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskUserDo;
import com.polaris.kpi.org.infr.company.ppojo.CompanyMsgCenterDo;
import com.polaris.kpi.org.infr.emp.pojo.EmployeeBaseInfoDo;
import com.polaris.kpi.setting.builder.ResultAuditFlowBuilder;
import com.polaris.kpi.setting.domain.entity.*;
import com.polaris.kpi.setting.domain.repo.ResultAuditFlowRepo;
import com.polaris.kpi.setting.ppojo.*;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.lufei.ibatis.builder.BatchUpdateBuilder;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.DeleteBuilder;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.common.data.ToDataBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ResultAuditFlowRepoImpl implements ResultAuditFlowRepo {



    public static final String auditFlowInstanceSeq = "result_audit_flow_instance";
    public static final String auditFlowNodeSeq = "result_audit_flow_node";
    public static final String auditFlowNodeRaterSeq = "result_audit_flow_node_rater";
    public static final String auditFlowUserSeq = "result_audit_flow_user";
    public static final String auditSummarySeq = "task_result_audit_summary";

    @Autowired
    private DomainDaoImpl domainDao;
    @Autowired
    private ResultAuditFlowRepo resultAuditFlowRepo;

    public void setDomainDao(DomainDaoImpl domainDao) {
        this.domainDao = domainDao;
    }

    final Supplier<String> instanceIdGen = () -> domainDao.nextLongAsStr(auditFlowInstanceSeq);
    final Supplier<String> nodeIdGen = () -> domainDao.nextLongAsStr(auditFlowNodeSeq);
    final Supplier<String> nodeRaterIdGen = () -> domainDao.nextLongAsStr(auditFlowNodeRaterSeq);
    final Supplier<String> userIdGen = () -> domainDao.nextLongAsStr(auditFlowUserSeq);

    @Override
    public void saveAuditFlow(ResultAuditFlow flow, EvalUser evalUser) {

        ResultAuditFlowBuilder builder = new ResultAuditFlowBuilder(instanceIdGen, nodeIdGen, nodeRaterIdGen, userIdGen,
                evalUser, flow.getCompanyId(), flow.getOpEmpId());
        builder.initResultAudit();
        builder.initFlowMD5();
        builder.build();
        //先删除存在
        deleteAllFlow(evalUser.getCompanyId().getId(), Arrays.asList(evalUser.getId()));
        domainDao.saveBatch(builder.getInstances());
        domainDao.saveBatch(builder.getNodes());
        domainDao.saveBatch(builder.getNodeRaters());
        //处理概要
        refreshSummary(evalUser.getCompanyId().getId(), evalUser.getTaskId());

    }

    @Override
    public void fixSaveAuditFlow(ResultAuditFlow flow, EvalUser evalUser) {

        ResultAuditFlowBuilder builder = new ResultAuditFlowBuilder(instanceIdGen, nodeIdGen, nodeRaterIdGen, userIdGen,
                evalUser, flow.getCompanyId(), flow.getOpEmpId());
        builder.initResultAudit();
        builder.initFlowMD5();
        builder.buildV2();
        //先删除存在
        deleteAllFlow(evalUser.getCompanyId().getId(), Arrays.asList(evalUser.getId()));
        domainDao.saveBatch(builder.getInstances());
        domainDao.saveBatch(builder.getNodes());
        domainDao.saveBatch(builder.getNodeRaters());
        //处理概要
        refreshSummary(evalUser.getCompanyId().getId(), evalUser.getTaskId());

    }

    @Override
    public void refreshAuditFlow(ResultAuditFlow flow, EvalUser evalUser) {

        ResultAuditFlowBuilder builder = new ResultAuditFlowBuilder(instanceIdGen, nodeIdGen, nodeRaterIdGen, userIdGen,
                evalUser, flow.getCompanyId(), flow.getOpEmpId());
        builder.initResultAudit();
        builder.initFlowMD5();
        builder.build();
        //先删除存在
        deleteAllFlow(evalUser.getCompanyId().getId(), Arrays.asList(evalUser.getId()));
        domainDao.saveBatch(builder.getInstances());
        domainDao.saveBatch(builder.getNodes());
        domainDao.saveBatch(builder.getNodeRaters());
    }

    @Override
    public void refreshAuditFlow(String companyId, String opEmpId, List<EvalUser> evalUsers) {
        if (CollUtil.isEmpty(evalUsers)) {
            return;
        }
         List<ResultAuditFlowInstanceDo> instances = new ArrayList<>();
         List<ResultAuditFlowNodeDo> nodes = new ArrayList<>();
         List<ResultAuditFlowNodeRaterDo> nodeRaters = new ArrayList<>();
        for (EvalUser evalUser : evalUsers) {
            ResultAuditFlowBuilder builder = new ResultAuditFlowBuilder(instanceIdGen, nodeIdGen, nodeRaterIdGen, userIdGen,
                    evalUser, companyId, opEmpId);
            builder.initResultAudit();
            builder.initFlowMD5();
            builder.build();
            instances.addAll(builder.getInstances());
            nodes.addAll(builder.getNodes());
            nodeRaters.addAll(builder.getNodeRaters());
        }
        //先删除存在
        deleteAllFlow(companyId, CollUtil.map(evalUsers,u -> u.getId(),true));
        domainDao.saveBatch(instances);
        domainDao.saveBatch(nodes);
        domainDao.saveBatch(nodeRaters);
    }

    //用于初始化用的。不是正常接口
    @Override
    public void saveAuditFlowV2(EvalUser evalUser, List<ResultAuditFlowUser> flowUsers) {

        ResultAuditFlowBuilder builder = new ResultAuditFlowBuilder(instanceIdGen, nodeIdGen, nodeRaterIdGen, userIdGen,
                evalUser, evalUser.getCompanyId().getId(), "9999");
        builder.initResultAudit();
        builder.initFlowMD5();
        builder.build();
        //先删除存在
        deleteAllFlow(evalUser.getCompanyId().getId(), Arrays.asList(evalUser.getId()));
        domainDao.saveBatch(builder.getInstances());
        domainDao.saveBatch(builder.getNodes());
        domainDao.saveBatch(builder.getNodeRaters());
    }

    @Override
    public void updateAuditFlow(ResultAuditFlow flow, Integer approvalOrder) {
        ResultAuditFlowBuilder builder = new ResultAuditFlowBuilder();
        builder.conver(flow);

        for (ResultAuditFlowNodeDo node : builder.getNodes()) {
            UpdateBuilder flowNode = UpdateBuilder.build(ResultAuditFlowNodeDo.class)
                    .set("status", node.getStatus())
                    .set("updated_time", new Date())
                    .whereEqReq("id", node.getId());
            domainDao.update(flowNode);
        }
        for (ResultAuditFlowNodeRaterDo nodeRater : builder.getNodeRaters()) {
            UpdateBuilder flowNodeRater = UpdateBuilder.build(ResultAuditFlowNodeRaterDo.class)
                    .set("status", nodeRater.getStatus())
                    .set("updated_time", new Date())
                    .whereEqReq("id", nodeRater.getId());
            domainDao.update(flowNodeRater);
        }
    }

    public void updateAuditNodeRater(String companyId, String nodeRaterId, String auditEmpId, Integer level, Integer status){
        UpdateBuilder flowNodeRater = UpdateBuilder.build(ResultAuditFlowNodeRaterDo.class)
                .set("status", status)
                .set("updated_time", new Date())
                .whereEqReq("auditEmpId", auditEmpId)
                .whereEqReq("level", level)
                .whereEqReq("companyId", companyId)
                .whereEqReq("id", nodeRaterId);
        domainDao.update(flowNodeRater);
    }

    @Override
    public void updateOldAuditFlow(ResultAuditFlow flow) {
        ResultAuditFlowBuilder builder = new ResultAuditFlowBuilder();
        builder.converOld(flow);
        DeleteBuilder deleteNode = DeleteBuilder.build(ResultAuditFlowNodeDo.class).whereInReq("id", CollUtil.map(builder.getNodes(), n -> n.getId(), true));
        domainDao.delete(deleteNode);
        DeleteBuilder deleteNodeRater = DeleteBuilder.build(ResultAuditFlowNodeRaterDo.class).whereInReq("id", CollUtil.map(builder.getNodeRaters(), n -> n.getId(), true));
        domainDao.delete(deleteNodeRater);
        domainDao.saveBatch(builder.getNodes());
        domainDao.saveBatch(builder.getNodeRaters());
    }


    @Override
    public void deleteAllFlow(String companyId, List<String> taskUserId) {
        ComQB qb = ComQB.build(ResultAuditFlowInstanceDo.class)
                .clearSelect().select("id").setRsType(String.class)
                .whereEq("company_id", companyId)
                .whereInReq("task_user_id", taskUserId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        List<String> instanceIds = domainDao.listAll(qb);
        if (CollUtil.isEmpty(instanceIds)) {
            return;
        }
        UpdateBuilder instanceUp = UpdateBuilder.build(ResultAuditFlowInstanceDo.class)
                .whereInReq("id", instanceIds)
                .set("is_deleted", Boolean.TRUE.toString());
        domainDao.update(instanceUp);

        UpdateBuilder nodeUp = UpdateBuilder.build(ResultAuditFlowNodeDo.class)
                .whereInReq("flow_instance_id", instanceIds)
                .set("is_deleted", Boolean.TRUE.toString());
        domainDao.update(nodeUp);

        UpdateBuilder nodeRaterUp = UpdateBuilder.build(ResultAuditFlowNodeRaterDo.class)
                .whereInReq("flow_instance_id", instanceIds)
                .set("is_deleted", Boolean.TRUE.toString());
        domainDao.update(nodeRaterUp);

        UpdateBuilder up = UpdateBuilder.build(ResultAuditFlowUserDo.class)
                .whereInReq("flow_instance_id", instanceIds)
                .set("is_deleted", Boolean.TRUE.toString());
        domainDao.update(up);
    }

    @Override
    public void deleteAllFlowV2(String companyId, String taskId) {
        ComQB qb = ComQB.build(ResultAuditFlowInstanceDo.class)
                .clearSelect().select("id").setRsType(String.class)
                .whereEq("company_id", companyId)
                .whereEq("task_id", taskId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        List<String> instanceIds = domainDao.listAll(qb);
        UpdateBuilder instanceUp = UpdateBuilder.build(ResultAuditFlowInstanceDo.class)
                .whereInReq("id", instanceIds)
                .set("is_deleted", Boolean.TRUE.toString());
        domainDao.update(instanceUp);

        UpdateBuilder nodeUp = UpdateBuilder.build(ResultAuditFlowNodeDo.class)
                .whereInReq("flow_instance_id", instanceIds)
                .set("is_deleted", Boolean.TRUE.toString());
        domainDao.update(nodeUp);

        UpdateBuilder nodeRaterUp = UpdateBuilder.build(ResultAuditFlowNodeRaterDo.class)
                .whereInReq("flow_instance_id", instanceIds)
                .set("is_deleted", Boolean.TRUE.toString());
        domainDao.update(nodeRaterUp);

//        UpdateBuilder up = UpdateBuilder.build(ResultAuditFlowUserDo.class)
//                .whereInReq("flow_instance_id", instanceIds)
//                .set("is_deleted", Boolean.TRUE.toString());
//        domainDao.update(up);
    }

    @Override
    public void rejectAuditFlow(List<String> taskUserIds, Integer orderNode) {
        if (CollUtil.isEmpty(taskUserIds)) {
            return;
        }
        for (String taskUserId : taskUserIds) {
            UpdateBuilder nodeUp = UpdateBuilder.build(ResultAuditFlowNodeDo.class)
                    .whereEq("task_user_id", taskUserId)
                    .whereEq("approval_order", orderNode)
                    .set("status", 0);
            domainDao.update(nodeUp);

            UpdateBuilder nodeRaterUp = UpdateBuilder.build(ResultAuditFlowNodeRaterDo.class)
                    .whereEq("task_user_id", taskUserId)
                    .whereEq("level", orderNode)
                    .set("status", 0);
            domainDao.update(nodeRaterUp);
        }
    }

    @Override
    public ResultAuditFlow getResultAuditFlow(String companyId, String taskId, List<String> clearTaskUserIds, boolean isRefresh, List<EvalScoreResult> curLevelRs) {
        ResultAuditFlow flow = new ResultAuditFlow();
        flow.setCompanyId(companyId);
        ComQB instance = ComQB.build(ResultAuditFlowInstanceDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_id", taskId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        List<ResultAuditFlowInstance> instanceList = domainDao.listAllDomain(instance, ResultAuditFlowInstance.class);
        if (CollUtil.isEmpty(instanceList)) {
            return flow;
        }
        List<String> instanceIds = CollUtil.map(instanceList, i -> i.getId(), true);
        ComQB nodeQb = ComQB.build(ResultAuditFlowNodeDo.class)
                .whereEqReq("company_id", companyId)
                .whereIn("flow_instance_id", instanceIds)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        List<ResultAuditFlowNode> nodes = domainDao.listAllDomain(nodeQb, ResultAuditFlowNode.class);

        ComQB nodeRaterQb = ComQB.build(ResultAuditFlowNodeRaterDo.class)
                .whereEqReq("company_id", companyId)
                .whereIn("flow_instance_id", instanceIds)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        List<ResultAuditFlowNodeRater> nodeRaters = domainDao.listAllDomain(nodeRaterQb, ResultAuditFlowNodeRater.class);

        ComQB userQb = ComQB.build(ResultAuditFlowUserDo.class, "fu")
                .leftJoin(PerfEvaluateTaskUserDo.class, "u")
                .appendOn("fu.company_id = u.company_id and fu.task_user_id = u.id")
                .leftJoin(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("u.company_id = b.company_id and u.task_id = b.id")
                .clearSelect().select("fu.*,u.emp_id as empId,b.id as taskId,b.task_name as taskName,u.task_status as taskStatus")
                .setRsType(ResultAuditFlowUser.class)
                .whereEqReq("fu.company_id", companyId)
                .whereIn("fu.flow_instance_id", instanceIds)
                .whereEq("fu.is_deleted", Boolean.FALSE.toString())
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereEq("b.is_deleted", Boolean.FALSE.toString());
        List<ResultAuditFlowUser> users = domainDao.listAll(userQb);
        if (CollUtil.isNotEmpty(clearTaskUserIds)) {
            if (CollUtil.isNotEmpty(users)) {
                users = users.stream().filter(u -> !clearTaskUserIds.contains(u.getTaskUserId())).collect(Collectors.toList());
            }
        }
        flow.builderFlow(instanceList, nodes, nodeRaters, users);
        if (isRefresh) {
            flow.refreshAuditFlow();
        } else {
            flow.loadAuditFlow(curLevelRs);
        }
        //可发送通知的数据
        flow.getSendNofityFlow(CollUtil.isEmpty(curLevelRs) ? null : curLevelRs.get(0).getApprovalOrder());
        return flow;
    }

    @Override
    public ResultAuditFlow getResultAuditFlowV2(String companyId, String taskId, List<String> clearTaskUserIds) {
        ResultAuditFlow flow = new ResultAuditFlow();
        flow.setCompanyId(companyId);

        ComQB instance = ComQB.build(ResultAuditFlowInstanceDo.class,"i")
                .join(PerfEvaluateTaskUserDo.class,"u").appendOn("i.company_id = u.company_id and i.task_user_id = u.id")
                .whereEqReq("i.company_id", companyId)
                .whereEqReq("i.task_id", taskId)
                .whereEq("i.is_deleted", Boolean.FALSE.toString())
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereNotIn("u.task_status", Arrays.asList(TalentStatus.DRAW_UP_ING.getStatus(), TalentStatus.TERMINATED.getStatus(), TalentStatus.PUBLISHED.getStatus()));//未发起与发起中的不计入校准统计
        List<ResultAuditFlowInstance> instanceList = domainDao.listAllDomain(instance, ResultAuditFlowInstance.class);
        if (CollUtil.isEmpty(instanceList)) {
            return null;
        }
        ComQB summary = ComQB.build(TaskResultAuditSummaryDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_id", taskId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        List<TaskResultAuditSummary> summaryList = domainDao.listAllDomain(summary, TaskResultAuditSummary.class);

        List<String> instanceIds = CollUtil.map(instanceList, i -> i.getId(), true);
        ComQB nodeQb = ComQB.build(ResultAuditFlowNodeDo.class)
                .whereEqReq("company_id", companyId)
                .whereIn("flow_instance_id", instanceIds)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        List<ResultAuditFlowNode> nodes = domainDao.listAllDomain(nodeQb, ResultAuditFlowNode.class);

        ComQB nodeRaterQb = ComQB.build(ResultAuditFlowNodeRaterDo.class, "fnr")
                .leftJoin(ResultAuditFlowNodeDo.class, "fn").appendOn("fnr.company_id = fn.company_id and fnr.flow_node_id = fn.id")
                .clearSelect().select("fnr.*,fn.approval_order as level")
                .whereEqReq("fnr.company_id", companyId)
                .whereIn("fnr.flow_instance_id", instanceIds)
                .whereEq("fnr.is_deleted", Boolean.FALSE.toString());
        List<ResultAuditFlowNodeRater> nodeRaters = domainDao.listAllDomain(nodeRaterQb,ResultAuditFlowNodeRater.class);
        flow.accFlow(instanceList, nodes, nodeRaters, summaryList);
        return flow;
    }

    @Override
    public ResultAuditFlow getResultAuditFlowByUserId(String companyId, String taskUserId) {
        ResultAuditFlow flow = new ResultAuditFlow();
        flow.setCompanyId(companyId);

        ComQB instance = ComQB.build(ResultAuditFlowInstanceDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        ResultAuditFlowInstance flowInstance = domainDao.findDomain(instance, ResultAuditFlowInstance.class);
        if (Objects.isNull(flowInstance)) {
            return null;
        }
        ComQB nodeQb = ComQB.build(ResultAuditFlowNodeDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("flow_instance_id", flowInstance.getId())
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .orderBy("approval_order");
        List<ResultAuditFlowNode> nodes = domainDao.listAllDomain(nodeQb, ResultAuditFlowNode.class);

        ComQB nodeRaterQb = ComQB.build(ResultAuditFlowNodeRaterDo.class, "fnr")
                .leftJoin(ResultAuditFlowNodeDo.class, "fn").appendOn("fnr.company_id = fn.company_id and fnr.flow_node_id = fn.id")
                .clearSelect().select("fnr.*,fn.approval_order as level").setRsType(ResultAuditFlowNodeRaterDo.class)
                .whereEqReq("fnr.company_id", companyId)
                .whereEqReq("fnr.flow_instance_id", flowInstance.getId())
                .whereEq("fnr.is_deleted", Boolean.FALSE.toString())
                .orderBy("fnr.level");
        List<ResultAuditFlowNodeRater> nodeRaters = domainDao.listAllDomain(nodeRaterQb,ResultAuditFlowNodeRater.class);
        flow.accFlow(Arrays.asList(flowInstance), nodes, nodeRaters, new ArrayList<>());
        flow.builderCurFlow(flowInstance, nodes, nodeRaters);
        return flow;
    }


    @Override
    public List<ResultAuditFlowUser> listFlowUserByTaskId(String companyId, String taskId) {
        ComQB userQb = ComQB.build(ResultAuditFlowUserDo.class, "fu")
                .leftJoin(PerfEvaluateTaskUserDo.class, "u")
                .appendOn("fu.company_id = u.company_id and fu.task_user_id = u.id")
                .clearSelect().select("fu.*")
                .setRsType(ResultAuditFlowUser.class)
                .whereEqReq("fu.company_id", companyId)
                .whereEq("fu.is_deleted", Boolean.FALSE.toString())
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereEq("u.task_id", taskId);
        List<ResultAuditFlowUser> users = domainDao.listAll(userQb);
        return users;
    }

    @Override
    public TaskResultAuditSummary getSummary(String companyId, String taskId, String adminEmpId, Integer level) {
        ComQB summary = ComQB.build(TaskResultAuditSummaryDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_id", taskId)
                .whereEq("audit_emp_id", adminEmpId)
                .whereEq("level", level)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        TaskResultAuditSummary summary1 = domainDao.findDomain(summary, TaskResultAuditSummary.class);
        if (Objects.isNull(summary1)) {
            return summary1;
        }
//        summary1.setCompanyId(companyId);
        ComQB nodeRaterQb = ComQB.build(ResultAuditFlowNodeRaterDo.class, "r")
                .leftJoin(PerfEvaluateTaskUserDo.class, "u").appendOn("r.company_id = u.company_id and r.task_user_id = u.id")
                .leftJoin(ResultAuditFlowInstanceDo.class, "i").appendOn("r.company_id = i.company_id and r.flow_instance_id = i.id")
                .clearSelect().select("r.*,u.emp_id as empId,u.task_status")
                .whereEqReq("r.company_id", companyId)
                .whereEq("r.audit_emp_id", summary1.getAuditEmpId())
                .whereEq("r.level", summary1.getLevel())
                .whereNotIn("u.task_status", Arrays.asList("drawUpIng", "published"))//未发起与发起中的不计入校准统计
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereEq("i.task_id", summary1.getTaskId())
                .whereEq("i.is_deleted", Boolean.FALSE.toString())
                .whereEq("r.is_deleted", Boolean.FALSE.toString())
                .appendWhere("r.skip_type IS NULL");
        List<ResultAuditFlowNodeRater> nodeRaters = domainDao.listAllDomain(nodeRaterQb, ResultAuditFlowNodeRater.class);
        summary1.setRaters(nodeRaters);
        return summary1;

    }

    @Override
    public List<TaskResultAuditSummary> listSummaryByTaskId(String companyId, String taskId) {
        ComQB comQB = ComQB.build(TaskResultAuditSummaryDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_id", taskId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        List<TaskResultAuditSummary> summary = domainDao.listAllDomain(comQB, TaskResultAuditSummary.class);
        if (CollUtil.isEmpty(summary)) {
            return summary;
        }
        ComQB nodeRaterQb = ComQB.build(ResultAuditFlowNodeRaterDo.class, "r")
                .join(PerfEvaluateTaskUserDo.class, "u").appendOn("r.company_id = u.company_id and r.task_user_id = u.id")
                .join(ResultAuditFlowInstanceDo.class, "i").appendOn("r.company_id = i.company_id and r.flow_instance_id = i.id")
                .clearSelect().select("r.*,u.emp_id as empId")
                .whereEqReq("r.company_id", companyId)
                .whereNotIn("u.task_status", Arrays.asList("drawUpIng", "published"))//未发起与发起中的不计入校准统计
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereEq("i.task_id", taskId)
                .whereEq("i.is_deleted", Boolean.FALSE.toString())
                .whereEq("r.is_deleted", Boolean.FALSE.toString());
        List<ResultAuditFlowNodeRater> nodeRaters = domainDao.listAllDomain(nodeRaterQb,ResultAuditFlowNodeRater.class);
        if (CollUtil.isEmpty(nodeRaters)) {
            return summary;
        }
        Map<String, List<ResultAuditFlowNodeRater>> nodeRaterMap = nodeRaters.stream().collect(Collectors.groupingBy(r -> r.getAuditEmpId() + "-" + r.getLevel()));
        for (TaskResultAuditSummary auditSummary : summary) {
            //auditSummary.setCompanyId(companyId);
            auditSummary.setRaters(nodeRaterMap.get(auditSummary.getAuditEmpId() + "-" + auditSummary.getLevel()));
        }
        log.debug("*****summary*****{}", JSONUtil.toJsonStr(summary));
        return summary;
    }

    @Override
    public List<TaskResultAuditSummary> listSummaryByTaskId(String companyId, String taskId, String auditEmpId) {
        ComQB comQB = ComQB.build(TaskResultAuditSummaryDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_id", taskId)
                .whereEq("audit_emp_id",auditEmpId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        List<TaskResultAuditSummary> summary = domainDao.listAllDomain(comQB, TaskResultAuditSummary.class);
        if (CollUtil.isEmpty(summary)) {
            return summary;
        }
        ComQB nodeRaterQb = ComQB.build(ResultAuditFlowNodeRaterDo.class, "r")
                .leftJoin(PerfEvaluateTaskUserDo.class, "u").appendOn("r.company_id = u.company_id and r.task_user_id = u.id")
                .leftJoin(ResultAuditFlowInstanceDo.class, "i").appendOn("r.company_id = i.company_id and r.flow_instance_id = i.id")
                .clearSelect().select("r.*,u.emp_id as empId").setRsType(ResultAuditFlowNodeRater.class)
                .whereEqReq("r.company_id", companyId)
                .whereNotIn("u.task_status", Arrays.asList("drawUpIng", "published"))//未发起与发起中的不计入校准统计
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereEq("i.task_id", taskId)
                .whereEq("i.is_deleted", Boolean.FALSE.toString())
                .whereEq("r.audit_emp_id",auditEmpId)
                .whereEq("r.is_deleted", Boolean.FALSE.toString());
        List<ResultAuditFlowNodeRater> nodeRaters = domainDao.listAll(nodeRaterQb);
        if (CollUtil.isEmpty(nodeRaters)) {
            return summary;
        }
        Map<String, List<ResultAuditFlowNodeRater>> nodeRaterMap = nodeRaters.stream().collect(Collectors.groupingBy(r -> r.getAuditEmpId() + "-" + r.getLevel()));
        for (TaskResultAuditSummary auditSummary : summary) {
            //auditSummary.setCompanyId(companyId);
            auditSummary.setRaters(nodeRaterMap.get(auditSummary.getAuditEmpId() + "-" + auditSummary.getLevel()));
        }
        return summary;
    }

    @Override
    public void saveTaskResultAuditSummary(List<TaskResultAuditSummary> taskResultAuditSummarys) {
        if (CollUtil.isEmpty(taskResultAuditSummarys)) {
            return;
        }
        final Supplier<String> instanceIdGen = () -> domainDao.nextLongAsStr(auditSummarySeq);
        List<TaskResultAuditSummaryDo> summaryDos = new ArrayList<>();
        for (TaskResultAuditSummary taskResultAuditSummary : taskResultAuditSummarys) {
            taskResultAuditSummary.setId(instanceIdGen.get());
            summaryDos.add(new ToDataBuilder<>(taskResultAuditSummary, TaskResultAuditSummaryDo.class).data());
        }
        domainDao.saveBatch(summaryDos);
    }

    @Override
    public int updateSummary(TaskResultAuditSummary auditSummary) {
        UpdateBuilder up = UpdateBuilder.build(TaskResultAuditSummaryDo.class)
                .set("finish_cnt", auditSummary.getFinishCnt())
                .set("status", auditSummary.getStatus())
                .set("version", auditSummary.getVersion() + 1)
                .whereEqReq("id", auditSummary.getId())
                .whereEq("version", auditSummary.getVersion())
                .whereEqReq("company_id", auditSummary.getCompanyId().getId())
                .whereEqReq("task_id", auditSummary.getTaskId())
                .whereEq("is_deleted", Boolean.FALSE.toString());
        int count = domainDao.update(up);
        return count;
    }

    @Override
    public void delTaskResultAuditSummary(String companyId, String taskId) {
        UpdateBuilder up = UpdateBuilder.build(TaskResultAuditSummaryDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_id", taskId)
                .set("is_deleted", Boolean.TRUE.toString());
        domainDao.update(up);
    }

    @Override
    public void removeResultAuditFlow(String companyId, String taskId, String taskUserId) {
        this.deleteAllFlow(companyId, Arrays.asList(taskUserId));
        refreshSummary(companyId, taskId);
    }

    @Override
    public void refreshSummary(String companyId, String taskId) {
        //处理概要
        delTaskResultAuditSummary(companyId, taskId);
        ResultAuditFlowDmSvc dmSvc = new ResultAuditFlowDmSvc(companyId, taskId, null);
        dmSvc.setResultAuditFlowRepo(resultAuditFlowRepo);
        dmSvc.generateResultAuditSummarie();
        saveTaskResultAuditSummary(dmSvc.getTaskResultAuditSummaries());
    }

    @Override
    public void rejectRefreshSummary(String companyId, String taskId) {
        delTaskResultAuditSummary(companyId, taskId);
        ResultAuditFlowDmSvc dmSvc = new ResultAuditFlowDmSvc(companyId, taskId, null);
        dmSvc.setResultAuditFlowRepo(resultAuditFlowRepo);
        dmSvc.generateResultAuditSummarie();
        List<TaskResultAuditSummary> summaryList = dmSvc.getTaskResultAuditSummaries();
        for (TaskResultAuditSummary auditSummary : summaryList) {
            if (Objects.equals(auditSummary.getTotalCnt(), auditSummary.getFinishCnt())) {
                auditSummary.setStatus(1);
            }
        }
        saveTaskResultAuditSummary(summaryList);
    }

    @Override
    public ListWrap<ResultAuditReviewers> reviewersListWrap(TenantId companyId, List<String> taskUserIds) {
        if (CollUtil.isEmpty(taskUserIds)) {
            return new ListWrap<>();
        }
        ComQB raterQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class,"sr")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn("sr.company_id = e.company_id and sr.scorer_id = e.employee_id")
                .leftJoin(CompanyMsgCenterDo.class,"mc")
                .appendOn("sr.company_id = mc.company_id and sr.task_user_id = mc.link_id and sr.scorer_id = mc.emp_id and mc.handler_status = 'false' and mc.business_scene = #{businessScene}", MsgSceneEnum.TASK_RESULT_AUDIT.getType())
                .clearSelect().select("e.employee_id  as emp_id, e.name as emp_name,e.avatar,e.ding_user_id as ex_user_id,e.jobnumber")
                .select("sr.task_user_id,sr.approval_order,sr.company_id")
                .select("if(sr.id is null ,'waitCollect', if(sr.audit_status is null and mc.id is null,'waitCollect','waitResultAudit')) as audit_status")
                .setRsType(ResultAuditReviewers.class)
                .whereEqReq("sr.company_id",companyId.getId())
                .whereInReq("sr.task_user_id",taskUserIds)
                .whereIsNull("sr.skip_type")
                .whereIsNull("sr.audit_status")
                .whereEq("sr.scorer_type","final_result_audit")
                .whereEq("sr.is_deleted",Boolean.FALSE.toString());
        raterQB.groupBy("sr.task_user_id, sr.scorer_id, sr.approval_order");
        List<ResultAuditReviewers> emps = domainDao.listAll(raterQB);
        return new ListWrap<>(emps).groupBy(EvalReviewers::getTaskUserId);
    }


    @Override
    public Map<String,TaskResultAuditSummary> listByEmpIdSummaryAsMap(String companyId, List<String> taskIds, String adminEmpId) {
        ComQB summary = ComQB.build(TaskResultAuditSummaryDo.class)
                .whereEqReq("company_id", companyId)
                .whereInReq("task_id", taskIds)
                .whereEq("audit_emp_id", adminEmpId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        List<TaskResultAuditSummary> summarys = domainDao.listAllDomain(summary, TaskResultAuditSummary.class);
        if (CollUtil.isEmpty(summarys)) {
            return new HashMap<>();
        }
        ListWrap<EvalScoreResult> resultListWrap = this.listResultAuditWarp(companyId,taskIds,adminEmpId);
        summarys.forEach(sum ->{
            sum.markAuditStatus(resultListWrap.groupGet(sum.getTaskId() + "|" + sum.getLevel()));
        });
        summarys = CollUtil.filterNew(summarys, s -> s.waitOp());

        return CollUtil.toMap(summarys,new HashMap<>(),TaskResultAuditSummary::getTaskId);
    }

    private ListWrap<EvalScoreResult> listResultAuditWarp(String companyId, List<String> taskIds, String adminEmpId) {
        ComQB resultQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .whereEqReq("company_id", companyId)
                .whereInReq("task_id", taskIds)
                .whereEq("scorer_type",EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene())
                .whereEq("scorer_id", adminEmpId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        List<EvalScoreResult> resultList = domainDao.listAllDomain(resultQB, EvalScoreResult.class);
        if (CollUtil.isEmpty(resultList)) {
            return new ListWrap<>();
        }
        return new ListWrap<>(resultList).groupBy(re -> re.getTaskId() + "|" + re.getApprovalOrder());
    }

    private ListWrap<EvalScoreResult> listResultAuditWarp(String companyId, List<String> taskUserIds, List<String> adminEmpId) {
        ComQB resultQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .whereEqReq("company_id", companyId)
                .whereInReq("task_user_id", taskUserIds)
                .whereEq("scorer_type",EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene())
                .whereInReq("scorer_id", adminEmpId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        List<EvalScoreResult> resultList = domainDao.listAllDomain(resultQB, EvalScoreResult.class);
        if (CollUtil.isEmpty(resultList)) {
            return new ListWrap<>();
        }
        return new ListWrap<>(resultList).groupBy(re -> re.getTaskUserId() + "|" + re.getApprovalOrder() + "|" + re.getScorerId());
    }

    @Override
    public void addResultSummary(String companyId,EvalAudit evalAudit) {
        ComQB summary = ComQB.build(TaskResultAuditSummaryDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_id", evalAudit.getTaskId())
                .whereEq("level", evalAudit.getApprovalOrder())
                .whereEq("audit_emp_id", evalAudit.getApproverInfo())
                .whereEq("is_deleted", Boolean.FALSE.toString());
        TaskResultAuditSummaryDo summaryDo = domainDao.findOne(summary);
        if (Objects.nonNull(summaryDo)) {
            summaryDo.setTotalCnt(summaryDo.getTotalCnt() + 1);
            domainDao.update(summaryDo);
        }else {
            List<TaskResultAuditSummary> auditSummarys = new ArrayList<>();
            auditSummarys.add(new TaskResultAuditSummary(companyId,evalAudit.getTaskId(),evalAudit.getApprovalOrder(),
                    evalAudit.getApproverInfo(),1,0));
            this.saveTaskResultAuditSummary(auditSummarys);
        }
        //插入实例
        ResultAuditFlowBuilder builder = new ResultAuditFlowBuilder(instanceIdGen, nodeIdGen, nodeRaterIdGen, userIdGen,
                companyId, evalAudit.getCreatedUser());
        ComQB instance = ComQB.build(ResultAuditFlowInstanceDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", evalAudit.getTaskUserId())
                .whereEq("is_deleted", Boolean.FALSE.toString());
        ResultAuditFlowInstance flowInstance = domainDao.findDomain(instance, ResultAuditFlowInstance.class);
        builder.build(evalAudit,flowInstance);
        if (Objects.nonNull(flowInstance)) {
            deltedVacancy(companyId,evalAudit.getTaskUserId(),evalAudit.getApprovalOrder());
        }
        domainDao.saveBatch(builder.getInstances());
        domainDao.saveBatch(builder.getNodes());
        domainDao.saveBatch(builder.getNodeRaters());
    }

    private void deltedVacancy(String companyId,String taskUserId,Integer level) {

        UpdateBuilder up = UpdateBuilder.build(ResultAuditFlowNodeDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEqReq("approval_order",level)
                .whereEqReq("skip_flag",1)
                .set("is_deleted", Boolean.TRUE.toString());
        domainDao.update(up);

        UpdateBuilder upRater = UpdateBuilder.build(ResultAuditFlowNodeRaterDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEqReq("level",level)
                .whereIn("skip_type",Arrays.asList(1,2))
                .set("is_deleted", Boolean.TRUE.toString());
        domainDao.update(upRater);
    }


    private List<ResultAuditFlowNodeRater> listByInstanceIdNodeRater(String companyId, String instanceId) {
        ComQB qb = ComQB.build(ResultAuditFlowNodeRaterDo.class, "r")
                .leftJoin(ResultAuditFlowNodeDo.class, "n")
                .appendOn("r.company_id = n.company_id and r.flow_node_id = n.id")
                .clearSelect().select("r.*,n.approval_order as approvalOrder,n.multiple_reviewers_type as multipleReviewersType").setRsType(ResultAuditFlowNodeRater.class)
                .whereEqReq("r.company_id", companyId)
                .whereEq("r.flow_instance_id", instanceId)
                .whereEq("r.is_deleted", Boolean.FALSE.toString());
        List<ResultAuditFlowNodeRater> nodeRaters = domainDao.listAll(qb);
        return nodeRaters;
    }


    private ResultAuditFlowInstance existFlow(String companyId, String md5Key) {
        ComQB qb = ComQB.build(ResultAuditFlowInstanceDo.class, "i")
                .leftJoin(ResultAuditFlowUserDo.class, "u").appendOn("i.company_id = u.company_id and i.id = u.flow_instance_id")
                .whereEqReq("i.company_id", companyId)
                .whereEq("i.md5_key", md5Key)
                .whereEq("i.is_deleted", Boolean.FALSE.toString())
                .whereEq("u.is_deleted", Boolean.FALSE.toString());
        ResultAuditFlowInstance instance = domainDao.findDomain(qb, ResultAuditFlowInstance.class);
        return instance;
    }
}
