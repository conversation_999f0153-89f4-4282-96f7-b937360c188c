package com.polaris.kpi.report.infr.dao;

import com.polaris.kpi.eval.infr.task.ppojo.CycleDo;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskBaseDo;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskUserDo;
import com.polaris.kpi.report.infr.pojo.EmpYearReportItemDo;
import org.lufei.ibatis.builder.Builder;
import org.lufei.ibatis.builder.ComQB;

import java.util.Arrays;
import java.util.List;

public class ReportSqlBuilder {
    private boolean isAvg;
    private Integer year;
    private List<String> empIds;
    private ComQB comQB;

    public ReportSqlBuilder(Integer year, List<String> empIds, boolean isAvg) {
        this.year = year;
        this.empIds = empIds;
        this.isAvg = isAvg;
    }

    public void createQuerySql(String companyId, Integer performanceType) {
        ComQB fromQb = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(PerfEvaluateTaskBaseDo.class, "b").appendOn("u.company_id = b.company_id and u.task_id = b.id and b.is_deleted = 'false' and u.is_deleted='false'")
                .join(CycleDo.class, "c").appendOn(" b.company_id = c.company_id and  b.cycle_id = c.id").clearSelect()
                .select("u.evaluation_level  as level, u.perf_coefficient  as coefficient, ifnull(u.score_of_ref,u.final_score) as final_score ")
                .select("u.emp_id,u.company_id, u.eval_org_name as evalOrgName, c.type as type,c.year as year, c.value as value ")
                .whereEq("u.company_id", companyId)
                .whereIn("u.task_status", Arrays.asList("finished,resultsAuditing,resultsAffirming,waitPublish".split(",")))
                .whereIn("u.emp_id", empIds)
                .whereEq("b.performance_type", performanceType)
                .whereEq("c.year", year);

        comQB = ComQB.build().fromQ(fromQb, "t1").clearSelect().setRsType(EmpYearReportItemDo.class)
                .select("t1.emp_id,t1.company_id ,t1.year")
                .select(jsonObjectResult("year", year))
                .select(jsonObjectResult("half_year", 1))
                .select(jsonObjectResult("half_year", 2))
                .select(jsonObjectResult("quarter", 1))
                .select(jsonObjectResult("quarter", 2))
                .select(jsonObjectResult("quarter", 3))
                .select(jsonObjectResult("quarter", 4));
        for (int i = 1; i <= 12; i++) {
            comQB.select(jsonObjectResult("month", i));
        }
        comQB.groupBy("t1.emp_id");
    }

    public String jsonObjectResult(String type, Integer value) {
        String fun = isAvg ? "AVG" : "SUM";
        String caseWhen = String.format("CASE WHEN type = '%s' and value = %s THEN ", type, value);

        String cnt = String.format("sum(" + caseWhen + " 1 END)", fun);
        String score = String.format("%s(" + caseWhen + " final_score END)", fun);
        String level = "max(" + caseWhen + "level END)";
        String coefficient = "max(" + caseWhen + " coefficient END)";
        String asName = type + (type.equals("year") ? 1 : value);
        String selectRs = String.format("json_object('fieldName','%s', 'cnt',%s,'score',%s, 'level',%s,'coefficient',%s ) as  %s ", asName, cnt, score, level, coefficient, asName);
        return selectRs;
    }

    public Builder getComQb() {
        return comQB;
    }

    public static void main(String[] args) {
        ReportSqlBuilder builder = new ReportSqlBuilder(2024, Arrays.asList("1001"), true);
        builder.createQuerySql("100012", 1);
        System.out.println(builder.getComQb().getSql());
    }
}
