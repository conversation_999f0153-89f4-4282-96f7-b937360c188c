package com.polaris.kpi.report.infr.pojo;

import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.eval.domain.task.entity.empeval.ComputeFinishValueProgress;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2025/5/12 17:07
 */
@Data
public class ItemFinishValueComparePo {

    private String itemId; //指标ID
    private String itemName; //指标名称
    @JSONField(serialize = false)
    private Integer finishValueType; //
    private BigDecimal totalFinishValue; //总完成值
//    @JSONField(serialize = false)
    private BigDecimal lastYearTotalFinishValue;
//    @JSONField(serialize = false)
    private BigDecimal lastQuarterTotalFinishValue;
    private String itemUnit; //指标单位
    private BigDecimal yoy; //同比
    private BigDecimal qoq; //环比
    private Integer itemOrder; //指标排序

    @JSONField(serialize = false)
    public void calcYoy() {
        if (lastYearTotalFinishValue != null && lastYearTotalFinishValue.compareTo(BigDecimal.ZERO) != 0 && totalFinishValue != null) {
            //负向指标分母加绝对值
            yoy = totalFinishValue.subtract(lastYearTotalFinishValue).divide(lastYearTotalFinishValue.abs(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
        }
    }

    @JSONField(serialize = false)
    public void calcQoq() {
        if (lastQuarterTotalFinishValue != null && lastQuarterTotalFinishValue.compareTo(BigDecimal.ZERO) != 0 && totalFinishValue != null) {
            qoq = totalFinishValue.subtract(lastQuarterTotalFinishValue).divide(lastQuarterTotalFinishValue.abs(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
        }
    }

    public void acceptFinishValue(ItemFinishValuePo current, ItemFinishValuePo lastYear, ItemFinishValuePo lastQuarter) {
        this.totalFinishValue = current.getTotalFinishValue();
        this.finishValueType = current.getFinishValueType();
        this.lastYearTotalFinishValue = lastYear != null ? lastYear.getTotalFinishValue() : null;
        this.lastQuarterTotalFinishValue = lastQuarter != null ? lastQuarter.getTotalFinishValue() : null;
    }
}
