package com.polaris.kpi.eval.infr.task.ppojo.open.eval;

import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrKeyResult;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskScoreResultDo;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.EmpEvalTaskScore2Po;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: xuxw
 * @Date: 2025/01/23 13:55
 * @Description:
 */
@Setter
@Getter
public class OpenKpiItemPo {
    // 指标ID
    private String kpiItemId;
    // 指标项名称
    private String kpiItemName;
    // 指标单位
    private String kpiItemUnit;
    // 指标项权重
    private BigDecimal kpiItemWeight;
    // 指标项规则
    private String kpiItemRule;
    // 指标项评分规则
    private String kpiItemScoringRule;
    // 指标项自定义字段JSON
    private String kpiItemCustomFieldJson;
    // 指标项目标值
    private BigDecimal kpiItemTargetValue;
    // 指标项完成值
    private BigDecimal kpiItemFinishValue;
    // 指标完成进度 kpiItemFinishValue/kpiItemTargetValue + "%"
    private String kpiItemFinishSchedule;
    // 指标完成情况
    private OpenKpiItemFilesPo kpiItemFinishSituation;
    // 1-显示进度，0-显示完成情况
    private Integer showFinishBar;
    // 指标项最终得分
    private BigDecimal kpiItemFinalScore;
    // 指标项自评得分
    private OpenKpiItemScorePo kpiItemSelfScore;
    // 指标项互评得分
    private OpenKpiItemScorePo kpiItemPeerScore;
    // 指标项下级评分
    private OpenKpiItemScorePo kpiItemSubScore;
    // 指标项上级评分
    private OpenKpiItemScorePo kpiItemSupScore;
    // 指标项指定评分
    private OpenKpiItemScorePo kpiItemAppointScore;
    private String keyResultId;
    private OkrKeyResult keyResult;

    public void buildKpiItemScore(EmpEvalTaskScore2Po.TaskUserKpiItemScore kpiItemScore,
                                  List<PerfEvaluateTaskScoreResultDo> scoreResultDos, String itemId, Map<String, String> empIdMap) {
        this.initKpiItemScore();

        OpenKpiItemScorePo scorePo;
        if (CollUtil.isNotEmpty(scoreResultDos)){
            Map<String, List<PerfEvaluateTaskScoreResultDo>> scoreTypeRsMap = scoreResultDos.stream().collect(Collectors.groupingBy(PerfEvaluateTaskScoreResultDo::getScorerType));
            if (ObjectUtil.isNotNull(kpiItemScore.getAppointScore())){
                scorePo = createOpenKpiItemScorePo(scoreTypeRsMap, SubScoreNodeEnum.APPOINT_SCORE.getScene(), kpiItemScore.getAppointScore(), itemId, empIdMap);
                this.kpiItemAppointScore = scorePo;
            }
            if (ObjectUtil.isNotNull(kpiItemScore.getSubScore())){
                scorePo = createOpenKpiItemScorePo(scoreTypeRsMap, SubScoreNodeEnum.SUB_SCORE.getScene(), kpiItemScore.getSubScore(), itemId, empIdMap);
                this.kpiItemSubScore = scorePo;
            }
            if (ObjectUtil.isNotNull(kpiItemScore.getSupScore())){
                scorePo = createOpenKpiItemScorePo(scoreTypeRsMap, SubScoreNodeEnum.SUPERIOR_SCORE.getScene(), kpiItemScore.getSupScore(), itemId, empIdMap);
                this.kpiItemSupScore = scorePo;
            }
            if (ObjectUtil.isNotNull(kpiItemScore.getPeerScore())){
                scorePo = createOpenKpiItemScorePo(scoreTypeRsMap, SubScoreNodeEnum.PEER_SCORE.getScene(), kpiItemScore.getPeerScore(), itemId, empIdMap);
                this.kpiItemPeerScore = scorePo;
            }
            if (ObjectUtil.isNotNull(kpiItemScore.getSelfScore())){
                scorePo = createOpenKpiItemScorePo(scoreTypeRsMap, SubScoreNodeEnum.SELF_SCORE.getScene(), kpiItemScore.getSelfScore(), itemId, empIdMap);
                this.kpiItemSelfScore = scorePo;
            }

        }
    }

    private void initKpiItemScore() {
        OpenKpiItemScorePo scorePo = new OpenKpiItemScorePo();
        scorePo.setIsOpen(false);
        this.kpiItemAppointScore = scorePo;
        this.kpiItemSubScore = scorePo;
        this.kpiItemSupScore = scorePo;
        this.kpiItemPeerScore = scorePo;
        this.kpiItemSelfScore = scorePo;
    }

    private OpenKpiItemScorePo createOpenKpiItemScorePo(Map<String, List<PerfEvaluateTaskScoreResultDo>> scoreTypeRsMap,
                                                        String scene, BigDecimal score, String itemId, Map<String, String> empIdMap) {
        OpenKpiItemScorePo scorePo = new OpenKpiItemScorePo();
        List<PerfEvaluateTaskScoreResultDo> resultDos = scoreTypeRsMap.get(scene);
        List<OpenKpiItemRatersPo> ratersPos = new ArrayList<>();

        if (CollUtil.isNotEmpty(resultDos)) {
            for (PerfEvaluateTaskScoreResultDo resultDo : resultDos) {
                if (StrUtil.equals(resultDo.getKpiItemId(), itemId)){
                    OpenKpiItemRatersPo ratersPo = new OpenKpiItemRatersPo();
                    ratersPo.setScorerId(resultDo.getScorerId());
                    ratersPo.setScorerName(empIdMap.get(resultDo.getScorerId()));
                    ratersPo.setScore(resultDo.getScore());
                    ratersPo.setScoreComment(resultDo.getScoreComment());
                    ratersPos.add(ratersPo);
                }
            }
        }
        scorePo.setItemScore(score);
        scorePo.setIsOpen(true);
        scorePo.setRaters(ratersPos);
        return scorePo;
    }
}
