package com.polaris.kpi.notice.dao;

import com.polaris.kpi.notice.ppojo.AnnouncementDo;
import com.polaris.kpi.notice.ppojo.EmpRefAnnouncementDo;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.dao.AutoBaseDao;
import org.lufei.ibatis.mapper.PagedList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class AnnouncementDao {
    @Autowired
    private AutoBaseDao dao;

    public PagedList<AnnouncementDo> pageWaitReaderAnnouncement(String announcementId, String empId, int pageNo, int pageSize) {
        ComQB comQB = ComQB.build(AnnouncementDo.class, "a")
                .leftJoin(EmpRefAnnouncementDo.class, "ea")
                .appendOn("a.id=ea.announcement_id and ea.emp_id ='" + empId + "'")
                .clearSelect()
                .select(" a.id,a.content,if(ea.announcement_id is null,false,true) as isReader,a.created_time")
                .setRsType(AnnouncementDo.class)
                .whereEq("a.id", announcementId)
                .groupBy(" a.id ")
                .orderByDesc(" a.created_time ")
                .setPage(pageNo, pageSize);
        return dao.listPage(comQB);
    }
}
