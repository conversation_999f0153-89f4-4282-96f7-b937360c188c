package com.polaris.kpi.common.infr;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR> luf<PERSON>
 * @date 2022/3/22 7:47 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BaseTableData extends DataObj {
    //@DPS (value = EmpId.class, dmFname = "createdUser")
    protected String createdUser;//创建用户
    protected Date createdTime;//创建时间
    //@DPS (value = EmpId.class, dmFname = "updatedUser")
    protected String updatedUser;//修改用户
    protected Date updatedTime;//修改时间
}
