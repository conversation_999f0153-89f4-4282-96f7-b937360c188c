package com.polaris.kpi.eval.infr.task.repimpl;

import cn.com.polaris.kpi.eval.EvalReviewers;
import cn.com.polaris.kpi.eval.SummaryScoreReviewer;
import cn.hutool.core.collection.CollUtil;
import com.google.common.base.Supplier;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.empeval.ChainDispatchRs;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.repo.ScorerSummaryTodoRepo;
import com.polaris.kpi.eval.infr.task.ppojo.ScorerTodoSummaryDo;
import com.polaris.kpi.eval.infr.task.ppojo.TaskUserScorerDo;
import com.polaris.kpi.org.infr.emp.pojo.EmployeeBaseInfoDo;
import com.polaris.kpi.setting.builder.ScorerInstanceBuilder;
import com.polaris.kpi.setting.domain.entity.ScorerTodoSummary;
import com.polaris.kpi.setting.domain.entity.TaskUserScorer;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.MapWrap;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.lufei.ibatis.builder.BatchUpdateBuilder;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.common.data.ToDataBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/18 15:47
 */
@Component
@Slf4j
public class ScorerSummaryTodoRepoImpl implements ScorerSummaryTodoRepo {

    public static final String taskUserScorerSeq = "task_user_scorer_seq";
    public static final String scorerSummaryTodoSeq = "scorer_summary_todo_seq";

    @Autowired
    public void setDomainDao(DomainDaoImpl domainDao) {
        this.domainDao = domainDao;
    }

    private DomainDaoImpl domainDao;

    final Supplier<String> instanceIdGen = () -> domainDao.nextLongAsStr(taskUserScorerSeq);

    @Override
    public Set<String> saveScorerInstance(ChainDispatchRs chainDispatchRs, EvalUser evalUser, EmpEvalMerge evalMerge) {

        ScorerInstanceBuilder builder = new ScorerInstanceBuilder(evalUser.getCompanyId().getId(), evalUser.getEmpId(), instanceIdGen, chainDispatchRs, evalUser, evalMerge);
        builder.buildV3();
        List<TaskUserScorerDo> instances = builder.getInstances();

        domainDao.saveBatch(instances);
        return instances.stream().map(TaskUserScorerDo::getScorerId).collect(Collectors.toSet());
    }

    @Override
    public List<String> queryScorerIds(EvalUser evalUser) {

        ComQB qb = ComQB.build(TaskUserScorerDo.class, "a")
                .clearSelect()
                .select("a.scorer_id")
                .setRsType(String.class)
                .whereEqReq("a.company_id", evalUser.getCompanyId().getId())
                .whereEqReq("a.task_user_id", evalUser.getId())
                .whereEqReq("a.is_deleted", "false");
        return domainDao.listAll(qb);
    }

    public List<String> listScorerIds(TenantId companyId, List<String> taskUserIds) {
        ComQB qb = ComQB.build(TaskUserScorerDo.class, "a")
                .clearSelect()
                .select("distinct a.scorer_id")
                .setRsType(String.class)
                .whereEqReq("a.company_id", companyId.getId())
                .whereInReq("a.task_user_id", taskUserIds)
                .whereEqReq("a.is_deleted", "false");
        return domainDao.listAll(qb);
    }

    @Override
    public void addOrUpdateAfterSendRemoteTodo(ScorerTodoSummary summary) {

        if (summary.isNew()) {
            summary.init(domainDao.nextLongAsStr(scorerSummaryTodoSeq));
            ScorerTodoSummaryDo data = new ToDataBuilder<>(summary, ScorerTodoSummaryDo.class).data();
            domainDao.save(data);
        } else {
            UpdateBuilder up = UpdateBuilder.build(ScorerTodoSummaryDo.class)
                    .set("todo_status", summary.getTodoStatus())
                    .set("third_msg_id", summary.getThirdMsgId())
                    .set("updated_time", new Date())
                    .set("version", summary.getVersion() + 1)
                    .whereEqReq("company_id", summary.getCompanyId())
                    .whereEqReq("id", summary.getId())
                    .whereEqReq("is_deleted", "false")
                    .whereEqReq("version", summary.getVersion());
            domainDao.update(up);
        }
    }

    @Override
    public void closeScorerCompanyMsg(ScorerTodoSummary updateSummary) {

        UpdateBuilder up = UpdateBuilder.build(TaskUserScorerDo.class)
                .set("company_msg_status", 1)
                .set("updated_time", new Date())
                .whereEqReq("company_id", updateSummary.getCompanyId())
                .whereEqReq("task_id", updateSummary.getTaskId())
                .whereEqReq("scorer_id", updateSummary.getScorerId())
                .whereEqReq("company_msg_status", 2)
                .whereEqReq("is_deleted", "false");
        domainDao.update(up);
    }

    @Override
    public void sendScorerCompanyMsg(List<TaskUserScorer> scorers, ScorerTodoSummary summary) {

        UpdateBuilder up = UpdateBuilder.build(TaskUserScorerDo.class)
                .set("company_msg_status", 2)
                .set("updated_time", new Date())
                .whereEqReq("company_id", summary.getCompanyId())
                .whereEqReq("task_id", summary.getTaskId())
                .whereEqReq("scorer_id", summary.getScorerId())
                .whereIn("id", scorers.stream().map(TaskUserScorer::getId).collect(Collectors.toList()))
                .whereEqReq("company_msg_status", 1)
                .whereEqReq("is_deleted", "false");
        domainDao.update(up);
    }

    @Override
    public Boolean lockSummary(ScorerTodoSummary summary) {
        UpdateBuilder up = UpdateBuilder.build(ScorerTodoSummaryDo.class)
                .set("version", summary.getVersion() + 1)
                .set("updated_time", new Date())
                .whereEqReq("company_id", summary.getCompanyId())
                .whereEqReq("id", summary.getId())
                .whereEqReq("version", summary.getVersion());
        int count = domainDao.update(up);
        summary.incrementVersion();
        return count > 0;
    }

    @Override
    public void batchResetSummaries(List<ScorerTodoSummary> batchUpdate) {
        if (CollUtil.isEmpty(batchUpdate)) {
            return;
        }
        List<ScorerTodoSummaryDo> scorerTodoSummaries = new ArrayList<>();
        for (ScorerTodoSummary scorerTodoSummary : batchUpdate) {
            ScorerTodoSummary summary = new ScorerTodoSummary();
            summary.copyBaseInfoFromOldOne(scorerTodoSummary);
            summary.init(domainDao.nextLongAsStr(scorerSummaryTodoSeq));
            ScorerTodoSummaryDo summaryDo = new ToDataBuilder<>(summary, ScorerTodoSummaryDo.class).data();
            scorerTodoSummaries.add(summaryDo);
        }
        domainDao.saveBatch(scorerTodoSummaries);
        batchUpdateSummaries(batchUpdate);
    }

    @Override
    public ListWrap<SummaryScoreReviewer> reviewersListWrap(TenantId tenantId, List<String> taskUserIds) {
        if (CollUtil.isEmpty(taskUserIds)) {
            return new ListWrap<>();
        }
        //只查待汇总的，汇总后的原有可以正常显示
        ComQB subQuery1 = ComQB.build(TaskUserScorerDo.class);
        subQuery1.clearSelect().select("distinct scorer_id")
                .setRsType(String.class)
                .whereIn("task_user_id", taskUserIds)
                .whereEqReq("is_deleted", "false")
                .whereEqReq("company_id", tenantId.getId());
        List<String> scorerIds = domainDao.listAll(subQuery1);

        ComQB subQuery2 = ComQB.build(ScorerTodoSummaryDo.class);
        subQuery2.clearSelect().select("scorer_id,task_id")
                .setRsType(String.class)
                .whereIn("scorer_id", scorerIds)
                .whereEqReq("is_deleted", "false")
                .whereEqReq("todo_status", 0)
                .whereEqReq("company_id", tenantId.getId())
                .groupBy("scorer_id,task_id");

        ComQB comQB = ComQB.build().fromQ(subQuery2, "a")
                .join(TaskUserScorerDo.class, "b")
                .appendOn("a.scorer_id = b.scorer_id and a.task_id = b.task_id")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn("b.company_id = e.company_id and b.scorer_id = e.employee_id")
                .clearSelect()
                .select("e.employee_id  as emp_id, e.name as emp_name,e.avatar,e.ding_user_id as ex_user_id,e.jobnumber")
                .select("b.task_user_id,b.company_id")
                .select(" 'waitSummary' as summaryStatus")
                .setRsType(SummaryScoreReviewer.class)
                .whereEqReq("b.company_id", tenantId.getId())
                .whereEqReq("b.is_deleted", "false")
                .whereIn("b.task_user_id", taskUserIds);

        List<SummaryScoreReviewer> emps = domainDao.listAll(comQB);
        return new ListWrap<>(emps).groupBy(EvalReviewers::getTaskUserId);
    }

    @Override
    public List<ScorerTodoSummary> queryNeedCloseRemoteSummaries(String tenantId, String taskId) {

        ComQB comQB = ComQB.build(ScorerTodoSummaryDo.class, "a")
                .clearSelect()
                .select("a.*")
                .setRsType(ScorerTodoSummary.class)
                .whereEqReq("a.company_id", tenantId)
                .whereEqReq("a.task_id", taskId)
                .whereEq("a.todo_status", 1)
                .whereEq("a.is_deleted", "false");
        return domainDao.listAll(comQB);
    }

    @Override
    public void batchUpdateSummaries(List<ScorerTodoSummary> updateSummaries) {

        if (updateSummaries.isEmpty()) {
            return;
        }

        BatchUpdateBuilder updateBuilder = new BatchUpdateBuilder("scorer_todo_summary")
                .addSetCaseProp("needCnt", "id:=")
                .addSetCaseProp("readyCnt", "id:=")
                .addSetCaseProp("scoredCnt", "id:=")
                .addSetCaseProp("todoStatus", "id:=")
                .addSetCaseProp("thirdMsgId", "id:=")
                .addSetCaseProp("updatedTime", "id:=")
                .addAllBean(updateSummaries)
                .whereEq("companyId", updateSummaries.get(0).getCompanyId())
                .whereUseIn("id");

        domainDao.updateBatch(updateBuilder);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public Set<String> dispatchScorerInstance(ChainDispatchRs chainDispatchRs, EvalUser evalUser, EmpEvalMerge evalMerge) {

        ScorerInstanceBuilder builder = new ScorerInstanceBuilder(evalUser.getCompanyId().getId(), evalUser.getEmpId(), instanceIdGen, chainDispatchRs, evalUser, evalMerge);
        builder.buildV3();
        List<TaskUserScorerDo> instances = builder.getInstances();
        Set<String> scorerIds = instances.stream().map(TaskUserScorerDo::getScorerId).collect(Collectors.toSet());
        UpdateBuilder up = UpdateBuilder.build(TaskUserScorerDo.class)
                .set("ready_status", 2)
                .set("updated_time", new Date())
                .whereEqReq("company_id", evalUser.getCompanyId().getId())
                .whereEqReq("task_user_id", evalUser.getId())
                .whereEqReq("ready_status", 1)
                .whereEqReq("is_deleted", "false");
        domainDao.update(up);
        return scorerIds;
    }

    @Override
    public ScorerTodoSummary countSummary(String scorerId, String companyId, String taskId) {
        //查询评分人的needCnt 和 readyCnt
        ComQB qb = ComQB.build(TaskUserScorerDo.class, "a")
                .clearSelect()
                .select("count(1) as needCnt, " +
                        "count(CASE WHEN a.ready_status = 2 THEN 1 ELSE NULL END) as readyCnt," +
                        "count(CASE WHEN a.score_status = 2 THEN 1 ELSE NULL END) as scoredCnt")
                .setRsType(ScorerTodoSummary.class)
                .whereEqReq("a.scorer_id", scorerId)
                .whereEqReq("a.company_id", companyId)
                .whereEqReq("a.is_deleted", "false")
                .whereEqReq("a.task_id", taskId);

        return domainDao.findDomain(qb, ScorerTodoSummary.class);
    }


    @Override
    public List<ScorerTodoSummary> listCountSummary(Set<String> scorerIds, String companyId, String taskId) {
        //查询评分人的needCnt 和 readyCnt
        ComQB qb = ComQB.build(TaskUserScorerDo.class, "a")
                .clearSelect()
                .select("a.scorer_id")
                .select("count(1) as needCnt, " +
                        "count(CASE WHEN a.ready_status = 2 THEN 1 ELSE NULL END) as readyCnt," +
                        "count(CASE WHEN a.score_status = 2 THEN 1 ELSE NULL END) as scoredCnt")
                .setRsType(ScorerTodoSummary.class)
                .whereInReq("a.scorer_id", scorerIds)
                .whereEqReq("a.company_id", companyId)
                .whereEqReq("a.is_deleted", "false")
                .whereEqReq("a.task_id", taskId)
                .groupBy("scorer_id");

        return domainDao.listAll(qb);
    }

    @Override
    public MapWrap<String, ScorerTodoSummary> listScorerTodoSummary(List<String> taskUserIds, String companyId, String taskId) {

        List<String> scorerIds = this.listScorerIds(new TenantId(companyId), taskUserIds);
        if (CollUtil.isEmpty(scorerIds)) {
            return new MapWrap<>(new ArrayList<>(), scorerTodoSummary -> scorerTodoSummary.getScorerId());
        }
        //查询评分人的needCnt 和 readyCnt
        ComQB qb = ComQB.build(TaskUserScorerDo.class, "a")
                .clearSelect()
                .select("count(1) as needCnt, " +
                        "count(CASE WHEN a.ready_status = 2 THEN 1 ELSE NULL END) as readyCnt," +
                        "count(CASE WHEN a.score_status = 2 THEN 1 ELSE NULL END) as scoredCnt")
                .setRsType(ScorerTodoSummary.class)
                .whereInReq("a.scorer_id", scorerIds)
                .whereEqReq("a.company_id", companyId)
                .whereEqReq("a.is_deleted", "false")
                .whereEqReq("a.task_id", taskId)
                .groupBy("a.scorer_id");
        List<ScorerTodoSummary> summaries = domainDao.listAllDomain(qb, ScorerTodoSummary.class);
        MapWrap<String, ScorerTodoSummary> sumWithMsgIds = new MapWrap<>(listScorerSummary(companyId, taskId, scorerIds), summary -> summary.getScorerId());
        List<ScorerTodoSummary> noneNulls = summaries.stream().filter(summary -> sumWithMsgIds.containsKey(summary.getScorerId())).collect(Collectors.toList());
        noneNulls.forEach(summary -> summary.copyFromOldOne(sumWithMsgIds.get(summary.getScorerId())));
        return new MapWrap<>(noneNulls, scorerTodoSummary -> scorerTodoSummary.getScorerId());
    }

    @Override
    public ScorerTodoSummary getScorerSummary(String companyId, String taskId, String scorerId) {
        ComQB comQB = ComQB.build(ScorerTodoSummaryDo.class, "a")
                .clearSelect()
                .select("a.*")
                .setRsType(ScorerTodoSummary.class)
                .whereEq("a.company_id", companyId)
                .whereEq("a.task_id", taskId)
                .whereEq("a.scorer_id", scorerId)
                .whereNotEq("a.todo_status", 2)
                .whereEq("a.is_deleted", "false");
        return domainDao.findOne(comQB);
    }

    @Override
    public List<ScorerTodoSummary> listScorerSummary(String companyId, String taskId, Set<String>  scorerIds) {

        ComQB comQB = ComQB.build(ScorerTodoSummaryDo.class, "a")
                .clearSelect()
                .select("a.*")
                .setRsType(ScorerTodoSummary.class)
                .whereEqReq("a.company_id", companyId)
                .whereEqReq("a.task_id", taskId)
                .whereInReq("a.scorer_id", scorerIds)
                .whereNotEq("a.todo_status", 2)
                .whereEq("a.is_deleted", "false");
        return domainDao.listAll(comQB);
    }



    public List<ScorerTodoSummary> listScorerSummary(String companyId, String taskId, List<String> scorerIds) {
        ComQB comQB = ComQB.build(ScorerTodoSummaryDo.class, "a")
                .clearSelect()
                .select("a.*")
                .setRsType(ScorerTodoSummary.class)
                .whereEq("a.company_id", companyId)
                .whereEq("a.task_id", taskId)
                .whereInReq("a.scorer_id", scorerIds)
                .whereNotEq("a.todo_status", 2)
                .whereEq("a.is_deleted", "false");
        return domainDao.findOne(comQB);
    }

    @Override
    public void updateFinishScoreStatus(String companyId, String taskUserId, Set<String> scorerIds) {

        UpdateBuilder up = UpdateBuilder.build(TaskUserScorerDo.class)
                .set("score_status", 2)
                .set("updated_time", new Date())
                .set("company_msg_status", 1)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereIn("scorer_id", scorerIds)
                .whereEqReq("is_deleted", "false");
        domainDao.update(up);
    }

    @Override
    public List<TaskUserScorer> queryNeedSendSystemTodoScorers(String companyId, String taskId, String scorerId) {
        ListWrap<TaskUserScorer> listTodoScorers = this.listTodoScorers(companyId, taskId, Arrays.asList(scorerId));
        return listTodoScorers.getDatas();
    }

    public ListWrap<TaskUserScorer> listTodoScorers(String companyId, String taskId, List<String> scorerIds) {
        ComQB qb = ComQB.build(TaskUserScorerDo.class, "a")
                .clearSelect()
                .select("a.*")
                .setRsType(TaskUserScorer.class)
                .appendWhere(" !exists (select * from company_msg_center where company_id = a.company_id and link_id = a.task_user_id and emp_id = a.scorer_id  and handler_status = 'false' " +
                        " and business_scene in ('task_wait_self_score') )")
                .whereInReq("a.scorer_id", scorerIds)
                .whereEqReq("a.company_id", companyId)
                .whereEqReq("a.task_id", taskId)
                .whereEqReq("a.ready_status", 2)//已就绪
                .whereEqReq("a.score_status", 1)//未评分
                .whereEqReq("a.company_msg_status", 1) //不存在系统待办
                .whereEqReq("a.is_deleted", "false");
        List<TaskUserScorer> scorers = domainDao.listAll(qb);
        return new ListWrap<>(scorers).groupBy(s -> s.getScorerId());
    }

    @Override
    public void removeScorer(String taskUserId, String skipUserId) {

        UpdateBuilder up = UpdateBuilder.build(TaskUserScorerDo.class)
                .set("is_deleted", "true")
                .set("updated_time", new Date())
                .whereEqReq("task_user_id", taskUserId)
                .whereEqReq("scorer_id", skipUserId)
                .whereEqReq("is_deleted", "false");
        domainDao.update(up);

    }

    @Override
    public void addScorer(TaskUserScorer scorer) {

        //如果存在相关实例，就不新增了
        ComQB comQB = ComQB.build(TaskUserScorerDo.class, "a")
                .clearSelect()
                .select("id")
                .whereEqReq("a.company_id", scorer.getCompanyId())
                .whereEqReq("a.task_id", scorer.getTaskUserId())
                .whereEqReq("a.scorer_id", scorer.getScorerId())
                .whereEqReq("a.is_deleted", "false");

        if (Objects.nonNull(domainDao.findOne(comQB))) {
            return;
        }

        scorer.init(domainDao.nextLongAsStr(taskUserScorerSeq));
        TaskUserScorerDo scorerDo = new ToDataBuilder<>(scorer, TaskUserScorerDo.class).data();
        domainDao.save(scorerDo);
    }

    @Override
    public Set<String> removeScorerByTaskUser(String companyId, String taskUserId) {

        ComQB qb = ComQB.build(TaskUserScorerDo.class, "a")
                .clearSelect()
                .select("a.scorer_id")
                .setRsType(String.class)
                .whereEqReq("a.company_id", companyId)
                .whereEqReq("a.task_user_id", taskUserId)
                .whereEqReq("a.is_deleted", "false");

        List<String> scorerIds = domainDao.listAll(qb);

        UpdateBuilder up = UpdateBuilder.build(TaskUserScorerDo.class)
                .set("is_deleted", "true")
                .set("updated_time", new Date())
                .whereEqReq("task_user_id", taskUserId)
                .whereEqReq("company_id", companyId)
                .whereEqReq("is_deleted", "false");
        domainDao.update(up);

        return new HashSet<>(scorerIds);
    }

    @Override
    public void batchAddSummaries(List<ScorerTodoSummary> addSummaries) {

        if (addSummaries.isEmpty()) {
            return;
        }

        List<ScorerTodoSummaryDo> collect = addSummaries.stream().map(addSummary -> {
            addSummary.init(domainDao.nextLongAsStr(scorerSummaryTodoSeq));
            return new ToDataBuilder<>(addSummary, ScorerTodoSummaryDo.class).data();
        }).collect(Collectors.toList());

        domainDao.saveBatch(collect);
    }

    @Override
    public void resetScorerScoreStatus(String companyId, String taskUserId, Set<String> scorerIdsSet) {

        UpdateBuilder up = UpdateBuilder.build(TaskUserScorerDo.class)
                .set("score_status", 1)
                .set("company_msg_status", 1)
                .set("updated_time", new Date())
                .whereEqReq("task_user_id", taskUserId)
                .whereEqReq("company_id", companyId)
                .whereIn("scorer_id", scorerIdsSet)
                .whereEqReq("is_deleted", "false");
        domainDao.update(up);

    }

    @Override
    public void resetScorerScoreStatusByTaskUser(EvalUser taskUser) {

        UpdateBuilder up = UpdateBuilder.build(TaskUserScorerDo.class)
                .set("score_status", 1)
                .set("company_msg_status", 1)
                .set("updated_time", new Date())
                .whereEqReq("task_user_id", taskUser.getId())
                .whereEqReq("company_id", taskUser.getCompanyId().getId())
                .whereEqReq("is_deleted", "false");
        domainDao.update(up);
    }

    @Override
    public void resetScorerStatusByTaskUser(EvalUser taskUser) {

        UpdateBuilder up = UpdateBuilder.build(TaskUserScorerDo.class)
                .set("score_status", 1)
                .set("ready_status", 1)
                .set("company_msg_status", 1)
                .set("updated_time", new Date())
                .whereEqReq("task_user_id", taskUser.getId())
                .whereEqReq("company_id", taskUser.getCompanyId().getId())
                .whereEqReq("is_deleted", "false");
        domainDao.update(up);
    }


}
