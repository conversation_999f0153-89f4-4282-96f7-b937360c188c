//package com.polaris.kpi.report.infr.pojo;
//
///**
// * <AUTHOR>
// * @date 2025/2/8 17:32
// */
//
//import com.polaris.kpi.common.infr.BaseWithCompanyIdData;
//import lombok.AllArgsConstructor;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//import lombok.experimental.Accessors;
//import org.apache.ibatis.annotations.Ckey;
//import org.apache.ibatis.annotations.Table;
//
//
//@Data
//@AllArgsConstructor
//@NoArgsConstructor
//@Accessors(chain = true)
//@Table("cycle_org_snap")
//public class CycleOrgSnapDo extends BaseWithCompanyIdData {
//
//    @Ckey
//    private String id;
//    private String cycleId;
//    private String orgId;
//    private String orgName;
//    private String parentOrgId;
//
//}
