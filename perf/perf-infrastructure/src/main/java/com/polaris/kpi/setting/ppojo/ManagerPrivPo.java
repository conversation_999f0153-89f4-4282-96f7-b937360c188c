package com.polaris.kpi.setting.ppojo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

@Getter
@Setter
public class ManagerPrivPo extends ManagerPrivDo{
    private String managerEmpName;
    private String orgName;  //所属部门

    @JSONField(serialize = false)
    public boolean isManager() {
        return (getManagerType() & 1) > 0;
    }

    @JSONField(serialize = false)
    public boolean isManagerPriv() {
        if (Objects.isNull(getPrivConf())) {
            return false;
        }
        if (Objects.isNull(getPrivConf().getIsOrgPriv())) {
            return false;
        }
        return getPrivConf().getIsOrgPriv();
    }

    @JSONField(serialize = false)
    public boolean isSuperPriv() {
        if (Objects.isNull(getPrivConf())) {
            return false;
        }
        if (Objects.isNull(getPrivConf().getIsSuperPriv())) {
            return false;
        }
        return getPrivConf().getIsSuperPriv();
    }
}
