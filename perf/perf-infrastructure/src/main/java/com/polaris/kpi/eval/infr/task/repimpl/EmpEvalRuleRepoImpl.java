package com.polaris.kpi.eval.infr.task.repimpl;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.com.polaris.kpi.eval.ItemCustomFieldValue;
import cn.com.polaris.kpi.eval.KpiItemUsedField;
import cn.com.polaris.kpi.eval.Rater;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.polaris.acl.dept.pojo.AdminSetDo;
import com.polaris.acl.dept.pojo.role.RoleDo;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.empeval.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.chain.ChainNode;
import com.polaris.kpi.eval.domain.task.event.admineval.EvalTaskErrorEvent;
import com.polaris.kpi.eval.domain.task.repo.BatchUpdateEvalErrorStatus;
import com.polaris.kpi.eval.domain.task.repo.EmpEvalRuleRepo;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.eval.infr.task.builder.*;
import com.polaris.kpi.eval.infr.task.dao.*;
import com.polaris.kpi.eval.infr.task.ppojo.*;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.EmpEvalOperationDo;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.*;
import com.polaris.kpi.org.domain.dept.entity.CompanyConf;
import com.polaris.kpi.org.domain.dept.type.TaskId;
import com.polaris.kpi.org.infr.company.dao.CompanyDaoImpl;
import com.polaris.kpi.org.infr.emp.pojo.EmployeeBaseInfoDo;
import com.polaris.kpi.org.infr.emp.pojo.RoleRefEmpDo;
import com.polaris.sdk.type.AuditEnum;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.lufei.ibatis.builder.BatchUpdateBuilder;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.DeleteBuilder;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.common.data.ToDataBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * @Author: lufei
 * @CreateTime: 2022-09-01  22:47
 * @Version: 1.0
 */
@Component
@Slf4j
public class EmpEvalRuleRepoImpl implements EmpEvalRuleRepo, BatchUpdateEvalErrorStatus {
    @Autowired
    private DomainDaoImpl domainDao;
    @Autowired
    private EmpEvalDao empEvalDao;
    @Autowired
    private EvalKpiDao kpiDao;
    @Autowired
    private AdminTaskDao taskDao;
    @Autowired
    private CompanyDaoImpl companyDao;
    @Autowired
    private TaskUserDao userDao;
    @Autowired
    private EmpEvalScorerDao scorerDao;
    @Autowired
    private EvaluateTaskDao evaluateTaskDao;

    public static final String diffLog = "log";
    private final Supplier<String> auditIdGen = () -> domainDao.nextLongAsStr(TaskUserRepoImpl.auditSeq);
    private final Supplier<String> evalCommonGen = () -> domainDao.nextLongAsStr(TaskUserRepoImpl.evalCommonSeq);
    private final Supplier<String> kpiItemGen = () -> domainDao.nextLongAsStr(TaskUserRepoImpl.kpiItemSeq);
    private final Supplier<String> formulaFieldGen = () -> domainDao.nextLongAsStr(formulaFieldSeq);

    private static final String formulaFieldSeq = "task_formula_field";


    public void setDomainDao(DomainDaoImpl domainDao) {
        this.domainDao = domainDao;
    }

    //查考核表 完全新加考核
    @Override
    public EmpEvalRule getEmpEvalRule(TenantId companyId, String taskUserId) {
        EmpEvalRule empEvalRule = empEvalDao.getEmpEvalRule(companyId, taskUserId);
        if (Objects.isNull(empEvalRule)) {
            return empEvalRule;
        }
        //录入人
        List<EvalKpi> kpiItems = empEvalRule.getKpiTypes().getDatas().stream().flatMap(type -> type.getItems().stream())
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(kpiItems)) {
            return empEvalRule;
        }
        Set<String> inpuEmpIds = kpiItems.stream().flatMap(evalKpi -> StrUtil.splitTrim(evalKpi.getResultInputEmpId(), ",")
                .stream()).collect(Collectors.toSet());
        ListWrap<KpiEmp> inpuEmpMap = kpiDao.listByEmp(companyId, inpuEmpIds);
        empEvalRule.getKpiTypes().getDatas().forEach(kpiType -> {
            kpiType.loadInputEmps(inpuEmpMap);
        });
        return empEvalRule;
    }

    @Override
    public EmpEvalMerge getEmpEvalMerge(TenantId companyId, String taskUserId, int mod) {
        return getEmpEvalMerge(companyId, taskUserId, null, mod, 0);
    }
    @Override
    public EmpEvalMerge getScoreEmpEvalMerge(TenantId companyId,String taskId, String taskUserId) {
        EmpEvalMerge evalRule = empEvalDao.getBaseEvalMerge(companyId, taskUserId);
        AdminTask task = taskDao.getAdminTaskBase(companyId,new TaskId(taskId));
        log.debug("getEmpEvalMerge.task={}", JSONUtil.toJsonStr(task));
        if (Objects.isNull(task)) {
            return null;
        }
        evalRule.setTaskName(task.getTaskName());
        evalRule.setTaskId(task.getId());
        KpiListWrap kpiTypes = kpiDao.getKpiTypeWrapV3(companyId, taskUserId);
        log.debug("getEmpEvalMerge.task.getScoreSortConf()={}", JSONUtil.toJsonStr(evalRule.getScoreSortConf()));
        //初始化s3到 指标上
        evalRule.extendsRaterRule(kpiTypes);
        //自定义满分分值 如果为null 则使用系统提供分值
        if (evalRule.getScoreValueConf().getCustomFullScore() == null) {
            BigDecimal fullScoreValue = companyDao.findFullScoreValue(companyId);
            evalRule.getScoreValueConf().setCustomFullScore(fullScoreValue);
        }
        if (evalRule.isSameTime()) {
            evalRule.initSameScoreChain();
        } else {
            evalRule.initTurnScoreChain();
        }
        evalRule.initSignatureFlag();//签名开关
        return evalRule;
    }

    @Override
    public EmpEvalMerge getScoreEmpEvalDetail(TenantId companyId, String taskUserId, String opEmpId) {
        EmpEvalMerge empEvalMerge = getEmpEvalMerge(companyId, taskUserId, null, EmpEvalMerge.all, 1);
        ListWrap<EvalScoreResult> total = kpiDao.listEvalEmpScoreRs(companyId, taskUserId, AuditEnum.totalScenes(), null);
        empEvalMerge.setTotalScoreRs(total);
        return empEvalMerge;
    }

    @Override
    public EmpEvalMerge getScoreEmpEvalDetailV3(TenantId companyId, String taskUserId, String opEmpId) {
        return getEmpEvalMerge(companyId, taskUserId, null, EmpEvalMerge.all, 1);
    }

    public EmpEvalMerge getEmpEvalMerge(TenantId companyId, String taskUserId, String opEmpId, int mod, int logMod) {
        EmpEvalMerge evalRule = empEvalDao.getBaseEvalMerge(companyId, taskUserId);
        List<BaseScoreResult> levelResults = kpiDao.listLevelTotal(companyId.getId(), taskUserId);
        AdminTask task = taskDao.getAdminTaskMerge(companyId, taskUserId);
        log.debug("getEmpEvalMerge.task={}", JSONUtil.toJsonStr(task));
        if (task == null) {
            return null;
        }
        KpiListWrap kpiTypes = kpiDao.listAndBuildKpiType(companyId, taskUserId, opEmpId, mod, logMod);
        //录入人
        List<EvalKpi> kpiItems = kpiTypes.getDatas().stream().flatMap(type -> type.getItems().stream())
                .collect(Collectors.toList());
        Set<String> inpuEmpIds = kpiTypes.getDatas().stream().flatMap(type -> type.getItems().stream())
                .flatMap(evalKpi -> StrUtil.splitTrim(evalKpi.getResultInputEmpId(), ",")
                        .stream()).collect(Collectors.toSet());
        ListWrap<KpiEmp> inpuEmpMap = kpiDao.listByEmp(companyId, inpuEmpIds);
        kpiItems.forEach(item -> {
            item.loadInputEmps(inpuEmpMap);
            item.initFinishValueSource();
        });

        if (Objects.isNull(evalRule) && task.isTmpTask()) {
            EvalUser user = userDao.getBaseEvalUser(companyId, taskUserId);
            evalRule = buildEvalRule(task, taskUserId);//转主阶段的配置
            TmpEvalConf2EmpEvalRuleBd builder = new TmpEvalConf2EmpEvalRuleBd(task.getTmpEvalFlow(), task.getId(), user.getEmpId(), companyId);
            //转化评分流程配置
            builder.buildTo(evalRule);
            loadItemRuleAudit(companyId, taskUserId, kpiTypes, evalRule.isCustom());
        } else {
            log.debug("getEmpEvalMerge.task.getScoreSortConf()={}", JSONUtil.toJsonStr(task.getScoreSortConf()));
            if (Objects.isNull(evalRule.getScoreSortConf())) {
                evalRule.setScoreSortConf(task.getScoreSortConf());
            }
            evalRule.setTaskName(task.getTaskName());
            evalRule.setTaskId(task.getId());
            evalRule.setScoreConf(task.getScoreConf());
            evalRule.setScoreView(task.getScoreView());
        }
        evalRule.setInputNotify(task.getInputNotifyConf());
        evalRule.setTotalLevelResults(levelResults);
        evalRule.setTotalEvalRs(loadEvalScoreRs(companyId, taskUserId));
        if (task.isCustom() && evalRule.fromOldTask()) {
            compatibleV1(kpiTypes);
        }
        //初始化s3到 指标上
        evalRule.extendsRaterRule(kpiTypes);
        //自定义满分分值 如果为null 则使用系统提供分值
        if (evalRule.getScoreValueConf().getCustomFullScore() == null) {
            BigDecimal fullScoreValue = companyDao.findFullScoreValue(companyId);
            evalRule.getScoreValueConf().setCustomFullScore(fullScoreValue);
        }
        if (evalRule.isSameTime()) {
            evalRule.initSameScoreChain();
        } else {
            evalRule.initTurnScoreChain();
        }
        evalRule.setCommentReqConf(task.getCommentReqConf());
        //签名开关
        evalRule.initSignatureFlag();
        if ((EmpEvalMerge.company & mod) > 0) {
            CompanyConf conf = companyDao.findCompanyConf(companyId);
            evalRule.setCompanyConf(conf);
        }

        //加载评分人
        List<EmpEvalScorer> scorers = scorerDao.listEmpEvalScorer(companyId.getId(), taskUserId);
        evalRule.setEvalScorersWrap(new EvalScorersWrap(scorers));
        evalRule.accEvalScorerForItem();//给每个指标接收评分人信息
        return evalRule;
    }

    public List<EvalScoreResult> loadEvalScoreRs(TenantId companyId, String taskUserId) {
        ComQB rsQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEqReq("is_deleted", Boolean.FALSE.toString())
                .whereInReq("scorer_type", AuditEnum.scoreScenes());
        List<EvalScoreResult> rss = domainDao.listAllDomain(rsQB, EvalScoreResult.class);
        return rss;
    }

    public EmpEvalMerge getEvalDetailBuilder(EmpDetailBuilder detailBuilder, TenantId companyId, String taskUserId, String opEmpId, int mod, int logMod) {
        EmpEvalMerge evalRule = empEvalDao.getBaseEvalMerge(companyId, taskUserId);
        List<BaseScoreResult> levelResults = kpiDao.listLevelTotal(companyId.getId(), taskUserId);
        detailBuilder.convertLevelResults(levelResults);
        AdminTask task = detailBuilder.getAdminTask();
        log.debug("getEmpEvalMerge.task={}", JSONUtil.toJsonStr(task));
        if (task == null) {
            return null;
        }
        KpiListWrap kpiTypes = kpiDao.listAndBuildKpiType(companyId, taskUserId, opEmpId, EmpEvalMerge.itemRule, logMod);
        detailBuilder.addEmpConvertFunc((p) -> detailBuilder.loadInputEmps());
        detailBuilder.convertKpiType(kpiTypes);

        ListWrap<PerfEvalTypeResult> typeScores = kpiDao.listEvalTypeScoreRs(companyId, taskUserId);
        ListWrap<EvalScoreResult> allItemRs = kpiDao.listEvalEmpScoreRs(companyId, taskUserId, null, null);
        detailBuilder.convertScoreRs(allItemRs, typeScores);

        if (Objects.isNull(evalRule) && task.isTmpTask()) {
            EvalUser user = detailBuilder.getTaskUser();
            evalRule = buildEvalRule(task, taskUserId);//转主阶段的配置
            TmpEvalConf2EmpEvalRuleBd builder = new TmpEvalConf2EmpEvalRuleBd(task.getTmpEvalFlow(), task.getId(), user.getEmpId(), companyId);
            //转化评分流程配置
            builder.buildTo(evalRule);
            loadItemRuleAudit2(detailBuilder, companyId, taskUserId, kpiTypes, evalRule.isCustom());
        } else {
            log.debug("getEmpEvalMerge.task.getScoreSortConf()={}", JSONUtil.toJsonStr(task.getScoreSortConf()));
            evalRule.setScoreSortConf(task.getScoreSortConf());
            evalRule.setTaskName(task.getTaskName());
            evalRule.setTaskId(task.getId());
            evalRule.setScoreConf(task.getScoreConf());
            evalRule.setScoreView(task.getScoreView());
        }
        //自定义满分分值 如果为null 则使用系统提供分值
        if (evalRule.getScoreValueConf().getCustomFullScore() == null) {
            BigDecimal fullScoreValue = companyDao.findFullScoreValue(companyId);
            evalRule.getScoreValueConf().setCustomFullScore(fullScoreValue);
        }
        detailBuilder.convertEvalRule(evalRule);
        if ((EmpEvalMerge.company & mod) > 0) {
            CompanyConf conf = companyDao.findCompanyConf(companyId);
            evalRule.setCompanyConf(conf);
        }
        if (task.isCustom() && evalRule.fromOldTask()) { //替换 compatibleV1(kpiTypes);
            detailBuilder.addEmpConvertFunc(list -> detailBuilder.convertV1Peer());
        }
        return evalRule;
    }


    //主要是兼容1.0任务作为考核表，考核规则，由谁邀请互评人
    private void compatibleV1(KpiListWrap kpiTypes) {
        EmpEvalKpiType anyType = kpiTypes.getDatas().get(0);
        for (EmpEvalKpiType kpiType : kpiTypes.getDatas()) {
            List<? extends EvalKpi> items = kpiType.getItems();
            if (CollUtil.isEmpty(items)) {
                continue;
            }
            for (EvalKpi item : items) {
                EvalItemScoreRule itemScoreRule = item.getItemScoreRule();
                if (itemScoreRule == null) {
                    continue;
                }
                itemScoreRule.mutualScoreV1();
                if (itemScoreRule.peerRaterWaitAppoint()) {
                    initPeerUserName(anyType.getCompanyId(), itemScoreRule);
                }
                if (itemScoreRule.subRaterWaitAppoint()) {
                    initSubUserName(anyType.getCompanyId(), itemScoreRule);
                }
            }
        }
    }

    private void loadItemRuleAudit2(EmpDetailBuilder detailBuilder, TenantId tenantId, String empEvalId, KpiListWrap kpiTypes, Boolean custom) {
        List<EmpEvalKpiType> types = kpiTypes.getDatas();
        if (CollUtil.isEmpty(types) || !custom) {
            return;
        }
        List<EvalKpi> items = new ArrayList<>();
        types.forEach(t -> items.addAll(t.getItems()));
        List<EvalItemScoreRule> itemScoreRules = items.stream().map(item -> item.getItemScoreRule()).collect(Collectors.toList());
        if (CollUtil.isEmpty(itemScoreRules)) {
            return;
        }
//        ComQB rsQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
//                .whereEqReq("company_id", tenantId.getId())
//                .whereEqReq("task_user_id", empEvalId)
//                .whereEqReq("is_deleted", Boolean.FALSE.toString());
//        List<EvalScoreResult> rss = domainDao.listAllDomain(rsQB, EvalScoreResult.class);
//        List<String> scoreIds = rss.stream().filter(rs -> StrUtil.isNotBlank(rs.getScorerId())).map(r -> r.getScorerId()).collect(Collectors.toList());

//        if (CollUtil.isNotEmpty(scoreIds)) {
//            ComQB rsNameQb = ComQB.build(EmployeeBaseInfoDo.class)
//                    .whereEqReq("company_id", tenantId.getId())
//                    .whereEq("is_delete", Boolean.FALSE.toString())
//                    .whereInReq("employee_id", scoreIds);
//            List<EmployeeBaseInfoDo> emps = domainDao.listAll(rsNameQb);
//            ListWrap<EmployeeBaseInfoDo> empWrap = new ListWrap<>(emps).groupBy(EmployeeBaseInfoDo::getEmployeeId);
//            for (EvalScoreResult scoreResult : rss) {
//                if (StrUtil.isEmpty(scoreResult.getScorerId())) {
//                    continue;
//                }
//                List<EmployeeBaseInfoDo> empInfos = empWrap.groupGet(scoreResult.getScorerId());
//                if (CollUtil.isEmpty(empInfos)) {
//                    continue;
//                }
//                scoreResult.setScorerName(empInfos.get(0).getName());
//            }
//        }
        ComQB itemRuleAuditQB = ComQB.build(TaskAuditDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", empEvalId)
                .whereEqReq("is_deleted", Boolean.FALSE.toString())
                .appendWhere(" scene like '%score%' ");
        List<EvalAudit> audits = domainDao.listAllDomain(itemRuleAuditQB, EvalAudit.class);
        if (CollUtil.isEmpty(detailBuilder.getAllItemRs().getDatas()) || CollUtil.isEmpty(audits)) {
            return;
        }
        List<EvalScoreResult> resultss = detailBuilder.getAllItemRs().getDatas().stream().filter(rs -> StrUtil.isNotBlank(rs.getTaskAuditId())).collect(Collectors.toList());
        ListWrap<EvalScoreResult> rssWrap = new ListWrap<>(resultss).groupBy(EvalScoreResult::getTaskAuditId);
        for (EvalAudit audit : audits) {
            List<EvalScoreResult> results = rssWrap.groupGet(audit.getId());
            audit.setScoreResults(results);
        }
        List<EvalAudit> dos = audits.stream().filter(a -> StrUtil.isNotBlank(a.getKpiItemId())).collect(Collectors.toList());
        ListWrap<EvalAudit> auditWrap = new ListWrap<>(dos).groupBy(EvalAudit::getKpiItemId);
        for (EmpEvalKpiType type : types) {
            List<? extends EvalKpi> kpiItems = type.getItems();
            if (CollUtil.isEmpty(kpiItems)) {
                continue;
            }
            for (EvalKpi item : type.getItems()) {
                EvalItemScoreRule itemScoreRule = item.getItemScoreRule();
                if (itemScoreRule == null) {
                    continue;
                }
                List<EvalAudit> itemRuleAudits = auditWrap.groupGet(item.getKpiItemId());
                if (CollUtil.isEmpty(itemRuleAudits)) {
                    continue;
                }
                itemScoreRule.matchItemRuleAudit(itemRuleAudits);
            }
        }
    }


    private void loadItemRuleAudit(TenantId tenantId, String empEvalId, KpiListWrap kpiTypes, Boolean custom) {
        List<EmpEvalKpiType> types = kpiTypes.getDatas();
        if (CollUtil.isEmpty(types) || !custom) {
            return;
        }
        List<EvalKpi> items = new ArrayList<>();
        types.forEach(t -> items.addAll(t.getItems()));
        List<EvalItemScoreRule> itemScoreRules = items.stream().map(item -> item.getItemScoreRule()).collect(Collectors.toList());
        if (CollUtil.isEmpty(itemScoreRules)) {
            return;
        }
        ComQB rsQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", empEvalId)
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        List<EvalScoreResult> rss = domainDao.listAllDomain(rsQB, EvalScoreResult.class);
        List<String> scoreIds = rss.stream().filter(rs -> StrUtil.isNotBlank(rs.getScorerId())).map(r -> r.getScorerId()).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(scoreIds)) {
            ComQB rsNameQb = ComQB.build(EmployeeBaseInfoDo.class)
                    .whereEqReq("company_id", tenantId.getId())
                    .whereEq("is_delete", Boolean.FALSE.toString())
                    .whereInReq("employee_id", scoreIds);
            List<EmployeeBaseInfoDo> emps = domainDao.listAll(rsNameQb);
            ListWrap<EmployeeBaseInfoDo> empWrap = new ListWrap<>(emps).groupBy(EmployeeBaseInfoDo::getEmployeeId);
            for (EvalScoreResult scoreResult : rss) {
                if (StrUtil.isEmpty(scoreResult.getScorerId())) {
                    continue;
                }
                List<EmployeeBaseInfoDo> empInfos = empWrap.groupGet(scoreResult.getScorerId());
                if (CollUtil.isEmpty(empInfos)) {
                    continue;
                }
                scoreResult.setScorerName(empInfos.get(0).getName());
            }
        }
        ComQB itemRuleAuditQB = ComQB.build(TaskAuditDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", empEvalId)
                .whereEqReq("is_deleted", Boolean.FALSE.toString())
                .appendWhere(" scene like '%score%' ");
        List<EvalAudit> audits = domainDao.listAllDomain(itemRuleAuditQB, EvalAudit.class);
        if (CollUtil.isEmpty(rss) || CollUtil.isEmpty(audits)) {
            return;
        }
        List<EvalScoreResult> resultss = rss.stream().filter(rs -> StrUtil.isNotBlank(rs.getTaskAuditId())).collect(Collectors.toList());
        ListWrap<EvalScoreResult> rssWrap = new ListWrap<>(resultss).groupBy(EvalScoreResult::getTaskAuditId);
        for (EvalAudit audit : audits) {
            List<EvalScoreResult> results = rssWrap.groupGet(audit.getId());
            audit.setScoreResults(results);
        }
        List<EvalAudit> dos = audits.stream().filter(a -> StrUtil.isNotBlank(a.getKpiItemId())).collect(Collectors.toList());
        ListWrap<EvalAudit> auditWrap = new ListWrap<>(dos).groupBy(EvalAudit::getKpiItemId);
        for (EmpEvalKpiType type : types) {
            List<? extends EvalKpi> kpiItems = type.getItems();
            if (CollUtil.isEmpty(kpiItems)) {
                continue;
            }
            for (EvalKpi item : type.getItems()) {
                EvalItemScoreRule itemScoreRule = item.getItemScoreRule();
                if (itemScoreRule == null) {
                    continue;
                }
                List<EvalAudit> itemRuleAudits = auditWrap.groupGet(item.getKpiItemId());
                if (CollUtil.isEmpty(itemRuleAudits)) {
                    continue;
                }
                itemScoreRule.matchItemRuleAudit(itemRuleAudits);
            }
        }
    }

    private EmpEvalMerge buildEvalRule(AdminTask task, String taskUserId) {
        EmpEvalMerge evalRule = new EmpEvalMerge();
        evalRule.setFromOldTask(Boolean.TRUE);
        evalRule.setEvaluateType(task.getEvaluateType());
        evalRule.setTaskId(task.getId());
        evalRule.setTaskName(task.getTaskName());
        evalRule.setEmpEvalId(taskUserId);
        evalRule.setCompanyId(task.getCompanyId());
        evalRule.setTypeWeightConf(task.getTypeWeightConf());
        evalRule.setScoreValueConf(task.getScoreValueConf());
        evalRule.setCommentConf(task.getCommentConf());
        evalRule.setScoreView(task.getScoreView());
        evalRule.setScoreConf(task.getScoreConf());
        evalRule.setScoreSortConf(task.getScoreSortConf());
        evalRule.setEnterScore(task.getEnterScore());
        evalRule.setConfirmTask(task.getConfirmTask());
        evalRule.setEditExeIndi(task.getEditExeIndi());
        evalRule.setAuditResult(task.getAuditResult());
        evalRule.setConfirmResult(task.getConfirmResult());
        evalRule.setPublishResult(task.getPublishResult());
        evalRule.setAppealConf(task.getAppealConf());

        evalRule.setCreatedUser(task.getCreatedUser());
        if (task.isTmpTask() && Objects.isNull(evalRule.getShowResultType())) {
            evalRule.setShowResultType(6);
        }
        return evalRule;
    }

    @Override
    public void addEmpEvalRule(EvalUser evalUser, EmpEvalRule evalRule, EmpEvalOperation operation, boolean refreshAudit) {
        EmpRule2DataBd builder = new EmpRule2DataBd(evalRule, kpiItemGen, auditIdGen, evalCommonGen, formulaFieldGen, refreshAudit);
        builder.initOrg(evalUser.getEvalOrgId(), evalUser.getOrgId(),evalUser.getEmpId(), evalUser.getEmpName(), evalUser.getAvatar());
        builder.initIsOpenAvgWeightCompute(evalUser.isOpenAvgWeightCompute());
        builder.build2Data(evalUser.getTaskId(), evalUser.getEmpId());
        if (evalUser.hasNameEmptyRaters()) {
            addRaterName(evalRule, evalUser.getResetRaterNameIds());
        }
        //查看是否已经存在考核规则
        EmpEvalRule empEvalRule = getBaseEmpEvalRule(evalRule.getCompanyId(), evalRule.getEmpEvalId());
        if (Objects.nonNull(empEvalRule)) {
            delEmpEvalRule(empEvalRule.getCompanyId(), empEvalRule.getEmpEvalId());
        }

        domainDao.add(EmpEvalRuleDo.class, evalRule);
        domainDao.saveBatch(builder.getTypeDos());          //指标分类
        domainDao.saveBatch(builder.getOkrTypes());         //okr分类
        domainDao.saveBatch(builder.getCustomItemRules());  //自定模式,指标的评分配置d,有则保存
        domainDao.saveBatch(builder.getFormulaFields());    //指标计算的字段及值
        domainDao.saveBatch(builder.getKpiItemDos());       //指标
        domainDao.saveBatch(builder.allAuditDos());         //确认阶段:指标审核,执行阶段指标审核,评分阶段评分人,校准
        domainDao.saveBatch(builder.getUsedFieldDos());     //维度自定义字段设置
        domainDao.saveBatch(builder.getItemUsedFieldDos()); //指标自定义字段设置
        domainDao.saveBatch(builder.getDeadLineJobs());  //阶段截止时间设置
        //updateEvalRuleStatus(evalUser.getCompanyId().getId(), evalUser.getId(), evalUser.getRuleConfStatus());
        //将校准流程放入user中
        evalUser.setResultAudits(builder.getResultAudit());
        if (operation == null) {
            return;
        }
        //考核表日志
        KpiEmp opEmp = getOpEmpBaseInfo(evalRule.getCompanyId(), operation.getOperatorId());
        operation.operate(opEmp);
        EmpEvalOperationDo empEvalLog = new ToDataBuilder<>(operation, EmpEvalOperationDo.class).data();
        empEvalLog.setId(domainDao.nextLongAsStr(diffLog));

        domainDao.save(empEvalLog);


    }

    public void batchAddEmpEvalRule(List<EvalUser> evalUsers, List<EmpEvalOperation> operations, boolean refreshAudit) {
        if (CollUtil.isEmpty(evalUsers)) {
            return;
        }
        List<EmpEvalRule> evalRules = new ArrayList<>();
        List<EmpEvalKpiTypeDo> typeDos = new ArrayList<>();
        List<PerfEvaluateTaskOkrTypeDo> okrTypeDos = new ArrayList<>();

        List<DeadLineJobDo> deadLineJobs = new ArrayList<>();
        List<PerfEvaluateTaskKpiDo> kpiItemDos = new ArrayList<>();
        List<PerfEvaluateTaskFormulaFieldDo> formulaFields = new ArrayList<>();
        List<PerfEvaluateTaskItemScoreRuleDo> customItemRules = new ArrayList<>();
        List<TaskAuditDo> auditDos = new ArrayList<>();
        List<EmpEvalTypeUsedFieldDo> typeUsedFieldDos = new ArrayList<>();
        List<PerfEvaluateItemUsedFieldDo> itemUsedFieldDos = new ArrayList<>();
        List<EmpEvalOperationDo> logs = new ArrayList<>();
        List<String> userIds = CollUtil.map(evalUsers, u -> u.getId(), true);
        TenantId companyId = evalUsers.get(0).getCompanyId();

        //EmpEvalRule empEvalRule = getBaseEmpEvalRule(evalRule.getCompanyId(), evalRule.getEmpEvalId());
        this.delEmpEvalRule(companyId, userIds);

        for (EvalUser evalUser : evalUsers) {
            EmpEvalRule evalRule = evalUser.getEmpEvalRule();
            EmpRule2DataBd builder = new EmpRule2DataBd(evalRule, kpiItemGen, auditIdGen, evalCommonGen, formulaFieldGen, refreshAudit);
            builder.initOrg(evalUser.getEvalOrgId(), evalUser.getOrgId(),evalUser.getEmpId(), evalUser.getEmpName(), evalUser.getAvatar());
            builder.initIsOpenAvgWeightCompute(evalUser.isOpenAvgWeightCompute());
            builder.build2Data(evalUser.getTaskId(), evalUser.getEmpId());
            if (evalUser.hasNameEmptyRaters()) {
                addRaterName(evalRule, evalUser.getResetRaterNameIds());
            }
            //查看是否已经存在考核规则
//            EmpEvalRule empEvalRule = getBaseEmpEvalRule(evalRule.getCompanyId(), evalRule.getEmpEvalId());
//            if (Objects.nonNull(empEvalRule)) {
//                delEmpEvalRule(empEvalRule.getCompanyId(), empEvalRule.getEmpEvalId());
//            }
            evalRules.add(evalRule);
            typeDos.addAll(builder.getTypeDos());
            okrTypeDos.addAll(builder.getOkrTypes());
            customItemRules.addAll(builder.getCustomItemRules());
            formulaFields.addAll(builder.getFormulaFields());
            kpiItemDos.addAll(builder.getKpiItemDos());
            auditDos.addAll(builder.allAuditDos());
            typeUsedFieldDos.addAll(builder.getUsedFieldDos());
            itemUsedFieldDos.addAll(builder.getItemUsedFieldDos());
            deadLineJobs.addAll(builder.getDeadLineJobs());
            //将校准流程放入user中
            evalUser.setResultAudits(builder.getResultAudit());

        }
        for (EmpEvalOperation operation : operations) {
            //考核表日志
            EmpEvalOperationDo empEvalLog = new ToDataBuilder<>(operation, EmpEvalOperationDo.class).data();
            empEvalLog.setId(domainDao.nextLongAsStr(diffLog));
            logs.add(empEvalLog);
        }
        domainDao.addBatch(EmpEvalRuleDo.class, evalRules);
        domainDao.saveBatch(typeDos);          //指标分类
        domainDao.saveBatch(okrTypeDos);         //okr分类
        domainDao.saveBatch(customItemRules);  //自定模式,指标的评分配置d,有则保存
        domainDao.saveBatch(formulaFields);    //指标计算的字段及值
        domainDao.saveBatch(kpiItemDos);       //指标
        domainDao.saveBatch(auditDos);         //确认阶段:指标审核,执行阶段指标审核,评分阶段评分人,校准
        domainDao.saveBatch(typeUsedFieldDos);     //维度自定义字段设置
        domainDao.saveBatch(itemUsedFieldDos); //指标自定义字段设置
        domainDao.saveBatch(deadLineJobs);  //阶段截止时间设置
        domainDao.saveBatch(logs);
    }

    public void fixField(String companyId,String taskUserId, KpiListWrap kpiTypes) {
        List<EmpEvalTypeUsedFieldDo> typeUsedFieldDos = new ArrayList<>();
        List<PerfEvaluateItemUsedFieldDo> itemUsedFieldDos = new ArrayList<>();
        for (EmpEvalKpiType kpiType : kpiTypes.getDatas()) {
            kpiType.getKpiTypeUsedFields().forEach(obj -> {
                EmpEvalTypeUsedFieldDo fieldDo = new EmpEvalTypeUsedFieldDo();
                BeanUtils.copyProperties(obj, fieldDo);
                if (obj.getType() == null) {
                    fieldDo.setType(1);
                }
                fieldDo.setTaskUserId(kpiType.getTaskUserId());
                fieldDo.setKpiTypeId(kpiType.getKpiTypeId());
                fieldDo.setIsDeleted("false");
                fieldDo.setCompanyId(kpiType.getCompanyId().getId());
                fieldDo.setCreatedUser(kpiType.getUpdatedUser());
                fieldDo.setCreatedTime(new Date());
                typeUsedFieldDos.add(fieldDo);

            });
            for (EvalKpi indicator : kpiType.getItems()) {
                for (ItemCustomFieldValue fieldValue : indicator.getFieldValueList()) {
                    //指标如果删除了，那么指标自定义字段也不 add
                    if (StrUtil.equals("true", indicator.getIsDeleted())) {
                        return;
                    }
                    PerfEvaluateItemUsedFieldDo itemUsedFieldDo = new PerfEvaluateItemUsedFieldDo();
                    BeanUtils.copyProperties(fieldValue, itemUsedFieldDo);
                    if (fieldValue.getType() == null) {
                        itemUsedFieldDo.setType(1);
                    }
                    if (fieldValue.getIsDeleted() == null) {
                        itemUsedFieldDo.setIsDeleted("false");
                    }
                    itemUsedFieldDo.setTaskUserId(indicator.getTaskUserId());
                    itemUsedFieldDo.setKpiItemId(indicator.getKpiItemId());
                    itemUsedFieldDo.setCompanyId(indicator.getCompanyId().getId());
                    itemUsedFieldDo.setName(fieldValue.getFieldName());
                    itemUsedFieldDo.setValue(fieldValue.getFieldValue());
                    itemUsedFieldDo.setStatus(fieldValue.getFieldStatus());
                    itemUsedFieldDo.setFieldId(fieldValue.getId());
                    itemUsedFieldDo.setReq(fieldValue.getIsReq());
                    itemUsedFieldDo.setAdminType(fieldValue.getAdminType());
                    itemUsedFieldDo.setCreatedUser(indicator.getUpdatedUser());
                    itemUsedFieldDo.setCreatedTime(new Date());
                    itemUsedFieldDo.setIsDeleted("false");
                    itemUsedFieldDos.add(itemUsedFieldDo);
                }
            }
        }
        //先del 指标自定义字段
        UpdateBuilder delTypeFs = UpdateBuilder.build(EmpEvalTypeUsedFieldDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.update(delTypeFs);
        domainDao.saveBatch(typeUsedFieldDos);     //维度自定义字段设置

        UpdateBuilder delKpiItemUsed = UpdateBuilder.build(PerfEvaluateItemUsedFieldDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.update(delKpiItemUsed);
        domainDao.saveBatch(itemUsedFieldDos);     //维度自定义字段设置
    }

    public void updateItemUserdField(String companyId, String empEvalId, List<KpiItemUsedField> fields) {
        //先del 指标自定义字段
        UpdateBuilder delKpiItemUsed = UpdateBuilder.build(PerfEvaluateItemUsedFieldDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", empEvalId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.update(delKpiItemUsed);
        List<PerfEvaluateItemUsedFieldDo> fieldDos = new ArrayList<>();
        fields.forEach(field -> {
            PerfEvaluateItemUsedFieldDo fieldDo = new ToDataBuilder<>(field, PerfEvaluateItemUsedFieldDo.class).data();
            fieldDo.setCompanyId(companyId);
            fieldDos.add(fieldDo);
        });
        domainDao.saveBatch(fieldDos); //指标自定义字段设置
    }


    public void updateEvalRuleStatus(String companyId, String taskUserId, Integer ruleConfStatus, String ruleConfError) {
        this.batchUpdateEvalRuleStatus(companyId, Arrays.asList(taskUserId), ruleConfStatus, ruleConfError);
    }

    public void batchUpdateEvalRuleStatus(String companyId, List<String> taskUserIds, Integer ruleConfStatus, String ruleConfError) {
        if (CollUtil.isEmpty(taskUserIds)) {
            return;
        }
        UpdateBuilder up = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                .whereInReq("id", taskUserIds)
                .whereEqReq("company_id", companyId)
                .set("rule_conf_status", ruleConfStatus)
                .set("rule_conf_error", ruleConfError);
        domainDao.update(up);
    }

    public void batchUpdateEvalErrorStatus(TenantId tenantId, List<EvalUser> evalUsers) {
        List<String> ids = evalUsers.stream().map(evalUser -> evalUser.getId()).collect(Collectors.toList());
        BatchUpdateBuilder batchUpdate = BatchUpdateBuilder.buildTable("perf_evaluate_task_user")
                .addSetCaseProp("ruleConfStatus", "id:=")
                .addSetCaseProp("ruleConfError", "id:=")
                .addAllBean(evalUsers)
                .whereEq("companyId", tenantId.getId())
                .whereIn("id", ids);
        domainDao.updateBatch(batchUpdate);
    }

    private void addRaterName(EmpEvalRule evalRule, List<String> resetRaterNames) {
        ComQB comQB = ComQB.build(EmployeeBaseInfoDo.class)
                .clearSelect()
                .select("employee_id, name, avatar, ding_user_id")
                .setRsType(EmpRefRolePo.class)
                .whereEq("is_delete", Boolean.FALSE.toString())
                .whereEqReq("company_id", evalRule.getCompanyId().getId())
                .whereInReq("employee_id", resetRaterNames);
        List<EmpRefRolePo> emps = domainDao.listAll(comQB);

        ComQB roleQB = ComQB.build(RoleRefEmpDo.class, "rre")
                .join(RoleDo.class, "r")
                .appendOn("r.company_id = rre.company_id and r.id = rre.role_id ")
                .clearSelect()
                .select("rre.emp_id as employee_id,r.id as role_id, r.role_name")
                .setRsType(EmpRefRolePo.class)
                .whereEq("rre.status", "valid")
                .whereEq("r.is_deleted", Boolean.FALSE.toString())
                .whereInReq("rre.emp_id", resetRaterNames)
                .whereEqReq("r.company_id", evalRule.getCompanyId().getId());
        List<EmpRefRolePo> roles = domainDao.listAll(roleQB);
        ListWrap<EmpRefRolePo> roleWrap = new ListWrap<>(roles).groupBy(EmpRefRolePo::getEmployeeId);

        List<EmpRefRolePo> empDos = new ArrayList<>();
        for (EmpRefRolePo emp : emps) {
            List<EmpRefRolePo> rolePos = roleWrap.groupGet(emp.getEmployeeId());
            if (CollUtil.isEmpty(rolePos)) {
                empDos.add(emp);
                continue;
            }
            for (EmpRefRolePo rolePo : rolePos) {
                rolePo.setName(emp.getName());
                rolePo.setEmployeeId(emp.getEmployeeId());
                rolePo.setAvatar(emp.getAvatar());
                rolePo.setDingUserId(emp.getDingUserId());
                empDos.add(rolePo);
            }
        }

        Map<String, List<EmpRefRolePo>> empMap = empDos.stream().collect(Collectors.groupingBy(emp -> emp.getEmployeeId()));

        AffirmTaskConf confirmTask = evalRule.getConfirmTask();
        EditExeIndiConf editExeIndi = evalRule.getEditExeIndi();
        AuditResultConf auditResult = evalRule.getAuditResult();
        if (confirmTask.isOpen()) {
            addRaterInfo(empMap, confirmTask.getAuditNodes());
        }
        if (editExeIndi.isOpen()) {
            addRaterInfo(empMap, editExeIndi.getAuditNodes());
        }
        if (auditResult.isOpen()) {
            addRaterInfo(empMap, auditResult.getAuditNodes());
        }
    }

    public <T extends BaseAuditNode> void addRaterInfo(Map<String, List<EmpRefRolePo>> empMap, List<T> auditNodes) {
        for (BaseAuditNode auditNode : auditNodes) {
            for (Rater rater : auditNode.getRaters()) {
                List<EmpRefRolePo> empDos = empMap.get(rater.getEmpId());
                if (CollUtil.isEmpty(empDos)) {
                    continue;
                }
                rater.setEmpName(empDos.get(0).getName());
                rater.setAvatar(empDos.get(0).getAvatar());
                rater.setDingUserId(empDos.get(0).getDingUserId());
                if ("role".equals(auditNode.getApproverType())) {
                    List<EmpRefRolePo> roles = empDos.stream().filter(d -> auditNode.getApproverInfo().equals(d.getRoleId())).collect(Collectors.toList());
                    if (CollUtil.isEmpty(roles)) {
                        continue;
                    }
                    rater.setRoleId(roles.get(0).getRoleId());
                    rater.setRoleName(roles.get(0).getRoleName());
                }
            }
        }
    }

    private KpiEmp getOpEmpBaseInfo(TenantId companyId, String empId) {
        ComQB comQB = ComQB.build(EmployeeBaseInfoDo.class, "e")
                .leftJoin(AdminSetDo.class, "a")
                .clearSelect().setRsType(KpiEmp.class)
                .select("e.employee_id emp_id ,e.name emp_name,e.avatar,e.ding_user_id ex_user_id,a.admin_type")
                .appendOn("e.company_id=a.company_id and e.employee_id=a.emp_id")
                .whereEqReq("e.employee_id", empId)
                .whereEqReq("e.company_id", companyId.getId())
                .appendWhere(" a.status = 'valid'");
        return domainDao.findOne(comQB);
    }

    @Override
    public void editEmpEvalRule(EvalUser evalUser, TalentStatus changedStage) {
        EmpEvalRule evalRule = evalUser.getEmpEvalRule();
        if (evalUser.hasNameEmptyRaters()) {
            addRaterName(evalRule, evalUser.getResetRaterNameIds());
        }
        EmpRule2DataBd builder = new EmpRule2DataBd(evalRule, kpiItemGen, auditIdGen, evalCommonGen, formulaFieldGen);
        builder.initOrg(evalUser.getEvalOrgId(), evalUser.getOrgId(),evalUser.getEmpId(), evalUser.getEmpName(), evalUser.getAvatar());
        builder.build2Data(evalUser.getTaskId(), evalUser.getEmpId());
        String companyId = evalRule.getCompanyId().getId();
        UpdateBuilder up = UpdateBuilder.build(EmpEvalRuleDo.class)
                .set("ruleName", evalRule.getRuleName())
                .set("indicatorCnt", evalRule.getIndicatorCnt())
                .set("evaluateType", evalRule.getEvaluateType())
                .whereEqReq("empEvalId", evalRule.getEmpEvalId())
                .whereEqReq("companyId", companyId)
                .appendWhere("is_deleted ='false'");//已删的不再更新.之前是物理删除的.
        if (changedStage.beforeEq(TalentStatus.CONFIRMING)) {
            up.set("confirmTask", JSON.toJSONString(evalRule.getConfirmTask()));
            resetAudit(companyId, evalUser.getId(), evalUser.getEmpId(), AuditEnum.CONFIRM_TASK.getScene(), builder.getConfirmTaskAudits());
        }

        if (changedStage.beforeEq(TalentStatus.CONFIRMED)) {
            up.set("editExeIndi", JSON.toJSONString(evalRule.getEditExeIndi()));
            resetAudit(companyId, evalUser.getId(), evalUser.getEmpId(), AuditEnum.EDIT_EXE_INDI.getScene(), builder.getEditExeIndiAudits());
        }
        if (changedStage.beforeEq(TalentStatus.FINISH_VALUE_AUDIT)) {
            up.set("finish_value_audit", JSON.toJSONString(evalRule.getFinishValueAudit()));
            resetAudit(companyId, evalUser.getId(), evalUser.getEmpId(), AuditEnum.FINISH_VALUE_AUDIT.getScene(), builder.getFinishValueAudits());
        }
        if (changedStage.beforeEq(TalentStatus.SCORING)) {
            up.set("s3SelfRater", JSON.toJSONString(evalRule.getS3SelfRater()));
            up.set("s3PeerRater", JSON.toJSONString(evalRule.getS3PeerRater()));
            up.set("s3SubRater", JSON.toJSONString(evalRule.getS3SubRater()));
            up.set("s3SuperRater", JSON.toJSONString(evalRule.getS3SuperRater()));
            up.set("enterScore", JSON.toJSONString(evalRule.getEnterScore()));
            up.set("scoreConf", JSON.toJSONString(evalRule.getScoreConf()));
            up.set("scoreSortConf", JSON.toJSONString(evalRule.getScoreSortConf()));
            up.set("scoreView", JSON.toJSONString(evalRule.getScoreView()));
            up.set("commentConf", JSON.toJSONString(evalRule.getCommentConf()));
            up.set("commentReqConf", JSON.toJSONString(evalRule.getCommentReqConf()));
            resetAudit(companyId, evalUser.getId(), evalUser.getEmpId(), AuditEnum.scoreScenes(), builder.getScorerAuditDos());
            builder.builderRefKrId();
            resetIndicator(evalUser, evalRule, builder);
        }
        if (changedStage.beforeEq(TalentStatus.RESULTS_AUDITING)) {
            up.set("auditResult", JSON.toJSONString(evalRule.getAuditResult()));
            resetAudit(companyId, evalUser.getId(), evalUser.getEmpId(), AuditEnum.FINAL_RESULT_AUDIT.getScene(), builder.getAuditResultAudits());
        }
        if (changedStage.beforeEq(TalentStatus.RESULTS_INTERVIEW)) {
            up.set("interviewConf", JSON.toJSONString(evalRule.getInterviewConf()));
        }
        if (changedStage.beforeEq(TalentStatus.RESULTS_AFFIRMING)) {
            up.set("confirmResult", JSON.toJSONString(evalRule.getConfirmResult()));
        }
        if (changedStage.beforeEq(TalentStatus.RESULTS_APPEAL)) {
            up.set("appealConf", JSON.toJSONString(evalRule.getAppealConf()));
        }
        if (changedStage.beforeEq(TalentStatus.WAIT_PUBLISHED)) {
            up.set("publishResult", JSON.toJSONString(evalRule.getPublishResult()));
        }
        if (changedStage.equals(TalentStatus.DEAD_LINE)) {
            up.set("deadLineConf", JSON.toJSONString(evalRule.getDeadLineConf()));
        }
        domainDao.update(up);
    }

    //更新指标
    private void resetIndicator(EvalUser evalUser, EmpEvalRule evalRule, EmpRule2DataBd builder) {
        TalentStatus atStatus = TalentStatus.statusOf(evalUser.getTaskStatus());
        if (atStatus.before(TalentStatus.SCORING)) {    //评分之前可以修改指标
            DeleteBuilder delType = DeleteBuilder.build(EmpEvalKpiTypeDo.class)
                    .whereEqReq("companyId", evalRule.getCompanyId().getId())
                    .whereEqReq("taskUserId", evalRule.getEmpEvalId());
            domainDao.delete(delType);
            domainDao.saveBatch(builder.getTypeDos());          //指标分类

            DeleteBuilder delOkType = DeleteBuilder.build(PerfEvaluateTaskOkrTypeDo.class)
                    .whereEqReq("companyId", evalRule.getCompanyId().getId())
                    .whereEqReq("taskUserId", evalRule.getEmpEvalId());
            domainDao.delete(delOkType);
            domainDao.saveBatch(builder.getOkrTypes());         //okr分类 旧流程要使用

            DeleteBuilder delItemRule = DeleteBuilder.build(PerfEvaluateTaskItemScoreRuleDo.class)
                    .whereEqReq("companyId", evalRule.getCompanyId().getId())
                    .whereEqReq("taskUserId", evalRule.getEmpEvalId());
            domainDao.delete(delItemRule);
            domainDao.saveBatch(builder.getCustomItemRules());  //自定模式,指标的评分配置,有则保存

            DeleteBuilder delField = DeleteBuilder.build(PerfEvaluateTaskFormulaFieldDo.class)
                    .whereEqReq("companyId", evalRule.getCompanyId().getId())
                    .whereEqReq("taskUserId", evalRule.getEmpEvalId());
            domainDao.delete(delField);
            domainDao.saveBatch(builder.getFormulaFields());    //指标计算的字段及值

            DeleteBuilder delItem = DeleteBuilder.build(PerfEvaluateTaskKpiDo.class)
                    .whereEqReq("companyId", evalRule.getCompanyId().getId())
                    .whereEqReq("taskUserId", evalRule.getEmpEvalId());
            domainDao.delete(delItem);
            domainDao.saveBatch(builder.getKpiItemDos());       //指标

        }

        //DeleteBuilder delAudit = DeleteBuilder.build(PerfEvaluateTaskAuditDo.class)
        //        .whereEqReq("companyId", evalRule.getCompanyId().getId())
        //        .whereEqReq("empId", evalUser.getEmpId());
        //domainDao.delete(delAudit);
        //domainDao.saveBatch(builder.allAuditDos());         //流程汇总
        //domainDao.save(builder.getScoreRule());
        //domainDao.add(PerfEvaluateTaskUserDo.class, taskUser);
        //domainDao.save(builder.getOperationLog());
    }

    private void resetAudit(String companyId, String taskUserId, String empId, List<String> scenes,
                            List<TaskAuditDo> addAuditDos) {
        DeleteBuilder delAudit = DeleteBuilder.build(TaskAuditDo.class)
                .whereEqReq("companyId", companyId)
                .whereEqReq("taskUserId", taskUserId)
                .whereEqReq("empId", empId)
                .whereInReq("scene", scenes);
        domainDao.delete(delAudit);
        domainDao.saveBatch(addAuditDos);
    }

    private void resetAudit(String companyId, String taskUserId, String empId, String scene,
                            List<TaskAuditDo> addAuditDos) {
        DeleteBuilder delAudit = DeleteBuilder.build(TaskAuditDo.class)
                .whereEqReq("companyId", companyId)
                .whereEqReq("taskUserId", taskUserId)
                .whereEqReq("empId", empId)
                .whereEqReq("scene", scene);
        domainDao.delete(delAudit);
        domainDao.saveBatch(addAuditDos);
    }

    //编辑旧模板任务
    public void editTempTaskAsRule(EvalUser evalUser, EmpEvalRule evalRule) {
        EmpRule2DataBd builder = new EmpRule2DataBd(evalRule, kpiItemGen, auditIdGen, evalCommonGen, formulaFieldGen);
        builder.initOrg(evalUser.getEvalOrgId(), evalUser.getOrgId(),evalUser.getEmpId(), evalUser.getEmpName(), evalUser.getAvatar());
        builder.build2Data(evalUser.getTaskId(), evalUser.getEmpId());
        domainDao.add(EmpEvalRuleDo.class, evalRule);
    }

    @Override
    public void saveDiffLog(EmpEvalOperation operation) {
        operation.setId(domainDao.nextLongAsStr(diffLog));
        EmpEvalOperationDo data = new ToDataBuilder<>(operation, EmpEvalOperationDo.class).data();
        domainDao.save(data);
    }

    @Override
    public void batchSaveDiffLog(List<EmpEvalOperation> operations) {
        if (CollUtil.isEmpty(operations)) {
            return;
        }
        List<EmpEvalOperationDo> doList = new ArrayList<>();
        for (EmpEvalOperation operation : operations) {
            operation.setId(domainDao.nextLongAsStr(diffLog));
            EmpEvalOperationDo data = new ToDataBuilder<>(operation, EmpEvalOperationDo.class).data();
            doList.add(data);
        }
        domainDao.saveBatch(doList);
    }

    @Override
    public void editScoreStageConf(EmpEvalRule rule, String taskId, String empId, List<String> raterNameIds,EvalUser evalUser) {
        editScoreStageConf(rule, taskId, empId, raterNameIds, false, evalUser);
    }

    @Override
    public void editScoreStageConf(EmpEvalRule rule, String taskId, String empId, List<String> raterNameIds,
                                   boolean refreshAudit, EvalUser evalUser) {
        ListWrap<EvalScoreResult> resultListWrap = evaluateTaskDao.listScoreStageBeforeScoreResults(rule.getCompanyId(), rule.getEmpEvalId());
        EmpRule2DataBd bd = new EmpRule2DataBd(rule, kpiItemGen, auditIdGen, evalCommonGen, formulaFieldGen, refreshAudit);
        bd.initOrg(evalUser.getEvalOrgId(), evalUser.getOrgId(),evalUser.getEmpId(), evalUser.getEmpName(), evalUser.getAvatar());
        bd.initScoreResult(resultListWrap);
        bd.build2Data(taskId, empId);
        if (CollUtil.isNotEmpty(raterNameIds)) {
            addRaterName(rule, raterNameIds);
        }
        //删除考核规则
        delEmpEvalRule(rule.getCompanyId(), rule.getEmpEvalId());

        domainDao.add(EmpEvalRuleDo.class, rule);

        domainDao.saveBatch(bd.getTypeDos());          //指标分类
        domainDao.saveBatch(bd.getUsedFieldDos());      //指标维度自定义字段配置
        domainDao.saveBatch(bd.getOkrTypes());         //okr分类
        domainDao.saveBatch(bd.getCustomItemRules());  //自定模式,指标的评分配置,有则保存
        domainDao.saveBatch(bd.getFormulaFields());    //指标计算的字段及值
        domainDao.saveBatch(bd.getKpiItemDos());       //指标
        domainDao.saveBatch(bd.allAuditDos());         //确认阶段:指标审核,执行阶段指标审核,评分阶段评分人,
        domainDao.saveBatch(bd.getDeadLineJobs());  //阶段截止时间设置
        //改成allAuditDos，包含了所有审批的
        //domainDao.saveBatch(bd.getScorerAuditDos());   //评分阶段评分人
        //domainDao.saveBatch(bd.getFinishValueAudits()); //完成值录入审核人
        domainDao.saveBatch(bd.getItemUsedFieldDos());  //指标自定义字段设置
        //修改评分阶段之前的审批流程关联的task_audit_id字段
        if (CollUtil.isNotEmpty(bd.getScoreResultDos())) {
            for (PerfEvaluateTaskScoreResultDo resultDo : bd.getScoreResultDos()) {
                updateScoreResultTaskAuditId(resultDo.getId(), resultDo.getTaskAuditId());
            }
        }
        //add 评分人
//        if (CollUtil.isNotEmpty(bd.getEmpEvalScorers())) {
//            EmpEvalScorerBatchDataBd scorerBatchDataBd = new EmpEvalScorerBatchDataBd(rule.getCompanyId().getId(),
//                    rule.getEmpEvalId(), empEvalScorerIdGen, empEvalScorerNodeIdGen, scorerScoreKpiItemIdGen);
//            scorerBatchDataBd.buildBatchEmpEvalScorer(bd.getEmpEvalScorers());
//            domainDao.saveBatch(scorerBatchDataBd.getScorerDo());  //评分人
//            domainDao.saveBatch(scorerBatchDataBd.getNodeDos());  //评分环节
//            domainDao.saveBatch(scorerBatchDataBd.getKpiItemDos());  //评分指标
//        }
    }

    @Override
    public void editEmpEvalRule(EmpEvalRule newEmpEvalRule) {
        domainDao.update(EmpEvalRuleDo.class, newEmpEvalRule);
    }


    @Override
    public void updateEmpEvalRule(BaseEmpEvalRule rule) {
        EmpEvalRuleDo ruleDo = new ToDataBuilder<>(rule, EmpEvalRuleDo.class).data();
        UpdateBuilder updateBuilder = UpdateBuilder.build("emp_eval_rule").setBean(ruleDo)
                .whereEqReq("company_id", rule.getCompanyId().getId())
                .whereEqReq("emp_eval_id", rule.getEmpEvalId())
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        domainDao.update(updateBuilder);
    }

    @Override
    public void updateDeadLine(String taskId, EmpEvalRule rule) {
        //清掉旧的
        UpdateBuilder delOld = UpdateBuilder.build(DeadLineJobDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", rule.getCompanyId().getId())
                .whereEqReq("task_user_id", rule.getEmpEvalId())
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.update(delOld);

        List<DeadLineJobDo> deadLineJobDos = new DeadLineJobBuilder(taskId, rule).buildNode();
        domainDao.saveBatch(deadLineJobDos);
    }

    @Override
    public void updateSuperiorScoreOrder(TenantId tenantId, String empEvalId,String superiorScoreOrder) {
        UpdateBuilder up = UpdateBuilder.build(EmpEvalRuleDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("emp_eval_id", empEvalId)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .set("superior_score_order", superiorScoreOrder)
                .set("updated_time", new Date());
        domainDao.update(up);
    }

    @Override
    public void updateSuperiorScoreOrderBatch(TenantId tenantId,  List<EmpEvalRule> rules) {
        if (CollUtil.isEmpty(rules)) {
            return;
        }
        for (EmpEvalRule rule : rules) {
            UpdateBuilder up = UpdateBuilder.build(EmpEvalRuleDo.class)
                    .whereEqReq("company_id", tenantId.getId())
                    .whereEqReq("emp_eval_id", rule.getEmpEvalId())
                    .whereEq("is_deleted", Boolean.FALSE.toString())
                    .set("superior_score_order", rule.getSuperiorScoreOrder())
                    .set("updated_time", new Date());
            domainDao.update(up);
        }
    }

    @Override
    public void editDeadLineConf(TenantId tenantId, EmpEvalRule rule) {
        UpdateBuilder up = UpdateBuilder.build(EmpEvalRuleDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("emp_eval_id", rule.getEmpEvalId())
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .set("dead_line_conf", JSONUtil.toJsonStr(rule.getDeadLineConf()));
        domainDao.update(up);
    }

    @Override
    public void editAuditResultConf(EmpEvalRule rule, String taskId, String empId) {
        //domainDao.update(EmpEvalRuleDo.class, rule);
        updateEmpEvalRule(rule);
        reSaveAudit(rule, taskId, empId, AuditEnum.FINAL_RESULT_AUDIT.getScene());
    }

    @Override
    public void editInterviewResultConf(EmpEvalRule rule) {
        updateEmpEvalRule(rule);
    }

    @Override
    public void editExeIndiConf(EmpEvalRule rule, String taskId, String empId) {
        //domainDao.update(EmpEvalRuleDo.class, rule);
        updateEmpEvalRule(rule);
        reSaveAudit(rule, taskId, empId, AuditEnum.EDIT_EXE_INDI.getScene());
    }

    public void reSaveAudit(EmpEvalRule domain, String taskId, String empId, String scene) {
        reSaveAudit(domain, taskId, empId, scene, null);
    }

    public void reSaveAudit(EmpEvalRule domain, String taskId, String empId, String scene, List<EvalKpi> items) {
        //清掉旧的
        UpdateBuilder delOld = UpdateBuilder.build(TaskAuditDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", domain.getCompanyId().getId())
                .whereEqReq("task_user_id", domain.getEmpEvalId())
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEqReq("scene", scene);
        domainDao.update(delOld);
        EmpRule2DataBd dataBd = new EmpRule2DataBd(domain, kpiItemGen, auditIdGen, evalCommonGen, formulaFieldGen);
        List<TaskAuditDo> auditDos = new ArrayList<>();
        if (scene.equals(AuditEnum.FINISH_VALUE_AUDIT.getScene())) {
            if (CollUtil.isEmpty(items)) {
                dataBd.build2Data(taskId, empId);
            } else {
                dataBd.initFinishValueAudit(items);
            }
            auditDos = dataBd.getFinishValueAudits();
        } else {
            dataBd.reBuildAudit(scene, taskId, empId);
            auditDos = dataBd.getAuditsByScene(scene);
        }
        if (CollUtil.isEmpty(auditDos)) {
            return;
        }
        domainDao.saveBatch(auditDos);
    }

    public void reSaveConf(EvalUser evalUser) {
        //清掉旧的
        this.clearConf(evalUser.getCompanyId(), evalUser.getId(), evalUser.getChangedAuditScene());
        EmpRule2DataBd dataBd = new EmpRule2DataBd(evalUser.getEmpEvalRule(), kpiItemGen, auditIdGen, evalCommonGen, formulaFieldGen);
        dataBd.initOrg(evalUser.getEvalOrgId(), evalUser.getOrgId(),evalUser.getEmpId(), evalUser.getEmpName(), evalUser.getAvatar());
        dataBd.build2Data(evalUser.getTaskId(), evalUser.getEmpId());
        updateEmpEvalRule(evalUser.getEmpEvalRule());
        domainDao.saveBatch(dataBd.getTypeDos());          //指标分类
        domainDao.saveBatch(dataBd.getCustomItemRules());  //自定模式,指标的评分配置,有则保存
        domainDao.saveBatch(dataBd.getKpiItemDos());       //指标
        domainDao.saveBatch(dataBd.allAuditDos(evalUser.getChangeScene()));         //确认阶段:指标审核,执行阶段指标审核,评分阶段评分人,校准
        //修改评分阶段之前的审批流程关联的task_audit_id字段
        if (CollUtil.isNotEmpty(dataBd.getScoreResultDos())) {
            for (PerfEvaluateTaskScoreResultDo resultDo : dataBd.getScoreResultDos()) {
                updateScoreResultTaskAuditId(resultDo.getId(), resultDo.getTaskAuditId());
            }
        }
    }

    public void clearConf(TenantId companyId, String taskUserId, List<String> changedAuditScene) {
        //del audit
        if (CollUtil.isNotEmpty(changedAuditScene)) {
            DeleteBuilder audit = DeleteBuilder.build(TaskAuditDo.class)
                    .whereEqReq("company_id", companyId.getId())
                    .whereEqReq("task_user_id", taskUserId)
                    .whereInReq("scene", changedAuditScene)
                    .whereEq("is_deleted", Boolean.FALSE.toString());
            domainDao.delete(audit);
        }

        //del type
        DeleteBuilder type = DeleteBuilder.build(EmpEvalKpiTypeDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.delete(type);


        //del 指标
        DeleteBuilder item = DeleteBuilder.build(PerfEvaluateTaskKpiDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.delete(item);

        //del itemRule
        DeleteBuilder itemRuleDel = DeleteBuilder.build(PerfEvaluateTaskItemScoreRuleDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.delete(itemRuleDel);

        //清除指标确认暂存
        if (changedAuditScene.contains(EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT.getScene())) {
            UpdateBuilder upCache = UpdateBuilder.build(CompanyCacheInfoDo.class)
                    .set("is_deleted", Boolean.TRUE.toString())
                    .whereEq("company_id", companyId.getId())
                    .whereEq("link_id", taskUserId)
                    .whereEq("business_scene", EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT.getScene())
                    .whereEq("is_deleted", Boolean.FALSE.toString());
            domainDao.update(upCache);
        }
    }

    @Override
    public void editConfirmConf(EmpEvalRule rule, String taskId, String empId) {
        //domainDao.update(EmpEvalRuleDo.class, rule);
        updateEmpEvalRule(rule);
        reSaveAudit(rule, taskId, empId, AuditEnum.CONFIRM_TASK.getScene());
    }

    @Override
    public void editFinishedValueAuditConf(EmpEvalRule rule, String taskId, String empId, List<EvalKpi> items) {
        //domainDao.update(EmpEvalRuleDo.class, rule);
        updateEmpEvalRule(rule);
        reSaveAudit(rule, taskId, empId, AuditEnum.FINISH_VALUE_AUDIT.getScene(), items);
    }

    @Override
    public void initiator(TenantId tenantId, List<String> taskUserIds, EmpId opEmpId) {
        final UpdateBuilder up = UpdateBuilder.build(EmpEvalRuleDo.class)
                .set("initiator", opEmpId.getId())
                .whereEqReq("company_id", tenantId.getId())
                .whereInReq("emp_eval_id", taskUserIds);
        domainDao.update(up);
    }

    public void clearRuleConf(TenantId companyId, String userId) {
        this.batchClearRuleConf(companyId, Arrays.asList(userId));
    }


    public void batchClearRuleConf(TenantId companyId, List<String> userIds) {
        UpdateBuilder delAudit = UpdateBuilder.build(TaskAuditDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("task_user_id", userIds)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        UpdateBuilder delEmpEvalType = UpdateBuilder.build(EmpEvalKpiTypeDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("task_user_id", userIds)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        UpdateBuilder delEmpEvalUsed = UpdateBuilder.build(EmpEvalTypeUsedFieldDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("task_user_id", userIds)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        UpdateBuilder delOkrType = UpdateBuilder.build(PerfEvaluateTaskOkrTypeDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("task_user_id", userIds)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        UpdateBuilder delItem = UpdateBuilder.build(PerfEvaluateTaskKpiDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("task_user_id", userIds)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        UpdateBuilder delKpiItemUsed = UpdateBuilder.build(PerfEvaluateItemUsedFieldDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("task_user_id", userIds)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        UpdateBuilder delItemRule = UpdateBuilder.build(PerfEvaluateTaskItemScoreRuleDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("task_user_id", userIds)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        UpdateBuilder delFormFile = UpdateBuilder.build(PerfEvaluateTaskFormulaFieldDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("task_user_id", userIds)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        UpdateBuilder delEmpEvalRule = UpdateBuilder.build(EmpEvalRuleDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("emp_eval_id", userIds)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        //清掉旧的
        UpdateBuilder delDeadLine = UpdateBuilder.build(DeadLineJobDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("task_user_id", userIds)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.update(delAudit);
        domainDao.update(delEmpEvalType);
        domainDao.update(delEmpEvalUsed);
        domainDao.update(delOkrType);
        domainDao.update(delItem);
        domainDao.update(delKpiItemUsed);
        domainDao.update(delItemRule);
        domainDao.update(delFormFile);
        domainDao.update(delEmpEvalRule);
        domainDao.update(delDeadLine);
    }

    @Override
    public void customItemFlow(EmpEvalRule rule, String taskId, String empId) {
        EmpRule2DataBd bd = new EmpRule2DataBd(rule, kpiItemGen, auditIdGen, evalCommonGen, formulaFieldGen);
        rule.getKpiTypes().getDatas().forEach(type -> {
            ComQB typeQ = ComQB.build(EmpEvalKpiTypeDo.class)
                    .whereEqReq("company_id", rule.getCompanyId().getId())
                    .whereEqReq("task_user_id", rule.getEmpEvalId())
                    .whereEq("kpi_type_id", type.getKpiTypeId())
                    .whereEqReq("is_deleted", Boolean.FALSE.toString());
            EmpEvalKpiType typeRule = domainDao.findDomain(typeQ, EmpEvalKpiType.class);
            for (EvalKpi item : type.getItems()) {
                ComQB itRuleQ = ComQB.build(PerfEvaluateTaskItemScoreRuleDo.class)
                        .whereEqReq("company_id", rule.getCompanyId().getId())
                        .whereEqReq("task_user_id", rule.getEmpEvalId())
                        .whereEq("kpi_item_id", item.getKpiItemId())
                        .whereEqReq("is_deleted", Boolean.FALSE.toString());
                EvalItemScoreRule itemRule = domainDao.findDomain(itRuleQ, EvalItemScoreRule.class);
                itemRule = extendRuleIfNeed(rule, typeRule, itemRule);
                item.setItemScoreRule(itemRule);
                item.getItemScoreRule().extendMutiConf(rule.getScoreConf());
                bd.buildScoreRule(taskId, empId, item, item.getItemScoreRule());
            }
        });
        //del audit
        UpdateBuilder audit = UpdateBuilder.build(TaskAuditDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", rule.getCompanyId().getId())
                .whereEqReq("task_user_id", rule.getEmpEvalId())
                .appendWhere("scene like '%score%'")
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.update(audit);
        //del itemRule
        UpdateBuilder itemRuleDel = UpdateBuilder.build(PerfEvaluateTaskItemScoreRuleDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", rule.getCompanyId().getId())
                .whereEqReq("task_user_id", rule.getEmpEvalId())
                .whereNotEq("kpi_item_id", '0')
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.update(itemRuleDel);

        domainDao.saveBatch(bd.getCustomItemRules());  //自定模式,指标的评分配置,有则保存
        domainDao.saveBatch(bd.getScorerAuditDos());   //评分阶段评分人
    }

    @Override
    public void customItemFlow(EmpEvalRule rule, String taskId, String empId, boolean indexCofMdfEmp) {
        EmpRule2DataBd bd = new EmpRule2DataBd(rule, kpiItemGen, auditIdGen, evalCommonGen, formulaFieldGen);
        rule.getKpiTypes().getDatas().forEach(type -> {
            ComQB typeQ = ComQB.build(EmpEvalKpiTypeDo.class)
                    .whereEqReq("company_id", rule.getCompanyId().getId())
                    .whereEqReq("task_user_id", rule.getEmpEvalId())
                    .whereEq("kpi_type_id", type.getKpiTypeId())
                    .whereEqReq("is_deleted", Boolean.FALSE.toString());
            EmpEvalKpiType typeRule = indexCofMdfEmp ? type : domainDao.findDomain(typeQ, EmpEvalKpiType.class);
            for (EvalKpi item : type.getItems()) {
                ComQB itRuleQ = ComQB.build(PerfEvaluateTaskItemScoreRuleDo.class)
                        .whereEqReq("company_id", rule.getCompanyId().getId())
                        .whereEqReq("task_user_id", rule.getEmpEvalId())
                        .whereEq("kpi_item_id", item.getKpiItemId())
                        .whereEqReq("is_deleted", Boolean.FALSE.toString());
                EvalItemScoreRule itemRule = indexCofMdfEmp ? item.getItemScoreRule() : domainDao.findDomain(itRuleQ, EvalItemScoreRule.class);
                itemRule = extendRuleIfNeed(rule, typeRule, itemRule);
                item.setItemScoreRule(itemRule);
                item.getItemScoreRule().extendMutiConf(rule.getScoreConf());
                bd.buildScoreRule(taskId, empId, item, item.getItemScoreRule());
            }
        });
        //del audit
        UpdateBuilder audit = UpdateBuilder.build(TaskAuditDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", rule.getCompanyId().getId())
                .whereEqReq("task_user_id", rule.getEmpEvalId())
                .appendWhere("scene like '%score%'")
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.update(audit);
        //del itemRule
        UpdateBuilder itemRuleDel = UpdateBuilder.build(PerfEvaluateTaskItemScoreRuleDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", rule.getCompanyId().getId())
                .whereEqReq("task_user_id", rule.getEmpEvalId())
                .whereNotEq("kpi_item_id", '0')
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.update(itemRuleDel);

        domainDao.saveBatch(bd.getCustomItemRules());  //自定模式,指标的评分配置,有则保存
        domainDao.saveBatch(bd.getScorerAuditDos());   //评分阶段评分人
    }

    private EvalItemScoreRule extendRuleIfNeed(EmpEvalRule rule, EmpEvalKpiType typeRule, EvalItemScoreRule itemRule) {
        if (itemRule != null) {
            return itemRule;
        }
        itemRule = new EvalItemScoreRule();
        if (typeRule != null && typeRule.isOpenRaterRule()) {
            itemRule.asRaterConf(typeRule.getSelfRater(), typeRule.getPeerRater(),
                    typeRule.getSubRater(), typeRule.getSuperRater(), typeRule.getAppointRater());
            return itemRule;
        }
        itemRule.asRaterConf(rule.getS3SelfRater(), rule.getS3PeerRater(),
                rule.getS3SubRater(), rule.getS3SuperRater(), rule.getS3AppointRater());
        return itemRule;
    }

    @Override
    public EmpEvalRule getBaseEmpEvalRule(TenantId tenantId, String empEvalId) {
        ComQB comQB = ComQB.build(EmpEvalRuleDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("emp_eval_id", empEvalId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        return domainDao.findDomain(comQB, EmpEvalRule.class);
    }

    @Override
    public void applyTemp(EvalUser user, EmpEvalRule empEvalRule, EmpEvalOperation operation) {
        clearRuleConf(user.getCompanyId(), user.getId());
        addEmpEvalRule(user, empEvalRule, operation, false);
    }

    @Override
    public void applyTempBatch(TenantId companyId, List<EvalUser> users, List<EmpEvalOperation> operations) {
        List<String> userIds = users.stream().map(evalUser -> evalUser.getId()).collect(Collectors.toList());
        batchClearRuleConf(companyId, userIds);
        batchAddEmpEvalRule(users, operations, false);
    }

    @Override
    public void applyChangeEval(List<EvalUser> users) {
        if (CollUtil.isEmpty(users)) {
            return;
        }
        for (EvalUser user : users) {
            reSaveConf(user);
            new EvalTaskErrorEvent(user.getCompanyId().getId(), user).publish();
        }
    }


    private void initPeerUserName(TenantId tenantId, EvalItemScoreRule itemRule) {
        if ("examInvite".equals(itemRule.getPeerUserType())) {
            itemRule.setPeerUserName("被考核人");
            return;
        }
        if ("empInvite".equals(itemRule.getPeerUserType())) {
            List<Rater> emps = listEmp(tenantId.getId(), Arrays.asList(itemRule.getPeerUserValue()));
            itemRule.setPeerUserName(emps.get(0).getEmpName());
            return;
        }
        if ("roleInvite".equals(itemRule.getPeerUserType())) {
            List<Rater> roleEmps = listEmp(tenantId.getId(), Arrays.asList(itemRule.getPeerUserValue().split(",")));
            itemRule.setRoleAppointerPeers(roleEmps);
            return;
        }
        if ("managerInvite".equals(itemRule.getPeerUserType())) {
            List<Rater> managers = listEmp(tenantId.getId(), Arrays.asList(itemRule.getPeerUserValue().split(",")));
            itemRule.setManagerAppointerPeers(managers);
        }
    }

    private List<Rater> listEmp(String companyId, List<String> empIds) {
        ComQB comQB = ComQB.build(EmployeeBaseInfoDo.class)
                .clearSelect().select("employee_id as emp_id, name as emp_name")
                .setRsType(Rater.class)
                .whereEqReq("company_id", companyId)
                .whereEq("is_delete", "false")
                .whereInReq("employee_id", empIds);
        return domainDao.listAll(comQB);
    }

    private List<EmployeeBaseInfoDo> listEmpInfo(String companyId, List<String> empIds) {
        ComQB comQB = ComQB.build(EmployeeBaseInfoDo.class)
                .whereEqReq("company_id", companyId)
                .whereEq("is_delete", "false")
                .whereInReq("employee_id", empIds);
        return domainDao.listAll(comQB);
    }

    private void initSubUserName(TenantId tenantId, EvalItemScoreRule itemRule) {
        if ("examInvite".equals(itemRule.getSubUserType())) {
            itemRule.setSubUserName("被考核人");
            return;
        }
        if ("empInvite".equals(itemRule.getSubUserType())) {
            List<Rater> emps = listEmp(tenantId.getId(), Arrays.asList(itemRule.getSubUserValue()));
            itemRule.setSubUserName(emps.get(0).getEmpName());
            return;
        }
        if ("roleInvite".equals(itemRule.getSubUserType())) {
            List<Rater> roleEmps = listEmp(tenantId.getId(), Arrays.asList(itemRule.getSubUserValue().split(",")));
            itemRule.setRoleAppointerSubs(roleEmps);
            return;
        }
        if ("managerInvite".equals(itemRule.getSubUserType())) {
            List<Rater> managers = listEmp(tenantId.getId(), Arrays.asList(itemRule.getSubUserValue().split(",")));
            itemRule.setManagerAppointerSubs(managers);
        }
    }


    @Override
    public void updateMutualRule(Collection<EvalItemScoreRule> changedRules) {
        for (EvalItemScoreRule itemRule : changedRules) {
            PerfEvaluateTaskItemScoreRuleDo custRule = new ToDataBuilder<>(itemRule, PerfEvaluateTaskItemScoreRuleDo.class).data();
            if (itemRule.getId() == null) {
                if (StrUtil.isBlank(itemRule.getId())) {
                    custRule.setId(evalCommonGen.get());
                }
                domainDao.save(custRule);
            } else {
                domainDao.update(custRule);
            }
        }
    }

    public void addItemRule(EvalItemScoreRule itemScoreRule) {
        if (StrUtil.isBlank(itemScoreRule.getId())) {
            itemScoreRule.setId(evalCommonGen.get());
        }
        PerfEvaluateTaskItemScoreRuleDo custRule = new ToDataBuilder<>(itemScoreRule, PerfEvaluateTaskItemScoreRuleDo.class).data();
        domainDao.add(PerfEvaluateTaskItemScoreRuleDo.class, custRule);
    }

    @Override
    public void updateIndexRaters(EmpEvalMerge forIndexRaters) {
        UpdateBuilder up = UpdateBuilder.build(EmpEvalRuleDo.class)
                .set("index_raters", JSONUtil.toJsonStr(forIndexRaters.getIndexRaters()))
                .whereEqReq("companyId", forIndexRaters.getCompanyId().getId())
                .whereEqReq("emp_eval_id", forIndexRaters.getEmpEvalId())
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        domainDao.update(up);
    }

    @Override
    public void updateInterviewConf(EmpEvalRule empEvalRule) {
        UpdateBuilder up = UpdateBuilder.build(EmpEvalRuleDo.class)
                .set("interview_conf", JSON.toJSONString(empEvalRule.getInterviewConf()))
                .whereEqReq("companyId", empEvalRule.getCompanyId().getId())
                .whereEqReq("emp_eval_id", empEvalRule.getEmpEvalId())
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        domainDao.update(up);
    }


    @Override
    public void updateTransferScoringIf(EmpEvalMerge empEvalMerge) {
        domainDao.update(EmpEvalRuleDo.class, empEvalMerge);
        for (EmpEvalKpiType data : empEvalMerge.getKpiTypes().getDatas()) {
            EmpEvalKpiTypeDo typeDo = new EmpEvalKpiTypeDo();
            BeanUtil.copyProperties(data, typeDo);
            typeDo.setCompanyId(data.getCompanyId().getId());
            UpdateBuilder up = UpdateBuilder.build("emp_eval_kpi_type").setBean(typeDo)
                    .whereEqReq("companyId", typeDo.getCompanyId())
                    .whereEqReq("task_user_id", typeDo.getTaskUserId())
                    .whereEqReq("kpi_type_id", typeDo.getKpiTypeId())
                    .whereEqReq("is_deleted", Boolean.FALSE.toString());
            domainDao.update(up);
            for (EvalKpi item : data.getItems()) {
                domainDao.update(PerfEvaluateTaskKpiDo.class, item);
                if (item.getItemScoreRule() != null && StrUtil.isNotBlank(item.getItemScoreRule().getId())) {// 通过继承模拟的数据不在数据库,无需更新
                    domainDao.update(PerfEvaluateTaskItemScoreRuleDo.class, item.getItemScoreRule());
                }
            }
        }
    }

    @Override
    public void updateTransferScoringIfV3(EmpEvalMerge empEvalMerge) {
        String companyId = empEvalMerge.getCompanyId().getId();
        String taskUserId = empEvalMerge.getEmpEvalId();
        domainDao.update(EmpEvalRuleDo.class, empEvalMerge);
        List<EmpEvalKpiTypeDo> dos = new ArrayList<>();
        List<PerfEvaluateTaskKpiDo> kpiDos = new ArrayList<>();
        List<PerfEvaluateTaskItemScoreRuleDo> ruleDos = new ArrayList<>();
        empEvalMerge.getKpiTypes().getDatas().forEach(kpiType -> {
            EmpEvalKpiTypeDo kpiTypeDo = new ToDataBuilder<>(kpiType, EmpEvalKpiTypeDo.class).data();
            kpiTypeDo.setUpdatedTime(new Date());
            dos.add(kpiTypeDo);
            //维度单独配置的评价规则，也需要更新
            if (kpiType.getTypeRule() != null && StrUtil.isNotBlank(kpiType.getTypeRule().getId())) {// 通过继承模拟的数据不在数据库,无需更新
                PerfEvaluateTaskItemScoreRuleDo itemRuleDo = new ToDataBuilder<>(kpiType.getTypeRule(), PerfEvaluateTaskItemScoreRuleDo.class).data();
                itemRuleDo.setUpdatedTime(new Date());
                ruleDos.add(itemRuleDo);
            }
            if (CollUtil.isEmpty(kpiType.getItems())) {
                return;
            }
            kpiType.getItems().forEach(item -> {
                PerfEvaluateTaskKpiDo kpiDo = new ToDataBuilder<>(item, PerfEvaluateTaskKpiDo.class).data();
                kpiDo.setUpdatedTime(new Date());
                kpiDos.add(kpiDo);
                if (item.getItemScoreRule() != null && StrUtil.isNotBlank(item.getItemScoreRule().getId())) {// 通过继承模拟的数据不在数据库,无需更新
                    PerfEvaluateTaskItemScoreRuleDo itemRuleDo = new ToDataBuilder<>(item.getItemScoreRule(), PerfEvaluateTaskItemScoreRuleDo.class).data();
                    itemRuleDo.setUpdatedTime(new Date());
                    ruleDos.add(itemRuleDo);
                }
            });
        });

        BatchUpdateBuilder up = BatchUpdateBuilder.buildTable("emp_eval_kpi_type")
                .addSetCaseProp("superRater", "kpiTypeId:=")
                .addSetCaseProp("appointRater", "kpiTypeId:=")
                .addSetCaseProp("peerRater", "kpiTypeId:=")
                .addSetCaseProp("subRater", "kpiTypeId:=")
                .addSetCaseProp("updatedTime", "kpiTypeId:=")
                .whereEq("companyId", companyId)
                .whereEq("taskUserId", taskUserId)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereUseIn("kpiTypeId");
        //up 更新指标
        BatchUpdateBuilder upKpi = BatchUpdateBuilder.buildTable("perf_evaluate_task_kpi")
                .addSetCaseProp("scorerObjId", "id:=")
                .addSetCaseProp("updatedTime", "id:=")
                .whereEq("companyId", companyId)
                .whereEq("taskUserId", taskUserId)
                .whereEq("isDeleted", "false")
                .whereUseIn("id");
        //up 更新指标rule PerfEvaluateTaskItemScoreRuleDo
        BatchUpdateBuilder upRule = BatchUpdateBuilder.buildTable("perf_evaluate_task_item_score_rule")
                .addSetCaseProp("peerRater", "id:=")
                .addSetCaseProp("subRater", "id:=")
                .addSetCaseProp("superRater", "id:=")
                .addSetCaseProp("appointRater", "id:=")
                .addSetCaseProp("updatedTime", "id:=")
                .whereEq("companyId", companyId)
                .whereEq("taskUserId", taskUserId)
                .whereEq("isDeleted", "false")
                .whereUseIn("id");
        if (CollUtil.isNotEmpty(dos)) {
            up.addAllBean(dos);
            domainDao.updateBatch(up);
        }
        if (CollUtil.isNotEmpty(kpiDos)) {
            upKpi.addAllBean(kpiDos);
            domainDao.updateBatch(upKpi);
        }
        if (CollUtil.isNotEmpty(ruleDos)) {
            upRule.addAllBean(ruleDos);
            domainDao.updateBatch(upRule);
        }
    }

    @Override
    public void addFormulaFields(EmpEvalRule evalRule) {
        EmpRule2DataBd builder = new EmpRule2DataBd(evalRule, formulaFieldGen);
        builder.builderFormulaField();
        UpdateBuilder up = UpdateBuilder.build(PerfEvaluateTaskFormulaFieldDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("companyId", evalRule.getCompanyId().getId())
                .whereEqReq("task_user_id", evalRule.getEmpEvalId())
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        domainDao.update(up);
        domainDao.saveBatch(builder.getFormulaFields());    //指标计算的字段及值
    }

    public void delEmpEvalRule(TenantId companyId, String taskUserIds){
        this.delEmpEvalRule(companyId, Arrays.asList(taskUserIds));
    }
    @Override
    public void delEmpEvalRule(TenantId companyId, List<String> taskUserIds) {
        //del audit
        DeleteBuilder audit = DeleteBuilder.build(TaskAuditDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("task_user_id", taskUserIds);
        domainDao.delete(audit);

        //del type
        DeleteBuilder type = DeleteBuilder.build(EmpEvalKpiTypeDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("task_user_id", taskUserIds)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.delete(type);

        DeleteBuilder delTypeUsed = DeleteBuilder.build(EmpEvalTypeUsedFieldDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("task_user_id", taskUserIds);
        domainDao.delete(delTypeUsed);

        //del okrType
        DeleteBuilder okrType = DeleteBuilder.build(PerfEvaluateTaskOkrTypeDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("task_user_id", taskUserIds)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.delete(okrType);

        //del 指标
        DeleteBuilder item = DeleteBuilder.build(PerfEvaluateTaskKpiDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("task_user_id", taskUserIds)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.delete(item);

        //del 指标
        DeleteBuilder itemUserd = DeleteBuilder.build(PerfEvaluateItemUsedFieldDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("task_user_id", taskUserIds);
        domainDao.delete(itemUserd);

        //del 自动计算公式
        DeleteBuilder delFormulaField = DeleteBuilder.build(PerfEvaluateTaskFormulaFieldDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("task_user_id", taskUserIds)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.delete(delFormulaField);

        //del itemRule
        DeleteBuilder itemRuleDel = DeleteBuilder.build(PerfEvaluateTaskItemScoreRuleDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("task_user_id", taskUserIds)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.delete(itemRuleDel);

        //UpdateBuilder editQB = UpdateBuilder.build(EmpEvalRuleDo.class)
        //        .set("evaluate_type", rule.getEvaluateType())
        //        .set("index_raters", JSONObject.toJSONString(rule.getIndexRaters()))
        //        .set("s3_self_rater", JSON.toJSONString(rule.getS3SelfRater()))
        //        .set("s3_peer_rater", JSON.toJSONString(rule.getS3PeerRater()))
        //        .set("s3_sub_rater", JSON.toJSONString(rule.getS3SubRater()))
        //        .set("s3_super_rater", JSON.toJSONString(rule.getS3SuperRater()))
        //        .set("score_value_conf", JSON.toJSONString(rule.getScoreValueConf()))
        //        .set("type_weight_conf", JSON.toJSONString(rule.getTypeWeightConf()))
        //        .whereEqReq("company_id", rule.getCompanyId().getId())
        //        .whereEqReq("emp_eval_id", rule.getEmpEvalId())
        //        .whereEq("is_deleted", Boolean.FALSE.toString());
        //domainDao.update(editQB);

        //del evalRule
        DeleteBuilder empEvalRuleDel = DeleteBuilder.build(EmpEvalRuleDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("emp_eval_id", taskUserIds)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.delete(empEvalRuleDel);

        //del deadLineJob
        DeleteBuilder deadLineDel = DeleteBuilder.build(DeadLineJobDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("task_user_id", taskUserIds)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.delete(deadLineDel);


        //清除指标确认暂存
        UpdateBuilder upCache = UpdateBuilder.build(CompanyCacheInfoDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEq("company_id", companyId.getId())
                .whereInReq("link_id", taskUserIds)
                .whereEq("business_scene", EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT.getScene())
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.update(upCache);
    }

    @Override
    public List<EvalAudit> saveReusltAudit(EmpEvalRule rule, String taskUserId, String empId, String taskId, Integer level) {
        this.updateEmpEvalRule(rule);
        UpdateBuilder updateBuilder = UpdateBuilder.build(TaskAuditDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", rule.getCompanyId().getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEqReq("approver_info", "-1")
                .whereEqReq("scene", "final_result_audit");
        domainDao.update(updateBuilder);

        EmpRule2DataBd builder = new EmpRule2DataBd(rule, kpiItemGen, auditIdGen, evalCommonGen, formulaFieldGen, false);
        builder.bdResult(taskId, empId, level);
        domainDao.saveBatch(builder.getAuditResultAudits());
        List<EvalAudit> auditList = new ArrayList<>();
        for (TaskAuditDo auditResultAudit : builder.getAuditResultAudits()) {
            EvalAudit evalAudit = new EvalAudit();
            evalAudit.setId(auditResultAudit.getId());
            evalAudit.setTaskId(auditResultAudit.getTaskId());
            evalAudit.setTaskUserId(auditResultAudit.getTaskUserId());
            evalAudit.setApprovalOrder(auditResultAudit.getApprovalOrder());
            evalAudit.setEmpId(auditResultAudit.getEmpId());
            evalAudit.setApproverInfo(auditResultAudit.getApproverInfo());
            evalAudit.setScene(auditResultAudit.getScene());
            evalAudit.setMultipleReviewersType(auditResultAudit.getMultipleReviewersType());
            evalAudit.setCompanyId(new TenantId(auditResultAudit.getCompanyId()));
            auditList.add(evalAudit);
        }
        return auditList;
    }

    @Override
    public void updateShowResultType(TenantId tenantId, List<String> taskUserIds, Integer showResultType) {
        UpdateBuilder updateBuilder = UpdateBuilder.build(EmpEvalRuleDo.class)
                .set("show_result_type", showResultType)
                .whereEqReq("company_id", tenantId.getId())
                .whereInReq("emp_eval_id", taskUserIds)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.update(updateBuilder);
    }


    public void updateScoreResultTaskAuditId(String id, String taskAuditId) {
        UpdateBuilder updateBuilder = UpdateBuilder.build(PerfEvaluateTaskScoreResultDo.class)
                .set("task_audit_id", taskAuditId)
                .whereEq("id", id);
        domainDao.update(updateBuilder);
    }
}
