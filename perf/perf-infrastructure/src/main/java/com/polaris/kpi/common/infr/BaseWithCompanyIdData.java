package com.polaris.kpi.common.infr;

import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.sdk.type.TenantId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.annotations.Ckey;
import org.lufei.ibatis.common.DPS;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> lufei
 * @date 2022/3/22 7:47 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BaseWithCompanyIdData extends BaseTableData {
    @Ckey
    @DPS(value = TenantId.class, dmFname = "companyId")
    @JSONField(serialize = false)
    protected String companyId;//公司id
}
