package com.polaris.kpi.eval.infr.extData.repimpl;

import com.polaris.kpi.eval.infr.extData.pojo.ExtDataItemFieldCorrDo;
import com.polaris.kpi.extData.domain.entity.ExtDataItemFieldCorr;
import com.polaris.kpi.extData.domain.repo.ExtDataItemCorrRepo;
import org.lufei.ibatis.builder.DeleteBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: xuxw
 * @Date: 2025/04/07 13:39
 * @Description:
 */
@Component
public class ExtDataItemFieldCorrRepoImpl implements ExtDataItemCorrRepo<ExtDataItemFieldCorr> {
    private static final String EXT_DATA_ITEM_FIELD_CORR = "ext_data_item_field_corr";

    @Resource
    private DomainDaoImpl domainDao;

    @Override
    public void add(List<ExtDataItemFieldCorr> itemFieldCorr) {
        itemFieldCorr.forEach(s -> s.setId(domainDao.nextLongAsStr(EXT_DATA_ITEM_FIELD_CORR)));
        domainDao.addBatch(ExtDataItemFieldCorrDo.class, itemFieldCorr);
    }

    @Override
    public void update(List<ExtDataItemFieldCorr> itemFieldCorr) {
        for (ExtDataItemFieldCorr corr : itemFieldCorr){
            domainDao.update(ExtDataItemFieldCorrDo.class, corr);
        }
    }

    public void deleteByItemId(String itemId, String companyId) {
        DeleteBuilder builder = DeleteBuilder.build("ext_data_item_field_corr")
                .whereEqReq("company_id", companyId)
                .whereEqReq("kpi_item_id", itemId);
        domainDao.delete(builder);
    }
}
