package com.polaris.kpi.eval.infr.statics.repimpl;

import cn.hutool.core.util.ObjectUtil;
import com.polaris.kpi.eval.domain.statics.entity.PerfEmpStatics;
import com.polaris.kpi.eval.domain.statics.repo.PerfEmpStaticsRepo;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.infr.statics.dao.PerfEmpStaticsDao;
import com.polaris.kpi.eval.infr.statics.ppojo.PerfEmpStaticsDo;
import com.quick.common.util.date.DateTimeUtils;
import org.lufei.ibatis.builder.NativeSQLBuilder;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/1/20 17:51
 */
@Component
public class PerfEmpStaticsRepoImpl implements PerfEmpStaticsRepo {

    @Resource
    private DomainDaoImpl domainDao;
    @Autowired
    private PerfEmpStaticsDao perfEmpStaticsDao;

    @Override
    public PerfEmpStatics buildPerfEmp(EvalUser taskUser, AdminTask adminTask) {

        PerfEmpStatics empStatics = PerfEmpStatics.buildInstance(taskUser);
        empStatics.setTaskName(adminTask.getTaskName());
        //查询当前周期所有等级组中的最高/最低级别绩效等级
        Map<String, Set<String>> highLowLevels = perfEmpStaticsDao.queryHighLowLevels(taskUser.getCompanyId().getId(),taskUser.getCycleId());
        empStatics.calcLevelInfo(highLowLevels);
        if (Objects.isNull(empStatics.getLevelHeight())){
            return null;
        }
        return empStatics;

    }

    @Override
    public void addOrUpdatePerfEmpStatics(PerfEmpStatics statics) {

        if (ObjectUtil.isNull(statics)){
            return;
        }

        // 构建 NativeSQLBuilder 对象
        NativeSQLBuilder<PerfEmpStaticsDo> builder = NativeSQLBuilder.build(PerfEmpStaticsDo.class);

        StringBuilder sql = new StringBuilder("INSERT INTO perf_emp_statics (id, company_id, cycle_id, " +
                "task_id, task_user_id, task_name, " +
                "emp_id, emp_name, avatar, org_id, org_name, eval_org_id, eval_org_name, " +
                "at_org_name_path, at_org_code_path, " +
                "evaluation_level, evaluation_level_sort, final_score, perf_coefficient, level_height, " +
                "is_deleted, created_user, created_time, updated_user, updated_time, version) VALUES ");

        sql.append(String.format("('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', %d, %s, '%s', %d, '%s', '%s', '%s', '%s', '%s', %d)",
                UUID.randomUUID(),
                statics.getCompanyId(),
                statics.getCycleId(),
                statics.getTaskId(),
                statics.getTaskUserId(),
                statics.getTaskName(),
                statics.getEmpId(),
                statics.getEmpName(),
                statics.getAvatar(),
                statics.getOrgId(),
                statics.getOrgName(),
                statics.getEvalOrgId(),
                statics.getEvalOrgName(),
                statics.getAtOrgNamePath(),
                statics.getAtOrgCodePath(),
                statics.getEvaluationLevel(),
                statics.getEvaluationLevelSort(),
                statics.getFinalScore(),
                statics.getPerfCoefficient(),
                statics.getLevelHeight(),
                statics.getIsDeleted(),
                statics.getCreatedUser(),
                DateTimeUtils.now2StrDateTime(),
                statics.getUpdatedUser(),
                DateTimeUtils.now2StrDateTime(),
                0
        ));

        String insertPart = sql.toString();

        // 要更新的字段列表
        String[] updateFields = {"final_score","evaluation_level", "evaluation_level_sort", "perf_coefficient", "level_height", "updated_time"};

        // 构造 ON DUPLICATE KEY UPDATE 部分的 SQL
        StringBuilder updatePart = new StringBuilder(" ON DUPLICATE KEY UPDATE ");
        int index = 0;
        for (String field : updateFields) {
            if (index > 0) {
                updatePart.append(", ");
            }
            updatePart.append(field).append(" = VALUES(").append(field).append(")");
            index++;
        }

        // 合并 SQL
        String finalSql = insertPart + updatePart;
        builder.setSql(finalSql);

        domainDao.nativeExecute(builder);

    }

    @Override
    public void deleteByTaskUserId(String taskUserId,String companyId) {
        UpdateBuilder delete = UpdateBuilder.build(PerfEmpStaticsDo.class)
                .set("is_deleted", "true")
                .set("updated_time", DateTimeUtils.now2StrDateTime())
                .whereEqReq("task_user_id", taskUserId)
                .whereEqReq("company_id", companyId);
        domainDao.update(delete);
    }

}
