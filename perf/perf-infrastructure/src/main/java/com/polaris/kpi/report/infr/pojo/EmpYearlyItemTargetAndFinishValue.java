package com.polaris.kpi.report.infr.pojo;


import com.polaris.kpi.eval.domain.task.entity.Cycle;
import com.polaris.kpi.report.infr.query.ItemAnalysisQuery;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/7/24 16:02
 */
@Setter
@Getter
@NoArgsConstructor
public class EmpYearlyItemTargetAndFinishValue extends BaseItemAnalysisPo{

    private Boolean isYearlySummary = false;
    private Integer cycleValue;
    private String cycleType;
    private String cycleYear;
    private String empId;
    private String evalOrgId;

    public EmpYearlyItemTargetAndFinishValue(Integer cycleValue, String cycleType, String cycleYear) {
        this.cycleValue = cycleValue;
        this.cycleType = cycleType;
        this.cycleYear = cycleYear;
    }

    public void accItemInfo(EmpYearlyItemTargetAndFinishValue subFinishValue) {
        if (this.getItemName()!= null){
            return;
        }
        this.setItemId(subFinishValue.getItemId());
        this.setItemName(subFinishValue.getItemName());
        this.setItemUnit(subFinishValue.getItemUnit());
    }

    public void accCycleInfo(Cycle cycle, Integer cycleValue){
        this.cycleValue = cycleValue;
        this.cycleType = cycle.getType();
        this.cycleYear = cycle.getYear().toString();
    }
}
