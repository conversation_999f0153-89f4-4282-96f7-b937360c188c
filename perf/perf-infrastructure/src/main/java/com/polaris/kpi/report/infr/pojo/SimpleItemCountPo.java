package com.polaris.kpi.report.infr.pojo;


import cn.hutool.core.collection.CollUtil;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/22 13:38
 */
@Getter
@Setter
@NoArgsConstructor
public class SimpleItemCountPo {

    private Integer itemCnt; //指标数
    private Integer itemFinishedCnt; //指标完成数
    private Integer itemUpdatedEmpCnt; //已更新人数
    private Integer itemTotalEmpCnt; //总考核人数

    private List<ItemTargetAndFinishValuePo> items;


    public void calItemCnt() {
        if (CollUtil.isNotEmpty(items)){
            itemCnt = items.size();
            itemFinishedCnt = items.stream().filter(item -> // 如果 finishValue 大于等于 targetValue，返回 true
                    item.getFinishValue().compareTo(item.getTargetValue()) >= 0).mapToInt(item -> 1).sum();
        }
    }

    public void accItemCnt(Integer itemUpdatedEmpCnt, Integer itemTotalEmpCnt) {
        this.itemUpdatedEmpCnt += itemUpdatedEmpCnt;
        this.itemTotalEmpCnt += itemTotalEmpCnt;
    }
}
