package com.polaris.kpi.eval.infr.statics.ppojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/21 14:42
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HighLowLevelAnalysisPo {

    private Integer lowCount;
    private Integer highCount;
    private List<EmpPerf> lowLevelEmps;
    private List<EmpPerf> highLevelEmps;

    public HighLowLevelAnalysisPo(Integer lowCount, Integer highCount) {
        this.lowCount = lowCount;
        this.highCount = highCount;
    }
}
