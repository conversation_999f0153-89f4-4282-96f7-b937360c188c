//package com.polaris.kpi.report.infr.dao;
//
//import cn.hutool.core.util.StrUtil;
//import com.polaris.kpi.report.infr.pojo.CycleOrgSnapDo;
//import com.polaris.kpi.report.infr.pojo.CycleOrgSnapPo;
//import org.lufei.ibatis.builder.ComQB;
//import org.lufei.ibatis.dao.DomainDaoImpl;
//import org.lufei.ibatis.mapper.PagedList;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @date 2025/2/8 18:11
// */
//@Component
//public class CycleOrgSnapDao {
//
//    @Autowired
//    private DomainDaoImpl domainDao;
//
//
//    public List<CycleOrgSnapPo> listSnapChildOrgIds(String companyId, String cycleId, String orgId) {
//
//        ComQB comQB = ComQB.build(CycleOrgSnapDo.class,"parent")
//                .leftJoin(CycleOrgSnapDo.class,"child")
//                .appendOn(" parent.org_id = child.parent_org_id and child.cycle_id = parent.cycle_id")
//                .clearSelect()
//                .select("parent.org_id,parent.org_name,parent.parent_org_id,COUNT(child.org_id) AS childCount")
//                .setRsType(CycleOrgSnapPo.class)
//                .whereEqReq("parent.company_id", companyId)
//                .whereEqReq("parent.cycle_id", cycleId)
//                .whereEq("parent.parent_org_id", orgId)
//                .groupBy("parent.org_id,parent.org_name,parent.parent_org_id")
//                .orderBy("parent.org_id");
//
//        if (StrUtil.isBlank(orgId)) {
//            comQB.whereIsNull("parent.parent_org_id");
//        }
//
//        return domainDao.listAll(comQB);
//    }
//
//    public PagedList<CycleOrgSnapPo> searchOrg(String companyId,String cycleId ,String orgName, Integer pageNo, Integer pageSize) {
//
//        ComQB comQB = ComQB.build(CycleOrgSnapDo.class, "r")
//                .clearSelect()
//                .setRsType(CycleOrgSnapPo.class)
//                .select(new String[]{"*"})
//                .whereEqReq("company_id", companyId)
//                .whereEqReq("cycle_id", cycleId)
//                .whereLike("org_name", orgName)
//                .orderBy("org_id")
//                .setPage(pageNo, pageSize);
//        return domainDao.listPage(comQB);
//
//    }
//}
