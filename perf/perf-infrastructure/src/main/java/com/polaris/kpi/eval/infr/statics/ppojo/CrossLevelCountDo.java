package com.polaris.kpi.eval.infr.statics.ppojo;


import com.polaris.kpi.common.infr.BaseWithCompanyIdData;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.ibatis.annotations.Ckey;
import org.apache.ibatis.annotations.Table;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/9 15:30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Table("cross_level_count")
public class CrossLevelCountDo extends BaseWithCompanyIdData {

    @Ckey
    private String id;
    private String cycleId;
    private Integer upCountState;
    private Date upCountedTime;
    private Integer downCountState;
    private Date downCountedTime;
    private Integer performanceType;

}
