//package com.polaris.kpi.eval.infr.statics.dao;
//
//import com.polaris.kpi.eval.infr.cycle.ppojo.ConsecutiveCountsPo;
//import com.polaris.kpi.eval.infr.statics.ppojo.ConsecutiveStatisticDo;
//import com.polaris.kpi.eval.infr.task.query.report.PerfAnalysisQuery;
//import org.lufei.ibatis.builder.ComQB;
//import org.lufei.ibatis.dao.DomainDaoImpl;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//
///**
// * <AUTHOR>
// * @date 2025/2/23 22:32
// */
//@Component
//public class ConsecutiveStatisticDao {
//
//    @Resource
//    private DomainDaoImpl autoBaseDao;
//
//    public ConsecutiveCountsPo queryConsecutiveCounts(PerfAnalysisQuery qry) {
//
//        ComQB comQB = ComQB.build(ConsecutiveStatisticDo.class)
//                .clearSelect().select("*")
//                .setRsType(ConsecutiveCountsPo.class)
//                .whereEqReq("company_id", qry.getCompanyId())
//                .whereEqReq("cycle_id", qry.getCycleId())
//                .whereEqReq("performance_type", qry.getPerformanceType())
//                .whereEqReq("rule_type", qry.getRuleType())
//                .whereEqReq("is_deleted", Boolean.FALSE.toString());
//
//        return autoBaseDao.findOne(comQB);
//
//    }
//}
