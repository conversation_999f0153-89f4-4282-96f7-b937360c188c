package com.polaris.kpi.eval.infr.task.ppojo.calibrated;

import com.polaris.kpi.eval.domain.task.entity.empeval.AuditResultEvalType;
import com.polaris.kpi.eval.infr.task.ppojo.HistoryScorePo;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.RefEvalPo;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.annotations.JsonAryColumn;
import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.infr.task.ppojo.calibrated
 * @Author: lufei
 * @CreateTime: 2023-12-21  18:43
 * @Description: TODO
 * @Version: 1.0
 */
@Getter
@Setter
public class ResultAuditTaskUserPo {

    private String auditId;
    private String taskId;
    private String empId;
    private String avatar;
    private String empName;
    private String orgId;               //部门ID
    private String orgName;             //部门名称
    private Integer commentReq;         //校准备注是否必填写
    private BigDecimal finalScore;      //最终得分
    private String perfCoefficient;     //绩效系数
    private String stepId;
    private String evaluationLevel;         //等级
    private BigDecimal originalFinalScore;      //原始分数
    private String originalEvaluationLevel;     //原始等级
    private String originalPerfCoefficient;     //原始系数
    private String taskUserId;
    @JsonAryColumn(HistoryScorePo.HScore.class)
    private List<HistoryScorePo.HScore> historyScore;//历史分值
    private int calibrationStatus;      //校准状态
    private String orgNameStr;          //多级部门
    private List<AuditResultEvalType> types;
    private BigDecimal weightOfRef;//'在关联绩效的权重'
    private BigDecimal scoreOfRef;//'关联绩效后的得分
    private Integer showResultType;

    //@JsonAryColumn(ScoreRange.class)
    //private List<ScoreRange> scoreRanges;//等级分值评分范围
    //private String operateReason;           //操作结果
    private String scoreComment;            //校准理由
    private int scoreSummarySwitch;   //评分总结开关 0非必填/1必填/-1关闭 默认=-1
    private List<RefEvalPo> refEvalScores;

    public void loadCache(ResultAuditCacheDo cacheDo) {
        if (cacheDo == null) {
            return;
        }
        this.finalScore = cacheDo.getFinalScore();
        this.stepId = cacheDo.getStepId();
        this.evaluationLevel = cacheDo.getEvaluationLevel();
        this.perfCoefficient = cacheDo.getPerfCoefficient();
        this.scoreComment = cacheDo.getScoreComment();
        this.types = cacheDo.getTypeScores();
    }

    //将初评分数追加入历史分值
    public void loadHistoryScore(HistoryScorePo historyScore) {
        if (historyScore == null) {
            this.historyScore = originaScoreStr();
            return;
        }
        this.historyScore = historyScore.getHistoryScore();
        this.historyScore.addAll(originaScoreStr());
    }

    @NotNull
    private List<HistoryScorePo.HScore> originaScoreStr() {
        return Arrays.asList(new HistoryScorePo.HScore(originalFinalScore, originalEvaluationLevel,originalPerfCoefficient));
    }

    public void loadTypes(List<AuditResultEvalType> types) {
        this.types = types;
    }

    //private int performanceType;        // 绩效类型 1=个人绩效，2=组织绩效
    //private List<AuditResultEvalType> types;
    //private Integer createTotalLevelType;

}
