package com.polaris.kpi;


import com.perf.www.model.kpi.CompanyKpiItemModel;
import com.polaris.kpi.ind.infr.ppojo.CompanyKpiItemVO;
import com.polaris.kpi.eval.infr.companyItem.ppojo.CompanyItemUsedFieldDo;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateItemUsedFieldDo;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskKpiDo;
import com.polaris.kpi.eval.infr.task.ppojo.emptable.EmpTableItemUsedFieldDo;
import com.polaris.kpi.eval.infr.task.ppojo.emptable.EmpTableKpiItemDo;
import com.polaris.kpi.eval.infr.temp.ppojo.PerfTemplItemUsedFieldDo;
import com.polaris.kpi.eval.infr.temp.ppojo.PerfTemplKpiItemDo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 初始化指标自定义数据
 */
@Component
public class InitItemDao {

    @Autowired
    private DomainDaoImpl domainDao;


    public List<CompanyKpiItemVO> getCompanyKpiItem(String companyId) {

        ComQB comQB = ComQB.build(CompanyKpiItemModel.class, "c")
                .setRsType(CompanyKpiItemVO.class)
                .whereEq("c.is_deleted",Boolean.FALSE.toString())
                .whereNotNull("c.item_custom_field_json");
        if (StringUtils.isNotBlank(companyId)) {
            comQB.whereEq("c.company_id",companyId);
        }
        List<CompanyKpiItemVO> categoryIdList = domainDao.listAll(comQB);
        return categoryIdList;
    }


    public List<EmpTableKpiItemDo> getEmpTableKpiItem(String companyId) {

        ComQB comQB = ComQB.build(EmpTableKpiItemDo.class, "c")
                .setRsType(EmpTableKpiItemDo.class)
                .whereEq("c.is_deleted",Boolean.FALSE.toString())
                .whereNotNull("c.item_custom_field_json");
        if (StringUtils.isNotBlank(companyId)) {
            comQB.whereEq("c.company_id",companyId);
        }
        List<EmpTableKpiItemDo> categoryIdList = domainDao.listAll(comQB);
        return categoryIdList;
    }


    public List<PerfTemplKpiItemDo> getPerfTemplKpiItem(String companyId) {

        ComQB comQB = ComQB.build(PerfTemplKpiItemDo.class, "c")
                .setRsType(PerfTemplKpiItemDo.class)
                .whereEq("c.is_deleted",Boolean.FALSE.toString())
                .whereNotNull("c.item_custom_field_json");
        if (StringUtils.isNotBlank(companyId)) {
            comQB.whereEq("c.company_id",companyId);
        }
        List<PerfTemplKpiItemDo> categoryIdList = domainDao.listAll(comQB);
        return categoryIdList;
    }

    public List<PerfEvaluateTaskKpiDo> getPerfEvaluateTaskKpi(String companyId) {

        ComQB comQB = ComQB.build(PerfEvaluateTaskKpiDo.class, "c")
                .setRsType(PerfEvaluateTaskKpiDo.class)
                .whereEq("c.is_deleted",Boolean.FALSE.toString())
                .whereNotNull("c.item_custom_field_json");
        if (StringUtils.isNotBlank(companyId)) {
            comQB.whereEq("c.company_id",companyId);
        }
        List<PerfEvaluateTaskKpiDo> categoryIdList = domainDao.listAll(comQB);
        return categoryIdList;
    }


    public void saveCompanyItemUsedField(List<CompanyItemUsedFieldDo> fieldDoList) {
        if (CollectionUtils.isNotEmpty(fieldDoList)) {
            domainDao.saveBatch(fieldDoList);
        }
    }

    public void saveEmpTableKpiItemUsedField(List<EmpTableItemUsedFieldDo> fieldDoList) {
        if (CollectionUtils.isNotEmpty(fieldDoList)) {
            domainDao.saveBatch(fieldDoList);
        }
    }

    public void savePerfTemplItemUsedField(List<PerfTemplItemUsedFieldDo> fieldDoList) {
        if (CollectionUtils.isNotEmpty(fieldDoList)) {
            domainDao.saveBatch(fieldDoList);
        }
    }

    public void savePerfEvaluateItemUsedField(List<PerfEvaluateItemUsedFieldDo> fieldDoList) {
        if (CollectionUtils.isNotEmpty(fieldDoList)) {
            domainDao.saveBatch(fieldDoList);
        }
    }
}
