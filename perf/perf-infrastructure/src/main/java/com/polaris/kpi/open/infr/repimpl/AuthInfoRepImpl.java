package com.polaris.kpi.open.infr.repimpl;

import cn.hutool.core.lang.Assert;
import com.polaris.kpi.open.domain.entity.AuthInfo;
import com.polaris.kpi.open.domain.rep.AuthInfoRep;
import com.polaris.kpi.open.infr.ppojo.AuthInfoDo;
import com.polaris.kpi.org.domain.dept.type.CorpId;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.common.DomainToDataBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class AuthInfoRepImpl implements AuthInfoRep {

    private DomainDaoImpl domainDao;
    @Autowired
    public void setDomainDao(DomainDaoImpl domainDao) {
        this.domainDao = domainDao;
    }

    @Override
    public void store(AuthInfo authInfo) {
        AuthInfoDo authInfoDo = new AuthInfoDo();
        new DomainToDataBuilder<AuthInfo>(authInfo, authInfoDo).build();
        int rows = this.domainDao.update(authInfoDo);
        if (rows > 0) {
            return;
        }
        this.domainDao.save(authInfoDo);
    }

    @Override
    public AuthInfo getValidAuthInfo(CorpId corpId) {
        ComQB comQB = ComQB.build(AuthInfoDo.class)
                .whereEq("corp_id", corpId.getCorpId())
                .limit(0, 1);
        //AuthInfoDo data = this.domainDao.findOne(comQB);
        AuthInfo authInfo = this.domainDao.findDomain(comQB, AuthInfo.class);
        Assert.notNull(authInfo, "50001: access forbidden");
        authInfo.assertAuthExpires();
        authInfo.assertAccessLimit();
        return authInfo;
    }
}
