package com.polaris.kpi.migration.pojo;

import com.polaris.kpi.common.infr.DataObj;
import com.polaris.sdk.type.TenantId;
import lombok.Data;
import org.apache.ibatis.annotations.Ckey;
import org.lufei.ibatis.common.DPS;

@Data
public class DingMigrationTaskDo extends DataObj {
    @Ckey
    private String id;//
    private String companyId;//新应用新授权的公司id
    private String dingCorpId;
    private String dingAgentId;
    private String dingSpaceId;
    private String migrationCompanyJson;//迁移公司数据，备份（用于回滚恢复）
    private String ossPath;
    private String status;// 0:未开始 1:进行中 2:已完成 3:失败
}
