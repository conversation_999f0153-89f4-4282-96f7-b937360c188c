package com.polaris.kpi.notice.repimpl;

import cn.hutool.core.collection.CollUtil;
import com.polaris.kpi.notice.domain.entity.Announcement;
import com.polaris.kpi.notice.domain.entity.Tip;
import com.polaris.kpi.notice.domain.rep.AnnouncementRep;
import com.polaris.kpi.notice.ppojo.EmpRefAnnouncementDo;
import com.polaris.kpi.notice.ppojo.TipDo;
import com.polaris.kpi.notice.ppojo.TipReadDo;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.dao.AutoBaseDao;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class AnnouncementRepImpl implements AnnouncementRep {
    @Autowired
    private AutoBaseDao dao;

    @Override
    public void readerAnnouncement(Announcement query) {
        dao.save(new EmpRefAnnouncementDo(query));
    }

    @Autowired
    private DomainDaoImpl domainDao;

    public List<Tip> readTips(String companyId, String empId) {
        ComQB qb = ComQB.build(TipDo.class, "t")
                .clearSelect().select("t.id,t.company_id")
                .whereEqReq("company_id", "0");
        List<Tip> tips = domainDao.listAllDomain(qb, Tip.class);
        if (tips.isEmpty()) {
            return tips;
        }
        List<String> tipIds = CollUtil.map(tips, tip -> tip.getId(), true);
        ComQB qbReads = ComQB.build(TipReadDo.class, "t")
                .clearSelect().select("tip_id").setRsType(String.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("emp_id", empId)
                .whereIn("tip_id", tipIds);
        List<String> readedIds = domainDao.listAll(qbReads);
        tips.removeIf(tip -> readedIds.contains(tip.getId()));
        List<TipReadDo> collect = tips.stream().map(tip -> {
            return new TipReadDo(tip.getId(), companyId, empId);
        }).collect(Collectors.toList());
        domainDao.saveBatch(collect);
        return tips;
    }
}
