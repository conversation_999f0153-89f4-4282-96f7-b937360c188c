package com.polaris.kpi.report.infr.pojo;


import com.polaris.kpi.eval.domain.task.entity.Cycle;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/7/23 15:09
 */
@Getter
@Setter
@NoArgsConstructor
public class OrgItemTrendPo extends BaseItemAnalysisPo{

    private String orgId;
    private String orgName;
    private Integer orgPathHeight;
    private Integer cycleValue;
    private String cycleType;
    private String cycleYear;

    public OrgItemTrendPo(Integer cycleValue, String cycleType, String cycleYear) {
        this.cycleValue = cycleValue;
        this.cycleType = cycleType;
        this.cycleYear = cycleYear;
    }

    public void accItemInfo(OrgItemTrendPo orgItemTrendPo) {
        if (this.getItemName()!= null){
            return;
        }
        this.setItemId(orgItemTrendPo.getItemId());
        this.setItemName(orgItemTrendPo.getItemName());
        this.setItemUnit(orgItemTrendPo.getItemUnit());
    }
}
