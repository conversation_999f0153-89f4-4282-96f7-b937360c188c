package com.polaris.kpi.eval.infr.cycle.repimpl;

import cn.hutool.core.collection.CollUtil;
import com.polaris.kpi.eval.domain.cycle.entity.AdminOfCycle;
import com.polaris.kpi.eval.domain.cycle.repo.CycleRepo;
import com.polaris.kpi.eval.domain.cycle.type.CycleOperationEnum;
import com.polaris.kpi.eval.domain.task.entity.Cycle;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminCycleOperation;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminCycleOperationDetail;
import com.polaris.kpi.eval.domain.task.entity.admineval.EvalRuleLogField;
import com.polaris.kpi.eval.domain.task.entity.admineval.EvalRuleOpLogMeta;
import com.polaris.kpi.eval.infr.cycle.ppojo.AdminOfCycleDo;
import com.polaris.kpi.eval.infr.task.ppojo.CycleDo;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskBaseDo;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskUserDo;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.AdminCycleOperationDetailDo;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.AdminCycleOperationDo;
import com.polaris.kpi.org.domain.emp.entity.KpiEmployee;
import com.polaris.kpi.org.infr.emp.dao.KpiEmpDao;
import com.polaris.sdk.type.TenantId;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.DeleteBuilder;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.common.data.ToDataBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class CycleRepoImpl implements CycleRepo {

    private static final String cycleSeq = "cycle_seq";
    public static final String diffLog = "admin_cycle_operation";
    private static final String ADMIN_CYCLE_OPERATION_DETAIL = "admin_cycle_operation_detail";

    @Resource
    private DomainDaoImpl domainDao;
    @Autowired
    private KpiEmpDao kpiEmpDao;

    public void setDomainDao(DomainDaoImpl domainDao) {
        this.domainDao = domainDao;
    }

    public void setKpiEmpDao(KpiEmpDao kpiEmpDao) {
        this.kpiEmpDao = kpiEmpDao;
    }

    @Override
    public String createCycle(Cycle domain) {
        domain.initOnNew(domainDao.nextLongAsStr(cycleSeq));
        domainDao.add(CycleDo.class, domain);
        return domain.getId();
    }

    @Override
    public void createAdminOfCycle(AdminOfCycle domain) {
        domainDao.add(AdminOfCycleDo.class, domain);
        return;
    }

    @Override
    public void updateAdminOfCycle(AdminOfCycle adminOfCycle) {
        AdminOfCycleDo data = new ToDataBuilder<>(adminOfCycle, AdminOfCycleDo.class).data();
        domainDao.update(data);
    }

    @Override
    public void removeAdminOfCycle(AdminOfCycle adminOfCycle) {
        AdminOfCycleDo data = new ToDataBuilder<>(adminOfCycle, AdminOfCycleDo.class).data();
        domainDao.update(data);
    }

    @Override
    public void terminatedCycle(String companyId, String cycleId, String opEmpId) {
        UpdateBuilder up = UpdateBuilder.build(CycleDo.class)
                .whereEqReq("id", cycleId)
                .whereEqReq("company_id", companyId)
                .set("updated_user", opEmpId)
                .set("cycle_status", "terminated");
        domainDao.update(up);
    }

    @Override
    public void deletedCycle(String companyId, String cycleId) {
        DeleteBuilder delete = DeleteBuilder.build(CycleDo.class)
                .whereEqReq("id", cycleId)
                .whereEqReq("company_id", companyId);
        domainDao.delete(delete);

        DeleteBuilder delCycleAdmin = DeleteBuilder.build(AdminOfCycleDo.class)
                .whereEqReq("cycle_id", cycleId)
                .whereEqReq("company_id", companyId);
        domainDao.delete(delCycleAdmin);
    }

    @Override
    public void editCycleBasicInfo(Cycle cycle) {
        CycleDo data = new ToDataBuilder<>(cycle, CycleDo.class).data();
        domainDao.update(data);

        //同步修改任务上的周期截止日期
        UpdateBuilder taskUp = UpdateBuilder.build(PerfEvaluateTaskBaseDo.class)
                .set("cycle_start_date", cycle.getCycleStart())
                .set("cycle_end_date",cycle.getCycleEnd())
                .whereEqReq("company_id", cycle.getCompanyId().getId())
                .whereEqReq("cycle_id", cycle.getId());
        domainDao.update(taskUp);
    }

    @Override
    public void saveDiffLog(AdminCycleOperation operation) {
        String id = domainDao.nextLongAsStr(diffLog);
        operation.setId(id);
        operation.initOperationDetails();
        AdminCycleOperationDo data = new ToDataBuilder<>(operation, AdminCycleOperationDo.class).data();
        domainDao.save(data);
        List<AdminCycleOperationDetail> operationDetails = operation.getOperationDetails();
        if (CollUtil.isNotEmpty(operationDetails)){
            for (AdminCycleOperationDetail op : operationDetails){
                op.setCycleOperationId(id);
                op.setId(domainDao.nextLongAsStr(ADMIN_CYCLE_OPERATION_DETAIL));
                op.setCompanyId(operation.getCompanyId());
            }
            domainDao.addBatch(AdminCycleOperationDetailDo.class, operationDetails);
        }
    }


    @Override
    public void saveBatchDiffLog(List<AdminCycleOperation> operationList) {
        List<AdminCycleOperationDo> saveList = operationList.stream().map(item -> {
            item.setId(domainDao.nextLongAsStr(diffLog));
            AdminCycleOperationDo data = new ToDataBuilder<>(item, AdminCycleOperationDo.class).data();
            return data;
        }).collect(Collectors.toList());
        domainDao.saveBatch(saveList);
    }

    @Override
    public void refreshCycleEmpCnt(TenantId tenantId, String cycleId) {
        ComQB cntQb = ComQB.build(PerfEvaluateTaskUserDo.class)
                .clearSelect().select("count(1) cnt").setRsType(Integer.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("cycle_id", cycleId)
                .whereEq("is_deleted", "false");
        Integer evalCnt = domainDao.findOne(cntQb);

        UpdateBuilder totCnt = UpdateBuilder.build(CycleDo.class)
                .set("eval_cnt", evalCnt)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("id", cycleId);
        domainDao.update(totCnt);
    }

    @Override
    public void updateArchiveStatus(String companyId, String cycleId, String opEmpId, Integer archiveStatus, String cycleName, String opAdminType) {
        CycleOperationEnum operationEnum = archiveStatus == 0 ? CycleOperationEnum.UNARCHIVE_CYCLE : CycleOperationEnum.ARCHIVE_CYCLE;
        UpdateBuilder up = UpdateBuilder.build(CycleDo.class)
                .whereEqReq("id", cycleId)
                .whereEqReq("company_id", companyId)
                .set("updated_user", opEmpId)
                .set("archive_status", archiveStatus);
        domainDao.update(up);
        KpiEmployee opEmp = kpiEmpDao.findEmployee(new TenantId(companyId), opEmpId);
        // 保存周期日志
        AdminCycleOperation operation = new AdminCycleOperation(cycleId, companyId);
        operation.setChangeType(operationEnum.getValue());
        operation.setAdminType(opAdminType);
        operation.setOperatorId(opEmp.getEmployeeId());
        operation.setOperatorName(opEmp.getName());
        operation.setOperatorAvatar(opEmp.getAvatar());
        operation.setOperationTime(System.currentTimeMillis());
        EvalRuleOpLogMeta logMeta = new EvalRuleOpLogMeta("考核周期归档", "");
        logMeta.addField(EvalRuleLogField.createIf("周期名称", cycleName, null));
        operation.setBaseInfo(Arrays.asList(logMeta));
        this.saveDiffLog(operation);
    }

}
