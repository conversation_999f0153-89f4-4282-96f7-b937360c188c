package com.polaris.kpi.eval.infr.acl.ding.imp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapWrapper;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.perf.www.common.constant.BusinessConstant;
import com.perf.www.common.utils.http.HttpUtil;
import com.polaris.acl.dept.pojo.CompanyDo;
import com.polaris.acl.dept.pojo.CompanyPo;
import com.polaris.acl.dept.repository.DeptEmpDao;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrAction;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrActionUpdate;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrTarget;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrTask;
import com.polaris.kpi.eval.domain.task.ext.EvalOkrItemInfo;
import com.polaris.kpi.eval.domain.task.ext.IOkrAclSvc;
import com.polaris.kpi.org.domain.dept.entity.Company;
import com.polaris.kpi.org.domain.dept.entity.CompanyConf;
import com.polaris.kpi.org.domain.dept.type.CorpId;
import com.polaris.kpi.org.domain.emp.entity.KpiEmployee;
import com.polaris.kpi.org.infr.company.dao.CompanyDaoImpl;
import com.polaris.kpi.org.infr.company.dao.GlobalSysParamDao;
import com.polaris.kpi.org.infr.company.dao.TenantSysConfDao;
import com.polaris.kpi.org.infr.company.ppojo.GlobalSysParamDo;
import com.polaris.kpi.org.infr.emp.dao.KpiEmpDao;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: lufei
 * @CreateTime: 2022-09-22  09:59
 * @Version: 1.0
 */
@Service
@Slf4j
@ConditionalOnMissingClass({"com.polaris.kpi.eval.infr.acl.okr.OkrAclSvcImpl"})
public class EvalOkrAclImpl implements IOkrAclSvc {
    @Autowired
    private GlobalSysParamDao paramDao;
    @Value("${okr.host}")
    private String okrHost;
    @Value("${okr.accessKey}")
    private String accessKey;
    @Value("${okr.accessSecret}")
    private String accessSecret;
    @Value("${okr.dingOkrHost}")
    private String dingOkrHost;//https://dingokr.eapps.dingtalkcloud.com/
    @Autowired
    private DeptEmpDao deptEmpDao;
    @Autowired
    private TenantSysConfDao confDao;
    @Autowired
    private CompanyDaoImpl companyDao;
    @Autowired
    private KpiEmpDao empDao;
    //查询OKR接口url
    public static final String QUERY_TASK_LIST_URL = "perf/api/taskList";
    //新增指标分类和成果的关联关系 url
    public static final String ADD_REF_ACTION_URL = "perf/api/addRefAction";
    //更新成果值
    public static final String UPDATE_ACTION_URL = "perf/api/updateAction";
    //查询成果详情 url
    public static final String GET_ACTION_URL = "perf/api/getAction";

    public static final String GET_COMPANY_STATUS_URL = "perf/api/getAccessToken";
    /**
     * 查询最后一条成果更新记录
     */
    public static final String GET_ACTION_LAST_UPDATEINFO = "perf/api/getActionLastUpdateInfo";

    /**
     * 查询成果更新记录
     */
    public static final String LIST_ACTION_UPDATE_INFO = "perf/api/listActionUpdateInfos?actionId=";
    /**
     * 打开或关闭OKR那边的KPI开关URL
     */
    public static final String OPEN_KPI_URL = "perf/api/openKpi";
    /**
     * 获取公司在OKR的状态-无记录
     */
    public static final String COMPANY_STATUS_NO_RECORD = "noRecord";
    /**
     * 获取公司在OKR的状态-正常
     */
    public static final String COMPANY_STATUS_NORMAL = "normal";
    /**
     * 获取公司在OKR的状态-已过期
     */
    public static final String COMPANY_STATUS_EXPIRED = "expired";

    public String sendOKRRequest(String apiUrl, Map<String, String> params) {
        Long timeStamp = System.currentTimeMillis();
        params.put("timestamp", timeStamp.toString());
        params.put("nonce", UUID.randomUUID().toString());
        String accessToken = getAccessToken();
        params.put("access_token", accessToken);

        Map<String, String> headerParameters = new HashMap<>();
        headerParameters.put("access_token", accessToken);
        //是否是钉钉人事【oem】版OKR，缓冲期，待钉钉人事完成全部升级即可去掉
        String res = HttpUtil.postWithParams(getUrl(params.get("corpId")) + apiUrl, params, headerParameters);
        log.info("请求OKR接口={},参数={}，返回结果={}", getUrl(params.get("corpId")) + apiUrl, JSONObject.toJSONString(params), res);
        JSONObject jsonObject = JSONObject.parseObject(res);
        if (BusinessConstant.FALSE.equals(jsonObject.getString("success"))) {
            throw new RuntimeException(jsonObject.getString("message"));
        }
        return res;
    }

    private String getUrl(String corpId) {
        if (isOpenDingOkr(corpId)) {
            return dingOkrHost;
        } else {
            return okrHost;
        }
    }

    //是否是钉钉人事【oem】版OKR
    private boolean isOpenDingOkr(String corpId) {
        log.info("isOpenDingOkr,corpId={}", corpId);
        if (StrUtil.isBlank(corpId)) {
            return false;
        }
        CompanyDo companyDo = companyDao.findByDingCorpId(corpId);
        if (Objects.isNull(companyDo)) {
            return false;
        }
        CompanyConf companyConf = confDao.findCompanyConf(new TenantId(companyDo.getId()));
        log.info("companyConf,openDingOkr:{},companyConf={}", companyConf.openDingOkr(), JSONObject.toJSONString(companyConf));
        return companyConf.openDingOkr();
    }


    private String getAccessToken() {
        GlobalSysParamDo param = paramDao.createParamIfOpt("OKRAccessToken", this::getOKRAccessToken);
        return param.getParamValue();
    }

    private String getOKRAccessToken() {
        Map<String, String> params = new HashMap<>();
        params.put("accessKey", accessKey);
        params.put("accessSecret", accessSecret);

        String res = HttpUtil.postWithParams(okrHost + GET_COMPANY_STATUS_URL, params, null);
        JSONObject jsonObject = JSONObject.parseObject(res);
        if (BusinessConstant.FALSE.equals(jsonObject.getString("success"))) {
            throw new RuntimeException(jsonObject.getString("message"));
        } else {
            return jsonObject.getString("data");
        }
    }

    /**
     * @param corpId
     * @date: 2022/9/22 10:53
     * @return: java.util.List<com.polaris.kpi.eval.domain.task.entity.okr.OkrAction>
     */
    @Override
    public List<OkrAction> loadActionByIds(String corpId, List<String> actionIds) {
        List<List<String>> groups = CollUtil.split(new HashSet<>(actionIds), 50);
        List<OkrAction> rs = new ArrayList<>();
        for (List<String> group : groups) {
            Map<String, String> params = new HashMap<>();
            params.put("corpId", corpId);
            params.put("actionId", CollUtil.join(group, ","));
            String res = sendOKRRequest(GET_ACTION_URL, params);
            JSONObject jsonObject = JSONObject.parseObject(res);
            rs.addAll(jsonObject.getJSONArray("data").toJavaList(OkrAction.class));
        }
        return rs;
    }

    /**
     * 查询最后一条成果更新记录
     */
    @Override
    public Map<String, OkrActionUpdate> getActionLastUpdateInfo(String corpId, List<String> actionIds) {
        List<List<String>> groups = CollUtil.split(new HashSet<>(actionIds), 50);
        Map<String, OkrActionUpdate> rs = new HashMap<>();
        for (List<String> group : groups) {
            Map<String, String> params = new HashMap<>();
            params.put("corpId", corpId);
            params.put("ids", CollUtil.join(group, ","));
            String res = sendOKRRequest(GET_ACTION_LAST_UPDATEINFO, params);
            Map<String, OkrActionUpdate> data = (Map<String, OkrActionUpdate>) JSONObject.parseObject(res).get("data");
            rs.putAll(data);
        }
        return rs;
    }


    public ListWrap<OkrActionUpdate> listActionUpdateInfo(TenantId companyId, String empId, List<String> actionIds) {
        CompanyDo company = companyDao.getCompany(companyId);
        KpiEmployee emp = empDao.findEmployee(companyId, empId);
        ListWrap<OkrActionUpdate> updateListWrap = this.listActionUpdateInfos(company.getDingCorpId(), actionIds, emp.getDingUserId());
        return updateListWrap;
    }

    @Override
    public ListWrap<OkrActionUpdate> listActionUpdateInfos(String corpId, List<String> actionIds, String dingUserId) {
        List<OkrActionUpdate> updates = new ArrayList<>();
        List<List<String>> groups = CollUtil.split(new HashSet<>(actionIds), 50);
        for (List<String> group : groups) {
            Map<String, String> params = new HashMap<>();
            params.put("corpId", corpId);
            params.put("thirdUserId ", dingUserId);
            String actionId = CollUtil.join(group, ",");
            String res = sendOKRRequest(LIST_ACTION_UPDATE_INFO + actionId, params);
            JSONObject jsonObject = JSONObject.parseObject(res);
            JSONArray arr = jsonObject.getJSONArray("data");
            if (arr == null) {
                continue;
            }
            updates.addAll(arr.toJavaList(OkrActionUpdate.class));
        }
        updates.removeIf(update -> StrUtil.isBlank(update.getActionId()));//okr接口会返回空actionId的记录(okr中已删除了KR)
        return new ListWrap<>(updates).groupBy(okrActionUpdate -> okrActionUpdate.getActionId());
    }

    public void loadOkrIf(TenantId tenantId, List<? extends EvalOkrItemInfo> okrItems) {
        if (CollUtil.isEmpty(okrItems)) {
            return;
        }

        //"left join perf_evaluate_task_ref_okr o on (k.id = o.task_kpi_id or k.kpi_item_id = o.task_kpi_id) and  k.company_id = o.company_id  and o.is_deleted = 'false' " +
        CompanyPo company = deptEmpDao.getTenant(tenantId);
        List<String> actionIds = okrItems.stream().map(EvalOkrItemInfo::getActionId).collect(Collectors.toList());
        List<OkrAction> okrActions = loadActionByIds(company.getDingCorpId(), actionIds);
        Map<String, OkrAction> actionMap = okrActions.stream().collect(Collectors.toMap(OkrAction::getId, Function.identity()));
        for (EvalOkrItemInfo okrItem : okrItems) {
            //设置OKR信息
            if (actionMap.containsKey(okrItem.getActionId())) {
                OkrAction action = actionMap.get(okrItem.getActionId());
                action.splitTargetTag();
                okrItem.acceptOkrValue(action);
            }
            if (okrItem.refOkr()) {
                //添加更新记录
                List<OkrAction> okrActions2 = loadActionByItemId(company.getDingCorpId(), okrItem.getId());
                okrItem.setItemRefKRList(okrActions2);
            }
        }
    }

    public void loadOkrIf(TenantId tenantId, List<? extends EvalOkrItemInfo> okrItems, String empId) {
        if (CollUtil.isEmpty(okrItems)) {
            return;
        }

        //"left join perf_evaluate_task_ref_okr o on (k.id = o.task_kpi_id or k.kpi_item_id = o.task_kpi_id) and  k.company_id = o.company_id  and o.is_deleted = 'false' " +

        CompanyPo company = deptEmpDao.getTenant(tenantId);
        KpiEmployee emp = empDao.findEmployee(tenantId, empId);
        List<String> actionIds = okrItems.stream().map(EvalOkrItemInfo::getActionId).collect(Collectors.toList());
        List<OkrAction> okrActions = this.loadActionByIds(company.getDingCorpId(), actionIds);
        Map<String, OkrAction> actionMap = okrActions.stream().collect(Collectors.toMap(OkrAction::getId, Function.identity()));
        //批量一次性查OKR那边更新记录,提高性能
        ListWrap<OkrActionUpdate> updateInfos = this.listActionUpdateInfos(company.getDingCorpId(), actionIds, emp.getDingUserId());

        for (EvalOkrItemInfo okrItem : okrItems) {
            //设置OKR信息
            List<OkrActionUpdate> okrActionUpdates = updateInfos.groupGet(okrItem.getActionId());
            if (actionMap.containsKey(okrItem.getActionId())) {
                //添加更新记录
                OkrAction action = actionMap.get(okrItem.getActionId());
                action.splitTargetTag();
                action.setOkrActionUpdates(okrActionUpdates);
                okrItem.acceptOkrValue(action);
            }
            if (okrItem.refOkr()) {
                //添加更新记录
                List<OkrAction> okrActions2 = loadActionByItemId(company.getDingCorpId(), okrItem.getId());
                for (OkrAction okrAction : okrActions2) {
                    if (okrItem.getId().equals(okrAction.getId())) {
                        okrAction.setOkrActionUpdates(okrActionUpdates);
                    }
                }
                okrItem.setItemRefKRList(okrActions2);
            }
        }
    }


    @Override
    public List<OkrAction> loadActionByItemId(String corpId, String kpiItemId) {
        Map<String, String> params = new HashMap<>();
        params.put("corpId", corpId);
        params.put("refId", kpiItemId);
        String res = sendOKRRequest(GET_ACTION_URL, params);
        JSONObject jsonObject = JSONObject.parseObject(res);
        return jsonObject.getJSONArray("data").toJavaList(OkrAction.class);
    }

    /**
     * 新增指标分类和成果的关联关系
     *
     * @param actionId 成果id，多个逗号分隔
     * @param refId    内部关联id, 这里用的是perf_evaluate_task_okr_type表的id
     * @param corpId   企业id
     */
    @Override
    public void addRefAction(String corpId, String actionId, String refId) {
        Map<String, String> params = new HashMap<>();
        params.put("corpId", corpId);
        params.put("actionId", actionId);
        params.put("refId", refId);
        sendOKRRequest(ADD_REF_ACTION_URL, params);
    }

    @Override
    public void addRefAction(String corpId, String actionId, String refId, String optType, String dingUserId) {
        Map<String, String> params = new HashMap<>();
        params.put("corpId", corpId);
        params.put("actionId", actionId);
        params.put("refId", refId);
        params.put("optType", optType);
        params.put("dingUserId", dingUserId);
        sendOKRRequest(ADD_REF_ACTION_URL, params);
    }

    @Override
    public void updateAction(String corpId, String actionId, String finishValue, String memo, String dingUserId, String refId) {
        Map<String, String> params = new HashMap<>();
        params.put("corpId", corpId);
        params.put("actionId", actionId);
        params.put("refId", refId);
        params.put("finishValue", finishValue);
        params.put("memo", memo);
        params.put("dingUserId", dingUserId);
        sendOKRRequest(UPDATE_ACTION_URL, params);
    }

    public List<OkrTarget> listOkr(Map<String, String> params) {
        String listOkr = sendOKRRequest(QUERY_TASK_LIST_URL, params);
        JSONObject jsonObject = JSONObject.parseObject(listOkr);
        JSONArray arr = jsonObject.getJSONArray("data");
        if (arr == null) {
            return null;
        }
        List<OkrTask> okrTasks = JSONArray.parseArray(arr.toJSONString(), OkrTask.class);
        if (CollUtil.isEmpty(okrTasks)) {
            return null;
        }
        okrTasks.forEach(okrTask -> {
            //if (query.getOpenOkrScore() == 1) {
            //    okrTask.isOKRActionDisable();
            //}
            okrTask.initKpiInfo();
        });
        List<OkrTarget> targetList = okrTasks.stream().map(OkrTask::getTargetList).flatMap(Collection::stream).collect(Collectors.toList());
        //按考核时间倒序
        targetList.sort(Comparator.comparing(OkrTarget::getEvaluateStartDate).reversed());
        return targetList;
    }

    @Override
    public List<OkrTask> listOkrTask(Map<String, String> params) {

        int openOkrScore = Integer.parseInt(params.get("openOkrScore"));
        params.remove("openOkrScore");
        String listOkr = sendOKRRequest(QUERY_TASK_LIST_URL, params);
        JSONObject jsonObject = JSONObject.parseObject(listOkr);
        JSONArray arr = jsonObject.getJSONArray("data");
        if (arr == null) {
            return null;
        }
        List<OkrTask> okrTasks = JSONArray.parseArray(arr.toJSONString(), OkrTask.class);
        if (CollUtil.isEmpty(okrTasks)) {
            return null;
        }

        List<OkrTask> existOkrTargetsTasks = okrTasks.stream().filter(OkrTask::existOkrTargets).collect(Collectors.toList());

        existOkrTargetsTasks.forEach(okrTask -> {
            okrTask.initKpiInfo();
            okrTask.initKpiEvalTime();
            List<OkrTarget> targets = okrTask.getTargetList();
            for (int i = 0; i < targets.size(); i++) {
                OkrTarget okrTarget = targets.get(i);
                okrTarget.initOpenOkrScore(openOkrScore);
                okrTarget.setIndex(i);
                List<OkrAction> actions = okrTarget.getActionList();
                for (int j = 0; j < actions.size(); j++) {
                    OkrAction okrAction = actions.get(j);
                    okrAction.setIndex(j);
                }
            }
        });
        return existOkrTargetsTasks;
    }

    @Override
    public void openKpi(String okrStatus, CorpId corpId) {
        String okrCompanyStatus = getCompanyStatus(corpId);
        if (!COMPANY_STATUS_NORMAL.equals(okrCompanyStatus)) {
            return;
        }
        //2. 开通OKR开关
        Map<String, String> params = new HashMap<>();
        params.put("corpId", corpId.getCorpId());
        params.put("type", okrStatus);
        sendOKRRequest(OPEN_KPI_URL, params);
    }

    public String getCompanyStatus(CorpId corpId) {
        Map<String, String> params = new HashMap<>();
        params.put("corpId", corpId.getCorpId());
        String res = sendOKRRequest(GET_COMPANY_STATUS_URL, params);
        JSONObject jsonObject = JSONObject.parseObject(res);
        return jsonObject.getString("data");
    }

    @Override
    public String getOKRStatus(TenantId companyId) {
        Company company = deptEmpDao.getCompany(companyId);
        return getCompanyStatus(new CorpId(company.getDingCorpId()));
    }
}
