package com.polaris.kpi.eval.infr.task.dao;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.com.polaris.kpi.company.MsgSceneEnum;
import cn.com.polaris.kpi.company.TenantSysConf;
import cn.com.polaris.kpi.eval.IndexCalibration;
import cn.com.polaris.kpi.eval.KpiItemUsedField;
import cn.com.polaris.kpi.eval.KpiTypeUsedField;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.perf.www.common.em.EvaluateTaskStatusEnum;
import com.perf.www.common.utils.string.StringTool;
import com.perf.www.dto.workbench.WorkRecordQueryDTO;
import com.polaris.kpi.eval.domain.task.entity.EvalKpi;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.domain.task.entity.MutualScoreResultCompute;
import com.polaris.kpi.eval.domain.task.entity.calibrated.LevelRate;
import com.polaris.kpi.eval.domain.task.entity.calibrated.ResultAuditBatch;
import com.polaris.kpi.eval.domain.task.entity.calibrated.ResultAuditCache;
import com.polaris.kpi.eval.domain.task.entity.empeval.AuditResultEvalType;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.entity.flow.RejectAuditResultFlow;
import com.polaris.kpi.eval.domain.task.entity.grade.ScoreRule;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.eval.infr.task.builder.ResultAuditBatchBuilder;
import com.polaris.kpi.eval.infr.task.ppojo.*;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.EmpEvalTaskScore2Po;
import com.polaris.kpi.eval.infr.task.ppojo.backlog.AuditResultPo;
import com.polaris.kpi.eval.infr.task.ppojo.backlog.*;
import com.polaris.kpi.eval.infr.task.ppojo.calibrated.*;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.CalibratedRangePo;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.EmpEvalKpiTypeDo;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.EmpEvalRuleDo;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.EmpEvalTypeUsedFieldDo;
import com.polaris.kpi.eval.infr.task.ppojo.grade.ScoreRangePo;
import com.polaris.kpi.eval.infr.task.ppojo.grade.ScoreRuleDo;
import com.polaris.kpi.eval.infr.task.ppojo.grade.ScoreRulePo;
import com.polaris.kpi.eval.infr.task.query.WorkWaitMsgQuery;
import com.polaris.kpi.eval.infr.task.query.calibrated.CalibrateTaskUserQry;
import com.polaris.kpi.eval.infr.task.query.calibrated.CalibratedOrgQry;
import com.polaris.kpi.org.domain.dept.entity.CompanyMsgCenter;
import com.polaris.kpi.org.infr.company.ppojo.CompanyMsgCenterDo;
import com.polaris.kpi.org.infr.company.ppojo.TenantSysConfDo;
import com.polaris.kpi.org.infr.emp.pojo.EmpOrganizationDo;
import com.polaris.kpi.org.infr.emp.pojo.EmployeeBaseInfoDo;
import com.polaris.sdk.type.AuditEnum;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.analysis.function.Subtract;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.NativeSQLBuilder;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.common.data.ToDataBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.lufei.ibatis.mapper.PagedList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 绩效校准
 */
@Component
@Slf4j
public class CalibratedDao {

    @Autowired
    private DomainDaoImpl domainDao;
    private static String result_audit_batch_seq = "result_audit_batch";
    private static String result_audit_cache_seq = "result_audit_cache";
    private static String result_audit_batch_owner_emp_seq = "result_audit_batch_owner_emp";

    /**
     * 校准审核列表
     */
    public PagedList<CalibratedTaskToDoPo> pageCalibratedAudit(WorkWaitMsgQuery query) {
        ComQB resultBuilder = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .clearSelect().select("id,emp_id,company_id,task_id,task_user_id,org_id,score,final_score,scorer_type,audit_status,scorer_id,transfer_id,updated_time")
                .whereEqReq("company_id", query.getCompanyId())
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEq("scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene())
                .whereEq("scorer_id", query.getCreatedUser())
                .whereIn("task_id", query.getTaskIds())
                .appendWhere("(audit_status = 'pass' or audit_status is null)")
                .orderBy("approval_order DESC")
                .limit(0, 999999);
        ComQB resultQb = ComQB.build().fromQ(resultBuilder, "a").groupBy("a.task_user_id");
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .leftJoin(CompanyMsgCenterDo.class, "m").appendOn("u.company_id = m.company_id and u.id = m.link_id")
                .leftJoinQ(resultQb, "r").appendOn("u.id = r.task_user_id  ")
                .leftJoin(PerfEvaluateTaskBaseDo.class, "b").appendOn("u.task_id = b.id AND u.company_id = b.company_id")
                .leftJoin(ResultAuditBatchItemDo.class, "rabi").appendOn("u.id = rabi.task_user_id AND u.company_id = rabi.company_id and rabi.status = 1 ")
                .leftJoin(ResultAuditBatchOwnerEmpDo.class, "rabo").appendOn("rabi.rank_audit_batch_id = rabo.result_audit_batch_id AND u.company_id = rabo.company_id")
                .leftJoin(CycleDo.class, "pec").appendOn(" pec.company_id = b.company_id AND pec.id = b.cycle_id ")
                .clearSelect()
                .select(" u.task_id,\n" +
                        "\tu.is_new_emp,\n" +
                        "\tGROUP_CONCAT(u.id) as taskUserId,\n"+
                        "\tb.performance_type,\n" +
                        "\tpec.id AS cycleId,\n" +
                        "\tb.task_name,\n" +
                        "\tpec.`name` AS cycle_name,\n" +
                        "\tIF ( b.audit_result IS NULL, \n" +
                        "\t( CASE WHEN json_valid ( b.templ_evaluate_json ) = 1  THEN \n" +
                        "\tIF ( JSON_UNQUOTE ( JSON_EXTRACT ( b.templ_evaluate_json -> '$[0]', '$.isAddAuditComment' ) ) = 'false', 0, 1 ) ELSE NULL  END ),\n" +
                        "\t( CASE WHEN json_valid ( b.audit_result ) = 1 THEN \n" +
                        "\tJSON_UNQUOTE ( JSON_EXTRACT ( b.audit_result -> '$[0]', '$.commentReq' ) ) ELSE NULL END ) ) AS commentReq,\n" +
                        "\tcount(DISTINCT (case when r.scorer_type = 'final_result_audit' AND r.audit_status = 'pass' then CONCAT(u.emp_id,'-',if(b.performance_type = 2,u.eval_org_id,IFNULL(u.org_id,' '))) end) ) AS calibrated,\n" +
                        "\tcount(DISTINCT ( CASE WHEN u.task_status = 'resultsAuditing' AND r.scorer_type = 'final_result_audit' AND r.audit_status IS NULL THEN CONCAT(u.emp_id,'-',if(b.performance_type = 2,u.eval_org_id,IFNULL(u.org_id,' '))) END ) ) AS toBeCalibrated")
                .select("sum(if(rabi.task_user_id is null,0,1)) on_reject_cnt ")
                .setRsType(CalibratedTaskToDoPo.class)
                .whereEq("u.company_id", query.getCompanyId())
                .whereEq("b.is_deleted", Boolean.FALSE.toString())
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereNotIn("u.task_status", Arrays.asList(EvaluateTaskStatusEnum.DRAW_UP_ING.getStatus(), EvaluateTaskStatusEnum.TERMINATED.getStatus()))
                .whereEq("b.task_status", "published")
                //.whereEq("e.status", EmployeeStatus.ON_THE_JOB)
                .whereLike("b.task_name", query.getTaskNameKeyword())
                .whereEq("b.performance_type", query.getPerformanceType())
                .whereEq("r.scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene())
                .whereEq("r.scorer_id", query.getCreatedUser())
                .whereEq("m.business_scene", MsgSceneEnum.TASK_RESULT_AUDIT.getType())
                .whereEq("m.emp_id", query.getCreatedUser())
                .whereEq("m.handler_status", Boolean.FALSE.toString())
                //.appendWhere(" r.scorer_type = '" +EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene() +"')")
                //.appendWhere(" r.scorer_id = '"+query.getCreatedUser()+"')")
                .groupBy("u.task_id ")
                .orderBy("b.created_time desc")
                .having(" toBeCalibrated > 0 ");

        if (CollUtil.isNotEmpty(query.getTaskIds())) {
            comQB.whereIn("u.task_id", query.getTaskIds());
        }
        if (CollUtil.isNotEmpty(query.getCycleIdList())) {
            comQB.whereIn("pec.id", query.getCycleIdList());
        }
        comQB.setPage(query.getPageNo(), query.getPageSize());
        PagedList<CalibratedTaskToDoPo> pos = domainDao.listPage(comQB);
        if (CollUtil.isEmpty(pos)) {
            return pos;
        }
        return pos;
    }


    /**
     * 查询待校准审核列表
     */
    public List<CalibratedPagePo> listCalibratedAudit(WorkWaitMsgQuery query) {
        ComQB resultBuilder = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .clearSelect().select("id,emp_id,company_id,task_id,task_user_id,org_id,score,final_score,scorer_type,audit_status,scorer_id,transfer_id,updated_time,created_time")
                .whereEqReq("company_id", query.getCompanyId())
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEq("scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene())
                .whereEq("scorer_id", query.getCreatedUser())
                .appendWhere("(audit_status = 'pass' or audit_status is null)")
                .orderBy("approval_order DESC")
                .limit(0, 999999);
        ComQB resultQb = ComQB.build().fromQ(resultBuilder, "a")
                .groupBy("a.task_user_id");
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .leftJoinQ(resultQb, "r")
                .appendOn("u.id = r.task_user_id  ")
                .leftJoin(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("u.task_id = b.id AND u.company_id = b.company_id")
                .leftJoin(EmployeeBaseInfoDo.class, "e")
                .appendOn("u.emp_id = e.employee_id AND u.company_id = e.company_id")
                .leftJoin(CycleDo.class, "pec")
                .appendOn(" pec.company_id = b.company_id AND pec.id = b.cycle_id ")
                .leftJoin(EmpOrganizationDo.class, "eo")
                .appendOn("u.org_id=eo.org_id and u.company_id=eo.company_id")
                .leftJoin(DeadLineJobDo.class, "er")
                .appendOn("u.id =er.task_user_id  and u.task_id=er.task_id and u.task_status=er.business_type  AND u.company_id =er.company_id  AND er.is_deleted =  'false'")
                .clearSelect()
                .select(" u.task_id,\n" +
                        "\tu.task_status,\n" +
                        "\tr.scorer_type,\n" +
                        "\tr.created_time,\n" +
                        "\tb.performance_type,\n" +
                        "\tGROUP_CONCAT(r.task_user_id) as task_user_id,\n" +
                        "\tpec.id AS cycleId,\n" +
                        "\tb.task_name,\n" +
                        "\ter.end_date,\n" +
                        "\tpec.`name` AS cycle_name,\n" +
                        "\tIF ( b.audit_result IS NULL, \n" +
                        "\t( CASE WHEN json_valid ( b.templ_evaluate_json ) = 1  THEN \n" +
                        "\tIF ( JSON_UNQUOTE ( JSON_EXTRACT ( b.templ_evaluate_json -> '$[0]', '$.isAddAuditComment' ) ) = 'false', 0, 1 ) ELSE NULL  END ),\n" +
                        "\t( CASE WHEN json_valid ( b.audit_result ) = 1 THEN \n" +
                        "\tJSON_UNQUOTE ( JSON_EXTRACT ( b.audit_result -> '$[0]', '$.commentReq' ) ) ELSE NULL END ) ) AS commentReq,\n" +
                        "\tcount(DISTINCT (case when r.scorer_type = 'final_result_audit' AND r.audit_status = 'pass' then CONCAT(u.emp_id,'-',if(b.performance_type = 2,u.eval_org_id,IFNULL(u.org_id,' '))) end) ) AS calibrated,\n" +
                        "\tcount(DISTINCT ( CASE WHEN u.task_status = 'resultsAuditing' AND r.scorer_type = 'final_result_audit' AND r.audit_status IS NULL THEN CONCAT(u.emp_id,'-',if(b.performance_type = 2,u.eval_org_id,IFNULL(u.org_id,' '))) END ) ) AS toBeCalibrated")
                .setRsType(CalibratedPagePo.class)
                .whereEq("u.company_id", query.getCompanyId())
                .whereEq("b.is_deleted", Boolean.FALSE.toString())
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereNotEq("u.task_status", EvaluateTaskStatusEnum.DRAW_UP_ING.getStatus())
                .whereEq("b.task_status", "published")
                .whereLike("b.task_name", query.getTaskNameKeyword())
                .whereEq("b.performance_type", query.getPerformanceType())
                .whereEq("r.scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene())
                .groupBy("u.task_id ")
                .orderBy("b.created_time desc")
                .having(" toBeCalibrated > 0 ");

        List<CalibratedPagePo> pos = domainDao.listAll(comQB);
        if (CollUtil.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos;
    }

    /**
     * 查询待办数量
     */
    public Integer getBeDoneCount(WorkRecordQueryDTO query) {
        ComQB resultBuilder = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .clearSelect().select("id,emp_id,company_id,task_id,task_user_id,org_id,score,final_score,scorer_type,audit_status,scorer_id,transfer_id,updated_time")
                .whereEqReq("company_id", query.getCompanyId())
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEq("scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene())
                .whereEq("scorer_id", query.getEmpId())
                .appendWhere("(audit_status = 'pass' or audit_status is null)")
                .orderBy("approval_order DESC")
                .limit(0, 999999);
        ComQB resultQb = ComQB.build().fromQ(resultBuilder, "a")
                .groupBy("a.task_user_id");
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .leftJoin(CompanyMsgCenterDo.class, "m").appendOn("u.company_id = m.company_id and u.id = m.link_id")
                .leftJoinQ(resultQb, "r")
                .appendOn("u.id = r.task_user_id  ")
                .leftJoin(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("u.task_id = b.id AND u.company_id = b.company_id")
                .leftJoin(EmployeeBaseInfoDo.class, "e")
                .appendOn("u.emp_id = e.employee_id AND u.company_id = e.company_id")
                .clearSelect()
                .select(" u.task_id,\n" +
                        "\tcount(DISTINCT ( CASE WHEN u.task_status = 'resultsAuditing' AND r.scorer_type = 'final_result_audit' AND r.audit_status IS NULL THEN CONCAT( u.emp_id, '-', if(b.performance_type = 2,u.eval_org_id,IFNULL(u.org_id,' ')) ) END ) ) AS toBeCalibrated")
                .setRsType(CalibratedPagePo.class)
                .whereEq("u.company_id", query.getCompanyId())
                .whereEq("b.is_deleted", Boolean.FALSE.toString())
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereNotEq("u.task_status", EvaluateTaskStatusEnum.DRAW_UP_ING.getStatus())
                .whereEq("b.task_status", "published")
                //.whereEq("e.status", EmployeeStatus.ON_THE_JOB)
                .whereEq("r.scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene())
                .whereEq("r.scorer_id", query.getEmpId())
                .whereEq("m.business_scene", MsgSceneEnum.TASK_RESULT_AUDIT.getType())
                .whereEq("m.emp_id", query.getEmpId())
                .whereEq("m.handler_status", Boolean.FALSE.toString())
                //.appendWhere(" if((r.id is not null and r.transfer_id is null) or (r.id is null),ta.scene = '" +EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene() +"',r.scorer_type = '" +EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene() +"')")
                //.appendWhere(" if((r.id is not null and r.transfer_id is null) or (r.id is null), ta.approver_info = '"+query.getEmpId()+"',r.scorer_id = '"+query.getEmpId()+"')")
                .groupBy("u.task_id,u.emp_id,if(b.performance_type = 2,u.eval_org_id,u.org_id) ")
                .having("toBeCalibrated > 0");
        ComQB countQb = ComQB.build().fromQ(comQB, "s").clearSelect().select("count(s.task_id) as count").setRsType(Integer.class);
        return domainDao.findOne(countQb);
    }


    /**
     * 根据taskID查询人员校准列表
     */
    public PagedList<CalibratedPagePo> pageCalibratedBaseInfoAudit(WorkWaitMsgQuery query) {
        ComQB resultQb = resultQ(query);
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .leftJoin(CompanyMsgCenterDo.class, "m").appendOn("u.company_id = m.company_id and u.id = m.link_id")
                .leftJoin(ResultAuditCacheDo.class,"ac").appendOn("u.company_id = ac.company_id and u.id = ac.task_user_id and ac.is_deleted = 'false' and owner_emp_id = #{createdUser}",query.getCreatedUser())
                .leftJoinQ(resultQb, "r").appendOn("u.id = r.task_user_id and u.company_id = r.company_id")
                .leftJoin(PerfEvaluateTaskBaseDo.class, "b").appendOn("u.task_id = b.id AND u.company_id = b.company_id")
                .leftJoin(CycleDo.class, "pec").appendOn(" pec.company_id = u.company_id AND pec.id = u.cycle_id ")
                .leftJoin(EmpOrganizationDo.class, "eo").appendOn("u.org_id=eo.org_id and u.company_id=eo.company_id")
                .leftJoin(EmpEvalRuleDo.class, "eer").appendOn("eer.emp_eval_id =u.id and u.company_id=eer.company_id and eer.is_deleted='false'")
                .clearSelect().select("u.task_id, b.task_name, b.performance_type, pec.id as cycleId, pec.`name` AS cycle_name, u.emp_id as empId, u.`emp_name` AS taskEmpName")
                .select("u.avatar, u.`score_ranges`, u.final_score, u.step_id, u.perf_coefficient, u.evaluation_level, u.original_final_score, u.original_evaluation_level,u.original_perf_coefficient")
                .select("\tif(b.performance_type = 2,eval_org_id,eo.org_id) as org_id,\n " +
                        "\tif(b.performance_type = 2,u.eval_org_name,IFNULL(eo.org_name,(SELECT REPLACE(GROUP_CONCAT(eo.org_name),',','/') FROM emp_ref_org ro left join emp_organization eo on ro.org_id = eo.org_id and ro.ref_type = 'org' WHERE ro.emp_id = u.emp_id))) as org_name,\n" +
                        "\tr.score_comment,\n" +
                        "\tu.id as taskUserId,\n" +
                        "\tIF ( b.audit_result IS NULL, \n" +
                        "\t( CASE WHEN json_valid ( b.templ_evaluate_json ) = 1  THEN \n" +
                        "\tIF ( JSON_UNQUOTE ( JSON_EXTRACT ( b.templ_evaluate_json -> '$[0]', '$.isAddAuditComment' ) ) = 'false', 0, 1 ) ELSE NULL  END ),\n" +
                        "\t( CASE WHEN json_valid ( b.audit_result ) = 1 THEN \n" +
                        "\tJSON_UNQUOTE ( JSON_EXTRACT ( b.audit_result -> '$[0]', '$.commentReq' ) ) ELSE NULL END ) ) AS commentReq,\n" +
                        "\t( CASE WHEN json_valid ( b.comment_conf ) = 1 THEN JSON_UNQUOTE ( JSON_EXTRACT ( b.comment_conf -> '$[0]', '$.scoreSummarySwitch' ) ) ELSE NULL END ) as scoreSummarySwitch,\n" +
                        "\t(case when r.scorer_type = 'final_result_audit' AND r.audit_status = 'pass' then 1 \n" +
                        "\t  when u.task_status = 'resultsAuditing' AND r.scorer_type = 'final_result_audit' AND r.audit_status IS NULL then 2 \n" +
                        "\t else 0 end \n" +
                        "\t) as calibration_status,eer.create_total_level_type")
                .select("eer.show_result_type ,u.weight_of_ref,u.score_of_ref")
                .setRsType(CalibratedPagePo.class)
                .whereEq("u.company_id", query.getCompanyId())
                .whereEq("b.is_deleted", Boolean.FALSE.toString())
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereNotEq("u.task_status", EvaluateTaskStatusEnum.DRAW_UP_ING.getStatus())
                //.whereEq("e.status", EmployeeStatus.ON_THE_JOB)
                .whereEq("b.task_status", "published")
                .whereEq("b.performance_type", query.getPerformanceType())
                //.appendWhere(" if((r.id is not null and r.transfer_id is null) or (r.id is null),ta.scene = '" +EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene() +"',r.scorer_type = '" +EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene() +"')")
                //.appendWhere(" if((r.id is not null and r.transfer_id is null) or (r.id is null),ta.approver_info = '"+query.getCreatedUser()+"',r.scorer_id = '"+query.getCreatedUser()+"')")
                .groupBy("u.task_id,u.emp_id,if(b.performance_type = 2,u.eval_org_id,u.org_id)")
                .orderBy("calibration_status desc,IFNULL(ac.final_score,u.final_score) desc");

        if (CollUtil.isNotEmpty(query.getOrgIds())) {
            comQB.whereIn("u.org_id", query.getOrgIds());
        }
        if (CollUtil.isNotEmpty(query.getLevels())) {
            String sql = StringTool.getInStrSql(query.getLevels());
            comQB.appendWhere("(IFNULL(ac.evaluation_level,u.evaluation_level) in "+sql+")");
        }
        if (CollUtil.isNotEmpty(query.getTaskUserIds())) {
            comQB.whereNotIn("u.id", query.getTaskUserIds());
        }
        if (CollUtil.isNotEmpty(query.getNotEnterTaskUserIds())) {
            comQB.whereIn("u.id", query.getNotEnterTaskUserIds());
        } else {
            comQB.whereEq("r.scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene());
            comQB.whereEq("r.scorer_id", query.getCreatedUser());
            comQB.whereEq("m.business_scene",MsgSceneEnum.TASK_RESULT_AUDIT.getType());
            comQB.whereEq("m.emp_id",query.getEmpId());
            comQB.whereEq("m.handler_status",Boolean.FALSE.toString());
        }
        if (CollUtil.isNotEmpty(query.getTaskIds())) {
            comQB.whereIn("u.task_id", query.getTaskIds());
        }
        if (CollUtil.isEmpty(query.getEmpIds())) {
            comQB.whereLike("u.emp_name", query.getEmpName());
        }else {
            comQB.whereIn("u.emp_id", query.getEmpIds());
        }
        comQB.setPage(query.getPageNo(), query.getPageSize());
        PagedList<CalibratedPagePo> pos = domainDao.listPage(comQB);
        if (CollUtil.isEmpty(pos)) {
            return pos;
        }
        List<String> taskUserIds = pos.stream().map(cb -> cb.getTaskUserId()).collect(Collectors.toList());
        // List<String> scoreRuleIds = pos.stream().map(cb -> cb.getScoreRanges().get(0).getScoreRuleId()).collect(Collectors.toList());
        // Map<String, ScoreRule> ruleMap = listScoreRule(query.getCompanyId(), scoreRuleIds);
        ListWrap<AuditResultEvalType> groupWrap = listTypeLevelResultPo(query.getCompanyId(), taskUserIds);
        ListWrap<HistoryScorePo> historyScoreMap = listMapHistoryScore(new TenantId(query.getCompanyId()), taskUserIds);
        for (CalibratedPagePo po : pos) {
            po.setTypes(groupWrap.groupGet(po.getTaskUserId()));
            po.loadHistoryScore(historyScoreMap.mapGet(po.getTaskUserId()));
            // po.setLevelDefType(ruleMap.get(po.getScoreRanges().get(0).getScoreRuleId()).getLevelDefType());
        }
        return pos;
    }

    public Map<String, ScoreRule> listScoreRule(String companyId, List<String> scoreRuleIds) {
        ComQB scoreRule = ComQB.build(ScoreRuleDo.class)
                .whereEq("company_id", companyId)
                .whereIn("id", scoreRuleIds);
        List<ScoreRule> scoreRules = domainDao.listAllDomain(scoreRule, ScoreRule.class);
        return CollUtil.toMap(scoreRules, new HashMap<>(), r -> r.getId());
    }

    public ListWrap<AuditResultEvalType> listTypeLevelResultPo(String companyId, List<String> taskUserIds) {
        ComQB qb = ComQB.build(EmpEvalKpiTypeDo.class, "et")
                .whereEqReq("companyId", companyId)
                .whereInReq("taskUserId", taskUserIds)
                .appendWhere("type_level is not null")
                .appendWhere("is_deleted ='false'");
        List<AuditResultEvalType> typeLevelPos = domainDao.listAllDomain(qb, AuditResultEvalType.class);
        ListWrap<AuditResultEvalType> groupWrap = new ListWrap<>(typeLevelPos).groupBy(typeLevelPo -> typeLevelPo.getTaskUserId());
        return groupWrap;
    }

    //单个校准数据
    public AuditResultPo getUserForAuditResult(String companyId, String taskUserId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("id", taskUserId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        AuditResultPo domain = domainDao.findDomain(comQB, AuditResultPo.class);
        ListWrap<AuditResultEvalType> wrap = listTypeLevelResultPo(companyId, Arrays.asList(taskUserId));
        domain.setTypes(wrap.groupGet(taskUserId));
        return domain;
    }


    public List<AuditResultPo> listEvalUserForAuditResult(String companyId, String taskId, List<String> taskUserIds) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .leftJoin(EmpEvalRuleDo.class, "er")
                .appendOn("er.emp_eval_id =u.id and u.company_id=er.company_id and er.is_deleted='false'")
                .select("u.* , er.create_total_level_type,er.show_result_type")
                .whereEqReq("u.company_id", companyId)
                .whereIn("u.id", taskUserIds)
                .whereEq("u.task_id", taskId)
                .whereEq("er.is_deleted", Boolean.FALSE.toString())
                .whereEq("u.is_deleted", Boolean.FALSE.toString());
        List<AuditResultPo> auditResultPos = domainDao.listAllDomain(comQB, AuditResultPo.class);
        if (CollUtil.isEmpty(auditResultPos)) {
            return auditResultPos;
        }
        ListWrap<AuditResultEvalType> wrap = listTypeLevelResultPo(companyId, taskUserIds);
//        List<String> scoreRuleIds = auditResultPos.stream().map(cb -> cb.getScoreRanges().get(0).getScoreRuleId()).collect(Collectors.toList());
//        Map<String, ScoreRule> ruleMap = listScoreRule(companyId, scoreRuleIds);
        for (AuditResultPo resultPo : auditResultPos) {
            resultPo.setTypes(wrap.groupGet(resultPo.getId()));
//            resultPo.setLevelDefType(ruleMap.get(resultPo.getScoreRanges().get(0).getScoreRuleId()).getLevelDefType());
        }
        return auditResultPos;
    }


    /**
     * 查询等级以及数量
     */
    public List<LevelNumberPo> getLevelNumberInfo(WorkWaitMsgQuery query) {
        ComQB resultBuilder = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .clearSelect().select("id,emp_id,company_id,task_id,task_user_id,org_id,score,final_score,scorer_type,audit_status,scorer_id, transfer_id,updated_time")
                .whereEqReq("company_id", query.getCompanyId())
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEq("scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene())
                .whereEq("scorer_id", query.getCreatedUser())
                .whereIn("task_id", query.getTaskIds())
                .appendWhere("(audit_status = 'pass' or audit_status is null)")
                .orderBy("approval_order DESC")
                .limit(0, 999999);
        ComQB resultQb = ComQB.build().fromQ(resultBuilder, "a")
                .groupBy("a.task_user_id");
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .leftJoin(CompanyMsgCenterDo.class, "m").appendOn("u.company_id = m.company_id and u.id = m.link_id")
                .leftJoin(ResultAuditCacheDo.class,"ac").appendOn("u.company_id = ac.company_id and u.id = ac.task_user_id and ac.is_deleted = 'false' and owner_emp_id = #{createdUser}",query.getCreatedUser())
                .leftJoinQ(resultQb, "r")
                .appendOn("u.id = r.task_user_id")
                .leftJoin(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("u.task_id = b.id AND u.company_id = b.company_id")
                .leftJoin(EmployeeBaseInfoDo.class, "e")
                .appendOn("u.emp_id = e.employee_id AND u.company_id = e.company_id")
                .leftJoin(EmpOrganizationDo.class, "eo")
                .appendOn("u.org_id=eo.org_id and u.company_id=eo.company_id")
                .clearSelect()
                .select(" (case when ( u.task_status != 'resultsAuditing' AND r.scorer_type IS NULL AND r.audit_status IS NULL ) OR ( u.task_status = 'resultsAuditing' AND r.id IS NULL ) or(u.evaluation_level IS NULL OR u.evaluation_level = '') then '未进入' else ifnull(ac.evaluation_level,u.evaluation_level) end) as levelName,\n" +
                        "\tu.id,u.step_id,\n" +
                        "\t(case when r.scorer_type = 'final_result_audit' AND r.audit_status = 'pass' then 1 \n" +
                        "\t  when u.task_status = 'resultsAuditing' AND r.scorer_type = 'final_result_audit' AND r.audit_status IS NULL then 2 \n" +
                        "\t  when (u.task_status != 'resultsAuditing' AND r.scorer_type IS NULL AND r.audit_status IS NULL) or (u.task_status = 'resultsAuditing' AND r.id is null) then 3 else 0 end \n" +
                        "\t) as calibration_status,GROUP_CONCAT(r.task_user_id) as task_user_id")
                .setRsType(CalibratedPagePo.class)
                .whereEqReq("u.company_id", query.getCompanyId())
                .whereEq("b.is_deleted", Boolean.FALSE.toString())
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereNotEq("u.task_status", EvaluateTaskStatusEnum.DRAW_UP_ING.getStatus())
                //.whereEq("e.status", EmployeeStatus.ON_THE_JOB)
                .whereEq("b.task_status", "published")
                .whereEq("b.performance_type", query.getPerformanceType())
                .whereEq("r.scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene())
                .whereEq("r.scorer_id", query.getCreatedUser())
                .whereEq("m.business_scene", MsgSceneEnum.TASK_RESULT_AUDIT.getType())
                .whereEq("m.emp_id", query.getEmpId())
                .whereEq("m.handler_status", Boolean.FALSE.toString())
                //.appendWhere(" if((r.id is not null and r.transfer_id is null) or (r.id is null),ta.scene = '" +EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene() +"',r.scorer_type = '" +EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene() +"')")
                //.appendWhere(" if((r.id is not null and r.transfer_id is null) or (r.id is null), ta.approver_info = '"+query.getCreatedUser()+"',r.scorer_id = '"+query.getCreatedUser()+"')")
                .groupBy("u.task_id,u.emp_id,if(b.performance_type = 2,u.eval_org_id,u.org_id)");

        if (CollUtil.isNotEmpty(query.getOrgIds())) {
            comQB.whereIn("eo.org_id", query.getOrgIds());
        }
        if (CollUtil.isNotEmpty(query.getLevels())) {
            comQB.whereIn("u.evaluation_level", query.getLevels());
        }
        if (CollUtil.isNotEmpty(query.getTaskIds())) {
            comQB.whereIn("u.task_id", query.getTaskIds());
        }
        ComQB qb = ComQB.build(LevelNumberPo.class, "ln")
                .fromQ(comQB, "a")
                .clearSelect().select("a.`levelName`,a.step_id, count(a.id) as count,count(case when a.calibration_status = 1 then 1 end) as calibrated,count(case when a.calibration_status = 2 then 1 end) as toBeCalibrated,GROUP_CONCAT(a.task_user_id) as task_user_id")
                .groupBy("a.levelName")
                .orderBy("find_in_set(a.levelName ,'未进入')");
        List<LevelNumberPo> pos = domainDao.listAll(qb);
        return pos;
    }


    /**
     * 根据taskID查询校准人员所有等级规则
     */
    public List<CalibratedRangePo> listTaskUserScoreRanges(WorkWaitMsgQuery query) {
        ComQB resultBuilder = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .clearSelect().select("id,emp_id,company_id,task_id,task_user_id,org_id,score,final_score,scorer_type,audit_status,score_comment,scorer_id,transfer_id,updated_time")
                .whereEqReq("company_id", query.getCompanyId())
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEq("scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene())
                .whereEq("scorer_id", query.getCreatedUser())
                .whereIn("task_id", query.getTaskIds())
                .appendWhere("(audit_status = 'pass' or audit_status is null)")
                .orderBy("approval_order DESC")
                .limit(0, 999999);
        ComQB resultQb = ComQB.build().fromQ(resultBuilder, "a")
                .groupBy("a.task_user_id");
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .leftJoin(CompanyMsgCenterDo.class, "m").appendOn("u.company_id = m.company_id and u.id = m.link_id")
                .leftJoinQ(resultQb, "r")
                .appendOn("u.id = r.task_user_id")
                .leftJoin(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("u.task_id = b.id AND u.company_id = b.company_id")
                .leftJoin(EmployeeBaseInfoDo.class, "e")
                .appendOn("u.emp_id = e.employee_id AND u.company_id = e.company_id")
                .leftJoin(EmpOrganizationDo.class, "eo")
                .appendOn("u.org_id=eo.org_id and u.company_id=eo.company_id")
                .clearSelect()
                .select(" u.score_ranges")
                .setRsType(CalibratedRangePo.class)
                .whereEqReq("u.company_id", query.getCompanyId())
                .whereEq("b.is_deleted", Boolean.FALSE.toString())
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereNotEq("u.task_status", EvaluateTaskStatusEnum.DRAW_UP_ING.getStatus())
                //.whereEq("e.status", EmployeeStatus.ON_THE_JOB)
                .whereEq("b.task_status", "published")
                .whereEq("b.performance_type", query.getPerformanceType())
                .whereEq("r.scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene())
                .whereEq("r.scorer_id", query.getCreatedUser())
                .whereEq("m.business_scene", MsgSceneEnum.TASK_RESULT_AUDIT.getType())
                .whereEq("m.emp_id", query.getEmpId())
                .whereEq("m.handler_status", Boolean.FALSE.toString())
                //.appendWhere(" if((r.id is not null and r.transfer_id is null) or (r.id is null),ta.scene = '" +EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene() +"',r.scorer_type = '" +EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene() +"')")
                //.appendWhere(" if((r.id is not null and r.transfer_id is null) or (r.id is null),ta.approver_info = '"+query.getCreatedUser()+"',r.scorer_id = '"+query.getCreatedUser()+"')")
                .groupBy("u.task_id,u.emp_id,if(b.performance_type = 2,u.eval_org_id,u.org_id)");

        if (CollUtil.isNotEmpty(query.getOrgIds())) {
            comQB.whereIn("eo.org_id", query.getOrgIds());
        }
        if (CollUtil.isNotEmpty(query.getLevels())) {
            comQB.whereIn("u.evaluation_level", query.getLevels());
        }
        if (CollUtil.isNotEmpty(query.getTaskIds())) {
            comQB.whereIn("u.task_id", query.getTaskIds());
        }
        List<CalibratedRangePo> pos = domainDao.listAll(comQB);
        return pos;
    }

    /**
     * 获取等级组名称
     *
     * @return
     */
    public void getScoreRangesName(List<CalibrationScoreRangePo> rangePoList) {
        ComQB comQB = ComQB.build(ScoreRuleDo.class, "sr")
                .clearSelect().select("id,name").setRsType(ScoreRulePo.class)
                .whereIn("id", rangePoList.stream().map(obj -> obj.getScoreRuleId()).distinct().collect(Collectors.toList()))
                .whereEq("is_deleted", Boolean.FALSE.toString());
        List<ScoreRulePo> rulePoList = domainDao.listAll(comQB);
        if (rulePoList != null && rulePoList.size() > 0) {
            Map<String, ScoreRulePo> map = rulePoList.stream().collect(Collectors.toMap(ScoreRulePo::getId, Function.identity()));
            rangePoList.forEach(obj -> {
                if (map.get(obj.getScoreRuleId()) != null) {
                    ScoreRulePo rulePo = map.get(obj.getScoreRuleId());
                    obj.setScoreRuleName(rulePo.getName());
                    obj.setLevelRateConf(rulePo.getLevelRateConf());
                }
            });
        }
    }

    public ScoreRangePo getScoreRuleCount(WorkWaitMsgQuery query) {
        throw new IllegalStateException("not support");
    }

    /**
     * 根据taskID统计等级等级组数量
     */
    public ScoreRangePo getTaskUserScoreRangesCount(WorkWaitMsgQuery query) {
        ComQB resultBuilder = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .clearSelect().select("id,emp_id,company_id,task_id,task_user_id,org_id,score,final_score,scorer_type,audit_status,scorer_id, transfer_id,updated_time")
                .whereEqReq("company_id", query.getCompanyId())
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEq("scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene())
                .whereEq("scorer_id", query.getCreatedUser())
                .whereIn("task_id", query.getTaskIds())
                .appendWhere("(audit_status = 'pass' or audit_status is null)")
                .orderBy("approval_order DESC")
                .limit(0, 999999);
        ComQB resultQb = ComQB.build().fromQ(resultBuilder, "a")
                .groupBy("a.task_user_id");
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .leftJoin(CompanyMsgCenterDo.class, "m").appendOn("u.company_id = m.company_id and u.id = m.link_id")
                .leftJoinQ(resultQb, "r")
                .appendOn("u.id = r.task_user_id")
                .leftJoin(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("u.task_id = b.id AND u.company_id = b.company_id")
                .leftJoin(EmployeeBaseInfoDo.class, "e")
                .appendOn("u.emp_id = e.employee_id AND u.company_id = e.company_id")
                .leftJoin(EmpOrganizationDo.class, "eo")
                .appendOn("u.org_id=eo.org_id and u.company_id=eo.company_id")
                .clearSelect()
                .select("(case when json_valid(u.score_ranges) = 1 then JSON_UNQUOTE(JSON_EXTRACT(u.score_ranges ->'$[0]','$.scoreRuleId')) else null end) as scoreRuleId,\n" +
                        "\t GROUP_CONCAT(r.task_user_id) as taskUserId, \n" +
                        "\t(case when r.scorer_type = 'final_result_audit' AND r.audit_status = 'pass' then 1 \n" +
                        "\t  when u.task_status = 'resultsAuditing' AND r.scorer_type = 'final_result_audit' AND r.audit_status IS NULL then 2 \n" +
                        "\t  else 0 end) as calibration_status")
                .setRsType(CalibratedPagePo.class)
                .whereEqReq("u.company_id", query.getCompanyId())
                .whereEq("b.is_deleted", Boolean.FALSE.toString())
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereNotEq("u.task_status", EvaluateTaskStatusEnum.DRAW_UP_ING.getStatus())
                //.whereEq("e.status", EmployeeStatus.ON_THE_JOB)
                .whereEq("b.task_status", "published")
                .whereEq("b.performance_type", query.getPerformanceType())
                .whereEq("r.scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene())
                .whereEq("r.scorer_id", query.getCreatedUser())
                .whereEq("m.business_scene", MsgSceneEnum.TASK_RESULT_AUDIT.getType())
                .whereEq("m.emp_id", query.getEmpId())
                .whereEq("m.handler_status", Boolean.FALSE.toString())
                //.appendWhere(" if((r.id is not null and r.transfer_id is null) or (r.id is null),ta.scene = '" +EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene() +"',r.scorer_type = '" +EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene() +"')")
                //.appendWhere(" if((r.id is not null and r.transfer_id is null) or (r.id is null),ta.approver_info = '"+query.getCreatedUser()+"',r.scorer_id = '"+query.getCreatedUser()+"')")
                .groupBy("u.task_id,u.emp_id,if(b.performance_type = 2,u.eval_org_id,u.org_id)")
                .having("scoreRuleId = '" + query.getScoreRuleId() + "'");

        if (CollUtil.isNotEmpty(query.getOrgIds())) {
            comQB.whereIn("eo.org_id", query.getOrgIds());
        }
        if (CollUtil.isNotEmpty(query.getLevels())) {
            comQB.whereIn("u.evaluation_level", query.getLevels());
        }
        if (CollUtil.isNotEmpty(query.getTaskIds())) {
            comQB.whereIn("u.task_id", query.getTaskIds());
        }
        ComQB qb = ComQB.build(ScoreRangePo.class, "rp")
                .fromQ(comQB, "a")
                .clearSelect().select("count( case when a.calibration_status = 1 then 1 else null end ) AS calibrated,\n" +
                        "\tcount( case when a.calibration_status = 2 then 1 else null end ) AS toBeCalibrated,\n" +
                        "\tGROUP_CONCAT(a.taskUserId) as taskUserId");
        ScoreRangePo pos = domainDao.findOne(qb);
        return pos;
    }


    public ComQB resultQ(WorkWaitMsgQuery query) {
        ComQB resultBuilder = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .clearSelect().select("id,emp_id,company_id,task_id,task_user_id,org_id,score,final_score,scorer_type")
                .select("score_comment,audit_status,scorer_id, transfer_id,updated_time")
                .whereEqReq("company_id", query.getCompanyId())
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEq("scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene())
                .whereEq("scorer_id", query.getCreatedUser())
                .whereIn("task_id", query.getTaskIds())
                .appendWhere("(audit_status = 'pass' or audit_status is null)")
                .orderBy("approval_order DESC")
                .limit(0, 999999);
        ComQB resultQb = ComQB.build().fromQ(resultBuilder, "a")
                .groupBy("a.task_user_id");
        return resultQb;
    }


    /**
     * 根据taskID查询校准人员评分等级分布
     */
    public PagedList<CalibratedPagePo> pageCalibratedEmpDistribute(WorkWaitMsgQuery query) {
        ComQB resultQb = resultQ(query);
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .leftJoin(CompanyMsgCenterDo.class, "m").appendOn("u.company_id = m.company_id and u.id = m.link_id")
                .leftJoin(ResultAuditCacheDo.class,"ac").appendOn("u.company_id = ac.company_id and u.id = ac.task_user_id and ac.is_deleted = 'false' and owner_emp_id = #{createdUser}",query.getCreatedUser())
                .leftJoinQ(resultQb, "r")
                .appendOn("u.id = r.task_user_id ")
                .leftJoin(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("u.task_id = b.id AND u.company_id = b.company_id")
                .leftJoin(EmployeeBaseInfoDo.class, "e")
                .appendOn("u.emp_id = e.employee_id AND u.company_id = e.company_id")
//                .appendOn("u.id = so.task_user_id")
//                .leftJoin(EmpOrganizationDo.class, "eo")
//                .appendOn("u.org_id=eo.org_id and u.company_id=eo.company_id")
                .leftJoin(EmpEvalRuleDo.class, "er")
                .appendOn("er.emp_eval_id =u.id and u.company_id=er.company_id and er.is_deleted='false'")
                .clearSelect().select(" er.show_result_type")
                .select("u.task_id,u.id as taskUserId,GROUP_CONCAT(u.id) as taskUserIds,ifnull(ac.final_score,u.final_score) as final_score,ifnull(ac.evaluation_level,u.evaluation_level) as evaluation_level")
                .select("ifnull(ac.perf_coefficient,u.perf_coefficient) as perf_coefficient,ac.item_scores as indexCalibration,ac.operate_reason as operateReason")
                .select("u.original_final_score,u.original_evaluation_level,u.original_perf_coefficient")
                .select("b.task_name,b.performance_type")
                .select("if(b.performance_type = 2, u.eval_org_name,emp_org_name) as org_name")
                .select("e.employee_id as empId,e.`name` AS taskEmpName,e.`avatar`")
                .select("(case when json_valid(u.score_ranges) = 1 then JSON_UNQUOTE(JSON_EXTRACT(u.score_ranges ->'$[0]','$.scoreRuleId')) else null end) as scoreRuleId")
                .select("(case when r.scorer_type = 'final_result_audit' AND r.audit_status = 'pass' then 1 when u.task_status = 'resultsAuditing' AND r.scorer_type = 'final_result_audit' AND r.audit_status IS NULL then 2 else 0 end ) as calibration_status, er.create_total_level_type")
                .setRsType(CalibratedPagePo.class)
                .whereEq("u.company_id", query.getCompanyId())
                .whereIn("u.task_id", query.getTaskIds())
                .whereEq("b.is_deleted", Boolean.FALSE.toString())
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereNotEq("u.task_status", EvaluateTaskStatusEnum.DRAW_UP_ING.getStatus())
                //.whereEq("e.status", EmployeeStatus.ON_THE_JOB)
                .whereEq("b.task_status", "published")
                .whereEq("b.performance_type", query.getPerformanceType())
                .whereEq("r.scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene())
                .whereEq("r.scorer_id", query.getCreatedUser())
                .whereEq("m.business_scene", MsgSceneEnum.TASK_RESULT_AUDIT.getType())
                .whereEq("m.emp_id", query.getEmpId())
                .whereEq("m.handler_status", Boolean.FALSE.toString())
                //.appendWhere(" if((r.id is not null and r.transfer_id is null) or (r.id is null),ta.scene = '" +EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene() +"',r.scorer_type = '" +EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene() +"')")
                //.appendWhere(" if((r.id is not null and r.transfer_id is null) or (r.id is null),ta.approver_info = '"+query.getCreatedUser()+"',r.scorer_id = '"+query.getCreatedUser()+"')")
                .groupBy("u.id,if(b.performance_type = 2,u.eval_org_id,u.org_id)")
                .having("scoreRuleId = '" + query.getScoreRuleId() + "'" + "and evaluation_level = '" + query.getLevel() + "'")
                .orderBy("calibration_status desc,r.updated_time desc");

        if (CollUtil.isNotEmpty(query.getOrgIds())) {
            comQB.whereIn("u.org_id", query.getOrgIds());
        }
        comQB.setPage(query.getPageNo(), query.getPageSize());
        PagedList<CalibratedPagePo> pos = domainDao.listPage(comQB);
        if (CollUtil.isEmpty(pos)) {
            return pos;
        }
        ListWrap<HistoryScorePo> historyScoreMap = listMapHistoryScore(new TenantId(query.getCompanyId()), CollUtil.map(pos.getData(),po -> po.getTaskUserId(),true));
        pos.getData().forEach(po ->{
            po.loadHistoryScore(historyScoreMap.mapGet(po.getTaskUserId()));
        });
        return pos;
    }


    /**
     * 根据taskUserID查询未进入校准人员评分等级分布
     */
    public PagedList<CalibratedPagePo> listUnCalibratedEmpInfo(WorkWaitMsgQuery query) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .leftJoin(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("u.company_id = b.company_id and u.task_id = b.id")
                .leftJoin(EmployeeBaseInfoDo.class, "e")
                .appendOn("u.emp_id = e.employee_id AND u.company_id = e.company_id")
//                .appendOn("u.id = so.task_user_id")
//                .leftJoin(EmpOrganizationDo.class, "eo")
//                .appendOn("u.org_id=eo.org_id and u.company_id=eo.company_id")
                .clearSelect()
                .select("u.task_id,u.id as taskUserId,'未进入校准' as evaluation_level,if(b.performance_type = 2, u.eval_org_name,emp_org_name) as org_name")
                .select("u.original_final_score,u.original_evaluation_level,u.original_perf_coefficient")
                .select("e.employee_id as empId,e.`name` AS taskEmpName, 3 as calibration_status,e.`avatar`")
                .setRsType(CalibratedPagePo.class)
                .whereIn("u.id", Arrays.asList(query.getTaskUserId().split(",")))
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                //.whereEq("e.status", EmployeeStatus.ON_THE_JOB)
                .whereEq("b.is_deleted", Boolean.FALSE)
                .groupBy("u.id")
                .having("scoreRuleId = '" + query.getScoreRuleId() + "'");
        comQB.setPage(query.getPageNo(), query.getPageSize());
        PagedList<CalibratedPagePo> pos = domainDao.listPage(comQB);
        if (CollUtil.isEmpty(pos)) {
            return pos;
        }
        ListWrap<HistoryScorePo> historyScoreMap = listMapHistoryScore(new TenantId(query.getCompanyId()), CollUtil.map(pos.getData(),po -> po.getTaskUserId(),true));
        pos.getData().forEach(po ->{
            po.loadHistoryScore(historyScoreMap.mapGet(po.getTaskUserId()));
        });
        return pos;
    }


    /**
     * 根据taskUserId查询用户基础信息
     */
    public ScoreDetailsPo getTaskEmpInfo(String companyId, String taskUserId) {
        NativeSQLBuilder scorerBuilder = NativeSQLBuilder.build(PerfEvaluateTaskScoreResultDo.class)
                .setSql("SELECT company_id, task_id, emp_id,approval_order,task_user_id,score, score_level,perf_coefficient " +
                        "FROM perf_evaluate_task_score_result  WHERE task_user_id = #{taskUserId}  AND is_deleted = 'false'  AND scorer_type = 'final_result_audit' and audit_status = 'pass' ORDER BY approval_order desc");
        scorerBuilder.setValue("taskUserId", taskUserId);
        /**查询用户基础信息*/
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .leftJoin(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("u.task_id = b.id AND u.company_id = b.company_id")
//                .appendOn("u.company_id = so.company_id and u.task_id = so.task_id and u.emp_id = so.emp_id")
                .leftJoin(EmpEvalRuleDo.class, "er")
                .appendOn("u.company_id = er.company_id and u.id = er.emp_eval_id and er.is_deleted = 'false'")
                .leftJoin(EmployeeBaseInfoDo.class, "e")
                .appendOn("u.emp_id = e.employee_id AND u.company_id = e.company_id")
                //.leftJoin(EmpOrganizationDo.class, "eo")
                //.appendOn("u.org_id=eo.org_id and u.company_id=eo.company_id")
                .clearSelect()
                .select("er.show_result_type, u.task_id,u.company_id,u.cycle_id,u.task_id,u.id as taskUserId,u.score_ranges,u.step_id,u.emp_org_name ")
                .select("u.avatar, e.employee_id as empId,e.`name` AS taskEmpName,er.evaluate_type")
                .select("u.final_score,u.mutual_eval_compute_type,u.evaluation_level,u.original_final_score,u.perf_coefficient")
                .select("u.original_final_score,u.original_evaluation_level,u.original_perf_coefficient")
                //不带权重的环节得分
                .select("u.v3_self_score as selfScore,u.v3_peer_score as peerScore")
                .select("u.v3_sub_score as subScore,u.v3_superior_score as superiorScore")
                .select("u.v3_final_item_score as finalItemScore,u.v3_appoint_score as appointScore")

                .select("( CASE WHEN json_valid ( b.comment_conf ) = 1 THEN JSON_UNQUOTE ( JSON_EXTRACT ( b.comment_conf -> '$[0]', '$.scoreSummarySwitch' ) ) ELSE NULL END ) as scoreSummarySwitch")
                //.select("(case when r.scorer_type = 'final_result_audit' AND r.audit_status = 'pass' then 1 when u.task_status = 'resultsAuditing' AND r.scorer_type = 'final_result_audit' AND r.audit_status IS NULL then 2 else 0 end ) as calibration_status")
                //采用 scorerNodes直接返回，无需从rule取
//                .select(
//                        "CONCAT(IFNULL(( CASE WHEN json_valid ( er.s3_self_rater ) = 1 then (case when JSON_UNQUOTE ( JSON_EXTRACT ( er.s3_self_rater -> '$[0]', '$.rateMode' ) ) = 'total' then 'self_score'  else '' end) END),','),\n" +
//                        "\tIFNULL(( CASE WHEN json_valid ( er.s3_super_rater ) = 1 then (case when JSON_UNQUOTE ( JSON_EXTRACT ( er.s3_super_rater -> '$[0]', '$.rateMode' ) ) = 'total' then 'superior_score' else '' end) END),','),\n" +
//                        "\tIFNULL(( CASE WHEN json_valid ( er.s3_sub_rater ) = 1 then (case when JSON_UNQUOTE ( JSON_EXTRACT ( er.s3_sub_rater -> '$[0]', '$.rateMode' ) ) = 'total' then 'sub_score' else '' end) END),','),\n" +
//                        "\t( CASE WHEN json_valid ( er.s3_peer_rater ) = 1 then (case when JSON_UNQUOTE ( JSON_EXTRACT ( er.s3_peer_rater -> '$[0]', '$.rateMode' ) ) = 'total' then 'peer_score' else '' end) END)) as scoreTotal")
                .select("score_value_conf->'$.baseScore' base_score")
                .setRsType(ScoreDetailsPo.class)
                .whereEq("u.id", taskUserId)
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                //.whereEq("e.status", EmployeeStatus.ON_THE_JOB)
                .groupBy("u.emp_id");
        ScoreDetailsPo detailsPo = domainDao.findOne(comQB);
        if (detailsPo == null) {
            return null;
        }
//        Map<String, ScoreRule> ruleMap = listScoreRule(companyId, Arrays.asList(detailsPo.getScoreRanges().get(0).getScoreRuleId()));
//        detailsPo.setLevelDefType(ruleMap.get(detailsPo.getScoreRanges().get(0).getScoreRuleId()).getLevelDefType());
        detailsPo.accOpenAvgWeightCompute();
        ListWrap<HistoryScorePo> historyScoreMap = listMapHistoryScore(new TenantId(companyId), Arrays.asList(taskUserId));
        detailsPo.loadHistoryScore(historyScoreMap.mapGet(detailsPo.getTaskUserId()));
        return detailsPo;
    }


    /**
     * 根据taskUserId获取指标类别信息
     *
     * @param taskUserId
     * @return
     */
    public List<CalibratedEmpEvalKpiTypePo> listTaskKpiTypeInfo(String companyId, String taskUserId) {
        ComQB comQB = ComQB.build(EmpEvalKpiTypeDo.class, "t")
                .clearSelect()
                .select(" t.task_user_id,\n" +
                        "\tt.kpi_type_id,\n" +
                        "\tt.kpi_type_name,\n" +
                        "\tt.kpi_type_weight,\n" +
                        "\tt.is_okr,\n" +
                        "\tt.`kpi_type_classify`,\n" +
                        "\tt.max_extra_score\n")
                .select("t.type_level,t.ind_level_group")
                .select("t.score_opt_type")
                .select("t.ask360_temp_id,t.ask360_temp_name,t.ask360_temp_desc,t.ask360_eval_id,t.ask360_eval_score")
                .setRsType(CalibratedEmpEvalKpiTypePo.class)
                .whereEq("t.is_deleted", Boolean.FALSE.toString())
                .whereEq("t.company_id", companyId)
                .whereEq("t.task_user_id", taskUserId)
                .groupBy("t.kpi_type_id")
                .orderBy("t.type_order");
        List<CalibratedEmpEvalKpiTypePo> taskKpiPo = domainDao.listAll(comQB);
        if (taskKpiPo == null) {
            return null;
        }
        taskKpiPo.forEach(type -> {
            type.setKpiTypeUsedFields(getTypeUsedFieldDoList(companyId, taskUserId, type.getKpiTypeId()));
        });
        return taskKpiPo;
    }


    /**
     * 根据taskUserId获取指标名称信息
     *
     * @param taskUserId
     * @return
     */
    public List<CalibratedPerfEvaluateTaskResultPo> listTaskKpiItemInfo(String taskUserId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskKpiDo.class, "tk")
                .leftJoin(PerfEvaluateTaskUserDo.class, "u")
                .appendOn("tk.task_user_id = u.id")
                .leftJoin(PerfEvaluateTaskScoreResultDo.class, "r")
                .appendOn("tk.task_user_id = r.task_user_id and tk.kpi_item_id = r.kpi_item_id and r.scorer_type in ('self_score','superior_score','appoint_score','sub_score','peer_score','item_score') AND r.is_deleted = 'false' ")
                .clearSelect()
                .select(" tk.task_user_id,\n" +
                        "\ttk.kpi_type_id,\n" +
                        "\ttk.kpi_type_name,\n" +
                        "\ttk.kpi_item_id,\n" +
                        "\ttk.id,\n" +
                        "\ttk.kpi_item_name,\n" +
                        "\ttk.item_target_value,\n" +
                        "\ttk.item_finish_value,\n" +
                        "\ttk.item_unit,\n" +
                        "\ttk.item_weight,\n" +
                        "\ttk.kpi_type_classify,\n" +
                        "\ttk.plus_limit,\n" +
                        "\ttk.subtract_limit,\n" +
                        "\ttk.max_extra_score,\n" +
                        "\ttk.item_rule,\n" +
                        "\ttk.scoring_rule,\n" +
                        "\ttk.scorer_type,\n" +
                        "\ttk.is_okr,\n" +
                        "\ttk.show_finish_bar,\n" +
                        "\ttk.finish_value_type,\n" +
                        "\ttk.show_target_value,\n" +
                        "\ttk.input_format AS input_format,\n" +
                        "\ttk.item_target_value_text AS item_target_value_text,\n" +
                        "\ttk.item_finish_value_text AS item_finish_value_text,\n" +
                        "\ttk.work_item_finish_value AS work_item_finish_value,\n" +
                        "\tsum( ( CASE WHEN (  tk.scorer_type = 'auto' or (tk.is_okr = 'true' and tk.open_okr_score = 1)) THEN tk.item_auto_score WHEN r.final_weight_score IS NULL AND tk.kpi_type_classify IS NOT NULL THEN ( CASE WHEN tk.kpi_type_classify = 'plus' THEN r.final_weight_plus_score WHEN tk.kpi_type_classify = 'subtract' THEN final_weight_subtract_score WHEN tk.kpi_type_classify = 'plusSub' THEN (ifnull( r.final_weight_subtract_score, 0 ) + ifnull( r.final_weight_plus_score, 0 )) ELSE NULL END ) ELSE r. final_weight_score END  ) ) AS finalWeightScore ")
                .select("tk.ind_level,tk.ind_level_group,tk.ind_level_group_id")
                .setRsType(CalibratedPerfEvaluateTaskResultPo.class)
                .whereEq("tk.task_user_id", taskUserId)
                .whereEq("tk.is_deleted", Boolean.FALSE.toString())
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .groupBy("tk.kpi_item_id")
                .orderBy("tk.id");
        List<CalibratedPerfEvaluateTaskResultPo> taskResultPos = domainDao.listAll(comQB);
        if (taskResultPos == null) {
            return null;
        }
        return taskResultPos;
    }

    /**
     * 根据taskUserId获取指标名称信息
     *
     * @param taskUserId
     * @return
     */
    public List<CalibratedPerfEvaluateTaskResultPo> listTaskOpenAvgScoreKpiItemInfo(String taskUserId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskKpiDo.class, "tk")
                .leftJoin(PerfEvaluateTaskUserDo.class, "u")
                .appendOn("tk.task_user_id = u.id")
                .leftJoin(PerfEvaluateTaskScoreResultDo.class, "r")
                .appendOn("tk.task_user_id = r.task_user_id and tk.kpi_item_id = r.kpi_item_id and r.scorer_type in ('self_score','superior_score','appoint_score','sub_score','peer_score','item_score') AND r.is_deleted = 'false' ")
                .clearSelect()
                .select(" tk.task_user_id,tk.kpi_type_id,tk.kpi_type_name,tk.kpi_item_id,tk.id,tk.kpi_item_name,tk.item_target_value,tk.item_finish_value,tk.item_unit,tk.item_weight")
                .select("tk.kpi_type_classify,tk.plus_limit,tk.subtract_limit,tk.max_extra_score,tk.item_rule,tk.scoring_rule,tk.scorer_type,tk.is_okr,tk.show_finish_bar")
                .select("tk.finish_value_type,tk.show_target_value,tk.input_format AS input_format,tk.item_target_value_text AS item_target_value_text,tk.item_finish_value_text AS item_finish_value_text")
                .select("tk.work_item_finish_value AS work_item_finish_value" )
                .select("tk.ind_level,tk.ind_level_group,tk.ind_level_group_id")
                //自动计算分
                .select(" SUM((if(tk.scorer_type = 'auto' OR (tk.is_okr = 'true' AND tk.open_okr_score = 1), tk.item_auto_score,null))) AS autoScore")
                //finalSubtractAndPlusWeightScore 加减分
               // .select(" SUM((CASE WHEN tk.kpi_type_classify = 'plus' THEN r.final_weight_plus_score WHEN tk.kpi_type_classify = 'subtract' THEN final_weight_subtract_score  WHEN tk.kpi_type_classify = 'plusSub' THEN (IFNULL(r.final_weight_subtract_score, 0) + IFNULL(r.final_weight_plus_score, 0)) ELSE NULL END)) AS finalSubtractAndPlusWeightScore")
                //finalWeightPlusScore 加分
                .select("SUM(if(r.scorer_type in ('self_score', 'superior_score', 'appoint_score', 'item_score') AND tk.kpi_type_classify = 'plus', r.final_weight_plus_score,null)) AS finalWeightPlusScore")
                //peerFinalWeightPlusScore
                .select("SUM(if(r.scorer_type = 'peer_score' AND tk.kpi_type_classify = 'plus', r.final_weight_plus_score,null)) AS peerFinalWeightPlusScore")
                //peerFinalWeightPlusScoreNum
                .select("SUM(if(r.scorer_type = 'peer_score' AND tk.kpi_type_classify = 'plus'and r.score_weight != '0.00', 1, 0)) AS peerFinalWeightPlusScoreNum")
                //subFinalWeightPlusScore
                .select("SUM(if(r.scorer_type = 'sub_score' AND tk.kpi_type_classify = 'plus', r.final_weight_plus_score,null)) AS subFinalWeightPlusScore")
                //subFinalWeightPlusScoreNum
                .select("SUM(if(r.scorer_type = 'sub_score' AND tk.kpi_type_classify = 'plus'and r.score_weight != '0.00', 1, 0)) AS subFinalWeightPlusScoreNum")

                //finalWeightSubtractScore
                .select("SUM(if(r.scorer_type in ('self_score', 'superior_score', 'appoint_score', 'item_score') AND tk.kpi_type_classify = 'subtract', r.final_weight_subtract_score,null)) AS finalWeightSubtractScore")
                //peerFinalWeightSubtractScore
                .select("SUM(if(r.scorer_type = 'peer_score' AND tk.kpi_type_classify = 'subtract', r.final_weight_subtract_score,null)) AS peerFinalWeightSubtractScore")
                //peerFinalWeightSubtractScoreNum
                .select("SUM(if(r.scorer_type = 'peer_score' AND tk.kpi_type_classify = 'subtract'and r.score_weight != '0.00', 1, 0)) AS peerFinalWeightSubtractScoreNum")
                //subFinalWeightSubtractScore
                .select("SUM(if(r.scorer_type = 'sub_score' AND tk.kpi_type_classify = 'subtract', r.final_weight_subtract_score,null)) AS subFinalWeightSubtractScore")
                //subFinalWeightSubtractScoreNum
                .select("SUM(if(r.scorer_type = 'sub_score' AND tk.kpi_type_classify = 'subtract'and r.score_weight != '0.00', 1, 0)) AS subFinalWeightSubtractScoreNum")

                //finalWeightPlusSubtractScore
                .select("SUM(if(r.scorer_type in ('self_score', 'superior_score', 'appoint_score', 'item_score') AND tk.kpi_type_classify = 'plusSub', (IFNULL(r.final_weight_subtract_score, 0) + IFNULL(r.final_weight_plus_score, 0)),null)) AS finalWeightPlusSubtractScore")
                //peerFinalWeightPlusSubtractScore
                .select(" SUM(if(r.scorer_type = 'peer_score' AND tk.kpi_type_classify = 'plusSub', r.final_weight_plus_score,null)) AS peerFinalWeightPlusSubtractScore")
                //peerFinalWeightPlusSubtractScoreNum
                .select("SUM(if(r.scorer_type = 'peer_score' AND tk.kpi_type_classify = 'plusSub'and r.score_weight != '0.00', 1, 0)) AS peerFinalWeightPlusSubtractScoreNum")
                //subFinalWeightPlusSubtractScore
                .select(" SUM(if(r.scorer_type = 'sub_score' AND tk.kpi_type_classify = 'plusSub', r.final_weight_plus_score,null)) AS subFinalWeightPlusSubtractScore")
                //subFinalWeightPlusSubtractScoreNum
                .select("SUM(if(r.scorer_type = 'sub_score' AND tk.kpi_type_classify = 'plusSub'and r.score_weight != '0.00', 1, 0)) AS subFinalWeightPlusSubtractScoreNum")

                // nomualFinalWeightScore
                .select("SUM(if(r.scorer_type in ('self_score', 'superior_score', 'appoint_score', 'item_score'), r.final_weight_score,null)) AS nomualFinalWeightScore")
                //同级
                .select("SUM(if(r.scorer_type = 'peer_score', r.final_weight_score, null)) AS nomualPeerFinalWeightScore")
                .select("SUM(if(r.scorer_type = 'peer_score' and r.score_weight != '0.00', 1, 0)) AS nomualPeerNum")
                //下级
                .select("SUM(if(r.scorer_type = 'sub_score', r.final_weight_score, null))  AS nomualSubFinalWeightScore")
                .select("SUM(if(r.scorer_type = 'sub_score' and r.score_weight != '0.00', 1, 0))  AS nomualSubNum")
                        // "\tsum( ( CASE WHEN (  tk.scorer_type = 'auto' or (tk.is_okr = 'true' and tk.open_okr_score = 1)) THEN tk.item_auto_score WHEN r.final_weight_score IS NULL AND tk.kpi_type_classify IS NOT NULL THEN ( CASE WHEN tk.kpi_type_classify = 'plus' THEN r.final_weight_plus_score WHEN tk.kpi_type_classify = 'subtract' THEN final_weight_subtract_score WHEN tk.kpi_type_classify = 'plusSub' THEN (ifnull( r.final_weight_subtract_score, 0 ) + ifnull( r.final_weight_plus_score, 0 )) ELSE NULL END ) ELSE r. final_weight_score END  ) ) AS finalWeightScore ")
                .setRsType(CalibratedPerfEvaluateTaskResultPo.class)
                .whereEq("tk.task_user_id", taskUserId)
                .whereEq("tk.is_deleted", Boolean.FALSE.toString())
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .groupBy("tk.kpi_item_id")
                .orderBy("tk.id");
        List<CalibratedPerfEvaluateTaskResultPo> taskResultPos = domainDao.listAll(comQB);
        if (taskResultPos == null) {
            return null;
        }

        for (CalibratedPerfEvaluateTaskResultPo resultPo : taskResultPos) {
            log.info(" =======resultPo:{}", JSONUtil.toJsonStr(resultPo));
            BigDecimal nomualFinalWeightScore = isNotNullScore(resultPo.getNomualFinalWeightScore())?resultPo.getNomualFinalWeightScore():BigDecimal.ZERO;
            //同级
            if (isNotNullScore(resultPo.getNomualPeerFinalWeightScore())) {
                MutualScoreResultCompute compute = new MutualScoreResultCompute(resultPo.getNomualPeerFinalWeightScore(), resultPo.getNomualPeerNum());
                nomualFinalWeightScore = nomualFinalWeightScore.add(compute.computeAvgScore()); //计算互评平均分
            }
            //下级
            if (isNotNullScore(resultPo.getNomualSubfinalWeightScore())) {
                MutualScoreResultCompute compute2 = new MutualScoreResultCompute(resultPo.getNomualSubfinalWeightScore(), resultPo.getNomualSubNum());
                nomualFinalWeightScore = nomualFinalWeightScore.add(compute2.computeAvgScore()); //计算互评平均分
            }
            //自动计算得分
            if (isNotNullScore(resultPo.getAutoScore())) {
                nomualFinalWeightScore = nomualFinalWeightScore.add(resultPo.getAutoScore());
                log.info(" =======nomualFinalWeightScore:{},resultPo.getAutoScore:{}", nomualFinalWeightScore,resultPo.getAutoScore());
            }

            //加分
            BigDecimal finalWeightPlusScore = isNotNullScore(resultPo.getFinalWeightPlusScore()) ? resultPo.getFinalWeightPlusScore() : BigDecimal.ZERO;
            if (isNotNullScore(resultPo.getPeerFinalWeightPlusScore())) {
                MutualScoreResultCompute computePlus1 = new MutualScoreResultCompute(resultPo.getPeerFinalWeightPlusScore(), resultPo.getPeerFinalWeightPlusScoreNum());
                finalWeightPlusScore =  finalWeightPlusScore.add(computePlus1.computeAvgScore());
            }
            if (isNotNullScore(resultPo.getSubFinalWeightPlusScore())) {
                MutualScoreResultCompute computePlus2 = new MutualScoreResultCompute(resultPo.getSubFinalWeightPlusScore(), resultPo.getSubFinalWeightPlusScoreNum());
                finalWeightPlusScore = finalWeightPlusScore.add(computePlus2.computeAvgScore());
            }
            resultPo.setFinalWeightPlusScore(finalWeightPlusScore);
            nomualFinalWeightScore = nomualFinalWeightScore.add(finalWeightPlusScore);
            log.info(" =======nomualFinalWeightScore:{},finalWeightPlusScore:{}", nomualFinalWeightScore,finalWeightPlusScore);

            //减分
            BigDecimal finalWeightSubtractScore = isNotNullScore(resultPo.getFinalWeightSubtractScore()) ? resultPo.getFinalWeightSubtractScore() : BigDecimal.ZERO;
            if (isNotNullScore(resultPo.getPeerFinalWeightSubtractScore())) {
                MutualScoreResultCompute computeSubtract1 = new MutualScoreResultCompute(resultPo.getPeerFinalWeightSubtractScore(), resultPo.getPeerFinalWeightSubtractScoreNum());
                finalWeightSubtractScore =  finalWeightSubtractScore.add(computeSubtract1.computeAvgScore());
            }
            if (isNotNullScore(resultPo.getSubFinalWeightSubtractScore())) {
                MutualScoreResultCompute computeSubtract2 = new MutualScoreResultCompute(resultPo.getSubFinalWeightSubtractScore(), resultPo.getSubFinalWeightSubtractScoreNum());
                finalWeightSubtractScore = finalWeightSubtractScore.add(computeSubtract2.computeAvgScore());
            }
            resultPo.setFinalWeightSubtractScore(finalWeightSubtractScore);
            nomualFinalWeightScore = nomualFinalWeightScore.add(finalWeightSubtractScore);
            log.info(" =======nomualFinalWeightScore:{},finalWeightSubtractScore:{}", nomualFinalWeightScore,finalWeightSubtractScore);

            //jia减分
            BigDecimal finalWeightPlusSubtractScore = isNotNullScore(resultPo.getFinalWeightPlusSubtractScore()) ? resultPo.getFinalWeightPlusSubtractScore() : BigDecimal.ZERO;
            if (isNotNullScore(resultPo.getPeerFinalWeightPlusSubtractScore())) {
                MutualScoreResultCompute computePlusSubtract1 = new MutualScoreResultCompute(resultPo.getPeerFinalWeightPlusSubtractScore(), resultPo.getPeerFinalWeightPlusSubtractScoreNum());
                finalWeightPlusSubtractScore =  finalWeightPlusSubtractScore.add(computePlusSubtract1.computeAvgScore());
            }
            if (isNotNullScore(resultPo.getSubFinalWeightPlusSubtractScore())) {
                MutualScoreResultCompute computePlusSubtract2 = new MutualScoreResultCompute(resultPo.getSubFinalWeightPlusSubtractScore(), resultPo.getSubFinalWeightPlusSubtractScoreNum());
                finalWeightPlusSubtractScore = finalWeightPlusSubtractScore.add(computePlusSubtract2.computeAvgScore());
            }
            resultPo.setFinalWeightPlusSubtractScore(finalWeightPlusSubtractScore);
            nomualFinalWeightScore = nomualFinalWeightScore.add(finalWeightPlusSubtractScore);
            log.info(" =======nomualFinalWeightScore:{},finalWeightPlusSubtractScore:{}", nomualFinalWeightScore,finalWeightPlusSubtractScore);
            resultPo.setFinalWeightScore(nomualFinalWeightScore);
        }

        return taskResultPos;
    }




    /**
     * 根据taskUserId获取指标名称信息
     *
     * @param taskUserId
     * @return
     */
    public List<CalibratedPerfEvaluateTaskResultPo> listTaskScoreKpiItemInfoV3(String taskUserId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskKpiDo.class, "tk")
                .leftJoin(PerfEvaluateTaskUserDo.class, "u")
                .appendOn("tk.task_user_id = u.id")
                .clearSelect()
                .select(" tk.task_user_id,tk.kpi_type_id,tk.kpi_type_name,tk.kpi_item_id,tk.id,tk.kpi_item_name,tk.item_target_value,tk.item_finish_value,tk.item_unit,tk.item_weight")
                .select("tk.kpi_type_classify,tk.plus_limit,tk.subtract_limit,tk.max_extra_score,tk.item_rule,tk.scoring_rule,tk.scorer_type,tk.is_okr,tk.show_finish_bar")
                .select("tk.finish_value_type,tk.show_target_value,tk.input_format AS input_format,tk.item_target_value_text AS item_target_value_text,tk.item_finish_value_text AS item_finish_value_text")
                .select("tk.work_item_finish_value AS work_item_finish_value" )
                .select("tk.ind_level,tk.ind_level_group,tk.ind_level_group_id")
                .select("tk.item_final_score,tk.item_item_score,tk.item_final_appoint_score,tk.item_self_score,tk.item_peer_score,tk.item_sub_score,tk.item_superior_score,tk.item_appoint_score")
                //自动计算分
                .select(" if(tk.scorer_type = 'auto' OR (tk.is_okr = 'true' AND tk.open_okr_score = 1), tk.item_score,null)) AS autoScore")
                .setRsType(CalibratedPerfEvaluateTaskResultPo.class)
                .whereEq("tk.task_user_id", taskUserId)
                .whereEq("tk.is_deleted", Boolean.FALSE.toString())
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .groupBy("tk.kpi_item_id")
                .orderBy("tk.id");
        List<CalibratedPerfEvaluateTaskResultPo> taskResultPos = domainDao.listAll(comQB);
        if (taskResultPos == null) {
            return null;
        }

        for (CalibratedPerfEvaluateTaskResultPo resultPo : taskResultPos) {
            log.info(" =======resultPo:{}", JSONUtil.toJsonStr(resultPo));
            BigDecimal nomualFinalWeightScore = isNotNullScore(resultPo.getNomualFinalWeightScore())?resultPo.getNomualFinalWeightScore():BigDecimal.ZERO;
            //同级
            if (isNotNullScore(resultPo.getNomualPeerFinalWeightScore())) {
                MutualScoreResultCompute compute = new MutualScoreResultCompute(resultPo.getNomualPeerFinalWeightScore(), resultPo.getNomualPeerNum());
                nomualFinalWeightScore = nomualFinalWeightScore.add(compute.computeAvgScore()); //计算互评平均分
            }
            //下级
            if (isNotNullScore(resultPo.getNomualSubfinalWeightScore())) {
                MutualScoreResultCompute compute2 = new MutualScoreResultCompute(resultPo.getNomualSubfinalWeightScore(), resultPo.getNomualSubNum());
                nomualFinalWeightScore = nomualFinalWeightScore.add(compute2.computeAvgScore()); //计算互评平均分
            }
            //自动计算得分
            if (isNotNullScore(resultPo.getAutoScore())) {
                nomualFinalWeightScore = nomualFinalWeightScore.add(resultPo.getAutoScore());
                log.info(" =======nomualFinalWeightScore:{},resultPo.getAutoScore:{}", nomualFinalWeightScore,resultPo.getAutoScore());
            }

            //加分
            BigDecimal finalWeightPlusScore = isNotNullScore(resultPo.getFinalWeightPlusScore()) ? resultPo.getFinalWeightPlusScore() : BigDecimal.ZERO;
            if (isNotNullScore(resultPo.getPeerFinalWeightPlusScore())) {
                MutualScoreResultCompute computePlus1 = new MutualScoreResultCompute(resultPo.getPeerFinalWeightPlusScore(), resultPo.getPeerFinalWeightPlusScoreNum());
                finalWeightPlusScore =  finalWeightPlusScore.add(computePlus1.computeAvgScore());
            }
            if (isNotNullScore(resultPo.getSubFinalWeightPlusScore())) {
                MutualScoreResultCompute computePlus2 = new MutualScoreResultCompute(resultPo.getSubFinalWeightPlusScore(), resultPo.getSubFinalWeightPlusScoreNum());
                finalWeightPlusScore = finalWeightPlusScore.add(computePlus2.computeAvgScore());
            }
            resultPo.setFinalWeightPlusScore(finalWeightPlusScore);
            nomualFinalWeightScore = nomualFinalWeightScore.add(finalWeightPlusScore);
            log.info(" =======nomualFinalWeightScore:{},finalWeightPlusScore:{}", nomualFinalWeightScore,finalWeightPlusScore);

            //减分
            BigDecimal finalWeightSubtractScore = isNotNullScore(resultPo.getFinalWeightSubtractScore()) ? resultPo.getFinalWeightSubtractScore() : BigDecimal.ZERO;
            if (isNotNullScore(resultPo.getPeerFinalWeightSubtractScore())) {
                MutualScoreResultCompute computeSubtract1 = new MutualScoreResultCompute(resultPo.getPeerFinalWeightSubtractScore(), resultPo.getPeerFinalWeightSubtractScoreNum());
                finalWeightSubtractScore =  finalWeightSubtractScore.add(computeSubtract1.computeAvgScore());
            }
            if (isNotNullScore(resultPo.getSubFinalWeightSubtractScore())) {
                MutualScoreResultCompute computeSubtract2 = new MutualScoreResultCompute(resultPo.getSubFinalWeightSubtractScore(), resultPo.getSubFinalWeightSubtractScoreNum());
                finalWeightSubtractScore = finalWeightSubtractScore.add(computeSubtract2.computeAvgScore());
            }
            resultPo.setFinalWeightSubtractScore(finalWeightSubtractScore);
            nomualFinalWeightScore = nomualFinalWeightScore.add(finalWeightSubtractScore);
            log.info(" =======nomualFinalWeightScore:{},finalWeightSubtractScore:{}", nomualFinalWeightScore,finalWeightSubtractScore);

            //jia减分
            BigDecimal finalWeightPlusSubtractScore = isNotNullScore(resultPo.getFinalWeightPlusSubtractScore()) ? resultPo.getFinalWeightPlusSubtractScore() : BigDecimal.ZERO;
            if (isNotNullScore(resultPo.getPeerFinalWeightPlusSubtractScore())) {
                MutualScoreResultCompute computePlusSubtract1 = new MutualScoreResultCompute(resultPo.getPeerFinalWeightPlusSubtractScore(), resultPo.getPeerFinalWeightPlusSubtractScoreNum());
                finalWeightPlusSubtractScore =  finalWeightPlusSubtractScore.add(computePlusSubtract1.computeAvgScore());
            }
            if (isNotNullScore(resultPo.getSubFinalWeightPlusSubtractScore())) {
                MutualScoreResultCompute computePlusSubtract2 = new MutualScoreResultCompute(resultPo.getSubFinalWeightPlusSubtractScore(), resultPo.getSubFinalWeightPlusSubtractScoreNum());
                finalWeightPlusSubtractScore = finalWeightPlusSubtractScore.add(computePlusSubtract2.computeAvgScore());
            }
            resultPo.setFinalWeightPlusSubtractScore(finalWeightPlusSubtractScore);
            nomualFinalWeightScore = nomualFinalWeightScore.add(finalWeightPlusSubtractScore);
            log.info(" =======nomualFinalWeightScore:{},finalWeightPlusSubtractScore:{}", nomualFinalWeightScore,finalWeightPlusSubtractScore);
            resultPo.setFinalWeightScore(nomualFinalWeightScore);
        }

        return taskResultPos;
    }

    private boolean isNotNullScore(BigDecimal score) {
        return null != score && BigDecimal.ZERO.compareTo(score) != 0;
    }
    /**
     * 查询纬度字段字定义配置
     */
    public List<KpiTypeUsedField> getTypeUsedFieldDoList(String companyId, String taskUserId, String kpiTypeId) {
        ComQB used = ComQB.build(EmpEvalTypeUsedFieldDo.class, "tu")
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEqReq("kpi_type_id", kpiTypeId)
                .appendWhere("is_deleted='false'")
                .orderByAsc("sort");
        return domainDao.listAllDomain(used, KpiTypeUsedField.class);
    }

    /**
     * 查询指标字段字定义配置
     */
    public List<KpiItemUsedField> getItemUsedFieldDoList(String companyId, String taskUserId) {
        ComQB itemUsed = ComQB.build(PerfEvaluateItemUsedFieldDo.class, "tu")
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .appendWhere("is_deleted='false'")
                .orderByAsc("sort");
        return domainDao.listAllDomain(itemUsed, KpiItemUsedField.class);
    }


    /**
     * 根据taskUserId获取指标评分信息
     *
     * @param taskUserId
     * @return
     */
    public List<CalibratedPerfEvaluateTaskResultPo> listTaskResultInfo(String taskUserId, EmpEvalMerge evalMerge, Map<String, KpiEmp> empMap,Boolean openAvgWeightCompute) {
        ComQB tkQb = ComQB.build(PerfEvaluateTaskKpiDo.class, "tk")
                .whereEq("tk.task_user_id", taskUserId)
                .whereEq("tk.is_deleted", Boolean.FALSE.toString())
                .appendWhere("tk.scorer_type = 'auto'");
        List<EvalKpi> autoKpis = domainDao.listAllDomain(tkQb, EvalKpi.class);
        //.whereEq("tk.scorer_type","auto")
        //.whereEq("is_okr","false");

        ComQB srQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "r")
                .leftJoin(PerfEvaluateTaskUserDo.class, "u")
                .appendOn("r.task_user_id = u.id")
                .leftJoin(EmpEvalRuleDo.class, "er")
                .appendOn("u.company_id = er.company_id and u.id = er.emp_eval_id and er.is_deleted = 'false'")
                .clearSelect()
                .select("r.task_user_id,r.kpi_type_id, r.kpi_item_id,r.scorer_type, if(r.veto_flag is null,( r.score * ( r.score_weight / 100 ) ),null) AS score")
                .select("r.final_score,r.final_weight_score,r.final_weight_plus_score,r.final_weight_subtract_score, r.veto_flag,r.score_level")
                .select(
                        "(case when r.scorer_type = 'self_score' \n" +
                                "\tthen ( CASE WHEN json_valid ( er.s3_self_rater ) = 1 THEN JSON_UNQUOTE ( JSON_EXTRACT ( er.s3_self_rater -> '$[0]', '$.nodeWeight' ) ) END )\n" +
                                "\twhen r.scorer_type = 'superior_score'\n" +
                                "\tthen ( CASE WHEN json_valid ( er.s3_super_rater ) = 1 THEN JSON_UNQUOTE ( JSON_EXTRACT ( er.s3_super_rater -> '$[0]', '$.nodeWeight' ) ) END )\n" +
                                "\twhen r.scorer_type = 'sub_score'\n" +
                                "\tthen ( CASE WHEN json_valid ( er.s3_sub_rater ) = 1 THEN JSON_UNQUOTE ( JSON_EXTRACT ( er.s3_sub_rater -> '$[0]', '$.nodeWeight' ) ) END )\n" +
                                "\twhen r.scorer_type = 'peer_score'\n" +
                                "\tthen ( CASE WHEN json_valid ( er.s3_peer_rater ) = 1 THEN JSON_UNQUOTE ( JSON_EXTRACT ( er.s3_peer_rater -> '$[0]', '$.nodeWeight' ) ) END )\n" +
                                "\t end ) as scoreWeight,\n" +
                                "(\n" +
                                "CASE\n" +
                                "\tWHEN r.scorer_type = 'self_score' THEN\n" +
                                "\t( CASE WHEN json_valid ( er.s3_self_rater ) = 1 THEN JSON_UNQUOTE ( JSON_EXTRACT ( er.s3_self_rater -> '$[0]', '$.rateMode' ) ) END ) \n" +
                                "\tWHEN r.scorer_type = 'superior_score' THEN\n" +
                                "\t( CASE WHEN json_valid ( er.s3_super_rater ) = 1 THEN JSON_UNQUOTE ( JSON_EXTRACT ( er.s3_super_rater -> '$[0]', '$.rateMode' ) ) END ) \n" +
                                "\tWHEN r.scorer_type = 'sub_score' THEN\n" +
                                "\t( CASE WHEN json_valid ( er.s3_sub_rater ) = 1 THEN JSON_UNQUOTE ( JSON_EXTRACT ( er.s3_sub_rater -> '$[0]', '$.rateMode' ) ) END ) \n" +
                                "\tWHEN r.scorer_type = 'peer_score' THEN\n" +
                                "\t( CASE WHEN json_valid ( er.s3_peer_rater ) = 1 THEN JSON_UNQUOTE ( JSON_EXTRACT ( er.s3_peer_rater -> '$[0]', '$.rateMode' ) ) END ) \n" +
                                "END) AS rateMode")
                .setRsType(CalibratedPerfEvaluateTaskResultPo.class)
                .whereEq("r.task_user_id", taskUserId)
                .whereEq("r.is_deleted", Boolean.FALSE.toString())
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereIn("r.scorer_type", new ArrayList<>(Arrays.asList("self_score", "superior_score", "appoint_score", "sub_score", "peer_score", "item_score")))
                .whereBig("r.score_weight", 0)
                .groupBy("r.task_user_id,r.kpi_item_id,r.scorer_type,r.scorer_id");

        ComQB comQB = ComQB.build().fromQ(srQB, "a")
                .clearSelect().select("a.task_user_id,\n" +
                        "\ta.kpi_type_id,\n" +
                        "\ta.kpi_item_id,\n" +
                        "\ta.scorer_type,\n" +
                        "\tsum(a.score) as score,\n" +
                        "\tsum(a.final_score) as final_score,\n" +
                        "\tsum(a.final_weight_score) as final_weight_score,\n" +
                        "\tsum(a.final_weight_plus_score) as final_weight_plus_score,\n" +
                        "\tsum(a.final_weight_subtract_score) as final_weight_subtract_score,\n" +
                        "\ta.scoreWeight,\n" +
                        "\ta.rateMode")
                .select("max(a.score_level) score_level")
                .select("(case when a.veto_flag = 'true' then 'true' else 'false' end ) vetoFlag")
                .setRsType(CalibratedPerfEvaluateTaskResultPo.class)
                .groupBy("a.scorer_type,a.kpi_item_id");
        List<CalibratedPerfEvaluateTaskResultPo> taskResultPos = domainDao.listAll(comQB);
        if (taskResultPos == null) {
            return null;
        }
        Map<String, EvalKpi> autoMap = CollUtil.toMap(autoKpis, new HashMap<>(), EvalKpi::getKpiItemId);
        Map<String, List<EvalScoreResult>> stringListMap = evalMerge.getScoreNodesOld();

        taskResultPos.forEach(r -> {
            Set<String> scorerIds = new HashSet<>();
            List<EvalScoreResult> results = stringListMap.get(r.getScorerType() + "-" + r.getKpiItemId());
            if (CollUtil.isEmpty(results)) {
                log.error("stringListMap 分组为空 key = {}",r.getScorerType() + "-" + r.getKpiItemId());
                return;
            }
            for (EvalScoreResult result : results) {
                if (result.getScoreWeight().compareTo(BigDecimal.ZERO) != 0 && EvaluateAuditSceneEnum.mutualScoreTypes().contains(r.getScorerType())) {
                    scorerIds.add(result.getScorerId());
                }
            }
            if (!openAvgWeightCompute && CollUtil.isNotEmpty(scorerIds)) {
                MutualScoreResultCompute compute = new MutualScoreResultCompute(r.getScore(), scorerIds.size());
                r.setScore(compute.computeAvgScore()); //计算互评平均分
                MutualScoreResultCompute compute2 = new MutualScoreResultCompute(r.getFinalScore(), scorerIds.size());
                r.setFinalScore(compute2.computeAvgScore()); //计算互评平均分
                MutualScoreResultCompute compute3 = new MutualScoreResultCompute(r.getFinalWeightScore(), scorerIds.size());
                r.setFinalWeightScore(compute3.computeAvgScore()); //计算互评平均分
            }
            r.setPlusSubScore();
        });

        taskResultPos.forEach(t -> {
            if (CollUtil.isNotEmpty(autoMap)) {
                t.addItemAutoScore(Objects.isNull(autoMap.get(t.getKpiItemId())) ? null : autoMap.get(t.getKpiItemId()).getItemAutoScore());
            }
            List<EvalScoreResult> results = stringListMap.get(t.getScorerType() + "-" + t.getKpiItemId());
            if (CollUtil.isEmpty(results)) {
                return;
            }
            List<EmpEvalTaskScore2Po.SubmitedScoreResult> submitedScoreResults = new ArrayList<>();
            for (EvalScoreResult result : results) {
                EmpEvalTaskScore2Po.SubmitedScoreResult convertRs = Convert.convert(EmpEvalTaskScore2Po.SubmitedScoreResult.class, result);
                KpiEmp kpiEmp = empMap.get(result.getScorerId());
                if (kpiEmp != null) {
                    convertRs.setScorer(kpiEmp.getEmpName());
                    convertRs.setAvatar(kpiEmp.getAvatar());
                }
                submitedScoreResults.add(convertRs);
            }
            t.addScoreNode(t.getScorerType(), submitedScoreResults);
        });
        return taskResultPos;
    }


    /**
     * 根据taskUserId获取校准信息
     *
     * @param taskUserId
     * @return
     */
    public List<CalibratedPerfEvaluateTaskResultPo> listTaskCalibratedInfo(String taskUserId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "r")
                .clearSelect()
                .select(" r.task_user_id,\n" +
                        "\tr.approval_order,\n" +
                        "\tr.index_calibration")
                .setRsType(CalibratedPerfEvaluateTaskResultPo.class)
                .whereEq("r.task_user_id", taskUserId)
                .whereEq("r.is_deleted", Boolean.FALSE.toString())
                .whereEq("r.scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene())
                .orderBy("r.approval_order");
        List<CalibratedPerfEvaluateTaskResultPo> taskResultPos = domainDao.listAll(comQB);
        if (taskResultPos == null) {
            return null;
        }
        return taskResultPos;
    }


    /**
     * 查询校准详情
     */
    public PagedList<CalibratedPagePo> pageCalibratedDetails(WorkWaitMsgQuery query) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "r")
                .leftJoin(EmployeeBaseInfoDo.class, "e")
                .appendOn(" r.company_id = e.company_id and r.scorer_id = e.employee_id ")
                .leftJoin(PerfEvaluateTaskUserDo.class, "u")
                .appendOn(" r.company_id = u.company_id and r.task_user_id = u.id and u.is_deleted ='false' ")
                .clearSelect()
                .select(" e.name as taskEmpName,\n" +
                        "\te.avatar,\n" +
                        "\tifnull(r.calibration_type,1) as calibration_type,\n" +
                        "\tr.operate_reason,\n" +
                        "\tr.score as finalScore,\n" +
                        "\tr.score_level as evaluationLevel,\n" +
                        "\tr.score_comment,\n" +
                        "\tr.updated_time,u.perf_coefficient ")
                .setRsType(CalibratedPagePo.class)
                .whereEqReq("r.company_id", query.getCompanyId())
                .whereEq("r.task_user_id", query.getTaskUserId())
                .whereEq("r.is_deleted", Boolean.FALSE.toString())
                .whereEq("r.scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene())
                .whereEq("r.audit_status", "pass")
                .orderBy("r.updated_time desc");
        comQB.setPage(query.getPageNo(), query.getPageSize());
        return domainDao.listPage(comQB);
    }

    public List<CalibratedOrgStaPo> calibratedOrg(CalibratedOrgQry qry) {
        log.debug("CalibratedOrgQry:{}", JSONUtil.toJsonStr(qry));
        List<String> dirChildOrgIds = directChildOrg(qry);
        if (CollUtil.isEmpty(dirChildOrgIds)) {
            return new ArrayList<>();
        }
        log.info("****dirChildOrgIds****{}", JSONUtil.toJsonStr(dirChildOrgIds));
        //查出统计部门信息
        ComQB qb = ComQB.build(EmpOrganizationDo.class, "org")
                .clearSelect().setRsType(CalibratedOrgStaPo.class)
                .select("org.org_id,org.org_name")
                .whereEq("org.company_id", qry.getCompanyId())
                .whereInReq("org.org_id", dirChildOrgIds);
        List<CalibratedOrgStaPo> staOrgs = domainDao.listAll(qb);
        log.info("****staOrgs****{}", JSONUtil.toJsonStr(staOrgs));
        CalibratedOrgStaPo total = new CalibratedOrgStaPo("total", "全部", 0);
        for (CalibratedOrgStaPo staOrg : staOrgs) {
            qry.setOrgId(staOrg.getOrgId());
            //统计各等级人数及占比
            ComQB groupByLevelQ = calibratedOrgBaseSql(qry).clearSelect().setRsType(LevelRate.class)
                    .select("u.task_status,rrim.distribution_id  distribution_id,  IFNULL(if(owr.audit_status is null and ta.status is not null, count(owr.id),if(u.task_status != 'resultsAuditing',count(1),null)),0) cnt")
                    .select("GROUP_CONCAT( DISTINCT if(owr.audit_status is null and ta.status is not null, null,if(u.task_status != 'resultsAuditing',u.id,null))) AS notEnterTaskUserId,GROUP_CONCAT(DISTINCT u.id) as taskUserId")
                    .select("if(rac.evaluation_level is null ,if(owr.id is null ,'none', if(owr.audit_status is null, u.evaluation_level,null)),rac.evaluation_level) level")
                    .whereLike("u.at_org_code_path", qry.asLikeOrgId())
                    .groupBy("u.task_status,rrim.distribution_id,level");
            List<LevelRate> levelRates = domainDao.listAll(groupByLevelQ);
            log.debug("****levelRates****{}", JSONUtil.toJsonStr(levelRates));
            staOrg.parseLevelRates(levelRates);
            total.addSum(levelRates, staOrg.getSumCnt());
        }
        staOrgs.add(total);
        staOrgs.removeIf(orgStaPo -> orgStaPo.getSumCnt() == 0 && orgStaPo.noneEqualZero());
        return staOrgs;
    }

    private Integer minDepth(List<String> orgCode) {
        log.debug("查询出的codes集合:{}",JSONUtil.toJsonStr(orgCode));
        if (CollUtil.isEmpty(orgCode)) {
            return null;
        }
        Map<String, List<Integer>> codePositions = new HashMap<>();
        if (orgCode.size() == 1) {
            return StrUtil.splitTrim(orgCode.get(0),"|").size();
        }
        for (String combinedCodes : orgCode) {
            int globalIndex = 0;
            List<String> codes = StrUtil.splitTrim(combinedCodes,"|");
            if (codes.size() == 1) {
                return 1;
            }
            for (String code : codes) {
                if (StrUtil.isBlank(code)) {
                    continue;
                }
                codePositions.computeIfAbsent(code, k -> new ArrayList<>()).add(globalIndex);
                globalIndex++;
            }
        }
        List<Integer> indexList = new ArrayList<>();
        for (Map.Entry<String, List<Integer>> entry : codePositions.entrySet()) {
            List<Integer> indices = entry.getValue();
            if (!indices.isEmpty()) {
                if (indices.size() == orgCode.size()) {
                    indexList.addAll(indices);
                }
            }
        }
        log.debug("下标集合:{}",JSONUtil.toJsonStr(indexList));
        indexList = indexList.stream()
                .collect(Collectors.groupingBy(e -> e, Collectors.counting()))
                .entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .map(Map.Entry::getKey)
                .distinct()
                .collect(Collectors.toList());
        return indexList.size() == 1 ? 2 : indexList.size() ;
    }

    public List<String> directChildOrg(CalibratedOrgQry qry) {
        ComQB orgQ;
        if (StrUtil.isBlank(qry.getOrgId())) {
            ComQB pathCodeComQB = calibratedMinOrgBaseSql(qry).clearSelect().setRsType(String.class)
                    .select("DISTINCT at_org_code_path as depth");
            List<String> pathDepth = domainDao.listAll(pathCodeComQB);
            Integer minDepth = minDepth(pathDepth);
            log.debug("查询最小部门层级:{}",minDepth);
            if (Objects.isNull(minDepth)) {
                return Collections.emptyList();
            }
            ComQB limit1 = calibratedMinOrgBaseSql(qry).clearSelect().setRsType(String.class)
                    .select("u.at_org_code_path")
                    .whereBigEq("u.at_org_path_hight", minDepth);
            List<String> orgPathCodes = domainDao.listAll(limit1);
            log.debug("查询出的部门pathCode:{}",JSONUtil.toJsonStr(orgPathCodes));
            MinDepthOrgPo depthOrg = new MinDepthOrgPo(minDepth, orgPathCodes);
            if (minDepth <= 1) {//已是根部门则直接返回根部门id
                return depthOrg.parseCommonParentOrgIds();
            }
            qry.setOrgIds(depthOrg.parseCommonParentOrgIds());
        }
        orgQ = directCalibratedOrgSql(qry);
        List<String> dirChildOrgIds = domainDao.listAll(orgQ);
        return dirChildOrgIds;
    }

//    private ComQB calibratedOrgBaseSql(CalibratedOrgQry qry) {
//        return ComQB.build(PerfEvaluateTaskUserDo.class, "u")
//                .leftJoin(CompanyMsgCenterDo.class,"m").appendOn("u.company_id = m.company_id and u.id = m.link_id")
//                .join(EmpEvalRuleDo.class, "r").appendOn("r.company_id = u.company_id and r.emp_eval_id = u.id and r.is_deleted='false'")
//                .join(ResultRankInstanceMemberDo.class, "rrim").appendOn("rrim.company_id= u.company_id and u.id = rrim.task_user_id")
//                .leftJoin(PerfEvaluateTaskScoreResultDo.class, "owr").appendOn("owr.company_id = u.company_id and u.id = owr.task_user_id  and owr.audit_status is null and  owr.is_deleted='false'  ")
//                .leftJoin(ResultAuditCacheDo.class, "rac").appendOn("rac.company_id = u.company_id and rac.task_user_id = u.id and rac.is_deleted='false' and rac.owner_emp_id='"
//                        + qry.getOpEmpId() + "'")
//                .appendWhere("u.is_deleted='false'")
//                .whereEqReq("owr.scorer_id", qry.getOpEmpId())
//                .whereEqReq("owr.scorer_type", AuditEnum.FINAL_RESULT_AUDIT.getScene())
//                .whereEqReq("u.company_id", qry.getCompanyId())
//                .whereEq("u.cycle_id", qry.getCycleId())
//                .whereIn("u.task_id", qry.getTaskId())
//                .whereEq("rrim.distribution_id", qry.getDistributionId())
//                .whereEq("m.business_scene", MsgSceneEnum.TASK_RESULT_AUDIT.getType())
//                .whereEq("m.emp_id", qry.getOpEmpId())
//                .whereEq("m.handler_status", Boolean.FALSE.toString()).groupBy("u.id");
//    }

    protected ComQB calibratedOrgBaseSql(CalibratedOrgQry qry) {
        return ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(CompanyMsgCenterDo.class, "m")
                .appendOn("u.company_id = m.company_id and u.id = m.link_id AND m.business_scene = 'task_result_audit' AND m.emp_id = '"+qry.getOpEmpId()+"' AND m.handler_status = 'false'")
                .join(ResultRankInstanceMemberDo.class, "rrim").appendOn("rrim.company_id= u.company_id and u.id = rrim.task_user_id")
                .leftJoin(TaskAuditDo.class, "ta")
                .appendOn("u.company_id = ta.company_id and u.id = ta.task_user_id")
                .leftJoin(PerfEvaluateTaskScoreResultDo.class, "owr")
                .appendOn("ta.company_id = owr.company_id and ta.id = owr.task_audit_id  and  owr.is_deleted='false' ")
                .leftJoin(ResultAuditCacheDo.class, "rac").appendOn("rac.company_id = u.company_id and rac.task_user_id = u.id and rac.is_deleted='false' and rac.owner_emp_id='"
                        + qry.getOpEmpId() + "'")
                .appendWhere("u.is_deleted='false'")
                .whereEqReq("u.company_id", qry.getCompanyId())
                .whereEq("u.cycle_id", qry.getCycleId())
                .whereIn("u.task_id", qry.getTaskId())
                .whereNotIn("u.task_status", Arrays.asList(TalentStatus.TERMINATED.getStatus(), TalentStatus.DRAW_UP_ING.getStatus(), TalentStatus.PUBLISHED.getStatus()))
                .appendWhere(" (if(owr.transfer_id is null, ta.approver_info = '"+qry.getOpEmpId()+"', owr.scorer_id = '"+qry.getOpEmpId()+"')) ")
                .appendWhere(" owr.audit_status is null ")
                .whereEq("ta.scene", AuditEnum.FINAL_RESULT_AUDIT.getScene())
                .whereEq("ta.is_deleted", Boolean.FALSE.toString())
                .whereEq("rrim.distribution_id", qry.getDistributionId())
                .whereEq("rrim.is_deleted", Boolean.FALSE.toString())
                .whereIsNull("owr.score");
    }


    protected ComQB calibratedMinOrgBaseSql(CalibratedOrgQry qry) {
        return ComQB.build(PerfEvaluateTaskUserDo.class, "u")
//                .join(EmpEvalRuleDo.class, "r").appendOn("r.company_id = u.company_id and r.emp_eval_id = u.id and r.is_deleted='false'")
                .leftJoin(CompanyMsgCenterDo.class, "m").appendOn("u.company_id = m.company_id and u.id = m.link_id")
                .join(ResultRankInstanceMemberDo.class, "rrim").appendOn("rrim.company_id= u.company_id and u.id = rrim.task_user_id")
                .leftJoin(PerfEvaluateTaskScoreResultDo.class, "owr")
                .appendOn("owr.company_id = u.company_id and owr.task_user_id = u.id  and  owr.is_deleted='false' ")
                .appendWhere("u.is_deleted='false'")
                .whereNotIn("u.task_status", Arrays.asList(TalentStatus.TERMINATED.getStatus(), TalentStatus.DRAW_UP_ING.getStatus(), TalentStatus.PUBLISHED.getStatus()))
                .whereEqReq("u.company_id", qry.getCompanyId())
                .whereEq("u.cycle_id", qry.getCycleId())
                .whereIn("u.task_id", qry.getTaskId())
                .whereEqReq("owr.scorer_id", qry.getOpEmpId())
                .whereEqReq("owr.scorer_type",AuditEnum.FINAL_RESULT_AUDIT.getScene())
                .whereEq("m.business_scene",MsgSceneEnum.TASK_RESULT_AUDIT.getType())
                .whereEq("m.emp_id",qry.getOpEmpId())
                .whereEq("m.handler_status",Boolean.FALSE.toString())
                .whereEq("rrim.distribution_id", qry.getDistributionId());
    }

    private ComQB directCalibratedOrgSql(CalibratedOrgQry qry) {
        ComQB orgComQb = ComQB.build(EmpOrganizationDo.class, "s")
                .clearSelect().select("s.org_id").setRsType(String.class)
                .whereEqReq("s.company_id", qry.getCompanyId())
                .appendWhere("s.status='valid'");
        if (qry.isIncludeCurrent()) {
            orgComQb.whereInReq("s.org_id",qry.getOrgIds());
        }else {
            orgComQb.whereInReq("s.parent_org_id", qry.getOrgIds());
        }
        return orgComQb;
    }

    public int batchSaveOrUpdateCache(List<ResultAuditCache> caches) {
        if (CollUtil.isEmpty(caches)) {
            return 0;
        }
        caches.forEach(resultAuditCache -> resultAuditCache.setId(domainDao.nextLongAsStr(result_audit_cache_seq)));
        TenantId companyId = caches.get(0).getCompanyId();
        String ownerEmpId = caches.get(0).getOwnerEmpId();
        List<String> taskUserIds = CollUtil.map(caches, cache -> cache.getTaskUserId(), true);
        UpdateBuilder del = UpdateBuilder.build(ResultAuditCacheDo.class)
                .appendSet("is_deleted='true'")
                .whereEq("companyId", companyId.getId())
                .whereEqReq("ownerEmpId", ownerEmpId)
                .whereInReq("taskUserId", taskUserIds);
        domainDao.update(del);
        List<ResultAuditCacheDo> datas = CollUtil.map(caches, cache -> new ToDataBuilder<>(cache, ResultAuditCacheDo.class).data(), true);
        int row = domainDao.saveBatch(datas);
        //驳回标记为完成
//        UpdateBuilder up = UpdateBuilder.build(ResultAuditBatchItemDo.class)
//                .set("status", 2)
//                .whereEqReq("company_id", companyId.getId())
//                .whereInReq("taskUserId", taskUserIds);
//        domainDao.update(up);
        return row;
    }

    public void delResultAuditCache(String companyId, List<String> taskUserIds, String opEmpId) {
        if (CollUtil.isEmpty(taskUserIds)) {
            return;
        }
        UpdateBuilder del = UpdateBuilder.build(ResultAuditCacheDo.class)
                .appendSet("is_deleted='true'")
                .whereEqReq("companyId", companyId)
                .whereEq("ownerEmpId", opEmpId)
                .whereInReq("taskUserId", taskUserIds);
        domainDao.update(del);
    }

    public void saveRejectBatch(Map<String, ResultAuditBatch> auditBatchs) {
        if (CollUtil.isEmpty(auditBatchs)) {
            return;
        }
        List<ResultAuditBatchDo> auditBatchDos = new ArrayList<>();
        List<ResultAuditBatchItemDo> itemDos = new ArrayList<>();
        List<ResultAuditBatchOwnerEmpDo> empOwns = new ArrayList<>();
        List<String> closeNodes = new ArrayList<>();
        List<String> closeAuditIds = new ArrayList<>();
        List<String> reopenNodes = new ArrayList<>();
        List<String> reopenMsgIds = new ArrayList<>();
        List<String> closeMsgIds = new ArrayList<>();

        auditBatchs.forEach((taskUserId, auditBatch) -> {
            auditBatch.setId(domainDao.nextLongAsStr(result_audit_batch_seq));
            ResultAuditBatchBuilder builder = new ResultAuditBatchBuilder(auditBatch);
            builder.build();
            closeNodes.addAll(CollUtil.map(builder.getCloseNodes(), r -> r.getId(), true));
            closeAuditIds.addAll(builder.getCloseAuditIds());
            reopenNodes.addAll(CollUtil.map(builder.getReOpenNodes(), r -> r.getId(), true));
            reopenMsgIds.addAll(builder.getReOpenMsgIds());
            closeMsgIds.addAll(builder.getCloseMsgIds());
            auditBatchDos.add(builder.getBatchDo());
            itemDos.addAll(builder.getItemDos());
            empOwns.addAll(builder.getOwnerEmpDos());
        });
        empOwns.forEach(empOwn -> empOwn.setId(domainDao.nextLongAsStr(result_audit_batch_owner_emp_seq)));
        domainDao.saveBatch(auditBatchDos);
        domainDao.saveBatch(itemDos);
        domainDao.saveBatch(empOwns);
        ResultAuditBatch first = CollUtil.getFirst(auditBatchs.values());
        UpdateBuilder closeUp = UpdateBuilder.build(PerfEvaluateTaskScoreResultDo.class)
                .set("is_deleted", "true")
                .set("updatedTime", new Date())
                .whereEq("company_id", first.getCompanyId().getId())
                .whereInReq("id", closeNodes);
        domainDao.update(closeUp);
        UpdateBuilder closeAuditUp = UpdateBuilder.build(TaskAuditDo.class)
                .set("status", null)
                .set("updatedTime", new Date())
                .whereEq("company_id", first.getCompanyId().getId())
                .whereInReq("id", closeAuditIds);
        domainDao.update(closeAuditUp);
        UpdateBuilder reOpen = UpdateBuilder.build(PerfEvaluateTaskScoreResultDo.class)
                .appendSet("auditStatus = null")
                .set("updatedTime", new Date())
                .whereEq("company_id", first.getCompanyId().getId())
                .whereInReq("id", reopenNodes);
        domainDao.update(reOpen);

        UpdateBuilder reOpenMsg = UpdateBuilder.build(CompanyMsgCenterDo.class)
                .appendSet("handlerStatus = 'false'")
                .set("updatedTime", new Date())
                .whereEq("company_id", first.getCompanyId().getId())
                .whereInReq("id", reopenMsgIds);
        domainDao.update(reOpenMsg);
        //关闭当前一级
//        UpdateBuilder closeMsgUp = UpdateBuilder.build(CompanyMsgCenterDo.class)
//                .appendSet("handlerStatus = 'true'")
//                .set("updatedTime", new Date())
//                .whereEq("company_id", first.getCompanyId().getId())
//                .whereInReq("id", closeMsgIds);
//        domainDao.update(closeMsgUp);
    }

    public List<RejectAuditResultFlow> listRejectAuditResultFlow(TenantId tenantId, List<String> taskUserIds) {
        ComQB resultBuilder = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "r")
                .whereEq("company_id", tenantId.getId())
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEq("scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene())
                .whereInReq("task_user_id", taskUserIds)
                .appendWhere("(audit_status = 'pass' or audit_status is null)");
        List<EvalScoreResult> results = domainDao.listAllDomain(resultBuilder, EvalScoreResult.class);
        ListWrap<EvalScoreResult> rsWrap = new ListWrap<>(results).groupBy(result -> result.getTaskUserId());

        ComQB cenQb = ComQB.build(CompanyMsgCenterDo.class, "c")
                .whereEq("company_id", tenantId.getId())
                //.whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEq("business_scene", MsgSceneEnum.TASK_RESULT_AUDIT.getType())
                .whereInReq("link_id", taskUserIds);
        List<CompanyMsgCenter> msgCenters = domainDao.listAllDomain(cenQb, CompanyMsgCenter.class);
        ListWrap<CompanyMsgCenter> centerListWrap = new ListWrap<>(msgCenters).groupBy(center -> center.getLinkId());


        List<RejectAuditResultFlow> flowWrap = new ArrayList<>();
        rsWrap.getGroups().forEach((taskUsrerId, rss) -> {
            List<CompanyMsgCenter> centers = centerListWrap.groupGet(taskUsrerId);
            RejectAuditResultFlow flow = new RejectAuditResultFlow(tenantId, taskUsrerId, rss, centers);
            flowWrap.add(flow);
        });

        return flowWrap;
    }

    public String rejectAuditResultReqNo() {
        return domainDao.nextLongAsStr(result_audit_batch_seq);
    }

    public List<ResultAuditBatchWithRejectNamePo> listRejectResultAuditBatch(TenantId companyId, String cycleId, String ownerEmpId) {
        ComQB qb = ComQB.build(ResultAuditBatchDo.class, "b")
                .join(ResultAuditBatchOwnerEmpDo.class, "oe").appendOn(" b.id = oe.result_audit_batch_id and b.company_id = oe.company_id")
                .join(EmployeeBaseInfoDo.class, "ebi").appendOn("b.company_id = ebi.company_id and b.reject_emp_id = ebi.employee_id")
                .clearSelect().setRsType(ResultAuditBatchWithRejectNamePo.class)
                .select(" b.*, ebi.name reject_emp_name ")
                .whereEqReq("oe.company_id", companyId.getId())
                .whereEqReq("oe.owner_emp_id", ownerEmpId)
                .whereEqReq("b.cycleId", cycleId)
                .orderByDesc("b.created_time");
        List<ResultAuditBatchWithRejectNamePo> batchs = domainDao.listAll(qb);
        if (CollUtil.isEmpty(batchs)) {
            return batchs;
        }
        List<String> batchIds = CollUtil.map(batchs, ba -> ba.getId(), true);
        ListWrap<ResultAuditBatchItemDo> itemDos = listRejectResultBatchItem(companyId, batchIds);
        batchs.forEach(batch -> {
            List<ResultAuditBatchItemDo> itemGroup = itemDos.groupGet(batch.getId());
            batch.setItems(itemGroup);
        });
        return batchs;
    }

    public RejectResultItemCountPo getRejectResultItemCount(TenantId companyId, String cycleId, String ownerEmpId, List<String> taskIds) {
        ComQB qb = ComQB.build(ResultAuditBatchItemDo.class, "rabi")
                .join(ResultAuditBatchOwnerEmpDo.class, "raboe")
                .appendOn("rabi.company_id = raboe.company_id and rabi.rank_audit_batch_id = raboe.result_audit_batch_id")
                .join(ResultAuditBatchDo.class, "b").appendOn(" b.id = raboe.result_audit_batch_id and b.company_id = raboe.company_id")
                .clearSelect().setRsType(RejectResultItemCountPo.class)
                .select("sum(if(rabi.status=2,0,1)) finished, sum(if(rabi.status=1,0,1)) wait,count(raboe.id) total ")
                .whereEqReq("raboe.company_id", companyId.getId())
                .whereLike("raboe.owner_emp_id", ownerEmpId)
                .whereEqReq("b.cycleId", cycleId);
        if (CollUtil.isNotEmpty(taskIds)){
            qb.appendWhere("rabi.task_id IN" + StringTool.getInStr(taskIds));
        }
        return domainDao.findOne(qb);
    }

    public ListWrap<ResultAuditBatchItemDo> listRejectResultBatchItem(TenantId tenantId, List<String> batchIds) {
        ComQB qb = ComQB.build(ResultAuditBatchItemDo.class, "bi")
                .whereEqReq("bi.company_id", tenantId.getId())
                .whereInReq("bi.rank_audit_batch_id", batchIds);
        List<ResultAuditBatchItemDo> items = domainDao.listAll(qb);
        return new ListWrap<>(items).groupBy(item -> item.getRankAuditBatchId());
    }

    public PagedList<ResultAuditTaskUserPo> pagedCalibrateTaskUserOnEmp(CalibrateTaskUserQry qry) {
        ComQB qb = ComQB.build(PerfEvaluateTaskUserDo.class, "tu")
                .leftJoin(EmpEvalRuleDo.class, "eer").appendOn(" tu.company_id=eer.company_id and eer.emp_eval_id =tu.id and eer.is_deleted='false'")
                .leftJoin(CompanyMsgCenterDo.class, "m").appendOn("tu.company_id = m.company_id and tu.id = m.link_id")
                .join(ResultRankInstanceMemberDo.class, "mb").appendOn("tu.company_id = mb.company_id and tu.id = mb.task_user_id")
                .join(PerfEvaluateTaskBaseDo.class, "tb").appendOn("tb.company_id= tu.company_id and tb.id = tu.task_id")
                .leftJoin(PerfEvaluateTaskScoreResultDo.class, "owr").appendOn("owr.company_id = tu.company_id and tu.id = owr.task_user_id  and owr.audit_status is null and  owr.is_deleted='false'  ")
                .leftJoin(ResultAuditCacheDo.class, "rac").appendOn("rac.company_id = tu.company_id and rac.task_user_id = tu.id and rac.owner_emp_id=owr.scorer_id and  rac.is_deleted = 'false'")
                .setRsType(ResultAuditTaskUserPo.class).clearSelect()
                .select("ifnull(rac.step_id,tu.step_id) step_id ")
                .select("ifnull(rac.final_score,tu.final_score) final_score ")
                .select("ifnull(rac.evaluation_level,tu.evaluation_level) evaluation_level")
                .select("tu.emp_id,tu.avatar,tu.emp_name,tu.emp_org_name org_name,tu.id  task_user_id,tu.task_id,tu.at_org_name_path org_name_str")
                .select("tu.org_id,tu.original_evaluation_level,tu.original_final_score,tu.perf_coefficient,tu.original_perf_coefficient, tu.weight_of_ref,tu.score_of_ref")
                .select("owr.audit_status,owr.id audit_id,if(owr.audit_status = 'pass',1,2) calibrationStatus ")
                .select("IFNULL(tb.audit_result->'$.commentReq',0) comment_req")
                .select("IFNULL(tb.comment_conf->'$.scoreSummarySwitch',0) score_summary_switch")
                .select("eer.show_result_type")
                .appendWhere("tu.is_deleted='false'")
                .whereEqReq("tu.company_id", qry.getCompanyId())
                .whereIn("tu.task_id", qry.getTaskId())
                .whereLike("tu.emp_name", qry.getEmpName())
                .whereEq("mb.distribution_id", qry.getDistributionId())
                .groupBy("tu.id")
                .orderBy(qry.orderBy())
                .setPage(qry.getPageNo(), qry.getPageSize());
        if(CollUtil.isNotEmpty(qry.getAllLevels())){
            qb.appendWhere("ifnull(rac.evaluation_level,tu.evaluation_level ) in "+ StringTool.getInStrSql(qry.getAllLevels()));
        }
        if (CollUtil.isNotEmpty(qry.getOrgLevelQrys())) {
            List<String> sqls = new ArrayList<>();
            for (CalibrateTaskUserQry.OrgLevelQry orgLevelQry : qry.getOrgLevelQrys()) {
                StrBuilder sql = new StrBuilder();
                if (StrUtil.isNotBlank(orgLevelQry.asLikeOrgId())) {
                    sql.append(" tu.at_org_code_path like '%" + orgLevelQry.asLikeOrgId() + "%'");
                }
                if (CollUtil.isNotEmpty(orgLevelQry.getLevels())) {
                    sql.append( " AND ifnull(rac.evaluation_level,tu.evaluation_level ) in "+StringTool.getInStr(orgLevelQry.getLevels()));
                }
                sqls.add(sql.toString());
            }
            String joinSql = "(" + CollUtil.join(sqls, " or ", " ( ", ")") + ")";
            qb.appendWhere(joinSql);
        }
        if (CollUtil.isNotEmpty(qry.getNotEnterTaskUserIds())) {
            qb.whereIn("tu.id", qry.getNotEnterTaskUserIds());
        } else {
            qb.whereEq("owr.scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene());
            qb.whereEq("owr.scorer_id", qry.getOpEmpId());
            qb.whereEq("m.business_scene",MsgSceneEnum.TASK_RESULT_AUDIT.getType());
            qb.whereEq("m.emp_id",qry.getOpEmpId());
            qb.whereEq("m.handler_status",Boolean.FALSE.toString());
        }
        PagedList<ResultAuditTaskUserPo> userPos = domainDao.listPage(qb);
        if (userPos.isEmpty()) {
            return userPos;
        }
        List<String> taskUserIds = CollUtil.map(userPos, u -> u.getTaskUserId(), true);
        TenantId companyId = new TenantId(qry.getCompanyId());
        ListWrap<ResultAuditCacheDo> caches = listAutitResultCache(companyId, qry.getOpEmpId(), taskUserIds);
        ListWrap<HistoryScorePo> historyScoreMap = listMapHistoryScore(companyId, taskUserIds);
        ListWrap<AuditResultEvalType> typesMap = listTypeLevelResultPo(qry.getCompanyId(), taskUserIds);
        for (ResultAuditTaskUserPo userPo : userPos) {
            userPo.loadTypes(typesMap.groupGet(userPo.getTaskUserId()));
            userPo.loadHistoryScore(historyScoreMap.mapGet(userPo.getTaskUserId()));
            userPo.loadCache(caches.mapGet(userPo.getTaskUserId()));
        }
        return userPos;
    }



    public PagedList<ResultAuditTaskUserPo> pagedCalibrateTaskUserOnTask(CalibrateTaskUserQry qry) {
        ComQB qb = ComQB.build(PerfEvaluateTaskUserDo.class, "tu")
                .leftJoin(EmpEvalRuleDo.class, "eer").appendOn(" tu.company_id=eer.company_id and eer.emp_eval_id =tu.id and eer.is_deleted='false'")
                .leftJoin(CompanyMsgCenterDo.class, "m").appendOn("tu.company_id = m.company_id and tu.id = m.link_id")
                .join(PerfEvaluateTaskBaseDo.class, "tb").appendOn("tb.company_id= tu.company_id and tb.id = tu.task_id")
                .leftJoin(PerfEvaluateTaskScoreResultDo.class, "owr").appendOn("owr.company_id = tu.company_id and tu.id = owr.task_user_id  and owr.audit_status is null and  owr.is_deleted='false'  ")
                .leftJoin(ResultAuditCacheDo.class, "rac").appendOn("rac.company_id = tu.company_id and rac.task_user_id = tu.id and rac.owner_emp_id=owr.scorer_id and  rac.is_deleted = 'false'")
                .setRsType(ResultAuditTaskUserPo.class).clearSelect()
                .select("ifnull(rac.step_id,tu.step_id) step_id ")
                .select("ifnull(rac.final_score,tu.final_score) final_score ")
                .select("ifnull(rac.evaluation_level,tu.evaluation_level) evaluation_level")
                .select("tu.emp_id,tu.avatar,tu.emp_name,tu.emp_org_name org_name,tu.id  task_user_id,tu.task_id,tu.at_org_name_path org_name_str")
                .select("tu.org_id,tu.original_evaluation_level,tu.original_final_score,tu.perf_coefficient,tu.original_perf_coefficient, tu.weight_of_ref,tu.score_of_ref")
                .select("owr.audit_status,owr.id audit_id,if(owr.audit_status = 'pass',1,2) calibrationStatus ")
                .select("IFNULL(tb.audit_result->'$.commentReq',0) comment_req")
                .select("IFNULL(tb.comment_conf->'$.scoreSummarySwitch',0) score_summary_switch")
                .select("eer.show_result_type")
                .appendWhere("tu.is_deleted='false'")
                .whereEqReq("tu.company_id", qry.getCompanyId())
                .whereIn("tu.task_id", qry.getTaskId())
                .whereLike("tu.emp_name", qry.getEmpName())
                .whereEq("tb.score_rule_snap_id", qry.getDistributionId())
                .groupBy("tu.id")
                .orderBy(qry.orderBy())
                .setPage(qry.getPageNo(), qry.getPageSize());
        if(CollUtil.isNotEmpty(qry.getAllLevels())){
            qb.appendWhere("ifnull(rac.evaluation_level,tu.evaluation_level ) in "+ StringTool.getInStrSql(qry.getAllLevels()));
        }
        if (CollUtil.isNotEmpty(qry.getOrgLevelQrys())) {
            List<String> sqls = new ArrayList<>();
            for (CalibrateTaskUserQry.OrgLevelQry orgLevelQry : qry.getOrgLevelQrys()) {
                StrBuilder sql = new StrBuilder();
                if (StrUtil.isNotBlank(orgLevelQry.asLikeOrgId())) {
                    sql.append(" tu.at_org_code_path like '%" + orgLevelQry.asLikeOrgId() + "%'");
                }
                if (CollUtil.isNotEmpty(orgLevelQry.getLevels())) {
                    sql.append( " AND ifnull(rac.evaluation_level,tu.evaluation_level ) in "+StringTool.getInStr(orgLevelQry.getLevels()));
                }
                sqls.add(sql.toString());
            }
            String joinSql = "(" + CollUtil.join(sqls, " or ", " ( ", ")") + ")";
            qb.appendWhere(joinSql);
        }
        if (CollUtil.isNotEmpty(qry.getNotEnterTaskUserIds())) {
            qb.whereIn("tu.id", qry.getNotEnterTaskUserIds());
        } else {
            qb.whereEq("owr.scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene());
            qb.whereEq("owr.scorer_id", qry.getOpEmpId());
            qb.whereEq("m.business_scene",MsgSceneEnum.TASK_RESULT_AUDIT.getType());
            qb.whereEq("m.emp_id",qry.getOpEmpId());
            qb.whereEq("m.handler_status",Boolean.FALSE.toString());
        }
        PagedList<ResultAuditTaskUserPo> userPos = domainDao.listPage(qb);
        if (userPos.isEmpty()) {
            return userPos;
        }
        List<String> taskUserIds = CollUtil.map(userPos, u -> u.getTaskUserId(), true);
        TenantId companyId = new TenantId(qry.getCompanyId());
        ListWrap<ResultAuditCacheDo> caches = listAutitResultCache(companyId, qry.getOpEmpId(), taskUserIds);
        ListWrap<HistoryScorePo> historyScoreMap = listMapHistoryScore(companyId, taskUserIds);
        ListWrap<AuditResultEvalType> typesMap = listTypeLevelResultPo(qry.getCompanyId(), taskUserIds);
        for (ResultAuditTaskUserPo userPo : userPos) {
            userPo.loadTypes(typesMap.groupGet(userPo.getTaskUserId()));
            userPo.loadHistoryScore(historyScoreMap.mapGet(userPo.getTaskUserId()));
            userPo.loadCache(caches.mapGet(userPo.getTaskUserId()));
        }
        return userPos;
    }

    public ListWrap<ResultAuditCacheDo> listAutitResultCache(TenantId companyId, String ownerEmpId, Collection<String> taskUserIds) {
        if (CollUtil.isEmpty(taskUserIds)) {
            return new ListWrap<ResultAuditCacheDo>().asMap(cacheDo -> cacheDo.getTaskUserId());
        }
        ComQB comQB = ComQB.build(ResultAuditCacheDo.class, "rac")
                .appendWhere("rac.is_deleted='false'")
                .whereEq("companyId", companyId.getId())
                .whereEqReq("ownerEmpId", ownerEmpId)
                .whereInReq("taskUserId", taskUserIds);
        List<ResultAuditCacheDo> datas = domainDao.listAll(comQB);
        return new ListWrap<>(datas).asMap(rs -> rs.getTaskUserId());
    }

    public ListWrap<RejectResultItemReasonPo> listRejectResultItemReason(TenantId companyId, String ownerEmpId, Collection<String> taskUserIds) {
        ComQB comQB = ComQB.build(ResultAuditBatchItemDo.class, "rabi")
                .join(ResultAuditBatchOwnerEmpDo.class, "raboe").appendOn("rabi.company_id = raboe.company_id and rabi.rank_audit_batch_id = raboe.result_audit_batch_id")
                .join(ResultAuditBatchDo.class, "b").appendOn("b.company_id = raboe.company_id and b.id = raboe.result_audit_batch_id")
                .clearSelect().setRsType(RejectResultItemReasonPo.class).select(" b.reject_reason,rabi.task_user_id ")
                .appendWhere("rabi.is_deleted='false'")
                .appendWhere("rabi.status= 1 ")
                .whereEq("raboe.companyId", companyId.getId())
                .whereEqReq("raboe.owner_emp_id", ownerEmpId)
                .whereInReq("rabi.taskUserId", taskUserIds);
        List<RejectResultItemReasonPo> datas = domainDao.listAll(comQB);
        return new ListWrap<>(datas).asMap(rs -> rs.getTaskUserId());
    }

    public ListWrap<HistoryScorePo> listMapHistoryScore(TenantId companyId, Collection<String> taskUserIds) {
        ComQB scorerBuilder = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .clearSelect().select("company_id, task_id, emp_id,approval_order,task_user_id, score,score_level,perf_coefficient")
                .setRsType(PerfEvaluateTaskScoreResultPo.class)
                .whereEq("company_id", companyId.getId())
                .whereEq("scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene())
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereNotNull("score")
                .appendWhere("audit_status = 'pass'")
                .orderBy("approval_order desc");
        ComQB scoreQb = ComQB.build().fromQ(scorerBuilder, "s").clearSelect().setRsType(HistoryScorePo.class)
                .select("task_user_id")
                .select("CONCAT('[', GROUP_CONCAT(if(score is null ,null,JSON_OBJECT('score', score, 'level', score_level,'coef', perf_coefficient)) ORDER BY approval_order desc ),']' ) as history_score")
                .whereInReq("task_user_id", taskUserIds)
                .groupBy("s.task_user_id");
        List<HistoryScorePo> datas = domainDao.listAll(scoreQb);
        return new ListWrap<>(datas).asMap(rs -> rs.getTaskUserId());
    }

    public boolean isCalibrateOplock(String companyId, List<String> taskIds) {
        List<String> beforeStatus = TalentStatus.beforeStatus(TalentStatus.RESULTS_AUDITING);
        beforeStatus.remove("drawUpIng");
        ComQB countQ = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(ResultRankInstanceMemberDo.class, "mb").appendOn("u.company_id = mb.company_id and u.id = mb.task_user_id")
                .join(ResultRankInstanceDo.class, "rri").appendOn("mb.company_id = rri.company_id and mb.distribution_id = rri.id")
                .clearSelect().select(" count(1)  cnt").setRsType(Integer.class)
                .whereEq("u.company_id", companyId)
                .whereInReq("u.taskStatus", beforeStatus)
                .appendWhere("u.is_deleted='false'")
                .whereIn("u.task_id", taskIds)
                .appendWhere("rri.level_def_type = 2");
        Integer beforResultsAuditing = domainDao.findOne(countQ);
        return beforResultsAuditing > 0;
    }

    public void batchUpdateEvalUserReviewer(TenantId companyId, List<String> taskUserIds, List<KpiEmp> reviewers) {
        UpdateBuilder up = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                .set("reviewersJson", JSONObject.toJSONString(reviewers))
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("id", taskUserIds);
        domainDao.update(up);
    }

    public String getAuditCycleVersion(String companyId, String taskUserId) {
        final ComQB comQB = ComQB.build(CycleDo.class, "c")
                .join(PerfEvaluateTaskUserDo.class, "u").appendOn("u.company_id = c.company_id and u.cycle_id = c.id")
                .join(TenantSysConfDo.class, "d").appendOn("d.company_id = c.company_id")
                .clearSelect().select("count(1) cnt").setRsType(Integer.class)
                .whereEq("d.conf_code", TenantSysConf.result_rank_open_20231213)
                .appendWhere("d.open = 1")
                .whereEq("u.id", taskUserId)
                .whereEq("c.companyId", companyId)
                .whereBig("c.createdTime", "2024-02-28 00:00:00");
        Integer cnt = domainDao.findOne(comQB);
        if (cnt == null) {
            return "v1";
        }
        return cnt > 0 ? "v2" : "v1";
    }

    public Map<String, ResultAuditRecordPo> listMyResultAuditRecordAsMap(String companyId, List<String> taskUserIds, String scorerId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "r")
                .leftJoin(EmployeeBaseInfoDo.class, "e")
                .appendOn(" r.company_id = e.company_id and r.scorer_id = e.employee_id ")
                .clearSelect().select(" e.name as scorer,e.avatar, r.score,r.score_level,r.score_comment,r.updated_time," +
                        "ifnull(r.calibration_type,1) as calibration_type,r.operate_reason,r.perf_coefficient,r.task_user_id")
                .setRsType(ResultAuditRecordPo.class)
                .whereEqReq("r.company_id", companyId)
                .whereInReq("r.task_user_id", taskUserIds)
                .whereEqReq("r.audit_status", "pass")
                .whereEq("r.scorer_id", scorerId)
                .whereEqReq("r.is_deleted", "false")
                .whereEqReq("r.scorer_type", "final_result_audit")
                //如果校准设置了直属主管、直属主管中存在多个人时校准记录只能查出操作了校准的具体人
                .appendWhere("( r.score is not null  or r.score_level is not null )")
                .orderByAsc(" r.approval_order ");
        List<ResultAuditRecordPo> recordPos = domainDao.listAll(comQB);
        if (CollUtil.isEmpty(recordPos)) {
            return new HashMap<>();
        }
        Map<String, ResultAuditRecordPo> recordPoMap = CollUtil.toMap(recordPos, new HashMap<>(), r -> r.getTaskUserId());
        return recordPoMap;
    }


    public ListWrap<IndexCalibration> listItemScoreWarp(String companyId,List<String> taskUserIds) {
        if (CollUtil.isEmpty(taskUserIds)) {
            return new ListWrap<>();
        }
        ComQB comQB = ComQB.build(PerfEvaluateTaskKpiDo.class, "tk")
                .leftJoin(PerfEvaluateTaskScoreResultDo.class, "sr")
                .appendOn("tk.company_id = sr.company_id AND tk.task_user_id = sr.task_user_id AND tk.kpi_item_id = sr.kpi_item_id")
                .clearSelect()
                .select("sr.task_user_id,sr.kpi_type_id,sr.kpi_item_id,sr.score_level,sr.veto_flag,tk.kpi_item_name")
                .select("sum((IFNULL( sr.final_weight_score, 0 ) + ( IFNULL( sr.final_weight_plus_score, 0 ) - ( IFNULL( sr.final_weight_subtract_score, 0 ) ) ) ) ) AS score")
                .setRsType(IndexCalibration.class)
                .whereEqReq("sr.company_id", companyId)
                .whereInReq("sr.task_user_id", taskUserIds)
                .whereEqReq("sr.is_deleted", "false")
                .whereEqReq("tk.is_deleted", "false")
                .appendWhere("( sr.kpi_item_id is not null )")
                .groupBy("tk.task_user_id,tk.kpi_item_id");
        List<IndexCalibration> list = domainDao.listAll(comQB);
        if (CollUtil.isEmpty(list)) {
            return new ListWrap<>();
        }

        ComQB resultBuilder = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .clearSelect().select("task_user_id,index_calibration").setRsType(PerfEvaluateTaskScoreResultDo.class)
                .whereEqReq("company_id", companyId)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEq("scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene())
                .whereIn("task_user_id", taskUserIds)
                .appendWhere("(audit_status = 'pass')")
                .orderBy("approval_order DESC")
                .limit(0, 999999);
        ComQB resultQb = ComQB.build().fromQ(resultBuilder, "a")
                .whereNotNull("a.index_calibration")
                .groupBy("a.task_user_id");
        List<PerfEvaluateTaskScoreResultDo> resultDoList = domainDao.listAll(resultQb);
        if (CollUtil.isEmpty(resultDoList)) {
            return new ListWrap<IndexCalibration>(list).groupBy(IndexCalibration::getTaskUserId);
        }
        List<IndexCalibration> resultIndexs = resultDoList.stream().flatMap(r -> r.getIndexCalibration().stream()).collect(Collectors.toList());
        Map<String,IndexCalibration> indexMap = CollUtil.toMap(resultIndexs,new HashMap<>(),i -> i.getTaskUserId() + "-" + i.getKpiItemId());
        for (IndexCalibration obj : list) {
            IndexCalibration index = indexMap.get(obj.getTaskUserId() + "-" + obj.getKpiItemId());
            if (Objects.nonNull(index)) {
                obj = index;
            }
        }
        return new ListWrap<IndexCalibration>(list).groupBy(IndexCalibration::getTaskUserId);
    }
}
