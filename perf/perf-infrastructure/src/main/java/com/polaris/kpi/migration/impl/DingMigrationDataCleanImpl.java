package com.polaris.kpi.migration.impl;

import cn.com.polaris.kpi.eval.EmpRefOrg;
import com.alibaba.fastjson.JSONObject;
import com.polaris.acl.dept.domain.role.Role;
import com.polaris.acl.dept.pojo.CompanyDo;
import com.polaris.acl.dept.pojo.EmpOrganizationDo;
import com.polaris.acl.dept.pojo.EmployeeBaseInfoDo;
import com.polaris.acl.dept.pojo.role.RoleRefEmpDo;
import com.polaris.kpi.migration.entity.DingMigrationTask;
import com.polaris.kpi.migration.pojo.DingMigrationTaskDo;
import com.polaris.kpi.migration.repo.IDingMigrationDataClean;
import com.polaris.kpi.org.infr.company.dao.CompanyDaoImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.DeleteBuilder;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.common.data.ToDataBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * 数据清洗器实现
 * 负责清洗和转换迁移数据
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DingMigrationDataCleanImpl implements IDingMigrationDataClean {

    @Autowired
    private CompanyDaoImpl companyDao;
    @Autowired
    private DomainDaoImpl domainDao;


    public void setDomainDao(DomainDaoImpl domainDao) {
        this.domainDao = domainDao;
    }

    public void setCompanyDao(CompanyDaoImpl companyDao) {
        this.companyDao = companyDao;
    }
    @Override
    public void migrationDataClean(DingMigrationTask task) {
        if (Objects.isNull(task)){
            log.error("migrationDataClean.DingMigrationTask newCompany is null");
            return;
        }

        String fromCompanyId = task.getCompanyId();
        String dingCorpId = task.getDingCorpId();

        log.info("开始数据清洗:fromCompanyId:{}, dingCorpId={}",fromCompanyId, dingCorpId);
        // 1. 查询迁移后旧企业数据
        CompanyDo oldCompany = companyDao.findByDingCorpId(dingCorpId);
//        if (Objects.isNull(oldCompany)) {
//            log.error("查询迁移后旧企业数据失败: dingCorpId={}", dingCorpId);
//            //需还原新企业,重新新增回去
//            saveMigrationCompany(task);
//            return;
//        }
        if (Objects.nonNull(oldCompany)) {
            oldCompany.setDingAgentId(task.getDingAgentId());
            oldCompany.setDingSpaceId(task.getDingSpaceId());
            companyDao.updateDingAgentId(oldCompany.getId(), task.getDingAgentId(),task.getDingSpaceId());
        }

        //删除员工表
        DeleteBuilder delEmp = DeleteBuilder.build(EmployeeBaseInfoDo.class)
                .whereEqReq("company_id",fromCompanyId);
        //删除部门表
        DeleteBuilder delOrg = DeleteBuilder.build(EmpOrganizationDo.class)
                .whereEqReq("company_id", fromCompanyId);
        //删除员工与部门关系表
        DeleteBuilder delRefEmpOrg = DeleteBuilder.build(EmpRefOrg.class)
                .whereEqReq("company_id", fromCompanyId);
        //删除角色表
        DeleteBuilder delRole = DeleteBuilder.build(Role.class)
                .whereEqReq("company_id", fromCompanyId);
        //删除员工与角色关系表
        DeleteBuilder delRoleRefEmp = DeleteBuilder.build(RoleRefEmpDo.class)
                .whereEqReq("company_id", fromCompanyId);
        domainDao.delete(delEmp);
        domainDao.delete(delOrg);
        domainDao.delete(delRefEmpOrg);
        domainDao.delete(delRole);
        domainDao.delete(delRoleRefEmp);
    }


    @Override
    public void migrationDataCollback(DingMigrationTask newCompany, List<String> allTableNames) {
    //    String dingCorpId = newCompany.getDingCorpId();
        // 1. 查询迁移后旧企业数据
 //       CompanyDo oldCompany = companyDao.findByDingCorpId(dingCorpId);
//        if (Objects.nonNull(oldCompany)) {
//            // 2.删除原迁移过来的绩效业务数据
//            deleteOldData(oldCompany.getId(), allTableNames);
//        }
        //需还原新企业
        saveMigrationCompany(newCompany);

        //删除迁移任务
//        DeleteBuilder del = DeleteBuilder.build(DingMigrationTaskDo.class)
//                .whereEqReq("ding_corp_id", dingCorpId);
  //      domainDao.delete(del);
    }


    @Override
    public void migrationDataCollbackTest(String oldCompanyId, List<String> allTableNames) {
        if (Objects.isNull(oldCompanyId)){
            return;
        }
        // 2.删除原迁移过来的绩效业务数据
        deleteOldData(oldCompanyId, allTableNames);
    }

    @Override
    public void save(DingMigrationTask newCompany) {
        newCompany.setId(UUID.randomUUID().toString());
        DingMigrationTaskDo data = new ToDataBuilder<>(newCompany, DingMigrationTaskDo.class).data();
        domainDao.save(data);
    }
    @Override
    public void saveMigrationCompany(DingMigrationTask task) {
        String migrationCompanyJson = task.getMigrationCompanyJson();//migrationCompanyJson转对象CompanyDo
        CompanyDo companyDo =  JSONObject.parseObject(migrationCompanyJson, CompanyDo.class);
        domainDao.save(companyDo);
    }

   @Override
    public void update(DingMigrationTask task) {
        final UpdateBuilder up = UpdateBuilder.build(DingMigrationTaskDo.class)
                .set("status", task.getStatus())
                .whereEqReq("ding_corp_id",task.getDingCorpId());
        domainDao.update(up);
    }

    @Override
    public DingMigrationTask getDingMigrationTask(String dingCorpId) {
        final ComQB comQB = ComQB.build(DingMigrationTaskDo.class)
                .whereEqReq("ding_corp_id", dingCorpId).orderByDesc("created_time").limit(0,1);
        return domainDao.findDomain(comQB, DingMigrationTask.class);
    }

    @Override
    public DingMigrationTask getDingMigrationTaskByCompanyId(String companyId) {
        final ComQB comQB = ComQB.build(DingMigrationTaskDo.class)
                .whereEqReq("company_id", companyId).orderByDesc("created_time").limit(0,1);
        return domainDao.findDomain(comQB, DingMigrationTask.class);
    }

    private void deleteOldData(String companyId, List<String> allTableNames) {
        for (String tableName : allTableNames) {
            //计算耗时
            long start = System.currentTimeMillis();
            String sql="";
            if ("company".equals(tableName)){
                sql = "DELETE FROM " + tableName + " WHERE id = '" + companyId + "'";
            }else {
                 sql = "DELETE FROM " + tableName + " WHERE company_id = '" + companyId + "'";
            }
            log.info("执行删除的sql:{}", sql);
            domainDao.executeBySql(sql);
            log.info("删除表{}耗时{}ms", tableName, System.currentTimeMillis() - start);
        }
    }
}