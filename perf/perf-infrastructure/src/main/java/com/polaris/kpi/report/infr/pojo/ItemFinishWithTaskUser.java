package com.polaris.kpi.report.infr.pojo;


import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/7/22 14:28
 */
@Setter
@Getter
@NoArgsConstructor
public class ItemFinishWithTaskUser {

    private String itemId;
    private String taskUserId;
    private String finishValueSum;
    private String targetValueSum;
    private BigDecimal finishRate;

    public void calcFinishRate(){


    }
}
