package com.polaris.kpi.report.infr.pojo;


import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/22 14:31
 */
@Setter
@Getter
@NoArgsConstructor
public class ItemAvgFinishRateWithTaskUser {

    private String taskUserId;
    private String avgFinishRate;
    private List<BigDecimal> finishRates;

    public void calcAvgFinishRate() {
        BigDecimal sum = BigDecimal.ZERO;
        for (BigDecimal finishRate : finishRates) {
            sum = sum.add(finishRate);
        }
        avgFinishRate = sum.divide(new BigDecimal(finishRates.size()), 2, RoundingMode.HALF_UP).toString();
    }

}
