package com.polaris.kpi.eval.infr.companyItem.builder;

import cn.com.polaris.kpi.eval.ItemCustomFieldValue;
import com.perf.www.common.utils.bean.Convert;
import com.polaris.kpi.ind.infr.ppojo.CompanyKpiItemVO;
import com.polaris.kpi.eval.infr.companyItem.ppojo.CompanyItemUsedFieldDo;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
public class CompanyItemDataBuilder {

    private CompanyKpiItemVO companyKpiItemVO;

    List<CompanyItemUsedFieldDo> usedFieldDos = new ArrayList<>();

    public CompanyItemDataBuilder(CompanyKpiItemVO companyKpiItemVO) {
        this.companyKpiItemVO = companyKpiItemVO;
    }

    public void buildData(String id, String empId) {
        /**处理自定义字段数据*/
        List<ItemCustomFieldValue> fields = companyKpiItemVO.getFieldValueList();
        if (CollectionUtils.isNotEmpty(fields)) {
            fields.forEach(item -> {
                CompanyItemUsedFieldDo fieldDo = Convert.convertOnlyMatch(item, CompanyItemUsedFieldDo.class);
                if (item.getType() == null) {
                    fieldDo.setType(1);
                }
                if (item.getIsDeleted() == null) {
                    fieldDo.setIsDeleted("false");
                }
                fieldDo.setKpiItemId(id);
                fieldDo.setCompanyId(companyKpiItemVO.getCompanyId());
                fieldDo.setName(item.getFieldName());
                fieldDo.setValue(item.getFieldValue());
                fieldDo.setStatus(item.getFieldStatus());
                fieldDo.setFieldId(item.getId());
                fieldDo.setReq(item.getIsReq());
                fieldDo.setAdminType(item.getAdminType() == null ? 0 :item.getAdminType());
                fieldDo.setCreatedUser(empId);
                fieldDo.setCreatedTime(new Date());
                fieldDo.setIsDeleted("false");
                this.usedFieldDos.add(fieldDo);
            });
        }
    }

}
