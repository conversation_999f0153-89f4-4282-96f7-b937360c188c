package com.polaris.kpi.eval.infr.statics.ppojo;

import com.polaris.kpi.common.infr.BaseWithCompanyIdData;
import com.polaris.kpi.eval.domain.cycle.entity.PerfStatisticRule;
import com.polaris.kpi.eval.domain.cycle.type.StatisticRuleItem;
import com.polaris.kpi.eval.domain.statics.entity.HistoryCycle;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.ibatis.annotations.JsonAryColumn;
import org.apache.ibatis.annotations.JsonColumn;
import org.apache.ibatis.annotations.Table;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/23 23:13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Table("consecutive_emp_detail")
public class ConsecutiveEmpDetailDo extends BaseWithCompanyIdData {

    private String id;
    private String cycleId;
    private String cycleTime;
    private String taskId;
    private String taskName;
    private String taskUserId;
    private String empId;
    private String empName;
    private String avatar;
    private String orgId;
    private String orgName;
    private String atOrgCodePath;
    private String atOrgNamePath;
    private String finalScore;
    private String evaluationLevel;
    private String perfCoefficient;
    private String showResultType;
    /**
     * 规则ID 默认规则为null
     */
    private String hitRuleId;

    private String ruleName;

    private String ruleDesc;

    @JsonColumn
    private PerfStatisticRule perfStatisticRule;

    @JsonAryColumn(StatisticRuleItem.class)
    private List<StatisticRuleItem> statisticRuleItems;

    /**
     * 统计时长
     */
    private Integer statisticDuration;

    /**
     * 统计考核周期类型
     */
    private String cycleType;

    /**
     * 连续类型 1、连续绩优 2、连续绩差
     */
    private Integer consecutiveType;
    /**
     * 历史周期数据
     */
    private List<HistoryCycle> historyCycles;

}
