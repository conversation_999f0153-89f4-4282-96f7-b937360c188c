package com.polaris.kpi.common.infr.dao;

import com.polaris.org.domain.req.entity.RequestModel;
import com.polaris.org.domain.req.reps.RequestRepo;
import com.polaris.sdk.type.TenantId;
import org.springframework.stereotype.Component;

/**
 * 这会儿还没这个表
 */
@Component
public class RequestDao implements RequestRepo {

    @Override
    public RequestModel getRequest(TenantId tenantId, String s) {
        return null;
    }

    @Override
    public void saveRequest(RequestModel requestModel) {

    }

    @Override
    public String nextId() {
        return null;
    }
}
