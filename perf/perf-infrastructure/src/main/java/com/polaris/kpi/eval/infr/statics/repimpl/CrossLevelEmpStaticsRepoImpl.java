package com.polaris.kpi.eval.infr.statics.repimpl;

import com.perf.www.common.utils.date.DateTimeUtils;
import com.polaris.kpi.eval.domain.statics.entity.CrossLevelEmpStatics;
import com.polaris.kpi.eval.domain.statics.repo.CrossLevelEmpStaticsRepo;
import com.polaris.kpi.eval.infr.statics.ppojo.CrossLevelCountDo;
import com.polaris.kpi.eval.infr.statics.ppojo.CrossLevelEmpStaticsDo;
import com.polaris.kpi.report.domain.entity.CrossLevelCount;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.common.data.ToDataBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/1/20 17:50
 */
@Component
public class CrossLevelEmpStaticsRepoImpl implements CrossLevelEmpStaticsRepo {

    @Resource
    private DomainDaoImpl domainDao;

    public static final String cross_level_emp_statics = "cross_level_emp_statics";
    public static final String cross_level_count = "cross_level_count";

    @Override
    public void batchDelete(String companyId ,String cycleId, Integer crossLevelType) {

        UpdateBuilder delete = UpdateBuilder.build(CrossLevelEmpStaticsDo.class)
                .set("is_deleted", "true")
                .set("updated_time", DateTimeUtils.now2StrDateTime())
                .whereEqReq("cycle_id", cycleId)
                .whereEqReq("company_id", companyId);

        if (crossLevelType == 1){
            delete.whereEqReq("cross_level_type", 1);
        }else if (crossLevelType == 2){
            delete.whereEqReq("cross_level_type", 2);
        }

        domainDao.update(delete);
    }

    @Override
    public void batchSave(List<CrossLevelEmpStatics> crossLevelEmps) {

        for (CrossLevelEmpStatics crossLevelEmp : crossLevelEmps) {
            crossLevelEmp.initOnNew(domainDao.nextLongAsStr(cross_level_emp_statics));
        }
        domainDao.saveBatch(crossLevelEmps);
    }

    @Override
    public void updateCrossCount(CrossLevelCount crossLevelCount) {
        if (crossLevelCount.isNew()){
            crossLevelCount.initOnNew(domainDao.nextLongAsStr(cross_level_count));
            CrossLevelCountDo data = new ToDataBuilder<>(crossLevelCount, CrossLevelCountDo.class).data();
            domainDao.save(data);
        }else {
            UpdateBuilder update = UpdateBuilder.build(CrossLevelCountDo.class)
                    .set("up_counted_time", crossLevelCount.getUpCountedTime())
                    .set("up_count_state", crossLevelCount.getUpCountState())
                    .set("down_counted_time", crossLevelCount.getDownCountedTime())
                    .set("down_count_state", crossLevelCount.getDownCountState())
                    .set("updated_time", crossLevelCount.getUpdatedTime())
                    .set("updated_user", crossLevelCount.getUpdatedUser())
                    .whereEqReq("id", crossLevelCount.getId());
            domainDao.update(update);
        }
    }

}
