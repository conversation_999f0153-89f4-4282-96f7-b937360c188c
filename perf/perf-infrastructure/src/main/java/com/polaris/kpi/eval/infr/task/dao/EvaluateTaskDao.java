package com.polaris.kpi.eval.infr.task.dao;

import cn.com.polaris.kpi.EmpStaff;
import cn.com.polaris.kpi.ItemDecompose;
import cn.com.polaris.kpi.ObjItem;
import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.com.polaris.kpi.company.TenantSysConf;
import cn.com.polaris.kpi.eval.*;
import cn.com.polaris.kpi.temp.CycleStatusEnum;
import cn.com.polaris.kpi.temp.CycleType;
import cn.com.polaris.kpi.temp.PublicTypeEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.perf.www.common.constant.BusinessConstant;
import com.perf.www.common.em.CompanyItemDecomposeStatusEnum;
import com.perf.www.common.utils.string.StringTool;
import com.perf.www.domain.entity.company.EmpRefOrgModel;
import com.perf.www.domain.entity.company.RoleModel;
import com.perf.www.domain.entity.company.RoleRefEmpModel;
import com.perf.www.model.task.*;
import com.perf.www.vo.okr.OKRGetTaskReturnVO;
import com.perf.www.vo.okr.OKRTaskQueryVO;
import com.perf.www.vo.task.EvaluationStaffVO;
import com.perf.www.vo.task.PerfEvaluateTaskBaseVO;
import com.perf.www.vo.task.audit.PerfEvaluateTaskAuditVO;
import com.perf.www.vo.task.item.PerfEvaluateTaskItemDynamicVO;
import com.perf.www.vo.task.query.EvaluationTaskUserQueryVO;
import com.polaris.kpi.cache.domain.entity.CompanyCacheInfo;
import com.polaris.kpi.common.KpiI18NException;
import com.polaris.kpi.eval.domain.task.entity.EvalItem;
import com.polaris.kpi.eval.domain.task.entity.EvalItemField;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.calibrated.RankRuleScoreRangeSnap;
import com.polaris.kpi.eval.domain.task.entity.calibrated.ResultRankInstance;
import com.polaris.kpi.eval.domain.task.entity.empeval.BindEvalResult;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalRule;
import com.polaris.kpi.eval.domain.task.entity.empeval.EvalBind;
import com.polaris.kpi.eval.domain.task.entity.empeval.MainEval;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.AffirmTaskConf;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.MutualNodeConf;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.S3RaterBaseConf;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.S3SuperRaterConf;
import com.polaris.kpi.eval.domain.task.entity.grade.BaseScoreRange;
import com.polaris.kpi.eval.domain.task.entity.grade.MatchedStep;
import com.polaris.kpi.eval.domain.task.ext.IOkrAclSvc;
import com.polaris.kpi.eval.domain.task.type.EmpEvalScorerTransferStatusEnum;
import com.polaris.kpi.eval.domain.temp.entity.ExamGroup;
import com.polaris.kpi.eval.domain.temp.entity.PerfTemplEvaluate;
import com.polaris.kpi.eval.domain.temp.entity.PerfTemplEvaluateAffirm;
import com.polaris.kpi.eval.infr.task.ppojo.*;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.*;
import com.polaris.kpi.eval.infr.task.ppojo.open.OpenTalentEvalPo;
import com.polaris.kpi.eval.infr.task.ppojo.open.OpenTaskBasePo;
import com.polaris.kpi.eval.infr.task.ppojo.report.EmpEvalScorerNodePo;
import com.polaris.kpi.eval.infr.task.ppojo.report.ReportEvalUserPo;
import com.polaris.kpi.eval.infr.task.ppojo.report.OkrTargetNameWithKpiItemIdPo;
import com.polaris.kpi.eval.infr.task.ppojo.report.ReportUserTaskExcelPo;
import com.polaris.kpi.eval.infr.task.query.*;
import com.polaris.kpi.eval.infr.task.query.empeval.EmpEvalAtTaskQuery;
import com.polaris.kpi.eval.infr.task.query.empeval.EmpEvalAtTaskQuery2;
import com.polaris.kpi.eval.infr.task.query.empeval.EmpEvalAtTaskQuery3;
import com.polaris.kpi.eval.infr.task.query.empeval.TestBindEvalQuery;
import com.polaris.kpi.eval.infr.task.query.report.TaskReportQry;
import com.polaris.kpi.org.domain.dept.type.TaskId;
import com.polaris.kpi.org.infr.emp.pojo.EmpOrganizationDo;
import com.polaris.kpi.org.infr.emp.pojo.EmpRefOrgDo;
import com.polaris.kpi.org.infr.emp.pojo.EmployeeBaseInfoDo;
import com.polaris.kpi.org.infr.emp.pojo.RoleRefEmpDo;
import com.polaris.sdk.type.AuditEnum;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.DeleteBuilder;
import org.lufei.ibatis.builder.NativeSQLBuilder;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.lufei.ibatis.mapper.PagedList;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lufei
 * @date 2022/2/9 10:10 上午
 */
@Component
@Slf4j
public class EvaluateTaskDao {
    @Resource
    private DomainDaoImpl autoBaseDao;
    @Autowired
    private EvalKpiDao evalKpiDao;
    @Autowired
    private IOkrAclSvc evalOkrAcl;
    @Value("${down.url}")
    private String downUrl;

    public void setDomainDao(DomainDaoImpl domainDao) {
        this.autoBaseDao = domainDao;
    }

    //导出汇总表 --不带指标
    public PagedList<ReportEvalUserPo> pagedReportTaskEvalUser(TenantSysConf conf, TaskReportQry query) {
        try {
            log.info("pagedReportTaskEvalUser.params:conf:{},query:{}", JSONUtil.toJsonStr(conf), JSONUtil.toJsonStr(query));
            final ComQB qb = buildTaskUserComQb(query);
            final PagedList<ReportEvalUserPo> excelVOS = autoBaseDao.listPage(qb);
            if (CollectionUtils.isEmpty(excelVOS.getData())) {
                return excelVOS;
            }
            List<String> taskUserIds = excelVOS.getData().stream().map(ReportEvalUserPo::getTaskUserId).collect(Collectors.toList());
            // 组装评分人详情数据
            Map<String, String> map = buildNoItemScorerNodeDetails(conf, excelVOS.getData());
            // 组装关联绩效数据
            Map<String, String> refMap = new HashMap<>();
            if (query.getOpenRefPerformance()) {
                refMap = listRefEvalResult(query.getCompanyId(), taskUserIds);
            }
            //自定义字段（北京联云天下科技有限公司 -- 1073152）(东软载波 -- 1046531) (北京爱知之星科技股份有限公司 -- 1151045) (成都四季常青电商 -- 1174164)
            List<PerfEvaluateTaskScoreResultPo> pos = listAuditResultAuditByTaskIds(query.getCompanyId(), taskUserIds);
            Set<String> userIds = new HashSet<>();
            for (ReportEvalUserPo vo : excelVOS.getData()) {
                vo.builderNoContainItemRes(userIds, downUrl, pos, map, refMap);
                vo.updateOrgNames();
            }
            return excelVOS;
        } catch (Exception e) {
            log.error("Error occurred in pagedReportTaskEvalUser", e);
            throw new RuntimeException("Failed to process report pagedReportTaskEvalUser task eval user", e);
        }
    }

    //pagedTaskDetailV3 替换
    @Deprecated
    public PagedList<ReportUserTaskExcelPo> pagedTaskDetail(TenantSysConf conf, TaskReportQry query) {
        log.info("pagedTaskDetail.params:conf:{},query:{}", JSONUtil.toJsonStr(conf), JSONUtil.toJsonStr(query));
        final ComQB qb = buildTaskDetailComQb(query);
        final PagedList<ReportUserTaskExcelPo> excelVOS = autoBaseDao.listPage(qb);
        if (CollectionUtils.isEmpty(excelVOS.getData())) {
            return excelVOS;
        }
        List<String> taskUserIds = excelVOS.getData().stream().map(ReportUserTaskExcelPo::getTaskUserId).collect(Collectors.toList());

        if (exportIncludeOkrTargetName(query)){ //如果开启了okr目标展示，在这里增加okr目标的查询
            List<String> kpiItemIds = excelVOS.stream().map(ReportUserTaskExcelPo::getKpiItemId).collect(Collectors.toList());
            List<OkrTargetNameWithKpiItemIdPo> okrs = listTargetNameWithKpiItemIds(query.getCompanyId(),kpiItemIds);
            ListWrap<OkrTargetNameWithKpiItemIdPo> okrWrap = new ListWrap<>(okrs).asMap(OkrTargetNameWithKpiItemIdPo::getKpiItemId);
            Map<String, List<ReportUserTaskExcelPo>> collect = excelVOS.getData().stream().filter(item -> item.getKpiItemId() != null) // 过滤掉getKpiItemId为null的元素
                    .collect(Collectors.groupingBy(ReportUserTaskExcelPo::getKpiItemId));
            //遍历map
            collect.forEach((kpiItemId, reportUserTaskExcelPos) -> {
                reportUserTaskExcelPos.forEach(reportUserTaskExcelPo -> {
                    if (okrWrap.contains(kpiItemId)){
                        String okrTargetName = okrWrap.mapGet(kpiItemId).getOkrTargetName();
                        String removedTags = removeHtmlTags(okrTargetName);
                        String removedEntities = removeHtmlEntities(removedTags);
                        reportUserTaskExcelPo.setOkrTargetName(removedEntities);
                    }
                });
            });
        }

        //数据组装
        // List<String> taskIds = excelVOS.getData().stream().map(ReportUserTaskExcelPo::getTaskId).collect(Collectors.toList());
        /**组装评分人详情数据*/
        Map<String, String> map = buildScorerDetails(conf, excelVOS.getData());
        /**组装关联绩效数据*/
        Map<String, String> refMap = new HashMap<>();
        if (query.getOpenRefPerformance()) {
            refMap = listRefEvalResult(query.getCompanyId(), taskUserIds);
        }
        //考核表
        Map<String, String> ruleMap = listRuleName(query.getCompanyId(), taskUserIds);
        //自定义字段（北京联云天下科技有限公司 -- 1073152）(东软载波 -- 1046531) (北京爱知之星科技股份有限公司 -- 1151045) (成都四季常青电商 -- 1174164)
        Map<String, List<PerfEvaluateItemUsedFieldDo>> listItemUsedField = listItemUsedField(query.getCompanyId(), taskUserIds);
        List<PerfEvaluateTaskScoreResultPo> pos = listResultByTaskIds(query.getCompanyId(), taskUserIds);
        //按指标校准数据
        List<PerfEvaluateTaskScoreResultDo> resultDoList = listTaskResultIndexCalibration(query.getCompanyId(), taskUserIds);
        Map<String, PerfEvaluateTaskScoreResultDo> calibrationMap = new HashMap<>();
        if (CollUtil.isNotEmpty(resultDoList)) {
            calibrationMap = CollUtil.toMap(resultDoList, new HashMap<>(), PerfEvaluateTaskScoreResultDo::getTaskUserId);
        }
        Map<String, Map<String, EvalKpi>> okrUpdatesMap = okrItemsMap(query.getCompanyId(), taskUserIds);
        Set<String> userIds = new HashSet<>();
        for (ReportUserTaskExcelPo vo : excelVOS.getData()) {
            vo.initClsColumn(query.getCusColumnFiledIds(), listItemUsedField);
            List<PerfEvaluateTaskItemDynamicVO> dynamics = vo.hasItemId() ? queryItemDynamic(vo.getTaskUserId(), vo.getKpiItemId()) : null;
            Map<String, EvalKpi> taskUserOkrUpMap = okrUpdatesMap.get(vo.getTaskUserId());
            EvalKpi tempKpi = MapUtil.isEmpty(taskUserOkrUpMap) ? null : taskUserOkrUpMap.get(vo.getKpiTypeId() + "_" + vo.getKpiItemId());
            vo.builderRes(conf, userIds, downUrl, pos, dynamics, map, refMap, ruleMap, calibrationMap.get(vo.getTaskUserId()), tempKpi);
        }
        return excelVOS;
    }

    //导出汇总表 -- 带指标
    public PagedList<ReportUserTaskExcelPo> pagedTaskDetailV3(TenantSysConf conf, TaskReportQry query) {
        try {
            log.info("pagedTaskDetailV3.params:conf:{},query:{}", JSONUtil.toJsonStr(conf), JSONUtil.toJsonStr(query));
            // 构建查询条件
            final ComQB qb = buildTaskDetailComQbV3(query);
            final PagedList<ReportUserTaskExcelPo> excelVOS = autoBaseDao.listPage(qb);
            if (CollUtil.isEmpty(excelVOS.getData())) {
                return excelVOS;
            }
            // 获取taskUserId列表
            List<String> taskUserIds = excelVOS.getData().stream().map(ReportUserTaskExcelPo::getTaskUserId).collect(Collectors.toList());
            //组装评分人详情数据
            Map<String, String> map = buildScorerNodeDetails(conf, excelVOS.getData());
            //组装关联绩效数据
            Map<String, String> refMap = new HashMap<>();
            if (query.getOpenRefPerformance()) {
                refMap = listRefEvalResult(query.getCompanyId(), taskUserIds);
            }
            //查询自定义字段 自定义字段（北京联云天下科技有限公司 -- 1073152）(东软载波 -- 1046531) (北京爱知之星科技股份有限公司 -- 1151045) (成都四季常青电商 -- 1174164)
            Map<String, List<PerfEvaluateItemUsedFieldDo>> listItemUsedField = this.listItemUsedField(query.getCompanyId(), taskUserIds);
            // 查询评分环节 KPI 数据
            ListWrap<EvalScorerNodeKpiItem> scorerKpiItemws = this.listEvalScoreNodeKpiItem(query.getCompanyId(), taskUserIds);
            //查询按人员校准数据
            List<PerfEvaluateTaskScoreResultPo> pos = this.listAuditResultAuditByTaskIds(query.getCompanyId(), taskUserIds);
            // 查询按指标校准数据
            Map<String, PerfEvaluateTaskScoreResultDo> calibrationMap = buildCalibrationMap(query.getCompanyId(), taskUserIds);
            // 查询 OKR 更新数据
            Map<String, Map<String, EvalKpi>> okrUpdatesMap = this.okrItemsMap(query.getCompanyId(), taskUserIds);
            // 查询完成值更新情况
            Map<String, List<PerfEvaluateTaskItemDynamicVO>> itemDynamicMap = buildItemDynamicMap(taskUserIds);
            // 初始化并构建每个 Excel 数据对象
            Set<String> userIds = new HashSet<>();
            for (ReportUserTaskExcelPo vo : excelVOS.getData()) {
                initAndBuildExcelVo(conf, vo, query, listItemUsedField, scorerKpiItemws, okrUpdatesMap, calibrationMap,
                        itemDynamicMap, map, refMap, pos, userIds);
            }
            return excelVOS;
        } catch (Exception e) {
            log.error("Error occurred in pagedTaskDetailV3", e);
            throw new RuntimeException("Failed to process report pagedTaskDetailV3 task eval user", e);
        }
    }

    // 初始化并构建每个 Excel 数据对象
    private void initAndBuildExcelVo(TenantSysConf conf,ReportUserTaskExcelPo vo, TaskReportQry query,
                                     Map<String, List<PerfEvaluateItemUsedFieldDo>> listItemUsedField,
                                     ListWrap<EvalScorerNodeKpiItem> scorerKpiItems,
                                     Map<String, Map<String, EvalKpi>> okrUpdatesMap,
                                     Map<String, PerfEvaluateTaskScoreResultDo> calibrationMap,
                                     Map<String, List<PerfEvaluateTaskItemDynamicVO>> itemDynamicMap,
                                     Map<String, String> scorerDetailsMap,
                                     Map<String, String> refMap,
                                     List<PerfEvaluateTaskScoreResultPo> auditResults,
                                     Set<String> userIds) {
        vo.initClsColumn(query.getCusColumnFiledIds(), listItemUsedField);

        List<PerfEvaluateTaskItemDynamicVO> dynamics = itemDynamicMap.getOrDefault(vo.getTaskUserId(), Collections.emptyList());
        List<EvalScorerNodeKpiItem> scorerKpiItemws = scorerKpiItems.groupGet(vo.getTaskUserId());
        Map<String, EvalKpi> taskUserOkrUpMap = okrUpdatesMap.getOrDefault(vo.getTaskUserId(), Collections.emptyMap());
        EvalKpi tempKpi = taskUserOkrUpMap.get(vo.getKpiTypeId() + "_" + vo.getKpiItemId());
        //构建数据
        vo.builderResV3(conf, userIds, downUrl, auditResults, dynamics, scorerDetailsMap, refMap, calibrationMap.get(vo.getTaskUserId()), tempKpi, scorerKpiItemws);
        vo.updateOrgNames();
    }
    // 构建完成值更新情况映射
    public Map<String, List<PerfEvaluateTaskItemDynamicVO>> buildItemDynamicMap(List<String> taskUserIds) {
        if (CollUtil.isEmpty(taskUserIds)) {
            return Collections.emptyMap();
        }
        List<PerfEvaluateTaskItemDynamicVO> itemDynamicPos = queryItemDynamicV3(taskUserIds);
        return itemDynamicPos.stream().collect(Collectors.groupingBy(PerfEvaluateTaskItemDynamicVO::getTaskUserId));
    }

    // 构建校准数据映射
    private Map<String, PerfEvaluateTaskScoreResultDo> buildCalibrationMap(String companyId,List<String> taskUserIds) {
        if (CollUtil.isEmpty(taskUserIds)) {
            return Collections.emptyMap();
        }
        List<PerfEvaluateTaskScoreResultDo> resultDoList = listTaskResultIndexCalibration(companyId, taskUserIds);
        return CollUtil.isNotEmpty(resultDoList) ? CollUtil.toMap(resultDoList, new HashMap<>(), PerfEvaluateTaskScoreResultDo::getTaskUserId) : Collections.emptyMap();
    }
    public static String removeHtmlTags(String html) {
        if (html == null || html.isEmpty()) {
            return html;
        }
        // 移除HTML标签的正则表达式
        String pattern = "<[^>]*>";
        return html.replaceAll(pattern, "");
    }

    public static String removeHtmlEntities(String html) {
        if (html == null || html.isEmpty()) {
            return html;
        }
        // 移除HTML实体的正则表达式
        String pattern = "&[^;]+;";
        return html.replaceAll(pattern, "");
    }

    private boolean exportIncludeOkrTargetName(TaskReportQry query) {
        //如果存在
        return query.getExportTitles() != null && query.getExportTitles().stream().anyMatch(t -> t.getFieldId().equals("okrTargetName"));
    }

    private List<OkrTargetNameWithKpiItemIdPo> listTargetNameWithKpiItemIds(String companyId, List<String> kpiItemIds) {

        ComQB qb = ComQB.build(EvalRefOkrDo.class)
                .clearSelect().select("target_name as okrTargetName", "task_kpi_id as kpiItemId")
                .setRsType(OkrTargetNameWithKpiItemIdPo.class)
                .whereEqReq("company_id", companyId)
                .whereInReq("task_kpi_id", kpiItemIds)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        return autoBaseDao.listAll(qb);
    }

    public Map<String, Map<String, EvalKpi>> okrItemsMap(String companyId, List<String> taskUserIds) {
        Map<String, Map<String, EvalKpi>> okrItemMap = new HashMap<>();
        //加载指标
        Map<String, List<EvalKpi>> kpiMap = evalKpiDao.listEmpEvalKpiItems(new TenantId(companyId), taskUserIds);
        if (MapUtil.isEmpty(kpiMap)) {
            return okrItemMap;
        }
        kpiMap.forEach((key, kpis) -> {
            List<EvalKpi> okrItems = okrItems(kpis);
            if (CollUtil.isEmpty(okrItems)) {
                return;
            }
            //指标更新记录
            evalOkrAcl.loadOkrIf(new TenantId(companyId), okrItems, okrItems.get(0).getEmpId());
            Map<String, EvalKpi> tempMap = new HashMap<>();
            for (EvalKpi kpi : okrItems) {
                tempMap.put(kpi.getKpiTypeId() + "_" + kpi.getKpiItemId(), kpi);
            }
            okrItemMap.put(key, tempMap);
        });
        return okrItemMap;
    }

    public List<EvalKpi> okrItems(List<EvalKpi> kpis) {
        if (kpis == null || CollUtil.isEmpty(kpis)) {
            return new ArrayList<>();
        }
        return kpis.stream().filter(EvalKpi::isOkrType).collect(Collectors.toList());
    }

    public Map<String, String> buildScorerDetails(TenantSysConf conf, List<ReportUserTaskExcelPo> excelVOS) {
        List<String> taskUserIds = excelVOS.stream().map(ReportUserTaskExcelPo::getTaskUserId).distinct().collect(Collectors.toList());
        List<PerfEvaluateTaskItemScoreRuleDo> scoreRuleDos = listTaskItemScoreRule(taskUserIds);
        List<EmpEvalRuleDo> ruleDos = listEmpEvalRule(taskUserIds);
        List<PerfEvaluateTaskScoreResultPo> resultPos = listTaskScoreResult(taskUserIds);
        List<PerfEvaluateTaskKpiDo> taskKpiDos = listTaskKpi(taskUserIds);
        ListWrap<PerfEvaluateTaskItemScoreRuleDo> scoreRuleMap = new ListWrap<>(scoreRuleDos).groupBy(PerfEvaluateTaskItemScoreRuleDo::getTaskUserId);
        ListWrap<PerfEvaluateTaskScoreResultPo> resultMap = new ListWrap<>(resultPos).asMap(obj -> obj.getTaskUserId() + "-" + obj.getScorerType());
        ListWrap<PerfEvaluateTaskKpiDo> kpiMap = new ListWrap<>(taskKpiDos).groupBy(PerfEvaluateTaskKpiDo::getTaskUserId);
        ListWrap<EmpEvalRuleDo> ruleDoMap = new ListWrap<>(ruleDos).asMap(EmpEvalRuleDo::getEmpEvalId);
        for (ReportUserTaskExcelPo excelPo : excelVOS) {
            List<PerfEvaluateTaskItemScoreRuleDo> scoreRuleDoList = scoreRuleMap.groupGet(excelPo.getTaskUserId());
            if (CollUtil.isNotEmpty(scoreRuleDoList)) {
                for (PerfEvaluateTaskItemScoreRuleDo scoreRuleDo : scoreRuleDoList) {
                    if (Objects.equals(scoreRuleDo.getKpiItemId(), excelPo.getKpiItemId())) {
                        if (Objects.nonNull(scoreRuleDo.getSelfRater()) && scoreRuleDo.getSelfRater().isOpen()) {
                            /**selfScoreFlag该字段只用于当前*/
                            excelPo.setSelfRater(scoreRuleDo.getSelfScoreFlag());
                        }
                        MutualNodeConf peer = scoreRuleDo.getPeerRater();
                        if (Objects.nonNull(peer) && peer.isOpen()) {
                            if (CollUtil.isNotEmpty(resultMap.getMap()) && Objects.nonNull(resultMap.mapGet(excelPo.getTaskUserId() + "-peer_score"))) {
                                PerfEvaluateTaskScoreResultPo resultPo = resultMap.mapGet(excelPo.getTaskUserId() + "-peer_score");
                                excelPo.setPeerRater(resultPo.getScorerName());
                            } else {
                                excelPo.setPeerRater(StringUtils.join(peer.allRater().stream().map(Rater::getEmpName).collect(Collectors.toList()), ","));
                            }
                        }

                        S3SuperRaterConf superRater = scoreRuleDo.getSuperRater();
                        if (Objects.nonNull(superRater) && superRater.isOpen()) {
                            if (CollUtil.isNotEmpty(resultMap.getMap()) && Objects.nonNull(resultMap.mapGet(excelPo.getTaskUserId() + "-superior_score"))) {
                                PerfEvaluateTaskScoreResultPo resultPo = resultMap.mapGet(excelPo.getTaskUserId() + "-superior_score");
                                excelPo.setSuperRater(resultPo.getScorerName());
                            } else {
                                excelPo.setSuperRater(StringUtils.join(superRater.allRater().stream().map(Rater::getEmpName).collect(Collectors.toList()), ","));
                            }
                        }

                        MutualNodeConf subRater = scoreRuleDo.getSubRater();
                        if (Objects.nonNull(subRater) && subRater.isOpen()) {
                            if (CollUtil.isNotEmpty(resultMap.getMap()) && Objects.nonNull(resultMap.mapGet(excelPo.getTaskUserId() + "-sub_score"))) {
                                PerfEvaluateTaskScoreResultPo resultPo = resultMap.mapGet(excelPo.getTaskUserId() + "-sub_score");
                                excelPo.setSubRater(resultPo.getScorerName());
                            } else {
                                excelPo.setPeerRater(StringUtils.join(subRater.allRater().stream().map(Rater::getEmpName).collect(Collectors.toList()), ","));
                            }
                        }
                        S3RaterBaseConf appointRater = scoreRuleDo.getAppointRater();
                        if (Objects.nonNull(appointRater) && appointRater.isOpen()) {
                            if (CollUtil.isNotEmpty(resultMap.getMap()) && Objects.nonNull(resultMap.mapGet(excelPo.getTaskUserId() + "-appoint_score"))) {
                                PerfEvaluateTaskScoreResultPo resultPo = resultMap.mapGet(excelPo.getTaskUserId() + "-appoint_score");
                                excelPo.setAppointRater(resultPo.getScorerName());
                            } else {
                                excelPo.setAppointRater(StringUtils.join(appointRater.allRater().stream().map(Rater::getEmpName).collect(Collectors.toList()), ","));
                            }
                        }
                    }
                }
            } else {
                if (CollUtil.isNotEmpty(ruleDoMap.getMap())) {
                    EmpEvalRuleDo empEvalRuleDo = ruleDoMap.mapGet(excelPo.getTaskUserId());
                    if (Objects.isNull(empEvalRuleDo)) {
                        continue;
                    }
                    if (Objects.nonNull(empEvalRuleDo.getS3SelfRater()) && empEvalRuleDo.getS3SelfRater().isOpen()) {
                        /**initiator该字段只用于当前场景做考核人名称*/
                        excelPo.setSelfRater(empEvalRuleDo.getInitiator());
                    }
                    MutualNodeConf peerRater = empEvalRuleDo.getS3PeerRater();
                    if (Objects.nonNull(peerRater) && peerRater.isOpen()) {
                        if (CollUtil.isNotEmpty(resultMap.getMap()) && Objects.nonNull(resultMap.mapGet(excelPo.getTaskUserId() + "-peer_score"))) {
                            PerfEvaluateTaskScoreResultPo resultPo = resultMap.mapGet(excelPo.getTaskUserId() + "-peer_score");
                            excelPo.setPeerRater(resultPo.getScorerName());
                        } else {
                            excelPo.setPeerRater(StringUtils.join(peerRater.allRater().stream().map(Rater::getEmpName).collect(Collectors.toList()), ","));
                        }
                    }
                    S3SuperRaterConf superRater = empEvalRuleDo.getS3SuperRater();
                    if (Objects.nonNull(superRater) && superRater.isOpen()) {
                        if (CollUtil.isNotEmpty(resultMap.getMap()) && Objects.nonNull(resultMap.mapGet(excelPo.getTaskUserId() + "-superior_score"))) {
                            PerfEvaluateTaskScoreResultPo resultPo = resultMap.mapGet(excelPo.getTaskUserId() + "-superior_score");
                            excelPo.setSuperRater(resultPo.getScorerName());
                        } else {
                            excelPo.setSuperRater(StringUtils.join(superRater.allRater().stream().map(Rater::getEmpName).collect(Collectors.toList()), ","));
                        }
                    }

                    MutualNodeConf subRater = empEvalRuleDo.getS3SubRater();
                    if (Objects.nonNull(subRater) && subRater.isOpen()) {
                        if (CollUtil.isNotEmpty(resultMap.getMap()) && Objects.nonNull(resultMap.mapGet(excelPo.getTaskUserId() + "-sub_score"))) {
                            PerfEvaluateTaskScoreResultPo resultPo = resultMap.mapGet(excelPo.getTaskUserId() + "-sub_score");
                            excelPo.setSubRater(resultPo.getScorerName());
                        } else {
                            excelPo.setPeerRater(StringUtils.join(subRater.allRater().stream().map(Rater::getEmpName).collect(Collectors.toList()), ","));
                        }
                    }

                    if (CollUtil.isNotEmpty(kpiMap.groupGet(excelPo.getTaskUserId()))) {
                        List<PerfEvaluateTaskKpiDo> kpiDoList = kpiMap.groupGet(excelPo.getTaskUserId());
                        for (PerfEvaluateTaskKpiDo perfEvaluateTaskKpiDo : kpiDoList) {
                            if (StrUtil.equals(perfEvaluateTaskKpiDo.getKpiItemId(), excelPo.getKpiItemId())) {
                                List<StaffConfItem> scorerObjId = perfEvaluateTaskKpiDo.getScorerObjId();
                                if (CollUtil.isNotEmpty(scorerObjId)) {
                                    for (StaffConfItem staffConfItem : scorerObjId) {
                                        if (CollUtil.isNotEmpty(staffConfItem.getObjItems())) {
                                            excelPo.setItemRater(StringUtils.join(staffConfItem.getObjItems().stream().map(ObjItem::getObjName).collect(Collectors.toList()), ","));
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        Map<String, List<ReportUserTaskExcelPo>> groupMap = excelVOS.stream().collect(Collectors.groupingBy(ReportUserTaskExcelPo::getTaskUserId));
        Map<String, String> map = new HashMap<>();
        if (groupMap != null && groupMap.size() > 0) {
            groupMap.forEach((k, v) -> {
                Set<String> itemRaterSet = new HashSet<>();
                Set<String> selfRaterSet = new HashSet<>();
                Set<String> peerRaterSet = new HashSet<>();
                Set<String> subRaterSet = new HashSet<>();
                Set<String> superRaterSet = new HashSet<>();
                Set<String> appointRaterSet = new HashSet<>();
                v.forEach(item -> {
                    List<String> markList = Arrays.asList("**");
                    if (item.getItemRater() != null) {
                        boolean mark = item.isMarkScorerName(conf, SubScoreNodeEnum.ITEM_SCORE.getScene());
                        itemRaterSet.addAll(mark ? markList : StrUtil.splitTrim(item.getItemRater(), ","));
                    }
                    if (item.getSelfRater() != null) {
                        boolean mark = item.isMarkScorerName(conf, SubScoreNodeEnum.SELF_SCORE.getScene());
                        selfRaterSet.addAll(mark ? markList : StrUtil.splitTrim(item.getEmpName(), ","));
                    }
                    if (item.getPeerRater() != null) {
                        boolean mark = item.isMarkScorerName(conf, SubScoreNodeEnum.PEER_SCORE.getScene());
                        peerRaterSet.addAll(mark ? markList : StrUtil.splitTrim(item.getPeerRater(), ","));
                    }
                    if (item.getSubRater() != null) {
                        boolean mark = item.isMarkScorerName(conf, SubScoreNodeEnum.SUB_SCORE.getScene());
                        subRaterSet.addAll(mark ? markList : StrUtil.splitTrim(item.getSubRater(), ","));
                    }
                    if (item.getSuperRater() != null) {
                        boolean mark = item.isMarkScorerName(conf, SubScoreNodeEnum.SUB_SCORE.getScene());
                        superRaterSet.addAll(mark ? markList : StrUtil.splitTrim(item.getSuperRater(), ","));
                    }
                    if (item.getAppointRater() != null) {
                        boolean mark = item.isMarkScorerName(conf, SubScoreNodeEnum.SUB_SCORE.getScene());
                        appointRaterSet.addAll(mark ? markList : StrUtil.splitTrim(item.getAppointRater(), ","));
                    }
                });

                StringBuffer buffer = new StringBuffer();
                if (CollUtil.isNotEmpty(selfRaterSet)) {
                    buffer.append("自评:").append(StringUtils.join(selfRaterSet, ",")).append(System.lineSeparator());
                }
                if (CollUtil.isNotEmpty(peerRaterSet)) {
                    buffer.append("同级评:").append(StringUtils.join(peerRaterSet, ",")).append(System.lineSeparator());
                }
                if (CollUtil.isNotEmpty(subRaterSet)) {
                    buffer.append("下级评:").append(StringUtils.join(subRaterSet, ",")).append(System.lineSeparator());
                }
                if (CollUtil.isNotEmpty(superRaterSet)) {
                    buffer.append("上级评:").append(StringUtils.join(superRaterSet, ",")).append(System.lineSeparator());
                }
                if (CollUtil.isNotEmpty(appointRaterSet)) {
                    buffer.append("指定评:").append(StringUtils.join(appointRaterSet, ",")).append(System.lineSeparator());
                }
                if (CollUtil.isNotEmpty(itemRaterSet)) {
                    buffer.append("定向评:").append(StringUtils.join(itemRaterSet, ",")).append(System.lineSeparator());
                }
                map.put(k, buffer.toString());
            });
        }
        return map;
    }

    public Map<String, String> buildNoItemScorerNodeDetails(TenantSysConf conf, List<ReportEvalUserPo> excelVOS) {
        List<String> taskUserIds = excelVOS.stream().map(ReportEvalUserPo::getTaskUserId).distinct().collect(Collectors.toList());
        List<EmpEvalScorerNodePo> scorerNodes = listEmpEvalScorerNode(conf.getCompanyId(), taskUserIds);
        ListWrap<EmpEvalScorerNodePo> scorerNodeWs = new ListWrap<>(scorerNodes).groupBy(EmpEvalScorerNodePo::getTaskUserId);
        Map<String, String> map = new HashMap<>();
        for (ReportEvalUserPo excelPo : excelVOS) {
            List<EmpEvalScorerNodePo> userScorerNodes = scorerNodeWs.groupGet(excelPo.getTaskUserId());
            excelPo.accpScorerNode(userScorerNodes);
            excelPo.buildScorerNodeDetails(map, conf);
        }
        return map;
    }

    public Map<String, String> buildScorerNodeDetails(TenantSysConf conf, List<ReportUserTaskExcelPo> excelVOS) {
        List<String> taskUserIds = excelVOS.stream().map(ReportUserTaskExcelPo::getTaskUserId).distinct().collect(Collectors.toList());
        List<EmpEvalScorerNodePo> scorerNodes = listEmpEvalScorerNode(conf.getCompanyId(), taskUserIds);
        ListWrap<EmpEvalScorerNodePo> scorerNodeWs = new ListWrap<>(scorerNodes).groupBy(EmpEvalScorerNodePo::getTaskUserId);
        for (ReportUserTaskExcelPo excelPo : excelVOS) {
            List<EmpEvalScorerNodePo> userScorerNodes = scorerNodeWs.groupGet(excelPo.getTaskUserId());
            excelPo.accpScorerNode(userScorerNodes);
        }
        Map<String, List<ReportUserTaskExcelPo>> groupMap = excelVOS.stream().collect(Collectors.groupingBy(ReportUserTaskExcelPo::getTaskUserId));
        Map<String, String> map = new HashMap<>();
        if (groupMap != null && groupMap.size() > 0) {
            groupMap.forEach((k, v) -> {
                Set<String> itemRaterSet = new HashSet<>();
                Set<String> selfRaterSet = new HashSet<>();
                Set<String> peerRaterSet = new HashSet<>();
                Set<String> subRaterSet = new HashSet<>();
                Set<String> superRaterSet = new HashSet<>();
                Set<String> appointRaterSet = new HashSet<>();
                v.forEach(item -> {
                    item.buildRaterSet(conf, itemRaterSet, selfRaterSet, peerRaterSet, subRaterSet, superRaterSet, appointRaterSet);
                });

                StringBuffer buffer = new StringBuffer();
                if (CollUtil.isNotEmpty(selfRaterSet)) {
                    buffer.append("自评:").append(StringUtils.join(selfRaterSet, ",")).append(System.lineSeparator());
                }
                if (CollUtil.isNotEmpty(peerRaterSet)) {
                    buffer.append("同级评:").append(StringUtils.join(peerRaterSet, ",")).append(System.lineSeparator());
                }
                if (CollUtil.isNotEmpty(subRaterSet)) {
                    buffer.append("下级评:").append(StringUtils.join(subRaterSet, ",")).append(System.lineSeparator());
                }
                if (CollUtil.isNotEmpty(superRaterSet)) {
                    buffer.append("上级评:").append(StringUtils.join(superRaterSet, ",")).append(System.lineSeparator());
                }
                if (CollUtil.isNotEmpty(appointRaterSet)) {
                    buffer.append("指定评:").append(StringUtils.join(appointRaterSet, ",")).append(System.lineSeparator());
                }
                if (CollUtil.isNotEmpty(itemRaterSet)) {
                    buffer.append("定向评:").append(StringUtils.join(itemRaterSet, ",")).append(System.lineSeparator());
                }
                map.put(k, buffer.toString());
            });
        }
        return map;
    }

    public List<PerfEvaluateTaskItemDynamicVO> queryItemDynamicV3(List<String> taskUserIds) {
        ComQB log = ComQB.build(OperationLogDo.class, "o")
                .leftJoin(EmployeeBaseInfoDo.class, "e").appendOn(" o.company_id = e.company_id and o.created_user = e.employee_id")
                .clearSelect().select(" e.name empName, e.avatar empAvatar, o.business_scene, o.before_value, o.after_value, o.description content, o.created_time,o.ref_id task_user_id, o.kpi_item_id")
                .whereInReq("o.ref_id", taskUserIds)
//                .whereEq("o.kpi_item_id", kpiItemId)
                .appendWhere(" o.business_scene in('input_finish_value','submit_finish_value','finish_value_audit','add_ind_ext_score_item') ");

        ComQB discuss = ComQB.build(PerfEvaluateTaskDiscussDo.class, "d")
                .clearSelect().select(" d.emp_name,d.emp_avatar, 'discuss' as businessScene,'' as beforeValue, '' as afterValue, d.content,d.created_time,d.task_user_id, d.kpi_item_id")
                .whereInReq("d.task_user_id", taskUserIds)
//                .whereEq("d.kpi_item_id", kpiItemId)
                .appendWhere(" d.is_deleted = 'false' ");

        ComQB uniQb = ComQB.build().fromQ(log.union(discuss), "uni")
                .setRsType(PerfEvaluateTaskItemDynamicVO.class)
                .orderByDesc(" uni.created_time ");
        List<PerfEvaluateTaskItemDynamicVO> objects = autoBaseDao.listAll(uniQb);
        return objects.stream().filter(idt -> ObjectUtil.isNotNull(idt))
                .filter(idt -> ObjectUtil.isNotNull(idt.getTaskUserId()) && ObjectUtil.isNotNull(idt.getKpiItemId()))
                .collect(Collectors.toList());
    }
    public List<PerfEvaluateTaskItemDynamicVO> queryItemDynamic(String taskUserId, String kpiItemId) {
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskItemDynamicVO.class).setSql("SELECT * FROM " +
                "(SELECT e.`name` empName, e.avatar empAvatar, o.business_scene, o.before_value, o.after_value, o.description content, o.created_time " +
                "FROM operation_log o  " +
                "LEFT JOIN employee_base_info e ON o.created_user = e.employee_id " +
                "WHERE o.ref_id = #{taskUserId} AND o.kpi_item_id = #{kpiItemId} AND o.business_scene in ('input_finish_value','submit_finish_value','finish_value_audit','add_ind_ext_score_item') " +
                "UNION ALL " +
                "SELECT d.emp_name,d.emp_avatar, 'discuss' as businessScene,'' as beforeValue, '' as afterValue, d.content,d.created_time " +
                "FROM perf_evaluate_task_discuss d WHERE d.task_user_id = #{taskUserId} AND d.kpi_item_id = #{kpiItemId} AND d.is_deleted = 'false')t " +
                "ORDER BY t.created_time DESC");
        sqlBuilder.setValue("taskUserId", taskUserId);
        sqlBuilder.setValue("kpiItemId", kpiItemId);
        return autoBaseDao.listAll(sqlBuilder);
    }

    public List<PerfEvaluateTaskScoreResultPo> listResultByTaskIds(String companyId, List<String> taskUserIds) {
        ComQB qb = ComQB.build(PerfEvaluateTaskScoreResultPo.class, "s")
                .join(EmployeeBaseInfoDo.class, "e").appendOn("s.scorer_id=e.employee_id and s.company_id=e.company_id ")
                .clearSelect().select("s.*,e.name as scorer_name ").setRsType(PerfEvaluateTaskScoreResultPo.class)
                .whereEq("s.company_id", companyId)
                .whereIn("s.task_user_id", taskUserIds)
                .whereEq("s.is_deleted", "false");
        return autoBaseDao.listAll(qb);
    }

    public List<PerfEvaluateTaskScoreResultPo> listAuditResultAuditByTaskIds(String companyId, List<String> taskUserIds) {
        ComQB qb = ComQB.build(PerfEvaluateTaskScoreResultPo.class, "s")
                .join(EmployeeBaseInfoDo.class, "e").appendOn("s.scorer_id=e.employee_id and s.company_id=e.company_id ")
                .clearSelect().select("s.*,e.name as scorer_name ").setRsType(PerfEvaluateTaskScoreResultPo.class)
                .whereEq("s.company_id", companyId)
                .whereIn("s.task_user_id", taskUserIds)
                .whereEq("s.scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene())
                .whereEq("s.is_deleted", "false");
        return autoBaseDao.listAll(qb);
    }

    public ListWrap<EvalScorerNodeKpiItem> listEvalScoreNodeKpiItem(String companyId, List<String> taskUserIds) {
        ComQB comQB = ComQB.build(EmpScorerScoreKpiItemDo.class, "sc")
                .join(EmpEvalScorerNodeDo.class,"sn").appendOn("sc.company_id=sn.company_id and sc.scorer_score_node_id = sn.id and sn.is_deleted = 'false'")
                .setRsType(EvalScorerNodeKpiItem.class)
                .whereEqReq("sc.company_id",companyId)
                .whereIn("sc.task_user_id", taskUserIds)
                .appendWhere("((sn.transfer_type is null) OR (sn.transfer_type = 'transfer' AND sn.transfer_from IS NOT NULL AND sn.transfer_to IS NULL) OR (sn.transfer_type = 'skip' AND sn.transfer_from IS NULL AND sn.transfer_to IS NOT NULL))") //不显示 跳过的人
                .whereEqReq("sc.is_deleted", Boolean.FALSE.toString());
        List<EvalScorerNodeKpiItem> kpiItems = autoBaseDao.listAll(comQB);
        return new ListWrap<>(kpiItems).groupBy(EvalScorerNodeKpiItem::getTaskUserId);
    }
    public FinalWeightSumScore sumFinalWeightScore(@NotNull TenantId companyId, String taskUserId, boolean isOpenAvgWeightCompute) {
        ComQB qb = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "s")
                .clearSelect().select("scorer_type,sum(final_weight_score) sum,count(distinct scorer_id) scorerNum")
                .setRsType(FinalWeightSumScore.SumNormalItem.class)
                .whereEqReq("company_id", companyId.getId())
                //.whereEqReq("task_id", taskId)
                //.whereEqReq("emp_id", empId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("audit_status", BusinessConstant.PASS)
                .whereInReq("scorerType", EvaluateAuditSceneEnum.finalScoreTypes())
                .whereEq("is_deleted", "false")
                .groupBy("scorer_type");
        List<FinalWeightSumScore.SumNormalItem> sums = autoBaseDao.listAllDomain(qb, FinalWeightSumScore.SumNormalItem.class);
        return new FinalWeightSumScore(sums, isOpenAvgWeightCompute);
    }


    public FinalPlusSubSumScore sumPlusAndSubScore(TenantId companyId, String taskUserId) {

        ComQB rsQb = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "sr")
                .clearSelect().select("sr.company_id,sr.task_user_id,sr.kpi_type_id ")
                .select("SUM(sr.final_weight_plus_score) plus_sum")
                .select("SUM(sr.final_weight_subtract_score) subtract_sum")
                .whereEqReq("sr.company_id", companyId.getId())
                //.whereEqReq("sr.task_id", taskId)
                //.whereEqReq("sr.emp_id", empId)
                .whereEqReq("sr.task_user_id", taskUserId)
                .whereEq("sr.audit_status", BusinessConstant.PASS)
                .whereInReq("sr.scorer_type", EvaluateAuditSceneEnum.finalScoreTypes())
                .appendWhere("sr.is_deleted ='false'")
                .groupBy(" sr.company_id, sr.task_user_id, sr.kpi_type_id ");

        ComQB kiRsQ = ComQB.build(PerfEvaluateTaskKpiDo.class, "ki")
                .clearSelect()
                .select("ki.company_id, ki.task_user_id, ki.kpi_type_id, ki.kpi_type_classify, ki.max_extra_score")
                .appendWhere("ki.is_deleted ='false'")
                .whereEqReq("ki.company_id", companyId.getId())
                //.whereEqReq("ki.task_id", taskId)
                //.whereEqReq("ki.emp_id", empId)
                .whereEqReq("ki.task_user_id", taskUserId)
                .appendWhere("ki.is_deleted ='false'")
                .groupBy(" ki.kpi_type_id, ki.kpi_type_classify");

        ComQB qb = ComQB.build().fromQ(rsQb, "rs")
                .joinQ(kiRsQ, "kirs")
                .clearSelect().select("  kirs.kpi_type_id, kirs.kpi_type_classify, kirs.max_extra_score ")
                .select("rs.plus_sum,rs.subtract_sum")
                .setRsType(FinalPlusSubSumScore.PlusSubItem.class)
                .appendOn("rs.company_id = kirs.company_id and rs.task_user_id = kirs.task_user_id " +
                        "  and rs.kpi_type_id = kirs.kpi_type_id")
                .whereEqReq("rs.company_id", companyId.getId())
                .whereEqReq("rs.task_user_id", taskUserId);
        qb.getParams().putAll(rsQb.getParams());
        qb.getParams().putAll(kiRsQ.getParams());
        List<FinalPlusSubSumScore.PlusSubItem> results = autoBaseDao.listAll(qb);
        LoggerFactory.getLogger(getClass()).info("results=={}", JSON.toJSONString(results));
        return new FinalPlusSubSumScore(results);
    }

    private static ComQB buildTaskDetailComQb(TaskReportQry query) {
        final ComQB inq = ComQB.buildDiff(Integer.class, "emp_ref_org")
                .clearSelect().select("org_id")
                .appendWhere("emp_id = e.employee_id AND ref_type = 'org'");
        final ComQB orgConcat = ComQB.build(EmpOrganizationDo.class, "o")
                .clearSelect().select("GROUP_CONCAT(o.org_name)")
                .appendWhere(" o.company_id = '" + query.getCompanyId() + "'")
                .whereInQ("o.org_id ", inq);

        final ComQB qb = buildTaskDetailBaseComQb(query);
        qb.clearSelect().select("(case when u.temp_task = '0' then eo.org_name else (" + orgConcat.getSql() + ") end) as orgName")
                .select("u.org_id,u.at_org_name_path ")
                .select("u.mutual_eval_compute_type")
                .select("u.eval_org_id,u.original_final_score,u.last_score_comment,u.signature_pic,u.score_of_ref,u.perf_coefficient, b.task_name,ec.cycle_start as cycle_start_date,ec.cycle_end as cycle_end_date, b.templ_evaluate_json,e.jobnumber, b.id as taskId,( CASE b.performance_type WHEN 1 THEN '个人绩效' WHEN 2 THEN '组织绩效' ELSE NULL END ) AS performanceTypeStr")
                .select("k.id, e.ding_user_id user_id,e.`name` as empName,u.emp_id,k.kpi_item_id,k.kpi_item_name,ifnull(k.kpi_type_name, kt.kpi_type_name) as kpi_type_name, kt.kpi_type_id,kt.kpi_type_weight,k.item_weight,k.item_rule,k.scoring_rule,k.item_target_value,k.item_unit,if(k.item_type = 'non-measurable' and k.input_format = 'text',k.item_finish_value_text,k.item_finish_value) as item_finish_value,k.work_item_finish_value,k.points_num as itemPointsNum,k.item_type,k.input_format")
                .select("u.id as task_user_id, u.final_score,case u.input_finish_status when '0' then '不需要录入' when '1' then '未录入' when '2' then '部分录入' when '3' then '全部录入' end as input_finish_status, u.evaluation_level,k.result_input_type,if(k.ind_level_group_id is null,k.kpi_type_classify,'indLevel') as kpi_type_classify,IF(u.task_status = 'confirmed' AND u.enter_score_flag = 'true', 'waitFinishValueInput', u.task_status) task_status,k.item_auto_score,k.item_target_value_text,k.company_id, ss.summaries as summaries ")
                .select("b.score_view,er.type_weight_conf")
                .select("ifnull(k.finish_value_type,1) as finishValueType")
                .setRsType(ReportUserTaskExcelPo.class);
        qb.groupBy(" u.company_id,u.id,k.kpi_type_id,k.kpi_item_id");
        qb.orderBy("u.id,kt.type_order,k.kpi_type_id,k.order,k.kpi_item_id");
//        qb.orderBy("u.at_org_name_path,u.id,kt.type_order,k.kpi_type_id,k.order,k.kpi_item_id");
        qb.setPage(query.getPageNo(), query.getPageSize());
        return qb;
    }

    private static ComQB buildTaskUserComQb(TaskReportQry query) {
        final ComQB inq = ComQB.buildDiff(Integer.class, "emp_ref_org")
                .clearSelect().select("org_id")
                .appendWhere("emp_id = e.employee_id AND ref_type = 'org'");
        final ComQB orgConcat = ComQB.build(EmpOrganizationDo.class, "o")
                .clearSelect().select("GROUP_CONCAT(o.org_name)")
                .appendWhere(" o.company_id = '" + query.getCompanyId() + "'")
                .whereInQ("o.org_id ", inq);

        final ComQB qb = buildTaskUserBaseComQb(query);
        qb.clearSelect().select("(case when u.temp_task = '0' then eo.org_name else (" + orgConcat.getSql() + ") end) as orgName")
                .select("u.org_id,u.at_org_name_path,ec.cycle_start as cycleStartDate,ec.cycle_end as cycleEndDate")
                .select("u.eval_org_id,u.original_final_score,u.last_score_comment,u.signature_pic,u.score_of_ref,u.perf_coefficient, b.task_name,ec.cycle_start as cycle_start_date,ec.cycle_end as cycle_end_date, b.templ_evaluate_json,e.jobnumber, b.id as taskId,( CASE b.performance_type WHEN 1 THEN '个人绩效' WHEN 2 THEN '组织绩效' ELSE NULL END ) AS performanceTypeStr")
                .select("e.ding_user_id user_id,e.`name` as empName,u.emp_id,er.rule_name ruleName")
                .select("u.task_id ,u.id as task_user_id, u.final_score,case u.input_finish_status when '0' then '不需要录入' when '1' then '未录入' when '2' then '部分录入' when '3' then '全部录入' end as input_finish_status, u.evaluation_level,IF(u.task_status = 'confirmed' AND u.enter_score_flag = 'true', 'waitFinishValueInput', u.task_status) task_status")
                .select("b.score_view,er.type_weight_conf")
                .setRsType(ReportEvalUserPo.class);
        qb.groupBy(" u.company_id,u.id");
        qb.orderBy("u.id");
        qb.setPage(query.getPageNo(), query.getPageSize());
        return qb;
    }

    public List<EmpEvalScorerNodePo> listEmpEvalScorerNode(String companyId, List<String> taskUserIds) {
        //评分人评分环节 select task_user_id,group_concat(scorer_name) from  emp_scorer_score_node  where task_user_id='1383057' and is_deleted='false'
        //group by task_user_id,score_type
        ComQB scoreNodeComQB = ComQB.build(EmpEvalScorerNodeDo.class)
                .clearSelect().setRsType(EmpEvalScorerNodePo.class).select("task_user_id,scorer_type,group_concat(scorer_name) as scorer_name")
                .whereEqReq("company_id", companyId)
                .whereInReq("task_user_id", taskUserIds)
                .appendWhere("((transfer_type is null) OR (transfer_type = 'transfer' AND transfer_from IS NOT NULL AND transfer_to IS NULL) OR (transfer_type = 'skip' AND transfer_from IS NULL AND transfer_to IS NOT NULL))")
                .whereEq("is_deleted", "false")
                .groupBy("task_user_id,scorer_type");
        return autoBaseDao.listAll(scoreNodeComQB);
    }

    private static ComQB buildTaskDetailComQbV3(TaskReportQry query) {
        final ComQB inq = ComQB.buildDiff(Integer.class, "emp_ref_org")
                .clearSelect().select("org_id")
                .appendWhere("emp_id = e.employee_id AND ref_type = 'org'");
        final ComQB orgConcat = ComQB.build(EmpOrganizationDo.class, "o")
                .clearSelect().select("GROUP_CONCAT(o.org_name)")
                .appendWhere(" o.company_id = '" + query.getCompanyId() + "'")
                .whereInQ("o.org_id ", inq);

        final ComQB qb = buildTaskDetailBaseComQb(query);
        qb.clearSelect().select("(case when u.temp_task = '0' then eo.org_name else (" + orgConcat.getSql() + ") end) as orgName")
                .select("u.org_id,u.at_org_name_path ")
                .select("u.mutual_eval_compute_type")
                .select("u.eval_org_id,u.original_final_score,u.last_score_comment,u.signature_pic,u.score_of_ref,u.perf_coefficient, b.task_name,ec.cycle_start as cycle_start_date,ec.cycle_end as cycle_end_date, b.templ_evaluate_json,e.jobnumber, b.id as taskId,( CASE b.performance_type WHEN 1 THEN '个人绩效' WHEN 2 THEN '组织绩效' ELSE NULL END ) AS performanceTypeStr")
                .select("k.id, e.ding_user_id user_id,e.`name` as empName,u.emp_id,k.kpi_item_id,k.kpi_item_name,ifnull(k.kpi_type_name, kt.kpi_type_name) as kpi_type_name, kt.kpi_type_id,kt.kpi_type_weight,k.item_weight,k.item_rule,k.scoring_rule,k.item_target_value,k.item_unit,if(k.item_type = 'non-measurable' and k.input_format = 'text',k.item_finish_value_text,k.item_finish_value) as item_finish_value,k.work_item_finish_value,k.points_num as itemPointsNum,k.item_type,k.input_format")
                .select("if(k.scorer_type != '' and k.scorer_type='auto',k.item_auto_score,ifnull(k.item_final_score,'-')) as kpiItemFinalScore")
                .select("ifnull(k.item_final_self_score,'-') as selfScore,ifnull(k.item_final_peer_score,'-') as peerScore ")
                .select("ifnull(k.item_final_sub_score,'-') as subScore,ifnull(k.item_final_superior_score,'-') as superiorScore,ifnull(k.item_final_item_score,'-') as itemItemScore ")
                .select("ifnull(k.item_final_appoint_score,'-') as appointScore,ifnull(k.item_self_score,'-') as selfFinalScore,ifnull(k.item_peer_score,'-') as peerFinalScore ")
                .select("ifnull(k.item_sub_score,'-') as subFinalScore,ifnull(k.item_superior_score,'-') as superiorFinalScore")
                .select("ifnull(k.item_appoint_score,'-') as appointFinalScore ")
                .select("ifnull(u.final_score,'-') as finalScore")
                .select("if(k.scorer_type != '' and k.scorer_type='auto',k.item_score,'-') as itemAutoScore") //替换了 item_auto_score
                .select("u.id as task_user_id,case u.input_finish_status when '0' then '不需要录入' when '1' then '未录入' when '2' then '部分录入' when '3' then '全部录入' end as input_finish_status, u.evaluation_level,k.result_input_type,if(k.ind_level_group_id is null,k.kpi_type_classify,'indLevel') as kpi_type_classify,IF(u.task_status = 'confirmed' AND u.enter_score_flag = 'true', 'waitFinishValueInput', u.task_status) task_status,k.item_target_value_text,k.company_id, ss.summaries as summaries ")
                .select("b.score_view,er.type_weight_conf")
                .select("ifnull(k.finish_value_type,1) as finishValueType")
                .setRsType(ReportUserTaskExcelPo.class);
        qb.groupBy(" u.company_id,u.id,k.kpi_type_id,k.kpi_item_id");
        qb.orderBy("u.id,kt.type_order,k.kpi_type_id,k.order,k.kpi_item_id");
        qb.setPage(query.getPageNo(), query.getPageSize());
        return qb;
    }
    private static ComQB buildTaskUserBaseComQb(TaskReportQry query) {
        final ComQB qb = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("b.company_id=u.company_id  and b.id = u.task_id ")
                .leftJoin(EmpEvalRuleDo.class, "er")
                .appendOn("er.company_id=u.company_id  and er.emp_eval_id = u.id and er.is_deleted = 'false'")
                .join(CycleDo.class, "ec")
                .appendOn("b.company_id=ec.company_id  and b.cycle_id = ec.id")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn("e.company_id = u.company_id and e.employee_id = u.emp_id")
                .leftJoin(EmpOrganizationDo.class, "eo")
                .appendOn("u.org_id = eo.org_id")
                .whereEq("u.company_id", query.getCompanyId())
                .appendWhere(" u.is_deleted = 'false' and b.task_status = 'published' AND b.is_deleted = 'false' ");

        if (StringUtils.isNotBlank(query.getEmpId())) {
            qb.appendWhere(" u.emp_id in " + StringTool.getInStrSql(query.getEmpId()));
        }
        if (StringUtils.isNotBlank(query.getCycleId())) {
            qb.appendWhere(" b.cycle_id in " + StringTool.getInStrSql(query.getCycleId()));
        }
        if (StringUtils.isNotBlank(query.getTaskId())) {
            qb.appendWhere(" u.task_id in" + StringTool.getInStrSql(query.getTaskId()));
        }
        if (StringUtils.isNotBlank(query.getLevel())) {
            qb.appendWhere(" u.evaluation_level in" + StringTool.getInStrSql(query.getLevel()));
        }
        if (StringUtils.isNotBlank(query.getCycleStartDate()) && StringUtils.isNotBlank(query.getCycleEndDate())) {
            final String format = String.format("(ec.cycle_start BETWEEN '%s' and '%s' OR ec.cycle_end BETWEEN '%s' and '%s')",
                    query.getCycleStartDate(), query.getCycleEndDate(), query.getCycleStartDate(), query.getCycleEndDate());
            qb.appendWhere(format);
        }
        if (StringUtils.isNotBlank(query.getOrgId())) {
            final ComQB existQ = ComQB.build(EmpRefOrgModel.class)
                    .clearSelect().select("id")
                    .appendWhere(" company_id ='" + query.getCompanyId() + "'")
                    .appendWhere(" org_id in " + StringTool.getInStrSql(query.getOrgId()))
                    .appendWhere("emp_id = u.emp_id AND ref_type = 'org'");
            qb.appendWhere(String.format("EXISTS(%s)", existQ.getSql()));
        }
        if (StringUtils.isNotBlank(query.getRoleId())) {
            final ComQB existQ = ComQB.build(RoleRefEmpDo.class)
                    .clearSelect().select("id")
                    .appendWhere(" company_id ='" + query.getCompanyId() + "'")
                    .appendWhere(" role_id in " + StringTool.getInStrSql(query.getRoleId()))
                    .appendWhere("emp_id = u.emp_id ");
            qb.appendWhere(String.format("EXISTS(%s)", existQ.getSql()));
        }
        return qb;
    }

    private static ComQB buildTaskDetailBaseComQb(TaskReportQry query) {
        ComQB subQB = ComQB.build(EvalScoreSummaryDo.class, "s")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn("s.company_id = e.company_id and s.created_user = e.employee_id and s.is_deleted = 'false' ")
                .clearSelect()
                .select("s.company_id,s.task_user_id")
                .select("JSON_ARRAYAGG( JSON_OBJECT('node', s.score_type,'name', e.name, 'summary', s.summary)) summaries ")
                .whereEqReq("s.company_id", query.getCompanyId())
                .whereEqReq("s.is_deleted", "false")
                .appendWhere(" (s.summary is not null and s.summary != '') ")
                .groupBy("s.task_user_id");

        final ComQB qb = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("b.company_id=u.company_id  and b.id = u.task_id ")
                .leftJoin(EmpEvalRuleDo.class, "er")
                .appendOn("er.company_id=u.company_id  and er.emp_eval_id = u.id and er.is_deleted = 'false'")
                .join(CycleDo.class, "ec")
                .appendOn("b.company_id=ec.company_id  and b.cycle_id = ec.id")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn("e.company_id = u.company_id and e.employee_id = u.emp_id")
                .leftJoin(EmpOrganizationDo.class, "eo")
                .appendOn("u.org_id = eo.org_id")
                .leftJoin(PerfEvaluateTaskKpiDo.class, "k")
                .appendOn("k.task_user_id = u.id and k.company_id=u.company_id  and k.is_deleted = 'false'")
                .leftJoin(EmpEvalKpiTypeDo.class, "kt")
                .appendOn("kt.company_id=u.company_id and kt.task_user_id=u.id and k.kpi_type_id = kt.kpi_type_id and kt.is_deleted='false'")
                .leftJoinQ(subQB, "ss")
                .appendOn("ss.company_id = u.company_id and ss.task_user_id = u.id")
                .whereEq("u.company_id", query.getCompanyId())
                .appendWhere(" u.is_deleted = 'false' and b.task_status = 'published' AND b.is_deleted = 'false' ");

        if (StringUtils.isNotBlank(query.getEmpId())) {
            qb.appendWhere(" u.emp_id in " + StringTool.getInStrSql(query.getEmpId()));
        }

        if (StringUtils.isNotBlank(query.getCycleId())) {
            qb.appendWhere(" b.cycle_id in " + StringTool.getInStrSql(query.getCycleId()));
        }

        if (StringUtils.isNotBlank(query.getTaskId())) {
            qb.appendWhere(" u.task_id in" + StringTool.getInStrSql(query.getTaskId()));
        }

        if (StringUtils.isNotBlank(query.getLevel())) {
            qb.appendWhere(" u.evaluation_level in" + StringTool.getInStrSql(query.getLevel()));
        }

        if (StringUtils.isNotBlank(query.getCycleStartDate()) && StringUtils.isNotBlank(query.getCycleEndDate())) {
            final String format = String.format("(ec.cycle_start BETWEEN '%s' and '%s' OR ec.cycle_end BETWEEN '%s' and '%s')",
                    query.getCycleStartDate(), query.getCycleEndDate(), query.getCycleStartDate(), query.getCycleEndDate());
            qb.appendWhere(format);
        }
//        if (StringUtils.isNotBlank(query.getOrgId())) {
//            Arrays.asList(query.getOrgId().split(","));
//            final ComQB existQ = ComQB.build(EmpRefOrgModel.class)
//                    .clearSelect().select("id")
//                    .appendWhere(" company_id ='" + query.getCompanyId() + "'")
//                    .appendWhere(" org_id in " + StringTool.getInStrSql(query.getOrgId()))
//                    .appendWhere("emp_id = u.emp_id AND ref_type = 'org'");
//            qb.appendWhere(String.format("EXISTS(%s)", existQ.getSql()));
//        }
        if (StringUtils.isNotBlank(query.getOrgId())) {
            qb.appendWhere(" u.org_id in" + StringTool.getInStrSql(query.getOrgId()));
        }

        if (StringUtils.isNotBlank(query.getRoleId())) {
            final ComQB existQ = ComQB.build(RoleRefEmpDo.class)
                    .clearSelect().select("id")
                    .appendWhere(" company_id ='" + query.getCompanyId() + "'")
                    .appendWhere(" role_id in " + StringTool.getInStrSql(query.getRoleId()))
                    .appendWhere("emp_id = u.emp_id ");
            qb.appendWhere(String.format("EXISTS(%s)", existQ.getSql()));
        }
        return qb;
    }

    public List<PerfEvaluateTaskItemScoreRuleDo> listTaskItemScoreRule(List<String> taskUserIds) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(PerfEvaluateTaskItemScoreRuleDo.class, "sr")
                .appendOn("u.id = sr.task_user_id")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn("u.company_id = e.company_id and e.employee_id = u.emp_id")
                .clearSelect()
                .select("sr.id,sr.company_id,sr.task_id,sr.task_user_id,e.name as selfScoreFlag, sr.kpi_item_id,self_rater,super_rater,appoint_rater,peer_rater,sub_rater")
                .setRsType(PerfEvaluateTaskItemScoreRuleDo.class)
                .whereEq("sr.is_deleted", Boolean.FALSE.toString())
                .whereIn("u.id", taskUserIds)
                .groupBy("sr.task_user_id,sr.kpi_item_id");
        return autoBaseDao.listAll(comQB);
    }

    private List<EmpEvalKpiTypeDo> listKpiTypeScoreRule(List<String> taskUserIds) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(EmpEvalKpiTypeDo.class, "sr")
                .appendOn("u.id = sr.task_user_id")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn("u.company_id = e.company_id and e.employee_id = u.emp_id")
                .clearSelect()
                .select("sr.company_id,sr.task_user_id,e.name as selfScoreFlag, sr.kpi_type_id,self_rater,super_rater,appoint_rater,peer_rater,sub_rater")
                .setRsType(EmpEvalKpiTypeDo.class)
                .whereEq("sr.is_deleted", Boolean.FALSE.toString())
                .whereIn("u.id", taskUserIds)
                .groupBy("sr.task_user_id,sr.kpi_type_id");
        return autoBaseDao.listAll(comQB);
    }

    public List<EmpEvalRuleDo> listEmpEvalRule(List<String> taskUserIds) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(EmpEvalRuleDo.class, "er")
                .appendOn("u.id = er.emp_eval_id and er.is_deleted='false' and er.company_id = u.company_id")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn("u.company_id = e.company_id and e.employee_id = u.emp_id")
                .clearSelect()
                .select("er.emp_eval_id,er.company_id,e.name as initiator,evaluate_type, s3_self_rater,s3_super_rater,s3_appoint_rater,s3_peer_rater,s3_sub_rater")
                .setRsType(EmpEvalRuleDo.class)
                .whereEq("er.is_deleted", Boolean.FALSE.toString())
                .whereNotEq("er.evaluate_type", "custom")
                .whereIn("u.id", taskUserIds)
                .groupBy("er.emp_eval_id");
        return autoBaseDao.listAll(comQB);
    }

    public List<PerfEvaluateTaskAuditVO> listTaskAudit(List<String> taskUserIds) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskAuditModel.class, "td")
                .leftJoin(EmployeeBaseInfoDo.class, "e")
                .appendOn("td.company_id = e.company_id and e.employee_id = td.approver_info")
                .clearSelect()
                .select("td.task_user_id,td.company_id,td.scene,GROUP_CONCAT(DISTINCT e.name) as approverName")
                .setRsType(PerfEvaluateTaskAuditVO.class)
                .whereEq("td.is_deleted", Boolean.FALSE.toString())
                .whereIn("td.task_user_id", taskUserIds)
                .groupBy("td.task_user_id,td.scene");
        return autoBaseDao.listAll(comQB);
    }

    public List<PerfEvaluateTaskScoreResultPo> listTaskScoreResult(List<String> taskUserIds) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultPo.class, "sr")
                .leftJoin(EmployeeBaseInfoDo.class, "e")
                .appendOn("sr.company_id = e.company_id and e.employee_id = sr.scorer_id")
                .clearSelect()
                .select("sr.task_user_id,sr.company_id,sr.scorer_type,sr.scorer_id,GROUP_CONCAT(DISTINCT e.name) as scorerName")
                .setRsType(PerfEvaluateTaskScoreResultPo.class)
                .whereEq("sr.is_deleted", Boolean.FALSE.toString())
                .whereIn("sr.task_user_id", taskUserIds)
                .groupBy("sr.task_user_id,sr.scorer_type");
        return autoBaseDao.listAll(comQB);
    }

    public List<PerfEvaluateTaskKpiDo> listTaskKpi(List<String> taskUserIds) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskKpiDo.class, "tk")
                .clearSelect()
                .select("tk.task_user_id,tk.company_id,tk.kpi_item_id,tk.scorer_obj_id")
                .setRsType(PerfEvaluateTaskKpiDo.class)
                .whereEq("tk.is_deleted", Boolean.FALSE.toString())
                .whereIn("tk.task_user_id", taskUserIds)
                .groupBy("tk.task_user_id,tk.kpi_item_id");
        return autoBaseDao.listAll(comQB);
    }

    public EvalMultistageSupScorePo queryMultistageSupScore(TenantId companyId, String taskUserId) {
        CycleEval taskBase = getMergeTaskBase(companyId, taskUserId);
        //PerfEvaluateTaskBaseModel taskBase = autoBaseDao.findById(PerfEvaluateTaskBaseModel.class, taskId);
        //上级是同时评分的就不用返回，不展示
//        if (taskBase.superiorInSame() || taskBase.isCustom()) {
//            return new EvalMultistageSupScorePo();
//        }
        if (taskBase.isCustom()) {
            return new EvalMultistageSupScorePo();
        }
        ComQB<PerfEvaluateTaskAuditModel> audit = ComQB.build(PerfEvaluateTaskAuditModel.class)
                .whereEqReq("companyId", companyId.getId())
                .whereEqReq("taskUserId", taskUserId)
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        List<PerfEvaluateTaskAuditModel> audits = autoBaseDao.listAll(audit);
        audits = audits.stream().filter(a -> Objects.nonNull(a.getApprovalOrder())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(audits)) {
            return new EvalMultistageSupScorePo();
        }

        ComQB<PerfEvaluateTaskSupScoreResult> comQB = ComQB.build(PerfEvaluateTaskKpiDo.class, "item")
                .join(PerfEvaluateTaskScoreResultModel.class, "r")
                .appendOn("item.kpi_item_id=r.kpi_item_id and item.company_id=r.company_id and item.task_user_id=r.task_user_id ")
                .clearSelect()
                .select(" r.*,item.kpi_type_weight,item.item_weight,item.multiple_reviewers_type")
                .setRsType(PerfEvaluateTaskSupScoreResult.class)
                .whereEq("item.is_deleted", Boolean.FALSE.toString())
                .whereEq("r.is_deleted", Boolean.FALSE.toString())
                .whereEq("r.company_id", companyId.getId())
                .whereEq("item.company_id", companyId.getId())
                .whereEqReq("r.task_user_id", taskUserId)
                .whereEq("r.scorer_type", "superior_score")
                .appendWhere("r.emp_id =item.emp_id")
                .appendWhere("ifnull(1=1,item.scorer_type != 'auto')");

        return new EvalMultistageSupScorePo(autoBaseDao.listAll(comQB), taskBase, audits);
    }

    public PagedList<CyclePo> pagedCycle(CycleQuery query) {
        final ComQB comQB = ComQB.build(CycleDo.class, "c");
        if (query.getShowEmptyCycle()) {
            comQB.leftJoin(PerfEvaluateTaskBaseDo.class, "b");
        } else {
            comQB.join(PerfEvaluateTaskBaseDo.class, "b");
        }
        comQB.appendOn("c.company_id=b.company_id and c.id=b.cycle_id and b.task_status = 'published' ")
                .clearSelect().select("c.*").setRsType(CyclePo.class)
                .whereEq("c.cycle_status", CycleStatusEnum.NORMAL.getType())
                .whereEq("c.company_id", query.getTenantId().getId())
                .whereLike("c.name", query.getName())
                .whereEq("c.year", query.getYear())
                .appendWhere("(b.is_new_emp = 0 or b.is_new_emp is null)");
        if (query.getIsMyCreated() != null && query.getIsMyCreated()) {
            comQB.whereEq("b.created_user", query.getCreatedUser());
        }
        if (!query.getShowEmptyCycle()) {
            comQB.whereEq("b.is_deleted", Boolean.FALSE.toString());
        }
        if (StringUtils.isNotBlank(query.getType())) {
            comQB.whereIn("c.type", Arrays.asList(query.getType().split(",")));
        }
        comQB.groupBy("c.id");
        comQB.orderByDesc("c.id")
                .setPage(query.getPageNo(), query.getPageSize());
        PagedList<CyclePo> list = autoBaseDao.listPage(comQB);
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        if (query.getIsMyCreated() != null && query.getIsMyCreated()) {
            cycleEmpCnt(list, query.getTenantId().getId(), new EmpId(query.getCreatedUser()));
        }
        //周期创建人
        cycleCntAndCreatedName(list, query.getTenantId().getId());
        return list;
    }

    public PagedList<CyclePo> pagedCycleOrderByCreatedTime(CycleQuery query) {
        final ComQB comQB = ComQB.build(CycleDo.class, "c");
        if (query.getShowEmptyCycle()) {
            comQB.leftJoin(PerfEvaluateTaskBaseDo.class, "b");
        } else {
            comQB.join(PerfEvaluateTaskBaseDo.class, "b");
        }
        comQB.appendOn("c.company_id=b.company_id and c.id=b.cycle_id and b.task_status = 'published' ")
                .clearSelect().select("c.*").setRsType(CyclePo.class)
                .whereEq("c.cycle_status", CycleStatusEnum.NORMAL.getType())
                .whereEqReq("c.company_id", query.getTenantId().getId())
                .whereLike("c.name", query.getName())
                .whereEq("c.year", query.getYear())
                .appendWhere("(b.is_new_emp = 0 or b.is_new_emp is null)");
        if (query.getIsMyCreated() != null && query.getIsMyCreated()) {
            comQB.whereEq("b.created_user", query.getCreatedUser());
        }
        if (!query.getShowEmptyCycle()) {
            comQB.whereEq("b.is_deleted", Boolean.FALSE.toString());
        }
        if (StringUtils.isNotBlank(query.getType())) {
            comQB.whereIn("c.type", Arrays.asList(query.getType().split(",")));
        }
        comQB.groupBy("c.id");
        comQB.orderByDesc("c.created_time")
                .setPage(query.getPageNo(), query.getPageSize());
        PagedList<CyclePo> list = autoBaseDao.listPage(comQB);
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        if (query.getIsMyCreated() != null && query.getIsMyCreated()) {
            cycleEmpCnt(list, query.getTenantId().getId(), new EmpId(query.getCreatedUser()));
        }
        //周期创建人
        cycleCntAndCreatedName(list, query.getTenantId().getId());
        return list;
    }


    public void cycleCntAndCreatedName(PagedList<CyclePo> list, String companyId) {
        //saveCycleEmpCnt(list, companyId, null);
        List<String> createdUserIds = list.getData().stream().map(li -> li.getCreatedUser()).collect(Collectors.toList());
        ComQB emp = ComQB.build(EmployeeBaseInfoDo.class)
                .whereEq("company_id", companyId)
                .whereIn("employee_id", createdUserIds);
        List<EmployeeBaseInfoDo> emps = autoBaseDao.listAll(emp);
        if (CollectionUtils.isEmpty(emps)) {
            return;
        }
        Map<String, List<EmployeeBaseInfoDo>> empMap = emps.stream().collect(Collectors.groupingBy(e -> e.getEmployeeId()));
        list.getData().forEach(li -> {
            List<EmployeeBaseInfoDo> empList = empMap.get(li.getCreatedUser());
            if (CollectionUtil.isNotEmpty(empList)) {
                li.setCreatedName(empList.get(0).getName());
            }
        });
    }


    public PagedList<CyclePo> pagedMyStartCycle(CycleQuery query) {
        final ComQB qb = ComQB.build(CycleDo.class, "c")
                .join(PerfEvaluateTaskBaseDo.class, "tb")
                .appendOn("c.company_id = tb.company_id and c.id = tb.cycle_id")
                .clearSelect().select(" distinct c.* ").setRsType(CyclePo.class)
                .whereEq("tb.is_deleted", Boolean.FALSE.toString())
                .whereEq("c.company_id", query.getTenantId().getId())
                .whereBigEq("c.eval_cnt", 0)
                .whereEq("tb.task_status", BusinessConstant.PUBLISHED)
                .whereEq("c.cycle_status", CycleStatusEnum.NORMAL.getType())
                .whereLike("c.name", query.getName());
        if (query.getStartor() != null) {
            qb.whereEq("tb.created_user", query.getStartor().getId());
        }
        if (StringUtils.isNotBlank(query.getType())) {
            qb.whereIn("c.type", Arrays.asList(query.getType().split(",")));
        }
        qb.orderByDesc("c.id");
        qb.groupBy(" c.id");
        qb.setPage(query.getPageNo(), query.getPageSize());
        PagedList<CyclePo> list = autoBaseDao.listPage(qb);
        if (CollectionUtils.isEmpty(list.getData())) {
            return list;
        }
        //统计周期中有多少考核人
        cycleEmpCnt(list, query.getTenantId().getId(), query.getStartor());
        return list;
    }

    //普通人看到的周期里的人数，需要重新统计一下
    public void cycleEmpCnt(PagedList<CyclePo> list, String companyId, EmpId empId) {
        List<String> cycleIds = list.stream().map(li -> li.getId()).collect(Collectors.toList());
        ComQB comQB = ComQB.buildDiff(TaskRefUser.class, "v_cycle_emp")
                .whereEq("company_id", companyId)
                .whereIn("cycle_id", cycleIds);
        if (empId != null) {
            comQB.whereEq("created_user", empId.getId());
        }
        List<TaskRefUser> taskRefUsers = autoBaseDao.listAll(comQB);
        if (CollectionUtils.isEmpty(taskRefUsers)) {
            return;
        }
        Map<String, List<TaskRefUser>> refMaps = taskRefUsers.stream()
                .collect(Collectors.groupingBy(ref -> ref.getCycleId()));
        for (CycleDo li : list) {
            List<TaskRefUser> cnts = refMaps.get(li.getId());
            if (CollectionUtils.isEmpty(cnts)) {
                li.setEvalCnt(0);
                continue;
            }
            li.setEvalCnt(cnts.size());
        }
    }

    public PagedList<CycleTaskUserPo> pagedUserEvalOnCycle(OnCycleQuery query) {

        final ComQB selOrgName = ComQB.build(EmpOrganizationDo.class, "o")
                .clearSelect().select("GROUP_CONCAT(o.org_name)")
                .whereEq("o.company_id", query.getTenantId().getId())
                .whereInQ("o.org_id",
                        ComQB.build(EmpRefOrgDo.class).clearSelect()
                                .select("org_id")
                                .appendWhere("emp_id = e.employee_id and ref_type = 'org'"));


        final ComQB selRoleName = ComQB.build(RoleModel.class, "r")
                .clearSelect().select(" GROUP_CONCAT(r.role_name)")
                .whereEq("r.company_id", query.getTenantId().getId())
                .appendWhere(" r.role_type='ding' and r.is_deleted='false'")
                .whereInQ("r.id",
                        ComQB.build(RoleRefEmpModel.class)
                                .clearSelect().select("role_id")
                                .appendWhere("emp_id = e.employee_id  and  is_deleted='false'"));

        //负责人
        final ComQB selReviewer = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "r")
                .leftJoin(EmployeeBaseInfoDo.class, "i").appendOn(" r.scorer_id = i.employee_id and r.company_id = i.company_id")
                .clearSelect().select("  GROUP_CONCAT(DISTINCT CONCAT_WS('&', i.`name`, i.`status`)) ")
                .whereEq("r.company_id", query.getTenantId().getId())
                .appendWhere("r.task_id = u.task_id")
                .appendWhere(" r.emp_id = u.emp_id and  r.audit_status is null  and r.is_deleted='false'");


        final ComQB qb = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .leftJoin(PerfEvaluateTaskBaseDo.class, "t").appendOn("u.company_id = t.company_id and u.task_id = t.id")
                .leftJoin(EmployeeBaseInfoDo.class, "e").appendOn(" u.company_id=e.company_id and  u.emp_id = e.employee_id")
                .clearSelect().select(" u.company_id,u.task_status,u.evaluation_level,u.final_score ")
                .select(" e.name as emp_name,e.avatar,e.status as emp_status,t.task_name")
                .select(String.format("(%s ) as %s", selOrgName.getSql(), " org_name "))
                .select(String.format("(%s ) as %s", selRoleName.getSql(), " role_name "))
                .select(String.format("(%s ) as %s", selReviewer.getSql(), " reviewers_emp "))
                .appendWhere("u.is_deleted = 'false'")
                .whereEq(" u.company_id", query.getTenantId().getId())
                .appendWhere("u.task_status is not null")
                .appendWhere(" t.is_deleted = 'false'")
                .whereIn(" u.task_id ", query.getTaskIds())
                .setPage(query.getPageNo(), query.getPageSize());
        if (StrUtil.isNotBlank(query.getOrgId())) {
            qb.appendWhere(" exists( select org_id from emp_ref_org where emp_id = u.emp_id and ref_type = 'org'and org_id = '"
                    + query.getOrgId() + "')");
        }
        if (StrUtil.isNotBlank(query.getRoleId())) {
            qb.appendWhere(" exists(  select role_id from role_ref_emp where emp_id = u.emp_id and task_name = 'ding' and role_id = '"
                    + query.getRoleId() + "')");
        }
        qb.setRsType(CycleTaskUserPo.class);
        return autoBaseDao.listPage(qb);
    }

    public PagedList<TaskBasePo> pagedTaskOnCycle(OnCycleQuery query) {

        final ComQB qb = ComQB.build(PerfEvaluateTaskBaseDo.class, "t")
                .join(EmployeeBaseInfoDo.class, "e").on("t.created_user", "e.employee_id")
                .clearSelect().select("t.id task_id,t.cycle_id,t.task_name,e.name as createdName" +
                        ",t.cycle_start_date,t.cycle_end_date,t.created_time")
                .setRsType(TaskBasePo.class)
                .whereEq("t.companyId", query.getTenantId().getId())
                .whereEq("t.cycleId", query.getCycleId())
                .whereEq("t.task_status", BusinessConstant.PUBLISHED)
                .whereIn("t.id", query.getTaskIds())
                .appendWhere("t.is_deleted='false'")
                .whereLike("t.task_name", query.getTaskName());
        if (query.getIsMyCreated() != null && query.getIsMyCreated()) {
            qb.whereEq("t.created_user", query.getCreatedUser());
        }
        qb.setPage(query.getPageNo(), query.getPageSize());
        PagedList<TaskBasePo> taskBasePos = autoBaseDao.listPageDomain(qb, TaskBasePo.class);
        if (CollectionUtils.isEmpty(taskBasePos.getData())) {
            return taskBasePos;
        }
        //考核任务下面的人
        List<String> taskIds = taskBasePos.getData().stream().map(task -> task.getTaskId()).collect(Collectors.toList());
        ComQB taskUsers = ComQB.build(PerfEvaluateTaskUserModel.class)
                .clearSelect().select("task_id,emp_id")
                .setRsType(PerfEvaluateTaskUserModel.class)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereIn("task_id", taskIds);
        List<PerfEvaluateTaskUserModel> users = autoBaseDao.listAll(taskUsers);
        if (CollectionUtil.isEmpty(users)) {
            return taskBasePos;
        }
        Map<String, List<PerfEvaluateTaskUserModel>> taskIdGroup = users.stream().collect(Collectors.groupingBy(user -> user.getTaskId()));
        for (TaskBasePo task : taskBasePos.getData()) {
            List<PerfEvaluateTaskUserModel> taskUserModels = taskIdGroup.get(task.getTaskId());
            if (CollectionUtil.isEmpty(taskUserModels)) {
                continue;
            }
            List<String> empIds = taskUserModels.stream().map(taskUser -> taskUser.getEmpId()).collect(Collectors.toList());
            task.setEmpId(empIds);
        }
        return taskBasePos;
    }

    public Map<String, ItemDecompose> listItemMonthDecompose(TenantId companyId,
                                                             List<String> empIds,
                                                             List<String> kpiItemIds,
                                                             Integer year, Integer month) {
        Map<String, ItemDecompose> itemMap = new HashMap<>();
        if (CollUtil.isEmpty(kpiItemIds)) {
            return itemMap;
        }
        ComQB comQB = ComQB.build().select("kpi_item_id,emp_id,month" + month + " as value")
                .addFromTable("company_item_decompose", "de")
                .setRsType(ItemDecompose.class)
                .whereEq("company_id", companyId.getId())
                .whereEq("is_deleted", "false")
                .whereEq("status", CompanyItemDecomposeStatusEnum.CONFIRMED.getStatus())
                .whereEq("year", year)
                .whereIn("kpi_item_id", kpiItemIds)
                .whereIn("emp_id", empIds);
        final List<ItemDecompose> rs = autoBaseDao.listAll(comQB);
        if (rs.isEmpty()) {
            return itemMap;
        }
        CollUtil.toMap(rs, itemMap, ItemDecompose::asKey);
        return itemMap;
    }

    //
//    select ev.emp_name,ev.emp_id,ev.final_score,ev.evaluation_level,
//    tb.cycle_start_date,tb.cycle_end_date ,tb.task_name,ev.task_status,tb.created_user
//    from perf_evaluate_task_user ev join perf_evaluate_task_base tb  on tb.company_id = ev.company_id and ev.task_id = tb.id
//    where tb.task_status !='draft' and ev.is_new_emp = 1;
    public PagedList<NewEmpTaskPo> pagedNewEmpTaskPo(NewEmpTaskQuery query) {
        final ComQB qb = ComQB.build(PerfEvaluateTaskUserDo.class, "ev")
                .join(PerfEvaluateTaskBaseDo.class, "tb").appendOn("tb.company_id = ev.company_id and ev.task_id = tb.id")
                .clearSelect().select("ev.id task_user_id, ev.task_id, ev.emp_name,ev.emp_id,ev.final_score,ev.evaluation_level,ev.task_status ")
                .select("tb.cycle_start_date,tb.cycle_end_date ,tb.task_name,tb.created_user,tb.is_auto ")
                .setRsType(NewEmpTaskPo.class)
                .whereEq("tb.companyId", query.getTenantId().getId())
                .appendWhere("tb.task_status !='draft' ")
                .whereEq("ev.task_status", query.getTaskStatus())
                .whereEq("ev.is_new_emp", 1)
                .appendWhere("ev.is_deleted ='false'")
                .whereLike("tb.task_name", query.getTaskName())
                .whereLike("ev.emp_name", query.getEmpName())
                .setPage(query.getPageNo(), query.getPageSize());
        if (StrUtil.isNotBlank(query.getOrderField())) {
            qb.orderBy(String.format("ev.%s  %s", query.getOrderField(), query.getOrderSort()));
        } else {
            qb.orderByDesc("ev.created_time");
        }
        return autoBaseDao.listPage(qb);
    }

    //新人入职已使用名额
    public Integer countNewEmpTaskUsedCnt(TenantId tenantId) {
        final ComQB qb = ComQB.build(PerfEvaluateTaskUserDo.class, "ev")
                .clearSelect().select("count(1) cnt").setRsType(Integer.class)
                .whereEq("company_id", tenantId.getId())
                .appendWhere("is_new_emp = 1");
        return autoBaseDao.findOne(qb);
    }

    public Boolean isTaskCreatedUser(TaskId taskId, EmpId empId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskBaseModel.class)
                .whereEq("id", taskId.getId())
                .whereEq("is_deleted", Boolean.FALSE.toString());
        PerfEvaluateTaskBaseModel task = autoBaseDao.findOne(comQB);
        if (task == null) {
            return false;
        }
        return empId.getId().equals(task.getCreatedUser());
    }

    public List<String> listCycleType(TenantId tenantId) {
        ComQB comQB = ComQB.build(CycleDo.class)
                .clearSelect().select("type").setRsType(String.class)
                .whereEq("company_id", tenantId.getId())
                .whereEq("cycle_status", CycleStatusEnum.NORMAL.getType())
                .groupBy(" type ");
        return autoBaseDao.listAll(comQB);
    }

    public PagedList<OpenTaskBasePo> pagedOpenTaskBase(CycleQuery query) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskBaseDo.class, "b")
                .leftJoin(CycleDo.class, "c")
                .appendOn("b.company_id = c.company_id and b.cycle_id = c.id")
                .clearSelect()
                .select("b.id as taskId, b.task_name as taskName,b.task_status as taskStatus, ifnull(c.type,b.cycle_type) as cycleType, ifnull(c.cycle_start,b.cycle_start_date) as cycleStart, ifnull(c.cycle_end,b.cycle_end_date) as cycleEnd")
                .setRsType(OpenTaskBasePo.class)
                .whereEq("b.company_id", query.getTenantId().getId())
                .whereLike("b.task_name", query.getName())
                .whereEq("b.cycle_id", query.getCycleId())
                .whereEq("b.is_deleted", "false")
                .orderByDesc("b.created_time")
                .setPage(query.getPageNo(), query.getPageSize());
        return this.autoBaseDao.listPage(comQB);
    }

    public PagedList<OpenTalentEvalPo> pagedOpenTalentEval(String companyId, String taskId, int pageNo, int pageSize) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn("u.company_id=e.company_id and u.emp_id=e.employee_id")
                .clearSelect()
                .select("e.employee_id as empId, e.ding_user_id as dingId, e.name as empName, u.final_score as score, " +
                        "u.evaluation_level as level,u.perf_coefficient as coef, u.task_status as status, u.emp_org_name as empOrgName, score_of_ref as scoreOfRef ")
                .select("reviewers_json reviewers , u.id eval_id,u.cycle_id")
                .setRsType(OpenTalentEvalPo.class)
                .whereEq("u.company_id", companyId)
                .whereEq("u.task_id", taskId)
                .orderByDesc("u.created_time")
                .setPage(pageNo, pageSize);
        return this.autoBaseDao.listPage(comQB);
    }

    private void buildEvaluationStaffDingUserId(PerfEvaluateTaskDraftTaskInfoPo po) {
        if (StrUtil.isNotBlank(po.getEvaluationStaff())) {
            List<EvaluationStaffVO> staffVOList = JSON.parseArray(po.getEvaluationStaff(), EvaluationStaffVO.class);
            List<EvaluationStaffVO> deptStaffs = staffVOList.stream().filter(staffVO ->
                    StaffItemType.dept.name().equals(staffVO.getObj_type())).collect(Collectors.toList());
            List<EvaluationStaffVO> userStaffs = staffVOList.stream().filter(staffVO ->
                    StaffItemType.user.name().equals(staffVO.getObj_type())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(deptStaffs)) {
                EvaluationStaffVO evaluationStaffVO = deptStaffs.get(0);
                List<String> orgIds = po.getObjIds(evaluationStaffVO);
                if (CollUtil.isNotEmpty(orgIds)) {
                    Map<String, EmpOrganizationDo> map = listOrg(po.getCompanyId(), orgIds);
                    evaluationStaffVO.getObjItems().forEach(item -> {
                        EmpOrganizationDo organizationDo = map.get(item.getObjId());
                        if (Objects.nonNull(organizationDo)) {
                            item.setDingOrgId(organizationDo.getDingOrgId());
                        }
                    });
                    po.setEvaluationStaff(JSONObject.toJSONString(staffVOList));
                }
            }
            if (CollUtil.isNotEmpty(userStaffs)) {
                EvaluationStaffVO evaluationStaffVO = userStaffs.get(0);
                List<String> userIds = po.getObjIds(evaluationStaffVO);
                if (CollUtil.isNotEmpty(userIds)) {
                    List<EmployeeBaseInfoDo> emps = listEmpByIds(userIds);
                    Map<String, EmployeeBaseInfoDo> map = CollUtil.toMap(emps, new HashMap<>(), e -> e.getEmployeeId());
                    evaluationStaffVO.getObjItems().forEach(item -> {
                        EmployeeBaseInfoDo emp = map.get(item.getObjId());
                        if (Objects.nonNull(emp)) {
                            item.setDingUserId(emp.getDingUserId());
                        }
                    });
                    po.setEvaluationStaff(JSONObject.toJSONString(staffVOList));
                }
            }
        }
        if (StrUtil.isNotBlank(po.getExcludeStaff())) {
            List<EmpStaff> empStaffs = JSONObject.parseArray(po.getExcludeStaff(), EmpStaff.class);
            List<String> empIdList = empStaffs.stream().map(emp -> emp.getEmpId()).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(empIdList)) {
                List<EmployeeBaseInfoDo> emps = listEmpByIds(empIdList);
                Map<String, EmployeeBaseInfoDo> empMap = CollUtil.toMap(emps, new HashMap<>(), emp -> emp.getEmployeeId());
                empStaffs.forEach(emp -> {
                    EmployeeBaseInfoDo empDo = empMap.get(emp.getEmpId());
                    if (Objects.nonNull(empDo)) {
                        emp.setDingUserId(empDo.getDingUserId());
                    }
                });
                po.setExcludeStaff(JSONObject.toJSONString(empStaffs));
            }
        }
    }

    private List<EmployeeBaseInfoDo> listEmpByIds(List<String> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        ComQB comQB = ComQB.build(EmployeeBaseInfoDo.class)
                .whereIn("employee_id", userIds);
        return autoBaseDao.listAll(comQB);
    }

    private Map<String, EmpOrganizationDo> listOrg(String companyId, List<String> orgIds) {
        ComQB comQB = ComQB.build(EmpOrganizationDo.class)
                .clearSelect().select("org_id,ding_org_id")
                .whereEqReq("company_id", companyId)
                .whereEq("status", "valid")
                .whereIn("org_id", orgIds);
        List<EmpOrganizationDo> orgs = autoBaseDao.listAll(comQB);
        if (CollUtil.isEmpty(orgs)) {
            return new HashMap<>();
        }
        return CollUtil.toMap(orgs, new HashMap<>(), r -> r.getOrgId());
    }

    //考核任务下面的人 是不是空的
    public Set<String> excludeHasEmpTaskId(String tenantId, Set<String> taskIds) {
        Set<String> hasEmpTaskIds = new HashSet();
        ComQB comQB = ComQB.build(PerfEvaluateTaskBaseDo.class, "b")
                .join(PerfEvaluateTaskUserModel.class, "u")
                .appendOn("b.company_id=u.company_id and b.id=u.task_id")
                .clearSelect().select("b.id").setRsType(String.class)
                .whereEq("b.is_deleted", Boolean.FALSE.toString())
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereEq("b.company_id", tenantId)
                .whereIn("b.id", taskIds);
        List<String> taskIdList = autoBaseDao.listAll(comQB);
        hasEmpTaskIds.addAll(taskIdList);
        LoggerFactory.getLogger(getClass()).info("hasEmpTaskIds=={}", JSON.toJSONString(hasEmpTaskIds));
        LoggerFactory.getLogger(getClass()).info("taskIdList=={}", JSON.toJSONString(taskIdList));
        LoggerFactory.getLogger(getClass()).info("taskIds=={}", JSON.toJSONString(taskIds));
        taskIds.removeAll(hasEmpTaskIds);
        return taskIds;
    }

    public List<String> listTaskName(List<String> taskNames, String companyId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskBaseDo.class)
                .clearSelect().select("task_name").setRsType(String.class)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEq("company_id", companyId)
                .whereIn("task_name", taskNames);
        return autoBaseDao.listAll(comQB);
    }

    public PagedList<PerfEvaluateTaskBaseVO> pagedMyManagerTask(EvaluationTaskUserQueryVO query) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskBaseDo.class, "t")
                .leftJoin(CycleDo.class, "c")
                .appendOn(" t.company_id=c.company_id and t.cycle_id=c.id and c.cycle_status='" + CycleStatusEnum.NORMAL.getType() + "'")
                .clearSelect().select("t.id,t.company_id,t.cycle_id, t.task_name,t.cycle_start_date, t.cycle_end_date,t.task_desc,t.cycle_type,c.name as cycleName")
                .setRsType(PerfEvaluateTaskBaseVO.class)
                .whereEq("t.is_deleted", Boolean.FALSE.toString())
                .whereEq("t.company_id", query.getCompanyId())
                .whereEq("t.task_status", query.getTaskStatus())
                .whereEq("t.created_user", query.getCreatedUser())
                .whereInReq("t.task_status", Arrays.asList(BusinessConstant.PUBLISHED, BusinessConstant.PUBLISHING))
                .whereLike("t.task_name", query.getKeyword())
                .whereEq("t.cycle_type", query.getCycleType())
                .appendWhere("(t.is_new_emp = 0 or t.is_new_emp is null)")
                .orderByDesc("t.created_time")
                .setPage(query.getPageNo(), query.getPageSize());
        PagedList<PerfEvaluateTaskBaseVO> taskVos = autoBaseDao.listPage(comQB);
        if (CollectionUtil.isEmpty(taskVos.getData())) {
            return taskVos;
        }
        List<String> taskIds = taskVos.getData().stream().map(task -> task.getId()).collect(Collectors.toList());
        ComQB taskUser = ComQB.build(PerfEvaluateTaskUserModel.class)
                .clearSelect().select(" task_id,count(1) as cnt").setRsType(TaskEmpCnt.class)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEq("company_id", query.getCompanyId())
                .whereIn("task_id", taskIds)
                .groupBy(" task_id");
        List<TaskEmpCnt> taskEmpCnts = autoBaseDao.listAll(taskUser);
        Map<String, List<TaskEmpCnt>> taskIdGroup = taskEmpCnts.stream().collect(Collectors.groupingBy(task -> task.getTaskId()));
        taskVos.getData().forEach(task -> {
            List<TaskEmpCnt> empCnt = taskIdGroup.get(task.getId());
            if (CollectionUtil.isNotEmpty(empCnt)) {
                task.setEmpNum(empCnt.get(0).getCnt());
            }
        });
        return taskVos;
    }

    public ListWrap<PerfEvaluateTaskBaseDo> listTaskByIds(List<String> taskIdList, String companyId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskBaseDo.class)
                .clearSelect().select("id,cycle_id,company_id,task_name").setRsType(PerfEvaluateTaskBaseDo.class)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEq("company_id", companyId)
                .whereInReq("id", taskIdList);
        List<PerfEvaluateTaskBaseDo> rs = autoBaseDao.listAll(comQB);
        return new ListWrap<>(rs).groupBy(PerfEvaluateTaskBaseDo::getId);
    }

    public PerfEvaluateTaskBaseDo getTaskById(TaskId taskId) {
        return autoBaseDao.findById(PerfEvaluateTaskBaseDo.class, taskId.getId());
    }

    /**
     * 替换 List<PerfEvaluateTaskScoreVO> taskScoreList = taskUserManager.queryScoreTaskUser(companyId, taskId, taskUserId, taskUserIdList);
     *
     * @param companyId
     * @param taskUserIds
     * @return
     */
    public List<EvalUser> listConfirmedTaskUser(TenantId companyId, List<String> taskUserIds) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .whereEqReq("u.company_id", companyId.getId())
                .whereInReq("u.id", taskUserIds)
                .whereIn("u.task_status", Arrays.asList("confirmed", "finishValueAudit"))
                .appendWhere("u.is_deleted = 'false' ");
        List<EvalUser> users = autoBaseDao.listAllDomain(comQB, EvalUser.class);

        ComQB kpiQ = ComQB.build(PerfEvaluateTaskKpiDo.class, "k")
                .whereEqReq("k.company_id", companyId.getId())
                .whereInReq("k.taskUserId", taskUserIds)
                .appendWhere("k.is_deleted ='false'");
        List<EvalKpi> items = autoBaseDao.listAllDomain(kpiQ, EvalKpi.class);
        Map<String, List<EvalKpi>> groups = items.stream().collect(Collectors.groupingBy(item -> item.getTaskUserId()));

        //EmpEvalRule
        ComQB empRuleQ = ComQB.build(EmpEvalRuleDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("emp_eval_id", taskUserIds)
                .appendWhere("is_deleted ='false'");
        List<EmpEvalRule> empRules = autoBaseDao.listAllDomain(empRuleQ, EmpEvalRule.class);
        Map<String, EmpEvalRule> empRuleMap = CollUtil.toMap(empRules, new HashMap<>(), EmpEvalRule::getEmpEvalId);

        users.forEach(taskUser -> {
            EmpEvalRule evalRule = empRuleMap.get(taskUser.getId());
            taskUser.setEmpEvalRule(evalRule);
            taskUser.setKpis(groups.get(taskUser.getId()));
        });

        return users;
    }

    //public CycleEval getTaskWithAdminRule(TenantId tenantId, String taskUserId) {
    //    CycleEval mergeTask = getMergeTaskBase(tenantId, taskUserId);
    //    if (mergeTask.isTmpTask()) {
    //        return mergeTask;
    //    }
    //    ComQB adminQb = ComQB.build(AdminTaskDo.class, "b")
    //            .whereEqReq("b.company_id", tenantId.getId())
    //            .whereEqReq("b.id", mergeTask.getId());
    //    AdminTaskDo adminTask = autoBaseDao.findOne(adminQb);
    //    mergeTask.setScoreSortConf(adminTask.getScoreSortConf());
    //    return mergeTask;
    //}

    public CycleEval getTaskBase(TenantId companyId, String taskId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskBaseDo.class)
                .setRsType(TaskBaseForStrDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("id", taskId);
        TaskBaseForStrDo one = autoBaseDao.findOne(comQB);
        //数据兼容, json串里有json串
        CycleEval convert = Convert.convert(CycleEval.class, one);
        convert.setTemplBaseJson(JSONUtil.parseObj(one.getTemplBaseJson()).toBean(ExamGroup.class));
        convert.setCycleType(new CycleType(one.getCycleType()));
        convert.setCompanyId(companyId);
        convert.setTaskName(new Name(one.getTaskName()));
        convert.setCreatedUser(new EmpId(one.getCreatedUser()));
        convert.setTemplInitiateJson(one.parseInitiate());
        return convert;
    }

    //查询成旧的任务格式用于流程运行
    public CycleEval getMergeTaskBase(TenantId companyId, String taskUserId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskBaseDo.class, "b")
                .join(PerfEvaluateTaskUserDo.class, "u").appendOn("b.company_id = u.company_id and b.id = u.task_id")
                .clearSelect().select("b.*")
                .whereEqReq("u.company_id", companyId.getId())
                .whereEqReq("u.id", taskUserId);
        PerfEvaluateTaskBaseDo data = autoBaseDao.findOne(comQB);
        if (Objects.isNull(data)) {
            return null;
        }
        //数据兼容, json串里有json串
        CycleEval domain = Convert.convert(CycleEval.class, data);
        domain.setCycleType(new CycleType(data.getCycleType()));
        domain.setCompanyId(companyId);
        domain.setTaskName(new Name(data.getTaskName()));
        domain.setCreatedUser(new EmpId(data.getCreatedUser()));
        //旧任务
        if (StrUtil.isNotBlank(data.getTemplBaseJson())) {
            data.appendJsonTo(domain);
            data.baseUpdate(domain);
            domain.markTmpTask();
            return domain;
        }
        //考核表格式
        ComQB ruleQ = ComQB.build(EmpEvalRuleDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("emp_eval_id", taskUserId)
                .appendWhere("is_deleted ='false'");
        EmpEvalRule empEvalRule = this.autoBaseDao.findDomain(ruleQ, EmpEvalRule.class);
        if (empEvalRule == null) {
            return domain;
        }
        domain.setEvaluateType(empEvalRule.getEvaluateType());
        domain.setAuditResult(empEvalRule.getAuditResult());
        domain.setInterviewConf(empEvalRule.getInterviewConf());
        //转成旧的模板任务
        appendEvaluateInfo(domain, empEvalRule);
        return domain;
    }

    private void appendEvaluateInfo(CycleEval convert, EmpEvalRule rule) {
        JSONObject evaluateJson = (JSONObject) JSONObject.toJSON(rule.getEnterScore());
        PerfTemplEvaluate evaluate = evaluateJson.toJavaObject(PerfTemplEvaluate.class);
        evaluate.setEvaluateType(rule.getEvaluateType());
        evaluate.setIsAddAuditComment(rule.getAuditResult().getCommentReq());
        evaluate.setAuditFlag(rule.getAuditResult().isOpen() + "");
        toTempEvaluateInfo(evaluate, rule, convert);
        //if (!rule.isCustom()) {//不是自定的加上
        //    toTempEvaluateInfo(evaluate, rule);
        //}
        //兼容成旧数据格式.
        AffirmTaskConf confirmTask = rule.getConfirmTask();
        JSONObject affirm = (JSONObject) JSONObject.toJSON(confirmTask);
        affirm.remove("auditNodes");
        affirm.put("auditList", JSON.toJSON(confirmTask.getAuditNodes()));

        convert.setTemplAffirmJson(affirm.toJavaObject(PerfTemplEvaluateAffirm.class));

        convert.setTemplEvaluateJson(evaluate);
        JSONObject templBase = new JSONObject();
        if (rule.getConfirmResult() != null) {
            templBase.putAll(rule.getConfirmResult().asTempJson());
        }
        if (rule.getTypeWeightConf() != null) {
            templBase.putAll(rule.getTypeWeightConf().asTempJson());
        }
        if (rule.getScoreValueConf() != null) {
            templBase.putAll(rule.getScoreValueConf().asTempJson());
        }
        convert.setTemplBaseJson(templBase.toJavaObject(ExamGroup.class));
        //结果确认
        convert.setConfirmResult(rule.getConfirmResult());
        //公示配置
        if (rule.getPublishResult() != null && rule.getPublishResult().isOpen()) {
            convert.setPublicToEmp(rule.getPublishResult().getToEmps());
            convert.setPublicEmpJson(rule.getPublishResult().getOpEmps());
            String type = rule.getPublishResult().getType();
            convert.setPublicType(PublicTypeEnum.typeOf(type));
        } else {
            convert.setPublicType(PublicTypeEnum.notPublic);
        }
    }

    private void appendCustomEvaluateInfo(PerfTemplEvaluate evaluate, EmpEvalRule rule) {
        EvalItemScoreRule selfItemRule = rule.itemRule(EvaluateAuditSceneEnum.SELF_SCORE.getScene());
        evaluate.setSelfScoreFlag(Boolean.FALSE.toString());
        evaluate.setSuperiorScoreFlag(Boolean.FALSE.toString());
        evaluate.setPeerScoreFlag(Boolean.FALSE.toString());
        evaluate.setSubScoreFlag(Boolean.FALSE.toString());
        if (Objects.nonNull(selfItemRule)) {
            evaluate.setSelfScoreFlag(selfItemRule.needSelfScore() + "");
            evaluate.setSelfScoreWeight(selfItemRule.getSelfRater().getNodeWeight());
            evaluate.setSelfScoreRule(selfItemRule.getSelfRater().getRateMode());
            evaluate.setSelfScoreViewRule(JSONUtil.toJsonStr(rule.getScoreView().getSelfScoreViewRule()));
        }

        EvalItemScoreRule superItemRule = rule.itemRule(EvaluateAuditSceneEnum.SUPERIOR_SCORE.getScene());
        if (Objects.nonNull(superItemRule)) {
            evaluate.setSelfScoreFlag(superItemRule.needSupScore() + "");
            evaluate.setSelfScoreWeight(superItemRule.getSuperRater().getNodeWeight());
            evaluate.setSelfScoreRule(superItemRule.getSuperRater().getRateMode());
            evaluate.setSelfScoreViewRule(JSONUtil.toJsonStr(rule.getScoreView().getSelfScoreViewRule()));
        }
    }

    private void toTempEvaluateInfo(PerfTemplEvaluate evaluate, EmpEvalRule rule, CycleEval convert) {
        if (rule.getScoreView() != null) {
            evaluate.setSelfScoreViewRule(JSONUtil.toJsonStr(convert.getScoreView().getSelfScoreViewRule()));
            evaluate.setAppointScoreViewRule(JSONUtil.toJsonStr(convert.getScoreView().getAppointScoreViewRule()));
            evaluate.setMutualScoreViewRule(JSONUtil.toJsonStr(convert.getScoreView().getMutualScoreViewRule()));
            evaluate.setSuperiorScoreViewRule(JSONUtil.toJsonStr(convert.getScoreView().getSuperiorScoreViewRule()));

            evaluate.setAppointScoreAnonymous(convert.getScoreView().getAppointScoreAnonymous());
            evaluate.setMutualScoreAnonymous(convert.getScoreView().getMutualScoreAnonymous());
            evaluate.setSuperiorScoreAnonymous(convert.getScoreView().getSuperiorScoreAnonymous());
        }
        if (rule.isCustom()) {
            return;
        }
        //简易自评
        evaluate.setSelfScoreFlag(rule.getS3SelfRater().isOpen() ? "true" : "false");
        evaluate.setSelfScoreWeight(rule.getS3SelfRater().getNodeWeight());
        evaluate.setSelfScoreRule(rule.getS3SelfRater().getRateMode());
        //简易互评
        evaluate.setMutualScoreFlag((rule.getS3PeerRater().isOpen() || rule.getS3SubRater().isOpen()) ? "true" : "false");
        evaluate.setMutualScoreRule(rule.getS3PeerRater().getRateMode());
        if (rule.getS3PeerRater().isOpen()) {
            evaluate.setPeerScoreFlag(rule.getS3PeerRater().isOpen() ? "true" : "false");
            evaluate.setPeerScoreWeight(rule.getS3PeerRater().getNodeWeight());
        }
        if (rule.getS3SubRater().isOpen()) {
            evaluate.setSubScoreFlag(rule.getS3SubRater().isOpen() ? "true" : "false");
            evaluate.setSubScoreWeight(rule.getS3SubRater().getNodeWeight());
        }
        //简易上级评
        evaluate.setSuperiorScoreFlag(rule.getS3SuperRater().isOpen() ? "true" : "false");
        evaluate.setSuperiorScoreWeight(rule.getS3SuperRater().getNodeWeight());
        evaluate.setSuperiorScoreRule(rule.getS3SuperRater().getRateMode());
        evaluate.setSuperiorScoreOrder(rule.getS3SuperRater().getSuperiorScoreOrder());
    }

    public Map<String, CycleEval> listMergeTaskBaseAsMap(TenantId companyId, List<String> taskUserIds) {
        ComQB comQB = ComQB.build(MergeEvalTaskBaseDo.class, "b")
                .join(PerfEvaluateTaskUserDo.class, "u").appendOn("b.company_id = u.company_id and b.id = u.task_id")
                .clearSelect().select("b.*,u.id task_user_id")
                .whereEqReq("u.company_id", companyId.getId())
                .whereInReq("u.id", taskUserIds);
        List<MergeEvalTaskBaseDo> tmpTaskDos = autoBaseDao.listAll(comQB);
        if (tmpTaskDos.isEmpty()) {
            return Collections.emptyMap();
        }
        //考核表任务
        ComQB ruleQ = ComQB.build(EmpEvalRuleDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("emp_eval_id", taskUserIds)
                .appendWhere("is_deleted ='false'");
        List<EmpEvalRule> empEvalRules = this.autoBaseDao.listAllDomain(ruleQ, EmpEvalRule.class);

//        String cycleId = tmpTaskDos.get(0).getCycleId();
//        ComQB cycleQB = ComQB.build(CycleEvalRuleDo.class)
//                .whereEqReq("company_id", companyId.getId())
//                .whereEqReq("cycle_id", cycleId);
//        CycleEvalRule cycleEvalRule = autoBaseDao.findDomain(cycleQB, CycleEvalRule.class);
//
//        for (EmpEvalRule empEvalRule : empEvalRules) {
//            empEvalRule.copyCycleConf(cycleEvalRule);
//        }

        Map<String, EmpEvalRule> evalRuleMap = CollUtil.toMap(empEvalRules, new HashMap<>(), parameter -> parameter.getEvaluateType());
        Map<String, CycleEval> rs = new LinkedHashMap<>();
        tmpTaskDos.forEach(data -> {
            CycleEval domain = Convert.convert(CycleEval.class, data);
            domain.setCycleType(new CycleType(data.getCycleType()));
            domain.setCompanyId(companyId);
            domain.setTaskName(new Name(data.getTaskName()));
            domain.setCreatedUser(new EmpId(data.getCreatedUser()));
            if (data.wasFromTmpTask()) {
                data.appendJsonTo(domain);
                data.baseUpdate(domain);
            } else {
                //转成旧的模板任务
                EmpEvalRule evalRule = evalRuleMap.get(data.getTaskUserId());
                appendEvaluateInfo(domain, evalRule);
            }
            rs.put(data.getTaskUserId(), domain);
        });
        return rs;
    }

    public boolean hasNotImportType(TenantId companyId, String taskUserId) {
        ComQB comQB = ComQB.build(EmpEvalKpiTypeDo.class, "type")
                .leftJoin(PerfEvaluateTaskKpiDo.class, "item")
                .appendOn("type.task_user_id=item.task_user_id and type.kpi_type_id =item.kpi_type_id and item.is_deleted='false'")
                .clearSelect().select("count(1)").setRsType(int.class)
                .whereEq("type.is_deleted", Boolean.FALSE.toString())
                .whereEqReq("type.company_id", companyId.getId())
                .whereEqReq("type.task_user_id", taskUserId)
                //.whereEq("type.is_okr", Boolean.TRUE.toString())
                .appendWhere("(type.kpi_type_classify is null or type.kpi_type_classify = 'custom' or type.kpi_type_classify = 'plus' or type.kpi_type_classify = 'subtract' or type.kpi_type_classify = 'plusSub' or type.kpi_type_classify = 'oneVoteVeto') ")
                .appendWhere(" item.id is null");
        int cnt = autoBaseDao.findOne(comQB);
        return cnt > 0;
        // ComQB existQ = ComQB.build(PerfEvaluateTaskKpiDo.class, "k")
        //         .clearSelect().select("k.id")
        //         .whereEqReq("k.company_id", companyId.getId())
        //         .whereEqReq("k.task_user_id", taskUserId)
        //         .appendWhere("k.is_deleted = 'false'")
        //         .appendWhere("k.kpi_type_id = o.kpi_type_id");

        // ComQB comQB = ComQB.build(PerfEvaluateTaskOkrTypeDo.class, "o")
        //         .leftJoin(CompanyDicDo.class, "d")
        //         .appendOn("o.company_id = d.company_id  and o.kpi_type_id = d.id and d.is_deleted = 'false'")
        //         .clearSelect().select("count(1) cnt").setRsType(Integer.class)
        //         .whereEqReq("o.company_id", companyId.getId())
        //         .whereEqReq("o.task_user_id", taskUserId)
        //         .appendWhere("o.import_okr_flag = 'false'")
        //         .appendWhere("o.is_deleted = 'false'")
        //         .appendWhere("not exists (" + existQ.getSql() + ")");
        // comQB.getParams().putAll(existQ.getParams());
        // Integer cnt = autoBaseDao.findOne(comQB);
        // return cnt > 0;
    }

    /**
     * @author: lufei
     * @date: 2022/9/28 10:56
     * @see com.polaris.kpi.eval.domain.task.repo.TaskUserRepo#getTaskUser(TenantId, String)
     **/
    @Deprecated
    public EvalUser findPerfEvaluateTaskUser(TenantId companyId, String taskUserId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("id", taskUserId)
                .appendWhere("is_deleted ='false'");
        EvalUser taskUser = this.autoBaseDao.findDomain(comQB, EvalUser.class);
        Assert.notNull(taskUser, "missed_task_user: company_id={}, taskUserId={}",
                companyId.getId(), taskUserId);

        ComQB kpiComQB = ComQB.build(PerfEvaluateTaskKpiDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("taskUserId", taskUserId)
                .appendWhere("is_deleted='false'");
        List<EvalKpi> kpis = this.autoBaseDao.listAllDomain(kpiComQB, EvalKpi.class);

        if (CollUtil.isNotEmpty(kpis)) {
            taskUser.setKpis(kpis);
        }

        ComQB scoreComQb = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("taskUserId", taskUserId)
                .appendWhere(" (audit_status != 'transferred' or audit_status is null) ")
                .whereLike("scorer_type", "score")
                .appendWhere("is_deleted='false'");
        List<EvalScoreResult> scoreResults = this.autoBaseDao.listAllDomain(scoreComQb, EvalScoreResult.class);

        if (CollUtil.isNotEmpty(scoreResults)) {
            taskUser.setScoreResults(scoreResults);
        }

        ComQB scoreRuleComQb = ComQB.build(TaskAuditDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("taskUserId", taskUserId)
                .appendWhere("is_deleted='false'");
        List<EvalAudit> audits = this.autoBaseDao.listAllDomain(scoreRuleComQb, EvalAudit.class);

        if (CollUtil.isNotEmpty(audits)) {
            for (EvalAudit audit : audits) {
                if (EvaluateAuditSceneEnum.SUB_SCORE.getScene().equals(audit.getScene())) {
                    if (taskUser.getSubScorers() == null) {
                        taskUser.setSubScorers(new ArrayList<>());
                    }
                    taskUser.getSubScorers().add(audit);
                }
                if (EvaluateAuditSceneEnum.PEER_SCORE.getScene().equals(audit.getScene())) {
                    if (taskUser.getPeerScorers() == null) {
                        taskUser.setPeerScorers(new ArrayList<>());
                    }
                    taskUser.getPeerScorers().add(audit);
                }
                if (EvaluateAuditSceneEnum.SUPERIOR_SCORE.getScene().equals(audit.getScene())) {
                    if (taskUser.getSupperScorers() == null) {
                        taskUser.setSupperScorers(new ArrayList<>());
                    }
                    taskUser.getSupperScorers().add(audit);
                }
                if (EvaluateAuditSceneEnum.APPOINT_SCORE.getScene().equals(audit.getScene())) {
                    if (taskUser.getAppointScorers() == null) {
                        taskUser.setAppointScorers(new ArrayList<>());
                    }
                    taskUser.getAppointScorers().add(audit);
                }
            }
        }
        return taskUser;
    }

    public List<EvalScoreResult> listPassedEvalScoreResults(TenantId companyId, String taskUserId) {
        ComQB scoreComQb = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_user_id", taskUserId)
                //.appendWhere("audit_status!='transferred'")
                .appendWhere(" audit_status = 'pass' ")
                .whereLike("scorer_type", "score")
                .appendWhere("is_deleted='false'");
        List<EvalScoreResult> scoreResults = this.autoBaseDao.listAllDomain(scoreComQb, EvalScoreResult.class);
        return scoreResults;
    }

    public ListWrap<EvalScoreResult> listScoreStageBeforeScoreResults(TenantId companyId, String taskUserId) {
        ComQB scoreComQb = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereIn("scorer_type", AuditEnum.scoreStageBefore())
                .appendWhere("is_deleted='false'");
        List<EvalScoreResult> scoreResults = this.autoBaseDao.listAllDomain(scoreComQb, EvalScoreResult.class);

        //过滤出有转交的taskAuditId
        if (CollUtil.isNotEmpty(scoreResults)) {
            List<String> taskAuditIdsWithTransfers = scoreResults.stream().filter(scoreResult ->
                            StrUtil.isNotBlank(scoreResult.getTransferId())).map(EvalScoreResult::getTaskAuditId)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(taskAuditIdsWithTransfers)) {
                //再查perf_templ_evaluate_audit表的approverInfo 条件id in(过滤出有转交的taskAuditId)
                Map<String, String> approverInfoMap = getApproverInfoMap(companyId.getId(), taskAuditIdsWithTransfers);

                scoreResults.forEach(scoreResult -> {
                    String approverInfo = approverInfoMap.get(scoreResult.getTaskAuditId());
                    if (StrUtil.isNotBlank(approverInfo)) {
                        scoreResult.setScorerId(approverInfo);
                    }
                });
            }
        }
        return new ListWrap<>(scoreResults).groupBy(EvalScoreResult::getScorerType);
    }

    private Map<String, String> getApproverInfoMap(String companyId, List<String> taskAuditIds) {
        List<EvalAudit> audits = listEvalAuditByIds(companyId, taskAuditIds);
        return audits.stream().collect(Collectors.toMap(EvalAudit::getId, EvalAudit::getApproverInfo));
    }

    public List<EvalAudit> listEvalAuditByIds(String companyId, List<String> ids) {
        ComQB comQB = ComQB.build(TaskAuditDo.class)
                .whereEqReq("company_id", companyId)
                .whereIn("id", ids)
                .whereEqReq("is_deleted", "false");
        return autoBaseDao.listAllDomain(comQB, EvalAudit.class);
    }

    /**
     * @description:
     * @author: lufei
     * @date: 2022/8/27 11:55
     * @param: [companyId, taskName]
     * @return:
     * @see AdminTaskDao#existedEvalTaskName(TenantId, String)
     **/
    @Deprecated
    public boolean isExistedName(TenantId companyId, String taskName) {
        UpdateBuilder up = UpdateBuilder.build(PerfEvaluateTaskBaseDo.class)
                .whereEq("company_id", companyId.getId())
                .whereEqReq("task_name", taskName)
                .whereEq("task_status", "published")
                .whereEq("is_deleted", "false");
        int row = autoBaseDao.update(up);
        return row > 0;
    }

    public List<EvalUser> listFixTaskUser(String companyId, String taskId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .appendWhere("is_deleted ='false'")
                .whereEq("companyId", companyId)
                .whereEq("taskId", taskId);
        return this.autoBaseDao.listAllDomain(comQB, EvalUser.class);
    }

    public List<ResultAuditRecordPo> listResultAuditRecord(TenantId companyId, String taskUserId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "r")
                .leftJoin(EmployeeBaseInfoDo.class, "e")
                .appendOn(" r.company_id = e.company_id and r.scorer_id = e.employee_id ")
                .leftJoin(EmpEvalRuleDo.class, "er")
                .appendOn(" r.task_user_id = er.emp_eval_id ")
                .clearSelect().select(" er.show_result_type AS showResultType, e.name as scorer,e.avatar, r.score,r.score_level,r.score_comment,r.updated_time," +
                        "ifnull(r.calibration_type,1) as calibration_type,r.operate_reason,r.perf_coefficient")
                .setRsType(ResultAuditRecordPo.class)
                .whereEqReq("r.company_id", companyId.getId())
                .whereEqReq("r.task_user_id", taskUserId)
                .whereEqReq("r.audit_status", "pass")
                .whereEqReq("r.is_deleted", "false")
                .whereEqReq("er.is_deleted", "false")
                .whereEqReq("r.scorer_type", "final_result_audit")
                //如果校准设置了直属主管、直属主管中存在多个人时校准记录只能查出操作了校准的具体人
                .appendWhere("( r.score is not null  or r.score_level is not null )")
                .orderByAsc(" r.approval_order ");
        List<ResultAuditRecordPo> pos = autoBaseDao.listAll(comQB);
        if (CollUtil.isEmpty(pos)) {
            return pos;
        }
        pos.forEach(po -> {
            po.convertJson();
        });
        return pos;
    }

    /**
     * public String getHistoryScore(TenantId companyId, String taskUserId) {
     * ComQB user = ComQB.build(PerfEvaluateTaskUserDo.class)
     * .clearSelect().select(" original_final_score as score,original_evaluation_level as scoreLevel")
     * .setRsType(HistoryScorePo.class)
     * .whereEqReq("company_id", companyId.getId())
     * .whereEqReq("id", taskUserId)
     * .whereEqReq("is_deleted", "false");
     * HistoryScorePo finalScore = autoBaseDao.findOne(user);
     * <p>
     * ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
     * .clearSelect().select(" score,score_level as scoreLevel")
     * .setRsType(HistoryScorePo.class)
     * .whereEqReq("company_id", companyId.getId())
     * .whereEqReq("task_user_id", taskUserId)
     * .whereEqReq("scorer_type", "final_result_audit")
     * .whereEqReq("is_deleted", "false")
     * .whereEqReq("audit_status", "pass");
     * List<HistoryScorePo> historyScorePos = autoBaseDao.listAll(comQB);
     * historyScorePos.add(finalScore);
     * List<HistoryScorePo> hasLevels = historyScorePos.stream()
     * .filter(po -> po.hasLevelValue())
     * .collect(Collectors.toList());
     * if (CollUtil.isNotEmpty(hasLevels)) {//返回null表示等级
     * return null;
     * }
     * //String historyScore = hasLevels.stream().map(po -> po.getScore() + "(" + po.getScoreLevel() + ")").collect(Collectors.joining("<<"));
     * return historyScore;
     * }
     */


    public List<EvalUser> fixInputFinishStatus() {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .clearSelect().select(" id,company_id,task_id,emp_id,updated_time,task_status ")
                .appendWhere(" id not in (select task_user_id from task_fix_finish_status ) and is_deleted = 'false' ")
                .limit(0, 100);
        List<EvalUser> evalUsers = autoBaseDao.listAllDomain(comQB, EvalUser.class);
        if (evalUsers.isEmpty()) {
            return null;
        }
        evalUsers.stream().forEach(evalUser -> {
            ComQB kpiComQB = ComQB.build(PerfEvaluateTaskKpiDo.class)
                    .whereEqReq("company_id", evalUser.getCompanyId().getId())
                    .whereEqReq("task_id", evalUser.getTaskId())
                    .whereEqReq("task_user_id", evalUser.getId())
                    .whereEq("is_deleted", Boolean.FALSE.toString());
            List<EvalKpi> kpis = this.autoBaseDao.listAllDomain(kpiComQB, EvalKpi.class);
            evalUser.setKpis(kpis);
        });
        return evalUsers;
    }

    //执行阶段录入完成值责任人
    public String findInputFinishReviewsEmp(String taskUserId, String companyId) {
        ComQB comQB = ComQB.build(EmployeeBaseInfoDo.class)
                .clearSelect().select(" GROUP_CONCAT( CONCAT_WS( '&', NAME, STATUS ) ) ")
                .setRsType(String.class)
                .appendWhere(" FIND_IN_SET(employee_id,(SELECT GROUP_CONCAT( IF(k.result_input_type = 'exam',k.emp_id,k.result_input_emp_id)) " +
                        "FROM perf_evaluate_task_kpi k WHERE k.company_id = '" + companyId + "' AND k.task_user_id = '" + taskUserId + "'")
                .appendWhere(" k.is_deleted = 'false' AND k.result_input_type in ('exam','user','manager') GROUP BY k.task_user_id)) AND company_id = '" + companyId + "'");
        return autoBaseDao.findOne(comQB);
    }

    public String findReviewsEmp(String taskId, String companyId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "r")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn(" r.company_id = e.company_id and r.scorer_id = e.employee_id and r.is_deleted = 'false' and e.is_delete = 'false'")
                .setRsType(String.class)
                .clearSelect().select(" GROUP_CONCAT(DISTINCT CONCAT_WS( '&', NAME, STATUS ) ) ")
                .whereEqReq("r.company_id", companyId)
                .whereEqReq("r.task_id", taskId)
                .whereEq("r.is_deleted", false)
                .whereEq("e.is_delete", false)
                .appendWhere(" r.audit_status is null ")
                .groupBy(" r.emp_id, r.task_id ");
        return autoBaseDao.findOne(comQB);
    }

    public String findTaskEmp(String taskId, String empId, String companyId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn(" u.company_id = e.company_id and u.emp_id = e.employee_id ")
                .setRsType(String.class)
                .clearSelect().select(" CONCAT_WS( '&', NAME, STATUS ) ")
                .whereEqReq("u.company_id", companyId)
                .whereEqReq("u.task_id", taskId)
                .whereEqReq("u.emp_id", empId)
                .whereEq("u.is_deleted", false)
                .whereEq("e.is_delete", false);
        return autoBaseDao.findOne(comQB);
    }

    public PagedList<RuleEmpEvalPo> pagedEmpEval(EmpEvalAtTaskQuery3 query) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn(" u.company_id=e.company_id and u.emp_id =e.employee_id")
                .clearSelect().select("e.name as emp_name ,u.emp_org_name,u.task_status,u.created_time ")
                .setRsType(RuleEmpEvalPo.class)
                .whereEq("u.task_id", query.getTaskId())
                .whereEqReq("u.company_id", query.getCompanyId())
                .whereEq("u.cycle_id", query.getCycleId())
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .setPage(query.getPageNo(), query.getPageSize())
                .orderByDesc(" u.created_time ");
        return autoBaseDao.listPage(comQB);
    }

    //考核成员查询,按状态tab查询
    public PagedList<EmpEvalByStatusPo> pagedEmpEvalByStatus(EmpEvalAtTaskQuery2 qry) {
        ComQB joinQb = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "r1")
                .join(EmployeeBaseInfoDo.class, "sc").appendOn("r1.company_id = sc.company_id and r1.scorer_id = sc.employee_id")
                .join(PerfEvaluateTaskBaseDo.class, "b1").appendOn("b1.company_id = r1.company_id and r1.task_id = b1.id")
                .clearSelect().select("r1.task_user_id, r1.company_id ,r1.scorer_id")
                .select(" GROUP_CONCAT(DISTINCT CONCAT_WS('&', IFNULL(sc.NAME, ''), IFNULL(sc.STATUS, ''))) AS reviewers_emp")
                .whereEqReq("b1.company_id", qry.getCompanyId())
                .whereEq("b1.cycle_id", qry.getCycleId())
                .groupBy("r1.task_user_id");

        ComQB qb1 = ComQB.build(PerfEvaluateTaskUserDo.class, "eu")
                .leftJoinQ(joinQb, "se").appendOn("eu.company_id = se.company_id and eu.id = se.task_user_id")
                .leftJoin(PerfEvaluateTaskBaseDo.class, "b").appendOn("b.company_id = eu.company_id AND b.id = eu.task_id")
                .clearSelect().setRsType(EmpEvalByStatusPo.class)
                .select("eu.task_id,b.task_name,eu.id,eu.emp_id,eu.emp_name,eu.emp_org_name ,eu.avatar,eu.task_status")
                .select("eu.final_score,eu.evaluation_level,eu.emp_org_name org_name,reviewers_emp,b.evaluate_type ")
                .whereEqReq("eu.company_id", qry.getCompanyId())
                .whereIn("eu.task_status", qry.getTaskStatuss())
                .appendWhere("eu.task_status!='drawUpIng'")
                .appendWhere("eu.is_deleted = 'false'")
                .whereIn("eu.task_id", qry.getTaskIds())
                .whereEq("b.cycle_id", qry.getCycleId())
                .whereIn("eu.emp_id", qry.getEmpIds())
                .whereIn("eu.evaluation_level", qry.getLevelNames())
                .whereLike("eu.emp_name", qry.getKeyword())
                .setPage(qry.getPageNo(), qry.getPageSize());
        if (StringUtils.isNotEmpty(qry.getSortOrder())) {
            qb1.orderBy("eu.final_score" + qry.getSortOrder());
        }
        qb1.orderBy("eu.created_time desc");
        PagedList<EmpEvalByStatusPo> rs = autoBaseDao.listPage(qb1);
        if (rs.isEmpty()) {
            return rs;
        }
        for (EmpEvalByStatusPo r : rs) {
            r.reviewerSelfIf();
        }
        return rs;
    }

    public PagedList<RuleEmpEvalDraftPo> pagedEmpEvalDraft(EmpEvalAtTaskQuery qry) {
        ComQB qb = ComQB.build(PerfEvaluateTaskUserDo.class, "eu")
                .leftJoin(EmpEvalRuleDo.class, "ru").appendOn("eu.company_id = ru.company_id and eu.id = ru.emp_eval_id")
                .clearSelect().setRsType(RuleEmpEvalDraftPo.class)
                .select(" eu.id task_user_id, eu.emp_id, eu.emp_name, eu.emp_org_name,ru.rule_name,ru.index_raters,ru.indicator_cnt")
                .select("eu.org_changed ,rule_conf_status,eu.avatar,eu.task_status!='drawUpIng' as started ")
                .appendWhere("eu.is_deleted = 'false'")
                .whereEqReq("eu.task_id", qry.getTaskId())
                .whereEqReq("eu.company_id", qry.getTenantId().getId())
                .whereLike("eu.emp_name", qry.getEmpKeyword())
                .whereLike("eu.emp_org_name", qry.getOrgKeyword())
                .whereEq("eu.rule_conf_status", qry.onlyErrRule())
                .orderBy("eu.created_time desc")
                .setPage(qry.getPageNo(), qry.getPageSize());
        return autoBaseDao.listPage(qb);
    }

    //PerfEvaluateTaskScoreResultManager.queryAuditedResultByScorerId[671]
    public List<ScoreResultWithItemPo> listPassedScoreResultWithItem(String companyId, String taskUserId, String scorerType, String scorerId) {
        ComQB leftQ = ComQB.build(PerfEvaluateTaskKpiDo.class, "a")
                .clearSelect().select("DISTINCT company_id, kpi_type_id, kpi_type_name,kpi_item_id,kpi_item_name,item_type,item_weight,kpi_type_classify,plus_limit,subtract_limit,max_extra_score,work_item_finish_value")
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .appendWhere("is_deleted = 'false'")
                .groupBy("kpi_item_id");

        ComQB scoreComQb = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "t")
                .leftJoinQ(leftQ, "k").appendOn(" k.kpi_item_id = t.kpi_item_id and  k.company_id = t.company_id")
                .clearSelect().setRsType(ScoreResultWithItemPo.class)
                .select("t.*")
                .select("k.kpi_item_name item_name,k.kpi_type_name,k.item_weight,k.kpi_type_classify,k.plus_limit,k.subtract_limit,k.max_extra_score,k.item_type,k.work_item_finish_value ")
                .whereEqReq("t.company_id", companyId)
                .whereEqReq("t.task_user_id", taskUserId)
                .whereEqReq("t.scorerId", scorerId)
                //.appendWhere("audit_status!='transferred'")
                .appendWhere(" audit_status = 'pass' ")
                .whereLike("scorer_type", scorerType)
                .appendWhere("is_deleted='false'");
        return autoBaseDao.listAll(scoreComQb);
    }

    public void loadSelfScore(String companyId, String taskUserId, List<ScoreResultWithItemPo> items) {
        ComQB qb = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .whereEq("company_id", companyId)
                .whereEq("task_user_id", taskUserId)
                .whereEq("scorer_type", EvaluateAuditSceneEnum.SELF_SCORE.getScene())
                .whereEq("is_deleted", "false");
        List<PerfEvaluateTaskScoreResultDo> selfRss = autoBaseDao.listAll(qb);
        List<PerfEvaluateTaskScoreResultDo> collect = selfRss.stream().filter(s -> StrUtil.isNotEmpty(s.getKpiItemId())).collect(Collectors.toList());
        Map<String, PerfEvaluateTaskScoreResultDo> selfScoreMap = CollUtil.toMap(collect, new HashMap<>(), parameter -> parameter.getKpiItemId());

        for (ScoreResultWithItemPo item : items) {
            PerfEvaluateTaskScoreResultDo itemSelfScore = selfScoreMap.get(item.getKpiItemId());
            item.setSelfScore(itemSelfScore.score());
            item.setSelfScoreLevel(itemSelfScore.getScoreLevel());
            item.setSelfScoreComment(itemSelfScore.getScoreComment());
        }
    }

    //迁移scoreResultManager line：860
    public SkipSelfScorePo getSelfEvaluateScore(String companyId, String taskUserId) {
        ComQB comQB = ComQB.buildDiff(PerfEvaluateTaskScoreResultDo.class, "v_task_score_result", "v")
                .clearSelect().select(" kpi_type_id, kpi_item_id ")
                .setRsType(PerfEvaluateTaskScoreResultPo.class)
                .whereEqReq("company_id", companyId)
                .whereEq("task_user_id", taskUserId)
                .appendWhere("scorer_type='self_score'")
                .whereEq("is_deleted", "false");
        List<PerfEvaluateTaskScoreResultPo> itemS = autoBaseDao.listAll(comQB);
        SkipSelfScorePo po = new SkipSelfScorePo(itemS, "跳过自评", taskUserId);
        return po;
    }

    public void cacheScenesScore(SubmitScoreCacheDo cacheDo) {
        //cacheDo.setBusinessScene("cacheScenesScore");
        cacheDo.setId(UUID.randomUUID().toString());
        cacheDo.setIsDeleted("false");
        DeleteBuilder cacheQB = DeleteBuilder.build(SubmitScoreCacheDo.class, "c")
                .whereEq("companyId", cacheDo.getCompanyId())
                .whereEq("taskUserId", cacheDo.getTaskUserId())
                .whereEq("ownerEmpId", cacheDo.getOwnerEmpId());
        autoBaseDao.delete(cacheQB);
        autoBaseDao.save(cacheDo);
    }

    public List<ResultAuditRecordPo> getFinalAuditResult(TenantId tenantId, String taskUserId) {
        ComQB comQB = ComQB.build(OperationLogDo.class, "o")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn("o.company_id=e.company_id and o.created_user=e.employee_id")
                .clearSelect()
                .select("e.avatar,e.name as scorer,o.after_value ,o.updated_time,o.description as adjust_reason,o.business_scene")
                .setRsType(ResultAuditRecordPo.class)
                .whereEqReq("o.company_id", tenantId.getId())
                .whereEqReq("o.ref_id", taskUserId)
                .whereEq("e.is_delete", Boolean.FALSE.toString())
                .whereInReq("o.business_scene", Arrays.asList("final_result_audit", "adjust_task_user_result"))
                .appendWhere("o.after_value!='' ")
                .orderByDesc(" o.created_time ");
        List<ResultAuditRecordPo> pos = autoBaseDao.listAll(comQB);
        if (CollUtil.isEmpty(pos)) {
            return pos;
        }
        pos.forEach(po -> {
            po.buildScore();
            po.convertJson();
        });
        return pos;
    }

    public Map<String, List<EvalBind>> testPersonBindMactch(TestBindEvalQuery qry) {
        List<TestBindEvalQuery.BindCond> personConds = qry.personConds();
        if (CollUtil.isEmpty(personConds)) {
            return new HashMap<>();
        }
        List<String> bindTaskIds = CollUtil.map(personConds, bindCond -> bindCond.getTaskId(), true);
        List<String> mainEmpIds = CollUtil.map(qry.getMainEvals(), mainEval -> mainEval.getEmpId(), true);
        List<String> mainUserIds = CollUtil.map(qry.getMainEvals(), mainEval -> mainEval.getTaskUserId(), true);
        ComQB qb = ComQB.build(PerfEvaluateTaskBaseDo.class, "b")
                .leftJoin(PerfEvaluateTaskUserDo.class, "u")
                .appendOn(" u.company_id = b.company_id and u.task_id = b.id and u.is_deleted = 'false'")
                .clearSelect().setRsType(EvalBind.class)
                .select("b.task_name,u.emp_name ref_emp_name,u.emp_org_name , u.task_id , u.id  ref_eval_id, u.emp_id ref_emp_id")
                .select(" if(count(1) > 1, 500,if(count(1)=0,404,200)) error")
                .whereEqReq("b.company_id", qry.getCompanyId())
                .whereIn(" b.id", bindTaskIds)
                .appendWhere("b.performance_type = 1")
                .whereIn("u.emp_id", mainEmpIds)
                .whereNotIn("u.id", mainUserIds)
                .groupBy("u.task_id , emp_id");
        List<EvalBind> binds = autoBaseDao.listAll(qb);
        //把权重更新回
        ListWrap<EvalBind> byEmpIdGroups = new ListWrap<>(binds).groupBy(evalBind -> evalBind.getRefEmpId());
        Map<String, List<EvalBind>> bindMap = new HashMap<>();
        for (MainEval mainEval : qry.getMainEvals()) {
            List<EvalBind> exist = byEmpIdGroups.groupGet(mainEval.getEmpId());
            ListWrap<EvalBind> taskIdMap = new ListWrap<>(exist).asMap(evalBind -> evalBind.getTaskId());
            List<EvalBind> existed = bindMap.get(mainEval.getTaskUserId());
            if (CollUtil.isEmpty(existed)) {
                existed = new ArrayList<>();
                bindMap.put(mainEval.getTaskUserId(), existed);
            }

            for (TestBindEvalQuery.BindCond personCond : personConds) {
                EvalBind evalBind1 = taskIdMap.mapGet(personCond.getTaskId());
                if (evalBind1 == null) {
                    evalBind1 = new EvalBind();
                    evalBind1.setTaskId(personCond.getTaskId());
                    evalBind1.setRefEmpId(mainEval.getEmpId());
                    evalBind1.setTaskName(personCond.getTaskName());
                    evalBind1.setError(404);
                    //evalBind1.setWeight(personCond.getWeight());
                    //evalBind1.setType(personCond.getType());
                    //binds.add(evalBind1);
                    //existed.add(evalBind1);
                }
                evalBind1.setWeight(personCond.getWeight());
                evalBind1.setType(personCond.getType());
                existed.add(evalBind1);
            }
        }
        return bindMap;
    }

    public List<EvalBind> listOtherBinds(TestBindEvalQuery qry) {
        List<EvalBind> otherBinds = qry.otherBinds();
        if (CollUtil.isEmpty(otherBinds)) {
            return new ArrayList<>();
        }
        List<String> taskUserIds = otherBinds.stream().map(evalBind -> evalBind.getRefEvalId()).collect(Collectors.toList());
        ComQB qb = ComQB.build(PerfEvaluateTaskUserDo.class, "eu")
                .join(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("eu.company_id = b.company_id and eu.task_id = b.id and eu.is_deleted='false'")
                .clearSelect().select("eu.id ref_eval_id,eu.emp_id ref_emp_id ,eu.emp_name ref_emp_name,eu.emp_org_name ")
                .select("eu.eval_org_name ref_eval_org_name,eu.task_id, b.task_name ")
                .setRsType(EvalBind.class)
                .whereIn("eu.id", taskUserIds)
                .whereEqReq("eu.company_id", qry.getCompanyId());
        List<EvalBind> others = autoBaseDao.listAll(qb);
        ListWrap<EvalBind> map = new ListWrap<>(others).asMap(evalBind -> evalBind.getRefEvalId());
        for (EvalBind otherBind : otherBinds) {
            EvalBind evalBind = map.mapGet(otherBind.getRefEvalId());
            if (evalBind == null) {
                continue;
            }
            evalBind.setWeight(otherBind.getWeight());
            evalBind.setType(otherBind.getType());
        }
        return others;
    }


    public Map<String, String> listRefEvalResult(String companyId, List<String> mainUserIds) {
        Map<String, String> stringMap = new HashMap<>();
        ComQB mQ = ComQB.build(PerfEvaluateTaskUserDo.class, "m")
                .leftJoin(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("m.company_id = b.company_id and m.task_id = b.id")
                .clearSelect().select("m.id as mainTaskUserId, b.task_name,m.final_score as scoreOfRef,m.weight_of_ref as weight")
                .setRsType(BindEvalResult.class)
                .whereEqReq("m.company_id ", companyId)
                .whereIn("m.id", mainUserIds.stream().distinct().collect(Collectors.toList()))
                .appendWhere("m.is_deleted = 'false'");
        List<BindEvalResult> refList = autoBaseDao.listAll(mQ);
        if (CollectionUtils.isEmpty(refList)) {
            return null;
        }
        ComQB qb = ComQB.build(RefEvalDo.class, "rev")
                .join(PerfEvaluateTaskUserDo.class, "u")
                .appendOn("rev.company_id = u.company_id and rev.ref_eval_id = u.id")
                .join(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("b.company_id = u.company_id and b.id = u.task_id")
                .clearSelect().select("b.task_name,rev.weight,rev.main_eval_id as mainTaskUserId,ifnull(rev.score,final_score) as scoreOfRef")
                .setRsType(BindEvalResult.class)
                .whereEqReq("rev.company_id ", companyId)
                .whereIn("rev.main_eval_id", mainUserIds);
        List<BindEvalResult> others = autoBaseDao.listAll(qb);
        if (!CollectionUtils.isEmpty(others)) {
            Map<String, List<BindEvalResult>> map = others.stream().collect(
                    Collectors.groupingBy(BindEvalResult::getMainTaskUserId));
            refList.forEach(ref -> {
                if (ref.getScoreOfRef() == null) {
                    stringMap.put(ref.getMainTaskUserId(), "-");
                    return;
                }
                if (map.get(ref.getMainTaskUserId()) != null) {
                    StringBuilder builder = new StringBuilder();
                    List<BindEvalResult> evalList = map.get(ref.getMainTaskUserId());
                    builder.append("T-").append(ref.getTaskName()).append("：").append(ref.getScoreOfRef().setScale(3) + "*" + ref.getWeight().setScale(2, RoundingMode.HALF_UP) + "%").append(System.lineSeparator());
                    for (int i = 0; i < evalList.size(); i++) {
                        builder.append("T").append(i + 1).append("-").append(evalList.get(i).getTaskName()).append("：");
                        if (evalList.get(i).getScoreOfRef() != null) {
                            builder.append(evalList.get(i).getScoreOfRef().setScale(3) + "*" + evalList.get(i).getWeight().setScale(2, RoundingMode.HALF_UP) + "%");
                        } else {
                            builder.append("--");
                        }
                        builder.append(System.lineSeparator());
                    }
                    stringMap.put(ref.getMainTaskUserId(), builder.toString());
                } else {
                    stringMap.put(ref.getMainTaskUserId(), "-");
                }
            });
        }
        return stringMap;
    }

    public Map<String, String> listRuleName(String companyId, List<String> taskUserId) {
        ComQB mQ = ComQB.build(EmpEvalRuleDo.class, "r")
                .clearSelect().select("r.emp_eval_id,rule_name")
                .setRsType(EmpEvalRuleDo.class)
                .whereEqReq("r.company_id ", companyId)
                .whereIn("r.emp_eval_id", taskUserId.stream().distinct().collect(Collectors.toList()))
                .appendWhere("r.is_deleted = 'false'");
        List<EmpEvalRuleDo> ruleDoList = autoBaseDao.listAll(mQ);
        if (CollectionUtils.isEmpty(ruleDoList)) {
            return null;
        }
        return ruleDoList.stream().collect(Collectors.toMap(EmpEvalRuleDo::getEmpEvalId, EmpEvalRuleDo::getRuleName, (key1, key2) -> key2));
    }

    //先写死。
    public Map<String, List<PerfEvaluateItemUsedFieldDo>> listItemUsedField(String companyId, List<String> taskUserId) {
        ComQB mQ = ComQB.build(PerfEvaluateItemUsedFieldDo.class, "u")
                .clearSelect().select("u.field_id,u.task_user_id,u.kpi_item_id,u.name,u.value,u.type")
                .setRsType(PerfEvaluateItemUsedFieldDo.class)
                .whereEqReq("u.company_id ", companyId)
                .whereIn("u.task_user_id", taskUserId.stream().distinct().collect(Collectors.toList()))
                .appendWhere("u.is_deleted = 'false'");
        List<PerfEvaluateItemUsedFieldDo> itemUsedFieldDos = autoBaseDao.listAll(mQ);
        if (CollectionUtils.isEmpty(itemUsedFieldDos)) {
            return null;
        }
        return itemUsedFieldDos.stream()
                .collect(Collectors.groupingBy(u -> u.getTaskUserId() + "|" + u.getKpiItemId()));
    }

    public BindEvalResult getBindEvalResult(String companyId, String mainUserId) {

        ComQB mQ = ComQB.build(PerfEvaluateTaskUserDo.class, "m")
                .whereEqReq("m.company_id ", companyId)
                .whereEqReq("m.id", mainUserId)
                .appendWhere("m.is_deleted = 'false'");
        PerfEvaluateTaskUserDo main = autoBaseDao.findOne(mQ);

        ComQB bQ = ComQB.build(PerfEvaluateTaskBaseDo.class, "b")
                .clearSelect().select("id,task_name, archive_status")
                .whereEqReq("b.company_id ", companyId)
                .whereEqReq("b.id", main.getTaskId())
                .appendWhere("b.is_deleted = 'false'");
        PerfEvaluateTaskBaseDo taskBaseDo = autoBaseDao.findOne(bQ);
        BindEvalResult rs = new BindEvalResult(mainUserId, main.getWeightOfRef(), taskBaseDo.getId(), taskBaseDo.getTaskName(), taskBaseDo.getArchiveStatus());

        List<EvalBind> others = getOtherEvalBinds(companyId, mainUserId);
        rs.setBinds(others);
        return rs;
    }

    public List<EvalBind> getOtherEvalBinds(String companyId, String mainUserId) {
        ComQB qb = ComQB.build(RefEvalDo.class, "rev")
                .join(PerfEvaluateTaskUserDo.class, "u")
                .appendOn("rev.company_id = u.company_id and rev.ref_eval_id = u.id")
                .join(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("b.company_id = u.company_id and b.id = u.task_id")
                .clearSelect().select("b.task_name,b.id task_id,rev.ref_eval_id ,rev.weight ,rev.main_eval_id ")
                .select(" u.emp_id ref_emp_id, u.emp_name ref_emp_name,u.emp_org_name ,u.eval_org_name ref_eval_org_name,(case when (u.is_deleted = 'true' or b.is_deleted = 'true') then 1 when ((b.task_status = 'terminated' and u.task_status = 'scoring') or u.task_status = 'terminated')then 2 when u.task_status in('drawUpIng','confirming','confirmed','changing','scoring') then 3 end) as unusualType")
                .select("u.final_score as finalScore")
                .setRsType(EvalBind.class)
                .whereEqReq("rev.company_id ", companyId)
                .whereEqReq("rev.main_eval_id", mainUserId);
        List<EvalBind> others = autoBaseDao.listAll(qb);
        return others;
    }


    public Map<String, BindEvalResult> getBindEvalResultAsMap(String companyId, List<String> mainUserIds) {

        if (CollUtil.isEmpty(mainUserIds)) {
            return new HashMap<>();
        }

        ComQB mQ = ComQB.build(PerfEvaluateTaskUserDo.class, "m")
                .leftJoin(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("m.company_id = b.company_id and m.task_id = b.id")
                .clearSelect().select("m.*,b.id as taskId,b.task_name")
                .setRsType(EvalUser.class)
                .whereEqReq("m.company_id ", companyId)
                .whereInReq("m.id", mainUserIds)
                .appendWhere("m.is_deleted = 'false'")
                .whereEq("b.is_deleted", Boolean.FALSE.toString());
        List<EvalUser> mainList = autoBaseDao.listAll(mQ);
        if (CollUtil.isEmpty(mainList)) {
            return new HashMap<>();
        }
        List<BindEvalResult> results = new ArrayList<>();
        for (EvalUser evalUser : mainList) {
            BindEvalResult rs = new BindEvalResult(evalUser.getId(), evalUser.getWeightOfRef(), evalUser.getTaskId(), evalUser.getTaskName());
            results.add(rs);
        }
        ComQB qb = ComQB.build(RefEvalDo.class, "rev")
                .join(PerfEvaluateTaskUserDo.class, "u")
                .appendOn("rev.company_id = u.company_id and rev.ref_eval_id = u.id")
                .join(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("b.company_id = u.company_id and b.id = u.task_id")
                .clearSelect().select("b.task_name,b.id task_id,rev.ref_eval_id ,rev.weight ,rev.main_eval_id ")
                .select(" u.emp_id ref_emp_id, u.emp_name ref_emp_name,u.emp_org_name ,u.eval_org_name ref_eval_org_name,(case when (u.is_deleted = 'true' or b.is_deleted = 'true') then 1 when ((b.task_status = 'terminated' and u.task_status = 'scoring') or u.task_status = 'terminated')then 2 when u.task_status in('drawUpIng','confirming','confirmed','changing','scoring') then 3 end) as unusualType")
                .select("u.final_score as finalScore")
                .setRsType(EvalBind.class)
                .whereEqReq("rev.company_id ", companyId)
                .whereInReq("rev.main_eval_id", mainUserIds);
        List<EvalBind> others = autoBaseDao.listAll(qb);
        if (CollUtil.isNotEmpty(others)) {
            Map<String, List<EvalBind>> bindMap = others.stream().collect(Collectors.groupingBy(o -> o.getRefEvalId()));
            for (BindEvalResult result : results) {
                result.setBinds(bindMap.get(result.getMainTaskUserId()));
            }
        }
        return CollUtil.toMap(results, new HashMap<>(), r -> r.getMainTaskUserId());
    }

    //weight_of_ref
    public void saveRefEval(String companyId, List<BindEvalResult> batchRs) {
        List<String> mainUserIds = new ArrayList<>();
        List<String> allUserIds = new ArrayList<>();
        List<RefEvalDo> allBindDos = new ArrayList<>();
        List<String> bindUserIds = batchRs.stream().flatMap(rs -> {
            mainUserIds.add(rs.getMainTaskUserId());
            return rs.getBinds().stream();
        }).map(evalBind -> evalBind.getRefEvalId()).collect(Collectors.toList());
        allUserIds.addAll(bindUserIds);
        allUserIds.addAll(mainUserIds);
        //查询被关联的任务列表
        ComQB qb = ComQB.build(PerfEvaluateTaskUserDo.class, "bu")
                .clearSelect().select("id, final_score,weight_of_ref,score_ranges")
                .whereEqReq("companyId", companyId)
                .whereIn("id", allUserIds);
        List<PerfEvaluateTaskUserDo> allUsers = autoBaseDao.listAll(qb);
        ListWrap<PerfEvaluateTaskUserDo> allUserMap = new ListWrap<>(allUsers).asMap(bindUser -> bindUser.getId());

        for (BindEvalResult mainEval : batchRs) {
            mainUserIds.add(mainEval.getMainTaskUserId());
            PerfEvaluateTaskUserDo mainUser = allUserMap.mapGet(mainEval.getMainTaskUserId());
            mainEval.countMainScore(mainUser.getFinalScore());
            for (EvalBind bind : mainEval.getBinds()) {
                PerfEvaluateTaskUserDo otherBind = allUserMap.mapGet(bind.getRefEvalId());
                mainEval.addRefScore(bind.getWeight(), otherBind.getFinalScore());
                RefEvalDo refEvalDo = new RefEvalDo(companyId, mainEval.getMainTaskUserId(), bind.getRefEvalId(), bind.getWeight());
                refEvalDo.setScore(otherBind.getFinalScore());
                allBindDos.add(refEvalDo);
            }
            mainEval.setScoreOfRef(mainEval.roundScore());
            mainEval.computeLevel(mainEval.getRanges());
        }

        for (BindEvalResult batchR : batchRs) {
            UpdateBuilder up = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                    .whereEqReq("company_id", companyId)
                    .whereEq("id", batchR.getMainTaskUserId())
                    .set("weight_of_ref", batchR.getWeight())
                    .set("score_of_ref", batchR.getScoreOfRef())
                    .set("evaluation_level", batchR.getLevelName())
                    .set("perf_coefficient", batchR.getPerfCoefficient());
            autoBaseDao.update(up);
        }

        DeleteBuilder del = DeleteBuilder.build(RefEvalDo.class)
                .whereEqReq("company_id", companyId)
                .whereInReq("mainEvalId", mainUserIds);
        autoBaseDao.delete(del);
        autoBaseDao.saveBatch(allBindDos);
    }

    //计算关联得分
    public void computeScoreOfRef(String companyId, EvalUser mainUser, boolean closeCompute) {
        if (mainUser == null || mainUser.getFinalScore() == null) {
            return;
        }
        List<RankRuleScoreRangeSnap> scoreRanges = mainUser.getScoreRanges();
        BigDecimal score = mainUser.getFinalScore().multiply(mainUser.getWeightOfRef());
        //查询被关联的任务列表
        ComQB qb = ComQB.build(RefEvalDo.class, "ref")
                .join(PerfEvaluateTaskUserDo.class, "ru").appendOn(" ref.company_id = ru.company_id and ref.ref_eval_id = ru.id")
                .clearSelect().select("ref.company_id,ref.main_eval_id, ref.ref_eval_id, ru.final_score as score, ref.weight")
                .setRsType(RefEvalDo.class)
                .whereEqReq("ref.companyId", companyId)
                .whereEqReq("ref.main_eval_id", mainUser.getId());
        List<RefEvalDo> refUsers = autoBaseDao.listAll(qb);
        for (RefEvalDo refUser : refUsers) {
            if (refUser == null || refUser.getScore() == null) {
                return;
            }
            score = score.add(refUser.getScore().multiply(refUser.getWeight()));
        }
        score = score.divide(Pecent.ONE_HUNDRED, 2, BigDecimal.ROUND_HALF_UP);

        UpdateBuilder up = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("id", mainUser.getId())
                .set("score_of_ref", score);

        if (!closeCompute && CollUtil.isNotEmpty(scoreRanges)) {
            ResultRankInstance scoreRule = new ResultRankInstance(scoreRanges);
            if (Objects.equals(scoreRanges.get(0).getLevelDefType(), 1)) {
                MatchedStep step = scoreRule.computeLevelStep(score);
                up.set("step_id", step.getStepId())
                        .set("original_step_id", step.getStepId())
                        .set("evaluation_level", step.getStepName())
                        .set("perf_coefficient", step.getPerfCoefficient());
            } else {//仅更新系数.因为系数可能是公式关联了分数.
                log.info("仅更新系数.因为系数可能是公式关联了分数 taskUserId={} stepId={}", mainUser.getId(), mainUser.getStepId());
                BaseScoreRange range = scoreRule.matchLevelByStepId(mainUser.getStepId());
                if (Objects.isNull(range)) {
                    return; //这个时候可能还没有计算出等级
                }
                String coefficient = range.computeCoefficient(score);
                mainUser.setPerfCoefficient(coefficient);
                up.set("perf_coefficient", coefficient);
            }
        }
        autoBaseDao.update(up);


        DeleteBuilder del = DeleteBuilder.build(RefEvalDo.class)
                .whereEqReq("company_id", companyId)
                .whereEq("mainEvalId", mainUser.getId());
        autoBaseDao.delete(del);
        autoBaseDao.saveBatch(refUsers);

    }

    public List<String> listNeedComputeMainUserId(String companyId, String changedUserId) {
        ComQB evalQb = ComQB.build(RefEvalDo.class, "r")
                .clearSelect().setRsType(String.class).select("distinct main_eval_id")
                .whereEqReq("companyId", companyId)
                .appendWhere("main_eval_id = '" + changedUserId + "' or ref_eval_id = '" + changedUserId + "'");
        return autoBaseDao.listAll(evalQb);
    }

    //weight_of_ref
    public void removeRefEval(String companyId, List<String> taskUserIds) {
        UpdateBuilder up = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                .whereEqReq("company_id", companyId)
                .whereInReq("id", taskUserIds)
                .appendSet("weight_of_ref = null")
                .appendSet("score_of_ref = null");
        autoBaseDao.update(up);

        DeleteBuilder del = DeleteBuilder.build(RefEvalDo.class)
                .whereEqReq("company_id", companyId)
                .whereInReq("mainEvalId", taskUserIds);
        autoBaseDao.delete(del);
    }


    public PagedList<TaskEvalItemPo> pagedImportTarget1(TaskTargetQuery query) {

        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn(" u.company_id = e.company_id and u.emp_id = e.employee_id and e.is_delete = 'false' ")
                .join(PerfEvaluateTaskKpiDo.class, "k")
                .appendOn(" k.company_id = u.company_id and u.id = k.task_user_id and u.emp_id = k.emp_id and k.is_deleted = 'false' ")
                .leftJoin(EmpEvalTypeUsedFieldDo.class, "uf")
                .appendOn("k.company_id = uf.company_id and u.id = uf.task_user_id and k.kpi_type_id = uf.kpi_type_id and uf.field_id = 'targetValue' and uf.is_deleted = 'false' ")
                .setRsType(TaskEvalItemPo.class)
                .clearSelect().select("u.id as taskUserId, u.emp_id,u.avatar,u.emp_name,u.emp_org_name as orgName,u.eval_org_id,u.eval_org_name,e.jobnumber as jobNumber")
                .whereEqReq("u.company_id", query.getCompanyId())
                .whereEqReq("u.task_id", query.getTaskId())
                .whereEq("u.task_status", "drawUpIng")
                .whereIn("k.kpi_item_id", query.getKpiItemIds())
                .whereIn("u.emp_id", query.getEmpIds())
                .appendWhere(" (k.item_type = 'measurable' or (k.item_type = 'non-measurable' and k.show_target_value = 'true')) ")
                .whereEq("u.is_deleted", "false")
                .groupBy("u.id")
                .appendWhere("if(k.item_type = 'non-measurable',k.input_format != 'text',k.input_format = 'num')")
                .appendWhere("if(uf.show is not null,uf.show = 1,uf.show is null)");
        comQB.setPage(query.getPageNo(), query.getPageSize());
        PagedList<TaskEvalItemPo> pagedList = autoBaseDao.listPage(comQB);
        if (CollUtil.isEmpty(pagedList.getData())) {
            return pagedList;
        }
        List<String> taskUserIds = CollUtil.map(pagedList.getData(), e -> e.getTaskUserId(), true);
        ComQB kpiQb1 = ComQB.build(PerfEvaluateTaskKpiDo.class, "k")
                .join(EmpEvalTypeUsedFieldDo.class, "uf")
                .appendOn("k.company_id = uf.company_id and k.task_user_id = uf.task_user_id and k.kpi_type_id = uf.kpi_type_id and uf.field_id = 'targetValue' and uf.is_deleted = 'false' ")
                .clearSelect().select("k.item_target_value,k.item_unit,k.kpi_item_id,k.kpi_item_name,k.task_user_id,'目标值' as kpiItemFieldName,uf.field_id,uf.sort")
                .setRsType(EvalItemPo.class)
                .whereEqReq("k.company_id", query.getCompanyId())
                .whereEqReq("k.is_deleted", "false")
                .whereIn("k.task_user_id", taskUserIds)
                .appendWhere("if(k.item_type = 'non-measurable',k.input_format != 'text',k.input_format = 'num')")
                .appendWhere("if(uf.show is not null,uf.show = 1,uf.show is null)");

        ComQB kpiQb2 = ComQB.build(PerfEvaluateTaskKpiDo.class, "k")
                .join(EmpEvalTypeUsedFieldDo.class, "uf")
                .appendOn("k.company_id = uf.company_id and k.task_user_id = uf.task_user_id and k.kpi_type_id = uf.kpi_type_id and uf.admin_type = 0 and uf.is_deleted = 'false' ")
                .join(PerfEvaluateItemUsedFieldDo.class, "iuf")
                .appendOn("iuf.task_user_id = uf.task_user_id and iuf.field_id = uf.field_id and iuf.is_deleted = 'false' and iuf.kpi_item_id = k.kpi_item_id")
                .clearSelect().select("iuf.value as itemTargetValue,k.item_unit,k.kpi_item_id,k.kpi_item_name,k.task_user_id ,uf.name as kpiItemFieldName,uf.field_id,uf.sort")
                .setRsType(EvalItemPo.class)
                .whereEqReq("k.company_id", query.getCompanyId())
                .whereEqReq("k.is_deleted", "false")
                .whereIn("k.task_user_id", taskUserIds)
                .appendWhere("if(k.item_type = 'non-measurable',k.input_format != 'text',k.input_format = 'num')")
                .appendWhere("if(uf.show is not null,uf.show = 1,uf.show is null)");

        kpiQb1.union(kpiQb2);

        ComQB comQB1 = ComQB.build().fromQ(kpiQb1, "aa").clearSelect().select(" * ")
                .setRsType(EvalItemPo.class)
                .orderByAsc("aa.sort");

        List<EvalItemPo> itemPos = autoBaseDao.listAll(comQB1);
        Map<String, List<EvalItemPo>> itemMap = itemPos.stream().collect(Collectors.groupingBy(EvalItemPo::getTaskUserId));
        for (TaskEvalItemPo datum : pagedList.getData()) {
            datum.setItems(itemMap.get(datum.getTaskUserId()));
        }
        return pagedList;
    }

    public PagedList<ExportTargetValue> pagedImportTarget(TaskTargetQuery query) {

        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn(" u.company_id = e.company_id and u.emp_id = e.employee_id ")
                .join(PerfEvaluateTaskKpiDo.class, "k")
                .appendOn(" k.company_id = u.company_id and u.id = k.task_user_id and u.emp_id = k.emp_id and k.is_deleted = 'false' ")
//                .leftJoin(EmpEvalTypeUsedFieldDo.class, "uf")
//                .appendOn("k.company_id = uf.company_id and u.id = uf.task_user_id and k.kpi_type_id = uf.kpi_type_id and uf.is_deleted = 'false' ")
                .setRsType(ExportTargetValue.class)
                .clearSelect().select("u.id as taskUserId, u.emp_id,u.avatar,u.emp_name,u.emp_org_name as orgName,u.eval_org_id,u.eval_org_name,e.jobnumber as jobNumber")
                .whereEqReq("u.company_id", query.getCompanyId())
                .whereEqReq("u.task_id", query.getTaskId())
                .whereEq("u.task_status", "drawUpIng")
                .whereIn("k.kpi_item_id", query.getKpiItemIds())
                .whereIn("u.emp_id", query.getEmpIds())
                .appendWhere(" (k.item_type = 'measurable' or (k.item_type = 'non-measurable' and k.show_target_value = 'true')) ")
                .whereEq("u.is_deleted", "false")
                .groupBy("u.id")
                .appendWhere("if(k.item_type = 'non-measurable',k.input_format != 'text',k.input_format = 'num')")
//                .appendWhere("if(uf.show is not null,uf.show = 1,uf.show is null)")
                .appendWhere("exists( select 1 from emp_eval_type_used_field as uf\n" +
                        "                  where k.company_id = uf.company_id and u.id = uf.task_user_id and\n" +
                        "                        k.kpi_type_id = uf.kpi_type_id and uf.is_deleted = 'false' and  if(uf.show is not null, uf.show = 1, uf.show is null)\n" +
                        "                  and ( uf.field_id = 'targetValue' or (uf.type = 2 and uf.admin_type = 0)))");

        //管理范围
        if (CollUtil.isNotEmpty(query.getAdminOrgIds())) {
            if (Objects.nonNull(query.getPerformanceType())) {
                if (query.getPerformanceType() == 1) {
                    comQB.whereIn("u.org_id", query.getAdminOrgIds());
                } else {
                    comQB.whereIn("u.eval_org_id", query.getAdminOrgIds());
                }
            }
        }

        comQB.setPage(query.getPageNo(), query.getPageSize());
        PagedList<ExportTargetValue> pagedList = autoBaseDao.listPage(comQB);
        if (CollUtil.isEmpty(pagedList.getData())) {
            return pagedList;
        }
        return pagedList;
    }

    /**
     * pagedImportTargetOpt 优化 - 替代EXISTS子查询
     * 将EXISTS子查询拆分为两个批量查询，在Java内存中进行匹配
     *
     * @param query 查询条件
     * @return 分页结果
     */
    public PagedList<ExportTargetValue> pagedImportTargetOptimized(TaskTargetQuery query) {

        // ========== 第一步：执行基础查询（不包含EXISTS条件） ==========
        // 这里执行原来的主查询，但去掉了EXISTS子查询部分
        // 目的是先获取所有可能符合条件的基础数据
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn(" u.company_id = e.company_id and u.emp_id = e.employee_id ")
                .join(PerfEvaluateTaskKpiDo.class, "k")
                .appendOn(" k.company_id = u.company_id and u.id = k.task_user_id and u.emp_id = k.emp_id and k.is_deleted = 'false' ")
                .setRsType(ExportTargetValue.class)
                .clearSelect().select("u.id as taskUserId, u.emp_id,u.avatar,u.emp_name,u.emp_org_name as orgName,u.eval_org_id,u.eval_org_name,e.jobnumber as jobNumber")
                .whereEqReq("u.company_id", query.getCompanyId())
                .whereEqReq("u.task_id", query.getTaskId())
                .whereEq("u.task_status", "drawUpIng")
                .whereIn("k.kpi_item_id", query.getKpiItemIds())
                .whereIn("u.emp_id", query.getEmpIds())
                .appendWhere(" (k.item_type = 'measurable' or (k.item_type = 'non-measurable' and k.show_target_value = 'true')) ")
                .whereEq("u.is_deleted", "false")
                .groupBy("u.id")
                .appendWhere("if(k.item_type = 'non-measurable',k.input_format != 'text',k.input_format = 'num')");

        // ========== 第二步：添加管理范围条件 ==========
        // 这部分逻辑保持不变，根据管理范围过滤数据
        if (CollUtil.isNotEmpty(query.getAdminOrgIds())) {
            if (Objects.nonNull(query.getPerformanceType())) {
                if (query.getPerformanceType() == 1) {
                    comQB.whereIn("u.org_id", query.getAdminOrgIds());
                } else {
                    comQB.whereIn("u.eval_org_id", query.getAdminOrgIds());
                }
            }
        }

        // ========== 第三步：执行分页查询 ==========
        // 设置分页参数并执行查询，获取基础结果集
        comQB.setPage(query.getPageNo(), query.getPageSize());
        PagedList<ExportTargetValue> pagedList = autoBaseDao.listPage(comQB);

        // 如果基础查询没有结果，直接返回空结果
        if (CollUtil.isEmpty(pagedList.getData())) {
            return pagedList;
        }

        // ========== 第四步：提取需要验证的任务用户ID列表 ==========
        // 从基础查询结果中提取所有的taskUserId，用于后续的批量验证
        List<String> taskUserIds = pagedList.getData().stream()
                .map(ExportTargetValue::getTaskUserId)
                .collect(Collectors.toList());

        // ========== 第五步：批量检查emp_eval_type_used_field记录 ==========
        // 这是核心优化点：用一次批量查询替代原来的EXISTS子查询
        // 返回每个taskUserId是否符合条件的映射关系
        Map<String, Boolean> validTaskUserMap = batchCheckEvalTypeUsedField(
                query.getCompanyId(), taskUserIds);

        // ========== 第六步：在Java内存中过滤结果 ==========
        // 根据批量查询的结果，过滤掉不符合条件的记录
        // 这里用Java Stream API进行高效的内存过滤
        List<ExportTargetValue> filteredData = pagedList.getData().stream()
                .filter(item -> validTaskUserMap.getOrDefault(item.getTaskUserId(), false))
                .collect(Collectors.toList());

        // 注意：这里的分页信息是基于第一步查询出来的结果进行的分页，如果第六步过滤掉了部分数据会导致分页错误
        //清空data数据，重新把过滤后的数据放入分页结果集中
        pagedList.clear();
        pagedList.addAll(filteredData);
        return pagedList;
    }

    /**
     * 批量检查emp_eval_type_used_field记录
     * 这是核心优化方法，将原来的EXISTS子查询拆分为两个批量查询
     *
     * @param companyId 公司ID
     * @param taskUserIds 需要检查的任务用户ID列表
     * @return 每个taskUserId是否符合条件的映射关系
     */
    private Map<String, Boolean> batchCheckEvalTypeUsedField(String companyId, List<String> taskUserIds) {
        // 参数校验：如果任务用户ID列表为空，直接返回空映射
        if (CollUtil.isEmpty(taskUserIds)) {
            return new HashMap<>();
        }

        // ========== 子步骤1：批量查询所有相关的KPI类型 ==========
        // 查询所有任务用户对应的KPI类型ID
        // 这个查询替代了原来EXISTS子查询中的关联条件
        ComQB kpiQuery = ComQB.build(PerfEvaluateTaskKpiDo.class, "k")
                .select("k.task_user_id, k.kpi_type_id")  // 只查询需要的字段
                .whereEq("k.company_id", companyId)       // 公司ID条件
                .whereIn("k.task_user_id", taskUserIds)   // 批量查询多个任务用户
                .whereEq("k.is_deleted", "false")         // 未删除条件
                .setRsType(PerfEvaluateTaskKpiDo.class);

        List<PerfEvaluateTaskKpiDo> kpiResults = autoBaseDao.listAll(kpiQuery);

        // ========== 子步骤2：构建任务用户ID到KPI类型ID列表的映射 ==========
        // 将查询结果转换为Map结构，便于后续匹配
        // Key: taskUserId, Value: 该用户对应的所有KPI类型ID集合
        Map<String, Set<String>> taskUserKpiTypeMap = kpiResults.stream()
                .collect(Collectors.groupingBy(
                        PerfEvaluateTaskKpiDo::getTaskUserId,  // 按task_user_id分组
                        Collectors.mapping(
                                PerfEvaluateTaskKpiDo::getKpiTypeId, // 提取kpi_type_id
                                Collectors.toSet()                      // 收集为Set去重
                        )
                ));

        // ========== 子步骤3：批量查询emp_eval_type_used_field记录 ==========
        // 查询所有符合条件的字段使用记录
        // 这个查询替代了原来EXISTS子查询中的主表查询
        ComQB fieldQuery = ComQB.build(EmpEvalTypeUsedFieldDo.class, "uf")
                .select("uf.task_user_id, uf.kpi_type_id")  // 只查询需要的字段
                .whereEq("uf.company_id", companyId)        // 公司ID条件
                .whereIn("uf.task_user_id", taskUserIds)    // 批量查询多个任务用户
                .whereEq("uf.is_deleted", "false")          // 未删除条件
                .appendWhere("(uf.show IS NULL OR uf.show = 1)")  // 显示条件：null或1
                .appendWhere("(uf.field_id = 'targetValue' OR (uf.type = 2 AND uf.admin_type = 0))") // 字段条件
                .setRsType(EmpEvalTypeUsedFieldDo.class);

        List<EmpEvalTypeUsedFieldDo> fieldResults = autoBaseDao.listAll(fieldQuery);

        // ========== 子步骤4：构建任务用户ID到有效KPI类型ID集合的映射 ==========
        // 将字段查询结果转换为Map结构
        // Key: taskUserId, Value: 该用户对应的所有有效KPI类型ID集合
        Map<String, Set<String>> taskUserValidKpiTypeMap = fieldResults.stream()
                .collect(Collectors.groupingBy(
                        EmpEvalTypeUsedFieldDo::getTaskUserId,  // 按task_user_id分组
                        Collectors.mapping(
                                EmpEvalTypeUsedFieldDo::getKpiTypeId, // 提取kpi_type_id
                                Collectors.toSet()                      // 收集为Set去重
                        )
                ));

        // ========== 子步骤5：检查每个任务用户是否有匹配的KPI类型 ==========
        // 这是核心逻辑：检查每个任务用户的KPI类型是否在有效KPI类型集合中
        // 如果存在交集，说明该任务用户符合条件
        Map<String, Boolean> result = new HashMap<>();
        for (String taskUserId : taskUserIds) {
            // 获取该任务用户的所有KPI类型
            Set<String> userKpiTypes = taskUserKpiTypeMap.getOrDefault(taskUserId, Collections.emptySet());
            // 获取该任务用户的所有有效KPI类型
            Set<String> validKpiTypes = taskUserValidKpiTypeMap.getOrDefault(taskUserId, Collections.emptySet());

            // 检查是否有交集：如果用户的KPI类型中有任何一个在有效KPI类型集合中，则符合条件
            boolean hasMatch = userKpiTypes.stream().anyMatch(validKpiTypes::contains);
            result.put(taskUserId, hasMatch);
        }

        return result;
    }


    public List<EvalItemPo> listItemHead(TaskTargetQuery query) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(PerfEvaluateTaskKpiDo.class, "k")
                .appendOn(" k.company_id = u.company_id and u.id = k.task_user_id and u.emp_id = k.emp_id and k.is_deleted = 'false' ")
                .leftJoin(EmpEvalTypeUsedFieldDo.class, "uf")
                .appendOn("k.company_id = uf.company_id and u.id = uf.task_user_id and k.kpi_type_id = uf.kpi_type_id and uf.field_id = 'targetValue' and uf.is_deleted = 'false' ")
                .setRsType(EvalItemPo.class)
                .clearSelect().select("kpi_item_id,kpi_item_name,item_unit ")
                .whereEqReq("u.company_id", query.getCompanyId())
                .whereEqReq("u.task_id", query.getTaskId())
                .whereEq("u.task_status", "drawUpIng")
                .whereIn("k.kpi_item_id", query.getKpiItemIds())
                .whereIn("u.emp_id", query.getEmpIds())
                .appendWhere(" (k.item_type = 'measurable' or (k.item_type = 'non-measurable' and k.show_target_value = 'true')) ")
                .whereEq("u.is_deleted", "false")
                .appendWhere("if(uf.show is not null,uf.show = 1,uf.show is null)")
                .appendWhere("if(k.item_type = 'non-measurable',k.input_format != 'text',k.input_format = 'num')")
                .groupBy(" k.kpi_item_id,k.kpi_item_name");
        List<EvalItemPo> items = autoBaseDao.listAll(comQB);
        return items;
    }

    public PagedList<EvalKpiItemPo> pagedEvalItem(EvalItemSelectQuery query) {
        ComQB itemQb = ComQB.build(PerfEvaluateTaskKpiDo.class, "k")
                .join(PerfEvaluateTaskUserDo.class, "u")
                .appendOn(" k.company_id = u.company_id AND k.task_id = u.task_id and u.id = k.task_user_id AND u.is_deleted != 'true' ")
                .clearSelect().select(" k.task_user_id,k.kpi_item_id,k.kpi_item_name,k.emp_id ")
                .setRsType(EvalKpiItemPo.class)
                .whereEqReq("k.company_id", query.getCompanyId())
                .whereEqReq("k.task_id", query.getTaskId())
                .appendWhere(" (k.item_type = 'measurable' or (k.item_type = 'non-measurable' and k.show_target_value = 'true')) ")
                .whereEq("u.task_status", "drawUpIng")
                .whereEq("k.is_deleted", "false")
                .whereLike("k.kpi_item_name", query.getKpiItemName())
                .groupBy("k.kpi_item_id");
        itemQb.setPage(query.getPageNo(), query.getPageSize());
        PagedList<EvalKpiItemPo> items = autoBaseDao.listPage(itemQb);
        return items;
    }

    public PagedList<EmpStaff> pagedEvalEmp(EvalEmpSelectQuery query) {
        ComQB userQb = ComQB.build(PerfEvaluateTaskKpiDo.class, "k")
                .join(PerfEvaluateTaskUserDo.class, "u")
                .appendOn(" k.company_id = u.company_id AND k.task_id = u.task_id and u.id = k.task_user_id AND u.is_deleted != 'true' ")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn(" k.company_id = e.company_id and k.emp_id = e.employee_id ")
                .clearSelect().select("  k.emp_id,e.name as empName ")
                .setRsType(EmpStaff.class)
                .whereEqReq("k.company_id", query.getCompanyId())
                .whereEqReq("k.task_id", query.getTaskId())
                .whereIn("k.kpi_item_id", query.getKpiItemIds())
                .whereEq("k.is_deleted", "false")
                .whereEq("u.task_status", "drawUpIng")
                .whereLike("e.name", query.getEmpName())
                .appendWhere(" (k.item_type = 'measurable' or (k.item_type = 'non-measurable' and k.show_target_value = 'true')) ")
                .groupBy("k.emp_id");
        userQb.setPage(query.getPageNo(), query.getPageSize());
        PagedList<EmpStaff> emps = autoBaseDao.listPage(userQb);
        return emps;
    }

    public PagedList<EmpStaff> pagedEvalOrg(EvalEmpSelectQuery query) {
        ComQB orgQb = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(PerfEvaluateTaskKpiDo.class, "k")
                .appendOn(" u.company_id = k.company_id and u.id = k.task_user_id and k.is_deleted = 'false' ")
                .clearSelect().select(" u.eval_org_id as orgId, u.eval_org_name as orgName ")
                .setRsType(EmpStaff.class)
                .whereEqReq("u.company_id", query.getCompanyId())
                .whereEqReq("u.task_id", query.getTaskId())
                .whereIn("k.kpi_item_id", query.getKpiItemIds())
                .whereEq("u.is_deleted", "false")
                .whereEq("u.task_status", "drawUpIng")
                .whereLike("u.eval_org_name", query.getOrgName())
                .appendWhere(" (k.item_type = 'measurable' or (k.item_type = 'non-measurable' and k.show_target_value = 'true')) ")
                .groupBy("u.eval_org_id");
        orgQb.setPage(query.getPageNo(), query.getPageSize());
        PagedList<EmpStaff> orgs = autoBaseDao.listPage(orgQb);
        return orgs;
    }

    public Integer getPerformanceType(String companyId, String taskId, String taskUserId) {
        if (StrUtil.isEmpty(taskId) && StrUtil.isEmpty(taskUserId)) {
            new KpiI18NException("file has exception ", "文件解析存在异常");
        }

        ComQB comQB = ComQB.build(PerfEvaluateTaskBaseDo.class, "b")
                .join(PerfEvaluateTaskUserDo.class, "u")
                .appendOn(" b.company_id = u.company_id and b.id = u.task_id and b.is_deleted = 'false' and u.is_deleted= 'false' ")
                .clearSelect().select(" b.performance_type ")
                .setRsType(Integer.class)
                .whereEqReq("b.company_id", companyId)
                .whereEq("b.id", taskId)
                .whereEq("u.id", taskUserId);
        return autoBaseDao.findOne(comQB);
    }

    /**
     * 查询变更任务okr缓存信息
     */
    public List<EmpChangeItemStagePo> listCacheOkrKpi(TenantId tenantId, String taskUserId, String empId, List<String> scenes) {
        ComQB comQB = ComQB.build(CompanyCacheInfoDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereInReq("business_scene", scenes)
                .whereEqReq("link_id", taskUserId)
                .whereEqReq("cache_key", taskUserId)
                .whereEqReq("is_deleted", Boolean.FALSE.toString())
                .orderByDesc("created_time")
                .limit(0, 1);
        CompanyCacheInfo cacheInfo = autoBaseDao.findDomain(comQB, CompanyCacheInfo.class);
        if (cacheInfo == null || StrUtil.isBlank(cacheInfo.getValue())) {
            return Collections.emptyList();
        }
        //List<ChangeItemStageCache> cacheKpiTypes = JSONUtil.parseArray(cacheInfo.getValue()).toList(ChangeItemStageCache.class);
        List<EmpChangeItemStagePo> cacheKpiTypes = JSONObject.parseArray(cacheInfo.getValue(), EmpChangeItemStagePo.class);
        return cacheKpiTypes;
    }


    //okr查询任务列表
    public List<OKRGetTaskReturnVO> queryOKRTaskList(OKRTaskQueryVO queryVO) {
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(OKRGetTaskReturnVO.class).setSql("SELECT b.id as taskId, b.task_name, b.cycle_start_date, b.cycle_end_date, b.cycle_type, u.task_status  " +
                "                FROM perf_evaluate_task_user u  " +
                "                LEFT JOIN perf_evaluate_task_base b ON u.task_id = b.id   " +
                "                WHERE b.is_deleted = 'false' AND b.task_status = 'published' AND u.is_deleted = 'false' AND u.task_status NOT in ('terminated','drawUpIng') " +
                "                AND u.company_id = #{companyId} ");
        sqlBuilder.setValue("companyId", queryVO.getCompanyId());
        sqlBuilder.appendIfOpt("AND u.emp_id = #{empId}", "empId", queryVO.getEmpId());
        sqlBuilder.appendIfOpt("AND b.cycle_start_date >= #{startTime}", "startTime", queryVO.getStartTime());
        sqlBuilder.appendIfOpt("AND b.cycle_start_date <= #{endTime}", "endTime", queryVO.getEndTime());
        sqlBuilder.appendIfOpt("AND u.task_status = #{taskStatus}", "taskStatus", queryVO.getTaskStatus());
        sqlBuilder.appendIfOpt("AND b.task_name LIKE CONCAT('%',#{keyword},'%')", "keyword", queryVO.getKeyword());
        sqlBuilder.append("ORDER BY b.created_time DESC");
        return autoBaseDao.listAll(sqlBuilder);
    }

    /**
     * 根据taskUserId批量查询最后一级已校准的按指标校准的数据
     */
    public List<PerfEvaluateTaskScoreResultDo> listTaskResultIndexCalibration(String companyId, List<String> taskUserIds) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .clearSelect().select("task_user_id,index_calibration").setRsType(PerfEvaluateTaskScoreResultDo.class)
                .whereEqReq("company_id", companyId)
                .whereInReq("task_user_id", taskUserIds)
                .whereEq("scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene())
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEq("audit_status", "pass")
                //.whereEq("calibration_type", 3)
                .whereNotNull("index_calibration")
                .orderBy("approval_order desc")
                .limit(0, 999999);
        ComQB resultQb = ComQB.build().fromQ(comQB, "a").setRsType(PerfEvaluateTaskScoreResultDo.class)
                .groupBy("a.task_user_id");
        return autoBaseDao.listAll(resultQb);
    }


    public ListWrap<IndexCalibration> indexCalibrationListWrap(String companyId, List<String> taskUserIds) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .clearSelect().select("task_user_id,index_calibration").setRsType(PerfEvaluateTaskScoreResultDo.class)
                .whereEqReq("company_id", companyId)
                .whereInReq("task_user_id", taskUserIds)
                .whereEq("scorer_type", EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene())
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEq("audit_status", "pass")
                //.whereEq("calibration_type", 3)
                .whereNotNull("index_calibration")
                .orderBy("approval_order desc")
                .limit(0, 999999);
        ComQB resultQb = ComQB.build().fromQ(comQB, "a").setRsType(PerfEvaluateTaskScoreResultDo.class)
                .groupBy("a.task_user_id");
        List<PerfEvaluateTaskScoreResultDo> scoreResultDos = autoBaseDao.listAll(resultQb);
        if (CollUtil.isEmpty(scoreResultDos)) {
            return new ListWrap<>();
        }
        List<IndexCalibration> list = scoreResultDos.stream().flatMap(r -> r.getIndexCalibration().stream()).collect(Collectors.toList());
        if (CollUtil.isEmpty(list)) {
            return new ListWrap<>();
        }
        return new ListWrap<>(list).asMap(r -> r.getTaskUserId() + "|" + r.getKpiItemId());
    }

    public List<EvalItem> listEvalItem(String companyId, List<String> taskUserIds, List<String> kpiItemIds) {

        ComQB kpiQb = ComQB.build(PerfEvaluateTaskKpiDo.class, "k")
                .clearSelect().select("k.item_unit,k.kpi_item_id,k.kpi_item_name,k.task_user_id")
                .setRsType(EvalItem.class)
                .whereEqReq("k.company_id", companyId)
                .whereEqReq("k.is_deleted", "false")
                .whereIn("k.task_user_id", taskUserIds)
                .whereIn("k.kpi_item_id", kpiItemIds)
                .appendWhere(" (k.item_type = 'measurable' or (k.item_type = 'non-measurable' and k.show_target_value = 'true')) ")
                .appendWhere("if(k.item_type = 'non-measurable',k.input_format != 'text',k.input_format = 'num')")
                .orderBy("k.type_order", "k.`order`");
        return autoBaseDao.listAll(kpiQb);
    }

    public List<EvalItemField> listEvalField(String companyId, List<String> taskUserIds, List<String> itemIds) {

        ComQB kpiQb1 = ComQB.build(PerfEvaluateItemUsedFieldDo.class, "iuf")
                .join(EmpEvalTypeUsedFieldDo.class, "tuf")
                .appendOn("iuf.task_user_id = tuf.task_user_id and iuf.field_id = tuf.field_id  and  EXISTS (\n" +
                        " SELECT 1 FROM perf_evaluate_task_kpi AS k WHERE k.task_user_id = iuf.task_user_id AND k.kpi_item_id = iuf.kpi_item_id AND k.is_deleted = false AND k.kpi_type_id = tuf.kpi_type_id)")
                .clearSelect().select("tuf.sort,iuf.kpi_item_id,tuf.name as fieldName,tuf.field_id,if(tuf.unit_switch = 1, iuf.field_unit, '') as field_unit," +
                        "if(iuf.value is null,'' ,iuf.value ) as fieldValue ," +
                        "iuf.task_user_id")
                .setRsType(EvalItemField.class)
                .whereIn("iuf.task_user_id", taskUserIds)
                .whereIn("iuf.kpi_item_id", itemIds)
                .whereEq("iuf.company_id", companyId)
                .whereEq("iuf.is_deleted", "false")
//                .whereEq("iuf.show", 1)
                .appendWhere("if(iuf.show is not null,iuf.show = 1,iuf.show is null)")
                .whereEq("iuf.type", 2)
                .whereEq("iuf.status", "valid")
                .whereEq("tuf.is_deleted", "false")
                .whereEq("tuf.show", 1)
                .whereEq("tuf.admin_type", 0);


        ComQB kpiQb2 = ComQB.build(PerfEvaluateTaskKpiDo.class, "k")
                .join(EmpEvalTypeUsedFieldDo.class, "tuf1 FORCE INDEX (idx_taskUserId_companyId)")
                .appendOn("k.task_user_id = tuf1.task_user_id and k.kpi_type_id = tuf1.kpi_type_id")
                .clearSelect().select("tuf1.sort,k.kpi_item_id,'目标值' as fieldName,tuf1.field_id")
                //子查询查是否展示单位
                .select("if(tuf1.field_id = 'unit' and tuf1.`show` = 1, k.item_unit, '')  as field_unit")
                .select ("if(k.item_target_value is null, '', k.item_target_value)  as fieldValue," +
                        "k.task_user_id")
                .setRsType(EvalItemField.class)
                .whereIn("k.task_user_id", taskUserIds)
                .whereIn("k.kpi_item_id", itemIds)
                .whereEq("k.company_id", companyId)
                .whereEq("tuf1.name", "目标值")
                .whereEq("k.is_deleted", "false")
                .whereEq("tuf1.is_deleted", "false")
                .whereEq("tuf1.show", 1)
                .whereEq("tuf1.admin_type", 1);

        kpiQb1.union(kpiQb2);

        ComQB comQB = ComQB.build().fromQ(kpiQb1, "aa").clearSelect().select(" * ")
                .setRsType(EvalItemField.class)
                .orderByAsc("aa.sort");

        return autoBaseDao.listAll(comQB);
    }

    public List<String> listTaskIdWithAppealOpen(Integer pageNo, Integer pageSize, String beginDate, String endDate) {

        ComQB comQB = ComQB.build(PerfEvaluateTaskBaseDo.class)
                .clearSelect().select("id")
                .setRsType(String.class)
                .whereEq("is_deleted", "false")
                .appendWhere(" `appeal_conf` like '%\"open\":1%' ")
                .orderBy("created_time asc")
                .setPage(pageNo, pageSize);

        if (StringUtils.isNotBlank(beginDate)) {
            comQB.appendWhere("created_time >= #{beginDate}", beginDate);
        }
        if (StringUtils.isNotBlank(endDate)) {
            comQB.appendWhere("created_time <= #{endDate}", endDate);
        }

        return autoBaseDao.listPage(comQB);
    }


    public List<String> listTaskIdWithAppealOpenWithUpdateTime(Integer pageNo, Integer pageSize, String beginDate, String endDate) {

        ComQB comQB = ComQB.build(PerfEvaluateTaskBaseDo.class)
                .clearSelect().select("id")
                .setRsType(String.class)
                .whereEq("is_deleted", "false")
                .appendWhere(" `appeal_conf` like '%\"open\":1%' ")
                .orderBy("created_time asc")
                .setPage(pageNo, pageSize);

        if (StringUtils.isNotBlank(beginDate)) {
            comQB.appendWhere("updated_time >= #{beginDate}", beginDate);
        }
        if (StringUtils.isNotBlank(endDate)) {
            comQB.appendWhere("updated_time <= #{endDate}", endDate);
        }

        return autoBaseDao.listPage(comQB);
    }
}
