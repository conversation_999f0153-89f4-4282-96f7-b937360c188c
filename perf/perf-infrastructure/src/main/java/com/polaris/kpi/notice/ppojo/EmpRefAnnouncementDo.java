package com.polaris.kpi.notice.ppojo;

import com.polaris.kpi.notice.domain.entity.Announcement;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.http.impl.cookie.DateUtils;

import java.util.Date;

@Getter
@Setter
public class EmpRefAnnouncementDo {
    private String empId;
    private String announcementId;
    private String createdTime;

    public EmpRefAnnouncementDo(Announcement query) {
        this.empId = query.getEmpId();
        this.announcementId = query.getId();
        this.createdTime = DateUtils.formatDate(new Date());
    }
}
