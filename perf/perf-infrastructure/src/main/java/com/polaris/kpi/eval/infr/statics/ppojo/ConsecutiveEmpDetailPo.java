package com.polaris.kpi.eval.infr.statics.ppojo;

import com.polaris.kpi.eval.domain.cycle.entity.PerfStatisticRule;
import com.polaris.kpi.eval.domain.cycle.type.StatisticRuleItem;
import com.polaris.kpi.eval.domain.statics.entity.HistoryCycle;
import lombok.Data;
import org.apache.ibatis.annotations.JsonAryColumn;
import org.apache.ibatis.annotations.JsonColumn;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/23 21:14
 */
@Data
public class ConsecutiveEmpDetailPo {

    private String id;
    private String companyId;
    private String cycleId;
    private String cycleTime;
    private String taskId;
    private String taskName;
    private String taskUserId;
    private String empId;
    private String empName;
    private String avatar;
    private String orgId;
    private String orgName;
    private String atOrgCodePath;
    private String atOrgNamePath;
    private String finalScore;
    private String evaluationLevel;
    private String perfCoefficient;
    private String showResultType;
    /**
     * 规则ID 默认规则为null
     */
    private String hitRuleId;

    private String ruleName;

    private String ruleDesc;
    /**
     * 连续类型 1、连续绩优 2、连续绩差
     */
    private Integer consecutiveType;
    /**
     * 历史周期数据
     */
    @JsonAryColumn(HistoryCycle.class)
    private List<HistoryCycle> historyCycles;

    @JsonColumn
    private PerfStatisticRule perfStatisticRule;

    @JsonAryColumn(StatisticRuleItem.class)
    private List<StatisticRuleItem> statisticRuleItems;

    /**
     * 统计时长
     */
    private Integer statisticDuration;

    /**
     * 统计考核周期类型
     */
    private String cycleType;

}
