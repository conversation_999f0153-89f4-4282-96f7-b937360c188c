package com.polaris.kpi.eval.infr.statics.query;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.polaris.kpi.common.infr.AdminScopeOrgData;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/18 16:33
 */
@Data
public class ConsecutiveQuery extends AdminScopeOrgData {

    private String companyId;
    private String cycleId;
    private String orgId;
    private List<String> orgIds;
    private String roleName;
    private List<String> roleNames;
    private String loginEmpId;
    private Integer performanceType;
    private Integer ruleType;

    private int pageNo = 1;
    private Integer pageSize = 10;


    public void splitTrimOrg() {
        if (StrUtil.isBlank(this.orgId) && CollUtil.isEmpty(this.orgIds)) {
            return;
        }
        if (StrUtil.isNotBlank(this.orgId)) {
            this.orgIds = StrUtil.splitTrim(this.orgId, ",");
        }
    }

    public void splitTrimRole() {
        if (StrUtil.isBlank(this.roleName) && CollUtil.isEmpty(this.roleNames)) {
            return;
        }
        if (StrUtil.isNotBlank(this.roleName)) {
            this.roleNames = StrUtil.splitTrim(this.roleName, ",");
        }
    }
}
