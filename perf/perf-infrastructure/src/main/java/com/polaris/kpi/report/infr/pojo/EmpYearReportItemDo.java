package com.polaris.kpi.report.infr.pojo;

import com.polaris.kpi.common.infr.DelData;
import com.polaris.kpi.report.domain.entity.EmpYearItemRs;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.annotations.Ckey;
import org.apache.ibatis.annotations.JsonColumn;

@Getter
@Setter
public class EmpYearReportItemDo extends DelData {
    @Ckey
    private String id;//
    private String reportId;//报告id,emp_year_report.id
    private String empId;//用户id
    private Integer year;//年份
    @JsonColumn
    private EmpYearItemRs weightYearAtMonth;//月度加权计算出的年度结果
    @JsonColumn
    private EmpYearItemRs weightYearAtQuarter;//季度加权计算出的年度结果

    @JsonColumn
    private EmpYearItemRs year1;//年度结果
    @JsonColumn
    private EmpYearItemRs halfYear1;//上半年
    @JsonColumn
    private EmpYearItemRs halfYear2;//下半年
    @JsonColumn
    private EmpYearItemRs quarter1;//第一季度
    @JsonColumn
    private EmpYearItemRs quarter2;//第二季度
    @JsonColumn
    private EmpYearItemRs quarter3;//第三季度
    @JsonColumn
    private EmpYearItemRs quarter4;//第四季度
    @JsonColumn
    private EmpYearItemRs month1;//1月
    @JsonColumn
    private EmpYearItemRs month2;//2月
    @JsonColumn
    private EmpYearItemRs month3;//3月
    @JsonColumn
    private EmpYearItemRs month4;//4月
    @JsonColumn
    private EmpYearItemRs month5;//5月
    @JsonColumn
    private EmpYearItemRs month6;//6月
    @JsonColumn
    private EmpYearItemRs month7;//7月
    @JsonColumn
    private EmpYearItemRs month8;//8月
    @JsonColumn
    private EmpYearItemRs month9;//9月
    @JsonColumn
    private EmpYearItemRs month10;//10月
    @JsonColumn
    private EmpYearItemRs month11;//11月
    @JsonColumn
    private EmpYearItemRs month12;//12月

}
