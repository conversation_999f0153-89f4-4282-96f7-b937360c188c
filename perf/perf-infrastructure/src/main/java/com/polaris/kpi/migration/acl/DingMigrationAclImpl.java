package com.polaris.kpi.migration.acl;

import cn.hutool.core.io.FileUtil;
import com.perf.www.common.utils.oss.OssUtil;
import com.polaris.kpi.migration.entity.MigrationFile;
import com.polaris.kpi.migration.entity.MigrationJdbcBatchExecutor;
import com.polaris.kpi.org.domain.dept.entity.Company;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.lufei.ibatis.builder.NativeSQLBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * 钉钉迁移仓储实现类
 * 实现所有迁移相关的仓储方法，包括DMS调用、OSS调用、HTTP回调等
 */
@Service
public class DingMigrationAclImpl implements IDingMigrationAcl {

    private static final Logger log = LoggerFactory.getLogger(DingMigrationAclImpl.class);
    @Autowired
    private DomainDaoImpl domainDao;


    // OSS目录前缀 - 直接写死，无需配置
    private static final String MIGRATION_PREFIX = "export_";
    private static final String PROCESSED_PREFIX = "processed_";
    private static final String FAILED_PREFIX = "failed_";

    // 迁移文件目录路径
    private static final String MIGRATION_BASE_PATH = getMigrationPath();

    @Resource
    private OssUtil ossUtil;
    @Resource
    private MigrationJdbcBatchExecutor jdbcBatchExecutor;

    public void setDomainDao(DomainDaoImpl domainDao) {
        this.domainDao = domainDao;
    }


    /**
     * 获取迁移目录路径
     */
    private static String getMigrationPath() {
        String catalinaHome = System.getProperty("catalina.home");
        String catalinaBase = System.getProperty("catalina.base");

        if (catalinaHome != null && !catalinaHome.trim().isEmpty()) {
            return catalinaHome + File.separator + "migration";
        } else if (catalinaBase != null && !catalinaBase.trim().isEmpty()) {
            return catalinaBase + File.separator + "migration";
        } else {
            return System.getProperty("user.dir") + File.separator + "migration";
        }
    }
    /**
     * 处理本地ZIP文件并执行SQL语句
     */
    public boolean processLocalZipFile(String dingCorpId, String localZipFilePath) {
        try {
            log.info("[processLocalZipFile]开始处理本地ZIP文件: dingCorpId={}, localZipFilePath={}",
                    dingCorpId, localZipFilePath);

            // 检查文件是否存在
            if (!Files.exists(Paths.get(localZipFilePath))) {
                log.error("本地ZIP文件不存在: {}", localZipFilePath);
                return false;
            }

            // 流式解压ZIP文件并分批处理SQL文件
            int totalFiles = 0;
            int skippedFiles = 0;
            int processedFiles = 0;

            try (ZipInputStream zipIn = new ZipInputStream(new FileInputStream(localZipFilePath))) {
                ZipEntry entry;

                while ((entry = zipIn.getNextEntry()) != null) {
                    if (!entry.isDirectory() && entry.getName().toLowerCase().endsWith(".sql")) {
                        totalFiles++;
                        log.info("[processLocalZipFile]发现SQL文件: {}, 大小: {} bytes",
                                entry.getName(), entry.getSize());

                        // 检查文件大小
//                        if (entry.getSize() <= 0) {
//                            log.warn("[processLocalZipFile]跳过空文件: {}, 大小: {} bytes",
//                                    entry.getName(), entry.getSize());
//                            skippedFiles++;
//                            zipIn.closeEntry();
//                            continue;
//                        }

                        // 流式处理SQL文件，每次1000条
                        boolean sqlSuccess = processSqlFileInBatches(zipIn, entry.getName(), 1000);
                        if (sqlSuccess) {
                            processedFiles++;
                            log.info("[processLocalZipFile]ZIP中SQL文件执行成功: dingCorpId={}, fileName={}",
                                    dingCorpId, entry.getName());
                        } else {
                            log.error("[processLocalZipFile]ZIP中SQL文件执行失败: dingCorpId={}, fileName={}",
                                    dingCorpId, entry.getName());
                            // 继续处理其他文件，不中断整个过程
                            log.warn("[processLocalZipFile]继续处理下一个文件，不中断整个迁移过程");
                        }
                    }
                    zipIn.closeEntry();
                }
            }

            log.info("[processLocalZipFile]ZIP文件处理完成: dingCorpId={}, 总文件数={}, 跳过={}, 成功={}",
                    dingCorpId, totalFiles, skippedFiles, processedFiles);

            return processedFiles > 0;

        } catch (Exception e) {
            log.error("[processLocalZipFile]处理本地ZIP文件失败: dingCorpId={}, localZipFilePath={}",
                    dingCorpId, localZipFilePath, e);
            return false;
        }
    }

    /**
     * 根据文件名处理迁移文件
     */
    @Override
    public boolean processMigrationFileByName(String dingCorpId, String fileName) {
        try {
            log.info("[processMigrationFileByName]开始处理迁移文件: dingCorpId={}, fileName={}", dingCorpId, fileName);

            // 获取文件信息
            MigrationFile migrationFile = getMigrationFileByName(fileName);
            if (migrationFile == null) {
                log.error("迁移文件不存在: {}", fileName);
                return false;
            }

            String filePath = migrationFile.getFilePath();
            String fileType = migrationFile.getFileType().toLowerCase();

            // 根据文件类型进行处理
            switch (fileType) {
                case "zip":
                    return processLocalZipFile(dingCorpId, filePath);
                default:
                    log.warn("不支持的文件类型: {}", fileType);
                    return false;
            }

        } catch (Exception e) {
            log.error("[processMigrationFileByName]处理迁移文件失败: dingCorpId={}, fileName={}",
                    dingCorpId, fileName, e);
            return false;
        }
    }


    /**
     * 获取迁移目录中的所有文件
     */
    private List<MigrationFile> getAllMigrationFiles() {
        List<MigrationFile> files = new ArrayList<>();

        try {
            File migrationDir = new File(MIGRATION_BASE_PATH);
            if (migrationDir.exists() && migrationDir.isDirectory()) {
                File[] fileList = migrationDir.listFiles();
                if (fileList != null) {
                    for (File file : fileList) {
                        if (file.isFile()) {
                            MigrationFile migrationFile = createMigrationFile(file);
                            files.add(migrationFile);
                        }
                    }
                }
            }

            // 按修改时间倒序排列
            files.sort((a, b) -> Long.compare(b.getLastModified(), a.getLastModified()));

        } catch (Exception e) {
            log.error("获取迁移文件列表失败: {}", e.getMessage());
        }

        return files;
    }

    /**
     * 根据文件名获取迁移文件
     */
    private MigrationFile getMigrationFileByName(String fileName) {
        try {
            File file = new File(fileName);

            if (file.exists() && file.isFile()) {
                return createMigrationFile(file);
            }

        } catch (Exception e) {
            log.error("获取迁移文件失败: fileName={}", fileName, e);
        }

        return null;
    }

    /**
     * 创建迁移文件对象
     */
    private MigrationFile createMigrationFile(File file) {
        MigrationFile migrationFile = new MigrationFile();
        migrationFile.setFileName(file.getName());
        migrationFile.setFilePath(file.getAbsolutePath());
        migrationFile.setFileSize(file.length());
        migrationFile.setLastModified(file.lastModified());
        migrationFile.setFileType(FilenameUtils.getExtension(file.getName()));
        migrationFile.setFileSizeFormatted(formatFileSize(file.length()));
        migrationFile.setLastModifiedFormatted(formatDate(file.lastModified()));

        return migrationFile;
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.2f KB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.2f MB", bytes / (1024.0 * 1024.0));
        return String.format("%.2f GB", bytes / (1024.0 * 1024.0 * 1024.0));
    }

    /**
     * 格式化日期
     */
    private String formatDate(long timestamp) {
        return new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date(timestamp));
    }
    /**
     * 处理OSS文件（支持ZIP和SQL文件）
     */
    private boolean processFilesFromOss(String dingCorpId, String ossPath) {
        try {
            // 处理ZIP文件
            boolean zipSuccess = processZipFileFromOss(dingCorpId, ossPath);
            if (!zipSuccess) {
                log.error("处理ZIP文件失败: dingCorpId={}, file={}", dingCorpId, ossPath);
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("处理OSS文件失败: dingCorpId={}, ossPath={}", dingCorpId, ossPath, e);
            return false;
        }
    }

    /**
     * 处理ZIP文件
     */
    private boolean processZipFileFromOss(String dingCorpId, String zipFilePath) {
        try {
            log.info("[processZipFileFromOss]开始处理ZIP文件: dingCorpId={}, zipFilePath={}", dingCorpId, zipFilePath);
            // 1. 从OSS下载ZIP文件
            InputStream zipInputStream = ossUtil.getFile(zipFilePath);
            if (zipInputStream == null) {
                log.error("从OSS获取ZIP文件失败: dingCorpId={}, zipFile={}", dingCorpId, zipFilePath);
                return false;
            }

            // 2. 流式解压ZIP文件并分批处理SQL文件
            int totalFiles = 0;
            int skippedFiles = 0;
            try (java.util.zip.ZipInputStream zipIn = new java.util.zip.ZipInputStream(zipInputStream)) {
                java.util.zip.ZipEntry entry;
                int processedFiles = 0;

                while ((entry = zipIn.getNextEntry()) != null) {
                    if (!entry.isDirectory() && entry.getName().toLowerCase().endsWith(".sql")) {
                        totalFiles++;
                        log.info("[processZipFileFromOss]发现SQL文件: {}, 大小: {} bytes", entry.getName(), entry.getSize());

                        // 检查文件大小
                        if (entry.getSize() <= 0) {
                            log.warn("[processZipFileFromOss]跳过空文件: {}, 大小: {} bytes", entry.getName(), entry.getSize());
                            skippedFiles++;
                            zipIn.closeEntry();
                            continue;
                        }

                        // 流式处理SQL文件，每次1000条
                        boolean sqlSuccess = processSqlFileInBatches(zipIn, entry.getName(), 1000);
                        if (sqlSuccess) {
                            processedFiles++;
                            log.info("[processZipFileFromOss]ZIP中SQL文件执行成功: dingCorpId={}, fileName={}", dingCorpId, entry.getName());
                        } else {
                            log.error("[processZipFileFromOss]ZIP中SQL文件执行失败: dingCorpId={}, fileName={}", dingCorpId, entry.getName());
                            // 继续处理其他文件，不中断整个过程
                            log.warn("[processZipFileFromOss]继续处理下一个文件，不中断整个迁移过程");
                        }
                    }
                    zipIn.closeEntry();
                }

                log.info("[processZipFileFromOss]ZIP文件处理完成: dingCorpId={}, 总文件数={}, 成功处理={}, 跳过空文件={}",
                        dingCorpId, totalFiles, processedFiles, skippedFiles);

                // 只要有一个文件处理成功就认为成功
                return processedFiles > 0;
            }

        } catch (Exception e) {
            log.error("[processZipFileFromOss]处理ZIP文件异常: dingCorpId={}, zipFilePath={}", dingCorpId, zipFilePath, e);
            return false;
        }
    }

    private boolean executeZipInBatchesForJdbc(String dingCorpId, MultipartFile file) {
        // 2. 解压ZIP文件并处理其中的SQL文件
        try {
            InputStream zipInputStream = file.getInputStream();
            java.util.zip.ZipInputStream zipIn = new java.util.zip.ZipInputStream(zipInputStream);
            java.util.zip.ZipEntry entry;
            int processedFiles = 0;
            log.info("ZIP文件处理转流[executeZipInBatchesForJdbc]: dingCorpId={}", dingCorpId);
            // 2. 流式解压ZIP文件并分批处理SQL文件
            int totalFiles = 0;
            int skippedFiles = 0;

            while ((entry = zipIn.getNextEntry()) != null) {
                if (!entry.isDirectory() && entry.getName().toLowerCase().endsWith(".sql")) {
                    totalFiles++;
                    log.info("发现SQL文件: {}, 大小: {} bytes", entry.getName(), entry.getSize());
//
//                    // 检查文件大小
//                    if (entry.getSize() <= 0) {
//                        log.warn("跳过空文件: {}, 大小: {} bytes", entry.getName(), entry.getSize());
//                        skippedFiles++;
//                        zipIn.closeEntry();
//                        continue;
//                    }

                    // 流式处理SQL文件，每次1000条
                    boolean sqlSuccess = processSqlFileInBatches(zipIn, entry.getName(), 1000);
                    if (sqlSuccess) {
                        processedFiles++;
                        log.info("ZIP中SQL文件执行成功: dingCorpId={}, fileName={}", dingCorpId, entry.getName());
                    } else {
                        log.error("ZIP中SQL文件执行失败: dingCorpId={}, fileName={}", dingCorpId, entry.getName());
                        // 继续处理其他文件，不中断整个过程
                        log.warn("继续处理下一个文件，不中断整个迁移过程");
                    }
                }
                zipIn.closeEntry();
            }

            log.info("ZIP文件处理完成: dingCorpId={}, 总文件数={}, 成功处理={}, 跳过空文件={}",
                    dingCorpId, totalFiles, processedFiles, skippedFiles);

            // 只要有一个文件处理成功就认为成功
            return processedFiles > 0;

        } catch (Exception e) {
            log.error("处理ZIP文件异常: dingCorpId={},", dingCorpId, e);
            return false;
        }
    }
    /**
     * 每批读取1000条完整SQL
     */
    /**
     * 每批读取1000条完整SQL（简化版本）
     */
    private boolean processSqlFileInBatches(java.util.zip.ZipInputStream zipIn, String fileName, int batchSize) {
        try {
            BufferedReader reader = new BufferedReader(new InputStreamReader(zipIn, StandardCharsets.UTF_8));

            List<String> sqlBatch = new ArrayList<>();
            StringBuilder currentSql = new StringBuilder();
            int totalProcessed = 0;
            int batchCount = 0;
            int sqlCount = 0;
            String tableName = null;

            // 动态批次配置
            int minBatchSize = 50;
            long lastBatchTime = System.currentTimeMillis();
            long maxWaitTime = 3000;
            long startTime = System.currentTimeMillis();
            long lastProgressTime = System.currentTimeMillis();

            // 添加调试变量
            int lastLineCount = 0;
            int consecutiveEmptyLines = 0;
            int maxConsecutiveEmptyLines = 100; // 连续空行阈值

            log.info("开始动态批次处理SQL: 文件={}, 最大批次={}, 最小批次={}", fileName, batchSize, minBatchSize);

            String line;
            int lineCount = 0;

            while ((line = reader.readLine()) != null) {
                lineCount++;

                // 每1000行记录一次进度
                if (lineCount % 1000 == 0) {
                    long currentTime = System.currentTimeMillis();
                    log.info("文件读取进度: 行数={}, SQL数={}, 批次大小={}, 耗时={}ms, 当前SQL长度={}",
                            lineCount, sqlCount, sqlBatch.size(), currentTime - startTime, currentSql.length());
                }

                // 跳过空行和注释行
                if (isCommentOrEmpty(line)) {
                    consecutiveEmptyLines++;
                    if (consecutiveEmptyLines > maxConsecutiveEmptyLines) {
                        log.warn("⚠️ 连续空行过多({}), 可能文件有问题", consecutiveEmptyLines);
                    }
                    continue;
                } else {
                    consecutiveEmptyLines = 0;
                }

                // 累积SQL语句
                if (currentSql.length() > 0) {
                    currentSql.append(" ").append(line.trim());
                } else {
                    currentSql.append(line.trim());
                }

                // 检查是否是完整的SQL语句
                if (isCompleteSqlStatement(currentSql.toString())) {
                    String completeSql = currentSql.toString().trim();

                    // 基本验证SQL有效性
                    if (isValidSqlForExecution(completeSql)) {
                        // 只在第一次解析表名
                        tableName = extractTableName(completeSql, tableName);
                        sqlBatch.add(completeSql);
                        sqlCount++;

                        log.debug("解析到完整SQL {}: 表={}, 长度={} bytes",
                                sqlCount, tableName, completeSql.length());

                        // 检查是否需要执行批次
                        boolean shouldExecuteBatch = shouldExecuteBatch(sqlBatch, batchSize, minBatchSize,
                                lastBatchTime, maxWaitTime);

                        if (shouldExecuteBatch) {
                            String reason = getBatchExecutionReason(sqlBatch, batchSize, lastBatchTime, maxWaitTime);
                            log.info("执行批次: 文件={}, 批次={}, 表={}, 数量={}, 原因={}",
                                    fileName, batchCount + 1, tableName, sqlBatch.size(), reason);

                            boolean batchSuccess = processSqlBatch(sqlBatch, fileName, batchCount, tableName);
                            if (!batchSuccess) {
                                log.error("批次执行失败: 文件={}, 批次={}, 表={}", fileName, batchCount, tableName);
                                return false;
                            }

                            totalProcessed += sqlBatch.size();
                            batchCount++;
                            lastBatchTime = System.currentTimeMillis();

                            log.info("批次执行成功: 文件={}, 批次={}, 表={}, 数量={}, 累计={}",
                                    fileName, batchCount - 1, tableName, sqlBatch.size(), totalProcessed);

                            // 清空批次，准备下一批
                            sqlBatch.clear();
                        }
                    } else {
                        log.warn("跳过无效SQL {}: 长度={} bytes, 内容前100字符={}",
                                sqlCount + 1, completeSql.length(),
                                completeSql.length() > 100 ? completeSql.substring(0, 100) : completeSql);
                    }

                    // 重置当前SQL，准备下一条
                    currentSql.setLength(0);
                } else {
                    // 调试：记录SQL构建过程
                    if (lineCount % 5000 == 0) {
                        log.debug("SQL构建状态: 行数={}, 当前SQL长度={}, 最后100字符={}",
                                lineCount, currentSql.length(),
                                currentSql.length() > 100 ? currentSql.substring(currentSql.length() - 100) : currentSql.toString());
                    }

                    // 检查SQL是否过长（可能有问题）
                    if (currentSql.length() > 10000) {
                        log.warn("⚠️ 当前SQL过长({} bytes), 可能解析有问题: {}",
                                currentSql.length(), currentSql.substring(0, 200));
                    }
                }

                // 检查是否有进度停滞
                if (sqlCount > lastLineCount) {
                    lastLineCount = sqlCount;
                    lastProgressTime = System.currentTimeMillis();
                } else {
                    long currentTime = System.currentTimeMillis();
                    if (currentTime - lastProgressTime > 10000) { // 10秒无进度
                        log.warn("⚠️ 警告：10秒内无新SQL解析，当前批次大小={}, 强制执行", sqlBatch.size());

                        // 强制执行当前批次
                        if (sqlBatch.size() > 0) {
                            log.info("强制执行当前批次: 数量={}", sqlBatch.size());

                            boolean batchSuccess = processSqlBatch(sqlBatch, fileName, batchCount, tableName);
                            if (batchSuccess) {
                                totalProcessed += sqlBatch.size();
                                batchCount++;
                                lastBatchTime = System.currentTimeMillis();

                                log.info("强制批次执行成功: 文件={}, 批次={}, 表={}, 数量={}, 累计={}",
                                        fileName, batchCount - 1, tableName, sqlBatch.size(), totalProcessed);

                                sqlBatch.clear();
                            }
                        }

                        lastProgressTime = currentTime;
                    }
                }
            }

            log.info("文件读取完成: 总行数={}, 总SQL数={}", lineCount, sqlCount);

            // 处理最后一批
            if (!sqlBatch.isEmpty()) {
                log.info("处理最后一批: {}条SQL, 表: {}", sqlBatch.size(), tableName);

                boolean batchSuccess = processSqlBatch(sqlBatch, fileName, batchCount, tableName);
                if (!batchSuccess) {
                    log.error("最后批次执行失败: 文件={}, 批次={}, 表={}", fileName, batchCount, tableName);
                    return false;
                }

                totalProcessed += sqlBatch.size();
                batchCount++;
                log.info("最后批次执行成功: 文件={}, 批次={}, 表={}, 数量={}, 累计={}",
                        fileName, batchCount - 1, tableName, sqlBatch.size(), totalProcessed);
            }

            // 检查文件是否有有效内容
            if (sqlCount == 0) {
                log.warn("SQL文件没有有效内容: {}, tableName: {}", fileName, tableName);
                return false;
            }

            log.info("SQL文件处理完成: {}, 表: {}, 总行数: {}, 总SQL数量: {}, 总批次: {}, 总处理: {}",
                    fileName, tableName, lineCount, sqlCount, batchCount, totalProcessed);
            return totalProcessed > 0;

        } catch (Exception e) {
            log.error("处理SQL文件失败: {}", fileName, e);
            return false;
        }
    }

    /**
     * 判断是否应该执行批次（优化版本）
     */
    private boolean shouldExecuteBatch(List<String> sqlBatch, int maxBatchSize, int minBatchSize,
                                       long lastBatchTime, long maxWaitTime) {
        // 1. 批次已满
        if (sqlBatch.size() >= maxBatchSize) {
            return true;
        }

        // 2. 达到最小批次大小且等待时间过长
        if (sqlBatch.size() >= minBatchSize) {
            long currentTime = System.currentTimeMillis();
            if (currentTime - lastBatchTime > maxWaitTime) {
                return true;
            }
        }

        // 3. 批次大小超过500且等待超过3秒（更积极的执行策略）
        if (sqlBatch.size() >= 500) {
            long currentTime = System.currentTimeMillis();
            if (currentTime - lastBatchTime > 3000) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取批次执行原因（优化版本）
     */
    private String getBatchExecutionReason(List<String> sqlBatch, int maxBatchSize,
                                           long lastBatchTime, long maxWaitTime) {
        if (sqlBatch.size() >= maxBatchSize) {
            return "批次已满";
        }

        long currentTime = System.currentTimeMillis();
        if (currentTime - lastBatchTime > maxWaitTime) {
            return "等待时间过长";
        }

        if (sqlBatch.size() >= 500 && currentTime - lastBatchTime > 3000) {
            return "中等批次等待超时";
        }

        return "未知原因";
    }
    /**
     * 提取表名（修复版本）
     */
    private String extractTableName(String sql, String existingTableName) {
        // 如果已经有表名，直接返回
        if (existingTableName != null) {
            return existingTableName;
        }

        // 提取表名
        String tableName = extractTableName(sql);
        if (tableName != null) {
            log.info("=== 开始处理表: {} ===", tableName);
        } else {
            log.warn("无法提取表名，使用默认标识");
            tableName = "unknown_table";
        }

        return tableName;
    }


    /**
     * 检查是否是完整的SQL语句（针对这种特殊格式修复）
     */
    private boolean isCompleteSqlStatement(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return false;
        }

        String trimmedSql = sql.trim();

        // 1. 检查是否以VALUES结尾（处理跨行SQL）
        if (trimmedSql.endsWith(");")) {
            return true;
        }

        // 2. 检查是否包含完整的INSERT结构
        if (trimmedSql.startsWith("INSERT INTO") &&
                trimmedSql.contains("VALUES") &&
                trimmedSql.contains("(") &&
                trimmedSql.contains(")")) {

            // 检查括号是否匹配
            int openBrackets = countChar(trimmedSql, '(');
            int closeBrackets = countChar(trimmedSql, ')');

            if (openBrackets == closeBrackets) {
                return true;
            }
        }

        return false;
    }
    /**
     * 统计字符出现次数
     */
    private int countChar(String str, char ch) {
        if (str == null || str.isEmpty()) {
            return 0;
        }

        int count = 0;
        for (char c : str.toCharArray()) {
            if (c == ch) {
                count++;
            }
        }
        return count;
    }
    /**
     * 清理SQL语句 - 移除多余的分隔符
     */
    private String cleanSqlStatement(String sql) {
        if (sql == null) {
            return sql;
        }

        String cleaned = sql.trim();

        // 移除末尾的分号+空格+INSERT
        if (cleaned.endsWith("; INSERT")) {
            cleaned = cleaned.substring(0, cleaned.length() - 9);
        }

        // 移除末尾的分号+INSERT
        if (cleaned.endsWith(";INSERT")) {
            cleaned = cleaned.substring(0, cleaned.length() - 8);
        }

        // 确保以分号结尾
        if (!cleaned.endsWith(";")) {
            cleaned = cleaned + ";";
        }

        return cleaned;
    }




    /**
     * 验证SQL是否适合执行（简化版本）
     */
    private boolean isValidSqlForExecution(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return false;
        }

        String trimmedSql = sql.trim();

        // 移除末尾分号
        if (trimmedSql.endsWith(";")) {
            trimmedSql = trimmedSql.substring(0, trimmedSql.length() - 1).trim();
        }

        // 基本格式检查
        String upperSql = trimmedSql.toUpperCase();

        // 检查是否以有效关键字开头
        if (!upperSql.startsWith("INSERT") && !upperSql.startsWith("REPLACE")) {
            return false;
        }

        // 检查是否包含必要关键字
        if (!upperSql.contains("INTO") || !upperSql.contains("VALUES")) {
            return false;
        }

        // 检查长度是否合理
        if (trimmedSql.length() < 20 || trimmedSql.length() > 5000000) {
            return false;
        }

        return true;
    }

    /**
     * 检查是否是注释或空行（简化版本）
     */
    private boolean isCommentOrEmpty(String line) {
        if (line == null || line.trim().isEmpty()) {
            return true;
        }

        String trimmedLine = line.trim();

        // 单行注释
        if (trimmedLine.startsWith("--")) {
            return true;
        }

        // 多行注释开始或结束
        if (trimmedLine.startsWith("/*") || trimmedLine.endsWith("*/")) {
            return true;
        }

        return false;
    }

    /**
     * 提取表名（简化版本）
     */
    private String extractTableName(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return null;
        }

        try {
            String upperSql = sql.toUpperCase();
            int intoIndex = upperSql.indexOf("INTO");
            if (intoIndex == -1) {
                return null;
            }

            // 从 "INTO" 后面开始查找表名
            String afterInto = sql.substring(intoIndex + 4).trim();

            // 表名可能在括号前
            int openParenIndex = afterInto.indexOf('(');
            String tableName;

            if (openParenIndex != -1) {
                // 表名在括号前: INTO table_name (field1, field2)
                tableName = afterInto.substring(0, openParenIndex).trim();
            } else {
                // 没有括号，整个就是表名
                tableName = afterInto.trim();
            }

            // 清理表名（移除引号）
            tableName = cleanTableName(tableName);

            return tableName;

        } catch (Exception e) {
            log.warn("提取表名失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 清理表名（移除引号）
     */
    private String cleanTableName(String tableName) {
        if (tableName == null) {
            return null;
        }

        // 移除首尾的引号
        tableName = tableName.trim();
        if (tableName.startsWith("`") && tableName.endsWith("`")) {
            tableName = tableName.substring(1, tableName.length() - 1);
        }
        if (tableName.startsWith("\"") && tableName.endsWith("\"")) {
            tableName = tableName.substring(1, tableName.length() - 1);
        }
        if (tableName.startsWith("'") && tableName.endsWith("'")) {
            tableName = tableName.substring(1, tableName.length() - 1);
        }

        return tableName;
    }




    /**
     * 处理SQL批次（增加表名参数）
     */
    private boolean processSqlBatch(List<String> sqlBatch, String fileName, int batchCount, String tableName) {
        try {
            log.info("开始处理SQL批次: 文件={}, 批次={}, 表={}, 数量={}",
                    fileName, batchCount, tableName, sqlBatch.size());

            // 使用现有的批量执行逻辑
            int successCount = jdbcBatchExecutor.executeBatchInsert(sqlBatch);

            boolean success = successCount > 0;
            log.info("SQL批次处理完成: 文件={}, 批次={}, 表={}, 成功={}/{}",
                    fileName, batchCount, tableName, successCount, sqlBatch.size());

            return success;

        } catch (Exception e) {
            log.error("处理SQL批次异常: 文件={}, 批次={}, 表={}", fileName, batchCount, tableName, e);
            return false;
        }
    }


    private boolean executeZipInBatchesForJdbcTest(String dingCorpId) {
        // 2. 解压ZIP文件并处理其中的SQL文件
        try {
            String sqlPath = "F:/work/数据迁移/dingDataMigrationForOem/all";
            File ref = new File(sqlPath + "/666666.zip");
            InputStream zipInputStream = new FileInputStream(ref);
            java.util.zip.ZipInputStream zipIn = new java.util.zip.ZipInputStream(zipInputStream);
            java.util.zip.ZipEntry entry;
            int processedFiles = 0;

            while ((entry = zipIn.getNextEntry()) != null) {
                if (!entry.isDirectory() && entry.getName().toLowerCase().endsWith(".sql")) {
                    // 读取SQL文件内容
                    String sqlContent = IOUtils.toString(zipIn, StandardCharsets.UTF_8);

                    // 执行SQL内容
                    boolean sqlSuccess = executeSqlContentInBatchesForJdbc(sqlContent);
                    if (sqlSuccess) {
                        processedFiles++;
                        log.info("ZIP中SQL文件执行成功: dingCorpId={}, fileName={}", dingCorpId, entry.getName());
                    } else {
                        log.error("ZIP中SQL文件执行失败: dingCorpId={}, fileName={}", dingCorpId, entry.getName());
                        return false;
                    }
                }
                zipIn.closeEntry();
            }

            log.info("ZIP文件处理完成: dingCorpId={}, 处理文件数={}", dingCorpId, processedFiles);
            return processedFiles > 0;
        } catch (Exception e) {
            log.error("处理ZIP文件异常: dingCorpId={}", dingCorpId, e);
        }
        return true;
    }

    // ==================== 数据导入相关 ====================
    @Override
    public List<String> readFilesFromOss(String dingCorpId) {
        try {
            log.info("从OSS读取数据: dingCorpId={}", dingCorpId);

            // 1. 查找最新的迁移目录
            String latestMigrationDir = findLatestMigrationDirectory(dingCorpId);
            if (latestMigrationDir == null) {
                log.error("未找到迁移目录: dingCorpId={}", dingCorpId);
                return new ArrayList<>();
            }

            // 2. 优先查找ZIP文件
            String zipFile = latestMigrationDir + "/migration_data_*.zip";
            List<String> zipFiles = ossUtil.listKeys(zipFile);

            if (!zipFiles.isEmpty()) {
                // 有ZIP文件，返回ZIP文件路径
                log.info("找到ZIP文件: dingCorpId={}, zipFile={}", dingCorpId, zipFiles.get(0));
                return zipFiles;
            }

            // 3. 没有ZIP文件，查找SQL文件
            String sqlFilePattern = latestMigrationDir + "/*.sql";
            List<String> sqlFiles = ossUtil.listKeys(sqlFilePattern);

            log.info("从OSS读取数据完成: dingCorpId={}, 找到{}个SQL文件", dingCorpId, sqlFiles.size());
            return sqlFiles;

        } catch (Exception e) {
            log.error("从OSS读取数据失败: dingCorpId={}", dingCorpId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 查找最新的迁移目录
     */
    private String findLatestMigrationDirectory(String dingCorpId) {
        try {
            // 查找所有迁移目录
            String migrationPattern = String.format("%s/%s/*", MIGRATION_PREFIX, dingCorpId);
            List<String> allDirs = ossUtil.listKeys(migrationPattern);

            if (allDirs.isEmpty()) {
                return null;
            }

            // 按时间戳排序，返回最新的
            return allDirs.stream()
                    .filter(dir -> dir.matches(".*\\d{13,}")) // 匹配时间戳格式
                    .max(String::compareTo)
                    .orElse(allDirs.get(allDirs.size() - 1));

        } catch (Exception e) {
            log.error("查找最新迁移目录失败: dingCorpId={}", dingCorpId, e);
            return null;
        }
    }


    @Override
    public boolean importFromOssToDatabase(String dingCorpId, String ossPath) {
        try {
            log.info("开始从OSS导入到数据库: dingCorpId={}, ossPath={}", dingCorpId, ossPath);
            // 2. 处理文件（优先处理ZIP文件）
            boolean importSuccess = processFilesFromOss(dingCorpId, ossPath);
            log.info("importFromOssToDatabase需要导入finished: dingCorpId={}, importSuccess={}", dingCorpId, importSuccess);
            return importSuccess;
        } catch (Exception e) {
            log.error("从OSS导入到数据库失败: dingCorpId={}, ossPath={}", dingCorpId, ossPath, e);
            return false;
        }
    }


    /**
     * 移动OSS目录到已处理状态
     */
    private boolean moveToProcessedDirectory(String dingCorpId, String sourceOssPath) {
        try {
            log.info("移动OSS目录到已处理状态: dingCorpId={}, sourceOssPath={}", dingCorpId, sourceOssPath);

            // 1. 构建目标目录名
            String processedDirName = buildProcessedDirectoryName(dingCorpId, sourceOssPath);

            // 2. 在OSS中移动目录
            boolean moveSuccess = moveOssDirectory(sourceOssPath, processedDirName);
            if (!moveSuccess) {
                log.error("移动OSS目录失败: source={}, target={}", sourceOssPath, processedDirName);
                return false;
            }

            log.info("OSS目录移动成功: {} -> {}", sourceOssPath, processedDirName);
            return true;

        } catch (Exception e) {
            log.error("移动OSS目录到已处理状态失败: dingCorpId={}, sourceOssPath={}", dingCorpId, sourceOssPath, e);
            return false;
        }
    }

    /**
     * 移动OSS目录到失败状态
     */
    private boolean moveToFailedDirectory(String dingCorpId, String sourceOssPath, String errorMessage) {
        try {
            log.info("移动OSS目录到失败状态: dingCorpId={}, sourceOssPath={}, error={}", dingCorpId, sourceOssPath, errorMessage);

            // 1. 构建目标目录名
            String failedDirName = buildFailedDirectoryName(dingCorpId, sourceOssPath);

            // 2. 在OSS中移动目录
            boolean moveSuccess = moveOssDirectory(sourceOssPath, failedDirName);
            if (!moveSuccess) {
                log.error("移动OSS目录失败: source={}, target={}", sourceOssPath, failedDirName);
                return false;
            }

            // 3. 记录失败原因
            boolean logSuccess = logFailureReason(failedDirName, errorMessage);
            if (!logSuccess) {
                log.warn("记录失败原因失败: failedDirName={}", failedDirName);
            }

            log.info("OSS目录移动成功: {} -> {}", sourceOssPath, failedDirName);
            return true;

        } catch (Exception e) {
            log.error("移动OSS目录到失败状态失败: dingCorpId={}, sourceOssPath={}", dingCorpId, sourceOssPath, e);
            return false;
        }
    }

    /**
     * 构建已处理目录名
     */
    private String buildProcessedDirectoryName(String dingCorpId, String sourceOssPath) {
        try {
            // 从源路径中提取时间戳
//            String timestamp = extractTimestampFromPath(sourceOssPath);
//            if (timestamp == null) {
//                timestamp = String.valueOf(System.currentTimeMillis());
//            }
            String timestamp = String.valueOf(System.currentTimeMillis());
            return PROCESSED_PREFIX + dingCorpId + "_" + timestamp;

        } catch (Exception e) {
            log.error("构建已处理目录名失败: dingCorpId={}, sourceOssPath={}", dingCorpId, sourceOssPath, e);
            return PROCESSED_PREFIX + dingCorpId + "_" + System.currentTimeMillis();
        }
    }

    /**
     * 构建失败目录名
     */
    private String buildFailedDirectoryName(String dingCorpId, String sourceOssPath) {
        try {
            // 从源路径中提取时间戳
//            String timestamp = extractTimestampFromPath(sourceOssPath);
//            if (timestamp == null) {
//                timestamp = String.valueOf(System.currentTimeMillis());
//            }

            String timestamp = String.valueOf(System.currentTimeMillis());
            return FAILED_PREFIX + dingCorpId + "_" + timestamp;

        } catch (Exception e) {
            log.error("构建失败目录名失败: dingCorpId={}, sourceOssPath={}", dingCorpId, sourceOssPath, e);
            return FAILED_PREFIX + dingCorpId + "_" + System.currentTimeMillis();
        }
    }

    /**
     * 从路径中提取时间戳
     */
    private String extractTimestampFromPath(String ossPath) {
        try {
            if (ossPath == null || ossPath.trim().isEmpty()) {
                return null;
            }

            // 查找最后一个下划线后的时间戳
            int lastUnderscoreIndex = ossPath.lastIndexOf("_");
            if (lastUnderscoreIndex != -1 && lastUnderscoreIndex < ossPath.length() - 1) {
                String timestampPart = ossPath.substring(lastUnderscoreIndex + 1);
                // 检查是否为数字（时间戳）
                if (timestampPart.matches("\\d+")) {
                    return timestampPart;
                }
            }

            return null;

        } catch (Exception e) {
            log.error("从路径提取时间戳失败: ossPath={}", ossPath, e);
            return null;
        }
    }

    /**
     * 在OSS中移动目录
     */
    private boolean moveOssDirectory(String sourcePath, String targetPath) {
        try {
            // TODO: 使用OSS SDK移动目录
            // 这里模拟移动操作
            log.info("模拟移动OSS目录: {} -> {}", sourcePath, targetPath);

            // 模拟移动过程
            Thread.sleep(2000);

            // 模拟移动成功
            boolean success = Math.random() > 0.05; // 95%成功率

            if (success) {
                log.info("OSS目录移动成功: {} -> {}", sourcePath, targetPath);
            } else {
                log.error("OSS目录移动失败: {} -> {}", sourcePath, targetPath);
            }

            return success;

        } catch (Exception e) {
            log.error("移动OSS目录失败: {} -> {}", sourcePath, targetPath, e);
            return false;
        }
    }

    /**
     * 记录失败原因
     */
    private boolean logFailureReason(String failedDirName, String errorMessage) {
        try {
            // TODO: 在OSS中创建失败原因文件
            String failureLog = String.format("失败时间: %s\n失败原因: %s\n",
                    new java.util.Date(), errorMessage);

            log.info("记录失败原因: failedDirName={}, errorMessage={}", failedDirName, errorMessage);
            return true;

        } catch (Exception e) {
            log.error("记录失败原因失败: failedDirName={}", failedDirName, e);
            return false;
        }
    }

    /**
     * 执行单个SQL文件
     */
    private boolean executeSqlFile(String companyId, String sqlFile) {
        try {
            log.info("开始执行SQL文件: companyId={}, sqlFile={}", companyId, sqlFile);

            // 1. 从OSS下载SQL文件内容
            String sqlContent = "";//downloadSqlFromOss(sqlFile);
            if (sqlContent == null || sqlContent.trim().isEmpty()) {
                log.warn("下载SQL文件失败: companyId={}, sqlFile={}", companyId, sqlFile);
                return false;
            }
            // 2. 直接执行SQL内容
            NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(Company.class).setSql(sqlContent);
            domainDao.nativeExecute(sqlBuilder);
            boolean executeSuccess = true;// executeSqlContent(sqlContent);
            if (!executeSuccess) {
                log.error("执行SQL内容失败: companyId={}, sqlFile={}", companyId, sqlFile);
                return false;
            }

            log.info("SQL文件执行成功: companyId={}, sqlFile={}", companyId, sqlFile);
            return true;

        } catch (Exception e) {
            log.error("执行SQL文件异常: companyId={}, sqlFile={}", companyId, sqlFile, e);
            return false;
        }
    }

    /**
     * 从OSS读取文件内容并执行SQL
     */
    public boolean processSqlFileFromOss(String dingCorpId, String sqlFile) {
        InputStream inputStream = null;
        try {
            // 1. 构建完整的OSS对象键
            log.info("准备从OSS读取SQL文件: dingCorpId={}, sqlFile={}", dingCorpId, sqlFile);
            // 2. 从OSS获取文件
            inputStream = ossUtil.getFile(sqlFile);
            if (inputStream == null) {
                log.error("从OSS获取文件流失败:dingCorpId:{}, sqlFile={}", dingCorpId, sqlFile);
                return false;
            }
            // 3. 读取文件内容
            String sqlContent = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.debug("成功读取SQL文件内容，大小: {} 字节", sqlContent.length());

            // 4. 按每页2000条分批执行SQL内容
            boolean executeSuccess = executeSqlContentInBatches(sqlContent);
            if (!executeSuccess) {
                log.error("执行SQL内容失败: dingCorpId={}, sqlFile={}", dingCorpId, sqlFile);
                return false;
            }
            log.info("SQL文件执行成功: dingCorpId={}, sqlFile={}", dingCorpId, sqlFile);
            return true;

        } catch (Exception e) {
            log.error("处理OSS SQL文件失败: dingCorpId={}, sqlFile={}", dingCorpId, sqlFile, e);
            return false;
        } finally {
            // 5. 清理资源
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.warn("关闭输入流失败", e);
                }
            }
        }
    }


    @Override
    public boolean importFromOssToDatabaseForZip(String dingCorpId, MultipartFile file) {
        boolean executeSuccess = executeZipInBatchesForJdbc(dingCorpId, file);
        log.info("SQL文件执行成功: dingCorpId={},executeSuccess:{}", dingCorpId, executeSuccess);
        return executeSuccess;
    }

    @Override
    public boolean importFromOssToDatabaseTest(String dingCorpId) {
        boolean executeSuccess = executeZipInBatchesForJdbcTest(dingCorpId);
        log.info("SQL文件执行成功: dingCorpId={},executeSuccess:{}", dingCorpId, executeSuccess);
        return true;
    }

    public boolean importFromOssToDatabaseTest2(String dingCorpId) {
        String sqlPath = "F:/work/数据迁移/dingDataMigrationForOem/";
        List<String> refTables = new ArrayList<>();
        refTables.add("emp_eval_type_used_field");
        refTables.add("perf_eval_task_interview_operation");
        refTables.add("admin_scope_priv_group_of_emp");
        for (String refTable : refTables) {
            //找对对应文件,替换
            File ref = new File(sqlPath + "/" + refTable + ".sql");
            if (!ref.exists()) {
                continue;
            }
            String sqls = FileUtil.readString(ref, Charset.defaultCharset());
            boolean executeSuccess = executeSqlContentInBatchesForJdbc(sqls);
            log.info("SQL文件执行成功: dingCorpId={},refTable:{},executeSuccess:{}", dingCorpId, refTable, executeSuccess);
        }
        //  File ref = new File("F:/work/数据迁移/dingDataMigrationForOem/perf_eval_task_interview_operation.sql");
        // String sqlContent = FileUtil.readString(ref, Charset.defaultCharset());
        return true;
    }

    /**
     * 分批执行SQL内容
     *
     * @param sqlContent 完整的SQL内容
     * @return 执行是否成功
     */
    private boolean executeSqlContentInBatchesForJdbc(String sqlContent) {
        try {
            // 1. 按分号分割SQL语句（过滤掉注释和空行）

            List<String> sqlStatements = parseSqlStatements(sqlContent);
            if (sqlStatements.isEmpty()) {
                log.warn("没有解析到有效的SQL语句");
                return true;
            }

            log.info("解析到SQL语句总数: {}", sqlStatements.size());
            // 2. 分批执行
            jdbcBatchExecutor.executeBatchInsert(sqlStatements);
            log.info("所有SQL批次执行完成，总语句数: {}", sqlStatements.size());
            return true;
        } catch (Exception e) {
            log.error("分批执行SQL内容异常", e);
            return false;
        }
    }

    /**
     * 分批执行SQL内容
     *
     * @param sqlContent 完整的SQL内容
     * @return 执行是否成功
     */
    private boolean executeSqlContentInBatches(String sqlContent) {
        try {
            int batchSize = 1000;
            // 1. 按分号分割SQL语句（过滤掉注释和空行）
            List<String> sqlStatements = parseSqlStatements(sqlContent);
            if (sqlStatements.isEmpty()) {
                log.warn("没有解析到有效的SQL语句");
                return true;
            }

            log.info("解析到SQL语句总数: {}", sqlStatements.size());
            // 2. 分批执行
            int totalBatches = (int) Math.ceil((double) sqlStatements.size() / batchSize);
            log.info("需要执行批次数: {}, 每批大小: {}", totalBatches, batchSize);

            for (int i = 0; i < totalBatches; i++) {
                int start = i * batchSize;
                int end = Math.min((i + 1) * batchSize, sqlStatements.size());
                List<String> batchStatements = sqlStatements.subList(start, end);
                // 构建批次SQL
                String batchSql = String.join("\n", batchStatements);

                try {
                    // 执行批次SQL
                    NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(Company.class).setSql(batchSql);
                    domainDao.nativeExecute(sqlBuilder);
                    log.info("执行SQL批次成功: 批次={}/{}, 语句数={}", i + 1, totalBatches, batchStatements.size());

                    // 添加短暂延迟避免数据库压力过大
                    if (i < totalBatches - 1) {
                        Thread.sleep(100);
                    }
                } catch (Exception e) {
                    log.error("执行SQL批次失败: 批次={}/{}, 语句数={}", i + 1, totalBatches, batchStatements.size(), e);
                    return false;
                }
            }
            log.info("所有SQL批次执行完成，总语句数: {}", sqlStatements.size());
            return true;
        } catch (Exception e) {
            log.error("分批执行SQL内容异常", e);
            return false;
        }
    }

    /**
     * 解析SQL内容，提取有效的INSERT语句
     *
     * @param sqlContent 原始SQL内容
     * @return 有效的SQL语句列表
     */
    /**
     * 解析SQL内容，提取有效的INSERT语句
     */
    /**
     * 解析SQL内容，提取有效的INSERT语句
     */
    private List<String> parseSqlStatements(String sqlContent) {
        List<String> statements = new ArrayList<>();

        if (sqlContent == null || sqlContent.trim().isEmpty()) {
            return statements;
        }

        log.info("开始解析SQL内容，长度: {}", sqlContent.length());

        // 智能分割SQL语句
        List<String> parts = splitSqlBySemicolon(sqlContent);
        log.info("分割得到 {} 个部分", parts.size());

        for (int i = 0; i < parts.size(); i++) {
            String part = parts.get(i);
            String trimmedPart = part.trim();

            // 过滤掉空行和注释行
            if (!trimmedPart.isEmpty() &&
                    !trimmedPart.startsWith("--") &&
                    !trimmedPart.startsWith("/*") &&
                    trimmedPart.toLowerCase().startsWith("insert")) {
                // 清理SQL语句，移除开头和结尾的分号
                String cleanSql = cleanSqlStatement(trimmedPart);
                // 验证SQL完整性
                if (isValidInsertSql(cleanSql)) {
                    statements.add(cleanSql);
                    log.debug("添加有效SQL语句 {}: {}", i + 1, trimmedPart.substring(0, Math.min(100, trimmedPart.length())) + "...");
                } else {
                    log.warn("跳过无效的INSERT语句 {}: {}", i + 1, trimmedPart.substring(0, Math.min(200, trimmedPart.length())) + "...");
                }
            }
        }

        log.info("解析完成，有效INSERT语句数量: {}", statements.size());
        return statements;
    }

    /**
     * 智能分割SQL语句，避免在字符串值中的分号分割
     */
    private List<String> splitSqlBySemicolon(String sqlContent) {
        List<String> parts = new ArrayList<>();
        if (sqlContent == null || sqlContent.trim().isEmpty()) {
            return parts;
        }

        StringBuilder currentPart = new StringBuilder();
        boolean inSingleQuote = false;
        boolean inDoubleQuote = false;
        boolean inComment = false;
        boolean inLineComment = false;

        for (int i = 0; i < sqlContent.length(); i++) {
            char c = sqlContent.charAt(i);
            char nextChar = (i + 1 < sqlContent.length()) ? sqlContent.charAt(i + 1) : '\0';

            // 处理行注释
            if (c == '-' && nextChar == '-' && !inSingleQuote && !inDoubleQuote && !inComment) {
                inLineComment = true;
                currentPart.append(c);
                continue;
            }

            // 处理块注释开始
            if (c == '/' && nextChar == '*' && !inSingleQuote && !inDoubleQuote && !inLineComment) {
                inComment = true;
                currentPart.append(c);
                continue;
            }

            // 处理块注释结束
            if (c == '*' && nextChar == '/' && inComment) {
                inComment = false;
                currentPart.append(c);
                currentPart.append(nextChar);
                i++; // 跳过下一个字符
                continue;
            }

            // 处理单引号
            if (c == '\'' && !inDoubleQuote && !inComment && !inLineComment) {
                inSingleQuote = !inSingleQuote;
            }

            // 处理双引号
            if (c == '"' && !inSingleQuote && !inComment && !inLineComment) {
                inDoubleQuote = !inDoubleQuote;
            }

            // 处理分号分割
            if (c == ';' && !inSingleQuote && !inDoubleQuote && !inComment && !inLineComment) {
                String part = currentPart.toString().trim();
                if (!part.isEmpty()) {
                    parts.add(part);
                }
                currentPart = new StringBuilder();
            } else {
                currentPart.append(c);
            }
        }

        // 添加最后一部分
        String lastPart = currentPart.toString().trim();
        if (!lastPart.isEmpty()) {
            parts.add(lastPart);
        }

        return parts;
    }

    /**
     * 验证SQL是否为有效的INSERT语句
     */
    private boolean isValidInsertSql(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return false;
        }

        String upperSql = sql.trim().toUpperCase();

        // 必须以INSERT开头
        if (!upperSql.startsWith("INSERT")) {
            return false;
        }

        // 必须包含INTO关键字
        if (!upperSql.contains("INTO")) {
            return false;
        }

        // 必须包含VALUES关键字
        if (!upperSql.contains("VALUES")) {
            return false;
        }

        // 检查括号是否匹配
        int openParens = 0;
        int closeParens = 0;
        boolean inQuotes = false;
        char quoteChar = 0;

        for (char c : sql.toCharArray()) {
            if (c == '\'' || c == '"') {
                if (!inQuotes) {
                    inQuotes = true;
                    quoteChar = c;
                } else if (c == quoteChar) {
                    inQuotes = false;
                }
            } else if (!inQuotes) {
                if (c == '(') openParens++;
                else if (c == ')') closeParens++;
            }
        }

        return openParens == closeParens;
    }


    /**
     * 调用钉钉关闭应用接口
     */
    @Override
    public boolean callDingTalkCloseApp(String companyId) {
        try {
            log.info("调用钉钉关闭应用接口: companyId={}", companyId);

            // TODO: 实现钉钉关闭应用接口调用
            // 参考钉钉开放平台文档

            // 模拟API调用
            Thread.sleep(300);
            boolean success = Math.random() > 0.1; // 90%成功率

            if (success) {
                log.info("钉钉关闭应用接口调用成功: companyId={}", companyId);
            } else {
                log.warn("钉钉关闭应用接口调用失败: companyId={}", companyId);
            }

            return success;

        } catch (Exception e) {
            log.error("调用钉钉关闭应用接口失败: companyId={}", companyId, e);
            return false;
        }
    }
    // 测试用例
    public void testSqlCompleteness() {
        String[] testSqls = {
                // 完整的SQL
                "INSERT INTO `emp_eval_kpi_type`(`task_user_id`,`company_id`) VALUES ('123','456');",

                // 不完整的SQL（您提到的问题）
                "INSERT INTO `emp_eval_kpi_type`(`task_user_id`,`company_id`,`kpi_type_id`,`kpi_type_name`,`kpi_type_weight`,`open_okr_score`,`is_okr`,`type_order`,`reserve_okr_weight`,`kpi_type_classify`,`max_extra_score`,`import_okr_flag`,`locked_items`,`item_limit_cnt`,`is_deleted`,`created_user`,`created_time`,`updated_user`,`updated_time`,`version`,`plus_sub_interval`,`ind_level_group_id`,`ind_level_group`,`ind_level`,`self_rater`,`super_rater`,`appoint_rater`,`peer_rater`,`sub_rater`,`type_level`,`finish_value_audit`,`score_opt_type`,`des`,`ask360_eval_id`,`ask360_eval_score`,`ask360_temp_id`,`ask360_temp_name`,`ask360_temp_desc`,`scoring_type`) VALUES ('2392238','acd50632-e6f0-4ee3-9f41-1f402fb1ac74','39fa1221-b19b-4811-bd8b-479575e3d37d','价值观考核',0.00,0,'false',0,25.00,'custom',0.00,'false','[\"addIndex\",\"modifyIndex\",\"deleteIndex\"]','{\"max\":\"\",\"min\":\"\",\"openItemLimit\":\"false\"}','false',null,'2023-12-18 15:49:54',null,'2023-12-18 15:49:54',0,null,null,null,null,null,null,null,null,null,null,'[{\"objItems\":[],\"objType\":\"role\"},{\"objItems\":[],\"objType\":\"user\"},{\"objItems\":[],\"objType\":\"manager\"}]',2,'1.本价值观评价标准属于塔斯汀的行为规范,与道德标准无关,不涉及人格评价;",

                // 其他测试用例
                "INSERT INTO test VALUES ('incomplete string;",
                "INSERT INTO test VALUES (1, 2, 3;",
                "INSERT INTO test VALUES (1, 2, 3);",
                "INSERT INTO test (col1, col2) VALUES (1, 2);"
        };

        for (String sql : testSqls) {
            boolean isComplete = isCompleteSqlStatement(sql);
            System.out.println("SQL: " + sql.substring(0, Math.min(50, sql.length())) + "...");
            System.out.println("Is Complete: " + isComplete);
            System.out.println("---");
        }
    }
}

