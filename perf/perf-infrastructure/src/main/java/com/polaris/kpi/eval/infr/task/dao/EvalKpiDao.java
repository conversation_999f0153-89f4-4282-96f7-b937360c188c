package com.polaris.kpi.eval.infr.task.dao;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.company.MsgSceneEnum;
import cn.com.polaris.kpi.company.ScorerScoreStatusEnum;
import cn.com.polaris.kpi.eval.KpiItemUsedField;
import cn.com.polaris.kpi.eval.KpiTypeUsedField;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.perf.www.model.kpi.CompanyItemRelTagModel;
import com.perf.www.model.kpi.CompanyTagModel;
import com.perf.www.model.task.PerfEvaluateTaskRefOkrModel;
import com.perf.www.vo.report.year.KpiTypePo;
import com.perf.www.vo.task.query.EvaluateTaskKpiQueryVO;
import com.polaris.kpi.cache.domain.entity.CompanyCacheInfo;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.*;
import com.polaris.kpi.eval.domain.task.entity.log.ItemDynamicLog;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrGoal;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrKeyResult;
import com.polaris.kpi.eval.domain.task.type.InnerFields;
import com.polaris.kpi.eval.domain.task.type.NodeWeight;
import com.polaris.kpi.eval.domain.temp.entity.PerfTemplEvaluate;
import com.polaris.kpi.eval.infr.task.builder.Audit2TypeRuleBd;
import com.polaris.kpi.eval.infr.task.builder.Audit3TypeRuleBd;
import com.polaris.kpi.eval.infr.task.builder.EmpDetailBuilder;
import com.polaris.kpi.eval.infr.task.ppojo.*;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.*;
import com.polaris.kpi.eval.infr.task.ppojo.okr.OkrGoalDo;
import com.polaris.kpi.eval.infr.task.ppojo.open.eval.OpenKpiAndOkrItemPo;
import com.polaris.kpi.eval.infr.task.ppojo.open.eval.OpenKpiTypePo;
import com.polaris.kpi.eval.infr.task.query.FinishValueQuery;
import com.polaris.kpi.eval.infr.task.query.KpiItemQuery;
import com.polaris.kpi.eval.infr.task.query.TaskKpiItemQuery;
import com.polaris.kpi.org.domain.dept.entity.CompanyConf;
import com.polaris.kpi.org.infr.company.ppojo.CompanyMsgCenterDo;
import com.polaris.kpi.org.infr.emp.pojo.EmployeeBaseInfoDo;
import com.polaris.sdk.type.AuditEnum;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.NativeSQLBuilder;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.lufei.ibatis.mapper.PagedList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class EvalKpiDao {

    @Autowired
    private DomainDaoImpl domainDao;
    @Autowired
    private TaskKpiItemDao taskKpiItemDao;

    public void setDomainDao(DomainDaoImpl domainDao) {
        this.domainDao = domainDao;
    }
    public EvalKpiPo getItemDetail(KpiItemQuery query) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskKpiDo.class, "k")
                .join(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("k.company_id=b.company_id and k.task_id = b.id")
                .clearSelect().select("k.* ,b.evaluate_type").setRsType(EvalKpiPo.class)
                .whereEq("k.task_id", query.getTaskId())
                .whereEq("k.task_user_id", query.getTaskUserId())
                .whereEq("k.emp_id", query.getEmpId())
                .whereEqReq("k.company_id", query.getCompanyId())
                .whereEq("k.kpi_item_id", query.getKpiItemId())
                .whereEq("k.is_deleted", Boolean.FALSE.toString())
                .whereEq("b.is_deleted", Boolean.FALSE.toString());
        EvalKpiPo kpiItem = domainDao.findOne(comQB);
        if (kpiItem == null) {
            return kpiItem;
        }
        //查询暂存数据
        ComQB cacheQB = ComQB.build(InputFinishValCacheDo.class)
                .whereEqReq("company_id", query.getCompanyId())
                .whereEqReq("task_user_id", query.getTaskUserId())
                .whereEqReq("operate_emp_id", query.getEmpId())
                .whereEq("kpi_item_id", kpiItem.getKpiItemId())
                .whereEq("is_deleted", Boolean.FALSE.toString());
        InputFinishValCacheDo cacheDo = domainDao.findOne(cacheQB);
        kpiItem.setCacheDo(cacheDo);

        /**查询指标字定义字段配置*/
        ComQB itemUsed = ComQB.build(PerfEvaluateItemUsedFieldDo.class, "tu")
                .whereEq("task_user_id", query.getTaskUserId())
                .whereEq("kpi_item_id", query.getKpiItemId())
                .appendWhere("is_deleted='false'")
                .orderByAsc("sort");
        List<KpiItemUsedField> itemUsedFieldDos = domainDao.listAllDomain(itemUsed, KpiItemUsedField.class);
        kpiItem.initKpiItemUsedFields(itemUsedFieldDos);
        kpiItem.setItemScorerName(getEmpName(kpiItem.getScorerObjId()));
        kpiItem.setTaskEmpName(getEmpName(kpiItem.getEmpId()));
        kpiItem.setInputEmpName(getEmpName(kpiItem.getResultInputEmpId()));
        kpiItem.clearThresholdJsonifExam();

        ComQB itemRuleQB = ComQB.build(PerfEvaluateTaskItemScoreRuleDo.class)
                .whereEq("kpi_item_id", kpiItem.getKpiItemId())
                .whereEqReq("company_id", query.getCompanyId())
                .whereEq("task_user_id", kpiItem.getTaskUserId())
                .whereEq("is_deleted", Boolean.FALSE.toString());
        PerfEvaluateTaskItemScoreRuleDo itemScoreRuleDo = domainDao.findOne(itemRuleQB);
        if (Objects.nonNull(itemScoreRuleDo)) {
            kpiItem.setAppointScoreFlag(itemScoreRuleDo.openAppointScore());
            kpiItem.setSuperiorScoreFlag(itemScoreRuleDo.openSuperiorScore());
            kpiItem.setSelfScoreFlag(itemScoreRuleDo.openSelfScore());
            kpiItem.setMutualScoreFlag(itemScoreRuleDo.openMutualScore());
            kpiItem.setEvaluateType("custom");
            return kpiItem;
        }
        ComQB typeQ = ComQB.build(EmpEvalKpiTypeDo.class)
                .whereEq("kpi_type_id", kpiItem.getKpiTypeId())
                .whereEqReq("company_id", query.getCompanyId())
                .whereEq("task_user_id", kpiItem.getTaskUserId())
                .whereEq("is_deleted", Boolean.FALSE.toString());
        EmpEvalKpiType type = domainDao.findDomain(typeQ, EmpEvalKpiType.class);
        if (type.isOpenRaterRule()) {
            kpiItem.setAppointScoreFlag(type.isOpenAppointRater() + "");
            kpiItem.setSuperiorScoreFlag(type.isOpenSuperiorRater() + "");
            kpiItem.setSelfScoreFlag(type.isOpenSelfRater() + "");
            kpiItem.setMutualScoreFlag((type.isOpenPeerRater() || type.isOpenSubRater()) + "");
            kpiItem.setEvaluateType("custom");
            return kpiItem;
        }

        ComQB deRuleQ = ComQB.build(EmpEvalRuleDo.class)
                .whereEqReq("company_id", query.getCompanyId())
                .whereEqReq("emp_eval_id", kpiItem.getTaskUserId())
                .appendWhere("is_deleted ='false'");
        EmpEvalRule rule = this.domainDao.findDomain(deRuleQ, EmpEvalRule.class);

        if (Objects.nonNull(rule)) {
            kpiItem.setAppointScoreFlag(rule.openAppointScore());
            kpiItem.setSuperiorScoreFlag(rule.openSuperiorScore());
            kpiItem.setSelfScoreFlag(rule.openSelfScore());
            kpiItem.setMutualScoreFlag(rule.openMutualScoreFlag());
            kpiItem.setEvaluateType(rule.getEvaluateType());
            return kpiItem;
        }
        return kpiItem;
    }

    public String getEmpName(String empId) {
        if (StrUtil.isEmpty(empId)) {
            return "";
        }
        ComQB comQB = ComQB.build(EmployeeBaseInfoDo.class)
                .clearSelect().select("name").setRsType(String.class)
                .whereIn("employee_id", Arrays.asList(empId.split(",")));
        List<String> empNames = domainDao.listAll(comQB);
        if (CollUtil.isEmpty(empNames)) {
            return "";
        }
        return String.join(",", empNames);
    }


    public List<ItemScorePo> getItemScoreDetailV3(EvalKpi kpi, KpiItemQuery query, List<NodeWeight> nodeWeight, boolean isOpenAvgWeightCompute) {
        ComQB comQB = ComQB.build(EmpScorerScoreKpiItemDo.class, "sc")
                .join(EmpEvalScorerNodeDo.class,"sn").appendOn("sc.company_id=sn.company_id and sc.scorer_score_node_id = sn.id and sn.is_deleted = 'false'")
                .clearSelect().setRsType(ItemScorePo.ItemScoreDetail.class)
                .select("distinct sc.scorer_type as scorerType,sc.score_weight as rater_weight,sc.score_level,if(sc.veto_flag is null,sc.score,null) as rater_score")
                .select("sc.score_comment,sc.scorer_name as name")
                .select("sc.score_att_url,sc.veto_flag ,ifnull(sc.updated_time,sc.created_time) as created_time ")
              //  .select("sc.signature_url")
                .whereEqReq("sc.company_id", query.getCompanyId())
                .whereEqReq("sc.task_user_id", query.getTaskUserId())
                .whereEqReq("sc.kpi_item_id", query.getKpiItemId())
               // .appendWhere("(CASE sn.transfer_type  WHEN 'transfer' THEN  (sn.transfer_from is not null and sn.transfer_to is null)  WHEN 'skip' THEN  (sn.transfer_from is null and sn.transfer_to is null)  ELSE 1=1 END)")
                .appendWhere("((transfer_type is null) OR (transfer_type = 'transfer' AND transfer_from IS NOT NULL AND transfer_to IS NULL) OR (transfer_type = 'skip' AND transfer_from IS NULL AND transfer_to IS NOT NULL))") //不显示 跳过的人
                .whereEq("sc.status", ScorerScoreStatusEnum.FINISHED_SUBMIT.getStatus())
                .whereEqReq("sc.is_deleted", Boolean.FALSE.toString());
        List<ItemScorePo.ItemScoreDetail> details = domainDao.listAll(comQB);
        if (CollUtil.isEmpty(details)) {
            return new ArrayList<>();
        }
        //获取环节权重
        List<ItemScorePo> pos = new ArrayList<>();
        Map<String, List<NodeWeight>> sceneMap = nodeWeight.stream().collect(Collectors.groupingBy(NodeWeight::getScorerScene));
        Map<String, List<ItemScorePo.ItemScoreDetail>> typeMap = new HashMap<>();
        List<ItemScorePo.ItemScoreDetail> temps;
        for(ItemScorePo.ItemScoreDetail detail:details){
            if (typeMap.containsKey(detail.getScorerType())){
                temps = typeMap.get(detail.getScorerType());
            }else{
                temps= new ArrayList<>();
            }
            temps.add(detail);
            typeMap.put(detail.getScorerType(),temps);
        }
        for (String scoreScene : typeMap.keySet()) {
            ItemScorePo po = new ItemScorePo(typeMap.get(scoreScene), sceneMap.get(scoreScene), isOpenAvgWeightCompute, kpi);
            pos.add(po);
        }
        return pos;
    }

    public List<ItemScorePo> getItemScoreDetail(KpiItemQuery query, List<NodeWeight> nodeWeight,boolean isOpenAvgWeightCompute) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "r")
                .leftJoin(EmployeeBaseInfoDo.class, "e").appendOn("r.scorer_id=e.employee_id and e.company_id = r.company_id")
                .leftJoin(PerfEvaluateTaskKpiDo.class, "k").appendOn("r.kpi_item_id=k.kpi_item_id  and r.task_user_id=k.task_user_id and r.company_id = k.company_id and k.is_deleted='false'")
                .leftJoin(EvalScoreSummaryDo.class, "s")
                .appendOn("r.company_id =s.company_id  and r.task_user_id = s.task_user_id and  r.scorer_id = s.created_user and r.scorer_type = s.score_type and s.is_deleted='false'")
                .clearSelect().setRsType(ItemScorePo.ItemScoreDetail.class)
                .select("distinct r.scorer_type, r.score_weight as rater_weight, r.score_level,  if(r.veto_flag is null,r.score,null) as rater_score, s.summary ,r.score_comment,r.score_att_url,r.veto_flag  ,e.name,r.signature_url,ifnull(r.updated_time,r.created_time) as created_time")
                .select("k.ind_level_group")
                .whereEqReq("r.kpi_item_id", query.getKpiItemId())
                .whereEqReq("r.task_user_id", query.getTaskUserId())
                .whereEqReq("r.company_id", query.getCompanyId())
                .whereEq("r.audit_status", "pass")
                .whereIn("r.scorer_type", AuditEnum.scoreScenes())
                .whereEq("r.is_deleted", Boolean.FALSE.toString());
        List<ItemScorePo.ItemScoreDetail> details = domainDao.listAll(comQB);
        if (CollUtil.isEmpty(details)) {
            return new ArrayList<>();
        }
        //获取环节权重
        List<ItemScorePo> pos = new ArrayList<>();
        Map<String, List<NodeWeight>> sceneMap = nodeWeight.stream().collect(Collectors.groupingBy(node -> node.getScorerScene()));
        Map<String, List<ItemScorePo.ItemScoreDetail>> typeMap = details.stream().collect(Collectors.groupingBy(d -> d.getScorerType()));
        for (String scoreScene : typeMap.keySet()) {
            ItemScorePo po = new ItemScorePo(typeMap.get(scoreScene), sceneMap.get(scoreScene), isOpenAvgWeightCompute);
            pos.add(po);
        }
        return pos;
    }

    public List<EmpChangeItemStagePo> listCacheKpi(TenantId tenantId, String taskUserId, List<String> scenes) {
        ComQB comQB = ComQB.build(CompanyCacheInfoDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereInReq("business_scene", scenes)
                .whereEqReq("link_id", taskUserId)
                .whereEqReq("is_deleted", Boolean.FALSE.toString())
                .orderByDesc("created_time")
                .limit(0, 1);
        CompanyCacheInfo cacheInfo = domainDao.findDomain(comQB, CompanyCacheInfo.class);
        if (cacheInfo == null || StrUtil.isBlank(cacheInfo.getValue())) {
            return Collections.emptyList();
        }
        List<EmpChangeItemStagePo> cacheKpiTypes = JSONObject.parseArray(cacheInfo.getValue(), EmpChangeItemStagePo.class);
        return cacheKpiTypes.stream().filter(type -> !Objects.equals(type.getIsDeleted(), Boolean.TRUE.toString())).collect(Collectors.toList());
    }

    public List<BatchFinishValuePo> queryFinishValueList(FinishValueQuery query, CompanyConf companyConf) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskKpiDo.class, "k")
                .leftJoin(PerfEvaluateTaskBaseDo.class, "b").appendOn(" k.task_id = b.id and k.company_id = b.company_id")
                .leftJoin(EmployeeBaseInfoDo.class, "e").appendOn(" k.emp_id = e.employee_id and k.company_id = e.company_id ")
                .leftJoin(PerfEvaluateTaskUserDo.class, "u").appendOn(" u.id = k.task_user_id and u.company_id = k.company_id")
                .clearSelect()
                .select(" e.ding_user_id,e.name as empName,b.task_name,k.kpi_item_name,k.item_rule,k.scoring_rule,k.item_target_value,k.item_finish_value,k.item_unit, " +
                        " (select GROUP_CONCAT(o.org_name) FROM emp_organization o WHERE o.company_id ='" + query.getCompanyId() + "' and o.org_id in " +
                        "(select org_id from emp_ref_org where emp_id = e.employee_id and ref_type = 'org')) as orgName ")
                .setRsType(BatchFinishValuePo.class)
                .whereEqReq("k.company_id", query.getCompanyId())
                .whereEq("k.task_id", query.getTaskId())
                .whereEq("k.emp_id", query.getEmpId())
                .whereEq("k.kpi_item_id", query.getKpiItemId())
                .appendWhere(" k.final_submit_finish_value = 0 ")
                .appendWhere(" k.result_input_type in ('exam','user') ")
                .appendWhere(" FIND_IN_SET('" + query.getOpEmpId() + "', IF(k.result_input_type = 'exam',k.emp_id,k.result_input_emp_id)) ")
                .appendWhere(" k.is_deleted = 'false' and b.is_deleted = 'false' and b.task_status = 'published'  ")
                .appendWhere(" u.task_status in ('confirmed', 'scoring','finishValueAudit')  ");
        //这里后续要梳理需求优化掉，严重影响查询性能
        if (!companyConf.canResSubmitInputFinish()) {
            comQB.appendWhere(" NOT EXISTS (SELECT id FROM perf_evaluate_task_score_result WHERE task_id = k.task_id AND task_user_id = k.task_user_id AND company_id = k.company_id AND emp_id = k.emp_id " +
                    " and company_id='" + query.getCompanyId() + "' AND audit_status = 'pass' AND scorer_type NOT IN ('modify_item_audit','change_item_audit')) ");
        }

        if (companyConf.openInputOnScoring()) {
            //开启再评分中可以录入完成值。如果待办是自动计算指标录入，则需过滤指标的类型为auto
            comQB.appendWhere(" IF(m.business_scene = 'task_submit_auto_item_progress',k.scorer_type = 'auto' ,1=1) ");
        } else {
            //未开启再评分中可以录入完成值。批量查询完成值需要过滤自动计算指标
            comQB.appendWhere(" IF(k.scorer_type = 'auto' and u.task_status = 'scoring',k.scorer_type !='auto',1=1) ");
        }
        if ("emp".equals(query.getOrderBy())) {
            comQB.orderBy(" k.emp_id ");
        } else {
            comQB.orderBy(" k.kpi_item_name ");
        }
        return domainDao.listAll(comQB);
    }

    public PagedList<EvalKpi> pageTaskKpiItem(TaskKpiItemQuery query) {
        final ComQB comQB = ComQB.build(PerfEvaluateTaskKpiDo.class)
                .whereEqReq("company_id", query.getCompanyId())
                .whereEqReq("task_user_id", query.getTaskUserId())
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .orderByAsc("type_order,`order`")
                .setPage(query.getPageNo(), query.getPageSize());
        PagedList<EvalKpi> pagedList = domainDao.listPageDomain(comQB, EvalKpi.class);
        if (CollUtil.isNotEmpty(pagedList.getData())) {
            pagedList.getData().forEach(s -> s.initFinishValueSource());
            List<KpiItemUsedField> itemUsedFields = getItemUsedFieldDoList(new TenantId(query.getCompanyId()), query.getTaskUserId());
            pagedList.getData().forEach(k -> {
                if (k.ifSelfInput()) {
                    k.setResultInputEmpId("");
                }
                k.builderKpiItemUsedFields(itemUsedFields);
            });
        }
        return pagedList;
    }

    public EvalKpi getEvalKpi(String companyId, String taskUserId, String kpiItemId) {
        final ComQB comQB = ComQB.build(PerfEvaluateTaskKpiDo.class,"a")
                .whereEqReq("a.company_id", companyId)
                .whereEqReq("a.task_user_id", taskUserId)
                .whereEqReq("a.kpi_item_id", kpiItemId)
                .whereEq("a.is_deleted", Boolean.FALSE.toString());
        EvalKpi domain = domainDao.findDomain(comQB, EvalKpi.class);

        final ComQB comQB1 = ComQB.build(EmpEvalKpiTypeDo.class,"b")
                .clearSelect().select("b.score_opt_type")
                .setRsType(Integer.class)
                .whereEqReq("b.company_id", companyId)
                .whereEqReq("b.task_user_id", taskUserId)
                .whereEqReq("b.kpi_type_id", domain.getKpiTypeId())
                .whereEq("b.is_deleted", Boolean.FALSE.toString());
        Integer scoreOptType = domainDao.findOne(comQB1);
        domain.setScoreOptType(scoreOptType);
        return domain;
    }


    public ListWrap<EmpEvalKpiType> listEmpEvalKpiTypeV3(TenantId tenantId, String empEvalId) {
        ComQB typeQB = ComQB.build(EmpEvalKpiTypeDo.class, "type")
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", empEvalId)
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        List<EmpEvalKpiType> kpiTypes = domainDao.listAllDomain(typeQB, EmpEvalKpiType.class);
        if (kpiTypes.isEmpty()) {//已发起旧的新人考核任务未增加type记录
            return new ListWrap<EmpEvalKpiType>(kpiTypes).asMap(type -> type.getKpiTypeId());
        }
        ListWrap<KpiTypeUsedField> fieldWrap = listWrapTypeUsedField(tenantId, empEvalId);
        Audit3TypeRuleBd typeRuleBd = new Audit3TypeRuleBd(kpiTypes);
        ListWrap<EvalItemScoreRule> typeRules = typeRuleBd.build2();
        ListWrap<EvalItemScoreRule> preTypeRues = !typeRules.isEmpty() ? typeRules : listPreTypeRule(tenantId, empEvalId);
        //查询维度字段字定义配置
        for (EmpEvalKpiType type : kpiTypes) {
            EvalItemScoreRule rule = preTypeRues.mapGet(type.getKpiTypeId());
            type.setTypeRule(rule);
            type.setKpiTypeUsedFields(fieldWrap.groupGet(type.getKpiTypeId()));
        }
        return new ListWrap<EmpEvalKpiType>(kpiTypes).asMap(type -> type.getKpiTypeId());
    }
    public ListWrap<EmpEvalKpiType> listEmpEvalKpiType(TenantId tenantId, String empEvalId) {
        ComQB typeQB = ComQB.build(EmpEvalKpiTypeDo.class, "type")
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", empEvalId)
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        List<EmpEvalKpiType> kpiTypes = domainDao.listAllDomain(typeQB, EmpEvalKpiType.class);
        if (kpiTypes.isEmpty()) {//已发起旧的新人考核任务未增加type记录
            return new ListWrap<EmpEvalKpiType>(kpiTypes).asMap(type -> type.getKpiTypeId());
        }
        ListWrap<EvalAudit> typeAudits = listTypeAudits(tenantId, empEvalId);
        ListWrap<KpiTypeUsedField> fieldWrap = listWrapTypeUsedField(tenantId, empEvalId);
        Audit2TypeRuleBd typeRuleBd = new Audit2TypeRuleBd(typeAudits.getDatas());
        ListWrap<EvalItemScoreRule> preTypeRues = !typeAudits.isEmpty() ? typeRuleBd.build() : listPreTypeRule(tenantId, empEvalId);
        //查询维度字段字定义配置
        for (EmpEvalKpiType type : kpiTypes) {
            EvalItemScoreRule rule = preTypeRues.mapGet(type.getKpiTypeId());
            type.setTypeRule(rule);
            type.setKpiTypeUsedFields(fieldWrap.groupGet(type.getKpiTypeId()));
        }
        return new ListWrap<EmpEvalKpiType>(kpiTypes).asMap(type -> type.getKpiTypeId());
    }

    public KpiListWrap getWrapBaseEmpEvalKpiTypeV3(TenantId tenantId, String empEvalId) {
        ComQB typeQB = ComQB.build(EmpEvalKpiTypeDo.class, "type")
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", empEvalId)
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        List<EmpEvalKpiType> kpiTypes = domainDao.listAllDomain(typeQB, EmpEvalKpiType.class);
        KpiListWrap wrap = new KpiListWrap(kpiTypes);
        wrap.asMap(EmpEvalKpiType::getKpiTypeId);
        return wrap;
    }

    public List<OpenKpiTypePo> listKpiType(String companyId, String taskUserId, String sql){
        ComQB comQB = ComQB.build(EmpEvalKpiTypeDo.class)
                .clearSelect()
                .select(sql)
                .setRsType(OpenKpiTypePo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("is_deleted", "false");
        return domainDao.listAll(comQB);
    }

    public List<OpenKpiAndOkrItemPo> listKpiAndOkrType(String companyId, String taskUserId, String kpiTypeId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskKpiDo.class, "k")
                .leftJoin(PerfEvaluateTaskRefOkrModel.class, "o")
                .appendOn("k.id = o.task_kpi_id OR k.kpi_item_id = o.task_kpi_id AND k.company_id = o.company_id AND o.is_deleted = 'false'")
                .clearSelect()
                .select("k.kpi_type_id as kpiTypeId,k.kpi_item_id,k.kpi_type_name as kpiTypeName," +
                        "k.kpi_type_weight as kpiTypeWeight,k.is_okr as isOkr,o.target_name as targetName, o.target_id, o.action_id as keyResultId")
                .setRsType(OpenKpiAndOkrItemPo.class)
                .whereEqReq("k.task_user_id", taskUserId)
                .whereEqReq("k.company_id", companyId)
                .whereEqReq("k.kpi_type_id", kpiTypeId)
                .whereEq("k.is_deleted", "false");
        return domainDao.listAll(comQB);
    }

    //预留的分类评分流程
    public ListWrap<EvalItemScoreRule> listPreTypeRule(TenantId tenantId, String empEvalId) {
        ComQB itemRuleQB = ComQB.build(PerfEvaluateTaskItemScoreRuleDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", empEvalId)
                .appendWhere("kpi_item_id='0'")
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        List<EvalItemScoreRule> itemRules = domainDao.listAllDomain(itemRuleQB, EvalItemScoreRule.class);
        for (EvalItemScoreRule itemRule : itemRules) {
            itemRule.customAsRaterConf();
        }
        return new ListWrap<>(itemRules).asMap(EvalItemScoreRule::getKpiTypeId);
    }

    @NotNull
    public ListWrap<EvalAudit> listTypeAudits(TenantId tenantId, String empEvalId) {
        ComQB auditQb = ComQB.build(TaskAuditDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", empEvalId)
                .appendWhere("is_deleted='false' and kpi_item_id ='' and kpi_type_id is not null and kpi_type_id !=''");
        List<EvalAudit> taskAuditDos = domainDao.listAllDomain(auditQb, EvalAudit.class);
        return new ListWrap<>(taskAuditDos).groupBy(taskAuditDo -> taskAuditDo.getKpiTypeId());
    }

    //查询纬度字段字定义配置
    public ListWrap<KpiTypeUsedField> listWrapTypeUsedField(TenantId tenantId, String taskUserId) {
        ComQB used = ComQB.build(EmpEvalTypeUsedFieldDo.class, "tu")
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .appendWhere("is_deleted='false'")
                .orderByAsc("sort");
        List<KpiTypeUsedField> fields = domainDao.listAllDomain(used, KpiTypeUsedField.class);
        return new ListWrap<KpiTypeUsedField>(fields).groupBy(fd -> fd.getKpiTypeId());
    }

    public ListWrap<EvalKpi> listEmpEvalKpiItem(TenantId tenantId, String empEvalId) {
        ComQB kpiQB = ComQB.build(PerfEvaluateTaskKpiDo.class, "item")
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", empEvalId)
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        List<EvalKpi> kpis = domainDao.listAllDomain(kpiQB, EvalKpi.class);
        if (CollUtil.isEmpty(kpis)) {
            return new ListWrap<>(kpis).groupBy(EvalKpi::getKpiTypeId);
        }
        listItemFile(empEvalId, kpis);
        listItemFinishComment(tenantId, empEvalId, kpis);//完成值备注
        loadOkrItemActionId(tenantId, empEvalId, kpis);
        return new ListWrap<>(kpis).groupBy(EvalKpi::getKpiTypeId);
    }

    public ListWrap<EvalKpi> listBaseEmpEvalKpiItem(TenantId tenantId, String empEvalId) {
        ComQB kpiQB = ComQB.build(PerfEvaluateTaskKpiDo.class, "item")
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", empEvalId)
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        List<EvalKpi> kpis = domainDao.listAllDomain(kpiQB, EvalKpi.class);
        return new ListWrap<>(kpis).groupBy(EvalKpi::getKpiTypeId);
    }


    public void listItemFinishComment(TenantId tenantId, String taskUserId, List<EvalKpi> kpis) {
        List<ItemDynamicLog> logPos = listItemDynamic(tenantId.getId(), taskUserId, null);
        if (CollUtil.isEmpty(logPos)) {
            return;
        }
        ListWrap<ItemDynamicLog> groups = new ListWrap<>(logPos).groupBy(log -> log.getKpiItemId());
        for (EvalKpi kpi : kpis) {
            List<ItemDynamicLog> logs = groups.groupGet(kpi.getKpiItemId());
            if (CollUtil.isNotEmpty(logs)) {
                kpi.setFinishValueComment(logs.get(0).getComment());//完成值评论 最新提交的
            }
        }
    }

    public void listItemFile(String taskUserId, List<EvalKpi> kpis) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskFileDo.class)
                .whereEq("task_user_id", taskUserId)
                .whereIn("kpi_item_id", CollUtil.map(kpis, k -> k.getKpiItemId(), true))
                .whereEq("is_deleted", "false")
                .orderByDesc("created_time");
        List<PerfEvaluateTaskFile> taskFiles = domainDao.listAllDomain(comQB, PerfEvaluateTaskFile.class);
        if (CollUtil.isEmpty(taskFiles)) {
            return;
        }
        ListWrap<PerfEvaluateTaskFile> fileListWrap = new ListWrap<>(taskFiles).groupBy(PerfEvaluateTaskFile::getKpiItemId);
        for (EvalKpi kpi : kpis) {
            kpi.setFiles(JSONUtil.toJsonStr(fileListWrap.groupGet(kpi.getKpiItemId())));
        }
    }

    public Map<String, List<EvalKpi>> listEmpEvalKpiItems(TenantId tenantId, List<String> empEvalIds) {
        ComQB kpiQB = ComQB.build(PerfEvaluateTaskKpiDo.class, "item")
                .whereEqReq("company_id", tenantId.getId())
                .whereInReq("task_user_id", empEvalIds)
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        List<EvalKpi> kpis = domainDao.listAllDomain(kpiQB, EvalKpi.class);
        if (CollUtil.isEmpty(kpis)) {
            return new HashMap<>();
        }
        ListWrap<EvalKpi> taskUserIdKpis = new ListWrap<>(kpis).groupBy(EvalKpi::getTaskUserId);
        Map<String, List<EvalKpi>> map = new HashMap<>();
        for (String taskUserId : empEvalIds) {
            if (CollUtil.isEmpty(taskUserIdKpis.groupGet(taskUserId))) {
                continue;
            }
            List<EvalKpi> tempkpis = taskUserIdKpis.groupGet(taskUserId);
            loadOkrItemActionId(tenantId, taskUserId, tempkpis);
            map.put(taskUserId, tempkpis);
        }
        return map;
    }

    public ListWrap<KpiEmp> listByEmp(TenantId tenantId, Collection<String> empIds) {
        if (CollUtil.isEmpty(empIds)) {
            return new ListWrap<KpiEmp>().asMap(kpiEmp -> kpiEmp.getEmpId());
        }
        final ComQB comQB = ComQB.build(EmployeeBaseInfoDo.class, "e")
                .clearSelect().setRsType(KpiEmp.class)
                .select("status, employee_id  as emp_id,  name as emp_name, ding_user_id ex_user_id , avatar,jobnumber")
                .whereEq("companyId", tenantId.getId())
                .whereIn("employeeId", empIds);
        List<KpiEmp> objects = domainDao.listAll(comQB);
        return new ListWrap<>(objects).asMap(kpiEmp -> kpiEmp.getEmpId());
    }

    public void loadOkrItemActionId(TenantId tenantId, String empEvalId, List<EvalKpi> items) {
        ComQB comQB = ComQB.build(EvalRefOkrDo.class)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", empEvalId);
        List<EvalRefOkrDo> refOkrDos = domainDao.listAll(comQB);
        if (CollUtil.isEmpty(refOkrDos)) {
            return;
        }
        ListWrap<EvalRefOkrDo> itemMap = new ListWrap<>(refOkrDos).groupBy(EvalRefOkrDo::getTaskKpiId);
        for (EvalKpi item : items) {
            List<EvalRefOkrDo> evalRefOkrDos = itemMap.groupGet(item.getKpiItemId());
            if (CollUtil.isEmpty(evalRefOkrDos)) {
                continue;
            }
            item.setActionId(evalRefOkrDos.get(0).getActionId());
        }
    }

    /**
     * 查询指标字段字定义配置
     */
    public List<KpiItemUsedField> getItemUsedFieldDoList(TenantId tenantId, String taskUserId) {
        ComQB itemUsed = ComQB.build(PerfEvaluateItemUsedFieldDo.class, "tu")
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .appendWhere("is_deleted='false'")
                .orderByAsc("sort");
        return domainDao.listAllDomain(itemUsed, KpiItemUsedField.class);
    }

    public ListWrap<EvalItemScoreRule> listEmpEvalItemRule(TenantId tenantId, String empEvalId) {
        ComQB itemRuleQB = ComQB.build(PerfEvaluateTaskItemScoreRuleDo.class, "irule")
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", empEvalId)
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        List<EvalItemScoreRule> itemRules = domainDao.listAllDomain(itemRuleQB, EvalItemScoreRule.class);
        if (CollUtil.isNotEmpty(itemRules)){
            itemRules.forEach(EvalItemScoreRule::customAsRaterConf);
        }
        return new ListWrap<>(itemRules).asMap(EvalItemScoreRule::getKpiItemId);
    }

    public EmpEvalKpiType getEmpEvalKpiType(TenantId tenantId, String taskUserId, String kpiTypeId) {
        ComQB kpiTypeQB = ComQB.build(EmpEvalKpiTypeDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEqReq("kpi_type_id", kpiTypeId)
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        return domainDao.findDomain(kpiTypeQB, EmpEvalKpiType.class);
    }

    public List<NodeWeight> listNodeWeightOfItem(EmpEvalRule evalRule, String companyId, String taskUserId, String kpiItemId) {
        ComQB itemRuleQB = ComQB.build(PerfEvaluateTaskItemScoreRuleDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEqReq("kpiItemId", kpiItemId)
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        EvalItemScoreRule itemRule = domainDao.findDomain(itemRuleQB, EvalItemScoreRule.class);
        if (itemRule != null) {//指标评分考核
            return itemRule.nodesWeight();
        }
        ComQB typeQB = ComQB.build(EmpEvalKpiTypeDo.class, "t")
                .join(PerfEvaluateTaskKpiDo.class, "k").appendOn("t.kpi_type_id = k.kpi_type_id and t.task_user_id = k.task_user_id  and t.company_id=k.company_id")
                .whereEqReq("t.company_id", companyId)
                .whereEqReq("t.task_user_id", taskUserId)
                .whereEqReq("k.kpi_item_id", kpiItemId)
                .whereEqReq("k.is_deleted", Boolean.FALSE.toString())
                .whereEqReq("t.is_deleted", Boolean.FALSE.toString());
        EmpEvalKpiType type = domainDao.findDomain(typeQB, EmpEvalKpiType.class);
        if (type != null && type.isTypeScore()) {//维度评分考核节点权重
            return type.nodesWeight();
        }
        if (evalRule != null) {
            return evalRule.asItemRule().nodesWeight();//统一流程考核节点权重
        }

        ComQB taskQB = ComQB.build(PerfEvaluateTaskBaseDo.class, "b")
                .join(PerfEvaluateTaskUserDo.class, "u").appendOn("b.company_id = u.company_id and b.id = u.task_id")
                .whereEqReq("u.company_id", companyId)
                .whereEqReq("u.id", taskUserId);
        PerfEvaluateTaskBaseDo task = domainDao.findOne(taskQB);
        PerfTemplEvaluate templEvaluateJson = JSONObject.parseObject(task.getTemplEvaluateJson(), PerfTemplEvaluate.class);
        if (Objects.nonNull(templEvaluateJson)) {
            NodeWeight self = new NodeWeight(AuditEnum.SELF_SCORE.getScene(), templEvaluateJson.getSelfScoreWeight());
            NodeWeight sup = new NodeWeight(AuditEnum.SUPERIOR_SCORE.getScene(), templEvaluateJson.getSuperiorScoreWeight());
            NodeWeight peer = new NodeWeight(AuditEnum.PEER_SCORE.getScene(), templEvaluateJson.getPeerScoreWeight());
            NodeWeight subPeer = new NodeWeight(AuditEnum.SUB_SCORE.getScene(), templEvaluateJson.getSubScoreWeight());
            return Arrays.asList(self, sup, peer, subPeer);
        }
        return null;
    }


    public ListWrap<EvalFormulaField> listEvalEmpFormulaField(TenantId tenantId, String taskUserId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskFormulaFieldDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        List<EvalFormulaField> formulaFields = domainDao.listAllDomain(comQB, EvalFormulaField.class);
        return new ListWrap<>(formulaFields).groupBy(EvalFormulaField::getKpiItemId);
    }

    public ListWrap<EvalScoreResult> listEvalEmpScoreRs(TenantId tenantId, String taskUserId, List<String> scenes, String auditStatus) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "sc")
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereIn("scorer_type", scenes)
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        if ("wait".equals(auditStatus)) {
            comQB.appendWhere(" ( audit_status is null or audit_status = '')");
        } else {
            comQB.whereEq("audit_status", auditStatus);
        }
        List<EvalScoreResult> results = domainDao.listAllDomain(comQB, EvalScoreResult.class);
        return new ListWrap<>(results);
    }

    public ListWrap<EvalScorerNodeKpiItem> listEvalScoreNodeKpiItem(TenantId tenantId, String taskUserId, Integer status) {
        ComQB scoreNodeComQB = ComQB.build(EmpEvalScorerNodeDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .appendWhere(" ((transfer_type is null) OR (transfer_type = 'transfer' AND transfer_from IS NOT NULL AND transfer_to IS NULL) OR (transfer_type = 'skip' AND transfer_from IS NOT NULL AND transfer_to IS NULL))")
                .whereEq("is_deleted", "false");
        List<EmpEvalScorerNode> scorerNodes = domainDao.listAllDomain(scoreNodeComQB, EmpEvalScorerNode.class);
        if (CollUtil.isEmpty(scorerNodes)) {
            return new ListWrap<>();
        }

        Set<String> scorerNodeIds = scorerNodes.stream().map(EmpEvalScorerNode::getId).collect(Collectors.toSet());

        ComQB comQB = ComQB.build(EmpScorerScoreKpiItemDo.class, "sc")
                .whereEqReq("sc.company_id", tenantId.getId())
                .whereEqReq("sc.task_user_id", taskUserId)
                .whereInReq("sc.scorer_score_node_id",scorerNodeIds)
                .whereEqReq("sc.is_deleted", Boolean.FALSE.toString());
        if (null != status) {
            comQB.whereEq("sc.status", status);
        }
        List<EvalScorerNodeKpiItem> kpiItems = domainDao.listAllDomain(comQB,EvalScorerNodeKpiItem.class);
        return new ListWrap<>(kpiItems);
    }
    public ListWrap<PerfEvalTypeResult> listEvalTypeScoreRs(TenantId tenantId, String taskUserId) {
        ComQB comQB = ComQB.build(PerfEvalTypeResultDo.class, "tr")
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        List<PerfEvalTypeResult> results = domainDao.listAllDomain(comQB, PerfEvalTypeResult.class);
        return new ListWrap<>(results);
    }

    // mod:1=分类,2=指标,4=指标规则,8=指标公示
    public KpiListWrap listAndBuildKpiType(TenantId companyId, String taskUserId, String opEmpId, int mod, int logMod) {
        ListWrap<EmpEvalKpiType> kpiTypes = listEmpEvalKpiTypeV3(companyId, taskUserId);
        List<OkrGoal> goals = this.listOkrGoals(companyId.getId(), taskUserId);
        KpiListWrap wrap = new KpiListWrap(kpiTypes.getDatas(), mod);
        for (EmpEvalKpiType type : wrap.getDatas()) {
            if (type.isOkr()) {
                type.setOkrGoals(goals);
            }
        }
        if ((4 & mod) == 0) {
            return wrap;
        }
        //指标
        ListWrap<EvalKpi> itemWrap = listEmpEvalKpiItem(companyId, taskUserId);
        if (itemWrap.isEmpty()) {
            return wrap;
        }
        //录入人
        List<EvalKpi> kpiItems = itemWrap.getDatas();
//        Set<String> inpuEmpIds = kpiItems.stream().flatMap(evalKpi -> StrUtil.splitTrim(evalKpi.getResultInputEmpId(), ",")
//                .stream()).collect(Collectors.toSet());
//        ListWrap<KpiEmp> inpuEmpMap = listByEmp(companyId, inpuEmpIds);
//        kpiItems.forEach(item -> item.loadInputEmps(inpuEmpMap));
        //指标使用规则
        List<KpiItemUsedField> kpiItemUsedFields = getItemUsedFieldDoList(companyId, taskUserId);
        for (EmpEvalKpiType type : wrap.getDatas()) {
            List<EvalKpi> items = itemWrap.groupGet(type.getKpiTypeId());
            type.setItems(items);
            type.initKpiItemUsedFields(kpiItemUsedFields);
        }
        //指标规则
        if ((4 & mod) > 0) {
            ListWrap<EvalItemScoreRule> ruleWrap = listEmpEvalItemRule(companyId, taskUserId);
            for (EvalKpi item : kpiItems) {
                item.setItemScoreRule(ruleWrap.mapGet(item.getKpiItemId()));
            }
            //return wrap;
        }
        //指标公式
        if ((8 & mod) > 0) {
            ListWrap<EvalFormulaField> formulaWrap = listEvalEmpFormulaField(companyId, taskUserId);
            for (EvalKpi item : kpiItems) {
                item.setFormulaFields(formulaWrap.groupGet(item.getKpiItemId()));
            }
            //return wrap;
        }
        //指标评分
        if ((16 & mod) > 0) {
            ListWrap<EvalScoreResult> empScoreRs = listEvalEmpScoreRs(companyId, taskUserId, AuditEnum.scoreScenes(), null);
            empScoreRs.groupBy(EvalScoreResult::getKpiItemId);
//            ListWrap<EvalScorerNodeKpiItem> nodeKpiItemWrap = listEvalScoreNodeKpiItem(companyId, taskUserId, null);
//            if (!nodeKpiItemWrap.isEmpty()) {
//                nodeKpiItemWrap.groupBy(EvalScorerNodeKpiItem::getKpiItemId);
//            }
            for (EvalKpi item : kpiItems) {
                List<EvalScoreResult> results = empScoreRs.groupGet(item.getKpiItemId());
                item.setWaitScoresOld(results);
//                if (!nodeKpiItemWrap.isEmpty()) {
//                    List<EvalScorerNodeKpiItem> scorerNodeKpiItems = nodeKpiItemWrap.groupGet(item.getKpiItemId());
//                    item.setWaitScores(scorerNodeKpiItems);//评价环节各个评分人当前评价指标
//                }
            }
            ListWrap<PerfEvalTypeResult> typeScores = listEvalTypeScoreRs(companyId, taskUserId);
            typeScores.groupBy(PerfEvalTypeResult::getKpiTypeId);
            for (EmpEvalKpiType type : kpiTypes.getDatas()) {
                type.acceptTypeRs(typeScores.groupGet(type.getKpiTypeId()));
            }
            //return wrap;
        }

        //指标标签及更新日志,详情会使用
        if ((1 & logMod) > 0) {
            List<ItemDynamicLog> logPos = listItemDynamic(companyId.getId(), taskUserId, null);
            ListWrap<ItemDynamicLog> groups = new ListWrap<>(logPos).groupBy(log -> log.getKpiItemId());
            ListWrap<ItemTag> tagGroups = listWrapTagName(companyId, CollUtil.map(kpiItems, i -> i.getKpiItemId(), true));

            for (EvalKpi item : kpiItems) {
                List<ItemDynamicLog> logs = groups.groupGet(item.getKpiItemId());
                item.setInputChangeRecord(logs);
                List<ItemTag> itemTags = tagGroups.groupGet(item.getKpiItemId());
                List<String> names = CollUtil.map(itemTags, t -> t.getName(), true);
                item.setItemTags(names);
            }
        }
        return wrap;
    }

    //
    public KpiListWrap getKpiTypeWrapV3(TenantId companyId, String taskUserId) {
        KpiListWrap wrap = this.getWrapBaseEmpEvalKpiTypeV3(companyId, taskUserId);
        ListWrap<EvalKpi> itemWrap = this.listBaseEmpEvalKpiItem(companyId, taskUserId); //指标
        if (itemWrap.isEmpty()) {
            return wrap;
        }
        wrap.getDatas().forEach(type -> {
            List<EvalKpi> items = itemWrap.groupGet(type.getKpiTypeId());
            type.setItems(items);//set 指标
        });
        return wrap;
    }

    public ListWrap<ItemTag> listWrapTagName(TenantId companyId, List<String> kpiItemIds) {
        ComQB comQB = ComQB.build(CompanyTagModel.class, "t")
                .join(CompanyItemRelTagModel.class, "tg")
                .appendOn(" t.company_id = tg.company_id and t.id = tg.tag_id ")
                .clearSelect().setRsType(ItemTag.class)
                .select("t.tag_name, tg.company_item_id  item_id ")
                .whereEq("t.is_deleted", "false")
                .whereEq("tg.is_deleted", "false")
                .whereEq("t.company_id", companyId.getId())
                .whereInReq("tg.company_item_id", kpiItemIds);
        List<ItemTag> itemTags = domainDao.listAll(comQB);
        return new ListWrap<>(itemTags).groupBy(itemTag -> itemTag.getItemId());
    }

    public List<ItemDynamicLog> listItemDynamic(String companyId, String taskUserId, String kpiItemId) {
        ComQB qb = ComQB.buildDiff(ItemDynamicLog.class, "operation_log", "o")
                .clearSelect().select("o.kpi_item_id, e.`name` empName, e.avatar empAvatar, o.business_scene, o.before_value, o.after_value, o.description content, o.created_time")
                .leftJoin(EmployeeBaseInfoDo.class, "e").appendOn(" o.created_user = e.employee_id and o.company_id = e.company_id ")
                .whereEqReq("o.company_id", companyId)
                .whereEqReq("o.ref_id", taskUserId)
                .whereEq("o.kpi_item_id", kpiItemId)
                .appendWhere("o.business_scene in('input_finish_value','submit_finish_value','finish_value_audit','add_ind_ext_score_item')")
                .orderByDesc("o.created_time");
        return domainDao.listAll(qb);
    }

    public ListWrap<ItemDynamicLog> listItemDynamicWrap(String companyId, List<String> taskUserIds) {
        ComQB qb = ComQB.buildDiff(ItemDynamicLog.class, "operation_log", "o")
                .clearSelect().select("o.ref_id as taskUserId, o.kpi_item_id, e.`name` empName, e.avatar empAvatar, o.business_scene, o.before_value, o.after_value, o.description content, o.created_time")
                .leftJoin(EmployeeBaseInfoDo.class, "e").appendOn(" o.created_user = e.employee_id and o.company_id = e.company_id ")
                .whereEqReq("o.company_id", companyId)
                .whereIn("o.ref_id", taskUserIds)
                .appendWhere("o.business_scene in('input_finish_value','submit_finish_value','finish_value_audit','add_ind_ext_score_item')")
                .orderByDesc("o.created_time");
        List<ItemDynamicLog> logs = domainDao.listAll(qb);
        return new ListWrap<>(logs).groupBy(ItemDynamicLog::getTaskUserId);
    }

    public ListWrap<EvalKpi> listNotUrgingFinishValue(TenantId tenantId, String empEvalId, String id) {
        if (StringUtils.isBlank(empEvalId) && StringUtils.isBlank(id)) {
            return null;
        }
        ComQB kpiQB = ComQB.build(PerfEvaluateTaskKpiDo.class)
                .whereEq("company_id", tenantId.getId())
                .whereEq("is_deleted", "false")
                .whereEq("task_user_id", empEvalId)
                //.whereEq("scorer_type", "auto")
                //.whereIsNull("item_finish_value")
                .whereEq("id", id)
                .appendWhere(" (scorer_type = 'auto' or `must_result_input` =true) and if(kpi_type_classify = 'workItem',\n" +
                        "          (work_item_finish_value = '' OR work_item_finish_value IS NULL),\n" +
                        "          item_finish_value IS NULL)")
                .whereIsNull("urging_flag");
        List<EvalKpi> kpis = domainDao.listAllDomain(kpiQB, EvalKpi.class);
        return new ListWrap<>(kpis);
    }

    public ListWrap<EvalKpi> listNotUrgingWorkItem(TenantId tenantId, String taskId, List<String> ids) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskKpiDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEq("is_deleted", "false")
                .whereEqReq("task_id", taskId)
                .whereNotEq("result_input_type", "no")
                .whereIsNull("work_item_finish_value")
                .whereEq("kpi_type_classify", "workItem")
                .whereIn("task_user_id", ids);
        List<EvalKpi> evalKpis = this.domainDao.listAllDomain(comQB, EvalKpi.class);
        return new ListWrap<>(evalKpis);
    }

    public void batchUpdateUrgingFlag(TenantId tenantId, List<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_kpi")
                .set("urging_flag", "true")
                .whereEq("company_id", tenantId.getId())
                .whereIn("id", ids);
        this.domainDao.update(updateBuilder);
    }

    public ListWrap<EmpEvalKpiIfvEmpPo> listNotFinishedValueTask(TenantId tenantId, List<String> empEvalId) {
        InnerFields<String> fields = new InnerFields<>(empEvalId);
        if (fields.isEmpty()) {
            return new ListWrap<>();
        }

        String joinTaskUserIds = CollUtil.join(empEvalId, "','", "'", "'");
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(EmpEvalKpiIfvEmpPo.class)
                .setSql("SELECT t.*, (SELECT GROUP_CONCAT(name) FROM employee_base_info WHERE company_id = #{companyId} and FIND_IN_SET(employee_id, t.result_input_emp_id)) as resultInputEmpName,\n" +
                        "(SELECT GROUP_CONCAT(ding_user_id) FROM employee_base_info WHERE company_id = #{companyId} and FIND_IN_SET(employee_id, t.result_input_emp_id)) AS resultInputDingId from " +
                        "(SELECT k.id, k.task_id, k.emp_id, k.task_user_id, k.kpi_item_id, k.kpi_item_name,k.urging_flag, e1.`name` as empName,e1.avatar AS avatar,e1.ding_user_id,IF(k.result_input_type = 'exam',k.emp_id, k.result_input_emp_id) as result_input_emp_id FROM `perf_evaluate_task_kpi` k " +
                        "LEFT JOIN employee_base_info e1 on e1.employee_id = k.emp_id " +
                        //"LEFT JOIN perf_evaluate_task_item_score_rule s on s.task_user_id = k.task_user_id AND s.kpi_item_id = k.kpi_item_id AND s.is_deleted = 'false' " +
                        "WHERE k.company_id = #{companyId} and k.task_user_id in (" + joinTaskUserIds + ") and k.scorer_type = 'auto' AND k.is_deleted = 'false' and k.item_finish_value is null" +
                        //" AND s.id is NULL " +
                        ")t ");
        sqlBuilder.setValue("companyId", tenantId.getId());
        List list = this.domainDao.listAll(sqlBuilder);

        NativeSQLBuilder sqlBuilder2 = NativeSQLBuilder.build(EmpEvalKpiIfvEmpPo.class).setSql("SELECT\n" +
                "  t.*,\n" +
                "(SELECT GROUP_CONCAT(`name`) FROM employee_base_info WHERE company_id = #{companyId} AND FIND_IN_SET(employee_id, t.result_input_emp_id)) AS resultInputEmpName,\n" +
                "(SELECT GROUP_CONCAT(ding_user_id) FROM employee_base_info WHERE company_id = #{companyId} and FIND_IN_SET(employee_id, t.result_input_emp_id)) AS resultInputDingId\n" +
                "FROM (SELECT\n" +
                "        k.id,\n" +
                "        k.task_id,\n" +
                "        k.kpi_type_classify,\n" +
                "        k.emp_id,\n" +
                "        k.task_user_id,\n" +
                "        k.kpi_item_id,\n" +
                "        k.kpi_item_name,\n" +
                "        k.urging_flag,\n" +
                "        e1.`name`  AS empName,\n" +
                "        e1.avatar  AS avatar,\n" +
                "        IF(k.result_input_type = 'exam', k.emp_id, k.result_input_emp_id) AS result_input_emp_id\n" +
                "      FROM `perf_evaluate_task_kpi` k LEFT JOIN employee_base_info e1 ON e1.employee_id = k.emp_id\n" +
                "      WHERE k.company_id = #{companyId} AND\n" +
                "            k.task_user_id IN   (" + joinTaskUserIds + ")  AND  k.must_result_input= TRUE " +
                " AND k.is_deleted = 'false' and k.scorer_type != 'auto' AND if(kpi_type_classify='workItem',(work_item_finish_value='' or work_item_finish_value is null),k.item_finish_value IS NULL) " +
                " and if (k.item_type = 'non-measurable' and k.input_format = 'text', (item_finish_value_text = '' OR item_finish_value_text IS NULL ), k.item_finish_value IS NULL) ) t");
        sqlBuilder2.setValue("companyId", tenantId.getId());
        list.addAll(this.domainDao.listAll(sqlBuilder2));

        if (CollUtil.isEmpty(list)) {
            return new ListWrap<>();
        }

        return new ListWrap<EmpEvalKpiIfvEmpPo>(list).groupBy(EmpEvalKpiIfvEmpPo::getTaskUserId);
    }

    public KpiListWrap listAndBuildOkrType(TenantId tenantId, String taskUserId) {
        int mod = EmpEvalMerge.item | EmpEvalMerge.type;
        //考核表格式
        ComQB typeQB = ComQB.build(EmpEvalKpiTypeDo.class)
                //ComQB typeQB = ComQB.build(PerfEvaluateTaskOkrTypeDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEqReq("is_deleted", Boolean.FALSE.toString())
                .appendWhere("is_okr='true'");
        List<EmpEvalKpiType> kpiTypes = domainDao.listAllDomain(typeQB, EmpEvalKpiType.class);


        ComQB kpiQB = ComQB.build(PerfEvaluateTaskKpiDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        List<EvalKpi> kpis = domainDao.listAllDomain(kpiQB, EvalKpi.class);
        ListWrap<EvalKpi> itemWrap = new ListWrap<>(kpis).groupBy(EvalKpi::getKpiTypeId);


        ComQB itemRuleQB = ComQB.build(PerfEvaluateTaskItemScoreRuleDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .appendWhere("is_deleted='false' and kpi_item_id='0'");
        List<EvalItemScoreRule> typeRules = domainDao.listAllDomain(itemRuleQB, EvalItemScoreRule.class);
        ListWrap<EvalItemScoreRule> typeRuleWrap = new ListWrap<>(typeRules).asMap(EvalItemScoreRule::getKpiTypeId);

        for (EmpEvalKpiType type : kpiTypes) {
            EvalItemScoreRule rule = typeRuleWrap.mapGet(type.getKpiTypeId());
            type.setTypeRule(rule);
            List<EvalKpi> items = itemWrap.groupGet(type.getKpiTypeId());
            for (EvalKpi item : items) {
                if (item.getItemScoreRule() == null) {
                    item.setItemScoreRule(rule);
                }
            }
            type.setItems(items);
        }
        ListWrap<EvalAudit> oldTypeAudits = listTypeAudits(tenantId, taskUserId);
        KpiListWrap wrap = new KpiListWrap(kpiTypes, mod);
        wrap.setOldTypeAudits(oldTypeAudits);
        wrap.asMap(type -> type.getKpiTypeId());
        return wrap;
    }

    public List<OkrKRPo> listOkrKRs(TenantId companyId, String userId, String kpiTypeId) {
        ComQB comQB = ComQB.build(EvalRefOkrDo.class, "r")
                .leftJoin(PerfEvaluateTaskKpiDo.class, "k")
                .appendOn("(r.task_kpi_id = k.id or r.task_kpi_id = k.kpi_item_id) and r.task_user_id=k.task_user_id")
                .clearSelect().select("r.*, k.item_weight, k.open_okr_score, k.okr_score,k.kpi_item_id,k.okr_ref_flag").setRsType(OkrKRPo.class)
                .whereEq("r.is_deleted", Boolean.FALSE.toString())
                .whereEq("k.is_deleted", Boolean.FALSE.toString())
                .whereEqReq("r.company_id", companyId.getId())
                .whereEqReq("r.task_user_id", userId)
                .whereEq("k.kpi_type_id", kpiTypeId);
        return domainDao.listAll(comQB);
    }

    public List<BaseScoreResult> listLevelTotal(String companyId, String taskUserId) {
        ComQB qb = ComQB.build(PerfEvalTotalLevelResultDo.class, "tr")
                .whereEq("company_id", companyId)
                .whereEq("taskUserId", taskUserId)
                .appendWhere("is_deleted='false'");
        List<BaseScoreResult> totalRs = domainDao.listAllDomain(qb, BaseScoreResult.class);
        return totalRs;
    }

    public List<EvalScoreResult> listScoreTotal(String companyId, String taskUserId) {
        ComQB qb = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "tr")
                .whereEq("company_id", companyId)
                .whereEq("taskUserId", taskUserId)
                .whereLikeReq("scorer_type", "total")
                .appendWhere("is_deleted='false'");
        List<EvalScoreResult> totalRs = domainDao.listAllDomain(qb, EvalScoreResult.class);
        return totalRs;
    }


    public EvalKpi getKpi(TenantId companyId, String userId, String itemId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskKpiDo.class, "k")
                .whereEq("k.is_deleted", Boolean.FALSE.toString())
                .whereEqReq("k.company_id", companyId.getId())
                .whereEqReq("k.task_user_id", userId)
                .whereEqReq("k.kpi_item_id", itemId);
        return domainDao.findDomain(comQB, EvalKpi.class);
    }

    public PagedList<FinishValueKpiItemSelectPo> pagedItemForSelectorFinishValue(TenantId companyId, String opEmpId,
                                                                                 String itemName, Integer pageNo, Integer pageSize) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskKpiDo.class, "k")
                .join(CompanyMsgCenterDo.class,"c").appendOn("k.task_user_id = c.link_id and k.company_id = c.company_id")
                .clearSelect().select("distinct kpi_item_id,kpi_item_name").setRsType(FinishValueKpiItemSelectPo.class)
                .whereEq("k.is_deleted", Boolean.FALSE.toString())
                .whereEqReq("k.company_id", companyId.getId())
                .whereLikeReq("result_input_emp_id", opEmpId)
                .whereLike("kpi_item_name", itemName)
                .appendWhere("FIND_IN_SET(#{opEmpId}, IF(k.result_input_type = 'exam', k.emp_id, k.result_input_emp_id))", opEmpId)
                .appendWhere(" c.business_scene='task_submit_progress' and c.handler_status='false'  ")
                .whereEqReq("c.emp_id",opEmpId)
                .setPage(pageNo, pageSize);
        return domainDao.listPage(comQB);
    }

    public PagedList<ExportInputValue> pagedFinishValue(EvaluateTaskKpiQueryVO queryVO, CompanyConf company) {
        ComQB evalQB = ComQB.build(CompanyMsgCenterDo.class, "m")
                .join(PerfEvaluateTaskUserDo.class, "u").appendOn("m.company_id = u.company_id and m.link_id = u.id")
                .join(PerfEvaluateTaskBaseDo.class, "b").appendOn("u.company_id = b.company_id and u.task_id = b.id")
                .join(PerfEvaluateTaskKpiDo.class, "k").appendOn("u.company_id = k.company_id and u.id = k.task_user_id")
                .clearSelect()
                .select("u.id as taskUserId,u.company_id, u.task_id,b.cycle_id,u.emp_id,u.emp_name,u.avatar,u.org_id,u.emp_org_name,u.at_org_name_path,u.eval_org_id,u.eval_org_name")
                .select("GROUP_CONCAT(k.id) as canInputItemKeys,b.task_name")
                .setRsType(ExportInputValue.class)
                .whereEqReq("m.company_id", queryVO.getCompanyId())
                .whereEq("m.emp_id", queryVO.getCreatedUser())
                .whereIn("m.business_scene", Arrays.asList(MsgSceneEnum.TASK_SUBMIT_PROGRESS.getType(), MsgSceneEnum.TASK_SUBMIT_AUTO_ITEM_PROGRESS.getType(), MsgSceneEnum.REJECT_FINISH_VALUE.getType()))
                .whereEq("m.handler_status", Boolean.FALSE.toString())
                .whereEq("k.is_deleted", Boolean.FALSE.toString())
                .whereEq("k.final_submit_finish_value", 0)
                .whereEq("k.kpi_item_id", queryVO.getKpiItemId())
                .whereIn("k.result_input_type", Arrays.asList("exam", "user", "manager", "role"))
                .whereEq("b.performance_type", queryVO.getPerformanceType())
                .whereEq("b.is_deleted", Boolean.FALSE.toString())
                .whereIn("u.task_id", queryVO.getTaskIds())
                .whereIn("u.emp_id", queryVO.getEmpIds())
                .whereEq("u.eval_org_id", queryVO.getEvalOrgId())
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereIn("u.task_status", Arrays.asList("confirmed", "scoring", "finishValueAudit", "changing"));
//                .appendWhere(" IF(k.scorer_type = 'auto' and u.task_status = 'scoring',k.scorer_type !='auto',1=1) ");
        evalQB.appendWhere("FIND_IN_SET('" + queryVO.getCreatedUser() + "', IF(k.result_input_type = 'exam',k.emp_id,k.result_input_emp_id))");
        //没有人提交评分的任务还可以录完成值
        if (!company.canResSubmitInputFinish()) {
            evalQB.appendWhere(" NOT EXISTS (SELECT id FROM perf_evaluate_task_score_result WHERE task_id = k.task_id AND emp_id = k.emp_id and task_user_id = k.task_user_id and company_id='" + queryVO.getCompanyId() + "' AND audit_status = 'pass' AND scorer_type NOT IN ('modify_item_audit','change_item_audit')) ");
        }
        if ("emp".equals(queryVO.getOrderBy())) {
            evalQB.orderBy(" u.emp_id ");
        } else {
            evalQB.orderByDesc(" u.created_time ");
        }
        evalQB.groupBy("u.id");
        evalQB.setPage(queryVO.getPageNo(), queryVO.getPageSize());
        PagedList<ExportInputValue> pagedList = domainDao.listPage(evalQB);
        if (CollUtil.isEmpty(pagedList.getData())) {
            return pagedList;
        }
        ListWrap<KpiEmp> empWrap = this.listByEmp(new TenantId(queryVO.getCompanyId()), CollUtil.map(pagedList.getData(), list -> list.getEmpId(), true));
        pagedList.getData().forEach(page -> {
            KpiEmp emp = empWrap.mapGet(page.getEmpId());
            page.setDingUserId(Objects.nonNull(emp) ? emp.getExUserId() : null);
            page.setJobnumber(Objects.nonNull(emp) ? emp.getJobnumber() : null);
        });
        List<String> itemKey = pagedList.getData().stream().flatMap(user -> StrUtil.splitTrim(user.getCanInputItemKeys(), ",").stream())
                .collect(Collectors.toList());
        //查询要录入的指标
        ComQB itemQB = ComQB.build(PerfEvaluateTaskKpiDo.class, "k")
                .join(EmpEvalTypeUsedFieldDo.class, "tuf1 FORCE INDEX (idx_taskUserId_companyId)")
                .appendOn("k.task_user_id = tuf1.task_user_id and k.kpi_type_id = tuf1.kpi_type_id")
                .clearSelect()
                .select("k.*,if(tuf1.`show` = 1, k.item_unit, '') as itemUnit")
                .whereEqReq("k.company_id", queryVO.getCompanyId())
                .whereEqReq("tuf1.field_id", "unit")
                .whereEqReq("tuf1.is_deleted", Boolean.FALSE.toString())
                .whereInReq("k.id", itemKey);
        List<EvalKpiInputValue> items = domainDao.listAllDomain(itemQB, EvalKpiInputValue.class);
        if (CollUtil.isEmpty(items)) {
            return pagedList;
        }
        List<String> taskUserIds = CollUtil.distinct(CollUtil.map(pagedList.getData(), user -> user.getTaskUserId(), true));
        //完成值更新记录
        ListWrap<ItemDynamicLog> logPos = listItemDynamicWrap(queryVO.getCompanyId(), taskUserIds);
        for (EvalKpiInputValue item : items) {
            item.buildInputRecord(logPos.groupGet(item.getTaskUserId()));
            List<KpiItemUsedField> kpiItemUsedFields = getItemUsedFieldDoList(new TenantId(queryVO.getCompanyId()), item.getTaskUserId());
            item.initKpiItemUsedFields(kpiItemUsedFields);
        }
        //完成值暂存记录
        ComQB comQB = ComQB.build(InputFinishValCacheDo.class)
                .whereEqReq("company_id", queryVO.getCompanyId())
                .whereInReq("task_user_id", taskUserIds)
                .whereEq("operate_emp_id", queryVO.getCreatedUser())
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .orderByDesc("created_time");
        List<InputFinishValCache> finishValCaches = domainDao.listAllDomain(comQB, InputFinishValCache.class);
        Map<String, InputFinishValCache> map = CollUtil.toMap(finishValCaches, new HashMap<>(), c -> c.getTaskUserId() + "_" + c.getKpiItemId());
        items.forEach(k -> {
            k.buildCacheFinishValue(map.get(k.getTaskUserId() + "_" + k.getKpiItemId()), null);
        });
        ListWrap<EvalKpiInputValue> kpiWrap = new ListWrap<>(items).groupBy(item -> item.getTaskUserId());
        pagedList.getData().forEach(paged -> {
            paged.repaceOrgNamePath();
            paged.setItems(kpiWrap.groupGet(paged.getTaskUserId()));
        });
        return pagedList;
    }

    public List<ExportInputValue> listFinishValue(EvaluateTaskKpiQueryVO queryVO, CompanyConf company) {
        ComQB evalQB = ComQB.build(CompanyMsgCenterDo.class, "m")
                .join(PerfEvaluateTaskUserDo.class, "u").appendOn("m.company_id = u.company_id and m.link_id = u.id")
                .join(PerfEvaluateTaskBaseDo.class, "b").appendOn("u.company_id = b.company_id and u.task_id = b.id")
                .join(PerfEvaluateTaskKpiDo.class, "k").appendOn("u.company_id = k.company_id and u.id = k.task_user_id")
                .clearSelect()
                .select("u.id as taskUserId,u.company_id, u.task_id,b.cycle_id,u.emp_id,u.emp_name,u.avatar,u.org_id,u.emp_org_name,u.at_org_name_path,u.eval_org_id,u.eval_org_name")
                .select("GROUP_CONCAT(k.id) as canInputItemKeys,b.task_name")
                .setRsType(ExportInputValue.class)
                .whereEqReq("m.company_id", queryVO.getCompanyId())
                .whereEq("m.emp_id", queryVO.getCreatedUser())
                .whereIn("m.business_scene", Arrays.asList(MsgSceneEnum.TASK_SUBMIT_PROGRESS.getType(), MsgSceneEnum.TASK_SUBMIT_AUTO_ITEM_PROGRESS.getType(), MsgSceneEnum.REJECT_FINISH_VALUE.getType()))
                .whereEq("m.handler_status", Boolean.FALSE.toString())
                .whereEq("k.is_deleted", Boolean.FALSE.toString())
                .whereEq("k.final_submit_finish_value", 0)
                .whereEq("k.kpi_item_id", queryVO.getKpiItemId())
                .whereIn("k.result_input_type", Arrays.asList("exam", "user", "manager", "role"))
                .whereEq("b.performance_type", queryVO.getPerformanceType())
                .whereEq("b.is_deleted", Boolean.FALSE.toString())
                .whereIn("u.task_id", queryVO.getTaskIds())
                .whereIn("u.emp_id", queryVO.getEmpIds())
                .whereEq("u.eval_org_id", queryVO.getEvalOrgId())
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereIn("u.task_status", Arrays.asList("confirmed", "scoring", "finishValueAudit"));
        evalQB.appendWhere("FIND_IN_SET('" + queryVO.getCreatedUser() + "', IF(k.result_input_type = 'exam',k.emp_id,k.result_input_emp_id))");
        //没有人提交评分的任务还可以录完成值
        if (!company.canResSubmitInputFinish()) {
            evalQB.appendWhere(" NOT EXISTS (SELECT id FROM perf_evaluate_task_score_result WHERE task_id = k.task_id AND emp_id = k.emp_id and task_user_id = k.task_user_id and company_id='" + queryVO.getCompanyId() + "' AND audit_status = 'pass' AND scorer_type NOT IN ('modify_item_audit','change_item_audit')) ");
        }

        if (company.openInputOnScoring()) {
            //开启再评分中可以录入完成值。如果待办是自动计算指标录入，则需过滤指标的类型为auto
            evalQB.appendWhere(" IF(m.business_scene = 'task_submit_auto_item_progress',k.scorer_type = 'auto' ,1=1) ");
        } else {
            //未开启再评分中可以录入完成值。批量查询完成值需要过滤自动计算指标
            evalQB.appendWhere(" IF(k.scorer_type = 'auto' and u.task_status = 'scoring',k.scorer_type !='auto',1=1) ");
        }
        if ("emp".equals(queryVO.getOrderBy())) {
            evalQB.orderBy(" u.emp_id ");
        } else {
            evalQB.orderByDesc(" u.created_time ");
        }
        evalQB.groupBy("u.id");
        List<ExportInputValue> listAll = domainDao.listAll(evalQB);
        if (CollUtil.isEmpty(listAll)) {
            return listAll;
        }
        ListWrap<KpiEmp> empWrap = this.listByEmp(new TenantId(queryVO.getCompanyId()), CollUtil.map(listAll, list -> list.getEmpId(), true));
        listAll.forEach(page -> {
            KpiEmp emp = empWrap.mapGet(page.getEmpId());
            page.setDingUserId(Objects.nonNull(emp) ? emp.getExUserId() : null);
            page.setJobnumber(Objects.nonNull(emp) ? emp.getJobnumber() : null);
        });
        List<String> itemKey = listAll.stream().flatMap(user -> StrUtil.splitTrim(user.getCanInputItemKeys(), ",").stream())
                .collect(Collectors.toList());
        //查询要录入的指标
        ComQB itemQB = ComQB.build(PerfEvaluateTaskKpiDo.class)
                .whereEqReq("company_id", queryVO.getCompanyId())
                .whereInReq("id", itemKey);
        List<EvalKpiInputValue> items = domainDao.listAllDomain(itemQB, EvalKpiInputValue.class);
        if (CollUtil.isEmpty(items)) {
            return listAll;
        }
        List<String> taskUserIds = CollUtil.distinct(CollUtil.map(listAll, user -> user.getTaskUserId(), true));
        //完成值更新记录
        ListWrap<ItemDynamicLog> logPos = listItemDynamicWrap(queryVO.getCompanyId(), taskUserIds);
        for (EvalKpiInputValue item : items) {
            item.buildInputRecord(logPos.groupGet(item.getTaskUserId()));
        }
        //完成值暂存记录
        ComQB comQB = ComQB.build(InputFinishValCacheDo.class)
                .whereEqReq("company_id", queryVO.getCompanyId())
                .whereInReq("task_user_id", taskUserIds)
                .whereEq("operate_emp_id", queryVO.getCreatedUser())
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .orderByDesc("created_time");
        List<InputFinishValCache> finishValCaches = domainDao.listAllDomain(comQB, InputFinishValCache.class);
        Map<String, InputFinishValCache> map = CollUtil.toMap(finishValCaches, new HashMap<>(), c -> c.getTaskUserId() + "_" + c.getKpiItemId());
        items.forEach(k -> {
            k.buildCacheFinishValue(map.get(k.getTaskUserId() + "_" + k.getKpiItemId()), null);
        });
        ListWrap<EvalKpiInputValue> kpiWrap = new ListWrap<>(items).groupBy(item -> item.getTaskUserId());
        listAll.forEach(paged -> {
            paged.repaceOrgNamePath();
            paged.setItems(kpiWrap.groupGet(paged.getTaskUserId()));
        });
        return listAll;
    }

    public List<EmpDetailKpiTypePo> loadKpiTypeR1(EmpDetailBuilder builder, EmpEvalMerge evalMerge) {
        try {
            EvalUser taskUser = builder.getTaskUser();
            String companyId = taskUser.getCompanyId().getId();
            String taskUserId = taskUser.getId();
            List<EmpDetailKpiTypePo> kpiTypes = evalMerge.getKpiTypes().getDatas().stream().map(type -> Convert.convert(EmpDetailKpiTypePo.class, type))
                    .collect(Collectors.toList());
            if (kpiTypes.isEmpty()) {
                return kpiTypes;
            }
            NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(EmpEvalDetailKpiItemPo.class).setSql(
                    "SELECT k.*,e.name empName,e.avatar,e.status empStatus," +
                            //"ie.name itemScorer,ie.avatar itemScorerAvatar,ie.status itemScorerStatus," +
                            //"re.name resultInputEmpName,re.avatar resultInputEmpAvatar,re.status resultInputEmpStatus," +
                            "o.action_id,o.target_id,o.target_name,o.okr_task_id,o.okr_task_name,o.dept_name,o.evaluate_start_date,o.goal_weight,o.kr_type,o.kr_actions,o.goal_actions " +
                            //"d.is_temporary as kpiTypeIsTemporary,i.item_plan_value " +
                            " FROM perf_evaluate_task_kpi k " +
                            "left join employee_base_info e on  e.employee_id = k.emp_Id  and e.company_id = k.company_id " +
                            //"left join employee_base_info ie on ie.employee_id = k.scorer_obj_id and ie.company_id = k.company_id " +
                            //"left join employee_base_info re on re.employee_id = k.result_input_emp_id and  re.company_id = k.company_id " +
                            "left join perf_evaluate_task_ref_okr o on (k.id = o.task_kpi_id or k.kpi_item_id = o.task_kpi_id) and  k.company_id = o.company_id  and o.is_deleted = 'false' " +
                            //"left join company_dic d on d.id = k.kpi_type_id and d.is_deleted = 'false' and d.company_id = k.company_id " +
                            //"left join company_kpi_item i on k.company_id = i.company_id and k.kpi_item_id = i.id " +
                            "where k.is_deleted = 'false' and k.task_user_id = #{taskUserId} and k.company_id = #{companyId}");
            sqlBuilder.setValue("companyId", companyId);
            sqlBuilder.setValue("taskUserId", taskUserId);
            sqlBuilder.append("order by k.kpi_type_id ASC, k.`order` asc ");
            List<EmpEvalDetailKpiItemPo> items = domainDao.listAll(sqlBuilder);
            if (items.isEmpty()) {
                return kpiTypes;
            }
            /**查询指标字定义字段配置*/
            //List<KpiItemUsedField> itemUsedFieldDos = listKpiItemUsedField(companyId,taskUserId);

            //查询audit
//        ComQB taskAudit = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "r")
//                .whereEq("scorer_type", AuditEnum.FINISH_VALUE_AUDIT.getScene())
//                .appendWhere("(audit_status is null or audit_status = '')")
//                .appendWhere("is_deleted='false'");

            ListScoreResultWrap listScoreResultWrap = builder.curScoreRss(null, AuditEnum.FINISH_VALUE_AUDIT.getScene());
            List<PerfEvaluateTaskScoreResultDo> resultDoList = listScoreResultWrap.getResultDos();
            //指标完成值审核
            List<PerfEvaluateTaskScoreResultDo> itemAudits = resultDoList.stream().filter(audit -> StringUtils.isNotBlank(audit.getKpiItemId())).collect(Collectors.toList());
            //指标完成值审核人
            Map<String, List<PerfEvaluateTaskScoreResultDo>> auditDoMap = null;
            if (!itemAudits.isEmpty()) {
                auditDoMap = itemAudits.stream().filter(audit -> StringUtils.isNotBlank(audit.getKpiItemId()))
                        .collect(Collectors.toList()).stream().collect(Collectors.groupingBy(PerfEvaluateTaskScoreResultDo::getKpiItemId));
            }
            //通用完成值审核人
            List<String> commonAuditIds = new ArrayList<>();
            commonAuditIds.addAll(resultDoList.stream().filter(audit -> StringUtils.isBlank(audit.getKpiItemId())).map(empId -> empId.getScorerId()).collect(Collectors.toList()));

            // 查询完成值附件
            List<PerfEvaluateTaskFile> perfEvaluateTaskFiles = taskKpiItemDao.listTaskUserFile(taskUserId);
            Map<String, List<PerfEvaluateTaskFile>> itemFileMap = null;
            if (CollUtil.isNotEmpty(perfEvaluateTaskFiles)) {
                itemFileMap = perfEvaluateTaskFiles.stream().collect(Collectors.groupingBy(PerfEvaluateTaskFile::getKpiItemId));
            }
            for (EmpEvalDetailKpiItemPo item : items) {
                EvalKpi kpiItem = evalMerge.getKpiTypes().getKpiItem(item.getKpiItemId());//维度删了,但对应的指标没有删除
                if (kpiItem == null) {
                    log.error("kpiItem is null id ={}", item.getKpiItemId());
                }
                item.setInputEmps(kpiItem.getInputEmps());
                item.setItemTags(kpiItem.getItemTags());
                //替换 initKpiItemUsed
                item.setFieldValueList(kpiItem.getFieldValueList());
                item.setFormulaFieldList(kpiItem.getFormulaFields());
                item.initFinishValueSource();

                List<String> finishedValueAuditIds = new ArrayList<>();
                finishedValueAuditIds.addAll(commonAuditIds);
                if (auditDoMap != null && auditDoMap.get(item.getKpiItemId()) != null) {
                    finishedValueAuditIds.addAll(auditDoMap.get(item.getKpiItemId()).stream().map(PerfEvaluateTaskScoreResultDo::getScorerId).collect(Collectors.toList()));
                }
                item.setFinishedValueAuditIds(finishedValueAuditIds);
                //item.setInputChangeRecord(listItemFinishComment(companyId, taskUserId, item.getKpiItemId()));
                item.setInputChangeRecord(kpiItem.getInputChangeRecord());
                if (itemFileMap != null && itemFileMap.get(item.getKpiItemId()) != null) {
                    item.setItemFiles(itemFileMap.get(item.getKpiItemId()));
                }
            }
            Map<String, List<EmpEvalDetailKpiItemPo>> itemMap = items.stream()
                    .collect(Collectors.groupingBy(EmpEvalDetailKpiItemPo::getKpiTypeId));
            List<OkrGoal> goals = builder.getGoals();
            for (EmpDetailKpiTypePo kpiType : kpiTypes) {
                if (Boolean.valueOf(kpiType.getIsOkr())) {
                    kpiType.setOkrGoals(goals);//先放入所有维度
                }
                List<EmpEvalDetailKpiItemPo> itemGroup = itemMap.get(kpiType.getKpiTypeId());
                String isTypeLocked = JSONUtil.toJsonStr(kpiType.getLockedItems());
                kpiType.setIsTypeLocked(isTypeLocked);
                kpiType.setTaskKpiList(itemGroup);
                if (CollUtil.isEmpty(itemGroup)) {
                    continue;
                }
                for (EmpEvalDetailKpiItemPo item : itemGroup) {
                    item.setIsTypeLocked(isTypeLocked);
                }
            }

            kpiTypes.sort(Comparator.comparing(EmpDetailKpiTypePo::typeOrder));
            return kpiTypes;
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    public List<OkrGoal> listOkrGoals(String companyId, String taskUserId) {
        ComQB comQB = ComQB.build(OkrGoalDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("is_deleted", "false");
        List<OkrGoal> goals = domainDao.listAllDomain(comQB, OkrGoal.class);
        return goals;
    }

    public ListWrap<OkrGoal> listOkrGoal(String companyId, List<String> taskUserIds, ListWrap<EvalRefOkrDo> refOkrs) {
        if (refOkrs.isEmpty()) {
            return new ListWrap<OkrGoal>().groupBy(OkrGoal::getTaskUserId);
        }

        ComQB comQB2 = ComQB.build(OkrGoalDo.class)
                .whereEqReq("company_id", companyId)
                .whereInReq("task_user_id", taskUserIds)
                .whereEq("is_deleted", "false");
        List<OkrGoal> goals = domainDao.listAllDomain(comQB2, OkrGoal.class);
        ListWrap<OkrGoal> goalListWrap = new ListWrap<>(goals).groupBy(OkrGoal::getTaskUserId);
        //关联 kpi的指标id
        goalListWrap.getGroups().forEach((taskUserId, okrGoals) -> {
            log.info("当前处理taskUserId = {}", taskUserId);
            List<EvalRefOkrDo> refs = refOkrs.groupGet(taskUserId);
            ListWrap<EvalRefOkrDo> map = new ListWrap<>(refs).asMap(evalRefOkr -> evalRefOkr.getActionId());
            for (OkrGoal okrGoal : okrGoals) {
                for (OkrKeyResult keyResult : okrGoal.getKeyResults()) {
                    EvalRefOkrDo evalRefOkr = map.mapGet(keyResult.getKeyResultId());
                    if (Objects.isNull(evalRefOkr)) {
                        continue;
                    }
                    keyResult.setKpiItemId(evalRefOkr.getTaskKpiId());
                }
            }
        });
        return goalListWrap;
    }

    public OkrGoal getOkrGoal(String companyId, String taskUserId, String exGoalId) {
        ComQB comQB = ComQB.build(OkrGoalDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("objectiveId", exGoalId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("is_deleted", "false");
        OkrGoal goals = domainDao.findDomain(comQB, OkrGoal.class);
        return goals;
    }

    /**
     * 获取所有驳回的指标
     *
     * @param tenantId
     * @param empEvalId
     * @return
     */
    public List<EvalKpi> listRejectKpiItem(String tenantId, String empEvalId, Integer finalSubmitFinishValue) {
        ComQB kpiQB = ComQB.build(PerfEvaluateTaskKpiDo.class, "item")
                .whereEqReq("company_id", tenantId)
                .whereEqReq("task_user_id", empEvalId)
                .whereEqReq("finish_value_audit_status", 2) // 审核驳回的指标
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        if (finalSubmitFinishValue != null) {
            kpiQB.whereEq("final_submit_finish_value", finalSubmitFinishValue);
        }
        return domainDao.listAllDomain(kpiQB, EvalKpi.class);
    }

    public List<EvalKpi> listKpiItemByIds(List<String> ids) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskKpiDo.class)
                .whereIn("id", ids);
        return domainDao.listAllDomain(comQB, EvalKpi.class);
    }

    public List<KpiTypePo> listKpiType(String companyId, List<String> taskUserIds) {
        ComQB comQB = ComQB.build(EmpEvalKpiTypeDo.class)
                .clearSelect()
                .select("task_user_id, kpi_type_name, type_level")
                .setRsType(KpiTypePo.class)
                .whereInReq("task_user_id", taskUserIds)
                .whereEqReq("is_deleted", Boolean.FALSE.toString())
                .whereEq("company_id", companyId);
        return domainDao.listAll(comQB);
    }

    public List<EvalKpi> listItemByTaskUserIds(String companyId, List<String> taskUserIds, String opEmpId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskKpiDo.class)
                .whereEqReq("company_id", companyId)
                .whereInReq("task_user_id", taskUserIds)
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        List<EvalKpi> items = domainDao.listAllDomain(comQB, EvalKpi.class);
        if (CollUtil.isEmpty(items)) {
            return items;
        }
        ListWrap<PerfEvaluateTaskFile> fileListWrap = listItemFileByTaskUserIds(companyId, taskUserIds);
        ListWrap<InputFinishValCache> cacheListWrap = listInputFinishValCacheByTaskUserIds(companyId, taskUserIds, opEmpId);
        ListWrap<InputFinishValCache> importWrap = listImportFinishValCacheByTaskUserIds(companyId, taskUserIds, opEmpId);
        items.forEach(item -> {
            String key = item.getTaskUserId() + "-" + item.getKpiItemId();
            List<PerfEvaluateTaskFile> files = fileListWrap.groupGet(key);
            if (CollUtil.isNotEmpty(files)) {
                item.setFiles(JSONUtil.toJsonStr(files));
            }
            InputFinishValCache importCache = importWrap.mapGet(key);
            item.buildCacheFinishValue(cacheListWrap.mapGet(key),importCache);
        });
        return items;
    }


    public ListWrap<PerfEvaluateTaskFile> listItemFileByTaskUserIds(String companyId, List<String> taskUserIds) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskFileDo.class)
                .whereEqReq("company_id", companyId)
                .whereInReq("task_user_id", taskUserIds)
                .whereEq("is_deleted", "false")
                .orderByDesc("created_time");
        List<PerfEvaluateTaskFile> taskFiles = domainDao.listAllDomain(comQB, PerfEvaluateTaskFile.class);
        ListWrap<PerfEvaluateTaskFile> fileListWrap = new ListWrap<>(taskFiles).groupBy(file -> file.getTaskUserId() + "-" + file.getKpiItemId());
        return fileListWrap;
    }

//    public ListWrap<InputFinishValCache> listInputFinishValCacheByTaskUserIds(String companyId, List<String> taskUserIds, String opEmpId) {
//        ComQB comQB = ComQB.build(InputFinishValCacheDo.class, "im")
//                .leftJoin(ImportFinishValCacheDo.class, "ip").appendOn(" ip.kpi_item_id= im.kpi_item_id  and ip.task_user_id=im.task_user_id and ip.is_deleted='false'")
//                .clearSelect().select("im.id,im.company_id,im.task_user_id,im.operate_emp_id,im.task_kpi_id,im.kpi_type_id,im.kpi_item_id")
//                .select("im.is_deleted,im.created_user,im.created_time,im.updated_user,im.updated_time,im.version")
//                .select("ifnull(im.item_finish_value,ip.item_finish_value) item_finish_value")
//                .select("ifnull(im.work_item_finish_value,ip.work_item_finish_value) work_item_finish_value")
//                .select("ifnull(im.finish_value_comment,ip.finish_value_comment) finish_value_comment")
//                .select("ifnull(im.item_finish_value_text,ip.item_finish_value_text) item_finish_value_text")
//                .select("ifnull(im.files,ip.files) files")
//                .whereEqReq("im.company_id", companyId)
//                .whereInReq("im.task_user_id", taskUserIds)
//                .whereEq("im.operate_emp_id", opEmpId)
//                .whereEq("im.is_deleted", "false")
//                .orderByDesc("im.created_time");
//        List<InputFinishValCache> caches = domainDao.listAllDomain(comQB, InputFinishValCache.class);
//        ListWrap<InputFinishValCache> fileListWrap = new ListWrap<>(caches).asMap(cache -> cache.getTaskUserId() + "-" + cache.getKpiItemId());
//        return fileListWrap;
//    }

    public ListWrap<InputFinishValCache> listImportFinishValCacheByTaskUserIds(String companyId, List<String> taskUserIds, String opEmpId) {
        ComQB comQB = ComQB.build(ImportFinishValCacheDo.class)
                .whereEqReq("company_id", companyId)
                .whereInReq("task_user_id", taskUserIds)
                .whereEq("operate_emp_id", opEmpId)
                .whereEq("is_deleted", "false")
                .orderByDesc("created_time");
        List<InputFinishValCache> caches = domainDao.listAllDomain(comQB, InputFinishValCache.class);
        ListWrap<InputFinishValCache> fileListWrap = new ListWrap<>(caches).asMap(cache -> cache.getTaskUserId() + "-" + cache.getKpiItemId());
        return fileListWrap;
    }

    public ListWrap<InputFinishValCache> listInputFinishValCacheByTaskUserIds(String companyId, List<String> taskUserIds, String opEmpId) {
        ComQB comQB = ComQB.build(InputFinishValCache.class)
                .whereEqReq("company_id", companyId)
                .whereInReq("task_user_id", taskUserIds)
                .whereEq("operate_emp_id", opEmpId)
                .whereEq("is_deleted", "false")
                .orderByDesc("created_time");
        List<InputFinishValCache> caches = domainDao.listAllDomain(comQB, InputFinishValCache.class);
        ListWrap<InputFinishValCache> fileListWrap = new ListWrap<>(caches).asMap(cache -> cache.getTaskUserId() + "-" + cache.getKpiItemId());
        return fileListWrap;
    }

    public PagedList<ExportInputValue> pagedImportFinishValCache(String companyId, String opEmpId, Integer pageNo, Integer pageSize) {
        ComQB evalQB = ComQB.build(ImportFinishValCacheDo.class, "fc")
                .join(PerfEvaluateTaskUserDo.class, "u").appendOn("fc.company_id = u.company_id and fc.task_user_id = u.id")
                .join(PerfEvaluateTaskBaseDo.class, "b").appendOn("u.company_id = b.company_id and u.task_id = b.id")
                .clearSelect()
                .select("u.id as taskUserId,u.company_id, u.task_id,b.cycle_id,u.emp_id,u.emp_name,u.avatar,u.org_id,u.emp_org_name,u.at_org_name_path,u.eval_org_id,u.eval_org_name")
                .select("GROUP_CONCAT(fc.task_kpi_id) as canInputItemKeys,b.task_name")
                .setRsType(ExportInputValue.class)
                .whereEqReq("fc.company_id", companyId)
                .whereEqReq("fc.operate_emp_id", opEmpId)
                .whereEq("fc.is_deleted", "false");
        evalQB.groupBy("fc.task_user_id");
        evalQB.setPage(pageNo, pageSize);
        PagedList<ExportInputValue> pagedList = domainDao.listPage(evalQB);
        if (CollUtil.isEmpty(pagedList.getData())) {
            return pagedList;
        }
        ListWrap<KpiEmp> empWrap = this.listByEmp(new TenantId(companyId), CollUtil.map(pagedList.getData(), list -> list.getEmpId(), true));
        pagedList.getData().forEach(page -> {
            KpiEmp emp = empWrap.mapGet(page.getEmpId());
            page.setDingUserId(Objects.nonNull(emp) ? emp.getExUserId() : null);
            page.setJobnumber(Objects.nonNull(emp) ? emp.getJobnumber() : null);
        });
        List<String> itemKey = pagedList.getData().stream().flatMap(user -> StrUtil.splitTrim(user.getCanInputItemKeys(), ",").stream())
                .collect(Collectors.toList());
        //查询要录入的指标
        ComQB itemQB = ComQB.build(PerfEvaluateTaskKpiDo.class)
                .whereEqReq("company_id", companyId)
                .whereInReq("id", itemKey);
        List<EvalKpiInputValue> items = domainDao.listAllDomain(itemQB, EvalKpiInputValue.class);
        if (CollUtil.isEmpty(items)) {
            return pagedList;
        }
        //查询暂存
        ComQB cacheQB = ComQB.build(ImportFinishValCacheDo.class)
                .whereEqReq("company_id", companyId)
                .whereInReq("task_kpi_id", itemKey)
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        List<ImportFinishValCache> caches = domainDao.listAllDomain(cacheQB, ImportFinishValCache.class);
        Map<String, ImportFinishValCache> map = CollUtil.toMap(caches, new HashMap<>(), c -> c.getTaskKpiId());
        items.forEach(k -> {
            k.buildImportCacheFinishValue(map.get(k.getId()));
        });
        ListWrap<EvalKpiInputValue> kpiWrap = new ListWrap<>(items).groupBy(item -> item.getTaskUserId());
        pagedList.getData().forEach(paged -> {
            paged.repaceOrgNamePath();
            paged.setItems(kpiWrap.groupGet(paged.getTaskUserId()));
        });
        return pagedList;
    }


    public List<ImportFinishValCache> listImportFinishValCache(String companyId, String opEmpId) {
        ComQB comQB = ComQB.build(ImportFinishValCacheDo.class, "fc")
                .whereEqReq("fc.company_id", companyId)
                .whereEqReq("operate_emp_id", opEmpId)
                .whereEq("is_deleted", "false");
        List<ImportFinishValCache> valCaches = domainDao.listAllDomain(comQB, ImportFinishValCache.class);
        if (CollUtil.isEmpty(valCaches)) {
            return valCaches;
        }
        valCaches = CollUtil.filterNew(valCaches, val -> !val.allValueFieldIsNull());
        return valCaches;
    }

    public List<EvalKpi> listNotFinishedValue(String companyId, String kpiItemId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskKpiDo.class)
                .clearSelect()
                .select("task_id,task_user_id,emp_id")
                .setRsType(EvalKpi.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("kpi_item_id", kpiItemId)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereIsNull("item_finish_value");
        return domainDao.listAll(comQB);
    }
}
