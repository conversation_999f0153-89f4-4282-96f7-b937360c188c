package com.polaris.kpi.eval.infr.statics.ppojo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/1/21 10:07
 */
@Data
public class PerfEmpStaticsPo implements Serializable {

    private String id;
    @JSONField(serialize = false)
    private String companyId; //公司ID
    @JSONField(serialize = false)
    private String cycleId; //周期ID
    private String taskId; //任务ID
    private String taskName; //任务名称
    private String taskUserId;
    private Integer performanceType; //是否组织考核
    @JSONField(serialize = false)
    private String empId; //被考核人ID
    private String empName; //被考核人姓名
    private String avatar; //被考核人头像
    private String orgId; //组织id
    private String orgName; //组织名称
    private String evalOrgId; //被考核组织id
    private String evalOrgName; //被考核组织名称
    @JSONField(serialize = false)
    private String atOrgNamePath;
    @JSONField(serialize = false)
    private String atOrgCodePath;
    private BigDecimal finalScore; //考核分
    private String evaluationLevel; //绩效等级
    private Integer evaluationLevelSort; //等级排序
    private String perfCoefficient; //绩效系数
    private Integer levelHeight; //高绩效1 低绩效2

}
