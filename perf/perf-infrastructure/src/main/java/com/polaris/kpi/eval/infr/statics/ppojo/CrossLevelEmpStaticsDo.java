package com.polaris.kpi.eval.infr.statics.ppojo;

import com.polaris.kpi.common.infr.BaseWithCompanyIdData;
import com.polaris.kpi.eval.domain.statics.entity.CrossLevelGroup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.ibatis.annotations.Ckey;
import org.apache.ibatis.annotations.JsonAryColumn;
import org.apache.ibatis.annotations.Table;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/25 15:37
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Table("cross_level_emp_statics")
public class CrossLevelEmpStaticsDo extends BaseWithCompanyIdData {


    /**
     * 唯一标识符，用于唯一标识一条考核数据记录。
     */
    @Ckey
    private String id;

    /**
     * 周期的唯一标识符，用于标识该考核数据所处的考核周期。
     */
    private String cycleId;

    private String taskId;

    /**
     * 任务关联的用户的唯一标识符，与该考核任务相关的用户。
     */
    @Ckey
    private String taskUserId;

    /**
     * 跨级类型，用于表示考核中的跨级情况。
     * 取值为 1 时表示上升，即考核层级向上跨越；
     * 取值为 2 时表示下降，即考核层级向下跨越。
     */
    private Integer crossLevelType;

    /**
     * 标识该考核是否为组织考核。
     * 通常使用特定的字符串值来表示，如 "Y" 表示是组织考核，"N" 表示不是组织考核。
     */
    private Integer performanceType;

    /**
     * 被考核人的唯一标识符，用于唯一标识被考核的员工。
     */
    private String empId;

    /**
     * 被考核人的姓名，记录被考核员工的真实姓名。
     */
    private String empName;

    /**
     * 被考核人的头像信息，可能是头像的存储路径或头像的唯一标识。
     */
    private String avatar;

    /**
     * 组织的唯一标识符，代表该考核数据所关联的组织。
     */
    private String orgId;

    /**
     * 组织的名称，对应 orgId 所代表组织的具体名称。
     */
    private String orgName;

    private String roleNames;

    /**
     * 被考核组织的唯一标识符，若考核对象为组织时使用。
     */
    private String evalOrgId;

    /**
     * 被考核组织的名称，对应 evalOrgId 所代表组织的具体名称。
     */
    private String evalOrgName;

    /**
     * 组织名称的路径信息，可能表示组织在组织架构中的层级路径名称。
     */
    private String atOrgNamePath;

    /**
     * 组织编码的路径信息，可能表示组织在组织架构中的层级路径编码。
     */
    private String atOrgCodePath;

    @JsonAryColumn(CrossLevelGroup.class)
    private List<CrossLevelGroup> perfCrossData;

}
