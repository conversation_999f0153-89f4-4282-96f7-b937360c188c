package com.polaris.kpi.report.infr.query;

import com.alibaba.cola.dto.PageQuery;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/12 17:07
 */
@Data
public class ItemAnalysisQuery extends PageQuery {

    private String companyId;
    private String opEmpId;
    private String cycleId;
    private String orgId;
    private List<String> orgIds;
    private List<String> privOrgIds;    //权限部门
    private Integer performanceType;
    private String adminType;
    private List<String> itemIds;
    private String itemId;
    private String itemName;
    private Integer orgPathHeight = 1;
    private List<String> cycleIds;
    private List<String> taskUserIds;
    private String queryRange; // all/directBelow
    private List<String> empIds;
    private String empId;

    public void accOp(String companyId,String opEmpId,String adminType) {
        this.companyId = companyId;
        this.opEmpId = opEmpId;
        this.adminType = adminType;
    }

    public Boolean singleOrg(){
        return StringUtils.isNotBlank(orgId);
    }

}
