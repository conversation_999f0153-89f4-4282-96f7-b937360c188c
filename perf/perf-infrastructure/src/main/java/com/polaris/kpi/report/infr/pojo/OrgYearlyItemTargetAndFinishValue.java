package com.polaris.kpi.report.infr.pojo;


import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/7/23 11:43
 */
@Setter
@Getter
@NoArgsConstructor
public class OrgYearlyItemTargetAndFinishValue extends BaseItemAnalysisPo{

    private String orgId;
    private String cycleId;
    private Integer cycleValue;
    private String cycleType;
    private String cycleYear;

    public OrgYearlyItemTargetAndFinishValue(Integer cycleValue, String cycleType, String cycleYear) {
        this.cycleValue = cycleValue;
        this.cycleType = cycleType;
        this.cycleYear = cycleYear;
    }

    public void accItemInfo(OrgYearlyItemTargetAndFinishValue subOrgFinishValue) {
        if (this.getItemName()!= null){
            return;
        }
        this.setItemId(subOrgFinishValue.getItemId());
        this.setItemName(subOrgFinishValue.getItemName());
        this.setItemUnit(subOrgFinishValue.getItemUnit());
    }
}
