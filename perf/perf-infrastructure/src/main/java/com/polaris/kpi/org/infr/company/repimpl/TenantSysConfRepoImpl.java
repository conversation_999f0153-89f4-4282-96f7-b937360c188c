package com.polaris.kpi.org.infr.company.repimpl;

import cn.com.polaris.kpi.company.TenantSysConf;
import cn.hutool.core.collection.CollUtil;
import com.polaris.kpi.org.domain.company.repo.TenantSysConfRepo;
import com.polaris.kpi.org.infr.company.ppojo.TenantSysConfDo;
import lombok.extern.slf4j.Slf4j;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class TenantSysConfRepoImpl implements TenantSysConfRepo {

    @Resource
    private DomainDaoImpl domainDao;

    @Override
    public void batchSaveTenantConfs(List<TenantSysConf> confs) {
        if(CollUtil.isEmpty(confs)){
            return;
        }
        for (TenantSysConf conf : confs) {
            saveTenantSys(conf);
        }
    }

    @Override
    public void saveTenantSys(TenantSysConf tenantSysConf) {
        if (!existCompanySysInfo(tenantSysConf.getCompanyId(), tenantSysConf.getConfCode())) {
            tenantSysConf.setCreatedTime(new Date());
            domainDao.add(TenantSysConfDo.class, tenantSysConf);
        } else {
            updateTenantSys(tenantSysConf);
        }
    }

    @Override
    public void updateTenantSys(TenantSysConf tenantSysConf) {
        UpdateBuilder updateBuilder = UpdateBuilder.build("tenant_sys_conf")
                .setBean(tenantSysConf)
                .whereEq("company_id", tenantSysConf.getCompanyId())
                .whereEq("conf_code", tenantSysConf.getConfCode());
        domainDao.update(updateBuilder);
    }

    @Override
    public TenantSysConf getCompanySysInfo(String companyId, String confCode) {
        ComQB qb = ComQB.build(TenantSysConfDo.class, "d")
                .whereEq("company_id", companyId)
                .whereEq("conf_code", confCode);
        TenantSysConf domain = domainDao.findDomain(qb, TenantSysConf.class);
        if (domain == null) {
            return new TenantSysConf(companyId, 0, confCode, "");
        }
        return domain;
    }


    public boolean existCompanySysInfo(String companyId, String confCode) {
        ComQB qb = ComQB.build(TenantSysConfDo.class, "d")
                .whereEq("company_id", companyId)
                .whereEq("conf_code", confCode);
        TenantSysConf domain = domainDao.findDomain(qb, TenantSysConf.class);
        return domain != null;
    }

    public List<String> existCompanyConf(String companyId, List<String> confCodes) {
        ComQB qb = ComQB.build(TenantSysConfDo.class, "d")
                .clearSelect().select("conf_code").setRsType(String.class)
                .whereEq("company_id", companyId)
                .whereIn("conf_code", confCodes);
        List<String> existCodes = domainDao.listAll(qb);
        return existCodes;
    }

    public static List<String> defaultOpens = Arrays.asList("mainAdminSeeAnonymous_20240202","childAdminSeeAnonymous_20240110");

    @Override
    public boolean isOpen(String companyId, String confCode) {
        ComQB qb = ComQB.build(TenantSysConfDo.class, "d")
                .whereEq("company_id", companyId)
                .whereEq("conf_code", confCode);
        TenantSysConf domain = domainDao.findDomain(qb, TenantSysConf.class);
        if (domain == null) {
            if (defaultOpens.contains(confCode)) {
                return true;
            }
            return false;
        }
        if (domain.getOpen() == 0) {
            return false;
        }
        return true;
    }
}
