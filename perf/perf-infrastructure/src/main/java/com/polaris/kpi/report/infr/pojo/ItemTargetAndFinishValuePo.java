package com.polaris.kpi.report.infr.pojo;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.eval.domain.task.entity.empeval.ComputeFinishValueProgress;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/12 22:05
 */
@Data
public class ItemTargetAndFinishValuePo {

    private String itemId;
    private String itemName;
    private String itemUnit;
    private Integer finishValueType;
    private BigDecimal targetValue;
    private BigDecimal finishValue;
    private String finishRate;
    @JSONField(serialize = false)
    private ComputeFinishValueProgress progress;

    public void accProcess(ComputeFinishValueProgress progress){
        this.progress = progress;
    }

    public void computeFinishRate(){
        finishRate = progress.getPercent(targetValue, finishValue, finishValueType);
    }

    public void removeZero(){
        if (StrUtil.isNotBlank(this.finishRate)) {
            BigDecimal value = new BigDecimal(this.finishRate);
            this.finishRate = value.stripTrailingZeros().toPlainString();
        }
    }

}
