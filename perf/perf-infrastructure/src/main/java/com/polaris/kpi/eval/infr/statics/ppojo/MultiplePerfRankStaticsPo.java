package com.polaris.kpi.eval.infr.statics.ppojo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/1/21 10:07
 */
@Data
public class MultiplePerfRankStaticsPo {

    private Integer assessTaskCount; //考核任务数量
    private Integer finishTaskCount; //考核完成数量
    private String empId; //被考核人ID
    private String empName; //被考核人姓名
    private String avatar; //被考核人头像
    private String orgId; //组织id
    private String orgName; //组织名称
    private String evalOrgId; //被考核组织id
    private String evalOrgName; //被考核组织名称
    private BigDecimal totalScore; //考核总分
    private String highestEvalLevel; //最高绩效等级
    private Integer rank;
    private boolean leaved;

    /**
     * 考核时员工所在部门名字以 | 连接的路径信息，可展示员工在考核时所属的部门层级关系。
     */
    private String atOrgNamePath;

    /**
     * 考核时员工所在部门 codePath 以 | 连接的路径信息，与部门名称路径对应，可能用于系统内部的部门编码标识。
     */
    private String atOrgCodePath;
}
