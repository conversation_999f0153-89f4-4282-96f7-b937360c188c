package com.polaris.kpi.eval.infr.temp.dao;

import cn.com.polaris.kpi.*;
import cn.com.polaris.kpi.company.TenantSysConf;
import cn.com.polaris.kpi.company.TenantSysConfEnum;
import cn.com.polaris.kpi.eval.KpiItemUsedField;
import cn.com.polaris.kpi.eval.KpiTypeUsedField;
import cn.com.polaris.kpi.eval.StaffConfItem;
import cn.com.polaris.kpi.temp.TempId;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.perf.www.common.constant.BusinessConstant;
import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import com.perf.www.common.em.EvaluateTypeEnum;
import com.perf.www.common.utils.bean.Convert;
import com.perf.www.common.utils.string.StringTool;
import com.perf.www.domain.entity.company.EmployeeBaseInfoModel;
import com.perf.www.domain.entity.company.RoleModel;
import com.perf.www.model.kpi.CompanyCategoryModel;
import com.perf.www.model.kpi.CompanyKpiItemModel;
import com.polaris.acl.dept.type.AdminType;
import com.polaris.kpi.eval.domain.temp.entity.*;
import com.polaris.kpi.eval.infr.task.ppojo.CompanyKpiItemDo;
import com.polaris.kpi.eval.infr.task.ppojo.PerfItemRefPointDetailDo;
import com.polaris.kpi.eval.infr.temp.repimpl.StdTempRepoImpl;
import com.polaris.kpi.eval.infr.temp.query.TempQuery;
import com.polaris.kpi.org.infr.company.ppojo.TenantSysConfDo;
import com.polaris.kpi.org.infr.emp.pojo.EmpOrganizationDo;
import com.polaris.kpi.org.infr.emp.pojo.EmployeeBaseInfoDo;
import com.polaris.kpi.org.infr.emp.pojo.EmpRefOrgDo;
import com.polaris.kpi.org.infr.emp.pojo.SystemAdminSetDo;
import com.polaris.sdk.type.TenantId;
import com.perf.www.model.perftmp.*;
import com.polaris.kpi.eval.infr.temp.ppojo.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.NativeSQLBuilder;
import org.lufei.ibatis.builder.QueryBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.lufei.ibatis.mapper.PagedList;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * TODO 查询还得再处理
 *
 * <AUTHOR> lufei
 * @date 2022/1/20 5:49 下午
 */
@Component
public class PerfTempDao {

    @Autowired
    private DomainDaoImpl domainDao;

    public boolean existName(ExamGroup model) {
        final ComQB existQ = ComQB.build(PerfTemplBaseDo.class, "t")
                .whereEq("companyId", model.getCompanyId().getId())
                .whereEq("name", model.getName())
                .whereNotEq("id", model.getId())
                .whereEq("is_deleted", "false")
                .limit(0, 1);
        final PerfTemplBaseDo one = domainDao.findOne(existQ);
        return one != null;
    }

    public Map<String, List<AutoStartTempDo>> matchPostExamGroup(TenantId tenantId, String postId, List<String> empOrgIds) {
        Map<String, List<AutoStartTempDo>> tmpMap = new HashMap<>();
        if (CollUtil.isEmpty(empOrgIds)) {
            return tmpMap;
        }
        ComQB orgComQb = ComQB.build(EmpOrganizationDo.class, "s")
                .join(EmpOrganizationDo.class, "p")
                .appendOn("s.company_id=p.company_id and instr(s.org_code,p.org_code) ")
                .clearSelect().select("p.org_id  ")
                .setRsType(String.class)
                .whereIn("s.org_id", empOrgIds)
                .whereEq("p.company_id", tenantId.getId())
                .appendWhere("s.status='valid'");
        List<String> empAtParentOrgIds = domainDao.listAll(orgComQb);
        //empAtParentOrgIds.removeAll(empOrgIds);
        if (StrUtil.isNotBlank(postId)) {
            //扫描一次不包含子部门的
            loadMatchedPostTemp(tenantId, tmpMap, empOrgIds, postId, 0);
            //扫描一次包含子部门的
            loadMatchedPostTemp(tenantId, tmpMap, empAtParentOrgIds, postId, 1);
            //匹配了职级的话,就不再匹配部门了
            if (CollUtil.isNotEmpty(tmpMap)) {
                return tmpMap;
            }
        }
        //加载直接部门的模板 不匹配配置执行一次
        loadMatchedOrgsTemp(tenantId, tmpMap, empOrgIds, 0);
        //加载父级部门的模板 匹配子部门执行一次
        loadMatchedOrgsTemp(tenantId, tmpMap, empAtParentOrgIds, 1);
        return tmpMap;
    }

    private Map<String, List<AutoStartTempDo>> loadMatchedPostTemp(TenantId tenantId, Map<String, List<AutoStartTempDo>> tmpMap, List<String> empAtParentOrgIds, String postId, int matchChild) {
        empAtParentOrgIds.forEach(orgId -> {
            final ComQB qb = ComQB.build(PerfTemplBaseDo.class, "p")
                    .join(PerfTemplEvaluateInitiateDo.class, "i").appendOn("p.company_id=i.company_id and p.id  =i.templ_base_id  ")
                    .clearSelect().select("p.id,p.name,p.onboard_cycle_cnt,p.onboard_cycle_unit,i.initiator_list").setRsType(AutoStartTempDo.class)
                    .whereEq("p.company_id", tenantId.getId())
                    .whereEq("p.isNewEmp", 1)
                    .whereEq("p.matchChildOrg", matchChild)
                    .whereEqReq("p.post", postId)//匹配带职级的
                    .whereEq("p.status", "published")
                    .whereEq("p.onboard_auto_start", "1")
                    .appendWhere("p.is_deleted='false'")
                    .whereLike("p.new_org_ids", orgId);//同时匹配部门
            List<AutoStartTempDo> initPage = domainDao.listAll(qb);
            addToTmpMap(tmpMap, orgId, initPage);
        });
        return tmpMap;
    }

    private void loadMatchedOrgsTemp(TenantId tenantId, Map<String, List<AutoStartTempDo>> tmpMap, List<String> empAtParentOrgIds, int matchChild) {

        empAtParentOrgIds.forEach(orgId -> {
            final ComQB qb = ComQB.build(PerfTemplBaseDo.class, "p")
                    .join(PerfTemplEvaluateInitiateDo.class, "i").appendOn("p.company_id=i.company_id and p.id  =i.templ_base_id  ")
                    .clearSelect().select("p.id,p.name,p.onboard_cycle_cnt,p.onboard_cycle_unit,i.initiator_list").setRsType(AutoStartTempDo.class)
                    .whereEq("p.company_id", tenantId.getId())
                    .whereEq("p.isNewEmp", 1)
                    .whereEq("p.matchChildOrg", matchChild)
                    .whereEq("p.status", "published")
                    .whereEq("p.onboard_auto_start", "1")
                    .appendWhere("p.is_deleted='false'")
                    .appendWhere("p.post ='' ")//不匹配带职级的
                    .whereLike("p.new_org_ids", orgId);//仅匹配部门
            List<AutoStartTempDo> initPage = domainDao.listAll(qb);
            addToTmpMap(tmpMap, orgId, initPage);
        });
    }

    private void addToTmpMap(Map<String, List<AutoStartTempDo>> tmpMap, String orgId, List<AutoStartTempDo> initPage) {
        if (tmpMap.get(orgId) == null) {
            tmpMap.put(orgId, new ArrayList<>());
        }
        tmpMap.get(orgId).addAll(initPage);
    }

    public List<PerfTemplEvaluateInitiatePo> matchPostTempInitor(TenantId tenantId, List<String> orgIds) {
        List<PerfTemplEvaluateInitiatePo> initiateDos = new ArrayList<>();
        if (CollUtil.isEmpty(orgIds)) {
            return initiateDos;
        }
        orgIds.forEach(orgId -> {
            final ComQB qb = ComQB.build(PerfTemplEvaluateInitiateDo.class, "i")
                    .join(PerfTemplBaseDo.class, "p").appendOn("i.company_id =p.company_id and i.templ_base_id = p.id ")
                    .clearSelect().select("i.*,p.name").setRsType(PerfTemplEvaluateInitiatePo.class)
                    .whereEq("i.company_id", tenantId.getId())
                    .whereEq("p.isNewEmp", 1)
                    .whereEq("p.status", "published")
                    .whereLike("p.new_org_ids", orgId)
                    .orderByDesc("p.created_time");
            List<PerfTemplEvaluateInitiatePo> initPage = domainDao.listAll(qb);
            initiateDos.addAll(initPage);
        });
        return initiateDos;
    }

    public List<ExamGroup> listOnbardingTemp(TenantId tenantId) {
        ComQB qb = ComQB.build(PerfTemplBaseDo.class, "p")
                .whereEqReq("p.company_id", tenantId.getId())
                .whereEq("p.isNewEmp", 1)
                .whereEq("p.status", "published");
        List<ExamGroup> temps = domainDao.listAllDomain(qb, ExamGroup.class);
        return temps;
    }

    public ExamGroup getOnbardingTemp(TenantId tenantId, String tempId) {
        ComQB qb = ComQB.build(PerfTemplBaseDo.class, "p")
                .whereEqReq("p.company_id", tenantId.getId())
                .whereEq("p.isNewEmp", 1)
                .whereEq("p.id", tempId);
        return domainDao.findDomain(qb, ExamGroup.class);
    }

    public ExamGroup getExamGroup(TenantId tenantId, TempId tmpId, int isNewEmp) {
        ExamGroup tempBase = getTempBase(tenantId, tmpId);
        appendTemplKpi(tempBase); //指标
        appendEvaluateInitiate(tempBase, isNewEmp);
        appendEvaluateAffirm(tempBase);
        appendExecute(tempBase);
        appendItemPointRule(tempBase);
        appendEvaluate(tempBase); //评分流程配置
        return tempBase;
    }

    public ExamGroup getTempBase(TenantId tenantId, TempId tmpId) {
        final ComQB qb = ComQB.build(PerfTemplBaseDo.class)
                .whereEq("company_id", tenantId.getId())
                .whereEq("id", tmpId.getId());
        return domainDao.findDomain(qb, ExamGroup.class);
    }

    private void appendTemplKpi(ExamGroup tempBase) {
        TenantId companyId = tempBase.getCompanyId();
        TempId tempBaseId = tempBase.tmpId();
        List<PerfTemplKpiType> kpiTypes = queryKpiTypeList(tempBaseId);
        /**查询纬度字定义配置*/
        List<KpiTypeUsedField> kpiTypeUsedFieldList = queryKpiTypeUsedList(tempBaseId);
        tempBase.initKpiTypes(kpiTypes, kpiTypeUsedFieldList);
        List<PerfTemplKpiItem> items = queryKpiItemByBaseId(tempBaseId);
        /**查询指标字定义*/
        List<KpiItemUsedField> itemUsedFieldDos = queryKpiItemUsedByBaseId(tempBaseId);
        //查模板关联的公式字段
        List<PerfTemplFormulaField> formulaFieldList = queryTempFormulaField(companyId, tempBaseId);
        Map<String, List<PerfTemplFormulaField>> itemFieldMap = formulaFieldList.stream()
                .collect(Collectors.groupingBy(PerfTemplFormulaField::getKpiItemId));
        items.forEach(item -> {
            if (BusinessConstant.SCORER_TYPE_AUTO.equals(item.getScorerType())) {
                item.setFormulaFieldList(itemFieldMap.get(item.getKpiItemId()));
            }
            item.initKpiItemUsed(item, itemUsedFieldDos);
        });
        LoggerFactory.getLogger(getClass()).debug("queryTemplKpiByBaseId调用 {}", tempBase.getEvaluateType());
        if (tempBase.isCustom()) { //设置自定义评分详情
            setTempCustomEvaluate(tempBaseId, kpiTypes, items);
        }
        Map<String, List<PerfTemplKpiItem>> itemMap = items.stream().collect(Collectors.groupingBy(PerfTemplKpiItem::getKpiTypeId));
        kpiTypes.forEach(t -> {
            t.setItems(itemMap.get(t.getTypeId()));

        });
    }

    private List<PerfTemplKpiType> queryKpiTypeList(TempId tempBaseId) {
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfTemplKpiType.class)
                .setSql("SELECT t.*,e.`name` as createdUserName,i.self_score_flag,i.mutual_score_flag,i.superior_score_flag,i.appoint_score_flag,d.is_temporary " +
                        " FROM perf_templ_kpi_type t " +
                        "LEFT JOIN employee_base_info e on e.employee_id = t.created_user " +
                        "LEFT JOIN perf_templ_item_evaluate i on t.type_id = i.kpi_type_id and i.is_deleted = 'false' AND i.templ_base_id = t.templ_base_id " +
                        "LEFT JOIN company_dic d on t.type_id = d.id and d.is_deleted = 'false' " +
                        "WHERE t.is_deleted = 'false' AND t.templ_base_id = #{tempBaseId} ORDER BY t.order ASC ");
        sqlBuilder.setValue("tempBaseId", tempBaseId.getId());
        return domainDao.listAll(sqlBuilder);
    }

    private List<KpiTypeUsedField> queryKpiTypeUsedList(TempId tempBaseId) {
        /**查询维度配置*/
        ComQB used = ComQB.build(PerfTemplTypeUsedFieldDo.class, "tu").setRsType(KpiTypeUsedField.class)
                .whereEq("templ_base_id", tempBaseId.getId())
                .appendWhere("is_deleted='false'")
                .orderByAsc("sort");
        return domainDao.listAll(used);
    }


    // private List<PerfTemplKpiType> findTempDic(TenantId companyId, TempId tempBaseId) {
    //     ComQB comQB = ComQB.build(CompanyDicModel.class)
    //             .whereEq("is_deleted", "false")
    //             .whereEq("sys_dic_type", SysDicTypeEnum.KPI_TYPE.getType())
    //             .whereEq("company_id", companyId.getId())
    //             .whereEq("is_temporary", "true")
    //             .whereEq("is_default", "true")
    //             .limit(0, 1);
    //     CompanyDicModel defaultKpiType = domainDao.findOne(comQB);
    //     PerfTemplKpiType kpiType =
    //             new PerfTemplKpiType(companyId.getId(), defaultKpiType.getId(), tempBaseId.getId(), defaultKpiType.getDicValue());
    //     return Collections.singletonList(kpiType);
    // }

    private List<PerfTemplKpiItem> queryKpiItemByBaseId(TempId tempBaseId) {
        ComQB itemEvaluateQB = ComQB.build(PerfTemplItemEvaluateDo.class)
                .whereEq("is_deleted", "false")
                .whereEq("templ_base_id", tempBaseId.getId());
        ComQB comQB = ComQB.build(PerfTemplKpiItemDo.class, "i")
                .leftJoin(CompanyKpiItemModel.class, "c").on("i.kpi_item_id", "c.id")
                .leftJoin(CompanyCategoryModel.class, "g").on("c.category_id", "g.id")
                .leftJoin(EmployeeBaseInfoModel.class, "e").on("e.employee_id", "c.created_user")
                .leftJoinQ(itemEvaluateQB, "ie").on("ie.kpi_item_id", "i.kpi_item_id")
                .whereEq("i.is_deleted", "false")
                .whereEq("i.templ_base_id", tempBaseId.getId());
        comQB.clearSelect().select("i.*,g.category_name,ifnull(i.kpi_item_name,c.item_name) as itemName,e.name as createdUserName," +
                "c.is_temporary,c.item_plan_value,ie.self_score_flag,ie.mutual_score_flag,ie.superior_score_flag,ie.appoint_score_flag,ie.mutual_user_type,ie.mutual_user_value," +
                "(SELECT GROUP_CONCAT(t.tag_name) FROM company_item_rel_tag r LEFT JOIN company_tag t on r.tag_id = t.id WHERE r.company_item_id = i.kpi_item_id AND r.is_deleted = 'false' AND t.is_deleted = 'false') as tagNames ");
        comQB.setRsType(PerfTemplKpiItem.class);
        comQB.orderByAsc("i.order");
        return domainDao.listAll(comQB);
    }

    private List<KpiItemUsedField> queryKpiItemUsedByBaseId(TempId tempBaseId) {
        /**查询指标配置*/
        ComQB itemUsed = ComQB.build(PerfTemplItemUsedFieldDo.class, "tu").setRsType(KpiItemUsedField.class)
                .whereEq("templ_base_id", tempBaseId.getId())
                .appendWhere("is_deleted='false'")
                .orderByAsc("sort");
        return domainDao.listAll(itemUsed);
    }

    private List<PerfTemplFormulaField> queryTempFormulaField(TenantId companyId, TempId tempId) {
        ComQB comQB = ComQB.build(PerfTemplFormulaFieldDo.class)
                .whereEq("is_deleted", "false")
                .whereEq("company_id", companyId.getId())
                .whereEq("templ_base_id", tempId.getId());
        return domainDao.listAllDomain(comQB, PerfTemplFormulaField.class);
    }

    private void setTempCustomEvaluate(TempId tempId, List<PerfTemplKpiType> typeVOS, List<PerfTemplKpiItem> items) {
        List<PerfTemplItemEvaluate> itemEvaluateModels = queryTempItemEvaluates(tempId, false);
        if (CollectionUtils.isEmpty(itemEvaluateModels)) {
            return;
        }
        //查审核人
        List<PerfTemplEvaluateAudit> auditList = queryAuditByScenes(tempId, null, null, null);

        //指标分类自定义流程配置
        List<PerfTemplItemEvaluate> typeEvaluateList = itemEvaluateModels.stream()
                .filter(l -> StrUtil.isNotBlank(l.getKpiTypeId()) && StrUtil.isBlank(l.getKpiItemId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(typeEvaluateList)) {
            Map<String, PerfTemplItemEvaluate> typeEvaluateMap = typeEvaluateList.stream().collect(Collectors.toMap(PerfTemplItemEvaluate::getKpiTypeId, Function.identity(), (a, b) -> b));
            for (PerfTemplKpiType typeVO : typeVOS) {
                PerfTemplItemEvaluate typeEvaluate = typeEvaluateMap.get(typeVO.getTypeId());
                if (Objects.isNull(typeEvaluate)) {
                    continue;
                }
                PerfTemplItemEvaluate res = Convert.convertOnlyMatch(typeEvaluate, PerfTemplItemEvaluate.class);
                List<PerfTemplEvaluateAudit> typeAuditList = auditList.stream().filter(l -> typeVO.getTypeId().equals(l.getKpiTypeId())
                        && StringUtils.isBlank(l.getKpiItemId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(typeAuditList)) {
                    // res.initAllAudit(typeAuditList);
                    StdTempRepoImpl.matchItemAudit(res, typeAuditList);
                }
                typeVO.customItemEvaluate(res);
            }
        }

        //指标自定义流程配置
        List<PerfTemplItemEvaluate> itemEvaluateList = itemEvaluateModels.stream().filter(l -> StrUtil.isBlank(l.getKpiTypeId()) && StrUtil.isNotBlank(l.getKpiItemId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(itemEvaluateList)) {
            Map<String, PerfTemplItemEvaluate> itemEvaluateMap = itemEvaluateList.stream().collect(Collectors.toMap(PerfTemplItemEvaluate::getKpiItemId, Function.identity(), (a, b) -> b));
            for (PerfTemplKpiItem itemVO : items) {
                PerfTemplItemEvaluate itemEvaluate = itemEvaluateMap.get(itemVO.getKpiItemId());
                if (Objects.isNull(itemEvaluate)) {
                    continue;
                }
                PerfTemplItemEvaluate res = Convert.convertOnlyMatch(itemEvaluate, PerfTemplItemEvaluate.class);
                List<PerfTemplEvaluateAudit> itemAuditList = auditList.stream().filter(l -> itemVO.getKpiItemId().equals(l.getKpiItemId()) && StrUtil.isBlank(l.getKpiTypeId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(itemAuditList)) {
                    StdTempRepoImpl.matchItemAudit(res, itemAuditList);
                }
                itemVO.setItemEvaluate(res);
            }
        }
    }

    private List<PerfTemplItemEvaluate> queryTempItemEvaluates(TempId tempId, boolean isItemNotNull) {
        ComQB queryBuilder = ComQB.build(PerfTemplItemEvaluateDo.class)
                .whereEq("is_deleted", "false")
                .whereEq("templ_base_id", tempId.getId());
        if (isItemNotNull) {
            queryBuilder.appendWhere("(kpi_item_id is not null or kpi_type_id is not null)");
        }
        return domainDao.listAllDomain(queryBuilder, PerfTemplItemEvaluate.class);
    }

    public List<PerfTemplEvaluateAudit> queryAuditByScenes(TempId tempBaseId, List<String> scenes, String kpiItemId, String kpiTypeId) {
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfTemplEvaluateAudit.class)
                .setSql("SELECT a.*,e1.`name` as taskEmpName,IFNULL(a.approver_emp_name,e2.name) as approverEmpName FROM perf_templ_evaluate_audit a  " +
                        "LEFT JOIN employee_base_info e1 on a.company_id = e1.company_id and a.emp_id = e1.employee_id " +
                        "LEFT JOIN employee_base_info e2 on a.company_id = e2.company_id and a.approver_info = e2.employee_id " +
                        // "LEFT JOIN role r on r.id = a.approver_info " +
                        "WHERE a.is_deleted = 'false' AND a.temp_base_id = #{tempBaseId} ");
        sqlBuilder.setValue("tempBaseId", tempBaseId.getId());
        sqlBuilder.appendIfOpt("AND a.kpi_item_id = #{kpiItemId}", "kpiItemId", kpiItemId);
        sqlBuilder.appendIfOpt("AND a.kpi_type_id = #{kpiTypeId}", "kpiTypeId", kpiTypeId);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(scenes)) {
            sqlBuilder.append("and a.scene in  ").append(StringTool.getInStr(scenes).toString());
        }
        sqlBuilder.append("order by a.scene asc, a.approval_order asc ");
        List<PerfTemplEvaluateAudit> list = domainDao.listAll(sqlBuilder);
        return list;
    }

    public List<PerfTemplEvaluateAudit> listCustomerDefFlow(TempId tempBaseId, List<String> scenes) {
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfTemplEvaluateAudit.class)
                .setSql("SELECT a.*,e1.`name` as taskEmpName,IFNULL(a.approver_emp_name,e2.name) as approverEmpName FROM perf_templ_evaluate_audit a  " +
                        "LEFT JOIN employee_base_info e1 on a.company_id = e1.company_id and a.emp_id = e1.employee_id " +
                        "LEFT JOIN employee_base_info e2 on a.company_id = e2.company_id and a.approver_info = e2.employee_id " +
                        "WHERE a.is_deleted = 'false' AND a.temp_base_id = #{tempBaseId} ");
        sqlBuilder.setValue("tempBaseId", tempBaseId.getId());
        sqlBuilder.append("AND (a.kpi_item_id = 's3') ");
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(scenes)) {
            sqlBuilder.append("and a.scene in  ").append(StringTool.getInStr(scenes).toString());
        }
        sqlBuilder.append("order by a.scene asc, a.approval_order asc ");
        List<PerfTemplEvaluateAudit> list = domainDao.listAll(sqlBuilder);
        return list;
    }

    private Map<String, RoleModel> listRoleByIds(String companyId, List<String> roleIds) {
        if (CollUtil.isEmpty(roleIds)) {
            return Collections.emptyMap();
        }
        ComQB<RoleModel> comQB = ComQB.build(RoleModel.class, "r")
                .whereEq("r.company_id", companyId)
                .whereIn("r.id", roleIds)
                .whereEq("r.is_deleted", "false");
        final List<RoleModel> roleModels = domainDao.listAll(comQB);
        final Map<String, RoleModel> roleMap = roleModels.stream().collect(Collectors.toMap(k -> k.getId(), v -> v));
        return roleMap;
    }

    public PerfTemplEvaluateInitiate appendEvaluateInitiate(ExamGroup examGroup, int isNewEmp) {
        PerfTemplEvaluateInitiate initiateModel = findInitiate(examGroup.tmpId(), isNewEmp);
        examGroup.appendInitiate(initiateModel);
        if (Objects.isNull(initiateModel)) {
            return null;
        }

        initiateModel.setTaskDesc(examGroup.getTemplDesc());
        if (StringUtils.isEmpty(initiateModel.getEnableAddReduce()) && examGroup.isCustom()) {
            //兼容历史数据 自定义流程不能控制增减指标
            initiateModel.setEnableAddReduce(BusinessConstant.FALSE);
        }
        //查是否全部是OKR类别
        List<PerfTemplKpiTypeModel> typeModels = listKpiType(examGroup.getCompanyId(), examGroup.tmpId());
        if (typeModels.stream().filter(l -> !BusinessConstant.TRUE.equals(l.getIsOkr())).findFirst().isPresent()) {
            initiateModel.setIsAllOkrType(BusinessConstant.FALSE);
        } else {
            initiateModel.setIsAllOkrType(BusinessConstant.TRUE);
        }
        //是否全部是空类别
        if (typeModels.stream().filter(l -> !BusinessConstant.TRUE.equals(l.getIsEmptyType())).findFirst().isPresent()) {
            initiateModel.setIsAllEmptyType(BusinessConstant.FALSE);
        } else {
            initiateModel.setIsAllEmptyType(BusinessConstant.TRUE);
        }

        //下面都是查询已禁用的被考核人
        List<String> empIds = new ArrayList<>();
        List<StaffConfItem> staffVOS = initiateModel.getExamineObjData();
        if (CollectionUtils.isEmpty(staffVOS)) {
            return initiateModel;
        }
        Optional<StaffConfItem> optional = staffVOS.stream().filter(s -> BusinessConstant.APPROVER_TYPE_USER.equals(s.getObjType())).findFirst();
        if (!optional.isPresent()) {
            return initiateModel;
        }
        StaffConfItem userEvaluationStaffVO = optional.get();
        if (CollectionUtils.isNotEmpty(userEvaluationStaffVO.getObjItems())) {
            List<String> ids = userEvaluationStaffVO.getObjItems().stream().map(ObjItem::getObjId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ids)) {
                empIds.addAll(ids);
            }
        }
        //List<EmpRefOrgDTO> empRefOrgDTOS = JSONArray.parseArray(initiateModel.getInitiatorList(), EmpRefOrgDTO.class);
        if (CollectionUtils.isNotEmpty(initiateModel.startEmpIds())) {
            empIds.addAll(initiateModel.startEmpIds());
        }

        if (CollectionUtils.isNotEmpty(empIds)) {
            List<EmployeeBaseInfoModel> employeeModels = findByEmpIdsWithOutStatus(initiateModel.getCompanyId(), empIds);
            if (CollectionUtils.isNotEmpty(employeeModels)) {
                initiateModel.setLeaveEmpIds(employeeModels.stream()
                        .filter(e -> !"on_the_job".equals(e.getStatus()) || "true".equals(e.getIsDelete()))
                        .map(EmployeeBaseInfoModel::getEmployeeId).collect(Collectors.toList()));
            }
        }
        return initiateModel;
    }

    public PerfTemplEvaluateInitiate findInitiate(TempId tempBaseId, int isNewEmp) {
        ComQB comQB = ComQB.build(PerfTemplEvaluateInitiateDo.class)
                .appendWhere("is_deleted='false'")
                .whereEq("templ_base_id", tempBaseId.getId());
        PerfTemplEvaluateInitiate evaluate = domainDao.findDomain(comQB, PerfTemplEvaluateInitiate.class);
        //选则父部门是否包含子部门 新人考核不包含
        if (isNewEmp == 1) {
            return evaluate;
        }
        List<ObjItem> orgChilds = getOrgChilds(new TenantId(evaluate.getCompanyId()), evaluate.evalDeptIds());
        evaluate.replaceExamineObjData(orgChilds);
        return evaluate;
    }

    public List<ObjItem> getOrgChilds(TenantId tenantId, List<String> orgIds) {
        if (CollUtil.isEmpty(orgIds)) {
            return new ArrayList<>();
        }
        ComQB orgComQb = ComQB.build(EmpOrganizationDo.class, "s")
                .join(EmpOrganizationDo.class, "p")
                .appendOn("s.company_id=p.company_id and instr(s.org_code,p.org_code) ")
                .clearSelect().select("s.org_id as objId,s.org_name as objName")
                .setRsType(ObjItem.class)
                .whereIn(" p.org_id", orgIds)
                .whereEq("p.company_id", tenantId.getId())
                .appendWhere("s.status='valid'")
                .groupBy(" s.org_id");
        return domainDao.listAll(orgComQb);
    }

    private List<EmployeeBaseInfoModel> findByEmpIdsWithOutStatus(String companyId, List<String> empIds) {
        if (CollectionUtils.isNotEmpty(empIds)) {
            ComQB<EmployeeBaseInfoModel> comQB = ComQB.build(EmployeeBaseInfoModel.class)
                    .whereEq("company_id", companyId)
                    .whereIn("employee_id", empIds);
            return domainDao.listAll(comQB);
        }
        return null;
    }

    private List<PerfTemplKpiTypeModel> listKpiType(TenantId companyId, TempId tempBaseId) {
        ComQB queryBuilder = ComQB.build(PerfTemplKpiTypeModel.class).
                whereEq("is_deleted", "false")
                .whereEq("templ_base_id", tempBaseId.getId())
                .whereEq("company_id", companyId.getId());
        return domainDao.listAll(queryBuilder);
    }

    private void appendEvaluateAffirm(ExamGroup examGroup) {
        ComQB queryBuilder = ComQB.build(PerfTemplEvaluateAffirmDo.class)
                .whereEq("is_deleted", "false")
                .whereEq("templ_base_id", examGroup.tmpId().getId());
        PerfTemplEvaluateAffirm affirm = domainDao.findDomain(queryBuilder, PerfTemplEvaluateAffirm.class);
        examGroup.setAffirm(affirm);
        if (Objects.isNull(affirm)) {
            return;
        }
        ComQB auditQ = ComQB.build(PerfTemplEvaluateAuditDo.class)
                .whereEq("is_deleted", "false")
                .whereEq("temp_base_id", examGroup.tmpId().getId())
                .whereEq("scene", EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT.getScene())
                .orderByAsc("approval_order");
        List<PerfTemplEvaluateAudit> audits = domainDao.listAllDomain(auditQ, PerfTemplEvaluateAudit.class);
        affirm.setAuditList(audits);
    }

    private void appendExecute(ExamGroup examGroup) {
        ComQB comQB = ComQB.build(PerfTemplEvaluateExecuteDo.class)
                .whereEq("templ_base_id", examGroup.tmpId().getId())
                .whereEq("is_deleted", "false");
        PerfTemplEvaluateExecute execute = domainDao.findDomain(comQB, PerfTemplEvaluateExecute.class);
        examGroup.setExecute(execute);
        if (Objects.isNull(execute)) {
            return;
        }
        ComQB auditQ = ComQB.build(PerfTemplEvaluateAuditDo.class)
                .whereEq("is_deleted", "false")
                .whereEq("temp_base_id", examGroup.tmpId().getId())
                .whereEq("scene", EvaluateAuditSceneEnum.CHANGE_ITEM_AUDIT.getScene())
                .orderByAsc("approval_order");
        final List<PerfTemplEvaluateAudit> audits = domainDao.listAllDomain(auditQ, PerfTemplEvaluateAudit.class);
        execute.setAuditList(audits);
    }

    private void appendItemPointRule(ExamGroup examGroup) {
        final String tempId = examGroup.tmpId().getId();
        final String companyId = examGroup.getCompanyId().getId();
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfTemplItemPointRule.class)
                .setSql("SELECT i.kpi_item_id,i.kpi_type_id, r.*, IFNULL(i.kpi_item_name,c.item_name)kpi_item_name, t.type_name as kpiTypeName, i.item_unit,i.item_type FROM perf_templ_kpi_item i " +
                        "LEFT JOIN perf_templ_kpi_type t ON i.kpi_type_id = t.type_id AND t.is_deleted = 'false' AND i.templ_base_id = t.templ_base_id " +
                        "LEFT JOIN perf_templ_item_point_rule r ON i.kpi_item_id = r.kpi_item_id AND i.kpi_type_id = r.kpi_type_id AND r.is_deleted = 'false' AND i.templ_base_id = r.templ_base_id " +
                        "LEFT JOIN company_kpi_item c ON i.kpi_item_id = c.id " +
                        "WHERE i.company_id = #{companyId} AND i.templ_base_id = #{tempId} AND i.is_deleted = 'false' AND (t.classify not in ('plus','subtract') or t.classify is NULL)  ORDER BY i.kpi_type_id, i.`order` ");
        sqlBuilder.setValue("tempId", tempId);
        sqlBuilder.setValue("companyId", companyId);
        List<PerfTemplItemPointRule> rules = domainDao.listAll(sqlBuilder);
        examGroup.setPointRules(rules);
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }
        ComQB comQB = ComQB.build(PerfItemRefPointDetailDo.class)
                .whereEq("company_id", companyId)
                .whereEq("templ_base_id", tempId)
                .whereEq("is_deleted", "false")
                .orderByDesc("grade_highest");
        List<PerfTempRefPointDetail> pointDetails = domainDao.listAllDomain(comQB, PerfTempRefPointDetail.class);
        if (pointDetails.isEmpty()) {
            return;
        }
        Map<String, List<PerfTempRefPointDetail>> pointDetailMap = pointDetails.stream().collect(Collectors.groupingBy(PerfTempRefPointDetail::getRefId));
        rules.forEach(itemPointModel -> {
            if (StringUtils.isNotEmpty(itemPointModel.getId())) {
                itemPointModel.setPointDetailList(pointDetailMap.get(itemPointModel.getId()));
            }
        });
    }

    public void appendEvaluate(ExamGroup examGroup) {
        String tempBaseId = examGroup.tmpId().getId();
        ComQB comQB = ComQB.build(PerfTemplEvaluateDo.class)
                .whereEq("is_deleted", "false")
                .whereEq("templ_base_id", tempBaseId);
        final PerfTemplEvaluate evaluate = domainDao.findDomain(comQB, PerfTemplEvaluate.class);
        examGroup.setEvaluate(evaluate);
        if (Objects.isNull(evaluate)) {
            return;
        }
        //设置360流程和简易流程的评分人,校准人每种流程都需要设置
        //List<PerfTemplEvaluateAudit> auditList = queryS3AuditByScenes(examGroup.tmpId(), EvaluateAuditSceneEnum.scoreTypes());
        List<PerfTemplEvaluateAudit> auditList = queryAuditByScenes(examGroup.tmpId(), EvaluateAuditSceneEnum.scoreTypes(), null, null);
        evaluate.finalAuditList(auditList.stream().filter(l -> EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene().equals(l.getScene())).collect(Collectors.toList()));
        if (!examGroup.isCustom()) {
            //同级互评
            evaluate.setPeerScoreList(auditList.stream().filter(l -> EvaluateAuditSceneEnum.PEER_SCORE.getScene().equals(l.getScene())).collect(Collectors.toList()));
            //下级互评
            evaluate.setSubScoreList(auditList.stream().filter(l -> EvaluateAuditSceneEnum.SUB_SCORE.getScene().equals(l.getScene())).collect(Collectors.toList()));
            //上级评分
            evaluate.setSuperiorScoreList(auditList.stream().filter(l -> EvaluateAuditSceneEnum.SUPERIOR_SCORE.getScene().equals(l.getScene())).collect(Collectors.toList()));
        }
        evaluate.setTypeWeightSwitch(examGroup.getTypeWeightSwitch());
        evaluate.setTypeWeightLimitFlag(examGroup.getTypeWeightLimitFlag());

        QueryBuilder affirmQ = QueryBuilder.build(PerfTemplEvaluateAffirmDo.class)
                .whereEq("is_deleted", "false")
                .whereEq("templ_base_id", tempBaseId);
        final PerfTemplEvaluateAffirm affirmModel = domainDao.findDomain(affirmQ, PerfTemplEvaluateAffirm.class);
        if (Objects.nonNull(affirmModel)) {
            evaluate.setModifyAuditFlag(affirmModel.getModifyAuditFlag());
        }
    }

    public Boolean exitsUserPeerScore(TempId tempId) {
        PerfTemplBaseDo temp = domainDao.findById(PerfTemplBaseDo.class, tempId.getId());
        if (!EvaluateTypeEnum.CUSTOM.getType().equals(temp.getEvaluateType())) {
            return false;
        }
        ComQB comQB = ComQB.build(PerfTemplItemEvaluateDo.class)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEq("templ_base_id", tempId.getId());
        List<PerfTemplItemEvaluateDo> items = domainDao.listAll(comQB);
        if (CollectionUtils.isEmpty(items)) {
            return false;
        }
        List<PerfTemplItemEvaluateDo> examPeers = items.stream().filter(item -> "user".equals(item.getMutualUserType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(examPeers)) {
            return false;
        }
        return true;
    }

    public List<KVPair> listTempName(TenantId tenantId, List<String> tmpIds) {
        final ComQB qb = ComQB.build(PerfTemplBaseDo.class)
                .clearSelect().select("id,name").setRsType(KVPair.class)
                .whereEq("company_id", tenantId.getId())
                .whereInReq("id", tmpIds);
        return domainDao.listAll(qb);
    }

    public PerfTemplEvalResultConfigPo evalResultConfig(String companyId, String templBaseId) {
        ComQB comQB = ComQB.build(PerfTemplBaseDo.class)
                .setRsType(PerfTemplEvalResultConfigPo.class)
                .whereEqReq("company_id", companyId)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEqReq("id", templBaseId);
        PerfTemplEvalResultConfigPo po = domainDao.findOne(comQB);
        if (Objects.isNull(po)) {
            return new PerfTemplEvalResultConfigPo();
        }
        po.setTemplBaseId(templBaseId);
        //兼容历史数据
        if (StringUtils.isEmpty(po.getEvaluateType())) {
            getEvaluateType(po);
        }
        //新人考核
        if (CollUtil.isNotEmpty(po.getNewOrgIds())) {
            List<KpiDept> depts = listOrgs(new TenantId(po.getCompanyId()), po.getNewOrgIds());
            po.setOrgs(depts);
        }
        List<PerfTemplItemPointRulePo> rulePos = listTemItemPointRule(companyId, templBaseId);
        po.setItemPointRuleList(rulePos);
        return po;
    }

    public void getEvaluateType(PerfTemplEvalResultConfigPo po) {
        ComQB evaluateTypeQb = ComQB.build(PerfTemplEvaluateDo.class)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEqReq("templ_base_id", po.getId());
        PerfTemplEvaluateDo evaluateDo = domainDao.findOne(evaluateTypeQb);
        if (Objects.isNull(evaluateDo)) {
            return;
        }
        po.setEvaluateType(evaluateDo.getEvaluateType());
    }

    public List<KpiDept> listOrgs(TenantId tenantId, List<String> orgIds) {
        final ComQB qb = ComQB.build(EmpOrganizationDo.class, "o")
                .clearSelect().select("org_name,org_id, ding_org_id  ext_dept_id ")
                .setRsType(KpiDept.class)
                .whereIn("org_id", orgIds)
                .whereEq("company_id", tenantId.getId())
                .appendWhere("status='valid'");
        return domainDao.listAllDomain(qb, KpiDept.class);
    }

    public List<PerfTemplItemPointRulePo> listTemItemPointRule(String companyId, String tempId) {
        ComQB comQB = ComQB.build(PerfTemplKpiItemDo.class, "i")
                .leftJoin(PerfTemplKpiTypeDo.class, "t")
                .appendOn("i.kpi_type_id = t.type_id AND t.is_deleted = 'false' AND i.templ_base_id = t.templ_base_id")
                .leftJoin(PerfTemplItemPointRuleDo.class, "r")
                .appendOn("i.kpi_item_id = r.kpi_item_id AND i.kpi_type_id = r.kpi_type_id AND r.is_deleted = 'false' AND i.templ_base_id = r.templ_base_id")
                .leftJoin(CompanyKpiItemDo.class, "c")
                .appendOn("i.kpi_item_id = c.id")
                .clearSelect()
                .select("i.kpi_item_id,i.kpi_type_id,r.*,IFNULL(i.kpi_item_name, c.item_name) kpi_item_name, \n" +
                        "t.type_name as   kpiTypeName,i.item_unit,i.item_type")
                .setRsType(PerfTemplItemPointRulePo.class)
                .whereEqReq("i.company_id", companyId)
                .whereEqReq("i.templ_base_id", tempId)
                .whereEqReq("i.is_deleted", Boolean.FALSE.toString())
                .appendWhere(" (t.classify not in ('plus', 'subtract') or t.classify is NULL)")
                .orderBy(" i.kpi_type_id, i.`order`");
        List<PerfTemplItemPointRulePo> rulePos = domainDao.listAll(comQB);
        if (CollUtil.isEmpty(rulePos)) {
            return rulePos;
        }
        List<String> ruleIds = rulePos.stream().filter(rule -> StringUtils.isNotBlank(rule.getId())).map(rule -> rule.getId()).collect(Collectors.toList());
        ComQB detailQb = ComQB.build(PerfItemRefPointDetailDo.class)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereIn("ref_id", ruleIds)
                .whereEqReq("company_id", companyId)
                .whereEqReq("templ_base_id", tempId);
        List<PerfItemRefPointDetailDo> detailDos = domainDao.listAll(detailQb);
        if (CollUtil.isEmpty(detailDos)) {
            return rulePos;
        }
        Map<String, List<PerfItemRefPointDetailDo>> refIdMap = detailDos.stream().filter(d -> StringUtils.isNotBlank(d.getRefId())).collect(Collectors.groupingBy(d -> d.getRefId()));
        rulePos.forEach(r -> {
            r.setPointDetailList(refIdMap.get(r.getId()));
        });
        return rulePos;
    }

    public TempAffirmPo queryTempAffirm(String companyId, String tempBaseId) {
        ComQB affirm = ComQB.build(PerfTemplEvaluateAffirmDo.class)
                .setRsType(TempAffirmPo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("templ_base_id", tempBaseId)
                .whereEqReq("is_deleted", "false");
        TempAffirmPo tempAffirmPo = domainDao.findOne(affirm);
        if (Objects.isNull(tempAffirmPo)) {
            return null;
        }
        List<ApprovalRulePo> approvalRules = getApprovalRules(companyId, tempBaseId, EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT.getScene());
        tempAffirmPo.setApprovalRules(approvalRules);
        return tempAffirmPo;
    }

    public TempExecutePo queryTempExecute(String companyId, String tempBaseId) {
        ComQB execute = ComQB.build(PerfTemplEvaluateExecuteDo.class)
                .setRsType(TempExecutePo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("templ_base_id", tempBaseId)
                .whereEqReq("is_deleted", "false");
        TempExecutePo tempExecutePo = domainDao.findOne(execute);
        if (Objects.isNull(tempExecutePo)) {
            return null;
        }
        List<ApprovalRulePo> approvalRules = getApprovalRules(companyId, tempBaseId, EvaluateAuditSceneEnum.CHANGE_ITEM_AUDIT.getScene());
        tempExecutePo.setApprovalRules(approvalRules);
        return tempExecutePo;
    }

    @NotNull
    private List<ApprovalRulePo> getApprovalRules(String companyId, String tempBaseId, String scene) {
        ComQB audit = ComQB.build(PerfTemplEvaluateAuditDo.class)
                .setRsType(ApprovalRulePo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("temp_base_id", tempBaseId)
                .whereEqReq("is_deleted", "false")
                .whereEq("scene", scene)
                .orderByAsc(" approval_order ");
        List<ApprovalRulePo> approvalRules = domainDao.listAll(audit);
        //如果审核人类型是角色，需要查询一下角色名用于页面回显
        approvalRules.stream().forEach(po -> {
            if (BusinessConstant.APPROVER_TYPE_ROLE.equals(po.getApproverType())) {
                ComQB role = ComQB.build(RoleModel.class)
                        .clearSelect().select(" role_name ")
                        .setRsType(String.class)
                        .whereEqReq("company_id", companyId)
                        .whereEqReq("id", po.getApproverInfo());
                po.setApproverName(domainDao.findOne(role));
            }

            if (BusinessConstant.APPROVER_TYPE_USER.equals(po.getApproverType())) {
                ComQB empQb = ComQB.build(EmployeeBaseInfoDo.class)
                        .clearSelect().select(" name ")
                        .setRsType(String.class)
                        .whereEqReq("company_id", companyId)
                        .whereEqReq("employeeId", po.getApproverInfo());
                po.setApproverName(domainDao.findOne(empQb));
            }

        });
        return approvalRules;
    }

    public PagedList<PerfTempPo> pagedTemp(TenantId tenantId, String keyword, String cycleType, int pageNo, int pageSize) {
        ComQB comQB = ComQB.build(PerfTemplBaseDo.class)
                .clearSelect()
                .select("id,name,cycle_type")
                .setRsType(PerfTempPo.class)
                .whereEq("company_id", tenantId.getId())
                .whereEq("cycle_type", cycleType)
                .whereLike("name", keyword)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEq("status", BusinessConstant.PUBLISHED)
                .whereEq("is_new_emp", 0)
                .orderByDesc("created_time")
                .setPage(pageNo, pageSize);
        return domainDao.listPage(comQB);
    }

    private boolean openedChildAdmin(String companyId) {
        ComQB qb = ComQB.build(TenantSysConfDo.class, "conf")
                .whereEq("companyId", companyId)
                .appendWhere("open= 1")
                .whereEq("confCode", TenantSysConfDo.temp_priv_202304025);
        TenantSysConfDo open = domainDao.findOne(qb);
        return open != null;
    }

    public PagedList<AdminTempPo> pagedAdminTemp(TempQuery query) {
        if (query.isChildAdmin() && openedChildAdmin(query.getCompanyId())) {
            return pagedYXChildAdminTemp(query);
        }
        ComQB<AdminTempPo> comQB = ComQB.build(PerfTemplBaseDo.class, "tp")
                .leftJoin(EmployeeBaseInfoDo.class, "e")
                .appendOn("tp.created_user = e.employee_id  and tp.company_id = e.company_id")
                .leftJoin(EmployeeBaseInfoDo.class, "e2")
                .appendOn("tp.updated_user = e2.employee_id  and tp.company_id = e2.company_id")
                .whereEq("tp.company_id", query.getCompanyId())
                .whereEq("tp.is_deleted", "false")
                .whereLike("tp.name", query.getKeyword())
                .whereEq("tp.status", "published")
                .whereEq("tp.is_new_emp", query.getIsNewEmp());
        if (StrUtil.isNotBlank(query.getEmpIds())) {
            comQB.whereIn("tp.created_user", Arrays.asList(query.getEmpIds().split(",")));
        }
        if (StrUtil.isNotBlank(query.getCreatedTime())) {
            comQB.appendWhere(" DATE_FORMAT(tp.created_time,'%Y-%m-%d') = '" + query.getCreatedTime() + "' ");
        }
        if (StrUtil.isNotBlank(query.getCycleType())) {
            comQB.whereIn("tp.cycle_type", Arrays.asList(query.getCycleType().split(",")));
        }
        if (StrUtil.isNotBlank(query.getEvaluateType())) {
            comQB.whereIn("tp.evaluate_type", Arrays.asList(query.getEvaluateType().split(",")));
        }
        if (StrUtil.isNotBlank(query.getCategoryId())) {
            if ("no".equals(query.getCategoryId())) {
                comQB.appendWhere("(tp.category_id is null or tp.category_id = '')");
            }else {
                comQB.whereEqReq("tp.category_id", query.getCategoryId());
            }
        }else {
            if (CollUtil.isNotEmpty(query.getAuthCategoryIds())) {
                comQB.appendWhere("(tp.category_id IN " + StringTool.getInStr(query.getAuthCategoryIds()) + "OR (tp.category_id is null or tp.category_id = ''))");
            }
        }
        if (query.appointTempScope()) {
            StringBuilder sb = new StringBuilder("((tp.created_user = '" + query.getEmpId() + "') ");
            if (StrUtil.isNotEmpty(query.getTempIds())) {
                //子管理员的授权模板为空，则只可以看到自己创建的模板
                sb.append(" or (tp.id IN ").append(StringTool.getInStr(query.getTempIds())).append(")");
            }
            sb.append(")");
            comQB.appendWhere(sb.toString());
        }
        comQB.clearSelect().select("tp.*,e.name empName,e2.name updatedName").setRsType(AdminTempPo.class)
                .groupBy("tp.id")
                .orderByDesc("tp.created_time")
                .setPage(query.getPageNo(), query.getPageSize());
        if (StrUtil.isNotBlank(query.getOrderField())) {
            comQB.orderBy(String.format("tp.%s  %s", query.getOrderField(), query.getOrderSort()));
        }

        PagedList<AdminTempPo> adminTempPos = domainDao.listPage(comQB);
        return replaceDepts(adminTempPos);
    }

    private PagedList<AdminTempPo> pagedYXChildAdminTemp(TempQuery query) {
        //圆心的子管理需要看到自己同级部门人创建的模板  和子部门的人创建的模板
        ComQB comQB = ComQB.build(EmpRefOrgDo.class)
                .clearSelect().select("org_id").setRsType(String.class)
                .whereEqReq("emp_id", query.getEmpId())
                .whereEqReq("company_id", query.getCompanyId())
                .whereEq("ref_type", "org");
        //我所在的部门
        List<String> orgIds = domainDao.listAll(comQB);
        //获取所有子部门
        ComQB orgComQb = ComQB.build(EmpOrganizationDo.class, "s")
                .join(EmpOrganizationDo.class, "p")
                .appendOn("s.company_id=p.company_id and instr(s.org_code,p.org_code) ")
                .clearSelect().select("s.org_id").setRsType(String.class)
                .whereIn(" p.org_id", orgIds)
                .whereEq("p.company_id", query.getCompanyId())
                .appendWhere("s.status='valid'")
                .groupBy(" s.org_id");
        //获取部门及部门下的人
        ComQB orgEmpQB = ComQB.build(EmpRefOrgDo.class)
                .clearSelect().select("distinct (emp_id)").setRsType(String.class)
                .whereIn("org_id", domainDao.listAll(orgComQb))
                .whereEqReq("company_id", query.getCompanyId())
                .whereEq("ref_type", "org");

        //查询模板
        ComQB<AdminTempPo> tempQB = ComQB.build(PerfTemplBaseDo.class, "tp")
                .leftJoin(EmployeeBaseInfoDo.class, "e")
                .appendOn("tp.created_user=e.employee_id and tp.company_id=e.company_id")
                .leftJoin(EmployeeBaseInfoDo.class, "e2")
                .appendOn("tp.updated_user=e2.employee_id and tp.company_id=e2.company_id")
                .whereEq("tp.company_id", query.getCompanyId())
                .whereEq("tp.is_deleted", "false")
                .whereLike("tp.name", query.getKeyword())
                .whereEq("tp.status", "published")
                .whereEq("tp.is_new_emp", query.getIsNewEmp())
                .whereIn("tp.created_user", domainDao.listAll(orgEmpQB));
        if (StrUtil.isNotBlank(query.getEvaluateType())) {
            tempQB.whereIn("tp.evaluate_type", Arrays.asList(query.getEvaluateType().split(",")));
        }
        if (StrUtil.isNotBlank(query.getCycleType())) {
            tempQB.whereIn("tp.cycle_type", Arrays.asList(query.getCycleType().split(",")));
        }
        if (StrUtil.isNotBlank(query.getCategoryId())) {
            tempQB.whereEqReq("tp.category_id", query.getCategoryId());
        }
        tempQB.clearSelect().select("tp.*,e.name empName,e2.name updatedName").setRsType(AdminTempPo.class).groupBy("tp.id").orderByDesc("tp.created_time");
        tempQB.setPage(query.getPageNo(), query.getPageSize());
        PagedList<AdminTempPo> adminTempPos = domainDao.listPage(tempQB);
        return replaceDepts(adminTempPos);
    }

    public SystemAdminSetDo getAdmin(String companyId, String empId) {
        ComQB<SystemAdminSetDo> adminQB = ComQB.build(SystemAdminSetDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("emp_id", empId)
                .whereEq("status", "valid");
        return domainDao.findOne(adminQB);
    }

    public PagedList<AdminTempPo> replaceDepts(PagedList<AdminTempPo> adminTempPos) {
        if (CollUtil.isEmpty(adminTempPos.getData())) {
            return adminTempPos;
        }
        //新人考核替换部门
        List<AdminTempPo> newTemps = adminTempPos.getData().stream().filter(temp -> CollUtil.isNotEmpty(temp.getNewOrgIds())).collect(Collectors.toList());
        if (CollUtil.isEmpty(newTemps)) {
            return adminTempPos;
        }
        newTemps.forEach(temp -> {
            List<String> orgIds = temp.getNewOrgIds();
            List<KpiDept> depts = listOrgs(new TenantId(temp.getCompanyId()), orgIds);
            temp.setOrgs(depts);
        });
        return adminTempPos;
    }

    public boolean existTempName(String companyId, String tempName, String tempId) {
        final ComQB existQ = ComQB.build(PerfTemplBaseDo.class, "t")
                .whereEqReq("companyId", companyId)
                .whereEqReq("name", tempName)
                .whereNotEq("id", tempId)
                .whereEq("is_deleted", "false")
                .limit(0, 1);
        final PerfTemplBaseDo one = domainDao.findOne(existQ);
        return one != null;
    }

    public boolean existTempCategoryName(String companyId, String categoryName) {
        final ComQB existQ = ComQB.build(PerfTemplCategoryDo.class, "c")
                .whereEqReq("companyId", companyId)
                .whereEqReq("category_name", categoryName)
                .whereEq("is_deleted", "false")
                .limit(0, 1);
        final PerfTemplCategoryDo one = domainDao.findOne(existQ);
        return one != null;
    }


    StringBuilder sqlBuilder = new StringBuilder("c.id,c.company_id,c.category_name,c.is_deleted,c.created_user, c.created_time,c.updated_user,c.updated_time,c.parent_category_id," +
            "ifnull(c.top_category_id,c.id) as top_category_id,ifnull(c.category_level,1) as category_level ");

    public PerfTemplCategory getParentCategoryInfo(String companyId, String parentCategoryId) {
        ComQB qb = ComQB.build(PerfTemplCategoryDo.class, "c")
                .clearSelect().select(sqlBuilder.toString()).setRsType(PerfTemplCategory.class)
                .whereEq("c.companyId", companyId)
                .whereEq("c.id", parentCategoryId)
                .whereEq("c.is_deleted", Boolean.FALSE);
        return domainDao.findOne(qb);
    }

    /**
     * 查询分类以及分类下的所有下级分类
     *
     * @param companyId
     * @param categoryId
     * @param isSelf     是否包含当前分类
     * @return
     */
    public List<PerfTemplCategory> listAllChildCategory(String companyId, String categoryId, Boolean isSelf) {
        List<PerfTemplCategory> modelList = new ArrayList<>();
        ComQB currentQb = ComQB.build(PerfTemplCategoryDo.class, "c")
                .clearSelect().select(sqlBuilder.toString())
                .setRsType(PerfTemplCategory.class)
                .whereEq("c.company_id", companyId)
                .whereEq("c.is_deleted", Boolean.FALSE.toString())
                .whereEq("c.id", categoryId);
        PerfTemplCategory model = domainDao.findOne(currentQb);
        if (model != null) {
            if (isSelf) {
                modelList.add(model);
            }
            List<String> childIds = listChildIdAll(companyId, model);
            if (CollectionUtils.isNotEmpty(childIds)) {
                ComQB qb = ComQB.build(PerfTemplCategoryDo.class, "c")
                        .clearSelect().select(sqlBuilder.toString())
                        .setRsType(PerfTemplCategory.class)
                        .whereEq("c.company_id", companyId)
                        .whereEq("c.is_deleted", Boolean.FALSE.toString())
                        .whereIn("c.id", childIds);
                modelList.addAll(domainDao.listAll(qb));
            }
        }
        return modelList;
    }

    private List<String> listChildIdAll(String companyId, PerfTemplCategory model) {
        NativeSQLBuilder builder = NativeSQLBuilder.build(String.class).setSql(
                "SELECT id FROM ( SELECT t1.id, t1.parent_category_id, IF ( find_in_set( parent_category_id, @pids ) > 0, @pids := concat( @pids, ',', id ), 0 ) AS ischild \n" +
                        "FROM ( SELECT *  FROM perf_templ_category t  WHERE t.company_id = #{companyId} AND t.top_category_id = #{topCategoryId} AND t.is_deleted = 'false' \n" +
                        "ORDER BY category_level ) t1, ( SELECT @pids := #{categoryId} ) t2  ) t3 \n" +
                        "WHERE ischild != '0'");
        builder.setValue("companyId", companyId);
        builder.setValue("topCategoryId", model.getTopCategoryId());
        builder.setValue("categoryId", model.getId());
        List<String> childIds = domainDao.listAll(builder);
        return childIds;
    }

    public List<String> listTempBaseIdsByCategoryId(String companyId, List<String> categoryIdList) {
        ComQB comQB = ComQB.build(PerfTemplBaseDo.class)
                .clearSelect().select("id")
                .setRsType(String.class)
                .whereEq("company_id", companyId)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereInReq("category_id", categoryIdList);
        return domainDao.listAll(comQB);
    }


    public List<PerfTemplCategory> listChildCategory(String companyId, String parentCategoryId) {
        ComQB qb = ComQB.build(PerfTemplCategoryDo.class, "c")
                .leftJoin(PerfTemplCategoryDo.class, "m")
                .appendOn("c.company_id = m.company_id and c.id = m.parent_category_id and m.is_deleted = 'false' ")
                .leftJoin(PerfTemplCategoryDo.class, "p")
                .appendOn("c.company_id = p.company_id and c.parent_category_id = p.id and p.is_deleted = 'false' ")
                .clearSelect().select(sqlBuilder.toString() + ",count(m.id) as childNum,p.category_name as parentCategoryName")
                .setRsType(PerfTemplCategory.class)
                .whereEq("c.company_id", companyId)
                .whereEq("c.is_deleted", Boolean.FALSE.toString())
                .whereEq("c.parent_category_id", parentCategoryId)
                .groupBy("c.id");
        List<PerfTemplCategory> list = domainDao.listAll(qb);
        if (CollUtil.isEmpty(list)) {
            return list;
        }
        //查询组装管理数据
        Map<String, List<AdminManageTemplCategory>> groupManage = asManageListToMap(companyId, CollUtil.map(list, PerfTemplCategory::getId, true));
        if (CollUtil.isEmpty(groupManage)) {
            return list;
        }
        for (PerfTemplCategory templCategory : list) {
            if (groupManage.get(templCategory.getId()) != null) {
                templCategory.setCategoryManageList(groupManage.get(templCategory.getId()));
            }
        }
        return list;
    }


    public Map<String, List<AdminManageTemplCategory>> asManageListToMap(String companyId, List<String> categoryIds) {
        //查询管理数据
        ComQB manageQb = ComQB.build(AdminManageTemplCategory.class, "a")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn("a.company_id = e.company_id and a.admin_emp_id = e.employee_id")
                .clearSelect().select("a.admin_emp_id as adminEmpId, e.name as adminEmpName,a.category_id")
                .setRsType(AdminManageTemplCategory.class)
                .whereEq("a.company_id", companyId)
                .whereEq("a.is_deleted", Boolean.FALSE.toString())
                .whereIn("a.category_id", categoryIds);
        List<AdminManageTemplCategory> manageTempCategoryList = domainDao.listAll(manageQb);
        if (CollUtil.isEmpty(manageTempCategoryList)) {
            return new HashMap<>();
        }
        Map<String, List<AdminManageTemplCategory>> groupManage = manageTempCategoryList.stream()
                .collect(Collectors.groupingBy(AdminManageTemplCategory::getCategoryId));
        return groupManage;
    }

    /**
     * 查询权限分类ids
     * @param companyId
     * @param categoryName
     * @param adminType
     * @return
     */
    public List<String> listAuthCategoryIds(String companyId, String categoryName, String adminType,String opEmpId) {
        ComQB qb = ComQB.build(PerfTemplCategoryDo.class, "c")
                .leftJoin(AdminManageTemplCategoryDo.class, "a")
                .appendOn("c.company_id = a.company_id and c.id = a.category_id and a.is_deleted = 'false' ")
                .clearSelect().select("c.id")
                .setRsType(String.class)
                .whereEq("c.company_id", companyId)
                .whereEq("c.is_deleted", Boolean.FALSE.toString())
                .groupBy("c.id");
        if (StringUtils.isNotBlank(categoryName)) {
            qb.whereLike("c.category_name", categoryName);
        }
        ComQB comQB = ComQB.build(TenantSysConfDo.class, "d")
                .whereEq("company_id", companyId)
                .whereEq("conf_code", TenantSysConfEnum.TEMP_AUTH.getType());
        TenantSysConf sysConf = domainDao.findDomain(comQB, TenantSysConf.class);
        if ((sysConf != null && sysConf.getOpen() == 0) || AdminType.MAIN.equals(adminType) || sysConf == null) {
            return domainDao.listAll(qb);
        } else if (sysConf != null && sysConf.getOpen() == 1) {
            qb.appendWhere(" (a.admin_emp_id = '" + 0 + "' or a.admin_emp_id = '" + opEmpId + "')");
        }
        List<String> ids = domainDao.listAll(qb);
        //无模板权限的人返回0;
        if (CollUtil.isEmpty(ids)) {
            ids.add("0");
        }
        return ids;
    }


    /**
     * 模板分类列表
     *
     * @param companyId
     * @return
     */
    public List<PerfTemplCategory> listAuthTemplCategory(String companyId, List<String> authCategoryIds, Boolean isEdit) {
        if (CollUtil.isEmpty(authCategoryIds)) {
            return new ArrayList<>();
        }
        ComQB comQB = ComQB.build(PerfTemplCategoryDo.class, "c")
                .leftJoin(PerfTemplCategoryDo.class, "m")
                .appendOn("c.company_id = m.company_id and c.id = m.parent_category_id and m.is_deleted = 'false' ")
                .leftJoin(PerfTemplCategoryDo.class, "p")
                .appendOn("c.company_id = p.company_id and c.parent_category_id = p.id and p.is_deleted = 'false' ")
                .clearSelect().select(sqlBuilder.toString() + ",count(m.id) as childNum,p.category_name as parentCategoryName")
                .setRsType(PerfTemplCategory.class)
                .whereEq("c.company_id", companyId)
                .whereEq("c.is_deleted", Boolean.FALSE.toString())
                .whereIn("c.id",authCategoryIds)
                .groupBy("c.id");
        if (isEdit != null && isEdit) {
            comQB.whereLow("c.category_level", 5);
        }
        List<PerfTemplCategory> authCategoryList = domainDao.listAll(comQB);
        if (CollUtil.isEmpty(authCategoryList)) {
            return new ArrayList<>();
        }
        //过滤出有权限等级最高的
        List<PerfTemplCategory> filterList = new ArrayList<>();
        Map<String, List<PerfTemplCategory>> groupMap = authCategoryList.stream()
                .collect(Collectors.groupingBy(PerfTemplCategory::getTopCategoryId));
        groupMap.forEach((k, v) -> {
            List<PerfTemplCategory> list = v;
            Integer min = list.stream().mapToInt(PerfTemplCategory::getCategoryLevel).min().getAsInt();
            filterList.addAll(list.stream().filter(obj -> obj.getCategoryLevel() == min).collect(Collectors.toList()));
        });
        return filterList;
    }

    public Integer getTempCountByCategory(String companyId, String categoryId) {
        ComQB currentQb = ComQB.build(PerfTemplCategoryDo.class, "c")
                .clearSelect().select(sqlBuilder.toString())
                .setRsType(PerfTemplCategory.class)
                .whereEq("c.company_id", companyId)
                .whereEq("c.is_deleted", Boolean.FALSE.toString())
                .whereEq("c.id", categoryId);
        PerfTemplCategory model = domainDao.findOne(currentQb);
        if (model != null) {
            List<String> childIds = listChildIdAll(companyId, model);
            childIds.add(model.getId());
            if (CollectionUtils.isNotEmpty(childIds)) {
                ComQB qb = ComQB.build(PerfTemplBaseDo.class, "b")
                        .clearSelect().select("count(b.id)")
                        .setRsType(Integer.class)
                        .whereEq("b.company_id", companyId)
                        .whereEq("b.is_deleted", Boolean.FALSE.toString())
                        .whereIn("b.category_id", childIds);
               return domainDao.findOne(qb);
            }
        }
        return 0;
    }

    public Map<String, String> listNameMapByIds(Set<String> ids) {
        ComQB qb = ComQB.build(PerfTemplBaseDo.class, "tb")
                .clearSelect()
                .select("tb.id AS id, tb.name AS name")
                .whereIn("tb.id", ids)
                .whereEq("tb.is_deleted", Boolean.FALSE.toString())
                .setRsType(PerfTemplBaseDo.class);

        List<PerfTemplBaseDo> result = domainDao.listAll(qb);

        return result.stream()
                .collect(Collectors.toMap(
                        PerfTemplBaseDo::getId,
                        PerfTemplBaseDo::getName
                ));
    }
}
