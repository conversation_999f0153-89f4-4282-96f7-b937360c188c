package com.polaris.kpi.eval.infr.task.dao;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.com.polaris.kpi.eval.EvalUserOfTask;
import cn.com.polaris.kpi.eval.EvalUserRelGrade;
import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.perf.www.common.constant.BusinessConstant;
import com.perf.www.common.em.EvaluateTaskStatusEnum;
import com.perf.www.common.em.EvaluateTypeEnum;
import com.perf.www.common.em.OrderFieldEnum;
import com.perf.www.common.utils.string.StringTool;
import com.perf.www.domain.entity.company.EmpOrganizationModel;
import com.perf.www.domain.entity.company.EmpRefOrgModel;
import com.perf.www.domain.entity.company.EmployeeBaseInfoModel;
import com.perf.www.dto.PerfEvaluateTaskUserDTO;
import com.perf.www.model.task.PerfEvaluateTaskBaseModel;
import com.perf.www.model.task.PerfEvaluateTaskItemScoreRuleModel;
import com.perf.www.model.task.PerfEvaluateTaskUserModel;
import com.perf.www.utils.ReportSeriesDataCalculator;
import com.perf.www.vo.report.ReportSeriesDataVO;
import com.perf.www.vo.task.PerfEvaluateTaskItemScoreRuleVO;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTaskDiff;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalRule;
import com.polaris.kpi.eval.domain.task.entity.empeval.PerfEvalTypeResult;
import com.polaris.kpi.eval.domain.task.entity.log.ItemDynamicLog;
import com.polaris.kpi.eval.domain.task.repo.EmpEvalRuleRepo;
import com.polaris.kpi.eval.domain.task.type.EmpEvalScorerTransferStatusEnum;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.eval.domain.task.type.TransferRater;
import com.polaris.kpi.eval.infr.stage.dao.BaseAuditFlowDao;
import com.polaris.kpi.eval.infr.task.ppojo.*;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.BaseResultExcelPo;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.EvalResultPo;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.*;
import com.polaris.kpi.eval.infr.task.ppojo.grade.GradeStepDo;
import com.polaris.kpi.eval.infr.task.query.*;
import com.polaris.kpi.eval.infr.task.query.empeval.EmpEvalErrorQuery;
import com.polaris.kpi.eval.infr.task.query.empeval.EvalOfEmpQuery;
import com.polaris.kpi.eval.infr.task.query.empeval.MyHandleTaskQuery;
import com.polaris.kpi.eval.infr.task.query.emptable.EmpForTableQuery;
import com.polaris.kpi.org.domain.dept.entity.CompanyConf;
import com.polaris.kpi.org.infr.company.dao.CompanyDaoImpl;
import com.polaris.kpi.org.infr.company.ppojo.CompanyMsgCenterDo;
import com.polaris.kpi.org.infr.dept.pojo.AdminManageOrgDo;
import com.polaris.kpi.org.infr.emp.pojo.EmpOrganizationDo;
import com.polaris.kpi.org.infr.emp.pojo.EmpRefOrgDo;
import com.polaris.kpi.org.infr.emp.pojo.EmployeeBaseInfoDo;
import com.polaris.kpi.org.infr.emp.pojo.RoleRefEmpDo;
import com.polaris.sdk.type.AuditEnum;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.DateUtils;
import org.joda.time.DateTime;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.NativeSQLBuilder;
import org.lufei.ibatis.builder.QueryBuilder;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.lufei.ibatis.mapper.PagedList;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TaskUserDao extends BaseAuditFlowDao {
    @Resource
    protected DomainDaoImpl autoBaseDao;
    @Resource
    protected EmpEvalRuleRepo empEvalRuleRepo;
    @Resource
    protected EvalKpiDao evalKpiDao;
    @Resource
    protected CompanyDaoImpl companyDao;

    public void setDomainDao(DomainDaoImpl autoBaseDao) {
        this.autoBaseDao = autoBaseDao;
    }

    public void adminScopeOrgReq(List<String> adminOrgIds, Integer performanceType, ComQB comQB) {
        if (CollUtil.isNotEmpty(adminOrgIds)) {
            if (Objects.equals(performanceType, 2)) {
                comQB.whereIn("u.eval_org_id", adminOrgIds);
            } else {
                String sql = StringTool.getInStrSql(adminOrgIds);
                comQB.appendWhere("(u.org_id in " + sql + " OR u.temp_task = 1)");
            }
        }
    }

    public void setAutoBaseDao(DomainDaoImpl autoBaseDao) {
        this.autoBaseDao = autoBaseDao;
    }

    public void updateOrgNames(String taskUserId, String empName, String empOrgId, String empOrgName) {
        final UpdateBuilder up = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                .set("emp_name", empName)
                .set("emp_org_id", empOrgId)
                .set("org_name_list", empOrgName)
                .whereEqReq("id", taskUserId);
        autoBaseDao.update(up);
    }

    public PagedList<ReportSeriesDataVO> pageEvaluateTaskUser(TaskUserQuery query) {
        Set<String> empIds = new HashSet<>();
        ComQB org = ComQB.build(EmpRefOrgModel.class, "r")
                .join(EmpOrganizationModel.class, "o").on("r.org_id", "o.org_id")
                .clearSelect().select("r.emp_id,GROUP_CONCAT(o.org_name) org_name")
                .whereEq("o.status", "valid").whereEq("r.ref_type", "org")
                .whereEq("r.company_id", query.getCompanyId())
                .whereEq("o.company_id", query.getCompanyId());
        if (StringUtils.isNotBlank(query.getOrgIds())) {
            org.whereIn("o.org_id", Arrays.asList(query.getOrgIds().split(",")));
        }
        org.groupBy("r.emp_id");

        ComQB<PerfEvaluateTaskUserDTO> comQB = ComQB.build(PerfEvaluateTaskUserModel.class, "u")
                .join(PerfEvaluateTaskBaseModel.class, "t").on("u.task_id", "t.id")
                .leftJoin(EmployeeBaseInfoModel.class, "e").on("e.employee_id", "u.emp_id")
                .leftJoinQ(org, "eo")
                .on("e.employee_id", "eo.emp_id")
                .clearSelect().select("u.*,t.task_name,t.cycle_type,t.cycle_start_date,t.cycle_end_date,e.name empName,eo.org_name,t.created_user initiatorId")
                .setRsType(TaskUserPo.class).whereIn("u.emp_id", query.getEmpIds())
                .whereEq("t.task_status", "published")
                .whereEq("t.company_id", query.getCompanyId())
                .whereLike("t.cycle_start_date", query.getYear())
                .whereEq("t.company_id", query.getCompanyId())
                .whereEq("t.cycle_id", query.getCycleId())
                .whereEq("t.is_deleted", "false")
                .whereEq("t.created_user", query.getCreatedUser())
                .whereEq("u.is_deleted", "false");
        if (StringUtils.isNotBlank(query.getTaskId())) {
            comQB.whereIn("u.task_id", Arrays.asList(query.getTaskId().split(",")));
        }
        if (StringUtils.isNotBlank(query.getTaskIds()) && StringUtils.isBlank(query.getTaskId())) {
            comQB.whereIn("u.task_id", Arrays.asList(query.getTaskIds().split(",")));
        }
        if (StringUtils.isNotBlank(query.getRoleIds())) {
            ComQB role = ComQB.buildDiff(EmpRolePo.class, "v_emp_role")
                    .clearSelect().select("employee_id").setRsType(String.class)
                    .whereIn("id", Arrays.asList(query.getRoleIds().split(",")));
            empIds.addAll(autoBaseDao.listAll(role));
        }
        if (CollectionUtils.isNotEmpty(empIds)) {
            comQB.whereIn("e.employee_id", empIds);
        }
        adminScopeOrgReq(query.getAdminOrgIds(), query.getPerformanceType(), comQB);
        comQB.setPage(query.getPageNo(), query.getPageSize());
        return toReportSeriesDataVO(autoBaseDao.listPage(comQB));
    }

    private PagedList<ReportSeriesDataVO> toReportSeriesDataVO(PagedList<TaskUserPo> taskUserPos) {
        List<ReportSeriesDataVO> list = new ArrayList<>();
        Map<String, ReportSeriesDataVO> map = new HashMap<>(EvaluateTaskStatusEnum.values().length * 2);
        EvaluateTaskStatusEnum.getStatusList().forEach(s -> {
            ReportSeriesDataVO data = ReportSeriesDataVO.builder().name(s).value(BigDecimal.ZERO).build();
            list.add(data);
            map.put(s, data);
        });
        if (CollectionUtils.isNotEmpty(taskUserPos.getData())) {
            taskUserPos.getData().stream()
                    .collect(Collectors.groupingBy(PerfEvaluateTaskUserModel::getTaskStatus))
                    .forEach((taskStatus, users) -> {
                        ReportSeriesDataVO data = map.get(taskStatus);
                        if (data != null) {
                            data.setValue(BigDecimal.valueOf(users.size()));
                        }
                    });
        }
        List<ReportSeriesDataVO> dataList = ReportSeriesDataCalculator.create().addAll(list).calculatePercentage().getDataList();
        PagedList<ReportSeriesDataVO> pages = new PagedList<>();
        pages.addAll(dataList);
        pages.setPageNo(taskUserPos.getPageNo());
        pages.setPageSize(taskUserPos.getPageSize());
        pages.setTotalRow(taskUserPos.getTotalRow());
        pages.setTotalPage(taskUserPos.getTotalPage());
        return pages;
    }

    public List<PerfEvaluateTaskBaseDo> queryNoSetMutualAuditEmp(List<WorkItemQuery> query, TenantId tenantId) {
        List<String> taskIds = query.stream().map(q -> q.getTaskId()).collect(Collectors.toList());
        ComQB comQB = ComQB.build(PerfEvaluateTaskBaseDo.class)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEqReq("company_id", tenantId.getId())
                .whereIn("id", taskIds)
                .whereEq("evaluate_type", EvaluateTypeEnum.CUSTOM.getType());
        return autoBaseDao.listAll(comQB);
    }

    public List<EvalUser> listDrawUpIng(TenantId tenantId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_status", "drawUpIng")
                .whereEq("is_deleted", Boolean.FALSE.toString());
        return autoBaseDao.listAllDomain(comQB, EvalUser.class);
    }


    public List<PerfEvaluateTaskItemScoreRuleVO> queryNoAuditExamMutual(TenantId tenantId, List<String> taskIds, List<String> taskUserIds) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskItemScoreRuleModel.class, "r")
                .leftJoin(PerfEvaluateTaskUserDo.class, "u")
                .appendOn("r.company_id = u.company_id AND r.task_id = u.task_id AND r.task_user_id = u.id")
                .leftJoin(EmployeeBaseInfoDo.class, "e")
                .appendOn("u.emp_id = e.employee_id and u.company_id=e.company_id")
                .clearSelect().select(" DISTINCT r.task_id, r.task_user_id, u.emp_id, e.`name` AS empName")
                .setRsType(PerfEvaluateTaskItemScoreRuleVO.class)
                .whereEq("r.company_id", tenantId.getId())
                .whereEq("r.is_deleted", Boolean.FALSE.toString())
                .whereIn("r.mutual_user_type", BusinessConstant.MUTUAL_USER_TYPES)
                .whereIn("r.task_user_id", taskUserIds);
        return autoBaseDao.listAll(comQB);
    }


    public EvalUser getInterviewTaskUserById(String companyId, String id) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .whereEq("companyId", companyId)
                // .whereEq("task_status", "resultsInterview")
                .whereEq("id", id);
        return autoBaseDao.findDomain(comQB, EvalUser.class);
    }

    public List<PerfEvaluateTaskUserDo> listTaskUserByIds(String companyId, Collection<String> ids) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .whereEq("companyId", companyId)
                .whereInReq("id", ids);
        return autoBaseDao.listAll(comQB);
    }

    public List<PerfEvaluateTaskUserDo> listTaskUserByEmpIds(String companyId, String taskId, List<String> taskUserEmpIds) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .whereEq("companyId", companyId)
                .whereEqReq("task_id", taskId)
                .whereInReq("emp_id", taskUserEmpIds)
                .whereEqReq("is_deleted", "false");
        return autoBaseDao.listAll(comQB);
    }


    //public List<EvalUser> listTaskUserByIds(String companyId, Collection<String> ids) {
    //    ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
    //            .whereEq("companyId", companyId)
    //            .whereInReq("id", ids);
    //    return autoBaseDao.listAll(comQB);
    //}


    public List<EvalUser> listStartingTaskUser(String companyId, List<String> taskIds, List<String> adminOrgIds, Integer performanceType) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .whereEqReq("company_id", companyId)
                .whereInReq("task_id", taskIds)
                .whereEqReq("task_status", "created")//前面已启动了
                .appendWhere("rule_conf_status = 200")
                .appendWhere("rule_conf_error  is null")
                .appendWhere("rule_conf_status = 200")
                .appendWhere("rule_conf_error  is null")
                .whereEqReq("is_deleted", "false");
        //管理范围
        if (CollUtil.isNotEmpty(adminOrgIds)) {
            if (performanceType == 1) {
                comQB.whereIn("org_id", adminOrgIds);
            } else {
                comQB.whereIn("eval_org_id", adminOrgIds);
            }
        }
        return autoBaseDao.listAllDomain(comQB, EvalUser.class);
    }


    public List<String> listDrawUpUserIds(String companyId, String taskId, List<String> adminOrgIds, Integer performanceType) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .clearSelect().setRsType(String.class).select("id")
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_id", taskId)
                .appendWhere("rule_conf_status = 200")
                .appendWhere("rule_conf_error  is null")
                .whereEqReq("task_status", "drawUpIng")
                .whereEqReq("is_deleted", "false");
        //管理范围
        if (CollUtil.isNotEmpty(adminOrgIds)) {
            if (performanceType == 1) {
                comQB.whereIn("org_id", adminOrgIds);
            } else {
                comQB.whereIn("eval_org_id", adminOrgIds);
            }
        }
        return autoBaseDao.listAll(comQB);
    }

    public String findTaskCreateUser(String taskUserId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .clearSelect().select(" created_user ")
                .setRsType(String.class)
                .whereEqReq("id", taskUserId);
        return autoBaseDao.findOne(comQB);
    }

    //考核结果
    public PagedList<EvalResultPo> pagedEvalResult(EvalUserQuery query) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(EmployeeBaseInfoDo.class, "e").appendOn("e.company_id = u.company_id AND e.employee_id = u.emp_id")
                .join(PerfEvaluateTaskBaseDo.class, "b").appendOn("u.company_id = b.company_id and u.task_id = b.id")
                .join(CycleDo.class, "cyc").appendOn("b.company_id = cyc.company_id and b.cycle_id = cyc.id")
                .leftJoin(EmpEvalRuleDo.class, "ru").appendOn("u.company_id = ru.company_id and u.id = ru.emp_eval_id AND ru.is_deleted = 'false'")
                .clearSelect()
               // .select("u.*,e.name,u.final_item_score final_appoint_score,e.is_delete as leaved,b.task_name,b.evaluate_type, u.emp_org_name as org_name , " +
                .select("u.*,e.name,e.is_delete as leaved,b.task_name,b.evaluate_type, u.emp_org_name as org_name , " +
                        "(SELECT GROUP_CONCAT(r.role_name) FROM role r WHERE r.company_id = '" + query.getCompanyId() + "' and r.is_deleted = 'false'\n" +
                        "AND r.id in (SELECT role_id FROM role_ref_emp  WHERE company_id = '" + query.getCompanyId() + "' AND is_deleted = 'false' \n" +
                        "AND emp_id = u.emp_id))   as roleName")
                .select("e.jobnumber")
                .select("ru.create_total_level_type,ru.show_result_type")
                .select("concat(cyc.cycle_start,' 至 ', cyc.cycle_end) cycle_date ")
                .select("e.on_the_job_status,e.post_rank,e.type,e.entry_date")
                .setRsType(EvalResultPo.class)
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                //.whereEq("e.is_delete", Boolean.FALSE.toString())
                .whereEq("b.is_deleted", Boolean.FALSE.toString())
                //.whereIn("u.task_status", Arrays.asList(TalentStatus.FINISHED.getStatus(), TalentStatus.TERMINATED.getStatus()))
                .whereEq("b.cycle_type", query.getCycleType());
        if (StrUtil.isNotBlank(query.getEvalOrgIds())) {
            comQB.whereIn("u.eval_org_id", Arrays.asList(query.getEvalOrgIds().split(",")));
        }
        if (StringUtils.isNotBlank(query.getCycleStartDate()) && StringUtils.isNotBlank(query.getCycleEndDate())) {
            comQB.appendWhere("(b.cycle_start_date BETWEEN " + query.getCycleStartDate() + " and " + query.getCycleEndDate() + " " +
                    "OR b.cycle_end_date BETWEEN " + query.getCycleStartDate() + " and " + query.getCycleEndDate() + ")");
        }
        if (StringUtils.isNotBlank(query.getTaskId())) {
            comQB.whereIn("u.task_id", Arrays.asList(query.getTaskId().split(",")));
        }
        if (StringUtils.isNotBlank(query.getTaskIds())) {
            comQB.whereIn("u.task_id", Arrays.asList(query.getTaskIds().split(",")));
        }
        if (CollUtil.isNotEmpty(query.getTaskIdList())) {
            comQB.whereIn("u.task_id", query.getTaskIdList());
        }
        if (StringUtils.isNotBlank(query.getCycleId())) {
            comQB.whereIn("b.cycle_id", Arrays.asList(query.getCycleId().split(",")));
        }
        if (StringUtils.isNotBlank(query.getEmpId())) {
            comQB.whereIn("u.emp_id", Arrays.asList(query.getEmpId().split(",")));
        }
        if (StringUtils.isNotBlank(query.getLevel())) {
            comQB.whereIn("u.evaluation_level", StrUtil.splitTrim(query.getLevel(), ","));
            //筛选了等级的情况下只查询任务状态在评分之后的(要包含终止状态)， 避免已经出过等级但是被重置回来的任务被筛选出来
            comQB.whereIn("u.task_status", TalentStatus.afterStatusIncludeTerminated(TalentStatus.SCORING));
        }
        adminScopeOrgReq(query.getAdminOrgIds(), query.getPerformanceType(), comQB);
        if (StringUtils.isNotBlank(query.getRoleId())) {
            comQB.appendWhere("EXISTS (SELECT id FROM role_ref_emp WHERE company_id = '" + query.getCompanyId() +
                    "' AND is_deleted = 'false' AND u.emp_id = emp_id AND role_id in" + StringTool.getInStrSql(query.getRoleId()) + "");
        }
//        if (StringUtils.isNotEmpty(query.getOrgId())) {
//            comQB.appendWhere(" EXISTS(select ref.emp_id\n" +
//                    "from emp_ref_org ref\n" +
//                    "         join\n" +
//                    "     emp_organization s on ref.company_id = s.company_id and s.org_id = ref.org_id\n" +
//                    "         join emp_organization p on s.company_id = p.company_id and instr(s.org_code, p.org_code)\n" +
//                    "  where s.status = 'valid'\n" +
//                    "  and p.org_id in " + StringTool.getInStrSql(query.getOrgId()) + "\n" +
//                    "  and ref.emp_id= u.emp_id  and ref.ref_type='org'\n" +
//                    "  and s.company_id = '" + query.getCompanyId() + "'\n)");
//        }
        if (CollUtil.isNotEmpty(query.getOrgIds())) {
            comQB.whereIn("u.org_id", query.getOrgIds());
        }
        if (StringUtils.isNotBlank(query.getRoleId())) {
            comQB.appendWhere(")");
        }
        if (StrUtil.isNotBlank(query.getType())) {
            comQB.whereEq("e.type", query.getType());
        }
        if (CollUtil.isNotEmpty(query.getOnTheJobStatus())) {
            if (StrUtil.isBlank(query.getEmpStatus())) {
                comQB.whereIn("e.on_the_job_status", query.getOnTheJobStatus());
                comQB.whereEq("e.is_delete", Boolean.FALSE.toString());
            } else {
                comQB.appendWhere(" (e.on_the_job_status in " + StringTool.getInStrSql(query.getOnTheJobStatus()) +
                        " or e.is_delete = '" + isLeave(query.getEmpStatus()) + "')");
            }
        }
        if (CollUtil.isNotEmpty(query.getEvalGroupIds())) {
            comQB.whereIn("u.eval_group_id", query.getEvalGroupIds());
        }
        handleEmpStatus(query, comQB);
        appendWhereEntryDate(comQB, query.getEntryDateStart(), query.getEntryDateEnd());
        comQB.groupBy("u.id");
        if (StringUtils.isBlank(query.getOrderField())) {
            comQB.orderByDesc("u.final_score");
        } else if ("evaluationLevel".equals(query.getOrderField())) {
            comQB.leftJoin(GradeStepDo.class, "g")
                    .appendOn("u.evaluation_level = g.name  and u.company_id = g.company_id and g.is_deleted = 'false'");
            String filed = StringUtils.isNotBlank(query.getOrderType()) ? query.getOrderType() : "";
            filed = reverseOrderType(filed);
            comQB.orderBy("COALESCE(g.sort, 9999) " + filed);
        } else if (StrUtil.equals("at_org_code_path", query.getOrderField())) {
            String filed = StringUtils.isNotBlank(query.getOrderType()) ? query.getOrderType() : "";
            comQB.orderBy("u.at_org_code_path" + " " + filed);
        } else {
            String orderField = OrderFieldEnum.getDataFieldByField(query.getOrderField());
            String filed = StringUtils.isNotBlank(query.getOrderType()) ? query.getOrderType() : "";
            comQB.orderBy("u." + orderField + " " + filed);
        }
        comQB.setPage(query.getPageNo(), query.getPageSize());

        comQB.clearSelect()
                //.select("u.*,e.name ,e.avatar,b.task_name,b.evaluate_type, u.emp_org_name as org_name , " +
                .select("u.*,e.name,CASE WHEN e.is_resigned != 0 THEN 1 ELSE 0 END as leaved,b.task_name,b.evaluate_type,b.archive_status ,u.emp_org_name as org_name , " +
                        "(SELECT GROUP_CONCAT(r.role_name) FROM role r WHERE r.company_id = '" + query.getCompanyId() + "' and r.is_deleted = 'false'\n" +
                        "AND r.id in (SELECT role_id FROM role_ref_emp  WHERE company_id = '" + query.getCompanyId() + "' AND is_deleted = 'false' \n" +
                        "AND emp_id = u.emp_id))   as roleName")
                .select("e.jobnumber")
                .select("ru.create_total_level_type,ru.show_result_type")
                .select("concat(cyc.cycle_start,' 至 ', cyc.cycle_end) cycle_date ")
                .select("e.on_the_job_status,e.post_rank,e.type,e.entry_date")
                .setRsType(EvalResultPo.class);

        PagedList<EvalResultPo> resultPos = autoBaseDao.listPage(comQB);
        if (CollUtil.isEmpty(resultPos)) {
            return resultPos;
        }

        List<String> taskUserIds = resultPos.getData().stream().map(BaseResultExcelPo::getId).collect(Collectors.toList());

        ListWrap<RefEvalPo> refEvalDoListWrap = listAllRefEvalPo(query.getCompanyId(), taskUserIds);
        for (EvalResultPo resultPo : resultPos) {
            resultPo.accpetV3Score();
            List<RefEvalPo> refEvalDos1 = refEvalDoListWrap.groupGet(resultPo.getId());
            List<RefEvalPo> sort = CollUtil.sort(refEvalDos1 == null ? Collections.emptyList() : refEvalDos1, Comparator.comparing(RefEvalPo::getRefEvalId));
            resultPo.setRefEvalScores(sort);
            //如果评分中，不显示，评分合计及核定总分
            if (StrUtil.equals(TalentStatus.SCORING.getStatus(), resultPo.getTaskStatus())) {
                resultPo.setOriginalFinalScore(null);
                resultPo.setFinalScore(null);
            }
           // resultPo.setPlusSubtractNull();
        }

//        if (CollUtil.isEmpty(resultPos.getData())) {
//            return resultPos;
//        }
//        List<PerfEvaluateTaskScoreResultDo> results = listResult(new TenantId(query.getCompanyId()), taskUserIds);
//        if (CollUtil.isEmpty(results)) {
//            return resultPos;
//        }
//        Map<String, List<PerfEvaluateTaskScoreResultDo>> taskResult = results.stream().filter(r -> StringUtils.isNotBlank(r.getTaskId())).collect(Collectors.groupingBy(result -> result.getTaskId()));
//        for (EvalResultPo po : resultPos.getData()) {
//            List<PerfEvaluateTaskScoreResultDo> taskResultDos = taskResult.get(po.getTaskId());
//            if (CollUtil.isEmpty(taskResultDos)) {
//                continue;
//            }
//            Map<String, List<PerfEvaluateTaskScoreResultDo>> empResult = taskResultDos.stream().filter(r -> StringUtils.isNotBlank(r.getEmpId())).collect(Collectors.groupingBy(result -> result.getTaskUserId()));
//            List<PerfEvaluateTaskScoreResultDo> resultDos = empResult.get(po.getId());
//            if (CollUtil.isEmpty(resultDos)) {
//                continue;
//            }
//            Map<String, List<PerfEvaluateTaskScoreResultDo>> scoreTypeMap = resultDos.stream().collect(Collectors.groupingBy(PerfEvaluateTaskScoreResultDo::getScorerType));
//            po.noWeightScore(scoreTypeMap, po);
//        }
        return resultPos;
    }

    private String reverseOrderType(String filed) {
        if (StrUtil.equals("asc", filed) || StrUtil.isBlank(filed)) {
            filed = "desc";
        } else {
            filed = "asc";
        }
        return filed;
    }


    /**
     * 构建员工状态筛选 SQL，支持组合条件：on_the_job_status + empStatus（转换为 is_resigned）
     *
     * @param empStatus          员工状态："on_the_job" / "leave"
     * @param onTheJobStatusList 在职状态列表
     * @param qb                 SQL构建器（ComQB）
     */
    private void buildEmployeeStatusWhere(String empStatus, List<String> onTheJobStatusList, ComQB qb) {
        boolean hasOnTheJobStatus = CollUtil.isNotEmpty(onTheJobStatusList);
        boolean hasEmpStatus = StrUtil.isNotBlank(empStatus);

        if (hasOnTheJobStatus && !hasEmpStatus) {
            qb.whereIn("e.on_the_job_status", onTheJobStatusList);
        } else if (hasOnTheJobStatus) {
            qb.appendWhere(" (e.on_the_job_status in " + StringTool.getInStrSql(onTheJobStatusList) +
                    " or e.is_resigned in " + StringTool.getInStrSql(toResignedValue(empStatus)) + "')");
        } else if (hasEmpStatus) {
            qb.appendWhere("e.is_resigned in " + StringTool.getInStrSql(toResignedValue(empStatus)));
        }
    }

    /**
     * 将 empStatus 转换为 is_resigned 字段值（字符串）
     * "on_the_job" => "0"，"leave" => "1"
     */
    private List<String> toResignedValue(String empStatus) {
        return "leave".equalsIgnoreCase(empStatus) ? Arrays.asList("1", "-1") : Arrays.asList("0");
    }

    private void handleEmpStatus(EvalUserQuery query, ComQB comQB) {
        if (StrUtil.isBlank(query.getEmpStatus())) {
            return;
        }
        if (StrUtil.isNotBlank(query.getEmpStatus()) && CollUtil.isEmpty(query.getOnTheJobStatus())) {
            boolean isOnTheJob = "on_the_job".equals(query.getEmpStatus());
            comQB.whereEq("e.is_delete", Boolean.toString(!isOnTheJob));
        }
    }

    private String isLeave(String empStatus) {
        return "leave".equals(empStatus) ? Boolean.TRUE.toString() : Boolean.FALSE.toString();
    }

    private void appendWhereEntryDate(ComQB qb1, String entryDateStart, String entryDateEnd) {
        if (StrUtil.isNotBlank(entryDateStart) && StrUtil.isNotBlank(entryDateEnd)) {
            qb1.appendWhere("e.entry_date BETWEEN '" + entryDateStart + "' and '" + entryDateEnd + "'");
            return;
        }

        if (StrUtil.isNotBlank(entryDateStart)) {
            qb1.whereBigEqReq("e.entry_date", entryDateStart);
            return;
        }
        if (StrUtil.isNotBlank(entryDateEnd)) {
            qb1.whereLowEqReq("e.entry_date", entryDateEnd);
        }
    }

    //考核结果评分人指标
    public List<ScorerItem> listScorerTotal(String companyId, List<String> taskUserIds) {
        ComQB resultComQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "s")
                .clearSelect().select("s.task_user_id,s.scorer_id,s.scorer_type, s.score_weight,s.kpi_type_id,s.kpi_item_id")
                .select("s.score as itemScore,s.plus_score,s.subtract_score,s.audit_status,s.score_level,false as isTotalLevel")
                .setRsType(ScorerItem.class)
                .whereEq("s.company_id", companyId)
                .whereInReq("s.task_user_id", taskUserIds)
                .whereInReq("s.scorer_type", AuditEnum.scoreNotSelfScenes())
                .appendWhere("(s.score_level is null or s.score_level = '') ")
                .whereEq("s.is_deleted", Boolean.FALSE.toString());
        List<ScorerItem> scorerItems = autoBaseDao.listAll(resultComQB);
        log.debug("打印sql日志listScorerTotal.resultComQB:{}", JSONUtil.toJsonStr(resultComQB.getParams()));
        if (CollUtil.isEmpty(scorerItems)) {
            return new ArrayList<>();
        }
        ComQB kpiItemComQB = ComQB.build(PerfEvaluateTaskKpiDo.class)
                .clearSelect()
                .select("company_id,task_user_id, kpi_item_id,item_weight as kpiItemWeight,false as itemAuto")
                .select("kpi_type_id,kpi_type_weight,kpi_type_classify,is_okr")
                .select("max_extra_score")
                .setRsType(ScorerItem.class)
                .whereEq("company_id", companyId)
                .whereInReq("task_user_id", taskUserIds)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        List<ScorerItem> kpiItems = autoBaseDao.listAll(kpiItemComQB);

        ComQB kpiTypeComQB = ComQB.build(EmpEvalKpiTypeDo.class)
                .clearSelect()
                .select("company_id,task_user_id,kpi_type_id, plus_sub_interval")
                .setRsType(ScorerItem.class)
                .whereEq("company_id", companyId)
                .whereInReq("task_user_id", taskUserIds)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        List<ScorerItem> kpiTypeItems = autoBaseDao.listAll(kpiTypeComQB);
        ListWrap<ScorerItem> kpiTypeWrap = new ListWrap<>(kpiTypeItems).asMap(type -> type.getTaskUserId() + "|" + type.getKpiTypeId());
        kpiItems.forEach(item -> {
            ScorerItem typeItem = kpiTypeWrap.mapGet(item.getTaskUserId() + "|" + item.getKpiTypeId());
            if (Objects.nonNull(typeItem)) {
                item.setPlusSubInterval(typeItem.getPlusSubInterval());
            }
        });
        ListWrap<ScorerItem> kpiItemWrap = new ListWrap<>(kpiItems).asMap(item -> item.getTaskUserId() + "|" + item.getKpiItemId());
        scorerItems.forEach(item -> {
            ScorerItem scorerItem = kpiItemWrap.mapGet(item.getTaskUserId() + "|" + item.getKpiItemId());
            if (Objects.nonNull(scorerItem)) {
                item.setKpiItemWeight(scorerItem.getKpiItemWeight());
                item.setKpiTypeWeight(scorerItem.getKpiTypeWeight());
                item.setKpiTypeClassify(scorerItem.getKpiTypeClassify());
            }
        });
        //查询打总等级的数据
        ComQB autoComQB = ComQB.build(PerfEvalTotalLevelResultDo.class, "t")
                .leftJoin(PerfEvaluateTaskUserDo.class, "u")
                .appendOn("t.company_id = u.company_id and t.task_user_id = u.id and u.is_deleted = 'false'")
                .clearSelect()
                .select("task_user_id,audit_status,scorer_id,score_level,true as isTotalLevel")
                .setRsType(ScorerItem.class)
                .whereEq("t.company_id", companyId)
                .whereInReq("u.id", taskUserIds)
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereEq("t.is_deleted", Boolean.FALSE.toString());
        List<ScorerItem> totalLevals = autoBaseDao.listAll(autoComQB);
        log.debug("打印sql日志listScorerTotal.autoComQB:{}", JSONUtil.toJsonStr(autoComQB.getParams()));
        scorerItems.addAll(totalLevals);

        return scorerItems;
    }

    public ListWrap<RefEvalPo> listAllRefEvalPo(String companyId, List<String> taskUserIds) {
        ComQB refEvalQ = ComQB.build(RefEvalDo.class, "rv")
                .leftJoin(PerfEvaluateTaskUserDo.class, "u")
                .appendOn("rv.company_id = u.company_id and rv.ref_eval_id = u.id")
                .leftJoin(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("u.company_id = b.company_id and u.task_id = b.id")
                .clearSelect().select("rv.*,(case when (u.is_deleted = 'true' or b.is_deleted = 'true') then 1 when ((b.task_status = 'terminated' and u.task_status = 'scoring') or u.task_status = 'terminated')then 2 when u.task_status in('drawUpIng','confirming','confirmed','changing','scoring') then 3 end) as unusualType,b.task_name")
                .setRsType(RefEvalPo.class)
                .whereEqReq("rv.company_id", companyId)
                .whereInReq("rv.mainEvalId", taskUserIds);
        List<RefEvalPo> refEvalDos = autoBaseDao.listAll(refEvalQ);
        ListWrap<RefEvalPo> refEvalDoListWrap = new ListWrap<>(refEvalDos).groupBy(refEvalDo -> refEvalDo.getMainEvalId());
        return refEvalDoListWrap;
    }

    //替换PerfEvaluateTaskUserManager.queryAllScoreTaskUser
    public List<String> listAutoEnterScoreTaskUser(String companyId) {
        List<String> needEnterScoreIds = new ArrayList<>();
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(EmpEvalRuleDo.class, "e").appendOn(" u.company_id = e.company_id and u.id = e.emp_eval_id  and e.is_deleted='false'  ")
                .join(CycleDo.class, "c").appendOn(" u.company_id = c.company_id and u.cycle_id = c.id  ")
                .setRsType(AutoEnterScorePo.class)
                .clearSelect().select(" u.id as taskUserId, e.enter_score,c.cycle_start, c.cycle_end")
                .appendWhere("u.temp_task=0")//2.0仅运行2.0的新任务
                .whereEqReq(" u.company_id", companyId)
                .appendWhere(" u.is_deleted = 'false' AND e.is_deleted = 'false' AND u.task_status = 'confirmed' ");
        List<AutoEnterScorePo> pos = autoBaseDao.listAll(comQB);
        pos.stream().forEach(po -> {
            if (po.needAutoEnterScore()) {
                needEnterScoreIds.add(po.getTaskUserId());
            }
        });
        String date = new DateTime().toString("yyyy-MM-dd");
        ComQB sqlBuilder = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .leftJoin(PerfEvaluateTaskBaseDo.class, "b").appendOn("b.id = u.task_id and b.company_id = u.company_id")
                .clearSelect().select("u.id as taskUserId")
                .setRsType(String.class)
                .appendWhere("u.temp_task=0")//2.0仅运行2.0的新任务
                .appendWhere("u.is_deleted = 'false' AND b.is_deleted = 'false' AND u.task_status = 'confirmed' ")
                .whereEqReq(" u.company_id", companyId)
                .appendWhere(" b.enter_score_method = 'auto'")
                .appendWhere(" IF (b.score_start_rule_type = 'before', DATEDIFF(b.cycle_end_date, '" + date +
                        "') <= b.score_start_rule_day, DATEDIFF('" + date + "', b.cycle_end_date) >= b.score_start_rule_day)");

        List<String> oldPos = autoBaseDao.listAll(sqlBuilder);
        needEnterScoreIds.addAll(oldPos);
        return needEnterScoreIds;
    }

    public PagedList<String> pagedFinishedTaskUser(String companyId, String taskId, boolean isFinished, Integer pageNo, Integer pageSize) {
        ComQB sqlBuilder = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .leftJoin(PerfEvaluateTaskBaseDo.class, "b").appendOn("b.id = u.task_id and b.company_id = u.company_id")
                .clearSelect().select("u.id as taskUserId")
                .setRsType(String.class)
                .appendWhere("u.temp_task=0")//2.0仅运行2.0的新任务
                .appendWhere("u.is_deleted = 'false' AND b.is_deleted = 'false' ")
                .whereInReq("u.task_status", isFinished ? TalentStatus.finishedStage() : TalentStatus.noFinishedStage())
                .whereEqReq(" u.company_id", companyId)
                .whereEq("u.task_id", taskId)
                .setPage(pageNo, pageSize);

        return autoBaseDao.listPage(sqlBuilder);
    }

    ////替换PerfEvaluateTaskUserManager.queryAllScoreTaskUser
    //public List<EvalUser> listAutoEnterScoreTaskUserOld(String companyId) {
    //    String date = new DateTime().toString("yyyy-MM-dd");
    //    ComQB sqlBuilder = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
    //            .leftJoin(PerfEvaluateTaskBaseDo.class, "b").appendOn("b.id = u.task_id and b.company_id = u.company_id")
    //            .clearSelect().select("u.company_id, u.id , u.task_id,u.org_id,u.emp_id")
    //            .setRsType(AutoEnterScorePo.class)
    //            .appendWhere("u.is_deleted = 'false' AND b.is_deleted = 'false' AND u.task_status = 'confirmed' ")
    //            .whereEqReq(" u.company_id", companyId)
    //            .appendWhere(" b.enter_score_method = 'auto'")
    //            .appendWhere(" IF (b.score_start_rule_type = 'before', DATEDIFF(b.cycle_end_date, '" + date +
    //                    "') <= b.score_start_rule_day, DATEDIFF('" + date + "', b.cycle_end_date) >= b.score_start_rule_day)");
    //    List<AutoEnterScorePo> oldPos = autoBaseDao.listAll(sqlBuilder);
    //    return autoBaseDao.listAllDomain(sqlBuilder, EvalUser.class);
    //}

    public List<PerfEvaluateTaskScoreResultDo> listResult(TenantId tenantId, List<String> taskUserIds) {
        if (CollectionUtils.isEmpty(taskUserIds)) {
            return new ArrayList<>();
        }
        ComQB queryBuilder = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .whereEq("company_id", tenantId.getId())
                .whereIn("task_user_id", taskUserIds)
                .whereEq("audit_status", "pass")
                .whereEq("is_deleted", "false");
        return autoBaseDao.listAll(queryBuilder);
    }

    public Map<String, List<EvalUserSummaryPo>> listEvalUserSummary(TenantId tenantId, String taskUserId) {
        ComQB comQB = ComQB.build(EvalScoreSummaryDo.class, "s")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn("s.company_id=e.company_id and s.created_user=e.employee_id")
                .clearSelect()
                .select("s.company_id,s.task_user_id,s.score_type,s.summary,s.att_url,e.employee_id as emp_id,e.name,e.avatar,s.created_time")
                .setRsType(EvalUserSummaryPo.class)
                .whereEqReq("s.company_id", tenantId.getId())
                .whereEqReq("s.taskUser_id", taskUserId)
                .whereEq("s.is_deleted", Boolean.FALSE.toString())
                .groupBy("s.score_type, s.created_user");
        List<EvalUserSummaryPo> summaryPos = autoBaseDao.listAll(comQB);
        if (CollUtil.isEmpty(summaryPos)) {
            return new HashMap<>();
        }
        Map<String, List<EvalUserSummaryPo>> summaryMap = summaryPos.stream()
                .filter(s -> StringUtils.isNotBlank(s.getScoreType()) && StrUtil.isNotBlank(s.getSummary()))
                .collect(Collectors.groupingBy(s -> s.getScoreType()));
        return summaryMap;
    }

    public Map<String, List<EvalUserSummaryPo>> listEvalUserSummaryV3(TenantId tenantId, String taskUserId) {
        ComQB comQB = ComQB.build(EmpEvalScorerNodeDo.class)
                .clearSelect()
                .setRsType(EvalUserSummaryPo.class)
                .select("company_id,task_user_id,scorer_type as scoreType,total_comment as summary,score_att_url as attUrl,scorer_id as emp_id,scorer_name as name,scorer_avatar as avatar,max(handler_time) as createdTime")
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("taskUser_id", taskUserId)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .appendWhere("((transfer_type is null) OR (transfer_type = 'transfer' AND transfer_from IS NOT NULL AND transfer_to IS NULL) OR (transfer_type = 'skip' AND transfer_from IS NULL AND transfer_to IS NOT NULL))")
                .groupBy("scorer_type, scorer_id")
                .orderByDesc("handler_time");
        List<EvalUserSummaryPo> summaryPos = autoBaseDao.listAll(comQB);
        if (CollUtil.isEmpty(summaryPos)) {
            return new HashMap<>();
        }
        return summaryPos.stream()
                .filter(s -> StringUtils.isNotBlank(s.getScoreType()) && StrUtil.isNotBlank(s.getSummary()))
                .collect(Collectors.groupingBy(EvalUserSummaryPo::getScoreType));
    }

    public PagedList<EvalInterviewPo> pagedEvalInterview(EvalInterviewQuery query) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .leftJoin(PerfEvaluateTaskCoachDo.class, "c")
                .appendOn("u.company_id=c.company_id and u.id=c.task_user_id")
                .leftJoin(EmployeeBaseInfoDo.class, "e")
                .appendOn("u.company_id=e.company_id and u.emp_id=e.employee_id");
        if (StrUtil.isNotBlank(query.getCycleId())) {
            comQB.leftJoin(PerfEvaluateTaskBaseDo.class, "b")
                    .appendOn("b.company_id=u.company_id and b.id=u.task_id")
                    .whereEq("b.is_deleted", Boolean.FALSE.toString())
                    .whereEq("b.cycle_id", query.getCycleId());
        }
        comQB.clearSelect()
                .select("u.task_id,u.id task_user_id ,u.task_status ,u.final_score,u.evaluation_level,e.employee_id emp_id," +
                        "e.name emp_name,e.avatar,count(c.id) coach_num,max(c.created_time) last_coach_time")
                .select("e.on_the_job_status,e.post_rank,e.type,e.entry_date")
                .setRsType(EvalInterviewPo.class);
        if (StrUtil.isNotBlank(query.getTaskId())) {
            comQB.whereIn("u.task_id", Arrays.asList(query.getTaskId().split(",")));
        }
        if (StrUtil.isNotBlank(query.getEmpId())) {
            comQB.whereIn("u.emp_id", Arrays.asList(query.getEmpId().split(",")));
        }
        if (StrUtil.isNotBlank(query.getEvaluationLevel())) {
            comQB.whereIn("u.evaluation_level", Arrays.asList(query.getEvaluationLevel().split(",")));
        }
        if (StrUtil.isNotBlank(query.getType())) {
            comQB.whereEq("e.type", query.getType());
        }
        if (Objects.nonNull(query.getOnTheJobStatus())) {
            comQB.whereEq("e.on_the_job_status", query.getOnTheJobStatus());
        }
        if (StrUtil.isNotBlank(query.getEntryDateStart()) && StrUtil.isNotBlank(query.getEntryDateEnd())) {
            comQB.appendWhere("e.entry_date BETWEEN '" + query.getEntryDateStart() + "' and '" + query.getEntryDateEnd() + "'");
        }
        comQB.whereEqReq("u.company_id", query.getCompanyId())
                .whereEq("u.created_user", query.getCreatedUser())
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .groupBy("u.id");
        if (Objects.nonNull(query.getCoachNum())) {
            if (query.getCoachNum() == 3) {
                comQB.having(" coach_num >= " + query.getCoachNum() + " ");
            } else {
                comQB.having(" coach_num =" + query.getCoachNum() + " ");
            }
        }
        adminScopeOrgReq(query.getAdminOrgIds(), query.getPerformanceType(), comQB);
        if (StrUtil.isNotBlank(query.getSortField())) {
            String tableAs = query.macthTableAs(query.getSortField());
            comQB.orderBy("  " + tableAs + query.getSortField() + "  " + query.getSortOrder());
        }
        comQB.setPage(query.getPageNo(), query.getPageSize());
        return autoBaseDao.listPage(comQB);
    }

//    public Boolean hasWaitAppeal(TenantId companyId, String taskUserId) {
//        ComQB comQB = ComQB.build(PerfEvaluateTaskAppealInfoDo.class, "i")
//                .join(PerfEvaluateTaskAppealBatchDo.class, "b")
//                .appendOn(" i.company_id = b.company_id and i.task_id = b.task_id and i.emp_id = b.emp_id ")
//                .clearSelect().select("count(i.id)").setRsType(Integer.class)
//                .whereEqReq("i.company_id", companyId.getId())
//                .whereEqReq("b.task_user_id", taskUserId)
//                .whereEq("i.is_deleted", Boolean.FALSE.toString())
//                .whereEq("b.is_deleted", Boolean.FALSE.toString())
//                .appendWhere(" i.appeal_result is null");
//        Integer cnt = autoBaseDao.findOne(comQB);
//        return cnt > 0;
//    }

    public Boolean hasWaitAppeal(TenantId companyId, String taskUserId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskAppealBatchDo.class, "i")
                .clearSelect().select("count(i.id)").setRsType(Integer.class)
                .whereEqReq("i.company_id", companyId.getId())
                .whereEqReq("i.task_user_id", taskUserId)
                .whereEq("i.is_deleted", Boolean.FALSE.toString())
                .whereEq("i.appeal_status", "wait");
        Integer cnt = autoBaseDao.findOne(comQB);
        return cnt > 0;
    }


    public List<String> listAutoAffirmTaskUser(String companyId) {
        List<String> taskUserIds = new ArrayList<>();
        //历史任务
        ComQB comQB = ComQB.build(PerfEvaluateTaskBaseDo.class, "b")
                .join(PerfEvaluateTaskUserDo.class, "u")
                .appendOn("b.id=u.task_id and b.company_id=u.company_id")
                .clearSelect()
                .select(" u.id")
                .setRsType(String.class)
                .whereEq("b.auto_result_affirm", Boolean.TRUE.toString())
                .whereBigEq("b.created_time", "2022-01-21 00:00:00")
                .whereEq("b.is_deleted", Boolean.FALSE.toString())
                .whereEq("u.task_status", TalentStatus.RESULTS_AFFIRMING.getStatus())
                .whereEq("b.company_id", companyId)
                .appendWhere(" datediff('" + DateUtils.formatDate(new Date(), "yyyy-MM-dd") + "', u.in_result_affirm_time) >= b.auto_result_affirm_day");
        taskUserIds = autoBaseDao.listAll(comQB);

        //兼容2.0任务
        ComQB v2Task = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(EmpEvalRuleDo.class, "r")
                .appendOn(" u.company_id = r.company_id and u.id = r.emp_eval_id and r.is_deleted='false'")
                .setRsType(AutoAffirmPo.class)
                .clearSelect().select(" id as taskUserId,in_result_affirm_time,r.confirm_result,r.dead_line_conf ")
                .whereEqReq("u.company_id", companyId)
                .whereEq("u.task_status", TalentStatus.RESULTS_AFFIRMING.getStatus())
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereBigEq("u.created_time", "'2022-01-21 00:00:00'")
                .appendWhere(" JSON_EXTRACT (r.confirm_result, '$.open') = 1 and JSON_EXTRACT (r.confirm_result, '$.auto') = 1 ");
        List<AutoAffirmPo> pos = autoBaseDao.listAll(v2Task);
        if (pos.isEmpty()) {
            return taskUserIds;
        }
        for (AutoAffirmPo po : pos) {
            //开启了截止时间，按截止日期配置来
            if (po.openDeadLine()) {
                continue;
            }
            if (po.outAffirmDay()) {
                taskUserIds.add(po.getTaskUserId());
            }
        }
        return taskUserIds.stream().distinct().collect(Collectors.toList());
    }

    public String getEvalUserId(TenantId tenantId, String taskBaseId, String empId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_id", taskBaseId)
                .whereEqReq("emp_id", empId);
        PerfEvaluateTaskUserDo taskUserDo = autoBaseDao.findOne(comQB);
        return taskUserDo.getId();
    }

    public String getLastEvalUserId(TenantId tenantId, String empId) {

        ComQB comQB = ComQB.build(PerfEvaluateTaskBaseDo.class, "b")
                .join(PerfEvaluateTaskUserDo.class, "tu").appendOn("b.company_id=tu.company_id and b.id=tu.task_id")
                .join(EmpEvalKpiTypeDo.class, "type").appendOn("tu.company_id = type.company_id and tu.id=type.task_user_id")
                .clearSelect().setRsType(String.class).select("tu.id")
                .whereEqReq("tu.company_id", tenantId.getId())
                .whereEqReq("tu.emp_id", empId)
                .whereEq("tu.is_deleted", Boolean.FALSE.toString())
                .whereEq("b.is_deleted", Boolean.FALSE.toString())
                .orderByDesc("tu.created_time").limit(0, 1);
        String lastTaskUserId = autoBaseDao.findOne(comQB);
        return lastTaskUserId;
    }

    public String getCompanyId(String taskUserId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .whereEqReq("id", taskUserId);
        PerfEvaluateTaskUserDo taskUserDo = autoBaseDao.findOne(comQB);
        return taskUserDo.getCompanyId();
    }

    public EvalUser getBaseEvalUserById(String taskUserId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .whereEqReq("id", taskUserId)
                .appendWhere("is_deleted ='false'");
        return autoBaseDao.findDomain(comQB, EvalUser.class);
    }

    public EvalUser getBaseEvalUser(TenantId companyId, String taskUserId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "base_u")
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("id", taskUserId)
                .appendWhere("is_deleted ='false'");
        return autoBaseDao.findDomain(comQB, EvalUser.class);
    }


    public List<EvalUser> listBaseEvalUser(TenantId companyId, List<String> taskUserIds) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "base_u")
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("id", taskUserIds)
                .appendWhere("is_deleted ='false'");
        return autoBaseDao.listAllDomain(comQB, EvalUser.class);
    }

    public List<EvalUser> listBaseEvalUserByEmpIdsInTask(TenantId companyId, String taskId, List<String> empIds) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "base_u")
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("emp_id", empIds)
                .whereEqReq("task_id", taskId)
                .appendWhere("is_deleted ='false'");
        return autoBaseDao.listAllDomain(comQB, EvalUser.class);
    }

    public ListWrap<EvalUser> listByEmpEvalUser(TenantId companyId, List<String> empIds) {
        if (CollUtil.isEmpty(empIds)) {
            return new ListWrap<>();
        }
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "base_u")
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("emp_id", empIds)
                .appendWhere("is_deleted ='false'");
        List<EvalUser> users = autoBaseDao.listAllDomain(comQB, EvalUser.class);
        return new ListWrap<>(users).groupBy(evalUser -> evalUser.getEmpId() + "|" + evalUser.getOrgId());
    }

    public List<EvalUser> listBaseEvalUserAndRule(TenantId companyId, List<String> taskUserIds) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "base_u")
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("id", taskUserIds)
                .whereNotEq("task_status", TalentStatus.TERMINATED.getStatus())
                .appendWhere("is_deleted ='false'");
        List<EvalUser> evalUsers = autoBaseDao.listAllDomain(comQB, EvalUser.class);
        if (CollUtil.isEmpty(evalUsers)) {
            return evalUsers;
        }
        ComQB ruleQb = ComQB.build(EmpEvalRuleDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("emp_eval_id", taskUserIds)
                .appendWhere("is_deleted ='false'");
        List<EmpEvalRule> rules = autoBaseDao.listAllDomain(ruleQb, EmpEvalRule.class);
        if (CollUtil.isEmpty(rules)) {
            return evalUsers;
        }
        Map<String, EmpEvalRule> evalRuleMap = CollUtil.toMap(rules, new HashMap<>(), r -> r.getEmpEvalId());
        for (EvalUser evalUser : evalUsers) {
            evalUser.setEmpEvalRule(evalRuleMap.get(evalUser.getId()));
        }
        return evalUsers;
    }

    public List<EvalUser> listTerminatedEvalUser(TenantId companyId, List<String> taskUserIds) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "base_u")
                .leftJoin(PerfEvaluateTaskBaseDo.class, "b").appendOn("base_u.company_id = b.company_id and base_u.task_id = b.id")
                .clearSelect().select("base_u.*,b.task_name").setRsType(EvalUser.class)
                .whereEqReq("base_u.company_id", companyId.getId())
                .whereInReq("base_u.id", taskUserIds)
                .whereEq("base_u.task_status", TalentStatus.TERMINATED.getStatus())
                .appendWhere("base_u.is_deleted ='false'")
                .whereEq("b.is_deleted", Boolean.FALSE.toString());
        return autoBaseDao.listAll(comQB);
    }

    public EvalUser getDelBaseEvalUser(TenantId companyId, String taskUserId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "base_u")
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("id", taskUserId);
        return autoBaseDao.findDomain(comQB, EvalUser.class);
    }

    public EvalUser getBothBaseUser(String companyId, String taskId, String empId, String taskUserId) {
        ComQB<PerfEvaluateTaskUserDo> comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "bu")
                .whereEqReq("company_id", companyId)
                .whereEq("task_id", taskId)
                .whereEq("emp_id", empId)
                .whereEq("id", taskUserId)
                .appendWhere("is_deleted = 'false'");
        EvalUser domain = autoBaseDao.findDomain(comQB, EvalUser.class);
        return domain;
    }

    //我协同的任务=我参与的
    public List<PerfEvaluateTaskUserDo> listMyCotaskingEval(String companyId, String taskId, String opEmpId) {
        ComQB qb = ComQB.build(CompanyMsgCenterDo.class, "m")
                .leftJoin(PerfEvaluateTaskUserDo.class, "u").appendOn(" m.link_id = u.id and  m.company_id = u.company_id")
                .clearSelect().setRsType(PerfEvaluateTaskUserDo.class)
                .select("distinct u.*")
                .appendWhere(" u.is_deleted = 'false'  and m.handler_status = 'false'  ")
                .whereEqReq(" u.company_id", companyId)
                .whereEqReq(" u.taskId", taskId)
                .whereEqReq(" m.emp_id", opEmpId)
                .orderByAsc("m.created_time");
        return autoBaseDao.listAll(qb);
    }

    public Set<String> listFinishedScoreType(String companyId, String taskUserId, String opEmpId) {
        Set<String> rs = new HashSet<>();
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .clearSelect().setRsType(String.class)
                .select("DISTINCT scorer_type")
                .whereEq("company_id", companyId)
                .whereEq("task_user_id", taskUserId)
                .appendWhere("is_deleted='false' and audit_status='pass' and score is not null ")//这里score is not null需要校验确实操作过评分的人才可以重新评分，否则AB或签场景，各占100权重，实际A平分了 b没评，到校准环节B还可以操作重新评分
                .whereEq("scorer_id", opEmpId)
                .appendWhere("scorer_type NOT LIKE 'total_%' " +
                        " AND scorer_type not in ('final_result_audit','modify_item_audit','finish_value_audit')");
        comQB.clearSelect().setRsType(String.class).select("DISTINCT scorer_type");
        List<String> itemScoreTypes = autoBaseDao.listAll(comQB);
        rs.addAll(itemScoreTypes);

        ComQB typCom = ComQB.build(PerfEvalTypeResultDo.class)
                .clearSelect().setRsType(String.class)
                .select("DISTINCT scorer_type")
                .whereEq("company_id", companyId)
                .whereEq("task_user_id", taskUserId)
                .appendWhere("is_deleted='false' and audit_status='pass'")
                .whereEq("scorer_id", opEmpId);
        List<String> types = autoBaseDao.listAll(typCom);
        rs.addAll(types);
        return rs;
    }

    public boolean hasAuditedResults(String companyId, String taskUserId, String scorerType, String scorerId) {
        ComQB queryBuilder = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .clearSelect().select("count(1) cnt").setRsType(Integer.class)
                .whereEq("company_id", companyId)
                .whereEq("task_user_id", taskUserId)
                .whereEq("scorer_type", scorerType)
                .whereEq("audit_status", "pass")
                //.whereEq("scorer_id", scorerId)
                .whereEq("is_deleted", "false");
        //只查普通指标
        //String sql = String.format("kpi_item_id in (select DISTINCT kpi_item_id from perf_evaluate_task_kpi WHERE kpi_type_classify not in ('plus','subtract') AND task_user_id = '%s' and is_deleted = 'false')", taskUserId);
        //queryBuilder.appendWhere(sql);
        Integer row = autoBaseDao.findOne(queryBuilder);
        return row > 0;
    }

    public List<EvalUserOfTask> listEvalUserOfTask(String companyId, List<String> taskUserIds) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("u.company_id=b.company_id and u.task_id=b.id")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn("u.emp_id=e.employee_id and u.company_id=e.company_id")
                .clearSelect()
                .select("b.id as task_id ,b.task_name,u.id as task_user_id ,u.emp_id,u.company_id ,e.name as emp_name,b.cycle_id ")
                .setRsType(EvalUserOfTask.class)
                .whereInReq("u.id", taskUserIds)
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereEqReq("u.company_id", companyId);
        return autoBaseDao.listAll(comQB);
    }

    public List<EvalUserOfTask> listEvalUserOfTaskByTaskId(String companyId, String taskId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("u.company_id=b.company_id and u.task_id=b.id")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn("u.emp_id=e.employee_id and u.company_id=e.company_id")
                .clearSelect()
                .select("b.id as task_id ,b.task_name,u.id as task_user_id ,u.emp_id,u.company_id ,e.name as emp_name ")
                .setRsType(EvalUserOfTask.class)
                .whereEqReq("u.task_id", taskId)
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereEqReq("u.company_id", companyId);
        return autoBaseDao.listAll(comQB);
    }

    public List<PerfEvalTypeResult> listTypeResultByScoreType(TenantId companyId, String taskUserId, String scene) {
        if (StrUtil.isEmpty(scene)) {
            throw new RuntimeException("where条件scene必传");
        }

        ComQB<EvalScoreResult> typeQb = ComQB.build(PerfEvalTypeResultDo.class)
                .whereEqReq("company_id", companyId.getId())
                //.whereEqReq("task_id", taskId)
                .whereEqReq("task_user_id", taskUserId)
                //.whereEq("scorer_id", null)
                .whereEq("is_deleted", "false")
                .whereEq("scorerType", scene)
                .appendWhere("( audit_status is null  or audit_status = 'reject' )");

        List<PerfEvalTypeResult> typeRs = this.autoBaseDao.listAllDomain(typeQb, PerfEvalTypeResult.class);
        return typeRs;
    }

    public List<EvalScoreResult> fixListResultByScoreType(TenantId companyId, String taskUserId, String scene) {
        ComQB<EvalScoreResult> typeQb = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("is_deleted", "false")
                .whereEq("scorer_type", scene);
        List<EvalScoreResult> scoreResults = this.autoBaseDao.listAllDomain(typeQb, EvalScoreResult.class);
        return scoreResults;
    }

    public List<EvalScoreResult> listResultByScoreTypeAndAuditStatus(TenantId companyId, String taskUserId) {
        ComQB<EvalScoreResult> typeQb = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("is_deleted", "false")
                .whereLikeReq("scorer_type", "score")
                .appendWhere(" audit_status is null")
                .groupBy("scorer_type,scorer_id");

        List<EvalScoreResult> scoreResults = this.autoBaseDao.listAllDomain(typeQb, EvalScoreResult.class);
        return scoreResults;
    }

    public List<EvalScoreResult> listResultByScoreTypes(TenantId companyId, String taskUserId, List<String> scenes, boolean isPassed) {
        ComQB scoreComQb = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "r")
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .appendWhere("is_deleted='false'")
                .whereInReq("scorerType", scenes);
        List<EvalScoreResult> scoreResults = autoBaseDao.listAllDomain(scoreComQb, EvalScoreResult.class);
        List<EvalScoreResult> results = scoreResults.stream().filter(r -> !"transferred".equals(r.getAuditStatus())).filter(s -> {
            return isPassed ? "pass".equals(s.getAuditStatus()) : (Objects.isNull(s.getAuditStatus()) || "reject".equals(s.getAuditStatus()));
        }).collect(Collectors.toList());
        for (EvalScoreResult scoreResult : results) {
            scoreResult.approvalOrderIfNull(1);
        }
        return results;
    }


    public ListWrap<EvalScoreResult> listResultByScoreTypes(TenantId companyId, Collection<String> taskUserIds, List<String> scenes, boolean isPassed) {
        ComQB scoreComQb = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "r")
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_user_id", taskUserIds)
                .appendWhere("is_deleted='false'")
                .whereInReq("scorerType", scenes);
        List<EvalScoreResult> scoreResults = autoBaseDao.listAllDomain(scoreComQb, EvalScoreResult.class);
        List<EvalScoreResult> results = scoreResults.stream().filter(r -> !"transferred".equals(r.getAuditStatus())).filter(s -> {
            return isPassed ? "pass".equals(s.getAuditStatus()) : (Objects.isNull(s.getAuditStatus()) || "reject".equals(s.getAuditStatus()));
        }).collect(Collectors.toList());
        for (EvalScoreResult scoreResult : results) {
            scoreResult.approvalOrderIfNull(1);
        }
        return new ListWrap<>(results).groupBy(r -> r.getTaskUserId());
    }

    public List<EvalScoreResult> listResultByScoreTypes(TenantId companyId, String taskUserId, List<String> scenes) {
        ComQB scoreComQb = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "r")
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_user_id", taskUserId)
                //.whereEq("scorerId", scorerId)
                .appendWhere("is_deleted='false'")
                .whereInReq("scorerType", scenes);
        List<EvalScoreResult> scoreResults = autoBaseDao.listAllDomain(scoreComQb, EvalScoreResult.class);
        for (EvalScoreResult scoreResult : scoreResults) {
            scoreResult.approvalOrderIfNull(1);
        }
        return scoreResults;
    }

    public List<EvalAudit> listAuditByScene(TenantId companyId, String taskUserId, String scene) {
        if (StrUtil.isEmpty(scene)) {
            throw new RuntimeException("where条件scene必传");
        }
        return listAuditByScenes(companyId, taskUserId, Arrays.asList(scene));
    }

    public List<EvalAudit> listAuditByScenes(TenantId companyId, String taskUserId, List<String> scenes) {
        ComQB comQB = ComQB.build(TaskAuditDo.class, "aut")
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("taskUserId", taskUserId)
                .whereIn("scene", scenes)
                .whereEq("is_deleted", "false")
                .orderByAsc("approval_order");
        List<EvalAudit> audits = autoBaseDao.listAllDomain(comQB, EvalAudit.class);
        for (EvalAudit audit : audits) {
            if (audit.getApprovalOrder() == null) {
                audit.setApprovalOrder(1);
            }
        }
        return audits;
    }

    public ListWrap<EvalAudit> listAuditByScenes(TenantId companyId, Collection<String> taskUserIds, List<String> scenes) {
        ComQB comQB = ComQB.build(TaskAuditDo.class, "aut")
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("taskUserId", taskUserIds)
                .whereIn("scene", scenes)
                .whereEq("is_deleted", "false")
                .orderByAsc("approval_order");
        List<EvalAudit> audits = autoBaseDao.listAllDomain(comQB, EvalAudit.class);
        for (EvalAudit audit : audits) {
            if (audit.getApprovalOrder() == null) {
                audit.setApprovalOrder(1);
            }
        }
        return new ListWrap<>(audits).groupBy(u -> u.getTaskUserId());
    }

    public EvalAudit getAudit(TenantId companyId, String taskUserId, String scene, String scorerId, Integer approveOrder) {
        ComQB comQB = ComQB.build(TaskAuditDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("taskUserId", taskUserId)
                .whereEqReq("scene", scene)
                .whereEq("approver_type", "user")
                .whereEqReq("approver_info", scorerId)
                .whereEq("is_deleted", "false")
                .whereEq("status", "dispatched")
                .whereEq("approval_order", approveOrder)
                .orderByDesc("approval_order")
                .limit(0, 1);
        EvalAudit audit = autoBaseDao.findDomain(comQB, EvalAudit.class);
        if (Objects.nonNull(audit) && audit.getApprovalOrder() == null) {
            audit.setApprovalOrder(1);
        }
        return audit;
    }

    public List<EvalUser> listEvalUser(String companyId, String taskId, String taskUserIds) {
        List<String> userIds = null;
        if (StringUtils.isNotEmpty(taskUserIds)) {
            String[] split = taskUserIds.split(",");
            userIds = Arrays.asList(split);
        }
        return this.listEvalUser(companyId, taskId, userIds);
    }

    public List<EvalUser> listEvalUser(String companyId, String taskId, List<String> taskUserIds) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .whereEqReq("company_id", companyId)
                .whereIn("id", taskUserIds)
                .whereEq("task_id", taskId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        return autoBaseDao.listAllDomain(comQB, EvalUser.class);
    }

    public List<EvalUser> listEvalUserByTaskStatus(String companyId, String taskId, String taskStatus) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .whereEqReq("company_id", companyId)
                .whereEq("task_id", taskId)
                .whereEq("task_status", taskStatus)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        return autoBaseDao.listAllDomain(comQB, EvalUser.class);
    }


    public List<EvalUser> listUserByCycle(String companyId, String cycleId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("cycle_id", cycleId)
                .whereEq("is_deleted", Boolean.FALSE.toString());

        List<EvalUser> evalUsers = autoBaseDao.listAllDomain(comQB, EvalUser.class);

        for (EvalUser evalUser : evalUsers) {
            EmpEvalRule empEvalRule = empEvalRuleRepo.getEmpEvalRule(new TenantId(companyId), evalUser.getId());
            evalUser.setEmpEvalRule(empEvalRule);
        }
        return evalUsers;
    }

    public List<EvalUser> listEvalUserByTask(String companyId, String taskId, int pageNo, List<String> adminOrgIds, Integer performanceType) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .whereEqReq("u.company_id", companyId)
                .whereEq("u.task_id", taskId)
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .limit((pageNo - 1) * 100, 100);
        adminScopeOrgReq(adminOrgIds, performanceType, comQB);
        List<EvalUser> evalUsers = autoBaseDao.listAllDomain(comQB, EvalUser.class);

        for (EvalUser evalUser : evalUsers) {
            EmpEvalRule empEvalRule = empEvalRuleRepo.getEmpEvalRule(new TenantId(companyId), evalUser.getId());
            evalUser.setEmpEvalRule(empEvalRule);
        }
        return evalUsers;
    }

    public List<KpiEmp> listRatersByOrderLevel(TenantId companyId, String userId, String scene, Integer orderLevel) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "r")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn("r.company_id=e.company_id and r.scorer_id=e.employee_id")
                .clearSelect().select("e.employee_id  as emp_id,  e.name as emp_name, e.ding_user_id ex_user_id , e.avatar")
                .setRsType(KpiEmp.class)
                .whereEqReq("r.company_id", companyId.getId())
                .whereEqReq("r.task_user_id", userId)
                .whereEq("r.scorer_type", scene)
                .appendWhere("(audit_status != 'transferred' or audit_status is null)")
                .whereEq("r.is_deleted", Boolean.FALSE.toString())
                .whereEq("e.is_delete", Boolean.FALSE.toString())
                .whereEq("r.approval_order", orderLevel);
        return autoBaseDao.listAll(comQB);
    }

    public List<EvalScoreResult> listWaitDispatchResult(TenantId companyId, String taskUserId, String scene) {
        ComQB scoreComQb = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "r")
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .appendWhere("is_deleted='false'")
                .appendWhere("audit_status='waitDispatch'")
                .whereEqReq("scorerType", scene);
        List<EvalScoreResult> scoreResults = autoBaseDao.listAllDomain(scoreComQb, EvalScoreResult.class);
        if (CollUtil.isEmpty(scoreResults)) {
            return scoreResults;
        }
        for (EvalScoreResult scoreResult : scoreResults) {
            scoreResult.approvalOrderIfNull(1);
        }
        return scoreResults;
    }

    public ListWrap<EvalScoreResult> listWaitDispatchResult(TenantId companyId, Collection<String> taskUserIds, String scene) {
        ComQB scoreComQb = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "r")
                .whereEqReq("company_id", companyId.getId())
                .whereInReq("task_user_id", taskUserIds)
                .appendWhere("is_deleted='false'")
                .appendWhere("audit_status='waitDispatch'")
                .whereEqReq("scorerType", scene);
        List<EvalScoreResult> scoreResults = autoBaseDao.listAllDomain(scoreComQb, EvalScoreResult.class);
        if (CollUtil.isEmpty(scoreResults)) {
            return new ListWrap<>(scoreResults).groupBy(r -> r.getTaskUserId());
        }
        for (EvalScoreResult scoreResult : scoreResults) {
            scoreResult.approvalOrderIfNull(1);
        }
        return new ListWrap<>(scoreResults).groupBy(r -> r.getTaskUserId());
    }


    public PagedList<MyTaskUserPo> queryMyHandleTask(MyHandleTaskQuery qry) {
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(MyTaskUserPo.class)
                .setSql("SELECT s.task_id,s.task_user_id,s.emp_id,e.`name` as  empName,s.company_id,b.task_name,c.cycle_start cycle_start_date,c.cycle_end cycle_end_date,b.task_desc,c.type cycle_type,b.created_time,c.id cycle_id,GROUP_CONCAT(DISTINCT s.scorer_type) scorer_type " +
                        " FROM perf_evaluate_task_score_result s  " +
                        "LEFT JOIN perf_evaluate_task_base b ON s.task_id = b.id  and s.company_id = b.company_id " +
                        "LEFT JOIN perf_evaluate_task_user u ON s.task_user_id = u.id AND s.company_id = u.company_id " +
                        "LEFT JOIN employee_base_info e ON s.emp_id = e.employee_id  and  s.company_id = e.company_id " +
                        "LEFT JOIN perf_evaluate_cycle c ON b.cycle_id = c.id and b.company_id = c.company_id " +
                        "WHERE s.company_id = #{companyId} AND s.scorer_id = #{createdUser} AND s.audit_status = 'pass' and u.is_deleted = 'false' and s.is_deleted = 'false' and b.is_deleted = 'false' and s.`scorer_type` NOT IN ('total_self_score','total_sub_score','total_peer_score','total_superior_score') ");
        sqlBuilder.setValue("companyId", qry.getCompanyId());
        sqlBuilder.setValue("createdUser", qry.getCreatedUser());

        sqlBuilder.appendIfOpt("AND s.scorer_type = #{scorerType}", "scorerType", qry.getScorerType());

        sqlBuilder.appendIfOpt("AND b.task_name LIKE CONCAT('%',#{keyword},'%')", "keyword", qry.getKeyword());
        sqlBuilder.appendIfOpt("AND b.cycle_type = #{cycleType}", "cycleType", qry.getCycleType());
        sqlBuilder.append("GROUP BY s.task_id,s.emp_id,s.scorer_id ORDER BY b.created_time DESC");
        sqlBuilder.setPage(qry.getPageNo(), qry.getPageSize());
        return autoBaseDao.listPage(sqlBuilder);
    }

    public PagedList<EvalOfEmpPo> pagedEvalOfEmp(EvalOfEmpQuery qry) {
        ComQB qb = buildEmpOfTask(qry);
        qb.whereLike("b.task_name", qry.getTaskName());
//        qb.appendWhere("((u.task_status = 'finished' and u.temp_task = 1) or (u.temp_task = 0))");
        qb.appendWhere(" u.temp_task = 0 "); //和思威确认这里应该把1.0的所有任务都排除
        qb.setPage(qry.getPageNo(), qry.getPageSize());
        return autoBaseDao.listPage(qb);
    }


    public PagedList<EvalOfEmpPo> pagedTaskOfEmpReport(EvalOfEmpQuery qry) {
        ComQB qb = buildEmpOfTask(qry);
        if (qry.filterCycleDate()) {
            qb.appendWhere("((c.cycle_start BETWEEN '" + qry.getCycleStartDate() + "' and '" + qry.getCycleEndDate() + "') " +
                    "or (c.cycle_end BETWEEN '" + qry.getCycleStartDate() + "' and '" + qry.getCycleEndDate() + "'))");
        }
        qb.setPage(qry.getPageNo(), qry.getPageSize());
        return autoBaseDao.listPage(qb);
    }


    public static ComQB buildEmpOfTask(EvalOfEmpQuery qry) {
        ComQB qb = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(PerfEvaluateTaskBaseDo.class, "b").appendOn("b.company_id = u.company_id AND u.task_id = b.id ")
                .join(CycleDo.class, "c").appendOn("u.company_id = c.company_id and u.cycle_id = c.id ")
                .leftJoin(EmpEvalRuleDo.class, "r").appendOn("u.company_id = r.company_id and u.id = r.emp_eval_id  and r.is_deleted='false'")
                .clearSelect().select("u.id task_user_id,b.task_name, b.archive_status,u.task_status ,ifnull(u.score_of_ref,u.final_score) final_score,u.evaluation_level")
                .select("c.type cycle_type,c.cycle_start cycle_start_date ,c.cycle_end cycle_end_date,r.show_result_type,u.perf_coefficient")
                .setRsType(EvalOfEmpPo.class)
                .whereEq("u.company_id", qry.getCompanyId())
                .whereEq("u.emp_id", qry.getEmpId())
                .appendWhere("u.is_new_emp = 0")
                .whereIn("b.id", qry.getTaskIds())
                .whereEq("b.is_deleted", Boolean.FALSE.toString())
                .whereEq("u.is_deleted", Boolean.FALSE.toString());
        if (StrUtil.isNotBlank(qry.getEmpId())) {
            qb.appendWhere("u.eval_org_id is null");
        }
        qb.appendWhere("u.task_status not in ('terminated', '')")
                .orderByDesc("u.created_time");
        if (CollUtil.isNotEmpty(qry.getTaskStatuss())) {
            qb.whereIn("u.task_status", qry.getTaskStatuss());
        }
        return qb;
    }

    public Boolean onlyAppointScore(TenantId tenantId, String taskId, String empId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .clearSelect().select("count(1)").setRsType(Integer.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_id", taskId)
                .whereEqReq("emp_id", empId)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereIn("scorer_type", EvaluateAuditSceneEnum.noAppointScore());
        Integer one = autoBaseDao.findOne(comQB);
        if (one > 0) {
            return false;
        }
        return true;
    }

    public EvalTaskUserSimplePo getEvalTaskSimpleInfo(String companyId, String taskUserId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskBaseDo.class, "tb")
                .join(PerfEvaluateTaskUserDo.class, "tu")
                .clearSelect().select("tb.id as taskId, tb.task_name as taskName, tu.emp_id as empId")
                .setRsType(EvalTaskUserSimplePo.class)
                .appendOn("tb.company_id=tu.company_id and tb.id=tu.task_id")
                .whereEqReq("tb.company_id", companyId)
                .whereEqReq("tu.id", taskUserId)
                .limit(0, 1);
        return this.autoBaseDao.findOne(comQB);
    }

//    public List<String> listTaskUserIds(String companyId, List<String> taskUserIds) {
//        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
//                .clearSelect().select("id").setRsType(String.class)
//                .whereEqReq("company_id", companyId)
//                .whereInReq("id", taskUserIds)
//                .whereEq("task_status", "scoring")
//                .whereEq("is_deleted", "false");
//        return this.autoBaseDao.listAllDomain(comQB, EvalUser.class);
//    }

    public List<String> listTaskUserIdByTask(String companyId, String taskId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .clearSelect().select("id").setRsType(String.class)
                .whereEqReq("company_id", companyId)
                .whereEq("task_id", taskId)
                .whereEq("is_deleted", "false");
        return this.autoBaseDao.listAll(comQB);
    }

    public PagedList<EvalUserOfDeptPo> pageEvalUserOfDept(ReportSeriesQuery query) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
//                .join(EmpOrganizationDo.class, "o")
//                .appendOn("u.company_id=o.company_id and u.org_id=o.org_id")
                .clearSelect().select("ifnull( u.eval_org_name ,u.emp_org_name) as  name ,count(u.id) as value")
                .setRsType(EvalUserOfDeptPo.class)
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
//                .whereEq("o.status", "valid")
                .whereIn("u.task_status", TalentStatus.drawUpdStatus())
                .whereEq("u.task_id", query.getTaskId())
                .whereEq("u.company_id", query.getTenantId().getId())
                .groupBy("ifnull( u.org_id ,u.eval_org_id) ")
                .setPage(query.getPageNo(), query.getPageSize());
        adminScopeOrgReq(query.getAdminOrgIds(), query.getPerformanceType(), comQB);
        return autoBaseDao.listPage(comQB);
    }

    public PagedList<EvalUserOfDeptPo> pagedEmpOfDept(ReportSeriesQuery query) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(EmpRefOrgDo.class, "ero")
                .appendOn("u.company_id=ero.company_id and u.emp_id =ero.emp_id")
                .join(EmpOrganizationDo.class, "o")
                .appendOn("ero.company_id=o.company_id and ero.org_id=o.org_id")
                .clearSelect().select("o.org_name as  name ,count(u.id) as value")
                .setRsType(EvalUserOfDeptPo.class)
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereEq("ero.ref_type", "org")
                .whereEq("o.status", "valid")
                .whereEq("u.company_id", query.getTenantId().getId())
                .whereIn("u.task_status", TalentStatus.drawUpdStatus())
                .whereEq("u.task_id", query.getTaskId())
                .groupBy("o.org_id")
                .setPage(query.getPageNo(), query.getPageSize());
        adminScopeOrgReq(query.getAdminOrgIds(), query.getPerformanceType(), comQB);
        return autoBaseDao.listPage(comQB);
    }

    public String hasSubmitedScore(TenantId tenantId, String taskUserId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .clearSelect().select("count(1)").setRsType(int.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEq("audit_status", "pass")
                .appendWhere("scorer_type like '%score%'");
        int one = autoBaseDao.findOne(comQB);
        return String.valueOf(one > 0);
    }

    public PagedList<EvalOfOrgPo> pagedOrgForTable(EmpForTableQuery query) {
        ComQB comQB = ComQB.build(EmpOrganizationDo.class, "eo");
        if (!query.isMainAdmin()) {
            comQB.join(AdminManageOrgDo.class, "amo")
                    .appendOn("eo.org_id = amo.org_id and amo.company_id = eo.company_id");
        }
        comQB.leftJoin(OrgEvalOwnerDo.class, "oeo")
                .appendOn("eo.company_id = oeo.company_id and eo.org_id = oeo.org_id and oeo.is_deleted = 'false'")
                .leftJoin(EmployeeBaseInfoDo.class, "e")
                .appendOn("oeo.org_owner_id = e.employee_id and oeo.company_id = e.company_id and e.is_delete = 'false'")
                //.leftJoin(EmpOrganizationDo.class, "p")
                //.appendOn("eo.company_id = p.company_id and eo.parent_org_id = p.org_id and p.status = 'valid'")
                .clearSelect()
                .select("eo.org_id ,eo.org_name,eo.ding_org_id as ext_dept_id,e.employee_id as org_owner_id,e.name as org_owner_name ")
                .select("e.avatar as org_owner_avatar,oeo.index_tables,oeo.table_cnt")
                //",p.org_id as  parent_org_id,p.org_name parent_org_name,p.ding_org_id parent_ext_dept_id
                .setRsType(EvalOfOrgPo.class)
                .whereEqReq("eo.company_id", query.getCompanyId())
                .whereEq("eo.status", "valid");
        if (query.filterDept()) {
            comQB.whereInReq("eo.org_id", query.getDeptIds());
        }
        if (query.filterOwner()) {
            comQB.whereInReq("e.employee_id", query.getEmpIds());
        }
        if (!query.isMainAdmin()) {
            comQB.whereEqReq("amo.admin_emp_id", query.getAdminId());
        }
        //comQB.groupBy("eo.org_id");
        if (query.needOrderBy()) {
            String orderBySql = query.orderByOrgName() ? "eo.org_name " + query.getOrderType() + "" : "e.name " + query.getOrderType() + "";
            comQB.orderBy(orderBySql);
        }
        comQB.setPage(query.getPageNo(), query.getPageSize());
        PagedList<EvalOfOrgPo> evalOfOrgPos = autoBaseDao.listPage(comQB);
        if (evalOfOrgPos.isEmpty()) {
            return evalOfOrgPos;
        }
        List<String> subOrgIds = CollUtil.map(evalOfOrgPos, evalOfOrgPo -> evalOfOrgPo.getOrgId(), true);
        //2) 使用1结果中的部门来查询上级部门信息
        ComQB parentQ = ComQB.build(EmpOrganizationDo.class, "eo")
                .leftJoin(EmpOrganizationDo.class, "p")
                .appendOn("eo.company_id = p.company_id and eo.parent_org_id = p.org_id and p.status = 'valid'")
                .clearSelect().setRsType(EvalOfOrgPo.class)
                .select("eo.org_id ,p.org_id as  parent_org_id,p.org_name parent_org_name,p.ding_org_id parent_ext_dept_id")
                .whereEqReq("eo.company_id", query.getCompanyId())
                .whereInReq("eo.org_id", subOrgIds);
        List<EvalOfOrgPo> parenOrgs = autoBaseDao.listAll(parentQ);
        ListWrap<EvalOfOrgPo> parentMap = new ListWrap<>(parenOrgs).asMap(evalOfOrgPo -> evalOfOrgPo.getOrgId());

        for (EvalOfOrgPo subOrg : evalOfOrgPos) {
            subOrg.acceptParentOrg(parentMap.mapGet(subOrg.getOrgId()));
        }
        return evalOfOrgPos;
    }

    public List<TransferRater> getTransferReviewers(String companyId, String userId) {
        EvalUser user = getBaseEvalUser(new TenantId(companyId), userId);
        ComQB comQB = ComQB.build(CompanyMsgCenterDo.class, "m")
                .leftJoin(EmployeeBaseInfoDo.class, "e")
                .appendOn("m.company_id=e.company_id and m.emp_id=e.employee_id")
                .clearSelect()
                .select("e.name, m.link_id as taskUserId, e.employee_id as emp_id,\n" +
                        "          case business_scene\n" +
                        "           when 'task_confirm' then 'modify_item_audit'\n" +
                        "           when 'change_item_audit' then 'change_item_audit'\n" +
                        "           when 'batch_submit_progress' then 'submit_progress'\n" +
                        "           when 'task_submit_auto_item_progress' then 'submit_progress'\n" +
                        "           when 'reject_finish_value' then 'submit_progress'\n" +
                        "           when 'task_submit_progress' then 'submit_progress'\n" +
                        "           when 'finish_value_audit' then 'finish_value_audit'\n" +
                        "           when 'task_wait_peer_score' then 'peer_score'\n" +
                        "           when 'task_wait_appoint_score' then 'appoint_score'\n" +
                        "           when 'task_wait_sub_score' then 'sub_score'\n" +
                        "           when 'task_wait_sup_score' then 'superior_score'\n" +
                        "           when 'task_all_score' then 'task_all_score'\n" +
                        "           when 'task_result_audit' then 'final_result_audit'\n" +
                        //    "           when 'task_result_affirm' then 'perf_results_affirm'\n" +
                        "           when 'task_interview_excute' then 'task_interview_excute'\n" +
                        "           when 'task_interview_input' then 'task_interview_input'\n" +
                        "           when 'task_interview_transfer_input' then 'task_interview_input'\n" +
                        "           when 'task_interview_confirm' then 'task_interview_confirm'\n" +
                        "           when 'task_result_appeal' then 'task_result_appeal'\n" +
                        "           else '' end as scene ")
                .setRsType(TransferRater.class)
                .whereEqReq("m.company_id", companyId)
                .whereEqReq("m.link_id", userId)
//                .whereEq("e.is_delete", Boolean.FALSE.toString())  //离职或者删除的人也希望可以在列表转交
                .whereEq("m.handler_status", Boolean.FALSE.toString());
        if (TalentStatus.beforeScoreStage().contains(user.getTaskStatus())) {
            comQB.whereNotIn("m.business_scene", Arrays.asList("task_wait_self_score", "set_mutual_audit", "invite_peer_audit", "invite_sub_audit"));
        } else {
            comQB.whereNotIn("m.business_scene", Arrays.asList("task_wait_self_score", "task_submit_progress", "task_submit_auto_item_progress", "task_result_affirm", "task_wait_public"));
        }
        comQB.groupBy("m.emp_id,m.business_scene");
        List<TransferRater> raters = autoBaseDao.listAll(comQB);
        if (CollUtil.isEmpty(raters)) {
            return new ArrayList<>();
        }
        List<String> interviewScenes = Arrays.asList("task_interview_excute", "task_interview_input", "task_interview_confirm");
        boolean isInterview = raters.stream().anyMatch(rater -> interviewScenes.contains(rater.getScene()));
        if (isInterview) {
            return raters;
        }
        List<TransferRater> resRaters = new ArrayList<>();
        Map<String, List<TransferRater>> map = raters.stream().collect(Collectors.groupingBy(rater -> rater.getEmpId()));
        for (String key : map.keySet()) {
            List<TransferRater> transferRaters = map.get(key);
            List<String> scenes = transferRaters.stream().map(r -> r.getScene()).collect(Collectors.toList());
            resRaters.add(new TransferRater(key, transferRaters.get(0).getName(), String.join(",", scenes)));
        }
        return resRaters;
    }

    public void clearCacheIno(TenantId tenantId, String taskUserId) {
        final UpdateBuilder up = UpdateBuilder.build(CompanyCacheInfoDo.class)
                .set("is_deleted", "true")
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("link_id", taskUserId)
                .whereEq("business_scene", "change_item_audit")
                .whereEq("is_deleted", "false");
        autoBaseDao.update(up);
    }

    public List<String> listPerformance(TenantId tenantId, String cycleId) {
        ComQB empEvalQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .clearSelect().select("count(1)").setRsType(int.class)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("cycle_id", cycleId)
                .appendWhere("eval_org_id is null");
        int empEvalCnt = autoBaseDao.findOne(empEvalQB);

        ComQB orgEvalQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .clearSelect().select("count(1)").setRsType(int.class)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("cycle_id", cycleId)
                .appendWhere("eval_org_id is not null");
        int orgEvalCnt = autoBaseDao.findOne(orgEvalQB);

        List<String> performances = new ArrayList<>();
        if (empEvalCnt > 0) {
            performances.add("personEval");
        }
        if (orgEvalCnt > 0) {
            performances.add("orgEval");
        }
        return performances;
    }

    public Set<EmpResetPo> listResetEmp(String companyId, String taskUserId) {
        List<EmpResetPo> all = new ArrayList<>();
        ComQB comQB = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "r")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn(" r.company_id = e.company_id and r.scorer_id = e.employee_id and e.is_delete = 'false' ")
                .clearSelect().select(" e.avatar,e.name,r.scorer_id as empId,r.scorer_type,r.scorer_id,r.approval_order,concat(r.scorer_id,'@',r.scorer_type,'@',r.approval_order) as jointSet ")
                .setRsType(EmpResetPo.class)
                .whereEqReq("r.company_id", companyId)
                .whereEqReq("r.task_user_id", taskUserId)
                .whereEqReq("r.audit_status", "pass")
                .whereEq("r.is_deleted", Boolean.FALSE.toString())
                //.appendWhere(" r.score is not null")
                .whereIn("r.scorer_type", SubScoreNodeEnum.listScoreScene())
                .groupBy(" r.scorer_type,r.scorer_id,r.approval_order ")
                .orderBy(" r.scorer_id,r.approval_order ");
        List<EmpResetPo> objects = autoBaseDao.listAll(comQB);
        all.addAll(objects);

        ComQB typeQ = ComQB.build(PerfEvalTypeResultDo.class, "r")
                .join(EmployeeBaseInfoDo.class, "e")
                .appendOn(" r.company_id = e.company_id and r.scorer_id = e.employee_id and e.is_delete = 'false' ")
                .clearSelect().select(" e.avatar,e.name,r.scorer_id as empId,r.scorer_type,r.scorer_id,r.approval_order ")
                .setRsType(EmpResetPo.class)
                .whereEqReq("r.company_id", companyId)
                .whereEqReq("r.task_user_id", taskUserId)
                .whereEqReq("r.audit_status", "pass")
                .whereEq("r.is_deleted", Boolean.FALSE.toString())
                .whereIn("r.scorer_type", SubScoreNodeEnum.listScoreScene())
                .groupBy(" r.scorer_type,r.scorer_id,r.approval_order ")
                .orderBy(" r.approval_order ");
        List<EmpResetPo> types = autoBaseDao.listAll(typeQ);
        all.addAll(types);

        // 按 scorerId, scorerType, approvalOrder 去重
        Map<String, EmpResetPo> uniqueMap = new LinkedHashMap<>();
        for (EmpResetPo po : all) {
            String key = po.getEmpId() + "@" + po.getScorerType() + "@" + po.getApprovalOrder();
            if (!uniqueMap.containsKey(key)) {
                uniqueMap.put(key, po);
            }
        }
        return new LinkedHashSet<>(uniqueMap.values());
    }

    public Set<EmpResetPo> listResetEmpV3(String companyId, String taskUserId) {
        Set set = new HashSet();
        ComQB comQB = ComQB.build(EmpEvalScorerNodeDo.class, "sr")
                .clearSelect()
                .select(" sr.scorer_avatar as avatar,sr.scorer_name as name")
                .select(" sr.scorer_id as empId,sr.scorer_type,sr.scorer_id,sr.approval_order,concat(sr.scorer_id,'@',sr.scorer_type,'@',sr.approval_order) as jointSet ")
                .setRsType(EmpResetPo.class)
                .whereEqReq("sr.company_id", companyId)
                .whereEqReq("sr.task_user_id", taskUserId)
                .whereEqReq("sr.status", "2")
                .whereInReq("sr.scorer_type", SubScoreNodeEnum.listScoreScene())
                .appendWhere(" (CASE sr.transfer_type  WHEN 'transfer' THEN  (transfer_from is not null and transfer_to is null)  WHEN 'skip' or 'orSkip' THEN  (1=0)  ELSE 1=1 END)")
                .whereEq("sr.is_deleted", Boolean.FALSE.toString())
                .groupBy(" sr.scorer_type,sr.scorer_id,sr.approval_order ")
                .orderBy(" sr.scorer_id,sr.approval_order ");
        List<EmpResetPo> objects = autoBaseDao.listAll(comQB);
        set.addAll(objects);
        return set;
    }

    public List<PerfEvaluateTaskKpiPo> listNoFinishValueEval(String companyId, List<String> taskUserIds) {
        // 第一个查询
        ComQB<PerfEvaluateTaskKpiPo> comQB1 = ComQB.build(PerfEvaluateTaskKpiDo.class, "k")
                .leftJoin(EmployeeBaseInfoDo.class, "e1").appendOn("k.company_id = e1.company_id and k.emp_id = e1.employee_id and e1.is_delete = 'false' ")
                .leftJoin(PerfEvaluateTaskItemScoreRuleDo.class, "s").appendOn("s.company_id = k.company_id and s.task_user_id = k.task_user_id and s.kpi_item_id = k.kpi_item_id and s.is_deleted = 'false' ")
                .leftJoin(EmpEvalTypeUsedFieldDo.class, "f").appendOn("f.company_id = k.company_id and f.task_user_id = k.task_user_id and f.kpi_type_id = k.kpi_type_id and f.is_deleted = 'false' and f.field_id = 'finishValue' ")
                .setRsType(PerfEvaluateTaskKpiPo.class)
                .select("k.*")
                .select("e1.name as empName")
                .select("e1.avatar as avatar")
                .select("IF(k.result_input_type = 'exam', k.emp_id, k.result_input_emp_id) as result_input_emp_id")
                .select("(SELECT GROUP_CONCAT(name) FROM employee_base_info WHERE company_id = '" + companyId + "' AND FIND_IN_SET(employee_id,  IF(k.result_input_type = 'exam', k.emp_id, k.result_input_emp_id))) as resultInputEmpName")
                .select("(SELECT GROUP_CONCAT(ding_user_id) FROM employee_base_info WHERE company_id = '" + companyId + "' AND FIND_IN_SET(employee_id,  IF(k.result_input_type = 'exam', k.emp_id, k.result_input_emp_id))) as resultInputDingId")
                .whereEq("k.company_id", companyId)
                .whereIn("k.task_user_id", taskUserIds)
                .appendWhere(" k.result_input_type != 'no'")
                .whereEq("k.is_deleted", "false")
                .orderByAsc("k.type_order,k.`order`");
        List<PerfEvaluateTaskKpiPo> kpis = autoBaseDao.listAll(comQB1);
        if (CollUtil.isEmpty(kpis)) {
            return new ArrayList<>();
        }
        CompanyConf conf = companyDao.findCompanyConf(new TenantId(companyId));
        List<PerfEvaluateTaskKpiPo> results = new ArrayList<>();
        ListWrap<PerfEvaluateTaskKpiPo> groups = new ListWrap<>(kpis).groupBy(PerfEvaluateTaskKpiPo::getTaskUserId);
        for (String empEvalId : taskUserIds) {
            List<PerfEvaluateTaskKpiPo> tempKpis = groups.groupGet(empEvalId);
            listItemFile(empEvalId, tempKpis);//附件
            listItemFinishComment(new TenantId(companyId), empEvalId, tempKpis);//完成值备注
            //校验必填
            for (PerfEvaluateTaskKpiPo kpi : tempKpis) {
                if (kpi.hasNotInputedOnReqAll(conf)) {
                    results.add(kpi);
                }
            }
        }
        return results;
    }

    public void listItemFinishComment(TenantId tenantId, String taskUserId, List<PerfEvaluateTaskKpiPo> kpis) {
        List<ItemDynamicLog> logPos = evalKpiDao.listItemDynamic(tenantId.getId(), taskUserId, null);
        if (CollUtil.isEmpty(logPos)) {
            return;
        }
        ListWrap<ItemDynamicLog> groups = new ListWrap<>(logPos).groupBy(ItemDynamicLog::getKpiItemId);
        for (PerfEvaluateTaskKpiPo kpi : kpis) {
            List<ItemDynamicLog> logs = groups.groupGet(kpi.getKpiItemId());
            if (CollUtil.isNotEmpty(logs)) {
                kpi.setFinishValueComment(logs.get(0).getComment());//完成值评论 最新提交的
            }
        }
    }

    public void listItemFile(String taskUserId, List<PerfEvaluateTaskKpiPo> kpis) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskFileDo.class)
                .whereEq("task_user_id", taskUserId)
                .whereIn("kpi_item_id", CollUtil.map(kpis, PerfEvaluateTaskKpiPo::getKpiItemId, true))
                .whereEq("is_deleted", "false")
                .orderByDesc("created_time");
        List<PerfEvaluateTaskFile> taskFiles = autoBaseDao.listAllDomain(comQB, PerfEvaluateTaskFile.class);
        if (CollUtil.isEmpty(taskFiles)) {
            return;
        }
        ListWrap<PerfEvaluateTaskFile> fileListWrap = new ListWrap<>(taskFiles).groupBy(PerfEvaluateTaskFile::getKpiItemId);
        for (PerfEvaluateTaskKpiPo kpi : kpis) {
            kpi.setFiles(JSONUtil.toJsonStr(fileListWrap.groupGet(kpi.getKpiItemId())));
        }
    }

//    public List<PerfEvaluateTaskKpiPo> listNoFinishValueEval(String companyId, List<String> taskUserIds) {
//        // 第一个查询
//        ComQB<PerfEvaluateTaskKpiPo> comQB1 = ComQB.build(PerfEvaluateTaskKpiDo.class, "k")
//                .leftJoin(EmployeeBaseInfoDo.class, "e1").appendOn("k.company_id = e1.company_id and k.emp_id = e1.employee_id and e1.is_delete = 'false' ")
//                .leftJoin(PerfEvaluateTaskItemScoreRuleDo.class, "s").appendOn("s.company_id = k.company_id and s.task_user_id = k.task_user_id and s.kpi_item_id = k.kpi_item_id and s.is_deleted = 'false' ")
//                .setRsType(PerfEvaluateTaskKpiPo.class)
//                .select("k.*")
//                .select("e1.name as empName")
//                .select("e1.avatar as avatar")
//                .select("IF(k.result_input_type = 'exam', k.emp_id, k.result_input_emp_id) as result_input_emp_id")
//                .select("(SELECT GROUP_CONCAT(name) FROM employee_base_info WHERE company_id = #{companyId} AND FIND_IN_SET(employee_id, k.result_input_emp_id)) as resultInputEmpName")
//                .whereEq("k.company_id", companyId)
//                .whereIn("k.task_user_id", taskUserIds)
//                .whereEq("k.scorer_type", "auto")
//                .whereEq("k.is_deleted", "false")
//                .appendWhere(" k.item_finish_value is null and s.id is null");
//        List<PerfEvaluateTaskKpiPo> list1 = autoBaseDao.listAll(comQB1);
//
//        // 第二个查询
//        ComQB<PerfEvaluateTaskKpiPo> comQB2 = ComQB.build(PerfEvaluateTaskKpiDo.class, "k")
//                .leftJoin(EmployeeBaseInfoDo.class, "e1").appendOn("k.company_id = e1.company_id and k.emp_id = e1.employee_id and e1.is_delete = 'false' ")
//                .setRsType(PerfEvaluateTaskKpiPo.class)
//                .select("k.*")
//                .select("e1.name as empName")
//                .select("e1.avatar as avatar")
//                .select("IF(k.result_input_type = 'exam', k.emp_id, k.result_input_emp_id) as result_input_emp_id")
//                .select("(SELECT GROUP_CONCAT(name) FROM employee_base_info WHERE company_id = #{companyId} AND FIND_IN_SET(employee_id, k.result_input_emp_id)) as resultInputEmpName")
//                .select("(SELECT GROUP_CONCAT(ding_user_id) FROM employee_base_info WHERE company_id = #{companyId} AND FIND_IN_SET(employee_id, k.result_input_emp_id)) as resultInputDingId")
//                .whereEq("k.company_id", companyId)
//                .whereIn("k.task_user_id", taskUserIds)
//                .whereEq("k.must_result_input", true)
//                .whereEq("k.is_deleted", "false")
//                .whereNotEq("k.scorer_type", "auto")
//                .appendWhere(" if(k.kpi_type_classify='workItem', (k.work_item_finish_value='' or k.work_item_finish_value is null), k.item_finish_value IS NULL)")
//                .appendWhere(" if(k.item_type = 'non-measurable' and k.input_format = 'text', (k.item_finish_value_text = '' OR k.item_finish_value_text IS NULL), k.item_finish_value IS NULL)");
//        List<PerfEvaluateTaskKpiPo> list2 = autoBaseDao.listAll(comQB2);
//
//        // 合并两个查询结果
//        list1.addAll(list2);
//        return list1;
//    }

    public PagedList<PerfEvaluateTaskUserDTO> pagedEvalError(EmpEvalErrorQuery query) {
        ComQB comQB = ComQB.build(PerfTaskExecuteBatchDetailDo.class, "d")
                .leftJoin(PerfEvaluateTaskBaseDo.class, "b").appendOn("d.company_id = b.company_id and d.task_id = b.id")
                .leftJoin(PerfEvaluateTaskUserDo.class, "u").appendOn("b.company_id = u.company_id and b.id = u.task_id")
                .leftJoin(EmployeeBaseInfoDo.class, "e").appendOn(" u.company_id = e.company_id and u.emp_id = e.employee_id and e.is_delete = 'false' ")
                .leftJoin(EmpEvalRule.class, "r").appendOn("u.company_id = r.company_id and u.id = r.emp_eval_id and r.is_deleted = 'false'")
                .leftJoin(CycleDo.class, "c").appendOn("u.company_id = c.company_id and u.cycle_id = c.id ")
                .clearSelect().select("e.avatar,e.name as empName,u.emp_id as empId,u.org_id, u.emp_org_name as empOrgName,b.task_name,b.performance_type,u.task_status,u.rule_conf_status,u.rule_conf_error,u.id, u.eval_org_id,u.eval_org_name,u.task_id,u.temp_task,r.rule_name,u.cycle_id,c.type as cycleType")
                .setRsType(PerfEvaluateTaskUserDTO.class)
                .whereEqReq("d.company_id", query.getCompanyId())
                .whereEqReq("d.execute_batch_id", query.getBatchId())
                .whereEqReq("d.is_deleted", Boolean.FALSE.toString())
                .whereEqReq("b.is_deleted", Boolean.FALSE.toString())
                .appendWhere("(u.rule_conf_status != 200 or u.rule_conf_error is not null)")
                .whereEqReq("u.is_deleted", Boolean.FALSE.toString())
                .groupBy(" u.id");
        if (CollUtil.isNotEmpty(query.getAdminOrgIds())) {
            if (Objects.nonNull(query.getPerformanceType())) {
                if (query.getPerformanceType() == 1) {
                    comQB.whereIn("u.org_id", query.getAdminOrgIds());
                } else {
                    comQB.whereIn("u.eval_org_id", query.getAdminOrgIds());
                }
            }
        }
        comQB.setPage(query.getPageNo(), query.getPageSize());
        PagedList<PerfEvaluateTaskUserDTO> errors = autoBaseDao.listPage(comQB);
        if (errors.isEmpty()) {
            return errors;
        }
        return errors;
    }

    public void realTimeSynEvalRule(TenantId companyId, List<String> taskUserIds, AdminTask adminTask, AdminTaskDiff diff) {
        final UpdateBuilder up = UpdateBuilder.build(EmpEvalRule.class)
                .whereEqReq("company_id", companyId.getId())
                .whereIn("emp_eval_id", taskUserIds)
                .whereEq("is_deleted", "false");
        if (CollUtil.isNotEmpty(diff.getScoreView())) {
            up.set("score_view", JSON.toJSONString(adminTask.getScoreView()));
        }
//        if (CollUtil.isNotEmpty(diff.getConfirmResult())) {
//            up.set("confirm_result", JSON.toJSONString(adminTask.getConfirmResult()));
//        }
        if (CollUtil.isNotEmpty(diff.getPublishResult())) {
            up.set("publish_result", JSON.toJSONString(adminTask.getPublishResult()));
        }
//        if (CollUtil.isNotEmpty(diff.getAppealConf())) {
//            up.set("appeal_conf", JSON.toJSONString(adminTask.getAppealConf()));
//        }
        autoBaseDao.update(up);
    }

    public void deleteScoreResult(String companyId, String taskUserId) {
        final UpdateBuilder up = UpdateBuilder.build(PerfEvaluateTaskScoreResultDo.class)
                .set("is_deleted", "true")
                .whereEqReq("company_id", companyId)
                .whereEq("task_user_id", taskUserId)
                .whereLike("scorer_type", "score")
                .whereEq("is_deleted", "false");
        autoBaseDao.update(up);
    }

    public List<DeadLineJobPo> listDeadLineUrging(String companyId, String urgingTime) {
        ComQB comQB = ComQB.build(DeadLineJobDo.class, "j")
                .join(PerfEvaluateTaskUserDo.class, "u")
                .appendOn(" j.company_id = u.company_id and j.task_user_id = u.id and u.is_deleted ='false' and u.task_status = j.business_type ")
                .join(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn(" j.company_id = b.company_id and j.task_id = b.id and b.is_deleted = 'false' ")
                .clearSelect().select(" j.company_id,b.id as taskId,b.task_name,u.id as taskUserId,u.task_status,u.emp_id ")
                .setRsType(DeadLineJobPo.class)
                .whereEq("j.is_deleted", Boolean.FALSE.toString())
                .whereEq("j.auto_urging", 1)
                .whereEq("j.company_id", companyId)
                .whereNotIn("u.task_status", Arrays.asList("drawUpIng", "terminated", "finished"))
                .whereEq("j.execute_status", 0)
                .appendWhere(" (j.first_urging_time = '" + urgingTime + "' or j.second_urging_time = '" + urgingTime + "') ")
                .groupBy("task_user_id");
        List<DeadLineJobPo> pos = autoBaseDao.listAll(comQB);
        return pos;
    }

    public List<String> listDeadLineCompanyIds(String endDate, List<Integer> autoSkips,
                                               Integer autoUrging, String urgingTime) {
        ComQB comQB = ComQB.build(DeadLineJobDo.class, "j").clearSelect().select("distinct j.company_id").setRsType(String.class)
                .whereEq("j.is_deleted", Boolean.FALSE.toString())
                .whereEq("j.execute_status", 0)
                .whereIn("j.auto_skip", autoSkips)
                .whereEq("j.auto_urging", autoUrging);
        if (StringUtils.isNotBlank(endDate)) {
            comQB.appendWhere(" j.end_date < '" + endDate + "'");
        }
        if (StringUtils.isNotBlank(urgingTime)) {
            comQB.appendWhere(" (j.first_urging_time = '" + urgingTime + "' or j.second_urging_time = '" + urgingTime + "') ");
        }
        return autoBaseDao.listAll(comQB);
    }

    public List<DeadLineJobPo> listDeadLineSkip(String companyId, String endDate) {
        ComQB comQB = ComQB.build(DeadLineJobDo.class, "j")
                .join(PerfEvaluateTaskUserDo.class, "u")
                .appendOn(" j.company_id = u.company_id and j.task_user_id = u.id and u.is_deleted ='false' and u.task_status = j.business_type ")
                .join(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn(" j.company_id = b.company_id and j.task_id = b.id and b.is_deleted = 'false' ")
                .clearSelect().select(" j.company_id,j.auto_skip,j.auto_skip_handler_type,b.id as taskId,b.task_name,u.id as taskUserId,u.task_status,u.emp_id ")
                .setRsType(DeadLineJobPo.class)
                .whereEq("j.is_deleted", Boolean.FALSE.toString())
                .whereEq("j.company_id", companyId)
                .whereEq("j.execute_status", 0)
                .whereIn("j.auto_skip", Arrays.asList(0, 1, 2))
                .whereNotIn("u.task_status", Arrays.asList("drawUpIng", "terminated", "finished"))
                .appendWhere(" j.end_date < '" + endDate + "'")
                .groupBy("task_user_id");
        List<DeadLineJobPo> pos = autoBaseDao.listAll(comQB);
        if (CollUtil.isEmpty(pos)) {
            return pos;
        }
        //过滤出跳过与终止的数据
        List<DeadLineJobPo> jobPos = CollUtil.filterNew(pos, p -> Arrays.asList(1, 2).contains(p.getAutoSkip()));
        //过滤出停留当前阶段且执行环节的数据
        List<DeadLineJobPo> stayList = CollUtil.filterNew(pos, p -> p.getAutoSkip() == 0 && TalentStatus.CONFIRMED.getStatus().equals(p.getTaskStatus()));
        //查询停留阶段完成值已录入完成的数据
        ComQB qb = ComQB.build(PerfEvaluateTaskKpiDo.class)
                .clearSelect().select("task_user_id").setRsType(String.class)
                .whereEq("company_id", companyId)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereIn("task_user_id", CollUtil.map(stayList, DeadLineJobPo::getTaskUserId, true))
                .groupBy("task_user_id").having("count(id)-count(result_input_type = 'no' or null) = count(final_submit_finish_value = 1 or null)");
        List<String> skipIds = autoBaseDao.listAll(qb);
        if (CollUtil.isEmpty(skipIds)) {
            return jobPos;
        }
        jobPos.addAll(CollUtil.filterNew(stayList, s -> skipIds.contains(s.getTaskUserId())));
        return jobPos;
    }

    public boolean hasAffirmingTimeout(String companyId, String taskUserId) {
        String endDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        ComQB comQB = ComQB.build(DeadLineJobDo.class)
                .clearSelect().select("count(task_user_id) cnt").setRsType(Integer.class)
                .whereEq("is_deleted", Boolean.FALSE.toString())
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("execute_status", 0)
                .whereEqReq("auto_skip", 1)
                .whereEq("business_type", TalentStatus.RESULTS_AFFIRMING.getStatus())
                .appendWhere(" end_date < '" + endDate + "'")
                .groupBy("task_user_id");
        Integer cnt = autoBaseDao.findOne(comQB);
        //开启了截止时间，按截止时间来
        if (Objects.nonNull(cnt)) {
            return true;
        }
        ComQB v2Task = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(EmpEvalRuleDo.class, "r")
                .appendOn(" u.company_id = r.company_id and u.id = r.emp_eval_id and r.is_deleted='false'")
                .setRsType(AutoAffirmPo.class)
                .clearSelect().select(" id as taskUserId,in_result_affirm_time,r.confirm_result,r.dead_line_conf ")
                .whereEqReq("u.company_id", companyId)
                .whereEqReq("id", taskUserId)
                .whereEq("u.task_status", TalentStatus.RESULTS_AFFIRMING.getStatus())
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereBigEq("u.created_time", "'2022-01-21 00:00:00'")
                .appendWhere(" JSON_EXTRACT (r.confirm_result, '$.open') = 1 and JSON_EXTRACT (r.confirm_result, '$.auto') = 1 ");
        AutoAffirmPo po = autoBaseDao.findOne(v2Task);
        if (Objects.isNull(po)) {
            return false;
        }
        if (po.outAffirmDay()) {
            return true;
        }
        return false;
    }

    public void updateDeadLineExecuteStatus(String taskUserId, String taskStatus) {
        final UpdateBuilder up = UpdateBuilder.build(DeadLineJobDo.class)
                .set("execute_status", 1)
                .whereEqReq("business_type", taskStatus)
                .whereEqReq("task_user_id", taskUserId);
        autoBaseDao.update(up);
    }

    public Boolean checkHasScoreAudit(String taskUserId) {
        QueryBuilder queryBuilder = QueryBuilder.build(PerfEvaluateTaskScoreResultDo.class)
                .whereEq("task_user_id", taskUserId)
                .whereEq("is_deleted", "false")
                .whereEq("audit_status", "pass")
                .whereNotIn("scorer_type", Arrays.asList(EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT.getScene(), EvaluateAuditSceneEnum.CHANGE_ITEM_AUDIT.getScene()));
        return CollectionUtils.isNotEmpty(autoBaseDao.listAll(queryBuilder));
    }

    public List<FixPerfEvaluateTaskUserDo> listV1NoneOrgTaskUser(String companyId, int pageNo) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .setRsType(FixPerfEvaluateTaskUserDo.class)
                .whereEq("company_id", companyId)
                //.whereIsNull("at_org_code_path")
                .whereEq("is_deleted", "false")
                //.whereEq("tempTask", 1)
                .setPage(pageNo, 1000);
        List<FixPerfEvaluateTaskUserDo> objects = autoBaseDao.listPage(comQB);
        return objects.stream().filter(userDo -> StrUtil.isBlank(userDo.getAtOrgCodePath())).collect(Collectors.toList());
    }

    public void updateOrgPath(FixPerfEvaluateTaskUserDo evalUser) {
        UpdateBuilder comQB = UpdateBuilder.build(PerfEvaluateTaskUserDo.class)
                .set("org_id", evalUser.getOrgId())
                .set("at_org_code_path", evalUser.getAtOrgCodePath())
                .set("at_org_name_path", evalUser.getAtOrgNamePath())
                .set("at_org_path_hight", evalUser.getAtOrgPathHight())
                .whereEq("company_id", evalUser.getCompanyId())
                .whereEqReq("id", evalUser.getId());
        autoBaseDao.update(comQB);
    }

    public List<String> listV1NoneOrgTaskUserCompany(int row) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .clearSelect().select("distinct u.company_id ").setRsType(String.class)
                .whereIsNull("at_org_code_path")
                .whereEq("tempTask", 1)
                .limit(0, row);
        return autoBaseDao.listAll(comQB);
    }

    public List<String> queryGradeTaskUserIds(EvalUserRelGrade grade) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
//                .leftJoin(EmployeeBaseInfoDo.class, "e")
//                .appendOn("e.employee_id = u.emp_id and e.company_id=u.company_id")
                .leftJoin(PerfEvaluateTaskBaseDo.class, "t")
                .appendOn("t.id = u.task_id and t.company_id=u.company_id")
//                .join(EmpRefOrgDo.class, "ero")
//                .appendOn("ero.company_id=u.company_id and ero.emp_id=u.emp_id")
                .clearSelect()
                .select("u.id")
                .setRsType(String.class)
                .whereEqReq("t.cycle_id", grade.getCycleId())
                .whereIn("t.id", grade.getTaskIds())
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereEq("t.is_deleted", Boolean.FALSE.toString())
                .whereIn("u.task_status", TalentStatus.canNormalDistributionStage())
                .whereEq("u.company_id", grade.getCompanyId());
        if (grade.getPerformanceType() != null) {
            comQB.whereEq("t.performance_type", grade.getPerformanceType());
        }
        if (org.apache.commons.lang.StringUtils.isNotBlank(grade.getEmpName())) {
            comQB.whereIn("u.emp_name", Arrays.asList(grade.getEmpName().split(",")));
        }
        if (CollUtil.isNotEmpty(grade.getOrgIds())) {
            ComQB existQ = ComQB.build(EmpRefOrgDo.class, "ref")
                    .join(EmpOrganizationDo.class, "s")
                    .appendOn("ref.company_id=s.company_id and ref.org_id=s.org_id")
                    .join(EmpOrganizationDo.class, "p")
                    .appendOn("s.company_id=p.company_id and instr(s.org_code,p.org_code)")
                    .clearSelect().select("ref.emp_id").setRsType(String.class)
                    .appendWhere("ref.ref_type='org'")
                    .appendWhere("s.status='valid'")
                    .appendWhere("ref.emp_id=u.emp_id")
                    .appendWhere("p.org_id in" + StringTool.getInStrSql(grade.getOrgIds()));
            comQB.appendWhere(String.format("EXISTS(%s)", existQ.getSql()));
        }
        if (CollUtil.isNotEmpty(grade.getRoleIds())) {
            ComQB roleQb = ComQB.build(RoleRefEmpDo.class)
                    .clearSelect().select("emp_id").setRsType(String.class)
                    .appendWhere("company_id='" + grade.getCompanyId() + "'")
                    .appendWhere("role_id in" + StringTool.getInStrSql(grade.getRoleIds()))
                    .groupBy("emp_id");
            List<String> empIds = autoBaseDao.listAll(roleQb);
            comQB.whereIn("u.emp_id", empIds);
        }
//        comQB.groupBy("u.id");
        return autoBaseDao.listAll(comQB);
    }

    public List<EvalUser> getTaskUsersByCycleId(String companyId, String cycleId, Integer performanceType) {

        List<String> alreadyScored = Arrays.asList(TalentStatus.FINISHED.getStatus(), TalentStatus.RESULTS_AUDITING.getStatus(),
                TalentStatus.WAIT_PUBLISHED.getStatus(), TalentStatus.RESULTS_APPEAL.getStatus(), TalentStatus.RESULTS_AFFIRMING.getStatus(),
                TalentStatus.RESULTS_INTERVIEW.getStatus(), TalentStatus.TERMINATED.getStatus());

//        List<String> alreadyScored = Arrays.asList(TalentStatus.FINISHED.getStatus(), TalentStatus.TERMINATED.getStatus());

        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class, "a")
                .join(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("a.task_id = b.id and a.company_id = b.company_id")
                .select("a.*,b.task_name as taskName")
                .setRsType(EvalUser.class)
                .whereEqReq("a.company_id", companyId)
                .whereEqReq("a.cycleId", cycleId)
                .whereIn("a.task_status", alreadyScored)
                .whereEq("a.is_deleted", Boolean.FALSE.toString())
                .whereEq("b.is_deleted", Boolean.FALSE.toString())
                .whereEq("b.performance_type", performanceType)
                .whereNotNull("a.final_score");

        return autoBaseDao.listAll(comQB);

    }

    public List<PerfEvaluateTaskScoreResultPo> findWasEvalScoreResults(String scorerId, String scene, TenantId companyId,
                                                                       List<String> itemIds, String taskId, String taskUserId) {
        ComQB build = ComQB.build(PerfEvaluateTaskScoreResultDo.class)
                .clearSelect()
                .select("kpi_item_id, score, scorer_id, veto_flag, score_level")
                .setRsType(PerfEvaluateTaskScoreResultPo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("scorer_id", scorerId)
                .whereEq("is_deleted", "false")
                .whereEqReq("scorer_type", scene)
                .whereInReq("kpi_item_id", itemIds)
                .whereEqReq("task_id", taskId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("audit_status", "pass");
        return autoBaseDao.listAll(build);
    }

    public List<String> listTaskUserIdByTaskId(String taskId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .clearSelect().select("id").setRsType(String.class)
                .whereEq("task_id", taskId)
                .whereEq("is_deleted", "false");
        return this.autoBaseDao.listAll(comQB);
    }

    public List<String> listTaskUserIdByTaskIdWithTime(String taskId, String beginDate, String endDate) {
        ComQB updateTimeQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .clearSelect().select("id").setRsType(String.class)
                .whereEq("task_id", taskId)
                .whereEq("is_deleted", "false");

        if (StringUtils.isNotBlank(beginDate)) {
            updateTimeQB.appendWhere("updated_time >= #{beginDate}", beginDate);
        }
        if (StringUtils.isNotBlank(endDate)) {
            updateTimeQB.appendWhere("updated_time <= #{endDate}", endDate);
        }
        List<String> taskUserIds = this.autoBaseDao.listAll(updateTimeQB);

        ComQB createTimeQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .clearSelect().select("id").setRsType(String.class)
                .whereEq("task_id", taskId)
                .whereEq("is_deleted", "false");

        if (StringUtils.isNotBlank(beginDate)) {
            createTimeQB.appendWhere("created_time >= #{beginDate}", beginDate);
        }
        if (StringUtils.isNotBlank(endDate)) {
            createTimeQB.appendWhere("created_time <= #{endDate}", endDate);
        }

        taskUserIds.addAll(this.autoBaseDao.listAll(createTimeQB));
        //去重
        taskUserIds = taskUserIds.stream().distinct().collect(Collectors.toList());

        return taskUserIds;
    }


    public void clearEmpEvalTypeUsedField() {
        String sqlStr = " DELETE from emp_eval_type_used_field where is_deleted = 'true' LIMIT 10000 ";
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(Integer.class).setSql(sqlStr);
        int delete = autoBaseDao.nativeExecute(sqlBuilder);
    }

    public List<TransferRater> listCanSkipRaters(List<String> taskUserIds, TenantId tenantId) {
        List<TransferRater> res = new ArrayList<>();
        for (String taskUserId : taskUserIds){
            List<TransferRater> transferReviewers = this.getTransferReviewers(tenantId.getId(), taskUserId);
            res.addAll(transferReviewers);
        }
        return res;
    }

    public List<EvalUser> listNoFinishTaskUser(List<String> companyIdList, List<String> empEvalIds) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .clearSelect()
                .select("id")
                .setRsType(EvalUser.class)
                .whereInReq("company_id", companyIdList)
//                .whereNotInReq("task_status", Arrays.asList("terminated", "finished"))
                .whereEq("is_deleted", Boolean.FALSE.toString());
        if (CollUtil.isNotEmpty(empEvalIds)){
            comQB.whereInReq("id", empEvalIds);
        }
        return autoBaseDao.listAllDomain(comQB, EvalUser.class);
    }


    public PagedList<String> pagedFinishedTaskUserForMigration(String companyId, boolean isFinished, Integer pageNo, Integer pageSize) {
        long t0 = System.currentTimeMillis();
        if (log.isDebugEnabled()) {
            log.debug("pagedFinishedTaskUserForMigration.enter: companyId={}, isFinished={}, pageNo={}, pageSize={}", companyId, isFinished, pageNo, pageSize);
        }
        ComQB qb = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("u.task_id = b.id and u.company_id = b.company_id and b.is_deleted = 'false' ")
                .clearSelect().select("u.id as taskUserId").setRsType(String.class)
                .whereEqReq("u.company_id", companyId)
                .whereEq("u.is_deleted", "false")
                .whereInReq("u.task_status", isFinished ? TalentStatus.endStatus() : TalentStatus.noFinishedStage())
                .appendWhere("NOT EXISTS (SELECT 1 FROM emp_eval_scorer s WHERE s.company_id = u.company_id AND s.task_user_id = u.id AND s.is_deleted = 'false' AND s.version = u.version)")
                .orderBy("u.id ASC")
                .setPage(pageNo, pageSize);
        PagedList<String> rs = autoBaseDao.listPage(qb);
        if (log.isDebugEnabled()) {
            log.debug("pagedFinishedTaskUserForMigration.exit: companyId={}, isFinished={}, pageNo={}, pageSize={}, rows={}, costMs={}",
                    companyId, isFinished, pageNo, pageSize, (rs == null ? 0 : rs.size()), System.currentTimeMillis() - t0);
        }
        return rs;
    }


    public PagedList<String> pagedFinishedTaskUserForMigrationByLastId(String companyId, boolean isFinished, String lastIdExclusive, Integer pageSize) {
        ComQB qb = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .join(PerfEvaluateTaskBaseDo.class, "b")
                .appendOn("u.task_id = b.id and u.company_id = b.company_id and b.is_deleted = 'false' ")
                .clearSelect().select("u.id as taskUserId").setRsType(String.class)
                .whereEqReq("u.company_id", companyId)
                .whereEq("u.is_deleted", "false")
                .whereInReq("u.task_status", isFinished ? TalentStatus.finishedStage() : TalentStatus.noFinishedStage())
                .appendWhere("NOT EXISTS (SELECT 1 FROM emp_eval_scorer s WHERE s.company_id = u.company_id AND s.task_user_id = u.id AND s.is_deleted = 'false' AND s.version = u.version)")
                .orderBy("u.id ASC");
        if (lastIdExclusive != null) {
            qb.appendWhere("u.id > #{lastId}", lastIdExclusive);
        }
        qb.setPage(1, pageSize);
        return autoBaseDao.listPage(qb);
    }


    public List<EvalUser> listTaskUsersByTaskId(TenantId tenantId, String taskId) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_id", taskId)
                .appendWhere("is_deleted ='false'");
        return autoBaseDao.listAllDomain(comQB, EvalUser.class);
    }

    public List<String> listTaskUserIdsByTaskIdAndStatus(String companyId, String taskId, List<String> statusList, int pageNo) {
        int pageSize = 100;
        ArrayList<String> list = new ArrayList<>(statusList);
        list.add("drawUpIng");
        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                .clearSelect().select("id").setRsType(String.class)
                .whereEq("company_id", companyId)
                .whereEqReq("task_id", taskId)
                .whereInReq("task_status", list)
                .appendWhere("is_deleted ='false'")
                .limit((pageNo - 1) * pageSize, pageSize);
        return autoBaseDao.listAll(comQB);
    }
}
