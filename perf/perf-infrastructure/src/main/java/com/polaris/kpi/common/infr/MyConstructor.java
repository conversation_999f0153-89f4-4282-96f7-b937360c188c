package com.polaris.kpi.common.infr;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.CONSTRUCTOR;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * <AUTHOR> luf<PERSON>
 * @date 2022/3/5 10:10 下午
 */
@Documented
@Target (CONSTRUCTOR) @Retention (RUNTIME)
public @interface MyConstructor {
    /**
     <p>The getter names.</p>
     @return the getter names corresponding to the parameters in the
     annotated constructor.
     */
    String[] value();
}