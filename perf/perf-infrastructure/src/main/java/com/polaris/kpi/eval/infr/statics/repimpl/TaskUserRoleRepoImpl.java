//package com.polaris.kpi.eval.infr.statics.repimpl;
//
//import cn.hutool.core.util.ObjectUtil;
//import com.polaris.acl.dept.pojo.role.RoleDo;
//import com.polaris.kpi.eval.domain.statics.entity.TaskUserRole;
//import com.polaris.kpi.eval.domain.statics.repo.TaskUserRoleRepo;
//import com.polaris.kpi.eval.domain.task.entity.EvalUser;
//import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskUserDo;
//import com.polaris.kpi.eval.infr.task.ppojo.TaskUserRoleDo;
//import com.polaris.kpi.org.infr.emp.pojo.RoleRefEmpDo;
//import org.lufei.ibatis.builder.ComQB;
//import org.lufei.ibatis.builder.DeleteBuilder;
//import org.lufei.ibatis.dao.DomainDaoImpl;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.List;
//
///**
// * <AUTHOR>
// * @date 2025/2/10 13:36
// */
//@Component
//public class TaskUserRoleRepoImpl implements TaskUserRoleRepo {
//
//    @Resource
//    private DomainDaoImpl domainDao;
//
//    @Override
//    public List<TaskUserRole> buildTaskUserRoles(EvalUser taskUser) {
//
//        ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class,"a")
//                .join(RoleRefEmpDo.class, "rre")
//                .appendOn(" a.emp_id = rre.emp_id ")
//                .join(RoleDo.class, "r")
//                .appendOn(" rre.role_id = r.id ")
//                .clearSelect()
//                .select("UUID() as id,a.company_id, a.cycle_id, a.id as taskUserId, r.role_name")
//                .setRsType(TaskUserRole.class)
//                .whereEqReq("a.company_id", taskUser.getCompanyId().getId())
//                .whereEqReq("a.cycle_id",taskUser.getCycleId())
//                .whereEqReq("a.id",taskUser.getId())
//                .whereNotNull("r.id");
//
//        return domainDao.listAll(comQB);
//    }
//
//    @Override
//    public void addOrUpdateTaskUserRole(List<TaskUserRole> taskUserRoles) {
//
//        if (ObjectUtil.isNotEmpty(taskUserRoles)){
//            deleteByTaskUserId(taskUserRoles.get(0).getTaskUserId(), taskUserRoles.get(0).getCompanyId());
//            //批量插入
//            domainDao.saveBatch(taskUserRoles,"task_user_role");
//        }
//    }
//
//    @Override
//    public void deleteByTaskUserId(String taskUserId, String companyId) {
//        DeleteBuilder delete = DeleteBuilder.build(TaskUserRoleDo.class)
//                .whereEqReq("task_user_id", taskUserId)
//                .whereEqReq("company_id", companyId);
//        domainDao.delete(delete);
//    }
//}
