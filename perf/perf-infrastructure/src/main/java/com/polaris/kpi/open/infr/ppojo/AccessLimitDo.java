package com.polaris.kpi.open.infr.ppojo;

import com.polaris.kpi.common.infr.DataObj;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.ibatis.annotations.Ckey;
import org.apache.ibatis.annotations.Table;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@Table(value = "open_access_limit")
public class AccessLimitDo extends DataObj {
    @Ckey
    protected String key;
    protected Long maxCount;
    protected Long accessCount;
    private Date createdTime;//
    private Date updatedTime;//
}
