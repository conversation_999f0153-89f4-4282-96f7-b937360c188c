package com.polaris.kpi.setting.ppojo;


import com.polaris.kpi.common.infr.DelData;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.ibatis.annotations.Ckey;

@Setter
@Getter
@NoArgsConstructor
public class ResultAuditFlowUserDo extends DelData {

    @Ckey
    private String id;
    private String flowInstanceId;
    private String nodeRaterId;
    private String taskUserId;
    private String multipleReviewersType;
    private Integer status;
    private Integer curLevel;  //当前层级

    public ResultAuditFlowUserDo(String id,String companyId, String flowInstanceId,String nodeRaterId, String taskUserId,
                                 String multipleReviewersType,Integer status, Integer curLevel,String opEmpId) {
        this.id = id;
        this.companyId = companyId;
        this.flowInstanceId = flowInstanceId;
        this.nodeRaterId = nodeRaterId;
        this.taskUserId = taskUserId;
        this.multipleReviewersType = multipleReviewersType;
        this.status = status;
        this.curLevel = curLevel;
        this.createdUser = opEmpId;
    }
}
