package com.polaris.kpi.setting.query;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.lufei.ibatis.mapper.PagedList;
import org.lufei.ibatis.mapper.PagedQuery;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class ManagerPrivQuery extends PagedQuery {
    private String companyId;
    private String opEmpId;
    private List<String> empIds;
    private List<String> orgIds;
    private Boolean noOrgPerm;

    public void accOp(String companyId,String opEmpId) {
        this.companyId = companyId;
        this.opEmpId = opEmpId;
    }
}
