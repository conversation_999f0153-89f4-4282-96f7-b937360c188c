package com.polaris.kpi.report.infr.pojo;


import cn.hutool.core.collection.CollUtil;
import com.polaris.sdk.type.ListWrap;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/22 13:59
 */
@Getter
@Setter
@NoArgsConstructor
public class ItemEvalUpdateCountPo {

    private String zeroToTwenty;
    private String twentyOneToFifty;
    private String fiftyOneToEighty;
    private String eightyOneToOneHundred;
    private String overOneHundred;

    private Integer zeroToTwentyCnt;
    private Integer twentyOneToFiftyCnt;
    private Integer fiftyOneToEightyCnt;
    private Integer eightyOneToOneHundredCnt;
    private Integer overOneHundredCnt;


    private List<ItemFinishRateTopEmp> top5Emps;
    private Map<String,List<ItemFinishWithTaskUser>> itemFinishWithTaskUserMap;
    private List<ItemAvgFinishRateWithTaskUser> itemAvgFinishRateWithTaskUsers = new ArrayList<>();

    public void calcTaskUserAvgFinishRate(){

        //遍历key
        for (Map.Entry<String, List<ItemFinishWithTaskUser>> entry : itemFinishWithTaskUserMap.entrySet()) {
            ItemAvgFinishRateWithTaskUser avgFinishRateWithTaskUser = new ItemAvgFinishRateWithTaskUser();
            avgFinishRateWithTaskUser.setTaskUserId(entry.getKey());
            List<ItemFinishWithTaskUser> itemFinishWithTaskUsers = entry.getValue();
            if (CollUtil.isEmpty(itemFinishWithTaskUsers)){
                continue;
            }
            for (ItemFinishWithTaskUser itemFinishWithTaskUser : itemFinishWithTaskUsers) {
                itemFinishWithTaskUser.calcFinishRate();
                avgFinishRateWithTaskUser.getFinishRates().add(itemFinishWithTaskUser.getFinishRate());
            }
            avgFinishRateWithTaskUser.calcAvgFinishRate();
            //判断所属区间

        }


    }

}
