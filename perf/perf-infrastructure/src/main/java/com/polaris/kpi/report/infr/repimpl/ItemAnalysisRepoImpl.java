package com.polaris.kpi.report.infr.repimpl;

import com.polaris.kpi.ind.domain.entity.FocusItemCount;
import com.polaris.kpi.report.domain.entity.OrgItemSummary;
import com.polaris.kpi.report.domain.repo.ItemAnalysisRepo;
import com.polaris.kpi.report.infr.pojo.FocusItemCountDo;
import com.polaris.kpi.report.infr.pojo.OrgItemSummaryDo;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.DeleteBuilder;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.common.data.ToDataBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/12 16:07
 */
@Component
public class ItemAnalysisRepoImpl implements ItemAnalysisRepo {

    public static final String orgItemSummarySeq = "org_item_summary_seq";
    public static final String focusItemCountSeq = "focus_item_count_seq";

    @Autowired
    public void setDomainDao(DomainDaoImpl domainDao) {
        this.domainDao = domainDao;
    }

    private DomainDaoImpl domainDao;


    @Override
    public void batchSave(List<OrgItemSummary> orgItemSummaries, String empId) {

        List<OrgItemSummaryDo> orgItemSummaryDos = new ArrayList<>();
        orgItemSummaries.forEach(orgItemSummary -> {
            orgItemSummary.initOnNew(domainDao.nextLongAsStr(orgItemSummarySeq),empId);
            orgItemSummaryDos.add(new ToDataBuilder<>(orgItemSummary, OrgItemSummaryDo.class).data());
        });
        domainDao.saveBatch(orgItemSummaryDos);
    }

    @Override
    public void delOrgItemSummary(String companyId, String cycleId, String empId, Integer performanceType) {

//        UpdateBuilder updateBuilder = UpdateBuilder.build(OrgItemSummaryDo.class)
//                .whereEqReq("company_id", companyId)
//                .whereEqReq("cycle_id", cycleId)
//                .set("is_deleted", Boolean.TRUE.toString())
//                .set("updated_user", empId)
//                .set("updated_time", new Date());
//        domainDao.update(updateBuilder);

        DeleteBuilder deleteBuilder = DeleteBuilder.build(OrgItemSummaryDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("performance_type", performanceType)
                .whereEqReq("cycle_id", cycleId);
        domainDao.delete(deleteBuilder);
    }

    @Override
    public void countStart(String companyId, String cycleId, String empId, Integer performanceType) {

        UpdateBuilder updateBuilder = UpdateBuilder.build(FocusItemCountDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("cycle_id", cycleId)
                .whereEqReq("performance_type", performanceType)
                .whereEqReq("is_deleted", Boolean.FALSE.toString())
                .set("is_deleted", Boolean.TRUE.toString())
                .set("updated_user", empId)
                .set("updated_time", new Date());
        domainDao.update(updateBuilder);

        FocusItemCount focusItemCount = new FocusItemCount(companyId,cycleId,0,performanceType);
        focusItemCount.initOnNew(domainDao.nextLongAsStr(focusItemCountSeq),empId);
        domainDao.save(new ToDataBuilder<>(focusItemCount, FocusItemCountDo.class).data());

    }

    @Override
    public void countEnd(String companyId, String cycleId, String empId, Integer performanceType) {
        UpdateBuilder updateBuilder = UpdateBuilder.build(FocusItemCountDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("cycle_id", cycleId)
                .whereEqReq("performance_type", performanceType)
                .whereEqReq("is_deleted", Boolean.FALSE.toString())
                .set("count_state", 1)
                .set("updated_user", empId)
                .set("updated_time", new Date());
        domainDao.update(updateBuilder);
    }

    @Override
    public boolean existsGoingCount(String companyId, String cycleId, String empId , Integer performanceType) {
        ComQB comQB = ComQB.build(FocusItemCountDo.class)
                .clearSelect().select("count(1)")
                .setRsType(int.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("cycle_id", cycleId)
                .whereEqReq("performance_type", performanceType)
                .whereEqReq("is_deleted", Boolean.FALSE.toString())
                .whereEqReq("count_state", 0);
        Integer one = domainDao.findOne(comQB);
        return one != null && one > 0;
    }
}
