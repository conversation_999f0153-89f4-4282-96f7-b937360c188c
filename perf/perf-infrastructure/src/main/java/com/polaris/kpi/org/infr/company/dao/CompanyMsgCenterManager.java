package com.polaris.kpi.org.infr.company.dao;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.perf.www.common.constant.BusinessConstant;
import com.polaris.kpi.eval.domain.task.entity.CompanyNotice;
import com.perf.www.model.common.CompanyMsgCenterModel;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.QueryBuilder;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.dao.AutoBaseDao;
import org.lufei.ibatis.mapper.PagedList;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;

@Component
public class CompanyMsgCenterManager {
    @Resource
    private AutoBaseDao autoBaseDao;

    public String saveNoticeMsg(CompanyNotice model){
        model.setId(UUID.randomUUID().toString());
        autoBaseDao.save(model);
        return model.getId();
    }

    public String saveMsg(CompanyMsgCenterModel model){
        if(StrUtil.isEmpty(model.getId())){
            model.setId(UUID.randomUUID().toString());
        }
        model.setHandlerStatus("false");
        autoBaseDao.save(model);
        return model.getId();
    }

    public String addMsg(String taskId, String companyId, String msgScene, String linkId,
                         String receiveId, String att, String url, String taskName){
        CompanyMsgCenterModel msgModel = setMsgModel(taskId, companyId, msgScene, linkId, receiveId, att, url, taskName, BusinessConstant.TRUE);
        return saveMsg(msgModel);
    }

//    public String addMsg(String taskId, String companyId, String msgScene, String linkId,
//                         String receiveId, String att, String url, String taskName, String thirdMsgFlag){
//        CompanyMsgCenterModel msgModel = setMsgModel(taskId, companyId, msgScene, linkId, receiveId, att, url, taskName, thirdMsgFlag);
//        return saveMsg(msgModel);
//    }

    @NotNull
    private CompanyMsgCenterModel setMsgModel(String taskId, String companyId, String msgScene, String linkId, String receiveId, String att, String url, String taskName, String thirdMsgFlag) {
        JSONObject attCont = new JSONObject();
        attCont.put("taskId", taskId);
        attCont.put("taskName", taskName);

        CompanyMsgCenterModel msgModel = new CompanyMsgCenterModel();
        msgModel.setCompanyId(companyId);
        msgModel.setBusinessScene(msgScene);
        msgModel.setEmpId(receiveId);
        msgModel.setHandlerStatus("false");
        msgModel.setLinkId(linkId);
        msgModel.setUrl(url);
        msgModel.setAttContent(attCont.toJSONString());
        msgModel.setParams(att);
        msgModel.setThirdMsgFlag(thirdMsgFlag);
        return msgModel;
    }

    public void updateHandlerStatus(String companyId, String businessScene, List<String> empIds, String handlerStatus, String linkId){
        if (StringUtils.isAnyBlank(companyId, businessScene, handlerStatus, linkId) || CollectionUtils.isEmpty(empIds)){
            return;
        }
        UpdateBuilder updateBuilder = UpdateBuilder.build("company_msg_center").set("handler_status", handlerStatus).whereEq("company_id", companyId)
                .whereIn("emp_id", empIds).whereEq("business_scene", businessScene).whereEq("link_id", linkId);
        autoBaseDao.update(updateBuilder);
    }

    public PagedList<CompanyMsgCenterModel> listPageCompanyMsgCenter(String companyId, String empId, String scene, int pageNo, int pageSize) {
        ComQB<CompanyMsgCenterModel> comQB = ComQB.build(CompanyMsgCenterModel.class)
                .whereEq("company_id",companyId).whereEq("emp_id",empId)
                .whereEq("business_scene",scene).whereEq("handler_status","false");
        comQB.setPage(pageNo, pageSize);
        return autoBaseDao.listPage(comQB);
    }

    public List<CompanyMsgCenterModel> queryNotHandleMsgList(String companyId, String empId, String linkId, String businessScene){
        if (StringUtils.isAnyBlank(linkId)){
            return null;
        }
        QueryBuilder queryBuilder = QueryBuilder.build(CompanyMsgCenterModel.class).whereEq("company_id",companyId).whereEq("emp_id",empId)
                .whereEq("link_id", linkId).whereEq("business_scene",businessScene).whereEq("handler_status","false");
        return autoBaseDao.listAll(queryBuilder);
    }

//    public List<CompanyMsgCenterModel> queryNotHandleMsgListByScenes(String companyId, String empId, String linkId, List<String> businessScene){
//        if (StringUtils.isAnyBlank(linkId)){
//            return null;
//        }
//        QueryBuilder queryBuilder = QueryBuilder.build(CompanyMsgCenterModel.class).whereEq("company_id",companyId).whereEq("emp_id",empId)
//                .whereEq("link_id", linkId).whereIn("business_scene",businessScene).whereEq("handler_status","false");
//        return autoBaseDao.listAll(queryBuilder);
//    }

//    public List<CompanyMsgCenterModel> queryNotHandleMsgListByEmpIds(String companyId, String empId, String linkId, String businessScene, List<String> empIds){
//        if (StringUtils.isAnyBlank(linkId, businessScene)){
//            return null;
//        }
//        QueryBuilder queryBuilder = QueryBuilder.build(CompanyMsgCenterModel.class).whereEq("company_id",companyId).whereEq("emp_id",empId)
//                .whereEq("link_id", linkId).whereEq("business_scene",businessScene).whereEq("handler_status","false")
//                .whereIn("emp_id", empIds);
//        return autoBaseDao.listAll(queryBuilder);
//    }

    public List<CompanyMsgCenterModel> queryNotHandleMsgListByEmpId(String companyId, String empId, String linkId, String businessScene){
        if (StringUtils.isAnyBlank(linkId, businessScene)){
            return null;
        }
        QueryBuilder queryBuilder = QueryBuilder.build(CompanyMsgCenterModel.class).whereEq("company_id",companyId).whereEq("emp_id",empId)
                .whereEq("link_id", linkId).whereEq("business_scene",businessScene).whereEq("handler_status","false");
        return autoBaseDao.listAll(queryBuilder);
    }

//    public List<CompanyMsgCenterModel> queryHandleMsgListByEmpId(String companyId, String empId, String linkId, String businessScene){
//        if (StringUtils.isAnyBlank(linkId, businessScene)){
//            return null;
//        }
//        QueryBuilder queryBuilder = QueryBuilder.build(CompanyMsgCenterModel.class).whereEq("company_id",companyId).whereEq("emp_id",empId)
//                .whereEq("link_id", linkId).whereEq("business_scene",businessScene);
//        return autoBaseDao.listAll(queryBuilder);
//    }

//    public List<CompanyMsgCenterModel> queryItemScoreNotHandleMsgList(String companyId, String linkId, String businessScene, String itemId){
//        if (StringUtils.isAnyBlank(linkId, businessScene)){
//            return null;
//        }
//        QueryBuilder queryBuilder = QueryBuilder.build(CompanyMsgCenterModel.class).whereEq("company_id",companyId).whereEq("params", itemId)
//                .whereEq("link_id", linkId).whereEq("business_scene",businessScene).whereEq("handler_status","false");
//        return autoBaseDao.listAll(queryBuilder);
//    }

    public void updateHandlerStatus(String id){
        if (StringUtils.isEmpty(id)){
            return;
        }
        UpdateBuilder updateBuilder = UpdateBuilder.build("company_msg_center").set("handler_status", "true").whereEq("id", id);
        autoBaseDao.update(updateBuilder);
    }

    public void updateThirdId(String id, String thirdMsgId){
            if (StringUtils.isEmpty(id)){
                return;
            }
        UpdateBuilder updateBuilder = UpdateBuilder.build("company_msg_center").set("third_msg_id", thirdMsgId).whereEq("id", id);
        autoBaseDao.update(updateBuilder);
    }

    public List<CompanyMsgCenterModel> listMsgByScene(String companyId, List<String> sceneList) {
        ComQB<CompanyMsgCenterModel> comQB = ComQB.build(CompanyMsgCenterModel.class)
                .whereEq("company_id",companyId)
                .whereIn("business_scene",sceneList);
        return autoBaseDao.listAll(comQB);
    }

    public CompanyMsgCenterModel findById(String id){
        return autoBaseDao.findById(CompanyMsgCenterModel.class, id);
    }

    public void updateMsg(String linkId,String type,String createdUser) {
        if (StringUtils.isBlank(linkId)) {
            return;
        }
        UpdateBuilder update = UpdateBuilder.build(CompanyMsgCenterModel.class)
                .set("handler_status", "true")
                .whereEq("link_id", linkId)
                .whereEq("emp_id", createdUser)
                .whereEq("business_scene", type);
        autoBaseDao.update(update);
    }
}
