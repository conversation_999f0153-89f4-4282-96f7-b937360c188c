package com.polaris.kpi.eval.infr.task.repimpl;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.hutool.core.collection.CollUtil;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.EvalScorersWrap;
import com.polaris.kpi.eval.domain.task.entity.empeval.TransferScorerRs;
import com.polaris.kpi.eval.domain.task.repo.EmpEvalScorerRepo;
import com.polaris.kpi.eval.infr.task.builder.EmpEvalScorerBatchDataBd;
import com.polaris.kpi.eval.infr.task.builder.EmpEvalScorerDataBd;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskUserDo;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.*;
import com.polaris.kpi.org.domain.dept.entity.CompanyMsgCenter;
import com.polaris.kpi.org.infr.company.ppojo.CompanyMsgCenterDo;
import com.polaris.kpi.org.infr.emp.pojo.EmployeeBaseInfoDo;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.lufei.ibatis.builder.BatchUpdateBuilder;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.DeleteBuilder;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.common.data.ToDataBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;


@Slf4j
@Component
public class EmpEvalScorerRepoImpl implements EmpEvalScorerRepo {
    @Autowired
    private DomainDaoImpl domainDao;

    private final Supplier<String> empEvalScorerIdGen = () -> domainDao.nextLongAsStr("emp_eval_scorer");
    private final Supplier<String> empEvalScorerNodeIdGen = () -> domainDao.nextLongAsStr("emp_scorer_score_node");
    private final Supplier<String> scorerScoreKpiItemIdGen = () -> domainDao.nextLongAsStr("perf_evaluate_task_score_result");

    @Override
    public  List<EmpEvalScorer> listEmpEvalScorer(String companyId, String taskUserId){
        //评分人评分环节
        ComQB scoreNodeComQB = ComQB.build(EmpEvalScorerNodeDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .appendWhere("((transfer_type is null) OR (transfer_type = 'transfer' AND transfer_from IS NOT NULL AND transfer_to IS NULL) OR (transfer_type = 'skip' AND transfer_from IS NOT NULL AND transfer_to IS NULL))")
                .whereEq("is_deleted", "false");
        List<EmpEvalScorerNode> scorerNodes = domainDao.listAllDomain(scoreNodeComQB, EmpEvalScorerNode.class);
        if (CollUtil.isEmpty(scorerNodes)) {
            return new ArrayList<>();
        }

        Set<String> scorerIds = scorerNodes.stream().map(EmpEvalScorerNode::getId).collect(Collectors.toSet());
        ComQB comQB = ComQB.build(EmpEvalScorerDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereInReq("scorer_id",scorerIds)
                .whereEq("is_deleted", "false");
        List<EmpEvalScorer> evalScorers = domainDao.listAllDomain(comQB, EmpEvalScorer.class);
        if (CollUtil.isEmpty(evalScorers)) {
            return new ArrayList<>();
        }

//        //评分人评分指标 eval_scorer_node_id
//        ComQB scorerScoreKpiItemComQB = ComQB.build(ScorerScoreKpiItemDo.class)
//                .whereEqReq("company_id", companyId)
//                .whereEqReq("task_user_id", taskUserId)
//                .whereEq("is_deleted", "false");
//        List<EvalScorerNodeKpiItem> scorerScoreKpiItems = domainDao.listAllDomain(scorerScoreKpiItemComQB, EvalScorerNodeKpiItem.class);
//        if (CollUtil.isNotEmpty(scorerScoreKpiItems)) {
//            //set 环节 指标
//            ListWrap<EvalScorerNodeKpiItem> scorerScoreKpiItemsWrap = new ListWrap<>(scorerScoreKpiItems).groupBy(EvalScorerNodeKpiItem::getScorerScoreNodeId);
//            scorerNodes.forEach(scorerNode -> {
//                if (!scorerScoreKpiItemsWrap.isEmpty() && scorerScoreKpiItemsWrap.groupGet(scorerNode.getId()) != null) {
//                    scorerNode.setScorerNodeKpiItems(scorerScoreKpiItemsWrap.groupGet(scorerNode.getId()));
//                }
//            });
//        }
        //set 评分人评分环节
        evalScorers.forEach(scorer -> {
            List<EmpEvalScorerNode> scorerNodesByScorerId = scorerNodes.stream().filter(node -> node.getEvalScorerId().equals(scorer.getId())).collect(Collectors.toList());
            scorer.setScorerNodes(scorerNodesByScorerId);
        });
        return evalScorers;
    }

    @Override
    public EvalScorersWrap getEmpEvalScorersWrap(String companyId, String taskUserId) {
        EvalScorersWrap scorersWrap = new EvalScorersWrap();
        ComQB comQB = ComQB.build(EmpEvalScorerDo.class)
                .whereEq("company_id", companyId)
                .whereEq("task_user_id", taskUserId)
                .whereEq("is_deleted", "false");
        List<EmpEvalScorer> evalScorers = domainDao.listAllDomain(comQB, EmpEvalScorer.class);
        if (CollUtil.isEmpty(evalScorers)) {
            return scorersWrap;
        }
        //评分人评分环节
        ComQB scoreNodeComQB = ComQB.build(EmpEvalScorerNodeDo.class)
                .whereEq("company_id", companyId)
                .whereEq("task_user_id", taskUserId)
                .whereEq("is_deleted", "false");
        List<EmpEvalScorerNode> scorerNodes = domainDao.listAllDomain(scoreNodeComQB, EmpEvalScorerNode.class);
        evalScorers.forEach(scorer -> {
            List<EmpEvalScorerNode> scorerNodesByScorerId = scorerNodes.stream().filter(node -> node.getEvalScorerId().equals(scorer.getId())).collect(Collectors.toList());
            scorer.setScorerNodes(scorerNodesByScorerId);
        });
        scorersWrap.setDatas(evalScorers);
        scorersWrap.groupBy(EmpEvalScorer::getScorerId);
        return scorersWrap;
    }

    @Override
    public EmpEvalScorer getEmpEvalScorer(String companyId, String taskUserId, String scorerId) {
        ComQB comQB = ComQB.build(EmpEvalScorerDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("scorer_id", scorerId)
                .whereEq("is_deleted", "false");
        EmpEvalScorer scorer = domainDao.findDomain(comQB, EmpEvalScorer.class);
        if (Objects.isNull(scorer)){
            return new EmpEvalScorer();
        }
        //评分人评分环节
        ComQB scoreNodeComQB = ComQB.build(EmpEvalScorerNodeDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("eval_scorer_id", scorer.getId())
                .whereEq("is_deleted", "false");
        List<EmpEvalScorerNode> scorerNodes = domainDao.listAllDomain(scoreNodeComQB, EmpEvalScorerNode.class);
        if (CollUtil.isEmpty(scorerNodes)){
            return scorer;
        }
//
//        List<String> evalScorerNodeIds = scorerNodes.stream().map(EmpEvalScorerNode::getId).collect(Collectors.toList());
//        //评分人评分指标
//        ComQB scorerScoreKpiItemComQB = ComQB.build(ScorerScoreKpiItemDo.class)
//                .whereEqReq("company_id", companyId)
//                .whereEqReq("task_user_id", taskUserId)
//                .whereInReq("scorer_score_node_id",evalScorerNodeIds)
//                .whereEq("is_deleted", "false");
//        List<EvalScorerNodeKpiItem> scorerScoreKpiItems = domainDao.listAllDomain(scorerScoreKpiItemComQB, EvalScorerNodeKpiItem.class);
//        if (CollUtil.isNotEmpty(scorerScoreKpiItems)) {
//            //set 环节 指标
//            ListWrap<EvalScorerNodeKpiItem> scorerScoreKpiItemsWrap = new ListWrap<>(scorerScoreKpiItems).groupBy(EvalScorerNodeKpiItem::getScorerScoreNodeId);
//            scorerNodes.forEach(scorerNode -> {
//                if (!scorerScoreKpiItemsWrap.isEmpty() && scorerScoreKpiItemsWrap.groupGet(scorerNode.getId()) != null) {
//                    scorerNode.setScorerNodeKpiItems(scorerScoreKpiItemsWrap.groupGet(scorerNode.getId()));
//                }
//            });
//        }
        scorer.setScorerNodes(scorerNodes);
        return scorer;
    }

    @Override
    public TransferScorerRs getTransferScorerRs(String companyId, String taskUserId, String fromScorerId,String toScorerId) {
        EmpEvalScorer fromScorer = getEmpEvalScorer(companyId, taskUserId, fromScorerId);
        EmpEvalScorer toScorer = getEmpEvalScorer(companyId, taskUserId, toScorerId);
        TransferScorerRs trs = new TransferScorerRs(fromScorerId,fromScorer, toScorerId,toScorer);
        List<String> scorerIds = Arrays.asList(fromScorerId, toScorerId);
        ComQB<EvalScoreResult> typeQb = ComQB.build(PerfEvalTypeResultDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereInReq("scorer_id", scorerIds)
                .whereEq("is_deleted", "false");
        typeQb.whereInReq("scorer_type", EvaluateAuditSceneEnum.allScoreTypes());
        List<EvalScoreResult> typeRs = this.domainDao.listAllDomain(typeQb, EvalScoreResult.class);
        ListWrap<EvalScoreResult> typeRsWrap = new ListWrap<>(typeRs).groupBy(EvalScoreResult::getScorerId);
        final ComQB comQB = ComQB.build(EmployeeBaseInfoDo.class, "e")
                .clearSelect().setRsType(KpiEmp.class)
                .select("status, employee_id  as emp_id,  name as emp_name, ding_user_id ex_user_id , avatar,jobnumber")
                .whereEqReq("companyId", companyId)
                .whereInReq("employeeId", scorerIds);
        List<KpiEmp> scorerEmp = domainDao.listAll(comQB);
        Map<String, KpiEmp> scorerEmpMap = CollUtil.toMap(scorerEmp, new HashMap<>(), KpiEmp::getEmpId);

        ComQB comMsgQB = ComQB.build(CompanyMsgCenterDo.class)
                .whereEqReq("company_id", companyId)
                .whereInReq("emp_id", scorerIds)
                .whereEqReq("link_id", taskUserId)
                .whereEq("handler_status", "false");
        List<CompanyMsgCenter> msgCenters = domainDao.listAllDomain(comMsgQB, CompanyMsgCenter.class);
        ListWrap<CompanyMsgCenter> msgCentersWrap = new ListWrap<>(msgCenters).groupBy(CompanyMsgCenter::getEmpId);
        trs.accpRs(typeRsWrap, scorerEmpMap, msgCentersWrap);
        return trs;
    }

    @Override
    public void saveBatchEmpEvalScorerForUsers(String companyId, String opEmpId, List<EvalScorersWrap> scorers) {
        if (CollUtil.isEmpty(scorers)){
            return;
        }

        List<String> taskUserIds = scorers.stream().map(EvalScorersWrap::getTaskUserId).distinct().collect(Collectors.toList());
        batchDelEmpEvalScorer(companyId, taskUserIds);//先清除，再新增

        List<EmpEvalScorer> empEvalScorers = scorers.stream().map(ListWrap::getDatas).flatMap(Collection::stream).collect(Collectors.toList());
        saveBatch(companyId, opEmpId, empEvalScorers);
    }
    @Override
    public void saveBatchEmpEvalScorer(String companyId, String opEmpId, String taskUserId, List<EmpEvalScorer> empEvalScorers) {
        delEmpEvalScorer(companyId, taskUserId);//先清除[ 物理删除 ]，再新增
        saveBatch(companyId, opEmpId, empEvalScorers);
    }

    @Override
    public void saveBatchEmpEvalScorerAndClearOld(String companyId, String opEmpId, String taskUserId, List<EmpEvalScorer> empEvalScorers) {
        clearEmpEvalScorer(companyId, taskUserId);//先清除[ 逻辑删除 ]，再新增【重置阶段环节的使用此】
        saveBatch(companyId, opEmpId, empEvalScorers);
    }


    @Override
    public void addEmpEvalScorer(String companyId, String opEmpId, EmpEvalScorer scorer) {
        EmpEvalScorerDataBd bd = new EmpEvalScorerDataBd(companyId, opEmpId, scorer, empEvalScorerIdGen, empEvalScorerNodeIdGen, scorerScoreKpiItemIdGen);
        bd.buildEmpEvalScorer();

        domainDao.save(bd.getScorerDo());
        domainDao.saveBatch(bd.getNodeDos());//save 评分环节
        domainDao.saveBatch(bd.getKpiItemDos());//save 评分环节指标
    }

    @Override
    public void saveDispatchNode(List<EmpEvalScorer> scorers) {
        if (CollUtil.isEmpty(scorers)) {
            return;
        }
        String companyId = scorers.get(0).getCompanyId().getId();
        String taskUserId = scorers.get(0).getTaskUserId();
        BatchUpdateBuilder up = BatchUpdateBuilder.buildTable("emp_eval_scorer")
                .addSetCaseProp("status", "id:=")
                .addSetCaseProp("updatedTime", "id:=")
                .whereEq("companyId", companyId)
                .whereEq("taskUserId", taskUserId)
                .whereEq("isDeleted", "false")
                .whereUseIn("id");

        //up 评分环节状态
        BatchUpdateBuilder upScorerNode = BatchUpdateBuilder.buildTable("emp_scorer_score_node")
                .addSetCaseProp("status", "id:=")
                .addSetCaseProp("scorerNodeKpiTypes", "id:=")
                .whereEq("companyId", companyId)
                .whereEq("taskUserId", taskUserId)
                .whereEq("isDeleted", "false")
                .whereUseIn("id");

        //up 评分环节状态
        BatchUpdateBuilder upScorerNodeKpi = BatchUpdateBuilder.buildTable("emp_scorer_score_kpi_item")
                .addSetCaseProp("status", "id:=")
                .whereEq("companyId", companyId)
                .whereEq("taskUserId", taskUserId)
                .whereEq("isDeleted", "false")
                .whereUseIn("id");

        boolean isUpScorer = false;
        boolean isUpScorerNode = false;
        boolean isUpScorerKpi = false;
        for (EmpEvalScorer scorer : scorers) {
            if (scorer.isFinished()){//已完成的不更新
                continue;
            }

            //评分人
            EmpEvalScorerDo evalScorerDo = new ToDataBuilder<>(scorer, EmpEvalScorerDo.class).data();
            evalScorerDo.accup(companyId);
            up.addBean(evalScorerDo);
            isUpScorer = true;

            for (EmpEvalScorerNode scorerNode : scorer.getScorerNodes()) {
                if (!scorerNode.isDispatch()) {//不是已分发的不更新
                    continue;
                }
                //评分环节
                EmpEvalScorerNodeDo scorerNodeDo = new ToDataBuilder<>(scorerNode, EmpEvalScorerNodeDo.class).data();
                scorerNodeDo.accup(companyId);
                upScorerNode.addBean(scorerNode);
                isUpScorerNode = true;
                if (CollUtil.isEmpty(scorerNode.getScorerNodeKpiTypes())) {
                    continue;
                }
                for (EvalScorerNodeKpiType scorerNodeKpiType : scorerNode.getScorerNodeKpiTypes()) {
                    if (CollUtil.isEmpty(scorerNodeKpiType.getScorerNodeKpiItems())) {
                        continue;
                    }
                    //评价指标
                    for (EvalScorerNodeKpiItem kpiItem : scorerNodeKpiType.getScorerNodeKpiItems()) {
                        EmpScorerScoreKpiItemDo kpiItemDo = new ToDataBuilder<>(kpiItem, EmpScorerScoreKpiItemDo.class).data();
                        kpiItemDo.accup(companyId);
                        upScorerNodeKpi.addBean(kpiItemDo);
                        isUpScorerKpi = true;
                    }
                }
            }
        }
        if (isUpScorer) {
            domainDao.updateBatch(up);
        }
        if (isUpScorerNode) {
            domainDao.updateBatch(upScorerNode);
        }
        if (isUpScorerKpi){
            domainDao.updateBatch(upScorerNodeKpi);
        }
    }

    public void saveTotalScorerNode(List<EmpEvalScorer> scorers) {
        if (CollUtil.isEmpty(scorers)) {
            return;
        }
        String companyId = scorers.get(0).getCompanyId().getId();
        String taskUserId = scorers.get(0).getTaskUserId();
        BatchUpdateBuilder up = BatchUpdateBuilder.buildTable("emp_eval_scorer")
                .addSetCaseProp("status", "id:=")
                .addSetCaseProp("updatedTime", "id:=")
                .whereEq("companyId", companyId)
                .whereEq("taskUserId", taskUserId)
                .whereEq("isDeleted", "false")
                .whereUseIn("id");

        BatchUpdateBuilder upScorerNode = BatchUpdateBuilder.buildTable("emp_scorer_score_node")
                .addSetCaseProp("status", "id:=")
                .whereEq("companyId", companyId)
                .whereEq("taskUserId", taskUserId)
                .whereEq("isDeleted", "false")
                .whereUseIn("id");

        for (EmpEvalScorer scorer : scorers) {
            if (scorer.isFinished()){//已完成的不更新
                continue;
            }
            //评分人
            EmpEvalScorerDo evalScorerDo = new ToDataBuilder<>(scorer, EmpEvalScorerDo.class).data();
            evalScorerDo.accup(companyId);
            up.addBean(evalScorerDo);

            for (EmpEvalScorerNode scorerNode : scorer.getScorerNodes()) {
                if (!scorerNode.isDispatch()) {//不是分发的不更新
                    continue;
                }
                //评分环节
                EmpEvalScorerNodeDo scorerNodeDo = new ToDataBuilder<>(scorerNode, EmpEvalScorerNodeDo.class).data();
                scorerNodeDo.setCompanyId(companyId);
                upScorerNode.addBean(scorerNode);
            }
        }
        domainDao.updateBatch(up);
        domainDao.updateBatch(upScorerNode);
    }
    @Override
    public void batchUpAndDelEmpEvalScorer(String companyId, String opEmpId, List<EmpEvalScorer> scorers) {
        if (CollUtil.isEmpty(scorers)) {
            return;
        }
        String taskUserId = scorers.get(0).getTaskUserId();
        //先进行删除，再新增
        clearEmpEvalScorerByScorerId(companyId, taskUserId, scorers);
        //筛选出评分环节不为空的。为空则不新增了
        scorers = scorers.stream().filter(scorer -> CollUtil.isNotEmpty(scorer.getScorerNodes())).collect(Collectors.toList());
        if (CollUtil.isEmpty(scorers)) {
            return;
        }
        //再新增
        saveBatch(companyId, opEmpId, scorers);
    }

    private void saveBatch( String companyId, String opEmpId, List<EmpEvalScorer> empEvalScorers){
        EmpEvalScorerBatchDataBd scorerBatchDataBd = new EmpEvalScorerBatchDataBd(companyId,
                opEmpId, empEvalScorerIdGen, empEvalScorerNodeIdGen, scorerScoreKpiItemIdGen);
        scorerBatchDataBd.buildBatchEmpEvalScorer(empEvalScorers);
        // ========== 【优化2：降低死锁风险】按ID排序，保证所有事务以相同顺序锁定索引页 ==========
        scorerBatchDataBd.getScorerDo().sort(java.util.Comparator.comparing(EmpEvalScorerDo::getId));
        scorerBatchDataBd.getNodeDos().sort(java.util.Comparator.comparing(EmpEvalScorerNodeDo::getId));
        scorerBatchDataBd.getKpiItemDos().sort(java.util.Comparator.comparing(EmpScorerScoreKpiItemDo::getId));
        // ========================================================================================
        domainDao.saveBatch(scorerBatchDataBd.getScorerDo());  //评分人
        domainDao.saveBatch(scorerBatchDataBd.getNodeDos());  //评分环节
        domainDao.saveBatch(scorerBatchDataBd.getKpiItemDos());  //评分指标
    }
    @Override
    public void batchAddEmpEvalScorer(String companyId, String taskUserId, List<EmpEvalScorer> scorers) {
        if (CollUtil.isEmpty(scorers)) {
            return;
        }

        //先进行删除，再新增
        delEmpEvalScorer(companyId,taskUserId);
        saveBatch(companyId, scorers.get(0).getCreatedUser(), scorers);
    }


    @Override
    public void upEmpEvalScorer(String companyId, EmpEvalScorer scorer) {
        int count = domainDao.update(EmpEvalScorerDo.class, scorer);
        if (count > 0) {
            scorer.incrementVersion();
        }
    }

    private void clearEmpEvalScorerByScorerId(String companyId, String taskUserId, List<EmpEvalScorer> scorers) {
        if (CollUtil.isEmpty(scorers)) {
            return;
        }
        List<String> scorerIds = scorers.stream().map(EmpEvalScorer::getScorerId).collect(Collectors.toList());
        List<String> scorerNodeIds = new ArrayList<>();
        //EmpEvalScorer.EmpEvalScorerNode 的 id
        scorers.stream().flatMap(scorer -> scorer.getScorerNodes().stream()).map(EmpEvalScorerNode::getId).forEach(scorerNodeIds::add);

        //先清除评分人
        DeleteBuilder upScorerDel =  DeleteBuilder.build(EmpEvalScorerDo.class)
                .whereEqReq("company_id",companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereInReq("scorer_id", scorerIds)
                .whereEqReq("is_deleted", "false");
        //清除评分人评分环节
        final DeleteBuilder upScorerNodeDel = DeleteBuilder.build(EmpEvalScorerNodeDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereInReq("scorer_id", scorerIds)
                .whereEq("is_deleted", "false");

        //清除评分人评分环节评价指标
        final DeleteBuilder upKpiItemDel = DeleteBuilder.build(EmpScorerScoreKpiItemDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereInReq("scorer_score_node_id", scorerNodeIds)
                .whereEq("is_deleted", "false");

        domainDao.delete(upScorerDel);
        domainDao.delete(upScorerNodeDel);
        domainDao.delete(upKpiItemDel);

    }
    public void clearEmpEvalScorer(String companyId, String taskUserId) {
        //先清除评分人
        UpdateBuilder upScorer = UpdateBuilder.build(EmpEvalScorerDo.class)
                .appendSet("is_deleted='true'")
                .whereEqReq("company_id",companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("is_deleted", "false");
        domainDao.update(upScorer);

        //清除评分人评分环节
        final UpdateBuilder upScorerNode = UpdateBuilder.build(EmpEvalScorerNodeDo.class)
                .set("is_deleted", "true")
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEqReq("is_deleted", "false");
        domainDao.update(upScorerNode);
        //清除评分人评分环节评价指标
        final UpdateBuilder upKpiItem = UpdateBuilder.build(EmpScorerScoreKpiItemDo.class)
                .set("is_deleted", "true")
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("is_deleted", "false");
        domainDao.update(upKpiItem);
    }

    public void delEmpEvalScorer(String companyId, String taskUserId) {
        //先清除评分人
        DeleteBuilder upScorerDel =  DeleteBuilder.build(EmpEvalScorerDo.class)
                .whereEqReq("company_id",companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("is_deleted", "false");
        domainDao.delete(upScorerDel);

        //清除评分人评分环节
        final DeleteBuilder upScorerNodeDel = DeleteBuilder.build(EmpEvalScorerNodeDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("is_deleted", "false");
        domainDao.delete(upScorerNodeDel);
        //清除评分人评分环节评价指标
        final DeleteBuilder upKpiItemDel = DeleteBuilder.build(EmpScorerScoreKpiItemDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("is_deleted", "false");
        domainDao.delete(upKpiItemDel);
    }
    private void batchDelEmpEvalScorer(String companyId, List<String> taskUserIds) {
        //先清除评分人
        DeleteBuilder upScorerDel =  DeleteBuilder.build(EmpEvalScorerDo.class)
                .whereEqReq("company_id",companyId)
                .whereInReq("task_user_id", taskUserIds)
                .whereEq("is_deleted", "false");
        domainDao.delete(upScorerDel);

        //清除评分人评分环节
        final DeleteBuilder upScorerNodeDel = DeleteBuilder.build(EmpEvalScorerNodeDo.class)
                .whereEqReq("company_id", companyId)
                .whereInReq("task_user_id", taskUserIds)
                .whereEq("is_deleted", "false");
        domainDao.delete(upScorerNodeDel);
        //清除评分人评分环节评价指标
        final DeleteBuilder upKpiItemDel = DeleteBuilder.build(EmpScorerScoreKpiItemDo.class)
                .whereEqReq("company_id", companyId)
                .whereInReq("task_user_id", taskUserIds)
                .whereEq("is_deleted", "false");
        domainDao.delete(upKpiItemDel);
    }


    private void delEmpEvalScorer(TenantId companyId, String taskUserId) {
        //del 评分人
        DeleteBuilder empEvalScorer = DeleteBuilder.build(EmpEvalScorerDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.delete(empEvalScorer);
        //del 评分人评分环节
        DeleteBuilder empEvalScorerNode = DeleteBuilder.build(EmpEvalScorerNodeDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.delete(empEvalScorerNode);
        //del 评分人评分环节指标
        DeleteBuilder empEvalScorerScoreKpiItem = DeleteBuilder.build(EmpScorerScoreKpiItemDo.class)
                .whereEqReq("company_id", companyId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        domainDao.delete(empEvalScorerScoreKpiItem);
    }

    @Override
    public void resetScoreEmpEvalScorer(String companyId,String opEmpId, String taskUserId, List<EmpEvalScorer> empEvalScorers) {
        if (CollUtil.isEmpty(empEvalScorers)){
            return;
        }

        //清除评分人未完成
        BatchUpdateBuilder up = BatchUpdateBuilder.buildTable("emp_eval_scorer")
                .addSetCaseProp("status", "id:=")
                .addSetCaseProp("updatedTime", "id:=")
                .whereEq("companyId", companyId)
                .whereEq("taskUserId", taskUserId)
                .whereEq("isDeleted", "false")
                .whereUseIn("id");

        //清除评分人状态为待分发
        BatchUpdateBuilder upScorerNode = BatchUpdateBuilder.buildTable("emp_scorer_score_node")
                .addSetCaseProp("status", "id:=")
                .addSetCaseProp("scorerNodeScore", "id:=")
                .addSetCaseProp("scorerNodeKpiTypes", "id:=")
                .addSetCaseProp("totalComment", "id:=")
                .addSetCaseProp("scoreLevel", "id:=")
                .addSetCaseProp("totalScore", "id:=")
                .addSetCaseProp("scoreAttUrl", "id:=")
                .addSetCaseProp("handlerTime", "id:=")
                .whereEq("companyId", companyId)
                .whereEq("taskUserId", taskUserId)
                .whereEq("isDeleted", "false")
                .whereUseIn("id");

        Set<String> empEvalScorerNodeIds = new HashSet<>();
        for (EmpEvalScorer scorer : empEvalScorers) {
            EmpEvalScorerDo evalScorerDo = new ToDataBuilder<>(scorer, EmpEvalScorerDo.class).data();
            evalScorerDo.accup(companyId, opEmpId);
            up.addBean(evalScorerDo);

            for (EmpEvalScorerNode scorerNode : scorer.getScorerNodes()) {
                EmpEvalScorerNodeDo scorerNodeDo = new ToDataBuilder<>(scorerNode, EmpEvalScorerNodeDo.class).data();
                scorerNodeDo.accup(companyId,opEmpId);
                upScorerNode.addBean(scorerNode);
                empEvalScorerNodeIds.add(scorerNode.getId());
            }
        }
        //清除评分人指标打分
        UpdateBuilder upKpi = UpdateBuilder.build("emp_scorer_score_kpi_item")
                .set("status", 0)//待分发
                .set("score", null).set("no_item_score", null).set("score_weight_score", null)
                .set("item_weight_score", null).set("final_score", null).set("final_weight_score", null)
                .set("score_comment", null).set("score_att_url", null).set("score_level", null)
                .set("score_option", null).set("veto_flag", null)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereInReq("scorer_score_node_id", empEvalScorerNodeIds)
                .whereEqReq("is_deleted", Boolean.FALSE.toString());

        UpdateBuilder clearCache = UpdateBuilder.build("submit_score_cache")
                .set("isDeleted", Boolean.TRUE.toString())
                .whereEqReq("companyId", companyId)
                .whereEqReq("taskUserId", taskUserId)
                .whereEqReq("isDeleted", Boolean.FALSE.toString());


        domainDao.updateBatch(up);
        domainDao.updateBatch(upScorerNode);
        domainDao.update(upKpi);
        domainDao.update(clearCache);//重置清除缓存
    }

    @Override
    public Set<String> listProcessedTaskUsers(String companyId, List<String> taskUserIds, String status, Integer versionIgnored) {
        if (CollUtil.isEmpty(taskUserIds)) {
            return java.util.Collections.emptySet();
        }
        // 以用户表为主，连接评分人表，要求两边版本一致（用以判定该版本已迁移过）
        ComQB qb = ComQB.build(PerfEvaluateTaskUserDo.class, "u")
                .clearSelect().select("DISTINCT u.id")
                .setRsType(String.class)
                .leftJoin(EmpEvalScorerDo.class, "s").appendOn("u.company_id = s.company_id AND u.id = s.task_user_id")
                .whereEqReq("u.company_id", companyId)
                .whereEq("u.is_deleted", Boolean.FALSE.toString())
                .whereInReq("u.id", taskUserIds)
                .appendWhere("s.is_deleted = 'false'")
                .appendWhere("u.version = s.version");
        List<String> ids = domainDao.listAll(qb);
        return new java.util.HashSet<>(ids);
    }
}
