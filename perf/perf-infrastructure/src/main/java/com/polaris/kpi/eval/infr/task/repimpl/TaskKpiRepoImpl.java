package com.polaris.kpi.eval.infr.task.repimpl;

import cn.com.polaris.kpi.eval.FinishValue;
import cn.com.polaris.kpi.eval.KpiItemUsedField;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.perf.www.common.em.EvaluateTaskStatusEnum;
import com.perf.www.common.em.OperationLogSceneEnum;
import com.polaris.kpi.eval.domain.task.dmsvc.ModifyFinishValueDmSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.RejectFinishedValueDmSvc;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalKpiType;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalRule;
import com.polaris.kpi.eval.domain.task.entity.empeval.KpiListWrap;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrGoal;
import com.polaris.kpi.eval.domain.task.repo.TaskKpiRepo;
import com.polaris.kpi.eval.domain.task.type.FinishValueAuditStatusEnum;
import com.polaris.kpi.eval.infr.task.builder.EvalItemScoreRuleDataBd;
import com.polaris.kpi.eval.infr.task.builder.EvalScoreRule2DataBd;
import com.polaris.kpi.eval.infr.task.builder.ItemFinishedValueAudit2DataBd;
import com.polaris.kpi.eval.infr.task.ppojo.*;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.EmpEvalKpiTypeDo;
import com.polaris.kpi.eval.infr.task.ppojo.okr.OkrGoalDo;
import com.polaris.kpi.org.infr.company.ppojo.CompanyConfDo;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import org.apache.commons.lang3.StringUtils;
import org.lufei.ibatis.builder.BatchUpdateBuilder;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.DeleteBuilder;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.common.DomainToDataBuilder;
import org.lufei.ibatis.common.data.ToDataBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Component
public class TaskKpiRepoImpl implements TaskKpiRepo {

    @Resource
    private DomainDaoImpl domainDao;
    private final String taskFileSeq = "task_file";
    public static final String inputBakSeq = "input_bak";
    public static final String inputFinishValCacheSeq = "input_finish_val_cache";
    private final Supplier<String> inputFinishValCacheIdGen = () -> domainDao.nextLongAsStr(inputFinishValCacheSeq);
    public static final String perfEvaluateOkrGoalSeq = "perf_evaluate_okr_goal";
    public static final String importFinishValCacheSeq = "input_finish_val_cache";
    private final Supplier<String> importFinishValCacheIdGen = () -> domainDao.nextLongAsStr(importFinishValCacheSeq);

    public void setDomainDao(DomainDaoImpl domainDao) {
        this.domainDao = domainDao;
    }

    @Override
    public void updateTaskKpiInputEmp(TenantId companyId, String taskUserId, String updateUser, String id, String newInputEmpId) {
        UpdateBuilder builder = UpdateBuilder.build(PerfEvaluateTaskKpiDo.class).set(" result_input_emp_id", newInputEmpId).set(" result_input_type", "user").set(" updated_user", updateUser).whereEqReq("company_id", companyId.getId()).whereEqReq("task_user_id", taskUserId).whereEq("is_deleted", "false").whereEqReq("id", id);
        domainDao.update(builder);
    }

    @Override
    public void inputItems(List<EvalKpi> kpis) {
        List<PerfEvaluateTaskKpiDo> tasKpis = kpis.stream().map(taskKpi -> new ToDataBuilder<>(taskKpi, PerfEvaluateTaskKpiDo.class).data()).collect(Collectors.toList());
        for (PerfEvaluateTaskKpiDo taskKpi : tasKpis) {
            domainDao.update(taskKpi);
        }
    }

    @Override
    public void batchUpdateFinishValue(TenantId tenantId, String taskUserId, EmpId opEmpId, List<FinishValue> finishValues, List<EvalKpi> oldKpis, String businessScene) {
        List<OperationLogDo> logDoList = new ArrayList<>();
        ListWrap<EvalKpi> kpiMap = new ListWrap<>(oldKpis).asMap(EvalKpi::getId);
        List<PerfEvaluateTaskFileDo> fileDataList = new ArrayList<>();
        List<PerfEvaluateTaskKpiDo> kpiDos = new ArrayList<>();
        for (FinishValue finishValue : finishValues) {
            EvalKpi oldKpi = kpiMap.mapGet(finishValue.getId());
            if (Objects.isNull(oldKpi)) {
                continue;
            }
            PerfEvaluateTaskKpiDo kpiDo = new ToDataBuilder<>(finishValue, PerfEvaluateTaskKpiDo.class).data();
            kpiDos.add(kpiDo);
            UpdateBuilder update = UpdateBuilder.build(PerfEvaluateTaskKpiDo.class)
                    .setBean(kpiDo)
                    .whereEq("company_id", tenantId.getId())
                    .whereEq("task_user_id", taskUserId)
                    .whereEqReq("id", finishValue.getId());
            if (!finishValue.isPlanItem()) {
                update.set("item_finish_value", finishValue.getItemFinishValue());
                update.set("work_item_finish_value", finishValue.getWorkItemFinishValue());
                update.set("item_finish_value_text", finishValue.getItemFinishValueText());
            }
            if (OperationLogSceneEnum.FINISH_VALUE_AUDIT.getScene().equals(businessScene)) {
                update.set("finish_value_audit_status", 1); // 更新指标审核状态为通过
            }
//            if (OperationLogSceneEnum.SUBMIT_FINISH_VALUE.getScene().equals(businessScene)){
//                update.set("finish_value_audit_status", null);
//            }
            domainDao.update(update);
            //记录日志
            OperationLogDo logModel = new OperationLogDo(tenantId.getId(), taskUserId,
                    StringUtils.isNotBlank(businessScene) ? businessScene : oldKpi.isSubmitFinishValueScene(),
                    oldKpi.beforeItemFinishValue(finishValue.getWorkItemFinishValue()),
                    oldKpi.afterItemFinishValue(finishValue.getItemFinishValue(), finishValue.getItemFinishValueText(),
                            finishValue.getWorkItemFinishValue()), opEmpId.getId(), oldKpi.getKpiItemName(), finishValue.getFinishValueComment(),
                    oldKpi.getItemUnit(), JSONObject.toJSONString(finishValue.getFiles()), oldKpi.getKpiItemId(),
                    finishValue.getWorkItemFinishValue(), oldKpi.getInputFormat());
            logDoList.add(logModel);

            oldKpi.setFinalSubmitFinishValue(finishValue.getFinalSubmitFinishValue());
            oldKpi.setItemFinishValue(finishValue.getItemFinishValue());
            oldKpi.setItemFinishValueText(finishValue.getItemFinishValueText());
            oldKpi.setWorkItemFinishValue(finishValue.getWorkItemFinishValue());
            //如果是经营计划指标则不继续进行更新
            if (finishValue.isPlanItem()) {
                continue;
            }

            UpdateBuilder updateFile = UpdateBuilder.build(PerfEvaluateTaskFileDo.class)
                    .set("is_deleted", Boolean.TRUE.toString())
                    .whereEq("company_id", tenantId.getId())
                    .whereEq("task_user_id", taskUserId)
                    .whereEqReq("kpi_item_id", oldKpi.getKpiItemId()).whereEqReq("is_deleted", Boolean.FALSE.toString());
            domainDao.update(updateFile);//更新，提交都需将原来的提交附件给更新is_deleted为false
            //记录附件
            if (CollUtil.isEmpty(finishValue.getFiles())) {
                //finishValue.getFiles() =  null 修改此指标的附件为删除。
                continue;
            }
            for (FinishValue.ItemFile file : finishValue.getFiles()) {
                FinishValue.FileData fileData = file.getFileData();
                if (Objects.isNull(fileData)) {
                    // 图片入口上传的也需存入附件表 //https://topscrm.oss-cn-hangzhou.aliyuncs.com/info/xYCxGTChRM.png
                    fileData = new FinishValue.FileData(file.getConType(), file.getUrl());
                }
                fileData.accop(tenantId.getId(), finishValue.getFinishValueComment(), oldKpi.getKpiItemId(), businessScene,
                        taskUserId, opEmpId.getId(), opEmpId.getId());
                PerfEvaluateTaskFileDo taskFileDo = new ToDataBuilder<>(fileData, PerfEvaluateTaskFileDo.class).data();
                taskFileDo.setId(domainDao.nextLongAsStr(taskFileSeq));
                taskFileDo.setIsDeleted(Boolean.FALSE.toString());
                fileDataList.add(taskFileDo);
            }
        }

//        BatchUpdateBuilder.buildTable("perf_evaluate_task_kpi")
//                .addSetCaseProp("scoreRuleSnapId", "id:=")
        //插入日志
        domainDao.saveBatch(logDoList);
        //插入文件
        domainDao.saveBatch(fileDataList);
        //更新后清除缓存(如果是单个更新，不能清除全部暂存)
        deletedCacheInfo(tenantId, taskUserId, CollUtil.map(finishValues, FinishValue::getKpiItemId, true), opEmpId);
    }

    @Override
    public void batchUpdateFinishValueAuditStatus(TenantId tenantId, String taskUserId, List<String> kpiItemIds) {
        UpdateBuilder update = UpdateBuilder.build(PerfEvaluateTaskKpiDo.class)
                .set("finish_value_audit_status", 1)
                .whereEq("company_id", tenantId.getId())
                .whereEq("task_user_id", taskUserId)
                .whereInReq("kpi_item_id", kpiItemIds);
        domainDao.update(update);
    }


    public void saveFinishValue(ModifyFinishValueDmSvc valueDmSvc) {
        EvalUser evalUser = valueDmSvc.getEvalUser();
        TenantId tenantId = evalUser.getCompanyId();
        String taskUserId = evalUser.getId();
        for (EvalKpi oldKpi : valueDmSvc.getValueChangedKpis()) {
            UpdateBuilder update = UpdateBuilder.build(PerfEvaluateTaskKpiDo.class).setBean(new ToDataBuilder<>(oldKpi, PerfEvaluateTaskKpiDo.class).data()).whereEq("company_id", tenantId.getId()).whereEq("task_user_id", taskUserId).whereEqReq("id", oldKpi.getId());
            domainDao.update(update);
        }
        //记录日志
        List<OperationLogDo> logDos = CollUtil.map(valueDmSvc.getLogs(), dm -> new ToDataBuilder<>(dm, OperationLogDo.class).data(), true);
        domainDao.saveBatch(logDos);
        //记录附件
        List<PerfEvaluateTaskFileDo> fileDos = CollUtil.map(valueDmSvc.getValueFiles(), dm -> {
            PerfEvaluateTaskFileDo data = new ToDataBuilder<>(dm, PerfEvaluateTaskFileDo.class).data();
            data.setId(domainDao.nextLongAsStr(taskFileSeq));
            return data;
        }, true);
        domainDao.saveBatch(fileDos);
        UpdateBuilder user = UpdateBuilder.build(PerfEvaluateTaskUserDo.class).set(" input_finish_status ", evalUser.getInputFinishStatus()).whereEqReq(" id ", taskUserId).whereEqReq("company_id", tenantId.getId());
        domainDao.update(user);
        List<String> kpiItemIds = CollUtil.map(valueDmSvc.getValueChangedKpis(), EvalKpi::getKpiItemId, true);
        this.deletedCacheInfo(tenantId, taskUserId, kpiItemIds, valueDmSvc.getOpEmpId());
    }

    public void modifyItem(List<EvalKpi> evalKpis) {
        List<PerfEvaluateTaskKpiDo> kpiDos = evalKpis.stream().map(kpi -> {
            PerfEvaluateTaskKpiDo kpiItem = new ToDataBuilder<>(kpi, PerfEvaluateTaskKpiDo.class).data();
            return kpiItem;
        }).collect(Collectors.toList());

        Map<String, List<PerfEvaluateTaskKpiDo>> operType = kpiDos.stream().collect(Collectors.groupingBy(item -> item.getExamineOperType()));
        List<PerfEvaluateTaskKpiDo> modifyItems = operType.get("update");
        List<PerfEvaluateTaskKpiDo> addItems = operType.get("add");
        List<PerfEvaluateTaskKpiDo> delItems = operType.get("delete");
        //修改的
        if (CollUtil.isNotEmpty(modifyItems)) {
            for (PerfEvaluateTaskKpiDo modifyItem : modifyItems) {
                UpdateBuilder update = UpdateBuilder.build(PerfEvaluateTaskKpiDo.class).setBean(modifyItem).whereEq("id", modifyItem.getId()).whereEq("task_userid", modifyItem.getTaskUserId()).whereEq("company_id", modifyItem.getCompanyId());
                int count = domainDao.update(update);
                if (count > 1) {
                    throw new RuntimeException("注意！ 更新时Id 为空");
                }
            }
        }
        //新增的
        if (CollUtil.isNotEmpty(addItems)) {
            addItems.forEach(item -> {
                item.setId(UUID.randomUUID().toString());
            });
            domainDao.saveBatch(addItems);
        }

        //删除的
        if (CollUtil.isEmpty(delItems)) {
            return;
        }
        List<String> delIds = delItems.stream().map(del -> del.getId()).collect(Collectors.toList());
        UpdateBuilder del = UpdateBuilder.build(PerfEvaluateTaskKpiDo.class).set("is_deleted", Boolean.TRUE.toString()).set("updated_time", new Date()).whereIn("id", delIds);
        domainDao.update(del);
    }

    public void reSetKpiSubmitTab(TenantId tenantId, String taskUserId, String resetNode) {
        UpdateBuilder cacheQb = UpdateBuilder.build(CompanyCacheInfoDo.class).set("is_deleted", Boolean.TRUE.toString()).whereEqReq("link_id", taskUserId).whereEqReq("company_id", tenantId.getId()).whereEq("is_deleted", Boolean.FALSE.toString()).whereEqReq("business_scene", "change_item_audit");
        domainDao.update(cacheQb);

        UpdateBuilder kpiQb = UpdateBuilder.build(PerfEvaluateTaskKpiDo.class)
                .set("final_submit_finish_value", 0)
                .set("finish_value_audit_status", 0)
                .set("finish_value_audit_reason", null)
                .whereEqReq("company_id", tenantId.getId()).whereEqReq("task_user_id", taskUserId);
        List<String> nodes = Arrays.asList(EvaluateTaskStatusEnum.CREATED.getStatus(), EvaluateTaskStatusEnum.CONFIRMING.getStatus(), EvaluateTaskStatusEnum.CONFIRMED.getStatus());
        if (nodes.contains(resetNode)) {
            domainDao.update(kpiQb);
            return;
        }
        ComQB comQB = ComQB.build(CompanyConfDo.class).clearSelect().select("can_res_submit_input_finish").setRsType(Integer.class).whereEqReq("id", tenantId.getId());
        Integer canResSubmitInputFinish = domainDao.findOne(comQB);
        if (canResSubmitInputFinish != 1) {
            return;
        }
        domainDao.update(kpiQb);
    }

    public void reSetKpiSubmitTab(TenantId tenantId, String taskUserId, String resetNode, Boolean inputOnScoring) {
        UpdateBuilder cacheQb = UpdateBuilder.build(CompanyCacheInfoDo.class).set("is_deleted", Boolean.TRUE.toString()).whereEqReq("link_id", taskUserId).whereEqReq("company_id", tenantId.getId()).whereEq("is_deleted", Boolean.FALSE.toString()).whereEqReq("business_scene", "change_item_audit");
        domainDao.update(cacheQb);

        UpdateBuilder kpiQb = UpdateBuilder.build(PerfEvaluateTaskKpiDo.class)
                .set("final_submit_finish_value", 0)
                .set("finish_value_audit_status", 0)
                .set("finish_value_audit_reason", null)
                .set("item_auto_score", null)
                .set("item_finish_value", null)
                .whereEqReq("company_id", tenantId.getId()).whereEqReq("task_user_id", taskUserId);
        List<String> nodes = Arrays.asList(EvaluateTaskStatusEnum.CREATED.getStatus(), EvaluateTaskStatusEnum.CONFIRMING.getStatus(), EvaluateTaskStatusEnum.CONFIRMED.getStatus());
        if (nodes.contains(resetNode)) {
            domainDao.update(kpiQb);
            return;
        }
        ComQB comQB = ComQB.build(CompanyConfDo.class).clearSelect().select("can_res_submit_input_finish").setRsType(Integer.class).whereEqReq("id", tenantId.getId());
        Integer canResSubmitInputFinish = domainDao.findOne(comQB);
        if (canResSubmitInputFinish != 1) {
            return;
        }
        domainDao.update(kpiQb);
    }

    @Override
    public void updateOkrKpi(EmpId opEmpId, KpiListWrap okrTypes, List<EvalKpi> addKpis, List<EvalRefOkr> adds,
                             List<EvalRefOkr> dels, List<EvalRefOkr> edits, EmpEvalRule empEvalRule, boolean isOpenAvgWeightCompute) {

        EmpEvalKpiType type1 = okrTypes.getDatas().get(0);
        String companyId = type1.getCompanyId().getId();
        Map<String, EmpEvalKpiType> okrTypeMap = okrTypes.getDatas().stream().collect(Collectors.toMap(EmpEvalKpiType::getKpiTypeId, Function.identity()));

        if (CollUtil.isNotEmpty(addKpis)) {
            Supplier<String> evalCommonIdGen = () -> domainDao.nextLongAsStr(TaskUserRepoImpl.evalCommonSeq);
            EvalItemScoreRuleDataBd itemScoreRuleDataBd = new EvalItemScoreRuleDataBd(evalCommonIdGen);
            itemScoreRuleDataBd.buildCustomKpiAndRuleDos(addKpis, okrTypeMap);
            domainDao.saveBatch(itemScoreRuleDataBd.getKpiDos());
            domainDao.saveBatch(itemScoreRuleDataBd.getCustRuleDos());

            Supplier<String> auditIdGen = () -> domainDao.nextLongAsStr(TaskUserRepoImpl.auditSeq);
            EvalKpi evalKpi = addKpis.get(0);
            EvalScoreRule2DataBd bd = new EvalScoreRule2DataBd(auditIdGen, companyId, evalKpi.getTaskUserId(),
                    opEmpId.getId(), evalKpi.getTaskId(), isOpenAvgWeightCompute);
            List<TaskAuditDo> auditDos = bd.buildCustomAuditDos(addKpis, evalKpi.getEmpId());
            domainDao.saveBatch(auditDos);

            ItemFinishedValueAudit2DataBd audit2DataBd = new ItemFinishedValueAudit2DataBd(auditIdGen, empEvalRule, itemScoreRuleDataBd.getKpiDos(), companyId, evalKpi.getTaskUserId(), opEmpId.getId(), evalKpi.getTaskId());
            domainDao.saveBatch(audit2DataBd.builderItemFinishValueAudit());

            itemScoreRuleDataBd.buildRefOkr(adds);
            domainDao.saveBatch(itemScoreRuleDataBd.getRefOkrDos());

        }

        if (CollUtil.isNotEmpty(dels)) {
            String taskUserId = dels.get(0).getTaskUserId();
            List<String> recordIds = new ListWrap<>(dels).distinctTick(EvalRefOkr::getTaskKpiId);
            UpdateBuilder delKpi = UpdateBuilder.build("perf_evaluate_task_kpi").set("is_deleted", "true").set("updated_user", opEmpId.getId()).whereEqReq("company_id", companyId).whereEqReq("task_user_id", taskUserId).whereInReq("kpi_item_id", recordIds).whereEq("is_deleted", "false");
            domainDao.update(delKpi);

            UpdateBuilder delAudit = UpdateBuilder.build("perf_evaluate_task_audit").set("is_deleted", "true").set("updated_user", opEmpId.getId()).whereEqReq("company_id", companyId).whereEqReq("task_user_id", taskUserId).whereIn("kpi_item_id", recordIds).whereEq("is_deleted", "false");
            domainDao.update(delAudit);

            UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_item_score_rule").set("is_deleted", "true").set("updated_user", opEmpId.getId()).whereEq("is_deleted", "false").whereEqReq("company_id", companyId).whereEqReq("task_user_id", taskUserId).whereIn("kpi_item_id", recordIds);
            domainDao.update(updateBuilder);

        }

        if (CollUtil.isNotEmpty(edits)) {
            List<EvalKpi> kpis = new ArrayList<>();
            for (EvalRefOkr edit : edits) {
                EmpEvalKpiType type = okrTypes.mapGet(edit.getKpiTypeId());
                //EvalKpi kpiItem = Convert.convertOnlyMatch(type, EvalKpi.class);
                Map<String, EvalKpi> map = type.getItems().stream().collect(Collectors.toMap(EvalKpi::getKpiItemId, Function.identity()));
                EvalKpi kpiItem = map.get(edit.getTaskKpiId());
                if (Objects.isNull(kpiItem)) {
                    continue;
                }
                kpiItem.builder(edit, type, opEmpId.getId());//from kpiBiz.231
                kpis.add(kpiItem);
                domainDao.update(PerfEvaluateTaskKpiDo.class, kpiItem);
                domainDao.update(EvalRefOkrDo.class, edit);
            }
            if (!kpis.isEmpty()) {
                List<String> recordIds = new ListWrap<>(edits).distinctTick(EvalRefOkr::getTaskKpiId);
                UpdateBuilder delAudit = UpdateBuilder.build("perf_evaluate_task_audit").set("is_deleted", "true").set("updated_user", opEmpId.getId()).whereEqReq("company_id", companyId).whereEqReq("task_user_id", edits.get(0).getTaskUserId()).whereIn("kpi_item_id", recordIds).whereEq("is_deleted", "false");
                domainDao.update(delAudit);

                Supplier<String> auditIdGen = () -> domainDao.nextLongAsStr(TaskUserRepoImpl.auditSeq);
                EvalRefOkr okr = edits.get(0);
                EvalScoreRule2DataBd bd = new EvalScoreRule2DataBd(auditIdGen, companyId, okr.getTaskUserId(), opEmpId.getId(), okr.getTaskId(), isOpenAvgWeightCompute);
                List<TaskAuditDo> auditDos = bd.buildCustomAuditDos(kpis, okr.getEmpId());
                domainDao.saveBatch(auditDos);
            }
        }

        if (CollUtil.isNotEmpty(okrTypes.getMergedAudits())) {
            for (EvalAudit mergedAudit : okrTypes.getMergedAudits()) {
                mergedAudit.setId(domainDao.nextLongAsStr(TaskUserRepoImpl.auditSeq));
            }
            domainDao.addBatch(TaskAuditDo.class, okrTypes.getMergedAudits());
        }

    }

    public void saveNewOkr(EmpId opEmpId, KpiListWrap okrTypes, EmpEvalRule empEvalRule, boolean isOpenAvgWeightCompute) {
        EmpEvalKpiType okrType = okrTypes.getDatas().get(0);
        if (CollUtil.isEmpty(okrType.getItems())) {
            return;
        }
        List<EvalRefOkr> adds = okrType.getItems().stream().map(EvalKpi::getRefOkr).collect(Collectors.toList());
        String companyId = okrType.getCompanyId().getId();
        List<EvalKpi> items = okrType.getItems();
        List<PerfEvaluateTaskKpiDo> kpiDos = new ArrayList<>();
        List<PerfEvaluateTaskItemScoreRuleDo> custRuleDos = new ArrayList<>();
        for (EvalKpi evalKpi : items) {
            PerfEvaluateTaskKpiDo data = new ToDataBuilder<>(evalKpi, PerfEvaluateTaskKpiDo.class).data();
            data.setFinishValueAudit(CollectionUtil.isNotEmpty(okrType.getFinishValueAudit()) ? okrType.getFinishValueAudit() : okrType.finishValueOfType());
            kpiDos.add(data);
            if (evalKpi.itemScoreRule() != null) {
                EvalItemScoreRule itemScoreRule = evalKpi.getItemScoreRule();
                itemScoreRule.setId(domainDao.nextLongAsStr(TaskUserRepoImpl.evalCommonSeq));
                PerfEvaluateTaskItemScoreRuleDo ruleDo = new ToDataBuilder<>(itemScoreRule, PerfEvaluateTaskItemScoreRuleDo.class).data();
                custRuleDos.add(ruleDo);
            }
        }
        domainDao.saveBatch(kpiDos);
        domainDao.saveBatch(custRuleDos);
        Supplier<String> auditIdGen = () -> domainDao.nextLongAsStr(TaskUserRepoImpl.auditSeq);
        EvalKpi evalKpi = items.get(0);
        EvalScoreRule2DataBd bd = new EvalScoreRule2DataBd(auditIdGen, companyId, evalKpi.getTaskUserId(), opEmpId.getId(), evalKpi.getTaskId(), isOpenAvgWeightCompute);
        List<TaskAuditDo> auditDos = bd.buildCustomAuditDos(items, evalKpi.getEmpId());
        domainDao.saveBatch(auditDos);
        ItemFinishedValueAudit2DataBd audit2DataBd = new ItemFinishedValueAudit2DataBd(auditIdGen, empEvalRule, kpiDos, companyId, evalKpi.getTaskUserId(), opEmpId.getId(), evalKpi.getTaskId());
        domainDao.saveBatch(audit2DataBd.builderItemFinishValueAudit());
        List<EvalRefOkrDo> refOkrDos = adds.stream().map(refOkr -> {
            refOkr.setId(UUID.randomUUID().toString());
            refOkr.setIsDeleted("false");
            EvalRefOkrDo data = new EvalRefOkrDo();
            new DomainToDataBuilder<>(refOkr, data).build();
            return data;
        }).collect(Collectors.toList());
        domainDao.saveBatch(refOkrDos);
        for (EvalAudit mergedAudit : okrTypes.getMergedAudits()) {
            mergedAudit.setId(domainDao.nextLongAsStr(TaskUserRepoImpl.auditSeq));
        }
        domainDao.addBatch(TaskAuditDo.class, okrTypes.getMergedAudits());
        //保存goal到表中
        List<OkrGoal> goals = okrType.getOkrGoals().stream().peek(okrGoal -> {
            okrGoal.setId(domainDao.nextLongAsStr(perfEvaluateOkrGoalSeq));
            okrGoal.setTaskUserId(okrType.getTaskUserId());
        }).collect(Collectors.toList());
        domainDao.addBatch(OkrGoalDo.class, goals);
    }


    @Override
    public void updateNewOkrKpi(EmpId opEmpId, KpiListWrap okrTypes, List<EvalKpi> addKpis, List<EvalRefOkr> adds,
                                List<EvalRefOkr> dels, List<EvalRefOkr> edits, EmpEvalRule empEvalRule, boolean isOpenAvgWeightCompute) {

        EmpEvalKpiType type1 = okrTypes.getDatas().get(0);
        String companyId = type1.getCompanyId().getId();
        Map<String, EmpEvalKpiType> okrTypeMap = okrTypes.getDatas().stream().collect(Collectors.toMap(EmpEvalKpiType::getKpiTypeId, Function.identity()));
        delEmpEvalKpi(companyId, type1.getTaskUserId(), okrTypes, type1.getKpiTypeId());

        if (CollUtil.isNotEmpty(addKpis)) {
            Supplier<String> evalCommonIdGen = () -> domainDao.nextLongAsStr(TaskUserRepoImpl.evalCommonSeq);
            EvalItemScoreRuleDataBd itemScoreRuleDataBd = new EvalItemScoreRuleDataBd(evalCommonIdGen);
            itemScoreRuleDataBd.buildCustomKpiAndRuleDos(addKpis, okrTypeMap);
            domainDao.saveBatch(itemScoreRuleDataBd.getKpiDos());
            domainDao.saveBatch(itemScoreRuleDataBd.getCustRuleDos());

            Supplier<String> auditIdGen = () -> domainDao.nextLongAsStr(TaskUserRepoImpl.auditSeq);
            EvalKpi evalKpi = addKpis.get(0);
            EvalScoreRule2DataBd bd = new EvalScoreRule2DataBd(auditIdGen, companyId, evalKpi.getTaskUserId(), opEmpId.getId(), evalKpi.getTaskId(), isOpenAvgWeightCompute);
            List<TaskAuditDo> auditDos = bd.buildCustomAuditDos(addKpis, evalKpi.getEmpId());
            domainDao.saveBatch(auditDos);

            ItemFinishedValueAudit2DataBd audit2DataBd = new ItemFinishedValueAudit2DataBd(auditIdGen, empEvalRule, itemScoreRuleDataBd.getKpiDos(), companyId, evalKpi.getTaskUserId(), opEmpId.getId(), evalKpi.getTaskId());
            domainDao.saveBatch(audit2DataBd.builderItemFinishValueAudit());

            itemScoreRuleDataBd.buildRefOkr(adds);
            domainDao.saveBatch(itemScoreRuleDataBd.getRefOkrDos());

        }

        if (CollUtil.isNotEmpty(dels)) {
            String taskUserId = dels.get(0).getTaskUserId();
            List<String> recordIds = new ListWrap<>(dels).distinctTick(EvalRefOkr::getTaskKpiId);
            UpdateBuilder delKpi = UpdateBuilder.build("perf_evaluate_task_kpi").set("is_deleted", "true").set("updated_user", opEmpId.getId()).whereEq("company_id", companyId).whereEqReq("task_user_id", taskUserId).whereInReq("kpi_item_id", recordIds).whereEq("is_deleted", "false");
            domainDao.update(delKpi);

            UpdateBuilder delAudit = UpdateBuilder.build("perf_evaluate_task_audit").set("is_deleted", "true").set("updated_user", opEmpId.getId()).whereEq("company_id", companyId).whereEqReq("task_user_id", taskUserId).whereIn("kpi_item_id", recordIds).whereEq("is_deleted", "false");
            domainDao.update(delAudit);

            UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_item_score_rule").set("is_deleted", "true").set("updated_user", opEmpId.getId()).whereEq("is_deleted", "false").whereEq("company_id", companyId).whereEqReq("task_user_id", taskUserId).whereIn("kpi_item_id", recordIds);
            domainDao.update(updateBuilder);

        }

        if (CollUtil.isNotEmpty(edits)) {
            for (EvalRefOkr edit : edits) {
                EmpEvalKpiType type = okrTypes.mapGet(edit.getKpiTypeId());
                //EvalKpi kpiItem = Convert.convertOnlyMatch(type, EvalKpi.class);
                Map<String, EvalKpi> map = type.getItems().stream().collect(Collectors.toMap(EvalKpi::getKpiItemId, Function.identity()));
                EvalKpi kpiItem = map.get(edit.getTaskKpiId());
                kpiItem.builder(edit, type, opEmpId.getId());//from kpiBiz.231
                domainDao.update(PerfEvaluateTaskKpiDo.class, kpiItem);
                domainDao.update(EvalRefOkrDo.class, edit);
            }
        }
    }

    public void delEmpEvalKpi(String companyId, String evalUserId, KpiListWrap okrTypes, String kpiTypeId) {
        List<String> itemIds = new ArrayList<>();
        List<String> typeIds = new ArrayList<>();
        if (okrTypes.getDatas() != null && okrTypes.getDatas().size() > 0) {
            typeIds = okrTypes.getDatas().stream().map(obj -> obj.getKpiTypeId()).collect(Collectors.toList());
            okrTypes.getDatas().forEach(obj -> {
                if (obj.getItems() != null && obj.getItems().size() > 0) {
                    itemIds.addAll(obj.getItems().stream().map(item -> item.getKpiItemId()).collect(Collectors.toList()));
                }
            });
        }

        UpdateBuilder type = UpdateBuilder.build(EmpEvalKpiType.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", evalUserId)
                .whereIn("kpi_type_id", typeIds);


        UpdateBuilder okrType = UpdateBuilder.build(PerfEvaluateTaskOkrTypeDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", evalUserId)
                .whereIn("kpi_type_id", typeIds);

        UpdateBuilder item = UpdateBuilder.build(PerfEvaluateTaskKpiDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", evalUserId)
                .whereIn("kpi_item_id", itemIds);

        UpdateBuilder filed = UpdateBuilder.build(PerfEvaluateTaskFileDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", evalUserId)
                .whereIn("kpi_item_id", itemIds);


        UpdateBuilder refOkrDo = UpdateBuilder.build(EvalRefOkrDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", evalUserId)
                .whereIn("task_kpi_id", itemIds);

        UpdateBuilder taskAudit = UpdateBuilder.build(TaskAuditDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", evalUserId)
                .whereInReq("kpi_type_id", typeIds);

        domainDao.update(type);
        domainDao.update(okrType);
        domainDao.update(item);
        domainDao.update(filed);
        domainDao.update(refOkrDo);
        domainDao.update(taskAudit);
    }

    public int batchUpdateItemTarget(String companyId, String opEmpId, String taskUserId, String kpiItemId, String kpiItemName, BigDecimal targetValue) {
        UpdateBuilder update = UpdateBuilder.build(PerfEvaluateTaskKpiDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("kpi_item_id", kpiItemId)
                .whereEq("kpi_item_name", kpiItemName) //批量导入时根据指标名称更新
                .whereEq("is_deleted", "false").set("updated_user", opEmpId).set("item_target_value", targetValue);
        return domainDao.update(update);
    }

    public void batchSaveInputBakIf(List<EvalKpiInputBak> inputBaks) {
        for (EvalKpiInputBak inputBak : inputBaks) {
            inputBak.setId(domainDao.nextIntId(inputBakSeq));
        }
        EvalKpiInputBak any = inputBaks.get(0);
        ComQB comQB = ComQB.build(EvalKpiInputBakDo.class).whereEqReq("company_id", any.getCompanyId().getId()).whereEqReq("task_user_id", any.getTaskUserId()).whereEq("is_deleted", Boolean.FALSE.toString());
        List<EvalKpiInputBakDo> exitBaks = domainDao.listAll(comQB);

        ListWrap<EvalKpiInputBakDo> kpiWrap = new ListWrap<>(exitBaks).groupBy(EvalKpiInputBakDo::getKpiItemId);
        List<EvalKpiInputBak> baks = inputBaks.stream().filter(b -> CollUtil.isEmpty(kpiWrap.groupGet(b.getKpiItemId()))).collect(Collectors.toList());
        if (CollUtil.isEmpty(baks)) {
            return;
        }
        domainDao.addBatch(EvalKpiInputBakDo.class, baks);
    }

    public ListWrap<EvalKpiInputBak> listWrapInputBak(TenantId tenantId, String taskUserId) {
        ComQB comQB = ComQB.build(EvalKpiInputBakDo.class).whereEqReq("company_id", tenantId.getId()).whereEqReq("task_user_id", taskUserId).whereEq("is_deleted", Boolean.FALSE.toString());
        List<EvalKpiInputBak> inputBaks = domainDao.listAllDomain(comQB, EvalKpiInputBak.class);
        return new ListWrap<>(inputBaks).groupBy(EvalKpiInputBak::getKpiItemId);
    }

    @Override
    public void reSetInput(TenantId companyId, List<EvalKpi> kpis) {
        if (CollUtil.isEmpty(kpis)) {
            return;
        }
        for (EvalKpi kpi : kpis) {
            UpdateBuilder update = UpdateBuilder.build(PerfEvaluateTaskKpiDo.class).set("result_input_emp_id", kpi.getResultInputEmpId()).set("result_input_type", kpi.getResultInputType()).whereEqReq("company_id", companyId.getId()).whereEqReq("id", kpi.getId()).whereEq("is_deleted", Boolean.FALSE.toString());
            domainDao.update(update);
        }
    }

    @Override
    public void addIndExtScoreItem(String beforScore, String scoreValue, String scoreComment, Date createTime, String opEmpId, EvalKpi evalKpi) {
        //记录日志
        OperationLogDo log = new OperationLogDo(evalKpi.getCompanyId().getId(), evalKpi.getTaskUserId(), OperationLogSceneEnum.add_ind_ext_score_item.getScene(), opEmpId, beforScore, scoreValue, scoreComment);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("kpiItemName", evalKpi.getKpiItemName());
        jsonObject.put("comment", scoreComment);
        log.setDescription(jsonObject.toJSONString());
        log.setFieldName(evalKpi.getKpiItemName());
        log.setCreatedTime(createTime);
        log.setUpdatedTime(new Date());
        log.setKpiItemId(evalKpi.getKpiItemId());
        log.setCreatedUser(opEmpId);
        domainDao.save(log);
    }

    @Override
    public void updateOkrRefFlag(String companyId, String createdUser, List<String> idList, String okrRefFlag) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_kpi").set("okr_ref_flag", okrRefFlag);
        if (StringUtils.isNotEmpty(createdUser)) {
            updateBuilder.set("updated_user", createdUser);
        }

        updateBuilder.whereEq("company_id", companyId).whereIn("id", idList);
        domainDao.update(updateBuilder);
    }

    @Override
    public void updateFinishValue(String companyId, String createdUser, String id, BigDecimal finishValue) {
        if (StringUtils.isEmpty(id)) {
            return;
        }
        UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_kpi").set("item_finish_value", finishValue);
        if (StringUtils.isNotEmpty(createdUser)) {
            updateBuilder.set("updated_user", createdUser);
        }

        updateBuilder.whereEq("company_id", companyId).whereEq("id", id);
        domainDao.update(updateBuilder);
    }

    @Override
    public void updateTargetValue(String companyId, String createdUser, String id, BigDecimal targetValue) {
        if (StringUtils.isEmpty(id)) {
            return;
        }
        UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_kpi").set("item_target_value", targetValue);
        if (StringUtils.isNotEmpty(createdUser)) {
            updateBuilder.set("updated_user", createdUser);
        }

        updateBuilder.whereEq("company_id", companyId).whereEq("id", id);
        domainDao.update(updateBuilder);
    }

    @Override
    public void saveInputFinishValCacheInfo(TenantId tenantId, String taskUserId, EmpId opEmpId, List<FinishValue> finishValues) {
        List<InputFinishValCacheDo> cacheDos = new ArrayList<>();
        for (FinishValue finishValue : finishValues) {
            InputFinishValCacheDo cacheDo = new ToDataBuilder<>(finishValue, InputFinishValCacheDo.class).data();
            cacheDo.accept(inputFinishValCacheIdGen.get(), tenantId.getId(), opEmpId.getId(), taskUserId);
            cacheDos.add(cacheDo);
        }
        if (CollUtil.isNotEmpty(cacheDos)) {
            domainDao.saveBatch(cacheDos);
        }
    }

    @Override
    public void bacthAddImportFinishValCache(TenantId tenantId, EmpId opEmpId, List<ImportFinishValCache> finishValues) {
        //先删除
        UpdateBuilder update = UpdateBuilder.build(ImportFinishValCacheDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .set("updated_user", opEmpId.getId())
                .set("updated_time", new Date())
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("operate_emp_id", opEmpId.getId())
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        domainDao.update(update);
        List<ImportFinishValCacheDo> cacheDos = new ArrayList<>();
        for (ImportFinishValCache finishValue : finishValues) {
            ImportFinishValCacheDo cacheDo = new ToDataBuilder<>(finishValue, ImportFinishValCacheDo.class).data();
            cacheDo.setId(importFinishValCacheIdGen.get());
            cacheDos.add(cacheDo);
        }
        if (CollUtil.isNotEmpty(cacheDos)) {
            domainDao.saveBatch(cacheDos);
        }
    }

    @Override
    public void updateImportFinishValCache(TenantId tenantId, EmpId opEmpId, List<ImportFinishValCache> finishValues) {
        for (ImportFinishValCache finishValue : finishValues) {
            finishValue.setTaskKpiId(finishValue.getId());
        }
        BatchUpdateBuilder builder = BatchUpdateBuilder.buildTable("import_finish_val_cache")
                .addSetCaseProp("itemFinishValue", "taskKpiId:=")
                .addSetCaseProp("workItemFinishValue", "taskKpiId:=")
                .addSetCaseProp("itemFinishValueText", "taskKpiId:=")
                .addSetCaseProp("finishValueComment", "taskKpiId:=")
                .addSetCaseProp("files", "taskKpiId:=")
                .whereEq("company_id", tenantId.getId())
                .whereEq("is_deleted", "false")
                .whereEq("operate_emp_id",opEmpId.getId())
                .whereUseIn("taskKpiId")
                .addAllBean(finishValues);
        domainDao.updateBatch(builder);
    }

    @Override
    public void deletedCacheInfo(TenantId tenantId, String taskUserId, EmpId opEmpId) {
        deletedCacheInfo(tenantId, taskUserId, null, opEmpId);
    }

    @Override
    public void updatePlanItemFinishValue(List<String> taskKpiIds, String createdUser, BigDecimal finishValue) {
        if (CollUtil.isEmpty(taskKpiIds)) {
            return;
        }
        UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_kpi").set("item_finish_value", finishValue);
        if (StringUtils.isNotEmpty(createdUser)) {
            updateBuilder.set("updated_user", createdUser);
        }
        updateBuilder.whereIn("id", taskKpiIds);
        domainDao.update(updateBuilder);
    }

    @Override
    public void updatePlanItemTargetValue(List<String> taskKpiIds, String createdUser, BigDecimal targetValue) {
        if (CollUtil.isEmpty(taskKpiIds)) {
            return;
        }
        UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_kpi").set("item_target_value", targetValue);
        if (StringUtils.isNotEmpty(createdUser)) {
            updateBuilder.set("updated_user", createdUser);
        }
        updateBuilder.whereIn("id", taskKpiIds);
        domainDao.update(updateBuilder);
    }

    @Override
    public void changedAskTypeScore(List<EmpEvalKpiType> kpiTypes, String companyId, String opEmpId) {
        if (CollUtil.isEmpty(kpiTypes)) {
            return;
        }
        for (EmpEvalKpiType kpiType : kpiTypes) {
            kpiType.accup(companyId, opEmpId);
            UpdateBuilder upType = UpdateBuilder.build("emp_eval_kpi_type")
                    .set("ask360_eval_score", kpiType.getAsk360EvalScore())
                    .set("updated_user", opEmpId)
                    .set("updated_time", new Date())
                    .whereEqReq("company_id", companyId)
                    .whereEqReq("task_user_id", kpiType.getTaskUserId())
                    .whereEqReq("kpi_type_id", kpiType.getKpiTypeId())
                    .whereEqReq("is_deleted", Boolean.FALSE.toString());
            domainDao.update(upType);

        }
    }

    @Override
    public void changedAskTypeScoreV3(String taskUserId,List<EmpEvalKpiType> askKpiTypes, String companyId, String opEmpId) {
        if (CollUtil.isEmpty(askKpiTypes)) {
            return;
        }
        BatchUpdateBuilder upType = BatchUpdateBuilder.buildTable("emp_eval_kpi_type")
                .addSetCaseProp("ask360EvalScore", "kpiTypeId:=")
                .addSetCaseProp("updatedUser", "kpiTypeId:=")
                .addSetCaseProp("updatedTime", "kpiTypeId:=")
                .whereEq("companyId", companyId)
                .whereEq("taskUserId", taskUserId)
                .whereEq("isDeleted", "false")
                .whereUseIn("kpiTypeId");

        //维度upType
        askKpiTypes.forEach(kpiType -> {
            kpiType.accup(companyId, opEmpId);
            EmpEvalKpiTypeDo data = new ToDataBuilder<>(kpiType, EmpEvalKpiTypeDo.class).data();
            data.setUpdatedTime(new Date());
            upType.addBean(data);
        });
        domainDao.updateBatch(upType);
    }

    @Override
    public void changedAskTypeEvalId(List<EmpEvalKpiType> kpiTypes, String companyId, String opEmpId) {
        if (CollUtil.isEmpty(kpiTypes)) {
            return;
        }
        for (EmpEvalKpiType kpiType : kpiTypes) {
            kpiType.accup(companyId, opEmpId);
            UpdateBuilder upType = UpdateBuilder.build("emp_eval_kpi_type")
                    .set("ask360_eval_id", kpiType.getAsk360EvalId())
                    .set("updated_user", opEmpId)
                    .set("updated_time", new Date())
                    .whereEqReq("company_id", companyId)
                    .whereEqReq("task_user_id", kpiType.getTaskUserId())
                    .whereEqReq("kpi_type_id", kpiType.getKpiTypeId())
                    .whereEqReq("is_deleted", Boolean.FALSE.toString());
            domainDao.update(upType);
        }
    }

    @Override
    public void updateEmpEvalKpiType(List<EmpEvalKpiType> kpiTypes, String companyId, String opEmpId) {
        if (CollUtil.isEmpty(kpiTypes)) {
            return;
        }
        for (EmpEvalKpiType kpiType : kpiTypes) {
            EmpEvalKpiTypeDo typeDo = new EmpEvalKpiTypeDo();
            BeanUtil.copyProperties(kpiType, typeDo);
            UpdateBuilder up = UpdateBuilder.build("emp_eval_kpi_type").setBean(typeDo)
                    .whereEqReq("companyId", kpiType.getCompanyId().getId())
                    .whereEqReq("task_user_id", typeDo.getTaskUserId())
                    .whereEqReq("kpi_type_id", typeDo.getKpiTypeId())
                    .whereEqReq("is_deleted", Boolean.FALSE.toString());
            domainDao.update(up);
        }
    }

    @Override
    public void updateEmpEvalKpiTypeScore(List<EmpEvalKpiType> kpiTypes, String companyId, String opEmpId) {
        if (CollUtil.isEmpty(kpiTypes)) {
            return;
        }
        for (EmpEvalKpiType kpiType : kpiTypes) {
            kpiType.accup(companyId, opEmpId);
            UpdateBuilder upType = UpdateBuilder.build(EmpEvalKpiTypeDo.class)
                    .set("type_final_score", kpiType.getTypeFinalScore())
                    .set("type_final_original_score", kpiType.getTypeFinalOriginalScore())
                    .set("type_score", kpiType.getTypeScore())
                    .set("type_original_score", kpiType.getTypeOriginalScore())
                    .set("type_final_self_score", kpiType.getTypeFinalSelfScore())
                    .set("type_final_peer_score", kpiType.getTypeFinalPeerScore())
                    .set("type_final_sub_score", kpiType.getTypeFinalSubScore())
                    .set("type_final_superior_score", kpiType.getTypeFinalSuperiorScore())
                    .set("type_final_item_score", kpiType.getTypeFinalItemScore())
                    .set("type_item_score", kpiType.getTypeItemScore())
                    .set("type_final_appoint_score", kpiType.getTypeFinalAppointScore())
                    .set("type_self_score", kpiType.getTypeSelfScore())
                    .set("type_peer_score", kpiType.getTypePeerScore())
                    .set("type_sub_score", kpiType.getTypeSubScore())
                    .set("type_superior_score", kpiType.getTypeSuperiorScore())
                    .set("type_appoint_score", kpiType.getTypeAppointScore())
                    .set("updated_user", opEmpId)
                    .set("updated_time", new Date())
                    .whereEqReq("company_id", companyId)
                    .whereEqReq("task_user_id", kpiType.getTaskUserId())
                    .whereEqReq("kpi_type_id", kpiType.getKpiTypeId())
                    .whereEqReq("is_deleted", Boolean.FALSE.toString());
            domainDao.update(upType);

            if (CollUtil.isNotEmpty(kpiType.getItems())) {
                updateEmpEvalKpiItemScore(kpiType.getItems(), companyId, opEmpId);
            }
        }
    }

    private void updateEmpEvalKpiItemScore(List<EvalKpi> evalKpis, String companyId, String opEmpId) {
        if (CollUtil.isEmpty(evalKpis)) {
            return;
        }
        for (EvalKpi kpi : evalKpis) {
            kpi.accup(companyId,opEmpId);
            if (Objects.isNull(kpi.getV3FinalWeightItemScore())){
                continue;
            }
            UpdateBuilder upKpi = UpdateBuilder.build(PerfEvaluateTaskKpiDo.class)
                    .set("item_final_original_score", kpi.getItemFinalScore())
                    .set("item_final_score", kpi.getItemFinalScore())
                    .set("item_original_score", kpi.getItemScore())
                    .set("item_score", kpi.getItemScore())
                    .set("item_final_self_score", kpi.getItemFinalSelfScore())
                    .set("item_final_peer_score", kpi.getItemFinalPeerScore())
                    .set("item_final_sub_score", kpi.getItemFinalSubScore())
                    .set("item_final_superior_score", kpi.getItemFinalSuperiorScore())
                    .set("item_item_score", kpi.getItemItemScore())
                    .set("item_final_item_score", kpi.getItemFinalItemScore())
                    .set("item_final_appoint_score", kpi.getItemFinalAppointScore())
                    .set("item_self_score", kpi.getItemSelfScore())
                    .set("item_peer_score", kpi.getItemPeerScore())
                    .set("item_sub_score", kpi.getItemSubScore())
                    .set("item_superior_score", kpi.getItemSuperiorScore())
                    .set("item_appoint_score", kpi.getItemAppointScore())
                    .set("updated_user", opEmpId)
                    .set("updated_time", new Date())
                    .whereEqReq("company_id", companyId)
                    .whereEqReq("task_user_id", kpi.getTaskUserId())
                    .whereEqReq("kpi_type_id", kpi.getKpiTypeId())
                    .whereEqReq("kpi_item_id", kpi.getKpiItemId())
                    .whereEqReq("id", kpi.getId())
                    .whereEqReq("is_deleted", Boolean.FALSE.toString());
            domainDao.update(upKpi);
        }
    }

    @Override
    public void updateFinishValueAuditStatus(RejectFinishedValueDmSvc dmSvc) {
        UpdateBuilder builder = UpdateBuilder.build(PerfEvaluateTaskKpiDo.class)
                .set("finish_value_audit_status", FinishValueAuditStatusEnum.REJECT.getType())
                .set("finish_value_audit_reason", dmSvc.getAuditReason())
                .set("final_submit_finish_value", 0)
                .whereIn("id", dmSvc.getAuditItemIds());
        domainDao.update(builder);

        // 保存操作日志
        OperationLogDo operationLogDo = new OperationLogDo(dmSvc.getTenantId().getId(), dmSvc.getTaskUser().getId(),
                "finish_value_audit_reject", FinishValueAuditStatusEnum.REJECT.getType().toString(), dmSvc.getOpEmpId().getId(),
                dmSvc.getKpiItemIds(), dmSvc.getKpiItemNames(), dmSvc.getAuditReason());
        domainDao.save(operationLogDo);
    }

    @Override
    public void extDataLock(List<EvalKpi> updateKpis, String companyId, String taskId) {
        BatchUpdateBuilder up = BatchUpdateBuilder.buildTable("perf_evaluate_task_kpi")
                .addSetCaseProp("itemFinishValue", "id:=")
                .addSetCaseProp("itemFinishValueText", "id:=")
                .addAllBean(updateKpis)
                .whereEq("company_id", companyId)
                .whereEq("task_id", taskId)
                .whereUseIn("id");
        domainDao.updateBatch(up);
    }

    public void deletedCacheInfo(TenantId tenantId, String taskUserId, List<String> kpiItemIds, EmpId opEmpId) {
        UpdateBuilder updateBuilder = UpdateBuilder.build("input_finish_val_cache")
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("operate_emp_id", opEmpId.getId())
                .whereIn("kpi_item_id", kpiItemIds);
        domainDao.update(updateBuilder);
    }

    public void batchSaveFinishValueCache(TenantId tenantId, EmpId opEmpId, Set<String> taskUserIds, List<InputFinishValCache> caches) {
        UpdateBuilder updateBuilder = UpdateBuilder.build("input_finish_val_cache")
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEq("company_id", tenantId.getId())
                .whereIn("task_user_id", taskUserIds)
                .whereEq("operate_emp_id", opEmpId.getId());
        domainDao.update(updateBuilder);

        //先删除
        UpdateBuilder update = UpdateBuilder.build(ImportFinishValCacheDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .set("updated_user", opEmpId.getId())
                .set("updated_time", new Date())
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("operate_emp_id", opEmpId.getId())
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        domainDao.update(update);

        List<InputFinishValCacheDo> cacheDos = new ArrayList<>();
        for (InputFinishValCache finishValue : caches) {
            InputFinishValCacheDo cacheDo = new ToDataBuilder<>(finishValue, InputFinishValCacheDo.class).data();
            cacheDo.accept(inputFinishValCacheIdGen.get(), tenantId.getId(), opEmpId.getId(), finishValue.getTaskUserId());
            cacheDos.add(cacheDo);
        }
        domainDao.saveBatch(cacheDos);
    }

    @Override
    public int batchUpdateTargetValue(String companyId,  List<String> taskUserIds, List<EvalKpi> targetValueList) {
        if (CollUtil.isEmpty(targetValueList) || CollUtil.isEmpty(taskUserIds)){
            return 0;
        }

        BatchUpdateBuilder batchUpdate = BatchUpdateBuilder.buildTable("perf_evaluate_task_kpi")
                .addSetCaseProp("updatedTime", "kpiItemId:=","taskUserId:=")
                .addSetCaseProp("updatedUser", "kpiItemId:=","taskUserId:=")
                .addSetCaseProp("itemTargetValue", "kpiItemId:=","taskUserId:=")
                .addAllBean(targetValueList)
                .whereEq("companyId", companyId)
                .whereEq("isDeleted","false")
                .whereIn("taskUserId", taskUserIds);

        return domainDao.updateBatch(batchUpdate);
    }

    @Override
    public int batchUpdateCustomFieldValue(String companyId, List<String> taskUserIds, List<KpiItemUsedField> customFieldList) {
        if (CollUtil.isEmpty(customFieldList) || CollUtil.isEmpty(taskUserIds)){
            return 0;
        }

        BatchUpdateBuilder batchUpdate = BatchUpdateBuilder.buildTable("perf_evaluate_item_used_field")
                .addSetCaseProp("updatedTime", "kpiItemId:=","taskUserId:=","fieldId:=")
                .addSetCaseProp("updatedUser", "kpiItemId:=","taskUserId:=","fieldId:=")
                .addSetCaseProp("value", "kpiItemId:=","taskUserId:=","fieldId:=")
                .addAllBean(customFieldList)
                .whereEq("companyId", companyId)
                .whereEq("isDeleted","false")
                .whereIn("taskUserId", taskUserIds);;
        return domainDao.updateBatch(batchUpdate);
    }

}
