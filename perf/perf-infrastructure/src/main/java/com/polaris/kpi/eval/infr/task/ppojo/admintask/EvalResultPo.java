package com.polaris.kpi.eval.infr.task.ppojo.admintask;

import cn.com.polaris.kpi.KpiOrgSupNames;
import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.com.seendio.polaris.excel.type.ClsColumn;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.perf.www.vo.common.OperationLogVO;
import com.polaris.kpi.eval.domain.task.entity.EvalScorer;
import com.polaris.kpi.eval.domain.task.entity.MutualScoreResultCompute;
import com.polaris.kpi.eval.domain.task.entity.empeval.AuditResultEvalType;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskScoreResultDo;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.RefEvalPo;
import com.polaris.sdk.type.ListWrap;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Getter
@Setter
@NoArgsConstructor
public class EvalResultPo extends EvalResultExcelPo {
    private String evalGroupId; // 考核组id
    private String evalGroupName; // 考核组名称
    private Integer actionNum;
    private String adjustReason;
    private String allScored;
    private String appealDeadLine;
    private String appealReceiverId;
    private BigDecimal appointScoreNoWeight;
    private String avatar;
    private String ccEmpIds;
    private String companyId;
    private String createUserName;
    private Date createdTime;
    private String createdUser;
    private String cycleEndDate;
    private String cycleStartDate;
    private String cycleType;
    private String distributionFlag;
    private String empId;
    private String empKey;
    //@ExcelProperty("被考核人")
    //private String name;
    //@ExcelProperty("所属部门")
    //private String empOrgName;
    private String empStatus;
    private String enterScoreFlag;
    private String evaluateType;
    //@ExcelProperty("绩效等级")
    //private String evaluationLevel;


    private BigDecimal finalItemPlusScore;

    private BigDecimal finalItemSubtractScore;

    private BigDecimal finalPlusScore;
    //@ExcelProperty("核定总分")
    //private BigDecimal finalScore;
    private BigDecimal finalSubtractScore;

    private BigDecimal finalSelfPlusScore;
    //@ExcelProperty("自评分")
    //private BigDecimal finalSelfScore;
    private BigDecimal finalSelfSubtractScore;

    private BigDecimal finalPeerPlusScore;
    //@ExcelProperty("同级评分")
    //private BigDecimal finalPeerScore;
    private BigDecimal finalPeerSubtractScore;

    private BigDecimal finalSubPlusScore;
    //@ExcelProperty("下级评分")
    //private BigDecimal finalSubScore;
    private BigDecimal finalSubSubtractScore;

    private BigDecimal finalSuperiorPlusScore;
    //@ExcelProperty("上级评分")
    //private BigDecimal finalSuperiorScore;
    private BigDecimal finalSuperiorSubtractScore;
    //@ExcelProperty("指定评分")
    //private BigDecimal finalAppointScore;

    private String hasAppeal;
    //@MegGroupProperty
    //@ExcelIgnore
    //private String id;
    private Date inResultAffirmTime;
    private boolean leaved;
    private String isDeleted;
    private Integer isNewEmp;
    private String isPublish;
    private String itemChangeUser;
    private Integer itemNum;
    private String itemScoreFlag;
    private String lastScoreComment;
    private OperationLogVO log;
    private String manualScoreFlag;
    private Integer measurableNum;
    private String orgId;
    private String orgName;
    private String originalEvaluationLevel;
    //@ExcelProperty("评分合计")
    //private BigDecimal originalFinalScore;
    private BigDecimal peerScoreNoWeight;
    private Integer publicDimension;
    private String publicFlag;
    private String publicToEmp;
    private String publicType;
    private String resultAuditFlag;
    private String reviewersEmp;
    private String reviewersJson;
    private String roleName;
    private String scoreRanges;
    private String scorerType;
    private String selfScoreFlag;
    private BigDecimal selfScoreNoWeight;
    private String signaturePic;
    private String stepId;
    private BigDecimal subScoreNoWeight;
    private String superiorScoreFlag;
    private BigDecimal superiorScoreNoWeight;
    private Date taskConfirmTime;
    private String taskDesc;
    private String taskId;
    private String taskName;
    private Date taskScoreStartTime;
    private String taskStatus;
    private String templBaseJson;
    private BigDecimal totalPointsNum;
    private Date updatedTime;
    private String updatedUser;
    private String urgingFlag;
    private String evalOrgId;
    private String evalOrgName;
    private String orgSupNames;
    private Integer showResultType;
    //@ExcelProperty("关联绩效得分")
    //private BigDecimal scoreOfRef;
    private List<RefEvalPo> refEvalScores;
    //@ExcelProperty("关联绩效")
    //private String refEvalScoresStr;
    @JSONField(serialize = false, deserialize = false)
    private Map<String, List<PerfEvaluateTaskScoreResultDo>> scoreTypeMap;
    private Integer onTheJobStatus; // 员工在职状态(实习:2 正式:3)
    private String type;
    private Date entryDate;      //入职日期
    private String atOrgNamePath;
    private Integer mutualEvalComputeType; //互评评分计算方式（0 平均权重，1-平均分）
    private Integer archiveStatus; // 归档状态


    //V3版本
    //不带环节权重的环节得分
    private BigDecimal v3SelfScore;
    private BigDecimal v3SubScore;
    private BigDecimal v3PeerScore;
    private BigDecimal v3SuperiorScore;
    private BigDecimal v3AppointScore;
    //带权重的环节得分
    private BigDecimal v3FinalItemScore;
    private BigDecimal v3FinalSelfScore;
    private BigDecimal v3FinalPeerScore;
    private BigDecimal v3FinalSubScore;
    private BigDecimal v3FinalSuperiorScore;
    private BigDecimal v3FinalAppointScore;

    public void accpetV3Score() {
        this.selfScoreNoWeight = v3SelfScore;
        this.subScoreNoWeight = v3SubScore;
        this.peerScoreNoWeight = v3PeerScore;
        this.superiorScoreNoWeight = v3SuperiorScore;
        this.appointScoreNoWeight = v3AppointScore;
        this.finalItemScore = v3FinalItemScore;
        this.finalSelfScore = v3FinalSelfScore;
        this.finalPeerScore = v3FinalPeerScore;
        this.finalSubScore = v3FinalSubScore;
        this.finalSuperiorScore = v3FinalSuperiorScore;
        this.finalAppointScore = v3FinalAppointScore;
    }

    public void setRefEvalScores(List<RefEvalPo> refEvalScores) {
        this.refEvalScores = refEvalScores;
        if (CollUtil.isEmpty(refEvalScores)) {
            return;
        }
        List<String> strs = CollUtil.map(refEvalScores, refEvalPo -> refEvalPo.asExportStr(), true);
        this.refEvalScoresStr = CollUtil.join(strs, "\n");

    }

    @ExcelProperty("绩效系数")
    private String perfCoefficient;//绩效系数
    private BigDecimal originalPerfCoefficient;//绩效系数
    private Integer createTotalLevelType; //'绩效总等级生成方式 1 = 自动, 2= 手动'

    private List<AuditResultEvalType> types;

    private String kpiTypeResult;

    public void setKpiTypeResult() {
        StringBuffer sb = new StringBuffer();
        //最后一个元素,不要换行
        for (int i = 0; i < types.size(); i++) {
            AuditResultEvalType type = types.get(i);
            sb.append(type.getKpiTypeName())
                    .append("：")
                    .append(type.getTypeLevel() == null ? "" : type.getTypeLevel());

            if (i != types.size() - 1) {
                sb.append("\n");
            }
        }
        this.kpiTypeResult = sb.toString();
    }


    public boolean isOpenAvgWeightCompute() {
        //是否开启平均权重计算
        return 0 == mutualEvalComputeType;
    }

    public void noWeightScore(Map<String, List<PerfEvaluateTaskScoreResultDo>> scoreTypeMap, EvalResultPo po) {
        this.scoreTypeMap = scoreTypeMap;
        setPlusSubtractNull();
        this.selfScoreNoWeight = getNoWeightScore(scoreTypeMap.get(EvaluateAuditSceneEnum.SELF_SCORE.getScene()), EvaluateAuditSceneEnum.SELF_SCORE.getScene());
        this.peerScoreNoWeight = getNoWeightScore(scoreTypeMap.get(EvaluateAuditSceneEnum.PEER_SCORE.getScene()), EvaluateAuditSceneEnum.PEER_SCORE.getScene());
        this.subScoreNoWeight = getNoWeightScore(scoreTypeMap.get(EvaluateAuditSceneEnum.SUB_SCORE.getScene()), EvaluateAuditSceneEnum.SUB_SCORE.getScene());
        this.superiorScoreNoWeight = getNoWeightScore(scoreTypeMap.get(EvaluateAuditSceneEnum.SUPERIOR_SCORE.getScene()), EvaluateAuditSceneEnum.SUPERIOR_SCORE.getScene());
        this.appointScoreNoWeight = getNoWeightScore(scoreTypeMap.get(EvaluateAuditSceneEnum.APPOINT_SCORE.getScene()), EvaluateAuditSceneEnum.APPOINT_SCORE.getScene());
        this.finalItemScore = getNoWeightScore(scoreTypeMap.get(EvaluateAuditSceneEnum.ITEM_SCORE.getScene()), EvaluateAuditSceneEnum.ITEM_SCORE.getScene());
        if (Objects.isNull(this.finalPeerScore)) {
            this.finalPeerScore = getWeightScore(scoreTypeMap.get(EvaluateAuditSceneEnum.PEER_SCORE.getScene()), EvaluateAuditSceneEnum.PEER_SCORE.getScene());
        }
        if (Objects.isNull(this.finalSelfScore)) {
            this.finalSelfScore = getWeightScore(scoreTypeMap.get(EvaluateAuditSceneEnum.SELF_SCORE.getScene()), EvaluateAuditSceneEnum.SELF_SCORE.getScene());
        }
        if (Objects.isNull(this.finalSubScore)) {
            this.finalSubScore = getWeightScore(scoreTypeMap.get(EvaluateAuditSceneEnum.SUB_SCORE.getScene()), EvaluateAuditSceneEnum.SUB_SCORE.getScene());
        }
        if (Objects.isNull(this.finalSuperiorScore)) {
            this.finalSuperiorScore = getWeightScore(scoreTypeMap.get(EvaluateAuditSceneEnum.SUPERIOR_SCORE.getScene()), EvaluateAuditSceneEnum.SUPERIOR_SCORE.getScene());
        }
        if (Objects.isNull(this.finalAppointScore)) {
            this.finalAppointScore = getWeightScore(scoreTypeMap.get(EvaluateAuditSceneEnum.APPOINT_SCORE.getScene()), EvaluateAuditSceneEnum.APPOINT_SCORE.getScene());
        }
    }

    public void setPlusSubtractNull() {
        this.finalPlusScore = null;
        this.finalSubtractScore = null;
    }

    public void exportScoreWeight(boolean openWeight) {
//        if (scoreTypeMap == null) {
//            return;
//        }
        if (!openWeight) {
            this.finalSelfScore = selfScoreNoWeight;
            this.finalPeerScore = peerScoreNoWeight;
            this.finalSubScore = subScoreNoWeight;
            this.finalSuperiorScore = superiorScoreNoWeight;
            this.finalAppointScore = appointScoreNoWeight;
           // this.finalItemScore = finalItemScore;
        }

    }

    private BigDecimal nomalFinalScore(List<PerfEvaluateTaskScoreResultDo> scoreTypeList, String scorerType) {
        Optional<BigDecimal> finalScoreOptional = scoreTypeList.stream().filter(l -> Objects.nonNull(l.getFinalScore())).map(PerfEvaluateTaskScoreResultDo::getFinalScore).reduce(BigDecimal::add);
        if (finalScoreOptional.isPresent()) {
            if (isOpenAvgWeightCompute()) {//平均权重
                return finalScoreOptional.get();
            }
            //非互评场景
            if (!EvaluateAuditSceneEnum.mutualScoreTypes().contains(scorerType)) {
                return finalScoreOptional.get();
            }

            //互评场景 + 开启平均分
            return getNomalPeerAndSubScore(scoreTypeList,false);//计算互评平均分
        }
        return BigDecimal.ZERO;
    }

    private BigDecimal computeAvgScore(BigDecimal sumScore, List<PerfEvaluateTaskScoreResultDo> results) {
        //互评场景 + 开启平均分
        Set<String> scoreIds = results.stream().filter(l -> l != null && l.getScoreWeight().compareTo(BigDecimal.ZERO) != 0)
                .map(PerfEvaluateTaskScoreResultDo::getScorerId).collect(Collectors.toSet());
        MutualScoreResultCompute compute = new MutualScoreResultCompute(sumScore, scoreIds.size());
        return compute.computeAvgScore(); //计算互评平均分
    }

    private BigDecimal computePlusAvgScore(BigDecimal sumScore, List<PerfEvaluateTaskScoreResultDo> results, boolean isWeightComp) {
        //互评场景 + 开启平均分
        Set<String> filter;
        if (isWeightComp) {
            filter = results.stream().filter(scoreResultDo -> scoreResultDo.getScoreWeight().compareTo(BigDecimal.ZERO) != 0 && "pass".equals(scoreResultDo.getAuditStatus())).map(PerfEvaluateTaskScoreResultDo::getScorerId).collect(Collectors.toSet());
        } else {
            filter = results.stream().filter(l -> l != null && l.getScoreWeight().compareTo(BigDecimal.ZERO) != 0 && Objects.nonNull(l.getFinalPlusScore()))
                    .map(PerfEvaluateTaskScoreResultDo::getScorerId).collect(Collectors.toSet());
        }
        MutualScoreResultCompute compute = new MutualScoreResultCompute(sumScore, filter.size());
        return compute.computeAvgScore(); //计算互评平均分
    }


    private BigDecimal computeSubtractAvgScore(BigDecimal sumScore, List<PerfEvaluateTaskScoreResultDo> results, boolean isWeightComp) {
        //互评场景 + 开启平均分
        Set<String> filter;
        if (isWeightComp) {
            filter = results.stream().filter(scoreResultDo -> scoreResultDo.getScoreWeight().compareTo(BigDecimal.ZERO) != 0 && "pass".equals(scoreResultDo.getAuditStatus())).map(PerfEvaluateTaskScoreResultDo::getScorerId).collect(Collectors.toSet());
        } else {
            filter = results.stream().filter(l -> l != null && l.getScoreWeight().compareTo(BigDecimal.ZERO) != 0  && Objects.nonNull(l.getFinalSubtractScore()))
                    .map(PerfEvaluateTaskScoreResultDo::getScorerId).collect(Collectors.toSet());
        }
        MutualScoreResultCompute compute = new MutualScoreResultCompute(sumScore, filter.size());
        return compute.computeAvgScore(); //计算互评平均分
    }

    private BigDecimal getNoWeightScore(List<PerfEvaluateTaskScoreResultDo> scoreTypeList, String scorerType) {
        if (CollectionUtils.isEmpty(scoreTypeList)) {
            return BigDecimal.ZERO;
        }
        BigDecimal finalScore = BigDecimal.ZERO;
        BigDecimal finalPlusScore = BigDecimal.ZERO;
        BigDecimal finalSubtractScore = BigDecimal.ZERO;
        //普通指标
        finalScore = finalScore.add(nomalFinalScore(scoreTypeList, scorerType));
//        Optional<BigDecimal> finalScoreOptional = scoreTypeList.stream().filter(l -> Objects.nonNull(l.getFinalScore())).map(PerfEvaluateTaskScoreResultDo::getFinalScore).reduce(BigDecimal::add);
//        if (finalScoreOptional.isPresent()) {
//            finalScore = finalScore.add(finalScoreOptional.get());
//        }
        //加分指标
        finalScore = finalScore.add(getNoWeightFinalPlusScore(scoreTypeList, scorerType));
//        Optional<BigDecimal> finalPlusScoreOptional = scoreTypeList.stream().filter(l -> Objects.nonNull(l.getFinalPlusScore())).map(PerfEvaluateTaskScoreResultDo::getFinalPlusScore).reduce(BigDecimal::add);
//        if (finalPlusScoreOptional.isPresent()) {
//            finalScore = finalScore.add(finalPlusScoreOptional.get());
//        }
        //减分指标
        finalScore = finalScore.subtract(getNoWeightFinalSubtractScore(scoreTypeList, scorerType));
//        Optional<BigDecimal> finalSubtractScoreOptional = scoreTypeList.stream().filter(l -> Objects.nonNull(l.getFinalSubtractScore())).
//                map(r -> r.getFinalSubtractScore().compareTo(BigDecimal.ZERO) < 0 ? r.getFinalSubtractScore().abs() : r.getFinalSubtractScore()).reduce(BigDecimal::add);
//        if (finalSubtractScoreOptional.isPresent()) {
//            finalScore = finalScore.subtract(finalSubtractScoreOptional.get());
//        }
        //加分指标
        BigDecimal noWeightFinalWeightPlusScore = getNoWeightFinalWeightPlusScore(scoreTypeList, scorerType);
        this.finalPlusScore = finalPlusScore.add(noWeightFinalWeightPlusScore).add(Objects.isNull(this.finalPlusScore) ? BigDecimal.ZERO : this.finalPlusScore);
//        Optional<BigDecimal> finalWeightPlusScoreOptional = scoreTypeList.stream().filter(l -> Objects.nonNull(l.getFinalWeightPlusScore())).map(PerfEvaluateTaskScoreResultDo::getFinalWeightPlusScore).reduce(BigDecimal::add);
//        if (finalWeightPlusScoreOptional.isPresent()) {
//            this.finalPlusScore = finalPlusScore.add(finalWeightPlusScoreOptional.get()).add(Objects.isNull(this.finalPlusScore) ? BigDecimal.ZERO : this.finalPlusScore);
//        }

        //减分指标
        BigDecimal noWeightFinalWeightSubtractScore = getNoWeightFinalWeightSubtractScore(scoreTypeList, scorerType);
        this.finalSubtractScore = finalSubtractScore.add(noWeightFinalWeightSubtractScore).add(Objects.isNull(this.finalSubtractScore) ? BigDecimal.ZERO : this.finalSubtractScore);
       // finalWeightSubtractScoreOptional.ifPresent(bigDecimal -> this.finalSubtractScore = finalSubtractScore.add(bigDecimal).add(Objects.isNull(this.finalSubtractScore) ? BigDecimal.ZERO : this.finalSubtractScore));
        return finalScore;
    }

    private BigDecimal getNoWeightFinalWeightSubtractScore(List<PerfEvaluateTaskScoreResultDo> scoreTypeList, String scorerType) {
        Optional<BigDecimal> finalWeightSubtractScoreOptional = scoreTypeList.stream().filter(l -> Objects.nonNull(l.getFinalWeightSubtractScore())).
                map(r -> r.getFinalWeightSubtractScore().compareTo(BigDecimal.ZERO) < 0 ? r.getFinalWeightSubtractScore().abs() : r.getFinalWeightSubtractScore()).reduce(BigDecimal::add);

        if (!finalWeightSubtractScoreOptional.isPresent()) {
            return BigDecimal.ZERO;
        }
        if (isOpenAvgWeightCompute()) {//平均权重
            return finalWeightSubtractScoreOptional.get();
        }
        //非互评场景
        if (!EvaluateAuditSceneEnum.mutualScoreTypes().contains(scorerType)) {
            return finalWeightSubtractScoreOptional.get();
        }

        //互评场景 + 开启平均分
//        Set<String> scoreIds = scoreTypeList.stream().filter(l -> l != null && l.getScoreWeight().compareTo(BigDecimal.ZERO) != 0 && Objects.nonNull(l.getFinalWeightSubtractScore()))
//                .map(PerfEvaluateTaskScoreResultDo::getScorerId).collect(Collectors.toSet());
//        MutualScoreResultCompute compute = new MutualScoreResultCompute(finalWeightSubtractScoreOptional.get(), scoreIds.size());
//        return compute.computeAvgScore(); //计算互评平均分
        return getSubtractPeerAndSubScore(scoreTypeList,true);
    }


    private BigDecimal getNoWeightFinalWeightPlusScore(List<PerfEvaluateTaskScoreResultDo> scoreTypeList, String scorerType) {
        Optional<BigDecimal> finalWeightPlusScoreOptional = scoreTypeList.stream().filter(l -> Objects.nonNull(l.getFinalWeightPlusScore())).map(PerfEvaluateTaskScoreResultDo::getFinalWeightPlusScore).reduce(BigDecimal::add);
        if (!finalWeightPlusScoreOptional.isPresent()) {
            return BigDecimal.ZERO;
        }
        if (isOpenAvgWeightCompute()) {//平均权重
            return finalWeightPlusScoreOptional.get();
        }
        //非互评场景
        if (!EvaluateAuditSceneEnum.mutualScoreTypes().contains(scorerType)) {
            return finalWeightPlusScoreOptional.get();
        }

        //互评场景 + 开启平均分
//
//        Set<String> scoreIds = scoreTypeList.stream().filter(l -> l != null && l.getScoreWeight().compareTo(BigDecimal.ZERO) != 0 && Objects.nonNull(l.getFinalWeightPlusScore()))
//                .map(PerfEvaluateTaskScoreResultDo::getScorerId).collect(Collectors.toSet());
//        MutualScoreResultCompute compute = new MutualScoreResultCompute(finalWeightPlusScoreOptional.get(), scoreIds.size());
//        return compute.computeAvgScore(); //计算互评平均分

        return getPlusPeerAndSubScore(scoreTypeList, true);//计算互评平均分
    }

    private BigDecimal getNoWeightFinalSubtractScore(List<PerfEvaluateTaskScoreResultDo> scoreTypeList, String scorerType) {
        Optional<BigDecimal> finalSubtractScoreOptional = scoreTypeList.stream().filter(l -> Objects.nonNull(l.getFinalSubtractScore())).
                map(r -> r.getFinalSubtractScore().compareTo(BigDecimal.ZERO) < 0 ? r.getFinalSubtractScore().abs() : r.getFinalSubtractScore()).reduce(BigDecimal::add);
        if (!finalSubtractScoreOptional.isPresent()) {
            return BigDecimal.ZERO;
        }
        if (isOpenAvgWeightCompute()) {//平均权重
            return finalSubtractScoreOptional.get();
        }
        //非互评场景
        if (!EvaluateAuditSceneEnum.mutualScoreTypes().contains(scorerType)) {
            return finalSubtractScoreOptional.get();
        }

        //互评场景 + 开启平均分
//        Set<String> scoreIds = scoreTypeList.stream().filter(l -> l != null && l.getScoreWeight().compareTo(BigDecimal.ZERO) != 0  && Objects.nonNull(l.getFinalSubtractScore()))
//                .map(PerfEvaluateTaskScoreResultDo::getScorerId).collect(Collectors.toSet());
//        MutualScoreResultCompute compute = new MutualScoreResultCompute(finalSubtractScoreOptional.get(), scoreIds.size());
//        return compute.computeAvgScore(); //计算互评平均分
        return getSubtractPeerAndSubScore(scoreTypeList,false);//计算互评平均分 [互评场景 + 开启平均分]
    }

    private BigDecimal getNoWeightFinalPlusScore(List<PerfEvaluateTaskScoreResultDo> scoreTypeList, String scorerType) {
        Optional<BigDecimal> finalPlusScoreOptional = scoreTypeList.stream().filter(l -> Objects.nonNull(l.getFinalPlusScore())).map(PerfEvaluateTaskScoreResultDo::getFinalPlusScore).reduce(BigDecimal::add);
        if (!finalPlusScoreOptional.isPresent()) {
            return BigDecimal.ZERO;
        }
        if (isOpenAvgWeightCompute()) {//平均权重
            return finalPlusScoreOptional.get();
        }
        //非互评场景
        if (!EvaluateAuditSceneEnum.mutualScoreTypes().contains(scorerType)) {
            return finalPlusScoreOptional.get();
        }

        //互评场景 + 开启平均分
//        Set<String> scoreIds = scoreTypeList.stream().filter(l -> l != null && l.getScoreWeight().compareTo(BigDecimal.ZERO) != 0 && Objects.nonNull(l.getFinalPlusScore()))
//                .map(PerfEvaluateTaskScoreResultDo::getScorerId).collect(Collectors.toSet());
//        MutualScoreResultCompute compute = new MutualScoreResultCompute(finalPlusScoreOptional.get(), scoreIds.size());
//        return compute.computeAvgScore(); //计算互评平均分

        //互评场景 + 开启平均分
        return getPlusPeerAndSubScore(scoreTypeList, false);//计算互评平均分
    }
    private BigDecimal getWeightNomalFinalScore(List<PerfEvaluateTaskScoreResultDo> scoreTypeList, String scorerType) {
        BigDecimal finalScore = BigDecimal.ZERO;
        //普通指标
        Optional<BigDecimal> finalScoreOptional = scoreTypeList.stream().filter(l -> Objects.nonNull(l.getFinalWeightScore())).map(PerfEvaluateTaskScoreResultDo::getFinalWeightScore).reduce(BigDecimal::add);
        if (!finalScoreOptional.isPresent()) {
            return finalScore;
        }

        //平均权重 或 非互评场景
        if (isOpenAvgWeightCompute() || !EvaluateAuditSceneEnum.mutualScoreTypes().contains(scorerType)) {
            return finalScoreOptional.get();
        }

        //平均分，有效评价人的
        finalScore = getNomalPeerAndSubScore(scoreTypeList,true);//计算互评平均分
//        Set<String> filter = scoreTypeList.stream().filter(scoreResultDo -> scoreResultDo.getScoreWeight().compareTo(BigDecimal.ZERO) != 0).map(PerfEvaluateTaskScoreResultDo::getScorerId).collect(Collectors.toSet());
//        MutualScoreResultCompute compute = new MutualScoreResultCompute(finalScoreOptional.get(), filter.size());
//        finalScore = compute.computeAvgScore(); //计算互评平均分
        return finalScore;
    }

    private BigDecimal getNomalPeerAndSubScore(List<PerfEvaluateTaskScoreResultDo> scoreTypeList,boolean isWeight) {
        //普通指标互评需按指标平均，再加和
        ListWrap<PerfEvaluateTaskScoreResultDo> resultDosWrap = new ListWrap<>(scoreTypeList).groupBy(PerfEvaluateTaskScoreResultDo::getKpiItemId);
        Set<String> kpiItemIds = resultDosWrap.groupKeySet();
        if (CollUtil.isEmpty(kpiItemIds)) {
            return BigDecimal.ZERO;
        }
        BigDecimal score = BigDecimal.ZERO;
        for (String kpiItemId : kpiItemIds) {
            List<PerfEvaluateTaskScoreResultDo> tempResults = resultDosWrap.groupGet(kpiItemId);
            if (CollUtil.isEmpty(tempResults)) {
                continue;
            }
            Optional<BigDecimal> finalScore;
            if (isWeight) {
                finalScore = tempResults.stream().filter(l -> Objects.nonNull(l.getFinalWeightScore())).map(PerfEvaluateTaskScoreResultDo::getFinalWeightScore).reduce(BigDecimal::add);
            } else {
                finalScore = tempResults.stream().filter(l -> Objects.nonNull(l.getFinalScore())).map(PerfEvaluateTaskScoreResultDo::getFinalScore).reduce(BigDecimal::add);
            }
            if (finalScore.isPresent()) {
                //平均之后加和
                score = score.add(computeAvgScore(finalScore.get(), tempResults));
            }
        }
        return score.setScale(4, RoundingMode.HALF_UP);
    }

    private BigDecimal getWeightScore(List<PerfEvaluateTaskScoreResultDo> scoreTypeList, String scorerType) {
        if (CollectionUtils.isEmpty(scoreTypeList)) {
            return BigDecimal.ZERO;
        }
        //普通指标
        BigDecimal finalScore = getWeightNomalFinalScore(scoreTypeList, scorerType);
//        Optional<BigDecimal> finalScoreOptional = scoreTypeList.stream().filter(l -> Objects.nonNull(l.getFinalWeightScore())).map(PerfEvaluateTaskScoreResultDo::getFinalWeightScore).reduce(BigDecimal::add);
//        if (finalScoreOptional.isPresent()) {
//            finalScore = finalScore.add(finalScoreOptional.get());
//        }
        //加分指标
        finalScore = finalScore.add(getFinalWeightPlusScore(scoreTypeList,scorerType));
//        Optional<BigDecimal> finalPlusScoreOptional = scoreTypeList.stream().filter(l -> Objects.nonNull(l.getFinalWeightPlusScore())).map(PerfEvaluateTaskScoreResultDo::getFinalWeightPlusScore).reduce(BigDecimal::add);
//        if (finalPlusScoreOptional.isPresent()) {
//            finalScore = finalScore.add(finalPlusScoreOptional.get());
//        }
        //减分指标
        finalScore = finalScore.subtract(getFinalWeightSubtractScore(scoreTypeList,scorerType));
//        Optional<BigDecimal> finalSubtractScoreOptional = scoreTypeList.stream().filter(l -> Objects.nonNull(l.getFinalWeightSubtractScore())).
//                map(r -> r.getFinalWeightSubtractScore().compareTo(BigDecimal.ZERO) < 0 ? r.getFinalWeightSubtractScore().abs() : r.getFinalWeightSubtractScore()).reduce(BigDecimal::add);
//        if (finalSubtractScoreOptional.isPresent()) {
//            finalScore = finalScore.subtract(finalSubtractScoreOptional.get());
//        }
        return finalScore;
    }

    private BigDecimal getSubtractPeerAndSubScore(List<PerfEvaluateTaskScoreResultDo> scoreTypeList, boolean isWeight) {
        //减分互评需按指标平均，再加和
        ListWrap<PerfEvaluateTaskScoreResultDo> resultDosWrap = new ListWrap<>(scoreTypeList).groupBy(PerfEvaluateTaskScoreResultDo::getKpiItemId);
        Set<String> kpiItemIds = resultDosWrap.groupKeySet();
        if (CollUtil.isEmpty(kpiItemIds)) {
            return BigDecimal.ZERO;
        }
        BigDecimal score = BigDecimal.ZERO;
        for (String kpiItemId : kpiItemIds) {
            List<PerfEvaluateTaskScoreResultDo> tempResults = resultDosWrap.groupGet(kpiItemId);
            if (CollUtil.isEmpty(tempResults)) {
                continue;
            }
            Optional<BigDecimal> subtractScoreOptional;
            if (isWeight) {
                subtractScoreOptional = tempResults.stream().filter(l -> Objects.nonNull(l.getFinalWeightSubtractScore())).
                        map(r -> r.getFinalWeightSubtractScore().compareTo(BigDecimal.ZERO) < 0 ? r.getFinalWeightSubtractScore().abs() : r.getFinalWeightSubtractScore()).reduce(BigDecimal::add);
            } else {
                subtractScoreOptional = tempResults.stream().filter(l -> Objects.nonNull(l.getFinalSubtractScore())).
                        map(r -> r.getFinalSubtractScore().compareTo(BigDecimal.ZERO) < 0 ? r.getFinalSubtractScore().abs() : r.getFinalSubtractScore()).reduce(BigDecimal::add);
            }
            if (subtractScoreOptional.isPresent()) {
                //平均之后加和
                score = score.add(computeSubtractAvgScore(subtractScoreOptional.get(), tempResults, isWeight));
            }
        }
        return score.setScale(4, RoundingMode.HALF_UP);
    }

    private BigDecimal getPlusPeerAndSubScore(List<PerfEvaluateTaskScoreResultDo> scoreTypeList, boolean isWeight) {
        //加分互评需按指标平均，再加和
        ListWrap<PerfEvaluateTaskScoreResultDo> resultDosWrap = new ListWrap<>(scoreTypeList).groupBy(PerfEvaluateTaskScoreResultDo::getKpiItemId);
        Set<String> kpiItemIds = resultDosWrap.groupKeySet();
        if (CollUtil.isEmpty(kpiItemIds)) {
            return BigDecimal.ZERO;
        }
        BigDecimal score = BigDecimal.ZERO;
        for (String kpiItemId : kpiItemIds) {
            List<PerfEvaluateTaskScoreResultDo> tempResults = resultDosWrap.groupGet(kpiItemId);
            if (CollUtil.isEmpty(tempResults)) {
                continue;
            }
            Optional<BigDecimal> plusScoreOptional;
            if (isWeight) {
                plusScoreOptional = tempResults.stream().filter(l -> Objects.nonNull(l.getFinalWeightPlusScore())).map(PerfEvaluateTaskScoreResultDo::getFinalWeightPlusScore).reduce(BigDecimal::add);
            } else {
                plusScoreOptional = tempResults.stream().filter(l -> Objects.nonNull(l.getFinalPlusScore())).map(PerfEvaluateTaskScoreResultDo::getFinalPlusScore).reduce(BigDecimal::add);
            }
            if (plusScoreOptional.isPresent()) {
                //平均之后加和
                score = score.add(computePlusAvgScore(plusScoreOptional.get(), tempResults, isWeight));
            }
        }
        return score.setScale(4, RoundingMode.HALF_UP);
    }

    private BigDecimal getFinalWeightSubtractScore(List<PerfEvaluateTaskScoreResultDo> scoreTypeList, String scorerType) {
        BigDecimal finalSubtractScore = BigDecimal.ZERO;
        //减分指标
        Optional<BigDecimal> finalSubtractScoreOptional = scoreTypeList.stream().filter(l -> Objects.nonNull(l.getFinalWeightSubtractScore())).
                map(r -> r.getFinalWeightSubtractScore().compareTo(BigDecimal.ZERO) < 0 ? r.getFinalWeightSubtractScore().abs() : r.getFinalWeightSubtractScore()).reduce(BigDecimal::add);
        if (!finalSubtractScoreOptional.isPresent()) {
            return finalSubtractScore;
        }

        //平均权重 或 非互评场景
        if (isOpenAvgWeightCompute() || !EvaluateAuditSceneEnum.mutualScoreTypes().contains(scorerType)) {
            return finalSubtractScoreOptional.get();
        }

        //平均分，有效评价人的
//        Set<String> filter = scoreTypeList.stream().filter(scoreResultDo -> scoreResultDo.getScoreWeight().compareTo(BigDecimal.ZERO) != 0 && "pass".equals(scoreResultDo.getAuditStatus())).map(PerfEvaluateTaskScoreResultDo::getScorerId).collect(Collectors.toSet());
//        MutualScoreResultCompute compute = new MutualScoreResultCompute(finalSubtractScoreOptional.get(), filter.size());
//        finalSubtractScore = compute.computeAvgScore(); //计算互评平均分
//        return finalSubtractScore;

        return getSubtractPeerAndSubScore(scoreTypeList,true);
    }

    private BigDecimal getFinalWeightPlusScore(List<PerfEvaluateTaskScoreResultDo> scoreTypeList, String scorerType) {
        BigDecimal finalPlusScore = BigDecimal.ZERO;
        //加分指标
        Optional<BigDecimal> finalPlusScoreOptional = scoreTypeList.stream().filter(l -> Objects.nonNull(l.getFinalWeightPlusScore())).map(PerfEvaluateTaskScoreResultDo::getFinalWeightPlusScore).reduce(BigDecimal::add);
        if (!finalPlusScoreOptional.isPresent()) {
            return finalPlusScore;
        }

        //平均权重 或 非互评场景
        if (isOpenAvgWeightCompute() || !EvaluateAuditSceneEnum.mutualScoreTypes().contains(scorerType)) {
            return finalPlusScoreOptional.get();
        }

        //平均分，有效评价人的
        finalPlusScore = getPlusPeerAndSubScore(scoreTypeList,true);//计算互评平均分
//        Set<String> filter = scoreTypeList.stream().filter(scoreResultDo -> scoreResultDo.getScoreWeight().compareTo(BigDecimal.ZERO) != 0 && "pass".equals(scoreResultDo.getAuditStatus())).map(PerfEvaluateTaskScoreResultDo::getScorerId).collect(Collectors.toSet());
//        MutualScoreResultCompute compute = new MutualScoreResultCompute(finalPlusScoreOptional.get(), filter.size());
//        finalPlusScore = compute.computeAvgScore(); //计算互评平均分
        return finalPlusScore;
    }

    public String matchOrId() {
        if (StrUtil.isNotBlank(evalOrgId)) {
            return evalOrgId;
        }
        return orgId;
    }

    public void matchSupNames(List<KpiOrgSupNames> supNames) {
        if (CollUtil.isEmpty(supNames)) {
            return;
        }
        List<KpiOrgSupNames> supNameList = supNames.stream().filter(s -> StrUtil.equals(matchOrId(), s.getOrgId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(supNameList)) {
            return;
        }
        this.orgSupNames = supNameList.get(0).getOrgSupName();
        List<String> matchOrgNamePath = StrUtil.splitTrim(orgSupNames, "/");
        if (CollUtil.isEmpty(matchOrgNamePath) || matchOrgNamePath.size() == 1) {//需求:不显示根部门
            return;
        }
        this.supOrgName1 = matchOrgNamePath.size() > 1 ? matchOrgNamePath.get(1) : "";
        this.supOrgName2 = matchOrgNamePath.size() > 2 ? matchOrgNamePath.get(2) : "";
        this.supOrgName3 = matchOrgNamePath.size() > 3 ? matchOrgNamePath.get(3) : "";
    }

    public void repaceOrgNamePath() {
        if (StrUtil.isEmpty(this.atOrgNamePath)) {
            return;
        }
        List<String> orgNames = StrUtil.splitTrim(this.atOrgNamePath, "|");
        //排除根部门
        orgNames.remove(0);
        if (CollUtil.isEmpty(orgNames)) {
            return;
        }
        this.orgSupNames = StrUtil.join("|", orgNames);
        this.supOrgName1 = orgNames.size() > 0 ? orgNames.get(0) : "";
        this.supOrgName2 = orgNames.size() > 1 ? orgNames.get(1) : "";
        this.supOrgName3 = orgNames.size() > 2 ? orgNames.get(2) : "";
    }
}
