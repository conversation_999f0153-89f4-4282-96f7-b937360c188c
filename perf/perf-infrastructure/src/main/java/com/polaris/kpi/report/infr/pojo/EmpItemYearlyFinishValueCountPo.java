package com.polaris.kpi.report.infr.pojo;


import com.polaris.kpi.eval.domain.task.entity.Cycle;
import com.polaris.kpi.eval.domain.task.entity.empeval.ComputeFinishValueProgress;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/24 15:57
 */
@Setter
@Getter
@NoArgsConstructor
public class EmpItemYearlyFinishValueCountPo {

    private String empId;
    private String empName;
    private String empOrgNames;
    private String evalOrgName;
    private String evalOrgId;
    private String avatar;
    private String cycleType;
    private Integer cycleYear;

    private List<EmpYearlyItemTargetAndFinishValue> empYearlyItemTargetAndFinishValues;


    public void accCycleInfo(Cycle cycle) {
        this.cycleType = cycle.getType();
        this.cycleYear = cycle.getYear();
    }

    public void completeCycles(){
        List<Integer> existIndexes = empYearlyItemTargetAndFinishValues.stream().map(EmpYearlyItemTargetAndFinishValue::getCycleValue).collect(Collectors.toList());
        List<Integer> notExistIndexes = new ArrayList<>();
        List<Integer> monthAllIndexes = Arrays.asList(1,2,3,4,5,6,7,8,9,10,11,12);
        List<Integer> quarterAllIndexes = Arrays.asList(1,2,3,4);
        List<Integer> halfYearAllIndexes = Arrays.asList(1,2);

        if ("month".equals(cycleType) || "cross_month".equals(cycleType)){
            notExistIndexes = monthAllIndexes.stream().filter(index -> !existIndexes.contains(index)).collect(Collectors.toList());
        }
        if ("quarter".equals(cycleType)){
            notExistIndexes = quarterAllIndexes.stream().filter(index -> !existIndexes.contains(index)).collect(Collectors.toList());
        }
        if ("half_year".equals(cycleType)){
            notExistIndexes = halfYearAllIndexes.stream().filter(index -> !existIndexes.contains(index)).collect(Collectors.toList());
        }
        for (Integer index : notExistIndexes) {
            EmpYearlyItemTargetAndFinishValue orgYearly = new EmpYearlyItemTargetAndFinishValue( index, cycleType, cycleYear.toString());
            empYearlyItemTargetAndFinishValues.add(orgYearly);
        }

        //按照cycleValue升序排序
        empYearlyItemTargetAndFinishValues.sort(Comparator.comparing(EmpYearlyItemTargetAndFinishValue::getCycleValue));
    }


    public void putInSummary(ComputeFinishValueProgress progress) {

        EmpYearlyItemTargetAndFinishValue summary = new EmpYearlyItemTargetAndFinishValue();
        summary.setIsYearlySummary(true);
        summary.accProcess(progress);

        for (EmpYearlyItemTargetAndFinishValue empYearlyItemTargetAndFinishValue : empYearlyItemTargetAndFinishValues) {
            summary.setTargetValue(summary.getTargetValue().add(empYearlyItemTargetAndFinishValue.getTargetValue()));
            summary.setFinishValue(summary.getFinishValue().add(empYearlyItemTargetAndFinishValue.getFinishValue()));
            summary.accItemInfo(empYearlyItemTargetAndFinishValue);
        }

        summary.computeFinishRate();
        summary.removeZero();
        empYearlyItemTargetAndFinishValues.add(0,summary);
    }
}
