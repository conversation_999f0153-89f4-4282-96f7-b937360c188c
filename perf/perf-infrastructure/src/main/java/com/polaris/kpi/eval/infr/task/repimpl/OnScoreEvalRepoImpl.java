package com.polaris.kpi.eval.infr.task.repimpl;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.eval.KpiItemUsedField;
import cn.com.polaris.kpi.eval.ScoreEmp;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.polaris.kpi.eval.domain.task.dmsvc.migration.ScorerDataMingrationDmSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.SubmitOrEvalScorerV3DmSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.score.SkipScorerDmSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.score.TransferScorerV3DmSvc;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTaskOperation;
import com.polaris.kpi.eval.domain.task.entity.empeval.*;
import com.polaris.kpi.eval.domain.task.entity.interview.EvalTaskInterview;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrGoal;
import com.polaris.kpi.eval.domain.task.repo.OnScoreEvalRepo;
import com.polaris.kpi.eval.domain.task.type.EvalScoreSummary;
import com.polaris.kpi.eval.infr.task.builder.Audit2TypeRuleBd;
import com.polaris.kpi.eval.infr.task.builder.Audit3TypeRuleBd;
import com.polaris.kpi.eval.infr.task.builder.TmpEvalConf2EmpEvalRuleBd;
import com.polaris.kpi.eval.infr.task.dao.BatchScoreEvalMergeDao;
import com.polaris.kpi.eval.infr.task.ppojo.*;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.AdminTaskOperationDo;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.*;
import com.polaris.kpi.eval.infr.task.ppojo.interview.EvalTaskInterviewConfirmDo;
import com.polaris.kpi.eval.infr.task.ppojo.interview.EvalTaskInterviewDo;
import com.polaris.kpi.eval.infr.task.ppojo.interview.ResultInterviewConfirmFlowDo;
import com.polaris.kpi.eval.infr.task.ppojo.interview.ResultInterviewConfirmFlowNodeDo;
import com.polaris.kpi.org.domain.dept.entity.CompanyMsgCenter;
import com.polaris.kpi.org.infr.company.ppojo.CompanyMsgCenterDo;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.lufei.ibatis.builder.BatchUpdateBuilder;
import org.lufei.ibatis.builder.DeleteBuilder;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.common.data.ToDataBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class OnScoreEvalRepoImpl implements OnScoreEvalRepo {
    public static final String diffLog = "log";
    public static final String scoreRejectSeq = "score_reject";
    
    @Autowired
    private DomainDaoImpl domainDao;
    @Autowired
    private BatchScoreEvalMergeDao batchDao;

    @Override
    public EvalOnScoreStage getOnScoreEval(TenantId companyId, String taskUserId) {
        EvalUser evalUser = batchDao.getBaseTaskUser(companyId, taskUserId);
        EmpEvalMerge evalRule = batchDao.getBaseEvalMerge(companyId, taskUserId);
        EvalScorersWrap scorersWrap = batchDao.getEmpEvalScoreWrap(companyId, taskUserId);
        List<EmpEvalKpiType> types = batchDao.listEvalKpiTypeByEmpEvalId(companyId, taskUserId);
        List<EvalKpi> kpis = batchDao.listEmpEvalKpiItemByEmpEvalId(companyId, taskUserId);
        AdminTask task = batchDao.getAdminTaskMerge(companyId, evalUser.getTaskId());
        List<EvalItemScoreRule> itemRules = batchDao.listEmpEvalItemRuleByUserId(companyId, taskUserId);
        ListWrap<EvalItemScoreRule> preTypeRueGroup =  getTypeRule(companyId,taskUserId, types);
        ListWrap<EvalItemScoreRule> ruleWrap = new ListWrap<>(itemRules).asMap(EvalItemScoreRule::getKpiItemId);
        List<OkrKRPo> okrKRPos = batchDao.listOkrKRsByUserId(companyId, taskUserId);
        ListWrap<OkrKRPo> itemOkrMap = new ListWrap<>(okrKRPos).asMap(OkrKRPo::getKpiItemId);
        if (Objects.isNull(evalRule.getScoreSortConf())) {
            evalRule.setScoreSortConf(task.getScoreSortConf());
        }

        //指标
        ListWrap<EvalKpi> itemWrap = new ListWrap<>(kpis).groupBy(EvalKpi::getKpiTypeId);
        for (EmpEvalKpiType type : types) {
            type.setItems(itemWrap.groupGet(type.getKpiTypeId())); // set items
            //指标规则
            for (EvalKpi item : itemWrap.getDatas()) {
                item.setItemScoreRule(ruleWrap.mapGet(item.getKpiItemId()));
                OkrKRPo okrKRPo = itemOkrMap.mapGet(item.getKpiItemId());
                if (okrKRPo != null) {
                    item.setTarget(okrKRPo.getTargetId(), okrKRPo.getTargetName());
                    item.setActionId(okrKRPo.getActionId());
                }
            }
            type.setTypeRule(preTypeRueGroup.mapGet(type.getKpiTypeId()));
        }
        //兼容taskUser中的指标信息,后面排查一下使用情况.
        evalUser.setKpis(kpis);
        evalUser.setKpiTypes(types);

        KpiListWrap kpiTypesWrap = new KpiListWrap(types);
        evalRule.extendsRaterRule(kpiTypesWrap);
        evalRule.initScoreChain();
        evalRule.setEvalScorersWrap(scorersWrap);
        evalRule.setTaskName(task.getTaskName());
        return new EvalOnScoreStage(evalUser, evalRule, task);
    }

    private ListWrap<EvalItemScoreRule> getTypeRule(TenantId companyId, String taskUserId, List<EmpEvalKpiType> types) {
        Audit3TypeRuleBd typeRuleBd = new Audit3TypeRuleBd(types);
        ListWrap<EvalItemScoreRule> typeRules = typeRuleBd.build2();
        if (!typeRules.isEmpty()) {
            return typeRules;
        }

        List<EvalItemScoreRule> preTypeRules = batchDao.listPreTypeRuleByUserId(companyId, taskUserId);
        return new ListWrap<>(preTypeRules).asMap(EvalItemScoreRule::getKpiTypeId);
    }

    @Override
    public EvalResetScoreEmp getResetScoreEmpEval(TenantId companyId, String taskUserId, List<ScoreEmp> scoreEmps) {
        List<String> scorerIds = scoreEmps.stream().map(ScoreEmp::getEmpId).collect(Collectors.toList());
        EvalUser evalUser = batchDao.getBaseTaskUser(companyId, taskUserId);
        EmpEvalMerge evalRule = batchDao.getBaseEvalMerge(companyId, taskUserId);
        EvalScorersWrap scorersWrap = batchDao.getEmpEvalScoreWrap(companyId, taskUserId);
        List<EmpEvalKpiType> types = batchDao.listEvalKpiTypeByEmpEvalId(companyId, taskUserId);

        ListWrap<EvalItemScoreRule> preTypeRueGroup =  getTypeRule(companyId,taskUserId, types);
        List<EvalKpi> kpis = batchDao.listEmpEvalKpiItemByEmpEvalId(companyId, taskUserId);
        AdminTask task = batchDao.getAdminTaskMerge(companyId, evalUser.getTaskId());
        EvalTaskInterview interview = batchDao.getEvalTaskInterviewByUserId(companyId, taskUserId);//查询面谈
        List<PerfEvaluateTaskAppealBatch> appealBatches = batchDao.listTaskAppealBatchByUserId(companyId, taskUserId);  //查询申诉
        List<CompanyMsgCenter> centerMsgs = batchDao.listEvalCenterMsgByUserId(companyId, taskUserId);
        List<KpiEmp> raters = batchDao.listByEmp(companyId, scorerIds);//需要重置的人
        if (Objects.isNull(evalRule.getScoreSortConf())) {
            evalRule.setScoreSortConf(task.getScoreSortConf());
        }

        //兼容taskUser中的指标信息,后面排查一下使用情况.
        evalUser.setKpis(kpis);
        evalUser.setKpiTypes(types);
        //指标
        ListWrap<EvalKpi> itemWrap = new ListWrap<>(kpis).groupBy(EvalKpi::getKpiTypeId);
        // set items
        for (EmpEvalKpiType type : types) {
            type.setItems(itemWrap.groupGet(type.getKpiTypeId()));
            type.setTypeRule(preTypeRueGroup.mapGet(type.getKpiTypeId()));
        }
        KpiListWrap kpiTypesWrap = new KpiListWrap(types);
        evalRule.extendsRaterRule(kpiTypesWrap);
        evalRule.initScoreChain();
        evalRule.setEvalScorersWrap(scorersWrap);
        evalRule.setTaskName(task.getTaskName());
        EmpEvalRule rule = new EmpEvalRule();
        BeanUtils.copyProperties(evalRule,rule);
        evalUser.setEmpEvalRule(rule);
        return new EvalResetScoreEmp(evalUser, evalRule, interview, appealBatches, centerMsgs, raters);
    }

    @Override
    public TransferScorerV3DmSvc getTransferScorerEmpEval(TenantId companyId, EvalUser evalUser,String fromEmpId, String toEmpId,  String opEmpId) {
        String taskUserId = evalUser.getId();
        EmpEvalMerge evalRule = getRuleMerge(companyId,evalUser.getTaskId(),taskUserId);
        List<CompanyMsgCenter> msgCenters = batchDao.listEvalCenterMsgByUserId(companyId, taskUserId);
        Set<String> scorerIds = new HashSet<>();
        scorerIds.add(toEmpId);
        scorerIds.add(fromEmpId);
        scorerIds.add(opEmpId);
        List<KpiEmp> raters = batchDao.listByEmp(companyId, scorerIds);//需要重置的人
        ListWrap<CompanyMsgCenter> msgCentersWrap = new ListWrap<>(msgCenters).groupBy(CompanyMsgCenter::getEmpId);
        return new TransferScorerV3DmSvc(companyId, opEmpId, fromEmpId, toEmpId, evalUser, evalRule, evalRule.getEvalScorersWrap(), msgCentersWrap, raters);
    }


    private EmpEvalMerge getRuleMerge(TenantId companyId,String taskId,String taskUserId){
        AdminTask task = batchDao.getAdminTaskMerge(companyId, taskId);
        EmpEvalMerge evalRule = batchDao.getBaseEvalMerge(companyId, taskUserId);
        EvalScorersWrap scorersWrap = batchDao.getEmpEvalScoreWrap(companyId, taskUserId);
        List<EmpEvalKpiType> types = batchDao.listEvalKpiTypeByEmpEvalId(companyId, taskUserId);
        List<EvalItemScoreRule> itemRules = batchDao.listEmpEvalItemRuleByUserId(companyId, taskUserId);
        ListWrap<EvalItemScoreRule> ruleWrap = new ListWrap<>(itemRules).asMap(EvalItemScoreRule::getKpiItemId);
        ListWrap<EvalItemScoreRule> preTypeRueGroup =  getTypeRule(companyId,taskUserId, types);
        if (Objects.isNull(evalRule.getScoreSortConf())) {
            evalRule.setScoreSortConf(task.getScoreSortConf());
        }

        List<EvalKpi> kpis = batchDao.listEmpEvalKpiItemByEmpEvalId(companyId, taskUserId);
        //指标
        ListWrap<EvalKpi> itemWrap = new ListWrap<>(kpis).groupBy(EvalKpi::getKpiTypeId);
        // set items
        types.forEach(type -> {
            List<EvalKpi> items = itemWrap.groupGet(type.getKpiTypeId());
            items.forEach(item -> item.setItemScoreRule(ruleWrap.mapGet(item.getKpiItemId())));
            type.setItems(items);
            type.setTypeRule(preTypeRueGroup.mapGet(type.getKpiTypeId()));
        });
        KpiListWrap kpiTypesWrap = new KpiListWrap(types);
        evalRule.extendsRaterRule(kpiTypesWrap);
        evalRule.initScoreChain();
        evalRule.setEvalScorersWrap(scorersWrap);
        evalRule.setTaskName(task.getTaskName());
        return evalRule;
    }

    @Override
    public SkipScorerDmSvc getSkipScorerEmpEval(TenantId companyId, String taskUserId, String skipScorerId, String skipScoreType, String opEmpId) {
        Set<String> scorerIds = new HashSet<>();
        scorerIds.add(skipScorerId);
        scorerIds.add(opEmpId);
        EvalUser evalUser = batchDao.getBaseTaskUser(companyId, taskUserId);
        EmpEvalMerge evalRule = getRuleMerge(companyId,evalUser.getTaskId(),taskUserId);
        List<CompanyMsgCenter> msgCenters = batchDao.listEvalCenterMsgByUserId(companyId, taskUserId);
        List<KpiEmp> raters = batchDao.listByEmp(companyId, scorerIds);//跳过和操作人的员工信息
        ListWrap<CompanyMsgCenter> msgCentersWrap = new ListWrap<>(msgCenters).groupBy(CompanyMsgCenter::getEmpId);
        evalUser.setTaskName(evalRule.getTaskName());
        return new SkipScorerDmSvc(companyId, opEmpId,  evalUser, evalRule, evalRule.getEvalScorersWrap(), msgCentersWrap, raters);
    }
    @Override
    public ListWrap<ScorerDataMingrationDmSvc> listOnScoreEvalMingration(TenantId companyId, List<String> taskUserIds) {
        ListWrap<EvalUser> evalUserMap = batchDao.mapBaseTaskUser(companyId, taskUserIds);
        if (evalUserMap.isEmpty()){
            return new ListWrap<>();
        }
        ListWrap<EmpEvalMerge> evalRuleMap = batchDao.mapBaseEvalMerge(companyId, taskUserIds);
        ListWrap<BaseScoreResult>  totalRsGroups = batchDao.listTotalLevelRs(companyId, taskUserIds);
        ListWrap<EmpEvalKpiType>  typeGroups = batchDao.listEvalKpiType(companyId, taskUserIds);
        ListWrap<EvalKpi>  kpiItemGroups =  batchDao.listEmpEvalKpiItem(companyId, taskUserIds);
        ListWrap<EmpEvalScorer> scorerRsGroups = batchDao.listWrapEmpEvalScoreRs(companyId, taskUserIds);
        ListWrap<EvalScoreResult> empScoreRsGroups = batchDao.listItemScoreRs(companyId, taskUserIds);

        ListWrap<EvalItemScoreRule> itemRuleGroups = batchDao.listEmpEvalItemRule(companyId, taskUserIds);
        ListWrap<EvalItemScoreRule> preTypeRuleGroups = batchDao.listPreTypeRule(companyId, taskUserIds);

        List<String> taskIds = CollUtil.map(evalUserMap.getDatas(), EvalUser::getTaskId, true);
        ListWrap<AdminTask> adminTaskMap = batchDao.mapAdminTaskMerge(companyId, taskIds);
        ListWrap<OkrGoal> goalsGroups =  batchDao.listOkrGoals(companyId, taskUserIds);
        ListWrap<KpiItemUsedField>  kpiItemUsedFieldGroups = batchDao.listItemUsedField(companyId, taskUserIds);
        ListWrap<PerfEvalTypeResult>  typeResultOfUserGroups = batchDao.listTypeScoreRs(companyId, taskUserIds);
        ListWrap<EvalAudit>  typeAuditsGroups = batchDao.listTypeAudit(companyId, taskUserIds);
        ListWrap<EvalScoreSummary>  summarysGroups = batchDao.listWrapSummaryByUserId(companyId, taskUserIds);
        ListWrap<EvalFormulaField>  formulaGroups = batchDao.listWrapEvalEmpFormulaField(companyId, taskUserIds);//公式
        Set<String> scorerEmpIds = new HashSet<>();
        List<ScorerDataMingrationDmSvc> rs = new ArrayList<>();
        for (EvalUser user : evalUserMap.getDatas()) {
            AdminTask task = adminTaskMap.mapGet(user.getTaskId());
            EmpEvalMerge evalRule = evalRuleMap.mapGet(user.getId());
            List<EvalItemScoreRule> itemRules = itemRuleGroups.groupGet(user.getId());
            List<EvalItemScoreRule> preTypeRules = preTypeRuleGroups.groupGet(user.getId());
            List<BaseScoreResult> totalRs = totalRsGroups.groupGet(user.getId());
            List<EvalScoreResult> empScoreRsOld = empScoreRsGroups.groupGet(user.getId());
            List<OkrGoal>  goals = goalsGroups.groupGet(user.getId());
            List<KpiItemUsedField>  kpiItemUsedFields = kpiItemUsedFieldGroups.groupGet(user.getId());
            List<PerfEvalTypeResult>  typeResultOfUsers = typeResultOfUserGroups.groupGet(user.getId());
            List<EvalAudit>  typeAudits = typeAuditsGroups.groupGet(user.getId());
            List<EvalScoreSummary>  summarys = summarysGroups.groupGet(user.getId());
            List<EvalFormulaField>  formulas = formulaGroups.groupGet(user.getId());
            List<EvalKpi>  kpis = kpiItemGroups.groupGet(user.getId());
            List<EmpEvalKpiType> kpiTypes = typeGroups.groupGet(user.getId());
            //kpiTypes 需要做下去重，根据kpiTypeId
            kpiTypes = kpiTypes.stream()
                    .filter(kpiType -> kpiType.getKpiTypeId() != null)
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(EmpEvalKpiType::getKpiTypeId, kpiType -> kpiType, (existing, replacement) -> existing),
                            map -> new ArrayList<>(map.values())));
            kpis = kpis.stream()
                    .filter(kpi -> kpi.asKpiItemKey() != null)
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(EvalKpi::asKpiItemKey, kpi -> kpi, (existing, replacement) -> existing),
                            map -> new ArrayList<>(map.values())));

            if (Objects.isNull(evalRule)) {//V1的数据
                if (!task.isTmpTask()){//是1.0任务，但是任务却非临时任务，则跳过
                    log.warn("1.0的user任务，但是task任务却非临时任务，则跳过,脏数据，不处理 ，userid:{},taskId:{}",user.getId(),task.getId());
                    continue;
                }
                evalRule = task.buildAsEvalRule(user.getId());//转主阶段的配置
                TmpEvalConf2EmpEvalRuleBd builder = new TmpEvalConf2EmpEvalRuleBd(task.getTmpEvalFlow(), task.getId(), user.getEmpId(), companyId);
                builder.buildTo(evalRule); //转化评分流程配置
            } else {
                evalRule.fromTaskConf(task.getTaskName(), task.getId(), task.getScoreConf(), task.getScoreSortConf());
            }
            if (Objects.isNull(evalRule.getScoreSortConf())) {
                evalRule.setScoreSortConf(task.getScoreSortConf());
            }

            Audit2TypeRuleBd typeRuleBd = new Audit2TypeRuleBd(typeAudits);
            ListWrap<EvalItemScoreRule> preTypeRuesv2 =  typeRuleBd.build();
            if (preTypeRuesv2.isEmpty()) {
                Audit3TypeRuleBd typeRuleBd3 = new Audit3TypeRuleBd(kpiTypes);
                preTypeRuesv2 = typeRuleBd3.build2();
            }
            ListWrap<EvalItemScoreRule> preTypeRues = !preTypeRuesv2.isEmpty() ? preTypeRuesv2 : new ListWrap<>(preTypeRules).asMap(EvalItemScoreRule::getKpiTypeId);
            //指标
            ListWrap<EvalKpi> itemWrap = new ListWrap<>(kpis).groupBy(EvalKpi::getKpiTypeId);
            ListWrap<EvalFormulaField> formulaFieldsWrap = new ListWrap<>(formulas).groupBy(EvalFormulaField::getKpiItemId);
            ListWrap<PerfEvalTypeResult> typeScoreRs = new ListWrap<>(typeResultOfUsers).groupBy(PerfEvalTypeResult::getKpiTypeId);
            ListWrap<EvalScoreResult> itemScoreGroup = new ListWrap<>(empScoreRsOld).groupBy(EvalScoreResult::asKpiItemKey);
            ListWrap<EvalItemScoreRule> ruleWrap = new ListWrap<>(itemRules).asMap(EvalItemScoreRule::getKpiItemId);

            for (EmpEvalKpiType type : kpiTypes) {
                if (type.isOkr()) {
                    type.setOkrGoals(goals);
                }
                type.acceptTypeRs(typeScoreRs.groupGet(type.getKpiTypeId()));
                type.setItems(itemWrap.groupGet(type.getKpiTypeId()));
                type.initKpiItemUsedFields(kpiItemUsedFields);
                //指标规则
                for (EvalKpi item : itemWrap.getDatas()) {
                    item.setItemScoreRule(ruleWrap.mapGet(item.getKpiItemId()));
                    List<EvalScoreResult> results = itemScoreGroup.groupGet(item.asKpiItemKey());
                    item.setWaitScoresOld(results);
                    item.setFormulaFields(formulaFieldsWrap.groupGet(item.getKpiItemId()));
                }
                type.setTypeRule(preTypeRues.mapGet(type.getKpiTypeId()));
            }


            //兼容taskUser中的指标信息,后面排查一下使用情况.
            user.setKpis(itemWrap.getDatas());
            user.setKpiTypes(kpiTypes);

            List<EmpEvalScorer> empScoreRs = scorerRsGroups.isEmpty()? new ArrayList<>() : scorerRsGroups.groupGet(user.getId());
            EvalScorersWrap empScoreRsWrap = new EvalScorersWrap(empScoreRs);
            empScoreRsWrap.groupBy(EmpEvalScorer::getScorerId);
            evalRule.setEvalScorersWrap(empScoreRsWrap);//评分人

            //各种临时id收集区
            KpiListWrap kpiTypesWrap = new KpiListWrap(kpiTypes);
            evalRule.setTotalLevelResults(totalRs);
            evalRule.extendsRaterRule(kpiTypesWrap);
            evalRule.initScoreChain();
            //兼容taskUser中的指标信息,后面排查一下使用情况.
            user.setKpis(itemWrap.getDatas());
            user.setKpiTypes(kpiTypes);
            scorerEmpIds.addAll(evalRule.getKpiTypes().scoreIds());
            ScorerDataMingrationDmSvc svc = new ScorerDataMingrationDmSvc(user, evalRule, empScoreRsOld, typeResultOfUsers, totalRs, summarys,empScoreRsWrap);
            rs.add(svc);
        }

        //二次查询,组装员工名字信息
        ListWrap<KpiEmp> kpiEmpMap = batchDao.mapKpiEmp(companyId, scorerEmpIds);
        if (kpiEmpMap.isEmpty()) {
            return new ListWrap<>(rs).asMap(evalAtTask -> evalAtTask.getTaskUser().getId());
        }

        for (ScorerDataMingrationDmSvc dmSvc : rs) {
            if (CollUtil.isEmpty(dmSvc.getEmpScoreRs())) {
                continue;
            }
            dmSvc.getEmpScoreRs().forEach(scoreResult -> {
                KpiEmp kpiEmp = kpiEmpMap.mapGet(scoreResult.getScorerId());
                if (kpiEmp == null) {
                    return;
                }
                scoreResult.setScorerName(kpiEmp.getEmpName());
            });

        }
        return new ListWrap<>(rs).asMap(evalAtTask -> evalAtTask.getTaskUser().getId());

    }
    @Override
    public ScorerDataMingrationDmSvc getOnScoreEvalMingration(TenantId companyId, String taskUserId) {
        EvalUser evalUser = batchDao.getBaseTaskUser(companyId, taskUserId);
        EmpEvalMerge evalRule = batchDao.getBaseEvalMerge(companyId, taskUserId);
        List<BaseScoreResult> totalRs = batchDao.listTotalLevelRsByUserId(companyId, taskUserId);
        List<EmpEvalKpiType> kpiTypes = batchDao.listEvalKpiTypeByEmpEvalId(companyId, taskUserId);
        List<EvalKpi> kpis = batchDao.listEmpEvalKpiItemByEmpEvalId(companyId, taskUserId);
        AdminTask task = batchDao.getAdminTaskMerge(companyId, evalUser.getTaskId());
        List<PerfEvalTypeResult> typeResultOfUser = batchDao.listTypeScoreRsByUserId(companyId, taskUserId);
        List<EvalScoreResult> empScoreRs = batchDao.listItemScoreRsByUserId(companyId, taskUserId);
        List<EvalItemScoreRule> itemRules = batchDao.listEmpEvalItemRuleByUserId(companyId, taskUserId);
        List<EvalAudit> typeAudits = batchDao.listTypeAuditByUserId(companyId, taskUserId);
        List<EvalItemScoreRule> preTypeRules = batchDao.listPreTypeRuleByUserId(companyId, taskUserId);
        List<EvalScoreSummary> summarys = batchDao.listSummaryByUserId(companyId, taskUserId);
        ListWrap<EvalFormulaField> formulaWrap = batchDao.listEvalEmpFormulaField(companyId, taskUserId);//公式
        ListWrap<OkrGoal> goalsWrap  = batchDao.listOkrGoals(companyId, Arrays.asList(taskUserId));
        List<OkrGoal> goals = goalsWrap.groupGet(taskUserId);
        List<KpiItemUsedField>  kpiItemUsedFields = batchDao.listItemUsedField(companyId, taskUserId);
        EvalScorersWrap scorersWrap = batchDao.getEmpEvalScoreWrap(companyId, taskUserId);
        //指标
        ListWrap<EvalKpi> itemWrap = new ListWrap<>(kpis).groupBy(EvalKpi::getKpiTypeId);

        ListWrap<PerfEvalTypeResult> typeScoreRs = new ListWrap<>(typeResultOfUser).groupBy(PerfEvalTypeResult::getKpiTypeId);
        ListWrap<EvalScoreResult> itemScoreGroup = new ListWrap<>(empScoreRs).groupBy(EvalScoreResult::asKpiItemKey);
        ListWrap<EvalItemScoreRule> ruleWrap = new ListWrap<>(itemRules).asMap(EvalItemScoreRule::getKpiItemId);
        if (Objects.isNull(evalRule)) {//V1的数据
            if (!task.isTmpTask()){//是1.0任务，但是任务却非临时任务，则跳过
                log.warn("1.0的user任务，但是task任务却非临时任务，则跳过,脏数据，不处理 ，userid:{},taskId:{}",evalUser.getId(),task.getId());
                return null;
            }
            evalRule = task.buildAsEvalRule(evalUser.getId());//转主阶段的配置
            TmpEvalConf2EmpEvalRuleBd builder = new TmpEvalConf2EmpEvalRuleBd(task.getTmpEvalFlow(), task.getId(), evalUser.getEmpId(), companyId);
            builder.buildTo(evalRule); //转化评分流程配置
        } else {
            evalRule.fromTaskConf(task.getTaskName(), task.getId(), task.getScoreConf(), task.getScoreSortConf());
        }
        if (Objects.isNull(evalRule.getScoreSortConf())) {
            evalRule.setScoreSortConf(task.getScoreSortConf());
        }

        Audit2TypeRuleBd typeRuleBd = new Audit2TypeRuleBd(typeAudits);
        ListWrap<EvalItemScoreRule> preTypeRuesv2 =  typeRuleBd.build();
        if (preTypeRuesv2.isEmpty()) {
            Audit3TypeRuleBd typeRuleBd3 = new Audit3TypeRuleBd(kpiTypes);
            preTypeRuesv2 = typeRuleBd3.build2();
        }
        ListWrap<EvalItemScoreRule> preTypeRues = !preTypeRuesv2.isEmpty() ? preTypeRuesv2 : new ListWrap<>(preTypeRules).asMap(EvalItemScoreRule::getKpiTypeId);
        for (EmpEvalKpiType type : kpiTypes) {
            if (type.isOkr()) {
                type.setOkrGoals(goals);
            }
            type.acceptTypeRs(typeScoreRs.groupGet(type.getKpiTypeId()));
            type.setItems(itemWrap.groupGet(type.getKpiTypeId()));
            type.initKpiItemUsedFields(kpiItemUsedFields);
            //指标规则
            for (EvalKpi item : itemWrap.getDatas()) {
                item.setItemScoreRule(ruleWrap.mapGet(item.getKpiItemId()));
                List<EvalScoreResult> results = itemScoreGroup.groupGet(item.asKpiItemKey());
                item.setWaitScoresOld(results);
                item.setFormulaFields(formulaWrap.groupGet(item.getKpiItemId()));
            }
            type.setTypeRule(preTypeRues.mapGet(type.getKpiTypeId()));
        }
        //各种临时id收集区
        KpiListWrap kpiTypesWrap = new KpiListWrap(kpiTypes);
        evalRule.setTotalLevelResults(totalRs);
        evalRule.extendsRaterRule(kpiTypesWrap);
        evalRule.initScoreChain();
        //兼容taskUser中的指标信息,后面排查一下使用情况.
        evalUser.setKpis(itemWrap.getDatas());
        evalUser.setKpiTypes(kpiTypes);
        Set<String> scorerEmpIds = new HashSet<>(evalRule.getKpiTypes().scoreIds());
        //二次查询,组装员工名字信息
        ListWrap<KpiEmp> kpiEmpMap = batchDao.mapKpiEmp(companyId, scorerEmpIds);
        for (EvalScoreResult scoreResult : empScoreRs) {
            if (StrUtil.isEmpty(scoreResult.getScorerId()) || kpiEmpMap.isEmpty()) {
                continue;
            }
            KpiEmp kpiEmp = kpiEmpMap.mapGet(scoreResult.getScorerId());
            if (kpiEmp == null) {
                continue;
            }
            scoreResult.setScorerName(kpiEmp.getEmpName());
        }
        return new ScorerDataMingrationDmSvc(evalUser, evalRule, empScoreRs, typeResultOfUser, totalRs, summarys,scorersWrap);
    }

    @Override
    public ListWrap<EvalOnScoreStage> listOnScoreEval(TenantId companyId, List<String> taskUserIds) {
        ListWrap<EvalUser> evalUserMap = batchDao.mapBaseTaskUser(companyId, taskUserIds);
        ListWrap<EmpEvalMerge> evalRuleMap = batchDao.mapBaseEvalMerge(companyId, taskUserIds);
        ListWrap<EmpEvalScorer> scorerRsGroups = batchDao.listWrapEmpEvalScoreRs(companyId, taskUserIds);
        List<String> taskIds = CollUtil.map(evalUserMap.getDatas(), EvalUser::getTaskId, true);
        ListWrap<AdminTask> adminTaskMap = batchDao.mapAdminTaskMerge(companyId, taskIds);
        ListWrap<EmpEvalKpiType> typeGroups = batchDao.listEvalKpiType(companyId, taskUserIds);
        ListWrap<EvalKpi> kpiItemGroups = batchDao.listEmpEvalKpiItem(companyId, taskUserIds);
        ListWrap<CompanyMsgCenter> centerMsgGroups = batchDao.listEvalCenterMsg(companyId, taskUserIds);
        ListWrap<EvalItemScoreRule> itemRuleGroups = batchDao.listEmpEvalItemRule(companyId, taskUserIds);
        ListWrap<EvalItemScoreRule> preTypeRuleGroups = batchDao.listPreTypeRule(companyId, taskUserIds);
        ListWrap<OkrKRPo> okrRefGroups = batchDao.listOkrKRs(companyId, taskUserIds);

        List<EvalOnScoreStage> rs = new ArrayList<>();
        for (EvalUser user : evalUserMap.getDatas()) {
            AdminTask task = adminTaskMap.mapGet(user.getTaskId());
            EmpEvalMerge evalRule = evalRuleMap.mapGet(user.getId());
            ListWrap<EvalItemScoreRule> ruleWrap = new ListWrap<>(itemRuleGroups.groupGet(user.getId())).asMap(EvalItemScoreRule::getKpiItemId);
            List<EvalItemScoreRule> preTypeRules = preTypeRuleGroups.groupGet(user.getId());


            List<OkrKRPo> okrKRPos = okrRefGroups.groupGet(user.getId());
            ListWrap<OkrKRPo> itemOkrMap = new ListWrap<>(okrKRPos).asMap(OkrKRPo::getKpiItemId);
            List<EmpEvalKpiType> kpiTypes = typeGroups.groupGet(user.getId());

            Audit3TypeRuleBd typeRuleBd = new Audit3TypeRuleBd(kpiTypes);
            ListWrap<EvalItemScoreRule> typeRules = typeRuleBd.build2();
            ListWrap<EvalItemScoreRule> preTypeRues = !typeRules.isEmpty() ? typeRules : new ListWrap<>(preTypeRules).asMap(EvalItemScoreRule::getKpiTypeId);
            //指标
            ListWrap<EvalKpi> itemWrap = new ListWrap<>(kpiItemGroups.groupGet(user.getId())).groupBy(EvalKpi::getKpiTypeId);
            //兼容taskUser中的指标信息,后面排查一下使用情况.
            user.setKpis(itemWrap.getDatas());
            user.setKpiTypes(kpiTypes);

            List<EmpEvalScorer> empScoreRs = scorerRsGroups.groupGet(user.getId());
            EvalScorersWrap empScoreRsWrap = new EvalScorersWrap(empScoreRs);
            empScoreRsWrap.groupBy(EmpEvalScorer::getScorerId);
            evalRule.setEvalScorersWrap(empScoreRsWrap);//评分人
            if (Objects.isNull(evalRule.getScoreSortConf())) {
                evalRule.setScoreSortConf(task.getScoreSortConf());
            }

            evalRule.fromTaskConf(task.getTaskName(), task.getId(), task.getScoreConf(), task.getScoreSortConf());
            // set items
            for (EmpEvalKpiType type : kpiTypes) {
                type.setItems(itemWrap.groupGet(type.getKpiTypeId()));
                //指标规则
                for (EvalKpi item : itemWrap.getDatas()) {
                    item.setItemScoreRule(ruleWrap.mapGet(item.getKpiItemId()));
                    OkrKRPo okrKRPo = itemOkrMap.mapGet(item.getKpiItemId());
                    if (okrKRPo != null) {
                        item.setTarget(okrKRPo.getTargetId(), okrKRPo.getTargetName());
                        item.setActionId(okrKRPo.getActionId());
                    }
                }
                type.setTypeRule(preTypeRues.mapGet(type.getKpiTypeId()));
            }

            KpiListWrap kpiTypesWrap = new KpiListWrap(kpiTypes);
            evalRule.extendsRaterRule(kpiTypesWrap);
            evalRule.initScoreChain();
            EvalOnScoreStage stage = new EvalOnScoreStage(user, evalRule, task, centerMsgGroups.groupGet(user.getId()));
            rs.add(stage);
        }
        return new ListWrap<>(rs).asMap(evalAtTask -> evalAtTask.getEvalUser().getId());
    }

    @Override
    public void batchSaveScore(List<EvalOnScoreStage> onScoreStageEvals, String opEmpId) {
        if (CollUtil.isEmpty(onScoreStageEvals)) {
            return;
        }
        EvalUser first = onScoreStageEvals.get(0).getEvalUser();

        BatchUpdateBuilder upUser = BatchUpdateBuilder.buildTable("perf_evaluate_task_user")
                .addSetCaseProp("reviewersJson",  "id:=")
                .addSetCaseProp("evaluationLevel",  "id:=")
                .addSetCaseProp("originalEvaluationLevel",  "id:=")
                .addSetCaseProp("stepId", "id:=")
                .whereEq("companyId", first.getCompanyId().getId())
                .whereEq("isDeleted", "false")
                .whereUseIn("id");

        List<OperationLogDo> logDoList = new ArrayList<>();

        //up 评分人
        BatchUpdateBuilder upScorer = BatchUpdateBuilder.buildTable("emp_eval_scorer")
                .addSetCaseProp("status", "taskUserId:=", "id:=")
                .addSetCaseProp("updatedTime", "taskUserId:=", "id:=")
                .whereEq("companyId", first.getCompanyId().getId())
                .whereEq("isDeleted", "false")
                .whereUseIn("id");

        //up 评分环节
        BatchUpdateBuilder upNode = BatchUpdateBuilder.buildTable("emp_scorer_score_node")
                .addSetCaseProp("status", "taskUserId:=", "id:=")
                .addSetCaseProp("scorerNodeScore", "taskUserId:=", "id:=")
                .addSetCaseProp("status", "taskUserId:=", "id:=")
                .addSetCaseProp("scorerNodeKpiTypes", "taskUserId:=", "id:=")
                .addSetCaseProp("totalComment", "taskUserId:=", "id:=")
                .addSetCaseProp("scoreLevel", "taskUserId:=", "id:=")
                .addSetCaseProp("totalScore", "taskUserId:=", "id:=")
                .addSetCaseProp("scoreAttUrl", "taskUserId:=", "id:=")
                .addSetCaseProp("handlerTime", "taskUserId:=", "id:=")
                .whereEq("companyId", first.getCompanyId().getId())
                .whereEq("isDeleted", "false")
                .whereUseIn("id");

        //up 评分环节 评价指标
        BatchUpdateBuilder upScoreKpiItem = BatchUpdateBuilder.buildTable("emp_scorer_score_kpi_item")
                .addSetCaseProp("status", "taskUserId:=", "id:=")
                .addSetCaseProp("score", "taskUserId:=", "id:=")
                .addSetCaseProp("noItemScore", "taskUserId:=", "id:=")
                .addSetCaseProp("scoreWeightScore", "taskUserId:=", "id:=")
                .addSetCaseProp("itemWeightScore", "taskUserId:=", "id:=")
                .addSetCaseProp("finalScore", "taskUserId:=", "id:=")
                .addSetCaseProp("finalWeightScore", "taskUserId:=", "id:=")
                .addSetCaseProp("scoreComment", "taskUserId:=", "id:=")
                .addSetCaseProp("scoreAttUrl", "taskUserId:=", "id:=")
                .addSetCaseProp("scoreLevel", "taskUserId:=", "id:=")
                .addSetCaseProp("scoreOption", "taskUserId:=", "id:=")
                .addSetCaseProp("vetoFlag", "taskUserId:=", "id:=")
                .addSetCaseProp("updatedTime", "taskUserId:=", "id:=")
                .whereEq("companyId", first.getCompanyId().getId())
                .whereEq("isDeleted", "false")
                .whereUseIn("id");

        //更新维度评价
        BatchUpdateBuilder upType = BatchUpdateBuilder.buildTable("emp_eval_kpi_type")
                .addSetCaseProp("typeLevel", "taskUserId:=", "kpiTypeId:=")
                .whereEq("isDeleted", "false")
                .whereEq("companyId", first.getCompanyId().getId())
                .whereUseIn("kpiTypeId");

        //up 更新指标等级
        BatchUpdateBuilder upKpi = BatchUpdateBuilder.buildTable("perf_evaluate_task_kpi")
                .addSetCaseProp("indLevel", "taskUserId:=", "id:=")
                .whereEq("isDeleted", "false")
                .whereEq("companyId", first.getCompanyId().getId())
                .whereUseIn("id");

        //需要将其暂存的评分数据去除。[如果是或签提交的评分人//这个逻辑在提交评分后事件操作]
        BatchUpdateBuilder clearCache = BatchUpdateBuilder.buildTable("submit_score_cache")
                .addSetCaseProp("isDeleted", "taskUserId:=")
                .whereEq("isDeleted", Boolean.FALSE.toString())
                .whereEq("companyId", first.getCompanyId().getId())
                .whereEq("ownerEmpId", opEmpId)
                .whereUseIn("taskUserId");

        //up 更新系统待办
        BatchUpdateBuilder upMsg = BatchUpdateBuilder.buildTable("company_msg_center")
                .addSetCaseProp("handlerStatus", "linkId:=", "id:=")
                .addSetCaseProp("updatedTime", "linkId:=", "id:=")
                .whereEq("companyId", first.getCompanyId().getId())
                .whereUseIn("id");

        boolean isUpType = false;
        boolean isUpKpi = false;
        boolean isUpScoreKpiItem = false;
        boolean isUpMsg = false;
        List<PerfEvaluateTaskUserDo> users = new ArrayList<>();
        for (EvalOnScoreStage onScoreStage : onScoreStageEvals) {
           // if (onScoreStage.isHaveSubmitTotalLevelNode()) {//总等级才需要更新,需要更新责任人，这里需要放开
                PerfEvaluateTaskUserDo userDo = new ToDataBuilder<>(onScoreStage.getEvalUser(), PerfEvaluateTaskUserDo.class).data();
                userDo.setCompanyId(first.getCompanyId().getId());
                users.add(userDo);
            //}
            //评分人upScorer
            List<EmpEvalScorer> scorers = onScoreStage.getScorers();
            //评分环节upNode
            for (EmpEvalScorer scorer : scorers) {
                EmpEvalScorerDo scorerDo = new ToDataBuilder<>(scorer, EmpEvalScorerDo.class).data();
                scorerDo.accup(first.getCompanyId().getId());
                upScorer.addBean(scorerDo);
                for (EmpEvalScorerNode scorerNode : scorer.getScorerNodes()) {
                    EmpEvalScorerNodeDo nodeDo = new ToDataBuilder<>(scorerNode, EmpEvalScorerNodeDo.class).data();
                    nodeDo.setUpdatedTime(new Date());
                    upNode.addBean(nodeDo);
                    //评分环节的评价指标upScoreKpiItem
                    for (EvalScorerNodeKpiType kpiType : scorerNode.getScorerNodeKpiTypes()) {
                        for (EvalScorerNodeKpiItem item : kpiType.getScorerNodeKpiItems()) {
                            EmpScorerScoreKpiItemDo data = new ToDataBuilder<>(item, EmpScorerScoreKpiItemDo.class).data();
                            data.setUpdatedTime(new Date());
                            upScoreKpiItem.addBean(data);
                            isUpScoreKpiItem = true;
                        }
                    }
                }
            }

            //维度upType
            List<EmpEvalKpiType> updatedTypes = onScoreStage.getUpdatedKpiTypes();
            for (EmpEvalKpiType empEvalKpiType : updatedTypes) {
                EmpEvalKpiTypeDo data = new ToDataBuilder<>(empEvalKpiType, EmpEvalKpiTypeDo.class).data();
                upType.addBean(data);
                isUpType = true;
            }

            //指标upKpi
            List<EvalKpi> updatedKpis = onScoreStage.getUpdatedKpis();
            for (EvalKpi evalKpi : updatedKpis) {
                PerfEvaluateTaskKpiDo data = new ToDataBuilder<>(evalKpi, PerfEvaluateTaskKpiDo.class).data();
                upKpi.addBean(data);
                isUpKpi = true;
            }
            //记录日志logDoList. ***_score
            List<OperationLogDo> logDos = Convert.toList(OperationLogDo.class, onScoreStage.getOpLogs());
            logDoList.addAll(logDos);

            //暂存clearCache
            EvalUser evalUser = onScoreStage.getEvalUser();
            SubmitScoreCacheDo cacheDo = new SubmitScoreCacheDo(evalUser.getCompanyId().getId(), evalUser.getId(), opEmpId);
            cacheDo.setIsDeleted(Boolean.TRUE.toString());
            clearCache.addBean(cacheDo);

            //clear msg
            List<CompanyMsgCenter> centers = onScoreStage.getCancelCenters();
            if (CollUtil.isNotEmpty(centers)){
                isUpMsg = true;
            }
            centers.stream().map(center -> new ToDataBuilder<>(center, CompanyMsgCenterDo.class).data()).forEach(centerDo -> {
                centerDo.setUpdatedTime(new Date());
                centerDo.setHandlerStatus(Boolean.TRUE.toString());
                upMsg.addBean(centerDo);
            });
        }

        if (CollUtil.isNotEmpty(users)) {
            upUser.addAllBean(users);
            domainDao.updateBatch(upUser);
        }
        domainDao.updateBatch(upScorer);
        domainDao.updateBatch(upNode);
        if (isUpScoreKpiItem) {
            domainDao.updateBatch(upScoreKpiItem);
        }
        if (isUpType) {
            domainDao.updateBatch(upType);
        }
        if (isUpKpi) {
            domainDao.updateBatch(upKpi);
        }
        domainDao.saveBatch(logDoList);//插入日志
        domainDao.updateBatch(clearCache);//提交后清除缓存
        if (isUpMsg) {
            domainDao.updateBatch(upMsg);//清除系统待办
        }
    }


    private BatchUpdateBuilder buildUpScorerStatus(String companyId, String taskUserId) {
        //up 评分人
        BatchUpdateBuilder upScorer = BatchUpdateBuilder.buildTable("emp_eval_scorer")
                .addSetCaseProp("status", "id:=")
                .addSetCaseProp("updatedTime", "id:=")
                .whereEq("companyId", companyId)
                .whereEq("taskUserId", taskUserId)
                .whereEq("isDeleted", "false")
                .whereUseIn("id");
        return upScorer;
    }

    @Override
    public void saveEvalOrScorer(SubmitOrEvalScorerV3DmSvc dmSvc) {
        EvalUser evalUser = dmSvc.getEvalUser();
        if (CollUtil.isNotEmpty(dmSvc.getOrFinishedScorerIds())) {
            UpdateBuilder up = UpdateBuilder.build("perf_evaluate_task_user")
                    .set("reviewers_json", JSONUtil.toJsonStr(evalUser.getReviewersJson()))
                    .whereEqReq("company_id", evalUser.getCompanyId().getId())
                    .whereEqReq("id", evalUser.getId());
            domainDao.update(up);
        }

        if (CollUtil.isEmpty(dmSvc.getOrWaitScorers())) {
            return;
        }
        List<EmpEvalScorer> scorers = dmSvc.getOrWaitScorers();
        List<String> scorerIds = scorers.stream().map(EmpEvalScorer::getId).collect(Collectors.toList());
        //up 评分人
        BatchUpdateBuilder upScorer = BatchUpdateBuilder.buildTable("emp_eval_scorer")
                .addSetCaseProp("status", "id:=")
                .addSetCaseProp("updatedTime", "id:=")
                .whereEq("companyId", evalUser.getCompanyId().getId())
                .whereEq("taskUserId", evalUser.getId())
                .whereEq("isDeleted", "false")
                .whereUseIn("id");
        //up 评分环节
        BatchUpdateBuilder upNode = BatchUpdateBuilder.buildTable("emp_scorer_score_node")
                .addSetCaseProp("status", "id:=")
                .addSetCaseProp("scorerNodeScore", "id:=")
                .addSetCaseProp("status", "id:=")
                .addSetCaseProp("scorerNodeKpiTypes", "id:=")
                .addSetCaseProp("totalComment", "id:=")
                .addSetCaseProp("scoreLevel", "id:=")
                .addSetCaseProp("totalScore", "id:=")
                .addSetCaseProp("scoreAttUrl", "id:=")
                .addSetCaseProp("transferType", "id:=")
                .addSetCaseProp("handlerTime", "id:=")
                .whereEq("companyId", evalUser.getCompanyId().getId())
                .whereEq("taskUserId", evalUser.getId())
                .whereEq("isDeleted", "false")
                .whereUseIn("id");

        //up 评分环节 评价指标
        BatchUpdateBuilder upScoreKpiItem = BatchUpdateBuilder.buildTable("emp_scorer_score_kpi_item")
                .addSetCaseProp("status", "id:=")
                .addSetCaseProp("score", "id:=")
                .addSetCaseProp("updatedTime", "id:=")
                .whereEq("companyId", evalUser.getCompanyId().getId())
                .whereEq("taskUserId", evalUser.getId())
                .whereEq("isDeleted", "false")
                .whereUseIn("id");

        //或签提交的评分人，需要将其暂存的评分数据去除。
        DeleteBuilder cacheQB = DeleteBuilder.build(SubmitScoreCacheDo.class, "c")
                .whereEqReq("companyId",  dmSvc.getCompanyId().getId())
                .whereEqReq("taskUserId", evalUser.getId())
                .whereEqReq("isDeleted", "false")
                .whereInReq("ownerEmpId", scorerIds);


        scorers.forEach(scorer -> {
            EmpEvalScorerDo scorerDo = new ToDataBuilder<>(scorer, EmpEvalScorerDo.class).data();
            scorerDo.accup(dmSvc.getCompanyId().getId());
            upScorer.addBean(scorerDo);
            for (EmpEvalScorerNode scorerNode : scorer.getScorerNodes()) {
                EmpEvalScorerNodeDo nodeDo = new ToDataBuilder<>(scorerNode, EmpEvalScorerNodeDo.class).data();
                upNode.addBean(nodeDo);
                //评分环节的评价指标upScoreKpiItem
                for (EvalScorerNodeKpiType kpiType : scorerNode.getScorerNodeKpiTypes()) {
                    kpiType.getScorerNodeKpiItems().stream().map(item -> new ToDataBuilder<>(item, EmpScorerScoreKpiItemDo.class).data())
                            .forEach(upScoreKpiItem::addBean);
                }
            }
        });

        this.domainDao.updateBatch(upScorer);
        this.domainDao.updateBatch(upNode);
        this.domainDao.updateBatch(upScoreKpiItem);
        this.domainDao.delete(cacheQB);
    }

    @Override
    public void saveEvalUserNodeScore(EvalUser evalUser, KpiListWrap kpiTypes, String opEmpId) {
        saveEvalUser(evalUser);
        if (kpiTypes.isEmpty()) {
            return;
        }
        String companyId = evalUser.getCompanyId().getId();
        String evalUserId = evalUser.getId();
        //更新维度评价
        BatchUpdateBuilder upType = buildUpType(companyId, evalUserId);
        //up 更新指标
        BatchUpdateBuilder upKpi = buildUpKpi(companyId, evalUserId);
        List<PerfEvaluateTaskKpiDo> kpiDatas = new ArrayList<>();
        //维度upType
        for (EmpEvalKpiType kpiType : kpiTypes.getDatas()) {
            kpiType.accup(evalUser.getCompanyId().getId(), opEmpId);
            //维度upType
            EmpEvalKpiTypeDo data = new ToDataBuilder<>(kpiType, EmpEvalKpiTypeDo.class).data();
            upType.addBean(data);
            //无指标需要修改，跳过
            if (CollUtil.isEmpty(kpiType.getItems())) {
                continue;
            }
            //指标upKpi
            for (EvalKpi evalKpi : kpiType.getItems()) {
                evalKpi.accup(evalUser.getCompanyId().getId(), opEmpId);
                PerfEvaluateTaskKpiDo kpiData = new ToDataBuilder<>(evalKpi, PerfEvaluateTaskKpiDo.class).data();
                kpiDatas.add(kpiData);
            }
        }

        domainDao.updateBatch(upType);
        if (CollUtil.isNotEmpty(kpiDatas)) {
            try {
                upKpi.addAllBean(kpiDatas);
                domainDao.updateBatch(upKpi);
            } catch (Exception e) {
                log.debug("saveEvalUserNodeScore error,kpiType.getItems():{},upKpi:{},", JSONUtil.toJsonStr(kpiTypes.getDatas()), JSONUtil.toJsonStr(upKpi));
                log.error("saveEvalUserNodeScore error", e);
            }
        }
    }

    @Override
    public void saveKpiItemAutoScore(String opEmpId, List<EvalOnExeStage> recomutedKpiScores) {
        if (CollUtil.isEmpty(recomutedKpiScores)) {
            return;
        }
        //评价中录入完成值
        for (EvalOnExeStage eval : recomutedKpiScores) {
            saveAutoScoreEvalUserNodeScore(eval.getEvalUser(), eval.getEmpEval().getKpiTypes(), opEmpId,eval.getEmpEval().getEvalScorersWrap());
        }
    }

    private void saveEvalUser(EvalUser evalUser) {
        UpdateBuilder up = UpdateBuilder.build("perf_evaluate_task_user")
                .set("final_score", evalUser.getFinalScore())
                .set("original_final_score", evalUser.getOriginalFinalScore())
                .set("evaluation_level", evalUser.getEvaluationLevel())
                .set("original_evaluation_level", evalUser.getOriginalEvaluationLevel())
                .set("final_item_auto_score", evalUser.getFinalItemAutoScore())
                .set("final_plus_score", evalUser.getFinalPlusScore())
                .set("final_subtract_score", evalUser.getFinalSubtractScore())
                .set("v3_final_self_score", evalUser.getV3FinalSelfScore())
                .set("v3_final_peer_score", evalUser.getV3FinalPeerScore())
                .set("v3_final_sub_score", evalUser.getV3FinalSubScore())
                .set("v3_final_superior_score", evalUser.getV3FinalSuperiorScore())
                .set("v3_final_item_score", evalUser.getV3FinalItemScore())
                .set("v3_final_appoint_score", evalUser.getV3FinalAppointScore())
                .set("v3_self_score", evalUser.getV3SelfScore())
                .set("v3_peer_score", evalUser.getV3PeerScore())
                .set("v3_sub_score", evalUser.getV3SubScore())
                .set("v3_superior_score", evalUser.getV3SuperiorScore())
                .set("v3_appoint_score", evalUser.getV3AppointScore())
                .whereEqReq("company_id", evalUser.getCompanyId().getId())
                .whereEqReq("id", evalUser.getId());
        int count = domainDao.update(up);
        if (count > 0) {
            evalUser.incrementVersion();
        }
    }

    public void saveAutoScoreEvalUserNodeScore(EvalUser evalUser, KpiListWrap kpiTypes, String opEmpId, EvalScorersWrap evalScorersWrap) {
        saveEvalUserNodeScore(evalUser, kpiTypes, opEmpId);
        //评分中的需要计算评分人环节得分
        //up 更新评分环节分数
        BatchUpdateBuilder upScorerNode = BatchUpdateBuilder.buildTable("emp_scorer_score_node")
                .addSetCaseProp("scorerNodeScore", "id:=")
                .addSetCaseProp("updatedTime", "id:=")
                .whereEq("companyId", evalUser.getCompanyId().getId())
                .whereEq("taskUserId", evalUser.getId())
                .whereEq("isDeleted", "false")
                .whereUseIn("id");
        //评分环节
        evalScorersWrap.empEvalScorerNodesAll().stream()
                .filter(EmpEvalScorerNode::isFinish).map(node -> new ToDataBuilder<>(node, EmpEvalScorerNodeDo.class).data())
                .forEach(upScorerNode::addBean);
        domainDao.updateBatch(upScorerNode);
    }

    private BatchUpdateBuilder buildUpType(String companyId, String taskUserId) {
        //更新维度评价
        BatchUpdateBuilder upType = BatchUpdateBuilder.buildTable("emp_eval_kpi_type")
                .addSetCaseProp("typeFinalScore", "kpiTypeId:=")
                .addSetCaseProp("typeFinalOriginalScore", "kpiTypeId:=")
                .addSetCaseProp("typeScore", "kpiTypeId:=")
                .addSetCaseProp("typeOriginalScore", "kpiTypeId:=")
                .addSetCaseProp("typeFinalSelfScore", "kpiTypeId:=")
                .addSetCaseProp("typeFinalPeerScore", "kpiTypeId:=")
                .addSetCaseProp("typeFinalSubScore", "kpiTypeId:=")
                .addSetCaseProp("typeFinalSuperiorScore", "kpiTypeId:=")
                .addSetCaseProp("typeFinalItemScore", "kpiTypeId:=")
                .addSetCaseProp("typeFinalAppointScore", "kpiTypeId:=")
                .addSetCaseProp("typeItemScore", "kpiTypeId:=")
                .addSetCaseProp("typeSelfScore", "kpiTypeId:=")
                .addSetCaseProp("typePeerScore", "kpiTypeId:=")
                .addSetCaseProp("typeSubScore", "kpiTypeId:=")
                .addSetCaseProp("typeSuperiorScore", "kpiTypeId:=")
                .addSetCaseProp("typeAppointScore", "kpiTypeId:=")
                .addSetCaseProp("updatedUser", "kpiTypeId:=")
                .addSetCaseProp("updatedTime", "kpiTypeId:=")
                .whereEq("companyId", companyId)
                .whereEq("taskUserId", taskUserId)
                .whereEq("isDeleted", "false")
                .whereUseIn("kpiTypeId");
        return upType;
    }

    private BatchUpdateBuilder buildUpKpi(String companyId, String taskUserId) {
        //up 更新指标
        BatchUpdateBuilder upKpi = BatchUpdateBuilder.buildTable("perf_evaluate_task_kpi")
                .addSetCaseProp("itemFinalOriginalScore", "id:=")
                .addSetCaseProp("itemFinalScore", "id:=")
                .addSetCaseProp("itemOriginalScore", "id:=")
                .addSetCaseProp("itemScore", "id:=")
                .addSetCaseProp("itemFinalSelfScore", "id:=")
                .addSetCaseProp("itemFinalPeerScore", "id:=")
                .addSetCaseProp("itemFinalSubScore", "id:=")
                .addSetCaseProp("itemFinalSuperiorScore", "id:=")
                .addSetCaseProp("itemFinalAppointScore", "id:=")
                .addSetCaseProp("itemFinalItemScore", "id:=")
                .addSetCaseProp("itemItemScore", "id:=")
                .addSetCaseProp("itemSelfScore", "id:=")
                .addSetCaseProp("itemPeerScore", "id:=")
                .addSetCaseProp("itemSubScore", "id:=")
                .addSetCaseProp("itemSuperiorScore", "id:=")
                .addSetCaseProp("itemAppointScore", "id:=")
                .addSetCaseProp("updatedUser", "id:=")
                .addSetCaseProp("updatedTime", "id:=")
                .whereEq("companyId", companyId)
                .whereEq("taskUserId", taskUserId)
                .whereEq("isDeleted", "false")
                .whereUseIn("id");
        return upKpi;
    }

    private BatchUpdateBuilder buildUpScorerNode(String companyId, String taskUserId) {
        //up 更新评分环节分数
        BatchUpdateBuilder upScorerNode = BatchUpdateBuilder.buildTable("emp_scorer_score_node")
                .addSetCaseProp("scorerNodeScore", "id:=")
                .addSetCaseProp("scorerNodeKpiTypes", "id:=")
                .addSetCaseProp("updatedTime", "id:=")
                .whereEq("companyId", companyId)
                .whereEq("taskUserId", taskUserId)
                .whereEq("isDeleted", "false")
                .whereUseIn("id");
        return upScorerNode;
    }

    private BatchUpdateBuilder buildUpScoreKpiItem(String companyId, String taskUserId) {
        //up 评分环节 评价指标
        BatchUpdateBuilder upScoreKpiItem = BatchUpdateBuilder.buildTable("emp_scorer_score_kpi_item")
                .addSetCaseProp("score", "id:=")
                .addSetCaseProp("scoreWeight", "id:=")
                .addSetCaseProp("noItemScore", "id:=")
                .addSetCaseProp("scoreWeightScore", "id:=")
                .addSetCaseProp("itemWeightScore", "id:=")
                .addSetCaseProp("finalScore", "id:=")
                .addSetCaseProp("finalWeightScore", "id:=")
                .addSetCaseProp("scoreComment", "id:=")
                .addSetCaseProp("scoreAttUrl", "id:=")
                .addSetCaseProp("scoreLevel", "id:=")
                .addSetCaseProp("scoreOption", "id:=")
                .addSetCaseProp("vetoFlag", "id:=")
                .addSetCaseProp("updatedTime", "id:=")
                .whereEq("companyId", companyId)
                .whereEq("taskUserId", taskUserId)
                .whereEq("isDeleted", "false")
                .whereUseIn("id");
        return upScoreKpiItem;
    }

    @Override
    public void saveEmpEvalSNScoreByTaskUserId(String companyId,String evalUserId,EvalScorersWrap evalScorersWrap){
        //up 更新评分环节分数
        BatchUpdateBuilder upScorerNode = buildUpScorerNode(companyId, evalUserId);
        //up 评分环节 评价指标
        BatchUpdateBuilder upScoreKpiItem = buildUpScoreKpiItem(companyId, evalUserId);
        //评分环节
        evalScorersWrap.empEvalScorerNodesAll().stream().filter(EmpEvalScorerNode::isFinish).map(node -> new ToDataBuilder<>(node, EmpEvalScorerNodeDo.class).data()).forEach(upScorerNode::addBean);
        //评分指标
        evalScorersWrap.empEvalScorerNodesKpiItemAll().stream().filter(item -> item.getScore() != null).map(item -> new ToDataBuilder<>(item, EmpScorerScoreKpiItemDo.class).data()).forEach(upScoreKpiItem::addBean);
        domainDao.updateBatch(upScorerNode);
        domainDao.updateBatch(upScoreKpiItem);
    }

    @Override
    public void saveEvalNodeScore(EvalUser evalUser, KpiListWrap kpiTypes, String opEmpId, EvalScorersWrap evalScorersWrap) {
        int count = domainDao.update(PerfEvaluateTaskUserDo.class, evalUser);
        if (count > 0) {
            evalUser.incrementVersion();
        }
        if (kpiTypes.isEmpty()) {
            return;
        }
        String companyId = evalUser.getCompanyId().getId();
        String evalUserId = evalUser.getId();
        //更新维度评价
        BatchUpdateBuilder upType = buildUpType(companyId, evalUserId);
        //up 更新指标
        BatchUpdateBuilder upKpi = buildUpKpi(companyId, evalUserId);
        //up 更新评分环节分数
        BatchUpdateBuilder upScorerNode = buildUpScorerNode(companyId, evalUserId);
        //up 评分环节 评价指标
        BatchUpdateBuilder upScoreKpiItem = buildUpScoreKpiItem(companyId, evalUserId);
        //评分环节
        evalScorersWrap.empEvalScorerNodesAll().stream().filter(EmpEvalScorerNode::isFinish).map(node -> new ToDataBuilder<>(node, EmpEvalScorerNodeDo.class).data()).forEach(upScorerNode::addBean);
        //评分指标
        List<EvalScorerNodeKpiItem> scorerNodeKpiItems = evalScorersWrap.empEvalScorerNodesKpiItemAll();
        scorerNodeKpiItems.stream().filter(EvalScorerNodeKpiItem::isScored).map(item -> new ToDataBuilder<>(item, EmpScorerScoreKpiItemDo.class).data()).forEach(upScoreKpiItem::addBean);
        //维度upType
        for (EmpEvalKpiType kpiType : kpiTypes.getDatas()) {
            kpiType.accup(evalUser.getCompanyId().getId(), opEmpId);
            //维度upType
            EmpEvalKpiTypeDo data = new ToDataBuilder<>(kpiType, EmpEvalKpiTypeDo.class).data();
            upType.addBean(data);

            //指标upKpi
            for (EvalKpi evalKpi : kpiType.getItems()) {
                evalKpi.accup(evalUser.getCompanyId().getId(), opEmpId);
                PerfEvaluateTaskKpiDo kpiData = new ToDataBuilder<>(evalKpi, PerfEvaluateTaskKpiDo.class).data();
                upKpi.addBean(kpiData);
            }
        }

        domainDao.updateBatch(upType);
        domainDao.updateBatch(upKpi);
        if (evalScorersWrap.isEmpty()) {
            log.info("无需评分人评价");
            return;
        }
        domainDao.updateBatch(upScorerNode);
        if (CollUtil.isNotEmpty(scorerNodeKpiItems)) {
            domainDao.updateBatch(upScoreKpiItem);
        }
    }

    private BatchUpdateBuilder buildUpScorerNodeStatus(String companyId, String taskUserId) {
        //up 评分环节状态
        BatchUpdateBuilder upScorerNode = BatchUpdateBuilder.buildTable("emp_scorer_score_node")
                .addSetCaseProp("status", "id:=")
                .addSetCaseProp("scorerNodeKpiTypes", "id:=")
                .whereEq("companyId", companyId)
                .whereEq("taskUserId", taskUserId)
                .whereEq("isDeleted", "false")
                .whereUseIn("id");
        return upScorerNode;
    }

    private BatchUpdateBuilder buildUpScorerNodeKpiStatus(String companyId, String taskUserId) {
        //up 评分环节状态
        BatchUpdateBuilder upScorerNodeKpi = BatchUpdateBuilder.buildTable("emp_scorer_score_kpi_item")
                .addSetCaseProp("status", "id:=")
                .whereEq("companyId", companyId)
                .whereEq("taskUserId", taskUserId)
                .whereEq("isDeleted", "false")
                .whereUseIn("id");
        return upScorerNodeKpi;
    }

    @Override
    public void saveResetScoreEmp(EvalUser evalUser, String opEmpId, EvalResetScoreEmp resetScoreEmp) {
        if (Objects.isNull(resetScoreEmp)) {
            return;
        }
        String companyId = evalUser.getCompanyId().getId();
        String taskUserId = evalUser.getId();
        int count = domainDao.update(PerfEvaluateTaskUserDo.class, evalUser);
        if (count > 0) {
            evalUser.incrementVersion();
        }
        //评分人
        BatchUpdateBuilder upScorerStatus = buildUpScorerStatus(companyId, taskUserId);
        BatchUpdateBuilder upScorerNode = buildUpScorerNodeStatus(companyId, taskUserId);
        BatchUpdateBuilder upScorerNodeKpi = buildUpScorerNodeKpiStatus(companyId, taskUserId);
        //面谈
        UpdateBuilder upInterview = buildUpInterview(companyId, taskUserId);
        BatchUpdateBuilder upInterviewConfirm = buildUpInterviewConfirm(companyId, taskUserId);
        BatchUpdateBuilder upInterviewConfirmFlow = buildUpInterviewConfirmFlow(companyId, taskUserId);
        BatchUpdateBuilder upInterviewConfirmFlowNode = buildUpInterviewConfirmFlowNode(companyId, taskUserId);
        //申诉
        BatchUpdateBuilder upAppealBatch = buildUpAppealBacth(companyId, taskUserId);
        BatchUpdateBuilder upAppealBatchInfo = buildUpAppealBacthInfo(companyId, taskUserId);
        //转换评分人Do
        boolean isUpKpi = covertScorersDo(companyId, resetScoreEmp.getEmpEvalScorers(), upScorerStatus, upScorerNode, upScorerNodeKpi);
        //转换面谈确认Do
        covertInterviewConfirmFlowDo(companyId, resetScoreEmp.getInterview(), upInterviewConfirm, upInterviewConfirmFlow, upInterviewConfirmFlowNode);
        //申诉转换Do
        covertAppealBatchDo(companyId, resetScoreEmp.getBatchs(), upAppealBatch, upAppealBatchInfo);
        //日志
        OperationLogDo opLogDo = new ToDataBuilder<>(resetScoreEmp.getOpLog(), OperationLogDo.class).data();

        domainDao.updateBatch(upScorerStatus);
        domainDao.updateBatch(upScorerNode);
        if (isUpKpi) {
            domainDao.updateBatch(upScorerNodeKpi);
        }
        this.upInterview(resetScoreEmp,  upInterview, upInterviewConfirm, upInterviewConfirmFlow, upInterviewConfirmFlowNode);
        this.upAppealBatch(resetScoreEmp,upAppealBatch,upAppealBatchInfo);
        domainDao.save(opLogDo);
    }

    @Override
    public void saveRejectScoreEmp(EvalUser evalUser, ScoreReject scoreReject,List<EmpEvalScorer> empEvalScorers,OperationLog opLog) {
        if (CollUtil.isEmpty(empEvalScorers)) {
            return;
        }
        String companyId = evalUser.getCompanyId().getId();
        String taskUserId = evalUser.getId();
        int count = domainDao.update(PerfEvaluateTaskUserDo.class, evalUser);
        if (count > 0) {
            evalUser.incrementVersion();
        }

        scoreReject.initOnNew(domainDao.nextIntId(scoreRejectSeq));
        ScoreRejectDo data = new ToDataBuilder<>(scoreReject, ScoreRejectDo.class).data();
        //日志
        OperationLogDo opLogDo = new ToDataBuilder<>(opLog, OperationLogDo.class).data();
        //评分人
        BatchUpdateBuilder upScorerStatus = buildUpScorerStatus(companyId, taskUserId);
        BatchUpdateBuilder upScorerNode = buildUpScorerNodeStatus(companyId, taskUserId);
        BatchUpdateBuilder upScorerNodeKpi = buildUpScorerNodeKpiStatus(companyId, taskUserId);
        //转换评分人Do
        boolean isUpKpi = covertScorersDo(companyId, empEvalScorers, upScorerStatus, upScorerNode, upScorerNodeKpi);

        domainDao.updateBatch(upScorerStatus);
        domainDao.updateBatch(upScorerNode);
        if (isUpKpi) {
            domainDao.updateBatch(upScorerNodeKpi);
        }
        domainDao.save(data);
        domainDao.save(opLogDo);
    }

    private void upInterview(EvalResetScoreEmp resetScoreEmp, UpdateBuilder upInterview, BatchUpdateBuilder upInterviewConfirm,
                             BatchUpdateBuilder upInterviewConfirmFlow, BatchUpdateBuilder upInterviewConfirmFlowNode) {
        if (Objects.isNull(resetScoreEmp.getInterview())) {
            return;
        }
        domainDao.update(upInterview);
        if (Objects.isNull(resetScoreEmp.getInterview().getConfirmFlow())) {
            return;
        }
        domainDao.updateBatch(upInterviewConfirm);
        domainDao.updateBatch(upInterviewConfirmFlow);
        domainDao.updateBatch(upInterviewConfirmFlowNode);
    }
    private void upAppealBatch(EvalResetScoreEmp resetScoreEmp, BatchUpdateBuilder upAppealBatch,BatchUpdateBuilder upAppealBatchInfo) {
        if (CollUtil.isEmpty(resetScoreEmp.getBatchs())) {
            return;
        }
        domainDao.updateBatch(upAppealBatch);
        domainDao.updateBatch(upAppealBatchInfo);
    }

    @Override
    public void saveTransferEval(EvalUser evalUser, List<CompanyMsgCenter> fromCenterMsgs, OperationLog log,AdminTaskOperation adminTaskOperation) {
        String taskUserId = evalUser.getId();
        int count = domainDao.update(PerfEvaluateTaskUserDo.class, evalUser);
        if (count > 0) {
            evalUser.incrementVersion();
        }

        //up 更新系统待办
        BatchUpdateBuilder upMsg = BatchUpdateBuilder.buildTable("company_msg_center")
                .addSetCaseProp("handlerStatus", "id:=")
                .addSetCaseProp("updatedTime", "id:=")
                .whereEq("companyId", evalUser.getCompanyId().getId())
                .whereEq("linkId", taskUserId)
                .whereUseIn("id");

        //clear msg
        fromCenterMsgs.stream().map(center -> new ToDataBuilder<>(center, CompanyMsgCenterDo.class).data()).forEach(centerDo -> {
            centerDo.setUpdatedTime(new Date());
            centerDo.setHandlerStatus(Boolean.TRUE.toString());
            upMsg.addBean(centerDo);
        });

        //记录日志
        OperationLogDo data = new ToDataBuilder<>(log, OperationLogDo.class).data();

        adminTaskOperation.setId(domainDao.nextLongAsStr(diffLog));
        AdminTaskOperationDo operationDo = new ToDataBuilder<>(adminTaskOperation,AdminTaskOperationDo.class).data();

        domainDao.save(data);
        domainDao.save(operationDo);
        domainDao.updateBatch(upMsg);//清除系统待办


    }

    @Override
    public void saveSkipEval(EvalUser evalUser, List<CompanyMsgCenter> fromCenterMsgs,OperationLog log) {
        String companyId = evalUser.getCompanyId().getId();
        String taskUserId = evalUser.getId();
        int count = domainDao.update(PerfEvaluateTaskUserDo.class, evalUser);
        if (count > 0) {
            evalUser.incrementVersion();
        }

        //记录日志
        OperationLogDo data = new ToDataBuilder<>(log, OperationLogDo.class).data();
        domainDao.save(data);

        if (CollUtil.isEmpty(fromCenterMsgs)){
            return;
        }
        //up 更新系统待办
        BatchUpdateBuilder upMsg = BatchUpdateBuilder.buildTable("company_msg_center")
                .addSetCaseProp("handlerStatus", "id:=")
                .addSetCaseProp("updatedTime", "id:=")
                .whereEq("companyId", companyId)
                .whereEq("linkId", taskUserId)
                .whereUseIn("id");

        //clear msg
        for (CompanyMsgCenter center : fromCenterMsgs) {
            CompanyMsgCenterDo centerDo = new ToDataBuilder<>(center, CompanyMsgCenterDo.class).data();
            centerDo.acceptUp();
            upMsg.addBean(centerDo);
        }
        domainDao.updateBatch(upMsg);//清除系统待办
    }


    private UpdateBuilder buildUpInterview(String companyId, String taskUserId) {
        //up 面谈
        UpdateBuilder del = UpdateBuilder.build(EvalTaskInterviewDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEq("company_id", companyId)
                .whereEq("task_user_id", taskUserId)
                .whereEq("is_deleted", "false");
        return del;
    }

    private BatchUpdateBuilder buildUpInterviewConfirm(String companyId, String taskUserId) {
        //up 面谈确认
        BatchUpdateBuilder upConfirm = BatchUpdateBuilder.buildTable("perf_eval_task_interview_confirm")
                .addSetCaseProp("isDeleted", "id:=")
                .addSetCaseProp("updatedTime", "id:=")
                .whereEq("companyId", companyId)
                .whereEq("taskUserId", taskUserId)
                .whereEq("isDeleted", "false")
                .whereUseIn("id");
        return upConfirm;
    }

    private BatchUpdateBuilder buildUpInterviewConfirmFlow(String companyId, String taskUserId) {
        //up 面谈确认流程
        BatchUpdateBuilder upConfirmFlow = BatchUpdateBuilder.buildTable("result_interview_confirm_flow")
                .addSetCaseProp("isDeleted", "id:=")
                .addSetCaseProp("updatedTime", "id:=")
                .whereEq("companyId", companyId)
                .whereEq("taskUserId", taskUserId)
                .whereEq("isDeleted", "false")
                .whereUseIn("id");
        return upConfirmFlow;
    }

    private BatchUpdateBuilder buildUpInterviewConfirmFlowNode(String companyId, String taskUserId) {
        //up 面谈确认流程节点
        BatchUpdateBuilder upConfirmFlowNode = BatchUpdateBuilder.buildTable("result_interview_confirm_flow_node")
                .addSetCaseProp("isDeleted", "id:=")
                .addSetCaseProp("updatedTime", "id:=")
                .whereEq("companyId", companyId)
                .whereEq("isDeleted", "false")
                .whereUseIn("id");
        return upConfirmFlowNode;
    }

    private BatchUpdateBuilder buildUpAppealBacth(String companyId, String taskUserId) {
        //up 申诉批次 PerfEvaluateTaskAppealBatch
        BatchUpdateBuilder upAppealBatch = BatchUpdateBuilder.buildTable("perf_evaluate_task_appeal_batch")
                .addSetCaseProp("isDeleted", "id:=")
                .addSetCaseProp("updatedTime", "id:=")
                .whereEq("companyId", companyId)
                .whereEq("taskUserId", taskUserId)
                .whereEq("isDeleted", "false")
                .whereUseIn("id");
        return upAppealBatch;
    }

    private BatchUpdateBuilder buildUpAppealBacthInfo(String companyId, String taskUserId) {
        //up 申诉批次信息 perf_evaluate_task_appeal_info
        BatchUpdateBuilder upAppealBatch = BatchUpdateBuilder.buildTable("perf_evaluate_task_appeal_info")
                .addSetCaseProp("isDeleted", "id:=")
                .addSetCaseProp("updatedTime", "id:=")
                .whereEq("companyId", companyId)
                .whereEq("taskUserId", taskUserId)
                .whereEq("isDeleted", "false")
                .whereUseIn("id");
        return upAppealBatch;
    }

    public void covertAppealBatchDo(String companyId, List<PerfEvaluateTaskAppealBatch> batchs, BatchUpdateBuilder upAppealBatch, BatchUpdateBuilder upAppealBatchInfo) {
        if (Objects.isNull(batchs)) {
            return;
        }

        for (PerfEvaluateTaskAppealBatch batch : batchs) {
            PerfEvaluateTaskAppealBatchDo batchDo = new ToDataBuilder<>(batch, PerfEvaluateTaskAppealBatchDo.class).data();
            batchDo.setCompanyId(companyId);
            batchDo.setUpdatedTime(new Date());
            upAppealBatch.addBean(batchDo);

            batch.getAppealInfos().stream().map(info -> new ToDataBuilder<>(info, PerfEvaluateTaskAppealInfoDo.class).data()).forEach(infoDo -> {
                infoDo.setCompanyId(companyId);
                infoDo.setUpdatedTime(new Date());
                upAppealBatchInfo.addBean(infoDo);
            });
        }
    }

    public void covertInterviewConfirmFlowDo(String companyId, EvalTaskInterview interview, BatchUpdateBuilder upConfirm,
                                             BatchUpdateBuilder upConfirmFlow, BatchUpdateBuilder upConfirmFlowNode) {
        if (Objects.isNull(interview)) {
            return;
        }

        if (CollUtil.isNotEmpty(interview.getConfirms())) {
            interview.getConfirms().stream().map(confirm -> new ToDataBuilder<>(confirm, EvalTaskInterviewConfirmDo.class).data()).forEach(confirmDo -> {
                confirmDo.setCompanyId(companyId);
                confirmDo.setUpdatedTime(new Date());
                upConfirm.addBean(confirmDo);
            });

            if (Objects.nonNull(interview.getConfirmFlow())) {
                ResultInterviewConfirmFlowDo confirmFlowDo = new ToDataBuilder<>(interview.getConfirmFlow(), ResultInterviewConfirmFlowDo.class).data();
                confirmFlowDo.setCompanyId(companyId);
                confirmFlowDo.setUpdatedTime(new Date());
                upConfirmFlow.addBean(confirmFlowDo);

                interview.getConfirmFlow().getConfirmFlowNodes().stream().map(flowNode -> new ToDataBuilder<>(flowNode, ResultInterviewConfirmFlowNodeDo.class).data()).forEach(confirmFlowNodeDo -> {
                    confirmFlowNodeDo.setCompanyId(companyId);
                    confirmFlowNodeDo.setUpdatedTime(new Date());
                    upConfirmFlowNode.addBean(confirmFlowNodeDo);
                });
            }
        }
    }

    private boolean covertScorersDo(String companyId, List<EmpEvalScorer> empEvalScorers, BatchUpdateBuilder upScorerStatus,
                                 BatchUpdateBuilder upScorerNode, BatchUpdateBuilder upScorerNodeKpi) {
        if (CollUtil.isEmpty(empEvalScorers)) {
            return false;
        }
        boolean isUpKpi = false;
        //已完成的不更新 //评分人
        for (EmpEvalScorer scorer : empEvalScorers) {
            upScorerStatus.addBean(buildScorerDo(companyId, scorer));
            //评分环节
            for (EmpEvalScorerNode scorerNode : scorer.getScorerNodes()) {
                if (scorerNode.isFinish()) {
                    continue;
                }

                upScorerNode.addBean(buildEmpEvalScorerNodeDo(companyId, scorerNode));
                if (CollUtil.isEmpty(scorerNode.getScorerNodeKpiTypes())) {
                    continue;
                }

                for (EvalScorerNodeKpiType scorerNodeKpiType : scorerNode.getScorerNodeKpiTypes()) {
                    if (CollUtil.isEmpty(scorerNodeKpiType.getScorerNodeKpiItems())) {
                        continue;
                    }
                    isUpKpi = true;
                    //评价指标
                    upScorerNodeKpi.addAllBean(buildEmpScorerScoreKpiItemDo(companyId, scorerNodeKpiType.getScorerNodeKpiItems()));
                }
            }
        }
        return isUpKpi;
    }

    private List<EmpScorerScoreKpiItemDo> buildEmpScorerScoreKpiItemDo(String companyId, List<EvalScorerNodeKpiItem> scorerNodeKpiItems){
        List<EmpScorerScoreKpiItemDo> scorerScoreKpiItemDos = new ArrayList<>();
        scorerNodeKpiItems.stream().map(kpiItem -> new ToDataBuilder<>(kpiItem, EmpScorerScoreKpiItemDo.class).data()).forEach(kpiItemDo -> {
            kpiItemDo.setCompanyId(companyId);
            scorerScoreKpiItemDos.add(kpiItemDo);
        });
        return scorerScoreKpiItemDos;
    }

    private EmpEvalScorerNodeDo buildEmpEvalScorerNodeDo(String companyId,EmpEvalScorerNode scorerNode){
        EmpEvalScorerNodeDo scorerNodeDo = new ToDataBuilder<>(scorerNode, EmpEvalScorerNodeDo.class).data();
        scorerNodeDo.accup(companyId);
        return scorerNodeDo;
    }
    private EmpEvalScorerDo buildScorerDo(String companyId,EmpEvalScorer scorer){
        EmpEvalScorerDo evalScorerDo = new ToDataBuilder<>(scorer, EmpEvalScorerDo.class).data();
        evalScorerDo.accup(companyId);
        return evalScorerDo;
    }
}
