package com.polaris.kpi.migration.impl;

import com.polaris.kpi.migration.entity.MigrationJdbcBatchExecutor;
import com.polaris.kpi.migration.entity.TableSelectInfo;
import com.polaris.kpi.migration.repo.IDingMigrationExportRepo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 钉钉迁移仓储实现类
 * 实现所有迁移相关的仓储方法，包括DMS调用、OSS调用、HTTP回调等
 */
@Service
public class DingMigrationExportImpl implements IDingMigrationExportRepo {
    private static final Logger log = LoggerFactory.getLogger(DingMigrationExportImpl.class);

    @Resource
    private MigrationJdbcBatchExecutor jdbcBatchExecutor;

    @Override
    public List<TableSelectInfo> getTableSelectStatements(String companyId) {
        return jdbcBatchExecutor.getTableSelectStatements(companyId);
    }

    /**
     * 处理单个表数据并返回处理状态
     */
    public boolean processTableDataWithStats(TableSelectInfo tableInfo, ZipOutputStream zipOut) {
        try {
            log.info("开始处理表: {}", tableInfo.getTableName());

            // 1. 检查表是否有数据（只查询数量，不查询数据）
            int dataCount = jdbcBatchExecutor.getTableDataCount(tableInfo.getSelectStatement());

            if (dataCount == 0) {
                log.info("表 {} 没有数据，跳过生成文件", tableInfo.getTableName());
                return false;
            }

            log.info("表 {} 查询到 {} 条数据，开始流式处理", tableInfo.getTableName(), dataCount);

            // 2. 流式处理数据，直接写入ZIP
            boolean success = processTableDataStreaming(tableInfo, zipOut, dataCount);

            if (success) {
                log.info("表 {} 处理完成，数据条数: {}", tableInfo.getTableName(), dataCount);
            }

            return success;

        } catch (Exception e) {
            log.error("处理表 {} 数据失败，跳过该表", tableInfo.getTableName(), e);
            return false;
        }
    }



    /**
     * 流式处理表数据
     */

    private boolean processTableDataStreaming(TableSelectInfo tableInfo, ZipOutputStream zipOut, int totalCount) {
        try {
            String fileName = tableInfo.getTableName() + ".sql";
            ZipEntry zipEntry = new ZipEntry(fileName);

            // 检查ZipOutputStream状态
            if (zipOut == null) {
                log.error("ZipOutputStream为null");
                return false;
            }

            // 创建ZIP条目
            zipOut.putNextEntry(zipEntry);

            // 分批处理数据
            int batchSize = 1000;
            int processedCount = 0;

            for (int offset = 0; offset < totalCount; offset += batchSize) {
                try {
                    // 分批查询数据
                    List<Map<String, Object>> batchData = jdbcBatchExecutor.queryDataInBatches(tableInfo.getTableName(),
                            tableInfo.getSelectStatement(), offset, batchSize);

                    if (batchData != null && !batchData.isEmpty()) {
                        // 直接写入ZIP，不使用BufferedWriter
                        for (Map<String, Object> row : batchData) {
                            String insertSql = jdbcBatchExecutor.generateSingleInsertStatement(tableInfo.getTableName(), row);
                            if (!insertSql.isEmpty()) {
                                byte[] sqlBytes = (insertSql + "\n").getBytes(StandardCharsets.UTF_8);
                                zipOut.write(sqlBytes);
                            }
                        }

                        processedCount += batchData.size();
                        log.debug("表 {} 已处理: {}/{}", tableInfo.getTableName(), processedCount, totalCount);

                        // 定期刷新
                        if (processedCount % 10000 == 0) {
                            zipOut.flush();
                            log.info("表 {} 处理进度: {}/{}", tableInfo.getTableName(), processedCount, totalCount);
                        }
                    }

                    // 清理批次数据
                    if (batchData != null) {
                        batchData.clear();
                        batchData = null;
                    }

                } catch (Exception e) {
                    log.error("处理批次数据失败: tableName={}, offset={}, batchSize={}",
                            tableInfo.getTableName(), offset, batchSize, e);
                    continue;
                }
            }

            // 关闭ZIP条目
            zipOut.closeEntry();

            log.info("表 {} 处理完成，数据条数: {}", tableInfo.getTableName(), totalCount);
            return true;

        } catch (Exception e) {
            log.error("流式处理表 {} 数据失败", tableInfo.getTableName(), e);
            return false;
        }
    }
}

