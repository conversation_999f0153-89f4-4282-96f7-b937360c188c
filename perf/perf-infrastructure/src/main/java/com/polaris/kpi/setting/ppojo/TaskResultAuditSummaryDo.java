package com.polaris.kpi.setting.ppojo;

import com.polaris.kpi.common.infr.DelData;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.ibatis.annotations.Ckey;

/** {
 "id":"",
 "companyId":"公司id",
 "taskId":"考核任务id",
 "level":"层级",
 "auditEmpId":"审批人",
 "totalCnt":"处理总人数",
 "finishCnt":"已处理人数",
 "isDeleted":"",
 "createdUser":"创建用户",
 "createdTime":"创建时间",
 "updatedUser":"修改用户",
 "updatedTime":"修改时间",
 "version":"版本号",
 } ***/
@Setter
@Getter
@NoArgsConstructor
public class TaskResultAuditSummaryDo extends DelData {

    @Ckey
    private String id;//
    private String taskId;//考核任务id
    private Integer level;//层级
    private String auditEmpId;//审批人
    private Integer totalCnt;//处理总人数
    private Integer finishCnt;//已处理人数
    private Integer status;     //0:待处理  1:已处理
}
