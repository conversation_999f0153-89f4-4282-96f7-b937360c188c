package com.polaris.kpi.report.infr.pojo;

import com.polaris.kpi.common.infr.DelData;
import lombok.Data;
import org.apache.ibatis.annotations.Ckey;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/5/12 16:50
 */
@Data
public class OrgItemSummaryDo extends DelData {

    @Ckey
    private String id;
    private String orgId;
    private String atOrgCodePath;
    private String itemId;
    private String itemName;
    private String itemUnit;
    private String cycleId;
    private BigDecimal targetValueSum;
    private BigDecimal finishValueSum;
    private Integer finishValueType;
    private Integer performanceType;

}
