package com.polaris.kpi.eval.infr.pip.msg.dao;

import cn.hutool.core.collection.CollUtil;
import com.polaris.acl.dept.repository.DeptEmpDao;
import com.polaris.kpi.eval.domain.pip.msg.entity.PipClearTodoMsg;
import com.polaris.kpi.eval.domain.pip.msg.entity.PipScorerMsg;
import com.polaris.kpi.eval.infr.pip.msg.ppojo.PipEvalMsgDo;
import com.polaris.kpi.eval.infr.pip.msg.ppojo.PipEvalMsgPo;
import com.polaris.kpi.eval.infr.pip.msg.query.PipEvalMsgQuery;
import com.polaris.kpi.eval.infr.pip.plan.ppojo.*;
import com.polaris.sdk.type.TenantId;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.lufei.ibatis.mapper.PagedList;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class PipEvalMsgDao {
    @Resource
    private DomainDaoImpl domainDao;
    @Resource
    private DeptEmpDao empDao;

    public void setDomainDao(DomainDaoImpl domainDao) {
        this.domainDao = domainDao;
    }

    public void setEmpDao(DeptEmpDao empDao) {
        this.empDao = empDao;
    }


    public PagedList<PipEvalMsgPo> pagedMyPipScorerMsg(PipEvalMsgQuery query) {
        if (CollUtil.isNotEmpty(query.getOrgIds())) {
            query.setOrgIds(empDao.listAllChildOrgIds(new TenantId(query.getCompanyId()), query.getOrgIds()));
        }
        ComQB comQB = ComQB.build(PipEvalMsgDo.class, "m")
                .join(PipEvalDo.class, "e")
                .appendOn("m.company_id = e.company_id and m.pip_eval_id = e.id ")
                .leftJoin(PipEvalRuleDo.class, "r")
                .appendOn("e.company_id = r.company_id and e.id = r.pip_eval_id")
                .clearSelect().select("m.id,m.scorer_type,m.updated_time")
                .select("e.id as pipEvalId,e.emp_id as evalEmpId, e.emp_name as empName,e.emp_avatar,e.emp_org_id as orgId,e.emp_org_name as orgName")
                .select("e.plan_id,e.plan_name,e.cycle_start_date,e.cycle_end_date")
                .setRsType(PipEvalMsgPo.class)
                .whereEq("m.company_id", query.getCompanyId())
                .whereEqReq("m.handler_emp_id", query.getOpEmpId())
                .whereIn("m.scorer_type", query.getScorerTypes())
                .whereEq("m.handler_status", Boolean.FALSE.toString())
                .whereEqReq("e.is_deleted",Boolean.FALSE.toString())
                .whereEqReq("r.is_deleted",Boolean.FALSE.toString())
                .whereLike("e.emp_name", query.getEvalEmpName())
                .whereLike("e.plan_name", query.getPlanName())
                .whereIn("e.emp_org_id", query.getOrgIds());
        comQB.orderByDesc("m.created_time");
        comQB.setPage(query.getPageNo(), query.getPageSize());
        return domainDao.listPage(comQB);
    }

    /**
     * 我的待处理任务列表
     */
    public List<PipPlanPo> listMyInHandPlan(String companyId, String scorerId) {
        ComQB comQB = ComQB.build(PipEvalMsgDo.class, "m")
                .leftJoin(PipEvalDo.class,"e")
                .appendOn("m.company_id = e.company_id and m.pip_eval_id = e.id ")
                .leftJoin(PipPlanDo.class,"p")
                .appendOn("e.company_id = p.company_id and e.plan_id = p.id")
                .clearSelect().select("p.id as plan_id,p.name as plan_name,e.id as pip_eval_id")
                .setRsType(PipPlanSelectPo.class)
                .whereEq("m.company_id", companyId)
                .whereEqReq("m.handler_emp_id",scorerId)
                .whereEq("m.handler_status", Boolean.FALSE.toString())
                .whereEqReq("e.is_deleted",Boolean.FALSE.toString())
                .whereEqReq("p.is_deleted",Boolean.FALSE.toString());
        comQB.orderByDesc("p.created_time");
        return domainDao.listAll(comQB);
    }

    public Integer cntUnhandledScorerMsg(String companyId,String opEmpId) {
        ComQB comQB = ComQB.build(PipEvalMsgDo.class, "m")
                .clearSelect().select("count(m.id)")
                .setRsType(Integer.class)
                .whereEq("m.company_id", companyId)
                .whereEqReq("m.handler_emp_id",opEmpId)
                .whereEq("m.handler_status", Boolean.FALSE.toString());
        return domainDao.findOne(comQB);
    }

    public List<PipScorerMsg> getPipEvalScorerMsg(String companyId,  String pipEvalId,String opEmpId) {
        ComQB comQB = ComQB.build(PipEvalMsgDo.class)
                .whereEq("company_id", companyId)
                .whereEq("pip_eval_id", pipEvalId)
                .whereEq("handler_emp_id", opEmpId)
                .whereEq("handler_status", Boolean.FALSE.toString());
        return domainDao.listAllDomain(comQB, PipScorerMsg.class);
    }

    public List<PipScorerMsg> listPipScorerMsg(PipClearTodoMsg clearTodoMsg) {
        ComQB comQB = ComQB.build(PipEvalMsgDo.class)
                .whereEq("company_id", clearTodoMsg.getCompanyId().getId())
                .whereIn("pip_eval_id",clearTodoMsg.getPipEvalIds())
                .whereIn("handler_emp_id",clearTodoMsg.getHandlerEmpIds())
                .whereEq("scorer_type",clearTodoMsg.getScorerType())
                .whereEq("handler_status", Boolean.FALSE.toString());
        return domainDao.listAllDomain(comQB,PipScorerMsg.class);
    }


    public void finishBatch(TenantId tenantId, List<String> ids) {
        UpdateBuilder up = UpdateBuilder.build(PipEvalMsgDo.class)
                .appendSet("handler_status='true'")
                .whereEqReq("companyId", tenantId.getId())
                .whereInReq("id", ids);
        domainDao.update(up);
    }


    public PagedList<PipEvalMsgPo> pagedMyHandledEndWork(PipEvalMsgQuery query) {
        if (CollUtil.isNotEmpty(query.getOrgIds())) {
            query.setOrgIds(empDao.listAllChildOrgIds(new TenantId(query.getCompanyId()), query.getOrgIds()));
        }
        ComQB comQB = ComQB.build(PipEvalMsgDo.class, "m")
                .join(PipEvalDo.class,"e")
                .appendOn("m.company_id = e.company_id and m.pip_eval_id = e.id ")
                .clearSelect().select("m.id,m.scorer_type,m.updated_time")
                .select("e.id as pipEvalId,e.emp_id as evalEmpId, e.emp_name as empName,e.emp_avatar,e.emp_org_id,e.emp_org_name")
                .select("e.plan_id,e.plan_name")
                .setRsType(PipEvalMsgPo.class)
                .whereEq("m.company_id", query.getCompanyId())
                .whereEqReq("m.handler_emp_id",query.getOpEmpId())
                .whereEq("m.handler_status", Boolean.TRUE.toString())
                .whereEq("e.is_deleted",Boolean.FALSE.toString())
                .whereLike("e.emp_name",query.getEvalEmpName())
                .whereIn("e.emp_org_id",query.getOrgIds())
                .whereIn("e.emp_id",query.getEvalEmpIds());
        comQB.orderByDesc("m.created_time");
        comQB.setPage(query.getPageNo(),query.getPageSize());
        return domainDao.listPage(comQB);
    }
}
