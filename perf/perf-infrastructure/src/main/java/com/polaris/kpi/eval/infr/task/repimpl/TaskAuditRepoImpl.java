package com.polaris.kpi.eval.infr.task.repimpl;

import cn.com.polaris.kpi.EmpStaff;
import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.com.polaris.kpi.company.MsgSceneEnum;
import cn.com.polaris.kpi.eval.Rater;
import cn.com.polaris.kpi.eval.RaterNode;
import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.perf.www.common.constant.BusinessConstant;
import com.perf.www.common.em.OperationLogSceneEnum;
import com.perf.www.common.utils.string.StringTool;
import com.polaris.acl.dept.domain.Emp;
import com.polaris.acl.dept.domain.emp.SimpleEmps;
import com.polaris.acl.dept.repository.DeptEmpDao;
import com.polaris.kpi.eval.domain.task.dmsvc.RejectFinishedValueDmSvc;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.BaseScoreResult;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalRule;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.AuditResultConf;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.BaseEvalFlowConf;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.ScoreSortConf;
import com.polaris.kpi.eval.domain.task.entity.flow.DisplayEvalFlow;
import com.polaris.kpi.eval.domain.task.entity.flow.FlowRater;
import com.polaris.kpi.eval.domain.task.entity.flow.LevelNode;
import com.polaris.kpi.eval.domain.task.entity.interview.EvalTaskInterview;
import com.polaris.kpi.eval.domain.task.entity.interview.EvalTaskInterviewConf;
import com.polaris.kpi.eval.domain.task.entity.interview.ResultInterviewConfirmFlow;
import com.polaris.kpi.eval.domain.task.repo.TaskAuditRepo;
import com.polaris.kpi.eval.domain.task.type.FinishValueAuditStatusEnum;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.eval.domain.temp.entity.PerfTemplEvaluate;
import com.polaris.kpi.eval.infr.task.dao.*;
import com.polaris.kpi.eval.infr.task.ppojo.*;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.EmpEvalScorerNodeDo;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.PerfEvalTotalLevelResultDo;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.PerfEvalTypeResultDo;
import com.polaris.kpi.org.domain.dept.entity.CompanyMsgCenter;
import com.polaris.kpi.org.domain.dept.type.TaskId;
import com.polaris.kpi.org.infr.company.dao.CompanyMsgCenterDao;
import com.polaris.kpi.org.infr.emp.dao.KpiEmpDao;
import com.polaris.kpi.org.infr.emp.pojo.EmployeeBaseInfoDo;
import com.polaris.kpi.setting.domain.entity.ResultAuditFlowNodeRater;
import com.polaris.sdk.type.AuditEnum;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import org.apache.commons.lang3.StringUtils;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.UpdateBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.infr.task.repimpl
 * @Author: lufei
 * @CreateTime: 2022-08-24  13:52
 * @Description: 审核流程
 * @Version: 1.0
 */
@Component
public class TaskAuditRepoImpl implements TaskAuditRepo {
    @Autowired
    private DomainDaoImpl domainDao;
    @Autowired
    private DeptEmpDao deptEmpDao;
    @Autowired
    private TaskKpiItemDao kpiItemDao;

    @Autowired
    private EmpEvalDao empEvalDao;
    @Autowired
    private EvalKpiDao evalKpiDao;
    @Autowired
    private EvaluateTaskDao evaluateTaskDao;
    @Autowired
    private KpiEmpDao empDao;
    @Autowired
    private CompanyMsgCenterDao centerDao;
    @Autowired
    private EvalTaskInterviewDao evalTaskInterviewDao;
    @Autowired
    private ResultInterviewConfirmFlowDao confirmFlowDao;
    private static final String auditSeq = "perf_evaluate_task_audit";

    public void setDomainDao(DomainDaoImpl domainDao) {
        this.domainDao = domainDao;
    }

    @Override
    public DisplayEvalFlow getFlow(EvalUser evalUser, String scene) {
        TenantId tenantId = evalUser.getCompanyId();
        List<EvalAudit> auditNodes = listAuditNode(tenantId, evalUser.getId(), scene);
        if (auditNodes.isEmpty()) {
            return null;
        }
        DisplayEvalFlow auditFlow = new DisplayEvalFlow(scene);
        List<FlowRater> results = listResult(tenantId, evalUser.getId(), scene).getDatas();
        ListWrap<FlowRater> groupOrder = new ListWrap<>(results).groupBy(flowRater -> flowRater.getApprovalOrder() + "");
        for (EvalAudit audit : auditNodes) {
            LevelNode level = buildEvalAudit(tenantId, groupOrder, audit);
            auditFlow.addLevel(level);
        }
        return auditFlow;
    }


    @Override
    public List<DisplayEvalFlow> listScoringFlow(EvalUser user, EmpEvalMerge empEvalRule) {
        TenantId tenantId = user.getCompanyId();
        String taskUserId = user.getId();
        ListWrap<EvalAudit> auditNodes = listScoringAuditNode(tenantId, user.getId());
        List<DisplayEvalFlow> auditFlows = new ArrayList<>();
        ScoreSortConf scoreSortConf = empEvalRule.getScoreSortConf();

        ListWrap<FlowRater> results = listResult(tenantId, taskUserId, AuditEnum.scoreScenes());
        //用于对相同的结点多个audit时进行合并.
        if (empEvalRule.needSelfScore()) {
            List<FlowRater> selftRts = results.groupGet(SubScoreNodeEnum.SELF_SCORE.getScene());
            FlowRater selfRt = selftRts.isEmpty() ? new FlowRater(user.getAvatar(), user.getEmpId(), user.getEmpName(), "on_the_job") :
                    selftRts.get(0);
            DisplayEvalFlow auditFlow = new DisplayEvalFlow(SubScoreNodeEnum.SELF_SCORE.getScene(), selfRt, scoreSortConf.getExeType());
            auditFlow.setNodeSort(scoreSortConf.getSort(auditFlow.sceneAtSortType()));
            auditFlow.matchDeadLineEndDate(user.matchDeadLineEndDate(TalentStatus.SCORING.getStatus()));
            auditFlows.add(auditFlow);
        }
        if (user.hasDirectionalItem()) {//有配置定向评分指标
            DisplayEvalFlow itemFlow = new DisplayEvalFlow(SubScoreNodeEnum.ITEM_SCORE.getScene());
            itemFlow.setNodeSort(scoreSortConf.getSort(itemFlow.sceneAtSortType()));
            itemFlow.matchDeadLineEndDate(user.matchDeadLineEndDate(TalentStatus.SCORING.getStatus()));
            auditFlows.add(itemFlow);
            List<FlowRater> itemRts = results.groupGet(SubScoreNodeEnum.ITEM_SCORE.getScene());
            if (CollUtil.isEmpty(itemRts)) {
                List<EvalScoreResult> preDisRs = user.dispatchDirectRaterItem();
                List<String> socrerIds = CollUtil.map(preDisRs, src -> src.getScorerId(), true);
                SimpleEmps simpleEmp = deptEmpDao.listSimpleEmp(user.getCompanyId().getId(), socrerIds);
                Function<Emp, FlowRater> raterFunc = emp -> new FlowRater(emp.getAvatar(), emp.getId(), emp.getName(), emp.getStatus());
                itemRts = CollUtil.map(simpleEmp.getEmps(), raterFunc, true);
            }
            itemFlow.setExeType(scoreSortConf.getExeType());
            itemFlow.addLevel(new LevelNode(itemRts));
            itemFlow.buildEndIf();
        }

        Map<String, DisplayEvalFlow> flowMap = new HashMap<>();

        for (Map.Entry<String, List<EvalAudit>> entry : auditNodes.getGroups().entrySet()) {
            String scene = entry.getKey();
            List<EvalAudit> evalAudits = entry.getValue();
            if (!flowMap.containsKey(scene)) {
                DisplayEvalFlow flow = new DisplayEvalFlow(scene);
                flow.setSuperiorScoreOrder(empEvalRule.isCustom() ? BusinessConstant.SUPERIOR_SCORE_ORDER_SAME_TIME
                        : empEvalRule.getS3SuperRater().getSuperiorScoreOrder());
                flow.setNodeSort(scoreSortConf.getSort(flow.sceneAtSortType()));
                flow.setExeType(scoreSortConf.getExeType());
                flow.initAnonymous(empEvalRule.getScoreView());
                flow.matchDeadLineEndDate(user.matchDeadLineEndDate(TalentStatus.SCORING.getStatus()));
                flowMap.put(scene, flow);
                auditFlows.add(flow);
            }
            DisplayEvalFlow auditFlow = flowMap.get(scene);
            for (EvalAudit audit : evalAudits) {
                List<FlowRater> rssOfScene = results.groupGet(audit.getScene());
                ListWrap<FlowRater> groupOrder = new ListWrap<>(rssOfScene).groupBy(flowRater -> flowRater.getApprovalOrder() + "");
                LevelNode level = buildEvalAudit(tenantId, groupOrder, audit);
                auditFlow.addLevel(level);
            }
            auditFlow.buildEndIf();
        }
        List<DisplayEvalFlow> sorts = auditFlows.stream().sorted((a, b) -> {
            if (a.dispatchOrder() != b.dispatchOrder()) {
                return a.dispatchOrder() - b.dispatchOrder();
            }
            if (a.timeOrder() != b.timeOrder()) {
                long l = a.timeOrder() - b.timeOrder();
                if (l > 0) {
                    return 1;
                } else {
                    return -1;
                }
            }
            return a.sceneOrder() - b.sceneOrder();
        }).collect(Collectors.toList());

        ListWrap<PerfEvalTotalLevelResultDo> listTotal = listTotal(tenantId.getId(), taskUserId);
        if (!empEvalRule.needComputeLevel()) {
            List<Rater> raters = empEvalRule.getTotalLevelRaters();
            DisplayEvalFlow auditFlow = new DisplayEvalFlow(SubScoreNodeEnum.TOTAL_LEVEL.getScene());
            LevelNode node = new LevelNode();
            List<FlowRater> raters1 = raters.stream().map(totalRater -> {
                FlowRater flowRater = new FlowRater(totalRater.getAvatar(), totalRater.getEmpId(), totalRater.getEmpName(), "on_the_job");
                PerfEvalTotalLevelResultDo totalLevel = listTotal.mapGet(totalRater.getEmpId());
                if (totalLevel == null) {
                    return flowRater;
                }
                flowRater.setApprovalOrder(totalLevel.getApprovalOrder());
                flowRater.setCreateTime(totalLevel.getCreatedTime());
                if ("pass".equals(totalLevel.getAuditStatus())) {
                    flowRater.setFinish(true);
                    flowRater.setFinishTime(totalLevel.getUpdatedTime());
                }
                return flowRater;
            }).collect(Collectors.toList());
            node.addAllSub(raters1);
            auditFlow.addLevel(node);
            auditFlow.setNodeSort(scoreSortConf.getSortItems().size());
            sorts.add(auditFlow);
            auditFlow.buildEndIf();
        }
        return sorts;
    }


    private List<DisplayEvalFlow> scoreFlowSorts(List<DisplayEvalFlow> auditFlows) {
        return auditFlows.stream().sorted((a, b) -> {
            if (a.dispatchOrder() != b.dispatchOrder()) {
                return a.dispatchOrder() - b.dispatchOrder();
            }
            if (a.timeOrder() != b.timeOrder()) {
                long l = a.timeOrder() - b.timeOrder();
                if (l > 0) {
                    return 1;
                } else {
                    return -1;
                }
            }
            return a.sceneOrder() - b.sceneOrder();
        }).collect(Collectors.toList());
    }

    public ListWrap<BaseScoreResult> listTotalV3(String companyId, String taskUserId) {
        ComQB qb = ComQB.build(PerfEvalTotalLevelResultDo.class, "tr")
                .whereEq("company_id", companyId)
                .whereEq("taskUserId", taskUserId)
                .appendWhere("is_deleted='false'");
        List<BaseScoreResult> totalRs = domainDao.listAll(qb);
        return new ListWrap<>(totalRs).asMap(BaseScoreResult::getScorerId);
    }

    public ListWrap<PerfEvalTotalLevelResultDo> listTotal(String companyId, String taskUserId) {
        ComQB qb = ComQB.build(PerfEvalTotalLevelResultDo.class, "tr")
                .whereEq("company_id", companyId)
                .whereEq("taskUserId", taskUserId)
                .appendWhere("is_deleted='false'");
        List<PerfEvalTotalLevelResultDo> totalRs = domainDao.listAll(qb);
        ListWrap<PerfEvalTotalLevelResultDo> wrap = new ListWrap<>(totalRs).asMap(PerfEvalTotalLevelResultDo::getScorerId);
        return wrap;
    }

    @Override
    public List<DisplayEvalFlow> listScoreFlow(String empId, String empName, String taskId, EmpEvalRule evalRule) {
        TenantId companyId = evalRule.getCompanyId();
        String taskUserId = evalRule.getEmpEvalId();
        List<DisplayEvalFlow> flows = new ArrayList<>();
        List<String> scoreTypes = EvaluateAuditSceneEnum.finalNotSelfScoreTypes();
        List<FlowRater> AuditFlowRs = listAuditFlowRater(companyId, taskUserId, scoreTypes);
        PerfEvaluateTaskBaseDo taskBaseDo = evaluateTaskDao.getTaskById(new TaskId(taskId));
        if (Objects.nonNull(evalRule)) {
            if (!evalRule.isCustom() && evalRule.getS3SelfRater().isOpen()) {
                AuditFlowRs.add(new FlowRater(empId, empName, EvaluateAuditSceneEnum.SELF_SCORE.getScene()));
            } else {
                //自定义时自评
                List<PerfEvaluateTaskItemScoreRuleDo> ruleDoList = empEvalDao.listItemScoreRule(companyId.getId(), taskUserId);
                if (CollUtil.isNotEmpty(ruleDoList) && ruleDoList.stream().filter(rule -> rule.getSelfRater().isOpen()).collect(Collectors.toList()).size() > 0) {
                    AuditFlowRs.add(new FlowRater(empId, empName, EvaluateAuditSceneEnum.SELF_SCORE.getScene()));
                }
            }
        } else {
            PerfTemplEvaluate evaluateJson = JSONUtil.parseObj(taskBaseDo.getTemplEvaluateJson()).toBean(PerfTemplEvaluate.class);
            if (Objects.nonNull(evaluateJson) && evaluateJson.getSelfScoreFlag().equals(Boolean.TRUE.toString())) {
                AuditFlowRs.add(new FlowRater(empId, empName, EvaluateAuditSceneEnum.SELF_SCORE.getScene()));
            }
        }
        //定向评分
        List<EvalKpi> kpis = evalKpiDao.listEmpEvalKpiItem(companyId, taskUserId).getDatas();
        if (CollUtil.isNotEmpty(kpis)) {
            kpis.forEach(kpi -> {
                List<EmpStaff> staffs = kpi.getScorers(null, null).stream()
                        .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>
                                (Comparator.comparing(x -> x.getEmpId() + x.getEmpName()))), ArrayList::new));
                List<FlowRater> raters = Convert.toList(FlowRater.class, staffs);
                raters.forEach(r -> {
                    r.setScorerType(EvaluateAuditSceneEnum.ITEM_SCORE.getScene());
                });
                AuditFlowRs.addAll(raters);
            });
        }
        if (!AuditFlowRs.isEmpty()) {
            Map<String, FlowRater> flowMap = listResult(companyId, taskUserId, scoreTypes).getDatas().stream().filter(o -> Objects.nonNull(o.getTaskAuditId()))
                    .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>
                            (Comparator.comparing(x -> x.getTaskAuditId()))), ArrayList::new)).stream().collect(Collectors.toMap(FlowRater::getTaskAuditId, o -> o));
            for (String scoreType : EvaluateAuditSceneEnum.finalScoreTypes()) {
                DisplayEvalFlow auditFlow = new DisplayEvalFlow(scoreType);
                Map<String, List<FlowRater>> rsGroups = AuditFlowRs.stream().collect(Collectors.groupingBy(result -> result.getScorerType()));
                rsGroups.forEach((k, v) -> {
                    LevelNode flowNode = new LevelNode();
                    if (scoreType.equals(k)) {
                        List<FlowRater> rss = v;
                        for (FlowRater auditNode : rss) {
                            if (Objects.nonNull(auditNode.getTaskAuditId()) && flowMap.get(auditNode.getTaskAuditId()) != null) {
                                FlowRater flowRater = flowMap.get(auditNode.getTaskAuditId());
                                for (FlowRater rater : rss) {
                                    if (flowRater.getTaskAuditId().equals(rater.getTaskAuditId())) {
                                        rater.transferEmp(flowRater.getEmpId(), flowRater.getEmpName());
                                    }
                                }
                            }
                            flowNode.addAllSub(rss);
                            auditFlow.addLevel(flowNode);
                            Integer order = taskBaseDo.getScoreSortConf() == null ? 1 : taskBaseDo.getScoreSortConf().getSort(auditFlow.sceneAtSortType());
                            auditFlow.setNodeSort(order == 0 ? 60 : order);
                        }
                        flows.add(auditFlow);
                    }
                });
            }
            flows.sort(Comparator.comparing(DisplayEvalFlow::getNodeSort));
        }
        return flows;
    }


    private LevelNode buildEvalAudit(TenantId tenantId, ListWrap<FlowRater> auditGroup, EvalAudit auditNode) {
        LevelNode level = new LevelNode(auditNode.getApprovalOrder(), auditNode.getMultipleReviewersType());
        //有已经派发的result，优先从result中获取 调整原因：评分中发生转交后重置， a转给b、此时audit中还是a，result中是b 流程中应该展示b
        if (!auditGroup.isEmpty() && !auditGroup.groupGet(auditNode.getApprovalOrder() + "").isEmpty()) {
            List<FlowRater> ratersOfAudit = auditGroup.groupGet(auditNode.getApprovalOrder() + "");
            level.addAllSub(ratersOfAudit);
            level.computeStatus();
        } else {
            auditNode.prevDispatchRaters();
            level.waitScore();
            loadRaterEmpInfo(tenantId, level, auditNode.getSubNodes());
        }
        return level;

//        if (!auditNode.isDispatched()) {//未派发任务的结点
//            auditNode.prevDispatchRaters();
//            level.waitScore();
//            loadRaterEmpInfo(tenantId, level, auditNode.getSubNodes());
//        } else {//已派发任务的结点
//            List<FlowRater> ratersOfAudit = auditGroup.groupGet(auditNode.getApprovalOrder() + "");
//            level.addAllSub(ratersOfAudit);
//            level.computeStatus();
//        }
//        return level;
    }

    private void loadRaterEmpInfo(TenantId tenantId, LevelNode level, List<EvalScoreResult> results) {
        List<String> empIds = CollUtil.map(results, result -> result.getScorerId(), true);
        ComQB qb = ComQB.build(EmployeeBaseInfoDo.class, "e")
                .clearSelect().select("employee_id emp_id , name emp_name, status emp_status,avatar ")
                .setRsType(FlowRater.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereInReq("employee_id", empIds);
        List<FlowRater> raters = domainDao.listAll(qb);
        level.addAllSub(raters);
        //取出为-1的，空缺的
        List<EvalScoreResult> resultsFilter = CollUtil.filterNew(results,r -> Objects.equals(r.getScorerId(),"-1"));
        if (CollUtil.isNotEmpty(resultsFilter)) {
            List<FlowRater> raterList = new ArrayList<>();
            for (EvalScoreResult result : resultsFilter) {
                raterList.add(new FlowRater("-1","",result.getSkipType()));
            }
            level.addAllSub(raterList);
        }
        Map<String,EvalScoreResult> map = CollUtil.toMap(results,new HashMap<>(),r -> r.getScorerId());
        for (FlowRater rater : level.getRaters()) {
            EvalScoreResult result = map.get(rater.getEmpId());
            rater.setSkipType(result.getSkipType());
        }
    }

    public List<EvalAudit> listAuditNode(TenantId tenantId, String taskUserId, String scene) {
        ComQB qb = ComQB.build(TaskAuditDo.class, "au")
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEqReq("scene", scene)
                .whereEq("is_deleted", "false")
                .orderByAsc("approval_order");
        return domainDao.listAllDomain(qb, EvalAudit.class);
    }

    public List<FlowRater> listAuditFlowRater(TenantId tenantId, String taskUserId, List<String> scene) {
        //WAIT = "wait"; DOING = "doing"; FINISHED = "finished"
        ComQB qb = ComQB.build(TaskAuditDo.class, "a")
                .join(EmployeeBaseInfoDo.class, "e").appendOn("a.company_id = e.company_id and a.approver_info = e.employee_id")
                .clearSelect().setRsType(FlowRater.class)
                .select("a.created_time create_time, a.id as task_audit_id,a.multiple_reviewers_type")
                .select("e.avatar,e.employee_id emp_id, e.name emp_name ,e.status emp_status , a.scene as scorerType,a.approval_order")
                .whereEqReq("a.company_id", tenantId.getId())
                .whereEqReq("a.task_user_id", taskUserId)
                .whereIn("a.scene", scene)
                .appendWhere("a.is_deleted = 'false'");
        return domainDao.listAll(qb);
    }

    private ListWrap<EmpEvalScorerNode> listEmpEvalScorerNode(TenantId tenantId, String taskUserId) {
        ComQB qb = ComQB.build(EmpEvalScorerNodeDo.class, "sn")
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("is_deleted", "false")
                .orderByAsc("approval_order");
        List<EmpEvalScorerNode> scorerNodes = domainDao.listAllDomain(qb, EmpEvalScorerNode.class);
        return new ListWrap<>(scorerNodes).groupBy(EmpEvalScorerNode::getScorerType);
    }
    private ListWrap<EvalAudit> listScoringAuditNode(TenantId tenantId, String taskUserId) {
        ComQB qb = ComQB.build(TaskAuditDo.class, "au")
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("task_user_id", taskUserId)
                .whereIn("scene", AuditEnum.scoreScenes())
                .whereEq("is_deleted", "false")
                .orderByAsc("approval_order");
        List<EvalAudit> audits = domainDao.listAllDomain(qb, EvalAudit.class);
        return new ListWrap<>(audits).groupBy(evalAudit -> evalAudit.getScene());
    }

    public ListWrap<FlowRater> listResult(TenantId tenantId, String taskUserId, List<String> scenes) {
        //WAIT = "wait"; DOING = "doing"; FINISHED = "finished"
        ComQB qb = ComQB.build(PerfEvaluateTaskScoreResultDo.class, "sr")
                .leftJoin(EmployeeBaseInfoDo.class, "e").appendOn("sr.company_id = e.company_id and sr.scorer_id = e.employee_id")
                .clearSelect().setRsType(FlowRater.class)
                .select("sr.created_time create_time, ifnull(sr.task_audit_id,(select id from perf_evaluate_task_audit where company_id = sr.company_id and task_user_id = sr.task_user_id  and kpi_item_id = sr.kpi_item_id and scene = sr.scorer_type and approval_order = sr.approval_order and approver_info = sr.scorer_id and is_deleted = 'false' limit 1)) as task_audit_id,if(sr.audit_status is null,'doing','finished') audit_status")
                .select("sr.skip_type, sr.audit_status='pass' finish,  if(sr.audit_status='pass',sr.updated_time ,null) finish_time")
                .select("e.avatar,ifnull(e.employee_id,'-1') emp_id, ifnull(e.name,'空缺跳过') emp_name ,e.status emp_status , sr.scorer_type,sr.approval_order")
                .whereEqReq("sr.company_id", tenantId.getId())
                .whereEqReq("sr.task_user_id", taskUserId)
                .whereInReq("sr.scorer_type", scenes)
                .appendWhere("(sr.audit_status !='transferred' or sr.audit_status  is null)")
                .appendWhere("sr.is_deleted = 'false'");
        List<FlowRater> raters = domainDao.listAll(qb);

        //WAIT = "wait"; DOING = "doing"; FINISHED = "finished"
        ComQB tyQb = ComQB.build(PerfEvalTypeResultDo.class, "sr")
                .join(EmployeeBaseInfoDo.class, "e").appendOn("sr.company_id = e.company_id and sr.scorer_id = e.employee_id")
                .clearSelect().setRsType(FlowRater.class)
                .select("sr.created_time create_time, null task_audit_id,if(sr.audit_status is null,'doing','finished') audit_status")
                .select("sr.audit_status='pass' finish,  if(sr.audit_status='pass',sr.updated_time ,null) finish_time")
                .select("e.avatar,e.employee_id emp_id, e.name emp_name ,e.status emp_status , sr.scorer_type,sr.approval_order")
                .whereEqReq("sr.company_id", tenantId.getId())
                .whereEqReq("sr.task_user_id", taskUserId)
                .whereInReq("sr.scorer_type", scenes)
                .appendWhere("(sr.audit_status !='transferred' or sr.audit_status  is null)")
                .appendWhere("sr.is_deleted = 'false'");
        List<FlowRater> typeRaters = domainDao.listAll(tyQb);
        ListWrap<FlowRater> flowRaterListWrap = new ListWrap<>(raters);
        flowRaterListWrap.addAll(typeRaters);
        return flowRaterListWrap.groupBy(flowRater -> flowRater.getScorerType());
    }

    private ListWrap<FlowRater> listResult(TenantId tenantId, String taskUserId, String scene) {
        return listResult(tenantId, taskUserId, Arrays.asList(scene));
    }

    public ListWrap<FlowRater> listInviteMutual(String type,EvalUser evalUser, EmpEvalMerge evalRule) {
        //WAIT = "wait"; DOING = "doing"; FINISHED = "finished"
        List<RaterNode> raters = evalRule.getInviteMutualRater(evalUser.getEmpId()); //邀请互评的人
        if (CollUtil.isEmpty(raters)) {
            return new ListWrap<>();
        }
        ListWrap<RaterNode> raterNodeWrap = new ListWrap<>(raters).groupBy(RaterNode::getNode);
        List<FlowRater> allFlowRaters = new ArrayList<>();
        allFlowRaters.addAll(loadFlowRater(type, raterNodeWrap, evalUser.getEmpId(), evalUser.getEmpName(), evalUser.getAvatar()));
        allFlowRaters.addAll(loadFlowRater(type + "Audit", raterNodeWrap, evalUser.getEmpId(), evalUser.getEmpName(), evalUser.getAvatar()));
        ListWrap<FlowRater> flowRaterListWrap = new ListWrap<>(allFlowRaters);
        return flowRaterListWrap.groupBy(FlowRater::getScorerType);
    }

    private List<FlowRater> loadFlowRater(String type,  ListWrap<RaterNode> raterNodeWrap, String empId,
                               String empName, String avatar) {
        List<FlowRater> flowRaters = new ArrayList<>();
        List<RaterNode> raterNodes = raterNodeWrap.groupGet(type);
        if (CollUtil.isEmpty(raterNodes)) {
            return flowRaters;
        }
        for (RaterNode node : raterNodes) {
            if (CollUtil.isEmpty(node.getRaters())) {
                continue;
            }
            node.buildEmpRater(empId, empName, avatar);//构建邀请人是被考核信息
            for (Rater rater : node.getRaters()) {
                FlowRater flowRater = new FlowRater(rater.getEmpId(), rater.getEmpName(), type);
                flowRater.accpAvatar(rater.getAvatar());
                flowRaters.add(flowRater);
            }
        }
        if (CollUtil.isEmpty(flowRaters)) { //去重flowRaters 根据FlowRater 的 empId
            flowRaters.stream().collect(Collectors.collectingAndThen(Collectors.toMap(FlowRater::getEmpId,
                    fr -> fr, (existing, replacement) -> existing), map -> new ArrayList<>(map.values())));
        }
        return flowRaters;
    }

    public List<EvalAudit> listResetTaskAudit(String companyId, String taskId, String empId) {
        ComQB comQB = ComQB.build(TaskAuditDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_id", taskId)
                .whereEqReq("emp_id", empId)
                .whereEq("is_deleted", Boolean.FALSE.toString());
        List<EvalAudit> auditDos = domainDao.listAllDomain(comQB, EvalAudit.class);
        if (CollUtil.isEmpty(auditDos)) {
            return auditDos;
        }
        auditDos.forEach(audit -> {
            audit.setIsDeleted(Boolean.TRUE.toString());
        });
        return auditDos;
    }

    @Override
    public List<EvalAudit> listTaskAuditNodes(String companyId, String taskId, String scene, String taskUserId) {
        ComQB approvalOrderQb = ComQB.build(TaskAuditDo.class)
                .clearSelect().select("DISTINCT approval_order").setRsType(Integer.class)
                .whereEqReq("company_id", companyId)
                .whereIn("task_id", Arrays.asList(taskId.split(",")))
                .whereEqReq("scene", scene)
                .whereEq("is_deleted", "false")
                .whereEq("status","dispatched");
        List<Integer> approvalOrder = domainDao.listAll(approvalOrderQb);


        ComQB qb = ComQB.build(TaskAuditDo.class, "au")
                .leftJoin(PerfEvaluateTaskUserDo.class, "u")
                .appendOn("au.company_id = u.company_id and au.task_user_id = u.id")
                .whereEqReq("au.company_id", companyId)
                .whereIn("au.task_id", Arrays.asList(taskId.split(",")))
                .whereEqReq("au.scene", scene)
                .whereEq("au.is_deleted", "false")
                .whereNotIn("au.task_user_id", Arrays.asList(taskUserId.split(",")))
                .whereIn("u.task_status", TalentStatus.beforeEqResultsStage())
                .whereEq("u.is_deleted", "false")
                .whereIn("approval_order",approvalOrder)
                .groupBy("au.task_user_id")
                .orderByAsc("approval_order");
        return domainDao.listAllDomain(qb, EvalAudit.class);
    }

    @Override
    public ListWrap<EvalAudit> listTaskAuditNodeWrap(String companyId, String taskId, String scene, String opEmpId) {
        ComQB qb = ComQB.build(PerfEvaluateTaskUserDo.class, "tu")
                .leftJoin(TaskAuditDo.class, "ta")
                .appendOn("tu.company_id = ta.company_id and tu.id = ta.task_user_id")
                .leftJoin(PerfEvaluateTaskScoreResultDo.class,"sr")
                .appendOn("ta.company_id = sr.company_id AND ta.id = sr.task_audit_id and sr.is_deleted = 'false'")
                .clearSelect().select("ta.*,if(sr.id is null,'notEntered',if(sr.audit_status is null,'waitAudit','finishd')) as auditStatus")
                .select("if(sr.id is null,'notEntered',if(sr.audit_status is null,'waitAudit','finishd')) as auditStatus")
                .setRsType(EvalAudit.class)
                .whereEqReq("tu.company_id", companyId)
                .whereInReq("tu.task_id", Arrays.asList(taskId.split(",")))
                .whereEq("tu.is_deleted", "false")
                .whereIn("tu.task_status", TalentStatus.beforeEqResultsStage())
                .whereEqReq("ta.scene", scene)
                .appendWhere(" (if(sr.transfer_id is null, ta.approver_info = '"+opEmpId+"', sr.scorer_id = '"+opEmpId+"')) ")
                .whereEq("ta.is_deleted", "false")
                .orderByAsc("ta.approval_order");
        List<EvalAudit> auditList = domainDao.listAll(qb);
        if (CollUtil.isEmpty(auditList)) {
            return new ListWrap<>();
        }
        List<EvalAudit> filterList = new ArrayList<>();
        Map<String,List<EvalAudit>> evalAuditMap = auditList.stream().collect(Collectors.groupingBy(EvalAudit::getTaskId));
        evalAuditMap.forEach((k,v) ->{
            Map<String,List<EvalAudit>> filterMap = v.stream().collect(Collectors.groupingBy(EvalAudit::getTaskUserId)).entrySet().stream()
                    .filter(entry -> entry.getValue().stream().allMatch(audit -> audit.isNotEntere()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            filterMap.forEach((key,val) ->{
                filterList.add(val.stream()
                        .min(Comparator.comparingInt(EvalAudit::getApprovalOrder)).get());
            });
        });
        return new ListWrap<>(filterList).groupBy(EvalAudit::getTaskId);
    }

    private DisplayEvalFlow getInputAuditFlow(EvalUser user) {
        List<EvalKpi> kpis = user.getKpis();
        List<String> raterIds = user.inputRaterIds();
        if (CollUtil.isEmpty(kpis) || raterIds.isEmpty()) {
            return null;
        }

        ComQB comQB = ComQB.build(EmployeeBaseInfoDo.class)
                .clearSelect().setRsType(FlowRater.class)
                .select("avatar ,employee_id emp_id,name emp_name, status emp_status")
                .whereEq("company_id", user.getCompanyId().getId())
//                .whereEq("is_delete", Boolean.FALSE.toString()) //fix 不要过滤到离职人员，会导致显示异常
                .whereIn("employee_id", raterIds);
        List<FlowRater> flowRaters = domainDao.listAll(comQB);
        for (FlowRater flowRater : flowRaters) {
            //selfInput
            if (user.getEmpId().equals(flowRater.getEmpId())) {
                List<EvalKpi> selfInputKpis = kpis.stream().filter(kpi -> "exam".equals(kpi.getResultInputType())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(selfInputKpis)) {
                    EvalKpi evalKpi = selfInputKpis.get(0);
                    if (Objects.equals(FinishValueAuditStatusEnum.REJECT.getType(), evalKpi.getFinishValueAuditStatus())
                            && !evalKpi.isFinalSubmit()) {//完成值审核驳回了,未提交
                        flowRater.setFinish(false);
                        flowRater.setFinishTime(null);
                    } else {
                        flowRater.setFinish(evalKpi.getItemFinishValue() != null || evalKpi.getItemFinishValueText() != null ? true : false);
                        flowRater.setFinishTime(evalKpi.getUpdatedTime());
                    }
                    continue;
                }
            }
            List<String> inputType = Arrays.asList("user", "manager", "role");
            //otherInput
            List<EvalKpi> otherInputKpis = kpis.stream().filter(kpi ->
                    inputType.contains(kpi.getResultInputType())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(otherInputKpis)) {
                for (EvalKpi otherInputKpi : otherInputKpis) {
                    if (otherInputKpi.getResultInputEmpId().contains(flowRater.getEmpId())) {
                        if (Objects.equals(FinishValueAuditStatusEnum.REJECT.getType(), otherInputKpi.getFinishValueAuditStatus())
                                && !otherInputKpi.isFinalSubmit()) {
                            flowRater.setFinish(false);//完成值审核驳回了,未提交
                            flowRater.setFinishTime(null);
                        } else {
                            flowRater.setFinish(otherInputKpi.getItemFinishValue() != null || otherInputKpi.getItemFinishValueText() != null ? true : false);
                            flowRater.setFinishTime(otherInputKpi.getUpdatedTime());
                        }
                    }
                }
            }
        }
        LevelNode flowNode = new LevelNode(1, BusinessConstant.REVIEWERS_TYPE_OR);
        flowNode.addAllSub(flowRaters);
        DisplayEvalFlow auditFlow = new DisplayEvalFlow(EvaluateAuditSceneEnum.INPUT_PROGRESS.getScene());
        auditFlow.addLevel(flowNode);
        boolean atAter = TalentStatus.CONFIRMED.before(TalentStatus.statusOf(user.getTaskStatus()));
        auditFlow.afterSetStatus(atAter);
        return auditFlow;
    }

    @Override
    public List<DisplayEvalFlow> getConfirmedFlow(EvalUser evalUser, EmpEvalMerge empEvalMerge, boolean hasInputWaitNotify) {
        List<DisplayEvalFlow> flows = new ArrayList<>();
        //录入流程
        DisplayEvalFlow inputAuditFlow = getInputAuditFlow(evalUser);
        if (inputAuditFlow != null) {
            inputAuditFlow.matchDeadLineEndDate(evalUser.matchDeadLineEndDate(TalentStatus.CONFIRMED.getStatus()));
            if (hasInputWaitNotify) {//有录入待通知，更改流程状态为 待通知
                inputAuditFlow.buildInputWaitNotifyStatus();
            }
            flows.add(inputAuditFlow);
        }
        //变更流程
        String scene = EvaluateAuditSceneEnum.CHANGE_ITEM_AUDIT.getScene();
        List<FlowRater> auditRs = listResult(evalUser.getCompanyId(), evalUser.getId(), scene).getDatas();
        if (!auditRs.isEmpty()) {
            DisplayEvalFlow auditChangeFlow = new DisplayEvalFlow("changed_user");//变更责任人
            List<ModificationRecordPo> recordPos= kpiItemDao.listModificationRecord(evalUser.getCompanyId().getId(), evalUser.getId(),OperationLogSceneEnum.CHANGE_ITEM.getScene());
            //获取recordPos变更状态 status = changing
            recordPos = recordPos.stream().filter(recordPo -> Objects.equals(recordPo.getStatus(), "changing")).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(recordPos)) {
                auditChangeFlow.buildChangedUserFlowNode(recordPos.get(0).getEmployeeId(), recordPos.get(0).getCreatedUserName());
                auditChangeFlow.matchDeadLineEndDate(evalUser.matchDeadLineEndDate(TalentStatus.CONFIRMED.getStatus()));
                flows.add(auditChangeFlow);
            }

            DisplayEvalFlow auditFlow = new DisplayEvalFlow(scene);//变更审批人
            List<FlowRater> auditNodes = listAuditFlowRater(evalUser.getCompanyId(), evalUser.getId(), Collections.singletonList(scene));
            Map<String, List<FlowRater>> rsGroups = auditNodes.stream().collect(Collectors.groupingBy(FlowRater::getTaskAuditId));
            auditFlow.doNoRepeat(auditRs);//去重
            Map<String, FlowRater> flowMap = auditRs.stream().collect(Collectors.toMap(FlowRater::getTaskAuditId, o -> o));
            auditFlow.buildConfirmChangeAuditFlowNode(flowMap,rsGroups,auditNodes);
            flows.add(auditFlow);
        }
        //录入完成值审核
        String finishValueAuditScene = EvaluateAuditSceneEnum.FINISH_VALUE_AUDIT.getScene();
        List<FlowRater> AuditFlowRs = listAuditFlowRater(evalUser.getCompanyId(), evalUser.getId(), Arrays.asList(finishValueAuditScene, ","));
        if (!AuditFlowRs.isEmpty()) {
            DisplayEvalFlow auditFlow = new DisplayEvalFlow(finishValueAuditScene);
            Map<String, FlowRater> flowMap = listResult(evalUser.getCompanyId(), evalUser.getId(), finishValueAuditScene).getDatas().stream().collect(Collectors.toMap(FlowRater::getTaskAuditId, o -> o));
            Map<String, List<FlowRater>> rsGroups = AuditFlowRs.stream().collect(Collectors.groupingBy(FlowRater::getTaskAuditId));
            auditFlow.buildFinishValueAuditFlowNode(flowMap, rsGroups, AuditFlowRs);
            auditFlow.matchDeadLineEndDate(evalUser.matchDeadLineEndDate(TalentStatus.FINISH_VALUE_AUDIT.getStatus()));
            flows.add(auditFlow);
        }

        List<CompanyMsgCenter> msgCenters = centerDao.listMsgByScene(evalUser.getCompanyId(), evalUser.getId(),
                Arrays.asList(MsgSceneEnum.SET_MUTUAL_AUDIT.getType(), MsgSceneEnum.INVITE_PEER_AUDIT.getType(), MsgSceneEnum.INVITE_SUB_AUDIT.getType()));
        //邀请同级互评人责任人+审核责任人
        String peerType = "peer";
        ListWrap<FlowRater> peerInviteMutualRs = listInviteMutual(peerType, evalUser, empEvalMerge);
        if (!peerInviteMutualRs.isEmpty()) {
            DisplayEvalFlow inviteMutualFlow = new DisplayEvalFlow("invite_peer");//邀请同级互评人责任人
            boolean isSetInvite = inviteMutualFlow.buildInviteMutualFlowNode(peerInviteMutualRs, msgCenters, peerType);
            if (isSetInvite) {
                inviteMutualFlow.matchDeadLineEndDate(evalUser.matchDeadLineEndDate(TalentStatus.CONFIRMED.getStatus()));
                flows.add(inviteMutualFlow);
            }

            DisplayEvalFlow inviteMutualAduitFlow = new DisplayEvalFlow("invite_peer_audit");//审核责任人
            boolean isAudit = inviteMutualAduitFlow.buildInviteMutualAuditFlowNode(peerInviteMutualRs, msgCenters, peerType);
            if (isAudit) {
                inviteMutualAduitFlow.matchDeadLineEndDate(evalUser.matchDeadLineEndDate(TalentStatus.CONFIRMED.getStatus()));
                flows.add(inviteMutualAduitFlow);
            }
        }

        //邀请下级互评人责任人+审核责任人
        String subType = "sub";
        ListWrap<FlowRater> subInviteMutualRs = listInviteMutual(subType, evalUser, empEvalMerge);
        if (!subInviteMutualRs.isEmpty()) {
            DisplayEvalFlow inviteMutualFlow = new DisplayEvalFlow("invite_sub");//邀请下级互评人责任人
            boolean isSetInvite = inviteMutualFlow.buildInviteMutualFlowNode(subInviteMutualRs, msgCenters, subType);
            if (isSetInvite) {
                inviteMutualFlow.matchDeadLineEndDate(evalUser.matchDeadLineEndDate(TalentStatus.CONFIRMED.getStatus()));
                flows.add(inviteMutualFlow);
            }

            DisplayEvalFlow inviteMutualAduitFlow = new DisplayEvalFlow("invite_sub_audit");//审核责任人
            boolean isAudit = inviteMutualAduitFlow.buildInviteMutualAuditFlowNode(subInviteMutualRs, msgCenters, subType);
            if (isAudit) {
                inviteMutualAduitFlow.matchDeadLineEndDate(evalUser.matchDeadLineEndDate(TalentStatus.CONFIRMED.getStatus()));
                flows.add(inviteMutualAduitFlow);
            }
        }
        return flows;
    }

    @Override
    public List<DisplayEvalFlow> getInterviewResultFlow(EvalUser taskUser) {
        List<DisplayEvalFlow> auditFlows = new ArrayList<>();
        EvalTaskInterviewConf interviewConf = taskUser.initEvalTaskInterview();
        if (Objects.isNull(interviewConf) || CollUtil.isEmpty(interviewConf.getInterviewExcuteEmpIds())) {
            return auditFlows;
        }

        //执行人
        List<String> interviewExcuteEmpIds = interviewConf.getInterviewExcuteEmpIds();
        EvalTaskInterview interviewPo = evalTaskInterviewDao.getEvalTaskInterview(taskUser.getCompanyId().getId(), taskUser.getId());

        List<String> notFinishExcuter = Objects.nonNull(interviewPo) && StrUtil.isNotBlank(interviewPo.getExcuterId()) ?
                new ArrayList<>() : new ArrayList<>(interviewExcuteEmpIds);
        Map<String, KpiEmp> ratersMap = empDao.listByEmpAsMap(taskUser.getCompanyId(), interviewExcuteEmpIds);
        DisplayEvalFlow auditFlow = new DisplayEvalFlow(AuditEnum.FINAL_RESULT_INTERVIEW.getScene(),
                Objects.nonNull(interviewPo) && StrUtil.isNotBlank(interviewPo.getExcuterId()) ?
                        "finished" : "wait", "or", 1, ratersMap, interviewExcuteEmpIds, notFinishExcuter);
        auditFlows.add(auditFlow);

        int order = 2;
        //录入人，如果添加面谈存在录入人，需要增加到阶段责任人展示
        if (Objects.nonNull(interviewPo) && StrUtil.isNotBlank(interviewPo.getRecorderId())) {
            List<String> inputEmpIdNotFinishs = new ArrayList<>();
            //查询被考核人的的待办信息
            List<CompanyMsgCenter> list = evalTaskInterviewDao.listToDoInterviewEmpId(taskUser.getCompanyId().getId(),
                    Collections.singletonList(taskUser.getId()), Arrays.asList(MsgSceneEnum.TASK_INTERVIEW_INPUT.getType(),
                            MsgSceneEnum.TASK_INTERVIEW_TRANSFER_INPUT.getType()));
            if (CollUtil.isNotEmpty(list)) {
                inputEmpIdNotFinishs.add(interviewPo.getRecorderId());
            }

            List<String> inputEmpId = Collections.singletonList(interviewPo.getRecorderId());
            Map<String, KpiEmp> ratersInputMap = empDao.listByEmpAsMap(taskUser.getCompanyId(), inputEmpId);
            DisplayEvalFlow auditFlowInput = new DisplayEvalFlow(AuditEnum.FINAL_RESULT_INTERVIEW_INPUT.getScene(),
                    CollUtil.isEmpty(inputEmpIdNotFinishs) ? "finished" : "wait", "or", order, ratersInputMap,
                    inputEmpId, inputEmpIdNotFinishs);
            auditFlows.add(auditFlowInput);
            order++;
        }

        //确认人 未开启面谈确认，则不添加这个节点
        if (!interviewConf.getIsOpenConfirm()) {
            return auditFlows;
        }
        ResultInterviewConfirmFlow confirmFlow = confirmFlowDao.getResultInterviewConfirmFlow(taskUser.getCompanyId(), taskUser.getId());
        DisplayEvalFlow displayconfirmFlow = new DisplayEvalFlow(AuditEnum.FINAL_RESULT_INTERVIEW_CONFIRM.getScene());//确认责任人
        if (Objects.nonNull(confirmFlow) && CollUtil.isNotEmpty(confirmFlow.getConfirmFlowNodes())) {
            displayconfirmFlow.buildInterviewConfirmFlowNode(null, confirmFlow.getConfirmFlowNodes());
        } else {
            displayconfirmFlow.buildInterviewConfirmFlowNode(taskUser.getEmpEvalRule().getInterviewConf().getInterviewConfirmConf().getInterviewConfirmInfo(), null);
        }
        auditFlows.add(displayconfirmFlow);
        return auditFlows;
    }

    @Override
    public void batchInsert(List<EvalAudit> evalAudits) {
        evalAudits.stream().forEach(evalAudit -> {
            evalAudit.initOnNew(domainDao.nextLongAsStr(auditSeq));
            domainDao.add(TaskAuditDo.class, evalAudit);
        });
        return;
    }

    @Override
    public void batchDelete(String companyId, String taskUserId, List<String> kpiItemIds, String opEmpId, String scene) {
        UpdateBuilder up = UpdateBuilder.build(TaskAuditDo.class)
                .set("updated_user", opEmpId)
                .set("is_deleted", Boolean.TRUE.toString())
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", taskUserId)
                .whereEq("scene", scene);
        if (!kpiItemIds.isEmpty()) {
            up.whereIn("kpi_item_id", kpiItemIds);
        }
        domainDao.update(up);
    }

    @Override
    public void updateFinishedValueAudit(String companyId, String taskUserId) {
        ComQB qb = ComQB.build(TaskAuditDo.class, "a")
                .clearSelect().select("a.*").setRsType(TaskAuditDo.class)
                .whereEqReq("a.company_id", companyId)
                .whereEqReq("a.task_user_id", taskUserId)
                .whereEqReq("a.scene", AuditEnum.FINISH_VALUE_AUDIT.getScene())
                .appendWhere("a.is_deleted = 'false'");
        List<TaskAuditDo> auditDoList = domainDao.listAll(qb);
        if (CollUtil.isEmpty(auditDoList)) {
            return;
        }
        List<TaskAuditDo> filterList = auditDoList.stream().filter(a -> StringUtils.isBlank(a.getKpiItemId())).collect(Collectors.toList());
        boolean flag = auditDoList.stream().filter(a -> StringUtils.isNotBlank(a.getKpiItemId()) && a.getApprovalOrder() == 1).collect(Collectors.toList()).size() > 0;
        boolean flag2 = auditDoList.stream().filter(a -> StringUtils.isBlank(a.getKpiItemId()) && a.getApprovalOrder() == 1).collect(Collectors.toList()).size() > 0;
        if (flag && flag2) {
            for (TaskAuditDo auditDo : filterList) {
                auditDo.setApprovalOrder(auditDo.getApprovalOrder() +1 );
                domainDao.update(auditDo);
            }
        }else {
            flag = auditDoList.stream().filter(a -> a.getApprovalOrder() == 1).collect(Collectors.toList()).size() == 0;
            if (flag) {
                for (TaskAuditDo auditDo : filterList) {
                    auditDo.setApprovalOrder(auditDo.getApprovalOrder() - 1 );
                    domainDao.update(auditDo);
                }
            }
        }
    }

    @Override
    public void updateFinalResultAuditOrder(String companyId, String id, Integer approvalOrder) {
        UpdateBuilder audit = UpdateBuilder.build(TaskAuditDo.class)
                .set(" approval_order ", approvalOrder)
                .whereEqReq("company_id",companyId)
                .whereEqReq("id",id);
        domainDao.update(audit);
    }

    @Override
    public void batchResetItemAuditStatus(RejectFinishedValueDmSvc dmSvc, String status) {
        ComQB comQB = ComQB.build(TaskAuditDo.class, "aut")
                .whereEqReq("company_id", dmSvc.getTenantId().getId())
                .whereEqReq("taskUserId", dmSvc.getTaskUser().getId())
                .whereEqReq("scene", OperationLogSceneEnum.FINISH_VALUE_AUDIT.getScene())
                .whereEq("is_deleted", "false")
                .orderByAsc("approval_order");
        if (StringUtils.isNotBlank(status)) {
            comQB.whereEq("status", status);
        }
        if (CollUtil.isEmpty(dmSvc.getKpiItemIds())) {
            comQB.appendWhere("kpi_item_id is null");
        } else {
            comQB.appendWhere("(kpi_item_id in " + StringTool.getInStr(dmSvc.getKpiItemIds()) + " or kpi_item_id is null)" );
        }
        List<EvalAudit> auditList = domainDao.listAllDomain(comQB, EvalAudit.class);
        if (CollUtil.isEmpty(auditList)) {
            return;
        }
        List<String> auditIds = auditList.stream().map(EvalAudit::getId).collect(Collectors.toList());
        UpdateBuilder updateBuilder = UpdateBuilder.build(TaskAuditDo.class)
                .set("status",null)
                .whereIn("id", auditIds)
                .whereEq("is_deleted", "false");
        domainDao.update(updateBuilder);

        // 重置score_result表  驳回这里审批流程置为移除，提交会重新分发完成值审批流
        UpdateBuilder scoreResultBuilder = UpdateBuilder.build(PerfEvaluateTaskScoreResultDo.class)
                //.set("audit_status", null)
                .set("is_deleted", "true")
                .whereIn("task_audit_id", auditIds)
                .whereEq("scorerType",  OperationLogSceneEnum.FINISH_VALUE_AUDIT.getScene())
                .whereEq("is_deleted", "false");
        domainDao.update(scoreResultBuilder);
        // 删除指标关联的完成值审核的待办
        List<String> empIds = auditList.stream().map(EvalAudit::getApproverInfo).collect(Collectors.toList());
        dmSvc.getOwnerEmpIds().addAll(empIds);
    }

    @Override
    public void updateApproverInfo(String companyId, String empEvalId, String fromEmpId, String toEmpId, Integer approvalOrder, String scene) {
        UpdateBuilder builder = UpdateBuilder.build(TaskAuditDo.class)
                .set("approver_info", toEmpId)
                .whereEqReq("company_id", companyId)
                .whereEqReq("task_user_id", empEvalId)
                .whereEqReq("scene", scene)
                .whereEqReq("approval_order", approvalOrder)
                .whereEqReq("approver_info", fromEmpId)
                .whereEq("is_deleted", "false");
        domainDao.update(builder);

//        //转交校准人考核规则中的校准人一并修改
//        UpdateBuilder upEmpEvalRule = UpdateBuilder.build(EmpEvalRule.class)
//                .set(sceneMatcher(scene), JSON.toJSONString(flowConf))
//                .whereEqReq("company_id", companyId)
//                .whereEqReq("emp_eval_id", empEvalId)
//                .whereEq("is_deleted", "false");
//        domainDao.update(upEmpEvalRule);

        if (EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene().equals(scene)) {
            //校准转交时需要更新result_audit_flow_node_rater中的audit_emp_id
            UpdateBuilder upAuditRater = UpdateBuilder.build(ResultAuditFlowNodeRater.class)
                    .set("audit_emp_id", toEmpId)
                    .whereEqReq("company_id", companyId)
                    .whereEqReq("level", approvalOrder)
                    .whereEqReq("task_user_id", empEvalId)
                    .whereEqReq("audit_emp_id", fromEmpId)
                    .whereEq("is_deleted", "false");
            domainDao.update(upAuditRater);
        }
    }

    private String sceneMatcher(String scene) {
        if (EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene().equals(scene)) {
            return "audit_result";
        }
        if (EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT.getScene().equals(scene)) {
            return "confirm_task";
        }
        return null;
    }
}
