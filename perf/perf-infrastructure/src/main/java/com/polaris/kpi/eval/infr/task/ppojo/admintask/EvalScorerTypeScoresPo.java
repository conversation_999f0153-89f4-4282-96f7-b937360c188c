package com.polaris.kpi.eval.infr.task.ppojo.admintask;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.eval.domain.task.entity.EmpEvalScorerNode;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.infr.task.ppojo.admintask
 * @Author: suxiaoqiu
 * @CreateTime: 2025-03-15  16:57
 * @Description: 评分详情-各个评分环节评分人总分
 * @Version: 2.0
 */
@Data
@NoArgsConstructor
@Slf4j
public class EvalScorerTypeScoresPo {
    private String scoreType;//评分环节
    private Integer nodeOrder;//环节顺序
    private List<EvalScorerNodeScorePo> scorerNodes;

    public EvalScorerTypeScoresPo(String scoreType) {
        this.scoreType = scoreType;
    }

    public void acceptScorerNodes(List<EmpEvalScorerNode> scorerNodes) {
        List<EvalScorerNodeScorePo> scorerNodePos = new ArrayList<>();
        scorerNodes.forEach(node -> {
            EvalScorerNodeScorePo scorerNodeScorePo = new EvalScorerNodeScorePo(scoreType
                    , node.getStatus()
                    , node.getScorerNodeScore()
                    , node.getApprovalOrder()
                    , node.getScorerId()
                    , node.getScorerName()
                    , node.getScorerAvatar()
                    , node.getScoreLevel()
                    , node.getEstimateLevel()
                    , node.getTotalScore()
                    , node.getScoreWeight());
            scorerNodeScorePo.acceptAuditStatus(node.isPassed(), node.getTransferType());
            // 如果是标记为通过，需要校验是否有分数和等级，不然是无效评价，不展示【可能是或签的】
//            if (node.isPassed() && "pass".equals(scorerNodeScorePo.getAuditStatus())) {
//                // 跳过无效评价节点
//                return;
//            }
            scorerNodePos.add(scorerNodeScorePo);
        });
        this.scorerNodes = scorerNodePos;
    }

}