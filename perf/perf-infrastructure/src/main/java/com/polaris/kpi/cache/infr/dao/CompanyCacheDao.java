package com.polaris.kpi.cache.infr.dao;

import com.polaris.kpi.cache.domain.entity.CompanyCacheInfo;
import com.polaris.kpi.eval.infr.task.ppojo.CompanyCacheInfoDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.lufei.ibatis.builder.ComQB;
import org.lufei.ibatis.builder.QueryBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class CompanyCacheDao {
    @Autowired
    private DomainDaoImpl domainDao;

    public CompanyCacheInfo queryCacheInfo(CompanyCacheInfo model){
        QueryBuilder queryBuilder = QueryBuilder.build(CompanyCacheInfoDo.class)
                .whereEq("company_id", model.getCompanyId())
                .whereEq("link_id", model.getLinkId())
                .whereEq("business_scene", model.getBusinessScene())
                .whereEq("cache_key", model.getCacheKey())
                .whereEq("is_deleted", "false")
                .whereEq("created_user", model.getCreatedUser())
                .orderByDesc("created_time");
        List<CompanyCacheInfo> list = domainDao.listAllDomain(queryBuilder,CompanyCacheInfo.class);
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }

    public CompanyCacheInfo queryCacheInfo(String companyId, String scene, String linkId){
        ComQB comQB = ComQB.build(CompanyCacheInfoDo.class)
                .whereEqReq("company_id", companyId)
                .whereEqReq("business_scene", scene)
                .whereEqReq("link_id", linkId)
                .whereEqReq("is_deleted", Boolean.FALSE.toString());
        return domainDao.findDomain(comQB, CompanyCacheInfo.class);
    }
}
