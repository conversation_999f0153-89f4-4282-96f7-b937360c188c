package com.polaris.kpi.eval.infr.statics.ppojo;

import com.polaris.kpi.report.domain.entity.CrossLevelCount;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/22 11:39
 */
@Data
@AllArgsConstructor
public class CrossLevelAnalysisPo {

    private Integer upCount;
    private Integer downCount;
    private Boolean upCounted = false; //0-未统计
    private Boolean downCounted = false; //0-未统计
    private Date upCountedTime;
    private Date downCountedTime;
    private List<EmpPerf> upEmpPerfs;
    private List<EmpPerf> downEmpPerfs;


    public CrossLevelAnalysisPo(Integer upCount, Integer downCount) {
        this.upCount = upCount;
        this.downCount = downCount;
    }

    public void accCrossLevelCount(CrossLevelCount crossLevelCount) {
        this.upCounted = crossLevelCount.getUpCountState() != 0;
        this.downCounted = crossLevelCount.getDownCountState() != 0;
        this.upCountedTime = crossLevelCount.getUpCountedTime();
        this.downCountedTime = crossLevelCount.getDownCountedTime();
    }
}
