package com.polaris.kpi.eval.infr.statics.ppojo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/1/21 10:07
 */
@Data
public class SinglePerfRankStaticsPo {

    private String taskName; //任务名称
    private String taskUserId;
    private String empId; //被考核人ID
    private String empName; //被考核人姓名
    private String avatar; //被考核人头像
    @JSONField(serialize = false)
    private String orgId; //组织id
    private String orgName; //组织名称
    private String evalOrgId; //被考核组织id
    private String evalOrgName; //被考核组织名称
    private BigDecimal finalScore; //考核分
    private String evaluationLevel; //绩效等级
    private String post; //岗位
    private String perfCoefficient; //绩效系数
    private Integer rank;

    /**
     * 考核时员工所在部门名字以 | 连接的路径信息，可展示员工在考核时所属的部门层级关系。
     */
    private String atOrgNamePath;

    /**
     * 考核时员工所在部门 codePath 以 | 连接的路径信息，与部门名称路径对应，可能用于系统内部的部门编码标识。
     */
    @JSONField(serialize = false)
    private String atOrgCodePath;

}
