package com.polaris.kpi.notice.ppojo;

import com.polaris.kpi.common.infr.DelData;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Ckey;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.notice.ppojo
 * @Author: lufei
 * @CreateTime: 2022-06-23  16:52
 * @Description: TODO
 * @Version: 1.0
 * {
 * "id":"格式化的key",
 * "companyId":"公司id0",
 * "createdTime":"",
 * "updatedTime":"",
 * "version":"版本号",
 * }
 ***/
@Getter
@NoArgsConstructor
public class TipReadDo extends DelData {
    @Ckey
    private String tipId;//格式化的key
    @Ckey
    private String empId;//员工id

    public TipReadDo(String tipId, String companyId, String empId) {
        this.tipId = tipId;
        this.companyId = companyId;
        this.empId = empId;
    }
}
