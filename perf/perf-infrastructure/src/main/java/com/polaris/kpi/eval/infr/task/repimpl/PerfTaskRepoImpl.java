package com.polaris.kpi.eval.infr.task.repimpl;

import cn.com.polaris.kpi.eval.CycleValue;
import cn.com.polaris.kpi.temp.CycleId;
import cn.com.polaris.kpi.temp.CycleStatusEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.perf.www.common.constant.BusinessConstant;
import com.perf.www.common.utils.string.StringTool;
import com.perf.www.model.perftmp.PerfTemplEvaluateAuditModel;
import com.perf.www.model.task.PerfEvaluateTaskAuditInitModel;
import com.perf.www.model.task.PerfEvaluateTaskAuditModel;
import com.perf.www.model.task.PerfEvaluateTaskCoachModel;
import com.polaris.kpi.common.KpiI18NException;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.repo.EvaluateTaskRepo;
import com.polaris.kpi.eval.domain.temp.entity.PerfTemplEvaluateAudit;
import com.polaris.kpi.eval.infr.task.dao.EvaluateTaskDao;
import com.polaris.kpi.eval.infr.task.ppojo.*;
import com.polaris.kpi.eval.infr.temp.ppojo.ScheduleTempDo;
import com.polaris.kpi.org.domain.dept.type.TaskId;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import com.quick.common.util.date.DateTimeUtils;
import org.apache.commons.lang3.StringUtils;
import org.lufei.ibatis.builder.*;
import org.lufei.ibatis.common.DomainToDataBuilder;
import org.lufei.ibatis.common.data.ToDataBuilder;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lufei
 * @date 2022/2/25 4:53 下午
 */
@Component
public class PerfTaskRepoImpl implements EvaluateTaskRepo {
    @Resource
    private DomainDaoImpl domainDao;
    private static final String cycleSeq = "cycle_seq";
    private static final String taskUserSeq = "perf_evaluate_task_user";
    private static final String adminTaskSeq = "perf_evaluate_task_base";
    private static final String auditSeq = "perf_evaluate_task_audit";
    private static final String kpiItemSeq = "perf_evaluate_task_kpi";
    private static final String evalCommonSeq = "perf_evaluate";
    @Autowired
    private EvaluateTaskDao taskDao;

    @Override
    public void saveCycleTask(CycleEval domain) {
        if (Objects.nonNull(domain.getTemplEvaluateJson())) {
            List<PerfTemplEvaluateAudit> finalAuditList = domain.getTemplEvaluateJson().getFinalAuditList();
            if (CollUtil.isNotEmpty(finalAuditList)) {
                LoggerFactory.getLogger(getClass()).info("任务保存是审核人的数量：" + finalAuditList.size());
            }
        }
        if (domain.isNew()) {
            domain.initOnNew(domainDao.nextLongAsStr(adminTaskSeq));
            domainDao.add(TaskBaseWriteDo.class, domain);
            saveInitTaskAudit(domain.getId(), domain.getTemplBaseId(), domain.getCreatedUser().getId());
            return;
        }
        domainDao.update(TaskBaseWriteDo.class, domain);
        saveInitTaskAudit(domain.getId(), domain.getTemplBaseId(), domain.getCreatedUser().getId());
    }

    private void saveInitTaskAudit(String taskId, String tempId, String createdUser) {
        deleteInitAudit(taskId, createdUser);
        List<PerfTemplEvaluateAuditModel> allTempAuditList = queryAuditBySceneAndTempId(tempId, null);
        List<PerfEvaluateTaskAuditInitModel> initAuditList = com.perf.www.common.utils.bean.Convert.convertListOnlyMatch(allTempAuditList, PerfEvaluateTaskAuditInitModel.class);
        batchSaveAuditInit(initAuditList, taskId, createdUser);
    }

    public List<PerfTemplEvaluateAuditModel> queryAuditBySceneAndTempId(String tempBaseId, String scene) {
        QueryBuilder queryBuilder = QueryBuilder.build(PerfTemplEvaluateAuditModel.class).whereEq("is_deleted", "false")
                .whereEq("temp_base_id", tempBaseId).whereEq("scene", scene).orderByAsc("approval_order");
        return domainDao.listAll(queryBuilder);
    }

    public void batchSaveAuditInit(List<PerfEvaluateTaskAuditInitModel> list, String taskId, String createdUser) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        StringBuilder sql = new StringBuilder("INSERT INTO perf_evaluate_task_audit_init (id,company_id,temp_base_id,task_id,emp_id,kpi_item_id,scene,approval_order,approver_type,approver_info," +
                "superior_score_weight,multiple_reviewers_type,transfer_flag,vacancy_approver_type,vacancy_approver_info,score_rule,created_user,created_time,is_deleted,is_default,kpi_type_id,modify_flag) VALUES ");
        for (PerfEvaluateTaskAuditInitModel auditModel : list) {
            sql.append(String.format("('%s','%s','%s','%s','%s','%s','%s',%s,'%s','%s',%s,'%s','%s','%s','%s','%s','%s','%s','false','%s','%s','%s')",
                    UUID.randomUUID().toString(), auditModel.getCompanyId(), auditModel.getTempBaseId(), taskId, StringTool.handleNullToEmpty(auditModel.getEmpId()), StringTool.handleNullToEmpty(auditModel.getKpiItemId()),
                    auditModel.getScene(), auditModel.getApprovalOrder(), auditModel.getApproverType(), auditModel.getApproverInfo(),
                    auditModel.getSuperiorScoreWeight(), StringTool.handleNullToEmpty(auditModel.getMultipleReviewersType()), StringTool.handleNullToEmpty(auditModel.getTransferFlag()), StringTool.handleNullToEmpty(auditModel.getVacancyApproverType()), StringTool.handleNullToEmpty(auditModel.getVacancyApproverInfo()),
                    StringTool.handleNullToEmpty(auditModel.getScoreRule()),
                    createdUser, DateTimeUtils.now2StrDateTime(), StringTool.handleNullToEmpty(auditModel.getIsDefault()), StringTool.handleNullToEmpty(auditModel.getKpiTypeId()), StringTool.handleNullToEmpty(auditModel.getModifyFlag()))).append(",");
        }
        String sqlStr = StringUtils.removeEnd(sql.toString(), ",");
        NativeSQLBuilder sqlBuilder = NativeSQLBuilder.build(PerfEvaluateTaskAuditModel.class).setSql(sqlStr);
        domainDao.nativeExecute(sqlBuilder);
    }


    public void deleteInitAudit(String taskId, String createdUser) {
        if (StringUtils.isEmpty(taskId)) {
            return;
        }
        UpdateBuilder updateBuilder = UpdateBuilder.build("perf_evaluate_task_audit_init").set("is_deleted", "true")
                .set("updated_user", createdUser).whereEq("task_id", taskId).whereEq("is_deleted", "false");
        domainDao.update(updateBuilder);
    }

    @Override
    public Cycle adminTaskAsCycle(TenantId tenantId, String taskId) {
        CycleEval cycleEval = getCycleEval(tenantId, new TaskId(taskId));
        Cycle cycle = new Cycle(tenantId, cycleEval.getCycleStartDate(), cycleEval.getCycleEndDate());
        cycle.setScoreRuleMod(4);
        return cycle;
    }

    @Override
    public CycleEval getCycleEval(TenantId tenantId, TaskId id) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskBaseDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("id", id.getId())
                .limit(0, 1);
        PerfEvaluateTaskBaseDo data = this.domainDao.findOne(comQB);
        if (data == null) {
            return null;
        }
        CycleEval domain = Convert.convert(CycleEval.class, data);
        data.appendJsonTo(domain);
        data.baseUpdate(domain);
        return domain;
    }

    @Override
    public CycleEval getMergeCycleEval(TenantId tenantId, String taskUserId) {
        return taskDao.getMergeTaskBase(tenantId, taskUserId);
    }

    //@Override
    //public CycleEval getTaskWithAdminRule(TenantId tenantId, String taskUserId) {
    //    return taskDao.getTaskWithAdminRule(tenantId, taskUserId);
    //}

    @Override
    public Cycle createCycleIfNeed(TenantId tenantId, CycleValue cycleValue, String empId) {
        ComQB comQB = ComQB.build(CycleDo.class)
                .whereEq("company_id", tenantId.getId())
                //whereEq("year", cycleValue.getYear())
                //whereEq("type", cycleValue.getType())
                //whereEq("value", cycleValue.getValue())
                .whereEq("name", cycleValue.getName())
                .whereEq("cycle_status", CycleStatusEnum.NORMAL.getType());
        Cycle cycleDomain = domainDao.findDomain(comQB, Cycle.class);
        if (cycleDomain != null) {
            throw new KpiI18NException("cycle.nameExist", "周期名称重复:" + cycleValue.getName());
        }
        cycleDomain = new Cycle(tenantId, cycleValue, 0);
        cycleDomain.initOnNew(domainDao.nextLongAsStr(cycleSeq));
        CycleDo data = new ToDataBuilder<>(cycleDomain, CycleDo.class).data();
        data.setName(cycleDomain.getCycleName());
        data.setCreatedUser(empId);
        domainDao.add(data);
        return cycleDomain;
    }

    @Override
    public Cycle getCycle(TenantId tenantId, CycleId id) {
        ComQB comQB = ComQB.build(CycleDo.class)
                .whereEq("company_id", tenantId.getId())
                .whereEq("id", id.getId());
        final Cycle domain = domainDao.findDomain(comQB, Cycle.class);
        return domain;
    }

    //@Override
    //public void addTalentEval(EvalUser taskUser) {
    //    if (!taskUser.isNew()) {
    //        return;
    //    }
    //    final Supplier<String> auditIdGen = () -> domainDao.nextLongAsStr(auditSeq);
    //    final Supplier<String> evalCommonGen = () -> domainDao.nextLongAsStr(evalCommonSeq);
    //    final Supplier<String> kpiItemGen = () -> domainDao.nextLongAsStr(kpiItemSeq);
    //    taskUser.initId(domainDao.nextLongAsStr(taskUserSeq));
    //    final TaskUserBuilder builder = new TaskUserBuilder(taskUser, kpiItemGen, auditIdGen, evalCommonGen);
    //    builder.build();
    //    domainDao.saveBatch(builder.getKpis());
    //    domainDao.saveBatch(builder.getOkrTypes());
    //    domainDao.saveBatch(builder.allAuditDatas());
    //    if (CollUtil.isNotEmpty(builder.getCustomItemRules())) {
    //        domainDao.saveBatch(builder.getCustomItemRules());
    //    }
    //    if (CollUtil.isNotEmpty(builder.getFormulaFields())) {
    //        domainDao.saveBatch(builder.getFormulaFields());
    //    }
    //    domainDao.save(builder.getScoreRule());
    //    domainDao.add(PerfEvaluateTaskUserDo.class, taskUser);
    //}

    @Override
    public void batchSaveScoreResult(List<EvalScoreResult> auditTasks) {
        if (CollUtil.isEmpty(auditTasks)) {
            return;
        }
        final List<PerfEvaluateTaskScoreResultDo> rsDatas = auditTasks.stream().map(resultDomain -> {
            return new ToDataBuilder<>(resultDomain, PerfEvaluateTaskScoreResultDo.class).data();
        }).collect(Collectors.toList());
        domainDao.saveBatch(rsDatas);
    }

    @Override
    public List<EvalKpi> listTaskKpis(TenantId companyId, String taskUserId) {
        final ComQB comQB = ComQB.build(PerfEvaluateTaskKpiDo.class, "k")
                .whereEq("companyId", companyId.getId())
                .whereEq("taskUserId", taskUserId);
        return domainDao.listAllDomain(comQB, EvalKpi.class);
    }

    @Override
    public void delTaskByIds(Set<String> delTaskIds, String updatedEmp) {
        final UpdateBuilder up = UpdateBuilder.build(PerfEvaluateTaskBaseDo.class)
                .set("is_deleted", Boolean.TRUE.toString())
                .set("updated_user", updatedEmp)
                .whereIn("id", delTaskIds);
        domainDao.update(up);
    }

    public void updateCycleEmpCnt(TenantId tenantId, CycleId cycleId) {
        ComQB comQB = ComQB.buildDiff(TaskRefUser.class, "v_cycle_emp")
                .clearSelect().select("count(1)").setRsType(Integer.class)
                .whereEq("company_id", tenantId.getId())
                .whereEq("cycle_id", cycleId.getId());
        Integer cycleCnt = domainDao.findOne(comQB);
        UpdateBuilder cycle = UpdateBuilder.build(CycleDo.class)
                .set("eval_cnt", cycleCnt)
                .whereEq("company_id", tenantId.getId())
                .whereEq("id", cycleId.getId());
        domainDao.update(cycle);
    }

    @Override
    public void markPublishEnd(TenantId tenantId, String taskId) {
        UpdateBuilder up = UpdateBuilder.build(TaskBaseWriteDo.class)
                .set("taskStatus", BusinessConstant.PUBLISHED)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("id", taskId);
        domainDao.update(up);
    }

    @Override
    public String delTaskIfNeed(TenantId tenantId, List<String> taskIds, String taskUserIds) {
        for (String taskId : taskIds) {
            ComQB comQB = ComQB.build(PerfEvaluateTaskUserDo.class)
                    .clearSelect().select("count(1)").setRsType(Integer.class)
                    .whereEq("is_deleted", Boolean.FALSE.toString())
                    .whereEq("company_id", tenantId.getId())
                    .whereEq("task_id", taskId)
                    .whereNotIn("id", Arrays.asList(taskUserIds.split(",")));
            Integer taskEmpCnt = domainDao.findOne(comQB);
            if (taskEmpCnt > 0) {
                continue;
            }
            UpdateBuilder update = UpdateBuilder.build(PerfEvaluateTaskBaseDo.class)
                    .set("is_deleted", Boolean.TRUE.toString())
                    .whereEq("company_id", tenantId.getId())
                    .whereEq("id", taskId);
            domainDao.update(update);
        }
        ComQB cycle = ComQB.build(PerfEvaluateTaskBaseDo.class)
                .clearSelect().select("cycle_id").setRsType(String.class)
                .whereEq("company_id", tenantId.getId())
                .whereEq("id", taskIds.get(0));
        String cycleId = domainDao.findOne(cycle);
        return cycleId;
    }

    @Override
    public void modifyMyAuditResult(TenantId tenantId, String taskBaseId, String empId, EmpId opEmpId) {
        UpdateBuilder update = UpdateBuilder.build(PerfEvaluateTaskScoreResultDo.class)
                .set("audit_status", "pass")
                .whereEq("company_id", tenantId.getId())
                .whereEq("task_id", taskBaseId)
                .whereEq("emp_id", empId)
                .whereEq("scorer_id", opEmpId);
        domainDao.update(update);
    }

    @Override
    public void addRecord(PerfModificationRecord record) {
        domainDao.save(new ToDataBuilder<>(record, PerfModificationRecordDo.class).data());
    }

    @Override
    public void delSchedule(Long batchNo) {
        DeleteBuilder del = DeleteBuilder.build(ScheduleTempDo.class)
                .whereEqReq("batch_no", batchNo);
        domainDao.delete(del);
    }

    // region 员工 考核任务中的 面谈辅导，评论，操作日志相关操作

    private static String evalTaskCoachSeq = "eval_task_coach";
    private static String evalTaskCommentSeq = "eval_task_comment";

    @Override
    public PerfEvaluateTaskCoach getTaskCoach(TenantId tenantId, String id) {
        ComQB comQB = ComQB.build(PerfEvaluateTaskCoachDo.class)
                .whereEqReq("company_id", tenantId.getId())
                .whereEqReq("id", id);
        return this.domainDao.findDomain(comQB, PerfEvaluateTaskCoach.class);
    }

    @Override
    public String saveTaskCoach(PerfEvaluateTaskCoach coach) {

        PerfEvaluateTaskCoachDo coachDo = new PerfEvaluateTaskCoachDo();
        new DomainToDataBuilder<>(coach, coachDo).build();

        if (coach.isNew()) {
            coachDo.setId(this.domainDao.nextLongAsStr(evalCommonSeq));
            this.domainDao.save(coachDo);
        } else {
            this.domainDao.update(coachDo);
        }

        return coachDo.getId();
    }

    @Override
    public void readTaskCoach(String id) {
        UpdateBuilder update = UpdateBuilder.build(PerfEvaluateTaskCoachModel.class)
                .set("is_read", Boolean.TRUE.toString())
                .whereEq("id", id);
        this.domainDao.update(update);
    }

    @Override
    public Integer readTaskDiscuss(String companyId, String taskUserId, String kpiItemId) {
        UpdateBuilder update = UpdateBuilder.build(PerfEvaluateTaskDiscussDo.class)
                .set("is_read", 1)
                .whereEq("company_id", companyId)
                .whereEq("task_user_id", taskUserId)
                .whereEq("kpi_item_id", kpiItemId)
                .whereEq("is_read", 0);
        return this.domainDao.update(update);
    }

    @Override
    public String saveTaskDiscuss(PerfEvaluateTaskDiscuss discuss) {
        PerfEvaluateTaskDiscussDo discussDo = new PerfEvaluateTaskDiscussDo();
        new DomainToDataBuilder<>(discuss, discussDo).build();
        if (discuss.isNew()) {
            discussDo.setId(this.domainDao.nextLongAsStr(evalTaskCommentSeq));
            this.domainDao.save(discussDo);
        } else {
            this.domainDao.update(discussDo);
        }
        return discussDo.getId();
    }

    // endregion
}
