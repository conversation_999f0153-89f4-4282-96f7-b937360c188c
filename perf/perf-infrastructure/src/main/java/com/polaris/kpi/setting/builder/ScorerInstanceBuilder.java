package com.polaris.kpi.setting.builder;

import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Supplier;
import com.polaris.kpi.eval.domain.task.entity.EmpEvalScorer;
import com.polaris.kpi.eval.domain.task.entity.EmpEvalScorerNode;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.empeval.ChainDispatchRs;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.entity.empeval.PerfEvalTypeResult;
import com.polaris.kpi.eval.infr.task.ppojo.TaskUserScorerDo;
import com.polaris.kpi.setting.domain.entity.TaskUserScorer;
import lombok.Getter;
import lombok.Setter;
import org.lufei.ibatis.common.data.ToDataBuilder;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/19 11:33
 */
@Getter
public class ScorerInstanceBuilder {

    private String companyId;
    private String empId;
    private Supplier<String> instanceIdGen;
    private EvalUser taskUser;
    private EmpEvalMerge evalMerge;
    private ChainDispatchRs chainDispatchRs;
    @Setter
    private String instanceId;

    private List<TaskUserScorerDo> instances = new ArrayList<>();

    public ScorerInstanceBuilder() {
    }

    public ScorerInstanceBuilder(String companyId, String empId, Supplier<String> instanceIdGen, ChainDispatchRs chainDispatchRs, EvalUser taskUser, EmpEvalMerge evalMerge) {
        this.companyId = companyId;
        this.empId = empId;
        this.instanceIdGen = instanceIdGen;
        this.taskUser = taskUser;
        this.evalMerge = evalMerge;
        this.chainDispatchRs = chainDispatchRs;
    }

    public void build() {
        if (Objects.isNull(this.taskUser)) {
            return;
        }
        if (CollUtil.isEmpty(this.chainDispatchRs.getItemResults().getDatas()) && CollUtil.isEmpty(this.chainDispatchRs.getTypeResults())) {
            return;
        }

        if (CollectionUtil.isNotEmpty(this.chainDispatchRs.getItemResults().getDatas())) { //转成评分人对象
            for (EvalScoreResult data : this.chainDispatchRs.getItemResults().getDatas()) {
                //排除自评
                if (SubScoreNodeEnum.SELF_SCORE.getScene().equals(data.getScorerType())){
                    continue;
                }
                TaskUserScorer scorer = new TaskUserScorer();
                scorer.populateTaskUserInfo(taskUser);
                scorer.populateScorerInfo(data.getScorerType(), data.getApprovalOrder(), data.getScorerId() ,evalMerge);
                scorer.initStatus();
                scorer.setId(instanceIdGen.get());
                TaskUserScorerDo scorerDo = new ToDataBuilder<>(scorer, TaskUserScorerDo.class).data();
                instances.add(scorerDo);
            }
        }
        if (CollectionUtil.isNotEmpty(this.chainDispatchRs.getTypeResults())) {
            for (PerfEvalTypeResult typeResult : this.chainDispatchRs.getTypeResults()) {
                //排除自评
                if (SubScoreNodeEnum.SELF_SCORE.getScene().equals(typeResult.getScorerType())){
                    continue;
                }
                TaskUserScorer scorer = new TaskUserScorer();
                scorer.populateTaskUserInfo(taskUser);
                scorer.populateScorerInfo(typeResult.getScorerType(), typeResult.getApprovalOrder(), typeResult.getScorerId(), evalMerge);
                scorer.initStatus();
                scorer.setId(instanceIdGen.get());
                TaskUserScorerDo scorerDo = new ToDataBuilder<>(scorer, TaskUserScorerDo.class).data();
                instances.add(scorerDo);
            }
        }

        List<TaskUserScorerDo> uniqueInstances = instances.stream()
                .collect(Collectors.toMap(
                        TaskUserScorerDo::getScorerId, // 去重依据字段
                        Function.identity(),        // 值保留对象本身
                        (existing, replacement) -> existing, // 冲突时保留第一个元素
                        LinkedHashMap::new           // 保持插入顺序
                ))
                .values()                        // 提取去重后的值
                .stream()
                .collect(Collectors.toList());

        instances = uniqueInstances;

    }

    public void buildV3() {
        if (Objects.isNull(this.taskUser)) {
            return;
        }
        if (CollUtil.isEmpty(this.chainDispatchRs.getEmpEvalScorers())) {
            return;
        }

        List<EmpEvalScorer>  empEvalScorers = this.chainDispatchRs.getEmpEvalScorers();
        for (EmpEvalScorer empEvalScorer : empEvalScorers) {
            if (CollUtil.isEmpty(empEvalScorer.getScorerNodes())){
                continue;
            }
            for (EmpEvalScorerNode scorerNode : empEvalScorer.getScorerNodes()){
                String scorerType = scorerNode.getScorerType();
                //排除自评
                if (StrUtil.equals(scorerType, SubScoreNodeEnum.SELF_SCORE.getScene())){
                    continue;
                }
                TaskUserScorer scorer = new TaskUserScorer();
                scorer.populateTaskUserInfo(taskUser);
                scorer.populateScorerInfo(scorerType, scorerNode.getApprovalOrder(), scorerNode.getScorerId(),evalMerge);
                scorer.initStatus();
                scorer.setId(instanceIdGen.get());
                TaskUserScorerDo scorerDo = new ToDataBuilder<>(scorer, TaskUserScorerDo.class).data();
                instances.add(scorerDo);
            }
        }

        List<TaskUserScorerDo> uniqueInstances = instances.stream()
                .collect(Collectors.toMap(
                        TaskUserScorerDo::getScorerId, // 去重依据字段
                        Function.identity(),        // 值保留对象本身
                        (existing, replacement) -> existing, // 冲突时保留第一个元素
                        LinkedHashMap::new           // 保持插入顺序
                ))
                .values()                        // 提取去重后的值
                .stream()
                .collect(Collectors.toList());

        instances = uniqueInstances;
    }
}
