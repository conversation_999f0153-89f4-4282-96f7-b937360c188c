package com.perf.www.controller.eventhandle.kpi;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.event.EventHandler;
import com.perf.www.controller.BaseEventHander;
import com.polaris.kpi.ExecutorEnum;
import com.polaris.kpi.eval.app.task.appsvc.ScoreStageAppSvc;
import com.polaris.kpi.eval.app.task.appsvc.SubmitScoreAppSvc;
import com.polaris.kpi.eval.app.task.dto.sumit.SubmitScoreCmd;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.entity.empeval.SubmitRaterScore;
import com.polaris.kpi.eval.domain.task.entity.empeval.TransferOrSkipRaters;
import com.polaris.kpi.eval.domain.task.event.talent.scoring.*;
import com.polaris.kpi.eval.domain.task.repo.TaskUserRepo;
import com.polaris.kpi.eval.domain.task.type.EvalScoreSummary;
import com.polaris.sdk.type.TenantId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.perf.www.controller.eventhandle.kpi
 * @Author: lufei
 * @CreateTime: 2022-05-11  20:31
 * @Description: 1.0版本的考核评分阶段事件注册包装器
 * @Version: 1.0
 */
public class WrapperScoringHandler {
    //评分环节结束事件中控器
    @Component
    @EventHandler
    public static class ScoreNodeEndWrapper extends BaseEventHander<ScoreNodeEnd> {
        @Autowired
        private SubmitScoreAppSvc submitScoreAppSvc;
        @Autowired
        private ScoreStageAppSvc scoreStageAppSvc;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(ScoreNodeEnd event) {
            return super.execute(event);
        }

        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(ScoreNodeEnd event) {
            //旧的任务流程
            if (event.getCycleEval() != null) {
                if (event.getCycleEval().isCustom()) {
                    submitScoreAppSvc.handleCustomScoreNodeEnd(event);
                    return;
                }
                submitScoreAppSvc.handleScoreNodeEnd(event);
                return;
            }
        }
    }

    //评分层中的上级评分串行子流程事件
    @Component
    @EventHandler
    public static class ScoreNodeOfChainEndWrapper extends BaseEventHander<ScoreNodeOfChainEnd> {
        @Autowired
        private ScoreStageAppSvc scoreStageAppSvc;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(ScoreNodeOfChainEnd event) {
            return super.execute(event);
        }
        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(ScoreNodeOfChainEnd event) {
            event.startAny();
            scoreStageAppSvc.handleScoreNodeOfChainEndV3(event);
        }
    }

    @Component
    @EventHandler
    public static class TransferScoreSubmitWrapper extends BaseEventHander<TransferScoreSubmit>{
        @Autowired
        private ScoreStageAppSvc scoreStageAppSvc;
        @Autowired
        private TaskUserRepo userRepo;
        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(TransferScoreSubmit event) {
            return super.execute(event);
        }
        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(TransferScoreSubmit event) {
            event.startAny();
            TransferOrSkipRaters transferOrSkipRaters = event.getTransferOrSkipRaters();
            EmpEvalMerge evalMerge = event.getEvalMerge();
            List<SubmitScoreCmd> cmds = this.bulidScoreSubmit(transferOrSkipRaters,evalMerge);

            if (CollUtil.isNotEmpty(cmds)){
                for (SubmitScoreCmd cmd : cmds){
                    scoreStageAppSvc.submitAnyWrap(cmd);
                }
            }
        }

        private List<SubmitScoreCmd> bulidScoreSubmit(TransferOrSkipRaters transferOrSkipRaters, EmpEvalMerge evalMerge) {
            Map<String, EvalScoreResult> itemScoreRes = transferOrSkipRaters.getItemScoreList()
                    .stream().collect(Collectors.toMap(EvalScoreResult::getKpiItemId, Function.identity(), (a ,b) -> a));

            List<EvalScoreResult> scoreResults = transferOrSkipRaters.getItemScoreList();
            List<String> resultIds = scoreResults.stream().map(s -> s.getId()).collect(Collectors.toList());
            List<EvalScoreResult> totalEvalRs = evalMerge.getTotalEvalRs();
            totalEvalRs = totalEvalRs.stream()
                    .filter(s -> !resultIds.contains(s.getId()) &&
                            transferOrSkipRaters.getScorerType().equals(transferOrSkipRaters.getScorerType()) &&
                            s.getScore() == null && s.getScorerId().equals(transferOrSkipRaters.getScorerId())).collect(Collectors.toList());

            if (CollUtil.isNotEmpty(totalEvalRs)){
                Map<Integer, List<EvalScoreResult>> orderResultMap = totalEvalRs
                        .stream().collect(Collectors.groupingBy(EvalScoreResult::getApprovalOrder));
                List<EvalScoreResult> itemScoreList;
                List<EvalScoreSummary> scoreSummarys;
                List<SubmitScoreCmd> submitScoreCmds = new ArrayList<>();
                for (Integer approvalOrder : orderResultMap.keySet()){
                    itemScoreList = new ArrayList<>();
                    scoreSummarys = new ArrayList<>();
                    List<EvalScoreResult> res = orderResultMap.get(approvalOrder);
                    EvalScoreResult evalScoreResult;
                    for (EvalScoreResult rs : res){
                        evalScoreResult = new EvalScoreResult();
                        evalScoreResult.setId(rs.getId());
                        evalScoreResult.setKpiTypeId(rs.getKpiTypeId());
                        evalScoreResult.setKpiItemId(rs.getKpiItemId());
                        evalScoreResult.setApprovalOrder(rs.getApprovalOrder());
                        evalScoreResult.setScore(itemScoreRes.get(rs.getKpiItemId()).getScore());
                        evalScoreResult.setScoreLevel(itemScoreRes.get(rs.getKpiItemId()).getScoreLevel());
                        evalScoreResult.setVetoFlag(itemScoreRes.get(rs.getKpiItemId()).getVetoFlag());
                        evalScoreResult.setScorerType(rs.getScorerType());
                        evalScoreResult.setScorerId(transferOrSkipRaters.getScorerId());
                        itemScoreList.add(evalScoreResult);
                    }
                    EvalScoreSummary scoreSummary = new EvalScoreSummary();
                    scoreSummary.setCompanyId(transferOrSkipRaters.getTenantId().getId());
                    scoreSummary.setTaskUserId(transferOrSkipRaters.getTaskUserId());
                    scoreSummary.setScoreType(transferOrSkipRaters.getScorerType());
                    scoreSummarys.add(scoreSummary);
                    SubmitRaterScore submitRaterScore = new SubmitRaterScore();
                    submitRaterScore.setTaskUserId(transferOrSkipRaters.getTaskUserId());
                    submitRaterScore.setIsReCommit(false);
                    submitRaterScore.setItemScoreList(itemScoreList);
                    submitRaterScore.setScoreSummarys(scoreSummarys);
                    submitRaterScore.setScorerType(transferOrSkipRaters.getScorerType());
                    submitRaterScore.setCompanyId(transferOrSkipRaters.getTenantId());
                    SubmitScoreCmd submitScoreCmd = new SubmitScoreCmd();
                    BeanUtils.copyProperties(submitRaterScore, submitScoreCmd);
                    submitScoreCmd.setWasTransferRater(true);
                    submitScoreCmd.setWasAddLog(false);
                    submitScoreCmds.add(submitScoreCmd);
                }
                return submitScoreCmds;
            }
            return null;
        }
    }

    //评分层级结束
    @Component
    @EventHandler
    public static class ChainNodeEndWrapper extends BaseEventHander<ChainNodeEnd> {
        @Autowired
        private ScoreStageAppSvc scoreStageAppSvc;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(ChainNodeEnd event) {
            return super.execute(event);
        }

        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(ChainNodeEnd event) {
            event.startAny();
           // scoreStageAppSvc.handleChainNodeEnd(event);
            scoreStageAppSvc.handleChainNodeEndV3(event);
        }
    }


    @Component
    @EventHandler
    public static class SelfScoringStageStartWrapper extends BaseEventHander<SelfNodeStart> {
        @Autowired
        private SubmitScoreAppSvc hander;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(SelfNodeStart event) {
            return super.execute(event);
        }

        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(SelfNodeStart event) {
            hander.handleSelfNodeStart(event);
        }
    }

    @Component
    @EventHandler
    public static class PeerScoringStageStartWrapper extends BaseEventHander<PeerNodeStart> {
        @Autowired
        private SubmitScoreAppSvc hander;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(PeerNodeStart event) {
            return super.execute(event);
        }
        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(PeerNodeStart event) {
            hander.handlePeerNodeStart(event);
        }
    }


    //@Component
    //@EventHandler
    //public static class MuNodeStartWrapper extends BaseEventHander<MuNodeStart> {
    //    @Autowired
    //    private SubmitScoreAppSvc hander;
    //
    //    public ExecutorService getExecutor() {
    //        return hightExecutor;
    //    }
    //
    //    @Override
    //    public Response execute(MuNodeStart event) {
    //        return super.execute(event);
    //    }
    //
    //    @Override
    //    public void handerEvent(MuNodeStart event) {
    //        hander.handleMuPeerNodeStart(event);
    //    }
    //}


    @Component
    @EventHandler
    public static class AppointScoringStageStartWrapper extends BaseEventHander<ItemNodeStart> {
        @Autowired
        private SubmitScoreAppSvc hander;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(ItemNodeStart event) {
            return super.execute(event);
        }
        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(ItemNodeStart event) {
            hander.handleItemNodeStart(event);
        }
    }


    @Component
    @EventHandler
    public static class SupScoringStageStartWrapper extends BaseEventHander<SuperiorNodeStart> {
        @Autowired
        private SubmitScoreAppSvc hander;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(SuperiorNodeStart event) {
            return super.execute(event);
        }
        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(SuperiorNodeStart event) {
            hander.handleSuperiorNodeStart(event);
        }
    }

    @Component
    @EventHandler
    public static class RaterScoreNodeEndWrapper extends BaseEventHander<RaterScoreNodeEnd> {
        @Autowired
        private SubmitScoreAppSvc submitScoreAppSvc;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(RaterScoreNodeEnd event) {
            return super.execute(event);
        }
        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(RaterScoreNodeEnd event) {
            submitScoreAppSvc.handleRaterScoreNodeEnd(event);
        }
    }


}
