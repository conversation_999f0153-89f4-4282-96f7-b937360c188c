package com.perf.www.controller.eventhandle.kpi;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.event.EventHandler;
import com.perf.www.controller.BaseEventHander;
import com.perf.www.controller.kpi.handler.KpiItemUpdateFinishedValueHandler;
import com.perf.www.controller.kpi.handler.RejectFinishValueSubmitEventHandler;
import com.perf.www.controller.task.hander.*;
import com.polaris.kpi.ExecutorEnum;
import com.polaris.kpi.eval.app.cycle.appsvc.CycleEvalAppSvc;
import com.polaris.kpi.eval.app.task.appsvc.EmpEvalAppSvc;
import com.polaris.kpi.eval.app.task.dto.eval.MarkPublishCmd;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.event.*;
import com.polaris.kpi.eval.domain.task.event.admineval.TerminateEmpEvalEvnet;
import com.polaris.kpi.eval.domain.task.event.talent.PublishToEmpEvent;
import com.polaris.kpi.eval.domain.task.event.talent.TalentEvalConfirmedEvent;
import com.polaris.kpi.eval.domain.task.event.talent.exeing.BatchRejectValuesumbitedEvent;
import com.polaris.kpi.eval.domain.temp.event.NewEmpTempChangedEvent;
import com.polaris.kpi.eval.domain.temp.event.NewEmpTempDeletedEvent;
import com.polaris.kpi.eval.domain.temp.event.TempChangedEvent;
import com.polaris.kpi.event.eval.TalentEvalConfirmedEventHandle;
import com.polaris.sdk.type.TenantId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR> oldlie
 * @date 2022/1/13 6:28 下午
 */
public class WrapperHandler {


    @Component
    @EventHandler
    public static class PublishToEmpEventWrapper extends BaseEventHander<PublishToEmpEvent> {
        @Autowired
        private EmpEvalAppSvc empEvalAppSvc;

        public ExecutorService getExecutor() {
            return middleExecutor;
        }

        @Override
        public Response execute(PublishToEmpEvent event) {
            return super.execute(event);
        }

        @EventListener
        @Async(ExecutorEnum.low)
        @Override
        public void handerEvent(PublishToEmpEvent event) {
            EvalUser taskUser = event.getTaskUser();
//            List<String> taskUserList = Arrays.asList(taskUser.getId());
//            taskUserApi.publicationTaskResult(taskUser.getEmpId(), taskUserList, taskUser.getCompanyId().getId(), true);
            MarkPublishCmd cmd = new MarkPublishCmd();
            cmd.accOp(taskUser.getCompanyId().getId(), taskUser.getCreatedUser(), "");
            cmd.setTaskUserIds(Arrays.asList(taskUser.getId()));
            empEvalAppSvc.markPublish(cmd);
        }
    }

    @Component
    @EventHandler
    public static class NewEmpTmpCreatedEventWrapper extends BaseEventHander<NewEmpTempChangedEvent> {

        @Autowired
        private TempChangedHandler handle;

        public ExecutorService getExecutor() {
            return middleExecutor;
        }

        @Override
        public Response execute(NewEmpTempChangedEvent event) {
            return super.execute(event);
        }

        @EventListener
        @Async(ExecutorEnum.low)
        @Override
        public void handerEvent(NewEmpTempChangedEvent event) {
            handle.handleNewEmpCreated(event);
        }
    }

    @Component
    @EventHandler
    public static class NewEmpTempDeletedEventWrapper extends BaseEventHander<NewEmpTempDeletedEvent> {

        @Autowired
        private TempChangedHandler handle;

        public ExecutorService getExecutor() {
            return middleExecutor;
        }

        @Override
        public Response execute(NewEmpTempDeletedEvent event) {
            return super.execute(event);
        }
        @EventListener
        @Async(ExecutorEnum.low)
        @Override
        public void handerEvent(NewEmpTempDeletedEvent event) {
            handle.handleNewEmpTempDeleted(event);
        }
    }

    @Component
    @EventHandler
    public static class TempChangedEventWrapper extends BaseEventHander<TempChangedEvent> {

        @Autowired
        private TempChangedHandler handle;

        public ExecutorService getExecutor() {
            return middleExecutor;
        }

        @Override
        public Response execute(TempChangedEvent event) {
            return super.execute(event);
        }
        @EventListener
        @Async(ExecutorEnum.low)
        @Override
        public void handerEvent(TempChangedEvent event) {
            handle.processTempChangedEvent(event);
        }
    }


//    @Component
//    @EventHandler
//    public static class TalentEvalConfirmedEventWrapper extends BaseEventHander<TalentEvalConfirmedEvent> {
//
//        @Autowired
//        private TalentEvalConfirmedEventHandle handle;
//
//        public ExecutorService getExecutor() {
//            return hightExecutor;
//        }
//
//        @Override
//        public Response execute(TalentEvalConfirmedEvent event) {
//            return super.execute(event);
//        }
//        @EventListener
//        @Async(ExecutorEnum.high)
//        @Override
//        public void handerEvent(TalentEvalConfirmedEvent event) {
//            handle.handle(event);
//        }
//    }

    @Component
    @EventHandler //发起考核过程结束
    public static class CycleEvalCreateEndWrapper extends BaseEventHander<CycleEvalCreateEndEvent> {
        @Autowired
        private OnbardingTaskHandler hander;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(CycleEvalCreateEndEvent event) {
            return super.execute(event);
        }
        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(CycleEvalCreateEndEvent event) {
            hander.handCycleEvalCreateEnd(event);
        }
    }


    @Component
    @EventHandler
    public static class CycleCreatedWrapper extends BaseEventHander<CycleCreated> {
        @Autowired
        private CycleHander hander;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(CycleCreated event) {
            return super.execute(event);
        }
        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(CycleCreated event) {
            hander.handCycleCreated(event);
        }
    }


    @Component
    @EventHandler
    public static class CycleEvalCreatedEventHanderWrapper extends BaseEventHander<CycleEvalCreatedEvent> {

        @Autowired
        private TaskEvalHander hander;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(CycleEvalCreatedEvent event) {
            return super.execute(event);
        }
        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(CycleEvalCreatedEvent event) {
            event.start();
            hander.handerCycleEvalCreated(event);
        }
    }

    @Component
    @EventHandler
    public static class RejectFinishValueItemSubmitEventWrapper extends BaseEventHander<RejectFinishValueItemSubmitEvent> {
        @Autowired
        private RejectFinishValueSubmitEventHandler hander;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(RejectFinishValueItemSubmitEvent event) {
            return super.execute(event);
        }
        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(RejectFinishValueItemSubmitEvent event) {
            event.start();
            hander.rejectFinishedValueSubmitHandler(event);
        }
    }


    @Component
    @EventHandler
    public static class BatchRejectValuesumbitedEventWrapper extends BaseEventHander<BatchRejectValuesumbitedEvent> {
        @Autowired
        private RejectFinishValueSubmitEventHandler hander;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(BatchRejectValuesumbitedEvent event) {
            return super.execute(event);
        }
        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(BatchRejectValuesumbitedEvent event) {
            event.start();
            for (RejectFinishValueItemSubmitEvent rejectSubmitedValue : event.getRejectSubmitedValues()) {
                try {
                    hander.rejectFinishedValueSubmitHandler(rejectSubmitedValue);
                } catch (Exception e){
                    logger.error(e.getMessage(), e);
                }
            }
        }
    }

    @Component
    @EventHandler
    public static class KpiItemUpdateFinishedValueEventWrapper extends BaseEventHander<KpiItemUpdateFinishedValueEvent> {
        @Autowired
        private KpiItemUpdateFinishedValueHandler hander;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(KpiItemUpdateFinishedValueEvent event) {
            return super.execute(event);
        }
        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(KpiItemUpdateFinishedValueEvent event) {
            event.start();
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            if (CollUtil.isNotEmpty(event.getTaskUserIds())) {
                hander.batchEnterScoreHandler(event);
                return;
            }
            hander.enterScoreHandler(event);
        }
    }

    @Component
    @EventHandler
    public static class AddEmpGradeRuleEventWrapper extends BaseEventHander<AddEmpGradeRuleEvent> {
        @Autowired
        private AddEmpGradeRuleEventHandler hander;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(AddEmpGradeRuleEvent event) {
            return super.execute(event);
        }
        @EventListener
        @Async(ExecutorEnum.high)
        public void handerEvent(AddEmpGradeRuleEvent event) {
            hander.addEmpGradeRuleHandler(event);
        }
    }

    @Component
    @EventHandler
    public static class DelTaskEmpEventWrapper extends BaseEventHander<UpdateTaskEmpCntEvent> {
        @Autowired
        private UpdateTaskEmpCntEventHandler hander;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(UpdateTaskEmpCntEvent event) {
            return super.execute(event);
        }
        @EventListener
        @Async(ExecutorEnum.high)
        public void handerEvent(UpdateTaskEmpCntEvent event) {
            if ("del".equals(event.getType())) {
                hander.delTaskEmpEventHandler(event);
            }
            if ("add".equals(event.getType())) {
                hander.addTaskEmpEventHandler(event);
            }
            if ("delTask".equals(event.getType())) {
                hander.delTask(event);
            }
        }
    }

    //@Component
    //@EventHandler
    //public static class BatchCreatedTaskWrapper extends BaseEventHander<BatchCreatedTaskEvent> {
    //    @Autowired
    //    private BatchCreatedTaskHandler hander;
    //
    //    public ExecutorService getExecutor() {
    //        return hightExecutor;
    //    }
    //
    //    @Override
    //    public Response execute(BatchCreatedTaskEvent event) {
    //        return super.execute(event);
    //    }
    //
    //    public void handerEvent(BatchCreatedTaskEvent event) {
    //        hander.batchCreatedTaskHandler(event);
    //    }
    //}

    @Component
    @EventHandler
    public static class BatchSetMutualScoreEmpWrapper extends BaseEventHander<BatchSetMutualScoreEmpEvent> {
        @Autowired
        private BatchSetMutualScoreEmpHandler hander;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(BatchSetMutualScoreEmpEvent event) {
            return super.execute(event);
        }
        @EventListener
        @Async(ExecutorEnum.high)
        public void handerEvent(BatchSetMutualScoreEmpEvent event) {
            hander.batchSetMutualScoreEmpHandler(event);
        }
    }

    //@Component
    //@EventHandler
    //public static class AddTaskEmpWrapper extends BaseEventHander<AddEmpToTaskEvent> {
    //    @Autowired
    //    private AddEmpToTaskHandler hander;
    //
    //    public ExecutorService getExecutor() {
    //        return hightExecutor;
    //    }
    //
    //    @Override
    //    public Response execute(AddEmpToTaskEvent event) {
    //        return super.execute(event);
    //    }
    //
    //    public void handerEvent(AddEmpToTaskEvent event) {
    //        hander.addEmpToTask(event);
    //    }
    //}

    @Component
    @EventHandler
    public static class SendTodoForInputOnScoreWrapper extends BaseEventHander<SendTodoForInputOnScoreEvent> {
        @Autowired
        private EmpEvalAppSvc empEvalAppSvc;

        public ExecutorService getExecutor() {
            return lowExecutor;
        }

        @Override
        public Response execute(SendTodoForInputOnScoreEvent event) {
            return super.execute(event);
        }
        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(SendTodoForInputOnScoreEvent event) {
            empEvalAppSvc.sendInputOnScoreIfNeed(event);
        }
    }


    @Component
    @EventHandler
    public static class TerminateEmpEvalEvnetWrapper extends BaseEventHander<TerminateEmpEvalEvnet> {
        @Autowired
        private EmpEvalAppSvc empEvalAppSvc;
        @Autowired
        private CycleEvalAppSvc cycleEvalAppSvc;


        public ExecutorService getExecutor() {
            return lowExecutor;
        }

        @Override
        public Response execute(TerminateEmpEvalEvnet event) {
            return super.execute(event);
        }
        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(TerminateEmpEvalEvnet event) {
            cycleEvalAppSvc.createLevelOnDel(new TenantId(event.getCompanyId()), event.getTaskId(), event.getTaskUserId());
            empEvalAppSvc.handerTerminateEmpEval(event);
        }
    }
}
