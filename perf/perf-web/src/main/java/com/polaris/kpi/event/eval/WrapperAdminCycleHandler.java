package com.polaris.kpi.event.eval;

import cn.com.polaris.kpi.eval.BusinessPlanItem;
import cn.com.polaris.kpi.temp.TempId;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.event.EventHandler;
import com.perf.www.controller.BaseEventHander;
import com.polaris.kpi.ExecutorEnum;
import com.polaris.kpi.ask.domain.acl.AskEvalAcl;
import com.polaris.kpi.eval.app.cycle.appsvc.CycleEvalAppSvc;
import com.polaris.kpi.eval.app.stage.appsvc.CopyStageAppSvc;
import com.polaris.kpi.eval.app.task.appsvc.AdminTaskAppSvc;
import com.polaris.kpi.eval.app.task.appsvc.EmpEvalAppSvc;
import com.polaris.kpi.eval.app.task.appsvc.EmpTableAppSvc;
import com.polaris.kpi.eval.app.task.appsvc.ResultAuditFlowAppSvc;
import com.polaris.kpi.eval.app.task.dto.eval.ApplyTempCmd;
import com.polaris.kpi.eval.app.task.dto.eval.ScoreStageRuleCmd;
import com.polaris.kpi.eval.app.temp.appsvc.StdTempAppSvc;
import com.polaris.kpi.eval.domain.group.entity.EvalGroup;
import com.polaris.kpi.eval.domain.group.entity.EvalGroupEmp;
import com.polaris.kpi.eval.domain.group.repo.EvalGroupRepo;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.AuditResultConf;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.BaseAuditNode;
import com.polaris.kpi.eval.domain.task.acl.CyclePushAcl;
import com.polaris.kpi.eval.domain.task.acl.IObItemPlanAcl;
import com.polaris.kpi.eval.domain.task.dmsvc.ApplyLastDmSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.EmpEvalDmSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.TableAsScoreStageRuleDmSvc;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.ScoreStageRule;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.admineval.OpEmpEval;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalOperation;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalRule;
import com.polaris.kpi.eval.domain.task.entity.emptable.EmpEvalTable;
import com.polaris.kpi.eval.domain.task.event.admineval.*;
import com.polaris.kpi.eval.domain.task.repo.AdminTaskRepo;
import com.polaris.kpi.eval.domain.task.repo.AdminTaskRepo;
import com.polaris.kpi.eval.domain.task.repo.EmpEvalRuleRepo;
import com.polaris.kpi.eval.domain.task.repo.ScorerSummaryTodoRepo;
import com.polaris.kpi.eval.domain.temp.entity.std.StdTemp;
import com.polaris.kpi.setting.domain.repo.ResultAuditFlowRepo;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import org.slf4j.MDC;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.*;
import java.util.concurrent.ExecutorService;

public class WrapperAdminCycleHandler {

    @Component
    @EventHandler
    public static class AdminCycleCreatedWrapper extends BaseEventHander<AdminCycleCreated> {
        @Autowired
        private CycleEvalAppSvc cycleEvalAppSvc;
        @Autowired
        private CyclePushAcl cyclePushAcl;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(AdminCycleCreated event) {
            return super.execute(event);
        }

        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(AdminCycleCreated event) {

            try {
                cycleEvalAppSvc.saveAdminCycleCreatedOperation(event.getOpEmpId(), event.getOpAdminType(), event.getCycle());
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }

            try {
                cyclePushAcl.pushCycle(event.getCycle());
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }
    }


    @Component
    @EventHandler
    public static class AdminCycleDeletedWrapper extends BaseEventHander<AdminCycleDeleted> {
        @Autowired
        private CyclePushAcl cyclePushAcl;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(AdminCycleDeleted event) {
            return super.execute(event);
        }

        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(AdminCycleDeleted event) {
            cyclePushAcl.deleteCycle(event.getTenantId(), event.getCycleId());
        }
    }

    @Component
    @EventHandler
    public static class AdminCycleTerminatedWrapper extends BaseEventHander<AdminCycleTerminated> {
        @Autowired
        private CycleEvalAppSvc cycleEvalAppSvc;
        @Autowired
        private AdminTaskAppSvc adminTaskAppSvc;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(AdminCycleTerminated event) {
            return super.execute(event);
        }

        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(AdminCycleTerminated event) {
            cycleEvalAppSvc.saveAdminCycleTerminatedOperation(event.getOpEmpId(), event.getOpAdminType(), event.getCycle());
            //周期终止，周期下的管理任务一并终止
            adminTaskAppSvc.terminatedCycleAdminTask(event.getCycle().getCompanyId(), event.getCycle().getId(),
                    new EmpId(event.getOpEmpId()));
        }
    }

    @Component
    @EventHandler
    public static class AdminCycleEditWrapper extends BaseEventHander<AdminCycleEdited> {
        @Autowired
        private CycleEvalAppSvc cycleEvalAppSvc;
        @Autowired
        private CyclePushAcl cyclePushAcl;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(AdminCycleEdited event) {
            return super.execute(event);
        }

        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(AdminCycleEdited event) {
            try {
                cycleEvalAppSvc.saveDiffAdminCycleBasicOperation(event.getOpEmpId(), event.getOpAdminType(), event.getBefore(), event.getAfter());
            } catch (Throwable e) {
                logger.error(e.getMessage(), e);
            }
            event.getAfter().setCreatedTime(event.getBefore().getCreatedTime());
            try {
                cyclePushAcl.pushCycle(event.getAfter());
            } catch (Throwable e) {
                logger.error(e.getMessage(), e);
            }
        }
    }

    @Component
    @EventHandler
    public static class AddCycleAdminWrapper extends BaseEventHander<CycleAdminAdded> {
        @Autowired
        private CycleEvalAppSvc cycleEvalAppSvc;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(CycleAdminAdded event) {
            return super.execute(event);
        }

        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(CycleAdminAdded event) {
            cycleEvalAppSvc.cycleAdminAdded(event.getOpEmpId(), event.getOpAdminType(), event.getAdmins());
        }
    }

    @Component
    @EventHandler
    public static class RemovedCycleAdminWrapper extends BaseEventHander<CycleAdminRemoved> {
        @Autowired
        private CycleEvalAppSvc cycleEvalAppSvc;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(CycleAdminRemoved event) {
            return super.execute(event);
        }

        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(CycleAdminRemoved event) {
            cycleEvalAppSvc.cycleAdminRemoved(event.getOpEmpId(), event.getOpAdminType(), event.getAdmins());
        }
    }

    @Component
    @EventHandler
    public static class CopyEvalRuleWrapper extends BaseEventHander<CopyEvalRule> {
        @Autowired
        private CycleEvalAppSvc cycleEvalAppSvc;
        @Autowired
        private AdminTaskAppSvc taskAppSvc;
        @Autowired
        private EmpEvalAppSvc empEvalAppSvc;
        @Autowired
        private EmpEvalRuleRepo empRuleRepo;
        @Autowired
        private ApplyLastDmSvc useLastDmSvc;
        @Autowired
        private AdminTaskRepo adminTaskRepo;
        @Autowired
        private EmpEvalDmSvc evalDmSvc;
        @Autowired
        private ResultAuditFlowRepo auditFlowRepo;
        @Autowired
        private AskEvalAcl askEvalAcl;
        @Autowired
        private IObItemPlanAcl objectAcl;
        @Autowired
        private ScorerSummaryTodoRepo scorerSummaryTodoRepo;
        @Autowired
        private CopyStageAppSvc copyStageAppSvc;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(CopyEvalRule event) {
            return super.execute(event);
        }

        @Override
        public void handerEvent(CopyEvalRule event) {
            event.start();
            for (OpEmpEval eval : event.getEmpEvals()) {// 37
                AdminTask task = adminTaskRepo.getAdminTask(event.getCompanyId(), event.getTaskId());
                EvalUser newUser = empEvalAppSvc.getBaseTaskUserV3(event.getCompanyId(), eval.getTaskUserId());
                if (Objects.isNull(newUser)) {
                    logger.error(" CopyEvalRule taskUserId :{} not exist ", eval.getTaskUserId());
                    continue;
                }
                EvalUser oldUser = empEvalAppSvc.getEvalUser(event.getCompanyId(), eval.getOldUserId());//23

//                cycleEvalAppSvc.createRankRuleSnapIfOpt(event.getCompanyId(), event.getCycleId(),
//                        eval.getEvalOrgId(), eval.getEmpId(), eval.getTaskUserId());
//                logger.info("oldId = " + eval.getOldUserId() + "newId" + eval.getTaskUserId());
                //如果是组织任务没有负责人的时候， 或者旧的考核任务没有默认考核规则，先不生成考核规则
                if (null == oldUser.getEmpEvalRule() || eval.ownerIdEmpty()) {//如果是组织任务，没有负责人的时候先不生成考核规则 2
                    empEvalAppSvc.updateEvalRuleStatus(event.getCompanyId().getId(), eval.getTaskUserId(), 0, null);
                    continue;
                }
                //沿用复用上次考核的逻辑生成一套新的考核规则
                EmpEvalRule empEvalRule = useLastDmSvc.applyLastScoreConf(newUser, oldUser, event.getOpEmpId());
                //根据任务上的截止时间段重新生成
                empEvalRule.setDeadLineConf(task.getDeadLineConf());
                //根据任务重新配置绩效通知汇总配置
                empEvalRule.getAuditResult().setCollectSendNotify(task.getResultSendNotify());
                //根据任务重新配置评分汇总发送待办配置
                empEvalRule.getScoreConf().setSummarySendTodo(task.getScoreConf().getSummarySendTodo() == null ? 1 : task.getScoreConf().getSummarySendTodo());
                //开启汇总评分将上级评改为同时评
                if (Objects.nonNull(task.getScoreConf().getSummarySendTodo()) &&
                        task.getScoreConf().getSummarySendTodo() == 2 &&
                        Objects.nonNull(empEvalRule.getS3SuperRater())) {
                    empEvalRule.getS3SuperRater().setSuperiorScoreOrder("sameTime");
                    empEvalRule.setSuperiorScoreOrder("sameTime");
                }
                //清除邀请的评分人
                empEvalRule.clearAppointRater();
                //如果有进行编辑，按任务的配置
                if (event.isEdit()) {
                    newUser.setEmpEvalRule(empEvalRule);
                    evalDmSvc.mergeConf(task, newUser, event.getOpEmpId());
                }
                empEvalRule.setScoreSortConf(event.getScoreSortConf());
                EmpEvalOperation operation = new EmpEvalOperation(eval.getTaskUserId(), event.getCompanyId().getId(), event.getOpEmpId(), event.getOpAdminType());
                operation.empEvalCreated(empEvalRule.getRuleName());
                //经营计划指标目标值只有一份时,默认选中
                List<BusinessPlanItem> planItemList = objectAcl.listBusinessPlanItem(event.getCompanyId().getId(), newUser.getId(), empEvalRule.getPlanItemIds());
                empEvalRule.refreshOkrGoalId(planItemList);
                empRuleRepo.addEmpEvalRule(newUser, empEvalRule, operation, true);
                newUser.setEmpEvalRule(empEvalRule);
                //生成360问卷考核实例
                newUser.setTaskName(task.getTaskName());
                askEvalAcl.createdAsk360Eval(newUser, event.getCompanyId().getId(), event.getOpEmpId());
                if (Objects.isNull(newUser.getResultSendNotify())) {
                    newUser.setResultSendNotify(task.getResultSendNotify());
                }

//                //环节同时评，并且上级同时评时，并且开启了汇总发送待办，才进行预派发
//                EmpEvalMerge evalMerge = empRuleRepo.getEmpEvalMerge(newUser.getCompanyId(), newUser.getId(), EmpEvalMerge.all);
//                ScorerTodoDmSvc todoDmSvc = new ScorerTodoDmSvc(evalMerge,newUser);
//                //先移除，再添加，对受影响的评分人重新汇总
//                Set<String> scorerIds = scorerSummaryTodoRepo.removeScorerByTaskUser(newUser.getCompanyId().getId(), newUser.getId());
//                if (todoDmSvc.support()){
//                    ChainDispatchRs chainDispatchRs = todoDmSvc.preDispatch(newUser);
//                    scorerIds.addAll(scorerSummaryTodoRepo.saveScorerInstance(chainDispatchRs, newUser, evalMerge));
//                    todoDmSvc.setRepo(scorerSummaryTodoRepo);
//                    todoDmSvc.refreshSummary(scorerIds);
//                    List<ScorerTodoSummary> addSummaries = todoDmSvc.getAddSummaries();
//                    List<ScorerTodoSummary> updateSummaries = todoDmSvc.getUpdateSummaries();
//                    if (!addSummaries.isEmpty()){
//                        scorerSummaryTodoRepo.batchAddSummaries(addSummaries);
//                    }
//                    if (!updateSummaries.isEmpty()){
//                        scorerSummaryTodoRepo.batchUpdateSummaries(updateSummaries);
//                    }
//                }

                //生成校准审批流程实例
//                if (newUser.isAuditResultCollectSend()) {
//                    ResultAuditFlow flow = new ResultAuditFlow(event.getCompanyId().getId(), event.getOpEmpId());
//                    auditFlowRepo.saveAuditFlow(flow, newUser);
//                }
                new EvalTaskErrorEvent(event.getCompanyId().getId(), newUser).publish();
                //考核规则变更事件
                new EvalRuleChanged(event.getCompanyId(),event.getOpEmpId(), newUser,empEvalRule).fire();
            }
            cycleEvalAppSvc.refreshCycleEmpCnt(event.getCompanyId(), event.getCycleId());
            taskAppSvc.refreshTaskCnt(event.getCompanyId().getId(), event.getTaskId());
        }


        @EventListener
        @Async(ExecutorEnum.high)
        public void batchHanderEvent(CopyEvalRule event) {
            event.start();
            copyStageAppSvc.batchCopyEmpEvalRule(event);
            cycleEvalAppSvc.refreshCycleEmpCnt(event.getCompanyId(), event.getCycleId());
            taskAppSvc.refreshTaskCnt(event.getCompanyId().getId(), event.getTaskId());
        }
    }

    @Slf4j
    @Component
    @EventHandler
    public static class AddEmpToTaskWrapper extends BaseEventHander<AddEmpToTask> {
        @Autowired
        private CycleEvalAppSvc cycleEvalAppSvc;
        @Autowired
        private AdminTaskAppSvc taskAppSvc;
        @Autowired
        private EmpEvalAppSvc empEvalAppSvc;
        @Autowired
        private EmpTableAppSvc tableAppSvc;
        @Autowired
        private EvalGroupRepo evalGroupRepo;
        @Autowired
        private StdTempAppSvc stdAppSvc;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(AddEmpToTask event) {
            return super.execute(event);
        }

        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(AddEmpToTask event) throws CloneNotSupportedException {
            event.start();
            Map<String, EvalGroup> groupWithTemplMap = new HashMap<>();
            Map<String, EvalGroupEmp> groupEmpMap = new HashMap<>();
            Map<String, StdTemp> stdTempCache = new HashMap<>(); // 新增：StdTemp缓存

            if (CollUtil.isNotEmpty(event.getEvalGroupIds())) {
                groupWithTemplMap = evalGroupRepo.mapEmpOrgToLatestEvalGroup(event.getEvalGroupIds());
                groupEmpMap = evalGroupRepo.mapEmpOrgToEvalGroupEmp(event.getEvalGroupIds());
                for (OpEmpEval emp : event.getEmps()) {
                    // 优先配置为考核关系组的模板 todo: 待优化
                    EvalGroup evalGroup = groupWithTemplMap.get(emp.getEmpId() + "_" + emp.getOrgId());
                    EvalGroupEmp evalGroupEmp = groupEmpMap.get(emp.getEmpId() + "_" + emp.getOrgId());

                    if (Objects.nonNull(evalGroup) && StringUtils.isNotBlank(evalGroup.getTemplBaseId()) && Objects.nonNull(evalGroupEmp)) {
                        // 使用缓存避免重复查询StdTemp
                        StdTemp stdTemp = stdTempCache.get(evalGroup.getTemplBaseId());
                        if (Objects.isNull(stdTemp)) {
                            stdTemp = stdAppSvc.getStdTempForApply(event.getCompanyId(), new TempId(evalGroup.getTemplBaseId()));
                            stdTempCache.put(evalGroup.getTemplBaseId(), stdTemp);
                        }

                        // 如果考核组成员有自定义配置，则使用自定义配置
                        if (evalGroupEmp.customScoreFlowConf()) {
                            // 克隆StdTemp避免影响缓存的原始对象
                            stdTemp = stdTemp.clone();
                            stdTemp.setEvaluate(evalGroupEmp.getEvaluate());
                        }
                        // 如果成员身上有自定义配置,则使用自己的
                        evalGroupEmp.accGroupConfIfNotHave(evalGroup);

                        ApplyTempCmd cmd = new ApplyTempCmd(event.getCompanyId(), event.getOpEmpId(), evalGroup.getTemplBaseId(), emp.getTaskUserId(), stdTemp, evalGroup.getId(), evalGroup.getGroupName(), evalGroupEmp.getAuditResultFlowConf());
                        try {
                            empEvalAppSvc.applyTemp(cmd);
                        } catch (Exception e) {
                            // 打印异常日志之后直接跳过
                            log.error("配置考核模板出错OpEmpEval={}, e=", emp, e);
                        }
                    }
                }
            } else {
                for (OpEmpEval emp : event.getEmps()) {
                    EmpEvalTable table = tableAppSvc.getDefaultEmpTable(event.getCompanyId().getId(), emp.getEmpId(), emp.getEvalOrgId());
                    if (table == null || emp.ownerIdEmpty()) {//如果是组织任务，没有负责人的时候先不生成考核规则
                        empEvalAppSvc.updateEvalRuleStatus(event.getCompanyId().getId(), emp.getTaskUserId(), 0, null);
                        continue;
                    }
                    TableAsScoreStageRuleDmSvc ruleDmSvc = new TableAsScoreStageRuleDmSvc(table);
                    ScoreStageRule scoreStageRule = ruleDmSvc.asScoreRule(emp.getTaskUserId());
                    //boolean hasLeavedRater = tableAppSvc.hasLeavedRater(table.getCompanyId(), ruleDmSvc.raterEmpIds());
                    ScoreStageRuleCmd cmd = new ScoreStageRuleCmd();
                    cmd.setOperator(event.getCompanyId().getId(), event.getOpEmpId(), event.getOpAdminType());
                    cmd.setScoreStageRule(scoreStageRule);
                    //cmd.setHasLeavedRater(hasLeavedRater);
                    cmd.checkAddParam();
                    empEvalAppSvc.addScoreStageRule(cmd);
                }
            }

            cycleEvalAppSvc.refreshCycleEmpCnt(event.getCompanyId(), event.getCycleId());
            taskAppSvc.refreshTaskCnt(event.getCompanyId().getId(), event.getTaskId());
        }
    }

    @Component
    @EventHandler
    public static class EvalEmpRemovedWrapper extends BaseEventHander<EvalEmpRemoved> {
        @Autowired
        private CycleEvalAppSvc cycleEvalAppSvc;
        @Autowired
        private AdminTaskAppSvc taskAppSvc;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(EvalEmpRemoved event) {
            return super.execute(event);
        }

        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(EvalEmpRemoved event) {
            event.start();
            cycleEvalAppSvc.refreshCycleEmpCnt(event.getCompanyId(), event.getCycleId());
            for (String taskId : event.getTaskIds()) {
                taskAppSvc.refreshTaskCnt(event.getCompanyId().getId(), taskId);
            }
        }
    }

    @Component
    @EventHandler
    public static class EvalEmpOkrLockRemovedWrapper extends BaseEventHander<EvalEmpOkrLockEdited> {
        @Autowired
        private EmpEvalAppSvc empEvalAppSvc;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(EvalEmpOkrLockEdited event) {
            return super.execute(event);
        }

        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(EvalEmpOkrLockEdited event) {
            empEvalAppSvc.okrLockEdited(event);
        }
    }


    @Component
    @EventHandler
    public static class EvalTaskErrorWrapper extends BaseEventHander<EvalTaskErrorEvent> {
        @Autowired
        private EmpEvalAppSvc empEvalAppSvc;
        @Autowired
        private ResultAuditFlowAppSvc flowAppSvc;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(EvalTaskErrorEvent event) {
            return super.execute(event);
        }

        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(EvalTaskErrorEvent event) {
            event.start();
            String companyId = event.getCompanyId();
            EvalUser evalUser = event.getEvalUse();
            evalUser.setRuleConfStatus(200);
            evalUser.setRuleConfError(null);
            String tid = MDC.get("tid");
            CompletableFuture.runAsync(() -> {
                try {
                    MDC.put("tid", tid);
                    empEvalAppSvc.anewEvalRuleStatus(companyId, evalUser);
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                }
            });
        }

        @EventListener
        @Async(ExecutorEnum.high)
        public void batchHanderEvent(CopyEmpEvalFinishedEvent event) {
            event.start();
            String tid = MDC.get("tid");
//            //统计评分汇总
//            empEvalAppSvc.refreshScoreMsgSummary(event.getCompanyId().getId(), event.getEvalUsers());
//            //统计校准汇总
            flowAppSvc.batchCreateResultAuditFlow(event.getCompanyId().getId(), event.getOpEmpId(), event.getEvalUsers());
        }
    }

    @Component
//    @EventHandler
    public static class EvalRuleConfedWrapper extends BaseEventHander<EvalRuleConfed> {
        @Autowired
        private EmpEvalAppSvc empEvalAppSvc;
        @Autowired
        private ResultAuditFlowAppSvc flowAppSvc;

        public ExecutorService getExecutor() {
            return middleExecutor;
        }

        @Override
        public Response execute(EvalRuleConfed event) {
            return super.execute(event);
        }

        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(EvalRuleConfed event) {
            logger.info("异步处理开始, 线程: {}", Thread.currentThread().getName());
            event.start();
            String companyId = event.getCompanyId();
            String tid = MDC.get("tid");
            CompletableFuture.runAsync(() -> {
                MDC.put("tid", tid);
                empEvalAppSvc.checkErro(new TenantId(companyId), event.getEvalUsers());
            }).thenRun(() -> {
                //批量解析评分人
                MDC.put("tid", tid);
                empEvalAppSvc.explainEmpEvalScorerBatchForUsers(new TenantId(companyId), event.getEvalUsers());
//                //统计评分汇总
//                empEvalAppSvc.refreshScoreMsgSummary(companyId, event.getEvalUsers());
                //统计校准汇总
                flowAppSvc.batchCreateResultAuditFlow(companyId, event.getOpEmpId(), event.getEvalUsers());
            });

        }
    }


    @Component
    @EventHandler
    public static class EmpOpenEventWrapper extends BaseEventHander<EmpAppStatusChangedEvent> {
        @Autowired
        private EmpEvalAppSvc empEvalAppSvc;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(EmpAppStatusChangedEvent event) {
            return super.execute(event);
        }

        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(EmpAppStatusChangedEvent event) {
            event.start();
            String companyId = event.getTenantId();
            List<EvalUser> evalUsers = empEvalAppSvc.listRefreshEvalErrorStatus(new TenantId(companyId));
            empEvalAppSvc.checkErro(new TenantId(companyId), evalUsers);
        }
    }

    @Component
    @EventHandler
    public static class EvalRuleChangedEventWrapper extends BaseEventHander<EvalRuleChanged> {
        @Autowired
        private EmpEvalAppSvc empEvalAppSvc;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(EvalRuleChanged event) {
            return super.execute(event);
        }

        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(EvalRuleChanged event) {
            event.start();
            TenantId companyId = event.getCompanyId();
            EmpEvalRule rule = event.getEmpEvalRule();
            String opEmpId = event.getOpEmpId();
            EvalUser user = event.getUser();
            empEvalAppSvc.explainEmpEvalScorer(companyId, opEmpId, user, rule);
            logger.info(" add 评分人 userID：{}", user.getId());
        }
    }
}
