package com.polaris.kpi.controller.eval.task.emp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.perf.www.common.annotation.NoneLogin;
import com.perf.www.common.web.AccountBaseController;
import com.perf.www.common.web.ResponseInfo;
import com.polaris.kpi.eval.app.priv.appsvc.AdminScopePrivAppSvc;
import com.polaris.kpi.eval.app.task.appsvc.FixTaskAppSvc;
import com.polaris.kpi.eval.app.task.appsvc.ResultAuditFlowAppSvc;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.event.FinishedValueAuditEvent;
import com.polaris.kpi.eval.domain.task.event.KpiItemUpdateFinishedValueEvent;
import com.polaris.kpi.eval.domain.task.repo.TaskUserRepo;
import com.polaris.kpi.eval.infr.task.query.WorkWaitMsgQuery;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
public class FixEmpEvalController extends AccountBaseController {

    @Autowired
    private FixTaskAppSvc fixTaskAppSvc;

    @Autowired
    private AdminScopePrivAppSvc adminScopePrivAppSvc;

    @Autowired
    private ResultAuditFlowAppSvc resultAuditFlowAppSvc;

    @Autowired
    private TaskUserRepo userRepo;

    //订正评价流程索引
    @RequestMapping("fix/eval/fixIndexRaters")
    @NoneLogin
    public ResponseInfo fixIndexRaters(String companyId, String taskId, String taskUserIds, String node) {
        if (StrUtil.isBlank(taskId) && StrUtil.isBlank(taskUserIds)) {
            return ResponseInfo.success("");
        }
        fixTaskAppSvc.fixIndexRaters(companyId,taskId,taskUserIds,node);
        return ResponseInfo.success("");
    }


    @RequestMapping("init/eval/initUnifyResultAudit")
    @NoneLogin
    public ResponseInfo initUnifyResultAudit(String companyId,String taskIds) {
        fixTaskAppSvc.initializeFlowSummary(companyId,taskIds);
        return ResponseInfo.success("");
    }


    //分发下级校准通知待办
    @RequestMapping("fix/calibrate/nextDispatchUnifyResultAudit")
    @NoneLogin
    public ResponseInfo nextDispatchResultAudit(String companyId,String taskUserId,Integer level) {
        fixTaskAppSvc.nextDispatchUnifyResultAudit(new TenantId(companyId),taskUserId,level);
        return ResponseInfo.success("");
    }

    //分发下级校准通知待办
    @RequestMapping("fix/calibrate/sendUnifyResultAuditMsg")
    @NoneLogin
    public ResponseInfo sendUnifyResultAuditMsg(String companyId,String taskId,String adminEmpId,Integer level) {
        fixTaskAppSvc.sendUnifyResultAuditMsg(new TenantId(companyId),taskId,adminEmpId,level);
        return ResponseInfo.success("");
    }


    //分发下级校准通知待办
    @RequestMapping("fix/calibrate/nextDispatchResultAudit")
    @NoneLogin
    public ResponseInfo nextDispatchSingleResultAudit(String companyId,String taskUserId,Integer level,String opEmpId) {
        fixTaskAppSvc.nextDispatchSingleResultAudit(new TenantId(companyId),taskUserId,level,new EmpId(opEmpId));
        return ResponseInfo.success("");
    }


    //重新刷新团队管理权限
    @RequestMapping("fix/adminScopePriv/refreshEmpManagerPriv")
    @NoneLogin
    public ResponseInfo refreshEmpManagerPriv(String companyId) {
        adminScopePrivAppSvc.refreshEmpManagerPriv(companyId);
        return ResponseInfo.success("");
    }

    @RequestMapping("fix/adminScopePriv/fullAddManagerPriv")
    @NoneLogin
    public ResponseInfo fullAddManagerPriv(String companyId) {
        adminScopePrivAppSvc.fullAddManagerPriv(companyId);
        return ResponseInfo.success("");
    }

    //生成校准汇总实例
    @RequestMapping("fix/calibrate/refreshFlow")
    @NoneLogin
    public ResponseInfo refreshFlow(@RequestBody WorkWaitMsgQuery query) {
        resultAuditFlowAppSvc.refreshFlow(query.getCompanyId(),query.getTaskUserIds(),"999999","resultsAuditing");
        return ResponseInfo.success("");
    }


    //初始化考核人员所属部门链路
    @RequestMapping("fix/eval/initAtOrgNamePath")
    @NoneLogin
    public ResponseInfo initAtOrgNamePath(String companyId,String taskId) {
        fixTaskAppSvc.initAtOrgNamePath(companyId,taskId);
        return ResponseInfo.success("");
    }

    @RequestMapping("fix/log")
    @NoneLogin
    public ResponseInfo log(@RequestBody JSONObject body) {
        log.info("调用日志记录{}", body);
        return ResponseInfo.success("");
    }

    //补发空缺的校准流程
    @RequestMapping("fix/calibrate/fixDispatchResultChainNode")
    @NoneLogin
    public ResponseInfo fixDispatchChainNode(String companyId,String taskUserIds,Integer level,String opEmpId) {
        fixTaskAppSvc.fixDispatchResultChainNode(new TenantId(companyId),taskUserIds,level,new EmpId(opEmpId));
        return ResponseInfo.success("");
    }

    //刷新校准汇总
    @RequestMapping("fix/calibrate/refreshSummary")
    @NoneLogin
    public ResponseInfo refreshSummary(String companyId,String taskId) {
        fixTaskAppSvc.refreshSummary(new TenantId(companyId),taskId);
        return ResponseInfo.success("");
    }

    //修改考核成员结果呈现
    @RequestMapping("fix/eval/updateShowResultType")
    @NoneLogin
    public ResponseInfo updateShowResultType(String companyId,String taskUserIds,Integer showResultType) {
        if (StrUtil.isBlank(companyId) || StrUtil.isBlank(taskUserIds)  || Objects.isNull(showResultType)) {
            return ResponseInfo.success("");
        }
        fixTaskAppSvc.updateShowResultType(new TenantId(companyId),taskUserIds,showResultType);
        return ResponseInfo.success("");
    }

    //发送校准通知待办
    @RequestMapping("fix/eval/sendTaskResultAuditMsg")
    @NoneLogin
    public ResponseInfo sendTaskResultAuditMsg(String companyId,String taskUserIds,String adminEmpId) {
        if (StrUtil.isBlank(companyId) || StrUtil.isBlank(taskUserIds)) {
            return ResponseInfo.success("");
        }
        fixTaskAppSvc.sendTaskResultAuditMsg(companyId,taskUserIds,adminEmpId);
        return ResponseInfo.success("");
    }

    //替换校准人
    @RequestMapping("fix/eval/replaceResultAuditEmp")
    @NoneLogin
    public ResponseInfo replaceResultAuditEmp(String companyId,String taskUserIds,
                                              String originalAuditEmpId,String newAuditEmpId,Integer level) {
        if (StrUtil.isBlank(companyId) || StrUtil.isBlank(taskUserIds)) {
            return ResponseInfo.success("");
        }
        fixTaskAppSvc.replaceResultAuditEmp(companyId,taskUserIds,originalAuditEmpId,newAuditEmpId,level);
        return ResponseInfo.success("");
    }

    //重新生成校准审批流程实例
    @RequestMapping("fix/eval/fixResultAuditFlow")
    @NoneLogin
    public ResponseInfo fixResultAuditFlow(String companyId,String taskUserIds) {
        if (StrUtil.isBlank(companyId) || StrUtil.isBlank(taskUserIds)) {
            return ResponseInfo.success("");
        }
        fixTaskAppSvc.fixResultAuditFlow(companyId, StrUtil.splitTrim(taskUserIds, ","));
        return ResponseInfo.success("");
    }

    /**
     * 重新生成校准审批流程实例-整个任务
     * @param companyId 跨公司多任务可不传
     * @param taskIds 必传
     * @param taskUserId 可选,传的时候优先按单个执行,需要companyId,taskId
     * @return
     */
    @RequestMapping("fix/eval/fixResultAuditFlowWithTaskId")
    @NoneLogin
    public ResponseInfo fixResultAuditFlowWithTaskId(String companyId, String taskIds, String taskUserId) {
        fixTaskAppSvc.fixResultAuditFlowWithTaskIds(companyId, taskIds, taskUserId);
        return ResponseInfo.success("");
    }

    //发送汇总通知,不管是否达到汇总数量条件
    @RequestMapping("fix/eval/sendResultCollectMsgTodo")
    @NoneLogin
    public ResponseInfo sendResultCollectMsgTodo(String companyId,String taskId) {
        if (StrUtil.isBlank(companyId) || StrUtil.isBlank(taskId)) {
            return ResponseInfo.success("");
        }
        fixTaskAppSvc.sendResultCollectMsgTodo(companyId, taskId);
        return ResponseInfo.success("");
    }

    //清除消息待办
    @RequestMapping("fix/eval/cancelMsg")
    @NoneLogin
    public ResponseInfo cancelMsg(String companyId,String taskUserIds,String msgScene) {
        if (StrUtil.isBlank(companyId) || StrUtil.isBlank(taskUserIds) || StrUtil.isBlank(msgScene)) {
            return ResponseInfo.success("");
        }
        fixTaskAppSvc.cancelMsg(companyId,taskUserIds,msgScene);
        return ResponseInfo.success("");
    }
    //清除20250211ding消息待办[绩效系统待办已处理的]
    @RequestMapping("fix/eval/fixCancelDingMsg20250211")
    @NoneLogin
    public ResponseInfo fixCancelDingMsg20250211(String companyId) {
        if (StrUtil.isBlank(companyId)) {
            return ResponseInfo.success("");
        }
        fixTaskAppSvc.fixCancelDingMsg20250211(companyId);
        return ResponseInfo.success("");
    }
    //订正上级环节Audit重复
    @RequestMapping("fix/eval/fixSuperiorAuditRepeat")
    @NoneLogin
    public ResponseInfo fixSuperiorAuditRepeat(String companyId,String taskUserIds,String scoreType) {
        if (StrUtil.isBlank(companyId) || StrUtil.isBlank(taskUserIds) || StrUtil.isBlank(scoreType)) {
            return ResponseInfo.success("");
        }
        fixTaskAppSvc.fixSuperiorAuditRepeat(companyId,taskUserIds,scoreType);
        return ResponseInfo.success("");
    }

    //订正上级环节Result重复
    @RequestMapping("fix/eval/fixSuperiorResultRepeat")
    @NoneLogin
    public ResponseInfo fixSuperiorResultRepeat(String companyId,String taskUserIds,String scoreType) {
        if (StrUtil.isBlank(companyId) || StrUtil.isBlank(taskUserIds) || StrUtil.isBlank(scoreType)) {
            return ResponseInfo.success("");
        }
        fixTaskAppSvc.fixSuperiorResultRepeat(companyId,taskUserIds,scoreType);
        return ResponseInfo.success("");
    }

    //订正指标自定义字段重复
    @RequestMapping("fix/eval/fixCustomUserFieldRepeatBatch")
    @NoneLogin
    public ResponseInfo fixCustomUserFieldRepeatBatch() {
        fixTaskAppSvc.fixCustomUserFieldRepeatBatch();
        return ResponseInfo.success("");
    }

    //订正指标自定义字段重复
//    @RequestMapping("fix/eval/fixKpiItemRepeatBatch")
//    @NoneLogin
//    public ResponseInfo fixKpiItemRepeatBatch() {
//        fixTaskAppSvc.fixKpiItemRepeatBatch();
//        return ResponseInfo.success("");
//    }

    @RequestMapping("fix/eval/refreshFinishedValueAudit")
    @NoneLogin
    public ResponseInfo refreshFinishedValueAudit(String companyId, String taskUserId, String opEmpId) {
        TenantId tenantId = new TenantId(companyId);
        List<String> taskUserIds = StrUtil.splitTrim(taskUserId, ",");
        if (CollUtil.isNotEmpty(taskUserIds)){
            for (String id : taskUserIds){
                EvalUser evalUser = userRepo.getTaskUser(tenantId, id);
                new FinishedValueAuditEvent(true, new TenantId(companyId), evalUser.getId(), evalUser, new EmpId(opEmpId)).publish();
            }
        }
        return ResponseInfo.success("");
    }
}
