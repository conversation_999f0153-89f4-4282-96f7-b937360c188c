package com.polaris.kpi.controller.eval.task;

import com.alibaba.cola.dto.SingleResponse;
import com.perf.www.common.web.AccountBaseController;
import com.polaris.kpi.eval.app.task.appsvc.ScorerDataMingrationAppSvc;
import com.polaris.kpi.eval.app.task.result.FailureRecordResult;
import com.polaris.kpi.eval.app.task.result.MigrationProgressResult;
import com.polaris.kpi.eval.app.task.result.MigrationStartResult;
import com.polaris.kpi.eval.domain.task.entity.OptimizedBitmapProgress;
import com.polaris.kpi.eval.domain.task.entity.empeval.migration.BatchPerformanceMetrics;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 评分人数据迁移服务控制器
 *
 * <AUTHOR> Assistant
 * @since 2025-01-27
 */
@Slf4j
@RestController
public class ScorerDataMingrationController extends AccountBaseController {

    @Autowired
    private ScorerDataMingrationAppSvc mingrationAppSvc;

    // ==================== 单个用户处理方法 ====================

    @RequestMapping("perf/task/migrateFinishedOne")
    public SingleResponse migrateFinishedOne(@RequestParam String taskUserId,@RequestParam String companyId) {
        log.info("单个评分迁移开始 migrateFinishedOne companyId:{},taskUserId:{}", companyId, taskUserId);
        mingrationAppSvc.migrateFinishedOne(companyId, taskUserId);
        log.info("单个评分迁移migrateFinishedOne【已完成】任务结束！！！");
        return SingleResponse.of("");
    }


    @RequestMapping("perf/task/migrateNoFinishOne")
    public SingleResponse migrateNoFinishOne(@RequestParam String taskUserId,@RequestParam String companyId) {
        log.info("单个评分迁移开始 migrateNoFinishOne,companyId:{},taskUserId:{}", companyId, taskUserId);
        mingrationAppSvc.migrateNoFinishOne(companyId, taskUserId);
        log.info("单个评分迁移migrateNoFinishOne【已完成】任务结束！！！");
        return SingleResponse.of("");
    }

    @RequestMapping("perf/task/logTaskStatus")
    public SingleResponse logTaskStatus() {
        log.info("评分迁移 logTaskStatus任务开始");
        mingrationAppSvc.logTaskStatus();
        log.info("评分迁移 logTaskStatus任务结束");
        return SingleResponse.of("");
    }

    // ==================== 单个公司迁移方法 ====================

    /**
     * 单个公司迁移（已完成用户）
     */
    @RequestMapping("perf/task/migrateCompanyFinished")
    public SingleResponse migrateCompanyFinished(@RequestParam String companyId) {
        log.info("单个公司迁移开始（已完成用户）: companyId={}", companyId);
        String tid = MDC.get("tid");

        try {
            String sessionId = mingrationAppSvc.migrateCompanyFinished(tid, companyId);

            MigrationStartResult result = new MigrationStartResult(sessionId, "FINISHED", "COMPLETED",
                    "单个公司迁移（已完成用户）执行成功");
            log.info("单个公司迁移（已完成用户）执行成功: companyId={}, sessionId={}", companyId, sessionId);
            return SingleResponse.of(result);

        } catch (Exception e) {
            log.error("单个公司迁移（已完成用户）执行失败: companyId={}", companyId, e);
            return SingleResponse.buildFailure("COMPANY_MIGRATION_FINISHED_ERROR",
                    "单个公司迁移（已完成用户）执行失败: " + e.getMessage());
        }
    }

    /**
     * 单个公司迁移（未完成用户）
     */
    @RequestMapping("perf/task/migrateCompanyNoFinished")
    public SingleResponse migrateCompanyNoFinished(@RequestParam String companyId) {
        log.info("单个公司迁移开始（未完成用户）: companyId={}", companyId);
        String tid = MDC.get("tid");

        try {
            String sessionId = mingrationAppSvc.migrateCompanyNoFinished(tid, companyId);

            MigrationStartResult result = new MigrationStartResult(sessionId, "NO_FINISHED", "COMPLETED",
                    "单个公司迁移（未完成用户）执行成功");
            log.info("单个公司迁移（未完成用户）执行成功: companyId={}, sessionId={}", companyId, sessionId);
            return SingleResponse.of(result);

        } catch (Exception e) {
            log.error("单个公司迁移（未完成用户）执行失败: companyId={}", companyId, e);
            return SingleResponse.buildFailure("COMPANY_MIGRATION_NO_FINISHED_ERROR",
                    "单个公司迁移（未完成用户）执行失败: " + e.getMessage());
        }
    }

    /**
     * 单个公司迁移（混合模式：串行执行）
     */
    @RequestMapping("perf/task/migrateCompanyMixed")
    public SingleResponse migrateCompanyMixed(@RequestParam String companyId) {
        log.info("单个公司迁移开始（混合模式）: companyId={}", companyId);
        String tid = MDC.get("tid");

        try {
            String sessionId = mingrationAppSvc.migrateCompanyMixed(tid, companyId);

            MigrationStartResult result = new MigrationStartResult(sessionId, "MIXED", "COMPLETED",
                    "单个公司迁移（混合模式）执行成功");
            log.info("单个公司迁移（混合模式）执行成功: companyId={}, sessionId={}", companyId, sessionId);
            return SingleResponse.of(result);

        } catch (Exception e) {
            log.error("单个公司迁移（混合模式）执行失败: companyId={}", companyId, e);
            return SingleResponse.buildFailure("COMPANY_MIGRATION_MIXED_ERROR",
                    "单个公司迁移（混合模式）执行失败: " + e.getMessage());
        }
    }

    // ==================== 优化迁移方法 ====================

    /**
     * 启动优化的大规模数据迁移
     */
    @RequestMapping("perf/task/startOptimizedMigration")
    public SingleResponse startOptimizedMigration(@RequestParam String migrationType) {
        log.info("启动优化迁移任务: type={}", migrationType);
        String tid = MDC.get("tid");
        try {
            String sessionId = mingrationAppSvc.startOptimizedMigration(tid, migrationType, null);

            MigrationStartResult result = new MigrationStartResult(sessionId, migrationType, "STARTED", "迁移任务启动成功");
            log.info("优化迁移任务启动成功: sessionId={}", sessionId);
            return SingleResponse.of(result);

        } catch (Exception e) {
            log.error("启动优化迁移任务失败: type={}", migrationType, e);
            return SingleResponse.buildFailure("MIGRATION_START_FAILED", "启动优化迁移任务失败: " + e.getMessage());
        }
    }


    /**
     * 启动优化的大规模数据迁移
     */
    @RequestMapping("perf/task/startOptimizedMigrationShard")
    public SingleResponse startOptimizedMigrationShard(@RequestParam String migrationType, Integer shardIndex, Integer shardTotal) {
        log.info("启动优化迁移任务: type={},shardIndex:{},shardTotal:{}", migrationType, shardIndex, shardTotal);
        String tid = MDC.get("tid");
        try {
            String sessionId = mingrationAppSvc.startOptimizedMigration(tid, migrationType, null, shardIndex, shardTotal);

            MigrationStartResult result = new MigrationStartResult(sessionId, migrationType, "STARTED", "迁移任务启动成功");
            log.info("优化迁移任务启动成功: sessionId={}", sessionId);
            return SingleResponse.of(result);

        } catch (Exception e) {
            log.error("启动优化迁移任务失败: type={}", migrationType, e);
            return SingleResponse.buildFailure("MIGRATION_START_FAILED", "启动优化迁移任务失败: " + e.getMessage());
        }
    }


    /**
     * 启动优化的大规模数据迁移
     */
    @RequestMapping("perf/task/startOptimizedMigrationShard21")
    public SingleResponse startOptimizedMigrationShard21(@RequestParam String migrationType, Integer shardIndex, Integer shardTotal) {
        log.info("启动优化迁移任务过期时间是21年份的迁移: type={},shardIndex:{},shardTotal:{}", migrationType, shardIndex, shardTotal);
        String tid = MDC.get("tid");
        try {
            String sessionId = mingrationAppSvc.startOptimizedMigrationShard21(tid, migrationType, null, shardIndex, shardTotal);

            MigrationStartResult result = new MigrationStartResult(sessionId, migrationType, "STARTED", "迁移任务启动成功");
            log.info("启动优化迁移任务过期时间是21年份的迁移成功: sessionId={}", sessionId);
            return SingleResponse.of(result);

        } catch (Exception e) {
            log.error("启动优化迁移任务过期时间是21年份的迁移任务失败: type={}", migrationType, e);
            return SingleResponse.buildFailure("MIGRATION_START_FAILED", "启动优化迁移任务过期时间是21年份的迁移任务失败: " + e.getMessage());
        }
    }

    /**
     * 恢复中断的优化迁移任务
     */
    @RequestMapping("perf/task/resumeOptimizedMigration")
    public SingleResponse resumeOptimizedMigration(@RequestParam String sessionId) {
        log.info("恢复优化迁移任务: sessionId={}", sessionId);
        String tid = MDC.get("tid");
        try {
            boolean success = mingrationAppSvc.resumeOptimizedMigration(tid, sessionId);

            if (success) {
                log.info("优化迁移任务恢复成功: sessionId={}", sessionId);
                return SingleResponse.of("优化迁移任务恢复成功");
            } else {
                log.warn("优化迁移任务恢复失败: sessionId={}", sessionId);
                return SingleResponse.buildFailure("MIGRATION_RESUME_FAILED", "优化迁移任务恢复失败，请检查任务状态");
            }

        } catch (Exception e) {
            log.error("恢复优化迁移任务失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("MIGRATION_RESUME_ERROR", "恢复优化迁移任务失败: " + e.getMessage());
        }
    }

    /**
     * 暂停优化迁移任务
     */
    @RequestMapping("perf/task/pauseOptimizedMigration")
    public SingleResponse pauseOptimizedMigration(@RequestParam String sessionId) {
        log.info("暂停优化迁移任务: sessionId={}", sessionId);

        try {
            boolean success = mingrationAppSvc.pauseOptimizedMigration(sessionId);

            if (success) {
                log.info("优化迁移任务暂停成功: sessionId={}", sessionId);
                return SingleResponse.of("优化迁移任务暂停成功");
            } else {
                log.warn("优化迁移任务暂停失败: sessionId={}", sessionId);
                return SingleResponse.buildFailure("MIGRATION_PAUSE_FAILED", "优化迁移任务暂停失败，请检查任务状态");
            }

        } catch (Exception e) {
            log.error("暂停优化迁移任务失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("MIGRATION_PAUSE_ERROR", "暂停优化迁移任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取优化迁移详细进度
     */
    @RequestMapping("perf/task/getOptimizedMigrationProgress")
    public SingleResponse getOptimizedMigrationProgress(@RequestParam String sessionId) {
        log.debug("获取优化迁移详细进度: sessionId={}", sessionId);

        try {
            OptimizedBitmapProgress progress = mingrationAppSvc.getOptimizedMigrationStatus(sessionId);

            if (progress == null) {
                return SingleResponse.buildFailure("MIGRATION_NOT_FOUND", "优化迁移任务不存在");
            }

            // 使用应用服务层的转换方法
            MigrationProgressResult response = mingrationAppSvc.convertToMigrationProgressResult(progress);

            return SingleResponse.of(response);

        } catch (Exception e) {
            log.error("获取优化迁移详细进度失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("MIGRATION_PROGRESS_ERROR", "获取优化迁移详细进度失败: " + e.getMessage());
        }
    }

    /**
     * 清理优化迁移文件
     */
    @RequestMapping("perf/task/cleanupOptimizedMigrationFiles")
    public SingleResponse cleanupOptimizedMigrationFiles(@RequestParam String sessionId) {
        log.info("清理优化迁移文件: sessionId={}", sessionId);

        try {
            boolean success = true;//mingrationAppSvc.cleanupOptimizedMigrationFiles(sessionId);

            if (success) {
                log.info("优化迁移文件清理成功: sessionId={}", sessionId);
                return SingleResponse.of("优化迁移文件清理成功");
            } else {
                log.warn("优化迁移文件清理失败: sessionId={}", sessionId);
                return SingleResponse.buildFailure("MIGRATION_CLEANUP_FAILED", "优化迁移文件清理失败");
            }

        } catch (Exception e) {
            log.error("清理优化迁移文件失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("MIGRATION_CLEANUP_ERROR", "清理优化迁移文件失败: " + e.getMessage());
        }
    }


    // ==================== 失败记录查询和重试API ====================

    /**
     * 根据用户ID快速定位失败记录
     */
    @RequestMapping("perf/task/getFailureRecordByUserId")
    public SingleResponse getFailureRecordByUserId(@RequestParam String sessionId, @RequestParam String userId) {
        log.info("根据用户ID获取失败记录: sessionId={}, userId={}", sessionId, userId);

        try {
            OptimizedBitmapProgress progress = mingrationAppSvc.getOptimizedMigrationStatus(sessionId);
            if (progress == null) {
                return SingleResponse.buildFailure("MIGRATION_NOT_FOUND", "迁移任务不存在");
            }

            OptimizedBitmapProgress.FailureRecord failureRecord = progress.getFailureRecord(userId);
            if (failureRecord == null) {
                return SingleResponse.buildFailure("FAILURE_RECORD_NOT_FOUND", "该用户没有失败记录");
            }

            // 使用应用服务层的转换方法
            FailureRecordResult response = mingrationAppSvc.convertToFailureRecordResult(failureRecord);
            return SingleResponse.of(response);

        } catch (Exception e) {
            log.error("根据用户ID获取失败记录失败: sessionId={}, userId={}", sessionId, userId, e);
            return SingleResponse.buildFailure("GET_FAILURE_RECORD_BY_USERID_ERROR", "获取失败记录失败: " + e.getMessage());
        }
    }


    // ==================== 分离式任务管理API ====================


    /**
     * 获取批量处理性能指标
     */
    @RequestMapping("perf/task/getBatchProcessingMetrics")
    public SingleResponse getBatchProcessingMetrics(@RequestParam String sessionId) {
        log.info("获取批量处理性能指标: sessionId={}", sessionId);

        try {
            Map<String, Object> metrics = new HashMap<>();

            // 获取批量处理性能数据
            Map<String, BatchPerformanceMetrics> batchMetrics = null;
                    //mingrationAppSvc.getBatchProcessingMetrics(sessionId);

            if (batchMetrics != null && !batchMetrics.isEmpty()) {
                // 计算总体性能指标
                double avgSuccessRate = 0;
//                batchMetrics.values().stream()
//                        .mapToDouble(ScorerDataMingrationAppSvc.BatchPerformanceMetrics::getSuccessRate)
//                        .average()
//                        .orElse(0.0);

                double avgProcessingTime = 0;
//                batchMetrics.values().stream()
//                        .mapToDouble(ScorerDataMingrationAppSvc.BatchPerformanceMetrics::getAverageTimePerRecord)
//                        .average()
//                        .orElse(0.0);

                int totalProcessed = batchMetrics.values().stream()
                        .mapToInt(m -> (int) m.getTotalProcessed())
                        .sum();

                metrics.put("totalBatches", batchMetrics.size());
                metrics.put("averageSuccessRate", String.format("%.2f%%", avgSuccessRate * 100));
                metrics.put("averageProcessingTimeMs", String.format("%.2f", avgProcessingTime));
                metrics.put("totalProcessed", totalProcessed);
                metrics.put("batchMetrics", batchMetrics);
            } else {
                metrics.put("message", "暂无批量处理数据");
            }

            return SingleResponse.of(metrics);

        } catch (Exception e) {
            log.error("获取批量处理性能指标失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("GET_BATCH_METRICS_ERROR", "获取批量处理性能指标失败: " + e.getMessage());
        }
    }
}
