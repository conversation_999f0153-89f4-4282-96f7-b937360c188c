package com.polaris.kpi.controller.eval.task.emp;

import cn.com.polaris.kpi.EmpStaff;
import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.com.polaris.kpi.dept.LevelLeader;
import cn.com.polaris.kpi.eval.LevelManager;
import cn.com.polaris.kpi.eval.RaterNode;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson.JSONObject;
import com.perf.www.common.annotation.NoneLogin;
import com.perf.www.common.web.AccountBaseController;
import com.perf.www.common.web.ResponseInfo;
import com.polaris.acl.dept.domain.org.EmpOrganization;
import com.polaris.acl.dept.repository.DeptEmpDao;
import com.polaris.kpi.eval.app.task.appsvc.*;
import com.polaris.kpi.eval.app.task.dto.FixOkrRuleCmd;
import com.polaris.kpi.eval.app.temp.appsvc.StdTempAppSvc;
import com.polaris.kpi.eval.domain.task.entity.CycleEval;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.flow.DisplayEvalFlow;
import com.polaris.kpi.eval.domain.task.entity.flow.FlowRater;
import com.polaris.kpi.eval.domain.task.repo.EvaluateTaskRepo;
import com.polaris.kpi.eval.domain.task.repo.TaskUserRepo;
import com.polaris.kpi.eval.domain.task.type.FIxEnterScoreCmd;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.eval.infr.acl.ding.imp.ZnxcEvalResultPusher;
import com.polaris.kpi.org.domain.dept.entity.CompanyConf;
import com.polaris.kpi.org.domain.dept.type.TaskId;
import com.polaris.kpi.org.infr.company.dao.CompanyDaoImpl;
import com.polaris.kpi.org.infr.dept.dao.KpiOrgDao;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

@Slf4j
@RestController
public class FixSubmitScoreController extends AccountBaseController {
    @Autowired
    private SubmitScoreAppSvc submitScoreAppSvc;
    @Autowired
    private ScoreStageAppSvc scoreStageAppSvc;
    @Autowired
    private EvalTaskAppSvc taskAppSvc;
    @Autowired
    private EmpEvalAppSvc empEvalAppSvc;
    @Autowired
    private TaskUserRepo userRepo;
    @Autowired
    private CompanyDaoImpl companyDao;
    @Autowired
    private StdTempAppSvc stdAppSvc;
    @Autowired
    private CalibratedAppSvc calibratedAppSvc;

    //订正数据
    @RequestMapping("eval/task/fixBatchEnterScoring")
    public SingleResponse fixBatchEnterScoring(@RequestBody List<String> taskUserIds) {
        String companyId = getCompanyId();
        List<String> error = new ArrayList<>();
        taskUserIds.forEach(taskUserId -> {
            try {
                CompanyConf conf = companyDao.findCompanyConf(new TenantId(companyId));
                EvalUser taskUser = userRepo.getTaskUser(new TenantId(companyId), taskUserId);
                //CycleEval task = taskDao.getTaskBase(new TenantId(companyId), taskUser.getTaskId());
                taskAppSvc.enterScoring(conf, taskUser, new EmpId(taskUser.getCreatedUser()), Boolean.FALSE);
            } catch (Exception e) {
                error.add(taskUserId);
            }
        });
        log.info("失败的taskUserId:{} ", JSONObject.toJSONString(error));
        return SingleResponse.buildSuccess();
    }

    @RequestMapping("eval/task/fixEnterScoring")
    public SingleResponse batchEnterScoring(@RequestBody List<FIxEnterScoreCmd> cmds) {
        if (CollUtil.isEmpty(cmds)) {
            return SingleResponse.buildSuccess();
        }
        List<String> error = new ArrayList<>();
        cmds.forEach(cmd -> {
            try {
                TenantId tenantId = new TenantId(cmd.getCompanyId());
                String taskUserId = cmd.getTaskUserId();
                CompanyConf conf = companyDao.findCompanyConf(tenantId);
                EvalUser taskUser = userRepo.getTaskUser(tenantId, taskUserId);
                //CycleEval task = taskDao.getTaskBase(tenantId, taskUser.getTaskId());
                taskAppSvc.enterScoring(conf, taskUser, new EmpId(taskUser.getCreatedUser()), Boolean.FALSE);
            } catch (Exception e) {
                error.add(cmd.getTaskUserId());
            }
            log.info("失败的taskUserId:{} ", JSONObject.toJSONString(error));
        });
        return SingleResponse.buildSuccess();
    }

    //订正数据用的
    @RequestMapping("eval/task/reComputeScore")
    public ResponseInfo reComputeScore(@RequestParam TenantId companyId, String taskUserIds, Boolean fixItemScore) {
        List<String> taskUserList = StringUtils.isNotEmpty(taskUserIds) ? Arrays.asList(StringUtils.split(taskUserIds, ",")) : null;
        taskUserList.stream().forEach(id -> {
            //submitScoreAppSvc.reComputeScore(companyId, new TaskId(taskId), id);
            scoreStageAppSvc.reCompute(companyId, id, fixItemScore);
        });
        return ResponseInfo.success("");
    }
    //订正数据用的 发送评分待办
    @RequestMapping("eval/task/fixSendScoreToDo")
    public ResponseInfo fixSendScoreToDo(@RequestParam TenantId companyId, String taskUserId) {
        if (StrUtil.isEmpty(taskUserId) || Objects.isNull(companyId)) {
            return ResponseInfo.error("参数不能为空");
        }
        scoreStageAppSvc.fixSendScoreToDo(companyId, taskUserId);
        return ResponseInfo.success("");
    }

    //修复评分结束卡流程，进入校准阶段
//    @RequestMapping("eval/task/scoreEndFix")
//    public ResponseInfo scoreEndFix(@RequestBody ScoreEndFixCmd cmd) {
//        log.info("submitItemScore入参：{}", JSONUtil.toJsonPrettyStr(cmd));
//        submitScoreAppSvc.scoreEndFix(cmd);
//        return ResponseInfo.success("");
//    }


    //订正数据用的 resetNode =false 仅查看
    @RequestMapping("eval/task/reSetSuperNode")
    public ResponseInfo reSetSuperNode(@RequestParam TenantId companyId,
                                       @RequestParam String taskUserId,
                                       @RequestParam Boolean resetNode) {
        Collection<EvalScoreResult> evalScoreResults = submitScoreAppSvc.reCreateSuperiorNode(companyId, taskUserId, resetNode);
        return ResponseInfo.success(evalScoreResults);
    }

    //订正圆心发起考核后需要替换考核任务等级组 1.0.1
//    @RequestMapping("eval/task/replaceScoreRule")
//    public ResponseInfo replaceScoreRule(String taskUserId, String taskId,String ruleId) {
//        if (StrUtil.isEmpty(taskUserId) && StrUtil.isEmpty(taskId)) {
//            throw new RuntimeException("参数异常！");
//        }
//        submitScoreAppSvc.replaceScoreRule(getCompanyId(), taskUserId, taskId,ruleId);
//        return ResponseInfo.success("");
//    }


    @Autowired
    private ExecuteStageAppSvc executeStageAppSvc;

    //订正应用等级规则之后，taskUser需要修复
    @NoneLogin
    @RequestMapping("fix/task/fixBatchInitScoreRanges")
    public ResponseInfo fixBatchInitScoreRanges(String companyId, String taskUserId) {
        List<String> taskUserIds = StrUtil.splitTrim(taskUserId, ",");
        List<EvalUser> finisedEvals = new ArrayList<>();
        for (String userId : taskUserIds) {
            EvalUser taskUser = userRepo.getBaseTaskUser(new TenantId(companyId), userId);
            finisedEvals.add(taskUser);
        }
        executeStageAppSvc.fixBatchInitScoreRanges(companyId, finisedEvals);
        return ResponseInfo.success("");
    }

    @Autowired
    private EvalStageAppSvc stageAppSvc;
    @Autowired
    private EvaluateTaskRepo taskRepo;
    @Autowired
    private ZnxcEvalResultPusher znxcEvalResultPusher;

    @NoneLogin
    @RequestMapping("fix/task/outEvalResult")
    public ResponseInfo outEvalResult(String companyId, String taskUserId) {
        List<String> taskUserIds = StrUtil.splitTrim(taskUserId, ",");
        for (String userId : taskUserIds) {
            EvalUser taskUser = userRepo.getBaseTaskUser(new TenantId(companyId), userId);
            CycleEval taskBase = taskRepo.getCycleEval(new TenantId(companyId), new TaskId(taskUser.getTaskId()));
            stageAppSvc.outputEvalResult(taskBase, taskUser);
        }
        return ResponseInfo.success("");
    }

    @NoneLogin
    @RequestMapping("fix/task/thirdPlatDataPush")
    public ResponseInfo thirdPlatDataPush(String companyId, String taskUserId) {
        List<String> taskUserIds = StrUtil.splitTrim(taskUserId, ",");
        for (String userId : taskUserIds) {
            EvalUser taskUser = userRepo.getBaseTaskUser(new TenantId(companyId), userId);
            znxcEvalResultPusher.evalResultPush(taskUser);
        }
        return ResponseInfo.success("");
    }

    @NoneLogin
    @RequestMapping("tenant/ding/pushEvalResult")
    public ResponseInfo fixPushEvalResult(String companyId, String taskUserId) {
        stageAppSvc.fixPushEvalResult(companyId, taskUserId);
        return ResponseInfo.success("");
    }


    @RequestMapping("fix/task/itemOkrRule")
    public ResponseInfo fixItemOkrRule(@RequestBody FixOkrRuleCmd cmd) {
        for (String taskUserId : cmd.getTaskUserId()) {
            TenantId tenantId = new TenantId(getCompanyId());
            stdAppSvc.fixItemOkrRule(tenantId, cmd.getTempId(), taskUserId, getEmpId());
        }
        return ResponseInfo.success("");
    }

    @RequestMapping("fix/eval/scoreRule")
    public ResponseInfo fixScoreRule(@RequestBody FixOkrRuleCmd cmd) {
        for (String taskUserId : cmd.getTaskUserId()) {
            stdAppSvc.fixScoreRule(new TenantId(getCompanyId()), taskUserId, new EmpId(getEmpId()), cmd.isSendMsg());
        }
        return ResponseInfo.success("");
    }

    @RequestMapping("fix/eval/getIndexRaters")
    public ResponseInfo getIndexRaters(@RequestParam String taskUserId) {
        List<RaterNode> raterNodes = stageAppSvc.getIndexRaters(new TenantId(getCompanyId()), taskUserId);
        return ResponseInfo.success(raterNodes);
    }

    @RequestMapping("fix/eval/initIndexRaters")
    public SingleResponse initIndexRaters(@RequestBody List<String> taskUserIds) {
        stageAppSvc.initIndexRaters(new TenantId(getCompanyId()), taskUserIds);
        return SingleResponse.buildSuccess();
    }

    @RequestMapping("fix/eval/fixResultAuditReviewers")
    public SingleResponse fixResultAuditReviewers(String companyId, String taskUserIds) {
        List<String> ids = StrUtil.splitTrim(taskUserIds,",");
        TenantId tenantId = new TenantId(companyId);
        for (String id : ids) {
            DisplayEvalFlow flow = empEvalAppSvc.getFlow(tenantId, id, TalentStatus.RESULTS_AUDITING,
                    EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT.getScene());
            List<FlowRater>  ingNode = flow.getFirstNode();
            stageAppSvc.fixResultAuditReviewers(tenantId ,id,ingNode);
        }
        return SingleResponse.buildSuccess();
    }


    @NoneLogin
    @RequestMapping("fix/sendTodoToRater")
    public ResponseInfo sendTodoToRater(String companyId, String taskUserIds) {
        stageAppSvc.fixSendTodoToRater(companyId, taskUserIds);
        return ResponseInfo.success("");
    }

    @Autowired
    private KpiOrgDao kpiOrgDao;

    @RequestMapping("eval/emp/allSuperManager")
    public ResponseInfo allSuperManager(@RequestParam TenantId companyId, @RequestParam EmpId empId) {
        List<LevelManager> rs = kpiOrgDao.listEmpsManager(companyId, empId);
        return ResponseInfo.success(rs);
    }


    @Autowired
    private FixTaskAppSvc fixTaskAppSvc;
    @RequestMapping("eval/emp/allSuperManager2")
    public ResponseInfo allSuperManager2(@RequestParam TenantId companyId, @RequestParam EmpId empId, @RequestParam String orgId) {
        LevelLeader testLeader = fixTaskAppSvc.getTestLeader(companyId, empId.getId(), orgId);
        return ResponseInfo.success(testLeader);
    }
    @RequestMapping("eval/emp/fixListEmpByRoleId")
    public ResponseInfo fixListEmpByRoleId(@RequestParam TenantId companyId, @RequestParam String roleId, @RequestParam String orgId,@RequestParam String empId) {
        List<EmpStaff> empStaffs = fixTaskAppSvc.fixListEmpByRoleId(companyId, roleId, orgId, empId);
        return ResponseInfo.success(empStaffs);
    }


    @Autowired
    private DeptEmpDao deptEmpDao;

    @RequestMapping("eval/emp/listDeptWithNamePath")
    public ResponseInfo listDeptWithNamePath(@RequestParam TenantId companyId, @RequestParam String orgId) {
        List<EmpOrganization> organizations = deptEmpDao.listDeptWithNamePath(companyId, Arrays.asList(orgId));
        return ResponseInfo.success(organizations);
    }

}
