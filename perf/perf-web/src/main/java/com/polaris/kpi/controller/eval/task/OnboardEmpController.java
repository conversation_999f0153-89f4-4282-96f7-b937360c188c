package com.polaris.kpi.controller.eval.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.perf.www.biz.task.EvaluateTaskProgressBiz;
import com.perf.www.common.web.AccountBaseController;
import com.perf.www.vo.task.progress.PerfEvaluateTaskProgressExecuteOrderVO;
import com.perf.www.vo.task.progress.PerfEvaluateTaskProgressExecutorVO;
import com.perf.www.vo.task.progress.PerfEvaluateTaskProgressVO;
import com.polaris.acl.dept.appsvc.DeptEmpAppSvc;
import com.polaris.acl.dept.domain.post.EmpRank;
import com.polaris.kpi.eval.app.task.appsvc.OnboardEmpAppSvc;
import com.polaris.kpi.eval.domain.task.entity.onbord.OnboardTimerConf;
import com.polaris.kpi.eval.domain.task.entity.onbord.OnboardTimerItem;
import com.polaris.kpi.eval.infr.task.ppojo.newemp.OnboardTimerConfDo;
import com.polaris.kpi.eval.infr.task.ppojo.newemp.ScoringSpendPo;
import com.polaris.kpi.eval.infr.task.query.newemp.ScoringSpendQuery;
import com.polaris.kpi.org.app.org.appsvc.HrmSolutionAppSvc;
import com.polaris.kpi.org.infr.company.ppojo.KpiNempOrderCntPo;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.lufei.ibatis.mapper.PagedList;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.controller.eval.task
 * @Author: lufei
 * @CreateTime: 2023-02-27  18:15
 * @Description: 新人考核任务controller
 * @Version: 1.0
 */
@Slf4j
@RestController
public class OnboardEmpController extends AccountBaseController {
    @Autowired
    private OnboardEmpAppSvc onboardEmpAppSvc;
    @Autowired
    private EvaluateTaskProgressBiz taskProgressBiz;
    @Autowired
    private HrmSolutionAppSvc hrmSolutionAppSvc;
    @Autowired
    private DeptEmpAppSvc deptEmpAppSvc;

    //减少1.0合并到2.0的难度先放这
    @RequestMapping("org/jobRanks")
    public SingleResponse jobRanks(String name) {
        List<EmpRank> jobRanks = deptEmpAppSvc.jobRanks(new TenantId(getCompanyId()), name);
        return SingleResponse.of(jobRanks);
    }

    @RequestMapping("eval/onboard/timerConf")
    public SingleResponse timerConf() {
        OnboardTimerConfDo conf = onboardEmpAppSvc.timerConf(getCompanyId());
        return SingleResponse.of(conf);
    }

    @RequestMapping("eval/onboard/saveTimerConf")
    public SingleResponse saveTimerConf(@RequestBody OnboardTimerConf conf) {
        conf.op(getCompanyId(), getEmpId());
        onboardEmpAppSvc.saveTimerConf(conf);
        return SingleResponse.of(conf);
    }


    @RequestMapping("eval/onboard/pagedScoringSpend")
    public MultiResponse pagedScoringSpend(@RequestBody ScoringSpendQuery qry) {
        qry.op(getCompanyId(), getEmpId());
        PagedList<ScoringSpendPo> data = onboardEmpAppSvc.pagedScoringSpend(qry);
        for (ScoringSpendPo da : data) {
            List<PerfEvaluateTaskProgressVO> scoring = taskProgressBiz.listTaskProgress(getCompanyId(), da.getTaskId(), da.getOrgId(), da.getEmpId(), "scoring", null);
            List<String> rates = covertRaters(scoring);
            da.setRaters(rates);
        }
        return MultiResponse.of(data.getData(), data.getPageNo(), data.getTotalRow(), data.getPageSize());
    }

    private List<String> covertRaters(List<PerfEvaluateTaskProgressVO> scoring) {
        if (CollUtil.isEmpty(scoring)) {
            return Collections.emptyList();
        }
        Set<String> set = new HashSet();
        for (PerfEvaluateTaskProgressVO progressVO : scoring) {
            List<PerfEvaluateTaskProgressExecuteOrderVO> orderList = progressVO.getExecuteOrderList();
            if (CollUtil.isEmpty(orderList)) {
                continue;
            }
            for (PerfEvaluateTaskProgressExecuteOrderVO executeOrderVO : orderList) {
                if (CollUtil.isEmpty(executeOrderVO.getExecutorList())) {
                    continue;
                }
                for (PerfEvaluateTaskProgressExecutorVO rater : executeOrderVO.getExecutorList()) {
                    set.add(rater.getEmpName());
                }
            }
        }
        return new ArrayList<>(set);
    }

    //直至完成任务确认 启动提醒
    @RequestMapping("eval/onboard/startConfirmTimer")
    @Scheduled(cron = "0 0/10 * * * ?")
    public SingleResponse startConfirmTimer() {
        String tid = IdUtil.fastSimpleUUID();
        MDC.put("tid", tid);
        List<String> companyIds = onboardEmpAppSvc.listOpenTimerCompanyIds();
        if (CollUtil.isEmpty(companyIds)) {
            return SingleResponse.of(0);
        }
        int count = 0;
        for (String companyId : companyIds) {
            List<OnboardTimerItem> timerItems = onboardEmpAppSvc.trylockTimerConf(companyId);
            if (CollUtil.isEmpty(timerItems)) {
                continue;
            }
            log.debug("开始启动新人提醒:{},配置数:{}", companyId, timerItems.size());
            for (OnboardTimerItem timerItem : timerItems) {
                MDC.put("tid", tid + "_" + timerItem.getId() + "_" + companyId);
                if (!timerItem.getConf().isAfterRunTime(DateTime.now())) {
                    log.debug("未到执行时间:{}", timerItem.getConf().getRunTime());
                    onboardEmpAppSvc.clearlockTimerItem(companyId, timerItem.getId());
                    continue;
                }
                count++;
                //直至完成任务确认 启动提醒
                if (timerItem.isConfirmTimer() && !timerItem.hasRunToday()) {
                    onboardEmpAppSvc.executeConfirmTimeOut(timerItem);
                    continue;
                }
                //评分超时每天提醒
                if (timerItem.isScoringEachTimer() && !timerItem.hasRunToday()) {
                    onboardEmpAppSvc.executScoringEachTimer(timerItem);
                    continue;
                }
                //评分超时单次提醒
                if (timerItem.isScoringOnceTimer()) {
                    onboardEmpAppSvc.executeScoringOnceTimer(timerItem);
                }
            }
        }
        return SingleResponse.of(count);
    }
    //新人额度及使用数量
    @RequestMapping("eval/onboard/getOrderInfo")
    public SingleResponse getOrderInfo() {
        KpiNempOrderCntPo orderInfo = hrmSolutionAppSvc.getOrderInfo(new TenantId(getCompanyId()));
        return SingleResponse.of(orderInfo);
    }

}
