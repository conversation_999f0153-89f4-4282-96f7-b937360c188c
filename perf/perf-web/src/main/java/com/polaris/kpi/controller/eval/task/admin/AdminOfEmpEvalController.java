package com.polaris.kpi.controller.eval.task.admin;

import cn.com.polaris.kpi.EmpStaff;
import cn.com.polaris.kpi.ShowSupOrg;
import cn.com.polaris.kpi.company.MsgSceneEnum;
import cn.com.polaris.kpi.eval.OperationTypeEnum;
import cn.com.polaris.kpi.eval.Rater;
import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.com.polaris.kpi.temp.TempId;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.perf.www.common.annotation.NoneLogin;
import com.perf.www.common.constant.BusinessConstant;
import com.perf.www.common.em.ExcelTemplateEnum;
import com.perf.www.common.excel.ExcelUtil;
import com.perf.www.common.utils.date.DateTimeUtils;
import com.perf.www.common.utils.file.FileUtil;
import com.perf.www.common.web.AccountBaseController;
import com.perf.www.common.web.ResponseInfo;
import com.perf.www.dto.PerfEvaluateTaskUserDTO;
import com.polaris.acl.dept.face.AppAcl;
import com.polaris.acl.kpi.eval.domain.EvalEmp;
import com.polaris.kpi.common.KpiI18NException;
import com.polaris.kpi.eval.app.confirm.appsvc.ConfirmStageAppSvc;
import com.polaris.kpi.eval.app.confirm.dto.OnlyConfirmtemCmd;
import com.polaris.kpi.eval.app.task.appsvc.*;
import com.polaris.kpi.eval.app.task.dto.*;
import com.polaris.kpi.eval.app.task.dto.admin.*;
import com.polaris.kpi.eval.app.task.dto.eval.*;
import com.polaris.kpi.eval.app.task.dto.sumit.SubmitScoreCmd;
import com.polaris.kpi.eval.app.task.dto.sumit.SubmitScoreV3Cmd;
import com.polaris.kpi.eval.app.temp.appsvc.StdTempAppSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.TableAsScoreStageRuleDmSvc;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.HistoryEvalImport.HistoryEvalImport;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.admineval.EmpEvalChangeResult;
import com.polaris.kpi.eval.domain.task.entity.empeval.BindEvalResult;
import com.polaris.kpi.eval.domain.task.entity.empeval.EditEmpEvalOrgInfoRs;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalOperation;
import com.polaris.kpi.eval.domain.task.entity.empeval.TaskExecuteBatch;
import com.polaris.kpi.eval.domain.task.entity.emptable.EmpEvalTable;
import com.polaris.kpi.eval.domain.task.event.msg.CancelTodoEvent;
import com.polaris.kpi.eval.domain.task.event.talent.TalentEvalConfirmedEvent;
import com.polaris.kpi.eval.domain.task.repo.AdminTaskOperationRepo;
import com.polaris.kpi.eval.domain.task.repo.EmpEvalRuleRepo;
import com.polaris.kpi.eval.domain.task.repo.TaskUserRepo;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.eval.domain.temp.entity.std.StdTemp;
import com.polaris.kpi.eval.infr.task.ImportEvalEmpPo;
import com.polaris.kpi.eval.infr.task.ppojo.*;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.AdminTaskParseAddEmpPo;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.*;
import com.polaris.kpi.eval.infr.task.query.*;
import com.polaris.kpi.eval.infr.task.query.empeval.*;
import com.polaris.kpi.org.app.org.appsvc.HrmSolutionAppSvc;
import com.polaris.kpi.org.domain.dept.type.TaskId;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import io.lettuce.core.dynamic.annotation.Param;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.lufei.ibatis.mapper.PagedList;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;


/**
 * @Author: lufei
 * @CreateTime: 2022-08-26  15:10
 * @Description: 管理员管理员工任务用例
 * @Version: 1.0
 */
@Slf4j
@RestController
public class AdminOfEmpEvalController extends AccountBaseController {
    @Autowired
    private AdminTaskAppSvc taskAppSvc;
    @Autowired
    private EmpEvalAppSvc empEvalAppSvc;
    @Autowired
    private EmpTableAppSvc empTableAppSvc;
    @Autowired
    private EvalTaskAppSvc evalTaskAppSvc;
    @Autowired
    private HrmSolutionAppSvc hrmSolutionAppSvc;
    @Autowired
    private StdTempAppSvc stdAppSvc;
    @Autowired
    private EvalStageAppSvc evalStageAppSvc;
    @Autowired
    private ConfirmStageAppSvc confirmStageAppSvc;

    @Autowired
    private TaskUserRepo userRepo;
    @Autowired
    private EmpEvalRuleRepo empEvalRuleRepo;
    @Autowired
    private SubmitScoreAppSvc submitScoreAppSvc;
    @Autowired
    private ScoreStageAppSvc scoreStageAppSvc;
    @Autowired
    private AdminTaskOperationRepo adminTaskOperationRepo;
    @Autowired
    private FixTaskAppSvc fixTaskAppSvc;
    @Autowired
    private ResultAuditFlowAppSvc resultAuditFlowAppSvc;
    @Autowired
    private AppAcl appAcl;
    @Autowired
    private HistoryEvalImportAppSvc historyEvalImportAppSvc;

    //202员工任务存在性检查checkExistAtCycle
    @RequestMapping("admin/eval/checkExistAtCycle")
    public SingleResponse checkExistAtCycle(@RequestBody EmpEvalAtCycleQuery query) {
        query.setTenantId(new TenantId(getCompanyId()));
        List<EvalEmp> existed = empEvalAppSvc.listExistAtCycle(query);
        return SingleResponse.of(existed);
    }

    //管理任务下的员工任务列表
    @RequestMapping("admin/eval/pagedSimpleEmpEval")
    public MultiResponse pagedSimpleEmpEval(@RequestBody EmpEvalAtTaskQuery query) {
        query.accOp(new TenantId(getCompanyId()), getEmpId(), getAdminType());
        PagedList<SimpleEmpEvalPo> empEvals = empEvalAppSvc.pagedSimpleEmpEval(query);
        return MultiResponse.of(empEvals.getData(), empEvals.getPageNo(), empEvals.getTotalRow(), empEvals.getPageSize());
    }

    //206 指定管理任务下,员工考核任务列表(未制定)
    @RequestMapping("admin/eval/pagedEmpEvalDraft")
    public MultiResponse pagedEmpEval(@RequestBody EmpEvalAtTaskQuery query) {
        if (query.paramIllegal()) {
            return MultiResponse.buildFailure("1010", "taskId cycleId all empty");
        }
        query.accOp(new TenantId(getCompanyId()), getEmpId(), getAdminType());
        PagedList<RuleEmpEvalDraftPo> empEvals = empEvalAppSvc.pagedEmpEvalDraft(query);
        return MultiResponse.of(empEvals.getData(), empEvals.getPageNo(), empEvals.getTotalRow(), empEvals.getPageSize());
    }

    @RequestMapping("admin/eval/showSupOrg")
    public ResponseInfo showSupOrg(@RequestBody List<ShowSupOrg> supOrgs) {
        return ResponseInfo.success(empEvalAppSvc.showSupOrg(new TenantId(getCompanyId()), supOrgs));
    }

    //206.2  指定管理任务下,员工考核任务列表（已发起）
    @RequestMapping("admin/eval/pagedEmpEvalByStatus")
    public MultiResponse pagedEmpEvalByStatus(@RequestBody EmpEvalAtTaskQuery2 query) {
        query.accOp(getCompanyId(), getEmpId(), getAdminType());
        PagedList<EmpEvalByStatusPo> empEvals = empEvalAppSvc.pagedEmpEvalByStatus(query);
        return MultiResponse.of(empEvals.getData(), empEvals.getPageNo(), empEvals.getTotalRow(), empEvals.getPageSize());
    }

    //考核表名称集合
    @RequestMapping("admin/eval/listRuleNames")
    public SingleResponse listRuleNames(@RequestBody RuleNameAtTaskQuery query) {
        query.accOp(getCompanyId(), getEmpId(), getAdminType());
        return SingleResponse.of(empEvalAppSvc.listRuleNames(query));
    }

    //考核表名称集合
    @RequestMapping("admin/eval/listEvalGroupNames")
    public SingleResponse listEvalGroupNames(@RequestBody EvalGroupNameAtTaskQuery query) {
        query.accOp(getCompanyId(), getEmpId(), getAdminType());
        return SingleResponse.of(empEvalAppSvc.listEvalGroupNames(query));
    }

    //指定管理任务下,员工考核任务列表（全部）
    @RequestMapping("admin/eval/pagedEmpEval")
    public MultiResponse pagedEmpEval(@RequestBody EmpEvalAtTaskQuery3 query) {
        query.accOp(getCompanyId(), getEmpId(), getAdminType());
        PagedList<RuleEmpEvalPo> empEvalPos = empEvalAppSvc.pagedEmpEval(query);
        return MultiResponse.of(empEvalPos.getData(), empEvalPos.getPageNo(), empEvalPos.getTotalRow(), empEvalPos.getPageSize());
    }

    //206.2 旧的兼容  指定管理任务下,员工考核任务列表
    @RequestMapping("perf/task/queryTaskUserList")
    public MultiResponse pagedEmpEvalByStatusOld(String keyword, String cycleId, String taskId, String taskStatus,
                                                 String reviewerId, Boolean isMyCreated, String empId, String levelName,
                                                 Integer pageSize, int pageNo, String sortOrder, String isOrgEval) {
        EmpEvalAtTaskQuery2 query = new EmpEvalAtTaskQuery2();
        query.setKeyword(keyword);
        query.setEmpIds(StrUtil.split(empId, ','));
        query.setCycleId(cycleId);
        query.setTaskIds(StrUtil.split(taskId, ','));
        query.setReviewerIds(StrUtil.split(reviewerId, ','));
        query.setIsMyCreated(isMyCreated);
        query.setPageNo(pageNo);
        query.setPageSize(pageSize);
        query.setSortOrder(sortOrder);
        query.setLevelNames(StrUtil.split(levelName, ','));
        query.setTaskStatuss(TalentStatus.startedStatus());
        query.setIsOrgEval(isOrgEval);
        query.accOp(getCompanyId(), getEmpId(), getAdminType());
        return pagedEmpEvalByStatus(query);
    }

    //207解析员工到管理任务parseAddEvalRepeatEmp ,批量/单个/
    @RequestMapping("admin/eval/parseAddEvalRepeatEmp")
    public SingleResponse parseAddEvalEmp(@RequestBody AddEvalEmpCmd cmd) {
        cmd.setOpEmpId(new EmpId(getEmpId()));
        cmd.accOp(new TenantId(getCompanyId()), getEmpId(), getAdminType());
        AdminTaskParseAddEmpPo repeatEmp = taskAppSvc.parseAddEvalRepeatEmp(cmd);
        log.info("parseAddEvalRepeatEmp:{}", JSONUtil.toJsonStr(repeatEmp));
        return SingleResponse.of(repeatEmp);
    }

    //207增加员工到管理任务addEvalEmp ,批量/单个/
    @RequestMapping("admin/eval/addEvalEmp")
    public SingleResponse addEvalEmp(@RequestBody AddEvalEmpCmd cmd) {
        TenantId tenantId = new TenantId(getCompanyId());
        cmd.setTenantId(tenantId);
        cmd.setOpEmpId(new EmpId(getEmpId()));
        cmd.accOp(tenantId, getEmpId(), getAdminType());
        boolean isExceed = taskAppSvc.addEvalEmpsNew(cmd);
        if (isExceed) {
            return SingleResponse.buildFailure("10010", "您因为是免费版本,只能添加六位考核人员");
        }
        return SingleResponse.buildSuccess();
    }

    //测试带入默认考核表forDefualtTable
    @RequestMapping("test/admin/eval/forDefualtTable")
    public SingleResponse forDefualtTable(String taskUserId, String empId) {
        String companyId = getCompanyId();
        EmpEvalTable table = empTableAppSvc.getDefaultEmpTable(companyId, empId, null);
        TableAsScoreStageRuleDmSvc ruleDmSvc = new TableAsScoreStageRuleDmSvc(table);
        ScoreStageRule scoreStageRule = ruleDmSvc.asScoreRule(taskUserId);
        ScoreStageRuleCmd cmd = new ScoreStageRuleCmd();
        cmd.setOperator(companyId, getEmpId(), getAdminType());
        cmd.setScoreStageRule(scoreStageRule);
        cmd.checkAddParam();
        empEvalAppSvc.addScoreStageRule(cmd);
        return SingleResponse.buildSuccess();
    }


    //207.2 删除员工到管理任务removeEvalEmp ,批量/单个/
    @RequestMapping("admin/eval/removeEvalEmp")
    public SingleResponse removeEvalEmp(@RequestBody RemoveEvalEmpCmd cmd) {
        cmd.accOp(new TenantId(getCompanyId()), getEmpId(), getAdminType());
        taskAppSvc.removeEvalEmp(cmd);
        return SingleResponse.buildSuccess();
    }

    // ---------------------------------------------------------------------------------------------------------

    //预览考核规则getEmpEvalRule
    @RequestMapping("admin/eval/getScoreStageRule")
    public SingleResponse getScoreStageRule(@RequestParam String taskUserId) {
        TenantId tenantId = new TenantId(getCompanyId());
        ScoreStageRule ofUser = empEvalAppSvc.getScoreStageRule(tenantId, taskUserId);
        return SingleResponse.of(ofUser);
    }

    //管理员新加考核表 评分阶段的规则addScoreStageRule
    @Deprecated
    @RequestMapping("admin/eval/addScoreStageRule")
    public SingleResponse addScoreStageRule(@RequestBody ScoreStageRuleCmd cmd) {
        cmd.setOperator(getCompanyId(), getEmpId(), getAdminType());
        cmd.checkAddParam();
        if (empEvalAppSvc.existRule(cmd.getScoreStageRule().getEmpEvalId())) {
            return SingleResponse.buildFailure("10002", "已存在规则");
        }
        empEvalAppSvc.addScoreStageRule(cmd);
        return SingleResponse.buildSuccess();
    }

    //管理员替换考核表-replaceEvalTable
    @RequestMapping("admin/eval/replaceEvalTable")
    public SingleResponse replaceTable(@RequestParam String taskUserId,
                                       @RequestParam Boolean createRule,//新建考核规则
                                       @RequestParam String tableId) {
        ScoreStageRuleCmd cmd = new ScoreStageRuleCmd();
        cmd.setOperator(getCompanyId(), getEmpId(), getAdminType());
        EmpEvalTable empTable = empTableAppSvc.getEmpTable(getCompanyId(), tableId);
        TableAsScoreStageRuleDmSvc ruleDmSvc = new TableAsScoreStageRuleDmSvc(empTable);
        ScoreStageRule scoreStageRule = ruleDmSvc.asScoreRule(taskUserId);
        cmd.setScoreStageRule(scoreStageRule);
        cmd.checkAddParam();
        //校验考核是否在制定中
        if (!empEvalAppSvc.onDrawUpIng(new TenantId(getCompanyId()), taskUserId)) {
            return SingleResponse.buildFailure("40010", "被考核人考核状态已发生改变，请刷新页面重新操作！！");
        }
        if (createRule) {
            empEvalAppSvc.addScoreStageRule(cmd);
        } else {
            cmd.setOpType(1);
            empEvalAppSvc.editScoreStageConf(cmd);
        }
        return SingleResponse.buildSuccess();
    }

    //管理员新加自定义考核规则addCustomScoreStageRule
    @RequestMapping("admin/eval/addCustomScoreStageRule")
    public SingleResponse addCustomScoreStageRule(@RequestBody ScoreStageRuleCmd cmd) {
        cmd.setOperator(getCompanyId(), getEmpId(), getAdminType());
        if (empEvalAppSvc.existRule(cmd.getScoreStageRule().getEmpEvalId())) {
            return SingleResponse.buildFailure("10002", "已存在规则");
        }
        empEvalAppSvc.addScoreStageRule(cmd);
        return SingleResponse.buildSuccess();
    }

    //编辑考核任务中人员评分阶段的配置（指标使用相同的评分配置）
    @RequestMapping({"admin/eval/editScoreStageConf", "admin/eval/editCustomScoreStageConf"})
    public SingleResponse editScoreStageConf(@RequestBody ScoreStageRuleCmd cmd) {
        cmd.setOperator(getCompanyId(), getEmpId(), getAdminType());
        log.debug("editScoreStageConf.cmd入参={}", JSONUtil.toJsonStr(cmd));
        if (cmd.afterStage(TalentStatus.SCORING)) {
            return SingleResponse.buildFailure("10003", "员工任务该阶段无法修改配置");
        }
        cmd.synRuleName();
        cmd.setOpType(2);
        TalentEvalConfirmedEvent event = empEvalAppSvc.editScoreStageConf(cmd);

        if (Objects.equals(cmd.getTaskStatus(), TalentStatus.CONFIRMED.getStatus())) {
            event.publish();
        }
        return SingleResponse.buildSuccess();
    }

    ////编辑考核表评分阶段的配置（指标使用不同的评分规则）
    //@RequestMapping("admin/eval/editCustomScoreStageConf") // todo 自定义可以编辑评分人
    //public SingleResponse editCustomScoreStageConf(@RequestBody ScoreStageRuleCmd cmd) {
    //    cmd.setOperator(getCompanyId(), getEmpId(), getAdminType());
    //    if (cmd.afterStage(TalentStatus.SCORING)) {
    //        return SingleResponse.buildFailure("10003", "员工任务该阶段无法修改配置");
    //    }
    //    cmd.synRuleName();
    //    empEvalAppSvc.editScoreStageConf(cmd);
    //    return SingleResponse.buildSuccess();
    //}

    //流程订正
    @RequestMapping("fix/eval/fixScoreStageConf")
    @NoneLogin
    public SingleResponse fixScoreStageConf(String companyId, String taskUserId) {

        empEvalAppSvc.fixScoreStageConf(companyId, taskUserId);
        return SingleResponse.buildSuccess();
    }

    //预览考核规则流程
    @RequestMapping("admin/eval/getEmpEvalRuleFlow")
    public SingleResponse getEmpEvalRuleFlow(@RequestParam String taskUserId) {
        TenantId tenantId = new TenantId(getCompanyId());
        EmpEvalRuleFlow flow = empEvalAppSvc.getEmpEvalRuleFlow(tenantId, taskUserId);
        return SingleResponse.of(flow);
    }

    //编辑考核流程-确认阶段配置
    @RequestMapping("admin/eval/editConfirmConf")
    public SingleResponse editConfirmConf(@RequestBody AffirmTaskConfCmd cmd) {
        log.info("editConfirmConf入参：{}", JSONObject.toJSONString(cmd));
        cmd.setOperator(getCompanyId(), getEmpId(), getAdminType());
        cmd.checkConfirmParam();
        empEvalAppSvc.editConfirmConf(cmd);
        return SingleResponse.buildSuccess();
    }

    //编辑考核流程-执行阶段配置
    @RequestMapping("admin/eval/editExeIndiConf")
    public SingleResponse editExeIndiConf(@RequestBody EditExeIndiConfCmd cmd) {
        log.info("editExeIndiConf入参：{}", JSONObject.toJSONString(cmd));
        cmd.setOperator(getCompanyId(), getEmpId(), getAdminType());
        empEvalAppSvc.editExeIndiConf(cmd);
        return SingleResponse.buildSuccess();
    }

    //编辑考核流程-完成值审核阶段配置
    @RequestMapping("admin/eval/editFinishedValueAuditConf")
    public SingleResponse editFinishedValueAuditConf(@RequestBody FinishedValueAuditConfCmd cmd) {
        log.info("editFinishedValueAuditConf入参：{}", JSONObject.toJSONString(cmd));
        cmd.setOperator(getCompanyId(), getEmpId(), getAdminType());
        cmd.checkConfirmParam();
        empEvalAppSvc.editFinishedValueAuditConf(cmd);
        return SingleResponse.buildSuccess();
    }

    //编辑考核流程-校准阶段配置
    @RequestMapping("admin/eval/editAuditResultConf")
    public SingleResponse editAuditResultConf(@RequestBody AuditResultConfCmd cmd) {
        log.info("editAuditResultConf入参：{}", JSONObject.toJSONString(cmd));
        cmd.setOperator(getCompanyId(), getEmpId(), getAdminType());
        cmd.checkAuditResultParam();
        empEvalAppSvc.editAuditResultConf(cmd);
        return SingleResponse.buildSuccess();
    }

    //编辑考核流程-绩效面谈阶段配置
    @RequestMapping("admin/eval/editInterviewConf")
    public SingleResponse editInterviewConf(@RequestBody InterviewConfCmd cmd) {
        log.info("editInterviewConf入参：{}", JSONObject.toJSONString(cmd));
        cmd.setOperator(getCompanyId(), getEmpId(), getAdminType());
        empEvalAppSvc.editInterviewConf(cmd);
        return SingleResponse.buildSuccess();
    }

    //编辑考核流程-公示阶段配置
    @RequestMapping("admin/eval/editPubResultConf")
    public SingleResponse editPubResultConf(@RequestBody PublishResultConfCmd cmd) {
        log.info("editPubResultConf入参：{}", JSONObject.toJSONString(cmd));
        cmd.setOperator(getCompanyId(), getEmpId(), getAdminType());
        empEvalAppSvc.editPubResultConf(cmd);
        return SingleResponse.buildSuccess();
    }

    //编辑考核流程-申诉阶段配置
    @RequestMapping("admin/eval/editAppealConf")
    public SingleResponse editAppealConf(@RequestBody AppealConfCmd cmd) {
        log.info("editAppealConf入参：{}", JSONObject.toJSONString(cmd));
        cmd.setOperator(getCompanyId(), getEmpId(), getAdminType());
        empEvalAppSvc.editAppealConf(cmd);
        return SingleResponse.buildSuccess();
    }

    @RequestMapping("admin/eval/getRaterWithTaskAdmin")
    public SingleResponse getRaterWithTaskAdmin(@RequestParam String taskUserId) {
        if (StringUtils.isEmpty(taskUserId)){
            throw new KpiI18NException("缺少必要参数，请检查！");
        }
        Rater rater = empEvalAppSvc.getRaterWithTaskAdmin(getCompanyId(),taskUserId);
        return SingleResponse.of(rater);
    }

    //编辑考核流程-申诉阶段配置
    @RequestMapping("admin/eval/editDeadLineConf")
    public SingleResponse editDeadLineConf(@RequestBody DeadLineLineCmd cmd) {
        log.info("editDeadLineConf入参：{}", JSONObject.toJSONString(cmd));
        cmd.setOperator(getCompanyId(), getEmpId(), getAdminType());
        empEvalAppSvc.editDeadLineConf(cmd);
        return SingleResponse.buildSuccess();
    }

    //214 是否可复用指定类型上次考核canUseLastEvalTask
    @RequestMapping("admin/eval/canUseLastEvalTask")
    public SingleResponse canUseLastEvalTask(@RequestBody HasLastEmpEvalQuery query) {
        TenantId tenantId = new TenantId(getCompanyId());
        query.setTenantId(tenantId);
        query.checkParam();
        List<EvalEmp> evalEmps = empEvalAppSvc.canUseLastEmpEval(query);
        return SingleResponse.of(evalEmps);
    }


    //215是复用上次考核evalRuleFromLast
    @RequestMapping("admin/eval/evalRuleFromLast")
    public SingleResponse evalRuleFromLast(@RequestBody UseLastEvalCmd cmd) {
        cmd.setOperator(getCompanyId(), getEmpId(), getAdminType());
        EvalUser user = empEvalAppSvc.getBaseTaskUser(cmd.getTenantId(), cmd.getTaskUserIds().get(0));
        AdminTask curAdminTask = empEvalAppSvc.getAdminTask(cmd.getTenantId(), user.getTaskId());
        cmd.setCurAdminTask(curAdminTask);
        //校验考核是否在制定中
        if (!TalentStatus.DRAW_UP_ING.getStatus().equals(user.getTaskStatus())) {
            return SingleResponse.buildFailure("40010", "被考核人考核状态已发生改变，请刷新页面重新操作！！");
        }
        for (String taskUserId : cmd.getTaskUserIds()) {
            cmd.setCurUserId(taskUserId);
            empEvalAppSvc.evalRuleFromLast(cmd);
        }
        return SingleResponse.buildSuccess();
    }

    //217 应用模板
    @RequestMapping("admin/eval/evalRuleFromTemp")
    public SingleResponse applyTemp(@RequestBody ApplyTempCmd cmd) {
        log.info("参数:{}", JSONUtil.toJsonStr(cmd));
        cmd.opEmpId(new TenantId(getCompanyId()), new EmpId(getEmpId()));
        StdTemp stdTemp = stdAppSvc.getStdTempForApply(new TenantId(getCompanyId()), new TempId(cmd.getTempId()));
        cmd.setTemp(stdTemp);
        empEvalAppSvc.applyTemp(cmd);
        return SingleResponse.buildSuccess();
    }


    //219发起考核:考核表-新接口,启动执行考核startExecuteEval
    @RequestMapping("admin/eval/startExecuteEval")
    public SingleResponse startExecuteEval(@RequestBody StartEmpEvalCmd cmd) {
        TenantId tenantId = new TenantId(getCompanyId());
        cmd.setTenantId(tenantId);
        cmd.setOpEmpId(new EmpId(getEmpId()));
        empEvalAppSvc.startExecuteEval(cmd).publish();
        return SingleResponse.buildSuccess();
    }

    //219发起考核:考核表-新接口,启动执行考核startExecuteEval
    @RequestMapping("admin/eval/batchStartExecuteEval")
    public SingleResponse batchStartExecuteEval(@RequestBody BatchStartEmpEvalCmd cmd) {
        cmd.setCompanyId(getCompanyId());
        cmd.setOpEmpId(new EmpId(getEmpId()));
        TaskExecuteBatch batch = taskAppSvc.saveExecuteBatch(cmd);
        cmd.setBatchId(batch.getId());
        empEvalAppSvc.batchStartExecuteEval(cmd);
        return SingleResponse.of(batch);
    }


    //考核任务异常列表
    @RequestMapping("perf/task/pagedEvalError")
    public MultiResponse pagedEvalError(@RequestBody EmpEvalErrorQuery query) {
        query.setCompanyId(getCompanyId());
        query.setOpEmpId(getEmpId());
        PagedList<PerfEvaluateTaskUserDTO> errors = empEvalAppSvc.pagedEvalError(query);
        return MultiResponse.of(errors.getData(), errors.getPageNo(), errors.getTotalRow(), errors.getPageSize());
    }

    //220考核表日志列表查询
    @RequestMapping("admin/eval/pagedEmpEvalOperation")
    public MultiResponse pagedEmpEvalOperation(@RequestBody EmpEvaOpLogQuery query) {
        TenantId tenantId = new TenantId(getCompanyId());
        query.setTenantId(tenantId);
        PagedList<EmpEvalOperation> rs = empEvalAppSvc.pagedEmpEvalOperation(query);
        return MultiResponse.of(rs.getData(), rs.getPageNo(), rs.getTotalRow(), rs.getPageSize());
    }

    // 替换 queryTaskDetailByEmp
    @RequestMapping({"perf/task/queryTaskDetailByEmp", "admin/eval/getEmpEvalDetail"})
    public ResponseInfo getEmpEvalDetail(String taskBaseId, String empId, String talentEvalId) {
        if (StrUtil.isEmpty(talentEvalId) && (StrUtil.isEmpty(taskBaseId) || StrUtil.isEmpty(empId))) {
            throw new KpiI18NException(" param is empty ", "请求参数异常！");
        }

        EmpEvalDetailPo rs = empEvalAppSvc.empEvalWithOp(new TenantId(getCompanyId()), talentEvalId, taskBaseId, empId, getEmpId(), getAdminType());
        return ResponseInfo.success(rs);
    }


    @RequestMapping("perf/task/listActionUpdateInfo")
    public ResponseInfo listActionUpdateInfo(String actionId) {
        OkrQuery query = new OkrQuery();
        query.setEmpId(getEmpId());
        query.setCompanyId(getCompanyId());
        query.setActionId(actionId);
        return ResponseInfo.success(empEvalAppSvc.listActionUpdateInfos(query));
    }

    //替换taskBiz：sendAutoItemInputFinishValueMsg(String taskId, String empId)  要通知前端参数替换成taskUserId后放开该接口
    @RequestMapping("perf/task/sendAutoItemInputFinishValueMsg")
    public ResponseInfo sendAutoItemInputFinishValueMsg(String taskUserId) {
        empEvalAppSvc.sendAutoItemInputFinishValueMsg(new TenantId(getCompanyId()), taskUserId);
        return ResponseInfo.success(null);
    }

    //替换taskBiz：sendAutoItemInputFinishValueMsg(String taskId, String empId)  要通知前端参数替换成taskUserId后放开该接口
    @RequestMapping("fix/task/sendAutoItemTdo")
    public ResponseInfo fixSendAutoItemTdo(@RequestBody List<String> taskUserIds) {
        for (String taskUserId : taskUserIds) {
            empEvalAppSvc.sendAutoItemInputFinishValueMsg(new TenantId(getCompanyId()), taskUserId);
        }
        return ResponseInfo.success(null);
    }

    /**
     * 订正处理阶段卡流程
     *
     * @param taskUserIds
     * @param stageStatus 当前阶段
     * @return
     */
    @RequestMapping("fix/task/anewStageEnded")
    @NoneLogin
    public ResponseInfo anewStageEnded(@RequestParam String taskUserIds, @RequestParam String stageStatus) {
        if (!"".equals(taskUserIds) && taskUserIds != null) {
            for (String taskUserId : Arrays.asList(taskUserIds.split(","))) {
                empEvalAppSvc.anewStageEnded(taskUserId, stageStatus);
            }
        }
        return ResponseInfo.success(null);
    }

    /**
     * 订正录入完成值待办取消
     *
     * @param taskUserIds
     * @return
     */
    @RequestMapping("fix/task/cancelSubmitFinishMsg")
    public ResponseInfo cancelSubmitFinishMsg(@RequestParam String taskUserIds) {
        if (!"".equals(taskUserIds) && taskUserIds != null) {
            for (String taskUserId : Arrays.asList(taskUserIds.split(","))) {
                EmpEvalMerge empEvalRule = empEvalRuleRepo.getEmpEvalMerge(new TenantId(getCompanyId()), taskUserId, EmpEvalMerge.all);
                //取消代办
                new CancelTodoEvent(new TenantId(getCompanyId()), empEvalRule.getResultInputEmpIds(getEmpId()), taskUserId, MsgSceneEnum.TASK_SUBMIT_PROGRESS.getType()).publish();
            }
        }
        return ResponseInfo.success(null);
    }

    //终止员工任务 替换perf/task/terminateTask
    @RequestMapping("admin/eval/terminateEmpEval")
    public ResponseInfo terminateTask(@RequestBody List<String> taskUserIds) {
        if (CollUtil.isEmpty(taskUserIds)) {
            return ResponseInfo.error("error.param.null", getLocale());
        }
        empEvalAppSvc.terminateEmpEval(getCompanyId(), getEmpId(), getAdminType(), taskUserIds);
        return ResponseInfo.success("");
    }

    //替代perf/task/queryResetNode 可重置的节点集合
    @RequestMapping({"admin/eval/listResetNodes", "perf/task/queryResetNode"})
    public ResponseInfo listResetNodes(String taskId, String taskUserId) {
        List<String> resetNodes = empEvalAppSvc.listResetNodes(getCompanyId(), taskId, taskUserId);
        return ResponseInfo.success(resetNodes);
    }

    //评分按人员重置， 可重置评分人列表
    @RequestMapping("admin/eval/listResetEmp")
    public ResponseInfo listResetEmp(@RequestParam String taskUserId) {
        Set<EmpResetPo> pos = empEvalAppSvc.listResetEmp(getCompanyId(), taskUserId);
        return ResponseInfo.success(pos);
    }

    /**
     * 手动发起评分时判断是否有自动评分的完成值未录入
     * 旧接口queryNoFinishValueTask2 迁移而来
     *
     * @param taskUserStr
     * @return
     */
    @RequestMapping("perf/task/listNoFinishValueEval")
    public ResponseInfo listNoFinishValueEval(String taskUserStr) {
        checkParamNull(taskUserStr);
        List<String> taskUserList = Arrays.asList(taskUserStr.split(","));
        return ResponseInfo.success(empEvalAppSvc.listNoFinishValueEval(getCompanyId(), taskUserList));
    }

    @RequestMapping("perf/task/startNewEmpEval")
    public ResponseInfo startNewEmpEval(@RequestBody StartNewEmpEvalCmd cmd) {
        log.info("startNewEmpEval 参数：{}", JSONUtil.toJsonPrettyStr(cmd));
        TenantId tenantId = new TenantId(getCompanyId());
        hrmSolutionAppSvc.checkHrmMaxCnt(tenantId, cmd.getNewEmpId());
        cmd.startorInfo(tenantId, new EmpId(getEmpId()));
        final String cycleTaskId = evalTaskAppSvc.directStartEval(cmd);
        return ResponseInfo.success(cycleTaskId);
    }


    //替代perf/task/queryFinishValueList 查询完成值录入列表
//    @RequestMapping({"emp/eval/listFinishValue", "perf/task/queryFinishValueList"})
    public ResponseInfo listFinishValue(FinishValueQuery query) {
        query.setCompanyId(getCompanyId());
        query.setOpEmpId(getEmpId());
        List<BatchFinishValuePo> pos = empEvalAppSvc.listFinishValue(query);
        return ResponseInfo.success(pos);
    }

    //替代perf/task/downBatchInputFinishValueExcel 下载批量录入完成值模板
//    @RequestMapping({"admin/eval/downLoadInputFinishTemp","perf/task/downBatchInputFinishValueExcel"})
    public void downBatchInputFinishValueExcel(HttpServletRequest request, HttpServletResponse response, FinishValueQuery query) {
        query.setCompanyId(getCompanyId());
        query.setOpEmpId(getEmpId());
        query.setOrderBy("emp");
        empEvalAppSvc.downLoadInputFinishTemp(request, response, query);
    }

    //替代perf/task/getTaskAppealItemList 获取任务申诉项目列表
    @RequestMapping({"emp/eval/getTaskAppealItemList", "perf/task/getTaskAppealItemList"})
    public ResponseInfo getTaskAppealItemList(String taskUserId) {
        checkParamNull(taskUserId);
        List<EvaluateTaskAppealItemPo> pos = empEvalAppSvc.getTaskAppealItemList(getCompanyId(), taskUserId);
        return ResponseInfo.success(pos);
    }

    //替代perf/task/queryNeedSetMutualItems 获取需要设置互评人的指标
    @RequestMapping({"emp/eval/listNeedSetMutualItem", "perf/task/queryNeedSetMutualItems"})
    public ResponseInfo listNeedSetMutualItem(@RequestBody TaskMutualItemQuery query) {
        query.setCompanyId(getCompanyId());
        query.setOpEmpId(getEmpId());
        return ResponseInfo.success(empEvalAppSvc.listNeedSetMutualItem(query));
    }

    @RequestMapping({"admin/eval/pageTaskItem", "perf/task/pageTaskItem"})
    public MultiResponse pageTaskItem(TaskKpiItemQuery query) {
        log.info("pageTaskItem==param:{}", JSONArray.toJSONString(query));
        query.setCompanyId(getCompanyId());
        PagedList<EvalKpi> kpis = empEvalAppSvc.pageTaskKpiItem(query);
        return MultiResponse.of(kpis.getData(), kpis.getPageNo(), kpis.getTotalRow(), kpis.getPageSize());
    }

    //替换queryNoSetMutualAuditEmp接口 查询未指定互评人的被考核人
    @RequestMapping({"admin/eval/listMutualAuditEmp", "perf/task/queryNoSetMutualAuditEmp"})
    public ResponseInfo listMutualAuditEmp(@RequestBody MutualEmpQuery query) {
        query.setCompanyId(getCompanyId());
        return ResponseInfo.success(empEvalAppSvc.listMutualAuditEmp(query));
    }

    //替换queryBatchFinalAuditList接口 工作台待校准列表 单个校准待办
    @RequestMapping({"emp/eval/pagedWaitResultAudit", "perf/task/queryBatchFinalAuditList"})
    public MultiResponse pagedWaitResultAudit(WorkWaitMsgQuery query) {
        query.setCompanyId(getCompanyId());
        query.setCreatedUser(getEmpId());
        PagedList<WorkWaitMsgPo> pos = empEvalAppSvc.pagedWaitResultAudit(query);
        return MultiResponse.of(pos.getData(), pos.getPageNo(), pos.getTotalRow(), pos.getPageSize());
    }


    //替换listWaitInputAutoItem接口 查询待录入完成值自动计算指标
    @RequestMapping({"emp/eval/listWaitInputAutoItem", "perf/task/listWaitInputAutoItem"})
    public ResponseInfo listWaitInputAutoItem(TaskKpiItemQuery query) {
        query.setCompanyId(getCompanyId());
        return ResponseInfo.success(empEvalAppSvc.listWaitInputAutoItem(query));
    }

    //替换queryItemDynamic接口 查询指标动态
    @RequestMapping({"emp/eval/listItemDynamic", "perf/task/queryItemDynamic"})
    public ResponseInfo listItemDynamic(String taskUserId, String kpiItemId) {
        checkParamNull(taskUserId, kpiItemId);
        return ResponseInfo.success(empEvalAppSvc.listItemDynamic(taskUserId, kpiItemId));
    }

    //替换queryItemFiles接口 查询指标附件
    @RequestMapping({"emp/eval/listItemFile", "perf/task/queryItemFiles"})
    public ResponseInfo listItemFile(String taskUserId, String kpiItemId) {
        checkParamNull(taskUserId, kpiItemId);
        return ResponseInfo.success(empEvalAppSvc.listItemFile(taskUserId, kpiItemId));
    }

    //替换saveTaskFile接口 指标上传附件
    @RequestMapping({"emp/eval/saveTaskFile", "perf/task/saveTaskFile"})
    public ResponseInfo saveTaskFile(String taskUserId, String kpiItemId, String fileList) {
        checkParamNull(taskUserId, fileList);
        empEvalAppSvc.saveTaskFile(getCompanyId(), getEmpId(), taskUserId, kpiItemId, fileList);
        return ResponseInfo.success("");
    }

    //替换deleteTaskFile接口 指标删除附件
    @RequestMapping({"emp/eval/deleteTaskFile", "perf/task/deleteTaskFile"})
    public ResponseInfo deleteTaskFile(String id) {
        checkParamNull(id);
        empEvalAppSvc.deleteTaskFile(getCompanyId(), getEmpId(), id);
        return ResponseInfo.success("");
    }

    //替换saveItemNotice接口 保存指标提醒记录
    @RequestMapping({"admin/eval/saveItemNotice", "perf/task/saveItemNotice"})
    public ResponseInfo saveItemNotice(PerfEvaluateItemNotice notice) {
        notice.setCompanyId(getCompanyId());
        notice.setEmpId(getEmpId());
        empEvalAppSvc.saveItemNotice(notice);
        return ResponseInfo.success("");
    }

    //替换queryNoticeByItem接口 查询指标提醒记录
    @RequestMapping({"admin/eval/getItemNotice", "perf/task/queryNoticeByItem"})
    public ResponseInfo getItemNotice(@RequestBody ItemNoticeQuery query) {
        query.setCompanyId(getCompanyId());
        return ResponseInfo.success(empEvalAppSvc.getItemNotice(query));
    }

    //替换queryItemSimpleInfo接口
    @RequestMapping({"admin/eval/pagedItemSimpleInfo", "perf/task/queryItemSimpleInfo"})
    public MultiResponse pagedItemSimpleInfo(@RequestBody ItemSimpleQuery query) {
        query.setCompanyId(getCompanyId());
        PagedList<ItemSimplePo> pos = empEvalAppSvc.pagedItemSimpleInfo(query);
        return MultiResponse.of(pos.getData(), pos.getPageNo(), pos.getTotalRow(), pos.getPageSize());
    }

    //迁移新人考核任务列表接口 pagedNewEmpTask
    @RequestMapping("admin/eval/pagedNewEmpTask")
    public MultiResponse pagedNewEmpTask(@RequestBody NewEmpTaskQuery query) {
        query.setTenantId(new TenantId(getCompanyId()));
        PagedList<NewEmpTaskPo> taskPos = evalTaskAppSvc.pagedNewEmpTaskPo(query);
        return MultiResponse.of(taskPos.getData(), taskPos.getPageNo(), taskPos.getTotalRow(), taskPos.getPageSize());
    }

    //迁移评分总结查询接口 listEvalUserSummary
    @RequestMapping("admin/eval/listEvalUserSummary")
    public ResponseInfo listEvalUserSummary(String taskUserId) {
        return ResponseInfo.success(evalTaskAppSvc.listEvalUserSummary(new TenantId(getCompanyId()), taskUserId));
    }

    //迁移没有录入工作事项的被考核人查询接口 queryNoWorkItemEmp
    @RequestMapping({"admin/eval/listNoWorkItemEmp", "perf/task/queryNoWorkItemEmp"})
    public ResponseInfo listNoWorkItemEmp(@RequestBody List<WorkItemQuery> query) {
        return ResponseInfo.success(empEvalAppSvc.noWorkItemEmp(new TenantId(getCompanyId()), query));
    }

    //迁移指标审核或变更记录查询接口 queryModificationRecords
    @RequestMapping({"admin/eval/listModificationRecord", "perf/task/queryModificationRecords"})
    public ResponseInfo listModificationRecord(String taskUserId, String scene) {
        checkParamNull(taskUserId, scene);
        return ResponseInfo.success(empEvalAppSvc.listModificationRecord(getCompanyId(), taskUserId, scene));
    }

    //跳过责任人
    @RequestMapping({"admin/eval/skipReviewer", "perf/task/skipReviewer"})
    public ResponseInfo skipRater(String taskUserId, String scorerId, String scorerName, String scorerType) {
        SkipRaterCmd cmd = new SkipRaterCmd(taskUserId, scorerId, scorerName, scorerType);
        cmd.setOperator(getCompanyId(), getEmpId(), StrUtil.EMPTY);
        if (cmd.isSelfScore()) {// 跳过自评：自评的指标都提交成0分
            EvalUser evalUser = this.userRepo.getBaseTaskUser(cmd.getTenantId(), cmd.getTaskUserId());
            TenantId companyId = new TenantId(getCompanyId());
            SubmitScoreCmd scoreCmd = this.submitScoreAppSvc.autoSubmitCmd(cmd.getTenantId(), evalUser);
            //scoreCmd.setTotalScore(BigDecimal.ZERO);
            scoreCmd.setScorerType(scorerType);
            scoreCmd.submitScoreLog(companyId, getEmpId());
            scoreCmd.checkParamNullAndBuild();
            if (evalUser.wasTempTask()) {
                scoreCmd.setOpEmpId(new EmpId(evalUser.getEmpId()));
                submitScoreAppSvc.submitScore(scoreCmd);
            } else {
                scoreStageAppSvc.submitAnyWrap(scoreCmd);
            }
        }
        if (cmd.isResultAffirm()) {// 跳过结果确认：考核人自己确认，跳过之后进入下一阶段（考核完成了）
            this.empEvalAppSvc.doAffirmResult(cmd.getTenantId(), cmd.getOpEmpId(), cmd.getTaskUserId(), true, "");
        }
        if (cmd.isKpiItemAudit()) {//指标审核 cmd.isKpiItemAudit()
            OnlyConfirmtemCmd onlyConfirmtemCmd = new OnlyConfirmtemCmd();
            onlyConfirmtemCmd.asSkipped(cmd.getTenantId(), new EmpId(cmd.getOpEmpId()),
                    cmd.getTaskUserId(), cmd.getScorerId(), scorerName);
            confirmStageAppSvc.skipConfirmItem(onlyConfirmtemCmd);
//            this.empEvalAppSvc.directConfirmItem(onlyConfirmtemCmd);
        }
        if (cmd.isFinalResultAudit()) {//跳过校准
            this.empEvalAppSvc.skipFinalResultAudit(cmd);
        }
        //插入操作日志
        adminTaskOperationRepo.saveSkipOperation(taskUserId, getCompanyId(), getEmpId(), getAdminType(), scorerId);
        return ResponseInfo.success("");
    }


    @RequestMapping({"admin/eval/skipSelfScoreReviewer", "perf/task/skipSelfScoreReviewer"})
    public ResponseInfo skipSelfScoreReviewer(String taskUserId, String scorerId, String scorerName, String scorerType) {
        SkipRaterCmd cmd = new SkipRaterCmd(taskUserId, scorerId, scorerName, scorerType);
        cmd.setOperator(getCompanyId(), getEmpId(), StrUtil.EMPTY);
        // 跳过自评：自评的指标都提交成0分
        EvalUser evalUser = this.userRepo.getBaseTaskUser(cmd.getTenantId(), cmd.getTaskUserId());
        TenantId companyId = new TenantId(getCompanyId());
        SubmitScoreV3Cmd scoreCmd = this.submitScoreAppSvc.autoSubmitCmd3(cmd.getTenantId(), evalUser);
        scoreCmd.checkParamNullAndBuild();
        scoreCmd.setSelfSkip( true);
        scoreCmd.submitScoreLog(companyId, getEmpId());
        String tid = MDC.get("tid");
        scoreStageAppSvc.batchSubmitScenesScoreV3(tid,companyId,new EmpId(evalUser.getEmpId()),Arrays.asList(scoreCmd));
        //插入操作日志
        adminTaskOperationRepo.saveSkipOperation(taskUserId,getCompanyId(),getEmpId(),getAdminType(),scorerId);
        return ResponseInfo.success("");
    }

    @RequestMapping("/perf/task/querySkipRaters")
    public SingleResponse querySkipRaters(@Param("taskUserId") String taskUserId) {
       return SingleResponse.of(empEvalAppSvc.querySkipRaters(taskUserId, getCompanyId()));
    }

    @RequestMapping("/perf/task/queryScoreStageSkipRaters")
    public SingleResponse queryScoreStageSkipRaters(@Param("taskUserId") String taskUserId){
        return SingleResponse.of(empEvalAppSvc.queryScoreSkipRatersV3(taskUserId, getCompanyId()));
    }

    @RequestMapping("/perf/task/skipRater")
    public SingleResponse skipRater(@RequestBody TaskSkipCmd cmd) {
        cmd.setOperator(getCompanyId(), getEmpId(), getAdminType());
       // empEvalAppSvc.skipRater(cmd);
        empEvalAppSvc.skipRaterV3(cmd);
        return SingleResponse.buildSuccess();
    }

    @RequestMapping("admin/eval/skipScore")
    public ResponseInfo skipScore(String taskUserId) throws InterruptedException {
        checkParamNull(taskUserId);
        Map<String, List<SubmitScoreCmd>> submitMap = submitScoreAppSvc.skipScoreSubmit(new TenantId(getCompanyId()), taskUserId, getEmpId());
        if (CollUtil.isEmpty(submitMap)) {
            throw new KpiI18NException("error.skip.null");
        }
        for (Map.Entry<String, List<SubmitScoreCmd>> stringListEntry : submitMap.entrySet()) {
            scoreStageAppSvc.submitAnyWrap(stringListEntry.getValue());//v2
            Thread.sleep(200);
        }
//        submitMap.forEach((k,v) ->{
//            scoreStageAppSvc.submitAnyWrap(v);//v2
//        });
        return ResponseInfo.success("");
    }

    @RequestMapping({"admin/eval/resetTask", "perf/task/resetTask"})
    public ResponseInfo resetTask(String resetNode, String taskUserIds, String resetReason) {
        checkParamNull(taskUserIds);
        List<String> taskUserList = StringUtils.isNotEmpty(taskUserIds) ? Arrays.asList(StringUtils.split(taskUserIds, ",")) : null;
        log.info("taskUserList==:{}", JSON.toJSONString(taskUserList));
        resultAuditFlowAppSvc.refreshFlow(getCompanyId(), taskUserList, getEmpId(), resetNode);
        for (String taskUserId : taskUserList) {
            evalStageAppSvc.handResetStage(new TenantId(getCompanyId()), taskUserId, getEmpId(), resetNode, OperationTypeEnum.RESET_NODE.getValue(), resetReason);
        }
        //插入操作日志
        adminTaskOperationRepo.saveResetOperation(taskUserList, getCompanyId(), getEmpId(), getAdminType(), resetNode);
        return ResponseInfo.success("");
    }

    @RequestMapping("admin/eval/resetScoreEmp")
    public ResponseInfo resetScoreEmp(@RequestBody ResetScoreEmpCmd cmd) {
        cmd.accOp(getCompanyId(), getEmpId());
        if (CollUtil.isEmpty(cmd.getScoreEmps())) {
            log.warn("考核任务不存在或没有重置的人！！！");
            return ResponseInfo.error("请选择需要重置的人！！！");
        }
        evalStageAppSvc.resetScoreEmpV3(cmd);
        return ResponseInfo.success("");
    }

    @RequestMapping("admin/eval/rejectScorerNode")
    public ResponseInfo rejectScorerNode(@RequestBody RejectScoreCmd cmd) {
        cmd.accOp(getCompanyId(), getEmpId());
        evalStageAppSvc.rejectScoreV3(cmd);
        return ResponseInfo.success("");
    }

    //数据订正,生成评分后续评分环节
    @RequestMapping("fix/eval/fixDispatchChainNode")
    @NoneLogin
    public ResponseInfo fixDispatchChainNode(String companyId, String taskUserId, String node, Integer approvalOrder) {

        SubScoreNodeEnum subNode = SubScoreNodeEnum.fromStr(node);
        scoreStageAppSvc.fixDispatchSubOfChainNode(new TenantId(companyId), taskUserId, subNode, approvalOrder);
        return ResponseInfo.success("");
    }

    //数据订正,生成评分后续评分环节
    @RequestMapping("fix/eval/fixChainNodeEnd")
    @NoneLogin
    public ResponseInfo fixChainNodeEnd(TenantId companyId, String taskUserId, String endNode, Integer approvalOrder) {
        SubScoreNodeEnum scoreNode = SubScoreNodeEnum.fromStr(endNode);
        for (String userId : StrUtil.splitTrim(taskUserId, ",")) {
            scoreStageAppSvc.fixChainNodeEnd(companyId, scoreNode, userId, approvalOrder);
        }
        return ResponseInfo.success("");
    }

    @RequestMapping("fix/eval/fixMsgTodoConfirmItem")
    @NoneLogin
    public ResponseInfo fixMsgTodoConfirmItem(String companyId, String taskUserId, String confirmEmpId) {
        empEvalAppSvc.fixMsgTodoConfirmItem(new TenantId(companyId), taskUserId, confirmEmpId);
        return ResponseInfo.success("");
    }

    //转交责任人信息  单个使用
    @RequestMapping("admin/eval/getTransferReviewers")
    public ResponseInfo getTransferReviewers(@RequestParam String userId) {
        return ResponseInfo.success(empEvalAppSvc.getTransferReviewers(getCompanyId(), userId));
    }

    //添加组织考核到任务中
    @RequestMapping("admin/eval/addEvalOrg")
    public SingleResponse addEvalOrg(@RequestBody AddEvalEmpCmd cmd) {
        cmd.accOp(new TenantId(getCompanyId()), getEmpId(), getAdminType());
        List<String> existEvalOrgIds = empEvalAppSvc.listExistEvalOrg(cmd.getTenantId(), new TaskId(cmd.getTaskId()), cmd.evalOrgIds());
        int addCnt = cmd.rmDuplicateEvalOrgId(existEvalOrgIds);
        if (addCnt == 0) {
            return SingleResponse.of(cmd.getResMsg());
        }
        taskAppSvc.addEvalOrg(cmd);
        return SingleResponse.of(cmd.getResMsg());
    }

    //测试匹配能否关联,交互第一步
    @RequestMapping("admin/eval/testBindMatch")
    public SingleResponse testBindMatch(@RequestBody TestBindEvalQuery cmd) {
        cmd.setCompanyId(getCompanyId());
        List<BindEvalResult> batchRs = empEvalAppSvc.testBindMatch(cmd);
        return SingleResponse.of(batchRs);
    }


    //查询单个任务的关联任务信息
    @RequestMapping("admin/eval/getBindEvalResult")
    public SingleResponse listBindByMainUserId(@RequestParam String mainUserId) {
        BindEvalResult rs = empEvalAppSvc.getBindEvalResult(getCompanyId(), mainUserId);
        return SingleResponse.of(rs);
    }


    //保存存关联结果
    @RequestMapping("admin/eval/saveRefEval")
    public SingleResponse saveRefEval(@RequestBody List<BindEvalResult> batchRs) {
        empEvalAppSvc.saveRefEval(getCompanyId(), batchRs);
        return SingleResponse.buildSuccess();
    }

    //公布关联结果
    @RequestMapping("admin/eval/publicRefEval")
    public SingleResponse publicRefEval(@RequestBody List<String> taskUserIds) {
        if (CollUtil.isEmpty(taskUserIds)) {
            return SingleResponse.buildFailure("009", "参数缺失");
        }
        empEvalAppSvc.publicRefEval(getCompanyId(), taskUserIds);
        return SingleResponse.buildSuccess();
    }

    //保存存关联结果 测试接口
    @RequestMapping("admin/eval/computeScoreAfterChange")
    public SingleResponse saveRefEval(@RequestParam String changedUserId) {
        empEvalAppSvc.computeScoreAfterChange(getCompanyId(), changedUserId);
        return SingleResponse.buildSuccess();
    }

    //保存存关联结果 测试接口
    @RequestMapping("admin/eval/removeRefEval")
    public SingleResponse removeRefEval(@RequestBody List<String> taskUserIds) {
        empEvalAppSvc.removeRefEval(getCompanyId(), taskUserIds);
        return SingleResponse.buildSuccess();
    }


    @RequestMapping("admin/eval/pagedImportTarget")
    public MultiResponse pagedImportTarget(@RequestBody TaskTargetQuery query) {
        query.accOp(getCompanyId(), getEmpId());
        PagedList<ExportTargetValue> pos = empEvalAppSvc.pagedImportTarget(query);
        return MultiResponse.of(pos.getData(), pos.getPageNo(), pos.getTotalRow(), pos.getPageSize());
    }

    @RequestMapping("admin/eval/pagedEvalItem")
    public MultiResponse listItems(@RequestBody EvalItemSelectQuery query) {
        query.setCompanyId(getCompanyId());
        PagedList<EvalKpiItemPo> pos = empEvalAppSvc.pagedEvalItem(query);
        return MultiResponse.of(pos.getData(), pos.getPageNo(), pos.getTotalRow(), pos.getPageSize());
    }

    @RequestMapping("admin/eval/pagedEvalEmpOrg")
    public MultiResponse pagedEvalEmpOrg(@RequestBody EvalEmpSelectQuery query) {
        query.setCompanyId(getCompanyId());
        PagedList<EmpStaff> pos = empEvalAppSvc.pagedEvalEmpOrg(query);
        return MultiResponse.of(pos.getData(), pos.getPageNo(), pos.getTotalRow(), pos.getPageSize());
    }

    @RequestMapping("admin/eval/batchUpdateTarget")
    public SingleResponse batchUpdateTarget(@RequestBody BatchUpdateTargetCmd cmd) {
        cmd.setCompanyId(getCompanyId());
        cmd.setOpEmpId(getEmpId());
        empEvalAppSvc.batchUpdateTarget(cmd);
        return SingleResponse.buildSuccess();
    }

    @RequestMapping("admin/eval/downLoadImportEvalEmpTemp")
    public void downLoadImportEvalEmpTemp(HttpServletRequest request, HttpServletResponse response) {
        try {
            ClassPathResource resource = new ClassPathResource(FileUtil.getTemplatePath(ExcelTemplateEnum.IMPORT_EVAL_EMP.getTemplateName()));
            log.info("下载导入考核成员模板templatePath={}", resource.getPath());
            String fileName = ExcelTemplateEnum.IMPORT_EVAL_EMP.getFileName() + DateTimeUtils.date2StrDate(new Date(), DateTimeUtils.FORMAT_yyyyMMddHHmmss) + BusinessConstant.XSSF_PREFIX;

            ExcelUtil.formatResponse(request, response, fileName);
            EasyExcel.write(response.getOutputStream()).withTemplate(resource.getInputStream()).sheet().doFill(new ArrayList<>());
            log.info("下载导入考核成员模板完成");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @RequestMapping("admin/eval/downLoadImportEvalOrgTemp")
    public void downLoadImportEvalOrgTemp(HttpServletRequest request, HttpServletResponse response) {
        try {
            ClassPathResource resource = new ClassPathResource(FileUtil.getTemplatePath(ExcelTemplateEnum.IMPORT_EVAL_ORG.getTemplateName()));
            log.info("下载导入考核部门模板templatePath={}", resource.getPath());
            String fileName = ExcelTemplateEnum.IMPORT_EVAL_ORG.getFileName() + DateTimeUtils.date2StrDate(new Date(), DateTimeUtils.FORMAT_yyyyMMddHHmmss) + BusinessConstant.XSSF_PREFIX;

            ExcelUtil.formatResponse(request, response, fileName);
            EasyExcel.write(response.getOutputStream()).withTemplate(resource.getInputStream()).sheet().doFill(new ArrayList<>());
            log.info("下载导入考核部门模板完成");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @RequestMapping("admin/eval/importEvalEmp")
    public SingleResponse importEvalEmp(@RequestParam MultipartFile file, @RequestParam String cycleId, @RequestParam String taskId) {
        AddEvalEmpCmd cmd = new AddEvalEmpCmd();
        cmd.accOp(new TenantId(getCompanyId()), getEmpId(), getAdminType());
        cmd.setCycleId(cycleId);
        cmd.setTaskId(taskId);
        ImportEvalEmpPo po = empEvalAppSvc.parseEvalEmp(file, cmd);
        if (cmd.getEmps().isEmpty()) {
            return SingleResponse.of(po);
        }
        EmpEvalAtCycleQuery query = new EmpEvalAtCycleQuery(cmd.getTenantId(), cmd.getTaskId(), cmd.getEmps());
        List<EvalEmp> existed = empEvalAppSvc.listExistAtCycle(query);
        cmd.filterExistsEmps(existed);
        taskAppSvc.addEvalEmp(cmd);
        return SingleResponse.of(po);
    }

    @RequestMapping("admin/eval/importEvalOrg")
    public SingleResponse importEvalOrg(@RequestParam MultipartFile file, @RequestParam String cycleId, @RequestParam String taskId) {
        AddEvalEmpCmd cmd = new AddEvalEmpCmd();
        cmd.accOp(new TenantId(getCompanyId()), getEmpId(), getAdminType());
        cmd.setCycleId(cycleId);
        cmd.setTaskId(taskId);
        ImportEvalEmpPo po = empEvalAppSvc.parseEvalOrg(file, cmd);
        if (cmd.evalOrgIds().isEmpty()) {
            return SingleResponse.of(po);
        }
        List<String> existEvalOrgIds = empEvalAppSvc.listExistEvalOrg(cmd.getTenantId(), new TaskId(cmd.getTaskId()), cmd.evalOrgIds());
        cmd.rmDuplicateEvalOrgId(existEvalOrgIds);
        taskAppSvc.addEvalOrg(cmd);
        return SingleResponse.of(po);
    }

    @RequestMapping("admin/eval/checkFormula")
    public ResponseInfo checkFormula(@RequestBody FormulaVerifyCmd formula) {
        String result = empEvalAppSvc.checkFormula(formula);
        return ResponseInfo.success(result);
    }


    //考核关系预览列表
    @RequestMapping("admin/eval/pagedEvalPreview")
    public MultiResponse pagedEvalPreview(@RequestBody EmpEvalAtTaskQuery2 query) {
        query.accOp(getCompanyId(), getEmpId(), getAdminType());
        PagedList<EmpEvalByStatusPo> empEvals = empEvalAppSvc.pagedEvalPreview(query);
        if (CollUtil.isNotEmpty(empEvals)) {
            log.info("33.getEditExeIndi = {}", JSONUtil.toJsonStr(empEvals.get(0).getEditExeIndi()));
        }
        return MultiResponse.of(empEvals.getData(), empEvals.getPageNo(), empEvals.getTotalRow(), empEvals.getPageSize());
    }

    //考核关系异动列表
    @RequestMapping("admin/eval/pagedEvalChange")
    public MultiResponse pagedEvalChange(@RequestBody EmpEvalAtTaskQuery2 query) {
        query.accOp(getCompanyId(), getEmpId(), getAdminType());
        PagedList<EmpEvalChangeResult> empEvals = empEvalAppSvc.pagedEvalChange(query);
        return MultiResponse.of(empEvals.getData(), empEvals.getPageNo(), empEvals.getTotalRow(), empEvals.getPageSize());
    }

    //创建考核异动
    @RequestMapping("admin/eval/createEvalChange")
    public MultiResponse createEvalChange(@RequestBody EmpEvalAtTaskQuery2 query) {
        query.accOp(getCompanyId(), getEmpId(), getAdminType());
        String batchId = empEvalAppSvc.createEvalChange(getCompanyId(), getEmpId(), query.getTaskIds().get(0));
        empEvalAppSvc.createEvalChangeResultV2(MDC.get("tid"), query, batchId);
        return MultiResponse.buildSuccess();
    }

    //选择单个异动刷新
    @RequestMapping("admin/eval/orgChangeRefreshResult")
    @NoneLogin
    public MultiResponse orgChangeRefreshResult(@RequestBody EmpEvalChangeResultCmd cmd) {
        cmd.accOp(getCompanyId(), getEmpId(), getAdminType());
        empEvalAppSvc.orgChangeRefreshResultV2(cmd);
        return MultiResponse.buildSuccess();
    }

    //异动刷新
    @RequestMapping("admin/eval/changeEmpEval")
    public MultiResponse changeEmpEval(@RequestBody ChangeEvalCmd cmd) {
        log.debug("参数:{}", JSONUtil.toJsonStr(cmd));
        cmd.accOp(getCompanyId(), getEmpId(), getAdminType());
        empEvalAppSvc.changeEmpEval(cmd);
        return MultiResponse.buildSuccess();
    }

    //查询异动批次信息
    @RequestMapping("admin/eval/getChangeStatus")
    public SingleResponse getChangeStatus(String taskId) {
        return SingleResponse.of(empEvalAppSvc.findEvalTaskChangeBatchInfo(getCompanyId(), taskId));
    }

    //修改状态
    @RequestMapping("admin/eval/upChangeBatchStatus")
    public SingleResponse upChangeBatchStatus(String batchId, String status) {
        if (StrUtil.isBlank(batchId) && StrUtil.isBlank(status)) {
            return SingleResponse.buildFailure("009", "参数缺失");
        }
        empEvalAppSvc.upChangeBatchStatus(getCompanyId(), batchId, status);
        return SingleResponse.buildSuccess();
    }

    @RequestMapping("emp/eval/getAppealReqFieldConf")
    public SingleResponse getAppealReqFieldConf(String taskUserId) {
        return SingleResponse.of(empEvalAppSvc.getAppealReqFieldConf(getCompanyId(),taskUserId));
    }

    @RequestMapping("admin/eval/editBatchEmpEvalOrgInfo")
    public SingleResponse editBatchEmpEvalOrgInfo(@RequestBody EditEmpEvalOrgInfoCmd cmd) {
        log.info("参数：{}", JSONUtil.toJsonStr(cmd));
        cmd.accOp(getCompanyId(),getEmpId(),getAdminType());
        cmd.checkParam();
        EditEmpEvalOrgInfoRs rs = empEvalAppSvc.editBatchEmpEvalOrgInfo(cmd);
        return SingleResponse.of(rs);
    }

    /**
     * 下载导入历史结果的模板
     */
    @RequestMapping("admin/eval/downloadHistoryEvalImportTemp")
    public void downloadHistoryEvalImportTemp(@RequestBody DownloadHistoryEvalQuery query) {
        query.accOp(getCompanyId(), getEmpId());
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            historyEvalImportAppSvc.downloadImportTemp(query, outputStream);
            this.appAcl.sendFile(new TenantId(query.getCompanyId()), getEmpId(), query.exportFileName(), outputStream.toByteArray());
            log.info("下载导入历史结果模板完成");
        } catch (Exception e) {
            log.error("下载导入历史结果模板出错" + e.getMessage(), e);
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    /**
     * 上传历史结果导入模板
     */
    @RequestMapping("admin/eval/uploadHistoryEvalImportTemp")
    public SingleResponse uploadHistoryEvalImportTemp(@RequestParam MultipartFile file, @RequestParam String taskId, @RequestParam String cycleId) throws IOException {
        if (taskId == null) {
            throw new KpiI18NException("taskId can not be null ", "任务ID不能为null");
        }
        if (cycleId == null) {
            throw new KpiI18NException("cycleId can not be null ", "周期ID不能为null");
        }
        String batchId = historyEvalImportAppSvc.parseAndSave(getCompanyId(), getEmpId(), taskId, cycleId, file);
        return SingleResponse.of(batchId);
    }

    /**
     * 轮询历史任务导入批次状态
     * @param batchId
     * @return
     */
    @RequestMapping("admin/eval/getHistoryEvalImportBatchStatus")
    public SingleResponse getHistoryEvalImportBatchStatus(String batchId) {
        return SingleResponse.of(historyEvalImportAppSvc.getImportBatchStatus(getCompanyId(), batchId));
    }

    /**
     * 分页查询历史任务导入记录明细
     * @param query
     * @return
     */
    @RequestMapping("admin/eval/pagedHistoryEvalImport")
    public MultiResponse pagedHistoryEvalImport(@RequestBody HistoryEvalImportQuery query) {
        query.accOp(getCompanyId(), getEmpId());
        PagedList<HistoryEvalImport> batches = historyEvalImportAppSvc.pagedHistoryEvalImport(query);
        return MultiResponse.of(batches.getData(), batches.getPageNo(), batches.getTotalRow(), batches.getPageSize());
    }

    /**
     * 查询任务下等级组
     * @param taskId
     * @return
     */
    @RequestMapping("admin/eval/listSnapScoreRange")
    public ResponseInfo listSnapScoreRange(String taskId) {
        return ResponseInfo.success(historyEvalImportAppSvc.listSnapScoreRange(new TenantId(getCompanyId()), taskId));
    }


    /**
     * 编辑历史任务导入记录明细
     * @param cmd
     * @return
     */
    @RequestMapping("admin/eval/editHistoryEvalImport")
    public SingleResponse editHistoryEvalImport(@RequestBody EditHistoryEvalImportCmd cmd) {
        cmd.accOp(getCompanyId(), getEmpId());
        historyEvalImportAppSvc.editHistoryEvalImport(cmd);
        return SingleResponse.buildSuccess();
    }

    /**
     * 批量删除历史任务导入记录明细
     * @param cmd
     * @return
     */
    @RequestMapping("admin/eval/batchRemoveHistoryEvalImport")
    public SingleResponse batchRemoveHistoryEvalImport(@RequestBody BatchRemoveHistoryEvalImportCmd cmd) {
        cmd.accOp(getCompanyId(), getEmpId());
        cmd.checkParam();
        historyEvalImportAppSvc.batchRemoveHistoryEvalImport(cmd);
        return SingleResponse.buildSuccess();
    }

    /**
     * 批量删除历史任务中taskUser
     * @param cmd
     * @return
     */
    @RequestMapping("admin/eval/batchRemoveHistoryTaskUserImport")
    public SingleResponse batchRemoveHistoryTaskUserImport(@RequestBody BatchRemoveHistoryTaskUserCmd cmd) {
        cmd.accOp(getCompanyId(), getEmpId());
        cmd.checkParam();
        historyEvalImportAppSvc.batchRemoveHistoryTaskUserImport(cmd);
        return SingleResponse.buildSuccess();
    }


    @RequestMapping("admin/eval/convertImportedAndSave2DB")
    public SingleResponse convertImportedAndSave2DB(String batchId) {
        HistoryImportedSaveRs rs = historyEvalImportAppSvc.convertImportedAndSave2DB(getCompanyId(), getEmpId(), getAdminType(), batchId);
        return SingleResponse.of(rs);
    }



    /**
     * 订正恢复终止任务
     *
     * @param taskUserIds
     * @return
     */
    @RequestMapping("fix/task/recoverTerminated")
    @NoneLogin
    public SingleResponse recoverTerminated(String taskUserIds, String taskId, String companyId) {
        if (StrUtil.isBlank(taskUserIds) && StrUtil.isBlank(taskId) && StrUtil.isBlank(companyId)) {
            return SingleResponse.buildFailure("009", "参数缺失");
        }
        empEvalAppSvc.recoverTerminated(StrUtil.splitTrim(taskUserIds, ","), companyId, taskId);
        return SingleResponse.buildSuccess();
    }

    /**
     * 进入完成值审核阶段
     *
     * @param taskUserIds
     * @param taskId
     * @param companyId
     * @return
     */
    @RequestMapping("fix/task/enterFinishedValueAudit")
    @NoneLogin
    public SingleResponse enterFinishedValueAudit(String taskUserIds, String taskId, String companyId) {
        if (StrUtil.isBlank(taskUserIds) && StrUtil.isBlank(taskId) && StrUtil.isBlank(companyId)) {
            return SingleResponse.buildFailure("009", "参数缺失");
        }
        empEvalAppSvc.enterFinishedValueAudit(StrUtil.splitTrim(taskUserIds, ","), companyId, taskId);
        return SingleResponse.buildSuccess();
    }


    //数据订正,重新刷新指标计算的字段及值
    @RequestMapping("fix/eval/fefreshFormulaField")
    @NoneLogin
    public ResponseInfo fixRefFormulaField(String companyId, String taskUserId, String tempId) {
        StdTemp stdTemp = stdAppSvc.getStdTempForApply(new TenantId(getCompanyId()), new TempId(tempId));
        empEvalAppSvc.fefreshFormulaField(companyId, taskUserId, stdTemp);
        return ResponseInfo.success("");
    }


    //数据订正,重新刷新指标计算的字段及值
    @RequestMapping("fix/eval/fixFinalResultAuditOrder")
    @NoneLogin
    public ResponseInfo fixFinalResultAuditOrder(String companyId, String taskId, String taskUserIds, Integer moveUpNum) {
        if (StrUtil.isBlank(taskId) && StrUtil.isBlank(taskUserIds)) {
            return ResponseInfo.success("");
        }
        if (StrUtil.isNotBlank(taskUserIds)) {
            for (String s : StrUtil.splitTrim(taskUserIds, ",")) {
                fixTaskAppSvc.fixFinalResultAuditOrder(companyId, taskId, s, moveUpNum);
            }
        }
        return ResponseInfo.success("");
    }



    //数据订正,重新刷新指标计算的字段及值
    @RequestMapping("fix/eval/fixExplainEvalScorer")
    public ResponseInfo fixExplainEvalScorer(String taskUserIds) {
        if (StrUtil.isBlank(taskUserIds)) {
            return ResponseInfo.success("");
        }

        TenantId tenantId = new TenantId(getCompanyId());
        if (StrUtil.isNotBlank(taskUserIds)) {
            for (String s : StrUtil.splitTrim(taskUserIds, ",")) {
                empEvalAppSvc.fixExplainEvalScorer(tenantId,s);
            }
        }
        return ResponseInfo.success("");
    }
}
