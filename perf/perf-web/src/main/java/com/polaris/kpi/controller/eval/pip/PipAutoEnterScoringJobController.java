package com.polaris.kpi.controller.eval.pip;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.polaris.kpi.eval.app.pip.dto.EnterPipScoringCmd;
import com.polaris.kpi.eval.app.pip.plan.PipEvalAppSvc;
import com.polaris.kpi.eval.app.task.appsvc.EmpEvalJobAppSvc;
import com.polaris.kpi.org.domain.dept.entity.Company;
import com.polaris.kpi.org.infr.company.dao.CompanyDaoImpl;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
public class PipAutoEnterScoringJobController {

    @Autowired
    private CompanyDaoImpl companyDao;
    @Autowired
    private EmpEvalJobAppSvc jobAppSvc;
    @Autowired
    private PipEvalAppSvc pipEvalAppSvc;

    //pip自动定时进入评分阶段,全量一天一次8:00执行
    @RequestMapping("pip/eval/autoEnterSorcing")
    @Scheduled(cron = "0 0 8 * * ?")
    public SingleResponse autoEnterSorcing() {
        MDC.put("tid", IdUtil.fastSimpleUUID());
        log.info("定时pip发起评分任务开始");
        //查询所有有效公司列表
        List<Company> companies = companyDao.allRunningCompany(2000000);
        if (CollectionUtils.isEmpty(companies)) {
            log.info("没有使用中的公司");
            return SingleResponse.of(companies.size());
        }
        for (Company company : companies) {
            try {
                String uniKey = jobAppSvc.lockCompanyForDayJob(company.getId(), "pipAutoEnterScoring");
                MDC.put("tid", uniKey);
                List<String> pipEvalIds = pipEvalAppSvc.listAutoEnterScorePipEval(company.getId());
                if (CollUtil.isEmpty(pipEvalIds)) {
                    continue;
                }
                log.info("autoEnterSorcing={}", uniKey);
                EnterPipScoringCmd cmd = new EnterPipScoringCmd(pipEvalIds, true);
                cmd.setOpEmpId(new TenantId(company.getId()), null);
                cmd.setAuto(true);
                pipEvalAppSvc.batchEnterPipScoring(cmd);
            } catch (DataIntegrityViolationException e) {
                log.error("定时进入评分冲突,公司:{}", company.getId());
            } catch (Exception e) {
                log.error("定时进入评分出错,公司:{}, error:{}", company.getId(), e.getMessage(), e);
            }
        }
        log.info("评分定时任务结束");
        MDC.clear();
        return SingleResponse.of(companies.size());
    }
}
