package com.polaris.kpi.event.eval;

import cn.com.polaris.kpi.ObjItem;
import cn.com.polaris.kpi.eval.StaffConfItem;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.event.EventHandler;
import com.perf.www.controller.BaseEventHander;
import com.polaris.kpi.ExecutorEnum;
import com.polaris.kpi.eval.app.cycle.appsvc.ConsecutiveTopResultEmpSvc;
import com.polaris.kpi.eval.app.cycle.appsvc.CycleEvalAppSvc;
import com.polaris.kpi.eval.app.cycle.appsvc.PerfStatisticRuleSvc;
import com.polaris.kpi.eval.app.cycle.appsvc.TopResultOfCycleSvc;
import com.polaris.kpi.eval.domain.cycle.entity.*;
import com.polaris.kpi.eval.domain.cycle.event.CycleTopResultStatisticEvent;
import com.polaris.kpi.eval.domain.task.entity.Cycle;
import com.polaris.kpi.eval.domain.task.entity.CycleTaskFinishResultByType;
import com.polaris.kpi.eval.infr.cycle.dao.PerfStatisticRuleDao;
import com.polaris.kpi.eval.infr.cycle.dao.TopResultOfCycleDao;
import com.polaris.kpi.eval.infr.cycle.query.TopResultOfCycleQuery;
import com.polaris.kpi.eval.infr.task.dao.AdminTaskDao;
import com.polaris.kpi.eval.infr.task.dao.CycleDao;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskBaseDo;
import com.polaris.kpi.org.infr.dept.dao.KpiOrgDao;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/9/29 15:46
 */
@Slf4j
public class WrapperCycleTopResultEventHandler {

    @Component
    @EventHandler
    public static class CycleTopResultStatisticWrapper extends BaseEventHander<CycleTopResultStatisticEvent> {

        @Autowired
        private CycleEvalAppSvc cycleEvalAppSvc;
        @Autowired
        private CycleDao cycleDao;
        @Autowired
        private KpiOrgDao kpiOrgDao;
        @Autowired
        private PerfStatisticRuleDao perfStatisticRuleDao;
        @Autowired
        private TopResultOfCycleDao topResultOfCycleDao;
        @Autowired
        private PerfStatisticRuleSvc perfStatisticRuleSvc;
        @Autowired
        private TopResultOfCycleSvc topResultOfCycleSvc;
        @Autowired
        private ConsecutiveTopResultEmpSvc consecutiveTopResultSvc;
        @Autowired
        private AdminTaskDao adminTaskDao;

        @Override
        public ExecutorService getExecutor() {
            return hightExecutor;
        }


        @Override
        public Response execute(CycleTopResultStatisticEvent event) {
            return super.execute(event);
        }

        @EventListener
        @Async(ExecutorEnum.high)
        @Override
//        @Transactional(rollbackFor = Exception.class)
        public void handerEvent(CycleTopResultStatisticEvent event) {
            event.handleStart();
            log.info("进入连续绩优/绩差统计事件");

            // 根据周期id，查周期
            // 查询周期
            TenantId tenantId = new TenantId(event.getTenantId());
            Cycle cycle = cycleEvalAppSvc.findCycle(event.getTenantId(), event.getCycleId());
            PerfEvaluateTaskBaseDo byTaskId = adminTaskDao.getByTaskId(event.getTenantId(), event.getTaskId());
            if (byTaskId == null) {
                log.error("处理CycleTopResultStatisticEvent事件，任务id为"+ event.getTaskId() + "的任务不存在！");
            }
            // 绩效类型 1=个人绩效，2=组织绩效
            Integer performanceType = byTaskId.getPerformanceType();

            // 先去周期内绩优/绩差结果表查有没有生成过记录，有记录，根据历史规则进行当前绩优/绩差结果统计
            List<PerfStatisticRule> historyRuleDataList = topResultOfCycleDao.listRuleByCycleIdAndPerfType(cycle.getId(), byTaskId.getPerformanceType());
            // 1.根据周期类型和任务类型，查当前公司配置的绩优/绩差规则
            List<PerfStatisticRule> statisticRuleList = perfStatisticRuleSvc.queryByCycleTypeAndRuleType(event.getTenantId(), cycle.getType(), performanceType);
            // 没有匹配到规则，直接退出统计
            if (CollUtil.isEmpty(statisticRuleList) && CollUtil.isEmpty(historyRuleDataList)) {
                return;
            }
            // 历史规则不为空，则根据历史规则进行统计
            if(CollUtil.isNotEmpty(historyRuleDataList)) {
                statisticRuleList = reSetRuleList(statisticRuleList, historyRuleDataList);
            }

            // 2.匹配到规则，根据规则适用类型（个人类型/组织类型），查周期下对应类型的考核任务是否已完成
            CycleTaskFinishResultByType taskFinishResult = cycleEvalAppSvc.getTaskFinishStatusByCycleType(cycle.getId(), performanceType);
            // 没有查到已发起的任务/里面存在未完成的任务，直接退出
            if (taskFinishResult == null || !taskFinishResult.isFinished()) {
                return;
            }
            List<String> finishedTaskIds = taskFinishResult.getTaskIds();

            // 3. 根据规则，查规则下面适用的员工列表
            List<ConsecutiveTopResultEmpBatch> addList = new ArrayList<>();
            for (PerfStatisticRule perfStatisticRule : statisticRuleList) {

                Set<String> deptIdList = new HashSet<>();
                Set<String> roleIdList = new HashSet<>();
                Set<String> fixEmpIdList = new HashSet<>();
                List<TopResultOfCycle> historyTopResults = new ArrayList<>();

                List<StaffConfItem> ruleApplyRange = perfStatisticRule.getRuleApplyRange();
                for (StaffConfItem staffConfItem : ruleApplyRange) {
                    if (staffConfItem.isDeptType()) {
                        List<String> deptIds = staffConfItem.getObjItems().stream().map(ObjItem::getObjId).collect(Collectors.toList());
                        deptIdList.addAll(deptIds);
                    }
                    if (staffConfItem.isRoleType()) {
                        List<String> roleIds = staffConfItem.getObjItems().stream().map(ObjItem::getObjId).collect(Collectors.toList());
                        roleIdList.addAll(roleIds);
                    }
                    if (staffConfItem.isFixEmpType()) {
                        List<String> fixEmpIds = staffConfItem.getObjItems().stream().map(ObjItem::getObjId).collect(Collectors.toList());
                        fixEmpIdList.addAll(fixEmpIds);
                    }
                }

                TopResultOfCycleQuery query = new TopResultOfCycleQuery();
                boolean isOraEval = perfStatisticRule.getRuleApplyType() == 1 ? false : true;
                query.setOrgEval(isOraEval);
                query.setRuleType(perfStatisticRule.getRuleType());

                //个人绩效,查部门下/角色下的人
                if (!isOraEval) {
                    // 员工id集合
                    Set<String> empIdSet = new HashSet<>();
                    if (CollUtil.isNotEmpty(deptIdList)) {
                        List<String> allChildDeptIdList = kpiOrgDao.listAllChildOrgIds(tenantId, new ArrayList<>(deptIdList));
                        deptIdList.addAll(allChildDeptIdList);
                        List<PerfStatisticRelatedUser> userByOrgIds = perfStatisticRuleDao.queryRelatedUserByOrgIds(tenantId, new ArrayList<>(deptIdList));
                        if (CollUtil.isNotEmpty(userByOrgIds)) {
                            empIdSet.addAll(userByOrgIds.stream().map(PerfStatisticRelatedUser::getEmployeeId).collect(Collectors.toSet()));
                        }
                    }
                    if (CollUtil.isNotEmpty(roleIdList)) {
                        List<PerfStatisticRelatedUser> userByRoleIds = perfStatisticRuleDao.queryRelatedUserByRoleIds(tenantId, new ArrayList<>(roleIdList));
                        if (CollUtil.isNotEmpty(userByRoleIds)) {
                            empIdSet.addAll(userByRoleIds.stream().map(PerfStatisticRelatedUser::getEmployeeId).collect(Collectors.toSet()));
                        }
                    }

                    // 4. 根据规则应用范围，统计任务下的绩优/绩差员工名单
                    List<TopResultOfCycle> currentTopResultList = topResultOfCycleDao.countTaskTopResultByRules(empIdSet, finishedTaskIds, perfStatisticRule.getStatisticRule(),isOraEval);
                    // 当前周期的统计结果为空，直接退出
                    if (CollUtil.isEmpty(currentTopResultList)) {
                        continue;
                    }
                    // 生成过周期内绩效差结果，直接删除
                    if (CollUtil.isNotEmpty(historyRuleDataList)) {
                        // 查询管理员直接删除的员工，不计入统计
                        List<String> removeList = consecutiveTopResultSvc.listAdminDelEmp(tenantId.getId(), cycle.getId(),perfStatisticRule.getRuleConfigId());
                        if (CollUtil.isNotEmpty(removeList)) {
                            currentTopResultList = currentTopResultList.stream().filter(item -> !removeList.contains(item.getEmpId() + "-" + item.getOrgId())).collect(Collectors.toList());
                        }
                    }

                    // 保存当前周期绩优/绩差结果至数据库
                    SaveTopResultOfCycle saveData = new SaveTopResultOfCycle();
                    saveData.setResultList(currentTopResultList)
                            .setCycleId(cycle.getId())
                            .setCycleStart(cycle.getCycleStart())
                            .setCycleEnd(cycle.getCycleEnd())
                            .setRuleConfigId(perfStatisticRule.getRuleConfigId())
                            .setRuleType(perfStatisticRule.getRuleType())
                            .setStatisticRuleDetail(perfStatisticRule)
                            .setTenantId(tenantId)
                            .setOpEmpId(null);
                    topResultOfCycleSvc.addTopResultOfCycleBatch(saveData);

                    // 5.根据周期规则类型，查匹配的周期，并统计当前周期下统计出来的绩优/绩查规则的员工，在前n个周期的绩优/绩查名单是否都存在
                    // 5.1 先根据绩优/绩差规则去周期表里匹配对应的周期
                    Map<String, List<Cycle>> cycleMap = cycleDao.mapConsecutiveCycleByYearValue(
                            event.getTenantId(), perfStatisticRule.getCycleType(), cycle.getYear(), cycle.getValue(), perfStatisticRule.getStatisticDuration());
                    // cycleMap.isEmpty()为空且duration为1，说明是连续一个周期
                    if (cycleMap.isEmpty() && perfStatisticRule.getStatisticDuration() > 1) {
                        continue;
                    }
                    // 5.2 根据周期，去TopResultOfCycle里查有没有历史记录，有直接用里面的统计结果，没有则根据规则去统计
                    // 拿到当前周期绩优/绩差员工id的列表
                    Set<String> currentTopEmpIdSet = currentTopResultList.stream().map(TopResultOfCycle::getEmpId).collect(Collectors.toSet());
                    boolean continueToNextFlag = false; //标志变量
                    // 同周期，只需要有任意一个任务满足绩优/绩差条件，则是连续的
                    for (String yearAndValue : cycleMap.keySet()) {
                        List<String> cycleIds = cycleMap.get(yearAndValue).stream().map(Cycle::getId).collect(Collectors.toList());
                        query.setCycleIds(cycleIds);
                        query.setEmpIds(new ArrayList<>(currentTopEmpIdSet));
                        query.setRuleConfigId(perfStatisticRule.getRuleConfigId());
                        List<TopResultOfCycle> historyTopResultOfCycles = topResultOfCycleSvc.listByCycleIdsAndRuleId(query);
                        // 计算未统计的周期ID
                        List<String> unCountCycleIds = consecutiveTopResultSvc.getUncountedCycleIds(historyTopResultOfCycles,cycleIds);
                        // 如果周期绩优/绩差结果为空，则去进行实时统计
                        if (CollUtil.isEmpty(historyTopResultOfCycles) || CollUtil.isNotEmpty(unCountCycleIds)) {
                            if (CollUtil.isNotEmpty(unCountCycleIds)) {
                                query.setCycleIds(unCountCycleIds);
                            }
                            historyTopResultOfCycles = topResultOfCycleSvc.countHistoryTopResultByEmpIds(query);
                            // 如果实时统计的数据为空，则不存在连续，则直接退出循环
                            if (CollUtil.isEmpty(historyTopResultOfCycles)) {
                                continueToNextFlag = true;
                                break;
                            }
                        }
                        // 将当前周期符合满足绩优/绩差条件的员工id赋值给下一个需要循环查找的周期
                        currentTopEmpIdSet = historyTopResultOfCycles.stream().map(TopResultOfCycle::getEmpId).collect(Collectors.toSet());
                        historyTopResults.addAll(historyTopResultOfCycles);
                    }
                    // 说明当前规则时存在不连续的，不符合规则，不纳入统计
                    if (continueToNextFlag) {
                        continue;
                    }
                    historyTopResults.addAll(currentTopResultList);

                    if (CollUtil.isNotEmpty(currentTopEmpIdSet) && CollUtil.isNotEmpty(historyTopResults)) {
                        ConsecutiveTopResultEmpBatch topResultEmpBatch = new ConsecutiveTopResultEmpBatch();
                        topResultEmpBatch.setCompanyId(tenantId)
                                .setCycleId(cycle.getId())
                                .setRuleType(perfStatisticRule.getRuleType())
                                .setRuleConfigId(perfStatisticRule.getRuleConfigId())
                                .setRuleName(perfStatisticRule.getRuleName())
                                .setStatisticRule(perfStatisticRule.getStatisticRule())
                                .setStatisticRuleDetail(perfStatisticRule)
                                .setTopResultEmpIdList(new ArrayList<>(currentTopEmpIdSet))
                                .setHistoryTopResults(historyTopResults)
                                .setRuleApplyType(perfStatisticRule.getRuleApplyType())
                                .setStatisticDuration(perfStatisticRule.getStatisticDuration())
                                .setCycleType(perfStatisticRule.getCycleType());
                        addList.add(topResultEmpBatch);
                    }
                }
            }

            // 批量保存连续周期绩优/绩差结果至数据库
            if (CollUtil.isNotEmpty(addList)) {
                consecutiveTopResultSvc.addResultBatch(tenantId, addList, null,"system");
            }
            log.info("连续绩优/绩差统计事件处理完成");
        }

        /**
         * 根据系统最新规则和历史规则快照，重置统计规则
         * @param statisticRuleList
         * @param historyRuleDataList
         * @return
         */
        private List<PerfStatisticRule> reSetRuleList(List<PerfStatisticRule> statisticRuleList, List<PerfStatisticRule> historyRuleDataList) {
            // 如果最新规则列表为空，则直接使用历史规则快照列表
            if (CollUtil.isEmpty(statisticRuleList)) {
                statisticRuleList = historyRuleDataList;
            }
            Set<String> historyIds = historyRuleDataList.stream().map(PerfStatisticRule::getRuleConfigId).collect(Collectors.toSet());
            // 过滤出最新规则中不在历史规则快照中的规则，并与历史规则快照合并
            return Stream.concat(statisticRuleList.stream()
                                    .filter(rule -> !historyIds.contains(rule.getRuleConfigId())),
                            historyRuleDataList.stream())
                    .collect(Collectors.toList());
        }
    }
}
