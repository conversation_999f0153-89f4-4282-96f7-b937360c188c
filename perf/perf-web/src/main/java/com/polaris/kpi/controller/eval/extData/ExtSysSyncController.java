package com.polaris.kpi.controller.eval.extData;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.perf.www.common.web.AccountBaseController;
import com.polaris.kpi.eval.app.TransactionWrap;
import com.polaris.kpi.eval.app.externalData.appsvc.ExtDataFieldSvc;
import com.polaris.kpi.eval.app.externalData.appsvc.ExtDataSyncSvc;
import com.polaris.kpi.eval.app.externalData.appsvc.ExtDataSysSvc;
import com.polaris.kpi.eval.app.externalData.dto.ExtDataSyncCmd;
import com.polaris.kpi.eval.app.task.appsvc.EmpEvalJobAppSvc;
import com.polaris.kpi.eval.infr.extData.pojo.po.ExtDataSyncPo;
import com.polaris.kpi.eval.infr.extData.query.ExtDataSyncQuery;
import com.polaris.kpi.extData.domain.entity.ExtDataField;
import com.polaris.kpi.extData.domain.entity.ExtDataFieldParam;
import com.polaris.kpi.extData.domain.entity.ExtDataSys;
import lombok.extern.slf4j.Slf4j;
import org.lufei.ibatis.mapper.PagedList;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: xuxw
 * @Date: 2025/02/24 11:39
 * @Description:
 */
@Slf4j
@RestController
@RequestMapping("/ext/data/")
public class ExtSysSyncController extends AccountBaseController {

    @Autowired
    private ExtDataSyncSvc svc;
    @Autowired
    private EmpEvalJobAppSvc jobAppSvc;
    @Autowired
    private ExtDataSysSvc sysSvc;
    @Autowired
    private ExtDataFieldSvc fieldSvc;
    @Autowired
    private TransactionWrap tx;

    /**
     * 数据同步
     * @param cmd
     * @return
     */
    @RequestMapping("/sync")
    public SingleResponse sync(@RequestBody ExtDataSyncCmd cmd){
        cmd.buildAddParam(getCompanyId(), getEmpId());
        cmd.buildUpdateParam(getEmpId(), getCompanyId());
        String res = svc.dataSync(cmd);
        return SingleResponse.of(res);
    }

    @RequestMapping("/autoSync")
    @Scheduled(cron = "0 0 6 * * ? ")
    public SingleResponse autoSync(){
        List<ExtDataSys> extSys = sysSvc.listAllExtSys();
        if (CollUtil.isNotEmpty(extSys)){
            List<String> companyIds = extSys.stream().map(s -> s.getCompanyId()).collect(Collectors.toList());
            for (String companyId : companyIds){
                log.info("公司ID：{} 开始同步外部数据", companyId );
                jobAppSvc.lockCompanyForDayJob(companyId, "extDataSync");
                String tid = MDC.get("tid");
                tx.runAsyn(tid, () ->{
                    List<ExtDataField> fields = fieldSvc.listAllField(companyId);
                    if (CollUtil.isNotEmpty(fields)){
                        ExtDataSyncCmd cmd;
                        List<String> fieldIds = fields.stream().map(s -> s.getId()).collect(Collectors.toList());
                        log.info("公司ID：{} 开始同步字段ID：{} 外部数据", companyId, String.join(",", fieldIds));
                        for (ExtDataField field : fields){
                            cmd = new ExtDataSyncCmd();
                            cmd.setExtDataSysId(field.getExtDataSysId());
                            cmd.setExtDataFieldId(field.getId());
                            cmd.setCompanyId(companyId);
                            svc.dataSync(cmd);
                        }
                    }
                });
                MDC.clear();
            }
        }
        return SingleResponse.buildSuccess();
    }

    /**
     * 分页查询同步数据
     * @param query
     * @return
     */
    @RequestMapping("/pagedSynData")
    public MultiResponse pagedSynData(@RequestBody ExtDataSyncQuery query){
        query.setCompanyId(getCompanyId());
        PagedList<ExtDataSyncPo> res = svc.pagedSynData(query);
        return MultiResponse.of(res, res.getPageNo(), res.getTotalRow(), res.getPageSize());
    }

    /**
     * 查询动态列
     * @param query
     * @return
     */
    @RequestMapping("/listDynamicColumn")
    public SingleResponse listDynamicColumn(@RequestBody ExtDataSyncQuery query){
        query.setCompanyId(getCompanyId());
        List<ExtDataFieldParam> res = svc.listDynamicColumn(query);
        return SingleResponse.of(res);
    }
}
