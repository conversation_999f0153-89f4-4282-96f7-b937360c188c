package com.polaris.kpi.controller.report;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson.JSON;
import com.perf.www.biz.kpi.CompanyItemBiz;
import com.perf.www.biz.report.ReportBiz;
import com.perf.www.common.annotation.NoneLogin;
import com.perf.www.common.utils.page.ModelPagedList;
import com.perf.www.common.web.AccountBaseController;
import com.perf.www.common.web.ResponseInfo;
import com.perf.www.model.kpi.CompanyKpiItemModel;
import com.perf.www.model.task.PerfEvaluateTaskBaseModel;
import com.perf.www.vo.report.*;
import com.perf.www.vo.report.query.ReportTaskQueryVO;
import com.perf.www.vo.task.query.EvaluationTaskUserQueryVO;
import com.polaris.kpi.eval.app.task.appsvc.EmpEvalAppSvc;
import com.polaris.kpi.eval.infr.task.dao.EvaluateTaskDao;
import com.polaris.kpi.eval.infr.task.ppojo.EvalUserOfDeptPo;
import com.polaris.kpi.eval.infr.task.ppojo.OrgGradeDistributionPo;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskBaseDo;
import com.polaris.kpi.eval.infr.task.ppojo.report.*;
import com.polaris.kpi.eval.infr.task.query.ReportSeriesQuery;
import com.polaris.kpi.eval.infr.task.query.TaskKpiItemQuery;
import com.polaris.kpi.eval.infr.task.query.TaskUserQuery;
import com.polaris.kpi.eval.infr.task.query.report.ReportQuery;
import com.polaris.kpi.eval.infr.task.query.report.TaskReportQry;
import com.polaris.kpi.org.domain.dept.type.TaskId;
import com.polaris.kpi.report.app.ReportAppSvc;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import org.apache.commons.lang3.StringUtils;
import org.lufei.ibatis.mapper.PagedList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <h3>绩效报表</h3>
 * created by Bruce.R on 2020/9/23
 */
@RestController
@RequestMapping("report")
public class ReportController extends AccountBaseController {
    @Resource
    private ReportBiz reportApi;
    @Autowired
    private ReportAppSvc reportAppSvc;
    @Resource
    private CompanyItemBiz companyItemApi;
    private final Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 年度数据分析
     *
     * @param queryVO
     * @return
     */
    @RequestMapping("listYearCountOption")
    public ResponseInfo listYearCountOption(ReportTaskQueryVO queryVO) {
        queryVO.setCompanyId(getCompanyId());
        queryVO.setCreatedUser(getEmpId());
        List<ReportTopCountOptionVO> list = reportApi.listYearCountOption(queryVO);
        return ResponseInfo.success(list);
    }

    /**
     * 考核数据汇总
     */
    @RequestMapping("listYearTaskCount")
    public ResponseInfo listYearTaskCount(@RequestBody ReportTaskQueryVO reportTaskQueryVO) {
        reportTaskQueryVO.setCompanyId(getCompanyId());
        reportTaskQueryVO.setCreatedUser(getEmpId());
        return ResponseInfo.success(reportApi.listPagedYearTaskCount(reportTaskQueryVO, getEmpId()));
    }


    /**
     * 考核数据详情
     */
    @RequestMapping("listPagedUserTask")
    public ResponseInfo listPagedUserTask(ReportTaskQueryVO queryVO) {
        queryVO.setCompanyId(getCompanyId());
        queryVO.setCreatedUser(getEmpId());
        return ResponseInfo.success(reportApi.listPagedUserTask(queryVO));
    }

    /**
     * 考核数据详情统计
     *
     * @param year
     * @return
     */
    @RequestMapping("countUserTask")
    public ResponseInfo countUserTask(String orgId, String empId, String year) {
        ReportUserTaskCountVO reportUserTaskCountVO = reportApi.countUserTask(getCompanyId(), empId, orgId, year);
        return ResponseInfo.success(reportUserTaskCountVO);
    }

    @Autowired
    private EmpEvalAppSvc empEvalAppSvc;

    /**
     * 被考核人饼图
     *
     * @param query
     * @return
     */
    @RequestMapping("listTaskUserSeries")
    public MultiResponse listTaskUserSeries(@RequestBody TaskUserQuery query) {
        query.accOp(getCompanyId(), getEmpId());
        PagedList<ReportSeriesDataVO> res = empEvalAppSvc.pageEvaluateSeries(query);
        return MultiResponse.of(res.getData(), res.getPageNo(), res.getTotalRow(), res.getPageSize());
    }

    /**
     * 量化指标完成度饼图
     *
     * @param query
     * @return
     */
    @RequestMapping("listMeasurableItemProgressSeries")
    public MultiResponse listMeasurableItemProgressSeries(@RequestBody TaskKpiItemQuery query) {
        query.setIsMobile(false);
        query.accOp(getCompanyId(), getEmpId());
        PagedList<ReportSeriesDataVO> res = empEvalAppSvc.pageMeasurableItemProgress(query);
        return MultiResponse.of(res.getData(), res.getPageNo(), res.getTotalRow(), res.getPageSize());
    }

    /**
     * 移动量化指标完成度饼图
     *
     * @param query
     * @return
     */
    @RequestMapping("listMeasurableItemProgressSeriesForMobile")
    public MultiResponse listMeasurableItemProgressSeriesForMobile(@RequestBody TaskKpiItemQuery query) {
        query.setIsMobile(true);
        query.accOp(getCompanyId(), getEmpId());
        PagedList<ReportSeriesDataVO> res = empEvalAppSvc.pageMeasurableItemProgress(query);
        return MultiResponse.of(res.getData(), res.getPageNo(), res.getTotalRow(), res.getPageSize());
    }

    /**
     * 关键行动完成度饼图
     *
     * @param taskId
     * @return
     */
    @RequestMapping("listTaskActionProgressSeries")
    public ResponseInfo listTaskActionProgressSeries(String taskId) {
        List<ReportSeriesDataVO> list = reportApi.listTaskActionProgressSeries(getCompanyId(), taskId);
        return ResponseInfo.success(list);
    }


    @Autowired
    private EvaluateTaskDao taskDao;

    //人员分布
    @RequestMapping("listEmpDistribution")
    public MultiResponse pageEvalUserOfDept(@RequestBody ReportSeriesQuery query) {
        query.checkParam();
        query.accOp(getCompanyId(), getEmpId());
        PerfEvaluateTaskBaseDo task = taskDao.getTaskById(new TaskId(query.getTaskId()));
        //1.0兼容，1.0 user里面没有对于的orgId
        if (task.wasFromTmpTask()) {
            PagedList<EvalUserOfDeptPo> pos = empEvalAppSvc.pagedEmpOfDept(query);
            return MultiResponse.of(pos.getData(), pos.getPageNo(), pos.getTotalRow(), pos.getPageSize());
        }
        PagedList<EvalUserOfDeptPo> pos = empEvalAppSvc.pageEvalUserOfDept(query);
        return MultiResponse.of(pos.getData(), pos.getPageNo(), pos.getTotalRow(), pos.getPageSize());
    }

    /**
     * 考核等级分布
     *
     * @param query
     * @return
     */
    @RequestMapping("listTaskUserLevelDistribution")
    public ResponseInfo listTaskUserLevelDistribution(@RequestBody ReportQuery query) {
        query.setCompanyId(getCompanyId());
        query.setLoginEmpId(getEmpId());
        query.setOpEmpId(getEmpId());
        query.splitTrimOrg();
        return ResponseInfo.success(reportAppSvc.listGradeDistribution(query));
    }

    /**
     * dingding 工作台
     *
     * @param corpId
     * @param dingUserId
     * @return
     */
    @NoneLogin
    @RequestMapping({"dingdingWorkbench", "lastLevelRank"})
    public ResponseInfo ddWorkbench(@RequestParam("corpId") String corpId, @RequestParam("userid") String dingUserId) {
        WorkbenchVo levelList = reportApi.ddWorkbench(corpId);
        if (levelList == null) {
            ResponseInfo.error("data not found.");
        }
        return ResponseInfo.success(levelList);
    }

    /**
     * 分数活力曲线
     *
     * @param taskId
     * @return
     */
    @RequestMapping("listTaskUserScoreDistribution")
    public ResponseInfo listTaskUserScoreDistribution(String taskId) {
        List<ReportSeriesDataVO> list = reportApi.listTaskUserScoreDistribution(getCompanyId(), taskId);
        return ResponseInfo.success(list);
    }

    /**
     * 考核结果排名
     *
     * @param queryVO
     * @return
     */
    @RequestMapping("listEmpTaskRank")
    public ResponseInfo listEmpTaskRank(ReportTaskQueryVO queryVO) {
        queryVO.setCompanyId(getCompanyId());
        queryVO.setCreatedUser(getEmpId());
        return ResponseInfo.success(reportApi.listPagedEmpTaskRank(queryVO));
    }

    /**
     * 考核结果排名--等级下拉框
     *
     * @param queryVO
     * @return
     */
    @RequestMapping("queryEmpRankLevel")
    public ResponseInfo queryEmpRankLevel(ReportTaskQueryVO queryVO) {
        queryVO.setCompanyId(getCompanyId());
        queryVO.setCreatedUser(getEmpId());
        return ResponseInfo.success(reportApi.queryEmpRankLevel(queryVO));
    }

    /**
     * 移动端-统计-人员列表
     *
     * @param taskId
     * @return
     */
    @RequestMapping("listPagedEmpTask")
    public ResponseInfo listPagedEmpTask(String taskId, String taskStatus, @RequestParam(defaultValue = "1") Integer pageNo, @RequestParam(defaultValue = "10") Integer pageSize) {
        ModelPagedList<ReportTaskUserVO> list = reportApi.listPagedEmpTask(getCompanyId(), taskId, taskStatus, pageNo, pageSize);
        return ResponseInfo.success(list);
    }

    @RequestMapping("downloadYearTaskDetail")
    public void downloadYearTaskDetailV3(@RequestBody TaskReportQry qry) {
        logger.info("downloadYearTaskDetail===param:{}", JSON.toJSONString(qry));
        qry.accOpEmp(getCompanyId(), getEmpId(), getAdminType());
        String tid = MDC.get("tid");
        qry.setTid(tid);
        qry.setPageNo(1);
        qry.setPageSize(200);
        qry.setFilterOrgId(qry.getOrgId());
        if (!qry.userIdMergeNew()) {
            reportAppSvc.downloadYearDetailWithMerge(qry);
        } else {
            reportAppSvc.downloadYearDetailWithItem(qry);
        }
    }

    @RequestMapping("downloadOrgTaskLevelAnalysis")
    public void downloadOrgTaskLevelAnalysis(@RequestBody ReportQuery qry) {
        logger.info("downloadOrgTaskLevelAnalysis===param:{}", JSON.toJSONString(qry));
        qry.setCompanyId(getCompanyId());
        qry.setCreatedUser(getEmpId());
        qry.setLoginEmpId(getEmpId());
        String tid = MDC.get("tid");
        qry.setTid(tid);
        qry.setPageNo(1);
        qry.setPageSize(200);
        reportAppSvc.downloadOrgTaskLevelAnalysis(qry);
    }

    @RequestMapping("test/fix/pagedYearTaskDetail")
    public PagedList<ReportUserTaskExcelPo> pagedYearTaskDetail(TaskReportQry qry) {
        logger.info("pagedYearTaskDetail===param:{}", JSON.toJSONString(qry));
        qry.accOpEmp(getCompanyId(), getEmpId(), getAdminType());
        String tid = MDC.get("tid");
        qry.setTid(tid);
        return reportAppSvc.pagedTaskDetail(null, qry);
    }

    /**
     * 导出指标分析
     */
    @RequestMapping("downloadItemScore")
    public void downloadItemScore(TaskReportQry qry) {
        logger.info("downloadItemScore===param:{}", JSON.toJSONString(qry));
        qry.setCompanyId(getCompanyId());
        qry.setCreatedUser(getEmpId());
        qry.setLoginEmpId(getEmpId());
        qry.setPageNo(1);
        qry.setPageSize(200);
        qry.setFilterOrgId(qry.getOrgId());
        String tid = MDC.get("tid");
        qry.setTid(tid);
        reportAppSvc.downloadItemScoreReport(qry);
    }

    @RequestMapping("downloadYearReport")
    public void downloadYearReportV3(@RequestBody ReportQuery qry) {
        logger.info("downloadYearReport===param:{}", JSON.toJSONString(qry));
        qry.setCompanyId(getCompanyId());
        qry.setLoginEmpId(getEmpId());
        qry.setPageSize(300);
        String tid = MDC.get("tid");
        qry.setTid(tid);
        if (qry.isMonth()) {
            reportAppSvc.downloadMothReportR(qry);
        }
        if (qry.isYear()) {
            reportAppSvc.downloadYearReport(qry);
        }
        if (qry.isQuarter()) {
            reportAppSvc.downloadQuarterReport(qry);
        }
    }

    //季度报告
    @RequestMapping("pagedQuarterReport")
    public MultiResponse pagedQuarterReport(@RequestBody ReportQuery query) {
        query.setCompanyId(getCompanyId());
        query.setLoginEmpId(getEmpId());
//        PagedList<EmpYearReportItemPo> res = reportAppSvc.pagedQuarterReport(query);
        PagedList<EmpYearReportItemPo> res = reportAppSvc.pagedReport(query);
        return MultiResponse.of(res.getData(), res.getPageNo(), res.getTotalRow(), res.getPageSize());
    }

    @RequestMapping("downloadQuarterReport")
    public void downloadQuarterReport(@RequestBody ReportQuery query) {
        logger.info("downloadQuarterReport===param:{}", JSON.toJSONString(query));
        checkParamNull(query.getYear(), query.getCycleType());
        query.setCompanyId(getCompanyId());
        query.setLoginEmpId(getEmpId());
        query.setPageSize(300);
        String tid = MDC.get("tid");
        query.setTid(tid);
        reportAppSvc.downloadQuarterReport(query);
    }

    //年度报告
    @RequestMapping("pagedYearReport")
    public MultiResponse pagedYearsReport(@RequestBody ReportQuery query) {
        query.setCompanyId(getCompanyId());
        query.setLoginEmpId(getEmpId());
        //  PagedList<YearReportPo> res = reportAppSvc.pagedYearReport(query);
        PagedList<EmpYearReportItemPo> res = reportAppSvc.pagedReport(query);
        return MultiResponse.of(res.getData(), res.getPageNo(), res.getTotalRow(), res.getPageSize());
    }

    //生成年度报告
    @RequestMapping("createYearReport")
    public ResponseInfo createYearReport(@RequestBody ReportQuery query) {
        query.setTid(MDC.get("tid"));
        query.setCompanyId(getCompanyId());
        query.setLoginEmpId(getEmpId());
        // reportAppSvc.builderYearReport(query);
        reportAppSvc.createYearReport(query.getTid(), new TenantId(getCompanyId()), new EmpId(getEmpId()), query.getYear(), query.isAvg(), query.getPerformanceType());
        return ResponseInfo.success(null);
    }

    //查询生成年度报告状态
    @RequestMapping("getCreateYearReportStatus")
    public SingleResponse getCreateYearReportStatus(String year) {
        Map<String, String> createYearReportStatus = reportAppSvc.getCreateYearReportStatus(getCompanyId(), year);
        return SingleResponse.of(createYearReportStatus);
    }


    @RequestMapping("queryScoreCount")
    public ResponseInfo queryScoreCount(ReportTaskQueryVO queryVO) {
        queryVO.setCompanyId(getCompanyId());
        queryVO.setCreatedUser(getEmpId());
        return ResponseInfo.success(reportApi.queryScoreCount(queryVO));
    }

    @RequestMapping("queryItemScoreCount")
    public ResponseInfo queryItemScoreCount(ReportTaskQueryVO queryVO) {
        queryVO.setCompanyId(getCompanyId());
        queryVO.setCreatedUser(getEmpId());
        return ResponseInfo.success(reportApi.queryItemScoreCount(queryVO));
    }

    @RequestMapping("queryItemFinishValueCount")
    public ResponseInfo queryItemFinishValueCount(ReportTaskQueryVO queryVO) {
        queryVO.setCompanyId(getCompanyId());
        queryVO.setCreatedUser(getEmpId());
        Map map = new HashMap<>(2);
        map.put("count", reportApi.queryItemFinishValueCount(queryVO));
        map.put("itemUnit", "");
        if (StringUtils.isNotBlank(queryVO.getItemId())) {
            CompanyKpiItemModel itemModel = companyItemApi.queryById(queryVO.getItemId());
            if (Objects.nonNull(itemModel)) {
                map.put("itemUnit", itemModel.getItemUnit());
            }
        }
        return ResponseInfo.success(map);
    }

    @RequestMapping("queryItemScoreRank")
    public ResponseInfo queryItemScoreRank(ReportTaskQueryVO queryVO) {
        queryVO.setCompanyId(getCompanyId());
        queryVO.setCreatedUser(getEmpId());
        return ResponseInfo.success(reportApi.queryItemScoreRank(queryVO));
    }

    @RequestMapping("queryItemReportTask")
    public MultiResponse queryItemReportTask(EvaluationTaskUserQueryVO queryVO) {
        queryVO.setCompanyId(getCompanyId());
        PagedList<PerfEvaluateTaskBaseModel> res = reportApi.pagedItemReportTask(queryVO);
        return MultiResponse.of(res.getData(), res.getPageNo(), res.getTotalRow(), res.getPageSize());
    }

    @RequestMapping("pageMonthlyReport")
    public MultiResponse pageMonthlyReport(@RequestBody ReportQuery query) {
        query.setCompanyId(getCompanyId());
        query.setLoginEmpId(getEmpId());
//        PagedList<MonthlyReportPo> res = reportAppSvc.pageMonthlyReport(query);
        PagedList<EmpYearReportItemPo> res = reportAppSvc.pagedReport(query);
        return MultiResponse.of(res.getData(), res.getPageNo(), res.getTotalRow(), res.getPageSize());
    }

    @RequestMapping("queryYearReportTaskDetail")
    public ResponseInfo queryYearReportTaskDetail(@RequestBody ReportTaskQueryVO queryVO) {
        queryVO.setCompanyId(getCompanyId());
        return ResponseInfo.success(reportApi.queryYearReportTaskDetail(queryVO));
    }

    @RequestMapping("queryOrgPerm")
    public ResponseInfo queryOrgPerm(ReportTaskQueryVO queryVO) {
        queryVO.setCompanyId(getCompanyId());
        queryVO.setCreatedUser(getEmpId());
        return ResponseInfo.success(reportApi.queryOrgPerm(queryVO));
    }

    /**
     * 部门考核等级配额报表
     *
     * @param queryVO
     * @return
     */
    @RequestMapping("queryReportOrgLevelQuotaList")
    public ResponseInfo queryReportOrgLevelQuotaList(ReportTaskQueryVO queryVO) {
        queryVO.setCompanyId(getCompanyId());
        return ResponseInfo.success(reportApi.queryReportOrgLevelQuotaList(queryVO));
    }

    /**
     * 配额等级占比相关任务详情
     *
     * @param queryVO
     * @return
     */
    @RequestMapping("querylevelQuotaRatioTaskInfo")
    public ResponseInfo querylevelQuotaRatioTaskInfo(ReportTaskQueryVO queryVO) {
        queryVO.setCompanyId(getCompanyId());
        return ResponseInfo.success(reportApi.querylevelQuotaRatioTaskInfo(queryVO));
    }


    /**
     * 周期内部门等级分布所有的等级
     *
     * @param query
     * @return
     */
    @RequestMapping("listOrgAnaylsisLevels")
    public ResponseInfo listOrgAnaylsisLevels(@RequestBody ReportQuery query) {
        query.setCompanyId(getCompanyId());
        query.setLoginEmpId(getEmpId());
        query.setOpEmpId(getEmpId());
        query.splitTrimOrg();
        return ResponseInfo.success(reportAppSvc.listOrgAnaylsisLevels(query));
    }

    /**
     * 部门等级分布
     *
     * @param query
     * @return
     */
    @RequestMapping("pageOrgLevelDistribution")
    public MultiResponse pageOrgLevelDistribution(@RequestBody ReportQuery query) {
        query.setCompanyId(getCompanyId());
        query.setLoginEmpId(getEmpId());
        query.setOpEmpId(getEmpId());
        query.splitTrimOrg();
        PagedList<OrgGradeDistributionPo> res = reportAppSvc.pageOrgLevelDistribution(query);
        return MultiResponse.of(res.getData(), res.getPageNo(), res.getTotalRow(), res.getPageSize());
    }
}
