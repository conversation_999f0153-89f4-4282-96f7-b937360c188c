package com.polaris.kpi.event.eval;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.event.EventHandler;
import com.perf.www.controller.BaseEventHander;
import com.polaris.kpi.ExecutorEnum;
import com.polaris.kpi.eval.app.task.appsvc.ScoreStageAppSvc;
import com.polaris.kpi.eval.domain.task.event.ChangedAskScoreEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;

public class ChangedAskScoreEventHandler {

    @Component
    @EventHandler
    public static class ChangedAskScoreHandler extends BaseEventHander<ChangedAskScoreEvent> {
        @Autowired
        private ScoreStageAppSvc scoreStageAppSvc;

        public ExecutorService getExecutor() {
            return hightExecutor;
        }

        @Override
        public Response execute(ChangedAskScoreEvent event) {
            return super.execute(event);
        }

        @EventListener
        @Async(ExecutorEnum.high)
        @Override
        public void handerEvent(ChangedAskScoreEvent event) {
            event.start();
           // scoreStageAppSvc.changedAskScore(event);
            scoreStageAppSvc.changedAskScoreV3(event);
        }
    }
}
