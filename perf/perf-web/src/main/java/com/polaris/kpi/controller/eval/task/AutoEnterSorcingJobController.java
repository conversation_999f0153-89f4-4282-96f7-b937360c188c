package com.polaris.kpi.controller.eval.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.polaris.kpi.eval.app.task.appsvc.EmpEvalJobAppSvc;
import com.polaris.kpi.eval.app.task.appsvc.EvalTaskAppSvc;
import com.polaris.kpi.eval.app.task.dto.EnterScoringCmd;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.repo.TaskUserRepo;
import com.polaris.kpi.eval.infr.task.dao.TaskUserDao;
import com.polaris.kpi.org.domain.dept.entity.Company;
import com.polaris.kpi.org.infr.company.dao.CompanyDaoImpl;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.controller.eval.task
 * @Author: lufei
 * @CreateTime: 2022-08-15  14:49
 * @Description: TODO
 * @Version: 1.0
 */
@Slf4j
@RestController
public class AutoEnterSorcingJobController {

    @Autowired
    private CompanyDaoImpl companyDao;
    @Autowired
    private TaskUserDao taskUserDao;
    @Autowired
    private EvalTaskAppSvc taskAppSvc;
    @Autowired
    private EmpEvalJobAppSvc jobAppSvc;
    @Autowired
    private TaskUserRepo userRepo;


    //2.0自动定时进入评分阶段,全量一天一次8:00执行
    @RequestMapping("perf/task/autoEnterSorcing")
    @Scheduled(cron = "0 0 8 * * ?")
    public SingleResponse autoEnterSorcing() {
        MDC.put("tid", IdUtil.fastSimpleUUID());
        log.info("定时发起评分任务开始");
        //查询所有有效公司列表
        List<Company> companies = companyDao.allRunningCompany(2000000);
        if (CollectionUtils.isEmpty(companies)) {
            log.info("没有使用中的公司");
            return SingleResponse.of(companies.size());
        }
        for (Company company : companies) {
            try {
                String uniKey = jobAppSvc.lockCompanyForDayJob(company.getId(), "autoEnterSorcing");
                MDC.put("tid", uniKey);
                List<String> taskUserIds = taskUserDao.listAutoEnterScoreTaskUser(company.getId());
                if (CollUtil.isEmpty(taskUserIds)) {
                    continue;
                }
                log.info("autoEnterSorcing={}", uniKey);
                EnterScoringCmd cmd = new EnterScoringCmd(taskUserIds, true);
                cmd.setOpEmpId(new TenantId(company.getId()), null);
                cmd.setAuto(true);
                taskAppSvc.batchEnterScoring(cmd);
            } catch (DataIntegrityViolationException e) {
                log.error("定时进入评分冲突,公司:{}", company.getId());
            } catch (Exception e) {
                log.error("定时进入评分出错,公司:{}, error:{}", company.getId(), e.getMessage(), e);
            }
        }
        //taskAppSvc.delSchedule(batchNo);
        log.info("评分定时任务结束");
        MDC.clear();
        return SingleResponse.of(companies.size());
    }

    //自动定时进入评分阶段 备用修正接口,用于对单个公司进行订正使用
    @RequestMapping("perf/task/fixAutoEnterSorcing")
    public SingleResponse testAutoEnterSorcing(String companyId) {
        MDC.put("tid", IdUtil.fastSimpleUUID());
        try {
            log.info("companyId = {}", companyId);
            List<String> taskUserIds = taskUserDao.listAutoEnterScoreTaskUser(companyId);
            if (CollUtil.isEmpty(taskUserIds)) {
                return SingleResponse.buildSuccess();
            }
            EnterScoringCmd cmd = new EnterScoringCmd(taskUserIds, true);
            cmd.setOpEmpId(new TenantId(companyId), null);
            cmd.setAuto(true);
            taskAppSvc.batchEnterScoring(cmd);
        } catch (Exception e) {
            log.error("定时进入评分出错公司:{}", companyId);
            log.error("定时进入评分出错:{}", e.getMessage(), e);
        }
        MDC.clear();
        return SingleResponse.buildSuccess();
    }


    //指标跟踪提醒任务 替换 PerfEvaluateItemNoticeJob
    //@RequestMapping("perf/task/itemNotice")
    ////@Scheduled(cron = "0 0 0/1 * * ?")
    //public SingleResponse itemNotice() {
    //    MDC.put("tid", IdUtil.fastSimpleUUID());
    //    log.info("指标提醒定时任务开始");
    //    //查询所有有效公司列表
    //    List<Company> companies = companyDao.allRunningCompany(2000000);
    //    if (CollectionUtils.isEmpty(companies)) {
    //        log.info("没有使用中的公司");
    //        return SingleResponse.of(companies.size());
    //    }
    //    for (Company company : companies) {
    //        Long runId = null;
    //        try {
    //            runId = jobAppSvc.markCompanyRuning(company.getId(), "itemNotice");
    //        } catch (DuplicateKeyException dupExp) {
    //            log.warn("指标提醒定抢占失败,公司:{} 跳过", company.getId());
    //            continue;
    //        }
    //        try {
    //            List<PerfEvaluateItemNoticeDo> items = jobAppSvc.listNowItemNotice(company.getId());
    //            if (CollUtil.isEmpty(items)) {
    //                log.info("没有需要执行的指标提醒 companyId:{}", company.getId());
    //                continue;
    //            }
    //            for (PerfEvaluateItemNoticeDo item : items) {
    //                jobAppSvc.noticeItem(item);
    //            }
    //        } catch (Exception e) {
    //            log.error("指标提醒定时任务出错,公司:{}, error:{}", company.getId(), e.getMessage(), e);
    //        } finally {
    //            if (null != runId) {
    //                taskAppSvc.delSchedule(runId);
    //            }
    //        }
    //    }
    //    log.info("指标提醒定时任务结束");
    //    MDC.clear();
    //    return SingleResponse.of(companies.size());
    //}


    //自动确认考核结果 替换 {@link com.perf.www.job.perf.TaskAutoResultAffirmJob}
    @RequestMapping("perf/task/taskAutoResultAffirmJob")
    @Scheduled(cron = "0 0 0 * * ?")
    public SingleResponse taskAutoResultAffirmJob() {
        MDC.put("tid", IdUtil.fastSimpleUUID());
        log.info("自动确认考核结果任务开始");
        //查询所有有效公司列表
        List<Company> companies = companyDao.allRunningCompany(2000000);
        if (CollectionUtils.isEmpty(companies)) {
            log.info("自动确认考核结果结束-没有使用中的公司");
            return SingleResponse.of(companies.size());
        }
        for (Company company : companies) {
            try {
                //每日执行一次
                String uniKey = jobAppSvc.lockCompanyForDayJob(company.getId(), "autoResultAffirmJob");
                MDC.put("tid", uniKey);
                List<String> userIds = jobAppSvc.listAutoAffirmTaskUser(company.getId());
                for (String userId : userIds) {
                    MDC.put("tid", uniKey + userId);
                    jobAppSvc.autoResultAffirm(new TenantId(company.getId()), userId);
                }
            } catch (DataIntegrityViolationException e) {
                log.error("autoResultAffirmJob冲突,公司:{}", company.getId());
            } catch (Exception e) {
                log.error("自动确认考核结果结束,公司:" + company.getId() + "\n" + e.getMessage(), e);
            }
        }
        log.info("自动确认考核结果结束");
        MDC.clear();
        return SingleResponse.of(companies.size());
    }

    //超时未确认，自动终止任务
    @RequestMapping("perf/task/autoStopNotConfirm")
    @Scheduled(cron = "0 0 1 * * ?")
    public SingleResponse autoStopNotConfirm() {
        MDC.put("tid", IdUtil.fastSimpleUUID());
        log.info("超时未确认，开始扫描自动跳过或终止任务！");
        List<String> companyIds = userRepo.listNotConfirmEvalUserCompanyIds();
        if (CollUtil.isEmpty(companyIds)) {
            log.info("没有符合确认超时自动终止的员工任务");
            return SingleResponse.of(companyIds.size());
        }
        for (String id : companyIds) {
            try {
                String uniKey = jobAppSvc.lockCompanyForDayJob(id, "autoStopNotConfirm");
                MDC.put("tid", uniKey);
                log.info("autoStopNotConfirm={}", uniKey);
                taskAppSvc.autoStopNotConfirm(id);
            } catch (DataIntegrityViolationException e) {
                log.error("超时未确认，自动终止任务,公司:{}", id);
            } catch (Exception e) {
                log.error("超时未确认，自动终止任务,公司:{}, error:{}", id, e.getMessage(), e);
            }
        }
        MDC.clear();
        return SingleResponse.buildSuccess();
    }

}
