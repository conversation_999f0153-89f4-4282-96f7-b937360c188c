package com.polaris.kpi.controller.eval.task.emp;

import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.perf.www.common.annotation.NoneLogin;
import com.perf.www.common.em.OperationLogSceneEnum;
import com.perf.www.common.web.AccountBaseController;
import com.perf.www.common.web.ResponseInfo;
import com.polaris.kpi.eval.app.TransactionWrap;
import com.polaris.kpi.eval.app.confirm.appsvc.ConfirmStageAppSvc;
import com.polaris.kpi.eval.app.confirm.dto.ConfirmEditItemCmd;
import com.polaris.kpi.eval.app.confirm.dto.OnlyConfirmtemCmd;
import com.polaris.kpi.eval.app.task.appsvc.CalibratedAppSvc;
import com.polaris.kpi.eval.app.task.appsvc.EmpEvalAppSvc;
import com.polaris.kpi.eval.app.task.appsvc.EvalTaskAppSvc;
import com.polaris.kpi.eval.app.task.dto.*;
import com.polaris.kpi.eval.app.task.dto.eval.*;
import com.polaris.kpi.eval.domain.task.entity.EvalKpi;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.PerfEvaluateTaskCoach;
import com.polaris.kpi.eval.domain.task.entity.PerfEvaluateTaskDiscuss;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTaskStatusCnt;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalMerge;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.chain.ChainNode;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.chain.ScoreSceneWrap;
import com.polaris.kpi.eval.domain.task.entity.flow.DisplayEvalFlow;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrTask;
import com.polaris.kpi.eval.domain.task.event.KpiItemUpdateFinishedValueEvent;
import com.polaris.kpi.eval.domain.task.event.talent.scoring.EvalScoreChanged;
import com.polaris.kpi.eval.domain.task.type.InnerFields;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.eval.infr.confirm.ppojo.ConfirmEmpEvalPo;
import com.polaris.kpi.eval.infr.task.ppojo.*;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.*;
import com.polaris.kpi.eval.infr.task.query.*;
import com.polaris.kpi.eval.infr.task.query.empeval.*;
import com.polaris.sdk.type.AuditEnum;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.lufei.ibatis.mapper.PagedList;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.controller.eval.task
 * @Author: lufei
 * @CreateTime: 2022-08-24  15:10
 * @Description: 员工考核
 * @Version: 1.0
 */
@Slf4j
@RestController
public class EmpEvalController extends AccountBaseController {
    @Autowired
    private EmpEvalAppSvc empEvalAppSvc;
    @Autowired
    private ConfirmStageAppSvc stageAppSvc;
    @Autowired
    private EvalTaskAppSvc taskAppSvc;
    @Autowired
    private CalibratedAppSvc calibratedAppSvc;
    @Autowired
    private TransactionWrap transactionWrap;

    //阶段流程 入口替换 listTaskProgress
    @RequestMapping("emp/eval/buildNodes")
    public SingleResponse buildNodes(String taskUserId) {
        ScoreSceneWrap scoreSceneWrap = empEvalAppSvc.buildNodes(new TenantId(getCompanyId()), taskUserId);
        return SingleResponse.of(scoreSceneWrap);
    }

    //阶段流程 入口替换 listTaskProgress
    @RequestMapping("emp/eval/getFlow")
    public SingleResponse getFlow(String taskUserId, String period) {
        if (TalentStatus.CONFIRMING.match(period)) {
            return getConfirmingFlow(taskUserId, period);
        }
        if (TalentStatus.CONFIRMED.match(period)) {
            return getConfirmedFlow(taskUserId);
        }
        if (TalentStatus.SCORING.match(period)) {
            List<DisplayEvalFlow> flows = empEvalAppSvc.getScoreFlowV2(new TenantId(getCompanyId()), taskUserId);
            return SingleResponse.of(flows);
        }
        if (TalentStatus.RESULTS_AUDITING.match(period)) {
            return getAuditResultFlow(taskUserId);
        }
        if (TalentStatus.RESULTS_INTERVIEW.match(period)) {
            return getInterviewResultFlow(taskUserId);
        }
        if (TalentStatus.RESULTS_AFFIRMING.match(period)) {
            return getResultAffirmingFlow(taskUserId, period);
        }
        return SingleResponse.buildFailure("009", "period参数错误");
    }

    //查看确认中阶段流程 分步替换 listTaskProgress
    //@RequestMapping("emp/eval/getConfirmingFlow")
    public SingleResponse getConfirmingFlow(String taskUserId, String period) {
        if (!TalentStatus.CONFIRMING.match(period)) {
            return SingleResponse.buildFailure("009", "period参数错误");
        }
        DisplayEvalFlow flow = empEvalAppSvc.getFlow(new TenantId(getCompanyId()), taskUserId, TalentStatus.statusOf(period),
                EvaluateAuditSceneEnum.MODIFY_ITEM_AUDIT.getScene());
        return SingleResponse.of(Arrays.asList(flow));
    }

    // TODO 查看执行中阶段流程 分步替换  listTaskProgress
    //@RequestMapping("emp/eval/getConfirmedFlow")
    public SingleResponse getConfirmedFlow(String taskUserId) {
        List<DisplayEvalFlow> flows = empEvalAppSvc.getConfirmedFlow(new TenantId(getCompanyId()), taskUserId);
        return SingleResponse.of(flows);
    }

    // 查看评分阶段的流程 分步替换  listTaskProgress
    @RequestMapping("test/eval/getScoreFlow2")
    public SingleResponse getScoreFlow(String taskUserId) {
        List<DisplayEvalFlow> flows = empEvalAppSvc.getScoreFlowV2(new TenantId(getCompanyId()), taskUserId);
        return SingleResponse.of(flows);
    }

    // 结果确认流程 分步替换  listTaskProgress
    //@RequestMapping("emp/eval/getResultAffirmingFlow")
    public SingleResponse getResultAffirmingFlow(String taskUserId, String period) {
        if (!TalentStatus.RESULTS_AFFIRMING.match(period)) {
            return SingleResponse.buildFailure("009", "period参数错误");
        }
        DisplayEvalFlow flow = empEvalAppSvc.getResultAffirmingFlow(new TenantId(getCompanyId()), taskUserId);
        return SingleResponse.of(Arrays.asList(flow));
    }

    //校准审核流程
    //@RequestMapping("emp/eval/getAuditResultFlow")
    public SingleResponse getAuditResultFlow(String taskUserId) {
        EvaluateAuditSceneEnum sceneEnum = EvaluateAuditSceneEnum.FINAL_RESULT_AUDIT;
        DisplayEvalFlow flow = empEvalAppSvc.getFlow(new TenantId(getCompanyId()), taskUserId, TalentStatus.RESULTS_AUDITING, sceneEnum.getScene());
        return SingleResponse.of(Arrays.asList(flow));
    }

    //面谈流程
    //@RequestMapping("emp/eval/getInterviewResultFlow")
    public SingleResponse getInterviewResultFlow(String taskUserId) {
        List<DisplayEvalFlow> flows = empEvalAppSvc.getInterviewResultFlow(new TenantId(getCompanyId()), taskUserId);
        return SingleResponse.of(flows);
    }

    //替换queryMyTask
    @RequestMapping("emp/eval/pageMyEmpEval")
    public MultiResponse pageMyEmpEval(@RequestBody MyEmpEvalQuery query) {
        query.setEmpId(getEmpId());
        query.setCompanyId(new TenantId(getCompanyId()));
        PagedList<MyEvalTaskPo> taskPos = empEvalAppSvc.pageMyEmpEval(query);
        return MultiResponse.of(taskPos.getData(), taskPos.getPageNo(), taskPos.getTotalRow(), taskPos.getPageSize());
    }

    //替换queryResultAuditRecord queryEvalAuditRecord 结果校准记录
    @RequestMapping("emp/eval/listResultsAuditing")
    public ResponseInfo listResultAuditRecord(String taskUserId) {
        List<ResultAuditRecordPo> list = empEvalAppSvc.listResultAuditRecord(new TenantId(getCompanyId()), taskUserId);
        return ResponseInfo.success(list);
    }

    @RequestMapping("emp/eval/getHistoryScore")
    public SingleResponse getHistoryScore(String taskUserId) {
        ListWrap<HistoryScorePo> historyScore = calibratedAppSvc.listMapHistoryScore(new TenantId(getCompanyId()), Arrays.asList(taskUserId));
        HistoryScorePo historyScorePo = historyScore.mapGet(taskUserId);
        return SingleResponse.of(historyScorePo == null ? null : historyScorePo.getHistoryScore());
        //return ResponseInfo.success(empEvalAppSvc.getHistoryScore(new TenantId(getCompanyId()), taskUserId));
    }

    @RequestMapping("emp/eval/getFinalAuditResult")
    public ResponseInfo getFinalAuditResult(String taskUserId) {
        return ResponseInfo.success(empEvalAppSvc.getFinalAuditResult(new TenantId(getCompanyId()), taskUserId));
    }


    //员工任务开启的评分环节标记 scoreFlags替换queryScoreFlag
    @RequestMapping("emp/eval/scoreFlags")
    public ResponseInfo scoreFlags(String taskUserId) {
        EvalNodeFlagPo flags = empEvalAppSvc.scoreFlags(new TenantId(getCompanyId()), taskUserId);
        return ResponseInfo.success(flags);
    }

    //替换queryKpiItemDetail
    @RequestMapping({"emp/eval/getItemDetail", "eval/kpi/getItemDetail"})
    public ResponseInfo getItemDetail(@RequestBody KpiItemQuery kpiItemQuery) {
        kpiItemQuery.setCompanyId(getCompanyId());
        kpiItemQuery.setOpEmpId(getEmpId());
        EvalKpiPo itemDetail = empEvalAppSvc.getItemDetail(kpiItemQuery);
        return ResponseInfo.success(itemDetail);
    }

    //替换perf/task/queryItemScoreDetail 指标评分详情
    @RequestMapping("emp/eval/getItemScoreDetail")
    public ResponseInfo getItemScoreDetail(@RequestBody KpiItemQuery query) {
        query.setCompanyId(getCompanyId());
        EvalKpi kpi = empEvalAppSvc.getKpi(getCompanyId(), query.getTaskUserId(), query.getKpiItemId());
        if (kpi.noNeedScore()) {
            return ResponseInfo.success(new ItemScorePo(Boolean.TRUE));
        }
        if (kpi.isAutoItem()) {
            return ResponseInfo.success(new AutoItemScorePo(kpi));
        }
        //定向评分人
        if (kpi.hasDirectionalRater()) {
           // return ResponseInfo.success(empEvalAppSvc.getItemRaterScoreDetail(query));
            return ResponseInfo.success(empEvalAppSvc.getItemRaterScoreDetailV3(kpi,query));
        }
      //  List<ItemScorePo> itemScorePos = empEvalAppSvc.getItemScoreDetail(query);
        List<ItemScorePo> itemScorePos = empEvalAppSvc.getItemScoreDetailV3(kpi,query);
        return ResponseInfo.success(itemScorePos);
    }

    //直接确认指标.不编辑
    @RequestMapping("emp/eval/onlyConfirmItem")
    public SingleResponse onlyConfirmItem(@RequestBody OnlyConfirmtemCmd cmd) {
        log.info("直接确认.不编辑==param {}", JSON.toJSONString(cmd));
        cmd.accOp(new TenantId(getCompanyId()), new EmpId(getEmpId()));
//        empEvalAppSvc.directConfirmItem(cmd);
        stageAppSvc.onlyConfirmItem(cmd);
        return SingleResponse.buildSuccess();
    }

    @Deprecated
    //指标确认  带编辑与不编辑的
    @RequestMapping("emp/eval/confirmItem")
    public ResponseInfo confirmItem(@RequestBody ConfirmItemCmd cmd) {
        log.info("confirmItem==param {}", JSON.toJSONString(cmd));
        cmd.accOp(new TenantId(getCompanyId()), new EmpId(getEmpId()));
        empEvalAppSvc.confirmItem(cmd);
        return ResponseInfo.success("");
    }

    @RequestMapping("emp/eval/confirmEditItem")
    public SingleResponse confirmEditItem(@RequestBody ConfirmEditItemCmd cmd) {
        log.info("confirmEditItem.param {}", JSON.toJSONString(cmd));
        cmd.accOp(new TenantId(getCompanyId()), new EmpId(getEmpId()));
        stageAppSvc.confirmEditItem(cmd);
        return SingleResponse.of("");
    }

    //订正评分人及考核规则, 按考核表的内容重新生成
    @RequestMapping("fix/eval/fixEvalRule")
    public SingleResponse fixEvalRule(@RequestBody List<String> taskUserIds) {
        log.info("confirmEditItem.param {}", JSON.toJSONString(taskUserIds));
        stageAppSvc.fixEvalRule(new TenantId(getCompanyId()), getEmpId(), taskUserIds);
        return SingleResponse.of("");
    }


    //指标确认-->审核驳回 替换perf/task/rejectItemAudit
    @RequestMapping("emp/eval/rejectConfirmItem")
    public ResponseInfo rejectConfirmItem(@RequestParam String taskUserId, @RequestParam String rejectReason) {
        empEvalAppSvc.rejectConfirmItem(getCompanyId(), getEmpId(), taskUserId, rejectReason);
        return ResponseInfo.success("");
    }

    //执行阶段变更指标 代替 submitChangeItem
    @RequestMapping("emp/eval/submitChangeItem")
    public ResponseInfo changeItemOfConfirmed(@RequestBody ChangeItemCmd cmd) {
        log.info("submitChangeItem==param：{}", JSONObject.toJSONString(cmd));
        cmd.accOp(new TenantId(getCompanyId()), new EmpId(getEmpId()));
        empEvalAppSvc.submitChangeItem(cmd);
        return ResponseInfo.success("");
    }

    //指标变更-->执行阶段-->驳回  替换perf/task/auditChangeItem
    @RequestMapping("emp/eval/rejectChangeItem")
    public ResponseInfo rejectChangeItem(@RequestParam String taskUserId, @RequestParam String reason) {
        empEvalAppSvc.rejectChangeItem(getCompanyId(), getEmpId(), taskUserId, reason);
        return ResponseInfo.success("");
    }

    //指标变更-->执行阶段-->驳回  替换perf/task/auditChangeItem
    @RequestMapping("emp/eval/passChangeItem")
    public ResponseInfo passChangeItem(@RequestParam String taskUserId, @RequestParam String reason) {
        empEvalAppSvc.passChangeItem(getCompanyId(), new EmpId(getEmpId()), taskUserId, reason);
        return ResponseInfo.success("");
    }

    @RequestMapping("emp/eval/finishedValueAudit")
    public ResponseInfo finishedValueAudit(@RequestBody FinishedValueAuditCmd cmd) {
        log.info("finishedValueAudit==param {}", JSONObject.toJSONString(cmd));
        cmd.setTenantId(new TenantId(getCompanyId()));
        cmd.setOpEmpId(new EmpId(getEmpId()));
        cmd.setFinishedValueAuditEmpId(new EmpId(getEmpId()));
        cmd.markKpiSubmitFinishValue();
        empEvalAppSvc.finishedValueAudit(cmd);
        return ResponseInfo.success("");
    }

    /**
     * 完成值驳回
     *
     * @param cmd
     * @return
     */
    @RequestMapping("emp/eval/rejectFinishedValue")
    public ResponseInfo rejectFinishedValue(@RequestBody RejectFinishedValueCmd cmd) {
        cmd.setTenantId(new TenantId(getCompanyId()));
        cmd.setOpEmpId(new EmpId(getEmpId()));
        if (CollUtil.isEmpty(cmd.getAuditItemIds())) {
            log.error("auditItemIds 必传 {}", JSONUtil.toJsonStr(cmd));
            return ResponseInfo.success("auditItemIds 必传");
        }
        empEvalAppSvc.rejectFinishedValue(cmd);
        return ResponseInfo.success("");
    }

    //结果校准 替换perf/task/auditResultScore
    @RequestMapping("emp/eval/resultAudit")
    public ResponseInfo resultAudit(@RequestBody ResultAuditCmd cmd) {
        cmd.accOp(new TenantId(getCompanyId()), new EmpId(getEmpId()));
        log.info("resultAudit入参：{}", JSONObject.toJSONString(cmd));
        empEvalAppSvc.doResultAudit(cmd);
        return ResponseInfo.success("");
    }

    //批量校准 替代perf/task/batchAuditResultScore
    @RequestMapping("emp/eval/batchResultAudit")
    public ResponseInfo batchResultAudit(@RequestBody List<ResultAuditCmd> cmds) {
        cmds.forEach(cmd -> {
            cmd.accOp(new TenantId(getCompanyId()), new EmpId(getEmpId()));
        });
        //final String tid = MDC.get("tid");
        empEvalAppSvc.batchResultAudit(cmds);
        return ResponseInfo.success("");
    }

    //绩效结果确认 替换 perf/task/resultAffirm
    @RequestMapping({"perf/task/resultAffirm", "emp/eval/resultAffirm"})
    public ResponseInfo resultAffirm(String taskUserId, String signPicUrl) {
        empEvalAppSvc.doAffirmResult(new TenantId(getCompanyId()), getEmpId(), taskUserId, false, signPicUrl);
        return ResponseInfo.success("");
    }

    //旧标是否公示 替换 perf/task/publicationTaskResult
    @RequestMapping({"perf/task/publicationTaskResult"})
    public ResponseInfo markPublish(String taskUserStr, @RequestParam(defaultValue = "true") Boolean isPublish) {
        MarkPublishCmd cmd = new MarkPublishCmd(taskUserStr, isPublish);
        return markPublish(cmd);
    }

    //公示操作,标记是否公示 替换 perf/task/publicationTaskResult
    @RequestMapping("emp/eval/markPublish")
    public ResponseInfo markPublish(@RequestBody MarkPublishCmd cmd) {
        cmd.accOp(getCompanyId(), getEmpId(), getAdminType());
        if (cmd.isPublish()) {
            empEvalAppSvc.markPublish(cmd);
        } else {
            empEvalAppSvc.markNonePublish(cmd);
        }
        return ResponseInfo.success("");
    }

    // 测试接口,验证员工合并后结果
    @NoneLogin
    @RequestMapping("fix/eval/test/getEmpEval")
    public SingleResponse empEval(String companyId, String taskUserId) {
        EmpEvalMerge rs = empEvalAppSvc.getEmpEval(new TenantId(companyId), taskUserId);
        ChainNode chainNode1 = rs.getScoreChain().getChain().get(1);
        ChainNode chainNode = rs.skipFinishedChainNode(chainNode1);
        log.info("skipFinishedChainNode:{}", JSONObject.toJSONString(chainNode));
        return SingleResponse.of(rs);
    }

    // 替换 queryTaskDetailByEmp ,进入评分页面使用 scoreNode 新加参数, 查指定的scoreNode 已使用empEvalForScoreV2
    //@Deprecated
    //@RequestMapping("emp/eval/empEvalForScore")
    //public SingleResponse empEvalForScore(String talentEvalId, String scoreNode) {
    //    EmpEvalForScorePo rs = empEvalAppSvc.empEvalForScore(new TenantId(getCompanyId()), talentEvalId, getEmpId());
    //    if (StrUtil.isNotBlank(scoreNode)) {//重新评分指定的环节
    //        rs.filterScoreNode(scoreNode);
    //    } else {//仅返回未评分的环节
    //        rs.filterWaitScoreNode();
    //    }
    //    return SingleResponse.of(rs);
    //}

    // 指标确认变更回填给考核人.
    @Deprecated
    @RequestMapping("emp/eval/empEvalForConfirmOld")
    public SingleResponse empEvalForConfirmOld(String taskUserId) {
        EmpEvalFoConfirmPo rs = empEvalAppSvc.empEvalForConfirm(new TenantId(getCompanyId()), taskUserId, getEmpId());
        return SingleResponse.of(rs);
    }

    @RequestMapping("emp/eval/empEvalForConfirm")
    public SingleResponse empEvalForConfirm(String taskUserId) {
        //  EmpEvalFoConfirmPo rs = empEvalAppSvc.empEvalForConfirm(new TenantId(getCompanyId()), taskUserId, getEmpId());
        ConfirmEmpEvalPo rs = stageAppSvc.empEvalForConfirm(new TenantId(getCompanyId()), taskUserId, getEmpId());
        log.info("stageAppSvc.empEvalForConfirm:{}", JSONObject.toJSONString(rs));
        return SingleResponse.of(rs);
    }

    // 查询指标确认当前处理人节点
    @Deprecated
    @RequestMapping("emp/eval/getConfirmFlowNodeOrder")
    public SingleResponse getConfirmFlowNodeOrder(String taskUserId) {
        Integer rs = empEvalAppSvc.getConfirmFlowNodeOrder(new TenantId(getCompanyId()), taskUserId, getEmpId());
        return SingleResponse.of(rs);
    }

    //考核面谈
    @RequestMapping("emp/eval/pagedEvalInterview")
    private MultiResponse pagedEvalInterview(@RequestBody EvalInterviewQuery query) {
        query.setCompanyId(getCompanyId());
        query.setOpEmpId(getEmpId());
        PagedList<EvalInterviewPo> pos = empEvalAppSvc.pagedEvalInterview(query);
        return MultiResponse.of(pos.getData(), pos.getPageNo(), pos.getTotalRow(), pos.getPageSize());
    }

    // 替换 queryTodoTaskUserByScoreId ,我协同的员工任务=我参与的
    @RequestMapping({"perf/task/queryTodoTaskUserByScoreId", "emp/eval/listMyCotaskingEval"})
    public SingleResponse queryTodoTaskUserByScoreId(@RequestParam String taskId) {
        List<PerfEvaluateTaskUserDo> cotasks = empEvalAppSvc.listMyCotaskingEval(getCompanyId(), taskId, getEmpId());
        return SingleResponse.of(cotasks);
    }

    // 替换 queryTodoTaskUserByScoreId ,我协同的员工任务=我参与的
    @RequestMapping({"perf/task/queryMyFinishedScoreType", "emp/eval/listReScoringType"})
    public SingleResponse listReScoringType(@RequestParam String talentEvalId) {
        //Set<String> types = empEvalAppSvc.listReScoringType(getCompanyId(), talentEvalId, getEmpId());
        Set<String> types = empEvalAppSvc.listReScoringTypeV3(getCompanyId(), talentEvalId, getEmpId());
        return SingleResponse.of(types);
    }


    // region 员工考核面谈，评论

    // 保存考核面谈 迁移：EvaluateTaskController::873 saveTaskCoach
    @RequestMapping(value = {"perf/task/saveTaskCoach", "emp/eval/saveTaskCoach"})
    public ResponseInfo saveTaskCoach(PerfEvaluateTaskCoach taskCoach, Boolean isSendNotice) {
        String opEmpId = getEmpId();
        String id = this.empEvalAppSvc.addTaskCoach(new TenantId(getCompanyId()), taskCoach, opEmpId, isSendNotice);
        return ResponseInfo.success(id);
    }

    // 考核面谈已读 迁移：EvaluateTaskController::878 readTaskCoach
    @RequestMapping(value = {"perf/task/redTaskCoach", "emp/eval/redTaskCoach"})
    public ResponseInfo readTaskCoach(String id) {
        TenantId tenantId = new TenantId(getCompanyId());
        this.empEvalAppSvc.readTaskCoach(tenantId, id, getEmpId());
        return ResponseInfo.success("");
    }

    //评论已读
    @RequestMapping(value = "perf/task/readTaskDiscuss")
    public ResponseInfo readTaskDiscuss(String taskUserId, String kpiItemId) {
        return ResponseInfo.success(empEvalAppSvc.readTaskDiscuss(getCompanyId(), taskUserId, kpiItemId));
    }

    // 查询面谈辅导记录列表 迁移：EvaluateTaskController::894 queryTaskCoachList
    @RequestMapping(value = {"perf/task/queryTaskCoachList", "emp/eval/queryTaskCoachList"})
    public ResponseInfo queryTaskCoachList(EmpEvalCoachQuery query) {
        query.setCompanyId(getCompanyId());
        List<PerfEvaluateTaskCoach> perfEvaluateTaskCoaches = this.empEvalAppSvc.listTaskCoach(query);
        return ResponseInfo.success(perfEvaluateTaskCoaches);
    }

    // 面谈辅导发通知 迁移：EvaluateTaskController::901 queryTaskCoachList
    @RequestMapping(value = {"perf/task/taskCoachNotice", "emp/eval/taskCoachNotice"})
    public ResponseInfo taskCoachNotice(String taskUserId, String coachId) {
        TenantId tenantId = new TenantId(getCompanyId());
        this.empEvalAppSvc.taskCoachNotice(tenantId, taskUserId, coachId);
        return ResponseInfo.success("");
    }

    //面谈辅导统计 迁移：EvaluateTaskController::911 queryTaskCoachList
    @RequestMapping(value = {"perf/task/coachCnt", "emp/eval/coachCnt"})
    public ResponseInfo coachCnt(@RequestBody EmpEvalCoachQuery query) {
        query.queriedBy(getCompanyId(), getEmpId());
        TotalCoachCntPo totalCoachCntPo = this.empEvalAppSvc.coachCnt(query);
        return ResponseInfo.success(totalCoachCntPo);
    }

    //添加评论 迁移：EvaluateTaskController::925 queryTaskCoachList
    @RequestMapping(value = {"perf/task/addTaskDiscuss", "emp/eval/addTaskDiscuss"})
    public ResponseInfo addTaskDiscuss(PerfEvaluateTaskDiscuss discuss) {
        discuss.asMainType();
        String id = this.empEvalAppSvc.saveTaskDiscuss(new TenantId(getCompanyId()), getEmpId(), discuss);
        return ResponseInfo.success(id);
    }

    //添加评论回复 迁移：EvaluateTaskController::939 replyTaskDiscuss
    @RequestMapping(value = {"perf/task/replyTaskDiscuss", "emp/eval/replyTaskDiscuss"})
    public ResponseInfo replyTaskDiscuss(PerfEvaluateTaskDiscuss discuss) {
        discuss.asReplyType();
        String id = this.empEvalAppSvc.saveTaskDiscuss(new TenantId(getCompanyId()), getEmpId(), discuss);
        return ResponseInfo.success(id);
    }

    //查询所有评论，携带了回复的 迁移：EvaluateTaskController::queryTaskDiscussList::953
    @RequestMapping(value = {"perf/task/queryTaskDiscussList", "emp/eval/queryTaskDiscussList"})
    public ResponseInfo queryTaskDiscussList(EmpEvalDiscussQuery query) {
        query.setCompanyId(getCompanyId());
        List<TaskDiscussPo> discuss = this.empEvalAppSvc.listTaskDiscuss(query);
        return ResponseInfo.success(discuss);
    }

    //查询所有评论，携带了回复的 迁移：EvaluateTaskController::queryTaskLog::990
    @RequestMapping(value = {"perf/task/queryTaskLog", "emp/eval/queryTaskLog"})
    public ResponseInfo queryTaskLog(String taskUserId) {
        List<OperationLogPo> logPos = this.empEvalAppSvc.listTaskLog(new TenantId(getCompanyId()), taskUserId, getAdminType());
        return ResponseInfo.success(logPos);
    }

    // 替换 taskChangeJSON没有值，执行中审核变更
    @RequestMapping("emp/eval/listAuditCachedKpi")
    public SingleResponse listAuditCachedKpi(String taskUserId) {
        //兼容1.0的缓存场景
        List<EmpChangeItemStagePo> rs = empEvalAppSvc.listAuditCachedKpi(new TenantId(getCompanyId()), taskUserId,
                Arrays.asList(AuditEnum.EDIT_EXE_INDI.getScene(), OperationLogSceneEnum.CHANGE_ITEM.getScene()));
        if (rs != null && rs.size() > 0) {
            for (EmpChangeItemStagePo r : rs) {
                if (r.getKrTree() != null && r.getKrTree().size() > 0) {
                    for (EmpEvalKpiRefTargetPo empEvalKpiRefTargetPo : r.getKrTree()) {
                        empEvalAppSvc.buildOkr(new TenantId(getCompanyId()), empEvalKpiRefTargetPo.getKrList(), empEvalKpiRefTargetPo.getKrList().stream().filter(obj -> Boolean.valueOf(obj.getOkrRefFlag())).collect(Collectors.toList()));
                    }
                }
            }
        }
        return SingleResponse.of(rs);
    }

    //转交 EvaluateTaskUserController::transfer::85
    @RequestMapping({"emp/eval/transfer", "perf/task/transfer"})
    public ResponseInfo transferTask(TaskTransferCmd cmd) {
        cmd.requiredCheck();
        cmd.setOperator(getCompanyId(), getEmpId(), getAdminType());
        //if (empEvalAppSvc.isTransferInputEmp(cmd)) {
        //    empEvalAppSvc.transferInputEmp(cmd);
        //    return ResponseInfo.success("");
        //}
        this.empEvalAppSvc.transferTask(cmd);
        return ResponseInfo.success("");
    }


    //转交 transferTask 抽出 评分环节的转交
    @RequestMapping({"emp/eval/transferScoreTask", "perf/task/transferScoreTask"})
    public ResponseInfo transferScoreTask(@RequestBody TaskTransferCmd cmd) {
        cmd.requiredCheck();
        cmd.verifyEmp();//如果没有转出的责任人，说明是责任人自己在操作，取当前操作人
        cmd.setOperator(getCompanyId(), getEmpId(), getAdminType());
        this.empEvalAppSvc.transferScoreTaskV3(cmd);
        return ResponseInfo.success("");
    }


    //批量转交 EvaluateTaskUserController::batchTransfer::171
    //@RequestMapping({"emp/eval/batchTransfer", "perf/task/batchTransfer"})
    //暂时先用老接口，后续重构
    public ResponseInfo batchTransfer(TaskTransferCmd cmd) {
        cmd.requiredCheck();
        cmd.setOperator(getCompanyId(), getEmpId(), getAdminType());
        this.empEvalAppSvc.batchTransferTask(cmd);
        return ResponseInfo.success("");
    }

    //查批量转交列表 EvaluateTaskUserController::queryBatchTransferList::133
    @RequestMapping({"emp/eval/queryBatchTransferList", "perf/task/queryBatchTransferList"})
    public MultiResponse queryBatchTransferList(TransferQuery query) {
        query.setCompanyId(getCompanyId());
        PagedList<TransferPo> pos = this.empEvalAppSvc.pageTransfer(query);
        return MultiResponse.of(pos.getData(), pos.getPageNo(), pos.getTotalRow(), pos.getPageSize());
    }

    //查询某个员工批量转交列表 EvaluateTaskUserController::queryBatchTransferList::143
    @RequestMapping({"emp/eval/queryBatchTransferByEmp", "perf/task/queryBatchTransferByEmp"})
    public ResponseInfo queryBatchTransferByEmp(String scorerId) {
        List<TransferPo> transferPos = this.empEvalAppSvc.listTransfer(new TenantId(getCompanyId()), scorerId);
        return ResponseInfo.success(transferPos);
    }

    //催办 EvaluateTaskUserController::queryBatchTransferList::98
    @RequestMapping({"emp/eval/urging", "perf/task/urging"})
    public ResponseInfo urgingTask(String taskUserStr) {
        UrgingTaskCmd cmd = new UrgingTaskCmd(getCompanyId(), taskUserStr);
        cmd.setOpAdminType(getAdminType());
        cmd.setOpEmpId(getEmpId());
        String tid = MDC.get("tid");
        transactionWrap.runAsyn(tid, () -> this.empEvalAppSvc.urgingTaskV2(cmd, tid));
        return ResponseInfo.success("success");
    }

    @RequestMapping("emp/eval/urgingAll")
    public ResponseInfo urgingTask(@RequestParam String cycleId, @RequestParam String taskId) {
        UrgingTaskCmd cmd = new UrgingTaskCmd(getCompanyId(), getEmpId(), getAdminType(), null);
        cmd.setTaskId(taskId);
        cmd.setCycleId(cycleId);
        cmd.setAll(true);
        String tid = MDC.get("tid");
        transactionWrap.runAsyn(tid, () -> this.empEvalAppSvc.urgingTaskV2(cmd, tid));
        return ResponseInfo.success("success");
    }

    //对指标完成值录入人发送待办 EvaluateTaskUserController::urgingFinishValue::194
    @RequestMapping({"emp/eval/urgingFinishValue", "perf/task/urgingFinishValue"})
    public ResponseInfo urgingFinishValue(@RequestBody UrgingFinishValueCmd cmd) {
        cmd.requiredCheck();
        cmd.setTenantId(new TenantId(getCompanyId()));
        this.empEvalAppSvc.urgingFinishValue(cmd);
        return ResponseInfo.success("");
    }

    //催办工作事项录入 EvaluateTaskUserController::sendInputWorkItemUrging::408
    @RequestMapping({"emp/eval/sendInputWorkItemUrging", "perf/task/sendInputWorkItemUrging"})
    public ResponseInfo sendInputWorkItemUrging(String taskId, String taskUserId) {
        this.empEvalAppSvc.sendInputWorkItemUrging(new TenantId(getCompanyId()), taskId, taskUserId, getEmpId());
        return ResponseInfo.success("");
    }

    //发送设置互评责任人的催办 EvaluateTaskUserController::urgingSetMutualScoreEmp::358
    @RequestMapping({"emp/eval/sendSetMutualAuditUrging", "perf/task/sendSetMutualAuditUrging"})
    public ResponseInfo urgingSetMutualScoreEmp(@RequestBody List<EvalUser> taskUsers) {
        String tid = MDC.get("tid");
        this.empEvalAppSvc.urgingSetMutualScoreEmp(new TenantId(getCompanyId()), taskUsers, tid);
        return ResponseInfo.success("");
    }

    @RequestMapping({"emp/eval/submitResultAppeal", "perf/task/submitAppealInfo"})
    public ResponseInfo submitResultAppeal(@RequestBody SubmitResultAppealCmd cmd) {
        cmd.setCompanyId(new TenantId(getCompanyId()));
        cmd.setOpEmpId(getEmpId());
        empEvalAppSvc.submitResultAppeal(cmd);
        return ResponseInfo.success("");
    }

    @RequestMapping({"emp/eval/auditResultAppeal", "perf/task/auditAppeal"})
    public ResponseInfo auditResultAppeal(@RequestBody AuditResultAppealCmd cmd) {
        cmd.setCompanyId(new TenantId(getCompanyId()));
        cmd.setOpEmpId(getEmpId());
        EvalScoreChanged evalScoreChanged = empEvalAppSvc.auditResultAppeal(cmd);
        evalScoreChanged.publish();
        return ResponseInfo.success("");
    }

    @RequestMapping({"emp/eval/cancelResultAppeal", "perf/task/cancelAppeal"})
    public ResponseInfo cancelResultAppeal(@RequestParam String appealBatchId) {
        empEvalAppSvc.cancelResultAppeal(getCompanyId(), getEmpId(), appealBatchId);
        return ResponseInfo.success("");
    }

    @RequestMapping({"emp/eval/transferAppeal", "perf/task/transferAppeal"})
    public ResponseInfo transferAppeal(@RequestBody TransferAppealCmd cmd) {
        cmd.setCompanyId(new TenantId(getCompanyId()));
        cmd.setOpEmpIdIfNull(getEmpId());
        empEvalAppSvc.transferAppeal(cmd);
        return ResponseInfo.success("");
    }

    @RequestMapping({"emp/eval/getOngoingAppealBatch"})
    public ResponseInfo getOngoingAppealBatch(@RequestParam String taskUserId) {
        return ResponseInfo.success(empEvalAppSvc.getOngoingAppealBatch(getCompanyId(), taskUserId));
    }

    @RequestMapping({"emp/eval/listTaskAppealRecord", "perf/task/queryTaskAppealBatchInfos"})
    public ResponseInfo listTaskAppealRecord(@RequestBody TaskAppealRecordQuery query) {
        query.setCompanyId(getCompanyId());
        return ResponseInfo.success(empEvalAppSvc.listTaskAppealRecord(query));
    }

    @RequestMapping({"emp/eval/readAppeal", "perf/task/readAppeal"})
    public ResponseInfo readAppeal(@RequestParam String taskUserId) {
        empEvalAppSvc.readAppeal(getCompanyId(), taskUserId);
        return ResponseInfo.success("");
    }

    //手动发起评分时判断是否有自动评分的完成值未录入 EvaluateTaskUserController::queryNoFinishValueTask::183
    @RequestMapping({"emp/eval/queryNoFinishValueTask", "perf/task/queryNoFinishValueTask"})
    public ResponseInfo listNotFinishValueTask(String taskUserStr) {
        InnerFields<String> fields = new InnerFields<>();
        fields.appendStr(taskUserStr, ",");
        if (fields.isEmpty()) {
            return ResponseInfo.success("没有指定考核任务");
        }
        List<EmpEvalIfvPo> empEvalIfvPos = this.empEvalAppSvc.listNotFinishTask(new TenantId(getCompanyId()), fields.getFields());
        return ResponseInfo.success(empEvalIfvPos);
    }

    //调整绩效结果 EvaluateTaskUserController::adjustTaskUserResult::183
    @RequestMapping({"emp/eval/adjustTaskUserResult", "perf/task/adjustTaskUserResult"})
    public ResponseInfo adjustTaskUserResult(@RequestBody ResultAuditCmd cmd) { // taskUserId是EvalUser的id
        cmd.occOp(getCompanyId(), getEmpId());
        EvalScoreChanged evalScoreChanged = this.empEvalAppSvc.adjustTaskUserResult(cmd);
        evalScoreChanged.publish();
        return ResponseInfo.success("");
    }

    @RequestMapping("emp/eval/inviteMutualEmp")
    public ResponseInfo inviteMutualEmp(@RequestBody InviteMutualCmd cmd) {
        log.debug("InviteMutualCmd:{}", JSONUtil.toJsonStr(cmd));
        cmd.buildOp(getCompanyId(), getEmpId());
        KpiItemUpdateFinishedValueEvent event = this.empEvalAppSvc.inviteMutualEmp(cmd);
        if (Objects.nonNull(event)) {
            event.fire();
        }
        return ResponseInfo.success("");
    }

    @RequestMapping("emp/eval/inviteMutualEmpAudit")
    public ResponseInfo inviteMutualEmpAudit(@RequestBody InviteMutualCmd cmd) {
        cmd.buildOp(getCompanyId(), getEmpId());
        KpiItemUpdateFinishedValueEvent event = this.empEvalAppSvc.inviteMutualEmpAudit(cmd);
        if (Objects.nonNull(event)) {
            event.fire();
        }
        return ResponseInfo.success("");
    }

    //替换 saveTaskRefKR
    @RequestMapping("perf/task/saveTaskRefKR")
    public ResponseInfo saveTaskRefKR(AddOKRCmd addOKRCmd) {
        addOKRCmd.setOperator(getCompanyId(), getEmpId(), getAdminType());
        EvalUser user = taskAppSvc.addOKRItem(addOKRCmd);
        if (user.getTaskStatus().equals(TalentStatus.CONFIRMED.getStatus())) {
            new KpiItemUpdateFinishedValueEvent(user.getCompanyId(), user, new EmpId(getEmpId())).fire();
        }
        return ResponseInfo.success("");
    }

    //@RequestMapping({"perf/task/queryRefKRList", "perf/task/saveTaskRefKR"})
    @RequestMapping({"perf/task/queryRefKRList", "emp/eval/listOkrKRs"})
    public ResponseInfo listOkrKRs(String taskUserId, String kpiTypeId) {
        return ResponseInfo.success(taskAppSvc.listOkrKRs(new TenantId(getCompanyId()), taskUserId, kpiTypeId));
    }

    //替换 queryMyHandleTask
    @RequestMapping("perf/task/queryMyHandleTask")
    public MultiResponse pagedMyHandleTask(MyHandleTaskQuery taskQuery) {
        taskQuery.setCompanyId(getCompanyId());
        taskQuery.setCreatedUser(getEmpId());
        PagedList<MyTaskUserPo> pos = taskAppSvc.pagedMyHandleTask(taskQuery);
        return MultiResponse.of(pos.getData(), pos.getPageNo(), pos.getTotalRow(), pos.getPageSize());
    }

    //我管理的 最新的周期
    @RequestMapping("perf/task/getINewestCycle")
    public ResponseInfo getINewestCycle() {
        MyManagerEmpEvalQuery query = new MyManagerEmpEvalQuery();
        query.accOp(getCompanyId(), getEmpId());
        query.setPerformanceType(1);
        return ResponseInfo.success(empEvalAppSvc.getINewestCycle(query));
    }

    //我管理的
    @RequestMapping("perf/task/pagedIManageEmpEval")
    public MultiResponse pagedIManageEmpEval(@RequestBody MyManagerEmpEvalQuery empEvalQuery) {
        empEvalQuery.accOp(getCompanyId(), getEmpId());
        PagedList<MyManagerEmpEval> pos = empEvalAppSvc.pagedIManageEmpEval(empEvalQuery);
        return MultiResponse.of(pos.getData(), pos.getPageNo(), pos.getTotalRow(), pos.getPageSize());
    }

    @RequestMapping("perf/task/getIManageEmpEvalStatusCnt")
    public SingleResponse evalStatusCnt(@RequestBody MyManagerEmpEvalQuery empEvalQuery) {
        empEvalQuery.accOp(getCompanyId(), getEmpId());
        AdminTaskStatusCnt cnt = empEvalAppSvc.getIManageEmpEvalStatusCnt(empEvalQuery);
        return SingleResponse.of(cnt);
    }

    @NoneLogin
    @RequestMapping("open/card/cardData")
    public CardDataReps cardData(@RequestBody CardDataReq query) {
        CardDataReps cardData = taskAppSvc.cardData(query);
        return cardData;
    }


    @RequestMapping("perf/task/listOkr")
    public ResponseInfo listOkr(@RequestBody OkrQuery query) {
        query.setCompanyId(getCompanyId());
        List<OkrTask> okrTasks = taskAppSvc.listOkr(query);
//        List<OkrTarget> okrTargets = taskAppSvc.listOkr(query);
        return ResponseInfo.success(okrTasks);
    }

    @RequestMapping("emp/eval/getLatestChangeItemLog")
    public SingleResponse getLatestChangeItemLog(String taskUserId) {
        OperationLogDo log = empEvalAppSvc.getLatestChangeItemLog(getCompanyId(), taskUserId);
        return SingleResponse.of(log);
    }

    /**
     * 处理历史记录的task上的appeal_conf 和eval_rule上的appeal_conf
     *
     * @param taskId
     * @return
     **/
//    @NoneLogin
//    @RequestMapping("fix/task/fixAppealHistoryData")
//    public SingleResponse fixAppealHistoryData(String taskId, String beginDate,String endDate) {
//        empEvalAppSvc.fixAppealHistoryData(taskId, beginDate, endDate);
//        return SingleResponse.buildSuccess();
//    }

//    /**
//     * 处理历史记录的task上的appeal_conf 和eval_rule上的appeal_conf
//     * @param beginDate,endDate
//     * @return
//     */
//    @NoneLogin
//    @RequestMapping("fix/task/fixAppealHistoryDataWithUpdateTime")
//    public SingleResponse fixAppealHistoryDataWithUpdateTime(String beginDate,String endDate) {
//        empEvalAppSvc.fixAppealHistoryDataWithUpdateTime(beginDate, endDate);
//        return SingleResponse.buildSuccess();
//    }
//
//    /**
//     *
//     * @param
//     * @return
//     */
//    @RequestMapping("fix/task/fixAppealOngoingAppealBatch")
//    public SingleResponse fixAppealOngoingAppealBatch() {
//        empEvalAppSvc.fixAppealOngoingAppealBatch();
//        return SingleResponse.buildSuccess();
//    }
    public static void main(String[] args) {
        System.out.println(System.currentTimeMillis());
    }
}
