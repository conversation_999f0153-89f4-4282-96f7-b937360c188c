package com.polaris.kpi.controller.eval.task;

import cn.com.polaris.kpi.company.MsgSceneEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.perf.www.common.annotation.NoneLogin;
import com.perf.www.common.web.ResponseInfo;
import com.polaris.kpi.eval.app.task.appsvc.EmpEvalJobAppSvc;
import com.polaris.kpi.eval.app.task.appsvc.EvalTaskAppSvc;
import com.polaris.kpi.eval.infr.task.dao.AdminTaskDao;
import lombok.extern.slf4j.Slf4j;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

@Slf4j
@RestController
public class AutoInputNotifyJobController {
    @Autowired
    private AdminTaskDao adminTaskDao;
    @Autowired
    private EmpEvalJobAppSvc jobAppSvc;

    //先组装好录入完成值通知的消息发送实体(全量五点执行一次)
    @RequestMapping("eval/auto/inputNotifyMsgTodoInfo")
    @Scheduled(cron = "0 0 5 * * ?")
    public SingleResponse inputNotifyMsgTodoInfo() {
        String tid = IdUtil.fastSimpleUUID();
        MDC.put("tid", tid);
        log.info("组装完成值录入通知开始！");
        String nowDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        List<String> companyIds = adminTaskDao.listInputNotifyCompanyIds(nowDate);
        if (CollUtil.isEmpty(companyIds)) {
            log.info("没有使用中的公司");
            return SingleResponse.of(companyIds.size());
        }
        for (String id : companyIds) {
            try {
                MDC.put("tid", tid + "_" + id);
                jobAppSvc.lockCompanyForDayJob(id, "inputNotifyMsgTodoInfo");
                log.info("inputNotifyMsgTodoInfo_companyId={}", id);
                jobAppSvc.builderSendInputNotify(id, nowDate);
            } catch (DataIntegrityViolationException e) {
                log.error("组装录入通知消息发送信息冲突,公司:" + id, e);
            } catch (Exception e) {
                log.error("组装录入通知消息发送信息出错,公司:{}, error:{}", id, e.getMessage(), e);
            }
        }
        MDC.clear();
        return SingleResponse.buildSuccess();
    }


    //发送录入完成值通知(每天七点四十五分执行)
    @RequestMapping("eval/auto/sendInputNotifyMsg")
    @Scheduled(cron = "0 45 7 ? * *")
    //@Scheduled(cron = "0 0 12 * * ?")
    public SingleResponse sendInputNotifyMsg() {
        MDC.put("tid", "sendInputNotifyMsg" + IdUtil.fastSimpleUUID());
        log.info("发送完成值录入通知开始！");
        List<String> companyIds = adminTaskDao.listMsgTodoCompanyIds(MsgSceneEnum.TASK_SUBMIT_PROGRESS.getType());
        if (CollUtil.isEmpty(companyIds)) {
            log.info("没有使用中的公司");
            return SingleResponse.of(companyIds.size());
        }
        String tid = MDC.get("tid");
        for (String id : companyIds) {
            try {
                MDC.put("tid", tid + "_" + id);
                String uniKey = jobAppSvc.lockCompanyForDayJob(id, "sendInputNotifyMsg");
                log.info("sendInputNotifyMsg_companyId={}", id);
                jobAppSvc.sendMsgTodo(id, MsgSceneEnum.TASK_SUBMIT_PROGRESS.getType());
            } catch (DataIntegrityViolationException e) {
                log.error("发送录入通知消息发送信息冲突,公司:{}", id);
            } catch (Exception e) {
                log.error("发送录入通知消息发送信息出错,公司:{}, error:{}", id, e.getMessage(), e);
            }
        }
        MDC.clear();
        return SingleResponse.buildSuccess();
    }


    /**
     * 重新发送完成值待办通知
     *
     * @return
     */
    @RequestMapping("fix/task/reSendInputNotifyMsg")
    @NoneLogin
    public ResponseInfo reSendInputNotifyMsg(String scene, String taskUserIds) {
        if (StrUtil.isNotBlank(taskUserIds)) {
            MsgSceneEnum msgSceneEnum = MsgSceneEnum.queryType(scene);
            jobAppSvc.reSendMsgTodo(msgSceneEnum.getType(), StrUtil.splitTrim(taskUserIds, ","));
        }
        return ResponseInfo.success(null);
    }
}
