package com.polaris.kpi.controller.eval.task.emp;

import cn.com.polaris.kpi.eval.EndScoreErrInfo;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.perf.www.common.web.AccountBaseController;
import com.perf.www.common.web.ResponseInfo;
import com.polaris.kpi.common.KpiI18NException;
import com.polaris.kpi.eval.app.conf.appsvc.EmpKpiConfSvc;
import com.polaris.kpi.eval.app.cycle.appsvc.CycleEvalAppSvc;
import com.polaris.kpi.eval.app.task.appsvc.EmpEvalAppSvc;
import com.polaris.kpi.eval.app.task.appsvc.EvalTaskAppSvc;
import com.polaris.kpi.eval.app.task.appsvc.ScoreStageAppSvc;
import com.polaris.kpi.eval.app.task.appsvc.SubmitScoreAppSvc;
import com.polaris.kpi.eval.app.task.dto.EnterScoringCmd;
import com.polaris.kpi.eval.app.task.dto.sumit.*;
import com.polaris.kpi.eval.app.task.result.EnterScoringResult;
import com.polaris.kpi.eval.app.todo.appsvc.WorkTodoAppSvc;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.empeval.vealResult.BatchSubmitScoreResult;
import com.polaris.kpi.eval.domain.task.repo.TaskUserRepo;
import com.polaris.kpi.eval.infr.task.ppojo.EvalMultistageSupScorePo;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.EmpEvalTaskScore3Po;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.EvalScorerNodeScorePo;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.EvalScorerTypeScoresPo;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.EmpEvalForScoreV3Po;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.EnbledSaveScorePo;
import com.polaris.kpi.eval.infr.task.ppojo.worktodo.SimpleEvalEmpPo;
import com.polaris.kpi.eval.infr.task.query.worktodo.ScoreWorkTodoQuery;
import com.polaris.kpi.org.type.TraceKey;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.lufei.ibatis.mapper.PagedList;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@RestController
public class SubmitScoreController extends AccountBaseController {
    @Autowired
    private CycleEvalAppSvc cycleEvalAppSvc;
    @Autowired
    private SubmitScoreAppSvc submitScoreAppSvc;
    @Autowired
    private ScoreStageAppSvc scoreStageAppSvc;
    @Autowired
    private EvalTaskAppSvc taskAppSvc;
    @Autowired
    private TaskUserRepo userRepo;
    @Autowired
    private WorkTodoAppSvc workTodoAppSvc;
    @Autowired
    private EmpEvalAppSvc empEvalAppSvc;
    @Autowired
    private EmpKpiConfSvc empKpiConfSvc;

    //批量手动进入评分期 替换manualEnterScoring
    @RequestMapping("eval/task/batchEnterScoring")
    public SingleResponse batchEnterScoring(@RequestBody EnterScoringCmd cmd) {
        log.info("batchEnterScoring入参：{}", JSONUtil.toJsonPrettyStr(cmd));
        cmd.setOpEmpId(new TenantId(getCompanyId()), new EmpId(getEmpId()));
        List<EnterScoringResult> results = taskAppSvc.finishValueAuditIsFinish(getCompanyId(), cmd.getTaskUserIds());
        filterTaskUserIds(results,cmd);//过滤掉完成值审核未完成的
        List<EnterScoringResult> rs = taskAppSvc.batchEnterScoring(cmd);
        if (CollUtil.isNotEmpty(rs)) {
            if (CollUtil.isEmpty(results)) {
                results = new ArrayList<>();
            }
            results.addAll(rs);
        }
        return SingleResponse.of(results);
    }

    private void filterTaskUserIds(List<EnterScoringResult> results, EnterScoringCmd cmd) {
        if (CollUtil.isEmpty(results)) {
            return;
        }
        Set<String> ids = results.stream().map(EnterScoringResult::getId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(ids)) {
            List<String> okIds = cmd.getTaskUserIds().stream()
                    .filter(id -> !ids.contains(id))
                    .collect(Collectors.toList());
            cmd.setTaskUserIds(okIds);
            log.info("剔除掉不满足条件【完成值审核未完成的】,okIds:{},ids:{}", okIds, ids);
        }
    }


    //单个手动进入评分期 替换manualEnterScoring
    @RequestMapping("eval/task/enterScoringOne")
    public SingleResponse enterScoringOne(@RequestBody EnterScoringCmd cmd) {
        cmd.setOpEmpId(new TenantId(getCompanyId()), new EmpId(getEmpId()));
        EnterScoringResult rs = taskAppSvc.enterScoringOne(cmd);
        return SingleResponse.of(rs);
    }

    //并行 自定义流程评分 替换saveAllScore
    @RequestMapping({"eval/task/submitSameTimeScore", "eval/task/submitCustomScore"})
    public ResponseInfo submitSameTimeScore(@RequestBody SubmitScoreCmd cmd) {
        log.info("submitCustomScore入参：{}", JSONUtil.toJsonPrettyStr(cmd));
        TenantId companyId = new TenantId(getCompanyId());
        cmd.submitScoreLog(companyId, getEmpId());
        cmd.checkParamNullAndBuild();
        EvalUser taskUser = userRepo.getBaseTaskUser(companyId, cmd.getTaskUserId());
        if (taskUser.wasTempTask()) {
            submitScoreAppSvc.submitCustomScore(null, cmd);
        } else {
            scoreStageAppSvc.submitAnyWrap(cmd);
        }
        return ResponseInfo.success("");
    }

    //重新提交自定评分,也可用于订正数据用
    @RequestMapping("eval/task/reSubmitCustomScore")
    public ResponseInfo reSubmitCustomScore(@RequestBody SubmitScoreCmd cmd) {
        log.info("submitCustomScore入参：{}", JSONUtil.toJsonPrettyStr(cmd));
        TenantId companyId = new TenantId(getCompanyId());
        cmd.submitScoreLog(companyId, getEmpId());
        cmd.setReCommit(true);
        cmd.checkParamNullAndBuild();
        EvalUser taskUser = userRepo.getBaseTaskUser(companyId, cmd.getTaskUserId());
        if (taskUser.wasTempTask()) {
            submitScoreAppSvc.reSubmitCustomScore(null, cmd);
        } else {
            scoreStageAppSvc.reSubmitAnyWrap(Arrays.asList(cmd));
        }
        return ResponseInfo.success("");
    }


    //串行提交接口
    @RequestMapping({"eval/task/submitInturnScore", "eval/task/submitSuperiorScore",
            "eval/task/submitSelfScore", "eval/task/submitItemScore",
            "eval/task/submitSubScore", "eval/task/submitPeerScore"})
    public ResponseInfo submitInturnScore(@RequestBody SubmitScoreCmd cmd) {
        log.info("submitInturnScore入参：{}", JSONUtil.toJsonPrettyStr(cmd));
        TenantId companyId = new TenantId(getCompanyId());
        cmd.submitScoreLog(companyId, getEmpId());
        cmd.checkParamNullAndBuild();
        EvalUser taskUser = userRepo.getBaseTaskUser(companyId, cmd.getTaskUserId());
        if (taskUser.wasTempTask()) {
            submitScoreAppSvc.submitScore(cmd);
        } else {
            scoreStageAppSvc.submitAnyWrap(cmd);
        }
        return ResponseInfo.success("");
    }

    //评分暂存
    @RequestMapping("eval/task/cacheScenesScore")
    public SingleResponse cacheScenesScore(@RequestBody BatchCacheScoreCmd.CmdWrap wrap) {
        log.info("cacheScenesScore：{}", JSONUtil.toJsonStr(wrap));
        scoreStageAppSvc.cacheScenesScore(getCompanyId(), getEmpId(), wrap);
        return SingleResponse.buildSuccess();
    }

    //评分暂存
    @RequestMapping("eval/task/batchCacheScenesScore")
    public SingleResponse batchCacheScenesScore(@RequestBody List<BatchCacheScoreCmd.CmdWrap> cmdWraps) {
        log.info("batchCacheScenesScore：{}", JSONUtil.toJsonStr(cmdWraps));
        BatchCacheScoreCmd batchCmd = new BatchCacheScoreCmd(getCompanyId(), getEmpId(), cmdWraps);
        scoreStageAppSvc.batchCacheScenesScore(batchCmd);
        return SingleResponse.buildSuccess();
    }

    @RequestMapping("eval/task/submitScenesScore")
    public ResponseInfo submitScenesScore(@RequestBody List<SubmitScoreCmd> cmds) {
        log.info("scenesSubmitScore：{}", JSONUtil.toJsonStr(cmds));
        TenantId companyId = new TenantId(getCompanyId());
        cmds.forEach(cmd -> {
            cmd.submitScoreLog(companyId, getEmpId());
            cmd.checkParamNullAndBuild();
            cmd.setEmpId(getEmpId());
        });
        SubmitScoreCmd cmd = cmds.get(0);
        EvalUser taskUser = userRepo.getBaseTaskUser(companyId, cmd.getTaskUserId());
        if (taskUser.wasTempTask()) {
            submitScoreAppSvc.submitAllV1(companyId, cmds);//转给v1接口处理
            return ResponseInfo.success("");
        }
        scoreStageAppSvc.submitAnyWrap(cmds);//v2
        return ResponseInfo.success("");
    }

    /**
     * 2.0的评分V3版本提交评分接口
     * 不支持1.0的评分
     * @param cmdWrap 每个环节的评分数据
     * @return ResponseInfo
     */
    @RequestMapping("eval/task/submitScenesScoreV3")
    public SingleResponse submitScenesScoreV3(@RequestBody BatchSubmitScoreCmd3.CmdWrap cmdWrap) {
        log.info("submitScenesScoreV3：{}", JSONUtil.toJsonStr(cmdWrap));
        TenantId companyId = new TenantId(getCompanyId());
        EmpId opEmpId = new EmpId(getEmpId());
        cmdWrap.checkAndBuild(getCompanyId(), getEmpId());
        String tid = MDC.get(TraceKey.TID);
        List<BatchSubmitScoreCmd3.CmdWrap> cmdWraps = new ArrayList<>();
        cmdWraps.add(cmdWrap);
        List<SubmitScoreV3Cmd> batchs = scoreStageAppSvc.buildBatchSubmitScoreCmd(getCompanyId(), getEmpId(), cmdWraps);//build cmd
        if (CollUtil.isEmpty(batchs)){
            return SingleResponse.buildFailure("", "无需评价，请刷新页面");
        }
        BatchSubmitScoreResult result = scoreStageAppSvc.batchSubmitScenesScoreV3(tid,companyId,opEmpId,batchs);//v2
        return SingleResponse.of(result);
    }

    /**
     * 批量提交评分接口V2.0[不支持1.0的评分]
     * @param cmdWraps 每个环节的评分数据
     * @return ResponseInfo
     */
    @RequestMapping("eval/task/batchSubmitScenesScoreV3")
    public SingleResponse batchSubmitScenesScoreV3(@RequestBody List<BatchSubmitScoreCmd3.CmdWrap> cmdWraps) {
        log.info("batchSubmitScenesScoreV3：{}", JSONUtil.toJsonStr(cmdWraps));
        TenantId companyId = new TenantId(getCompanyId());
        EmpId opEmpId = new EmpId(getEmpId());
        for (BatchSubmitScoreCmd3.CmdWrap cmdWrap : cmdWraps) {
            cmdWrap.checkAndBuild(getCompanyId(), getEmpId());
        }
        String tid = MDC.get("tid");
        List<SubmitScoreV3Cmd> batchs = scoreStageAppSvc.buildBatchSubmitScoreCmd(getCompanyId(), getEmpId(), cmdWraps);//build cmd
        if (CollUtil.isEmpty(batchs)){
            return SingleResponse.buildFailure("", "无需评价，请刷新页面");
        }
        BatchSubmitScoreResult result = scoreStageAppSvc.batchSubmitScenesScoreV3(tid,companyId,opEmpId,batchs);//v2
        return SingleResponse.of(result);
    }


    //评分暂存
    @RequestMapping("eval/task/cacheScenesScoreV3")
    public SingleResponse cacheScenesScoreV3(@RequestBody BatchCacheScoreCmd.CmdWrap wrap) {
        log.info("cacheScenesScoreV3：{}", JSONUtil.toJsonStr(wrap));
        scoreStageAppSvc.cacheScenesScore(getCompanyId(), getEmpId(), wrap);
        return SingleResponse.buildSuccess();
    }

    //评分暂存
    @RequestMapping("eval/task/batchCacheScenesScoreV3")
    public SingleResponse batchCacheScenesScoreV3(@RequestBody List<BatchCacheScoreCmd.CmdWrap> cmdWraps) {
        log.info("batchCacheScenesScoreV3：{}", JSONUtil.toJsonStr(cmdWraps));
        BatchCacheScoreCmd batchCmd = new BatchCacheScoreCmd(getCompanyId(), getEmpId(), cmdWraps);
        scoreStageAppSvc.batchCacheScenesScore(batchCmd);
        return SingleResponse.buildSuccess();
    }

    //批量评分
    @RequestMapping("eval/task/batchSubmitScenesScore")
    public SingleResponse batchSubmitScenesScore(@RequestBody List<BatchSubmitScoreCmd.CmdWrap> cmdWraps) {
        log.info("batchSubmitScenesScore：{}", JSONUtil.toJsonStr(cmdWraps));
        for (BatchSubmitScoreCmd.CmdWrap cmdWrap : cmdWraps) {
            cmdWrap.checkAndBuild(getCompanyId(), getEmpId());
        }
        Set<BatchSubmitScoreCmd> batchs = scoreStageAppSvc.splitAndMarkV1Score(getCompanyId(), getEmpId(), cmdWraps);
        for (BatchSubmitScoreCmd batch : batchs) {
            batch.setTid(MDC.get("tid"));
            if (batch.isV1()) {
                submitScoreAppSvc.batchSubmitScenesScoreV1(batch);
            } else {
                scoreStageAppSvc.batchSubmitScenesScore(batch);
            }
        }
        return SingleResponse.buildSuccess();
    }

    //替换reModifyScore,以及其它所有评分
    //@RequestMapping("eval/task/submitScenesScore")

    /**
     * public ResponseInfo submitScenesScore(@RequestBody List<SubmitScoreCmd> cmds) {
     * log.info("scenesSubmitScore：{}", JSONUtil.toJsonStr(cmds));
     * TenantId companyId = new TenantId(getCompanyId());
     * cmds.forEach(cmd -> {
     * cmd.submitScoreLog(companyId, getEmpId());
     * cmd.checkParamNullAndBuild();
     * });
     * SubmitScoreCmd cmd = cmds.get(0);
     * CycleEval cycleEval = taskRepo.getMergeCycleEval(companyId, cmd.getTaskUserId());
     * EvalUser taskUser = userRepo.getBaseTaskUser(companyId, cmd.getTaskUserId());
     * if (cmd.isReCommit()) {//重新评分
     * if (taskUser.wasTempTask() && !cycleEval.isCustom()) {//旧数据s3串行
     * submitScoreAppSvc.reSubmitScenesScore(cycleEval, cmds);
     * } else if (taskUser.wasTempTask() && cycleEval.isCustom()) {//旧数据cutom并行
     * SubmitScoreCmd customCmd = merge2Custom(cmds);
     * submitScoreAppSvc.reSubmitCustomScore(cycleEval, customCmd);
     * } else {//考核格式数据
     * //scoreStageAppSvc.reSubmitAnyWrap(cmds);
     * scoreStageAppSvc.submitAnyWrap(cmds);
     * }
     * return ResponseInfo.success("");
     * }
     * <p>
     * if (taskUser.wasTempTask() && !cycleEval.isCustom()) {//旧数据s3串行
     * submitScoreAppSvc.submitScenesScore(cycleEval, cmds);
     * } else if (taskUser.wasTempTask() && cycleEval.isCustom()) {//旧数据cutom并行
     * SubmitScoreCmd customCmd = merge2Custom(cmds);
     * submitScoreAppSvc.submitCustomScore(cycleEval, customCmd);
     * } else {//考核格式数据
     * scoreStageAppSvc.submitAnyWrap(cmds);
     * }
     * return ResponseInfo.success("");
     * }
     **/
    //提交总等级
    @RequestMapping("eval/task/submitTotalLevel")
    public SingleResponse submitTotalLevel(@RequestBody TotalLevelCmd3 cmd) {
        log.info("submitTotalLevel：{}", JSONUtil.toJsonStr(cmd));
        TenantId companyId = new TenantId(getCompanyId());
        EmpId opEmpId = new EmpId(getEmpId());
        cmd.submitScoreLog(companyId, getEmpId());
        cmd.checkParamNullAndBuild();
        BatchSubmitScoreCmd3.CmdWrap cmdWrap = new BatchSubmitScoreCmd3.CmdWrap(cmd.getTaskUserId(), cmd);
        String tid = MDC.get(TraceKey.TID);
        List<BatchSubmitScoreCmd3.CmdWrap> cmdWraps = new ArrayList<>();
        cmdWraps.add(cmdWrap);
        List<SubmitScoreV3Cmd> batchs = scoreStageAppSvc.buildBatchSubmitScoreCmd(getCompanyId(), getEmpId(), cmdWraps);//build cmd
        if (CollUtil.isEmpty(batchs)) {
            return SingleResponse.buildFailure("", "无需评价，请刷新页面");
        }
        BatchSubmitScoreResult result = scoreStageAppSvc.batchSubmitScenesScoreV3(tid, companyId, opEmpId, batchs);//v2
        return SingleResponse.of(result);
    }


    //提交总等级
//    @RequestMapping("eval/task/submitTotalLevel")
//    public ResponseInfo submitTotalLevel(@RequestBody TotalLevelCmd cmd) {
//        log.info("submitTotalLevel：{}", JSONUtil.toJsonStr(cmd));
//        TenantId companyId = new TenantId(getCompanyId());
//        cmd.submitScoreLog(companyId, getEmpId());
//        cmd.checkParamNullAndBuild();
//        scoreStageAppSvc.submitTotalLevel(cmd);
//        return ResponseInfo.success("");
//    }

    @RequestMapping("eval/task/reSubmitScenesScore")
    public ResponseInfo reSubmitScenesScore(@RequestBody List<SubmitScoreCmd> cmds) {
        log.info("reSubmitScenesScore：{}", JSONUtil.toJsonPrettyStr(cmds));
        TenantId companyId = new TenantId(getCompanyId());
        cmds.forEach(cmd -> {
            cmd.submitScoreLog(companyId, getEmpId());
            cmd.setReCommit(true);
        });
        EvalUser taskUser = userRepo.getBaseTaskUser(companyId, cmds.get(0).getTaskUserId());
        if (taskUser.wasTempTask()) {
            submitScoreAppSvc.reSubmitScenesScore(null, cmds);
        } else {
            scoreStageAppSvc.reSubmitAnyWrap(cmds);
        }
        return ResponseInfo.success("");
    }


    //查询多级上级评分  替换 queryMultistageSupScore
    @RequestMapping({"perf/task/queryMultistageSupScore", "eval/task/listEvalScoreForSuper"})
    public SingleResponse listEvalScoreForSuper(String taskUserId) {
        EvalMultistageSupScorePo scoreForSuper = submitScoreAppSvc.listEvalScoreForSuper(new TenantId(getCompanyId()), taskUserId);
        return SingleResponse.of(scoreForSuper);
    }

    //查询可以修改的打分记录 替换 queryEnableModifyScoreResult
    @RequestMapping({"perf/task/queryEnableModifyScoreResult", "eval/task/listEnabledScoreRs"})
    public SingleResponse listEnabledScoreRs(String taskUserId, String scorerType) {
        checkParamNull(taskUserId, scorerType);
        EnbledSaveScorePo rs = submitScoreAppSvc.listEnabledScoreRs(getCompanyId(), taskUserId, scorerType, getEmpId());
        return SingleResponse.of(rs);
    }

    //评分详情,支持自定串行显示,所有指标
    @RequestMapping({"eval/task/scoreDetail", "report/getMergeScoreResult"})
    public ResponseInfo scoreDetail(@RequestParam String taskUserId) {
       // EmpEvalTaskScore2Po taskScore2Po = scoreStageAppSvc.scoreDetail(new TenantId(getCompanyId()), taskUserId, getEmpId());
        EmpEvalTaskScore3Po taskScore2Po = scoreStageAppSvc.scoreDetailV3(new TenantId(getCompanyId()), taskUserId, getEmpId());
        return ResponseInfo.success(taskScore2Po);
    }


    //评分详情，查询总环节，各个评分人环节打分
    @RequestMapping({"eval/task/listScorerNodeScoreDetail"})
    public SingleResponse listScorerNodeScoreDetail(@RequestParam String taskUserId) {
        List<EvalScorerTypeScoresPo> scorerNodeScores = scoreStageAppSvc.listScorerNodeScore(new TenantId(getCompanyId()), taskUserId);
        return SingleResponse.of(scorerNodeScores);
    }


    //结束评分阶段，（给融通农业(北京公司)评分需求做的）
    @RequestMapping("eval/task/endScoreStage")
    public ResponseInfo endScoreStage(@RequestBody EndScoreStageCmd cmd) {
        log.info("endScoreStage入参：{}", JSONUtil.toJsonPrettyStr(cmd));
        cmd.accOp(new TenantId(getCompanyId()), new EmpId(getEmpId()));
        List<EndScoreErrInfo> errInfos = new ArrayList<>();
        for (String taskUserId : cmd.getTaskUserIds()) {
            cmd.setCurUserId(taskUserId);
            try {
                submitScoreAppSvc.endScoreStage(cmd);
            } catch (KpiI18NException e) {
                errInfos.add(new EndScoreErrInfo(taskUserId, e.getI18NMsg()));
            }
        }
        return ResponseInfo.success(errInfos);
    }

    @RequestMapping("eval/task/pagedWaitScoreEmpEval")
    public MultiResponse pagedWaitScoreEmpEval(@RequestBody ScoreWorkTodoQuery query) {
        query.accOp(getCompanyId(), getEmpId());
        PagedList<SimpleEvalEmpPo> data = workTodoAppSvc.pagedWaitScoreEmpEval(query);
        if (data.isEmpty()) {
            return MultiResponse.of(data, data.getPageNo(), data.getTotalRow(), data.getPageSize());
        }
        List<String> taskUserIds = CollUtil.map(data, SimpleEvalEmpPo::getTaskUserId, true);
        List<EmpEvalForScoreV3Po> scoreV2Pos = scoreStageAppSvc.listEmpEvalForScoreV2(new TenantId(query.getCompanyId()), taskUserIds, query.getOpEmpId());
        ListWrap<EmpEvalForScoreV3Po> map = new ListWrap<>(scoreV2Pos).asMap(EmpEvalForScoreV3Po::getTaskUserId);
        PagedList<EmpEvalForScoreV3Po> rs = new PagedList<>(data.getPageNo(), data.getPageSize(), data.getTotalRow());
        AtomicInteger order = new AtomicInteger(0);
        List<EmpEvalForScoreV3Po> newOrders = data.stream().map(datum -> {
            EmpEvalForScoreV3Po v2Po = map.mapGet(datum.getTaskUserId());
            if (v2Po != null) {
                v2Po.setOrderBy(order.incrementAndGet());
            }
            return v2Po;
        }).collect(Collectors.toList());
        return MultiResponse.of(newOrders, rs.getPageNo(), rs.getTotalRow(), rs.getPageSize());
    }

    // 替换 queryTaskDetailByEmp ,进入评分页面使用 scoreNode 新加参数, 查指定的scoreNode
    @RequestMapping("emp/eval/empEvalForScoreV2")
    public SingleResponse empEvalForScoreV2(String talentEvalId, String scoreNode) {
        EmpEvalForScoreV3Po rs = empEvalAppSvc.empEvalForScoreV2(new TenantId(getCompanyId()), talentEvalId, getEmpId(), scoreNode);
        log.info("empEvalForScoreV2.WaitScoreTypes 出参：{}", JSONUtil.toJsonStr(rs.getWaitScoreTypes()));
        return SingleResponse.of(rs);
    }

    // 计算指标环节，总环节分数
    @RequestMapping("emp/eval/computeEvalNodeScore")
    public SingleResponse computeEvalNodeScore(String companyId, String opEmpId, String taskUserId) {
        scoreStageAppSvc.computeEvalNodeScore(companyId, opEmpId, taskUserId);
        return SingleResponse.of("");
    }

    // 计算环节分数
    @RequestMapping("emp/eval/computeEmpEvalSNScoreByTaskUserId")
    public SingleResponse computeEmpEvalSNScoreByTaskUserId(String taskUserId) {
        scoreStageAppSvc.computeEmpEvalSNScoreByTaskUserId(getCompanyId(),taskUserId);
        return SingleResponse.of("");
    }

    // 计算最终的总分环节分数【包含同级去高去低】
    @RequestMapping("emp/eval/reComputeFinalScore")
    public SingleResponse reComputeFinalScore(String taskUserId) {
        scoreStageAppSvc.reComputeFinalScore(getCompanyId(),taskUserId);
        return SingleResponse.of("");
    }
}
