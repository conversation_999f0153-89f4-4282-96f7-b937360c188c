#阿里云oss
aliyun:
  oss:
    access:
      keyId: LTAI5t3iEAY1ZVk3
      keySecret: vo3B2CF7wdRrRYfLYPsKA3LhEBBTnj
    endpoint: https://topscrm.oss-cn-hangzhou-internal.aliyuncs.com # 正式环境只能internal内网访问
    bucketName: topscrm
    defaultAvatar: http://zxb-online.oss-cn-beijing.aliyuncs.com/default.jpg
    imageOssHost: https://topscrm.oss-cn-hangzhou.aliyuncs.com/

# 信息安全
security:
  csrf:
    enable: false
    excludes:
plat: ding

spring:
  profiles:
    active: prod
  datasource:
    company:
      url: ***************************************************************************************************************************************************************
      username: kpi2024
      password: 20240418D9wfs$Lsnns7
    ding:
      url: ********************************************************************************************************************************************
      username: kpi2024
      password: 20240418D9wfs$Lsnns7
      hikari:
        driver-class-name: com.mysql.cj.jdbc.Driver
        # 最小空闲连接数量
        minimum-idle: 5
        # 空闲连接存活最大时间，默认600000（10分钟）
        idle-timeout: 180000
        # 连接池最大连接数，默认是10
        maximum-pool-size: 10
        # 此属性控制从池返回的连接的默认自动提交行为,默认值：true
        auto-commit: true
        # 连接池名称
        pool-name: MyHikariCP
        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
        max-lifetime: 1800000
        # 数据库连接超时时间,默认30秒，即30000
        connection-timeout: 30000
        connection-test-query: SELECT 1
  security:
    user:
      name: admin
      password: admin
#钉钉配置
ding:
  appId: 137231
  suiteKey: suitek1cccbxkd8b0tqxa
  suiteSecret: i53hx7Jzn41JEiFmADyfdpCluPsCupTCSrxtwXA0lVPTxc7QjAgo6dAj-rsaXWoD
  SSOSecret:
  goodsCode:
  freeSkuCode: DT_GOODS_881603867765821_836012 #  免费sku 和线上一样
  trySkuCode: DT_GOODS_881603867765821_212006 # 试用sku 和线上一样
  simpleSkuCode: DT_GOODS_881603867765821_212005 #  标准版sku 和线上一样
  enterpriseSkuCode: DT_GOODS_881603867765821_212006 # 企业版本
  service:
    token: 2bc90f577c12bdb3572073e04e3b033a
  inAppCourseCode:
  inAppCallBackPage:
  hrm:
    tenantId: 28
    accessKey: 052c5e0a4202373488dca14996f3f2e2
ext:
  sign:
    url: http://kpiuat.pekhr.com:8084/v1/index.html
host:
  url: https://dingkpi.eapps.dingtalkcloud.com/
  todoUrlPrefix: toRouteName=taskDetail&
  version: v2
#global数据库表名
database:
  global: global_perf

#OKR接口地址
okr:
  host: https://demo1.topscrm.cn:8443/
  # host: http://app37766.eapps.dingtalkcloud.com/
  accessKey: 14006174250411491330
  accessSecret: b5d1373a5ae411ebb7d10050568a58a6
  dingAppId: 41774
  domain: https://demo1.topscrm.cn:8443/

down:
  url: https://topscrm.oss-cn-hangzhou.aliyuncs.com
monitoring:
  alarm:
    open: true
    robotToken: 40d6505bde19f7af09748c8fc67523d4b8e980ded2cbdb5ac3777c26b0b75402
kpi:
  msg:
    tmps: #消息模板配置
      new_emp_eval: 60d9cc7ff7d247b29dc42448aae950bd #1-37-请发起新人考核
      create_task: 49e30cc9a3df4a57b3cb9ba7002ba982 #1-1您收到新的考核任务
      task_enact: 1ef0162d0d2f4e74b594ffa835c31cda #1-81请进行指标制定
      task_confirm: 42f894bc34c34855bb3260d2332faa87 #1-30请进行指标确认
      task_confirm_audit: b7279e7e5f884dbaafa4040c3c5d4ff2 #1-54请进行指标审核
      task_confirm_reject: a971486721294561ac54b0a0419733d1 #1-55指标审核被驳回
      user_enable_apply: 4e6e0042f5954fe195ac618a09a60ba8 #1-13您收到一条开通应用权限的申请
      urging_user_enable_apply: 6f01abc7e5ce48b798c991c8c342d278 #1-14催办_申请使用权限
      user_enable_pass: fdb90a6beeab442396240966b7b5a8a6 #1-25申请使用结果通知
      user_enable_reject: fdb90a6beeab442396240966b7b5a8a6 #1-25申请使用结果通知
      item_audit_reject: 05dd290f6cb341ad91e7c6bca628c94d #1-29指标确认考核指标审核结果
      task_submit_progress: 8ff2a21ff69e47b99ad3e7577afd076f #1-3请提交完成值
      batch_submit_progress: 6935885b43574d95a82531eb5e81a429 #1-32批量录入指标完成值
      change_item_audit: d3b2c50a74c54e03af5541449ea87cd5 #1-35请进行指标变更审核
      change_item_reject: 87dce3b78ae84010a43f426b91a1c2bc #1-56指标变更被驳回
      item_notice: 8ff2a21ff69e47b99ad3e7577afd076f #1-3请提交完成值
      transfer_task: 8835a8372ead4613b149ded0b6b7b570 #1-4您收到一条转交的考核任务
      change_item_success: 1d211392e17140d29b20ef3c0401076f #已删除_1-5您有考核任务正在变更
      task_wait_self_score: a33f609cdc9a401f8ec0669958de45d6 #1-31请完成自评
      task_wait_help_score: 8ca0577a2e114d75910f93de66da0abe #1-32请提交协同人评分
      task_all_score: 5b566043d1ce4c77b54feac491c6d5e2 #1-32请提交协同人评分
      task_result_audit: 004c1e22967d44d6ba964d9baf758dae #1-33请进行结果校准
      task_result_collect_audit: 869212f5b5284ab79c958a01c600a49a #1-79请进行绩效校准
      task_result_affirm: 328c8a1e493f4d108af89389333cd0eb # 1-7考核任务进入结果确认阶段
      task_wait_public: ec544027a16840e9a3a74172a2ff8e83 #1-8您收到一条待公示的考核任务
      task_result_appeal: c940916c6a0e4ec7aca7ca1c952efd1d #1-9您收到一条待处理的申诉
      reset_task: 738947ee20e84f01b25eadc62ff559ce #1-10您的考核任务已被重置
      task_stopped: 33dad895074840b6b1296b7aa02f3408 #1-11您的考核任务被终止
      urging: 4a37b3cb3c96464eadb3d1d308627816 #1-12您收到一条催办
      urging_task: 02ebdbef55f5483fa40b92dedaf74f86 #1-12-1请尽快
      task_scorer_miss: 00195d4981e6488fa474cbe31316bbf3 #1-15责任人空缺
      task_finished: 605ba6d9afdd481e9ba32bc71834a16f #1-16考核任务已完成
      result_appeal_handled: 2124ca1c89474efe85d391fa4dcac3ed #1-17 结果申诉已处理完毕
      task_has_publican: e84c3b88b01b4f5ea28acbe10806ae16 #1-26考核结果公示
      item_progress_update: c4f2ac29cb6b4db2966cadcdca3ca327 #1-28指标更新
      set_mutual_audit: 8819a9747d05478b846d8526d4eb5482 #1-34考核人设定互评人
      simple_open: b8e981f4a6da4d5b86a442aee80f526e #1-18开通北极星绩效试用版
      enterprise_open: dc0bbb414afc4e2bb44a3e115fb29f4a #1-20开通北极星绩效企业版
      enterprise_renewal: c68e3f4830a44d44984c315fcad2be23 #1-21正式版续费
      simple_3d: 7f84b21a746d4df8abd87693f61b1cb6 #1-22使用3天
      enterprise_rest_10d: f2a0b3192a9743e7866e2b1977a56ec8 #1-23正式版剩余10天
      enterprise_expire: b771ebc074ef429789bbb1cb74ac74b3 #1-24企业版到期
      task_coach_created: 514ccf0efefd426bb5f511371459b559 #1-36面谈辅导通知
      onBardingEmp: fd77a3d5d99843628d19541a8ea8c55b #1-37-请发起新人考核
      notice_person: daef3576b4ac47188b707a704a40185b #通知_请查看_员工
      notice_admin: 7012f15c66b941b5ad4b90658dfb5527 #通知_管理员
      apply_menu_msg: 67cfb63c83834443b3788d4236562a2a #1-50您收到一条新人考核功能的开通申请
      scoring_time_out_to_rater: 6d285cc188ad4df68337e1f6a9796a5f #1-51评分超时提醒评分人-单次
      scoring_time_out_to_admin: 220d717e974848baad68256e926b4c20 #1-52评分超时提醒管理员
      eval_ref_result_public: 8291688c45fa4f849af76dd3bce51c1b #1-53考核关联绩效结果公布
      ind_score_item_input: c09839f971a6449f968af829084d438d #1-58指标评分通知
      ind_score_item_changed: 97f58527a552475bb32f9d4a80f20214 #1-59指标评分修改通知
      finish_value_audit: 9989cd1f915e4afb828a09759d51c53d #1-57请进行完成值审核
      eval_review_inform: ccedfe30eb254b86b08eaf338c801df0 #1-60评论通知
      reject_audit_result: 542eeec3606b4355bbe628ff359eee52 #1-61绩效校准被驳回
      reject_to_score_stage: c0a1f42ae32047f087f0b97b249c05f4 #1-62校准中绩效评分被驳回