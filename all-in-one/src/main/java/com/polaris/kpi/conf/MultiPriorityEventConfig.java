package com.polaris.kpi.conf;

import com.polaris.kpi.ExecutorEnum;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskDecorator;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.Map;

@Configuration
@Slf4j
public class MultiPriorityEventConfig implements AsyncConfigurer {

    private ThreadPoolTaskExecutor high;

    private ThreadPoolTaskExecutor low;

    // 高优先级线程池
    @Bean(name = ExecutorEnum.high)
    public ThreadPoolTaskExecutor highPriorityExecutor() {
        this.high = createExecutor(ExecutorEnum.high, 8, 15, 2000);
        return this.high;
    }

    // 低优先级线程池
    @Bean(name = ExecutorEnum.low)
    public ThreadPoolTaskExecutor lowPriorityExecutor() {
        this.low = createExecutor(ExecutorEnum.low, 2, 5, 3000);
        return low;
    }

    private ThreadPoolTaskExecutor createExecutor(String prefix, int core, int max, int queue) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(core);
        executor.setMaxPoolSize(max);
        executor.setQueueCapacity(queue);
        executor.setTaskDecorator(new MdcTaskDecorator()); // 关键装饰器
        executor.setThreadNamePrefix(prefix);
        executor.initialize();
        return executor;
    }

    // 判断队列是否超过阈值（例如90%）
    public void checkQueueFull() {
        int backlogSize = high.getThreadPoolExecutor().getQueue().size();
        int queueCapacity = high.getThreadPoolExecutor().getQueue().remainingCapacity() + backlogSize;
        double usageRatio = (double) backlogSize / queueCapacity;
        if( usageRatio > 0.9){
            System.err.printf("[警告]  HIGHT 事件队列已满！当前积压: %d/%d%n",
                    backlogSize,
                    queueCapacity);
        }
    }

    public void checkQueueFullLow() {
        int backlogSize = low.getThreadPoolExecutor().getQueue().size();
        int queueCapacity = low.getThreadPoolExecutor().getQueue().remainingCapacity() + backlogSize;
        double usageRatio = (double) backlogSize / queueCapacity;
        if( usageRatio > 0.9){
            System.err.printf("[警告] 事件队列已满！当前积压: %d/%d%n",
                    backlogSize,
                    queueCapacity);
        }
    }

    @Scheduled(fixedRate = 5000) // 每5秒检查一次
    public void checkQueue() {
        this.checkQueueFullLow();
        this.checkQueueFull();
    }


    static class MdcTaskDecorator implements TaskDecorator {
        @Override
        public Runnable decorate(Runnable runnable) {
            Map<String, String> context = MDC.getCopyOfContextMap();
            return () -> {
                try {
                    if (context != null) {
                        MDC.setContextMap(context);
                    }
                    runnable.run();
                } finally {
                    MDC.clear();
                }
            };
        }
    }
}